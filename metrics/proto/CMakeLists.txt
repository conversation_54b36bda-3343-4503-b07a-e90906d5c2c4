CompileProto(metrics_aggregator_service.proto GENERATED_PATH GOPKG proto/metrics_aggregator LANGS python mypy grpc_python go go-grpc cpp grpc)

add_library(metrics_aggregator_proto SHARED ${GENERATED_PATH}/metrics_aggregator_service.grpc.pb.cc ${GENERATED_PATH}/metrics_aggregator_service.pb.cc)
target_compile_options(metrics_aggregator_proto PRIVATE "-w")
target_link_libraries(metrics_aggregator_proto PUBLIC grpc++ protobuf metrics_proto)

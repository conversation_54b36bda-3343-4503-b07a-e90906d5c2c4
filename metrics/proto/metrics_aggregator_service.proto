syntax = "proto3";

package metrics_aggregator;
option go_package = "proto/metrics_aggregator";

import "proto/metrics/metrics.proto";

message PingRequest {
  uint32 x = 1;
}

message PingResponse {
  uint32 x = 1;
}

message Metrics {
  map <string, string> metrics = 1;
}

message GetMetricsRequest {
}
message GetMetricsResponse {
  map<string, Metrics> daily_metrics = 1;
}
message AcknowledgeDailyMetricRequest {
  repeated string days = 1;
}
message AcknowledgeDailyMetricResponse {
}

message GetJobMetricsRequest {
}
message GetJobMetricsResponse {
  Metrics jobMetrics = 1;
}

message GetLaserLifeTimesRequest {}
message SetLaserResponse {}
message GetLaserChangeTimesRequest {}

message RegisterSpatialClientRequest {}
message RegisterSpatialClientResponse {
  uint32 id = 1;
}
message SpatialClientBeatRequest {
  uint32 id = 1;
}
message SpatialClientBeatResponse {
  bool success = 1;
}
message SpatialClientAckRequest {
  uint32 client_id = 1;
  uint64 block_id = 2;
}
message SpatialClientAckResponse {
  bool success = 1;
}
message GetNextSpatialBlockRequest {
  uint64 block_id =1;
}
message GetNextSpatialBlockResponse {
  uint64 block_id =1;
}

message OverrideLaserRequest {
  carbon.metrics.LaserIdentifier laser = 1;
  uint64 lifetime_s = 2;
}

service MetricsAggregatorService {
  rpc Ping(PingRequest) returns (PingResponse) {}
  rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse) {}
  rpc AcknowledgeDailyMetric(AcknowledgeDailyMetricRequest) returns (AcknowledgeDailyMetricResponse) {}
  rpc GetJobMetrics(GetJobMetricsRequest) returns (GetJobMetricsResponse);
  rpc GetLaserLifeTimes(GetLaserLifeTimesRequest) returns (carbon.metrics.LaserLifeTimes) {}
  rpc SetLaser(carbon.metrics.LaserIdentifier) returns (SetLaserResponse) {}
  rpc OverrideLaser(OverrideLaserRequest) returns (SetLaserResponse) {}
  rpc GetLaserChangeTimes(GetLaserChangeTimesRequest) returns (carbon.metrics.LaserChangeTimes) {}
  rpc RegisterSpatialClient(RegisterSpatialClientRequest) returns (RegisterSpatialClientResponse) {}
  rpc SpatialClientBeat(SpatialClientBeatRequest) returns (SpatialClientBeatResponse) {}
  rpc SpatialClientAck(SpatialClientAckRequest) returns (SpatialClientAckResponse) {}
  rpc GetNextSpatialBlock(GetNextSpatialBlockRequest) returns (GetNextSpatialBlockResponse) {}
}

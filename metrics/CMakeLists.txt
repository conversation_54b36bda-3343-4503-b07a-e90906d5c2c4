add_subdirectory(proto)
add_subdirectory(cpp/prometheus)
add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)
file(GLOB SPATIAL_SOURCES CONFIGURE_DEPENDS cpp/spatial/*.cpp cpp/spatial/*.c cpp/spatial/*.h cpp/spatial/*.hpp)

add_library(metrics_spatial SHARED ${SPATIAL_SOURCES})
target_compile_definitions(metrics_spatial PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(metrics_spatial PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib  utils metrics_proto metrics_aggregator_proto)

add_library(metrics SHARED ${SOURCES})
target_compile_definitions(metrics PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(metrics PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib zonedetect date-tz hardware_manager_client redis_client prometheus-cpp-core prometheus-cpp-pull geohash geometric weed_tracking trajectory utils targeting metrics_spatial scanner robot_definition)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(metrics_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(metrics_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(metrics_python PUBLIC metrics)
set_target_properties(metrics_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
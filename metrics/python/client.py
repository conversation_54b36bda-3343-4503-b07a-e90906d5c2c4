from typing import Dict

import grpc

import generated.metrics.proto.metrics_aggregator_service_pb2 as metrics_aggregator_pb
import generated.metrics.proto.metrics_aggregator_service_pb2_grpc as metrics_aggregator_grpc
import generated.proto.metrics.metrics_pb2 as metrics_pb
from lib.common.generation import get_command_ip
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class MetricsAggregatorClient:
    def __init__(self, hostname: str = get_command_ip(), port: int = 61010):
        self._hostname = hostname
        self._port = port
        self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
        self._stub = metrics_aggregator_grpc.MetricsAggregatorServiceStub(self._channel)

    async def ping(self, x: int) -> int:
        req = metrics_aggregator_pb.PingRequest(x=x)
        response: metrics_aggregator_pb.PingResponse = await self._stub.Ping(req)
        return int(response.x)

    async def get_metrics(self, x: int) -> Dict[str, Dict[str, str]]:
        req = metrics_aggregator_pb.GetMetricsRequest()
        response: metrics_aggregator_pb.GetMetricsResponse = await self._stub.GetMetrics(req)
        resp = {}
        for key in response.daily_metrics:
            resp[key] = dict(response.daily_metrics[key].metrics)
        return resp

    async def get_laser_lifetimes(self) -> metrics_pb.LaserLifeTimes:
        req = metrics_aggregator_pb.GetLaserLifeTimesRequest()
        response: metrics_pb.LaserLifeTimes = await self._stub.GetLaserLifeTimes(req)
        return response

    async def set_laser(self, serial: str, row: int, slot: int) -> None:
        req = metrics_pb.LaserIdentifier(position=metrics_pb.LaserPosition(row=row, slot=slot), serial=serial)
        await self._stub.SetLaser(req)

    async def override_laser(self, serial: str, row: int, slot: int, lifetime_s: int) -> None:
        req = metrics_aggregator_pb.OverrideLaserRequest(
            laser=metrics_pb.LaserIdentifier(position=metrics_pb.LaserPosition(row=row, slot=slot), serial=serial),
            lifetime_s=lifetime_s,
        )
        await self._stub.OverrideLaser(req)

    async def get_laser_details(self) -> metrics_pb.LaserChangeTimes:
        req = metrics_aggregator_pb.GetLaserChangeTimesRequest()
        resposne: metrics_pb.LaserChangeTimes = await self._stub.GetLaserChangeTimes(req)
        return resposne

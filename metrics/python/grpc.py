import asyncio
from typing import List

import grpc

import generated.metrics.proto.metrics_aggregator_service_pb2 as metrics_aggregator_pb
import generated.metrics.proto.metrics_aggregator_service_pb2_grpc as metrics_aggregator_grpc
import generated.proto.metrics.metrics_pb2 as metrics_pb
import lib.common.logging
from lib.common.metrics.daily_metric_aggregator import DailyMetricAggregator
from lib.common.metrics.job_metric_aggregator import JobMetricAggregator
from lib.common.metrics.laser_time_metric import LaserTimeMetric
from lib.common.tasks.owner import Owner
from metrics.python.spatial.spatial_block_generator import SpatialBlockGenerator

LOG = lib.common.logging.get_logger(__name__)


class GrpcServicer(metrics_aggregator_grpc.MetricsAggregatorServiceServicer):
    def __init__(
        self,
        daily_metrics: DailyMetricAggregator,
        job_metrics: JobMetricAggregator,
        laser_time_metric: LaserTimeMetric,
        spatial_block_generator: SpatialBlockGenerator,
    ) -> None:
        self._daily_metrics = daily_metrics
        self._job_metrics = job_metrics
        self._ltm = laser_time_metric
        self._sbg = spatial_block_generator

    async def Ping(
        self, request: metrics_aggregator_pb.PingRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.PingResponse:
        LOG.info(f"Got A Ping: {request.x}")
        context.set_code(grpc.StatusCode.OK)
        return metrics_aggregator_pb.PingResponse(x=request.x)

    async def GetMetrics(
        self, request: metrics_aggregator_pb.GetMetricsRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.GetMetricsResponse:
        metrics = await self._daily_metrics.get_metrics()
        response = metrics_aggregator_pb.GetMetricsResponse()
        for day, met in metrics.items():
            response.daily_metrics[day].metrics.update(met)
        return response

    async def AcknowledgeDailyMetric(
        self, request: metrics_aggregator_pb.AcknowledgeDailyMetricRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.AcknowledgeDailyMetricResponse:
        days = list(filter(lambda day: day, request.days))
        await self._daily_metrics.remove_keys(days)
        return metrics_aggregator_pb.AcknowledgeDailyMetricResponse()

    async def GetJobMetrics(
        self, request: metrics_aggregator_pb.GetJobMetricsRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.GetJobMetricsResponse:
        response = metrics_aggregator_pb.GetJobMetricsResponse()
        response.jobMetrics.metrics.update(await self._job_metrics.getMetrics())
        return response

    async def GetLaserLifeTimes(
        self, request: metrics_aggregator_pb.GetLaserLifeTimesRequest, context: grpc.ServicerContext
    ) -> metrics_pb.LaserLifeTimes:
        tmp_lasers = await self._ltm.get_lasers()
        lasers = {v: k for k, v in tmp_lasers.items()}
        lifetimes = self._ltm.lifetimes
        resp = metrics_pb.LaserLifeTimes()
        for serial, time_ms in lifetimes.items():
            if serial in lasers:
                position = metrics_pb.LaserPosition(row=lasers[serial][0], slot=lasers[serial][1])
            else:
                position = metrics_pb.LaserPosition(row=0, slot=0)
            resp.lifetimes.append(
                metrics_pb.LaserLifeTime(
                    id=metrics_pb.LaserIdentifier(position=position, serial=serial), lifetime_sec=round(time_ms / 1000)
                )
            )
        return resp

    async def SetLaser(
        self, request: metrics_pb.LaserIdentifier, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.SetLaserResponse:
        await self._ltm.set_mapping(request.serial, request.position.row, request.position.slot)
        return metrics_aggregator_pb.SetLaserResponse()

    async def OverrideLaser(
        self, request: metrics_aggregator_pb.OverrideLaserRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.SetLaserResponse:
        await self._ltm.override_mapping(
            request.laser.serial, request.laser.position.row, request.laser.position.slot, request.lifetime_s
        )
        return metrics_aggregator_pb.SetLaserResponse()

    async def GetLaserChangeTimes(
        self, request: metrics_aggregator_pb.GetLaserChangeTimesRequest, context: grpc.ServicerContext
    ) -> metrics_pb.LaserChangeTimes:
        installed, removed = await self._ltm.get_laser_details()
        resp = metrics_pb.LaserChangeTimes()
        for laser in installed:
            resp.installs.append(
                metrics_pb.LaserEventTime(
                    id=metrics_pb.LaserIdentifier(
                        position=metrics_pb.LaserPosition(row=laser.row, slot=laser.slot), serial=laser.serial
                    ),
                    timestamp_sec=laser.timestamp_sec,
                )
            )
        for laser in removed:
            resp.removals.append(
                metrics_pb.LaserEventTime(
                    id=metrics_pb.LaserIdentifier(
                        position=metrics_pb.LaserPosition(row=laser.row, slot=laser.slot), serial=laser.serial
                    ),
                    timestamp_sec=laser.timestamp_sec,
                )
            )
        return resp

    async def RegisterSpatialClient(
        self, request: metrics_aggregator_pb.RegisterSpatialClientRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.RegisterSpatialClientResponse:
        return metrics_aggregator_pb.RegisterSpatialClientResponse(id=await self._sbg.register_client())

    async def SpatialClientBeat(
        self, request: metrics_aggregator_pb.SpatialClientBeatRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.SpatialClientBeatResponse:
        return metrics_aggregator_pb.SpatialClientBeatResponse(success=await self._sbg.beat(request.id))

    async def SpatialClientAck(
        self, request: metrics_aggregator_pb.SpatialClientAckRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.SpatialClientAckResponse:
        return metrics_aggregator_pb.SpatialClientAckResponse(
            success=await self._sbg.ack(request.client_id, request.block_id)
        )

    async def GetNextSpatialBlock(
        self, request: metrics_aggregator_pb.GetNextSpatialBlockRequest, context: grpc.ServicerContext
    ) -> metrics_aggregator_pb.GetNextSpatialBlockResponse:
        return metrics_aggregator_pb.GetNextSpatialBlockResponse(
            block_id=await self._sbg.get_next_block(request.block_id)
        )


class GrpcServer:
    def __init__(
        self,
        port: int,
        daily_metrics: DailyMetricAggregator,
        job_metrics: JobMetricAggregator,
        laser_time_metric: LaserTimeMetric,
        spatial_block_generator: SpatialBlockGenerator,
        tasks: List[Owner],
    ):
        self._port = port
        self._server = grpc.aio.server()
        self._daily_metrics = daily_metrics
        self._job_metrics = job_metrics
        self._ltm = laser_time_metric
        self._tasks = tasks
        self._tasks.append(daily_metrics)
        self._tasks.append(laser_time_metric)
        self._tasks.append(job_metrics)
        self._servicer = GrpcServicer(
            daily_metrics=daily_metrics,
            laser_time_metric=laser_time_metric,
            job_metrics=job_metrics,
            spatial_block_generator=spatial_block_generator,
        )
        self._loggingServicer = lib.common.logging.LoggingGRPCServicer(self._server, "metrics_aggregator.log")
        metrics_aggregator_grpc.add_MetricsAggregatorServiceServicer_to_server(self._servicer, self._server)
        self._server.add_insecure_port(f"[::]:{self._port}")
        self._started = False

    async def serve(self) -> None:
        self._loop = asyncio.get_event_loop()
        await asyncio.gather(*[task.start() for task in self._tasks])
        await self._server.start()
        self._started = True
        LOG.info(f"Started Metrics Aggregator at 0.0.0.0:{self._port}")
        await self._server.wait_for_termination()

    async def _shutdown(self) -> None:
        await self._server.stop(0)
        await asyncio.gather(*[task.stop() for task in self._tasks])

    def shutdown(self) -> None:
        if self._started:
            asyncio.run_coroutine_threadsafe(self._shutdown(), self._loop)
        self._started = False

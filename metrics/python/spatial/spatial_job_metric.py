import aioredis

import lib.common.logging
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock
from metrics.python.spatial.spatial_metric import SpatialMetic

LOG = lib.common.logging.get_logger(__name__)
JOB_ID_KEY = "jobs/active_job"


class SpatialJobMetric(SpatialMetic):
    def __init__(self, redis: aioredis.Redis) -> None:
        self._redis = redis

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        job_id = await self._redis.get(JOB_ID_KEY)
        if job_id is not None:
            block.job_metric.job_id = job_id.decode()

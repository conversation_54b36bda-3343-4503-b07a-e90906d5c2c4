from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock


class SpatialMetic:
    def __init__(self) -> None:
        pass

    async def start(self) -> None:
        pass

    async def start_block(self, block_id: int, timestamp: int) -> None:
        pass

    async def end_block(self, block_id: int, timestamp: int) -> None:
        pass

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        pass

import asyncio
from typing import Dict, Generator

import grpc

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.generation import is_reaper, rows
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.blocking_spatial_metric import BlockingSpatialMetric
from weed_tracking.python.client.weed_tracking_client import WeedTrackingClient

LOG = lib.common.logging.get_logger(__name__)


def get_row_ips() -> Generator[str, None, None]:
    disabled = get_global_config_subscriber().get_config_node("common", "disabled_rows")
    disabled_names = set([f"row{ch.get_name()}" for ch in disabled.get_children_nodes()])
    aimbot_addresses = rows()
    if is_reaper():
        aimbot_addresses = RobotDefinition.get().get_aimbot_addresses()
        LOG.info(f"Is Reaper, aimbot addresses: {aimbot_addresses}")
    for name, ip in aimbot_addresses.items():
        if name not in disabled_names:
            yield ip


class SpatialWidthMetric(BlockingSpatialMetric):
    def __init__(self, owner: SpatialBlockOwner) -> None:
        super().__init__(owner)
        self._in_progress_blocks: Dict[int, float] = {}

        config_subscriber = get_global_config_subscriber()
        self._row_width_conf = config_subscriber.get_config_node("common", "row_width_in")
        self._wt_clients = [WeedTrackingClient(hostname=ip) for ip in get_row_ips()]
        self._targeting = [False for _ in self._wt_clients]

    async def start(self) -> None:
        await super().start()
        self._targeting_lock = asyncio.Lock()
        for i in range(len(self._targeting)):
            asyncio.get_event_loop().create_task(self._check_targeting(i))

    async def _check_targeting(self, index: int) -> None:
        while not bot_stop_handler.stopped:
            targeting = False
            try:
                targeting = await self._wt_clients[index].get_targeting_enabled()
            except grpc.aio.AioRpcError as ex:
                LOG.warning(f"Failed to get targeting state for row {index + 1}, error code ='{ex.code()}'")
            except Exception as e:
                LOG.warning(f"Failed to get targeting state for row {index + 1}: '{e}'")
            async with self._targeting_lock:
                self._targeting[index] = targeting
            await asyncio.sleep(5)

    async def _get_targeting_row_count(self) -> int:
        total = 0
        async with self._targeting_lock:
            for state in self._targeting:
                if state:
                    total += 1
        return total

    async def start_block(self, block_id: int, timestamp: int) -> None:
        try:
            row_width_mm = self._row_width_conf.get_float_value() * 25.4
            total_width_mm = (await self._get_targeting_row_count()) * row_width_mm
            async with self._lock:
                self._in_progress_blocks[block_id] = total_width_mm
        except Exception as e:
            LOG.warning(f"Failed to get width for block {block_id}: {e}")

    async def end_block(self, block_id: int, timestamp: int) -> None:
        async with self._lock:
            if block_id not in self._in_progress_blocks:
                LOG.warning(f"Missing start data for block {block_id}")
        await self.ack(block_id)

    async def get_width(self, block_id: int) -> float:
        async with self._lock:
            if block_id in self._in_progress_blocks:
                return self._in_progress_blocks[block_id]
        return 0.0

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        async with self._lock:
            if block_id in self._in_progress_blocks:
                block.implement_width_data.width_mm = self._in_progress_blocks[block_id]
                del self._in_progress_blocks[block_id]

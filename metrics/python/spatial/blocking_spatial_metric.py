import asyncio

import lib.common.logging
from lib.common.bot.stop_handler import bot_stop_handler
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.spatial_metric import SpatialMetic

LOG = lib.common.logging.get_logger(__name__)


class BlockingSpatialMetric(SpatialMetic):
    def __init__(self, owner: SpatialBlockOwner) -> None:
        self._owner = owner
        self._client_id = 0

    async def start(self) -> None:
        self._lock = asyncio.Lock()
        asyncio.get_event_loop().create_task(self.__beat())

    async def __client_id(self) -> int:
        async with self._lock:
            if self._client_id == 0:
                self._client_id = self._owner.register_client()
            return self._client_id

    async def __beat(self) -> None:
        while not bot_stop_handler.stopped:
            client_id = await self.__client_id()
            if not self._owner.beat(client_id):
                async with self._lock:
                    self._client_id = 0
            await asyncio.sleep(10)

    async def ack(self, block_id: int) -> None:
        client_id = await self.__client_id()
        if not self._owner.ack(client_id=client_id, block_id=block_id):
            async with self._lock:
                self._client_id = 0

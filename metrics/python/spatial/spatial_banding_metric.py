from typing import Dict

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock
from lib.common.metrics.banding_percentage_checker import Banding<PERSON>heckerError, BandingPercentageChecker
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.blocking_spatial_metric import BlockingSpatialMetric

LOG = lib.common.logging.get_logger(__name__)


class SpatialBandingMetric(BlockingSpatialMetric):
    def __init__(self, owner: SpatialBlockOwner) -> None:
        super().__init__(owner)
        self._in_progress_blocks: Dict[int, float] = {}

        config_subscriber = get_global_config_subscriber()
        common_conf = config_subscriber.get_config_node("common", "")
        self._checker = BandingPercentageChecker(common_conf)

    async def start_block(self, block_id: int, timestamp: int) -> None:
        try:
            percent = await self._checker.get_percentage_banded()
            async with self._lock:
                self._in_progress_blocks[block_id] = percent
        except BandingCheckerError as ex:
            LOG.warning(
                f"Failed to get banding percent for block {block_id}. err code = {ex.code}, while getting data from {ex.hostname}"
            )
        except Exception as e:
            LOG.warning(f"Failed to get banding percent for block {block_id}: {e}")

    async def end_block(self, block_id: int, timestamp: int) -> None:
        async with self._lock:
            if block_id not in self._in_progress_blocks:
                LOG.warning(f"Missing start data for block {block_id}")
        await self.ack(block_id)

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        async with self._lock:
            if block_id in self._in_progress_blocks:
                block.banding_data.percent_banded = self._in_progress_blocks[block_id]
                del self._in_progress_blocks[block_id]

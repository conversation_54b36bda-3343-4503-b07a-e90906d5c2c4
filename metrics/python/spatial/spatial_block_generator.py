import asyncio
import copy
import math
import uuid
from collections import deque
from typing import Awaitable, Callable, Deque, List, Optional, Tuple

import navpy
import numpy as np
import pyproj
from aioredis import Redis

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from generated.hardware_manager.proto.hardware_manager_service_pb2 import GetGPSDataResponse
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock, SpatialPosition
from hardware_manager.python.client import HardwareManagerClient
from lib.common.bot.stop_handler import bot_stop_handler
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.spatial_metric import SpatialMetic

LOG = lib.common.logging.get_logger(__name__)

SLEEP_INTERVAL_SECONDS = 100 / 1000
BLOCKS_READY_FOR_SYNC = "/spatial/sync_queue"
BLOCKS_HASH = "/spatial/blocks"
BLOCKS_DEBUG = "/spatial/debug"


class SpatialPos:
    def __init__(self, gps_data: Optional[GetGPSDataResponse] = None) -> None:
        if gps_data is not None:
            self.lat = gps_data.lla.lat
            self.lon = gps_data.lla.lng
            self.height_mm = int(gps_data.lla.alt * 1000)
            self.ts = gps_data.lla.timestamp_ms
            self.x = gps_data.ecef.x
            self.y = gps_data.ecef.y
            self.z = gps_data.ecef.z
        else:
            self.lat = 0
            self.lon = 0
            self.height_mm = 0
            self.ts = 0
            ecef = navpy.lla2ecef(self.lat, self.lon, self.height_mm / 1000, "deg")
            self.x = ecef[0]
            self.y = ecef[1]
            self.z = ecef[2]

    def __str__(self) -> str:
        return f"ecef:({self.x}, {self.y}, {self.z})"

    def __repr__(self) -> str:
        return self.__str__()

    def to_proto(self) -> SpatialPosition:
        return SpatialPosition(
            latitude=self.lat,
            longitude=self.lon,
            height_mm=self.height_mm,
            timestamp_ms=self.ts,
            ecef_x=self.x,
            ecef_y=self.y,
            ecef_z=self.z,
        )

    def apply_offset(self, bearing_rad: float, x_off: float, y_off: float, z_off: float) -> None:
        # establish offsets in enu
        """
        bearing is degrees from north clockwise,
        -bearing makes it degrees from the x-axis (east) counter-clockwise
        to get the total x change from both the x and y offsets (x_r, y_r) in the robot`s coordinates
        x_r * cos(-bearing) + y_r * cos(-bearing + 90), simplifies to
        x_r * cos(-bearing) + y_r * sin(bearing)
        """
        enu = np.array(
            [
                [x_off * math.cos(-bearing_rad) + y_off * math.sin(bearing_rad)],  # E or X+
                [x_off * math.sin(-bearing_rad) + y_off * math.cos(bearing_rad)],  # N or Y+
                [-z_off],  # U or Z+
            ]
        )
        # math: https://gis.stackexchange.com/questions/308445/local-enu-point-of-interest-to-ecef

        lat_rad = math.radians(self.lat)
        lon_rad = math.radians(self.lon)
        conv_mat = np.array(
            [
                [-math.sin(lon_rad), -math.sin(lat_rad) * math.cos(lon_rad), math.cos(lat_rad) * math.cos(lon_rad)],
                [math.cos(lon_rad), -math.sin(lat_rad) * math.sin(lon_rad), math.cos(lat_rad) * math.sin(lon_rad)],
                [0, math.cos(lat_rad), math.sin(lat_rad)],
            ]
        )

        origin_ecef = np.array([[self.x], [self.y], [self.z]])
        ecef_mat = np.add(np.matmul(conv_mat, enu), origin_ecef)
        new_ecef = [ecef_mat[0][0], ecef_mat[1][0], ecef_mat[2][0]]
        new_lla = navpy.ecef2lla(new_ecef, "deg")

        self.lat = new_lla[0]
        self.lon = new_lla[1]
        self.height_mm = new_lla[2] * 1000
        self.x = new_ecef[0]
        self.y = new_ecef[1]
        self.z = new_ecef[2]


class SpatialBlock:
    def __init__(self, start: SpatialPos, end: SpatialPos, bearing_rad: float, id: int, suspicious: bool) -> None:
        self.start = start
        self.end = end
        self.id = id
        self.start_left = copy.deepcopy(start)
        self.start_right = copy.deepcopy(start)
        self.end_left = copy.deepcopy(end)
        self.end_right = copy.deepcopy(end)
        self.bearing_rad = bearing_rad
        self.suspicious = suspicious

    def update_corners(self, width_mm: float) -> None:
        width_m = width_mm / 1000
        # will always be centered,
        # TODO: figure out what row are active and offset accordingly
        self.start_left.apply_offset(self.bearing_rad, -width_m / 2, 0, 0)
        self.start_right.apply_offset(self.bearing_rad, width_m / 2, 0, 0)
        self.end_left.apply_offset(self.bearing_rad, -width_m / 2, 0, 0)
        self.end_right.apply_offset(self.bearing_rad, width_m / 2, 0, 0)


async def BuildSpatialBlockGenerator(
    hmc: HardwareManagerClient,
    redis: Redis,
    owner: SpatialBlockOwner,
    metrics: List[SpatialMetic],
    width_fetcher: Callable[[int], Awaitable[float]],
) -> "SpatialBlockGenerator":
    s = SpatialBlockGenerator(hmc, redis, owner, metrics, width_fetcher)
    await s._async_init()
    return s


class SpatialBlockGenerator:
    def __init__(
        self,
        hmc: HardwareManagerClient,
        redis: Redis,
        owner: SpatialBlockOwner,
        metrics: List[SpatialMetic],
        width_fetcher: Callable[[int], Awaitable[float]],
    ) -> None:
        self._hmc = hmc
        self._block_id = 1
        self._cur_pos = SpatialPos()
        self._owner = owner
        self._metrics = metrics
        self._redis = redis
        self._width_fetcher = width_fetcher
        self._length_conf = get_global_config_subscriber().get_config_node("metrics_aggregator", "spatial_block_length")
        self._debug = get_global_config_subscriber().get_config_node("metrics_aggregator", "spatial_block_debug")
        self._debug_ttl = get_global_config_subscriber().get_config_node(
            "metrics_aggregator", "spatial_block_debug_ttl"
        )
        self._x_offset_config = (
            get_global_config_subscriber()
            .get_config_node("common", "geometry")
            .get_node("gps")
            .get_node("gps_offset_x")
        )
        self._y_offset_config = (
            get_global_config_subscriber()
            .get_config_node("common", "geometry")
            .get_node("gps")
            .get_node("gps_offset_y")
        )
        self._z_offset_config = (
            get_global_config_subscriber()
            .get_config_node("common", "geometry")
            .get_node("gps")
            .get_node("gps_offset_z")
        )
        self._geod = pyproj.Geod(ellps="WGS84")

    async def _async_init(self) -> None:
        self._queue: asyncio.Queue[SpatialBlock] = asyncio.Queue()
        self._cond = asyncio.Condition()

        await asyncio.gather(*[metric.start() for metric in self._metrics])
        asyncio.get_event_loop().create_task(self.__block_gen())
        asyncio.get_event_loop().create_task(self.__block_writter())

    async def get_pos(self) -> SpatialPos:
        try:
            gps_data = await self._hmc.get_gps()
            return SpatialPos(gps_data)
        except Exception as ex:
            LOG.error(f"Error retrieving gps data for Spatial Block Generator: {ex}")
            return SpatialPos()

    async def __init_pos(self) -> None:
        while not bot_stop_handler.stopped:
            cur_pos = await self.get_pos()
            if self.pos_make_sense(cur_pos):
                self._cur_pos = cur_pos
                return
            await asyncio.sleep(SLEEP_INTERVAL_SECONDS)

    async def __start_block(self, block_id: int, timestamp: int) -> None:
        LOG.info(f"Starting block {block_id}")
        await asyncio.gather(*[metric.start_block(block_id, timestamp) for metric in self._metrics])

    async def __end_block(self, block_id: int, timestamp: int) -> None:
        await asyncio.gather(*[metric.end_block(block_id, timestamp) for metric in self._metrics])

    async def __block_gen(self) -> None:
        await self.__init_pos()
        asyncio.get_event_loop().create_task(self.__start_block(self._block_id, self._cur_pos.ts))
        while not bot_stop_handler.stopped:
            # offsets in config are in mm, convert to meters
            x_off = self._x_offset_config.get_float_value() / 1000
            y_off = self._y_offset_config.get_float_value() / 1000
            z_off = self._z_offset_config.get_float_value() / 1000
            await asyncio.sleep(SLEEP_INTERVAL_SECONDS)
            pos = await self.get_pos()
            change, suspicious = self.__change_block(self._cur_pos, pos)
            if change:
                bearing_rad = self.__get_bearing(self._cur_pos, pos)
                cur_pos_adj = copy.deepcopy(self._cur_pos)
                pos_adj = copy.deepcopy(pos)
                cur_pos_adj.apply_offset(bearing_rad, x_off, y_off, z_off)
                pos_adj.apply_offset(bearing_rad, x_off, y_off, z_off)
                block = SpatialBlock(cur_pos_adj, pos_adj, bearing_rad, self._block_id, suspicious)
                asyncio.get_event_loop().create_task(self.__end_block(self._block_id, pos.ts))
                async with self._cond:
                    self._block_id += 1
                    self._cond.notify_all()
                self._cur_pos = pos
                asyncio.get_event_loop().create_task(self.__start_block(self._block_id, pos.ts))
                await self._queue.put(block)

    async def __ready_to_write(self, block: SpatialBlock) -> bool:
        return await asyncio.get_event_loop().run_in_executor(None, lambda: self._owner.can_close(block.id))

    async def __write_block(self, block: SpatialBlock) -> None:
        block.update_corners(await self._width_fetcher(block.id))
        metric_block = SpatialMetricBlock(
            start=block.start.to_proto(),
            end=block.end.to_proto(),
            start_left=block.start_left.to_proto(),
            start_right=block.start_right.to_proto(),
            end_left=block.end_left.to_proto(),
            end_right=block.end_right.to_proto(),
            suspicious=block.suspicious,
        )
        await asyncio.gather(*[metric.write_block(block.id, metric_block) for metric in self._metrics])
        block_uuid = str(uuid.uuid1())
        block_encoded = metric_block.SerializeToString()
        await self._redis.hset(BLOCKS_HASH, block_uuid, block_encoded)
        await self._redis.sadd(BLOCKS_READY_FOR_SYNC, block_uuid)
        try:
            if self._debug.get_bool_value():
                await self._redis.set(
                    f"{BLOCKS_DEBUG}/{block_uuid}", block_encoded, ex=self._debug_ttl.get_uint_value()
                )
        except Exception:
            LOG.exception(f"failed to write debug info for block {block_uuid}:{block.id}")

        LOG.info(f"Wrote block {block.id} to redis as {block_uuid}")

    async def __block_writter(self) -> None:
        blocks_to_write: Deque[SpatialBlock] = deque()
        while not bot_stop_handler.stopped:
            if len(blocks_to_write) == 0:
                block = await self._queue.get()
                blocks_to_write.append(block)
            while not self._queue.empty():
                try:
                    blocks_to_write.append(self._queue.get_nowait())
                except asyncio.QueueEmpty:
                    break
            if await self.__ready_to_write(blocks_to_write[0]):
                asyncio.get_event_loop().create_task(self.__write_block(blocks_to_write.popleft()))
            else:
                await asyncio.sleep(5)

    def pos_make_sense(self, pos: SpatialPos) -> bool:
        if pos.ts == 0:
            return False
        if (pos.lat > -1 and pos.lat < 1) and (pos.lon > -1 and pos.lon < 1):
            return False
        return True

    def __change_block(self, start_pos: SpatialPos, cur_pos: SpatialPos) -> Tuple[bool, bool]:
        if (not self.pos_make_sense(start_pos)) or (not self.pos_make_sense(cur_pos)):
            LOG.error(f"filter triggered, start: {start_pos}, cur: {cur_pos}")
            return False, False
        delta = math.sqrt(
            (cur_pos.x - start_pos.x) ** 2 + (cur_pos.y - start_pos.y) ** 2 + (cur_pos.z - start_pos.z) ** 2
        )
        length = self._length_conf.get_float_value()
        if delta > length:
            return True, delta > (length * 5)
        else:
            return False, False

    def __get_bearing(self, start_pos: SpatialPos, cur_pos: SpatialPos) -> float:
        fwd_azimuth, _, _ = self._geod.inv(start_pos.lon, start_pos.lat, cur_pos.lon, cur_pos.lat)
        return math.radians(fwd_azimuth)

    async def register_client(self) -> int:
        return await asyncio.get_event_loop().run_in_executor(None, self._owner.register_client)

    async def beat(self, client_id: int) -> bool:
        return await asyncio.get_event_loop().run_in_executor(None, lambda: self._owner.beat(client_id))

    async def ack(self, client_id: int, block_id: int) -> bool:
        return await asyncio.get_event_loop().run_in_executor(None, lambda: self._owner.ack(client_id, block_id))

    async def get_next_block(self, block_id: int) -> int:
        while not bot_stop_handler.stopped:
            async with self._cond:
                if self._block_id > block_id:
                    return self._block_id
                await self._cond.wait()
        return self._block_id

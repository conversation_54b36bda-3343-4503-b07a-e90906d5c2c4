import aioredis

import lib.common.logging
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock, WeedCounterChunk
from metrics.pybind.metrics_python import ConclusionType
from metrics.python.spatial.spatial_metric import SpatialMetic

LOG = lib.common.logging.get_logger(__name__)

SPATIAL_WEED_BLOCKS_KEY = "/spatial/weed_blocks"


class SpatialWeedCounterWritter(SpatialMetic):
    def __init__(self, redis: aioredis.Redis) -> None:
        self._redis = redis

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        keys = await self._redis.hkeys(SPATIAL_WEED_BLOCKS_KEY)
        initialized = False
        for bin_key in keys:
            key: str = bin_key.decode()
            if key.startswith(f"{block_id}/row"):
                data = await self._redis.hget(SPATIAL_WEED_BLOCKS_KEY, key)
                decoded = WeedCounterChunk.FromString(data)
                await self._redis.hdel(SPATIAL_WEED_BLOCKS_KEY, key)
                expected = ConclusionType.kConclusionTypeNumber.value
                if (
                    len(decoded.conclusion_counts.disarmed_weed) != expected
                    or len(decoded.conclusion_counts.armed_weed) != expected
                    or len(decoded.conclusion_counts.disarmed_crop) != expected
                    or len(decoded.conclusion_counts.armed_crop) != expected
                ):
                    LOG.warn(f"Skipping {key}, as it has invalid data")
                    continue
                if not initialized:
                    for val in decoded.conclusion_counts.disarmed_weed:
                        block.weed_count.conclusion_counts.disarmed_weed.append(val)
                    for val in decoded.conclusion_counts.armed_weed:
                        block.weed_count.conclusion_counts.armed_weed.append(val)
                    for val in decoded.conclusion_counts.disarmed_crop:
                        block.weed_count.conclusion_counts.disarmed_crop.append(val)
                    for val in decoded.conclusion_counts.armed_crop:
                        block.weed_count.conclusion_counts.armed_crop.append(val)
                    block.weed_count.weed_size_data.count = decoded.weed_size_data.count
                    block.weed_count.weed_size_data.cumulative_size = decoded.weed_size_data.cumulative_size
                    block.weed_count.crop_size_data.count = decoded.crop_size_data.count
                    block.weed_count.crop_size_data.cumulative_size = decoded.crop_size_data.cumulative_size
                    block.weed_count.targeted_laser_time_data.count = decoded.targeted_laser_time_data.count
                    block.weed_count.targeted_laser_time_data.cumulative_time = (
                        decoded.targeted_laser_time_data.cumulative_time
                    )
                    block.weed_count.untargeted_laser_time_data.count = decoded.untargeted_laser_time_data.count
                    block.weed_count.untargeted_laser_time_data.cumulative_time = (
                        decoded.untargeted_laser_time_data.cumulative_time
                    )
                    block.weed_count.valid_crop_count = decoded.valid_crop_count
                else:
                    for (i, val) in enumerate(decoded.conclusion_counts.disarmed_weed):
                        block.weed_count.conclusion_counts.disarmed_weed[i] += val
                    for (i, val) in enumerate(decoded.conclusion_counts.armed_weed):
                        block.weed_count.conclusion_counts.armed_weed[i] += val
                    for (i, val) in enumerate(decoded.conclusion_counts.disarmed_crop):
                        block.weed_count.conclusion_counts.disarmed_crop[i] += val
                    for (i, val) in enumerate(decoded.conclusion_counts.armed_crop):
                        block.weed_count.conclusion_counts.armed_crop[i] += val
                    block.weed_count.weed_size_data.count += decoded.weed_size_data.count
                    block.weed_count.weed_size_data.cumulative_size += decoded.weed_size_data.cumulative_size
                    block.weed_count.crop_size_data.count += decoded.crop_size_data.count
                    block.weed_count.crop_size_data.cumulative_size += decoded.crop_size_data.cumulative_size

                    block.weed_count.untargeted_laser_time_data.count += decoded.untargeted_laser_time_data.count
                    block.weed_count.untargeted_laser_time_data.cumulative_time += (
                        decoded.untargeted_laser_time_data.cumulative_time
                    )
                    block.weed_count.valid_crop_count += decoded.valid_crop_count
                initialized = True
                for cat, val in decoded.counts_by_category.items():
                    if cat in block.weed_count.counts_by_category:
                        block.weed_count.counts_by_category[cat] += val
                    else:
                        block.weed_count.counts_by_category[cat] = val

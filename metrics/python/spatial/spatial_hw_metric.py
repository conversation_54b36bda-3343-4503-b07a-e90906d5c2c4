import asyncio
from typing import Dict

import lib.common.logging
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock
from hardware_manager.python.client import HardwareManagerClient, SafetyStatus
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.generation import get_command_ip
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.blocking_spatial_metric import BlockingSpatialMetric

LOG = lib.common.logging.get_logger(__name__)


class SpatialHWMetric(BlockingSpatialMetric):
    def __init__(self, owner: SpatialBlockOwner) -> None:
        super().__init__(owner)
        self._in_progress_blocks: Dict[int, SafetyStatus] = {}
        self._cur_block = -1
        self._hw_client = HardwareManagerClient(hostname=get_command_ip())

    async def start(self) -> None:
        await super().start()
        self._block_lock = asyncio.Lock()
        while True:
            try:
                self._last_status = await self._hw_client.safety_status()
                break
            except Exception:
                LOG.exception("Failed to get initial status")
                await asyncio.sleep(1)
        asyncio.get_event_loop().create_task(self._check_hw_status())

    async def _check_hw_status(self) -> None:
        while not bot_stop_handler.stopped:
            try:
                results = await self._hw_client.safety_status()
                async with self._block_lock:
                    self._last_status = results
                    if self._cur_block in self._in_progress_blocks:
                        self._in_progress_blocks[self._cur_block] |= results
            except Exception as e:
                LOG.error(f"Unknown error getting hw status: {e}")
            await asyncio.sleep(5)

    async def start_block(self, block_id: int, timestamp: int) -> None:
        async with self._block_lock:
            self._in_progress_blocks[block_id] = self._last_status
            self._cur_block = block_id

    async def end_block(self, block_id: int, timestamp: int) -> None:
        async with self._block_lock:
            self._cur_block = -1
        await self.ack(block_id)

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        async with self._block_lock:
            if block_id in self._in_progress_blocks:
                data = self._in_progress_blocks[block_id]
                block.hw_metric.lifted = data.lifted
                block.hw_metric.estopped = data.estopped
                block.hw_metric.laser_key = data.laser_key
                block.hw_metric.interlock = data.interlock
                block.hw_metric.water_protect = data.water_protect
                block.hw_metric.debug_mode = data.debug_mode
                del self._in_progress_blocks[block_id]

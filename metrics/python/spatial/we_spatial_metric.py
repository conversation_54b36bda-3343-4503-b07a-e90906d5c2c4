from typing import Dict

import lib.common.logging
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock, WheelEncoderSpatialData
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.blocking_spatial_metric import BlockingSpatialMetric
from wheel_encoder.pybind.wheel_encoder_python import WheelEncoder

LOG = lib.common.logging.get_logger(__name__)


class StartStopPos:
    def __init__(self, start_pos: float) -> None:
        self._start = start_pos
        self._end = start_pos

    def end(self, end_pos: float) -> None:
        self._end = end_pos

    def to_proto(self, proto: WheelEncoderSpatialData) -> None:
        proto.start_pos_m = self._start
        proto.end_pos_m = self._end


class WESpatialMetric(BlockingSpatialMetric):
    def __init__(self, owner: SpatialBlockOwner) -> None:
        super().__init__(owner)
        self._encoder = WheelEncoder.get()
        self._in_progress_blocks: Dict[int, StartStopPos] = {}

    async def start_block(self, block_id: int, timestamp: int) -> None:
        cur_pos = self._encoder.total_meters()
        async with self._lock:
            self._in_progress_blocks[block_id] = StartStopPos(cur_pos)

    async def end_block(self, block_id: int, timestamp: int) -> None:
        cur_pos = self._encoder.total_meters()
        async with self._lock:
            if block_id in self._in_progress_blocks:
                self._in_progress_blocks[block_id].end(cur_pos)
            else:
                LOG.warning(f"Missing start data for block {block_id}")
                self._in_progress_blocks[block_id] = StartStopPos(cur_pos)
                self._in_progress_blocks[block_id].end(cur_pos)
        await self.ack(block_id)

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        async with self._lock:
            if block_id in self._in_progress_blocks:
                self._in_progress_blocks[block_id].to_proto(block.we_data)
                del self._in_progress_blocks[block_id]

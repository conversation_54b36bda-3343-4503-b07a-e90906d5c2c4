import asyncio
from abc import abstractmethod
from collections import deque
from typing import Deque, Dict, Generator, List, Tuple

import aioredis
import grpc

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import AimbotClient
from generated.proto.metrics.metrics_pb2 import SpatialMetricBlock
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.generation import is_reaper, rows
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from lib.common.time import maka_control_timestamp_ms
from metrics.pybind.metrics_python import SpatialBlockOwner
from metrics.python.spatial.blocking_spatial_metric import BlockingSpatialMetric

LOG = lib.common.logging.get_logger(__name__)


def get_row_ips() -> Generator[Tuple[str, str], None, None]:
    disabled = get_global_config_subscriber().get_config_node("common", "disabled_rows")
    disabled_names = set([f"row{ch.get_name()}" for ch in disabled.get_children_nodes()])
    aimbot_addresses = rows()
    if is_reaper():
        aimbot_addresses = RobotDefinition.get().get_aimbot_addresses()
        LOG.info(f"Is Reaper, aimbot addresses: {aimbot_addresses}")
    for name, ip in aimbot_addresses.items():
        if name not in disabled_names:
            yield name, ip


class VelDataProducer:
    def __init__(self, name: str) -> None:
        self._name = name
        self._deque: Deque[Tuple[int, float]] = deque(maxlen=200)

    async def start(self) -> None:
        self._lock = asyncio.Lock()
        asyncio.get_event_loop().create_task(self._vel_loop())

    async def get_avg_vel(self, start_time: int, end_time: int) -> float:
        async with self._lock:
            snapshot = list(self._deque)
        count = 0
        total = 0.0
        for data_point in snapshot:
            if data_point[0] >= start_time:
                if data_point[0] <= end_time:
                    count += 1
                    total += data_point[1]
                else:
                    break
        if count > 0:
            return total / count
        return -1.0

    @property
    def name(self) -> str:
        return self._name

    @abstractmethod
    async def _vel_loop(self) -> None:
        ...


class VelFetcher(VelDataProducer):
    def __init__(self, name: str, hostname: str) -> None:
        super().__init__(name)
        self._client = AimbotClient(hostname=hostname)
        self._hostname = hostname

    async def _vel_loop(self) -> None:
        while not bot_stop_handler.stopped:
            try:
                resp = await self._client.get_target_velocity()
                now = maka_control_timestamp_ms()
                if resp[0] >= 0:
                    avg = resp[0] / 1000  # only use min as that is primary convert mm/s to m/s
                    async with self._lock:
                        self._deque.append((now, avg))
            except grpc.aio.AioRpcError as ex:
                LOG.warning(
                    f"Failed to fetch target velocity from '{self.name}({self._hostname})'. Err code: {ex.code()}"
                )
            except Exception as ex:
                LOG.warning(f"Failed to fetch target velocity. Err: {ex}")
            await asyncio.sleep(1)


class VelReader(VelDataProducer):
    def __init__(self, redis: aioredis.Redis) -> None:
        super().__init__("cmd")
        self._redis = redis

    async def _vel_loop(self) -> None:
        while not bot_stop_handler.stopped:
            try:
                resp = await self._redis.get("/tve/primary_vel")
                now = maka_control_timestamp_ms()
                if resp is not None and resp != "":
                    avg = float(resp) * 0.44704  # convert mph back to m/s
                    async with self._lock:
                        self._deque.append((now, avg))
            except Exception as ex:
                LOG.warning(f"Failed to read target velocity. Err: {ex}")
            await asyncio.sleep(1)


class VelBlock:
    def __init__(self, start_time: int, fetcher: VelDataProducer) -> None:
        self._start_time = start_time
        self._fetcher = fetcher
        self._end_time = 0
        self._vel = 0.0

    async def end(self, end_time: int) -> None:
        self._end_time = end_time
        self._vel = await self._fetcher.get_avg_vel(self._start_time, self._end_time)

    @property
    def velocity(self) -> float:
        return self._vel

    @property
    def name(self) -> str:
        return self._fetcher.name


class SpatialVelocityMetric(BlockingSpatialMetric):
    def __init__(self, owner: SpatialBlockOwner, redis: aioredis.Redis) -> None:
        super().__init__(owner)
        self._clients = [VelFetcher(name=name, hostname=ip) for name, ip in get_row_ips()]
        self._reader = VelReader(redis)
        self._in_progress_blocks: Dict[int, List[VelBlock]] = {}

    async def start(self) -> None:
        await super().start()
        for fetcher in self._clients:
            await fetcher.start()
        await self._reader.start()

    async def start_block(self, block_id: int, timestamp: int) -> None:
        try:
            blocks = [VelBlock(timestamp, fetcher) for fetcher in self._clients]
            blocks.append(VelBlock(timestamp, self._reader))
            async with self._lock:
                self._in_progress_blocks[block_id] = blocks
        except Exception as e:
            LOG.warning(f"Failed to get vel for block {block_id}: {e}")

    async def end_block(self, block_id: int, timestamp: int) -> None:
        async with self._lock:
            if block_id not in self._in_progress_blocks:
                LOG.warning(f"Missing start data for block {block_id}")
            else:
                for block in self._in_progress_blocks[block_id]:
                    await block.end(timestamp)
        await self.ack(block_id)

    async def write_block(self, block_id: int, block: SpatialMetricBlock) -> None:
        async with self._lock:
            if block_id in self._in_progress_blocks:
                for vel_block in self._in_progress_blocks[block_id]:
                    block.vel_data.avg_target_vel[vel_block.name] = vel_block.velocity
                del self._in_progress_blocks[block_id]

#pragma once
#include <memory>
#include <mutex>
#include <vector>

namespace prometheus {
class Registry;
class Exposer;
} // namespace prometheus
namespace carbon::metrics {
class ExposerOwner {
public:
  static void register_collectable(std::shared_ptr<prometheus::Registry> r);

private:
  ExposerOwner();
  ExposerOwner(const ExposerOwner &) = delete;
  ExposerOwner &operator=(const ExposerOwner &) = delete;
  std::mutex mut_;
  std::vector<std::shared_ptr<prometheus::Registry>> registries_;
  std::unique_ptr<prometheus::Exposer> exposer_;

  void _register_collectable(std::shared_ptr<prometheus::Registry> r);
};
} // namespace carbon::metrics
#include "metrics/cpp/prometheus/exposer_owner.hpp"

#include <cstdlib>
#include <stdexcept>

#include <fmt/format.h>
#include <prometheus/exposer.h>
#include <prometheus/registry.h>
#include <spdlog/spdlog.h>

// Exposer exposer(fmt::format("0.0.0.0:{}", kMetricsListenPort));

// std::unique_ptr<prometheus::Exposer> exposer_;
#define METRICS_PORT_ENV_NAME "SERVICE_CPP_METRICS_PORT"

std::string get_metrics_port() {
  char *val_ptr = std::getenv(METRICS_PORT_ENV_NAME);
  std::string val = "";
  if (val_ptr != NULL) {
    val += val_ptr;
  }
  if (val == "") {
    throw std::runtime_error("No port defined for metrics service");
  }
  return val;
}
namespace carbon::metrics {
ExposerOwner::ExposerOwner()
    : exposer_(std::make_unique<prometheus::Exposer>(fmt::format("0.0.0.0:{}", get_metrics_port()))) {}

void ExposerOwner::register_collectable(std::shared_ptr<prometheus::Registry> r) {
  static ExposerOwner inst;
  inst._register_collectable(r);
}
void ExposerOwner::_register_collectable(std::shared_ptr<prometheus::Registry> r) {
  std::lock_guard<std::mutex> lk(mut_);
  registries_.emplace_back(r); // exposer does not own registry so we will instead
  exposer_->RegisterCollectable(r);
}
} // namespace carbon::metrics
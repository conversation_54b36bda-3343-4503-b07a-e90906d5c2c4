#include "metrics/cpp/prometheus/time_gauge_guard.hpp"

#include <prometheus/gauge.h>

namespace carbon::metrics {
TimeGauge::Guard::Guard(TimeGauge *tg) : tg_(tg), start_(std::chrono::steady_clock::now()), cancel_(false) {}
TimeGauge::Guard::~Guard() {
  if (cancel_) {
    return;
  }
  auto delta = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start_);
  tg_->add(delta);
}
TimeGauge::TimeGauge(std::reference_wrapper<prometheus::Gauge> g, size_t win_size)
    : gauge_(g), count_(0), times_(win_size) {}
TimeGauge::Guard TimeGauge::time() { return Guard(this); }
void TimeGauge::add(std::chrono::milliseconds delta) {
  times_.add(delta.count());
  count_ = (count_ + 1) % times_.size();
  if (count_ == 0) {
    gauge_.get().Set(times_.avg());
  }
}
} // namespace carbon::metrics

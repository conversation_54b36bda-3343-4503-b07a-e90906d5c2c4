#pragma once

#include <chrono>
#include <functional>

#include <lib/common/cpp/utils/moving_average.hpp>

namespace prometheus {
class Gauge;
} // namespace prometheus
namespace carbon::metrics {
class TimeGauge {
public:
  class Guard {
  public:
    ~Guard();
    inline void cancel() { cancel_ = true; };
    friend class TimeGauge;

  private:
    Guard(TimeGauge *tg);
    Guard() = delete;
    Guard(const Guard &) = delete;
    Guard &operator=(const Guard &) = delete;
    TimeGauge *tg_;
    std::chrono::steady_clock::time_point start_;
    bool cancel_;
  };
  friend class Guard;
  TimeGauge(std::reference_wrapper<prometheus::Gauge> g, size_t win_size);
  Guard time();

private:
  std::reference_wrapper<prometheus::Gauge> gauge_;
  size_t count_;
  common::MovingAverage<float> times_;

  void add(std::chrono::milliseconds delta);
};
} // namespace carbon::metrics
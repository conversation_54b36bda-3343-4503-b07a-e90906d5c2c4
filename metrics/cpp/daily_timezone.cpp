#include "metrics/cpp/daily_timezone.hpp"

#include <config/client/cpp/config_subscriber.hpp>

#include <ctime>

#include <date/tz.h>
#include <spdlog/spdlog.h>
#include <zonedetect.h>

namespace carbon::metrics {
constexpr auto kDefaultTz = "America/Los_Angeles";
constexpr auto tz_key = "timezone";
std::string get_timezone(double lat, double lon) {
  ZoneDetect *const cd = ZDOpenDatabase("/robot/lib/common/zonedetect/timezone21.bin");
  if (!cd) {
    spdlog::info("can't open timezone db, assuming we're in {}", kDefaultTz);
    return kDefaultTz;
  }

  float safezone = 0;
  ZoneDetectResult *results = ZDLookup(cd, (float)lat, (float)lon, &safezone);

  unsigned int index = 0;
  std::string tzprefix;
  std::string tzid;

  while (results[index].lookupResult != ZD_LOOKUP_END) {
    if (results[index].lookupResult == ZD_LOOKUP_IN_ZONE) {
      if (results[index].data) {
        for (unsigned int i = 0; i < results[index].numFields; i++) {
          if (results[index].fieldNames[i] && results[index].data[i]) {
            if (strcmp(results[index].fieldNames[i], "TimezoneId") == 0) {
              tzid = results[index].data[i];
            }
            if (strcmp(results[index].fieldNames[i], "TimezoneIdPrefix") == 0) {
              tzprefix = results[index].data[i];
            }
          }
        }
      }
    }
    index++;
  }
  ZDFreeResults(results);
  ZDCloseDatabase(cd);

  if (tzprefix != "" && tzid != "") {
    return tzprefix + tzid;
  } else {
    spdlog::info("couldn't find tz for coordinates {} {}, assuming we're in {}", lat, lon, kDefaultTz);
    return kDefaultTz;
  }
}
DailyTimezone::DailyTimezone(bool owner)
    : owner_(owner),
      hw_client_(carbon::config::make_robot_local_addr(hardware_manager::HardwareManagerClient::DEFAULT_PORT)),
      day_str_(""), expiration_(std::chrono::system_clock::now() - std::chrono::hours(1)) {}
std::string DailyTimezone::get_day() {
  update();
  return day_str_;
}
template <class Duration, class TimeZonePtr>
std::chrono::system_clock::duration duration_till_midnight(const date::zoned_time<Duration, TimeZonePtr> &t) {
  auto t_local = t.get_local_time();
  auto today_local = std::chrono::floor<date::days>(t_local);
  auto time_of_today = t_local - today_local;
  return std::chrono::hours(24) - time_of_today;
}
void DailyTimezone::update() {
  auto now = std::chrono::system_clock::now();
  std::unique_lock lk(mut_);
  if (now < expiration_) {
    return;
  }
  auto prev_tz = redis_.hget(tz_key, "zone");
  auto day = redis_.hget(tz_key, "day");
  bool reset_required = false;
  bool keep_prev = false;
  bool tz_aquired = false;
  std::string curr_tz = kDefaultTz;
  if (prev_tz && prev_tz->length() > 0) {
    curr_tz = *prev_tz; // If we fail to get gps data try to fallback to last known TZ better than hardcoded at least
  }
  try {
    auto gps_data = hw_client_.get_gps_data(true);
    curr_tz = get_timezone(gps_data.lla().lat(), gps_data.lla().lng());
    spdlog::info("Using gps pos ({}, {}), got TZ = {}", gps_data.lla().lat(), gps_data.lla().lng(), curr_tz);
    tz_aquired = true;
  } catch (const std::exception &ex) {
    spdlog::warn("Failed to get gps data using default time zone {}", curr_tz);
    tz_aquired = false;
  }
  if (!prev_tz || !day) {
    reset_required = true;
  }
  if (!reset_required && curr_tz != *prev_tz) {
    auto t = date::make_zoned(*prev_tz, now);
    std::string cur_day = date::format("%Y-%m-%d", t);
    spdlog::info("The current day for '{}' is {}", curr_tz, cur_day);
    if (cur_day != *day) {
      reset_required = true;
    } else {
      // Need to stay in current tz until we EOD
      keep_prev = true;
      day_str_ = cur_day;
      expiration_ = now + duration_till_midnight(t);
    }
  }
  if (!keep_prev) {
    reset_required = true;
  }
  if (reset_required) {
    auto t = date::make_zoned(curr_tz, now);
    day_str_ = date::format("%Y-%m-%d", t);
    if (tz_aquired) {
      try {
        auto d = duration_till_midnight(t);
        spdlog::info("saving data: day_str={}, tz={} for {} seconds", day_str_, curr_tz,
                     std::chrono::duration_cast<std::chrono::seconds>(d).count());
        save_tz(curr_tz);
        expiration_ = now + d;
      } catch (const std::exception &ex) {
        spdlog::warn("Failed to set current day/timezone, will try again");
      }
    } else {
      spdlog::info("Failed to find timezone, not saving data. day_str={}, tz={}", day_str_, curr_tz);
    }
  }
}

void DailyTimezone::save_tz(const std::string &tz) {
  if (!owner_) {
    return;
  }
  redis_.hset(tz_key, "zone", tz);
  redis_.hset(tz_key, "day", day_str_);
}
} // namespace carbon::metrics
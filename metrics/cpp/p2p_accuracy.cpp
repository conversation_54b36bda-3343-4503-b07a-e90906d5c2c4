#include "metrics/cpp/p2p_accuracy.hpp"

#include <algorithm>
#include <chrono>
#include <cmath>
#include <thread>
#include <unordered_map>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/moving_window_percent.hpp>
#include <scanner/cpp/scanner_wrapper.h>

#include <prometheus/exposer.h>
#include <prometheus/gauge.h>
#include <prometheus/histogram.h>
#include <prometheus/registry.h>
#include <spdlog/spdlog.h>

constexpr size_t MAX_COUNT = 15;
constexpr size_t MAX_WINDOW_SIZE = 100;
namespace carbon::metrics {
struct P2PAccuracyMetric {
  prometheus::Gauge &gauge;
  carbon::common::MovingWindowPercent<float> in_threshold;
  P2PAccuracyMetric(prometheus::Family<prometheus::Gauge> &gf, std::string &id, const std::string &type, size_t count,
                    float threshold)
      : gauge(
            gf.Add({{"scanner_id", id},
                    {"threshold", fmt::format("sub-{}mm", threshold)},
                    {"offset_type", type},
                    {"num_p2p_matches", count > MAX_COUNT ? fmt::format("gt_{}", MAX_COUNT) : std::to_string(count)}})),
        in_threshold(MAX_WINDOW_SIZE, [=](float val) { return val < threshold; }) {}
  void add(float value) {
    in_threshold.add(value);
    gauge.Set(in_threshold.percent());
  }
};
struct P2PAccuracyTypes {
  P2PAccuracyMetric pan_offset;
  P2PAccuracyMetric tilt_offset;
  P2PAccuracyMetric total_offset;
  P2PAccuracyTypes(prometheus::Family<prometheus::Gauge> &gf, std::string &id, size_t count, float threshold)
      : pan_offset(gf, id, "pan-offset", count, threshold), tilt_offset(gf, id, "tilt-offset", count, threshold),
        total_offset(gf, id, "total-offset", count, threshold) {}
};
const std::vector<float> thresholds = {1.0f, 2.0f, 2.75f, 3.5f, 5.0f, 7.0f, 10.0f};
struct P2PAccuracyScanner {
  std::vector<std::vector<P2PAccuracyTypes>> metrics;
  P2PAccuracyScanner(prometheus::Family<prometheus::Gauge> &gf, uint32_t _id) : metrics(MAX_COUNT + 1) {
    auto id = scanner::ScannerWrapper::make_identifier(_id);
    for (size_t i = 0; i <= MAX_COUNT; ++i) {
      for (auto threshold : thresholds) {
        metrics[i].emplace_back(gf, id, i + 1, threshold);
      }
    }
  }
  void add(const P2PAccuracy::DataPoint &dp) {
    size_t index = std::min(static_cast<size_t>(dp.num_p2p_matches - 1), MAX_COUNT);
    for (auto &metric : metrics[index]) {
      metric.pan_offset.add(dp.pan_offset);
      metric.tilt_offset.add(dp.tilt_offset);
      metric.total_offset.add(std::sqrt(dp.pan_offset * dp.pan_offset + dp.tilt_offset * dp.tilt_offset));
    }
  }
};
class P2PAccuracy::MetricsHolder {
public:
  MetricsHolder(std::shared_ptr<prometheus::Registry> registry, common::ThreadSafeQueue<DataPoint> &data_points)
      : data_points_(data_points), family_(prometheus::BuildGauge()
                                               .Name("aimbot_p2p_offset")
                                               .Help("Avg p2p offset by scanner, count, and type")
                                               .Register(*registry)) {
    for (auto &pair : scanner::ScannerWrapperOwner::get().get_scanners()) {
      metrics_.try_emplace(pair.first, family_, pair.first);
    }
    thread_ = std::thread(&MetricsHolder::loop, this);
  }

private:
  common::ThreadSafeQueue<DataPoint> &data_points_;
  prometheus::Family<prometheus::Gauge> &family_;
  std::unordered_map<uint32_t, P2PAccuracyScanner> metrics_;

  std::thread thread_;

  void loop() {
    while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
      auto start = std::chrono::system_clock::now();
      while (true) {
        auto optional_dp = data_points_.pop();
        if (!optional_dp) {
          break;
        }
        auto dp = optional_dp.value();
        metrics_.at(dp.scanner_id).add(dp);
      }
      std::this_thread::sleep_until(start + std::chrono::seconds(5));
    }
  }
};
P2PAccuracy &P2PAccuracy::get() {
  static P2PAccuracy pa;
  return pa;
}
P2PAccuracy::P2PAccuracy() {}
void P2PAccuracy::add(uint32_t scanner_id, float pan_offset, float tilt_offset, uint32_t num_p2p_matches) {
  data_points_.emplace_add(scanner_id, pan_offset, tilt_offset, num_p2p_matches);
}
void P2PAccuracy::start(std::shared_ptr<prometheus::Registry> registry) {
  if (!holder_) {
    holder_ = std::make_unique<MetricsHolder>(registry, data_points_);
  }
}

} // namespace carbon::metrics
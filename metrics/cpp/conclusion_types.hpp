#pragma once
#include <string>

namespace carbon::metrics {
enum class ConclusionType {
  kNotWeeding = 0,
  kOutOfBand = 1,
  kIntersectsWithNonShootable = 2,
  kOutOfRange = 3,
  kUnimportant = 4,
  kNotShot = 5,
  kPartiallyShot = 6,
  kShot = 7,
  kP2PNotFound = 8,
  kError = 9,
  kFlicker = 10,
  kMarkedForThinning = 11,
  kNotTargeted = 12,
  kP2PMissingContext = 13,
  kConclusionTypeNumber, // Always Last
};
enum class SchedulingFailureType {
  kAlreadyShot = 0,
  kDuplicate = 1,
  kNonShootable = 2,
  kUntracked = 3,
  kNonTargetable = 4,
  kSchedulingFailureTypeCount, // Always Last
};
std::string conclusion_type_to_string(ConclusionType type);
std::string scheduling_failure_to_string(SchedulingFailureType type);
inline bool conclusion_is_targetable(ConclusionType type) {
  switch (type) {
  case ConclusionType::kNotShot:
  case ConclusionType::kPartiallyShot:
  case ConclusionType::kShot:
  case ConclusionType::kP2PNotFound:
  case ConclusionType::kError:
  case ConclusionType::kMarkedForThinning:
  case ConclusionType::kP2PMissingContext:
    return true;
  default:
    return false;
  }
  return false;
}
} // namespace carbon::metrics
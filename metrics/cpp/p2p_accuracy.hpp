#pragma once
#include <memory>
#include <stdint.h>

#include <lib/common/cpp/utils/thread_safe_queue.hpp>

namespace prometheus {
class Registry;
}
namespace carbon::metrics {
class P2PAccuracy {
public:
  P2PAccuracy(const P2PAccuracy &) = delete;
  static P2PAccuracy &get();
  void add(uint32_t scanner_id, float pan_offset, float tilt_offset, uint32_t num_p2p_matches);
  void start(std::shared_ptr<prometheus::Registry> registry);

  struct DataPoint {
    uint32_t scanner_id;
    float pan_offset;
    float tilt_offset;
    uint32_t num_p2p_matches;
    DataPoint(uint32_t _scanner_id, float _pan_offset, float _tilt_offset, uint32_t _num_p2p_matches)
        : scanner_id(_scanner_id), pan_offset(_pan_offset), tilt_offset(_tilt_offset),
          num_p2p_matches(_num_p2p_matches) {}
    DataPoint(const DataPoint &rhs)
        : scanner_id(rhs.scanner_id), pan_offset(rhs.pan_offset), tilt_offset(rhs.tilt_offset),
          num_p2p_matches(rhs.num_p2p_matches) {}
  };

private:
  class MetricsHolder;
  P2PAccuracy();

  std::unique_ptr<MetricsHolder> holder_;
  common::ThreadSafeQueue<DataPoint> data_points_;
};
} // namespace carbon::metrics
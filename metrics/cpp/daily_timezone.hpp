#pragma once
#include <hardware_manager/cpp/grpc_client.h>
#include <lib/common/redis/redis_client.hpp>

#include <mutex>

namespace carbon::metrics {
class DailyTimezone {
public:
  DailyTimezone(bool owner = false);
  std::string get_day();
  inline lib::common::RedisClient &redis() { return redis_; }

private:
  bool owner_;
  hardware_manager::HardwareManagerClient hw_client_;
  lib::common::RedisClient redis_;
  std::string day_str_;
  std::chrono::system_clock::time_point expiration_;
  std::mutex mut_;
  void update();
  void save_tz(const std::string &tz);
};
} // namespace carbon::metrics
#include "metrics/cpp/spatial/spatial_block_owner.hpp"

#include <vector>

#include <spdlog/spdlog.h>
namespace carbon::metrics {
SpatialBlockOwner::SpatialBlockOwner() {}
SpatialBlockOwner::ClientID SpatialBlockOwner::register_client() {
  ClientID id = static_cast<ClientID>(
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count());
  const std::unique_lock lock(mut_);
  // Handle case of multiple requests in same ms
  while (last_ack_.find(id) != last_ack_.end()) {
    ++id;
  }
  last_beat_.emplace(id, std::chrono::system_clock::now());
  last_ack_.emplace(id, 0);
  return id;
}
bool SpatialBlockOwner::beat(ClientID id) {
  const std::unique_lock lock(mut_);
  auto it = last_beat_.find(id);
  if (it == last_beat_.end()) {
    return false;
  }
  it->second = std::chrono::system_clock::now();
  return true;
}
bool SpatialBlockOwner::ack(ClientID client_id, BlockID block_id) {
  const std::unique_lock lock(mut_);
  auto it = last_ack_.find(client_id);
  if (it == last_ack_.end()) {
    return false;
  }
  if (block_id > it->second) {
    it->second = block_id;
  }
  return true;
}
bool SpatialBlockOwner::can_close(BlockID block_id) {
  auto now = std::chrono::system_clock::now();
  bool acked = true;
  const std::unique_lock lock(mut_);

  std::vector<ClientID> to_rm;
  for (const auto &ack : last_ack_) {
    if (ack.second < block_id) {
      // this client has not acked this block yet check last heart beat and if still alive then not ready
      auto beat_it = last_beat_.find(ack.first);
      if (beat_it == last_beat_.end()) {
        spdlog::warn("Client {} does not exist in last beat", ack.first);
        to_rm.emplace_back(ack.first);
        continue;
      }
      if (std::chrono::duration_cast<std::chrono::seconds>(now - beat_it->second) > std::chrono::seconds(30)) {
        // This client is no longer alive
        spdlog::info("Client {} appears to have died, removing from blocker", ack.first);
        to_rm.emplace_back(ack.first);
      } else {
        // This client is alive but has not acked this block we cannot send close block yet
        acked = false;
      }
    }
  }
  for (auto client_id : to_rm) {
    last_ack_.erase(client_id);
    last_beat_.erase(client_id);
  }
  return acked;
}
} // namespace carbon::metrics
#pragma once

#include <generated/metrics/proto/metrics_aggregator_service.grpc.pb.h>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <mutex>
#include <string>

namespace carbon::metrics {
class MetricsClient {

public:
  MetricsClient();
  ~MetricsClient();

  void ping();
  uint32_t register_spatial_client();
  bool spatial_client_beat(uint32_t id);
  bool spatial_client_ack(uint32_t client_id, uint64_t block_id);
  uint64_t get_next_block(uint64_t block_id);

  void reset();

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> get_grpc_stub();
  void reset_stub();

  mutable std::mutex mut_;
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> stub_;
};

} // namespace carbon::metrics
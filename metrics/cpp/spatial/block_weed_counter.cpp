#include "metrics/cpp/spatial/block_weed_counter.hpp"

#include <lib/common/cpp/utils/environment.hpp>
#include <lib/common/redis/redis_client.hpp>

#include <generated/proto/metrics/metrics.pb.h>

#include <spdlog/spdlog.h>

auto constexpr spatial_weed_blocks_key = "/spatial/weed_blocks";
namespace carbon::metrics {
size_t bools_to_index(bool armed, bool is_weed) { return (size_t)armed + ((size_t)!is_weed << 1); }

BlockWeedCounter::BlockWeedCounter(uint64_t block_id, lib::common::RedisClient *redis)
    : block_id_(block_id), redis_(redis), completed_(false), counts_(4), total_weeds_size_mm_(0.0),
      total_crops_size_mm_(0.0), total_weed_count_(0), total_crop_count_(0), total_targetable_req_laser_time_ms_(0),
      total_untargetable_req_laser_time_ms_(0), total_targetable_count_(0), total_untargetable_count_(0),
      total_valid_crop_count_(0) {
  for (auto &cnt : counts_) {
    cnt.resize(static_cast<size_t>(ConclusionType::kConclusionTypeNumber), 0);
  }
}
void BlockWeedCounter::increment(ConclusionType type, bool armed, bool is_weed, float size_mm,
                                 const std::string &type_name, uint32_t required_laser_time, bool valid,
                                 bool is_marked_for_thinning) {
  std::unique_lock lk(mut_);
  if (completed_) {
    spdlog::warn("Trying to add data to block marked as complete.");
    return;
  }
  ++counts_[bools_to_index(armed, is_weed)][static_cast<size_t>(type)];
  if (type == ConclusionType::kFlicker) {
    // don't count flicker in any other metric
    return;
  }
  if (is_weed) {
    total_weeds_size_mm_ += size_mm;
    ++total_weed_count_;
    auto iter = count_by_type_.find(type_name);
    if (iter == count_by_type_.end()) {
      count_by_type_.emplace(type_name, 1u);
    } else {
      ++(iter->second);
    }
  } else {
    total_crops_size_mm_ += size_mm;
    ++total_crop_count_;
    if (valid) {
      ++total_valid_crop_count_;
    }
  }
  if (conclusion_is_targetable(type)) {
    // only count crops that are marked for thinning as targetable
    // count all weeds as targetable
    if (is_weed || is_marked_for_thinning) {
      total_targetable_req_laser_time_ms_ += required_laser_time;
      ++total_targetable_count_;
    }
  } else {
    total_untargetable_req_laser_time_ms_ += required_laser_time;
    ++total_untargetable_count_;
  }
}
void BlockWeedCounter::save() {
  {
    std::unique_lock lk(mut_);
    if (completed_) {
      spdlog::warn("Trying to re-save already saved block");
      return;
    }
    completed_ = true;
  }
  WeedCounterChunk chunk;
  for (auto count : counts_[0]) {
    chunk.mutable_conclusion_counts()->add_disarmed_weed(count);
  }
  for (auto count : counts_[1]) {
    chunk.mutable_conclusion_counts()->add_armed_weed(count);
  }
  for (auto count : counts_[2]) {
    chunk.mutable_conclusion_counts()->add_disarmed_crop(count);
  }
  for (auto count : counts_[3]) {
    chunk.mutable_conclusion_counts()->add_armed_crop(count);
  }
  chunk.mutable_weed_size_data()->set_cumulative_size(total_weeds_size_mm_);
  chunk.mutable_weed_size_data()->set_count(total_weed_count_);
  chunk.mutable_crop_size_data()->set_cumulative_size(total_crops_size_mm_);
  chunk.mutable_crop_size_data()->set_count(total_crop_count_);
  chunk.mutable_targeted_laser_time_data()->set_cumulative_time(total_targetable_req_laser_time_ms_);
  chunk.mutable_targeted_laser_time_data()->set_count(total_targetable_count_);
  chunk.mutable_untargeted_laser_time_data()->set_cumulative_time(total_untargetable_req_laser_time_ms_);
  chunk.mutable_untargeted_laser_time_data()->set_count(total_untargetable_count_);
  for (const auto &pair : count_by_type_) {
    (*chunk.mutable_counts_by_category())[pair.first] = pair.second;
  }
  chunk.set_valid_crop_count(total_valid_crop_count_);
  auto str_data = chunk.SerializeAsString();
  try {
    redis_->hset(spatial_weed_blocks_key, fmt::format("{}/{}", block_id_, common::get_row()), str_data);
  } catch (std::exception &ex) {
    spdlog::warn("Failed to write block {} with err: {}", block_id_, ex.what());
  }
}
} // namespace carbon::metrics
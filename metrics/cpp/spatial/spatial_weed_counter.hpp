#pragma once
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>

#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <lib/common/redis/redis_client.hpp>
#include <metrics/cpp/conclusion_types.hpp>
#include <metrics/cpp/spatial/block_weed_counter.hpp>
#include <metrics/cpp/spatial/metrics_client.hpp>

namespace carbon::metrics {
class SpatialWeedCounter {
public:
  class BlockIdentifier {
  public:
    BlockIdentifier(uint64_t id);
    ~BlockIdentifier();
    inline uint64_t id() const { return id_; }
    friend class SpatialWeedCounter;

  private:
    uint64_t id_;
    std::shared_ptr<BlockIdentifier> next_; // force future data alive until after this block is written
  };
  SpatialWeedCounter(const SpatialWeedCounter &) = delete;
  ~SpatialWeedCounter();
  static SpatialWeedCounter &get();
  void increment(std::shared_ptr<BlockIdentifier> block, ConclusionType type, bool armed, bool is_weed, float size_mm,
                 const std::string &type_name, uint32_t required_laser_time, bool valid, bool is_marked_for_thinning);
  std::shared_ptr<BlockIdentifier> get_current() const;

private:
  SpatialWeedCounter();
  uint32_t reset_client_id(bool reset_block);
  void mark_for_writting(uint64_t id);

  void heartbeat_loop();
  void block_id_loop();
  void writter_loop();

  mutable std::mutex mut_;
  mutable std::mutex client_mut_;
  lib::common::RedisClient redis_;
  MetricsClient client_;
  uint32_t client_id_;

  std::shared_ptr<BlockIdentifier> current_block_;
  carbon::common::ThreadSafeQueue<uint64_t> completed_blocks_;
  std::unordered_map<uint64_t, BlockWeedCounter> counters_;

  std::thread heartbeat_thread_;
  std::thread block_id_thread_;
  std::thread writter_thread_;
};
} // namespace carbon::metrics
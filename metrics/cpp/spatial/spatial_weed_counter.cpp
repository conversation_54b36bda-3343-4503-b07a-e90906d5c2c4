#include "metrics/cpp/spatial/spatial_weed_counter.hpp"
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>

#include <chrono>

#include <spdlog/spdlog.h>

namespace carbon::metrics {
SpatialWeedCounter::BlockIdentifier::BlockIdentifier(uint64_t id) : id_(id), next_(nullptr) {}
SpatialWeedCounter::BlockIdentifier::~BlockIdentifier() { SpatialWeedCounter::get().mark_for_writting(id_); }

SpatialWeedCounter::SpatialWeedCounter()
    : client_id_(0), current_block_(nullptr), heartbeat_thread_(&SpatialWeedCounter::heartbeat_loop, this),
      block_id_thread_(&SpatialWeedCounter::block_id_loop, this),
      writter_thread_(&SpatialWeedCounter::writter_loop, this) {}
SpatialWeedCounter::~SpatialWeedCounter() {
  heartbeat_thread_.join();
  block_id_thread_.join();
  writter_thread_.join();
}

SpatialWeedCounter &SpatialWeedCounter::get() {
  static SpatialWeedCounter swc;
  return swc;
}
void SpatialWeedCounter::increment(std::shared_ptr<BlockIdentifier> block, ConclusionType type, bool armed,
                                   bool is_weed, float size_mm, const std::string &type_name,
                                   uint32_t required_laser_time, bool valid, bool is_marked_for_thinning) {
  if (!block) {
    return;
  }
  if (is_weed && !valid) {
    return;
  }
  std::unique_lock lk(mut_);
  auto it = counters_.find(block->id());
  if (it == counters_.end()) {
    spdlog::warn("Failed to find counter for block {}", block->id());
    return;
  }
  it->second.increment(type, armed, is_weed, size_mm, type_name, required_laser_time, valid, is_marked_for_thinning);
}
std::shared_ptr<SpatialWeedCounter::BlockIdentifier> SpatialWeedCounter::get_current() const {
  std::unique_lock lk(mut_);
  return current_block_;
}

uint32_t SpatialWeedCounter::reset_client_id(bool reset_block) {
  std::unique_lock lk(client_mut_);
  if (client_id_ == 0) {
    client_id_ = client_.register_spatial_client();
    if (reset_block) {
      std::unique_lock blk_lk(mut_);
      current_block_ = nullptr; // had to reset client so now we need to start blocks back over
    }
  }
  return client_id_;
}

void SpatialWeedCounter::mark_for_writting(uint64_t id) { completed_blocks_.emplace_add(id); }

void SpatialWeedCounter::heartbeat_loop() {
  uint32_t id = reset_client_id(false); // dont reset on initialization only
  while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
    if (!client_.spatial_client_beat(id)) {
      std::unique_lock lk(client_mut_);
      client_id_ = 0;
    }
    id = reset_client_id(true);
    lib::common::bot::BotStopHandler::get().sleep_safe(10000);
  }
}
void SpatialWeedCounter::block_id_loop() {
  while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
    uint64_t cur_block_id(0);
    {
      std::unique_lock lk(mut_);
      if (current_block_) {
        cur_block_id = current_block_->id();
      } else {
        cur_block_id = 0;
      }
    }
    uint64_t new_block = client_.get_next_block(cur_block_id); // This is a long poll blocking call
    if (new_block > cur_block_id) {
      std::unique_lock lk(mut_);
      auto prev_block = current_block_;
      current_block_ = std::make_shared<BlockIdentifier>(new_block);
      if (prev_block) {
        prev_block->next_ =
            current_block_; // this will keep new current block alive at least until prev block no matter what
      }
      counters_.try_emplace(new_block, new_block, &redis_);
    }
  }
}
void SpatialWeedCounter::writter_loop() {
  auto bse(lib::common::bot::BotStopHandler::get().create_scoped_event("spatial_writter",
                                                                       [&]() { this->completed_blocks_.terminate(); }));
  while (!bse.is_stopped()) {
    auto optional_block_id = completed_blocks_.wait_pop(0);
    if (!optional_block_id) {
      continue;
    }
    auto id = optional_block_id.value();
    {
      std::unique_lock lk(mut_);
      auto it = counters_.find(id);
      if (it == counters_.end()) {
        spdlog::warn("Request to write block {} failed as could not find block", id);
        continue;
      }
      it->second.save();
      counters_.erase(it);
    }
    uint32_t client_id = reset_client_id(true);
    {
      std::unique_lock lk(mut_);
      if (!current_block_ || id >= current_block_->id()) {
        spdlog::warn("Block id was reset most likely due to service restart, ignoring old blocks.");
        continue;
      }
    }
    client_.spatial_client_ack(client_id, id);
  }
}
} // namespace carbon::metrics
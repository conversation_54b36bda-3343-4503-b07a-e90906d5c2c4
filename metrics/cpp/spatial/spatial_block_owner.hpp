#pragma once
#include <atomic>
#include <chrono>
#include <mutex>
#include <stdint.h>
#include <unordered_map>

namespace carbon::metrics {
class SpatialBlockOwner {
public:
  using ClientID = uint32_t;
  using BlockID = uint64_t;
  SpatialBlockOwner();
  ClientID register_client();
  bool beat(ClientID id);
  bool ack(ClientID client_id, BlockID block_id);
  bool can_close(BlockID block_id);

private:
  mutable std::mutex mut_;
  std::unordered_map<ClientID, std::chrono::system_clock::time_point> last_beat_;
  std::unordered_map<ClientID, BlockID> last_ack_;
};
} // namespace carbon::metrics
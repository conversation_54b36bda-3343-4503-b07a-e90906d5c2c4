#pragma once
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

#include <metrics/cpp/conclusion_types.hpp>

namespace lib::common {
class RedisClient;
}
namespace carbon::metrics {
class BlockWeedCounter {
public:
  BlockWeedCounter(uint64_t block_id, lib::common::RedisClient *redis);
  void increment(ConclusionType type, bool armed, bool is_weed, float size_mm, const std::string &type_name,
                 uint32_t required_laser_time, bool valid, bool is_marked_for_thinning);
  void save();

private:
  mutable std::mutex mut_;
  uint64_t block_id_;
  lib::common::RedisClient *redis_;
  bool completed_;

  std::vector<std::vector<uint32_t>> counts_;
  double total_weeds_size_mm_;
  double total_crops_size_mm_;
  uint64_t total_weed_count_;
  uint64_t total_crop_count_;
  uint64_t total_targetable_req_laser_time_ms_;
  uint64_t total_untargetable_req_laser_time_ms_;
  uint64_t total_targetable_count_;
  uint64_t total_untargetable_count_;
  uint64_t total_valid_crop_count_;
  std::unordered_map<std::string, uint32_t> count_by_type_;
};
} // namespace carbon::metrics
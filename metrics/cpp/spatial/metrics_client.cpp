#include "metrics/cpp/spatial/metrics_client.hpp"

#include <lib/common/cpp/utils/environment.hpp>

#include <functional>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon::metrics {

MetricsClient::MetricsClient() : addr_(fmt::format("{}:61010", common::get_command_ip())), channel_(nullptr) {}

MetricsClient::~MetricsClient() {}

std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> MetricsClient::get_grpc_stub() {
  const std::unique_lock lock(mut_);
  if (this->channel_ == nullptr) {
    this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<::metrics_aggregator::MetricsAggregatorService::Stub>(this->channel_);
  }
  return this->stub_;
}

void MetricsClient::reset() { return this->reset_stub(); }

void MetricsClient::reset_stub() {
  const std::unique_lock lock(mut_);
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status MetricsClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) {
    this->reset_stub();
    throw ex;
  }
}

void MetricsClient::ping() {
  grpc::ClientContext context;
  ::metrics_aggregator::PingRequest req;
  ::metrics_aggregator::PingResponse resp;
  req.set_x(42);
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(
      std::bind(&::metrics_aggregator::MetricsAggregatorService::Stub::Ping, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::warn("ping failed with error {}", status.error_code());
  }
}
uint32_t MetricsClient::register_spatial_client() {
  grpc::ClientContext context;
  ::metrics_aggregator::RegisterSpatialClientRequest req;
  ::metrics_aggregator::RegisterSpatialClientResponse resp;
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(
      &::metrics_aggregator::MetricsAggregatorService::Stub::RegisterSpatialClient, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::warn("register failed with error {}", status.error_code());
    return 0;
  }
  return resp.id();
}
bool MetricsClient::spatial_client_beat(uint32_t id) {
  grpc::ClientContext context;
  ::metrics_aggregator::SpatialClientBeatRequest req;
  ::metrics_aggregator::SpatialClientBeatResponse resp;
  req.set_id(id);
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(
      std::bind(&::metrics_aggregator::MetricsAggregatorService::Stub::SpatialClientBeat, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::warn("beat failed with error {}", status.error_code());
    return false;
  }
  return resp.success();
}
bool MetricsClient::spatial_client_ack(uint32_t client_id, uint64_t block_id) {
  grpc::ClientContext context;
  ::metrics_aggregator::SpatialClientAckRequest req;
  ::metrics_aggregator::SpatialClientAckResponse resp;
  req.set_client_id(client_id);
  req.set_block_id(block_id);
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(
      std::bind(&::metrics_aggregator::MetricsAggregatorService::Stub::SpatialClientAck, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::warn("ack failed with error {}", status.error_code());
    return false;
  }
  return resp.success();
}
uint64_t MetricsClient::get_next_block(uint64_t block_id) {
  grpc::ClientContext context;
  ::metrics_aggregator::GetNextSpatialBlockRequest req;
  ::metrics_aggregator::GetNextSpatialBlockResponse resp;
  req.set_block_id(block_id);
  std::shared_ptr<::metrics_aggregator::MetricsAggregatorService::Stub> stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(
      &::metrics_aggregator::MetricsAggregatorService::Stub::GetNextSpatialBlock, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    return block_id;
  }
  return resp.block_id();
}

} // namespace carbon::metrics
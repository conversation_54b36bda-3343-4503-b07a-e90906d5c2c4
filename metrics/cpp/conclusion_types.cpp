#include "metrics/cpp/conclusion_types.hpp"
namespace carbon::metrics {
std::string conclusion_type_to_string(ConclusionType type) {
  switch (type) {
  case ConclusionType::kNotWeeding:
    return "Not Weeding";
  case ConclusionType::kOutOfBand:
    return "Out of Band";
  case ConclusionType::kIntersectsWithNonShootable:
    return "Intersect With Non Shootable";
  case ConclusionType::kOutOfRange:
    return "Out of Range";
  case ConclusionType::kUnimportant:
    return "Unimportant";
  case ConclusionType::kNotShot:
    return "Not Shot";
  case ConclusionType::kPartiallyShot:
    return "Partially Shot";
  case ConclusionType::kShot:
    return "Shot";
  case ConclusionType::kP2PNotFound:
    return "P2P Not Found";
  case ConclusionType::kError:
    return "Error";
  case ConclusionType::kFlicker:
    return "Flicker";
  case ConclusionType::kMarkedForThinning:
    return "Marked for thinning";
  case ConclusionType::kNotTargeted:
    return "Intentionally not shot";
  case ConclusionType::kP2PMissingContext:
    return "Missing perspective for p2p context";
  default:
    return "Error";
  }
}
std::string scheduling_failure_to_string(SchedulingFailureType type) {
  switch (type) {
  case SchedulingFailureType::kAlreadyShot:
    return "Already Shot";
  case SchedulingFailureType::kDuplicate:
    return "Duplicate";
  case SchedulingFailureType::kNonShootable:
    return "Intersects with Non-shootable";
  case SchedulingFailureType::kUntracked:
    return "Removed from tracker";
  case SchedulingFailureType::kNonTargetable:
    return "No longer targetable";
  default:
    return "Unknown";
  }
}
} // namespace carbon::metrics
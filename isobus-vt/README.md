# Isobus Virtual Terminal Python Interface

The libvt.so should be build from the libvt repo. This is only the top level python and layout files for loading
our virtual terminal application.

For development, the easiest way to run is like this:

```LD_LIBRARY_PATH=../../libvt/ PYTHONPATH=../../libvt/ ./pyrunner.py```

We need to tell the shell where the libvt.so and the python import interface lives.


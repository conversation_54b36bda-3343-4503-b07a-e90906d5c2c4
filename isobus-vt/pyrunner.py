#!/usr/bin/env python

import asyncio
import re
from datetime import datetime as dt

import python.vt as vt

logfile_name = "isobus.log"
log_level = vt.LOG_LEVEL.INFO
loglines = 20


class RowModule:
    def __init__(self, name: str):
        self.name = name
        self.off()

    def off(self) -> None:
        self.state = "DISABLED"
        self.directions = "CLICK TO ENABLE"
        self.font = "buttonred"

    def switch_state(self) -> None:
        if self.state == "DISABLED":
            self.state = "ENABLED"
            self.directions = "CLICK TO DISABLE"
            self.font = "buttonblack"
        else:
            self.off()


class Laser:
    def __init__(self) -> None:
        self.off()

    def off(self) -> None:
        self.state = "DISABLED"
        self.directions = "CLICK TO ENABLE"
        self.bgcolor = "d1d1d1"

    def switch_state(self) -> None:
        if self.state == "DISABLED":
            self.state = "ENABLED"
            self.directions = "CLICK TO DISABLE"
            self.bgcolor = "be2c1c"
        else:
            self.off()


rows = [RowModule("row1"), RowModule("row2"), RowModule("row3")]
laser = Laser()


def button_state_str(code: int) -> str:
    for e in vt.ButtonState.__members__.values():
        if e.value == code:
            return str(e.name)

    return "UNKNOWN"


async def write_laser(name: str, laser: Laser) -> None:
    await vt.set_string_value(name + "/status", laser.state)
    await vt.set_string_value(name + "/directions", laser.directions)
    await vt.set_button_bgcolor(name, laser.bgcolor)


async def write_row(name: str, rm: RowModule) -> None:
    await vt.set_string_value(name + "/status", rm.state)
    await vt.set_string_font(name + "/status", rm.font)
    await vt.set_string_value(name + "/directions", rm.directions)


async def sync_state() -> None:
    global rows
    await write_laser("main/laser_state", laser)
    for rm in rows:
        await write_row(f"main/{rm.name}_enable", rm)


async def button_cb(name: str, code: int) -> int:

    if code != vt.ButtonState.PRESSED.value:
        return 0

    if name == "maintenance":
        await vt.set_active_mask("maintenance")
    elif name == "main":
        await vt.set_active_mask("main")
    elif name == "alarms":
        await vt.set_active_mask("alarms_alarms")
    elif name == "alarms_alarms":
        await vt.set_active_mask("alarms_alarms")
    elif name == "alarms_logs":
        await vt.set_active_mask("alarms_logs")
    elif name == "checklist":
        await vt.set_active_mask("checklist")
    elif name == "screenswitch":
        await vt.switch_screen()
        await sync_state()
    elif name == "main/laser_state":
        laser.switch_state()
        await write_laser(name, laser)
    elif name == "main/estop":
        laser.off()
        for rm in rows:
            rm.off()
        await sync_state()
    else:
        m = re.match("main/row([0-9])_enable", name)
        if m is not None and m.group(0):
            row = int(m.group(1))
            rm = rows[row - 1]
            rm.switch_state()
            await write_row(name, rm)
    return 0


async def inputlist_cb(name: str, val: str) -> int:
    if name == "main/crop_select":
        print(f"CROP: {val}")
    return 0


async def speed_cb(speed: float) -> int:
    target_speed = 1.1 * speed
    await vt.set_string_value("main/current_speed", "%.2f MPH" % speed)
    await vt.set_string_value("main/target_speed", "%.2f MPH" % target_speed)
    speed_val = (speed / target_speed) * 50
    if speed_val > 100:
        speed_val = 100
    await vt.set_meter_value("main/speed_meter", int(speed_val))
    return 0


async def hitch_cb(position: int) -> int:
    await vt.set_string_value("main/hitch_position", "%d %%" % position)
    return 0


async def set_fake_cell() -> None:
    # fill up to 10 entries
    for i in range(10):
        await vt.append_table_row(
            "alarms_alarms/alarm_alarms", [str(dt.now()), "COL1", "COL2", "Here is my message", "X"]
        )
        await asyncio.sleep(1)

    # every second append a new one and remove the first one
    while True:
        await asyncio.sleep(10)
        await vt.append_table_row(
            "alarms_alarms/alarm_alarms", [str(dt.now()), "COL1", "COL2", "Here is my message", "X"]
        )
        await vt.delete_table_row("alarms_alarms/alarm_alarms", 0)


async def output_log_file() -> None:
    global loglines
    try:
        f = open(logfile_name)
    except Exception:
        return
    lines = [l for l in f][-loglines:]
    # only care about the last 'lines' lines
    for l in lines:
        await vt.append_table_row("alarms_logs/alarm_logs", [str(l)])


async def main() -> None:
    global logfile_name
    global log_level
    vt.set_event_loop(asyncio.get_running_loop())
    vt.set_inputlist_dispatch(inputlist_cb)
    vt.set_button_dispatch(button_cb)
    vt.set_speed_dispatch(speed_cb)
    vt.set_hitch_dispatch(hitch_cb)
    # vt.set_preferred_vt(38) // example usage
    await vt.start_isobus(0, log_level, logfile_name)
    await output_log_file()
    await set_fake_cell()
    while True:
        await asyncio.sleep(999999)


if __name__ == "__main__":
    asyncio.run(main())

from dataclasses import dataclass, field
from enum import Enum, IntEnum
from typing import Any, Dict, List, Optional

from dataclass_wizard import JSON<PERSON>izard

from deck.config import FullyDefinedView, Metadata, OutputConfig, ViewConfig, Window
from lib.common.messaging.message import Empty, empty_sender_builder, msg_sender_builder
from lib.common.serialization.rectangle import Rectangle
from lib.drivers.deck_cam.config import <PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>, CameraType
from lib.rtc.messaging.message import Message


class MessageType(str, Enum):
    MEASURE_REQ = "MEASURE_REQUEST"
    MEASURE_RESP = "MEASURE_RESPONSE"
    READY_REQ = "READY_REQUEST"
    READY_RESP = "READY_RESPONSE"

    LIST_CAMS_REQ = "LIST_CAMERAS_REQUEST"
    LIST_CAMS_RESP = "LIST_CAMERAS_RESPONSE"
    LIST_VIEWS_REQ = "LIST_VIEWS_REQUEST"
    LIST_VIEWS_RESP = "LIST_VIEWS_RESPONSE"
    GET_VIEW_REQ = "GET_VIEW_REQUEST"
    GET_VIEW_RESP = "GET_VIEW_RESPONSE"
    GET_WIN_REQ = "GET_WINDOW_REQUEST"
    GET_WIN_RESP = "GET_WINDOW_RESPONSE"
    LIST_WIN_FOR_CAM_REQ = "LIST_WINDOWS_FOR_CAMERA_REQUEST"
    LIST_WIN_FOR_CAM_RESP = "LIST_WINDOWS_FOR_CAMERA_RESPONSE"
    TAG_VIEW_REQ = "TAG_VIEW_REQUEST"  # TODO Delete once FE moves to SET_VIEW_TAGS_REQ
    TAG_VIEW_RESP = "TAG_VIEW_RESPONSE"
    GET_VIEW_FOR_TAG_REQ = "GET_VIEW_FOR_TAG_REQUEST"
    GET_VIEW_FOR_TAG_RESP = "GET_VIEW_FOR_TAG_RESPONSE"
    STORE_VIEW_REQ = "STORE_VIEW_REQUEST"
    STORE_VIEW_RESP = "STORE_VIEW_RESPONSE"
    STORE_WIN_REQ = "STORE_WINDOW_REQUEST"
    STORE_WIN_RESP = "STORE_WINDOW_RESPONSE"
    SET_VIEW_ACTIVE_REQ = "SET_VIEW_ACTIVE_REQUEST"
    SET_VIEW_ACTIVE_RESP = "SET_VIEW_ACTIVE_RESPONSE"
    GET_DEF_VIEW_REQ = "GET_DEFAULT_VIEW_REQUEST"
    GET_DEF_VIEW_RESP = "GET_DEFAULT_VIEW_RESPONSE"
    RESET_VIEW_REQ = "RESET_VIEW_REQUEST"
    RESET_VIEW_RESP = "RESET_VIEW_RESPONSE"
    DEL_VIEW_REQ = "DELETE_VIEW_REQUEST"
    DEL_VIEW_RESP = "DELETE_VIEW_RESPONSE"
    RESET_WIN_REQ = "RESET_WINDOW_REQUEST"
    RESET_WIN_RESP = "RESET_WINDOW_RESPONSE"
    DEL_WIN_REQ = "DELETE_WINDOWS_REQUEST"
    DEL_WIN_RESP = "DELETE_WINDOWS_RESPONSE"
    LIST_WIN_REQ = "LIST_WINDOWS_REQUEST"
    LIST_WIN_RESP = "LIST_WINDOWS_RESPONSE"
    LIST_TAG_REQ = "LIST_TAGS_REQUEST"
    LIST_TAG_RESP = "LIST_TAGS_RESPONSE"
    SET_CFG_REQ = "SET_CONFIG_REQUEST"
    SET_CFG_RESP = "SET_CONFIG_RESPONSE"
    GET_CFG_REQ = "GET_CONFIG_REQUEST"
    GET_CFG_RESP = "GET_CONFIG_RESPONSE"
    GET_VIEWPORT_DATA_REQ = "GET_VIEWPORT_DATA_REQUEST"
    GET_VIEWPORT_DATA_RESP = "GET_VIEWPORT_DATA_RESPONSE"
    LIST_CFG_REQ = "LIST_CONFIG_REQUEST"
    LIST_CFG_RESP = "LIST_CONFIG_RESPONSE"
    SET_VIEW_TAGS_REQ = "SET_VIEW_TAGS_REQUEST"
    SET_VIEW_TAGS_RESP = "SET_VIEW_TAGS_RESPONSE"
    SET_WHEEL_POS_REQ = "SET_WHEEL_POS_REQUEST"
    SET_WHEEL_POS_RESP = "SET_WHEEL_POS_RESPONSE"


class StatusCodes(IntEnum):
    INVALID_TRANSFORMS = 1001
    FORBIDDEN = 403


@dataclass
class MeasureRequest(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    color: str


@dataclass
class MeasureResponse(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    timestamp: int
    color: str


@dataclass
class StatusResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    status: bool
    msg: Optional[str] = None
    code: Optional[int] = None


@dataclass
class MultiStatusResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    statuses: Dict[str, StatusResp]


@dataclass
class CamDetails(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    width: int
    height: int
    cam_type: CameraType
    wheel_pos: Optional[Rectangle]


@dataclass
class ListCamResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    cameras: Dict[str, CamDetails] = field(default_factory=dict)


@dataclass
class ListViewsResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    views: Dict[str, Metadata] = field(default_factory=dict)
    active: str = ""


@dataclass
class IDMsg(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    id: str


@dataclass
class MultiID(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    ids: List[str] = field(default_factory=list)


@dataclass
class GetWinResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    window: Optional[Window] = None


@dataclass
class ListWinCamResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    windows: Dict[str, Metadata] = field(default_factory=dict)


@dataclass
class TagViewReq(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    id: str
    tag: str


@dataclass
class SetViewTags(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    view_id: str
    tags: List[str] = field(default_factory=list)


@dataclass
class StoreViewReq(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    view: ViewConfig
    save: bool


@dataclass
class StoreWinReq(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    window: Window
    save: bool


@dataclass
class ListWinResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    windows: List[Window] = field(default_factory=list)


@dataclass
class SetCfgVal(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    camera: str
    key: str
    val: Any


@dataclass
class GetCfgReq(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    camera: str
    key: str


@dataclass
class GetCfgResp(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    val: Optional[Any]


@dataclass
class CamConfigData(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    config: Dict[str, CamConfigNode] = field(default_factory=dict)


@dataclass
class WheelPosMsg(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    camera: str
    pos: Rectangle


REQ_RESP_MAP: Dict[str, str] = {
    MessageType.MEASURE_REQ: MessageType.MEASURE_RESP,
    MessageType.READY_REQ: MessageType.READY_RESP,
    MessageType.LIST_CAMS_REQ: MessageType.LIST_CAMS_RESP,
    MessageType.LIST_VIEWS_REQ: MessageType.LIST_VIEWS_RESP,
    MessageType.GET_VIEW_REQ: MessageType.GET_VIEW_RESP,
    MessageType.GET_WIN_REQ: MessageType.GET_WIN_RESP,
    MessageType.LIST_WIN_FOR_CAM_REQ: MessageType.LIST_WIN_FOR_CAM_RESP,
    MessageType.TAG_VIEW_REQ: MessageType.TAG_VIEW_RESP,
    MessageType.GET_VIEW_FOR_TAG_REQ: MessageType.GET_VIEW_FOR_TAG_RESP,
    MessageType.STORE_VIEW_REQ: MessageType.STORE_VIEW_RESP,
    MessageType.STORE_WIN_REQ: MessageType.STORE_WIN_RESP,
    MessageType.SET_VIEW_ACTIVE_REQ: MessageType.SET_VIEW_ACTIVE_RESP,
    MessageType.GET_DEF_VIEW_REQ: MessageType.GET_DEF_VIEW_RESP,
    MessageType.RESET_VIEW_REQ: MessageType.RESET_VIEW_RESP,
    MessageType.DEL_VIEW_REQ: MessageType.DEL_VIEW_RESP,
    MessageType.RESET_WIN_REQ: MessageType.RESET_WIN_RESP,
    MessageType.DEL_WIN_REQ: MessageType.DEL_WIN_RESP,
    MessageType.LIST_WIN_REQ: MessageType.LIST_WIN_RESP,
    MessageType.LIST_TAG_REQ: MessageType.LIST_TAG_RESP,
    MessageType.SET_CFG_REQ: MessageType.SET_CFG_RESP,
    MessageType.GET_CFG_REQ: MessageType.GET_CFG_RESP,
    MessageType.GET_VIEWPORT_DATA_REQ: MessageType.GET_VIEWPORT_DATA_RESP,
    MessageType.LIST_CFG_REQ: MessageType.LIST_CFG_RESP,
    MessageType.SET_VIEW_TAGS_REQ: MessageType.SET_VIEW_TAGS_RESP,
    MessageType.SET_WHEEL_POS_REQ: MessageType.SET_WHEEL_POS_RESP,
}
REQ_MAP: Dict[str, JSONWizard] = {
    MessageType.MEASURE_REQ: MeasureRequest,
    MessageType.GET_VIEW_REQ: IDMsg,
    MessageType.GET_WIN_REQ: IDMsg,
    MessageType.LIST_WIN_FOR_CAM_REQ: IDMsg,
    MessageType.TAG_VIEW_REQ: TagViewReq,
    MessageType.GET_VIEW_FOR_TAG_REQ: IDMsg,
    MessageType.STORE_VIEW_REQ: StoreViewReq,
    MessageType.STORE_WIN_REQ: StoreWinReq,
    MessageType.SET_VIEW_ACTIVE_REQ: IDMsg,
    MessageType.GET_DEF_VIEW_REQ: IDMsg,
    MessageType.RESET_VIEW_REQ: IDMsg,
    MessageType.DEL_VIEW_REQ: IDMsg,
    MessageType.RESET_WIN_REQ: IDMsg,
    MessageType.DEL_WIN_REQ: MultiID,
    MessageType.SET_CFG_REQ: SetCfgVal,
    MessageType.GET_CFG_REQ: GetCfgReq,
    MessageType.LIST_CFG_REQ: IDMsg,
    MessageType.SET_VIEW_TAGS_REQ: SetViewTags,
    MessageType.SET_WHEEL_POS_REQ: WheelPosMsg,
}

RESP_MAP: Dict[str, JSONWizard] = {
    MessageType.MEASURE_RESP: MeasureResponse,
    MessageType.READY_RESP: Empty,
    MessageType.LIST_CAMS_RESP: ListCamResp,
    MessageType.LIST_VIEWS_RESP: ListViewsResp,
    MessageType.GET_VIEW_RESP: FullyDefinedView,
    MessageType.GET_WIN_RESP: GetWinResp,
    MessageType.LIST_WIN_FOR_CAM_RESP: ListWinCamResp,
    MessageType.TAG_VIEW_RESP: Empty,
    MessageType.GET_VIEW_FOR_TAG_RESP: FullyDefinedView,
    MessageType.STORE_VIEW_RESP: IDMsg,
    MessageType.STORE_WIN_RESP: IDMsg,
    MessageType.SET_VIEW_ACTIVE_RESP: Empty,
    MessageType.GET_DEF_VIEW_RESP: FullyDefinedView,
    MessageType.RESET_VIEW_RESP: Empty,
    MessageType.DEL_VIEW_RESP: Empty,
    MessageType.RESET_WIN_RESP: Empty,
    MessageType.DEL_WIN_RESP: MultiStatusResp,
    MessageType.LIST_WIN_RESP: ListWinResp,
    MessageType.LIST_TAG_RESP: MultiID,
    MessageType.SET_CFG_RESP: Empty,
    MessageType.GET_CFG_RESP: GetCfgResp,
    MessageType.GET_VIEWPORT_DATA_RESP: OutputConfig,
    MessageType.LIST_CFG_RESP: CamConfigData,
    MessageType.SET_VIEW_TAGS_RESP: Empty,
    MessageType.SET_WHEEL_POS_RESP: Empty,
}

empty_sender = empty_sender_builder(Message, REQ_RESP_MAP)
msg_sender = msg_sender_builder(Message, REQ_RESP_MAP)

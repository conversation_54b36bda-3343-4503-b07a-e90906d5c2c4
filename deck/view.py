import asyncio
import logging
from functools import partial
from types import TracebackType
from typing import Dict, List, Optional, Tuple, Type

from deck.config import ViewConfig, Window, WindowConfig
from lib.drivers.deck_cam.pybind.deck_cam_python import Camera
from lib.rtc.deck_transform.pybind.deck_transform_python import CompositeView
from lib.rtc.deck_transform.transforms import get_pipeline

LOG = logging.getLogger(__name__)
OutputShape = Tuple[int, int]


def get_roi(output_shape: OutputShape, pipeline_shape: OutputShape, config: WindowConfig) -> Tuple[int, int, int, int]:
    return (
        int(round(config.x * output_shape[0])),
        int(round(config.y * output_shape[1])),
        pipeline_shape[0],
        pipeline_shape[1],
    )


def get_mask(window_shape: OutputShape, config: Window) -> List[Tuple[int, int]]:
    mask = []
    for pt in config.mask_polygon:
        mask.append((int(round(pt[0] * window_shape[0])), int(round(pt[1] * window_shape[1]))))
    return mask


class View:
    def __init__(
        self,
        fd: int,
        config: ViewConfig,
        shape: OutputShape,
        cameras: Dict[str, Camera],
        window_desc: Dict[str, Window],
    ) -> None:
        self.config = config
        cam_names = set(window.camera for window in window_desc.values()) & set(
            cameras.keys()
        )  # both available and needed

        self.cameras = {cam_name: cameras[cam_name] for cam_name in cam_names}
        # This is ordered by the view config to maintain z height
        self._unavailable = set()
        self._roi_finder = {}
        self._comp_view = CompositeView(fd, shape[0], shape[1])
        for win_cfg in config.windows:
            win_desc = window_desc[win_cfg.id]
            if win_desc.camera not in cam_names:
                LOG.info(f"window {win_cfg.id} will not be displayed as camera {win_desc.camera} is not available")
                self._unavailable.add(win_cfg.id)
                continue
            pipeline, win_shape = get_pipeline(win_desc.transforms, cameras[win_desc.camera])
            self._roi_finder[win_cfg.id] = partial(get_roi, shape, win_shape)
            self._comp_view.add_pane(
                win_cfg.id, pipeline, *self._roi_finder[win_cfg.id](win_cfg), get_mask(win_shape, win_desc)
            )

    def reload_pos(self, config: ViewConfig) -> None:
        self.config = config
        ordered = []
        for win_cfg in config.windows:
            if win_cfg.id in self._unavailable:
                continue
            self._comp_view.update_pane(win_cfg.id, *self._roi_finder[win_cfg.id](win_cfg))
            ordered.append(win_cfg.id)
        self._comp_view.update_order(ordered)

    async def render(self) -> None:
        await asyncio.get_event_loop().run_in_executor(None, self._comp_view.render)

    def __enter__(self) -> "View":
        self._comp_view.start()
        return self

    def __exit__(
        self, type: Optional[Type[BaseException]], value: Optional[BaseException], traceback: Optional[TracebackType]
    ) -> None:
        self._comp_view.stop()

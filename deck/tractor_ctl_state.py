from atomic_data_share.pybind.atomic_data_share_python import AtomicDataW<PERSON>per<PERSON>loat
from tractor_ctl.control_state_sync import ControlState, ControlStateSync

TRACTOR_SPEED_MPH = AtomicDataWrapperFloat(0.0)


async def state_change_cb(state: ControlState) -> None:
    if state.speed is not None:
        TRACTOR_SPEED_MPH.set(state.speed.speed_mph)


CONTROL_STATE_SYNC = ControlStateSync(state_change_cb)

import asyncio
from collections import Counter
from dataclasses import dataclass
from typing import Any, <PERSON>wai<PERSON>, Dict, List, Optional, Set, Tuple, cast
from uuid import uuid1

import aioredis

from deck.config import FullyDefinedView, OutputConfig, ViewConfig, Window, WindowConfig
from deck.messages import CamDetails, Rectangle, StatusCodes, StatusResp
from lib.common.logging import get_logger
from lib.common.messaging.message import ErrorMsg, ErrorMsgException
from lib.common.redis_client import RedisClient
from lib.common.time.time import epoch_ms
from lib.drivers.deck_cam.config import CameraType
from lib.drivers.deck_cam.pybind.deck_cam_python import Camera
from lib.rtc.deck_transform.config import (
    EulerFisheyeConfig,
    FisheyeRectConfig,
    MetadataTransforms,
    ModifyTransforms,
    RequiredFirstTransforms,
    Resize,
    RgbdToViewable,
    WheelPosViz,
)
from lib.rtc.deck_transform.pybind.deck_transform_python import WheelPosRenderTransform
from lib.rtc.deck_transform.transforms import get_shape
from tractor_ctl.grpc.client import TractorCtlGrpcClient

LOG = get_logger(__name__)


@dataclass
class ViewChangeDetails:
    view_id: str = ""
    full_reload_req: bool = True


class ViewChangeMonitor:
    def __init__(self, manager: "ViewManager") -> None:
        self._mgr = manager
        self._has_changed = False
        self._details = ViewChangeDetails()

    def _changed(self, view_id: str, full_reload_required: bool) -> None:
        self._details.view_id = view_id
        self._details.full_reload_req = full_reload_required
        self._has_changed = True

    async def get_view_if_changed(self) -> Optional[ViewChangeDetails]:
        if self._has_changed:
            self._has_changed = False
            return self._details
        return None


class ViewManager:
    WINDOW_CONF_KEY = "/deck/windows"
    VIEW_CONF_KEY = "/deck/views"
    TAGS_KEY = "/deck/tagged_views"
    WHEEL_POS_KEY = "/cv/wheel_pos"
    MAIN_TAG = "main"

    def __init__(
        self, redis: aioredis.Redis, cameras: Dict[str, Camera], output_config: OutputConfig, stop_event: asyncio.Event,
    ) -> None:
        # DO NOT call create instance directly only use the Build function
        self._redis = redis
        self._stop_event = stop_event
        self._tcc = TractorCtlGrpcClient()
        self._cams: Dict[str, CamDetails] = {}
        for name, cam in cameras.items():
            # wheel_pos will be populated in `__load_data`
            self._cams[name] = CamDetails(
                width=cam.width, height=cam.height, cam_type=CameraType(cast(int, cam.type)), wheel_pos=None
            )
        assert len(self._cams) > 0
        self._output_cfg = output_config
        self._unsaved_windows: Dict[str, Window] = {}
        self._unsaved_views: Dict[str, ViewConfig] = {}
        self._active_view = ""
        self._active_windows: Set[str] = set()
        self._monitors: List[ViewChangeMonitor] = []

        self._windows: Dict[str, Window] = {}
        self._views: Dict[str, ViewConfig] = {}
        self._tags: Dict[str, str] = {}

    @staticmethod
    async def Build(
        cameras: Dict[str, Camera], output_config: OutputConfig, stop_event: asyncio.Event
    ) -> "ViewManager":
        redis = await RedisClient.build()
        vm = ViewManager(redis, cameras, output_config, stop_event)
        await vm.__async_init()
        return vm

    @property
    def output_config(self) -> OutputConfig:
        return self._output_cfg

    @property
    def cameras(self) -> Dict[str, CamDetails]:
        return self._cams

    async def __async_init(self) -> None:
        self._redis_queue: asyncio.Queue[Awaitable[Any]] = asyncio.Queue()
        await self.__load_data()
        self.__init_cur_view()
        self._redis_task = asyncio.get_event_loop().create_task(self.__redis_sync_loop())

    async def __redis_sync_loop(self) -> None:
        while not self._stop_event.is_set():
            try:
                redis_cmd = await asyncio.wait_for(self._redis_queue.get(), timeout=0.5)
            except asyncio.TimeoutError:
                continue
            try:
                await redis_cmd
            except Exception as ex:
                LOG.warning(f"Failed to run redis command: {ex}")
        if not self._redis_queue.empty():
            LOG.error("Failed to save some data to disk, we may be missing some changes")

    async def __load_data(self) -> None:
        async for window_json in self._redis.hscan_iter(self.WINDOW_CONF_KEY, match="*"):
            window = cast(Window, Window.from_json(window_json[1]))
            self._windows[window.id] = window

        async for view_json in self._redis.hscan_iter(self.VIEW_CONF_KEY, match="*"):
            view = cast(ViewConfig, ViewConfig.from_json(view_json[1]))
            if self.__can_use_view(view, False):
                self._views[view.id] = view
        async for tag_tuple in self._redis.hscan_iter(self.TAGS_KEY, match="*"):
            self._tags[str(tag_tuple[0])] = str(tag_tuple[1])
        async for pos_tuple in self._redis.hscan_iter(self.WHEEL_POS_KEY, match="*"):
            cam_id = str(pos_tuple[0])
            cam_info = self._cams.get(cam_id)
            if cam_info:
                cam_info.wheel_pos = Rectangle.from_json(pos_tuple[1])
                assert cam_info.wheel_pos is not None
                WheelPosRenderTransform.set_pos_for_cam(
                    cam_id,
                    int(round(cam_info.wheel_pos.top_left_x * cam_info.width)),
                    int(round(cam_info.wheel_pos.top_left_y * cam_info.height)),
                    int(round(cam_info.wheel_pos.bottom_right_x * cam_info.width)),
                    int(round(cam_info.wheel_pos.bottom_right_y * cam_info.height)),
                )

    def __init_cur_view(self) -> None:
        default_id: Optional[str] = None
        if self.MAIN_TAG in self._tags:
            default_id = self._tags[self.MAIN_TAG]
        elif self._tags:
            default_id = self._tags[sorted(self._tags.keys())[0]]
        if default_id:
            view = self._views.get(default_id, None)
            if view is not None and self.__can_use_view(view, False):
                return self.__set_view_active(view)
        if self._views:
            for view in self._views.values():
                return self.__set_view_active(view)
        for cam in self._cams:
            view, _ = self.build_default_view(cam)
            if view is not None:
                return self.__set_view_active(view)

    def __set_view_active(self, view: ViewConfig) -> None:
        self._active_view = view.id
        self._active_windows = set([window.id for window in view.windows])

    def list_tags(self) -> List[str]:
        avail_tags = []
        for tag, view_id in self._tags.items():
            if view_id in self._views:
                avail_tags.append(tag)
        return avail_tags

    def set_view_for_tag(self, tag: str, view_id: str) -> None:
        self._tags[tag] = view_id
        self._redis_queue.put_nowait(self._redis.hset(self.TAGS_KEY, tag, view_id))

    def set_tags_for_view(self, view_id: str, tags: List[str]) -> Optional[ErrorMsg]:
        existing = {tag for (tag, vid) in self._tags.items() if vid == view_id}
        tag_set = set(tags)
        to_rm = existing - tag_set
        to_add = tag_set - existing
        for tag in to_rm:
            self._tags.pop(tag, None)
            self._redis_queue.put_nowait(self._redis.hdel(self.TAGS_KEY, tag))
        for tag in to_add:
            self._tags[tag] = view_id
            self._redis_queue.put_nowait(self._redis.hset(self.TAGS_KEY, tag, view_id))
        return None

    def get_view_for_tag(self, tag: str) -> Optional[str]:
        return self._tags.get(tag, None)

    def set_view_active(self, view_id: str) -> Optional[ErrorMsg]:
        view = self._views.get(view_id, None)
        if view is None:
            return ErrorMsg(msg=f"view {view_id} not found")
        if not self.__can_use_view(view):
            return ErrorMsg(msg=f"view {view_id} references cameras that are not available")
        self.__set_view_active(view)
        self.__notify(view.id, True)
        return None

    def get_active_view(self) -> str:
        return self._active_view

    def monitor_active_view(self) -> ViewChangeMonitor:
        self._monitors.append(ViewChangeMonitor(self))
        return self._monitors[-1]

    def __notify(self, view_id: str, full_reload_required: bool) -> None:
        for monitor in self._monitors:
            monitor._changed(view_id, full_reload_required)

    def get_available_windows(self) -> Dict[str, Window]:
        cams = set(self._cams.keys())
        return {window.id: window for window in self._windows.values() if window.camera in cams}

    def get_windows_for_view(self, view: ViewConfig) -> Dict[str, Window]:
        return {win_cfg.id: self._windows[win_cfg.id] for win_cfg in view.windows if win_cfg.id in self._windows}

    def get_view(self, id: str) -> Optional[ViewConfig]:
        return self._views.get(id, None)

    def view_to_fully_defined_view(self, view: Optional[ViewConfig]) -> FullyDefinedView:
        if view is None or not self.__can_use_view(view):
            return FullyDefinedView()
        windows = self.get_windows_for_view(view)
        broken = []
        oob = []
        shapes = {}
        for win_id, win in windows.items():
            if win.camera not in self._cams:
                shapes[win_id] = (0.0, 0.0)
                broken.append(win_id)
            else:
                cam_info = self._cams[win.camera]
                shape = get_shape(win.transforms, (cam_info.width, cam_info.height))
                shapes[win_id] = (shape[0] / self._output_cfg.width, shape[1] / self._output_cfg.height)
        for i in range(len(view.windows)):
            view.windows[i].width = shapes[view.windows[i].id][0]
            view.windows[i].height = shapes[view.windows[i].id][1]
        for win_cfg in view.windows:
            if (
                win_cfg.x < 0.0
                or win_cfg.y < 0.0
                or win_cfg.x + win_cfg.width > 1.0
                or win_cfg.y + win_cfg.height > 1.0
            ):
                oob.append(win_cfg.id)
        # While we don't allow a view to be saved with an OOB window, windows can be modified post view add.
        # As such it is possible to end up with a window that is OOB in a view.
        return FullyDefinedView(view=view, windows=windows, broken_windows=broken, oob_windows=oob)

    def get_fully_defined_view(self, id: str) -> FullyDefinedView:
        if id == "":
            id = self._active_view
        view = self.get_view(id)
        return self.view_to_fully_defined_view(view)

    def get_windows_for_camera(self, camera: str) -> Dict[str, Window]:
        return {k: v for k, v in self._windows.items() if v.camera == camera}

    def __can_use_view(self, view: ViewConfig, allow_empty: bool = True) -> bool:
        # At least 1 cam must be currently available to be usable
        for cam_name in view.cameras:
            if cam_name in self._cams:
                return True
        if allow_empty and len(view.windows) == 0:
            return True
        return False

    def get_views(self) -> Dict[str, ViewConfig]:
        return self._views.copy()

    def add_view(self, view: ViewConfig, save: bool) -> str:
        if save and len(view.windows) == 0:
            raise ErrorMsgException(ErrorMsg("Cannot save a view with no windows to disk."))
        view.metadata.last_modified = epoch_ms()
        if view.id == "":
            view.id = str(uuid1())
        windows = self.get_windows_for_view(view)
        for wc in view.windows:
            if wc.id not in windows:
                raise ErrorMsgException(ErrorMsg(msg=f"View references unknown window {wc.id}"))
        if save:
            for _, win in windows.items():
                if win.metadata.last_saved == 0:
                    raise ErrorMsgException(ErrorMsg(msg=f"Cannot save view that references unsaved window {win.id}"))
        full_reload = True
        curr_view = self._views.get(view.id, None)
        if curr_view is not None:
            curr_ids = Counter(win_cfg.id for win_cfg in curr_view.windows)
            new_ids = Counter(win.id for win in windows.values())
            full_reload = not (new_ids == curr_ids)
        view.cameras = list(set([window.camera for window in windows.values()]))
        fdv = self.view_to_fully_defined_view(view)
        if fdv.view is None:
            raise ErrorMsgException(ErrorMsg("Invalid view"))
        if len(fdv.oob_windows) > 0:
            raise ErrorMsgException(
                ErrorMsg(f"The following windows do not fit within the display bounds {','.join(fdv.oob_windows)}")
            )
        if save:
            view.metadata.last_saved = view.metadata.last_modified
        self._views[view.id] = view
        if self._active_view == view.id:
            if full_reload:
                # This will re-set active windows which is only needed on full reload
                self.__set_view_active(view)
            self.__notify(view.id, full_reload)
        if save:
            self._redis_queue.put_nowait(self._redis.hset(self.VIEW_CONF_KEY, view.id, view.to_json()))
        return view.id

    async def reset_view(self, id: str) -> Optional[ErrorMsg]:
        saved = await self._redis.hget(self.VIEW_CONF_KEY, id)
        if saved is None or (not saved):
            return ErrorMsg(msg="Cannot reset view as it has never been saved")
        view = cast(ViewConfig, ViewConfig.from_json(saved))
        self._views[view.id] = view
        if self._active_view == id:
            self.__set_view_active(view)
            self.__notify(view.id, True)
        return None

    def delete_view(self, id: str) -> Optional[ErrorMsg]:
        if self._active_view == id:
            return ErrorMsg(msg="Cannot delete active view")
        deleted_view = self._views.pop(id, None)
        if deleted_view is None:
            return ErrorMsg(msg="Invalid view ID")
        self._redis_queue.put_nowait(self._redis.hdel(self.VIEW_CONF_KEY, id))
        return None

    def __check_transforms(self, window: Window) -> Optional[str]:
        depth_transform = False
        modify_transform_count = 0
        for i, t in enumerate(window.transforms):
            if i != 0 and isinstance(t, RequiredFirstTransforms):
                return f"Invalid transforms list, {type(t)} must be the first transform if used"
            elif modify_transform_count > 0 and isinstance(t, MetadataTransforms):
                return "Invalid transforms list, metadata transforms must be applied prior to modifying transforms"
            elif isinstance(t, ModifyTransforms):
                modify_transform_count += 1

            if isinstance(t, (FisheyeRectConfig, EulerFisheyeConfig)):
                if modify_transform_count > 1:  # can only be the first modify transform
                    return "Invalid transforms list, fisheye rectification can only be used as the first modifying transform"
                if self._cams[window.camera].cam_type != CameraType.FISHEYE:
                    return f"Cannot use fisheye rectification with non fisheye camera {window.camera}"
                elif isinstance(t, FisheyeRectConfig):
                    cast(FisheyeRectConfig, window.transforms[i]).window_width = self._output_cfg.width
                    cast(FisheyeRectConfig, window.transforms[i]).window_height = self._output_cfg.height
                else:
                    cast(EulerFisheyeConfig, window.transforms[i]).configured_view_width = self._output_cfg.width

            elif isinstance(t, RgbdToViewable):
                depth_transform = True
            elif isinstance(t, WheelPosViz):
                cam_info = self._cams.get(window.camera)
                if not cam_info or not cam_info.wheel_pos:
                    return f"No wheel position defined for {window.camera}"
            if not t.validate():
                return f"Transform {i} has invalid parameters."

        if self._cams[window.camera].cam_type == CameraType.RGBD and not depth_transform:
            return f"rgbd to viewable transform is required as first transform for camera {window.camera}"
        elif self._cams[window.camera].cam_type != CameraType.RGBD and depth_transform:
            return "rgbd to viewable transform is can only be used with rgbd cameras"
        return None

    def add_window(self, window: Window, save: bool) -> str:
        window.metadata.last_modified = epoch_ms()
        if window.camera not in self._cams:
            raise ErrorMsgException(ErrorMsg(msg=f"Cannot create window for unknown camera {window.camera}"))
        if window.id == "":
            window.id = str(uuid1())
        else:
            prev_win = self._windows.get(window.id, None)
            if prev_win is not None:
                if prev_win.camera != window.camera:
                    raise ErrorMsgException(ErrorMsg(msg="Cannot change camera for a window, must define new window"))
        opt_resp = self.__check_transforms(window)
        if opt_resp is not None:
            raise ErrorMsgException(ErrorMsg(msg=opt_resp, code=StatusCodes.INVALID_TRANSFORMS))
        if save:
            window.metadata.last_saved = window.metadata.last_modified

        self._windows[window.id] = window
        if window.id in self._active_windows:
            self.__notify(self._active_view, True)
        if save:
            self._redis_queue.put_nowait(self._redis.hset(self.WINDOW_CONF_KEY, window.id, window.to_json()))
        return window.id

    async def reset_window(self, id: str) -> Optional[ErrorMsg]:
        saved = await self._redis.hget(self.WINDOW_CONF_KEY, id)
        if not saved:
            return ErrorMsg(msg="Cannot reset window as no previous save state exists")
        window = cast(Window, Window.from_json(saved))
        self._windows[id] = window
        if id in self._active_windows:
            self.__notify(self._active_view, True)
        return None

    def __delete_window(self, id: str) -> StatusResp:
        self._windows.pop(id, None)
        self._redis_queue.put_nowait(self._redis.hdel(self.WINDOW_CONF_KEY, id))
        return StatusResp(status=True)

    def delete_windows(self, ids: List[str]) -> Dict[str, StatusResp]:
        views = self.get_views()
        all_used_windows = []
        for view in views.values():
            for win in view.windows:
                all_used_windows.append(win.id)
        in_use = set(all_used_windows)
        resp = {}
        for id in ids:
            if id in in_use:
                resp[id] = StatusResp(status=False, msg="Window is referenced from an existing view")
            else:
                resp[id] = self.__delete_window(id)
        return resp

    def build_default_view(self, cam: str) -> Tuple[Optional[ViewConfig], Dict[str, Window]]:
        if cam not in self._cams:
            return (None, {})
        cam_info = self._cams[cam]
        id = str(uuid1())
        window = Window(id=id, camera=cam)
        if cam_info.cam_type != CameraType.FISHEYE:
            if cam_info.cam_type == CameraType.RGBD:
                window.transforms.append(RgbdToViewable(to_depth=True))

            cam_aspect_ratio = cam_info.width / cam_info.height
            output_aspect_ratio = self._output_cfg.width / self._output_cfg.height
            if cam_aspect_ratio > output_aspect_ratio:
                # camera is wider -> letterbox
                fac = self._output_cfg.width / cam_info.width
            else:
                # camera is taller -> pillarbox
                fac = self._output_cfg.height / cam_info.height
            window.transforms.append(Resize(width=fac, height=fac))
        else:
            aspect_ratio = self._output_cfg.aspect_ratio
            if aspect_ratio > 1:
                (width, height) = (1.0, 1 / aspect_ratio)
            else:
                (width, height) = (aspect_ratio, 1.0)
            window.transforms.append(
                EulerFisheyeConfig(
                    roll=0.0,
                    pitch=0.0,
                    yaw=0.0,
                    zoom=0.25,
                    configured_view_width=self._output_cfg.width,
                    mirror=False,
                    flip=False,
                    tangent_plane_size=(width, height),
                )
            )
        id = str(uuid1())
        view = ViewConfig(id=id, cameras=[cam], windows=[WindowConfig(id=window.id, x=0, y=0, width=1.0, height=1.0)],)
        if view is not None:
            self._views[view.id] = view
            self._windows[window.id] = window
        return (view, {window.id: window})

    def set_wheel_pos(self, cam: str, pos: Rectangle) -> Optional[ErrorMsg]:
        cam_info = self._cams.get(cam)
        if cam_info:  # okay if not; update Redis but nothing to do in memory
            cam_info.wheel_pos = pos
            WheelPosRenderTransform.set_pos_for_cam(
                cam,
                int(round(cam_info.wheel_pos.top_left_x * cam_info.width)),
                int(round(cam_info.wheel_pos.top_left_y * cam_info.height)),
                int(round(cam_info.wheel_pos.bottom_right_x * cam_info.width)),
                int(round(cam_info.wheel_pos.bottom_right_y * cam_info.height)),
            )

        async def __inner() -> None:
            await self._redis.hset(self.WHEEL_POS_KEY, cam, pos.to_json())
            try:
                await self._tcc.reload_wheel_pos()
            except Exception:
                LOG.exception("Failed to request wheel pos reload from tractor ctl.")

        self._redis_queue.put_nowait(__inner())
        return None

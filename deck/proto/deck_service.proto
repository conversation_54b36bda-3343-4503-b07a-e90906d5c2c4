syntax = "proto3";

package deck;
option go_package = "proto/deck";


message Config {
  string key = 1;
  oneof value {
    bool bool_val = 2;
    int64 int_val = 3;
    double float_val = 4;
    string str_val = 5;
  }
}
message SetConfigsRequest {
  string camera = 1;
  repeated Config configs = 2;
}

message SetConfigsResponse {
}

message GetConfigRequest {
  string camera = 1;
  string key = 2;
  string type = 3;
}

message GetConfigResponse {
  Config config = 1;
}

message PingMsg {
  uint32 x = 1;
}

message PongMsg {
  uint32 x = 1;
}
service DeckService {
  rpc Ping(PingMsg) returns (PongMsg) {}
  rpc SetConfigs(SetConfigsRequest) returns (SetConfigsResponse) {}
  rpc GetConfig(GetConfigRequest) returns (GetConfigResponse) {}
}

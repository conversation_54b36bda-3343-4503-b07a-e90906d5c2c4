# Configuring cameras

Use config service to add cameras

## Mocking videos for testing

### Non-fisheye flat videos

Place a test video file or files somewhere under the `robot` directory.

We need to add each camera using config service.
`bot run` to start executing in commander

For each camera:

1. Add the camera `cfg add rtc/deck/video_cameras CAMERA_NAME`
2. See all the fields you need to populate by running `cfg list | grep rtc/deck/video_cameras/CAMERA_NAME`
3. Set the file path `cfg set rtc/deck/video_cameras/CAMERA_NAME/path /robot/YOUR_PATH`
4. Enable the camera `cfg set rtc/deck/video_cameras/CAMERA_NAME/enabled true`

### Mocking fisheye streams for testing

You can use the [Cam Grabber Tool](../tools/deck/cam_grabber.py) to record frames from a live fish eye camera. Example output can be found [here](https://drive.google.com/file/d/1pn8B5-T7P2AVLBKxWYx81WVeHFRBzbsD/view). Make sure to go to the config service to record relevant fish eye camera parameters ('k' and 'd' and 'binning')

To use the resulting fish eye capture, place the video file or files somewhere under the `data` directory.

We need to add each camera using config service.
`bot run` to start executing in commander

For each camera:

1. Add the camera `cfg add rtc/deck/img_dir_cameras CAMERA_NAME`
2. See all the fields you need to populate by running `cfg list | grep rtc/deck/img_dir_cameras/CAMERA_NAME`
3. Set the file path `cfg set rtc/deck/img_dir_cameras/CAMERA_NAME/path /data/YOUR_PATH`
4. Set that the camera is a fisheye `cfg set rtc/deck/img_dir_cameras/CAMERA_NAME/fisheye/is_fisheye true`
5. Set the fisheye d param `cfg set rtc/deck/img_dir_cameras/CAMERA_NAME/fisheye/d "[[ 0.00927061], [ 0.01598885], [-0.01244864], [ 0.00252432]]"`
6. Set the fisheye k param `cfg set rtc/deck/img_dir_cameras/CAMERA_NAME/fisheye/k "[[8.89068474e+02, 0.00000000e+00, 1.47576564e+03], [0.00000000e+00, 8.88832493e+02, 9.35193559e+02], [0.00000000e+00, 0.00000000e+00, 1.00000000e+00]]"`
7. Set the fisheye binning param `cfg set rtc/deck/img_dir_cameras/CAMERA_NAME/fisheye/binning 2`
8. Enable the camera `cfg set rtc/deck/img_dir_cameras/CAMERA_NAME/enabled true`

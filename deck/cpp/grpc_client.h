#pragma once

#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <variant>

#include "generated/deck/proto/deck_service.grpc.pb.h"
#include "generated/deck/proto/deck_service.pb.h"

#include <grpcpp/grpcpp.h>

namespace carbon::deck {

class DeckClient {
  using CfgValueType = std::variant<bool, int, double, std::string>;

protected:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<::deck::DeckService::Stub> stub_;
  uint32_t max_backoff_ms_;
  double backoff_multiplier_;
  std::mutex channel_setup_mutex_;

public:
  static const uint32_t DEFAULT_PORT = 61014;
  DeckClient(const std::string &host = "127.0.0.1", int port = DEFAULT_PORT, uint32_t max_backoff_ms = 30000,
             double backoff_multiplier = 1.5);

  bool await_connection(uint64_t timeout_ms = 0);
  void reset();
  void ping(uint32_t x = 42);
  void set_configs(const std::string &camera, const std::map<std::string, CfgValueType> &configs);
  double get_double_config(const std::string &camera, const std::string &key);
  std::string get_string_config(const std::string &camera, const std::string &key);
  int get_int_config(const std::string &camera, const std::string &key);
  bool get_bool_config(const std::string &camera, const std::string &key);

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void setup_grpc(bool reconnect_if_down = true);
  std::shared_ptr<::deck::DeckService::Stub> get_grpc_stub(bool reconnect_if_down = true);
  void reset_stub();
  ::deck::GetConfigResponse get_config(const std::string &camera, const std::string &key, const std::string &type);
};

} // namespace carbon::deck

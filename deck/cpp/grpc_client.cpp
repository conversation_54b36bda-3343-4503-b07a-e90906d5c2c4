#include "deck/cpp/grpc_client.h"

#include <functional>
#include <stdexcept>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon::deck {

DeckClient::DeckClient(const std::string &host, int port, uint32_t max_backoff_ms, double backoff_multiplier)
    : addr_(fmt::format("{}:{}", host, port)), channel_(nullptr), max_backoff_ms_(max_backoff_ms),
      backoff_multiplier_(backoff_multiplier) {}

void DeckClient::setup_grpc(bool reconnect_if_down) {
  // Not Thread Safe
  if (this->channel_ != nullptr && this->channel_->GetState(true) != GRPC_CHANNEL_READY) {
    this->reset_stub();
  }
  if (this->channel_ == nullptr) {
    if (!reconnect_if_down) {
      return;
    }
    this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<::deck::DeckService::Stub>(this->channel_);
  }
}

std::shared_ptr<::deck::DeckService::Stub> DeckClient::get_grpc_stub(bool reconnect_if_down) {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->setup_grpc(reconnect_if_down);
  return this->stub_;
}

void DeckClient::reset() {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  return this->reset_stub();
}

void DeckClient::reset_stub() {
  // Not Thread Safe
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status DeckClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    this->reset_stub();
    throw;
  }
}

bool DeckClient::await_connection(uint64_t timeout_ms) {
  auto start = std::chrono::steady_clock::now();
  std::chrono::milliseconds chrono_timeout_ms{timeout_ms};
  uint32_t backoff_ms = 1000;
  std::chrono::duration<double> duration_s;
  while (true) {
    duration_s = std::chrono::steady_clock::now() - start;
    if (timeout_ms != 0 && duration_s > chrono_timeout_ms) {
      return false;
    }
    try {
      this->ping();
      return true;
    } catch (const std::exception &ex) {
    }
    backoff_ms = (uint32_t)(backoff_ms * this->backoff_multiplier_);
    backoff_ms = backoff_ms <= this->max_backoff_ms_ ? backoff_ms : this->max_backoff_ms_;

    if (timeout_ms != 0 &&
        (duration_s > chrono_timeout_ms || std::chrono::milliseconds(backoff_ms) > (chrono_timeout_ms - duration_s))) {
      return false;
    }
    spdlog::warn("Awaiting Connection to Deck with Backoff: {} ms", backoff_ms);
    std::this_thread::sleep_for(std::chrono::milliseconds(backoff_ms));
  }
}

void DeckClient::ping(uint32_t x) {
  grpc::ClientContext context;
  ::deck::PingMsg req;
  ::deck::PongMsg resp;
  req.set_x(x);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&::deck::DeckService::Stub::Ping, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw std::runtime_error(fmt::format("Invalid Ping Status Code: {}", status.error_code()));
  }

  if (resp.x() != x) {
    throw std::runtime_error(fmt::format("Invalid Pong Value: {}, Expected: {}", resp.x(), 42));
  }
}
void DeckClient::set_configs(const std::string &camera, const std::map<std::string, CfgValueType> &configs) {
  grpc::ClientContext context;
  ::deck::SetConfigsRequest req;
  ::deck::SetConfigsResponse resp;
  req.set_camera(camera);
  for (auto &cfg_in : configs) {
    auto *cfg = req.add_configs();
    cfg->set_key(cfg_in.first);
    if (std::holds_alternative<bool>(cfg_in.second)) {
      cfg->set_bool_val(std::get<bool>(cfg_in.second));
    } else if (std::holds_alternative<int>(cfg_in.second)) {
      cfg->set_int_val(std::get<int>(cfg_in.second));
    } else if (std::holds_alternative<double>(cfg_in.second)) {
      cfg->set_float_val(std::get<double>(cfg_in.second));
    } else if (std::holds_alternative<std::string>(cfg_in.second)) {
      cfg->set_str_val(std::get<std::string>(cfg_in.second));
    }
  }
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&::deck::DeckService::Stub::SetConfigs, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::error(status.error_details());
    throw std::runtime_error(fmt::format("Invalid set configs Status Code: {}", status.error_code()));
  }
}

double DeckClient::get_double_config(const std::string &camera, const std::string &key) {
  auto resp = get_config(camera, key, "float");
  if (resp.config().has_float_val()) {
    return resp.config().float_val();
  } else {
    throw std::runtime_error(fmt::format("Config {} is not of type float", key));
  }
}

std::string DeckClient::get_string_config(const std::string &camera, const std::string &key) {
  auto resp = get_config(camera, key, "str");
  if (resp.config().has_str_val()) {
    return resp.config().str_val();
  } else {
    throw std::runtime_error(fmt::format("Config {} is not of type string", key));
  }
}

int DeckClient::get_int_config(const std::string &camera, const std::string &key) {
  auto resp = get_config(camera, key, "int");
  if (resp.config().has_int_val()) {
    return (int)resp.config().int_val();
  } else {
    throw std::runtime_error(fmt::format("Config {} is not of type int", key));
  }
}

bool DeckClient::get_bool_config(const std::string &camera, const std::string &key) {
  auto resp = get_config(camera, key, "bool");
  if (resp.config().has_bool_val()) {
    return resp.config().bool_val();
  } else {
    throw std::runtime_error(fmt::format("Config {} is not of type bool", key));
  }
}

::deck::GetConfigResponse DeckClient::get_config(const std::string &camera, const std::string &key,
                                                 const std::string &type) {
  grpc::ClientContext context;
  ::deck::GetConfigRequest req;
  ::deck::GetConfigResponse resp;
  req.set_camera(camera);
  req.set_key(key);
  req.set_type(type);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&::deck::DeckService::Stub::GetConfig, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::error(status.error_details());
    throw std::runtime_error(fmt::format("Invalid get config Status Code: {}", status.error_code()));
  }

  return resp;
}

} // namespace carbon::deck

import datetime

import cv2
import numpy as np
import numpy.typing as npt


def annotate_time(image: npt.NDArray[np.uint8]) -> None:
    text = str(datetime.datetime.now())
    font = cv2.FONT_HERSHEY_COMPLEX_SMALL
    font_scale = 1
    thickness = 1
    padding = 5

    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    cv2.putText(
        image,
        text,
        (image.shape[1] - text_size[0] - padding, text_size[1] + padding),
        fontFace=font,
        fontScale=font_scale,
        color=(0, 0, 0),
        thickness=thickness + 2,
    )
    cv2.putText(
        image,
        text,
        (image.shape[1] - text_size[0] - padding, text_size[1] + padding),
        fontFace=font,
        fontScale=font_scale,
        color=(255, 219, 0),
        thickness=thickness,
    )


def annotate_color_marker(image: npt.NDArray[np.uint8], color: str) -> None:
    color = color.lstrip("#")
    rgb = tuple(int(color[i : i + 2], 16) for i in (0, 2, 4))  # noqa: E203
    image[0, 0] = rgb

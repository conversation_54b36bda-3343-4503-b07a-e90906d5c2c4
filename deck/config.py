from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, cast

from dataclass_wizard import JSONWizard, YAMLWizard

from lib.common.time.time import epoch_ms
from lib.drivers.deck_cam.config import CamConfigType
from lib.rtc.deck_transform.config import TransformType
from lib.rtc.messaging.message import JSONObject


@dataclass
class Metadata(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    last_saved: int = 0
    last_modified: int = field(default_factory=epoch_ms)
    version: str = "0.0.0"
    data: JSONObject = field(default_factory=dict)


@dataclass
class Window(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    id: str  # uuid
    camera: str
    transforms: List[TransformType] = field(default_factory=list)
    metadata: Metadata = field(default_factory=Metadata)
    mask_polygon: List[Tuple[float, float]] = field(default_factory=list)


@dataclass
class WindowConfig:
    id: str
    x: float
    y: float
    width: float
    height: float


@dataclass
class ViewConfig(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    id: str  # uuid
    windows: List[WindowConfig] = field(default_factory=list)
    cameras: List[str] = field(default_factory=list)
    metadata: Metadata = field(default_factory=Metadata)


@dataclass
class FullyDefinedView(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    view: Optional[ViewConfig] = None
    windows: Dict[str, Window] = field(default_factory=dict)
    broken_windows: List[str] = field(default_factory=list)
    oob_windows: List[str] = field(
        default_factory=list
    )  # out of bounds windows cannot be displayed as they would overrun the image


@dataclass
class OutputConfig(JSONWizard):
    width: int = 1920
    height: int = 1080

    @property
    def aspect_ratio(self) -> float:
        return self.width / self.height


@dataclass(eq=True, frozen=True)
class Config(JSONWizard, YAMLWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    output: OutputConfig
    cameras: Dict[str, CamConfigType] = field(default_factory=dict)


def load_config(path: str) -> Config:
    return cast(Config, Config.from_yaml_file(path))

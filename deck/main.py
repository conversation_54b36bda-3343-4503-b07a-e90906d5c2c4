#!/usr/bin/env -S python3 -u
import argparse
import asyncio
import sys
from contextlib import ExitStack
from time import time
from typing import Any, Dict, List, Optional

import cv2
from prometheus_client import Gauge, start_http_server

from carbon_logging.pybind.logging_python import init_logger as init_cpp_logger
from config.client.cpp.config_client_python import get_computer_config_prefix, get_global_config_subscriber
from cv.deck.deck_cv_python import DeckCV
from deck.config import OutputConfig
from deck.grpc.server import GrpcServer
from deck.messages import REQ_RESP_MAP, MeasureRequest, MeasureResponse, MessageType, StatusResp
from deck.tractor_ctl_state import CONTROL_STATE_SYNC, TRACTOR_SPEED_MPH
from deck.view import View
from deck.view_if import ViewIF
from deck.view_manager import ViewManager
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.data_structures.circular_buffer import CircularBuffer
from lib.common.generation import is_rtc
from lib.common.logging import get_logger, init_log
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.deck_cam.pybind.deck_cam_python import (
    Camera,
    ImgDirCamera,
    LucidCamera,
    VideoFileCamera,
    get_cr_multi_cams,
)
from lib.rtc.data_provider.data_bus_client import AUTH_READ_REQ, DATA_BUS
from lib.rtc.messaging.message import Message
from v4l2_utils.pybind.v4l2_utils_python import find_or_create_v4l2_device, v4l2_fd_set_format_rgb24

CHECK_SECONDS: int = 1
LOG = get_logger(__name__)
SERVICE_NAME = "deck"


class Deck:
    def __init__(
        self,
        card: str,
        cameras: Dict[str, Camera],
        output_cfg: OutputConfig,
        view_manager: ViewManager,
        stop_event: asyncio.Event,
    ):
        self._loop = asyncio.get_event_loop()
        self._cameras = cameras
        self._output_shape = (output_cfg.width, output_cfg.height)
        self._vm = view_manager
        dev = find_or_create_v4l2_device(card)
        assert dev is not None
        self._output_dev = dev
        self._stop_event = stop_event
        self._latency_event = asyncio.Event()
        self._ready = asyncio.Event()
        self._latency_marker_color = ""
        self._latency_resp_queue: asyncio.Queue[MeasureResponse] = asyncio.Queue()
        DATA_BUS.register(MessageType.READY_REQ, self._ready_req, AUTH_READ_REQ)

        self._view_if = ViewIF(self._vm)

        self._frame_rate = Gauge(
            subsystem=SERVICE_NAME, name="frame_rate", documentation="Current frame rate of system",
        )

    def camera_exists(self, camera: str) -> bool:
        return camera in self._cameras

    def set_camera_config(self, camera: str, key: str, value: Any) -> None:
        if not self.camera_exists(camera):
            raise KeyError(f"No camera named {camera} found.")
        if isinstance(value, (int, bool, float, str)):
            self._cameras[camera].set_config(key, value)
        else:
            raise NotImplementedError("Unsupported value type")

    def get_camera_config(self, camera: str, key: str) -> Any:
        if not self.camera_exists(camera):
            raise KeyError(f"No camera named {camera} found.")
        return self._cameras[camera].get_config_as_str(key)

    def get_camera_config_bool(self, camera: str, key: str) -> Optional[bool]:
        if not self.camera_exists(camera):
            raise KeyError(f"No camera named {camera} found.")
        return self._cameras[camera].get_config_bool(key)

    def get_camera_config_str(self, camera: str, key: str) -> Optional[str]:
        if not self.camera_exists(camera):
            raise KeyError(f"No camera named {camera} found.")
        return self._cameras[camera].get_config_str(key)

    def get_camera_config_int(self, camera: str, key: str) -> Optional[int]:
        if not self.camera_exists(camera):
            raise KeyError(f"No camera named {camera} found.")
        return self._cameras[camera].get_config_int(key)

    def get_camera_config_float(self, camera: str, key: str) -> Optional[float]:
        if not self.camera_exists(camera):
            raise KeyError(f"No camera named {camera} found.")
        return self._cameras[camera].get_config_double(key)

    async def _request_measure_frame(self, msg: Message) -> Optional[Message]:
        req = MeasureRequest.from_dict(msg.content)
        self._latency_marker_color = req.color
        self._latency_event.set()
        resp = await self._latency_resp_queue.get()
        return Message.build(MessageType.MEASURE_RESP, resp)

    async def _ready_req(self, _: Message) -> Optional[Message]:
        return Message.build(MessageType.READY_RESP, StatusResp(status=self._ready.is_set()))

    async def run(self) -> None:
        win_size = 300
        with ExitStack() as stack:
            for cam in self._cameras.values():
                cam.start()
                stack.callback(cam.stop)
            LOG.info("running")
            with open(self._output_dev, "wb", buffering=0) as vd:
                assert v4l2_fd_set_format_rgb24(vd.fileno(), self._output_shape[0], self._output_shape[1])
                view_monitor = self._vm.monitor_active_view()
                active_view_id = self._vm.get_active_view()
                while not self._stop_event.is_set():
                    view_cfg = self._vm.get_view(active_view_id)
                    assert view_cfg is not None
                    windows = self._vm.get_windows_for_view(view_cfg)
                    view = View(vd.fileno(), view_cfg, self._output_shape, self._cameras, windows)
                    with view:
                        render_speed = CircularBuffer(win_size, float)
                        count = 0
                        LOG.info(f"Starting view render {view_cfg.id}")
                        self._ready.set()
                        while not self._stop_event.is_set():
                            new_view_details = await view_monitor.get_view_if_changed()
                            if new_view_details is not None:
                                if new_view_details.full_reload_req:
                                    active_view_id = new_view_details.view_id
                                    break
                                else:
                                    assert active_view_id == new_view_details.view_id
                                    view_cfg = self._vm.get_view(active_view_id)
                                    assert view_cfg is not None
                                    view.reload_pos(view_cfg)
                            await view.render()
                            render_speed.push(time())
                            count = (count + 1) % win_size
                            if count == 0:
                                size = len(render_speed)
                                delta_t = render_speed[-1] - render_speed[0]
                                if size > 0 and delta_t > 0:
                                    rate = size / delta_t
                                    self._frame_rate.set(rate)


def load_cams(deck_cv: Optional[DeckCV]) -> Dict[str, Camera]:
    cam_builder_map = {
        "lucid_cameras": lambda tree: LucidCamera(tree, deck_cv),
        "video_cameras": lambda tree: VideoFileCamera(tree),
        "img_dir_cameras": lambda tree: ImgDirCamera(tree),
    }
    multi_cam_builder_map = {"cr_multi_sense_cameras": lambda tree: get_cr_multi_cams(tree, deck_cv)}
    config_subscriber = get_global_config_subscriber()
    deck_cfg = config_subscriber.get_config_node(SERVICE_NAME, "")
    cams: Dict[str, Camera] = {}
    for list_name in cam_builder_map.keys():
        for cfg in deck_cfg.get_node(list_name).get_children_nodes():
            if not cfg.get_node("enabled").get_bool_value():
                LOG.info(f"Skipping disabled camera {cfg.get_name()}")
                continue
            LOG.info(f"Loading camera {cfg.get_name()}")
            cams[cfg.get_name()] = cam_builder_map[list_name](cfg)
    for list_name in multi_cam_builder_map.keys():
        for cfg in deck_cfg.get_node(list_name).get_children_nodes():
            LOG.info(f"Loading cameras for {cfg.get_name()}")
            multi_cams: List[Camera] = multi_cam_builder_map[list_name](cfg)
            for cam in multi_cams:
                LOG.info(f"Loading camera {cam.name}")
                cams[cam.name] = cam

    for cfg in deck_cfg.get_node("cameras").get_children_nodes():
        if not cfg.get_node("enabled").get_bool_value():
            LOG.info(f"Skipping disabled camera {cfg.get_name()}")
            continue
        if cfg.get_node("type").get_string_value() != "LucidCameraConfig":
            LOG.info(
                f"Skipping camera {cfg.get_name()} as camera type {cfg.get_node('type').get_string_value()} is not currently supported"
            )
            continue
        LOG.warning(
            f"Loading lucid camera {cfg.get_name()} from generic list, please move this config to lucid_cameras list"
        )
        cams[cfg.get_name()] = LucidCamera(cfg, deck_cv)
    return cams


def get_output_cfg() -> OutputConfig:
    config_subscriber = get_global_config_subscriber()
    out_tree = config_subscriber.get_config_node(SERVICE_NAME, "output")
    return OutputConfig(
        width=out_tree.get_node("width").get_int_value(), height=out_tree.get_node("height").get_int_value()
    )


async def run() -> None:
    assert cv2.cuda.getCudaEnabledDeviceCount() > 0
    parser = argparse.ArgumentParser(description="Observation Deck")
    parser.add_argument("--card", help="v4l2 card name to output to", default="Deck", type=str)
    parser.add_argument("--config", help="Configuration file", type=str, default="deck_config.yaml")
    parser.add_argument("--ws-port", help="Port to start server", type=int, default=8766)
    parser.add_argument("--metrics-port", help="Port to start metrics server", type=int, default=62010)
    args = parser.parse_args()
    config_subscriber = get_global_config_subscriber()
    if is_rtc():
        config_subscriber.add_config_tree(
            SERVICE_NAME, f"{get_computer_config_prefix()}/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml"
        )
        config_subscriber.add_config_tree("common", "common", "services/rtc_common.yaml")
    else:
        LOG.info("Non RTC hardware pretending to be RTC")
        config_subscriber.add_config_tree(SERVICE_NAME, f"rtc/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml")
        config_subscriber.add_config_tree("common", "common", "services/common.yaml")
    config_subscriber.start()
    await asyncio.get_event_loop().run_in_executor(None, lambda: config_subscriber.wait_until_ready())

    start_http_server(args.metrics_port)
    stop_event = await bot_stop_handler.get_stop_event()
    deck_cv: Optional[DeckCV] = None
    if is_rtc():
        deck_cv = DeckCV(TRACTOR_SPEED_MPH)
    cameras = load_cams(deck_cv)
    if deck_cv is not None:
        await asyncio.get_event_loop().run_in_executor(None, lambda: deck_cv.start())
    output = get_output_cfg()
    view_manager = await ViewManager.Build(cameras, output, stop_event)
    server = Deck(args.card, cameras, output, view_manager, stop_event)
    grpc_server = GrpcServer(server)
    await grpc_server.start()
    tractor_sync_task = await CONTROL_STATE_SYNC.start(stop_event)
    await DATA_BUS.start("view_controls", REQ_RESP_MAP)
    await server.run()
    if deck_cv:
        await asyncio.get_event_loop().run_in_executor(None, lambda: deck_cv.stop())
    await tractor_sync_task


async def main() -> None:
    try:
        await run()
    except Exception as ex:
        bot_stop_handler.exit_with_exception(ex)


if __name__ == "__main__":
    init_log(level="INFO", logfile=f"{SERVICE_NAME}.log")
    init_cpp_logger(f"{SERVICE_NAME}.log")
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name())
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)

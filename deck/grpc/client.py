from argparse import Argument<PERSON><PERSON><PERSON>
from typing import Any, Dict, Optional

import grpc

from deck.grpc.server import DEFAULT_PORT
from generated.deck.proto import deck_service_pb2, deck_service_pb2_grpc


class DeckGrpcClient:
    def __init__(self, hostname: str = "localhost", port: int = DEFAULT_PORT):
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stub: Optional[deck_service_pb2_grpc.DeckServiceStub] = None

    def _maybe_connect(self) -> None:
        if self._stub is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
            self._stub = deck_service_pb2_grpc.DeckServiceStub(self._channel)

    async def ping(self, x: int = 42) -> int:
        self._maybe_connect()
        assert self._stub is not None
        req = deck_service_pb2.PingMsg(x=x)
        response: deck_service_pb2.PongMsg = await self._stub.Ping(req)
        return int(response.x)

    async def SetConfigs(self, camera: str, configs: Dict[str, Any]) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = deck_service_pb2.SetConfigsRequest(camera=camera)
        for key, value in configs.items():
            if isinstance(value, bool):
                req.configs.append(deck_service_pb2.Config(key=key, bool_val=value))
            elif isinstance(value, int):
                req.configs.append(deck_service_pb2.Config(key=key, int_val=value))
            elif isinstance(value, float):
                req.configs.append(deck_service_pb2.Config(key=key, float_val=value))
            elif isinstance(value, str):
                req.configs.append(deck_service_pb2.Config(key=key, str_val=value))
            else:
                raise NotImplementedError(f"set configs does not support type={type(value)} being used for key={key}")
        await self._stub.SetConfigs(req)


def interactive(hostname: str, port: int) -> None:
    from lib.common.asyncio.repl import start

    client = DeckGrpcClient(hostname, port)
    imports = {
        "client": client,
    }
    start(imports)


def main() -> None:
    parser = ArgumentParser("Deck GRPC client")
    parser.add_argument("--hostname", type=str, default="localhost", help="deck grpc service host")
    parser.add_argument("-p", "--port", type=int, default=DEFAULT_PORT, help="deck grpc service port")
    args = parser.parse_args()
    interactive(args.hostname, args.port)


if __name__ == "__main__":
    main()

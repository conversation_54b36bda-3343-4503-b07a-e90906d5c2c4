import asyncio
from typing import Any, Optional, Protocol

import grpc

from generated.deck.proto import deck_service_pb2, deck_service_pb2_grpc
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import LoggingGRPCServicer, get_logger

LOG = get_logger(__name__)
DEFAULT_PORT = 61014


class CameraOwner(Protocol):
    def camera_exists(self, camera: str) -> bool:
        ...

    def set_camera_config(self, camera: str, key: str, value: Any) -> None:
        ...

    def get_camera_config(self, camera: str, key: str) -> Any:
        ...

    def get_camera_config_float(self, camera: str, key: str) -> Optional[float]:
        ...

    def get_camera_config_int(self, camera: str, key: str) -> Optional[int]:
        ...

    def get_camera_config_str(self, camera: str, key: str) -> Optional[str]:
        ...

    def get_camera_config_bool(self, camera: str, key: str) -> Optional[bool]:
        ...


class GrpcServicer(deck_service_pb2_grpc.DeckServiceServicer):
    def __init__(self, server: grpc.aio.server, camera_owner: CameraOwner):
        deck_service_pb2_grpc.add_DeckServiceServicer_to_server(self, server)
        self._camera_owner = camera_owner

    async def SetConfigs(
        self, request: deck_service_pb2.SetConfigsRequest, context: grpc.ServicerContext
    ) -> deck_service_pb2.SetConfigsResponse:
        context.set_code(grpc.StatusCode.OK)
        if not self._camera_owner.camera_exists(request.camera):
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(f"Camera {request.camera} is not available")
            return deck_service_pb2.SetConfigsResponse()
        errors = []
        for cfg in request.configs:
            try:
                if cfg.WhichOneof("value") == "bool_val":
                    self._camera_owner.set_camera_config(request.camera, cfg.key, cfg.bool_val)
                elif cfg.WhichOneof("value") == "int_val":
                    self._camera_owner.set_camera_config(request.camera, cfg.key, cfg.int_val)
                elif cfg.WhichOneof("value") == "float_val":
                    self._camera_owner.set_camera_config(request.camera, cfg.key, cfg.float_val)
                elif cfg.WhichOneof("value") == "str_val":
                    self._camera_owner.set_camera_config(request.camera, cfg.key, cfg.str_val)
                else:
                    errors.append(f"Unknown config type key={cfg.key} for camera {request.camera}")
                    LOG.error(errors[-1])
                    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            except Exception:
                errors.append(f"Failed to set cfg {cfg.key} for {request.camera}")
                LOG.exception(errors[-1])
                context.set_code(grpc.StatusCode.INTERNAL)
        if errors:
            context.set_details("\n".join(errors))
        return deck_service_pb2.SetConfigsResponse()

    async def GetConfig(
        self, request: deck_service_pb2.GetConfigRequest, context: grpc.ServicerContext
    ) -> deck_service_pb2.GetConfigResponse:
        context.set_code(grpc.StatusCode.OK)
        if not self._camera_owner.camera_exists(request.camera):
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(f"Camera {request.camera} is not available")
            return deck_service_pb2.GetConfigResponse()
        if request.type == "int":
            int_val = self._camera_owner.get_camera_config_int(request.camera, request.key)
            return deck_service_pb2.GetConfigResponse(config=deck_service_pb2.Config(key=request.key, int_val=int_val))
        elif request.type == "bool":
            bool_val = self._camera_owner.get_camera_config_bool(request.camera, request.key)
            return deck_service_pb2.GetConfigResponse(
                config=deck_service_pb2.Config(key=request.key, bool_val=bool_val)
            )
        elif request.type == "float":
            float_val = self._camera_owner.get_camera_config_float(request.camera, request.key)
            return deck_service_pb2.GetConfigResponse(
                config=deck_service_pb2.Config(key=request.key, float_val=float_val)
            )
        elif request.type == "str":
            str_val = self._camera_owner.get_camera_config_str(request.camera, request.key)
            return deck_service_pb2.GetConfigResponse(config=deck_service_pb2.Config(key=request.key, str_val=str_val))
        else:
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            context.set_details(f"Unknown config type key={request.key} for camera {request.camera}")
        return deck_service_pb2.GetConfigResponse()

    async def Ping(self, request: deck_service_pb2.PingMsg, context: grpc.ServicerContext) -> deck_service_pb2.PongMsg:
        context.set_code(grpc.StatusCode.OK)
        return deck_service_pb2.PongMsg(x=request.x)


class GrpcServer:
    def __init__(self, camera_owner: CameraOwner, port: int = DEFAULT_PORT):
        self._port = port
        self._server = grpc.aio.server()
        self._servicer = GrpcServicer(self._server, camera_owner=camera_owner)
        self._loggingServicer = LoggingGRPCServicer(self._server, "deck.log")
        self._server.add_insecure_port(f"[::]:{self._port}")
        self._started = False
        bot_stop_handler.add_callback(self.shutdown)

    @property
    def server(self) -> grpc.aio.Server:
        return self._server

    async def start(self) -> None:
        if self._started:
            return
        self._loop = asyncio.get_event_loop()
        await self._server.start()
        LOG.info(f"Started deck grpc at 0.0.0.0:{self._port}")
        self._started = True

    def shutdown(self) -> None:
        if not self._started:
            return
        asyncio.run_coroutine_threadsafe(self._server.stop(0), self._loop)
        self._started = False

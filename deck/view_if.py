from typing import Optional

from deck import messages as msg
from deck.view_manager import <PERSON>Manager
from lib.common.messaging.message import msg_decoder
from lib.rtc.data_provider.data_bus_client import AUTH_READ_REQ, AUTH_WRITE_REQ, DATA_BUS
from lib.rtc.messaging.message import ErrorMsg, Message


class ViewIF:
    def __init__(self, manager: ViewManager) -> None:
        self._mgr = manager
        DATA_BUS.register(msg.MessageType.LIST_CAMS_REQ, self._list_cams, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.LIST_VIEWS_REQ, self._list_views, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.GET_VIEW_REQ, self._get_view, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.GET_WIN_REQ, self._get_window, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.LIST_WIN_FOR_CAM_REQ, self._list_windows_for_cam, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.TAG_VIEW_REQ, self._tag_view, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.GET_VIEW_FOR_TAG_REQ, self._get_view_for_tag, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.STORE_VIEW_REQ, self._store_view, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.STORE_WIN_REQ, self._store_window, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.SET_VIEW_ACTIVE_REQ, self._set_active, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.GET_DEF_VIEW_REQ, self._get_default_view, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.RESET_VIEW_REQ, self._reset_view, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.DEL_VIEW_REQ, self._del_view, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.RESET_WIN_REQ, self._reset_win, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.DEL_WIN_REQ, self._del_win, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.LIST_WIN_REQ, self._list_windows, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.LIST_TAG_REQ, self._list_tags, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.GET_VIEWPORT_DATA_REQ, self._get_output_data, AUTH_READ_REQ)
        DATA_BUS.register(msg.MessageType.SET_VIEW_TAGS_REQ, self._set_tags_for_view, AUTH_WRITE_REQ)
        DATA_BUS.register(msg.MessageType.SET_WHEEL_POS_REQ, self._set_wheel_pos, AUTH_WRITE_REQ)

    @msg.msg_sender
    async def _list_cams(self, _: Message) -> msg.ListCamResp:
        return msg.ListCamResp(cameras=self._mgr.cameras)

    @msg.msg_sender
    async def _list_views(self, _: Message) -> msg.ListViewsResp:
        views = self._mgr.get_views()
        meta = {id: view.metadata for id, view in views.items()}
        active = self._mgr.get_active_view()
        return msg.ListViewsResp(views=meta, active=active)

    @msg.msg_sender
    @msg_decoder(msg.IDMsg)
    async def _get_view(self, message: msg.IDMsg) -> msg.FullyDefinedView:
        return self._mgr.get_fully_defined_view(message.id)

    @msg.msg_sender
    @msg_decoder(msg.IDMsg)
    async def _get_window(self, message: msg.IDMsg) -> msg.GetWinResp:
        windows = self._mgr.get_available_windows()
        win = None
        if message.id in windows:
            win = windows[message.id]
        return msg.GetWinResp(window=win)

    @msg.msg_sender
    @msg_decoder(msg.IDMsg)
    async def _list_windows_for_cam(self, message: msg.IDMsg) -> msg.ListWinCamResp:
        windows = self._mgr.get_windows_for_camera(message.id)
        meta = {id: win.metadata for id, win in windows.items()}
        return msg.ListWinCamResp(windows=meta)

    @msg.empty_sender
    @msg_decoder(msg.TagViewReq)
    async def _tag_view(self, message: msg.TagViewReq) -> Optional[ErrorMsg]:
        self._mgr.set_view_for_tag(message.tag, message.id)
        return None

    @msg.empty_sender
    @msg_decoder(msg.SetViewTags)
    async def _set_tags_for_view(self, message: msg.SetViewTags) -> Optional[ErrorMsg]:
        return self._mgr.set_tags_for_view(message.view_id, message.tags)

    @msg.msg_sender
    @msg_decoder(msg.IDMsg)
    async def _get_view_for_tag(self, message: msg.IDMsg) -> msg.FullyDefinedView:
        view_id = self._mgr.get_view_for_tag(message.id)
        if view_id is None:
            raise KeyError(f"No view id found for {message.id}")
        return self._mgr.get_fully_defined_view(view_id)

    @msg.msg_sender
    @msg_decoder(msg.StoreViewReq)
    async def _store_view(self, message: msg.StoreViewReq) -> msg.IDMsg:
        id = self._mgr.add_view(message.view, message.save)
        return msg.IDMsg(id=id)

    @msg.msg_sender
    @msg_decoder(msg.StoreWinReq)
    async def _store_window(self, message: msg.StoreWinReq) -> msg.IDMsg:
        id = self._mgr.add_window(message.window, message.save)
        return msg.IDMsg(id=id)

    @msg.empty_sender
    @msg_decoder(msg.IDMsg)
    async def _set_active(self, message: msg.IDMsg) -> Optional[ErrorMsg]:
        return self._mgr.set_view_active(message.id)

    @msg.msg_sender
    @msg_decoder(msg.IDMsg)
    async def _get_default_view(self, message: msg.IDMsg) -> msg.FullyDefinedView:
        view, windows = self._mgr.build_default_view(message.id)
        return msg.FullyDefinedView(view=view, windows=windows)

    @msg.empty_sender
    @msg_decoder(msg.IDMsg)
    async def _reset_view(self, message: msg.IDMsg) -> Optional[ErrorMsg]:
        return await self._mgr.reset_view(message.id)

    @msg.empty_sender
    @msg_decoder(msg.IDMsg)
    async def _del_view(self, message: msg.IDMsg) -> Optional[ErrorMsg]:
        return self._mgr.delete_view(message.id)

    @msg.empty_sender
    @msg_decoder(msg.IDMsg)
    async def _reset_win(self, message: msg.IDMsg) -> Optional[ErrorMsg]:
        return await self._mgr.reset_window(message.id)

    @msg.msg_sender
    @msg_decoder(msg.MultiID)
    async def _del_win(self, message: msg.MultiID) -> msg.MultiStatusResp:
        status = self._mgr.delete_windows(message.ids)
        return msg.MultiStatusResp(statuses=status)

    @msg.msg_sender
    async def _list_windows(self, _: Message) -> msg.ListWinResp:
        windows = self._mgr.get_available_windows()
        return msg.ListWinResp(windows=list(windows.values()))

    @msg.msg_sender
    async def _list_tags(self, _: Message) -> msg.MultiID:
        tags = self._mgr.list_tags()
        return msg.MultiID(ids=tags)

    @msg.msg_sender
    async def _get_output_data(self, _: Message) -> msg.OutputConfig:
        return self._mgr.output_config

    @msg.empty_sender
    @msg_decoder(msg.WheelPosMsg)
    async def _set_wheel_pos(self, message: msg.WheelPosMsg) -> Optional[ErrorMsg]:
        return self._mgr.set_wheel_pos(message.camera, message.pos)

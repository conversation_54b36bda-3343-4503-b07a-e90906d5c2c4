package main

import (
	"context"
	"errors"
	"fmt"
	"io/fs"
	"net/http"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	crerr "github.com/carbonrobotics/robot/golang/lib/errors"
	crlog "github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

const (
	npzExt  = ".npz"
	pngExt  = ".png"
	metaExt = ".metadata.json"
	zipExt  = ".zip"
)

var (
	uploaderFs = afero.NewOsFs()
	timeNow    = time.Now
)

type RobotClient struct {
	HttpClient  *http.Client
	Environment environment.Robot
}

var banner = strings.ReplaceAll(`
  __  __     __             __       
 / / / /__  / /__  ___ ____/ /__ ____
/ /_/ / _ \/ / _ \/ _ q/ _  / -_) __/
\____/ .__/_/\___/\_,_/\_,_/\__/_/
    /_/`, "q", "`")

type Uploader struct {
	DisableConfig         bool          `envconfig:"DISABLE_CONFIG"`
	MetricsPort           int           `envconfig:"METRICS_PORT" default:"62012"`
	UploadDirectory       string        `envconfig:"UPLOAD_DIRECTORY" required:"true"`
	UploadInterval        time.Duration `envconfig:"UPLOAD_INTERVAL" default:"5m"`
	FailedUploadDirectory string        `envconfig:"FAILED_UPLOAD_DIRECTORY" required:"true"`
	FailedUploadLifetime  time.Duration `envconfig:"FAILED_UPLOAD_LIFETIME" default:"72h"`
	CleanupInterval       time.Duration `envconfig:"CLEANUP_INTERVAL" default:"1h"`
	MaxHourlyUploads      int           `envconfig:"MAX_HOURLY_UPLOADS" default:"5"`
	OauthTokenURL         string        `envconfig:"OAUTH_TOKEN_URL" default:"http://127.0.0.1:61900/oauth/token"`
	HttpClientTimeout     time.Duration `envconfig:"HTTP_CLIENT_TIMEOUT" default:"5m"`
	configNode            config.Tree

	robotClient RobotClient
	logger      *logrus.Logger
	wg          sync.WaitGroup
	tzLocation  *time.Location
	cfg         *Configurator
}

func (u *Uploader) configure() error {
	u.logger = logrus.New()
	u.logger.SetFormatter(crlog.DefaultLogFormat)

	if err := envconfig.Process("", u); err != nil {
		return fmt.Errorf("failed to parse environment: %w", err)
	}
	if !u.DisableConfig {
		configurator.Start()
		u.logger.SetLevel(configurator.LogLevel())
		u.UploadInterval = configurator.UploadInterval(u.UploadInterval)
		u.FailedUploadLifetime = configurator.FailedUploadLifetime(u.FailedUploadLifetime)
		u.MaxHourlyUploads = configurator.MaxHourlyUploads()
	}
	return nil
}

func (u *Uploader) Run(ctx context.Context) error {
	fmt.Println(banner)
	if err := u.configure(); err != nil {
		return err
	}
	env, err := environment.GetRobot()
	if err != nil {
		return fmt.Errorf("failed to parse robot environment: %w", err)
	}
	a0cfg := auth.NewRobotAuth0Config(env, env.MakaAuthScopes, u.OauthTokenURL)
	httpClient := a0cfg.AuthenticatedHttpClient(ctx)
	httpClient.Timeout = u.HttpClientTimeout
	u.robotClient = RobotClient{HttpClient: httpClient, Environment: env}

	uploaderFs.MkdirAll(u.UploadDirectory, 0755)
	uploaderFs.MkdirAll(u.FailedUploadDirectory, 0755)

	u.logger.Infoln("starting up...")
	// subsystems
	u.wg.Add(1)
	go u.backgroundProcessor(ctx)
	metrics.Serve("", u.MetricsPort)

	<-ctx.Done()
	u.logger.Infoln("shutting down")
	u.wg.Wait()
	return nil
}

func (u *Uploader) backgroundProcessor(ctx context.Context) {
	defer u.wg.Done()

	uploadTicker := time.NewTicker(u.UploadInterval)
	cleanupTicker := time.NewTicker(u.CleanupInterval)
	defer func() {
		uploadTicker.Stop()
		cleanupTicker.Stop()
	}()

	meteredHour := timeNow().Hour()
	hourlySuccessUploads := 0
	hourlyFailedUploads := 0
	for {
		select {
		case <-ctx.Done():
			return

		case <-uploadTicker.C:
			currentHour := timeNow().Hour()
			if meteredHour != currentHour {
				meteredHour = currentHour
				hourlySuccessUploads = 0
				hourlyFailedUploads = 0
			}

			u.logger.Infoln("scanning", u.UploadDirectory, "for potential uploads")
			pairs, err := findArtifactPairs(u.UploadDirectory, npzExt, pngExt, zipExt)
			if err != nil {
				u.logger.WithError(err).Warnln("failed to find artifacts")
				continue
			}
			if len(pairs) > 0 {
				u.logger.Infoln("found", len(pairs), "artifact pairs")
			}
			for _, pair := range pairs {
				if hourlySuccessUploads >= u.MaxHourlyUploads {
					u.logger.Warnf("max uploads reached (%d/%d) for curent hour", hourlySuccessUploads, u.MaxHourlyUploads)
					break
				}
				u.logger.Infoln("attempting upload of meta:", pair.MetaFilepath(), "artifact:", pair.ArtifactFilepath())
				err = u.upload(ctx, pair.MetaFilepath(), pair.ArtifactFilepath())
				if err != nil {
					u.logger.WithError(err).Warnln("failed to upload artifact")
					hourlyFailedUploads++
				} else {
					hourlySuccessUploads++
				}
			}
			u.logger.Infof("current hour uploads (%d/%d) suceeded", hourlySuccessUploads, hourlySuccessUploads+hourlyFailedUploads)

		case <-cleanupTicker.C:
			if err := cleanupFiles(u.FailedUploadDirectory, u.FailedUploadLifetime); err != nil {
				u.logger.WithError(err).Warnln("failed to cleanup files")
			}
		}
	}
}

func (u *Uploader) upload(ctx context.Context, metaFilepath, artifactFilepath string) (err error) {
	defer func() {
		if err != nil {
			_, metaFilename := filepath.Split(metaFilepath)
			failedMetaFilepath := filepath.Join(u.FailedUploadDirectory, metaFilename)
			if err := uploaderFs.Rename(metaFilepath, failedMetaFilepath); err != nil {
				u.logger.WithError(err).Errorln("failed to move meta file:", metaFilepath, "->", failedMetaFilepath)
			}
			_, artifactFilename := filepath.Split(artifactFilepath)
			failedArtifactFilepath := filepath.Join(u.FailedUploadDirectory, artifactFilename)
			if err := uploaderFs.Rename(artifactFilepath, filepath.Join(u.FailedUploadDirectory, artifactFilename)); err != nil {
				u.logger.WithError(err).Errorln("failed to move artifact file:", artifactFilepath, "->", failedArtifactFilepath)
			}
		} else {
			if err := uploaderFs.Remove(metaFilepath); err != nil {
				u.logger.WithError(err).Errorln("failed to remove meta file:", metaFilepath)
			}
			if err := uploaderFs.Remove(artifactFilepath); err != nil {
				u.logger.WithError(err).Errorln("failed to remove artifact file:", metaFilepath)
			}
		}
	}()

	if err := u.metaUpload(ctx, metaFilepath); err != nil {
		// 409 error is when files already exist, and we should consider upload success
		var uerr *crerr.UploadError
		if errors.As(err, &uerr) && uerr.Code() == 409 {
			u.logger.Warnln("upload of", metaFilepath, "aborted with status 409, files already uploaded")
			return nil
		}
		return fmt.Errorf("failed to upload meta file %s - %w", metaFilepath, err)
	}
	if err := u.chunkedArtifactUpload(ctx, artifactFilepath); err != nil {
		return fmt.Errorf("failed to upload artifact file %s - %w", artifactFilepath, err)
	}
	return nil
}

func cleanupFiles(dir string, fileLifetime time.Duration) error {
	return afero.Walk(uploaderFs, dir,
		func(path string, info fs.FileInfo, err error) error {
			if err != nil {
				return err
			}
			info.ModTime()
			if !info.IsDir() && time.Since(info.ModTime()) >= fileLifetime {
				if err := uploaderFs.Remove(path); err != nil {
					return err
				}
			}
			return nil
		},
	)
}

type artifactPair struct {
	directory        string
	metaFilename     string
	artifactFilename string
}

func (p *artifactPair) MetaFilepath() string {
	return filepath.Join(p.directory, p.metaFilename)
}

func (p *artifactPair) ArtifactFilepath() string {
	return filepath.Join(p.directory, p.artifactFilename)
}

func findArtifactPairs(targetPath string, artifactFileExt ...string) ([]*artifactPair, error) {
	possibleArtifacts := make(map[string]string) // map of artifact path to base path (/dir/foo/artifact.png -> /dir/foo/artifact)
	possibleMetadata := make(map[string]bool)

	err := afero.Walk(uploaderFs, targetPath, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			return err
		}
		for _, ext := range artifactFileExt {
			if strings.HasSuffix(path, ext) {
				possibleArtifacts[path] = strings.TrimSuffix(path, ext)
			}
		}
		if strings.HasSuffix(path, metaExt) {
			possibleMetadata[path] = true
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	pairs := make([]*artifactPair, 0)
	for artifact, base := range possibleArtifacts {
		metaFile := base + metaExt
		dir, artifactFilename := filepath.Split(artifact)
		if _, ok := possibleMetadata[metaFile]; ok {
			_, metaFilename := filepath.Split(metaFile)
			pairs = append(pairs, &artifactPair{
				directory:        dir,
				metaFilename:     metaFilename,
				artifactFilename: artifactFilename,
			})
		}
	}
	return pairs, nil
}

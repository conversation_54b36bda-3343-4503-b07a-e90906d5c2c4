package main

import (
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/sirupsen/logrus"
)

var configurator = &Configurator{}

type Configurator struct {
	subscriber *config.ConfigSubscriber
}

func (c *Configurator) Start() {
	c.subscriber = config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	c.subscriber.AddConfigTree("uploader", fmt.Sprintf("%s/uploader", config.GetComputerConfigPrefix()), "services/uploader.yaml")
	c.subscriber.Start()
	logrus.Infoln("waiting for config...")
	c.subscriber.WaitUntilReady()
}

func (c *Configurator) LogLevel() logrus.Level {
	levelStr := c.subscriber.GetConfigNode("uploader", "log_level").GetStringValue()
	level, err := logrus.ParseLevel(levelStr)
	if err != nil {
		return logrus.InfoLevel
	}
	return level
}

func (c *Configurator) UploadInterval(def time.Duration) time.Duration {
	str := c.subscriber.GetConfigNode("uploader", "upload_interval").GetStringValue()
	return parseDurationWithDefault(str, def)
}

func (c *Configurator) FailedUploadLifetime(def time.Duration) time.Duration {
	str := c.subscriber.GetConfigNode("uploader", "failed_upload_lifetime").GetStringValue()
	return parseDurationWithDefault(str, def)
}

func (c *Configurator) MaxHourlyUploads() int {
	return int(c.subscriber.GetConfigNode("uploader", "max_hourly_uploads").GetIntValue())
}

func parseDurationWithDefault(durationStr string, def time.Duration) time.Duration {
	dur, err := time.ParseDuration(durationStr)
	if err != nil {
		return def
	}
	return dur
}

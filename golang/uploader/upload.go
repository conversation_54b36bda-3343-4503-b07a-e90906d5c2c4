package main

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"net/url"
	"path/filepath"
	"strconv"
	"time"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	crerr "github.com/carbonrobotics/robot/golang/lib/errors"
	"github.com/spf13/afero"
)

const (
	chunkSize             uint64 = 5 << 20 // 5MB
	uploadCompletionToken        = "EOF"
	ingestChunkedEndpoint        = "/robot/chunked"
)

func (u *Uploader) metaUpload(ctx context.Context, fileName string) error {
	start := time.Now()
	_, fName := filepath.Split(fileName)
	contents, err := afero.ReadFile(uploaderFs, fileName)
	if err != nil {
		return err
	}
	body := &bytes.Buffer{}
	w := multipart.NewWriter(body)

	fileWriter, err := w.CreateFormFile("metadata", fName)
	if err != nil {
		return err
	}
	if _, err := fileWriter.Write(contents); err != nil {
		return err
	}
	w.Close()

	targetURL := &url.URL{
		Scheme: "https",
		Host:   u.robotClient.Environment.CarbonIngestHost,
		Path:   ingestChunkedEndpoint,
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, targetURL.String(), body)
	if err != nil {
		return err
	}
	req.Header.Set("User-Agent", fmt.Sprintf("robot-uploader-%s", u.robotClient.Environment.MakaRobotName))
	req.Header.Set("Content-Type", w.FormDataContentType())
	req.Header.Set("Content-MD5", md5sum(contents))
	req.Header.Set(carbon.RobotHTTPHeader, u.robotClient.Environment.MakaRobotName)
	req.Header.Set(carbon.GenerationHTTPHeader, u.robotClient.Environment.MakaGen)
	req.ContentLength = -1

	resp, err := u.robotClient.HttpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	duration := time.Since(start)
	u.logger.Infof("Request status: %s, %s, duration_ms: %s, meta %s", resp.Status, string(b), duration, fileName)

	if resp.StatusCode != http.StatusOK {
		return crerr.NewUploadError(resp.StatusCode, fmt.Sprintf("Request error %v", resp.Status))
	}

	return nil
}

func (u *Uploader) chunkedArtifactUpload(ctx context.Context, fileName string) error {
	f, err := uploaderFs.Open(fileName)
	if err != nil {
		return err
	}
	defer f.Close()

	for partNumber := 1; ; partNumber++ {
		end, err := u.uploadChunk(ctx, f, fileName, partNumber)
		if err != nil {
			return err
		}
		if end {
			return nil
		}
	}
}

func (u *Uploader) uploadChunk(ctx context.Context, f afero.File, fileName string, partNumber int) (bool, error) {
	partStr := strconv.Itoa(partNumber)
	buf := make([]byte, chunkSize)
	bytesRead, err := f.Read(buf)
	if err != nil {
		if errors.Is(err, io.EOF) {
			partStr = uploadCompletionToken // special last part to notify for completion
		} else {
			return false, err
		}
	}
	data := buf[:bytesRead]

	body := &bytes.Buffer{}
	contentType, err := addBodyPart(body, data, fileName, partStr)
	if err != nil {
		return false, err
	}

	targetURL := &url.URL{
		Scheme: "https",
		Host:   u.robotClient.Environment.CarbonIngestHost,
		Path:   ingestChunkedEndpoint,
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, targetURL.String(), body)
	if err != nil {
		return false, err
	}

	req.Header.Set("User-Agent", fmt.Sprintf("robot-uploader-%s", u.robotClient.Environment.MakaRobotName))
	req.Header.Set("Content-Type", contentType)
	req.Header.Set("Content-MD5", md5sum(data))
	req.Header.Set(environment.RobotHTTPHeader, u.robotClient.Environment.MakaRobotName)
	req.Header.Set(environment.GenerationHTTPHeader, u.robotClient.Environment.MakaGen)
	start := time.Now()
	resp, err := u.robotClient.HttpClient.Do(req)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		return false, errors.New(fmt.Sprintf("Request error %s: %s", resp.Status, b))
	}
	duration := time.Since(start)
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}
	u.logger.Infof("Request status: %s, %s, duration_ms: %s, file: %s (chunk: %s)", resp.Status, string(b), duration, fileName, partStr)
	if partStr == uploadCompletionToken {
		return true, nil
	}
	return false, nil
}

func addBodyPart(body io.Writer, data []byte, filename string, partNumber string) (string, error) {
	w := multipart.NewWriter(body)
	defer w.Close()
	h := make(textproto.MIMEHeader)
	field := fmt.Sprintf("chunk-%s", partNumber)
	h.Set("Content-Disposition", fmt.Sprintf(`form-data; name=%q; filename=%q`, field, filename))
	part, err := w.CreatePart(h)
	if err != nil {
		return "", err
	}
	if _, err := part.Write(data); err != nil {
		return "", err
	}
	return w.FormDataContentType(), nil
}

func md5sum(data []byte) string {
	h := md5.New()
	h.Write(data)
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

.NOTPARALLEL:
.PHONY: all unit_tests clean configure_git

all: configure_git
	make -C ../ cmake_configure
	make -C ../build/golang/ -j$(shell nproc) all

configure_git:
ifeq ($(GITHUB_TOKEN),)
	git config --global url."ssh://**************/".insteadOf "https://github.com/"
else
	git config --global url."https://$(GITHUB_TOKEN):<EMAIL>/".insteadOf "https://github.com/"
endif

unit_tests: configure_git
	go test -cover $(shell go list -buildvcs=false ./... | grep -v isobus-vt)

clean:
	rm -rf generated

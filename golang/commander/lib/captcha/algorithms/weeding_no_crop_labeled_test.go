package captcha

import (
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWeedingNoCropLabeledAlgorithm(t *testing.T) {
	tests := []struct {
		name                     string
		minRecommenededThreshold float32
	}{
		{"happy path", float32(0.0)},
		{"min threshold set", float32(0.5)},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctx, captcha, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.CaptchaResults = GenerateCaptchaResults(true, true, true)
			results.UseWeedCategoriesForWeedLabels = true
			results.MinRecommendedWeedThreshold = test.minRecommenededThreshold
			a := &CategorySizeWeedingNoCropLabeledAlgorithm{}
			captchaSubmissionResult, _ := a.Submit(ctx, captcha, results)

			categories := captchaSubmissionResult.ModelinatorConfig.GetCategories()

			assert.True(t, captchaSubmissionResult.Succeeded)
			assert.Equal(t, captchaSubmissionResult.ModelinatorConfig.GetModelId(), "model1")
			assert.Equal(t, captchaSubmissionResult.ModelinatorConfig.GetCropId(), "CROP")
			assert.Equal(t, len(categories), 3)
			for _, param := range categories {
				if param.Type.Classification == almanac.CategoryClassification_CATEGORY_CROP {
					assert.True(t, param.Trusts[0].WeedingThreshold == float32(1.0))
					assert.True(t, param.Trusts[0].MinDoo == float32(1.0))
					assert.True(t, param.Trusts[1].WeedingThreshold == float32(1.0))
					assert.True(t, param.Trusts[1].MinDoo == float32(1.0))
					assert.True(t, param.Trusts[2].WeedingThreshold == float32(1.0))
					assert.True(t, param.Trusts[2].MinDoo == float32(1.0))
				} else if param.Type.Category == "GRASS" {
					assert.True(t, param.Trusts[0].WeedingThreshold == max(float32(0.3), test.minRecommenededThreshold))
					assert.True(t, param.Trusts[0].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[1].WeedingThreshold == max(float32(0.6), test.minRecommenededThreshold))
					assert.True(t, param.Trusts[1].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[2].WeedingThreshold == float32(0.56))
					assert.True(t, param.Trusts[2].MinDoo == float32(0.0))
				} else if param.Type.Category == "BROADLEAF" {
					assert.True(t, param.Trusts[0].WeedingThreshold == float32(0.34))
					assert.True(t, param.Trusts[0].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[1].WeedingThreshold == max(float32(0.6), test.minRecommenededThreshold))
					assert.True(t, param.Trusts[1].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[2].WeedingThreshold == float32(0.56))
					assert.True(t, param.Trusts[2].MinDoo == float32(0.0))
				} else {
					assert.True(t, false)
				}
			}
		})
	}
}

func TestOtherMethodsUseNoCropLabeledIfNoCropLabeled(t *testing.T) {
	tests := []struct {
		name                   string
		minRecommendedThresold float32
		algorithm              PlantCaptchaAlgorithm
	}{
		{"weeding", float32(0.5), &CategorySizeWeedingPlantCaptchaAlgorithm{}},
		{"weeding_exhaustive", float32(0.5), &CategorySizeWeedingExhaustivePlantCaptchaAlgorithm{}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctx, captcha, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.CaptchaResults = GenerateCaptchaResultsNoCropLabels()
			results.UseWeedCategoriesForWeedLabels = true
			results.MinRecommendedWeedThreshold = test.minRecommendedThresold
			captchaSubmissionResult, _ := test.algorithm.Submit(ctx, captcha, results)

			categories := captchaSubmissionResult.ModelinatorConfig.GetCategories()

			assert.True(t, captchaSubmissionResult.Succeeded)
			assert.Equal(t, captchaSubmissionResult.ModelinatorConfig.GetModelId(), "model1")
			assert.Equal(t, captchaSubmissionResult.ModelinatorConfig.GetCropId(), "CROP")
			assert.Equal(t, len(categories), 3)
			for _, param := range categories {
				if param.Type.Classification == almanac.CategoryClassification_CATEGORY_CROP {
					assert.True(t, param.Trusts[0].WeedingThreshold == float32(1.0))
					assert.True(t, param.Trusts[0].MinDoo == float32(1.0))
					assert.True(t, param.Trusts[1].WeedingThreshold == float32(1.0))
					assert.True(t, param.Trusts[1].MinDoo == float32(1.0))
					assert.True(t, param.Trusts[2].WeedingThreshold == float32(1.0))
					assert.True(t, param.Trusts[2].MinDoo == float32(1.0))
				} else if param.Type.Category == "GRASS" {
					assert.True(t, param.Trusts[0].WeedingThreshold == float32(0.34))
					assert.True(t, param.Trusts[0].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[1].WeedingThreshold == max(float32(0.6), test.minRecommendedThresold))
					assert.True(t, param.Trusts[1].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[2].WeedingThreshold == float32(0.56))
					assert.True(t, param.Trusts[2].MinDoo == float32(0.0))
				} else if param.Type.Category == "BROADLEAF" {
					assert.True(t, param.Trusts[0].WeedingThreshold == max(float32(0.2), test.minRecommendedThresold))
					assert.True(t, param.Trusts[0].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[1].WeedingThreshold == max(float32(0.2), test.minRecommendedThresold))
					assert.True(t, param.Trusts[1].MinDoo == float32(0.0))
					assert.True(t, param.Trusts[2].WeedingThreshold == float32(0.56))
					assert.True(t, param.Trusts[2].MinDoo == float32(0.0))
				} else {
					assert.True(t, false)
				}
			}
		})
	}
}

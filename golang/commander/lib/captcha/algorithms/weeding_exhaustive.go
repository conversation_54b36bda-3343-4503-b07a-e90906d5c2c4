package captcha

import (
	"context"
	"math"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
)

type ConfigurationTracker struct {
	bestCropWeedConfigurations map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues
	mu                         sync.Mutex
}

func NewConfigurationTracker() ConfigurationTracker {
	configTracker := ConfigurationTracker{
		bestCropWeedConfigurations: make(map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues),
	}

	return configTracker
}

func (c *ConfigurationTracker) add(cropKey CropConfigurationKey, weedKey WeedConfigurationKey, performanceValues WeedPerformanceValues, results *frontend.PlantCaptchaResults) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if _, ok := c.bestCropWeedConfigurations[cropKey]; !ok {
		c.bestCropWeedConfigurations[cropKey] = map[WeedConfigurationKey]WeedPerformanceValues{}
	}

	bestItem, ok := c.bestCropWeedConfigurations[cropKey][weedKey]

	if !ok {
		c.bestCropWeedConfigurations[cropKey][weedKey] = performanceValues
	} else {
		weedsTargeted := float32(len(performanceValues.weedsTargeted)) / float32(len(performanceValues.weedsTotal))
		bestWeedsTargeted := float32(len(bestItem.weedsTargeted)) / float32(len(bestItem.weedsTotal))

		replace := weedsTargeted >= bestWeedsTargeted
		if results.UseOtherAsTiebreaker {
			replace = weedsTargeted > bestWeedsTargeted
			if weedsTargeted == bestWeedsTargeted {
				if len(performanceValues.othersTotal) == 0 {
					replace = true
				} else {
					othersTargeted := float32(len(performanceValues.othersTargeted)) / (float32(len(performanceValues.othersTotal)) + 1e-6)
					bestOthersTargeted := float32(len(bestItem.othersTargeted)) / (float32(len(bestItem.othersTotal)) + 1e-6)
					replace = othersTargeted < bestOthersTargeted
				}
			}
		}

		if replace {
			c.bestCropWeedConfigurations[cropKey][weedKey] = performanceValues
		}
	}
}

func (c *ConfigurationTracker) retrieve() map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.bestCropWeedConfigurations
}

type CategorySizeWeedingExhaustivePlantCaptchaAlgorithm struct{}

func (s *CategorySizeWeedingExhaustivePlantCaptchaAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	start := time.Now()
	captchaResults, labelCounts, categoryToSizes := splitCaptchasCategorySize(results)

	percentageLimits := getPercentageLimits(results)

	modelinator := results.GetCurrentParameters()

	weedCount := labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED)
	cropCount := labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP)

	if cropCount == 0 {
		if weedCount > 0 {
			logrus.Error("Weeds labeled but no crops, using weeding_no_crop_labeled mode")
			return (&CategorySizeWeedingNoCropLabeledAlgorithm{}).Submit(ctx, plantCaptcha, results)
		} else {
			logrus.Error("Could not calculate weeding recommendation, missing either weed labels and crop labels")
			return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NOT_ENOUGH_ITEMS}, nil
		}
	}

	cropConfigurations := make([]map[string]ThresholdMindooMetrics, 0)

	allCrops := getAllOfPredictionType(captchaResults, weed_tracking.PlantCaptchaUserPrediction_CROP)
	allOthers := getAllOfPredictionType(captchaResults, weed_tracking.PlantCaptchaUserPrediction_OTHER)

	cropSizes := categoryToSizes.get("CROP")
	cropSize1 := cropSizes[0]
	cropSize2 := cropSizes[1]

	// Build out crop configurations
	for smallThreshPercentage := percentageLimits.minCropThresholdPercentage; smallThreshPercentage <= percentageLimits.maxCropThresholdPercentage; smallThreshPercentage += 10 {
		for smallMindooPercentage := percentageLimits.minMindooPercentage; smallMindooPercentage <= percentageLimits.maxMindooPercentage; smallMindooPercentage += 20 {
			for mediumThreshPercentage := percentageLimits.minCropThresholdPercentage; mediumThreshPercentage <= percentageLimits.maxCropThresholdPercentage; mediumThreshPercentage += 10 {
				for mediumMindooPercentage := percentageLimits.minMindooPercentage; mediumMindooPercentage <= percentageLimits.maxMindooPercentage; mediumMindooPercentage += 20 {
					for largeThreshPercentage := percentageLimits.minCropThresholdPercentage; largeThreshPercentage <= percentageLimits.maxCropThresholdPercentage; largeThreshPercentage += 10 {
						for largeMindooPercentage := percentageLimits.minMindooPercentage; largeMindooPercentage <= percentageLimits.maxMindooPercentage; largeMindooPercentage += 20 {
							smallThreshold := float32(smallThreshPercentage) / float32(100)
							smallMindoo := float32(smallMindooPercentage) / float32(100)
							mediumThreshold := float32(mediumThreshPercentage) / float32(100)
							mediumMindoo := float32(mediumMindooPercentage) / float32(100)
							largeThreshold := float32(largeThreshPercentage) / float32(100)
							largeMindoo := float32(largeMindooPercentage) / float32(100)
							item := make(map[string]ThresholdMindooMetrics)
							item["small"] = ThresholdMindooMetrics{threshold: smallThreshold, mindoo: smallMindoo, category: "crop", size: "small"}
							item["medium"] = ThresholdMindooMetrics{threshold: mediumThreshold, mindoo: mediumMindoo, category: "crop", size: "medium"}
							item["large"] = ThresholdMindooMetrics{threshold: largeThreshold, mindoo: largeMindoo, category: "crop", size: "large"}
							cropConfigurations = append(cropConfigurations, item)
						}
					}
				}
			}
		}
	}

	configurationTracker := NewConfigurationTracker()

	numCpus := runtime.NumCPU()

	for weedCategory, weedSizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED] {
		if weedCategory == "CROP" {
			continue
		}

		for weedSize, weedResults := range weedSizes {
			var weedSize1 float32
			var weedSize2 float32
			if weedSize == "small" {
				weedSize1 = 0
				weedSize2 = categoryToSizes.get(weedCategory)[0]
			} else if weedSize == "medium" {
				weedSize1 = categoryToSizes.get(weedCategory)[0]
				weedSize2 = categoryToSizes.get(weedCategory)[1]
			} else {
				weedSize1 = categoryToSizes.get(weedCategory)[1]
				weedSize2 = float32(math.Inf(1))
			}

			for i := 0; i < len(cropConfigurations); i += numCpus {
				var wgConfigurations sync.WaitGroup

				for j := i; j < min(len(cropConfigurations), i+numCpus); j += 1 {
					wgConfigurations.Add(1)

					cropConfig := cropConfigurations[j]

					go func(smallConfiguration ThresholdMindooMetrics, mediumConfiguration ThresholdMindooMetrics, largeConfiguration ThresholdMindooMetrics, weedSize1 float32, weedSize2 float32, weedResults []*frontend.PlantCaptchaResult, weedSize, weedCategory string) {
						defer wgConfigurations.Done()

						for threshPercentage := percentageLimits.minWeedThresholdPercentage; threshPercentage <= percentageLimits.maxWeedThresholdPercentage; threshPercentage += 10 {
							for **************** := percentageLimits.minMindooPercentage; **************** <= percentageLimits.maxMindooPercentage; **************** += 20 {
								threshold := float32(threshPercentage) / float32(100)
								mindoo := float32(****************) / float32(100)

								weedsTargeted, weedsTotal := calculateItemsTargetedWithinWeedSize(threshold, mindoo, smallConfiguration.threshold, smallConfiguration.mindoo, mediumConfiguration.threshold, mediumConfiguration.mindoo, largeConfiguration.threshold, largeConfiguration.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, weedResults, true)
								cropsTargeted, cropsTotal := calculateItemsTargetedWithinWeedSize(threshold, mindoo, smallConfiguration.threshold, smallConfiguration.mindoo, mediumConfiguration.threshold, mediumConfiguration.mindoo, largeConfiguration.threshold, largeConfiguration.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, allCrops, true)
								othersTargeted, othersTotal := calculateItemsTargetedWithinWeedSize(threshold, mindoo, smallConfiguration.threshold, smallConfiguration.mindoo, mediumConfiguration.threshold, mediumConfiguration.mindoo, largeConfiguration.threshold, largeConfiguration.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, allOthers, true)

								cropConfigurationKey := CropConfigurationKey{
									cropSmallThresh:  smallConfiguration.threshold,
									cropSmallMindoo:  smallConfiguration.mindoo,
									cropMediumThresh: mediumConfiguration.threshold,
									cropMediumMindoo: mediumConfiguration.mindoo,
									cropLargeThresh:  largeConfiguration.threshold,
									cropLargeMindoo:  largeConfiguration.mindoo,
								}

								weedConfigurationKey := WeedConfigurationKey{
									weedCategory: weedCategory,
									weedSize:     weedSize,
								}

								performanceValues := WeedPerformanceValues{
									weedsTargeted:  weedsTargeted,
									weedsTotal:     weedsTotal,
									cropsTargeted:  cropsTargeted,
									cropsTotal:     cropsTotal,
									othersTargeted: othersTargeted,
									othersTotal:    othersTotal,
									threshold:      threshold,
									mindoo:         mindoo,
								}
								configurationTracker.add(cropConfigurationKey, weedConfigurationKey, performanceValues, results)
							}
						}
					}(cropConfig["small"], cropConfig["medium"], cropConfig["large"], weedSize1, weedSize2, weedResults, weedSize, weedCategory)
				}

				wgConfigurations.Wait()
			}
		}
	}

	bestCropWeedConfigurations := configurationTracker.retrieve()

	bestCropConfiguration, validConfiguarionExists := getBestConfiguration(results, bestCropWeedConfigurations)

	if !validConfiguarionExists {
		logrus.Error("Could not calculate weeding recommendation, no bestCropWeedConfigurations")
		return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_METRICS_NOT_MET}, nil
	}

	logrus.Infof("CategorySizeWeedingExhaustivePlantCaptchaAlgorithm ran in %v", time.Since(start))

	setModelinatorValues(modelinator, bestCropConfiguration, captchaResults, bestCropWeedConfigurations)

	return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: modelinator, Succeeded: true, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NO_FAILURE}, nil
}

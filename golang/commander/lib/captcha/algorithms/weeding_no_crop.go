package captcha

import (
	"context"
	"math"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
)

type CategorySizeWeedingNoCropPlantCaptchaAlgorithm struct{}

func getInSize(allObjects []*frontend.PlantCaptchaResult, size1, size2 float32) []*frontend.PlantCaptchaResult {
	objectsInSize := make([]*frontend.PlantCaptchaResult, 0)
	for _, object := range allObjects {
		if object.GetMetadata().GetSizeMm() >= size1 && object.GetMetadata().GetSizeMm() < size2 {
			objectsInSize = append(objectsInSize, object)
		}
	}
	return objectsInSize
}

func (s *CategorySizeWeedingNoCropPlantCaptchaAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	captchaResults, labelCounts, categoryToSizes := splitCaptchasCategorySize(results)

	percentageLimits := getPercentageLimits(results)

	modelinator := results.CurrentParameters

	if labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED) < 1 {
		logrus.Error("Could not calculate weeding_no_crop recommendation, missing weed labels")
		return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NOT_ENOUGH_ITEMS}, nil
	}

	allCrops := getAllOfPredictionType(captchaResults, weed_tracking.PlantCaptchaUserPrediction_CROP)
	allOthers := getAllOfPredictionType(captchaResults, weed_tracking.PlantCaptchaUserPrediction_OTHER)

	bestWeedConfigurations := make(map[WeedConfigurationKey]WeedDetectionPerformanceValues)

	for weedCategory, weedSizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED] {
		if weedCategory == "CROP" {
			continue
		}
		for weedSize, weedResults := range weedSizes {
			var weedSize1 float32
			var weedSize2 float32
			if weedSize == "small" {
				weedSize1 = 0
				weedSize2 = categoryToSizes.get(weedCategory)[0]
			} else if weedSize == "medium" {
				weedSize1 = categoryToSizes.get(weedCategory)[0]
				weedSize2 = categoryToSizes.get(weedCategory)[1]
			} else {
				weedSize1 = categoryToSizes.get(weedCategory)[1]
				weedSize2 = float32(math.Inf(1))
			}

			cropsInSize := getInSize(allCrops, weedSize1, weedSize2)
			othersInSize := getInSize(allOthers, weedSize1, weedSize2)

			bestDifference := float32(0.0)
			for threshPercentage := percentageLimits.minWeedThresholdPercentage; threshPercentage <= percentageLimits.maxWeedThresholdPercentage; threshPercentage += 5 {
				for mindooPercentage := percentageLimits.minMindooPercentage; mindooPercentage <= percentageLimits.maxMindooPercentage; mindooPercentage += 5 {
					threshold := float32(threshPercentage) / float32(100)
					mindoo := float32(mindooPercentage) / float32(100)

					weedsDetected := calculateDetectedWithMindoo(threshold, mindoo, weedResults, weed_tracking.PlantCaptchaUserPrediction_WEED)
					cropsDetectedAsWeed := float32(0)
					if len(cropsInSize) > 0 {
						cropsDetectedAsWeed = calculateDetectedWithMindoo(threshold, mindoo, cropsInSize, weed_tracking.PlantCaptchaUserPrediction_WEED)
					}
					othersDetectedAsWeed := float32(0)
					if len(othersInSize) > 0 {
						othersDetectedAsWeed = calculateDetectedWithMindoo(threshold, mindoo, othersInSize, weed_tracking.PlantCaptchaUserPrediction_WEED)
					}

					difference := weedsDetected - cropsDetectedAsWeed

					weedConfigurationKey := WeedConfigurationKey{
						weedCategory: weedCategory,
						weedSize:     weedSize,
					}

					if _, ok := bestWeedConfigurations[weedConfigurationKey]; !ok {
						bestWeedConfigurations[weedConfigurationKey] = WeedDetectionPerformanceValues{
							weedsDetected:        weedsDetected,
							cropsDetectedAsWeed:  cropsDetectedAsWeed,
							othersDetectedAsWeed: othersDetectedAsWeed,
							weedingThreshold:     threshold,
							mindoo:               mindoo,
						}
						continue
					}

					replace := difference >= bestDifference

					if results.UseOtherAsTiebreaker {
						replace = difference > bestDifference
						if difference == bestDifference {
							if weedsDetected > bestWeedConfigurations[weedConfigurationKey].weedsDetected {
								replace = true
							} else if weedsDetected == bestWeedConfigurations[weedConfigurationKey].weedsDetected {
								if othersDetectedAsWeed < bestWeedConfigurations[weedConfigurationKey].othersDetectedAsWeed || othersDetectedAsWeed == 0 {
									replace = true
								}
							}
						}
					}

					if replace {
						bestDifference = difference
						bestWeedConfigurations[weedConfigurationKey] = WeedDetectionPerformanceValues{
							weedsDetected:        weedsDetected,
							cropsDetectedAsWeed:  cropsDetectedAsWeed,
							othersDetectedAsWeed: othersDetectedAsWeed,
							weedingThreshold:     threshold,
							mindoo:               mindoo,
						}
					}
				}
			}
		}
	}

	logrus.Infof("CategorySizeWeedingNoCropPlantCaptchaAlgorithm completed: %v items", len(bestWeedConfigurations))

	for _, param := range modelinator.GetCategories() {
		paramType := param.GetType()
		if paramType == nil {
			logrus.Warn("PlantCaptcha: Skipping category because there's no associated Type")
			continue
		}
		category := paramType.GetCategory()
		if category == "" {
			logrus.Warn("PlantCaptcha: Skipping category because it doesn't have a category")
			continue
		}
		weedCategoryKeySmall := WeedConfigurationKey{
			weedCategory: translateCategory(category),
			weedSize:     "small",
		}
		weedCategoryKeyMedium := WeedConfigurationKey{
			weedCategory: translateCategory(category),
			weedSize:     "medium",
		}
		weedCategoryKeyLarge := WeedConfigurationKey{
			weedCategory: translateCategory(category),
			weedSize:     "large",
		}
		for ind, key := range []WeedConfigurationKey{weedCategoryKeySmall, weedCategoryKeyMedium, weedCategoryKeyLarge} {
			item, ok := bestWeedConfigurations[key]

			if ok {
				logrus.Infof("Setting for %v: weeding threshold %v, mindoo %v: weeds detected %v, crops detected as weeds %v, others detected as weeds %v", key, item.weedingThreshold, item.mindoo, item.weedsDetected, item.cropsDetectedAsWeed, item.othersDetectedAsWeed)
				param.Trusts[ind].WeedingThreshold = item.weedingThreshold
				param.Trusts[ind].MinDoo = item.mindoo
			}
		}
	}

	return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: modelinator, Succeeded: true, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NO_FAILURE}, nil
}

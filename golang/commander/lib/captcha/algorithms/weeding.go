package captcha

import (
	"context"
	"math"
	"sort"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
)

type CategorySizeWeedingPlantCaptchaAlgorithm struct{}

func (s *CategorySizeWeedingPlantCaptchaAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	start := time.Now()
	captchaResults, labelCounts, categoryToSizes := splitCaptchasCategorySize(results)

	percentageLimits := getPercentageLimits(results)

	modelinator := results.CurrentParameters

	weedCounts := labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED)
	cropCounts := labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP)

	if cropCounts == 0 {
		if weedCounts > 0 {
			logrus.Error("Weeds labeled but no crops, using weeding_no_crop_labeled mode")
			return (&CategorySizeWeedingNoCropLabeledAlgorithm{}).Submit(ctx, plantCaptcha, results)
		} else {
			logrus.Error("Could not calculate weeding recommendation, missing weed labels and crop labels")
			return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NOT_ENOUGH_ITEMS}, nil
		}
	}

	cropMetrics := make(map[string][]ThresholdMindooMetrics, 0)
	cropsMissed := make(map[string][]float64, 0)

	allCrops := getAllOfPredictionType(captchaResults, weed_tracking.PlantCaptchaUserPrediction_CROP)
	allOthers := getAllOfPredictionType(captchaResults, weed_tracking.PlantCaptchaUserPrediction_OTHER)

	currentCropModelinator := getCurrentCropParamConfiguration(modelinator)

	cropSizes := categoryToSizes.get("CROP")
	cropSize1 := cropSizes[0]
	cropSize2 := cropSizes[1]

	for category, sizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_CROP] {
		for size, res := range sizes {
			var currentThreshold float32
			var currentMindoo float32
			if size == "small" {
				currentThreshold = currentCropModelinator.smallWeedingThreshold
				currentMindoo = currentCropModelinator.smallMindoo
			} else if size == "medium" {
				currentThreshold = currentCropModelinator.mediumWeedingThreshold
				currentMindoo = currentCropModelinator.mediumMindoo
			} else if size == "large" {
				currentThreshold = currentCropModelinator.largeWeedingThreshold
				currentMindoo = currentCropModelinator.largeMindoo
			}
			missedAtCurrentThresholdMindoo := 1 - calculateDetectedWithMindoo(currentThreshold, currentMindoo, res, weed_tracking.PlantCaptchaUserPrediction_CROP)

			for threshPercentage := percentageLimits.minCropThresholdPercentage; threshPercentage < percentageLimits.maxCropThresholdPercentage; threshPercentage += 10 {
				for **************** := percentageLimits.minMindooPercentage; **************** <= percentageLimits.maxMindooPercentage; **************** += 20 {
					threshold := float32(threshPercentage) / float32(100)
					mindoo := float32(****************) / float32(100)
					missed := 1 - calculateDetectedWithMindoo(threshold, mindoo, res, weed_tracking.PlantCaptchaUserPrediction_CROP)

					add_config := true
					if results.LimitByCropsMissed {
						add_config = missed <= missedAtCurrentThresholdMindoo
					}
					if add_config {
						cropMetrics[size] = append(cropMetrics[size], ThresholdMindooMetrics{threshold: threshold, mindoo: mindoo, category: category, size: size, cropsMissed: missed})
						cropsMissed[size] = append(cropsMissed[size], float64(missed))
					}
				}
			}
		}
	}

	var cropModelinatorSettings []*almanac.ModelTrust
	for _, categ := range modelinator.Categories {
		if categ.Type.Classification == almanac.CategoryClassification_CATEGORY_CROP {
			cropModelinatorSettings = categ.Trusts
		}
	}

	// For the case we have more than N qualifying items, get the ones with the best crops missed metrics per size
	cropMetricsFiltered := make(map[string][]ThresholdMindooMetrics, 0)
	for size, missed := range cropsMissed {
		sort.Float64s(missed)
		numberItems := len(missed)
		if results.NumberOfCropConfigurations > 0 && numberItems > int(results.NumberOfCropConfigurations) {
			numberItems = int(results.NumberOfCropConfigurations)
		}
		for _, item := range cropMetrics[size] {
			if item.cropsMissed <= float32(missed[numberItems-1]) {
				cropMetricsFiltered[size] = append(cropMetricsFiltered[size], item)
			}
		}
	}

	if len(cropMetricsFiltered["small"]) == 0 {
		cropMetricsFiltered["small"] = append(cropMetricsFiltered["small"], ThresholdMindooMetrics{threshold: cropModelinatorSettings[0].WeedingThreshold, mindoo: cropModelinatorSettings[0].MinDoo, category: "CROP", size: "small"})
	}
	if len(cropMetricsFiltered["medium"]) == 0 {
		cropMetricsFiltered["medium"] = append(cropMetricsFiltered["medium"], ThresholdMindooMetrics{threshold: cropModelinatorSettings[1].WeedingThreshold, mindoo: cropModelinatorSettings[1].MinDoo, category: "CROP", size: "medium"})
	}
	if len(cropMetricsFiltered["large"]) == 0 {
		cropMetricsFiltered["large"] = append(cropMetricsFiltered["large"], ThresholdMindooMetrics{threshold: cropModelinatorSettings[2].WeedingThreshold, mindoo: cropModelinatorSettings[2].MinDoo, category: "CROP", size: "large"})
	}

	if results.GetPadCropConfigurations() {
		cropMetricsFiltered["small"] = appendDefaultCropConfigurations(cropMetricsFiltered["small"], percentageLimits, "small")
		cropMetricsFiltered["medium"] = appendDefaultCropConfigurations(cropMetricsFiltered["medium"], percentageLimits, "medium")
		cropMetricsFiltered["large"] = appendDefaultCropConfigurations(cropMetricsFiltered["large"], percentageLimits, "large")
	}

	bestCropWeedConfigurations := make(map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues)

	logrus.Infof("Crop configurations %v %v %v", len(cropMetricsFiltered["small"]), len(cropMetricsFiltered["medium"]), len(cropMetricsFiltered["large"]))

	for weedCategory, weedSizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED] {
		if weedCategory == "CROP" {
			continue
		}
		for weedSize, weedResults := range weedSizes {
			var weedSize1 float32
			var weedSize2 float32
			if weedSize == "small" {
				weedSize1 = 0
				weedSize2 = categoryToSizes.get(weedCategory)[0]
			} else if weedSize == "medium" {
				weedSize1 = categoryToSizes.get(weedCategory)[0]
				weedSize2 = categoryToSizes.get(weedCategory)[1]
			} else {
				weedSize1 = categoryToSizes.get(weedCategory)[1]
				weedSize2 = float32(math.Inf(1))
			}
			for _, smallMetric := range cropMetricsFiltered["small"] {
				for _, mediumMetric := range cropMetricsFiltered["medium"] {
					for _, largeMetric := range cropMetricsFiltered["large"] {

						for threshPercentage := percentageLimits.minWeedThresholdPercentage; threshPercentage < percentageLimits.maxWeedThresholdPercentage; threshPercentage += 10 {
							for **************** := percentageLimits.minMindooPercentage; **************** <= percentageLimits.maxMindooPercentage; **************** += 20 {
								threshold := float32(threshPercentage) / float32(100)
								mindoo := float32(****************) / float32(100)

								weedsTargetedItems, weedsTotalItems := calculateItemsTargetedWithinWeedSize(threshold, mindoo, smallMetric.threshold, smallMetric.mindoo, mediumMetric.threshold, mediumMetric.mindoo, largeMetric.threshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, weedResults, true)
								cropsTargetedItems, cropsTotalItems := calculateItemsTargetedWithinWeedSize(threshold, mindoo, smallMetric.threshold, smallMetric.mindoo, mediumMetric.threshold, mediumMetric.mindoo, largeMetric.threshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, allCrops, true)
								othersTargetedItems, othersTotalItems := calculateItemsTargetedWithinWeedSize(threshold, mindoo, smallMetric.threshold, smallMetric.mindoo, mediumMetric.threshold, mediumMetric.mindoo, largeMetric.threshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, allOthers, true)

								cropConfigurationKey := CropConfigurationKey{
									cropSmallThresh:  smallMetric.threshold,
									cropSmallMindoo:  smallMetric.mindoo,
									cropMediumThresh: mediumMetric.threshold,
									cropMediumMindoo: mediumMetric.mindoo,
									cropLargeThresh:  largeMetric.threshold,
									cropLargeMindoo:  largeMetric.mindoo,
								}

								weedConfigurationKey := WeedConfigurationKey{
									weedCategory: weedCategory,
									weedSize:     weedSize,
								}

								if _, ok := bestCropWeedConfigurations[cropConfigurationKey]; !ok {
									bestCropWeedConfigurations[cropConfigurationKey] = map[WeedConfigurationKey]WeedPerformanceValues{}
								}

								bestItem, ok := bestCropWeedConfigurations[cropConfigurationKey][weedConfigurationKey]

								if !ok {
									bestCropWeedConfigurations[cropConfigurationKey][weedConfigurationKey] = WeedPerformanceValues{
										weedsTargeted:  weedsTargetedItems,
										weedsTotal:     weedsTotalItems,
										cropsTargeted:  cropsTargetedItems,
										cropsTotal:     cropsTotalItems,
										othersTargeted: othersTargetedItems,
										othersTotal:    othersTotalItems,
										threshold:      threshold,
										mindoo:         mindoo,
									}
								} else {
									weedsTargeted := float32(len(weedsTargetedItems)) / float32(len(weedsTotalItems))
									bestWeedsTargeted := float32(len(bestItem.weedsTargeted)) / float32(len(bestItem.weedsTotal))

									replace := weedsTargeted >= bestWeedsTargeted
									if results.UseOtherAsTiebreaker {
										replace = weedsTargeted > bestWeedsTargeted
										if weedsTargeted == bestWeedsTargeted {
											if len(othersTotalItems) == 0 {
												replace = true
											} else {
												othersTargeted := float32(len(othersTargetedItems)) / (float32(len(othersTotalItems)) + 1e-6)
												bestOthersTargeted := float32(len(bestItem.othersTargeted)) / (float32(len(bestItem.othersTotal)) + 1e-6)
												replace = othersTargeted < bestOthersTargeted
											}
										}
									}

									if replace {
										bestCropWeedConfigurations[cropConfigurationKey][weedConfigurationKey] = WeedPerformanceValues{
											weedsTargeted:  weedsTargetedItems,
											weedsTotal:     weedsTotalItems,
											cropsTargeted:  cropsTargetedItems,
											cropsTotal:     cropsTotalItems,
											othersTargeted: othersTargetedItems,
											othersTotal:    othersTotalItems,
											threshold:      threshold,
											mindoo:         mindoo,
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	bestCropConfiguration, validConfiguarationExists := getBestConfiguration(results, bestCropWeedConfigurations)

	if !validConfiguarationExists {
		logrus.Error("Could not calculate weeding recommendation, no bestCropWeedConfigurations")
		return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_METRICS_NOT_MET}, nil
	}

	logrus.Infof("CategorySizeWeedingPlantCaptchaAlgorithm ran in %v", time.Since(start))

	setModelinatorValues(modelinator, bestCropConfiguration, captchaResults, bestCropWeedConfigurations)

	return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: modelinator, Succeeded: true, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NO_FAILURE}, nil
}

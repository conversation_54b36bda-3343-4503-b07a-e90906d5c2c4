package captcha

import (
	"context"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
)

type VeselkaPlantCaptchaAlgorithm struct {
	VeselkaClient *veselka.Client
}

func (v *VeselkaPlantCaptchaAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	robotName, err := environment.GetRobot()
	if err != nil {
		logrus.Warnf("PlantCaptcha: Could not get robot name in veselka upload algorithm: %v", err)
	}
	modelinator, succeeded, err := v.VeselkaClient.RecommendModel(ctx, results, robotName.MakaRobotName)
	if err != nil {
		logrus.WithError(err).Error("PlantCaptcha: Could not read from veselka")
		return nil, err
	}
	return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: modelinator, Succeeded: succeeded}, nil
}

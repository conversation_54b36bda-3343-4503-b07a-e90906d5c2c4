package captcha

import (
	"context"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
)

type CategorySizeWeedingNoCropLabeledAlgorithm struct{}

func (s *CategorySizeWeedingNoCropLabeledAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	captchaResults, labelCounts, _ := splitCaptchasCategorySize(results)
	modelinator := results.GetCurrentParameters()

	weedCount := labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED)

	if weedCount < 1 {
		logrus.Errorf("Count not calculate weeding recommendation, missing weed lables (%v)", weedCount)
		return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NOT_ENOUGH_ITEMS}, nil
	}

	cropConfiguration := CropConfigurationKey{
		cropSmallThresh:  float32(1.0),
		cropSmallMindoo:  float32(1.0),
		cropMediumThresh: float32(1.0),
		cropMediumMindoo: float32(1.0),
		cropLargeThresh:  float32(1.0),
		cropLargeMindoo:  float32(1.0),
	}

	weedConfigurations := make(map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues)

	weedConfigurations[cropConfiguration] = map[WeedConfigurationKey]WeedPerformanceValues{}
	for weedCategory, weedSizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED] {
		if weedCategory == "CROP" {
			continue
		}

		for weedSize, weedResults := range weedSizes {
			minThreshold := max(getMinThreshold(weedResults), results.GetMinRecommendedWeedThreshold())
			mindoo := float32(0.0)

			weedConfigurationKey := WeedConfigurationKey{
				weedCategory: weedCategory,
				weedSize:     weedSize,
			}

			weedConfigurations[cropConfiguration][weedConfigurationKey] = WeedPerformanceValues{
				threshold: minThreshold,
				mindoo:    mindoo,
			}
		}
	}

	setModelinatorValues(modelinator, cropConfiguration, captchaResults, weedConfigurations)

	return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: modelinator, Succeeded: true, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NO_FAILURE}, nil
}

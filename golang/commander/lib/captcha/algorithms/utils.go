package captcha

import (
	"context"
	"fmt"
	"math"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
)

type PlantCaptchaAlgorithm interface {
	Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error)
}

func getPercentageLimits(results *frontend.PlantCaptchaResults) PercentageLimits {
	return PercentageLimits{
		minMindooPercentage:        int(results.GetMinRecommendedMindoo() * 100),
		maxMindooPercentage:        int(results.GetMaxRecommendedMindoo() * 100),
		minWeedThresholdPercentage: int(results.GetMinRecommendedWeedThreshold() * 100),
		maxWeedThresholdPercentage: int(results.GetMaxRecommendedWeedThreshold() * 100),
		minCropThresholdPercentage: int(results.GetMinRecommendedCropThreshold() * 100),
		maxCropThresholdPercentage: int(results.GetMaxRecommendedCropThreshold() * 100),
	}
}

func getAllOfPredictionType(objectMap map[weed_tracking.PlantCaptchaUserPrediction]map[string]map[string][]*frontend.PlantCaptchaResult, typ weed_tracking.PlantCaptchaUserPrediction) []*frontend.PlantCaptchaResult {
	allObjects := make([]*frontend.PlantCaptchaResult, 0)
	for _, sizes := range objectMap[typ] {
		for _, res := range sizes {
			allObjects = append(allObjects, res...)
		}
	}
	return allObjects
}

func getMinThreshold(captchaResults []*frontend.PlantCaptchaResult) float32 {
	minThreshold := float32(1.0)
	for _, result := range captchaResults {
		weedConfidenceHistory := result.GetMetadata().GetWeedConfidenceHistory()

		for _, score := range weedConfidenceHistory {
			if score < minThreshold {
				minThreshold = score
			}
		}
	}

	return minThreshold
}

func calculateItemsTargetedWithinWeedSize(weedThreshold, weedMindoo, cropSmallThreshold, cropSmallMindoo, cropMediumThreshold, cropMediumMindoo, cropLargeThreshold, cropLargeMindoo, cropSize1, cropSize2, weedSize1, weedSize2 float32, captchaResults []*frontend.PlantCaptchaResult, targetAsWeed bool) (map[string]bool, map[string]bool) {
	targetedItems := make(map[string]bool)
	existingItems := make(map[string]bool)
	for _, result := range captchaResults {
		if result.GetMetadata().GetSizeMm() < weedSize1 || result.GetMetadata().GetSizeMm() >= weedSize2 {
			continue
		}
		existingItems[result.GetMetadata().GetId()] = true

		detectedAsWeed := 0
		detectedAsCrop := 0

		weedConfidenceHistory := result.GetMetadata().GetWeedConfidenceHistory()
		cropConfidenceHistory := result.GetMetadata().GetCropConfidenceHistory()

		var cropThreshold float32
		var cropMindoo float32
		if result.GetMetadata().GetSizeMm() < cropSize1 {
			cropThreshold = cropSmallThreshold
			cropMindoo = cropSmallMindoo
		} else if result.GetMetadata().GetSizeMm() < cropSize2 {
			cropThreshold = cropMediumThreshold
			cropMindoo = cropMediumMindoo
		} else {
			cropThreshold = cropLargeThreshold
			cropMindoo = cropLargeMindoo
		}

		for i, weedConfidence := range weedConfidenceHistory {
			cropConfidence := cropConfidenceHistory[i]
			if weedConfidence > weedThreshold {
				detectedAsWeed += 1
			}

			if cropConfidence > cropThreshold {
				detectedAsCrop += 1
			}
		}

		minRequiredWeedDetections := int(weedMindoo * float32(len(weedConfidenceHistory)))
		minRequiredCropDetections := int(cropMindoo * float32(len(cropConfidenceHistory)))

		targeted := detectedAsCrop > minRequiredCropDetections && detectedAsWeed <= minRequiredWeedDetections
		if targetAsWeed {
			targeted = detectedAsWeed > minRequiredWeedDetections && detectedAsCrop <= minRequiredCropDetections
		}
		if targeted {
			targetedItems[result.GetMetadata().GetId()] = true
		}
	}

	return targetedItems, existingItems
}

func getCurrentCropParamConfiguration(modelinator *almanac.ModelinatorConfig) ParameterConfiguration {
	for _, param := range modelinator.GetCategories() {
		if param.Type.Classification == almanac.CategoryClassification_CATEGORY_CROP {
			return ParameterConfiguration{
				smallWeedingThreshold:   param.Trusts[0].WeedingThreshold,
				smallThinningThreshold:  param.Trusts[0].ThinningThreshold,
				smallMindoo:             param.Trusts[0].MinDoo,
				mediumWeedingThreshold:  param.Trusts[1].WeedingThreshold,
				mediumThinningThreshold: param.Trusts[1].ThinningThreshold,
				mediumMindoo:            param.Trusts[0].MinDoo,
				largeWeedingThreshold:   param.Trusts[2].WeedingThreshold,
				largeThinningThreshold:  param.Trusts[2].ThinningThreshold,
				largeMindoo:             param.Trusts[2].MinDoo,
			}
		}
	}

	return ParameterConfiguration{
		smallWeedingThreshold:   0.5,
		smallThinningThreshold:  0.5,
		smallMindoo:             0,
		mediumWeedingThreshold:  0.5,
		mediumThinningThreshold: 0.5,
		mediumMindoo:            0,
		largeWeedingThreshold:   0.5,
		largeThinningThreshold:  0.5,
		largeMindoo:             0,
	}
}

func calculateDetectedWithMindoo(threshold, mindoo float32, captchaResults []*frontend.PlantCaptchaResult, detectedAs weed_tracking.PlantCaptchaUserPrediction) float32 {
	numberDetected := 0
	for _, result := range captchaResults {
		detected := 0

		var opportunities float32

		var confidenceHistory []float32
		if detectedAs == weed_tracking.PlantCaptchaUserPrediction_WEED {
			confidenceHistory = result.GetMetadata().GetWeedConfidenceHistory()
		} else if detectedAs == weed_tracking.PlantCaptchaUserPrediction_CROP {
			confidenceHistory = result.GetMetadata().GetCropConfidenceHistory()
		} else {
			continue
		}

		opportunities = float32(len(confidenceHistory))
		for _, confidence := range confidenceHistory {
			if confidence > threshold {
				detected += 1
			}
		}

		minRequiredDetections := int(mindoo * opportunities)
		if detected > minRequiredDetections {
			numberDetected += 1
		}
	}

	return float32(numberDetected) / float32(len(captchaResults))
}

func findAlmanacCategories(almanacConf *almanac.AlmanacConfig) []string {
	categories := make([]string, 0)

	for _, cat := range almanacConf.Categories {
		catString := "DEFAULT"
		if cat.Type.Category != "DEFAULT" {
			catString = translateCategory(cat.Type.Category)
		}
		categories = append(categories, catString)
	}

	return categories
}

func findAlmanacSizes(almanacConf *almanac.AlmanacConfig, typeCat string) (float32, float32, error) {
	for _, cat := range almanacConf.Categories {
		catString := "DEFAULT"
		if cat.Type.Category != "DEFAULT" {
			catString = translateCategory(cat.Type.Category)
		}
		if catString == typeCat {
			if len(cat.Sizes) < 2 {
				logrus.Errorf("Almanac doesn't contain the size info")
				return 0, 0, fmt.Errorf("Almanac doesn't contain the size info")
			}
			return cat.Sizes[0], cat.Sizes[1], nil
		}
	}
	logrus.Errorf("Could find almanac category")
	return 0, 0, fmt.Errorf("Could find almanac category")
}

func translateCategory(name string) string {
	if name == "9fd4e9d0-2d01-4ae1-89c5-f4b4f6de9a3c" || name == "BROADLEAF" {
		return "BROADLEAF"
	} else if name == "f032b206-92cf-400a-b79e-e22bdb7930e3" || name == "GRASS" {
		return "GRASS"
	} else if name == "2e678c61-f476-4485-aa9c-6a0e4bc7c626" || name == "OFFSHOOT" {
		return "OFFSHOOT"
	} else if name == "1659226a-fe1c-450c-828f-88dc8819c25d" || name == "PURSLANE" {
		return "PURSLANE"
	} else {
		return "CROP"
	}
}

type CategoryToSizes struct {
	categoriesToSizeMap map[string][]float32
}

func (t *CategoryToSizes) load(almanac *almanac.AlmanacConfig, categories []string) {
	t.categoriesToSizeMap = make(map[string][]float32)

	for _, category := range categories {
		if _, ok := t.categoriesToSizeMap[category]; !ok {
			size1, size2, err := findAlmanacSizes(almanac, category)
			if err == nil {
				t.categoriesToSizeMap[category] = append(t.categoriesToSizeMap[category], size1)
				t.categoriesToSizeMap[category] = append(t.categoriesToSizeMap[category], size2)
			}
		}
	}
}

func (t *CategoryToSizes) get(category string) []float32 {
	catToSize, catToSizeOk := t.categoriesToSizeMap[category]

	if !catToSizeOk {
		catToSize, _ = t.categoriesToSizeMap["DEFAULT"]
	}

	return catToSize
}

func splitCaptchasCategorySize(results *frontend.PlantCaptchaResults) (map[weed_tracking.PlantCaptchaUserPrediction]map[string]map[string][]*frontend.PlantCaptchaResult, LabelCounts, CategoryToSizes) {
	outMap := make(map[weed_tracking.PlantCaptchaUserPrediction]map[string]map[string][]*frontend.PlantCaptchaResult)

	categories := findAlmanacCategories(results.Almanac)

	useWeedCategoriesForWeeds := results.GetUseWeedCategoriesForWeedLabels()
	useBeneficialsAsCrops := results.GetUseBeneficialsAsCrops()
	useVolunteersAsWeeds := results.GetUseVolunteersAsWeeds()

	categoryToSizes := CategoryToSizes{}
	categoryToSizes.load(results.Almanac, categories)

	for _, result := range results.CaptchaResults {
		resultLabel := result.GetLabel()

		if useBeneficialsAsCrops && resultLabel == weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL {
			resultLabel = weed_tracking.PlantCaptchaUserPrediction_CROP
		}
		if useVolunteersAsWeeds && resultLabel == weed_tracking.PlantCaptchaUserPrediction_VOLUNTEER {
			resultLabel = weed_tracking.PlantCaptchaUserPrediction_WEED
		}
		if result.GetMetadata().GetDoo() < results.GetMinDooForRecommendation() {
			continue
		}
		maxCategory := ""
		maxValue := float32(0)
		metadataCats := result.GetMetadata().GetCategories()
		if useWeedCategoriesForWeeds {
			metadataCats = result.GetMetadata().GetWeedCategories()
		}
		for category, value := range metadataCats {
			if value > maxValue {
				maxCategory = category
				maxValue = value
			}
		}

		if resultLabel == weed_tracking.PlantCaptchaUserPrediction_CROP {
			maxCategory = "CROP"
		}

		sizeMm := result.GetMetadata().GetSizeMm()
		var size string
		catToSize := categoryToSizes.get(maxCategory)

		if sizeMm < catToSize[0] {
			size = "small"
		} else if sizeMm < catToSize[1] {
			size = "medium"
		} else {
			size = "large"
		}

		if _, ok := outMap[resultLabel]; !ok {
			outMap[resultLabel] = map[string]map[string][]*frontend.PlantCaptchaResult{}
		}

		if _, ok := outMap[resultLabel][maxCategory]; !ok {
			outMap[resultLabel][maxCategory] = map[string][]*frontend.PlantCaptchaResult{}
		}

		if _, ok := outMap[resultLabel][maxCategory][size]; !ok {
			outMap[resultLabel][maxCategory][size] = []*frontend.PlantCaptchaResult{}
		}

		outMap[resultLabel][maxCategory][size] = append(outMap[resultLabel][maxCategory][size], result)
	}

	// Remove everything that doesn't have enough items
	for hit, categoryMap := range outMap {
		for category, sizeMap := range categoryMap {
			var keysToDelete []string
			for size, res := range sizeMap {
				if len(res) < int(results.GetMinItemsForRecommendation()) {
					keysToDelete = append(keysToDelete, size)
				}
			}

			for _, k := range keysToDelete {
				delete(outMap[hit][category], k)
			}
		}

		var categoriesToDelete []string
		for category, sizeMap := range categoryMap {
			if len(sizeMap) == 0 {
				categoriesToDelete = append(categoriesToDelete, category)
			}
		}

		for _, k := range categoriesToDelete {
			delete(outMap[hit], k)
		}
	}

	labelCounts := getLabelCounts(outMap)

	return outMap, labelCounts, categoryToSizes
}

func getLabelCounts(outMap map[weed_tracking.PlantCaptchaUserPrediction]map[string]map[string][]*frontend.PlantCaptchaResult) LabelCounts {
	labelCounts := LabelCounts{
		counts: make(map[weed_tracking.PlantCaptchaUserPrediction]int),
	}
	for hit, categoryMap := range outMap {
		for _, sizeMap := range categoryMap {
			for _, res := range sizeMap {
				labelCounts.add(hit, len(res))
			}
		}
	}
	return labelCounts
}

func appendDefaultCropConfigurations(threshMindooMetrics []ThresholdMindooMetrics, percentageLimits PercentageLimits, size string) []ThresholdMindooMetrics {
	for threshPercentage := percentageLimits.minCropThresholdPercentage; threshPercentage <= percentageLimits.maxCropThresholdPercentage; threshPercentage += 10 {
		for **************** := percentageLimits.minMindooPercentage; **************** <= percentageLimits.maxMindooPercentage; **************** += 20 {
			threshold := float32(threshPercentage) / float32(100)
			mindoo := float32(****************) / float32(100)

			threshMindooMetrics = append(threshMindooMetrics, ThresholdMindooMetrics{threshold: threshold, mindoo: mindoo, category: "CROP", size: size})
		}
	}

	return threshMindooMetrics
}

func setModelinatorValues(modelinator *almanac.ModelinatorConfig, bestCropConfiguration CropConfigurationKey, captchaResults map[weed_tracking.PlantCaptchaUserPrediction]map[string]map[string][]*frontend.PlantCaptchaResult, bestCropWeedConfigurations map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues) {
	for _, param := range modelinator.GetCategories() {
		paramType := param.GetType()
		if paramType == nil {
			logrus.Warn("PlantCaptcha: Skipping category because there's no associated type")
			continue
		}
		if paramType.GetClassification() == almanac.CategoryClassification_CATEGORY_CROP {
			logrus.Infof("Crop small wThresh: %v Mindoo: %v, medium wThresh: %v Mindoo: %v, large wThresh: %v Mindoo: %v", bestCropConfiguration.cropSmallThresh, bestCropConfiguration.cropSmallMindoo, bestCropConfiguration.cropMediumThresh, bestCropConfiguration.cropMediumMindoo, bestCropConfiguration.cropLargeThresh, bestCropConfiguration.cropLargeMindoo)
			param.Trusts[0].WeedingThreshold = bestCropConfiguration.cropSmallThresh
			param.Trusts[0].MinDoo = bestCropConfiguration.cropSmallMindoo
			param.Trusts[1].WeedingThreshold = bestCropConfiguration.cropMediumThresh
			param.Trusts[1].MinDoo = bestCropConfiguration.cropMediumMindoo
			param.Trusts[2].WeedingThreshold = bestCropConfiguration.cropLargeThresh
			param.Trusts[2].MinDoo = bestCropConfiguration.cropLargeMindoo

			logrus.Infof("	Total small crops %v", len(captchaResults[weed_tracking.PlantCaptchaUserPrediction_CROP]["CROP"]["small"]))
			logrus.Infof("	Total medium crops %v", len(captchaResults[weed_tracking.PlantCaptchaUserPrediction_CROP]["CROP"]["medium"]))
			logrus.Infof("	Total large crops %v", len(captchaResults[weed_tracking.PlantCaptchaUserPrediction_CROP]["CROP"]["large"]))
		} else {
			paramCategory := paramType.GetCategory()
			if paramCategory == "" {
				logrus.Warn("PlantCaptcha: Skipping category because it doesn't have a category")
				continue
			}
			weedCategoryKeySmall := WeedConfigurationKey{
				weedCategory: translateCategory(paramCategory),
				weedSize:     "small",
			}
			weedCategoryKeyMedium := WeedConfigurationKey{
				weedCategory: translateCategory(paramCategory),
				weedSize:     "medium",
			}
			weedCategoryKeyLarge := WeedConfigurationKey{
				weedCategory: translateCategory(paramCategory),
				weedSize:     "large",
			}
			for ind, key := range []WeedConfigurationKey{weedCategoryKeySmall, weedCategoryKeyMedium, weedCategoryKeyLarge} {
				item, ok := bestCropWeedConfigurations[bestCropConfiguration][key]

				totalItems := len(captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED][key.weedCategory][key.weedSize])

				if ok {
					logrus.Infof("Setting for %v: weeding threshold %v, mindoo %v. Total count: %v", key, item.threshold, item.mindoo, totalItems)
					param.Trusts[ind].WeedingThreshold = item.threshold
					param.Trusts[ind].MinDoo = item.mindoo
				}
			}
		}
	}
}

func getBestConfiguration(results *frontend.PlantCaptchaResults, bestCropWeedConfigurations map[CropConfigurationKey]map[WeedConfigurationKey]WeedPerformanceValues) (CropConfigurationKey, bool) {
	tiebreaker := results.GetTiebreaker()
	mindooTiebreaker := results.GetMindooTiebreaker()

	goalValue := map[string]float64{
		"low":    float64(0),
		"middle": float64(0.5),
		"high":   float64(1),
	}

	var bestCropConfiguration CropConfigurationKey
	closestAvgThreshold := float64(1)
	avgWeedThresholdGoal := goalValue["middle"]
	avgCropThresholdGoal := goalValue["middle"]

	if tiebreaker == "custom" {
		avgWeedThresholdGoal = goalValue[results.GetTiebreakerStrategyThresholdWeed()]
		avgCropThresholdGoal = goalValue[results.GetTiebreakerStrategyThresholdCrop()]
	}

	closestAvgMindoo := float64(1)
	avgWeedMindooGoal := goalValue["middle"]
	avgCropMindooGoal := goalValue["middle"]
	if mindooTiebreaker == "mean_lowest" {
		avgWeedMindooGoal = goalValue["low"]
		avgCropMindooGoal = goalValue["low"]
	} else if mindooTiebreaker == "mean_highest" {
		avgWeedMindooGoal = goalValue["high"]
		avgCropMindooGoal = goalValue["high"]
	} else if mindooTiebreaker == "custom" {
		avgWeedMindooGoal = goalValue[results.GetTiebreakerStrategyMindooWeed()]
		avgCropMindooGoal = goalValue[results.GetTiebreakerStrategyMindooCrop()]
	}

	bestWeedsTargeted := float32(0)
	bestDifference := float32(0)
	bestCropsTargeted := float32(0)
	bestOthersTargeted := float32(0)

	validConfigurationExists := false

	for cropConfiguration, weedMap := range bestCropWeedConfigurations {
		weedsTargetedItems := make(map[string]bool)
		weedsTotalItems := make(map[string]bool)
		cropsTargetedItems := make(map[string]bool)
		cropsTotalItems := make(map[string]bool)
		othersTargetedItems := make(map[string]bool)
		othersTotalItems := make(map[string]bool)

		for _, weedValues := range weedMap {
			for k, _ := range weedValues.weedsTargeted {
				weedsTargetedItems[k] = true
			}
			for k, _ := range weedValues.weedsTotal {
				weedsTotalItems[k] = true
			}
			for k, _ := range weedValues.cropsTargeted {
				cropsTargetedItems[k] = true
			}
			for k, _ := range weedValues.cropsTotal {
				cropsTotalItems[k] = true
			}
			for k, _ := range weedValues.othersTargeted {
				othersTargetedItems[k] = true
			}
			for k, _ := range weedValues.othersTotal {
				othersTotalItems[k] = true
			}
		}

		weedsTargeted := float32(len(weedsTargetedItems)) / (float32(len(weedsTotalItems)) + 1e-6)
		cropsTargeted := float32(len(cropsTargetedItems)) / (float32(len(cropsTotalItems)) + 1e-6)
		othersTargeted := float32(len(othersTargetedItems)) / (float32(len(othersTotalItems)) + 1e-6)

		if cropsTargeted > results.GetGoalCropsTargeted() {
			continue
		}

		difference := weedsTargeted - cropsTargeted

		replaceRecommendation := weedsTargeted > bestWeedsTargeted

		weedConfigurations := bestCropWeedConfigurations[cropConfiguration]
		numWeedConfigurations := len(weedConfigurations)

		sumCropMindoo := (math.Abs(float64(cropConfiguration.cropSmallMindoo)) + math.Abs(float64(cropConfiguration.cropMediumMindoo)) + math.Abs(float64(cropConfiguration.cropLargeMindoo)))
		sumWeedMindoo := 0.0
		for _, weedParam := range weedConfigurations {
			sumWeedMindoo += math.Abs(float64(weedParam.mindoo))
		}
		numCropConfigurations := 3

		avgFromGoal := ((math.Abs(avgWeedMindooGoal*float64(numWeedConfigurations) - sumWeedMindoo)) + (math.Abs(avgCropMindooGoal*float64(numCropConfigurations) - sumCropMindoo))) / float64(numWeedConfigurations+numCropConfigurations)

		betterMindoo := avgFromGoal < closestAvgMindoo

		avgThresholdDistance := 0.0
		sumCropThresholdDistance := 0.0
		sumWeedThresholdDistance := 0.0

		if tiebreaker == "mean_absolute" || tiebreaker == "mean_square" || tiebreaker == "custom" {
			if tiebreaker == "mean_absolute" || tiebreaker == "custom" {
				sumCropThresholdDistance = (math.Abs(avgCropThresholdGoal-float64(cropConfiguration.cropSmallThresh)) + math.Abs(avgCropThresholdGoal-float64(cropConfiguration.cropMediumThresh)) + math.Abs(avgCropThresholdGoal-float64(cropConfiguration.cropLargeThresh)))
				for _, weedParam := range weedConfigurations {
					sumWeedThresholdDistance += math.Abs(avgWeedThresholdGoal - float64(weedParam.threshold))
				}
			} else if tiebreaker == "mean_square" {
				sumCropThresholdDistance = (math.Pow(avgCropThresholdGoal-float64(cropConfiguration.cropSmallThresh), 2) + math.Pow(avgCropThresholdGoal-float64(cropConfiguration.cropMediumThresh), 2) + math.Pow(avgCropThresholdGoal-float64(cropConfiguration.cropLargeThresh), 2))
				for _, weedParam := range weedConfigurations {
					sumWeedThresholdDistance += math.Pow(avgWeedThresholdGoal-float64(weedParam.threshold), 2)
				}
			}
			avgThresholdDistance = (sumWeedThresholdDistance + sumCropThresholdDistance) / float64(numWeedConfigurations+numCropConfigurations)
			replaceRecommendation = (weedsTargeted > bestWeedsTargeted) || (weedsTargeted == bestWeedsTargeted && avgThresholdDistance < closestAvgThreshold) || (weedsTargeted == bestWeedsTargeted && avgThresholdDistance == closestAvgThreshold && betterMindoo)
		}

		if replaceRecommendation {
			validConfigurationExists = true
			bestWeedsTargeted = weedsTargeted
			bestDifference = difference
			bestCropConfiguration = cropConfiguration
			bestCropsTargeted = cropsTargeted
			bestOthersTargeted = othersTargeted
			closestAvgThreshold = avgThresholdDistance
			closestAvgMindoo = avgFromGoal
		}
	}

	logrus.Infof("Found best configuration with overall bestWeedsTargeted %v, respectiveCropsTargeted %v, others targeted %v, bestCropConfiguration %v, bestWeedConfigurations %v, %v bestDifference", bestWeedsTargeted, bestCropsTargeted, bestOthersTargeted, bestCropConfiguration, bestCropWeedConfigurations[bestCropConfiguration], bestDifference)

	return bestCropConfiguration, validConfigurationExists
}

type LabelCounts struct {
	counts map[weed_tracking.PlantCaptchaUserPrediction]int
}

func (l LabelCounts) add(key weed_tracking.PlantCaptchaUserPrediction, value int) {
	if _, ok := l.counts[key]; !ok {
		l.counts[key] = 0
	}

	l.counts[key] += value
}

func (l LabelCounts) get(key weed_tracking.PlantCaptchaUserPrediction) int {
	val, ok := l.counts[key]
	if !ok {
		return 0
	}

	return val
}

type ThresholdMindooMetrics struct {
	threshold     float32
	mindoo        float32
	category      string
	size          string
	weedsTargeted float32
	cropsTargeted float32
	weedsDetected float32
	cropsMissed   float32
}

type WeedingThinningMindooMetrics struct {
	weedingThreshold     float32
	thinningThreshold    float32
	mindoo               float32
	category             string
	size                 string
	weedsTargeted        float32
	cropsTargeted        float32
	weedsDetected        float32
	cropsMissed          float32
	weedsDetectedAsCrops float32
	average              float64
}

type CropConfigurationKey struct {
	cropSmallThresh  float32
	cropSmallMindoo  float32
	cropMediumThresh float32
	cropMediumMindoo float32
	cropLargeThresh  float32
	cropLargeMindoo  float32
}

type CropThinningConfigurationKey struct {
	cropSmallWeedingThresh   float32
	cropSmallThinningThresh  float32
	cropSmallMindoo          float32
	cropMediumWeedingThresh  float32
	cropMediumThinningThresh float32
	cropMediumMindoo         float32
	cropLargeWeedingThresh   float32
	cropLargeThinningThresh  float32
	cropLargeMindoo          float32
}

type ParameterConfiguration struct {
	smallWeedingThreshold   float32
	smallThinningThreshold  float32
	smallMindoo             float32
	mediumWeedingThreshold  float32
	mediumThinningThreshold float32
	mediumMindoo            float32
	largeWeedingThreshold   float32
	largeThinningThreshold  float32
	largeMindoo             float32
}

type WeedConfigurationKey struct {
	weedCategory string
	weedSize     string
}

type WeedPerformanceValues struct {
	weedsTargeted  map[string]bool
	weedsTotal     map[string]bool
	cropsTargeted  map[string]bool
	cropsTotal     map[string]bool
	othersTargeted map[string]bool
	othersTotal    map[string]bool
	threshold      float32
	mindoo         float32
}

type WeedDetectionPerformanceValues struct {
	weedsDetected        float32
	cropsDetectedAsWeed  float32
	othersDetectedAsWeed float32
	weedingThreshold     float32
	mindoo               float32
}

type WeedThinPerformanceValues struct {
	weedsTargetedCount         float32
	weedsTotalCount            float32
	cropsTargetedCount         float32
	cropsTotalCount            float32
	weedsThinningTargetedCount float32
	weedsThinningTotalCount    float32
	cropsThinningTargetedCount float32
	cropsThinningTotalCount    float32
	weedingThreshold           float32
	thinningThreshold          float32
	mindoo                     float32
}

type WeedThinConfigurationKey struct {
	weedingThreshold  float32
	thinningThreshold float32
	mindoo            float32
}

type PercentageLimits struct {
	minMindooPercentage        int
	maxMindooPercentage        int
	minWeedThresholdPercentage int
	maxWeedThresholdPercentage int
	minCropThresholdPercentage int
	maxCropThresholdPercentage int
}

package captcha

import (
	"context"
	"math"
	"sort"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
)

type CategorySizeWeedingThinningPlantCaptchaAlgorithm struct{}

func (s *CategorySizeWeedingThinningPlantCaptchaAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	captchaResults, labelCounts, categoryToSizes := splitCaptchasCategorySize(results)

	percentageLimits := getPercentageLimits(results)

	modelinator := results.CurrentParameters

	if labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED) < 1 || labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP) < 1 {
		logrus.Error("Could not calculate weeding_thinning recommendation, missing either weed labels, crop labels, or both")
		return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NOT_ENOUGH_ITEMS}, nil
	}

	cropMetrics := make(map[string][]WeedingThinningMindooMetrics, 0)
	cropAverages := make(map[string][]float64, 0)
	cropBestValue := make(map[string]float32, 0)
	cropBestValue["small"] = float32(1)
	cropBestValue["medium"] = float32(1)
	cropBestValue["large"] = float32(1)

	allCrops := make([]*frontend.PlantCaptchaResult, 0)
	allWeeds := make([]*frontend.PlantCaptchaResult, 0)

	catSizes := categoryToSizes.get("CROP")
	cropSize1 := catSizes[0]
	cropSize2 := catSizes[1]

	currentCropModelinator := getCurrentCropParamConfiguration(modelinator)

	cropSizeToWeeds := make(map[string][]*frontend.PlantCaptchaResult, 0)

	for _, sizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED] {
		for _, res := range sizes {
			allWeeds = append(allWeeds, res...)
			for _, r := range res {
				if r.GetMetadata().GetSizeMm() < cropSize1 {
					cropSizeToWeeds["small"] = append(cropSizeToWeeds["small"], r)
				} else if r.GetMetadata().GetSizeMm() < cropSize2 {
					cropSizeToWeeds["medium"] = append(cropSizeToWeeds["medium"], r)
				} else {
					cropSizeToWeeds["large"] = append(cropSizeToWeeds["large"], r)
				}
			}
		}
	}

	currentWeedingPerformance := make(map[string]float32, 0)
	currentThinningPerformance := make(map[string]float32, 0)
	for _, sizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_CROP] {
		for size, res := range sizes {
			allCrops = append(allCrops, res...)
			var currentWeedingThreshold float32
			var currentThinningThreshold float32
			var currentMindoo float32
			if size == "small" {
				currentWeedingThreshold = currentCropModelinator.smallWeedingThreshold
				currentThinningThreshold = currentCropModelinator.smallThinningThreshold
				currentMindoo = currentCropModelinator.smallMindoo
			} else if size == "medium" {
				currentWeedingThreshold = currentCropModelinator.mediumWeedingThreshold
				currentThinningThreshold = currentCropModelinator.mediumThinningThreshold
				currentMindoo = currentCropModelinator.mediumMindoo
			} else if size == "large" {
				currentWeedingThreshold = currentCropModelinator.largeWeedingThreshold
				currentThinningThreshold = currentCropModelinator.largeThinningThreshold
				currentMindoo = currentCropModelinator.largeMindoo
			}
			missedAtCurrentThresholdMindoo := 1 - calculateDetectedWithMindoo(currentWeedingThreshold, currentMindoo, res, weed_tracking.PlantCaptchaUserPrediction_CROP)
			weeds, ok := cropSizeToWeeds[size]
			weedsDetectedAsCrops := float32(0)

			if ok {
				weedsDetectedAsCrops = calculateDetectedWithMindoo(currentThinningThreshold, currentMindoo, weeds, weed_tracking.PlantCaptchaUserPrediction_CROP)
			}

			currentWeedingPerformance[size] = missedAtCurrentThresholdMindoo
			currentThinningPerformance[size] = weedsDetectedAsCrops
		}
	}

	logrus.Infof("All weeds %v", len(allWeeds))

	for **************** := percentageLimits.minMindooPercentage; **************** <= percentageLimits.maxMindooPercentage; **************** += 10 {
		mindoo := float32(****************) / float32(100)
		for weedingThresholdPercentage := percentageLimits.minCropThresholdPercentage; weedingThresholdPercentage < percentageLimits.maxCropThresholdPercentage; weedingThresholdPercentage += 10 {
			weedingThreshold := float32(weedingThresholdPercentage) / float32(100)
			for thinningThresholdPercentage := percentageLimits.minCropThresholdPercentage; thinningThresholdPercentage < percentageLimits.maxCropThresholdPercentage; thinningThresholdPercentage += 10 {
				thinningThreshold := float32(thinningThresholdPercentage) / float32(100)

				for category, sizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_CROP] {
					for size, res := range sizes {
						weeds, ok := cropSizeToWeeds[size]
						weedsDetectedAsCrops := float32(0)

						if ok {
							weedsDetectedAsCrops = calculateDetectedWithMindoo(thinningThreshold, mindoo, weeds, weed_tracking.PlantCaptchaUserPrediction_CROP)
						}
						cropsMissed := 1 - calculateDetectedWithMindoo(weedingThreshold, mindoo, res, weed_tracking.PlantCaptchaUserPrediction_CROP)

						average := (cropsMissed + weedsDetectedAsCrops) / float32(2)
						currentWeedingPerformance, ok := currentWeedingPerformance[size]
						if !ok {
							logrus.Errorf("Don't have current weeding performance for %v crops", size)
						}
						currentThinningPerformance, ok := currentThinningPerformance[size]
						if !ok {
							logrus.Errorf("Don't have current thinning performance for %v crops", size)
						}
						currentAverage := (currentWeedingPerformance + currentThinningPerformance) / float32(0)
						if average <= currentAverage {
							cropMetrics[size] = append(cropMetrics[size], WeedingThinningMindooMetrics{weedingThreshold: weedingThreshold, thinningThreshold: thinningThreshold, mindoo: mindoo, weedsDetectedAsCrops: weedsDetectedAsCrops, cropsMissed: cropsMissed, category: category, size: size, average: float64(average)})
							cropAverages[size] = append(cropAverages[size], float64(average))
						}
					}
				}
			}
		}
	}

	var cropModelinatorSettings []*almanac.ModelTrust
	for _, categ := range modelinator.Categories {
		if categ.Type.Classification == almanac.CategoryClassification_CATEGORY_CROP {
			cropModelinatorSettings = categ.Trusts
		}
	}

	// For the case we have more than 50 qualifying items, get the ones with the best crops missed metrics per size
	cropMetricsFiltered := make(map[string][]WeedingThinningMindooMetrics, 0)
	for size, averages := range cropAverages {
		sort.Float64s(averages)
		numberItems := len(averages)
		if numberItems > 50 {
			numberItems = 50
		}
		for _, item := range cropMetrics[size] {
			if item.average <= averages[numberItems-1] {
				cropMetricsFiltered[size] = append(cropMetricsFiltered[size], item)
			}
		}
	}

	if len(cropMetricsFiltered["small"]) == 0 {
		cropMetricsFiltered["small"] = append(cropMetricsFiltered["small"], WeedingThinningMindooMetrics{weedingThreshold: cropModelinatorSettings[0].WeedingThreshold, thinningThreshold: cropModelinatorSettings[0].ThinningThreshold, mindoo: cropModelinatorSettings[0].MinDoo, category: "CROP", size: "small"})
	}
	if len(cropMetricsFiltered["medium"]) == 0 {
		cropMetricsFiltered["medium"] = append(cropMetricsFiltered["medium"], WeedingThinningMindooMetrics{weedingThreshold: cropModelinatorSettings[1].WeedingThreshold, thinningThreshold: cropModelinatorSettings[1].ThinningThreshold, mindoo: cropModelinatorSettings[1].MinDoo, category: "CROP", size: "medium"})
	}
	if len(cropMetricsFiltered["large"]) == 0 {
		cropMetricsFiltered["large"] = append(cropMetricsFiltered["large"], WeedingThinningMindooMetrics{weedingThreshold: cropModelinatorSettings[2].WeedingThreshold, thinningThreshold: cropModelinatorSettings[2].ThinningThreshold, mindoo: cropModelinatorSettings[2].MinDoo, category: "CROP", size: "large"})
	}

	logrus.Infof("Going to check configurations %v %v %v", len(cropMetricsFiltered["small"]), len(cropMetricsFiltered["medium"]), len(cropMetricsFiltered["large"]))

	bestCropWeedConfigurations := make(map[CropThinningConfigurationKey]map[WeedConfigurationKey]WeedThinPerformanceValues)

	for weedCategory, weedSizes := range captchaResults[weed_tracking.PlantCaptchaUserPrediction_WEED] {
		if weedCategory == "CROP" {
			continue
		}
		for weedSize, weedResults := range weedSizes {
			var weedSize1 float32
			var weedSize2 float32
			if weedSize == "small" {
				weedSize1 = 0
				weedSize2 = categoryToSizes.get(weedCategory)[0]
			} else if weedSize == "medium" {
				weedSize1 = categoryToSizes.get(weedCategory)[0]
				weedSize2 = categoryToSizes.get(weedCategory)[1]
			} else {
				weedSize1 = categoryToSizes.get(weedCategory)[1]
				weedSize2 = float32(math.Inf(1))
			}
			for _, smallMetric := range cropMetricsFiltered["small"] {
				for _, mediumMetric := range cropMetricsFiltered["medium"] {
					for _, largeMetric := range cropMetricsFiltered["large"] {
						bestOverallPerformance := float32(0.0)
						var bestCombinedMetrics WeedThinPerformanceValues
						for **************** := percentageLimits.minMindooPercentage; **************** <= percentageLimits.maxMindooPercentage; **************** += 10 {
							mindoo := float32(****************) / float32(100)
							bestWeedingDifference := float32(0.0)
							var bestWeedingMetrics WeedThinPerformanceValues
							for weedingThresholdPercentage := percentageLimits.minWeedThresholdPercentage; weedingThresholdPercentage < percentageLimits.maxWeedThresholdPercentage; weedingThresholdPercentage += 10 {
								weedingThreshold := float32(weedingThresholdPercentage) / float32(100)
								weedsTargetedItems, weedsTotalItems := calculateItemsTargetedWithinWeedSize(weedingThreshold, mindoo, smallMetric.weedingThreshold, smallMetric.mindoo, mediumMetric.weedingThreshold, mediumMetric.mindoo, largeMetric.weedingThreshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, weedResults, true)
								cropsTargetedItems, cropsTotalItems := calculateItemsTargetedWithinWeedSize(weedingThreshold, mindoo, smallMetric.weedingThreshold, smallMetric.mindoo, mediumMetric.weedingThreshold, mediumMetric.mindoo, largeMetric.weedingThreshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, allCrops, true)
								weedsTargetedCount := float32(len(weedsTargetedItems))
								weedsTotalCount := float32(len(weedsTotalItems))
								cropsTargetedCount := float32(len(cropsTargetedItems))
								cropsTotalCount := float32(len(cropsTotalItems))
								weedsTargeted := weedsTargetedCount / (weedsTotalCount + 1e-6)
								cropsTargeted := cropsTargetedCount / (cropsTotalCount + 1e-6)

								difference := weedsTargeted - cropsTargeted
								if difference >= bestWeedingDifference {
									bestWeedingDifference = difference
									bestWeedingMetrics = WeedThinPerformanceValues{
										weedsTargetedCount: weedsTargetedCount,
										weedsTotalCount:    weedsTotalCount,
										cropsTargetedCount: cropsTargetedCount,
										cropsTotalCount:    cropsTotalCount,
										weedingThreshold:   weedingThreshold,
										mindoo:             mindoo,
									}
								}
							}

							bestThinningDifference := float32(0.0)
							var bestThinningMetrics WeedThinPerformanceValues
							for thinningThresholdPercentage := percentageLimits.minWeedThresholdPercentage; thinningThresholdPercentage < percentageLimits.maxWeedThresholdPercentage; thinningThresholdPercentage += 10 {
								thinningThreshold := float32(thinningThresholdPercentage) / float32(100)
								cropsThinnedItems, cropsTotalItems := calculateItemsTargetedWithinWeedSize(thinningThreshold, mindoo, smallMetric.thinningThreshold, smallMetric.mindoo, mediumMetric.thinningThreshold, mediumMetric.mindoo, largeMetric.thinningThreshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, allCrops, false)
								cropsThinned := float32(len(cropsThinnedItems)) / (float32(len(cropsTotalItems)) + 1e-6)
								weedsThinnedItems, weedsTotalItems := calculateItemsTargetedWithinWeedSize(thinningThreshold, mindoo, smallMetric.thinningThreshold, smallMetric.mindoo, mediumMetric.thinningThreshold, mediumMetric.mindoo, largeMetric.thinningThreshold, largeMetric.mindoo, cropSize1, cropSize2, weedSize1, weedSize2, weedResults, false)
								weedsThinned := float32(len(weedsThinnedItems)) / (float32(len(weedsTotalItems)) + 1e-6)
								difference := cropsThinned - weedsThinned
								if difference >= bestThinningDifference {
									bestThinningDifference = difference
									bestThinningMetrics = WeedThinPerformanceValues{
										weedsThinningTargetedCount: float32(len(weedsThinnedItems)),
										weedsThinningTotalCount:    float32(len(weedsTotalItems)),
										cropsThinningTargetedCount: float32(len(cropsThinnedItems)),
										cropsThinningTotalCount:    float32(len(cropsTotalItems)),
										thinningThreshold:          thinningThreshold,
										mindoo:                     mindoo,
									}
								}
							}

							if (bestWeedingMetrics.cropsTargetedCount)/(bestWeedingMetrics.cropsTotalCount+1e-6) > results.GetGoalCropsTargeted() {
								continue
							}

							overallPerformance := (2 * bestWeedingDifference * bestThinningDifference) / (bestWeedingDifference + bestThinningDifference + 1e-6)

							if overallPerformance > bestOverallPerformance {
								bestOverallPerformance = overallPerformance
								bestCombinedMetrics = WeedThinPerformanceValues{
									weedsTargetedCount:         bestWeedingMetrics.weedsTargetedCount,
									weedsTotalCount:            bestWeedingMetrics.weedsTotalCount,
									cropsTargetedCount:         bestWeedingMetrics.cropsTargetedCount,
									cropsTotalCount:            bestWeedingMetrics.cropsTotalCount,
									weedsThinningTargetedCount: bestThinningMetrics.weedsThinningTargetedCount,
									weedsThinningTotalCount:    bestThinningMetrics.weedsThinningTotalCount,
									cropsThinningTargetedCount: bestThinningMetrics.cropsThinningTargetedCount,
									cropsThinningTotalCount:    bestThinningMetrics.cropsThinningTotalCount,
									weedingThreshold:           bestWeedingMetrics.weedingThreshold,
									thinningThreshold:          bestThinningMetrics.thinningThreshold,
									mindoo:                     mindoo,
								}
							}
						}
						cropConfigurationKey := CropThinningConfigurationKey{
							cropSmallWeedingThresh:   smallMetric.weedingThreshold,
							cropSmallThinningThresh:  smallMetric.thinningThreshold,
							cropSmallMindoo:          smallMetric.mindoo,
							cropMediumWeedingThresh:  mediumMetric.weedingThreshold,
							cropMediumThinningThresh: mediumMetric.thinningThreshold,
							cropMediumMindoo:         mediumMetric.mindoo,
							cropLargeWeedingThresh:   largeMetric.weedingThreshold,
							cropLargeThinningThresh:  largeMetric.thinningThreshold,
							cropLargeMindoo:          largeMetric.mindoo,
						}

						weedConfigurationKey := WeedConfigurationKey{
							weedCategory: weedCategory,
							weedSize:     weedSize,
						}
						if bestOverallPerformance == 0 {
							// If we didn't find anything better than default, then we wont add it as a "best result"
							continue
						}
						if _, ok := bestCropWeedConfigurations[cropConfigurationKey]; !ok {
							bestCropWeedConfigurations[cropConfigurationKey] = map[WeedConfigurationKey]WeedThinPerformanceValues{}
						}

						bestItem, ok := bestCropWeedConfigurations[cropConfigurationKey][weedConfigurationKey]

						if !ok {
							if bestCombinedMetrics.weedingThreshold == 0 || bestCombinedMetrics.thinningThreshold == 0 {
								logrus.Infof("Got bad thresholds at %v", bestCombinedMetrics)
							}
							bestCropWeedConfigurations[cropConfigurationKey][weedConfigurationKey] = bestCombinedMetrics
						} else {
							weedsTargeted := bestCombinedMetrics.weedsTargetedCount / bestCombinedMetrics.weedsTotalCount
							cropsTargeted := bestCombinedMetrics.cropsTargetedCount / bestCombinedMetrics.cropsTotalCount
							cropsThinned := bestCombinedMetrics.cropsThinningTargetedCount / bestCombinedMetrics.cropsThinningTotalCount
							weedsThinned := bestCombinedMetrics.weedsThinningTargetedCount / bestCombinedMetrics.weedsThinningTotalCount
							bestWeedsTargeted := bestItem.weedsTargetedCount / bestItem.weedsTotalCount
							bestCropsTargeted := bestItem.cropsTargetedCount / bestItem.cropsTotalCount
							bestCropsThinned := bestItem.cropsThinningTargetedCount / bestItem.cropsThinningTotalCount
							bestWeedsThinned := bestItem.weedsThinningTargetedCount / bestItem.weedsThinningTotalCount

							weedingDifference := weedsTargeted - cropsTargeted
							thinningDifference := cropsThinned - weedsThinned

							bestWeedingDifference := bestWeedsTargeted - bestCropsTargeted
							bestThinningDifference := bestCropsThinned - bestWeedsThinned

							harmonicMean := (2 * weedingDifference * thinningDifference) / (weedingDifference + thinningDifference + 1e-6)
							bestHarmonicMean := (2 * bestWeedingDifference * bestThinningDifference) / (bestWeedingDifference + bestThinningDifference + 1e-6)
							if harmonicMean >= bestHarmonicMean {
								bestCropWeedConfigurations[cropConfigurationKey][weedConfigurationKey] = bestCombinedMetrics
							}
						}
					}
				}
			}
		}
	}

	var bestCropConfiguration CropThinningConfigurationKey
	bestOverallPerformance := float32(0)
	bestWeedsTargeted := float32(0)
	bestCropsThinned := float32(0)

	validConfiguarionExists := false

	for cropConfiguration, weedMap := range bestCropWeedConfigurations {
		weedsTargetedCount := float32(0)
		weedsTotalCount := float32(0)
		cropsTargetedCount := float32(0)
		cropsTotalCount := float32(0)
		cropsThinnedCount := float32(0)
		cropsThinnedTotalCount := float32(0)
		weedsThinnedCount := float32(0)
		weedsThinnedTotalCount := float32(0)

		for _, weedValues := range weedMap {
			weedsTargetedCount += weedValues.weedsTargetedCount
			weedsTotalCount += weedValues.weedsTotalCount
			cropsTargetedCount += weedValues.cropsTargetedCount
			cropsTotalCount += weedValues.cropsTotalCount
			cropsThinnedCount += weedValues.cropsThinningTargetedCount
			cropsThinnedTotalCount += weedValues.cropsThinningTotalCount
			weedsThinnedCount += weedValues.weedsThinningTargetedCount
			weedsThinnedTotalCount += weedValues.weedsThinningTotalCount
		}

		weedsTargeted := weedsTargetedCount / weedsTotalCount
		cropsTargeted := cropsTargetedCount / cropsTotalCount
		cropsThinned := cropsThinnedCount / cropsThinnedTotalCount
		weedsThinned := weedsThinnedCount / weedsThinnedTotalCount

		weedingDifference := weedsTargeted - cropsTargeted
		thinningDifference := cropsThinned - weedsThinned

		overallPerformance := (2 * weedingDifference * thinningDifference) / (weedingDifference + thinningDifference + 1e-6)

		if overallPerformance > bestOverallPerformance {
			validConfiguarionExists = true
			bestOverallPerformance = overallPerformance
			bestCropConfiguration = cropConfiguration
			bestWeedsTargeted = weedsTargeted
			bestCropsThinned = cropsThinned
		}
	}

	if !validConfiguarionExists {
		logrus.Error("Could not calculate weeding recommendation, no bestCropWeedConfigurations")
		return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: results.GetCurrentParameters(), Succeeded: false, FailureReason: frontend.PlantLabelAlgorithmFailureReason_METRICS_NOT_MET}, nil
	}

	logrus.Infof("CategorySizeWeedingThinningPlantCaptchaAlgorithm ran with overall bestOverallPerformance %v, bestWeedsTargeted %v, bestCropsThinned %v, bestCropConfiguration %v, bestWeedConfigurations %v", bestOverallPerformance, bestWeedsTargeted, bestCropsThinned, bestCropConfiguration, bestCropWeedConfigurations[bestCropConfiguration])

	for _, param := range modelinator.GetCategories() {
		paramType := param.GetType()
		if paramType == nil {
			logrus.Warn("PlantCaptcha: Skipping category because there's no associated Type")
			continue
		}
		if paramType.GetClassification() == almanac.CategoryClassification_CATEGORY_CROP {
			logrus.Infof("Crop small wThresh: %v tThresh: %v Mindoo: %v, medium wThresh: %v tThresh: %v Mindoo: %v, large wThresh: %v tThresh: %v Mindoo %v", bestCropConfiguration.cropSmallWeedingThresh, bestCropConfiguration.cropSmallThinningThresh, bestCropConfiguration.cropSmallMindoo, bestCropConfiguration.cropMediumWeedingThresh, bestCropConfiguration.cropMediumThinningThresh, bestCropConfiguration.cropMediumMindoo, bestCropConfiguration.cropLargeWeedingThresh, bestCropConfiguration.cropLargeThinningThresh, bestCropConfiguration.cropLargeMindoo)
			param.Trusts[0].WeedingThreshold = bestCropConfiguration.cropSmallWeedingThresh
			param.Trusts[0].ThinningThreshold = bestCropConfiguration.cropSmallThinningThresh
			param.Trusts[0].MinDoo = bestCropConfiguration.cropSmallMindoo
			param.Trusts[1].WeedingThreshold = bestCropConfiguration.cropMediumWeedingThresh
			param.Trusts[1].ThinningThreshold = bestCropConfiguration.cropMediumThinningThresh
			param.Trusts[1].MinDoo = bestCropConfiguration.cropMediumMindoo
			param.Trusts[2].WeedingThreshold = bestCropConfiguration.cropLargeWeedingThresh
			param.Trusts[2].ThinningThreshold = bestCropConfiguration.cropLargeThinningThresh
			param.Trusts[2].MinDoo = bestCropConfiguration.cropLargeMindoo
		} else {
			category := paramType.GetCategory()
			if category == "" {
				logrus.Warn("PlantCaptcha: Skipping category because it doesn't have a category")
				continue
			}
			weedCategoryKeySmall := WeedConfigurationKey{
				weedCategory: translateCategory(category),
				weedSize:     "small",
			}
			weedCategoryKeyMedium := WeedConfigurationKey{
				weedCategory: translateCategory(category),
				weedSize:     "medium",
			}
			weedCategoryKeyLarge := WeedConfigurationKey{
				weedCategory: translateCategory(category),
				weedSize:     "large",
			}
			for ind, key := range []WeedConfigurationKey{weedCategoryKeySmall, weedCategoryKeyMedium, weedCategoryKeyLarge} {
				item, ok := bestCropWeedConfigurations[bestCropConfiguration][key]

				if ok {
					logrus.Infof("Setting for %v: weeding threshold %v, thinning threshold %v, mindoo %v", key, item.weedingThreshold, item.thinningThreshold, item.mindoo)
					param.Trusts[ind].WeedingThreshold = item.weedingThreshold
					param.Trusts[ind].ThinningThreshold = item.thinningThreshold
					param.Trusts[ind].MinDoo = item.mindoo
				}
			}
		}

	}

	return &frontend.CalculatePlantCaptchaResponse{ModelinatorConfig: modelinator, Succeeded: true, FailureReason: frontend.PlantLabelAlgorithmFailureReason_NO_FAILURE}, nil
}

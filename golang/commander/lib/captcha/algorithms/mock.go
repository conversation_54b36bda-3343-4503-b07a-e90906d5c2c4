package captcha

import (
	"context"
	"math/rand"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
)

type MockPlantCaptchaAlgorithm struct{}

func (m *MockPlantCaptchaAlgorithm) Submit(ctx context.Context, plantCaptcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) (*frontend.CalculatePlantCaptchaResponse, error) {
	resp := &frontend.CalculatePlantCaptchaResponse{
		ModelinatorConfig: &almanac.ModelinatorConfig{
			ModelId: plantCaptcha.ModelId,
			CropId:  plantCaptcha.CropId,
			Categories: []*almanac.ModelinatorTypeCategory{
				&almanac.ModelinatorTypeCategory{
					Type: &almanac.TypeCategory{
						Category:       plantCaptcha.CropId,
						Classification: almanac.CategoryClassification_CATEGORY_CROP,
					},
					Trusts: []*almanac.ModelTrust{
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
					},
				},
				&almanac.ModelinatorTypeCategory{
					Type: &almanac.TypeCategory{
						Category:       "9fd4e9d0-2d01-4ae1-89c5-f4b4f6de9a3c",
						Classification: almanac.CategoryClassification_CATEGORY_WEED,
					},
					Trusts: []*almanac.ModelTrust{
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
					},
				},
				&almanac.ModelinatorTypeCategory{
					Type: &almanac.TypeCategory{
						Category:       "f032b206-92cf-400a-b79e-e22bdb7930e3",
						Classification: almanac.CategoryClassification_CATEGORY_WEED,
					},
					Trusts: []*almanac.ModelTrust{
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
					},
				},
				&almanac.ModelinatorTypeCategory{
					Type: &almanac.TypeCategory{
						Category:       "2e678c61-f476-4485-aa9c-6a0e4bc7c626",
						Classification: almanac.CategoryClassification_CATEGORY_WEED,
					},
					Trusts: []*almanac.ModelTrust{
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
					},
				},
				&almanac.ModelinatorTypeCategory{
					Type: &almanac.TypeCategory{
						Category:       "1659226a-fe1c-450c-828f-88dc8819c25d",
						Classification: almanac.CategoryClassification_CATEGORY_WEED,
					},
					Trusts: []*almanac.ModelTrust{
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
						&almanac.ModelTrust{
							MinDoo:            rand.Float32(),
							WeedingThreshold:  rand.Float32(),
							ThinningThreshold: rand.Float32(),
							BandingThreshold:  rand.Float32(),
						},
					},
				},
			},
		},
	}
	return resp, nil
}

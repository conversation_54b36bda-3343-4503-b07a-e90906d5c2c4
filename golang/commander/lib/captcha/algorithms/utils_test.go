package captcha

import (
	"context"
	"math"
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/stretchr/testify/assert"
)

func GenerateAlgorithmInputs(algorithm string, goalCropsTargeted, goalWeedsTargeted, goalUnknownTargeted float32) (ctx context.Context, captcha *frontend.PlantCaptcha, results *frontend.PlantCaptchaResults) {
	captcha = &frontend.PlantCaptcha{
		Name:        "name1",
		ModelId:     "model1",
		CropId:      "cropId1",
		CropName:    "cropName1",
		StartTimeMs: 100,
	}

	results = &frontend.PlantCaptchaResults{
		CaptchaResults:      make([]*frontend.PlantCaptchaResult, 0),
		CurrentParameters:   &almanac.ModelinatorConfig{},
		Algorithm:           algorithm,
		GoalCropsTargeted:   goalCropsTargeted,
		GoalWeedsTargeted:   goalWeedsTargeted,
		GoalUnknownTargeted: goalUnknownTargeted,
	}

	results.CurrentParameters.ModelId = "model1"
	results.CurrentParameters.CropId = "CROP"
	results.CurrentParameters.Modified = false

	cropCategory := &almanac.ModelinatorTypeCategory{
		Type: &almanac.TypeCategory{
			Category:       "CROP",
			Classification: almanac.CategoryClassification_CATEGORY_CROP,
		},
		Trusts: []*almanac.ModelTrust{&almanac.ModelTrust{WeedingThreshold: 0.12}, &almanac.ModelTrust{WeedingThreshold: 0.34}, &almanac.ModelTrust{WeedingThreshold: 0.45}},
	}

	weedCategory1 := &almanac.ModelinatorTypeCategory{
		Type: &almanac.TypeCategory{
			Category:       "BROADLEAF",
			Classification: almanac.CategoryClassification_CATEGORY_WEED,
		},
		Trusts: []*almanac.ModelTrust{&almanac.ModelTrust{WeedingThreshold: 0.34}, &almanac.ModelTrust{WeedingThreshold: 0.45}, &almanac.ModelTrust{WeedingThreshold: 0.56}},
	}

	weedCategory2 := &almanac.ModelinatorTypeCategory{
		Type: &almanac.TypeCategory{
			Category:       "GRASS",
			Classification: almanac.CategoryClassification_CATEGORY_WEED,
		},
		Trusts: []*almanac.ModelTrust{&almanac.ModelTrust{WeedingThreshold: 0.34}, &almanac.ModelTrust{WeedingThreshold: 0.45}, &almanac.ModelTrust{WeedingThreshold: 0.56}},
	}

	results.CurrentParameters.Categories = []*almanac.ModelinatorTypeCategory{cropCategory, weedCategory1, weedCategory2}

	almanacTypeCategories := []*almanac.AlmanacTypeCategory{}
	almanacTypeCategories = append(almanacTypeCategories, &almanac.AlmanacTypeCategory{
		Type: &almanac.TypeCategory{
			Category: "CROP",
		},
		Sizes: []float32{11, 30},
	})
	almanacTypeCategories = append(almanacTypeCategories, &almanac.AlmanacTypeCategory{
		Type: &almanac.TypeCategory{
			Category: "BROADLEAF",
		},
		Sizes: []float32{11, 30},
	})
	almanacTypeCategories = append(almanacTypeCategories, &almanac.AlmanacTypeCategory{
		Type: &almanac.TypeCategory{
			Category: "GRASS",
		},
		Sizes: []float32{9, 29},
	})

	results.Almanac = &almanac.AlmanacConfig{
		Id:         "test_almanac",
		Name:       "TestAlmanac",
		Categories: almanacTypeCategories,
	}

	ctx = context.TODO()

	return
}

func GenerateCaptchaResults(base, mispredictedWeeds, mispredictedCrops bool) []*frontend.PlantCaptchaResult {
	captchaResults := make([]*frontend.PlantCaptchaResult, 0)
	if base {
		captchaResults = []*frontend.PlantCaptchaResult{
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.8, 0.7, 0.6},
					CropConfidenceHistory: []float32{0.2, 0.3, 0.3},
					SizeMm:                22,
					Doo:                   1.0,
					Categories:            map[string]float32{"BROADLEAF": 1.0, "GRASS": 0.5},
					WeedCategories:        map[string]float32{"BROADLEAF": 1.0, "GRASS": 0.5},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.6, 0.6, 0.6},
					CropConfidenceHistory: []float32{0.2, 0.3, 0.3},
					SizeMm:                10,
					Doo:                   1.0,
					Categories:            map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_CROP,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.2, 0.3, 0.2},
					CropConfidenceHistory: []float32{0.5, 0.6, 0.7},
					SizeMm:                25,
					Doo:                   1.0,
					Categories:            map[string]float32{"CROP": 0.6},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 0.3},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_CROP,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.2, 0.3, 0.2},
					CropConfidenceHistory: []float32{0.72, 0.7, 0.7},
					SizeMm:                3,
					Doo:                   1.0,
					Categories:            map[string]float32{"CROP": 0.3},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.3, "GRASS": 0.3},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_UNKNOWN,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.3, 0.3, 0.3},
					CropConfidenceHistory: []float32{0.3, 0.3, 0.3},
					SizeMm:                14,
					Doo:                   1.0,
					Categories:            map[string]float32{"CROP": 0.6},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_IGNORE,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.3, 0.3, 0.3},
					CropConfidenceHistory: []float32{0.3, 0.3, 0.3},
					SizeMm:                14,
					Doo:                   1.0,
					Categories:            map[string]float32{"CROP": 0.6},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_OTHER,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.3, 0.4, 0.3},
					CropConfidenceHistory: []float32{0.2, 0.3, 0.6},
					SizeMm:                12,
					Doo:                   1.0,
					Categories:            map[string]float32{"CROP": 0.7},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.75, "GRASS": 0.90},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_OTHER,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.6, 0.6, 0.5},
					CropConfidenceHistory: []float32{0.2, 0.3, 0.6},
					SizeMm:                8,
					Doo:                   1.0,
					Categories:            map[string]float32{"BROADLEAF": 0.9, "GRASS": 0.80},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.9, "GRASS": 0.80},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_VOLUNTEER,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.6, 0.6, 0.5},
					CropConfidenceHistory: []float32{0.2, 0.3, 0.6},
					SizeMm:                8,
					Doo:                   1.0,
					Categories:            map[string]float32{"BROADLEAF": 0.9, "GRASS": 0.80},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.9, "GRASS": 0.80},
				},
			},
			&frontend.PlantCaptchaResult{
				Label: weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL,
				Metadata: &weed_tracking.PlantCaptchaItemMetadata{
					WeedConfidenceHistory: []float32{0.6, 0.6, 0.5},
					CropConfidenceHistory: []float32{0.2, 0.3, 0.6},
					SizeMm:                8,
					Doo:                   1.0,
					Categories:            map[string]float32{"BROADLEAF": 0.9, "GRASS": 0.80},
					WeedCategories:        map[string]float32{"BROADLEAF": 0.9, "GRASS": 0.80},
				},
			},
		}
	}

	if mispredictedWeeds {
		captchaResults = append(captchaResults, &frontend.PlantCaptchaResult{
			Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
			Metadata: &weed_tracking.PlantCaptchaItemMetadata{
				WeedConfidenceHistory: []float32{0.3, 0.4, 0.3},
				CropConfidenceHistory: []float32{0.6, 0.5, 0.6},
				SizeMm:                8,
				Doo:                   1.0,
				Categories:            map[string]float32{"CROP": 0.6},
				WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
			},
		})
	}

	if mispredictedCrops {
		captchaResults = append(captchaResults, &frontend.PlantCaptchaResult{
			Label: weed_tracking.PlantCaptchaUserPrediction_CROP,
			Metadata: &weed_tracking.PlantCaptchaItemMetadata{
				WeedConfidenceHistory: []float32{0.6, 0.5, 0.6},
				CropConfidenceHistory: []float32{0.3, 0.3, 0.3},
				SizeMm:                24,
				Doo:                   1.0,
				Categories:            map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
				WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
			},
		})
	}

	return captchaResults
}

func GenerateCaptchaResultsNoCropLabels() []*frontend.PlantCaptchaResult {
	captchaResults := make([]*frontend.PlantCaptchaResult, 0)
	captchaResults = []*frontend.PlantCaptchaResult{
		&frontend.PlantCaptchaResult{
			Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
			Metadata: &weed_tracking.PlantCaptchaItemMetadata{
				WeedConfidenceHistory: []float32{0.8, 0.7, 0.6},
				CropConfidenceHistory: []float32{0.2, 0.3, 0.3},
				SizeMm:                22,
				Doo:                   1.0,
				Categories:            map[string]float32{"BROADLEAF": 1.0, "GRASS": 0.5},
				WeedCategories:        map[string]float32{"BROADLEAF": 1.0, "GRASS": 0.5},
			},
		},
		&frontend.PlantCaptchaResult{
			Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
			Metadata: &weed_tracking.PlantCaptchaItemMetadata{
				WeedConfidenceHistory: []float32{0.6, 0.6, 0.6},
				CropConfidenceHistory: []float32{0.2, 0.3, 0.3},
				SizeMm:                10,
				Doo:                   1.0,
				Categories:            map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
				WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 1.0},
			},
		},
		&frontend.PlantCaptchaResult{
			Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
			Metadata: &weed_tracking.PlantCaptchaItemMetadata{
				WeedConfidenceHistory: []float32{0.2, 0.3, 0.2},
				CropConfidenceHistory: []float32{0.5, 0.6, 0.7},
				SizeMm:                25,
				Doo:                   1.0,
				Categories:            map[string]float32{"CROP": 0.6},
				WeedCategories:        map[string]float32{"BROADLEAF": 0.5, "GRASS": 0.3},
			},
		},
		&frontend.PlantCaptchaResult{
			Label: weed_tracking.PlantCaptchaUserPrediction_WEED,
			Metadata: &weed_tracking.PlantCaptchaItemMetadata{
				WeedConfidenceHistory: []float32{0.2, 0.3, 0.2},
				CropConfidenceHistory: []float32{0.72, 0.7, 0.7},
				SizeMm:                3,
				Doo:                   1.0,
				Categories:            map[string]float32{"CROP": 0.3},
				WeedCategories:        map[string]float32{"BROADLEAF": 0.4, "GRASS": 0.3},
			},
		},
	}

	return captchaResults
}

func sumCategorySize(outMap map[weed_tracking.PlantCaptchaUserPrediction]map[string]map[string][]*frontend.PlantCaptchaResult, category, size string) int {
	count := 0

	for _, categoryMap := range outMap {
		sizeMap, ok := categoryMap[category]
		if ok {
			items, ok := sizeMap[size]
			if ok {
				count += len(items)
			}
		}
	}

	return count
}

func TestSplitCaptchasCategorySize(t *testing.T) {
	tests := []struct {
		name                           string
		simple                         bool
		mispredictedWeeds              bool
		mispredictedCrops              bool
		useWeedCategoriesForWeedLabels bool
		expectedWeeds                  int
		expectedCrops                  int
		expectedUnknowns               int
		expectedOthers                 int
		expectedIgnores                int
		expectedWeedCategories         int
		expectedCropCategories         int
		expectedUnknownCategories      int
		expectedOtherCategories        int
		expectedIgnoreCategories       int
		expectedBroadleafSmall         int
		expectedBroadleafMedium        int
		expectedBroadleafLarge         int
		expectedGrassSmall             int
		expectedGrassMedium            int
		expectedGrassLarge             int
		expectedCropSmall              int
		expectedCropMedium             int
		expectedCropLarge              int
	}{
		{"simple", true, false, false, false, 2, 2, 1, 2, 1, 2, 1, 1, 2, 1, 3, 1, 0, 0, 1, 0, 1, 4, 0},
		{"mispredicted weed", true, true, false, false, 3, 2, 1, 2, 1, 3, 1, 1, 2, 1, 3, 1, 0, 0, 1, 0, 2, 4, 0},
		{"mispredicted crop", true, false, true, false, 2, 3, 1, 2, 1, 2, 1, 1, 2, 1, 3, 1, 0, 0, 1, 0, 1, 5, 0},
		{"mispredicted both", true, true, true, false, 3, 3, 1, 2, 1, 3, 1, 1, 2, 1, 3, 1, 0, 0, 1, 0, 2, 5, 0},
		{"simple use_weeds_for_weeds", true, false, false, true, 2, 2, 1, 2, 1, 2, 1, 1, 2, 1, 3, 1, 0, 0, 4, 0, 1, 1, 0},
		{"mispredicted weed use_weeds_for_weeds", true, true, false, true, 3, 2, 1, 2, 1, 2, 1, 1, 2, 1, 3, 1, 0, 1, 4, 0, 1, 1, 0},
		{"mispredicted crop use_weeds_for_weeds", true, false, true, true, 2, 3, 1, 2, 1, 2, 1, 1, 2, 1, 3, 1, 0, 0, 4, 0, 1, 2, 0},
		{"mispredicted both use_weeds_for_weeds", true, true, true, true, 3, 3, 1, 2, 1, 2, 1, 1, 2, 1, 3, 1, 0, 1, 4, 0, 1, 2, 0},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, _, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.CaptchaResults = GenerateCaptchaResults(test.simple, test.mispredictedWeeds, test.mispredictedCrops)
			results.UseWeedCategoriesForWeedLabels = test.useWeedCategoriesForWeedLabels
			outMap, labelCounts, _ := splitCaptchasCategorySize(results)

			assert.Equal(t, test.expectedCrops, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP))
			assert.Equal(t, test.expectedWeeds, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED))
			assert.Equal(t, test.expectedUnknowns, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_UNKNOWN))
			assert.Equal(t, test.expectedOthers, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_OTHER))
			assert.Equal(t, test.expectedIgnores, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_IGNORE))

			assert.Equal(t, test.expectedWeedCategories, len(outMap[weed_tracking.PlantCaptchaUserPrediction_WEED]))
			assert.Equal(t, test.expectedCropCategories, len(outMap[weed_tracking.PlantCaptchaUserPrediction_CROP]))
			assert.Equal(t, test.expectedUnknownCategories, len(outMap[weed_tracking.PlantCaptchaUserPrediction_UNKNOWN]))
			assert.Equal(t, test.expectedOtherCategories, len(outMap[weed_tracking.PlantCaptchaUserPrediction_OTHER]))
			assert.Equal(t, test.expectedIgnoreCategories, len(outMap[weed_tracking.PlantCaptchaUserPrediction_IGNORE]))

			assert.Equal(t, test.expectedBroadleafSmall, sumCategorySize(outMap, "BROADLEAF", "small"))
			assert.Equal(t, test.expectedBroadleafMedium, sumCategorySize(outMap, "BROADLEAF", "medium"))
			assert.Equal(t, test.expectedBroadleafLarge, sumCategorySize(outMap, "BROADLEAF", "large"))

			assert.Equal(t, test.expectedGrassSmall, sumCategorySize(outMap, "GRASS", "small"))
			assert.Equal(t, test.expectedGrassMedium, sumCategorySize(outMap, "GRASS", "medium"))
			assert.Equal(t, test.expectedGrassLarge, sumCategorySize(outMap, "GRASS", "large"))

			assert.Equal(t, test.expectedCropSmall, sumCategorySize(outMap, "CROP", "small"))
			assert.Equal(t, test.expectedCropMedium, sumCategorySize(outMap, "CROP", "medium"))
			assert.Equal(t, test.expectedCropLarge, sumCategorySize(outMap, "CROP", "large"))
		})
	}

	tests_2 := []struct {
		name                           string
		minItemsForRecommendation      int
		expectedWeeds                  int
		expectedCrops                  int
		expectedUnknowns               int
		expectedOthers                 int
		expectedVolunteers             int
		expectedBeneficial             int
		expectedDebris                 int
		useWeedCategoriesForWeedLabels bool
	}{
		{"Remove fewer than 0", 0, 3, 3, 1, 2, 1, 1, 0, true},
		{"Remove fewer than 1", 1, 3, 3, 1, 2, 1, 1, 0, true},
		{"Remove fewer than 2", 2, 0, 2, 0, 0, 0, 0, 0, true},
		{"Remove fewer than 3", 3, 0, 0, 0, 0, 0, 0, 0, true},
	}
	for _, test := range tests_2 {
		t.Run(test.name, func(t *testing.T) {
			_, _, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.CaptchaResults = GenerateCaptchaResults(true, true, true)
			results.UseWeedCategoriesForWeedLabels = test.useWeedCategoriesForWeedLabels
			results.MinItemsForRecommendation = int32(test.minItemsForRecommendation)
			_, labelCounts, _ := splitCaptchasCategorySize(results)

			assert.Equal(t, test.expectedCrops, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP))
			assert.Equal(t, test.expectedWeeds, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED))
			assert.Equal(t, test.expectedUnknowns, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_UNKNOWN))
			assert.Equal(t, test.expectedOthers, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_OTHER))
		})
	}

	tests_3 := []struct {
		name                  string
		useBeneficialsAsCrops bool
		useVolunteersAsWeeds  bool
		expectedWeeds         int
		expectedCrops         int
		expectedBeneficials   int
		expectedVolunteers    int
	}{
		{"not useBeneficialsAsCrops and not useVolunteersAsWeeds", false, false, 3, 3, 1, 1},
		{"not useBeneficialsAsCrops and useVolunteersAsWeeds", false, true, 4, 3, 1, 0},
		{"useBeneficialsAsCrops and not useVolunteersAsWeeds", true, false, 3, 4, 0, 1},
		{"useBeneficialsAsCrops and useVolunteersAsWeeds", true, true, 4, 4, 0, 0},
	}
	for _, test := range tests_3 {
		t.Run(test.name, func(t *testing.T) {
			_, _, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.UseBeneficialsAsCrops = test.useBeneficialsAsCrops
			results.UseVolunteersAsWeeds = test.useVolunteersAsWeeds
			results.CaptchaResults = GenerateCaptchaResults(true, true, true)
			_, labelCounts, _ := splitCaptchasCategorySize(results)

			assert.Equal(t, test.expectedCrops, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP))
			assert.Equal(t, test.expectedWeeds, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED))
			assert.Equal(t, test.expectedBeneficials, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL))
			assert.Equal(t, test.expectedVolunteers, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_VOLUNTEER))
			assert.Equal(t, 1, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_UNKNOWN))
			assert.Equal(t, 2, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_OTHER))
			assert.Equal(t, 1, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_IGNORE))
			assert.Equal(t, 0, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_DEBRIS))
		})
	}
}

func TestLabelCount(t *testing.T) {
	items := []weed_tracking.PlantCaptchaUserPrediction{
		weed_tracking.PlantCaptchaUserPrediction_WEED,
		weed_tracking.PlantCaptchaUserPrediction_WEED,
		weed_tracking.PlantCaptchaUserPrediction_WEED,
		weed_tracking.PlantCaptchaUserPrediction_CROP,
		weed_tracking.PlantCaptchaUserPrediction_CROP,
		weed_tracking.PlantCaptchaUserPrediction_UNKNOWN,
		weed_tracking.PlantCaptchaUserPrediction_UNKNOWN,
		weed_tracking.PlantCaptchaUserPrediction_UNKNOWN,
		weed_tracking.PlantCaptchaUserPrediction_UNKNOWN,
		weed_tracking.PlantCaptchaUserPrediction_OTHER,
		weed_tracking.PlantCaptchaUserPrediction_VOLUNTEER,
		weed_tracking.PlantCaptchaUserPrediction_VOLUNTEER,
		weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL,
		weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL,
		weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL,
	}

	labelCounts := LabelCounts{
		counts: make(map[weed_tracking.PlantCaptchaUserPrediction]int),
	}

	for _, item := range items {
		labelCounts.add(item, 1)
	}

	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_WEED), 3)
	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_CROP), 2)
	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_UNKNOWN), 4)
	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_OTHER), 1)
	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_VOLUNTEER), 2)
	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_BENEFICIAL), 3)
	assert.Equal(t, labelCounts.get(weed_tracking.PlantCaptchaUserPrediction_DEBRIS), 0)
}

func approximatelyEqual(a, b float32) bool {
	return math.Abs(float64(a)-float64(b)) <= 1e-9
}

func TestCalculateDetectedWithMindoo(t *testing.T) {
	tests := []struct {
		name                        string
		threshold                   float32
		mindoo                      float32
		expectedSmallCropsDetected  float32
		expectedMediumCropsDetected float32
	}{
		{"low threshold and low mindoo", 0.1, 0, 1, 1},
		{"low threshold and high mindoo", 0.1, 0.7, 1, 1},
		{"high threshold and low mindoo", 0.71, 0, 1, 0},
		{"high threshold and high mindoo", 0.71, 0.7, 0, 0},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, _, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.CaptchaResults = GenerateCaptchaResults(true, true, true)
			results.UseWeedCategoriesForWeedLabels = true
			outMap, _, _ := splitCaptchasCategorySize(results)
			smallCrops, _ := outMap[weed_tracking.PlantCaptchaUserPrediction_CROP]["CROP"]["small"]
			mediumCrops, _ := outMap[weed_tracking.PlantCaptchaUserPrediction_CROP]["CROP"]["medium"]

			detectedSmall := calculateDetectedWithMindoo(test.threshold, test.mindoo, smallCrops, weed_tracking.PlantCaptchaUserPrediction_CROP)
			detectedMedium := calculateDetectedWithMindoo(test.threshold, test.mindoo, mediumCrops, weed_tracking.PlantCaptchaUserPrediction_CROP)

			assert.True(t, approximatelyEqual(test.expectedSmallCropsDetected, detectedSmall))
			assert.True(t, approximatelyEqual(test.expectedMediumCropsDetected, detectedMedium))
		})
	}
}

func TestGetMinThreshold(t *testing.T) {
	tests := []struct {
		name                 string
		sizeCategory         string
		expectedMinThreshold float32
	}{
		{"medium", "medium", 0.6},
		{"small", "small", 0.3},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, _, results := GenerateAlgorithmInputs("weeding", 0.01, 0.0, 0.1)
			results.CaptchaResults = GenerateCaptchaResults(true, true, true)
			results.UseWeedCategoriesForWeedLabels = true
			outMap, _, _ := splitCaptchasCategorySize(results)
			weeds, _ := outMap[weed_tracking.PlantCaptchaUserPrediction_WEED]["GRASS"][test.sizeCategory]

			minThreshold := getMinThreshold(weeds)

			assert.True(t, approximatelyEqual(test.expectedMinThreshold, minThreshold))
		})
	}
}

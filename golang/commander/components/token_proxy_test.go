package components

import (
	"bytes"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/components/mocks"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"golang.org/x/oauth2/clientcredentials"
)

func TestNewTokenProxy(t *testing.T) {
	testPort := 1000
	testTokenFile := "foo/bar.json"
	tests := []struct {
		name                string
		robot               environment.Robot
		expectedTokenURL    string
		expectedOauthConfig *clientcredentials.Config
	}{
		{
			"user/pass",
			environment.Robot{
				MakaAuthScopes:      []string{"scope1"},
				CarbonIDMHost:       "idm-host",
				CarbonRobotUsername: "test_user",
				CarbonRobotPassword: "test_pass",
			},
			"https://idm-host/oauth/token",
			&clientcredentials.Config{
				TokenURL: "https://idm-host/oauth/token",
				Scopes:   []string{"scope1"},
			},
		},
		{
			"old style, still suported",
			environment.Robot{
				MakaAuthDomain:       "auth-domain",
				MakaAuthClientID:     "client-id",
				Ma<PERSON>AuthClientSecret: "secret",
				MakaAuthScopes:       []string{"scope1"},
			},
			"https://auth-domain/oauth/token",
			&clientcredentials.Config{
				ClientID:     "client-id",
				ClientSecret: "secret",
				TokenURL:     "https://auth-domain/oauth/token",
				Scopes:       []string{"scope1"},
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			proxy := NewTokenProxy(test.robot, testPort, testTokenFile)
			assert.Equal(t, test.robot, proxy.Robot)
			assert.Equal(t, testPort, proxy.Port)
			assert.Equal(t, testTokenFile, proxy.TokenCacheFile)
			assert.Equal(t, 5*time.Minute, proxy.TokenRetryLimit)
			assert.NotNil(t, proxy.log)
			assert.EqualValues(t, test.expectedOauthConfig, proxy.oAuthConfig)
			assert.Equal(t, time.Minute, proxy.tokenSaveInterval)
		})
	}
}

func TestTokenProxy_Token(t *testing.T) {
	testPort := 1000
	testTokenFile := "foo/bar.json"

	testToken := []byte(`{"access_token": "foo", "token_type": "bar", "expiry": "2024-04-14T19:19:21.57293348Z"}`)
	tests := []struct {
		name        string
		robot       environment.Robot
		tokenReqErr error
		expectError bool
	}{
		{
			"happy path",
			environment.Robot{
				MakaRobotName:       "foobot",
				MakaGen:             "foogen",
				CarbonRobotUsername: "robot1",
				CarbonRobotPassword: "pass1",
			},
			nil,
			false,
		},
		{
			"failed auth request",
			environment.Robot{
				MakaRobotName:       "foobot",
				MakaGen:             "foogen",
				CarbonRobotUsername: "robot1",
				CarbonRobotPassword: "pass1",
			},
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockHttpClient := new(mocks.HttpClient)
			mockHttpClient.On("Do", mock.Anything).
				Run(func(args mock.Arguments) {
					req := args.Get(0).(*http.Request)
					assert.Equal(t, test.robot.MakaRobotName, req.Header.Get(environment.RobotHTTPHeader))
					assert.Equal(t, test.robot.MakaGen, req.Header.Get(environment.GenerationHTTPHeader))
					if user, pass, ok := req.BasicAuth(); ok {
						assert.Equal(t, test.robot.CarbonRobotUsername, user)
						assert.Equal(t, test.robot.CarbonRobotPassword, pass)
					}
				}).
				Return(&http.Response{Body: io.NopCloser(bytes.NewReader(testToken))}, test.tokenReqErr)

			proxy := NewTokenProxy(test.robot, testPort, testTokenFile)
			proxy.httpClient = mockHttpClient

			token, err := proxy.Token()
			if test.expectError {
				assert.Error(t, err)
				alarmCount := 0
				proxy.activeAlarms.Range(func(key, value any) bool {
					alarmCount++
					return true
				})
				assert.NotZero(t, alarmCount)
				assert.NotZero(t, proxy.nextTokenRequestTime.Load())
				// check alarm & next request time
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, token)
			}
		})
	}
}

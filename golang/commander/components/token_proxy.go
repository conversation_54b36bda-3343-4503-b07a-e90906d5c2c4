package components

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
	log "github.com/sirupsen/logrus"
	"github.com/spf13/afero"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/clientcredentials"
)

var (
	tokenProxyLogger = logrus.New()
	tokenProxyFs     = afero.NewOsFs()
	tokenExpiryGauge = promauto.NewGauge(
		prometheus.GaugeOpts{
			Subsystem: "token_proxy",
			Name:      "token_expiry",
		})
	tokenRequestDurationHistogram = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Subsystem: "token_proxy",
			Name:      "token_request_duration_seconds",
			Help:      "token request duration histogram.",
			Buckets:   []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
		})
	tokenRequestErrorCount = promauto.NewCounter(
		prometheus.CounterOpts{
			Subsystem: "token_proxy",
			Name:      "token_request_error_count",
			Help:      "token request error total",
		})

	// error definitions
	ErrInvalidToken = fmt.Errorf("invalid token")
	ErrToken        = fmt.Errorf("failed to get token")
	ErrTokenLimit   = fmt.Errorf("token request rate limited")

	// alarm definitions
	authFailureAlarmCode = alarms.AlarmCode{Subsystem: alarms.Authentication, Number: 1}
	authFailureAlarm     = alarms.NewAlarm(
		"robot_authentication_failure",
		authFailureAlarmCode,
		"Robot connection failed due to authentication",
		alarms.AlarmLevelHigh,
		alarms.AlarmImpactOffline,
		fmt.Sprintf("AlarmCode_%s", authFailureAlarmCode.String()),
		[]*frontend.TranslationParameter{},
		nil)

	// test point
	timeNow = time.Now
)

type HttpClient interface {
	Do(req *http.Request) (*http.Response, error)
}

type tokenWrapper struct {
	TokenURL string        `json:"token_url,omitempty"`
	ClientID string        `json:"client_id,omitempty"`
	Username string        `json:"username,omitempty"`
	Token    *oauth2.Token `json:"token"`
}

type TokenProxy struct {
	Robot           environment.Robot
	Port            int
	TokenCacheFile  string
	TokenRetryLimit time.Duration

	log                  *logrus.Entry
	tokenURL             string
	oAuthConfig          *clientcredentials.Config
	tokenSource          oauth2.TokenSource
	tokenSaveInterval    time.Duration
	nextTokenRequestTime atomic.Int64
	activeAlarms         sync.Map
	httpClient           HttpClient
}

func NewTokenProxy(robot environment.Robot, port int, tokenFile string) *TokenProxy {
	tokenURL := ""
	if robot.CarbonRobotUsername != "" {
		tokenURL = fmt.Sprintf("https://%s/oauth/token", robot.CarbonIDMHost)
	} else {
		tokenURL = fmt.Sprintf("https://%s/oauth/token", robot.MakaAuthDomain)
	}

	tokenProxyLogger.SetFormatter(logging.DefaultLogFormat)
	return &TokenProxy{
		Robot:           robot,
		Port:            port,
		TokenCacheFile:  tokenFile,
		TokenRetryLimit: 5 * time.Minute, // if token request fails, wait this amount of time (likely will need cred fix)
		log:             tokenProxyLogger.WithFields(logrus.Fields{"module": "TokenProxy"}),
		tokenURL:        tokenURL,
		oAuthConfig: &clientcredentials.Config{
			ClientID:     robot.MakaAuthClientID,
			ClientSecret: robot.MakaAuthClientSecret,
			TokenURL:     tokenURL,
			Scopes:       robot.MakaAuthScopes,
		},
		tokenSaveInterval: time.Minute,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

func (proxy *TokenProxy) SetLogLevel(level logrus.Level) {
	tokenProxyLogger.SetLevel(level)
}

func (proxy *TokenProxy) GetActiveAlarms() []*alarms.Alarm {
	activeAlarms := make([]*alarms.Alarm, 0)
	proxy.activeAlarms.Range(func(key, value any) bool {
		if alarm, ok := value.(*alarms.Alarm); ok {
			activeAlarms = append(activeAlarms, alarm)
		}
		return true
	})
	return activeAlarms
}

func (proxy *TokenProxy) activateAlarm(alarm *alarms.Alarm) {
	proxy.activeAlarms.Store(alarm.Identifier, alarm)
}

func (proxy *TokenProxy) deactivateAlarm(identifier string) {
	proxy.activeAlarms.Delete(identifier)
}

func (proxy *TokenProxy) Token() (_ *oauth2.Token, err error) {
	defer func() {
		if err != nil {
			tokenRequestErrorCount.Inc()
			proxy.activateAlarm(authFailureAlarm)
			if errors.Is(err, ErrToken) || errors.Is(err, ErrInvalidToken) {
				proxy.nextTokenRequestTime.Store(timeNow().Add(proxy.TokenRetryLimit).Unix())
			}
		} else {
			proxy.deactivateAlarm(authFailureAlarm.Identifier)
		}
	}()

	nextTokenRequestTime := time.Unix(proxy.nextTokenRequestTime.Load(), 0)
	if time.Until(nextTokenRequestTime) > 0 {
		proxy.log.Debugln("token proxy request limiter enabled, next allowed token request:", nextTokenRequestTime.Format(time.RFC3339))
		return nil, ErrTokenLimit
	}

	req, err := http.NewRequest(http.MethodPost, proxy.tokenURL, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set(environment.RobotHTTPHeader, proxy.Robot.MakaRobotName)
	req.Header.Set(environment.GenerationHTTPHeader, proxy.Robot.MakaGen)
	req.SetBasicAuth(proxy.Robot.CarbonRobotUsername, proxy.Robot.CarbonRobotPassword)

	resp, err := proxy.httpClient.Do(req)
	if err != nil {
		proxy.log.WithError(err).Error(ErrToken)
		return nil, ErrToken
	}
	defer resp.Body.Close()
	token := new(TokenResponse)
	if err := json.NewDecoder(resp.Body).Decode(&token); err != nil {
		proxy.log.WithError(err).Error(ErrInvalidToken)
		return nil, ErrInvalidToken
	}

	return &oauth2.Token{
		AccessToken:  token.AccessToken,
		TokenType:    token.TokenType,
		RefreshToken: token.RefreshToken,
		Expiry:       expiresInToExpiry(token.ExpiresIn),
	}, nil
}

func (proxy *TokenProxy) TokenSource() oauth2.TokenSource {
	return proxy.tokenSource
}

func (proxy *TokenProxy) loadToken() (*oauth2.Token, error) {
	f, err := tokenProxyFs.Open(proxy.TokenCacheFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	wrap := new(tokenWrapper)
	if err := json.NewDecoder(f).Decode(wrap); err != nil {
		return nil, err
	}
	if wrap.TokenURL != proxy.tokenURL ||
		wrap.ClientID != proxy.oAuthConfig.ClientID ||
		wrap.Username != proxy.Robot.CarbonRobotUsername {
		return nil, fmt.Errorf("stored token does not match oauth configuration")
	}
	return wrap.Token, nil
}

func (proxy *TokenProxy) storeToken() {
	token, err := proxy.tokenSource.Token()
	if err != nil {
		proxy.log.WithError(err).Error("failed to store token")
		return
	}
	wrap := &tokenWrapper{
		TokenURL: proxy.tokenURL,
		ClientID: proxy.oAuthConfig.ClientID,
		Username: proxy.Robot.CarbonRobotUsername,
		Token:    token,
	}
	tokenBytes, err := json.Marshal(wrap)
	if err != nil {
		proxy.log.WithError(err).Error("failed to marshal token")
		return
	}
	if err = afero.WriteFile(tokenProxyFs, proxy.TokenCacheFile, tokenBytes, 0666); err != nil {
		proxy.log.WithError(err).Error("failed to store token")
	}
}

func (proxy *TokenProxy) Start(ctx context.Context) {
	tk, err := proxy.loadToken()
	if err != nil {
		proxy.log.WithError(err).Error("failed to load token")
	} else {
		tokenExpiryGauge.Set(float64(tk.Expiry.Unix()))
		proxy.log.Infoln("token loaded! type:", tk.Type(), "expires:", tk.Expiry)
	}
	if proxy.Robot.CarbonRobotUsername != "" && proxy.Robot.CarbonRobotPassword != "" {
		proxy.tokenSource = oauth2.ReuseTokenSource(tk, proxy)
	} else {
		proxy.tokenSource = oauth2.ReuseTokenSource(tk, proxy.oAuthConfig.TokenSource(context.Background()))
	}

	log.Println("starting token proxy")
	srv := &http.Server{
		Addr: fmt.Sprintf(":%d", proxy.Port),
	}
	http.HandleFunc("/oauth/token", proxy.oauthTokenHandler)
	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			proxy.log.WithError(err).Fatalln("http server error")
		}
	}()
	go proxy.incrementalStoreToken(ctx)

	<-ctx.Done()
	proxy.storeToken()
	log.Println("stopping token proxy")

	stopCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(stopCtx); err != nil {
		proxy.log.WithError(err).Error("server forced to shutdown")
	}
}

func (proxy *TokenProxy) incrementalStoreToken(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-time.After(proxy.tokenSaveInterval):
			proxy.storeToken()
		}
	}
}

func expiryToExpiresIn(ex time.Time) int64 {
	return int64(time.Until(ex).Seconds())
}

func expiresInToExpiry(seconds int64) time.Time {
	return timeNow().Add(time.Duration(seconds) * time.Second)
}

type TokenResponse struct {
	AccessToken  string    `json:"access_token"`
	TokenType    string    `json:"token_type,omitempty"`
	RefreshToken string    `json:"refresh_token,omitempty"`
	Expiry       time.Time `json:"expiry,omitempty"`
	ExpiresIn    int64     `json:"expires_in,omitempty"`
}

func (proxy *TokenProxy) oauthTokenHandler(w http.ResponseWriter, r *http.Request) {
	start := timeNow()
	defer func() {
		rqTime := time.Since(start)
		tokenRequestDurationHistogram.Observe(rqTime.Seconds())
	}()

	tk, err := proxy.tokenSource.Token()
	if err != nil {
		proxy.log.WithError(err).Error("failed to retrieve token")
		code := http.StatusUnauthorized
		switch {
		case errors.Is(err, ErrInvalidToken):
		case errors.Is(err, ErrToken):
			code = http.StatusInternalServerError
		case errors.Is(err, ErrTokenLimit):
			code = http.StatusTooManyRequests
		}
		http.Error(w, "failed to retrieve token", code)
		return
	}

	w.Header().Set("Cache-Control", "no-store")
	w.Header().Set("Content-Type", "application/json")
	tokenResp := TokenResponse{
		AccessToken:  tk.AccessToken,
		TokenType:    tk.TokenType,
		RefreshToken: tk.RefreshToken,
		Expiry:       tk.Expiry,
		ExpiresIn:    expiryToExpiresIn(tk.Expiry),
	}
	if err := json.NewEncoder(w).Encode(tokenResp); err != nil {
		proxy.log.WithError(err).Error("failed to encode token")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}

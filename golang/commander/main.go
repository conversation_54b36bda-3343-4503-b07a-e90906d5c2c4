package main

import (
	"context"
	"flag"
	"fmt"
	"math/rand"
	"net"
	"net/http"
	"os/signal"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/components"

	"github.com/carbonrobotics/robot/golang/commander/services"
	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/client_owner"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	crgrpc "github.com/carbonrobotics/robot/golang/lib/grpc"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/metrics_aggregator"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_definition"
	"github.com/carbonrobotics/robot/golang/lib/robot_syncer_client"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type CommanderState struct {
	OperationsState           *state.OperationsState
	ScannerState              *state.OverallScannerState
	CameraState               *state.OverallCameraState
	ChipManagerState          *state.ChipManagerState
	ImplementState            *state.ImplementState
	DataCaptureState          *state.DataCaptureState
	AlarmState                *state.AlarmState
	HostState                 *state.HostState
	ModelManagerState         *state.ModelManagerState
	SoftwareState             *state.OverallSoftwareState
	ServiceStatusState        *state.ServiceStatusState
	BoardState                *state.BoardState
	DistanceState             *state.DistanceState
	WeedingState              *state.WeedingState
	DataStorageState          *state.DataStorageState
	TrackingState             *state.TrackingState
	BandingState              *state.BandingState
	BandingVisualizationState *state.BandingVisualizationState
	WeedingDiagnosticsState   *state.WeedingDiagnosticsState
	CVRuntimeState            *state.CVRuntimeState
	WDUploadState             *state.WeedingDiagnosticsUploadState
	FeatureState              *state.FeatureState
	StartupTaskState          *state.OverallStartupTaskState
	ReportingState            *state.ReportingState
	ThinningState             *state.ThinningConfState
	JobsState                 *state.JobsState
	AlmanacState              *state.AlmanacCfgState
	DiscriminatorState        *state.DiscriminatorCfgState
	ModelinatorState          *state.ModelinatorCfgState
	PlantCaptchaState         *state.PlantCaptchaState
	VelocityV2State           *state.VelocityV2State
	TVEProfileState           *state.TargetVelocityProfileState
	CategoryCollectionState   *state.CategoryCollectionCfgState
	CategoryState             *state.CategoryCfgState
	ModuleOrchestratorState   *state.ModuleOrchestratorState
	RobotDefinitionState      *state.RobotDefinitionState
	TractorIFState            *state.TractorIFState
	TractorSafetyState        *state.TractorSafetyState
	CruiseControlState        *state.CruiseControlState
	PingCheckState            *state.PingCheckState
}

type CommanderActions struct {
	WeedingEnforcer                   *state.WeedingEnforcer
	ExtraStatusManager                *state.ExtraStatusManager
	SelectedModelWatcher              *state.SelectedModelWatcher
	AlarmManager                      *state.AlarmManager
	ScannerWatcher                    *state.ScannerWatcher
	CameraWatcher                     *state.CameraWatcher
	ChipManagerWatcher                *state.ChipManagerWatcher
	DataCaptureWatcher                *state.DataCaptureWatcher
	HostWatcher                       *state.HostWatcher
	ModelManagerWatcher               *state.ModelManagerWatcher
	SoftwareWatcher                   *state.SoftwareWatcher
	ImplementSupervisoryWatcher       *state.ImplementSupervisoryWatcher
	ImplementSafetyWatcher            *state.ImplementSafetyWatcher
	ImplementRotaryWatcher            *state.ImplementRotaryWatcher
	ImplementStrobeWatcher            *state.ImplementStrobeWatcher
	ImplementStatusWatcher            *state.ImplementStatusWatcher
	ServiceStatusWatcher              *state.ServiceStatusWatcher
	StatusLevelWatcher                *state.StatusLevelWatcher
	HealthWatcher                     *state.HealthWatcher
	BoardWatcher                      *state.BoardWatcher
	DistanceWatcher                   *state.DistanceWatcher
	WeedingStatusWatcher              *state.WeedingStatusWatcher
	DataStorageWatcher                *state.DataStorageWatcher
	TrackingWatcher                   *state.TrackingWatcher
	PowerReporter                     *state.ImplementPowerReporter
	AlarmWatcher                      *state.AlarmWatcher
	BandingVisualizationStateWatcher  *state.BandingVisualizationStateWatcher
	OperationsMetricWatcher           *state.OperationsMetricWatcher
	CVRuntimeWatcher                  *state.CVRuntimeWatcher
	WDUploadStateWatcher              *state.WeedingDiagnosticsUploadStateWatcher
	StartupTaskWatcher                *state.StartupTaskWatcher
	ReportingWatcher                  *state.ReportingWatcher
	JobsWatcher                       *state.JobsWatcher
	AlarmSyncWatcher                  *state.AlarmSyncWatcher
	ProfileSyncManager                *state.ProfileSyncManager
	PlantCaptchaUploadsWatcher        *state.PlantCaptchaUploadsWatcher
	ModelInfoSyncManager              *state.ModelInfoSyncManager
	SpatialMetricsSyncManager         *state.SpatialMetricsSyncManager
	ModelHistorySyncManager           *state.ModelHistorySyncManager
	ActualVelocityWatcher             *state.ActualVelocityWatcher
	TargetVelocityWatcher             *state.TargetVelocityWatcher
	PlantCaptchaWatcher               *state.PlantCaptchaWatcher
	RobotDefinitionEnforcer           *state.RobotDefinitionEnforcer
	ImplementReaperModuleWatcher      *state.ImplementReaperModuleWatcher
	ImplementCameraTemperatureWatcher *state.ImplementCameraTemperatureWatcher
	TractorIFStateWatcher             *state.TractorIFStateWatcher
}

type CommanderServices struct {
	DebugService              *services.DebugService
	StatusBarService          *services.StatusBarService
	DashboardService          *services.DashboardService
	FocusService              *services.FocusService
	LaserService              *services.LaserService
	CameraService             *services.CameraService
	CategoryCollectionService *services.CategoryCollectionService
	CategoryService           *services.CategoryService
	ChipService               *services.ChipService
	CrosshairService          *services.CrosshairService
	DataCaptureService        *services.DataCaptureService
	AlarmService              *services.AlarmService
	ModelService              *services.ModelService
	SoftwareService           *services.SoftwareService
	PowerService              *services.PowerService
	BandingService            *services.BandingService
	LoggingService            *logging.LoggingService
	WeedingDiagnosticsService *services.WeedingDiagnosticsService
	CalibrationService        *services.CalibrationService
	FeatureService            *services.FeatureService
	StartupTaskService        *services.StartupTaskService
	ReportingService          *services.ReportingService
	ThinningService           *services.ThinningService
	JobsService               *services.JobsService
	AlmanacService            *services.AlmanacConfigService
	PlantCaptchaService       *services.PlantCaptchaService
	TVEProfileService         *services.TargetVelocityEstimatorService
	ModuleOrchestratorService *services.ModuleOrchestratorService
	ModuleAssignmentService   *services.ModuleAssignmentService
	ActuactionTaskService     *services.ActuationTasksService
	TractorService            *services.TractorService
	CruiseControlService      *services.CruiseControlService
}
type CommanderClients struct {
	Rows              map[int]*rows.RowClients
	Hosts             []*hosts.HostClients
	DataUploadManager *data_upload_manager.EmergencyClient
	ConfigClient      *config.ConfigClient
	PortalClient      *portal_clients.Client
	RoSyClient        *robot_syncer_client.Client
}

func main() {
	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)

	rand.Seed(time.Now().UnixNano())

	logrus.Info("Starting Commander...")
	metrics.Serve("", 62002)

	robot, err := environment.GetRobot()
	if err != nil {
		logrus.Fatalf("Failed to parse environment: %v", err)
	}

	tokenFileName := filepath.Join(robot.MakaDataDir, ".auth_token.json")
	tokenProxy := components.NewTokenProxy(robot, 61900, tokenFileName)
	go tokenProxy.Start(stopCtx)

	configSubscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	configSubscriber.AddConfigTree("commander", fmt.Sprintf("%s/commander", config.GetComputerConfigPrefix()), "services/commander.yaml")
	configSubscriber.AddConfigTree("data_upload_manager", fmt.Sprintf("%s/data_upload_manager", config.GetComputerConfigPrefix()), "services/data_upload_manager.yaml")
	configSubscriber.AddConfigTree("common", "common", "services/common.yaml")
	if environment.IsReaper() {
		configSubscriber.AddConfigTree("hw_status_range", "hw_status_range", "components/reaper_hw_status_ranges.yaml")
	}
	configSubscriber.Start()
	configSubscriber.WaitUntilReady()
	configClient := config.NewConfigClient(config.MakeRobotLocalAddr(61001))

	hardwareManagerClient := hardware_manager.NewHardwareManagerClient(config.MakeRobotLocalAddr(61006))
	metricsAggregatorClient := metrics_aggregator.NewMetricsAggregatorClient(config.MakeRobotLocalAddr(61010))

	if configSubscriber.GetConfigNode("common", "environment").GetStringValue() == "production" {
		sentry_reporter.InitializeSentry()
		defer sentry_reporter.HandlePanic()
	}

	var nodeHwStatusRange *config.ConfigTree
	supportPhone := configSubscriber.GetConfigNode("commander", "support_phone")
	nodeCommander := configSubscriber.GetConfigNode("commander", "")
	nodeLogLevel := configSubscriber.GetConfigNode("commander", "log_level")
	nodeCommon := configSubscriber.GetConfigNode("common", "")
	nodeDataUploadManager := configSubscriber.GetConfigNode("data_upload_manager", "")
	velocityCfg := nodeCommon.GetNode("global_scheduler/velocity_smoothing")
	cruiseFeatureFlag := nodeCommon.GetNode("feature_flags/cruise_control_feature")
	nodeLogLevel.RegisterCallback(func() {
		level := logging.StringToLogLevel(nodeLogLevel.GetStringValue())
		tokenProxy.SetLogLevel(level)
	})
	if environment.IsReaper() {
		nodeHwStatusRange = configSubscriber.GetConfigNode("hw_status_range", "")
	}
	flag.Parse() // No Flags For Now

	logrus.Infof("Before Redis Client")
	redisClient := redis.New(robot)
	logrus.Infof("After Redis Client")

	clientOwner := client_owner.NewClientOwner(redisClient, nodeCommon, &robot)

	var robotDefinition *robot_definition.RobotDefinition
	var moduleOrchestratorState *state.ModuleOrchestratorState
	var robotDefinitionState *state.RobotDefinitionState
	var robotDefinitionEnforcer *state.RobotDefinitionEnforcer

	var uploadTriggerConnector *state.UploadTriggerConnector
	if environment.IsReaper() {
		exists, err := robot_definition.CurrentDefExistsInRedis(redisClient)
		if err != nil {
			logrus.Fatalf("Failed to check if current robot definition exists in redis: %v", err)
		} else if exists {
			robotDefinition, err = robot_definition.ReadCurrentDefFromRedis(redisClient)
			if err != nil {
				logrus.Fatalf("Failed to read current robot definition from redis: %v", err)
			}
		} else {
			robotDefinition = &robot_definition.RobotDefinition{}
		}
		uploadTriggerConnector = &state.UploadTriggerConnector{}
		// robot definition does not trigger an upload since it causes a reboot on change
		robotDefinitionState = state.NewRobotDefinitionState(redisClient, clientOwner)
		robotDefinitionEnforcer = state.NewRobotDefinitionEnforcer(robotDefinitionState)
		moduleOrchestratorState = state.NewModuleOrchestratorState(stopCtx, redisClient, robotDefinitionState, uploadTriggerConnector)
	}

	jobsState := state.NewJobsState(redisClient, robot.MakaRobotName)
	modelManagerState := state.NewModelManagerState()
	logrus.Infof("After Model Manager State")
	almanacState, discriminatorState, modelinatorState := state.NewAlmanacCfgStates(stopCtx, redisClient, modelManagerState, jobsState, clientOwner.GetRowClients(), robot.MakaRobotName)
	logrus.Infof("After Almanac Cfg State")
	vel2State, tveState := state.NewVelocityStates(redisClient, clientOwner.GetRowClients(), jobsState)
	categoryCollectionState := state.NewCategoryCollectionCfgState(redisClient, clientOwner.GetRowClients(), nodeCommander, configClient)
	categoryState := state.NewCategoryCfgState(redisClient, clientOwner.GetRowClients())
	logrus.Infof("Before Creating State")

	commanderState := &CommanderState{
		OperationsState:           state.NewOperationsState(clientOwner.GetRowClients()),
		ScannerState:              state.NewOverallScannerState(robot),
		CameraState:               state.NewOverallCameraState(),
		ChipManagerState:          state.NewChipManagerState(),
		DataCaptureState:          state.NewDataCaptureState(configSubscriber),
		AlarmState:                state.NewAlarmState(redisClient, nodeCommander),
		HostState:                 state.NewHostState(len(clientOwner.GetHostClients())),
		ModelManagerState:         modelManagerState,
		SoftwareState:             state.NewOverallSoftwareState(),
		ImplementState:            state.NewImplementState(robot, robotDefinition),
		ServiceStatusState:        state.NewServiceStatusState(),
		BoardState:                state.NewBoardState(),
		DistanceState:             state.NewDistanceState(),
		WeedingState:              state.NewWeedingState(),
		DataStorageState:          state.NewDataStorageState(),
		TrackingState:             state.NewTrackingState(),
		BandingState:              state.NewBandingState(redisClient, jobsState, clientOwner.GetRowClients()),
		BandingVisualizationState: state.NewBandingVisualizationState(),
		WeedingDiagnosticsState:   state.NewWeedingDiagnosticsState(),
		CVRuntimeState:            state.NewCVRuntimeState(),
		WDUploadState:             state.NewWeedingDiagnosticsUploadState(),
		FeatureState:              state.NewFeatureState(nodeCommon),
		StartupTaskState:          state.NewOverallStartupTaskState(),
		ReportingState:            state.NewReportingState(redisClient),
		ThinningState:             state.NewThinningConfState(redisClient, jobsState, clientOwner.GetRowClients()),
		JobsState:                 jobsState,
		AlmanacState:              almanacState,
		DiscriminatorState:        discriminatorState,
		ModelinatorState:          modelinatorState,
		PlantCaptchaState:         state.NewPlantCaptchaState(redisClient),
		VelocityV2State:           vel2State,
		TVEProfileState:           tveState,
		CategoryCollectionState:   categoryCollectionState,
		CategoryState:             categoryState,
		ModuleOrchestratorState:   moduleOrchestratorState,
		RobotDefinitionState:      robotDefinitionState,
		TractorIFState:            state.NewTractorIFState(),
		TractorSafetyState:        state.NewTractorSafetyState(nodeCommon.GetNode("feature_flags/tractor_enforced_safety_feature")),
		CruiseControlState:        state.NewCruiseControlState(),
		PingCheckState:            state.NewPingCheckState(clientOwner, moduleOrchestratorState, robot),
	}

	logrus.Infof("Created State")

	a0cfg := auth.NewRobotAuth0Config(robot, robot.MakaAuthScopes, "http://127.0.0.1:61900/oauth/token")

	robotMetadataInjector := crgrpc.RobotMetadataInjector(func() map[string]string {
		return state.RobotMetaRetriever(robot, commanderState.SoftwareState)
	})
	commanderClients := &CommanderClients{
		Rows:              clientOwner.GetRowClients(),
		Hosts:             clientOwner.GetHostClients(),
		DataUploadManager: data_upload_manager.NewEmergencyServiceClient(config.MakeRobotLocalAddr(61003), robotMetadataInjector),
		ConfigClient:      config.NewConfigClient(config.MakeRobotLocalAddr(61001)),
		PortalClient:      portal_clients.NewPortalClient(robot.CarbonPortalHost, tokenProxy.TokenSource(), robotMetadataInjector),
		RoSyClient:        robot_syncer_client.NewRoSyClient(robot.CarbonConfigCloudHost, robot.CarbonConfigCloudPort, tokenProxy.TokenSource(), robotMetadataInjector),
	}

	httpClient := a0cfg.AuthenticatedHttpClient(context.Background())
	httpClient.CheckRedirect = func(req *http.Request, via []*http.Request) error { return http.ErrUseLastResponse }

	veselkaClient := veselka.New(robot, nodeCommon.GetNode("environment"), nodeCommon.GetNode("experimental_flag_list"), httpClient)
	veselkaClient.SetLogFormatter(logging.DefaultLogFormat)

	prometheusGenerator := services.NewPrometheusGenerator(configSubscriber, robot, clientOwner)
	prometheusGenerator.GeneratePrometheusConfig()

	extraStatusManager := state.NewExtraStatusManager(commanderState.OperationsState, commanderState.ImplementState, commanderState.WeedingState, nodeCommander)
	modelManagerParams := state.ModelManagerWatcherParams{
		CommanderNode:           nodeCommander,
		CommonNode:              nodeCommon,
		Env:                     robot,
		ConfigClient:            configClient,
		Rows:                    commanderClients.Rows,
		HttpClient:              httpClient,
		ModelState:              commanderState.ModelManagerState,
		OperationsState:         commanderState.OperationsState,
		ImplementState:          commanderState.ImplementState,
		StopCtx:                 stopCtx,
		VeselkaClient:           veselkaClient,
		HardwareManagerClient:   hardwareManagerClient,
		Redis:                   redisClient,
		ModelinatorConfigClient: modelinatorState,
	}
	modelManager := state.NewModelManagerWatcher(modelManagerParams)

	syncProfileMap := map[frontend.ProfileType]state.Profile{
		frontend.ProfileType_ALMANAC:                   commanderState.AlmanacState,
		frontend.ProfileType_DISCRIMINATOR:             commanderState.DiscriminatorState,
		frontend.ProfileType_MODELINATOR:               commanderState.ModelinatorState,
		frontend.ProfileType_BANDING:                   commanderState.BandingState,
		frontend.ProfileType_THINNING:                  commanderState.ThinningState,
		frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR: commanderState.TVEProfileState,
		frontend.ProfileType_CATEGORY_COLLECTION:       commanderState.CategoryCollectionState,
		frontend.ProfileType_CATEGORY:                  commanderState.CategoryState,
	}
	profileSync := state.NewProfileSyncManager(redisClient, commanderClients.PortalClient, commanderClients.RoSyClient, robot.MakaRobotName, syncProfileMap)

	chipManagerParams := state.ChipManagerWatcherParams{
		ChipManagerState: commanderState.ChipManagerState,
		CommanderNode:    nodeCommander,
		CommonNode:       nodeCommon,
		Env:              robot,
		RowClients:       commanderClients.Rows,
		StopCtx:          stopCtx,
		VeselkaClient:    veselkaClient,
		RedisClient:      redisClient,

		CategoryCollectionState: commanderState.CategoryCollectionState,
		CategoryState:           commanderState.CategoryState,
	}

	commanderActions := &CommanderActions{
		WeedingEnforcer:             state.NewWeedingEnforcer(commanderState.OperationsState, commanderState.ImplementState, commanderClients.Rows),
		ExtraStatusManager:          extraStatusManager,
		SelectedModelWatcher:        state.NewSelectedModelWatcher(commanderState.OperationsState, commanderState.JobsState, nodeCommander, nodeCommon),
		AlarmManager:                state.NewAlarmManager(commanderState.AlarmState, stopCtx, commanderState.OperationsState, commanderState.ScannerState, commanderState.CameraState, commanderState.VelocityV2State, commanderState.DataCaptureState, commanderState.HostState, commanderState.SoftwareState, commanderState.BoardState, commanderState.ImplementState, commanderState.ModelManagerState, commanderState.DataStorageState, nodeCommander, nodeCommon, commanderState.TrackingState, commanderState.CVRuntimeState, commanderState.TractorIFState, hardwareManagerClient, clientOwner.GetRowClients(), robotDefinition),
		ScannerWatcher:              state.NewScannerWatcher(commanderState.ScannerState, commanderState.ImplementState, commanderClients.Rows, metricsAggregatorClient),
		CameraWatcher:               state.NewCameraWatcher(commanderState.CameraState, commanderClients.Rows, robot),
		ChipManagerWatcher:          state.NewChipManagerWatcher(chipManagerParams),
		DataCaptureWatcher:          state.NewDataCaptureWatcher(commanderState.DataCaptureState, commanderClients.DataUploadManager, configSubscriber, hardwareManagerClient),
		HostWatcher:                 state.NewHostWatcher(commanderState.HostState, commanderClients.Hosts, commanderState.ImplementState, commanderState.PingCheckState),
		ModelManagerWatcher:         modelManager,
		SoftwareWatcher:             state.NewSoftwareWatcher(commanderState.SoftwareState, commanderClients.Hosts),
		ImplementSupervisoryWatcher: state.NewImplementSupervisoryWatcher(commanderState.ImplementState, commanderState.OperationsState, hardwareManagerClient, nodeCommon, robot),
		ImplementSafetyWatcher:      state.NewImplementSafetyWatcher(commanderState.ImplementState, hardwareManagerClient, robot),
		ImplementRotaryWatcher:      state.NewImplementRotaryWatcher(commanderState.ImplementState, hardwareManagerClient),
		ImplementStrobeWatcher:      state.NewImplementStrobeWatcher(hardwareManagerClient, clientOwner.GetRowClients()),
		ImplementStatusWatcher:      state.NewImplementStatusWatcher(stopCtx, commanderState.OperationsState, commanderState.AlarmState, hardwareManagerClient, commanderState.TractorIFState, commanderState.TractorSafetyState),
		StatusLevelWatcher:          state.NewStatusLevelWatcher(commanderState.AlarmState, commanderState.ImplementState, commanderState.OperationsState, commanderState.ServiceStatusState, commanderState.ModelManagerState, extraStatusManager, commanderState.SoftwareState, nodeCommander, nodeCommon, stopCtx, robot, robotDefinitionState, moduleOrchestratorState, commanderState.TractorSafetyState),
		ServiceStatusWatcher:        state.NewServiceStatusWatcher(commanderState.ServiceStatusState, commanderClients.Rows, hardwareManagerClient),
		HealthWatcher: state.NewHealthWatcher(
			commanderClients.PortalClient,
			robot,
			commanderState.AlarmState,
			commanderState.OperationsState,
			commanderState.ModelManagerState,
			hardwareManagerClient,
			nodeCommander,
			nodeCommon,
			commanderState.SoftwareState,
			commanderState.WeedingState,
			redisClient,
			metricsAggregatorClient,
			commanderState.ScannerState,
			commanderState.ThinningState,
			commanderState.JobsState,
			commanderState.AlmanacState,
			commanderState.DiscriminatorState,
			commanderState.HostState,
			profileSync,
			commanderState.FeatureState,
			commanderState.ModelinatorState,
			commanderState.TVEProfileState,
			commanderState.CategoryCollectionState,
			commanderState.CategoryState,
		),
		BoardWatcher:                      state.NewBoardWatcher(commanderState.BoardState, hardwareManagerClient),
		DistanceWatcher:                   state.NewDistanceWatcher(commanderState.DistanceState, hardwareManagerClient),
		WeedingStatusWatcher:              state.NewWeedingStatusWatcher(configSubscriber, hardwareManagerClient, redisClient, commanderState.WeedingState, commanderState.OperationsState, commanderState.ImplementState, commanderState.VelocityV2State, stopCtx),
		DataStorageWatcher:                state.NewDataStorageWatcher(commanderState.DataStorageState, hardwareManagerClient),
		TrackingWatcher:                   state.NewTrackingWatcher(commanderState.TrackingState, commanderClients.Rows, robot),
		PowerReporter:                     state.NewImplementPowerReporter(commanderState.ImplementState, nodeCommon, redisClient, stopCtx),
		AlarmWatcher:                      state.NewAlarmWatcher(commanderState.AlarmState, nodeCommander),
		BandingVisualizationStateWatcher:  state.NewBandingVisualizationStateWatcher(commanderState.BandingVisualizationState, commanderClients.Rows),
		OperationsMetricWatcher:           state.NewOperationsMetricWatcher(commanderState.OperationsState, commanderClients.Rows, nodeCommon),
		CVRuntimeWatcher:                  state.NewCVRuntimeWatcher(commanderState.CVRuntimeState, clientOwner.GetRowClients()),
		WDUploadStateWatcher:              state.NewWeedingDiagnosticsUploadStateWatcher(commanderState.WDUploadState, redisClient),
		StartupTaskWatcher:                state.NewStatupTaskWatcher(commanderState.StartupTaskState, redisClient, commanderState.ServiceStatusState, commanderState.ScannerState, hardwareManagerClient),
		ReportingWatcher:                  state.NewReportingWatcher(nodeCommon, hardwareManagerClient, commanderState.ReportingState, commanderState.WeedingState),
		JobsWatcher:                       state.NewJobsWatcher(commanderClients.PortalClient, commanderState.JobsState),
		AlarmSyncWatcher:                  state.NewAlarmSyncWatcher(commanderState.AlarmState, commanderClients.PortalClient, robot.MakaRobotName),
		ProfileSyncManager:                profileSync,
		PlantCaptchaUploadsWatcher:        state.NewPlantCaptchaUploadsWatcher(commanderState.PlantCaptchaState, redisClient),
		SpatialMetricsSyncManager:         state.NewSpatialMetricsSyncManager(redisClient, commanderClients.PortalClient, robot.MakaRobotName),
		ModelInfoSyncManager:              state.NewModelInfoSyncManager(modelManager, commanderClients.PortalClient, robot.MakaRobotName),
		ModelHistorySyncManager:           state.NewModelHistorySyncManager(redisClient, commanderClients.PortalClient, robot.MakaRobotName),
		ActualVelocityWatcher:             state.NewActualVelocityWatcher(commanderState.VelocityV2State, hardwareManagerClient),
		TargetVelocityWatcher:             state.NewTargetVelocityWatcher(commanderState.VelocityV2State, commanderState.TVEProfileState, commanderState.OperationsState, velocityCfg),
		PlantCaptchaWatcher:               state.NewPlantCaptchaWatcher(commanderState.PlantCaptchaState),
		RobotDefinitionEnforcer:           robotDefinitionEnforcer,
		ImplementReaperModuleWatcher:      state.NewImplementReaperModuleWatcher(commanderState.ImplementState, hardwareManagerClient, moduleOrchestratorState),
		ImplementCameraTemperatureWatcher: state.NewImplementCameraTemperatureWatcher(commanderState.ImplementState, clientOwner),
		TractorIFStateWatcher:             state.NewTractorIFStateWatcher(commanderState.TractorIFState, hardwareManagerClient),
	}
	modelinatorState.SetDefaultFetcher(commanderActions.ModelManagerWatcher.GetDefaultModelParameters)
	modelinatorState.AddUpdateLocalToSyncCallback(func(s string, profileType frontend.ProfileType, protected bool) error {
		if profileType == frontend.ProfileType_MODELINATOR {
			parts := strings.Split(s, "/")
			if len(parts) < 2 {
				return fmt.Errorf("invalid model modelinator id: %s", s)
			}
			modelID := parts[0]
			cropID := parts[1]
			cfg, err := modelinatorState.FetchModelinatorConfig(modelID, cropID)
			if err != nil {
				return err
			}
			return commanderActions.ModelManagerWatcher.TrackModelinatorChange(modelID, cropID, cfg)
		}
		return nil
	})
	go func() {
		defer sentry_reporter.HandlePanic()
		commanderActions.AlarmManager.IntervalAlarmObserver(stopCtx, time.Second, state.AlarmTypeAuthentication, tokenProxy.GetActiveAlarms)
	}()
	logrus.Infof("Created Actions")

	runnables := []state.Runnable{ // TODO Figure out how to get more varied interval loops, some don't need to be polled that often
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.WeedingEnforcer),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ExtraStatusManager),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.SelectedModelWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ScannerWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.DataCaptureWatcher),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.CameraWatcher, 5*time.Second),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ChipManagerWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.HostWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ModelManagerWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.SoftwareWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ImplementSupervisoryWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ImplementSafetyWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.ImplementRotaryWatcher),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ImplementStrobeWatcher, 15*time.Minute),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ImplementStatusWatcher, 5*time.Second),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ServiceStatusWatcher, 1*time.Second),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.BoardWatcher),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.HealthWatcher, 1*time.Minute),
		commanderActions.AlarmManager,
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.DistanceWatcher),
		commanderActions.WeedingStatusWatcher,
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.DataStorageWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.TrackingWatcher),
		commanderActions.PowerReporter,
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.AlarmWatcher, 1*time.Second),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.BandingVisualizationStateWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.OperationsMetricWatcher),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.CVRuntimeWatcher),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.WDUploadStateWatcher, 1*time.Second),
		state.NewIntervalLoopExecutor(stopCtx, commanderActions.StartupTaskWatcher),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ReportingWatcher, 10*time.Second),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.JobsWatcher, 5500*time.Millisecond),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.AlarmSyncWatcher, 1*time.Minute),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ProfileSyncManager, 1*time.Minute),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.PlantCaptchaUploadsWatcher, 1*time.Second),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.SpatialMetricsSyncManager, 1*time.Minute),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ModelInfoSyncManager, 1*time.Minute),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ModelHistorySyncManager, 1*time.Minute),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ActualVelocityWatcher, 1*time.Second),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.TargetVelocityWatcher, 1*time.Second),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.PlantCaptchaWatcher, 5*time.Second),
		state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.TractorIFStateWatcher, 5*time.Second),
	}

	var reaperConfigurationUploader *state.ReaperConfigurationUploader
	if environment.IsReaper() {
		runnables = append(runnables,
			state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.RobotDefinitionEnforcer, 30*time.Second),
			state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ImplementReaperModuleWatcher, 5*time.Second),
			state.NewIntervalLoopExecutorWithInterval(stopCtx, commanderActions.ImplementCameraTemperatureWatcher, 10*time.Second),
		)
		reaperConfigurationUploader = state.NewReaperConfigurationUploader(stopCtx, commanderClients.PortalClient, moduleOrchestratorState, robotDefinitionState, uploadTriggerConnector)
	}

	logrus.Infof("Created Runnables")

	var wg sync.WaitGroup
	wg.Add(len(runnables)) // Actions
	wg.Add(1)              // GRPC server

	for _, runnable := range runnables {
		go func(r state.Runnable) {
			defer sentry_reporter.HandlePanic()
			defer wg.Done()
			r.Run()
		}(runnable)
	}
	go func() {
		defer sentry_reporter.HandlePanic()
		state.CruiseControlUpdater(stopCtx, cruiseFeatureFlag, configSubscriber.GetConfigNode("commander", "cruise_update_interval_msec"), hardwareManagerClient, commanderState.VelocityV2State, commanderState.AlarmState, commanderState.OperationsState, commanderState.TractorSafetyState, commanderState.CruiseControlState, &wg)
	}()
	go func() {
		defer sentry_reporter.HandlePanic()
		state.TractorSafetyStateWatcher(stopCtx, commanderState.TractorIFState, commanderState.TractorSafetyState, hardwareManagerClient, &wg)
	}()
	go func() {
		defer sentry_reporter.HandlePanic()
		state.EnforcementPolicyEnforcer(stopCtx, commanderState.TractorSafetyState, hardwareManagerClient, &wg)
	}()
	go func() {
		defer sentry_reporter.HandlePanic()
		state.TractorSafetyEnforcer(stopCtx, commanderState.TractorSafetyState, commanderState.OperationsState, commanderActions.WeedingEnforcer.EventTrigger, &wg)
	}()

	// Open TCP Port
	addr := fmt.Sprintf("0.0.0.0:%d", 61002)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.Fatalf("failed to listen: %v", err)
	}

	logrus.Infof("Opened TCP Port")

	robotMetadataServerInjectorInterceptor := crgrpc.RobotMetadataServerInjectorInterceptorFactory(func() map[string]string {
		return state.RobotMetaRetriever(robot, commanderState.SoftwareState)
	})
	// Create GRPC Server
	opts := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(
			sentry_reporter.PanicInterceptor,
			robotMetadataServerInjectorInterceptor,
		),
	}
	grpcServer := grpc.NewServer(opts...)

	// Create HTTP Server
	httpServeMux := http.NewServeMux()

	jobProfiles := []services.JobProfile{commanderState.BandingState, commanderState.ThinningState, commanderState.AlmanacState, commanderState.DiscriminatorState, commanderState.TVEProfileState}
	// GRPC Services
	bandingService := services.NewBandingService(grpcServer, redisClient, commanderClients.Rows, commanderState.BandingState, commanderState.BandingVisualizationState, commanderState.JobsState, &robot, configClient, commanderActions.ProfileSyncManager, commanderState.OperationsState)

	var moduleOrchestratorService *services.ModuleOrchestratorService
	var moduleAssignmentService *services.ModuleAssignmentService
	if environment.IsReaper() {
		moduleOrchestratorService = services.NewModuleOrchestratorService(grpcServer, commanderState.ModuleOrchestratorState)
		moduleAssignmentService = services.NewModuleAssignmentService(grpcServer, commanderState.ModuleOrchestratorState, commanderState.RobotDefinitionState, hardwareManagerClient)
	}

	commanderServices := &CommanderServices{
		DebugService:              services.NewDebugService(grpcServer, commanderState.AlarmState, &robot, commanderClients.Rows, commanderActions.SpatialMetricsSyncManager, commanderActions.ProfileSyncManager),
		StatusBarService:          services.NewStatusBarService(grpcServer, commanderState.OperationsState, supportPhone, commanderClients.PortalClient, robot.MakaRobotName, commanderState.SoftwareState),
		DashboardService:          services.NewDashboardService(grpcServer, commanderState.OperationsState, commanderState.VelocityV2State, commanderActions.WeedingEnforcer, commanderActions.SelectedModelWatcher, configClient, nodeCommander, nodeCommon, commanderState.ModelManagerState, commanderState.TractorSafetyState, hardwareManagerClient),
		FocusService:              services.NewFocusService(grpcServer, commanderState.CameraState, commanderActions.CameraWatcher, commanderClients.Rows),
		LaserService:              services.NewLaserService(grpcServer, stopCtx, commanderState.ScannerState, commanderActions.ScannerWatcher, commanderClients.Rows, redisClient, metricsAggregatorClient, configClient, robot),
		CameraService:             services.NewCameraService(robot, grpcServer, commanderState.CameraState, commanderActions.CameraWatcher, commanderClients.Rows),
		CategoryCollectionService: services.NewCategoryCollectionService(grpcServer, redisClient, commanderState.CategoryCollectionState),
		CategoryService:           services.NewCategoryService(grpcServer, redisClient, commanderState.CategoryState),
		ChipService:               services.NewChipService(grpcServer, commanderState.ChipManagerState),
		CrosshairService:          services.NewCrosshairService(grpcServer, commanderState.ScannerState, commanderActions.ScannerWatcher, commanderClients.Rows, commanderState.CameraState),
		DataCaptureService:        services.NewDataCaptureService(grpcServer, commanderState.DataCaptureState, commanderActions.DataCaptureWatcher, commanderClients.DataUploadManager, configClient, nodeCommander, nodeDataUploadManager, commanderState.CameraState),
		AlarmService:              services.NewAlarmService(grpcServer, commanderState.AlarmState, commanderActions.AlarmManager, hardwareManagerClient),
		ModelService:              services.NewModelService(grpcServer, commanderActions.ModelManagerWatcher, commanderState.ModelManagerState, configClient, nodeCommander, nodeCommon),
		SoftwareService:           services.NewSoftwareService(grpcServer, commanderState.SoftwareState, commanderClients.Hosts, nodeCommander, configClient),
		PowerService:              services.NewPowerService(grpcServer, hardwareManagerClient, commanderState.ImplementState, nodeHwStatusRange),
		BandingService:            bandingService,
		LoggingService:            logging.NewLoggingService(grpcServer, redisClient, "commander", &robot),
		WeedingDiagnosticsService: services.NewWeedingDiagnosticsService(grpcServer, httpServeMux, commanderState.WeedingDiagnosticsState, commanderClients.Rows, redisClient, commanderState.ScannerState, commanderClients.ConfigClient, hardwareManagerClient, bandingService, &robot, commanderClients.DataUploadManager, commanderState.WDUploadState, commanderState.ThinningState),
		CalibrationService:        services.NewCalibrationService(grpcServer, commanderState.CameraState, commanderClients.Rows, configClient),
		FeatureService:            services.NewFeatureService(grpcServer, commanderState.FeatureState, &robot, clientOwner.GetRowClients()),
		StartupTaskService:        services.NewStartupTaskService(grpcServer, commanderState.StartupTaskState, nodeCommon),
		ReportingService:          services.NewReportingService(grpcServer, commanderState.ReportingState),
		ThinningService:           services.NewThinningService(grpcServer, commanderState.ThinningState),
		JobsService:               services.NewJobsService(grpcServer, commanderState.JobsState, commanderState.OperationsState, redisClient, &robot, commanderClients.ConfigClient, metricsAggregatorClient, jobProfiles),
		AlmanacService:            services.NewAlmanacService(grpcServer, redisClient, nodeCommon.GetNode("almanac/almanac_size_category_count"), commanderState.AlmanacState, commanderState.DiscriminatorState, commanderState.ModelinatorState),
		PlantCaptchaService:       services.NewPlantCaptchaService(grpcServer, commanderState.PlantCaptchaState, commanderState.ModelinatorState, commanderState.AlmanacState, commanderClients.Rows, &robot, httpServeMux, commanderClients.DataUploadManager, veselkaClient, redisClient, configSubscriber),
		TVEProfileService:         services.NewTVEProfileService(grpcServer, commanderState.TVEProfileState),
		ModuleOrchestratorService: moduleOrchestratorService,
		ModuleAssignmentService:   moduleAssignmentService,
		ActuactionTaskService:     services.NewActuationTasksService(grpcServer, stopCtx, commanderState.OperationsState, commanderClients.Rows, configClient, robot),
		TractorService:            services.NewTractorService(grpcServer, commanderState.TractorIFState, commanderState.TractorSafetyState),
		CruiseControlService:      services.NewCruiseControlService(grpcServer, commanderState.CruiseControlState),
	}

	logrus.Infof("Created Services")

	bandingService.UpgradeBandingToUUIDs()

	// No Direct Use for services yet, but they are kept
	_ = commanderServices

	// Serve Requests Forever
	logrus.Infof("Started Commander Server at: %s", addr)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil {
			logrus.Fatalf("Failed to Serve GRPC Server: %v", err)
		}
	}()

	fmt.Println("Commander HTTP Server started at port 61011")

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err = http.ListenAndServe(":61011", httpServeMux)
		if err != nil {
			logrus.Fatalf("Failed to Serve HTTP Server: %v", err)
		}
	}()

	<-stopCtx.Done()

	grpcServer.Stop()
	commanderState.OperationsState.Terminate()
	commanderState.CameraState.Terminate()
	commanderState.ScannerState.Terminate()
	commanderState.DataCaptureState.Terminate()
	commanderState.AlarmState.Terminate()
	commanderState.HostState.Terminate()
	commanderState.ModelManagerState.Terminate()
	commanderState.SoftwareState.Terminate()
	commanderState.BoardState.Terminate()
	commanderState.DataStorageState.Terminate()
	commanderState.TrackingState.Terminate()
	commanderState.FeatureState.Terminate()
	commanderState.StartupTaskState.Terminate()
	if commanderState.ModuleOrchestratorState != nil {
		commanderState.ModuleOrchestratorState.Terminate()
	}
	if commanderState.RobotDefinitionState != nil {
		commanderState.RobotDefinitionState.Terminate()
	}
	if reaperConfigurationUploader != nil {
		reaperConfigurationUploader.Terminate()
	}

	wg.Wait()
}

package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type CategoryCfg interface {
	GetNextCategoryData(ctx context.Context, timestampMs int64) (bool, []*category.Category, int64)
}

type CategoryService struct {
	frontend.UnimplementedCategoryServiceServer
	redisClient *redis.Client
	categoryCfg CategoryCfg
}

func NewCategoryService(grpcServer *grpc.Server, redisClient *redis.Client, categoryCfg CategoryCfg) *CategoryService {
	service := &CategoryService{
		redisClient: redisClient,
		categoryCfg: categoryCfg,
	}
	frontend.RegisterCategoryServiceServer(grpcServer, service)
	return service
}

func (s *CategoryService) GetNextCategoryData(ctx context.Context, req *frontend.GetNextCategoryDataRequest) (*frontend.GetNextCategoryDataResponse, error) {
	success, categories, newTs := s.categoryCfg.GetNextCategoryData(ctx, req.GetTs().GetTimestampMs())
	if !success {
		return nil, status.Error(codes.Aborted, "context cancelled before Category  data was retrieved")
	}

	resp := &frontend.GetNextCategoryDataResponse{
		Ts:         &frontend.Timestamp{TimestampMs: newTs},
		Categories: categories,
	}

	return resp, nil
}

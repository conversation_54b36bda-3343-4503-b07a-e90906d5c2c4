package services

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	statemocks "github.com/carbonrobotics/robot/golang/commander/state/mocks"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

func TestNewDataCaptureService(t *testing.T) {
	saveFrontendRegisterDataCaptureServiceServer := frontendRegisterDataCaptureServiceServer
	defer func() { frontendRegisterDataCaptureServiceServer = saveFrontendRegisterDataCaptureServiceServer }()

	testGrpcServer := &grpc.Server{}
	testDataState := &state.DataCaptureState{}
	testDataWatcher := &state.DataCaptureWatcher{}
	testDumClient := &data_upload_manager.EmergencyClient{}
	testConfigClient := &config.ConfigClient{}
	testConfigTree := &config.ConfigTree{}
	testCameraState := &state.OverallCameraState{}

	var gotCaptureSvc frontend.DataCaptureServiceServer
	frontendRegisterDataCaptureServiceServer = func(s grpc.ServiceRegistrar, srv frontend.DataCaptureServiceServer) {
		assert.Equal(t, testGrpcServer, s)
		gotCaptureSvc = srv
	}
	captureSvc := NewDataCaptureService(testGrpcServer, testDataState, testDataWatcher, testDumClient, testConfigClient, testConfigTree, testConfigTree, testCameraState)

	assert.Equal(t, testDataState, captureSvc.dataState)
	assert.Equal(t, testDataWatcher, captureSvc.dataWatcher)
	assert.Equal(t, testDumClient, captureSvc.dataUploadManagerClient)
	assert.Equal(t, testConfigClient, captureSvc.configClient)
	assert.Equal(t, testConfigTree, captureSvc.nodeCommander)
	assert.Equal(t, testConfigTree, captureSvc.nodeDataUploadManager)
	assert.Equal(t, gotCaptureSvc, captureSvc)
}

func TestDataCaptureService_StartDataCapture(t *testing.T) {
	testTime := time.Now()
	saveTimeNow := timeNow
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	saveDefaultDataCaptureSession := defaultDataCaptureSession
	saveGetDataCaptureParams := getDataCaptureParams
	defer func() {
		timeNow = saveTimeNow
		defaultDataCaptureStep = saveDefaultDataCaptureStep
		defaultDataCaptureSession = saveDefaultDataCaptureSession
		getDataCaptureParams = saveGetDataCaptureParams
	}()

	tests := []struct {
		name                       string
		request                    *frontend.StartDataCaptureRequest
		config                     dataCaptureConfig
		dataCaptureStep            state.DataCaptureStep
		dumStartDataCaptureSessErr error
		expectedSession            state.DataCaptureSession
		expectError                bool
	}{
		{"success",
			&frontend.StartDataCaptureRequest{
				Name:        "test",
				Rate:        10,
				Crop:        "onion",
				SnapCapture: false,
			},
			dataCaptureConfig{},
			state.DataCaptureStepNew,
			nil,
			state.DataCaptureSession{
				CaptureRate: 10,
				SessionName: "test",
				Crop:        "onion",
			},
			false,
		},
		{"step error",
			&frontend.StartDataCaptureRequest{
				Name:        "test",
				Rate:        10,
				Crop:        "onion",
				SnapCapture: false,
			},
			dataCaptureConfig{},
			state.DataCaptureStepUploadingUSBPaused,
			nil,
			state.DataCaptureSession{
				CaptureRate: 10,
				SessionName: "test",
				Crop:        "onion",
			},
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockState.On("WriteOnCurrent", mock.AnythingOfType("func()")).Return()
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StartDataCaptureSession",
				fmt.Sprintf("%s-%d", test.request.Name, testTime.Unix()),
				test.request.Rate,
				test.request.Crop,
				test.request.CropId,
				test.config.enableSinglePredictCapture,
				uint32(test.config.RowInd),
				test.config.PredictCamID,
				test.request.SnapCapture,
			).Return(test.dumStartDataCaptureSessErr)

			getDataCaptureParams = func(s *DataCaptureService) dataCaptureConfig {
				return test.config
			}
			timeNow = func() time.Time {
				return testTime
			}
			defaultDataCaptureStep = test.dataCaptureStep

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}

			resp, err := captureSvc.StartDataCapture(context.TODO(), test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
				mockState.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_PauseDataCapture(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name                       string
		dataCaptureStep            state.DataCaptureStep
		pauseDataCaptureSessionErr error
		expectError                bool
	}{
		{
			"success",
			state.DataCaptureStepCapturing,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepCaptureComplete,
			nil,
			true,
		},
		{
			"failed to pause data capture session",
			state.DataCaptureStepCapturing,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("PauseDataCaptureSession").
				Return(test.pauseDataCaptureSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.PauseDataCapture(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StopDataCapture(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name               string
		dataCaptureStep    state.DataCaptureStep
		stopDataCaptureErr error
		expectError        bool
	}{
		{
			"success",
			state.DataCaptureStepCapturePaused,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepUploadingUSB,
			nil,
			true,
		},
		{
			"failed to stop data capture session",
			state.DataCaptureStepCapturePaused,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StopDataCaptureSession").
				Return(test.stopDataCaptureErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StopDataCapture(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_ResumeDataCapture(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name                 string
		dataCaptureStep      state.DataCaptureStep
		resumeDataCaptureErr error
		expectError          bool
	}{
		{
			"success",
			state.DataCaptureStepCapturePaused,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepUploadingUSB,
			nil,
			true,
		},
		{
			"failed to resume data capture session",
			state.DataCaptureStepCapturePaused,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("ResumeDataCaptureSession").
				Return(test.resumeDataCaptureErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.ResumeDataCapture(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_CompleteDataCapture(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name                          string
		dataCaptureStep               state.DataCaptureStep
		completeDataCaptureSessionErr error
		expectError                   bool
	}{
		{
			"success",
			state.DataCaptureStepCapturePaused,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepUploadingUSB,
			nil,
			true,
		},
		{
			"failed to complete data capture",
			state.DataCaptureStepCapturePaused,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("CompleteDataCaptureSession").
				Return(test.completeDataCaptureSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.CompleteDataCapture(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StartDataCaptureWirelessUpload(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	saveDefaultWirelessUploadAvailable := defaultWirelessUploadAvailable
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
		defaultWirelessUploadAvailable = saveDefaultWirelessUploadAvailable
	}()

	tests := []struct {
		name                      string
		dataCaptureStep           state.DataCaptureStep
		wirelessAvailable         bool
		startDataUploadSessionErr error
		expectError               bool
	}{
		{
			"success",
			state.DataCaptureStepCaptureComplete,
			true,
			nil,
			false,
		},
		{
			"wireless unavailable",
			state.DataCaptureStepCaptureComplete,
			false,
			nil,
			true,
		},
		{
			"invalid step",
			state.DataCaptureStepUploadingUSB,
			true,
			nil,
			true,
		},
		{
			"failed to start data upload session",
			state.DataCaptureStepCaptureComplete,
			true,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StartDataUploadSession", "wireless").
				Return(test.startDataUploadSessionErr)

			defaultDataCaptureStep = test.dataCaptureStep
			defaultWirelessUploadAvailable = test.wirelessAvailable
			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StartDataCaptureWirelessUpload(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StartDataCaptureUSBUpload(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	saveDefaultUSBStorageConnected := defaultUSBStorageConnected
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
		defaultUSBStorageConnected = saveDefaultUSBStorageConnected
	}()

	tests := []struct {
		name                      string
		dataCaptureStep           state.DataCaptureStep
		usbStorageConnected       bool
		startDataUploadSessionErr error
		expectError               bool
	}{
		{
			"success",
			state.DataCaptureStepCaptureComplete,
			true,
			nil,
			false,
		},
		{
			"usb not connected",
			state.DataCaptureStepCaptureComplete,
			false,
			nil,
			true,
		},
		{
			"invalid step",
			state.DataCaptureStepUploadingUSB,
			true,
			nil,
			true,
		},
		{
			"failed to start data upload session",
			state.DataCaptureStepCaptureComplete,
			true,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StartDataUploadSession", "usb").
				Return(test.startDataUploadSessionErr)

			defaultDataCaptureStep = test.dataCaptureStep
			defaultUSBStorageConnected = test.usbStorageConnected
			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StartDataCaptureUSBUpload(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StopDataCaptureUpload(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name                     string
		dataCaptureStep          state.DataCaptureStep
		stopDataUploadSessionErr error
		expectError              bool
	}{
		{
			"success",
			state.DataCaptureStepUploadingUSB,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepNew,
			nil,
			true,
		},
		{
			"failed to stop data upload session",
			state.DataCaptureStepUploadingUSB,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StopDataUploadSession").
				Return(test.stopDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StopDataCaptureUpload(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_PauseDataCaptureUpload(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name                     string
		dataCaptureStep          state.DataCaptureStep
		stopDataUploadSessionErr error
		expectError              bool
	}{
		{
			"success",
			state.DataCaptureStepUploadingWireless,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepCaptureComplete,
			nil,
			true,
		},
		{
			"failed to pause data upload session",
			state.DataCaptureStepUploadingWireless,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("PauseDataUploadSession").
				Return(test.stopDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.PauseDataCaptureUpload(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_ResumeDataCaptureUpload(t *testing.T) {
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	defer func() {
		defaultDataCaptureStep = saveDefaultDataCaptureStep
	}()

	tests := []struct {
		name                       string
		dataCaptureStep            state.DataCaptureStep
		resumeDataUploadSessionErr error
		expectError                bool
	}{
		{
			"success",
			state.DataCaptureStepUploadingWirelessPaused,
			nil,
			false,
		},
		{
			"invalid step",
			state.DataCaptureStepCaptureComplete,
			nil,
			true,
		},
		{
			"failed to pause data upload session",
			state.DataCaptureStepUploadingWirelessPaused,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("ResumeDataUploadSession").
				Return(test.resumeDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			defaultDataCaptureStep = test.dataCaptureStep
			captureSvc := &DataCaptureService{
				dataState:               mockState,
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.ResumeDataCaptureUpload(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StartBackgroundDataCaptureWirelessUpload(t *testing.T) {
	testSessionName := "some-session-name"
	tests := []struct {
		name                                string
		startBackgroundDataUploadSessionErr error
		expectError                         bool
	}{
		{
			"success",
			nil,
			false,
		},
		{
			"failed to start background data upload session",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StartBackgroundDataUploadSession", "wireless", testSessionName).
				Return(test.startBackgroundDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StartBackgroundDataCaptureWirelessUpload(context.TODO(), &frontend.SessionName{Name: testSessionName})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StartBackgroundDataCaptureUSBUpload(t *testing.T) {
	testSessionName := "some-session-name"
	tests := []struct {
		name                                string
		startBackgroundDataUploadSessionErr error
		expectError                         bool
	}{
		{
			"success",
			nil,
			false,
		},
		{
			"failed to start background data capture usb upload",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StartBackgroundDataUploadSession", "usb", testSessionName).
				Return(test.startBackgroundDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StartBackgroundDataCaptureUSBUpload(context.TODO(), &frontend.SessionName{Name: testSessionName})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_StopBackgroundDataCaptureUpload(t *testing.T) {
	testSessionName := "some-session-name"
	tests := []struct {
		name                                string
		startBackgroundDataUploadSessionErr error
		expectError                         bool
	}{
		{
			"success",
			nil,
			false,
		},
		{
			"failed to stop background data upload",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("StopBackgroundDataUploadSession", testSessionName).
				Return(test.startBackgroundDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.StopBackgroundDataCaptureUpload(context.TODO(), &frontend.SessionName{Name: testSessionName})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_PauseBackgroundDataCaptureUpload(t *testing.T) {
	testSessionName := "some-session-name"
	tests := []struct {
		name                                string
		pauseBackgroundDataUploadSessionErr error
		expectError                         bool
	}{
		{
			"success",
			nil,
			false,
		},
		{
			"failed to pause background data upload session",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("PauseBackgroundDataUploadSession", testSessionName).
				Return(test.pauseBackgroundDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.PauseBackgroundDataCaptureUpload(context.TODO(), &frontend.SessionName{Name: testSessionName})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_ResumeBackgroundDataCaptureUpload(t *testing.T) {
	testSessionName := "some-session-name"
	tests := []struct {
		name                                 string
		resumeBackgroundDataUploadSessionErr error
		expectError                          bool
	}{
		{
			"success",
			nil,
			false,
		},
		{
			"failed to resume background data upload session",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("ResumeBackgroundDataUploadSession", testSessionName).
				Return(test.resumeBackgroundDataUploadSessionErr)

			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockDataWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}

			captureSvc := &DataCaptureService{
				dataWatcher:             mockDataWatcher,
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.ResumeBackgroundDataCaptureUpload(context.TODO(), &frontend.SessionName{Name: testSessionName})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, 1, triggerCount)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_SnapImages(t *testing.T) {
	tests := []struct {
		name          string
		crop          string
		cropID        string
		snapImagesErr error
		expectError   bool
		sessionName   string
		camId         string
		rowInd        uint32
		timestampMs   int64
	}{
		{
			"success crop",
			"onion",
			"",
			nil,
			false,
			"test-1",
			"predict1",
			1,
			1234567890,
		},
		{
			"success cropID",
			"",
			"123-456",
			nil,
			false,
			"test-2",
			"predict2",
			1,
			1234567890,
		},
		{
			"success both",
			"onion",
			"123-345",
			nil,
			false,
			"test-3",
			"predict1",
			0,
			1234567890,
		},
		{
			"failed to snap images",
			"onion",
			"",
			assert.AnError,
			true,
			"test-4",
			"predict1",
			0,
			1234567890,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedCrop := test.crop
			expectedCropID := test.cropID
			expectedSessionName := test.sessionName
			expectedCamId := test.camId
			expectedRowInd := test.rowInd
			expectedTimestampMs := test.timestampMs
			if len(test.cropID) > 0 {
				expectedCrop = ""
			}

			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("SnapImages", expectedCrop, expectedCropID, expectedCamId, expectedRowInd, expectedTimestampMs, expectedSessionName).
				Return(test.snapImagesErr)

			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})

			mockCameraState := new(state.MockCommanderCameraState)
			mockCameraState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockCameraState.On("GetCamera", mock.AnythingOfType("string")).
				Return(&state.Camera{RowNumber: test.rowInd, LocalCameraId: test.camId}, true)

			captureSvc := &DataCaptureService{
				dataUploadManagerClient: mockDUMEClient,
				cameraState:             mockCameraState,
				dataState:               mockState,
			}
			resp, err := captureSvc.SnapImages(context.TODO(), &frontend.SnapImagesRequest{Crop: test.crop, CropId: test.cropID, SessionName: test.sessionName, CamId: &(test.camId), TimestampMs: &(test.timestampMs)})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureService_GetSessions(t *testing.T) {
	tests := []struct {
		name           string
		sessions       []data_upload_manager.Session
		getSessionsErr error
		expectError    bool
	}{
		{
			"success",
			[]data_upload_manager.Session{{
				Name:         "test",
				NumberImages: 123,
				IsUploading:  true,
				HasCompleted: false,
				IsCapturing:  true,
			}},
			nil,
			false,
		},
		{
			"failed to get sessions images",
			nil,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("GetSessions").
				Return(test.sessions, test.getSessionsErr)

			captureSvc := &DataCaptureService{
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.GetSessions(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, dumSessionsToFrontendSessions(t, test.sessions...), resp.Sessions)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func dumSessionsToFrontendSessions(t *testing.T, sessions ...data_upload_manager.Session) []*frontend.Session {
	t.Helper()

	feSessions := make([]*frontend.Session, 0, len(sessions))
	for _, session := range sessions {
		feSessions = append(feSessions, &frontend.Session{
			Name:            session.Name,
			ImagesRemaining: session.NumberImages,
			IsUploading:     session.IsUploading,
			HasCompleted:    session.HasCompleted,
			IsCapturing:     session.IsCapturing,
		})
	}
	return feSessions
}

func TestDataCaptureService_GetRegularCaptureStatus(t *testing.T) {
	tests := []struct {
		name                    string
		regularCaptureStatus    data_upload_manager.RegularCaptureStatus
		regularCaptureStatusErr error
		expectError             bool
	}{
		{
			"success",
			data_upload_manager.RegularCaptureStatus{
				Uploaded:            23,
				Budget:              2333,
				LastUploadTimestamp: 23423,
			},
			nil,
			false,
		},
		{
			"failed to get regular capture stats",
			data_upload_manager.RegularCaptureStatus{},
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockDUMEClient := new(MockDUMEmergencyClient)
			mockDUMEClient.On("GetRegularCaptureStatus").
				Return(test.regularCaptureStatus, test.regularCaptureStatusErr)

			captureSvc := &DataCaptureService{
				dataUploadManagerClient: mockDUMEClient,
			}
			resp, err := captureSvc.GetRegularCaptureStatus(context.TODO(), &frontend.Empty{})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, test.regularCaptureStatus.Uploaded, resp.Uploaded)
				assert.Equal(t, test.regularCaptureStatus.Budget, resp.Budget)
				assert.Equal(t, test.regularCaptureStatus.LastUploadTimestamp, resp.LastUploadTimestamp)
				mockDUMEClient.AssertExpectations(t)
			}
		})
	}
}

func TestDataCaptureFn(t *testing.T) {
	testTimestamp := time.Now().UnixMilli()
	saveDefaultDataCaptureSession := defaultDataCaptureSession
	saveDefaultDataCaptureStep := defaultDataCaptureStep
	saveDefaultWirelessUploadAvailable := defaultWirelessUploadAvailable
	saveDefaultUSBStorageConnected := defaultUSBStorageConnected
	defer func() {
		defaultDataCaptureSession = saveDefaultDataCaptureSession
		defaultDataCaptureStep = saveDefaultDataCaptureStep
		defaultWirelessUploadAvailable = saveDefaultWirelessUploadAvailable
		defaultUSBStorageConnected = saveDefaultUSBStorageConnected
	}()
	tests := []struct {
		name                    string
		session                 state.DataCaptureSession
		step                    state.DataCaptureStep
		wirelessUploadAvailable bool
		usbStorageConnected     bool
		expectedFeStep          frontend.ProcedureStep
	}{
		{
			"success",
			state.DataCaptureSession{
				ImagesTaken:                     123,
				TargetImagesTaken:               4343,
				EstimatedCaptureRemainingTimeMs: 5235,
				ImagesUploaded:                  2334,
				TargetImagesUploaded:            23233,
				EstimatedUploadRemainingTimeMs:  222,
				CaptureRate:                     23,
				CaptureStatus:                   "test999",
				UploadStatus:                    "asdf123",
				SessionName:                     "ffff",
				CaptureTimestampStart:           1231231,
				UploadTimestampStart:            1212,
				Crop:                            "poop",
				ErrorMessage:                    "error",
			},
			state.DataCaptureStepNew,
			true,
			true,
			frontend.ProcedureStep_NEW,
		},
		{
			"step capturing",
			state.DataCaptureSession{},
			state.DataCaptureStepCapturing,
			false,
			false,
			frontend.ProcedureStep_CAPTURING,
		},
		{
			"step capture paused",
			state.DataCaptureSession{},
			state.DataCaptureStepCapturePaused,
			false,
			false,
			frontend.ProcedureStep_CAPTURE_PAUSED,
		},
		{
			"step capture complete",
			state.DataCaptureSession{},
			state.DataCaptureStepCaptureComplete,
			false,
			false,
			frontend.ProcedureStep_CAPTURE_COMPLETE,
		},
		{
			"step upload wireless",
			state.DataCaptureSession{},
			state.DataCaptureStepUploadingWireless,
			false,
			false,
			frontend.ProcedureStep_UPLOADING_WIRELESS,
		},
		{
			"step wireless paused",
			state.DataCaptureSession{},
			state.DataCaptureStepUploadingWirelessPaused,
			false,
			false,
			frontend.ProcedureStep_UPLOADING_WIRELESS_PAUSED,
		},
		{
			"step upload usb",
			state.DataCaptureSession{},
			state.DataCaptureStepUploadingUSB,
			false,
			false,
			frontend.ProcedureStep_UPLOADING_USB,
		},
		{
			"step upload usb paused",
			state.DataCaptureSession{},
			state.DataCaptureStepUploadingUSBPaused,
			false,
			false,
			frontend.ProcedureStep_UPLOADING_USB_PAUSED,
		},
		{
			"step upload complete",
			state.DataCaptureSession{},
			state.DataCaptureStepUploadingComplete,
			false,
			false,
			frontend.ProcedureStep_UPLOADING_COMPLETE,
		},
		{
			"unknown/default step",
			state.DataCaptureSession{},
			state.DataCaptureStep(999999999),
			false,
			false,
			frontend.ProcedureStep_NEW,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("GetTimestampMs").Return(testTimestamp)

			defaultUSBStorageConnected = test.usbStorageConnected
			defaultWirelessUploadAvailable = test.wirelessUploadAvailable
			defaultDataCaptureSession = test.session
			captureSvc := &DataCaptureService{
				dataState: mockState,
			}
			resp := &frontend.DataCaptureState{}
			captureSvc.dataCaptureStateFn(resp)()
			assert.Equal(t, test.session.CaptureStatus, resp.CaptureStatus)
			assert.Equal(t, test.session.EstimatedCaptureRemainingTimeMs, resp.EstimatedCaptureRemainingTimeMs)
			assert.Equal(t, test.session.EstimatedUploadRemainingTimeMs, resp.EstimatedUploadRemainingTimeMs)
			assert.Equal(t, test.session.ImagesTaken, resp.ImagesTaken)
			assert.Equal(t, test.session.CaptureRate, resp.Rate.Rate)
			assert.Equal(t, test.session.Crop, resp.Crop)
			assert.Equal(t, test.session.SessionName, resp.SessionName)
			assert.Equal(t, test.session.TargetImagesTaken, resp.TargetImagesTaken)
			assert.Equal(t, test.session.TargetImagesUploaded, resp.TargetImagesUploaded)
			assert.Equal(t, test.session.UploadStatus, resp.UploadStatus)
			assert.Equal(t, test.usbStorageConnected, resp.UsbStorageConnected)
			assert.Equal(t, test.wirelessUploadAvailable, resp.WirelessUploadAvailable)
			assert.Equal(t, testTimestamp, resp.Ts.TimestampMs)
			assert.Equal(t, test.session.ErrorMessage, resp.ErrorMessage)
		})
	}
}

func TestDataCaptureService_GetNextDataCaptureState(t *testing.T) {
	testTimestamp := time.Now().UnixMilli()
	tests := []struct {
		name             string
		readOnNextResult bool
		expectError      bool
	}{
		{
			"success",
			true,
			false,
		},
		{
			"error",
			false,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(statemocks.ManagedState)
			mockState.On("ReadOnNext",
				mock.AnythingOfType("context.todoCtx"),
				mock.MatchedBy(func(ts int64) bool {
					return assert.Equal(t, testTimestamp, ts)
				}),
				mock.AnythingOfType("func()"),
			).Return(test.readOnNextResult)
			captureSvc := &DataCaptureService{
				dataState: mockState,
			}
			resp, err := captureSvc.GetNextDataCaptureState(context.TODO(), &frontend.Timestamp{TimestampMs: testTimestamp})
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				mockState.AssertExpectations(t)
			}
		})
	}
}

package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type FeatureService struct {
	frontend.UnimplementedFeatureServiceServer
	featureState *state.FeatureState
	robot        *environment.Robot
	rowClients   map[int]*rows.RowClients
}

func NewFeatureService(grpcServer *grpc.Server, featureState *state.FeatureState, robot *environment.Robot, rowClients map[int]*rows.RowClients) *FeatureService {
	svc := &FeatureService{
		featureState: featureState,
		robot:        robot,
		rowClients:   rowClients,
	}
	frontend.RegisterFeatureServiceServer(grpcServer, svc)
	return svc
}

func (svc *FeatureService) GetNextFeatureFlags(ctx context.Context, req *frontend.Timestamp) (*frontend.FeatureFlags, error) {
	featureFlags := &frontend.FeatureFlags{}
	result, flags, ts := svc.featureState.GetNextFeatureFlags(ctx, req.GetTimestampMs())
	if !result {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Feature Flags Retrieved")
	}
	featureFlags.Ts = &frontend.Timestamp{TimestampMs: ts}
	featureFlags.Flags = flags
	return featureFlags, nil
}

func fillRows(r *frontend.RobotConfiguration, rows int32, predicts int32, targets int32) {
	r.NumRows = rows
	var i int32
	for i = 1; i <= rows; i++ {
		r.RowConfiguration[i] = &frontend.RowConfiguration{
			NumPredicts: predicts,
			NumTargets:  targets,
		}
	}
}

func (s *FeatureService) fillReaperRowsFromClients(r *frontend.RobotConfiguration) {
	r.NumRows = int32(len(s.rowClients))
	for i, row := range s.rowClients {
		numCVs := len(row.CVRuntimeClients)
		numPredicts := int32(numCVs)
		numTargets := int32(numCVs) * 2
		r.RowConfiguration[int32(i)] = &frontend.RowConfiguration{
			NumPredicts: numPredicts,
			NumTargets:  numTargets,
		}
	}
}

func (s *FeatureService) GetRobotConfiguration(context.Context, *frontend.Empty) (*frontend.RobotConfiguration, error) {
	r := &frontend.RobotConfiguration{RowConfiguration: make(map[int32]*frontend.RowConfiguration)}
	gen := environment.CarbonGen(s.robot.MakaGen)
	switch gen {
	case environment.CarbonGenBud:
		fillRows(r, 1, 4, 8)
		r.Generation = frontend.Generation_Undefined
	case environment.CarbonGenSlayer:
		fillRows(r, 3, 4, 10)
		r.Generation = frontend.Generation_Slayer
	case environment.CarbonGenReaper:
		s.fillReaperRowsFromClients(r)
		r.Generation = frontend.Generation_Reaper
	default:
		r.Generation = frontend.Generation_Undefined
	}
	return r, nil
}

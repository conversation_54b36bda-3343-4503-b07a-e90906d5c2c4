package services

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	logging_pb "github.com/carbonrobotics/robot/golang/generated/proto/logging"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type DebugService struct {
	frontend.UnimplementedDebugServiceServer
	alarmState  *state.AlarmState
	serial      string
	env         *environment.Robot
	rowClients  map[int]*rows.RowClients
	spatial     *state.SpatialMetricsSyncManager
	profileSync *state.ProfileSyncManager
}

func NewDebugService(grpcServer *grpc.Server, alarmState *state.AlarmState, env *environment.Robot, rowClients map[int]*rows.RowClients, spatial *state.SpatialMetricsSyncManager, profileSync *state.ProfileSyncManager) *DebugService {
	service := &DebugService{
		alarmState:  alarmState,
		serial:      env.MakaRobotName,
		env:         env,
		rowClients:  rowClients,
		spatial:     spatial,
		profileSync: profileSync,
	}
	frontend.RegisterDebugServiceServer(grpcServer, service)
	return service
}

func (debugService *DebugService) GetRobot(ctx context.Context, req *frontend.Empty) (*frontend.RobotMessage, error) {
	alarmList := make([]*frontend.AlarmRow, 0)
	debugService.alarmState.ReadOnCurrent(func() {
		for _, prefix := range debugService.alarmState.Alarms {
			for _, alarm := range prefix {
				alarmList = append(alarmList, alarms.Alarm2Frontend(alarm))
			}
		}
	})
	return &frontend.RobotMessage{
		Serial: debugService.serial,
		Alarms: alarmList,
	}, nil
}

func (d *DebugService) getClientAddr(component string, row_id int32) (string, error) {
	commanderMap := map[string]int{
		"commander":           61002,
		"software_manager":    61005,
		"hardware_manager":    61006,
		"data_upload_manager": 61003,
		"host_check":          6943,
		"config":              61001,
		"calibration":         61009,
	}
	rowMap := map[string]int{
		"software_manager": 61005,
		"aimbot":           6942,
		"host_check":       6943,
		"model_receiver":   61004,
		"weed_tracking":    65432,
		"cv":               15053,
	}

	if d.env.MakaGen == string(environment.CarbonGenBud) {
		if port, ok := commanderMap[component]; ok {
			return fmt.Sprintf("127.0.0.1:%v", port), nil
		}
		if port, ok := rowMap[component]; ok {
			return fmt.Sprintf("127.0.0.1:%v", port), nil
		}
	} else if d.env.MakaGen == string(environment.CarbonGenReaper) {
		if port, ok := commanderMap[component]; ok {
			return fmt.Sprintf("127.0.0.1:%v", port), nil
		}
		if row_id < 1 || row_id > 9 { // TODO this will need addressing for reaper1+ once we figure out the networking there
			return "", errors.New(fmt.Sprintf("Module %v doesn't exist", row_id))
		}
		if port, ok := rowMap[component]; ok {
			return fmt.Sprintf("*********%v:%v", row_id, port), nil
		}
	} else {
		if port, ok := commanderMap[component]; ok {
			return fmt.Sprintf("127.0.0.1:%v", port), nil
		}
		if row_id < 1 || row_id > 3 {
			return "", errors.New(fmt.Sprintf("Row %v doesn't exist", row_id))
		}
		if port, ok := rowMap[component]; ok {
			return fmt.Sprintf("*********%v:%v", row_id, port), nil
		}
	}

	return "", errors.New(fmt.Sprintf("Component %v not found or not supported", component))
}

func (d *DebugService) sendSetLogLevelReq(component string, row int32, level logging_pb.LogLevel) error {
	addr, err := d.getClientAddr(component, row)
	if err != nil {
		logrus.WithError(err).Errorf("Could not set loglevel for request %v:%v:%v", component, row, level)
		return err
	}

	client := logging.NewLoggingClient(addr)
	err = client.SetLogLevel(level)
	if err != nil {
		logrus.WithError(err).Errorf("Could not set loglevel for request %v:%v:%v", component, row, level)
		return err
	}

	logrus.Infof("Successfully set loglevel for request %v:%v:%v", component, row, level)
	return nil
}

func (d *DebugService) SetLogLevel(ctx context.Context, req *frontend.SetLogLevelRequest) (*frontend.Empty, error) {
	err := d.sendSetLogLevelReq(req.Component, req.RowNum, req.Level)
	if err != nil {
		return nil, err
	}
	if req.Component == "aimbot" && d.env.MakaGen != "bud" {
		err = d.sendSetLogLevelReq("weed_tracking", req.RowNum, req.Level)
		if err != nil {
			return nil, err
		}
	}
	return &frontend.Empty{}, nil
}

func (d *DebugService) replayFilename(filename string, row_idx int) string {
	datadir := d.env.MakaDataDir
	if !strings.HasPrefix(filename, datadir) {
		filename = datadir + "/" + filename
	}
	dot := strings.LastIndex(filename, ".")
	if dot == -1 {
		return fmt.Sprintf("%v.row%v", filename, row_idx)
	} else {
		return fmt.Sprintf("%v.row%v.%v", filename[0:dot], row_idx, filename[dot+1:])
	}
}

func (d *DebugService) StartSavingCropLineDetectionReplay(ctx context.Context, req *weed_tracking.StartSavingCropLineDetectionReplayRequest) (*weed_tracking.Empty, error) {
	for id, clients := range d.rowClients {
		rowReq := &weed_tracking.StartSavingCropLineDetectionReplayRequest{
			Filename: d.replayFilename(req.Filename, id),
			TtlMs:    req.TtlMs,
		}
		clients.WeedTrackingClient.StartSavingCropLineDetectionReplay(rowReq)
	}
	return &weed_tracking.Empty{}, nil
}

func (d *DebugService) StartRecordingAimbotInputs(ctx context.Context, req *weed_tracking.RecordAimbotInputRequest) (*weed_tracking.Empty, error) {
	wg := sync.WaitGroup{}
	wg.Add(len(d.rowClients))
	for _, clients := range d.rowClients {
		go func(c *rows.RowClients) {
			defer wg.Done()
			c.WeedTrackingClient.StartRecordingAimbotInputs(req)
		}(clients)
	}
	wg.Wait()
	return &weed_tracking.Empty{}, nil
}

func (d *DebugService) AddMockSpatialMetricsBlock(ctx context.Context, req *frontend.Empty) (*frontend.Empty, error) {
	err := d.spatial.AddMockBlock()
	return &frontend.Empty{}, err
}

func (d *DebugService) DeleteProfileSyncData(ctx context.Context, req *frontend.Empty) (*frontend.Empty, error) {
	err := d.profileSync.DeleteProfileSyncData()
	return &frontend.Empty{}, err
}

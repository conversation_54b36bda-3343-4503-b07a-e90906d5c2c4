package services

import (
	"context"
	"sort"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type CategoryCollectionCfg interface {
	GetNextCategoryCollectionsData(ctx context.Context, timestampMs int64) (bool, []*category.CategoryCollection, int64)
	GetNextActiveId(ctx context.Context, timestampMs int64) (bool, string, int64, bool, int64)
	SetActiveCfg(id string) error
	ReloadCVCategoryCollection() error
}

type CategoryCollectionService struct {
	frontend.UnimplementedCategoryCollectionServiceServer
	redisClient           *redis.Client
	categoryCollectionCfg CategoryCollectionCfg
}

func NewCategoryCollectionService(grpcServer *grpc.Server, redisClient *redis.Client, categoryCollectionCfg CategoryCollectionCfg) *CategoryCollectionService {
	service := &CategoryCollectionService{
		redisClient:           redisClient,
		categoryCollectionCfg: categoryCollectionCfg,
	}
	frontend.RegisterCategoryCollectionServiceServer(grpcServer, service)
	return service
}

func (s *CategoryCollectionService) GetNextCategoryCollectionsData(ctx context.Context, req *frontend.GetNextCategoryCollectionsDataRequest) (*frontend.GetNextCategoryCollectionsDataResponse, error) {
	success, categoryCollections, newTs := s.categoryCollectionCfg.GetNextCategoryCollectionsData(ctx, req.GetTs().GetTimestampMs())
	if !success {
		return nil, status.Error(codes.Aborted, "context cancelled before Category Collection data was retrieved")
	}

	resp := &frontend.GetNextCategoryCollectionsDataResponse{
		Ts:                  &frontend.Timestamp{TimestampMs: newTs},
		CategoryCollections: categoryCollections,
	}
	sort.SliceStable(resp.CategoryCollections, func(i, j int) bool {
		return resp.CategoryCollections[i].Name < resp.CategoryCollections[j].Name
	})

	return resp, nil
}

func (s *CategoryCollectionService) SetActiveCategoryCollectionId(ctx context.Context, req *frontend.SetActiveCategoryCollectionIdRequest) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "context cancelled before active Category Collection Id was set")
	}

	err := s.categoryCollectionCfg.SetActiveCfg(req.GetUuid())
	if err != nil {
		logrus.Warnf("failed to set active Category Collection Id %v", err)
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *CategoryCollectionService) GetNextActiveCategoryCollectionId(ctx context.Context, req *frontend.GetNextActiveCategoryCollectionIdRequest) (*frontend.GetNextActiveCategoryCollectionIdResponse, error) {
	success, id, ts, reloadRequired, lastUpdatedTimestampMs := s.categoryCollectionCfg.GetNextActiveId(ctx, req.GetTs().GetTimestampMs())
	if !success {
		return nil, status.Error(codes.Aborted, "context cancelled before active Category Collection Id was retrieved")
	}

	return &frontend.GetNextActiveCategoryCollectionIdResponse{
		Uuid:                   id,
		Ts:                     &frontend.Timestamp{TimestampMs: ts},
		ReloadRequired:         reloadRequired,
		LastUpdatedTimestampMs: lastUpdatedTimestampMs,
	}, nil
}

func (s *CategoryCollectionService) ReloadCategoryCollection(ctx context.Context, req *frontend.Empty) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "context cancelled before active Category Collection Id was set")
	}

	err := s.categoryCollectionCfg.ReloadCVCategoryCollection()
	if err != nil {
		logrus.Warnf("failed to reload Category Collection %v", err)
		return nil, err
	}
	return &frontend.Empty{}, nil
}

package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	// test point
	frontendRegisterCrosshairServiceServer = frontend.RegisterCrosshairServiceServer
	getAimbotClient                        = func(rowClients map[int]*rows.RowClients, row int) (AimbotClient, bool) {
		if cl, ok := rowClients[row]; ok {
			return cl.AimbotClient, true
		}
		return nil, false
	}
)

type AimbotClient interface {
	SetCrosshairPosition(scannerId uint32, x uint32, y uint32) error
	MoveScanner(scannerId uint32, x uint32, y uint32) error
	StartAutoCalibrateCrosshair(scannerId uint32) error
}

type OverallCameraState interface {
	state.ManagedState
	GetCamera(camID string) (*state.Camera, bool)
}

type OverallScannerState interface {
	state.ManagedState
	GetScannerState(camID string) (state.ScannerState, bool)
	GetNextXHairProgress(ts int64, failedScanners *[]string, ctx context.Context) (bool, float32, int64, bool)
}

type CrosshairService struct {
	frontend.UnimplementedCrosshairServiceServer
	scState     OverallScannerState
	scWatcher   state.Triggerable
	rows        map[int]*rows.RowClients
	cameraState OverallCameraState
}

func NewCrosshairService(grpcServer *grpc.Server, scState OverallScannerState, scWatcher *state.ScannerWatcher, rowClients map[int]*rows.RowClients, cameraState OverallCameraState) *CrosshairService {
	service := &CrosshairService{
		scState:     scState,
		scWatcher:   scWatcher,
		rows:        rowClients,
		cameraState: cameraState,
	}
	frontendRegisterCrosshairServiceServer(grpcServer, service)
	return service
}

func (s *CrosshairService) StartAutoCalibrateCrosshair(_ context.Context, req *frontend.CameraRequest) (*frontend.Empty, error) {
	logrus.Infof("Start Calibrate Crosshair: %s", req.CamId)

	scannerResp := new(getScannerResp)
	s.scState.ReadOnCurrent(s.getScannerStateFn(scannerResp, req.CamId))
	if scannerResp.ScannerState == nil {
		return nil, status.Error(codes.NotFound, "Camera ID not found")
	}
	scanner := scannerResp.ScannerState
	if scanner.Crosshair.Calibrating {
		return nil, status.Error(codes.NotFound, "Camera ID already calibrating")
	}

	rowNum := scanner.Descriptor.RowNumber
	targetID := scanner.Descriptor.TargetID()
	if client, ok := getAimbotClient(s.rows, int(rowNum)); ok {
		err := client.StartAutoCalibrateCrosshair(targetID)
		if err != nil {
			return nil, err
		}
	}
	return &frontend.Empty{}, nil
}

func (s *CrosshairService) StartAutoCalibrateAllCrosshairs(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Info("Start Calibrate All Crosshairs")

	for _, rowClients := range s.rows {
		err := rowClients.AimbotClient.StartAutoCalibrateAllCrosshairs()
		if err != nil {
			return nil, err
		}
	}

	return &frontend.Empty{}, nil
}

func (s *CrosshairService) StopAutoCalibrate(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Info("Stop Calibrate Crosshairs")

	for _, rowClients := range s.rows {
		rowClients.AimbotClient.StopAutoCalibrate()
	}

	return &frontend.Empty{}, nil
}

func (s *CrosshairService) GetNextCrosshairState(ctx context.Context, req *frontend.CrosshairPositionRequest) (*frontend.CrosshairPositionState, error) {
	ts := req.Ts.GetTimestampMs()
	resp := &frontend.CrosshairPositionState{}
	camResp := new(getCamResp)
	s.cameraState.ReadOnCurrent(s.getCameraFn(camResp, req.CamId))
	if camResp.Camera == nil {
		return nil, status.Error(codes.Aborted, "CameraId not Found")
	}

	result := s.scState.ReadOnNext(ctx, ts, s.getCrosshairPositionFn(camResp.Camera, req, resp))
	if resp.Pos == nil {
		return nil, status.Error(codes.Aborted, "CameraId crosshair position not Found")
	}

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Crosshair Retrieved")
}

func (s *CrosshairService) SetCrosshairPosition(_ context.Context, req *frontend.SetCrosshairPositionRequest) (*frontend.Empty, error) {
	logrus.Infof("Set Crosshair Position: %s, (%f, %f)", req.CamId, req.Pos.X, req.Pos.Y)

	scannerResp := new(getScannerResp)
	s.scState.ReadOnCurrent(s.getScannerStateFn(scannerResp, req.CamId))
	if scannerResp.ScannerState == nil {
		return nil, status.Error(codes.Aborted, "Scanner state for CameraId not Found")
	}
	scannerState := scannerResp.ScannerState

	rowNum := scannerState.Descriptor.RowNumber
	targetID := scannerState.Descriptor.TargetID()
	camResp := new(getCamResp)
	s.scState.ReadOnCurrent(s.getCameraFn(camResp, req.CamId))
	if camResp.Camera == nil {
		return nil, status.Error(codes.Aborted, "CameraId not Found")
	}

	if client, ok := getAimbotClient(s.rows, int(rowNum)); ok {
		x := uint32(req.Pos.X * float32(camResp.Camera.Width))
		y := uint32(req.Pos.Y * float32(camResp.Camera.Height))
		if err := client.SetCrosshairPosition(targetID, x, y); err != nil {
			logrus.Error("Failed to set crosshair position:", err)
		}
	} else {
		return nil, status.Errorf(codes.Aborted, "Invalid Clients for row: %d", rowNum)
	}

	s.scWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *CrosshairService) MoveScanner(_ context.Context, req *frontend.MoveScannerRequest) (*frontend.Empty, error) {
	logrus.Infof("Move Scanner: %s, (%f, %f)", req.CamId, req.X, req.Y)

	scannerResp := new(getScannerResp)
	s.scState.ReadOnCurrent(s.getScannerStateFn(scannerResp, req.CamId))
	if scannerResp.ScannerState == nil {
		return nil, status.Error(codes.Aborted, "ScannerState not Found")
	}
	scanner := scannerResp.ScannerState

	rowNum := scanner.Descriptor.RowNumber
	targetID := scanner.Descriptor.TargetID()
	o := scanner == nil
	logrus.Infof("Row target %v, %v, %v", rowNum, targetID, o)

	camResp := new(getCamResp)
	s.cameraState.ReadOnCurrent(s.getCameraFn(camResp, req.CamId))
	if camResp.Camera == nil {
		return nil, status.Error(codes.Aborted, "CameraId not Found")
	}

	if client, ok := getAimbotClient(s.rows, int(rowNum)); ok {
		x := uint32(req.X * float32(camResp.Camera.Width))
		y := uint32(req.Y * float32(camResp.Camera.Height))
		if err := client.MoveScanner(targetID, x, y); err != nil {
			logrus.Error("Failed to move scanner:", err)
		}
	} else {
		return nil, status.Errorf(codes.Aborted, "Invalid Clients for row: %d", rowNum)
	}

	s.scWatcher.Trigger()
	return &frontend.Empty{}, nil
}
func (s *CrosshairService) GetNextAutoCrossHairCalState(ctx context.Context, req *frontend.AutoCrossHairCalStateRequest) (*frontend.AutoCrossHairCalStateResponse, error) {
	ts := req.Ts.GetTimestampMs()
	resp := &frontend.AutoCrossHairCalStateResponse{}
	in_progress, progress, new_ts, result := s.scState.GetNextXHairProgress(ts, &resp.Failed, ctx)
	resp.InProgress = in_progress
	resp.Progress = progress
	resp.Ts = &frontend.Timestamp{TimestampMs: new_ts}

	if result {
		return resp, nil
	}
	return nil, status.Error(codes.Aborted, "Context Cancelled before Progress updated")
}

func (s *CrosshairService) getCrosshairPositionFn(cam *state.Camera, req *frontend.CrosshairPositionRequest, resp *frontend.CrosshairPositionState) func() {
	return func() {
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.scState.GetTimestampMs(),
		}
		if scanner, ok := s.scState.GetScannerState(req.CamId); ok {
			resp.Pos = &frontend.CrosshairPosition{
				X: float32(scanner.Crosshair.X) / float32(cam.Width),
				Y: float32(scanner.Crosshair.Y) / float32(cam.Height),
			}
			resp.Calibrating = scanner.Crosshair.Calibrating
			resp.CalibrationFailed = scanner.Crosshair.CalibrationFailed
		}
	}
}

type getScannerResp struct {
	*state.ScannerState
}

func (s *CrosshairService) getScannerStateFn(scannerOut *getScannerResp, camID string) func() {
	return func() {
		if scanner, ok := s.scState.GetScannerState(camID); ok {
			scannerOut.ScannerState = &scanner
		} else {
			scannerOut.ScannerState = nil
		}
	}
}

type getCamResp struct {
	*state.Camera
}

func (s *CrosshairService) getCameraFn(camOut *getCamResp, camID string) func() {
	return func() {
		if camera, ok := s.cameraState.GetCamera(camID); ok {
			camOut.Camera = camera
		} else {
			camOut.Camera = nil
		}
	}
}

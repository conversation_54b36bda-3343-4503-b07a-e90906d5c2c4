package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

const DefaultName = "DEFAULT"

type BaseAlmanacCfg interface {
	GetActiveCfg() string
	Notify()
	GetNextActiveCfg(ctx context.Context, timestampMs int64) (bool, string, int64)
}
type AlmanacCfg interface {
	BaseAlmanacCfg
	GetEnabledPointCategories(lang string) map[string]string
	GetEnabledCrops(lang string) map[string]string
	GetNextConfigData(ctx context.Context, timestampMs int64, lang string) (bool, map[string]string, map[string]string, int64)
	SetActiveCfg(id string) error
	SaveAlmanacConfig(cfg *almanac.AlmanacConfig, setActive bool) error
	DeleteAlmanacConfig(id string, newActiveId string) error
}
type DiscriminatorCfg interface {
	BaseAlmanacCfg
	SetActiveCfg(id string, crop_id string) (bool, error)
	LoadDiscriminatorConfig(id string) (*almanac.DiscriminatorConfig, error)
	SaveDiscriminatorConfig(cfg *almanac.DiscriminatorConfig, associateWithActiveCrop bool) error
	DeleteDiscriminatorConfig(uuid string) error
}
type ModelinatorCfg interface {
	BaseAlmanacCfg
	FetchModelinatorConfig(string, string) (*almanac.ModelinatorConfig, error)
	FetchActiveModelinatorConfig() (*almanac.ModelinatorConfig, error)
	SaveModelinatorConfig(cfg *almanac.ModelinatorConfig) error
	ResetToDefault() error
}

type AlmanacConfigService struct {
	frontend.UnimplementedAlmanacConfigServiceServer
	redisClient      *redis.Client
	sizesCount       *config.ConfigTree
	almanacCfg       AlmanacCfg
	discriminatorCfg DiscriminatorCfg
	modelinatorCfg   ModelinatorCfg
}

func NewAlmanacService(grpcServer *grpc.Server, redisClient *redis.Client, sizesCount *config.ConfigTree, almanacCfg AlmanacCfg, discriminatorCfg DiscriminatorCfg, modelinatorCfg ModelinatorCfg) *AlmanacConfigService {
	service := &AlmanacConfigService{
		redisClient:      redisClient,
		sizesCount:       sizesCount,
		almanacCfg:       almanacCfg,
		discriminatorCfg: discriminatorCfg,
		modelinatorCfg:   modelinatorCfg,
	}
	frontend.RegisterAlmanacConfigServiceServer(grpcServer, service)
	return service
}

// Deprecated: Use GetNextConfigData instead
// GetConfigData does not support translations
func (s *AlmanacConfigService) GetConfigData(ctx context.Context, req *frontend.Empty) (*frontend.GetConfigDataResponse, error) {
	resp := &frontend.GetConfigDataResponse{
		NumSizeCategories: uint32(s.sizesCount.GetUIntValue()),
	}
	resp.WeedCategoryNames = s.almanacCfg.GetEnabledPointCategories(state.DefaultLanguage)
	resp.CropCategoryNames = s.almanacCfg.GetEnabledCrops(state.DefaultLanguage)
	return resp, nil
}
func (s *AlmanacConfigService) GetNextConfigData(ctx context.Context, req *frontend.GetNextConfigDataRequest) (*frontend.GetNextConfigDataResponse, error) {
	ts := req.GetTs().GetTimestampMs()
	lang := req.GetLang()

	// for now, point categories are treated as weeds. Eventually there will be no weeds or crops just a list of point categories
	success, enabledCrops, enabledWeeds, newTs := s.almanacCfg.GetNextConfigData(ctx, ts, lang)
	if !success {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Config Data Retrieved")
	}

	resp := &frontend.GetNextConfigDataResponse{
		Ts:                &frontend.Timestamp{TimestampMs: newTs},
		NumSizeCategories: uint32(s.sizesCount.GetUIntValue()),
		CropCategoryNames: enabledCrops,
		WeedCategoryNames: enabledWeeds,
	}

	return resp, nil
}
func (s *AlmanacConfigService) LoadAlmanacConfig(ctx context.Context, req *frontend.LoadAlmanacConfigRequest) (*frontend.LoadAlmanacConfigResponse, error) {
	conf, err := s.redisClient.LoadAlmanacConfig(req.GetId())
	if err != nil {
		return nil, err
	}
	if !s.validateAlmanacCfg(conf) {
		msg := fmt.Sprintf("Cfg %v has invalid size categories, rebuild and restart", req.GetId())
		logrus.Warn(msg)
		return nil, status.Error(codes.FailedPrecondition, msg)
	}
	resp := &frontend.LoadAlmanacConfigResponse{
		Config: conf,
	}
	return resp, nil
}
func (s *AlmanacConfigService) SaveAlmanacConfig(ctx context.Context, req *frontend.SaveAlmanacConfigRequest) (*frontend.SaveAlmanacConfigResponse, error) {
	exists := false
	var err error
	cfg := req.GetConfig()
	if !s.validateAlmanacCfg(cfg) {
		return nil, status.Error(codes.FailedPrecondition, "Cfg is invalid.")
	}
	if cfg.GetId() != "" {
		exists, err = s.redisClient.HExists(redis.AlmanacCfgs, cfg.GetId())
		if err != nil {
			logrus.Warnf("Failed to check if key (%v) already exists: %v", cfg.GetId(), err)
			return nil, err
		}
	}
	if !exists {
		cfg.Id = uuid.New().String()
	}
	err = s.almanacCfg.SaveAlmanacConfig(cfg, req.GetSetActive())
	if err != nil {
		return nil, err
	}
	resp := &frontend.SaveAlmanacConfigResponse{
		Id: cfg.Id,
	}
	return resp, nil
}
func (s *AlmanacConfigService) SetActiveAlmanacConfig(ctx context.Context, req *frontend.SetActiveAlmanacConfigRequest) (*frontend.Empty, error) {
	err := s.almanacCfg.SetActiveCfg(req.GetId())
	if err != nil {
		logrus.Warnf("Failed to set active Cfg %v", err)
		return nil, err
	}
	return &frontend.Empty{}, nil
}
func (s *AlmanacConfigService) DeleteAlmanacConfig(ctx context.Context, req *frontend.DeleteAlmanacConfigRequest) (*frontend.Empty, error) {
	err := s.almanacCfg.DeleteAlmanacConfig(req.GetId(), req.GetNewActiveId())
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *AlmanacConfigService) GetNextAlmanacConfig(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextAlmanacConfigResponse, error) {
	ts := req.GetTimestampMs()
	success, curr, newTs := s.almanacCfg.GetNextActiveCfg(ctx, ts)
	if !success {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Almanac cfg Retrieved")
	}
	data, err := s.redisClient.HGetAll(redis.AlmanacCfgs)
	if err != nil {
		logrus.Warnf("Failed to fetch almanac cfgs from redis.")
		return nil, err
	}
	resp := &frontend.GetNextAlmanacConfigResponse{
		Ts:     &frontend.Timestamp{TimestampMs: newTs},
		Active: curr,
	}
	resp.Available = make(map[string]string)
	for id, dataStr := range data {
		conf := &almanac.AlmanacConfig{}
		perr := proto.Unmarshal([]byte(dataStr), conf)
		if perr != nil {
			logrus.Warnf("Failed to unmarshal %v: %v", id, perr)
			continue
		}
		resp.Available[id] = conf.GetName()
	}
	return resp, nil
}

func (s *AlmanacConfigService) LoadDiscriminatorConfig(ctx context.Context, req *frontend.LoadDiscriminatorConfigRequest) (*frontend.LoadDiscriminatorConfigResponse, error) {
	conf, err := s.discriminatorCfg.LoadDiscriminatorConfig(req.GetId())
	if err != nil {
		return nil, err
	}
	if !s.validateDiscriminatorCfg(conf) {
		logrus.Warnf("Cfg %v has invalid size categories, rebuild and restart", req.GetId())
		return nil, status.Error(codes.FailedPrecondition, fmt.Sprintf("Cfg %v has invalid size categories, rebuild and restart", req.GetId()))
	}
	resp := &frontend.LoadDiscriminatorConfigResponse{
		Config: conf,
	}
	return resp, nil
}

func (s *AlmanacConfigService) SaveDiscriminatorConfig(ctx context.Context, req *frontend.SaveDiscriminatorConfigRequest) (*frontend.SaveDiscriminatorConfigResponse, error) {
	exists := false
	var err error
	cfg := req.GetConfig()
	if !s.validateDiscriminatorCfg(cfg) {
		return nil, status.Error(codes.FailedPrecondition, "Cfg has invalid size categories")
	}
	if cfg.GetId() != "" {
		exists, err = s.redisClient.HExists(redis.DiscriminatorCfgs, cfg.GetId())
		if err != nil {
			logrus.Warnf("Failed to check if key (%v) already exists: %v", cfg.GetId(), err)
			return nil, err
		}
	}
	if !exists {
		cfg.Id = uuid.New().String()
	}
	err = s.discriminatorCfg.SaveDiscriminatorConfig(cfg, req.GetAssociateWithActiveCrop())
	if err != nil {
		return nil, err
	}
	resp := &frontend.SaveDiscriminatorConfigResponse{
		Id: cfg.Id,
	}
	return resp, nil
}
func (s *AlmanacConfigService) SetActiveDiscriminatorConfig(ctx context.Context, req *frontend.SetActiveDiscriminatorConfigRequest) (*frontend.Empty, error) {
	_, err := s.discriminatorCfg.SetActiveCfg(req.GetId(), req.GetCropId())
	if err != nil {
		logrus.Warnf("Failed to set %v as active cfg %v", req.GetId(), err)
		return nil, err
	}
	return &frontend.Empty{}, nil
}
func (s *AlmanacConfigService) DeleteDiscriminatorConfig(ctx context.Context, req *frontend.DeleteDiscriminatorConfigRequest) (*frontend.Empty, error) {
	err := s.discriminatorCfg.DeleteDiscriminatorConfig(req.GetId())
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}
func (s *AlmanacConfigService) GetNextDiscriminatorConfig(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextDiscriminatorConfigResponse, error) {
	ts := req.GetTimestampMs()
	success, curr, newTs := s.discriminatorCfg.GetNextActiveCfg(ctx, ts)
	if !success {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Discriminator cfg Retrieved")
	}
	data, err := s.redisClient.HGetAll(redis.DiscriminatorCfgs)
	if err != nil {
		logrus.Warnf("Failed to fetch discriminator cfgs from redis.")
		return nil, err
	}
	resp := &frontend.GetNextDiscriminatorConfigResponse{
		Ts:     &frontend.Timestamp{TimestampMs: newTs},
		Active: curr,
	}
	resp.Available = make(map[string]string)
	for id, dataStr := range data {
		conf := &almanac.DiscriminatorConfig{}
		perr := proto.Unmarshal([]byte(dataStr), conf)
		if perr != nil {
			logrus.Warnf("Failed to unmarshal %v: %v", id, perr)
			continue
		}
		resp.Available[id] = conf.GetName()
	}
	return resp, nil
}
func (s *AlmanacConfigService) GetNextModelinatorConfig(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextModelinatorConfigResponse, error) {
	ts := req.GetTimestampMs()
	success, _, newTs := s.modelinatorCfg.GetNextActiveCfg(ctx, ts)
	if !success {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Modelinator cfg Retrieved")
	}
	conf, err := s.modelinatorCfg.FetchActiveModelinatorConfig()
	if err != nil {
		return nil, err
	}
	resp := &frontend.GetNextModelinatorConfigResponse{
		Ts:     &frontend.Timestamp{TimestampMs: newTs},
		Config: conf,
	}
	return resp, nil
}

func (s *AlmanacConfigService) SaveModelinatorConfig(ctx context.Context, req *frontend.SaveModelinatorConfigRequest) (*frontend.Empty, error) {
	cfg := req.GetConfig()
	if !s.validateModelinatorCfg(cfg) {
		return nil, status.Error(codes.FailedPrecondition, "Cfg is invalid.")
	}
	err := s.modelinatorCfg.SaveModelinatorConfig(cfg)
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *AlmanacConfigService) FetchModelinatorConfig(ctx context.Context, req *frontend.FetchModelinatorConfigRequest) (*frontend.FetchModelinatorConfigResponse, error) {
	conf, err := s.modelinatorCfg.FetchModelinatorConfig(req.GetModelId(), req.GetCropId())
	if err != nil {
		return nil, err
	}
	resp := &frontend.FetchModelinatorConfigResponse{
		Config: conf,
	}
	return resp, nil
}
func (s *AlmanacConfigService) ResetModelinatorConfig(ctx context.Context, req *frontend.ResetModelinatorConfigRequest) (*frontend.Empty, error) {
	err := s.modelinatorCfg.ResetToDefault()
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *AlmanacConfigService) validateAlmanacCfg(conf *almanac.AlmanacConfig) bool {
	count := int(s.sizesCount.GetUIntValue())
	defaultCount := 0
	for _, typeCat := range conf.GetCategories() {
		if len(typeCat.GetFormulas()) != count || len(typeCat.GetSizes()) != (count-1) {
			return false
		}
		if typeCat.GetType().GetCategory() == DefaultName {
			defaultCount += 1
		}
	}
	return defaultCount == 1
}

func (s *AlmanacConfigService) validateDiscriminatorCfg(conf *almanac.DiscriminatorConfig) bool {
	count := int(s.sizesCount.GetUIntValue())
	for _, typeCat := range conf.GetCategories() {
		if len(typeCat.GetTrusts()) != count {
			return false
		}
	}
	return true
}
func (s *AlmanacConfigService) validateModelinatorCfg(conf *almanac.ModelinatorConfig) bool {
	count := int(s.sizesCount.GetUIntValue())
	for _, typeCat := range conf.GetCategories() {
		if len(typeCat.GetTrusts()) != count {
			return false
		}
	}
	return true
}

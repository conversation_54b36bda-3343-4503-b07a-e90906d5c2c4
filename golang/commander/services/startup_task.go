package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/startup_task"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type StartupTaskService struct {
	frontend.UnimplementedStartupTaskServiceServer
	stState    *state.OverallStartupTaskState
	enabledCFG *config.ConfigTree
}

func NewStartupTaskService(grpcServer *grpc.Server, stState *state.OverallStartupTaskState, commonConfigNode *config.ConfigTree) *StartupTaskService {
	service := &StartupTaskService{
		stState:    stState,
		enabledCFG: commonConfigNode.GetNode("feature_flags/startup_task_feature"),
	}
	frontend.RegisterStartupTaskServiceServer(grpcServer, service)
	return service
}

func (s *StartupTaskService) GetNextTasks(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextTasksResponse, error) {
	var ts int64 = req.GetTimestampMs()
	resp := &frontend.GetNextTasksResponse{}
	result := s.stState.ReadOnNext(ctx, ts, func() {
		crosshair_tasks := s.stState.GetCrosshairCalTasks()
		for index := range crosshair_tasks {
			task_state := startup_task.TaskState_COMPLETE
			if crosshair_tasks[index].CalRequired {
				task_state = startup_task.TaskState_QUEUED
			}
			resp.Tasks = append(resp.Tasks, &startup_task.Task{

				Id:          fmt.Sprintf("%s_%d_%d", state.CrosshairTaskPrefix, crosshair_tasks[index].RowNumber, crosshair_tasks[index].LaserId),
				Label:       fmt.Sprintf("Calibrate crosshair for Row %d Laser %d", crosshair_tasks[index].RowNumber, crosshair_tasks[index].LaserId),
				Description: "Adjust the crosshair position to match the location of the laser, such that the center of the crosshair is in the center of the laser effected zone.",
				State:       task_state,
				TaskDetails: &startup_task.Task_CrosshairCalTask{
					CrosshairCalTask: &startup_task.CrosshairCalibrationTask{
						RowNumber: crosshair_tasks[index].RowNumber,
						LaserId:   crosshair_tasks[index].LaserId,
					},
				},
			})
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.stState.GetTimestampMs(),
		}
	})

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Tasks Retrieved")
}

func (s *StartupTaskService) MarkTaskComplete(ctx context.Context, req *frontend.MarkTaskCompleteRequest) (*frontend.MarkTaskCompleteResponse, error) {
	logrus.Infof("Request to mark task '%v' complete", req.TaskId)
	changed := false
	s.stState.ConditionalWriteOnCurrent(func() bool {
		changed := s.stState.MarkTaskComplete(req.TaskId)
		return changed
	})
	if !changed {
		return nil, status.Errorf(codes.FailedPrecondition, "Cannot mark task %s as complete.", req.TaskId)
	}

	return &frontend.MarkTaskCompleteResponse{}, nil
}

package services

import (
	"context"
	"errors"
	"fmt"
	"math"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	pb "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/formatting"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type PowerService struct {
	frontend.UnimplementedPowerServiceServer
	hardware            *hardware_manager.HardwareManagerClient
	implement           *state.ImplementState
	hwStatusRangeConfig *config.ConfigTree
}

func NewPowerService(grpcServer *grpc.Server, hardware *hardware_manager.HardwareManagerClient, implement *state.ImplementState, hwStatusRangeConfig *config.ConfigTree) *PowerService {
	service := &PowerService{
		hardware:            hardware,
		implement:           implement,
		hwStatusRangeConfig: hwStatusRangeConfig,
	}
	frontend.RegisterPowerServiceServer(grpcServer, service)
	return service
}

func (power *PowerService) GetNextPowerStatus(ctx context.Context, req *frontend.PowerStatusRequest) (*frontend.PowerStatusResponse, error) {
	var supervisoryState state.SupervisoryState
	var safetyState state.SafetyState
	var wheelEncoderState state.WheelEncoderState

	response := &frontend.PowerStatusResponse{}
	power.implement.ReadOnNext(ctx,
		req.Ts.GetTimestampMs(),
		func() {
			supervisoryState = power.implement.SupervisoryState
			safetyState = power.implement.SafetyState
			wheelEncoderState = power.implement.WheelEncoderState
			response.Ts = &frontend.Timestamp{TimestampMs: power.implement.GetTimestampMs()}
		})

	devices := make([]*frontend.DeviceStatus, 0)

	// laser key status boolean
	var keyValue string
	var keyColor frontend.DeviceValueColor
	if safetyState.LaserKey {
		keyValue = "ARMED"
		keyColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		keyValue = "DISARMED"
		keyColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_KEY,
		Label:  "Laser Key",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  keyColor,
				Status: keyValue,
			},
		},
	})

	// remote interlock status boolean
	var interlockValue string
	var interlockColor frontend.DeviceValueColor
	if safetyState.Interlock {
		interlockValue = "ENGAGED"
		interlockColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		interlockValue = "DISENGAGED"
		interlockColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_INTERLOCK,
		Label:  "Remote Interlock",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  interlockColor,
				Status: interlockValue,
			},
		},
	})

	// lifted status boolean
	var liftedValue string
	var liftedColor frontend.DeviceValueColor
	if safetyState.Lifted {
		liftedValue = "LIFTED"
		liftedColor = frontend.DeviceValueColor_COLOR_RED
	} else {
		liftedValue = "LOWERED"
		liftedColor = frontend.DeviceValueColor_COLOR_GREEN
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_LIFTED,
		Label:  "Lift Sensor",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  liftedColor,
				Status: liftedValue,
			},
		},
	})

	// server temperature
	temperatureValue := supervisoryState.ServerCabinetTemp
	var temperatureColor frontend.DeviceValueColor
	if temperatureValue > 33 {
		temperatureColor = frontend.DeviceValueColor_COLOR_RED
	} else {
		temperatureColor = frontend.DeviceValueColor_COLOR_GREEN
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_SERVER_TEMPERATURE,
		Label:  "Server Temperature",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  temperatureColor,
				Status: fmt.Sprintf("%.1f°C", temperatureValue),
			},
		},
	})

	// server humidity
	humidityValue := supervisoryState.ServerCabinetHumidity
	var humidityColor frontend.DeviceValueColor
	if humidityValue > 60 {
		humidityColor = frontend.DeviceValueColor_COLOR_RED
	} else {
		humidityColor = frontend.DeviceValueColor_COLOR_GREEN
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_SERVER_HUMIDITY,
		Label:  "Server Humidity",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  humidityColor,
				Status: fmt.Sprintf("%.1f%%", humidityValue),
			},
		},
	})

	// water detection
	var waterValue string
	var waterColor frontend.DeviceValueColor
	if power.implement.SafetyState.WaterProtect {
		waterValue = "OK"
		waterColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		waterValue = "WARNING"
		waterColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_WATER,
		Label:  "Water Sensor",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  waterColor,
				Status: waterValue,
			},
		},
	})

	// 12V battery voltage in volts
	batteryValue := supervisoryState.BatteryVoltage_12V

	var batteryColor frontend.DeviceValueColor
	if batteryValue < 11.6 {
		batteryColor = frontend.DeviceValueColor_COLOR_RED
	} else {
		batteryColor = frontend.DeviceValueColor_COLOR_GREEN
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_12V_BATTERY,
		Label:  "Backup Battery",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  batteryColor,
				Status: fmt.Sprintf("%.1fV", batteryValue),
			},
		},
	})

	// power quality string
	powerKW := float64(supervisoryState.PhasePowerW_3) / 1000
	var powerColor frontend.DeviceValueColor
	if supervisoryState.PowerGood {
		powerColor = frontend.DeviceValueColor_COLOR_GREEN
	} else if supervisoryState.PowerBad {
		powerColor = frontend.DeviceValueColor_COLOR_RED
	} else {
		powerColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_POWER_QUALITY,
		Label:  "Power",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  powerColor,
				Status: fmt.Sprintf("%.1fkW", powerKW),
			},
		},
	})

	// tractor connection
	var tractorValue string
	var tractorColor frontend.DeviceValueColor
	if supervisoryState.TractorPower {
		tractorValue = "CONNECTED"
		tractorColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		tractorValue = "DISCONNECTED"
		tractorColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_TRACTOR,
		Label:  "Tractor",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  tractorColor,
				Status: tractorValue,
			},
		},
	})

	// AC Freqency
	acValue := supervisoryState.AcFrequency
	var avColor frontend.DeviceValueColor
	if acValue > 56 && acValue < 64 {
		avColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		avColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_AC_FREQUENCY,
		Label:  "AC Frequency",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  avColor,
				Status: fmt.Sprintf("%.1fHz", acValue),
			},
		},
	})

	// A-B voltage
	abVoltValue := supervisoryState.AcVoltageAB
	var abVoltColor frontend.DeviceValueColor
	if abVoltValue > 205 && abVoltValue < 243 {
		abVoltColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		abVoltColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_AB_VOLTAGE,
		Label:  "A-B Voltage",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  abVoltColor,
				Status: fmt.Sprintf("%.0fV", abVoltValue),
			},
		},
	})

	// B-C voltage
	bcVoltValue := supervisoryState.AcVoltageBC
	var bcVoltColor frontend.DeviceValueColor
	if bcVoltValue > 205 && bcVoltValue < 243 {
		bcVoltColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		bcVoltColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_BC_VOLTAGE,
		Label:  "B-C Voltage",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  bcVoltColor,
				Status: fmt.Sprintf("%.0fV", bcVoltValue),
			},
		},
	})

	// A-C voltage
	acVoltValue := supervisoryState.AcVoltageBC
	var acVoltColor frontend.DeviceValueColor
	if acVoltValue > 205 && acVoltValue < 243 {
		acVoltColor = frontend.DeviceValueColor_COLOR_GREEN
	} else {
		acVoltColor = frontend.DeviceValueColor_COLOR_RED
	}
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_AC_VOLTAGE,
		Label:  "A-C Voltage",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  acVoltColor,
				Status: fmt.Sprintf("%.0fV", acVoltValue),
			},
		},
	})

	// A current
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_A_CURRENT,
		Label:  "A Current",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: fmt.Sprintf("%.0fA", supervisoryState.AcVoltageA),
			},
		},
	})

	// B current
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_B_CURRENT,
		Label:  "B Current",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: fmt.Sprintf("%.0fA", supervisoryState.AcVoltageB),
			},
		},
	})

	// C current
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_C_CURRENT,
		Label:  "C Current",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: fmt.Sprintf("%.0fA", supervisoryState.AcVoltageC),
			},
		},
	})

	// suicide switch
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_SUICIDE,
		Label:  "Kill All Power",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: true,
			},
		},
	})

	// reboot
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_REBOOT,
		Label:  "Reboot",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: true,
			},
		},
	})

	// main contactor
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_MAIN,
		Label:  "Main Power",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.MainContactorDisabled,
			},
		},
	})

	// Row 1 Server
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ROW_1,
		Label:  "Row 1 Computer",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ServerDisabled[0],
			},
		},
	})

	// Row 2 Server
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ROW_2,
		Label:  "Row 2 Computer",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ServerDisabled[1],
			},
		},
	})

	// Row 3 Server
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ROW_3,
		Label:  "Row 3 Computer",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ServerDisabled[2],
			},
		},
	})

	// Row 1 Lights
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_LIGHTS_1,
		Label:  "Row 1 Lights",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.BtlDisabled[0],
			},
		},
	})

	// Row 2 Lights
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_LIGHTS_2,
		Label:  "Row 2 Lights",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.BtlDisabled[1],
			},
		},
	})

	// Row 3 Lights
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_LIGHTS_3,
		Label:  "Row 3 Lights",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.BtlDisabled[2],
			},
		},
	})

	// Row 1 Scanners
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_SCANNER_1,
		Label:  "Row 1 Targeting",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ScannersDisabled[0],
			},
		},
	})

	// Row 2 Scanners
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_SCANNER_2,
		Label:  "Row 2 Targeting",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ScannersDisabled[1],
			},
		},
	})

	// Row 3 Scanners
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_SCANNER_3,
		Label:  "Row 3 Targeting",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ScannersDisabled[2],
			},
		},
	})

	// Air Conditioner
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_AC,
		Label:  "Air Conditioning",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.AirConditionerDisabled,
			},
		},
	})

	// Chiller
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_CHILLER,
		Label:  "Chiller",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.ChillerDisabled,
			},
		},
	})

	// Strobe and Predict Cameras
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_STROBE,
		Label:  "Strobe and Predict Cameras",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.StrobeDisabled,
			},
		},
	})

	// Front left wheel encoder
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ENCODER_FRONT_LEFT,
		Label:  "Front Left",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: !wheelEncoderState.FrontLeftEnabled,
			},
		},
	})
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_ENCODER_FRONT_LEFT,
		Label:  "Front Left",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: formatting.NumberFormat(float64(wheelEncoderState.FrontLeftTicks), 0),
			},
		},
	})

	// Front right wheel encoder
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ENCODER_FRONT_RIGHT,
		Label:  "Front Right",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: !wheelEncoderState.FrontRightEnabled,
			},
		},
	})
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_ENCODER_FRONT_RIGHT,
		Label:  "Front Right",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: formatting.NumberFormat(float64(wheelEncoderState.FrontRightTicks), 0),
			},
		},
	})

	// Back left wheel encoder
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ENCODER_BACK_LEFT,
		Label:  "Back Left",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: !wheelEncoderState.BackLeftEnabled,
			},
		},
	})
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_ENCODER_BACK_LEFT,
		Label:  "Back Left",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: formatting.NumberFormat(float64(wheelEncoderState.BackLeftTicks), 0),
			},
		},
	})

	// Back right wheel encoder
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ENCODER_BACK_RIGHT,
		Label:  "Back Right",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: !wheelEncoderState.BackRightEnabled,
			},
		},
	})
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_SENSOR_ENCODER_BACK_RIGHT,
		Label:  "Back Right",
		Type: &frontend.DeviceStatus_SensorType{
			SensorType: &frontend.DeviceStatus_SensorStatus{
				Color:  frontend.DeviceValueColor_COLOR_GRAY,
				Status: formatting.NumberFormat(float64(wheelEncoderState.BackRightTicks), 0),
			},
		},
	})

	// Encoder Board
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_ENCODER_BOARD,
		Label:  "Encoder Board",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.WheelEncoderDisabled,
			},
		},
	})

	// GPS
	devices = append(devices, &frontend.DeviceStatus{
		Device: frontend.Device_RELAY_GPS,
		Label:  "GPS",
		Type: &frontend.DeviceStatus_RelayType{
			RelayType: &frontend.DeviceStatus_RelayStatus{
				Disabled: supervisoryState.GpsDisabled,
			},
		},
	})

	gps, err := power.hardware.GetGPSData(true)
	if err == nil {
		devices = append(devices, &frontend.DeviceStatus{
			Device: frontend.Device_SENSOR_LATITUDE,
			Label:  "Latitude",
			Type: &frontend.DeviceStatus_SensorType{
				SensorType: &frontend.DeviceStatus_SensorStatus{
					Color:  frontend.DeviceValueColor_COLOR_GRAY,
					Status: fmt.Sprintf("%.4f", gps.Lla.Lat),
				},
			},
		})

		devices = append(devices, &frontend.DeviceStatus{
			Device: frontend.Device_SENSOR_LONGITUDE,
			Label:  "Longitude",
			Type: &frontend.DeviceStatus_SensorType{
				SensorType: &frontend.DeviceStatus_SensorStatus{
					Color:  frontend.DeviceValueColor_COLOR_GRAY,
					Status: fmt.Sprintf("%.4f", gps.Lla.Lng),
				},
			},
		})
	}

	response.Devices = devices
	return response, nil
}

func (power *PowerService) TurnOffDevice(ctx context.Context, request *frontend.RelayRequest) (*frontend.RelayResponse, error) {
	switch request.Device {
	case frontend.Device_RELAY_SUICIDE:
		success, err := power.hardware.SuicideSwitch()
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_REBOOT:
		success, err := power.hardware.CommandComputerPowerCycle()
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_MAIN:
		success, err := power.hardware.SetMainContactorDisable(true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ROW_1:
		success, err := power.hardware.SetServerDisable(1, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ROW_2:
		success, err := power.hardware.SetServerDisable(2, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ROW_3:
		success, err := power.hardware.SetServerDisable(3, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_LIGHTS_1:
		success, err := power.hardware.SetBTLDisable(1, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_LIGHTS_2:
		success, err := power.hardware.SetBTLDisable(2, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_LIGHTS_3:
		success, err := power.hardware.SetBTLDisable(3, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_SCANNER_1:
		success, err := power.hardware.SetScannersDisable(1, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_SCANNER_2:
		success, err := power.hardware.SetScannersDisable(2, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_SCANNER_3:
		success, err := power.hardware.SetScannersDisable(3, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_AC:
		success, err := power.hardware.SetAirConditionerDisable(true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_CHILLER:
		success, err := power.hardware.SetChillerDisable(true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_STROBE:
		success, err := power.hardware.SetStrobeDisable(true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_BOARD:
		success, err := power.hardware.SetWheelEncoderBoardDisable(true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_FRONT_LEFT:
		success, err := power.hardware.SetWheelEncoderDisable(true, true, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_FRONT_RIGHT:
		success, err := power.hardware.SetWheelEncoderDisable(true, false, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_BACK_LEFT:
		success, err := power.hardware.SetWheelEncoderDisable(false, true, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_BACK_RIGHT:
		success, err := power.hardware.SetWheelEncoderDisable(false, false, true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_GPS:
		success, err := power.hardware.SetGPSDisable(true)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	default:
		return &frontend.RelayResponse{
			Success: false,
		}, errors.New(fmt.Sprintf("No device found matching %s", request.Device))
	}
}

func (power *PowerService) TurnOnDevice(ctx context.Context, request *frontend.RelayRequest) (*frontend.RelayResponse, error) {
	switch request.Device {
	case frontend.Device_RELAY_SUICIDE:
		return nil, errors.New(fmt.Sprintf("On operation not supported for %s", request.Device))
	case frontend.Device_RELAY_REBOOT:
		return nil, errors.New(fmt.Sprintf("On operation not supported for %s", request.Device))
	case frontend.Device_RELAY_MAIN:
		success, err := power.hardware.SetMainContactorDisable(false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ROW_1:
		success, err := power.hardware.SetServerDisable(1, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ROW_2:
		success, err := power.hardware.SetServerDisable(2, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ROW_3:
		success, err := power.hardware.SetServerDisable(3, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_LIGHTS_1:
		success, err := power.hardware.SetBTLDisable(1, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_LIGHTS_2:
		success, err := power.hardware.SetBTLDisable(2, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_LIGHTS_3:
		success, err := power.hardware.SetBTLDisable(3, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_SCANNER_1:
		success, err := power.hardware.SetScannersDisable(1, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_SCANNER_2:
		success, err := power.hardware.SetScannersDisable(2, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_SCANNER_3:
		success, err := power.hardware.SetScannersDisable(3, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_AC:
		success, err := power.hardware.SetAirConditionerDisable(false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_CHILLER:
		success, err := power.hardware.SetChillerDisable(false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_STROBE:
		success, err := power.hardware.SetStrobeDisable(false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_BOARD:
		success, err := power.hardware.SetWheelEncoderBoardDisable(false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_FRONT_LEFT:
		success, err := power.hardware.SetWheelEncoderDisable(true, true, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_FRONT_RIGHT:
		success, err := power.hardware.SetWheelEncoderDisable(true, false, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_BACK_LEFT:
		success, err := power.hardware.SetWheelEncoderDisable(false, true, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_ENCODER_BACK_RIGHT:
		success, err := power.hardware.SetWheelEncoderDisable(false, false, false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	case frontend.Device_RELAY_GPS:
		success, err := power.hardware.SetGPSDisable(false)
		return &frontend.RelayResponse{
			Success: success,
		}, err
	default:
		return &frontend.RelayResponse{
			Success: false,
		}, errors.New(fmt.Sprintf("No device found matching %s", request.Device))
	}
}

func (s *PowerService) valueVsExpected(value float64, path string) *frontend.ValueWithRange {
	// don't crash if node doesn't exist
	if !s.hwStatusRangeConfig.HasNode(path) {
		logrus.Warnf("No config node for value %s", path)
		return &frontend.ValueWithRange{
			Value: value,
			IsOk:  false,
		}
	}

	// otherwise check if in bounds
	node := s.hwStatusRangeConfig.GetNode(path)
	minNode, maxNode := node.GetChild("min"), node.GetChild("max")
	minValue, maxValue := minNode.GetFloatValue(), maxNode.GetFloatValue()

	return &frontend.ValueWithRange{
		Value: value,
		IsOk:  ((value >= minValue) && (value <= maxValue)),
	}
}

func convNetworkPortState(state *pb.NetworkPortState) *frontend.NetworkPortState {
	return &frontend.NetworkPortState{
		LinkUp:            state.LinkUp,
		ActualLinkSpeed:   frontend.NetworkLinkSpeed(state.ActualLinkSpeed),
		ExpectedLinkSpeed: frontend.NetworkLinkSpeed(state.ExpectedLinkSpeed),
	}
}

func convNetworkPortStateListIdx(states []*pb.NetworkPortState, idx int) *frontend.NetworkPortState {
	if idx < 0 || idx >= len(states) {
		return nil
	}
	return convNetworkPortState(states[idx])
}

func (s *PowerService) convScannerMotorData(state *pb.ReaperScannerMotorData) *frontend.ReaperScannerMotorData {
	if state == nil {
		return nil
	}

	return &frontend.ReaperScannerMotorData{
		ControllerSn:       state.ControllerSn,
		TemperatureOutputC: s.valueVsExpected(state.TemperatureOutputC, "module/scanner/motor/output_temp"),
		MotorSupplyV:       s.valueVsExpected(state.MotorSupplyV, "module/scanner/motor/supply"),
		MotorCurrentA:      s.valueVsExpected(state.MotorCurrentA, "module/scanner/motor/current"),
		EncoderPosition:    state.EncoderPosition,
	}
}

func (s *PowerService) convScannerLaserStatus(state *pb.ReaperScannerLaserStatus) *frontend.ReaperScannerLaserStatus {
	if state == nil {
		return nil
	}

	laserCurrentVal := s.valueVsExpected(state.LaserCurrentMa, "module/scanner/laser/current")
	if laserCurrentVal.Value == 0 {
		laserCurrentVal.IsOk = true
	}

	return &frontend.ReaperScannerLaserStatus{
		Model:          state.Model,
		Sn:             state.Sn,
		RatedPower:     state.RatedPower,
		TemperatureC:   s.valueVsExpected(state.TemperatureC, "module/scanner/laser/temp"),
		Humidity:       s.valueVsExpected(state.Humidity, "module/scanner/laser/humidity"),
		LaserCurrentMa: laserCurrentVal,
		// TODO: convert the faults list
		Faults: nil,
	}
}

func (s *PowerService) convScannerSensorData(state *pb.ReaperScannerSensorData) *frontend.ReaperScannerSensorData {
	if state == nil {
		return nil
	}

	return &frontend.ReaperScannerSensorData{
		ScannerSn:              state.ScannerSn,
		CurrentA:               s.valueVsExpected(state.CurrentA, "module/scanner/power_current"),
		FuseTripped:            state.FuseTripped,
		TemperatureCollimatorC: s.valueVsExpected(state.TemperatureCollimatorC, "module/scanner/temp_collimator"),
		TemperatureFiberC:      s.valueVsExpected(state.TemperatureFiberC, "module/scanner/temp_fiber"),
		LaserPowerW:            s.valueVsExpected(state.LaserPowerW, "module/scanner/laser_power_w"),
		LaserConnected:         state.LaserConnected,
		LaserStatus:            s.convScannerLaserStatus(state.LaserStatus),
		TargetConnected:        state.TargetConnected,
		TargetSn:               state.TargetSn,
		MotorPan:               s.convScannerMotorData(state.MotorPan),
		MotorTilt:              s.convScannerMotorData(state.MotorTilt),
		ScannerPowerEnabled:    state.PowerOn,
		TargetCamPowerEnabled:  state.TargetCamPowerEnabled,
	}

}

func (s *PowerService) getNextReaperCenterEnclosureData() (*frontend.ReaperCenterEnclosureData, error) {
	// chiller temp "ok" status by checking actual temp is ±1.5°C of set temp
	chiller_diff := math.Abs(s.implement.SupervisoryState.ChillerSetTemp - s.implement.SupervisoryState.ChillerTemp)
	chiller_ok := (chiller_diff < 1.5)

	resp := &frontend.ReaperCenterEnclosureData{
		WaterProtectStatus:    s.implement.SupervisoryState.WaterProtect,
		MainContactorStatusFb: s.implement.SupervisoryState.MainContactorStatusFb,
		PowerStatus: func() frontend.AcPowerStatus {
			if s.implement.SupervisoryState.PowerVeryBad {
				return frontend.AcPowerStatus_POWER_VERY_BAD
			} else if s.implement.SupervisoryState.PowerBad {
				return frontend.AcPowerStatus_POWER_BAD
			} else if s.implement.SupervisoryState.PowerGood {
				return frontend.AcPowerStatus_POWER_GOOD
			}
			return frontend.AcPowerStatus_POWER_UNKNOWN
		}(),
		LiftedStatus: s.implement.SupervisoryState.LiftedStatus,
		TractorPower: s.implement.SupervisoryState.TractorPower,

		AcFrequency:           s.valueVsExpected(s.implement.SupervisoryState.AcFrequency, "power/frequency"),
		AcVoltageAB:           s.valueVsExpected(s.implement.SupervisoryState.AcVoltageAB, "power/phase_v"),
		AcVoltageBC:           s.valueVsExpected(s.implement.SupervisoryState.AcVoltageBC, "power/phase_v"),
		AcVoltageAC:           s.valueVsExpected(s.implement.SupervisoryState.AcVoltageAC, "power/phase_v"),
		AcVoltageA:            s.valueVsExpected(s.implement.SupervisoryState.AcVoltageA, "power/current"),
		AcVoltageB:            s.valueVsExpected(s.implement.SupervisoryState.AcVoltageB, "power/current"),
		AcVoltageC:            s.valueVsExpected(s.implement.SupervisoryState.AcVoltageC, "power/current"),
		PhasePowerW_3:         s.implement.SupervisoryState.PhasePowerW_3,
		PhasePowerVa_3:        s.implement.SupervisoryState.PhasePowerVa_3,
		PowerFactor:           s.valueVsExpected(s.implement.SupervisoryState.PowerFactor, "power/power_factor"),
		ServerCabinetTemp:     s.valueVsExpected(s.implement.SupervisoryState.ServerCabinetTemp, "enclosure/temp"),
		ServerCabinetHumidity: s.valueVsExpected(s.implement.SupervisoryState.ServerCabinetHumidity, "enclosure/humidity"),
		BatteryVoltage_12V:    s.valueVsExpected(s.implement.SupervisoryState.BatteryVoltage_12V, "power/battery"),

		WheelEncoderDisabled:   s.implement.SupervisoryState.WheelEncoderDisabled,
		StrobeDisabled:         s.implement.SupervisoryState.StrobeDisabled,
		GpsDisabled:            s.implement.SupervisoryState.GpsDisabled,
		MainContactorDisabled:  s.implement.SupervisoryState.MainContactorDisabled,
		AirConditionerDisabled: s.implement.SupervisoryState.AirConditionerDisabled,
		ChillerDisabled:        s.implement.SupervisoryState.ChillerDisabled,

		ChillerAlarms: s.implement.SupervisoryState.ChillerAlarms.ToStrings(),
		ChillerTempC: &frontend.ValueWithRange{
			Value: s.implement.SupervisoryState.ChillerTemp,
			IsOk:  chiller_ok,
		},
		ChillerFlowLMin:         s.valueVsExpected(s.implement.SupervisoryState.ChillerFlow, "chiller/flow"),
		ChillerPressurePsi:      s.valueVsExpected(s.implement.SupervisoryState.ChillerPressure, "chiller/pressure"),
		ChillerConductivityUsCm: s.valueVsExpected(s.implement.SupervisoryState.ChillerConductivity, "chiller/conductivity"),
		ChillerSetTempC:         s.valueVsExpected(s.implement.SupervisoryState.ChillerSetTemp, "chiller/set_temp"),
		ChillerHeatTransferKbtu: s.valueVsExpected(s.implement.SupervisoryState.ChillerHeatTransfer, "chiller/heat_transfer"),
		ChillerFluidDeltaTempC:  s.valueVsExpected(s.implement.SupervisoryState.ChillerFluidDeltaTemp, "chiller/delta_temp"),
	}

	// fetch wheel encoder state and position data
	resp.WheelEncoder = &frontend.ReaperWheelEncoderData{
		FrontLeft:  s.implement.WheelEncoderState.FrontLeftTicks,
		FrontRight: s.implement.WheelEncoderState.FrontRightTicks,
	}

	gps, err := s.hardware.GetGPSData(true)
	if err == nil {
		resp.Gps = &frontend.ReaperGpsData{
			// TODO: there's no such flag provided via the GPS data API, but if the position solution
			// is invalid the GPS module reports a position of (0, 0) so check for that
			HasFix:    math.Abs(gps.Lla.Lat) > 0.01 && math.Abs(gps.Lla.Lng) > 0.01,
			Latitude:  float32(gps.Lla.Lat),
			Longitude: float32(gps.Lla.Lng),
		}
	} else {
		logrus.Warnf("Failed to query GPS data: %v", err)
	}

	return resp, nil
}

func (s *PowerService) getNextReaperModuleSensorData(state *pb.ReaperModuleSensorData) (*frontend.ReaperModuleSensorData, error) {
	if state == nil {
		return nil, nil
	}
	resp := &frontend.ReaperModuleSensorData{
		ModuleId: state.GetModuleId(),
		ModuleSn: state.GetModuleSn(),
		EnviroEnclosure: &frontend.EnvironmentalSensorData{
			TemperatureC: s.valueVsExpected(state.EnviroEnclosure.TemperatureC, "module/enviro/enclosure_temp"),
			HumidityRh:   s.valueVsExpected(state.EnviroEnclosure.HumidityRh, "module/enviro/enclosure_humidity"),
			PressureHpa:  s.valueVsExpected(state.EnviroEnclosure.PressureHpa, "module/enviro/enclosure_pressure"),
		},
		EnviroPc: &frontend.EnvironmentalSensorData{
			TemperatureC: s.valueVsExpected(state.EnviroPc.TemperatureC, "module/enviro/pc_temp"),
			HumidityRh:   s.valueVsExpected(state.EnviroPc.HumidityRh, "module/enviro/pc_humidity"),
			PressureHpa:  s.valueVsExpected(state.EnviroPc.PressureHpa, "module/enviro/pc_pressure"),
		},
		CoolantInlet: &frontend.CoolantSensorData{
			TemperatureC: s.valueVsExpected(state.CoolantInlet.TemperatureC, "module/coolant/inlet_temp"),
			PressureKpa:  s.valueVsExpected(state.CoolantInlet.PressureKpa, "module/coolant/inlet_pressure"),
		},
		CoolantOutlet: &frontend.CoolantSensorData{
			TemperatureC: s.valueVsExpected(state.CoolantOutlet.TemperatureC, "module/coolant/outlet_temp"),
			PressureKpa:  s.valueVsExpected(state.CoolantOutlet.PressureKpa, "module/coolant/outlet_pressure"),
		},
		StrobeTemperatureC: s.valueVsExpected(state.StrobeTemperatureC, "module/strobe/temp"),
		StrobeCapVoltage:   s.valueVsExpected(state.StrobeCapVoltage, "module/strobe/voltage"),
		StrobeCurrent:      s.valueVsExpected(state.StrobeCurrent, "module/strobe/current"),
		Pc: func() *frontend.ReaperPcSensorData {
			if state.Pc == nil {
				return nil
			}

			gpuTemp1, gpuTemp2 := 0.0, 0.0

			if state.Pc.TemperatureGpu_1C != nil {
				gpuTemp1 = *state.Pc.TemperatureGpu_1C
			}
			if state.Pc.TemperatureGpu_2C != nil {
				gpuTemp2 = *state.Pc.TemperatureGpu_2C
			}

			return &frontend.ReaperPcSensorData{
				TemperatureCpuCoreC: s.valueVsExpected(state.Pc.TemperatureCpuCoreC, "module/pc/cpu_core_temp"),
				TemperatureSystemC:  s.valueVsExpected(state.Pc.TemperatureSystemC, "module/pc/system_temp"),
				TemperatureGpu_1C:   s.valueVsExpected(gpuTemp1, "module/pc/gpu_temp"),
				TemperatureGpu_2C:   s.valueVsExpected(gpuTemp2, "module/pc/gpu_temp"),
				Psu_12V:             s.valueVsExpected(state.Pc.Psu_12V, "module/pc/psu_12v"),
				Psu_5V:              s.valueVsExpected(state.Pc.Psu_5V, "module/pc/psu_5v"),
				Psu_3V3:             s.valueVsExpected(state.Pc.Psu_3V3, "module/pc/psu_3v3"),
				Load:                s.valueVsExpected(state.Pc.Load, "module/pc/loadavg"),
				Uptime:              state.Pc.Uptime,
				RamUsagePercent:     s.valueVsExpected(state.Pc.RamUsagePercent, "module/pc/ram_usage"),
				DiskUsagePercent:    s.valueVsExpected(state.Pc.DiskUsagePercent, "module/pc/disk_usage"),
				ScannerALink:        convNetworkPortStateListIdx(state.Pc.ScannerLink, 0),
				ScannerBLink:        convNetworkPortStateListIdx(state.Pc.ScannerLink, 1),
				TargetCamALink:      convNetworkPortStateListIdx(state.Pc.TargetCamLink, 0),
				TargetCamBLink:      convNetworkPortStateListIdx(state.Pc.TargetCamLink, 1),
				PredictCamLink:      convNetworkPortState(state.Pc.PredictCamLink),
				IpmiLink:            convNetworkPortState(state.Pc.IpmiLink),
				ExtLink:             convNetworkPortState(state.Pc.ExtLink),
			}
		}(),
		ScannerA:               s.convScannerSensorData(state.ScannerA),
		ScannerB:               s.convScannerSensorData(state.ScannerB),
		PcPowerEnabled:         state.PcPowerEnabled,
		LasersPowerEnabled:     state.LasersPowerEnabled,
		PredictCamPowerEnabled: state.PredictCamPowerEnabled,
		StrobePowerEnabled:     state.StrobePowerEnabled,
		StrobeEnabled:          state.StrobeEnabled,
	}
	if cameraTemps, ok := s.implement.CameraTemperatures[uint32(state.GetModuleId())]; ok {
		// todo, waiting on predict cam temp in protos
		resp.ScannerA.TemperatureTargetC = s.valueVsExpected(cameraTemps.TargetA, "module/scanner/target_cam_temp")
		resp.ScannerB.TemperatureTargetC = s.valueVsExpected(cameraTemps.TargetB, "module/scanner/target_cam_temp")
	}
	return resp, nil
}

func (s *PowerService) GetNextReaperHardwareStatus(ctx context.Context, req *frontend.GetNextReaperHardwareStatusRequest) (*frontend.GetNextReaperHardwareStatusResponse, error) {
	resp := &frontend.GetNextReaperHardwareStatusResponse{}

	var err error
	result := s.implement.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		switch req.GetRequest().(type) {
		case *frontend.GetNextReaperHardwareStatusRequest_CenterEnclosureStatus:
			centerEnclosureStatus, statusErr := s.getNextReaperCenterEnclosureData()
			if statusErr != nil {
				err = statusErr
				return
			}
			resp.Response = &frontend.GetNextReaperHardwareStatusResponse_CenterEnclosureStatus{
				CenterEnclosureStatus: centerEnclosureStatus,
			}
		case *frontend.GetNextReaperHardwareStatusRequest_ModuleStatus:
			moduleId := int(req.GetModuleStatus().GetModuleId())
			idx := moduleId - 1
			if idx < 0 || idx >= len(s.implement.ReaperModuleStates) {
				err = errors.New("invalid module id")
				return
			}
			state := s.implement.ReaperModuleStates[idx]
			moduleSensorStatus, statusErr := s.getNextReaperModuleSensorData(state)
			if statusErr != nil {
				err = statusErr
				return
			}
			resp.Response = &frontend.GetNextReaperHardwareStatusResponse_ModuleStatus{
				ModuleStatus: moduleSensorStatus,
			}
		default:
			err = errors.New("invalid request")
			return
		}
	})

	if !result {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Status Retrieved")
	} else if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *PowerService) GetNextReaperAllHardwareStatus(ctx context.Context, req *frontend.GetNextReaperAllHardwareStatusRequest) (*frontend.GetNextReaperAllHardwareStatusResponse, error) {
	resp := &frontend.GetNextReaperAllHardwareStatusResponse{}
	var err error
	result := s.implement.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		centerEnclosureStatus, statusErr := s.getNextReaperCenterEnclosureData()
		if statusErr != nil {
			err = statusErr
			return
		}
		resp.CenterEnclosureStatus = centerEnclosureStatus
		resp.ModuleStatus = make([]*frontend.ReaperModuleSensorData, len(s.implement.ReaperModuleStates))
		for idx, state := range s.implement.ReaperModuleStates {
			moduleSensorStatus, statusErr := s.getNextReaperModuleSensorData(state)
			if statusErr != nil {
				err = statusErr
				return
			}
			resp.ModuleStatus[idx] = moduleSensorStatus
		}
	})

	if !result {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Status Retrieved")
	} else if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *PowerService) SetReaperScannerPower(ctx context.Context, req *frontend.SetReaperScannerPowerRequest) (*frontend.SetReaperScannerPowerResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Power Set")
	}

	success, err := s.hardware.SetReaperScannerPower(req.GetModuleId(), req.ScannerAPower, req.ScannerBPower)
	if err != nil {
		return nil, err
	}
	return &frontend.SetReaperScannerPowerResponse{Success: success}, nil
}

func (s *PowerService) SetReaperTargetPower(ctx context.Context, req *frontend.SetReaperTargetPowerRequest) (*frontend.SetReaperTargetPowerResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Power Set")
	}

	success, err := s.hardware.SetReaperTargetPower(req.GetModuleId(), req.TargetAPower, req.TargetBPower)
	if err != nil {
		return nil, err
	}

	return &frontend.SetReaperTargetPowerResponse{Success: success}, nil
}

func (s *PowerService) SetReaperPredictCamPower(ctx context.Context, req *frontend.SetReaperPredictCamPowerRequest) (*frontend.SetReaperPredictCamPowerResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Power Set")
	}

	success, err := s.hardware.SetReaperPredictCamPower(req.ModuleId, req.Enabled)
	if err != nil {
		return nil, err
	}

	return &frontend.SetReaperPredictCamPowerResponse{Success: success}, nil
}

func (s *PowerService) SetReaperStrobeEnable(ctx context.Context, req *frontend.SetReaperStrobeEnableRequest) (*frontend.SetReaperStrobeEnableResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Power Set")
	}

	success, err := s.hardware.SetReaperStrobeEnable([]uint32{req.ModuleId}, req.Enabled, nil)
	if err != nil {
		return nil, err
	}

	return &frontend.SetReaperStrobeEnableResponse{Success: success}, nil
}

func (s *PowerService) SetReaperAllStrobesEnable(ctx context.Context, req *frontend.SetReaperAllStrobesEnableRequest) (*frontend.SetReaperAllStrobesEnableResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before strobe state set")
	}

	success, err := s.hardware.SetReaperStrobeEnable(nil, req.Enabled, nil)
	if err != nil {
		return nil, err
	}

	return &frontend.SetReaperAllStrobesEnableResponse{Success: success}, nil
}

func (s *PowerService) SetReaperModulePcPower(ctx context.Context, req *frontend.SetReaperModulePcPowerRequest) (*frontend.SetReaperModulePcPowerResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Power Set")
	}

	success, err := s.hardware.SetReaperModulePcPower(req.ModuleId, req.Enabled)
	if err != nil {
		return nil, err
	}

	return &frontend.SetReaperModulePcPowerResponse{Success: success}, nil
}

func (s *PowerService) SetReaperModuleLaserPower(ctx context.Context, req *frontend.SetReaperModuleLaserPowerRequest) (*frontend.SetReaperModuleLaserPowerResponse, error) {
	if ctx.Err() != nil {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Power Set")
	}

	success, err := s.hardware.SetReaperModuleLaserPower(req.ModuleId, req.Enabled)
	if err != nil {
		return nil, err
	}

	return &frontend.SetReaperModuleLaserPowerResponse{Success: success}, nil
}

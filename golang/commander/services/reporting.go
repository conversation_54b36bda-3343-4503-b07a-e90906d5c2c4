package services

import (
	"context"
	"sort"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ReportingService struct {
	frontend.UnimplementedReportingServiceServer
	reporting *state.ReportingState
}

func NewReportingService(grpcServer *grpc.Server, reporting *state.ReportingState) *ReportingService {
	service := &ReportingService{
		reporting: reporting,
	}
	frontend.RegisterReportingServiceServer(grpcServer, service)
	return service
}

func (r *ReportingService) GetNextLocationHistory(ctx context.Context, req *frontend.Timestamp) (*frontend.LocationHistory, error) {
	var ts int64 = req.GetTimestampMs()
	response := &frontend.LocationHistory{}
	result := r.reporting.ReadOnNext(ctx, ts, func() {
		response.Ts = &frontend.Timestamp{TimestampMs: r.reporting.GetTimestampMs()}

		// convert map to slice
		response.History = []*frontend.Location{}
		for _, point := range r.reporting.LocationHistory {
			response.History = append(response.History, point)
		}

		// sort slice by time
		sort.Slice(response.History, func(i, j int) bool {
			return response.History[i].Ts.TimestampMs < response.History[j].Ts.TimestampMs
		})
	})

	if result {
		return response, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Status Retrieved")
}

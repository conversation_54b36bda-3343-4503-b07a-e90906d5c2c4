package services

import (
	"context"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"

	"github.com/carbonrobotics/robot/golang/commander/state/mocks"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
)

func TestNewAlarmService(t *testing.T) {
	saveFrontendRegisterAlarmServiceServer := frontendRegisterAlarmServiceServer
	defer func() { frontendRegisterAlarmServiceServer = saveFrontendRegisterAlarmServiceServer }()

	grpcServer := &grpc.Server{}
	frontendRegisterAlarmServiceServer = func(s grpc.ServiceRegistrar, srv frontend.AlarmServiceServer) {
		assert.Equal(t, grpcServer, s)
	}

	alarmState := &state.AlarmState{}
	alarmmanager := &state.AlarmManager{}
	svc := NewAlarmService(grpcServer, alarmState, alarmmanager, nil)
	assert.Equal(t, alarmState, svc.alarmState)
	assert.Equal(t, alarmmanager, svc.alarmManager)
}

func newTestAlarmRow(acode string, level frontend.AlarmLevel, impact frontend.AlarmImpact) *frontend.AlarmRow {
	return &frontend.AlarmRow{
		AlarmCode: acode,
		Level:     level,
		Impact:    impact,
	}
}

func TestSortAlarms(t *testing.T) {
	arTest12 := newTestAlarmRow("test", 1, 2)
	arTest52 := newTestAlarmRow("test", 5, 2)
	arTest72 := newTestAlarmRow("test", 7, 2)
	arAaa12 := newTestAlarmRow("aaa", 1, 2)
	arXxx12 := newTestAlarmRow("xxx", 1, 2)
	arZzz12 := newTestAlarmRow("zzz", 1, 2)
	arTest10 := newTestAlarmRow("test", 1, 0)
	arTest150 := newTestAlarmRow("test", 1, 50)
	arTest1100 := newTestAlarmRow("test", 1, 100)

	tests := []struct {
		name          string
		alarmsInOrder []*frontend.AlarmRow
	}{
		{
			"already ordered",
			[]*frontend.AlarmRow{arTest12, arTest12, arXxx12},
		},
		{
			"level sorts",
			[]*frontend.AlarmRow{arTest72, arTest12, arTest52},
		},
		{
			"code sorts",
			[]*frontend.AlarmRow{arZzz12, arAaa12, arXxx12},
		},
		{
			"impact sorts",
			[]*frontend.AlarmRow{arTest1100, arTest150, arTest10},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			alarmsClone := make([]*frontend.AlarmRow, 0, len(test.alarmsInOrder))
			for _, a := range test.alarmsInOrder {
				alarmsClone = append(alarmsClone, a)
			}
			rand.Shuffle(len(alarmsClone), func(i, j int) { alarmsClone[i], alarmsClone[j] = alarmsClone[j], alarmsClone[i] })
			SortAlarms(alarmsClone)
			for i := 0; i < len(alarmsClone); i++ {
				assertSameAlarmRow(t, test.alarmsInOrder[i], alarmsClone[i])
			}
		})
	}
}

func assertSameAlarmRow(t *testing.T, a, b *frontend.AlarmRow) bool {
	t.Helper()
	return a.AlarmCode == b.AlarmCode &&
		a.Acknowledged == b.Acknowledged &&
		a.Identifier == b.Identifier &&
		a.Impact == b.Impact &&
		a.TimestampMs == b.TimestampMs &&
		a.Level == b.Level &&
		a.Description == b.Description &&
		a.Subsystem == b.Subsystem
}

func createAlarmsMap(alarmType state.AlarmType, alrms ...*alarms.Alarm) map[state.AlarmType]map[string]*alarms.Alarm {
	alarmsMap := make(map[state.AlarmType]map[string]*alarms.Alarm)
	am := make(map[string]*alarms.Alarm)
	for _, aa := range alrms {
		am[aa.Identifier] = aa
	}
	alarmsMap[alarmType] = am
	return alarmsMap
}

var testValidAlarm = &alarms.Alarm{
	Identifier: "test001",
	AlarmCode: alarms.AlarmCode{
		Subsystem: "test",
		Number:    7,
	},
	Valid: true,
}
var testInvalidAlarm = &alarms.Alarm{
	Identifier: "test002",
	AlarmCode: alarms.AlarmCode{
		Subsystem: "test",
		Number:    7,
	},
	Valid: false,
}
var testHiddenAlarm = &alarms.Alarm{
	Identifier: "test003",
	AlarmCode: alarms.AlarmCode{
		Subsystem: "test",
		Number:    7,
	},
	Level: alarms.AlarmLevelHidden,
	Valid: true,
}
var testAcknowledgedAlarm = &alarms.Alarm{
	Identifier: "test004",
	AlarmCode: alarms.AlarmCode{
		Subsystem: "test",
		Number:    7,
	},
	Valid:        true,
	Acknowledged: true,
}

func Test_alarmListFn(t *testing.T) {
	testTime := time.Now().UnixMilli()
	saveAlarmsDefault := alarmsDefault
	defer func() {
		alarmsDefault = saveAlarmsDefault
	}()
	tests := []struct {
		name               string
		alarms             []*alarms.Alarm
		expectedAlarmCount int
	}{
		{
			name:               "success",
			alarms:             []*alarms.Alarm{testValidAlarm},
			expectedAlarmCount: 1,
		},
		{
			name:               "invalid alarm",
			alarms:             []*alarms.Alarm{testInvalidAlarm},
			expectedAlarmCount: 0,
		},
		{
			name:               "hidden alarm",
			alarms:             []*alarms.Alarm{testHiddenAlarm},
			expectedAlarmCount: 0,
		},
		{
			name: "all together",
			alarms: []*alarms.Alarm{
				testValidAlarm,
				testInvalidAlarm,
				testHiddenAlarm,
			},
			expectedAlarmCount: 1,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockAlarmState := new(mocks.ManagedState)
			mockAlarmState.On("GetTimestampMs").
				Return(testTime)

			alarmsDefault = createAlarmsMap(state.AlarmTypeSoftware, test.alarms...)
			gotResp := new(frontend.AlarmTable)
			svc := &AlarmService{alarmState: mockAlarmState}
			svc.alarmListFn(gotResp)()
			assert.Len(t, gotResp.Alarms, test.expectedAlarmCount)
			assert.Equal(t, testTime, gotResp.Ts.TimestampMs)
		})
	}
}

func Test_newAlarmListFn(t *testing.T) {
	testTime := time.Now().UnixMilli()
	saveAlarmsDefault := alarmsDefault
	defer func() {
		alarmsDefault = saveAlarmsDefault
	}()
	tests := []struct {
		name               string
		alarms             []*alarms.Alarm
		expectedAlarmCount int
	}{
		{
			name:               "success",
			alarms:             []*alarms.Alarm{testValidAlarm},
			expectedAlarmCount: 1,
		},
		{
			name:               "invalid alarm",
			alarms:             []*alarms.Alarm{testInvalidAlarm},
			expectedAlarmCount: 0,
		},
		{
			name:               "hidden alarm",
			alarms:             []*alarms.Alarm{testHiddenAlarm},
			expectedAlarmCount: 0,
		},
		{
			name:               "acknowledged alarm",
			alarms:             []*alarms.Alarm{testAcknowledgedAlarm},
			expectedAlarmCount: 0,
		},
		{
			name: "all together",
			alarms: []*alarms.Alarm{
				testValidAlarm,
				testInvalidAlarm,
				testHiddenAlarm,
				testAcknowledgedAlarm,
			},
			expectedAlarmCount: 1,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockAlarmState := new(mocks.ManagedState)
			mockAlarmState.On("GetTimestampMs").
				Return(testTime)

			alarmsDefault = createAlarmsMap(state.AlarmTypeSoftware, test.alarms...)
			gotResp := new(frontend.AlarmTable)
			svc := &AlarmService{alarmState: mockAlarmState}
			svc.newAlarmListFn(gotResp)()
			assert.Len(t, gotResp.Alarms, test.expectedAlarmCount)
			assert.Equal(t, testTime, gotResp.Ts.TimestampMs)
		})
	}
}

func Test_alarmCountFn(t *testing.T) {
	testTime := time.Now().UnixMilli()
	saveAlarmsDefault := alarmsDefault
	defer func() {
		alarmsDefault = saveAlarmsDefault
	}()
	tests := []struct {
		name               string
		alarms             []*alarms.Alarm
		expectedAlarmCount uint32
	}{
		{
			name:               "success",
			alarms:             []*alarms.Alarm{testValidAlarm},
			expectedAlarmCount: 1,
		},
		{
			name:               "invalid alarm",
			alarms:             []*alarms.Alarm{testInvalidAlarm},
			expectedAlarmCount: 0,
		},
		{
			name:               "hidden alarm",
			alarms:             []*alarms.Alarm{testHiddenAlarm},
			expectedAlarmCount: 0,
		},
		{
			name: "all together",
			alarms: []*alarms.Alarm{
				testValidAlarm,
				testInvalidAlarm,
				testHiddenAlarm,
			},
			expectedAlarmCount: 1,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockAlarmState := new(mocks.ManagedState)
			mockAlarmState.On("GetTimestampMs").
				Return(testTime)

			alarmsDefault = createAlarmsMap(state.AlarmTypeSoftware, test.alarms...)
			gotResp := new(frontend.AlarmCount)
			svc := &AlarmService{alarmState: mockAlarmState}
			svc.alarmCountFn(gotResp)()
			assert.Equal(t, gotResp.Count, test.expectedAlarmCount)
			assert.Equal(t, testTime, gotResp.Ts.TimestampMs)
		})
	}
}

func TestAlarmService_GetNextAlarmList(t *testing.T) {
	testTime := time.Now().UnixMilli()
	tests := []struct {
		name  string
		error error
	}{
		{"success", nil},
		{"error", assert.AnError},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockAlarmState := new(mocks.ManagedState)
			mockAlarmState.On("ReadOnNext",
				mock.AnythingOfType("context.todoCtx"),
				testTime,
				mock.AnythingOfType("func()"),
			).Return(test.error == nil)

			svc := &AlarmService{alarmState: mockAlarmState}
			req := &frontend.Timestamp{TimestampMs: testTime}
			_, err := svc.GetNextAlarmList(context.TODO(), req)
			if test.error != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockAlarmState.AssertExpectations(t)
			}
		})
	}
}

func TestAlarmService_GetNextAlarmCount(t *testing.T) {
	testTime := time.Now().UnixMilli()
	tests := []struct {
		name  string
		error error
	}{
		{"success", nil},
		{"error", assert.AnError},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockAlarmState := new(mocks.ManagedState)
			mockAlarmState.On("ReadOnNext",
				mock.AnythingOfType("context.todoCtx"),
				testTime,
				mock.AnythingOfType("func()"),
			).Return(test.error == nil)

			svc := &AlarmService{alarmState: mockAlarmState}
			req := &frontend.Timestamp{TimestampMs: testTime}
			_, err := svc.GetNextAlarmCount(context.TODO(), req)
			if test.error != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockAlarmState.AssertExpectations(t)
			}
		})
	}
}

func TestAlarmService_GetNextNewAlarmList(t *testing.T) {
	testTime := time.Now().UnixMilli()
	tests := []struct {
		name  string
		error error
	}{
		{"success", nil},
		{"error", assert.AnError},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockAlarmState := new(mocks.ManagedState)
			mockAlarmState.On("ReadOnNext",
				mock.AnythingOfType("context.todoCtx"),
				testTime,
				mock.AnythingOfType("func()"),
			).Return(test.error == nil)

			svc := &AlarmService{alarmState: mockAlarmState}
			req := &frontend.Timestamp{TimestampMs: testTime}
			_, err := svc.GetNextNewAlarmList(context.TODO(), req)
			if test.error != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockAlarmState.AssertExpectations(t)
			}
		})
	}
}

func TestAlarmService_AcknowledgeAlarm(t *testing.T) {
	saveAcknowledgeAlarmsDefault := errInvalidAlarmState
	defer func() { errInvalidAlarmState = saveAcknowledgeAlarmsDefault }()
	tests := []struct {
		name  string
		error error
	}{
		{"success", nil},
		{"error", assert.AnError},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			errInvalidAlarmState = test.error
			svc := &AlarmService{}
			_, err := svc.AcknowledgeAlarm(context.TODO(), &frontend.AcknowledgeRequest{Identifier: "test"})
			if test.error != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

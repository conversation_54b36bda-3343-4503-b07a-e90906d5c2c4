package services

import (
	"context"
	"fmt"
	"math/rand"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/rows"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/commander/state/mocks"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

func TestNewCameraService(t *testing.T) {
	saveCamerasDefault := camerasDefault
	saveFrontendRegisterCameraServiceServer := frontendRegisterCameraServiceServer
	defer func() {
		camerasDefault = saveCamerasDefault
		frontendRegisterCameraServiceServer = saveFrontendRegisterCameraServiceServer
	}()
	grpcServer := &grpc.Server{}
	camState := &state.OverallCameraState{}
	camWatcher := &state.CameraWatcher{}
	var gotCameraSvc frontend.CameraServiceServer
	frontendRegisterCameraServiceServer = func(s grpc.ServiceRegistrar, srv frontend.CameraServiceServer) {
		assert.Equal(t, grpcServer, s)
		gotCameraSvc = srv
	}
	svc := NewCameraService(environment.Robot{}, grpcServer, camState, camWatcher, make(map[int]*rows.RowClients))
	assert.Equal(t, camState, svc.camState)
	assert.Equal(t, camWatcher, svc.camWatcher)
	assert.Equal(t, svc, gotCameraSvc)
}

func Test_cameraListFn(t *testing.T) {
	testTime := time.Now().UnixMilli()
	tests := []struct {
		name                string
		request             interface{}
		camType             state.CameraType
		connectedCameras    int
		disconnectedCameras int
		expectedCameras     int
	}{
		{"any:target no disconn",
			&frontend.CameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypeTarget,
			5, 5, 5},
		{"any:target all",
			&frontend.CameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypeTarget,
			5, 5, 10},
		{"any:predict no disconn",
			&frontend.CameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypePredict,
			5, 5, 5},
		{"any:predict all",
			&frontend.CameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypePredict,
			5, 5, 10},
		{"target:predict all",
			&frontend.CameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_TARGET,
			},
			state.CameraTypePredict,
			5, 5, 0},
		{"target:predict no disconn",
			&frontend.CameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_TARGET,
			},
			state.CameraTypePredict,
			5, 5, 0},
		{"predict:target all",
			&frontend.CameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_PREDICT,
			},
			state.CameraTypeTarget,
			5, 5, 0},
		{"predict:target no disconn",
			&frontend.CameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_PREDICT,
			},
			state.CameraTypeTarget,
			5, 5, 0},
		{"predict:predict all",
			&frontend.CameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_PREDICT,
			},
			state.CameraTypePredict,
			5, 5, 10},
		{"target:target no disconn",
			&frontend.CameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_TARGET,
			},
			state.CameraTypeTarget,
			5, 5, 5},

		{"any:target no disconn",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypeTarget,
			5, 5, 5},
		{"any:target all",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypeTarget,
			5, 5, 10},
		{"any:predict no disconn",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypePredict,
			5, 5, 5},
		{"any:predict all",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_ANY,
			},
			state.CameraTypePredict,
			5, 5, 10},
		{"target:predict all",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_TARGET,
			},
			state.CameraTypePredict,
			5, 5, 0},
		{"target:predict no disconn",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_TARGET,
			},
			state.CameraTypePredict,
			5, 5, 0},
		{"predict:target all",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_PREDICT,
			},
			state.CameraTypeTarget,
			5, 5, 0},
		{"predict:target no disconn",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_PREDICT,
			},
			state.CameraTypeTarget,
			5, 5, 0},
		{"predict:predict all",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: true,
				Type:                frontend.CameraType_PREDICT,
			},
			state.CameraTypePredict,
			5, 5, 10},
		{"target:target no disconn",
			&frontend.NextCameraListRequest{
				IncludeDisconnected: false,
				Type:                frontend.CameraType_TARGET,
			},
			state.CameraTypeTarget,
			5, 5, 5},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockCameraState := new(mocks.ManagedState)
			mockCameraState.On("GetTimestampMs").
				Return(testTime)

			camerasDefault = createCameraList(t, test.camType, test.disconnectedCameras, test.connectedCameras)
			gotResp := new(frontend.CameraList)
			svc := &CameraService{camState: mockCameraState}
			svc.cameraListFn(test.request, gotResp)()
			assert.Len(t, gotResp.Cameras, test.expectedCameras)
			assert.Equal(t, testTime, gotResp.Ts.TimestampMs)
		})
	}
}

func createCameraList(t *testing.T, typ state.CameraType, disconnected, connected int) map[string]*state.Camera {
	t.Helper()
	camList := make(map[string]*state.Camera)
	for i := 0; i < disconnected; i++ {
		gid := fmt.Sprintf("d-%d", i)
		camList[gid] = &state.Camera{
			GlobalCameraId: gid,
			Type:           typ,
			Connected:      false,
		}
	}
	for i := 0; i < connected; i++ {
		gid := fmt.Sprintf("c-%d", i)
		camList[gid] = &state.Camera{
			GlobalCameraId: gid,
			Type:           typ,
			Connected:      true,
		}
	}
	return camList
}

func TestCameraService_GetCameraList(t *testing.T) {
	tests := []struct {
		name  string
		error error
	}{
		{"success", nil},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockCameraState := new(mocks.ManagedState)
			mockCameraState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return(test.error == nil)

			svc := &CameraService{camState: mockCameraState}
			req := &frontend.CameraListRequest{}
			_, err := svc.GetCameraList(context.TODO(), req)
			if test.error != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockCameraState.AssertExpectations(t)
			}
		})
	}
}

func TestCameraService_GetNextCameraList(t *testing.T) {
	testTime := time.Now().UnixMilli()
	tests := []struct {
		name  string
		error error
	}{
		{"success", nil},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockCameraState := new(mocks.ManagedState)
			mockCameraState.On("ReadOnNext",
				mock.AnythingOfType("context.todoCtx"),
				testTime,
				mock.AnythingOfType("func()"),
			).Return(test.error == nil)

			svc := &CameraService{camState: mockCameraState}
			req := &frontend.NextCameraListRequest{
				Ts: &frontend.Timestamp{TimestampMs: testTime},
			}
			_, err := svc.GetNextCameraList(context.TODO(), req)
			if test.error != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockCameraState.AssertExpectations(t)
			}
		})
	}
}

func TestSortCameras(t *testing.T) {
	testCameraA := &frontend.Camera{CameraId: "a"}
	testCameraB := &frontend.Camera{CameraId: "b"}
	testCameraC := &frontend.Camera{CameraId: "c"}
	testCameraD := &frontend.Camera{CameraId: "d"}
	testCameraE := &frontend.Camera{CameraId: "e"}

	tests := []struct {
		name          string
		sortedCameras []*frontend.Camera
	}{
		{"", []*frontend.Camera{
			testCameraA, testCameraB, testCameraC, testCameraD, testCameraE,
		}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			cloneCams := make([]*frontend.Camera, 0)
			for _, cam := range test.sortedCameras {
				cloneCams = append(cloneCams, cam)
			}
			rand.Shuffle(len(cloneCams), func(i, j int) { cloneCams[i], cloneCams[j] = cloneCams[j], cloneCams[i] })
			SortCameras(cloneCams)
			assertEqualCameras(t, test.sortedCameras, cloneCams)
		})
	}
}

func assertEqualCameras(t *testing.T, a, b []*frontend.Camera) {
	t.Helper()
	assert.Len(t, a, len(b))
	for idx, cam := range a {
		assert.Equal(t, cam.CameraId, b[idx].CameraId)
	}
}

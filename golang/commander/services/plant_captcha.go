package services

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"os/exec"
	"sort"
	"strings"
	"time"

	captcha "github.com/carbonrobotics/robot/golang/commander/lib/captcha/algorithms"
	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/jlaffaye/ftp"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
)

type PlantCaptchaService struct {
	frontend.UnimplementedPlantCaptchaServiceServer

	plantCaptchaState *state.PlantCaptchaState
	modelinatorState  *state.ModelinatorCfgState
	almanacState      *state.AlmanacCfgState

	rowClients    map[int]*rows.RowClients
	env           *environment.Robot
	serveMux      *http.ServeMux
	dumClient     *data_upload_manager.EmergencyClient
	veselkaClient *veselka.Client
	redisClient   *redis.Client
	configNode    *config.ConfigTree
}

type PlantCaptchaItemPrediction struct {
	Metadata   *weed_tracking.PlantCaptchaItemMetadata
	Prediction weed_tracking.PlantCaptchaUserPrediction
}

func NewPlantCaptchaService(grpcServer *grpc.Server, plantCaptchaState *state.PlantCaptchaState, modelinatorState *state.ModelinatorCfgState, almanacState *state.AlmanacCfgState, rowClients map[int]*rows.RowClients, env *environment.Robot, serveMux *http.ServeMux, dumClient *data_upload_manager.EmergencyClient, veselkaClient *veselka.Client, redisClient *redis.Client, configSubscriber *config.ConfigSubscriber) *PlantCaptchaService {
	rowsToUse := rowClients
	if environment.CarbonRole(env.MakaRole) == environment.CarbonRoleSimulator {
		// in simulator, use only row 1
		rowsToUse = make(map[int]*rows.RowClients)
		rowsToUse[1] = rowClients[1]
	}

	s := &PlantCaptchaService{
		rowClients:        rowsToUse,
		plantCaptchaState: plantCaptchaState,
		modelinatorState:  modelinatorState,
		almanacState:      almanacState,
		serveMux:          serveMux,
		env:               env,
		dumClient:         dumClient,
		veselkaClient:     veselkaClient,
		redisClient:       redisClient,
		configNode:        configSubscriber.GetConfigNode("common", "plant_captcha"),
	}
	frontend.RegisterPlantCaptchaServiceServer(grpcServer, s)
	serveMux.Handle("/plant_captcha/", http.HandlerFunc(s.HandleImage))
	return s
}

func (s *PlantCaptchaService) saveAlmanacAndModelinator(plantCaptcha *frontend.PlantCaptcha) error {
	modelinatorCfg, err := s.modelinatorState.FetchModelinatorConfig(plantCaptcha.ModelId, plantCaptcha.CropId)
	if err != nil {
		return fmt.Errorf(state.PlantCaptchaNoModelinator, plantCaptcha.ModelId, plantCaptcha.CropId)
	}

	modelinatorJson, err := protojson.Marshal(modelinatorCfg)
	if err != nil {
		return fmt.Errorf(state.PlantCaptchaCantSaveModelinator, plantCaptcha.ModelId, plantCaptcha.CropId)
	}

	err = os.WriteFile(state.PlantCaptchaDir+plantCaptcha.Name+"/modelinator.json", modelinatorJson, 0644)
	if err != nil {
		return fmt.Errorf(state.PlantCaptchaCantSaveModelinator, plantCaptcha.ModelId, plantCaptcha.CropId)
	}

	activeAlmanac := s.almanacState.GetActiveCfg()
	almanacCfg, err := s.redisClient.LoadAlmanacConfig(activeAlmanac)
	if err != nil {
		return fmt.Errorf(state.PlantCaptchaNoAlmanac, activeAlmanac)
	}

	almanacJson, err := protojson.Marshal(almanacCfg)
	if err != nil {
		return fmt.Errorf(state.PlantCaptchaCantSaveAlmanac)
	}

	err = os.WriteFile(state.PlantCaptchaDir+plantCaptcha.Name+"/almanac.json", almanacJson, 0644)
	if err != nil {
		return fmt.Errorf(state.PlantCaptchaCantSaveAlmanac)
	}

	return nil
}

func (s *PlantCaptchaService) StartPlantCaptcha(ctx context.Context, req *frontend.StartPlantCaptchaRequest) (resp *frontend.StartPlantCaptchaResponse, err error) {
	if !isAlphaNumeric(req.PlantCaptcha.Name) {
		return nil, errors.New(state.PlantCaptchaErrorMustBeAlphaNumeric)
	}

	os.Mkdir(state.PlantCaptchaDir, os.ModePerm)
	os.Mkdir(state.PlantCaptchaDir+req.PlantCaptcha.Name, os.ModePerm)
	os.Mkdir(state.PlantCaptchaDir+req.PlantCaptcha.Name+"/results", os.ModePerm)

	err = s.saveAlmanacAndModelinator(req.PlantCaptcha)
	if err != nil {
		os.RemoveAll(state.PlantCaptchaDir + req.PlantCaptcha.Name)
		return nil, err
	}

	logrus.Infof("PlantCaptcha: Received request to start plant captcha")
	req.PlantCaptcha.StartTimeMs = time.Now().UnixMilli()

	req.PlantCaptcha.RowsUsed = make([]int32, 0)
	for i := range s.rowClients {
		req.PlantCaptcha.RowsUsed = append(req.PlantCaptcha.RowsUsed, int32(i))
	}

	err = s.plantCaptchaState.StartPlantCaptcha(req.PlantCaptcha)
	if err != nil {
		logrus.WithError(err).Error("PlantCaptcha: Start plant captcha unsuccessful")
		os.RemoveAll(state.PlantCaptchaDir + req.PlantCaptcha.Name)
		return nil, err
	}

	defer func() {
		if err != nil {
			for _, rowClient := range s.rowClients {
				rowClient.WeedTrackingClient.CancelPlantCaptcha()
			}
		}
	}()

	for i, r := range s.rowClients {
		logrus.Info("PlantCaptcha: Starting plant captcha on row ", i)
		err = r.WeedTrackingClient.StartPlantCaptcha()
		if err != nil {
			logrus.WithError(err).Errorf("PlantCaptcha: Could not start plant captcha on row %v", i)
			s.cleanupOnError(req.PlantCaptcha.Name)
			return nil, errors.New(state.PlantCaptchaErrorInternal)
		}
	}

	go s.queryCaptchaResults(req.PlantCaptcha.Name)

	return &frontend.StartPlantCaptchaResponse{}, nil
}

func (s *PlantCaptchaService) cleanupOnError(captchaId string) {
	for _, r := range s.rowClients {
		r.WeedTrackingClient.RemovePlantCaptchaDirectory()
	}
	os.RemoveAll(state.PlantCaptchaDir + captchaId)
	s.plantCaptchaState.CaptchaFailed(captchaId)
}

func (s *PlantCaptchaService) queryCaptchaResults(captchaId string) {
	ready := make(map[int]bool)
	for i := range s.rowClients {
		ready[i] = false
	}

	folder := state.PlantCaptchaDir + captchaId

	exemplarCounts := make(map[string]int32)

	for {
		allReady := true
		for i := range s.rowClients {
			allReady = allReady && ready[i]
		}

		if allReady {
			break
		}

		time.Sleep(1 * time.Second)

		totalImages := 0
		imagesTaken := 0
		metadataTaken := 0

		exemplarCounts = make(map[string]int32)

		queryRetries := int(s.configNode.GetChild("query_retries").GetUIntValue())

		for rowIdx, row := range s.rowClients {
			var resp *weed_tracking.PlantCaptchaStatusResponse
			var err error
			for attempt := 0; attempt < queryRetries; attempt++ {
				resp, err = row.WeedTrackingClient.GetPlantCaptchaStatus()
				if err == nil {
					break
				}
				logrus.Warnf("PlantCaptcha: failed to get status from row %v on attempt %v/%v, trying again: err=%v", rowIdx, attempt+1, queryRetries, err)
				time.Sleep(1 * time.Second)
			}

			if err != nil {
				logrus.Errorf("PlantCaptcha: failed to get captcha from row %v, err=%v", rowIdx, err)
				s.cleanupOnError(captchaId)
				return
			} else if resp.Status == weed_tracking.PlantCaptchaStatus_CAPTCHA_FAILED {
				logrus.Errorf("PlantCaptcha: received status=CAPTCHA_FAILED on row=%v", rowIdx)
				s.cleanupOnError(captchaId)
				return
			}

			if resp.Status == weed_tracking.PlantCaptchaStatus_CAPTCHA_CANCELLED {
				return
			}

			totalImages += int(resp.TotalImages)
			imagesTaken += int(resp.ImagesTaken)
			metadataTaken += int(resp.MetadataTaken)

			if resp.ExemplarCounts != nil {
				// join the maps
				for k, v := range resp.ExemplarCounts {
					exemplarCounts[k] += v
				}
			}

			if ready[rowIdx] {
				continue
			}

			if resp.Status == weed_tracking.PlantCaptchaStatus_CAPTCHA_FINISHED {
				logrus.Infof("PlantCaptcha: Captcha on row %v finished, starting gathering files", rowIdx)
				conn, err := ftp.Dial(row.StreamHosts[row.PrimaryPCId]+":21", ftp.DialWithTimeout(10*time.Second))
				if err != nil {
					logrus.Errorf("PlantCaptcha: could not ftp-dial to row %v, err=%v", rowIdx, err)
					s.cleanupOnError(folder)
					return
				}
				err = conn.Login("recorder", auth.FTPPassword)
				if err != nil {
					logrus.Errorf("PlantCaptcha: could not ftp-login to row %v, err=%v", rowIdx, err)
					s.cleanupOnError(folder)
					return
				}
				err = conn.ChangeDir("plant_captcha")
				if err != nil {
					logrus.Errorf("PlantCaptcha: could not ftp-change-dir on row %v, err=%v", rowIdx, err)
					s.cleanupOnError(folder)
					return
				}
				r, err := conn.Retr("plant_captcha.zip")
				if err != nil {
					logrus.Errorf("PlantCaptcha: could transfer file over ftp from row %v, err=%v", rowIdx, err)
					s.cleanupOnError(folder)
					return
				}
				defer r.Close()
				outFileName := fmt.Sprintf("%v/row%v_plant_captcha.zip", folder, rowIdx)
				outFile, err := os.Create(outFileName)
				if err != nil {
					logrus.Errorf("PlantCaptcha: could not transfer file over ftp from row %v, can't open file, err=%v", rowIdx, err)
					s.cleanupOnError(folder)
					return
				}

				bufferedWriter := bufio.NewWriter(outFile)
				buf := make([]byte, 1024)
				for {
					n, err := r.Read(buf)
					_, err1 := bufferedWriter.Write(buf[:n])
					if err == io.EOF {
						break
					}
					if err != nil || err1 != nil {
						logrus.Errorf("PlantCaptcha: could not transfer file over ftp from row %v, err=%v %v", rowIdx, err, err1)
						s.cleanupOnError(folder)
						outFile.Close()
						return
					}
				}
				bufferedWriter.Flush()
				outFile.Close()

				cmd := exec.Command("bash", "-c", fmt.Sprintf("cd %v && mkdir row%v && unzip %v -d row%v/", folder, rowIdx, outFileName, rowIdx))
				out, err := cmd.Output()
				if err != nil {
					logrus.Errorf("PlantCaptcha: could not unzip %v, err=%v, out=%v", outFileName, err, string(out))
					os.RemoveAll(outFileName)
					s.cleanupOnError(folder)
					return
				}
				os.RemoveAll(outFileName)

				row.WeedTrackingClient.RemovePlantCaptchaDirectory()
				logrus.Infof("PlantCaptcha: Gathering files on row %v finished", rowIdx)
				ready[rowIdx] = true
			}
		}

		s.plantCaptchaState.WriteOnCurrent(func() {
			s.plantCaptchaState.TotalImages = int32(totalImages)
			s.plantCaptchaState.ImagesTaken = int32(imagesTaken)
			s.plantCaptchaState.MetadataTaken = int32(metadataTaken)
		})
	}

	exemplarJson, err := json.Marshal(exemplarCounts)
	if err != nil {
		logrus.WithError(err).Error("PlantCaptcha: failed to marshal exemplar counts")
		s.cleanupOnError(folder)
		return
	}

	err = os.WriteFile(folder+"/exemplars.json", exemplarJson, 0644)
	if err != nil {
		logrus.WithError(err).Error("PlantCaptcha: write exemplar counts to file")
		s.cleanupOnError(folder)
		return
	}

	s.plantCaptchaState.WriteOnCurrent(func() {
		s.plantCaptchaState.Status = weed_tracking.PlantCaptchaStatus_CAPTCHA_FINISHED
		s.plantCaptchaState.ActiveCaptcha = ""
	})
}

func (s *PlantCaptchaService) GetNextPlantCaptchaStatus(ctx context.Context, req *frontend.GetNextPlantCaptchaStatusRequest) (*frontend.GetNextPlantCaptchaStatusResponse, error) {
	resp := &frontend.GetNextPlantCaptchaStatusResponse{}
	s.plantCaptchaState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		resp.Status = s.plantCaptchaState.Status
		resp.TotalImages = s.plantCaptchaState.TotalImages
		resp.ImagesTaken = s.plantCaptchaState.ImagesTaken
		resp.MetadataTaken = s.plantCaptchaState.MetadataTaken
		resp.Ts = &frontend.Timestamp{TimestampMs: s.plantCaptchaState.GetTimestampMs()}
	})

	return resp, nil
}

func (s *PlantCaptchaService) GetNextPlantCaptchasList(ctx context.Context, req *frontend.GetNextPlantCaptchasListRequest) (*frontend.GetNextPlantCaptchasListResponse, error) {
	resp := &frontend.GetNextPlantCaptchasListResponse{
		PlantCaptchas: make([]*frontend.PlantCaptchaListItem, 0),
	}
	s.plantCaptchaState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		for _, pc := range s.plantCaptchaState.PlantCaptchas {
			if pc.Name == s.plantCaptchaState.ActiveCaptcha {
				continue
			}
			li := &frontend.PlantCaptchaListItem{
				PlantCaptcha: pc,
			}
			resp.PlantCaptchas = append(resp.PlantCaptchas, li)
		}
		resp.Ts = &frontend.Timestamp{TimestampMs: s.plantCaptchaState.GetTimestampMs()}
	})

	for _, li := range resp.PlantCaptchas {
		li.ImagesTaken = s.getNumImagesTaken(li.PlantCaptcha)
		li.ImagesProcessed = s.plantCaptchaState.GetNumImagesProcessed(li.PlantCaptcha.Name)
	}
	sort.Slice(resp.PlantCaptchas, func(i, j int) bool {
		return resp.PlantCaptchas[i].PlantCaptcha.StartTimeMs > resp.PlantCaptchas[j].PlantCaptcha.StartTimeMs
	})
	return resp, nil
}

func (s *PlantCaptchaService) getNumImagesTaken(captcha *frontend.PlantCaptcha) int32 {
	var total int32 = 0
	for _, rowIdx := range captcha.RowsUsed {
		dir := fmt.Sprintf("%v%v/row%v/", state.PlantCaptchaDir, captcha.Name, rowIdx)
		files, err := os.ReadDir(dir)
		if err == nil {
			total += int32(len(files))
		}
	}
	return total
}

func (s *PlantCaptchaService) DeletePlantCaptcha(ctx context.Context, req *frontend.DeletePlantCaptchaRequest) (*frontend.Empty, error) {
	return &frontend.Empty{}, s.plantCaptchaState.DeletePlantCaptcha(req.Name)
}

// for simulator
func getLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, address := range addrs {
		// check the address type and if it is not a loopback the display it
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return ""
}

func (s *PlantCaptchaService) GetPlantCaptcha(ctx context.Context, req *frontend.GetPlantCaptchaRequest) (*frontend.GetPlantCaptchaResponse, error) {
	var captcha *frontend.PlantCaptcha
	s.plantCaptchaState.ReadOnCurrent(func() {
		captcha = s.plantCaptchaState.PlantCaptchas[req.Name]
	})
	if captcha == nil {
		return nil, fmt.Errorf("Can't find plant captcha %v", req.Name)
	}

	items, err := s.getItems(captcha)
	if err != nil {
		return nil, err
	}
	resp := &frontend.GetPlantCaptchaResponse{
		PlantCaptcha: captcha,
		Items:        items,
	}

	return resp, nil
}

func (s *PlantCaptchaService) getItems(captcha *frontend.PlantCaptcha) ([]*frontend.PlantCaptchaItem, error) {
	items := make([]*frontend.PlantCaptchaItem, 0)

	host := "*********"
	if environment.CarbonRole(s.env.MakaRole) == environment.CarbonRoleSimulator {
		host = getLocalIP()
	} else if strings.HasPrefix(s.env.MakaRobotName, "slayertb1-mini") {
		host = "slayertb1-mini-command"
	} else if strings.HasPrefix(s.env.MakaRobotName, "slayertb1") {
		host = "slayertb1-command"
	} else if strings.HasPrefix(s.env.MakaRobotName, "reapertb1") {
		host = "reapertb1-command"
	}

	for _, rowIdx := range captcha.RowsUsed {
		dir := fmt.Sprintf("%v%v/row%v/", state.PlantCaptchaDir, captcha.Name, rowIdx)
		itemDirs, err := os.ReadDir(dir)
		if err != nil {
			logrus.WithError(err).Errorf("PlantCaptcha: could not read directory %v", dir)
			continue
		}
		for _, itemDir := range itemDirs {
			if !itemDir.IsDir() {
				continue
			}

			urls := make([]string, 0)
			metas := make([]*weed_tracking.PlantCaptchaItemMetadata, 0)

			files, err := os.ReadDir(dir + itemDir.Name())
			if err != nil {
				logrus.WithError(err).Errorf("PlantCaptcha: could not read directory %v", dir)
				continue
			}
			for _, f := range files {
				if !strings.HasSuffix(f.Name(), ".png") {
					continue
				}

				url := fmt.Sprintf("http://%v:61011/plant_captcha/%v/row%v/%v/%v", host, captcha.Name, rowIdx, itemDir.Name(), f.Name())
				urls = append(urls, url)

				metaFileName := dir + itemDir.Name() + "/" + f.Name()[:len(f.Name())-4] + ".meta.json"
				json, err := ioutil.ReadFile(metaFileName)
				if err != nil {
					return nil, err
				}
				meta := &weed_tracking.PlantCaptchaItemMetadata{}
				protojson.Unmarshal(json, meta)

				metas = append(metas, meta)
			}

			if len(urls) == 0 {
				return nil, fmt.Errorf("PlantCaptcha: no images found in %v", dir)
			}

			// urls should be sorted by nature of the file system
			// use the middle image as the main image
			mid := len(urls) / 2
			items = append(items, &frontend.PlantCaptchaItem{
				ImageUrl: urls[mid], Metadata: metas[mid],
				AdditionalImageUrls: urls, AdditionalMetadatas: metas,
			})
		}
	}
	sort.Slice(items, func(i, j int) bool { return strings.Compare(items[i].Metadata.Id, items[j].Metadata.Id) < 0 })
	return items, nil
}

func (s *PlantCaptchaService) HandleImage(w http.ResponseWriter, r *http.Request) {
	if r == nil {
		logrus.Error("PlantCaptcha: request for HandleImage is nil")
		http.Error(w, "Request is nil", http.StatusBadRequest)
		return
	}
	localPath := strings.Replace(r.URL.Path, "/plant_captcha/", state.PlantCaptchaDir, 1)
	handleImageRequest(w, localPath)
}

func (s *PlantCaptchaService) CancelPlantCaptcha(ctx context.Context, req *frontend.Empty) (*frontend.Empty, error) {
	var err error
	s.plantCaptchaState.WriteOnCurrent(func() {
		if s.plantCaptchaState.Status != weed_tracking.PlantCaptchaStatus_CAPTCHA_STARTED {
			return
		}

		for _, rowClient := range s.rowClients {
			err = rowClient.WeedTrackingClient.CancelPlantCaptcha()
			if err != nil {
				return
			}
		}

		s.plantCaptchaState.CancelPlantCaptcha()
		logrus.Info("PlantCaptcha: Running captcha cancelled")
	})

	return &frontend.Empty{}, err
}

func (s *PlantCaptchaService) StartPlantCaptchaUpload(ctx context.Context, req *frontend.StartPlantCaptchaUploadRequest) (*frontend.Empty, error) {
	var err error
	s.plantCaptchaState.WriteOnCurrent(func() {
		if st, ok := s.plantCaptchaState.UploadStates[req.Name]; ok {
			if st.UploadState == frontend.PlantCaptchaUploadState_IN_PROGRESS {
				err = fmt.Errorf("Already uploading")
				return
			}

			if st.UploadState == frontend.PlantCaptchaUploadState_DONE {
				err = s.plantCaptchaState.DeleteUpload(req.Name)
			}
		}
		logrus.Infof("PlantCatcha: received request to upload %v", req.Name)
		s.plantCaptchaState.UploadStates[req.Name] = &state.PlantCaptchaUploadState{UploadState: frontend.PlantCaptchaUploadState_IN_PROGRESS}
		err = s.dumClient.StartPlantCaptchaUpload(req.Name)
	})

	return &frontend.Empty{}, err
}

func (s *PlantCaptchaService) GetNextPlantCaptchaUploadState(ctx context.Context, req *frontend.GetNextPlantCaptchaUploadStateRequest) (*frontend.GetNextPlantCaptchaUploadStateResponse, error) {
	resp := &frontend.GetNextPlantCaptchaUploadStateResponse{}
	var err error
	s.plantCaptchaState.ReadOnCurrent(func() {
		us := s.plantCaptchaState.UploadStates[req.Name]
		if us == nil {
			err = fmt.Errorf("Upload %v not found", req.Name)
			return
		}

		resp.Percent = us.Percent
		resp.UploadState = us.UploadState
	})
	return resp, err
}

func (s *PlantCaptchaService) SubmitPlantCaptchaResults(ctx context.Context, req *frontend.SubmitPlantCaptchaResultsRequest) (*frontend.Empty, error) {
	for _, res := range req.Results {
		s.plantCaptchaState.SaveResult(req.Name, res)
	}
	return &frontend.Empty{}, nil
}

func (s *PlantCaptchaService) readAlmanacAndModelinator(name string) (*almanac.ModelinatorConfig, *almanac.AlmanacConfig, error) {
	modelinatorJson, err := os.ReadFile(state.PlantCaptchaDir + name + "/modelinator.json")
	if err != nil {
		return nil, nil, errors.New(state.PlantCaptchaCantReadModelinator)
	}
	modelinatorCfg := &almanac.ModelinatorConfig{}
	err = protojson.Unmarshal(modelinatorJson, modelinatorCfg)
	if err != nil {
		return nil, nil, errors.New(state.PlantCaptchaCantReadModelinator)
	}
	almanacJson, err := os.ReadFile(state.PlantCaptchaDir + name + "/almanac.json")
	if err != nil {
		return nil, nil, errors.New(state.PlantCaptchaCantReadAlmanac)
	}
	almanacCfg := &almanac.AlmanacConfig{}
	err = protojson.Unmarshal(almanacJson, almanacCfg)
	if err != nil {
		return nil, nil, errors.New(state.PlantCaptchaCantReadAlmanac)
	}
	return modelinatorCfg, almanacCfg, nil
}

func (s *PlantCaptchaService) extractPlantCaptchaUserPrediction(res string) weed_tracking.PlantCaptchaUserPrediction {
	labelNum := weed_tracking.PlantCaptchaUserPrediction_value[res]
	label := weed_tracking.PlantCaptchaUserPrediction(labelNum)

	if label.String() != res {
		logrus.Warnf("PlantCaptcha: Got a label object that doesn't match the result name. Label %v, result %v. Marking as IGNORE", label, res)
		label = weed_tracking.PlantCaptchaUserPrediction_IGNORE
	}

	return label
}

func (s *PlantCaptchaService) CalculatePlantCaptcha(ctx context.Context, req *frontend.CalculatePlantCaptchaRequest) (*frontend.CalculatePlantCaptchaResponse, error) {
	var pc *frontend.PlantCaptcha
	s.plantCaptchaState.ReadOnCurrent(func() {
		pc = s.plantCaptchaState.PlantCaptchas[req.Name]
	})
	if pc == nil {
		return nil, fmt.Errorf("Capture %v not found", req.Name)
	}

	modelinatorCfg, almanacCfg, err := s.readAlmanacAndModelinator(req.Name)
	if err != nil {
		return nil, err
	}

	results := &frontend.PlantCaptchaResults{
		CaptchaResults:                  make([]*frontend.PlantCaptchaResult, 0),
		CurrentParameters:               modelinatorCfg,
		Algorithm:                       s.configNode.GetChild("veselka_algorithm").GetStringValue(),
		GoalCropsTargeted:               float32(s.configNode.GetChild("goal_crops_targeted").GetFloatValue()),
		GoalWeedsTargeted:               float32(s.configNode.GetChild("goal_weeds_targeted").GetFloatValue()),
		GoalUnknownTargeted:             float32(s.configNode.GetChild("goal_unknown_targeted").GetFloatValue()),
		Almanac:                         almanacCfg,
		MinRecommendedMindoo:            float32(s.configNode.GetChild("min_recommended_mindoo").GetFloatValue()),
		MaxRecommendedMindoo:            float32(s.configNode.GetChild("max_recommended_mindoo").GetFloatValue()),
		MinRecommendedWeedThreshold:     float32(s.configNode.GetChild("min_recommended_weed_threshold").GetFloatValue()),
		MaxRecommendedWeedThreshold:     float32(s.configNode.GetChild("max_recommended_weed_threshold").GetFloatValue()),
		MinRecommendedCropThreshold:     float32(s.configNode.GetChild("min_recommended_crop_threshold").GetFloatValue()),
		MaxRecommendedCropThreshold:     float32(s.configNode.GetChild("max_recommended_crop_threshold").GetFloatValue()),
		MinItemsForRecommendation:       int32(s.configNode.GetChild("min_items_for_recommendation").GetIntValue()),
		UseWeedCategoriesForWeedLabels:  s.configNode.GetChild("use_weed_categories_for_weed_labels").GetBoolValue(),
		MinDooForRecommendation:         float32(s.configNode.GetChild("min_doo_for_recommendation").GetFloatValue()),
		UseOtherAsTiebreaker:            s.configNode.GetChild("use_other_as_tiebreaker").GetBoolValue(),
		LimitByCropsMissed:              s.configNode.GetChild("limit_by_crops_missed").GetBoolValue(),
		NumberOfCropConfigurations:      int32(s.configNode.GetChild("number_of_crop_configurations").GetIntValue()),
		Tiebreaker:                      s.configNode.GetChild("tiebreaker").GetStringValue(),
		PadCropConfigurations:           s.configNode.GetChild("pad_crop_configurations").GetBoolValue(),
		MindooTiebreaker:                s.configNode.GetChild("mindoo_tiebreaker").GetStringValue(),
		UseBeneficialsAsCrops:           s.configNode.GetChild("use_beneficials_as_crops").GetBoolValue(),
		UseVolunteersAsWeeds:            s.configNode.GetChild("use_volunteers_as_weeds").GetBoolValue(),
		TiebreakerStrategyThresholdWeed: s.configNode.GetChild("tiebreaker_strategy_threshold_weed").GetStringValue(),
		TiebreakerStrategyThresholdCrop: s.configNode.GetChild("tiebreaker_strategy_threshold_crop").GetStringValue(),
		TiebreakerStrategyMindooWeed:    s.configNode.GetChild("tiebreaker_strategy_mindoo_weed").GetStringValue(),
		TiebreakerStrategyMindooCrop:    s.configNode.GetChild("tiebreaker_strategy_mindoo_crop").GetStringValue(),
	}

	items, err := s.getItems(pc)
	resMap, err := s.plantCaptchaState.GetResults(req.Name)
	for _, item := range items {
		res := resMap[item.Metadata.Id]
		if res == "" {
			continue
		}

		label := s.extractPlantCaptchaUserPrediction(res)

		result := &frontend.PlantCaptchaResult{
			Label:    label,
			Metadata: item.Metadata,
		}
		results.CaptchaResults = append(results.CaptchaResults, result)
	}
	algorithm := s.configNode.GetChild("algorithm").GetStringValue()
	var a captcha.PlantCaptchaAlgorithm
	switch algorithm {
	case "veselka":
		a = &captcha.VeselkaPlantCaptchaAlgorithm{VeselkaClient: s.veselkaClient}
	case "weeding":
		a = &captcha.CategorySizeWeedingPlantCaptchaAlgorithm{}
	case "weeding_exhaustive":
		a = &captcha.CategorySizeWeedingExhaustivePlantCaptchaAlgorithm{}
	case "weeding_thinning":
		a = &captcha.CategorySizeWeedingThinningPlantCaptchaAlgorithm{}
	case "weeding_no_crop":
		a = &captcha.CategorySizeWeedingNoCropPlantCaptchaAlgorithm{}
	default:
		a = &captcha.MockPlantCaptchaAlgorithm{}
	}
	return a.Submit(ctx, pc, results)
}

func (s *PlantCaptchaService) GetPlantCaptchaItemResults(ctx context.Context, req *frontend.GetPlantCaptchaItemResultsRequest) (*frontend.GetPlantCaptchaItemResultsResponse, error) {
	resp := &frontend.GetPlantCaptchaItemResultsResponse{Results: make([]*frontend.PlantCaptchaItemResult, 0)}
	results, err := s.plantCaptchaState.GetResults(req.Name)
	if err != nil {
		return nil, err
	}
	for _, id := range req.Id {
		resultStr := results[id]
		if resultStr != "" {
			resultNum := weed_tracking.PlantCaptchaUserPrediction_value[resultStr]
			result := weed_tracking.PlantCaptchaUserPrediction(resultNum)
			resp.Results = append(resp.Results, &frontend.PlantCaptchaItemResult{Id: id, UserPrediction: result})
		}
	}
	return resp, nil
}

// returns the modelinator setting that was saved at the start of the plant captcha
// returns error if the modelinator or almanac files are not found.
func (s *PlantCaptchaService) GetOriginalModelinatorConfig(ctx context.Context, req *frontend.GetOriginalModelinatorConfigRequest) (*frontend.GetOriginalModelinatorConfigResponse, error) {
	modelinatorCfg, _, err := s.readAlmanacAndModelinator(req.Name)
	if err != nil {
		return nil, err
	}

	return &frontend.GetOriginalModelinatorConfigResponse{ModelinatorConfig: modelinatorCfg}, nil
}

func (s *PlantCaptchaService) GetCaptchaRowStatus(ctx context.Context, req *frontend.Empty) (*frontend.GetCaptchaRowStatusResponse, error) {
	resp := &frontend.GetCaptchaRowStatusResponse{
		RowStatus: make(map[int32]*weed_tracking.PlantCaptchaStatusResponse),
	}
	for i, r := range s.rowClients {
		status, err := r.WeedTrackingClient.GetPlantCaptchaStatus()
		if err != nil {
			return nil, err
		}
		resp.RowStatus[int32(i)] = status
	}
	return resp, nil
}

func (s *PlantCaptchaService) CancelPlantCaptchaOnRow(ctx context.Context, req *frontend.CancelPlantCaptchaOnRowRequest) (*frontend.Empty, error) {
	if _, ok := s.rowClients[int(req.RowId)]; !ok {
		return nil, fmt.Errorf("Row %v not found", req.RowId)
	}

	err := s.rowClients[int(req.RowId)].WeedTrackingClient.CancelPlantCaptcha()
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

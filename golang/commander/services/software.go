package services

import (
	"context"
	"fmt"
	"sort"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type SoftwareService struct {
	frontend.UnimplementedSoftwareServiceServer
	softwareState  *state.OverallSoftwareState
	clients        []*hosts.HostClients
	showUpdateNode *config.ConfigTree
	configClient   *config.ConfigClient
	idToNameMap    map[uint32]string // map of host id to human readable name
	logger         *logrus.Entry
}

func NewSoftwareService(grpcServer *grpc.Server, softwareState *state.OverallSoftwareState, hosts []*hosts.HostClients, commanderNode *config.ConfigTree, configClient *config.ConfigClient) *SoftwareService {
	service := &SoftwareService{
		softwareState:  softwareState,
		clients:        hosts,
		showUpdateNode: commanderNode.GetChild("show_software_update_to_user"),
		configClient:   configClient,
		idToNameMap:    make(map[uint32]string),
		logger:         logrus.WithField("module", "SoftwareService"),
	}
	frontend.RegisterSoftwareServiceServer(grpcServer, service)
	service.showUpdateNode.RegisterCallback(func() {
		service.softwareState.WriteOnCurrent(func() {})
	})

	for _, client := range hosts {
		service.idToNameMap[client.PcId] = client.HostClient.Hostname
	}

	return service
}

func (s *SoftwareService) GetNextSoftwareVersionState(ctx context.Context, req *frontend.SoftwareVersionStateRequest) (*frontend.SoftwareVersionState, error) {
	resp := &frontend.SoftwareVersionState{
		Current: &frontend.SoftwareVersion{
			Available: false,
			Ready:     false,
			Tag:       "",
		},
		Target: &frontend.SoftwareVersion{
			Available: false,
			Ready:     false,
			Tag:       "",
		},
		Previous: &frontend.SoftwareVersion{
			Available: false,
			Ready:     false,
			Tag:       "",
		},
	}

	s.softwareState.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.softwareState.GetTimestampMs(),
		}

		resp.Updating = s.softwareState.Updating || s.softwareState.IsHostUpdating()

		if s.softwareState.Summary == nil || s.softwareState.Summary.Current == nil || s.softwareState.Summary.Target == nil || s.softwareState.Summary.Previous == nil {
			// This should never happen in theory
			s.logger.Warn("Software State Summary Unset")
			return
		}

		if s.softwareState.Summary.Current != nil {
			resp.Current.Ready = s.softwareState.Summary.Current.Ready
			resp.Current.Available = s.softwareState.Summary.Current.Available
			resp.Current.Tag = s.softwareState.Summary.Current.Tag
		}

		if s.softwareState.Summary.Target != nil {
			resp.Target.Ready = s.softwareState.Summary.Target.Ready
			resp.Target.Available = s.softwareState.Summary.Target.Available
			resp.Target.Tag = s.softwareState.Summary.Target.Tag
		}

		if s.softwareState.Summary.Previous != nil {
			resp.Previous.Ready = s.softwareState.Summary.Previous.Ready
			resp.Previous.Available = s.softwareState.Summary.Previous.Available
			resp.Previous.Tag = s.softwareState.Summary.Previous.Tag
		}

		resp.ShowSoftwareUpdateToUser = s.showUpdateNode.GetBoolValue()

		resp.HostStates = make([]*frontend.HostSoftwareVersionState, 0)
		for id, hostState := range s.softwareState.Hosts {
			if req.GetHostStates {
				resp.HostStates = append(resp.HostStates, &frontend.HostSoftwareVersionState{
					HostName: s.idToNameMap[id],
					HostId:   id,
					Active:   hostState.Active,
					Updating: hostState.Updating,
					Current: &frontend.SoftwareVersion{
						Ready:     hostState.Current.Ready,
						Available: hostState.Current.Available,
						Tag:       hostState.Current.Tag,
					},
					Target: &frontend.SoftwareVersion{
						Ready:     hostState.Target.Ready,
						Available: hostState.Target.Available,
						Tag:       hostState.Target.Tag,
					},
					Previous: &frontend.SoftwareVersion{
						Ready:     hostState.Previous.Ready,
						Available: hostState.Previous.Available,
						Tag:       hostState.Previous.Tag,
					},
				})
				// if the summary current version is not the same as the host current version, set version mismatch
				if resp.Current.Tag != state.InvalidTag && resp.Current.Tag != "" &&
					hostState.Current.Tag != state.InvalidTag && hostState.Current.Tag != "" &&
					resp.Current.Tag != hostState.Current.Tag {
					resp.VersionMismatch = true
				}
			}
		}
	})
	if len(resp.HostStates) > 0 {
		sort.Slice(resp.HostStates, func(i, j int) bool {
			return resp.HostStates[i].HostId < resp.HostStates[j].HostId
		})
	}
	return resp, nil
}

func (s *SoftwareService) RebootHosts(hosts []uint32) error {
	clients := map[uint32]*software_manager_client.SoftwareManagerClient{}
	for _, client := range s.clients {
		clients[client.PcId] = client.SoftwareManagerClient
	}

	errors := make([]error, 0)
	for _, PcId := range hosts {
		if client, ok := clients[PcId]; ok {
			rebootErr := client.Reboot()
			if rebootErr != nil {
				errors = append(errors, fmt.Errorf("host %v: %v", PcId, rebootErr.Error()))
			}
		}
	}

	if len(errors) > 0 {
		errorString := "Errors Occurred: "
		for _, err := range errors {
			errorString = errorString + err.Error() + ", "
		}
		s.logger.Error(errorString)
		return status.Error(codes.Internal, errorString)
	}

	return nil
}

// TwoPCUpdate performs a two-phase commit update on the given hosts
// First phase: Prepare, lock the hosts for update and preform the necessary checks and state changes to update
// Second phase: Trigger, actually trigger the update by rebooting
// If the first phase fails for any host, the update is aborted on all hosts
// precon: set updating to true
func (s *SoftwareService) TwoPCUpdate(hosts []uint32, newVersion, oldVersion string) error {
	clients := map[uint32]*software_manager_client.SoftwareManagerClient{}
	for _, client := range s.clients {
		clients[client.PcId] = client.SoftwareManagerClient
	}

	sortedHosts := make([]uint32, len(hosts))
	copy(sortedHosts, hosts)

	sort.Slice(sortedHosts, func(i int, j int) bool {
		return sortedHosts[i] > sortedHosts[j] // Inverse sort so host 0 aka command computer gets done last since it coordinates and updating causes reboot
	})

	// 2pc

	reqId := uuid.New().String()

	// Phase 1: Prepare
	errors := make([]error, 0)

	for _, pcId := range sortedHosts {
		if client, ok := clients[pcId]; ok {
			prepErr := client.PrepareUpdate(newVersion, reqId)
			if prepErr != nil {
				errors = append(errors, fmt.Errorf("host %v: %v", pcId, prepErr.Error()))
			}
		}
	}

	if len(errors) > 0 {
		// error occurred, do not pass go, do not collect $200
		errorString := "Errors Occurred: "
		for _, err := range errors {
			errorString = errorString + err.Error() + ", "
		}
		s.logger.Error(errorString)

		// abort phase 1
		for _, pcId := range sortedHosts {
			if client, ok := clients[pcId]; ok {
				err2 := client.AbortUpdate(oldVersion, reqId)
				if err2 != nil {
					s.logger.WithError(err2).Errorf("Error Aborting Update on Host: %v", pcId)
				}
			}
		}

		return status.Error(codes.Internal, errorString)
	}

	// Phase 2: Trigger
	errors = make([]error, 0)
	for _, pcId := range sortedHosts {
		if client, ok := clients[pcId]; ok {
			triggerErr := client.TriggerUpdate(newVersion, reqId)
			if triggerErr != nil {
				errors = append(errors, fmt.Errorf("host %v: %v", pcId, triggerErr.Error()))
			}
		}
	}

	if len(errors) > 0 {
		errorString := "Errors Occurred: "
		for _, err := range errors {
			errorString = errorString + err.Error() + ", "
		}
		s.logger.Error(errorString)
		return status.Error(codes.Internal, errorString)
	}

	return nil
}

func (s *SoftwareService) updateAllToVersion(newVersion, oldVersion string) (err error) {
	if newVersion == "" || oldVersion == "" {
		return status.Error(codes.FailedPrecondition, "no valid versions available")
	}

	s.softwareState.WriteOnCurrent(
		func() {
			if s.softwareState.Updating || s.softwareState.IsHostUpdating() {
				err = status.Error(codes.FailedPrecondition, "Software is currently updating")
			} else {
				s.softwareState.Updating = true
			}
		},
	)

	if err != nil {
		return err
	}

	// no need to set updating to false before returning on the non error case,
	// if the update is successful, command computer is restarted resetting the state
	defer s.softwareState.WriteOnCurrent(func() {
		if err != nil {
			s.softwareState.Updating = false
		}
	})

	// get all the hosts
	hosts := []uint32{}
	for _, client := range s.clients {
		hosts = append(hosts, client.PcId)
	}

	err = s.TwoPCUpdate(hosts, newVersion, oldVersion)
	if err != nil {
		return err
	}

	return nil
}

func (s *SoftwareService) Update(ctx context.Context, _ *frontend.Empty) (*frontend.Empty, error) {
	targetVersion := ""
	currentVersion := ""
	var err error

	s.softwareState.ReadOnCurrent(func() {
		if s.softwareState.Summary == nil {
			err = status.Error(codes.FailedPrecondition, "software state summary is nil")
		} else if !s.softwareState.Summary.Target.IsValid() {
			err = status.Error(codes.FailedPrecondition, "target Version is not Available/Ready for Update")
		} else if !s.softwareState.HostsCurrentVersionIsValid() {
			err = status.Error(codes.FailedPrecondition, "current Version is not Available/Ready for Update")
		} else if s.softwareState.Summary.Target.Tag == s.softwareState.Summary.Current.Tag {
			err = status.Error(codes.FailedPrecondition, "software is already up to date")
		} else {
			targetVersion = s.softwareState.Summary.Target.Tag
			currentVersion = s.softwareState.Summary.Current.Tag
		}
	})

	if err != nil {
		return nil, err
	}

	err = s.updateAllToVersion(targetVersion, currentVersion)
	s.logger.Infof("Update to version %v -> %v, err: %v", currentVersion, targetVersion, err)
	if err != nil {
		return nil, err
	}

	// set previous version to the current version
	// prior to updating previous != current != target
	// after updating previous != current == target, previous == prior current

	err = s.configClient.SetStringValue("common/software_manager/previous_version", currentVersion)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to set previous version: %v", err)
	}

	return &frontend.Empty{}, nil
}

func (s *SoftwareService) Revert(ctx context.Context, _ *frontend.Empty) (*frontend.Empty, error) {
	previousVersion := ""
	currentVersion := ""
	var err error

	s.softwareState.ReadOnCurrent(func() {
		if s.softwareState.Summary == nil {
			err = status.Error(codes.FailedPrecondition, "software state summary is nil")
		} else if !s.softwareState.Summary.Previous.IsValid() {
			err = status.Error(codes.FailedPrecondition, "previous Version is not Available/Ready for Revert")
		} else if !s.softwareState.HostsCurrentVersionIsValid() {
			err = status.Error(codes.FailedPrecondition, "current Version is not Available/Ready for Revert")
		} else if s.softwareState.Summary.Previous.Tag == s.softwareState.Summary.Current.Tag {
			err = status.Error(codes.FailedPrecondition, "software is already reverted")
		} else {
			previousVersion = s.softwareState.Summary.Previous.Tag
			currentVersion = s.softwareState.Summary.Current.Tag
		}
	})

	if err != nil {
		return nil, err
	}

	err = s.updateAllToVersion(previousVersion, currentVersion)
	s.logger.Infof("Revert to version %v -> %v, err: %v", currentVersion, previousVersion, err)
	if err != nil {
		return nil, err
	}

	// Not setting previous version here, we are moving current version from the target version to the previous version
	// so the previous version is already set
	// prior to reverting previous != curret == target
	// after reverting previous == current != target

	return &frontend.Empty{}, nil
}

func (s *SoftwareService) FixVersionMismatch(ctx context.Context, req *frontend.Empty) (res *frontend.Empty, err error) {
	// TODO(jfroel): come up with a less naive implementation that doesn't brick the robot
	return nil, status.Error(codes.Unimplemented, "FixVersionMismatch is permanently disabled")
}

func (s *SoftwareService) UpdateHost(ctx context.Context, req *frontend.UpdateHostRequest) (res *frontend.Empty, err error) {
	// try to update single host to the target version

	hostId := req.HostId

	// translate host string to uint id
	hostName, ok := s.idToNameMap[hostId]
	if !ok {
		return nil, status.Errorf(codes.NotFound, "Host with id %v not found", hostId)
	}

	targetVersion := ""
	currentVersion := ""
	s.softwareState.WriteOnCurrent(func() {
		if s.softwareState.Updating { // don't care if another host is updating, just if a complete software update is happening
			err = status.Error(codes.FailedPrecondition, "Software is currently updating")
		} else {
			hostState, ok := s.softwareState.Hosts[hostId]
			if !ok {
				err = status.Errorf(codes.NotFound, "Host %s not found", hostName)
				return
			} else if hostState.Updating {
				err = status.Errorf(codes.FailedPrecondition, "Host %s is currently updating", hostName)
				return
			} else if hostState.Target.IsValid() &&
				hostState.Current.IsValid() &&
				hostState.Target.Tag != hostState.Current.Tag { // target version is valid, current version is valid, and they are different
				targetVersion = hostState.Target.Tag
				currentVersion = hostState.Current.Tag
			} else {
				err = status.Errorf(codes.FailedPrecondition, "Host %s has no valid update", hostName)
			}
		}
	})

	if err != nil {
		return nil, err
	}

	s.logger.Infof("grepper: Host %v is updating to version %v", hostName, targetVersion)
	err = s.TwoPCUpdate([]uint32{hostId}, targetVersion, currentVersion)
	s.logger.Infof("grepper: Updating host %v version %v -> %v, err: %v", hostName, currentVersion, targetVersion, err)

	if err != nil {
		return nil, err
	}

	return &frontend.Empty{}, nil
}

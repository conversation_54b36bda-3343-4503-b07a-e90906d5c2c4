package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"google.golang.org/grpc"
)

type TractorService struct {
	frontend.UnimplementedTractorServiceServer
	tractorState       *state.TractorIFState
	tractorSafetyState *state.TractorSafetyState
}

func NewTractorService(grpcServer *grpc.Server, tractorState *state.TractorIFState, tractorSafetyState *state.TractorSafetyState) *TractorService {
	service := &TractorService{
		tractorState:       tractorState,
		tractorSafetyState: tractorSafetyState,
	}
	frontend.RegisterTractorServiceServer(grpcServer, service)
	return service
}

// rpc GetNextTractorSafetyState(carbon.frontend.util.Timestamp) returns (GetNextTractorSafetyStateResponse);
func (s TractorService) GetNextTractorIfState(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextTractorIfStateResponse, error) {
	resp := &frontend.GetNextTractorIfStateResponse{}
	s.tractorState.ReadOnNext(ctx, req.GetTimestampMs(), func() {
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.tractorState.GetTimestampMs(),
		}
		resp.State = &frontend.TractorIfState{
			Expected:  s.tractorState.Expected,
			Connected: s.tractorState.Connected,
		}
	})
	return resp, nil
}

func (s TractorService) GetNextTractorSafetyState(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextTractorSafetyStateResponse, error) {
	resp := &frontend.GetNextTractorSafetyStateResponse{}
	s.tractorSafetyState.ReadOnNext(ctx, req.GetTimestampMs(), func() {
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.tractorSafetyState.GetTimestampMs(),
		}
		resp.State = &frontend.TractorSafetyState{
			IsSafe:   s.tractorSafetyState.IsSafe,
			Enforced: s.tractorSafetyState.ActualEnforcementPolicy,
		}
	})
	return resp, nil
}

func (s TractorService) SetEnforcementPolicy(ctx context.Context, req *frontend.SetEnforcementPolicyRequest) (*frontend.SetEnforcementPolicyResponse, error) {
	resp := &frontend.SetEnforcementPolicyResponse{}
	s.tractorSafetyState.SetEnforcementPolicy(req.GetEnforced())
	return resp, nil
}

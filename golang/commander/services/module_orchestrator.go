package services

import (
	"context"
	"fmt"
	"sort"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/module/orchestrator"
	"github.com/carbonrobotics/robot/golang/generated/proto/module/types"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ModuleOrchestratorService struct {
	orchestrator.UnimplementedModuleOrchestratorServiceServer
	state *state.ModuleOrchestratorState
}

func NewModuleOrchestratorService(grpcServer *grpc.Server, state *state.ModuleOrchestratorState) *ModuleOrchestratorService {
	service := &ModuleOrchestratorService{
		state: state,
	}

	orchestrator.RegisterModuleOrchestratorServiceServer(grpcServer, service)

	return service
}

func (s *ModuleOrchestratorService) Heartbeat(ctx context.Context, req *orchestrator.HeartbeatRequest) (*types.Empty, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	if req.Identity == nil {
		return nil, fmt.Errorf("HeartbeatRequest malformed, missing Identity")
	} else if req.ModuleIps == nil {
		return nil, fmt.Errorf("HeartbeatRequest malformed, missing ModuleIps")
	}

	err := s.state.HandleHeartbeat(
		&state.Module{
			ID:     req.GetIdentity().GetId(),
			Serial: req.GetIdentity().GetSerial(),
			MCBIp:  req.GetModuleIps().GetMcbIp(),
			PCIp:   req.GetModuleIps().GetPcIp(),
			IPMIIp: req.GetModuleIps().GetIpmiIp(),
		},
	)
	if err != nil {
		return nil, err
	}
	return &types.Empty{}, nil
}

type ModuleAssignmentService struct {
	frontend.UnimplementedModuleAssignmentServiceServer
	moState    *state.ModuleOrchestratorState
	defState   *state.RobotDefinitionState
	hwmgClient *hardware_manager.HardwareManagerClient
}

func NewModuleAssignmentService(grpcServer *grpc.Server, moState *state.ModuleOrchestratorState, defState *state.RobotDefinitionState, hwmgClient *hardware_manager.HardwareManagerClient) *ModuleAssignmentService {
	service := &ModuleAssignmentService{
		moState:    moState,
		defState:   defState,
		hwmgClient: hwmgClient,
	}

	frontend.RegisterModuleAssignmentServiceServer(grpcServer, service)
	return service
}

func (s *ModuleAssignmentService) GetNextModulesList(ctx context.Context, req *frontend.GetNextModulesListRequest) (*frontend.GetNextModulesListResponse, error) {
	resp := &frontend.GetNextModulesListResponse{}

	result := s.moState.ReadOnNext(ctx,
		req.GetTs().GetTimestampMs(),
		func() {
			resp.AssignedModules = make([]*frontend.ModuleIdentity, 0)
			for _, module := range s.moState.AssignedModules {
				resp.AssignedModules = append(resp.AssignedModules, &frontend.ModuleIdentity{
					Id:     module.ID,
					Serial: module.Serial,
				})
			}
			resp.UnassignedModules = make([]*frontend.ModuleIdentity, 0)
			for _, module := range s.moState.ActiveModules {
				if module.ID == state.UnassignedModuleID {
					resp.UnassignedModules = append(resp.UnassignedModules, &frontend.ModuleIdentity{
						Id:     module.ID,
						Serial: module.Serial,
					})
				}
			}
			resp.UnsetSerialModules = make([]*frontend.ModuleIdentity, 0)
			for _, module := range s.moState.UnsetSerialModules {
				resp.UnsetSerialModules = append(resp.UnsetSerialModules, &frontend.ModuleIdentity{
					Id:     module.ID,
					Serial: module.Serial,
				})
			}
			resp.Ts = &frontend.Timestamp{
				TimestampMs: s.moState.GetTimestampMs(),
			}
		})

	if len(resp.UnassignedModules) != 0 {
		sort.Slice(resp.UnassignedModules, func(a, b int) bool {
			return resp.UnassignedModules[a].GetSerial() < resp.UnassignedModules[b].GetSerial()
		})
	}
	if len(resp.AssignedModules) != 0 {
		sort.Slice(resp.AssignedModules, func(a, b int) bool {
			return resp.AssignedModules[a].GetId() < resp.AssignedModules[b].GetId()
		})
	}

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context cancelled before modules list retrieved")
}

func (s *ModuleAssignmentService) GetNextActiveModules(ctx context.Context, req *frontend.GetNextActiveModulesRequest) (*frontend.GetNextActiveModulesResponse, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	resp := &frontend.GetNextActiveModulesResponse{}
	s.moState.ReadOnNext(ctx,
		req.GetTs().GetTimestampMs(),
		func() {
			resp.ActiveModules = make([]*frontend.ModuleIdentity, 0)
			for _, module := range s.moState.ActiveModules {
				resp.ActiveModules = append(resp.ActiveModules, &frontend.ModuleIdentity{
					Id:     module.ID,
					Serial: module.Serial,
				})
			}
			resp.Ts = &frontend.Timestamp{
				TimestampMs: s.moState.GetTimestampMs(),
			}
		})

	return resp, nil
}

func (s *ModuleAssignmentService) IdentifyModule(ctx context.Context, req *frontend.IdentifyModuleRequest) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	if req.ModuleIdentity == nil {
		return nil, fmt.Errorf("IdentifyModuleRequest malformed, missing ModuleIdentity")
	}

	toIdIp := ""
	turnOffIps := make([]string, 0)

	s.moState.ReadOnCurrent(func() {
		for _, module := range s.moState.ActiveModules {
			if module.Serial == req.GetModuleIdentity().GetSerial() {
				toIdIp = module.MCBIp
			} else {
				turnOffIps = append(turnOffIps, module.MCBIp)
			}
		}
	})

	if toIdIp == "" {
		return nil, status.Errorf(codes.NotFound, "Module %v not found", req.GetModuleIdentity().GetSerial())
	}

	success, err := s.hwmgClient.IdentifyModule(toIdIp, turnOffIps)
	if err != nil {
		return nil, err
	}

	if !success {
		return nil, status.Error(codes.Internal, "Failed to identify module")
	}

	return &frontend.Empty{}, nil
}

func (s *ModuleAssignmentService) AssignModule(ctx context.Context, req *frontend.AssignModuleRequest) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	if req.ModuleIdentity == nil {
		return nil, fmt.Errorf("AssignModuleRequest malformed, missing ModuleIdentity")
	}
	err := s.moState.AssignModule(req.GetModuleIdentity().GetId(), req.GetModuleIdentity().GetSerial())
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *ModuleAssignmentService) ClearModuleAssignment(ctx context.Context, req *frontend.ClearModuleAssignmentRequest) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	if req.ModuleIdentity == nil {
		return nil, fmt.Errorf("ClearModuleAssignmentRequest malformed, missing ModuleIdentity")
	}
	err := s.moState.ClearModuleAssignment(req.GetModuleIdentity().GetSerial())
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *ModuleAssignmentService) SetModuleSerial(ctx context.Context, req *frontend.SetModuleSerialRequest) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	if req.ModuleIdentity == nil {
		return nil, fmt.Errorf("SetModuleSerialRequest malformed, missing ModuleIdentity")
	}
	err := s.moState.SetModuleSerial(req.GetModuleIdentity().GetSerial(), req.GetNewSerial())
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *ModuleAssignmentService) GetPresetsList(ctx context.Context, req *frontend.GetPresetsListRequest) (*frontend.GetPresetsListResponse, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	language := req.GetLanguage()
	resp := &frontend.GetPresetsListResponse{}
	resp.Presets = make([]*frontend.Preset, 0)
	s.defState.ReadOnCurrent(func() {
		for _, presetStorage := range s.defState.Presets {
			p := &frontend.Preset{
				Definition:  presetStorage.Preset.Definition,
				Uuid:        presetStorage.Preset.Uuid,
				DisplayName: presetStorage.Preset.DisplayName, // default to English
			}
			if translation, ok := presetStorage.Translations[language]; ok {
				p.DisplayName = translation
			}
			resp.Presets = append(resp.Presets, p)
		}
	})
	return resp, nil
}

func (s *ModuleAssignmentService) GetCurrentRobotDefinition(ctx context.Context, req *frontend.Empty) (*frontend.GetCurrentRobotDefinitionResponse, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	resp := &frontend.GetCurrentRobotDefinitionResponse{}
	s.defState.ReadOnCurrent(func() {
		if !s.defState.CurrentDefinition.IsValid() {
			// no current definition, definition is optional
			resp.CurrentDefinition = nil
			return
		}
		resp.CurrentDefinition = s.defState.CurrentDefinition.ToProto()
	})
	return resp, nil
}

func (s *ModuleAssignmentService) SetCurrentRobotDefinition(ctx context.Context, req *frontend.SetCurrentRobotDefinitionRequest) (*frontend.Empty, error) {
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	err := s.defState.SetCurrentRobotDefinition(req.GetCurrentDefinition())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to set current robot definition: %v", err)
	}
	return &frontend.Empty{}, nil
}

package services

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type FocusService struct {
	frontend.UnimplementedFocusServiceServer
	camState   *state.OverallCameraState
	camWatcher *state.CameraWatcher
	rows       map[int]*rows.RowClients
}

func NewFocusService(grpcServer *grpc.Server, camState *state.OverallCameraState, camWatcher *state.CameraWatcher, rowClients map[int]*rows.RowClients) *FocusService {
	service := &FocusService{
		camState:   camState,
		camWatcher: camWatcher,
		rows:       rowClients,
	}
	frontend.RegisterFocusServiceServer(grpcServer, service)
	return service
}

func (s *FocusService) TogglePredictGridView(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Toggle Grid View")

	// TODO Do this proper through config sent to streamers
	// Temporarily done through state directly for testing
	s.camState.WriteOnCurrent(func() {
		s.camState.GridViewEnabled = !s.camState.GridViewEnabled
	})

	s.camWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *FocusService) GetNextFocusState(ctx context.Context, req *frontend.FocusStateRequest) (*frontend.FocusState, error) {
	var ts int64 = req.Ts.GetTimestampMs()
	resp := &frontend.FocusState{}
	var notFound bool = false
	result := s.camState.ReadOnNext(ctx, ts, func() {
		resp.GridViewEnabled = s.camState.GridViewEnabled
		resp.GlobalFocusProgressPct = s.camState.GlobalFocusProgressPct
		resp.FocusInProgress = s.camState.FocusInProgress

		if cam, ok := s.camState.Cameras[req.CamId]; ok {
			switch cam.Type {
			case state.CameraTypePredict:
				resp.TypeState = &frontend.FocusState_Predict{}
			case state.CameraTypeTarget:
				resp.TypeState = &frontend.FocusState_Target{
					Target: &frontend.TargetFocusState{
						LiquidLensValue:  cam.Focus.LensValue,
						FocusProgressPct: cam.Focus.FocusProgressPct,
						MinLensValue:     cam.Focus.MinLensValue,
						MaxLensValue:     cam.Focus.MaxLensValue,
						FocusInProgress:  cam.Focus.FocusInProgress,
					},
				}
			default:
				notFound = true
			}
		} else {
			notFound = true
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.camState.GetTimestampMs(),
		}
	})

	if notFound {
		return nil, status.Error(codes.NotFound, "Camera Not Found")
	}

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Velocity Retrieved")
}

func (s *FocusService) StartAutoFocusSpecific(ctx context.Context, req *frontend.CameraRequest) (*frontend.Empty, error) {
	logrus.Infof("Start Auto Focus: %s", req.CamId)

	camera, ok := s.camState.Cameras[req.CamId]
	if !ok {
		logrus.Errorf("Camera Not Found %v", req.CamId)
		return nil, status.Error(codes.NotFound, "Camera Not Found")
	}
	rowId := camera.RowNumber
	scannerId := camera.CameraIdx + 1
	err := s.rows[int(rowId)].AimbotClient.LensAutoFocus(uint32(scannerId))
	if err != nil {
		logrus.Errorf("Failed to start auto focus for scanner %v: %v", scannerId, err)
		return nil, err
	}

	s.camWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *FocusService) runAllInParallel(cameras []*state.Camera, fn func(rowId uint32, scannerId uint32) error) error {
	wg := sync.WaitGroup{}
	errs := make([]error, len(cameras))
	for i, camera := range cameras {
		wg.Add(1)
		go func(_i int, _camera *state.Camera) {
			defer wg.Done()
			rowId := _camera.RowNumber
			scannerId := _camera.CameraIdx + 1
			err := fn(rowId, scannerId)
			if err != nil {
				errs[_i] = fmt.Errorf("failed to start auto focus for target cam %v_%v: %v", rowId, scannerId, err)
			}
		}(i, camera)
	}
	wg.Wait()

	return errors.Join(errs...)
}

func (s *FocusService) lensAutoFocusWrapper(rowId uint32, scannerId uint32) error {
	return s.rows[int(rowId)].AimbotClient.LensAutoFocus(scannerId)
}

func (s *FocusService) stopLensAutoFocusWrapper(rowId uint32, scannerId uint32) error {
	return s.rows[int(rowId)].AimbotClient.StopLensAutoFocus(scannerId)
}

func (s *FocusService) StartAutoFocusAll(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Start Auto Focus All")
	cameras := make([]*state.Camera, 0) // the state only changes the camera pointers not the actual camera objects so pointers are safe
	s.camState.ReadOnCurrent(func() {
		for _, camera := range s.camState.Cameras {
			if camera.AutoFocusable {
				cameras = append(cameras, camera)
			}
		}
	})

	if len(cameras) == 0 {
		logrus.Errorf("No Cameras Found")
		return nil, status.Error(codes.NotFound, "No Cameras Found")
	}

	err := s.runAllInParallel(cameras, s.lensAutoFocusWrapper)
	if err != nil {
		logrus.Errorf("Failed to start auto focus for all cameras: %v", err)
		return nil, err
	}

	s.camWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *FocusService) StopAutoFocus(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Stop Auto Focus")

	cameras := make([]*state.Camera, 0) // the state only changes the camera pointers not the actual camera objects so pointers are safe
	s.camState.ReadOnCurrent(func() {
		for _, camera := range s.camState.Cameras {
			if camera.AutoFocusable {
				cameras = append(cameras, camera)
			}
		}
	})
	if len(cameras) == 0 {
		logrus.Errorf("No Cameras Found")
		return nil, status.Error(codes.NotFound, "No Cameras Found")
	}

	err := s.runAllInParallel(cameras, s.stopLensAutoFocusWrapper)
	if err != nil {
		logrus.Errorf("Failed to stop auto focus for all cameras: %v", err)
		return nil, err
	}

	s.camWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *FocusService) SetLensValue(ctx context.Context, req *frontend.LensSetRequest) (*frontend.Empty, error) {
	logrus.Infof("Set Lens Value: %s, %d", req.CamId, req.LensValue)

	var camera *state.Camera
	var err error
	s.camState.ReadOnCurrent(func() {
		var ok bool
		camera, ok = s.camState.Cameras[req.CamId]
		if !ok {
			err = status.Error(codes.NotFound, "Camera Not Found")
			return
		}
	})

	if err != nil {
		logrus.Errorf("Camera Not Found %v", req.CamId)
		return nil, err
	}

	rowId := camera.RowNumber
	scannerId := camera.CameraIdx + 1
	err = s.rows[int(rowId)].AimbotClient.LensSet(uint32(scannerId), req.LensValue)
	if err != nil {
		logrus.Errorf("Failed to start auto focus for scanner %v: %v", scannerId, err)
		return nil, err
	}

	s.camWatcher.Trigger()

	return &frontend.Empty{}, nil
}

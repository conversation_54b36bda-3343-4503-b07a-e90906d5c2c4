package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
)

const upgradeBandingUUIDKey = "banding/upgrade_uuid"
const bandingDefsByNameKey = "banding/banding_defs"

type VisualizationClass int32

const (
	VisualizationClass_BeingShot VisualizationClass = 0
	VisualizationClass_Shot      VisualizationClass = 1
	VisualizationClass_Error     VisualizationClass = 2
	VisualizationClass_Ignored   VisualizationClass = 3
	VisualizationClass_Targeted  VisualizationClass = 4
	VisualizationClass_Kept      VisualizationClass = 5
	VisualizationClass_Duplicate VisualizationClass = 6
)

type TargetingState interface {
	ThinningEnabled(row int) bool
}

type BandingService struct {
	frontend.UnimplementedBandingServiceServer
	redisClient    *redis.Client
	rows           map[int]*rows.RowClients
	bState         *state.BandingState
	bvState        *state.BandingVisualizationState
	jobsState      *state.JobsState
	env            *environment.Robot
	config         *config.ConfigClient
	profileSync    *state.ProfileSyncManager
	weedFilters    map[frontend.VisualizationTypeToInclude]VisualizationClass
	cropFilters    map[frontend.VisualizationTypeToInclude]VisualizationClass
	targetingState TargetingState
}

func NewBandingService(grpcServer *grpc.Server, redisClient *redis.Client, rowClients map[int]*rows.RowClients, bState *state.BandingState, bvState *state.BandingVisualizationState, jobsState *state.JobsState, env *environment.Robot, config *config.ConfigClient, profileSync *state.ProfileSyncManager, targetingState TargetingState) *BandingService {
	service := &BandingService{
		redisClient: redisClient,
		rows:        rowClients,
		bState:      bState,
		bvState:     bvState,
		jobsState:   jobsState,
		env:         env,
		config:      config,
		profileSync: profileSync,
		weedFilters: map[frontend.VisualizationTypeToInclude]VisualizationClass{
			frontend.VisualizationTypeToInclude_DUPLICATE_WEED: VisualizationClass_Duplicate,
			frontend.VisualizationTypeToInclude_KILLED_WEED:    VisualizationClass_Shot,
			frontend.VisualizationTypeToInclude_KILLING_WEED:   VisualizationClass_BeingShot,
			frontend.VisualizationTypeToInclude_IGNORED_WEED:   VisualizationClass_Ignored,
			frontend.VisualizationTypeToInclude_ERROR_WEED:     VisualizationClass_Error,
			frontend.VisualizationTypeToInclude_WEED:           VisualizationClass_Targeted,
		},
		cropFilters: map[frontend.VisualizationTypeToInclude]VisualizationClass{
			frontend.VisualizationTypeToInclude_DUPLICATE_CROP: VisualizationClass_Duplicate,
			frontend.VisualizationTypeToInclude_KILLED_CROP:    VisualizationClass_Shot,
			frontend.VisualizationTypeToInclude_KILLING_CROP:   VisualizationClass_BeingShot,
			frontend.VisualizationTypeToInclude_ERROR_CROP:     VisualizationClass_Error,
			frontend.VisualizationTypeToInclude_IGNORED_CROP:   VisualizationClass_Ignored,
			frontend.VisualizationTypeToInclude_CROP:           VisualizationClass_Targeted,
			frontend.VisualizationTypeToInclude_CROP_KEPT:      VisualizationClass_Kept,
		},
		targetingState: targetingState,
	}
	frontend.RegisterBandingServiceServer(grpcServer, service)
	return service
}

func (b *BandingService) SaveBandingDef(ctx context.Context, req *frontend.SaveBandingDefRequest) (*frontend.Empty, error) {
	logrus.Infof("Banding: received def=%v to save", req.BandingDef)
	id := req.BandingDef.Uuid
	if id == "" {
		existing, err := b.bState.FindBandingDefByName(req.BandingDef.Name)
		if err == nil {
			id = existing.Uuid
		} else {
			id = uuid.New().String()
		}
		req.BandingDef.Uuid = id
	}

	err := b.bState.SaveBandingDef(req.BandingDef, req.SetActive)
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (b *BandingService) LoadBandingDefs(ctx context.Context, e *frontend.Empty) (*frontend.LoadBandingDefsResponse, error) {
	uuid, name := b.bState.GetActiveBandingDef()

	all, err := b.bState.LoadBandingDefs()
	if err != nil {
		return nil, err
	}

	resp := &frontend.LoadBandingDefsResponse{
		BandingDefs:   all,
		ActiveDef:     name,
		ActiveDefUUID: uuid,
	}

	return resp, nil
}

func (b *BandingService) DeleteBandingDef(ctx context.Context, req *frontend.DeleteBandingDefRequest) (*frontend.Empty, error) {
	uuid := req.Uuid
	if uuid == "" {
		def, err := b.bState.FindBandingDefByName(req.Name)
		if err != nil {
			return nil, err
		}
		uuid = def.Uuid
	}

	exists, err := b.redisClient.HExists(state.BandingDefsByUUIDKey, uuid)
	if err != nil {
		return nil, err
	}

	if !exists {
		return nil, errors.New("Banding def " + uuid + " not found")
	}

	err = b.bState.DeleteBandingDef(uuid)
	if err != nil {
		return nil, err
	}

	return &frontend.Empty{}, nil
}

func (b *BandingService) SetActiveBandingDef(ctx context.Context, req *frontend.SetActiveBandingDefRequest) (*frontend.Empty, error) {
	uuid := req.Uuid
	name := ""
	if uuid == "" {
		def, err := b.bState.FindBandingDefByName(req.Name)
		if err != nil {
			return nil, err
		}
		uuid = def.Uuid
		name = def.Name
	}

	exists, err := b.redisClient.HExists(state.BandingDefsByUUIDKey, uuid)
	if err != nil {
		return nil, err
	}

	if !exists {
		return nil, errors.New("Banding def " + uuid + " not found")
	}

	if name == "" {
		def, err := b.bState.LoadBandingDef(uuid)
		if err != nil {
			return nil, err
		}
		name = def.Name
	}

	err = b.bState.SetActiveBandingDef(uuid, name)
	if err != nil {
		return nil, err
	}

	b.bState.NotifyOnBandingChange()

	return &frontend.Empty{}, nil
}

func (b *BandingService) GetActiveBandingDef(ctx context.Context, e *frontend.Empty) (*frontend.GetActiveBandingDefResponse, error) {
	uuid, name := b.bState.GetActiveBandingDef()
	return &frontend.GetActiveBandingDefResponse{Name: name, Uuid: uuid}, nil
}

func (b *BandingService) GetNextVisualizationData(ctx context.Context, req *frontend.GetNextVisualizationDataRequest) (*frontend.GetNextVisualizationDataResponse, error) {
	resp := &frontend.GetNextVisualizationDataResponse{
		Data: make([]*frontend.VisualizationData, 0),
	}
	b.bvState.RowState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		data := b.bvState.RowState.Rows[int(req.RowId)]
		if data != nil && data.Dets != nil {
			if data.Dets.Trajectories != nil {
				for _, d := range data.Dets.Trajectories {
					det := &frontend.VisualizationData{
						XMm:    int32(d.XMm),
						YMm:    int32(d.YMm),
						ZMm:    int32(d.ZMm),
						IsWeed: d.IsWeed,
					}
					resp.Data = append(resp.Data, det)
				}
			}
			if data.Dets.Bands != nil {
				for _, b := range data.Dets.Bands {
					band := &weed_tracking.BandDefinition{
						OffsetMm: b.OffsetMm,
						WidthMm:  b.WidthMm,
						Id:       b.Id,
					}
					resp.Bands = append(resp.Bands, band)
				}
			}
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: b.bvState.RowState.GetTimestampMs(),
		}
	})

	b.bvState.ReqState.WriteOnCurrent(func() {
		b.bvState.ReqState.LastReqTsSec = time.Now().Unix()
	})

	return resp, nil
}

func isTrajIgnored(traj *weed_tracking.TrajectorySnapshot) bool {
	ignoredStates := map[weed_tracking.TargetableState]bool{
		weed_tracking.TargetableState_TARGET_INTERSECTS_NON_SHOOTABLE: true,
		weed_tracking.TargetableState_TARGET_DOO_TOO_LOW:              true,
		weed_tracking.TargetableState_TARGET_IGNORED_FROM_ALMANAC:     true,
		weed_tracking.TargetableState_TARGET_OUT_OF_BAND:              true,
		weed_tracking.TargetableState_TARGET_AVOID_FROM_ALMANAC:       true,
	}
	if _, ok := ignoredStates[traj.ScoreState.TargetState]; ok {
		return true
	}

	if (traj.IsWeed && !traj.Thresholds.PassedWeeding) || (!traj.IsWeed && (!traj.Thresholds.PassedThinning || !traj.MarkedForThinning)) {
		return true
	}

	return false
}

func isTrajErrored(traj *weed_tracking.TrajectorySnapshot) bool {
	erroredStatuses := map[weed_tracking.KillStatus]bool{
		weed_tracking.KillStatus_STATUS_PARTIALLY_SHOT:      true,
		weed_tracking.KillStatus_STATUS_P2P_NOT_FOUND:       true,
		weed_tracking.KillStatus_STATUS_ERROR:               true,
		weed_tracking.KillStatus_STATUS_P2P_MISSING_CONTEXT: true,
	}
	if _, ok := erroredStatuses[traj.GetKillStatus()]; ok {
		return true
	}

	scoreState := traj.GetScoreState()
	if scoreState != nil && scoreState.GetTargetState() == weed_tracking.TargetableState_TARGET_TOO_MANY_FAILURES {
		return true
	}

	return false
}

type CategorizedTraj struct {
	traj     *weed_tracking.TrajectorySnapshot
	vizClass VisualizationClass
}

func (b *BandingService) isTrajIncluded(cTraj *CategorizedTraj, filter frontend.VisualizationTypeToInclude) bool {
	var expectedClass = VisualizationClass_Targeted
	if val, ok := b.weedFilters[filter]; ok {
		if !cTraj.traj.IsWeed {
			return false
		}
		expectedClass = val
	} else if val, ok := b.cropFilters[filter]; ok {
		if cTraj.traj.IsWeed {
			return false
		}
		expectedClass = val
	} else {
		return false
	}

	if cTraj.vizClass == expectedClass {
		return true
	}

	return false
}

func categorizeTraj(traj *weed_tracking.TrajectorySnapshot, thinningEnabled bool) *CategorizedTraj {
	vizClass := VisualizationClass_BeingShot
	if traj.DuplicateStatus == weed_tracking.DuplicateStatus_DUPLICATE {
		vizClass = VisualizationClass_Duplicate
	} else if traj.KillStatus == weed_tracking.KillStatus_STATUS_BEING_SHOT {
		vizClass = VisualizationClass_BeingShot
	} else if traj.KillStatus == weed_tracking.KillStatus_STATUS_SHOT {
		vizClass = VisualizationClass_Shot
	} else if isTrajErrored(traj) {
		vizClass = VisualizationClass_Error
	} else if !traj.IsWeed {
		if traj.ThinningState == weed_tracking.ThinningState_THINNING_MARKED_FOR_THINNING {
			vizClass = VisualizationClass_Targeted
		} else if traj.ThinningState == weed_tracking.ThinningState_THINNING_KEPT {
			vizClass = VisualizationClass_Kept
		} else if traj.ThinningState == weed_tracking.ThinningState_THINNING_UNSET && thinningEnabled {
			vizClass = VisualizationClass_Targeted // Using internal type targeted here since all crops are "targeted" when thinnning until we decide keep/kill/ignore
		} else {
			vizClass = VisualizationClass_Ignored
		}
	} else {
		if isTrajIgnored(traj) {
			vizClass = VisualizationClass_Ignored
		} else {
			vizClass = VisualizationClass_Targeted
		}
	}

	return &CategorizedTraj{
		traj:     traj,
		vizClass: vizClass,
	}
}

func (b *BandingService) filterTrajs(trajectories []*weed_tracking.TrajectorySnapshot, typeFilters []frontend.VisualizationTypeToInclude, thresholdFilters *frontend.ThresholdFilters, rowId int) []*weed_tracking.TrajectorySnapshot {
	return b.filterTrajsByType(b.filterTrajsByThreshold(trajectories, thresholdFilters), typeFilters, rowId)
}
func failsThresholdFilter(state frontend.ThresholdState, passedThreshold bool) bool {
	return (!passedThreshold && state == frontend.ThresholdState_PASS) || (passedThreshold && state == frontend.ThresholdState_FAIL)
}
func (b *BandingService) filterTrajsByThreshold(trajectories []*weed_tracking.TrajectorySnapshot, thresholdFilters *frontend.ThresholdFilters) []*weed_tracking.TrajectorySnapshot {
	filteredTrajectories := []*weed_tracking.TrajectorySnapshot{}
	for _, traj := range trajectories {
		thresholdFilter := thresholdFilters.GetCrop()
		if traj.GetIsWeed() {
			thresholdFilter = thresholdFilters.GetWeed()
		}
		if failsThresholdFilter(thresholdFilter.GetWeeding(), traj.GetThresholds().GetPassedWeeding()) {
			continue
		}
		if failsThresholdFilter(thresholdFilter.GetThinning(), traj.GetThresholds().GetPassedThinning()) {
			continue
		}
		if failsThresholdFilter(thresholdFilter.GetBanding(), traj.GetThresholds().GetPassedBanding()) {
			continue
		}
		filteredTrajectories = append(filteredTrajectories, traj)
	}
	return filteredTrajectories
}
func (b *BandingService) filterTrajsByType(trajectories []*weed_tracking.TrajectorySnapshot, typeFilters []frontend.VisualizationTypeToInclude, rowId int) []*weed_tracking.TrajectorySnapshot {
	if len(typeFilters) == 0 {
		return trajectories
	}

	catTrajs := []*CategorizedTraj{}
	filteredTrajectories := []*weed_tracking.TrajectorySnapshot{}
	thinningEnabled := b.targetingState.ThinningEnabled(rowId)

	for _, traj := range trajectories {
		catTrajs = append(catTrajs, categorizeTraj(traj, thinningEnabled))
	}

	nextRemainingTrajectories := catTrajs

	for _, filter := range typeFilters {
		remainingTrajectories := nextRemainingTrajectories
		nextRemainingTrajectories = []*CategorizedTraj{}
		for _, cTraj := range remainingTrajectories {
			if b.isTrajIncluded(cTraj, filter) {
				filteredTrajectories = append(filteredTrajectories, cTraj.traj)
			} else {
				nextRemainingTrajectories = append(remainingTrajectories, cTraj)
			}
		}
	}

	logrus.Debugf("Input: %v Output: %v", len(trajectories), len(filteredTrajectories))

	return filteredTrajectories
}

func (b *BandingService) GetNextVisualizationData2(ctx context.Context, req *frontend.GetNextVisualizationDataRequest) (*frontend.GetNextVisualizationData2Response, error) {
	resp := &frontend.GetNextVisualizationData2Response{}
	b.bvState.RowState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		data := b.bvState.RowState.Rows[int(req.RowId)]
		if data != nil {
			resp.Data = proto.Clone(data.Dets).(*weed_tracking.DiagnosticsSnapshot)
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: b.bvState.RowState.GetTimestampMs(),
		}
	})

	if resp.Data != nil {
		resp.Data.Trajectories = b.filterTrajs(resp.Data.Trajectories, req.GetTypesToInclude(), req.GetThresholdFilters(), int(req.RowId))
	}

	b.bvState.ReqState.WriteOnCurrent(func() {
		b.bvState.ReqState.LastReqTsSec = time.Now().Unix()
	})

	return resp, nil
}

func (b *BandingService) GetNextVisualizationDataForAllRows(ctx context.Context, req *frontend.GetNextVisualizationDataForAllRowsRequest) (*frontend.GetNextVisualizationDataForAllRowsResponse, error) {
	resp := &frontend.GetNextVisualizationDataForAllRowsResponse{DataPerRow: make(map[int32]*weed_tracking.DiagnosticsSnapshot)}
	b.bvState.RowState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		for row_idx, row := range b.bvState.RowState.Rows {
			if row != nil {
				resp.DataPerRow[int32(row_idx)] = proto.Clone(row.Dets).(*weed_tracking.DiagnosticsSnapshot)
			}
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: b.bvState.RowState.GetTimestampMs(),
		}
	})

	for key := range resp.DataPerRow {
		resp.DataPerRow[key].Trajectories = b.filterTrajs(resp.DataPerRow[key].Trajectories, req.GetTypesToInclude(), req.GetThresholdFilters(), int(key))
	}

	b.bvState.ReqState.WriteOnCurrent(func() {
		b.bvState.ReqState.LastReqTsSec = time.Now().Unix()
	})

	return resp, nil
}

func (b *BandingService) GetDimensions(ctx context.Context, req *frontend.GetDimensionsRequest) (*aimbot.GetDimensionsResponse, error) {
	return b.GetRowDimensions(int(req.RowId))
}

func (b *BandingService) GetRowDimensions(rowId int) (*aimbot.GetDimensionsResponse, error) {
	var dims *aimbot.GetDimensionsResponse
	b.bvState.DimensionsState.ReadOnCurrent(func() {
		dims = b.bvState.DimensionsState.Dimensions[rowId]
	})

	if dims != nil {
		return dims, nil
	}

	row, ok := b.rows[rowId]
	if !ok {
		return nil, errors.New("Row not found")
	}

	var err error
	dims, err = row.AimbotClient.GetDimensions()
	if err != nil {
		return nil, fmt.Errorf("No dimensions data, aimbot not booted yet, err=%v", err)
	} else {
		b.bvState.DimensionsState.WriteOnCurrent(func() {
			b.bvState.DimensionsState.Dimensions[rowId] = dims
		})
	}

	return dims, nil
}

func (b *BandingService) SetBandingEnabled(ctx context.Context, req *frontend.SetBandingEnabledRequest) (*frontend.SetBandingEnabledResponse, error) {
	err := b.bState.SetIsBandingEnabled(ctx, req.GetEnabled())
	if err != nil {
		return nil, err
	}
	return &frontend.SetBandingEnabledResponse{}, nil
}

func (b *BandingService) IsBandingEnabled(ctx context.Context, req *frontend.Empty) (*frontend.IsBandingEnabledResponse, error) {
	en := b.bState.GetIsBandingEabled()
	return &frontend.IsBandingEnabledResponse{Enabled: en}, nil
}

func (b *BandingService) SetDynamicBandingEnabled(ctx context.Context, req *frontend.SetBandingEnabledRequest) (*frontend.SetBandingEnabledResponse, error) {
	err := b.bState.SetIsDynamicBandingEnabled(ctx, req.GetEnabled())
	if err != nil {
		return nil, err
	}
	return &frontend.SetBandingEnabledResponse{}, nil
}

func (b *BandingService) IsDynamicBandingEnabled(ctx context.Context, req *frontend.Empty) (*frontend.IsBandingEnabledResponse, error) {
	en := b.bState.GetIsDynamicBandingEnabled()
	return &frontend.IsBandingEnabledResponse{Enabled: en}, nil
}

func (b *BandingService) GetNextBandingState(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextBandingStateResponse, error) {
	resp, err := b.bState.GetNextBandingState(ctx, req.GetTimestampMs())
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (b *BandingService) GetVisualizationMetadata(context.Context, *frontend.Empty) (*frontend.GetVisualizationMetadataResponse, error) {
	resp := &frontend.GetVisualizationMetadataResponse{
		CropSafetyRadiusMmPerRow: make(map[int32]float32),
	}
	for r := range b.rows {
		topElement := fmt.Sprintf("row%v", r)
		if environment.CarbonGen(b.env.MakaGen) == environment.CarbonGenBud {
			topElement = "bud"
		}

		node, err := b.config.GetNode(topElement + "/aimbot/weed_tracking/crop_safety_radius_mm")
		if err != nil {
			return nil, err
		}
		resp.CropSafetyRadiusMmPerRow[int32(r)] = float32(node.Node.Value.GetFloatVal())
	}
	return resp, nil
}

func (b *BandingService) UpgradeBandingToUUIDs() error {
	done, err := b.redisClient.ReadString(upgradeBandingUUIDKey, "0")
	if err != nil {
		return err
	}
	if done == "1" {
		return nil
	}
	logrus.Info("Banding: running uuid upgrade")
	oldAll, err := b.redisClient.HGetAll(bandingDefsByNameKey)
	if err != nil {
		logrus.WithError(err).Error("Banding: could not read old banding defs")
		return err
	}
	oldActiveName, err := b.redisClient.ReadString(state.ActiveDefByNameKey, "")
	if err != nil {
		logrus.WithError(err).Error("Banding: could not read old active def")
		return err
	}
	newActiveUUID := ""
	for _, old := range oldAll {
		var def frontend.BandingDef
		err = json.Unmarshal([]byte(old), &def)
		if err != nil {
			logrus.WithError(err).Errorf("Banding: Could not unmarshal banding_def: %v", old)
			return err
		}
		def.Uuid = uuid.New().String()
		j, err := json.Marshal(&def)
		if err != nil {
			logrus.WithError(err).Error("Banding: Could not marshal banding_def to json")
			return err
		}
		err = b.redisClient.HSet(state.BandingDefsByUUIDKey, def.Uuid, string(j))
		if err != nil {
			logrus.WithError(err).Error("Banding: could not write new banding def")
			return err
		}
		err = b.profileSync.ProfileUpdated(def.Uuid, frontend.ProfileType_BANDING, false)
		if err != nil {
			logrus.WithError(err).Error("Banding: could not update profile for sync")
			return err
		}
		if def.Name == oldActiveName {
			newActiveUUID = def.Uuid
		}
	}
	b.bState.SetActiveBandingDef(newActiveUUID, oldActiveName)
	if err != nil {
		logrus.WithError(err).Error("Banding: could not write new active def")
		return err
	}
	b.bState.NotifyOnBandingChange()
	err = b.redisClient.WriteString(upgradeBandingUUIDKey, "1")
	if err != nil {
		logrus.WithError(err).Error("Banding: could not write upgrade completed flag")
		return err
	}
	return nil
}

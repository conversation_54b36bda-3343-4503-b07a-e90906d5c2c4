package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type ActuationTasksService struct {
	frontend.UnimplementedActuationTasksServiceServer
	stopCtx                     context.Context
	opState                     *state.OperationsState
	configClient                *config.ConfigClient
	rows                        map[int]*rows.RowClients
	robot                       environment.Robot
	lastStartTimestampMs        int64
	lastStartExpectedDurationMs int64
	sync.RWMutex
}

func NewActuationTasksService(grpcServer *grpc.Server, stopCtx context.Context, opState *state.OperationsState, rows map[int]*rows.RowClients, configClient *config.ConfigClient, robot environment.Robot) *ActuationTasksService {
	service := &ActuationTasksService{
		stopCtx:      stopCtx,
		opState:      opState,
		rows:         rows,
		configClient: configClient,
		robot:        robot,
	}
	frontend.RegisterActuationTasksServiceServer(grpcServer, service)
	return service
}

func (s *ActuationTasksService) GetNextGlobalActuationTaskState(ctx context.Context, req *frontend.Timestamp) (*frontend.GlobalActuationTaskState, error) {
	var ts int64 = req.GetTimestampMs()

	var running bool = false
	s.opState.ReadOnNext(ctx, ts, func() {
		for _, rowWeedingState := range s.opState.RowWeeding {
			if rowWeedingState.ActuationTasksRunning {
				running = true
			}
		}
	})

	var elapsedTimeMs int64 = 0
	var expectedTimeMs int64 = 0

	func() {
		s.RLock()
		defer s.RUnlock()

		elapsedTimeMs = time.Now().UnixMilli() - s.lastStartTimestampMs
		expectedTimeMs = s.lastStartExpectedDurationMs

	}()

	return &frontend.GlobalActuationTaskState{
		Running:        running,
		ElapsedTimeMs:  uint32(elapsedTimeMs),
		ExpectedTimeMs: uint32(expectedTimeMs),
	}, nil

}

func (s *ActuationTasksService) StartGlobalAimbotActuationTask(ctx context.Context, req *frontend.GlobalAimbotActuationTaskRequest) (*frontend.Empty, error) {
	logrus.Infof("Starting Actuation Task to row: %v of type: %v", req.RowId, req.Task)

	var durationMs int64 = 0

	if req.Task != nil {
		switch req.Task.Task.(type) {
		case *aimbot.ActuationTaskRequest_LaserTest:
			lt := req.Task.GetLaserTest()
			if lt == nil {
				return nil, fmt.Errorf("invalid Laser Test Message is nil")
			}
			durationMs = int64(lt.DurationMs)
		case *aimbot.ActuationTaskRequest_ImageDraw:
			durationMs = 300000 // Just kinda guessing here for now, not important for this case
		case *aimbot.ActuationTaskRequest_RangeDraw:
			lt := req.Task.GetRangeDraw()
			if lt == nil {
				return nil, fmt.Errorf("invalid Range Draw Message is nil")
			}
			durationMs = int64(lt.DurationS * 1000)
		}
	}

	func() {
		s.Lock()
		defer s.Unlock()

		s.lastStartTimestampMs = time.Now().UnixMilli()
		s.lastStartExpectedDurationMs = durationMs

	}()

	if req.RowId == 0 {
		for _, rowClients := range s.rows {
			err := rowClients.AimbotClient.StartActuationTask(req.Task)
			if err != nil {
				return nil, err
			}
		}
	} else if rowClient, exists := s.rows[int(req.RowId)]; exists {
		err := rowClient.AimbotClient.StartActuationTask(req.Task)
		if err != nil {
			return nil, err
		}
	} else {
		return nil, fmt.Errorf("couldn't find RowId %v", req.RowId)
	}

	return &frontend.Empty{}, nil
}

func (s *ActuationTasksService) CancelGlobalAimbotActuationTask(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Info("Cancelling Actuaction Task")

	for _, rowClients := range s.rows {
		err := rowClients.AimbotClient.CancelActuationTask()
		if err != nil {
			return nil, err
		}
	}

	return &frontend.Empty{}, nil
}

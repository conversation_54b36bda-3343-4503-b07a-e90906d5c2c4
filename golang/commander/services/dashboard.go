package services

import (
	"context"
	"errors"
	"sort"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type VelocityProviderIF interface {
	GetNextVelocity(ts int64, ctx context.Context, resp *frontend.WeedingVelocity) bool
}

type DashboardService struct {
	frontend.UnimplementedDashboardServiceServer
	opState              *state.OperationsState
	velState             VelocityProviderIF
	weedingEnforcer      *state.WeedingEnforcer
	selectedModelWatcher *state.SelectedModelWatcher
	configClient         *config.ConfigClient
	configNode           *config.ConfigTree
	configNodeCommon     *config.ConfigTree
	modelState           *state.ModelManagerState
	tractorSafetyState   *state.TractorSafetyState
	hwClient             *hardware_manager.HardwareManagerClient
}

func NewDashboardService(grpcServer *grpc.Server, opState *state.OperationsState, velState VelocityProviderIF, weedingEnforcer *state.WeedingEnforcer, selectedModelWatcher *state.SelectedModelWatcher, configClient *config.ConfigClient, configNode *config.ConfigTree, configNodeCommon *config.ConfigTree, modelState *state.ModelManagerState, tractorSafetyState *state.TractorSafetyState, hwClient *hardware_manager.HardwareManagerClient) *DashboardService {
	service := &DashboardService{
		opState:              opState,
		velState:             velState,
		weedingEnforcer:      weedingEnforcer,
		selectedModelWatcher: selectedModelWatcher,
		configClient:         configClient,
		configNode:           configNode,
		configNodeCommon:     configNodeCommon,
		modelState:           modelState,
		hwClient:             hwClient,
		tractorSafetyState:   tractorSafetyState,
	}
	frontend.RegisterDashboardServiceServer(grpcServer, service)
	return service
}

func (s *DashboardService) ToggleRow(ctx context.Context, req *frontend.RowId) (*frontend.Empty, error) {
	logrus.Warnf("Toggle Row is depricated use set target mode")

	weeding := false
	s.opState.ReadOnCurrent(func() {
		weeding = !s.opState.ExpectedTargeting.WeedState.Enabled
	})
	s.opState.WriteOnCurrent(func() {
		s.opState.ExpectedTargeting.WeedState.Enabled = weeding
		s.opState.ExpectedTargeting.ThinningState.Enabled = false
	})
	s.weedingEnforcer.Trigger()
	return &frontend.Empty{}, nil
}
func (s *DashboardService) ToggleLasers(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Toggle Lasers Called")
	return nil, status.Error(codes.Unimplemented, "Laser arming is no longer supported.")
}

func (s *DashboardService) GetCropModelOptions(context.Context, *frontend.Empty) (*frontend.CropModelOptions, error) {
	logrus.Infof("Get CropEnvironment Model")

	var crops []veselka.CropEnvironment
	s.modelState.ReadOnCurrent(func() {
		crops = s.modelState.CropEnvironments
	})
	preferredCrops := map[string]bool{}
	for _, cropNode := range s.configNode.GetNode("preferred_crops").GetChildrenNodes() {
		preferredCrops[cropNode.GetName()] = true
	}

	models := make([]*frontend.CropModel, 0)
	for _, crop := range crops {
		models = append(models, &frontend.CropModel{Crop: crop.Name, HasModel: crop.HasModel, Preferred: preferredCrops[crop.Name]})
	}

	sort.SliceStable(models, func(i, j int) bool {
		if models[i].Preferred && !models[j].Preferred {
			return true
		} else if models[j].Preferred && !models[i].Preferred {
			return false
		}
		return models[i].Crop < models[j].Crop
	})

	resp := &frontend.CropModelOptions{
		Models: models,
	}

	return resp, nil
}

func (s *DashboardService) SetCropModel(ctx context.Context, req *frontend.CropModel) (*frontend.Empty, error) {
	logrus.Infof("Set CropEnvironment Model: %s", req.Crop)

	cropFound := false
	s.modelState.ReadOnCurrent(func() {
		for _, crop := range s.modelState.CropEnvironments {
			if crop.Name == req.Crop {
				cropFound = true
				return
			}
		}
	})
	if !cropFound {
		return nil, status.Errorf(codes.NotFound, "Invalid CropEnvironment: %v", req.Crop)
	}
	err := s.configClient.SetStringValue(config.GetComputerConfigPrefix()+"/commander/current_crop", req.Crop)
	if err != nil {
		return nil, err
	}

	s.selectedModelWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DashboardService) GetNextDashboardState(ctx context.Context, req *frontend.Timestamp) (*frontend.DashboardStateMessage, error) {
	var ts int64 = req.GetTimestampMs()
	resp := &frontend.DashboardStateMessage{
		RowStates: make(map[int32]*frontend.RowStateMessage),
	}
	result := s.opState.ReadOnNext(ctx, ts, func() {
		resp.DebugMode = s.opState.DebugMode
		resp.LasersEnabled = true
		resp.TargetingState = &frontend.TargetingState{
			WeedState: &frontend.WeedTargeting{Enabled: s.opState.ExpectedTargeting.WeedState.Enabled},
			ThinningState: &frontend.ThinningTargeting{
				Enabled: s.opState.ExpectedTargeting.ThinningState.Enabled,
			},
			EnabledRows: make(map[int32]bool),
		}
		maxVal := 0
		for i := range s.opState.RowWeeding {
			if i > maxVal {
				maxVal = i
			}
		}
		for i := 1; i <= maxVal; i++ {
			val, ok := s.opState.RowWeeding[i]
			if !ok {
			} else {
				resp.RowStates[int32(i)] = &frontend.RowStateMessage{
					Enabled:             val.ActualTargeting.Enabled(),
					TargetStateMismatch: !s.opState.ExpectedTargeting.IsEqualWithRow(&val.ActualTargeting.TargetingState, i),
					Ready:               val.RowReady,
					SafetyOverrideState: intToSafetyState(val.RowSafetyState),
				}
				resp.TargetingState.EnabledRows[int32(i)] = s.opState.ExpectedTargeting.IsEnabled[i]
			}
		}
		statuses := []*state.ExtraDashboardStatus{}
		for _, val := range s.opState.ExtraStatuses {
			statuses = append(statuses, val)

		}
		sort.Slice(statuses, func(i int, j int) bool {
			return statuses[i].Priority > statuses[j].Priority || (statuses[i].Priority == statuses[j].Priority && statuses[i].Title <= statuses[j].Title)
		})

		for _, val := range statuses {
			resp.Extras = append(resp.Extras, &frontend.ExtraStatus{
				Title:       val.Title,
				GroupId:     val.GroupId,
				IconName:    val.IconName,
				IconColor:   val.IconColor,
				StatusText:  val.StatusText,
				BottomText:  val.BottomText,
				StatusColor: val.StatusColor,
				Progress:    val.Progress,
			})
		}

		resp.SelectedModel = &frontend.CropModel{
			Crop:   s.opState.SelectedModel.Crop,
			CropId: s.opState.SelectedModel.CropID,
		}
		resp.RowWidthIn = s.configNodeCommon.GetNode("row_width_in").GetFloatValue()
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.opState.GetTimestampMs(),
		}
		resp.ImplementState = s.opState.Dashboard.ImplementState
		resp.EfficiencyEnabled = s.opState.Dashboard.EfficiencyEnabled
		resp.EfficiencyPercent = &frontend.PercentValue{Percent: s.opState.Dashboard.EfficiencyPercent}
		resp.ErrorRateEnabled = s.opState.Dashboard.ErrorRateEnabled
		resp.ErrorRate = &frontend.PercentValue{Percent: s.opState.Dashboard.ErrorPercent}
		resp.ExtraConclusions = s.opState.Dashboard.ExtraConclusions
		resp.AreaWeededToday = &frontend.AreaValue{Value: &frontend.AreaValue_Acres{Acres: s.opState.Dashboard.AreaWeededToday}}
		resp.AreaWeededTotal = &frontend.AreaValue{Value: &frontend.AreaValue_Acres{Acres: s.opState.Dashboard.AreaWeededTotal}}
		resp.TimeWeededToday = &frontend.DurationValue{Value: &frontend.DurationValue_Minutes{Minutes: uint64(s.opState.Dashboard.TimeWeededTodayMinutes)}}
		resp.TimeWeededTotal = &frontend.DurationValue{Value: &frontend.DurationValue_Minutes{Minutes: uint64(s.opState.Dashboard.TimeWeededTotalMinutes)}}
		resp.WeedsKilledToday = &frontend.IntegerValue{Value: s.opState.Dashboard.WeedsKilledToday}
		resp.WeedsKilledTotal = &frontend.IntegerValue{Value: s.opState.Dashboard.WeedsKilledTotal}
		resp.CropsKilledToday = &frontend.IntegerValue{Value: s.opState.Dashboard.CropsKilledToday}
		resp.CropsKilledTotal = &frontend.IntegerValue{Value: s.opState.Dashboard.CropsKilledTotal}
		resp.WeedingEnabled = s.opState.Dashboard.WeedingEnabled
		resp.CruiseEnabled = s.opState.Dashboard.CruiseEnabled
		resp.CruiseAllowEnable = s.opState.Dashboard.CruiseAllowEnable
	})

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Dashboard Retrieved")
}

func (s *DashboardService) GetNextWeedingVelocity(ctx context.Context, req *frontend.Timestamp) (*frontend.WeedingVelocity, error) {
	var ts int64 = req.GetTimestampMs()
	resp := &frontend.WeedingVelocity{}
	result := s.velState.GetNextVelocity(ts, ctx, resp)
	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Velocity Retrieved")
}
func (s *DashboardService) SetTargetingState(ctx context.Context, req *frontend.TargetingState) (*frontend.Empty, error) {
	if !s.tractorSafetyState.IsSafeComputed() && (req.GetWeedState().GetEnabled() || req.GetThinningState().GetEnabled()) {
		return nil, errors.New("Cannot Enable LaserWeeder when not in safe state.")
	}
	s.opState.WriteOnCurrent(func() {
		s.opState.ExpectedTargeting.WeedState.Enabled = req.GetWeedState().GetEnabled()
		s.opState.ExpectedTargeting.ThinningState.Enabled = req.GetThinningState().GetEnabled()
		for i, _ := range s.opState.ExpectedTargeting.IsEnabled {
			s.opState.ExpectedTargeting.IsEnabled[i] = true // reset all to true
		}
		for i, enabled := range req.GetEnabledRows() {
			s.opState.ExpectedTargeting.IsEnabled[int(i)] = enabled
		}
	})
	s.weedingEnforcer.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DashboardService) SetRowSpacing(ctx context.Context, req *frontend.RowSpacing) (*frontend.Empty, error) {
	err := s.configClient.SetDoubleValue("common/row_width_in", float64(req.Width))
	return &frontend.Empty{}, err
}

func (s *DashboardService) SetCruiseEnabled(ctx context.Context, req *frontend.CruiseEnable) (*frontend.Empty, error) {
	logrus.Infof("Set Cruise Enabled: %v", req.Enabled)
	_, err := s.hwClient.SetCruiseEnabled(req.Enabled)
	return &frontend.Empty{}, err
}

func intToSafetyState(state int32) frontend.SafetyOverrideState {
	switch state {
	case 0:
		return frontend.SafetyOverrideState_SafetyOverrideNone
	case 1:
		return frontend.SafetyOverrideState_SafetyOverrideVelocityStop
	default:
		return frontend.SafetyOverrideState_SafetyOverrideNone
	}
}

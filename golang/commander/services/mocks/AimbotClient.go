// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// AimbotClient is an autogenerated mock type for the AimbotClient type
type AimbotClient struct {
	mock.Mock
}

// MoveScanner provides a mock function with given fields: scannerId, x, y
func (_m *AimbotClient) MoveScanner(scannerId uint32, x uint32, y uint32) error {
	ret := _m.Called(scannerId, x, y)

	var r0 error
	if rf, ok := ret.Get(0).(func(uint32, uint32, uint32) error); ok {
		r0 = rf(scannerId, x, y)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetCrosshairPosition provides a mock function with given fields: scannerId, x, y
func (_m *AimbotClient) SetCrosshairPosition(scannerId uint32, x uint32, y uint32) error {
	ret := _m.Called(scannerId, x, y)

	var r0 error
	if rf, ok := ret.Get(0).(func(uint32, uint32, uint32) error); ok {
		r0 = rf(scannerId, x, y)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartAutoCalibrateCrosshair provides a mock function with given fields: scannerId
func (_m *AimbotClient) StartAutoCalibrateCrosshair(scannerId uint32) error {
	ret := _m.Called(scannerId)

	var r0 error
	if rf, ok := ret.Get(0).(func(uint32) error); ok {
		r0 = rf(scannerId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewAimbotClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewAimbotClient creates a new instance of AimbotClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewAimbotClient(t mockConstructorTestingTNewAimbotClient) *AimbotClient {
	mock := &AimbotClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

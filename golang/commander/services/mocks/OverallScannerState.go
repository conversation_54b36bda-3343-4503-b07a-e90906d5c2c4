// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	state "github.com/carbonrobotics/robot/golang/commander/state"
)

// OverallScannerState is an autogenerated mock type for the OverallScannerState type
type OverallScannerState struct {
	mock.Mock
}

// GetNextXHairProgress provides a mock function with given fields: ts, failedScanners, ctx
func (_m *OverallScannerState) GetNextXHairProgress(ts int64, failedScanners *[]string, ctx context.Context) (bool, float32, int64, bool) {
	ret := _m.Called(ts, failedScanners, ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetNextXHairProgress")
	}

	var r0 bool
	var r1 float32
	var r2 int64
	var r3 bool
	if rf, ok := ret.Get(0).(func(int64, *[]string, context.Context) (bool, float32, int64, bool)); ok {
		return rf(ts, failedScanners, ctx)
	}
	if rf, ok := ret.Get(0).(func(int64, *[]string, context.Context) bool); ok {
		r0 = rf(ts, failedScanners, ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(int64, *[]string, context.Context) float32); ok {
		r1 = rf(ts, failedScanners, ctx)
	} else {
		r1 = ret.Get(1).(float32)
	}

	if rf, ok := ret.Get(2).(func(int64, *[]string, context.Context) int64); ok {
		r2 = rf(ts, failedScanners, ctx)
	} else {
		r2 = ret.Get(2).(int64)
	}

	if rf, ok := ret.Get(3).(func(int64, *[]string, context.Context) bool); ok {
		r3 = rf(ts, failedScanners, ctx)
	} else {
		r3 = ret.Get(3).(bool)
	}

	return r0, r1, r2, r3
}

// GetScannerState provides a mock function with given fields: camID
func (_m *OverallScannerState) GetScannerState(camID string) (state.ScannerState, bool) {
	ret := _m.Called(camID)

	if len(ret) == 0 {
		panic("no return value specified for GetScannerState")
	}

	var r0 state.ScannerState
	var r1 bool
	if rf, ok := ret.Get(0).(func(string) (state.ScannerState, bool)); ok {
		return rf(camID)
	}
	if rf, ok := ret.Get(0).(func(string) state.ScannerState); ok {
		r0 = rf(camID)
	} else {
		r0 = ret.Get(0).(state.ScannerState)
	}

	if rf, ok := ret.Get(1).(func(string) bool); ok {
		r1 = rf(camID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// GetTimestampMs provides a mock function with no fields
func (_m *OverallScannerState) GetTimestampMs() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimestampMs")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// ReadOnCurrent provides a mock function with given fields: readOnCurrent
func (_m *OverallScannerState) ReadOnCurrent(readOnCurrent func()) {
	_m.Called(readOnCurrent)
}

// ReadOnNext provides a mock function with given fields: ctx, timestampMs, readOnNext
func (_m *OverallScannerState) ReadOnNext(ctx context.Context, timestampMs int64, readOnNext func()) bool {
	ret := _m.Called(ctx, timestampMs, readOnNext)

	if len(ret) == 0 {
		panic("no return value specified for ReadOnNext")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, int64, func()) bool); ok {
		r0 = rf(ctx, timestampMs, readOnNext)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Terminate provides a mock function with no fields
func (_m *OverallScannerState) Terminate() {
	_m.Called()
}

// WriteOnCurrent provides a mock function with given fields: writeOnCurrent
func (_m *OverallScannerState) WriteOnCurrent(writeOnCurrent func()) {
	_m.Called(writeOnCurrent)
}

// WriteOnCurrentWithTimestamp provides a mock function with given fields: writeOnCurrent, timestampMs
func (_m *OverallScannerState) WriteOnCurrentWithTimestamp(writeOnCurrent func(), timestampMs int64) {
	_m.Called(writeOnCurrent, timestampMs)
}

// NewOverallScannerState creates a new instance of OverallScannerState. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOverallScannerState(t interface {
	mock.TestingT
	Cleanup(func())
}) *OverallScannerState {
	mock := &OverallScannerState{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

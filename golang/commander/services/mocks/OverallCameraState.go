// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	state "github.com/carbonrobotics/robot/golang/commander/state"
)

// OverallCameraState is an autogenerated mock type for the OverallCameraState type
type OverallCameraState struct {
	mock.Mock
}

// GetCamera provides a mock function with given fields: camID
func (_m *OverallCameraState) GetCamera(camID string) (*state.Camera, bool) {
	ret := _m.Called(camID)

	if len(ret) == 0 {
		panic("no return value specified for GetCamera")
	}

	var r0 *state.Camera
	var r1 bool
	if rf, ok := ret.Get(0).(func(string) (*state.Camera, bool)); ok {
		return rf(camID)
	}
	if rf, ok := ret.Get(0).(func(string) *state.Camera); ok {
		r0 = rf(camID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*state.Camera)
		}
	}

	if rf, ok := ret.Get(1).(func(string) bool); ok {
		r1 = rf(camID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// GetTimestampMs provides a mock function with no fields
func (_m *OverallCameraState) GetTimestampMs() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimestampMs")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// ReadOnCurrent provides a mock function with given fields: readOnCurrent
func (_m *OverallCameraState) ReadOnCurrent(readOnCurrent func()) {
	_m.Called(readOnCurrent)
}

// ReadOnNext provides a mock function with given fields: ctx, timestampMs, readOnNext
func (_m *OverallCameraState) ReadOnNext(ctx context.Context, timestampMs int64, readOnNext func()) bool {
	ret := _m.Called(ctx, timestampMs, readOnNext)

	if len(ret) == 0 {
		panic("no return value specified for ReadOnNext")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, int64, func()) bool); ok {
		r0 = rf(ctx, timestampMs, readOnNext)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Terminate provides a mock function with no fields
func (_m *OverallCameraState) Terminate() {
	_m.Called()
}

// WriteOnCurrent provides a mock function with given fields: writeOnCurrent
func (_m *OverallCameraState) WriteOnCurrent(writeOnCurrent func()) {
	_m.Called(writeOnCurrent)
}

// WriteOnCurrentWithTimestamp provides a mock function with given fields: writeOnCurrent, timestampMs
func (_m *OverallCameraState) WriteOnCurrentWithTimestamp(writeOnCurrent func(), timestampMs int64) {
	_m.Called(writeOnCurrent, timestampMs)
}

// NewOverallCameraState creates a new instance of OverallCameraState. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOverallCameraState(t interface {
	mock.TestingT
	Cleanup(func())
}) *OverallCameraState {
	mock := &OverallCameraState{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

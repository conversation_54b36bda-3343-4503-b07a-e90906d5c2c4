package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ChipCfg interface {
	GetChipMetadata(ctx context.Context) ([]*frontend.ChipData, error)
	GetDownloadedChipIds(ctx context.Context) []string
	GetSyncedChipIds(ctx context.Context) []string
}

type ChipService struct {
	frontend.UnimplementedChipServiceServer
	chipCfg ChipCfg
}

func NewChipService(grpcServer *grpc.Server, chipCfg ChipCfg) *ChipService {
	service := &ChipService{
		chipCfg: chipCfg,
	}
	frontend.RegisterChipServiceServer(grpcServer, service)
	return service
}

func (s *ChipService) GetChipMetadata(ctx context.Context, req *frontend.Empty) (*frontend.GetChipMetadataResponse, error) {
	chipData, err := s.chipCfg.GetChipMetadata(ctx)
	if err != nil {
		return nil, status.Error(codes.Aborted, "context cancelled before Chip data was retrieved")
	}

	resp := &frontend.GetChipMetadataResponse{
		Chips: chipData,
	}

	return resp, nil
}

func (s *ChipService) GetDownloadedChipIds(ctx context.Context, req *frontend.Empty) (*frontend.ChipIdsResponse, error) {
	downloadedChipIds := s.chipCfg.GetDownloadedChipIds(ctx)
	resp := &frontend.ChipIdsResponse{
		ChipIds: downloadedChipIds,
	}

	return resp, nil
}

func (s *ChipService) GetSyncedChipIds(ctx context.Context, req *frontend.Empty) (*frontend.ChipIdsResponse, error) {
	syncedChipIds := s.chipCfg.GetSyncedChipIds(ctx)
	resp := &frontend.ChipIdsResponse{
		ChipIds: syncedChipIds,
	}

	return resp, nil
}

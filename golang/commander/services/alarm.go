package services

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type AlarmService struct {
	frontend.UnimplementedAlarmServiceServer
	alarmState   state.ManagedState
	alarmManager *state.AlarmManager
	hwClient     *hardware_manager.HardwareManagerClient
}

// test points
var (
	frontendRegisterAlarmServiceServer = func(s grpc.ServiceRegistrar, srv frontend.AlarmServiceServer) {
		frontend.RegisterAlarmServiceServer(s, srv)
	}
	alarmsDefault        = make(map[state.AlarmType]map[string]*alarms.Alarm)
	errInvalidAlarmState = errors.New("invalid alarm state")

	alarmAutofixCount = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem: "commander",
		Name:      "alarm_autofix_count",
		Help:      "count of alarm autofix attempts",
	}, []string{"alarm"})
)

func NewAlarmService(grpcServer *grpc.Server, alarmState *state.AlarmState, alarmManager *state.AlarmManager, hwClient *hardware_manager.HardwareManagerClient) *AlarmService {
	service := &AlarmService{
		alarmState:   alarmState,
		alarmManager: alarmManager,
		hwClient:     hwClient,
	}
	frontendRegisterAlarmServiceServer(grpcServer, service)
	return service
}

func SortAlarms(alarmSlice []*frontend.AlarmRow) {
	sort.SliceStable(alarmSlice, func(i, j int) bool {
		a := alarmSlice[i]
		b := alarmSlice[j]

		if a.Level != b.Level {
			return a.Level < b.Level
		} else if a.Impact != b.Impact {
			return a.Impact < b.Impact
		} else {
			return a.AlarmCode < b.AlarmCode
		}
	})
}

func (s *AlarmService) alarmListFn(resp *frontend.AlarmTable) func() {
	return func() {
		for _, alarmsByIdentifier := range alarmsFromState(s.alarmState) {
			for _, alarm := range alarmsByIdentifier {
				if alarm.Level == alarms.AlarmLevelHidden || !alarm.Valid {
					continue
				}
				resp.Alarms = append(resp.Alarms, alarms.Alarm2Frontend(alarm))
			}
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.alarmState.GetTimestampMs(),
		}
	}
}

func (s *AlarmService) GetNextAlarmList(ctx context.Context, req *frontend.Timestamp) (*frontend.AlarmTable, error) {
	ts := req.GetTimestampMs()
	resp := &frontend.AlarmTable{}
	result := s.alarmState.ReadOnNext(ctx, ts, s.alarmListFn(resp))

	SortAlarms(resp.Alarms)

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Alarms Retrieved")
}

func (s *AlarmService) alarmCountFn(resp *frontend.AlarmCount) func() {
	return func() {
		sum := 0
		for _, alarmsByIdentifier := range alarmsFromState(s.alarmState) {
			for _, alarm := range alarmsByIdentifier {
				if alarm.Level == alarms.AlarmLevelHidden || !alarm.Valid {
					continue
				}
				sum += 1
			}
		}
		resp.Count = uint32(sum)
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.alarmState.GetTimestampMs(),
		}
	}
}

func (s *AlarmService) GetNextAlarmCount(ctx context.Context, req *frontend.Timestamp) (*frontend.AlarmCount, error) {
	ts := req.GetTimestampMs()
	resp := &frontend.AlarmCount{}
	result := s.alarmState.ReadOnNext(ctx, ts, s.alarmCountFn(resp))

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Alarm Count Retrieved")
}

func (s *AlarmService) newAlarmListFn(resp *frontend.AlarmTable) func() {
	return func() {
		for _, alarmsByIdentifier := range alarmsFromState(s.alarmState) {
			for _, alarm := range alarmsByIdentifier {
				if alarm.Level == alarms.AlarmLevelHidden || alarm.Acknowledged || !alarm.Valid {
					continue
				}
				resp.Alarms = append(resp.Alarms, alarms.Alarm2Frontend(alarm))
			}
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.alarmState.GetTimestampMs(),
		}
	}
}

func (s *AlarmService) GetNextNewAlarmList(ctx context.Context, req *frontend.Timestamp) (*frontend.AlarmTable, error) {
	ts := req.GetTimestampMs()
	resp := &frontend.AlarmTable{}
	result := s.alarmState.ReadOnNext(ctx, ts, s.newAlarmListFn(resp))

	SortAlarms(resp.Alarms)

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Alarms Retrieved")
}

func (s *AlarmService) AcknowledgeAlarm(ctx context.Context, req *frontend.AcknowledgeRequest) (*frontend.Empty, error) {
	logrus.Infof("Acknowledging Alarm: %s", req.Identifier)
	err := acknowledgeAlarmState(s.alarmState, req.Identifier)

	if err != nil {
		return nil, status.Error(codes.NotFound, err.Error())
	}

	return &frontend.Empty{}, nil
}

func (s *AlarmService) ResetAlarms(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Info("Resetting Alarms")
	s.alarmManager.Reset()
	return &frontend.Empty{}, nil
}

// alarmsFromState bridges the state.AlarmState to the ManagedState interface
func alarmsFromState(rawAlarmState interface{}) map[state.AlarmType]map[string]*alarms.Alarm {
	if as, ok := rawAlarmState.(*state.AlarmState); ok {
		return as.Alarms
	}
	return alarmsDefault
}

// acknowledgeAlarmState bridges the state.AlarmState to the ManagedState interface
func acknowledgeAlarmState(rawAlarmState interface{}, identifier string) error {
	if as, ok := rawAlarmState.(*state.AlarmState); ok {
		return as.Acknowledge(identifier)
	}
	return errInvalidAlarmState
}

func (s *AlarmService) GetNextAlarmLogCount(ctx context.Context, req *frontend.GetNextAlarmLogCountRequest) (*frontend.GetNextAlarmLogCountResponse, error) {
	resp := &frontend.GetNextAlarmLogCountResponse{}
	s.alarmState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		st := s.alarmState.(*state.AlarmState)
		resp.NumAlarms = int32(len(st.GetAlarmLog(req.VisibleOnly)))
		resp.Ts = &frontend.Timestamp{TimestampMs: s.alarmState.GetTimestampMs()}
	})
	return resp, nil
}

func (s *AlarmService) GetNextAlarmLog(ctx context.Context, req *frontend.GetNextAlarmLogRequest) (*frontend.GetNextAlarmLogResponse, error) {
	resp := &frontend.GetNextAlarmLogResponse{Alarms: make([]*frontend.AlarmRow, 0)}
	s.alarmState.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		st := s.alarmState.(*state.AlarmState)
		alarmLog := st.GetAlarmLog(req.VisibleOnly)
		for idx := len(alarmLog) - int(req.FromIdx) - 1; idx >= 0 && idx >= len(alarmLog)-int(req.ToIdx); idx-- {
			resp.Alarms = append(resp.Alarms, alarmLog[idx])
		}
		resp.Ts = &frontend.Timestamp{TimestampMs: s.alarmState.GetTimestampMs()}
	})
	return resp, nil
}

func (s *AlarmService) AttemptAutofixAlarm(ctx context.Context, req *frontend.AttemptAutofixAlarmRequest) (*frontend.Empty, error) {
	idx := strings.Index(req.Identifier, "_")
	prefixStr := req.Identifier[6:idx]
	alarmTypeInt, _ := strconv.Atoi(prefixStr)
	alarmType := state.IntToAlarmType[alarmTypeInt]
	st := s.alarmState.(*state.AlarmState)

	var e error
	var alarm *alarms.Alarm
	st.WriteOnCurrent(func() {
		if st.AutofixInProgress {
			e = errors.New("An autofix is already in progress")
			return
		}
		alarm = st.GetAlarmsByIdentifier(alarmType)[req.Identifier]
		if alarm == nil {
			e = fmt.Errorf("Couldn't find alarm %v", req.Identifier)
			return
		}
		if alarm.AutofixAttempted {
			e = fmt.Errorf("Autofix already attempted for alarm %v", req.Identifier)
			return
		}
		if alarm.AutofixFunc == nil {
			e = fmt.Errorf("Autofix not supported for this type of alarm")
			return
		}
		st.AutofixInProgress = true
		st.AutofixErrorMessage = ""
		alarm.AutofixAttempted = true
	})
	if e != nil {
		return nil, e
	}

	alarmAutofixCount.WithLabelValues(req.Identifier).Inc()

	go func(alarm *alarms.Alarm) {
		err := alarm.AutofixFunc()

		st.WriteOnCurrent(func() {
			st.AutofixInProgress = false
			if err != nil {
				st.AutofixErrorMessage = err.Error()
			}
		})
	}(alarm)

	return &frontend.Empty{}, e
}

func (s *AlarmService) GetNextAutofixAlarmStatus(ctx context.Context, req *frontend.GetNextAutofixAlarmStatusRequest) (*frontend.GetNextAutofixAlarmStatusResponse, error) {
	resp := &frontend.GetNextAutofixAlarmStatusResponse{}
	st := s.alarmState.(*state.AlarmState)
	st.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		resp.Completed = !st.AutofixInProgress
		resp.ErrorMessage = st.AutofixErrorMessage
		resp.Ts = &frontend.Timestamp{TimestampMs: st.GetTimestampMs()}
	})
	return resp, nil
}

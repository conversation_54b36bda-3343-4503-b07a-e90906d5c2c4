package services

import (
	"context"
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// test point
var (
	frontendRegisterDataCaptureServiceServer = frontend.RegisterDataCaptureServiceServer
	defaultUSBStorageConnected               = false
	defaultWirelessUploadAvailable           = false
	defaultDataCaptureStep                   state.DataCaptureStep
	defaultDataCaptureSession                state.DataCaptureSession
	timeNow                                  = time.Now
)

type DUMEmergencyClient interface {
	StartDataCaptureSession(sessionName string, captureRate float64, crop, cropID string, useLatest bool, rowInd uint32, camId string, snapCapture bool) error
	PauseDataCaptureSession() error
	StopDataCaptureSession() error
	ResumeDataCaptureSession() error
	CompleteDataCaptureSession() error
	StartDataUploadSession(method string) error
	PauseDataUploadSession() error
	StopDataUploadSession() error
	ResumeDataUploadSession() error
	StartBackgroundDataUploadSession(method string, name string) error
	PauseBackgroundDataUploadSession(name string) error
	StopBackgroundDataUploadSession(name string) error
	ResumeBackgroundDataUploadSession(name string) error
	GetCaptureProgress() (data_upload_manager.CaptureProgress, error)
	GetUploadProgress() (data_upload_manager.UploadProgress, error)
	SnapImages(crop, cropID, camId string, rowInd uint32, timestampMs int64, sessionName string) error
	GetSessions() ([]data_upload_manager.Session, error)
	GetRegularCaptureStatus() (data_upload_manager.RegularCaptureStatus, error)
}

type DataCaptureService struct {
	frontend.UnimplementedDataCaptureServiceServer
	dataState               state.ManagedState
	dataWatcher             state.Triggerable
	dataUploadManagerClient DUMEmergencyClient
	configClient            *config.ConfigClient
	nodeCommander           *config.ConfigTree
	nodeDataUploadManager   *config.ConfigTree
	cameraState             state.CommanderCameraState
}

func NewDataCaptureService(grpcServer *grpc.Server, dataState *state.DataCaptureState, dataWatcher *state.DataCaptureWatcher, dataUploadManagerClient *data_upload_manager.EmergencyClient, configClient *config.ConfigClient, nodeCommander *config.ConfigTree, nodeDataUploadManager *config.ConfigTree, cameraState *state.OverallCameraState) *DataCaptureService {
	service := &DataCaptureService{
		dataState:               dataState,
		dataWatcher:             dataWatcher,
		dataUploadManagerClient: dataUploadManagerClient,
		configClient:            configClient,
		nodeCommander:           nodeCommander,
		nodeDataUploadManager:   nodeDataUploadManager,
		cameraState:             cameraState,
	}
	frontendRegisterDataCaptureServiceServer(grpcServer, service)
	return service
}

type dataCaptureConfig struct {
	enableSinglePredictCapture bool
	RowInd                     uint64
	PredictCamID               string
}

var getDataCaptureParams = func(s *DataCaptureService) dataCaptureConfig {
	latestCaptureParams := s.nodeDataUploadManager.GetNode("single_predict_emergency_capture")
	return dataCaptureConfig{
		enableSinglePredictCapture: latestCaptureParams.GetNode("enable_single_predict_capture").GetBoolValue(),
		RowInd:                     latestCaptureParams.GetNode("row_ind").GetUIntValue(),
		PredictCamID:               latestCaptureParams.GetNode("predict_cam_id").GetStringValue(),
	}
}

func (s *DataCaptureService) StartDataCapture(_ context.Context, req *frontend.StartDataCaptureRequest) (*frontend.Empty, error) {
	logrus.Infof("Start Data Capture")

	invalid := false
	sec := timeNow().Unix()
	sessionName := fmt.Sprintf("%s-%d", req.GetName(), sec)
	captureRate := req.GetRate()
	crop := req.GetCrop()
	cropID := req.GetCropId()
	snapCapture := req.GetSnapCapture()
	if len(cropID) > 0 {
		crop = ""
	}

	captureConfig := getDataCaptureParams(s)

	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepNew, state.DataCaptureStepUploadingComplete) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Start Data Capture. In state %v", step)
	}

	if err := s.dataUploadManagerClient.StartDataCaptureSession(sessionName, captureRate, crop, cropID, captureConfig.enableSinglePredictCapture, uint32(captureConfig.RowInd), captureConfig.PredictCamID, snapCapture); err != nil {
		return nil, err
	}

	// Surface the name passed in the command so users see what they entered, rather than our modified session name
	s.dataState.WriteOnCurrent(updateDataCaptureSessionFn(s.dataState, req.GetName(), crop, cropID, req.GetRate()))

	s.dataWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) PauseDataCapture(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Pause Data Capture")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepCapturing) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Pause Data Capture. In state %v", step)
	}

	if err := s.dataUploadManagerClient.PauseDataCaptureSession(); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StopDataCapture(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Stop Data Capture")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepCapturePaused, state.DataCaptureStepCapturing, state.DataCaptureStepCaptureComplete) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Stop Data Capture. In state %v", step)
	}

	if err := s.dataUploadManagerClient.StopDataCaptureSession(); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) ResumeDataCapture(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Resume Data Capture")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepCapturePaused) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Resume Data Capture. In state %v", step)
	}

	if err := s.dataUploadManagerClient.ResumeDataCaptureSession(); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) CompleteDataCapture(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Complete Data Capture")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepCapturePaused, state.DataCaptureStepCapturing, state.DataCaptureStepCaptureComplete) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Complete Data Capture. In state %v", step)
	}

	if err := s.dataUploadManagerClient.CompleteDataCaptureSession(); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StartDataCaptureWirelessUpload(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Start Wireless Upload")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepCaptureComplete) || !isWirelessUploadAvailable(s.dataState) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Start Wireless Upload, or Wireless Upload is unavailable. In state %v", step)
	}

	if err := s.dataUploadManagerClient.StartDataUploadSession("wireless"); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StartDataCaptureUSBUpload(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Start USB Upload")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepCaptureComplete) || !isUSBStorageConnected(s.dataState) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Start USB Upload, or USB storage is unavailable. In state %v", step)
	}

	if err := s.dataUploadManagerClient.StartDataUploadSession("usb"); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StopDataCaptureUpload(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Stop Data Upload")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepUploadingUSB, state.DataCaptureStepUploadingUSBPaused, state.DataCaptureStepUploadingWireless, state.DataCaptureStepUploadingWirelessPaused) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Stop Data Upload. In state %v", step)
	}

	if err := s.dataUploadManagerClient.StopDataUploadSession(); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) PauseDataCaptureUpload(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Pause Data Upload")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepUploadingWireless, state.DataCaptureStepUploadingUSB) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Pause Data Upload. In state %v", step)
	}

	if err := s.dataUploadManagerClient.PauseDataUploadSession(); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) ResumeDataCaptureUpload(context.Context, *frontend.Empty) (*frontend.Empty, error) {
	logrus.Infof("Resume Data Upload")

	invalid := false
	s.dataState.ReadOnCurrent(func() {
		step := stepFromDataCaptureState(s.dataState)
		if step.IsNot(state.DataCaptureStepUploadingWirelessPaused, state.DataCaptureStepUploadingUSBPaused) {
			invalid = true
		}
	})

	if invalid {
		step := stepFromDataCaptureState(s.dataState)
		return nil, status.Errorf(codes.FailedPrecondition, "Data Capture in Invalid State to Resume Data Upload. In state %v", step)
	}

	if err := s.dataUploadManagerClient.ResumeDataUploadSession(); err != nil {
		return nil, err
	}
	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StartBackgroundDataCaptureWirelessUpload(_ context.Context, session *frontend.SessionName) (*frontend.Empty, error) {
	logrus.Infof("Start Background Wireless Upload")

	if err := s.dataUploadManagerClient.StartBackgroundDataUploadSession("wireless", session.GetName()); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StartBackgroundDataCaptureUSBUpload(_ context.Context, session *frontend.SessionName) (*frontend.Empty, error) {
	logrus.Infof("Start Background USB Upload")

	if err := s.dataUploadManagerClient.StartBackgroundDataUploadSession("usb", session.GetName()); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) StopBackgroundDataCaptureUpload(_ context.Context, session *frontend.SessionName) (*frontend.Empty, error) {
	logrus.Infof("Stop Background Data Upload")

	if err := s.dataUploadManagerClient.StopBackgroundDataUploadSession(session.GetName()); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) PauseBackgroundDataCaptureUpload(_ context.Context, session *frontend.SessionName) (*frontend.Empty, error) {
	logrus.Infof("Pause Background Data Upload")

	if err := s.dataUploadManagerClient.PauseBackgroundDataUploadSession(session.GetName()); err != nil {
		return nil, err
	}

	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) ResumeBackgroundDataCaptureUpload(_ context.Context, session *frontend.SessionName) (*frontend.Empty, error) {
	logrus.Infof("Resume Background Data Upload")

	if err := s.dataUploadManagerClient.ResumeBackgroundDataUploadSession(session.GetName()); err != nil {
		return nil, err
	}
	s.dataWatcher.Trigger()
	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) SnapImages(_ context.Context, req *frontend.SnapImagesRequest) (*frontend.Empty, error) {
	logrus.Infof("Snapping Images")

	crop := req.GetCrop()
	cropID := req.GetCropId()
	if len(cropID) > 0 {
		crop = ""
	}

	if req.GetSessionName() == "" {
		env, err := environment.GetRobot()
		robotName := ""
		if err == nil {
			robotName = env.MakaRobotName
		}
		req.SessionName = fmt.Sprintf("%v_%v", robotName, time.Now().UnixMilli())
	}

	camId := req.GetCamId()
	var rowInd uint32 = 0
	timestampMs := req.GetTimestampMs()

	// Translate camera id to cam and row and pass to SnapImages
	if req.GetCamId() != "" {
		s.cameraState.ReadOnCurrent(func() {
			if cam, ok := s.cameraState.GetCamera(req.GetCamId()); ok {
				camId = cam.LocalCameraId
				rowInd = cam.RowNumber
			}
		})
	}

	if err := s.dataUploadManagerClient.SnapImages(crop, cropID, camId, rowInd, timestampMs, req.GetSessionName()); err != nil {
		return nil, err
	}

	return &frontend.Empty{}, nil
}

func (s *DataCaptureService) GetSessions(_ context.Context, req *frontend.Empty) (*frontend.AvailableSessionResponse, error) {
	logrus.Infof("Getting Sessions")

	sessions, err := s.dataUploadManagerClient.GetSessions()

	if err != nil {
		return nil, err
	}

	availableSessions := &frontend.AvailableSessionResponse{}

	for _, session := range sessions {
		ses := &frontend.Session{
			Name:            session.Name,
			ImagesRemaining: session.NumberImages,
			IsUploading:     session.IsUploading,
			HasCompleted:    session.HasCompleted,
			IsCapturing:     session.IsCapturing,
		}
		availableSessions.Sessions = append(availableSessions.Sessions, ses)
	}

	return availableSessions, nil
}

func (s *DataCaptureService) GetRegularCaptureStatus(_ context.Context, req *frontend.Empty) (*frontend.RegularCaptureStatus, error) {
	logrus.Infof("Getting RegularCaptureStatus")

	resp, err := s.dataUploadManagerClient.GetRegularCaptureStatus()

	if err != nil {
		return nil, err
	}

	captureStatus := &frontend.RegularCaptureStatus{}

	captureStatus.Uploaded = resp.Uploaded
	captureStatus.Budget = resp.Budget
	captureStatus.LastUploadTimestamp = resp.LastUploadTimestamp

	return captureStatus, nil
}

func (s *DataCaptureService) dataCaptureStateFn(resp *frontend.DataCaptureState) func() {
	return func() {
		session := sessionFromDataCaptureState(s.dataState)
		resp.CaptureStatus = session.CaptureStatus
		resp.EstimatedCaptureRemainingTimeMs = session.EstimatedCaptureRemainingTimeMs
		resp.EstimatedUploadRemainingTimeMs = session.EstimatedUploadRemainingTimeMs
		resp.ImagesTaken = session.ImagesTaken
		resp.ImagesUploaded = session.ImagesUploaded
		resp.Rate = &frontend.DataCaptureRate{
			Rate: session.CaptureRate,
		}
		resp.Crop = session.Crop
		resp.CropId = session.CropID
		resp.SessionName = session.SessionName
		switch stepFromDataCaptureState(s.dataState) {
		case state.DataCaptureStepNew:
			resp.Step = frontend.ProcedureStep_NEW
		case state.DataCaptureStepCapturing:
			resp.Step = frontend.ProcedureStep_CAPTURING
		case state.DataCaptureStepCapturePaused:
			resp.Step = frontend.ProcedureStep_CAPTURE_PAUSED
		case state.DataCaptureStepCaptureComplete:
			resp.Step = frontend.ProcedureStep_CAPTURE_COMPLETE
		case state.DataCaptureStepUploadingWireless:
			resp.Step = frontend.ProcedureStep_UPLOADING_WIRELESS
		case state.DataCaptureStepUploadingWirelessPaused:
			resp.Step = frontend.ProcedureStep_UPLOADING_WIRELESS_PAUSED
		case state.DataCaptureStepUploadingUSB:
			resp.Step = frontend.ProcedureStep_UPLOADING_USB
		case state.DataCaptureStepUploadingUSBPaused:
			resp.Step = frontend.ProcedureStep_UPLOADING_USB_PAUSED
		case state.DataCaptureStepUploadingComplete:
			resp.Step = frontend.ProcedureStep_UPLOADING_COMPLETE
		default:
			resp.Step = frontend.ProcedureStep_NEW
		}

		resp.TargetImagesTaken = session.TargetImagesTaken
		resp.TargetImagesUploaded = session.TargetImagesUploaded
		resp.UploadStatus = session.UploadStatus
		resp.UsbStorageConnected = isUSBStorageConnected(s.dataState)
		resp.WirelessUploadAvailable = isWirelessUploadAvailable(s.dataState)
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.dataState.GetTimestampMs(),
		}
		if session.ErrorMessage != "" {
			resp.ErrorMessage = session.ErrorMessage
		}
	}
}

func (s *DataCaptureService) GetNextDataCaptureState(ctx context.Context, req *frontend.Timestamp) (*frontend.DataCaptureState, error) {
	ts := req.GetTimestampMs()
	resp := &frontend.DataCaptureState{}
	result := s.dataState.ReadOnNext(ctx, ts, s.dataCaptureStateFn(resp))

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Lasers Retrieved")
}

func sessionFromDataCaptureState(rawDataCaptureState interface{}) state.DataCaptureSession {
	if dataState, ok := rawDataCaptureState.(*state.DataCaptureState); ok {
		return dataState.Session
	}
	return defaultDataCaptureSession
}

func stepFromDataCaptureState(rawDataCaptureState interface{}) state.DataCaptureStep {
	if dataState, ok := rawDataCaptureState.(*state.DataCaptureState); ok {
		return dataState.Step
	}
	return defaultDataCaptureStep
}

func isUSBStorageConnected(rawDataState interface{}) bool {
	if dataState, ok := rawDataState.(*state.DataCaptureState); ok {
		return dataState.USBStorageConnected
	}
	return defaultUSBStorageConnected
}

func isWirelessUploadAvailable(rawDataState interface{}) bool {
	if dataState, ok := rawDataState.(*state.DataCaptureState); ok {
		return dataState.WirelessUploadAvailable
	}
	return defaultWirelessUploadAvailable
}

func updateDataCaptureSessionFn(rawDataCaptureState interface{}, name, crop, cropID string, rate float64) func() {
	return func() {
		if dataState, ok := rawDataCaptureState.(*state.DataCaptureState); ok {
			dataState.Session.SessionName = name
			dataState.Session.CaptureRate = rate
			dataState.Session.Crop = crop
			dataState.Session.CropID = cropID
		}
	}
}

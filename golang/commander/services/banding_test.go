package services

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/stretchr/testify/assert"
)

func Test_isTrajErrored(t *testing.T) {
	tests := []struct {
		name     string
		traj     *weed_tracking.TrajectorySnapshot
		expected bool
	}{
		{
			"non-error",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_SHOT,
				ScoreState: &weed_tracking.ScoreState{TargetState: weed_tracking.TargetableState_TARGET_SCORED},
			},
			false,
		},
		{
			"expected kill status partial error",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_PARTIALLY_SHOT,
				ScoreState: &weed_tracking.ScoreState{TargetState: weed_tracking.TargetableState_TARGET_SCORED},
			},
			true,
		},
		{
			"expected kill status p2p not found error",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_P2P_NOT_FOUND,
				ScoreState: &weed_tracking.ScoreState{TargetState: weed_tracking.TargetableState_TARGET_SCORED},
			},
			true,
		},
		{
			"expected kill status error",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_ERROR,
				ScoreState: &weed_tracking.ScoreState{TargetState: weed_tracking.TargetableState_TARGET_SCORED},
			},
			true,
		},
		{
			"expected kill status p2p missing ctx error",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_P2P_MISSING_CONTEXT,
				ScoreState: &weed_tracking.ScoreState{TargetState: weed_tracking.TargetableState_TARGET_SCORED},
			},
			true,
		},
		{
			"target too many failures error",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_SHOT,
				ScoreState: &weed_tracking.ScoreState{TargetState: weed_tracking.TargetableState_TARGET_TOO_MANY_FAILURES},
			},
			true,
		},
		{
			"npe regression",
			&weed_tracking.TrajectorySnapshot{
				KillStatus: weed_tracking.KillStatus_STATUS_SHOT,
				ScoreState: nil,
			},
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := isTrajErrored(test.traj)
			assert.Equal(t, test.expected, got)
		})
	}
}

package services

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/carbonrobotics/robot/golang/lib/client_owner"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/services"
	"github.com/sirupsen/logrus"
)

type PrometheusGenerator struct {
	robotEnv             environment.Robot
	minicomputerRowsNode *config.ConfigTree
	clientOwner          *client_owner.ClientOwner
}

func NewPrometheusGenerator(configSubscriber *config.ConfigSubscriber, robot environment.Robot, clientOwner *client_owner.ClientOwner) *PrometheusGenerator {
	m := &PrometheusGenerator{
		robotEnv:             robot,
		minicomputerRowsNode: configSubscriber.GetConfigNode("common", "minicomputer_rows"),
		clientOwner:          clientOwner,
	}

	// we have a callback for a config change but the rest of commander requires a restart for the change to take effect
	m.minicomputerRowsNode.RegisterCallback(func() {
		m.clientOwner.ReloadClients()
		err := m.GeneratePrometheusConfig()
		if err != nil {
			logrus.Warn(err)
		}
	})
	return m
}

type prometheusSDFile struct {
	Targets []string          `json:"targets,omitempty"`
	Labels  map[string]string `json:"labels,omitempty"`
}

func (m *PrometheusGenerator) makeIpAddress(ip string, port int) string {
	if port != 0 {
		return fmt.Sprintf("%s:%v", ip, port)
	} else {
		return ip
	}
}

func (m *PrometheusGenerator) GeneratePrometheusConfig() error {
	targetRoot := filepath.Join(m.robotEnv.MakaConfigDir, "prometheus", "targets")
	if err := os.MkdirAll(targetRoot, os.ModePerm); err != nil {
		return err
	}

	for name, service := range services.GetServices() {
		targets := make([]string, 0)
		for _, host := range m.clientOwner.GetHostClients() {
			if host.Row == 0 {
				continue
			} else if host.IsSecondary && !service.RunsOnSecondary {
				continue
			}
			targets = append(targets, m.makeIpAddress(host.IpAddress, service.MetricsPort))
		}

		targetsFile := []prometheusSDFile{{Targets: targets}}
		jsonString, err := json.Marshal(targetsFile)
		if err != nil {
			return err
		}

		targetsFilename := filepath.Join(targetRoot, fmt.Sprintf("%s_targets.json", name))
		err = os.WriteFile(targetsFilename, jsonString, 0666)
		if err != nil {
			return err
		}
	}
	return nil
}

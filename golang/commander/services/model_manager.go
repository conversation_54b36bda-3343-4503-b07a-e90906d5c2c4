package services

import (
	"context"
	"errors"
	"sort"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	svcModelLogger = logrus.New()
)

type ModelService struct {
	frontend.UnimplementedModelServiceServer
	modelWatcher  *state.ModelManagerWatcher
	modelState    *state.ModelManagerState
	commanderNode *config.ConfigTree
	commonNode    *config.ConfigTree
	configClient  *config.ConfigClient
	logger        *logrus.Entry
}

func NewModelService(grpcServer *grpc.Server, modelWatcher *state.ModelManagerWatcher, modelState *state.ModelManagerState, configClient *config.ConfigClient, commanderNode *config.ConfigTree, commonNode *config.ConfigTree) *ModelService {
	service := &ModelService{
		modelWatcher:  modelWatcher,
		modelState:    modelState,
		configClient:  configClient,
		commanderNode: commanderNode,
		commonNode:    commonNode,
		logger:        svcModelLogger.WithFields(logrus.Fields{"module": "ModelService"}),
	}
	frontend.RegisterModelServiceServer(grpcServer, service)
	return service
}

func (m *ModelService) GetNextSelectedCropID(ctx context.Context, ts *frontend.Timestamp) (*frontend.GetNextSelectedCropIDResponse, error) {
	resp := new(frontend.GetNextSelectedCropIDResponse)
	m.modelState.ReadOnNext(ctx, ts.GetTimestampMs(), func() {
		resp.Ts = &frontend.Timestamp{TimestampMs: m.modelState.GetTimestampMs()}
		resp.CropId = m.modelWatcher.GetSelectedCropID()
	})
	return resp, nil
}

func (m *ModelService) SelectCrop(_ context.Context, req *frontend.SelectCropRequest) (*frontend.Empty, error) {
	if err := m.modelWatcher.SelectCropByID(req.CropId); err != nil {
		return &frontend.Empty{}, status.Errorf(codes.Unknown, "failed to select crop by id %q - %v", req.CropId, err)
	}
	return &frontend.Empty{}, nil
}

func (m *ModelService) TriggerDownload(_ context.Context, _ *frontend.Empty) (*frontend.Empty, error) {
	m.logger.Info("TriggerDownload called")
	m.modelWatcher.TriggerDownload()
	return &frontend.Empty{}, nil
}

func (m *ModelService) DownloadModel(_ context.Context, req *frontend.DownloadModelRequest) (*frontend.Empty, error) {
	m.logger.Info("requesting model download modelID:", req.GetModelId())
	go func() {
		if err := m.modelWatcher.DownloadModel(context.Background(), req.GetModelId()); err != nil {
			m.logger.WithError(err).Errorf("model (%s) download failed", req.GetModelId())
		}
	}()
	return &frontend.Empty{}, nil
}

func (m *ModelService) ListEnabledCrops(_ context.Context, params *frontend.ListCropParameters) (*frontend.EnabledCropList, error) {
	enabledCropList := m.modelWatcher.EnabledCropList(false)
	return EnabledCropListToFrontend(enabledCropList, params.GetLang()), nil
}

func (m *ModelService) ListCaptureCrops(_ context.Context, params *frontend.ListCropParameters) (*frontend.EnabledCropList, error) {
	enabledCropList := m.modelWatcher.EnabledCropList(true)
	return EnabledCropListToFrontend(enabledCropList, params.GetLang()), nil
}

func (m *ModelService) GetNextEnabledCrops(ctx context.Context, req *frontend.GetNextEnabledCropsRequest) (*frontend.GetNextEnabledCropsResponse, error) {
	enabledCropList, _, ts := m.modelState.GetNextEnabledCrops(ctx, req.Ts.GetTimestampMs(), m.modelWatcher)
	resp := &frontend.GetNextEnabledCropsResponse{
		Ts:           &frontend.Timestamp{TimestampMs: ts},
		EnabledCrops: EnabledCropListToFrontend(enabledCropList, req.GetLang()).EnabledCrops,
	}
	return resp, nil
}

func (m *ModelService) GetNextCaptureCrops(ctx context.Context, req *frontend.GetNextCaptureCropsRequest) (*frontend.GetNextCaptureCropsResponse, error) {
	enabledCropList, _, ts := m.modelState.GetNextCaptureCrops(ctx, req.Ts.GetTimestampMs(), m.modelWatcher)
	resp := &frontend.GetNextCaptureCropsResponse{
		Ts:           &frontend.Timestamp{TimestampMs: ts},
		EnabledCrops: EnabledCropListToFrontend(enabledCropList, req.GetLang()).EnabledCrops,
	}
	return resp, nil
}

func (m *ModelService) UpdateModel(ctx context.Context, req *frontend.Empty) (*frontend.Empty, error) {
	m.modelWatcher.ForceModelUpdate()
	return &frontend.Empty{}, nil
}

func (m *ModelService) PinModel(ctx context.Context, req *frontend.PinModelRequest) (empty *frontend.Empty, err error) {
	empty = &frontend.Empty{}
	if req.GetP2P() {
		err = m.modelWatcher.PinP2PModel(req.GetId())
	} else {
		err = m.modelWatcher.PinModel(req.GetId(), req.GetCropId())
	}
	if err != nil {
		return &frontend.Empty{}, status.Errorf(codes.Unknown, "Failed to pin model: %s", err.Error())
	}
	return
}

func (m *ModelService) UnpinModel(ctx context.Context, req *frontend.UnpinModelRequest) (empty *frontend.Empty, err error) {
	empty = &frontend.Empty{}
	if req.GetP2P() {
		err = m.modelWatcher.UnpinP2PModel()
	} else {
		err = m.modelWatcher.UnpinModel(req.GetCropId())
	}
	if err != nil {
		return &frontend.Empty{}, status.Errorf(codes.Unknown, "Failed to unpin model: %s", err.Error())
	}
	return
}

func (m *ModelService) GetNextAllModelState(ctx context.Context, req *frontend.GetNextModelStateRequest) (*frontend.GetNextModelStateResponse, error) {
	resp := &frontend.GetNextModelStateResponse{}
	list := m.modelWatcher.ListModels(req.GetCropId())
	for _, model := range list {
		resp.Models = append(resp.Models, ModelInfoToProto(model))
	}

	m.modelState.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		resp.CurrentDeepweedModelId = m.modelState.CurrentDeepWeedModelId
		resp.CurrentP2PModelId = m.modelState.CurrentP2PModelID
		resp.Ts = &frontend.Timestamp{
			TimestampMs: m.modelState.GetTimestampMs(),
		}
	})

	sort.SliceStable(resp.Models, func(i, j int) bool {
		return resp.Models[i].Ts.TimestampMs > resp.Models[j].Ts.TimestampMs
	})
	return resp, nil
}

func (m *ModelService) GetNextModelState(ctx context.Context, req *frontend.GetNextModelStateRequest) (*frontend.GetNextModelStateResponse, error) {
	resp := &frontend.GetNextModelStateResponse{}

	list := m.modelWatcher.ListModels(req.GetCropId())
	for _, model := range list {
		if model.Downloading {
			continue
		}
		resp.Models = append(resp.Models, ModelInfoToProto(model))
	}

	m.modelState.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		resp.CurrentDeepweedModelId = m.modelState.CurrentDeepWeedModelId
		resp.CurrentP2PModelId = m.modelState.CurrentP2PModelID
		resp.Ts = &frontend.Timestamp{
			TimestampMs: m.modelState.GetTimestampMs(),
		}
	})

	sort.SliceStable(resp.Models, func(i, j int) bool {
		return resp.Models[i].Ts.TimestampMs > resp.Models[j].Ts.TimestampMs
	})
	return resp, nil
}

func (m *ModelService) GetNextModelHistory(ctx context.Context, req *frontend.ModelHistoryRequest) (resp *frontend.ModelHistoryResponse, err error) {
	m.modelState.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		resp, err = m.GetModelHistory(ctx, req)
		if resp != nil {
			resp.Ts = &frontend.Timestamp{
				TimestampMs: m.modelState.GetTimestampMs(),
			}
		}
	})
	if err != nil {
		return &frontend.ModelHistoryResponse{}, err
	}
	return resp, nil
}

func (m *ModelService) GetModelHistory(_ context.Context, req *frontend.ModelHistoryRequest) (*frontend.ModelHistoryResponse, error) {
	query := state.ModelHistoryQuery{
		StartTimestamp:   req.GetStartTimestamp(),
		Count:            req.GetCount(),
		MatchFilter:      FrontendModelEventToModelEvent(req.GetMatchFilter()),
		EventTypeMatcher: FrontEndModelEventTypeMatcherToModelEventTypes(req.GetEventTypeMatcher()),
		Reverse:          req.GetReverse(),
	}
	results, err := m.modelWatcher.GetModelHistory(query)
	if err != nil {
		return nil, err
	}
	feResults := make([]*frontend.ModelEvent, 0)
	for _, res := range results {
		feResults = append(feResults, ModelEventToFrontend(res))
	}
	resp := &frontend.ModelHistoryResponse{
		Events: feResults,
	}
	return resp, nil
}

func (m *ModelService) GetNextModelNicknames(ctx context.Context, req *frontend.GetModelNicknamesRequest) (resp *frontend.GetModelNicknamesResponse, err error) {
	m.modelState.ReadOnNext(ctx, req.Ts.GetTimestampMs(), func() {
		resp, err = m.GetModelNicknames(ctx, req)
		if resp != nil {
			resp.Ts = &frontend.Timestamp{
				TimestampMs: m.modelState.GetTimestampMs(),
			}
		}
	})
	if err != nil {
		return nil, err
	}
	return resp, err
}

func (m *ModelService) GetModelNicknames(_ context.Context, req *frontend.GetModelNicknamesRequest) (*frontend.GetModelNicknamesResponse, error) {
	resp := &frontend.GetModelNicknamesResponse{ModelNicknames: make(map[string]string)}
	for _, modelID := range req.GetModelIds() {
		nickname, err := m.modelWatcher.GetModelNickname(modelID)
		if err == nil {
			resp.ModelNicknames[modelID] = nickname
		} else if !errors.Is(err, state.ErrNotFound) {
			return nil, err
		}
	}
	return resp, nil
}

func (m *ModelService) SetModelNickname(_ context.Context, req *frontend.SetModelNicknameRequest) (*frontend.Empty, error) {
	modelID := req.GetModelId()
	modelNickname := req.GetModelNickname()
	if modelNickname == "" {
		return &frontend.Empty{}, m.modelWatcher.DeleteModelNickname(modelID)
	}
	return &frontend.Empty{}, m.modelWatcher.SaveModelNickname(modelID, modelNickname, time.Now())
}

func (m *ModelService) RefreshDefaultModelParameters(_ context.Context, req *frontend.RefreshDefaultModelParametersRequest) (*frontend.Empty, error) {
	cmPairs := make([]veselka.ModelParameterRequest, 0)
	for _, pair := range req.GetCropModelPairs() {
		cmPairs = append(cmPairs, veselka.ModelParameterRequest{ModelID: pair.GetModelId(), CropID: pair.GetCropId()})
	}
	return &frontend.Empty{}, m.modelWatcher.UpdateModelParametersFromRemote(cmPairs)
}

func (m *ModelService) SyncCropIDs(_ context.Context, req *frontend.SyncCropIDsRequest) (*frontend.Empty, error) {
	m.logger.Info("CropID Sync requested forceCacheRefresh:", req.GetForceCacheRefresh())
	m.modelWatcher.TriggerCropIDSync(req.GetForceCacheRefresh())
	return &frontend.Empty{}, nil
}

func FrontEndModelEventTypeMatcherToModelEventTypes(eventTypeMatcher *frontend.ModelEventTypeMatcher) state.ModelEventTypes {
	matcher := state.ModelEventTypes{}
	if eventTypeMatcher.GetRobotStart() {
		matcher = append(matcher, state.RobotStart)
	}
	if eventTypeMatcher.GetPinned() {
		matcher = append(matcher, state.Pinned)
	}
	if eventTypeMatcher.GetUnpinned() {
		matcher = append(matcher, state.UnPinned)
	}
	if eventTypeMatcher.GetRecommended() {
		matcher = append(matcher, state.Recommended)
	}
	if eventTypeMatcher.GetActivated() {
		matcher = append(matcher, state.Activated)
	}
	if eventTypeMatcher.GetNicknameChange() {
		matcher = append(matcher, state.NicknameChange)
	}
	if eventTypeMatcher.GetNicknameDelete() {
		matcher = append(matcher, state.NicknameDelete)
	}
	if eventTypeMatcher.GetDefaultParameterChange() {
		matcher = append(matcher, state.DefaultParameterChange)
	}
	if eventTypeMatcher.GetParameterChange() {
		matcher = append(matcher, state.ParameterChange)
	}
	return matcher
}

func FrontendModelEventToModelEvent(event *frontend.ModelEvent) state.ModelEvent {
	if event == nil {
		return state.ModelEvent{}
	}
	return state.ModelEvent{
		Type:            state.ModelEventTypeFromStr(event.Type),
		ModelID:         event.ModelId,
		ModelNickname:   event.ModelNickname,
		ModelParameters: event.ModelParameters,
		ModelType:       event.ModelType,
		CropID:          event.CropId,
		JobName:         event.JobName,
		Time:            time.UnixMilli(event.Time),
	}
}

func ModelEventToFrontend(event state.ModelEvent) *frontend.ModelEvent {
	return &frontend.ModelEvent{
		Type:            string(event.Type),
		ModelId:         event.ModelID,
		ModelNickname:   event.ModelNickname,
		ModelParameters: event.ModelParameters,
		ModelType:       event.ModelType,
		CropId:          event.CropID,
		JobName:         event.JobName,
		Time:            event.Time.UnixMilli(),
	}
}

func EnabledCropListToFrontend(ecl state.EnabledCropList, lang string) *frontend.EnabledCropList {
	output := new(frontend.EnabledCropList)
	for _, crop := range ecl {
		localizedCrop := crop.Localize(lang)
		enabledCrop := &frontend.EnabledCrop{
			Id:               crop.ID,
			Created:          crop.Created,
			CommonName:       localizedCrop.Name,
			Description:      localizedCrop.Description,
			PinnedModelId:    crop.PinnedModel,
			RecommendedModel: crop.RecommendedModel,
		}
		output.EnabledCrops = append(output.EnabledCrops, enabledCrop)
	}
	return output
}

func ModelInfoToProto(info state.ModelInfo) *frontend.Model {
	lastUsedMillis := info.LastUsed.UnixMilli()
	downloadedMillis := info.Downloaded.UnixMilli()
	if info.LastUsed.IsZero() {
		lastUsedMillis = 0
	}
	if info.Downloaded.IsZero() {
		downloadedMillis = 0
	}
	return &frontend.Model{
		Id:                                  info.ID,
		Crop:                                info.Crop,
		Ts:                                  &frontend.Timestamp{TimestampMs: info.Created},
		Custom:                              len(info.RobotName) > 0 || len(info.Customer) > 0,
		Pinned:                              info.Pinned,
		Active:                              info.Active,
		Synced:                              info.Synced,
		Maintained:                          info.Maintained,
		Nickname:                            info.Nickname,
		SyncedToRows:                        info.RowsSynced,
		Downloading:                         info.Downloading,
		Type:                                string(info.Model.Type),
		LastUsedTimestamp:                   &frontend.Timestamp{TimestampMs: lastUsedMillis},
		DownloadedTimestamp:                 &frontend.Timestamp{TimestampMs: downloadedMillis},
		DownloadingProgress:                 info.DownloadingProgress,
		EstimatedDownloadingRemainingTimeMs: info.RemainingTimeMs,
		Recommended:                         info.Recommended,
		ViableCropIds:                       info.ViableCropIDs,
	}
}

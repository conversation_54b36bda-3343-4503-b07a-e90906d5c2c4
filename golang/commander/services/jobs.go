package services

import (
	"context"
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/metrics_aggregator"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/docker/distribution/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type JobProfile interface {
	GetActiveProfileForJob() (string, string, frontend.ProfileType)
}
type JobsService struct {
	frontend.UnimplementedJobsServiceServer

	st          *state.JobsState
	opState     *state.OperationsState
	jobProfiles []JobProfile

	redis         *redis.Client
	robot         *environment.Robot
	configClient  *config.ConfigClient
	metricsClient *metrics_aggregator.MetricsAggregatorClient
}

func NewJobsService(grpcServer *grpc.Server, st *state.JobsState, opState *state.OperationsState, redis *redis.Client, robot *environment.Robot, configClient *config.ConfigClient, metricsClient *metrics_aggregator.MetricsAggregatorClient, jobProfiles []JobProfile) *JobsService {
	s := &JobsService{
		redis:         redis,
		robot:         robot,
		st:            st,
		opState:       opState,
		configClient:  configClient,
		metricsClient: metricsClient,
		jobProfiles:   jobProfiles,
	}
	frontend.RegisterJobsServiceServer(grpcServer, s)
	return s
}

func (s *JobsService) CreateJob(ctx context.Context, req *frontend.CreateJobRequest) (*frontend.CreateJobResponse, error) {
	jobId := uuid.Generate().String()
	if req.Active {
		err := s.dumpConfigs(jobId)
		if err != nil {
			return nil, err
		}
	}

	cropId := ""
	s.opState.ReadOnCurrent(func() {
		cropId = s.opState.SelectedModel.CropID
	})

	j := &frontend.Job{
		JobDescription: &frontend.JobDescription{
			JobId:       jobId,
			Name:        req.Name,
			TimestampMs: time.Now().UnixMilli(),
		},
		ExpectedAcreage: req.ExpectedAcreage,
		CropId:          cropId,
		ActiveProfiles:  make(map[int32]*frontend.ActiveProfile),
	}
	for _, profile := range s.jobProfiles {
		uuid, name, profileType := profile.GetActiveProfileForJob()
		j.ActiveProfiles[int32(profileType)] = &frontend.ActiveProfile{
			ProfileType: profileType,
			Id:          uuid,
			Name:        name,
		}
		// Backwards compatability, remove once frontend no longer needs
		switch profileType {
		case frontend.ProfileType_ALMANAC:
			j.AlmanacProfileUUID = uuid
		case frontend.ProfileType_BANDING:
			j.BandingProfile = name
			j.BandingProfileUUID = uuid
		case frontend.ProfileType_DISCRIMINATOR:
			j.DiscriminatorProfileUUID = uuid
		case frontend.ProfileType_THINNING:
			j.ThinningProfile = name
			j.ThinningProfileUUID = uuid
		}
	}

	err := s.st.CreateJob(j, req.Active)
	if err != nil {
		return nil, err
	}

	return &frontend.CreateJobResponse{JobId: j.JobDescription.JobId}, nil
}

func (s *JobsService) UpdateJob(ctx context.Context, req *frontend.UpdateJobRequest) (*frontend.Empty, error) {
	err := s.st.UpdateJob(req)
	if err != nil {
		return nil, err
	}
	return &frontend.Empty{}, nil
}

func (s *JobsService) GetNextJobs(ctx context.Context, req *frontend.GetNextJobsRequest) (*frontend.GetNextJobsResponse, error) {
	resp := &frontend.GetNextJobsResponse{
		Jobs: make([]*frontend.JobWithMetrics, 0),
	}

	s.st.ReadOnNext(ctx, req.Timestamp.TimestampMs, func() {
		for _, j := range s.st.AllJobs {
			resp.Jobs = append(resp.Jobs, &frontend.JobWithMetrics{Job: j})
		}
		resp.ActiveJobId = s.st.ActiveJobId
		resp.Timestamp = &frontend.Timestamp{TimestampMs: s.st.GetTimestampMs()}
	})

	for _, jm := range resp.Jobs {
		metrics, err := s.st.ReadMetrics(jm.Job.JobDescription.JobId)
		if err == nil {
			jm.Metrics = metrics
		} else {
			logrus.WithError(err).Errorf("Could not read metrics for job %v", jm.Job.JobDescription.JobId)
		}
	}

	return resp, nil
}

func (s *JobsService) StopActiveJob(ctx context.Context, req *frontend.Empty) (*frontend.Empty, error) {
	err := s.st.StopActiveJob()
	if err != nil {
		return nil, err
	}

	return &frontend.Empty{}, nil
}

func (s *JobsService) GetNextActiveJobId(ctx context.Context, req *frontend.GetNextActiveJobIdRequest) (*frontend.GetNextActiveJobIdResponse, error) {
	var activeJobId string = ""
	var ts int64
	s.st.ReadOnNext(ctx, req.Timestamp.TimestampMs, func() {
		activeJobId = s.st.ActiveJobId
		ts = s.st.GetTimestampMs()
	})

	return &frontend.GetNextActiveJobIdResponse{ActiveJobId: activeJobId, Timestamp: &frontend.Timestamp{TimestampMs: ts}}, nil
}

func (s *JobsService) GetJob(ctx context.Context, req *frontend.GetJobRequest) (*frontend.GetJobResponse, error) {
	job, err := s.st.GetJob(req.JobId)
	if err != nil {
		return nil, err
	}

	return &frontend.GetJobResponse{Job: job}, nil
}

func (s *JobsService) StartJob(ctx context.Context, req *frontend.StartJobRequest) (*frontend.Empty, error) {
	err := s.dumpConfigs(req.JobId)
	if err != nil {
		return nil, err
	}
	err = s.st.StartJob(req.JobId)
	if err != nil {
		return nil, err
	}

	return &frontend.Empty{}, nil
}

func (s *JobsService) dumpConfigs(jobId string) error {
	resp, err := s.configClient.GetTree()
	if err != nil {
		return err
	}
	root := &frontend.ConfigNodeSnapshot{
		Values:     make(map[string]string),
		ChildNodes: make(map[string]*frontend.ConfigNodeSnapshot),
	}
	for _, node := range resp.Node.Children {
		addConfigNode(root, node)
	}
	err = s.st.WriteConfigs(jobId, root)
	if err != nil {
		return err
	}
	return nil
}

func (s *JobsService) GetConfigDump(ctx context.Context, req *frontend.GetConfigDumpRequest) (*frontend.GetConfigDumpResponse, error) {
	root, err := s.st.ReadConfigs(req.JobId)
	if err != nil {
		return nil, err
	}
	return &frontend.GetConfigDumpResponse{RootConfig: root}, nil
}

func (s *JobsService) GetActiveJobMetrics(context.Context, *frontend.Empty) (*frontend.GetActiveJobMetricsResponse, error) {
	resp, err := s.metricsClient.GetJobMetrics()
	if err != nil {
		return nil, err
	}
	return &frontend.GetActiveJobMetricsResponse{JobMetrics: resp.JobMetrics}, nil
}

func (s *JobsService) DeleteJob(ctx context.Context, req *frontend.DeleteJobRequest) (*frontend.Empty, error) {
	s.st.DeleteJob(req.JobId)
	return &frontend.Empty{}, nil
}

func (s *JobsService) MarkJobCompleted(ctx context.Context, req *frontend.MarkJobCompletedRequest) (*frontend.Empty, error) {
	s.st.MarkJobCompleted(req.JobId)
	return &frontend.Empty{}, nil
}

func (s *JobsService) MarkJobIncomplete(ctx context.Context, req *frontend.MarkJobIncompleteRequest) (*frontend.Empty, error) {
	s.st.MarkJobIncomplete(req.JobId)
	return &frontend.Empty{}, nil
}

func (s *JobsService) GetNextJob(ctx context.Context, req *frontend.GetNextJobRequest) (*frontend.GetNextJobResponse, error) {
	resp := &frontend.GetNextJobResponse{}
	var err error
	s.st.ReadOnNext(ctx, req.Ts.TimestampMs, func() {
		if s.st.AllJobs[req.JobId] == nil {
			err = fmt.Errorf("Job with id %v not found", req.JobId)
			return
		}
		resp.Job = &frontend.JobWithMetrics{
			Job: s.st.AllJobs[req.JobId],
		}
		resp.Ts = &frontend.Timestamp{TimestampMs: s.st.GetTimestampMs()}
		metrics, err := s.st.ReadMetrics(req.JobId)
		if err == nil {
			resp.Job.Metrics = metrics
		} else {
			logrus.WithError(err).Errorf("Could not read metrics for job %v", req.JobId)
		}
	})

	return resp, err
}

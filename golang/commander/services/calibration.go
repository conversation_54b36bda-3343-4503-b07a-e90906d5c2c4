package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type CalibrationService struct {
	frontend.UnimplementedCalibrationServiceServer
	camState     *state.OverallCameraState
	rows         map[int]*rows.RowClients
	configClient *config.ConfigClient
}

func NewCalibrationService(grpcServer *grpc.Server, state *state.OverallCameraState, rows map[int]*rows.RowClients, configClient *config.ConfigClient) *CalibrationService {
	s := &CalibrationService{camState: state, rows: rows, configClient: configClient}
	frontend.RegisterCalibrationServiceServer(grpcServer, s)
	return s
}

func (s *CalibrationService) blockUntilWhiteBalanceModified(cam *state.Camera, enableAutoWhitebalance bool) error {
	failures := 0
	for {
		if failures > 5 {
			return errors.New("Couldn't start whitebalancing. Please try again.")
		}
		err := cam.CVRuntimeClient.SetAutoWhitebalance(cam.LocalCameraId, enableAutoWhitebalance)
		if err != nil {
			return err
		}
		time.Sleep(time.Second)
		settings, err := cam.CVRuntimeClient.GetSettings(cam.LocalCameraId)
		if err != nil {
			return err
		}

		if settings.GetCameraSettingsResponse() == nil {
			return errors.New("Settings are nil")
		}

		camSettings := settings.GetCameraSettingsResponse()[0]

		if camSettings.GetAutoWhitebalance() != enableAutoWhitebalance {
			failures += 1
			logrus.Infof("Couldn't modify auto whitebalancing, retrying")
			continue
		}
		break
	}

	return nil
}

func (s *CalibrationService) StartColorCalibration(ctx context.Context, req *frontend.CameraRequest) (*frontend.ColorCalibrationValues, error) {
	logrus.Infof("ColorCal: StartColorCalibration called: cam id %v", req.GetCamId())

	cam := s.camState.Cameras[req.GetCamId()]
	logrus.Infof("ColorCal: Cams: %v", s.camState.Cameras)

	if cam == nil {
		logrus.Infof("ColorCal: No cam with that id %v", req.GetCamId())
		return nil, errors.New(fmt.Sprintf("No camera with cam id %v", req.GetCamId()))
	}
	if cam.Type == state.CameraTypeTarget {
		// Center the cam
		scannerId := cam.CameraIdx + 1
		limits, err := s.rows[int(cam.RowNumber)].AimbotClient.ServoGetLimits(scannerId)
		if err != nil {
			return nil, err
		}
		middlePan := (limits.GetPanMax() + limits.GetPanMin()) / 2
		middleTilt := (limits.GetTiltMax() + limits.GetTiltMin()) / 2
		if !(middlePan == 0 && middleTilt == 0) {
			_, err = s.rows[int(cam.RowNumber)].AimbotClient.ServoGoTo(scannerId, "pan", middlePan, 0, true)
			if err != nil {
				logrus.Errorf("ColorCal: The pan didn't work %v. Cannot reliably color calibrate", err)
				return nil, err
			}
			_, err = s.rows[int(cam.RowNumber)].AimbotClient.ServoGoTo(scannerId, "tilt", middleTilt, 0, true)
			if err != nil {
				logrus.Errorf("ColorCal: The tilt didn't work %v. Cannot reliably color calibrate", err)
				return nil, err
			}
		} else {
			return nil, errors.New("Middle pan and middle tilt are both 0, cannot reliably color calibrate this camera.")
		}
	}

	err := s.blockUntilWhiteBalanceModified(cam, true)

	if err != nil {
		logrus.Errorf("ColorCal: Couldn't start whitebalancing: %v", err)
		return nil, err
	}

	time.Sleep(5 * time.Second)
	settings, err := cam.CVRuntimeClient.GetSettings(cam.LocalCameraId)
	if err != nil {
		logrus.Errorf("ColorCal: Couldn't get settings: %v", err)
		return nil, err
	}

	if settings.GetCameraSettingsResponse() == nil {
		logrus.Errorf("ColorCal: Settings are nil")
		return nil, errors.New("Settings are nil")
	}

	camSettings := settings.GetCameraSettingsResponse()[0]

	red := camSettings.GetWbRatioRed()
	green := camSettings.GetWbRatioGreen()
	blue := camSettings.GetWbRatioBlue()

	logrus.Infof("ColorCal: Got red: %v, green: %v, blue: %v", red, green, blue)

	return &frontend.ColorCalibrationValues{
		Red:   red,
		Green: green,
		Blue:  blue,
		CamId: req.GetCamId(),
	}, nil
}

func (s *CalibrationService) SaveColorCalibration(ctx context.Context, req *frontend.ColorCalibrationValues) (*frontend.Empty, error) {
	cam := s.camState.Cameras[req.GetCamId()]
	prefix := config.GetRowComputerConfigPrefix(int(cam.RowNumber))
	err := s.configClient.SetDoubleValue(fmt.Sprintf("%v/cv/cameras/%v/white_balance/red", prefix, cam.LocalCameraId), float64(req.GetRed()))
	if err != nil {
		logrus.Errorf("ColorCal: Couldn't set red value: %v", err)
		return nil, err
	}
	err = s.configClient.SetDoubleValue(fmt.Sprintf("%v/cv/cameras/%v/white_balance/green", prefix, cam.LocalCameraId), float64(req.GetGreen()))
	if err != nil {
		logrus.Errorf("ColorCal: Couldn't set green value: %v", err)
		return nil, err
	}

	err = s.configClient.SetDoubleValue(fmt.Sprintf("%v/cv/cameras/%v/white_balance/blue", prefix, cam.LocalCameraId), float64(req.GetBlue()))
	if err != nil {
		logrus.Errorf("ColorCal: Couldn't set blue value: %v", err)
		return nil, err
	}

	err = s.blockUntilWhiteBalanceModified(cam, false)
	if err != nil {
		logrus.Errorf("ColorCal: Couldn't stop whitebalancing: %v", err)
		return nil, err
	}

	return &frontend.Empty{}, nil
}

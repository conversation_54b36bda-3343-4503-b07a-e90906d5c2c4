package services

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/rows"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type CameraService struct {
	frontend.UnimplementedCameraServiceServer
	camState   state.ManagedState
	camWatcher *state.CameraWatcher
	env        environment.Robot
	rows       map[int]*rows.RowClients
}

// test points
var (
	frontendRegisterCameraServiceServer = func(s grpc.ServiceRegistrar, srv frontend.CameraServiceServer) {
		frontend.RegisterCameraServiceServer(s, srv)
	}
	camerasDefault = make(map[string]*state.Camera)
)

func NewCameraService(env environment.Robot, grpcServer *grpc.Server, camState *state.OverallCameraState, camWatcher *state.CameraWatcher, rowClients map[int]*rows.RowClients) *CameraService {
	service := &CameraService{
		camState:   camState,
		camWatcher: camWatcher,
		env:        env,
		rows:       rowClients,
	}
	frontendRegisterCameraServiceServer(grpcServer, service)
	return service
}

func (s *CameraService) cameraListFn(inReq interface{}, resp *frontend.CameraList) func() {
	return func() {
		var includeDisconnected bool
		var requestType frontend.CameraType
		switch req := inReq.(type) {
		case *frontend.CameraListRequest:
			includeDisconnected = req.IncludeDisconnected
			requestType = req.Type
		case *frontend.NextCameraListRequest:
			includeDisconnected = req.IncludeDisconnected
			requestType = req.Type
		default:
			logrus.Warnf("unsupported camera list request type: %T", req)
			return
		}

		for _, cam := range camerasFromState(s.camState) {
			if !includeDisconnected && !cam.Connected {
				logrus.Infof("skipping disconnected camera %v, %v", cam.StreamHost, cam.Type)
				continue
			}

			var camType frontend.CameraType
			switch cam.Type {
			case state.CameraTypeTarget:
				camType = frontend.CameraType_TARGET
			case state.CameraTypePredict:
				camType = frontend.CameraType_PREDICT
			case state.CameraTypeKillCam:
				camType = frontend.CameraType_KILLCAM
			default:
				camType = frontend.CameraType_ANY
			}

			if requestType == frontend.CameraType_ANY || requestType == camType {
				row := s.rows[int(cam.RowNumber)]
				rtcInfo := &frontend.RTCInfo{}
				switch {
				case s.env.MakaRole == string(environment.CarbonRoleSimulator):
					rtcInfo.HostId = fmt.Sprintf("%s-%s", s.env.MakaRobotName, s.env.MakaRole)
				case row != nil:
					rtcInfo.HostId = cam.StreamHostName
				}
				if cam.V4l2DeviceId != "" {
					rtcInfo.StreamId = strings.Join([]string{rtcInfo.HostId, cam.V4l2DeviceId}, ":")
				}
				resp.Cameras = append(resp.Cameras, &frontend.Camera{
					RowNumber:     cam.RowNumber,
					CameraId:      cam.GlobalCameraId,
					Type:          camType,
					AutoFocusable: cam.AutoFocusable,
					StreamHost:    cam.StreamHost,
					StreamPort:    cam.StreamPort,
					Width:         cam.Width,
					Height:        cam.Height,
					Transpose:     cam.Transpose,
					Connected:     cam.Connected,
					RtcInfo:       rtcInfo,
				})
			}
		}
		resp.Ts = &frontend.Timestamp{TimestampMs: s.camState.GetTimestampMs()}
	}
}

func (s *CameraService) GetCameraList(ctx context.Context, req *frontend.CameraListRequest) (*frontend.CameraList, error) {
	logrus.Info("GetCameraList called")
	resp := &frontend.CameraList{}
	s.camState.ReadOnCurrent(s.cameraListFn(req, resp))

	SortCameras(resp.Cameras)

	return resp, nil
}

func (s *CameraService) GetNextCameraList(ctx context.Context, req *frontend.NextCameraListRequest) (*frontend.CameraList, error) {
	logrus.Info("GetNextCameraList called")
	resp := &frontend.CameraList{}
	s.camState.ReadOnNext(ctx, req.Ts.GetTimestampMs(), s.cameraListFn(req, resp))

	SortCameras(resp.Cameras)

	return resp, nil
}

func SortCameras(camSlice []*frontend.Camera) {
	sort.SliceStable(camSlice, func(i, j int) bool {
		return camSlice[i].CameraId < camSlice[j].CameraId
	})
}

// camerasFromState bridges the state.OverallCameraState to the ManagedState interface
func camerasFromState(rawCameraState interface{}) map[string]*state.Camera {
	if cs, ok := rawCameraState.(*state.OverallCameraState); ok {
		return cs.Cameras
	}
	return camerasDefault
}

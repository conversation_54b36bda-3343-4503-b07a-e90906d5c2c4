package services

import (
	"context"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/services/mocks"
	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

func TestNewCrosshairService(t *testing.T) {
	saveFrontendRegisterCrosshairServiceServer := frontendRegisterCrosshairServiceServer
	defer func() { frontendRegisterCrosshairServiceServer = saveFrontendRegisterCrosshairServiceServer }()

	testGrpcServer := &grpc.Server{}
	testScannerState := &state.OverallScannerState{}
	testScannerWatcher := &state.ScannerWatcher{}
	testRowClients := make(map[int]*rows.RowClients)
	testCameraState := &state.OverallCameraState{}

	var gotCrosshairSvc frontend.CrosshairServiceServer
	frontendRegisterCrosshairServiceServer = func(s grpc.ServiceRegistrar, srv frontend.CrosshairServiceServer) {
		assert.Equal(t, testGrpcServer, s)
		gotCrosshairSvc = srv
	}
	chSvc := NewCrosshairService(testGrpcServer, testScannerState, testScannerWatcher, testRowClients, testCameraState)

	assert.Equal(t, chSvc, gotCrosshairSvc)
	assert.Equal(t, testScannerState, chSvc.scState)
	assert.Equal(t, testScannerWatcher, chSvc.scWatcher)
	assert.Equal(t, testRowClients, chSvc.rows)
	assert.Equal(t, testCameraState, chSvc.cameraState)
}

func TestCrosshairService_StartAutoCalibrateCrosshair(t *testing.T) {
	testCamID := "some-cam-id"
	tests := []struct {
		name                 string
		scannerExists        bool
		crossHairCalibrating bool
		expectErr            bool
	}{
		{"success", true, false, false},
		{"no scanner", false, false, true},
		{"already calibrating", true, true, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testScannerState := state.ScannerState{Crosshair: state.CrosshairPosition{Calibrating: test.crossHairCalibrating}}

			mockState := new(mocks.OverallScannerState)
			mockState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockState.On("GetScannerState", testCamID).
				Return(testScannerState, test.scannerExists)

			crosshairSvc := &CrosshairService{
				scState: mockState,
			}

			req := &frontend.CameraRequest{CamId: testCamID}
			resp, err := crosshairSvc.StartAutoCalibrateCrosshair(context.TODO(), req)
			if test.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
			}
		})
	}
}

func TestCrosshairService_StartAutoCalibrateAllCrosshairs(t *testing.T) {
	t.Skip("todo - implement StartAutoCalibrateAllCrosshairs")
	tests := []struct {
		name string
	}{
		{"todo"},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			crosshairSvc := &CrosshairService{}
			req := &frontend.Empty{}
			resp, err := crosshairSvc.StartAutoCalibrateAllCrosshairs(context.TODO(), req)
			assert.NotNil(t, resp)
			assert.NoError(t, err)
		})
	}
}

func TestCrosshairService_StopAutoCalibrate(t *testing.T) {
	t.Skip("todo - implement StopAutoCalibrate")
	tests := []struct {
		name string
	}{
		{"todo"},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			crosshairSvc := &CrosshairService{}
			req := &frontend.Empty{}
			resp, err := crosshairSvc.StopAutoCalibrate(context.TODO(), req)
			assert.NotNil(t, resp)
			assert.NoError(t, err)
		})
	}
}

func TestCrosshairService_GetNextCrosshairState(t *testing.T) {
	testTimestamp := time.Now().UnixMilli()

	testCamID := "test-cam-id"
	testCamera := &state.Camera{Width: 100, Height: 100}
	testScannerState := state.ScannerState{
		Crosshair: state.CrosshairPosition{
			Calibrating: true,
			X:           100,
			Y:           100}}
	tests := []struct {
		name             string
		readOnNextResult bool
		camExists        bool
		scannerExists    bool
		expectErr        bool
	}{
		{"success", true, true, true, false},
		{"no camera", true, false, true, true},
		{"read on next fail", false, true, true, true},
		{"no scanner", true, true, false, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockCamState := new(mocks.OverallCameraState)
			mockCamState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockCamState.On("GetCamera", testCamID).
				Return(testCamera, test.camExists)

			mockScannerState := new(mocks.OverallScannerState)
			mockScannerState.On("GetTimestampMs").Return(testTimestamp)
			mockScannerState.On("ReadOnNext",
				mock.AnythingOfType("context.todoCtx"),
				mock.MatchedBy(func(ts int64) bool { return assert.Equal(t, testTimestamp, ts) }),
				mock.AnythingOfType("func()"),
			).Return(test.readOnNextResult).
				Run(func(args mock.Arguments) {
					fn := args.Get(2).(func())
					fn()
				})
			mockScannerState.On("GetScannerState", testCamID).
				Return(testScannerState, test.scannerExists)

			crosshairSvc := &CrosshairService{
				scState:     mockScannerState,
				cameraState: mockCamState,
			}
			req := &frontend.CrosshairPositionRequest{
				CamId: testCamID,
				Ts:    &frontend.Timestamp{TimestampMs: testTimestamp}}
			resp, err := crosshairSvc.GetNextCrosshairState(context.TODO(), req)
			if test.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp.Pos)
			}
		})
	}
}

func TestCrosshairService_SetCrosshairPosition(t *testing.T) {
	saveGetAimbotClient := getAimbotClient
	defer func() {
		getAimbotClient = saveGetAimbotClient
	}()

	testCamID := "test-cam-id"
	camH := uint32(3)
	camW := uint32(2)
	crossHairX := float32(100)
	crossHairY := float32(100)
	expectedX := uint32(crossHairX * float32(camW))
	expectedY := uint32(crossHairY * float32(camH))

	tests := []struct {
		name               string
		camExists          bool
		scannerExists      bool
		aimbotClientExists bool
		expectErr          bool
	}{
		{"success", true, true, true, false},
		{"no camera", false, true, true, true},
		{"no scanner", true, false, true, true},
		{"no aimbot client", true, true, false, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockScannerState := new(mocks.OverallScannerState)
			mockScannerState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockScannerState.On("GetScannerState", testCamID).
				Return(state.ScannerState{Descriptor: state.ScannerDescriptor{RowNumber: 1, ScannerId: 1}}, test.scannerExists)

			mockCameraState := new(mocks.OverallCameraState)
			mockCameraState.On("GetCamera", testCamID).
				Return(&state.Camera{Width: camW, Height: camH}, test.camExists)

			mockAimbotClient := new(mocks.AimbotClient)
			mockAimbotClient.On("SetCrosshairPosition", uint32(1), expectedX, expectedY).Return(nil)

			getAimbotClient = func(rowClients map[int]*rows.RowClients, row int) (AimbotClient, bool) {
				return mockAimbotClient, test.aimbotClientExists
			}
			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockScWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}
			crosshairSvc := &CrosshairService{
				cameraState: mockCameraState,
				scState:     mockScannerState,
				scWatcher:   mockScWatcher,
			}
			req := &frontend.SetCrosshairPositionRequest{
				CamId: testCamID,
				Pos:   &frontend.CrosshairPosition{X: crossHairX, Y: crossHairY},
			}
			resp, err := crosshairSvc.SetCrosshairPosition(context.TODO(), req)
			if test.expectErr {
				assert.Error(t, err)
			} else {
				assert.NotNil(t, resp)
				assert.NoError(t, err)
				assert.Equal(t, 1, triggerCount)
				mockAimbotClient.AssertExpectations(t)
				mockScannerState.AssertExpectations(t)
			}
		})
	}
}

func TestCrosshairService_MoveScanner(t *testing.T) {
	saveGetAimbotClient := getAimbotClient
	defer func() {
		getAimbotClient = saveGetAimbotClient
	}()

	testCamID := "some-cam-id"
	camH := uint32(3)
	camW := uint32(2)
	reqX := float32(100)
	reqY := float32(100)
	expectedX := uint32(reqX * float32(camW))
	expectedY := uint32(reqY * float32(camH))

	tests := []struct {
		name               string
		camExists          bool
		scannerExists      bool
		aimbotClientExists bool
		expectErr          bool
	}{
		{"success", true, true, true, false},
		{"no camera", false, true, true, true},
		{"no scanner", true, false, true, true},
		{"no aimbot client", true, true, false, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockCameraState := new(mocks.OverallCameraState)
			mockCameraState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockCameraState.On("GetCamera", testCamID).
				Return(&state.Camera{Height: camH, Width: camW}, test.camExists)

			mockScannerState := new(mocks.OverallScannerState)
			mockScannerState.On("ReadOnCurrent",
				mock.AnythingOfType("func()"),
			).Return().
				Run(func(args mock.Arguments) {
					fn := args.Get(0).(func())
					fn()
				})
			mockScannerState.On("GetScannerState", testCamID).
				Return(state.ScannerState{Descriptor: state.ScannerDescriptor{RowNumber: 1, ScannerId: 1}}, test.scannerExists)

			mockAimbotClient := new(mocks.AimbotClient)
			mockAimbotClient.On("MoveScanner", uint32(1), expectedX, expectedY).Return(nil)

			getAimbotClient = func(rowClients map[int]*rows.RowClients, row int) (AimbotClient, bool) {
				return mockAimbotClient, test.aimbotClientExists
			}
			triggerCount := 0
			triggerFn := func() {
				triggerCount++
			}
			mockScWatcher := &mockTriggerable{
				TriggerFn: triggerFn,
				EventCh:   make(chan bool, 10),
			}
			crosshairSvc := &CrosshairService{
				cameraState: mockCameraState,
				scState:     mockScannerState,
				scWatcher:   mockScWatcher,
			}
			req := &frontend.MoveScannerRequest{
				CamId: testCamID,
				X:     reqX, Y: reqY,
			}
			resp, err := crosshairSvc.MoveScanner(context.TODO(), req)
			if test.expectErr {
				assert.Error(t, err)
			} else {
				assert.NotNil(t, resp)
				assert.NoError(t, err)
				assert.Equal(t, 1, triggerCount)
				mockAimbotClient.AssertExpectations(t)
				mockScannerState.AssertExpectations(t)
			}
		})
	}
}

func TestGetCrosshairPositionFn(t *testing.T) {
	testTimestamp := time.Now().UnixMilli()

	testCamID := "some-cam-id"
	camH := uint32(3)
	camW := uint32(2)
	testScannerCrosshairX := uint32(300)
	testScannerCrosshairY := uint32(200)
	expectedX := float32(testScannerCrosshairX) / float32(camW)
	expectedY := float32(testScannerCrosshairY) / float32(camH)

	tests := []struct {
		name          string
		scannerExists bool
		calbrating    bool
	}{
		{"success scanner calibrating", true, true},
		{"scanner not calibrating", true, false},
		{"no scanner", false, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(mocks.OverallScannerState)
			mockState.On("GetTimestampMs").Return(testTimestamp)
			mockState.On("GetScannerState", testCamID).
				Return(state.ScannerState{Crosshair: state.CrosshairPosition{
					Calibrating: test.calbrating,
					X:           testScannerCrosshairX,
					Y:           testScannerCrosshairY}}, test.scannerExists)

			crosshairSvc := &CrosshairService{
				scState: mockState,
			}
			req := &frontend.CrosshairPositionRequest{CamId: testCamID}
			resp := &frontend.CrosshairPositionState{}
			crosshairSvc.getCrosshairPositionFn(&state.Camera{Width: camW, Height: camH}, req, resp)()
			assert.Equal(t, testTimestamp, resp.Ts.TimestampMs)
			if test.scannerExists {
				assert.NotNil(t, resp.Pos)
				assert.Equal(t, expectedX, resp.Pos.X)
				assert.Equal(t, expectedY, resp.Pos.Y)
				assert.Equal(t, test.calbrating, resp.Calibrating)
				mockState.AssertExpectations(t)
			} else {
				assert.Nil(t, resp.Pos)
				assert.False(t, resp.Calibrating)
			}
		})
	}
}

func TestGetScannerStateFn(t *testing.T) {
	testCamID := "some-cam-id"
	testScannerState := state.ScannerState{}
	tests := []struct {
		name          string
		scannerExists bool
	}{
		{"scanner exists", true},
		{"scanner doesn't exists", false},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(mocks.OverallScannerState)
			mockState.On("GetScannerState", testCamID).
				Return(testScannerState, test.scannerExists)

			crosshairSvc := &CrosshairService{
				scState: mockState,
			}
			resp := new(getScannerResp)
			crosshairSvc.getScannerStateFn(resp, testCamID)()
			if test.scannerExists {
				assert.Equal(t, &testScannerState, resp.ScannerState)
			} else {
				assert.Nil(t, resp.ScannerState)
			}
		})
	}
}

func TestGetCameraFn(t *testing.T) {
	testCamID := "some-cam-id"
	testCamera := new(state.Camera)

	tests := []struct {
		name      string
		camExists bool
	}{
		{"cam exists", true},
		{"cam doesn't exists", false},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockState := new(mocks.OverallCameraState)
			mockState.On("GetCamera", testCamID).
				Return(testCamera, test.camExists)

			crosshairSvc := &CrosshairService{
				cameraState: mockState,
			}

			resp := new(getCamResp)
			crosshairSvc.getCameraFn(resp, testCamID)()
			if test.camExists {
				assert.Equal(t, testCamera, resp.Camera)
			} else {
				assert.Nil(t, resp.Camera)
			}
		})
	}
}

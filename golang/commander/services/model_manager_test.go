package services

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/stretchr/testify/assert"
)

func TestEnabledCropListToFrontend(t *testing.T) {
	tests := []struct {
		name            string
		enabledCropList state.EnabledCropList
		language        string
		expected        []*frontend.EnabledCrop
	}{
		{
			"happy path, no translation",
			state.EnabledCropList{
				{
					Crop: veselka.Crop{
						ID:           "foo-id",
						Created:      123,
						CommonName:   "bar-name",
						Description:  "blah blah",
						Translations: nil,
					},
					PinnedModel:      "pinned 123",
					RecommendedModel: "recommended 123",
				},
			},
			"",
			[]*frontend.EnabledCrop{
				{
					Id:               "foo-id",
					Created:          123,
					CommonName:       "bar-name",
					Description:      "blah blah",
					PinnedModelId:    "pinned 123",
					RecommendedModel: "recommended 123",
				},
			},
		},
		{
			"happy path, with translation",
			state.EnabledCropList{
				{
					Crop: veselka.Crop{
						ID:          "foo-id",
						Created:     123,
						CommonName:  "bar-name",
						Description: "blah blah",
						Translations: []veselka.CropTranslation{
							{
								Name:        "foo in french",
								Description: "french desc",
								Language:    "fr",
								Version:     1,
							},
						},
					},
					PinnedModel:      "pinned 123",
					RecommendedModel: "recommended 123",
				},
			},
			"fr",
			[]*frontend.EnabledCrop{
				{
					Id:               "foo-id",
					Created:          123,
					CarbonName:       "foo-name",
					CommonName:       "foo in french",
					Description:      "french desc",
					PinnedModelId:    "pinned 123",
					RecommendedModel: "recommended 123",
				},
			},
		},
		{
			"translate with default",
			state.EnabledCropList{
				{
					Crop: veselka.Crop{
						ID:          "foo-id",
						Created:     123,
						CommonName:  "bar-name",
						Description: "blah blah",
						Translations: []veselka.CropTranslation{
							{
								Name:        "", // no french name
								Description: "french desc",
								Language:    "fr",
								Version:     1,
							},
						},
					},
					PinnedModel:      "pinned 123",
					RecommendedModel: "recommended 123",
				},
			},
			"fr",
			[]*frontend.EnabledCrop{
				{
					Id:               "foo-id",
					Created:          123,
					CommonName:       "bar-name",
					Description:      "french desc",
					PinnedModelId:    "pinned 123",
					RecommendedModel: "recommended 123",
				},
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := EnabledCropListToFrontend(test.enabledCropList, test.language)
			verifyFrontendEnabledCrop(t, test.expected, got.EnabledCrops)
		})
	}
}

func verifyFrontendEnabledCrop(t *testing.T, expected, got []*frontend.EnabledCrop) {
	t.Helper()
	assert.Len(t, got, len(expected))
	for _, expectedCrop := range expected {
		found := false
		for _, gotCrop := range got {
			if expectedCrop.Id == gotCrop.Id {
				found = true
				assert.Equal(t, expectedCrop.Id, gotCrop.Id)
				assert.Equal(t, expectedCrop.Created, gotCrop.Created)
				assert.Equal(t, expectedCrop.CommonName, gotCrop.CommonName)
				assert.Equal(t, expectedCrop.Description, gotCrop.Description)
				assert.Equal(t, expectedCrop.PinnedModelId, gotCrop.PinnedModelId)
				assert.Equal(t, expectedCrop.RecommendedModel, gotCrop.RecommendedModel)
			}
		}
		assert.True(t, found, "expected enabled crop with id %s but not found", expectedCrop.Id)
	}
}

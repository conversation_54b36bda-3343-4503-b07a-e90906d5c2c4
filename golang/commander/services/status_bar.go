package services

import (
	"context"
	"errors"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type StatusBarService struct {
	frontend.UnimplementedStatusBarServiceServer
	opState            *state.OperationsState
	supportPhoneConfig *config.ConfigTree
	portalClient       *portal_clients.Client
	robotSerial        string
	software           *state.OverallSoftwareState
}

func NewStatusBarService(grpcServer *grpc.Server, opState *state.OperationsState, supportPhoneConfig *config.ConfigTree, portalClient *portal_clients.Client, robotSerial string, software *state.OverallSoftwareState) *StatusBarService {
	service := &StatusBarService{
		opState:            opState,
		supportPhoneConfig: supportPhoneConfig,
		portalClient:       portalClient,
		robotSerial:        robotSerial,
		software:           software,
	}
	frontend.RegisterStatusBarServiceServer(grpcServer, service)
	return service
}

func (s *StatusBarService) GetNextStatus(ctx context.Context, req *frontend.Timestamp) (*frontend.StatusBarMessage, error) {
	var ts int64 = req.GetTimestampMs()
	resp := &frontend.StatusBarMessage{}
	resp.Serial = s.robotSerial
	result := s.opState.ReadOnNext(ctx, ts, func() {
		resp.StatusLevel = s.opState.Status
		resp.StatusMessage = s.opState.StatusMessage
		resp.TranslatedStatusMessage = s.opState.TranslatedStatusMessage
		resp.LasersEnabled = true
		var weedingEnabled bool = false
		for _, val := range s.opState.RowWeeding {
			weedingEnabled = weedingEnabled || val.ActualTargeting.Enabled()
		}
		resp.WeedingEnabled = weedingEnabled
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.opState.GetTimestampMs(),
		}
		commandStatusLevel := frontend.StatusLevel_LOADING
		if s.opState.CommandStatus.Ready {
			commandStatusLevel = frontend.StatusLevel_READY
		}
		resp.CommandStatus = &frontend.ServerStatus{StatusLevel: commandStatusLevel}
		for _, service := range s.opState.CommandStatus.ServicesState {
			serviceStatusLevel := frontend.StatusLevel_LOADING
			if service.Ready {
				serviceStatusLevel = frontend.StatusLevel_READY
			}
			resp.CommandStatus.ServiceStatus = append(resp.CommandStatus.ServiceStatus, &frontend.ServiceStatus{StatusLevel: serviceStatusLevel, Name: service.Name})
		}
		resp.RowStatus = make(map[int32]*frontend.ServerStatus)
		for rowIndex, row := range s.opState.RowStatuses {
			rowStatusLevel := frontend.StatusLevel_LOADING
			if row.Ready {
				rowStatusLevel = frontend.StatusLevel_READY
			}
			rowStatus := &frontend.ServerStatus{StatusLevel: rowStatusLevel}
			for _, service := range row.ServicesState {
				serviceStatusLevel := frontend.StatusLevel_LOADING
				if service.Ready {
					serviceStatusLevel = frontend.StatusLevel_READY
				}
				rowStatus.ServiceStatus = append(rowStatus.ServiceStatus, &frontend.ServiceStatus{StatusLevel: serviceStatusLevel, Name: service.Name})
			}
			resp.RowStatus[int32(rowIndex)] = rowStatus
		}
	})

	if result {
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Status Retrieved")
}

func (s *StatusBarService) ReportIssue(ctx context.Context, req *frontend.ReportIssueRequest) (*frontend.Empty, error) {
	// skip logging if there is no portal client
	if s.portalClient == nil {
		return nil, errors.New("Failed to report issue: no portal client")
	}
	crop := "Invalid"
	cropID := "Invalid"
	model := "Invalid"
	s.opState.ReadOnCurrent(func() {
		crop = s.opState.SelectedModel.Crop
		cropID = s.opState.SelectedModel.CropID
		model = s.opState.SelectedModel.Model
	})
	software := "Invalid"
	s.software.ReadOnCurrent(func() {
		software = s.software.Summary.Current.Tag
	})
	_, err := s.portalClient.ReportIssue(&portal.IssueReport{
		Description:     req.Description,
		PhoneNumber:     req.PhoneNumber,
		RobotSerial:     s.robotSerial,
		Crop:            crop,
		CropId:          cropID,
		ModelId:         model,
		ReportedAt:      time.Now().UnixMilli(),
		SoftwareVersion: software,
	})

	if err != nil {
		logrus.WithError(err).Error("Error reporting issue to portal")
		return nil, status.Error(codes.Aborted, "Failed To Report Issue / Contact support manually by phone")
	}

	return &frontend.Empty{}, nil
}

func (s *StatusBarService) GetSupportPhone(ctx context.Context, req *frontend.Empty) (*frontend.SupportPhoneMessage, error) {
	resp := &frontend.SupportPhoneMessage{
		SupportPhone: s.supportPhoneConfig.GetStringValue(),
	}
	return resp, nil
}

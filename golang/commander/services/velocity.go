package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	tve "github.com/carbonrobotics/robot/golang/generated/proto/target_velocity_estimator"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type TargetVelocityEstimatorProfile interface {
	GetNextActiveProfile(ts int64, ctx context.Context) (bool, *tve.TVEProfile, int64)
	GetNextAvailableProfiles(ts int64, available *[]*tve.ProfileDetails, ctx context.Context) (bool, int64)
	LoadProfile(id string) (*tve.TVEProfile, error)
	SaveProfile(profile *tve.TVEProfile, set_active bool) (string, error)
	SetActiveProfile(id string) error
	DeleteProfile(id string, new_active_id string) error
}

type TargetVelocityEstimatorService struct {
	frontend.UnimplementedTargetVelocityEstimatorServiceServer
	targetVelEstimatorProfile TargetVelocityEstimatorProfile
}

func NewTVEProfileService(grpcServer *grpc.Server, targetVelEstimatorProfile TargetVelocityEstimatorProfile) *TargetVelocityEstimatorService {
	service := &TargetVelocityEstimatorService{
		targetVelEstimatorProfile: targetVelEstimatorProfile,
	}
	frontend.RegisterTargetVelocityEstimatorServiceServer(grpcServer, service)
	return service
}

func (s TargetVelocityEstimatorService) GetNextAvailableProfiles(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextAvailableTVEProfilesResponse, error) {
	resp := &frontend.GetNextAvailableTVEProfilesResponse{}
	success, ts := s.targetVelEstimatorProfile.GetNextAvailableProfiles(req.GetTimestampMs(), &resp.Profiles, ctx)
	if !success {
		return nil, status.Error(codes.Aborted, "Context Cancelled before next available TVE profiles Retrieved")
	}
	resp.Ts = &frontend.Timestamp{
		TimestampMs: ts,
	}
	return resp, nil
}
func (s TargetVelocityEstimatorService) GetNextActiveProfile(ctx context.Context, req *frontend.Timestamp) (*frontend.GetNextActiveTVEProfileResponse, error) {
	success, profile, ts := s.targetVelEstimatorProfile.GetNextActiveProfile(req.GetTimestampMs(), ctx)
	if !success {
		return nil, status.Error(codes.Aborted, "Context Cancelled before next active TVE profile Retrieved")
	}
	return &frontend.GetNextActiveTVEProfileResponse{Ts: &frontend.Timestamp{TimestampMs: ts}, Profile: profile}, nil
}
func (s TargetVelocityEstimatorService) LoadProfile(ctx context.Context, req *frontend.LoadTVEProfileRequest) (*frontend.LoadTVEProfileResponse, error) {
	profile, err := s.targetVelEstimatorProfile.LoadProfile(req.GetId())
	if err != nil {
		return nil, err
	}
	return &frontend.LoadTVEProfileResponse{Profile: profile}, nil
}
func (s TargetVelocityEstimatorService) SaveProfile(ctx context.Context, req *frontend.SaveTVEProfileRequest) (*frontend.SaveTVEProfileResponse, error) {
	id, err := s.targetVelEstimatorProfile.SaveProfile(req.GetProfile(), req.GetSetActive())
	if err != nil {
		return nil, err
	}
	return &frontend.SaveTVEProfileResponse{Id: id}, nil
}
func (s TargetVelocityEstimatorService) SetActive(ctx context.Context, req *frontend.SetActiveTVEProfileRequest) (*frontend.SetActiveTVEProfileResponse, error) {
	err := s.targetVelEstimatorProfile.SetActiveProfile(req.GetId())
	if err != nil {
		return nil, err
	}
	return &frontend.SetActiveTVEProfileResponse{}, nil
}
func (s TargetVelocityEstimatorService) DeleteProfile(ctx context.Context, req *frontend.DeleteTVEProfileRequest) (*frontend.DeleteTVEProfileResponse, error) {
	err := s.targetVelEstimatorProfile.DeleteProfile(req.GetId(), req.GetNewActiveId())
	if err != nil {
		return nil, err
	}
	return &frontend.DeleteTVEProfileResponse{}, nil
}

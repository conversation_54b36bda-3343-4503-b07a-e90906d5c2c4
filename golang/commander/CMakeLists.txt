add_go_executable(commander main.go gopkg_proto_proto_frontend gopkg_proto_proto_data_upload_manager gopkg_proto_proto_hardware_manager gopkg_proto_proto_host_check gopkg_proto_proto_model_receiver gopkg_proto_proto_portal config_client_lib go_swig_config_client generate_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_go generate_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_go-grpc generate_config_api_proto_config_service_go generate_config_api_proto_config_service_go-grpc generate_frontend_proto_camera_go generate_frontend_proto_camera_go-grpc generate_proto_logging_logging_go generate_proto_logging_logging_go-grpc metrics)

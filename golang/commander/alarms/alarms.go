package alarms

import (
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/host_check"
)

type Subsystem string

const (
	Computer       Subsystem = "CO"
	Chiller                  = "CH"
	Generator                = "G"
	Tractor                  = "T"
	Scanner                  = "S"
	Wheels                   = "W"
	GPS                      = "GP"
	LTE                      = "LT"
	Electrical               = "E"
	Camera                   = "C"
	Lights                   = "LI"
	Software                 = "SW"
	Height                   = "H"
	Enclosure                = "EC"
	Laser                    = "L"
	Engine                   = "EG"
	SafetyPLC                = "SA"
	SupervisoryPLC           = "SU"
	Models                   = "MD"
	Storage                  = "ST"
	WeedTracking             = "WT"
	CVRuntime                = "CV"
	Authentication           = "AX"
)

type AlarmCode struct {
	Subsystem Subsystem
	Number    uint32
}

func (c *AlarmCode) String() string {
	return fmt.Sprintf("%s%03d", c.Subsystem, c.Number)
}

type AlarmImpact int32

const (
	AlarmImpactUndefined AlarmImpact = iota
	AlarmImpactNone                  // weeding is fine - please be aware
	AlarmImpactDegraded              // weeding may still work but not at performance
	AlarmImpactOffline               // weeding has stopped eg. gpufail
	AlarmImpactCritical              // shut down asap eg. tube broke
)

type AlarmLevel int32

const (
	AlarmLevelUnknown AlarmLevel = iota
	AlarmLevelCritical
	AlarmLevelHigh
	AlarmLevelMedium
	AlarmLevelLow
	AlarmLevelHidden
)

type Alarm struct {
	Identifier            string
	TimestampMs           int64
	AlarmCode             AlarmCode
	Description           string
	Level                 AlarmLevel
	Impact                AlarmImpact
	RowHost               int
	Param                 int32
	Acknowledged          bool
	Valid                 bool // Used to hide flickering alarms
	AutofixFunc           func() error
	AutofixAttempted      bool
	AutofixDurationSec    uint32
	DescriptionKey        string
	TranslationParameters []*frontend.TranslationParameter
	ModuleId              *uint32
}

func NewAlarm(identifier string, code AlarmCode, description string, level AlarmLevel, impact AlarmImpact, descriptionKey string, translationParams []*frontend.TranslationParameter, moduleId *uint32) *Alarm {
	return &Alarm{
		Identifier:            identifier,
		AlarmCode:             code,
		Description:           description,
		Level:                 level,
		Impact:                impact,
		Acknowledged:          false,
		TimestampMs:           time.Now().UnixMilli(),
		Valid:                 false,
		DescriptionKey:        descriptionKey,
		TranslationParameters: translationParams,
		ModuleId:              moduleId,
	}
}

func Level2Frontend(inlevel AlarmLevel) frontend.AlarmLevel {
	var outlevel frontend.AlarmLevel
	switch inlevel {
	case AlarmLevelUnknown:
		outlevel = frontend.AlarmLevel_AL_UNKNOWN
	case AlarmLevelHidden:
		outlevel = frontend.AlarmLevel_AL_HIDDEN
	case AlarmLevelLow:
		outlevel = frontend.AlarmLevel_AL_LOW
	case AlarmLevelMedium:
		outlevel = frontend.AlarmLevel_AL_MEDIUM
	case AlarmLevelHigh:
		outlevel = frontend.AlarmLevel_AL_HIGH
	case AlarmLevelCritical:
		outlevel = frontend.AlarmLevel_AL_CRITICAL
	default:
		outlevel = frontend.AlarmLevel_AL_UNKNOWN
	}
	return outlevel
}

func Impact2Frontend(inImpact AlarmImpact) frontend.AlarmImpact {
	var outImpact frontend.AlarmImpact
	switch inImpact {
	case AlarmImpactUndefined:
		outImpact = frontend.AlarmImpact_AI_UNKNOWN
	case AlarmImpactNone:
		outImpact = frontend.AlarmImpact_AI_NONE
	case AlarmImpactDegraded:
		outImpact = frontend.AlarmImpact_AI_DEGRADED
	case AlarmImpactOffline:
		outImpact = frontend.AlarmImpact_AI_OFFLINE
	case AlarmImpactCritical:
		outImpact = frontend.AlarmImpact_AI_CRITICAL
	default:
		outImpact = frontend.AlarmImpact_AI_UNKNOWN
	}
	return outImpact
}

func Host2Level(inlevel host_check.AlarmLevel) AlarmLevel {
	var outlevel AlarmLevel
	switch inlevel {
	case host_check.AlarmLevel_AL_Unknown:
		outlevel = AlarmLevelUnknown
	case host_check.AlarmLevel_AL_Critical:
		outlevel = AlarmLevelCritical
	case host_check.AlarmLevel_AL_High:
		outlevel = AlarmLevelHigh
	case host_check.AlarmLevel_AL_Medium:
		outlevel = AlarmLevelMedium
	case host_check.AlarmLevel_AL_Low:
		outlevel = AlarmLevelLow
	case host_check.AlarmLevel_AL_Hidden:
		outlevel = AlarmLevelHidden
	}
	return outlevel
}

func Host2Impact(inimpact host_check.AlarmImpact) AlarmImpact {
	var outimpact AlarmImpact
	switch inimpact {
	case host_check.AlarmImpact_AI_None:
		outimpact = AlarmImpactNone
	case host_check.AlarmImpact_AI_Degraded:
		outimpact = AlarmImpactDegraded
	case host_check.AlarmImpact_AI_Offline:
		outimpact = AlarmImpactOffline
	case host_check.AlarmImpact_AI_Critical:
		outimpact = AlarmImpactCritical
	default:
		outimpact = AlarmImpactUndefined
	}
	return outimpact
}

func Host2Code(incode host_check.AlarmCode) AlarmCode {
	outcode := AlarmCode{
		Subsystem: Computer,
	}
	switch incode {
	case host_check.AlarmCode_A_NOALARM:
		outcode.Number = 0
	case host_check.AlarmCode_A_GPUFAIL:
		outcode.Number = 5
	case host_check.AlarmCode_A_GPUOVERTEMP:
		outcode.Number = 6
	case host_check.AlarmCode_A_DISKSPACE:
		outcode.Number = 1
	case host_check.AlarmCode_A_PTPCLOCK:
		outcode.Number = 7
	case host_check.AlarmCode_A_DISKSPACE_ERROR:
		outcode.Number = 8
	case host_check.AlarmCode_A_DISKSPACE_WARN:
		outcode.Number = 9
	case host_check.AlarmCode_A_GPU_CHECKER_FAIL:
		outcode.Number = 10
	case host_check.AlarmCode_A_NETWORK_CHECKER_FAIL:
		outcode.Number = 11
	case host_check.AlarmCode_A_NETWORK:
		outcode.Number = 12
	}
	return outcode
}

func Alarm2Frontend(inalarm *Alarm) *frontend.AlarmRow {
	return &frontend.AlarmRow{
		TimestampMs:           inalarm.TimestampMs,
		AlarmCode:             inalarm.AlarmCode.String(),
		Description:           inalarm.Description,
		Subsystem:             "",
		Level:                 Level2Frontend(inalarm.Level),
		Identifier:            inalarm.Identifier,
		Acknowledged:          inalarm.Acknowledged,
		Impact:                Impact2Frontend(inalarm.Impact),
		AutofixAvailable:      inalarm.AutofixFunc != nil,
		AutofixAttempted:      inalarm.AutofixAttempted,
		AutofixDurationSec:    inalarm.AutofixDurationSec,
		DescriptionKey:        inalarm.DescriptionKey,
		TranslationParameters: inalarm.TranslationParameters,
	}
}

package state

import (
	"context"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"

	"github.com/sirupsen/logrus"
)

type UploadTriggerConnector struct {
	trigger<PERSON>han chan struct{}
}

func (s *UploadTriggerConnector) TriggerUpload() {
	if s == nil {
		return
	}
	select {
	case s.trigger<PERSON>han <- struct{}{}:
	default:
	}
}

func (s *UploadTriggerConnector) UploadTriggered() <-chan struct{} {
	return s.triggerChan
}

type ReaperConfigurationUploader struct {
	portalClient *portal_clients.Client

	moduleOrchestratorState *ModuleOrchestratorState
	robotDefinitionState    *RobotDefinitionState
	connector               *UploadTriggerConnector

	wg sync.WaitGroup

	logger *logrus.Entry
}

func NewReaperConfigurationUploader(
	stopCtx context.Context,
	portalClient *portal_clients.Client,
	moduleOrchestratorState *ModuleOrchestratorState,
	robotDefinitionState *RobotDefinitionState,
	connector *UploadTriggerConnector,
) *ReaperConfigurationUploader {
	uploader := &ReaperConfigurationUploader{
		portalClient:            portalClient,
		moduleOrchestratorState: moduleOrchestratorState,
		robotDefinitionState:    robotDefinitionState,
		connector:               connector,
		logger:                  logrus.WithField("module", "ReaperConfigurationUploader"),
	}

	uploader.wg.Add(1)
	go uploader.uploadControlLoop(stopCtx)
	uploader.connector.TriggerUpload() // upload on boot
	return uploader
}

func (s *ReaperConfigurationUploader) Terminate() {
	s.wg.Wait()
}

func (s *ReaperConfigurationUploader) uploadControlLoop(stopCtx context.Context) {
	defer s.wg.Done()
	for {
		select {
		case <-stopCtx.Done():
		case <-s.connector.UploadTriggered():
			s.uploadReaperConfiguration(stopCtx)
		}
	}
}

func (s *ReaperConfigurationUploader) uploadReaperConfiguration(stopCtx context.Context) {
	assignedModules := make([]*frontend.ModuleIdentity, 0)
	s.moduleOrchestratorState.ReadOnCurrent(func() {
		for _, m := range s.moduleOrchestratorState.AssignedModules {
			assignedModules = append(assignedModules, &frontend.ModuleIdentity{
				Id:     m.ID,
				Serial: m.Serial,
			})
		}
	})

	var robotDefinition *frontend.RobotDefinition
	s.robotDefinitionState.ReadOnCurrent(
		func() {
			if !s.robotDefinitionState.CurrentDefinition.IsValid() {
				return
			}
			robotDefinition = s.robotDefinitionState.CurrentDefinition.ToProto()
		},
	)

	err := s.portalClient.UploadReaperConfiguration(stopCtx, assignedModules, robotDefinition)
	if err != nil {
		s.logger.WithError(err).Info("Failed to upload reaper configuration")
		return
	}

	s.logger.WithError(err).Info("Reaper configuration uploaded")
}

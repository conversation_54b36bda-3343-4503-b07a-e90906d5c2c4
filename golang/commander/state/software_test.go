package state

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/software_manager"
	"github.com/stretchr/testify/assert"
)

const (
	ready        = true
	notReady     = false
	available    = true
	notAvailable = false
	updating     = true
	notUpdating  = false
	active       = true
	notActive    = false
)

func buildSoftwareVersionMeta(tag string, ready, available bool) *software_manager.SoftwareVersionMetadata {
	return &software_manager.SoftwareVersionMetadata{
		Tag:       tag,
		Ready:     ready,
		Available: available,
	}
}

func buildSoftwareVersion(tag string, ready, available bool) *SoftwareVersion {
	return &SoftwareVersion{
		Tag:       tag,
		Ready:     ready,
		Available: available,
	}
}

func TestOverallSoftwareState_FillVersionConditional(t *testing.T) {
	tests := []struct {
		name     string
		states   []*SoftwareVersion // actual code uses map, but for testing, we use slice to test different order since function is a fold left operation
		expected *SoftwareVersion
	}{
		{
			"happy path",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
			},
			buildSoftwareVersion("v1.2.3", ready, available),
		},
		{
			"invalid, one missing, permutation 1",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("", notReady, notAvailable),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
		{
			"invalid, one missing, permutation 2",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("", notReady, notAvailable),
				buildSoftwareVersion("v1.2.3", ready, available),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
		{
			"invalid, one missing, permutation 3",
			[]*SoftwareVersion{
				buildSoftwareVersion("", notReady, notAvailable),
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
		{
			"mismatch, one different, permutation 1",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.4", ready, available),
			},
			buildSoftwareVersion("v1.2.3", notReady, notAvailable),
		},
		{
			"mismatch, one different, permutation 2",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.4", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
			},
			buildSoftwareVersion("v1.2.3", notReady, notAvailable),
		},
		{
			"mismatch, one different, permutation 3",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.4", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.3", ready, available),
			},
			buildSoftwareVersion("v1.2.4", notReady, notAvailable),
		},
		{
			"invalid, one different and one missing, permutation 1",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.4", ready, available),
				buildSoftwareVersion("", notReady, notAvailable),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
		{
			"invalid, one different and one missing, permutation 2",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("", notReady, notAvailable),
				buildSoftwareVersion("v1.2.4", ready, available),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
		{
			"invalid, one different and one missing, permutation 3",
			[]*SoftwareVersion{
				buildSoftwareVersion("", notReady, notAvailable),
				buildSoftwareVersion("v1.2.3", ready, available),
				buildSoftwareVersion("v1.2.4", ready, available),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
		{
			"invalid, one different and one missing, permutation 4",
			[]*SoftwareVersion{
				buildSoftwareVersion("v1.2.4", ready, available),
				buildSoftwareVersion("", notReady, notAvailable),
				buildSoftwareVersion("v1.2.3", ready, available),
			},
			buildSoftwareVersion(InvalidTag, notReady, notAvailable),
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := &SoftwareVersion{}
			for _, state := range test.states {
				fillVersionConditional(result, state)
			}
			assert.Equal(t, test.expected, result)
		})
	}
}

func TestOverallSoftwareState_IsSameState(t *testing.T) {
	tests := []struct {
		name     string
		a        *SoftwareState
		b        *SoftwareState
		expected bool
	}{
		{
			"same state",
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			true,
		},
		{
			"different current",
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.4", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			false,
		},
		{
			"different target",
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.4", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			false,
		},
		{
			"different previous",
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.4", ready, available),
			},
			false,
		},
		{
			"one nil",
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			nil,
			false,
		},
		{
			"both nil",
			nil,
			nil,
			true,
		},
		{
			"one nil current",
			&SoftwareState{
				Current:  nil,
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			res := isSameState(test.a, test.b)
			assert.Equal(t, test.expected, res)
		})
	}
}

func TestOverallSoftwareState_IsSameHostState(t *testing.T) {
	tests := []struct {
		name     string
		a        map[uint32]*HostSoftwareState
		b        map[uint32]*HostSoftwareState
		expected bool
	}{
		{
			"same state",
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			true,
		},
		{
			"different current",
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.2", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.2", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.2", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			false,
		},
		{
			"different length",
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			false,
		},
		{
			"change updating",
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: updating,
					Active:   active,
				},
			},
			false,
		},
		{
			"change active",
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
			map[uint32]*HostSoftwareState{
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   notActive,
				},
			},
			false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			res := isSameHostState(test.a, test.b)
			assert.Equal(t, test.expected, res)
		})
	}
}

func TestOverallSoftwareState_ProcessVersionSummaries(t *testing.T) {
	tests := []struct {
		name            string
		responses       map[uint32]*software_manager.VersionSummaryReply
		expectedOverall *SoftwareState
		expextedHosts   map[uint32]*HostSoftwareState
	}{
		{
			"happy path",
			map[uint32]*software_manager.VersionSummaryReply{
				0: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				1: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				2: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				3: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			map[uint32]*HostSoftwareState{
				0: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
		},
		{
			"empty responses",
			map[uint32]*software_manager.VersionSummaryReply{},
			&SoftwareState{
				Current:  buildSoftwareVersion("", notReady, notAvailable),
				Target:   buildSoftwareVersion("", notReady, notAvailable),
				Previous: buildSoftwareVersion("", notReady, notAvailable),
			},
			map[uint32]*HostSoftwareState{},
		},
		{
			"missing response",
			map[uint32]*software_manager.VersionSummaryReply{
				0: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				1: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				2: nil,
				3: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("Invalid", notReady, notAvailable),
				Target:   buildSoftwareVersion("Invalid", notReady, notAvailable),
				Previous: buildSoftwareVersion("Invalid", notReady, notAvailable),
			},
			map[uint32]*HostSoftwareState{
				0: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("", notReady, notAvailable),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("", notReady, notAvailable),
					},
					Updating: notUpdating,
					Active:   notActive,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
		},
		{
			"missing all responses",
			map[uint32]*software_manager.VersionSummaryReply{
				0: nil,
				1: nil,
				2: nil,
				3: nil,
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("Invalid", notReady, notAvailable),
				Target:   buildSoftwareVersion("Invalid", notReady, notAvailable),
				Previous: buildSoftwareVersion("Invalid", notReady, notAvailable),
			},
			map[uint32]*HostSoftwareState{
				0: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("", notReady, notAvailable),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("", notReady, notAvailable),
					},
					Updating: notUpdating,
					Active:   notActive,
				},
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("", notReady, notAvailable),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("", notReady, notAvailable),
					},
					Updating: notUpdating,
					Active:   notActive,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("", notReady, notAvailable),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("", notReady, notAvailable),
					},
					Updating: notUpdating,
					Active:   notActive,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("", notReady, notAvailable),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("", notReady, notAvailable),
					},
					Updating: notUpdating,
					Active:   notActive,
				},
			},
		},
		{
			"one host updating",
			map[uint32]*software_manager.VersionSummaryReply{
				0: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				1: {
					Current:  buildSoftwareVersionMeta("v1.2.2", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: updating,
				},
				2: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				3: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", notReady, notAvailable),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			map[uint32]*HostSoftwareState{
				0: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.2", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: updating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
		},
		{
			"current version mismatch",
			map[uint32]*software_manager.VersionSummaryReply{
				0: {
					Current:  buildSoftwareVersionMeta("v1.2.2", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				1: {
					Current:  buildSoftwareVersionMeta("v1.2.2", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				2: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				3: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("v1.2.3", ready, available),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.2", notReady, notAvailable),
				Target:   buildSoftwareVersion("v1.2.3", ready, available),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			map[uint32]*HostSoftwareState{
				0: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.2", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.2", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("v1.2.3", ready, available),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
		},
		{
			"missing target version",
			map[uint32]*software_manager.VersionSummaryReply{
				0: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("", notReady, notAvailable),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				1: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("", notReady, notAvailable),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				2: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("", notReady, notAvailable),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
				3: {
					Current:  buildSoftwareVersionMeta("v1.2.3", ready, available),
					Target:   buildSoftwareVersionMeta("", notReady, notAvailable),
					Previous: buildSoftwareVersionMeta("v1.2.2", ready, available),
					Updating: notUpdating,
				},
			},
			&SoftwareState{
				Current:  buildSoftwareVersion("v1.2.3", ready, available),
				Target:   buildSoftwareVersion("Invalid", notReady, notAvailable),
				Previous: buildSoftwareVersion("v1.2.2", ready, available),
			},
			map[uint32]*HostSoftwareState{
				0: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				1: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				2: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
				3: {
					SoftwareState: SoftwareState{
						Current:  buildSoftwareVersion("v1.2.3", ready, available),
						Target:   buildSoftwareVersion("", notReady, notAvailable),
						Previous: buildSoftwareVersion("v1.2.2", ready, available),
					},
					Updating: notUpdating,
					Active:   active,
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			overall, hosts := processVersionSummaries(test.responses)
			assert.Equal(t, test.expectedOverall, overall)
			assert.Equal(t, test.expextedHosts, hosts)
		})
	}
}

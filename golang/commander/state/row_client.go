package state

import (
	"fmt"
	"slices"
	"sort"
	"sync"
	"sync/atomic"

	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/sirupsen/logrus"
)

type RowClientList []*RowClient

func (rcl RowClientList) ConnectionState() (int, []bool) {
	state := make([]bool, 0)
	connected := 0
	for _, row := range rcl {
		rowState := row.connected.Load()
		if rowState {
			connected++
		}
		state = append(state, rowState)
	}
	return connected, state
}

func (rcl RowClientList) Sync() {
	for _, row := range rcl {
		clientSynced, err := row.client.GetDownloadedModels()
		if err != nil {
			logrus.Errorf("Remote models error: %s -  %v", row.addr, err)
			row.connected.Store(false)
			continue
		}
		row.connected.Store(true)
		syncedModels := make([]string, 0)
		syncedArtifacts := make([]string, 0)
		for _, model := range clientSynced.GetModels() {
			syncedModels = append(syncedModels, model.Id)
			syncedArtifacts = append(syncedArtifacts, model.ArtifactIds...)
		}

		row.UpdateSyncedModels(syncedModels, syncedArtifacts)
	}
}

func (rcl RowClientList) GetSyncedModels() []string {
	counts := make(map[string]int)
	for _, row := range rcl {
		for _, id := range row.GetSyncedModels() {
			counts[id] += 1
		}
	}
	synced := make([]string, 0)
	for modelID, count := range counts {
		if count == len(rcl) {
			synced = append(synced, modelID)
		}
	}
	sort.Strings(synced)
	return synced
}

func (rcl RowClientList) GetSyncedArtifacts() []string {
	counts := make(map[string]int)
	for _, row := range rcl {
		for _, id := range row.GetSyncedArtifacts() {
			counts[id] += 1
		}
	}
	synced := make([]string, 0)
	for artifactId, count := range counts {
		if count == len(rcl) {
			synced = append(synced, artifactId)
		}
	}
	sort.Strings(synced)
	return synced
}

func (rcl RowClientList) IsSynced(modelID string) (bool, []bool) {
	syncCount := 0
	rowsSynced := make([]bool, len(rcl))
	for rowIdx, row := range rcl {
		for _, id := range row.GetSyncedModels() {
			if id == modelID {
				rowsSynced[rowIdx] = true
				syncCount++
				break
			}
		}
	}
	return syncCount == len(rcl), rowsSynced
}

// GetAllModels returns the total superset of all models across all rows
func (rcl RowClientList) GetAllModels() []string {
	models := make(map[string]bool)
	for _, row := range rcl {
		for _, id := range row.GetSyncedModels() {
			models[id] = true
		}
	}
	allModels := make([]string, 0)
	for id := range models {
		allModels = append(allModels, id)
	}
	sort.Strings(allModels)
	return allModels
}

func (rcl RowClientList) ReportStatus() []string {
	report := make([]string, 0)
	for idx, row := range rcl {
		synced := make([]string, 0)
		for _, id := range row.GetSyncedModels() {
			synced = append(synced, id)
		}
		sort.Strings(synced)
		rowReportLine := fmt.Sprintf("row idx: %d host: %s connected: %t syncedModels: %v", idx, row.addr, row.connected.Load(), synced)
		report = append(report, rowReportLine)
	}
	return report
}

func (rcl RowClientList) GetCVComputeCapabilities() ([]string, error) {
	capablities := make([]string, 0)

	for _, row := range rcl {
		cvComputeCapabilities, err := row.cvRuntimeClient.GetCVComputeCapabilities()
		if err != nil {
			return capablities, err
		}

		for _, capability := range cvComputeCapabilities.GetCapabilities() {
			if !slices.Contains(capablities, capability) {
				capablities = append(capablities, capability)
			}
		}
	}

	return capablities, nil
}

func (rcl RowClientList) GetCVSupportedTensorRTVersions() ([]string, error) {
	versions := make([]string, 0)

	for _, row := range rcl {
		tensorRTVersions, err := row.cvRuntimeClient.GetCVSupportedTensorRTVersions()
		if err != nil {
			return versions, err
		}

		for _, version := range tensorRTVersions.GetVersions() {
			if !slices.Contains(versions, version) {
				versions = append(versions, version)
			}
		}
	}

	return versions, nil
}

func (rcl RowClientList) UpdateSyncedChipIds() {
	for _, row := range rcl {
		clientSynced, err := row.client.GetDownloadedChips()
		if err != nil {
			logrus.Errorf("Remote models error: %s -  %v", row.addr, err)
			row.connected.Store(false)
			continue
		}
		row.connected.Store(true)
		syncedChipIds := make([]string, 0)
		for _, chip := range clientSynced.GetChips() {
			syncedChipIds = append(syncedChipIds, chip.Id)
		}

		row.UpdateSyncedChipIds(syncedChipIds)
	}
}

func (rcl RowClientList) GetSyncedChipIds() []string {
	counts := make(map[string]int)
	for _, row := range rcl {
		for _, id := range row.GetSyncedChipIds() {
			counts[id] += 1
		}
	}
	synced := make([]string, 0)
	for chipId, count := range counts {
		if count == len(rcl) {
			synced = append(synced, chipId)
		}
	}
	sort.Strings(synced)
	return synced
}

type CVRuntimeClient interface {
	GetCVSupportedTensorRTVersions() (*cv.SupportedTensorRTVersionsResponse, error)
	GetCVComputeCapabilities() (*cv.ComputeCapabilitiesResponse, error)
}

type ModelReceiverClient interface {
	DownloadModelArtifact(request *model_receiver.DownloadModelArtifactRequest) (*model_receiver.Empty, error)
	DownloadModelMetadata(request *model_receiver.DownloadModelMetadataRequest) (*model_receiver.Empty, error)
	CleanupModels(modelIds []string) (*model_receiver.Empty, error)
	GetDownloadedModels() (*model_receiver.DownloadedModelResponse, error)

	DownloadChip(*model_receiver.DownloadChipRequest) (*model_receiver.Empty, error)
	DownloadChipMetadata(*model_receiver.DownloadChipMetadataRequest) (*model_receiver.Empty, error)
	GetDownloadedChips() (*model_receiver.GetDownloadedChipsResponse, error)
	RemoveChips(*model_receiver.RemoveChipsRequest) (*model_receiver.Empty, error)
}

type RowClient struct {
	addr            string
	client          ModelReceiverClient
	cvRuntimeClient CVRuntimeClient
	mu              sync.Mutex
	connected       atomic.Bool
	syncedModels    []string
	syncedArtifacts []string
	syncedChipIds   []string
}

func (rc *RowClient) UpdateSyncedModels(syncedModels, syncedArtifacts []string) {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.syncedModels = syncedModels
	rc.syncedArtifacts = syncedArtifacts
}

func (rc *RowClient) GetSyncedModels() []string {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.syncedModels
}

func (rc *RowClient) GetSyncedArtifacts() []string {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.syncedArtifacts
}

func (rc *RowClient) UpdateSyncedChipIds(syncedChipIds []string) {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.syncedChipIds = syncedChipIds
}

func (rc *RowClient) GetSyncedChipIds() []string {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	return rc.syncedChipIds
}

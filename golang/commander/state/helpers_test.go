package state

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/stretchr/testify/assert"
)

func TestRobotMetaRetriever(t *testing.T) {
	tests := []struct {
		name     string
		robot    environment.Robot
		reader   CurrentReader
		expected map[string]string
	}{
		{
			"dev ver",
			environment.Robot{
				CarbonVersionTag: "latest",
			},
			nil,
			map[string]string{
				"robot-version": "DEVELOPMENT",
			},
		},
		{
			"no state, fallback to env tag",
			environment.Robot{
				CarbonVersionTag: "v1.1.1",
			},
			nil,
			map[string]string{
				"robot-version": "v1.1.1",
			},
		},
		{
			"current software ver",
			environment.Robot{
				CarbonVersionTag: "not-latest",
			},
			&OverallSoftwareState{Summary: &SoftwareState{Current: &SoftwareVersion{Tag: "v1.2.3"}}},
			map[string]string{
				"robot-version": "v1.2.3",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := RobotMetaRetriever(test.robot, test.reader)
			assert.Equal(t, test.expected, got)
		})
	}
}

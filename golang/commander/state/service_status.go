package state

import (
	"fmt"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

type ServiceStatus struct {
	Name  string
	Ready bool
}

type ServerStatus struct {
	Ready         bool
	ServicesState []ServiceStatus
}

type ServiceStatusState struct {
	ManagedStateImpl
	OverallStatus frontend.StatusLevel
	Reason        string
	RowStatuses   map[int]ServerStatus
	CommandStatus ServerStatus
}

func NewServiceStatusState() *ServiceStatusState {
	state := &ServiceStatusState{
		ManagedStateImpl: ManagedStateImpl{name: "ServiceStatusState"},
		OverallStatus:    frontend.StatusLevel_LOADING,
	}
	state.initialize()
	return state
}

type ServiceStatusWatcher struct {
	EventTrigger
	serviceStatusState    *ServiceStatusState
	rows                  map[int]*rows.RowClients
	hardwareManagerClient *hardware_manager.HardwareManagerClient
}

func NewServiceStatusWatcher(serviceStatusState *ServiceStatusState, rows map[int]*rows.RowClients, hardwareManagerClient *hardware_manager.HardwareManagerClient) *ServiceStatusWatcher {
	action := &ServiceStatusWatcher{
		serviceStatusState:    serviceStatusState,
		rows:                  rows,
		hardwareManagerClient: hardwareManagerClient,
	}
	action.triggerChannel = make(chan bool)
	for _, row := range rows {
		row.WeedTrackingClient = row.WeedTrackingClient.Copy()
		newMap := make(map[uint32]*cv_runtime_client.CVRuntimeClient)
		for id, row := range row.CVRuntimeClients {
			newMap[id] = row.Copy()
		}
	}
	return action
}

func (w *ServiceStatusWatcher) Action() {
	var wg sync.WaitGroup
	commandStatus := ServerStatus{Ready: true}
	rowStatus := make(map[int]*ServerStatus)
	for i := range w.rows {
		rowStatus[i] = &ServerStatus{Ready: true}
	}
	for i := range w.rows {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			for pcIdx, cvClient := range w.rows[index].CVRuntimeClients {
				cvRuntimeReady, err := cvClient.GetReady()
				if err != nil {
					time.Sleep(100 * time.Millisecond)
					cvRuntimeReady, err = cvClient.GetReady()
					if err != nil {
						logrus.WithError(err).Info("Second attempt to get cv ready state failed")
					}
				}
				rowStatus[index].ServicesState = append(rowStatus[index].ServicesState, ServiceStatus{Name: fmt.Sprintf("cv_runtime (PC: %v)", pcIdx), Ready: cvRuntimeReady})
			}

			aimbotBooted, err := w.rows[index].WeedTrackingClient.GetBooted()
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				aimbotBooted, err = w.rows[index].WeedTrackingClient.GetBooted()
				if err != nil {
					logrus.WithError(err).Info("Second attempt to get aimbot booted state failed")
				}
			}
			rowStatus[index].ServicesState = append(rowStatus[index].ServicesState, ServiceStatus{Name: fmt.Sprintf("aimbot (PC: %v)", w.rows[index].PrimaryPCId), Ready: aimbotBooted})
		}(i)
	}

	wg.Add(1)
	go func() {
		defer wg.Done()
		var hardwareManagerReady bool
		var err error
		for retry := 1; retry <= 10; retry++ {
			hardwareManagerReady, err = w.hardwareManagerClient.GetReady()
			if err == nil {
				break
			}
			time.Sleep(5 * time.Second)
		}

		commandStatus.ServicesState = append(commandStatus.ServicesState, ServiceStatus{Name: "hardware_manager", Ready: hardwareManagerReady})
	}()

	wg.Wait()

	anyLoading := false
	for _, row := range rowStatus {
		anyLoadingRow := false
		for _, service := range row.ServicesState {
			anyLoadingRow = anyLoadingRow || !service.Ready
		}
		row.Ready = !anyLoadingRow

		anyLoading = anyLoading || anyLoadingRow
	}

	anyLoadingCommand := false
	for _, service := range commandStatus.ServicesState {
		anyLoadingCommand = anyLoadingCommand || !service.Ready
	}
	commandStatus.Ready = !anyLoadingCommand

	anyLoading = anyLoading || anyLoadingCommand

	status := frontend.StatusLevel_LOADING
	if !anyLoading {
		status = frontend.StatusLevel_READY
	}

	changed := false
	w.serviceStatusState.ReadOnCurrent(func() {
		if w.serviceStatusState.OverallStatus != status {
			changed = true
			return
		}
		if w.serviceStatusState.CommandStatus.Ready != commandStatus.Ready || len(w.serviceStatusState.CommandStatus.ServicesState) != len(commandStatus.ServicesState) {
			changed = true
			return
		}
		for i, service := range commandStatus.ServicesState {
			if service.Name != w.serviceStatusState.CommandStatus.ServicesState[i].Name || service.Ready != w.serviceStatusState.CommandStatus.Ready {
				changed = true
				return
			}
		}

		if len(w.serviceStatusState.RowStatuses) != len(rowStatus) {
			changed = true
			return
		}
		for row := range rowStatus {
			if w.serviceStatusState.RowStatuses[row].Ready != rowStatus[row].Ready {
				changed = true
				return
			}
			for i, service := range rowStatus[row].ServicesState {
				if service.Name != w.serviceStatusState.RowStatuses[row].ServicesState[i].Name || service.Ready != w.serviceStatusState.RowStatuses[row].ServicesState[i].Ready {
					changed = true
					return
				}
			}
		}
	})
	if changed {
		w.serviceStatusState.WriteOnCurrent(func() {
			w.serviceStatusState.OverallStatus = status
			w.serviceStatusState.CommandStatus = commandStatus
			w.serviceStatusState.RowStatuses = make(map[int]ServerStatus)
			for row := range rowStatus {
				w.serviceStatusState.RowStatuses[row] = *rowStatus[row]
			}
		})
	}
}

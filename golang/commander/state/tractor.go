package state

import (
	"context"
	"errors"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"

	"github.com/sirupsen/logrus"
)

type TractorIFState struct {
	ManagedStateImpl
	Expected  bool
	Connected bool
}

func NewTractorIFState() *TractorIFState {
	t := &TractorIFState{}
	t.initialize()
	return t
}
func (t *TractorIFState) shouldBeConnected() bool {
	expected := false
	t.ReadOnCurrent(func() { expected = t.Expected })
	return expected
}

type TractorIFStateWatcher struct {
	EventTrigger
	TractorState *TractorIFState
	hardware     *hardware_manager.HardwareManagerClient
}

func NewTractorIFStateWatcher(tractor *TractorIFState, hardware *hardware_manager.HardwareManagerClient) *TractorIFStateWatcher {
	return &TractorIFStateWatcher{
		TractorState: tractor,
		hardware:     hardware,
	}
}
func (action *TractorIFStateWatcher) Action() {
	new_state, err := action.hardware.GetTractorIFState()
	if err != nil {
		logrus.Error(err)
		return
	}
	action.TractorState.ConditionalWriteOnCurrent(func() bool {
		if new_state.Expected != action.TractorState.Expected || new_state.Connected != action.TractorState.Connected {
			action.TractorState.Expected = new_state.Expected
			action.TractorState.Connected = new_state.Connected
			return true
		}
		return false
	})
}

type TractorSafetyState struct {
	ManagedStateImpl
	IsSafe                           bool
	TractorReportedEnforcementPolicy bool
	ActualEnforcementPolicy          bool
	featureFlag                      *config.ConfigTree
}

func NewTractorSafetyState(featureFlag *config.ConfigTree) *TractorSafetyState {
	t := &TractorSafetyState{
		featureFlag: featureFlag,
	}
	t.initialize()
	return t
}

func (t *TractorSafetyState) SetEnforcementPolicy(enforced bool) {
	t.ConditionalWriteOnCurrent(func() bool {
		if enforced != t.ActualEnforcementPolicy {
			t.ActualEnforcementPolicy = enforced
			return true
		}
		return false
	})
}

func (t *TractorSafetyState) IsSafeComputed() bool {
	if !t.featureFlag.GetBoolValue() {
		// If feature flag is off we are always safe
		return true
	}
	isSafe := false
	t.ReadOnCurrent(func() {
		if !t.ActualEnforcementPolicy {
			isSafe = true
		} else {
			isSafe = t.IsSafe
		}
	})
	return isSafe
}

func TractorSafetyStateWatcher(ctx context.Context, tractor *TractorIFState, safetyState *TractorSafetyState, hardware *hardware_manager.HardwareManagerClient, wg *sync.WaitGroup) {
	wg.Add(1)
	defer wg.Done()
	tractorSig := tractor.AddWatcher()
	lastReadTsMs := int64(0)
	for {
		if !tractor.shouldBeConnected() {
			select {
			case <-tractorSig:
			case <-ctx.Done():
				return
			}
			continue
		}
		read_state, err := hardware.GetTractorSafetyState(lastReadTsMs, ctx)
		if err != nil {
			if errors.Is(err, hardware_manager.LongPollTimeoutError) {
				continue
			}
			logrus.Error(err)
			// Check if error is due to context being done
			select {
			case <-ctx.Done():
				return
			default:
			}
		} else {
			safetyState.WriteOnCurrent(func() {
				safetyState.IsSafe = read_state.GetIsSafe()
				safetyState.TractorReportedEnforcementPolicy = read_state.GetEnforced()
			})
			lastReadTsMs = read_state.TimestampMs
		}
	}
}

func EnforcementPolicyEnforcer(ctx context.Context, safetyState *TractorSafetyState, hardware *hardware_manager.HardwareManagerClient, wg *sync.WaitGroup) {
	wg.Add(1)
	defer wg.Done()
	safetySig := safetyState.AddWatcher()
	for {
		select {
		case <-safetySig:
		case <-ctx.Done():
			return
		}
		match := false
		enforced := false
		safetyState.ReadOnCurrent(func() {
			match = safetyState.ActualEnforcementPolicy == safetyState.TractorReportedEnforcementPolicy
			enforced = safetyState.ActualEnforcementPolicy
		})
		if !match {
			hardware.SetSafeStateEnforcement(enforced)
		}
	}
}

func TractorSafetyEnforcer(ctx context.Context, safetyState *TractorSafetyState, opState *OperationsState, event EventTrigger, wg *sync.WaitGroup) {
	wg.Add(1)
	defer wg.Done()
	safetySig := safetyState.AddWatcher()
	for {
		select {
		case <-safetySig:
		case <-ctx.Done():
			return
		}
		isSafe := safetyState.IsSafeComputed()
		if !isSafe {
			changed := false
			opState.ConditionalWriteOnCurrent(func() bool {
				if opState.ExpectedTargeting.ThinningState.Enabled {
					opState.ExpectedTargeting.ThinningState.Enabled = false
					changed = true
				}
				if opState.ExpectedTargeting.WeedState.Enabled {
					opState.ExpectedTargeting.WeedState.Enabled = false
					changed = true
				}
				return changed
			})
			if changed {
				event.Trigger()
			}
		}
	}
}

type ImplementStatusWatcher struct {
	EventTrigger
	operations    *OperationsState
	alarmState    *AlarmState
	hardware      *hardware_manager.HardwareManagerClient
	tractor       *TractorIFState
	tractorSafety *TractorSafetyState
	stopCtx       context.Context
}

func NewImplementStatusWatcher(stopCtx context.Context, operations *OperationsState, alarmState *AlarmState, hardware *hardware_manager.HardwareManagerClient, tractor *TractorIFState, tractorSafety *TractorSafetyState) *ImplementStatusWatcher {
	action := &ImplementStatusWatcher{
		operations:    operations,
		alarmState:    alarmState,
		hardware:      hardware,
		tractor:       tractor,
		tractorSafety: tractorSafety,
		stopCtx:       stopCtx,
	}
	return action
}

func (action *ImplementStatusWatcher) Action() {
	if !action.tractor.shouldBeConnected() {
		return
	}
	newStatus := frontend.Status_STATUS_UNKNOWN
	action.operations.ReadOnCurrent(func() {
		newStatus = action.operations.Status
	})

	implementError := action.alarmState.HasImpactingAlarm() || !action.tractorSafety.IsSafeComputed()
	implementActive := newStatus == frontend.Status_STATUS_WEEDING
	action.hardware.SetImplementStateOnTractor(implementActive, implementError)
}

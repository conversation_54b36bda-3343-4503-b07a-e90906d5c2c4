package state

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/sirupsen/logrus"
)

const xHairRedisKey = "crosshair_cal"
const epochKeyComponent = "/epoch"

const (
	CrosshairTaskPrefix string = "crosshair_cal"
)

type CrosshairTask struct {
	RowNumber   uint32
	LaserId     uint32
	CalRequired bool
}

type CrosshairCalState struct {
	CalState map[string]bool
	Enabled  map[string]bool
	LoadTime int64
}

type OverallStartupTaskState struct {
	ManagedStateImpl
	CrosshairState CrosshairCalState
}

func NewOverallStartupTaskState() *OverallStartupTaskState {
	state := &OverallStartupTaskState{ManagedStateImpl: ManagedStateImpl{name: "OverallStartupTaskState "}}
	state.CrosshairState.CalState = make(map[string]bool)
	state.CrosshairState.Enabled = make(map[string]bool)
	state.initialize()
	return state
}

type StartupTaskWatcher struct {
	EventTrigger
	stState      *OverallStartupTaskState
	redisClient  *redis.Client
	serviceState *ServiceStatusState
	scannerState *OverallScannerState
	hwManager    *hardware_manager.HardwareManagerClient
	loaded       bool
}

func NewStatupTaskWatcher(stState *OverallStartupTaskState, redisClient *redis.Client, serviceState *ServiceStatusState, scannerState *OverallScannerState, hwManager *hardware_manager.HardwareManagerClient) *StartupTaskWatcher {
	action := &StartupTaskWatcher{
		stState:      stState,
		redisClient:  redisClient,
		serviceState: serviceState,
		scannerState: scannerState,
		hwManager:    hwManager,
		loaded:       false,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *StartupTaskWatcher) Action() {
	if !w.loaded {
		canLoad := false
		w.serviceState.ReadOnCurrent(func() {
			for _, state := range w.serviceState.RowStatuses {
				if state.Ready {
					canLoad = true
					break
				}
			}
		})
		if canLoad {
			w.stState.WriteOnCurrent(func() {
				w.stState.CrosshairState.initialize(w.redisClient, w.hwManager)
			})
			w.loaded = true
		} else {
			return
		}
	}
	// Data has been loaded
	enabled_lasers := make(map[string]bool)
	w.scannerState.ReadOnCurrent(func() {
		for _, sc := range w.scannerState.CamMapping {
			key := fmt.Sprintf("%d/%d", sc.Descriptor.RowNumber, sc.Descriptor.ScannerId)
			enabled_lasers[key] = sc.Laser.Enabled
		}
	})
	calibrationInfo, _ := w.redisClient.HGetAll(xHairRedisKey)

	changed := false
	w.stState.ReadOnCurrent(func() {
		if len(w.stState.CrosshairState.Enabled) != len(enabled_lasers) {
			changed = true
		} else {
			for id, enabled := range enabled_lasers {
				if cur_enabled, found := w.stState.CrosshairState.Enabled[id]; found {
					if enabled != cur_enabled {
						changed = true
						break
					}
				}
			}
		}
		if !changed {
			// check for diffs in cal info
			for key, val := range calibrationInfo {
				if !strings.HasSuffix(key, epochKeyComponent) {
					continue
				}
				stateKey := strings.Replace(key, epochKeyComponent, "", 1)
				setTime, _ := strconv.ParseInt(val, 10, 64)
				if calibrated, found := w.stState.CrosshairState.CalState[stateKey]; found {
					if setTime > w.stState.CrosshairState.LoadTime && !calibrated {
						changed = true
						break
					}
				} else {
					changed = true
					break
				}
			}
		}
	})

	if changed {
		w.stState.WriteOnCurrent(func() {
			w.stState.CrosshairState.Enabled = make(map[string]bool)
			for id, enabled := range enabled_lasers {
				w.stState.CrosshairState.Enabled[id] = enabled
			}
			for key, val := range calibrationInfo {
				if !strings.HasSuffix(key, epochKeyComponent) {
					continue
				}
				stateKey := strings.Replace(key, epochKeyComponent, "", 1)
				setTime, _ := strconv.ParseInt(val, 10, 64)
				if setTime > w.stState.CrosshairState.LoadTime {
					w.stState.CrosshairState.CalState[stateKey] = true
				}
			}
		})
	}
}

func (st *OverallStartupTaskState) GetCrosshairCalTasks() []CrosshairTask {
	tasks := make([]CrosshairTask, 0)
	for id, enabled := range st.CrosshairState.Enabled {
		required := false
		if enabled {
			if cal, found := st.CrosshairState.CalState[id]; found {
				required = !cal
			} else {
				required = true
			}
		}
		parts := strings.Split(id, "/")
		row, _ := strconv.ParseUint(parts[0], 10, 32)
		laser, _ := strconv.ParseUint(parts[1], 10, 32)
		tasks = append(tasks, CrosshairTask{RowNumber: uint32(row), LaserId: uint32(laser), CalRequired: required})
	}
	sort.Slice(tasks, func(i, j int) bool {
		if tasks[i].RowNumber == tasks[j].RowNumber {
			return tasks[i].LaserId < tasks[j].LaserId
		}
		return tasks[i].RowNumber < tasks[j].RowNumber
	})
	return tasks
}

func (st *OverallStartupTaskState) MarkTaskComplete(task_id string) bool {
	if strings.HasPrefix(task_id, CrosshairTaskPrefix) {
		return false
	}
	// TODO handle other types of tasks as they are added
	return false
}

func getTZ(redisClient *redis.Client) string {
	tz, err := redisClient.GetTimezone()
	if err != nil {
		logrus.Warnf("StartupTask: Failed to get current timezone, falling back to %v", default_tz)
		return default_tz
	}
	return tz
}

func (ccw *CrosshairCalState) initialize(redisClient *redis.Client, hwManager *hardware_manager.HardwareManagerClient) {
	calibrationInfo, err := redisClient.HGetAll(xHairRedisKey)
	if err != nil {
		logrus.Errorf("Failed to lookup crosshair cal info Err: %v", err)
		return
	}
	now := time.Now()
	today := getToday(getTZ(redisClient))
	for key, val := range calibrationInfo {
		if !strings.HasSuffix(key, epochKeyComponent) {
			continue
		}
		stateKey := strings.Replace(key, epochKeyComponent, "", 1)
		setTimeMillis, convErr := strconv.ParseInt(val, 10, 64)
		if convErr != nil {
			continue
		}
		setTime := time.UnixMilli(setTimeMillis)
		if now.After(setTime.Add(time.Hour * 12)) { // Only check if older then 12 hours
			if v, found := calibrationInfo[strings.Replace(key, epochKeyComponent, "/day", 1)]; found {
				ccw.CalState[stateKey] = v == today
			} else {
				ccw.CalState[stateKey] = false
			}
		} else {
			ccw.CalState[stateKey] = true // This x-hair has been calibrated
		}
	}
	ccw.LoadTime = now.UnixMilli()
}

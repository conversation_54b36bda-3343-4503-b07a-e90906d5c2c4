package state

import (
	"context"
	"fmt"
	"math"
	"os/exec"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	tve "github.com/carbonrobotics/robot/golang/generated/proto/target_velocity_estimator"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

const MmsPerMile float64 = 304.8 * 5280
const MsPerHour float64 = 1000 * 60 * 60
const SPerHour float64 = 60 * 60

const profilesKey = "/tve/profiles"
const activeKey = "/tve/active"
const primaryVelKey = "/tve/primary_vel"

var (
	velocityCurrentGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "velocity_current_mph",
		Help:      "current velocity in miles per hour",
	})
	velocityTargetGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "velocity_target_mph",
		Help:      "target velocity in miles per hour",
	})
)

/*
Visualization of target velocity data

------------Secondary Top (Yellow) ---------

------------Primary Top (Green) ---------

------------Primary Bottom (Green) ---------

------------Secondary Bottom (Yellow) ---------
*/
type TargetVelocityData struct {
	PrimaryTargetVelocityTopMph      float64
	PrimaryTargetVelocityBottomMph   float64
	SecondaryTargetVelocityTopMph    float64
	SecondaryTargetVelocityBottomMph float64
	CruiseControlVelocityMph         float64
	RawPrimaryMph                    float64
	RawSecondaryMph                  float64
}
type VelocityV2State struct {
	ManagedStateImpl
	TargetVelocityData
	CurrentVelocityMph float64
}
type activeTargetVelocityProfileState struct {
	ManagedStateImpl
	active *tve.TVEProfile
}
type availableTargetVelocityProfileState struct {
	ManagedStateImpl
}
type TargetVelocityProfileState struct {
	available   *availableTargetVelocityProfileState
	active      *activeTargetVelocityProfileState
	redisClient *redis.Client
	rows        map[int]*rows.RowClients
	jobsState   *JobsState

	localToSyncUpdateCallback func(string, frontend.ProfileType, bool) error
	localToSyncDeleteCallback func(string, frontend.ProfileType, bool) error
}

type speedUpdate struct {
	actualSpeed    float64
	requestedSpeed float64
}
type ActualVelocityWatcher struct {
	EventTrigger
	velState              *VelocityV2State
	hardwareManagerClient *hardware_manager.HardwareManagerClient
}
type TargetVelocityWatcher struct {
	EventTrigger
	velState        *VelocityV2State
	profileState    *TargetVelocityProfileState
	opState         *OperationsState
	velConfigNode   *config.ConfigTree
	lastProfileTime int64
}

func NewVelocityStates(redisClient *redis.Client, rowClients map[int]*rows.RowClients, jobsState *JobsState) (*VelocityV2State, *TargetVelocityProfileState) {
	velState := &VelocityV2State{
		ManagedStateImpl: ManagedStateImpl{name: "VelocityV2State"},
	}
	velState.initialize()

	profileState := &TargetVelocityProfileState{
		available:   newAvailableTVEState(),
		active:      newActiveTVEState(),
		redisClient: redisClient,
		rows:        rowClients,
		jobsState:   jobsState,
	}
	logrus.Infof("Before loadActive")
	profileState.loadActive()
	logrus.Infof("After loadActive")
	return velState, profileState
}

func newAvailableTVEState() *availableTargetVelocityProfileState {
	state := &availableTargetVelocityProfileState{
		ManagedStateImpl: ManagedStateImpl{name: "availableTargetVelocityProfileState"},
	}
	state.initialize()
	return state
}
func newActiveTVEState() *activeTargetVelocityProfileState {
	state := &activeTargetVelocityProfileState{
		ManagedStateImpl: ManagedStateImpl{name: "activeTargetVelocityProfileState"},
	}
	state.initialize()
	return state
}

func NewActualVelocityWatcher(velState *VelocityV2State, hardwareManagerClient *hardware_manager.HardwareManagerClient) *ActualVelocityWatcher {
	action := &ActualVelocityWatcher{
		velState:              velState,
		hardwareManagerClient: hardwareManagerClient,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func NewTargetVelocityWatcher(velState *VelocityV2State, profileState *TargetVelocityProfileState, opState *OperationsState, velConfigNode *config.ConfigTree) *TargetVelocityWatcher {
	action := &TargetVelocityWatcher{
		velState:      velState,
		profileState:  profileState,
		opState:       opState,
		velConfigNode: velConfigNode,
	}
	initialVel := velConfigNode.GetNode("initial_vel_mph").GetFloatValue()
	velocityTargetGauge.Set(initialVel)
	velState.WriteOnCurrent(func() {
		velState.PrimaryTargetVelocityTopMph = initialVel
		velState.PrimaryTargetVelocityBottomMph = initialVel
		velState.SecondaryTargetVelocityTopMph = initialVel
		velState.SecondaryTargetVelocityBottomMph = initialVel
		velState.CruiseControlVelocityMph = initialVel
		velState.RawPrimaryMph = initialVel
		velState.RawSecondaryMph = initialVel
	})
	profileState.redisClient.WriteFloat(primaryVelKey, initialVel)
	action.triggerChannel = make(chan bool)
	return action
}
func shouldNotUseVel(opState *OperationsState) bool {
	impStatus := frontend.Status_STATUS_UNKNOWN
	anyRowEnabled := false
	opState.ReadOnCurrent(func() {
		impStatus = opState.Status
		for _, rws := range opState.RowWeeding {
			if rws.ActualTargeting.Enabled() {
				anyRowEnabled = true
				break
			}
		}
	})
	return !anyRowEnabled || (impStatus != frontend.Status_STATUS_WEEDING && impStatus != frontend.Status_STATUS_STANDBY)
}

func (w *ActualVelocityWatcher) Action() {
	currentVelocityMmPerMs, err := w.hardwareManagerClient.GetVelocity()
	if err != nil {
		logrus.Warningf("Couldn't get velocity, %v", err)
		velocityCurrentGauge.Set(0)
		return
	}

	currentVelocityMPH := (currentVelocityMmPerMs / MmsPerMile) * MsPerHour // mm/ms -> miles/hour
	velocityCurrentGauge.Set(currentVelocityMPH)
	w.velState.ConditionalWriteOnCurrent(func() bool {
		changed := w.velState.CurrentVelocityMph != currentVelocityMPH
		if changed {
			w.velState.CurrentVelocityMph = currentVelocityMPH
		}
		return changed
	})
}

func (w *TargetVelocityWatcher) Action() {
	responses := make(chan *aimbot.TargetVelocityReply)
	ets := w.opState.GetExpectedTargetingStateWithRows()
	if !ets.TargetingState.WeedState.Enabled && !ets.TargetingState.ThinningState.Enabled {
		// No need to update as we have no targeting state.
		return
	}
	count := 0
	for i, row := range w.profileState.rows {
		if !ets.IsEnabled[i] {
			continue
		}
		count++
		go func(row_inner *rows.RowClients) {
			resp, err := row_inner.AimbotClient.GetTargetVelocity()
			if err != nil {
				logrus.Errorf("Failed to get target velocity: %v", err)
			}
			responses <- resp
		}(row)
	}
	primaryVel := math.MaxFloat64
	secondaryVel := math.MaxFloat64
	for i := 0; i < count; i++ {
		resp := <-responses
		if resp == nil {
			continue
		}
		if float64(resp.VelocityMin) < primaryVel && resp.VelocityMin >= 0.0 {
			// if resp is < 0 ignore since uninitialized value.
			primaryVel = float64(resp.VelocityMin)
		}
		if float64(resp.VelocityMax) < secondaryVel && resp.VelocityMax >= 0.0 {
			// if resp is < 0 ignore since uninitialized value.
			secondaryVel = float64(resp.VelocityMax)
		}
	}
	profile := &tve.TVEProfile{}
	profileTime := int64(0)
	w.profileState.active.ReadOnCurrent(func() {
		*profile = *w.profileState.active.active
		profileTime = w.profileState.active.GetTimestampMs()
	})
	minVel := float64(profile.GetMinVelMph())
	maxVel := float64(profile.GetMaxVelMph())
	cfgInitialVel := w.velConfigNode.GetNode("initial_vel_mph").GetFloatValue()
	if primaryVel == math.MaxFloat64 {
		primaryVel = cfgInitialVel
	} else {
		primaryVel = (primaryVel / MmsPerMile * SPerHour) * (1.0 - float64(profile.GetCruiseOffsetPercent()))
	}
	if secondaryVel == math.MaxFloat64 {
		secondaryVel = cfgInitialVel
	} else {
		secondaryVel = (secondaryVel / MmsPerMile * SPerHour) * (1.0 - float64(profile.GetCruiseOffsetPercent()))
	}
	secondaryVel = math.Max(primaryVel, secondaryVel) //top of secondary must be above or same as top of primary
	curTarget := w.velState.GetTargetVel()
	changed := primaryVel != curTarget.RawPrimaryMph || secondaryVel != curTarget.RawSecondaryMph || profileTime != w.lastProfileTime
	if changed {
		averaging := float64(profile.GetDecreaseSmoothing())
		if primaryVel > curTarget.RawPrimaryMph {
			averaging = float64(profile.GetIncreaseSmoothing())
		}
		curTarget.RawPrimaryMph = primaryVel*averaging + (curTarget.RawPrimaryMph * (1.0 - averaging))
		curTarget.PrimaryTargetVelocityTopMph = math.Min(maxVel, math.Max(minVel, curTarget.RawPrimaryMph))

		averaging = float64(profile.GetDecreaseSmoothing())
		if secondaryVel > curTarget.RawSecondaryMph {
			averaging = float64(profile.GetIncreaseSmoothing())
		}
		curTarget.RawSecondaryMph = secondaryVel*averaging + (curTarget.RawSecondaryMph * (1.0 - averaging))
		curTarget.SecondaryTargetVelocityTopMph = math.Min(maxVel, math.Max(curTarget.RawSecondaryMph, curTarget.PrimaryTargetVelocityTopMph))

		curTarget.PrimaryTargetVelocityBottomMph = math.Max(minVel, curTarget.PrimaryTargetVelocityTopMph*(1.0-float64(profile.GetPrimaryRange())))
		curTarget.SecondaryTargetVelocityBottomMph = math.Max(minVel, curTarget.PrimaryTargetVelocityBottomMph*(1.0-float64(profile.GetSecondaryRange())))
		curTarget.CruiseControlVelocityMph = (curTarget.PrimaryTargetVelocityTopMph + curTarget.PrimaryTargetVelocityBottomMph) / 2.0

		velocityTargetGauge.Set(curTarget.CruiseControlVelocityMph)
		w.velState.WriteOnCurrent(func() {
			w.velState.PrimaryTargetVelocityTopMph = curTarget.PrimaryTargetVelocityTopMph
			w.velState.PrimaryTargetVelocityBottomMph = curTarget.PrimaryTargetVelocityBottomMph
			w.velState.SecondaryTargetVelocityTopMph = curTarget.SecondaryTargetVelocityTopMph
			w.velState.SecondaryTargetVelocityBottomMph = curTarget.SecondaryTargetVelocityBottomMph
			w.velState.CruiseControlVelocityMph = curTarget.CruiseControlVelocityMph
			w.velState.RawPrimaryMph = curTarget.RawPrimaryMph
			w.velState.RawSecondaryMph = curTarget.RawSecondaryMph
		})
		w.lastProfileTime = profileTime
		w.profileState.redisClient.WriteFloat(primaryVelKey, curTarget.PrimaryTargetVelocityTopMph)
	}
}

func (s *TargetVelocityProfileState) GetActiveId() string {
	activeId := ""
	s.active.ReadOnCurrent(func() {
		activeId = s.active.active.Id
	})
	return activeId
}

func (s *VelocityV2State) GetTargetVel() *TargetVelocityData {
	data := &TargetVelocityData{}
	s.ReadOnCurrent(func() {
		data.PrimaryTargetVelocityTopMph = s.PrimaryTargetVelocityTopMph
		data.PrimaryTargetVelocityBottomMph = s.PrimaryTargetVelocityBottomMph
		data.SecondaryTargetVelocityTopMph = s.SecondaryTargetVelocityTopMph
		data.SecondaryTargetVelocityBottomMph = s.SecondaryTargetVelocityBottomMph
		data.CruiseControlVelocityMph = s.CruiseControlVelocityMph
		data.RawPrimaryMph = s.RawPrimaryMph
		data.RawSecondaryMph = s.RawSecondaryMph
	})
	return data
}

func (s *TargetVelocityProfileState) saveProfile(profile *tve.TVEProfile, set_active bool, allow_unknown_uuid bool) (string, error) {
	if profile.GetId() == "" {
		profile.Id = uuid.New().String()
	} else if !allow_unknown_uuid {
		exists, _ := s.redisClient.HExists(profilesKey, profile.GetId())
		if !exists {
			return "", fmt.Errorf("Invalid profile, %v does not already exist, must pass empty id for new profile", profile.GetId())
		}
	}
	profile.UpdateTs = time.Now().UnixMilli()
	data, err := proto.Marshal(profile)
	if err != nil {
		logrus.Warnf("Failed to marshal protobuf data err: %v", err)
		return "", err
	}
	err = s.redisClient.HSet(profilesKey, profile.Id, string(data))
	if err != nil {
		logrus.Warnf("Failed to save cfg to redis %v", err)
		return "", err
	}
	s.available.WriteOnCurrent(func() {})
	if !set_active {
		curActive := ""
		s.active.ReadOnCurrent(func() {
			curActive = s.active.active.GetId()
		})
		if profile.GetId() == curActive {
			set_active = true
		}
	}
	if set_active {
		err = s.SetActiveProfile(profile.Id)
		if err != nil {
			return "", err
		}
	}
	return profile.GetId(), nil
}
func (s *TargetVelocityProfileState) SaveProfile(profile *tve.TVEProfile, set_active bool) (string, error) {
	id, err := s.saveProfile(profile, set_active, false)
	if err == nil {
		err = s.callUpdateCB(id)
	}
	return id, err
}
func (s *TargetVelocityProfileState) deleteProfile(id string, new_active_id string) error {
	curActive := ""
	s.active.ReadOnCurrent(func() {
		curActive = s.active.active.GetId()
	})
	if id == curActive {
		err := s.SetActiveProfile(new_active_id)
		if err != nil {
			return err
		}
	}
	s.redisClient.HDel(profilesKey, id)
	s.available.WriteOnCurrent(func() {})
	return nil
}
func (s *TargetVelocityProfileState) DeleteProfile(id string, new_active_id string) error {
	err := s.deleteProfile(id, new_active_id)
	if err == nil {
		err = s.callDeleteCB(id)
	}
	return err
}

func (s *TargetVelocityProfileState) GetNextActiveProfile(ts int64, ctx context.Context) (bool, *tve.TVEProfile, int64) {
	var profile *tve.TVEProfile
	result := s.active.ReadOnNext(ctx, ts, func() {
		profile = s.active.active
	})
	return result, profile, s.active.GetTimestampMs()
}
func (s *TargetVelocityProfileState) GetNextAvailableProfiles(ts int64, available *[]*tve.ProfileDetails, ctx context.Context) (bool, int64) {
	result := s.available.ReadOnNext(ctx, ts, func() {})
	if result {
		vals, err := s.redisClient.HGetAll(profilesKey)
		if err != nil {
			logrus.Warnf("Failed to fetch data err: %v", err)
			return false, ts
		}
		profile := &tve.TVEProfile{}
		for key, val := range vals {
			if err == nil {
				perr := proto.Unmarshal([]byte(val), profile)
				if perr != nil {
					logrus.Warnf("Failed to unmarshal %v with err: %v", key, perr)
				} else {
					*available = append(*available, &tve.ProfileDetails{Id: profile.GetId(), Name: profile.GetName()})
				}
			}
		}
	}
	return result, s.available.GetTimestampMs()
}
func (s *TargetVelocityProfileState) LoadProfile(id string) (*tve.TVEProfile, error) {
	str_val, err := s.redisClient.HGet(profilesKey, id)
	if err != nil {
		return nil, err
	}
	if str_val == "" {
		return nil, fmt.Errorf("Profile %v does not appear to exist", id)
	}
	profile := &tve.TVEProfile{}
	err = proto.Unmarshal([]byte(str_val), profile)
	if err != nil {
		return nil, err
	}
	return profile, nil
}
func (s *TargetVelocityProfileState) SetActiveProfile(id string) error {
	profile, lerr := s.LoadProfile(id)
	if lerr != nil {
		return lerr
	}
	err := s.redisClient.WriteString(activeKey, id)
	if err != nil {
		return err
	}
	s.active.WriteOnCurrent(func() {
		s.active.active = profile
	})
	s.notifyAimbot()
	s.jobsState.UpdateActiveProfile(profile.GetId(), profile.GetName(), frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR)
	return nil
}

func (s *TargetVelocityProfileState) getActiveId() string {
	s.active.RLock()
	defer s.active.RUnlock()
	if s.active.active == nil {
		return ""
	}
	return s.active.active.GetId()
}
func (s *TargetVelocityProfileState) DeleteProfileFromSync(id string) error {
	return s.deleteProfile(id, id) // pass same id twice so we don't delete active ever
}
func (s *TargetVelocityProfileState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *portal.GetProfileResponse_TargetVel:
		_, err := s.saveProfile(r.TargetVel, r.TargetVel.Id == s.getActiveId(), true)
		return true, err
	default:
		return false, nil
	}
}

func (s *TargetVelocityProfileState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_TargetVelocityEstimator:
		_, err := s.saveProfile(r.TargetVelocityEstimator, r.TargetVelocityEstimator.Id == s.getActiveId(), true)
		return true, err
	default:
		return false, nil
	}
}

func (s *TargetVelocityProfileState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	profile, err := s.LoadProfile(id)
	if err == nil {
		req.Profile = &portal.UploadProfileRequest_TargetVel{
			TargetVel: profile,
		}
	}
	return err
}
func (s *TargetVelocityProfileState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	profile, err := s.LoadProfile(id)
	if err == nil {
		req.Profile = &robot_syncer.UploadProfileRequest_TargetVelocityEstimator{
			TargetVelocityEstimator: profile,
		}
	}
	return err
}
func (s *TargetVelocityProfileState) SetActiveProfileFromSync(id string) error {
	return s.SetActiveProfile(id)
}

func (s *TargetVelocityProfileState) AddUpdateLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	s.localToSyncUpdateCallback = cb
}
func (s *TargetVelocityProfileState) AddDeleteLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	s.localToSyncDeleteCallback = cb
}
func (s *TargetVelocityProfileState) callDeleteCB(id string) error {
	cb := s.localToSyncDeleteCallback
	if cb != nil {
		return cb(id, frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR, false)
	}
	return nil
}
func (s *TargetVelocityProfileState) callUpdateCB(id string) error {
	cb := s.localToSyncUpdateCallback
	if cb != nil {
		return cb(id, frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR, false)
	}
	return nil
}

func (s *TargetVelocityProfileState) GetActiveProfileForJob() (string, string, frontend.ProfileType) {
	s.active.RLock()
	defer s.active.RUnlock()
	if s.active.active == nil {
		return "", "", frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR
	}
	return s.active.active.GetId(), s.active.active.GetName(), frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR
}

func (s *TargetVelocityProfileState) loadActive() {
	cmd := exec.Command("python", "-m", "tools.velocity.target_velocity_bootstrap")
	out, err := cmd.Output()
	logrus.Infof("After command")
	if err != nil {
		logrus.Warnf("Bootstrap failed err: %v, out: %v", err, string(out))
	}

	active_id, err := s.redisClient.Get(activeKey)
	if err != nil {
		logrus.Warnf("Failed to fetch active id with error: %v", err)
		return
	}
	err = s.SetActiveProfile(active_id)
	if err != nil {
		logrus.Warnf("Failed to set active id: %v", err)
	}
}
func (s *TargetVelocityProfileState) notifyAimbot() {
	for _, row := range s.rows {
		go func(row_inner *rows.RowClients) {
			err := row_inner.AimbotClient.ReloadTVEProfile()
			if err != nil {
				logrus.Errorf("Failed to reload aimbot %v", err)
			}
		}(row)
	}
}
func (s *VelocityV2State) GetCurrentVelocity() float64 {
	s.RLock()
	defer s.RUnlock()
	return s.CurrentVelocityMph
}

func (s *VelocityV2State) GetNextVelocity(ts int64, ctx context.Context, resp *frontend.WeedingVelocity) bool {
	return s.ReadOnNext(ctx, ts, func() {
		resp.CurrentVelocityMph = s.CurrentVelocityMph
		resp.PrimaryTargetVelocityTopMph = s.PrimaryTargetVelocityTopMph
		resp.PrimaryTargetVelocityBottomMph = s.PrimaryTargetVelocityBottomMph
		resp.SecondaryTargetVelocityTopMph = s.SecondaryTargetVelocityTopMph
		resp.SecondaryTargetVelocityBottomMph = s.SecondaryTargetVelocityBottomMph
		resp.CruiseControlVelocityMph = s.CruiseControlVelocityMph

		// For backwards compatability only remove once new app is released
		resp.TargetVelocityMph = s.PrimaryTargetVelocityTopMph
		resp.ToleranceMph = s.PrimaryTargetVelocityBottomMph
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.GetTimestampMs(),
		}
	})
}

package state

import (
	"context"
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/metrics_aggregator"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

var (
	laserEnabledStateGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: subsystem,
			Name:      "laser_enabled",
			Help:      "Laser enabled boolean.",
		}, []string{"row", "laser"})
	laserPowerStateGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: subsystem,
			Name:      "laser_power",
			Help:      "Laser has power boolean.",
		}, []string{"row", "laser"})
	laserErrorStateGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: subsystem,
			Name:      "laser_error_state",
			Help:      "Laser error state boolean.",
		}, []string{"row", "laser"})
	laserPowerErrorStateGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: subsystem,
			Name:      "laser_power_error",
			Help:      "Laser power error state boolean.",
		}, []string{"row", "laser"})
)

type CrosshairPosition struct {
	X                 uint32
	Y                 uint32
	Calibrating       bool
	CalibrationFailed bool
}

type ScannerDescriptor struct {
	RowNumber uint32
	ScannerId uint32
}

type LifeTimeDetails struct {
	Serial      string
	LifeTimeSec uint64
}

type ChangeDetails struct {
	Serial      string
	InstalledAt int64
}

func (s ScannerDescriptor) TargetID() uint32 {
	return (s.ScannerId-1)%10 + 1
}

type LaserState struct {
	Enabled     bool
	Firing      bool
	Error       bool
	Power       bool
	PowerError  bool
	DeltaTemp   float32
	Current     float32
	Arced       bool
	PowerLevel  float32
	Serial      string
	LifeTimeSec uint64
	InstalledAt int64
}

type ScannerState struct {
	Descriptor         ScannerDescriptor
	Crosshair          CrosshairPosition
	Laser              LaserState
	Error              bool
	ErrorCode          string
	ErrorMessage       string
	CameraId           string
	TargetTrajectoryId uint32
	PanFailure         bool
	TiltFailure        bool
}

func (s ScannerState) changed(state ScannerState) bool {
	return s.Descriptor != state.Descriptor ||
		s.Crosshair != state.Crosshair ||
		!s.Laser.Equal(state.Laser) || s.Error != state.Error || s.ErrorCode != state.ErrorCode || s.ErrorMessage != state.ErrorMessage || s.TargetTrajectoryId != state.TargetTrajectoryId || s.PanFailure != state.PanFailure || s.TiltFailure != state.TiltFailure
}

type OverallScannerState struct {
	ManagedStateImpl
	CamMapping         map[string]ScannerState
	XHairCalInProgress bool
	XHairCalProgress   float32
	XHairCalTs         int64
	robot              environment.Robot
}

func NewOverallScannerState(robot environment.Robot) *OverallScannerState {
	state := &OverallScannerState{ManagedStateImpl: ManagedStateImpl{name: "OverallScannerState"}, robot: robot}
	state.CamMapping = make(map[string]ScannerState)
	state.initialize()
	return state
}

func (s *OverallScannerState) GetNextXHairProgress(ts int64, failedScanners *[]string, ctx context.Context) (bool, float32, int64, bool) {
	in_progress := false
	progress := float32(0.0)
	cur_ts := int64(0)
	result := s.ReadOnNextSpecifyTs(ctx, ts, func() {
		in_progress = s.XHairCalInProgress
		progress = s.XHairCalProgress
		cur_ts = s.XHairCalTs
		s.setFailedScanners(failedScanners)
	}, func() int64 { return s.XHairCalTs })
	return in_progress, progress, cur_ts, result
}

func (s *OverallScannerState) setFailedScanners(failedScanners *[]string) {
	for camId, state := range s.CamMapping {
		if state.Crosshair.CalibrationFailed {
			*failedScanners = append(*failedScanners, camId)
		}
	}
}
func (s *OverallScannerState) CamIdToRowTargetId(camId string) (rowNum uint32, targetId uint32, ok bool) {
	var notFound bool = false

	s.ReadOnCurrent(func() {
		if scanner, ok := s.CamMapping[camId]; ok {
			rowNum = scanner.Descriptor.RowNumber
			targetId = (scanner.Descriptor.ScannerId-1)%10 + 1
		} else {
			notFound = true
		}
	})
	if notFound {
		return 0, 0, false
	} else {
		return rowNum, targetId, true
	}
}

func (s *OverallScannerState) GetScannerState(camID string) (ScannerState, bool) {
	ss, ok := s.CamMapping[camID]
	return ss, ok
}

func (s *OverallScannerState) GetLaserState(redisClient *redis.Client, readStateFunc func(func()) bool) (*frontend.LaserStateList, error) {
	resp := &frontend.LaserStateList{}
	result := readStateFunc(func() {
		for _, scanner := range s.CamMapping {
			resp.Lasers = append(resp.Lasers, &frontend.LaserState{
				LaserDescriptor: &frontend.LaserDescriptor{
					RowNumber: scanner.Descriptor.RowNumber,
					LaserId:   scanner.Descriptor.ScannerId,
					CameraId:  ScannerIdToCamId(s.robot, scanner.Descriptor.RowNumber, scanner.Descriptor.ScannerId),
					Serial:    scanner.Laser.Serial,
				},
				Firing:             scanner.Laser.Firing,
				Enabled:            scanner.Laser.Enabled,
				Error:              scanner.Laser.Error || scanner.Laser.PowerError || scanner.Error,
				DeltaTemp:          scanner.Laser.DeltaTemp,
				Current:            scanner.Laser.Current,
				TargetTrajectoryId: scanner.TargetTrajectoryId,
				LifetimeSec:        scanner.Laser.LifeTimeSec,
				PowerLevel:         scanner.Laser.PowerLevel,
				InstalledAt:        scanner.Laser.InstalledAt,
			})
		}
		resp.Ts = &frontend.Timestamp{
			TimestampMs: s.GetTimestampMs(),
		}
	})

	if result {
		for _, laser := range resp.Lasers {
			laser.TotalFireCount, _ = redisClient.ReadInt64(fmt.Sprintf("weeding_metrics/row%v/laser_fire_count_total/%v",
				laser.LaserDescriptor.RowNumber, laser.LaserDescriptor.LaserId), 0)
			laser.TotalFireTimeMs, _ = redisClient.ReadInt64(fmt.Sprintf("weeding_metrics/row%v/laser_fire_time_total/%v",
				laser.LaserDescriptor.RowNumber, laser.LaserDescriptor.LaserId), 0)
		}
		return resp, nil
	}

	return nil, status.Error(codes.Aborted, "Context Cancelled before Lasers Retrieved")
}

type ScannerWatcher struct {
	EventTrigger
	scState        *OverallScannerState
	implementState *ImplementState
	rows           map[int]*rows.RowClients
	metricsClient  *metrics_aggregator.MetricsAggregatorClient
	power_cut      bool
}

type watcherResponse struct {
	reply  *aimbot.ScannerStatusReply
	rowNum uint32
}

func ScannerIdToCamId(robot environment.Robot, rowId uint32, scannerId uint32) string {
	// TODO only do this if it's slayer
	prefix := getRowName(robot, rowId)
	return fmt.Sprintf("%vtarget%02d", prefix, scannerId)
}

func NewScannerWatcher(scState *OverallScannerState, implementState *ImplementState, rowClients map[int]*rows.RowClients, metricsClient *metrics_aggregator.MetricsAggregatorClient) *ScannerWatcher {
	action := &ScannerWatcher{
		scState:        scState,
		implementState: implementState,
		rows:           rowClients,
		metricsClient:  metricsClient,
		power_cut:      implementState.LaserSafetyPowerCut(),
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *ScannerWatcher) Action() {
	responses := make(chan watcherResponse)
	for index, row := range w.rows {
		go func(idx uint32, row_inner *rows.RowClients) {
			resp, err := row_inner.AimbotClient.GetScannerStatus()
			if err != nil {
				logrus.Errorf("Failed to get scannner status %v", err)
			}
			responses <- watcherResponse{reply: resp, rowNum: idx}
		}(uint32(index), row)
	}
	power_cut := w.implementState.LaserSafetyPowerCut()

	lifetimes := w.GetLaserLifeTimes()
	changes := w.GetLaserChangeTimes()

	var scanners []ScannerState
	x_hair_cal_progress := float32(1.0)
	x_hair_cal_in_progress := false
	for range w.rows {
		resp := <-responses
		if resp.reply == nil {
			continue
		}
		if resp.reply.GetXHairProgress().GetInProgress() {
			x_hair_cal_in_progress = true
			if resp.reply.GetXHairProgress().GetProgress() < x_hair_cal_progress {
				x_hair_cal_progress = resp.reply.GetXHairProgress().GetProgress()
			}
		}
		for _, status := range resp.reply.States {
			scannerId := status.ScannerDescriptor.Id
			camId := ScannerIdToCamId(w.scState.robot, uint32(resp.rowNum), scannerId)
			descriptor := ScannerDescriptor{RowNumber: uint32(resp.rowNum), ScannerId: scannerId}
			serial := ""
			usage := uint64(0)
			if ltd, ok := lifetimes[descriptor]; ok {
				serial = ltd.Serial
				usage = ltd.LifeTimeSec
			}
			installedAt := int64(0)
			if change, ok := changes[descriptor]; ok {
				installedAt = change.InstalledAt
			}

			scanners = append(scanners, ScannerState{
				Descriptor: descriptor,
				Laser: LaserState{
					Enabled:     status.LaserState.Enabled,
					Firing:      status.LaserState.Firing,
					Error:       status.LaserState.Error,
					Power:       status.LaserState.Power,
					PowerError:  !status.LaserState.Power && status.LaserState.Enabled && !(power_cut || w.power_cut),
					DeltaTemp:   status.LaserState.DeltaTemp,
					Current:     status.LaserState.Current,
					Arced:       status.LaserState.Arced,
					PowerLevel:  status.LaserState.PowerLevel,
					Serial:      serial,
					LifeTimeSec: usage,
					InstalledAt: installedAt,
				},
				Error:              status.ScannerError,
				ErrorCode:          status.ErrorCode,
				ErrorMessage:       status.ErrorMessage,
				Crosshair:          CrosshairPosition{X: status.CrosshairState.X, Y: status.CrosshairState.Y, Calibrating: status.CrosshairState.Calibrating, CalibrationFailed: status.GetCrosshairState().GetCalibrationFailed()},
				CameraId:           camId,
				TargetTrajectoryId: status.TargetTrajectoryId,
				PanFailure:         status.PanFailure,
				TiltFailure:        status.TiltFailure,
			})
		}
	}
	w.power_cut = power_cut // give an extra cycle to allow switching from power cut to powered
	var changed bool = false
	scannerSet := map[string]bool{}

	w.scState.ReadOnCurrent(func() {
		if w.scState.XHairCalInProgress != x_hair_cal_in_progress || w.scState.XHairCalProgress != x_hair_cal_progress {
			changed = true
		}
		for _, scanner := range scanners {
			scannerSet[scanner.CameraId] = true
			if curScanner, ok := w.scState.CamMapping[scanner.CameraId]; ok {
				if scanner.changed(curScanner) {
					changed = true
				}
			} else {
				changed = true //New Id found
			}
			row := fmt.Sprint(scanner.Descriptor.RowNumber)
			laser := fmt.Sprint((scanner.Descriptor.ScannerId-1)%10 + 1)
			laserPowerStateGauge.WithLabelValues(row, laser).Set(metrics.BoolToFloat(scanner.Laser.Power))
			laserPowerErrorStateGauge.WithLabelValues(row, laser).Set(metrics.BoolToFloat(scanner.Laser.PowerError))
			laserEnabledStateGauge.WithLabelValues(row, laser).Set(metrics.BoolToFloat(scanner.Laser.Enabled))
			laserErrorStateGauge.WithLabelValues(row, laser).Set(metrics.BoolToFloat(scanner.Laser.Error))
		}

		for camId := range w.scState.CamMapping {
			if _, ok := scannerSet[camId]; !ok {
				changed = true // Id Needs to be Removed
			}
		}

	})

	if changed {
		w.scState.WriteOnCurrent(func() {
			if w.scState.XHairCalInProgress != x_hair_cal_in_progress || w.scState.XHairCalProgress != x_hair_cal_progress {
				w.scState.XHairCalTs = time.Now().UnixMilli()
			}
			w.scState.XHairCalInProgress = x_hair_cal_in_progress
			w.scState.XHairCalProgress = x_hair_cal_progress
			for _, scanner := range scanners {
				w.scState.CamMapping[scanner.CameraId] = scanner
			}
			for camId := range w.scState.CamMapping {
				if _, ok := scannerSet[camId]; !ok {
					delete(w.scState.CamMapping, camId)
				}
			}
		})
	}
}

func (w *ScannerWatcher) GetLaserLifeTimes() map[ScannerDescriptor]LifeTimeDetails {
	output := make(map[ScannerDescriptor]LifeTimeDetails)
	resp, err := w.metricsClient.GetLaserLifeTimes()
	if err != nil {
		return output
	}
	for _, ltd := range resp.GetLifetimes() {
		row := ltd.GetId().GetPosition().GetRow()
		slot := ltd.GetId().GetPosition().GetSlot()
		if row == 0 || slot == 0 {
			continue
		}
		output[ScannerDescriptor{RowNumber: row, ScannerId: slot}] = LifeTimeDetails{Serial: ltd.GetId().GetSerial(), LifeTimeSec: ltd.GetLifetimeSec()}
	}
	return output
}

func (w *ScannerWatcher) GetLaserChangeTimes() map[ScannerDescriptor]*ChangeDetails {
	output := make(map[ScannerDescriptor]*ChangeDetails)
	resp, err := w.metricsClient.GetLaserDetails()
	if err != nil {
		return output
	}
	for _, installDetail := range resp.GetInstalls() {
		row := installDetail.GetId().GetPosition().GetRow()
		slot := installDetail.GetId().GetPosition().GetSlot()
		if row == 0 || slot == 0 {
			continue
		}
		output[ScannerDescriptor{RowNumber: row, ScannerId: slot}] = &ChangeDetails{Serial: installDetail.GetId().GetSerial(), InstalledAt: installDetail.GetTimestampSec()}
	}
	return output
}

func (lhs LaserState) Equal(rhs LaserState) bool {
	return lhs.Enabled == rhs.Enabled && lhs.Firing == rhs.Firing && lhs.Error == rhs.Error && lhs.Power == rhs.Power && lhs.PowerError == rhs.PowerError && lhs.Current == rhs.Current && lhs.DeltaTemp == rhs.DeltaTemp && lhs.Serial == rhs.Serial && lhs.LifeTimeSec == rhs.LifeTimeSec && lhs.Arced == rhs.Arced && lhs.PowerLevel == rhs.PowerLevel
}

// Code generated by mockery v2.28.2. DO NOT EDIT.

package mocks

import (
	context "context"

	redis "github.com/go-redis/redis/v8"
	mock "github.com/stretchr/testify/mock"
)

// MHSRedis is an autogenerated mock type for the MHSRedis type
type MHSRedis struct {
	mock.Mock
}

// ReadInt64 provides a mock function with given fields: key, def
func (_m *MHSRedis) ReadInt64(key string, def int64) (int64, error) {
	ret := _m.Called(key, def)

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(string, int64) (int64, error)); ok {
		return rf(key, def)
	}
	if rf, ok := ret.Get(0).(func(string, int64) int64); ok {
		r0 = rf(key, def)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(string, int64) error); ok {
		r1 = rf(key, def)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// WriteInt64 provides a mock function with given fields: key, i
func (_m *MHSRedis) WriteInt64(key string, i int64) error {
	ret := _m.Called(key, i)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, int64) error); ok {
		r0 = rf(key, i)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ZRangeByScoreWithScores provides a mock function with given fields: ctx, key, opt
func (_m *MHSRedis) ZRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error) {
	ret := _m.Called(ctx, key, opt)

	var r0 []redis.Z
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) ([]redis.Z, error)); ok {
		return rf(ctx, key, opt)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) []redis.Z); ok {
		r0 = rf(ctx, key, opt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *redis.ZRangeBy) error); ok {
		r1 = rf(ctx, key, opt)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMHSRedis interface {
	mock.TestingT
	Cleanup(func())
}

// NewMHSRedis creates a new instance of MHSRedis. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMHSRedis(t mockConstructorTestingTNewMHSRedis) *MHSRedis {
	mock := &MHSRedis{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

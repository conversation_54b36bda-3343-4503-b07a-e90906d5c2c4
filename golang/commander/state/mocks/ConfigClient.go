// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// ConfigClient is an autogenerated mock type for the ConfigClient type
type ConfigClient struct {
	mock.Mock
}

// AddToList provides a mock function with given fields: key, name
func (_m *ConfigClient) AddToList(key string, name string) error {
	ret := _m.Called(key, name)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(key, name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemoveFromList provides a mock function with given fields: key, name
func (_m *ConfigClient) RemoveFromList(key string, name string) error {
	ret := _m.Called(key, name)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(key, name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetBoolValue provides a mock function with given fields: key, val
func (_m *ConfigClient) SetBoolValue(key string, val bool) error {
	ret := _m.Called(key, val)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, bool) error); ok {
		r0 = rf(key, val)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetDoubleValue provides a mock function with given fields: key, val
func (_m *ConfigClient) SetDoubleValue(key string, val float64) error {
	ret := _m.Called(key, val)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, float64) error); ok {
		r0 = rf(key, val)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetStringValue provides a mock function with given fields: key, val
func (_m *ConfigClient) SetStringValue(key string, val string) error {
	ret := _m.Called(key, val)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(key, val)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewConfigClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewConfigClient creates a new instance of ConfigClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewConfigClient(t mockConstructorTestingTNewConfigClient) *ConfigClient {
	mock := &ConfigClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

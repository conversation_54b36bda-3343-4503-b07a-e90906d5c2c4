// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	hardware_manager "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	mock "github.com/stretchr/testify/mock"
)

// HWMgrClient is an autogenerated mock type for the HWMgrClient type
type HWMgrClient struct {
	mock.Mock
}

// GetGPSData provides a mock function with given fields: _a0
func (_m *HWMgrClient) GetGPSData(_a0 bool) (*hardware_manager.GetGPSDataResponse, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for GetGPSData")
	}

	var r0 *hardware_manager.GetGPSDataResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(bool) (*hardware_manager.GetGPSDataResponse, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(bool) *hardware_manager.GetGPSDataResponse); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*hardware_manager.GetGPSDataResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(bool) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewHWMgrClient creates a new instance of HWMgrClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewHWMgrClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *HWMgrClient {
	mock := &HWMgrClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

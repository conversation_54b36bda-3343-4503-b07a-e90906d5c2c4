// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	model_receiver "github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	mock "github.com/stretchr/testify/mock"
)

// ModelReceiverClient is an autogenerated mock type for the ModelReceiverClient type
type ModelReceiverClient struct {
	mock.Mock
}

// CleanupModels provides a mock function with given fields: modelIds
func (_m *ModelReceiverClient) CleanupModels(modelIds []string) (*model_receiver.Empty, error) {
	ret := _m.Called(modelIds)

	if len(ret) == 0 {
		panic("no return value specified for CleanupModels")
	}

	var r0 *model_receiver.Empty
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) (*model_receiver.Empty, error)); ok {
		return rf(modelIds)
	}
	if rf, ok := ret.Get(0).(func([]string) *model_receiver.Empty); ok {
		r0 = rf(modelIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.Empty)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(modelIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DownloadChip provides a mock function with given fields: _a0
func (_m *ModelReceiverClient) DownloadChip(_a0 *model_receiver.DownloadChipRequest) (*model_receiver.Empty, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for DownloadChip")
	}

	var r0 *model_receiver.Empty
	var r1 error
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadChipRequest) (*model_receiver.Empty, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadChipRequest) *model_receiver.Empty); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.Empty)
		}
	}

	if rf, ok := ret.Get(1).(func(*model_receiver.DownloadChipRequest) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DownloadChipMetadata provides a mock function with given fields: _a0
func (_m *ModelReceiverClient) DownloadChipMetadata(_a0 *model_receiver.DownloadChipMetadataRequest) (*model_receiver.Empty, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for DownloadChipMetadata")
	}

	var r0 *model_receiver.Empty
	var r1 error
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadChipMetadataRequest) (*model_receiver.Empty, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadChipMetadataRequest) *model_receiver.Empty); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.Empty)
		}
	}

	if rf, ok := ret.Get(1).(func(*model_receiver.DownloadChipMetadataRequest) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DownloadModelArtifact provides a mock function with given fields: request
func (_m *ModelReceiverClient) DownloadModelArtifact(request *model_receiver.DownloadModelArtifactRequest) (*model_receiver.Empty, error) {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for DownloadModelArtifact")
	}

	var r0 *model_receiver.Empty
	var r1 error
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadModelArtifactRequest) (*model_receiver.Empty, error)); ok {
		return rf(request)
	}
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadModelArtifactRequest) *model_receiver.Empty); ok {
		r0 = rf(request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.Empty)
		}
	}

	if rf, ok := ret.Get(1).(func(*model_receiver.DownloadModelArtifactRequest) error); ok {
		r1 = rf(request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DownloadModelMetadata provides a mock function with given fields: request
func (_m *ModelReceiverClient) DownloadModelMetadata(request *model_receiver.DownloadModelMetadataRequest) (*model_receiver.Empty, error) {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for DownloadModelMetadata")
	}

	var r0 *model_receiver.Empty
	var r1 error
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadModelMetadataRequest) (*model_receiver.Empty, error)); ok {
		return rf(request)
	}
	if rf, ok := ret.Get(0).(func(*model_receiver.DownloadModelMetadataRequest) *model_receiver.Empty); ok {
		r0 = rf(request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.Empty)
		}
	}

	if rf, ok := ret.Get(1).(func(*model_receiver.DownloadModelMetadataRequest) error); ok {
		r1 = rf(request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDownloadedChips provides a mock function with given fields:
func (_m *ModelReceiverClient) GetDownloadedChips() (*model_receiver.GetDownloadedChipsResponse, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDownloadedChips")
	}

	var r0 *model_receiver.GetDownloadedChipsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func() (*model_receiver.GetDownloadedChipsResponse, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *model_receiver.GetDownloadedChipsResponse); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.GetDownloadedChipsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDownloadedModels provides a mock function with given fields:
func (_m *ModelReceiverClient) GetDownloadedModels() (*model_receiver.DownloadedModelResponse, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDownloadedModels")
	}

	var r0 *model_receiver.DownloadedModelResponse
	var r1 error
	if rf, ok := ret.Get(0).(func() (*model_receiver.DownloadedModelResponse, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *model_receiver.DownloadedModelResponse); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.DownloadedModelResponse)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RemoveChips provides a mock function with given fields: _a0
func (_m *ModelReceiverClient) RemoveChips(_a0 *model_receiver.RemoveChipsRequest) (*model_receiver.Empty, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for RemoveChips")
	}

	var r0 *model_receiver.Empty
	var r1 error
	if rf, ok := ret.Get(0).(func(*model_receiver.RemoveChipsRequest) (*model_receiver.Empty, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(*model_receiver.RemoveChipsRequest) *model_receiver.Empty); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model_receiver.Empty)
		}
	}

	if rf, ok := ret.Get(1).(func(*model_receiver.RemoveChipsRequest) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewModelReceiverClient creates a new instance of ModelReceiverClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewModelReceiverClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ModelReceiverClient {
	mock := &ModelReceiverClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

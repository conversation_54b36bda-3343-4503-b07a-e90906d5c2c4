// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	almanac "github.com/carbonrobotics/robot/golang/generated/proto/almanac"

	http "net/http"

	logrus "github.com/sirupsen/logrus"

	mock "github.com/stretchr/testify/mock"

	veselka "github.com/carbonrobotics/robot/golang/lib/veselka"
)

// VeselkaClient is an autogenerated mock type for the VeselkaClient type
type VeselkaClient struct {
	mock.Mock
}

// GetAllCrops provides a mock function with given fields: ctx
func (_m *VeselkaClient) GetAllCrops(ctx context.Context) ([]*veselka.Crop, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllCrops")
	}

	var r0 []*veselka.Crop
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*veselka.Crop, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*veselka.Crop); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*veselka.Crop)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllPointCategories provides a mock function with given fields: ctx
func (_m *VeselkaClient) GetAllPointCategories(ctx context.Context) ([]veselka.PointCategory, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllPointCategories")
	}

	var r0 []veselka.PointCategory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]veselka.PointCategory, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []veselka.PointCategory); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]veselka.PointCategory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDefaultP2PModel provides a mock function with given fields: ctx, currentModelVersion
func (_m *VeselkaClient) GetDefaultP2PModel(ctx context.Context, currentModelVersion int) (*veselka.Model, error) {
	ret := _m.Called(ctx, currentModelVersion)

	if len(ret) == 0 {
		panic("no return value specified for GetDefaultP2PModel")
	}

	var r0 *veselka.Model
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int) (*veselka.Model, error)); ok {
		return rf(ctx, currentModelVersion)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int) *veselka.Model); ok {
		r0 = rf(ctx, currentModelVersion)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*veselka.Model)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = rf(ctx, currentModelVersion)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetModelArtifact provides a mock function with given fields: ctx, computeCapability, modelID, tensorRTVersion, startingSize
func (_m *VeselkaClient) GetModelArtifact(ctx context.Context, computeCapability string, modelID string, tensorRTVersion string, startingSize int64) (*http.Response, error) {
	ret := _m.Called(ctx, computeCapability, modelID, tensorRTVersion, startingSize)

	if len(ret) == 0 {
		panic("no return value specified for GetModelArtifact")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, int64) (*http.Response, error)); ok {
		return rf(ctx, computeCapability, modelID, tensorRTVersion, startingSize)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, int64) *http.Response); ok {
		r0 = rf(ctx, computeCapability, modelID, tensorRTVersion, startingSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, int64) error); ok {
		r1 = rf(ctx, computeCapability, modelID, tensorRTVersion, startingSize)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetModelForCropID provides a mock function with given fields: ctx, params
func (_m *VeselkaClient) GetModelForCropID(ctx context.Context, params veselka.GetRecommendedModelParams) (*veselka.Model, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetModelForCropID")
	}

	var r0 *veselka.Model
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, veselka.GetRecommendedModelParams) (*veselka.Model, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, veselka.GetRecommendedModelParams) *veselka.Model); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*veselka.Model)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, veselka.GetRecommendedModelParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetModelInfo provides a mock function with given fields: ctx, id
func (_m *VeselkaClient) GetModelInfo(ctx context.Context, id string) (*veselka.Model, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetModelInfo")
	}

	var r0 *veselka.Model
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*veselka.Model, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *veselka.Model); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*veselka.Model)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetModelInfos provides a mock function with given fields: ctx, ids
func (_m *VeselkaClient) GetModelInfos(ctx context.Context, ids []string) (map[string]*veselka.Model, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetModelInfos")
	}

	var r0 map[string]*veselka.Model
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) (map[string]*veselka.Model, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) map[string]*veselka.Model); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]*veselka.Model)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetModelParameters provides a mock function with given fields: ctx, parametersRequest
func (_m *VeselkaClient) GetModelParameters(ctx context.Context, parametersRequest []veselka.ModelParameterRequest) ([]*almanac.ModelinatorConfig, error) {
	ret := _m.Called(ctx, parametersRequest)

	if len(ret) == 0 {
		panic("no return value specified for GetModelParameters")
	}

	var r0 []*almanac.ModelinatorConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []veselka.ModelParameterRequest) ([]*almanac.ModelinatorConfig, error)); ok {
		return rf(ctx, parametersRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []veselka.ModelParameterRequest) []*almanac.ModelinatorConfig); ok {
		r0 = rf(ctx, parametersRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*almanac.ModelinatorConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []veselka.ModelParameterRequest) error); ok {
		r1 = rf(ctx, parametersRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetLogLevel provides a mock function with given fields: level
func (_m *VeselkaClient) SetLogLevel(level logrus.Level) {
	_m.Called(level)
}

// NewVeselkaClient creates a new instance of VeselkaClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewVeselkaClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *VeselkaClient {
	mock := &VeselkaClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

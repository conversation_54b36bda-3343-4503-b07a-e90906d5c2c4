// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// ManagedState is an autogenerated mock type for the ManagedState type
type ManagedState struct {
	mock.Mock
}

// GetTimestampMs provides a mock function with no fields
func (_m *ManagedState) GetTimestampMs() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimestampMs")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// ReadOnCurrent provides a mock function with given fields: readOnCurrent
func (_m *ManagedState) ReadOnCurrent(readOnCurrent func()) {
	_m.Called(readOnCurrent)
}

// ReadOnNext provides a mock function with given fields: ctx, timestampMs, readOnNext
func (_m *ManagedState) ReadOnNext(ctx context.Context, timestampMs int64, readOnNext func()) bool {
	ret := _m.Called(ctx, timestampMs, readOnNext)

	if len(ret) == 0 {
		panic("no return value specified for ReadOnNext")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, int64, func()) bool); ok {
		r0 = rf(ctx, timestampMs, readOnNext)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Terminate provides a mock function with no fields
func (_m *ManagedState) Terminate() {
	_m.Called()
}

// WriteOnCurrent provides a mock function with given fields: writeOnCurrent
func (_m *ManagedState) WriteOnCurrent(writeOnCurrent func()) {
	_m.Called(writeOnCurrent)
}

// WriteOnCurrentWithTimestamp provides a mock function with given fields: writeOnCurrent, timestampMs
func (_m *ManagedState) WriteOnCurrentWithTimestamp(writeOnCurrent func(), timestampMs int64) {
	_m.Called(writeOnCurrent, timestampMs)
}

// NewManagedState creates a new instance of ManagedState. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewManagedState(t interface {
	mock.TestingT
	Cleanup(func())
}) *ManagedState {
	mock := &ManagedState{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

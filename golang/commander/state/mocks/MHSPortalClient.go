// Code generated by mockery v2.28.2. DO NOT EDIT.

package mocks

import (
	portal "github.com/carbonrobotics/robot/golang/generated/proto/portal"
	mock "github.com/stretchr/testify/mock"
)

// MHSPortalClient is an autogenerated mock type for the MHSPortalClient type
type MHSPortalClient struct {
	mock.Mock
}

// UploadModelEvents provides a mock function with given fields: robotName, events
func (_m *MHSPortalClient) UploadModelEvents(robotName string, events []*portal.ModelEvent) error {
	ret := _m.Called(robotName, events)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, []*portal.ModelEvent) error); ok {
		r0 = rf(robotName, events)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMHSPortalClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMHSPortalClient creates a new instance of MHSPortalClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMHSPortalClient(t mockConstructorTestingTNewMHSPortalClient) *MHSPortalClient {
	mock := &MHSPortalClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

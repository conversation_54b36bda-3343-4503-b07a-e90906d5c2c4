// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	cv "github.com/carbonrobotics/robot/golang/generated/proto/cv"
	mock "github.com/stretchr/testify/mock"
)

// CVRuntimeClient is an autogenerated mock type for the CVRuntimeClient type
type CVRuntimeClient struct {
	mock.Mock
}

// GetCVComputeCapabilities provides a mock function with given fields:
func (_m *CVRuntimeClient) GetCVComputeCapabilities() (*cv.ComputeCapabilitiesResponse, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetCVComputeCapabilities")
	}

	var r0 *cv.ComputeCapabilitiesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func() (*cv.ComputeCapabilitiesResponse, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *cv.ComputeCapabilitiesResponse); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cv.ComputeCapabilitiesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCVSupportedTensorRTVersions provides a mock function with given fields:
func (_m *CVRuntimeClient) GetCVSupportedTensorRTVersions() (*cv.SupportedTensorRTVersionsResponse, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetCVSupportedTensorRTVersions")
	}

	var r0 *cv.SupportedTensorRTVersionsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func() (*cv.SupportedTensorRTVersionsResponse, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *cv.SupportedTensorRTVersionsResponse); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cv.SupportedTensorRTVersionsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCVRuntimeClient creates a new instance of CVRuntimeClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCVRuntimeClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *CVRuntimeClient {
	mock := &CVRuntimeClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

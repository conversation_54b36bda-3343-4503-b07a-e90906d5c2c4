// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// Actionable is an autogenerated mock type for the Actionable type
type Actionable struct {
	mock.Mock
}

// Action provides a mock function with given fields:
func (_m *Actionable) Action() {
	_m.Called()
}

// GetTriggeredEventChannel provides a mock function with given fields:
func (_m *Actionable) GetTriggeredEventChannel() chan bool {
	ret := _m.Called()

	var r0 chan bool
	if rf, ok := ret.Get(0).(func() chan bool); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(chan bool)
		}
	}

	return r0
}

// Trigger provides a mock function with given fields:
func (_m *Actionable) Trigger() {
	_m.Called()
}

type mockConstructorTestingTNewActionable interface {
	mock.TestingT
	Cleanup(func())
}

// NewActionable creates a new instance of Actionable. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewActionable(t mockConstructorTestingTNewActionable) *Actionable {
	mock := &Actionable{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Code generated by mockery v2.40.1. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// ModelinatorConfigClient is an autogenerated mock type for the ModelinatorConfigClient type
type ModelinatorConfigClient struct {
	mock.Mock
}

// CopyFrom provides a mock function with given fields: cropID, oldModelID, newModelID
func (_m *ModelinatorConfigClient) CopyFrom(cropID string, oldModelID string, newModelID string) error {
	ret := _m.Called(cropID, oldModelID, newModelID)

	if len(ret) == 0 {
		panic("no return value specified for CopyFrom")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string) error); ok {
		r0 = rf(cropID, oldModelID, newModelID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewModelinatorConfigClient creates a new instance of ModelinatorConfigClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewModelinatorConfigClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ModelinatorConfigClient {
	mock := &ModelinatorConfigClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

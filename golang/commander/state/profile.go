package state

import (
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
)

type Profile interface {
	DeleteProfileFromSync(id string) error
	SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error)
	SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error)
	LoadProfileForSync(id string, req *portal.UploadProfileRequest) error
	LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error
	SetActiveProfileFromSync(id string) error
	AddUpdateLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error)
	AddDeleteLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error)
}

package state

import (
	"context"
	"fmt"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	crredis "github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

const modelHistoryLastSuccessfulSyncTs = "/model_history_sync/last_successful_sync_ts_ms"

type MHSRedis interface {
	ZRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error)
	ReadInt64(key string, def int64) (int64, error)
	WriteInt64(key string, i int64) error
}

type MHSPortalClient interface {
	UploadModelEvents(robotName string, events []*portal.ModelEvent) error
}

type ModelHistorySyncManager struct {
	EventTrigger

	redisClient            MHSRedis
	portalClient           MHSPortalClient
	robotName              string
	lastSuccessfulSyncTsMs int64
}

func NewModelHistorySyncManager(redisClient MHSRedis, portalClient MHSPortalClient, robotName string) *ModelHistorySyncManager {
	m := &ModelHistorySyncManager{
		redisClient:  redisClient,
		portalClient: portalClient,
		robotName:    robotName,
	}
	m.lastSuccessfulSyncTsMs, _ = redisClient.ReadInt64(modelHistoryLastSuccessfulSyncTs, 0)
	m.triggerChannel = make(chan bool)
	return m
}

func (m *ModelHistorySyncManager) eventTypeToProtobuf(eventType ModelEventType) portal.ModelEventType {
	switch eventType {
	case RobotStart:
		return portal.ModelEventType_ROBOT_START
	case Pinned:
		return portal.ModelEventType_PINNED
	case UnPinned:
		return portal.ModelEventType_UNPINNED
	case Recommended:
		return portal.ModelEventType_RECOMMENDED
	case Activated:
		return portal.ModelEventType_ACTIVATED
	case NicknameChange:
		return portal.ModelEventType_NICKNAME_CHANGE
	case NicknameDelete:
		return portal.ModelEventType_NICKNAME_DELETE
	case DefaultParameterChange:
		return portal.ModelEventType_DEFAULT_PARAMETER_CHANGE
	default:
		return portal.ModelEventType_UNKNOWN
	}
}

func (m *ModelHistorySyncManager) Action() {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	members, err := m.redisClient.ZRangeByScoreWithScores(ctx, commanderModelManagerHistoryKey, &redis.ZRangeBy{
		Min:    fmt.Sprint(m.lastSuccessfulSyncTsMs),
		Max:    crredis.Infinity,
		Count:  int64(1000),
		Offset: int64(0),
	})
	if err != nil {
		logrus.WithError(err).Error("ModelHistorySync: could not read model history events to sync")
		return
	}
	syncTs := time.Now().UnixMilli()

	if len(members) > 0 {
		logrus.Infof("ModelHistorySync: got %v events", len(members))
		portalEvents := make([]*portal.ModelEvent, 0)
		for _, member := range members {
			modelEvent, err := ModelEventFromZ(member)
			if err != nil {
				logrus.WithError(err).Error("ModelHistorySync: could not unmarshal event, skipping")
				continue
			}

			portalEvent := &portal.ModelEvent{
				Type:            m.eventTypeToProtobuf(modelEvent.Type),
				ModelId:         modelEvent.ModelID,
				ModelNickname:   modelEvent.ModelNickname,
				ModelParameters: modelEvent.ModelParameters,
				ModelType:       modelEvent.ModelType,
				CropId:          modelEvent.CropID,
				JobId:           modelEvent.JobName,
				TimestampMs:     modelEvent.Time.UnixMilli(),
			}
			portalEvents = append(portalEvents, portalEvent)
		}

		err = m.portalClient.UploadModelEvents(m.robotName, portalEvents)
		if err != nil {
			logrus.WithError(err).Errorf("ModelHistorySync: could not upload events to portal")
			return
		}
	}

	m.lastSuccessfulSyncTsMs = syncTs
	m.redisClient.WriteInt64(modelHistoryLastSuccessfulSyncTs, m.lastSuccessfulSyncTsMs)
}

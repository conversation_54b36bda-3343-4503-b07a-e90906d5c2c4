package state

import (
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

type TrackingState struct {
	ManagedStateImpl
	atCapacity          map[string]bool
	atSchedulerCapacity map[uint32]bool
	rotaryTimeout       map[string]bool
	deepweedError       map[string]bool
}

func NewTrackingState() *TrackingState {
	state := &TrackingState{
		atCapacity:          make(map[string]bool),
		atSchedulerCapacity: make(map[uint32]bool),
		rotaryTimeout:       make(map[string]bool),
		deepweedError:       make(map[string]bool),
	}
	state.initialize()
	return state
}

type TrackingWatcher struct {
	EventTrigger
	trackingState *TrackingState
	rows          map[int]*rows.RowClients
	robot         environment.Robot
}

func NewTrackingWatcher(trackingState *TrackingState, rowClients map[int]*rows.RowClients, robot environment.Robot) *TrackingWatcher {
	action := &TrackingWatcher{
		trackingState: trackingState,
		rows:          rowClients,
		robot:         robot,
	}
	action.triggerChannel = make(chan bool)
	return action
}

type trackingWatcherResponse struct {
	reply  *aimbot.TrackingState
	rowNum uint32
}

func TrackerIdToGlobalTrackerId(rowId uint32, trackerId uint32, robot environment.Robot) string {
	// TODO only do this if it's slayer
	var prefix string
	if robot.MakaGen == string(environment.CarbonGenReaper) {
		prefix = getReaperModuleNames(rowId)
	} else {
		prefix = getSlayerRowNames()[rowId-1]
	}
	return fmt.Sprintf("%vtracker%02d", prefix, trackerId)
}

func (w *TrackingWatcher) Action() {
	responses := make(chan trackingWatcherResponse)

	for index, row := range w.rows {
		go func(idx uint32, row_inner *rows.RowClients) {
			resp, err := row_inner.AimbotClient.GetTrackingState()
			if err != nil {
				logrus.Errorf("Failed to get tracking status %v", err)
				responses <- trackingWatcherResponse{reply: nil, rowNum: idx}
			} else {
				responses <- trackingWatcherResponse{reply: resp, rowNum: idx}
			}
		}(uint32(index), row)
	}

	atCapacity := make(map[string]bool)
	atSchedulerCapacity := make(map[uint32]bool)
	rotaryTimeout := make(map[string]bool)
	deepweedError := make(map[string]bool)
	for range w.rows {
		resp := <-responses
		if resp.reply == nil || resp.reply.SchedulerState == nil {
			if resp.reply != nil && (resp.reply.SchedulerState == nil) {
				logrus.Warnf("TrackingWatcher: reply was non nil but submessage was: %+v", resp.reply)
			}
			continue
		}
		for _, status := range resp.reply.States {
			trackerId := status.Id
			globalTrackerId := TrackerIdToGlobalTrackerId(uint32(resp.rowNum), trackerId, w.robot)
			atCapacity[globalTrackerId] = status.AtWeedLimit
			rotaryTimeout[globalTrackerId] = status.RotaryTimeout
			deepweedError[globalTrackerId] = status.DeepweedError

		}
		atSchedulerCapacity[uint32(resp.rowNum)] = resp.reply.SchedulerState.OverCapacity
	}
	var changed bool = false

	w.trackingState.ReadOnCurrent(func() {
		for key, cap := range atCapacity {
			if curCap, ok := w.trackingState.atCapacity[key]; ok {
				changed = changed || curCap != cap
			} else {
				changed = true //New Id found
			}
		}
		for key, cap := range atSchedulerCapacity {
			if curCap, ok := w.trackingState.atSchedulerCapacity[key]; ok {
				changed = changed || curCap != cap
			} else {
				changed = true
			}
		}
		for key, timeout := range rotaryTimeout {
			if curTimeout, ok := w.trackingState.rotaryTimeout[key]; ok {
				changed = changed || curTimeout != timeout
			} else {
				changed = true
			}
		}
		for key, timeout := range deepweedError {
			if curTimeout, ok := w.trackingState.deepweedError[key]; ok {
				changed = changed || curTimeout != timeout
			} else {
				changed = true
			}
		}
	})

	if changed {
		w.trackingState.WriteOnCurrent(func() {
			w.trackingState.atCapacity = atCapacity
			w.trackingState.atSchedulerCapacity = atSchedulerCapacity
			w.trackingState.rotaryTimeout = rotaryTimeout
			w.trackingState.deepweedError = deepweedError
		})
	}
}

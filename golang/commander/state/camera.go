package state

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

func getSlayerRowNames() [3]string {
	return [3]string{"left_", "middle_", "right_"}
}

func getReaperModuleNames(rowNumber uint32) string {
	return fmt.Sprintf("row%02d_", rowNumber)
}

func getRowName(robot environment.Robot, rowNumber uint32) string {
	if robot.MakaGen == "slayer" {
		return getSlayerRowNames()[rowNumber-1]
	} else if robot.MakaGen == "reaper" {
		return getReaperModuleNames(rowNumber)
	}
	return getSlayerRowNames()[0]
}

type CameraFocus struct {
	LensValue        uint32
	FocusProgressPct float64
	MinLensValue     uint32
	MaxLensValue     uint32
	FocusInProgress  bool
}

type CameraType int32

const (
	CameraTypePredict CameraType = iota
	CameraTypeTarget
	CameraTypeDrive
	CameraTypeKillCam
)

type CameraErrorType int32

const (
	CameraErrorTypeNONE CameraErrorType = iota
	CameraErrorTypeGRAB
	CameraErrorTypeCONNECTION
	CameraErrorTypeNO_IMPLEMENTATION
	CameraErrorTypeNO_IMAGE_IN_LAST_MINUTE
)

func protoToCameraType(errorType cv.ErrorType) CameraErrorType {
	switch errorType {
	case cv.ErrorType_CONNECTION:
		return CameraErrorTypeCONNECTION
	case cv.ErrorType_GRAB:
		return CameraErrorTypeGRAB
	case cv.ErrorType_NONE:
		return CameraErrorTypeNONE
	case cv.ErrorType_NO_IMAGE_IN_LAST_MINUTE:
		return CameraErrorTypeNO_IMAGE_IN_LAST_MINUTE
	case cv.ErrorType_NO_IMPLEMENTATION:
		return CameraErrorTypeNO_IMPLEMENTATION
	default:
		logrus.Warn("Invalid Camera Info Error Type")
		return CameraErrorTypeNONE
	}
}

type Camera struct {
	RowNumber             uint32
	PCIdx                 uint32
	CameraIdx             uint32
	LocalCameraId         string
	GlobalCameraId        string
	Type                  CameraType
	AutoFocusable         bool
	Focus                 CameraFocus
	StreamHost            string
	StreamHostName        string
	StreamPort            uint32
	Width                 uint32
	Height                uint32
	Transpose             bool
	Connected             bool
	LinkSpeed             uint64
	V4l2DeviceId          string
	FirmwareVersion       string
	LatestFirmwareVersion *string
	NeedsFirmwareUpdate   bool
	ErrorType             CameraErrorType
	CVRuntimeClient       *cv_runtime_client.CVRuntimeClient
}

func NewCamera(robot environment.Robot, rowNumber uint32, PCIdx, cameraIdx uint32, localCameraId string, cameraType CameraType, autoFocusable bool, focus CameraFocus, streamHost, streamHostName string, streamPort uint32, width uint32, height uint32, transpose bool, connected bool, linkSpeed uint64, v4l2DeviceId string, firmwareVersion string, latestFirmwareVersion *string, errorType CameraErrorType, cvRuntimeClient *cv_runtime_client.CVRuntimeClient) *Camera {
	camera := &Camera{}
	camera.RowNumber = rowNumber
	camera.PCIdx = PCIdx
	camera.CameraIdx = cameraIdx
	camera.LocalCameraId = localCameraId
	camera.Type = cameraType
	camera.AutoFocusable = autoFocusable
	camera.Focus = focus
	camera.StreamHost = streamHost
	camera.StreamHostName = streamHostName
	camera.StreamPort = streamPort
	camera.GlobalCameraId = camera.getGlobalCameraId(robot)
	camera.Width = width
	camera.Height = height
	camera.Transpose = transpose
	camera.Connected = connected
	camera.LinkSpeed = linkSpeed
	camera.V4l2DeviceId = v4l2DeviceId
	camera.FirmwareVersion = firmwareVersion
	camera.LatestFirmwareVersion = latestFirmwareVersion
	camera.NeedsFirmwareUpdate = camera.needsFirmwareUpdate()
	camera.ErrorType = errorType
	camera.CVRuntimeClient = cvRuntimeClient
	return camera
}

func (c *Camera) needsFirmwareUpdate() bool {
	if !c.Connected || c.LatestFirmwareVersion == nil {
		return false
	}
	// versions are x.x.x.x
	firmwareVersionParts := strings.Split(c.FirmwareVersion, ".")
	latestFirmwareVersionParts := strings.Split(*c.LatestFirmwareVersion, ".")
	if len(firmwareVersionParts) != 4 || len(latestFirmwareVersionParts) != 4 {
		logrus.Warnf("%v has invalid camera firmware version format, current: %v, latest: %v", c.GlobalCameraId, c.FirmwareVersion, *c.LatestFirmwareVersion)
		return false
	}
	for i := 0; i < 4; i++ {
		firmwareVersionPart, err := strconv.Atoi(firmwareVersionParts[i])
		if err != nil {
			logrus.Warnf("%v has invalid firmware version part: %v", c.GlobalCameraId, firmwareVersionParts[i])
			return false
		}
		latestFirmwareVersionPart, err := strconv.Atoi(latestFirmwareVersionParts[i])
		if err != nil {
			logrus.Warnf("%v has invalid latest firmware version part: %v", c.GlobalCameraId, latestFirmwareVersionParts[i])
			return false
		}
		if firmwareVersionPart < latestFirmwareVersionPart {
			return true
		}
	}
	return false
}

func (c *Camera) getGlobalCameraId(robot environment.Robot) string {
	if c.Type == CameraTypeTarget {
		return getRowName(robot, c.RowNumber) + "target" + fmt.Sprintf("%02d", int(c.CameraIdx+1))
	} else if c.Type == CameraTypePredict {
		return getRowName(robot, c.RowNumber) + "predict" + fmt.Sprintf("%02d", int(c.CameraIdx+1))
	}
	return c.LocalCameraId
}

type CommanderCameraState interface {
	ManagedState
	GetCamera(camID string) (*Camera, bool)
	GetDimensions(camId string) (width uint32, height uint32, ok bool)
}

type OverallCameraState struct {
	ManagedStateImpl
	Cameras                map[string]*Camera
	GlobalFocusProgressPct float64
	GridViewEnabled        bool
	FocusInProgress        bool
}

func NewOverallCameraState() *OverallCameraState {
	state := &OverallCameraState{ManagedStateImpl: ManagedStateImpl{name: "OverallCameraState"}}
	state.Cameras = make(map[string]*Camera)
	state.initialize()
	return state
}

func (c *OverallCameraState) GetCamera(camID string) (*Camera, bool) {
	cam, ok := c.Cameras[camID]
	return cam, ok
}

func (c *OverallCameraState) GetDimensions(camId string) (width uint32, height uint32, ok bool) {
	var notFound bool = false
	c.ReadOnCurrent(func() {
		if camera, ok := c.Cameras[camId]; ok {
			width = camera.Width
			height = camera.Height
		} else {
			notFound = true
		}
	})
	if notFound {
		return 0, 0, false
	} else {
		return width, height, true
	}
}

func ParseCameraName(name string, robot environment.Robot) (CameraType, uint32, error) {
	var camType CameraType
	var cameraId uint32
	if strings.Contains(name, "target") {
		camType = CameraTypeTarget
		idx, err := strconv.Atoi(strings.TrimPrefix(name, "target"))
		if err != nil {
			return camType, cameraId, err
		}
		cameraId = uint32(idx - 1)
	} else if strings.Contains(name, "predict") {
		camType = CameraTypePredict
		idx, err := strconv.Atoi(strings.TrimPrefix(name, "predict"))
		if err != nil {
			return camType, cameraId, err
		}
		cameraId = uint32(idx - 1)
	} else if name == "killcam" {
		camType = CameraTypeKillCam
		cameraId = 0
	} else {
		camType = CameraTypeDrive
		if name == "front_right" {
			cameraId = 0
		} else if name == "front_left" {
			cameraId = 1
		} else if name == "back_left" {
			cameraId = 2
		} else {
			cameraId = 3
		}
	}
	return camType, cameraId, nil
}

type CameraWatcher struct {
	EventTrigger
	camState *OverallCameraState
	rows     map[int]*rows.RowClients
	robot    environment.Robot
}

func NewCameraWatcher(camState *OverallCameraState, rows map[int]*rows.RowClients, robot environment.Robot) *CameraWatcher {
	action := &CameraWatcher{
		camState: camState,
		rows:     rows,
		robot:    robot,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *CameraWatcher) Action() {
	var cameras []*Camera
	for i, row := range w.rows {
		lensesInfo, err := row.AimbotClient.LensGetAll()
		if err != nil {
			logrus.Infof("Failed to get lens info for row %v: %v", i, err)
		}
		lensesInfoMap := make(map[uint32]*aimbot.LensGetReply)
		for _, lens := range lensesInfo.GetLensStatus() {
			lensesInfoMap[lens.ScannerId] = lens
		}
		for pcIdx, cvRuntimeClient := range row.CVRuntimeClients {
			camera_info, err := cvRuntimeClient.GetCameraInfo()
			if err != nil {
				logrus.Infof("Failed to retrieve camera info from row %v: %v", i, err)
				continue
			}
			for _, camera := range camera_info.CameraInfo {
				cameraType, cameraIdx, err := ParseCameraName(camera.CamId, w.robot)
				if err != nil {
					logrus.Infof("Failed to parse camera info for camera %v: %v", camera.CamId, err)
					continue
				}
				transpose := false
				if camera.Connected {
					dimensions, err := cvRuntimeClient.GetCameraDimensions(camera.CamId)
					if err != nil {
						logrus.Infof("Failed to get dimensions for camera %v: %v", camera.CamId, err)
						continue
					}
					transpose = dimensions.Transpose
				}

				rowId := uint32(i)
				autofocusable := cameraType == CameraTypeTarget
				v4l2Device := ""
				if camera.V4L2DeviceId != nil {
					v4l2Device = *camera.V4L2DeviceId
				}
				cameraInfo := NewCamera(
					w.robot,
					rowId,
					pcIdx,
					cameraIdx,
					camera.CamId,
					cameraType,
					autofocusable,
					CameraFocus{},
					row.StreamHosts[pcIdx],
					row.StreamHostNames[pcIdx],
					row.StreamPort,
					camera.Width,
					camera.Height,
					transpose,
					camera.Connected,
					camera.LinkSpeed,
					v4l2Device,
					camera.FirmwareVersion,
					camera.LatestFirmwareVersion,
					protoToCameraType(camera.ErrorType),
					cvRuntimeClient,
				)
				if autofocusable {
					scannerId := cameraIdx + 1
					if lensInfo, ok := lensesInfoMap[scannerId]; ok {
						cameraInfo.Focus.LensValue = lensInfo.Value
						cameraInfo.Focus.MinLensValue = lensInfo.MinValue
						cameraInfo.Focus.MaxLensValue = lensInfo.MaxValue
						cameraInfo.Focus.FocusInProgress = lensInfo.ManualAutofocusing
						cameraInfo.Focus.FocusProgressPct = float64(lensInfo.ManualAutofocusPercent)
					}
				}

				cameras = append(cameras, cameraInfo)
			}
		}
	}

	var globalFocusProgress float64 = 0 // TODO Consider ticking for simulating
	var gridViewEnabled bool = false
	var focusInProgress bool = false

	var changed bool = false

	var globalFocusProgressTotal float64 = 0
	var globalFocusProgressCount float64 = 0
	for _, cam := range cameras {
		if cam.AutoFocusable && cam.Focus.FocusInProgress {
			globalFocusProgressTotal += cam.Focus.FocusProgressPct
			globalFocusProgressCount += 1
		}
	}
	if globalFocusProgressCount > 0 {
		globalFocusProgress = globalFocusProgressTotal / globalFocusProgressCount
		focusInProgress = true
	}

	w.camState.ReadOnCurrent(func() {
		// TODO Get the following from actual source
		gridViewEnabled = w.camState.GridViewEnabled
		if gridViewEnabled != w.camState.GridViewEnabled || globalFocusProgress != w.camState.GlobalFocusProgressPct || len(cameras) != len(w.camState.Cameras) {
			changed = true
		}
		for _, cam := range cameras {
			if stateCam, ok := w.camState.Cameras[cam.GlobalCameraId]; ok {
				if cam.RowNumber != stateCam.RowNumber || cam.PCIdx != stateCam.PCIdx || cam.Type != stateCam.Type || cam.AutoFocusable != stateCam.AutoFocusable || cam.Focus != stateCam.Focus || cam.StreamHost != stateCam.StreamHost || cam.StreamPort != stateCam.StreamPort || cam.Width != stateCam.Width || cam.Height != stateCam.Height || cam.Connected != stateCam.Connected || cam.LinkSpeed != stateCam.LinkSpeed || cam.ErrorType != stateCam.ErrorType || cam.V4l2DeviceId != stateCam.V4l2DeviceId {
					changed = true
				}
			} else {
				changed = true
			}
		}
	})

	if changed {
		w.camState.WriteOnCurrent(func() {
			w.camState.GlobalFocusProgressPct = globalFocusProgress
			w.camState.GridViewEnabled = gridViewEnabled
			w.camState.FocusInProgress = focusInProgress
			w.camState.Cameras = make(map[string]*Camera)
			for _, cam := range cameras {
				w.camState.Cameras[cam.GlobalCameraId] = cam
			}
		})
	}
}

func (c *Camera) IsTargetType() bool {
	return c.Type == CameraTypeTarget
}

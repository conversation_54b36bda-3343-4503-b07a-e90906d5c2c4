package state

import (
	"github.com/carbonrobotics/robot/golang/lib/environment"
)

type CurrentReader interface {
	ReadOnCurrent(func())
}

func RobotMetaRetriever(robot environment.Robot, ms CurrentReader) map[string]string {
	version := robot.CarbonVersionTag
	if version == "latest" {
		version = "DEVELOPMENT"
	} else {
		if oss, ok := ms.(*OverallSoftwareState); ok {
			oss.ReadOnCurrent(func() { version = oss.Summary.Current.Tag })
		}
	}
	return map[string]string{
		"robot-version": version,
	}
}

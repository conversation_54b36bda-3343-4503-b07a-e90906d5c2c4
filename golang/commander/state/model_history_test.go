package state

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state/mocks"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	crredis "github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestModelEventTypeFromStr(t *testing.T) {
	tests := []struct {
		input             string
		expectedEventType ModelEventType
	}{
		{string(RobotStart), RobotStart},
		{string(Pinned), Pinned},
		{string(UnPinned), UnPinned},
		{string(Recommended), Recommended},
		{string(Activated), Activated},
		{string(NicknameChange), NicknameChange},
		{string(NicknameDelete), NicknameDelete},
		{string(DefaultParameterChange), DefaultParameterChange},
		{string(None), None},
		{"", None}, // Verify none is empty
	}
	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			got := ModelEventTypeFromStr(test.input)
			assert.Equal(t, test.expectedEventType, got)
		})
	}
}

func TestModelEvent_Filtered(t *testing.T) {
	tests := []struct {
		name           string
		filterEvent    ModelEvent
		event          ModelEvent
		expectFiltered bool
	}{
		{"not filtered by time",
			ModelEvent{Time: time.Now()},
			ModelEvent{Time: time.UnixMilli(0)},
			false,
		},
		{"filtered by model id",
			ModelEvent{ModelID: "foo-bar"},
			ModelEvent{ModelID: "bar-foo"},
			true,
		},
		{"not filtered matching model id",
			ModelEvent{ModelID: "foo-bar"},
			ModelEvent{ModelID: "foo-bar"},
			false,
		},
		{"not filtered by model type",
			ModelEvent{ModelType: "foo-bar"},
			ModelEvent{ModelType: "bar-foo"},
			true,
		},
		{"not filtered matching model type",
			ModelEvent{ModelType: "foo-bar"},
			ModelEvent{ModelType: "foo-bar"},
			false,
		},
		{"not filtered by crop id",
			ModelEvent{CropID: "foo-bar"},
			ModelEvent{CropID: "bar-foo"},
			true,
		},
		{"not filtered matching crop id",
			ModelEvent{CropID: "foo-bar"},
			ModelEvent{CropID: "foo-bar"},
			false,
		},
		{"not filtered by job name",
			ModelEvent{JobName: "foo-bar"},
			ModelEvent{JobName: "bar-foo"},
			true,
		},
		{"not filtered matching job name",
			ModelEvent{JobName: "foo-bar"},
			ModelEvent{JobName: "foo-bar"},
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := test.filterEvent.Filtered(test.event)
			assert.Equal(t, test.expectFiltered, got)
		})
	}
}

func TestModelEvent_Valid(t *testing.T) {
	testTime := time.Now()
	tests := []struct {
		name        string
		event       ModelEvent
		expectValid bool
	}{
		{
			"happy path",
			ModelEvent{
				Type:            Pinned,
				ModelID:         "foo-bar-123",
				ModelNickname:   "xyz",
				ModelParameters: "{}",
				ModelType:       string(veselka.ModelTypeDeepweed),
				CropID:          "xxx-yyy-zzz",
				JobName:         "testing",
				Time:            time.Now(),
			},
			true,
		},
		{
			"no event type",
			ModelEvent{
				ModelID:   "model",
				ModelType: "type",
				CropID:    "crop",
				JobName:   "job",
				Time:      testTime,
			},
			false,
		},
		{
			"invalid time",
			ModelEvent{
				Type:      "type",
				ModelType: "type",
				CropID:    "crop",
				JobName:   "job",
				Time:      time.Time{},
			},
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := test.event.Valid()
			if test.expectValid {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestModelHistoryTracker_EventProcessor(t *testing.T) {
	tests := []struct {
		name              string
		event             ModelEvent
		zAddErr           error
		expectSubmitError bool
	}{
		{
			"happy path",
			ModelEvent{
				Type:      RobotStart,
				ModelID:   "foo-bar",
				ModelType: "deepweed",
				CropID:    "xyz-123",
				JobName:   "foo",
				Time:      time.Now(),
			},
			nil,
			false,
		},
		{
			"invalid event",
			ModelEvent{},
			nil,
			true,
		},
		{
			"zadd fails",
			ModelEvent{
				Type:      RobotStart,
				ModelID:   "foo-bar",
				ModelType: "deepweed",
				CropID:    "xyz-123",
				JobName:   "foo",
				Time:      time.Now(),
			},
			assert.AnError,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			redisMock := new(mocks.RedisClient)
			redisMock.On("ZAdd",
				mock.Anything,
				commanderModelManagerHistoryKey,
				mock.AnythingOfType("*redis.Z"),
			).Return(int64(0), test.zAddErr)

			mht := &ModelHistoryTracker{
				redis:   redisMock,
				eventCh: make(chan ModelEvent, 100),
				logger:  logrus.New().WithFields(logrus.Fields{"test": t.Name()}),
				wg:      sync.WaitGroup{},
			}
			mht.wg.Add(1)
			mht.running.Store(true)
			go mht.startEventProcessor(context.TODO())
			err := mht.SubmitEvent(test.event)
			mht.Stop()
			if test.expectSubmitError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				redisMock.AssertExpectations(t)
			}
		})
	}
}

func TestModelHistoryTracker_pruneEvents(t *testing.T) {
	testTime := time.Now()
	tests := []struct {
		name                          string
		firstEvent                    redis.Z
		zRevRangeByScoreWithScoresErr error
		zRemRangeByScoreErr           error
		expectError                   bool
	}{
		{
			"happy path",
			redis.Z{Score: float64(testTime.Add(-5 * time.Minute).UnixMilli())},
			nil,
			nil,
			false,
		},
		{
			"last event > 90 days",
			redis.Z{Score: float64(testTime.Add(-91 * Day).UnixMilli())},
			nil,
			nil,
			true,
		},
		{
			"ZRevRangeByScoreWithScores failed",
			redis.Z{Score: float64(testTime.Add(-5 * time.Minute).UnixMilli())},
			assert.AnError,
			nil,
			true,
		},
		{
			"ZRemRangeByScore failed",
			redis.Z{Score: float64(testTime.Add(-5 * time.Minute).UnixMilli())},
			nil,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			redisMock := new(mocks.RedisClient)
			redisMock.On("ZRevRangeByScoreWithScores",
				mock.Anything,
				commanderModelManagerHistoryKey,
				mock.MatchedBy(func(in *redis.ZRangeBy) bool {
					return assert.Equal(t, in.Min, fmt.Sprint(testTime.UnixMilli())) &&
						assert.Equal(t, in.Max, crredis.NegInfinity) &&
						assert.Equal(t, in.Count, int64(1))
				}),
			).Return([]redis.Z{test.firstEvent}, test.zRevRangeByScoreWithScoresErr)
			redisMock.On("ZRemRangeByScore",
				mock.Anything,
				commanderModelManagerHistoryKey,
				crredis.NegInfinity,
				fmt.Sprint(testTime.UnixMilli()),
			).Return(int64(1), test.zRemRangeByScoreErr)

			mht := &ModelHistoryTracker{
				redis:  redisMock,
				logger: logrus.New().WithFields(logrus.Fields{"test": t.Name()}),
			}
			err := mht.pruneEvents(context.TODO(), testTime)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestModelHistoryTracker_GetModelHistory(t *testing.T) {
	tests := []struct {
		name                 string
		query                ModelHistoryQuery
		zResults             []redis.Z
		expectedRedisQueryFn string
		expectedResults      []ModelEvent
		zRedisRangeErr       error
		expectError          bool
	}{
		{
			"happy path forward",
			ModelHistoryQuery{},
			[]redis.Z{
				{Member: `{"model_id":"foo-model"}`},
			},
			"ZRangeByScoreWithScores",
			[]ModelEvent{{
				ModelID: "foo-model",
			}},
			nil,
			false,
		},
		{
			"happy path reverse",
			ModelHistoryQuery{
				Reverse: true,
			},
			[]redis.Z{
				{Member: `{"model_id":"foo-model"}`},
			},
			"ZRevRangeByScoreWithScores",
			[]ModelEvent{{
				ModelID: "foo-model",
			}},
			nil,
			false,
		},
		{
			"redis failure",
			ModelHistoryQuery{},
			[]redis.Z{},
			"ZRangeByScoreWithScores",
			[]ModelEvent{},
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			redisMock := new(mocks.RedisClient)
			redisMock.On(test.expectedRedisQueryFn,
				mock.Anything,
				commanderModelManagerHistoryKey,
				mock.MatchedBy(func(in *redis.ZRangeBy) bool {
					assert.Equal(t, int64(100), in.Count)
					if test.query.Reverse && test.query.StartTimestamp > 0 {
						return assert.Equal(t, in.Max, fmt.Sprint(test.query.StartTimestamp)) && assert.Equal(t, in.Min, crredis.NegInfinity)
					} else if test.query.StartTimestamp > 0 {
						return assert.Equal(t, in.Min, fmt.Sprint(test.query.StartTimestamp)) && assert.Equal(t, in.Max, crredis.Infinity)
					} else {
						return assert.Equal(t, crredis.NegInfinity, in.Min) && assert.Equal(t, crredis.Infinity, in.Max)
					}
				}),
			).Return(test.zResults, test.zRedisRangeErr).Once()
			redisMock.On(test.expectedRedisQueryFn,
				mock.Anything,
				commanderModelManagerHistoryKey,
				mock.Anything,
			).Return([]redis.Z{}, test.zRedisRangeErr)

			mht := &ModelHistoryTracker{
				redis:  redisMock,
				logger: logrus.New().WithFields(logrus.Fields{"test": t.Name()}),
			}

			got, err := mht.GetModelHistory(context.TODO(), test.query)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedResults, got)
			}
		})
	}
}

func TestModelHistorySync_ReadLastTs(t *testing.T) {
	redisMock := new(mocks.MHSRedis)
	portalMock := new(mocks.MHSPortalClient)
	redisMock.On("ReadInt64", modelHistoryLastSuccessfulSyncTs, int64(0)).Return(int64(100), nil)
	mhs := NewModelHistorySyncManager(redisMock, portalMock, "robot")
	assert.Equal(t, int64(100), mhs.lastSuccessfulSyncTsMs)
}

func TestModelHistorySync_ConsecutiveRuns(t *testing.T) {
	redisMock := new(mocks.MHSRedis)
	portalMock := new(mocks.MHSPortalClient)
	redisMock.On("ReadInt64", modelHistoryLastSuccessfulSyncTs, int64(0)).Return(int64(100), nil)
	redisMock.On(
		"ZRangeByScoreWithScores",
		mock.Anything,
		commanderModelManagerHistoryKey,
		&redis.ZRangeBy{
			Min:    fmt.Sprint(int64(100)),
			Max:    crredis.Infinity,
			Count:  int64(1000),
			Offset: int64(0),
		}).Return([]redis.Z{
		{
			Score:  1,
			Member: `{"model_id":"foo-model", "time":"1970-01-01T00:00:00.001Z"}`,
		},
	}, nil)
	redisMock.On("WriteInt64", modelHistoryLastSuccessfulSyncTs, mock.Anything).Return(nil)
	portalMock.On("UploadModelEvents", "robot", []*portal.ModelEvent{
		{
			ModelId:     "foo-model",
			Type:        portal.ModelEventType_UNKNOWN,
			TimestampMs: 1,
		},
	}).Return(nil)

	mhs := NewModelHistorySyncManager(redisMock, portalMock, "robot")
	assert.Equal(t, int64(100), mhs.lastSuccessfulSyncTsMs)
	mhs.Action()
	assert.NotEqual(t, int64(100), mhs.lastSuccessfulSyncTsMs)

	redisMock2 := new(mocks.MHSRedis)
	portalMock2 := new(mocks.MHSPortalClient)
	mhs.redisClient = redisMock2
	mhs.portalClient = portalMock2
	redisMock2.On(
		"ZRangeByScoreWithScores",
		mock.Anything,
		commanderModelManagerHistoryKey,
		&redis.ZRangeBy{
			Min:    fmt.Sprint(mhs.lastSuccessfulSyncTsMs),
			Max:    crredis.Infinity,
			Count:  int64(1000),
			Offset: int64(0),
		}).Return([]redis.Z{}, nil)
	redisMock2.On("WriteInt64", modelHistoryLastSuccessfulSyncTs, mock.Anything).Return(nil)
	mhs.Action()

	redisMock3 := new(mocks.MHSRedis)
	portalMock3 := new(mocks.MHSPortalClient)
	mhs.redisClient = redisMock3
	mhs.portalClient = portalMock3
	redisMock3.On(
		"ZRangeByScoreWithScores",
		mock.Anything,
		commanderModelManagerHistoryKey,
		&redis.ZRangeBy{
			Min:    fmt.Sprint(mhs.lastSuccessfulSyncTsMs),
			Max:    crredis.Infinity,
			Count:  int64(1000),
			Offset: int64(0),
		}).Return([]redis.Z{
		{
			Score:  1,
			Member: `{"model_id":"foo-model", "time":"1970-01-01T00:00:00.001Z"}`,
		},
	}, nil)
	portalMock3.On("UploadModelEvents", "robot", []*portal.ModelEvent{
		{
			ModelId:     "foo-model",
			Type:        portal.ModelEventType_UNKNOWN,
			TimestampMs: 1,
		},
	}).Return(errors.New("error"))
	prevTs := mhs.lastSuccessfulSyncTsMs
	mhs.Action()
	assert.Equal(t, prevTs, mhs.lastSuccessfulSyncTsMs)
}

func TestModelEventFromZ(t *testing.T) {
	tests := []struct {
		name          string
		z             redis.Z
		expectedEvent ModelEvent
		expectError   bool
	}{
		{
			"convert ok",
			redis.Z{
				Member: `{"model_id":"foo"}`,
			},
			ModelEvent{ModelID: "foo"},
			false,
		},
		{
			"invalid z member (not string)",
			redis.Z{
				Member: 123,
			},
			ModelEvent{},
			true,
		},
		{
			"parse failure",
			redis.Z{
				Member: "}{",
			},
			ModelEvent{},
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := ModelEventFromZ(test.z)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedEvent, got)
			}
		})
	}
}

func TestModelHistoryQuery_parseResults(t *testing.T) {
	testRobotStartEvent := ModelEvent{
		Type:    RobotStart,
		ModelID: "xxx-yyy-zzz",
	}
	testPinnedEvent := ModelEvent{
		Type:    Pinned,
		ModelID: "xxx-yyy-zzz",
	}
	testUnPinnedEvent := ModelEvent{
		Type:    UnPinned,
		ModelID: "xxx-yyy-zzz",
	}
	testRecommendedEvent := ModelEvent{
		Type:    Recommended,
		ModelID: "xxx-yyy-zzz",
	}
	testActivatedEvent := ModelEvent{
		Type:    Activated,
		ModelID: "xxx-yyy-zzz",
	}
	testModelNicknameChangeEvent := ModelEvent{
		Type:    NicknameChange,
		ModelID: "xxx-yyy-zzz",
	}
	testModelNicknameDeleteEvent := ModelEvent{
		Type:    NicknameDelete,
		ModelID: "xxx-yyy-zzz",
	}
	testDefaultParameterChangeEvent := ModelEvent{
		Type:    DefaultParameterChange,
		ModelID: "xxx-yyy-zzz",
	}
	resultEvents := []ModelEvent{
		testRobotStartEvent, testPinnedEvent, testUnPinnedEvent,
		testRecommendedEvent, testActivatedEvent, testModelNicknameChangeEvent,
		testModelNicknameDeleteEvent, testDefaultParameterChangeEvent,
	}
	testQueryResults := testModelEventToZ(t, resultEvents...)

	tests := []struct {
		name            string
		query           ModelHistoryQuery
		expectedResults []ModelEvent
	}{
		{
			"only robot start",
			ModelHistoryQuery{EventTypeMatcher: ModelEventTypes{RobotStart}},
			[]ModelEvent{testRobotStartEvent},
		},
		{
			"recommended and activated",
			ModelHistoryQuery{EventTypeMatcher: ModelEventTypes{Recommended, Activated}},
			[]ModelEvent{testRecommendedEvent, testActivatedEvent},
		},
		{
			"nick name deleted by modelid",
			ModelHistoryQuery{
				EventTypeMatcher: ModelEventTypes{NicknameDelete},
				MatchFilter:      ModelEvent{ModelID: "xxx-yyy-zzz"},
			},
			[]ModelEvent{testModelNicknameDeleteEvent},
		},
		{
			"no events found",
			ModelHistoryQuery{
				EventTypeMatcher: ModelEventTypes{DefaultParameterChange},
				MatchFilter:      ModelEvent{ModelID: "foo-bar"},
			},
			[]ModelEvent{},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mht := &ModelHistoryTracker{}
			got := mht.parseResults(testQueryResults, test.query)
			assert.Equal(t, test.expectedResults, got)
		})
	}
}

func testModelEventToZ(t *testing.T, events ...ModelEvent) []redis.Z {
	t.Helper()
	results := make([]redis.Z, 0)
	for _, event := range events {
		eventBytes, err := json.Marshal(event)
		if err != nil {
			t.Fatal(err)
		}
		results = append(results, redis.Z{
			Member: string(eventBytes),
		})
	}
	return results
}

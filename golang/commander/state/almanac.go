package state

import (
	"context"
	"errors"
	"fmt"
	"maps"
	"os/exec"
	"strings"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/robot/golang/lib"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

const (
	DefaultLanguage = "DEFAULT"
)

type translations map[string]string

func (t *translations) Equals(other *translations) bool {
	if t == nil || other == nil {
		return t == other
	}
	return maps.Equal(*t, *other)
}

type pointCategoryMap map[string]translations

func (m *pointCategoryMap) Localize(lang string) map[string]string {
	ret := make(map[string]string)
	if m == nil {
		return ret
	}
	for id, t := range *m {
		if name, ok := t[lang]; ok {
			ret[id] = name
		} else {
			ret[id] = t[DefaultLanguage]
		}
	}
	return ret
}

type cfgState struct {
	ManagedStateImpl
	ActiveId    string
	redisClient *redis.Client
	rows        map[int]*rows.RowClients

	profileType               frontend.ProfileType
	localToSyncUpdateCallback []func(string, frontend.ProfileType, bool) error
	localToSyncDeleteCallback []func(string, frontend.ProfileType, bool) error
}
type AlmanacCfgState struct {
	cfgState
	modelManager     *ModelManagerState
	jobsState        *JobsState
	EnabledCrops     pointCategoryMap
	EnabledPointCats pointCategoryMap
}
type DiscriminatorCfgState struct {
	cfgState
	ActiveCropId string
	modelManager *ModelManagerState
	jobsState    *JobsState
}
type ModelinatorCfgState struct {
	cfgState
	ActiveCropId   string
	ActiveModelId  string
	modelManager   *ModelManagerState
	robotName      string
	defaultFetcher func(string, string) (*almanac.ModelinatorConfig, error)
}

func NewAlmanacCfgStates(stopCtx context.Context, redisClient *redis.Client, modelManager *ModelManagerState, jobsState *JobsState, rowClients map[int]*rows.RowClients, robotName string) (*AlmanacCfgState, *DiscriminatorCfgState, *ModelinatorCfgState) {
	bootStrapAlmanac()
	almanacState := newAlmanacCfgState(redisClient, modelManager, jobsState, rowClients)
	discriminatorState := newDiscriminatorCfgState(redisClient, modelManager, jobsState, rowClients)
	modelinatorState := newModelinatorCfgState(redisClient, modelManager, rowClients, robotName)
	go watchModelManager(stopCtx, almanacState, discriminatorState, modelinatorState)
	return almanacState, discriminatorState, modelinatorState
}
func newAlmanacCfgState(redisClient *redis.Client, modelManager *ModelManagerState, jobsState *JobsState, rows map[int]*rows.RowClients) *AlmanacCfgState {
	state := &AlmanacCfgState{
		cfgState: cfgState{
			ManagedStateImpl: ManagedStateImpl{name: "AlmanacCfgState"}, redisClient: redisClient, rows: rows, profileType: frontend.ProfileType_ALMANAC,
		},
		modelManager:     modelManager,
		jobsState:        jobsState,
		EnabledCrops:     make(pointCategoryMap),
		EnabledPointCats: make(pointCategoryMap),
	}

	data, err := ReadActiveAlmanacId(redisClient)
	if err != nil {
		logrus.Warnf("Failed to get active almanac id: %v", err)
	} else {
		state.ActiveId = data
	}
	state.initialize()
	return state
}

func newDiscriminatorCfgState(redisClient *redis.Client, modelManager *ModelManagerState, jobsState *JobsState, rows map[int]*rows.RowClients) *DiscriminatorCfgState {
	state := &DiscriminatorCfgState{
		cfgState: cfgState{
			ManagedStateImpl: ManagedStateImpl{name: "DiscriminatorCfgState"}, redisClient: redisClient, rows: rows, profileType: frontend.ProfileType_DISCRIMINATOR,
		},
		modelManager: modelManager,
		jobsState:    jobsState,
	}
	state.initialize()
	return state
}
func newModelinatorCfgState(redisClient *redis.Client, modelManager *ModelManagerState, rows map[int]*rows.RowClients, robotName string) *ModelinatorCfgState {
	state := &ModelinatorCfgState{
		cfgState: cfgState{
			ManagedStateImpl: ManagedStateImpl{name: "ModelinatorCfgState"}, redisClient: redisClient, rows: rows, profileType: frontend.ProfileType_MODELINATOR,
		},
		modelManager:   modelManager,
		robotName:      robotName,
		defaultFetcher: nil,
	}
	state.initialize()
	modelManager.RegisterModelParameterCallback(state.newDefaultModelinatorCB)
	return state
}
func (s *cfgState) AddUpdateLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	s.Lock()
	defer s.Unlock()
	s.localToSyncUpdateCallback = append(s.localToSyncUpdateCallback, cb)
}
func (s *cfgState) AddDeleteLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	s.Lock()
	defer s.Unlock()
	s.localToSyncDeleteCallback = append(s.localToSyncDeleteCallback, cb)
}
func (s *cfgState) callDeleteCB(id string) error {
	s.RLock()
	cbs := s.localToSyncDeleteCallback
	s.RUnlock()

	allErrs := make([]error, 0)
	for _, cb := range cbs {
		if err := cb(id, s.profileType, false); err != nil {
			allErrs = append(allErrs, fmt.Errorf("cfgState delete callback failed id: %s, pfileType: %T err: %w", id, s.profileType, err))
		}
	}
	return errors.Join(allErrs...)
}
func (s *cfgState) callUpdateCB(id string) error {
	s.RLock()
	cbs := s.localToSyncUpdateCallback
	s.RUnlock()

	allErrs := make([]error, 0)
	for _, cb := range cbs {
		if err := cb(id, s.profileType, false); err != nil {
			allErrs = append(allErrs, fmt.Errorf("cfgState update callback failed id: %s, pfileType: %T err: %w", id, s.profileType, err))
		}
	}
	return errors.Join(allErrs...)
}

func (s *cfgState) GetActiveProfileForJob() (string, string, frontend.ProfileType) {
	s.RLock()
	defer s.RUnlock()
	return s.ActiveId, "", s.profileType
}

func (s *AlmanacCfgState) SetActiveCfg(id string) error {
	if id == "" {
		return errors.New("Invalid ID")
	}
	curr := s.GetActiveCfg()
	if id == curr {
		s.Notify()
		return s.ReloadAlmanac()
	}
	exists, err := s.redisClient.HExists(redis.AlmanacCfgs, id)
	if err != nil {
		return err
	}
	if !exists {
		return fmt.Errorf("No almanac with ID %v exists, cannot set it active.", id)
	}
	err = s.redisClient.WriteString(redis.AlmanacActive, id)
	s.WriteOnCurrent(func() {
		s.ActiveId = id
	})
	s.jobsState.UpdateActiveProfile(id, "", frontend.ProfileType_ALMANAC)
	return s.ReloadAlmanac()
}

func (s *AlmanacCfgState) GetEnabledPointCategories(lang string) (ret map[string]string) {
	s.ReadOnCurrent(func() {
		ret = s.EnabledPointCats.Localize(lang)
	})
	return
}

func (s *AlmanacCfgState) GetEnabledCrops(lang string) (ret map[string]string) {
	s.ReadOnCurrent(func() {
		ret = s.EnabledCrops.Localize(lang)
	})
	return
}

func (s *AlmanacCfgState) GetNextConfigData(ctx context.Context, timestampMs int64, lang string) (result bool, enabledCrops map[string]string, enabledPointCategories map[string]string, ts int64) {
	result = s.ReadOnNext(ctx, timestampMs, func() {
		enabledCrops = s.EnabledCrops.Localize(lang)
		enabledPointCategories = s.EnabledPointCats.Localize(lang)
	})

	ts = s.GetTimestampMs()
	return

}

func (s *AlmanacCfgState) saveAlmanacConfig(cfg *almanac.AlmanacConfig, setActive bool) error {
	data, err := proto.Marshal(cfg)
	if err != nil {
		logrus.Warnf("Failed to marshal protobuf data err: %v", err)
		return err
	}
	err = s.redisClient.HSet(redis.AlmanacCfgs, cfg.Id, string(data))
	if err != nil {
		logrus.Warnf("Failed to save cfg to redis %v", err)
		return err
	}
	if setActive {
		err = s.SetActiveCfg(cfg.Id)
		if err != nil {
			logrus.Warnf("Failed to set %v as active: %v", cfg.Id, err)
			return err
		}
	}
	curr := s.GetActiveCfg()
	if cfg.Id == curr {
		if !setActive {
			// still need to notify that a change to active has occurred
			s.Notify()
			return s.ReloadAlmanac()
		}
	}
	return nil
}
func (s *AlmanacCfgState) SaveAlmanacConfig(cfg *almanac.AlmanacConfig, setActive bool) error {
	err := s.saveAlmanacConfig(cfg, setActive)
	if err == nil {
		err = s.callUpdateCB(cfg.Id)
	}
	return err
}

func (s *AlmanacCfgState) deleteAlmanacConfig(id string, newActiveId string) error {
	curr := s.GetActiveCfg()
	if id == curr {
		if newActiveId != id {
			err := s.SetActiveCfg(newActiveId)
			if err != nil {
				return err
			}
		} else {
			logrus.Warn("Cannot delete active cfg.")
			return status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot delete %v as it is currently active.", id))
		}
	}
	err := s.redisClient.HDel(redis.AlmanacCfgs, id)
	if err != nil {
		logrus.Warnf("Failed to delete almanac cfg %v, err: %v", id, err)
		return err
	}
	s.Notify()
	return nil
}
func (s *AlmanacCfgState) DeleteAlmanacConfig(id string, newActiveId string) error {
	err := s.deleteAlmanacConfig(id, newActiveId)
	if err == nil {
		err = s.callDeleteCB(id)
	}
	return err
}

func (s *AlmanacCfgState) ReloadAlmanac() error {
	for _, rowClients := range s.rows {
		err := rowClients.AimbotClient.ReloadAlmanacConf()
		if err != nil {
			return err
		}
	}
	return nil
}
func (s *AlmanacCfgState) DeleteProfileFromSync(id string) error {
	return s.deleteAlmanacConfig(id, id) //Intentionally dont send new active as we shouldnt delete active from sync
}
func (s *AlmanacCfgState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *portal.GetProfileResponse_Almanac:
		return true, s.saveAlmanacConfig(r.Almanac, s.GetActiveCfg() == r.Almanac.Id)
	default:
		return false, nil
	}
}
func (s *AlmanacCfgState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_Almanac:
		return true, s.saveAlmanacConfig(r.Almanac, s.GetActiveCfg() == r.Almanac.Id)
	default:
		return false, nil
	}
}
func (s *AlmanacCfgState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	almanac, err := s.redisClient.LoadAlmanacConfig(id)
	if err == nil {
		req.Profile = &portal.UploadProfileRequest_Almanac{
			Almanac: almanac,
		}
	}
	return err
}
func (s *AlmanacCfgState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	almanac, err := s.redisClient.LoadAlmanacConfig(id)
	if err == nil {
		req.Profile = &robot_syncer.UploadProfileRequest_Almanac{
			Almanac: almanac,
		}
	}
	return err
}
func (s *AlmanacCfgState) SetActiveProfileFromSync(id string) error {
	return s.SetActiveCfg(id)
}

func (s *DiscriminatorCfgState) SetActiveCfg(id string, crop_id string) (bool, error) {
	setActive := false
	s.ReadOnCurrent(func() {
		if crop_id == "" {
			crop_id = s.ActiveCropId
		}
		setActive = crop_id == s.ActiveCropId
	})
	if id == "" {
		err := s.redisClient.HDel(redis.DiscriminatorActive, crop_id)
		if err != nil {
			return setActive, err
		}
	} else {
		err := s.redisClient.HSet(redis.DiscriminatorActive, crop_id, id)
		if err != nil {
			return setActive, err
		}
	}
	if setActive {
		s.WriteOnCurrent(func() {
			s.ActiveId = id
		})
		s.jobsState.UpdateActiveProfile(id, "", frontend.ProfileType_DISCRIMINATOR)
	}
	var err error = nil
	if setActive {
		err = s.ReloadDiscriminator()
	}
	return setActive, err
}

func (s *cfgState) GetActiveCfg() string {
	curr := ""
	s.ReadOnCurrent(func() {
		curr = s.ActiveId
	})
	return curr
}
func (s *cfgState) Notify() {
	s.WriteOnCurrent(func() {}) // Update ts to now
}
func (s *cfgState) GetNextActiveCfg(ctx context.Context, timestampMs int64) (bool, string, int64) {
	curr := ""
	result := s.ReadOnNext(ctx, timestampMs, func() {
		curr = s.ActiveId
	})
	return result, curr, s.GetTimestampMs()
}

func watchModelManager(stopCtx context.Context, almanacState *AlmanacCfgState, discriminatorState *DiscriminatorCfgState, modelinatorState *ModelinatorCfgState) {
	var ts int64 = 0
	cropId := ""
	modelId := ""
	enabledCrops := make(map[string]translations)
	enabledPointCats := make(map[string]translations)
	modelManager := almanacState.modelManager
	redisClient := almanacState.redisClient
	for {
		select {
		case <-stopCtx.Done():
			return
		default:
		}

		prevCropId := cropId
		prevModelId := modelId
		prevEnabledCrops := enabledCrops
		prevEnabledPointCats := enabledPointCats
		modelManager.ReadOnNext(stopCtx, ts, func() {
			cropId = modelManager.currentCropID
			modelId = modelManager.CurrentDeepWeedModelId
			enabledCrops = make(map[string]translations)
			for _, crop := range modelManager.EnabledCropsList {
				enabledCrops[crop.ID] = make(translations)
				enabledCrops[crop.ID][DefaultLanguage] = crop.CommonName
				for _, t := range crop.Translations {
					enabledCrops[crop.ID][t.Language] = t.Name
				}
			}
			enabledPointCats = make(map[string]translations)
			for _, pc := range modelManager.CurrentPointCategories {
				enabledPointCats[pc.ID] = make(translations)
				enabledPointCats[pc.ID][DefaultLanguage] = pc.DisplayName
				for _, t := range pc.Translations {
					enabledPointCats[pc.ID][t.Language] = t.Name
				}
			}
			ts = modelManager.GetTimestampMs()
		})

		if prevCropId != cropId {
			rerr := redisClient.WriteString(redis.ActiveCropId, cropId)
			if rerr != nil {
				logrus.WithError(rerr).Errorf("Failed to set active crop id in redis to %v", cropId)
			}
		}
		if prevModelId != modelId {
			rerr := redisClient.WriteString(redis.ActiveModelId, modelId)
			if rerr != nil {
				logrus.WithError(rerr).Errorf("Failed to set active model id in redis to %v", modelId)
			}
		}
		if prevCropId != cropId {
			discriminatorState.updateCropId(cropId)
		}
		if prevCropId != cropId || prevModelId != modelId {
			modelinatorState.setActiveIds(modelId, cropId)
		}

		if !maps.EqualFunc(prevEnabledCrops, enabledCrops, func(a, b translations) bool { return a.Equals(&b) }) {
			almanacState.WriteOnCurrent(func() {
				almanacState.EnabledCrops = enabledCrops
			})
		}

		if !maps.EqualFunc(prevEnabledPointCats, enabledPointCats, func(a, b translations) bool { return a.Equals(&b) }) {
			almanacState.WriteOnCurrent(func() {
				almanacState.EnabledPointCats = enabledPointCats
			})
		}
	}
}

func (s *DiscriminatorCfgState) updateCropId(cropId string) {
	curr := ""
	s.ReadOnCurrent(func() {
		curr = s.ActiveCropId
	})
	if curr != cropId {
		active, err := s.getDiscriminatorForCropId(cropId)
		if err != nil {
			logrus.WithError(err).Error("Failed to get active discriminator")
			return
		}
		logrus.Infof("Updating active id for discriminator to %v as crop id changed from %v to %v", active, curr, cropId)
		s.WriteOnCurrent(func() {
			s.ActiveId = active
			s.ActiveCropId = cropId
		})
		s.ReloadDiscriminator()
	}
}

func (s *DiscriminatorCfgState) getDiscriminatorForCropId(cropId string) (string, error) {
	val, err := s.redisClient.HReadStringSafe(redis.DiscriminatorActive, cropId, "")
	if err != nil {
		return "", err
	}
	if val == "" {
		return val, nil
	}
	exists, rerr := s.redisClient.HExists(redis.DiscriminatorCfgs, val)
	if rerr != nil {
		return "", err
	}
	if !exists {
		logrus.Infof("Removing active mapping for crop %v to discriminator %v as discriminator no longer exists.", cropId, val)
		s.redisClient.HDel(redis.DiscriminatorActive, cropId)
		val = ""
	}
	return val, nil
}

func (s *DiscriminatorCfgState) LoadDiscriminatorConfig(id string) (*almanac.DiscriminatorConfig, error) {
	config, err := s.redisClient.HGet(redis.DiscriminatorCfgs, id)
	if err != nil {
		logrus.Warnf("Failed to find discriminator %v", id)
		return nil, err
	}
	conf := &almanac.DiscriminatorConfig{}
	err = proto.Unmarshal([]byte(config), conf)
	if err != nil {
		logrus.Warnf("Failed to parse discriminator config %v", id)
		return nil, err
	}
	return conf, nil
}

func (s *DiscriminatorCfgState) saveDiscriminatorConfig(cfg *almanac.DiscriminatorConfig, associateWithActiveCrop bool) error {
	data, err := proto.Marshal(cfg)
	if err != nil {
		logrus.Warnf("Failed to marshal protobuf data err: %v", err)
		return err
	}
	err = s.redisClient.HSet(redis.DiscriminatorCfgs, cfg.Id, string(data))
	if err != nil {
		logrus.Warnf("Failed to save cfg to redis %v", err)
		return err
	}

	if associateWithActiveCrop {
		_, err = s.SetActiveCfg(cfg.Id, "")
		if err != nil {
			logrus.Warnf("Failed to set %v as active: %v", cfg.Id, err)
			return err
		}
	}

	curr := s.GetActiveCfg()
	if cfg.Id == curr {
		if !associateWithActiveCrop {
			s.Notify()
			return s.ReloadDiscriminator()
		}
	}

	return nil
}
func (s *DiscriminatorCfgState) SaveDiscriminatorConfig(cfg *almanac.DiscriminatorConfig, associateWithActiveCrop bool) error {
	err := s.saveDiscriminatorConfig(cfg, associateWithActiveCrop)
	if err == nil {
		err = s.callUpdateCB(cfg.Id)
	}
	return err
}

func (s *DiscriminatorCfgState) ReloadDiscriminator() error {
	for _, rowClients := range s.rows {
		err := rowClients.AimbotClient.ReloadDiscriminatorConf()
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *DiscriminatorCfgState) deleteDiscriminatorConfig(uuid string) error {
	err := s.redisClient.HDel(redis.DiscriminatorCfgs, uuid)
	if err != nil {
		logrus.Warnf("Failed to delete discriminator cfg %v, err: %v", uuid, err)
		return err
	}
	curr := s.GetActiveCfg()
	if uuid == curr {
		_, err := s.SetActiveCfg("", "") // set active to none
		if err != nil {
			logrus.Warnf("Failed to set %v as active cfg %v", uuid, err)
			return err
		}
	} else {
		s.Notify()
	}

	return nil
}
func (s *DiscriminatorCfgState) DeleteDiscriminatorConfig(uuid string) error {
	err := s.deleteDiscriminatorConfig(uuid)
	if err == nil {
		err = s.callUpdateCB(uuid)
	}
	return err
}
func (s *DiscriminatorCfgState) DeleteProfileFromSync(id string) error {
	return s.deleteDiscriminatorConfig(id)
}
func (s *DiscriminatorCfgState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *portal.GetProfileResponse_Discriminator:
		return true, s.saveDiscriminatorConfig(r.Discriminator, s.GetActiveCfg() == r.Discriminator.Id)
	default:
		return false, nil
	}
}
func (s *DiscriminatorCfgState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_Discriminator:
		return true, s.saveDiscriminatorConfig(r.Discriminator, s.GetActiveCfg() == r.Discriminator.Id)
	default:
		return false, nil
	}
}
func (s *DiscriminatorCfgState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	discriminator, err := s.LoadDiscriminatorConfig(id)
	if err == nil {
		req.Profile = &portal.UploadProfileRequest_Discriminator{
			Discriminator: discriminator,
		}
	}
	return err
}
func (s *DiscriminatorCfgState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	discriminator, err := s.LoadDiscriminatorConfig(id)
	if err == nil {
		req.Profile = &robot_syncer.UploadProfileRequest_Discriminator{
			Discriminator: discriminator,
		}
	}
	return err
}
func (s *DiscriminatorCfgState) SetActiveProfileFromSync(id string) error {
	_, err := s.SetActiveCfg(id, "")
	return err
}

func (s *ModelinatorCfgState) SetDefaultFetcher(fetcher func(string, string) (*almanac.ModelinatorConfig, error)) {
	s.WriteOnCurrent(func() {
		s.defaultFetcher = fetcher
	})
}

func (s *ModelinatorCfgState) setActiveIds(modelId string, cropId string) {
	currCrop := ""
	currModel := ""
	s.ReadOnCurrent(func() {
		currCrop = s.ActiveCropId
		currModel = s.ActiveModelId
	})
	if currCrop != cropId || currModel != modelId {
		s.WriteOnCurrent(func() {
			s.ActiveId = lib.BuildModelinatorId(modelId, cropId)
			s.ActiveCropId = cropId
			s.ActiveModelId = modelId
		})
		s.ReloadModelinator()
		d, err := s.isModelinatorSettingsDefault(modelId, cropId)
		if err != nil {
			logrus.WithError(err).Error("Error when checking if the modelionator is default")
			return
		}
		setDefaultSettingsMetric(d)
	}
}
func (s *ModelinatorCfgState) GetCurrentIds() (string, string, string) {
	activeId := ""
	modelId := ""
	cropId := ""
	s.ReadOnCurrent(func() {
		activeId = s.ActiveId
		modelId = s.ActiveModelId
		cropId = s.ActiveCropId
	})
	return activeId, modelId, cropId
}

func (s *ModelinatorCfgState) fetchDefault(modelId string, cropId string) (*almanac.ModelinatorConfig, error) {
	var conf *almanac.ModelinatorConfig = nil
	var err error = nil
	s.ReadOnCurrent(func() {
		if s.defaultFetcher != nil {
			conf, err = s.defaultFetcher(modelId, cropId)
		} else {
			err = errors.New("no fetcher set")
		}
	})
	return conf, err
}

func (s *ModelinatorCfgState) ResetToDefault() error {
	_, modelId, cropId := s.GetCurrentIds()
	conf, err := s.fetchDefault(modelId, cropId)
	if err != nil {
		return err
	}
	err = s.saveModelinatorConfig(conf)
	if err != nil {
		return err
	}
	s.ReloadModelinator()
	return nil
}

func (s *ModelinatorCfgState) FetchModelinatorConfig(modelId string, cropId string) (*almanac.ModelinatorConfig, error) {
	id := lib.BuildModelinatorId(modelId, cropId)
	return s.redisClient.FetchModelinatorConfigById(id)
}
func (s *ModelinatorCfgState) FetchActiveModelinatorConfig() (*almanac.ModelinatorConfig, error) {
	_, modelId, cropId := s.GetCurrentIds()
	return s.FetchModelinatorConfig(modelId, cropId)
}
func (s *ModelinatorCfgState) saveAnyModelinatorConfig(cfg *almanac.ModelinatorConfig) error {
	data, merr := proto.Marshal(cfg)
	if merr != nil {
		logrus.Warnf("Failed to marshal protobuf data err: %v", merr)
		return merr
	}
	id := lib.BuildModelinatorId(cfg.GetModelId(), cfg.GetCropId())
	rerr := s.redisClient.HSet(redis.ModelinatorCfgs, id, string(data))
	if rerr != nil {
		return rerr
	}
	return nil
}
func (s *ModelinatorCfgState) getRemoteProfileId(id string) string {
	return id + "/" + s.robotName
}
func (s *ModelinatorCfgState) saveModelinatorConfig(cfg *almanac.ModelinatorConfig) error {
	_, modelId, cropId := s.GetCurrentIds()
	if cfg.GetModelId() != modelId || cfg.GetCropId() != cropId {
		msg := fmt.Sprintf("Mismatch between save config and active ids (model id=%v, crop id=%v)", modelId, cropId)
		logrus.Warn(msg)
		return errors.New(msg)
	}
	s.saveAnyModelinatorConfig(cfg)
	s.Notify()
	s.ReloadModelinator()
	id := lib.BuildModelinatorId(cfg.ModelId, cfg.CropId)
	profileId := s.getRemoteProfileId(id)
	if err := s.callUpdateCB(profileId); err != nil {
		return err
	}
	d, err := s.isModelinatorSettingsDefault(cfg.ModelId, cfg.CropId)
	if err != nil {
		return err
	}
	setDefaultSettingsMetric(d)
	return nil
}
func (s *ModelinatorCfgState) SaveModelinatorConfig(cfg *almanac.ModelinatorConfig) error {
	cfg.Modified = true
	return s.saveModelinatorConfig(cfg)
}
func (s *ModelinatorCfgState) ReloadModelinator() error {
	for _, rowClients := range s.rows {
		err := rowClients.AimbotClient.ReloadModelinatorConf()
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *ModelinatorCfgState) newDefaultModelinatorCB(cfg *almanac.ModelinatorConfig) {
	curCfg, err := s.FetchModelinatorConfig(cfg.GetModelId(), cfg.GetCropId())
	if err != nil {
		logrus.Warn("Failed to fetch current version of cfg, assuming we can overwrite it")
	}
	if curCfg.GetModified() {
		logrus.Infof("Not updating modelinator for crop %v, model %v as user has modified it.", cfg.GetCropId(), cfg.GetModelId())
		return
	}
	_, modelId, cropId := s.GetCurrentIds()
	s.saveAnyModelinatorConfig(cfg)
	id := lib.BuildModelinatorId(cfg.GetModelId(), cfg.GetCropId())
	remoteId := s.getRemoteProfileId(id)
	s.callUpdateCB(remoteId)
	if cfg.GetModelId() == modelId && cfg.GetCropId() == cropId {
		// Active has been updated, so need to notify
		s.Notify()
		s.ReloadModelinator()
	}
}

func (s *ModelinatorCfgState) CopyFrom(cropID string, oldModelID string, newModelID string) error {
	cpyCfg, err := s.FetchModelinatorConfig(oldModelID, cropID)
	if err != nil {
		return err
	}
	cpyCfg.ModelId = newModelID
	cpyCfg.Modified = true // Since this is copied we should set this true
	_, modelId, cropId := s.GetCurrentIds()
	err = s.saveAnyModelinatorConfig(cpyCfg)
	if err != nil {
		return err
	}
	id := lib.BuildModelinatorId(cpyCfg.GetModelId(), cpyCfg.GetCropId())
	remoteId := s.getRemoteProfileId(id)
	s.callUpdateCB(remoteId)
	if cpyCfg.GetModelId() == modelId && cpyCfg.GetCropId() == cropId {
		// Active has been updated, so need to notify
		s.Notify()
		s.ReloadModelinator()
	}
	return nil
}

func (s *ModelinatorCfgState) DeleteProfileFromSync(id string) error {
	return errors.New("Cannot delete modelinators.")
}
func (s *ModelinatorCfgState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *portal.GetProfileResponse_Modelinator:
		cfg := r.Modelinator
		cfg.Modified = true
		err := s.saveAnyModelinatorConfig(cfg)
		if err == nil {
			s.callUpdateCB(lib.BuildModelinatorId(cfg.GetModelId(), cfg.GetCropId()))

			_, modelId, cropId := s.GetCurrentIds()
			if modelId == cfg.ModelId && cropId == cfg.CropId {
				s.Notify()
				s.ReloadModelinator()
			}
		}
		return true, err
	default:
		return false, nil
	}
}
func (s *ModelinatorCfgState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_Modelinator:
		cfg := r.Modelinator
		cfg.Modified = true
		err := s.saveAnyModelinatorConfig(cfg)
		if err == nil {
			s.callUpdateCB(lib.BuildModelinatorId(cfg.GetModelId(), cfg.GetCropId()))

			_, modelId, cropId := s.GetCurrentIds()
			if modelId == cfg.ModelId && cropId == cfg.CropId {
				s.Notify()
				s.ReloadModelinator()
			}
		}
		return true, err
	default:
		return false, nil
	}
}
func (s *ModelinatorCfgState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	modelinator, err := s.redisClient.FetchModelinatorConfigById(getLocalModelinatorId(id))
	if err == nil {
		req.Profile = &portal.UploadProfileRequest_Modelinator{
			Modelinator: modelinator,
		}
	}
	return err
}
func (s *ModelinatorCfgState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	modelinator, err := s.redisClient.FetchModelinatorConfigById(getLocalModelinatorId(id))
	if err == nil {
		req.Profile = &robot_syncer.UploadProfileRequest_Modelinator{
			Modelinator: modelinator,
		}
	}
	return err
}
func (s *ModelinatorCfgState) SetActiveProfileFromSync(id string) error {
	return errors.New("Cannot set active modelinator.")
}
func getLocalModelinatorId(fullId string) string {
	idx := strings.LastIndex(fullId, "/")
	if idx > 0 {
		return fullId[:idx]
	}
	return fullId
}

func (s *ModelinatorCfgState) isModelinatorSettingsDefault(modelId string, cropId string) (bool, error) {
	curCfg, err := s.FetchModelinatorConfig(modelId, cropId)
	if err != nil {
		return false, err
	}
	defaultCfg, err := s.fetchDefault(modelId, cropId)
	if err != nil {
		return false, err
	}

	curCats := curCfg.GetCategories()
	defCats := defaultCfg.GetCategories()
	if len(curCats) != len(defCats) {
		return false, nil
	}

	// build a map to check the categories regardless of order
	curCatsMap := modelinatorCategoriesToMap(curCats)
	defCatsMap := modelinatorCategoriesToMap(defCats)
	for uuid, defTrusts := range defCatsMap {
		curTrusts, ok := curCatsMap[uuid]
		if !ok {
			return false, nil
		}

		if len(curTrusts) != len(defTrusts) {
			return false, nil
		}

		for i := range defTrusts {
			if !proto.Equal(curTrusts[i], defTrusts[i]) {
				return false, nil
			}
		}
	}
	return true, nil
}

func bootStrapAlmanac() {
	cmd := exec.Command("python", "-m", "tools.almanac.almanac_dumper", "--load", "--bootstrap", "tools/almanac/bootstrap.json")
	out, err := cmd.Output()
	if err != nil {
		logrus.Warnf("Bootstrap failed err: %v, out: %v", err, string(out))
	}
}

func ReadActiveAlmanacId(redisClient *redis.Client) (string, error) {
	return redisClient.ReadString(redis.AlmanacActive, "")
}

func setDefaultSettingsMetric(isDefault bool) {
	d := float64(0)
	if isDefault {
		d = float64(1)
	}
	ActiveRecomendedModelinatorGauge.Set(d)
}

func modelinatorCategoriesToMap(cats []*almanac.ModelinatorTypeCategory) map[string][]*almanac.ModelTrust {
	res := make(map[string][]*almanac.ModelTrust) // uuid -> size trusts
	for _, cat := range cats {
		res[cat.GetType().GetCategory()] = cat.GetTrusts()
	}
	return res
}

package state

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/aimbot_client"
	"github.com/carbonrobotics/robot/golang/lib/client_owner"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_definition"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/sirupsen/logrus"
)

const (
	presetDir = "/robot/reaper_presets"

	aimbotAddrTemplate = "10.10.20.%d:6942"
	swmgAddrTemplate   = "10.10.20.%d:61005"
)

type RedisRDClient interface {
	HSetWithContext(ctx context.Context, hash string, val ...any) error
	HGetWithContext(ctx context.Context, hash string, key string) (string, error)
	HGetAllWithContext(ctx context.Context, hash string) (map[string]string, error)
	HDelWithContext(ctx context.Context, key string, member ...string) error
	HExistsWithCtx(ctx context.Context, hash string, key string) (bool, error)
}

type PresetStore struct {
	Preset       *frontend.Preset  `json:"preset"`
	Translations map[string]string `json:"translations"`
}

type RobotDefinitionState struct {
	ManagedStateImpl
	redisClient RedisRDClient
	logger      *logrus.Entry

	clientOwner *client_owner.ClientOwner

	Presets           []*PresetStore
	CurrentDefinition *robot_definition.RobotDefinition
}

func NewRobotDefinitionState(redisClient *redis.Client, clientOwner *client_owner.ClientOwner) *RobotDefinitionState {
	s := &RobotDefinitionState{
		ManagedStateImpl: ManagedStateImpl{name: "RobotDefinitionState"},
		redisClient:      redisClient,
		logger:           logrus.WithField("module", "RobotDefinitionState"),

		clientOwner: clientOwner,

		Presets:           make([]*PresetStore, 0),
		CurrentDefinition: &robot_definition.RobotDefinition{},
	}
	s.logger.Info("Initializing RobotDefinitionState")
	s.init()
	s.logger.Info("RobotDefinitionState initialized")
	return s
}

func (s *RobotDefinitionState) init() {
	s.ManagedStateImpl.initialize()
	err := s.readPresetsFromJSON()
	if err != nil {
		s.logger.WithError(err).Error("Failed to read presets from JSON")
	}
	def, err := s.readCurrentDefFromRedis()
	if err != nil {
		s.logger.WithError(err).Fatal("Failed to read current robot definition from redis, exiting")
		return
	}
	s.CurrentDefinition = def
}

func (s *RobotDefinitionState) DefinitionIsSet() bool {
	ret := false
	s.ReadOnCurrent(func() {
		ret = s.CurrentDefinition.Number != 0
	})
	return ret
}

func (s *RobotDefinitionState) PushRobotDefinitionUpdate() {
	hostsClients := s.clientOwner.GetHostClients()
	s.logger.Infof("Pushing robot definition update to %v hosts", len(hostsClients))
	wg := sync.WaitGroup{}
	for i, host := range hostsClients {
		if i == 0 {
			// skip the first host, its command, restart that last
			continue
		}
		wg.Add(1)
		go func(host *hosts.HostClients) {
			defer wg.Done()
			err := host.SoftwareManagerClient.PushRobotDefinitionUpdate()
			if err != nil {
				s.logger.WithError(err).Errorf("Failed to push robot definition update to host %v", host.IpAddress)
			}
		}(host)
	}
	wg.Wait()
	s.logger.Infof("Pushing robot definition update to command host")
	hostsClients[0].SoftwareManagerClient.PushRobotDefinitionUpdate()
}

func (s *RobotDefinitionState) RobotDefinitionUpdate() {
	// here we would call whatever callbacks needed

	// reload clients to get get the correct number of hosts for the new definition
	s.clientOwner.ReloadClients()
	s.PushRobotDefinitionUpdate()
}

func checkDefinition(protoDef *frontend.RobotDefinition) error {
	if protoDef == nil {
		return errors.New("robot definition is nil")
	} else if protoDef.BarDefinition == nil {
		return errors.New("bar definition is nil")
	} else {
		lastRowId := uint32(0)
		lastModuleId := uint32(0)
		for i, row := range protoDef.Rows {
			if row == nil {
				return errors.New("row definition is nil")
			} else if row.RowId == 0 {
				return errors.New("row id can't be 0")
			} else if len(row.Modules) == 0 {
				return fmt.Errorf("row %v has no modules", row.RowId)
			} else if i != 0 && row.RowId != lastRowId+1 {
				return errors.New("rows are not sequential")
			}
			for j, module := range row.Modules {
				if module == nil {
					return fmt.Errorf("module in row %v is nil", row.RowId)
				} else if module.ModuleId == 0 {
					return errors.New("module id can't be 0")
				} else if !(i == 0 && j == 0) && module.ModuleId != lastModuleId+1 {
					if j == 0 {
						return fmt.Errorf("modules in row %v are not sequential with previous row", row.RowId)
					}
					return fmt.Errorf("modules in row %v are not sequential", row.RowId)
				} else if j != 0 && module.ModuleSpacingMm == 0 {
					return fmt.Errorf("module %v in row %v has a 0 spacing", module.ModuleId, row.RowId)
				}
				lastModuleId = module.ModuleId
			}
			lastRowId = row.RowId
		}
	}
	return nil
}

func (s *RobotDefinitionState) SetCurrentRobotDefinition(protoDef *frontend.RobotDefinition) error {
	if err := checkDefinition(protoDef); err != nil {
		return fmt.Errorf("invalid robot definition: %w", err)
	}

	s.logger.Infof("%v", protoDef)

	def := &robot_definition.RobotDefinition{}
	def.FromProto(protoDef)

	s.logger.Infof("%+v", def)

	def.AssignAimbotHosts()

	var err error
	s.WriteOnCurrent(func() {

		def.Number = s.CurrentDefinition.Number + 1

		err = s.writeCurrentDefToRedis(def)
		if err != nil {
			err = fmt.Errorf("failed to write current robot definition to redis: %w", err)
			return
		}

		s.CurrentDefinition = def
	})

	if err != nil {
		return err
	}
	go s.RobotDefinitionUpdate()
	return nil
}

func (s *RobotDefinitionState) readPresetsFromJSON() error {
	files, err := os.ReadDir(presetDir)
	if err != nil {
		return fmt.Errorf("failed to read presets directory: %w", err)
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		data, err := os.ReadFile(filepath.Join(presetDir, file.Name()))
		if err != nil {
			s.logger.WithError(err).Error("Failed to read preset file")
			continue
		}

		preset := &PresetStore{}
		err = json.Unmarshal(data, preset)
		if err != nil {
			s.logger.WithError(err).Error("Failed to unmarshal preset")
			continue
		}
		if preset.Preset == nil {
			continue
		}

		s.logger.Infof("Preset: %v", preset)
		s.Presets = append(s.Presets, preset)
	}

	return nil
}

func (s *RobotDefinitionState) readCurrentDefFromRedis() (*robot_definition.RobotDefinition, error) {
	defExists, err := robot_definition.CurrentDefExistsInRedis(s.redisClient)
	if err != nil {
		return nil, err
	}
	if !defExists {
		return &robot_definition.RobotDefinition{}, nil
	}
	return robot_definition.ReadCurrentDefFromRedis(s.redisClient)
}

func (s *RobotDefinitionState) writeCurrentDefToRedis(def *robot_definition.RobotDefinition) error {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	data, err := json.Marshal(def)
	if err != nil {
		return err
	}

	err = s.redisClient.HSetWithContext(ctx, robot_definition.RobotDefinitionStateKey, robot_definition.CurrentRobotDefinitionKey, data)
	if err != nil {
		return err
	}
	return nil
}

// TODO(jfroel): RobotDefinitionEnforcer
// right now this just enforces the aimbot participation

type EnforcedHost struct {
	id uint32

	// clients
	aimbotClient *aimbot_client.AimbotClient
	swmgClient   *software_manager_client.SoftwareManagerClient

	//states
	expectedAimbotLeadership bool
}

type RobotDefinitionEnforcer struct {
	EventTrigger
	logger               *logrus.Entry
	robotDefinitionState *RobotDefinitionState

	enforcedHosts []*EnforcedHost
}

func NewRobotDefinitionEnforcer(robotDefinitionState *RobotDefinitionState) *RobotDefinitionEnforcer {
	e := &RobotDefinitionEnforcer{
		EventTrigger:         EventTrigger{triggerChannel: make(chan bool)},
		logger:               logrus.WithField("module", "RobotDefinitionEnforcer"),
		robotDefinitionState: robotDefinitionState,
	}
	e.ReloadHosts()
	return e
}

func (e *RobotDefinitionEnforcer) ReloadHosts() {
	e.enforcedHosts = make([]*EnforcedHost, 0)
	e.robotDefinitionState.ReadOnCurrent(func() {
		for _, row := range e.robotDefinitionState.CurrentDefinition.Rows {
			for _, module := range row.Modules {
				e.enforcedHosts = append(e.enforcedHosts, &EnforcedHost{
					id:                       module.ModuleId,
					aimbotClient:             aimbot_client.NewAimbotClient(fmt.Sprintf(aimbotAddrTemplate, module.ModuleId)),
					swmgClient:               software_manager_client.NewSoftwareManagerClient(fmt.Sprintf(swmgAddrTemplate, module.ModuleId)),
					expectedAimbotLeadership: module.AimbotHost,
				})
			}
		}
	})
}

func (e *RobotDefinitionEnforcer) Action() {
	for _, enforcedHost := range e.enforcedHosts {
		participation, err := enforcedHost.aimbotClient.GetParticipation()
		if err != nil {
			e.logger.WithError(err).Errorf("failed to get aimbot participation for host: %v", enforcedHost.id)
			continue
		}

		if participation.GetRunningAsLeader() != enforcedHost.expectedAimbotLeadership {
			e.logger.Infof("Host %v aimbot participation is not as expected, requesting restart", enforcedHost.id)
			err = enforcedHost.swmgClient.RestartDependentServices()
			if err != nil {
				e.logger.WithError(err).Errorf("failed to request restart for host: %v", enforcedHost.id)
				continue
			}
		}
	}
}

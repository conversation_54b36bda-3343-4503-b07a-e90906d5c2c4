package state

import (
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
)

type DataCaptureStep int32

func (d DataCaptureStep) IsNot(steps ...DataCaptureStep) bool {
	for _, step := range steps {
		if d == step {
			return false
		}
	}
	return true
}

// Steps are meant to provide the frontend with an idea about where in the emergency process we are.
const (
	DataCaptureStepNew = DataCaptureStep(iota)
	DataCaptureStepCapturing
	DataCaptureStepCapturePaused
	DataCaptureStepCaptureComplete
	DataCaptureStepUploadingWireless
	DataCaptureStepUploadingWirelessPaused
	DataCaptureStepUploadingUSB
	DataCaptureStepUploadingUSBPaused
	DataCaptureStepUploadingComplete
)

// Session is meant to represent the emergency session
type DataCaptureSession struct {
	ImagesTaken                     uint32
	TargetImagesTaken               uint32
	EstimatedCaptureRemainingTimeMs uint64
	ImagesUploaded                  uint32
	TargetImagesUploaded            uint32
	EstimatedUploadRemainingTimeMs  uint64
	CaptureRate                     float64
	CaptureStatus                   string
	UploadStatus                    string
	SessionName                     string
	CaptureTimestampStart           uint64
	UploadTimestampStart            uint64
	Crop                            string
	CropID                          string
	ErrorMessage                    string
}

// State holds the step and session.
type DataCaptureState struct {
	ManagedStateImpl
	Session                 DataCaptureSession
	Step                    DataCaptureStep
	WirelessUploadAvailable bool
	USBStorageConnected     bool
}

func NewDataCaptureState(configSubscriber *config.ConfigSubscriber) *DataCaptureState {
	state := &DataCaptureState{ManagedStateImpl: ManagedStateImpl{name: "DataCaptureState"}}
	state.initialize()
	state.Session.TargetImagesTaken = uint32(configSubscriber.GetConfigNode("data_upload_manager", "emergency_capture_target").GetUIntValue())
	state.Session.TargetImagesUploaded = uint32(configSubscriber.GetConfigNode("data_upload_manager", "emergency_upload_target").GetUIntValue())
	state.WirelessUploadAvailable = configSubscriber.GetConfigNode("data_upload_manager", "use_online_upload_flow").GetBoolValue()
	state.USBStorageConnected = configSubscriber.GetConfigNode("data_upload_manager", "use_offline_upload_flow").GetBoolValue()
	return state
}

// Watcher should hold anything (config nodes, clients) that will be used to update state in Action()
type DataCaptureWatcher struct {
	EventTrigger
	dataState              *DataCaptureState
	dumClient              *data_upload_manager.EmergencyClient
	wirelessAvailable      *config.ConfigTree
	usbAvailable           *config.ConfigTree
	emergencyCaptureTarget *config.ConfigTree
	emergencyUploadTarget  *config.ConfigTree
	hamClient              *hardware_manager.HardwareManagerClient
	offlineAgentMaxLoad    *config.ConfigTree
}

func NewDataCaptureWatcher(dataState *DataCaptureState, dumClient *data_upload_manager.EmergencyClient, configSubscriber *config.ConfigSubscriber, hamClient *hardware_manager.HardwareManagerClient) *DataCaptureWatcher {
	action := &DataCaptureWatcher{
		dataState:              dataState,
		dumClient:              dumClient,
		wirelessAvailable:      configSubscriber.GetConfigNode("data_upload_manager", "use_online_upload_flow"),
		usbAvailable:           configSubscriber.GetConfigNode("data_upload_manager", "use_offline_upload_flow"),
		emergencyCaptureTarget: configSubscriber.GetConfigNode("data_upload_manager", "emergency_capture_target"),
		emergencyUploadTarget:  configSubscriber.GetConfigNode("data_upload_manager", "emergency_upload_target"),
		hamClient:              hamClient,
		offlineAgentMaxLoad:    configSubscriber.GetConfigNode("data_upload_manager", "offline_agent_max_load"),
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *DataCaptureWatcher) Action() {
	// Get state parameters
	var session DataCaptureSession
	var wireless bool
	var usb bool
	var oldWireless bool
	var oldUSB bool
	var step DataCaptureStep
	errorMessage := ""

	wireless = w.wirelessAvailable.GetBoolValue()
	usb = w.usbAvailable.GetBoolValue()

	w.dataState.ReadOnCurrent(func() {
		session = w.dataState.Session
		step = w.dataState.Step
		oldUSB = w.dataState.USBStorageConnected
		oldWireless = w.dataState.WirelessUploadAvailable
	})

	var changed bool = false

	if oldUSB != usb || oldWireless != wireless {
		changed = true
	}

	captureTarget := uint32(w.emergencyCaptureTarget.GetUIntValue())
	uploadTarget := uint32(w.emergencyUploadTarget.GetUIntValue())
	if captureTarget != session.TargetImagesTaken {
		session.TargetImagesTaken = captureTarget
		changed = true
	}
	if uploadTarget != session.TargetImagesUploaded {
		session.TargetImagesUploaded = uploadTarget
		changed = true
	}

	switch step {
	case DataCaptureStepNew:
		progress, err := w.dumClient.GetCaptureProgress()
		if err != nil {
			logrus.Errorf("Cannot get capturing progress %v", err)
			return
		}
		if progress.Exists && progress.IsCapturing {
			session.ImagesTaken = 0
			session.CaptureTimestampStart = uint64(time.Now().UnixMilli())
			session.EstimatedCaptureRemainingTimeMs = 600000
			step = DataCaptureStepCapturing
			changed = true
		}
	case DataCaptureStepCapturing:
		progress, err := w.dumClient.GetCaptureProgress()
		captureSessionExists := progress.Exists
		imagesTaken := progress.ImagesCaptured
		isCapturing := progress.IsCapturing
		hasCompleted := progress.HasCompleted
		if err != nil {
			logrus.Errorf("Cannot get capturing progress %v", err)
			return
		}
		if !captureSessionExists {
			step = DataCaptureStepNew
			changed = true
			break
		}
		if session.ImagesTaken != imagesTaken {
			session.ImagesTaken = imagesTaken

			if imagesTaken > 0 {
				now := uint64(time.Now().UnixMilli())
				start := session.CaptureTimestampStart
				diff := now - start
				perImageMs := diff / uint64(imagesTaken)

				imagesLeft := uint64(session.TargetImagesTaken - imagesTaken)
				session.EstimatedCaptureRemainingTimeMs = imagesLeft * perImageMs
			}

			changed = true
		}
		if !isCapturing {
			step = DataCaptureStepCapturePaused
			changed = true
		}
		if hasCompleted {
			step = DataCaptureStepCaptureComplete
			changed = true
		}
	case DataCaptureStepCapturePaused:
		progress, err := w.dumClient.GetCaptureProgress()
		captureSessionExists := progress.Exists
		isCapturing := progress.IsCapturing
		if err != nil {
			logrus.Errorf("Cannot get capturing progress %v", err)
			return
		}
		if !captureSessionExists {
			step = DataCaptureStepNew
			changed = true
			break
		}
		if isCapturing {
			step = DataCaptureStepCapturing
			changed = true
		}
	case DataCaptureStepCaptureComplete:
		progress, err := w.dumClient.GetUploadProgress()
		exists := progress.Exists
		method := progress.Method
		isUploading := progress.IsUploading
		if err != nil {
			logrus.Errorf("Cannot get upload session %v", err)
			return
		}
		captureProgress, err := w.dumClient.GetCaptureProgress()
		if err != nil {
			logrus.Errorf("Cannot get capture session %v", err)
			return
		}
		if exists && isUploading {
			changed = true
			session.UploadTimestampStart = uint64(time.Now().UnixMilli())
			session.EstimatedUploadRemainingTimeMs = 600000
			session.ImagesUploaded = 0
			if method == "wireless" {
				step = DataCaptureStepUploadingWireless
			} else if method == "usb" {
				step = DataCaptureStepUploadingUSB
			}
		} else if !captureProgress.Exists {
			changed = true
			step = DataCaptureStepNew
		}
	case DataCaptureStepUploadingWireless, DataCaptureStepUploadingUSB:
		progress, err := w.dumClient.GetUploadProgress()
		imagesUploaded := progress.ImagesUploaded
		isUploading := progress.IsUploading
		method := progress.Method
		exists := progress.Exists
		hasCompleted := progress.HasCompleted
		if err != nil {
			logrus.Errorf("Cannot get upload progress %v", err)
			return
		}
		if !exists {
			step = DataCaptureStepCaptureComplete
			changed = true
			break
		}
		if session.ImagesUploaded != imagesUploaded {
			session.ImagesUploaded = imagesUploaded

			if imagesUploaded > 0 {
				now := uint64(time.Now().UnixMilli())
				start := session.UploadTimestampStart
				diff := now - start
				perImageMs := diff / uint64(imagesUploaded)

				imagesLeft := uint64(session.TargetImagesUploaded - imagesUploaded)
				session.EstimatedUploadRemainingTimeMs = imagesLeft * perImageMs
			}

			changed = true
		}
		if !isUploading {
			if method == "wireless" {
				step = DataCaptureStepUploadingWirelessPaused
				changed = true
			} else if method == "usb" {
				step = DataCaptureStepUploadingUSBPaused
				changed = true
			}
		}

		if hasCompleted {
			step = DataCaptureStepUploadingComplete
			changed = true
		}
	case DataCaptureStepUploadingWirelessPaused, DataCaptureStepUploadingUSBPaused:
		progress, err := w.dumClient.GetUploadProgress()
		isUploading := progress.IsUploading
		method := progress.Method
		exists := progress.Exists
		if err != nil {
			logrus.Errorf("Cannot get upload progress %v", err)
			return
		}
		if !exists {
			step = DataCaptureStepCaptureComplete
			changed = true
			break
		}
		if isUploading {
			if method == "wireless" {
				step = DataCaptureStepUploadingWireless
				changed = true
			} else if method == "usb" {
				step = DataCaptureStepUploadingUSB
				changed = true
			}
		} else {
			usbUsed, success, usbAvailable, err := w.hamClient.GetAvailableUSBStorage()

			if err != nil {
				logrus.Errorf("Failed to get USB status %v", err)
			}

			if method == "usb" && success {
				if !usbAvailable {
					errorMessage = "Upload Paused: USB is not detected, replace USB and press Resume"
				} else if usbUsed >= float32(w.offlineAgentMaxLoad.GetFloatValue()) {
					errorMessage = "Upload Paused: USB is full, replace USB and press Resume"
				}
			}
		}
	case DataCaptureStepUploadingComplete:
		step = DataCaptureStepNew
		changed = true
	default:
	}

	if errorMessage != session.ErrorMessage {
		session.ErrorMessage = errorMessage
		changed = true
	}

	if changed {
		w.dataState.WriteOnCurrent(func() {
			w.dataState.Session = session
			w.dataState.Step = step
			w.dataState.WirelessUploadAvailable = wireless
			w.dataState.USBStorageConnected = usb
		})
	}
}

type DataStorageState struct {
	ManagedStateImpl
	success           bool
	usbUsedPercentage float32
}

func NewDataStorageState() *DataStorageState {
	state := &DataStorageState{ManagedStateImpl: ManagedStateImpl{name: "DataStorageState"}}
	state.initialize()
	return state
}

type DataStorageWatcher struct {
	EventTrigger
	dataStorageState *DataStorageState
	hamClient        *hardware_manager.HardwareManagerClient
}

func NewDataStorageWatcher(dataStorageState *DataStorageState, hamClient *hardware_manager.HardwareManagerClient) *DataStorageWatcher {
	action := &DataStorageWatcher{
		dataStorageState: dataStorageState,
		hamClient:        hamClient,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *DataStorageWatcher) Action() {
	var usbUsedPercentage float32

	w.dataStorageState.ReadOnCurrent(func() {
		usbUsedPercentage = w.dataStorageState.usbUsedPercentage
	})

	usbUsed, _, _, err := w.hamClient.GetAvailableUSBStorage()

	if err != nil {
		logrus.Errorf("Cannot get usb storage status %v", err)
		return
	}

	if usbUsed != usbUsedPercentage {
		w.dataStorageState.WriteOnCurrent(func() {
			w.dataStorageState.usbUsedPercentage = usbUsed
		})
	}
}

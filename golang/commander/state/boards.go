package state

import (
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
)

type BoardState struct {
	ManagedStateImpl
	errors      []string
	encoder_err string
	gps_has_fix bool
}

func NewBoardState() *BoardState {
	state := &BoardState{ManagedStateImpl: ManagedStateImpl{name: "BoardState"}}
	state.initialize()
	return state
}

type BoardWatcher struct {
	EventTrigger
	boardState            *BoardState
	hardwareManagerClient *hardware_manager.HardwareManagerClient
}

func NewBoardWatcher(boardState *BoardState, hardwareManagerClient *hardware_manager.HardwareManagerClient) *BoardWatcher {
	action := &BoardWatcher{
		boardState:            boardState,
		hardwareManagerClient: hardwareManagerClient,
	}
	action.triggerChannel = make(chan bool)
	return action
}
func contains(s []string, str string) bool {
	for _, v := range s {
		if v == str {
			return true
		}
	}

	return false
}

func (w *BoardWatcher) Action() {
	boards, encoder_err, gps_has_fix, err := w.hardwareManagerClient.GetManagedBoardErrors()

	if err != nil {
		logrus.Errorf("Error getting board status %v", err)
	}
	var changed bool = false

	w.boardState.ReadOnCurrent(func() {
		for _, v := range boards {
			if !contains(w.boardState.errors, v) {
				changed = true
				break
			}
		}
		for _, v := range w.boardState.errors {
			if !contains(boards, v) {
				changed = true
				break
			}
		}
		if w.boardState.encoder_err != encoder_err ||
			w.boardState.gps_has_fix != gps_has_fix {
			changed = true
		}
	})

	if changed {
		w.boardState.WriteOnCurrent(func() {
			w.boardState.errors = boards
			w.boardState.encoder_err = encoder_err
			w.boardState.gps_has_fix = gps_has_fix
		})
	}
}

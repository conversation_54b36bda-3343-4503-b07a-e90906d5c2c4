package state

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	crredis "github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

var (
	modelHistoryLogger = logrus.New()
	Year               = 365 * 24 * time.Hour
	Day                = 24 * time.Hour
)

const (
	commanderModelManagerHistoryKey = "commander:mm:history"

	RobotStart             ModelEventType = "robot-start"
	Pinned                 ModelEventType = "pinned"
	UnPinned               ModelEventType = "unpinned"
	Recommended            ModelEventType = "recommended"
	Activated              ModelEventType = "activated"
	NicknameChange         ModelEventType = "nickname-change"
	NicknameDelete         ModelEventType = "nickname-delete"
	DefaultParameterChange ModelEventType = "default-parameter-change"
	ParameterChange        ModelEventType = "parameter-change"
	None                   ModelEventType = ""
)

type ModelEventType string

func ModelEventTypeFromStr(str string) ModelEventType {
	switch str {
	case string(RobotStart):
		return RobotStart
	case string(Pinned):
		return Pinned
	case string(UnPinned):
		return UnPinned
	case string(Recommended):
		return Recommended
	case string(Activated):
		return Activated
	case string(NicknameChange):
		return NicknameChange
	case string(NicknameDelete):
		return NicknameDelete
	case string(DefaultParameterChange):
		return DefaultParameterChange
	case string(ParameterChange):
		return ParameterChange
	}
	return None
}

type ModelEventTypes []ModelEventType

func (m ModelEventTypes) Contains(eventType ModelEventType) bool {
	for _, et := range m {
		if et == eventType {
			return true
		}
	}
	return false
}

type ModelEvent struct {
	Type            ModelEventType `json:"type,omitempty"`
	ModelID         string         `json:"model_id,omitempty"`
	ModelNickname   string         `json:"model_nickname,omitempty"`
	ModelParameters string         `json:"model_parameters,omitempty"`
	ModelType       string         `json:"model_type,omitempty"`
	CropID          string         `json:"crop_id,omitempty"`
	JobName         string         `json:"job_name,omitempty"`
	Time            time.Time      `json:"time,omitempty"`
}

func (me ModelEvent) Filtered(event ModelEvent) bool {
	if len(me.Type) > 0 && me.Type != event.Type {
		return true
	}
	if len(me.ModelID) > 0 && me.ModelID != event.ModelID {
		return true
	}
	if len(me.ModelNickname) > 0 && me.ModelNickname != event.ModelNickname {
		return true
	}
	if len(me.ModelType) > 0 && me.ModelType != event.ModelType {
		return true
	}
	if len(me.CropID) > 0 && me.CropID != event.CropID {
		return true
	}
	if len(me.JobName) > 0 && me.JobName != event.JobName {
		return true
	}
	return false
}

func (me ModelEvent) String() string {
	b, err := json.Marshal(me)
	if err != nil {
		return err.Error()
	}
	return string(b)
}

func (me ModelEvent) Valid() error {
	// TODO:(smt) validate based on event?
	if me.Type == "" {
		return errors.New("invalid Type")
	}
	if me.Time.IsZero() {
		return errors.New("invalid Time")
	}
	return nil
}

type ModelHistoryTracker struct {
	redis        crredis.RedisClient // Changed type to the common interface
	historyRange time.Duration
	eventCh      chan ModelEvent
	logger       *logrus.Entry
	wg           sync.WaitGroup
	running      atomic.Bool
}

func NewModelHistoryTracker(ctx context.Context, redisClient crredis.RedisClient) *ModelHistoryTracker { // Changed parameter type
	tracker := &ModelHistoryTracker{
		redis:        redisClient,
		eventCh:      make(chan ModelEvent, 1000),
		historyRange: Year,
		logger:       modelHistoryLogger.WithFields(logrus.Fields{"module": "ModelHistoryTracker"}),
	}
	tracker.running.Store(true)
	tracker.logger.Info("starting model history tracker")
	tracker.wg.Add(1)
	if err := tracker.pruneEvents(ctx, time.Now().Add(-tracker.historyRange)); err != nil {
		tracker.logger.WithError(err).Warn("history pruning aborted")
	}
	go tracker.startEventProcessor(ctx)
	go func() {
		select {
		case <-ctx.Done():
			tracker.Stop()
		}
	}()
	return tracker
}

func (mht *ModelHistoryTracker) SetLogLevel(level logrus.Level) {
	modelHistoryLogger.SetLevel(level)
}

func (mht *ModelHistoryTracker) SetLogFormatter(formatter *logrus.TextFormatter) {
	modelHistoryLogger.SetFormatter(formatter)
}

func (mht *ModelHistoryTracker) Stop() {
	if mht.running.Load() {
		mht.running.Store(false)
		close(mht.eventCh)
		mht.wg.Wait()
	}
}

func (mht *ModelHistoryTracker) startEventProcessor(ctx context.Context) {
	defer mht.wg.Done()
	for event := range mht.eventCh {
		mht.logger.Debug("processing event:", event)
		if _, err := mht.redis.ZAdd(ctx, commanderModelManagerHistoryKey, &redis.Z{
			Score:  float64(event.Time.UnixMilli()),
			Member: event.String(),
		}); err != nil {
			mht.logger.WithError(err).Errorf("failed to add event: %s", event)
		}
	}
}

func (mht *ModelHistoryTracker) pruneEvents(ctx context.Context, before time.Time) error {
	mht.logger.Infof("pruning events older than %s", before.Format(time.RFC822))
	results, err := mht.redis.ZRevRangeByScoreWithScores(ctx, commanderModelManagerHistoryKey, &redis.ZRangeBy{
		Min:   fmt.Sprint(before.UnixMilli()),
		Max:   crredis.NegInfinity,
		Count: 1,
	})
	if err != nil {
		return err
	}
	for _, z := range results {
		// Let's try to safeguard history against erroneous time skew
		if time.Since(time.UnixMilli(int64(z.Score))) > 90*Day {
			return errors.New("last event time > 90 Days ago")
		}
	}
	n, err := mht.redis.ZRemRangeByScore(ctx, commanderModelManagerHistoryKey, crredis.NegInfinity, fmt.Sprint(before.UnixMilli()))
	if err != nil {
		return err
	}
	mht.logger.Infof("%d events pruned", n)
	return nil
}

func (mht *ModelHistoryTracker) SubmitEvent(event ModelEvent) error {
	if !mht.running.Load() {
		return errors.New("model history tracker no longer accepting events")
	}
	tm := time.Now()
	event.Time = tm
	if err := event.Valid(); err != nil {
		return fmt.Errorf("invalid event: %w", err)
	}
	select {
	case mht.eventCh <- event:
	default:
		mht.logger.Error("model history channel full!, dropping event:", event)
	}
	return nil
}

type ModelHistoryQuery struct {
	StartTimestamp   int64
	Count            int64
	MatchFilter      ModelEvent
	EventTypeMatcher ModelEventTypes
	Reverse          bool
}

func (mhq ModelHistoryQuery) String() string {
	b, err := json.Marshal(mhq)
	if err != nil {
		return err.Error()
	}
	return string(b)
}

func (mht *ModelHistoryTracker) GetModelHistory(ctx context.Context, query ModelHistoryQuery) ([]ModelEvent, error) {
	mht.logger.Debug("GetModelHistory invoked query:", query)
	redisFn := mht.redis.ZRangeByScoreWithScores
	min := crredis.NegInfinity
	max := crredis.Infinity
	if query.Reverse {
		redisFn = mht.redis.ZRevRangeByScoreWithScores
		if query.StartTimestamp > 0 {
			max = fmt.Sprint(query.StartTimestamp)
		}
	} else {
		if query.StartTimestamp > 0 {
			min = fmt.Sprint(query.StartTimestamp)
		}
	}
	if query.Count == 0 {
		query.Count = 100
	}

	results := make([]ModelEvent, 0)

	offset := int64(0)
	count := int64(100)
	for int64(len(results)) < query.Count {
		members, err := redisFn(ctx, commanderModelManagerHistoryKey, &redis.ZRangeBy{
			Min:    min,
			Max:    max,
			Count:  count,
			Offset: offset,
		})
		if err != nil {
			return nil, err
		}
		if len(members) < 1 {
			break
		}
		offset += count
		resultEvents := mht.parseResults(members, query)
		if len(resultEvents) < 1 {
			continue
		}
		results = append(results, resultEvents...)
		if int64(len(results)) >= query.Count {
			return results[:query.Count], nil
		}
	}
	return results, nil
}

func (mht *ModelHistoryTracker) parseResults(results []redis.Z, query ModelHistoryQuery) []ModelEvent {
	records := make([]ModelEvent, 0)
	for _, z := range results {
		event, err := ModelEventFromZ(z)
		if err != nil {
			mht.logger.WithError(err).Error("skipping event")
			continue
		}
		if len(query.EventTypeMatcher) > 0 {
			if !query.EventTypeMatcher.Contains(event.Type) {
				continue
			}
		}
		if !query.MatchFilter.Filtered(event) {
			records = append(records, event)
		}
	}
	return records
}

func ModelEventFromZ(z redis.Z) (ModelEvent, error) {
	str, ok := z.Member.(string)
	if !ok {
		return ModelEvent{}, errors.New("invalid member")
	}
	event := ModelEvent{}
	if err := json.Unmarshal([]byte(str), &event); err != nil {
		return ModelEvent{}, fmt.Errorf("failed to unmarshal event: %w", err)
	}
	return event, nil
}

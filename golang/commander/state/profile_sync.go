package state

import (
	"context"
	"fmt"
	"slices"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_syncer_client"
	"github.com/golang/protobuf/jsonpb"
	"github.com/sirupsen/logrus"
)

type ProfileSyncManager struct {
	EventTrigger

	redis        *redis.Client
	portalClient *portal_clients.Client
	rosyClient   *robot_syncer_client.Client
	robotName    string

	profiles map[frontend.ProfileType]Profile
	lock     sync.Mutex
}

var (
	profilesMigratedToRoSy = []frontend.ProfileType{
		frontend.ProfileType_CATEGORY_COLLECTION,
		frontend.ProfileType_CATEGORY,
	}
)

func NewProfileSyncManager(redis *redis.Client, portalClient *portal_clients.Client, rosyClient *robot_syncer_client.Client, robotName string, profiles map[frontend.ProfileType]Profile) *ProfileSyncManager {
	p := &ProfileSyncManager{
		redis:        redis,
		portalClient: portalClient,
		rosyClient:   rosyClient,
		robotName:    robotName,
		profiles:     profiles,
	}
	for _, profile := range p.profiles {
		profile.AddUpdateLocalToSyncCallback(p.ProfileUpdated)
		profile.AddDeleteLocalToSyncCallback(p.ProfileDeleted)
	}

	p.triggerChannel = make(chan bool)
	return p
}

func (p *ProfileSyncManager) updateProfile(uuid string, profileType frontend.ProfileType, protected, deleted bool) error {
	data := &frontend.ProfileSyncData{
		ProfileType:     profileType,
		LastUpdatedTsMs: time.Now().UnixMilli(),
		Deleted:         deleted,
		Protected:       protected,
	}
	m := jsonpb.Marshaler{}
	str, err := m.MarshalToString(data)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	err = p.redis.HSetWithContext(ctx, redis.ProfileSyncItems, uuid, str)
	if err != nil {
		return err
	}

	return nil
}

func (p *ProfileSyncManager) ProfileUpdated(uuid string, profileType frontend.ProfileType, protected bool) error {
	return p.updateProfile(uuid, profileType, protected, false)
}

func (p *ProfileSyncManager) ProfileDeleted(uuid string, profileType frontend.ProfileType, protected bool) error {
	return p.updateProfile(uuid, profileType, protected, true)
}

func (p *ProfileSyncManager) readProfileSyncData() (map[string]*frontend.ProfileSyncData, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	// if key does not exist, return empty map
	exists, err := p.redis.ExistsWithCtx(ctx, redis.ProfileSyncItems)
	if err != nil {
		return nil, err
	}
	if !exists {
		logrus.Warn("ProfileSync: profile sync redis key does not exist")
		return make(map[string]*frontend.ProfileSyncData), nil
	}

	profiles := make(map[string]*frontend.ProfileSyncData)
	jsons, err := p.redis.HGetAllWithContext(ctx, redis.ProfileSyncItems)
	if err != nil {
		return nil, err
	}

	for uuid, j := range jsons {
		d := &frontend.ProfileSyncData{}
		jsonpb.UnmarshalString(j, d)
		profiles[uuid] = d
	}

	return profiles, nil
}

func (p *ProfileSyncManager) Action() {
	p.lock.Lock()
	defer p.lock.Unlock()

	// fetch local, Portal, and RoSy profiles
	localProfiles, err := p.readProfileSyncData()
	if err != nil {
		logrus.WithError(err).Warn("ProfileSync: could not read local profiles")
		return
	}
	logrus.Debugf("ProfileSync: Got %v local profiles ", len(localProfiles))

	portalProfiles, err := p.portalClient.GetProfilesData(p.robotName)
	if err != nil {
		logrus.WithError(err).Warn("ProfileSync: could not get profiles from portal")
		return
	}
	if portalProfiles == nil {
		portalProfiles = make(map[string]*frontend.ProfileSyncData)
	}
	logrus.Debugf("ProfileSync: Got %v profiles from Portal", len(portalProfiles))
	portalProfilesCount := len(portalProfiles)

	rosyProfiles, err := p.rosyClient.GetProfilesData(p.robotName)
	if err != nil {
		logrus.WithError(err).Warn("ProfileSync: could not get profiles from RoSy")
		return
	}
	if rosyProfiles == nil {
		rosyProfiles = make(map[string]*frontend.ProfileSyncData)
	}
	logrus.Debugf("ProfileSync: Got %v profiles from RoSy", len(rosyProfiles))
	rosyProfilesCount := len(rosyProfiles)

	remoteProfiles := portalProfiles
	for k, v := range rosyProfiles {
		remoteProfiles[k] = v
	}

	protectedProfileCount := 0
	if len(rosyProfiles) == 0 {
		logrus.Warn("ProfileSync: No Profiles in RoSy response -- skipping protected profile sync")
	}

	// sync Portal and RoSy profiles
	toPurge := make(map[string]*frontend.ProfileSyncData, 0)
	for uuid, local := range localProfiles {
		if local.Protected {
			protectedProfileCount++
		}

		// shared operations for customer and protected profiles
		if remote, ok := remoteProfiles[uuid]; ok {
			if remote.Deleted && local.Deleted {
				logrus.Debugf("ProfileSync: profile %v deleted both locally and remotely", uuid)
				toPurge[uuid] = remoteProfiles[uuid]
				continue
			}
			if remote.Deleted && !local.Deleted {
				logrus.Infof("ProfileSync: profile %v deleted remotely, will delete locally", uuid)
				if p.deleteLocal(uuid, local) == nil {
					toPurge[uuid] = remoteProfiles[uuid]
				}
				continue
			}
			if local.LastUpdatedTsMs == remote.LastUpdatedTsMs {
				logrus.Debugf("ProfileSync: profile %v didn't change", uuid)
				continue
			}
			if local.LastUpdatedTsMs < remote.LastUpdatedTsMs {
				logrus.Infof("ProfileSync: profile %v changed remotely", uuid)
				p.getRemote(uuid, remote)
				continue
			}
		}

		if local.Protected {
			// Only RoSy profiles can be protected
			if len(rosyProfiles) == 0 {
				// no Profiles in RoSy response -- skip protected profile sync
				continue
			}

			if remote, ok := remoteProfiles[uuid]; ok {
				if local.Deleted && !remote.Deleted {
					logrus.Infof("ProfileSync: protected profile %v deleted locally, re-fetching remote", uuid)
					p.getRemote(uuid, remote)
					continue
				}
				if local.LastUpdatedTsMs > remote.LastUpdatedTsMs {
					logrus.Infof("ProfileSync: protected profile %v changed locally, re-fetching remote", uuid)
					p.getRemote(uuid, remote)
					continue
				}
			} else {
				// never purge protected profiles
				if !local.Deleted {
					// remote not found, local exists and has not been deleted
					if err := p.deleteLocal(uuid, local); err != nil {
						logrus.WithError(err).Warnf("ProfileSync: could not delete protected profile %v locally", uuid)
					}
				}
			}
		} else {
			// RoSy customer profiles and all Portal profiles
			if remote, ok := remoteProfiles[uuid]; ok {
				if local.Deleted && !remote.Deleted {
					logrus.Infof("ProfileSync: customer profile %v deleted locally, will delete remotely", uuid)
					if p.deleteRemote(uuid, local) == nil {
						toPurge[uuid] = remoteProfiles[uuid]
					}
					continue
				}
				if local.LastUpdatedTsMs > remote.LastUpdatedTsMs {
					logrus.Infof("ProfileSync: customer profile %v changed locally", uuid)
					p.uploadLocal(uuid, local)
					continue
				}
			} else {
				// remote not found, local exists
				if local.Deleted {
					// profile deleted before first sync succeeded
					logrus.Infof("ProfileSync: customer profile %v deleted locally and doesn't appear on remote", uuid)
					toPurge[uuid] = localProfiles[uuid]
					continue
				}
				err := p.uploadLocal(uuid, local)
				if err == nil {
					// ignore error case as it likely indicates the skipping of a protected profile
					logrus.Infof("ProfileSync: customer profile %v added locally", uuid)
				}
			}
		}
	}

	// fetch or purge remote profiles not present locally
	for uuid, remote := range remoteProfiles {
		if _, ok := localProfiles[uuid]; !ok {
			// remote exists, local not found
			if remote.Protected {
				protectedProfileCount++
				if remote.Deleted {
					// never purge protected profiles
					continue
				}
			} else {
				if remote.Deleted {
					// profile deleted before first sync succeeded
					toPurge[uuid] = remoteProfiles[uuid]
					continue
				}
			}

			logrus.Infof("ProfileSync: profile %v added remotely", uuid)
			p.getRemote(uuid, remote)
		}
	}

	// purge necessary remote profiles
	for uuid, profile := range toPurge {
		p.purge(uuid, profile)
	}

	logrus.Infof("ProfileSync: Evaluated %v Portal profiles and %v RoSy profiles (including %d protected) against %v local profiles", portalProfilesCount, rosyProfilesCount, protectedProfileCount, len(localProfiles))
}

func (p *ProfileSyncManager) uploadLocal(uuid string, profile *frontend.ProfileSyncData) error {
	if p.profileMigratedToRoSy(profile) {
		if profile.Protected {
			// local changes to protected profiles are ignored
			return nil
		}

		req := &robot_syncer.UploadProfileRequest{
			LastUpdateTimeMs: profile.LastUpdatedTsMs,
			RobotSerial:      p.robotName,
		}
		var err error = nil
		profileObj, ok := p.profiles[profile.ProfileType]
		if ok {
			err = profileObj.LoadProfileForRoSySync(uuid, req)
		} else {
			err = fmt.Errorf("%v is not currently supported by profile sync.", profile.ProfileType)
		}
		if err != nil {
			logrus.Errorf("ProfileSync: %v", err)
			return err
		}

		if err = p.rosyClient.UploadProfile(req); err != nil {
			logrus.WithError(err).Error("ProfileSync: failed to upload profile to robot_syncer")
			return err
		}
	} else {
		req := &portal.UploadProfileRequest{
			LastUpdateTimeMs: profile.LastUpdatedTsMs,
			RobotName:        p.robotName,
		}
		var err error = nil
		profileObj, ok := p.profiles[profile.ProfileType]
		if ok {
			err = profileObj.LoadProfileForSync(uuid, req)
		} else {
			err = fmt.Errorf("%v is not currently supported by profile sync.", profile.ProfileType)
		}
		if err != nil {
			logrus.Errorf("ProfileSync: %v", err)
			return err
		}

		if err = p.portalClient.UploadProfile(req); err != nil {
			logrus.WithError(err).Error("ProfileSync: failed to upload profile to portal")
			return err
		}
	}

	return nil
}

func (p *ProfileSyncManager) getRemote(uuid string, sync *frontend.ProfileSyncData) error {
	handled := false
	var err error
	if p.profileMigratedToRoSy(sync) {
		resp, err := p.rosyClient.GetProfile(uuid)
		if err != nil {
			logrus.WithError(err).Warnf("ProfileSync: failed to get profile %v from robot syncer", uuid)
			return err
		}
		for _, profile := range p.profiles {
			handled, err = profile.SaveProfileFromRoSySync(resp)
			if handled {
				break
			}
		}
	} else {
		resp, err := p.portalClient.GetProfile(uuid)
		if err != nil {
			logrus.WithError(err).Warnf("ProfileSync: failed to get profile %v from portal", uuid)
			return err
		}
		for _, profile := range p.profiles {
			handled, err = profile.SaveProfileFromSync(resp)
			if handled {
				break
			}
		}
	}

	if !handled {
		err = fmt.Errorf("Unknonwn profile type, no way to save profile %v", uuid)
	}
	if err != nil {
		logrus.Errorf("ProfileSync: %v", err)
		return err
	}

	return p.saveSyncData(uuid, sync)
}

func (p *ProfileSyncManager) saveSyncData(uuid string, sync *frontend.ProfileSyncData) error {
	m := jsonpb.Marshaler{}
	s, err := m.MarshalToString(sync)
	if err != nil {
		logrus.WithError(err).Warnf("ProfileSync: could not marshal profile sync data for %v", uuid)
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	err = p.redis.HSetWithContext(ctx, redis.ProfileSyncItems, uuid, s)
	if err != nil {
		logrus.WithError(err).Warnf("ProfileSync: could not save profile sync data for %v", uuid)
		return err
	}
	return nil
}

func (p *ProfileSyncManager) deleteLocal(uuid string, sync *frontend.ProfileSyncData) error {
	var err error = nil
	profileObj, ok := p.profiles[sync.ProfileType]
	if ok {
		err = profileObj.DeleteProfileFromSync(uuid)
	} else {
		err = fmt.Errorf("%v is not currently supported by profile sync.", sync.ProfileType)
	}
	if err != nil {
		logrus.Errorf("ProfileSync: %v", err)
		return err
	}

	sync.Deleted = true
	return p.saveSyncData(uuid, sync)
}

func (p *ProfileSyncManager) deleteRemote(uuid string, profile *frontend.ProfileSyncData) error {
	var err error
	if p.profileMigratedToRoSy(profile) {
		err = p.rosyClient.DeleteProfile(uuid)
	} else {
		err = p.portalClient.DeleteProfile(uuid)
	}
	if err != nil {
		logrus.WithError(err).Warnf("ProfileSync: could not delete remote profile %v from portal", uuid)
	}
	return err
}

func (p *ProfileSyncManager) purge(uuid string, profile *frontend.ProfileSyncData) error {
	var err error
	if profile == nil {
		// nothing to do
		return nil
	}

	if !p.profileMigratedToRoSy(profile) {
		// never purge RoSy profiles as they are shared across all robots owned by a customer
		if err = p.portalClient.PurgeProfile(uuid); err != nil {
			logrus.WithError(err).Warnf("ProfileSync: could not purge profile %v from portal", uuid)
			return err
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	return p.redis.HDelWithContext(ctx, redis.ProfileSyncItems, uuid)
}

func (p *ProfileSyncManager) DeleteProfileSyncData() error {
	logrus.Info("ProfileSync: deleting profile sync data")
	p.lock.Lock()
	defer p.lock.Unlock()
	return p.redis.Del(redis.ProfileSyncItems)
}

func (p *ProfileSyncManager) profileMigratedToRoSy(profile *frontend.ProfileSyncData) bool {
	return slices.Contains(profilesMigratedToRoSy, profile.ProfileType)
}

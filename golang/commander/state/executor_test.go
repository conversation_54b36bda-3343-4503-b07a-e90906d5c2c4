package state

import (
	"context"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state/mocks"
)

func TestIntervalLoopExecutor(t *testing.T) {
	saveIntervalDefault := intervalDefault
	defer func() { intervalDefault = saveIntervalDefault }()

	intervalDefault = 50 * time.Millisecond
	tests := []struct {
		name                string
		runTimeMillis       int
		triggers            int
		expectedActionCalls int
	}{
		{"no interval", 0, 0, 1},
		{"one interval", 75, 0, 2},
		{"2 invocations no triggers", 125, 0, 3},
		{"no invocations 3 triggers", 45, 3, 4},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctx, cancel := context.WithCancel(context.TODO())
			actionCh := make(chan bool, 10)
			mockAction := new(mocks.Actionable)
			mockAction.On("Action").Return()
			mockAction.On("GetTriggeredEventChannel").Return(actionCh)
			loopEx := NewIntervalLoopExecutor(ctx, mockAction)
			time.AfterFunc(time.Duration(test.runTimeMillis)*time.Millisecond, cancel)
			trigger := EventTrigger{actionCh}
			for i := 0; i < test.triggers; i++ {
				go func() { trigger.Trigger() }()
			}
			loopEx.Run()

			mockAction.AssertNumberOfCalls(t, "Action", test.expectedActionCalls)
		})
	}
}

package state

import (
	"context"
	"errors"
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

type CategoryCfgState struct {
	/**
	 * N.B. CategoryCfgState does not use cfgState ActiveId. There is no
	 * such thing as an active Category Id. There are 0 or more categories in
	 * use by each robot. The Category Ids in use can be determined by inspecting
	 * the config of the active Category Collection.
	 **/
	cfgState
	categories []*category.Category

	// used by ChipMan to determine if it needs to download and sync new chips
	unsyncedUpdates bool
}

func NewCategoryCfgState(redisClient *redis.Client, rows map[int]*rows.RowClients) *CategoryCfgState {
	state := &CategoryCfgState{
		cfgState: cfgState{
			ManagedStateImpl: ManagedStateImpl{name: "CategoryCfgState"}, redisClient: redisClient, rows: rows, profileType: frontend.ProfileType_CATEGORY,
		},
		unsyncedUpdates: false,
	}

	state.initialize()
	return state
}

func (s *CategoryCfgState) GetProfileUpdatesFlag() (unsyncedUpdates bool) {
	s.ReadOnCurrent(func() {
		unsyncedUpdates = s.unsyncedUpdates
	})
	return
}

func (s *CategoryCfgState) ResetProfileUpdatesFlag() {
	s.WriteOnCurrent(func() {
		s.unsyncedUpdates = false
	})
}

func (s *CategoryCfgState) GetAllCfgs(ctx context.Context) (map[string]*category.Category, error) {
	cfgs := make(map[string]*category.Category)
	categories := make([]*category.Category, 0)
	data, err := s.redisClient.HGetAllWithContext(ctx, redis.CategoryCfgs)
	if err != nil {
		return cfgs, err
	}

	for id, cfg := range data {
		var category category.Category
		err := proto.Unmarshal([]byte(cfg), &category)
		if err != nil {
			logrus.Warnf("Failed to unmarshal category collection %v: %v", id, err)
			continue
		}

		categories = append(categories, &category)
		cfgs[id] = &category
	}

	var localCategories []*category.Category
	s.ReadOnCurrent(func() {
		localCategories = s.categories
	})

	if !categoriesEqual(categories, localCategories) {
		s.WriteOnCurrent(func() {
			s.categories = categories
		})
	}

	return cfgs, nil
}

func (s *CategoryCfgState) GetNextCategoryData(ctx context.Context, timestampMs int64) (result bool, category []*category.Category, ts int64) {
	result = s.ReadOnNext(ctx, timestampMs, func() {
		category = s.categories
	})

	ts = s.GetTimestampMs()
	return
}

func (s *CategoryCfgState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_Category:
		return true, s.saveCategoryConfig(r.Category)
	default:
		return false, nil
	}
}

func (s *CategoryCfgState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	return false, errors.New("SaveProfileFromSync Not implemented by Category Profile -- Use RoSySync")
}

func (s *CategoryCfgState) saveCategoryConfig(cfg *category.Category) error {
	data, err := proto.Marshal(cfg)
	if err != nil {
		logrus.Warnf("Failed to marshal protobuf data err: %v", err)
		return err
	}

	// Before saving to redis, check if the category exists already, is used by
	// the active collection, or if it was changed in any way. If the category
	// is new or was changed, we need to trigger Chip Manager to download any
	// new chips. If the category is active, we need to notify the CVRuntimeClients
	// to reload the category AFTER all chips are downloaded and synced.
	unsyncedUpdates := false
	existingCategory, err := s.redisClient.LoadCategoryConfig(cfg.Id)
	if err != nil {
		unsyncedUpdates = true
	} else {
		if !categoryEqual(cfg, existingCategory) {
			unsyncedUpdates = true
		}
	}

	err = s.redisClient.HSet(redis.CategoryCfgs, cfg.Id, string(data))
	if err != nil {
		logrus.Warnf("Failed to save cfg to redis %v", err)
		return err
	}

	if unsyncedUpdates {
		s.WriteOnCurrent(func() {
			s.unsyncedUpdates = true
		})
	}

	return nil
}

func categoriesEqual(a, b []*category.Category) bool {
	if len(a) != len(b) {
		return false
	}

	for _, category1 := range a {
		foundMatch := false
		for _, category2 := range b {
			if category1.Id == category2.Id {
				if categoryEqual(category1, category2) {
					foundMatch = true
				}
			}
		}

		if !foundMatch {
			return false
		}
	}

	return true
}

func categoryEqual(a, b *category.Category) bool {
	if a.Id == b.Id &&
		a.Name == b.Name &&
		a.Protected == b.Protected &&
		a.CustomerId == b.CustomerId &&
		len(a.ChipIds) == len(b.ChipIds) {
		for _, chip1 := range a.ChipIds {
			foundMatchingChip := false
			for _, chip2 := range b.ChipIds {
				if chip1 == chip2 {
					foundMatchingChip = true
					break
				}
			}

			if !foundMatchingChip {
				return false
			}
		}
	} else {
		return false
	}

	return true
}

func (s *CategoryCfgState) DeleteProfileFromSync(id string) error {
	return s.deleteCategoryConfig(id)
}

func (s *CategoryCfgState) deleteCategoryConfig(id string) error {
	err := s.redisClient.HDel(redis.CategoryCfgs, id)
	if err != nil {
		logrus.Warnf("Failed to delete category cfg %v, err: %v", id, err)
		return err
	}

	// skip write to unsyncedUpdates as we are not deleting active
	s.Notify()
	return nil
}

func (s *CategoryCfgState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	category, err := s.redisClient.LoadCategoryConfig(id)
	if err != nil {
		return err
	}

	if category.Protected {
		return fmt.Errorf("Category %s is protected and cannot be uploaded", id)
	}

	req.Profile = &robot_syncer.UploadProfileRequest_Category{
		Category: category,
	}

	return nil
}

func (s *CategoryCfgState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	return fmt.Errorf("LoadProfileForSync Not implemented by Category Profile -- Use RoSyLoad")
}

func (s *CategoryCfgState) SetActiveProfileFromSync(id string) error {
	return fmt.Errorf("SetActiveProfileFromSync not implemented for CategoryCfgState -- there is no concept of active Category")
}

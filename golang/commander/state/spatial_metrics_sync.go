package state

import (
	"context"
	"errors"
	"math/rand"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

const (
	spatialBlocksKey = "/spatial/blocks"
	spatialQueueKey  = "/spatial/sync_queue"

	maxUploadBatchSize = 500
)

type SpatialMetricsSyncManager struct {
	EventTrigger

	redis        *redis.Client
	portalClient *portal_clients.Client
	robotName    string
}

func NewSpatialMetricsSyncManager(redis *redis.Client, portalClient *portal_clients.Client, robotName string) *SpatialMetricsSyncManager {
	m := &SpatialMetricsSyncManager{
		redis:        redis,
		portalClient: portalClient,
		robotName:    robotName,
	}

	m.triggerChannel = make(chan bool)
	return m
}

func (m *SpatialMetricsSyncManager) Action() {
	q, err := m.redis.SMembers(spatialQueueKey)
	if err != nil {
		logrus.WithError(err).Error("SpatialMetricsSync: couldn't read queue")
		return
	}

	if len(q) == 0 {
		return
	}

	logrus.Infof("SpatialMetricsSync: uploading %v blocks", len(q))

	blocks := make([]*metrics.SpatialMetricBlock, 0)
	blockIds := make([]string, 0)
	for _, id := range q {
		blockProto, err := m.redis.HGet(spatialBlocksKey, id)
		if err != nil || blockProto == "" {
			logrus.WithError(nil).Errorf("SpatialMetricsSync: couldn't get block %v", id)
			continue
		}
		block := &metrics.SpatialMetricBlock{}
		err = proto.Unmarshal([]byte(blockProto), block)
		if err != nil {
			logrus.WithError(err).Errorf("SpatialMetricsSync: could not unmarshal block %v", id)
			continue
		}
		blocks = append(blocks, block)
		blockIds = append(blockIds, id)
	}

	for len(blocks) > 0 {
		batchSize := min(len(blocks), maxUploadBatchSize)
		err = m.portalClient.SyncBlocks(blocks[:batchSize], m.robotName)
		if err != nil {
			logrus.WithError(err).Error("SpatialMetricsSync: received error from portal")
			return
		}
		blocks = blocks[batchSize:]

		if err = m.removeIds(blockIds[:batchSize]); err != nil {
			logrus.WithError(err).Warn("SpatialMetricsSync: couldn't remove ids from redis")
		}
		blockIds = blockIds[batchSize:]
	}
}

func (m *SpatialMetricsSyncManager) removeIds(ids []string) error {
	delCtx, delCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer delCancel()
	delErr := m.redis.HDelWithContext(delCtx, spatialBlocksKey, ids...)
	remCtx, remCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer remCancel()
	remErr := m.redis.SRemWithContext(remCtx, spatialQueueKey, ids...)
	return errors.Join(delErr, remErr)
}

func (m *SpatialMetricsSyncManager) AddMockBlock() error {
	block := &metrics.SpatialMetricBlock{
		Start: &metrics.SpatialPosition{
			Latitude:    rand.Float64(),
			Longitude:   rand.Float64(),
			HeightMm:    rand.Float64(),
			TimestampMs: uint64(time.Now().UnixMilli()),
			EcefX:       rand.Float64(),
			EcefY:       rand.Float64(),
			EcefZ:       rand.Float64(),
		},
		End: &metrics.SpatialPosition{
			Latitude:    rand.Float64(),
			Longitude:   rand.Float64(),
			HeightMm:    rand.Float64(),
			TimestampMs: uint64(time.Now().UnixMilli()),
			EcefX:       rand.Float64(),
			EcefY:       rand.Float64(),
			EcefZ:       rand.Float64(),
		},
		WeedCount: &metrics.WeedCounterChunk{
			ConclusionCounts: &metrics.CountsByConclusionType{
				DisarmedWeed: []uint32{rand.Uint32(), rand.Uint32()},
				ArmedWeed:    []uint32{rand.Uint32(), rand.Uint32()},
				DisarmedCrop: []uint32{rand.Uint32(), rand.Uint32()},
				ArmedCrop:    []uint32{rand.Uint32(), rand.Uint32()},
			},
			WeedSizeData: &metrics.TargetSizeData{
				CumulativeSize: rand.Float64(),
				Count:          rand.Uint64(),
			},
			CropSizeData: &metrics.TargetSizeData{
				CumulativeSize: rand.Float64(),
				Count:          rand.Uint64(),
			},
			CountsByCategory: map[string]uint32{"PURSLANE": rand.Uint32()},
		},
	}
	id := uuid.New().String()
	data, err := proto.Marshal(block)
	if err != nil {
		return err
	}
	err = m.redis.HSet(spatialBlocksKey, id, string(data))
	if err != nil {
		return err
	}
	err = m.redis.SAdd(spatialQueueKey, id)
	return err
}

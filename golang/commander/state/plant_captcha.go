package state

import (
	"errors"
	"io/fs"
	"os"
	"strconv"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
)

const plantCaptchaUploadsKey = "/plant_captcha/uploads"
const plantCaptchaProgressKey = "/plant_captcha/upload_progress"

const PlantCaptchaErrorAlreadyExists = "1: Capture already exists"
const PlantCaptchaErrorAlreadyInProgress = "2: Capture already in progress"
const PlantCaptchaErrorMustBeAlphaNumeric = "3: Capture name must be alphanumeric"
const PlantCaptchaErrorInternal = "4: Internal error"
const PlantCaptchaNoModelinator = "5: No modelinator found for model %v crop %v"
const PlantCaptchaCantSaveModelinator = "6: Can't save modelinator for model %v crop %v"
const PlantCaptchaCantReadModelinator = "7: Can't read modelinator.json"
const PlantCaptchaNoAlmanac = "8: Almanac %v not found"
const PlantCaptchaCantSaveAlmanac = "9: Can't save almanac"
const PlantCaptchaCantReadAlmanac = "10: Can't read almanac.json"

const PlantCaptchaDir = "/data/plant_captcha/"

type PlantCaptchaUploadState struct {
	UploadState frontend.PlantCaptchaUploadState
	Percent     int32
}

type PlantCaptchaState struct {
	ManagedStateImpl

	Status        weed_tracking.PlantCaptchaStatus
	PlantCaptchas map[string]*frontend.PlantCaptcha
	ActiveCaptcha string
	TotalImages   int32
	ImagesTaken   int32
	MetadataTaken int32
	UploadStates  map[string]*PlantCaptchaUploadState

	redisClient *redis.Client
}

func NewPlantCaptchaState(redisClient *redis.Client) *PlantCaptchaState {
	s := &PlantCaptchaState{
		Status:        weed_tracking.PlantCaptchaStatus_NOT_STARTED,
		redisClient:   redisClient,
		PlantCaptchas: make(map[string]*frontend.PlantCaptcha),
		UploadStates:  make(map[string]*PlantCaptchaUploadState),
	}
	s.initialize()
	s.readPlantCaptchas()
	return s
}

func (s *PlantCaptchaState) readPlantCaptchas() error {
	entries, err := os.ReadDir(PlantCaptchaDir)
	if err != nil {
		logrus.Errorf("PlantCaptcha: could not read %v", PlantCaptchaDir)
		return err
	}

	for _, entry := range entries {
		if entry.IsDir() {
			pcName := entry.Name()
			pcJson, err := os.ReadFile(PlantCaptchaDir + pcName + "/plant_captcha.json")
			if err != nil {
				logrus.Errorf("PlantCaptcha: could not read %v%v/plant_captcha.json", PlantCaptchaDir, pcName)
				continue
			}
			p := &frontend.PlantCaptcha{}
			protojson.Unmarshal(pcJson, p)
			p.Name = pcName // in case download manager renames directory name, like it does
			if p.RowsUsed == nil {
				p.RowsUsed = []int32{1, 2, 3} // backwards compatibility
			}
			s.PlantCaptchas[pcName] = p
		}
	}

	return nil
}

func (s *PlantCaptchaState) StartPlantCaptcha(captcha *frontend.PlantCaptcha) error {
	var err error
	s.WriteOnCurrent(func() {
		if s.PlantCaptchas[captcha.Name] != nil {
			err = errors.New(PlantCaptchaErrorAlreadyExists)
			return
		}
		if s.Status == weed_tracking.PlantCaptchaStatus_CAPTCHA_STARTED {
			err = errors.New(PlantCaptchaErrorAlreadyInProgress)
			return
		}
		s.Status = weed_tracking.PlantCaptchaStatus_CAPTCHA_STARTED
		s.PlantCaptchas[captcha.Name] = captcha
		s.ActiveCaptcha = captcha.Name
		s.TotalImages = 0
		s.ImagesTaken = 0
	})
	if err != nil {
		return err
	}

	str, err := protojson.Marshal(captcha)
	if err != nil {
		logrus.WithError(err).Errorf("PlantCaptcha: Failed to marshal %v", captcha)
		return errors.New(PlantCaptchaErrorInternal)
	}
	err = os.WriteFile(PlantCaptchaDir+captcha.Name+"/plant_captcha.json", []byte(str), 0644)
	if err != nil {
		logrus.WithError(err).Errorf("PlantCaptcha: Failed to write plant_captcha.json")
		return errors.New(PlantCaptchaErrorInternal)
	}
	return nil
}

func (s *PlantCaptchaState) CaptchaFailed(captchaId string) {
	s.WriteOnCurrent(func() {
		if s.Status != weed_tracking.PlantCaptchaStatus_CAPTCHA_CANCELLED {
			s.Status = weed_tracking.PlantCaptchaStatus_CAPTCHA_FAILED
		}
		delete(s.PlantCaptchas, captchaId)
		s.ActiveCaptcha = ""
		s.TotalImages = 0
		s.ImagesTaken = 0
	})
	os.RemoveAll(PlantCaptchaDir + captchaId)
}

func (s *PlantCaptchaState) DeletePlantCaptcha(name string) error {
	var err error
	s.WriteOnCurrent(func() {
		if s.ActiveCaptcha == name {
			err = errors.New("Can't remove captcha that is in progress")
			return
		}
		delete(s.PlantCaptchas, name)
	})
	if err == nil {
		os.RemoveAll(PlantCaptchaDir + name)
	}
	return err
}

func (s *PlantCaptchaState) CancelPlantCaptcha() {
	// called under lock when plant captcha is running
	s.Status = weed_tracking.PlantCaptchaStatus_CAPTCHA_CANCELLED
	os.RemoveAll(PlantCaptchaDir + s.ActiveCaptcha)
	delete(s.PlantCaptchas, s.ActiveCaptcha)
	s.ActiveCaptcha = ""
}

func (s *PlantCaptchaState) DeleteUpload(name string) error {
	return s.redisClient.HDel(plantCaptchaUploadsKey, name)
}

func (s *PlantCaptchaState) SaveResult(captchaName string, result *frontend.PlantCaptchaItemResult) error {
	err := os.WriteFile(PlantCaptchaDir+captchaName+"/results/"+result.Id, []byte(result.UserPrediction.String()), 0644)
	if err != nil {
		logrus.WithError(err).Error("PlantCaptcha: cant save result")
	}
	return err
}

func (s *PlantCaptchaState) GetNumImagesProcessed(name string) int32 {
	files, err := os.ReadDir(PlantCaptchaDir + name + "/results/")
	if err != nil {
		return 0
	}
	return int32(len(files))
}

func (s *PlantCaptchaState) GetResults(name string) (map[string]string, error) {
	m := make(map[string]string)
	dirName := PlantCaptchaDir + name + "/results/"
	files, err := os.ReadDir(dirName)
	if err != nil {
		logrus.WithError(err).Error("PlantCaptcha: Could not read result files")
		return m, err
	}
	for _, f := range files {
		fileName := dirName + "/" + f.Name()
		content, err := os.ReadFile(fileName)
		if err != nil {
			logrus.WithError(err).Errorf("PlantCaptcha: Could not read %v", fileName)
		} else {
			m[f.Name()] = string(content)
		}
	}
	return m, nil
}

type PlantCaptchaWatcher struct {
	EventTrigger

	st *PlantCaptchaState
}

func NewPlantCaptchaWatcher(st *PlantCaptchaState) *PlantCaptchaWatcher {
	action := &PlantCaptchaWatcher{
		st: st,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *PlantCaptchaWatcher) Action() {
	entries, err := os.ReadDir(PlantCaptchaDir)
	if err != nil {
		if !errors.Is(err, fs.ErrNotExist) {
			logrus.WithError(err).Errorf("PlantCaptcha: could not read %v", PlantCaptchaDir)
		}
		return
	}
	onDisk := make(map[string]bool)
	for _, e := range entries {
		if e.IsDir() {
			onDisk[e.Name()] = true
		}
	}

	inMem := 0
	w.st.ReadOnCurrent(func() {
		inMem = len(w.st.PlantCaptchas)
	})
	changed := len(onDisk) != inMem
	if !changed {
		w.st.ReadOnCurrent(func() {
			for name := range onDisk {
				if _, ok := w.st.PlantCaptchas[name]; !ok {
					changed = true
					break
				}
			}
		})
	}

	if changed {
		w.st.WriteOnCurrent(func() {
			for k := range w.st.PlantCaptchas {
				delete(w.st.PlantCaptchas, k)
			}
			w.st.readPlantCaptchas()
		})
	}
}

type PlantCaptchaUploadsWatcher struct {
	EventTrigger

	st    *PlantCaptchaState
	redis *redis.Client
}

func NewPlantCaptchaUploadsWatcher(st *PlantCaptchaState, redis *redis.Client) *PlantCaptchaUploadsWatcher {
	action := &PlantCaptchaUploadsWatcher{st: st, redis: redis}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *PlantCaptchaUploadsWatcher) Action() {
	vals, err := w.redis.HGetAll("plant_captcha/uploads")
	if err != nil {
		logrus.Errorf("PlantCaptcha: could not get uploads state, err=%v", err)
		return
	}
	progress, err := w.redis.HGetAll("plant_captcha/upload_progress")
	if err != nil {
		logrus.Errorf("PlantCaptcha: could not get uploads progress, err=%v", err)
		return
	}
	newMap := make(map[string]*PlantCaptchaUploadState)
	for name, status := range vals {
		var us frontend.PlantCaptchaUploadState = frontend.PlantCaptchaUploadState_NONE
		if status == "DONE" {
			us = frontend.PlantCaptchaUploadState_DONE
		} else if status != "NONE" {
			us = frontend.PlantCaptchaUploadState_IN_PROGRESS
		}
		var percent int = 0
		if pr, ok := progress[name]; ok {
			percent, _ = strconv.Atoi(pr)
		}
		newMap[name] = &PlantCaptchaUploadState{UploadState: us, Percent: int32(percent)}
	}

	var oldMap map[string]*PlantCaptchaUploadState
	w.st.ReadOnCurrent(func() {
		oldMap = w.st.UploadStates
	})
	oldMap = w.st.UploadStates
	changed := false
	if len(oldMap) != len(newMap) {
		changed = true
	} else {
		for name, status := range newMap {
			if oldStatus, ok := oldMap[name]; !ok {
				changed = true
			} else {
				if status.UploadState != oldStatus.UploadState || status.Percent != oldStatus.Percent {
					changed = true
				}
			}
		}
	}
	if changed {
		w.st.WriteOnCurrent(func() {
			w.st.UploadStates = newMap
		})
	}
}

package state

import (
	"context"
	"fmt"
	"regexp"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/formatting"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	swig_metrics "github.com/carbonrobotics/robot/golang/swig/metrics"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/carbonrobotics/robot/golang/commander/model"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

const default_tz = "America/Los_Angeles"

var (
	lasersEnabledStateGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "lasers_enabled",
		Help:      "overall lasers enabled status",
	})
	weedingEnabledGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "weeding_enabled",
		Help:      "if weeding is enabled",
	}, []string{"row"})
	cropModelGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "crop_model",
		Help:      "crop model selected",
	}, []string{"model", "crop"})
	isWeedingGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "is_weeding",
		Help:      "boolean if the robot is weeding",
	})
	areaWeededTotalGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "area_weeded_total",
		Help:      "area weeded in acres",
	})
	areaWeededTodayGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "area_weeded_today",
		Help:      "area weeded today in acres",
	})
	timeWeededTotalGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "time_weeded_total_min",
		Help:      "total time weeeded in minutes",
	})
	timeWeededTodayGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "time_weeded_today_min",
		Help:      "time weeeded today in minutes",
	})
)

type StatusPriority int32

const (
	StatusPriorityUnknown StatusPriority = iota
	StatusPriorityLow
	StatusPriorityNormal
	StatusPriorityHigh
)

type ExtraDashboardStatus struct {
	Title       string
	GroupId     string
	IconName    string
	IconColor   string
	StatusText  string
	BottomText  string
	StatusColor string
	Priority    StatusPriority
	Progress    float64
}

type ConclusionMetrics struct {
	Valid                      bool
	NotWeeding                 uint32
	OutOfBand                  uint32
	IntersectsWithNonShootable uint32
	OutOfRange                 uint32
	Unimportant                uint32
	NotShot                    uint32
	PartiallyShot              uint32
	Shot                       uint32
	P2PNotFound                uint32
	Error                      uint32
	Flicker                    uint32
	MarkedForThinning          uint32
	NotTargeted                uint32
	P2PMissingContext          uint32
}

type WeedTargetingState struct {
	Enabled bool
}
type ThinningTargetingState struct {
	Enabled bool
}
type TargetingState struct {
	WeedState     WeedTargetingState
	ThinningState ThinningTargetingState
}
type ExpectedTargetingState struct {
	TargetingState
	IsEnabled map[int]bool
}

type EnforcedTargetingState struct {
	TargetingState
	OffsyncCycles uint32
}

func (s *TargetingState) Enabled() bool {
	return s.ThinningState.Enabled || s.WeedState.Enabled
}
func (s *EnforcedTargetingState) Enabled() bool {
	return s.TargetingState.Enabled()
}
func (s *TargetingState) IsEqual(rhs *TargetingState) bool {
	return s.WeedState.Enabled == rhs.WeedState.Enabled && s.ThinningState.Enabled == rhs.ThinningState.Enabled
}
func (s *EnforcedTargetingState) IsEqual(rhs *EnforcedTargetingState) bool {
	return s.TargetingState.IsEqual(&rhs.TargetingState)
}
func (s *ExpectedTargetingState) IsEqualWithRow(rhs *TargetingState, row int) bool {
	val, ok := s.IsEnabled[row]
	if ok {
		if val {
			return s.IsEqual(rhs)
		} else {
			return !rhs.Enabled()
		}
	} else {
		// if not in map assume row is enabled
		return s.IsEqual(rhs)
	}
}
func (s *ExpectedTargetingState) WeedingEnabled(row int) bool {
	val, ok := s.IsEnabled[row]
	if ok {
		if val {
			return s.TargetingState.WeedState.Enabled
		} else {
			return false
		}
	} else {
		// if not in map assume row is enabled
		return s.TargetingState.WeedState.Enabled
	}
}
func (s *ExpectedTargetingState) ThinningEnabled(row int) bool {
	val, ok := s.IsEnabled[row]
	if ok {
		if val {
			return s.TargetingState.ThinningState.Enabled
		} else {
			return false
		}
	} else {
		// if not in map assume row is enabled
		return s.TargetingState.ThinningState.Enabled
	}
}

type DashboardState struct {
	ImplementState         frontend.ImplementState
	EfficiencyEnabled      bool
	EfficiencyPercent      uint32
	ErrorRateEnabled       bool
	ErrorPercent           uint32
	ExtraConclusions       []*frontend.ExtraConclusion
	AreaWeededToday        float64
	AreaWeededTotal        float64
	TimeWeededTodayMinutes int64
	TimeWeededTotalMinutes int64
	WeedsKilledToday       int64
	WeedsKilledTotal       int64
	CropsKilledToday       int64
	CropsKilledTotal       int64
	WeedingEnabled         bool
	CruiseEnabled          bool
	CruiseAllowEnable      bool
}

type RowWeedingState struct {
	ActualTargeting       EnforcedTargetingState
	RowReady              bool
	RowSafetyState        int32
	ActuationTasksRunning bool
}

func (s *RowWeedingState) IsEqual(rhs *RowWeedingState) bool {
	return s.ActualTargeting.IsEqual(&rhs.ActualTargeting) && s.RowReady == rhs.RowReady && s.RowSafetyState == rhs.RowSafetyState && s.ActuationTasksRunning == rhs.ActuationTasksRunning
}

type OperationsState struct {
	ManagedStateImpl
	StartTime               time.Time
	Status                  frontend.Status
	Ready                   bool
	StatusMessage           string
	TranslatedStatusMessage *frontend.TranslatedStatusMessage
	StatusChangedAt         time.Time
	ExtraStatuses           map[string]*ExtraDashboardStatus
	SelectedModel           model.CropModel
	RowStatuses             map[int]ServerStatus
	CommandStatus           ServerStatus
	RowConclusionMetrics    map[int]*ConclusionMetrics
	ExpectedTargeting       ExpectedTargetingState
	Dashboard               DashboardState
	DebugMode               bool
	RowWeeding              map[int]RowWeedingState
}

func NewOperationsState(rowClients map[int]*rows.RowClients) *OperationsState {
	state := &OperationsState{
		ManagedStateImpl: ManagedStateImpl{name: "OperationsState"},
		RowWeeding:       make(map[int]RowWeedingState),
	}
	primaryRowClients := rowClients
	state.Status = frontend.Status_STATUS_UNKNOWN
	state.StatusMessage = ""
	state.StatusChangedAt = time.Now()
	state.ExtraStatuses = make(map[string]*ExtraDashboardStatus)
	state.StartTime = time.Now()
	state.RowConclusionMetrics = make(map[int]*ConclusionMetrics)
	state.ExpectedTargeting.ThinningState.Enabled = false
	state.ExpectedTargeting.WeedState.Enabled = false
	state.ExpectedTargeting.IsEnabled = make(map[int]bool)
	for i, _ := range primaryRowClients {
		state.ExpectedTargeting.IsEnabled[i] = true
		state.RowWeeding[i] = RowWeedingState{}
	}
	state.Dashboard = DashboardState{ExtraConclusions: make([]*frontend.ExtraConclusion, 0)}
	state.DebugMode = false
	state.initialize()
	return state
}

func (o *OperationsState) GetExpectedTargetingState() TargetingState {
	var ts TargetingState
	o.ReadOnCurrent(func() {
		ts = o.ExpectedTargeting.TargetingState
	})
	return ts
}
func (o *OperationsState) GetExpectedTargetingStateWithRows() ExpectedTargetingState {
	var ets ExpectedTargetingState
	o.ReadOnCurrent(func() {
		ets = o.ExpectedTargeting
	})
	return ets
}
func (o *OperationsState) ThinningEnabled(row int) bool {
	enabled := false
	o.ReadOnCurrent(func() {
		enabled = o.ExpectedTargeting.ThinningEnabled(row)
	})
	return enabled
}
func (o *OperationsState) SetCruiseControlHWState(ccState *cruiseControlHWState) {
	o.WriteOnCurrent(func() {
		o.Dashboard.CruiseAllowEnable = ccState.CruiseAllowEnable
		o.Dashboard.CruiseEnabled = ccState.CruiseEnabled
	})
}

type OperationsMetricWatcher struct {
	EventTrigger
	opState *OperationsState
	rows    map[int]*rows.RowClients
}

func NewOperationsMetricWatcher(opState *OperationsState, rowClients map[int]*rows.RowClients, commonNode *config.ConfigTree) *OperationsMetricWatcher {
	action := &OperationsMetricWatcher{
		opState: opState,
		rows:    rowClients,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (o *OperationsMetricWatcher) Action() {
	newRowConclusionMetrics := map[int]*ConclusionMetrics{}

	for key := range o.rows {
		newRowConclusionMetrics[key] = &ConclusionMetrics{
			Valid:                      false,
			NotWeeding:                 0,
			OutOfBand:                  0,
			IntersectsWithNonShootable: 0,
			OutOfRange:                 0,
			Unimportant:                0,
			NotShot:                    0,
			PartiallyShot:              0,
			Shot:                       0,
			P2PNotFound:                0,
			Error:                      0,
			Flicker:                    0,
			MarkedForThinning:          0,
			NotTargeted:                0,
			P2PMissingContext:          0,
		}
	}

	wg := sync.WaitGroup{}
	wg.Add(len(o.rows))

	for key, row := range o.rows {
		go func(k int, r *rows.RowClients) {
			defer wg.Done()
			conclusions, err := r.WeedTrackingClient.GetConclusionCounter()

			metric := newRowConclusionMetrics[k]

			if err == nil {
				metric.Valid = true

				for _, count := range conclusions.Counts {
					switch count.Type {
					case weed_tracking.ConclusionType_NOT_WEEDING:
						metric.NotWeeding = count.Count
					case weed_tracking.ConclusionType_OUT_OF_BAND:
						metric.OutOfBand = count.Count
					case weed_tracking.ConclusionType_INTERSECTS_WITH_NON_SHOOTABLE:
						metric.IntersectsWithNonShootable = count.Count
					case weed_tracking.ConclusionType_OUT_OF_RANGE:
						metric.OutOfRange = count.Count
					case weed_tracking.ConclusionType_UNIMPORTANT:
						metric.Unimportant = count.Count
					case weed_tracking.ConclusionType_NOT_SHOT:
						metric.NotShot = count.Count
					case weed_tracking.ConclusionType_PARTIALLY_SHOT:
						metric.PartiallyShot = count.Count
					case weed_tracking.ConclusionType_SHOT:
						metric.Shot = count.Count
					case weed_tracking.ConclusionType_P2P_NOT_FOUND:
						metric.P2PNotFound = count.Count
					case weed_tracking.ConclusionType_ERROR:
						metric.Error = count.Count
					case weed_tracking.ConclusionType_FLICKER:
						metric.Flicker = count.Count
					case weed_tracking.ConclusionType_MARKED_FOR_THINNING:
						metric.MarkedForThinning = count.Count
					case weed_tracking.ConclusionType_NOT_TARGETED:
						metric.NotTargeted = count.Count
					case weed_tracking.ConclusionType_P2P_MISSING_CONTEXT:
						metric.P2PMissingContext = count.Count
					}
				}
			}

		}(key, row)

	}

	wg.Wait()

	oldRowConclusionMetrics := map[int]*ConclusionMetrics{}

	o.opState.ReadOnCurrent(func() {
		for k, v := range o.opState.RowConclusionMetrics {
			oldRowConclusionMetrics[k] = &ConclusionMetrics{}
			*oldRowConclusionMetrics[k] = *v
		}
	})

	changed := false

	for k := range oldRowConclusionMetrics {
		if _, ok := newRowConclusionMetrics[k]; !ok {
			changed = true
		}
	}

	for k, v := range newRowConclusionMetrics {
		if v2, ok := oldRowConclusionMetrics[k]; ok {
			if *v != *v2 {
				changed = true
			}
		} else {
			changed = true
		}
	}

	if changed {
		o.opState.WriteOnCurrent(func() {
			o.opState.RowConclusionMetrics = newRowConclusionMetrics
		})
	}
}

type WeedingEnforcer struct {
	EventTrigger
	opState   *OperationsState
	implement *ImplementState
	rows      map[int]*rows.RowClients
}

func NewWeedingEnforcer(opState *OperationsState, implement *ImplementState, rowClients map[int]*rows.RowClients) *WeedingEnforcer {
	action := &WeedingEnforcer{
		opState:   opState,
		implement: implement,
		rows:      rowClients,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *WeedingEnforcer) Action() {
	var expectedState ExpectedTargetingState
	rowWeeding := make(map[int]RowWeedingState)
	isDebugModeOn := false
	isDebugModeSet := false
	w.opState.ReadOnCurrent(func() {
		for k, v := range w.opState.RowWeeding {
			rowWeeding[k] = v
		}
		expectedState = w.opState.ExpectedTargeting
		isDebugModeSet = w.opState.DebugMode
	})

	w.implement.ReadOnCurrent(func() {
		isDebugModeOn = w.implement.SafetyState.DebugMode
	})

	if isDebugModeOn && (expectedState.TargetingState.WeedState.Enabled || expectedState.TargetingState.ThinningState.Enabled) {
		expectedState.TargetingState.WeedState.Enabled = false
		expectedState.TargetingState.ThinningState.Enabled = false
		w.opState.WriteOnCurrent(func() {
			w.opState.ExpectedTargeting.WeedState.Enabled = false
			w.opState.ExpectedTargeting.ThinningState.Enabled = false
		})
	}

	if isDebugModeOn != isDebugModeSet {
		w.opState.WriteOnCurrent(func() {
			w.opState.DebugMode = isDebugModeOn
		})
	}

	// Take Action
	var wg sync.WaitGroup
	for i, r := range rowWeeding {
		if !expectedState.IsEqualWithRow(&r.ActualTargeting.TargetingState, i) {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()
				err := w.rows[index].AimbotClient.SetTargetingState(expectedState.WeedingEnabled(index), expectedState.ThinningEnabled(index))
				if err != nil {
					logrus.Infof("Error setting state in Row %d: %s", index, err)
				}
			}(i)
		}
	}
	wg.Wait()

	// Assess State
	var wg2 sync.WaitGroup
	var lock sync.Mutex // to avoid 'concurrent map writes'
	current := make(map[int]RowWeedingState)
	for i, r := range rowWeeding {
		current[i] = r //initialize map with same values
	}
	for i, _ := range rowWeeding {
		wg2.Add(1)
		go func(index int) {
			defer wg2.Done()
			aimbotState, err := w.rows[index].AimbotClient.GetAimbotState()

			if err != nil {
				// TODO should we raise an alarm for this?
				logrus.Errorf("Error Getting Aimbot State For Row %d: %s", index, err)
				return
			}

			lock.Lock()
			val, _ := current[index]
			val.ActualTargeting.TargetingState.WeedState.Enabled = aimbotState.GetTargetingState().GetWeedingEnabled()
			val.ActualTargeting.TargetingState.ThinningState.Enabled = aimbotState.GetTargetingState().GetThinningEnabled()
			val.RowReady = aimbotState.GetReady()
			val.ActuationTasksRunning = aimbotState.GetActuationTasksRunning()
			val.RowSafetyState = int32(aimbotState.GetSafetyOverrideState())
			current[index] = val
			lock.Unlock()
		}(i)
	}
	wg2.Wait()

	for i, currentRow := range current {
		// If Current State has changed
		prevState, _ := rowWeeding[i]
		if !currentRow.IsEqual(&prevState) {
			w.opState.WriteOnCurrent(func() {
				val, _ := w.opState.RowWeeding[i]
				val.ActualTargeting.TargetingState = currentRow.ActualTargeting.TargetingState
				val.RowReady = currentRow.RowReady
				val.RowSafetyState = currentRow.RowSafetyState
				val.ActuationTasksRunning = currentRow.ActuationTasksRunning
				w.opState.RowWeeding[i] = val
			})
		}
		// If State is In Sync
		if expectedState.IsEqualWithRow(&currentRow.ActualTargeting.TargetingState, i) {
			if rowWeeding[i].ActualTargeting.OffsyncCycles > 0 {
				w.opState.WriteOnCurrent(func() {
					val, _ := w.opState.RowWeeding[i]
					val.ActualTargeting.OffsyncCycles = 0
					w.opState.RowWeeding[i] = val
				})
			}
		} else {
			// TODO Take Actual Action For the State to be In Sync
			w.opState.WriteOnCurrent(func() {
				val, _ := w.opState.RowWeeding[i]
				val.ActualTargeting.OffsyncCycles += 1
				w.opState.RowWeeding[i] = val
			})
		}
	}

}

type ExtraStatusManager struct {
	EventTrigger
	opState                   *OperationsState
	implementState            *ImplementState
	weedingState              *WeedingState
	EfficiencyEnabled         *config.ConfigTree
	EfficiencyGoodThreshold   *config.ConfigTree
	EfficiencyMediumThreshold *config.ConfigTree
	ErrorRateEnabled          *config.ConfigTree
	ErrorRateGoodThreshold    *config.ConfigTree
	ErrorRateMediumThreshold  *config.ConfigTree
	ExtraConclusions          *config.ConfigTree
	ExistingExtraConclusions  map[string]bool
}

func NewExtraStatusManager(opState *OperationsState, implementState *ImplementState, weedingState *WeedingState, commanderConfigNode *config.ConfigTree) *ExtraStatusManager {

	action := &ExtraStatusManager{
		opState:                   opState,
		implementState:            implementState,
		weedingState:              weedingState,
		EfficiencyEnabled:         commanderConfigNode.GetNode("dashboard/metrics/efficiency/enabled"),
		EfficiencyGoodThreshold:   commanderConfigNode.GetNode("dashboard/metrics/efficiency/good_threshold_pct"),
		EfficiencyMediumThreshold: commanderConfigNode.GetNode("dashboard/metrics/efficiency/medium_threshold_pct"),
		ErrorRateEnabled:          commanderConfigNode.GetNode("dashboard/metrics/error_rate/enabled"),
		ErrorRateGoodThreshold:    commanderConfigNode.GetNode("dashboard/metrics/error_rate/good_threshold_pct"),
		ErrorRateMediumThreshold:  commanderConfigNode.GetNode("dashboard/metrics/error_rate/medium_threshold_pct"),
		ExtraConclusions:          commanderConfigNode.GetNode("dashboard/metrics/extra_conclusions"),
		ExistingExtraConclusions:  map[string]bool{},
	}
	action.triggerChannel = make(chan bool)
	return action
}

const (
	InternetStatus  string = "Internet"
	ImplementStatus string = "Implement"
	WeedingStatus   string = "Weeding"
)

func (e *ExtraStatusManager) ExtraAction(current *ExtraDashboardStatus) {
	var state ExtraDashboardStatus
	var exists bool = false

	e.opState.ReadOnCurrent(func() {
		if stat, ok := e.opState.ExtraStatuses[current.Title]; ok {
			state = *stat
			exists = true
		}
	})

	if !exists || current.StatusText != state.StatusText || current.IconName != state.IconName || current.StatusColor != state.StatusColor {
		e.opState.WriteOnCurrent(func() {
			e.opState.ExtraStatuses[current.Title] = current
		})
	}
}

func (e *ExtraStatusManager) ExtraActionRemove(title string) {
	var exists bool = false

	e.opState.ReadOnCurrent(func() {
		if _, ok := e.opState.ExtraStatuses[title]; ok {
			exists = true
		}
	})

	if exists {
		e.opState.WriteOnCurrent(func() {
			delete(e.opState.ExtraStatuses, title)
		})
	}
}

func (e *ExtraStatusManager) ImplementAction() {
	var lifted bool
	e.implementState.ReadOnCurrent(func() {
		lifted = e.implementState.SafetyState.Lifted
	})
	var current *ExtraDashboardStatus
	if lifted {
		current = &ExtraDashboardStatus{
			Title:       "Implement",
			IconName:    "ImplementRaised",
			IconColor:   "red",
			BottomText:  "Raised",
			StatusColor: "red",
			Priority:    StatusPriorityHigh,
		}
	} else {
		current = &ExtraDashboardStatus{
			Title:       "Implement",
			IconName:    "ImplementLowered",
			IconColor:   "green",
			BottomText:  "Lowered",
			StatusColor: "green",
			Priority:    StatusPriorityHigh,
		}
	}

	e.ExtraAction(current)
	e.opState.WriteOnCurrent(func() {
		if lifted {
			e.opState.Dashboard.ImplementState = frontend.ImplementState_RAISED
		} else {
			e.opState.Dashboard.ImplementState = frontend.ImplementState_LOWERED
		}
	})

}

func (e *ExtraStatusManager) ConclusionMetricsAction() {
	rowConclusionMetrics := map[int]*ConclusionMetrics{}

	e.opState.ReadOnCurrent(func() {
		for k, v := range e.opState.RowConclusionMetrics {
			rowConclusionMetrics[k] = &ConclusionMetrics{}
			*rowConclusionMetrics[k] = *v
		}
	})

	overallMetric := &ConclusionMetrics{
		Valid:                      false,
		NotWeeding:                 0,
		OutOfBand:                  0,
		IntersectsWithNonShootable: 0,
		OutOfRange:                 0,
		Unimportant:                0,
		NotShot:                    0,
		PartiallyShot:              0,
		Shot:                       0,
		P2PNotFound:                0,
		NotTargeted:                0,
		Error:                      0,
		P2PMissingContext:          0,
	}

	for _, row := range rowConclusionMetrics {
		if row.Valid {
			overallMetric.Valid = true
			overallMetric.NotWeeding += row.NotWeeding

			if row.NotWeeding == 0 {
				overallMetric.OutOfBand += row.OutOfBand
				overallMetric.IntersectsWithNonShootable += row.IntersectsWithNonShootable
				overallMetric.OutOfRange += row.OutOfRange
				overallMetric.Unimportant += row.Unimportant
				overallMetric.NotShot += row.NotShot
				overallMetric.PartiallyShot += row.PartiallyShot
				overallMetric.P2PNotFound += row.P2PNotFound
				overallMetric.Error += row.Error
				overallMetric.NotTargeted += row.NotTargeted
				overallMetric.P2PMissingContext += row.P2PMissingContext
			}
			overallMetric.Shot += row.Shot
		}
	}

	if e.EfficiencyEnabled.GetBoolValue() {
		var efficiencyPercent uint32 = 0
		efficiencyColor := "green"
		if overallMetric.Shot+overallMetric.NotShot != 0 {
			efficiencyPercent = (100 * overallMetric.Shot) / (overallMetric.Shot + overallMetric.NotShot + overallMetric.MarkedForThinning)
			if efficiencyPercent < uint32(e.EfficiencyMediumThreshold.GetUIntValue()) {
				efficiencyColor = "red"
			} else if efficiencyPercent < uint32(e.EfficiencyGoodThreshold.GetUIntValue()) {
				efficiencyColor = "orange"
			}
		}

		statusText := fmt.Sprintf("%d%%", efficiencyPercent)
		if efficiencyPercent >= 100 {
			statusText = "99+%"
		}

		efficiencyStatus := &ExtraDashboardStatus{
			Title:       "Efficiency",
			IconName:    "",
			IconColor:   "",
			StatusText:  statusText,
			StatusColor: efficiencyColor,
			Priority:    StatusPriorityHigh,
		}
		e.ExtraAction(efficiencyStatus)
		e.opState.WriteOnCurrent(func() {
			e.opState.Dashboard.EfficiencyEnabled = true
			e.opState.Dashboard.EfficiencyPercent = efficiencyPercent
		})
	} else {
		e.ExtraActionRemove("Efficiency")
		e.opState.WriteOnCurrent(func() {
			e.opState.Dashboard.EfficiencyEnabled = false
			e.opState.Dashboard.EfficiencyPercent = 0
		})
	}

	if e.ErrorRateEnabled.GetBoolValue() {
		errorCount := overallMetric.PartiallyShot + overallMetric.P2PNotFound + overallMetric.Error + overallMetric.OutOfRange + overallMetric.P2PMissingContext
		totalCount := errorCount + overallMetric.NotShot + overallMetric.Shot + overallMetric.OutOfBand + overallMetric.IntersectsWithNonShootable + overallMetric.Unimportant + overallMetric.MarkedForThinning + overallMetric.NotTargeted
		var errorPercent uint32 = 0
		if totalCount != 0 {
			errorPercent = (100 * errorCount) / totalCount
		}
		errorColor := "green"
		if errorPercent > uint32(e.ErrorRateMediumThreshold.GetUIntValue()) {
			errorColor = "red"
		} else if errorPercent > uint32(e.ErrorRateGoodThreshold.GetUIntValue()) {
			errorColor = "orange"
		}

		errorPercentStatus := &ExtraDashboardStatus{
			Title:       "Error Rate",
			IconName:    "",
			IconColor:   "",
			StatusText:  fmt.Sprintf("%d%%", errorPercent),
			StatusColor: errorColor,
			Priority:    StatusPriorityHigh,
		}

		e.ExtraAction(errorPercentStatus)
		e.opState.WriteOnCurrent(func() {
			e.opState.Dashboard.ErrorRateEnabled = true
			e.opState.Dashboard.ErrorPercent = errorPercent
		})
	} else {
		e.ExtraActionRemove("Error Rate")
		e.opState.WriteOnCurrent(func() {
			e.opState.Dashboard.ErrorRateEnabled = false
			e.opState.Dashboard.ErrorPercent = 0
		})
	}

	extraConclusionsChildren := e.ExtraConclusions.GetChildrenNodes()
	addedExtraConclusions := map[string]bool{}
	dashboardExtraConclusions := make([]*frontend.ExtraConclusion, 0)
	for _, extraConclusion := range extraConclusionsChildren {
		dashboardExtraConclusion := &frontend.ExtraConclusion{}
		defer config.PreDeleteConfigTree(extraConclusion)
		enabled := extraConclusion.GetChild("enabled")
		defer config.PreDeleteConfigTree(enabled)

		if !enabled.GetBoolValue() {
			continue
		}

		title := extraConclusion.GetChild("title")
		defer config.PreDeleteConfigTree(title)
		flipThresholds := extraConclusion.GetChild("flip_thresholds")
		defer config.PreDeleteConfigTree(flipThresholds)
		goodThreshold := extraConclusion.GetChild("good_threshold_pct")
		defer config.PreDeleteConfigTree(goodThreshold)
		mediumThreshold := extraConclusion.GetChild("medium_threshold_pct")
		defer config.PreDeleteConfigTree(mediumThreshold)

		var numerator uint32 = 0
		numNotWeeding := extraConclusion.GetNode("numerator/not_weeding")
		defer config.PreDeleteConfigTree(numNotWeeding)
		if numNotWeeding.GetBoolValue() {
			numerator += overallMetric.NotWeeding
		}
		numOutOfBand := extraConclusion.GetNode("numerator/out_of_band")
		defer config.PreDeleteConfigTree(numOutOfBand)
		if numOutOfBand.GetBoolValue() {
			numerator += overallMetric.OutOfBand
		}
		numIntersectsWithNonShootable := extraConclusion.GetNode("numerator/intersects_with_non_shootable")
		defer config.PreDeleteConfigTree(numIntersectsWithNonShootable)
		if numIntersectsWithNonShootable.GetBoolValue() {
			numerator += overallMetric.IntersectsWithNonShootable
		}
		numOutOfRange := extraConclusion.GetNode("numerator/out_of_range")
		defer config.PreDeleteConfigTree(numOutOfRange)
		if numOutOfRange.GetBoolValue() {
			numerator += overallMetric.OutOfRange
		}
		numUnimportant := extraConclusion.GetNode("numerator/unimportant")
		defer config.PreDeleteConfigTree(numUnimportant)
		if numUnimportant.GetBoolValue() {
			numerator += overallMetric.Unimportant
		}
		numNotShot := extraConclusion.GetNode("numerator/not_shot")
		defer config.PreDeleteConfigTree(numNotShot)
		if numNotShot.GetBoolValue() {
			numerator += overallMetric.NotShot
		}
		numPartiallyShot := extraConclusion.GetNode("numerator/partially_shot")
		defer config.PreDeleteConfigTree(numPartiallyShot)
		if numPartiallyShot.GetBoolValue() {
			numerator += overallMetric.PartiallyShot
		}
		numShot := extraConclusion.GetNode("numerator/shot")
		defer config.PreDeleteConfigTree(numShot)
		if numShot.GetBoolValue() {
			numerator += overallMetric.Shot
		}
		numP2PNotFound := extraConclusion.GetNode("numerator/p2p_not_found")
		defer config.PreDeleteConfigTree(numP2PNotFound)
		if numP2PNotFound.GetBoolValue() {
			numerator += overallMetric.P2PNotFound
		}
		numError := extraConclusion.GetNode("numerator/error")
		defer config.PreDeleteConfigTree(numError)
		if numError.GetBoolValue() {
			numerator += overallMetric.Error
		}
		numFlicker := extraConclusion.GetNode("numerator/flicker")
		defer config.PreDeleteConfigTree(numFlicker)
		if numFlicker.GetBoolValue() {
			numerator += overallMetric.Flicker
		}
		numMarkedForThinning := extraConclusion.GetNode("numerator/marked_for_thinning")
		defer config.PreDeleteConfigTree(numMarkedForThinning)
		if numMarkedForThinning.GetBoolValue() {
			numerator += overallMetric.MarkedForThinning
		}
		numNotTargeted := extraConclusion.GetNode("numerator/not_targeted")
		defer config.PreDeleteConfigTree(numNotTargeted)
		if numNotTargeted.GetBoolValue() {
			numerator += overallMetric.NotTargeted
		}
		numP2PMissingContext := extraConclusion.GetNode("numerator/p2p_missing_context")
		defer config.PreDeleteConfigTree(numP2PMissingContext)
		if numP2PMissingContext.GetBoolValue() {
			numerator += overallMetric.P2PMissingContext
		}

		var denominator uint32 = 0
		denNotWeeding := extraConclusion.GetNode("denominator/not_weeding")
		defer config.PreDeleteConfigTree(denNotWeeding)
		if denNotWeeding.GetBoolValue() {
			denominator += overallMetric.NotWeeding
		}
		denOutOfBand := extraConclusion.GetNode("denominator/out_of_band")
		defer config.PreDeleteConfigTree(denOutOfBand)
		if denOutOfBand.GetBoolValue() {
			denominator += overallMetric.OutOfBand
		}
		denIntersectsWithNonShootable := extraConclusion.GetNode("denominator/intersects_with_non_shootable")
		defer config.PreDeleteConfigTree(denIntersectsWithNonShootable)
		if denIntersectsWithNonShootable.GetBoolValue() {
			denominator += overallMetric.IntersectsWithNonShootable
		}
		denOutOfRange := extraConclusion.GetNode("denominator/out_of_range")
		defer config.PreDeleteConfigTree(denOutOfRange)
		if denOutOfRange.GetBoolValue() {
			denominator += overallMetric.OutOfRange
		}
		denUnimportant := extraConclusion.GetNode("denominator/unimportant")
		defer config.PreDeleteConfigTree(denUnimportant)
		if denUnimportant.GetBoolValue() {
			denominator += overallMetric.Unimportant
		}
		denNotShot := extraConclusion.GetNode("denominator/not_shot")
		defer config.PreDeleteConfigTree(denNotShot)
		if denNotShot.GetBoolValue() {
			denominator += overallMetric.NotShot
		}
		denPartiallyShot := extraConclusion.GetNode("denominator/partially_shot")
		defer config.PreDeleteConfigTree(denPartiallyShot)
		if denPartiallyShot.GetBoolValue() {
			denominator += overallMetric.PartiallyShot
		}
		denShot := extraConclusion.GetNode("denominator/shot")
		defer config.PreDeleteConfigTree(denShot)
		if denShot.GetBoolValue() {
			denominator += overallMetric.Shot
		}
		denP2PNotFound := extraConclusion.GetNode("denominator/p2p_not_found")
		defer config.PreDeleteConfigTree(denP2PNotFound)
		if denP2PNotFound.GetBoolValue() {
			denominator += overallMetric.P2PNotFound
		}
		denError := extraConclusion.GetNode("denominator/error")
		defer config.PreDeleteConfigTree(denError)
		if denError.GetBoolValue() {
			denominator += overallMetric.Error
		}
		denFlicker := extraConclusion.GetNode("denominator/flicker")
		defer config.PreDeleteConfigTree(denFlicker)
		if denFlicker.GetBoolValue() {
			denominator += overallMetric.Flicker
		}
		denMarkedForThinning := extraConclusion.GetNode("denominator/marked_for_thinning")
		defer config.PreDeleteConfigTree(denMarkedForThinning)
		if denMarkedForThinning.GetBoolValue() {
			denominator += overallMetric.MarkedForThinning
		}
		denNotTargeted := extraConclusion.GetNode("denominator/not_targeted")
		defer config.PreDeleteConfigTree(denNotTargeted)
		if denNotTargeted.GetBoolValue() {
			denominator += overallMetric.NotTargeted
		}
		denP2PMissingContext := extraConclusion.GetNode("denominator/p2p_missing_context")
		defer config.PreDeleteConfigTree(denP2PMissingContext)
		if denP2PMissingContext.GetBoolValue() {
			denominator += overallMetric.P2PMissingContext
		}

		var percent uint32 = 0
		if denominator != 0 {
			percent = (100 * numerator) / denominator
		}

		color := "green"
		if percent != 0 {
			if flipThresholds.GetBoolValue() {
				if percent > uint32(mediumThreshold.GetUIntValue()) {
					color = "red"
				} else if percent > uint32(goodThreshold.GetUIntValue()) {
					color = "orange"
				}
			} else {
				if percent < uint32(mediumThreshold.GetUIntValue()) {
					color = "red"
				} else if percent < uint32(goodThreshold.GetUIntValue()) {
					color = "orange"
				}
			}
		}

		dashboardExtraConclusion.FlipThresholds = flipThresholds.GetBoolValue()
		dashboardExtraConclusion.GoodThresholdPercent = uint32(goodThreshold.GetUIntValue())
		dashboardExtraConclusion.MediumThresholdPercent = uint32(mediumThreshold.GetUIntValue())

		statusText := fmt.Sprintf("%d%%", percent)
		if percent >= 100 {
			statusText = "99+%"
		}
		dashboardExtraConclusion.Percent = &frontend.PercentValue{Percent: percent}

		titleText := title.GetStringValue()
		dashboardExtraConclusion.Title = titleText

		extraPercentStatus := &ExtraDashboardStatus{
			Title:       titleText,
			IconName:    "",
			IconColor:   "",
			StatusText:  statusText,
			StatusColor: color,
			Priority:    StatusPriorityHigh,
		}

		e.ExtraAction(extraPercentStatus)
		addedExtraConclusions[titleText] = true
		dashboardExtraConclusions = append(dashboardExtraConclusions, dashboardExtraConclusion)
	}

	for existingExtraConclusion := range e.ExistingExtraConclusions {
		if _, ok := addedExtraConclusions[existingExtraConclusion]; !ok {
			e.ExtraActionRemove(existingExtraConclusion)
		}
	}
	e.ExistingExtraConclusions = addedExtraConclusions
	e.opState.WriteOnCurrent(func() {
		e.opState.Dashboard.ExtraConclusions = dashboardExtraConclusions
	})
}

func (e *ExtraStatusManager) WeedingAction() {
	var enabled bool
	var areaTotal float64
	var areaToday float64
	var timeTotalMinutes int64
	var timeTodayMinutes int64
	var weedsToday int64
	var weedsTotal int64
	var cropsToday int64
	var cropsTotal int64
	e.weedingState.ReadOnCurrent(func() {
		enabled = e.weedingState.IsWeeding
		areaTotal = e.weedingState.AreaWeededTotal
		areaToday = e.weedingState.AreaWeededToday
		timeTotalMinutes = e.weedingState.TimeWeededTotalMs / 60000
		timeTodayMinutes = e.weedingState.TimeWeededTodayMs / 60000
		weedsToday = e.weedingState.WeedsKilledToday
		weedsTotal = e.weedingState.WeedsKilledTotal
		cropsToday = e.weedingState.CropsKilledToday
		cropsTotal = e.weedingState.CropsKilledTotal
	})

	iconColor := "green"
	if !enabled {
		iconColor = "grey"
	}

	const areaGroupId = "group_area"

	today := &ExtraDashboardStatus{
		Title:       "Area Weeded Today",
		GroupId:     areaGroupId,
		IconName:    "",
		IconColor:   "",
		StatusText:  fmt.Sprintf("%.2f acres", areaToday),
		StatusColor: iconColor,
		Priority:    StatusPriorityLow,
	}
	e.ExtraAction(today)

	total := &ExtraDashboardStatus{
		Title:       "Area Weeded Total",
		GroupId:     areaGroupId,
		IconName:    "",
		IconColor:   "",
		StatusText:  fmt.Sprintf("%.2f acres", areaTotal),
		StatusColor: iconColor,
		Priority:    StatusPriorityLow,
	}
	e.ExtraAction(total)

	const killedGroupId = "group_killed"

	today = &ExtraDashboardStatus{
		Title:       "Weeds Killed Today",
		GroupId:     killedGroupId,
		IconName:    "",
		IconColor:   "",
		StatusText:  fmt.Sprintf("%s weeds", formatting.NearestThousandFormat(float64(weedsToday))),
		StatusColor: iconColor,
		Priority:    StatusPriorityLow,
	}
	e.ExtraAction(today)

	total = &ExtraDashboardStatus{
		Title:       "Weeds Killed Total",
		GroupId:     killedGroupId,
		IconName:    "",
		IconColor:   "",
		StatusText:  fmt.Sprintf("%s weeds", formatting.NearestThousandFormat(float64(weedsTotal))),
		StatusColor: iconColor,
		Priority:    StatusPriorityLow,
	}
	e.ExtraAction(total)

	const timeGroupId = "group_time"

	todayHours := timeTodayMinutes / 60
	todayMinutes := timeTodayMinutes % 60
	today = &ExtraDashboardStatus{
		Title:       "Time Weeded Today",
		GroupId:     timeGroupId,
		IconName:    "",
		IconColor:   "",
		StatusText:  fmt.Sprintf("%dh %dm", todayHours, todayMinutes),
		StatusColor: iconColor,
		Priority:    StatusPriorityLow,
	}
	e.ExtraAction(today)

	totalHours := timeTotalMinutes / 60
	total = &ExtraDashboardStatus{
		Title:       "Time Weeded Total",
		GroupId:     timeGroupId,
		IconName:    "",
		IconColor:   "",
		StatusText:  fmt.Sprintf("%dh", totalHours),
		StatusColor: iconColor,
		Priority:    StatusPriorityLow,
	}
	e.ExtraAction(total)

	e.opState.WriteOnCurrent(func() {
		e.opState.Dashboard.AreaWeededToday = areaToday
		e.opState.Dashboard.AreaWeededTotal = areaTotal
		e.opState.Dashboard.TimeWeededTodayMinutes = timeTodayMinutes
		e.opState.Dashboard.TimeWeededTotalMinutes = timeTotalMinutes
		e.opState.Dashboard.WeedsKilledToday = weedsToday
		e.opState.Dashboard.WeedsKilledTotal = weedsTotal
		e.opState.Dashboard.CropsKilledToday = cropsToday
		e.opState.Dashboard.CropsKilledTotal = cropsTotal
		e.opState.Dashboard.WeedingEnabled = enabled
	})
}

func (e *ExtraStatusManager) Action() {
	var wg sync.WaitGroup

	actions := []func(){
		e.ImplementAction,
		e.WeedingAction,
		e.ConclusionMetricsAction,
	}

	wg.Add(len(actions))
	for _, action := range actions {
		go func(a func()) {
			defer wg.Done()
			a()
		}(action)
	}

	wg.Wait()
}

type SelectedModelWatcher struct {
	EventTrigger
	opState             *OperationsState
	jobsState           *JobsState
	commanderConfigNode *config.ConfigTree
	commonConfigNode    *config.ConfigTree
}

func NewSelectedModelWatcher(opState *OperationsState, jobsState *JobsState, commanderConfigNode *config.ConfigTree, commonConfigNode *config.ConfigTree) *SelectedModelWatcher {
	action := &SelectedModelWatcher{
		opState:             opState,
		jobsState:           jobsState,
		commanderConfigNode: commanderConfigNode,
		commonConfigNode:    commonConfigNode,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (e *SelectedModelWatcher) Action() {
	changed := false
	e.opState.ReadOnCurrent(func() {
		if e.opState.SelectedModel.Crop != e.commanderConfigNode.GetNode("current_crop").GetStringValue() ||
			e.opState.SelectedModel.CropID != e.commanderConfigNode.GetNode("current_crop_id").GetStringValue() ||
			e.opState.SelectedModel.Model != e.commonConfigNode.GetNode("deepweed/model_id").GetStringValue() {
			changed = true
		}
	})
	if changed {
		e.opState.WriteOnCurrent(func() {
			e.opState.SelectedModel.Crop = e.commanderConfigNode.GetNode("current_crop").GetStringValue()
			e.opState.SelectedModel.CropID = e.commanderConfigNode.GetNode("current_crop_id").GetStringValue()
			e.opState.SelectedModel.Model = e.commonConfigNode.GetNode("deepweed/model_id").GetStringValue()
			e.jobsState.UpdateCropId(e.opState.SelectedModel.CropID)
		})
	}
	cropModelGauge.WithLabelValues(e.commonConfigNode.GetNode("deepweed/model_id").GetStringValue(), e.commanderConfigNode.GetNode("current_crop").GetStringValue()).Set(1)
}

type WeedingState struct {
	ManagedStateImpl
	IsWeeding bool

	AreaWeededTotal   float64
	AreaWeededToday   float64
	TimeWeededTodayMs int64
	TimeWeededTotalMs int64
	WeedsKilledTotal  int64
	WeedsKilledToday  int64
	CropsKilledTotal  int64
	CropsKilledToday  int64

	numRowsActive int
}

func NewWeedingState() *WeedingState {
	state := WeedingState{
		ManagedStateImpl: ManagedStateImpl{name: "WeedingState"},
	}
	state.initialize()
	return &state
}

type VelocityStateIF interface {
	ManagedState
	GetCurrentVelocity() float64
}

type WeedingStatusWatcher struct {
	Runnable
	weedingState                *WeedingState
	opState                     *OperationsState
	implState                   *ImplementState
	velState                    VelocityStateIF
	configSubscriber            *config.ConfigSubscriber
	hwManager                   *hardware_manager.HardwareManagerClient
	redisClient                 *redis.Client
	minVelocity                 *config.ConfigTree
	killCountPersistIntervalSec *config.ConfigTree
	areaPersistIntervalSec      *config.ConfigTree
	areaUpdateIntervalSec       *config.ConfigTree
	rowWidthIn                  *config.ConfigTree
	gpsTZTimeout                uint64
	stopCtx                     context.Context

	lastKnownWeedingTs int64
	initialized        bool
}

func NewWeedingStatusWatcher(configSubscriber *config.ConfigSubscriber, hwManager *hardware_manager.HardwareManagerClient, redisClient *redis.Client, weedingState *WeedingState, opState *OperationsState, implState *ImplementState, velState VelocityStateIF, stopCtx context.Context) *WeedingStatusWatcher {
	watcher := &WeedingStatusWatcher{
		weedingState:                weedingState,
		opState:                     opState,
		implState:                   implState,
		velState:                    velState,
		configSubscriber:            configSubscriber,
		redisClient:                 redisClient,
		hwManager:                   hwManager,
		stopCtx:                     stopCtx,
		minVelocity:                 configSubscriber.GetConfigNode("commander", "min_speed_for_weeding_indication_mph"),
		killCountPersistIntervalSec: configSubscriber.GetConfigNode("common", "weeding_metrics/kill_count_persist_interval_sec"),
		areaPersistIntervalSec:      configSubscriber.GetConfigNode("common", "weeding_metrics/area_persist_interval_sec"),
		areaUpdateIntervalSec:       configSubscriber.GetConfigNode("common", "weeding_metrics/area_update_interval"),
		rowWidthIn:                  configSubscriber.GetConfigNode("common", "row_width_in"),
		gpsTZTimeout:                configSubscriber.GetConfigNode("commander", "gps_timezone_init_timeout").GetUIntValue(),
		initialized:                 false,
	}
	return watcher
}

func notifyUpdate(state ManagedState, updated chan bool, stopCtx context.Context) {
	var ts int64
	ts = 0
	for {
		result := state.ReadOnNext(stopCtx, ts, func() {
			ts = state.GetTimestampMs()
		})
		if !result {
			return
		}
		updated <- true
	}
}

func commanderTodaysKey(what string, tz string) string {
	return fmt.Sprintf("%v/commander/%v_weeded_today", getToday(tz), what)
}
func (w *WeedingStatusWatcher) getTZ() string {
	tz, err := w.redisClient.GetTimezone()
	if err != nil {
		logrus.Warnf("WeedingStatusWatcher: Failed to get current timezone, falling back to %v", default_tz)
		return default_tz
	}
	return tz
}

func (w *WeedingStatusWatcher) aimbotTodaysKey(row string) string {
	return fmt.Sprintf("%v/weed_tracking/row%v/counts", getToday(w.getTZ()), row)
}

func getToday(tz string) string {
	loc, _ := time.LoadLocation(tz)
	curTime := time.Now().In(loc)
	return fmt.Sprintf("%d-%02d-%02d", curTime.Year(), curTime.Month(), curTime.Day())
}

func aimbotTotalKey(row string, is_weed bool) string {
	typeStr := "crop"
	if is_weed {
		typeStr = "weed"
	}
	return fmt.Sprintf("weed_tracking/row%v/%v_kill_count_total", row, typeStr)
}

func (w *WeedingStatusWatcher) readTodayKillCount(is_weed bool) int64 {
	/*
	 * Because redis does not support regex key pattern matching, just glob,
	 * we get potentially more keys than desired. As such need to filter to only
	 * the row/module keys using a regex once we globed keys from redis
	 */
	r, _ := regexp.Compile(w.aimbotTodaysKey("[^/]+"))
	keys, _ := w.redisClient.Keys(w.aimbotTodaysKey("*"))
	typeStr := "crop"
	if is_weed {
		typeStr = "weed"
	}
	hashKey := fmt.Sprintf("armed/%v/%v", typeStr, swig_metrics.ConclusionType_kShot)
	var total int64 = 0
	for _, key := range keys {
		if !r.MatchString(key) {
			continue
		}
		tmp, _ := w.redisClient.HReadInt64(key, hashKey, 0)
		total += tmp
	}
	return total
}
func (w *WeedingStatusWatcher) readTotalKillCount(is_weed bool) int64 {
	/*
	 * Because redis does not support regex key pattern matching, just glob,
	 * we get potentially more keys than desired. As such need to filter to only
	 * the row/module keys using a regex once we globed keys from redis
	 */
	r, _ := regexp.Compile(aimbotTotalKey("[^/]+", is_weed))
	keys, _ := w.redisClient.Keys(aimbotTotalKey("*", is_weed))
	var total int64 = 0
	for _, key := range keys {
		if !r.MatchString(key) {
			continue
		}
		tmp, _ := w.redisClient.ReadInt64(key, 0)
		total += tmp
	}
	return total
}

func (w *WeedingStatusWatcher) loopReadKillCountFromDB() {
	for {
		time.Sleep(time.Duration(w.killCountPersistIntervalSec.GetUIntValue()) * time.Second)
		weedsKilledTotal := w.readTotalKillCount(true)
		weedsKilledToday := w.readTodayKillCount(true)
		cropsKilledTotal := w.readTotalKillCount(false)
		cropsKilledToday := w.readTodayKillCount(false)
		var prevWeedTotal int64
		var prevWeedToday int64
		var prevCropTotal int64
		var prevCropToday int64
		w.weedingState.ReadOnCurrent(func() {
			prevWeedTotal = w.weedingState.WeedsKilledTotal
			prevWeedToday = w.weedingState.WeedsKilledToday
			prevCropTotal = w.weedingState.CropsKilledTotal
			prevCropToday = w.weedingState.CropsKilledToday
		})
		if prevWeedToday != weedsKilledToday || prevWeedTotal != weedsKilledTotal || prevCropToday != cropsKilledToday || prevCropTotal != cropsKilledTotal {
			w.weedingState.WriteOnCurrent(func() {
				w.weedingState.WeedsKilledTotal = weedsKilledTotal
				w.weedingState.WeedsKilledToday = weedsKilledToday
				w.weedingState.CropsKilledTotal = cropsKilledTotal
				w.weedingState.CropsKilledToday = cropsKilledToday
			})
		}
	}
}

func (w *WeedingStatusWatcher) initialReadFromDB() {
	areaTotal, _ := w.redisClient.ReadFloat("commander/area_weeded_total", 0)
	areaToday, _ := w.redisClient.ReadFloat(commanderTodaysKey("area", w.getTZ()), 0)
	timeWeededTotal, _ := w.redisClient.ReadInt64("commander/time_weeded_total", 0)
	timeWeededToday, _ := w.redisClient.ReadInt64(commanderTodaysKey("time", w.getTZ()), 0)
	weedsKilledTotal := w.readTotalKillCount(true)
	weedsKilledToday := w.readTodayKillCount(true)
	cropsKilledTotal := w.readTotalKillCount(false)
	cropsKilledToday := w.readTodayKillCount(false)

	w.weedingState.WriteOnCurrent(func() {
		w.weedingState.AreaWeededTotal = areaTotal
		w.weedingState.AreaWeededToday = areaToday
		w.weedingState.TimeWeededTotalMs = timeWeededTotal
		w.weedingState.TimeWeededTodayMs = timeWeededToday
		w.weedingState.WeedsKilledTotal = weedsKilledTotal
		w.weedingState.WeedsKilledToday = weedsKilledToday
		w.weedingState.CropsKilledTotal = cropsKilledTotal
		w.weedingState.CropsKilledToday = cropsKilledToday
	})

	logrus.Debugf("WeedingStatusWatcher: Read weeding stats from redis: area=%v/%v, time=%v/%v, kills=%v/%v",
		areaToday, areaTotal, timeWeededToday, timeWeededTotal, weedsKilledToday, weedsKilledTotal)

	// report current values to prometheus, because otherwise
	// it will show values = zero <= unexpected behavior
	areaWeededTotalGauge.Set(areaTotal)
	areaWeededTodayGauge.Set(areaToday)
	timeWeededTotalGauge.Set(float64(timeWeededTotal / 60000))
	timeWeededTodayGauge.Set(float64(timeWeededToday / 60000))
	isWeedingGauge.Set(metrics.BoolToFloat(false))

}

func (w *WeedingStatusWatcher) writeToDB() {
	for {
		sleepSec := w.areaPersistIntervalSec.GetUIntValue()
		time.Sleep(time.Duration(sleepSec) * time.Second)

		var enabled bool
		var areaTotal float64
		var areaToday float64
		var timeTotal int64
		var timeToday int64

		w.weedingState.ReadOnCurrent(func() {
			enabled = w.weedingState.IsWeeding
			areaTotal = w.weedingState.AreaWeededTotal
			areaToday = w.weedingState.AreaWeededToday
			timeTotal = w.weedingState.TimeWeededTotalMs
			timeToday = w.weedingState.TimeWeededTodayMs
		})

		if enabled {
			w.redisClient.WriteFloat("commander/area_weeded_total", areaTotal)
			w.redisClient.WriteFloat(commanderTodaysKey("area", w.getTZ()), areaToday)
			w.redisClient.WriteInt64("commander/time_weeded_total", timeTotal)
			w.redisClient.WriteInt64(commanderTodaysKey("time", w.getTZ()), timeToday)

			logrus.Debugf("WeedingStatusWatcher: Wrote weeding stats to redis, area=%v/%v, time=%v/%v", areaToday, areaTotal, timeToday, timeTotal)
		}
	}
}
func getDayAndTimeTillTomorrow(tz string) (int, time.Duration) {
	loc, _ := time.LoadLocation(tz)
	curTime := time.Now().In(loc)
	d := curTime.Day()
	year, month, day := curTime.Date()
	tomorrow := time.Date(year, month, day, 0, 0, 0, 0, curTime.Location()).AddDate(0, 0, 1)
	return d, tomorrow.Sub(curTime)
}
func (w *WeedingStatusWatcher) clockWatch() {
	prevTz := w.getTZ()
	logrus.Infof("The current timezone is %v", prevTz)
	d, nextDaySec := getDayAndTimeTillTomorrow(prevTz)
	nextDayTicker := time.NewTicker(nextDaySec)
	tzCheckTick := time.NewTicker(time.Duration(1) * time.Minute)

	defer tzCheckTick.Stop()
	defer nextDayTicker.Stop()
	for {
		select {
		case <-tzCheckTick.C:
		case <-nextDayTicker.C:
		}
		curTz := w.getTZ()
		if curTz != prevTz {
			logrus.Infof("Timezone has changed from %v to %v", prevTz, curTz)
			prevTz = curTz
		}
		curDay, nextDaySec := getDayAndTimeTillTomorrow(prevTz)
		if curDay != d {
			d = curDay
			nextDayTicker.Reset(nextDaySec)

			logrus.Infof("WeedingStatusWatcher: resetting daily counter")
			w.weedingState.WriteOnCurrent(func() {
				w.weedingState.AreaWeededToday = 0
				w.weedingState.TimeWeededTodayMs = 0
			})
		}
	}
}

func (w *WeedingStatusWatcher) initTZ() {
	freq := time.Duration(10) * time.Second
	endTime := time.Now().Add(time.Duration(1) * time.Minute)
	for {
		_, err := w.redisClient.GetTimezone()
		if err == nil {
			return
		}
		if time.Now().After(endTime) {
			logrus.Warn("Timed out waiting for timezone data from redis")
			return
		}
		time.Sleep(freq)
	}
}
func (w *WeedingStatusWatcher) Run() {
	w.initTZ()
	w.updateDistance()

	w.initialReadFromDB()

	stateUpdated := make(chan bool, 1)

	go notifyUpdate(w.opState, stateUpdated, w.stopCtx)
	go notifyUpdate(w.implState, stateUpdated, w.stopCtx)
	go notifyUpdate(w.velState, stateUpdated, w.stopCtx)
	go w.writeToDB()
	go w.clockWatch()
	go w.loopReadKillCountFromDB()
	go w.loopUpdateDistance()

	for {
		select {
		case <-stateUpdated:
			w.onStateUpdate()
		}
	}
}

func (w *WeedingStatusWatcher) loopUpdateDistance() {
	for {
		w.updateDistance()
		sleepSec := w.areaUpdateIntervalSec.GetUIntValue()
		time.Sleep(time.Duration(sleepSec) * time.Second)
	}
}
func (w *WeedingStatusWatcher) updateDistance() {

	deltaMM, err := w.hwManager.GetDeltaTravelMM("WeedingStatusWatcher")
	if err != nil {
		logrus.WithError(err).Error("WeedingStatusWatcher: Could not read delta distance from hw manager, waiting for reconnect and update")
		return
	}

	if deltaMM < 0 {
		// treadkill can drive in reverse,
		// implement shouldn't, (maybe only when turning?).
		// in any case, prometheus counter will crash the commander if area is negative,
		// and it doesnt make sense to measure it anyway
		logrus.Warningf("WeedingStatusWatcher: Cannot have negative deltaMM.")
		return
	}

	var enabled bool
	var rows int
	var areaToday float64
	var timeWeededToday int64
	var timeWeededTotal int64
	w.weedingState.ReadOnCurrent(func() {
		enabled = w.weedingState.IsWeeding
		rows = w.weedingState.numRowsActive
		timeWeededTotal = w.weedingState.TimeWeededTotalMs
		timeWeededToday = w.weedingState.TimeWeededTodayMs
		areaToday = w.weedingState.AreaWeededToday
	})

	if !enabled {
		return
	}

	deltaTs := time.Now().UnixMilli() - w.lastKnownWeedingTs
	w.lastKnownWeedingTs = time.Now().UnixMilli()

	rowWidthIn := w.rowWidthIn.GetFloatValue()
	rowWidthFeet := float64(rowWidthIn / 12.0)
	deltaFeet := deltaMM / 304.8

	areaSquareFeet := deltaFeet * rowWidthFeet * float64(rows)
	areaAcres := areaSquareFeet / 43560

	var areaTotal float64
	w.weedingState.WriteOnCurrent(func() {
		w.weedingState.AreaWeededTotal += areaAcres
		w.weedingState.AreaWeededToday += areaAcres
		w.weedingState.TimeWeededTodayMs += deltaTs
		w.weedingState.TimeWeededTotalMs += deltaTs
		areaTotal = w.weedingState.AreaWeededTotal
		timeWeededTotal = w.weedingState.TimeWeededTotalMs
		timeWeededToday = w.weedingState.TimeWeededTodayMs
		areaToday = w.weedingState.AreaWeededToday
	})

	areaWeededTotalGauge.Set(areaTotal)
	areaWeededTodayGauge.Set(areaToday)
	timeWeededTotalGauge.Set(float64(timeWeededTotal / 60000))
	timeWeededTodayGauge.Set(float64(timeWeededToday / 60000))
	isWeedingGauge.Set(metrics.BoolToFloat(enabled))
}

func (w *WeedingStatusWatcher) onStateUpdate() {
	var prevIsWeeding bool
	var prevNumRowsActive int

	var ready bool
	var lifted bool
	var numRowsEnabled int
	velocity := w.velState.GetCurrentVelocity()

	w.weedingState.ReadOnCurrent(func() {
		prevIsWeeding = w.weedingState.IsWeeding
		prevNumRowsActive = w.weedingState.numRowsActive
	})

	w.opState.ReadOnCurrent(func() {
		ready = w.opState.Ready
		for _, r := range w.opState.RowWeeding {
			if r.ActualTargeting.Enabled() {
				numRowsEnabled++
			}
		}
	})
	w.implState.ReadOnCurrent(func() {
		lifted = w.implState.SafetyState.Lifted
	})

	minVelocity := w.minVelocity.GetFloatValue()
	isWeeding := ready && !lifted && (numRowsEnabled > 0) && (velocity > minVelocity)

	if (isWeeding != prevIsWeeding) || (numRowsEnabled != prevNumRowsActive) {
		logrus.Infof("WeedingStatusWatcher: updating weeding state to enabled=%v, lasers=%v, lifted=%v, numRowEnabled=%v, velocity=%v", isWeeding, ready, lifted, numRowsEnabled, velocity)

		w.weedingState.WriteOnCurrent(func() {
			w.weedingState.IsWeeding = isWeeding
			w.weedingState.numRowsActive = numRowsEnabled
		})

		if isWeeding {
			w.lastKnownWeedingTs = time.Now().UnixMilli()
		}
	}
	if isWeeding != prevIsWeeding || !w.initialized {
		w.initialized = true
		weeding_str := "0"
		if isWeeding {
			weeding_str = "1"
		}
		w.redisClient.WriteString("commander/weeding", weeding_str)
	}

	isWeedingGauge.Set(metrics.BoolToFloat(isWeeding))
}

package state

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/stretchr/testify/assert"
)

func TestCategoryCollectionsEqual(t *testing.T) {
	tests := []struct {
		name  string
		a     []*category.CategoryCollection
		b     []*category.CategoryCollection
		equal bool
	}{
		{
			name:  "nil lists",
			a:     nil,
			b:     nil,
			equal: true,
		},
		{
			name:  "empty lists",
			a:     []*category.CategoryCollection{},
			b:     []*category.CategoryCollection{},
			equal: true,
		},
		{
			name:  "different lengths",
			a:     []*category.CategoryCollection{{Id: "1"}},
			b:     []*category.CategoryCollection{},
			equal: false,
		},
		{
			name:  "different ids",
			a:     []*category.CategoryCollection{{Id: "1"}},
			b:     []*category.CategoryCollection{{Id: "2"}},
			equal: false,
		},
		{
			name:  "same ids",
			a:     []*category.CategoryCollection{{Id: "1"}},
			b:     []*category.CategoryCollection{{Id: "1"}},
			equal: true,
		},
		{
			name:  "same ids different order",
			a:     []*category.CategoryCollection{{Id: "1"}, {Id: "2"}},
			b:     []*category.CategoryCollection{{Id: "2"}, {Id: "1"}},
			equal: true,
		},
		{
			name:  "same category ids different order",
			a:     []*category.CategoryCollection{{Id: "1", CategoryIds: []string{"1", "2"}}, {Id: "2", CategoryIds: []string{"3", "4"}}},
			b:     []*category.CategoryCollection{{Id: "2", CategoryIds: []string{"4", "3"}}, {Id: "1", CategoryIds: []string{"2", "1"}}},
			equal: true,
		},
		{
			name:  "different category ids",
			a:     []*category.CategoryCollection{{Id: "1", CategoryIds: []string{"1", "2"}}, {Id: "2", CategoryIds: []string{"3", "4"}}},
			b:     []*category.CategoryCollection{{Id: "2", CategoryIds: []string{"4", "3"}}, {Id: "1", CategoryIds: []string{"2", "3"}}},
			equal: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, test.equal, categoryCollectionsEqual(test.a, test.b))
		})
	}
}

func TestCategoryCollectionEqual(t *testing.T) {
	tests := []struct {
		name  string
		a     *category.CategoryCollection
		b     *category.CategoryCollection
		equal bool
	}{
		{
			name:  "empty lists",
			a:     &category.CategoryCollection{},
			b:     &category.CategoryCollection{},
			equal: true,
		},
		{
			name:  "different lengths",
			a:     &category.CategoryCollection{Id: "1"},
			b:     &category.CategoryCollection{},
			equal: false,
		},
		{
			name:  "different ids",
			a:     &category.CategoryCollection{Id: "1"},
			b:     &category.CategoryCollection{Id: "2"},
			equal: false,
		},
		{
			name:  "same ids",
			a:     &category.CategoryCollection{Id: "1"},
			b:     &category.CategoryCollection{Id: "1"},
			equal: true,
		},
		{
			name:  "same chip ids different order",
			a:     &category.CategoryCollection{Id: "1", CategoryIds: []string{"1", "2"}},
			b:     &category.CategoryCollection{Id: "1", CategoryIds: []string{"2", "1"}},
			equal: true,
		},
		{
			name:  "different chip ids",
			a:     &category.CategoryCollection{Id: "1", CategoryIds: []string{"1", "2"}},
			b:     &category.CategoryCollection{Id: "1", CategoryIds: []string{"2", "5"}},
			equal: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, test.equal, categoryCollectionEqual(test.a, test.b))
		})
	}
}

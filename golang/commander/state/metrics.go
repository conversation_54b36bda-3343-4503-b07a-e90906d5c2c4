package state

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	chipDiskBytesGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "chip_bytes",
		Help:      "total size of chips on disk.",
	})
	localChipCountGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "local_chip_count",
		Help:      "count of chips on disk.",
	})
	modelMinDownloadAge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "model_min_download_time",
		Help:      "age of youngest model on implement",
	})
	modelDiskBytesGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "model_bytes",
		Help:      "total size of models on disk.",
	})
	localModelCountGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "local_model_count",
		Help:      "count of models containing artifacts on disk.",
	})
	localModelArtifactCountGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "local_model_artifact_count",
		Help:      "count of model artifacts on disk.",
	})
	lastCropIDSyncGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_crop_id_sync_time",
		Help:      "time of last crop id sync",
	})
	lastModelSyncGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_model_sync_time",
		Help:      "time of last model sync.",
	})
	lastModelDownloadGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_model_download_time",
		Help:      "time of last model download.",
	})
	lastModelCleanupGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_model_cleanup_time",
		Help:      "time of last model cleanup.",
	})
	lastModelUpdateSyncGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_model_update_sync_time",
		Help:      "time of last sync update.",
	})
	pinnedModelGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "model_pinned",
		Help:      "boolean value if the best model is pinned.",
	})
	recommendedModelGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "model_recommended",
		Help:      "boolean value if the best model is recommended.",
	})
	lastActiveModelGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "model_last_active",
		Help:      "boolean value if the best model is last active.",
	})
	viableModelGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "model_viable",
		Help:      "boolean value if the best model is viable.",
	})
	deepweedModelCreateTSGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "deepweed_model_create_time_ms",
		Help:      "created timestamp of the active deep weed model.",
	})
	p2pModelCreateTSGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "p2p_model_create_time_ms",
		Help:      "created timestamp of the active p2p model.",
	})
	ActiveRecomendedModelinatorGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "active_recomended_modelinator",
		Help:      "boolean value if implement is using the recommended modelinator settings",
	})
	lastChipSyncGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_chip_sync_time",
		Help:      "time of last chip sync.",
	})
	lastChipDownloadGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_chip_download_time",
		Help:      "time of last chip download.",
	})
	lastChipCleanupGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "last_chip_cleanup_time",
		Help:      "time of last chip cleanup.",
	})
)

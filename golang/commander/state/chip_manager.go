package state

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/carbonrobotics/robot/golang/lib/chip_manager"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	crredis "github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

/**
 * Chip Manager manages the downloading of chips associated with the
 * categories linked to a robot's customer's Category Profiles. Chips
 * are partial images cropped for use by a specific category.
 *
 * The Chip Manager is responsible for looking to redis for the
 * latest Category Profiles, assessing the Categories associated with
 * those profiles, and downloading the chips associated with those
 * Categories. Priority is given to those used by active Categories.
 *
 * Chips and metadata files are stored in /data/carbon/data/embeddings/chip_cache
 * in the format <chip_id>.json and <chip_id>.png, respectively.
 **/

const (
	ChipCacheDirName = "embeddings/chip_cache"

	chipCleanupInterval         = 24 * time.Hour
	chipDownloadInterval        = 30 * time.Minute
	chipSyncToRowsInterval      = 60 * time.Second
	veselkaChipSyncInterval     = 30 * time.Minute
	checkProfileUpdatesInterval = 15 * time.Second

	chipMetadataBatchSize = 64

	chipExpirationThreshold       = 30 * 24 * time.Hour
	chunkSize               int64 = 4 * 1024 * 1024  // 4MB
	maxChipSize             int64 = 10 * 1024 * 1024 // 10MB
)

var (
	ChipManagerFs     = afero.NewOsFs()
	ChipManagerLogger = logrus.New()
)

type ChipManVeselkaClient interface {
	SetLogLevel(level logrus.Level)
	GetImageMetadata(ctx context.Context, ids []string) (map[string]*veselka.ChipDefinition, error)
}

type ChipManagerState struct {
	ManagedStateImpl
	LocalChips  map[string]*chip_manager.Chip
	SyncedChips map[string]*chip_manager.Chip

	lastChipCleanup        time.Time
	lastChipDownload       time.Time
	lastChipSync           time.Time
	lastVeselkaChipSync    time.Time
	lastProfileUpdateCheck time.Time

	activeCategoryCollection *category.CategoryCollection
	activeCategories         map[string]*category.Category
}

func NewChipManagerState() *ChipManagerState {
	state := &ChipManagerState{
		LocalChips:  make(map[string]*chip_manager.Chip),
		SyncedChips: make(map[string]*chip_manager.Chip),
	}
	state.initialize()
	return state
}

func (c *ChipManagerState) GetLocalChips() (chips map[string]*chip_manager.Chip) {
	c.ReadOnCurrent(func() {
		chips = CopyMap(c.LocalChips)
	})
	return
}

func (c *ChipManagerState) GetSyncedChips() (chips map[string]*chip_manager.Chip) {
	c.ReadOnCurrent(func() {
		chips = CopyMap(c.SyncedChips)
	})
	return
}

/****************************************************************************************/
// GRPC functions
func (c *ChipManagerState) GetChipMetadata(ctx context.Context) ([]*frontend.ChipData, error) {
	chips := c.GetLocalChips()
	chipData := make([]*frontend.ChipData, 0, len(chips))
	for _, chip := range chips {
		chipData = append(chipData, &frontend.ChipData{
			Id:            chip.Id,
			Url:           chip.URL,
			Geohash:       chip.Geohash,
			Checksum:      chip.Checksum,
			ContentLength: uint32(chip.ContentLength),
			DownloadedTs:  &frontend.Timestamp{TimestampMs: int64(chip.DownloadedTimestamp)},
			LastUsedTs:    &frontend.Timestamp{TimestampMs: int64(chip.LastUsedTimestamp)},
		})
	}
	return chipData, nil
}

func (c *ChipManagerState) GetDownloadedChipIds(ctx context.Context) []string {
	chips := c.GetLocalChips()
	chipIds := make([]string, 0, len(chips))
	for id := range chips {
		chipIds = append(chipIds, id)
	}
	return chipIds
}

func (c *ChipManagerState) GetSyncedChipIds(ctx context.Context) []string {
	chips := c.GetSyncedChips()
	chipIds := make([]string, 0, len(chips))
	for id := range chips {
		chipIds = append(chipIds, id)
	}
	return chipIds
}

/****************************************************************************************/

type ChipManagerWatcher struct {
	EventTrigger
	chipManagerState *ChipManagerState
	chipCacheDir     string
	commanderNode    config.Tree
	commonNode       config.Tree
	env              environment.Robot
	logger           *logrus.Entry
	rowClients       RowClientList
	stopCtx          context.Context
	veselkaClient    ChipManVeselkaClient
	redisClient      *crredis.Client

	categoryCollectionState *CategoryCollectionCfgState
	categoryState           *CategoryCfgState

	cleanupChipsCh           chan bool
	downloadCh               chan bool
	syncChipsCh              chan bool
	fetchChipsCh             chan bool
	updateSyncedChipsCh      chan bool
	checkForProfileUpdatesCh chan bool

	chipCacheWriteMutex *sync.Mutex
}

type ChipManagerWatcherParams struct {
	ChipManagerState *ChipManagerState
	CommanderNode    *config.ConfigTree
	CommonNode       *config.ConfigTree
	Env              environment.Robot
	RowClients       map[int]*rows.RowClients
	StopCtx          context.Context
	VeselkaClient    *veselka.Client
	RedisClient      *crredis.Client

	CategoryCollectionState *CategoryCollectionCfgState
	CategoryState           *CategoryCfgState
}

func NewChipManagerWatcher(params ChipManagerWatcherParams) *ChipManagerWatcher {
	dataDir := params.Env.MakaDataDir
	if len(dataDir) == 0 {
		dataDir = "/data"
	}

	rowClients := make([]*RowClient, 0)
	for _, row := range params.RowClients {
		for id, cvClient := range row.CVRuntimeClients {
			modelReceiverClient, mrOk := row.ModelReceiverClients[id]
			streamHost, shOk := row.StreamHosts[id]
			if !mrOk || !shOk {
				continue
			}
			if modelReceiverClient != nil && cvClient != nil {
				rowClients = append(rowClients, &RowClient{
					addr:            streamHost,
					client:          modelReceiverClient,
					cvRuntimeClient: cvClient,
					syncedModels:    make([]string, 0),
				})
			}
		}
	}

	commanderNode := config.WrapConfigTree(params.CommanderNode)
	commonNode := config.WrapConfigTree(params.CommonNode)
	action := &ChipManagerWatcher{
		EventTrigger: EventTrigger{
			triggerChannel: make(chan bool),
		},
		chipCacheDir:     filepath.Join(dataDir, ChipCacheDirName),
		chipManagerState: params.ChipManagerState,
		commanderNode:    commanderNode,
		commonNode:       commonNode,
		env:              params.Env,
		rowClients:       rowClients,
		stopCtx:          params.StopCtx,
		veselkaClient:    params.VeselkaClient,
		redisClient:      params.RedisClient,

		categoryCollectionState: params.CategoryCollectionState,
		categoryState:           params.CategoryState,

		cleanupChipsCh:           make(chan bool),
		downloadCh:               make(chan bool),
		syncChipsCh:              make(chan bool),
		fetchChipsCh:             make(chan bool),
		updateSyncedChipsCh:      make(chan bool),
		checkForProfileUpdatesCh: make(chan bool),

		chipCacheWriteMutex: &sync.Mutex{},
	}

	logLevelNode := commanderNode.GetNode(logLevelNodeName)
	level := logging.StringToLogLevel(logLevelNode.GetStringValue())
	ChipManagerLogger.SetLevel(level)
	ChipManagerLogger.SetFormatter(logging.DefaultLogFormat)
	action.logger = ChipManagerLogger.WithField("module", "ChipManagerWatcher")
	action.logger.Debug("Initializing ChipManagerWatcher")
	logLevelNode.RegisterCallback(func() {
		level := logging.StringToLogLevel(logLevelNode.GetStringValue())
		ChipManagerLogger.SetLevel(level)
		action.veselkaClient.SetLogLevel(level)
	})

	action.chipManagerState.WriteOnCurrent(func() {
		action.chipManagerState.LocalChips = action.GetLocalChips()
	})

	if err := ChipManagerFs.MkdirAll(action.chipCacheDir, os.ModePerm); err != nil {
		action.logger.Errorf("Couldn't create chip cache folder %v", err)
	}

	go action.checkForProfileUpdatesLoop(params.StopCtx)
	go action.syncChipsFromVeselkaLoop(params.StopCtx)
	go action.downloadChipsLoop(params.StopCtx)
	go action.syncChipsToRowsLoop(params.StopCtx)
	go action.updateSyncedChipIds(params.StopCtx)
	go action.cleanupChipsLoop(params.StopCtx)

	return action
}

func (c *ChipManagerWatcher) triggerCheckForProfileUpdates() {
	select {
	case c.checkForProfileUpdatesCh <- true:
	default: // no block
	}
}

func (c *ChipManagerWatcher) triggerUpdateRowSyncedChipIds() {
	select {
	case c.updateSyncedChipsCh <- true:
	default: // no block
	}
}

func (c *ChipManagerWatcher) TriggerChipSyncFromVeselka() {
	select {
	case c.fetchChipsCh <- true:
	default: // no block
	}
}

func (c *ChipManagerWatcher) TriggerChipSyncToRows() {
	select {
	case c.syncChipsCh <- true:
	default: // no block
	}
}

func (c *ChipManagerWatcher) TriggerDownload() {
	select {
	case c.downloadCh <- true:
	default: // no block
	}
}

func (c *ChipManagerWatcher) triggerCleanup() {
	select {
	case c.cleanupChipsCh <- true:
	default: // no block
	}
}

/**
 * syncChipsFromVeselkaLoop fetches chip metadata from Veselka and updates the
 * local chip metadata files with the latest information. Downloading of the
 * actual chip files is triggered by the downloadChipsLoop. This loops just
 * keeps the metadata files up to date.
 *
 * N.B. This loop calls fetchCategoryProfilesFromRedis which updates the
 * categoryCollectionState, which is used to notify the frontend of changes.
 **/
func (c *ChipManagerWatcher) syncChipsFromVeselkaLoop(stopCtx context.Context) {
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-c.fetchChipsCh:
		}

		c.chipManagerState.WriteOnCurrent(func() {
			c.chipManagerState.LocalChips = c.GetLocalChips()
		})

		collections, categories, err := c.fetchCategoryProfilesFromRedis(stopCtx)
		if err != nil {
			c.logger.WithError(err).Error("failed to fetch category profiles from redis")
			continue
		}

		missingChips := c.detectMissingLocalChips(collections, categories)
		if len(missingChips) == 0 {
			c.logger.Debug("no missing chips detected, skipping sync from Veselka")
			c.chipManagerState.WriteOnCurrent(func() {
				now := time.Now()
				c.chipManagerState.lastVeselkaChipSync = now
			})
			continue
		}

		// batch the missing chips into batches of size chipMetadataBatchSize to avoid overwhelming Veselka
		for batch := range slices.Chunk(missingChips, chipMetadataBatchSize) {
			chips, err := c.veselkaClient.GetImageMetadata(stopCtx, batch)
			if err != nil {
				c.logger.WithError(err).Error("failed to get chip batch from Veselka")
				continue
			}

			for _, veselkaChip := range chips {
				chip := *veselkaChip
				chipMetadata := &chip_manager.Chip{
					ChipDefinition:      chip,
					DownloadedTimestamp: 0,
					LastUsedTimestamp:   0,
				}

				if err := chip_manager.WriteChipMetadata(c.chipCacheDir, chipMetadata, c.chipCacheWriteMutex); err != nil {
					c.logger.WithError(err).Errorf("failed to write chip metadata for: %s", chip.Id)
					continue
				}
				c.logger.Infof("successfully synced chip %s metadata from veselka", chip.Id)
			}

			c.TriggerDownload()
		}

		c.chipManagerState.WriteOnCurrent(func() {
			now := time.Now()
			c.logger.Debugln("last chip sync from Veselka:", now)
			c.chipManagerState.lastVeselkaChipSync = now
		})
	}
}

func (c *ChipManagerWatcher) fetchCategoryProfilesFromRedis(ctx context.Context) (map[string]*category.CategoryCollection, map[string]*category.Category, error) {
	collections, err := c.categoryCollectionState.GetAllCfgs(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get all category collections: %w", err)
	}

	categories, err := c.categoryState.GetAllCfgs(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get all categories: %w", err)
	}

	return collections, categories, nil
}

func (c *ChipManagerWatcher) detectMissingLocalChips(collections map[string]*category.CategoryCollection, categories map[string]*category.Category) []string {
	desiredChips := make([]string, 0)
	for _, collection := range collections {
		for _, categoryId := range collection.CategoryIds {
			category, ok := categories[categoryId]
			if !ok {
				c.logger.Errorf("unable to detect missing chips -- category not found in synced profiles: %s", categoryId)
				continue
			}

			for _, chipId := range category.ChipIds {
				if _, ok := c.chipManagerState.LocalChips[chipId]; !ok {
					found := false
					for _, desiredChip := range desiredChips {
						if desiredChip == chipId {
							found = true
							break
						}
					}

					if !found {
						desiredChips = append(desiredChips, chipId)
					}
				}
			}
		}
	}

	return desiredChips
}

/**
 * downloadChipsLoop downloads the actual chip files from Veselka. The logic of
 * this loop is intentionally limited to
 * 1. Iterate over the localChips map.
 * 2. Verify the metadata of each chip.
 * 3. If any verification fails, trigger chip sync from Veselka.
 * 4. Verify the chip downloads.
 * 5. If any download verification fails, download that chip from S3.
 **/
func (c *ChipManagerWatcher) downloadChipsLoop(stopCtx context.Context) {
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-c.downloadCh:
		}

		addedChips := false
		c.chipManagerState.WriteOnCurrent(func() {
			c.chipManagerState.LocalChips = c.GetLocalChips()
		})

		for _, mappedChip := range c.chipManagerState.LocalChips {
			chip, err := chip_manager.GetVerifyMetadata(filepath.Join(c.chipCacheDir, mappedChip.MetaFilename()))
			if err != nil {
				c.logger.WithError(err).Errorf("failed to get chip metadata: %s", mappedChip.Id)
				c.TriggerChipSyncFromVeselka()
				continue
			}

			if err := chip_manager.VerifyChipDownload(chip, filepath.Join(c.chipCacheDir, chip.Filename())); err != nil {
				addedChips = true
				if err := c.downloadChip(stopCtx, chip); err != nil {
					c.logger.WithError(err).Errorf("failed to download chip: %s", chip.Id)
					continue
				}
			}
		}

		if addedChips {
			c.TriggerChipSyncToRows()
		}

		c.chipManagerState.WriteOnCurrent(func() {
			now := time.Now()
			c.logger.Debugln("last chip download loop:", now)
			c.chipManagerState.lastChipDownload = now
			lastChipDownloadGauge.SetToCurrentTime()
		})
	}
}

func (c *ChipManagerWatcher) downloadChip(ctx context.Context, chip *chip_manager.Chip) error {
	// remove old png file associated with specified chip if it exists
	cachedChipPath := filepath.Join(c.chipCacheDir, chip.Filename())
	c.chipCacheWriteMutex.Lock()
	err := ChipManagerFs.Remove(cachedChipPath)
	c.chipCacheWriteMutex.Unlock()
	if err != nil && !errors.Is(err, os.ErrNotExist) {
		c.logger.Warnf("failed to remove %s - %v", cachedChipPath, err)
		return nil
	}

	return c.downloadChipFromPresignedS3URL(ctx, chip)
}

func (c *ChipManagerWatcher) downloadChipFromPresignedS3URL(ctx context.Context, chip *chip_manager.Chip) error {
	cachedChipPath := filepath.Join(c.chipCacheDir, chip.Filename())
	chipMetaFilePath := filepath.Join(c.chipCacheDir, chip.MetaFilename())
	c.logger.Infof("Starting download of chip: %s", chip.Filename())
	startingSize := int64(0)
	cacheChipTempPath := cachedChipPath + ".tmp"
	if fi, err := ChipManagerFs.Stat(cacheChipTempPath); err == nil {
		startingSize = fi.Size()
	}

	resp, err := http.Get(chip.PreSignedURL)
	if err != nil {
		return fmt.Errorf("request to download presigned url failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// remove metadata file as presigned url likely expired
		c.chipCacheWriteMutex.Lock()
		err = ChipManagerFs.Remove(chipMetaFilePath)
		c.chipCacheWriteMutex.Unlock()
		c.TriggerChipSyncFromVeselka()
		return fmt.Errorf("failed to download chip: HTTP status %d -- forcing metadata refetch", resp.StatusCode)
	}

	if chip.ContentLength <= 0 {
		chip.ContentLength = resp.ContentLength
		c.logger.Warnf("chip %s has missing or invalid ContentLength, using resp length", chip.Id)
	}

	if chip.ContentLength <= 0 {
		return fmt.Errorf("chip %s has missing or invalid ContentLength", chip.Id)
	}

	if chip.ContentLength > maxChipSize {
		return fmt.Errorf("chip %s (size %d) is larger than max permitted chip size of %d", chip.Id, chip.ContentLength, maxChipSize)
	}

	pngFile, err := ChipManagerFs.OpenFile(cacheChipTempPath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, os.ModePerm)
	if err != nil {
		return err
	}
	defer pngFile.Close()

	if err := func() error {
		c.chipCacheWriteMutex.Lock()
		defer c.chipCacheWriteMutex.Unlock()
		for begin := startingSize; begin < chip.ContentLength; begin += chunkSize {
			select {
			case <-ctx.Done():
				return errors.New("download aborted")
			default:
			}

			bytesToRead := chunkSize
			remainingBytes := chip.ContentLength - begin
			if remainingBytes < bytesToRead {
				bytesToRead = remainingBytes
			}

			if _, err = io.CopyN(pngFile, resp.Body, bytesToRead); err != nil && err != io.EOF {
				return err
			}
		}

		if err := chip_manager.VerifyChipDownload(chip, cacheChipTempPath); err != nil {
			if err := ChipManagerFs.Remove(cacheChipTempPath); err != nil {
				c.logger.Warnf("failed to remove: %s - %v", cacheChipTempPath, err)
			}
			return fmt.Errorf("download malformed: %w", err)
		}
		return nil
	}(); err != nil {
		return err
	}

	timeNow := uint64(time.Now().UnixMilli())
	chip.DownloadedTimestamp = timeNow
	chip.LastUsedTimestamp = timeNow
	if err := chip_manager.WriteChipMetadata(c.chipCacheDir, chip, c.chipCacheWriteMutex); err != nil {
		return err
	}

	c.chipCacheWriteMutex.Lock()
	defer c.chipCacheWriteMutex.Unlock()
	return ChipManagerFs.Rename(cacheChipTempPath, cachedChipPath)
}

/**
 * syncChipsToRowsLoop syncs all local chips to all rows.
 **/
func (c *ChipManagerWatcher) syncChipsToRowsLoop(stopCtx context.Context) {
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-c.syncChipsCh:
		}

		rowsUpdated := false
		for _, row := range c.rowClients {
			if !row.connected.Load() {
				continue
			}

			syncedChipIds := row.GetSyncedChipIds()
			for chipId, chip := range c.chipManagerState.LocalChips {
				if !slices.Contains(syncedChipIds, chipId) {
					if err := chip_manager.VerifyChipDownload(chip, filepath.Join(c.chipCacheDir, chip.Filename())); err != nil {
						c.logger.WithError(err).Errorf("failed to verify chip download: %s -- skipping it in sync to row", chipId)
						continue
					}

					rowsUpdated = true
					if err := c.syncChipToRow(row, chip); err != nil {
						c.logger.WithError(err).Errorf("failed to sync chip to row: %s", chipId)
						continue
					}
				}
			}
		}

		if rowsUpdated {
			c.triggerUpdateRowSyncedChipIds()
		}

		c.chipManagerState.WriteOnCurrent(func() {
			now := time.Now()
			c.logger.Debugln("last chip sync to rows:", now)
			c.chipManagerState.lastChipSync = now
			lastChipSyncGauge.SetToCurrentTime()
		})
	}
}

func (c *ChipManagerWatcher) syncChipToRow(row *RowClient, chip *chip_manager.Chip) error {
	c.logger.Infof("syncing chip %s to row %s", chip.Id, row.addr)
	metaBytes, err := json.Marshal(chip)
	if err != nil {
		return fmt.Errorf("failed to marshal chip metadata: %w", err)
	}

	_, err = row.client.DownloadChipMetadata(
		&model_receiver.DownloadChipMetadataRequest{
			ChipId:           chip.Id,
			MetadataContents: metaBytes,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to download chip metadata: %w", err)
	}

	chipFilePath := filepath.Join(c.chipCacheDir, chip.Filename())
	bytes, err := afero.ReadFile(ChipManagerFs, chipFilePath)
	if err != nil {
		return fmt.Errorf("failed to read chip file: %w", err)
	}

	_, err = row.client.DownloadChip(&model_receiver.DownloadChipRequest{
		ChipId:   chip.Id,
		Contents: bytes,
	})
	if err != nil {
		return fmt.Errorf("failed to download chip: %w", err)
	}

	return nil
}

/**
 * updateSyncedChipIds updates the synced chip ids for each row client and
 * reflects any changes in the chipManagerState.
 **/
func (c *ChipManagerWatcher) updateSyncedChipIds(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-c.updateSyncedChipsCh:
		}

		localChips := c.GetLocalChips()
		c.rowClients.UpdateSyncedChipIds()
		syncedChips := make(map[string]*chip_manager.Chip, 0)
		for _, chipID := range c.rowClients.GetSyncedChipIds() {
			if local, ok := localChips[chipID]; ok {
				syncedChips[chipID] = local
			}
		}

		changed := false
		c.chipManagerState.ReadOnCurrent(func() {
			if len(c.chipManagerState.SyncedChips) != len(syncedChips) || len(c.chipManagerState.LocalChips) != len(syncedChips) {
				changed = true
				return
			}
			for key := range c.chipManagerState.SyncedChips {
				if _, ok := syncedChips[key]; !ok {
					changed = true
					return
				}
			}
			for i, chip := range c.chipManagerState.LocalChips {
				if chip != localChips[i] {
					changed = true
					return
				}
			}
		})

		if changed {
			c.chipManagerState.WriteOnCurrent(func() {
				c.chipManagerState.SyncedChips = syncedChips
				c.chipManagerState.LocalChips = localChips
			})
		}
	}
}

/**
 * cleanupChipsLoop removes any chips that are no longer needed. To do this, it
 * iterates over the localChips map and removes any chips that are not present
 * in Categories associated with Category Collections synced from Robot Syncer.
 * cleanupChipsLoop updates the last_used_timestamp of chips that fall under
 * the active category collection to the current time.
 **/
func (c *ChipManagerWatcher) cleanupChipsLoop(stopCtx context.Context) {
	for {
		select {
		case <-stopCtx.Done():
			return
		case <-c.cleanupChipsCh:
		}

		chipsRemoved := make([]string, 0)
		localChips := c.GetLocalChips()
		err := c.updateChipsInUse(stopCtx, localChips)
		if err != nil {
			c.logger.WithError(err).Error("failed to update chips in use -- skipping cleanup")
			continue
		}

		chipsInUse, err := c.listChipsPresentInCategoryProfiles(stopCtx)
		if err != nil {
			c.logger.WithError(err).Error("failed to list chips present in category profiles")
			continue
		}

		for chipId, chip := range c.chipManagerState.LocalChips {
			if _, ok := chipsInUse[chipId]; !ok {
				if chip.LastUsedTimestamp < uint64(time.Now().Add(-chipExpirationThreshold).UnixMilli()) {
					chipFilePath := filepath.Join(c.chipCacheDir, chip.Filename())
					chipMetaFilePath := filepath.Join(c.chipCacheDir, chip.MetaFilename())
					c.chipCacheWriteMutex.Lock()
					err := ChipManagerFs.Remove(chipFilePath)
					if err != nil {
						err = ChipManagerFs.Remove(chipMetaFilePath)
					}
					c.chipCacheWriteMutex.Unlock()
					if err != nil {
						c.logger.WithError(err).Errorf("failed to remove chip: %s", chipId)
						continue
					} else {
						chipsRemoved = append(chipsRemoved, chipId)
						c.logger.Infof("chip %s removed during cleanup", chipId)
					}
				} else {
					c.logger.Debugf("chip %s is not present in category profiles but not expired", chipId)
				}
			}
		}

		for _, rowClient := range c.rowClients {
			if _, err := rowClient.client.RemoveChips(&model_receiver.RemoveChipsRequest{ChipId: chipsRemoved}); err != nil {
				c.logger.WithError(err).Errorf("failed to remove chips %v from row %s", chipsRemoved, rowClient.addr)
			}
		}

		c.chipManagerState.WriteOnCurrent(func() {
			c.chipManagerState.LocalChips = c.GetLocalChips()
		})

		c.chipManagerState.WriteOnCurrent(func() {
			now := time.Now()
			c.logger.Infof("last chip cleanup: %v", now)
			c.chipManagerState.lastChipCleanup = now
			lastChipCleanupGauge.SetToCurrentTime()
		})
	}
}

// A chip is considered in use if it is present in any category
func (c *ChipManagerWatcher) updateChipsInUse(ctx context.Context, localChips map[string]*chip_manager.Chip) error {
	timeNow := uint64(time.Now().UnixMilli())
	chips, err := c.listChipsPresentInCategoryProfiles(ctx)
	if err != nil {
		return err
	}

	for chipId, chip := range chips {
		if chip != nil {
			chip.LastUsedTimestamp = timeNow
			if err := chip_manager.WriteChipMetadata(c.chipCacheDir, chip, c.chipCacheWriteMutex); err != nil {
				c.logger.WithError(err).Errorf("failed to update chip metadata for: %s", chipId)
			}
		}
	}
	return nil
}

func (c *ChipManagerWatcher) listChipsPresentInCategoryProfiles(ctx context.Context) (map[string]*chip_manager.Chip, error) {
	chipsPresent := make(map[string]*chip_manager.Chip, 0)
	collections, categories, err := c.fetchCategoryProfilesFromRedis(ctx)
	if err != nil {
		return nil, err
	}

	for _, collection := range collections {
		for _, categoryId := range collection.CategoryIds {
			category, ok := categories[categoryId]
			if !ok {
				c.logger.Errorf("unable to list chips -- category not found in synced profiles: %s", categoryId)
				continue
			}

			for _, chipId := range category.ChipIds {
				chip, ok := c.chipManagerState.LocalChips[chipId]
				if !ok {
					chipsPresent[chipId] = nil
				}
				chipsPresent[chipId] = chip
			}
		}
	}
	return chipsPresent, nil
}

/**
 * GetLocalChips returns a map of chips present in the local chip cache. This
 * includes any chip with a valid metadata file in the chip cache directory. No
 * verification is done on the actual chip files.
 **/
func (c *ChipManagerWatcher) GetLocalChips() map[string]*chip_manager.Chip {
	chips := make(map[string]*chip_manager.Chip, 0)
	localChipCount := 0
	files, err := afero.ReadDir(ChipManagerFs, c.chipCacheDir)
	if err != nil {
		return chips
	}

	for _, file := range files {
		if !strings.HasSuffix(file.Name(), chip_manager.MetaExt) {
			continue
		}

		metaFilename := filepath.Join(c.chipCacheDir, file.Name())
		chip, err := chip_manager.GetVerifyMetadata(metaFilename)
		if err != nil {
			c.logger.Warnf("invalid metadata file: %s - %v", metaFilename, err)
			c.chipCacheWriteMutex.Lock()
			if err := ChipManagerFs.Remove(metaFilename); err != nil {
				c.logger.Warnf("failed to remove %s - %v", metaFilename, err)
			}
			c.chipCacheWriteMutex.Unlock()
			continue
		}
		localChipCount += 1
		chips[chip.Id] = chip
	}

	localChipCountGauge.Set(float64(localChipCount))
	return chips
}

/**
 * checkForProfileUpdatesLoop assesses the unsynced profile updates status of
 * both the CategoryCollection and Category states. If any updates are detected,
 * it triggers a sync of chips from Veselka, which invokes the download and sync
 * to rows loops after succeeding.
 *
 * If the active Category Collection profile Id changed or there were changes to
 * the categories or chips underlying the active Category Collection, a call is
 * made to CV Runtime to reload the active Category Collection. Note that all we
 * care about in this case is
 * 1. Whether the active Category Collection profile Id changed
 * 2. Whether the chips underlying that Collection's Categories changed
 * 3. Whether the those chips have been synced to all rows
 *
 * Only then can we reload CV Runtime.
 **/
func (c *ChipManagerWatcher) checkForProfileUpdatesLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-c.checkForProfileUpdatesCh:
		}

		err := c.categoryCollectionState.UpdateCVStatus()
		if err != nil {
			c.logger.WithError(err).Error("failed to fetch category collection status from CV Runtime")
			continue
		}
		categoryCollectionProfileState := c.categoryCollectionState.GetProfileUpdateState()
		categoryCollectionUpdates := categoryCollectionProfileState.UnsyncedUpdates || categoryCollectionProfileState.UnsyncedUpdatesToActive
		c.categoryCollectionState.ResetProfileUpdateState() // reset immediately so we catch any new updates

		categoryProfileUpdates := c.categoryState.GetProfileUpdatesFlag()
		c.categoryState.ResetProfileUpdatesFlag() // reset immediately so we catch any new updates

		if categoryCollectionUpdates || categoryProfileUpdates {
			c.TriggerChipSyncFromVeselka()
		}

		ready, err := c.readyForCVReload(ctx)
		if err != nil {
			c.logger.WithError(err).Error("failed to check if ready for CV reload")
			continue
		}
		c.categoryCollectionState.SetCVReloadReady(ready)
		c.chipManagerState.WriteOnCurrent(func() {
			now := time.Now()
			c.chipManagerState.lastProfileUpdateCheck = now
		})
	}
}

// Compares active state as synced from Robot Syncer toi the last known active state running on CV.
func (c *ChipManagerWatcher) changesToActiveCategoryCollection(collections map[string]*category.CategoryCollection, categories map[string]*category.Category) bool {
	// active state as synced from Robot Syncer
	activeCategoryCollectionId := c.categoryCollectionState.GetActiveCfg()
	activeCategoryCollection, ok := collections[activeCategoryCollectionId]
	if !ok {
		return true
	}

	// last known active state running on CV
	var expectedCategoryCollection *category.CategoryCollection
	var expectedCategories map[string]*category.Category
	c.chipManagerState.ReadOnCurrent(func() {
		expectedCategoryCollection = c.chipManagerState.activeCategoryCollection
		expectedCategories = c.chipManagerState.activeCategories
	})

	if expectedCategoryCollection == nil || expectedCategories == nil {
		return true
	}

	if !categoryCollectionEqual(activeCategoryCollection, expectedCategoryCollection) {
		return true
	}

	for _, categoryId := range activeCategoryCollection.CategoryIds {
		category, ok := categories[categoryId]
		if !ok {
			return true
		}

		expectedCategory, ok := expectedCategories[categoryId]
		if !ok {
			return true
		}

		if !categoryEqual(category, expectedCategory) {
			return true
		}
	}

	return false
}

// Checks if chips are synced for CV Runtime to reload the latest Category Collection profile.
func (c *ChipManagerWatcher) readyForCVReload(ctx context.Context) (bool, error) {
	syncedChips := c.chipManagerState.GetSyncedChips()
	activeCategoryCollectionId := c.categoryCollectionState.GetActiveCfg()
	collections, categories, err := c.fetchCategoryProfilesFromRedis(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to fetch category profiles from redis: %w", err)
	}

	// compose map of all chips required under active category collection
	requiredChips := map[string]bool{}
	for _, collection := range collections {
		if collection.Id == activeCategoryCollectionId {
			for _, categoryId := range collection.CategoryIds {
				category, ok := categories[categoryId]
				if !ok {
					c.logger.Infof("category under active collection %s not found in synced profiles: %s", activeCategoryCollectionId, categoryId)
					c.TriggerChipSyncFromVeselka()
					return false, nil
				}

				for _, chipId := range category.ChipIds {
					requiredChips[chipId] = false
				}
			}
		}
	}

	// check if all required chips have been synced to all rows
	missingChips := []string{}
	for chipId, _ := range requiredChips {
		if _, ok := syncedChips[chipId]; !ok {
			missingChips = append(missingChips, chipId)
		}
	}

	if len(missingChips) > 0 {
		c.logger.Infof("missing chips required for CV Runtime reload: %v", missingChips)
		c.TriggerChipSyncFromVeselka()
		c.triggerUpdateRowSyncedChipIds()
		return false, nil
	}

	// update the active Category Collection and Categories
	c.chipManagerState.WriteOnCurrent(func() {
		activeCollection, ok := collections[c.categoryCollectionState.GetActiveCfg()]
		if !ok {
			c.logger.Errorf("active collection %s not found in synced profiles", c.categoryCollectionState.GetActiveCfg())
			return
		}

		var activeCategories = make(map[string]*category.Category)
		for _, categoryId := range activeCollection.CategoryIds {
			category, ok := categories[categoryId]
			if !ok {
				c.logger.Errorf("category %s not found in synced profiles", categoryId)
				return
			}
			activeCategories[categoryId] = category
		}

		c.chipManagerState.activeCategoryCollection = activeCollection
		c.chipManagerState.activeCategories = activeCategories
	})
	return true, nil
}

func (c *ChipManagerWatcher) Action() {
	if time.Since(c.chipManagerState.lastVeselkaChipSync) > veselkaChipSyncInterval {
		c.TriggerChipSyncFromVeselka()
	}

	if time.Since(c.chipManagerState.lastChipDownload) > chipDownloadInterval {
		c.TriggerDownload()
	}

	if time.Since(c.chipManagerState.lastChipSync) > chipSyncToRowsInterval {
		c.TriggerChipSyncToRows()
		c.triggerUpdateRowSyncedChipIds()
	}

	if time.Since(c.chipManagerState.lastChipCleanup) > chipCleanupInterval {
		c.triggerCleanup()
	}

	if time.Since(c.chipManagerState.lastProfileUpdateCheck) > checkProfileUpdatesInterval {
		c.triggerCheckForProfileUpdates()
	}
}

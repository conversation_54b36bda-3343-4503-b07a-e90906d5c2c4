package state

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const ActiveDefByNameKey = "banding/active_def"
const ActiveDefUUIDKey = "banding/active_def_uuid"
const BandingDefsByUUIDKey = "banding/banding_defs_uuid"
const BandingEnabledKey = "banding/enabled"
const DynamicBandingEnabledKey = "banding/dynamic_banding_selected"

const InitTimeout = 5

type BandingState struct {
	ManagedStateImpl

	redis *redis.Client

	activeBandingDefUUID    string
	activeBandingDefName    string
	isBandingEnabled        bool
	isDynamicBandingEnabled bool

	jobsState *JobsState
	rows      map[int]*rows.RowClients

	localToSyncUpdateCallback func(string, frontend.ProfileType, bool) error
	localToSyncDeleteCallback func(string, frontend.ProfileType, bool) error
}

func NewBandingState(redis *redis.Client, jobsState *JobsState, rowClients map[int]*rows.RowClients) *BandingState {
	st := &BandingState{
		redis:                     redis,
		jobsState:                 jobsState,
		rows:                      rowClients,
		localToSyncUpdateCallback: nil,
		localToSyncDeleteCallback: nil,
	}
	st.initialize()
	st.init()
	return st
}

func (s *BandingState) init() {
	uuid, err := s.redis.ReadString(ActiveDefUUIDKey, "")
	if err != nil {
		logrus.Errorf("Banding: could not read active banding def: %v", err)
		return
	}

	def, err := s.LoadBandingDef(uuid)
	if err != nil {
		logrus.Errorf("Banding: could not read active banding def: %v", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), InitTimeout*time.Second)
	defer cancel()

	isBandingEnabled, err := s.isEnabled(ctx, BandingEnabledKey)
	if err != nil {
		logrus.Errorf("Banding: could not read %v: %v", BandingEnabledKey, err)
		return
	}

	isDynamicBandingEnabled, err := s.isEnabled(ctx, DynamicBandingEnabledKey)
	if err != nil {
		logrus.Errorf("Banding: could not read %v: %v", DynamicBandingEnabledKey, err)
		return
	}

	s.activeBandingDefName = def.Name
	s.activeBandingDefUUID = uuid
	s.isBandingEnabled = isBandingEnabled
	s.isDynamicBandingEnabled = isDynamicBandingEnabled
}

func (b *BandingState) isEnabled(ctx context.Context, key string) (bool, error) {
	val, err := b.redis.ReadInt64WithContext(ctx, key, 0)
	if err != nil {
		return false, err
	}

	var en bool = false
	if val == 1 {
		en = true
	}

	return en, nil
}

func (s *BandingState) GetIsBandingEabled() (isBandingEnabled bool) {
	s.ReadOnCurrent(func() {
		isBandingEnabled = s.isBandingEnabled
	})
	return
}

func (s *BandingState) GetIsDynamicBandingEnabled() (isDynamicBandingEnabled bool) {
	s.ReadOnCurrent(func() {
		isDynamicBandingEnabled = s.isDynamicBandingEnabled
	})
	return
}

func (s *BandingState) setIsEnabled(ctx context.Context, enabled bool, key string, setFunc func()) error {
	var e error = nil
	var val int64 = 0
	if enabled {
		val = 1
	}
	s.WriteOnCurrent(func() {
		err := s.redis.WriteInt64WithContext(ctx, key, val)
		if err != nil {
			e = err
			return
		}

		setFunc()

		s.NotifyOnBandingChange()
	})
	return e
}

func (s *BandingState) SetIsBandingEnabled(ctx context.Context, enabled bool) error {
	return s.setIsEnabled(ctx, enabled, BandingEnabledKey, func() {
		s.isBandingEnabled = enabled
	})
}

func (s *BandingState) SetIsDynamicBandingEnabled(ctx context.Context, enabled bool) error {
	return s.setIsEnabled(ctx, enabled, DynamicBandingEnabledKey, func() {
		s.isDynamicBandingEnabled = enabled
	})
}

func (s *BandingState) GetActiveBandingDef() (uuid string, name string) {
	s.ReadOnCurrent(func() {
		name = s.activeBandingDefName
		uuid = s.activeBandingDefUUID
	})
	return
}

func (s *BandingState) GetNextBandingState(ctx context.Context, ts int64) (*frontend.GetNextBandingStateResponse, error) {
	resp := &frontend.GetNextBandingStateResponse{}
	var e error

	result := s.ReadOnNext(ctx, ts, func() {
		respTs := &frontend.Timestamp{
			TimestampMs: s.GetTimestampMs(),
		}
		bandingDefs, err := s.LoadBandingDefs()
		if err != nil {
			e = err
			return
		}
		resp.Ts = respTs
		resp.BandingDefs = bandingDefs
		resp.ActiveDefUUID = s.activeBandingDefUUID
		resp.IsBandingEnabled = s.isBandingEnabled
		resp.IsDynamicBandingEnabled = s.isDynamicBandingEnabled
	})

	if e != nil {
		return nil, e
	} else if !result {
		return nil, status.Error(codes.Aborted, "Context Cancelled before Tasks Retrieved")
	}

	return resp, nil
}

func (s *BandingState) FindBandingDefByName(name string) (*frontend.BandingDef, error) {
	all, err := s.redis.HGetAll(BandingDefsByUUIDKey)
	if err != nil {
		return nil, err
	}
	for _, j := range all {
		def := &frontend.BandingDef{}
		json.Unmarshal([]byte(j), def)

		if def.Name == name {
			return def, nil
		}
	}
	return nil, fmt.Errorf("Banding: def %v not found", name)
}

func (s *BandingState) SetActiveBandingDef(uuid string, name string) error {
	var e error = nil
	s.WriteOnCurrent(func() {
		err := s.redis.WriteString(ActiveDefUUIDKey, uuid)
		if err != nil {
			e = err
			return
		}

		s.activeBandingDefName = name
		s.activeBandingDefUUID = uuid

		s.jobsState.UpdateActiveProfile(uuid, name, frontend.ProfileType_BANDING)
	})
	return e
}

func (s *BandingState) saveBandingDef(def *frontend.BandingDef, setActive bool) error {
	j, err := json.Marshal(def)
	if err != nil {
		logrus.WithError(err).Error("Banding: Could not marshal banding_def to json")
		return err
	}

	err = s.redis.HSet(BandingDefsByUUIDKey, def.Uuid, string(j))
	if err != nil {
		return err
	}

	if setActive {
		err = s.SetActiveBandingDef(def.Uuid, def.Name)
		if err != nil {
			return err
		}
		s.NotifyOnBandingChange()
	} else {
		active, _ := s.GetActiveBandingDef()
		if active == def.Uuid {
			s.NotifyOnBandingChange()
		}
	}

	return nil
}
func (s *BandingState) SaveBandingDef(def *frontend.BandingDef, setActive bool) error {
	err := s.saveBandingDef(def, setActive)
	if err == nil {
		err = s.callUpdateCB(def.Uuid)
	}
	return err
}

func (s *BandingState) LoadBandingDef(uuid string) (*frontend.BandingDef, error) {
	j, err := s.redis.HGet(BandingDefsByUUIDKey, uuid)
	if err != nil {
		return nil, err
	}
	def := &frontend.BandingDef{}
	err = json.Unmarshal([]byte(j), def)
	if err != nil {
		return nil, err
	}
	return def, nil
}

func (s *BandingState) NotifyOnBandingChange() {
	for idx, r := range s.rows {
		go func(idx int, r *rows.RowClients) {
			err := r.WeedTrackingClient.UpdateBands()
			if err != nil {
				logrus.WithError(err).Errorf("Banding: Could not notify weed_tracking on row %v on changes in banding", idx)
			}
		}(idx, r)
	}
}

func (s *BandingState) LoadBandingDefs() ([]*frontend.BandingDef, error) {
	res := make([]*frontend.BandingDef, 0)

	m, err := s.redis.HGetAll(BandingDefsByUUIDKey)
	if err != nil {
		return nil, err
	}

	for _, v := range m {
		var def frontend.BandingDef
		err = json.Unmarshal([]byte(v), &def)
		if err != nil {
			logrus.WithError(err).Errorf("Banding: Could not unmarshal banding_def: %v", v)
			return nil, err
		}

		res = append(res, &def)
	}
	return res, nil
}

func (s *BandingState) deleteBandingDef(uuid string) error {
	defs, err := s.LoadBandingDefs()
	if err != nil {
		return err
	}

	if len(defs) <= 1 {
		return errors.New("Cannot delete last banding def. Please create a new first.")
	}

	err = s.redis.HDel(BandingDefsByUUIDKey, uuid)
	if err != nil {
		return err
	}

	activeUUID, _ := s.GetActiveBandingDef()
	if uuid == activeUUID {
		// For app this will always be true, but not for CLI
		for _, def := range defs {
			if def.Uuid == uuid {
				continue
			}
			err = s.SetActiveBandingDef(def.Uuid, def.Name)
			if err != nil {
				return err
			}
			break
		}
		s.NotifyOnBandingChange()
	}
	return nil
}
func (s *BandingState) DeleteBandingDef(uuid string) error {
	err := s.deleteBandingDef(uuid)
	if err == nil {
		err = s.callDeleteCB(uuid)
	}
	return err
}

func (s *BandingState) DeleteProfileFromSync(id string) error {
	err := s.deleteBandingDef(id)
	if err == nil {
		s.WriteOnCurrent(func() {})
	}
	return err
}
func (s *BandingState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *portal.GetProfileResponse_Banding:
		activeUuid, _ := s.GetActiveBandingDef()
		err := s.saveBandingDef(r.Banding, activeUuid == r.Banding.Uuid)
		if err != nil {
			return true, err
		}
		s.WriteOnCurrent(func() {})
		return true, nil
	default:
		return false, nil
	}
}
func (s *BandingState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_Banding:
		activeUuid, _ := s.GetActiveBandingDef()
		err := s.saveBandingDef(r.Banding, activeUuid == r.Banding.Uuid)
		if err != nil {
			return true, err
		}
		s.WriteOnCurrent(func() {})
		return true, nil
	default:
		return false, nil
	}
}
func (s *BandingState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	bandingDef, err := s.LoadBandingDef(id)
	if err != nil {
		return err
	}
	req.Profile = &portal.UploadProfileRequest_Banding{
		Banding: bandingDef,
	}
	return nil
}
func (s *BandingState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	bandingDef, err := s.LoadBandingDef(id)
	if err != nil {
		return err
	}
	req.Profile = &robot_syncer.UploadProfileRequest_Banding{
		Banding: bandingDef,
	}
	return nil
}
func (s *BandingState) SetActiveProfileFromSync(id string) error {
	banding, err := s.LoadBandingDef(id)
	if err == nil {
		err = s.SetActiveBandingDef(id, banding.Name)
	}
	return err
}
func (s *BandingState) AddUpdateLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	s.Lock()
	defer s.Unlock()
	s.localToSyncUpdateCallback = cb
}
func (s *BandingState) AddDeleteLocalToSyncCallback(cb func(string, frontend.ProfileType, bool) error) {
	s.Lock()
	defer s.Unlock()
	s.localToSyncDeleteCallback = cb
}
func (s *BandingState) callDeleteCB(id string) error {
	s.RLock()
	cb := s.localToSyncDeleteCallback
	s.RUnlock()
	if cb != nil {
		return cb(id, frontend.ProfileType_BANDING, false)
	}
	return nil
}
func (s *BandingState) callUpdateCB(id string) error {
	s.RLock()
	cb := s.localToSyncUpdateCallback
	s.RUnlock()
	if cb != nil {
		return cb(id, frontend.ProfileType_BANDING, false)
	}
	return nil
}
func (s *BandingState) GetActiveProfileForJob() (string, string, frontend.ProfileType) {
	id, name := s.GetActiveBandingDef()
	return id, name, frontend.ProfileType_BANDING
}

type RowData struct {
	Dets *weed_tracking.DiagnosticsSnapshot
}

type BandingVisualizationState struct {
	RowState        *BandingVisualizationRowState
	ReqState        *BandingVisualizationRequestState
	DimensionsState *BandingVisualizationDimensionsState
}

// state of cameras
type BandingVisualizationRowState struct {
	ManagedStateImpl
	Rows map[int]*RowData
}

// when was the last request from ui received?
type BandingVisualizationRequestState struct {
	ManagedStateImpl
	LastReqTsSec int64
}

type BandingVisualizationDimensionsState struct {
	ManagedStateImpl
	Dimensions map[int]*aimbot.GetDimensionsResponse
}

func NewBandingVisualizationState() *BandingVisualizationState {
	s := &BandingVisualizationState{
		RowState: &BandingVisualizationRowState{
			Rows: make(map[int]*RowData, 0),
		},
		ReqState: &BandingVisualizationRequestState{},
		DimensionsState: &BandingVisualizationDimensionsState{
			Dimensions: make(map[int]*aimbot.GetDimensionsResponse, 0),
		},
	}
	s.RowState.initialize()
	s.ReqState.initialize()
	s.DimensionsState.initialize()
	return s
}

type BandingVisualizationStateWatcher struct {
	EventTrigger
	bvState *BandingVisualizationState
	rows    map[int]*rows.RowClients
}

func NewBandingVisualizationStateWatcher(bvState *BandingVisualizationState, rowClients map[int]*rows.RowClients) *BandingVisualizationStateWatcher {
	return &BandingVisualizationStateWatcher{
		bvState: bvState,
		rows:    rowClients,
	}
}

func sortDetections(d *weed_tracking.DiagnosticsSnapshot) {
	sort.SliceStable(d.Trajectories, func(i, j int) bool {
		dd1 := d.Trajectories[i]
		dd2 := d.Trajectories[j]
		return dd1.XMm < dd2.XMm || ((dd1.XMm == dd2.XMm) && (dd1.YMm < dd2.YMm))
	})
}

func sortBands(d *weed_tracking.DiagnosticsSnapshot) {
	sort.SliceStable(d.Bands, func(i, j int) bool {
		dd1 := d.Bands[i]
		dd2 := d.Bands[j]
		return dd1.OffsetMm < dd2.OffsetMm
	})
}

func detectionsEqual(d1 *weed_tracking.DiagnosticsSnapshot, d2 *weed_tracking.DiagnosticsSnapshot) bool {
	if (d1 == nil) && (d2 == nil) {
		return true
	}
	if (d1 == nil) || (d2 == nil) {
		return false
	}

	if d1.Bands != nil && d2.Bands != nil {
		if len(d1.Bands) != len(d2.Bands) {
			return false
		}
		sortBands(d1)
		sortBands(d2)
		for i, b1 := range d1.Bands {
			b2 := d2.Bands[i]
			if b1.OffsetMm != b2.OffsetMm || b1.WidthMm != b2.WidthMm {
				return false
			}
		}
	} else {
		if d1.Bands != nil && d2.Bands == nil {
			return false
		}
		if d1.Bands == nil && d2.Bands != nil {
			return false
		}
	}

	if d1.Trajectories != nil && d2.Trajectories != nil {
		if len(d1.Trajectories) != len(d2.Trajectories) {
			return false
		}
		sortDetections(d1)
		sortDetections(d2)

		for i, dd1 := range d1.Trajectories {
			dd2 := d2.Trajectories[i]
			if dd1.XMm != dd2.XMm || dd1.YMm != dd2.YMm || dd1.ZMm != dd2.ZMm || dd1.IsWeed != dd2.IsWeed {
				return false
			}
		}
	} else {
		if d1.Trajectories == nil && d2.Trajectories != nil {
			return false
		}
		if d1.Trajectories != nil && d2.Trajectories == nil {
			return false
		}
	}

	return true
}

func (w *BandingVisualizationStateWatcher) Action() {
	var lastTsSec int64
	w.bvState.ReqState.ReadOnCurrent(func() {
		lastTsSec = w.bvState.ReqState.LastReqTsSec
	})

	// if last request from UI came more than a minute ago,
	// stop spamming aimbot with detections request
	if time.Now().Unix()-lastTsSec > 60 {
		return
	}
	rowDatas := make(map[int]*RowData)
	w.bvState.RowState.ReadOnCurrent(func() {
		for row_id := range w.rows {
			rowDatas[row_id] = w.bvState.RowState.Rows[row_id]
		}
	})

	var lock sync.RWMutex
	var wg sync.WaitGroup
	for row_id, row := range w.rows {
		wg.Add(1)
		go func(row *rows.RowClients, row_id int) {
			defer wg.Done()

			var oldDets *weed_tracking.DiagnosticsSnapshot
			lock.RLock()
			rowData := rowDatas[row_id]
			if rowData != nil {
				oldDets = rowData.Dets
			}
			lock.RUnlock()

			resp, err := row.WeedTrackingClient.GetCurrentTrajectories()
			if err != nil {
				logrus.WithError(err).Debugf("Banding: Could not get detections for row %v", row_id)
				if oldDets != nil {
					lock.Lock()
					delete(rowDatas, row_id)
					lock.Unlock()
				}
			} else {
				lock.Lock()
				rowDatas[row_id] = &RowData{Dets: resp}
				lock.Unlock()
			}
		}(row, row_id)
	}

	wg.Wait()

	w.bvState.RowState.WriteOnCurrent(func() {
		w.bvState.RowState.Rows = rowDatas
	})
}

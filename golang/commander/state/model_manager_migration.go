package state

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/spf13/afero"
)

// Migration to new artifact cache location
//
// This code can be removed once robots have all been upgraded to a version where
// customers are no longer able to downgrade to before model manager switched to using
// the new artifact cache location.
// That is likely the version after that in which the upgrade occurred.
//
// This migration is necessary because the model artifacts cache folder was moved from
// /data/model_manager/model_cache to /data/model_manager/model_artifacts. Models are
// copied 1:1 to artifacts in the new location so they can be used without re-downloading
// from veselka. This migration is idempotent and can be re-run without data loss. Here
// is how it works:
//
//  1. Iterate over all model metadata files in the old model cache directory.
//  2. Verify each metadata file.
//  3. If the metadata file does not have associated model artifacts, copy the model file
//     to a temp file in the new artifact cache location, then rename.
//  4. Verify the artifact downloaded properly.
//  5. Update the metadata file with the new artifact information.
//  6. Sync moved artifacts down to row computers.
func getModelFromMetadata(metaFilePath string) (*model_manager.Model, error) {
	metaBytes, err := afero.ReadFile(ModelManagerFs, metaFilePath)
	if err != nil {
		return nil, err
	}
	model := &model_manager.Model{}
	if err := json.Unmarshal(metaBytes, model); err != nil {
		return nil, err
	}
	if model.ID == "" || model.Type == "" {
		return nil, fmt.Errorf("invalid metadata [%s] id and type are required", metaFilePath)
	}

	for _, artifact := range model.ModelArtifacts {
		if artifact.Checksum == "" || artifact.ComputeCapability == "" || artifact.ModelID == "" || artifact.TensorRTVersion == "" {
			return nil, fmt.Errorf("metadata contains incomplete artifact [%s]", artifact.ModelManagerArtifactID())
		}
	}

	return model, nil
}

func (m *ModelManagerWatcher) copyModelsToArtifactsOnUpgrade(ctx context.Context) error {
	m.logger.Info("** Copying existing models to new artifact cache location")

	initialSupportedComputeCapability := "8.6"
	initialSupportedTensorRTVersion := "*******"

	if err := ModelManagerFs.MkdirAll(m.modelArtifactCacheDir, os.ModePerm); err != nil {
		return fmt.Errorf("Couldn't create model artifact cache folder %v", err)
	}

	metadataFiles, err := afero.ReadDir(ModelManagerFs, m.modelMetadataCacheDir)
	if err != nil {
		return fmt.Errorf("Couldn't read model metadata cache folder %v", err)
	}

	for _, metadataFile := range metadataFiles {
		// skip all non metadata files
		if !strings.HasSuffix(metadataFile.Name(), model_manager.MetaExt) {
			continue
		}

		m.logger.Infof("processing metadata file: %s", metadataFile.Name())

		// skip if metadata is invalid or can't be parsed
		metaFilePath := filepath.Join(m.modelMetadataCacheDir, metadataFile.Name())
		model, err := getModelFromMetadata(metaFilePath)
		if err != nil {
			m.logger.Warnf("skipping invalid metadata file: %s - %v", metaFilePath, err)
			continue
		}

		// skip if associated model artifacts already exist
		if model.ModelArtifacts != nil && len(model.ModelArtifacts) > 0 {
			m.logger.Infof("model %s already has associated artifacts, skipping", model.ID)
			continue
		}

		modelArtifacts := make([]veselka.ModelArtifact, 0)
		modelArtifact := veselka.ModelArtifact{
			// use checksum of original model
			Checksum:          model.Checksum,
			ComputeCapability: initialSupportedComputeCapability,
			ContentLength:     -1,
			TensorRTVersion:   initialSupportedTensorRTVersion,
			ModelID:           model.ID,
		}
		modelArtifacts = append(modelArtifacts, modelArtifact)

		// check for associated model artifact file
		cachedModelArtifactPath := filepath.Join(m.modelArtifactCacheDir, modelArtifact.Filename())
		if ok, err := afero.Exists(ModelManagerFs, cachedModelArtifactPath); !ok && err == nil {
			// if artifact file doesn't exist, copy it from old location
			err := copyFile(cachedModelArtifactPath, filepath.Join(m.modelMetadataCacheDir, model.ID+model_manager.ModelExt))
			if err != nil {
				m.logger.Errorf("failed to copy model artifact to %s: %v", cachedModelArtifactPath, err)
				continue
			}

			if err := model_manager.VerifyModelArtifactDownload(modelArtifact, cachedModelArtifactPath); err != nil {
				m.logger.Errorf("failed to verify copied model artifact %s: %v", modelArtifact.ModelManagerArtifactID(), err)
				continue
			}
		} else {
			if err != nil {
				m.logger.Errorf("failed to check if %s exists: %v", cachedModelArtifactPath, err)
			} else {
				m.logger.Infof("model artifact %s already exists", modelArtifact.ModelManagerArtifactID())
			}
		}

		// update model metadata with artifacts list
		var writeErr error
		m.modelState.WriteOnCurrent(func() {
			model.ModelArtifacts = modelArtifacts
			if err := m.writeModelMetadata(model); err != nil {
				writeErr = err
				return
			}
			m.modelState.LocalModels[model.ID] = model
		})
		if writeErr != nil {
			m.logger.Errorf("failed to write updated model metadata: %v", writeErr)
			continue
		}

		// sync model metadata and artifacts down to row computers
		for _, row := range m.rows {
			m.logger.Infof("syncing model %s to row %s", model.ID, row.addr)

			metaFile, err := afero.ReadFile(ModelManagerFs, filepath.Join(m.modelMetadataCacheDir, model.MetaFilename()))
			if err != nil {
				m.logger.Errorf("failed to read metadata: %v", err)
				continue
			}

			modelMetadata := model_manager.Model{}
			if err := json.Unmarshal(metaFile, &modelMetadata); err != nil {
				m.logger.Errorf("failed to unmarshal model metadata from file:%s - %v", model.MetaFilename(), err)
				continue
			}

			modelArtifactFileNameMapping := make(map[string]string, 0)
			for _, artifact := range modelMetadata.ModelArtifacts {
				// We rename artifact files before writing them to the row computer.
				// Artifact files are written to a folder named after the associated
				// model ID and named sm{compute_capability}_trt{tensorrt_version}.trt
				modelArtifactFileName := model_manager.ComposeModelArtifactFilename(artifact.ComputeCapability, artifact.TensorRTVersion)
				modelArtifactFileNameMapping[modelArtifactFileName] = filepath.Join(m.modelArtifactCacheDir, artifact.Filename())
			}

			if len(modelArtifactFileNameMapping) == 0 {
				m.logger.Warnf("no model artifacts associated with model: %s, skipping sync", model.ID)
			} else {
				if _, err := row.client.DownloadModelMetadata(&model_receiver.DownloadModelMetadataRequest{ModelId: model.ID, MetadataContents: metaFile}); err != nil {
					m.logger.Errorf("failed to push model %s metadata: %v", model.ID, err)
					continue
				}

				for artifactFileDestName, localArtifactName := range modelArtifactFileNameMapping {
					modelArtifactContents, err := afero.ReadFile(ModelManagerFs, localArtifactName)
					if err != nil {
						m.logger.Errorf("failed to read model: %v", err)
						continue
					}

					if _, err := row.client.DownloadModelArtifact(&model_receiver.DownloadModelArtifactRequest{ModelId: model.ID, ArtifactName: artifactFileDestName, ArtifactContents: modelArtifactContents}); err != nil {
						m.logger.Errorf("failed to push model %s artifact: %v", model.ID, err)
						continue
					}
				}
			}
		}
	}

	return nil
}

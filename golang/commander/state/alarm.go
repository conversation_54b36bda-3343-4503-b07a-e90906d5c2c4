package state

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_definition"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/carbonrobotics/robot/golang/lib/translation"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

const subsystem = "commander"
const alarmLogKey = "alarm_log/alarms"
const alarmLogSyncKey = "alarm_log/sync_queue"

var (
	alarmsCurrentTotalGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "alarms_current_count",
		Help:      "the current alarm count",
	})
	alarmResetTotal = promauto.NewCounter(prometheus.CounterOpts{
		Subsystem: subsystem,
		Name:      "alarm_reset_total",
		Help:      "total count of alarm resets",
	})
)

type AlarmType int

const (
	AlarmTypeSoftware AlarmType = iota
	AlarmTypeHost
	AlarmTypeScanner
	AlarmTypeCameras
	AlarmTypeBoards
	AlarmTypeImplement
	AlarmTypeModel
	AlarmTypeDataStorage
	AlarmTypeWeedTracking
	AlarmTypeCVRuntime
	AlarmTypeAuthentication
	AlarmTypeTractor
)

var IntToAlarmType = map[int]AlarmType{
	0:  AlarmTypeSoftware,
	1:  AlarmTypeHost,
	2:  AlarmTypeScanner,
	3:  AlarmTypeCameras,
	4:  AlarmTypeBoards,
	5:  AlarmTypeImplement,
	6:  AlarmTypeModel,
	7:  AlarmTypeDataStorage,
	8:  AlarmTypeWeedTracking,
	9:  AlarmTypeCVRuntime,
	10: AlarmTypeAuthentication,
	11: AlarmTypeTractor,
}

type AlarmLogKey struct {
	Identifier string
	Timestamp  int64
}

type AlarmState struct {
	ManagedStateImpl
	Alarms                 map[AlarmType]map[string]*alarms.Alarm
	alarmTypesByIdentifier map[string]AlarmType
	IgnoreList             map[string]bool

	AlarmLog    []*frontend.AlarmRow
	AlarmLogMap map[AlarmLogKey]*frontend.AlarmRow
	redis       redis.RedisClient

	configNodeCommander *config.ConfigTree

	AutofixInProgress   bool
	AutofixErrorMessage string
}

type AlarmWatcher struct {
	EventTrigger
	alarmState     *AlarmState
	flickerFilterS *config.ConfigTree
}

func NewAlarmWatcher(alarmState *AlarmState, commanderNode *config.ConfigTree) *AlarmWatcher {
	action := &AlarmWatcher{
		alarmState:     alarmState,
		flickerFilterS: commanderNode.GetNode("alarm/flicker_filter_s"),
	}
	action.triggerChannel = make(chan bool)
	return action
}

// Returns alarm identifiers and types that have passed the flicker filter period.
func (w *AlarmWatcher) getNewlyStableAlarms(nowMs int64, flickerFilterS uint64) (map[string]*alarms.Alarm, map[string]AlarmType) {
	stableAlarms := make(map[string]*alarms.Alarm)
	stableAlarmTypes := make(map[string]AlarmType)

	for _, alarmMap := range w.alarmState.Alarms {
		for alarmKey, alarmStruct := range alarmMap {
			if !alarmStruct.Valid && alarmStruct.TimestampMs+int64(flickerFilterS*1000) < nowMs {
				stableAlarms[alarmKey] = alarmStruct
				stableAlarmTypes[alarmKey] = w.alarmState.alarmTypesByIdentifier[alarmKey]
			}
		}
	}
	return stableAlarms, stableAlarmTypes
}

// Returns a map of module IDs that currently have host alarms.
func (w *AlarmWatcher) getModuleIdsWithHostAlarms() map[uint32]bool {
	moduleIdsWithHostAlarms := make(map[uint32]bool)
	if hostAlarmsMap, ok := w.alarmState.Alarms[AlarmTypeHost]; ok {
		for _, hostAlarmStruct := range hostAlarmsMap {
			if hostAlarmStruct.ModuleId != nil {
				moduleIdsWithHostAlarms[*hostAlarmStruct.ModuleId] = true
			}
		}
	}
	return moduleIdsWithHostAlarms
}

// Checks if a scanner alarm should be suppressed due to an active host alarm on the same module.
func shouldSuppressScannerAlarm(alarmType AlarmType, alarmStruct *alarms.Alarm, moduleIdsWithHostAlarms map[uint32]bool) bool {
	if alarmType == AlarmTypeScanner && alarmStruct.ModuleId != nil {
		moduleId := *alarmStruct.ModuleId
		if _, found := moduleIdsWithHostAlarms[moduleId]; found {
			logrus.Infof("Suppressing scanner alarm %s (Module %d) due to host alarm on the same module",
				alarmStruct.Identifier, moduleId)
			return true
		}
	}
	return false
}

func (w *AlarmWatcher) makeAlarmsValid(flickerFilterS uint64) {
	nowMs := time.Now().UnixNano() / int64(time.Millisecond)

	var alarmsToMakeValid []*alarms.Alarm
	w.alarmState.ReadOnCurrent(func() {
		newlyStableAlarms, alarmTypesByIdentifier := w.getNewlyStableAlarms(nowMs, flickerFilterS)
		if len(newlyStableAlarms) == 0 {
			return
		}
		moduleIdsWithHostAlarms := w.getModuleIdsWithHostAlarms()

		alarmsToMakeValid = make([]*alarms.Alarm, 0, len(newlyStableAlarms))
		for alarmIdentifier, alarmStruct := range newlyStableAlarms {
			alarmType, typeOk := alarmTypesByIdentifier[alarmIdentifier]
			if !typeOk {
				logrus.Errorf("Alarm type not found for identifier: %s", alarmIdentifier)
				continue
			}

			if shouldSuppressScannerAlarm(alarmType, alarmStruct, moduleIdsWithHostAlarms) {
				continue
			}
			alarmsToMakeValid = append(alarmsToMakeValid, alarmStruct)
		}
	})

	if len(alarmsToMakeValid) == 0 {
		return
	}

	w.alarmState.WriteOnCurrent(func() {
		for _, alarmStruct := range alarmsToMakeValid {
			alarmStruct.Valid = true
			w.alarmState.addAlarmToLog(alarmStruct)
		}
	})
}

func (w *AlarmWatcher) Action() {
	w.makeAlarmsValid(w.flickerFilterS.GetUIntValue())
}

func (a *AlarmState) Acknowledge(identifier string) error {
	var returnErr error = nil
	a.WriteOnCurrent(func() {
		alarmType, ok := a.alarmTypesByIdentifier[identifier]

		if !ok {
			returnErr = fmt.Errorf("Alarm type not found for identifier: %s", identifier)
			return
		}

		alarmsByIdentifier := a.GetAlarmsByIdentifier(alarmType)

		if alarm, ok := alarmsByIdentifier[identifier]; ok {
			alarm.Acknowledged = true
		} else {
			returnErr = fmt.Errorf("Alarms not found for alarm type: %s", identifier)
		}
	})

	return returnErr
}

func MakeAlarmIdentifier(alarmType AlarmType, line string) string {
	return fmt.Sprintf("Prefix%d_%s", alarmType, line)
}

func (a *AlarmState) getAlarmsByIdentifierForWrite(alarmType AlarmType) map[string]*alarms.Alarm {
	// Must be called under WRITE lock
	alarmsByIdentifier, exists := a.Alarms[alarmType]
	if !exists || alarmsByIdentifier == nil {
		alarmsByIdentifier = make(map[string]*alarms.Alarm)
		a.Alarms[alarmType] = alarmsByIdentifier
	}

	return alarmsByIdentifier
}

func (a *AlarmState) GetAlarmsByIdentifier(alarmType AlarmType) map[string]*alarms.Alarm {
	// Must be called under lock
	alarmsByIdentifier, exists := a.Alarms[alarmType]
	if !exists || alarmsByIdentifier == nil {
		return make(map[string]*alarms.Alarm)
	}

	return alarmsByIdentifier
}

// Creates new alarms, updates existing ones, and removes alarms that are no longer present. New
// alarms are initially created as invalid and will be enabled by AlarmWatcher after a period
// to avoid flickering.
func (a *AlarmState) ReconcileAlarms(alarmType AlarmType, generatedAlarms []*alarms.Alarm) {
	a.WriteOnCurrent(func() {
		if a.AutofixInProgress {
			return
		}

		alarmsByIdentifier := a.getAlarmsByIdentifierForWrite(alarmType)
		toKeep := map[string]bool{}
		newAndUpdatedAlarms := []*alarms.Alarm{}

		for _, al := range generatedAlarms {
			if _, ok := a.IgnoreList[al.Identifier]; ok {
				al.Level = alarms.AlarmLevelHidden
				al.Description = al.Description + " (Ignored via Override)"
			}
			toKeep[al.Identifier] = true

			if existing, ok := alarmsByIdentifier[al.Identifier]; ok {
				if al.Description != existing.Description || al.AlarmCode != existing.AlarmCode || al.Level != existing.Level || al.Impact != existing.Impact {
					al.TimestampMs = existing.TimestampMs
					al.Valid = existing.Valid
					al.AutofixAttempted = existing.AutofixAttempted
					newAndUpdatedAlarms = append(newAndUpdatedAlarms, al)
				}
			} else {
				newAndUpdatedAlarms = append(newAndUpdatedAlarms, al)
			}
		}

		sort.SliceStable(newAndUpdatedAlarms, func(i, j int) bool {
			return newAndUpdatedAlarms[i].TimestampMs < newAndUpdatedAlarms[j].TimestampMs
		})

		for _, al := range newAndUpdatedAlarms {
			a.alarmTypesByIdentifier[al.Identifier] = alarmType
			alarmsByIdentifier[al.Identifier] = al

			alarmLogKey := AlarmLogKey{Identifier: al.Identifier, Timestamp: al.TimestampMs}
			if existingLogAlarm, ok := a.AlarmLogMap[alarmLogKey]; ok {
				logAlarm := alarms.Alarm2Frontend(al)
				existingLogAlarm.Description = logAlarm.Description
				existingLogAlarm.AlarmCode = logAlarm.AlarmCode
				existingLogAlarm.Level = logAlarm.Level
				existingLogAlarm.Impact = logAlarm.Impact
				a.writeAlarmLog(logAlarm)
			}
		}

		for key := range alarmsByIdentifier {
			if _, ok := toKeep[key]; ok {
				continue
			}

			if alarm, ok := alarmsByIdentifier[key]; ok {
				alarmLogKey := AlarmLogKey{Identifier: alarm.Identifier, Timestamp: alarm.TimestampMs}
				if a.AlarmLogMap[alarmLogKey] != nil {
					a.AlarmLogMap[alarmLogKey].StopTimestampMs = time.Now().UnixMilli()
					a.writeAlarmLog(a.AlarmLogMap[alarmLogKey])
				}

				delete(alarmsByIdentifier, key)
				delete(a.alarmTypesByIdentifier, key)
			} else {
				logrus.Errorf("Alarm not found for identifier: %s", key)
			}
		}

		a.trimAlarmLog()
	})
}

func (a *AlarmState) addAlarmToLog(al *alarms.Alarm) {
	alarmLogKey := AlarmLogKey{Identifier: al.Identifier, Timestamp: al.TimestampMs}
	logAlarm := alarms.Alarm2Frontend(al)
	a.AlarmLog = append(a.AlarmLog, logAlarm)
	a.AlarmLogMap[alarmLogKey] = logAlarm
	a.writeAlarmLog(logAlarm)
}

func (a *AlarmState) trimAlarmLog() {
	maxSize := int(a.configNodeCommander.GetChild("alarm_log_size").GetUIntValue())
	if len(a.AlarmLog) <= maxSize {
		return
	}

	for i := 0; i < len(a.AlarmLog)-maxSize; i++ {
		alarm := a.AlarmLog[i]
		alarmLogKey := AlarmLogKey{Identifier: alarm.Identifier, Timestamp: alarm.TimestampMs}
		delete(a.AlarmLogMap, alarmLogKey)
		a.removeAlarmLog(alarm)
		a.removeAlarmLogToSync(alarm)
	}

	a.AlarmLog = a.AlarmLog[len(a.AlarmLog)-maxSize:]
}

func redisAlarmLogKey(alarm *frontend.AlarmRow) string {
	return fmt.Sprintf("%v_%v", alarm.Identifier, alarm.TimestampMs)
}

func (a *AlarmState) writeAlarmLog(alarm *frontend.AlarmRow) {
	alarmBytes, _ := json.Marshal(alarm)
	alarmStr := string(alarmBytes)
	a.redis.HSet(alarmLogKey, redisAlarmLogKey(alarm), alarmStr)

	a.writeAlarmLogToSync(alarm)
}

func (a *AlarmState) removeAlarmLog(alarm *frontend.AlarmRow) {
	a.redis.HDel(alarmLogKey, redisAlarmLogKey(alarm))
}

func (a *AlarmState) writeAlarmLogToSync(alarm *frontend.AlarmRow) {
	keyBytes, _ := json.Marshal(AlarmLogKey{Identifier: alarm.Identifier, Timestamp: alarm.TimestampMs})
	keyString := string(keyBytes)
	a.redis.SAdd(alarmLogSyncKey, keyString)
	logrus.Infof("AlarmSync: added %v to sync", keyString)
}

func (a *AlarmState) removeAlarmLogToSync(alarm *frontend.AlarmRow) {
	keyBytes, _ := json.Marshal(AlarmLogKey{Identifier: alarm.Identifier, Timestamp: alarm.TimestampMs})
	keyString := string(keyBytes)
	a.redis.SRem(alarmLogSyncKey, keyString)
}

func (a *AlarmState) readAlarmLog() {
	all, err := a.redis.HGetAll(alarmLogKey)
	if err != nil {
		logrus.WithError(err).Error("Could not read alarm log")
		return
	}
	for _, jsAlarm := range all {
		var alarm *frontend.AlarmRow
		json.Unmarshal([]byte(jsAlarm), &alarm)
		if alarm.StopTimestampMs == 0 {
			// on startup, stop all alarms that were active before restart
			alarm.StopTimestampMs = time.Now().UnixMilli()
			a.writeAlarmLog(alarm)
		}
		alarmLogKey := AlarmLogKey{Identifier: alarm.Identifier, Timestamp: alarm.TimestampMs}
		a.AlarmLogMap[alarmLogKey] = alarm
		a.AlarmLog = append(a.AlarmLog, alarm)
	}
	sort.SliceStable(a.AlarmLog, func(i, j int) bool {
		return a.AlarmLog[i].TimestampMs < a.AlarmLog[j].TimestampMs
	})
}

func (a *AlarmState) copyAlarm(alarm *frontend.AlarmRow) *frontend.AlarmRow {
	return &frontend.AlarmRow{
		TimestampMs:     alarm.TimestampMs,
		AlarmCode:       alarm.AlarmCode,
		Subsystem:       alarm.Subsystem,
		Description:     alarm.Description,
		Level:           alarm.Level,
		Identifier:      alarm.Identifier,
		Acknowledged:    alarm.Acknowledged,
		Impact:          alarm.Impact,
		StopTimestampMs: alarm.StopTimestampMs,
	}
}

func (a *AlarmState) readAlarmsToSync() []*frontend.AlarmRow {
	// must run under lock
	all, err := a.redis.SMembers(alarmLogSyncKey)
	if err != nil {
		logrus.WithError(err).Error("Could not read alarm log sync queue")
		return nil
	}
	result := make([]*frontend.AlarmRow, 0)
	for _, jsKey := range all {
		var key AlarmLogKey
		json.Unmarshal([]byte(jsKey), &key)
		alarm := a.AlarmLogMap[key]
		if alarm == nil {
			logrus.Warnf("Could not find alarm to sync %v", key)
		} else {
			result = append(result, a.copyAlarm(alarm))
		}
		a.redis.SRem(alarmLogSyncKey, jsKey)
	}
	return result
}

func (a *AlarmState) GetAlarmLog(visibleOnly bool) []*frontend.AlarmRow {
	if visibleOnly {
		visible := make([]*frontend.AlarmRow, 0)
		for _, a := range a.AlarmLog {
			if a.Level != frontend.AlarmLevel_AL_HIDDEN {
				visible = append(visible, a)
			}
		}
		return visible
	}

	return a.AlarmLog
}
func (a *AlarmState) HasImpactingAlarm() bool {
	impactingAlarm := false
	a.ReadOnCurrent(func() {
		for _, alarmsByIdentifier := range a.Alarms {
			for _, alarm := range alarmsByIdentifier {
				if alarm.Level == alarms.AlarmLevelHidden || alarm.Acknowledged || !alarm.Valid || alarm.Impact == alarms.AlarmImpactNone || alarm.Impact == alarms.AlarmImpactUndefined {
					continue
				} else {
					impactingAlarm = true
					break
				}
			}
		}
	})
	return impactingAlarm
}

func (a *AlarmManager) ReconcileAlarms(alarmType AlarmType, alarmList []*alarms.Alarm) {
	a.alarmState.ReconcileAlarms(alarmType, alarmList)
}

func NewAlarmState(redis redis.RedisClient, nodeCommander *config.ConfigTree) *AlarmState {
	state := &AlarmState{ManagedStateImpl: ManagedStateImpl{name: "AlarmState"}}
	state.Alarms = make(map[AlarmType]map[string]*alarms.Alarm)
	state.alarmTypesByIdentifier = make(map[string]AlarmType)
	state.IgnoreList = make(map[string]bool)
	state.AlarmLog = make([]*frontend.AlarmRow, 0)
	state.AlarmLogMap = make(map[AlarmLogKey]*frontend.AlarmRow)
	state.redis = redis
	state.configNodeCommander = nodeCommander
	state.initialize()
	state.readAlarmLog()
	return state
}

type AlarmDefinitionJSON struct {
	Code   string `json:"code"`
	Level  string `json:"level"`
	Impact string `json:"impact"`
}

type AlarmDefinitionsJSON struct {
	Alarms []AlarmDefinitionJSON `json:"alarms"`
}

type AlarmDefinition struct {
	Level  alarms.AlarmLevel
	Impact alarms.AlarmImpact
}

type AlarmDefinitions struct {
	definitions map[string]*AlarmDefinition
}

func NewAlarmDefinitions() *AlarmDefinitions {
	defs := &AlarmDefinitions{definitions: make(map[string]*AlarmDefinition)}

	fileContent, err := os.ReadFile("/robot/golang/commander/alarms_definition.json")
	if err != nil {
		logrus.WithError(err).Fatal("Couldn't read alarms definition file")
	} else {
		defsJson := &AlarmDefinitionsJSON{}
		err = json.Unmarshal(fileContent, defsJson)
		if err != nil {
			logrus.WithError(err).Fatal("Couldn't unmarshal alarms definition file")
		} else {
			for _, alarmJson := range defsJson.Alarms {
				ad := &AlarmDefinition{}
				switch strings.ToLower(alarmJson.Level) {
				case "critical":
					ad.Level = alarms.AlarmLevelCritical
				case "high":
					ad.Level = alarms.AlarmLevelHigh
				case "medium":
					ad.Level = alarms.AlarmLevelMedium
				case "low":
					ad.Level = alarms.AlarmLevelLow
				case "hidden":
					ad.Level = alarms.AlarmLevelHidden
				}
				switch strings.ToLower(alarmJson.Impact) {
				case "none":
					ad.Impact = alarms.AlarmImpactNone
				case "degraded":
					ad.Impact = alarms.AlarmImpactDegraded
				case "offline":
					ad.Impact = alarms.AlarmImpactOffline
				case "critical":
					ad.Impact = alarms.AlarmImpactCritical
				}
				defs.definitions[alarmJson.Code] = ad
			}
		}
	}
	return defs
}

type AlarmManager struct {
	Runnable
	alarmState        *AlarmState
	opState           *OperationsState
	scState           *OverallScannerState
	camState          *OverallCameraState
	velState          VelocityStateIF
	dataState         *DataCaptureState
	stopCtx           context.Context
	hostState         *HostState
	softwareState     *OverallSoftwareState
	boardState        *BoardState
	implementState    *ImplementState
	modelManagerState *ModelManagerState
	dataStorageState  *DataStorageState
	commanderNode     *config.ConfigTree
	commonNode        *config.ConfigTree
	alarmNode         *config.ConfigTree
	trackingState     *TrackingState
	cvRuntimeState    *CVRuntimeState
	tractorState      *TractorIFState
	hwClient          *hardware_manager.HardwareManagerClient
	alarmDefs         *AlarmDefinitions
	rows              map[int]*rows.RowClients
	robotDefinition   *robot_definition.RobotDefinition

	wg sync.WaitGroup
}

func NewAlarmManager(alarmState *AlarmState, stopCtx context.Context, opState *OperationsState, scState *OverallScannerState, camState *OverallCameraState, velState VelocityStateIF, dataState *DataCaptureState, hostState *HostState, softwareState *OverallSoftwareState, boardState *BoardState, implementState *ImplementState, modelManagerState *ModelManagerState, dataStorageState *DataStorageState, commanderNode, commonNode *config.ConfigTree, trackingState *TrackingState, cvRuntimeState *CVRuntimeState, tractorState *TractorIFState, hwClient *hardware_manager.HardwareManagerClient, rowClients map[int]*rows.RowClients, robotDef *robot_definition.RobotDefinition) *AlarmManager {
	action := &AlarmManager{
		alarmState:        alarmState,
		opState:           opState,
		scState:           scState,
		camState:          camState,
		velState:          velState,
		dataState:         dataState,
		stopCtx:           stopCtx,
		hostState:         hostState,
		softwareState:     softwareState,
		boardState:        boardState,
		implementState:    implementState,
		modelManagerState: modelManagerState,
		dataStorageState:  dataStorageState,
		commanderNode:     commanderNode,
		commonNode:        commonNode,
		alarmNode:         commanderNode.GetNode("alarm"),
		trackingState:     trackingState,
		cvRuntimeState:    cvRuntimeState,
		tractorState:      tractorState,
		hwClient:          hwClient,
		alarmDefs:         NewAlarmDefinitions(),
		rows:              rowClients,
		robotDefinition:   robotDef,
	}
	action.Reset()
	action.alarmNode.RegisterCallback(action.Reset)
	return action
}

func (a *AlarmManager) NewAlarm(identifier string, code alarms.AlarmCode, description string, translationParams []*frontend.TranslationParameter, moduleId *uint32) *alarms.Alarm {
	codeStr := code.String()
	ad := a.alarmDefs.definitions[codeStr]
	if ad == nil {
		logrus.Errorf("Could not find alarm definition for %v", codeStr)
		// if alarms definition file has no value for alarm, return Critical level and impact for visibility
		return alarms.NewAlarm(identifier, code, description, alarms.AlarmLevelCritical, alarms.AlarmImpactCritical, "AlarmCode_"+codeStr, translationParams, moduleId)
	}
	return alarms.NewAlarm(identifier, code, description, ad.Level, ad.Impact, "AlarmCode_"+codeStr, translationParams, moduleId)
}

func (a *AlarmManager) NewAlarmWithAutofix(identifier string, code alarms.AlarmCode, description string, translationParams []*frontend.TranslationParameter, autofix func() error, autofixDurationSec uint32, moduleId *uint32) *alarms.Alarm {
	al := a.NewAlarm(identifier, code, description, translationParams, moduleId)
	al.AutofixFunc = autofix
	al.AutofixDurationSec = autofixDurationSec
	return al
}

func (a *AlarmManager) Reset() {
	alarmResetTotal.Inc()

	ignoreIdentifiers := map[string]bool{}
	ignoreListNode := a.commanderNode.GetNode("alarm/ignore_list")
	ignoreChildren := ignoreListNode.GetChildrenNodes()
	for _, child := range ignoreChildren {
		enabled := child.GetChild("enabled")
		if enabled.GetBoolValue() {
			ignoreIdentifiers[child.GetName()] = true
		}
	}

	a.alarmState.WriteOnCurrent(func() {
		for _, alarmMap := range a.alarmState.Alarms {
			for _, alarm := range alarmMap {
				alarmLogKey := AlarmLogKey{Identifier: alarm.Identifier, Timestamp: alarm.TimestampMs}
				if alarmRow, ok := a.alarmState.AlarmLogMap[alarmLogKey]; ok {
					alarmRow.StopTimestampMs = time.Now().UnixMilli()
					a.alarmState.writeAlarmLog(alarmRow)
				}
			}
		}
		a.alarmState.Alarms = make(map[AlarmType]map[string]*alarms.Alarm)
		a.alarmState.alarmTypesByIdentifier = make(map[string]AlarmType)
		a.alarmState.IgnoreList = ignoreIdentifiers
	})

	// Touch to trigger observers
	a.opState.WriteOnCurrent(func() {})
	a.scState.WriteOnCurrent(func() {})
	a.camState.WriteOnCurrent(func() {})
	a.velState.WriteOnCurrent(func() {})
	a.dataState.WriteOnCurrent(func() {})
	a.hostState.WriteOnCurrent(func() {})
	a.softwareState.WriteOnCurrent(func() {})
	a.boardState.WriteOnCurrent(func() {})
	a.implementState.WriteOnCurrent(func() {})
	a.modelManagerState.WriteOnCurrent(func() {})
	a.trackingState.WriteOnCurrent(func() {})
	a.dataStorageState.WriteOnCurrent(func() {})
	a.cvRuntimeState.WriteOnCurrent(func() {})
	a.tractorState.WriteOnCurrent(func() {})
}

type observeArgs struct {
	ts_ms             int64
	rowsPowered       bool
	serverACValidated bool
	chillerValidated  bool
}

func (a *AlarmManager) IntervalAlarmObserver(ctx context.Context, interval time.Duration, alarmType AlarmType, fn func() []*alarms.Alarm) {
	a.wg.Add(1)
	defer a.wg.Done()
	for {
		select {
		case <-ctx.Done():
			return
		case <-time.After(interval):
		}
		alarmList := fn()
		a.ReconcileAlarms(alarmType, alarmList)
	}
}

func (a *AlarmManager) observeState(m ManagedState, s *ImplementState, dependantStates []ManagedState, f func(*observeArgs) (AlarmType, []*alarms.Alarm)) {

	stateChannel := make(chan bool)
	go notifyUpdate(m, stateChannel, a.stopCtx)
	go notifyUpdate(s, stateChannel, a.stopCtx)
	for _, dependant := range dependantStates {
		go notifyUpdate(dependant, stateChannel, a.stopCtx)
	}

	/* Keep checking state until terminated */
	for {
		select {
		case <-a.stopCtx.Done():
			return
		case <-stateChannel:
		}
		var args observeArgs
		s.ReadOnCurrent(func() {
			args = observeArgs{
				rowsPowered:       s.RowsPowered,
				serverACValidated: s.ServerACValidated,
				chillerValidated:  s.ChillerValidated,
			}
		})
		var (
			alarmType AlarmType
			alarmList []*alarms.Alarm
		)
		m.ReadOnCurrent(func() {
			args.ts_ms = m.GetTimestampMs()
			alarmType, alarmList = f(&args)
		})
		a.ReconcileAlarms(alarmType, alarmList)
	}
}

func (a *AlarmManager) observeOperations(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	// TODO Check for alarms
	// alarmsCurrentTotalGauge.WithLabelValues(alarm.Identifier, alarm.AlarmCode).Inc()
	return AlarmTypeSoftware, []*alarms.Alarm{}
}

func (a *AlarmManager) observeScanners(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in Read for scanners */
	state := a.scState
	alarmType := AlarmTypeScanner

	alarmList := []*alarms.Alarm{}
	if !args.rowsPowered {
		return alarmType, alarmList
	}

	for _, scanner := range state.CamMapping { // scanner is *ScannerState
		var moduleIdPtr *uint32
		var moduleId, scannerOffset uint32
		if environment.IsReaper() {
			moduleId, scannerOffset = a.moduleIdFromScanner(&scanner.Descriptor)
			moduleIdPtr = &moduleId
		}
		scannerNumber := (scanner.Descriptor.RowNumber-1)*10 + scanner.Descriptor.ScannerId

		if scanner.Error && scanner.Laser.Enabled {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_laser_unresponsive", scannerNumber))
			var alarmDesc string
			alarmNumber := 1 // unknown error
			if len(scanner.ErrorMessage) > 0 {
				alarmDesc = scanner.ErrorMessage
			} else {
				switch scanner.ErrorCode {
				case "NETWORK_ERROR":
					alarmNumber = 2
					identifier = MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_laser_network_error", scannerNumber))
				case "BOOTING":
					alarmNumber = 3
					identifier = MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_laser_booting", scannerNumber))
				}
				alarmDesc = fmt.Sprintf("Error code: %v", scanner.ErrorCode)
			}

			rowNumber := int64(scanner.Descriptor.RowNumber)
			alarmList = append(alarmList, a.NewAlarmWithAutofix(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Scanner,
					Number:    uint32(alarmNumber),
				},
				fmt.Sprintf("Row %d Laser Control Board %d Unresponsive, %v", scanner.Descriptor.RowNumber,
					scanner.Descriptor.ScannerId, alarmDesc),
				translation.Params(
					translation.Uint32Param("row_id", scanner.Descriptor.RowNumber),
					translation.Uint32Param("scanner_id", scanner.Descriptor.ScannerId),
				),
				func() error {
					if environment.IsReaper() {
						return a.restartScanner(moduleId, scannerOffset)
					} else {
						return a.restartScanners(rowNumber)
					}
				},
				35,
				moduleIdPtr,
			))
		} else if scanner.Laser.PowerError && scanner.Laser.Enabled {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_laser_not_powered", scannerNumber))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Laser,
					Number:    1,
				},
				fmt.Sprintf("Row %d Laser %d does not have power", scanner.Descriptor.RowNumber, scanner.Descriptor.ScannerId),
				translation.Params(
					translation.Uint32Param("row_id", scanner.Descriptor.RowNumber),
					translation.Uint32Param("scanner_id", scanner.Descriptor.ScannerId),
				),
				moduleIdPtr,
			))
		} else if scanner.Laser.Error && scanner.Laser.Enabled {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_laser_error", scannerNumber))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Laser,
					Number:    2,
				},
				fmt.Sprintf("Row %d Laser %d is not firing, laser may be damaged", scanner.Descriptor.RowNumber, scanner.Descriptor.ScannerId),
				translation.Params(
					translation.Uint32Param("row_id", scanner.Descriptor.RowNumber),
					translation.Uint32Param("scanner_id", scanner.Descriptor.ScannerId),
				),
				moduleIdPtr,
			))
		} else if scanner.Laser.Arced && scanner.Laser.Enabled {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_laser_arc", scannerNumber))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Laser,
					Number:    3,
				},
				fmt.Sprintf("Row %d Laser %d may be arcing", scanner.Descriptor.RowNumber, scanner.Descriptor.ScannerId),
				translation.Params(
					translation.Uint32Param("row_id", scanner.Descriptor.RowNumber),
					translation.Uint32Param("scanner_id", scanner.Descriptor.ScannerId),
				),
				moduleIdPtr,
			))
		} else if (scanner.PanFailure || scanner.TiltFailure) && scanner.Laser.Enabled {
			useScannerResetAutofix := a.alarmNode.GetNode("scanner_reset_autofix").GetBoolValue()

			failedServo := "tilt"
			alarmNumber := 5
			if scanner.PanFailure {
				failedServo = "pan"
				alarmNumber = 4
			}
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%d_pan_failure_%v", scannerNumber, failedServo))
			var alarmDesc string = fmt.Sprintf("Row %d Laser Control Board %d Servo Failure detected for %v", scanner.Descriptor.RowNumber,
				scanner.Descriptor.ScannerId, failedServo)

			rowNumber := int64(scanner.Descriptor.RowNumber)
			scannerId := int64(scanner.Descriptor.ScannerId)

			autoFixTime := 35
			autofixFunc := func() error {
				a.resetScanner(int(rowNumber), scannerId, true)

				// allow individual power cycling of the scanner for Reaper
				if environment.IsReaper() {
					return a.restartScanner(moduleId, scannerOffset)
				} else {
					return a.restartScanners(rowNumber)
				}
			}

			if useScannerResetAutofix {
				autoFixTime = 15
				autofixFunc = func() error {
					return a.resetScanner(int(rowNumber), scannerId, false)
				}
			}

			alarmList = append(alarmList, a.NewAlarmWithAutofix(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Scanner,
					Number:    uint32(alarmNumber),
				},
				alarmDesc,
				translation.Params(
					translation.Uint32Param("row_id", scanner.Descriptor.RowNumber),
					translation.Uint32Param("scanner_id", scanner.Descriptor.ScannerId),
				),
				autofixFunc,
				uint32(autoFixTime),
				moduleIdPtr,
			))
		}
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeCameras(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in Read for cameras */
	state := a.camState
	alarmType := AlarmTypeCameras

	alarmList := []*alarms.Alarm{}

	if !args.rowsPowered {
		return alarmType, alarmList
	}

	// Check for disconnected cameras
	for _, camera := range state.Cameras {
		if !camera.Connected {
			var autofix func() error
			autofixDurationSec := 35
			if camera.IsTargetType() {
				var disabled bool = false
				a.scState.ReadOnCurrent(func() {
					if sc, found := a.scState.CamMapping[camera.GlobalCameraId]; found {
						if !sc.Laser.Enabled {
							disabled = true
						}
					}
				})
				if disabled {
					continue
				}

				// power cycle the individual target cam (via the scanner) on Reaper
				if environment.IsReaper() {
					moduleId, scannerId := a.moduleIdFromCamera(camera)
					autofix = func() error {
						return a.restartTargetCam(moduleId, scannerId)
					}
					autofixDurationSec = 30
				} else {
					rowNumber := int64(camera.RowNumber)
					autofix = func() error {
						return a.restartScanners(rowNumber)
					}
					autofixDurationSec = 45
				}
			} else {
				// Reaper can power cycle the predict cam per module
				if environment.IsReaper() {
					moduleId, _ := a.moduleIdFromCamera(camera)
					autofix = func() error {
						return a.restartPredictCam(moduleId)
					}
					autofixDurationSec = 30
				} else {
					autofix = a.restartPredicts
				}
			}

			causeIdentifier := "none"
			causeText := "Unknown"
			subCode := 1
			switch camera.ErrorType {
			case CameraErrorTypeCONNECTION:
				causeIdentifier = "connection"
				causeText = "Connection Issues"
				subCode = 3
			case CameraErrorTypeGRAB:
				causeIdentifier = "grab"
				causeText = "Grab Timeout"
				subCode = 4
			case CameraErrorTypeNONE:
				causeIdentifier = "none"
			case CameraErrorTypeNO_IMAGE_IN_LAST_MINUTE:
				causeIdentifier = "no_image_in_last_minute"
				causeText = "No Image in last minute"
				subCode = 5
			case CameraErrorTypeNO_IMPLEMENTATION:
				causeIdentifier = "no_implementation"
				causeText = "No Implementation"
				subCode = 6
			default:
				causeIdentifier = "none"
			}

			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%s_camera_disconnected_%s", camera.GlobalCameraId, causeIdentifier))
			alarmList = append(alarmList, a.NewAlarmWithAutofix(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Camera,
					Number:    uint32(subCode),
				},
				fmt.Sprintf("Camera Disconnected: %s due to %s", camera.GlobalCameraId, causeText),
				translation.Params(
					translation.StringParam("camera_id", camera.GlobalCameraId),
				),
				autofix,
				uint32(autofixDurationSec),
				nil,
			))
		} else { // if camera.Connected
			if (environment.CarbonGen(a.implementState.robot.MakaGen) != environment.CarbonGenBud) && camera.IsTargetType() && camera.LinkSpeed < 125000000 { // link speed as reported by thinklucid cams
				identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%s_camera_link_speed", camera.GlobalCameraId))
				alarmList = append(alarmList, a.NewAlarm(
					identifier,
					alarms.AlarmCode{
						Subsystem: alarms.Camera,
						Number:    2,
					},
					fmt.Sprintf("Camera %v link speed is lower than expected: %v < 125000000", camera.GlobalCameraId, camera.LinkSpeed),
					translation.Params(
						translation.StringParam("camera_id", camera.GlobalCameraId),
						translation.Int64Param("link_speed", int64(camera.LinkSpeed)),
					),
					nil,
				))

			}
			if camera.NeedsFirmwareUpdate {
				identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%s_camera_firmware_version", camera.GlobalCameraId))
				alarmList = append(alarmList, a.NewAlarm(
					identifier,
					alarms.AlarmCode{
						Subsystem: alarms.Camera,
						Number:    7,
					},
					fmt.Sprintf("Camera %v firmware version is out of date: (current: %v, latest: %v)", camera.GlobalCameraId, camera.FirmwareVersion, *camera.LatestFirmwareVersion),
					translation.Params(
						translation.StringParam("camera_id", camera.GlobalCameraId),
						translation.StringParam("expected_version", *camera.LatestFirmwareVersion),
						translation.StringParam("current_version", camera.FirmwareVersion),
					),
					nil,
				))
			}
		}
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeVelocity(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	// TODO Check for alarms
	// alarmsCurrentTotalGauge.WithLabelValues(alarm.Identifier, alarm.AlarmCode).Inc()
	return AlarmTypeSoftware, []*alarms.Alarm{}
}

func (a *AlarmManager) observeDataCapture(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	// TODO Check for alarms
	// alarmsCurrentTotalGauge.WithLabelValues(alarm.Identifier, alarm.AlarmCode).Inc()
	return AlarmTypeSoftware, []*alarms.Alarm{}
}

func (a *AlarmManager) observeHosts(args *observeArgs) (AlarmType, []*alarms.Alarm) {

	/* We are in ReadOnCurrent for host */
	state := a.hostState
	alarmType := AlarmTypeHost

	alarmList := []*alarms.Alarm{}
	if !args.rowsPowered {
		return alarmType, alarmList
	}

	for _, host_info := range state.hosts {
		for _, alarm := range host_info.Alarms {
			var moduleIdPtr *uint32
			if environment.IsReaper() {
				moduleIdPtr = alarm.ModuleId
			}
			identifier := MakeAlarmIdentifier(alarmType, alarm.Identifier)
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarm.AlarmCode,
				alarm.Description,
				alarm.TranslationParameters,
				moduleIdPtr,
			))
		}
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeSoftware(args *observeArgs) (AlarmType, []*alarms.Alarm) {

	/* We are in Read for software */
	state := a.softwareState
	alarmType := AlarmTypeSoftware

	summary := state.Summary
	if summary.Current.Tag == InvalidTag {
		return alarmType, []*alarms.Alarm{}
	}

	alarmList := []*alarms.Alarm{}

	getHostName := func(row uint32) string {
		if row == 0 {
			return "command"
		}
		if environment.IsReaper() {
			return fmt.Sprintf("module%d", row)
		}
		return fmt.Sprintf("row%d", row)
	}

	// Check for Mismatching Current Version
	for r, s := range state.Hosts {
		if s.Current.Tag == "" || s.Current.Tag == InvalidTag {
			continue // We don't want this type of alarm if the computer is offline, default return is "Invalid"
		}
		hostname := getHostName(r)
		if s.Current.Tag != summary.Current.Tag {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("%s_current_version_mismatch", hostname))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Software,
					Number:    1,
				},
				fmt.Sprintf("Current Version Mismatch For Host: %s, Expected: %s, Found: %s", hostname, summary.Current.Tag, s.Current.Tag),
				translation.Params(
					translation.StringParam("hostname", hostname),
					translation.StringParam("expected_version", summary.Current.Tag),
					translation.StringParam("found_version", s.Current.Tag),
				),
				nil,
			))
		}
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeBoards(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in ReadOnCurrent for boards */
	state := a.boardState
	alarmType := AlarmTypeBoards

	alarmList := []*alarms.Alarm{}
	if !args.rowsPowered {
		return alarmType, alarmList
	}
	for _, board := range state.errors {
		switch board {
		case "GPS":
			identifier := MakeAlarmIdentifier(alarmType, "gps_disconnected")
			alarmList = append(alarmList, a.NewAlarmWithAutofix(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.GPS,
					Number:    1,
				},
				"GPS board is disconnected",
				translation.Params(),
				a.restartGPS,
				35,
				nil,
			))
		case "encoder":
			identifier := MakeAlarmIdentifier(alarmType, "encoder_disconnected")
			alarmList = append(alarmList, a.NewAlarmWithAutofix(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Wheels,
					Number:    1,
				},
				"Wheel encoder board is disconnected",
				translation.Params(),
				a.restartWheelEncoderBoard,
				35,
				nil,
			))
		case "strobe":
			identifier := MakeAlarmIdentifier(alarmType, "strobe_disconnected")
			alarmList = append(alarmList, a.NewAlarmWithAutofix(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.Lights,
					Number:    1,
				},
				"Strobe board is disconnected",
				translation.Params(),
				a.restartStrobe,
				35,
				nil,
			))
		case "safety":
			if a.implementState.hasPlc {
				identifier := MakeAlarmIdentifier(alarmType, "safety_plc_disconnected")
				alarmList = append(alarmList, a.NewAlarm(
					identifier,
					alarms.AlarmCode{
						Subsystem: alarms.SafetyPLC,
						Number:    1,
					},
					"Safety plc is disconnected",
					translation.Params(),
					nil,
				))
			}
		case "supervisory":
			if environment.CarbonGen(a.implementState.robot.MakaGen) != environment.CarbonGenBud && a.implementState.hasPlc {
				identifier := MakeAlarmIdentifier(alarmType, "supervisory_plc_disconnected")
				alarmList = append(alarmList, a.NewAlarm(
					identifier,
					alarms.AlarmCode{
						Subsystem: alarms.SupervisoryPLC,
						Number:    1,
					},
					"Supervisory plc is disconnected",
					translation.Params(),
					nil,
				))
			}
		}
	}
	if state.encoder_err != "" {
		identifier := MakeAlarmIdentifier(alarmType, "encoder_anomoly")
		/*remove this and uncommend below once proven*/
		/*
			level := alarms.AlarmLevelMedium
			if strings.Contains(state.encoder_err, "automatically disabled") {
				level = alarms.AlarmLevelHidden
			}
		*/
		alarmList = append(alarmList, a.NewAlarm(
			identifier,
			alarms.AlarmCode{
				Subsystem: alarms.Wheels,
				Number:    2,
			},
			state.encoder_err,
			translation.Params(),
			nil,
		))
	}

	// NOTE: should the "gps no fix" alarm be inhibited if gps is errored?
	if !state.gps_has_fix {
		identifier := MakeAlarmIdentifier(alarmType, "gps_fixnt")
		alarmList = append(alarmList, a.NewAlarm(
			identifier,
			alarms.AlarmCode{
				Subsystem: alarms.GPS,
				Number:    2,
			},
			"GPS position fix is invalid",
			translation.Params(),
			nil,
		))
	}
	return alarmType, alarmList
}

func muricanTemp(input float64) float64 {
	return input*1.8 + 32
}

func (a *AlarmManager) observeImplementPower(alarmList []*alarms.Alarm) []*alarms.Alarm {
	state := a.implementState.SupervisoryState

	Vab := state.AcVoltageAB
	Vac := state.AcVoltageAC
	Vbc := state.AcVoltageBC
	Hz := state.AcFrequency

	alarm := ""
	alarmNum := uint32(0)
	var parameters []*frontend.TranslationParameter
	if (Vab > 243) || (Vac > 243) || (Vbc > 243) {
		alarm = fmt.Sprintf("Overvoltage Alarm - Vab=%v V, Vac=%v V, Vbc=%v V", Vab, Vac, Vbc)
		alarmNum = 5
		parameters = translation.Params(
			translation.VoltageParam("vab", Vab),
			translation.VoltageParam("vac", Vac),
			translation.VoltageParam("vbc", Vbc),
		)
	} else if (Vab < 205) || (Vac < 205) || (Vbc < 205) {
		alarm = fmt.Sprintf("Undervoltage Alarm - Vab=%v V, Vac=%v V, Vbc=%v V", Vab, Vac, Vbc)
		alarmNum = 6

		if ((Vab >= 40) && (Vab <= 60)) || ((Vac >= 40) && (Vac <= 60)) || ((Vbc >= 40) && (Vbc <= 60)) {
			alarm = fmt.Sprintf("Generator may be faulty (AVR) - Vab=%v V, Vac=%v V, Vbc=%v V", Vab, Vac, Vbc)
			alarmNum = 7
		}
		parameters = translation.Params(
			translation.VoltageParam("vab", Vab),
			translation.VoltageParam("vac", Vac),
			translation.VoltageParam("vbc", Vbc),
		)
	} else if ((Vab >= 205) && (Vab <= 243)) && ((Vac >= 205) && (Vac <= 243)) && ((Vbc >= 205) && (Vbc <= 243)) {
		if Hz < 58 {
			alarm = fmt.Sprintf("RPMs too low. Frequency=%v Hz", Hz)
			alarmNum = 8

		} else if Hz > 62 {
			alarm = fmt.Sprintf("RPMs too high. Frequency=%v Hz", Hz)
			alarmNum = 9
		}
		parameters = translation.Params(
			translation.FrequencyParam("freq", Hz),
		)
	}

	if alarmNum > 0 {
		alarmType := AlarmTypeImplement

		alarmList = append(alarmList, a.NewAlarm(
			MakeAlarmIdentifier(alarmType, fmt.Sprintf("power_%d", alarmNum)),
			alarms.AlarmCode{
				Subsystem: alarms.SupervisoryPLC,
				Number:    alarmNum,
			},
			alarm,
			parameters,
			nil,
		))
	}

	return alarmList
}

func (a *AlarmManager) observeImplement(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in Read for implement */
	state := a.implementState
	alarmType := AlarmTypeImplement

	alarmList := []*alarms.Alarm{}
	if environment.CarbonGen(state.robot.MakaGen) == environment.CarbonGenBud || args.rowsPowered {
		if !state.SafetyState.WaterProtect && args.chillerValidated {
			identifier := MakeAlarmIdentifier(alarmType, "water_protect")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SafetyPLC,
					Number:    5,
				},
				fmt.Sprintf("Check laser coolant system"),
				translation.Params(),
				nil,
			))
		}
	}
	if environment.CarbonGen(state.robot.MakaGen) != environment.CarbonGenBud {
		alarmList = a.observeImplementPower(alarmList)

		if !state.SupervisoryState.TempStatus && args.serverACValidated {
			identifier := MakeAlarmIdentifier(alarmType, "temp_status")

			descriptionPrefix := ""
			alarmNumber := 13
			if state.SupervisoryState.TempBypassStatus {
				descriptionPrefix = "[Hidden] "
				identifier = MakeAlarmIdentifier(alarmType, "temp_status_bypassed")
				alarmNumber = 56
			}

			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    uint32(alarmNumber),
				},
				descriptionPrefix+fmt.Sprintf("Temperature in the server cabinet is outside of acceptable range [32-91F]. Temperature is %.2f F", muricanTemp(state.SupervisoryState.ServerCabinetTemp)),
				translation.Params(
					translation.TemperatureFahrenheitParam("range_start", 32),
					translation.TemperatureFahrenheitParam("range_end", 91),
					translation.TemperatureCelciusParam("temp", state.SupervisoryState.ServerCabinetTemp),
				),
				nil,
			))
		}
		if !state.SupervisoryState.HumidityStatus {
			identifier := MakeAlarmIdentifier(alarmType, "humidity_status")

			alarmNumber := 14
			descriptionPrefix := ""
			if state.SupervisoryState.HumidityBypassStatus {
				descriptionPrefix = "[Hidden] "
				identifier = MakeAlarmIdentifier(alarmType, "humidity_status_bypassed")
				alarmNumber = 57
			}

			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    uint32(alarmNumber),
				},
				descriptionPrefix+fmt.Sprintf("Humidity in the server cabinet is outside of acceptable range. Humidity is %.0f %%", state.SupervisoryState.ServerCabinetHumidity),
				translation.Params(
					translation.PercentParam("humidity", uint32(state.SupervisoryState.ServerCabinetHumidity)),
				),
				nil,
			))
		}
		if state.SupervisoryState.BatteryVoltage_12V < 11.6 {
			identifier := MakeAlarmIdentifier(alarmType, "battery_voltage")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    10,
				},
				fmt.Sprintf("Battery voltage is low. Current voltage is %v V", state.SupervisoryState.BatteryVoltage_12V),
				translation.Params(
					translation.VoltageParam("voltage", state.SupervisoryState.BatteryVoltage_12V),
				),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.LowLevelInTank {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmLowLevelInTank")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    15,
				},
				"Chiller: low level in tank",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.HighCirculatingFluidDischargeTemp && args.chillerValidated {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmHighCirculatingFluidDischargeTemp")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    16,
				},
				"Chiller: high circulating fluid discharge temp",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidDischargeTempRise && args.chillerValidated {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidDischargeTempRise")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    17,
				},
				"Chiller: circulating fluid discharge temp rise",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidDischargeTempDrop && args.chillerValidated {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidDischargeTempDrop")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    18,
				},
				"Chiller: circulating fluid discharge temp drop",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.HighCirculatingFluidReturnTemp {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmHighCirculatingFluidReturnTemp")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    19,
				},
				"Chiller: high circulating fluid return temp",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidDischargePressureRise {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidDischargePressureRise")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    20,
				},
				"Chiller: circulating fluid discharge pressure rise",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidDischargePressureDrop {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidDischargePressureDrop")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    21,
				},
				"Chiller: circulating fluid discharge pressure drop",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.HighCompressorSuctionTemp {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmHighCompressorSuctionTemp")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    22,
				},
				"Chiller: high compressor suction temp",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.LowCompressorSuctionTemp {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmLowCompressorSuctionTemp")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    23,
				},
				"Chiller: low compressor suction temp",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.LowSuperHeatTemp {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmLowSuperHeatTemp")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    24,
				},
				"Chiller: low super heat temp",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.HighCompressorDischargePressure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmHighCompressorDischargePressure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    25,
				},
				"Chiller: high compressor discharge pressure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.RefrigerantCircutPressureHighDrop {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmRefrigerantCircutPressureHighDrop")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    26,
				},
				"Chiller: refrigerant circut pressure high drop",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.RefrigerantCircutPressureLowRise {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmRefrigerantCircutPressureLowRise")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    27,
				},
				"Chiller: refrigerant circut pressure low rise",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.RefrigerantCircutPressureLowDrop {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmRefrigerantCircutPressureLowDrop")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    28,
				},
				"Chiller: refrigerant circut pressure low drop",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorRunningFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorRunningFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    29,
				},
				"Chiller: compressor running failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CommunicationError {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCommunicationError")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    30,
				},
				"Chiller: communication error",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.MemoryError {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmMemoryError")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    31,
				},
				"Chiller: memory error",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.DcLineFuseCut {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmDcLineFuseCut")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    32,
				},
				"Chiller: dc line fuse cut",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidDischargeTempSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidDischargeTempSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    33,
				},
				"Chiller: circulating fluid discharge temp sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidReturnTempSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidReturnTempSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    34,
				},
				"Chiller: circulating fluid return temp sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidSuctionTempSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidSuctionTempSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    35,
				},
				"Chiller: circulating fluid suction temp sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CirculatingFluidDischargePressureSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCirculatingFluidDischargePressureSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    36,
				},
				"Chiller: circulating fluid discharge pressure sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorDischargePressureSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorDischargePressureSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    37,
				},
				"Chiller: compressor discharge pressure sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorSuctionPressureSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorSuctionPressureSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    38,
				},
				"Chiller: compressor suction pressure sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.PumpMaintenance {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmPumpMaintenance")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    39,
				},
				"Chiller: pump maintenance",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.FanMaintenance {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmFanMaintenance")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    40,
				},
				"Chiller: fan maintenance",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorMaintenance {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorMaintenance")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    41,
				},
				"Chiller: compressor maintenance",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.ContactInput1SignalDetection {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmContactInput1SignalDetection")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    42,
				},
				"Chiller: contact input1 signal detection",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.ContactInput2SignalDetection {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmContactInput2SignalDetection")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    43,
				},
				"Chiller: contact input2 signal detection",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorDischargeTempSensorFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorDischargeTempSensorFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    44,
				},
				"Chiller: compressor discharge temp sensor failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorDischargeTempRise {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorDischargeTempRise")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    45,
				},
				"Chiller: compressor discharge temp rise",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.DustproofFilterMaintenance {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmDustproofFilterMaintenance")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    46,
				},
				"Chiller: dustproof filter maintenance",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.PowerStoppage {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmPowerStoppage")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    47,
				},
				"Chiller: power stoppage",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorWaiting {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorWaiting")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    48,
				},
				"Chiller: compressor waiting",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.FanFailure {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmFanFailure")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    49,
				},
				"Chiller: fan failure",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.CompressorOverCurrent {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmCompressorOverCurrent")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    50,
				},
				"Chiller: compressor over current",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.PumpOverCurrent {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmPumpOverCurrent")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    51,
				},
				"Chiller: pump over current",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.AirExhaustFanStoppage {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmAirExhaustFanStoppage")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    52,
				},
				"Chiller: air exhaust fan stoppage",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.IncorrectPhaseError {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmIncorrectPhaseError")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    53,
				},
				"Chiller: incorrect phase error",
				translation.Params(),
				nil,
			))
		}
		if state.SupervisoryState.ChillerAlarms.PhaseBoardOverCurrent {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerAlarmPhaseBoardOverCurrent")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    54,
				},
				"Chiller: phase board over current",
				translation.Params(),
				nil,
			))
		}
		expectedTemp := a.commonNode.GetNode("expected_chiller_temp").GetFloatValue()
		if state.SupervisoryState.ChillerSetTemp != expectedTemp {
			identifier := MakeAlarmIdentifier(alarmType, "ChillerSetTemp")
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.SupervisoryPLC,
					Number:    55,
				},
				fmt.Sprintf("Chiller: temperature set to %v which is not expected %.2f", state.SupervisoryState.ChillerSetTemp, expectedTemp),
				translation.Params(
					translation.TemperatureCelciusParam("temp", state.SupervisoryState.ChillerSetTemp),
					translation.TemperatureCelciusParam("expected_temp", expectedTemp),
				),
				nil,
			))
		}
	}
	return alarmType, alarmList
}

func (a *AlarmManager) observeModels(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	state := a.modelManagerState
	alarmType := AlarmTypeModel

	alarmList := make([]*alarms.Alarm, 0)
	if !args.rowsPowered {
		return alarmType, alarmList
	}

	for _, mmAlarm := range state.GetActiveAlarms() {
		trParams := make([]*frontend.TranslationParameter, 0)
		desc := mmAlarm.description
		for key, val := range mmAlarm.params {
			switch v := val.(type) {
			case string:
				trParams = append(trParams, translation.StringParam(key, v))
				// TODO:(smt) we should properly templatize
				replaceKey := fmt.Sprintf("{%s}", key)
				desc = strings.ReplaceAll(desc, replaceKey, v)
			}
		}
		code := alarms.AlarmCode{Subsystem: alarms.Models, Number: mmAlarm.code}
		newAlarm := a.NewAlarm(mmAlarm.identifier, code, desc, trParams, nil)
		alarmList = append(alarmList, newAlarm)
	}
	return alarmType, alarmList
}

func (a *AlarmManager) observeDataStorageState(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in Read */
	state := a.dataStorageState
	alarmType := AlarmTypeDataStorage

	alarmList := []*alarms.Alarm{}
	storage := int(state.usbUsedPercentage * 100)

	if storage >= 90 {
		identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("usb_storage_low_warning"))
		alarmList = append(alarmList, a.NewAlarm(
			identifier,
			alarms.AlarmCode{
				Subsystem: alarms.Storage,
				Number:    1,
			},
			fmt.Sprintf("USB device more than %d%% full", storage),
			translation.Params(
				translation.IntParam("percent", storage),
			),
			nil,
		))
	} else if storage >= 95 {
		identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("usb_storage_high_warning"))
		alarmList = append(alarmList, a.NewAlarm(
			identifier,
			alarms.AlarmCode{
				Subsystem: alarms.Storage,
				Number:    1,
			},
			fmt.Sprintf("USB device more than %d%% full", storage),
			translation.Params(
				translation.PercentParam("percent", uint32(storage)),
			),
			nil,
		))
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeTracking(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in Read */
	state := a.trackingState
	alarmType := AlarmTypeWeedTracking

	alarmList := []*alarms.Alarm{}
	atCapacity := state.atCapacity

	if !args.rowsPowered {
		return alarmType, alarmList
	}
	for key, capacity := range atCapacity {
		if capacity {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("tracking_at_capacity_%v", key))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.WeedTracking,
					Number:    1,
				},
				fmt.Sprintf("Tracker %v at capacity", key),
				translation.Params(
					translation.StringParam("tracker_id", key),
				),
				nil,
			))
		}
	}
	atSchedulerCapacity := state.atSchedulerCapacity

	for key, capacity := range atSchedulerCapacity {
		if capacity {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("scheduler_at_capacity_%v", key))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.WeedTracking,
					Number:    2,
				},
				fmt.Sprintf("Scheduler %v at capacity", key),
				translation.Params(
					translation.Uint32Param("tracker_id", key),
				),
				nil,
			))
		}
	}

	rotaryTimeout := state.rotaryTimeout

	for key, timeout := range rotaryTimeout {
		if timeout {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("rotary_timeout_%v", key))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.WeedTracking,
					Number:    3,
				},
				fmt.Sprintf("Rotary encoder timeout for tracker at %v", key),
				translation.Params(
					translation.StringParam("tracker_id", key),
				),
				nil,
			))
		}
	}

	deepweedError := state.deepweedError

	for key, timeout := range deepweedError {
		if timeout {
			identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("deepweed_error_%v", key))
			alarmList = append(alarmList, a.NewAlarm(
				identifier,
				alarms.AlarmCode{
					Subsystem: alarms.WeedTracking,
					Number:    4,
				},
				fmt.Sprintf("Deepweed error for tracker at %v", key),
				translation.Params(
					translation.StringParam("tracker_id", key),
				),
				nil,
			))
		}
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeCVRuntime(args *observeArgs) (AlarmType, []*alarms.Alarm) {
	/* We are in Read */
	state := a.cvRuntimeState
	alarmType := AlarmTypeCVRuntime

	alarmList := []*alarms.Alarm{}

	if !args.rowsPowered {
		return alarmType, alarmList
	}

	if a.commonNode.GetNode("feature_flags/embedding_based_classification_feature").GetBoolValue() {
		for key, err := range state.ModelUnsupportedEmbeddings {
			cvId := fmt.Sprintf("cv_%v", key)
			if err {
				identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("model_unsupported_embeddings_%v", cvId))
				alarmList = append(alarmList, a.NewAlarm(
					identifier,
					alarms.AlarmCode{
						Subsystem: alarms.CVRuntime,
						Number:    1,
					},
					fmt.Sprintf("Deepweed model does not support embeddings on: %v", cvId),
					translation.Params(
						translation.StringParam("cv_id", cvId),
					),
					nil,
				))
			}
		}

		for key, err := range state.PlantProfileError {
			cvId := fmt.Sprintf("cv_%v", key)
			if err {
				identifier := MakeAlarmIdentifier(alarmType, fmt.Sprintf("plant_profile_error_%v", cvId))
				alarmList = append(alarmList, a.NewAlarm(
					identifier,
					alarms.AlarmCode{
						Subsystem: alarms.CVRuntime,
						Number:    2,
					},
					fmt.Sprintf("Active plant profile could not be loaded on: %v", cvId),
					translation.Params(
						translation.StringParam("cv_id", cvId),
					),
					nil,
				))
			}
		}
	}

	return alarmType, alarmList
}

func (a *AlarmManager) observeTractor(args *observeArgs) (AlarmType, []*alarms.Alarm) {

	/* We are in ReadOnCurrent for host */
	state := a.tractorState
	alarmType := AlarmTypeTractor

	alarmList := []*alarms.Alarm{}
	if state.Expected && !state.Connected {
		identifier := MakeAlarmIdentifier(alarmType, "tractor_disconnected")
		alarmList = append(alarmList, a.NewAlarm(
			identifier,
			alarms.AlarmCode{
				Subsystem: alarms.Tractor,
				Number:    1,
			},
			"Connection to smart tractor appears to be down",
			translation.Params(),
			nil,
		))
	}

	return alarmType, alarmList
}
func (a *AlarmManager) Run() {
	observers := []func(){
		func() { a.observeState(a.opState, a.implementState, []ManagedState{}, a.observeOperations) },
		func() { a.observeState(a.scState, a.implementState, []ManagedState{}, a.observeScanners) },
		func() {
			a.observeState(a.camState, a.implementState, []ManagedState{a.scState}, a.observeCameras)
		},
		func() { a.observeState(a.velState, a.implementState, []ManagedState{}, a.observeVelocity) },
		func() {
			a.observeState(a.dataState, a.implementState, []ManagedState{}, a.observeDataCapture)
		},
		func() { a.observeState(a.hostState, a.implementState, []ManagedState{}, a.observeHosts) },
		func() {
			a.observeState(a.softwareState, a.implementState, []ManagedState{}, a.observeSoftware)
		},
		func() { a.observeState(a.boardState, a.implementState, []ManagedState{}, a.observeBoards) },
		func() {
			a.observeState(a.implementState, a.implementState, []ManagedState{}, a.observeImplement)
		},
		func() {
			a.observeState(a.modelManagerState, a.implementState, []ManagedState{}, a.observeModels)
		},
		func() {
			a.observeState(a.dataStorageState, a.implementState, []ManagedState{}, a.observeDataStorageState)
		},
		func() {
			a.observeState(a.trackingState, a.implementState, []ManagedState{}, a.observeTracking)
		},
		func() {
			a.observeState(a.cvRuntimeState, a.implementState, []ManagedState{}, a.observeCVRuntime)
		},
		func() {
			a.observeState(a.tractorState, a.implementState, []ManagedState{}, a.observeTractor)
		},
	}

	a.wg.Add(len(observers)) // Observers

	/* Launch all observers and done the waitgroup when they exit */
	for _, observer := range observers {
		go func(f func()) {
			defer a.wg.Done()
			f()
		}(observer)
	}

	a.wg.Wait()
}

func (a *AlarmManager) restartPredicts() error {
	res, err := a.hwClient.SetStrobeDisable(true)
	if !res {
		err = errors.New("Autofix failed, could not disable strobe and predicts")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not disable strobe and predicts")
		return err
	}

	time.Sleep(5 * time.Second)

	res, err = a.hwClient.SetStrobeDisable(false)
	if !res {
		err = errors.New("Autofix failed, could not enable strobe and predicts")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not enable strobe and predicts")
		return err
	}

	time.Sleep(30 * time.Second)

	return nil
}

func (a *AlarmManager) restartScanners(row int64) error {
	res, err := a.hwClient.SetScannersDisable(row, true)
	if !res {
		err = errors.New("Autofix failed, could not disable scanners")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not disable scanners")
		return err
	}

	time.Sleep(5 * time.Second)

	res, err = a.hwClient.SetScannersDisable(row, false)
	if !res {
		err = errors.New("Autofix failed, could not enable scanners")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not enable scanners")
		return err
	}

	time.Sleep(30 * time.Second)

	return nil
}

/**
 * @brief Convert a camera to a module id and scanner offset
 */
func (a *AlarmManager) moduleIdFromCamera(camera *Camera) (uint32, uint32) {
	rowDef := a.robotDefinition.GetRow(camera.RowNumber)
	cameraLoc := rowDef.GetModuleForCamera(camera.LocalCameraId)

	if camera.Type == CameraTypePredict {
		return cameraLoc.ModuleId, 0
	} else if camera.Type == CameraTypeTarget {
		return cameraLoc.ModuleId, cameraLoc.Slot
	} else {
		logrus.Warnf("failed to convert camera (%v) to module id", *camera)
		return 0, 0
	}
}

/**
 * @brief Restart a predict camera by power cycling
 */
func (a *AlarmManager) restartPredictCam(moduleId uint32) error {
	logrus.Infof("Power cycling predict in module %d", moduleId)

	// turn off the camera
	res, err := a.hwClient.SetReaperPredictCamPower(moduleId, false)
	if !res {
		err = errors.New("Autofix failed: error disabling predict power")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed: error disabling predict power")
		return err
	}

	time.Sleep(10 * time.Second)

	// turn camera back on
	res, err = a.hwClient.SetReaperPredictCamPower(moduleId, true)
	if !res {
		err = errors.New("Autofix failed: error enabling predict power")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed: error enabling predict power")
		return err
	}

	time.Sleep(20 * time.Second)

	return nil
}

/**
 * @brief Restart a target camera by power cycling (through scanner)
 */
func (a *AlarmManager) restartTargetCam(moduleId uint32, scannerId uint32) error {
	var res bool
	var err error

	logrus.Infof("Power cycling target cam in module %d, scanner %d", moduleId, scannerId)

	// turn off the camera
	off := false

	if scannerId == 0 {
		res, err = a.hwClient.SetReaperTargetPower(moduleId, &off, nil)
	} else {
		res, err = a.hwClient.SetReaperTargetPower(moduleId, nil, &off)
	}
	if !res {
		err = fmt.Errorf("Autofix failed: error disabling target power")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed: error disabling target power")
		return err
	}

	time.Sleep(10 * time.Second)

	// turn camera back on
	on := true

	if scannerId == 0 {
		res, err = a.hwClient.SetReaperTargetPower(moduleId, &on, nil)
	} else {
		res, err = a.hwClient.SetReaperTargetPower(moduleId, nil, &on)
	}
	if !res {
		err = fmt.Errorf("Autofix failed: error enabling target power")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed: error enabling target power")
		return err
	}

	time.Sleep(20 * time.Second)

	return nil
}

/**
 * @brief Get the module id + offset for a scanner descriptor
 */
func (a *AlarmManager) moduleIdFromScanner(scanner *ScannerDescriptor) (uint32, uint32) {
	rowDef := a.robotDefinition.GetRow(scanner.RowNumber)
	scannerLoc := rowDef.GetModuleForScanner(scanner.ScannerId)
	return scannerLoc.ModuleId, scannerLoc.Slot
}

/**
 * @brief Restart a single scanner (via MCB power control)
 */
func (a *AlarmManager) restartScanner(moduleId uint32, scannerId uint32) error {
	var res bool
	var err error

	logrus.Infof("Power cycling scanner in module %d, scanner %d", moduleId, scannerId)

	// turn off
	off := false

	if scannerId == 0 {
		res, err = a.hwClient.SetReaperScannerPower(moduleId, &off, nil)
	} else {
		res, err = a.hwClient.SetReaperScannerPower(moduleId, nil, &off)
	}
	if !res {
		err = fmt.Errorf("Autofix failed: error disabling scanner power")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed: error disabling scanner power")
		return err
	}

	time.Sleep(10 * time.Second)

	// turn back on
	on := true

	if scannerId == 0 {
		res, err = a.hwClient.SetReaperScannerPower(moduleId, &on, nil)
	} else {
		res, err = a.hwClient.SetReaperScannerPower(moduleId, nil, &on)
	}
	if !res {
		err = fmt.Errorf("Autofix failed: error enabling scanner power")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed: error enabling scanner power")
		return err
	}

	// Reaper scanners take about 15-20 seconds after a power cycle
	time.Sleep(25 * time.Second)

	return nil
}

func (a *AlarmManager) resetScanner(row int, scannerId int64, metricsOnly bool) error {

	rowClient, ok := a.rows[row]

	if !ok {
		return fmt.Errorf("autofix failed, could not find row %v", row)
	}

	err := rowClient.AimbotClient.ResetScanner(&aimbot.ResetScannerRequest{
		ScannerId:   uint32(scannerId),
		MetricsOnly: metricsOnly,
	})

	if err != nil {
		logrus.Errorf("Error while resetting scanner: %v", err)
		return err
	}

	if metricsOnly {
		return nil
	}

	time.Sleep(15 * time.Second)

	return nil
}

func (a *AlarmManager) restartGPS() error {
	res, err := a.hwClient.SetGPSDisable(true)
	if !res {
		err = fmt.Errorf("Autofix failed, could not disable GPS")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not disable GPS")
		return err
	}

	time.Sleep(5 * time.Second)

	res, err = a.hwClient.SetGPSDisable(false)
	if !res {
		err = fmt.Errorf("Autofix failed, could not enable GPS")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not enable GPS")
		return err
	}

	time.Sleep(30 * time.Second)

	return nil
}

func (a *AlarmManager) restartWheelEncoderBoard() error {
	res, err := a.hwClient.SetWheelEncoderBoardDisable(true)
	if !res {
		err = fmt.Errorf("Autofix failed, could not disable wheel encoder board")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not disable wheel encoder board")
		return err
	}

	time.Sleep(5 * time.Second)

	res, err = a.hwClient.SetWheelEncoderBoardDisable(false)
	if !res {
		err = fmt.Errorf("Autofix failed, could not enable wheel encoder board")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not enable wheel encoder board")
		return err
	}

	time.Sleep(30 * time.Second)

	return nil
}

func (a *AlarmManager) restartStrobe() error {
	res, err := a.hwClient.SetStrobeDisable(true)
	if !res {
		err = fmt.Errorf("Autofix failed, could not disable strobe board")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not disable strobe board")
		return err
	}

	time.Sleep(5 * time.Second)

	res, err = a.hwClient.SetStrobeDisable(false)
	if !res {
		err = fmt.Errorf("Autofix failed, could not enable strobe board")
		return err
	}
	if err != nil {
		logrus.WithError(err).Errorf("Autofix failed, could not enable strobe board")
		return err
	}

	time.Sleep(30 * time.Second)

	return nil
}

type AlarmSyncWatcher struct {
	EventTrigger
	alarmState     *AlarmState
	alarmLogClient *portal_clients.Client
	robotName      string
}

func NewAlarmSyncWatcher(alarmState *AlarmState, alarmLogClient *portal_clients.Client, robotName string) *AlarmSyncWatcher {
	action := &AlarmSyncWatcher{
		alarmState:     alarmState,
		alarmLogClient: alarmLogClient,
		robotName:      robotName,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *AlarmSyncWatcher) Action() {
	var alarms []*frontend.AlarmRow
	w.alarmState.WriteOnCurrent(func() {
		alarms = w.alarmState.readAlarmsToSync()
	})

	if len(alarms) == 0 {
		return
	}

	logrus.Infof("AlarmSync: will sync %v alarms for %v", len(alarms), w.robotName)
	err := w.alarmLogClient.SyncAlarms(alarms, w.robotName)
	if err != nil {
		logrus.WithError(err).Warn("AlarmSync: Could not sync alarms, adding them back to queue")
		w.alarmState.WriteOnCurrent(func() {
			for _, alarm := range alarms {
				w.alarmState.writeAlarmLogToSync(alarm)
			}
		})
	}
}

package state

import (
	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/rows"
)

type CVRuntimeState struct {
	ManagedStateImpl
	TimingInfo                 map[int][]*cv.NodeTiming
	ModelUnsupportedEmbeddings map[int]bool
	PlantProfileError          map[int]bool
}

func NewCVRuntimeState() *CVRuntimeState {
	state := &CVRuntimeState{ManagedStateImpl: ManagedStateImpl{name: "CVRuntimeState"}}
	state.initialize()
	return state
}

type CVRuntimeWatcher struct {
	EventTrigger
	cvRuntimeState *CVRuntimeState
	rows           map[int]*rows.RowClients
}

func NewCVRuntimeWatcher(cvRuntimeState *CVRuntimeState, rows map[int]*rows.RowClients) *CVRuntimeWatcher {
	action := &CVRuntimeWatcher{
		cvRuntimeState: cvRuntimeState,
		rows:           rows,
	}
	return action
}

func (w *CVRuntimeWatcher) Action() {
	nodeTimings := make(map[int][]*cv.NodeTiming)
	modelUnsupportedEmbeddings := make(map[int]bool)
	plantProfileError := make(map[int]bool)
	for _, row := range w.rows {
		for pcIndex, client := range row.CVRuntimeClients {
			timings, err := client.GetTiming()
			if err == nil {
				nodeTimings[int(pcIndex)] = timings.NodeTiming
			}
			errorState, err := client.GetErrorState()
			if err == nil {
				modelUnsupportedEmbeddings[int(pcIndex)] = errorState.ModelUnsupportedEmbeddings
				plantProfileError[int(pcIndex)] = errorState.PlantProfileError
			}
		}
	}

	w.cvRuntimeState.WriteOnCurrent(func() {
		w.cvRuntimeState.TimingInfo = nodeTimings
		w.cvRuntimeState.ModelUnsupportedEmbeddings = modelUnsupportedEmbeddings
		w.cvRuntimeState.PlantProfileError = plantProfileError
	})
}

package state

import (
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/sirupsen/logrus"
)

type ModelInfoSyncManager struct {
	EventTrigger

	modelManager *ModelManagerWatcher
	portalClient *portal_clients.Client
	robotName    string
}

func NewModelInfoSyncManager(modelManager *ModelManagerWatcher, portalClient *portal_clients.Client, robotName string) *ModelInfoSyncManager {
	m := &ModelInfoSyncManager{
		modelManager: modelManager,
		portalClient: portalClient,
		robotName:    robotName,
	}
	m.triggerChannel = make(chan bool)
	return m
}

func (m *ModelInfoSyncManager) Action() {
	m.renameModels()
	m.syncModels()
}

func (m *ModelInfoSyncManager) renameModels() {
	resp, err := m.portalClient.GetRenameModelCommands(m.robotName)
	if err != nil {
		logrus.WithError(err).Error("ModelInfoSync: could not get rename command from portal")
		return
	}

	if len(resp.Commands) == 0 {
		return
	}

	toPurge := make([]*portal.RenameModelCommand, 0)
	for _, command := range resp.Commands {
		err = m.modelManager.SaveModelNickname(command.ModelId, command.NewNickname, time.Now())
		if err != nil {
			logrus.WithError(err).Errorf("ModelInfoSync: could not rename model %v", command.ModelId)
			continue
		}
		toPurge = append(toPurge, command)
	}

	if len(toPurge) > 0 {
		err = m.portalClient.PurgeRenameModelCommands(m.robotName, toPurge)
		if err != nil {
			logrus.WithError(err).Error("ModelInfoSync: could not purge rename commands from portal")
		}
	}
}

func (m *ModelInfoSyncManager) syncModels() {
	models := m.modelManager.ListModels("")
	modelInfos := make([]*portal.ModelInfo, 0)
	for _, model := range models {
		modelInfo := &portal.ModelInfo{
			ModelId:  model.ID,
			CropIds:  model.ViableCropIDs,
			Nickname: model.Nickname,
		}
		modelInfos = append(modelInfos, modelInfo)
	}
	err := m.portalClient.UploadModelInfos(m.robotName, modelInfos)
	if err != nil {
		logrus.WithError(err).Error("ModelInfoSync: could not upload models to portal")
	}
}

package state

import (
	"context"
	"fmt"
	"math"
	"reflect"
	"strings"
	"sync"
	"time"

	pb "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/client_owner"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_definition"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

type DistanceState struct {
	ManagedStateImpl
	TotalDistanceMM float64
}

func NewDistanceState() *DistanceState {
	state := &DistanceState{ManagedStateImpl: ManagedStateImpl{name: "DistanceState"}}
	state.initialize()
	return state
}

type DistanceWatcher struct {
	EventTrigger
	distState             *DistanceState
	hardwareManagerClient *hardware_manager.HardwareManagerClient
}

func NewDistanceWatcher(distState *DistanceState, hwClient *hardware_manager.HardwareManagerClient) *DistanceWatcher {
	watcher := &DistanceWatcher{
		distState:             distState,
		hardwareManagerClient: hwClient,
	}
	watcher.triggerChannel = make(chan bool)
	return watcher
}

func (d *DistanceWatcher) Action() {
	distanceMM, _, err := d.hardwareManagerClient.GetNextDistance()
	if err != nil {
		logrus.Errorf("Could not get distance from hw manager, %v", err)
		return
	}

	var previousDistance float64
	d.distState.ReadOnCurrent(func() {
		previousDistance = d.distState.TotalDistanceMM
	})

	if previousDistance != distanceMM {
		d.distState.WriteOnCurrent(func() {
			d.distState.TotalDistanceMM = distanceMM
		})
	}
}

type SafetyState struct {
	Lifted             bool
	EStopped           bool
	InCabEStopped      bool
	LeftEStopped       bool
	RightEStopped      bool
	LaserKey           bool
	Interlock          bool
	WaterProtect       bool
	ResetRequired      bool
	CenterEstop        bool
	PowerButtonEstop   bool
	LeftLPSUInterlock  bool
	RightLPSUInterlock bool
	DebugMode          bool
}

func (s SafetyState) updateMetrics() {
	safteyLiftedGauge.Set(metrics.BoolToFloat(s.Lifted))
	safteyEStoppedGauge.Set(metrics.BoolToFloat(s.EStopped))
	safteyInCabEStoppedGauge.Set(metrics.BoolToFloat(s.InCabEStopped))
	safteyLeftEStoppedGauge.Set(metrics.BoolToFloat(s.LeftEStopped))
	safteyRightEStoppedGauge.Set(metrics.BoolToFloat(s.RightEStopped))
	safteyLaserKeyGauge.Set(metrics.BoolToFloat(s.LaserKey))
	safteyInterlockGauge.Set(metrics.BoolToFloat(s.Interlock))
	safteyWaterProtectGauge.Set(metrics.BoolToFloat(s.WaterProtect))
}
func (s *SafetyState) LaserSafetyPowerCut() bool {
	return s.Lifted || s.EStopped
}

type ChillerAlarms struct {
	LowLevelInTank                                 bool
	HighCirculatingFluidDischargeTemp              bool
	CirculatingFluidDischargeTempRise              bool
	CirculatingFluidDischargeTempDrop              bool
	HighCirculatingFluidReturnTemp                 bool
	CirculatingFluidDischargePressureRise          bool
	CirculatingFluidDischargePressureDrop          bool
	HighCompressorSuctionTemp                      bool
	LowCompressorSuctionTemp                       bool
	LowSuperHeatTemp                               bool
	HighCompressorDischargePressure                bool
	RefrigerantCircutPressureHighDrop              bool
	RefrigerantCircutPressureLowRise               bool
	RefrigerantCircutPressureLowDrop               bool
	CompressorRunningFailure                       bool
	CommunicationError                             bool
	MemoryError                                    bool
	DcLineFuseCut                                  bool
	CirculatingFluidDischargeTempSensorFailure     bool
	CirculatingFluidReturnTempSensorFailure        bool
	CirculatingFluidSuctionTempSensorFailure       bool
	CirculatingFluidDischargePressureSensorFailure bool
	CompressorDischargePressureSensorFailure       bool
	CompressorSuctionPressureSensorFailure         bool
	PumpMaintenance                                bool
	FanMaintenance                                 bool
	CompressorMaintenance                          bool
	ContactInput1SignalDetection                   bool
	ContactInput2SignalDetection                   bool
	CompressorDischargeTempSensorFailure           bool
	CompressorDischargeTempRise                    bool
	DustproofFilterMaintenance                     bool
	PowerStoppage                                  bool
	CompressorWaiting                              bool
	FanFailure                                     bool
	CompressorOverCurrent                          bool
	PumpOverCurrent                                bool
	AirExhaustFanStoppage                          bool
	IncorrectPhaseError                            bool
	PhaseBoardOverCurrent                          bool
}

func (c ChillerAlarms) ToStrings() []string {
	var alarms []string
	v := reflect.ValueOf(c)
	for i := 0; i < v.NumField(); i++ {
		if v.Field(i).Bool() {
			alarms = append(alarms, v.Type().Field(i).Name)
		}
	}
	return alarms
}

func FromProto(proto *pb.ChillerAlarms) ChillerAlarms {
	if proto == nil {
		return ChillerAlarms{}
	}
	return ChillerAlarms{
		LowLevelInTank:                                 proto.LowLevelInTank,
		HighCirculatingFluidDischargeTemp:              proto.HighCirculatingFluidDischargeTemp,
		CirculatingFluidDischargeTempRise:              proto.CirculatingFluidDischargeTempRise,
		CirculatingFluidDischargeTempDrop:              proto.CirculatingFluidDischargeTempDrop,
		HighCirculatingFluidReturnTemp:                 proto.HighCirculatingFluidReturnTemp,
		CirculatingFluidDischargePressureRise:          proto.CirculatingFluidDischargePressureRise,
		CirculatingFluidDischargePressureDrop:          proto.CirculatingFluidDischargePressureDrop,
		HighCompressorSuctionTemp:                      proto.HighCompressorSuctionTemp,
		LowCompressorSuctionTemp:                       proto.LowCompressorSuctionTemp,
		LowSuperHeatTemp:                               proto.LowSuperHeatTemp,
		HighCompressorDischargePressure:                proto.HighCompressorDischargePressure,
		RefrigerantCircutPressureHighDrop:              proto.RefrigerantCircutPressureHighDrop,
		RefrigerantCircutPressureLowRise:               proto.RefrigerantCircutPressureLowRise,
		RefrigerantCircutPressureLowDrop:               proto.RefrigerantCircutPressureLowDrop,
		CompressorRunningFailure:                       proto.CompressorRunningFailure,
		CommunicationError:                             proto.CommunicationError,
		MemoryError:                                    proto.MemoryError,
		DcLineFuseCut:                                  proto.DcLineFuseCut,
		CirculatingFluidDischargeTempSensorFailure:     proto.CirculatingFluidDischargeTempSensorFailure,
		CirculatingFluidReturnTempSensorFailure:        proto.CirculatingFluidReturnTempSensorFailure,
		CirculatingFluidSuctionTempSensorFailure:       proto.CirculatingFluidSuctionTempSensorFailure,
		CirculatingFluidDischargePressureSensorFailure: proto.CirculatingFluidDischargePressureSensorFailure,
		CompressorDischargePressureSensorFailure:       proto.CompressorDischargePressureSensorFailure,
		CompressorSuctionPressureSensorFailure:         proto.CompressorSuctionPressureSensorFailure,
		PumpMaintenance:                                proto.PumpMaintenance,
		FanMaintenance:                                 proto.FanMaintenance,
		CompressorMaintenance:                          proto.CompressorMaintenance,
		ContactInput1SignalDetection:                   proto.ContactInput_1SignalDetection,
		ContactInput2SignalDetection:                   proto.ContactInput_2SignalDetection,
		CompressorDischargeTempSensorFailure:           proto.CompressorDischargeTempSensorFailure,
		CompressorDischargeTempRise:                    proto.CompressorDischargeTempRise,
		DustproofFilterMaintenance:                     proto.DustproofFilterMaintenance,
		PowerStoppage:                                  proto.PowerStoppage,
		CompressorWaiting:                              proto.CompressorWaiting,
		FanFailure:                                     proto.FanFailure,
		CompressorOverCurrent:                          proto.CompressorOverCurrent,
		PumpOverCurrent:                                proto.PumpOverCurrent,
		AirExhaustFanStoppage:                          proto.AirExhaustFanStoppage,
		IncorrectPhaseError:                            proto.IncorrectPhaseError,
		PhaseBoardOverCurrent:                          proto.PhaseBoardOverCurrent,
	}
}

type SupervisoryState struct {
	MainContactorStatusFb  bool
	PowerGood              bool
	PowerBad               bool
	PowerVeryBad           bool
	TempHumidityStatus     bool
	TractorPower           bool
	AcFrequency            float64
	AcVoltageAB            float64
	AcVoltageBC            float64
	AcVoltageAC            float64
	AcVoltageA             float64
	AcVoltageB             float64
	AcVoltageC             float64
	PhasePowerW_3          int64
	PhasePowerVa_3         int64
	PowerFactor            float64
	ServerCabinetTemp      float64
	ServerCabinetHumidity  float64
	BatteryVoltage_12V     float64
	TempBypassStatus       bool
	HumidityBypassStatus   bool
	TempStatus             bool
	HumidityStatus         bool
	BtlDisabled            [3]bool
	ServerDisabled         [3]bool
	ScannersDisabled       [3]bool
	WheelEncoderDisabled   bool
	StrobeDisabled         bool
	GpsDisabled            bool
	MainContactorDisabled  bool
	AirConditionerDisabled bool
	ChillerDisabled        bool
	ChillerTemp            float64
	ChillerFlow            float64
	ChillerPressure        float64
	ChillerConductivity    float64
	ChillerSetTemp         float64
	ChillerAlarms          ChillerAlarms

	// Reaper specific
	WaterProtect          bool
	LiftedStatus          bool
	ChillerHeatTransfer   float64
	ChillerFluidDeltaTemp float64
}

type WheelEncoderState struct {
	FrontLeftTicks    int64
	FrontRightTicks   int64
	BackLeftTicks     int64
	BackRightTicks    int64
	FrontLeftEnabled  bool
	FrontRightEnabled bool
	BackLeftEnabled   bool
	BackRightEnabled  bool
	TimestampMs       uint64
}

type ModuleCameraTemperatures struct {
	Predict float64
	TargetA float64
	TargetB float64
}

type ImplementState struct {
	ManagedStateImpl
	robot  environment.Robot
	hasPlc bool

	SupervisoryState  SupervisoryState
	SafetyState       SafetyState
	WheelEncoderState WheelEncoderState
	ServerACValid     bool
	ServerACValidated bool
	ChillerValid      bool
	ChillerValidated  bool
	RowsPowered       bool

	// reaper specific
	ReaperModuleStates []*pb.ReaperModuleSensorData // expected to be in order of module id
	CameraTemperatures map[uint32]ModuleCameraTemperatures
}

func (i *ImplementState) LaserSafetyPowerCut() bool {
	power_cut := false
	i.ReadOnCurrent(func() {
		power_cut = i.SafetyState.LaserSafetyPowerCut()
	})
	return power_cut
}

// This is meant to be used by robot that don't have a PLC
func initGoodPLCStates(state *ImplementState) {
	state.SafetyState.LaserKey = true
	state.SafetyState.Interlock = true
	state.SafetyState.WaterProtect = true
	state.SupervisoryState.MainContactorStatusFb = true
	state.SupervisoryState.PowerGood = true
	state.SupervisoryState.TempHumidityStatus = true
	state.SupervisoryState.TractorPower = true
	state.SupervisoryState.TempStatus = true
	state.SupervisoryState.HumidityStatus = true
	state.SupervisoryState.BatteryVoltage_12V = 12
	state.SupervisoryState.AcVoltageAB = 220
	state.SupervisoryState.AcVoltageBC = 220
	state.SupervisoryState.AcVoltageAC = 220
	state.SupervisoryState.AcFrequency = 60
	state.SupervisoryState.WaterProtect = true
	state.ChillerValid = true
	state.ChillerValidated = true
	state.ServerACValid = true
	state.ServerACValidated = true
	state.RowsPowered = true
}

func NewImplementState(robot environment.Robot, rd *robot_definition.RobotDefinition) *ImplementState {
	state := &ImplementState{
		ManagedStateImpl:   ManagedStateImpl{name: "ImplementState"},
		robot:              robot,
		hasPlc:             true,
		ReaperModuleStates: make([]*pb.ReaperModuleSensorData, 0),
	}

	if environment.CarbonGen(robot.MakaGen) == environment.CarbonGenReaper {
		if strings.HasPrefix(robot.MakaRobotName, "reapertb") || strings.HasPrefix(robot.MakaRobotName, "mvs") {
			state.hasPlc = false
			initGoodPLCStates(state)
		}
		state.ReaperModuleStates = make([]*pb.ReaperModuleSensorData, rd.NumberOfModules())
		state.CameraTemperatures = make(map[uint32]ModuleCameraTemperatures)
	}
	state.initialize()
	return state
}

func (i *ImplementState) ModuleStateSafeModify(moduleId uint32, modifyFunc func(*pb.ReaperModuleSensorData)) error {
	var err error
	i.WriteOnCurrent(func() {
		moduleIdx := moduleId - 1
		if moduleIdx >= uint32(len(i.ReaperModuleStates)) {
			err = fmt.Errorf("module id %v out of range", moduleId)
			return
		}
		moduleState := i.ReaperModuleStates[moduleIdx]
		if moduleState == nil {
			err = fmt.Errorf("hardware state for module id %v not initialized", moduleId)
			return
		}
		modifyFunc(moduleState)
	})
	return err
}

type ImplementSupervisoryWatcher struct {
	EventTrigger
	implementState        *ImplementState
	operationState        *OperationsState
	hardwareManagerClient *hardware_manager.HardwareManagerClient
	expectedChillerTemp   *config.ConfigTree
	chillerTempBound      *config.ConfigTree
	robot                 environment.Robot
}

type ImplementSafetyWatcher struct {
	EventTrigger
	implementState        *ImplementState
	hardwareManagerClient *hardware_manager.HardwareManagerClient
	robot                 environment.Robot
}

func NewImplementSafetyWatcher(implementState *ImplementState, hardwareManagerClient *hardware_manager.HardwareManagerClient, robot environment.Robot) *ImplementSafetyWatcher {
	action := &ImplementSafetyWatcher{
		implementState:        implementState,
		hardwareManagerClient: hardwareManagerClient,
		robot:                 robot,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *ImplementSafetyWatcher) Action() {

	if !w.implementState.hasPlc {
		return
	}

	var currentSafetyState SafetyState

	w.implementState.ReadOnCurrent(func() {
		currentSafetyState = w.implementState.SafetyState
	})

	safetyState, err := w.hardwareManagerClient.GetSafetyStatus()
	if err != nil {
		logrus.Errorf("Error getting safety status %v", err)
	}

	lifted := currentSafetyState.Lifted
	estopped := currentSafetyState.EStopped
	inCabEStopped := currentSafetyState.InCabEStopped
	leftEStopped := currentSafetyState.LeftEStopped
	rightEStopped := currentSafetyState.RightEStopped
	interlock := currentSafetyState.Interlock
	laserKey := currentSafetyState.LaserKey
	waterProtect := currentSafetyState.WaterProtect
	resetRequired := currentSafetyState.ResetRequired
	centerEstop := currentSafetyState.CenterEstop
	powerButtonEstop := currentSafetyState.PowerButtonEstop
	leftLPSUInterlock := currentSafetyState.LeftLPSUInterlock
	rightLPSUInterlock := currentSafetyState.RightLPSUInterlock
	debugMode := currentSafetyState.DebugMode

	if safetyState != nil {
		lifted = safetyState.Lifted
		estopped = safetyState.Estopped
		inCabEStopped = safetyState.InCabEstopped
		leftEStopped = safetyState.LeftEstopped
		rightEStopped = safetyState.RightEstopped
		interlock = safetyState.Interlock
		laserKey = safetyState.LaserKey
		waterProtect = safetyState.WaterProtect
		resetRequired = safetyState.ResetRequired
		centerEstop = safetyState.CenterEstop
		powerButtonEstop = safetyState.PowerButtonEstop
		leftLPSUInterlock = safetyState.LeftLpsuInterlock
		rightLPSUInterlock = safetyState.RightLpsuInterlock
		debugMode = safetyState.DebugMode
	}

	changeCondition := lifted != currentSafetyState.Lifted ||
		estopped != currentSafetyState.EStopped ||
		inCabEStopped != currentSafetyState.InCabEStopped ||
		leftEStopped != currentSafetyState.LeftEStopped ||
		rightEStopped != currentSafetyState.RightEStopped ||
		laserKey != currentSafetyState.LaserKey ||
		interlock != currentSafetyState.Interlock ||
		waterProtect != currentSafetyState.WaterProtect ||
		resetRequired != currentSafetyState.ResetRequired ||
		centerEstop != currentSafetyState.CenterEstop ||
		powerButtonEstop != currentSafetyState.PowerButtonEstop ||
		leftLPSUInterlock != currentSafetyState.LeftLPSUInterlock ||
		rightLPSUInterlock != currentSafetyState.RightLPSUInterlock ||
		debugMode != currentSafetyState.DebugMode

	if changeCondition {
		w.implementState.WriteOnCurrent(func() {
			w.implementState.SafetyState.Lifted = lifted
			w.implementState.SafetyState.EStopped = estopped
			w.implementState.SafetyState.InCabEStopped = inCabEStopped
			w.implementState.SafetyState.LeftEStopped = leftEStopped
			w.implementState.SafetyState.RightEStopped = rightEStopped
			w.implementState.SafetyState.LaserKey = laserKey
			w.implementState.SafetyState.Interlock = interlock
			w.implementState.SafetyState.WaterProtect = waterProtect
			w.implementState.SafetyState.ResetRequired = resetRequired
			w.implementState.SafetyState.CenterEstop = centerEstop
			w.implementState.SafetyState.PowerButtonEstop = powerButtonEstop
			w.implementState.SafetyState.LeftLPSUInterlock = leftLPSUInterlock
			w.implementState.SafetyState.RightLPSUInterlock = rightLPSUInterlock
			w.implementState.SafetyState.DebugMode = debugMode
			w.implementState.SafetyState.updateMetrics()
		})
	}
}

type ImplementStrobeWatcher struct {
	EventTrigger
	hardwareManagerClient *hardware_manager.HardwareManagerClient
	rows                  map[int]*rows.RowClients
}

func NewImplementStrobeWatcher(hardwareManagerClient *hardware_manager.HardwareManagerClient, rows map[int]*rows.RowClients) *ImplementStrobeWatcher {
	action := &ImplementStrobeWatcher{
		hardwareManagerClient: hardwareManagerClient,
		rows:                  rows,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *ImplementStrobeWatcher) Action() {
	min_target_fps := math.MaxInt
	max_targets_per_predict_ratio := 0
	for i, row := range w.rows {
		for _, client := range row.CVRuntimeClients {
			strobe_settings, err := client.GetRecommendedStrobeSettings()
			if err != nil {
				logrus.Infof("Failed to retrieve recommended strobe settings from row %v: %v", i, err)
				continue
			}
			min_target_fps = int(math.Min(float64(strobe_settings.TargetCameraFps), float64(min_target_fps)))
			max_targets_per_predict_ratio = int(math.Max(float64(strobe_settings.TargetsPerPredictRatio), float64(max_targets_per_predict_ratio)))
		}
	}

	var period_us uint32 = uint32(1000000 / min_target_fps)
	var targets_per_predict_ratio uint32 = uint32(max_targets_per_predict_ratio)
	success, err := w.hardwareManagerClient.SetStrobeSettings(nil, &period_us, &targets_per_predict_ratio)
	if err != nil || !success {
		logrus.Infof("Failed to set strobe settings: %v", err)
	}
}

type ImplementRotaryWatcher struct {
	EventTrigger
	implementState        *ImplementState
	hardwareManagerClient *hardware_manager.HardwareManagerClient
}

func NewImplementRotaryWatcher(implementState *ImplementState, hardwareManagerClient *hardware_manager.HardwareManagerClient) *ImplementRotaryWatcher {
	action := &ImplementRotaryWatcher{
		implementState:        implementState,
		hardwareManagerClient: hardwareManagerClient,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *ImplementRotaryWatcher) Action() {
	var currentWheelEncoderState WheelEncoderState
	w.implementState.ReadOnCurrent(func() {
		currentWheelEncoderState = w.implementState.WheelEncoderState
	})

	wheelEncoderState, err := w.hardwareManagerClient.GetRotaryTicks()
	if err != nil {
		logrus.Errorf("Error getting rotary ticks %v", err)
	}

	newWheelEncoderState := currentWheelEncoderState
	if wheelEncoderState != nil {
		newWheelEncoderState.TimestampMs = wheelEncoderState.TimestampMs
		newWheelEncoderState.FrontLeftTicks = wheelEncoderState.FrontLeftTicks
		newWheelEncoderState.FrontRightTicks = wheelEncoderState.FrontRightTicks
		newWheelEncoderState.BackLeftTicks = wheelEncoderState.BackLeftTicks
		newWheelEncoderState.BackRightTicks = wheelEncoderState.BackRightTicks
		newWheelEncoderState.FrontLeftEnabled = wheelEncoderState.FrontLeftEnabled
		newWheelEncoderState.FrontRightEnabled = wheelEncoderState.FrontRightEnabled
		newWheelEncoderState.BackLeftEnabled = wheelEncoderState.BackLeftEnabled
		newWheelEncoderState.BackRightEnabled = wheelEncoderState.BackRightEnabled
	}

	changeCondition :=
		newWheelEncoderState.TimestampMs != currentWheelEncoderState.TimestampMs ||
			newWheelEncoderState.FrontLeftTicks != currentWheelEncoderState.FrontLeftTicks ||
			newWheelEncoderState.FrontRightTicks != currentWheelEncoderState.FrontRightTicks ||
			newWheelEncoderState.BackLeftTicks != currentWheelEncoderState.BackLeftTicks ||
			newWheelEncoderState.BackRightTicks != currentWheelEncoderState.BackRightTicks ||
			newWheelEncoderState.FrontLeftEnabled != currentWheelEncoderState.FrontLeftEnabled ||
			newWheelEncoderState.FrontRightEnabled != currentWheelEncoderState.FrontRightEnabled ||
			newWheelEncoderState.BackLeftEnabled != currentWheelEncoderState.BackLeftEnabled ||
			newWheelEncoderState.BackRightEnabled != currentWheelEncoderState.BackRightEnabled

	if changeCondition {
		w.implementState.WriteOnCurrent(func() {
			w.implementState.WheelEncoderState = newWheelEncoderState
		})
	}
}

func NewImplementSupervisoryWatcher(implementState *ImplementState, operationState *OperationsState, hardwareManagerClient *hardware_manager.HardwareManagerClient, commonConfig *config.ConfigTree, robot environment.Robot) *ImplementSupervisoryWatcher {
	action := &ImplementSupervisoryWatcher{
		implementState:        implementState,
		operationState:        operationState,
		hardwareManagerClient: hardwareManagerClient,
		expectedChillerTemp:   commonConfig.GetNode("expected_chiller_temp"),
		chillerTempBound:      commonConfig.GetNode("chiller_temp_bound"),
		robot:                 robot,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *ImplementSupervisoryWatcher) FetchSlayerSupervisoryState(state *SupervisoryState) error {
	supervisoryState, err := w.hardwareManagerClient.GetSupervisoryStatus()
	if err != nil {
		return err
	}

	state.MainContactorStatusFb = supervisoryState.MainContactorStatusFb
	state.PowerGood = supervisoryState.PowerGood
	state.PowerBad = supervisoryState.PowerBad
	state.PowerVeryBad = supervisoryState.PowerVeryBad
	state.TempHumidityStatus = supervisoryState.TempHumidityStatus
	state.TractorPower = supervisoryState.TractorPower
	state.AcFrequency = supervisoryState.AcFrequency
	state.AcVoltageAB = supervisoryState.AcVoltageAB
	state.AcVoltageBC = supervisoryState.AcVoltageBC
	state.AcVoltageAC = supervisoryState.AcVoltageAC
	state.AcVoltageA = supervisoryState.AcVoltageA
	state.AcVoltageB = supervisoryState.AcVoltageB
	state.AcVoltageC = supervisoryState.AcVoltageC
	state.PhasePowerW_3 = supervisoryState.PhasePowerW_3
	state.PhasePowerVa_3 = supervisoryState.PhasePowerVa_3
	state.PowerFactor = supervisoryState.PowerFactor
	state.ServerCabinetTemp = supervisoryState.ServerCabinetTemp
	state.ServerCabinetHumidity = supervisoryState.ServerCabinetHumidity
	state.BatteryVoltage_12V = supervisoryState.BatteryVoltage_12V
	state.TempBypassStatus = supervisoryState.TempBypassStatus
	state.HumidityBypassStatus = supervisoryState.HumidityBypassStatus
	state.TempStatus = supervisoryState.TempStatus
	state.HumidityStatus = supervisoryState.HumidityStatus
	copy(state.BtlDisabled[:], supervisoryState.BtlDisabled[:3])
	copy(state.ServerDisabled[:], supervisoryState.ServerDisabled[:3])
	copy(state.ScannersDisabled[:], supervisoryState.ScannersDisabled[:3])
	state.WheelEncoderDisabled = supervisoryState.WheelEncoderDisabled
	state.StrobeDisabled = supervisoryState.StrobeDisabled
	state.GpsDisabled = supervisoryState.GpsDisabled
	state.MainContactorDisabled = supervisoryState.MainContactorDisabled
	state.AirConditionerDisabled = supervisoryState.AirConditionerDisabled
	state.ChillerDisabled = supervisoryState.ChillerDisabled
	state.ChillerTemp = supervisoryState.ChillerTemp
	state.ChillerFlow = supervisoryState.ChillerFlow
	state.ChillerPressure = supervisoryState.ChillerPressure
	state.ChillerConductivity = supervisoryState.ChillerConductivity
	state.ChillerSetTemp = supervisoryState.ChillerSetTemp
	state.ChillerAlarms = FromProto(supervisoryState.ChillerAlarms)

	return nil
}

func (w *ImplementSupervisoryWatcher) FetchReaperSupervisoryState(state *SupervisoryState) error {
	supervisoryState, err := w.hardwareManagerClient.GetReaperEnclosureSensors()
	if err != nil {
		return err
	}

	state.WaterProtect = supervisoryState.WaterProtectStatus
	state.MainContactorStatusFb = supervisoryState.MainContactorStatusFb
	state.PowerGood = supervisoryState.PowerGood
	state.PowerBad = supervisoryState.PowerBad
	state.PowerVeryBad = supervisoryState.PowerVeryBad

	state.LiftedStatus = supervisoryState.LiftedStatus
	state.TractorPower = supervisoryState.TractorPower

	state.AcFrequency = supervisoryState.AcFrequency
	state.AcVoltageAB = supervisoryState.AcVoltageAB
	state.AcVoltageBC = supervisoryState.AcVoltageBC
	state.AcVoltageAC = supervisoryState.AcVoltageAC
	state.AcVoltageA = supervisoryState.AcVoltageA
	state.AcVoltageB = supervisoryState.AcVoltageB
	state.AcVoltageC = supervisoryState.AcVoltageC
	state.PhasePowerW_3 = supervisoryState.PhasePowerW_3
	state.PhasePowerVa_3 = supervisoryState.PhasePowerVa_3
	state.PowerFactor = supervisoryState.PowerFactor

	state.ServerCabinetTemp = supervisoryState.ServerCabinetTemp
	state.ServerCabinetHumidity = supervisoryState.ServerCabinetHumidity

	state.BatteryVoltage_12V = supervisoryState.BatteryVoltage_12V

	state.WheelEncoderDisabled = supervisoryState.WheelEncoderDisabled
	state.StrobeDisabled = supervisoryState.StrobeDisabled
	state.GpsDisabled = supervisoryState.GpsDisabled
	state.MainContactorDisabled = supervisoryState.MainContactorDisabled
	state.AirConditionerDisabled = supervisoryState.AirConditionerDisabled
	state.ChillerDisabled = supervisoryState.ChillerDisabled

	state.ChillerTemp = supervisoryState.ChillerTemp
	state.ChillerFlow = supervisoryState.ChillerFlow
	state.ChillerPressure = supervisoryState.ChillerPressure
	state.ChillerConductivity = supervisoryState.ChillerConductivity
	state.ChillerSetTemp = supervisoryState.ChillerSetTemp
	state.ChillerHeatTransfer = supervisoryState.ChillerHeatTransfer
	state.ChillerFluidDeltaTemp = supervisoryState.ChillerFluidDeltaTemp
	state.ChillerAlarms = FromProto(supervisoryState.ChillerAlarms)
	state.TempStatus = true
	state.HumidityStatus = true

	return nil
}

func (w *ImplementSupervisoryWatcher) Action() {
	gen := environment.CarbonGen(w.implementState.robot.MakaGen)
	if gen != environment.CarbonGenSlayer && gen != environment.CarbonGenReaper {
		return
	} else if !w.implementState.hasPlc {
		return
	}

	var currentSupervisoryState SupervisoryState
	var currentServerACValid bool
	var currentChillerValid bool

	w.implementState.ReadOnCurrent(func() {
		currentSupervisoryState = w.implementState.SupervisoryState
		currentServerACValid = w.implementState.ServerACValid
		currentChillerValid = w.implementState.ChillerValid
	})

	supervisoryState := SupervisoryState{
		MainContactorStatusFb: true,
		BtlDisabled:           [3]bool{false, false, false},
		ServerDisabled:        [3]bool{false, false, false},
		ScannersDisabled:      [3]bool{false, false, false},
		ChillerAlarms:         ChillerAlarms{},
	}

	// Todo: Use different call for reaper, parse the response and update the state
	if gen == environment.CarbonGenSlayer {
		err := w.FetchSlayerSupervisoryState(&supervisoryState)
		if err != nil {
			logrus.Errorf("Error getting supervisory status %v", err)
			return
		}
	} else if gen == environment.CarbonGenReaper {
		err := w.FetchReaperSupervisoryState(&supervisoryState)
		if err != nil {
			logrus.Errorf("Error getting supervisory status %v", err)
			return
		}
	}

	// used by fsm in health.go
	serverACValid := supervisoryState.MainContactorStatusFb && (supervisoryState.TempStatus || supervisoryState.ServerCabinetTemp < 0)
	expectedChillerTemp := w.expectedChillerTemp.GetFloatValue()
	chillerTempBound := w.chillerTempBound.GetFloatValue()
	chillerValid := supervisoryState.MainContactorStatusFb && (supervisoryState.ChillerTemp > expectedChillerTemp-chillerTempBound && supervisoryState.ChillerTemp < expectedChillerTemp+chillerTempBound)

	changeCondition := supervisoryState != currentSupervisoryState ||
		serverACValid != currentServerACValid ||
		chillerValid != currentChillerValid

	if changeCondition {
		w.implementState.WriteOnCurrent(func() {
			w.implementState.ServerACValid = serverACValid
			w.implementState.ChillerValid = chillerValid
			w.implementState.SupervisoryState = supervisoryState
			w.implementState.SupervisoryState.updateMetrics()
		})
	}
}

type ImplementPowerReporter struct {
	Runnable
	implementState *ImplementState
	commonConfig   *config.ConfigTree
	redisClient    *redis.Client
	stopCtx        context.Context
}

func NewImplementPowerReporter(implementState *ImplementState, commonConfig *config.ConfigTree, redisClient *redis.Client, stopCtx context.Context) *ImplementPowerReporter {
	reporter := &ImplementPowerReporter{
		implementState: implementState,
		commonConfig:   commonConfig,
		redisClient:    redisClient,
		stopCtx:        stopCtx,
	}
	return reporter
}

func (r *ImplementPowerReporter) Run() {
	totalPowerOnMs, err := r.redisClient.ReadInt64("commander/total_time_powered_ms", 0)
	if err != nil {
		logrus.Errorf("ImplementPowerReporter: Received error while reading commander/total_time_powered_ms from redis, assuming metric is 0. Error: %v", err)
	} else {
		logrus.Infof("ImplementPowerReporter: Read from redis, commander/total_time_powered_ms=%v", totalPowerOnMs)
	}

	var currentState bool
	r.implementState.ReadOnCurrent(func() {
		currentState = r.implementState.SupervisoryState.MainContactorStatusFb
	})
	lastKnownPowerOnTime := time.Now().UnixMilli()
	lastReport := time.Now().UnixMilli()

	newVal := make(chan bool, 1)
	go r.notifyUpdate(newVal, r.stopCtx)

	for {
		select {
		case newState := <-newVal:
			{
				if currentState {
					deltaTs := time.Now().UnixMilli() - lastKnownPowerOnTime
					totalPowerOnMs += deltaTs
					lastReport = r.writeToRedisIfNeeded(totalPowerOnMs, lastReport)
				}
				if newState {
					lastKnownPowerOnTime = time.Now().UnixMilli()
				}
				currentState = newState
			}
		case <-time.After(time.Duration(r.commonConfig.GetNode("weeding_metrics/power_on_persist_interval_sec").GetUIntValue()) * time.Second):
			{
				if currentState {
					deltaTs := time.Now().UnixMilli() - lastKnownPowerOnTime
					totalPowerOnMs += deltaTs
					lastKnownPowerOnTime = time.Now().UnixMilli()
					lastReport = r.writeToRedisIfNeeded(totalPowerOnMs, lastReport)
				}
			}
		}
	}
}

func (r *ImplementPowerReporter) writeToRedisIfNeeded(totalPowerOnMs int64, lastReport int64) int64 {
	if time.Now().UnixMilli()-lastReport > 1000*int64(r.commonConfig.GetNode("weeding_metrics/power_on_persist_interval_sec").GetUIntValue()) {
		r.redisClient.WriteInt64("commander/total_time_powered_ms", totalPowerOnMs)
		logrus.Infof("ImplementPowerReporter:  Wrote to redis commander/total_time_powered_ms=%v", totalPowerOnMs)
		powerOnTimeSecGauge.Set(float64(totalPowerOnMs / 1000))
		lastReport = time.Now().UnixMilli()
	}
	return lastReport
}

func (r *ImplementPowerReporter) notifyUpdate(c chan bool, stopCtx context.Context) {
	ts := time.Now().UnixMilli()
	for {
		var val bool
		result := r.implementState.ReadOnNext(stopCtx, ts, func() {
			ts = r.implementState.timestampMs
			val = r.implementState.SupervisoryState.MainContactorStatusFb
		})
		if !result {
			return
		}
		c <- val
	}
}

type ImplementReaperModuleWatcher struct {
	EventTrigger
	hardwareManagerClient   *hardware_manager.HardwareManagerClient
	clientOwner             *client_owner.ClientOwner
	implementState          *ImplementState
	logger                  *logrus.Entry
	moduleOrchestratorState *ModuleOrchestratorState
}

func NewImplementReaperModuleWatcher(implementState *ImplementState, hardwareManagerClient *hardware_manager.HardwareManagerClient, moduleOrchestratorState *ModuleOrchestratorState) *ImplementReaperModuleWatcher {
	watcher := &ImplementReaperModuleWatcher{
		hardwareManagerClient:   hardwareManagerClient,
		implementState:          implementState,
		logger:                  logrus.WithField("module", "ReaperModuleWatcher"),
		moduleOrchestratorState: moduleOrchestratorState,
	}
	return watcher
}

func (w *ImplementReaperModuleWatcher) Action() {
	// Fetch module id mapping
	moduleIds := w.moduleOrchestratorState.GetAssignedIdSerialMap()

	// Grab the latest state from the hardware manager
	fetchedStates, err := w.hardwareManagerClient.GetReaperModuleSensors()
	if err != nil {
		w.logger.Errorf("error getting reaper module sensors %v", err)
		return
	}

	currentStates := make([]*pb.ReaperModuleSensorData, 0)
	w.implementState.ReadOnCurrent(func() {
		for _, state := range w.implementState.ReaperModuleStates {
			currentStates = append(currentStates, proto.Clone(state).(*pb.ReaperModuleSensorData))
		}
	})

	if len(fetchedStates) != len(currentStates) {
		w.logger.Warnf("number of fetched module states (%v) does not match expected number of states (%v)", len(fetchedStates), len(currentStates))
	}

	// mapify the fetched states since we don't know what modules were fetched
	fetchedStatesMap := make(map[int32]*pb.ReaperModuleSensorData)
	for _, fetchedState := range fetchedStates {
		maybeSn, ok := moduleIds[uint32(fetchedState.ModuleId)]
		if ok {
			fetchedState.ModuleSn = &maybeSn
		}
		fetchedStatesMap[fetchedState.ModuleId] = fetchedState
	}

	// check if the state has changed
	changed := false
	for i, currentState := range currentStates {
		fetchedState := fetchedStatesMap[int32(i+1)]
		// current or fetched state might be nil
		changed = changed || !proto.Equal(currentState, fetchedState)
	}

	if changed {
		w.implementState.WriteOnCurrent(func() {
			for i := range currentStates {
				// non existent module states are set to nil
				w.implementState.ReaperModuleStates[i] = fetchedStatesMap[int32(i+1)]
			}
		})
	}

	// Update metrics for all modules
	for _, fetchedState := range fetchedStates {
		updateReaperModuleMetrics(fetchedState)
	}
}

type ImplementCameraTemperatureWatcher struct {
	EventTrigger
	clientOwner    *client_owner.ClientOwner
	implementState *ImplementState
	logger         *logrus.Entry
}

func NewImplementCameraTemperatureWatcher(implementState *ImplementState, clientOwner *client_owner.ClientOwner) *ImplementCameraTemperatureWatcher {
	watcher := &ImplementCameraTemperatureWatcher{
		clientOwner:    clientOwner,
		implementState: implementState,
		logger:         logrus.WithField("module", "CameraTemperatureWatcher"),
	}
	return watcher
}

func (w *ImplementCameraTemperatureWatcher) Action() {
	fetchedStatesMap := make(map[uint32]ModuleCameraTemperatures)
	fetchLock := sync.Mutex{}
	wg := sync.WaitGroup{}

	for _, row := range w.clientOwner.GetRowClients() {
		for id, client := range row.CVRuntimeClients {
			wg.Add(1)
			go func(client_id uint32, client *cv_runtime_client.CVRuntimeClient) {
				defer wg.Done()
				cameraTemperatures, err := client.GetCameraTemperatures()
				if err != nil {
					w.logger.Errorf("error getting camera temperatures %v", err)
					return
				}

				temps := ModuleCameraTemperatures{}
				for _, cameraTemperature := range cameraTemperatures.GetTemperature() {
					name := cameraTemperature.GetCamId()
					if strings.Contains(name, "predict") {
						temps.Predict = cameraTemperature.Temperature
					} else if strings.Contains(name, "target") {
						// target temperatures are in the format "target<odd_number> or target<even_number>"
						// we can use the last character to determine if it's targetA or targetB
						if name[len(name)-1]%2 == 0 {
							temps.TargetB = cameraTemperature.Temperature
						} else {
							temps.TargetA = cameraTemperature.Temperature
						}
					}
				}
				fetchLock.Lock()
				fetchedStatesMap[client_id] = temps
				fetchLock.Unlock()
			}(id, client)
		}
	}

	wg.Wait()

	if len(fetchedStatesMap) == 0 {
		return
	}

	currentStates := make(map[uint32]ModuleCameraTemperatures)
	w.implementState.ReadOnCurrent(func() {
		for id, state := range w.implementState.CameraTemperatures {
			currentStates[id] = state
		}
	})

	changed := false
	if len(fetchedStatesMap) != len(currentStates) {
		changed = true
	} else {
		for id, fetchedState := range fetchedStatesMap {
			currentState, ok := currentStates[id]
			if !ok {
				changed = true
				continue
			}
			changed = changed || fetchedState != currentState
		}
	}

	if changed {
		w.implementState.WriteOnCurrent(func() {
			for id, state := range fetchedStatesMap {
				w.implementState.CameraTemperatures[id] = state
			}
		})
	}

	// Update camera temperature metrics
	updateCameraTemperatureMetrics(fetchedStatesMap)
}

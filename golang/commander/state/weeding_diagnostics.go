package state

import (
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type WeedingDiagnosticsState struct {
	ManagedStateImpl

	Status            weed_tracking.RecordingStatus
	RecordingName     string
	EndRecordingTsSec uint64

	OpenRecordings map[string]*Recording
}

func NewWeedingDiagnosticsState() *WeedingDiagnosticsState {
	s := &WeedingDiagnosticsState{
		OpenRecordings: map[string]*Recording{},
	}
	s.initialize()
	return s
}

type WeedingDiagnosticsUploadProgress struct {
	UploadState frontend.UploadState
	Percent     uint32
}

type WeedingDiagnosticsUploadState struct {
	ManagedStateImpl
	UploadStates map[string]*WeedingDiagnosticsUploadProgress
}

func NewWeedingDiagnosticsUploadState() *WeedingDiagnosticsUploadState {
	state := &WeedingDiagnosticsUploadState{
		ManagedStateImpl: ManagedStateImpl{name: "WeedingDiagnosticUploadState"},
		UploadStates:     make(map[string]*WeedingDiagnosticsUploadProgress),
	}
	state.initialize()
	return state
}

type Recording struct {
	Name                   string
	RowSnapshots           map[int]*ProtobufFileReader
	StaticData             *frontend.StaticRecordingData
	RowTrajectoryImages    map[uint32]map[uint32]*frontend.TrajectoryData
	RowPredictImages       map[uint32]*frontend.PredictImagesPerCam
	RowDeepweedPredictions map[int32]map[int32]*ProtobufFileReader
	RowRotaryTicks         map[int32]*ProtobufFileReader
}

type P2PFile struct {
	TargetCoordX      float32
	TargetCoordY      float32
	TargetTimestampMs string
}

type CrosshairFile struct {
	Target_Crosshair []int
}

func OpenRecording(name string, rows map[int]*rows.RowClients) (*Recording, error) {
	r := &Recording{
		Name:                   name,
		RowSnapshots:           make(map[int]*ProtobufFileReader),
		RowTrajectoryImages:    make(map[uint32]map[uint32]*frontend.TrajectoryData),
		RowPredictImages:       make(map[uint32]*frontend.PredictImagesPerCam),
		RowDeepweedPredictions: make(map[int32]map[int32]*ProtobufFileReader),
		RowRotaryTicks:         make(map[int32]*ProtobufFileReader),
	}

	staticData, err := os.ReadFile(fmt.Sprintf("/data/diagnostics/%v/static_data.carbon", name))
	if err != nil {
		return nil, err
	}

	r.StaticData = &frontend.StaticRecordingData{}
	proto.Unmarshal(staticData, r.StaticData)

	for _, row := range r.StaticData.RowsRecorded {
		reader, err := NewProtobufFileReader(fmt.Sprintf("/data/diagnostics/%v/row%v/diagnostic_snapshots.carbon", name, row), "DiagnosticSnapshot")
		if err != nil {
			return nil, err
		}
		r.RowSnapshots[int(row)] = reader
		r.RowDeepweedPredictions[row] = make(map[int32]*ProtobufFileReader)
		robot, _ := environment.GetRobot()

		entries, err := os.ReadDir(fmt.Sprintf("/data/diagnostics/%v/row%v/", name, row))
		if err != nil {
			fmt.Println("Error reading directory:", err)
			return nil, err
		}
		numPredicts := 0
		for _, file := range entries {
			if file.IsDir() {
				continue
			}
			name := file.Name()
			numStr := "0"
			if idx := strings.Index(name, "predict"); idx != -1 {
				numStr = name[idx+len("predict") : idx+len("predict")+1]
			}
			num, err := strconv.Atoi(numStr)
			if err != nil {
				continue
			}

			if num > numPredicts {
				numPredicts = num
			}
		}
		if environment.CarbonGen(robot.MakaGen) == environment.CarbonGenReaper {
			numPredicts = 1
		}

		for cam := 1; cam <= numPredicts; cam++ {
			r.RowDeepweedPredictions[row][int32(cam)], err = NewProtobufFileReader(fmt.Sprintf("/data/diagnostics/%v/row%v/predict%v_deepweed.carbon", name, row, cam), "DeepweedPredictionRecord")
			if err != nil {
				return nil, err
			}
		}

		r.RowRotaryTicks[row], err = NewProtobufFileReader(fmt.Sprintf("/data/diagnostics/%v/row%v/wheel_encoder.carbon", name, row), "RotaryTicksRecord")
		if err != nil {
			return nil, err
		}

		r.RowPredictImages[uint32(row)] = &frontend.PredictImagesPerCam{}
		predictsDir, err := ioutil.ReadDir(fmt.Sprintf("/data/diagnostics/%v/row%v/predict_images", name, row))
		r.RowPredictImages[uint32(row)].Images = make(map[int32]*frontend.PredictImages)
		r.RowPredictImages[uint32(row)].Images[1] = &frontend.PredictImages{}
		r.RowPredictImages[uint32(row)].Images[2] = &frontend.PredictImages{}
		r.RowPredictImages[uint32(row)].Images[3] = &frontend.PredictImages{}
		r.RowPredictImages[uint32(row)].Images[4] = &frontend.PredictImages{}
		for _, d := range predictsDir {
			if strings.HasSuffix(d.Name(), ".png") {
				cam_id, _ := strconv.Atoi(d.Name()[7:8])
				r.RowPredictImages[uint32(row)].Images[int32(cam_id)].Names = append(r.RowPredictImages[uint32(row)].Images[int32(cam_id)].Names, d.Name())
			}
		}

		r.RowTrajectoryImages[uint32(row)] = make(map[uint32]*frontend.TrajectoryData)
		readTargetImages(r, int(row))
		readTrajectoryPredictImages(r, uint32(row))
		readP2PPredictImages(r, uint32(row))

		// finding last snapshot
		if len(r.RowTrajectoryImages[uint32(row)]) > 0 {
			for snapshot_num := 0; snapshot_num < reader.NumRecords(); snapshot_num++ {
				record, err := reader.ReadRecord(uint32(snapshot_num))
				if err != nil {
					logrus.Warnf("WeedingDiagnostics: couldn't parse snapshot %v", snapshot_num)
					continue
				}
				snapshot := weed_tracking.DiagnosticsSnapshot{}
				proto.Unmarshal(record, &snapshot)
				for _, t := range snapshot.Trajectories {
					if images, ok := r.RowTrajectoryImages[uint32(row)][t.Id]; ok {
						images.LastSnapshot = &frontend.LastSnapshot{Trajectory: t, DiagnosticsSnapshotNumber: uint32(snapshot_num)}
					}
				}
			}
		}
	}

	return r, nil
}

func readTrajectoryPredictImages(r *Recording, row uint32) {
	dirName := fmt.Sprintf("/data/diagnostics/%v/row%v/trajectory_predict_images", r.Name, row)
	files, err := ioutil.ReadDir(dirName)
	if err != nil {
		logrus.Warnf("WeedingDiagnostics: couldn't read trajectory_predict_images directory: %v", err)
		return
	}
	for _, f := range files {
		if strings.HasSuffix(f.Name(), ".png") {
			tid, _ := strconv.Atoi(f.Name()[:len(f.Name())-4])
			var trajectoryData *frontend.TrajectoryData
			var ok bool
			if trajectoryData, ok = r.RowTrajectoryImages[row][uint32(tid)]; !ok {
				trajectoryData = &frontend.TrajectoryData{
					PredictImage:     &frontend.TrajectoryPredictImageMetadata{},
					TargetImages:     make([]*frontend.TargetImage, 0),
					P2PPredictImages: make([]*frontend.P2PPredictImage, 0)}
				r.RowTrajectoryImages[row][uint32(tid)] = trajectoryData
			}

			metaFilename := fmt.Sprintf("%v/%v.meta.json", dirName, f.Name()[:len(f.Name())-4])
			metaJson, err := ioutil.ReadFile(metaFilename)
			if err != nil {
				logrus.Warnf("WeedingDiagnostics: couldn't read %v: %v", metaFilename, err)
				continue
			}
			protojson.Unmarshal(metaJson, trajectoryData.PredictImage)
		}
	}
}

func readP2PPredictImages(r *Recording, row uint32) {
	dirName := fmt.Sprintf("/data/diagnostics/%v/row%v/p2p_predicts", r.Name, row)
	dirs, err := ioutil.ReadDir(dirName)
	if err != nil {
		logrus.Warnf("WeedingDiagnostics: couldn't read p2p_predicts directory: %v", err)
		return
	}

	for _, d := range dirs {
		if d.IsDir() {
			tid, err := strconv.Atoi(d.Name())
			if err != nil {
				logrus.Warnf("WeedingDiagnostics: couldn't parse trajectory ID from directory name %v: %v", d.Name(), err)
				continue
			}

			// Get or create the trajectory data
			var trajectoryData *frontend.TrajectoryData
			var ok bool
			if trajectoryData, ok = r.RowTrajectoryImages[row][uint32(tid)]; !ok {
				trajectoryData = &frontend.TrajectoryData{
					PredictImage:     &frontend.TrajectoryPredictImageMetadata{},
					TargetImages:     make([]*frontend.TargetImage, 0),
					P2PPredictImages: make([]*frontend.P2PPredictImage, 0)}
				r.RowTrajectoryImages[row][uint32(tid)] = trajectoryData
			}

			// Ensure P2PPredictImages is initialized
			if trajectoryData.P2PPredictImages == nil {
				trajectoryData.P2PPredictImages = make([]*frontend.P2PPredictImage, 0)
			}

			// Read all PNG files in the directory
			p2pDirPath := fmt.Sprintf("%s/%s", dirName, d.Name())
			files, err := ioutil.ReadDir(p2pDirPath)
			if err != nil {
				logrus.Warnf("WeedingDiagnostics: couldn't read p2p_predicts images directory %v: %v", p2pDirPath, err)
				continue
			}

			for _, f := range files {
				if strings.HasSuffix(f.Name(), ".png") {
					// For each PNG file, read its metadata
					baseName := f.Name()[:len(f.Name())-4] // Remove .png extension
					metaFilename := fmt.Sprintf("%s/%s.meta.json", p2pDirPath, baseName)

					metaJson, err := ioutil.ReadFile(metaFilename)
					if err != nil {
						logrus.Warnf("WeedingDiagnostics: couldn't read p2p metadata file %v: %v", metaFilename, err)
						continue
					}

					// Create a new P2PPredictImage and populate it from the metadata
					p2pImage := &frontend.P2PPredictImage{
						Name: f.Name(),
						Ts:   &frontend.Timestamp{},
					}

					err = protojson.Unmarshal(metaJson, p2pImage)
					if err != nil {
						logrus.Warnf("WeedingDiagnostics: couldn't parse p2p metadata file %v: %v", metaFilename, err)
						continue
					}

					p2pImage.Name = f.Name()

					// Add the P2PPredictImage to the trajectory data
					trajectoryData.P2PPredictImages = append(trajectoryData.P2PPredictImages, p2pImage)
				}
			}
		}
	}
}

func readTargetImages(r *Recording, row int) {
	dirs, err := ioutil.ReadDir(fmt.Sprintf("/data/diagnostics/%v/row%v/burst_records", r.Name, row))
	if err != nil {
		return
	}
	// target images (burst records)
	for _, d := range dirs {
		if d.IsDir() && strings.HasSuffix(d.Name(), "_burst_record") {
			idx := strings.Index(d.Name(), "_")
			id, err := strconv.Atoi(d.Name()[:idx])
			imagesDirName := fmt.Sprintf("/data/diagnostics/%v/row%v/burst_records/%v", r.Name, row, d.Name())
			if err != nil {
				logrus.Warnf("WeedingDiagnostics: coudln't read images directory %v", imagesDirName)
				continue
			}

			var crosshair CrosshairFile
			crosshairJson, _ := ioutil.ReadFile(imagesDirName + "/meta.json")
			json.Unmarshal(crosshairJson, &crosshair)
			if len(crosshair.Target_Crosshair) < 2 {
				logrus.Warnf("WeedingDiagnostics: invalid meta.json file in %v", imagesDirName)
				continue
			}

			images := &frontend.TrajectoryData{
				PredictImage:     &frontend.TrajectoryPredictImageMetadata{},
				TargetImages:     make([]*frontend.TargetImage, 0),
				P2PPredictImages: make([]*frontend.P2PPredictImage, 0),
				CrosshairX:       uint32(crosshair.Target_Crosshair[0]),
				CrosshairY:       uint32(crosshair.Target_Crosshair[1])}
			r.RowTrajectoryImages[uint32(row)][uint32(id)] = images

			files, err := ioutil.ReadDir(imagesDirName)
			if err != nil {
				logrus.Warnf("WeedingDiagnostics: couldn't read images directory %v", imagesDirName)
				continue
			}
			for _, f := range files {
				if strings.HasPrefix(f.Name(), "burst-target") && strings.HasSuffix(f.Name(), ".png") {
					p2pFileName := f.Name()[:len(f.Name())-3] + "p2p"
					p2pFileJson, err := ioutil.ReadFile(imagesDirName + "/" + p2pFileName)
					if err != nil {
						logrus.Warnf("WeedingDiagnostics: couldn't read p2p file %v, err=%v", p2pFileName, err)
						continue
					}
					var p2p P2PFile
					err = json.Unmarshal(p2pFileJson, &p2p)
					if err != nil {
						logrus.Warnf("WeedingDiagnostics: couldn't read p2p file %v, err=%v", p2pFileName, err)
						continue
					}
					p2px := uint32(p2p.TargetCoordX)
					p2py := uint32(p2p.TargetCoordY)
					ts, _ := strconv.Atoi(p2p.TargetTimestampMs)
					img := &frontend.TargetImage{Name: f.Name(), P2PPredictX: p2px, P2PPredictY: p2py, Timestamp: uint64(ts)}
					images.TargetImages = append(images.TargetImages, img)
				}
			}
		}
	}
}

type ProtobufFileReader struct {
	file           *os.File
	recordPointers []int64
	mutex          sync.Mutex
}

const CARBON_PROTO_FILE_STREAM_MAGIC = 0x4E4F42524143

func NewProtobufFileReader(filename string, expectedDescriptor string) (*ProtobufFileReader, error) {
	f, err := os.Open(filename)
	if err != nil {
		return nil, err
	}

	p := &ProtobufFileReader{file: f, recordPointers: make([]int64, 0)}

	num, err := p.readLittleEndian64()
	if err != nil {
		return nil, err
	}

	if num != CARBON_PROTO_FILE_STREAM_MAGIC {
		return nil, fmt.Errorf("%v doesn't appear to be a diagnostics snapshot file", filename)
	}

	len, err := p.readLittleEndian32()
	if err != nil {
		return nil, err
	}

	desc, err := p.readBytes(len)
	if err != nil {
		return nil, err
	}

	if string(desc) != expectedDescriptor {
		return nil, fmt.Errorf("%v doesn't appear to be a diagnostics snapshot file", filename)
	}

	for {
		offset, err := p.file.Seek(0, io.SeekCurrent)
		if err != nil {
			return nil, err
		}

		len, err := p.readLittleEndian32()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("%v appears to be corrupt, error=%v", filename, err)
		}

		_, err = p.file.Seek(int64(len), io.SeekCurrent)
		if err != nil {
			return nil, fmt.Errorf("%v appears to be corrupt, error=%v", filename, err)
		}

		p.recordPointers = append(p.recordPointers, offset)
	}
	logrus.Infof("WeedingDiagnostics: opened file %v ", filename)

	return p, nil
}

func (p *ProtobufFileReader) readBytes(n int32) ([]byte, error) {
	if n < 0 {
		return nil, fmt.Errorf("slice length is negative: %v", n)
	}

	buf := make([]byte, n)
	_, err := p.file.Read(buf)
	if err != nil {
		return nil, err
	}
	return buf, nil
}

func (p *ProtobufFileReader) readLittleEndian64() (int64, error) {
	buf := make([]byte, 8)
	_, err := p.file.Read(buf)
	if err != nil {
		return 0, err
	}

	return int64(buf[0])<<0 | int64(buf[1])<<8 | int64(buf[2])<<16 | int64(buf[3])<<24 | int64(buf[4])<<32 | int64(buf[5])<<40 | int64(buf[6])<<48 | int64(buf[7])<<56, nil
}

func (p *ProtobufFileReader) readLittleEndian32() (int32, error) {
	buf := make([]byte, 4)
	_, err := p.file.Read(buf)
	if err != nil {
		return 0, err
	}

	return int32(buf[0])<<0 | int32(buf[1])<<8 | int32(buf[2])<<16 | int32(buf[3])<<24, nil
}

func (p *ProtobufFileReader) ReadRecord(num uint32) ([]byte, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if int(num) >= len(p.recordPointers) {
		return nil, fmt.Errorf("WeedingDiagnostics: Record number %v is out for range [%v]", num, len(p.recordPointers)-1)
	}
	offset := p.recordPointers[num]
	_, err := p.file.Seek(offset, io.SeekStart)
	if err != nil {
		return nil, err
	}

	len, err := p.readLittleEndian32()
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: Error while reading length, offset=%v, error=%v", offset, err)
		return nil, err
	}

	bytes, err := p.readBytes(len)
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: Error while reading bytes buffer, offset=%v, err=%v, len=%v", offset, err, len)
		return nil, err
	}

	return bytes, nil
}

func (p *ProtobufFileReader) NumRecords() int {
	return len(p.recordPointers)
}

type WeedingDiagnosticsUploadStateWatcher struct {
	EventTrigger
	state *WeedingDiagnosticsUploadState
	redis *redis.Client
}

func NewWeedingDiagnosticsUploadStateWatcher(state *WeedingDiagnosticsUploadState, redis *redis.Client) *WeedingDiagnosticsUploadStateWatcher {
	action := &WeedingDiagnosticsUploadStateWatcher{state: state, redis: redis}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *WeedingDiagnosticsUploadStateWatcher) Action() {
	vals, err := w.redis.HGetAll("weeding_diagnostics/uploads")
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: could not get uploads state, err=%v", err)
		return
	}
	progress, err := w.redis.HGetAll("weeding_diagnostics/upload_progress")
	if err != nil {
		logrus.Errorf("WeedingDiagnostics: could not get uploads progress, err=%v", err)
		return
	}

	newMap := make(map[string]*WeedingDiagnosticsUploadProgress)
	for name, status := range vals {
		var us frontend.UploadState = frontend.UploadState_NONE
		if status == "DONE" {
			us = frontend.UploadState_DONE
		} else if status != "NONE" {
			us = frontend.UploadState_IN_PROGRESS
		}
		var percent int = 0
		if pr, ok := progress[name]; ok {
			percent, _ = strconv.Atoi(pr)
		}
		newMap[name] = &WeedingDiagnosticsUploadProgress{UploadState: us, Percent: uint32(percent)}
	}

	var oldMap map[string]*WeedingDiagnosticsUploadProgress
	w.state.ReadOnCurrent(func() {
		oldMap = w.state.UploadStates
	})

	changed := false
	if len(oldMap) != len(newMap) {
		changed = true
	} else {
		for name, status := range newMap {
			if oldStatus, ok := oldMap[name]; !ok {
				changed = true
			} else {
				if status.UploadState != oldStatus.UploadState || status.Percent != oldStatus.Percent {
					changed = true
				}
			}
		}
	}

	if changed {
		w.state.WriteOnCurrent(func() {
			w.state.UploadStates = newMap
		})
	}
}

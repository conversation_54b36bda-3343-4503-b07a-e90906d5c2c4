package state

import (
	"context"
	"errors"
	"fmt"
	"math"
	"path"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

const (
	lastActiveCategoryCollectionNodeName = "last_active_category_collection_id"
)

type ProfileUpdateState struct {
	UnsyncedUpdates         bool
	UnsyncedUpdatesToActive bool
}

type CategoryCollectionCfgState struct {
	cfgState
	categoryCollections         []*category.CategoryCollection
	currentCropIDNode           config.Tree
	cropIDsNode                 config.Tree
	configClient                *config.ConfigClient
	cvMinLastUpdatedTimestampMs int64
	// Is the category collection in use by CV Runtime out of date?
	cvReloadRequired bool
	// Is the category collection synced and downloaded to be used by CV Runtime?
	cvReloadReady bool

	// used by ChipMan to determine if it needs to download and sync new chips
	profileUpdateState ProfileUpdateState
}

func NewCategoryCollectionCfgState(redisClient *redis.Client, rows map[int]*rows.RowClients, commanderNodeConfig *config.ConfigTree, configClient *config.ConfigClient) *CategoryCollectionCfgState {
	commanderNode := config.WrapConfigTree(commanderNodeConfig)
	state := &CategoryCollectionCfgState{
		cfgState: cfgState{
			ManagedStateImpl: ManagedStateImpl{name: "CategoryCollectionCfgState"}, redisClient: redisClient, rows: rows, profileType: frontend.ProfileType_CATEGORY_COLLECTION,
		},
		categoryCollections: make([]*category.CategoryCollection, 0),
		currentCropIDNode:   commanderNode.GetNode(currentCropIDNodeName),
		cropIDsNode:         commanderNode.GetNode(cropIDsNodeName),
		profileUpdateState:  ProfileUpdateState{UnsyncedUpdates: false, UnsyncedUpdatesToActive: false},
		configClient:        configClient,
	}

	data, err := ReadActiveCategoryCollectionId(redisClient)
	if err != nil {
		logrus.Warnf("Failed to get active category collection id: %v", err)
	} else {
		state.ActiveId = data
	}

	state.initialize()
	state.updateCropConfigLastActiveCategoryCollection()
	state.currentCropIDNode.RegisterCallback(func() {
		cropID := state.currentCropIDNode.GetStringValue()
		lastActiveCategoryCollectionID := state.cropIDsNode.GetNode(cropID).GetNode(lastActiveCategoryCollectionNodeName).GetStringValue()
		state.SetActiveCfg(lastActiveCategoryCollectionID)
	})
	return state
}

func (s *CategoryCollectionCfgState) updateCropConfigLastActiveCategoryCollection() {
	cropID := s.currentCropIDNode.GetStringValue()
	activeCategoryCollectionID := s.GetActiveCfg()

	prefix := configGetComputerConfigPrefix()
	catCollectionKey := path.Join(prefix, commanderNodeName, cropIDsNodeName, cropID, lastActiveCategoryCollectionNodeName)
	if err := s.configClient.SetStringValue(catCollectionKey, activeCategoryCollectionID); err != nil {
		logrus.Warnf("failed to set config key: %s value: %s - %v", catCollectionKey, activeCategoryCollectionID, err)
	}
}

func (s *CategoryCollectionCfgState) GetProfileUpdateState() (profileUpdateState ProfileUpdateState) {
	s.ReadOnCurrent(func() {
		profileUpdateState = s.profileUpdateState
	})
	return
}

func (s *CategoryCollectionCfgState) ResetProfileUpdateState() {
	changed := false
	s.ReadOnCurrent(func() {
		changed = s.profileUpdateState.UnsyncedUpdates || s.profileUpdateState.UnsyncedUpdatesToActive
	})
	if changed {
		s.WriteOnCurrent(func() {
			s.profileUpdateState.UnsyncedUpdates = false
			s.profileUpdateState.UnsyncedUpdatesToActive = false
		})
	}
}

func ReadActiveCategoryCollectionId(redisClient *redis.Client) (string, error) {
	return redisClient.ReadString(redis.CategoryCollectionActive, "")
}

func (s *CategoryCollectionCfgState) GetAllCfgs(ctx context.Context) (map[string]*category.CategoryCollection, error) {
	cfgs := make(map[string]*category.CategoryCollection)
	categoryCollections := make([]*category.CategoryCollection, 0)
	data, err := s.redisClient.HGetAllWithContext(ctx, redis.CategoryCollectionCfgs)
	if err != nil {
		return cfgs, err
	}

	for id, cfg := range data {
		var categoryCollection category.CategoryCollection
		err := proto.Unmarshal([]byte(cfg), &categoryCollection)
		if err != nil {
			logrus.Warnf("Failed to unmarshal category collection %v: %v", id, err)
			continue
		}
		categoryCollections = append(categoryCollections, &categoryCollection)
		cfgs[id] = &categoryCollection
	}

	var collections []*category.CategoryCollection
	s.ReadOnCurrent(func() {
		collections = s.categoryCollections
	})

	if !categoryCollectionsEqual(categoryCollections, collections) {
		s.WriteOnCurrent(func() {
			s.categoryCollections = categoryCollections
		})
	}

	return cfgs, nil
}

func categoryCollectionsEqual(a, b []*category.CategoryCollection) bool {
	if len(a) != len(b) {
		return false
	}

	for _, collection1 := range a {
		foundMatch := false
		for _, collection2 := range b {
			if collection1.Id == collection2.Id {
				if categoryCollectionEqual(collection1, collection2) {
					foundMatch = true
				}
			}
		}

		if !foundMatch {
			return false
		}
	}

	return true
}

func categoryCollectionEqual(a, b *category.CategoryCollection) bool {
	if a.Id == b.Id &&
		a.Name == b.Name &&
		a.Protected == b.Protected &&
		a.CustomerId == b.CustomerId &&
		len(a.CategoryIds) == len(b.CategoryIds) {
		for _, category1 := range a.CategoryIds {
			foundMatchingCategory := false
			for _, category2 := range b.CategoryIds {
				if category1 == category2 {
					foundMatchingCategory = true
					break
				}
			}

			if !foundMatchingCategory {
				return false
			}
		}
	} else {
		return false
	}

	return true
}

func (s *CategoryCollectionCfgState) GetNextCategoryCollectionsData(ctx context.Context, timestampMs int64) (result bool, categoryCollections []*category.CategoryCollection, ts int64) {
	result = s.ReadOnNext(ctx, timestampMs, func() {
		categoryCollections = s.categoryCollections
	})

	ts = s.GetTimestampMs()
	return
}

func (s *CategoryCollectionCfgState) GetNextActiveId(ctx context.Context, timestampMs int64) (result bool, id string, ts int64, reloadRequired bool, lastUpdatedTimestampMs int64) {
	result = s.ReadOnNext(ctx, timestampMs, func() {
		id = s.ActiveId
		reloadRequired = s.cvReloadRequired && s.cvReloadReady
		lastUpdatedTimestampMs = s.cvMinLastUpdatedTimestampMs
	})

	ts = s.GetTimestampMs()
	return
}

func (s *CategoryCollectionCfgState) SaveProfileFromRoSySync(profile *robot_syncer.GetProfileResponse) (bool, error) {
	switch r := profile.Profile.(type) {
	case *robot_syncer.GetProfileResponse_CategoryCollection:
		return true, s.saveCategoryCollectionConfig(r.CategoryCollection, s.GetActiveCfg() == r.CategoryCollection.Id)
	default:
		return false, nil
	}
}

func (s *CategoryCollectionCfgState) SaveProfileFromSync(profile *portal.GetProfileResponse) (bool, error) {
	return false, errors.New("SaveProfileFromSync Not implemented by Category Collection Profile -- Use RoSySync")
}

func (s *CategoryCollectionCfgState) saveCategoryCollectionConfig(cfg *category.CategoryCollection, setActive bool) error {
	activeCollectionId := s.GetActiveCfg()
	data, err := proto.Marshal(cfg)
	if err != nil {
		logrus.Warnf("Failed to marshal protobuf data err: %v", err)
		return err
	}

	// Before saving to redis, check if the category collection exists already,
	// is active currently, or if it was changed in any way. If the collection
	// is new or was changed, we need to trigger Chip Manager to download any
	// new chips. If the collection is active, we need to notify the CVRuntimeClients
	// to reload the category AFTER all chips are downloaded and synced.
	unsyncedUpdates := false
	unsyncedUpdatesToActive := false
	existingCategoryCollection, err := s.redisClient.LoadCategoryCollectionConfig(cfg.Id)
	if err != nil {
		unsyncedUpdates = true
		unsyncedUpdatesToActive = cfg.Id == activeCollectionId
	} else {
		if !categoryCollectionEqual(cfg, existingCategoryCollection) {
			unsyncedUpdates = true
			unsyncedUpdatesToActive = cfg.Id == activeCollectionId
		}
	}

	err = s.redisClient.HSet(redis.CategoryCollectionCfgs, cfg.Id, string(data))
	if err != nil {
		logrus.Warnf("Failed to save cfg to redis %v", err)
		return err
	}

	if setActive {
		err = s.SetActiveCfg(cfg.Id)
		if err != nil {
			logrus.Warnf("Failed to set %v as active: %v", cfg.Id, err)
			return err
		}
	}

	if cfg.Id == activeCollectionId {
		if !setActive {
			// still need to notify that a change to active has occurred
			s.Notify()
			return nil
		}
	}

	if unsyncedUpdates {
		s.WriteOnCurrent(func() {
			s.profileUpdateState.UnsyncedUpdates = true
			if unsyncedUpdatesToActive {
				s.profileUpdateState.UnsyncedUpdatesToActive = true
			}
		})
	}

	return nil
}

func (s *CategoryCollectionCfgState) SetActiveCfg(id string) error {
	curr := s.GetActiveCfg()
	if id == curr {
		s.Notify()
		return nil
	}

	if id != "" {
		exists, err := s.redisClient.HExists(redis.CategoryCollectionCfgs, id)
		if err != nil {
			return err
		}
		if !exists {
			return fmt.Errorf("No category collection with ID %v exists, cannot set it active.", id)
		}
	}

	err := s.redisClient.WriteString(redis.CategoryCollectionActive, id)
	if err != nil {
		return err
	}
	s.WriteOnCurrent(func() {
		s.ActiveId = id
	})

	s.updateCropConfigLastActiveCategoryCollection()
	s.ReloadCVCategoryCollection()

	return nil
}

func (s *CategoryCollectionCfgState) DeleteProfileFromSync(id string) error {
	return s.deleteCategoryCollectionConfig(id, id) // intentionally don't send new active as we shouldn't delete active from sync
}

func (s *CategoryCollectionCfgState) deleteCategoryCollectionConfig(id string, newActiveId string) error {
	curr := s.GetActiveCfg()
	if id == curr {
		if newActiveId != id {
			err := s.SetActiveCfg(newActiveId)
			if err != nil {
				return err
			}
		} else {
			logrus.Warnf("Cannot delete active cfg.")
			return status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot delete %v as it is currently active.", id))
		}
	}
	err := s.redisClient.HDel(redis.CategoryCollectionCfgs, id)
	if err != nil {
		logrus.Warnf("Failed to delete category collection cfg %v, err: %v", id, err)
		return err
	}

	// skip write to unsyncedUpdates as we are not deleting active
	s.Notify()
	return nil
}

func (s *CategoryCollectionCfgState) LoadProfileForRoSySync(id string, req *robot_syncer.UploadProfileRequest) error {
	categoryCollection, err := s.redisClient.LoadCategoryCollectionConfig(id)
	if err != nil {
		return err
	}

	if categoryCollection.Protected {
		return fmt.Errorf("Category Collection %s is protected and cannot be uploaded", id)
	}

	req.Profile = &robot_syncer.UploadProfileRequest_CategoryCollection{
		CategoryCollection: categoryCollection,
	}

	return nil
}

func (s *CategoryCollectionCfgState) LoadProfileForSync(id string, req *portal.UploadProfileRequest) error {
	return errors.New("LoadProfileForSync Not implemented by Category Collection Profile -- Use RoSyLoad")
}

func (s *CategoryCollectionCfgState) SetActiveProfileFromSync(id string) error {
	return s.SetActiveCfg(id)
}

func (s *CategoryCollectionCfgState) ReloadCVCategoryCollection() error {
	for _, rowClients := range s.rows {
		for _, cvRuntimeClient := range rowClients.CVRuntimeClients {
			_, err := cvRuntimeClient.ReloadCategoryCollection()
			if err != nil {
				return err
			}
		}
	}
	s.WriteOnCurrent(func() {
		s.cvReloadRequired = false
	})
	return nil
}

func (s *CategoryCollectionCfgState) UpdateCVStatus() error {
	activeCategoryCollectionId := s.GetActiveCfg()
	cvMinLastUpdatedTimestampMs := int64(math.MaxInt64)
	activeCategoryCollectionIdInSync := true
	for _, rowClients := range s.rows {
		for _, cvRuntimeClient := range rowClients.CVRuntimeClients {
			categoryCollectionInfo, err := cvRuntimeClient.GetCategoryCollection()
			if err != nil {
				return err
			}
			if categoryCollectionInfo.LastUpdatedTimestampMs < cvMinLastUpdatedTimestampMs {
				cvMinLastUpdatedTimestampMs = categoryCollectionInfo.LastUpdatedTimestampMs
			}
			if categoryCollectionInfo.CategoryCollectionId != activeCategoryCollectionId {
				activeCategoryCollectionIdInSync = false
			}
		}
	}

	profileSyncData, err := s.redisClient.ReadProfileSyncData(activeCategoryCollectionId)
	if err != nil {
		return err
	} else if profileSyncData == nil {
		return nil
	}
	cvReloadRequired := profileSyncData.LastUpdatedTsMs > cvMinLastUpdatedTimestampMs || !activeCategoryCollectionIdInSync

	changed := false
	s.ReadOnCurrent(func() {
		changed = s.cvMinLastUpdatedTimestampMs != cvMinLastUpdatedTimestampMs || s.cvReloadRequired != cvReloadRequired
	})
	if changed {
		s.WriteOnCurrent(func() {
			s.cvMinLastUpdatedTimestampMs = cvMinLastUpdatedTimestampMs
			s.cvReloadRequired = cvReloadRequired
		})
	}
	return nil
}

func (s *CategoryCollectionCfgState) SetCVReloadReady(ready bool) {
	changed := false
	s.ReadOnCurrent(func() {
		changed = s.cvReloadReady != ready
	})
	if changed {
		s.WriteOnCurrent(func() {
			s.cvReloadReady = ready
		})
	}
}

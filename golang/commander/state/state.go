package state

import (
	"context"
	"sync"
	"time"
)

type ManagedState interface {
	GetTimestampMs() int64
	Terminate()
	ReadOnCurrent(readOnCurrent func())
	ReadOnNext(ctx context.Context, timestampMs int64, readOnNext func()) bool
	WriteOnCurrent(writeOnCurrent func())
	WriteOnCurrentWithTimestamp(writeOnCurrent func(), timestampMs int64)
}

type ManagedStateImpl struct {
	name        string
	timestampMs int64
	terminated  bool
	sync.RWMutex
	// notifyCh is closed and replaced with a new channel whenever the managed
	// state is written. Readers waiting on <-notifyCh should make sure to load
	// notify<PERSON>h again before each wait (holding the mutex) in case the
	// underlying channel has changed.
	notifyCh         chan struct{}
	watcherNotifiers []chan struct{}
}

func (state *ManagedStateImpl) Terminate() {
	state.WriteOnCurrent(func() {
		state.terminated = true
	})
}

func (state *ManagedStateImpl) GetTimestampMs() int64 {
	return state.timestampMs
}

func (state *ManagedStateImpl) initialize() {
	state.Lock()
	defer state.Unlock()
	state.notifyCh = make(chan struct{})
	state.watcherNotifiers = make([]chan struct{}, 0)
	state.terminated = false
	state.timestampMs = time.Now().UnixMilli()
}

func (state *ManagedStateImpl) ReadOnCurrent(readOnCurrent func()) {
	state.RLock()
	defer state.RUnlock()
	readOnCurrent()
}

func (state *ManagedStateImpl) ReadOnNext(ctx context.Context, timestampMs int64, readOnNext func()) bool {
	return state.ReadOnNextSpecifyTs(ctx, timestampMs, readOnNext, state.GetTimestampMs)
}
func (state *ManagedStateImpl) ReadOnNextSpecifyTs(ctx context.Context, timestampMs int64, readOnNext func(), ts_func func() int64) bool {
	for {
		state.RLock()
		ch := state.notifyCh
		if state.terminated {
			state.RUnlock()
			return false
		} else if ts_func() <= timestampMs {
			state.RUnlock()
		} else {
			break // still holding the read lock
		}

		// block until next update, lock must be released
		select {
		case <-ctx.Done():
			return false
		case <-ch:
			continue
		}
	}
	defer state.RUnlock()
	readOnNext()
	return true
}
func (state *ManagedStateImpl) WriteOnCurrent(writeOnCurrent func()) {
	state.WriteOnCurrentWithTimestamp(writeOnCurrent, time.Now().UnixMilli())
}
func (state *ManagedStateImpl) ConditionalWriteOnCurrent(writeOnCurrent func() bool) {
	state.ConditionalWriteOnCurrentWithTimestamp(writeOnCurrent, time.Now().UnixMilli())
}

func (state *ManagedStateImpl) WriteOnCurrentWithTimestamp(writeOnCurrent func(), timestampMs int64) {
	state.ConditionalWriteOnCurrentWithTimestamp(func() bool {
		writeOnCurrent()
		return true
	}, timestampMs)
}
func (state *ManagedStateImpl) ConditionalWriteOnCurrentWithTimestamp(writeOnCurrent func() bool, timestampMs int64) {
	state.Lock()
	defer state.Unlock()
	changed := writeOnCurrent()
	if changed {
		if state.timestampMs >= timestampMs {
			state.timestampMs++
		} else {
			state.timestampMs = timestampMs
		}
		close(state.notifyCh)
		state.notifyCh = make(chan struct{})
		state.notifyWatchers()
	}
}
func (state *ManagedStateImpl) AddWatcher() chan struct{} {
	watcherNotifier := make(chan struct{}, 1)
	state.Lock()
	defer state.Unlock()
	state.watcherNotifiers = append(state.watcherNotifiers, watcherNotifier)
	return watcherNotifier
}

// helper func called from public func that already has lock
func (state *ManagedStateImpl) notifyWatchers() {
	// non-blocking, put signal on channel if possible
	for _, ch := range state.watcherNotifiers {
		select {
		case ch <- struct{}{}:
		default:
		}
	}
}

type EnforcedState struct {
	Enforced      bool
	Current       bool
	OffsyncCycles uint32
}

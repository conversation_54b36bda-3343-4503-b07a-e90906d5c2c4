package state

import (
	"errors"
	"fmt"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/software_manager"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

const (
	InvalidTag = "Invalid"
)

var (
	softwareVersionInfoGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "software_version",
		Help: "Robot software version info metric.",
	}, []string{"row", "version", "target", "previous"})
)

type SoftwareVersion struct {
	Tag       string
	Available bool
	Ready     bool
}

func (s *SoftwareVersion) IsValid() bool {
	if s == nil {
		return false
	}
	return s.Tag != "" && s.Tag != InvalidTag && s.Available && s.Ready
}

type SoftwareState struct {
	Current  *SoftwareVersion
	Target   *SoftwareVersion
	Previous *SoftwareVersion
}

type HostSoftwareState struct {
	SoftwareState
	Updating bool
	Active   bool
}

type OverallSoftwareState struct {
	ManagedStateImpl
	Summary  *SoftwareState
	Hosts    map[uint32]*HostSoftwareState
	Updating bool
}

func NewOverallSoftwareState() *OverallSoftwareState {
	state := &OverallSoftwareState{
		ManagedStateImpl: ManagedStateImpl{name: "OverallSoftwareState"},
		Updating:         false,
		Hosts:            map[uint32]*HostSoftwareState{},
		Summary: &SoftwareState{
			Current: &SoftwareVersion{
				Tag:       InvalidTag,
				Ready:     false,
				Available: false,
			},
			Target: &SoftwareVersion{
				Tag:       InvalidTag,
				Ready:     false,
				Available: false,
			},
			Previous: &SoftwareVersion{
				Tag:       InvalidTag,
				Ready:     false,
				Available: false,
			},
		},
	}
	state.initialize()
	return state
}

func (s *OverallSoftwareState) IsHostUpdating() bool {
	res := false
	for _, host := range s.Hosts {
		res = res || host.Updating
	}
	return res
}

func (s *OverallSoftwareState) HostsCurrentVersionIsValid() bool {
	res := true
	for _, host := range s.Hosts {
		res = res && host.Current.IsValid()
	}
	return res
}

type SoftwareWatcher struct {
	EventTrigger
	softwareState *OverallSoftwareState
	clients       []*hosts.HostClients
	logger        *logrus.Entry
}

func NewSoftwareWatcher(softwareState *OverallSoftwareState, hosts []*hosts.HostClients) *SoftwareWatcher {
	action := &SoftwareWatcher{
		softwareState: softwareState,
		clients:       hosts,
		logger:        logrus.WithField("component", "SoftwareWatcher"),
	}
	action.triggerChannel = make(chan bool)
	return action
}

func fillVersionConditional(summaryVersion *SoftwareVersion, response *SoftwareVersion) {
	if summaryVersion.Tag == InvalidTag { // once set to invalid, it should not change
		return
	} else if response == nil || response.Tag == "" {
		summaryVersion.Tag = InvalidTag
		summaryVersion.Available = false
		summaryVersion.Ready = false
	} else if summaryVersion.Tag == "" { // summary is yet to be set
		summaryVersion.Tag = response.Tag
		summaryVersion.Ready = response.Ready
		summaryVersion.Available = response.Available
	} else if summaryVersion.Tag != response.Tag { // non empty and different
		summaryVersion.Ready = false
		summaryVersion.Available = false
	} else { // summaryVersion.Tag == response.Tag
		summaryVersion.Ready = summaryVersion.Ready && response.Ready
		summaryVersion.Available = summaryVersion.Available && response.Available
	}
}

func isSameVersion(v1 *SoftwareVersion, v2 *SoftwareVersion) bool {
	if v1 == nil || v2 == nil {
		return v1 == v2
	}

	return v1.Available == v2.Available && v1.Ready == v2.Ready && v1.Tag == v2.Tag
}

func isSameState(v1 *SoftwareState, v2 *SoftwareState) bool {
	if v1 == nil || v2 == nil {
		return v1 == v2
	}

	return isSameVersion(v1.Current, v2.Current) && isSameVersion(v1.Target, v2.Target) && isSameVersion(v1.Previous, v2.Previous)
}

func isSameHostState(v1 map[uint32]*HostSoftwareState, v2 map[uint32]*HostSoftwareState) bool {
	if v1 == nil && v2 == nil {
		return true
	} else if v1 == nil || v2 == nil {
		return false
	}

	for key, val1 := range v1 {
		if val2, ok := v2[key]; ok {
			if isSameState(&val1.SoftwareState, &val2.SoftwareState) && val1.Updating == val2.Updating && val1.Active == val2.Active {
				continue
			} else {
				return false
			}
		} else {
			return false
		}
	}

	return len(v1) == len(v2)
}

func processVersionSummaries(responses map[uint32]*software_manager.VersionSummaryReply) (*SoftwareState, map[uint32]*HostSoftwareState) {
	hostSummaries := map[uint32]*HostSoftwareState{}
	for pcIdx, response := range responses {
		summary := &HostSoftwareState{}
		if response == nil {
			summary.Current = &SoftwareVersion{}
			summary.Target = &SoftwareVersion{}
			summary.Previous = &SoftwareVersion{}
			summary.Updating = false
			summary.Active = false
		} else {
			summary.Current = &SoftwareVersion{
				Tag:       response.Current.Tag,
				Ready:     response.Current.Ready,
				Available: response.Current.Available,
			}
			summary.Target = &SoftwareVersion{
				Tag:       response.Target.Tag,
				Ready:     response.Target.Ready,
				Available: response.Target.Available,
			}
			summary.Previous = &SoftwareVersion{
				Tag:       response.Previous.Tag,
				Ready:     response.Previous.Ready,
				Available: response.Previous.Available,
			}
			summary.Updating = response.Updating
			summary.Active = true
		}
		hostSummaries[pcIdx] = summary
	}

	overallSummary := &SoftwareState{
		Current:  &SoftwareVersion{},
		Target:   &SoftwareVersion{},
		Previous: &SoftwareVersion{},
	}

	// assume commander is the expected source of truth for the overall state
	// start by populating the overall summary with commanders state
	if commanderState, ok := hostSummaries[0]; ok { // commander state should always exist but just in case
		fillVersionConditional(overallSummary.Current, commanderState.Current)
		fillVersionConditional(overallSummary.Target, commanderState.Target)
		fillVersionConditional(overallSummary.Previous, commanderState.Previous)
	}

	for pcIdx, hostSummary := range hostSummaries {
		if pcIdx == 0 {
			continue // skip the commander
		}
		fillVersionConditional(overallSummary.Current, hostSummary.Current)
		fillVersionConditional(overallSummary.Target, hostSummary.Target)
		fillVersionConditional(overallSummary.Previous, hostSummary.Previous)
	}

	return overallSummary, hostSummaries
}

func (w *SoftwareWatcher) updateMetrics(map[uint32]*HostSoftwareState) {
	for key, val := range w.softwareState.Hosts {
		softwareVersionInfoGauge.WithLabelValues(fmt.Sprint(key), val.Current.Tag, val.Target.Tag, val.Previous.Tag).Set(1)
	}
}

func (w *SoftwareWatcher) Action() {
	responses := map[uint32]*software_manager.VersionSummaryReply{}
	errs := make([]error, 0)

	type result struct {
		pcId  uint32
		reply *software_manager.VersionSummaryReply
		err   error
	}
	resultChan := make(chan result, len(w.clients))

	var wg sync.WaitGroup
	wg.Add(len(w.clients))

	for _, client := range w.clients {
		go func(c *hosts.HostClients) {
			defer wg.Done()
			summaryReply, err := c.SoftwareManagerClient.GetVersionsSummary()
			resultChan <- result{
				pcId:  c.PcId,
				reply: summaryReply,
				err:   err,
			}
		}(client)
	}

	wg.Wait()
	close(resultChan)

	for res := range resultChan {
		if res.err != nil {
			errs = append(errs, res.err)
			responses[res.pcId] = nil
		} else {
			responses[res.pcId] = res.reply
		}
	}

	if len(errs) > 0 {
		combinedError := errors.Join(errs...)
		w.logger.WithError(combinedError).Warn("Errors occurred while getting software version summaries")
	}

	summary, responseSummary := processVersionSummaries(responses)
	w.updateMetrics(responseSummary)

	w.softwareState.ConditionalWriteOnCurrent(func() bool {
		if !isSameState(w.softwareState.Summary, summary) || !isSameHostState(w.softwareState.Hosts, responseSummary) {
			w.softwareState.Summary = summary
			w.softwareState.Hosts = responseSummary
			return true
		}
		return false
	})
}

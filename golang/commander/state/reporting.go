package state

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	log "github.com/sirupsen/logrus"
)

const redisLocationHistoryKey = "reporting/location_history"

type ReportingState struct {
	ManagedStateImpl
	LocationHistory map[int64]*frontend.Location
	redis           *redis.Client
}

func NewReportingState(redis *redis.Client) *ReportingState {
	state := &ReportingState{ManagedStateImpl: ManagedStateImpl{name: "ReportingState"}, redis: redis}

	// load location history
	state.LocationHistory = map[int64]*frontend.Location{}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	rawLocationHistory, err := redis.HGetAllWithContext(ctx, redisLocationHistoryKey)
	cancel()
	if err == nil {
		for rawKey, rawPoint := range rawLocationHistory {
			point := &frontend.Location{}
			pointError := json.Unmarshal([]byte(rawPoint), &point)
			validationErr := ValidatePoint(point)
			unixMilli, keyError := strconv.ParseInt(rawKey, 10, 64)
			if pointError != nil || keyError != nil || validationErr != nil {
				// corrupt point, purge
				ctx, cancel := context.WithTimeout(context.Background(), time.Second)
				state.redis.HDelWithContext(ctx, redisLocationHistoryKey, rawKey)
				cancel()
				continue
			}
			state.LocationHistory[unixMilli] = point
		}
	}

	state.initialize()
	// state.pruneOldLocationHistory()
	return state
}

func (r *ReportingState) AddLocationHistory(point *frontend.Location) {
	if err := ValidatePoint(point); err != nil {
		log.WithError(err).Errorf("failed to addLocationHistory invalid point %v", point)
		return
	}

	// persist to redis
	serializedPoint, err := json.Marshal(point)
	if err != nil {
		log.WithError(err).Warnf("Failed to serialize location history at %d (%f, %f) for persistence:", point.Ts.TimestampMs, point.Latitude, point.Longitude)
		return
	}

	// persist to memory
	r.WriteOnCurrent(func() { r.LocationHistory[point.Ts.TimestampMs] = point })

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	r.redis.HSetWithContext(ctx, redisLocationHistoryKey, strconv.FormatInt(point.Ts.TimestampMs, 10), string(serializedPoint))

	r.pruneOldLocationHistory()
}

func (r *ReportingState) pruneOldLocationHistory() {
	var locationHistory map[int64]*frontend.Location
	r.ReadOnCurrent(func() {
		locationHistory = r.LocationHistory
	})

	r.WriteOnCurrent(func() {
		now := time.Now()
		for unixMilli := range locationHistory {
			tm := time.UnixMilli(unixMilli)
			if !IsDateEqual(tm, now) {
				// delete from memory
				delete(r.LocationHistory, unixMilli)

				ctx, cancel := context.WithTimeout(context.Background(), time.Second)
				// delete from redis
				r.redis.HDelWithContext(ctx, redisLocationHistoryKey, strconv.FormatInt(unixMilli, 10))
				cancel()
			}
		}
	})
}

type ReportingWatcher struct {
	EventTrigger
	common         *config.ConfigTree
	hardware       *hardware_manager.HardwareManagerClient
	reporting      *ReportingState
	weeding        *WeedingState
	mappingEnabled bool
}

func NewReportingWatcher(
	common *config.ConfigTree,
	hardware *hardware_manager.HardwareManagerClient,
	reporting *ReportingState,
	weeding *WeedingState,
) *ReportingWatcher {
	action := &ReportingWatcher{
		common:    common,
		hardware:  hardware,
		reporting: reporting,
		weeding:   weeding,
	}
	action.triggerChannel = make(chan bool)

	action.mappingEnabled, _ = config.FeatureFlagEnabled(action.common, config.FeatureFlagMapping, func(newEnabled bool) {
		action.mappingEnabled = newEnabled
	})
	return action
}

func (r *ReportingWatcher) Action() {
	if r.mappingEnabled {
		point := &frontend.Location{
			Ts: &frontend.Timestamp{TimestampMs: int64(time.Now().UnixMilli())},
		}

		GPSData, err := r.hardware.GetGPSData(true)
		if err != nil || GPSData == nil || GPSData.Lla == nil {
			return
		} else {
			point.Latitude = float32(GPSData.Lla.Lat)
			point.Longitude = float32(GPSData.Lla.Lng)
			point.Altitude = float32(GPSData.Lla.Alt)
		}

		r.weeding.ReadOnCurrent(func() {
			point.IsWeeding = r.weeding.IsWeeding
		})

		r.reporting.AddLocationHistory(point)
	}
}

func IsDateEqual(a, b time.Time) bool {
	ya, ma, da := a.Date()
	yb, mb, db := b.Date()
	return ya == yb && ma == mb && da == db
}

func ValidatePoint(point *frontend.Location) error {
	switch {
	case point.GetLatitude() < -90 || point.GetLatitude() > 90:
		return fmt.Errorf("invalid latitude %f", point.GetLatitude())
	case point.GetLongitude() < -180 || point.GetLongitude() > 180:
		return fmt.Errorf("invalid longitude %f", point.GetLongitude())
	}
	return nil
}

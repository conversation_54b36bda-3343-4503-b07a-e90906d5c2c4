package state

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDataCaptureStep_IsNot(t *testing.T) {
	tests := []struct {
		name     string
		step     DataCaptureStep
		steps    []DataCaptureStep
		expected bool
	}{
		{
			"is",
			DataCaptureStepNew,
			[]DataCaptureStep{DataCaptureStepNew},
			false,
		},
		{
			"is multiple",
			DataCaptureStepNew,
			[]DataCaptureStep{DataCaptureStepCapturing, DataCaptureStepNew},
			false,
		},
		{
			"IsNot",
			DataCaptureStepNew,
			[]DataCaptureStep{DataCaptureStepCapturing},
			true,
		},
		{
			"IsNot multiple",
			DataCaptureStepNew,
			[]DataCaptureStep{DataCaptureStepCapturing, DataCaptureStepCapturePaused},
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := test.step.IsNot(test.steps...)
			assert.Equal(t, test.expected, got)
		})
	}
}

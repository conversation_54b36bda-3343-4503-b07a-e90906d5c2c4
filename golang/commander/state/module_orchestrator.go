package state

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"

	"context"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/module_server_client"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/sirupsen/logrus"
)

const (
	moduleServerPort     = 61016
	heartbeatTimeout     = 5 * time.Second
	modulesKey           = "module_orchestration/modules"
	moduleErrorFmt       = "module error [serial: %v, id: %v]: %v"
	UnassignedModuleID   = uint32(0)
	UnsetSerialPrefix    = "UNSET_"
	blockedUnsetsTimeout = 1 * time.Minute
)

type RedisMOClient interface {
	HSetWithContext(ctx context.Context, hash string, val ...any) error
	HGetWithContext(ctx context.Context, hash string, key string) (string, error)
	HGetAllWithContext(ctx context.Context, hash string) (map[string]string, error)
	HDelWithContext(ctx context.Context, key string, member ...string) error
}

type Module struct {
	ID     uint32 `json:"id"`
	Serial string `json:"serial"`
	MCBIp  string `json:"mcb_ip"`
	PCIp   string `json:"pc_ip"`
	IPMIIp string `json:"ipmi_ip"`
}

func (a *Module) ipsEquals(b *Module) bool {
	if a == nil || b == nil {
		return false
	}
	return a.MCBIp == b.MCBIp && a.PCIp == b.PCIp && a.IPMIIp == b.IPMIIp
}

func (a *Module) modulesEquals(b *Module) bool {
	if a == nil || b == nil {
		return false
	}
	return a.ID == b.ID && a.Serial == b.Serial && a.ipsEquals(b)
}

type TTLModule struct {
	Module
	HeartbeatCh chan struct{}
}

type TimeExpiringSet struct {
	data map[string]time.Time
	dur  time.Duration
}

func NewTimeExpiringSet(dur time.Duration) *TimeExpiringSet {
	return &TimeExpiringSet{
		data: make(map[string]time.Time),
		dur:  dur,
	}
}

func (s *TimeExpiringSet) cleanup() {
	// remove expired entries
	now := time.Now()
	for k, v := range s.data {
		if now.Sub(v) > s.dur {
			delete(s.data, k)
		}
	}
}

func (s *TimeExpiringSet) Add(key string) {
	s.cleanup()
	s.data[key] = time.Now()
}

func (s *TimeExpiringSet) Contains(key string) bool {
	s.cleanup()
	_, ok := s.data[key]
	return ok
}

type ModuleOrchestratorState struct {
	ManagedStateImpl // there is a lot of contention on the state lock, use carefully
	stopCtx          context.Context
	wg               sync.WaitGroup

	// redisModules used outside of the state lock but only in the single-threaded redis control loop
	redisClient        RedisMOClient
	redisModules       map[string]*Module // serial -> Module, in memory redis state
	triggerRedisSyncCh chan struct{}

	AssignedModules map[string]*Module // serial -> Module, in memory state of the assigned modules
	inUseIDs        map[uint32]string  // id to serial for quick lookup taken ids

	// modules that are only known through heartbeats, they are removed after a timeout
	ActiveModules      map[string]*TTLModule // serial -> Module
	UnsetSerialModules map[string]*TTLModule // serial -> Module
	blockedUnsets      *TimeExpiringSet      // keeps track of unset serials that have had their serial set to avoid reprocessing heartbeats. Has timeout so they can be retried after a while and the list doesn't grow indefinitely at manufacturing where they don't restart commander often.

	robotDefinitionState          *RobotDefinitionState
	triggerModulesMatchRobotDefCh chan struct{} // used to trigger comparison with the robot definition state
	assignedModulesMatchRobotDef  atomic.Bool
	connector                     *UploadTriggerConnector

	logger *logrus.Entry
}

func NewModuleOrchestratorState(stopCtx context.Context, redisClient *redis.Client, robotDefinitionState *RobotDefinitionState, connector *UploadTriggerConnector) *ModuleOrchestratorState {
	s := &ModuleOrchestratorState{
		ManagedStateImpl:   ManagedStateImpl{name: "ModuleOrchestratorState"},
		stopCtx:            stopCtx,
		redisClient:        redisClient,
		redisModules:       make(map[string]*Module),
		triggerRedisSyncCh: make(chan struct{}, 1),

		ActiveModules:      make(map[string]*TTLModule),
		UnsetSerialModules: make(map[string]*TTLModule),
		AssignedModules:    make(map[string]*Module),
		inUseIDs:           make(map[uint32]string),
		blockedUnsets:      NewTimeExpiringSet(blockedUnsetsTimeout),

		robotDefinitionState:          robotDefinitionState,
		triggerModulesMatchRobotDefCh: make(chan struct{}, 1),
		assignedModulesMatchRobotDef:  atomic.Bool{},
		connector:                     connector,

		logger: logrus.WithField("module", "ModuleOrchestratorState"),
	}
	s.logger.Info("Initializing ModuleOrchestratorState")
	s.init()
	s.logger.Info("ModuleOrchestratorState initialized")
	return s
}

func (s *ModuleOrchestratorState) init() {
	s.ManagedStateImpl.initialize()
	err := s.fillAssignedModulesFromRedis()
	if err != nil {
		s.logger.WithError(err).Error("failed to fill assigned modules from redis")
	}
	s.compareAssignedModulesToRobotDef()
	s.wg.Add(2)
	go s.redisControlLoop()
	go s.modulesMatchRobotDefControlLoop()
}

func (s *ModuleOrchestratorState) Terminate() {
	// wait for all the loops
	s.wg.Wait()
}

/*
 * Thread-safe public accessors
 */

func (s *ModuleOrchestratorState) GetAssignedIdSerialMap() map[uint32]string {
	ids_copy := make(map[uint32]string)
	s.ReadOnCurrent(func() {
		for id, sn := range s.inUseIDs {
			ids_copy[id] = sn
		}
	})
	return ids_copy
}

func (s *ModuleOrchestratorState) AssignedModulesMatchRobotDef() bool {
	return s.assignedModulesMatchRobotDef.Load()
}

func (s *ModuleOrchestratorState) GetLastKnownDHCPIPs() map[uint32]string {
	ips := make(map[uint32]string)
	s.ReadOnCurrent(func() {
		for _, m := range s.AssignedModules {
			ips[m.ID] = m.PCIp
		}
	})
	return ips
}

/*
 * RPC methods
 */

func (s *ModuleOrchestratorState) HandleHeartbeat(module *Module) (err error) {
	// get a module id and serial
	// cases:
	// 1. module is missing a serial number
	// 2. module is missing an id
	//   - if an id is set for this serial, set it
	// 3. module has a serial number and id
	//   - if the serial number is different from the one set for this id, invalidate the module

	if module.Serial == "" {
		// todo: this should be an alarm
		s.logger.Errorf("Module missing serial number: %v", module)
		return fmt.Errorf(moduleErrorFmt, "", module.ID, "missing serial number")
	}

	syncRedis := false
	expectedID := UnassignedModuleID
	// inside critical section needs to be very fast, no logging or blocking calls
	s.ConditionalWriteOnCurrent(func() bool {
		assignedModule, assigned := s.AssignedModules[module.Serial]
		if assigned {
			expectedID = assignedModule.ID
			if module.ID != assignedModule.ID {
				return false // reject the heartbeat, we need to fix the id first
			}
			if !assignedModule.ipsEquals(module) {
				// ips have changed, need to update orchestrator state
				assignedModule.MCBIp = module.MCBIp
				assignedModule.PCIp = module.PCIp
				assignedModule.IPMIIp = module.IPMIIp
				syncRedis = true
			}
		} else {
			if module.ID != UnassignedModuleID {
				// this module was not assigned but has an id, reset it to unassigned
				expectedID = UnassignedModuleID
				return false // reject the heartbeat, we need to clear the id first
			}
		}

		if strings.HasPrefix(module.Serial, UnsetSerialPrefix) {
			if s.blockedUnsets.Contains(module.Serial) {
				return false // don't process this heartbeat, it was already moved to a real serial
			}
			ret := s.createOrUpdateTTLModule(s.UnsetSerialModules, module)
			// todo: alarm
			return ret
		} else {
			return s.createOrUpdateTTLModule(s.ActiveModules, module)
		}
	})

	if module.ID != expectedID {
		s.logger.Warnf("Module ID mismatch for %v, expected: %v, got: %v", module.Serial, expectedID, module.ID)
		module.ID = expectedID
		setErr := s.setModuleIdentity(*module)
		if setErr != nil {
			return errors.New("module has incorrect ID")
		}
	}

	if syncRedis {
		s.triggerRedisSync()
	}

	return nil
}

func (s *ModuleOrchestratorState) AssignModule(id uint32, serial string) (err error) {
	if id == UnassignedModuleID {
		err = fmt.Errorf(moduleErrorFmt, serial, id, "cannot assign module to unassigned id, use ClearModuleAssignment")
		return err
	}

	s.logger.Infof("Assigning module: id=%v, serial=%v", id, serial)

	var toSet Module
	changed := false
	s.ConditionalWriteOnCurrent(func() bool {
		if ownerSerial, ok := s.inUseIDs[id]; ok {
			if serial != ownerSerial {
				errStr := fmt.Sprintf("module ID already in use by module: %v", ownerSerial)
				err = fmt.Errorf(moduleErrorFmt, serial, id, errStr)
				return false
			} else {
				// this serial is already assigned this id, nothing to do, no state change
				return false
			}
		}

		// id is not in use by any other serial, proceed to assign it

		// if the this is an assigned module
		//  - get the module's current id, we need to remove it from the inUseIDs
		// 	- update the id and and notify the control loops
		// if this is an unassigned module
		// 	- check if it is an active module, we can copy its ips
		// 	- add it to the assigned modules and notify the control loop

		s.inUseIDs[id] = serial
		assignedModule, assigned := s.AssignedModules[serial]
		if assigned {
			oldID := assignedModule.ID
			assignedModule.ID = id
			delete(s.inUseIDs, oldID)
		} else {
			// not already assigned, add it
			assignedModule = &Module{
				ID:     id,
				Serial: serial,
			}

			if m, ok := s.ActiveModules[serial]; ok {
				assignedModule.MCBIp = m.MCBIp
				assignedModule.PCIp = m.PCIp
				assignedModule.IPMIIp = m.IPMIIp

				// update the active module to have the new id
				m.ID = id
			}
			s.AssignedModules[serial] = assignedModule
		}
		toSet = *assignedModule
		changed = true
		return true
	})
	if err != nil {
		s.logger.WithError(err).Errorf(moduleErrorFmt, serial, id, "failed to assign module")
		return err
	}
	if !changed {
		// no change was made to the state module's assignment, return early
		return nil
	}
	s.logger.Infof("Module assigned: %v", toSet)
	go s.setModuleIdentity(toSet) // set the module id, failure is not critical since it will be retried next heartbeat
	s.triggerRedisSync()
	s.triggerModulesMatchRobotDef()
	s.connector.TriggerUpload()
	return nil
}

func (s *ModuleOrchestratorState) ClearModuleAssignment(serial string) (err error) {
	s.logger.Infof("Clearing module assignment for serial: %v", serial)
	// remove the module from the assigned modules
	// remove the id from the inUseIDs
	// update id in active modules if it possible
	var toClear Module
	s.WriteOnCurrent(func() {
		assignedModule, assigned := s.AssignedModules[serial]
		if !assigned {
			err = fmt.Errorf(moduleErrorFmt, serial, UnassignedModuleID, "module not assigned")
			return
		}

		delete(s.AssignedModules, serial)
		delete(s.inUseIDs, assignedModule.ID)

		if m, ok := s.ActiveModules[serial]; ok {
			m.ID = UnassignedModuleID
		}
		assignedModule.ID = UnassignedModuleID
		toClear = *assignedModule
	})
	s.logger.Infof("Clearing module assignment: %v", toClear)
	go s.setModuleIdentity(toClear) // set the module id, failure is not critical since it will be retried next heartbeat
	s.triggerRedisSync()
	s.triggerModulesMatchRobotDef()
	s.connector.TriggerUpload()
	return nil
}

func (s *ModuleOrchestratorState) SetModuleSerial(placeholderSerial, newSerial string) (err error) {
	s.logger.Infof("Setting module serial: placeholderSerial=%v, newSerial=%v", placeholderSerial, newSerial)
	// 1. find the module in the unset_serial modules
	// 2. set the new serial number
	// 3. remove the module from the unset_serial modules
	// 4. add the module to the active modules

	// do all of this while holding the lock, not the most efficient but this should be a very rare operation
	// We need to make sure that a heartbeat doesn't come in and add the module back to the unset_serial modules
	// while we are moving it to the active modules, that would cause the module to appear twice

	if !strings.HasPrefix(placeholderSerial, UnsetSerialPrefix) {
		err = fmt.Errorf(moduleErrorFmt, placeholderSerial, UnassignedModuleID, fmt.Sprintf("placeholder serial number must start with %v", UnsetSerialPrefix))
		return
	}

	if strings.HasPrefix(newSerial, UnsetSerialPrefix) {
		err = fmt.Errorf(moduleErrorFmt, newSerial, UnassignedModuleID, fmt.Sprintf("new serial number cannot start with %v", UnsetSerialPrefix))
		return
	}

	if newSerial == "" {
		err = fmt.Errorf(moduleErrorFmt, placeholderSerial, UnassignedModuleID, "new serial number cannot be empty")
		return
	}

	s.ConditionalWriteOnCurrent(func() bool {
		if m, ok := s.UnsetSerialModules[placeholderSerial]; ok {
			module := m.Module
			module.Serial = newSerial
			err = s.setModuleSerial(module)
			if err != nil {
				return false
			}
			// serial set correctly, delete from unset_serial modules and block any further heartbeats from re-adding it to unset_serial
			// it will appear in the active modules once it starts sending heartbeats with the new serial
			delete(s.UnsetSerialModules, placeholderSerial)
			s.blockedUnsets.Add(placeholderSerial) // mark this serial as blocked to avoid reprocessing heartbeats for it until timeout expires
			return true
		} else {
			err = fmt.Errorf("failed to find module with placeholder serial: %v", placeholderSerial)
			return false
		}
	})
	if err != nil {
		s.logger.WithError(err).Errorf(moduleErrorFmt, placeholderSerial, UnassignedModuleID, "failed to set new serial")
	}
	return err
}

/*
 * Private functions
 */

func (s *ModuleOrchestratorState) newTTLModule(module Module) *TTLModule {
	s.logger.Infof("New active module: %v", module)
	return &TTLModule{
		Module:      module,
		HeartbeatCh: make(chan struct{}, 1),
	}
}

func (s *ModuleOrchestratorState) monitorTTLModule(ttlMap map[string]*TTLModule, module *TTLModule) {
	defer s.wg.Done()
	for {
		select {
		case <-s.stopCtx.Done():
			return
		case <-module.HeartbeatCh:
		case <-time.After(heartbeatTimeout):
			s.WriteOnCurrent(func() {
				s.logger.Infof("Module heartbeat timeout, deleting module %v", module.Serial)
				delete(ttlMap, module.Serial)
			})
			return
		}
	}
}

func (s *ModuleOrchestratorState) createOrUpdateTTLModule(ttlMap map[string]*TTLModule, module *Module) bool {
	m, ok := ttlMap[module.Serial]
	if ok {
		m.HeartbeatCh <- struct{}{}
		if !m.modulesEquals(module) {
			m.Module = *module // update the ips
			return true
		}
		return false
	} else {
		ttlModule := s.newTTLModule(*module)
		ttlMap[module.Serial] = ttlModule
		s.wg.Add(1)
		go s.monitorTTLModule(ttlMap, ttlModule)
		return true
	}
}

/*
 * Trigger functions for single-threaded control loops
 */

// trigger the redis current assigned modules to be synced with the redis
func (s *ModuleOrchestratorState) triggerRedisSync() {
	select {
	case s.triggerRedisSyncCh <- struct{}{}:
	default:
	}
}

// trigger the comparison with the robot definition state for "missing module assignment" status
func (s *ModuleOrchestratorState) triggerModulesMatchRobotDef() {
	select {
	case s.triggerModulesMatchRobotDefCh <- struct{}{}:
	default:
	}
}

/*
 * Single-threaded control loops and helpers
 */

func (s *ModuleOrchestratorState) redisControlLoop() {
	defer sentry_reporter.HandlePanic()
	defer s.wg.Done()
	failedSync := false
	for {
		if failedSync {
			time.Sleep(5 * time.Second)
			s.triggerRedisSync()
		}
		failedSync = false
		select {
		case <-s.stopCtx.Done():
			return
		case <-s.triggerRedisSyncCh:
			modulesToWrite := make(map[string]*Module)
			s.ReadOnCurrent(func() {
				for serial, m := range s.AssignedModules {
					modulesToWrite[serial] = &Module{
						ID:     m.ID,
						Serial: m.Serial,
						MCBIp:  m.MCBIp,
						PCIp:   m.PCIp,
						IPMIIp: m.IPMIIp,
					}
				}
			})

			redisUpToDate := s.moduleMapsEqual(modulesToWrite, s.redisModules)

			if redisUpToDate {
				// modules match, no need to write
				continue
			}

			s.logger.Info("Syncing modules to redis")

			// 1. find the modules that need to be removed, delete them
			// 2. write the rest of the modules

			deletes := s.redisModulesForDelete(modulesToWrite)
			if len(deletes) > 0 {
				err := s.batchDeleteModulesFromRedis(deletes)
				if err != nil {
					s.logger.WithError(err).Error("failed to batch delete modules from redis")
					failedSync = true
					continue
				}
			}

			err := s.batchWriteModulesToRedis(modulesToWrite)
			if err != nil {
				s.logger.WithError(err).Error("failed to batch write modules to redis")
				failedSync = true
				continue
			}
			// we know the modules are up to date
			s.redisModules = modulesToWrite
		}
	}
}

func (s *ModuleOrchestratorState) moduleMapsEqual(map1 map[string]*Module, map2 map[string]*Module) bool {
	if len(map1) != len(map2) {
		return false
	}

	for k, v := range map1 {
		if v2, ok := map2[k]; !ok || !v.modulesEquals(v2) {
			return false
		}
	}
	return true
}

// assumes lock is held
func (s *ModuleOrchestratorState) redisModulesForDelete(currentModules map[string]*Module) map[string]struct{} {
	deletes := make(map[string]struct{})
	for serial, _ := range s.redisModules {
		if _, ok := currentModules[serial]; !ok {
			deletes[serial] = struct{}{}
		}
	}
	return deletes
}

func (s *ModuleOrchestratorState) modulesMatchRobotDefControlLoop() {
	defer sentry_reporter.HandlePanic()
	defer s.wg.Done()
	for {
		select {
		case <-s.stopCtx.Done():
			return
		case <-s.triggerModulesMatchRobotDefCh:
			s.compareAssignedModulesToRobotDef()
		}
	}
}

func (s *ModuleOrchestratorState) compareAssignedModulesToRobotDef() {
	assignedModulesMatch := true
	currentIds := make(map[uint32]struct{})
	s.ReadOnCurrent(func() {
		for _, m := range s.AssignedModules {
			currentIds[m.ID] = struct{}{}
		}
	})

	s.robotDefinitionState.ReadOnCurrent(func() {
		for _, row := range s.robotDefinitionState.CurrentDefinition.Rows {
			for _, module := range row.Modules {
				if module.Disabled {
					continue
				}
				if _, ok := currentIds[module.ModuleId]; !ok {
					assignedModulesMatch = false
					return
				}
			}
		}
	})
	s.assignedModulesMatchRobotDef.Store(assignedModulesMatch)
}

/*
 * Module Server rpc functions
 */

// setModuleIdentity creates client for module server using the dhcp ip of the module and sets the module identity.
func (s *ModuleOrchestratorState) setModuleIdentity(module Module) error {
	if module.PCIp == "" {
		return errors.New("module PC IP is missing")
	}
	client := module_server_client.NewModuleServerClient(fmt.Sprintf("%s:%d", module.PCIp, moduleServerPort))
	err := client.SetModuleIdentity(module.ID, module.Serial)
	return err
}

// setModuleSerial creates client for module server using the dhcp ip of the module and sets the module serial number.
func (s *ModuleOrchestratorState) setModuleSerial(module Module) error {
	client := module_server_client.NewModuleServerClient(fmt.Sprintf("%s:%d", module.PCIp, moduleServerPort))
	err := client.SetModuleSerialNumber(module.Serial, false)
	return err
}

/*
 * Redis accessors
 */

func (s *ModuleOrchestratorState) writeModuleToRedis(module Module) error {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	data, err := json.Marshal(module)
	if err != nil {
		s.logger.WithError(err).Error("failed to marshal module")
		return err
	}
	err = s.redisClient.HSetWithContext(ctx, modulesKey, module.Serial, data)
	if err != nil {
		s.logger.WithError(err).Error("failed to write module to redis")
		return err
	}
	return nil
}

func (s *ModuleOrchestratorState) batchWriteModulesToRedis(modules map[string]*Module) error {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	if len(modules) == 0 {
		return nil
	}

	data := make(map[string]string, len(modules))
	for serial, module := range modules {
		d, err := json.Marshal(module)
		if err != nil {
			return err
		}
		data[serial] = string(d)
	}

	err := s.redisClient.HSetWithContext(ctx, modulesKey, data)
	return err
}

func (s *ModuleOrchestratorState) readModulesHashFromRedis() (map[string]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	hash, err := s.redisClient.HGetAllWithContext(ctx, modulesKey)
	if err != nil {
		s.logger.WithError(err).Error("failed to read modules from redis")
		return nil, err
	}
	return hash, nil
}

func (s *ModuleOrchestratorState) fillAssignedModulesFromRedis() error {
	s.logger.Info("Filling assigned modules from redis")

	hash, err := s.readModulesHashFromRedis()
	if err != nil {
		s.logger.WithError(err).Error("failed to fill assigned modules from redis")
		return err
	}

	for serial, data := range hash {
		var module Module
		err = json.Unmarshal([]byte(data), &module)
		if err != nil {
			s.logger.WithError(err).Error("failed to unmarshal module")
			return err
		}

		// temp solution to avoid duplicate module ids
		if _, ok := s.inUseIDs[module.ID]; ok {
			s.logger.Errorf(moduleErrorFmt, serial, module.ID, "module ID already in use, skipping")
			continue
		}

		s.redisModules[serial] = &Module{
			ID:     module.ID,
			Serial: module.Serial,
			MCBIp:  module.MCBIp,
			PCIp:   module.PCIp,
			IPMIIp: module.IPMIIp,
		}
		s.AssignedModules[serial] = &Module{
			ID:     module.ID,
			Serial: module.Serial,
			MCBIp:  module.MCBIp,
			PCIp:   module.PCIp,
			IPMIIp: module.IPMIIp,
		}
		s.inUseIDs[module.ID] = serial
	}
	s.logger.Info("Assigned modules filled from redis")
	return nil
}

func (s *ModuleOrchestratorState) deleteModuleFromRedis(module Module) error {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()
	err := s.redisClient.HDelWithContext(ctx, modulesKey, module.Serial)
	return err
}

func (s *ModuleOrchestratorState) batchDeleteModulesFromRedis(serials map[string]struct{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	if len(serials) == 0 {
		return nil
	}

	keys := make([]string, 0, len(serials))
	for serial := range serials {
		keys = append(keys, serial)
	}

	err := s.redisClient.HDelWithContext(ctx, modulesKey, keys...)
	return err
}

package state

import (
	"context"

	"github.com/carbonrobotics/robot/golang/lib/config"
)

type FeatureState struct {
	ManagedStateImpl
	featureFlagConfigNode *config.ConfigTree
	flags                 map[string]bool
}

func NewFeatureState(commonNode *config.ConfigTree) *FeatureState {
	fstate := &FeatureState{
		featureFlagConfigNode: commonNode.GetNode("feature_flags"),
	}
	fstate.initialize()
	fstate.reload()
	fstate.featureFlagConfigNode.RegisterCallback(fstate.reload)
	return fstate
}

func (fst *FeatureState) reload() {
	flags := fst.featureFlags()
	fst.WriteOnCurrent(func() {
		fst.flags = flags
	})
}

func (fst *FeatureState) featureFlags() map[string]bool {
	flags := make(map[string]bool)
	for _, feature := range fst.featureFlagConfigNode.GetChildrenNodes() {
		defer config.PreDeleteConfigTree(feature)
		flags[feature.GetName()] = feature.GetBoolValue()
	}
	flags["hide_laser_arm_in_ui_feature"] = true // This is way to show that this version of robot code supports hidden laser arming.
	flags["use_targeting_mode_feature"] = true   // This is way to show that this version of robot code supports targeting mode to set shooting state.
	flags["diagnostic_find_trajectory_feature"] = true
	flags["support_xhair_cal_state"] = true      // Supports crosshair calibration state
	flags["banding_uuid_feature"] = true         // Banding definitions identified by unique ID
	flags["thinning_conf_use_id"] = true         // Thinning configs are now unique by ID only names ar just for human readability
	flags["snap_as_data_capture_feature"] = true // Use startDataCapture instead of snapImages to capture snaps
	flags["has_row_spacing"] = true              // robot has Get/SetRowSpacing feature
	flags["score_state_feature"] = true          // has score state in weed tracking
	flags["tve_profiles_feature"] = true         // display the TVE profile editor, and read the updated velocity data from GetNextWeedingVelocity grpc call
	flags["thinning_conf_use_n_rows"] = true     // Thinning configs now support n rows using a map of row id to bounds
	flags["dashboard_use_n_rows_map"] = true     // Look at the maps for row data rather than the arrays
	flags["threshold_filter_support"] = true     // Support for threshold based filtering in vis
	flags["new_dashboard_feature"] = true        // uses the new dashboard design instead, determined by whether or not robot is on v2.0 and above
	return flags
}

func (fst *FeatureState) GetFeatureFlags() map[string]bool {
	flags := make(map[string]bool)
	fst.ReadOnCurrent(func() {
		flags = fst.flags
	})
	return flags
}

func (fst *FeatureState) GetNextFeatureFlags(ctx context.Context, ts int64) (bool, map[string]bool, int64) {
	flags := make(map[string]bool)
	retTs := int64(0)
	result := fst.ReadOnNext(ctx, ts, func() {
		flags = fst.flags
		retTs = fst.GetTimestampMs()
	})
	return result, flags, retTs
}

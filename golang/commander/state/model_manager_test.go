package state

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"sort"
	"sync/atomic"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/state/mocks"
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/cv"

	hw_proto "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	crredis "github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestRowClientList_ConnectionState(t *testing.T) {
	testDisconnected := &RowClient{}

	testConnected := &RowClient{}
	testConnected.connected.Store(true)

	tests := []struct {
		name                    string
		rowClientList           RowClientList
		expectedConnectionCount int
		expectedConnectionState []bool
	}{
		{"single happy client", RowClientList{
			testConnected},
			1, []bool{true}},
		{"all states", RowClientList{
			testConnected, testConnected, testDisconnected, testDisconnected},
			2, []bool{true, true, false, false}},
		{"all disconnected", RowClientList{
			testDisconnected, testDisconnected},
			0, []bool{false, false},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			connectedCount, connectionState := test.rowClientList.ConnectionState()
			assert.Equal(t, test.expectedConnectionCount, connectedCount)
			assert.Equal(t, test.expectedConnectionState, connectionState)
		})
	}
}

func TestRowClientList_Sync(t *testing.T) {
	testModels := []*model_receiver.Model{
		{Id: "test-123"}, {Id: "test-5123"}, {Id: "test-222"},
	}

	tests := []struct {
		name                   string
		getDownloadedModelsErr error
	}{
		{"happy path", nil},
		{"get downloaded error", assert.AnError},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {

			mockRowClient := new(mocks.ModelReceiverClient)
			mockRowClient.On("GetDownloadedModels").
				Return(&model_receiver.DownloadedModelResponse{Models: testModels}, test.getDownloadedModelsErr)

			testRowClient := &RowClient{client: mockRowClient}
			rcl := RowClientList{testRowClient}
			rcl.Sync()

			if test.getDownloadedModelsErr != nil {
				assert.False(t, testRowClient.connected.Load())
			} else {
				assert.True(t, testRowClient.connected.Load())
				expectedModels := make([]string, 0)
				for _, m := range testModels {
					expectedModels = append(expectedModels, m.GetId())
				}
				assert.Equal(t, expectedModels, testRowClient.syncedModels)
			}
		})
	}
}

func TestRowClientList_GetSyncedModels(t *testing.T) {
	tests := []struct {
		name           string
		rowModels      [][]string
		expectedSynced []string
	}{
		{"happy path", [][]string{
			{"model1", "model2"},
			{"model1", "model2"},
			{"model1", "model2"}},
			[]string{"model1", "model2"},
		},
		{"nothing synced across all", [][]string{
			{"model1", "model2"},
			{"model1"},
			{"model2"}},
			[]string{},
		},
		{"one synced", [][]string{
			{"model1", "model2"},
			{"model1"}},
			[]string{"model1"},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {

			rcl := make(RowClientList, 0)
			for _, rowModels := range test.rowModels {
				rcl = append(rcl, &RowClient{syncedModels: rowModels})
			}
			gotSynced := rcl.GetSyncedModels()
			assert.Equal(t, test.expectedSynced, gotSynced)
		})
	}
}

func TestRowClientList_IsSynced(t *testing.T) {
	tests := []struct {
		name               string
		rowModels          [][]string
		modelID            string
		expectedSynced     bool
		expectedRowsSynced []bool
	}{
		{
			"synced",
			[][]string{
				{"model1"},
				{"model1"},
			},
			"model1",
			true,
			[]bool{true, true},
		},
		{
			"not synced",
			[][]string{
				{},
				{},
			},
			"model1",
			false,
			[]bool{false, false},
		},
		{
			"half synced",
			[][]string{
				{"model1"},
				{"foobar"},
			},
			"model1",
			false,
			[]bool{true, false},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			rcl := make(RowClientList, 0)
			for _, rowModels := range test.rowModels {
				rcl = append(rcl, &RowClient{syncedModels: rowModels})
			}
			gotSynced, gotRowsSynced := rcl.IsSynced(test.modelID)
			assert.Equal(t, test.expectedSynced, gotSynced)
			assert.Equal(t, test.expectedRowsSynced, gotRowsSynced)
		})
	}
}

func TestRowClientList_GetAllModels(t *testing.T) {
	tests := []struct {
		name           string
		rowModels      [][]string
		expectedModels []string
	}{
		{
			"full set",
			[][]string{
				{"model1"},
				{"model2"},
				{"model3"},
			},
			[]string{"model1", "model2", "model3"},
		},
		{
			"unique set",
			[][]string{
				{"model1"},
				{"model1"},
				{"model2"},
			},
			[]string{"model1", "model2"},
		},
		{
			"no models",
			[][]string{},
			[]string{},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			rcl := make(RowClientList, 0)
			for _, rowModels := range test.rowModels {
				rcl = append(rcl, &RowClient{syncedModels: rowModels})
			}
			gotModels := rcl.GetAllModels()
			assert.Equal(t, test.expectedModels, gotModels)
		})
	}
}

func TestRowClient_UpdateSyncedModels(t *testing.T) {
	testSyncedModels := []string{"test1", "test2"}
	testSyncedArtifacts := []string{"test3", "test4"}
	testRowClient := &RowClient{}

	testRowClient.UpdateSyncedModels(testSyncedModels, testSyncedArtifacts)
	assert.Equal(t, testSyncedModels, testRowClient.syncedModels)
	assert.Equal(t, testSyncedArtifacts, testRowClient.syncedArtifacts)
}

func TestRowClient_GetSyncedModels(t *testing.T) {
	testSyncedModels := []string{"test1", "test2"}
	testRowClient := &RowClient{syncedModels: testSyncedModels}

	got := testRowClient.GetSyncedModels()
	assert.Equal(t, testSyncedModels, got)
}

func TestRowClient_GetCVComputeCapabilities(t *testing.T) {
	tests := []struct {
		name                  string
		cvComputeCapabilities []string
	}{
		{"happy path", []string{"v1", "v2"}},
		{"empty list", []string{}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockRowClient := new(mocks.CVRuntimeClient)
			mockRowClient.On("GetCVComputeCapabilities").
				Return(&cv.ComputeCapabilitiesResponse{Capabilities: test.cvComputeCapabilities}, nil)

			testRowClient := &RowClient{cvRuntimeClient: mockRowClient}
			rcl := RowClientList{testRowClient}
			computeCapabilities, _ := rcl.GetCVComputeCapabilities()

			assert.Equal(t, computeCapabilities, test.cvComputeCapabilities)
		})
	}
}

func TestRowClient_GetCVSupportedTensorRTVersions(t *testing.T) {
	tests := []struct {
		name             string
		tensorRTVersions []string
	}{
		{"happy path", []string{"v1", "v2"}},
		{"empty list", []string{}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockRowClient := new(mocks.CVRuntimeClient)
			mockRowClient.On("GetCVSupportedTensorRTVersions").
				Return(&cv.SupportedTensorRTVersionsResponse{Versions: test.tensorRTVersions}, nil)

			testRowClient := &RowClient{cvRuntimeClient: mockRowClient}
			rcl := RowClientList{testRowClient}
			tensorRTVersions, _ := rcl.GetCVSupportedTensorRTVersions()

			assert.Equal(t, tensorRTVersions, test.tensorRTVersions)
		})
	}
}

func TestRowClient_GetCVAspectsDifferentVersions(t *testing.T) {
	trtVersion8 := []string{"8.0.1.6"}
	trtVersion10 := []string{"10.0.1"}
	compCap89 := []string{"8.9"}
	compCap86 := []string{"8.6"}

	tests := []struct {
		name                        string
		tensorRTVersions1           []string
		tensorRTVersions2           []string
		tensorRTVersions3           []string
		cvComputeCapabilities1      []string
		cvComputeCapabilities2      []string
		cvComputeCapabilities3      []string
		expectedTensorRTVersions    []string
		expectedComputeCapabilities []string
	}{
		{"all rows same versions", trtVersion8, trtVersion8, trtVersion8, compCap86, compCap86, compCap86, trtVersion8, compCap86},
		{"one row different versions", trtVersion10, trtVersion8, trtVersion8, compCap89, compCap86, compCap86, append(trtVersion10, trtVersion8...), append(compCap89, compCap86...)},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockRowClient1 := new(mocks.CVRuntimeClient)
			mockRowClient2 := new(mocks.CVRuntimeClient)
			mockRowClient3 := new(mocks.CVRuntimeClient)

			mockRowClient1.On("GetCVSupportedTensorRTVersions").
				Return(&cv.SupportedTensorRTVersionsResponse{Versions: test.tensorRTVersions1}, nil)
			mockRowClient2.On("GetCVSupportedTensorRTVersions").
				Return(&cv.SupportedTensorRTVersionsResponse{Versions: test.tensorRTVersions2}, nil)
			mockRowClient3.On("GetCVSupportedTensorRTVersions").
				Return(&cv.SupportedTensorRTVersionsResponse{Versions: test.tensorRTVersions3}, nil)

			mockRowClient1.On("GetCVComputeCapabilities").
				Return(&cv.ComputeCapabilitiesResponse{Capabilities: test.cvComputeCapabilities1}, nil)
			mockRowClient2.On("GetCVComputeCapabilities").
				Return(&cv.ComputeCapabilitiesResponse{Capabilities: test.cvComputeCapabilities2}, nil)
			mockRowClient3.On("GetCVComputeCapabilities").
				Return(&cv.ComputeCapabilitiesResponse{Capabilities: test.cvComputeCapabilities3}, nil)

			testRowClient1 := &RowClient{cvRuntimeClient: mockRowClient1}
			testRowClient2 := &RowClient{cvRuntimeClient: mockRowClient2}
			testRowClient3 := &RowClient{cvRuntimeClient: mockRowClient3}
			rcl := RowClientList{testRowClient1, testRowClient2, testRowClient3}
			tensorRTVersions, _ := rcl.GetCVSupportedTensorRTVersions()
			computeCapabilities, _ := rcl.GetCVComputeCapabilities()

			assert.Equal(t, tensorRTVersions, test.expectedTensorRTVersions)
			assert.Equal(t, computeCapabilities, test.expectedComputeCapabilities)
		})
	}
}

func TestGetCropConfigByID(t *testing.T) {
	saveConfigPreDeleteConfigTree := config.PreDeleteConfigTree
	defer func() { config.PreDeleteConfigTree = saveConfigPreDeleteConfigTree }()

	config.PreDeleteConfigTree = func(node any) { /* NO OP */ }

	tests := []struct {
		name               string
		cropID             string
		cropNameOverride   string
		enabled            bool
		pinnedModel        string
		recommendedModel   string
		lastActiveModel    string
		maintainedModels   []string
		hasCropTree        bool
		hasNilTree         bool
		expectedCropConfig CropConfig
	}{
		{
			"retrieve crop config",
			"123-123",
			"crop crop",
			true,
			"fut-999",
			"fit-999",
			"fut-fooo",
			[]string{"fut-123", "fat-234"},
			true,
			false,
			CropConfig{
				ID:               "123-123",
				CropNameOverride: "crop crop",
				Enabled:          true,
				PinnedModel:      "fut-999",
				RecommendedModel: "fit-999",
				LastActiveModel:  "fut-fooo",
				MaintainedModels: []string{"fut-123", "fat-234"},
			},
		},
		{
			"no crop tree",
			"123-123",
			"",
			false,
			"",
			"",
			"",
			[]string{},
			false,
			false,
			CropConfig{},
		},
		{
			"nil tree",
			"123-123",
			"",
			false,
			"",
			"",
			"",
			[]string{},
			false,
			true,
			CropConfig{},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			maintainedSubTree := make(map[string]config.MockConfigTree)
			for _, modelID := range test.maintainedModels {
				maintainedSubTree[modelID] = config.MockConfigTree{Name: modelID}
			}

			cropTree := config.MockConfigTree{
				SubTrees: map[string]config.MockConfigTree{
					cropNameOverrideNodeName: {Value: test.cropNameOverride},
					enabledNodeName:          {Value: test.enabled},
					pinnedModelNodeName:      {Value: test.pinnedModel},
					recommendedModelNodeName: {Value: test.recommendedModel},
					lastActiveModelNodeName:  {Value: test.lastActiveModel},
					maintainedModelsNodeName: {
						SubTrees: maintainedSubTree},
				},
			}
			nilTree := config.MockConfigTree{
				SubTrees: map[string]config.MockConfigTree{},
			}

			cropsNode := config.MockConfigTree{
				SubTrees: make(map[string]config.MockConfigTree),
			}
			if test.hasCropTree {
				cropsNode.SubTrees[test.cropID] = cropTree
			}
			if test.hasNilTree {
				cropsNode.SubTrees[test.cropID] = nilTree
			}

			got := GetCropConfigByID(cropsNode, test.cropID)

			assert.Equal(t, test.expectedCropConfig.ID, got.ID)
			assert.Equal(t, test.expectedCropConfig.CropNameOverride, got.CropNameOverride)
			assert.Equal(t, test.expectedCropConfig.Enabled, got.Enabled)
			assert.Equal(t, test.expectedCropConfig.PinnedModel, got.PinnedModel)
			assert.Equal(t, test.expectedCropConfig.RecommendedModel, got.RecommendedModel)
			assert.Equal(t, test.expectedCropConfig.LastActiveModel, got.LastActiveModel)
			assert.ElementsMatch(t, test.expectedCropConfig.MaintainedModels, got.MaintainedModels)
		})
	}
}

func TestUpdateCropConfigLastActiveModel(t *testing.T) {
	saveConfigGetComputerConfigPrefix := configGetComputerConfigPrefix
	defer func() {
		configGetComputerConfigPrefix = saveConfigGetComputerConfigPrefix
	}()

	testCropID := "crop-999-000"
	testModelID := "model-999-234"
	testPrefix := "test-prefix"

	configGetComputerConfigPrefix = func() string {
		return testPrefix
	}

	tests := []struct {
		name              string
		setStringValueErr error
		expectError       bool
	}{
		{"no error", nil, false},
		{"error", assert.AnError, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName, testCropID, lastActiveModelNodeName)
			mockConfigClient := new(mocks.ConfigClient)
			mockConfigClient.On("SetStringValue", expectedKey, testModelID).Return(test.setStringValueErr)

			err := UpdateCropConfigLastActiveModel(mockConfigClient, testCropID, testModelID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockConfigClient.AssertExpectations(t)
			}
		})
	}
}

func TestUpdateCropConfigRecommendedModel(t *testing.T) {
	saveConfigGetComputerConfigPrefix := configGetComputerConfigPrefix
	defer func() {
		configGetComputerConfigPrefix = saveConfigGetComputerConfigPrefix
	}()

	testCropID := "crop-999-000"
	testModelID := "model-999-234"
	testPrefix := "test-prefix"

	configGetComputerConfigPrefix = func() string {
		return testPrefix
	}

	tests := []struct {
		name              string
		setStringValueErr error
		expectError       bool
	}{
		{"no error", nil, false},
		{"error", assert.AnError, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName, testCropID, recommendedModelNodeName)
			mockConfigClient := new(mocks.ConfigClient)
			mockConfigClient.On("SetStringValue", expectedKey, testModelID).Return(test.setStringValueErr)

			err := UpdateCropConfigRecommendedModel(mockConfigClient, testCropID, testModelID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockConfigClient.AssertExpectations(t)
			}
		})
	}
}

func TestUpdateCropConfigPinnedModel(t *testing.T) {
	saveConfigGetComputerConfigPrefix := configGetComputerConfigPrefix
	defer func() {
		configGetComputerConfigPrefix = saveConfigGetComputerConfigPrefix
	}()

	testCropID := "crop-999-000"
	testModelID := "model-999-234"
	testPrefix := "test-prefix"

	configGetComputerConfigPrefix = func() string {
		return testPrefix
	}

	tests := []struct {
		name              string
		setStringValueErr error
		expectError       bool
	}{
		{"no error", nil, false},
		{"error", assert.AnError, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName, testCropID, pinnedModelNodeName)
			mockConfigClient := new(mocks.ConfigClient)
			mockConfigClient.On("SetStringValue", expectedKey, testModelID).Return(test.setStringValueErr)

			err := UpdateCropConfigPinnedModel(mockConfigClient, testCropID, testModelID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockConfigClient.AssertExpectations(t)
			}
		})
	}
}

func TestUpdateP2PPinnedModel(t *testing.T) {
	saveConfigGetComputerConfigPrefix := configGetComputerConfigPrefix
	defer func() {
		configGetComputerConfigPrefix = saveConfigGetComputerConfigPrefix
	}()

	testModelID := "model-999-234"
	testPrefix := "test-prefix"

	configGetComputerConfigPrefix = func() string {
		return testPrefix
	}

	tests := []struct {
		name              string
		setStringValueErr error
		expectError       bool
	}{
		{"no error", nil, false},
		{"error", assert.AnError, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedKey := path.Join(testPrefix, commanderNodeName, pinnedNonCropModelsNodeName, p2pNodeName)
			mockConfigClient := new(mocks.ConfigClient)
			mockConfigClient.On("SetStringValue", expectedKey, testModelID).Return(test.setStringValueErr)

			err := UpdateP2PPinnedModel(mockConfigClient, testModelID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockConfigClient.AssertExpectations(t)
			}
		})
	}
}

func TestAddMissingCropConfig(t *testing.T) {
	saveConfigGetComputerConfigPrefix := configGetComputerConfigPrefix
	defer func() {
		configGetComputerConfigPrefix = saveConfigGetComputerConfigPrefix
	}()

	testCropID := "crop-999-000"
	testPrefix := "test-prefix"

	configGetComputerConfigPrefix = func() string {
		return testPrefix
	}

	tests := []struct {
		name              string
		cropIDNodeExists  bool
		addToListErr      error
		setStringValueErr error
		setBoolValueErr   error
		expectError       bool
	}{
		{"happy path", false, nil, nil, nil, false},
		{"crop id exists", true, nil, nil, nil, false},
		{"add to list err", false, assert.AnError, nil, nil, true},
		{"set string err", false, nil, assert.AnError, nil, true},
		{"set bool err", false, nil, nil, assert.AnError, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testCropConfig := CropConfig{
				ID:               testCropID,
				Enabled:          true,
				PinnedModel:      "foo",
				RecommendedModel: "bar",
				LastActiveModel:  "xyz",
			}

			mockCropsNode := config.MockConfigTree{}
			if test.cropIDNodeExists {
				mockCropsNode.SubTrees = map[string]config.MockConfigTree{
					testCropID: {},
				}
			}

			mockConfigClient := new(mocks.ConfigClient)
			if !test.cropIDNodeExists {
				expectedListKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName)
				mockConfigClient.On("AddToList", expectedListKey, testCropID).Return(test.addToListErr)

				expectedEnabledKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName, testCropID, enabledNodeName)
				mockConfigClient.On("SetBoolValue", expectedEnabledKey, false).Return(test.setBoolValueErr)

				expectedPinnedModelKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName, testCropID, pinnedModelNodeName)
				mockConfigClient.On("SetStringValue", expectedPinnedModelKey, "").Return(test.setStringValueErr)

				expectedRecModelKey := path.Join(testPrefix, commanderNodeName, cropIDsNodeName, testCropID, recommendedModelNodeName)
				mockConfigClient.On("SetStringValue", expectedRecModelKey, "").Return(test.setStringValueErr)
			}

			err := AddMissingCropConfig(mockConfigClient, mockCropsNode, testCropConfig)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockConfigClient.AssertExpectations(t)
			}
		})
	}
}

func TestModelManagerWatcher_EnabledCropList(t *testing.T) {
	crop1 := EnabledCrop{
		Crop: veselka.Crop{
			ID:          "1",
			CommonName:  "Crop1",
			CaptureOnly: false,
		},
	}
	captureOnlyCrop := EnabledCrop{
		Crop: veselka.Crop{
			ID:          "3",
			CommonName:  "CaptureCrop",
			CaptureOnly: true,
		},
	}
	testCropList := EnabledCropList{crop1, captureOnlyCrop}

	tests := []struct {
		name               string
		includeCaptureOnly bool
		expectedCropList   EnabledCropList
	}{
		{
			"crop list without capture",
			false,
			EnabledCropList{crop1},
		},
		{
			"crop list with capture",
			true,
			EnabledCropList{crop1, captureOnlyCrop},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mms := &ModelManagerState{EnabledCropsList: testCropList}
			mmw := &ModelManagerWatcher{
				modelState: mms,
			}
			got := mmw.EnabledCropList(test.includeCaptureOnly)
			assert.Equal(t, test.expectedCropList, got)
		})
	}
}

func TestModelManagerWatcher_UpdateEnabledCropList(t *testing.T) {
	testCrops := []*veselka.Crop{
		{
			ID:          "enabled-crop",
			CommonName:  "enabled common",
			Description: "enabled foo bar",
		},
		{
			ID:          "enabled-overridden-crop",
			CommonName:  "enabled common blah",
			Description: "enabled foo bar baz",
		},
		{
			ID:          "disabled-crop",
			CommonName:  "disabled common",
			Description: "disabled foo bar",
		},
	}

	mockCropIDsNode := config.MockConfigTree{
		SubTrees: map[string]config.MockConfigTree{
			"enabled-crop": {
				SubTrees: map[string]config.MockConfigTree{
					cropNameOverrideNodeName: {Value: ""},
					enabledNodeName:          {Value: true},
					pinnedModelNodeName:      {Value: "pinned-model"},
					recommendedModelNodeName: {Value: "recommended-model"},
					lastActiveModelNodeName:  {Value: "last-active-model"},
					maintainedModelsNodeName: {SubTrees: map[string]config.MockConfigTree{
						"model-123": {Name: "model-123"},
						"model-432": {Name: "model-432"},
					}},
				},
			},
			"enabled-overridden-crop": {
				SubTrees: map[string]config.MockConfigTree{
					cropNameOverrideNodeName: {Value: "override crop"},
					enabledNodeName:          {Value: true},
					pinnedModelNodeName:      {Value: "pinned-model"},
					recommendedModelNodeName: {Value: "recommended-model"},
					lastActiveModelNodeName:  {Value: "last-active-model"},
					maintainedModelsNodeName: {SubTrees: map[string]config.MockConfigTree{
						"model-123": {Name: "model-123"},
						"model-432": {Name: "model-432"},
					}},
				},
			},
			"disabled-crop": {
				SubTrees: map[string]config.MockConfigTree{
					cropNameOverrideNodeName: {Value: ""},
					enabledNodeName:          {Value: false},
					pinnedModelNodeName:      {Value: "pinned-model"},
					recommendedModelNodeName: {Value: "recommended-model"},
					lastActiveModelNodeName:  {Value: "last-active-model"},
					maintainedModelsNodeName: {},
				},
			},
		},
	}

	mmState := NewModelManagerState()
	mmw := &ModelManagerWatcher{
		cropIDsNode: mockCropIDsNode,
		modelState:  mmState,
		logger:      appModelLogger.WithFields(logrus.Fields{"module": "TESTModelManagerWatcher"})}
	mmw.UpdateEnabledCropList(testCrops)

	enabledCropsList := mmw.modelState.EnabledCropsList

	assert.Len(t, enabledCropsList, 2)
	assert.Equal(t, enabledCropsList[0].ID, "enabled-crop")
	assert.Equal(t, enabledCropsList[0].CommonName, "enabled common")
	assert.Equal(t, enabledCropsList[0].PinnedModel, "pinned-model")
	assert.Equal(t, enabledCropsList[0].RecommendedModel, "recommended-model")
	assert.Equal(t, enabledCropsList[0].LastActiveModel, "last-active-model")
	assert.ElementsMatch(t, enabledCropsList[0].MaintainedModels, []string{"model-123", "model-432"})

	assert.Equal(t, enabledCropsList[1].ID, "enabled-overridden-crop")
	assert.Equal(t, enabledCropsList[1].CommonName, "override crop")
	assert.Equal(t, enabledCropsList[1].PinnedModel, "pinned-model")
	assert.Equal(t, enabledCropsList[1].RecommendedModel, "recommended-model")
	assert.Equal(t, enabledCropsList[1].LastActiveModel, "last-active-model")
	assert.ElementsMatch(t, enabledCropsList[1].MaintainedModels, []string{"model-123", "model-432"})
}

func TestModelManagerWatcher_GetCacheAllCrops(t *testing.T) {
	saveModelManagerFs := ModelManagerFs
	defer func() { ModelManagerFs = saveModelManagerFs }()

	testDataDir := "/data"

	localCacheData := []*veselka.Crop{
		{ID: "testCropIDOne"},
		{ID: "testCropIDTwo"},
	}
	localCacheDataBytes, err := json.Marshal(localCacheData)
	if err != nil {
		t.Fatal(err)
	}
	remoteCacheData := []*veselka.Crop{
		{ID: "testCropIDUno"},
		{ID: "testCropIDDos"},
	}
	remoteCacheDataBytes, err := json.Marshal(remoteCacheData)
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name           string
		cachedExists   bool
		updateCache    bool
		getAllCropsErr error
		expectUpdate   bool
		expectError    bool
	}{
		{
			"update cache",
			false, true, nil, true, false,
		},
		{
			"cache exists, with update",
			true, true, nil, true, false,
		},
		{
			"cache exists, no update",
			true, false, nil, false, false,
		},
		{
			"cache exists, update fails",
			true, true, assert.AnError, false, false,
		},
		{
			"cache doesn't exists, update fails",
			false, true, assert.AnError, false, true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ModelManagerFs = afero.NewMemMapFs()

			if test.cachedExists {
				if err := afero.WriteFile(ModelManagerFs, filepath.Join(testDataDir, allCropsCacheFilename), localCacheDataBytes, os.ModePerm); err != nil {
					t.Fatal(err)
				}
			}

			mockVeselkaClient := new(mocks.VeselkaClient)
			mockVeselkaClient.On("GetAllCrops", mock.Anything).Return(remoteCacheData, test.getAllCropsErr)

			mmw := &ModelManagerWatcher{
				env:           environment.Robot{MakaDataDir: testDataDir},
				veselkaClient: mockVeselkaClient,
				logger:        appModelLogger.WithFields(logrus.Fields{"module": "TESTModelManagerWatcher"}),
			}

			gotCrops, err := mmw.GetCacheAllCrops(test.updateCache)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				cache, err := afero.ReadFile(ModelManagerFs, filepath.Join(testDataDir, allCropsCacheFilename))
				if err != nil {
					t.Fatal(err)
				}
				assert.NotEmpty(t, cache)
				if test.expectUpdate {
					assert.Equal(t, remoteCacheData, gotCrops)
					assert.Equal(t, remoteCacheDataBytes, cache)
				} else {
					assert.Equal(t, localCacheData, gotCrops)
					assert.Equal(t, localCacheDataBytes, cache)
				}
			}
		})
	}
}

func TestModelManagerWatcher_UpdatePointCategories(t *testing.T) {
	tests := []struct {
		name                      string
		pointCategories           []veselka.PointCategory
		expectedPointCategoryIds  []string // This is a set of key1, val1, key2, val2... for bulk inserting into redis
		expectedPointCategoryDefs []string
		getAllPointCategoriesErr  error
		hSetWithContextErr        error
		expectVeselkaError        bool
		expectRedisError          bool
	}{
		{
			"happy path",
			[]veselka.PointCategory{
				{
					Archived: false,
					ID:       "id-123",
					Name:     "foo-bar",
				},
			},
			[]string{"id-123", "foo-bar"},
			[]string{"id-123", "{\"id\":\"id-123\",\"name\":\"foo-bar\"}"},
			nil,
			nil,
			false,
			false,
		},
		{
			"multi & skip archived",
			[]veselka.PointCategory{
				{
					Archived: false,
					ID:       "id-123",
					Name:     "foo-bar",
				},
				{
					Archived: true,
					ID:       "xxx",
					Name:     "zzz",
				},
				{
					Archived: false,
					ID:       "abcde",
					Name:     "abcdeX",
				},
				{
					Archived: false,
					ID:       "another-id",
					Name:     "Another Name",
				},
			},
			[]string{"id-123", "foo-bar", "abcde", "abcdeX", "another-id", "Another Name"},
			[]string{"id-123", "{\"id\":\"id-123\",\"name\":\"foo-bar\"}", "abcde", "{\"id\":\"abcde\",\"name\":\"abcdeX\"}", "another-id", "{\"id\":\"another-id\",\"name\":\"Another Name\"}"},
			nil,
			nil,
			false,
			false,
		},
		{
			"complete definition",
			[]veselka.PointCategory{
				{
					Archived:    false,
					Created:     12345,
					Description: "test description",
					DisplayName: "test display name",
					ID:          "id-123",
					Name:        "foo-bar",
					Translations: []veselka.PointCategoryTranslation{
						{
							Name:     "bar-foo",
							Language: "backwards",
							Version:  1,
						},
					},
					Updated: 67890,
				},
			},
			[]string{"id-123", "foo-bar"},
			[]string{"id-123", "{\"created\":12345,\"description\":\"test description\",\"display_name\":\"test display name\",\"id\":\"id-123\",\"name\":\"foo-bar\",\"translations\":[{\"name\":\"bar-foo\",\"language\":\"backwards\",\"version\":1}],\"updated\":67890}"},
			nil,
			nil,
			false,
			false,
		},
		{
			"retrieval failure",
			[]veselka.PointCategory{},
			[]string{},
			[]string{},
			assert.AnError,
			nil,
			true,
			false,
		},
		{
			"redis set failure",
			[]veselka.PointCategory{
				{
					Archived: false,
					ID:       "id-123",
					Name:     "foo-bar",
				},
			},
			[]string{"id-123", "foo-bar"},
			[]string{"id-123", "{\"id\":\"id-123\",\"name\":\"foo-bar\"}"},
			nil,
			assert.AnError,
			false,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockVeselkaClient := new(mocks.VeselkaClient)
			mockVeselkaClient.On("GetAllPointCategories", mock.Anything).Return(test.pointCategories, test.getAllPointCategoriesErr)

			mockRedis := new(mocks.RedisClient)
			mockRedis.On("HSetWithContext", mock.Anything, crredis.PointCategoryIds, test.expectedPointCategoryIds).Return(test.hSetWithContextErr)
			mockRedis.On("HSetWithContext", mock.Anything, crredis.PointCategoryDefs, test.expectedPointCategoryDefs).Return(test.hSetWithContextErr)

			mmw := &ModelManagerWatcher{
				veselkaClient: mockVeselkaClient,
				redis:         mockRedis,
				logger:        appModelLogger.WithFields(logrus.Fields{"module": "TESTModelManagerWatcher"}),
			}
			pointCategories, err := mmw.getAllPointCategories()
			if test.expectVeselkaError {
				assert.Error(t, err)
				return
			} else {
				assert.NoError(t, err)
			}

			err = mmw.UpdatePointCategoriesRedis(pointCategories)
			if test.expectRedisError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestModelManagerWatcher_ReloadModel(t *testing.T) {
	saveConfigGetComputerConfigPrefix := configGetComputerConfigPrefix
	saveConfigFeatureFlagEnabledIfExists := config.FeatureFlagEnabledIfExists
	defer func() {
		configGetComputerConfigPrefix = saveConfigGetComputerConfigPrefix
		config.FeatureFlagEnabledIfExists = saveConfigFeatureFlagEnabledIfExists
	}()

	appModelLogger.SetLevel(logrus.DebugLevel)
	saveConfigGetComputerConfigPrefix = func() string {
		return "test"
	}

	testCropID := "some-crop-id"
	testModel := &model_manager.Model{
		Model:                      veselka.Model{ID: "test-model-id"},
		LastUsedCropPointThreshold: 44.44,
		LastUsedWeedPointThreshold: 55.55,
	}
	testModelinator := &almanac.ModelinatorConfig{
		ModelId:  testModel.ID,
		CropId:   testCropID,
		Modified: false,
		Categories: []*almanac.ModelinatorTypeCategory{
			{
				Type: &almanac.TypeCategory{
					Category:       "foo",
					Classification: 0,
				},
				Trusts: []*almanac.ModelTrust{
					{
						MinDoo:            1,
						WeedingThreshold:  2,
						ThinningThreshold: 3,
						BandingThreshold:  4,
					},
				},
			},
		},
	}
	tests := []struct {
		name                          string
		currentModelID                string
		pinnedModelID                 string
		recommendedModelID            string
		lastActiveModelID             string
		modelType                     veselka.ModelType
		expectedModelID               string // best or resulting model
		pinningDefaultFeatureEnabled  bool
		copyModelinatorFeatureEnabled bool
	}{
		{
			"happy path deepweed pinned",
			"current-model-xyz",
			"new-pinned-model",
			"some-rec-model",
			"",
			veselka.ModelTypeDeepweed,
			"new-pinned-model",
			false,
			false,
		},
		{
			"recommend new model",
			"current-model-xyz",
			"",
			"some-rec-model",
			"",
			veselka.ModelTypeDeepweed,
			"some-rec-model",
			false,
			false,
		},
		{
			"recommend new model, default pinning feature",
			"current-model-xyz",
			"",
			"some-rec-model",
			"",
			veselka.ModelTypeDeepweed,
			"some-rec-model",
			true,
			false,
		},
		{
			"recommend new model, default pinning feature & modelinator copy feature",
			"current-model-xyz",
			"",
			"some-rec-model",
			"",
			veselka.ModelTypeDeepweed,
			"some-rec-model",
			true,
			true,
		},
		{
			"fall back to last active model",
			"",
			"",
			"",
			"last-active-model",
			veselka.ModelTypeDeepweed,
			"last-active-model",
			false,
			false,
		},
		{
			"pinned p2p",
			"current-model-xyz",
			"pinned-p2p-model",
			"",
			"",
			veselka.ModelTypeP2P,
			"pinned-p2p-model",
			false,
			false,
		},
		{
			"same p2p no change",
			"current-model-xyz",
			"current-model-xyz",
			"",
			"",
			veselka.ModelTypeP2P,
			"current-model-xyz",
			false,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			config.FeatureFlagEnabledIfExists = func(commonNode any, feature string) bool {
				switch feature {
				case config.FeatureFlagModelPinningDefault:
					return test.pinningDefaultFeatureEnabled
				case config.FeatureFlagCopyModelinator:
					return test.copyModelinatorFeatureEnabled
				}
				return false
			}
			commonNode := config.MockConfigTree{SubTrees: map[string]config.MockConfigTree{
				"feature_flags": {SubTrees: map[string]config.MockConfigTree{
					config.FeatureFlagModelPinningDefault: {
						Value: test.pinningDefaultFeatureEnabled,
					},
				},
				},
				path.Join(string(veselka.ModelTypeDeepweed), modelIDNodeName): {
					Value: test.currentModelID,
				},
				path.Join(string(veselka.ModelTypeP2P), modelIDNodeName): {
					Value: test.currentModelID,
				},
			},
			}
			currentCropIDNode := config.MockConfigTree{
				Value: testCropID,
			}
			cropIDsNode := config.MockConfigTree{SubTrees: map[string]config.MockConfigTree{
				testCropID: {SubTrees: map[string]config.MockConfigTree{
					enabledNodeName:          {Value: true},
					pinnedModelNodeName:      {Value: test.pinnedModelID},
					recommendedModelNodeName: {Value: test.recommendedModelID},
					lastActiveModelNodeName:  {Value: test.lastActiveModelID},
				}},
			}}
			pinnedNonCropModelNode := config.MockConfigTree{
				SubTrees: map[string]config.MockConfigTree{
					string(veselka.ModelTypeP2P): {
						Value: test.pinnedModelID,
					},
				},
			}

			mmState := NewModelManagerState()
			mmState.CurrentDeepWeedModelId = test.currentModelID
			mmState.CurrentP2PModelID = test.currentModelID
			mmState.EnabledCropsList = EnabledCropList{
				EnabledCrop{
					Crop: veselka.Crop{
						ID:         testCropID,
						CommonName: "common-name",
					},
					PinnedModel:      test.pinnedModelID,
					RecommendedModel: test.recommendedModelID,
					LastActiveModel:  test.lastActiveModelID,
				},
			}
			if len(test.currentModelID) > 0 {
				mmState.SyncedModels[test.currentModelID] = &model_manager.Model{
					Model:                      veselka.Model{ID: test.currentModelID},
					LastUsedWeedPointThreshold: 0,
					LastUsedCropPointThreshold: 0,
				}
			}
			if len(test.expectedModelID) > 0 {
				testModelinator.ModelId = test.expectedModelID
				testModel.ID = test.expectedModelID
				mmState.SyncedModels[test.expectedModelID] = testModel
			}

			mockConfigClient := new(mocks.ConfigClient)
			if test.currentModelID != test.expectedModelID {
				if test.modelType == veselka.ModelTypeDeepweed {
					lastActiveKey := path.Join(configGetComputerConfigPrefix(), commanderNodeName, cropIDsNodeName, testCropID, lastActiveModelNodeName)
					mockConfigClient.On("SetStringValue", lastActiveKey, test.expectedModelID).Return(nil)
					if test.pinningDefaultFeatureEnabled {
						pinnedKey := path.Join(configGetComputerConfigPrefix(), commanderNodeName, cropIDsNodeName, testCropID, pinnedModelNodeName)
						mockConfigClient.On("SetStringValue", pinnedKey, test.expectedModelID).Return(nil)
					}
				}
				modelIDKey := path.Join(commonNodeName, string(test.modelType), modelIDNodeName)
				mockConfigClient.On("SetStringValue", modelIDKey, test.expectedModelID).Return(nil)
			}

			mockHWMgrClient := new(mocks.HWMgrClient)
			mockHWMgrClient.On("GetGPSData", mock.Anything).
				Return(&hw_proto.GetGPSDataResponse{Lla: &hw_proto.GeoLLA{Lat: 47.6205, Lng: 122.3493}}, nil)

			mockVeselkaClient := new(mocks.VeselkaClient)
			if test.modelType == veselka.ModelTypeDeepweed && test.currentModelID != test.expectedModelID {
				mockVeselkaClient.On("GetModelParameters", mock.Anything, mock.MatchedBy(func(input []veselka.ModelParameterRequest) bool {
					return assert.NotEmpty(t, input) &&
						assert.Equal(t, test.expectedModelID, input[0].ModelID) &&
						assert.Equal(t, testCropID, input[0].CropID) &&
						assert.Equal(t, "y8rnbjh192d7", input[0].Geohash) &&
						assert.Equal(t, "slayerTest", input[0].RobotID)
				})).Return([]*almanac.ModelinatorConfig{testModelinator}, nil)
			}

			modelParametersKey := fmt.Sprintf("%s-%s", test.expectedModelID, testCropID)
			mockRedis := new(mocks.RedisClient)
			mockRedis.On("HGetWithContext", mock.Anything, modelParametersHashKey, modelParametersKey).
				Return("", redis.Nil)
			mockRedis.On("HSetWithContext", mock.Anything, modelParametersHashKey, modelParametersKey, mock.Anything).
				Return(nil)

			mockModelinatorCfgClient := new(mocks.ModelinatorConfigClient)
			if test.copyModelinatorFeatureEnabled {
				mockModelinatorCfgClient.On("CopyFrom", testCropID, test.currentModelID, test.expectedModelID).Return(nil)
			}

			mmw := &ModelManagerWatcher{
				configClient:            mockConfigClient,
				commonNode:              commonNode,
				currentCropIDNode:       currentCropIDNode,
				cropIDsNode:             cropIDsNode,
				pinnedNonCropModelsNode: pinnedNonCropModelNode,
				modelState:              mmState,
				syncChannel:             make(chan bool, 1000),
				downloadChannel:         make(chan bool, 1000),
				logger:                  appModelLogger.WithFields(logrus.Fields{"module": "TESTModelManagerWatcher"}),
				modelHistoryTracker: &ModelHistoryTracker{
					eventCh: make(chan ModelEvent, 1000),
					logger:  logrus.New().WithFields(logrus.Fields{"test": "mht"}),
				},
				veselkaClient:           mockVeselkaClient,
				redis:                   mockRedis,
				hardwareManagerClient:   mockHWMgrClient,
				env:                     environment.Robot{MakaRobotName: "slayerTest"},
				modelinatorConfigClient: mockModelinatorCfgClient,
			}
			err := mmw.ReloadModel(test.modelType)
			assert.NoError(t, err)
			mockConfigClient.AssertExpectations(t)
			mockVeselkaClient.AssertExpectations(t)
			mockModelinatorCfgClient.AssertExpectations(t)
			mmState.WriteOnCurrent(func() {})
			switch test.modelType {
			case veselka.ModelTypeP2P:
				assert.Equal(t, test.expectedModelID, mmState.CurrentP2PModelID)
			case veselka.ModelTypeDeepweed:
				assert.Equal(t, test.expectedModelID, mmState.CurrentDeepWeedModelId)
			default:
				t.Fatal("invalid model type")
			}
		})
	}
}

func TestModelManagerWatcher_ListModels(t *testing.T) {
	testCropID := "some-crop-id"

	testDeepweedModel1 := &model_manager.Model{
		Model: veselka.Model{
			ID:            "test-model-dw-1",
			Type:          veselka.ModelTypeDeepweed,
			ViableCropIDs: []string{testCropID},
		},
		LastUsedCropPointThreshold: 44.44,
		LastUsedWeedPointThreshold: 55.55,
	}
	testDeepweedModel2 := &model_manager.Model{
		Model: veselka.Model{
			ID:   "test-model-dw-2",
			Type: veselka.ModelTypeDeepweed,
		},
	}
	testP2PModel1 := &model_manager.Model{
		Model: veselka.Model{
			ID:   "test-p2p-1",
			Type: veselka.ModelTypeDeepweed,
		},
		LastUsedCropPointThreshold: 44.44,
		LastUsedWeedPointThreshold: 55.55,
	}
	testDownloadingModel1 := &model_manager.Model{
		Model: veselka.Model{
			ID:   "test-downloading-1",
			Type: veselka.ModelTypeDeepweed,
		},
	}

	testLocalModels := map[string]*model_manager.Model{
		testDeepweedModel1.ID: testDeepweedModel1,
		testDeepweedModel2.ID: testDeepweedModel2,
		testP2PModel1.ID:      testP2PModel1,
		"test-downloading-1": {
			Model: veselka.Model{
				ID:   "test-downloading-1",
				Type: veselka.ModelTypeDeepweed,
			},
		},
	}
	testDownloadingModelArtifacts := map[string]DownloadingModelArtifactState{
		testDownloadingModel1.ID: {
			Progress:        0.05,
			RemainingTimeMs: 50000,
		},
	}

	tests := []struct {
		name             string
		cropID           string
		activeModel      bool
		recommendedModel bool
		pinnedModel      bool
		maintainedModel  bool
	}{
		{
			"no crop id",
			"",
			false,
			false,
			false,
			false,
		},
		{
			"viable crop id",
			testCropID,
			false,
			false,
			false,
			false,
		},
		{
			"active",
			"foo-crop",
			true,
			false,
			false,
			false,
		},
		{
			"recommended",
			"foo-crop",
			false,
			true,
			false,
			false,
		},
		{
			"pinned",
			"foo-crop",
			false,
			false,
			true,
			false,
		},
		{
			"maintained",
			"foo-crop",
			false,
			false,
			false,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {

			activeModel := ""
			if test.activeModel {
				activeModel = testDeepweedModel1.ID
			}
			commonNode := config.MockConfigTree{
				SubTrees: map[string]config.MockConfigTree{
					path.Join(string(veselka.ModelTypeDeepweed), modelIDNodeName): {Value: activeModel},
					path.Join(string(veselka.ModelTypeP2P), modelIDNodeName):      {Value: testP2PModel1.ID},
				},
			}

			currentCropIDNode := config.MockConfigTree{
				Value: testCropID,
			}

			pinnedNonCropModelNode := config.MockConfigTree{
				SubTrees: map[string]config.MockConfigTree{
					string(veselka.ModelTypeP2P): {
						Value: testP2PModel1.ID,
					},
				},
			}

			mockRedis := new(mocks.RedisClient)
			mockRedis.On("ZRevRangeByScoreWithScores", mock.Anything, mock.Anything, mock.Anything).
				Return([]redis.Z{{Member: "test-nickname"}}, nil)

			rc1 := &RowClient{
				connected:    atomic.Bool{},
				syncedModels: []string{testP2PModel1.ID, testDeepweedModel1.ID},
			}
			rc1.connected.Store(true)

			pinnedModel, recommendedModel, maintainedModel := "", "", ""
			if test.pinnedModel {
				pinnedModel = testDeepweedModel1.ID
			}
			if test.recommendedModel {
				recommendedModel = testDeepweedModel1.ID
			}
			if test.maintainedModel {
				maintainedModel = testDeepweedModel1.ID
			}

			mmState := NewModelManagerState()
			mmState.LocalModels = testLocalModels
			mmState.DownloadingModelArtifacts = testDownloadingModelArtifacts
			mmState.EnabledCropsList = EnabledCropList{
				EnabledCrop{
					Crop: veselka.Crop{
						ID:         test.cropID,
						CommonName: "common-name",
					},
					PinnedModel:      pinnedModel,
					RecommendedModel: recommendedModel,
					MaintainedModels: []string{maintainedModel},
				},
			}

			mmw := &ModelManagerWatcher{
				commonNode:              commonNode,
				currentCropIDNode:       currentCropIDNode,
				pinnedNonCropModelsNode: pinnedNonCropModelNode,
				modelState:              mmState,
				redis:                   mockRedis,
				rows:                    RowClientList{rc1},
				logger:                  appModelLogger.WithFields(logrus.Fields{"module": t.Name()}),
			}
			gotModelInfo := mmw.ListModels(test.cropID)
			gotModelIDs := make([]string, 0)
			for _, mi := range gotModelInfo {
				gotModelIDs = append(gotModelIDs, mi.ID)
			}

			switch test.cropID {
			case "":
				assert.Contains(t, gotModelIDs, testDownloadingModel1.ID)
				assert.Contains(t, gotModelIDs, testP2PModel1.ID)
				assert.Contains(t, gotModelIDs, testDeepweedModel2.ID)
				assert.Contains(t, gotModelIDs, testDeepweedModel1.ID)
			case testCropID:
				assert.Contains(t, gotModelIDs, testDeepweedModel1.ID)
				assert.NotContains(t, gotModelIDs, testDeepweedModel2.ID)
				assert.NotContains(t, gotModelIDs, testDownloadingModel1.ID)
			default:
				assert.NotContains(t, gotModelIDs, testDeepweedModel2.ID)
				assert.NotContains(t, gotModelIDs, testDownloadingModel1.ID)
				if test.activeModel || test.recommendedModel || test.pinnedModel || test.maintainedModel {
					assert.Contains(t, gotModelIDs, testDeepweedModel1.ID)
				} else {
					assert.NotContains(t, gotModelIDs, testDeepweedModel1.ID)
				}
			}
		})
	}
}

func TestModelManagerWatcher_CopyModelsToArtifactsOnUpgrade(t *testing.T) {
	saveCmdrModelManagerFs := ModelManagerFs
	saveModelManagerAppFs := model_manager.AppFs
	defer func() {
		ModelManagerFs = saveCmdrModelManagerFs
		model_manager.AppFs = saveModelManagerAppFs
	}()

	appModelLogger.SetLevel(logrus.DebugLevel)

	testModelCacheDir := "/model_cache"
	testArtifactCacheDir := "/model_artifact_cache"
	testGoodModelArtifactBody := []byte("some data that pretends that it is a model 2x+3y=z")
	testComputeCapability := "8.6"
	testModelID := "some-model-id"
	testTensorRTVersion := "8.0.1.6"

	testModelArtifact := &veselka.ModelArtifact{
		Checksum:          fmt.Sprintf("%x", md5.Sum(testGoodModelArtifactBody)),
		ComputeCapability: testComputeCapability,
		ContentLength:     -1,
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
	}
	testGoodModel := &model_manager.Model{
		Model: veselka.Model{
			ID:       testModelID,
			URL:      "s3://foobar.trt",
			Type:     veselka.ModelTypeDeepweed,
			Checksum: fmt.Sprintf("%x", md5.Sum(testGoodModelArtifactBody)),
		},
		Downloaded: time.Now(),
	}

	veselkaMock := new(mocks.VeselkaClient)

	mmState := NewModelManagerState()
	mmw := &ModelManagerWatcher{
		modelState:            mmState,
		modelMetadataCacheDir: testModelCacheDir,
		modelArtifactCacheDir: testArtifactCacheDir,
		veselkaClient:         veselkaMock,
		logger:                appModelLogger.WithFields(logrus.Fields{"module": t.Name()}),
	}

	tests := []struct {
		name                    string
		model                   *model_manager.Model
		artifactsCacheDirExists bool
		artifactWritten         bool
		existingArtifacts       bool
	}{
		{"artifact cache folder exists empty",
			testGoodModel, true, false, false},
		{"artifact cache folder exists and all artifacts present",
			testGoodModel, true, true, false},
		{"successful folder creation and copy",
			testGoodModel, false, false, false},
		{"skip metadata file with existing artifacts",
			testGoodModel, false, false, false},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ModelManagerFs = afero.NewMemMapFs()
			ModelManagerFs.MkdirAll(testModelCacheDir, os.ModeDir)
			model_manager.AppFs = ModelManagerFs

			if test.existingArtifacts {
				// intentionally don't write model artifact to file system so we can
				// confirm the migration does not attempt to copy
				test.model.ModelArtifacts = []veselka.ModelArtifact{*testModelArtifact}
			}

			// we will have always written the metadata file by this point and
			// it will not contain any artifacts. Those are added to the model
			// during the migration
			writeModelMetaFile(t, ModelManagerFs, testModelCacheDir, test.model)

			// write models to old model cache dir
			oldModelFilePath := filepath.Join(testModelCacheDir, test.model.ID+model_manager.ModelExt)
			ModelManagerFs.Create(oldModelFilePath)
			if err := afero.WriteFile(ModelManagerFs, oldModelFilePath, testGoodModelArtifactBody, os.ModePerm); err != nil {
				t.Fatal(err)
			}

			if test.artifactsCacheDirExists {
				ModelManagerFs.MkdirAll(testArtifactCacheDir, os.ModeDir)

				if test.artifactWritten {
					writeModelArtifactFile(t, ModelManagerFs, testArtifactCacheDir, testModelArtifact.ModelManagerArtifactID(), testGoodModelArtifactBody)
				}
			}

			err := mmw.copyModelsToArtifactsOnUpgrade(context.TODO())

			if test.existingArtifacts {
				assert.NoError(t, err)

				// confirm the directory exists, but no new artifacts were written
				_, err := ModelManagerFs.Stat(testArtifactCacheDir)
				assert.NoError(t, err)
				_, err = ModelManagerFs.Stat(filepath.Join(testArtifactCacheDir, testModelArtifact.ModelManagerArtifactID()+model_manager.ModelExt))
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				verifyModelMetadataFile(t, testModelCacheDir, test.model)

				// confirm artifact file was written
				assert.True(t, verifyModelArtifactDownload(t, testArtifactCacheDir, testModelArtifact))

				// confirm the model metadata was updated
				metaFilename := filepath.Join(testModelCacheDir, test.model.ID+model_manager.MetaExt)
				modelFromMetadata, err := model_manager.GetVerifyMetadata(metaFilename)
				assert.NoError(t, err)
				assert.Equal(t, modelFromMetadata.ModelArtifacts, []veselka.ModelArtifact{*testModelArtifact})
			}
		})
	}
}

func TestModelManagerWatcher_CompareSemanticVersions(t *testing.T) {
	tests := []struct {
		name        string
		ver1        string
		ver2        string
		expected    int
		expectedErr bool
	}{
		{"same version", "1.0.0", "1.0.0", 0, false},
		{"same major version", "1.0.0", "1.1.0", -1, false},
		{"same minor version", "1.0.0", "1.0.1", -1, false},
		{"same patch version", "1.0.0", "1.0.0", 0, false},
		{"greater major version", "2.0.0", "1.0.0", 1, false},
		{"greater minor version", "1.1.0", "1.0.0", 1, false},
		{"greater patch version", "1.0.1", "1.0.0", 1, false},
		{"greater minor patch version", "1.0.0.1", "1.0.0.0", 1, false},
		{"only two fields", "1.0", "0.7", 1, false},
		{"invalid left version", "1.asdf.0", "1.0.0", 0, true},
		{"invalid right version", "1.0.0", "1.asdf.0", 0, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := compareSemanticVersions(test.ver1, test.ver2)
			if test.expectedErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expected, got)
			}
		})
	}
}

func TestModelManagerWacher_FilterModelArtifacts(t *testing.T) {
	appModelLogger.SetLevel(logrus.DebugLevel)

	testCacheDir := "/cache"
	testModelID := "test-model"
	testComputeCapability1 := "8.6"
	testComputeCapability2 := "8.7"
	testTensorRTVersion1 := "8.0.1.6"
	testTensorRTVersion2 := "8.0.1.7"

	artifact1 := veselka.ModelArtifact{
		ID:                "test-model-artifact1",
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion1,
		ComputeCapability: testComputeCapability1,
	}
	artifact2 := veselka.ModelArtifact{
		ID:                "test-model-artifact2",
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion1,
		ComputeCapability: testComputeCapability2,
	}
	artifact3 := veselka.ModelArtifact{
		ID:                "test-model-artifact3",
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion2,
		ComputeCapability: testComputeCapability1,
	}

	// seed model with every artifact imaginable
	veselkaModel := &veselka.Model{
		ID:   testModelID,
		Type: veselka.ModelTypeDeepweed,
		ModelArtifacts: []veselka.ModelArtifact{
			artifact1, artifact2, artifact3},
	}

	tests := []struct {
		name                      string
		computeCapabilities       []string
		supportedTensorRTVersions []string
		desiredArtifacts          []veselka.ModelArtifact
	}{
		{"happy path one artifact",
			[]string{testComputeCapability1}, []string{testTensorRTVersion1}, []veselka.ModelArtifact{artifact1}},
		{"multiple compute capabilities",
			[]string{testComputeCapability1, testComputeCapability2}, []string{testTensorRTVersion1}, []veselka.ModelArtifact{artifact1, artifact2}},
		{"multiple tensorRT versions, only take highest",
			[]string{testComputeCapability1}, []string{testTensorRTVersion1, testTensorRTVersion2}, []veselka.ModelArtifact{artifact3}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Create a mock RowClient
			mockRowClient := new(mocks.CVRuntimeClient)
			mockRowClient.On("GetCVSupportedTensorRTVersions").
				Return(&cv.SupportedTensorRTVersionsResponse{Versions: test.supportedTensorRTVersions}, nil)
			mockRowClient.On("GetCVComputeCapabilities").
				Return(&cv.ComputeCapabilitiesResponse{Capabilities: test.computeCapabilities}, nil)

			testRowClient := &RowClient{cvRuntimeClient: mockRowClient}
			rcl := RowClientList{testRowClient}

			// Create a mock ModelManagerState
			mockModelState := &ModelManagerState{
				LocalModels:            make(map[string]*model_manager.Model),
				computeCapabilityCache: test.computeCapabilities,
				tensorRTVersionCache:   test.supportedTensorRTVersions,
			}

			// Create a mock ModelManagerWatcher
			mockWatcher := &ModelManagerWatcher{
				modelState:            mockModelState,
				modelMetadataCacheDir: testCacheDir,
				rows:                  rcl,
				logger:                appModelLogger.WithFields(logrus.Fields{"module": t.Name()}),
			}

			artifacts := mockWatcher.filterModelArtifacts(veselkaModel)
			sort.Slice(test.desiredArtifacts, func(i, j int) bool {
				return test.desiredArtifacts[i].ID < test.desiredArtifacts[j].ID
			})
			sort.Slice(artifacts, func(i, j int) bool {
				return artifacts[i].ID < artifacts[j].ID
			})

			// assert that the correct artifacts were returned
			assert.Equal(t, artifacts, test.desiredArtifacts)
		})
	}
}

func TestModelManagerWatcher_UpdateModelMetadata(t *testing.T) {
	appModelLogger.SetLevel(logrus.DebugLevel)

	testCacheDir := "/cache"
	testModelID := "test-model"
	testModelArtifactID := "test-model-artifact"
	testTensorRTVersion := "8.0.1.6"
	testComputeCapability := "8.6"
	testArtifactURL := "https://carbonrobotics.com/model"
	testChecksum := "1234567890"

	testModelArtifact := &veselka.ModelArtifact{
		ID:                testModelArtifactID,
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
		ComputeCapability: testComputeCapability,
		URL:               testArtifactURL,
		Checksum:          testChecksum,
	}

	testModel := &model_manager.Model{
		Model: veselka.Model{
			ID:             testModelID,
			Type:           veselka.ModelTypeDeepweed,
			ModelArtifacts: []veselka.ModelArtifact{*testModelArtifact},
		},
	}

	// Create a mock RowClient
	mockRowClient := new(mocks.CVRuntimeClient)
	mockRowClient.On("GetCVSupportedTensorRTVersions").
		Return(&cv.SupportedTensorRTVersionsResponse{Versions: []string{testTensorRTVersion}}, nil)
	mockRowClient.On("GetCVComputeCapabilities").
		Return(&cv.ComputeCapabilitiesResponse{Capabilities: []string{testComputeCapability}}, nil)

	testRowClient := &RowClient{cvRuntimeClient: mockRowClient}
	rcl := RowClientList{testRowClient}

	// Create a mock VeselkaClient
	mockVeselkaClient := new(mocks.VeselkaClient)
	mockVeselkaClient.On("GetModelInfo", mock.Anything, mock.Anything).Return(&veselka.Model{
		ID:             testModelID,
		Type:           veselka.ModelTypeDeepweed,
		ModelArtifacts: []veselka.ModelArtifact{*testModelArtifact},
	}, nil)

	// Create a mock ModelManagerState
	mockModelState := &ModelManagerState{
		LocalModels: make(map[string]*model_manager.Model),
	}
	mockModelState.initialize()

	// Create a mock ModelManagerWatcher
	ModelManagerFs = afero.NewMemMapFs()
	ModelManagerFs.MkdirAll(testCacheDir, os.ModeDir)
	model_manager.AppFs = ModelManagerFs
	mockWatcher := &ModelManagerWatcher{
		modelState:            mockModelState,
		modelMetadataCacheDir: testCacheDir,
		veselkaClient:         mockVeselkaClient,
		rows:                  rcl,
		logger:                appModelLogger.WithFields(logrus.Fields{"module": t.Name()}),
	}

	// Call the function being tested
	_, err := mockWatcher.UpdateModelMetadata(testModelID, true)

	assert.NoError(t, err)
	verifyModelMetadataFile(t, testCacheDir, testModel)
}

func TestModelManagerWatcher_DownloadModelArtifact(t *testing.T) {
	saveCmdrModelManagerFs := ModelManagerFs
	saveModelManagerAppFs := model_manager.AppFs
	defer func() {
		ModelManagerFs = saveCmdrModelManagerFs
		model_manager.AppFs = saveModelManagerAppFs
	}()

	appModelLogger.SetLevel(logrus.DebugLevel)

	testCacheDir := "/cache"
	testArtifactDir := "/artifacts"
	testGoodModelBody := []byte("some data that pretends that it is a model 2x+3y=z")
	testComputeCapability := "infinite"
	testModelID := "some-model-id"
	testTensorRTVersion := "4"
	testModelArtifact := &veselka.ModelArtifact{
		Checksum:          fmt.Sprintf("%x", md5.Sum(testGoodModelBody)),
		ComputeCapability: testComputeCapability,
		ContentLength:     int64(len(testGoodModelBody)),
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
	}
	testGoodModel := &model_manager.Model{
		Model: veselka.Model{
			ID:             testModelID,
			URL:            "s3://foobar.trt",
			Type:           veselka.ModelTypeDeepweed,
			Checksum:       fmt.Sprintf("%x", md5.Sum(testGoodModelBody)),
			ModelArtifacts: []veselka.ModelArtifact{*testModelArtifact},
		},
		Downloaded: time.Now(),
	}
	testBadModel := &model_manager.Model{
		Model:      veselka.Model{ID: "bad"},
		Downloaded: time.Now(),
	}

	mockRowClient := new(mocks.CVRuntimeClient)
	mockRowClient.On("GetCVSupportedTensorRTVersions").
		Return(&cv.SupportedTensorRTVersionsResponse{Versions: []string{testTensorRTVersion}}, nil)
	mockRowClient.On("GetCVComputeCapabilities").
		Return(&cv.ComputeCapabilitiesResponse{Capabilities: []string{testComputeCapability}}, nil)

	testRowClient := &RowClient{cvRuntimeClient: mockRowClient}
	rcl := RowClientList{testRowClient}

	tests := []struct {
		name         string
		model        *model_manager.Model
		responseBody []byte
		modelExists  bool
		getModelErr  error
		expectError  bool
	}{
		{"file exists",
			testGoodModel, testGoodModelBody, true, nil, false},
		{"file is downloaded",
			testGoodModel, testGoodModelBody, false, nil, false},
		{"bad download",
			testGoodModel, []byte("bad data"), false, nil, true},
		{"bad meta",
			testBadModel, testGoodModelBody, false, nil, true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ModelManagerFs = afero.NewMemMapFs()
			ModelManagerFs.MkdirAll(testCacheDir, os.ModeDir)
			ModelManagerFs.MkdirAll(testArtifactDir, os.ModeDir)
			model_manager.AppFs = ModelManagerFs
			mmState := NewModelManagerState()

			// we will have always written the metadata file by this point
			writeModelMetaFile(t, ModelManagerFs, testCacheDir, test.model)

			if test.modelExists {
				writeModelArtifactFile(t, ModelManagerFs, testArtifactDir, testModelArtifact.ModelManagerArtifactID(), test.responseBody)
				mmState.LocalModels[test.model.ID] = test.model
			}

			veselkaMock := new(mocks.VeselkaClient)
			veselkaMock.On("GetModelArtifact", mock.Anything, testComputeCapability, test.model.ID, testTensorRTVersion, mock.AnythingOfType("int64")).
				Return(&http.Response{
					ContentLength: int64(len(test.responseBody)),
					Body:          io.NopCloser(bytes.NewReader(test.responseBody))}, test.getModelErr)
			veselkaMock.On("GetModelInfos", mock.Anything, []string{test.model.ID}).
				Return(map[string]*veselka.Model{
					test.model.ID: &test.model.Model,
				}, nil)

			mmw := &ModelManagerWatcher{
				modelState:            mmState,
				modelMetadataCacheDir: testCacheDir,
				modelArtifactCacheDir: testArtifactDir,
				veselkaClient:         veselkaMock,
				rows:                  rcl,
				logger:                appModelLogger.WithFields(logrus.Fields{"module": t.Name()}),
			}

			err := mmw.DownloadModelArtifact(context.TODO(), *testModelArtifact)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				verifyModelMetadataFile(t, testCacheDir, test.model)

				// confirm artifact file was written
				assert.True(t, verifyModelArtifactDownload(t, testArtifactDir, testModelArtifact))
			}
		})
	}
}

func TestModelManagerWatcher_GetDefaultModelParameters(t *testing.T) {
	testModelID := "foo-bar-123"
	testCropID := "crop-id-xxxyyy"
	testModelinatorConfig := &almanac.ModelinatorConfig{
		ModelId: testModelID,
		CropId:  testCropID,
	}
	testModelinatorJSON, err := json.Marshal(testModelinatorConfig)
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name        string
		redisVal    string
		redisErr    error
		expectError bool
	}{
		{
			"happy path",
			string(testModelinatorJSON),
			nil,
			false,
		},
		{
			"invalid stored modeliantor json",
			"}{",
			nil,
			true,
		},
		{
			"redis error",
			string(testModelinatorJSON),
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedHashKeyField := fmt.Sprintf("%s-%s", testModelID, testCropID)
			mockRedis := new(mocks.RedisClient)
			mockRedis.On("HGetWithContext", mock.Anything, modelParametersHashKey, expectedHashKeyField).
				Return(test.redisVal, test.redisErr)

			mmw := &ModelManagerWatcher{
				redis: mockRedis,
			}
			got, err := mmw.GetDefaultModelParameters(testModelID, testCropID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, testModelinatorConfig, got)
				mockRedis.AssertExpectations(t)
			}
		})
	}
}

func TestModelManagerWatcher_SaveDefaultModelParameters(t *testing.T) {
	testModelID := "foo-bar-123"
	testCropID := "crop-id-xxxyyy"
	testModelinatorConfig := &almanac.ModelinatorConfig{
		ModelId: testModelID,
		CropId:  testCropID,
	}
	testModelinatorJSON, err := json.Marshal(testModelinatorConfig)
	if err != nil {
		t.Fatal(err)
	}
	testModelinatorConfig2 := &almanac.ModelinatorConfig{
		ModelId:  testModelID,
		CropId:   testCropID,
		Modified: true,
	}
	testModelinatorJSON2, err := json.Marshal(testModelinatorConfig2)
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name                    string
		existingModelinatorJSON []byte
		redisGetErr             error
		redisSetErr             error
		expectError             bool
		expectUpdate            bool
	}{
		{
			"happy path update",
			testModelinatorJSON2,
			nil,
			nil,
			false,
			true,
		},
		{
			"happy path update, redis.Nil error",
			testModelinatorJSON2,
			redis.Nil,
			nil,
			false,
			true,
		},
		{
			"happy path no update",
			testModelinatorJSON,
			nil,
			nil,
			false,
			false,
		},
		{
			"redis retrieval error",
			testModelinatorJSON,
			assert.AnError,
			nil,
			true,
			false,
		},
		{
			"redis set error",
			testModelinatorJSON2,
			nil,
			assert.AnError,
			true,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedHashKeyField := fmt.Sprintf("%s-%s", testModelID, testCropID)

			mockRedis := new(mocks.RedisClient)
			mockRedis.On("HGetWithContext", mock.Anything, modelParametersHashKey, expectedHashKeyField).
				Return(string(test.existingModelinatorJSON), test.redisGetErr)
			mockRedis.On("HSetWithContext", mock.Anything, modelParametersHashKey, expectedHashKeyField, string(testModelinatorJSON)).
				Return(test.redisSetErr)

			callbackCalledCh := make(chan bool, 5)
			modelState := &ModelManagerState{
				modelParameterChangeCallbacks: map[string]func(cfg *almanac.ModelinatorConfig){
					"test_cb": func(cfg *almanac.ModelinatorConfig) {
						callbackCalledCh <- true
					},
				},
			}
			mmw := &ModelManagerWatcher{
				modelState: modelState,
				redis:      mockRedis,
				logger:     appModelLogger.WithFields(logrus.Fields{"test": t.Name()}),
				modelHistoryTracker: &ModelHistoryTracker{
					eventCh: make(chan ModelEvent, 1000),
					logger:  appModelLogger.WithFields(logrus.Fields{"test": t.Name()}),
				},
			}

			err := mmw.SaveDefaultModelParameters(testModelID, testCropID, testModelinatorConfig)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				mockRedis.AssertNumberOfCalls(t, "HGetWithContext", 1)
				if test.expectUpdate {
					mockRedis.AssertNumberOfCalls(t, "HSetWithContext", 1)
					select {
					case <-callbackCalledCh:
					case <-time.After(100 * time.Millisecond):
						t.Fail()
					}
				}
			}
		})
	}
}

func TestModelManagerWatcher_GetModelNickname(t *testing.T) {
	testModelID := "test-model-foo"
	testModelNickname := "test-model-nick"
	tests := []struct {
		name                              string
		redisResult                       []redis.Z
		redisZRevRangeByScoreWithScoreErr error
		expectError                       bool
	}{
		{
			"happy path, got nickname",
			[]redis.Z{{Member: testModelNickname}},
			nil,
			false,
		},
		{
			"no nickname",
			[]redis.Z{},
			nil,
			true,
		},
		{
			"failed redis",
			[]redis.Z{},
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			modelNickNameKey := fmt.Sprint(modelNicknameKeyPrefix, testModelID)

			mockRedis := new(mocks.RedisClient)
			mockRedis.On("ZRevRangeByScoreWithScores", mock.Anything, modelNickNameKey, &redis.ZRangeBy{
				Min:   crredis.NegInfinity,
				Max:   crredis.Infinity,
				Count: 1,
			}).Return(test.redisResult, test.redisZRevRangeByScoreWithScoreErr)
			mmw := &ModelManagerWatcher{
				redis: mockRedis,
			}
			got, err := mmw.GetModelNickname(testModelID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, testModelNickname, got)
			}
		})
	}
}

func TestModelManagerWatcher_SaveModelNickname(t *testing.T) {
	testModelID := "foo-model-bar"
	testModelNickname := "foofoo-barbar"
	testTime := time.Now()

	tests := []struct {
		name        string
		zAddErr     error
		expectError bool
	}{
		{
			"happy path",
			nil,
			false,
		},
		{
			"zadd err",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			modelNickNameKey := fmt.Sprint(modelNicknameKeyPrefix, testModelID)

			zReq := &redis.Z{Score: float64(testTime.UnixMilli()), Member: testModelNickname}
			redisMock := new(mocks.RedisClient)
			redisMock.On("ZAdd", mock.Anything, modelNickNameKey, zReq).
				Return(int64(0), test.zAddErr)

			mmw := &ModelManagerWatcher{
				redis:               redisMock,
				modelHistoryTracker: &ModelHistoryTracker{eventCh: make(chan ModelEvent, 1000)},
				logger:              appModelLogger.WithFields(logrus.Fields{"test": t.Name()}),
			}
			err := mmw.SaveModelNickname(testModelID, testModelNickname, testTime)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestModelManagerWatcher_DeleteModelNickname(t *testing.T) {
	testModelID := "foo-model-bar"

	tests := []struct {
		name             string
		zRemRangeByScore error
		expectError      bool
	}{
		{
			"happy path",
			nil,
			false,
		},
		{
			"zremrangebyscore err",
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			modelNickNameKey := fmt.Sprint(modelNicknameKeyPrefix, testModelID)

			redisMock := new(mocks.RedisClient)
			redisMock.On("ZRemRangeByScore", mock.Anything, modelNickNameKey, crredis.NegInfinity, crredis.Infinity).
				Return(int64(0), test.zRemRangeByScore)

			mmw := &ModelManagerWatcher{
				redis:               redisMock,
				modelHistoryTracker: &ModelHistoryTracker{eventCh: make(chan ModelEvent, 1000)},
				logger:              appModelLogger.WithFields(logrus.Fields{"test": t.Name()}),
			}
			err := mmw.DeleteModelNickname(testModelID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestModelManagerWatcher_getBestModelForCropID(t *testing.T) {
	testEnabledCropID := "999-888-777"
	testModel := &model_manager.Model{
		Model: veselka.Model{
			ID:            "test-model-1",
			ViableCropIDs: []string{testEnabledCropID},
			Created:       1695662160562,
		},
	}
	testModel2 := &model_manager.Model{
		Model: veselka.Model{
			ID:            "test-model-2",
			ViableCropIDs: []string{testEnabledCropID},
			Created:       1695662160562,
		},
	}
	testModel3 := &model_manager.Model{
		Model: veselka.Model{
			ID:            "test-model-3",
			ViableCropIDs: []string{testEnabledCropID},
			Created:       1695662160562,
		},
	}
	testNonViableModel := &model_manager.Model{
		Model: veselka.Model{
			ID: "not-viable-model",
		},
	}
	testLatestModel := &model_manager.Model{
		Model: veselka.Model{
			ID:            "latest-model",
			ViableCropIDs: []string{testEnabledCropID},
			Created:       time.Now().UnixMilli(),
		},
	}

	tests := []struct {
		name               string
		cropID             string
		recommendedModelID string
		pinnedModelID      string
		lastActiveModelID  string
		syncedModels       map[string]*model_manager.Model
		expectedModelID    string
		expectError        bool
	}{
		{"pinned model selection",
			testEnabledCropID,
			testModel2.ID,
			testModel.ID,
			testModel3.ID,
			map[string]*model_manager.Model{
				testModel.ID:          testModel,
				testModel2.ID:         testModel2,
				testModel3.ID:         testModel3,
				testNonViableModel.ID: testNonViableModel,
				testLatestModel.ID:    testLatestModel,
			},
			testModel.ID,
			false,
		},
		{"recommended model selection",
			testEnabledCropID,
			testModel.ID,
			"",
			testModel3.ID,
			map[string]*model_manager.Model{
				testModel.ID:          testModel,
				testModel2.ID:         testModel2,
				testModel3.ID:         testModel3,
				testNonViableModel.ID: testNonViableModel,
				testLatestModel.ID:    testLatestModel,
			},
			testModel.ID,
			false,
		},
		{"last active model selection",
			testEnabledCropID,
			testModel2.ID,
			"",
			testModel.ID,
			map[string]*model_manager.Model{
				testModel.ID:          testModel,
				testNonViableModel.ID: testNonViableModel,
				testLatestModel.ID:    testLatestModel,
			},
			testModel.ID,
			false,
		},
		{"latest viable model selection",
			testEnabledCropID,
			testModel2.ID,
			"",
			testModel.ID,
			map[string]*model_manager.Model{
				testNonViableModel.ID: testNonViableModel,
				testLatestModel.ID:    testLatestModel,
			},
			testLatestModel.ID,
			false,
		},
		{"no models",
			testEnabledCropID,
			testModel2.ID,
			"",
			testModel.ID,
			map[string]*model_manager.Model{},
			"",
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			enabledCrop := EnabledCrop{
				RecommendedModel: test.recommendedModelID,
				PinnedModel:      test.pinnedModelID,
				LastActiveModel:  test.lastActiveModelID,
				Crop: veselka.Crop{
					ID:          testEnabledCropID,
					CommonName:  "FooCrop",
					CaptureOnly: false,
				},
			}
			enabledCropList := EnabledCropList{enabledCrop}
			mmstate := &ModelManagerState{
				EnabledCropsList: enabledCropList,
				SyncedModels:     test.syncedModels,
			}
			mmw := &ModelManagerWatcher{
				modelState:          mmstate,
				downloadChannel:     make(chan bool, 500),
				modelHistoryTracker: &ModelHistoryTracker{eventCh: make(chan ModelEvent, 1000)},
				logger:              appModelLogger.WithFields(logrus.Fields{"test": t.Name()}),
			}
			got, err := mmw.getBestModelForCropID(test.cropID)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedModelID, got.ID)
			}
		})
	}
}

func TestModelManagerWatcher_getBestP2PModel(t *testing.T) {
	testModel := &model_manager.Model{
		Model: veselka.Model{
			ID:      "test-model-1",
			Created: 1695662160562,
			Type:    veselka.ModelTypeP2P,
		},
	}
	testModel2 := &model_manager.Model{
		Model: veselka.Model{
			ID:      "test-model-2",
			Created: 1695662160562,
			Type:    veselka.ModelTypeP2P,
		},
	}
	testLatestModel := &model_manager.Model{
		Model: veselka.Model{
			ID:      "latest-model",
			Created: time.Now().UnixMilli(),
			Type:    veselka.ModelTypeP2P,
		},
	}

	tests := []struct {
		name              string
		pinnedModelID     string
		defaultP2PModelID string
		syncedModels      map[string]*model_manager.Model
		expectedModelID   string
		expectError       bool
	}{
		{
			"pinned p2p",
			testModel.ID,
			testModel2.ID,
			map[string]*model_manager.Model{
				testModel.ID:  testModel,
				testModel2.ID: testModel2,
			},
			testModel.ID,
			false,
		},
		{
			"default p2p",
			"",
			testModel2.ID,
			map[string]*model_manager.Model{
				testModel.ID:  testModel,
				testModel2.ID: testModel2,
			},
			testModel2.ID,
			false,
		},
		{
			"fallback to latest p2p",
			"",
			"",
			map[string]*model_manager.Model{
				testModel.ID:       testModel,
				testModel2.ID:      testModel2,
				testLatestModel.ID: testLatestModel,
			},
			testLatestModel.ID,
			false,
		},
		{
			"no model found",
			"",
			"",
			map[string]*model_manager.Model{},
			"",
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			pinnedNonCropModelsNode := config.MockConfigTree{
				SubTrees: map[string]config.MockConfigTree{
					p2pNodeName: {
						Value: test.pinnedModelID,
					},
				},
			}

			mmstate := &ModelManagerState{
				defaultP2PModelID: test.defaultP2PModelID,
				SyncedModels:      test.syncedModels,
			}
			mmw := &ModelManagerWatcher{
				modelState:              mmstate,
				downloadChannel:         make(chan bool, 500),
				pinnedNonCropModelsNode: pinnedNonCropModelsNode,
				modelHistoryTracker:     &ModelHistoryTracker{eventCh: make(chan ModelEvent, 1000)},
				logger:                  appModelLogger.WithFields(logrus.Fields{"test": t.Name()}),
			}
			got, err := mmw.getBestP2PModel()
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedModelID, got.ID)
			}
		})
	}
}

func verifyModelMetadataFile(t *testing.T, cacheDir string, model *model_manager.Model) {
	t.Helper()
	m, err := model_manager.GetVerifyMetadata(filepath.Join(cacheDir, model.MetaFilename()))
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, model.ID, m.ID)
}

func verifyModelArtifactDownload(t *testing.T, cacheDir string, artifact *veselka.ModelArtifact) bool {
	t.Helper()
	files, _ := afero.ReadDir(ModelManagerFs, cacheDir)
	for _, file := range files {
		if file.Name() == artifact.ModelManagerArtifactID()+model_manager.ModelExt {
			contents, _ := afero.ReadFile(ModelManagerFs, filepath.Join(cacheDir, artifact.ModelManagerArtifactID()+model_manager.ModelExt))
			if fmt.Sprintf("%x", md5.Sum(contents)) == artifact.Checksum {
				return true
			}
		}
	}
	return false
}

func writeModelMetaFile(t *testing.T, fs afero.Fs, cacheDir string, model *model_manager.Model) {
	t.Helper()
	metaFilename := filepath.Join(cacheDir, model.ID+model_manager.MetaExt)
	if err := afero.WriteFile(fs, metaFilename, []byte(model.String()), os.ModePerm); err != nil {
		t.Fatal(err)
	}
}

func writeModelArtifactFile(t *testing.T, fs afero.Fs, cacheDir, artifactID string, body []byte) {
	t.Helper()
	modelFile := filepath.Join(cacheDir, artifactID+model_manager.ModelExt)
	if err := afero.WriteFile(fs, modelFile, body, os.ModePerm); err != nil {
		t.Fatal(err)
	}
}

func TestEnabledCrop_Localize(t *testing.T) {
	tests := []struct {
		name                 string
		enabledCrop          *EnabledCrop
		language             string
		expectedLocalization LocalizeCrop
	}{
		{
			"happy path, no language",
			&EnabledCrop{
				Crop: veselka.Crop{
					CommonName:   "foo",
					Description:  "bar",
					Translations: nil,
				},
				customNameOverride: false,
			},
			"",
			LocalizeCrop{
				Name:        "foo",
				Description: "bar",
			},
		},
		{
			"localize",
			&EnabledCrop{
				Crop: veselka.Crop{
					CommonName:  "foo",
					Description: "bar",
					Translations: []veselka.CropTranslation{
						{
							Name:        "name fr",
							Description: "desc fr",
							Language:    "fr",
							Version:     1,
						},
						{
							Name:        "name en",
							Description: "desc en",
							Language:    "en",
							Version:     1,
						},
					},
				},
				customNameOverride: false,
			},
			"fr",
			LocalizeCrop{
				Name:        "name fr",
				Description: "desc fr",
			},
		},
		{
			"localize with override",
			&EnabledCrop{
				Crop: veselka.Crop{
					CommonName:  "foo",
					Description: "bar",
					Translations: []veselka.CropTranslation{
						{
							Name:        "name fr",
							Description: "desc fr",
							Language:    "fr",
							Version:     1,
						},
						{
							Name:        "name en",
							Description: "desc en",
							Language:    "en",
							Version:     1,
						},
					},
				},
				customNameOverride: true,
			},
			"fr",
			LocalizeCrop{
				Name:        "foo",
				Description: "desc fr",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := test.enabledCrop.Localize(test.language)
			assert.Equal(t, test.expectedLocalization, got)
		})
	}
}

func Test_parseConfigDuration(t *testing.T) {
	tests := []struct {
		name             string
		interval         string
		defaultInterval  time.Duration
		expectedDuration time.Duration
	}{
		{
			"happy path",
			"10h5m20s",
			0,
			10*time.Hour + 5*time.Minute + 20*time.Second,
		},
		{
			"fallback to default",
			"not an interval",
			time.Hour,
			time.Hour,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tree := config.MockConfigTree{
				Name:  "test-name",
				Value: test.interval,
			}
			got := parseConfigDuration(tree, test.defaultInterval)
			assert.Equal(t, test.expectedDuration, got)
		})
	}
}

func TestModelManagerState_GetActiveAlarms(t *testing.T) {
	testAlarm1 := modelManagerAlarm{identifier: "one"}
	testAlarm2 := modelManagerAlarm{identifier: "two"}

	tests := []struct {
		name                 string
		activatedAlarms      []modelManagerAlarm
		deactiveatedAlarms   []string
		expectedActiveAlarms int
	}{
		{
			"none",
			nil,
			nil,
			0,
		},
		{
			"active",
			[]modelManagerAlarm{testAlarm1, testAlarm2},
			nil,
			2,
		},
		{
			"activate 1 disable 1",
			[]modelManagerAlarm{testAlarm1},
			[]string{testAlarm2.identifier},
			1,
		},
		{
			"deactivate nothing",
			[]modelManagerAlarm{},
			[]string{testAlarm1.identifier, testAlarm2.identifier, "foo", "bar"},
			0,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mms := ModelManagerState{}
			for _, alrm := range test.activatedAlarms {
				mms.activateAlarm(alrm, map[string]any{"test": "test"})
			}
			for _, id := range test.deactiveatedAlarms {
				mms.deactivateAlarm(id)
			}
			got := mms.GetActiveAlarms()
			assert.Len(t, got, test.expectedActiveAlarms)
			for _, alarm := range got {
				assert.Equal(t, alarm.params, map[string]any{"test": "test"})
			}
		})
	}
}

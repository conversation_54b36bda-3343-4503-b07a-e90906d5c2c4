package state

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/stretchr/testify/assert"
)

func TestValidatePoint(t *testing.T) {
	tests := []struct {
		name        string
		point       *frontend.Location
		expectError bool
	}{
		{
			"valid point",
			&frontend.Location{
				Latitude:  45.34,
				Longitude: 22.333,
				Altitude:  34.0,
			},
			false,
		},
		{
			"invalid lat > 90",
			&frontend.Location{
				Latitude:  91.0,
				Longitude: 22.333,
				Altitude:  34.0,
			},
			true,
		},
		{
			"invalid lat < -90",
			&frontend.Location{
				Latitude:  -91.0,
				Longitude: 22.333,
				Altitude:  34.0,
			},
			true,
		},
		{
			"invalid lng > 180",
			&frontend.Location{
				Latitude:  10.0,
				Longitude: 181.0,
				Altitude:  34.0,
			},
			true,
		},
		{
			"invalid lng < -180",
			&frontend.Location{
				Latitude:  -23.0,
				Longitude: -181.0,
				Altitude:  34.0,
			},
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := ValidatePoint(test.point)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

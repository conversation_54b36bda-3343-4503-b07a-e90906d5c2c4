package state

import (
	"context"
	"time"
)

type Runnable interface {
	Run()
}

type Actionable interface {
	Action()
	Triggerable
}

type Triggerable interface {
	Trigger()
	GetTriggeredEventChannel() chan bool
}

type EventTrigger struct {
	triggerChannel chan bool
}

func (t *EventTrigger) Trigger() {
	select {
	case t.triggerChannel <- true:
	default:
	}
}

func (t *EventTrigger) GetTriggeredEventChannel() chan bool {
	return t.triggerChannel
}

type IntervalLoopExecutor struct {
	Runnable
	Actionable
	stopCtx  context.Context
	interval time.Duration
}

var intervalDefault = 500 * time.Millisecond

func NewIntervalLoopExecutor(stopCtx context.Context, a Actionable) *IntervalLoopExecutor {
	return &IntervalLoopExecutor{
		stopCtx:    stopCtx,
		Actionable: a,
		interval:   intervalDefault,
	}
}

func NewIntervalLoopExecutorWithInterval(stopCtx context.Context, a Actionable, interval time.Duration) *IntervalLoopExecutor {
	return &IntervalLoopExecutor{
		stopCtx:    stopCtx,
		Actionable: a,
		interval:   interval,
	}
}

func (e *IntervalLoopExecutor) Run() {
	for {
		e.Action()
		select {
		case <-e.stopCtx.Done():
			return
		case <-time.After(e.interval):
		case <-e.GetTriggeredEventChannel():
		}
	}
}

package state

import (
	"context"
	"os"
	"path"
	"sync"
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/carbonrobotics/robot/golang/lib/chip_manager"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

const (
	tempChipCacheDir = "./.tmp/"
)

func TestGetLocalChips(t *testing.T) {
	w := &ChipManagerWatcher{
		chipManagerState:    NewChipManagerState(),
		chipCacheDir:        tempChipCacheDir,
		stopCtx:             context.Background(),
		chipCacheWriteMutex: &sync.Mutex{},
	}

	err := ChipManagerFs.MkdirAll(w.chipCacheDir, os.ModePerm)
	assert.NoError(t, err)

	chips := w.GetLocalChips()
	assert.Equal(t, 0, len(chips))

	// Add a chip and write metadata
	chipMetadata := &chip_manager.Chip{
		ChipDefinition: veselka.ChipDefinition{
			Id:           "test_chip",
			PreSignedURL: "http://carbonrobotics.com",
			Checksum:     "123",
		},
		DownloadedTimestamp: 0,
		LastUsedTimestamp:   0,
	}

	err = chip_manager.WriteChipMetadata(w.chipCacheDir, chipMetadata, w.chipCacheWriteMutex)
	assert.NoError(t, err)

	chipMetaFilePath := path.Join(w.chipCacheDir, chipMetadata.MetaFilename())
	chip, err := chip_manager.GetVerifyMetadata(chipMetaFilePath)
	assert.NoError(t, err)
	assert.Equal(t, chipMetadata.Id, chip.Id)
	assert.Equal(t, chipMetadata.PreSignedURL, chip.PreSignedURL)
	assert.Equal(t, chipMetadata.Checksum, chip.Checksum)

	chips = w.GetLocalChips()
	assert.Equal(t, 1, len(chips))

	err = ChipManagerFs.Remove(chipMetaFilePath)
	assert.NoError(t, err)
	err = ChipManagerFs.Remove(w.chipCacheDir)
	assert.NoError(t, err)
}

func TestDownloadChip(t *testing.T) {
	// To run, comment out the line below and update the URL and Checksum in the chipDefinition
	t.SkipNow()

	w := &ChipManagerWatcher{
		chipManagerState:    NewChipManagerState(),
		chipCacheDir:        tempChipCacheDir,
		stopCtx:             context.Background(),
		chipCacheWriteMutex: &sync.Mutex{},
		logger:              logrus.New().WithField("module", "ChipManagerWatcher"),
	}

	err := ChipManagerFs.MkdirAll(w.chipCacheDir, os.ModePerm)
	assert.NoError(t, err)

	// Add a chip and write metadata
	chipMetadata := &chip_manager.Chip{
		ChipDefinition: veselka.ChipDefinition{
			Id:           "test_chip",
			PreSignedURL: "replace_with_presignedurl",
			Checksum:     "replace_with_checksum",
		},
		DownloadedTimestamp: 0,
		LastUsedTimestamp:   0,
	}

	err = chip_manager.WriteChipMetadata(w.chipCacheDir, chipMetadata, w.chipCacheWriteMutex)
	assert.NoError(t, err)
	assert.Equal(t, int64(0), chipMetadata.ContentLength)
	assert.Equal(t, uint64(0), chipMetadata.DownloadedTimestamp)

	err = w.downloadChip(w.stopCtx, chipMetadata)
	assert.NoError(t, err)

	chipMetaFilePath := path.Join(w.chipCacheDir, chipMetadata.MetaFilename())
	chip, err := chip_manager.GetVerifyMetadata(chipMetaFilePath)
	assert.NoError(t, err)
	assert.Equal(t, chipMetadata.Id, chip.Id)
	assert.Equal(t, chipMetadata.PreSignedURL, chip.PreSignedURL)
	assert.Equal(t, chipMetadata.Checksum, chip.Checksum)
	assert.NotEqual(t, 0, chip.ContentLength)
	assert.NotEqual(t, 0, chip.DownloadedTimestamp)

	chipFilePath := path.Join(w.chipCacheDir, chip.Filename())
	err = ChipManagerFs.Remove(chipFilePath)
	assert.NoError(t, err)
	err = ChipManagerFs.Remove(chipMetaFilePath)
	assert.NoError(t, err)
	err = ChipManagerFs.Remove(w.chipCacheDir)
	assert.NoError(t, err)
}

func TestDetectMissingLocalChips(t *testing.T) {
	w := &ChipManagerWatcher{
		chipManagerState:    NewChipManagerState(),
		chipCacheDir:        tempChipCacheDir,
		stopCtx:             context.Background(),
		chipCacheWriteMutex: &sync.Mutex{},
		logger:              logrus.New().WithField("module", "ChipManagerWatcher"),
	}

	tests := []struct {
		name                string
		localChips          map[string]*chip_manager.Chip
		categoryCollections map[string]*category.CategoryCollection
		categories          map[string]*category.Category
		expected            []string
	}{
		{
			name: "no missing chips",
			localChips: map[string]*chip_manager.Chip{
				"test_chip": {
					ChipDefinition: veselka.ChipDefinition{
						Id:           "test_chip",
						PreSignedURL: "http://carbonrobotics.com",
						Checksum:     "123",
					},
					DownloadedTimestamp: 0,
					LastUsedTimestamp:   0,
				},
			},
			categoryCollections: map[string]*category.CategoryCollection{
				"test_collection": {
					Id:          "test_collection",
					CategoryIds: []string{"test_category"},
				},
			},
			categories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip"},
				},
			},
			expected: []string{},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			w.chipManagerState.LocalChips = test.localChips

			missingChips := w.detectMissingLocalChips(test.categoryCollections, test.categories)
			assert.Equal(t, test.expected, missingChips)
		})
	}
}

func TestChangesToActiveCategoryCollection(t *testing.T) {
	w := &ChipManagerWatcher{
		chipManagerState:    NewChipManagerState(),
		chipCacheDir:        tempChipCacheDir,
		stopCtx:             context.Background(),
		chipCacheWriteMutex: &sync.Mutex{},
		categoryCollectionState: &CategoryCollectionCfgState{
			categoryCollections: []*category.CategoryCollection{
				{
					Id:          "test_collection",
					CategoryIds: []string{"test_category"},
				},
			},
			profileUpdateState: ProfileUpdateState{UnsyncedUpdates: false, UnsyncedUpdatesToActive: false},
			cfgState: cfgState{
				ActiveId: "test_collection",
			},
		},

		logger: logrus.New().WithField("module", "ChipManagerWatcher"),
	}

	tests := []struct {
		name                       string
		expectedCategoryCollection *category.CategoryCollection
		expectedCategories         map[string]*category.Category
		syncedCategoryCollections  map[string]*category.CategoryCollection
		syncedCategories           map[string]*category.Category
		expectedErr                bool
		expectedChanges            bool
	}{
		{
			name:                       "nil expectedCategoryCollection",
			expectedCategoryCollection: nil,
			expectedCategories:         map[string]*category.Category{},
			syncedCategoryCollections:  map[string]*category.CategoryCollection{},
			syncedCategories:           map[string]*category.Category{},
			expectedChanges:            true,
		},
		{
			name:                       "nil expectedCategories",
			expectedCategoryCollection: &category.CategoryCollection{},
			expectedCategories:         nil,
			syncedCategoryCollections:  map[string]*category.CategoryCollection{},
			syncedCategories:           map[string]*category.Category{},
			expectedChanges:            true,
		},
		{
			name: "differences between active and expected category collection",
			expectedCategoryCollection: &category.CategoryCollection{
				Id: "test_collection1",
			},
			expectedCategories: map[string]*category.Category{},
			syncedCategoryCollections: map[string]*category.CategoryCollection{
				"test_collection": {
					Id:          "test_collection2",
					CategoryIds: []string{"test_category"},
				},
			},
			syncedCategories: map[string]*category.Category{},
			expectedChanges:  true,
		},
		{
			name: "category not in syncedCategories",
			expectedCategoryCollection: &category.CategoryCollection{
				Id:          "test_collection",
				CategoryIds: []string{"test_category"},
			},
			expectedCategories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip"},
				},
			},
			syncedCategoryCollections: map[string]*category.CategoryCollection{
				"test_collection": {
					Id:          "test_collection",
					CategoryIds: []string{"test_category"},
				},
			},
			syncedCategories: map[string]*category.Category{},
			expectedChanges:  true,
		},
		{
			name: "category not in expectedCategories",
			expectedCategoryCollection: &category.CategoryCollection{
				Id:          "test_collection",
				CategoryIds: []string{"test_category"},
			},
			expectedCategories: map[string]*category.Category{},
			syncedCategoryCollections: map[string]*category.CategoryCollection{
				"test_collection": {
					Id:          "test_collection",
					CategoryIds: []string{"test_category"},
				},
			},
			syncedCategories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip"},
				},
			},
			expectedChanges: true,
		},
		{
			name: "mismatched category chips",
			expectedCategoryCollection: &category.CategoryCollection{
				Id:          "test_collection",
				CategoryIds: []string{"test_category"},
			},
			expectedCategories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip1"},
				},
			},
			syncedCategoryCollections: map[string]*category.CategoryCollection{
				"test_collection": {
					Id:          "test_collection",
					CategoryIds: []string{"test_category"},
				},
			},
			syncedCategories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip2"},
				},
			},
			expectedChanges: true,
		},
		{
			name: "no changes",
			expectedCategoryCollection: &category.CategoryCollection{
				Id:          "test_collection",
				CategoryIds: []string{"test_category"},
			},
			expectedCategories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip"},
				},
			},
			syncedCategoryCollections: map[string]*category.CategoryCollection{
				"test_collection": {
					Id:          "test_collection",
					CategoryIds: []string{"test_category"},
				},
			},
			syncedCategories: map[string]*category.Category{
				"test_category": {
					Id:      "test_category",
					ChipIds: []string{"test_chip"},
				},
			},
			expectedChanges: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			w.chipManagerState.WriteOnCurrent(func() {
				w.chipManagerState.activeCategoryCollection = test.expectedCategoryCollection
				w.chipManagerState.activeCategories = test.expectedCategories
			})
			changes := w.changesToActiveCategoryCollection(test.syncedCategoryCollections, test.syncedCategories)
			assert.Equal(t, test.expectedChanges, changes)
		})
	}
}

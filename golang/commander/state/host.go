package state

import (
	"fmt"
	"sync"
	"sync/atomic"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/host_check"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/carbonrobotics/robot/golang/lib/translation"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

var (
	hostRespondingGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem:   subsystem,
			Name:        "host_responding",
			Help:        "boolean denoting if host is responding.",
			ConstLabels: nil,
		}, []string{"hostname"})
)

type HostAlarm struct {
	Identifier            string
	AlarmCode             alarms.AlarmCode
	Description           string
	Level                 alarms.AlarmLevel
	Impact                alarms.AlarmImpact
	RowHost               int
	Param                 int32
	TranslationParameters []*frontend.TranslationParameter
	ModuleId              *uint32
}

func Host2HostAlarm(inalarm *host_check.Alarm, rowhost int, moduleId *uint32) *HostAlarm {
	code := alarms.Host2Code(inalarm.Code)
	return &HostAlarm{
		Identifier:            fmt.Sprintf("%d.%s.%d", rowhost, code.String(), inalarm.Param),
		AlarmCode:             code,
		Description:           inalarm.Description,
		Level:                 alarms.Host2Level(inalarm.Level),
		Impact:                alarms.Host2Impact(inalarm.Impact),
		Param:                 inalarm.Param,
		RowHost:               rowhost,
		TranslationParameters: inalarm.TranslationParameters,
		ModuleId:              moduleId,
	}
}

type HostInfo struct {
	Alarms []*HostAlarm
}

type HostState struct {
	ManagedStateImpl
	num_hosts int
	hosts     []*HostInfo
	servers   map[string]string
}

func NewHostState(count int) *HostState {
	state := &HostState{ManagedStateImpl: ManagedStateImpl{name: "HostState"},
		num_hosts: count,
		hosts:     make([]*HostInfo, count),
		servers:   make(map[string]string),
	}
	for i := 0; i < count; i++ {
		state.hosts[i] = &HostInfo{}
	}
	state.initialize()
	return state
}

type HostWatcher struct {
	EventTrigger
	state          *HostState
	hosts          []*hosts.HostClients
	implementState *ImplementState
	has_alarms     bool
	pingCheck      *PingCheckState
	logger         *logrus.Entry
}

func NewHostWatcher(state *HostState, hosts []*hosts.HostClients, implementState *ImplementState, pingCheck *PingCheckState) *HostWatcher {
	action := &HostWatcher{
		state:          state,
		hosts:          hosts,
		has_alarms:     false,
		implementState: implementState,
		pingCheck:      pingCheck,
		logger:         logrus.WithField("module", "HostWatcher"),
	}
	action.triggerChannel = make(chan bool)
	return action
}

type HostUpdate struct {
	alarms   []*HostAlarm
	hostname string
	serial   string
}

func createHostAlarm(hostIndex int, hostname string, description string, alarmNumber uint32, moduleId *uint32) *HostAlarm {
	code := alarms.AlarmCode{
		Subsystem: alarms.Computer,
		Number:    alarmNumber,
	}
	return &HostAlarm{
		Identifier:            fmt.Sprintf("%d.%s.%d", hostIndex, code.String(), int32(hostIndex)),
		AlarmCode:             code,
		Description:           description,
		Level:                 alarms.AlarmLevelHigh,
		Impact:                alarms.AlarmImpactDegraded,
		RowHost:               hostIndex,
		Param:                 int32(hostIndex),
		TranslationParameters: translation.Params(translation.StringParam("hostname", hostname)),
		ModuleId:              moduleId,
	}
}

func (w *HostWatcher) Action() {
	var wg sync.WaitGroup
	var has_alarms atomic.Bool
	has_alarms.Store(false)

	/* Capture all the status updates in parallel */
	updates := make([]*HostUpdate, len(w.hosts))
	for i := 0; i < len(w.hosts); i++ {
		updates[i] = &HostUpdate{alarms: make([]*HostAlarm, 0)}
	}

	for host_index, r := range w.hosts {
		wg.Add(1)
		go func(host_index int, hu *HostUpdate, host *hosts.HostClients) {
			defer wg.Done()
			hostRespondingGauge.WithLabelValues(host.HostClient.Hostname).Set(0)
			status, err := host.HostClient.GetHostState()
			if err != nil {
				err = w.pingCheck.PingHost(host)
				if err != nil {
					w.logger.WithError(err).Errorf("Failed to ping host %v", host.HostClient.Hostname)
					descr := fmt.Sprintf("%s computer unreachable", host.HostClient.Hostname)
					hu.alarms = append(hu.alarms, createHostAlarm(host_index, host.HostClient.Hostname, descr, 4, &host.PcId))
				} else {
					descr := fmt.Sprintf("%s computer software failed to load", host.HostClient.Hostname)
					hu.alarms = append(hu.alarms, createHostAlarm(host_index, host.HostClient.Hostname, descr, 13, &host.PcId))
				}
				has_alarms.Store(true)
			} else {
				hostRespondingGauge.WithLabelValues(host.HostClient.Hostname).Set(1)
				for _, alarm := range status.Alarms {
					al := Host2HostAlarm(alarm, host_index, &host.PcId)
					hu.alarms = append(hu.alarms, al)
					has_alarms.Store(true)
				}
				hu.hostname = status.GetSystemInfo().GetHostname()
				hu.serial = status.GetSystemInfo().GetSerial()
			}
		}(host_index, updates[host_index], r)
	}
	wg.Wait()

	w.state.ConditionalWriteOnCurrent(func() bool {
		changed := false
		for host_index, _ := range w.state.hosts {
			if updates[host_index].hostname == "" {
				continue
			}
			val, ok := w.state.servers[updates[host_index].hostname]
			if !ok || val != updates[host_index].serial {
				changed = true
				w.state.servers[updates[host_index].hostname] = updates[host_index].serial
			}
		}
		return changed
	})
	/* If we a) have alarms, or b) are transitioning from some to none, then write them all in one shot */
	if has_alarms.Load() || (!has_alarms.Load() && w.has_alarms) {
		w.state.WriteOnCurrent(func() {
			for host_index, host := range w.state.hosts {
				host.Alarms = make([]*HostAlarm, 0)
				for _, a := range updates[host_index].alarms {
					host.Alarms = append(host.Alarms, a)
				}
			}
		})
	}

	w.has_alarms = has_alarms.Load()
}

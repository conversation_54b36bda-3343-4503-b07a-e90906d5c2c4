package state

import (
	"strconv"

	pb "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	connectionStatusGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "connection_strength_db",
		Help:      "Connection strength in decibels.",
	}, []string{"connected"})
	powerOnTimeSecGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "total_power_on_time_sec",
		Help:      "amount of time the machine was powered in sec",
	})
)

const (
	scannerId_A = "scanner_a"
	scannerId_B = "scanner_b"
	predictId   = "predict"
	targetId_A  = "target_a"
	targetId_B  = "target_b"
)

// Reaper module sensor gauges
var (
	// Gauge to map module IDs to serial numbers
	moduleIdToSerialGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_id_to_serial",
		Help:      "Mapping of module IDs to serial numbers",
	}, []string{"module_id", "serial"})

	// Environmental sensors - enclosure
	moduleEnviroEnclosureTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_enviro_enclosure_temp_c",
		Help:      "Module enclosure temperature in celsius",
	}, []string{"module_id"})
	moduleEnviroEnclosureHumidityGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_enviro_enclosure_humidity",
		Help:      "Module enclosure humidity",
	}, []string{"module_id"})
	moduleEnviroEnclosurePressureGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_enviro_enclosure_pressure_hpa",
		Help:      "Module enclosure pressure in hPa",
	}, []string{"module_id"})

	// Camera temperature gauges
	moduleCameraTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_camera_temp_c",
		Help:      "Module camera temperature in celsius",
	}, []string{"module_id", "camera_id"})

	// Environmental sensors - PC
	moduleEnviroPcTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_enviro_pc_temp_c",
		Help:      "Module PC temperature in celsius",
	}, []string{"module_id"})
	moduleEnviroPcHumidityGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_enviro_pc_humidity",
		Help:      "Module PC humidity",
	}, []string{"module_id"})
	moduleEnviroPcPressureGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_enviro_pc_pressure_hpa",
		Help:      "Module PC pressure in hPa",
	}, []string{"module_id"})

	// Coolant inlet sensors
	moduleCoolantInletTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_coolant_inlet_temp_c",
		Help:      "Module coolant inlet temperature in celsius",
	}, []string{"module_id"})
	moduleCoolantInletPressureGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_coolant_inlet_pressure_kpa",
		Help:      "Module coolant inlet pressure in kPa",
	}, []string{"module_id"})

	// Coolant outlet sensors
	moduleCoolantOutletTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_coolant_outlet_temp_c",
		Help:      "Module coolant outlet temperature in celsius",
	}, []string{"module_id"})
	moduleCoolantOutletPressureGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_coolant_outlet_pressure_kpa",
		Help:      "Module coolant outlet pressure in kPa",
	}, []string{"module_id"})

	// Strobe sensors
	moduleStrobeTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_strobe_temp_c",
		Help:      "Module strobe temperature in celsius",
	}, []string{"module_id"})
	moduleStrobeCapVoltageGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_strobe_cap_voltage",
		Help:      "Module strobe capacitor voltage",
	}, []string{"module_id"})
	moduleStrobeCurrentGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_strobe_current",
		Help:      "Module strobe current",
	}, []string{"module_id"})

	// PC sensors
	modulePcCpuTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_cpu_temp_c",
		Help:      "Module PC CPU temperature in celsius",
	}, []string{"module_id"})
	modulePcSystemTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_system_temp_c",
		Help:      "Module PC system temperature in celsius",
	}, []string{"module_id"})
	modulePcGpu1TempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_gpu1_temp_c",
		Help:      "Module PC GPU 1 temperature in celsius",
	}, []string{"module_id"})
	modulePcGpu2TempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_gpu2_temp_c",
		Help:      "Module PC GPU 2 temperature in celsius",
	}, []string{"module_id"})
	modulePc12vGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_12v",
		Help:      "Module PC 12V supply voltage",
	}, []string{"module_id"})
	modulePc5vGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_5v",
		Help:      "Module PC 5V supply voltage",
	}, []string{"module_id"})
	modulePc3v3Gauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_3v3",
		Help:      "Module PC 3.3V supply voltage",
	}, []string{"module_id"})
	modulePcLoadGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_load",
		Help:      "Module PC load average",
	}, []string{"module_id"})
	modulePcUptimeGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_uptime_sec",
		Help:      "Module PC uptime in seconds",
	}, []string{"module_id"})
	modulePcRamUsageGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_ram_usage_percent",
		Help:      "Module PC RAM usage percentage",
	}, []string{"module_id"})
	modulePcDiskUsageGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_disk_usage_percent",
		Help:      "Module PC disk usage percentage",
	}, []string{"module_id"})

	// Scanner sensors - combined A and B with scanner_id label
	moduleScannerCurrentGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_current_a",
		Help:      "Module scanner current in amps",
	}, []string{"module_id", "scanner_id"})
	moduleScannerFuseTrippedGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_fuse_tripped",
		Help:      "Module scanner fuse tripped status",
	}, []string{"module_id", "scanner_id"})
	moduleScannerCollimatorTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_collimator_temp_c",
		Help:      "Module scanner collimator temperature in celsius",
	}, []string{"module_id", "scanner_id"})
	moduleScannerFiberTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_fiber_temp_c",
		Help:      "Module scanner fiber temperature in celsius",
	}, []string{"module_id", "scanner_id"})
	moduleScannerLaserPowerGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_laser_power_w",
		Help:      "Module scanner laser power in watts",
	}, []string{"module_id", "scanner_id"})
	moduleScannerLaserPowerRawGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_laser_power_raw_mv",
		Help:      "Module scanner raw laser power reading in millivolts",
	}, []string{"module_id", "scanner_id"})
	moduleScannerLaserConnectedGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_laser_connected",
		Help:      "Module scanner laser connected status",
	}, []string{"module_id", "scanner_id"})
	moduleScannerTargetConnectedGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_target_connected",
		Help:      "Module scanner target connected status",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorPanTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_pan_temp_c",
		Help:      "Module scanner pan motor temperature in celsius",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorPanVoltageGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_pan_voltage_v",
		Help:      "Module scanner pan motor voltage",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorPanCurrentGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_pan_current_a",
		Help:      "Module scanner pan motor current in amps",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorPanEncoderGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_pan_encoder",
		Help:      "Module scanner pan motor encoder position",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorTiltTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_tilt_temp_c",
		Help:      "Module scanner tilt motor temperature in celsius",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorTiltVoltageGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_tilt_voltage_v",
		Help:      "Module scanner tilt motor voltage",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorTiltCurrentGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_tilt_current_a",
		Help:      "Module scanner tilt motor current in amps",
	}, []string{"module_id", "scanner_id"})
	moduleScannerMotorTiltEncoderGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_scanner_motor_tilt_encoder",
		Help:      "Module scanner tilt motor encoder position",
	}, []string{"module_id", "scanner_id"})

	// Power status
	modulePcPowerEnabledGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_pc_power_enabled",
		Help:      "Module PC power enabled status",
	}, []string{"module_id"})
	moduleLasersPowerEnabledGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_lasers_power_enabled",
		Help:      "Module lasers power enabled status",
	}, []string{"module_id"})
	modulePredictCamPowerEnabledGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_predict_cam_power_enabled",
		Help:      "Module predict camera power enabled status",
	}, []string{"module_id"})
	moduleStrobePowerEnabledGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_strobe_power_enabled",
		Help:      "Module strobe power enabled status",
	}, []string{"module_id"})
	moduleStrobeEnabledGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "module_strobe_enabled",
		Help:      "Module strobe enabled status",
	}, []string{"module_id"})
)

var (
	// saftey metrics
	safteyLiftedGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "lifted",
		Help:      "boolean of lifted status",
	})
	safteyEStoppedGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "estopped",
		Help:      "boolean of estopped status",
	})
	safteyInCabEStoppedGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "estopped_in_cab",
		Help:      "boolean of in cab estopped status",
	})
	safteyLeftEStoppedGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "estopped_left",
		Help:      "boolean of left estopped status",
	})
	safteyRightEStoppedGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "estopped_right",
		Help:      "boolean of right estopped status",
	})
	safteyLaserKeyGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "laser_key",
		Help:      "boolean of laser key status",
	})
	safteyInterlockGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "interlock",
		Help:      "boolean of interlock status",
	})
	safteyWaterProtectGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "water_protect",
		Help:      "boolean of water protect status",
	})
)

var (
	// supervisory metrics
	mainContactorStatusBoolGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "main_connector_status",
		Help:      "boolean of main connector status",
	})
	powerGoodGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "power_good",
		Help:      "boolean of power good status",
	})
	powerBadGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "power_bad",
		Help:      "boolean of power bad status",
	})
	powerVeryBadGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "power_very_bad",
		Help:      "boolean of power very bad status",
	})
	tempHumidityStatusGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "temp_humidity_status",
		Help:      "boolean of temperature humidity status",
	})
	tractorPowerGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "tractor_power",
		Help:      "boolean of tractor power status",
	})
	acFrequencyGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_freq_hz",
		Help:      "ac frequency in hertz",
	})
	acVoltageABGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_voltage_ab",
		Help:      "ac ab voltage",
	})
	acVoltageBCGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_voltage_bc",
		Help:      "ac bc voltage",
	})
	acVoltageACGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_voltage_ac",
		Help:      "ac ac voltage",
	})
	acVoltageAGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_voltage_a",
		Help:      "ac a voltage",
	})
	acVoltageBGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_voltage_b",
		Help:      "ac b voltage",
	})
	acVoltageCGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ac_voltage_c",
		Help:      "ac c voltage",
	})
	phasePowerW3Gauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "phase_power_watts",
		Help:      "phase power in watts",
	})
	phasePowerVa3Gauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "phase_power_volt_amps",
		Help:      "phase power in volt amps",
	})
	powerFactorGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "power_factor",
		Help:      "power factor",
	})
	serverCabTempGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "server_cabinet_temperature_c",
		Help:      "server cabinet temperature in celsius",
	})
	serverCabHumidityGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "server_cabinet_humidity",
		Help:      "server cabinet humidity",
	})
	batteryVoltage12VGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "battery_volts",
		Help:      "battery level in volts",
	})
	tempBypassGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "temp_bypass",
		Help:      "boolean of temperature bypass status",
	})
	humidityBypassGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "humidity_bypass",
		Help:      "boolean of humidity bypass status",
	})
	tempStatusGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "temp_status",
		Help:      "boolean of temperature status",
	})
	humidityStatusGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "humidity_status",
		Help:      "boolean of humidity status",
	})
	chillerTempGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "chiller_temp",
		Help:      "chiller temperature",
	})
	chillerFlowGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "chiller_flow",
		Help:      "chiller flow",
	})
	chillerPressureGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "chiller_pressure",
		Help:      "chiller pressure",
	})
	chillerConductivityGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "chiller_conductivity",
		Help:      "chiller conductivity",
	})
	chillerSetTempGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "chiller_set_temp",
		Help:      "chiller set temperature",
	})
)

func (s SupervisoryState) updateMetrics() {
	mainContactorStatusBoolGauge.Set(metrics.BoolToFloat(s.MainContactorStatusFb))
	powerGoodGauge.Set(metrics.BoolToFloat(s.PowerGood))
	powerBadGauge.Set(metrics.BoolToFloat(s.PowerBad))
	powerVeryBadGauge.Set(metrics.BoolToFloat(s.PowerVeryBad))
	tempHumidityStatusGauge.Set(metrics.BoolToFloat(s.TempHumidityStatus))
	tractorPowerGauge.Set(metrics.BoolToFloat(s.TractorPower))
	acFrequencyGauge.Set(s.AcFrequency)
	acVoltageABGauge.Set(s.AcVoltageAB)
	acVoltageBCGauge.Set(s.AcVoltageBC)
	acVoltageACGauge.Set(s.AcVoltageAC)
	acVoltageAGauge.Set(s.AcVoltageA)
	acVoltageBGauge.Set(s.AcVoltageB)
	acVoltageCGauge.Set(s.AcVoltageC)
	phasePowerW3Gauge.Set(float64(s.PhasePowerW_3))
	phasePowerVa3Gauge.Set(float64(s.PhasePowerVa_3))
	powerFactorGauge.Set(s.PowerFactor)
	serverCabTempGauge.Set(s.ServerCabinetTemp)
	serverCabHumidityGauge.Set(s.ServerCabinetHumidity)
	batteryVoltage12VGauge.Set(s.BatteryVoltage_12V)
	tempBypassGauge.Set(metrics.BoolToFloat(s.TempBypassStatus))
	humidityBypassGauge.Set(metrics.BoolToFloat(s.HumidityBypassStatus))
	tempStatusGauge.Set(metrics.BoolToFloat(s.TempStatus))
	humidityStatusGauge.Set(metrics.BoolToFloat(s.HumidityStatus))
	chillerTempGauge.Set(s.ChillerTemp)
	chillerFlowGauge.Set(s.ChillerFlow)
	chillerPressureGauge.Set(s.ChillerPressure)
	chillerConductivityGauge.Set(s.ChillerConductivity)
	chillerSetTempGauge.Set(s.ChillerSetTemp)
}

// updateReaperModuleMetrics updates Prometheus gauges with the values from a ReaperModuleSensorData struct
func updateReaperModuleMetrics(moduleState *pb.ReaperModuleSensorData) {
	if moduleState == nil {
		return
	}

	// Convert module ID to string for labels
	moduleId := strconv.Itoa(int(moduleState.ModuleId))
	moduleSn := ""
	if moduleState.ModuleSn != nil {
		moduleSn = *moduleState.ModuleSn
	}

	// Set the ID to serial mapping gauge
	if moduleSn != "" {
		moduleIdToSerialGauge.WithLabelValues(moduleId, moduleSn).Set(1)
	}

	// Environmental sensors - enclosure
	if moduleState.EnviroEnclosure != nil {
		moduleEnviroEnclosureTempGauge.WithLabelValues(moduleId).Set(moduleState.EnviroEnclosure.TemperatureC)
		moduleEnviroEnclosureHumidityGauge.WithLabelValues(moduleId).Set(moduleState.EnviroEnclosure.HumidityRh)
		moduleEnviroEnclosurePressureGauge.WithLabelValues(moduleId).Set(moduleState.EnviroEnclosure.PressureHpa)
	}

	// Environmental sensors - PC
	if moduleState.EnviroPc != nil {
		moduleEnviroPcTempGauge.WithLabelValues(moduleId).Set(moduleState.EnviroPc.TemperatureC)
		moduleEnviroPcHumidityGauge.WithLabelValues(moduleId).Set(moduleState.EnviroPc.HumidityRh)
		moduleEnviroPcPressureGauge.WithLabelValues(moduleId).Set(moduleState.EnviroPc.PressureHpa)
	}

	// Coolant inlet sensors
	if moduleState.CoolantInlet != nil {
		moduleCoolantInletTempGauge.WithLabelValues(moduleId).Set(moduleState.CoolantInlet.TemperatureC)
		moduleCoolantInletPressureGauge.WithLabelValues(moduleId).Set(moduleState.CoolantInlet.PressureKpa)
	}

	// Coolant outlet sensors
	if moduleState.CoolantOutlet != nil {
		moduleCoolantOutletTempGauge.WithLabelValues(moduleId).Set(moduleState.CoolantOutlet.TemperatureC)
		moduleCoolantOutletPressureGauge.WithLabelValues(moduleId).Set(moduleState.CoolantOutlet.PressureKpa)
	}

	// Strobe sensors
	moduleStrobeTempGauge.WithLabelValues(moduleId).Set(moduleState.StrobeTemperatureC)
	moduleStrobeCapVoltageGauge.WithLabelValues(moduleId).Set(moduleState.StrobeCapVoltage)
	moduleStrobeCurrentGauge.WithLabelValues(moduleId).Set(moduleState.StrobeCurrent)

	// PC sensors
	if moduleState.Pc != nil {
		modulePcCpuTempGauge.WithLabelValues(moduleId).Set(moduleState.Pc.TemperatureCpuCoreC)
		modulePcSystemTempGauge.WithLabelValues(moduleId).Set(moduleState.Pc.TemperatureSystemC)

		if moduleState.Pc.TemperatureGpu_1C != nil {
			modulePcGpu1TempGauge.WithLabelValues(moduleId).Set(*moduleState.Pc.TemperatureGpu_1C)
		}

		if moduleState.Pc.TemperatureGpu_2C != nil {
			modulePcGpu2TempGauge.WithLabelValues(moduleId).Set(*moduleState.Pc.TemperatureGpu_2C)
		}

		modulePc12vGauge.WithLabelValues(moduleId).Set(moduleState.Pc.Psu_12V)
		modulePc5vGauge.WithLabelValues(moduleId).Set(moduleState.Pc.Psu_5V)
		modulePc3v3Gauge.WithLabelValues(moduleId).Set(moduleState.Pc.Psu_3V3)
		modulePcLoadGauge.WithLabelValues(moduleId).Set(moduleState.Pc.Load)
		modulePcUptimeGauge.WithLabelValues(moduleId).Set(float64(moduleState.Pc.Uptime))
		modulePcRamUsageGauge.WithLabelValues(moduleId).Set(moduleState.Pc.RamUsagePercent)
		modulePcDiskUsageGauge.WithLabelValues(moduleId).Set(moduleState.Pc.DiskUsagePercent)
	}

	if moduleState.ScannerA != nil {
		updateScannerMetrics(moduleId, scannerId_A, moduleState.ScannerA)
	}

	if moduleState.ScannerB != nil {
		updateScannerMetrics(moduleId, scannerId_B, moduleState.ScannerB)
	}

	// Power status
	modulePcPowerEnabledGauge.WithLabelValues(moduleId).Set(metrics.BoolToFloat(moduleState.PcPowerEnabled))
	moduleLasersPowerEnabledGauge.WithLabelValues(moduleId).Set(metrics.BoolToFloat(moduleState.LasersPowerEnabled))
	modulePredictCamPowerEnabledGauge.WithLabelValues(moduleId).Set(metrics.BoolToFloat(moduleState.PredictCamPowerEnabled))
	moduleStrobePowerEnabledGauge.WithLabelValues(moduleId).Set(metrics.BoolToFloat(moduleState.StrobePowerEnabled))
	moduleStrobeEnabledGauge.WithLabelValues(moduleId).Set(metrics.BoolToFloat(moduleState.StrobeEnabled))
}

// updateScannerMetrics updates the metrics for a single scanner (A or B)
func updateScannerMetrics(moduleId, scannerId string, scanner *pb.ReaperScannerSensorData) {
	if scanner == nil {
		return
	}

	moduleScannerCurrentGauge.WithLabelValues(moduleId, scannerId).Set(scanner.CurrentA)
	moduleScannerFuseTrippedGauge.WithLabelValues(moduleId, scannerId).Set(metrics.BoolToFloat(scanner.FuseTripped))
	moduleScannerCollimatorTempGauge.WithLabelValues(moduleId, scannerId).Set(scanner.TemperatureCollimatorC)
	moduleScannerFiberTempGauge.WithLabelValues(moduleId, scannerId).Set(scanner.TemperatureFiberC)
	moduleScannerLaserPowerGauge.WithLabelValues(moduleId, scannerId).Set(scanner.LaserPowerW)
	moduleScannerLaserPowerRawGauge.WithLabelValues(moduleId, scannerId).Set(scanner.LaserPowerRawMv)
	moduleScannerLaserConnectedGauge.WithLabelValues(moduleId, scannerId).Set(metrics.BoolToFloat(scanner.LaserConnected))
	moduleScannerTargetConnectedGauge.WithLabelValues(moduleId, scannerId).Set(metrics.BoolToFloat(scanner.TargetConnected))

	// Pan motor
	if scanner.MotorPan != nil {
		moduleScannerMotorPanTempGauge.WithLabelValues(moduleId, scannerId).Set(scanner.MotorPan.TemperatureOutputC)
		moduleScannerMotorPanVoltageGauge.WithLabelValues(moduleId, scannerId).Set(scanner.MotorPan.MotorSupplyV)
		moduleScannerMotorPanCurrentGauge.WithLabelValues(moduleId, scannerId).Set(scanner.MotorPan.MotorCurrentA)
		moduleScannerMotorPanEncoderGauge.WithLabelValues(moduleId, scannerId).Set(float64(scanner.MotorPan.EncoderPosition))
	}

	// Tilt motor
	if scanner.MotorTilt != nil {
		moduleScannerMotorTiltTempGauge.WithLabelValues(moduleId, scannerId).Set(scanner.MotorTilt.TemperatureOutputC)
		moduleScannerMotorTiltVoltageGauge.WithLabelValues(moduleId, scannerId).Set(scanner.MotorTilt.MotorSupplyV)
		moduleScannerMotorTiltCurrentGauge.WithLabelValues(moduleId, scannerId).Set(scanner.MotorTilt.MotorCurrentA)
		moduleScannerMotorTiltEncoderGauge.WithLabelValues(moduleId, scannerId).Set(float64(scanner.MotorTilt.EncoderPosition))
	}
}

// updateCameraTemperatureMetrics updates Prometheus gauges with camera temperature values
func updateCameraTemperatureMetrics(cameraTemps map[uint32]ModuleCameraTemperatures) {
	for id, temps := range cameraTemps {
		// Convert module ID to string for labels
		moduleId := strconv.Itoa(int(id))

		// Update predict camera temperature gauge
		if temps.Predict != 0 {
			moduleCameraTempGauge.WithLabelValues(moduleId, predictId).Set(temps.Predict)
		}

		// Update target A camera temperature gauge
		if temps.TargetA != 0 {
			moduleCameraTempGauge.WithLabelValues(moduleId, targetId_A).Set(temps.TargetA)
		}

		// Update target B camera temperature gauge
		if temps.TargetB != 0 {
			moduleCameraTempGauge.WithLabelValues(moduleId, targetId_B).Set(temps.TargetB)
		}
	}
}

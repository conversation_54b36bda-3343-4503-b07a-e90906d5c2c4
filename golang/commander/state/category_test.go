package state

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"github.com/stretchr/testify/assert"
)

func TestCategoryEqual(t *testing.T) {
	tests := []struct {
		name  string
		a     *category.Category
		b     *category.Category
		equal bool
	}{
		{
			name:  "empty lists",
			a:     &category.Category{},
			b:     &category.Category{},
			equal: true,
		},
		{
			name:  "different lengths",
			a:     &category.Category{Id: "1"},
			b:     &category.Category{},
			equal: false,
		},
		{
			name:  "different ids",
			a:     &category.Category{Id: "1"},
			b:     &category.Category{Id: "2"},
			equal: false,
		},
		{
			name:  "same ids",
			a:     &category.Category{Id: "1"},
			b:     &category.Category{Id: "1"},
			equal: true,
		},
		{
			name:  "same chip ids different order",
			a:     &category.Category{Id: "1", ChipIds: []string{"1", "2"}},
			b:     &category.Category{Id: "1", ChipIds: []string{"2", "1"}},
			equal: true,
		},
		{
			name:  "different chip ids",
			a:     &category.Category{Id: "1", ChipIds: []string{"1", "2"}},
			b:     &category.Category{Id: "1", ChipIds: []string{"2", "5"}},
			equal: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, test.equal, categoryEqual(test.a, test.b))
		})
	}
}

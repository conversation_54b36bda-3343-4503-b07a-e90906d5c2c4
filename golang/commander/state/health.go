package state

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	pb_metrics "github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/metrics_aggregator"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
)

const redis_health_queue_key = "health/queue"

var (
	isEStoppedGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "is_estopped",
		Help:      "boolean if the current state is estopped",
	})
	isLoadingGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "is_loading",
		Help:      "boolean if the current state is loading",
	})
	isReadyGauge = promauto.NewGauge(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "is_ready",
		Help:      "boolean if the current state is ready",
	})
)

type formatStatusArgs struct {
	TargetingState      *TargetingState
	ThinningEnabled     bool
	LaserPausedDueToVel bool
	TimerSeconds        int
}

func formatStatus(status frontend.Status, details string, args *formatStatusArgs) (string, *frontend.TranslatedStatusMessage) {
	var prefix string
	switch status {
	case frontend.Status_STATUS_ERROR:
		prefix = "Error"
	case frontend.Status_STATUS_ESTOPPED:
		prefix = "E-Stopped"
	case frontend.Status_STATUS_LIFTED:
		prefix = "Lifted"
	case frontend.Status_STATUS_POWERED_DOWN:
		prefix = "Powered Down"
	case frontend.Status_STATUS_POWERING_UP:
		prefix = "Powering Up"
	case frontend.Status_STATUS_UPDATE_INSTALLING:
		prefix = "Software Update Installing"
	case frontend.Status_STATUS_MODEL_LOADING:
		prefix = "Model Loading"
	case frontend.Status_STATUS_PRE_ARMED:
		prefix = "Not Armed"
	case frontend.Status_STATUS_WEEDING:
		if args.LaserPausedDueToVel {
			prefix = "Shooting paused while not moving."
		} else if args.TargetingState.WeedState.Enabled && args.TargetingState.ThinningState.Enabled {
			prefix = "Weeding and Thinning"
		} else if args.TargetingState.WeedState.Enabled {
			prefix = "Weeding"
		} else if args.TargetingState.ThinningState.Enabled {
			prefix = "Thinning"
		} else {
			prefix = "Weeding"
		}
	case frontend.Status_STATUS_STANDBY:
		if args.ThinningEnabled {
			prefix = "Ready to Weed/Thin"
		} else {
			prefix = "Ready to Weed"
		}
	case frontend.Status_STATUS_DISCONNECTED:
		prefix = "Disconnected"
	case frontend.Status_STATUS_LOADING:
		prefix = "Loading"
	case frontend.Status_STATUS_ALARM_AUTOFIX_IN_PROGRESS:
		prefix = "Alarm Autofix In Progress"
	case frontend.Status_STATUS_FAILED_TO_POWER_UP:
		prefix = "Software Failed to Load"
	case frontend.Status_STATUS_SERVER_CABINET_COOLDOWN:
		prefix = "Server Cabinet Adjusting Temperature"
	case frontend.Status_STATUS_CHILLER_COOLDOWN:
		prefix = "Chiller Adjusting Temperature"
	case frontend.Status_STATUS_TRACTOR_NOT_SAFE:
		prefix = "Outside of safe zone"
	default:
		prefix = "Unknown State"
	}
	tsm := &frontend.TranslatedStatusMessage{Prefix: prefix}
	if details == "" {
		return prefix, tsm
	} else {
		if args.TimerSeconds == -1 {
			tsm.Details = &frontend.TranslatedStatusMessageDetails{
				Details: &frontend.TranslatedStatusMessageDetails_DetailsStringKey{
					DetailsStringKey: details,
				},
			}
		} else {
			tsm.Details = &frontend.TranslatedStatusMessageDetails{
				Details: &frontend.TranslatedStatusMessageDetails_Timer{
					Timer: &frontend.DurationValue{
						Value: &frontend.DurationValue_Seconds{
							Seconds: uint64(args.TimerSeconds),
						},
					},
				},
			}
		}
		return fmt.Sprintf("%s (%s)", prefix, details), tsm
	}
}

type FeatureFlagsGetter struct {
	ffConfig        *config.ConfigTree
	ThinningEnabled atomic.Bool
	IsEU            atomic.Bool
	// add feature flags as needed
}

func NewFeatureFlagsGetter(commonConfig *config.ConfigTree) *FeatureFlagsGetter {
	ffg := &FeatureFlagsGetter{
		ffConfig: commonConfig.GetNode("feature_flags"),
	}
	ffg.ffConfig.RegisterCallback(ffg.callback)
	ffg.callback()
	return ffg
}

func (ffg *FeatureFlagsGetter) callback() {
	ffg.ThinningEnabled.Store(ffg.ffConfig.GetNode("thinning_feature").GetBoolValue())
	ffg.IsEU.Store(ffg.ffConfig.GetNode("eu_compliant_feature").GetBoolValue())
}

type StartUpTimer struct {
	status            frontend.Status
	operations        *OperationsState
	featureFlags      *FeatureFlagsGetter
	commanderConfig   *config.ConfigTree
	timerResetChannel chan bool
	timerStopChannel  chan bool
	timerExpired      *atomic.Bool
	tickedTime        int64
	maxTime           int64
	times             []int64
	timerStarted      bool
	mutex             sync.Mutex
}

func NewStartUpTimer(operations *OperationsState, featureFlags *FeatureFlagsGetter, commanderConfig *config.ConfigTree) *StartUpTimer {
	s := &StartUpTimer{
		operations:        operations,
		featureFlags:      featureFlags,
		commanderConfig:   commanderConfig,
		timerResetChannel: make(chan bool),
		timerStopChannel:  make(chan bool),
		tickedTime:        0,
		maxTime:           0,
		times:             make([]int64, 3),
		timerStarted:      false,
	}
	return s
}

func (s *StartUpTimer) Start(i int) {

	s.mutex.Lock()
	alreadyStarted := s.timerStarted
	s.mutex.Unlock()
	if alreadyStarted {
		return
	}
	if s.times[i] >= s.maxTime {
		(*s.timerExpired).Store(true)
		// then spins in the else case below
	}
	go func() {
		s.mutex.Lock()
		s.timerStarted = true
		s.mutex.Unlock()
		tick := time.NewTicker(time.Second)
		defer tick.Stop()
		for {
			select {
			case <-tick.C:
				s.mutex.Lock()
				time := s.tickedTime + s.times[i]
				if time < s.maxTime {
					if time == s.maxTime-1 {
						(*s.timerExpired).Store(true)
					}
					s.tickedTime++
				} else {
					s.mutex.Unlock()
					continue
				}
				powerUpTime := s.maxTime - (s.tickedTime + s.times[i])
				ts := s.operations.GetExpectedTargetingState()
				s.operations.WriteOnCurrent(func() {
					minutes := int(math.Abs(math.Floor(float64(powerUpTime / 60))))
					seconds := int(math.Abs(math.Mod(float64(powerUpTime), 60)))
					thinning_enabled := s.featureFlags.ThinningEnabled.Load()
					laserPausedDueToVel := false
					for _, safety_state := range s.operations.RowWeeding {
						if safety_state.RowSafetyState == 1 {
							laserPausedDueToVel = true
						}
					}
					s.operations.StatusMessage, s.operations.TranslatedStatusMessage =
						formatStatus(s.status, fmt.Sprintf("%d:%02d", minutes, seconds), &formatStatusArgs{
							TargetingState:      &ts,
							ThinningEnabled:     thinning_enabled,
							LaserPausedDueToVel: laserPausedDueToVel,
							TimerSeconds:        int(powerUpTime),
						})
				})
				s.mutex.Unlock()
			case <-s.timerResetChannel:
				s.mutex.Lock()
				s.timerStarted = false
				s.mutex.Unlock()
				s.timerStopChannel <- true
				return
			}
		}
	}()
}

func (s *StartUpTimer) Stop() {
	s.timerResetChannel <- true
	<-s.timerStopChannel
}

func (s *StartUpTimer) UpdateStatus(status frontend.Status, time string, timerExpired *atomic.Bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.status = status
	s.maxTime = int64(s.commanderConfig.GetNode(time).GetUIntValue())
	s.timerExpired = timerExpired
}

func (s *StartUpTimer) Split(i int) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.times[i] = s.tickedTime
	s.tickedTime = 0
}

func (s *StartUpTimer) HardReset() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	alreadyStarted := s.timerStarted
	if alreadyStarted {
		s.Stop()
	}
	s.tickedTime = 0
	s.maxTime = 0
	s.times = make([]int64, 3)
	s.timerExpired = nil
}

type StatusLevelWatcher struct {
	EventTrigger
	Status                        frontend.Status
	Ready                         bool
	StatusMessage                 string
	TranslatedStatusMessage       *frontend.TranslatedStatusMessage
	StatusChangedAt               time.Time
	CommandStatus                 frontend.StatusLevel
	RowStatuses                   map[int]ServerStatus
	alarms                        *AlarmState
	implement                     *ImplementState
	operations                    *OperationsState
	services                      *ServiceStatusState
	models                        *ModelManagerState
	extra                         *ExtraStatusManager
	software                      *OverallSoftwareState
	commanderConfig               *config.ConfigTree
	commonConfig                  *config.ConfigTree
	stopCtx                       context.Context
	hasACValidated                bool
	hasChillerValidated           bool
	hasPoweredUp                  bool
	startUpTimer                  *StartUpTimer
	acValidationTimerExpired      atomic.Bool
	chillerValidationTimerExpired atomic.Bool
	powerUpTimerExpired           atomic.Bool
	robot                         environment.Robot
	featureFlags                  *FeatureFlagsGetter
	robotDefinitionState          *RobotDefinitionState
	moduleOrchestratorState       *ModuleOrchestratorState
	tractorSafetyState            *TractorSafetyState
}

func NewStatusLevelWatcher(alarms *AlarmState, implement *ImplementState, operations *OperationsState, services *ServiceStatusState, models *ModelManagerState, extra *ExtraStatusManager, software *OverallSoftwareState, commanderConfig *config.ConfigTree, commonConfig *config.ConfigTree, stopCtx context.Context, robot environment.Robot, robotDefinitionState *RobotDefinitionState, moduleOrchestratorState *ModuleOrchestratorState, tractorSafetyState *TractorSafetyState) *StatusLevelWatcher {
	action := &StatusLevelWatcher{
		Ready:                   false,
		alarms:                  alarms,
		implement:               implement,
		operations:              operations,
		services:                services,
		models:                  models,
		extra:                   extra,
		software:                software,
		hasACValidated:          false,
		hasChillerValidated:     false,
		hasPoweredUp:            false,
		commanderConfig:         commanderConfig,
		commonConfig:            commonConfig,
		stopCtx:                 stopCtx,
		robot:                   robot,
		featureFlags:            NewFeatureFlagsGetter(commonConfig),
		robotDefinitionState:    robotDefinitionState,
		moduleOrchestratorState: moduleOrchestratorState,
		tractorSafetyState:      tractorSafetyState,
	}
	action.triggerChannel = make(chan bool)
	action.startUpTimer = NewStartUpTimer(operations, action.featureFlags, commanderConfig)

	go action.watch()
	return action
}

func (action *StatusLevelWatcher) watch() {
	go notifyUpdate(action.alarms, action.triggerChannel, action.stopCtx)
	go notifyUpdate(action.implement, action.triggerChannel, action.stopCtx)
	go notifyUpdate(action.operations, action.triggerChannel, action.stopCtx)
	go notifyUpdate(action.services, action.triggerChannel, action.stopCtx)
	go notifyUpdate(action.models, action.triggerChannel, action.stopCtx)
	go notifyUpdate(action.software, action.triggerChannel, action.stopCtx)
	go notifyUpdate(action.tractorSafetyState, action.triggerChannel, action.stopCtx)
	if action.robotDefinitionState != nil {
		go notifyUpdate(action.robotDefinitionState, action.triggerChannel, action.stopCtx)
	}
	if action.moduleOrchestratorState != nil {
		go notifyUpdate(action.moduleOrchestratorState, action.triggerChannel, action.stopCtx)
	}

	for {
		select {
		case <-action.triggerChannel:
			action.Action()
		}
	}
}

func (action *StatusLevelWatcher) Action() {
	// alarms
	alarmList := make([]*alarms.Alarm, 0)
	hasPositiveOperatorConformation := true
	hasBlockingError := false
	alarmAutofixInProgress := false
	hasCenterEstop := action.featureFlags.IsEU.Load() || environment.IsReaper()
	action.implement.ReadOnCurrent(func() {
		if !action.implement.SafetyState.ResetRequired &&
			action.implement.SafetyState.EStopped &&
			action.implement.SafetyState.EStopped != (action.implement.SafetyState.InCabEStopped ||
				action.implement.SafetyState.LeftEStopped ||
				action.implement.SafetyState.RightEStopped ||
				(action.implement.SafetyState.CenterEstop && hasCenterEstop) ||
				action.implement.SafetyState.PowerButtonEstop ||
				!action.implement.SafetyState.WaterProtect ||
				!action.implement.SafetyState.Interlock ||
				!action.implement.SafetyState.LaserKey ||
				!action.implement.SafetyState.LeftLPSUInterlock ||
				!action.implement.SafetyState.RightLPSUInterlock ||
				action.implement.SafetyState.Lifted) {
			hasPositiveOperatorConformation = false
		}
	})
	action.alarms.ReadOnCurrent(func() {
		alarmAutofixInProgress = action.alarms.AutofixInProgress
		for _, prefix := range action.alarms.Alarms {
			for _, alarm := range prefix {
				if !alarm.Valid {
					continue
				}
				alarmList = append(alarmList, alarm)
				if alarm.Impact == alarms.AlarmImpactCritical {
					hasBlockingError = true
				}
			}
		}
	})

	// service
	isSoftwareInitialized := false
	isSoftwareInvalid := false
	var commanderStatus ServerStatus
	var rowStatuses map[int]ServerStatus
	action.services.ReadOnCurrent(func() {
		isSoftwareInitialized = action.services.OverallStatus == frontend.StatusLevel_READY
		isSoftwareInvalid = action.services.OverallStatus == frontend.StatusLevel_INVALID
		commanderStatus = action.services.CommandStatus
		rowStatuses = action.services.RowStatuses
	})

	// models
	isModelInitialized := false
	action.models.ReadOnCurrent(func() {
		isModelInitialized = action.models.Synchronized
	})

	// implement
	isEStopped := false
	isLeftEStopPressed := false
	isRightEStopPressed := false
	isWaterProtectOn := false
	isResetRequired := false
	isCenterEStopPressed := false
	isPowerButtonEStopPressed := false
	isLeftLPSUInterlockPressed := false
	isRightLPSUInterlockPressed := false
	isInterlockOn := false
	isLaserKeyOn := false
	isDebugModeOn := false
	isCabEStopPressed := false
	isMainContactorOn := false
	isLifted := false
	isServerACValid := false
	curServerACValidated := false
	isChillerValid := false
	curChillerValidated := false
	curRowsPowered := false
	action.implement.ReadOnCurrent(func() {
		isEStopped = action.implement.SafetyState.EStopped
		isLeftEStopPressed = action.implement.SafetyState.LeftEStopped
		isRightEStopPressed = action.implement.SafetyState.RightEStopped
		isCabEStopPressed = action.implement.SafetyState.InCabEStopped
		isWaterProtectOn = action.implement.SafetyState.WaterProtect
		isResetRequired = action.implement.SafetyState.ResetRequired
		isCenterEStopPressed = (action.implement.SafetyState.CenterEstop && hasCenterEstop)
		isPowerButtonEStopPressed = action.implement.SafetyState.PowerButtonEstop
		isLeftLPSUInterlockPressed = action.implement.SafetyState.LeftLPSUInterlock
		isRightLPSUInterlockPressed = action.implement.SafetyState.RightLPSUInterlock
		isInterlockOn = action.implement.SafetyState.Interlock
		isLaserKeyOn = action.implement.SafetyState.LaserKey
		isDebugModeOn = action.implement.SafetyState.DebugMode
		isMainContactorOn = environment.CarbonGen(action.implement.robot.MakaGen) == environment.CarbonGenBud || action.implement.SupervisoryState.MainContactorStatusFb
		isLifted = action.implement.SafetyState.Lifted
		isServerACValid = action.implement.ServerACValid
		curServerACValidated = action.implement.ServerACValidated
		isChillerValid = action.implement.ChillerValid
		curChillerValidated = action.implement.ChillerValidated
		curRowsPowered = action.implement.RowsPowered
	})

	// operations
	rowsEnabled := 0
	hasOperationsStatusChanged := false
	laserPausedDueToVel := false
	action.operations.ReadOnCurrent(func() {
		for _, rowStatus := range action.operations.RowWeeding {
			if rowStatus.ActualTargeting.Enabled() {
				rowsEnabled = rowsEnabled + 1
			}
		}
		for _, safety_state := range action.operations.RowWeeding {
			if safety_state.RowSafetyState == 1 {
				laserPausedDueToVel = true
			}
		}
		if action.operations.CommandStatus.Ready != commanderStatus.Ready || len(action.operations.CommandStatus.ServicesState) != len(commanderStatus.ServicesState) {
			hasOperationsStatusChanged = true
			return
		}
		for i, service := range commanderStatus.ServicesState {
			if service.Name != action.operations.CommandStatus.ServicesState[i].Name || service.Ready != action.operations.CommandStatus.Ready {
				hasOperationsStatusChanged = true
				return
			}
		}

		if len(action.operations.RowStatuses) != len(rowStatuses) {
			hasOperationsStatusChanged = true
			return
		}
		for row := range rowStatuses {
			if action.operations.RowStatuses[row].Ready != rowStatuses[row].Ready {
				hasOperationsStatusChanged = true
				return
			}
			for i, service := range rowStatuses[row].ServicesState {
				if service.Name != action.operations.RowStatuses[row].ServicesState[i].Name || service.Ready != action.operations.RowStatuses[row].ServicesState[i].Ready {
					hasOperationsStatusChanged = true
					return
				}
			}
		}
	})

	// extra
	isWeeding := action.extra.weedingState.IsWeeding

	// software
	isUpdating := false
	action.software.ReadOnCurrent(func() {
		isUpdating = action.software.Updating || action.software.IsHostUpdating()
	})

	// toggle server AC states
	acValidationTimerExpired := action.acValidationTimerExpired.Load()
	if (isServerACValid || acValidationTimerExpired) && !action.hasACValidated {
		action.hasACValidated = true
	}

	if !isMainContactorOn && action.hasACValidated {
		action.hasACValidated = false
	}
	serverACValidated := action.hasACValidated && isMainContactorOn

	// toggle chiller states
	chillerValidationTimerExpired := action.chillerValidationTimerExpired.Load()
	if (isChillerValid || chillerValidationTimerExpired) && !action.hasChillerValidated {
		action.hasChillerValidated = true
	}

	if !isMainContactorOn && action.hasChillerValidated {
		action.hasChillerValidated = false
	}
	chillerValidated := action.hasChillerValidated && isMainContactorOn

	// toggle powered on state
	if isMainContactorOn && isSoftwareInitialized && !action.hasPoweredUp {
		action.hasPoweredUp = true
	}
	if !isMainContactorOn && action.hasPoweredUp {
		action.hasPoweredUp = false
	}
	powerUpTimerExpired := action.powerUpTimerExpired.Load()
	rowsPowered := (action.hasPoweredUp || powerUpTimerExpired) && isMainContactorOn && serverACValidated

	// robot definition
	robotDefIsSet := true
	if environment.IsReaper() {
		robotDefIsSet = action.robotDefinitionState.DefinitionIsSet()
	}

	moduleAssignmentsMatchRobotDef := true
	if environment.IsReaper() {
		moduleAssignmentsMatchRobotDef = action.moduleOrchestratorState.AssignedModulesMatchRobotDef()
	}

	details := ""
	status := frontend.Status_STATUS_UNKNOWN
	rowsFormat := "%v / %v"
	if !robotDefIsSet {
		status = frontend.Status_STATUS_LOADING
		details = "Robot Not Configured"
		action.Ready = false
	} else if !moduleAssignmentsMatchRobotDef {
		status = frontend.Status_STATUS_LOADING
		details = "Missing Module Assignment"
		action.Ready = false
	} else if isUpdating {
		status = frontend.Status_STATUS_UPDATE_INSTALLING
		action.Ready = false
	} else if alarmAutofixInProgress {
		status = frontend.Status_STATUS_ALARM_AUTOFIX_IN_PROGRESS
		details = "Please wait"
		action.Ready = false
	} else if !isMainContactorOn {
		status = frontend.Status_STATUS_POWERED_DOWN
		action.Ready = false
	} else if !serverACValidated {
		status = frontend.Status_STATUS_SERVER_CABINET_COOLDOWN
		action.Ready = false
	} else if !chillerValidated {
		status = frontend.Status_STATUS_CHILLER_COOLDOWN
		action.Ready = false
	} else if !isSoftwareInitialized {
		if action.hasPoweredUp {
			status = frontend.Status_STATUS_LOADING
			details = "Software loading"
		} else {
			if powerUpTimerExpired {
				status = frontend.Status_STATUS_FAILED_TO_POWER_UP
				details = "Please contact support"
			} else {
				status = frontend.Status_STATUS_POWERING_UP
			}
		}
		action.Ready = false
	} else if !isModelInitialized {
		status = frontend.Status_STATUS_MODEL_LOADING
		action.Ready = false
	} else if isSoftwareInvalid {
		status = frontend.Status_STATUS_ERROR
		action.Ready = false
	} else if isLifted {
		status = frontend.Status_STATUS_LIFTED
		action.Ready = false
	} else if isResetRequired {
		status = frontend.Status_STATUS_ESTOPPED
		details = "Press The Start Button on Pendant"
		action.Ready = false
	} else if isDebugModeOn {
		status = frontend.Status_STATUS_ERROR
		details = "Debug Mode"
		action.Ready = false
	} else if !hasPositiveOperatorConformation {
		status = frontend.Status_STATUS_ESTOPPED
		details = "Press and reset any E-Stop"
		action.Ready = false
	} else if isEStopped {
		status = frontend.Status_STATUS_ESTOPPED
		if !isWaterProtectOn {
			details = "Water Protect"
		} else if isCabEStopPressed {
			details = "In-cab E-Stop"
		} else if isLeftEStopPressed {
			details = "Left E-Stop"
		} else if isRightEStopPressed {
			details = "Right E-Stop"
		} else if isCenterEStopPressed {
			details = "Center E-Stop"
		} else if isPowerButtonEStopPressed {
			details = "Power Button E-Stop"
		} else if !isLeftLPSUInterlockPressed {
			details = "Left LPSU Door Interlock"
		} else if !isRightLPSUInterlockPressed {
			details = "Right LPSU Door Interlock"
		} else if !isInterlockOn {
			details = "Interlock"
		} else if !isLaserKeyOn {
			details = "Laser Key"
		}
		action.Ready = false
	} else if hasBlockingError {
		status = frontend.Status_STATUS_ERROR
		details = "Operation-blocking alarm"
		action.Ready = true
	} else if isWeeding {
		status = frontend.Status_STATUS_WEEDING
		details = fmt.Sprintf(rowsFormat, rowsEnabled, len(rowStatuses))
		action.Ready = true
	} else if !action.tractorSafetyState.IsSafeComputed() {
		status = frontend.Status_STATUS_TRACTOR_NOT_SAFE
		details = "Outside of safe operating zone"
		action.Ready = false
	} else {
		status = frontend.Status_STATUS_STANDBY
		details = fmt.Sprintf(rowsFormat, rowsEnabled, len(rowStatuses))
		action.Ready = true
	}

	// server ac cooldown, start timer
	if status == frontend.Status_STATUS_SERVER_CABINET_COOLDOWN && action.Status != frontend.Status_STATUS_SERVER_CABINET_COOLDOWN {
		action.startUpTimer.UpdateStatus(status, "ac_cooldown_time_seconds", &action.acValidationTimerExpired)
		action.startUpTimer.Start(0)
	}

	// server ac cooldown, stop timer
	if status != frontend.Status_STATUS_SERVER_CABINET_COOLDOWN && action.Status == frontend.Status_STATUS_SERVER_CABINET_COOLDOWN {
		action.startUpTimer.Stop()
		action.startUpTimer.Split(1) // store how long the ac timer took for use by chiller

	}

	// chiller cooldown, start timer
	if status == frontend.Status_STATUS_CHILLER_COOLDOWN && action.Status != frontend.Status_STATUS_CHILLER_COOLDOWN {
		action.startUpTimer.UpdateStatus(status, "chiller_cooldown_time_seconds", &action.chillerValidationTimerExpired)
		action.startUpTimer.Start(1)
	}

	// chiller cooldown, stop timer
	if status != frontend.Status_STATUS_CHILLER_COOLDOWN && action.Status == frontend.Status_STATUS_CHILLER_COOLDOWN {
		action.startUpTimer.Stop()
		action.startUpTimer.Split(2) // store how long the chiller timer took for use by powering up timer
	}

	// power up started, start timer
	if status == frontend.Status_STATUS_POWERING_UP && action.Status != frontend.Status_STATUS_POWERING_UP {
		action.startUpTimer.UpdateStatus(status, "power_on_time_seconds", &action.powerUpTimerExpired)
		action.startUpTimer.Start(2)
	}

	// power up ended, stop timer
	if status != frontend.Status_STATUS_POWERING_UP && action.Status == frontend.Status_STATUS_POWERING_UP {
		action.startUpTimer.Stop()
	}

	// powering down, reset timer states
	if status == frontend.Status_STATUS_POWERED_DOWN && action.Status != frontend.Status_STATUS_POWERED_DOWN {
		action.startUpTimer.HardReset()
		action.acValidationTimerExpired.Store(false)
		action.chillerValidationTimerExpired.Store(false)
		action.powerUpTimerExpired.Store(false)
	}

	ts := action.operations.GetExpectedTargetingState()
	thinning_enabled := action.featureFlags.ThinningEnabled.Load()
	statusMessage, translatedStatusMessage := formatStatus(action.Status, details, &formatStatusArgs{
		TargetingState:      &ts,
		ThinningEnabled:     thinning_enabled,
		LaserPausedDueToVel: laserPausedDueToVel,
		TimerSeconds:        -1,
	})
	hasStatusChanged := hasOperationsStatusChanged || action.Status != status || statusMessage != action.StatusMessage
	action.Status = status
	action.StatusMessage = statusMessage
	action.TranslatedStatusMessage = translatedStatusMessage
	action.StatusChangedAt = time.Now()

	if hasStatusChanged {
		action.operations.WriteOnCurrent(func() {
			action.operations.Status = action.Status
			// if powering up (or AC/Chiller cooldown), don't write message here
			// it's handled by the timer
			if status != frontend.Status_STATUS_POWERING_UP &&
				status != frontend.Status_STATUS_SERVER_CABINET_COOLDOWN &&
				status != frontend.Status_STATUS_CHILLER_COOLDOWN {
				action.operations.StatusMessage = action.StatusMessage
				action.operations.TranslatedStatusMessage = action.TranslatedStatusMessage
			}
			action.operations.StatusChangedAt = action.StatusChangedAt
			action.operations.Ready = action.Ready
			action.operations.RowStatuses = rowStatuses
			action.operations.CommandStatus = commanderStatus
		})
	}

	hasImplementStatusChanged := serverACValidated != curServerACValidated ||
		chillerValidated != curChillerValidated ||
		rowsPowered != curRowsPowered
	if hasImplementStatusChanged {
		action.implement.WriteOnCurrent(func() {
			// this is the only place these variables should be set
			action.implement.ServerACValidated = serverACValidated
			action.implement.ChillerValidated = chillerValidated
			action.implement.RowsPowered = rowsPowered
		})
	}

	isEStoppedGauge.Set(metrics.BoolToFloat(isEStopped))
	isLoadingGauge.Set(metrics.BoolToFloat(action.Status == frontend.Status_STATUS_POWERING_UP || action.Status == frontend.Status_STATUS_FAILED_TO_POWER_UP))
	isReadyGauge.Set(metrics.BoolToFloat(action.Ready))
}

type HealthWatcher struct {
	EventTrigger
	portal             *portal_clients.Client
	robot              environment.Robot
	alarms             *AlarmState
	operations         *OperationsState
	models             *ModelManagerState
	hardware           *hardware_manager.HardwareManagerClient
	commander          *config.ConfigTree
	common             *config.ConfigTree
	software           *OverallSoftwareState
	weeding            *WeedingState
	redis              *redis.Client
	metrics            *metrics_aggregator.MetricsAggregatorClient
	scanner            *OverallScannerState
	thinning           *ThinningConfState
	jobs               *JobsState
	almanac            *AlmanacCfgState
	discriminator      *DiscriminatorCfgState
	host               *HostState
	profileSync        *ProfileSyncManager
	features           *FeatureState
	modelinator        *ModelinatorCfgState
	velocity           *TargetVelocityProfileState
	categoryCollection *CategoryCollectionCfgState
	category           *CategoryCfgState
}

func NewHealthWatcher(
	portal *portal_clients.Client,
	robot environment.Robot,
	alarms *AlarmState,
	operations *OperationsState,
	models *ModelManagerState,
	hardware *hardware_manager.HardwareManagerClient,
	commander *config.ConfigTree,
	common *config.ConfigTree,
	software *OverallSoftwareState,
	weeding *WeedingState,
	redisClient *redis.Client,
	metrics *metrics_aggregator.MetricsAggregatorClient,
	scanner *OverallScannerState,
	thinning *ThinningConfState,
	jobs *JobsState,
	almanac *AlmanacCfgState,
	discriminator *DiscriminatorCfgState,
	host *HostState,
	profileSync *ProfileSyncManager,
	features *FeatureState,
	modelinator *ModelinatorCfgState,
	velocity *TargetVelocityProfileState,
	categoryCollection *CategoryCollectionCfgState,
	category *CategoryCfgState,
) *HealthWatcher {
	action := &HealthWatcher{
		portal:             portal,
		robot:              robot,
		alarms:             alarms,
		operations:         operations,
		models:             models,
		hardware:           hardware,
		commander:          commander,
		common:             common,
		software:           software,
		weeding:            weeding,
		redis:              redisClient,
		metrics:            metrics,
		scanner:            scanner,
		thinning:           thinning,
		jobs:               jobs,
		almanac:            almanac,
		discriminator:      discriminator,
		host:               host,
		profileSync:        profileSync,
		features:           features,
		modelinator:        modelinator,
		velocity:           velocity,
		categoryCollection: categoryCollection,
		category:           category,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func (w *HealthWatcher) Action() {
	if w.robot.MakaRobotName == "" {
		log.Warn("robot name not set, not skipping adding health log")
		return
	}

	alarmList := make([]*frontend.AlarmRow, 0)
	w.alarms.ReadOnCurrent(func() {
		for _, prefix := range w.alarms.Alarms {
			for _, alarm := range prefix {
				if !alarm.Valid {
					continue
				}
				alarmList = append(alarmList, alarms.Alarm2Frontend(alarm))
			}
		}
	})

	models := []string{}

	w.models.ReadOnCurrent(func() {
		for _, model := range w.models.LocalModels {
			models = append(models, model.ID)
		}
	})

	performance := &portal.Performance{}
	w.weeding.ReadOnCurrent(func() {
		performance.Weeding = &portal.WeedingPerformance{
			AreaWeededTotal: w.weeding.AreaWeededTotal,
			AreaWeededToday: w.weeding.AreaWeededToday,
			TimeWeededToday: w.weeding.TimeWeededTodayMs,
		}
	})

	var location *portal.Location
	GPSData, err := w.hardware.GetGPSData(true)
	if err != nil || GPSData == nil || GPSData.Lla == nil {
		location = nil
	} else {
		location = &portal.Location{
			X: GPSData.Lla.Lat,
			Y: GPSData.Lla.Lng,
			Z: GPSData.Lla.Alt,
		}
	}

	targetVersion := ""
	targetVersionReady := false
	w.software.ReadOnCurrent(func() {
		targetVersion = w.software.Summary.Target.Tag
		targetVersionReady = w.software.Summary.Target.Available && w.software.Summary.Target.Ready
	})

	status := frontend.Status_STATUS_UNKNOWN
	statusMessage := ""
	statusChangedAt := int64(0)
	var translatedStatusMessage *frontend.TranslatedStatusMessage
	w.operations.ReadOnCurrent(func() {
		status = w.operations.Status
		statusMessage = w.operations.StatusMessage
		translatedStatusMessage = w.operations.TranslatedStatusMessage
		statusChangedAt = w.operations.StatusChangedAt.Unix()
	})

	fieldConfig := &portal.FieldConfig{}
	isBandingEnabled, err := isBandingEnabled(w.redis)
	if err == nil {
		fieldConfig.BandingEnabled = *isBandingEnabled
	}
	isBandingDynamic, err := isBandingDynamic(w.redis)
	if err == nil {
		fieldConfig.BandingDynamic = *isBandingDynamic
	}
	activeBandingDef, err := getActiveBandingDef(w.redis)
	if err == nil {
		fieldConfig.ActiveBandConfig = *activeBandingDef
	}
	fieldConfig.ActiveThinningConfigId = w.thinning.GetActiveId()
	fieldConfig.ActiveJobId = w.jobs.GetActiveId()
	fieldConfig.ActiveAlmanacId = w.almanac.GetActiveCfg()
	fieldConfig.ActiveDiscriminatorId = w.discriminator.GetActiveCfg()
	fieldConfig.ActiveModelinatorId = w.modelinator.getRemoteProfileId(w.modelinator.GetActiveCfg())
	fieldConfig.ActiveVelocityEstimatorId = w.velocity.getActiveId()
	fieldConfig.ActiveCategoryCollectionId = w.categoryCollection.GetActiveCfg()
	w.operations.ReadOnCurrent(func() {
		if w.operations.ExpectedTargeting.WeedState.Enabled {
			fieldConfig.IsWeeding = true
		}
		if w.operations.ExpectedTargeting.ThinningState.Enabled {
			fieldConfig.IsThinning = true
		}
	})
	crop := w.commander.GetNode("current_crop").GetStringValue()
	cropID := w.commander.GetNode("current_crop_id").GetStringValue()
	if len(cropID) > 0 {
		crop = ""
	}
	healthLog := &portal.HealthLog{
		Location:    location,
		CropId:      cropID,
		Crop:        crop,
		Model:       w.common.GetNode("deepweed/model_id").GetStringValue(),
		P2P:         w.common.GetNode("p2p/model_id").GetStringValue(),
		Models:      models,
		Performance: performance,
		ReportedAt:  time.Now().Unix(),
		RobotSerial: w.robot.MakaRobotName,
		// TODO: Systems
		Status:                  status,
		StatusMessage:           statusMessage,
		TranslatedStatusMessage: translatedStatusMessage,
		StatusChangedAt:         statusChangedAt,
		TargetVersion:           targetVersion,
		TargetVersionReady:      targetVersionReady,
		MetricTotals:            metricTotals(w.redis),
		AlarmList:               alarmList,
		FieldConfig:             fieldConfig,
		Metrics:                 getMetrics(w.metrics),
		RobotRuntime_240V:       w.GetRuntime(),
		LaserState:              w.getLaserState(),
		LaserChangeTimes:        w.getLaserDetails(),
		HostSerials:             w.getHostSerials(),
		FeatureFlags:            w.features.GetFeatureFlags(),
	}

	if w.robot.CarbonVersionTag == "latest" {
		healthLog.SoftwareVersion = "DEVELOPMENT"
	} else {
		w.software.ReadOnCurrent(func() {
			healthLog.SoftwareVersion = w.software.Summary.Current.Tag
		})
	}

	serializedLog, err := protojson.Marshal(healthLog)

	// persist to redis
	if err != nil {
		log.Warnf("Failed to serialize health log at %d for persistence", healthLog.ReportedAt)
	} else {
		w.redis.HSet(redis_health_queue_key, strconv.FormatInt(healthLog.ReportedAt, 10), string(serializedLog))
	}

	w.SendLogs()
}

func (w *HealthWatcher) getLaserState() *frontend.LaserStateList {
	s, _ := w.scanner.GetLaserState(w.redis, func(f func()) bool {
		w.scanner.ReadOnCurrent(f)
		return true
	})
	return s
}

func (w *HealthWatcher) SendLogs() {
	// skip logging if there is no portal client
	if w.portal == nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()
	keys, err := w.redis.HKeysWithContext(ctx, redis_health_queue_key)
	if err != nil {
		log.WithError(err).Error("Could not read health log keys")
		return
	}

	sort.Slice(keys, func(i, j int) bool {
		return strings.Compare(keys[i], keys[j]) < 0
	})

	for index, key := range keys {
		rawHealthLog, err := w.redis.HGet(redis_health_queue_key, key)
		if err != nil {
			log.WithError(err).Errorf("failed to read health log %v", key)
			w.redis.HDel(redis_health_queue_key, key)
			continue
		}
		healthLog := &portal.HealthLog{}
		err = protojson.Unmarshal([]byte(rawHealthLog), healthLog)
		if err != nil {
			// corrupt message, purge
			log.WithError(err).Errorf("failed to unmarshal health log %v", key)
			w.redis.HDel(redis_health_queue_key, key)
			continue
		}

		// purposely ignoring delete error
		delKeyFunc := func(key, member string) {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()
			w.redis.HDelWithContext(ctx, key, member)
		}

		_, err = w.portal.LogHealth(healthLog)
		switch status.Code(err) {
		case codes.OK:
			delKeyFunc(redis_health_queue_key, key)
			acknowledeMetrics(w.metrics, healthLog.Metrics)
		case codes.InvalidArgument:
			log.WithError(err).Warnf("Invalid health log %v, dropping", key)
			delKeyFunc(redis_health_queue_key, key)
		case codes.Internal:
			// Portal database error, for now retry. We may choose to handle this differently in the future.
			fallthrough
		case codes.NotFound:
			// Portal doesn't know about this robot yet, retry later
			fallthrough
		default:
			log.WithError(err).Warnf("Error while pushing health log. Logs in queue for next retry: %d/%d", index, len(keys))
			return
		}
	}
}

func (w *HealthWatcher) GetRuntime() uint32 {
	runtime_240v, err := w.hardware.GetRuntime()
	if err != nil {
		return 0
	}
	return runtime_240v
}

func isBandingEnabled(redis *redis.Client) (*bool, error) {
	value, err := redis.ReadInt64("banding/enabled", 0)
	if err != nil {
		return nil, fmt.Errorf("Failed to read from Redis: %e", err)
	}
	isBandingEnabled := value == 1
	return &isBandingEnabled, nil
}

func isBandingDynamic(redis *redis.Client) (*bool, error) {
	value, err := redis.ReadInt64("banding/dynamic_banding_selected", 0)
	if err != nil {
		return nil, fmt.Errorf("Failed to read from Redis: %e", err)
	}
	isBandingDynamic := value == 1
	return &isBandingDynamic, nil
}

func getActiveBandingDef(redis *redis.Client) (*string, error) {
	name, err := redis.ReadString("banding/active_def_uuid", "")
	if err != nil {
		return nil, fmt.Errorf("Failed to read from Redis: %e", err)
	}
	return &name, nil
}

// keys to query from redis to be used as metric totals. **NOTE** the last part (split by '/') MUST be in openmetrics format.
var metricRedisKeyPatterns = []string{"weed_tracking/*/weed_kill_count_total", "commander/area_weeded_total", "commander/time_weeded_total", "weed_tracking/*/crop_kill_count_total"}

func metricTotals(redis *redis.Client) map[string]uint64 {
	totals := make(map[string]uint64)
	for _, pattern := range metricRedisKeyPatterns {
		keys, err := redis.Keys(pattern)
		if err != nil {
			log.Errorf("redis Keys(%s) failed, error:%s", pattern, err)
			continue
		}
		for _, key := range keys {
			if tot, err := redis.Get(key); err != nil {
				log.Errorf("redis Get(%s) failed, error:%s", key, err)
			} else {
				// we don't really care about 0/error here since we know to only ever take the higher #
				totals[key], _ = strconv.ParseUint(tot, 10, 64)
			}
		}
	}
	return totals
}

func getMetrics(metrics *metrics_aggregator.MetricsAggregatorClient) *portal.Metrics {
	daily_metrics := make(map[string]*portal.DailyMetrics)
	metrics_resp, err := metrics.GetMetrics()
	if err == nil {
		for key, element := range metrics_resp.DailyMetrics {
			daily_metrics[key] = &portal.DailyMetrics{
				Metrics: element.Metrics,
			}
		}
	}
	return &portal.Metrics{
		DailyMetrics: daily_metrics,
	}

}
func acknowledeMetrics(metrics_client *metrics_aggregator.MetricsAggregatorClient, metrics *portal.Metrics) {
	days := make([]string, len(metrics.DailyMetrics))
	for day := range metrics.DailyMetrics {
		days = append(days, day)
	}
	metrics_client.AcknowledgeDailyMetric(days)
}

func (w *HealthWatcher) getLaserDetails() *pb_metrics.LaserChangeTimes {
	details, err := w.metrics.GetLaserDetails()
	if err != nil {
		log.Errorf("Failed to fetch laser details. Err: {%v}", err)
		return nil
	}
	return details
}
func (w *HealthWatcher) getHostSerials() map[string]string {
	var serials map[string]string
	w.host.ReadOnCurrent(func() {
		serials = w.host.servers
	})
	return serials
}

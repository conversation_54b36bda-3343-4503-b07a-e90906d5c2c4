syntax = "proto3";

package carbon.host_check.host;
option go_package = "proto/host_check";

import "frontend/proto/translation.proto";

message HostStatusRequest {
}

enum HostStatusState {
	OK = 0;
    DEGRADED = 1;
    FAILED = 2;
}

enum AlarmCode {
	A_NOALARM = 0;
	A_GPUFAIL = 1;
	A_GPUOVERTEMP = 2;
	A_DISKSPACE = 3 [deprecated=true];
	A_PTPCLOCK = 4;
	A_NETWORK = 5;
	A_DISKSPACE_WARN = 6;
	A_DISKSPACE_ERROR = 7;
	A_GPU_CHECKER_FAIL = 8;
	A_NETWORK_CHECKER_FAIL = 9;
}

enum AlarmImpact {
	AI_None = 0;
	AI_Degraded = 1;
	AI_Offline = 2;
	AI_Critical = 3;
}

enum AlarmLevel {
	AL_Unknown = 0;
	AL_Critical = 1;
	AL_High = 2;
	AL_Medium = 3;
	AL_Low = 4;
	AL_Hidden = 5;
}

message Alarm {
	AlarmCode code = 1;
	string subsystem = 2;
	string description = 3;	
	AlarmLevel level = 4;
	AlarmImpact impact = 5;
	int32 param = 6;
	repeated frontend.translation.TranslationParameter translation_parameters = 7;
}
message SysInfo {
	string hostname = 1;
	string serial = 2;
}

message HostStatus {
	HostStatusState status = 1;
	repeated Alarm alarms = 2;
	SysInfo system_info = 3;
}

service HostService {
	rpc GetHostStatus(HostStatusRequest) returns (HostStatus);
}

package main

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/host_check"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/carbonrobotics/robot/golang/lib/translation"
	"github.com/hellflame/argparse"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

const subsystem = "host"

/* These would be config settings - XXX config service doesn't like mac */
var max_gpu_temp_C int = 82
var disk_space_warn_pct int = 75
var disk_space_error_pct int = 95

var (
	diskSpaceUsedBytesGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "disk_space_used_bytes",
		Help:      "total bytes used on disk",
	}, []string{"host", "partition"})
	diskSpaceUsedPctGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "disk_space_used_pct",
		Help:      "percentage space used on disk",
	}, []string{"host", "partition"})
	gpuTempGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "gpu_temp_c",
		Help:      "GPU temperature in c",
	}, []string{"host", "gpu"})
	linkDetectedCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem: subsystem,
		Name:      "network_link_not_detected_count",
		Help:      "Number of times network link was not detected",
	}, []string{"host", "interface"})
	ipmiStatusGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "ipmi_status",
		Help:      "Is IPMI down",
	}, []string{"host"})
)

var uniqueParams = map[string]int32{
	"global":        1,
	"predict1":      2,
	"predict2":      3,
	"predict3":      4,
	"predict4":      4,
	"targets":       5,
	"target1":       6,
	"target2":       7,
	"scannerboards": 8,
	"scanner1":      9,
	"scanner2":      10,
	"drivecams":     11,
	"eno1":          12,
	"eno2":          13,
}

type HostService struct {
	host_check.UnimplementedHostServiceServer
	config_subscriber         *config.ConfigSubscriber
	max_gpu_temp_C_node       *config.ConfigTree
	disk_space_warn_pct_node  *config.ConfigTree
	disk_space_error_pct_node *config.ConfigTree
	robot                     environment.Robot
	ptpAlarmFilter            *PTPAlarmFilter
	nicStatusMap              map[string]bool
	serial                    string
}

func getCmdLines(name string, args ...string) []string {
	var ret []string
	var out bytes.Buffer
	cmd := exec.Command(name, args...)
	cmd.Stdout = &out
	cmd.Stderr = &out
	err := cmd.Run()
	if err != nil {
		logrus.Warnf("cmd '%v' '%v' failed with error %v", name, err, args)
		return []string{}
	}
	lines := strings.Split(out.String(), "\n")

	// remove empty lines
	for _, line := range lines {
		if len(line) > 0 {
			ret = append(ret, line)
		}
	}
	return ret
}

func getHostname(robot *environment.Robot) string {
	if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleModule {
		return fmt.Sprintf("%s_%s", robot.MakaRole, robot.CarbonModuleID)
	} else if environment.CarbonRole(robot.MakaRole) != environment.CarbonRoleCommand {
		return fmt.Sprintf("%s_%s", robot.MakaRole, robot.MakaRow)
	} else {
		return robot.MakaRole
	}
}

func getDiskInfo(robot *environment.Robot, do_print bool) []*host_check.Alarm {
	var ret []*host_check.Alarm
	var pct float32
	var used int
	var size int

	var diskreg = regexp.MustCompile("\\S+\\s+(\\d+)\\s+(\\d+)")
	partitions := []string{"/old", "/data", "/other"}
	partitionNames := []string{"/", "/data", "/other"}
	for idx, partition := range partitions {
		partitionName := partitionNames[idx]
		lines := getCmdLines("df", partition)
		used = 0
		size = 0
		pct = 0

		if len(lines) >= 2 {
			line := lines[1]
			vals := diskreg.FindAllStringSubmatch(line, -1)
			/* match will come back with one set of matches:
			   first set contains 0: full string, 1: submatch 1, 2: submatch 2

			   what we want is match[0][1] and match[0][2]
			*/
			size, _ = strconv.Atoi(vals[0][1])
			used, _ = strconv.Atoi(vals[0][2])
			size *= 512 // 512 byte blocks
			used *= 512 // 512 byte blocks
			pct = (float32(used) / float32(size)) * 100.0
			if int(pct) > disk_space_error_pct {
				descr := fmt.Sprintf("%s partition %v FULL (%d%%, over %d%%)", getHostname(robot), partitionName, int(pct), disk_space_error_pct)
				ret = append(ret, &host_check.Alarm{Code: host_check.AlarmCode_A_DISKSPACE_ERROR, Subsystem: "Host/Disk", Description: descr,
					Level:  host_check.AlarmLevel_AL_High,
					Impact: host_check.AlarmImpact_AI_None,
					Param:  int32(idx),
					TranslationParameters: translation.Params(
						translation.StringParam("hostname", getHostname(robot)),
						translation.StringParam("partition", partitionName),
						translation.IntParam("percent", int(pct)),
						translation.IntParam("error_percent", disk_space_error_pct),
					),
				})
			} else if int(pct) > disk_space_warn_pct {
				descr := fmt.Sprintf("%s partition %v space at %d%% (over %d%%)", getHostname(robot), partitionName, int(pct), disk_space_warn_pct)
				ret = append(ret, &host_check.Alarm{Code: host_check.AlarmCode_A_DISKSPACE_WARN, Subsystem: "Host/Disk", Description: descr,
					Level:  host_check.AlarmLevel_AL_Low,
					Impact: host_check.AlarmImpact_AI_None,
					Param:  int32(idx),
					TranslationParameters: translation.Params(
						translation.StringParam("hostname", getHostname(robot)),
						translation.StringParam("partition", partitionName),
						translation.IntParam("percent", int(pct)),
						translation.IntParam("warning_percent", disk_space_warn_pct),
					),
				})
			}
		}

		if do_print {
			fmt.Printf("%v: %d bytes of %d bytes used (%.2f %%)\n", partitionName, used, size, pct)
		} else {
			/* Disk Gauges */
			hostname := getHostname(robot)
			diskSpaceUsedBytesGauge.WithLabelValues(fmt.Sprint(hostname), partitionName).Set(float64(used))
			diskSpaceUsedPctGauge.WithLabelValues(fmt.Sprint(hostname), partitionName).Set(float64(pct))
		}
	}

	lines := getCmdLines("bash", "-c", "mount | grep /other")
	if len(lines) == 0 {
		ret = append(ret, &host_check.Alarm{Code: host_check.AlarmCode_A_DISKSPACE, Subsystem: "Host/Disk", Description: "/other partition not found",
			Level:  host_check.AlarmLevel_AL_Hidden,
			Impact: host_check.AlarmImpact_AI_None})
	}
	if do_print {
		fmt.Printf("Other partition exists: %v\n", len(lines) > 0)
	}

	return ret
}

type PTPAlarmFilter struct {
	UseFilter          bool
	FirstAlarmTime     map[string]int64
	WaitToAlarmTimeSec *config.ConfigTree
}

func getRowName(robot *environment.Robot) string {
	var rowName string
	row := robot.MakaRow
	if row == "0" || row == "1" {
		rowName = "left"
	} else if row == "2" {
		rowName = "middle"
	} else if row == "3" {
		rowName = "right"
	}

	if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRowPrimary {
		rowName = rowName + "_primary"
	} else if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRowSecondary {
		rowName = rowName + "_secondary"
	}

	if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleModule {
		rowName = fmt.Sprintf("module%02s", robot.CarbonModuleID)
	}

	return rowName
}

func getPTPInfo(do_print bool, robot *environment.Robot, filter *PTPAlarmFilter) []*host_check.Alarm {
	var ret []*host_check.Alarm
	const toolong_ns = 200 * 1000
	/* example output:

	predict4 : phc_ctl[82512.523]: offset from CLOCK_REALTIME is -433ns
	*/
	interfaces := ""
	switch environment.CarbonRole(robot.MakaRole) {
	case environment.CarbonRoleCommand:
		interfaces = "global"
	case environment.CarbonRoleModule:
		interfaces = "global predict1 target1 target2 scanner1 scanner2"
	case environment.CarbonRoleRow:
		fallthrough
	case environment.CarbonRoleRowPrimary:
		fallthrough
	case environment.CarbonRoleRowSecondary:
		interfaces = "global predict1 predict2 predict3 predict4 targets scannerboards"
	default:
		logrus.Warnf("Unknown role %v, skipping getPTPInfo", robot.MakaRole)
		return ret
	}

	phcCmdFmt := "for net in %s; do echo -n $net \": \"; /usr/sbin/phc_ctl $net cmp; done || true"
	lines := getCmdLines("bash", "-c", fmt.Sprintf(phcCmdFmt, interfaces))

	var timereg = regexp.MustCompile(`^(\S+).* ([-\d]+)(\S+)$`)
	/* should match: netname, number, units */

	rowName := getRowName(robot)

	var index int
	for _, line := range lines {
		vals := timereg.FindAllStringSubmatch(line, -1)
		if len(vals) > 0 {
			subvals := vals[0]
			if len(subvals) >= 4 {
				device := subvals[1]
				digits, _ := strconv.Atoi(subvals[2])
				units := subvals[3]
				device_id := fmt.Sprintf("%s_%s", rowName, device)
				if digits > toolong_ns || digits < -toolong_ns || units != "ns" {
					shouldAlarm := true
					if filter.UseFilter {
						if firstTs, ok := filter.FirstAlarmTime[device_id]; ok {
							// alarm appeared before and was not cleared yet
							shouldAlarm = time.Now().Unix()-firstTs > int64(filter.WaitToAlarmTimeSec.GetUIntValue())
						} else {
							// first appearence of alarm
							shouldAlarm = false
							filter.FirstAlarmTime[device_id] = time.Now().Unix()
						}
					}
					if shouldAlarm {
						code := host_check.AlarmCode_A_PTPCLOCK
						descr := fmt.Sprintf("PTP device %s off > 4usec (%d %s)", device_id, digits, units)
						ret = append(ret, &host_check.Alarm{Code: code, Subsystem: "Host/PTP", Description: descr,
							Level:  host_check.AlarmLevel_AL_High,
							Impact: host_check.AlarmImpact_AI_Degraded,
							Param:  int32(index),
							TranslationParameters: translation.Params(
								translation.StringParam("device_id", device_id),
							),
						})
						index++
					}
				} else {
					// no alarm, clear saved timestamp in the filter
					delete(filter.FirstAlarmTime, device_id)
				}
				if do_print {
					fmt.Printf("%s / %d / %s\n", device, digits, units)
				}
			}
		}
	}
	return ret
}

const expected_host_gpus int = 8
const expected_host_gpus_minicomputer int = 5
const expected_host_gpus_module int = 2
const gpufile = "/tmp/host_gpu_check.json"

type GPUInfo struct {
	GPUNum int `json:"gpu_num"`
	TempC  int `json:"tempC"`
}

type GPUInfos struct {
	gpus map[int]GPUInfo
}

func NewGPUInfos(filename string) (*GPUInfos, error) {
	info := GPUInfos{gpus: make(map[int]GPUInfo)}
	fromfile := make([]GPUInfo, 10)

	file, err := os.Stat(filename)
	if err != nil {
		return nil, err
	}

	if file.ModTime().Before(time.Now().Add(-10 * time.Second)) {
		return nil, errors.New("GPU checker offline, hasn't responded for more than 10 seconds.")
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(data, &fromfile)
	if err != nil {
		return nil, err
	}

	for _, v := range fromfile {
		info.gpus[v.GPUNum] = v
	}
	return &info, nil
}

func (g *GPUInfos) GetTempCByIndex(index int) (int, error) {
	gpu, exist := g.gpus[index]
	if !exist {
		return 0, fmt.Errorf("gpu %d not available.", index)
	}
	return gpu.TempC, nil
}

func getGPUInfo(robot *environment.Robot, do_print bool) []*host_check.Alarm {
	var ret []*host_check.Alarm
	var expected_gpus []bool
	expected_gpu_count := expected_host_gpus
	if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRowPrimary || environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRowSecondary {
		expected_gpu_count = expected_host_gpus_minicomputer
	} else if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleModule {
		expected_gpu_count = expected_host_gpus_module
	}

	gpus, err := NewGPUInfos(gpufile)
	if err != nil {
		code := host_check.AlarmCode_A_GPU_CHECKER_FAIL
		descr := fmt.Sprintf("%s GPU cheker offline", getHostname(robot))
		ret = append(ret, &host_check.Alarm{Code: code, Subsystem: "Host/GPU", Description: descr,
			Level: host_check.AlarmLevel_AL_High, Impact: host_check.AlarmImpact_AI_Degraded,
			TranslationParameters: translation.Params(
				translation.StringParam("hostname", getHostname(robot)),
			)})
		if do_print {
			fmt.Printf("Alarm %v %v\n", code, descr)
		}
		return ret
	}

	for index := 0; index < expected_gpu_count; index++ {
		temp, err := gpus.GetTempCByIndex(index)
		if err != nil {
			// we will log the error below
			continue
		}
		if index < expected_host_gpus {
			expected_gpus = append(expected_gpus, true)
		}
		if int(temp) > max_gpu_temp_C {
			code := host_check.AlarmCode_A_GPUOVERTEMP
			descr := fmt.Sprintf("%s GPU %d temp %dC ( over %dC )", getHostname(robot), index, temp, max_gpu_temp_C)
			ret = append(ret, &host_check.Alarm{Code: code, Subsystem: "Host/GPU", Description: descr,
				Level:  host_check.AlarmLevel_AL_High,
				Impact: host_check.AlarmImpact_AI_Degraded,
				Param:  int32(index),
				TranslationParameters: translation.Params(
					translation.StringParam("hostname", getHostname(robot)),
					translation.IntParam("gpu_id", index),
					translation.TemperatureCelciusParam("temp", float64(temp)),
					translation.TemperatureCelciusParam("max_temp", float64(max_gpu_temp_C)),
				)})
		}
		if do_print {
			fmt.Printf("GPU %d TempC: %d\n", index, temp)
		} else {
			/* GPU Gauges */
			gpuTempGauge.WithLabelValues(fmt.Sprint(getHostname(robot)), fmt.Sprint(index)).Set(float64(temp))
		}
	}
	for i := range expected_gpus {
		if !expected_gpus[i] {
			code := host_check.AlarmCode_A_GPUFAIL
			descr := fmt.Sprintf("%s GPU %d offline", getHostname(robot), i)
			ret = append(ret, &host_check.Alarm{Code: code, Subsystem: "Host/GPU", Description: descr,
				Level:  host_check.AlarmLevel_AL_Critical,
				Impact: host_check.AlarmImpact_AI_Offline,
				Param:  int32(i),
				TranslationParameters: translation.Params(
					translation.StringParam("hostname", getHostname(robot)),
					translation.IntParam("gpu_id", i),
				),
			})
		}
	}

	return ret
}

func getNetworkCardSpeed(hostname, device string) (int, bool, error) {
	lines := getCmdLines("ethtool", device)
	speed := 0
	var err error
	linkDetected := false
	strLinkDetected := "Link detected: "
	speedFound := false
	linkDetectedFound := false

	for _, line := range lines {
		idx1 := strings.Index(line, "Speed: ")
		idx2 := strings.Index(line, "Mb/s")
		if idx1 > 0 && idx2 < 0 {
			// happens when link is down
			speed = 0
			speedFound = true
		} else if idx1 > 0 && idx2 > (idx1+7) {
			speed, err = strconv.Atoi(line[idx1+7 : idx2])
			speedFound = err == nil
		}

		idx1 = strings.Index(line, strLinkDetected)
		if idx1 > 0 {
			linkDetected = line[idx1+len(strLinkDetected):] == "yes"
			linkDetectedFound = true
		}
	}

	if len(lines) == 0 || err != nil || !speedFound || !linkDetectedFound {
		return 0, false, errors.New(fmt.Sprintf("Can't query network device %v_%v", hostname, device))
	} else {
		return speed, linkDetected, nil
	}
}

func getNetworkInfo(robot *environment.Robot, do_print bool, nicStatusMap map[string]bool) []*host_check.Alarm {
	var ret []*host_check.Alarm

	expect := map[string]int{}
	if robot.IsCommand() {
		expect["global"] = 1000
	} else if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRow {
		expect["global"] = 1000
		expect["targets"] = 10000
		expect["predict1"] = 1000
		expect["predict2"] = 1000
		expect["predict3"] = 1000
		expect["predict4"] = 1000
		expect["scannerboards"] = 1000
	} else if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleBud {
		expect["boards"] = 1000
		expect["targets"] = 10000
		expect["predict1"] = 5000
		expect["predict2"] = 5000
		expect["predict3"] = 5000
		expect["predict4"] = 5000
		expect["drivecams"] = 10000
	} else if robot.IsSimulator() {
		expect["eno1"] = 10000
		expect["eno2"] = 10000
	} else if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRowPrimary {
		expect["global"] = 1000
		expect["targets"] = 10000
		expect["predict1"] = 1000
		expect["predict2"] = 1000
		expect["scannerboards"] = 1000
	} else if environment.CarbonRole(robot.MakaRole) == environment.CarbonRoleRowSecondary {
		expect["global"] = 1000
		expect["targets"] = 10000
		expect["predict3"] = 1000
		expect["predict4"] = 1000
	} else if robot.IsModule() {
		expect["global"] = 1000
		expect["predict1"] = 2500
		expect["target1"] = 1000
		expect["scanner1"] = 100
		expect["target2"] = 1000
		expect["scanner2"] = 100
	}

	hostname := getRowName(robot)

	for deviceName, expect_speed := range expect {
		code := host_check.AlarmCode_A_NOALARM
		var desc string
		var parameters []*frontend.TranslationParameter

		name := fmt.Sprintf("%s_%s", hostname, deviceName)

		speed, linkDetected, err := getNetworkCardSpeed(hostname, deviceName)
		if do_print {
			fmt.Printf("Device %v speed %vMb/s, expected %vMb/s, link_detected: %v, err: %v\n", deviceName, speed, expect_speed, linkDetected, err)
		}
		if err != nil {
			code = host_check.AlarmCode_A_NETWORK_CHECKER_FAIL
			desc = err.Error()
			parameters = translation.Params(
				translation.StringParam("device_id", name),
			)
		} else {
			if expect_speed > speed {
				code = host_check.AlarmCode_A_NETWORK
				desc = fmt.Sprintf("Network device %v is running at lower speed than expected (%v Mb/s < %v Mb/s)", name, speed, expect_speed)
				parameters = translation.Params(
					translation.StringParam("device_id", name),
					translation.IntParam("speed", speed),
					translation.IntParam("expected_speed", expect_speed),
				)
			}
			if !linkDetected {
				prevLinkDetected := nicStatusMap[name]
				if prevLinkDetected {
					linkDetectedCounter.WithLabelValues(fmt.Sprint(getHostname(robot)), name).Inc()
				}
			}
			nicStatusMap[name] = linkDetected
		}

		if code != host_check.AlarmCode_A_NOALARM {
			ret = append(ret, &host_check.Alarm{Code: code, Subsystem: "Host/Network", Description: desc,
				Level: host_check.AlarmLevel_AL_High, Impact: host_check.AlarmImpact_AI_Degraded, Param: uniqueParams[deviceName], TranslationParameters: parameters})
		}
	}

	return ret
}

func getIPMIStatus() int {
	_, err := os.Stat("/dev/ipmi0")
	if err == nil {
		return 1
	}
	return 0
}

func (s *HostService) GetHostStatus(ctx context.Context, req *host_check.HostStatusRequest) (*host_check.HostStatus, error) {
	if s.robot.IsSimulator() {
		return &host_check.HostStatus{
			Status: host_check.HostStatusState_OK,
			Alarms: make([]*host_check.Alarm, 0),
			SystemInfo: &host_check.SysInfo{
				Hostname: getHostname(&s.robot),
				Serial:   s.serial,
			},
		}, nil
	}

	/* Refresh our metric comparisons */
	max_gpu_temp_C = int(s.max_gpu_temp_C_node.GetUIntValue())
	disk_space_warn_pct = int(s.disk_space_warn_pct_node.GetUIntValue())
	disk_space_error_pct = int(s.disk_space_error_pct_node.GetUIntValue())

	/* Query the host services */
	disk_alarms := getDiskInfo(&s.robot, false)
	network_alarms := getNetworkInfo(&s.robot, false, s.nicStatusMap)

	var gpu_alarms []*host_check.Alarm
	var ptp_alarms []*host_check.Alarm

	if environment.CarbonRole(s.robot.MakaRole) != environment.CarbonRoleCommand {
		gpu_alarms = getGPUInfo(&s.robot, false)
		ptp_alarms = getPTPInfo(false, &s.robot, s.ptpAlarmFilter)
		ipmiStatus := getIPMIStatus()
		ipmiStatusGauge.WithLabelValues(fmt.Sprint(getHostname(&s.robot))).Set(float64(ipmiStatus))
	}

	/* Pack the alerts into the response */
	var alarms []*host_check.Alarm = make([]*host_check.Alarm, 0)
	if disk_alarms != nil {
		alarms = append(alarms, disk_alarms...)
	}
	if network_alarms != nil {
		alarms = append(alarms, network_alarms...)
	}
	if gpu_alarms != nil {
		alarms = append(alarms, gpu_alarms...)
	}
	if ptp_alarms != nil {
		alarms = append(alarms, ptp_alarms...)
	}

	/* Return the response for sending over the grpc */
	return &host_check.HostStatus{
		Status: host_check.HostStatusState_OK, // XXX NO
		Alarms: alarms,
		SystemInfo: &host_check.SysInfo{
			Hostname: getHostname(&s.robot),
			Serial:   s.serial,
		},
	}, nil
}

func main() {

	parser := argparse.NewParser("host_check", "Validate basic carbon host sanity.",
		&argparse.ParserConfig{DisableDefaultShowHelp: true})

	service := parser.Flag("service", "service-mode", &argparse.Option{
		Help:     "Run in grpc service mode.",
		Required: false,
	})

	if e := parser.Parse(nil); e != nil {
		fmt.Println(e.Error())
		return
	}

	robot, err := environment.GetRobot()
	if err != nil {
		logrus.Errorf("Failed to parse environment: %v", err)
		return
	}

	if *service {

		config_subscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
		config_subscriber.AddConfigTree("host_check", fmt.Sprintf("%s/host_check", config.GetComputerConfigPrefix()), "services/host_check.yaml")
		config_subscriber.AddConfigTree("common", "common", "services/common.yaml")
		config_subscriber.Start()
		config_subscriber.WaitUntilReady()

		if config_subscriber.GetConfigNode("common", "environment").GetStringValue() == "production" {
			sentry_reporter.InitializeSentry()
			defer sentry_reporter.HandlePanic()
		}

		addr := fmt.Sprintf("0.0.0.0:%d", 6943)
		lis, err := net.Listen("tcp", addr)
		if err != nil {
			logrus.Fatalf("Failed to listen on addr %s: %v", addr, err)
		}

		server := grpc.NewServer(grpc.UnaryInterceptor(sentry_reporter.PanicInterceptor))
		service := &HostService{config_subscriber: config_subscriber,
			max_gpu_temp_C_node:       config_subscriber.GetConfigNode("host_check", "max_gpu_temp_C"),
			disk_space_warn_pct_node:  config_subscriber.GetConfigNode("host_check", "disk_space_warn_pct"),
			disk_space_error_pct_node: config_subscriber.GetConfigNode("host_check", "disk_space_error_pct"),
			robot:                     robot,
			ptpAlarmFilter: &PTPAlarmFilter{
				UseFilter:          true,
				FirstAlarmTime:     make(map[string]int64),
				WaitToAlarmTimeSec: config_subscriber.GetConfigNode("host_check", "ptp_alarm_wait_before_alert_time_sec"),
			},
			nicStatusMap: map[string]bool{
				"global":        true,
				"targets":       true,
				"predict1":      true,
				"predict2":      true,
				"predict3":      true,
				"predict4":      true,
				"scannerboards": true,
				"boards":        true,
				"drivecams":     true,
				"eno1":          true,
				"eno2":          false,
			},
			serial: getCmdLines("dmidecode", "-s", "system-serial-number")[0],
		}

		redisClient := redis.New(robot)
		_ = logging.NewLoggingService(server, redisClient, "host_check", &robot)

		host_check.RegisterHostServiceServer(server, service)

		metrics.Serve("", 62106)

		fmt.Printf("Serving grpc ...\n")
		if err := server.Serve(lis); err != nil {
			logrus.Fatalf("Failed to grpc serve: %v", err)
		}
		return
	} else {
		fmt.Println("disk info:")
		getDiskInfo(&robot, true)
		fmt.Println("network info:")
		getNetworkInfo(&robot, true, map[string]bool{})
		if environment.CarbonRole(robot.MakaRole) != environment.CarbonRoleCommand {
			fmt.Println()
			fmt.Println("PTP clock info:")
			getPTPInfo(true, &robot, &PTPAlarmFilter{UseFilter: false})
			fmt.Println()
			fmt.Println("GPU info:")
			getGPUInfo(&robot, true)
			fmt.Printf("\nIPMI Status: %v\n", getIPMIStatus())
		}
	}
}

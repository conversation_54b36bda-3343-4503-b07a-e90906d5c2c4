package services

import (
	"context"
	"time"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/docker"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/compose-spec/compose-go/v2/types"
	"github.com/docker/compose/v2/pkg/api"
	"github.com/docker/docker/client"
	"github.com/sirupsen/logrus"
)

type ServiceMaintainer struct {
	cli                *client.Client
	service            api.Service
	project            *types.Project
	services           []string
	stopCtx            context.Context
	autoRestartNode    *config.ConfigTree
	autoRestartAllNode *config.ConfigTree
	robotEnv           environment.Robot
	crashHandlers      map[string][]ContainerCrashHandler
	controls           bool // XXX remove when controls is removed and/or shmem dependency
}

func (m *ServiceMaintainer) addCrashHandler(containerName string, handler Container<PERSON><PERSON><PERSON><PERSON><PERSON>) {
	if handlers, ok := m.crashHandlers[containerName]; ok {
		handlers = append(handlers, handler)
		m.crashHandlers[containerName] = handlers
	} else {
		m.crashHandlers[containerName] = []ContainerCrashHandler{handler}
	}
}

func NewServiceMaintainer(stopCtx context.Context, config_subscriber *config.ConfigSubscriber, robotEnv environment.Robot) (*ServiceMaintainer, error) {
	m := &ServiceMaintainer{
		robotEnv:      robotEnv,
		crashHandlers: make(map[string][]ContainerCrashHandler),
	}

	m.autoRestartNode = config_subscriber.GetConfigNode("common", "software_manager/enable_auto_restart")
	m.autoRestartAllNode = config_subscriber.GetConfigNode("common", "software_manager/enable_auto_restart_all")
	m.stopCtx = stopCtx

	botDef, err := bot_context.LoadBotDef()
	if err != nil {
		return nil, err
	}

	m.cli, err = docker.GetDockerCLI()
	if err != nil {
		return nil, err
	}

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	if err != nil {
		return nil, err
	}

	m.services = make([]string, 0)
	for _, service := range role.Services {
		if service == "software_manager" {
			continue
		}
		if service == "controls" {
			m.controls = true
		}
		m.services = append(m.services, service)
	}

	m.service, err = docker.GetComposeService()
	if err != nil {
		return nil, err
	}

	m.project, err = bot_context.LoadComposeProject(botDef, role)
	if err != nil {
		return nil, err
	}

	for _, svc := range m.services {
		handler := NewLogCaptureCrashHandler(m.cli, m.robotEnv, svc)
		handler.CleanupCrashLogs()
		m.addCrashHandler(svc, handler)
	}
	m.addCrashHandler("prometheus", NewPrometheusCrashHandler(m.cli, m.robotEnv))

	return m, nil
}

func (m *ServiceMaintainer) RestartServices(refreshEnv bool) error {
	logrus.Infof("restarting ALL services: %v", m.services)
	err := docker.CleanupContainers(m.cli, m.services)
	if err != nil {
		return err
	}

	if refreshEnv {
		botDef, err := bot_context.LoadBotDef()
		if err != nil {
			return err
		}

		role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
		if err != nil {
			return err
		}

		m.project, err = bot_context.LoadComposeProject(botDef, role)
		if err != nil {
			return err
		}
	}

	err = docker.StartServices(m.cli, m.service, m.project, m.services)
	if err != nil {
		return err
	}

	return nil
}

func (m *ServiceMaintainer) RestartServicesFromList(services []string, refreshEnv bool) error {
	logrus.Infof("restarting SOME services: %v", services)
	err := docker.CleanupContainers(m.cli, services)
	if err != nil {
		return err
	}

	if refreshEnv {
		botDef, err := bot_context.LoadBotDef()
		if err != nil {
			return err
		}

		role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
		if err != nil {
			return err
		}

		m.project, err = bot_context.LoadComposeProject(botDef, role)
		if err != nil {
			return err
		}
	}

	err = docker.StartServices(m.cli, m.service, m.project, services)
	if err != nil {
		return err
	}

	return nil
}

func (m *ServiceMaintainer) RestartService(service string) error {
	var services []string = []string{service}

	logrus.Infof("restarting service: %v", services)
	err := docker.CleanupContainers(m.cli, services)
	if err != nil {
		return err
	}

	err = docker.StartServices(m.cli, m.service, m.project, services)
	if err != nil {
		return err
	}

	return nil
}

func (m *ServiceMaintainer) MaintainServicesAtInterval() {
	containerState := make(map[string]string)

	elapsed := time.Duration(0)
	for {
		timeToWait := time.Second - elapsed

		if timeToWait < 0 {
			timeToWait = 0
		}

		select {
		case <-m.stopCtx.Done():
			return
		case <-time.After(timeToWait):
		}

		start := time.Now()

		summary, err := docker.PsServices(m.cli, m.service, m.project, m.services)
		if err != nil {
			elapsed = 0
			continue
		}

		needRestart := false

		for _, container := range summary {
			stateChange := false
			if previousState, ok := containerState[container.Name]; ok {
				stateChange = container.State != previousState
			}
			containerState[container.Name] = container.State
			if container.State == "exited" && container.ExitCode != 0 {
				needRestart = true
				if stateChange { // only handle crashes when the crash is detected
					logrus.Warnf("%v crashed", container.Name)

					if handlers, ok := m.crashHandlers[container.Name]; ok {
						for _, handler := range handlers {
							logrus.Infof("invoking %q crash handler", container.Name)
							if err := handler.Handle(m.stopCtx); err != nil {
								logrus.Warnf("%s crash handler failed: %s", container.Name, err)
							}
						}
					}
				}
			}

			if needRestart && m.autoRestartNode.GetBoolValue() && !m.autoRestartAllNode.GetBoolValue() {
				needRestart = false
				if err := m.RestartService(container.Name); err != nil {
					logrus.Info("error restarting", container.Name, err)
					elapsed = 0
					continue
				}

			}
		}

		if needRestart && m.autoRestartAllNode.GetBoolValue() {
			if err := m.RestartServices(false); err != nil {
				elapsed = 0
				continue
			}
		}

		elapsed = time.Since(start)
	}
}

package services

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/docker/docker/api/types/container"
	"github.com/spf13/afero"
)

var (
	crashHandlerFs       = afero.NewOsFs()
	timeNow              = time.Now // test point
	promMagicNumberErrRe = regexp.MustCompile(`opening storage failed: (/prometheus/chunks_head/\d+): .+`)
	robotLogDir          = "logs"
	containerCrashLogFmt = "%s-crash-%d.log"   // containerName-crash-1675813129.log
	crashLogLifetime     = 30 * 24 * time.Hour // 1 month
)

type ContainerCrashHandler interface {
	Handle(ctx context.Context) error
}

type DockerCrashClient interface {
	ContainerLogs(ctx context.Context, container string, options container.LogsOptions) (io.ReadCloser, error)
}

type LogCaptureCrashHandler struct {
	robotEnv      environment.Robot
	containerName string
	dockerClient  DockerCrashClient
	logger        *logrus.Logger
}

func NewLogCaptureCrashHandler(dockerClient DockerCrashClient, robotEnv environment.Robot, containerName string) *LogCaptureCrashHandler {
	return &LogCaptureCrashHandler{
		robotEnv:      robotEnv,
		containerName: containerName,
		dockerClient:  dockerClient,
		logger:        logrus.New(),
	}
}

func (lch *LogCaptureCrashHandler) CleanupCrashLogs() {
	dirPath := filepath.Join(lch.robotEnv.MakaDataDir, robotLogDir)
	dirContents, err := afero.ReadDir(crashHandlerFs, dirPath)
	if err != nil {
		lch.logger.WithError(err).Error("Failed to read log directory")
	}
	for _, f := range dirContents {
		if f.IsDir() {
			continue
		}
		parts := strings.Split(f.Name(), "-")
		if len(parts) > 2 && parts[0] == lch.containerName && parts[1] == "crash" { // only care about our own crash logs
			epoch, err := strconv.Atoi(strings.TrimSuffix(parts[2], ".log"))
			if err != nil {
				continue
			}
			crashLogTime := time.Unix(int64(epoch), 0)
			if time.Since(crashLogTime) >= crashLogLifetime {
				if err := crashHandlerFs.Remove(filepath.Join(dirPath, f.Name())); err != nil {
					lch.logger.WithError(err).Warnln("Failed to remove expired crash log:", f.Name())
				}
			}
		}
	}
}

func (lch *LogCaptureCrashHandler) Handle(ctx context.Context) error {
	containerCrashCounterVec.WithLabelValues(lch.containerName).Inc()
	sentry_reporter.CaptureMessage(fmt.Sprintf("%s container crashed", lch.containerName))
	logReader, err := lch.dockerClient.ContainerLogs(ctx, lch.containerName, container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Details:    true,
		Timestamps: true,
	})
	if err != nil {
		return fmt.Errorf("failed to get %q container logs: %w", lch.containerName, err)
	}
	defer logReader.Close()
	logFileName := filepath.Join(lch.robotEnv.MakaDataDir, robotLogDir, fmt.Sprintf(containerCrashLogFmt, lch.containerName, timeNow().Unix()))
	f, err := crashHandlerFs.Create(logFileName)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = io.Copy(f, logReader)
	return err
}

type PrometheusCrashHandler struct {
	robotEnv      environment.Robot
	containerName string
	dockerClient  DockerCrashClient
}

func NewPrometheusCrashHandler(dockerClient DockerCrashClient, robotEnv environment.Robot) *PrometheusCrashHandler {
	return &PrometheusCrashHandler{
		robotEnv:      robotEnv,
		containerName: "prometheus",
		dockerClient:  dockerClient,
	}
}

func (cch *PrometheusCrashHandler) Handle(ctx context.Context) error {
	logs, err := cch.dockerClient.ContainerLogs(ctx, cch.containerName, container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Tail:       strconv.Itoa(1),
	})
	if err != nil {
		return fmt.Errorf("failed to get %q container logs: %w", cch.containerName, err)
	}
	defer logs.Close()
	logBytes, err := ioutil.ReadAll(logs)
	if err != nil {
		return fmt.Errorf("error: %w", err)
	}
	results := promMagicNumberErrRe.FindAllStringSubmatch(string(logBytes), 1)
	brokenChunks := make([]string, 0)
	if len(results) > 0 {
		if len(results[0]) > 1 {
			brokenChunks = append(brokenChunks, results[0][1])
		}
	}

	for _, chunk := range brokenChunks {
		bc := filepath.Join(cch.robotEnv.MakaDataDir, chunk)
		if exists, _ := afero.Exists(crashHandlerFs, bc); exists {
			if err := crashHandlerFs.Remove(bc); err != nil {
				return fmt.Errorf("%s - failed to remove broken chunk: %s - %w", cch.containerName, bc, err)
			}
		}
	}
	return nil
}

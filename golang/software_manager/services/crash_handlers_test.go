package services

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/software_manager/services/mocks"
	"github.com/spf13/afero"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func Test_PrometheusCrashHandler(t *testing.T) {
	saveCrashHandlerFs := crashHandlerFs
	defer func() { crashHandlerFs = saveCrashHandlerFs }()

	tests := []struct {
		name                 string
		lastLogLine          string
		expectedChunkDeleted string
		logRetrievalErr      error
	}{
		{
			"success",
			`ts=2022-06-07T16:12:56.308Z caller=main.go:1097 level=error err="opening storage failed: /prometheus/chunks_head/000007: invalid magic number 0`,
			"/data/prometheus/chunks_head/000007",
			nil,
		},
		{
			"other case",
			`ts=2022-07-12T23:26:40.069Z caller=main.go:1097 level=error err="opening storage failed: /prometheus/chunks_head/000004: invalid head chunk file header: invalid size`,
			"/data/prometheus/chunks_head/000004",
			nil,
		},
		{
			"error",
			`some log line`,
			"/data/prometheus/chunks_head/000007",
			assert.AnError,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			crashHandlerFs = afero.NewMemMapFs()
			dir, _ := filepath.Split(test.expectedChunkDeleted)
			if err := crashHandlerFs.MkdirAll(dir, 0777); err != nil {
				t.Fatal(err)
			}
			if err := afero.WriteFile(crashHandlerFs, test.expectedChunkDeleted, []byte("blah blah"), 0777); err != nil {
				t.Fatal(err)
			}

			mockDocker := new(mocks.DockerCrashClient)
			mockDocker.On("ContainerLogs", mock.Anything, "prometheus", mock.AnythingOfType("container.LogsOptions")).
				Return(io.NopCloser(bytes.NewReader([]byte(test.lastLogLine))), test.logRetrievalErr)

			handler := NewPrometheusCrashHandler(mockDocker, environment.Robot{MakaDataDir: "/data"})
			err := handler.Handle(context.TODO())
			if test.logRetrievalErr != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if exists, err := afero.Exists(crashHandlerFs, test.expectedChunkDeleted); err != nil {
					t.Fatal(err)
				} else {
					assert.False(t, exists)
				}
			}
		})
	}
}

func TestNewLogCaptureCrashHandler(t *testing.T) {
	saveCrashHandlerFs := crashHandlerFs
	saveTimeNow := timeNow
	defer func() {
		crashHandlerFs = saveCrashHandlerFs
		timeNow = saveTimeNow
	}()

	testContainerName := "containerXyZ"
	testTime := time.Now()

	tests := []struct {
		name            string
		dockerLogs      []byte
		logRetrievalErr error
		expectError     bool
	}{
		{
			"happy path",
			[]byte("some logs\n blah blah\n crash\n"),
			nil,
			false,
		},
		{
			"docker log error",
			nil,
			assert.AnError,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			crashHandlerFs = afero.NewMemMapFs()
			timeNow = func() time.Time {
				return testTime
			}

			mockDocker := new(mocks.DockerCrashClient)
			mockDocker.On("ContainerLogs", mock.Anything, testContainerName, mock.AnythingOfType("container.LogsOptions")).
				Return(io.NopCloser(bytes.NewReader(test.dockerLogs)), test.logRetrievalErr)

			env := environment.Robot{
				MakaDataDir: "/data",
				MakaLogDir:  "", // ?
			}
			handler := NewLogCaptureCrashHandler(mockDocker, env, testContainerName)
			err := handler.Handle(context.TODO())
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				expectedFilename := filepath.Join(env.MakaDataDir, robotLogDir, fmt.Sprintf(containerCrashLogFmt, testContainerName, testTime.Unix()))
				exist, err := afero.Exists(crashHandlerFs, expectedFilename)
				if err != nil {
					t.Fatal(err)
				}
				assert.True(t, exist)
				fileData, err := afero.ReadFile(crashHandlerFs, expectedFilename)
				if err != nil {
					t.Fatal(err)
				}
				assert.Equal(t, test.dockerLogs, fileData)
			}
		})
	}
}

func TestLogCaptureCrashHandler_CleanupCrashLogs(t *testing.T) {
	saveCrashHandlerFs := crashHandlerFs
	saveTimeNow := timeNow
	defer func() {
		crashHandlerFs = saveCrashHandlerFs
		timeNow = saveTimeNow
	}()

	testContainerName := "testCN"
	testTime := time.Now()

	tests := []struct {
		name          string
		existingFiles []string
		expectedFiles []string
	}{
		{"only delete expired",
			[]string{
				"/data/logs/some.log",
				fmt.Sprintf("/data/logs/%s-crash-%d.log", testContainerName, testTime.Unix()),
				fmt.Sprintf("/data/logs/%s-crash-%d.log", testContainerName, testTime.Add(-crashLogLifetime).Unix()),
				"/data/logs/testCN-crash-1724039345.log",
			},
			[]string{
				fmt.Sprintf("/data/logs/%s-crash-%d.log", testContainerName, testTime.Unix()),
				"/data/logs/some.log",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			crashHandlerFs = afero.NewMemMapFs()
			timeNow = func() time.Time {
				return testTime
			}

			for _, file := range test.existingFiles {
				if err := afero.WriteFile(crashHandlerFs, file, []byte("blah blah"), os.ModePerm); err != nil {
					t.Fatal(err)
				}
			}

			mockDocker := new(mocks.DockerCrashClient)

			env := environment.Robot{
				MakaDataDir: "/data",
				MakaLogDir:  "", // ?
			}
			handler := NewLogCaptureCrashHandler(mockDocker, env, testContainerName)
			handler.CleanupCrashLogs()
			for _, file := range test.expectedFiles {
				exist, err := afero.Exists(crashHandlerFs, file)
				if err != nil {
					t.Fatal(err)
				}
				assert.True(t, exist)
			}
		})
	}
}

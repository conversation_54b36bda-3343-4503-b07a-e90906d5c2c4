// Code generated by mockery v2.13.1. DO NOT EDIT.

package mocks

import (
	context "context"
	io "io"

	container_pkg "github.com/docker/docker/api/types/container"
	mock "github.com/stretchr/testify/mock"
)

// DockerCrashClient is an autogenerated mock type for the DockerCrashClient type
type DockerCrashClient struct {
	mock.Mock
}

// ContainerLogs provides a mock function with given fields: ctx, container, options
func (_m *DockerCrashClient) ContainerLogs(ctx context.Context, container string, options container_pkg.LogsOptions) (io.ReadCloser, error) {
	ret := _m.Called(ctx, container, options)

	var r0 io.ReadCloser
	if rf, ok := ret.Get(0).(func(context.Context, string, container_pkg.LogsOptions) io.ReadCloser); ok {
		r0 = rf(ctx, container, options)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(io.ReadCloser)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, string, container_pkg.LogsOptions) error); ok {
		r1 = rf(ctx, container, options)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewDockerCrashClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewDockerCrashClient creates a new instance of DockerCrashClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewDockerCrashClient(t mockConstructorTestingTNewDockerCrashClient) *DockerCrashClient {
	mock := &DockerCrashClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

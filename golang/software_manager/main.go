package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/docker"
	"github.com/carbonrobotics/robot/golang/generated/proto/software_manager"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/portal_clients"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/remoteit"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/carbonrobotics/robot/golang/software_manager/host"
	"github.com/carbonrobotics/robot/golang/software_manager/registry"
	"github.com/carbonrobotics/robot/golang/software_manager/robot_definition"
	"github.com/carbonrobotics/robot/golang/software_manager/service"
	"github.com/carbonrobotics/robot/golang/software_manager/services"
	"github.com/carbonrobotics/robot/golang/software_manager/system"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

const (
	remoteItConfigJSONPath = "/remoteit/config.json"
)

func main() {
	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)

	logrus.Info("Starting Software Manager...")
	metrics.Serve("", 62006)

	env, err := environment.GetRobot()
	if err != nil {
		logrus.WithError(err).Fatal("Failed to Get Robot Environment")
	}

	currentSystemVersion, err := bot_context.GetSystemVersion()
	if err != nil {
		logrus.WithError(err).Error("Failed to get current system version")
	} else {
		softwareVersion, err := bot_context.LoadVersionDef(true, currentSystemVersion)
		if err != nil {
			logrus.WithError(err).Error("Failed to get current software version")
		} else {
			if env.CarbonVersionTag != softwareVersion.Tag {
				logrus.Errorf("Software manager version %v isn't equal to %v required by system version %v, will not commit current version",
					env.CarbonVersionTag, softwareVersion, currentSystemVersion)
			} else {
				for {
					err := system.CommitCurrentVersion(&env)
					if err != nil {
						logrus.WithError(err).Error("Failed to commit current system version... Retrying... in ", 15, " seconds")
						time.Sleep(time.Second * 15)
					} else {
						break
					}
				}
			}
		}
	}

	flag.Parse() // No Flags For Now

	err = host.PingHostActor()
	if err != nil {
		if env.IsUnmanagedSystem() {
			logrus.WithError(err).Info("Failed to Ping Host Actor")
		} else {
			logrus.WithError(err).Error("Failed to Ping Host Actor")
		}
	} else {
		logrus.Info("Successfully Ping Host Actor")
	}

	cli, err := docker.GetDockerCLI()

	if err != nil {
		logrus.WithError(err).Fatal("Failed to Get Docker CLI")
	}

	configSubscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	configSubscriber.AddConfigTree("common", "common", "services/common.yaml")
	configSubscriber.Start()
	configSubscriber.WaitUntilReady()
	configClient := config.NewConfigClient(config.MakeRobotLocalAddr(61001))

	if configSubscriber.GetConfigNode("common", "environment").GetStringValue() == "production" {
		sentry_reporter.InitializeSentry()
		defer sentry_reporter.HandlePanic()
	}

	tokenProxyIp := "*********"
	if env.IsBud() || env.IsRtc() || env.IsSimulator() {
		tokenProxyIp = "127.0.0.1"
	}

	a0cfg := auth.NewRobotAuth0Config(env, env.MakaAuthScopes, fmt.Sprintf("http://%v:61900/oauth/token", tokenProxyIp))
	tokenSource := a0cfg.TokenSource(context.Background())
	healthClient := portal_clients.NewPortalClient(env.CarbonPortalHost, tokenSource)

	deviceName, err := host.UpdateHostname(env)
	if err != nil {
		logrus.WithError(err).Warn("failed to update /etc/hostname")
		logrus.Warn("skipping remoteit configuration")

		// only update remote it for production robots
		// ignore reaper modules
	} else if env.IsProduction() {
		if err := host.SetHostname(deviceName); err != nil {
			logrus.WithError(err).Warn("failed to set hostname via host actor")
		}
		if !env.IsModule() {
			ritCfg, err := remoteit.ParseConfig(remoteItConfigJSONPath)
			if err != nil {
				logrus.WithError(err).Warn("failed to parse remote.it config:", remoteItConfigJSONPath)
			} else {
				logrus.Infof("Configuring remote.it device: %s with name %s", ritCfg.Device.ID, deviceName)
				if res, err := healthClient.RemoteItConfigure(ritCfg.Device.ID, deviceName); err != nil {
					logrus.WithError(err).Warn("failed to update remote.it configuration")
				} else {
					logrus.Infoln("update remote.it config result:", res.Status)
				}
			}
		}
	}

	// Setting Bud as Registry Host also for more testing
	isRegistryHost := environment.CarbonRole(env.MakaRole) == environment.CarbonRoleCommand || environment.CarbonRole(env.MakaRole) == environment.CarbonRoleBud || env.IsSimulator()
	isSWMgrClient := !isRegistryHost && !env.IsRtc()
	var registryClient *registry.RegistryClient = nil
	var softwareManagerClient *software_manager_client.SoftwareManagerClient

	redisClient := redis.New(env)

	if isRegistryHost {
		registryClient = registry.NewRegistryClient("http://127.0.0.1:5000")
	} else if isSWMgrClient {
		softwareManagerClient = software_manager_client.NewSoftwareManagerClient("*********:61005")
	}

	versionMaintainer, err := registry.NewVersionMaintainer(stopCtx, &env, cli, redisClient, configSubscriber, configClient, registryClient, softwareManagerClient, tokenSource)
	if err != nil {
		logrus.WithError(err).Fatal("Failed to Create Version Maintainer")
	}

	serviceMaintainer, err := services.NewServiceMaintainer(stopCtx, configSubscriber, env)
	if err != nil {
		logrus.WithError(err).Fatal("Failed to Create Service Maintainer")
	}

	definitionMaintainer, err := robot_definition.NewDefinitionMaintainer(stopCtx, redisClient, serviceMaintainer, &env)
	if err != nil {
		logrus.WithError(err).Fatal("Failed to Create Definition Maintainer")
	}

	if softwareManagerClient != nil {
		var identity *software_manager.IdentityInfo
		for {
			identity, err = softwareManagerClient.GetIdentity()

			if err != nil {
				logrus.WithError(err).Warn("Waiting for Connection to Master Software Manager...")
				continue
			}

			break
		}

		robotDef := bot_context.RobotDef{
			Name:       identity.Name,
			Generation: identity.Generation,
			Computer: bot_context.ComputerDef{
				Role:       identity.Role.Role,
				Row:        identity.Role.Row,
				ExtraRoles: identity.Role.ExtraRoles,
			},
			AuthClientId:        identity.AuthClientId,
			AuthClientSecret:    identity.AuthClientSecret,
			AuthDomain:          identity.AuthDomain,
			Environment:         identity.Environment,
			CarbonRobotUsername: identity.CarbonRobotUsername,
			CarbonRobotPassword: identity.CarbonRobotPassword,
		}

		identityChanged, err := robotDef.OverwriteRobotIdentityIfNeeded()

		if err != nil {
			logrus.WithError(err).Error("Failed to Overwrite Robot Identity")
		} else if identityChanged {
			logrus.Info("Synchronizing Identity")

			// Since we updated the identity we need to restart all services so they get
			// Injected with the proper environment
			// We restart all services except software manager
			serviceMaintainer.RestartServices(true)

			// We exit software manager and docker restarts it
			os.Exit(0)
		}
	}

	if env.IsCommand() && env.IsReaper() {
		versionMaintainer.CleanOldModuleSerialKeys()
	}

	wg := sync.WaitGroup{}
	wg.Add(1)

	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		versionMaintainer.MaintainVersionsAtInterval()
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		versionMaintainer.MaintainVersionSummaryAtInterval()
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		serviceMaintainer.MaintainServicesAtInterval()
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		definitionMaintainer.MaintainDefinitionAtInterval() // exits immediately if not reaper
	}()

	// Open TCP Port
	addr := fmt.Sprintf("0.0.0.0:%d", 61005)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.Fatalf("failed to listen: %v", err)
	}

	// Create GRPC Server
	var opts []grpc.ServerOption
	opts = append(opts, grpc.UnaryInterceptor(sentry_reporter.PanicInterceptor))
	grpcServer := grpc.NewServer(opts...)

	_ = logging.NewLoggingService(grpcServer, redisClient, "software_manager", &env)
	_ = service.NewSoftwareManagerService(grpcServer, versionMaintainer, definitionMaintainer)

	// Serve Requests Forever
	logrus.Infof("Started Software Manager Server at: %s", addr)
	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil {
			logrus.Fatalf("Failed to Serve GRPC Server: %v", err)
		}
	}()

	<-stopCtx.Done()
	wg.Wait()

	grpcServer.Stop()
}

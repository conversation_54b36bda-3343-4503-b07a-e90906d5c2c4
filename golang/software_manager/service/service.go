package service

import (
	"context"
	"os"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/generated/proto/software_manager"
	"github.com/carbonrobotics/robot/golang/software_manager/meta"
	"github.com/carbonrobotics/robot/golang/software_manager/registry"
	"github.com/carbonrobotics/robot/golang/software_manager/robot_definition"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type SoftwareManagerService struct {
	software_manager.UnimplementedSoftwareManagerServiceServer
	maintainer           *registry.VersionMaintainer
	definitionMaintainer *robot_definition.DefinitionMaintainer
}

func NewSoftwareManagerService(grpcServer *grpc.Server, maintainer *registry.VersionMaintainer, definitionMaintainer *robot_definition.DefinitionMaintainer) *SoftwareManagerService {
	service := &SoftwareManagerService{
		maintainer:           maintainer,
		definitionMaintainer: definitionMaintainer,
	}
	software_manager.RegisterSoftwareManagerServiceServer(grpcServer, service)
	return service
}

func (s *SoftwareManagerService) GetSoftwareVersionMetadata(ctx context.Context, req *software_manager.SoftwareVersionMetadataRequest) (*software_manager.SoftwareVersionMetadata, error) {
	metadata, err := meta.LoadSoftwareVersionMetadata(s.maintainer.GetEnv(), req.Tag)

	if err != nil {
		return nil, err
	}

	retval := &software_manager.SoftwareVersionMetadata{
		Tag:        metadata.Tag,
		Containers: metadata.Containers,
		System:     metadata.System,
	}
	return retval, nil
}

func (s *SoftwareManagerService) GetVersionsSummary(context.Context, *software_manager.VersionSummaryRequest) (*software_manager.VersionSummaryReply, error) {
	reply := &software_manager.VersionSummaryReply{
		Current: &software_manager.SoftwareVersionMetadata{
			Tag:       "",
			Available: false,
			Ready:     false,
		},
		Target: &software_manager.SoftwareVersionMetadata{
			Tag:       "",
			Available: false,
			Ready:     false,
		},
		Previous: &software_manager.SoftwareVersionMetadata{
			Tag:       "",
			Available: false,
			Ready:     false,
		},
		Updating: false,
	}
	currentMetadata, currentReady, currentErr := s.maintainer.GetCurrentVersionMetadata()
	targetMetadata, targetReady, targetErr := s.maintainer.GetTargetVersionMetadata()
	previousMetadata, previousReady, previousErr := s.maintainer.GetPreviousVersionMetadata()

	if currentErr != nil || currentMetadata == nil {
		logrus.WithError(currentErr).Debug("Error Getting Current Version Metadata")
	} else {
		reply.Current.Available = true
		reply.Current.Tag = currentMetadata.Tag
		reply.Current.Ready = currentReady
	}

	if targetErr != nil || targetMetadata == nil {
		logrus.WithError(targetErr).Debug("Error Getting Target Version Metadata")
	} else {
		reply.Target.Available = true
		reply.Target.Tag = targetMetadata.Tag
		reply.Target.Ready = targetReady
	}

	if previousErr != nil || previousMetadata == nil {
		logrus.WithError(previousErr).Debug("Error Getting Previous Version Metadata")
	} else {
		reply.Previous.Available = true
		reply.Previous.Tag = previousMetadata.Tag
		reply.Previous.Ready = previousReady
	}

	reply.Updating = s.maintainer.IsUpdating()

	return reply, nil
}

func (s *SoftwareManagerService) PrepareUpdate(ctx context.Context, req *software_manager.PrepareUpdateRequest) (*software_manager.PrepareUpdateResponse, error) {
	resp := &software_manager.PrepareUpdateResponse{}

	err := s.maintainer.PrepareUpdate(ctx, req.Tag, req.ReqId)

	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to prepare update: %v", err)
	}

	return resp, nil
}

func (s *SoftwareManagerService) AbortUpdate(ctx context.Context, req *software_manager.AbortUpdateRequest) (*software_manager.AbortUpdateResponse, error) {
	resp := &software_manager.AbortUpdateResponse{}

	err := s.maintainer.AbortUpdate(req.Tag, req.ReqId)

	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to abort update: %v", err)
	}

	return resp, nil
}

func (s *SoftwareManagerService) TriggerUpdate(ctx context.Context, req *software_manager.TriggerUpdateRequest) (*software_manager.TriggerUpdateReply, error) {
	reply := &software_manager.TriggerUpdateReply{}

	err := s.maintainer.TriggerUpdate(req.Tag, req.ReqId)

	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to trigger update: %s, err: %v", req.Tag, err)
	}

	return reply, nil
}

func (s *SoftwareManagerService) Reboot(ctx context.Context, req *software_manager.Empty) (*software_manager.Empty, error) {
	resp := &software_manager.Empty{}

	err := s.maintainer.Reboot()

	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to reboot, err: %v", err)
	}

	return resp, nil
}

func (s *SoftwareManagerService) GetIdentity(ctx context.Context, req *software_manager.GetIdentityRequest) (*software_manager.IdentityInfo, error) {
	reply := &software_manager.IdentityInfo{}
	robotDef, err := bot_context.LoadRobotDef()

	if err != nil {
		return nil, status.Errorf(codes.FailedPrecondition, "Failed to read Robot Def %s", err)
	}

	reply.Name = robotDef.Name
	reply.Generation = robotDef.Generation
	reply.Role = &software_manager.ComputerRole{
		Role:       robotDef.Computer.Role,
		Row:        robotDef.Computer.Row,
		ExtraRoles: robotDef.Computer.ExtraRoles,
	}
	reply.AuthClientId = robotDef.AuthClientId
	reply.AuthClientSecret = robotDef.AuthClientSecret
	reply.AuthDomain = robotDef.AuthDomain
	reply.CarbonRobotUsername = robotDef.CarbonRobotUsername
	reply.CarbonRobotPassword = robotDef.CarbonRobotPassword

	return reply, nil
}

func (s *SoftwareManagerService) ClearPackagesCache(context.Context, *software_manager.ClearPackagesCacheRequest) (*software_manager.ClearPackagesCacheResponse, error) {
	err := os.RemoveAll("/apt_archives/carbon")
	if err != nil {
		logrus.WithError(err).Warn("Could not remove apt_cacher_ng cache at /data/archives/carbon, packages may be reported as corrupted. Please remove the folder manually in this case.")
		return nil, err
	}

	return &software_manager.ClearPackagesCacheResponse{}, nil
}

func (s *SoftwareManagerService) PushRobotDefinitionUpdate(ctx context.Context, req *software_manager.Empty) (*software_manager.Empty, error) {
	s.definitionMaintainer.PushTrigger()
	return &software_manager.Empty{}, nil
}

func (s *SoftwareManagerService) RestartDependentServices(ctx context.Context, req *software_manager.Empty) (*software_manager.Empty, error) {
	s.definitionMaintainer.RestartTrigger()
	return &software_manager.Empty{}, nil
}

func (s *SoftwareManagerService) Ping(ctx context.Context, req *software_manager.Empty) (*software_manager.Empty, error) {
	return &software_manager.Empty{}, nil
}

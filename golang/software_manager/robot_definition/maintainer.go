package robot_definition

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/module_server_client"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_definition"
	"github.com/carbonrobotics/robot/golang/software_manager/host"
	"github.com/carbonrobotics/robot/golang/software_manager/services"
	"github.com/sirupsen/logrus"
)

type DefinitionMaintainer struct {
	logger             *logrus.Entry
	redisClient        *redis.Client
	moduleServerClient *module_server_client.ModuleServerClient
	serviceMaintainer  *services.ServiceMaintainer
	stopCtx            context.Context
	pushTriggerCh      chan struct{}
	restartTriggerCh   chan struct{}
	moduleID           uint32
	currentDef         *robot_definition.RobotDefinition
	currentEnv         *environment.Robot
}

const (
	moduleServerAddress = "127.0.0.1:61016"

	maintainerInterval = 60 * time.Second
)

// Services that require restart when the module id, row id or definition changes
// i.e. services that use config or environment variables
var DependentServices map[environment.CarbonRole][]string = map[environment.CarbonRole][]string{
	environment.CarbonRoleCommand: {"commander", "data_upload_manager", "hardware_manager", "metrics_aggregator"},
	environment.CarbonRoleModule:  {"aimbot", "cv", "broadcaster", "host_check", "model_receiver"},
}

func NewDefinitionMaintainer(stopCtx context.Context, redisClient *redis.Client, serviceMaintainer *services.ServiceMaintainer, env *environment.Robot) (*DefinitionMaintainer, error) {
	if !environment.IsReaper() {
		// no op
		return &DefinitionMaintainer{}, nil
	}

	maintainer := &DefinitionMaintainer{
		logger:             logrus.WithField("component", "DefinitionMaintainer"),
		redisClient:        redisClient,
		moduleServerClient: module_server_client.NewModuleServerClient(moduleServerAddress),
		serviceMaintainer:  serviceMaintainer,
		stopCtx:            stopCtx,
		pushTriggerCh:      make(chan struct{}, 1),
		restartTriggerCh:   make(chan struct{}, 1),
		moduleID:           0,
		currentDef:         &robot_definition.RobotDefinition{},
		currentEnv:         env,
	}

	err := maintainer.init()
	if err != nil {
		return nil, err
	}
	return maintainer, nil
}

func (s *DefinitionMaintainer) init() error {
	s.logger.Info("Initializing definition maintainer")
	def, err := s.readCurrentDefFromRedis()
	if err != nil {
		return err
	}
	s.currentDef = def

	if environment.IsModule() {
		// this is the module id that was in module_identity.json at the time of swmg boot
		s.moduleID = s.moduleIDFromEnv()
		changed, err := s.setRowForModuleID()
		if err != nil {
			return err
		} else if changed {
			err := s.RestartDependentServices()
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (s *DefinitionMaintainer) PushTrigger() {
	select {
	case s.pushTriggerCh <- struct{}{}:
	default:
	}
}

func (s *DefinitionMaintainer) RestartTrigger() {
	select {
	case s.restartTriggerCh <- struct{}{}:
	default:
	}
}

func (s *DefinitionMaintainer) setRowForModuleID() (bool, error) {
	s.logger.Infof("Setting row for module id %d", s.moduleID)
	rowDef := s.currentDef.GetRowForModule(s.moduleID)
	newRowStr := "0"
	if rowDef != nil {
		newRowStr = strconv.Itoa(int(rowDef.RowId))
	}

	changed, err := bot_context.OverwriteRowIfNeeded(newRowStr)
	if err != nil {
		return false, err
	}
	if changed {
		s.logger.Infof("Update: Row is now %s", newRowStr)
		return true, nil
	} else {
		s.logger.Infof("Row is already set to %s", newRowStr)
	}
	return false, nil
}

func (s *DefinitionMaintainer) MaintainDefinition() {
	s.logger.Info("Maintaining definition")
	newDef, err := s.readCurrentDefFromRedis()
	if err != nil {
		s.logger.WithError(err).Error("failed to read current robot definition from redis")
		return
	}
	s.logger.Infof("Received definition number: %v", newDef.Number)
	defChanged := s.currentDef.Number != newDef.Number

	newID := s.moduleID
	newSerial := ""
	if environment.IsModule() {
		newID, newSerial, err = s.moduleServerClient.GetModuleIdentity()
		if err != nil {
			s.logger.WithError(err).Error("failed to get module identity from module server")
			return
		}
		idStr := strconv.Itoa(int(newID))
		if idStr != s.currentEnv.CarbonModuleID || newSerial != s.currentEnv.CarbonModuleSerial {
			s.logger.Infof("Update: module id is %s, serial is %s", idStr, newSerial)
			s.currentEnv.CarbonModuleID = idStr
			s.currentEnv.CarbonModuleSerial = newSerial
			deviceName, err := host.UpdateHostname(*s.currentEnv)
			if err != nil {
				s.logger.WithError(err).Error("failed to update /etc/hostname")
			} else {
				err := host.SetHostname(deviceName)
				if err != nil {
					// todo(jfroel): figure out how to retry this later, not immediately necessary
					s.logger.WithError(err).Error("failed to set hostname via host actor")
				}
			}
		}
	}
	moduleChanged := s.moduleID != newID && environment.IsModule() // only relevant for modules, command ignores

	oldID := s.moduleID
	oldDef := s.currentDef

	s.moduleID = newID
	s.currentDef = newDef

	var changeErr error
	defer func() {
		if changeErr != nil {
			// revert back to old values, so that we can try again
			s.moduleID = oldID
			s.currentDef = oldDef
		}
	}()

	rowChanged := false
	if environment.IsModule() {
		rowChanged, changeErr = s.setRowForModuleID()
		if changeErr != nil {
			s.logger.WithError(changeErr).Error("Failed to set row for module id")
			return
		}
	}

	s.logger.Infof("Definition changed: %v, module changed: %v, row changed: %v", defChanged, moduleChanged, rowChanged)

	if moduleChanged || rowChanged || defChanged {
		changeErr = s.RestartDependentServices()
		if changeErr != nil {
			s.logger.WithError(changeErr).Error("Failed to restart services")
			return
		}
	}

	// todo(jfroel): remote it change for module id
}

func (s *DefinitionMaintainer) MaintainDefinitionAtInterval() {
	if environment.IsReaper() {
		for {
			select {
			case <-s.stopCtx.Done():
				return
			case <-time.After(maintainerInterval):
				s.MaintainDefinition()
			case <-s.pushTriggerCh:
				s.MaintainDefinition()
			case <-s.restartTriggerCh:
				err := s.RestartDependentServices()
				if err != nil {
					s.logger.WithError(err).Error("Failed to restart dependent services")
				}
			}
		}
	}
}

func (s *DefinitionMaintainer) readCurrentDefFromRedis() (*robot_definition.RobotDefinition, error) {
	defExists, err := robot_definition.CurrentDefExistsInRedis(s.redisClient)
	if err != nil {
		return nil, err
	}
	if !defExists {
		return &robot_definition.RobotDefinition{}, nil
	}
	return robot_definition.ReadCurrentDefFromRedis(s.redisClient)
}

func (s *DefinitionMaintainer) moduleIDFromEnv() uint32 {
	moduleIDStr := s.currentEnv.CarbonModuleID
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		s.logger.WithError(err).Errorf("Failed to parse module id %s", moduleIDStr)
		return 0
	}
	return uint32(moduleID)
}

func (s *DefinitionMaintainer) RestartDependentServices() error {
	role := environment.GetCarbonRole()
	servicesToRestart, ok := DependentServices[role]
	if !ok {
		return fmt.Errorf("no dependent services for role %s", role)
	}
	err := s.serviceMaintainer.RestartServicesFromList(servicesToRestart, true)
	return err
}

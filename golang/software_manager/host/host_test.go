package host

import (
	"testing"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/spf13/afero"
	"github.com/stretchr/testify/assert"
)

func TestUpdateHostname(t *testing.T) {
	saveAppFS := appFS
	defer func() { appFS = saveAppFS }()

	tests := []struct {
		name                 string
		env                  environment.Robot
		readOnlyFS           bool
		expectedDeviceName   string
		expectHostnameUpdate bool
		expectError          bool
	}{
		{
			"happy path write",
			environment.Robot{
				MakaRobotName: "slayer1",
				Maka<PERSON><PERSON>:      "row",
				MakaRow:       "1",
			},
			false,
			"slayer1-row-1",
			true,
			false,
		},
		{
			"happy path bud",
			environment.Robot{
				MakaRobotName: "bud1",
				Maka<PERSON><PERSON>:      "bud",
				MakaRow:       "0",
			},
			false,
			"bud1",
			true,
			false,
		},
		{
			"happy path command",
			environment.Robot{
				MakaRobotName: "slayer1",
				Maka<PERSON><PERSON>:      "command",
				MakaRow:       "0",
			},
			false,
			"slayer1-command",
			true,
			false,
		},
		{
			"happy path simulator",
			environment.Robot{
				MakaRobotName: "simulator1",
				Ma<PERSON><PERSON><PERSON>:      "simulator",
				MakaRow:       "0",
			},
			false,
			"simulator1",
			false,
			false,
		},
		{
			"write fails",
			environment.Robot{
				MakaRobotName: "slayer1",
				MakaRole:      "command",
				MakaRow:       "0",
			},
			true,
			"",
			true,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			appFS = afero.NewMemMapFs()
			if test.readOnlyFS {
				appFS = afero.NewReadOnlyFs(appFS)
			}

			gotDevice, err := UpdateHostname(test.env)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedDeviceName, gotDevice)
				if test.expectHostnameUpdate {
					byt, err := afero.ReadFile(appFS, hostnameFilepath)
					if err != nil {
						t.Fatal(err)
					}
					assert.Equal(t, test.expectedDeviceName, string(byt))
				}
			}
		})
	}
}

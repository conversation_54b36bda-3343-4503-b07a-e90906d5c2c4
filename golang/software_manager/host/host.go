package host

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os/exec"
	"strings"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

var (
	// test helpers
	appFS = afero.NewOsFs()
)

const hostnameFilepath = "/etc/hostname"

type HostActorResponse struct {
	Result string `json:"result"`
}

type HostActorRequest struct {
	Command string `json:"command"`
}

func PingHostActor() error {
	reqData := &HostActorRequest{
		Command: "ping",
	}

	jsonData, err := json.MarshalIndent(reqData, "", "  ")

	if err != nil {
		return err
	}

	request, err := http.NewRequest("POST", "http://127.0.0.1:61008", bytes.NewBuffer(jsonData))

	if err != nil {
		return err
	}

	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	client := &http.Client{}
	response, err := client.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	body, err := ioutil.ReadAll(response.Body)

	if err != nil {
		return err
	}

	resp := &HostActorResponse{}
	err = json.Unmarshal(body, resp)

	if err != nil {
		return err
	}

	if resp.Result != "pong" {
		return fmt.Errorf("invalid Response From Host Actor: %s", resp.Result)
	}

	return nil
}

func RebootHost() error {
	// Note This function is not expected to return on success since the host will reboot, or might reboot shortly after returning
	// This will only work in a container with the proper bind mounts
	reqData := &HostActorRequest{
		Command: "reboot",
	}

	jsonData, err := json.MarshalIndent(reqData, "", "  ")

	if err != nil {
		return err
	}

	request, err := http.NewRequest("POST", "http://127.0.0.1:61008", bytes.NewBuffer(jsonData))

	if err != nil {
		return err
	}

	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	client := &http.Client{}
	response, err := client.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	// TODO maybe at some point parse response..., but really who cares

	return nil
}

func HardRebootHost() error {
	// Note This function is not expected to return on success since the host will reboot, or might reboot shortly after returning
	// This will only work in a container with the proper bind mounts
	// "/proc/sys/kernel/sysrq:/sysrq:rw",
	// "/proc/sysrq-trigger:/sysrq-trigger:rw",

	logrus.Info("Rebooting Host")

	cmd := exec.Command("bash", "-c", "echo 1 > /sysrq")
	err := cmd.Run()

	if err != nil {
		return err
	}

	cmd = exec.Command("bash", "-c", "echo s > /sysrq-trigger")
	err = cmd.Run()

	if err != nil {
		return err
	}

	cmd = exec.Command("bash", "-c", "echo b > /sysrq-trigger")
	err = cmd.Run()

	return err
}

func UpdateHostname(env environment.Robot) (deviceName string, err error) {
	deviceName = "unset"
	switch {
	case environment.CarbonRole(env.MakaRole) == environment.CarbonRoleSimulator:
		// return robot name, but don't update for simulators
		return env.MakaRobotName, nil
	case environment.CarbonRole(env.MakaRole) == environment.CarbonRoleBud:
		deviceName = env.MakaRobotName
	case environment.IsModule():
		switch {
		case env.CarbonModuleID != environment.UnsetCarbonModuleID:
			deviceName = strings.Join([]string{env.MakaRobotName, env.MakaRole, env.CarbonModuleID}, "-")
		case env.CarbonModuleSerial != environment.UnsetCarbonModuleSerial:
			deviceName = strings.Join([]string{env.MakaRobotName, env.CarbonModuleSerial}, "-")
		default:
			deviceName = strings.Join([]string{env.MakaRobotName, env.MakaRole}, "-")
		}
	case environment.CarbonRole(env.MakaRole) == environment.CarbonRoleCommand:
		deviceName = strings.Join([]string{env.MakaRobotName, env.MakaRole}, "-")
	case environment.IsRtc():
		deviceName = env.MakaRobotName
	default:
		deviceName = strings.Join([]string{env.MakaRobotName, env.MakaRole, env.MakaRow}, "-")
	}
	deviceName = strings.ReplaceAll(deviceName, "_", "-")
	err = afero.WriteFile(appFS, hostnameFilepath, []byte(deviceName), 0644)
	if err != nil {
		return "", fmt.Errorf("failed to write hostname (%s) to /etc/hostname -  %w", deviceName, err)
	}
	return deviceName, nil
}

func SetHostname(deviceName string) error {
	reqData := &HostActorRequest{
		Command: "hostname",
	}

	jsonData, err := json.MarshalIndent(reqData, "", "  ")

	if err != nil {
		return err
	}

	request, err := http.NewRequest("POST", "http://127.0.0.1:61008", bytes.NewBuffer(jsonData))

	if err != nil {
		return err
	}

	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	client := &http.Client{}
	response, err := client.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	return nil
}

package registry

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"strings"

	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/flosch/pongo2/v5"
	"github.com/google/go-github/v43/github"
	"github.com/sirupsen/logrus"
	"golang.org/x/oauth2"
	"gopkg.in/yaml.v2"
)

type DockerService struct {
	Image string `yaml:"image"`
}
type DockerServices struct {
	Services map[string]DockerService `yaml:"services"`
}

func GetGithubContainersForService(client *github.Client, service string, version string) ([]string, error) {
	ctx := context.Background()
	file, _, _, err := client.Repositories.GetContents(ctx, "carbonrobotics", "robot", fmt.Sprintf("services/compositions/%s.yaml.j2", service), &github.RepositoryContentGetOptions{Ref: version})

	if err != nil {
		return nil, err
	}

	fileDecoded, err := base64.StdEncoding.DecodeString(*file.Content)

	if err != nil {
		return nil, err
	}

	pongoCtx := pongo2.Context{
		"CARBON_VERSION_TAG": version,
	}

	tpl, err := pongo2.FromBytes(fileDecoded)
	if err != nil {
		return nil, err
	}

	out, err := tpl.Execute(pongoCtx)
	if err != nil {
		return nil, err
	}

	conf := DockerServices{}

	err = yaml.Unmarshal([]byte(out), &conf)

	if err != nil {
		return nil, err
	}

	results := []string{}

	for _, service := range conf.Services {
		result := os.Expand(service.Image, func(key string) string {
			env := map[string]string{
				"CARBON_VERSION_TAG": version,
			}

			splits := strings.Split(key, ":")

			if len(splits) == 0 {
				return ""
			}

			if val, ok := env[splits[0]]; ok {
				return val
			}
			return ""
		})
		results = append(results, result)
	}

	return results, nil
}

func GetGithubRolesForVersion(client *github.Client, version string) (map[string][]string, error) {
	ctx := context.Background()
	file, _, _, err := client.Repositories.GetContents(ctx, "carbonrobotics", "robot", "services/roles.yaml", &github.RepositoryContentGetOptions{Ref: version})

	if err != nil {
		return nil, err
	}

	fileDecoded, err := base64.StdEncoding.DecodeString(*file.Content)

	if err != nil {
		return nil, err
	}

	roles := map[string][]string{}

	err = yaml.Unmarshal(fileDecoded, &roles)
	if err != nil {
		return nil, err
	}

	return roles, nil
}

func MergeRoles(env *environment.Robot, roles map[string][]string) []string {
	roleSet := map[string]bool{}
	serviceSet := map[string]bool{}

	switch env.MakaGen {
	case string(environment.CarbonGenBud):
		roleSet[string(environment.CarbonRoleBud)] = true
	case string(environment.CarbonGenSlayer):
		roleSet[string(environment.CarbonRoleCommand)] = true
		roleSet[string(environment.CarbonRoleRow)] = true
	case string(environment.CarbonGenReaper):
		roleSet[string(environment.CarbonRoleCommand)] = true
		roleSet[string(environment.CarbonRoleModule)] = true
	default:
		roleSet[string(environment.CarbonRoleBud)] = true
	}

	for role := range roleSet {
		if services, ok := roles[role]; ok {
			for _, service := range services {
				serviceSet[service] = true
			}
		}
	}

	results := []string{}

	for service := range serviceSet {
		results = append(results, service)
	}

	return results
}

func StripContainerName(container string) string {
	splits := strings.Split(container, ":") // Splits the Tag

	if len(splits) == 0 {
		return "" // Improper Version Tag, we discard
	}

	fullName := splits[0]

	name := strings.TrimPrefix(fullName, RegistryPrefix)

	if name == fullName {
		return "" // Improper repo, we discard
	}

	return name
}

func GetGithubClient() *github.Client {
	ctx := context.Background()

	// TODO Token Security LMAO? This is a personal access token with access to robot repo
	// We could look at a simple cloud service that can use oauth to authenticate and return the requested data
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: auth.GitHubAccessToken},
	)
	tc := oauth2.NewClient(ctx, ts)
	client := github.NewClient(tc)

	return client
}

func GetGithubContainersForVersion(env *environment.Robot, version string) ([]string, error) {
	client := GetGithubClient()

	roles, err := GetGithubRolesForVersion(client, version)
	if err != nil {
		return nil, err
	}

	services := MergeRoles(env, roles)
	containerSet := map[string]bool{
		"gobot": true, // Gobot is always required but not in a service
	}

	for _, service := range services {
		logrus.Info("Retrieving Container Names for Service: ", service)
		containers, err := GetGithubContainersForService(client, service, version)

		if err != nil {
			return nil, err
		}

		for _, container := range containers {
			name := StripContainerName(container)
			if name != "" {
				containerSet[name] = true
			}
		}
	}

	containers := []string{}
	for container := range containerSet {
		containers = append(containers, container)
	}

	return containers, nil
}

type SystemVersionGithub struct {
	Version string `yaml:"version"`
}

func GetGithubSystemVersionForVersion(version string) (string, error) {
	ctx := context.Background()
	client := GetGithubClient()
	file, _, _, err := client.Repositories.GetContents(ctx, "carbonrobotics", "robot", "system_version.yaml", &github.RepositoryContentGetOptions{Ref: version})

	if err != nil {
		return "", err
	}

	fileDecoded, err := base64.StdEncoding.DecodeString(*file.Content)

	if err != nil {
		return "", err
	}

	systemVersion := SystemVersionGithub{}

	err = yaml.Unmarshal(fileDecoded, &systemVersion)
	if err != nil {
		return "", err
	}

	return systemVersion.Version, nil
}

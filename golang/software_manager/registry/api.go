package registry

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/sirupsen/logrus"
)

type Catalog struct {
	Respositories []string `json:"repositories"`
}

type Tags struct {
	Name string   `json:"name"`
	Tags []string `json:"tags"`
}

type RegistryClient struct {
	addr   string
	client *http.Client
}

func NewRegistryClient(addr string) *RegistryClient {
	return &RegistryClient{
		addr:   addr,
		client: &http.Client{},
	}
}

func (c *RegistryClient) GetCatalog() ([]string, error) {
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/v2/_catalog", c.addr), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.client.Do(req)

	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	catalog := Catalog{}

	err = json.Unmarshal(body, &catalog)
	if err != nil {
		return nil, err
	}

	return catalog.Respositories, nil
}

func (c *RegistryClient) GetDigest(image string, tag string) (string, error) {
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/v2/%s/manifests/%s", c.addr, image, tag), nil)

	if err != nil {
		return "", err
	}

	req.Header.Set("Accept", "application/vnd.docker.distribution.manifest.v2+json")
	resp, err := c.client.Do(req)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()
	if val, ok := resp.Header["Docker-Content-Digest"]; ok {

		logrus.Info("Got Manifest For: ", image, ":", tag, " Digest: ", val[0])
		return val[0], nil
	}
	return "", fmt.Errorf("Docker-Content-Digest not set")
}

func (c *RegistryClient) GetTags(image string) ([]string, error) {
	req, err := http.NewRequest("GET", fmt.Sprintf("%s//v2/%s/tags/list", c.addr, image), nil)

	if err != nil {
		return nil, err
	}

	resp, err := c.client.Do(req)

	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	tags := Tags{}

	err = json.Unmarshal(body, &tags)
	if err != nil {
		return nil, err
	}

	return tags.Tags, nil
}

func (c *RegistryClient) Delete(image string, digest string) error {
	req, err := http.NewRequest("DELETE", fmt.Sprintf("%s/v2/%s/manifests/%s", c.addr, image, digest), nil)

	if err != nil {
		return err
	}

	resp, err := c.client.Do(req)

	if err != nil {
		return err
	}

	defer resp.Body.Close()

	_, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	return nil
}

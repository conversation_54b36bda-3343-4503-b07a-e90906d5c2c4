package registry

import (
	"context"
	"errors"
	"fmt"
	"os"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/docker"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/carbonrobotics/robot/golang/software_manager/host"
	"github.com/carbonrobotics/robot/golang/software_manager/meta"
	"github.com/carbonrobotics/robot/golang/software_manager/system"
	"github.com/carbonrobotics/robot/golang/software_manager/system_version"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
	"github.com/sirupsen/logrus"
	"golang.org/x/oauth2"
)

const (
	redisTimeout  = 10 * time.Second
	dockerTimeout = 15 * time.Second
)

const (
	// Redis keys
	// docker/<host>/images/<use_case>/<version>
	RegistryDownloadStatusKey = "docker/%v/images/downloaded/registry/%v"
	KnownVersionsKey          = "docker/%v/images/known_versions/"
)

type TwoPCState uint32

const (
	Ready       TwoPCState = 0
	Preparing   TwoPCState = 1
	Prepared    TwoPCState = 2
	AbortFailed TwoPCState = 3
)

type VersionMetadataCacheEntry struct {
	metadata    *meta.SoftwareVersionMetadata
	ready       bool
	checkedOnce bool
}

type VersionMetadataCache struct {
	lk    sync.Mutex
	cache map[string]*VersionMetadataCacheEntry
}

func NewVersionMetadataCache() *VersionMetadataCache {
	return &VersionMetadataCache{
		cache: make(map[string]*VersionMetadataCacheEntry),
	}
}

func (c *VersionMetadataCache) Get(tag string) (*meta.SoftwareVersionMetadata, bool, error) {
	c.lk.Lock()
	defer c.lk.Unlock()
	entry, ok := c.cache[tag]
	if !ok {
		return nil, false, fmt.Errorf("metadata not found")
	} else if entry.metadata == nil {
		return nil, false, fmt.Errorf("metadata not found")
	}
	return entry.metadata, entry.ready && entry.checkedOnce, nil
}

func (c *VersionMetadataCache) Set(tag string, metadata *meta.SoftwareVersionMetadata, ready bool) {
	c.lk.Lock()
	defer c.lk.Unlock()
	if entry, ok := c.cache[tag]; ok {
		entry.metadata = metadata
		entry.ready = ready
	} else {
		c.cache[tag] = &VersionMetadataCacheEntry{
			metadata: metadata,
			ready:    ready,
		}
	}
}

func (c *VersionMetadataCache) GetCheckedOnce(tag string) bool {
	c.lk.Lock()
	defer c.lk.Unlock()
	if entry, ok := c.cache[tag]; ok {
		return entry.checkedOnce
	}
	return false
}

func (c *VersionMetadataCache) SetCheckedOnce(tag string, metadata *meta.SoftwareVersionMetadata) {
	c.lk.Lock()
	defer c.lk.Unlock()
	if entry, ok := c.cache[tag]; ok {
		entry.metadata = metadata
		entry.checkedOnce = true
	} else {
		c.cache[tag] = &VersionMetadataCacheEntry{
			metadata:    metadata,
			checkedOnce: true,
		}
	}
}

func (c *VersionMetadataCache) Invalidate(tag string) {
	c.lk.Lock()
	defer c.lk.Unlock()
	delete(c.cache, tag)
}

func (c *VersionMetadataCache) Clean(tagsToKeep map[string]struct{}) {
	c.lk.Lock()
	defer c.lk.Unlock()
	for tag := range c.cache {
		if _, ok := tagsToKeep[tag]; !ok {
			delete(c.cache, tag)
		}
	}
}

type VersionMaintainer struct {
	stopCtx                    context.Context
	env                        *environment.Robot
	sourcePrefix               string
	targetPrefix               string
	cli                        *client.Client
	redis                      *redis.Client
	targetVersion              *config.ConfigTree
	previousVersion            *config.ConfigTree
	versionPollIntervalS       *config.ConfigTree
	verifyImagesUsingChecksums *config.ConfigTree
	configClient               *config.ConfigClient
	registryClient             *RegistryClient
	softwareManagerClient      *software_manager_client.SoftwareManagerClient
	metaClient                 *meta.VersionMetadataClient
	lock                       sync.Mutex
	upgradingSystem            bool
	twoPCLk                    sync.Mutex
	twoPCState                 TwoPCState
	twoPCReqId                 string

	versionMetadataCache  *VersionMetadataCache
	cacheRefreshTriggerCh chan struct{}
	cachedCurrentVersion  atomic.Value
}

func isTagMaintainable(tag string) bool {
	return len(tag) > 0 && tag[0] == 'v'
}

func NewVersionMaintainer(stopCtx context.Context, env *environment.Robot, cli *client.Client, redis *redis.Client, config_subscriber *config.ConfigSubscriber, configClient *config.ConfigClient, registryClient *RegistryClient, softwareManagerClient *software_manager_client.SoftwareManagerClient, tokenSource oauth2.TokenSource) (*VersionMaintainer, error) {
	versionMetadataHost := env.CarbonVersionMetadataHost
	metaClient := meta.NewVersionMetadataClient(versionMetadataHost, tokenSource)
	m := &VersionMaintainer{
		stopCtx:                    stopCtx,
		env:                        env,
		sourcePrefix:               GetSourcePrefix(env),
		targetPrefix:               GetTargetPrefix(env),
		cli:                        cli,
		redis:                      redis,
		targetVersion:              config_subscriber.GetConfigNode("common", "software_manager/target_version"),
		previousVersion:            config_subscriber.GetConfigNode("common", "software_manager/previous_version"),
		versionPollIntervalS:       config_subscriber.GetConfigNode("common", "software_manager/version_poll_interval_s"),
		verifyImagesUsingChecksums: config_subscriber.GetConfigNode("common", "software_manager/verify_images_using_checksums"),
		registryClient:             registryClient,
		softwareManagerClient:      softwareManagerClient,
		configClient:               configClient,
		metaClient:                 metaClient,
		upgradingSystem:            false,
		twoPCState:                 Ready,
		versionMetadataCache:       NewVersionMetadataCache(),
		cacheRefreshTriggerCh:      make(chan struct{}, 1),
		cachedCurrentVersion:       atomic.Value{},
	}
	m.cachedCurrentVersion.Store("")
	return m, nil
}

func setDownloadedStatus(env *environment.Robot, redis *redis.Client, image string, version string, value bool) error {
	var host string
	if env.IsModule() {
		host = env.CarbonModuleSerial
	} else {
		host = env.GetHost()
	}

	err := redis.HSet(fmt.Sprintf(KnownVersionsKey, host), version, "true")

	if err != nil {
		return err
	}

	val := "false"

	if value {
		val = "true"
	}

	return redis.HSet(fmt.Sprintf(RegistryDownloadStatusKey, host, version), image, val)
}

func getDownloadedStatus(env *environment.Robot, redis *redis.Client, image string, version string) (bool, error) {
	var host string
	if env.IsModule() {
		host = env.CarbonModuleSerial
	} else {
		host = env.GetHost()
	}

	// check if the version is known and dowloaded, versions not in the known versions are not downloaded
	versionDict, err := redis.HGetAll(fmt.Sprintf(KnownVersionsKey, host))
	if err != nil {
		return false, err
	}

	if result, ok := versionDict[version]; ok {
		if result != "true" {
			return false, nil
		}
	} else {
		return false, nil
	}

	var dict map[string]string
	dict, err = redis.HGetAll(fmt.Sprintf(RegistryDownloadStatusKey, host, version))
	if err != nil {
		return false, err
	}

	if val, ok := dict[image]; !ok {
		return false, nil
	} else if val == "true" {
		return true, nil
	}

	return false, nil
}

func clearUnwantedDownloadedStatus(env *environment.Robot, redis *redis.Client, tags map[string]bool) error {
	var host string
	if env.IsModule() {
		host = env.CarbonModuleSerial
	} else {
		host = env.GetHost()
	}

	logrus.Info("Clearing Unwanted Downloaded Status")

	versionDict, err := redis.HGetAll(fmt.Sprintf(KnownVersionsKey, host))

	if err != nil {
		return err
	}

	for version, val := range versionDict {
		if _, ok := tags[version]; ok {
			continue
		}

		if val != "true" {
			continue
		}

		err = redis.Del(fmt.Sprintf(RegistryDownloadStatusKey, host, version))
		if err != nil {
			logrus.WithError(err).Error("Failed to remove registry download status: ", version)
		}

		err = redis.HDel(fmt.Sprintf(KnownVersionsKey, host), version)
		if err != nil {
			return err
		}
	}

	return nil
}

func (m *VersionMaintainer) GetEnv() *environment.Robot {
	return m.env
}

func (m *VersionMaintainer) isVersionAvailable(version string, alreadyLocked bool) bool {
	check_redis, err := m.shouldCheckRedis(version)

	if err != nil {
		logrus.WithError(err).Error("Can't check if redis is needed in IsVersionAvailable: ", version)
		return false
	}

	metadata, err := meta.LoadSoftwareVersionMetadata(m.env, version)
	if err != nil {
		logrus.Warn("Version Metadata Not yet Available: ", version)
		return false
	}

	available := true

	wg := sync.WaitGroup{}

	results := make([]bool, len(metadata.Containers))
	wg.Add(len(metadata.Containers))
	for i, image := range metadata.Containers {
		go func(index int, img string) {
			defer wg.Done()
			result := m.imageExists(fmt.Sprintf("%s%s", RegistryPrefix, img), version)

			if check_redis {
				redis_result, err := getDownloadedStatus(m.env, m.redis, img, version)
				if err != nil {
					redis_result = false
				}

				result = result && redis_result
			}

			results[index] = result
			imageDownloadStatusGauge.WithLabelValues(img, getVersionLabel(check_redis)).Set(boolToFloat(result))
		}(i, image)
	}

	wg.Wait()

	for _, r := range results {
		available = available && r
	}

	upgrading := true
	func() {
		if !alreadyLocked {
			m.lock.Lock()
			defer m.lock.Unlock()
		}
		upgrading = m.upgradingSystem
	}()

	if upgrading {
		return false
	}

	currentSystemVersion, err := bot_context.GetSystemVersion()
	if err != nil {
		logrus.WithError(err).Error("Failed to get current system version")
		return false
	}

	systemInfo, err := system.GetSystemInfo(m.env)

	if err != nil {
		logrus.WithError(err).Error("Failed to Get System Info")
		return false
	}

	if metadata.System != systemInfo.Versions.Other && metadata.System != currentSystemVersion {
		return false
	}

	return available
}

func (m *VersionMaintainer) clearUnwantedRegistryTags(tags map[string]bool) {
	images, err := m.registryClient.GetCatalog()

	if err != nil {
		logrus.WithError(err).Error("Failed To List Registry Catalog")
	}

	for _, image := range images {
		if !strings.HasPrefix(image, "robot/") {
			continue
		}

		tagList, err := m.registryClient.GetTags(image)

		if err != nil {
			logrus.WithError(err).Error("Failed To List Tags for: ", image)
			continue
		}

		digestsToKeep := map[string]bool{}

		for tag, _ := range tags {
			digest, err := m.registryClient.GetDigest(image, tag)
			if err != nil {
				logrus.WithError(err).Error("Failed To Get Digest To Keep: ", image, ":", tag)
				continue
			}

			digestsToKeep[digest] = true
		}

		for _, tag := range tagList {
			if isTagMaintainable(tag) {
				// We only consider tag starting with v since those are the ones we manage
				continue
			}

			if tags[tag] {
				// We only prune tags we don't care about
				continue
			}

			digest, err := m.registryClient.GetDigest(image, tag)
			if err != nil {
				logrus.WithError(err).Error("Failed To Get Digest for: ", image, ":", tag)
				continue
			}

			if val, ok := digestsToKeep[digest]; ok {
				if val {
					// Don't delete if digest is marked to keep
					continue
				}
			}

			err = m.registryClient.Delete(image, digest)
			if err != nil {
				logrus.WithError(err).Error("Failed To Delete Image Digest for: ", image, ":", tag, " Digest: ", digest)
				continue
			}

			logrus.Info("Successfully Cleared Image from Registry: ", image, ":", tag)
		}
	}

	err = docker.ExecCommandInRunningContainer(m.cli, "registry", []string{"bin/registry", "garbage-collect", "--delete-untagged", "/etc/docker/registry/config.yml"})
	if err != nil {
		logrus.WithError(err).Error("Failed to Launch Registry Garbage Collection")
	}
}

func (m *VersionMaintainer) pruneDangling() {
	ctx := docker.GetDockerContext(m.cli)

	containerReport, err := m.cli.ContainersPrune(ctx, filters.NewArgs(
		filters.KeyValuePair{
			Key:   "until",
			Value: "72h",
		},
	))

	if err != nil {
		logrus.WithError(err).Error("Failed To Prune Dangling Containers")
	} else {
		for _, c := range containerReport.ContainersDeleted {
			logrus.Info("Deleted Dangling Container: ", c)
		}

		if containerReport.SpaceReclaimed == 0 {
			logrus.Info("Reclaimed space from Containers: ", containerReport.SpaceReclaimed, " bytes")
		}
	}

	imagesReport, err := m.cli.ImagesPrune(ctx, filters.NewArgs())

	if err != nil {
		logrus.WithError(err).Error("Failed To Prune Dangling Images")
	} else {
		for _, c := range imagesReport.ImagesDeleted {
			logrus.Info("Deleted Dangling Images: ", c)
		}

		if imagesReport.SpaceReclaimed == 0 {
			logrus.Info("Reclaimed space from Images: ", imagesReport.SpaceReclaimed, " bytes")
		}
	}
}

// clean up any exited verify-image containers
func (m *VersionMaintainer) removeDanglingVerificationImages(parentCtx context.Context) {
	logrus.Info("Removing dangling verify-image containers")

	listCtx, cancel := context.WithTimeout(parentCtx, dockerTimeout)
	defer cancel()

	result, err := m.cli.ContainerList(listCtx, container.ListOptions{
		All: true,
	})

	if err != nil {
		logrus.WithError(err).Error("Failed To List Containers")
	}

	for _, c := range result {
		verifyImage := strings.HasPrefix(c.Names[0], "/verify-image-")
		exited := c.State == "exited"
		createdAndStale := c.State == "created" && c.Created < time.Now().Add(-1*time.Minute).UnixMilli()

		logrus.Debugf("Container: %v, VerifyImage: %v, Exited: %v, CreatedAndStale: %v", c.Names[0], verifyImage, exited, createdAndStale)

		// delete any verify-image containers that are exited or created and stale
		if !(verifyImage && (exited || createdAndStale)) {
			continue
		}

		removeCtx, cancel := context.WithTimeout(parentCtx, dockerTimeout)
		defer cancel()
		err := m.cli.ContainerRemove(removeCtx, c.ID, container.RemoveOptions{
			Force: true,
		})

		if err != nil {
			logrus.WithError(err).Error("Failed to remove verify-image container: ", c.Names[0])
			continue
		} else {
			logrus.Info("Removed dangling verify-image container: ", c.Names[0])
		}
	}
}

func (m *VersionMaintainer) clearKnownInvalidImage(parentCtx context.Context, name string, tag string, prefix string) {
	ctx, cancel := context.WithTimeout(parentCtx, dockerTimeout)
	defer cancel()
	path := prefix + name + ":" + tag

	resp, err := m.cli.ImageRemove(ctx, path, image.RemoveOptions{
		Force:         true,
		PruneChildren: true,
	})

	if err != nil {
		logrus.WithError(err).Error("Failed to remove image: ", path)
	}

	for _, r := range resp {
		if r.Deleted != "" {
			logrus.Info("Deleted Image: ", path, " at ", r.Deleted)
		}
		if r.Untagged != "" {
			logrus.Info("Untagged Image: ", path, " at ", r.Untagged)
		}
	}
}

// deletes are mutually exculsive with an update so we hold the lock to not interfere with a prepare
func (m *VersionMaintainer) updateSafeCombinedClearKnownInvalidImage(name, tag string) {
	m.lock.Lock()
	defer m.lock.Unlock()
	m.clearKnownInvalidImage(m.stopCtx, name, tag, m.sourcePrefix)
	m.clearKnownInvalidImage(m.stopCtx, name, tag, m.targetPrefix)
}

func (m *VersionMaintainer) ClearUnwantedTags(tags map[string]bool, prefix string) {
	ctx := docker.GetDockerContext(m.cli)

	result, err := m.cli.ImageList(ctx, image.ListOptions{
		Filters: filters.NewArgs(filters.Arg("reference", prefix+"*")),
	})

	if err != nil {
		logrus.WithError(err).Error("Failed To Search Image: ", prefix)
	}

	for _, summary := range result {
		for _, fullNameFound := range summary.RepoTags {
			splits := strings.Split(fullNameFound, ":")

			if len(splits) < 2 {
				logrus.Warn("Container Name Is Not Valid: ", fullNameFound)
				continue
			}

			tag := splits[len(splits)-1]

			if !isTagMaintainable(tag) {
				// We only consider tag starting with v since those are the ones we manage
				continue
			}

			if tags[tag] {
				// We only prune tags we don't care about
				continue
			}

			resp, err := m.cli.ImageRemove(ctx, fullNameFound, image.RemoveOptions{
				PruneChildren: true,
			})

			if err != nil {
				logrus.WithError(err).Error("Failed to remove container: ", fullNameFound)
				continue
			}

			for _, r := range resp {
				if r.Deleted != "" {
					logrus.Info("Deleted Image: ", fullNameFound, " at ", r.Deleted)
				}
				if r.Untagged != "" {
					logrus.Info("Untagged Image: ", fullNameFound, " at ", r.Untagged)
				}
			}
		}
	}
}

// Lightweight check to see if manifest is downloaded
func verifyImageManifest(parentCtx context.Context, cli *client.Client, fullName string) error {
	ctx, cancel := context.WithTimeout(parentCtx, dockerTimeout)
	defer cancel()
	if _, b, err := cli.ImageInspectWithRaw(ctx, fullName); err != nil || len(b) < 1 {
		return fmt.Errorf("image manifest failed inspection: %v", err)
	}
	return nil
}

// Checks if the image lists in the docker daemon, only gaurantees that the manifest is downloaded
func (m *VersionMaintainer) imageExists(name string, tag string) bool {
	fullName := name + ":" + tag
	ctx := docker.GetDockerContext(m.cli)

	// only gaurantees that the manifest is downloaded, not that the image is valid
	result, err := m.cli.ImageList(ctx, image.ListOptions{
		Filters: filters.NewArgs(filters.Arg("reference", fullName)),
	})

	if err != nil {
		logrus.WithError(err).Error("Failed To Search Image: ", fullName)
		return false
	}

	lookup := map[string]bool{}

	for _, summary := range result {
		for _, fullNameFound := range summary.RepoTags {
			lookup[fullNameFound] = true
		}
	}

	inLookup := lookup[fullName]

	if !inLookup {
		return false
	}

	if err := verifyImageManifest(m.stopCtx, m.cli, fullName); err != nil {
		logrus.WithError(err).Error("image verification failed: ", fullName)
		return false
	}

	return true
}

// FetchImageFromRegistry
// If on command, fetches image from the github registry and saves it to the local registry
// If on a row, fetches image from the local registry on command
func (m *VersionMaintainer) fetchImageFromRegistry(name, tag string) error {
	sourceTag := tag
	if runtime.GOARCH == "arm64" {
		sourceTag += "-arm64"
	}
	sourcePath := m.sourcePrefix + name + ":" + sourceTag
	targetPath := m.targetPrefix + name + ":" + tag

	sourceExists := m.imageExists(m.sourcePrefix+name, sourceTag)
	targetExists := m.imageExists(m.targetPrefix+name, tag)

	if !sourceExists {
		if err := docker.Pull(m.cli, sourcePath, true); err != nil {
			if currentVersion := m.cachedCurrentVersion.Load().(string); currentVersion != "" {
				imageDowloadFailedTotal.WithLabelValues(name, getVersionLabel(tag == currentVersion)).Inc()
			}

			logrus.WithError(err).Error("Failed To Pull Image: ", sourcePath)
			return err
		}
		if err := verifyImageManifest(m.stopCtx, m.cli, sourcePath); err != nil {
			logrus.WithError(err).Error("image verification failed: ", sourcePath)
			return err
		}
	}

	if !targetExists {
		logrus.Info("Tagging ", sourcePath, " to ", targetPath)
		ctx := docker.GetDockerContext(m.cli)
		err := m.cli.ImageTag(ctx, sourcePath, targetPath)

		if err != nil {
			logrus.WithError(err).Error("Failed to Tag ", sourcePath, " to ", targetPath)
			return err
		}
	}

	if m.registryClient != nil {
		logrus.Info("Pushing To Self Registry as Host: ", targetPath)
		err := docker.Push(m.cli, targetPath, true)
		if err != nil {
			if currentVersion := m.cachedCurrentVersion.Load().(string); currentVersion != "" {
				imagePushFailedTotal.WithLabelValues(name, getVersionLabel(tag == currentVersion)).Inc()
			}

			logrus.WithError(err).Error("Failed to Push to Self Registry: ", targetPath)
			return err
		}
	}

	return nil
}

func (m *VersionMaintainer) getSoftwareVersionMetadataFromCloud(version string) (*meta.SoftwareVersionMetadata, error) {
	var containerNames []string
	var systemVersion string
	vm, err := m.metaClient.GetVersionMetadata(version, m.env.MakaGen)
	if err != nil {
		return nil, err
	} else {
		containerNames = vm.Containers
		systemVersion = vm.SystemVersion
	}

	// Special Case for non-managed OS robots
	if m.env.IsUnmanagedSystem() {
		systemVersion = system.UnmanagedSystemVersion
	}

	meta := &meta.SoftwareVersionMetadata{
		Tag:        version,
		Containers: containerNames,
		System:     systemVersion,
	}

	logrus.Debugf("Maintainer got version metadata: %v", meta)

	return meta, nil
}

func (m *VersionMaintainer) getSoftwareVersionMetadataFromCommander(version string) (*meta.SoftwareVersionMetadata, error) {
	if m.softwareManagerClient == nil {
		return nil, fmt.Errorf("Software Manager Client is nil, this is likely some kind of configuration error")
	}

	remoteMetadata, err := m.softwareManagerClient.GetSoftwareVersionMetadata(version)

	if err != nil {
		return nil, err
	}

	metadata := &meta.SoftwareVersionMetadata{
		Tag:        remoteMetadata.Tag,
		Containers: remoteMetadata.Containers,
		System:     remoteMetadata.System,
	}

	// Special Case for non-managed OS robots
	if m.env.IsUnmanagedSystem() {
		metadata.System = system.UnmanagedSystemVersion
	}

	return metadata, nil
}

func (m *VersionMaintainer) getSoftwareVersionMetadata(version string) (*meta.SoftwareVersionMetadata, error) {
	if m.softwareManagerClient == nil {
		return m.getSoftwareVersionMetadataFromCloud(version)
	} else {
		return m.getSoftwareVersionMetadataFromCommander(version)
	}
}

func (m *VersionMaintainer) shouldCheckRedis(version string) (bool, error) {
	// We check all versions except current
	current_version, err := m.getCurrentVersion()

	if err != nil {
		logrus.WithError(err).Error("Failed to retrieve current version in ShouldCheckRedis: ", version)
		return false, err
	}

	return current_version != version, nil
}

func (m *VersionMaintainer) shouldRemoveInvalidImage(version string) bool {
	current_version, err := m.getCurrentVersion()

	if err != nil {
		// We don't risk removing an image if it could be the current
		logrus.WithError(err).Error("Failed to retrieve Current Version")
		return false
	}

	return current_version != version
}

func (m *VersionMaintainer) maintainVersion(version string) {
	check_redis, err := m.shouldCheckRedis(version)
	if err != nil {
		logrus.WithError(err).Error("Can't check if redis is needed in maintain version: ", version)
		return
	}

	verifyImagesUsingChecksums := m.verifyImagesUsingChecksums.GetBoolValue()

	logrus.Infof("Maintaining Version: %v, ShouldCheckRedis: %v, VerifyImagesUsingChecksums: %v", version, check_redis, verifyImagesUsingChecksums)

	meta, err := m.getSoftwareVersionMetadata(version)
	if err != nil {
		logrus.WithError(err).Error("Failed to retrieve version metadata: ", version)
		return
	}

	err = meta.Save()
	if err != nil {
		logrus.WithError(err).Error("Failed to save version metadata: ", version)
		return
	}

	m.TriggerCacheRefresh() // meta data would have changed, so refresh the cache

	for _, name := range meta.Containers {
		downloadedStatus := true
		if check_redis { // check_redis is a misnomer, it checks the this version is not the current version

			// 1. get downloaded status from redis
			// 2. check if the image lists
			// 3. check if the image manifest is downloaded

			downloadedStatus, err = getDownloadedStatus(m.env, m.redis, name, version)
			if err != nil {
				logrus.WithError(err).Error("Failed to get version status from Redis")
				return
			}

			if downloadedStatus && (!m.imageExists(fmt.Sprintf("%s%s", m.sourcePrefix, name), version) || !m.imageExists(fmt.Sprintf("%s%s", m.targetPrefix, name), version)) {
				downloadedStatus = false
			}

			if !downloadedStatus {
				logrus.Warn("Image had false downloaded status: ", name, ":", version, " -> ", downloadedStatus)
				m.updateSafeCombinedClearKnownInvalidImage(name, version)
			}
		}

		if !downloadedStatus {
			err = setDownloadedStatus(m.env, m.redis, name, version, false)
			if err != nil {
				logrus.WithError(err).Error("Failed to set Download Status false in redis")
				return
			}
		}

		err = m.fetchImageFromRegistry(name, version)
		if err != nil {
			logrus.WithError(err).Error("Failed to Fetch Image: ", name)
			return
		}

		passedVerification := true
		if check_redis && verifyImagesUsingChecksums {
			// Heavyweight check to see if image is valid by starting a container
			// only verify the source prefix, its the only that needs to run
			logrus.Infof("Verifying Image: %v:%v", name, version)
			if err := docker.VerifyImageUsingChecksums(m.stopCtx, m.cli, fmt.Sprintf("%s%s:%s", m.sourcePrefix, name, version)); err != nil {
				logrus.WithError(err).Errorf("image verification failed: %v:%v, removing image", name, version)
				passedVerification = false
				m.updateSafeCombinedClearKnownInvalidImage(name, version)
				imageChecksumFailedTotal.WithLabelValues(name, getVersionLabel(check_redis)).Inc()
			}
		}

		err = setDownloadedStatus(m.env, m.redis, name, version, passedVerification)
		if err != nil {
			logrus.WithError(err).Error("Failed to set Download Status in redis: ", name, ":", version)
			return
		}

		if !passedVerification {
			logrus.Errorf("Failed to download and verify version: %v, will retry later", version)
			return
		}
	}

	// docker images have been checked
	m.versionMetadataCache.SetCheckedOnce(version, meta)

	systemVersion, err := bot_context.GetSystemVersion()
	if err != nil {
		logrus.WithError(err).Error("Failed to get current system version")
		return
	}

	logrus.Info("Current System Version: ", systemVersion, " , ", "Meta: ", meta.System)
	if systemVersion == meta.System {
		return
	}

	systemInfo, err := system.GetSystemInfo(m.env)

	if err != nil {
		logrus.WithError(err).Error("Failed to get System Info while maintaining version")
		return
	}

	logrus.Info("Other System Version: ", systemInfo.Versions.Other, " , ", "Meta: ", meta.System)
	if systemInfo.Versions.Other == meta.System {
		systemVersionDownloadStatusGauge.WithLabelValues(getVersionLabel(check_redis)).Set(1)
		return
	}

	systemVersionDownloadStatusGauge.WithLabelValues(getVersionLabel(check_redis)).Set(0)
	upgradingSystemFunc := func(val bool) {
		m.lock.Lock()
		defer m.lock.Unlock()
		m.upgradingSystem = val
	}
	upgradingSystemFunc(true)
	defer upgradingSystemFunc(false)

	logrus.Info("Preparing System Version: ", meta.System)
	err = system_version.PrepareSystemVersion(m.env, meta.System, m.softwareManagerClient)
	if err != nil {
		logrus.WithError(err).Error("Failed to prepare system version: ", meta.System)
		systemVersionDownloadFailedTotal.WithLabelValues(getVersionLabel(check_redis)).Inc()
		return
	}

	logrus.Info("Finished Preparing Version: ", meta.System)

	systemInfo, err = system.GetSystemInfo(m.env)

	if err != nil {
		logrus.WithError(err).Error("Failed to get System Info after preparing version")
	} else {
		logrus.Info("Current System: ", systemInfo.Versions.Current, ", Other: ", systemInfo.Versions.Other)
		systemVersionDownloadStatusGauge.WithLabelValues(getVersionLabel(check_redis)).Set(1)
	}
	m.TriggerCacheRefresh() // version might be ready now, so refresh the cache
}

func (m *VersionMaintainer) maintainSoftwareVersions(versions []string) {
	wantedTags := map[string]bool{}
	keepArm := runtime.GOARCH == "arm64"
	for _, version := range versions {
		if version == "unset" { // This is a default value for strings in the config service
			continue
		}
		wantedTags[version] = true
		if keepArm {
			wantedTags[version+"-arm64"] = true
		}
		m.maintainVersion(version)
	}

	// if a verify-image operation is interrupted, it may leave a dangling container
	// clean them up here before we try to remove images
	m.removeDanglingVerificationImages(m.stopCtx)

	if len(wantedTags) > 0 && len(versions) >= 2 {
		logrus.Info("Clearing Unwanted Tags")
		func() {
			m.lock.Lock()
			defer m.lock.Unlock()
			err := clearUnwantedDownloadedStatus(m.env, m.redis, wantedTags)

			if err != nil {
				logrus.Error("Failed To Clear Unwanted Downloaded Status: ", err)
			}

			m.ClearUnwantedTags(wantedTags, m.sourcePrefix)
			m.ClearUnwantedTags(wantedTags, m.targetPrefix)
			if m.registryClient != nil {
				m.clearUnwantedRegistryTags(wantedTags)
			}
			m.pruneDangling()
		}()
	}
}

func (m *VersionMaintainer) getCurrentVersion() (string, error) {
	systemVersion, err := bot_context.GetSystemVersion()

	if err != nil {
		return "", err
	}

	versionDef, err := bot_context.LoadVersionDef(true, systemVersion)

	if err != nil {
		return "", err
	}

	// Add First Version to Maintain aka Current
	currentVersion := m.env.CarbonVersionTag
	if m.env.CarbonVersionTag == "latest" {
		currentVersion = versionDef.Tag
	}
	return currentVersion, nil
}

func (m *VersionMaintainer) getVersionsToMaintain() ([]string, error) {
	targetVersion := m.targetVersion.GetStringValue()
	previousVersion := m.previousVersion.GetStringValue()
	versionSet := map[string]bool{}
	versions := []string{}

	// Always keep current version
	currentVersion, err := m.getCurrentVersion()

	if err != nil {
		return nil, err
	}
	versionSet[currentVersion] = true

	// Second Version to Maintain, either Target or Previous or None
	if targetVersion != currentVersion && targetVersion != "" {
		versionSet[targetVersion] = true
	} else if previousVersion != "" {
		versionSet[previousVersion] = true
	}

	for v := range versionSet {
		versions = append(versions, v)
	}

	return versions, nil
}

func (m *VersionMaintainer) MaintainVersionsAtInterval() {
	configCh := make(chan bool, 1)
	triggerFunc := func() {
		select {
		case configCh <- true:
		default:
		}
	}
	m.targetVersion.RegisterCallback(triggerFunc)
	m.versionPollIntervalS.RegisterCallback(triggerFunc)

	for {
		select {
		case <-m.stopCtx.Done():
			return
		case <-configCh:
		case <-time.After(time.Duration(m.versionPollIntervalS.GetUIntValue()) * time.Second):
		}

		if m.env.IsModule() && (m.env.CarbonModuleID == "0" || m.env.CarbonModuleSerial == "") {
			// don't try to download images on unassigned modules or modules without serial
			logrus.Warn("Skipping version maintenance on unassigned module or module without serial")
			continue
		}

		versions, err := m.getVersionsToMaintain()

		if err != nil {
			logrus.WithError(err).Error("Failed to get versions to maintain")
			continue
		}

		logrus.Infof("Maintaining Versions: %v", versions)
		m.maintainSoftwareVersions(versions)
	}
}

func (m *VersionMaintainer) TriggerCacheRefresh() {
	select {
	case m.cacheRefreshTriggerCh <- struct{}{}:
	default:
	}
}

func (m *VersionMaintainer) MaintainVersionSummaryAtInterval() {
	configCh := make(chan bool, 1)
	triggerFunc := func() {
		select {
		case configCh <- true:
		default:
		}
	}
	m.targetVersion.RegisterCallback(triggerFunc)
	m.versionPollIntervalS.RegisterCallback(triggerFunc)
	for {
		select {
		case <-m.stopCtx.Done():
			return
		case <-configCh:
		case <-m.cacheRefreshTriggerCh:
		case <-time.After(time.Duration(5 * time.Second)):
		}

		if m.env.IsModule() && (m.env.CarbonModuleID == "0" || m.env.CarbonModuleSerial == "") {
			// don't check download status on unassigned modules or modules without serial
			logrus.Warn("Skipping version summary maintenance on unassigned module or module without serial")
			continue
		}

		m.maintainVersionSummary()
	}
}

func (m *VersionMaintainer) maintainVersionSummary() {
	versionsSet := map[string]struct{}{}
	addVersionFunc := func(version string) {
		if version != "" {
			versionsSet[version] = struct{}{}
		}
	}
	addVersionFunc(m.targetVersion.GetStringValue())
	addVersionFunc(m.previousVersion.GetStringValue())
	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		logrus.WithError(err).Error("Failed to get current version")
	} else {
		addVersionFunc(currentVersion)
	}

	// aquire lock to prevent updates while we are updating the cache and to prevent the cache from being updated while we are updating the software
	m.lock.Lock()
	defer m.lock.Unlock()

	m.cachedCurrentVersion.Store(currentVersion) // make sure this is under the lock, don't want it to change while we are updating the software
	// m.GetCurrentVersion() call will return the "updating to" version once the update is triggered if that version uses the same system version
	for version := range versionsSet {
		meta, ready, err := m.GetMetadataforVersion(version, true)
		if err != nil {
			if errors.Is(err, os.ErrNotExist) {
				logrus.Info("Version not yet available: ", version)
			} else {
				logrus.WithError(err).Error("Failed to get metadata for version: ", version)
			}
			m.versionMetadataCache.Invalidate(version) // don't know the current state so invalidate any old data
			continue
		}
		m.versionMetadataCache.Set(version, meta, ready)
	}

	// clean up any old data
	m.versionMetadataCache.Clean(versionsSet)
}

func (m *VersionMaintainer) GetMetadataforVersion(version string, alreadyLocked bool) (*meta.SoftwareVersionMetadata, bool, error) {
	metadata, err := meta.LoadSoftwareVersionMetadata(m.env, version)
	if err != nil {
		return nil, false, err
	}

	// call is blocked if update is in progress
	// don't do the heavyweight checksum test here, happens too frequently
	available := m.isVersionAvailable(version, alreadyLocked)

	return metadata, available, nil
}

func (m *VersionMaintainer) GetCurrentVersionMetadata() (*meta.SoftwareVersionMetadata, bool, error) {
	version := m.cachedCurrentVersion.Load().(string)
	return m.versionMetadataCache.Get(version)
}

func (m *VersionMaintainer) GetPreviousVersionMetadata() (*meta.SoftwareVersionMetadata, bool, error) {
	version := m.previousVersion.GetStringValue()
	return m.versionMetadataCache.Get(version)
}

func (m *VersionMaintainer) GetTargetVersionMetadata() (*meta.SoftwareVersionMetadata, bool, error) {
	version := m.targetVersion.GetStringValue()
	return m.versionMetadataCache.Get(version)
}

// prep everything for an update, but don't actually trigger it
func (m *VersionMaintainer) PrepareUpdate(ctx context.Context, version, reqId string) (err error) {

	// Prepare, Abort, and Trigger are all mutually exclusive
	m.twoPCLk.Lock()
	defer m.twoPCLk.Unlock()

	if reqId == m.twoPCReqId {
		logrus.Error("prepare request already processed, (Prepare)")
		return fmt.Errorf("prepare request already processed")
	} else if (m.twoPCState == Preparing || m.twoPCState == Prepared) && reqId != m.twoPCReqId { // update is already in progress and not the same request
		logrus.Error("Update already in progress, (Prepare)")
		return fmt.Errorf("update already in progress")
	} else if m.twoPCState == AbortFailed {
		logrus.Error("Update previously failed to abort, (Prepare), check version definition file and grub file")
		return fmt.Errorf("previous update failed to abort, check version definition file and grub file")
	}

	m.twoPCReqId = reqId // hold the request id until we reboot or abort

	m.lock.Lock() // hold lock until we reboot or abort

	// check if the rpc context timed out while we were waiting for the lock
	if ctx.Err() != nil {
		return fmt.Errorf("context error while waiting for lock: %v", ctx.Err())
	}

	logrus.Info("Preparing Update for: ", version)

	m.twoPCState = Preparing

	err = host.PingHostActor()

	if err != nil {
		if m.env.IsUnmanagedSystem() {
			logrus.WithError(err).Info("Host Actor Appears Offline")
		} else {
			logrus.WithError(err).Error("Host Actor Appears Offline")
			return fmt.Errorf("failed to ping Host Actor: %v", err)
		}
	}

	// do not use the cached state here, we want the most up to date state
	ready := m.isVersionAvailable(version, true)

	// do check that the version has been checked at least once
	checkedOnce := m.versionMetadataCache.GetCheckedOnce(version)

	if !ready || !checkedOnce {
		return fmt.Errorf("version not available: %v", version)
	}

	currentVersion, err := m.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("failed to get current software version: %v", err)
	}

	if currentVersion == version {
		return fmt.Errorf("version already current: %v", version)
	}

	systemVersion, err := bot_context.GetSystemVersion()
	if err != nil {
		return fmt.Errorf("failed to get current system version: %v", err)
	}

	metadata, err := meta.LoadSoftwareVersionMetadata(m.env, version)
	if err != nil {
		return fmt.Errorf("failed to get Software Version Metadata: %v", err)
	}

	if metadata.System != systemVersion {
		if m.upgradingSystem {
			return fmt.Errorf("system is currently upgrading")
		}

		systemInfo, err := system.GetSystemInfo(m.env)
		if err != nil {
			return fmt.Errorf("failed to get system info: %v", err)
		}

		if metadata.System != systemInfo.Versions.Other {
			return fmt.Errorf("requested New System Version is not available")
		}
	}

	// state changes:
	// 1. set the system version file
	// 2. set boot other

	def := bot_context.VersionDef{
		Tag: version,
	}
	err = def.Save(metadata.System)
	if err != nil {
		return fmt.Errorf("failed to save Version Def: %v", err)
	}

	if metadata.System != systemVersion {
		logrus.Info("Setting boot other")
		err = system.BootOtherVersion(m.env)

		if err != nil {
			return fmt.Errorf("failed to set Grub Boot Other: %v", err)
		}
	}

	logrus.Info("Update Prepared for: ", version)

	// success
	m.twoPCState = Prepared
	return nil
}

func (m *VersionMaintainer) AbortUpdate(version, reqId string) (err error) {

	// Prepare, Abort, and Trigger are all mutually exclusive
	m.twoPCLk.Lock()
	defer m.twoPCLk.Unlock()

	if reqId != m.twoPCReqId {
		logrus.Error("abort request does not mach active prepare request, (Abort)")
		return fmt.Errorf("abort request does not match active prepare request")
	} else if !(m.twoPCState == Prepared || m.twoPCState == Preparing) {
		logrus.Error("Update not prepared, (Abort)")
		return fmt.Errorf("update not prepared")
	}

	defer func() {
		if err != nil {
			// setting state to failed to abort failed will prevent the system from trying to update again
			// until someone manually fixes the version def file or the grub file. However, we release the maintainer lock
			// so the swmg can continue to maintain versions
			m.twoPCState = AbortFailed
		} else {
			m.twoPCState = Ready
		}
		m.lock.Unlock()
	}()

	logrus.Info("Aborting Update")

	// state changes to revert:
	// 1. set the system version file for the old current version
	// 2. set boot other to boot current partition

	metadata, err := meta.LoadSoftwareVersionMetadata(m.env, version)
	if err != nil {
		return fmt.Errorf("failed to get Software Version Metadata: %v", err)
	}

	// set the system version file for the old current version
	def := bot_context.VersionDef{
		Tag: version,
	}
	err = def.Save(metadata.System)
	if err != nil {
		return fmt.Errorf("failed to save Version Def: %v", err)
	}

	systemVersion, err := bot_context.GetSystemVersion()
	if err != nil {
		return fmt.Errorf("failed to get current system version: %v", err)
	}

	if metadata.System != systemVersion {
		// boot current
		logrus.Info("Setting boot current")
		err = system.BootCurrentVersion(m.env)

		if err != nil {
			return fmt.Errorf("failed to set Grub Boot Current: %v", err)
		}
	}

	logrus.Info("Update Aborted")
	return err
}

func (m *VersionMaintainer) TriggerUpdate(version, reqId string) error {

	// Prepare, Abort, and Trigger are all mutually exclusive
	m.twoPCLk.Lock()
	defer m.twoPCLk.Unlock()

	if reqId != m.twoPCReqId {
		logrus.Error("trigger request does not mach active prepare request, (Trigger)")
		return fmt.Errorf("trigger request does not match active prepare request")
	} else if !(m.twoPCState == Prepared) {
		logrus.Error("Update not prepared, (Trigger)")
		return fmt.Errorf("update not prepared")
	}

	logrus.Info("Triggering Update for: ", version)

	// At this point we are ready to reboot and trigger the actual update
	// Do not unlock once past here since we expect the computer and processes to reboot
	go func() {
		for {
			time.Sleep(time.Second * 10)
			err := host.RebootHost()
			if err == nil {
				// Note this log may not show up if the reboot happens before we have time to log it
				logrus.Infof("Update %v successfully Triggered, Host Rebooting.", version)
				break
			} else {
				if m.env.IsUnmanagedSystem() {
					logrus.WithError(err).Errorf("Failed to reboot to trigger update to: %v, Unmanaged system requires manual reboot", version)
					break
				} else {
					logrus.WithError(err).Errorf("Failed to reboot to trigger update to: %v, will retry in 10 seconds", version)
				}
			}
		}
	}()

	return nil
}

func (m *VersionMaintainer) Reboot() error {
	logrus.Info("Rebooting")
	err := host.PingHostActor()
	if err != nil {
		logrus.WithError(err).Error("Host Actor Appears Offline")
		return fmt.Errorf("failed to ping Host Actor: %v", err)
	}
	go func() {
		for {
			time.Sleep(time.Second * 10)
			err := host.RebootHost()
			if err == nil {
				// Note this log may not show up if the reboot happens before we have time to log it
				logrus.Infof("Host Rebooting")
				break
			} else {
				logrus.WithError(err).Errorf("Failed to reboot, will retry in 10 seconds")
			}
		}
	}()
	return nil
}

func (m *VersionMaintainer) IsUpdating() bool {
	m.twoPCLk.Lock()
	defer m.twoPCLk.Unlock()
	return m.twoPCState == Preparing || m.twoPCState == Prepared
}

// todo: this function is pretty much copy-paste from the module orchestrator in command, should be moved to a common package
func readModulesHashFromRedis(redisClient *redis.Client) (map[string]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	hash, err := redisClient.HGetAllWithContext(ctx, "module_orchestration/modules")
	if err != nil {
		logrus.WithError(err).Error("failed to read modules from redis")
		return nil, err
	}
	return hash, nil
}

func (m *VersionMaintainer) CleanOldModuleSerialKeys() {
	getKeysFunc := func(pattern string) ([]string, error) {
		ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
		defer cancel()
		return m.redis.KeysWithContext(ctx, pattern)
	}

	deleteKeysFunc := func(key string) error {
		ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
		defer cancel()
		return m.redis.DelWithContext(ctx, key)
	}

	hashes, err := readModulesHashFromRedis(m.redis)
	if err != nil {
		logrus.WithError(err).Error("failed to read assigned modules from redis")
		return
	}

	dockerKeyPatterns := []string{
		RegistryDownloadStatusKey,
		KnownVersionsKey,
	}
	for _, pattern := range dockerKeyPatterns {
		// replace the %v with * in the pattern
		pattern = fmt.Sprintf(pattern, "*", "*")
		keys, err := getKeysFunc(pattern)
		if err != nil {
			logrus.WithError(err).Errorf("failed to get dockerKeyPattern from redis: %v", pattern)
			continue
		}
		for _, key := range keys {
			// get the serial from the key
			serial := strings.Split(key, "/")[1]
			if serial == "command" { // don't nuke commands download status
				continue
			} else if _, ok := hashes[serial]; !ok {
				err := deleteKeysFunc(key)
				if err != nil {
					logrus.WithError(err).Errorf("failed to delete key: %v", key)
				}
			}
		}
	}
}

package registry

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/carbonrobotics/robot/golang/lib/environment"
)

func TestGetSourcePrefix(t *testing.T) {
	tests := []struct {
		name           string
		MakaRole       string
		expectedPrefix string
	}{
		{
			"bud",
			"bud",
			RegistryPrefix,
		},
		{
			"row",
			"row",
			"10.10.3.1:5000/robot/",
		},
		{
			"row-primary",
			"row-primary",
			"10.10.3.1:5000/robot/",
		},
		{
			"row-secondary",
			"row-secondary",
			"10.10.3.1:5000/robot/",
		},
		{
			"command",
			"command",
			RegistryPrefix,
		},
		{
			"default",
			"asdf fjkdlsfj asdlfkj",
			RegistryPrefix,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := GetSourcePrefix(&environment.Robot{MakaRole: test.MakaRole})
			assert.Equal(t, test.expectedPrefix, got)
		})
	}
}

func TestGetTargetPrefix(t *testing.T) {
	tests := []struct {
		name           string
		MakaRole       string
		expectedPrefix string
	}{
		{
			"bud",
			"bud",
			"127.0.0.1:5000/robot/",
		},
		{
			"row",
			"row",
			RegistryPrefix,
		},
		{
			"row-primary",
			"row-primary",
			RegistryPrefix,
		},
		{
			"row-secondary",
			"row-secondary",
			RegistryPrefix,
		},
		{
			"command",
			"command",
			"10.10.3.1:5000/robot/",
		},
		{
			"default",
			"foo bar asdfds",
			RegistryPrefix,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := GetTargetPrefix(&environment.Robot{MakaRole: test.MakaRole})
			assert.Equal(t, test.expectedPrefix, got)
		})
	}
}

package registry

import "github.com/carbonrobotics/robot/golang/lib/environment"

const (
	RegistryPrefix = "ghcr.io/carbonrobotics/robot/"
)

func GetSourcePrefix(env *environment.Robot) string {
	switch environment.CarbonRole(env.MakaRole) {
	case environment.CarbonRoleBud:
		return RegistryPrefix
	case environment.CarbonRoleRow, environment.CarbonRoleRowPrimary, environment.CarbonRoleRowSecondary, environment.CarbonRoleModule:
		return "10.10.3.1:5000/robot/"
	case environment.CarbonRoleCommand:
		return RegistryPrefix
	case environment.CarbonRoleRtc:
		return RegistryPrefix
	default:
		return RegistryPrefix
	}
}

func GetTargetPrefix(env *environment.Robot) string {
	switch environment.CarbonRole(env.MakaRole) {
	case environment.CarbonRoleBud:
		return "127.0.0.1:5000/robot/"
	case environment.CarbonRoleRow, environment.CarbonRoleRowPrimary, environment.CarbonRoleRowSecondary, environment.CarbonRoleModule:
		return RegistryPrefix
	case environment.CarbonRoleCommand:
		return "10.10.3.1:5000/robot/"
	case environment.CarbonRoleRtc:
		return RegistryPrefix
	default:
		return RegistryPrefix
	}
}

package system_version

import (
	"os"
	"os/exec"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/carbonrobotics/robot/golang/software_manager/system"
	"github.com/sirupsen/logrus"
)

func PrepareSystemVersion(env *environment.Robot, version string, softwareManagerClient *software_manager_client.SoftwareManagerClient) error {
	// Special Case for non-managed OS robots
	if env.IsUnmanagedSystem() {
		logrus.Info("Pretending to Prepare New System Version")
		return nil
	}

	botstrapType, err := system.GetSystemType()

	if err != nil {
		return err
	}

	if environment.CarbonRole(env.MakaRole) == environment.CarbonRoleCommand {
		// Aug 2022: we have an unsolved bug where carbon debian packages
		// fail validation during download. The /data/archives/carbon directory
		// is used by apt-cacher-ng (apt http proxy), and during the buggy scenario
		// it contains packages from several carbon versions.
		// It is probably related to version numbers as specified inside DEBIAN/control files, however we weren't
		// able to solve it completely.
		// This is a workaround.
		err = os.RemoveAll("/apt_archives/carbon") // mapped to /data/archives/carbon on host
		if err != nil {
			logrus.WithError(err).Warn("Could not remove apt_cacher_ng cache at /data/archives/carbon, packages may be reported as corrupted. Please remove the folder manually in this case.")
		}
	}

	var cmd *exec.Cmd
	switch environment.CarbonGen(env.MakaGen) {
	case environment.CarbonGenBud:
		// Mar 2023: Disable apt-proxy for Bud until all issues solved
		cmd = exec.Command(system.BOTSTRAP_EXEC, "prepare", "--version", version, "--type", botstrapType, "--proxy", "")
	default:
		cmd = exec.Command(system.BOTSTRAP_EXEC, "prepare", "--version", version, "--type", botstrapType)
	}

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err = cmd.Run()
	if err != nil {
		logrus.WithError(err).Warnf("%v exited with error, will clean apt cache", system.BOTSTRAP_EXEC)
		if softwareManagerClient != nil {
			softwareManagerClient.ClearPackagesCache()
		}
	}
	return err
}

package meta

import (
	"bytes"
	"fmt"
	"os"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/software_manager/system"
	"github.com/natefinch/atomic"
	"gopkg.in/yaml.v2"
)

const VERSION_METADATA_DIR = "/data/software_manager/versions"

type SoftwareVersionMetadata struct {
	Tag        string   `yaml:"tag"`
	Containers []string `yaml:"containers"`
	System     string   `yaml:"system"`
}

func (m *SoftwareVersionMetadata) Save() error {
	os.MkdirAll(VERSION_METADATA_DIR, os.ModePerm)
	data, err := yaml.Marshal(m)

	if err != nil {
		return err
	}

	reader := bytes.NewReader(data)
	err = atomic.WriteFile(fmt.Sprintf("%s/%s.yaml", VERSION_METADATA_DIR, m.Tag), reader)

	if err != nil {
		return err
	}

	return nil
}

func LoadSoftwareVersionMetadata(env *environment.Robot, version string) (*SoftwareVersionMetadata, error) {
	os.MkdirAll(VERSION_METADATA_DIR, os.ModePerm)
	yfile, err := os.ReadFile(fmt.Sprintf("%s/%s.yaml", VERSION_METADATA_DIR, version))

	if err != nil {
		return nil, err
	}

	meta := &SoftwareVersionMetadata{}

	err = yaml.Unmarshal(yfile, meta)

	if err != nil {
		return nil, err
	}

	// Special Case for non-managed OS robots
	if env.IsUnmanagedSystem() {
		meta.System = system.UnmanagedSystemVersion
	}

	return meta, nil
}

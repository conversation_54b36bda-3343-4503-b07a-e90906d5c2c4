package meta

import (
	"context"
	"fmt"
	"net"
	"runtime"
	"strings"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/version_metadata"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	"golang.org/x/oauth2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type VersionMetadataClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client version_metadata.VersionMetadataServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *VersionMetadataClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewVersionMetadataClient(addr string, tokenSource oauth2.TokenSource) *VersionMetadataClient {
	if _, port, _ := net.SplitHostPort(addr); port == "" {
		addr = net.JoinHostPort(addr, "443") // default to 443
	}
	client := &VersionMetadataClient{
		addr: addr,
	}
	secure := strings.HasSuffix(addr, ":443")
	bundle := auth.NewCredentialsBundle(tokenSource, secure)
	client.opts = append(client.opts, grpc.WithCredentialsBundle(bundle))
	if !secure {
		client.opts = append(client.opts, grpc.WithUnaryInterceptor(func(
			ctx context.Context,
			method string,
			req interface{},
			reply interface{},
			cc *grpc.ClientConn,
			invoker grpc.UnaryInvoker,
			opts ...grpc.CallOption,
		) error {
			token, err := tokenSource.Token()
			if err != nil {
				return err
			}
			ctx = metadata.AppendToOutgoingContext(ctx, "authorization", fmt.Sprintf("Bearer %s", token.AccessToken))
			return invoker(ctx, method, req, reply, cc, opts...)
		}))
	}
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *VersionMetadataClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *VersionMetadataClient) getClient() (version_metadata.VersionMetadataServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = version_metadata.NewVersionMetadataServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *VersionMetadataClient) sendRequest(f func(version_metadata.VersionMetadataServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

func (c *VersionMetadataClient) GetVersionMetadata(version string, gen string) (*version_metadata.VersionMetadata, error) {
	metadata, err := c.sendRequest(func(client version_metadata.VersionMetadataServiceClient) (interface{}, error) {
		return client.GetVersionMetadata(context.Background(), &version_metadata.GetVersionMetadataRequest{
			Version: version,
			Gen:     gen,
		})
	})

	if err != nil {
		return nil, err
	}

	return metadata.(*version_metadata.VersionMetadata), err
}

func (c *VersionMetadataClient) UploadVersionMetadata(version string, gen string, vm *version_metadata.VersionMetadata) (*version_metadata.Empty, error) {
	_, err := c.sendRequest(func(client version_metadata.VersionMetadataServiceClient) (interface{}, error) {
		return client.UploadVersionMetadata(context.Background(), &version_metadata.UploadVersionMetadataRequest{
			Version:         version,
			Gen:             gen,
			VersionMetadata: vm,
		})
	})

	if err != nil {
		return nil, err
	}

	return &version_metadata.Empty{}, err
}

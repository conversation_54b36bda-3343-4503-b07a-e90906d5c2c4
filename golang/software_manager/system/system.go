package system

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
)

const BOTSTRAP_EXEC = "/old/carbon/sbin/carbon-upgrade"
const SYS_TYPE_FILE = "/old/carbon/etc/carbon.json"
const SYS_TYPE_KEY = "carbon_installed_type"

type SystemVersions struct {
	Current string `json:"current"`
	Other   string `json:"other"`
}

type SystemPartitions struct {
	Current string `json:"current"`
	Other   string `json:"other"`
}

type SystemInfo struct {
	Versions   SystemVersions   `json:"versions"`
	Partitions SystemPartitions `json:"partitions"`
}

const (
	UnmanagedSystemVersion string = "s1.0.0"
)

func GetSystemInfo(env *environment.Robot) (*SystemInfo, error) {
	// Special Case for non-managed OS robots
	if env.IsUnmanagedSystem() {
		return &SystemInfo{
			Versions: SystemVersions{
				Current: UnmanagedSystemVersion,
				Other:   UnmanagedSystemVersion,
			},
			Partitions: SystemPartitions{
				Current: "A",
				Other:   "B",
			},
		}, nil
	}

	cmd := exec.Command(BOTSTRAP_EXEC, "info")

	var out bytes.Buffer
	cmd.Stdout = &out

	err := cmd.Run()

	if err != nil {
		return nil, err
	}

	info := &SystemInfo{}
	err = json.Unmarshal(out.Bytes(), info)

	if err != nil {
		return nil, err
	}

	return info, nil
}

func GetSystemType() (string, error) {
	jsonFile, err := os.Open(SYS_TYPE_FILE)
	if err != nil {
		return "", err
	}
	// defer the closing of our jsonFile so that we can parse it later on
	defer jsonFile.Close()
	byteValue, _ := ioutil.ReadAll(jsonFile)

	var result map[string]interface{}
	json.Unmarshal([]byte(byteValue), &result)
	val, ok := result[SYS_TYPE_KEY]
	if !ok {
		return "", fmt.Errorf("%v does not contain %v", SYS_TYPE_FILE, SYS_TYPE_KEY)
	}
	data, str_ok := val.(string)
	if !str_ok {
		return "", fmt.Errorf("Cannot parse robot type from %v", val)
	}
	return data, nil
}

func CommitCurrentVersion(env *environment.Robot) error {
	// Special Case for non-managed OS robots
	if env.IsUnmanagedSystem() {
		logrus.Info("Pretending to Commit Current System Version")
		return nil
	}

	return execCommand(BOTSTRAP_EXEC, "commit")
}

func BootOtherVersion(env *environment.Robot) error {
	// Special Case for non-managed OS robots
	if env.IsUnmanagedSystem() {
		logrus.Info("Pretending to Boot Other System Version")
		return nil
	}

	return execCommand(BOTSTRAP_EXEC, "boot-other")
}

func BootCurrentVersion(env *environment.Robot) error {
	// Special Case for non-managed OS robots
	if env.IsUnmanagedSystem() {
		logrus.Info("Pretending to Boot Current System Version")
		return nil
	}

	return execCommand(BOTSTRAP_EXEC, "boot-current")
}

func execCommand(command string, args ...string) error {
	cmd := exec.Command(command, args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

file(GLOB PROTO_FILES *.proto)

foreach(PROTO_FILE ${PROTO_FILES})
CompileProto(${PROTO_FILE} GENERATED_PATH GOPKG proto/rtc_sim_UI LANGS grpc_python python mypy go go-grpc cpp grpc)
endforeach()

string(REGEX REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "${GENERATED_PATH}" PROTO_GEN "${PROTO_FILES}")
string(REGEX REPLACE "[.]proto" ".pb.cc" PROTO_SOURCES "${PROTO_GEN}")
string(REGEX REPLACE "[.]proto" ".grpc.pb.cc" GRPC_SOURCES "${PROTO_GEN}")

add_library(rtc_simulator_ui_proto ${PROTO_SOURCES} ${GRPC_SOURCES})
target_compile_options(rtc_simulator_ui_proto PRIVATE "-w")
target_link_libraries(rtc_simulator_ui_proto grpc++ protobuf)
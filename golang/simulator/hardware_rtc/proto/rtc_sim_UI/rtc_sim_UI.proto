syntax = "proto3";

package carbon.rtc_sim_UI;
option go_package = "proto/rtc_sim_UI";

enum Gear {
    // Must match enum in lib/drivers/nanopb/proto/ots_tractor.proto
    GEAR_PARK = 0;
    GEAR_REVERSE = 1;
    GEAR_NEUTRAL = 2;
    GEAR_FORWARD = 3;
    GEAR_POWERZERO = 4;
}
enum Lights {
    // Must match enum in lib/drivers/nanopb/proto/ots_tractor.proto
    LIGHTS_OFF = 0;
    LIGHTS_LOW = 1;
    LIGHTS_HIGH = 2;
}

message Empty {}

message EnableRequest {
    bool enabled = 1;
}

message SafetySensorsRequest {
    bool sensor_1 = 1;
    bool sensor_2 = 2;
    bool sensor_3 = 3;
    bool sensor_4 = 4;
}

message SetSpeedRequest{
    float speed_mph = 1;
}
message SetGearRequest{
    Gear gear = 1;
}
message SetLightsRequest {
    Lights lights = 1;
}
message SetEngineRpmRequest {
    int32 rpms = 1;
}

message SetErrorFlagRequest {
    int32 error_flag = 1;
}
message SetFuelLevelRequest {
    float fuel_level = 1;
}

service RTCSimulatorUIService {
    rpc SetInCabSwitch(EnableRequest) returns (Empty) {};
    rpc SetSafetySensors(SafetySensorsRequest) returns (Empty) {};
    rpc SetSpeed(SetSpeedRequest) returns (Empty) {};
    rpc SetGear(SetGearRequest) returns (Empty) {};
    rpc SetLights(SetLightsRequest) returns (Empty) {};
    rpc SetEstop(EnableRequest) returns (Empty) {};
    rpc SetEngineRpm(SetEngineRpmRequest) returns (Empty) {};
    rpc SetFrontPto(EnableRequest) returns (Empty) {};
    rpc SetRearPto(EnableRequest) returns (Empty) {};
    rpc SetErrorFlag(SetErrorFlagRequest) returns (Empty) {};
    rpc SetFuelLevel(SetFuelLevelRequest) returns (Empty) {};
}
package motion_controller

import (
	"sync"

	"github.com/carbonrobotics/robot/golang/lib/config"
)

type DataToGameState struct {
	Velocity      float32 `json:"velocity"` // this is speed + sign (fwd/rev)
	WheelAngleDeg float32 `json:"wheel_angle_deg"`
}

type DataToSetOnGame struct {
	dataToGameState *DataToGameState
	lock            sync.Mutex
}

type DataToHH struct {
	speed         float32 // in MPH
	wheelAngleDeg float32

	lock sync.Mutex
}

type MotionController struct {
	dataToSetOnGame *DataToSetOnGame
	dataToHH        *DataToHH
	gameFlagEnabled bool
}

func NewDataToSetOnGame() *DataToSetOnGame {
	return &DataToSetOnGame{dataToGameState: &DataToGameState{Velocity: 0, WheelAngleDeg: 0}}
}

func NewDataToHH() *DataToHH {
	return &DataToHH{speed: 0, wheelAngleDeg: 0}
}

func NewMotionController(configSubscriber *config.ConfigSubscriber) *MotionController {
	gameFlag := false
	if configSubscriber != nil {
		gameFlag = configSubscriber.GetConfigNode("simulator", "game_server_enabled").GetBoolValue()
	}

	return &MotionController{dataToSetOnGame: NewDataToSetOnGame(), dataToHH: NewDataToHH(), gameFlagEnabled: gameFlag}
}

func (c *MotionController) SetSpeed(speed float32, isGearReverse bool) {
	if c.gameFlagEnabled {
		mult := float32(1.0)
		if isGearReverse {
			mult = float32(-1.0)
		}

		c.dataToSetOnGame.lock.Lock()
		defer c.dataToSetOnGame.lock.Unlock()
		c.dataToSetOnGame.dataToGameState.Velocity = speed * mult
	} else {
		c.dataToHH.lock.Lock()
		defer c.dataToHH.lock.Unlock()
		c.dataToHH.speed = speed
	}
}

func (c *MotionController) GetSpeed() float32 {
	c.dataToHH.lock.Lock()
	defer c.dataToHH.lock.Unlock()
	return c.dataToHH.speed
}

func (c *MotionController) SetWheelAngle(wheelAngle float32) {
	if c.gameFlagEnabled {
		c.dataToSetOnGame.lock.Lock()
		defer c.dataToSetOnGame.lock.Unlock()
		c.dataToSetOnGame.dataToGameState.WheelAngleDeg = wheelAngle
	} else {
		c.dataToHH.lock.Lock()
		defer c.dataToHH.lock.Unlock()
		c.dataToHH.wheelAngleDeg = wheelAngle
	}
}

func (c *MotionController) GetWheelAngle() float32 {
	c.dataToHH.lock.Lock()
	defer c.dataToHH.lock.Unlock()
	return c.dataToHH.wheelAngleDeg
}

func (c *MotionController) IsGameServerEnabled() bool {
	return c.gameFlagEnabled
}

func (c *MotionController) SetSpeedHH(speed float32) {
	c.dataToHH.lock.Lock()
	defer c.dataToHH.lock.Unlock()
	c.dataToHH.speed = speed
}

func (c *MotionController) SetWheelAngleHH(wheelAngle float32) {
	c.dataToHH.lock.Lock()
	defer c.dataToHH.lock.Unlock()
	c.dataToHH.wheelAngleDeg = wheelAngle
}

func (c *MotionController) GetDataToSetOnGame() *DataToGameState {
	c.dataToSetOnGame.lock.Lock()
	defer c.dataToSetOnGame.lock.Unlock()
	return &DataToGameState{Velocity: c.dataToSetOnGame.dataToGameState.Velocity, WheelAngleDeg: c.dataToSetOnGame.dataToGameState.WheelAngleDeg}
}

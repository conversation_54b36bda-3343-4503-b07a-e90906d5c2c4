package gps

import (
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	nanopb "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/benjamin_gps_board"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/gps"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/gps_provider"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

const mToMm = 1000

type GPSBoardSim struct {
	gpsProvider gps_provider.GpsProvider

	stopSig []chan bool
	wg      sync.WaitGroup
}

func NewGPSBoardSim(gpsProvider gps_provider.GpsProvider) *GPSBoardSim {
	return &GPSBoardSim{
		gpsProvider: gpsProvider,
		stopSig:     make([]chan bool, 0),
	}
}

func (s *GPSBoardSim) Start() {
	logrus.Info("GPS board simulation started")
	s.listenAndServe()
}

func (s *GPSBoardSim) Stop() {
	for _, channel := range s.stopSig {
		channel <- true
	}
	s.wg.Wait()
}

func (s *GPSBoardSim) waitForTerminateSignal(sleepTime time.Duration, channel chan bool) bool {
	select {
	case <-channel:
		logrus.Infof("GPS_Sim: received request to terminate")
		return true
	case <-time.After(sleepTime):
		return false
	}
}

func (s *GPSBoardSim) listenAndServe() {
	stopSig := make(chan bool, 1)
	s.stopSig = append(s.stopSig, stopSig)
	s.wg.Add(1)
	go func() {
		sleepTime := 5 * time.Millisecond

		pc, err := net.ListenPacket("udp", ":64547")
		if err != nil {
			log.Fatal(err)
		}
		defer pc.Close()

		for {
			if s.waitForTerminateSignal(sleepTime, stopSig) {
				break
			}
			buf := make([]byte, 2048)
			n, addr, err := pc.ReadFrom(buf)
			if err != nil {
				continue
			}
			go s.serve(pc, addr, buf[:n])
		}
		s.wg.Done()
	}()
}

func (s *GPSBoardSim) serve(pc net.PacketConn, addr net.Addr, buf []byte) {
	logrus.Debugf("GPS_Sim: received request from %v, content_len=(%v)", addr, len(buf))

	req := &nanopb.Request{}
	proto.Unmarshal(buf, req)
	var err error = nil
	var reply *nanopb.Reply = &nanopb.Reply{
		Header: &request.RequestHeader{
			RequestId: req.GetHeader().RequestId,
		}}

	logrus.Debugf("GPS_Sim: unmarshalled to id=%v, nils gps %v heading %v ping %v", req.GetHeader().RequestId, req.GetGps() == nil, req.GetHeading() == nil, req.GetPing() == nil)
	switch typedReq := req.Request.(type) {
	case *nanopb.Request_Ping:
		reply.Reply = &nanopb.Reply_Pong{Pong: &diagnostic.Pong{X: typedReq.Ping.GetX()}}
	case *nanopb.Request_Gps:
		curLLA := s.gpsProvider.GetLLA()
		reply.Reply = &nanopb.Reply_Gps{
			Gps: &gps.Reply{
				Reply: &gps.Reply_Position{
					Position: &gps.Position_Reply{
						Latitude:      curLLA.Lat,
						Longitude:     curLLA.Lng,
						HeightMm:      int32(curLLA.Alt * mToMm),
						TimestampMs:   uint64(time.Now().UnixMilli()),
						HaveApproxFix: true,
						HaveFix:       true,
						FixType:       3,
						FixFlags:      0b10000011,
						Dual: &gps.DualGpsData{
							GnssValid:       true,
							DiffCorrections: true,
							IsMovingBase:    true,
							CarrierPhase:    2,
							Heading:         &gps.ValueWithAccuracy{Value: curLLA.Heading},
						},
					},
				},
			},
		}
	default:
		err = fmt.Errorf("Unknown msg type (%T)", typedReq)
	}
	if err != nil {
		logrus.Errorf("Failed to handle msg %v Err: %v", req, err)
		return
	}
	msg, err := proto.Marshal(reply)
	if err != nil {
		logrus.Error(err)
	} else {
		n, err := pc.WriteTo(msg, addr)
		logrus.Debugf("GPS_sim: Write done to %v with %v and err=%v", addr, n, err)
	}
}

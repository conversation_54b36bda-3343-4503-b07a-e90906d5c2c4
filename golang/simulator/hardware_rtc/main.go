package main

import (
	"context"
	"fmt"
	"net"
	"os/signal"
	"sync"
	"syscall"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/game_server"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/gps"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/gps_provider"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/hasselhoff"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/motion_controller"
	web_server_rtc "github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/web_server"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

const WebServerPort = 8091 // if you change this, you must change envoy_rtc.yaml
const CfgServicePort = 61001
const GameServerPort = 8092 // if you change this, you must change the port in the autotractor-simulator

type Stopable interface {
	Stop()
}

func bindAndListen(port int) (*net.Listener, error) {
	addr := fmt.Sprintf("0.0.0.0:%d", port)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.WithError(err).Fatalf("failed to listen on port: %v", port)
	}
	return &lis, err
}

func serve(grpcServer *grpc.Server, listener *net.Listener, wg *sync.WaitGroup) {
	// Serve() is blocking hence go routine
	err := grpcServer.Serve(*listener)
	if err != nil {
		logrus.WithError(err).Fatalf("Failed to Serve GRPC Server")
	}
	wg.Done()
}

func main() {
	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()
	robot, err := environment.GetRobot()
	if err != nil {
		logrus.Fatalf("Failed to parse environment: %v", err)
		panic(err)
	}

	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)

	var configSubscriber *config.ConfigSubscriber = nil
	if robot.IsSimulator() {
		configSubscriber = config.NewConfigSubscriber(config.MakeRobotLocalAddr(CfgServicePort))
		configSubscriber.AddConfigTree("simulator", "simulator", "services/simulator.yaml")
		configSubscriber.Start()
		configSubscriber.WaitUntilReady()
	}

	var wg sync.WaitGroup

	webServerListener, err := bindAndListen(WebServerPort)
	if err != nil {
		return
	}
	var stopables []Stopable
	var opts []grpc.ServerOption
	wsGRPCServer := grpc.NewServer(opts...)
	stopables = append(stopables, wsGRPCServer)
	controller := motion_controller.NewMotionController(configSubscriber)
	hh := hasselhoff.NewHHBoardSim(configSubscriber, controller)
	webServer := web_server_rtc.NewRTCSimulatorUIService(wsGRPCServer, hh)

	var provider gps_provider.GpsProvider = nil
	gameServerEnabled := controller.IsGameServerEnabled()
	if gameServerEnabled {
		gameGpsProvider := game_server.NewGameGpsProvider()
		gameServer := game_server.NewGameServer(GameServerPort, gameGpsProvider, controller, &wg)
		go gameServer.Start()
		stopables = append(stopables, gameServer)
		provider = gameGpsProvider
	} else {
		localGpsProvider := gps_provider.NewLocalGpsProvider(hh, configSubscriber)
		localGpsProvider.Start(&wg)
		stopables = append(stopables, localGpsProvider)
		provider = localGpsProvider
	}

	gpsBoard := gps.NewGPSBoardSim(provider)

	go webServer.Start()
	stopables = append(stopables, webServer)
	gpsBoard.Start()
	stopables = append(stopables, gpsBoard)
	wg.Add(1)
	go serve(wsGRPCServer, webServerListener, &wg)

	<-stopCtx.Done()

	for _, stopable := range stopables {
		stopable.Stop()
	}

	wg.Wait()
	logrus.Infof("Hardware Simulator Shut Down Gracefully")
}

package game_server

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/gps_provider"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/gps_utils"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/motion_controller"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

type DataToSetOnHwSim struct {
	Lat           float64 `json:"lat"`
	Lng           float64 `json:"lng"`
	Heading       float64 `json:"yaw"`
	Speed         float32 `json:"speed"`
	WheelAngleDeg float32 `json:"wheel_angle_deg"`
}

type gameGpsProvider struct {
	lat     float64 // degrees
	lng     float64 // degrees
	heading float64 // radians
	lock    sync.Mutex
}

type GameServer struct {
	server      *http.Server
	wg          *sync.WaitGroup
	redisClient *redis.Client
}

type LatLng struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type FarmPoints struct {
	Points []LatLng `json:"points"`
}

type HardwareBundle struct {
	gpsProvider *gameGpsProvider
	controller  *motion_controller.MotionController
}

func NewGameGpsProvider() *gameGpsProvider {
	return &gameGpsProvider{
		lat:     0,
		lng:     0,
		heading: gps_utils.DefaultTheta,
	}
}

func (g *gameGpsProvider) setBotLLH(lat float64, lng float64, heading float64) {
	g.lock.Lock()
	defer g.lock.Unlock()

	g.lat = lat
	g.lng = lng
	g.heading = gps_utils.XYAngleToAzimuth(heading)
}

func (g *gameGpsProvider) GetLLA() gps_provider.LLA {
	g.lock.Lock()
	defer g.lock.Unlock()

	return gps_provider.LLA{
		Lat:     g.lat,
		Lng:     g.lng,
		Alt:     0,
		Heading: g.heading,
	}
}

func (hw HardwareBundle) getDataToSetOnGameHandler(w http.ResponseWriter, r *http.Request) {
	jsonData, err := json.Marshal(*(hw.controller.GetDataToSetOnGame()))
	if err != nil {
		errMsg := "Error marshaling JSON"
		logrus.Errorf("%s: %s", errMsg, err)
		http.Error(w, errMsg, http.StatusInternalServerError)
		return
	}

	fmt.Fprint(w, string(jsonData))
}

func (hw HardwareBundle) setDataOnHwSimHandler(w http.ResponseWriter, r *http.Request) {
	var gameData DataToSetOnHwSim
	err := json.NewDecoder(r.Body).Decode(&gameData)
	if err != nil {
		errMsg := "Error unmarshaling JSON"
		logrus.Errorf("%s: %s", errMsg, err)
		http.Error(w, errMsg, http.StatusBadRequest)
		return
	}

	hw.gpsProvider.setBotLLH(gameData.Lat, gameData.Lng, gameData.Heading)
	hw.controller.SetSpeedHH(gameData.Speed)
	hw.controller.SetWheelAngleHH(gameData.WheelAngleDeg)

	fmt.Fprint(w, "")
}

func NewGameServer(port int, gpsProvider *gameGpsProvider, controller *motion_controller.MotionController, wg *sync.WaitGroup) *GameServer {
	hardwareBundle := HardwareBundle{gpsProvider: gpsProvider, controller: controller}

	router := mux.NewRouter()

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: router,
	}

	robot, err := environment.GetRobot()
	if err != nil {
		logrus.Fatalf("Failed to parse environment: %v", err)
	}

	redisClient := redis.New(robot)
	game_server := &GameServer{
		server:      server,
		wg:          wg,
		redisClient: redisClient,
	}

	if controller.IsGameServerEnabled() {
		router.HandleFunc("/", hardwareBundle.getDataToSetOnGameHandler).Methods("GET")
		router.HandleFunc("/setGameData", hardwareBundle.setDataOnHwSimHandler).Methods("POST")
		router.HandleFunc("/getFarmPoints", game_server.getFarmPointsHandler).Methods("GET")
	}

	return game_server
}

func (gameServer *GameServer) getFarmPointsHandler(w http.ResponseWriter, r *http.Request) {
	value, err := gameServer.redisClient.Get("/farm/active")
	if err != nil {
		errMsg := "Error getting key from redis"
		logrus.Errorf("%s: %s", errMsg, err)
		http.Error(w, errMsg, http.StatusInternalServerError)
		return
	}

	data := []byte(value)
	farm := &portal.Farm{}
	if err := proto.Unmarshal(data, farm); err != nil {
		errMsg := "Error unmarshaling Farm proto"
		logrus.Errorf("%s: %s", errMsg, err)
		http.Error(w, errMsg, http.StatusInternalServerError)
		return
	}

	var points FarmPoints
	for _, pointDef := range farm.PointDefs {
		points.Points = append(points.Points, LatLng{Lat: pointDef.Point.Lat, Lng: pointDef.Point.Lng})
	}

	jsonData, err := json.Marshal(points)
	if err != nil {
		errMsg := "Error marshaling JSON"
		logrus.Errorf("%s: %s", errMsg, err)
		http.Error(w, errMsg, http.StatusInternalServerError)
		return
	}

	fmt.Fprint(w, string(jsonData))
}

func (gameServer *GameServer) Start() {
	gameServer.wg.Add(1)
	go func() {
		if err := gameServer.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("listen: %s\n", err)
		}
	}()

	logrus.Infof("Game server started")
}

func (gameServer *GameServer) Stop() {
	if err := gameServer.server.Shutdown(context.Background()); err != nil {
		logrus.Errorf("Server Shutdown Failed:%+v", err)
	} else {
		logrus.Infof("Game server exited successfully")
	}

	gameServer.wg.Done()
}

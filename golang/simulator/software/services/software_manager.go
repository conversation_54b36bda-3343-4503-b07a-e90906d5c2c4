package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/software_manager"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type SoftwareManagerState struct {
	UseProxy bool                                      `json:"use_proxy"`
	Current  *software_manager.SoftwareVersionMetadata `json:"current"`
	Target   *software_manager.SoftwareVersionMetadata `json:"target"`
	Previous *software_manager.SoftwareVersionMetadata `json:"previous"`
}

type SoftwareManagerService struct {
	SimulatedServiceImpl
	software_manager.UnimplementedSoftwareManagerServiceServer
	proxyClient *software_manager_client.SoftwareManagerClient

	state *SoftwareManagerState
}

func NewSoftwareManagerService(id uint32, grpcServer *grpc.Server) *SoftwareManagerService {
	s := &SoftwareManagerService{
		SimulatedServiceImpl: SimulatedServiceImpl{id},
		proxyClient:          software_manager_client.NewSoftwareManagerClient(fmt.Sprintf("%v:%v", CommandAddr, SWMGPort)),
		state:                &SoftwareManagerState{},
	}
	software_manager.RegisterSoftwareManagerServiceServer(grpcServer, s)
	return s
}

func (s *SoftwareManagerService) GetVersionsSummary(context.Context, *software_manager.VersionSummaryRequest) (summary *software_manager.VersionSummaryReply, err error) {
	err2 := s.LoadState(s.state)
	if err2 != nil {
		logrus.WithError(err2).Warn("Couldn't load state, defaulting to proxy")
	}
	if err2 != nil || s.state.UseProxy {
		summary, err = s.proxyClient.GetVersionsSummary()
		if err != nil {
			logrus.WithError(err).Warn("Couldn't get versions summary from commander")
		}
	} else {
		summary = &software_manager.VersionSummaryReply{
			Current:  s.state.Current,
			Target:   s.state.Target,
			Previous: s.state.Previous,
		}
	}
	return summary, err
}

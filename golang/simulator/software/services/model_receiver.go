package services

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"slices"
	"strings"

	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"github.com/carbonrobotics/robot/golang/lib/chip_manager"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

const (
	CVModelMetadataCacheDir = "/data/model_receiver/model_manager/model_cache"
	CVChipCacheDir          = "/data/model_receiver/embeddings/chip_cache"
)

type ModelReceiverState struct {
	IgnoredFiles []string `json:"ignored_files"`
}

type ModelReceiverService struct {
	SimulatedServiceImpl
	model_receiver.UnimplementedModelReceiverServer
	state *ModelReceiverState
}

func NewModelReceiverService(id uint32, grpcServer *grpc.Server) *ModelReceiverService {
	mr := &ModelReceiverService{
		SimulatedServiceImpl: SimulatedServiceImpl{id},
		state:                &ModelReceiverState{},
	}
	model_receiver.RegisterModelReceiverServer(grpcServer, mr)
	return mr
}

func (m *ModelReceiverService) GetDownloadedModels(context.Context, *model_receiver.Empty) (*model_receiver.DownloadedModelResponse, error) {
	resp := &model_receiver.DownloadedModelResponse{
		Models: make([]*model_receiver.Model, 0),
	}
	ignoredFiles := make([]string, 0)
	err := m.LoadState(m.state)
	if err != nil {
		logrus.Warnf("ModelReceiver: failed to load state, defaulting to all files: %v", err)
	} else {
		ignoredFiles = m.state.IgnoredFiles
	}

	files, err := os.ReadDir(CVModelMetadataCacheDir)
	if err != nil {
		return nil, err
	}

	// fetch models based on metadata files in model_manager cache
	for i, f := range files {
		if slices.Contains(ignoredFiles, f.Name()) {
			continue
		}
		if strings.HasSuffix(f.Name(), model_manager.MetaExt) {
			metaPath := filepath.Join(CVModelMetadataCacheDir, f.Name())
			model, err := model_manager.GetVerifyMetadata(metaPath)
			if err != nil {
				return nil, err
			}

			artifactIDs := make([]string, 0)

			for _, artifact := range model.ModelArtifacts {
				artifactIDs = append(artifactIDs, artifact.ModelManagerArtifactID())
			}
			resp.Models = append(resp.Models, &model_receiver.Model{
				Id:          model.ID,
				ArtifactIds: artifactIDs,
				ModelSha:    fmt.Sprintf("ModelSHA%v", i),
				MetadataSha: fmt.Sprintf("MetadataSHA%v", i),
			})
		}
	}
	return resp, nil
}

func (m *ModelReceiverService) DownloadModelMetadata(_ context.Context, req *model_receiver.DownloadModelMetadataRequest) (*model_receiver.Empty, error) {
	return &model_receiver.Empty{}, nil
}

func (m *ModelReceiverService) DownloadModelArtifact(_ context.Context, req *model_receiver.DownloadModelArtifactRequest) (*model_receiver.Empty, error) {
	return &model_receiver.Empty{}, nil
}

func (m *ModelReceiverService) DownloadChip(_ context.Context, req *model_receiver.DownloadChipRequest) (*model_receiver.Empty, error) {
	return &model_receiver.Empty{}, nil
}

func (m *ModelReceiverService) DownloadChipMetadata(_ context.Context, req *model_receiver.DownloadChipMetadataRequest) (*model_receiver.Empty, error) {
	return &model_receiver.Empty{}, nil
}

func (m *ModelReceiverService) GetDownloadedChips(_ context.Context, _ *model_receiver.Empty) (*model_receiver.GetDownloadedChipsResponse, error) {
	resp := &model_receiver.GetDownloadedChipsResponse{
		Chips: make([]*model_receiver.Chip, 0),
	}
	ignoredFiles := make([]string, 0)
	err := m.LoadState(m.state)
	if err != nil {
		logrus.Warnf("ModelReceiver: failed to load state, defaulting to all files: %v", err)
	} else {
		ignoredFiles = m.state.IgnoredFiles
	}

	files, err := os.ReadDir(CVChipCacheDir)
	if err != nil {
		return nil, err
	}

	// fetch models based on metadata files in model_manager cache
	for _, f := range files {
		if slices.Contains(ignoredFiles, f.Name()) {
			continue
		}
		if strings.HasSuffix(f.Name(), model_manager.MetaExt) {
			metaPath := filepath.Join(CVChipCacheDir, f.Name())
			chip, err := chip_manager.GetVerifyMetadata(metaPath)
			if err != nil {
				return nil, err
			}

			resp.Chips = append(resp.Chips, &model_receiver.Chip{
				Id: chip.Id,
			})
		}
	}
	return resp, nil
}

func (m *ModelReceiverService) RemoveChips(_ context.Context, req *model_receiver.RemoveChipsRequest) (*model_receiver.Empty, error) {
	return &model_receiver.Empty{}, nil
}

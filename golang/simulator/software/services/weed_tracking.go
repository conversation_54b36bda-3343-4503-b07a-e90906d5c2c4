package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/carbonrobotics/robot/golang/lib/weed_tracking_client"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type WeedTrackingState struct {
	UseProxy bool `json:"use_proxy"`

	// Plant Captcha
	PlantCaptchaStatus weed_tracking.PlantCaptchaStatus `json:"plant_captcha_status"`
	TotalImages        int32                            `json:"total_images"`
	ImagesTaken        int32                            `json:"images_taken"`
	MetadataTaken      int32                            `json:"metadata_taken"`
	StartSuccess       bool                             `json:"start_success"`
	RemoveDirSuccess   bool                             `json:"remove_dir_success"`
	CancelSuccess      bool                             `json:"cancel_success"`
}

type WeedTrackingService struct {
	SimulatedServiceImpl
	weed_tracking.UnimplementedWeedTrackingServiceServer

	proxyClient *weed_tracking_client.WeedTrackingClient
	state       *WeedTrackingState
}

func NewWeedTrackingService(id uint32, grpcServer *grpc.Server) *WeedTrackingService {
	s := &WeedTrackingService{
		SimulatedServiceImpl: SimulatedServiceImpl{id},
		proxyClient:          weed_tracking_client.NewWeedTrackingClient(fmt.Sprintf("%v:%v", Row1Addr, WTPort)),
		state: &WeedTrackingState{
			PlantCaptchaStatus: weed_tracking.PlantCaptchaStatus_CAPTCHA_STARTED,
		},
	}
	weed_tracking.RegisterWeedTrackingServiceServer(grpcServer, s)
	return s
}

func (s *WeedTrackingService) GetBands(ctx context.Context, req *weed_tracking.Empty) (*weed_tracking.BandDefinitions, error) {
	return s.proxyClient.GetBands()
}

func (s *WeedTrackingService) GetTargetingEnabled(ctx context.Context, req *weed_tracking.GetTargetingEnabledRequest) (*weed_tracking.GetTargetingEnabledResponse, error) {
	return s.proxyClient.GetTargetingEnabled()
}

func (a *WeedTrackingService) GetBooted(context.Context, *weed_tracking.GetBootedRequest) (*weed_tracking.GetBootedResponse, error) {
	return &weed_tracking.GetBootedResponse{
		Booted: true,
	}, nil
}

func (s *WeedTrackingService) StartPlantCaptcha(ctx context.Context, req *weed_tracking.Empty) (*weed_tracking.Empty, error) {
	// do not use proxy client for start, don't want to start multiple times
	err := s.LoadState(s.state)
	if err != nil {
		logrus.WithError(err).Warn("Couldn't load state, defaulting to success")
	}
	if err != nil || s.state.StartSuccess {
		if s.state.PlantCaptchaStatus == weed_tracking.PlantCaptchaStatus_CAPTCHA_STARTED {
			return nil, fmt.Errorf("Plant Captcha already started")
		}
		s.state.PlantCaptchaStatus = weed_tracking.PlantCaptchaStatus_CAPTCHA_STARTED
		err = s.SaveState(s.state)
		if err != nil {
			logrus.WithError(err).Warn("Couldn't save captcha started state")
			return nil, err
		}
		return &weed_tracking.Empty{}, nil
	} else {
		return nil, fmt.Errorf("failed to start plant captcha")
	}
}

func (s *WeedTrackingService) GetPlantCaptchaStatus(ctx context.Context, req *weed_tracking.Empty) (resp *weed_tracking.PlantCaptchaStatusResponse, err error) {
	err2 := s.LoadState(s.state)
	if err2 != nil {
		logrus.WithError(err2).Warn("Couldn't load state, defaulting to proxy")
	}
	if err2 != nil || s.state.UseProxy {
		resp, err = s.proxyClient.GetPlantCaptchaStatus()
		if err != nil {
			logrus.WithError(err).Warn("Couldn't get plant captcha status from aimbot")
		}
	} else {
		resp = &weed_tracking.PlantCaptchaStatusResponse{
			Status:        s.state.PlantCaptchaStatus,
			TotalImages:   s.state.TotalImages,
			ImagesTaken:   s.state.ImagesTaken,
			MetadataTaken: s.state.MetadataTaken,
		}
	}
	return resp, err
}

func (s *WeedTrackingService) RemovePlantRemovePlantCaptchaDirectory(ctx context.Context, req *weed_tracking.Empty) (*weed_tracking.Empty, error) {
	// do not use proxy client for remove, don't want to remove multiple times
	err := s.LoadState(s.state)
	if err != nil {
		logrus.WithError(err).Warn("Couldn't load state, defaulting to success")
	}
	if err != nil || s.state.RemoveDirSuccess {
		return &weed_tracking.Empty{}, nil
	} else {
		return nil, fmt.Errorf("failed to remove plant captcha directory")
	}
}

func (s *WeedTrackingService) CancelPlantCaptcha(ctx context.Context, req *weed_tracking.Empty) (*weed_tracking.Empty, error) {
	// do not use proxy client for cancel, don't want to cancel multiple times
	err := s.LoadState(s.state)
	if err != nil {
		logrus.WithError(err).Warn("Couldn't load state, defaulting to success")
	}
	if err != nil || s.state.CancelSuccess {
		s.state.PlantCaptchaStatus = weed_tracking.PlantCaptchaStatus_CAPTCHA_CANCELLED
		err = s.SaveState(s.state)
		if err != nil {
			logrus.WithError(err).Warn("Couldn't save captcha cancelled state")
			return nil, err
		}
		return &weed_tracking.Empty{}, nil
	} else {
		return nil, fmt.Errorf("failed to cancel plant captcha")
	}
}

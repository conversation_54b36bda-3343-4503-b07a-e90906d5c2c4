package services

import (
	"context"
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"google.golang.org/grpc"
)

type CVState struct {
	SupportedTensorRTVersions                []string `json:"supported_tensor_rt_versions"`
	ComputeCapabilities                      []string `json:"compute_capabilities"`
	CategoryCollectionId                     string   `json:"category_collection_id"`
	CategoryCollectionLastUpdatedTimestampMs int64    `json:"category_collection_last_updated_timestamp_ms"`
}

type CVService struct {
	SimulatedServiceImpl
	cv.UnimplementedCVRuntimeServiceServer
	CameraInfo []*cv.CameraInfo
	state      *CVState
}

func NewCVService(id uint32, grpcServer *grpc.Server) *CVService {
	c := &CVService{
		SimulatedServiceImpl: SimulatedServiceImpl{id},
		CameraInfo:           make([]*cv.CameraInfo, 0),
		state:                &CVState{},
	}

	numPredicts := 4
	numTargets := 10
	if environment.IsReaper() {
		numPredicts = 1
		numTargets = 2
	}
	for i := 1; i <= numPredicts; i++ {
		ip := fmt.Sprintf("10.11.32.%v", 2+(i-1)*4)
		serial := fmt.Sprintf("predict%v", i)
		c.CameraInfo = append(c.CameraInfo, &cv.CameraInfo{
			CamId:        fmt.Sprintf("predict%v", i),
			IpAddress:    &ip,
			SerialNumber: &serial,
			Model:        "model",
			Width:        4000,
			Height:       3072,
			Connected:    true,
			LinkSpeed:    125000000,
		})
	}

	for i := 1; i <= numTargets; i++ {
		ip := fmt.Sprintf("10.11.33.%v", i+1)
		serial := fmt.Sprintf("target%v", i)
		c.CameraInfo = append(c.CameraInfo, &cv.CameraInfo{
			CamId:        fmt.Sprintf("target%v", i),
			IpAddress:    &ip,
			SerialNumber: &serial,
			Model:        "model",
			Width:        4000,
			Height:       3072,
			Connected:    true,
			LinkSpeed:    125000000,
		})
	}

	cv.RegisterCVRuntimeServiceServer(grpcServer, c)
	return c
}

func (c *CVService) GetCameraInfo(context.Context, *cv.GetCameraInfoRequest) (*cv.GetCameraInfoResponse, error) {
	return &cv.GetCameraInfoResponse{CameraInfo: c.CameraInfo}, nil
}

func (c *CVService) GetCameraDimensions(context.Context, *cv.GetCameraDimensionsRequest) (*cv.GetCameraDimensionsResponse, error) {
	return &cv.GetCameraDimensionsResponse{
		Width:  4000,
		Height: 3072,
	}, nil
}

func (c *CVService) GetReady(context.Context, *cv.GetReadyRequest) (*cv.GetReadyResponse, error) {
	return &cv.GetReadyResponse{
		Ready: true,
	}, nil
}

func (c *CVService) GetSupportedTensorRTVersions(context.Context, *cv.Empty) (*cv.SupportedTensorRTVersionsResponse, error) {
	c.LoadState(c.state)
	return &cv.SupportedTensorRTVersionsResponse{
		Versions: c.state.SupportedTensorRTVersions,
	}, nil
}

func (c *CVService) GetComputeCapabilities(context.Context, *cv.Empty) (*cv.ComputeCapabilitiesResponse, error) {
	c.LoadState(c.state)
	return &cv.ComputeCapabilitiesResponse{
		Capabilities: c.state.ComputeCapabilities,
	}, nil
}

func (c *CVService) ReloadCategoryCollection(ctx context.Context, in *cv.Empty) (*cv.Empty, error) {
	return &cv.Empty{}, nil
}

func (c *CVService) GetCategoryCollection(ctx context.Context, in *cv.Empty) (*cv.GetCategoryCollectionResponse, error) {
	c.LoadState(c.state)
	return &cv.GetCategoryCollectionResponse{CategoryCollectionId: c.state.CategoryCollectionId, LastUpdatedTimestampMs: c.state.CategoryCollectionLastUpdatedTimestampMs}, nil
}

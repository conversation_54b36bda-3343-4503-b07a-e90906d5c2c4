package services

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"google.golang.org/grpc"
)

const (
	StatesJSON = "/robot/golang/simulator/software/services/states.json"

	SWMGPort   = "61005"
	AimbotPort = "6942"
	WTPort     = "65432"

	CommandAddr = "127.0.0.1"
	Row1Addr    = "10.10.3.11"
)

type Service interface{}

type SimulatedServiceImpl struct {
	ID uint32
}

func (s *SimulatedServiceImpl) LoadState(state any) error {
	stateMap, err := getStateMap()
	if err != nil {
		return err
	}

	id := fmt.Sprintf("%v", s.ID)
	stateKey := strings.Split(fmt.Sprintf("%T", state), ".")[1]
	err = json.Unmarshal(stateMap[id][stateKey], state)
	if err != nil {
		return err
	}

	return nil
}

func (s *SimulatedServiceImpl) SaveState(state any) error {
	stateMap, err := getStateMap()
	if err != nil {
		return err
	}

	id := fmt.Sprintf("%v", s.ID)
	stateKey := strings.Split(fmt.Sprintf("%T", state), ".")[1]

	newState, err := json.Marshal(state)
	if err != nil {
		return err
	}

	if _, ok := stateMap[id]; !ok {
		stateMap[id] = make(map[string]json.RawMessage)
	}

	stateMap[id][stateKey] = newState

	stateJSON, err := json.MarshalIndent(stateMap, "", "    ")
	if err != nil {
		return err
	}

	err = os.WriteFile(StatesJSON, stateJSON, 0644)
	if err != nil {
		return err
	}
	return nil
}

func getStateMap() (map[string]map[string]json.RawMessage, error) {
	stateJSON, err := os.ReadFile(StatesJSON)
	if err != nil {
		return nil, err
	}
	var stateMap map[string]map[string]json.RawMessage
	err = json.Unmarshal(stateJSON, &stateMap)
	if err != nil {
		return nil, err
	}
	return stateMap, nil
}

func ServiceInterceptor(ctx context.Context,
	req interface{},
	info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler) (interface{}, error) {

	h, err := handler(ctx, req)
	return h, err
}

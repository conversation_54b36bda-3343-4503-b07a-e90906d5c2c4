package main

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/simulator/software/services"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

/*

	The goal is to simulate the correct number of services for the robot
	The following need to be simulated:
	- Aimbot (Aimbot / WeedTracking)
	- CV
	- ModelReceiver
	- SoftwareManager

	Real containers when in simulator mode:
	- Aimbot (row 1) - *********1:6942 -> 127.0.0.1:6942
	- CV (row 1) - *********1:15053 -> 127.0.0.1:15053
	- CV (row 1 secondary) - **********:15054 -> 127.0.0.1:15054
	- ModelReceiver (row 1) - *********1:61004 -> 127.0.0.1:61004
	- SoftwareManager (command) - *********:61005 -> 127.0.0.1:61005

	* Each row needs to simulate a software manager
	* Rows 2, ..., N, need to simulate <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ModelReceiver

	Simulated rows are given a port number starting at 64002 for Row 1, 64003 for Row 2, etc.
	See golang/simulator/iptables.sh for the port mappings

	TODO: mini pc support

*/

const (
	NumSlayerRows   = 3
	NumReaperRows   = 8
	StartingPort    = 64002
	MaxMessageBytes = int(1e9) // 1 GB
)

func appendRowServices(servicesList []services.Service, id uint32, grpcServer *grpc.Server) []services.Service {
	servicesList = append(servicesList, services.NewAimbotService(id, grpcServer))
	servicesList = append(servicesList, services.NewCVService(id, grpcServer))
	servicesList = append(servicesList, services.NewModelReceiverService(id, grpcServer))
	servicesList = append(servicesList, services.NewWeedTrackingService(id, grpcServer))
	return servicesList
}

func main() {
	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	if _, err := os.Stat(services.StatesJSON); os.IsNotExist(err) {
		_, err = os.Create(services.StatesJSON)
		if err != nil {
			logrus.Fatalf("Failed to create states.json: %v", err)
		}
	}

	var wg sync.WaitGroup

	rowServers := make([]*grpc.Server, 0)
	servicesList := make([]services.Service, 0)

	// create row rpc servers
	numRows := NumSlayerRows
	if environment.IsReaper() {
		numRows = NumReaperRows
	}

	/*
	 * Using ports
	 * Slayer
	 * 64002
	 * 64003
	 * 64004
	 * Reaper
	 * 64005
	 * 64006
	 * 64007
	 * 64008
	 * 64009
	 */

	port := StartingPort
	for i := uint32(1); i <= uint32(numRows); i++ {

		fmt.Printf("Creating simulated row %v on port %v\n", i, port)
		addr := fmt.Sprintf("0.0.0.0:%d", port)
		lis, err := net.Listen("tcp", addr)
		if err != nil {
			logrus.Fatalf("Failed to listen: %v", err)
		}

		var opts []grpc.ServerOption
		opts = append(opts, grpc.MaxRecvMsgSize(MaxMessageBytes))
		opts = append(opts, grpc.UnaryInterceptor(services.ServiceInterceptor))
		grpcServer := grpc.NewServer(opts...)
		rowServers = append(rowServers, grpcServer)

		servicesList = append(servicesList, services.NewSoftwareManagerService(i, grpcServer))
		if i > 1 {
			servicesList = appendRowServices(servicesList, i, grpcServer)
		}

		wg.Add(1)
		go func(id uint32) {
			defer wg.Done()
			err = grpcServer.Serve(lis)
			if err != nil {
				logrus.Fatalf("Simulated row %v failed to Serve GRPC Server: %v", id, err)
			}
		}(i)
		port++
	}

	<-stopCtx.Done()

	for _, server := range rowServers {
		server.Stop()
	}

	wg.Wait()
}

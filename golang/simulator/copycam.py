import os


def copycam(fname: str, fcamname: str, rowname: str, camname: str) -> None:
    with open(fname, "r") as f:
        lines = f.readlines()

    splits = {}
    for l in lines:
        spl = l.strip().split()
        if len(spl) < 4:
            continue
        k = spl[2]
        v = spl[3]
        splits[k] = v

    print(splits)

    for (k, v) in splits.items():
        idx = k.find("/")
        k2 = rowname + k[idx:-1].replace(fcamname, camname)
        print(k2 + ": " + v)

        if v == "0" or v == "1":
            os.system(f"bot run common python -m tools.config.cli set {k2} {v}")
            bv = "True" if v == "1" else "False"
            os.system(f"bot run common python -m tools.config.cli set {k2} {bv}")
        else:
            os.system(f"bot run common python -m tools.config.cli set {k2} {v}")


def addcam(cam: str, row: str) -> None:
    os.system(f"bot run common python -m tools.config.cli add {row}/cv/cameras {cam}")


if __name__ == "__main__":
    addcam("predict1", "row1")
    addcam("predict2", "row1")
    addcam("predict3", "row1")
    addcam("predict4", "row1")

    copycam("predict", "predict1", "row1", "predict1")
    copycam("predict", "predict1", "row1", "predict2")
    copycam("predict", "predict1", "row1", "predict3")
    copycam("predict", "predict1", "row1", "predict4")

    addcam("target1", "row1")
    addcam("target2", "row1")
    addcam("target3", "row1")
    addcam("target4", "row1")
    addcam("target5", "row1")
    addcam("target6", "row1")
    addcam("target7", "row1")
    addcam("target8", "row1")
    addcam("target9", "row1")
    addcam("target10", "row1")

    copycam("target", "target2", "row1", "target1")
    copycam("target", "target2", "row1", "target2")
    copycam("target", "target2", "row1", "target3")
    copycam("target", "target2", "row1", "target4")
    copycam("target", "target2", "row1", "target5")
    copycam("target", "target2", "row1", "target6")
    copycam("target", "target2", "row1", "target7")
    copycam("target", "target2", "row1", "target8")
    copycam("target", "target2", "row1", "target9")
    copycam("target", "target2", "row1", "target10")

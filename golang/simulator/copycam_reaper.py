import os


def run(cmd: str) -> None:
    print("Running " + cmd)
    os.system(cmd)


# file name, camera name in file, dest row name, dest cam name
def copycam(fname: str, fcamname: str, rowname: str, camname: str) -> None:
    with open(fname, "r") as f:
        lines = f.readlines()

    splits = {}
    for l in lines:
        spl = l.strip().split()
        if len(spl) < 4:
            continue
        k = spl[2]
        v = spl[3]
        splits[k] = v

    for (k, v) in splits.items():
        idx = k.find("/")
        k2 = "modules/" + rowname + k[idx:-1].replace(fcamname, camname)
        print(k2 + ": " + v)

        if v == "0" or v == "1":
            run(f"bot run common python -m tools.config.cli set {k2} {v}")
            bv = "True" if v == "1" else "False"
            run(f"bot run common python -m tools.config.cli set {k2} {bv}")
        else:
            run(f"bot run common python -m tools.config.cli set {k2} {v}")


def addcam(cam: str, row: str) -> None:
    run(f"bot run common python -m tools.config.cli add {row}/cv/cameras {cam}")


if __name__ == "__main__":
    # addcam("predict1", "row1")

    copycam("predict", "predict1", "module1", "predict1")

    # addcam("target1", "row1")
    # addcam("target2", "row1")

    copycam("target", "target2", "module1", "target1")
    copycam("target", "target2", "module1", "target2")

{"command": {"commander": {"alarm": {"cv_runtime_latency_threshold_ms": {"ts": 0, "value": 3000.0}, "flicker_filter_s": {"ts": 0, "value": 10}, "ignore_list": {"__unsync_erased__": null, "ts": 0}}, "crop_ids": {"009807ea-865d-4e9e-a753-8aaa598b230e": {"carbon_name": {"ts": 1675819648740, "value": "Red Cabbage"}, "common_name": {"ts": 1675819648732, "value": "Red Cabbage"}, "enabled": {"ts": 1675819648745, "value": false}, "pinned_model": {"ts": 1675819648751, "value": ""}, "recommended_model": {"ts": 1675819648758, "value": ""}}, "00d346ab-3dca-4339-88ff-b02ad6bdd9fb": {"carbon_name": {"ts": 1673991089282, "value": "legacy - mint"}, "common_name": {"ts": 1673991089278, "value": "legacy - mint"}, "enabled": {"ts": 1673991089286, "value": false}, "pinned_model": {"ts": 1673991089288, "value": ""}, "recommended_model": {"ts": 1673991089295, "value": ""}}, "010eb0cc-66ab-46d1-9b42-c8e0c7404618": {"carbon_name": {"ts": 1673991089441, "value": "legacy - carrot"}, "common_name": {"ts": 1673991089438, "value": "legacy - carrot"}, "enabled": {"ts": 1673991089446, "value": false}, "pinned_model": {"ts": 1673991089449, "value": ""}, "recommended_model": {"ts": 1673991089453, "value": ""}}, "05f804d9-e758-4ca3-9b29-e23f7023e29c": {"carbon_name": {"ts": 1675205991145, "value": "Bur<PERSON>"}, "common_name": {"ts": 1675205991035, "value": "Bur<PERSON>"}, "enabled": {"ts": 1675205991151, "value": false}, "pinned_model": {"ts": 1675205991156, "value": ""}, "recommended_model": {"ts": 1675205991160, "value": ""}}, "07d163d8-4808-4233-b2bc-0289bab63b41": {"carbon_name": {"ts": 1673991088497, "value": "Brassica Napus (Pabularia Group)"}, "common_name": {"ts": 1673991088493, "value": "Red Russian Kale"}, "enabled": {"ts": 1673991088502, "value": false}, "pinned_model": {"ts": 1673991088505, "value": ""}, "recommended_model": {"ts": 1673991088508, "value": ""}}, "09d48ef3-b697-443a-a22b-8c61eaf622c6": {"carbon_name": {"ts": 1673991088875, "value": "legacy - chard"}, "common_name": {"ts": 1673991088870, "value": "legacy - chard"}, "enabled": {"ts": 1673991088880, "value": false}, "pinned_model": {"ts": 1673991088883, "value": ""}, "recommended_model": {"ts": 1673991088885, "value": ""}}, "0bfd59fb-080c-4dbe-abbd-8a9565f948a5": {"carbon_name": {"ts": 1673991089809, "value": "Allium fistulosum"}, "common_name": {"ts": 1673991089804, "value": "Green Onion"}, "enabled": {"ts": 1673991089814, "value": false}, "pinned_model": {"ts": 1673991089819, "value": ""}, "recommended_model": {"ts": 1673991089822, "value": ""}}, "0c8616c9-d98e-4513-a71c-a9b726bf2a99": {"carbon_name": {"ts": 1673991088935, "value": "legacy - brassica"}, "common_name": {"ts": 1673991088932, "value": "legacy - brassica"}, "enabled": {"ts": 1673991088939, "value": false}, "pinned_model": {"ts": 1673991088942, "value": ""}, "recommended_model": {"ts": 1673991088945, "value": ""}}, "0f2b4a58-841b-4934-b986-cbe0a3527807": {"carbon_name": {"ts": 1673991088144, "value": "Brassica Oleracea v. Bo<PERSON>tis"}, "common_name": {"ts": 1673991088140, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1673991088147, "value": false}, "pinned_model": {"ts": 1673991088150, "value": ""}, "recommended_model": {"ts": 1673991088153, "value": ""}}, "101f0d51-1e44-44c5-b680-4a3949a69e59": {"carbon_name": {"ts": 1675205577504, "value": "Ashwagandha"}, "common_name": {"ts": 1675205577496, "value": "Ashwagandha"}, "enabled": {"ts": 1675205577509, "value": false}, "pinned_model": {"ts": 1675205577514, "value": ""}, "recommended_model": {"ts": 1675205577518, "value": ""}}, "1b050589-a7f1-4090-8f34-1d8c11173967": {"carbon_name": {"ts": 1673991089510, "value": "Potato"}, "common_name": {"ts": 1673991089505, "value": "Potato"}, "enabled": {"ts": 1673991089514, "value": false}, "pinned_model": {"ts": 1673991089518, "value": ""}, "recommended_model": {"ts": 1673991089521, "value": ""}}, "1f10212a-1081-4dcf-bd31-42d4f9f9dfc9": {"carbon_name": {"ts": 1673991089951, "value": "Lettuce"}, "common_name": {"ts": 1673991089948, "value": "Lettuce"}, "enabled": {"ts": 1673991089955, "value": false}, "pinned_model": {"ts": 1673991089961, "value": ""}, "recommended_model": {"ts": 1673991089966, "value": ""}}, "2b17d172-3d98-4672-8303-77ec96955317": {"carbon_name": {"ts": 1673991088954, "value": "legacy - carbon-crosses"}, "common_name": {"ts": 1673991088951, "value": "legacy - carbon-crosses"}, "enabled": {"ts": 1673991088957, "value": false}, "pinned_model": {"ts": 1673991088959, "value": ""}, "recommended_model": {"ts": 1673991088962, "value": ""}}, "2ccab222-fa08-485d-b30e-999ca7d4f4cc": {"carbon_name": {"ts": 1675805497324, "value": "Green Leaf Lettuce"}, "common_name": {"ts": 1675805497316, "value": "Green Leaf Lettuce"}, "enabled": {"ts": 1675805497329, "value": false}, "pinned_model": {"ts": 1675805497335, "value": ""}, "recommended_model": {"ts": 1675805497341, "value": ""}}, "2d70888b-8ae5-479f-866c-8a37eab3387f": {"carbon_name": {"ts": 1673991090028, "value": "<PERSON><PERSON>"}, "common_name": {"ts": 1673991090025, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1673991090033, "value": false}, "pinned_model": {"ts": 1673991090258, "value": ""}, "recommended_model": {"ts": 1673991090265, "value": ""}}, "313521e0-b974-4442-87ac-85f1ab592a2b": {"carbon_name": {"ts": 1673991088063, "value": "Brassica oleracea var. viridis"}, "common_name": {"ts": 1673991088059, "value": "<PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1673991088067, "value": false}, "pinned_model": {"ts": 1673991088069, "value": ""}, "recommended_model": {"ts": 1673991088073, "value": ""}}, "37ebab62-b76e-4bbf-b8da-ddd5523ee993": {"carbon_name": {"ts": 1673991088581, "value": "legacy - spinach"}, "common_name": {"ts": 1673991088578, "value": "legacy - spinach"}, "enabled": {"ts": 1673991088585, "value": false}, "pinned_model": {"ts": 1673991088589, "value": ""}, "recommended_model": {"ts": 1673991088592, "value": ""}}, "384ce736-3801-4a76-80e1-c00866c5d05c": {"carbon_name": {"ts": 1673991089761, "value": "legacy - green_bean"}, "common_name": {"ts": 1673991089755, "value": "legacy - green_bean"}, "enabled": {"ts": 1673991089764, "value": false}, "pinned_model": {"ts": 1673991089767, "value": ""}, "recommended_model": {"ts": 1673991089771, "value": ""}}, "39a0ff59-55b4-4bba-9eb8-c083fbbe86f1": {"carbon_name": {"ts": 1673991089156, "value": "legacy - dandelion"}, "common_name": {"ts": 1673991089152, "value": "legacy - dandelion"}, "enabled": {"ts": 1673991089159, "value": false}, "pinned_model": {"ts": 1673991089162, "value": ""}, "recommended_model": {"ts": 1673991089165, "value": ""}}, "3a51297b-b72b-46cb-90df-340b4aca9094": {"carbon_name": {"ts": 1675206890814, "value": "Catnip"}, "common_name": {"ts": 1675206890808, "value": "Catnip"}, "enabled": {"ts": 1675206890820, "value": false}, "pinned_model": {"ts": 1675206890829, "value": ""}, "recommended_model": {"ts": 1675206890832, "value": ""}}, "3ad8d648-62f1-4846-b7e2-64bbf8b1d71b": {"carbon_name": {"ts": 1673991088164, "value": "<PERSON><PERSON><PERSON> sativus var. Longipinnatus"}, "common_name": {"ts": 1673991088160, "value": "Daikon"}, "enabled": {"ts": 1673991088167, "value": false}, "pinned_model": {"ts": 1673991088171, "value": ""}, "recommended_model": {"ts": 1673991088175, "value": ""}}, "3b0a0d25-be70-4968-bffd-b7cc29749c78": {"carbon_name": {"ts": 1673991089008, "value": "legacy - arugula"}, "common_name": {"ts": 1673991089005, "value": "legacy - arugula"}, "enabled": {"ts": 1673991089010, "value": false}, "pinned_model": {"ts": 1673991089014, "value": ""}, "recommended_model": {"ts": 1673991089017, "value": ""}}, "3caafb8b-1a61-4690-b5b5-4cc165087cfb": {"carbon_name": {"ts": 1673991089026, "value": "legacy - parsley"}, "common_name": {"ts": 1673991089022, "value": "legacy - parsley"}, "enabled": {"ts": 1673991089031, "value": false}, "pinned_model": {"ts": 1673991089035, "value": ""}, "recommended_model": {"ts": 1673991089038, "value": ""}}, "3cfb7914-1df8-4a40-a83c-5e35bb37ecb0": {"carbon_name": {"ts": 1675460810046, "value": "<PERSON><PERSON><PERSON>"}, "common_name": {"ts": 1675460810043, "value": "<PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1675460810049, "value": false}, "pinned_model": {"ts": 1675460810053, "value": ""}, "recommended_model": {"ts": 1675460810057, "value": ""}}, "439fb381-b60d-4430-a082-510f6e657cdb": {"carbon_name": {"ts": 1674777029250, "value": "legacy - carbon_crosses"}, "common_name": {"ts": 1674777029245, "value": "legacy - carbon_crosses"}, "enabled": {"ts": 1674777029255, "value": false}, "pinned_model": {"ts": 1674777029261, "value": ""}, "recommended_model": {"ts": 1674777029267, "value": ""}}, "462fcb07-7b5d-4e53-9107-8caf90260775": {"carbon_name": {"ts": 1675819648780, "value": "Asparagus"}, "common_name": {"ts": 1675819648772, "value": "Asparagus"}, "enabled": {"ts": 1675819648787, "value": false}, "pinned_model": {"ts": 1675819648970, "value": ""}, "recommended_model": {"ts": 1675819648977, "value": ""}}, "46f74bec-7d2d-47f0-9711-cacf742398ed": {"carbon_name": {"ts": 1673991089396, "value": "Allium Porrum"}, "common_name": {"ts": 1673991089392, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1673991089400, "value": false}, "pinned_model": {"ts": 1673991089403, "value": ""}, "recommended_model": {"ts": 1673991089406, "value": ""}}, "47e1f09d-cfa3-45a0-8ad5-701ca7f20fc4": {"carbon_name": {"ts": 1673991089046, "value": "legacy - red_lettuce"}, "common_name": {"ts": 1673991089042, "value": "legacy - red_lettuce"}, "enabled": {"ts": 1673991089051, "value": false}, "pinned_model": {"ts": 1673991089054, "value": ""}, "recommended_model": {"ts": 1673991089057, "value": ""}}, "4a32aef1-7caf-4f3a-9a0c-7ef785fec23b": {"carbon_name": {"ts": 1673991088228, "value": "Taraxacum erythrospermum"}, "common_name": {"ts": 1673991088224, "value": "Red Dandelion"}, "enabled": {"ts": 1673991088232, "value": false}, "pinned_model": {"ts": 1673991088235, "value": ""}, "recommended_model": {"ts": 1673991088239, "value": ""}}, "4aafdc99-6bda-4b5a-b64b-6c0d6970a958": {"carbon_name": {"ts": 1673991089100, "value": "legacy - test-thinning-lettuce"}, "common_name": {"ts": 1673991089097, "value": "legacy - test-thinning-lettuce"}, "enabled": {"ts": 1673991089103, "value": false}, "pinned_model": {"ts": 1673991089105, "value": ""}, "recommended_model": {"ts": 1673991089108, "value": ""}}, "4ffba7bb-cf3a-4499-a256-abdec638e6dd": {"carbon_name": {"ts": 1673991088659, "value": "legacy - cilantro"}, "common_name": {"ts": 1673991088657, "value": "legacy - cilantro"}, "enabled": {"ts": 1673991088662, "value": false}, "pinned_model": {"ts": 1673991088665, "value": ""}, "recommended_model": {"ts": 1673991088668, "value": ""}}, "50670ebe-d114-4d24-8f5d-df513bb56193": {"carbon_name": {"ts": 1673991088106, "value": "Lactuca Sativa var. Capitata"}, "common_name": {"ts": 1673991088103, "value": "Icerberg Lettuce"}, "enabled": {"ts": 1673991088109, "value": false}, "pinned_model": {"ts": 1673991088111, "value": ""}, "recommended_model": {"ts": 1673991088114, "value": ""}}, "51d010d0-510d-429b-8321-a0ea967a719a": {"carbon_name": {"ts": 1673991089602, "value": "legacy - baby_kale"}, "common_name": {"ts": 1673991089599, "value": "legacy - baby_kale"}, "enabled": {"ts": 1673991089605, "value": false}, "pinned_model": {"ts": 1673991089608, "value": ""}, "recommended_model": {"ts": 1673991089610, "value": ""}}, "53d36f11-80ce-4664-b0e1-3686736734f7": {"carbon_name": {"ts": 1673991088562, "value": "Petroselinum <PERSON>rispum (flat leaf)"}, "common_name": {"ts": 1673991088560, "value": "Flat Leaf Parsley"}, "enabled": {"ts": 1673991088565, "value": false}, "pinned_model": {"ts": 1673991088567, "value": ""}, "recommended_model": {"ts": 1673991088570, "value": ""}}, "5ab64bb4-faca-48a4-bbb4-1f1fda2c55ec": {"carbon_name": {"ts": 1673991089304, "value": "legacy - celery"}, "common_name": {"ts": 1673991089301, "value": "legacy - celery"}, "enabled": {"ts": 1673991089308, "value": false}, "pinned_model": {"ts": 1673991089311, "value": ""}, "recommended_model": {"ts": 1673991089314, "value": ""}}, "5b0979ba-6a93-4d51-8e42-74fe8e1e0c1c": {"carbon_name": {"ts": 1673991089197, "value": "legacy - fennel"}, "common_name": {"ts": 1673991089194, "value": "legacy - fennel"}, "enabled": {"ts": 1673991089200, "value": false}, "pinned_model": {"ts": 1673991089203, "value": ""}, "recommended_model": {"ts": 1673991089207, "value": ""}}, "5be638a4-0644-46ff-9773-03bd1e09c776": {"carbon_name": {"ts": 1673991089259, "value": "legacy - shallot"}, "common_name": {"ts": 1673991089256, "value": "legacy - shallot"}, "enabled": {"ts": 1673991089264, "value": false}, "pinned_model": {"ts": 1673991089268, "value": ""}, "recommended_model": {"ts": 1673991089272, "value": ""}}, "631a5bbd-0f0f-4069-bb6f-1c430416bd01": {"carbon_name": {"ts": 1673991089533, "value": "<PERSON>"}, "common_name": {"ts": 1673991089529, "value": "<PERSON>"}, "enabled": {"ts": 1673991089537, "value": false}, "pinned_model": {"ts": 1673991089542, "value": ""}, "recommended_model": {"ts": 1673991089547, "value": ""}}, "6885b03e-a9ca-4320-9ba5-d310bf305cc9": {"carbon_name": {"ts": 1673991088291, "value": "lactuca Sativa Var<PERSON> "}, "common_name": {"ts": 1673991088286, "value": "Red Leaf Lettuce"}, "enabled": {"ts": 1673991088294, "value": false}, "pinned_model": {"ts": 1673991088298, "value": ""}, "recommended_model": {"ts": 1673991088301, "value": ""}}, "689b40d9-2aeb-4f8d-8ce2-dd86c964bf82": {"carbon_name": {"ts": 1673991089784, "value": "legacy - green_lettuce"}, "common_name": {"ts": 1673991089780, "value": "legacy - green_lettuce"}, "enabled": {"ts": 1673991089789, "value": false}, "pinned_model": {"ts": 1673991089792, "value": ""}, "recommended_model": {"ts": 1673991089795, "value": ""}}, "6aa86837-2897-4c3e-ab81-2de4ffd47507": {"carbon_name": {"ts": 1673991089929, "value": "<PERSON><PERSON>"}, "common_name": {"ts": 1673991089925, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1673991089934, "value": false}, "pinned_model": {"ts": 1673991089936, "value": ""}, "recommended_model": {"ts": 1673991089940, "value": ""}}, "6b31e920-1a2c-4c69-8c36-6290ecedb5d3": {"carbon_name": {"ts": 1673991089342, "value": "Brassica oleracea var. capitata"}, "common_name": {"ts": 1673991089339, "value": "Cabbage"}, "enabled": {"ts": 1673991089349, "value": false}, "pinned_model": {"ts": 1673991089354, "value": ""}, "recommended_model": {"ts": 1673991089358, "value": ""}}, "6c48a535-c3fb-4458-ab3f-a417d5f8f93b": {"carbon_name": {"ts": 1673991089370, "value": "NULL"}, "common_name": {"ts": 1673991089364, "value": "NULL"}, "enabled": {"ts": 1673991089374, "value": false}, "pinned_model": {"ts": 1673991089379, "value": ""}, "recommended_model": {"ts": 1673991089383, "value": ""}}, "6cd5f04a-85f2-4b76-a734-bdc3c5fefd5b": {"carbon_name": {"ts": 1673991088971, "value": "legacy - broccoli_cauliflower"}, "common_name": {"ts": 1673991088969, "value": "legacy - broccoli_cauliflower"}, "enabled": {"ts": 1673991088974, "value": false}, "pinned_model": {"ts": 1673991088977, "value": ""}, "recommended_model": {"ts": 1673991088980, "value": ""}}, "6df7141f-d9d8-4557-a614-3e44ee8cb9a1": {"carbon_name": {"ts": 1673991089464, "value": "Allium sativum"}, "common_name": {"ts": 1673991089461, "value": "<PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1673991089467, "value": false}, "pinned_model": {"ts": 1673991089470, "value": ""}, "recommended_model": {"ts": 1673991089474, "value": ""}}, "6e46e38f-7821-4347-90de-62de476be9bf": {"carbon_name": {"ts": 1673991088186, "value": "Foeniculum vulgare"}, "common_name": {"ts": 1673991088182, "value": "Fennel"}, "enabled": {"ts": 1673991088189, "value": false}, "pinned_model": {"ts": 1673991088192, "value": ""}, "recommended_model": {"ts": 1673991088196, "value": ""}}, "70a5afa6-acc7-4f3b-bc09-9315a3f4a273": {"carbon_name": {"ts": 1673991089837, "value": "Celery"}, "common_name": {"ts": 1673991089830, "value": "Celery"}, "enabled": {"ts": 1673991089843, "value": false}, "pinned_model": {"ts": 1673991089847, "value": ""}, "recommended_model": {"ts": 1673991089852, "value": ""}}, "710728e1-8fb6-475f-a3b6-9c01d2ba76f6": {"carbon_name": {"ts": 1673991088125, "value": "Brassica"}, "common_name": {"ts": 1673991088121, "value": "Mustard"}, "enabled": {"ts": 1673991088128, "value": false}, "pinned_model": {"ts": 1673991088132, "value": ""}, "recommended_model": {"ts": 1673991088134, "value": ""}}, "72228f8b-a202-4036-8650-7f5e95732391": {"carbon_name": {"ts": 1673991089116, "value": "legacy - cotton"}, "common_name": {"ts": 1673991089114, "value": "legacy - cotton"}, "enabled": {"ts": 1673991089119, "value": false}, "pinned_model": {"ts": 1673991089122, "value": ""}, "recommended_model": {"ts": 1673991089125, "value": ""}}, "77af57e3-57b9-4d69-aeb7-734037dba280": {"carbon_name": {"ts": 1673991089174, "value": "legacy - echinacea_angustifolia"}, "common_name": {"ts": 1673991089170, "value": "legacy - echinacea_angustifolia"}, "enabled": {"ts": 1673991089177, "value": false}, "pinned_model": {"ts": 1673991089180, "value": ""}, "recommended_model": {"ts": 1673991089186, "value": ""}}, "7be2df9d-46c6-43bf-abd4-5d1b28723d4e": {"carbon_name": {"ts": 1673991088381, "value": "Ipomo<PERSON> batatas"}, "common_name": {"ts": 1673991088378, "value": "Sweet Potatos"}, "enabled": {"ts": 1673991088383, "value": false}, "pinned_model": {"ts": 1673991088386, "value": ""}, "recommended_model": {"ts": 1673991088391, "value": ""}}, "7d12429d-246c-47e0-ae62-118facadcfc8": {"carbon_name": {"ts": 1673991090006, "value": "Petroselinum Crispum"}, "common_name": {"ts": 1673991090002, "value": "<PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1673991090010, "value": false}, "pinned_model": {"ts": 1673991090015, "value": ""}, "recommended_model": {"ts": 1673991090018, "value": ""}}, "81e978de-9e7c-490f-bce7-7171b0027054": {"carbon_name": {"ts": 1673991089240, "value": "legacy - soybean"}, "common_name": {"ts": 1673991089236, "value": "legacy - soybean"}, "enabled": {"ts": 1673991089243, "value": false}, "pinned_model": {"ts": 1673991089247, "value": ""}, "recommended_model": {"ts": 1673991089250, "value": ""}}, "82d22586-58d7-4dd6-8a7a-c0f7b7aa2488": {"carbon_name": {"ts": 1673991088602, "value": "legacy - lettuce"}, "common_name": {"ts": 1673991088599, "value": "legacy - lettuce"}, "enabled": {"ts": 1673991088606, "value": false}, "pinned_model": {"ts": 1673991088609, "value": ""}, "recommended_model": {"ts": 1673991088612, "value": ""}}, "851a835c-233e-4712-b52f-fd944ec822cd": {"carbon_name": {"ts": 1673991089734, "value": "legacy - potato"}, "common_name": {"ts": 1673991089731, "value": "legacy - potato"}, "enabled": {"ts": 1673991089737, "value": false}, "pinned_model": {"ts": 1673991089741, "value": ""}, "recommended_model": {"ts": 1673991089745, "value": ""}}, "879a6a0c-22d1-4c1a-9544-49f014921cf0": {"carbon_name": {"ts": 1673991089981, "value": "Mint"}, "common_name": {"ts": 1673991089977, "value": "Mint"}, "enabled": {"ts": 1673991089985, "value": false}, "pinned_model": {"ts": 1673991089988, "value": ""}, "recommended_model": {"ts": 1673991089992, "value": ""}}, "8869edd8-6ef7-499d-b9f5-ddc2723f171b": {"carbon_name": {"ts": 1673991089679, "value": "legacy - experimental-removed-crops"}, "common_name": {"ts": 1673991089675, "value": "legacy - experimental-removed-crops"}, "enabled": {"ts": 1673991089682, "value": false}, "pinned_model": {"ts": 1673991089684, "value": ""}, "recommended_model": {"ts": 1673991089687, "value": ""}}, "8ab7746a-56a0-4e47-a9bc-c6499c947821": {"carbon_name": {"ts": 1673991089323, "value": "Chard"}, "common_name": {"ts": 1673991089320, "value": "Chard"}, "enabled": {"ts": 1673991089327, "value": false}, "pinned_model": {"ts": 1673991089330, "value": ""}, "recommended_model": {"ts": 1673991089333, "value": ""}}, "8ce9f91f-1476-4484-941d-debf69f55aa4": {"carbon_name": {"ts": 1673991088915, "value": "legacy - beet"}, "common_name": {"ts": 1673991088911, "value": "legacy - beet"}, "enabled": {"ts": 1673991088919, "value": false}, "pinned_model": {"ts": 1673991088921, "value": ""}, "recommended_model": {"ts": 1673991088926, "value": ""}}, "9288e023-cd10-4b9a-aac5-92c48e14a9a4": {"carbon_name": {"ts": 1675207066059, "value": "<PERSON><PERSON>"}, "common_name": {"ts": 1675207065903, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1675207066068, "value": false}, "pinned_model": {"ts": 1675207066072, "value": ""}, "recommended_model": {"ts": 1675207066075, "value": ""}}, "943c319d-d38f-4708-96c7-c2d99899496c": {"carbon_name": {"ts": 1673991088207, "value": "Taraxacum"}, "common_name": {"ts": 1673991088204, "value": "Green Dandelion"}, "enabled": {"ts": 1673991088211, "value": false}, "pinned_model": {"ts": 1673991088214, "value": ""}, "recommended_model": {"ts": 1673991088218, "value": ""}}, "945043e0-95d5-44bf-9dd5-fab7d44f60c4": {"carbon_name": {"ts": 1673991088402, "value": "Brassica oleracea var. italica"}, "common_name": {"ts": 1673991088397, "value": "<PERSON><PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1673991088405, "value": false}, "pinned_model": {"ts": 1673991088408, "value": ""}, "recommended_model": {"ts": 1673991088412, "value": ""}}, "9611d658-cac1-40b5-91d3-dc9228177900": {"carbon_name": {"ts": 1673991089217, "value": "legacy - test-treadkill"}, "common_name": {"ts": 1673991089212, "value": "legacy - test-treadkill"}, "enabled": {"ts": 1673991089220, "value": false}, "pinned_model": {"ts": 1673991089225, "value": ""}, "recommended_model": {"ts": 1673991089230, "value": ""}}, "96c510c0-5586-4565-bf9b-98ab1286ece8": {"carbon_name": {"ts": 1673991089697, "value": "legacy - treadkill-carbon-crosses"}, "common_name": {"ts": 1673991089692, "value": "legacy - treadkill-carbon-crosses"}, "enabled": {"ts": 1673991089700, "value": false}, "pinned_model": {"ts": 1673991089703, "value": ""}, "recommended_model": {"ts": 1673991089706, "value": ""}}, "97f25ddd-93d3-4291-a5d9-573fbdd7b200": {"carbon_name": {"ts": 1675460810022, "value": "Watermelon"}, "common_name": {"ts": 1675460810018, "value": "Watermelon"}, "enabled": {"ts": 1675460810027, "value": false}, "pinned_model": {"ts": 1675460810031, "value": ""}, "recommended_model": {"ts": 1675460810035, "value": ""}}, "9c829d39-af0a-409c-9912-f2d0b6ab0470": {"carbon_name": {"ts": 1675892539025, "value": "World Ag Expo 2023"}, "common_name": {"ts": 1675892539013, "value": "World Ag Expo 2023"}, "enabled": {"ts": 1675892539029, "value": false}, "pinned_model": {"ts": 1675892539032, "value": ""}, "recommended_model": {"ts": 1675892539040, "value": ""}}, "__unsync_erased__": null, "a0d81b6e-9bb9-4b25-b73d-fc932e9a79b8": {"carbon_name": {"ts": 1673991089863, "value": "Arugula"}, "common_name": {"ts": 1673991089860, "value": "Arugula"}, "enabled": {"ts": 1673991089868, "value": false}, "pinned_model": {"ts": 1673991089870, "value": ""}, "recommended_model": {"ts": 1673991089874, "value": ""}}, "a27cc4de-d945-4d62-9673-0ca345184aff": {"carbon_name": {"ts": 1673991089066, "value": "legacy - basil"}, "common_name": {"ts": 1673991089062, "value": "legacy - basil"}, "enabled": {"ts": 1673991089068, "value": false}, "pinned_model": {"ts": 1673991089072, "value": ""}, "recommended_model": {"ts": 1673991089074, "value": ""}}, "a63792a3-e8e7-48ef-a14b-867373917271": {"carbon_name": {"ts": 1673991088620, "value": "legacy - leek"}, "common_name": {"ts": 1673991088618, "value": "legacy - leek"}, "enabled": {"ts": 1673991088623, "value": false}, "pinned_model": {"ts": 1673991088625, "value": ""}, "recommended_model": {"ts": 1673991088629, "value": ""}}, "a69e4c80-ba78-49ec-9c1e-b5d3bed05758": {"carbon_name": {"ts": 1673991089885, "value": "<PERSON>"}, "common_name": {"ts": 1673991089881, "value": "<PERSON>"}, "enabled": {"ts": 1673991089890, "value": false}, "pinned_model": {"ts": 1673991089894, "value": ""}, "recommended_model": {"ts": 1673991089897, "value": ""}}, "a903afe1-8c13-4ce1-9b05-d882275343db": {"carbon_name": {"ts": 1673991088326, "value": "Coriandrum sativum"}, "common_name": {"ts": 1673991088323, "value": "Cilantro"}, "enabled": {"ts": 1673991088328, "value": false}, "pinned_model": {"ts": 1673991088332, "value": ""}, "recommended_model": {"ts": 1673991088336, "value": ""}}, "ac2cf80f-d975-4769-a432-6f31ec77ece1": {"carbon_name": {"ts": 1673991088641, "value": "legacy - garlic"}, "common_name": {"ts": 1673991088636, "value": "legacy - garlic"}, "enabled": {"ts": 1673991088643, "value": false}, "pinned_model": {"ts": 1673991088648, "value": ""}, "recommended_model": {"ts": 1673991088651, "value": ""}}, "ae8664cc-a0e9-4930-a385-f2e126cad2b3": {"carbon_name": {"ts": 1673991088082, "value": "Beta vulgaris subsp. vulgaris"}, "common_name": {"ts": 1673991088079, "value": "Beets"}, "enabled": {"ts": 1673991088089, "value": false}, "pinned_model": {"ts": 1673991088093, "value": ""}, "recommended_model": {"ts": 1673991088096, "value": ""}}, "aef618a1-a6d5-4c2e-bd7b-516605206448": {"carbon_name": {"ts": 1673991088247, "value": "Anethum graveolens"}, "common_name": {"ts": 1673991088245, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1673991088251, "value": false}, "pinned_model": {"ts": 1673991088255, "value": ""}, "recommended_model": {"ts": 1673991088257, "value": ""}}, "b3a3533d-bfe5-48dc-bb00-7481d437e242": {"carbon_name": {"ts": 1673991088545, "value": "Petroselinum <PERSON>rispum (curly leaf)"}, "common_name": {"ts": 1673991088536, "value": "Curly <PERSON><PERSON>"}, "enabled": {"ts": 1673991088549, "value": false}, "pinned_model": {"ts": 1673991088552, "value": ""}, "recommended_model": {"ts": 1673991088554, "value": ""}}, "b686316c-06d9-492a-bd84-68f16cb84011": {"carbon_name": {"ts": 1675819648702, "value": "Green Cabbage"}, "common_name": {"ts": 1675819648694, "value": "Green Cabbage"}, "enabled": {"ts": 1675819648708, "value": false}, "pinned_model": {"ts": 1675819648713, "value": ""}, "recommended_model": {"ts": 1675819648720, "value": ""}}, "b6e58966-904e-495b-b6e4-18036f002954": {"carbon_name": {"ts": 1673991089583, "value": "Broccoli & Cauliflower Mix"}, "common_name": {"ts": 1673991089579, "value": "Broccoli & Cauliflower Mix"}, "enabled": {"ts": 1673991089586, "value": false}, "pinned_model": {"ts": 1673991089589, "value": ""}, "recommended_model": {"ts": 1673991089593, "value": ""}}, "b7ff0cfd-233a-4be5-ab21-6741ed843ab9": {"carbon_name": {"ts": 1673991089418, "value": "<PERSON><PERSON><PERSON> carota"}, "common_name": {"ts": 1673991089415, "value": "Carrot"}, "enabled": {"ts": 1673991089422, "value": false}, "pinned_model": {"ts": 1673991089428, "value": ""}, "recommended_model": {"ts": 1673991089432, "value": ""}}, "b987b450-3cb8-439d-a2bb-b1fda45041ac": {"carbon_name": {"ts": 1673991088895, "value": "legacy - cauliflower"}, "common_name": {"ts": 1673991088890, "value": "legacy - cauliflower"}, "enabled": {"ts": 1673991088898, "value": false}, "pinned_model": {"ts": 1673991088900, "value": ""}, "recommended_model": {"ts": 1673991088905, "value": ""}}, "b9d5df47-3e57-420d-b375-915042236194": {"carbon_name": {"ts": 1674675304369, "value": "legacy - mizuna"}, "common_name": {"ts": 1674675304362, "value": "legacy - mizuna"}, "enabled": {"ts": 1674675304375, "value": false}, "pinned_model": {"ts": 1674675304382, "value": ""}, "recommended_model": {"ts": 1674675304387, "value": ""}}, "ba0180e4-bcc6-43ca-9a91-07670a155939": {"carbon_name": {"ts": 1673991088518, "value": "legacy - onion"}, "common_name": {"ts": 1673991088515, "value": "legacy - onion"}, "enabled": {"ts": 1673991088522, "value": false}, "pinned_model": {"ts": 1673991088526, "value": ""}, "recommended_model": {"ts": 1673991088529, "value": ""}}, "bbee3cf2-76c6-4484-9e5f-1a5db89cd6c7": {"carbon_name": {"ts": 1673991088988, "value": "legacy - bok_choy"}, "common_name": {"ts": 1673991088986, "value": "legacy - bok_choy"}, "enabled": {"ts": 1673991088991, "value": false}, "pinned_model": {"ts": 1673991088995, "value": ""}, "recommended_model": {"ts": 1673991088999, "value": ""}}, "c9db109c-138b-47d3-9f2c-131c3b3c1669": {"carbon_name": {"ts": 1675471900642, "value": "Wheat"}, "common_name": {"ts": 1675471900636, "value": "Wheat"}, "enabled": {"ts": 1675471900646, "value": false}, "pinned_model": {"ts": 1675471900652, "value": ""}, "recommended_model": {"ts": 1675471900661, "value": ""}}, "ca3f51d9-03b5-4dd5-8c75-c830cbefd66a": {"carbon_name": {"ts": 1673991089136, "value": "legacy - cucumber"}, "common_name": {"ts": 1673991089132, "value": "legacy - cucumber"}, "enabled": {"ts": 1673991089139, "value": false}, "pinned_model": {"ts": 1673991089142, "value": ""}, "recommended_model": {"ts": 1673991089145, "value": ""}}, "ce4d8c01-96a7-419f-b582-72be63a42659": {"carbon_name": {"ts": 1675805497358, "value": "<PERSON><PERSON><PERSON><PERSON>"}, "common_name": {"ts": 1675805497352, "value": "<PERSON><PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1675805497366, "value": false}, "pinned_model": {"ts": 1675805497371, "value": ""}, "recommended_model": {"ts": 1675805497381, "value": ""}}, "d1d4f15e-5a86-4fbf-8b2d-d151069cb959": {"carbon_name": {"ts": 1673991088363, "value": "Solanum lycopersicum"}, "common_name": {"ts": 1673991088361, "value": "Tomatos"}, "enabled": {"ts": 1673991088366, "value": false}, "pinned_model": {"ts": 1673991088369, "value": ""}, "recommended_model": {"ts": 1673991088372, "value": ""}}, "d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b": {"carbon_name": {"ts": 1673991089486, "value": "Allium cepa L."}, "common_name": {"ts": 1673991089482, "value": "Onion"}, "enabled": {"ts": 1673991089490, "value": false}, "pinned_model": {"ts": 1673991089493, "value": ""}, "recommended_model": {"ts": 1673991089498, "value": ""}}, "d701d78a-8799-41be-a49e-412f9c3466ad": {"carbon_name": {"ts": 1673991089714, "value": "legacy - test"}, "common_name": {"ts": 1673991089711, "value": "legacy - test"}, "enabled": {"ts": 1673991089717, "value": false}, "pinned_model": {"ts": 1673991089720, "value": ""}, "recommended_model": {"ts": 1673991089722, "value": ""}}, "dc136a4f-4406-44d1-b5cc-07628ffec8eb": {"carbon_name": {"ts": 1673991088266, "value": "Brassica rapa subsp. chinensis"}, "common_name": {"ts": 1673991088264, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1673991088270, "value": false}, "pinned_model": {"ts": 1673991088274, "value": ""}, "recommended_model": {"ts": 1673991088277, "value": ""}}, "dd5331f6-e938-42ff-9c9e-04ab1aef8ce3": {"carbon_name": {"ts": 1675367488929, "value": "Cotton"}, "common_name": {"ts": 1675367488926, "value": "Cotton"}, "enabled": {"ts": 1675367488932, "value": false}, "pinned_model": {"ts": 1675367488938, "value": ""}, "recommended_model": {"ts": 1675367488943, "value": ""}}, "e01ad0a4-6c8b-438b-bf64-fa6edd0f918a": {"carbon_name": {"ts": 1673991088461, "value": "Brassica oleracea var. sabellica"}, "common_name": {"ts": 1673991088459, "value": "Curly <PERSON>"}, "enabled": {"ts": 1673991088463, "value": false}, "pinned_model": {"ts": 1673991088465, "value": ""}, "recommended_model": {"ts": 1673991088468, "value": ""}}, "e1af8684-92fb-4a1e-bd6e-0c936379d415": {"carbon_name": {"ts": 1675819648993, "value": "Dandelion"}, "common_name": {"ts": 1675819648988, "value": "Dandelion"}, "enabled": {"ts": 1675819648999, "value": false}, "pinned_model": {"ts": 1675819649006, "value": ""}, "recommended_model": {"ts": 1675819649013, "value": ""}}, "e9080966-ab04-4e8c-a7e7-29f66aeebaee": {"carbon_name": {"ts": 1673991088476, "value": "Brassica oleracea var. palmifolia"}, "common_name": {"ts": 1673991088474, "value": "<PERSON><PERSON><PERSON>"}, "enabled": {"ts": 1673991088479, "value": false}, "pinned_model": {"ts": 1673991088482, "value": ""}, "recommended_model": {"ts": 1673991088485, "value": ""}}, "e9fcaa61-e114-4e47-bf48-56f70a20e636": {"carbon_name": {"ts": 1673991088432, "value": "Lactuca sativa L. var. longifolia"}, "common_name": {"ts": 1673991088427, "value": "<PERSON><PERSON>"}, "enabled": {"ts": 1673991088436, "value": false}, "pinned_model": {"ts": 1673991088441, "value": ""}, "recommended_model": {"ts": 1673991088450, "value": ""}}, "ea09168c-12bb-4228-b0a3-159c13c4adb4": {"carbon_name": {"ts": 1673991088311, "value": "Lactuca Sativa"}, "common_name": {"ts": 1673991088306, "value": "Butter Lettuce"}, "enabled": {"ts": 1673991088314, "value": false}, "pinned_model": {"ts": 1673991088317, "value": ""}, "recommended_model": {"ts": 1673991088319, "value": ""}}, "ebe2a5be-eb14-44c6-b051-8b872a695802": {"carbon_name": {"ts": 1673991089084, "value": "Chard (low density)"}, "common_name": {"ts": 1673991089080, "value": "Chard (low density)"}, "enabled": {"ts": 1673991089087, "value": false}, "pinned_model": {"ts": 1673991089089, "value": ""}, "recommended_model": {"ts": 1673991089091, "value": ""}}, "f03f5553-b143-408c-a2a1-23f33b401704": {"carbon_name": {"ts": 1673991089907, "value": "Carbon crosses"}, "common_name": {"ts": 1673991089904, "value": "Carbon crosses"}, "enabled": {"ts": 1673991089912, "value": false}, "pinned_model": {"ts": 1673991089915, "value": ""}, "recommended_model": {"ts": 1673991089919, "value": ""}}, "f2c24ccb-b138-4e69-86e9-9b811688aa28": {"carbon_name": {"ts": 1673991089561, "value": "Unknown <PERSON><PERSON>"}, "common_name": {"ts": 1673991089557, "value": "Unknown <PERSON><PERSON>"}, "enabled": {"ts": 1673991089566, "value": false}, "pinned_model": {"ts": 1673991089569, "value": ""}, "recommended_model": {"ts": 1673991089573, "value": ""}}, "f4941bd1-1d09-48e5-8d49-e7e68a1e8d59": {"carbon_name": {"ts": 1675460809970, "value": "Cannabis"}, "common_name": {"ts": 1675460809964, "value": "Cannabis"}, "enabled": {"ts": 1675460809973, "value": false}, "pinned_model": {"ts": 1675460809980, "value": ""}, "recommended_model": {"ts": 1675460809987, "value": ""}}, "f4a21ae3-72bc-4888-bb1e-8ffcbd8e28eb": {"carbon_name": {"ts": 1673991088345, "value": "<PERSON><PERSON><PERSON> sativus"}, "common_name": {"ts": 1673991088342, "value": "Radishes"}, "enabled": {"ts": 1673991088348, "value": false}, "pinned_model": {"ts": 1673991088351, "value": ""}, "recommended_model": {"ts": 1673991088354, "value": ""}}, "f50721cb-2953-4773-b6b5-76b6ae042f38": {"carbon_name": {"ts": 1675460810000, "value": "Soybean"}, "common_name": {"ts": 1675460809996, "value": "Soybean"}, "enabled": {"ts": 1675460810003, "value": false}, "pinned_model": {"ts": 1675460810007, "value": ""}, "recommended_model": {"ts": 1675460810012, "value": ""}}, "fd0ec530-c322-423c-be01-da69a1cf17ca": {"carbon_name": {"ts": 1675819649035, "value": "<PERSON><PERSON><PERSON>ber"}, "common_name": {"ts": 1675819649029, "value": "<PERSON><PERSON><PERSON>ber"}, "enabled": {"ts": 1675819649040, "value": false}, "pinned_model": {"ts": 1675819649045, "value": ""}, "recommended_model": {"ts": 1675819649051, "value": ""}}, "ts": 1675892539040}, "crop_models": {"__unsync_erased__": null, "ts": 0}, "current_crop": {"ts": 1674001093946, "value": "test-treadkill"}, "current_crop_id": {"ts": 0, "value": ""}, "customer": {"ts": 0, "value": ""}, "dashboard": {"metrics": {"efficiency": {"enabled": {"ts": 1675464203998, "value": true}, "good_threshold_pct": {"ts": 0, "value": 90}, "medium_threshold_pct": {"ts": 0, "value": 75}}, "error_rate": {"enabled": {"ts": 1675464195582, "value": true}, "good_threshold_pct": {"ts": 0, "value": 10}, "medium_threshold_pct": {"ts": 0, "value": 25}}, "extra_conclusions": {"__unsync_erased__": null, "ts": 0}}}, "min_speed_for_weeding_indication_mph": {"ts": 0, "value": 0.01}, "model_expiration_days": {"ts": 0, "value": 30}, "pinned_noncrop_models": {"p2p": {"ts": 1668652645239, "value": "c4d13ea4ee9c10df7abd421dfe2dc50f"}}, "power_on_time_seconds": {"ts": 0, "value": 600}, "preferred_crops": {"__unsync_erased__": null, "ts": 0}, "support_phone": {"ts": 0, "value": ""}, "support_slack": {"ts": 0, "value": "#support-null"}, "veselka_url": {"ts": 0, "value": "https://veselka.cloud.carbonrobotics.com"}, "weeding_update_db_interval_sec": {"ts": 0, "value": 60}}, "data_upload_manager": {"admin": {"ts": 0, "value": 1}, "ambiguous_max_score": {"ts": 0, "value": 0.6}, "ambiguous_min_score": {"ts": 0, "value": 0.4}, "completed_transfer_expiration_hours": {"ts": 0, "value": 48}, "emergency_capture_proportions": {"ambiguous_crop_count": {"ts": 0, "value": 0.1}, "ambiguous_weed_count": {"ts": 0, "value": 0.1}, "crop_margin_max": {"ts": 0, "value": 0.1}, "recency": {"ts": 0, "value": 0.1}, "weed_margin_max": {"ts": 0, "value": 0.1}, "weed_tracking": {"ts": 0, "value": 0.1}}, "emergency_capture_rate": {"ts": 0, "value": 10.0}, "emergency_capture_target": {"ts": 0, "value": 100}, "emergency_image_expiration_hours": {"ts": 0, "value": 72}, "emergency_session_name": {"ts": 0, "value": ""}, "emergency_upload_target": {"ts": 0, "value": 100}, "lightweight_burst_records": {"max_uploads": {"ts": 0, "value": 100}, "upload_interval": {"ts": 0, "value": 30}}, "margin_max_percentage": {"ts": 0, "value": 0.5}, "max_images_per_24_hours": {"ts": 0, "value": 300}, "max_images_per_hour": {"ts": 0, "value": 10}, "max_queue_size": {"ts": 0, "value": 300}, "min_ambiguous_crop_count_score": {"ts": 0, "value": 0.0}, "min_ambiguous_weed_count_score": {"ts": 0, "value": 0.0}, "min_crop_margin_max_score": {"ts": 0, "value": 0.9}, "min_margin_max_score": {"ts": 0, "value": 0.7}, "min_random_score": {"ts": 0, "value": 0.9}, "min_recency_score": {"ts": 0, "value": 0.0}, "min_weed_margin_max_score": {"ts": 0, "value": 0.9}, "min_weed_tracking_score": {"ts": 0, "value": 0.7}, "offline_agent_max_load": {"ts": 0, "value": 0.99}, "p2p_capture": {"max_uploads": {"ts": 0, "value": 100}, "retrieval_interval_seconds": {"ts": 0, "value": 240}, "success_ratio": {"ts": 0, "value": 0.2}, "upload_interval_seconds": {"ts": 0, "value": 240}}, "predict_camera_aperture": {"ts": 0, "value": "f/5.6"}, "random_percentage": {"ts": 0, "value": 0.5}, "retrieval_interval_seconds": {"ts": 0, "value": 180}, "single_predict_emergency_capture": {"enable_single_predict_capture": {"ts": 0, "value": false}, "predict_cam_id": {"ts": 0, "value": "predict1"}, "row_ind": {"ts": 0, "value": 1}}, "target_camera_aperture": {"ts": 0, "value": "f/5"}, "to_upload_expiration_hours": {"ts": 0, "value": 48}, "transfer_interval_seconds": {"ts": 0, "value": 30}, "upload_interval_seconds": {"ts": 0, "value": 300}, "upload_proportions": {"ambiguous_crop_count": {"ts": 0, "value": 0.1}, "ambiguous_weed_count": {"ts": 0, "value": 0.1}, "crop_margin_max": {"ts": 0, "value": 0.1}, "recency": {"ts": 0, "value": 0.1}, "weed_margin_max": {"ts": 0, "value": 0.1}, "weed_tracking": {"ts": 0, "value": 0.1}}, "use_offline_upload_flow": {"ts": 0, "value": false}, "use_online_upload_flow": {"ts": 0, "value": false}, "weed_tracking_percentage": {"ts": 0, "value": 0.5}, "weeding_diagnostics": {"max_retries": {"ts": 0, "value": 20}, "max_timeout_sec": {"ts": 0, "value": 3600}}}, "hardware_manager": {"device_overrides": {"extra_params": {"__unsync_erased__": null, "ts": 0}, "extra_params_encoded": {"__unsync_erased__": null, "ts": 0}, "scanner_params": {"__unsync_erased__": null, "ts": 0}, "skip_list": {"__unsync_erased__": null, "ts": 0}, "strobe_control_params": {"exposure_us": {"ts": 0, "value": 800}, "period_us": {"ts": 0, "value": 33333}, "targets_per_predict_ratio": {"ts": 0, "value": 5}}}, "humidity_bypass": {"ts": 0, "value": false}, "supervisory_plc_variant": {"ts": 0, "value": ""}, "temp_bypass": {"ts": 0, "value": false}, "velocity_smoothing": {"ts": 0, "value": 50}}, "host_check": {"disk_space_error_pct": {"ts": 0, "value": 95}, "disk_space_warn_pct": {"ts": 0, "value": 75}, "max_gpu_temp_C": {"ts": 0, "value": 82}, "ptp_alarm_wait_before_alert_time_sec": {"ts": 0, "value": 10}}, "metrics_aggregator": {"device_overrides": {"extra_params": {"__unsync_erased__": null, "ts": 0}, "extra_params_encoded": {"__unsync_erased__": null, "ts": 0}, "scanner_params": {"__unsync_erased__": null, "ts": 0}, "skip_list": {"__unsync_erased__": null, "ts": 0}, "strobe_control_params": {"exposure_us": {"ts": 0, "value": 800}, "period_us": {"ts": 0, "value": 33333}, "targets_per_predict_ratio": {"ts": 0, "value": 5}}}}}, "common": {"almanac": {"base_time": {"ts": 0, "value": 50.0}, "base_time_multiplier": {"ts": 0, "value": 50.0}, "default_cat_importance": {"ts": 0, "value": 1.0}, "default_multiplier": {"ts": 0, "value": 1.0}, "default_size_importance": {"ts": 0, "value": 1.0}, "size_categories": {"__unsync_erased__": null, "ts": 0}, "static_kill_time": {"ts": 1675813572042, "value": 2000}, "use_static_kill_time": {"ts": 1675813524183, "value": true}, "weed_categories": {"__unsync_erased__": null, "ts": 0}}, "crop_line_detection": {"algorithm": {"ts": 1674615940895, "value": "kde"}, "clamping_distance_mm": {"ts": 0, "value": 200}, "default_crop_radius_mm": {"ts": 0, "value": 1.0}, "enabled": {"ts": 1674592002150, "value": true}, "kde_a": {"ts": 0, "value": 1.0}, "kde_c": {"ts": 0, "value": 15.0}, "kde_nearby_mm": {"ts": 0, "value": 100.0}, "kde_precision_mm": {"ts": 0, "value": 10.0}, "kde_select_factor": {"ts": 0, "value": 5.0}, "min_items_in_crop_line": {"ts": 1674592094654, "value": 1}, "num_lines": {"ts": 0, "value": 8}, "outlier_removal_percent": {"ts": 0, "value": 25.0}, "row_padding_mm": {"ts": 0, "value": 5.0}, "search_radius_increment_mm": {"ts": 0, "value": 10}, "search_radius_initial_mm": {"ts": 0, "value": 10}, "search_radius_max_mm": {"ts": 0, "value": 50}, "smoothing_factor": {"ts": 1674616040193, "value": 1.0}, "use_clamping": {"ts": 1674616018408, "value": false}, "work_interval_ms": {"ts": 0, "value": 200}}, "cross_cam_deduplication": {"crop": {"delta_time_ms": {"ts": 0, "value": 50}, "enabled": {"ts": 0, "value": true}, "radius": {"ts": 0, "value": 6.0}}, "delta_time_ms": {"ts": 0, "value": 50}, "enabled": {"ts": 0, "value": true}, "radius": {"ts": 0, "value": 6.0}, "weed": {"delta_time_ms": {"ts": 0, "value": 50}, "enabled": {"ts": 0, "value": true}, "radius": {"ts": 0, "value": 6.0}}}, "cv_runtime": {"gpu_assignment_override": {"__unsync_erased__": null, "ts": 0}, "gpu_assignment_scheme": {"ts": 0, "value": ""}}, "deepweed": {"crop_point_threshold": {"ts": 1675995533507, "value": 0.5}, "crop_point_threshold_image_scoring": {"ts": 0, "value": 0.05}, "model_id": {"ts": 1675995533513, "value": "fut-20230126-qg6c89sy3y"}, "pointCategoriesLegacy": {"__unsync_erased__": null, "ts": 0}, "segmentationCategories": {"__unsync_erased__": null, "ts": 0}, "weed_point_threshold": {"ts": 1675995533501, "value": 0.5}, "weed_point_threshold_image_scoring": {"ts": 0, "value": 0.05}}, "disabled_rows": {"__unsync_erased__": null, "ts": 0}, "dynamic_banding_enabled": {"ts": 1674591973711, "value": true}, "environment": {"ts": 1671230639525, "value": "development"}, "experimental_flag_list": {"__unsync_erased__": null, "ts": 0}, "exposure_update_interval_minutes": {"ts": 0, "value": 2}, "feature_flags": {"auto_crosshair_calibration_feature": {"ts": 0, "value": false}, "crop_ids_feature": {"ts": 0, "value": false}, "dynamic_banding_feature": {"ts": 1675724168093, "value": true}, "mapping_feature": {"ts": 0, "value": false}, "messaging_feature": {"ts": 0, "value": false}, "startup_task_feature": {"ts": 0, "value": false}}, "furrows": {"model_path": {"ts": 0, "value": ""}}, "global_scheduler": {"add_max_age": {"ts": 0, "value": 2000}, "add_max_time": {"ts": 0, "value": 100}, "cancellable": {"ts": 0, "value": true}, "ignore_weeds": {"ts": 0, "value": false}, "merge_radius": {"ts": 0, "value": 2.5}, "missed_delete_ttl": {"ts": 0, "value": 5}, "multi_laser_enabled": {"ts": 0, "value": true}, "next_weed_look_ahead": {"ts": 0, "value": 40}, "scoring": {"ignore_weeds_out_of_bands": {"ts": 0, "value": false}, "laser_overhead_time": {"ts": 0, "value": 200}, "max_extermination_failures": {"ts": 0, "value": 3}, "min_detections_over_opportunities": {"ts": 0, "value": 0.0}, "pan_inset_filter": {"ts": 0, "value": 500}, "pan_offset": {"ts": 0, "value": 100}, "starting_weed_look_ahead": {"ts": 0, "value": 50}, "thread_pool_size": {"ts": 0, "value": 0}, "tilt_offset_back": {"ts": 0, "value": 1000}, "tilt_offset_back_killed": {"ts": 0, "value": 1000}, "tilt_offset_front": {"ts": 0, "value": 500}, "weights": {"being_shot": {"ts": 0, "value": 100.0}, "busy": {"ts": 0, "value": 500.0}, "category_importance": {"ts": 0, "value": 500.0}, "in_range": {"ts": 0, "value": 1000.0}, "pan": {"ts": 0, "value": 50.0}, "size": {"ts": 0, "value": 500.0}, "tilt": {"ts": 0, "value": 1000.0}}}, "tracking_service_port": {"ts": 0, "value": 65432}, "velocity_smoothing": {"debug_logging": {"ts": 0, "value": false}, "decrease_averaging": {"ts": 0, "value": 0.1}, "dist_meters": {"ts": 0, "value": 1.0}, "increase_averaging": {"ts": 0, "value": 0.05}, "kill_percent_target": {"ts": 0, "value": 1.0}, "max_vel_mph": {"ts": 0, "value": 1.5}, "max_weed_time_ms_for_vel": {"ts": 0, "value": 2000}, "min_delta_vel_mph": {"ts": 0, "value": 0.1}, "min_vel_mph": {"ts": 0, "value": 0.05}, "use_new_ve": {"ts": 0, "value": false}, "use_scheduler_pos": {"ts": 0, "value": true}, "velocity_offest": {"ts": 0, "value": 0.0}, "window_size_multiplier": {"ts": 0, "value": 1.0}}}, "greedy_thinning": {"bounding_box_horizontal_1": {"ts": 0, "value": 3.0}, "bounding_box_horizontal_2": {"ts": 0, "value": 6.0}, "bounding_box_vertical_1": {"ts": 0, "value": 18.0}, "bounding_box_vertical_2": {"ts": 0, "value": 6.0}, "size_filter": {"acceptable_variance": {"ts": 0, "value": 0.5}, "enabled": {"ts": 0, "value": false}, "samples_size": {"ts": 0, "value": 25}}, "window_offset": {"ts": 0, "value": 3.0}}, "laser_power": {"baseline": {"ts": 0, "value": 140.0}, "scaling_formula": {"a": {"ts": 0, "value": 1.0}, "b": {"ts": 0, "value": 0.0}}}, "metrics": {"conclusions": {"rolling_duration_s": {"ts": 0, "value": 60}}}, "p2p": {"capture_cache_interval_seconds": {"ts": 0, "value": 5}, "model_id": {"ts": 1668653008865, "value": "c4d13ea4ee9c10df7abd421dfe2dc50f"}}, "portal_host": {"ts": 0, "value": "customer.cloud.carbonrobotics.com:443"}, "row_width_in": {"ts": 0, "value": 80.0}, "shooting": {"laser_shoot_update_interval": {"ts": 0, "value": 25}, "rotary_and_p2p": {"max_p2p_target_switch_dist": {"ts": 0, "value": 25.0}, "p2p_on_target_count": {"ts": 0, "value": 100}, "p2p_target_switch_count": {"ts": 0, "value": 3}}}, "software_manager": {"enable_auto_restart": {"ts": 1671757467133, "value": false}, "previous_version": {"ts": 0, "value": ""}, "target_version": {"ts": 0, "value": ""}, "version_metadata_service_url": {"ts": 0, "value": "versionmetadata.cloud.carbonrobotics.com:443"}, "version_poll_interval_s": {"ts": 0, "value": 900}}, "static_banding_v2_enabled": {"ts": 0, "value": false}, "targeting_mode": {"thinning": {"algorithm": {"ts": 0, "value": 0}, "enabled": {"ts": 0, "value": false}}, "weeding": {"enabled": {"ts": 0, "value": true}}}, "temp_humidity_bypass": {"ts": 0, "value": false}, "thinning": {"enabled": {"ts": 0, "value": false}, "ideal_spacing": {"ts": 0, "value": 12.0}, "min_distance": {"ts": 0, "value": 9.0}, "phase_shifted": {"ts": 0, "value": true}, "row_start_skip_len": {"ts": 0, "value": 3.0}, "window_offset": {"ts": 0, "value": 6.0}}, "two_drop_thinning": {"enabled": {"ts": 0, "value": false}, "horizontal_search_dist": {"ts": 0, "value": 1.0}, "size_based_shooting": {"ts": 0, "value": false}, "vertical_search_dist": {"ts": 0, "value": 3.0}}, "use_controls_weed_tracking": {"ts": 0, "value": false}, "use_experimental_models": {"ts": 0, "value": false}, "use_treadkill": {"ts": 1675995555763, "value": false}, "weeding_diagnostics": {"end_recording_images_timeout_ms": {"ts": 0, "value": 5000}, "work_interval_ms": {"ts": 1674616139972, "value": 200}}, "weeding_metrics": {"area_persist_interval_sec": {"ts": 0, "value": 60}, "kill_count_persist_interval_sec": {"ts": 0, "value": 1}, "laser_time_persist_interval_sec": {"ts": 0, "value": 1}, "power_on_persist_interval_sec": {"ts": 0, "value": 60}}, "wheel_encoders": {"anomaly_detection_count": {"ts": 0, "value": 15}, "anomaly_detection_enabled": {"ts": 0, "value": true}, "back_left": {"diameter_in": {"ts": 0, "value": 32.391739672}, "enabled": {"ts": 1674524303148, "value": true}}, "back_right": {"diameter_in": {"ts": 0, "value": 32.391739672}, "enabled": {"ts": 1674524311686, "value": true}}, "front_left": {"diameter_in": {"ts": 0, "value": 32.391739672}, "enabled": {"ts": 0, "value": true}}, "front_right": {"diameter_in": {"ts": 0, "value": 32.391739672}, "enabled": {"ts": 0, "value": true}}, "max_allowable_percent_off": {"ts": 0, "value": 0.05}, "non_zero_delta_mm": {"ts": 0, "value": 10.0}}}, "row1": {"aimbot": {"algorithm": {"ts": 0, "value": "RotaryAndP2P"}, "banding": {"bands": {"__unsync_erased__": null, "ts": 0}, "enabled": {"ts": 0, "value": false}, "offset": {"ts": 0, "value": 0.0}, "unit_multiplier": {"ts": 0, "value": 1.0}}, "debug_trajectory_monitor": {"ts": 0, "value": false}, "dedup_radius_px": {"ts": 0, "value": 100}, "device_overrides": {"extra_params": {"__unsync_erased__": null, "ts": 0}, "extra_params_encoded": {"__unsync_erased__": null, "ts": 0}, "scanner_params": {"__unsync_erased__": null, "ts": 0}, "skip_list": {"__unsync_erased__": null, "ts": 0}, "strobe_control_params": {"exposure_us": {"ts": 0, "value": 800}, "period_us": {"ts": 0, "value": 33333}, "targets_per_predict_ratio": {"ts": 0, "value": 5}}}, "emi_test_pulse_time_ms": {"ts": 0, "value": 100}, "enable_auto_focus": {"ts": 0, "value": false}, "follow_forever_pan_pos": {"ts": 0, "value": 0.5}, "geometric": {"predicts": {"__unsync_erased__": null, "ts": 0}, "scanners": {"__unsync_erased__": null, "ts": 0}}, "go_to_interval_sleep_ms": {"ts": 0, "value": 10}, "height_estimation": {"crop_search_radius": {"ts": 1675824342328, "value": 200.0}, "enabled": {"ts": 0, "value": true}, "num_lanes": {"ts": 1675824348758, "value": 20}, "only_weeds_for_global": {"ts": 0, "value": true}, "trajectory_smoothing_factor": {"ts": 0, "value": 0.0}, "weed_search_radius": {"ts": 0, "value": 200.0}}, "image_interval_sleep_ms": {"ts": 0, "value": 5}, "laser_test_time_ms": {"ts": 0, "value": 5000}, "load": {"lookback_ms": {"ts": 0, "value": 2000}, "percentile": {"ts": 0, "value": 90}, "target": {"ts": 0, "value": 0.8}}, "log_wheel_encoders": {"ts": 0, "value": false}, "match_perspective_across_predicts": {"ts": 0, "value": false}, "max_number_of_weeds": {"ts": 0, "value": 1000}, "max_p2p_match_fail": {"ts": 0, "value": 6}, "max_pos_y_offset_mm": {"ts": 1674681914423, "value": 2000.0}, "p2p_capture": {"delay_ms": {"ts": 0, "value": 10}, "enabled": {"ts": 0, "value": false}, "filter": {"__unsync_erased__": null, "ts": 0}, "miss_rate": {"ts": 0, "value": 0.02}, "rate": {"ts": 0, "value": 0.002}}, "positional_smoothing": {"ts": 0, "value": 0.0}, "scanners": {"__unsync_erased__": null, "scanner1": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801565832, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner10": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801582792, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner2": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801567662, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner3": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801569492, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner4": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801571414, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner5": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801573238, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner6": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801575156, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner7": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801577106, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner8": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801579006, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner9": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 1675801580939, "value": false}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "ts": 1675801582792}, "short_follow_time_ms": {"ts": 0, "value": 1000}, "target_burst_capture": {"filter": {"__unsync_erased__": null, "ts": 0}, "rate": {"ts": 0, "value": 0.0}}, "target_burst_capture_filter": {"__unsync_erased__": null, "ts": 0}, "target_safety_zone": {"backward_px": {"ts": 0, "value": 10}, "forward_px": {"ts": 0, "value": 40}, "side_px": {"ts": 0, "value": 10}}, "tilt_mirror_height_mm": {"ts": 0, "value": 850.9}, "tilt_mirror_z_offset_mm": {"ts": 0, "value": 167.64}, "use_low_latency_initial_move": {"ts": 0, "value": false}, "velocity_estimator": {"kill_percent_target": {"ts": 0, "value": 1.0}, "kpt_override_enabled": {"ts": 0, "value": false}}, "weed_accuracy_sampling": {"rate": {"ts": 0, "value": 0.0}, "record_enabled": {"ts": 0, "value": false}}, "weed_tracking": {"crop_safety_radius_mm": {"ts": 0, "value": 1.0}, "distance_bucket_size": {"ts": 0, "value": 400.0}, "error_bucket_size": {"ts": 0, "value": 25.0}, "error_calculation": {"crop_weight": {"ts": 0, "value": 0.0}}, "max_age_in_bounds": {"ts": 0, "value": 1000}, "max_age_out_of_bounds": {"ts": 0, "value": 20000}}}, "cv": {"cameras": {"__unsync_erased__": null, "predict1": {"auto_brightness": {"exposure_delta_us": {"ts": 1673387952439, "value": 2.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1673387956036, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1673387957882, "value": 30.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1673387961452, "value": true}, "buffer_enabled": {"ts": 1673387965042, "value": true}, "buffer_max_size": {"ts": 1675809836921, "value": 20}, "burst_record_predict_save_enabled": {"ts": 1673387968742, "value": true}, "camera_matrix": {"cx": {"ts": 1673387970530, "value": 1621.67}, "cy": {"ts": 1673387972332, "value": 2002.34}, "fx": {"ts": 1673387974125, "value": 1006.37}, "fy": {"ts": 1673387975942, "value": 1006.37}}, "deepweed_enabled": {"ts": 1674249099293, "value": true}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675713178456, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1673387981273, "value": -0.552499}, "k2": {"ts": 1673387983146, "value": 0.0769327}, "k3": {"ts": 1673387984972, "value": 0.000323339}, "k4": {"ts": 1673387986732, "value": -0.545659}, "k5": {"ts": 1673387988579, "value": 0.0731298}, "k6": {"ts": 1673387990427, "value": 0.000863996}, "p1": {"ts": 1673387992194, "value": 0.000204479}, "p2": {"ts": 1673387993987, "value": -0.000849495}, "s1": {"ts": 1673387995709, "value": 0.0}, "s2": {"ts": 1673387999318, "value": 0.0}, "s3": {"ts": 1673388002904, "value": 0.0}, "s4": {"ts": 1673388006533, "value": 0.0}, "tau1": {"ts": 1673388010235, "value": 0.0}, "tau2": {"ts": 1673388013833, "value": 0.0}}, "enabled": {"ts": 1673388019446, "value": true}, "exposure_us": {"ts": 1673388021210, "value": 122.43}, "flip": {"ts": 1673388024726, "value": true}, "focus_metric_enabled": {"ts": 1673388028261, "value": false}, "furrows_enabled": {"ts": 1673388031856, "value": false}, "gain_db": {"ts": 1673388033646, "value": 0.0}, "gamma": {"ts": 1673388037214, "value": 0.7}, "gpu_id": {"ts": 1673388038945, "value": 0}, "ip_address": {"ts": 1673388042450, "value": "**********"}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1673388046001, "value": true}, "model": {"ts": 1673388047839, "value": "ATL120S-C"}, "p2p_enabled": {"ts": 1675467143578, "value": false}, "ppi": {"ts": 1673388053315, "value": 200.0}, "ptp": {"ts": 1673388056920, "value": true}, "publisher_reduction_ratio": {"ts": 1673388058726, "value": 6}, "random_image_scoring_enabled": {"ts": 1673388062266, "value": false}, "roi": {"height": {"ts": 1673388064056, "value": 3000.0}, "offset_x": {"ts": 1673388065884, "value": 0.0}, "offset_y": {"ts": 1673388069504, "value": 0.0}, "width": {"ts": 1673388073162, "value": 4096.0}}, "serial_number": {"ts": 0, "value": ""}, "sim_fps": {"ts": 1673388074884, "value": 1.0}, "strobing": {"ts": 1673388080265, "value": true}, "transpose": {"ts": 1673388083839, "value": false}, "vendor": {"ts": 1673388085627, "value": "mock"}, "white_balance": {"blue": {"ts": 1673388087421, "value": 2.5603}, "green": {"ts": 1673388089226, "value": 1.00366}, "red": {"ts": 1673388091031, "value": 1.53613}}}, "predict2": {"auto_brightness": {"exposure_delta_us": {"ts": 1673388092802, "value": 2.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1673388096368, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1673388098146, "value": 30.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1673388101660, "value": true}, "buffer_enabled": {"ts": 1673388105244, "value": true}, "buffer_max_size": {"ts": 1675809842035, "value": 20}, "burst_record_predict_save_enabled": {"ts": 1673388108885, "value": true}, "camera_matrix": {"cx": {"ts": 1673388110702, "value": 1621.67}, "cy": {"ts": 1673388112537, "value": 2002.34}, "fx": {"ts": 1673388114344, "value": 1006.37}, "fy": {"ts": 1673388116133, "value": 1006.37}}, "deepweed_enabled": {"ts": 1674249096557, "value": true}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675713184242, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1673388121533, "value": -0.552499}, "k2": {"ts": 1673388123328, "value": 0.0769327}, "k3": {"ts": 1673388125143, "value": 0.000323339}, "k4": {"ts": 1673388126945, "value": -0.545659}, "k5": {"ts": 1673388128698, "value": 0.0731298}, "k6": {"ts": 1673388130421, "value": 0.000863996}, "p1": {"ts": 1673388132239, "value": 0.000204479}, "p2": {"ts": 1673388134038, "value": -0.000849495}, "s1": {"ts": 1673388135766, "value": 0.0}, "s2": {"ts": 1673388139447, "value": 0.0}, "s3": {"ts": 1673388143044, "value": 0.0}, "s4": {"ts": 1673388146670, "value": 0.0}, "tau1": {"ts": 1673388150299, "value": 0.0}, "tau2": {"ts": 1673388153811, "value": 0.0}}, "enabled": {"ts": 1673388159174, "value": true}, "exposure_us": {"ts": 1673388160940, "value": 122.43}, "flip": {"ts": 1673388164540, "value": true}, "focus_metric_enabled": {"ts": 1673388168152, "value": false}, "furrows_enabled": {"ts": 1673388171686, "value": false}, "gain_db": {"ts": 1673388173550, "value": 0.0}, "gamma": {"ts": 1673388177136, "value": 0.7}, "gpu_id": {"ts": 1673388178925, "value": 0}, "ip_address": {"ts": 1673388182525, "value": "**********"}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1673388186106, "value": true}, "model": {"ts": 1673388187859, "value": "ATL120S-C"}, "p2p_enabled": {"ts": 1675467138479, "value": false}, "ppi": {"ts": 1673388193243, "value": 200.0}, "ptp": {"ts": 1673388196819, "value": true}, "publisher_reduction_ratio": {"ts": 1673388198631, "value": 6}, "random_image_scoring_enabled": {"ts": 1673388202225, "value": false}, "roi": {"height": {"ts": 1673388204057, "value": 3000.0}, "offset_x": {"ts": 1673388205864, "value": 0.0}, "offset_y": {"ts": 1673388209469, "value": 0.0}, "width": {"ts": 1673388213050, "value": 4096.0}}, "serial_number": {"ts": 0, "value": ""}, "sim_fps": {"ts": 1673388214816, "value": 1.0}, "strobing": {"ts": 1673388220272, "value": true}, "transpose": {"ts": 1673388223828, "value": false}, "vendor": {"ts": 1673388225640, "value": "mock"}, "white_balance": {"blue": {"ts": 1673388227395, "value": 2.5603}, "green": {"ts": 1673388229266, "value": 1.00366}, "red": {"ts": 1673388231001, "value": 1.53613}}}, "predict3": {"auto_brightness": {"exposure_delta_us": {"ts": 1673388232869, "value": 2.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1673388236504, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1673388238341, "value": 30.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1673388241912, "value": true}, "buffer_enabled": {"ts": 1673388245542, "value": true}, "buffer_max_size": {"ts": 1675809847089, "value": 20}, "burst_record_predict_save_enabled": {"ts": 1673388249146, "value": true}, "camera_matrix": {"cx": {"ts": 1673388250965, "value": 1621.67}, "cy": {"ts": 1673388252824, "value": 2002.34}, "fx": {"ts": 1673388254642, "value": 1006.37}, "fy": {"ts": 1673388256491, "value": 1006.37}}, "deepweed_enabled": {"ts": 1674249093678, "value": true}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675713190399, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1673388261884, "value": -0.552499}, "k2": {"ts": 1673388263699, "value": 0.0769327}, "k3": {"ts": 1673388265528, "value": 0.000323339}, "k4": {"ts": 1673388267340, "value": -0.545659}, "k5": {"ts": 1673388269105, "value": 0.0731298}, "k6": {"ts": 1673388270864, "value": 0.000863996}, "p1": {"ts": 1673388272638, "value": 0.000204479}, "p2": {"ts": 1673388274409, "value": -0.000849495}, "s1": {"ts": 1673388276171, "value": 0.0}, "s2": {"ts": 1673388279752, "value": 0.0}, "s3": {"ts": 1673388283259, "value": 0.0}, "s4": {"ts": 1673388286846, "value": 0.0}, "tau1": {"ts": 1673388290463, "value": 0.0}, "tau2": {"ts": 1673388294020, "value": 0.0}}, "enabled": {"ts": 1673388299463, "value": true}, "exposure_us": {"ts": 1673388301195, "value": 122.43}, "flip": {"ts": 1673388304805, "value": true}, "focus_metric_enabled": {"ts": 1673388308403, "value": false}, "furrows_enabled": {"ts": 1673388312000, "value": false}, "gain_db": {"ts": 1673388313777, "value": 0.0}, "gamma": {"ts": 1673388317371, "value": 0.7}, "gpu_id": {"ts": 1673388319247, "value": 0}, "ip_address": {"ts": 1673388322793, "value": "**********"}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1673388326360, "value": true}, "model": {"ts": 1673388328156, "value": "ATL120S-C"}, "p2p_enabled": {"ts": 1675467133386, "value": false}, "ppi": {"ts": 1673388333666, "value": 200.0}, "ptp": {"ts": 1673388337302, "value": true}, "publisher_reduction_ratio": {"ts": 1673388339113, "value": 6}, "random_image_scoring_enabled": {"ts": 1673388342647, "value": false}, "roi": {"height": {"ts": 1673388344468, "value": 3000.0}, "offset_x": {"ts": 1673388346325, "value": 0.0}, "offset_y": {"ts": 1673388349964, "value": 0.0}, "width": {"ts": 1673388353538, "value": 4096.0}}, "serial_number": {"ts": 0, "value": ""}, "sim_fps": {"ts": 1673388355386, "value": 1.0}, "strobing": {"ts": 1673388360843, "value": true}, "transpose": {"ts": 1673388364482, "value": false}, "vendor": {"ts": 1673388366220, "value": "mock"}, "white_balance": {"blue": {"ts": 1673388368023, "value": 2.5603}, "green": {"ts": 1673388369844, "value": 1.00366}, "red": {"ts": 1673388371619, "value": 1.53613}}}, "predict4": {"auto_brightness": {"exposure_delta_us": {"ts": 1673388373447, "value": 2.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1673388377001, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1673388378815, "value": 30.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1673388382417, "value": true}, "buffer_enabled": {"ts": 1673388386002, "value": true}, "buffer_max_size": {"ts": 1675809852023, "value": 20}, "burst_record_predict_save_enabled": {"ts": 1673388389612, "value": true}, "camera_matrix": {"cx": {"ts": 1673388391417, "value": 1621.67}, "cy": {"ts": 1673388393212, "value": 2002.34}, "fx": {"ts": 1673388395010, "value": 1006.37}, "fy": {"ts": 1673388396772, "value": 1006.37}}, "deepweed_enabled": {"ts": 1674249090797, "value": true}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675713196290, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1673388402184, "value": -0.552499}, "k2": {"ts": 1673388403999, "value": 0.0769327}, "k3": {"ts": 1673388405864, "value": 0.000323339}, "k4": {"ts": 1673388407705, "value": -0.545659}, "k5": {"ts": 1673388409532, "value": 0.0731298}, "k6": {"ts": 1673388411351, "value": 0.000863996}, "p1": {"ts": 1673388413131, "value": 0.000204479}, "p2": {"ts": 1673388414921, "value": -0.000849495}, "s1": {"ts": 1673388416733, "value": 0.0}, "s2": {"ts": 1673388420387, "value": 0.0}, "s3": {"ts": 1673388424028, "value": 0.0}, "s4": {"ts": 1673388427660, "value": 0.0}, "tau1": {"ts": 1673388431274, "value": 0.0}, "tau2": {"ts": 1673388434771, "value": 0.0}}, "enabled": {"ts": 1673388440138, "value": true}, "exposure_us": {"ts": 1673388441988, "value": 122.43}, "flip": {"ts": 1673388445556, "value": true}, "focus_metric_enabled": {"ts": 1673388449228, "value": false}, "furrows_enabled": {"ts": 1673388452829, "value": false}, "gain_db": {"ts": 1673388454592, "value": 0.0}, "gamma": {"ts": 1673388458148, "value": 0.7}, "gpu_id": {"ts": 1673388459948, "value": 0}, "ip_address": {"ts": 1673388463488, "value": "**********"}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1673388467006, "value": true}, "model": {"ts": 1673388468790, "value": "ATL120S-C"}, "p2p_enabled": {"ts": 1675467124413, "value": false}, "ppi": {"ts": 1673388474237, "value": 200.0}, "ptp": {"ts": 1673388477851, "value": true}, "publisher_reduction_ratio": {"ts": 1673388479580, "value": 6}, "random_image_scoring_enabled": {"ts": 1673388483124, "value": false}, "roi": {"height": {"ts": 1673388484928, "value": 3000.0}, "offset_x": {"ts": 1673388486715, "value": 0.0}, "offset_y": {"ts": 1673388490351, "value": 0.0}, "width": {"ts": 1673388493917, "value": 4096.0}}, "serial_number": {"ts": 0, "value": ""}, "sim_fps": {"ts": 1673388495692, "value": 1.0}, "strobing": {"ts": 1673388501126, "value": true}, "transpose": {"ts": 1673388504707, "value": false}, "vendor": {"ts": 1673388506494, "value": "mock"}, "white_balance": {"blue": {"ts": 1673388508313, "value": 2.5603}, "green": {"ts": 1673388510150, "value": 1.00366}, "red": {"ts": 1673388511983, "value": 1.53613}}}, "target1": {"auto_brightness": {"exposure_delta_us": {"ts": 1675726547137, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675726550530, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675726552259, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675726555983, "value": true}, "buffer_enabled": {"ts": 1675726559544, "value": false}, "buffer_max_size": {"ts": 1675726561310, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675726564764, "value": false}, "camera_matrix": {"cx": {"ts": 1675726566563, "value": 0.0}, "cy": {"ts": 1675726570239, "value": 0.0}, "fx": {"ts": 1675726573705, "value": 0.0}, "fy": {"ts": 1675726577207, "value": 0.0}}, "deepweed_enabled": {"ts": 1675726582386, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730272351, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675726584102, "value": 0.0}, "k2": {"ts": 1675726587497, "value": 0.0}, "k3": {"ts": 1675726591081, "value": 0.0}, "k4": {"ts": 1675726594698, "value": 0.0}, "k5": {"ts": 1675726598169, "value": 0.0}, "k6": {"ts": 1675726601741, "value": 0.0}, "p1": {"ts": 1675726605414, "value": 0.0}, "p2": {"ts": 1675726608907, "value": 0.0}, "s1": {"ts": 1675726612289, "value": 0.0}, "s2": {"ts": 1675726615783, "value": 0.0}, "s3": {"ts": 1675726619311, "value": 0.0}, "s4": {"ts": 1675726622948, "value": 0.0}, "tau1": {"ts": 1675726626531, "value": 0.0}, "tau2": {"ts": 1675726630029, "value": 0.0}}, "enabled": {"ts": 1675726635453, "value": true}, "exposure_us": {"ts": 1675726637243, "value": 420.0}, "flip": {"ts": 1675726640722, "value": false}, "focus_metric_enabled": {"ts": 1675726644197, "value": false}, "furrows_enabled": {"ts": 1675726647909, "value": false}, "gain_db": {"ts": 1675726649721, "value": 0.0}, "gamma": {"ts": 1675726653326, "value": 0.0}, "gpu_id": {"ts": 1675726656792, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675726662053, "value": false}, "model": {"ts": 1675726663843, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675726667422, "value": true}, "ppi": {"ts": 1675726669271, "value": 182.0}, "ptp": {"ts": 1675726672881, "value": true}, "publisher_reduction_ratio": {"ts": 1675726674700, "value": 10}, "random_image_scoring_enabled": {"ts": 1675726678260, "value": false}, "roi": {"height": {"ts": 1675795285625, "value": 100.0}, "offset_x": {"ts": 1675726681752, "value": 328.0}, "offset_y": {"ts": 1675726683493, "value": 332.0}, "width": {"ts": 1675795287300, "value": 100.0}}, "serial_number": {"ts": 1675726686738, "value": "214500785"}, "sim_fps": {"ts": 1675726688423, "value": 1.0}, "strobing": {"ts": 1675726693541, "value": true}, "transpose": {"ts": 1675726697106, "value": true}, "vendor": {"ts": 1675726698822, "value": "mock"}, "white_balance": {"blue": {"ts": 1675726700571, "value": 2.32397}, "green": {"ts": 1675726702205, "value": 1.0}, "red": {"ts": 1675726705676, "value": 1.54736}}}, "target10": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727990812, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727994319, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727996177, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727999747, "value": true}, "buffer_enabled": {"ts": 1675728003335, "value": false}, "buffer_max_size": {"ts": 1675728005096, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675728008604, "value": false}, "camera_matrix": {"cx": {"ts": 1675728010411, "value": 0.0}, "cy": {"ts": 1675728013889, "value": 0.0}, "fx": {"ts": 1675728017521, "value": 0.0}, "fy": {"ts": 1675728021135, "value": 0.0}}, "deepweed_enabled": {"ts": 1675728026440, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730323686, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675728028164, "value": 0.0}, "k2": {"ts": 1675728031692, "value": 0.0}, "k3": {"ts": 1675728035249, "value": 0.0}, "k4": {"ts": 1675728038754, "value": 0.0}, "k5": {"ts": 1675728042225, "value": 0.0}, "k6": {"ts": 1675728045941, "value": 0.0}, "p1": {"ts": 1675728049419, "value": 0.0}, "p2": {"ts": 1675728052893, "value": 0.0}, "s1": {"ts": 1675728056338, "value": 0.0}, "s2": {"ts": 1675728059779, "value": 0.0}, "s3": {"ts": 1675728063214, "value": 0.0}, "s4": {"ts": 1675728066750, "value": 0.0}, "tau1": {"ts": 1675728070248, "value": 0.0}, "tau2": {"ts": 1675728073834, "value": 0.0}}, "enabled": {"ts": 1675728079199, "value": true}, "exposure_us": {"ts": 1675728081011, "value": 420.0}, "flip": {"ts": 1675728084560, "value": false}, "focus_metric_enabled": {"ts": 1675728088141, "value": false}, "furrows_enabled": {"ts": 1675728091567, "value": false}, "gain_db": {"ts": 1675728093400, "value": 0.0}, "gamma": {"ts": 1675728096921, "value": 0.0}, "gpu_id": {"ts": 1675728100389, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675728105793, "value": false}, "model": {"ts": 1675728107495, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675728110985, "value": true}, "ppi": {"ts": 1675728112692, "value": 182.0}, "ptp": {"ts": 1675728116356, "value": true}, "publisher_reduction_ratio": {"ts": 1675728118106, "value": 10}, "random_image_scoring_enabled": {"ts": 1675728121634, "value": false}, "roi": {"height": {"ts": 1675795316231, "value": 100.0}, "offset_x": {"ts": 1675728125126, "value": 328.0}, "offset_y": {"ts": 1675728126896, "value": 332.0}, "width": {"ts": 1675795317984, "value": 100.0}}, "serial_number": {"ts": 1675728130471, "value": "214500785"}, "sim_fps": {"ts": 1675728132263, "value": 1.0}, "strobing": {"ts": 1675728137591, "value": true}, "transpose": {"ts": 1675728141096, "value": true}, "vendor": {"ts": 1675728142897, "value": "mock"}, "white_balance": {"blue": {"ts": 1675728144720, "value": 2.32397}, "green": {"ts": 1675728146593, "value": 1.0}, "red": {"ts": 1675728150100, "value": 1.54736}}}, "target2": {"auto_brightness": {"exposure_delta_us": {"ts": 1675726707496, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675726710987, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675726712762, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675726716256, "value": true}, "buffer_enabled": {"ts": 1675726719789, "value": false}, "buffer_max_size": {"ts": 1675726721487, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675726724975, "value": false}, "camera_matrix": {"cx": {"ts": 1675726726703, "value": 0.0}, "cy": {"ts": 1675726730178, "value": 0.0}, "fx": {"ts": 1675726733701, "value": 0.0}, "fy": {"ts": 1675726737355, "value": 0.0}}, "deepweed_enabled": {"ts": 1675726742638, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730278355, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675726744364, "value": 0.0}, "k2": {"ts": 1675726747920, "value": 0.0}, "k3": {"ts": 1675726751523, "value": 0.0}, "k4": {"ts": 1675726755081, "value": 0.0}, "k5": {"ts": 1675726758628, "value": 0.0}, "k6": {"ts": 1675726762234, "value": 0.0}, "p1": {"ts": 1675726765884, "value": 0.0}, "p2": {"ts": 1675726769368, "value": 0.0}, "s1": {"ts": 1675726772953, "value": 0.0}, "s2": {"ts": 1675726776561, "value": 0.0}, "s3": {"ts": 1675726780081, "value": 0.0}, "s4": {"ts": 1675726783593, "value": 0.0}, "tau1": {"ts": 1675726787180, "value": 0.0}, "tau2": {"ts": 1675726790804, "value": 0.0}}, "enabled": {"ts": 1675726796334, "value": true}, "exposure_us": {"ts": 1675726798132, "value": 420.0}, "flip": {"ts": 1675726801609, "value": false}, "focus_metric_enabled": {"ts": 1675726805038, "value": false}, "furrows_enabled": {"ts": 1675726808509, "value": false}, "gain_db": {"ts": 1675726810345, "value": 0.0}, "gamma": {"ts": 1675726813907, "value": 0.0}, "gpu_id": {"ts": 1675726817359, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675726822635, "value": false}, "model": {"ts": 1675726824410, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675726827893, "value": true}, "ppi": {"ts": 1675726829643, "value": 182.0}, "ptp": {"ts": 1675726833233, "value": true}, "publisher_reduction_ratio": {"ts": 1675726835001, "value": 10}, "random_image_scoring_enabled": {"ts": 1675726838621, "value": false}, "roi": {"height": {"ts": 1675795288998, "value": 100.0}, "offset_x": {"ts": 1675726842250, "value": 328.0}, "offset_y": {"ts": 1675726844054, "value": 332.0}, "width": {"ts": 1675795290690, "value": 100.0}}, "serial_number": {"ts": 1675726847517, "value": "214500785"}, "sim_fps": {"ts": 1675726849248, "value": 1.0}, "strobing": {"ts": 1675726854495, "value": true}, "transpose": {"ts": 1675726858140, "value": true}, "vendor": {"ts": 1675726859921, "value": "mock"}, "white_balance": {"blue": {"ts": 1675726861748, "value": 2.32397}, "green": {"ts": 1675726863421, "value": 1.0}, "red": {"ts": 1675726866935, "value": 1.54736}}}, "target3": {"auto_brightness": {"exposure_delta_us": {"ts": 1675726868664, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675726872209, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675726873916, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675726877474, "value": true}, "buffer_enabled": {"ts": 1675726880840, "value": false}, "buffer_max_size": {"ts": 1675726882571, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675726886236, "value": false}, "camera_matrix": {"cx": {"ts": 1675726888034, "value": 0.0}, "cy": {"ts": 1675726891640, "value": 0.0}, "fx": {"ts": 1675726895068, "value": 0.0}, "fy": {"ts": 1675726898853, "value": 0.0}}, "deepweed_enabled": {"ts": 1675726904327, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730284096, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675726905995, "value": 0.0}, "k2": {"ts": 1675726909535, "value": 0.0}, "k3": {"ts": 1675726913076, "value": 0.0}, "k4": {"ts": 1675726916639, "value": 0.0}, "k5": {"ts": 1675726920194, "value": 0.0}, "k6": {"ts": 1675726923557, "value": 0.0}, "p1": {"ts": 1675726927040, "value": 0.0}, "p2": {"ts": 1675726930664, "value": 0.0}, "s1": {"ts": 1675726934121, "value": 0.0}, "s2": {"ts": 1675726937474, "value": 0.0}, "s3": {"ts": 1675726941028, "value": 0.0}, "s4": {"ts": 1675726944611, "value": 0.0}, "tau1": {"ts": 1675726948164, "value": 0.0}, "tau2": {"ts": 1675726951751, "value": 0.0}}, "enabled": {"ts": 1675726957041, "value": true}, "exposure_us": {"ts": 1675726958829, "value": 420.0}, "flip": {"ts": 1675726962367, "value": false}, "focus_metric_enabled": {"ts": 1675726965949, "value": false}, "furrows_enabled": {"ts": 1675726969559, "value": false}, "gain_db": {"ts": 1675726971312, "value": 0.0}, "gamma": {"ts": 1675726974843, "value": 0.0}, "gpu_id": {"ts": 1675726978370, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675726983570, "value": false}, "model": {"ts": 1675726985353, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675726988866, "value": true}, "ppi": {"ts": 1675726990624, "value": 182.0}, "ptp": {"ts": 1675726994067, "value": true}, "publisher_reduction_ratio": {"ts": 1675726995853, "value": 10}, "random_image_scoring_enabled": {"ts": 1675726999317, "value": false}, "roi": {"height": {"ts": 1675795292352, "value": 100.0}, "offset_x": {"ts": 1675727002835, "value": 328.0}, "offset_y": {"ts": 1675727004562, "value": 332.0}, "width": {"ts": 1675795294061, "value": 100.0}}, "serial_number": {"ts": 1675727008176, "value": "214500785"}, "sim_fps": {"ts": 1675727009954, "value": 1.0}, "strobing": {"ts": 1675727015191, "value": true}, "transpose": {"ts": 1675727018744, "value": true}, "vendor": {"ts": 1675727020460, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727022312, "value": 2.32397}, "green": {"ts": 1675727024052, "value": 1.0}, "red": {"ts": 1675727027638, "value": 1.54736}}}, "target4": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727029416, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727032991, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727034849, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727038410, "value": true}, "buffer_enabled": {"ts": 1675727041977, "value": false}, "buffer_max_size": {"ts": 1675727043701, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675727047220, "value": false}, "camera_matrix": {"cx": {"ts": 1675727049015, "value": 0.0}, "cy": {"ts": 1675727052655, "value": 0.0}, "fx": {"ts": 1675727056066, "value": 0.0}, "fy": {"ts": 1675727059532, "value": 0.0}}, "deepweed_enabled": {"ts": 1675727064975, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730289665, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675727066774, "value": 0.0}, "k2": {"ts": 1675727070332, "value": 0.0}, "k3": {"ts": 1675727073856, "value": 0.0}, "k4": {"ts": 1675727077373, "value": 0.0}, "k5": {"ts": 1675727080912, "value": 0.0}, "k6": {"ts": 1675727084344, "value": 0.0}, "p1": {"ts": 1675727087857, "value": 0.0}, "p2": {"ts": 1675727091290, "value": 0.0}, "s1": {"ts": 1675727094799, "value": 0.0}, "s2": {"ts": 1675727098163, "value": 0.0}, "s3": {"ts": 1675727101682, "value": 0.0}, "s4": {"ts": 1675727105164, "value": 0.0}, "tau1": {"ts": 1675727108550, "value": 0.0}, "tau2": {"ts": 1675727111936, "value": 0.0}}, "enabled": {"ts": 1675727117111, "value": true}, "exposure_us": {"ts": 1675727118874, "value": 420.0}, "flip": {"ts": 1675727122589, "value": false}, "focus_metric_enabled": {"ts": 1675727126170, "value": false}, "furrows_enabled": {"ts": 1675727129643, "value": false}, "gain_db": {"ts": 1675727131370, "value": 0.0}, "gamma": {"ts": 1675727134791, "value": 0.0}, "gpu_id": {"ts": 1675727138322, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675727143546, "value": false}, "model": {"ts": 1675727145293, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675727148775, "value": true}, "ppi": {"ts": 1675727150615, "value": 182.0}, "ptp": {"ts": 1675727154055, "value": true}, "publisher_reduction_ratio": {"ts": 1675727155827, "value": 10}, "random_image_scoring_enabled": {"ts": 1675727159406, "value": false}, "roi": {"height": {"ts": 1675795295767, "value": 100.0}, "offset_x": {"ts": 1675727162921, "value": 328.0}, "offset_y": {"ts": 1675727164573, "value": 332.0}, "width": {"ts": 1675795297508, "value": 100.0}}, "serial_number": {"ts": 1675727168144, "value": "214500785"}, "sim_fps": {"ts": 1675727169869, "value": 1.0}, "strobing": {"ts": 1675727175208, "value": true}, "transpose": {"ts": 1675727178695, "value": true}, "vendor": {"ts": 1675727180409, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727182166, "value": 2.32397}, "green": {"ts": 1675727183933, "value": 1.0}, "red": {"ts": 1675727187474, "value": 1.54736}}}, "target5": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727189296, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727192836, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727194609, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727198081, "value": true}, "buffer_enabled": {"ts": 1675727201651, "value": false}, "buffer_max_size": {"ts": 1675727203430, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675727206958, "value": false}, "camera_matrix": {"cx": {"ts": 1675727208730, "value": 0.0}, "cy": {"ts": 1675727212199, "value": 0.0}, "fx": {"ts": 1675727215715, "value": 0.0}, "fy": {"ts": 1675727219336, "value": 0.0}}, "deepweed_enabled": {"ts": 1675727224630, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730295283, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675727226393, "value": 0.0}, "k2": {"ts": 1675727229848, "value": 0.0}, "k3": {"ts": 1675727233321, "value": 0.0}, "k4": {"ts": 1675727236845, "value": 0.0}, "k5": {"ts": 1675727240302, "value": 0.0}, "k6": {"ts": 1675727243657, "value": 0.0}, "p1": {"ts": 1675727247230, "value": 0.0}, "p2": {"ts": 1675727250788, "value": 0.0}, "s1": {"ts": 1675727254147, "value": 0.0}, "s2": {"ts": 1675727257592, "value": 0.0}, "s3": {"ts": 1675727261047, "value": 0.0}, "s4": {"ts": 1675727264457, "value": 0.0}, "tau1": {"ts": 1675727267911, "value": 0.0}, "tau2": {"ts": 1675727271300, "value": 0.0}}, "enabled": {"ts": 1675727276588, "value": true}, "exposure_us": {"ts": 1675727278358, "value": 420.0}, "flip": {"ts": 1675727281861, "value": false}, "focus_metric_enabled": {"ts": 1675727285381, "value": false}, "furrows_enabled": {"ts": 1675727288974, "value": false}, "gain_db": {"ts": 1675727290766, "value": 0.0}, "gamma": {"ts": 1675727294377, "value": 0.0}, "gpu_id": {"ts": 1675727297924, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675727303245, "value": false}, "model": {"ts": 1675727304986, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675727308656, "value": true}, "ppi": {"ts": 1675727310413, "value": 182.0}, "ptp": {"ts": 1675727313998, "value": true}, "publisher_reduction_ratio": {"ts": 1675727315716, "value": 10}, "random_image_scoring_enabled": {"ts": 1675727319182, "value": false}, "roi": {"height": {"ts": 1675795299169, "value": 100.0}, "offset_x": {"ts": 1675727322773, "value": 328.0}, "offset_y": {"ts": 1675727324528, "value": 332.0}, "width": {"ts": 1675795300849, "value": 100.0}}, "serial_number": {"ts": 1675727327946, "value": "214500785"}, "sim_fps": {"ts": 1675727329674, "value": 1.0}, "strobing": {"ts": 1675727334885, "value": true}, "transpose": {"ts": 1675727338422, "value": true}, "vendor": {"ts": 1675727340198, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727341960, "value": 2.32397}, "green": {"ts": 1675727343670, "value": 1.0}, "red": {"ts": 1675727347113, "value": 1.54736}}}, "target6": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727348887, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727352470, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727354201, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727357670, "value": true}, "buffer_enabled": {"ts": 1675727361172, "value": false}, "buffer_max_size": {"ts": 1675727362888, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675727366399, "value": false}, "camera_matrix": {"cx": {"ts": 1675727368128, "value": 0.0}, "cy": {"ts": 1675727371558, "value": 0.0}, "fx": {"ts": 1675727375094, "value": 0.0}, "fy": {"ts": 1675727378482, "value": 0.0}}, "deepweed_enabled": {"ts": 1675727383690, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730300879, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675727385510, "value": 0.0}, "k2": {"ts": 1675727388931, "value": 0.0}, "k3": {"ts": 1675727392278, "value": 0.0}, "k4": {"ts": 1675727395917, "value": 0.0}, "k5": {"ts": 1675727399388, "value": 0.0}, "k6": {"ts": 1675727402980, "value": 0.0}, "p1": {"ts": 1675727406473, "value": 0.0}, "p2": {"ts": 1675727409954, "value": 0.0}, "s1": {"ts": 1675727413521, "value": 0.0}, "s2": {"ts": 1675727416879, "value": 0.0}, "s3": {"ts": 1675727420438, "value": 0.0}, "s4": {"ts": 1675727424121, "value": 0.0}, "tau1": {"ts": 1675727427902, "value": 0.0}, "tau2": {"ts": 1675727431483, "value": 0.0}}, "enabled": {"ts": 1675727436739, "value": true}, "exposure_us": {"ts": 1675727438554, "value": 420.0}, "flip": {"ts": 1675727442094, "value": false}, "focus_metric_enabled": {"ts": 1675727445629, "value": false}, "furrows_enabled": {"ts": 1675727449211, "value": false}, "gain_db": {"ts": 1675727450978, "value": 0.0}, "gamma": {"ts": 1675727454542, "value": 0.0}, "gpu_id": {"ts": 1675727458027, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675727463369, "value": false}, "model": {"ts": 1675727465020, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675727468545, "value": true}, "ppi": {"ts": 1675727470294, "value": 182.0}, "ptp": {"ts": 1675727473771, "value": true}, "publisher_reduction_ratio": {"ts": 1675727475498, "value": 10}, "random_image_scoring_enabled": {"ts": 1675727478988, "value": false}, "roi": {"height": {"ts": 1675795302634, "value": 100.0}, "offset_x": {"ts": 1675727482469, "value": 328.0}, "offset_y": {"ts": 1675727484232, "value": 332.0}, "width": {"ts": 1675795304271, "value": 100.0}}, "serial_number": {"ts": 1675727488075, "value": "214500785"}, "sim_fps": {"ts": 1675727489869, "value": 1.0}, "strobing": {"ts": 1675727495129, "value": true}, "transpose": {"ts": 1675727498589, "value": true}, "vendor": {"ts": 1675727500354, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727502161, "value": 2.32397}, "green": {"ts": 1675727503885, "value": 1.0}, "red": {"ts": 1675727507395, "value": 1.54736}}}, "target7": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727509152, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727512808, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727514524, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727518008, "value": true}, "buffer_enabled": {"ts": 1675727521536, "value": false}, "buffer_max_size": {"ts": 1675727523305, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675727526749, "value": false}, "camera_matrix": {"cx": {"ts": 1675727528513, "value": 0.0}, "cy": {"ts": 1675727531914, "value": 0.0}, "fx": {"ts": 1675727535490, "value": 0.0}, "fy": {"ts": 1675727538986, "value": 0.0}}, "deepweed_enabled": {"ts": 1675727544277, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730306437, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675727546137, "value": 0.0}, "k2": {"ts": 1675727549656, "value": 0.0}, "k3": {"ts": 1675727553244, "value": 0.0}, "k4": {"ts": 1675727556647, "value": 0.0}, "k5": {"ts": 1675727560118, "value": 0.0}, "k6": {"ts": 1675727563532, "value": 0.0}, "p1": {"ts": 1675727567163, "value": 0.0}, "p2": {"ts": 1675727570876, "value": 0.0}, "s1": {"ts": 1675727574419, "value": 0.0}, "s2": {"ts": 1675727577895, "value": 0.0}, "s3": {"ts": 1675727581351, "value": 0.0}, "s4": {"ts": 1675727585026, "value": 0.0}, "tau1": {"ts": 1675727588588, "value": 0.0}, "tau2": {"ts": 1675727592036, "value": 0.0}}, "enabled": {"ts": 1675727597244, "value": true}, "exposure_us": {"ts": 1675727598873, "value": 420.0}, "flip": {"ts": 1675727602411, "value": false}, "focus_metric_enabled": {"ts": 1675727606110, "value": false}, "furrows_enabled": {"ts": 1675727609660, "value": false}, "gain_db": {"ts": 1675727611384, "value": 0.0}, "gamma": {"ts": 1675727614759, "value": 0.0}, "gpu_id": {"ts": 1675727618209, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675727623633, "value": false}, "model": {"ts": 1675727625460, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675727628973, "value": true}, "ppi": {"ts": 1675727630766, "value": 182.0}, "ptp": {"ts": 1675727634396, "value": true}, "publisher_reduction_ratio": {"ts": 1675727636368, "value": 10}, "random_image_scoring_enabled": {"ts": 1675727639971, "value": false}, "roi": {"height": {"ts": 1675795305947, "value": 100.0}, "offset_x": {"ts": 1675727643533, "value": 328.0}, "offset_y": {"ts": 1675727645300, "value": 332.0}, "width": {"ts": 1675795307711, "value": 100.0}}, "serial_number": {"ts": 1675727648953, "value": "214500785"}, "sim_fps": {"ts": 1675727650786, "value": 1.0}, "strobing": {"ts": 1675727655921, "value": true}, "transpose": {"ts": 1675727659500, "value": true}, "vendor": {"ts": 1675727661200, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727662931, "value": 2.32397}, "green": {"ts": 1675727664699, "value": 1.0}, "red": {"ts": 1675727668239, "value": 1.54736}}}, "target8": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727669924, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727673436, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727675275, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727678820, "value": true}, "buffer_enabled": {"ts": 1675727682296, "value": false}, "buffer_max_size": {"ts": 1675727683951, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675727687487, "value": false}, "camera_matrix": {"cx": {"ts": 1675727689309, "value": 0.0}, "cy": {"ts": 1675727692813, "value": 0.0}, "fx": {"ts": 1675727696562, "value": 0.0}, "fy": {"ts": 1675727700105, "value": 0.0}}, "deepweed_enabled": {"ts": 1675727705233, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730312003, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675727707034, "value": 0.0}, "k2": {"ts": 1675727710630, "value": 0.0}, "k3": {"ts": 1675727714011, "value": 0.0}, "k4": {"ts": 1675727717535, "value": 0.0}, "k5": {"ts": 1675727721130, "value": 0.0}, "k6": {"ts": 1675727724623, "value": 0.0}, "p1": {"ts": 1675727728110, "value": 0.0}, "p2": {"ts": 1675727731596, "value": 0.0}, "s1": {"ts": 1675727735184, "value": 0.0}, "s2": {"ts": 1675727738528, "value": 0.0}, "s3": {"ts": 1675727742094, "value": 0.0}, "s4": {"ts": 1675727745565, "value": 0.0}, "tau1": {"ts": 1675727749127, "value": 0.0}, "tau2": {"ts": 1675727752691, "value": 0.0}}, "enabled": {"ts": 1675727757892, "value": true}, "exposure_us": {"ts": 1675727759629, "value": 420.0}, "flip": {"ts": 1675727763136, "value": false}, "focus_metric_enabled": {"ts": 1675727766591, "value": false}, "furrows_enabled": {"ts": 1675727770024, "value": false}, "gain_db": {"ts": 1675727771748, "value": 0.0}, "gamma": {"ts": 1675727775236, "value": 0.0}, "gpu_id": {"ts": 1675727778837, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675727784181, "value": false}, "model": {"ts": 1675727785900, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675727789413, "value": true}, "ppi": {"ts": 1675727791188, "value": 182.0}, "ptp": {"ts": 1675727794685, "value": true}, "publisher_reduction_ratio": {"ts": 1675727796489, "value": 10}, "random_image_scoring_enabled": {"ts": 1675727800063, "value": false}, "roi": {"height": {"ts": 1675795309448, "value": 100.0}, "offset_x": {"ts": 1675727803474, "value": 328.0}, "offset_y": {"ts": 1675727805253, "value": 332.0}, "width": {"ts": 1675795311185, "value": 100.0}}, "serial_number": {"ts": 1675727808638, "value": "214500785"}, "sim_fps": {"ts": 1675727810271, "value": 1.0}, "strobing": {"ts": 1675727815547, "value": true}, "transpose": {"ts": 1675727818953, "value": true}, "vendor": {"ts": 1675727820741, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727822486, "value": 2.32397}, "green": {"ts": 1675727824312, "value": 1.0}, "red": {"ts": 1675727827824, "value": 1.54736}}}, "target9": {"auto_brightness": {"exposure_delta_us": {"ts": 1675727829631, "value": 5.0}, "max_brightness": {"ts": 0, "value": 0.85}, "max_exposure_us": {"ts": 1675727832994, "value": 500.0}, "min_brightness": {"ts": 0, "value": 0.8}, "min_exposure_us": {"ts": 1675727834734, "value": 80.0}, "quantile": {"ts": 0, "value": 0.8}}, "auto_brightness_enabled": {"ts": 1675727838369, "value": true}, "buffer_enabled": {"ts": 1675727841868, "value": false}, "buffer_max_size": {"ts": 1675727843604, "value": 30}, "burst_record_predict_save_enabled": {"ts": 1675727847168, "value": false}, "camera_matrix": {"cx": {"ts": 1675727849023, "value": 0.0}, "cy": {"ts": 1675727852571, "value": 0.0}, "fx": {"ts": 1675727856049, "value": 0.0}, "fy": {"ts": 1675727859679, "value": 0.0}}, "deepweed_enabled": {"ts": 1675727865076, "value": false}, "distance_buffer_interval_inches": {"ts": 0, "value": 2.0}, "distance_buffer_max_size": {"ts": 1675730317419, "value": 2}, "distortion_coefficients": {"k1": {"ts": 1675727866882, "value": 0.0}, "k2": {"ts": 1675727870365, "value": 0.0}, "k3": {"ts": 1675727873892, "value": 0.0}, "k4": {"ts": 1675727877446, "value": 0.0}, "k5": {"ts": 1675727880912, "value": 0.0}, "k6": {"ts": 1675727884366, "value": 0.0}, "p1": {"ts": 1675727887908, "value": 0.0}, "p2": {"ts": 1675727891439, "value": 0.0}, "s1": {"ts": 1675727894938, "value": 0.0}, "s2": {"ts": 1675727898533, "value": 0.0}, "s3": {"ts": 1675727902065, "value": 0.0}, "s4": {"ts": 1675727905524, "value": 0.0}, "tau1": {"ts": 1675727909215, "value": 0.0}, "tau2": {"ts": 1675727912624, "value": 0.0}}, "enabled": {"ts": 1675727917786, "value": true}, "exposure_us": {"ts": 1675727919551, "value": 420.0}, "flip": {"ts": 1675727923105, "value": false}, "focus_metric_enabled": {"ts": 1675727926518, "value": false}, "furrows_enabled": {"ts": 1675727929939, "value": false}, "gain_db": {"ts": 1675727931725, "value": 0.0}, "gamma": {"ts": 1675727935247, "value": 0.0}, "gpu_id": {"ts": 1675727938756, "value": 0}, "ip_address": {"ts": 0, "value": ""}, "light_source_preset": {"ts": 0, "value": ""}, "mirror": {"ts": 1675727944110, "value": false}, "model": {"ts": 1675727945862, "value": "TRI028S-C"}, "p2p_enabled": {"ts": 1675727949551, "value": true}, "ppi": {"ts": 1675727951336, "value": 182.0}, "ptp": {"ts": 1675727955076, "value": true}, "publisher_reduction_ratio": {"ts": 1675727956945, "value": 10}, "random_image_scoring_enabled": {"ts": 1675727960551, "value": false}, "roi": {"height": {"ts": 1675795312939, "value": 100.0}, "offset_x": {"ts": 1675727964129, "value": 328.0}, "offset_y": {"ts": 1675727965854, "value": 332.0}, "width": {"ts": 1675795314573, "value": 100.0}}, "serial_number": {"ts": 1675727969460, "value": "214500785"}, "sim_fps": {"ts": 1675727971214, "value": 1.0}, "strobing": {"ts": 1675727976597, "value": true}, "transpose": {"ts": 1675727980121, "value": true}, "vendor": {"ts": 1675727981922, "value": "mock"}, "white_balance": {"blue": {"ts": 1675727983762, "value": 2.32397}, "green": {"ts": 1675727985523, "value": 1.0}, "red": {"ts": 1675727988991, "value": 1.54736}}}, "ts": 1675809852023}}, "host_check": {"disk_space_error_pct": {"ts": 0, "value": 95}, "disk_space_warn_pct": {"ts": 0, "value": 75}, "max_gpu_temp_C": {"ts": 0, "value": 82}, "ptp_alarm_wait_before_alert_time_sec": {"ts": 0, "value": 10}}}, "row2": {"aimbot": {"algorithm": {"ts": 0, "value": "RotaryAndP2P"}, "banding": {"bands": {"__unsync_erased__": null, "ts": 0}, "enabled": {"ts": 0, "value": false}, "offset": {"ts": 0, "value": 0.0}, "unit_multiplier": {"ts": 0, "value": 1.0}}, "debug_trajectory_monitor": {"ts": 0, "value": false}, "dedup_radius_px": {"ts": 0, "value": 100}, "device_overrides": {"extra_params": {"__unsync_erased__": null, "ts": 0}, "extra_params_encoded": {"__unsync_erased__": null, "ts": 0}, "scanner_params": {"__unsync_erased__": null, "ts": 0}, "skip_list": {"__unsync_erased__": null, "ts": 0}, "strobe_control_params": {"exposure_us": {"ts": 0, "value": 800}, "period_us": {"ts": 0, "value": 33333}, "targets_per_predict_ratio": {"ts": 0, "value": 5}}}, "emi_test_pulse_time_ms": {"ts": 0, "value": 100}, "enable_auto_focus": {"ts": 0, "value": false}, "follow_forever_pan_pos": {"ts": 0, "value": 0.5}, "geometric": {"predicts": {"__unsync_erased__": null, "ts": 0}, "scanners": {"__unsync_erased__": null, "ts": 0}}, "go_to_interval_sleep_ms": {"ts": 0, "value": 10}, "height_estimation": {"crop_search_radius": {"ts": 0, "value": 600.0}, "enabled": {"ts": 0, "value": true}, "num_lanes": {"ts": 0, "value": 5}, "only_weeds_for_global": {"ts": 0, "value": true}, "trajectory_smoothing_factor": {"ts": 0, "value": 0.0}, "weed_search_radius": {"ts": 0, "value": 200.0}}, "image_interval_sleep_ms": {"ts": 0, "value": 5}, "laser_test_time_ms": {"ts": 0, "value": 5000}, "load": {"lookback_ms": {"ts": 0, "value": 2000}, "percentile": {"ts": 0, "value": 90}, "target": {"ts": 0, "value": 0.8}}, "log_wheel_encoders": {"ts": 0, "value": false}, "match_perspective_across_predicts": {"ts": 0, "value": false}, "max_number_of_weeds": {"ts": 0, "value": 1000}, "max_p2p_match_fail": {"ts": 0, "value": 6}, "max_pos_y_offset_mm": {"ts": 0, "value": 100.0}, "p2p_capture": {"delay_ms": {"ts": 0, "value": 10}, "enabled": {"ts": 0, "value": false}, "filter": {"__unsync_erased__": null, "ts": 0}, "miss_rate": {"ts": 0, "value": 0.02}, "rate": {"ts": 0, "value": 0.002}}, "positional_smoothing": {"ts": 0, "value": 0.0}, "scanners": {"__unsync_erased__": null, "scanner1": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner10": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner2": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner3": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner4": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner5": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner6": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner7": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner8": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "scanner9": {"enabled": {"ts": 0, "value": true}, "focus": {"ts": 0, "value": 100}, "laser": {"min_delta_temp": {"ts": 0, "value": 0.25}, "min_duty_cycle": {"ts": 0, "value": 0.25}, "power": {"ts": 0, "value": -1.0}, "trust_power": {"ts": 0, "value": true}, "trust_thermistors": {"ts": 0, "value": true}}, "target": {"x": {"ts": 0, "value": 0}, "y": {"ts": 0, "value": 0}}}, "ts": 1669760359593}, "short_follow_time_ms": {"ts": 0, "value": 1000}, "target_burst_capture": {"filter": {"__unsync_erased__": null, "ts": 0}, "rate": {"ts": 0, "value": 0.0}}, "target_burst_capture_filter": {"__unsync_erased__": null, "ts": 0}, "target_safety_zone": {"backward_px": {"ts": 0, "value": 10}, "forward_px": {"ts": 0, "value": 40}, "side_px": {"ts": 0, "value": 10}}, "tilt_mirror_height_mm": {"ts": 0, "value": 850.9}, "tilt_mirror_z_offset_mm": {"ts": 0, "value": 167.64}, "use_low_latency_initial_move": {"ts": 0, "value": false}, "velocity_estimator": {"kill_percent_target": {"ts": 0, "value": 1.0}, "kpt_override_enabled": {"ts": 0, "value": false}}, "weed_accuracy_sampling": {"rate": {"ts": 0, "value": 0.0}, "record_enabled": {"ts": 0, "value": false}}, "weed_tracking": {"crop_safety_radius_mm": {"ts": 0, "value": 1.0}, "distance_bucket_size": {"ts": 0, "value": 400.0}, "error_bucket_size": {"ts": 0, "value": 25.0}, "error_calculation": {"crop_weight": {"ts": 0, "value": 0.0}}, "max_age_in_bounds": {"ts": 0, "value": 1000}, "max_age_out_of_bounds": {"ts": 0, "value": 20000}}}, "cv": {"cameras": {"__unsync_erased__": null, "ts": 0}}, "host_check": {"disk_space_error_pct": {"ts": 0, "value": 95}, "disk_space_warn_pct": {"ts": 0, "value": 75}, "max_gpu_temp_C": {"ts": 0, "value": 82}, "ptp_alarm_wait_before_alert_time_sec": {"ts": 0, "value": 10}}}, "row3": {"aimbot": {"algorithm": {"ts": 0, "value": "RotaryAndP2P"}, "banding": {"bands": {"__unsync_erased__": null, "ts": 0}, "enabled": {"ts": 0, "value": false}, "offset": {"ts": 0, "value": 0.0}, "unit_multiplier": {"ts": 0, "value": 1.0}}, "debug_trajectory_monitor": {"ts": 0, "value": false}, "dedup_radius_px": {"ts": 0, "value": 100}, "device_overrides": {"extra_params": {"__unsync_erased__": null, "ts": 0}, "extra_params_encoded": {"__unsync_erased__": null, "ts": 0}, "scanner_params": {"__unsync_erased__": null, "ts": 0}, "skip_list": {"__unsync_erased__": null, "ts": 0}, "strobe_control_params": {"exposure_us": {"ts": 0, "value": 800}, "period_us": {"ts": 0, "value": 33333}, "targets_per_predict_ratio": {"ts": 0, "value": 5}}}, "emi_test_pulse_time_ms": {"ts": 0, "value": 100}, "enable_auto_focus": {"ts": 0, "value": false}, "follow_forever_pan_pos": {"ts": 0, "value": 0.5}, "geometric": {"predicts": {"__unsync_erased__": null, "ts": 0}, "scanners": {"__unsync_erased__": null, "ts": 0}}, "go_to_interval_sleep_ms": {"ts": 0, "value": 10}, "height_estimation": {"crop_search_radius": {"ts": 0, "value": 600.0}, "enabled": {"ts": 0, "value": true}, "num_lanes": {"ts": 0, "value": 5}, "only_weeds_for_global": {"ts": 0, "value": true}, "trajectory_smoothing_factor": {"ts": 0, "value": 0.0}, "weed_search_radius": {"ts": 0, "value": 200.0}}, "image_interval_sleep_ms": {"ts": 0, "value": 5}, "laser_test_time_ms": {"ts": 0, "value": 5000}, "load": {"lookback_ms": {"ts": 0, "value": 2000}, "percentile": {"ts": 0, "value": 90}, "target": {"ts": 0, "value": 0.8}}, "log_wheel_encoders": {"ts": 0, "value": false}, "match_perspective_across_predicts": {"ts": 0, "value": false}, "max_number_of_weeds": {"ts": 0, "value": 1000}, "max_p2p_match_fail": {"ts": 0, "value": 6}, "max_pos_y_offset_mm": {"ts": 0, "value": 100.0}, "p2p_capture": {"delay_ms": {"ts": 0, "value": 10}, "enabled": {"ts": 0, "value": false}, "filter": {"__unsync_erased__": null, "ts": 0}, "miss_rate": {"ts": 0, "value": 0.02}, "rate": {"ts": 0, "value": 0.002}}, "positional_smoothing": {"ts": 0, "value": 0.0}, "scanners": {"__unsync_erased__": null, "ts": 0}, "short_follow_time_ms": {"ts": 0, "value": 1000}, "target_burst_capture": {"filter": {"__unsync_erased__": null, "ts": 0}, "rate": {"ts": 0, "value": 0.0}}, "target_burst_capture_filter": {"__unsync_erased__": null, "ts": 0}, "target_safety_zone": {"backward_px": {"ts": 0, "value": 10}, "forward_px": {"ts": 0, "value": 40}, "side_px": {"ts": 0, "value": 10}}, "tilt_mirror_height_mm": {"ts": 0, "value": 850.9}, "tilt_mirror_z_offset_mm": {"ts": 0, "value": 167.64}, "use_low_latency_initial_move": {"ts": 0, "value": false}, "velocity_estimator": {"kill_percent_target": {"ts": 0, "value": 1.0}, "kpt_override_enabled": {"ts": 0, "value": false}}, "weed_accuracy_sampling": {"rate": {"ts": 0, "value": 0.0}, "record_enabled": {"ts": 0, "value": false}}, "weed_tracking": {"crop_safety_radius_mm": {"ts": 0, "value": 1.0}, "distance_bucket_size": {"ts": 0, "value": 400.0}, "error_bucket_size": {"ts": 0, "value": 25.0}, "error_calculation": {"crop_weight": {"ts": 0, "value": 0.0}}, "max_age_in_bounds": {"ts": 0, "value": 1000}, "max_age_out_of_bounds": {"ts": 0, "value": 20000}}}, "cv": {"cameras": {"__unsync_erased__": null, "ts": 0}}, "host_check": {"disk_space_error_pct": {"ts": 0, "value": 95}, "disk_space_warn_pct": {"ts": 0, "value": 75}, "max_gpu_temp_C": {"ts": 0, "value": 82}, "ptp_alarm_wait_before_alert_time_sec": {"ts": 0, "value": 10}}}, "simulator": {"deepweed_wait_time": {"ts": 1675820832690, "value": 100}, "generate_random_predictions": {"ts": 1675995546556, "value": true}, "generated_rows": {"ts": 0, "value": 6}, "generated_weed_probability": {"ts": 0, "value": 0.5}, "replay_diagnostic_name": {"ts": 1675911922316, "value": "test1"}, "replay_diagnostic_row": {"ts": 0, "value": 2}, "safety": {"in_cab_estop": {"ts": 0, "value": false}, "interlock": {"ts": 1675377450606, "value": true}, "laser_key": {"ts": 0, "value": true}, "left_estop": {"ts": 0, "value": false}, "lifted": {"ts": 0, "value": false}, "overall_estop": {"ts": 0, "value": false}, "right_estop": {"ts": 0, "value": false}, "water_protect": {"ts": 0, "value": true}}, "supervisory": {"ac_frequency": {"ts": 0, "value": 600.0}, "ac_voltage_a": {"ts": 0, "value": 2400.0}, "ac_voltage_a_b": {"ts": 0, "value": 2400.0}, "ac_voltage_a_c": {"ts": 0, "value": 2400.0}, "ac_voltage_b": {"ts": 0, "value": 2400.0}, "ac_voltage_b_c": {"ts": 0, "value": 2400.0}, "ac_voltage_c": {"ts": 0, "value": 2400.0}, "air_conditioner_disable": {"ts": 0, "value": false}, "battery_voltage_12v": {"ts": 0, "value": 13000.0}, "chiller_disable": {"ts": 0, "value": false}, "gps_disable": {"ts": 0, "value": false}, "humidity_bypass_disable": {"ts": 0, "value": false}, "humidity_status": {"ts": 0, "value": true}, "lifted_status": {"ts": 0, "value": false}, "main_contactor_status_fb": {"ts": 0, "value": true}, "phase_power_va_3": {"ts": 0, "value": 0.0}, "phase_power_w_3": {"ts": 0, "value": 1000.0}, "power_bad": {"ts": 0, "value": false}, "power_factor": {"ts": 0, "value": 0.0}, "power_good": {"ts": 0, "value": true}, "power_very_bad": {"ts": 0, "value": false}, "row_1_btl_disable": {"ts": 0, "value": false}, "row_1_scanners_disable": {"ts": 0, "value": false}, "row_1_server_disable": {"ts": 0, "value": false}, "row_2_btl_disable": {"ts": 0, "value": false}, "row_2_scanners_disable": {"ts": 0, "value": false}, "row_2_server_disable": {"ts": 0, "value": false}, "row_3_btl_disable": {"ts": 0, "value": false}, "row_3_scanners_disable": {"ts": 0, "value": false}, "row_3_server_disable": {"ts": 0, "value": false}, "server_cabinet_humidity": {"ts": 0, "value": 1000.0}, "server_cabinet_temp": {"ts": 0, "value": 2000.0}, "strobe_disable": {"ts": 0, "value": false}, "temp_bypass_disable": {"ts": 0, "value": false}, "temp_humidity_bypass_disable": {"ts": 0, "value": false}, "temp_humidity_status": {"ts": 0, "value": true}, "temp_status": {"ts": 0, "value": true}, "tractor_power": {"ts": 0, "value": true}, "water_protect_status": {"ts": 0, "value": true}, "wheel_encoder_disable": {"ts": 0, "value": false}}, "velocity_mm_sec": {"ts": 1674768859001, "value": 90.0}, "wheel_diameter_in": {"ts": 0, "value": 32.3917}}}
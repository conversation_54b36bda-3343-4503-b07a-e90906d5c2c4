#!/usr/bin/env bash

set -eo pipefail

### TO DELETE RULES RUN SCRIPT WITH `D` Parameter ( sudo iptables.sh D )

# check current iptables:
# sudo iptables -L -t nat --line-numbers

# delete line number:
# sudo iptables -t nat -D OUTPUT 18

# delete multiple:
# for i in {2..43}; do sudo iptables -t nat -D OUTPUT 2 || true; done

if [ "$EUID" -ne 0 ]
  then echo "Please run with sudo/root"
  exit
fi

MODE=$1
echo "MODE=$MODE"
#IP=$(ip -4 addr show | grep -oP '(?<=inet\s)10.1(\.\d+){2}')
IP=*********
echo "Dev Server IP: ${IP}"

EXISTING_RULES=($(iptables -t nat -L OUTPUT --line-numbers | awk '/DNAT/' | cut -d' ' -f1 | tac | xargs))
if [[ "${EXISTING_RULES[0]}" =~ [0-9]+ ]]; then
  echo "Removing ${#EXISTING_RULES[@]} existing rules";
  for rule in "${EXISTING_RULES[@]}"; do
        iptables -t nat -D OUTPUT "$rule"
  done
 else
  echo "No pre-existing rules found."
fi

if [[ "$MODE" == "D" ]]; then
  echo "you specified delete only, exiting...";
  exit 0;
fi

echo "Adding Rules for..."
# hh board
echo "hh board..."
iptables -t nat -A OUTPUT -p udp -d ********** --dport 4243 -j DNAT --to-destination ${IP}:64559

echo "Done updating ip simulator rules!"

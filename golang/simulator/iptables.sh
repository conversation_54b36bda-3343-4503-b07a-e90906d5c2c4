#!/usr/bin/env bash

set -eo pipefail

### TO DELETE RULES RUN SCRIPT WITH `D` Parameter ( sudo iptables.sh D )

# check current iptables:
# sudo iptables -L -t nat --line-numbers

# delete line number:
# sudo iptables -t nat -D OUTPUT 18

# delete multiple:
# for i in {2..43}; do sudo iptables -t nat -D OUTPUT 2 || true; done

if [ "$EUID" -ne 0 ]
  then echo "Please run with sudo/root"
  exit
fi

MODE=$1
echo "MODE=$MODE"
IP=$(ip -4 addr show | grep -oP '(?<=inet\s)10.9(\.\d+){2}')
echo "Dev Server IP: ${IP}"

EXISTING_RULES=($(iptables -t nat -L OUTPUT --line-numbers | awk '/DNAT/' | cut -d' ' -f1 | tac | xargs))
if [[ "${EXISTING_RULES[0]}" =~ [0-9]+ ]]; then
  echo "Removing ${#EXISTING_RULES[@]} existing rules";
  for rule in "${EXISTING_RULES[@]}"; do
        iptables -t nat -D OUTPUT "$rule"
  done
 else
  echo "No pre-existing rules found."
fi

if [[ "$MODE" == "D" ]]; then
  echo "you specified delete only, exiting...";
  exit 0;
fi

echo "Adding Rules for..."
# aimbot
echo "aimbot..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6942  -j DNAT --to-destination 127.0.0.1:6942
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6942  -j DNAT --to-destination 127.0.0.1:64003
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6942  -j DNAT --to-destination 127.0.0.1:64004
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6942  -j DNAT --to-destination 127.0.0.1:64005

# cv runtime
echo "cv runtime..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 15053  -j DNAT --to-destination 127.0.0.1:15053
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 15053  -j DNAT --to-destination 127.0.0.1:64003
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 15053  -j DNAT --to-destination 127.0.0.1:64004
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 15054  -j DNAT --to-destination 127.0.0.1:15054
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 15053  -j DNAT --to-destination 127.0.0.1:64005

# model_receiver
echo "model receiver..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61004  -j DNAT --to-destination 127.0.0.1:61004
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61004  -j DNAT --to-destination 127.0.0.1:64003
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61004  -j DNAT --to-destination 127.0.0.1:64004
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61004  -j DNAT --to-destination 127.0.0.1:64005

# software_manager
echo "software manager..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61005  -j DNAT --to-destination 127.0.0.1:64002
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61005  -j DNAT --to-destination 127.0.0.1:64003
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61005  -j DNAT --to-destination 127.0.0.1:64004
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 61005  -j DNAT --to-destination 127.0.0.1:64005

# Supervisory board:
echo "supervisory board..."
iptables -t nat -A OUTPUT -p udp -d ********* --dport 8501 -j DNAT --to-destination ${IP}:64545

# Safety board:
echo "safety borad..."
iptables -t nat -A OUTPUT -p tcp -d ********* --dport 502 -j DNAT --to-destination 127.0.0.1:64546
iptables -t nat -A OUTPUT -p tcp -d ********* --dport 9504 -j DNAT --to-destination 127.0.0.1:64546

# GPS board:
# PsocMEthernetConnector can't work with 127.0.0.1, only real ip - getting it from ifconfig eno1
echo "gps board..."
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64547

# nofx board:
echo "nofx board..."
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64548

# scanners:
echo "scanners..."
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64549
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64550
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64551
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64552
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64553
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64554
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64555
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64556
iptables -t nat -A OUTPUT -p udp -d ********* --dport 4243 -j DNAT --to-destination ${IP}:64557
iptables -t nat -A OUTPUT -p udp -d *********0 --dport 4243 -j DNAT --to-destination ${IP}:64558

# redis:
echo "redis..."
iptables -t nat -A OUTPUT -p tcp -d ********* --dport 6379 -j DNAT --to-destination 127.0.0.1:6379

# weed tracking
echo "weed tracking..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 65432  -j DNAT --to-destination 127.0.0.1:65432
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 65432  -j DNAT --to-destination 127.0.0.1:64003
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 65432  -j DNAT --to-destination 127.0.0.1:64004
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 65432  -j DNAT --to-destination 127.0.0.1:64005

# host check
echo "host check..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6943  -j DNAT --to-destination ${IP}:6943
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6943  -j DNAT --to-destination ${IP}:6943
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6943  -j DNAT --to-destination ${IP}:6943
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 6943  -j DNAT --to-destination ${IP}:6943

# hardware_manager
echo "hardware manager..."
iptables -t nat -A OUTPUT -p tcp -d ********* --dport 61006 -j DNAT --to-destination 127.0.0.1:61006

# hh board
echo "hh board..."
iptables -t nat -A OUTPUT -p udp -d ********** --dport 4243 -j DNAT --to-destination ${IP}:64559

# metrics endpoints
echo "metrics endpoints..."
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 62101  -j DNAT --to-destination 127.0.0.1:62101
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 62103  -j DNAT --to-destination 127.0.0.1:62103
iptables -t nat -A OUTPUT -p tcp -d ********** --dport 62108  -j DNAT --to-destination 127.0.0.1:62108

echo "Done updating ip simulator rules!"
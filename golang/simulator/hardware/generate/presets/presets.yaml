type: "node"
children:
  crop_and_field:
    type: "node"
    children:
      crop_style:
        type: "string"
        selected: "custom"
        choices: ["custom", "Grimmway Style Carrots", "Mercer Style Onions", "Triangle Style Two-Drop Lettuce", "C&B Style lettuce", "Superdense Spinach or Arugula"]
        hint: "Affects the row width, number of beds, and placement of the crops"
      planting_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "perfect", "shitty planter"]
        hint: "Affects the standard deviation of bed and crop placement"
      dirt_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "perfect", "Clunky dirt"]
        hint: "Affects the height variation of the beds"
      furrow_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Average", "Deep and wet", "Flat"]
        hint: "Affects the bed height and width of the bed slope"
      germination_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Average", "Bad", "Super Bad"]
        hint: "Affects the germination of crops, perfect means all crop will germinate"
  model_quality:
    type: "node"
    children:
      detection_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Average", "Bad", "Super Bad"]
        hint: "Affects the probability for crops and weeds to show up in a prediction, perfect means they are always detected"
      double_detection_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Average", "Bad", "Super Bad"]
        hint: "Affects the probability for crops and weeds to double detect, perfect means they are never double detected"
      classification_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Average", "Bad", "Super Bad"]
        hint: "Affects the probability for weeds to have variance in their detection classes and two high detection classes, perfect means they are consistently classed and have only one high detection class"
      confidence_distribution_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Bimodal", "Super Bad"]
        hint: "Affects the shape of the weed and crop confidence distributions, perfect is all scores above 95%"
      double_detect_confidence_distribution_quality:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Bimodal", "Super Bad"]
        hint: "Affects the shape of the weed and crop confidence distributions for double detections, perfect is all scores below 5%"
  weed_and_growth:
    type: "node"
    children:
      density:
        type: "string"
        selected: "custom"
        choices: ["custom", "None", "Low", "Medium", "High", "Super High", "Deadbed Left", "Deadbed Right"]
        hint: "Affects weed density"
      placement:
        type: "string"
        selected: "custom"
        choices: ["custom", "Equal", "All On Bed Top", "All In Crop Line", "Most On Bed Top", "Most In Crop Line", "Most In Crop Line Spacing", "Most In Furrow"]
        hint: "Affects how the weeds should be placed with relation to the bedtops and furrows"
      entry_time:
        type: "string"
        selected: "custom"
        choices: ["custom", "Perfect", "Good", "Average", "Bad", "Super Bad", "Second Flush"]
        hint: "Affects the entry time (weed and crop size). Affects weed species uniformly."

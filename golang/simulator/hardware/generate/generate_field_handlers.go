package generate

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path"

	"github.com/sirupsen/logrus"
)

type CropStyle struct {
	RowWidth  float64 `json:"row_width_in"`
	NumBeds   int32   `json:"beds_per_row"`
	BedWidth  float64 `json:"bed_top_width_in"`
	NumCL     int32   `json:"crop_lines_per_bed"`
	SpaceCL   float64 `json:"crop_line_spacing"`
	SpaceC2C  float64 `json:"crop_to_crop_spacing"`
	NumSL     int32   `json:"seed_lines_per_crop_lines"`
	SpaceSL   float64 `json:"seed_line_spacing"`
	NumDrops  int32   `json:"drops"`
	SpaceDrop float64 `json:"drop_spacing"`
}

type PlantingQuality struct {
	BedStdDev         float64 `json:"bed_pos_std_dev"`
	SpaceCLStdDev     float64 `json:"crop_line_spacing_std_dev"`
	SpaceC2CStdDev    float64 `json:"crop_to_crop_spacing_std_dev"`
	SpaceSLStdDev     float64 `json:"seed_line_spacing_std_dev"`
	XYZPlantingStdDev float64 `json:"planting_std_dev"`
	ProbWeedIsCrop    float64 `json:"prob_weed_is_crop"`
}

type DirtQuality struct {
	BedHeightStdDev float64 `json:"bed_top_height_std_dev"`
}

type FurrowQuality struct {
	BedHeight     float64 `json:"bed_top_height_in"`
	BedSlopeWidth float64 `json:"bed_top_slope_width_in"`
}

type GerminationQuality struct {
	ProbGermination float64 `json:"prob_germination"`
}

type ClassificationQuality struct {
	ProbClassChange   float64 `json:"prob_weed_class_changes"`
	ProbDoubleClassed float64 `json:"prob_weed_double_classed"`
}

type DoubleDetectionQuality struct {
	ProbCropDoubleDetect float64 `json:"prob_crop_double_detected"`
	ProbWeedDoubleDetect float64 `json:"prob_weed_double_detected"`
}

type DetectionQuality struct {
	ProbDetectCrop        float64 `json:"crop_detection_average_probablity"`
	ProbDetectCropStdDev  float64 `json:"crop_detection_std_dev"`
	ProbDetectWeed        float64 `json:"weed_detection_average_probablity"`
	ProbDetectWeedStdDev  float64 `json:"weed_detection_std_dev"`
	ProbIntersectDriptape float64 `json:"prob_intersect_driptape"`
}

type ConfidenceDistributionQuality struct {
	CropConfidence        string `json:"crop_confidence_distribution"`
	WeedConfidence        string `json:"weed_confidence_distribution"`
	WeedConfidenceForCrop string `json:"weed_confidence_for_crop_distribution"`
	CropConfidenceForWeed string `json:"crop_confidence_for_weed_distribution"`
}

type DoubleDetectConfidenceDistributionQuality struct {
	CropConfidence string `json:"double_detect_crop_confidence_distribution"`
	WeedConfidence string `json:"double_detect_weed_confidence_distribution"`
}

type WeedDensity struct {
	WeedDensity string `json:"weed_density"`
}

type WeedPlacement struct {
	RatioWeedOnBed float64 `json:"ratio_on_bed_vs_furrow"`
	RatioWeedInCL  float64 `json:"ratio_in_crop_line_vs_space"`
}

type EntryTimeWeed struct {
	RatioSmall       float64 `json:"ratio_small"`
	RatioMedium      float64 `json:"ratio_medium"`
	RatioLarge       float64 `json:"ratio_large"`
	AvgSizeSmall     float64 `json:"average_size_small_in"`
	AvgSizeMedium    float64 `json:"average_size_medium_in"`
	AvgSizeLarge     float64 `json:"average_size_large_in"`
	SizeStdDevSmall  float64 `json:"size_small_std_dev"`
	SizeStdDevMedium float64 `json:"size_medium_std_dev"`
	SizeStdDevLarge  float64 `json:"size_large_std_dev"`
}

type EntryTimeCrop struct {
	CropSize       float64 `json:"crop_size_in"`
	CropSizeStdDev float64 `json:"crop_size_std_dev"`
}

func UnmarshalPreset[T any](fileName, presetValue string, presetStruct *T) {
	content, err := ioutil.ReadFile(fmt.Sprintf("./golang/simulator/hardware/generate/presets/%v", fileName))
	if err != nil {
		logrus.WithError(err).Error("SimPresetManager: Failed to open preset json")
		return
	}
	var presetMap map[string]json.RawMessage
	err = json.Unmarshal(content, &presetMap)
	if err != nil {
		logrus.WithError(err).Error("SimPresetManager: Failed to unmarshal")
		return
	}
	if _, ok := presetMap[presetValue]; !ok {
		logrus.Errorf("No preset named: %v", presetValue)
		return
	}
	err = json.Unmarshal(presetMap[presetValue], presetStruct)
	if err != nil {
		logrus.WithError(err).Error("SimPresetManager: Failed to unmarshal")
		return
	}
}

/////////////////////////////
// Preset Handlers
/////////////////////////////

func SetCropStyle(f *Field, value string) {
	var params CropStyle
	UnmarshalPreset("field/cropStyle.json", value, &params)
	f.updateField(
		func() {
			f.RowWidth = in2mm(params.RowWidth)
			f.NumBeds = int32(params.NumBeds)
			f.BedWidth = in2mm(params.BedWidth)
			f.NumCL = int32(params.NumCL)
			f.SpaceCL = in2mm(params.SpaceCL)
			f.SpaceC2C = in2mm(params.SpaceC2C)
			f.NumSL = int32(params.NumSL)
			f.SpaceSL = in2mm(params.SpaceSL)
			f.NumDrops = int32(params.NumDrops)
			f.SpaceDrop = in2mm(params.SpaceDrop)
		},
	)
}

func SetPlantingQuality(f *Field, value string) {
	var params PlantingQuality
	UnmarshalPreset("field/plantingQuality.json", value, &params)
	f.updateField(
		func() {
			f.BedStdDev = in2mm(params.BedStdDev)
			f.SpaceCLStdDev = in2mm(params.SpaceCLStdDev)
			f.SpaceC2CStdDev = in2mm(params.SpaceC2CStdDev)
			f.SpaceSLStdDev = in2mm(params.SpaceSLStdDev)
			f.XYZPlantingStdDev = in2mm(params.XYZPlantingStdDev)
			f.ProbWeedIsCrop = params.ProbWeedIsCrop
		},
	)
}

func SetDirtQuality(f *Field, value string) {
	var params DirtQuality
	UnmarshalPreset("field/dirtQuality.json", value, &params)
	f.updateField(
		func() {
			f.BedHeightStdDev = in2mm(params.BedHeightStdDev)
		},
	)
}

func SetFurrowQuality(f *Field, value string) {
	var params FurrowQuality
	UnmarshalPreset("field/furrowQuality.json", value, &params)
	f.updateField(
		func() {
			f.BedSlopeWidth = in2mm(params.BedSlopeWidth)
			f.BedHeight = in2mm(params.BedHeight)
		},
	)
}

func SetGerminationQuality(f *Field, value string) {
	var params GerminationQuality
	UnmarshalPreset("field/germinationQuality.json", value, &params)
	f.updateField(
		func() {
			f.ProbGermination = params.ProbGermination
		},
	)
}

func SetClassificationQuality(f *Field, value string) {
	var params ClassificationQuality
	UnmarshalPreset("model/classificationQuality.json", value, &params)
	f.updateField(
		func() {
			f.ProbClassChange = params.ProbClassChange
			f.ProbDoubleClassed = params.ProbDoubleClassed
		},
	)
}

func SetDoubleDetectionQuality(f *Field, value string) {
	var params DoubleDetectionQuality
	UnmarshalPreset("model/doubleDetectionQuality.json", value, &params)
	f.updateField(
		func() {
			f.ProbCropDoubleDetect = params.ProbCropDoubleDetect
			f.ProbWeedDoubleDetect = params.ProbWeedDoubleDetect
		},
	)
}

func SetDetectionQuality(f *Field, value string) {
	var params DetectionQuality
	UnmarshalPreset("model/detectionQuality.json", value, &params)
	f.updateField(
		func() {
			f.ProbDetectCrop = params.ProbDetectCrop
			f.ProbDetectCropStdDev = params.ProbDetectCropStdDev
			f.ProbDetectWeed = params.ProbDetectWeed
			f.ProbDetectWeedStdDev = params.ProbDetectWeedStdDev
			f.ProbIntersectDriptape = params.ProbIntersectDriptape
		},
	)
}

func SetConfidenceDistributionQuality(f *Field, value string) {
	var params ConfidenceDistributionQuality
	UnmarshalPreset("model/confidenceDistributionQuality.json", value, &params)
	f.updateField(
		func() {
			f.CropConfidence = buildDistribution(params.CropConfidence)
			f.WeedConfidence = buildDistribution(params.WeedConfidence)
			f.CropConfidenceForWeed = buildDistribution(params.CropConfidenceForWeed)
			f.WeedConfidenceForCrop = buildDistribution(params.WeedConfidenceForCrop)
		},
	)
}

func SetDoubleDetectConfidenceDistributionQuality(f *Field, value string) {
	var params DoubleDetectConfidenceDistributionQuality
	UnmarshalPreset("model/doubleDetectConfidenceDistributionQuality.json", value, &params)
	f.updateField(
		func() {
			f.FakeCropConfidence = buildDistribution(params.CropConfidence)
			f.FakeWeedConfidence = buildDistribution(params.WeedConfidence)
		},
	)
}

func SetWeedDensity(f *Field, value string) {
	var params WeedDensity
	UnmarshalPreset("weeds/weedDensity.json", value, &params)
	f.updateField(
		func() {
			f.buildWeedDensities(params.WeedDensity)
		},
	)
}

func SetWeedPlacement(f *Field, value string) {
	var params WeedPlacement
	UnmarshalPreset("weeds/weedPlacement.json", value, &params)
	f.updateField(
		func() {
			f.RatioWeedOnBed = params.RatioWeedOnBed
			f.RatioWeedInCL = params.RatioWeedInCL
		},
	)
}

func SetEntryTime(f *Field, value string) {
	var weedParams EntryTimeWeed
	var cropParams EntryTimeCrop
	UnmarshalPreset("weeds/entryTime.json", value, &weedParams)
	UnmarshalPreset("weeds/entryTime.json", value, &cropParams)
	f.updateField(
		func() {
			f.CropSize = in2mm(cropParams.CropSize)
			f.CropSizeStdDev = in2mm(cropParams.CropSizeStdDev)
			for _, w := range WeedClasses {
				if weedData, ok := f.WeedData[w]; ok {
					small := weedData.Small
					small.Ratio = float32(weedParams.RatioSmall)
					small.AvgSize = float32(in2mm(weedParams.AvgSizeSmall))
					small.SizeStdDev = float32(in2mm(weedParams.SizeStdDevSmall))
					medium := weedData.Medium
					medium.Ratio = float32(weedParams.RatioMedium)
					medium.AvgSize = float32(in2mm(weedParams.AvgSizeMedium))
					medium.SizeStdDev = float32(in2mm(weedParams.SizeStdDevMedium))
					large := weedData.Large
					large.Ratio = float32(weedParams.RatioLarge)
					large.AvgSize = float32(in2mm(weedParams.AvgSizeLarge))
					large.SizeStdDev = float32(in2mm(weedParams.SizeStdDevLarge))
				} else {
					logrus.Errorf("WeedClasses map is missing weed: %v, check config", w)
				}
			}
		},
	)
}

var requestMap map[string]func(*Field, string) = map[string]func(*Field, string){
	"crop_and_field/crop_style":                                   SetCropStyle,
	"crop_and_field/planting_quality":                             SetPlantingQuality,
	"crop_and_field/dirt_quality":                                 SetDirtQuality,
	"crop_and_field/furrow_quality":                               SetFurrowQuality,
	"crop_and_field/germination_quality":                          SetGerminationQuality,
	"model_quality/detection_quality":                             SetDetectionQuality,
	"model_quality/double_detection_quality":                      SetDoubleDetectionQuality,
	"model_quality/classification_quality":                        SetClassificationQuality,
	"model_quality/confidence_distribution_quality":               SetConfidenceDistributionQuality,
	"model_quality/double_detect_confidence_distribution_quality": SetDoubleDetectConfidenceDistributionQuality,
	"weed_and_growth/density":                                     SetWeedDensity,
	"weed_and_growth/placement":                                   SetWeedPlacement,
	"weed_and_growth/entry_time":                                  SetEntryTime,
}

/////////////////////////////
// Config / Custom Handlers
/////////////////////////////

func customCropStyle(f *Field) {
	fieldNode := f.configNode.GetNode("field")
	f.RowWidth = in2mm(fieldNode.GetNode("row_width_in").GetFloatValue())
	f.NumBeds = int32(fieldNode.GetNode("beds_per_row").GetIntValue())
	f.BedWidth = in2mm(fieldNode.GetNode("bed_top_width_in").GetFloatValue())
	f.NumCL = int32(fieldNode.GetNode("crop_lines_per_bed").GetIntValue())
	f.SpaceCL = in2mm(fieldNode.GetNode("crop_line_spacing").GetFloatValue())
	f.SpaceC2C = in2mm(fieldNode.GetNode("crop_to_crop_spacing").GetFloatValue())
	f.NumSL = int32(fieldNode.GetNode("seed_lines_per_crop_lines").GetIntValue())
	f.SpaceSL = in2mm(fieldNode.GetNode("seed_line_spacing").GetFloatValue())
	f.NumDrops = int32(fieldNode.GetNode("drops").GetIntValue())
	f.SpaceDrop = in2mm(fieldNode.GetNode("drop_spacing").GetFloatValue())
}

func customPlantingQuality(f *Field) {
	fieldNode := f.configNode.GetNode("field")
	f.BedStdDev = in2mm(fieldNode.GetNode("bed_pos_std_dev").GetFloatValue())
	f.SpaceCLStdDev = in2mm(fieldNode.GetNode("crop_line_spacing_std_dev").GetFloatValue())
	f.SpaceC2CStdDev = in2mm(fieldNode.GetNode("crop_to_crop_spacing_std_dev").GetFloatValue())
	f.SpaceSLStdDev = in2mm(fieldNode.GetNode("seed_line_spacing_std_dev").GetFloatValue())
	f.XYZPlantingStdDev = in2mm(fieldNode.GetNode("planting_std_dev").GetFloatValue())
	f.ProbWeedIsCrop = fieldNode.GetNode("prob_weed_is_crop").GetFloatValue()
}

func customDirtQuality(f *Field) {
	fieldNode := f.configNode.GetNode("field")
	f.BedHeight = in2mm(fieldNode.GetNode("bed_top_height_in").GetFloatValue())
}

func customFurrowQuality(f *Field) {
	fieldNode := f.configNode.GetNode("field")
	f.BedSlopeWidth = in2mm(fieldNode.GetNode("bed_top_slope_width_in").GetFloatValue())
	f.BedHeightStdDev = in2mm(fieldNode.GetNode("bed_top_height_std_dev").GetFloatValue())
}

func customGerminationQuality(f *Field) {
	fieldNode := f.configNode.GetNode("field")
	f.ProbGermination = fieldNode.GetNode("prob_germination").GetFloatValue()
}

func customDetectionQuality(f *Field) {
	modelNode := f.configNode.GetNode("model")
	f.ProbDetectCrop = modelNode.GetNode("crop_detection_average_probablity").GetFloatValue()
	f.ProbDetectCropStdDev = modelNode.GetNode("crop_detection_std_dev").GetFloatValue()
	f.ProbDetectWeed = modelNode.GetNode("weed_detection_average_probablity").GetFloatValue()
	f.ProbDetectWeedStdDev = modelNode.GetNode("weed_detection_std_dev").GetFloatValue()
	f.ProbIntersectDriptape = modelNode.GetNode("intersect_driptape_probability").GetFloatValue()
}

func customDoubleDetectionQuality(f *Field) {
	modelNode := f.configNode.GetNode("model")
	f.ProbCropDoubleDetect = modelNode.GetNode("prob_crop_double_detected").GetFloatValue()
	f.ProbWeedDoubleDetect = modelNode.GetNode("prob_weed_double_detected").GetFloatValue()
}

func customClassificationQuality(f *Field) {
	modelNode := f.configNode.GetNode("model")
	f.ProbClassChange = modelNode.GetNode("prob_weed_class_changes").GetFloatValue()
	f.ProbDoubleClassed = modelNode.GetNode("prob_weed_double_classed").GetFloatValue()
}

func customConfidenceDistributionQuality(f *Field) {
	modelNode := f.configNode.GetNode("model")
	f.CropConfidence = buildDistribution(modelNode.GetNode("crop_confidence_distribution").GetStringValue())
	f.WeedConfidence = buildDistribution(modelNode.GetNode("weed_confidence_distribution").GetStringValue())
	f.CropConfidenceForWeed = buildDistribution(modelNode.GetNode("crop_confidence_for_weed_distribution").GetStringValue())
	f.WeedConfidenceForCrop = buildDistribution(modelNode.GetNode("weed_confidence_for_crop_distribution").GetStringValue())
}

func customDoubleDetectConfidenceDistributionQuality(f *Field) {
	modelNode := f.configNode.GetNode("model")
	f.FakeCropConfidence = buildDistribution(modelNode.GetNode("double_detect_crop_confidence_distribution").GetStringValue())
	f.FakeWeedConfidence = buildDistribution(modelNode.GetNode("double_detect_weed_confidence_distribution").GetStringValue())
}

func customWeedDensity(f *Field) {
	weedNode := f.configNode.GetNode("weeds")
	f.buildWeedDensities(weedNode.GetNode("weed_density").GetStringValue())
}

func customWeedPlacement(f *Field) {
	weedNode := f.configNode.GetNode("weeds")
	f.RatioWeedOnBed = weedNode.GetNode("ratio_on_bed_vs_furrow").GetFloatValue()
	f.RatioWeedInCL = weedNode.GetNode("ratio_in_crop_line_vs_space").GetFloatValue()
}

func customEntryTime(f *Field) {
	fieldNode := f.configNode.GetNode("field")
	f.CropSize = in2mm(fieldNode.GetNode("crop_size_in").GetFloatValue())
	f.CropSizeStdDev = in2mm(fieldNode.GetNode("crop_size_std_dev").GetFloatValue())
	weedNode := f.configNode.GetNode("weeds")
	classesNode := weedNode.GetNode("weed_classes")
	for _, w := range WeedClasses {
		if weedData, ok := f.WeedData[w]; ok {
			classNode := classesNode.GetNode(w)
			small := weedData.Small
			small.Ratio = float32(classNode.GetNode("ratio_small").GetFloatValue())
			small.AvgSize = float32(in2mm(classNode.GetNode("average_size_small_in").GetFloatValue()))
			small.SizeStdDev = float32(in2mm(classNode.GetNode("size_small_std_dev").GetFloatValue()))
			medium := weedData.Medium
			medium.Ratio = float32(classNode.GetNode("ratio_medium").GetFloatValue())
			medium.AvgSize = float32(in2mm(classNode.GetNode("average_size_medium_in").GetFloatValue()))
			medium.SizeStdDev = float32(in2mm(classNode.GetNode("size_medium_std_dev").GetFloatValue()))
			large := weedData.Large
			large.Ratio = float32(classNode.GetNode("ratio_large").GetFloatValue())
			large.AvgSize = float32(in2mm(classNode.GetNode("average_size_large_in").GetFloatValue()))
			large.SizeStdDev = float32(in2mm(classNode.GetNode("size_large_std_dev").GetFloatValue()))
		} else {
			logrus.Errorf("WeedClasses map is missing weed: %v, check config", w)
		}
	}
}

func customWeedRatios(f *Field) {
	weedNode := f.configNode.GetNode("weeds")
	classesNode := weedNode.GetNode("weed_classes")
	for _, w := range WeedClasses {
		if weedData, ok := f.WeedData[w]; ok {
			classNode := classesNode.GetNode(w)
			weedData.Ratio = float32(classNode.GetNode("ratio").GetFloatValue())
		} else {
			logrus.Errorf("WeedClasses map is missing weed: %v, check config", w)
		}
	}
}

var configSetterMap map[string]func(*Field) = map[string]func(*Field){
	"crop_and_field/crop_style":                                   customCropStyle,
	"crop_and_field/planting_quality":                             customPlantingQuality,
	"crop_and_field/dirt_quality":                                 customDirtQuality,
	"crop_and_field/furrow_quality":                               customFurrowQuality,
	"crop_and_field/germination_quality":                          customGerminationQuality,
	"model_quality/detection_quality":                             customDetectionQuality,
	"model_quality/double_detection_quality":                      customDoubleDetectionQuality,
	"model_quality/classification_quality":                        customClassificationQuality,
	"model_quality/confidence_distribution_quality":               customConfidenceDistributionQuality,
	"model_quality/double_detect_confidence_distribution_quality": customDoubleDetectConfidenceDistributionQuality,
	"weed_and_growth/density":                                     customWeedDensity,
	"weed_and_growth/placement":                                   customWeedPlacement,
	"weed_and_growth/entry_time":                                  customEntryTime,
	"weed_and_growth/weed_ratios":                                 customWeedRatios,
}

/////////////////////////////
// Web Server Handlers
/////////////////////////////

func (f *Field) SetVelocity(velocityMPH float64) {
	f.fieldLock.Lock()
	f.velocityMmSec = velocityMPH * mph2mmps
	f.fieldLock.Unlock()
}

func (f *Field) SetPreset(pathArray []string, value string) error {
	preset := path.Join(pathArray...)
	err := f.PresetMap.SetPresetValue(preset, value)
	if err != nil {
		return err
	}
	if value == CustomPreset {
		configHandler, ok := configSetterMap[preset]
		if !ok {
			return fmt.Errorf("No config function handler for: %v", preset)
		}
		f.updateField(
			func() {
				configHandler(f)
			},
		)
	} else {
		handler, ok := requestMap[preset]
		if !ok {
			return fmt.Errorf("No function handler for: %v", preset)
		}
		handler(f, value)
	}
	return nil
}

package generate

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestC2CSpacingWhileRunning(t *testing.T) {
	tests := []struct {
		name     string
		SpaceC2C float64
	}{
		{"1 inch spaces", in2mm(1)},
		{"2 inch spaces", in2mm(2)},
		{"3 inch spaces", in2mm(3)},
		{"5 inch spaces", in2mm(5)},
		{"7 inch spaces", in2mm(7)},
		{"12 inch spaces", in2mm(12)},
		{"17 inch spaces", in2mm(17)},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testField := &Field{
				velocityMmSec:     500,
				rowCenter:         (2454.2 + 2911.4) / 2,
				RowWidth:          in2mm(80.0),
				NumBeds:           1,
				BedStdDev:         0.0,
				BedWidth:          in2mm(30.0),
				BedSlopeWidth:     in2mm(2.5),
				NumCL:             1,
				SpaceCL:           in2mm(10.0),
				SpaceCLStdDev:     0.0,
				SpaceC2C:          test.SpaceC2C,
				SpaceC2CStdDev:    0.0,
				NumSL:             1,
				SpaceSL:           in2mm(1.0),
				SpaceSLStdDev:     0.0,
				NumDrops:          1,
				SpaceDrop:         0.0,
				BedHeight:         in2mm(4.5),
				BedHeightStdDev:   0.0,
				XYZPlantingStdDev: 0.0,
				CropSize:          1,
				CropSizeStdDev:    0.0,
				WeedData: map[string]*WeedData{
					"BROADLEAF": &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"GRASS":     &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"OFFSHOOT":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"PURSLANE":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
				},
				ProbWeedIsCrop:        0.0,
				ProbGermination:       1.0,
				RatioWeedOnBed:        0.0,
				RatioWeedInCL:         0.0,
				ProbCropDoubleDetect:  0.0,
				ProbWeedDoubleDetect:  0.0,
				ProbClassChange:       0.0,
				ProbDoubleClassed:     0.0,
				ProbDetectCrop:        1.0,
				ProbDetectCropStdDev:  0.0,
				ProbDetectWeed:        1.0,
				ProbDetectWeedStdDev:  0.0,
				lastBedPosY:           make([]float64, 1),
				lastBedPosZ:           make([]float64, 1),
				WeedConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				CropConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeWeedConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeCropConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				WeedConfidenceForCrop: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
				CropConfidenceForWeed: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),

				fieldObjects:      make([]*FieldObject, 0),
				chunkQueue:        make([]*FieldChunk, 0),
				TerminateChannel1: make(chan bool, 1),
				TerminateChannel2: make(chan bool, 1),
				StoppedChannel1:   make(chan bool, 1),
				StoppedChannel2:   make(chan bool, 1),
				stopped:           false,
			}
			testField.buildWeedDensities("0.0")

			testField.Start()
			time.Sleep(100 * time.Millisecond)
			objects := testField.GetObjects()
			for i := 0; i < len(objects)-1; i++ {
				assert.InDelta(t, objects[i].y-test.SpaceC2C, objects[i+1].y, 0.00001)
			}
			testField.Stop()
		})
	}
}

func TestGenRowNoWeeds(t *testing.T) {
	tests := []struct {
		name          string
		NumBeds       int32
		BedWidth      float64
		BedSlopeWidth float64
		NumCL         int32
		SpaceCL       float64
		SpaceC2C      float64
		NumSL         int32
		SpaceSL       float64
		expectedLen   int
		expectedErr   error
		expected      []float64
	}{
		{
			name:          "1 bed, 1 CL, 1 SL",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         1,
			SpaceCL:       0,
			NumSL:         1,
			SpaceSL:       0,
			expectedLen:   1,
			expectedErr:   nil,
			expected:      []float64{1016.0},
		},
		{
			name:          "1 bed, 1 CL, 5 SL",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         1,
			SpaceCL:       0,
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   5,
			expectedErr:   nil,
			expected:      []float64{965.2, 990.6, 1016, 1041.4, 1066.8},
		},
		{
			name:          "1 bed, 1 CL, 30 SL, CL greater width than bed top",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         1,
			SpaceCL:       0,
			NumSL:         30,
			SpaceSL:       in2mm(2.0),
			expectedLen:   0,
			expectedErr:   fmt.Errorf("Crop lines exceed usable bed top width"),
			expected:      []float64{},
		},
		{
			name:          "1 bed, 2 CL, 5 SL",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         2,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   10,
			expectedErr:   nil,
			expected:      []float64{1066.8, 1092.2, 1117.6, 1143, 1168.3999999999999, 863.6, 889, 914.4, 939.8, 965.2},
		},
		{
			name:          "1 bed, 3 CL, 5 SL",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         3,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   15,
			expectedErr:   nil,
			expected:      []float64{965.2, 990.6, 1016, 1041.4, 1066.8, 1168.4, 1193.8000000000002, 1219.2, 1244.6000000000001, 1270, 762, 787.4, 812.8, 838.2, 863.6},
		},
		{
			name:          "1 bed, 4 CL, 5 SL",
			BedWidth:      in2mm(60.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         4,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   20,
			expectedErr:   nil,
			expected: []float64{1066.8, 1092.2, 1117.6, 1143, 1168.3999999999999, 863.6, 889, 914.4, 939.8, 965.2, 1270, 1295.4, 1320.8, 1346.2, 1371.6,
				660.4000000000001, 685.8000000000001, 711.2, 736.6000000000001, 762.0000000000001},
		},
		{
			name:          "1 bed, 5 CL, 5 SL",
			BedWidth:      in2mm(60.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       1,
			NumCL:         5,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   25,
			expectedErr:   nil,
			expected: []float64{965.2, 990.6, 1016, 1041.4, 1066.8, 1168.4, 1193.8000000000002, 1219.2,
				1244.6000000000001, 1270, 762, 787.4, 812.8, 838.2, 863.6, 1371.6000000000001, 1397.0000000000002,
				1422.4, 1447.8000000000002, 1473.2, 558.8, 584.1999999999999, 609.5999999999999, 635, 660.4},
		},
		{
			name:          "2 bed, 2 CL, 5 SL",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       2,
			NumCL:         2,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   20,
			expectedErr:   nil,
			expected: []float64{558.8, 584.1999999999999, 609.5999999999999, 635, 660.4, 355.59999999999997, 380.99999999999994, 406.4, 431.79999999999995, 457.19999999999993, 1574.8, 1600.2,
				1625.6, 1651, 1676.3999999999999, 1371.6000000000001, 1397.0000000000002, 1422.4, 1447.8000000000002, 1473.2},
		},
		{
			name:          "3 bed, 1 CL, 5 SL",
			BedWidth:      in2mm(10.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       3,
			NumCL:         1,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   15,
			expectedErr:   nil,
			expected: []float64{287.8666666666666, 313.2666666666666, 338.66666666666663, 364.0666666666666, 389.4666666666666,
				965.1999999999999, 990.5999999999999, 1015.9999999999999, 1041.3999999999999, 1066.8, 1642.533333333333, 1667.9333333333332, 1693.333333333333, 1718.7333333333331, 1744.133333333333},
		},
		{
			name:          "3 bed, exceed row width",
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       3,
			NumCL:         1,
			SpaceCL:       in2mm(4),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			expectedLen:   0,
			expectedErr:   fmt.Errorf("Furrow width is negative, bed tops don't fit in specified row width"),
			expected:      []float64{},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testField := &Field{
				rowCenter:         (2454.2 + 2911.4) / 2,
				RowWidth:          in2mm(80.0),
				NumBeds:           test.NumBeds,
				BedStdDev:         0.0,
				BedWidth:          test.BedWidth,
				BedSlopeWidth:     test.BedSlopeWidth,
				NumCL:             test.NumCL,
				SpaceCL:           test.SpaceCL,
				SpaceCLStdDev:     0.0,
				SpaceC2C:          1,
				SpaceC2CStdDev:    0.0,
				NumSL:             test.NumSL,
				SpaceSL:           test.SpaceSL,
				SpaceSLStdDev:     0.0,
				NumDrops:          1,
				SpaceDrop:         0.0,
				BedHeight:         in2mm(4.5),
				BedHeightStdDev:   0.0,
				XYZPlantingStdDev: 0.0,
				CropSize:          1,
				CropSizeStdDev:    0.0,
				WeedData: map[string]*WeedData{
					"BROADLEAF": &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"GRASS":     &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"OFFSHOOT":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"PURSLANE":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
				},
				ProbWeedIsCrop:        0.0,
				ProbGermination:       1.0,
				RatioWeedOnBed:        0.0,
				RatioWeedInCL:         0.0,
				ProbCropDoubleDetect:  0.0,
				ProbWeedDoubleDetect:  0.0,
				ProbClassChange:       0.0,
				ProbDoubleClassed:     0.0,
				ProbDetectCrop:        1.0,
				ProbDetectCropStdDev:  0.0,
				ProbDetectWeed:        1.0,
				ProbDetectWeedStdDev:  0.0,
				lastBedPosY:           make([]float64, test.NumBeds),
				lastBedPosZ:           make([]float64, test.NumBeds),
				WeedConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				CropConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeWeedConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeCropConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				WeedConfidenceForCrop: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
				CropConfidenceForWeed: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
			}
			testField.buildWeedDensities("0.0")

			objects, err := testField.genRow(0, 0)
			assert.Equal(t, test.expectedLen, len(objects))
			if test.expectedErr != nil {
				assert.ErrorContains(t, err, test.expectedErr.Error())
			} else {
				assert.Nil(t, err)
			}
			var xLoctions []float64
			for _, obj := range objects {
				xLoctions = append(xLoctions, obj.x)
			}
			assert.ElementsMatch(t, test.expected, xLoctions)
		})
	}

}

func TestPickWeedClass(t *testing.T) {
	testField := &Field{
		rowCenter:    (2454.2 + 2911.4) / 2,
		fieldObjects: make([]*FieldObject, 0),
		chunkQueue:   make([]*FieldChunk, 0),
		WeedData:     make(map[string]*WeedData),
	}
	tests := []struct {
		name       string
		weedRatios map[string]float32
		expected   []string
	}{
		{
			"broadleaf only",
			map[string]float32{
				"BROADLEAF": float32(1),
				"GRASS":     float32(0),
				"OFFSHOOT":  float32(0),
				"PURSLANE":  float32(0)},
			[]string{"BROADLEAF"},
		},
		{
			"grass only",
			map[string]float32{
				"BROADLEAF": float32(0),
				"GRASS":     float32(1),
				"OFFSHOOT":  float32(0),
				"PURSLANE":  float32(0)},
			[]string{"GRASS"},
		},
		{
			"offshoot only",
			map[string]float32{
				"BROADLEAF": float32(0),
				"GRASS":     float32(0),
				"OFFSHOOT":  float32(1),
				"PURSLANE":  float32(0)},
			[]string{"OFFSHOOT"},
		},
		{
			"purslane only",
			map[string]float32{
				"BROADLEAF": float32(0),
				"GRASS":     float32(0),
				"OFFSHOOT":  float32(0),
				"PURSLANE":  float32(1)},
			[]string{"PURSLANE"},
		},
		{
			"purslane and offshoot",
			map[string]float32{
				"BROADLEAF": float32(0),
				"GRASS":     float32(0),
				"OFFSHOOT":  float32(0.5),
				"PURSLANE":  float32(0.5)},
			[]string{"OFFSHOOT", "PURSLANE"},
		},
		{
			"not offshoot",
			map[string]float32{
				"BROADLEAF": float32(0.25),
				"GRASS":     float32(0.5),
				"OFFSHOOT":  float32(0),
				"PURSLANE":  float32(0.25)},
			[]string{"BROADLEAF", "GRASS", "PURSLANE"},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			for k, v := range test.weedRatios {
				testField.WeedData[k] = &WeedData{
					Ratio:  v,
					Small:  nil,
					Medium: nil,
					Large:  nil,
				}
			}
			for i := 0; i < 10; i++ {
				got := testField.pickWeedClass()
				assert.Contains(t, test.expected, got)
			}
		})
	}
}

func TestFillDetectionClasses(t *testing.T) {
	tests := []struct {
		name         string
		selectedWeed string
	}{
		{"Pick Broadleaf", "BROADLEAF"},
		{"Pick Grass", "GRASS"},
		{"Pick Offshoot", "OFFSHOOT"},
		{"Pick Purslane", "PURSLANE"},
		{"Pick nothing", ""},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := make(map[string]float32)
			fillDetectionClasses(got, test.selectedWeed)
			for k, v := range got {
				if k == test.selectedWeed {
					assert.GreaterOrEqual(t, v, float32(0.85))
				} else {
					assert.LessOrEqual(t, v, float32(.2))
				}
			}
		})
	}
}
func TestDoubleClass(t *testing.T) {
	tests := []struct {
		name              string
		selectedWeed      string
		ProbDoubleClassed float64
		expected          int
	}{
		{"Pick Broadleaf", "BROADLEAF", 0.0, 1},
		{"Pick Broadleaf, double class", "BROADLEAF", 1.0, 2},
		{"Pick Grass", "GRASS", 0.0, 1},
		{"Pick Grass, double class", "GRASS", 1.0, 2},
		{"Pick Offshoot", "OFFSHOOT", 0.0, 1},
		{"Pick Offshoot, double class", "OFFSHOOT", 1.0, 2},
		{"Pick Purslane", "PURSLANE", 0.0, 1},
		{"Pick Purslane, double class", "PURSLANE", 1.0, 2},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := make(map[string]float32)
			fillDetectionClasses(got, test.selectedWeed)
			doubleClass(got, test.selectedWeed, test.ProbDoubleClassed)
			actual := 0
			for _, v := range got {
				if v >= 0.85 {
					actual++
				}
			}
			assert.Equal(t, test.expected, actual)
		})
	}
}

func TestPlaceWeed(t *testing.T) {
	tests := []struct {
		name                 string
		ProbWeedIsCrop       float64
		ProbCropDoubleDetect float64
		ProbWeedDoubleDetect float64
		expected             []bool
	}{
		{
			"Single Weed",
			0.0, 0.0, 0.0,
			[]bool{true},
		},
		{
			"Weed, double detect as crop",
			0.0, 0.0, 1.0,
			[]bool{true, false},
		},
		{
			"Single Crop",
			1.0, 0.0, 0.0,
			[]bool{false},
		},
		{
			"Crop, double detect as Weed",
			1.0, 1.0, 0.0,
			[]bool{false, true},
		},
		{
			"Weed, double detect as crop, ignore weed double detect",
			0.0, 1.0, 1.0,
			[]bool{true, false},
		},
		{
			"Weed, ignore crop double detect",
			0.0, 1.0, 0.0,
			[]bool{true},
		},
		{
			"Crop, double detect as Weed, ignore weed double detect",
			1.0, 1.0, 1.0,
			[]bool{false, true},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testField := &Field{
				fieldObjects:      make([]*FieldObject, 0),
				rowCenter:         (2454.2 + 2911.4) / 2,
				XYZPlantingStdDev: 0.0,
				CropSize:          24.4,
				CropSizeStdDev:    0.0,
				WeedData: map[string]*WeedData{
					"BROADLEAF": &WeedData{1.0, &WeedSize{1, 10, 0.0}, &WeedSize{0, 0, 0.0}, &WeedSize{0, 0, 0.0}},
					"GRASS":     &WeedData{0.0, &WeedSize{1, 10, 0.0}, &WeedSize{0, 0, 0.0}, &WeedSize{0, 0, 0.0}},
					"OFFSHOOT":  &WeedData{0.0, &WeedSize{1, 10, 0.0}, &WeedSize{0, 0, 0.0}, &WeedSize{0, 0, 0.0}},
					"PURSLANE":  &WeedData{0.0, &WeedSize{1, 10, 0.0}, &WeedSize{0, 0, 0.0}, &WeedSize{0, 0, 0.0}},
				},
				ProbWeedIsCrop:        test.ProbWeedIsCrop,
				ProbGermination:       1.0,
				ProbCropDoubleDetect:  test.ProbCropDoubleDetect,
				ProbWeedDoubleDetect:  test.ProbWeedDoubleDetect,
				ProbClassChange:       0.0,
				ProbDoubleClassed:     0.0,
				ProbDetectCrop:        1,
				ProbDetectWeed:        1,
				WeedConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				CropConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeWeedConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeCropConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				WeedConfidenceForCrop: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
				CropConfidenceForWeed: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
			}
			testField.fieldObjects = testField.placeWeed(0, 0, 0)
			got := testField.GetObjects()
			var isWeeds []bool
			for _, obj := range got {
				isWeeds = append(isWeeds, obj.isWeed)
			}
			assert.Equal(t, test.expected, isWeeds)
		})
	}
}

func TestGetObjectsConcurrentReadWrite(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			"basic test",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testField := &Field{
				velocityMmSec:     100,
				rowCenter:         (2454.2 + 2911.4) / 2,
				RowWidth:          in2mm(80.0),
				NumBeds:           2,
				BedStdDev:         0.0,
				BedWidth:          in2mm(30.0),
				BedSlopeWidth:     in2mm(2.5),
				NumCL:             2,
				SpaceCL:           in2mm(10.0),
				SpaceCLStdDev:     0.0,
				SpaceC2C:          in2mm(3),
				SpaceC2CStdDev:    0.0,
				NumSL:             5,
				SpaceSL:           in2mm(1.0),
				SpaceSLStdDev:     0.0,
				NumDrops:          1,
				SpaceDrop:         0.0,
				BedHeight:         in2mm(4.5),
				BedHeightStdDev:   0.0,
				XYZPlantingStdDev: 0.0,
				CropSize:          15,
				CropSizeStdDev:    0.0,
				WeedData: map[string]*WeedData{
					"BROADLEAF": &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"GRASS":     &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"OFFSHOOT":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"PURSLANE":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
				},
				ProbWeedIsCrop:        0.0,
				ProbGermination:       1.0,
				RatioWeedOnBed:        0.9,
				RatioWeedInCL:         0.8,
				ProbCropDoubleDetect:  0.0,
				ProbWeedDoubleDetect:  0.0,
				ProbClassChange:       0.0,
				ProbDoubleClassed:     0.0,
				ProbDetectCrop:        1.0,
				ProbDetectCropStdDev:  0.0,
				ProbDetectWeed:        1.0,
				ProbDetectWeedStdDev:  0.0,
				lastBedPosY:           make([]float64, 2),
				lastBedPosZ:           make([]float64, 2),
				WeedConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				CropConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeWeedConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeCropConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				WeedConfidenceForCrop: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
				CropConfidenceForWeed: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),

				fieldObjects:      make([]*FieldObject, 0),
				chunkQueue:        make([]*FieldChunk, 0),
				TerminateChannel1: make(chan bool, 1),
				TerminateChannel2: make(chan bool, 1),
				StoppedChannel1:   make(chan bool, 1),
				StoppedChannel2:   make(chan bool, 1),
				stopped:           false,
			}
			testField.buildWeedDensities("20.0")

			testField.Start()
			for i := 0; i < 5; i++ {
				objects := testField.GetObjects()
				for _, obj := range objects {
					for k, v := range obj.detectionClasses {
						assert.NotNil(t, k)
						assert.NotNil(t, v)
						obj.detectionClasses[k] = 0.0
					}
				}
				for _, obj := range objects {
					for k, v := range obj.weedDetectionClasses {
						assert.NotNil(t, k)
						assert.NotNil(t, v)
						obj.weedDetectionClasses[k] = 0.0
					}
				}
				time.Sleep(100 * time.Millisecond)
			}
			testField.Stop()
		})
	}
}

func TestGenRowVaryingWeedDensities(t *testing.T) {
	tests := []struct {
		name          string
		RowWidth      float64
		NumBeds       int32
		BedWidth      float64
		BedSlopeWidth float64
		NumCL         int32
		SpaceCL       float64
		SpaceC2C      float64
		NumSL         int32
		SpaceSL       float64
		weedDensities string
	}{
		{
			name:          "1 weed density: 30",
			RowWidth:      in2mm(80.0),
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       2,
			NumCL:         2,
			SpaceCL:       in2mm(5),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			weedDensities: "30.0",
		},
		{
			name:          "1 weed density: 0",
			RowWidth:      in2mm(80.0),
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       2,
			NumCL:         2,
			SpaceCL:       in2mm(5),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			weedDensities: "0.0",
		},
		{
			name:          "mulitple weed densities: off and on",
			RowWidth:      in2mm(80.0),
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       2,
			NumCL:         2,
			SpaceCL:       in2mm(5),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			weedDensities: "30.0, 0.0, 30.0, 0.0, 30.0, 0.0, 30.0, 0.0",
		},
		{
			name:          "mulitple weed densities: decending",
			RowWidth:      in2mm(80.0),
			BedWidth:      in2mm(30.0),
			BedSlopeWidth: in2mm(2.0),
			NumBeds:       2,
			NumCL:         2,
			SpaceCL:       in2mm(5),
			NumSL:         5,
			SpaceSL:       in2mm(1),
			weedDensities: "40.0, 30.0, 20.0, 10.0, 0.0",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testField := &Field{
				rowCenter:         (2454.2 + 2911.4) / 2,
				RowWidth:          test.RowWidth,
				NumBeds:           test.NumBeds,
				BedStdDev:         0.0,
				BedWidth:          test.BedWidth,
				BedSlopeWidth:     test.BedSlopeWidth,
				NumCL:             test.NumCL,
				SpaceCL:           test.SpaceCL,
				SpaceCLStdDev:     0.0,
				SpaceC2C:          1,
				SpaceC2CStdDev:    0.0,
				NumSL:             test.NumSL,
				SpaceSL:           test.SpaceSL,
				SpaceSLStdDev:     0.0,
				NumDrops:          1,
				SpaceDrop:         0.0,
				BedHeight:         in2mm(4.5),
				BedHeightStdDev:   0.0,
				XYZPlantingStdDev: 0.0,
				CropSize:          1,
				CropSizeStdDev:    0.0,
				WeedData: map[string]*WeedData{
					"BROADLEAF": &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"GRASS":     &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"OFFSHOOT":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"PURSLANE":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
				},
				ProbWeedIsCrop:        0.0,
				ProbGermination:       1.0,
				RatioWeedOnBed:        0.5,
				RatioWeedInCL:         0.5,
				ProbCropDoubleDetect:  0.0,
				ProbWeedDoubleDetect:  0.0,
				ProbClassChange:       0.0,
				ProbDoubleClassed:     0.0,
				ProbDetectCrop:        1.0,
				ProbDetectCropStdDev:  0.0,
				ProbDetectWeed:        1.0,
				ProbDetectWeedStdDev:  0.0,
				lastBedPosY:           make([]float64, test.NumBeds),
				lastBedPosZ:           make([]float64, test.NumBeds),
				WeedConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				CropConfidence:        buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeWeedConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				FakeCropConfidence:    buildDistribution("0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"),
				WeedConfidenceForCrop: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
				CropConfidenceForWeed: buildDistribution("100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"),
			}
			testField.buildWeedDensities(test.weedDensities)
			denArr := testField.WeedDensity

			objects, _ := testField.genRow(0, testField.rowCenter-testField.RowWidth/2)

			for _, obj := range objects {
				if obj.isWeed {
					inBand := false
					for i := 0; i < len(denArr)-1; i++ {
						denStart := denArr[i]
						denEnd := denArr[i+1]
						denIsNonZero := denStart.Val > 0.0
						if denIsNonZero && obj.x >= float64(denStart.X) && obj.x <= float64(denEnd.X) {
							inBand = true
							break
						}
					}
					assert.True(t, inBand)
				}
			}
		})
	}

}

func TestDensitySections(t *testing.T) {
	// "40, 30, 20, 10" =
	// {1666.8 0.0004305566} {2174.8 0.00032291745}
	// {2682.8 0.0002152783} {3190.8 0.00010763915}
	tests := []struct {
		name      string
		densities string
		leftEdge  float64
		rightEdge float64
		expected  []density
	}{
		{
			"noraml case: both left and right in array, subarray",
			"40, 30, 20, 10",
			2454.2,
			2911.4,
			[]density{
				density{2174.8, float32(sqft2sqmm(30))},
				density{2682.8, float32(sqft2sqmm(20))},
			},
		},
		{
			"noraml case: both left and right in array, whole array",
			"40, 30, 20, 10",
			1700,
			3200,
			[]density{
				density{1666.8, float32(sqft2sqmm(40))},
				density{2174.8, float32(sqft2sqmm(30))},
				density{2682.8, float32(sqft2sqmm(20))},
				density{3190.8, float32(sqft2sqmm(10))},
			},
		},
		{
			"edge case 1: left is pre array, right in array",
			"40, 30, 20, 10",
			1600,
			1700,
			[]density{
				density{1600, float32(sqft2sqmm(40))},
				density{1666.8, float32(sqft2sqmm(40))},
			},
		},
		{
			"edge case 2: left is pre array, right is pre array",
			"40, 30, 20, 10",
			1500,
			1600,
			[]density{
				density{1500, float32(sqft2sqmm(40))},
			},
		},
		{
			"edge case 3: left is in array, right is post array",
			"40, 30, 20, 10",
			3200,
			4000,
			[]density{
				density{3190.8, float32(sqft2sqmm(10))},
			},
		},
		{
			"edge case 4: left is post array, right is post array",
			"40, 30, 20, 10",
			3750,
			4000,
			[]density{
				density{3698.8, float32(sqft2sqmm(10))},
			},
		},
		{
			"edge case 5: left is pre array, right is post array",
			"40, 30, 20, 10",
			1600,
			4000,
			[]density{
				density{1600, float32(sqft2sqmm(40))},
				density{1666.8, float32(sqft2sqmm(40))},
				density{2174.8, float32(sqft2sqmm(30))},
				density{2682.8, float32(sqft2sqmm(20))},
				density{3190.8, float32(sqft2sqmm(10))},
			},
		},
		{
			"edge case 6:",
			"10",
			3571.8,
			3698.8,
			[]density{
				density{1666.8, float32(sqft2sqmm(10))},
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			testField := &Field{
				rowCenter: (2454.2 + 2911.4) / 2,
				RowWidth:  in2mm(80),
				WeedData: map[string]*WeedData{
					"BROADLEAF": &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"GRASS":     &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"OFFSHOOT":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
					"PURSLANE":  &WeedData{0.25, &WeedSize{0.75, 10, 0.0}, &WeedSize{0.25, 15, 0.0}, &WeedSize{0, 0, 0.0}},
				},
			}
			testField.buildWeedDensities(test.densities)
			got := testField.getDensitySections(test.leftEdge, test.rightEdge)
			assert.Equal(t, test.expected, got)
		})
	}
}

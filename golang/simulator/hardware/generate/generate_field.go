package generate

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	wr "github.com/mroth/weightedrand"
	"github.com/sirupsen/logrus"
)

const ChunkSize = 254 // 10 inches
const MaxQueueSize = 20
const BufferSpace = 100
const MinY = 1000
const MaxY = 4000
const PredictSpaceStart = 1500
const PredictSpaceEnd = 2500
const mph2mmps = 447.04
const minCropSize = 0.01
const numEmbeddingDimensions = 2

const cache_file string = "./golang/simulator/hardware/generate/states.json"

const CropClass = "CROP"

var WeedClasses = [...]string{"BROADLEAF", "GRASS", "OFFSHOOT", "PURSLANE"}

type density struct {
	X   float32 `json:"x"`
	Val float32 `json:"val"`
}

type WeedSize struct {
	Ratio      float32 `json:"ratio"`
	AvgSize    float32 `json:"avg_size"`
	SizeStdDev float32 `json:"size_std_dev"`
}

type WeedData struct {
	Ratio  float32   `json:"ratio"`
	Small  *WeedSize `json:"small"`
	Medium *WeedSize `json:"medium"`
	Large  *WeedSize `json:"large"`
}

func (wd *WeedData) getWeedSize() float32 {
	totalProb := wd.Small.Ratio + wd.Medium.Ratio + wd.Large.Ratio
	sizeRoll := rand.Float32() * totalProb
	var size float32
	if sizeRoll < wd.Small.Ratio {
		size = wd.Small.AvgSize + float32(rand.NormFloat64())*wd.Small.SizeStdDev
	} else if sizeRoll < wd.Small.Ratio+wd.Medium.Ratio {
		size = wd.Medium.AvgSize + float32(rand.NormFloat64())*wd.Medium.SizeStdDev
	} else {
		size = wd.Large.AvgSize + float32(rand.NormFloat64())*wd.Large.SizeStdDev
	}
	if size < 0 {
		size = minCropSize
	}
	return size
}

// single point of interest in the field, crop or weed
type FieldObject struct {
	x                       float64
	y                       float64
	z                       float64
	size                    float32
	score                   float32
	cropScore               float32
	weedScore               float32
	plantScore              float32
	detectionClasses        map[string]float32
	weedDetectionClasses    map[string]float32
	maskIntersectionClasses []string
	isWeed                  bool
	isReal                  bool
	realType                string
	embedding               []float32
}

func (obj *FieldObject) GetX() float64          { return obj.x }
func (obj *FieldObject) GetY() float64          { return obj.y }
func (obj *FieldObject) GetZ() float64          { return obj.z }
func (obj *FieldObject) GetSize() float32       { return obj.size }
func (obj *FieldObject) GetScore() float32      { return obj.score }
func (obj *FieldObject) GetWeedScore() float32  { return obj.weedScore }
func (obj *FieldObject) GetCropScore() float32  { return obj.cropScore }
func (obj *FieldObject) GetPlantScore() float32 { return obj.plantScore }
func (obj *FieldObject) GetDetectionClasses() map[string]float32 {
	return copyMap(obj.detectionClasses)
}
func (obj *FieldObject) GetWeedDetectionClasses() map[string]float32 {
	return copyMap(obj.weedDetectionClasses)
}
func (obj *FieldObject) GetIsWeed() bool     { return obj.isWeed }
func (obj *FieldObject) GetIsReal() bool     { return obj.isReal }
func (obj *FieldObject) GetRealType() string { return obj.realType }
func (obj *FieldObject) GetEmbedding() []float32 {
	res := make([]float32, len(obj.embedding))
	copy(res, obj.embedding)
	return res
}
func (obj *FieldObject) GetMaskIntersectionClasses() []string {
	res := make([]string, len(obj.maskIntersectionClasses))
	copy(res, obj.maskIntersectionClasses)
	return res
}

type FieldChunk struct {
	objects []*FieldObject
	lock    sync.Mutex
}

type Field struct {
	velocityMmSec float64
	bbh           float64
	genEmbeddings bool

	// field params
	RowWidth          float64 `json:"row_width"`
	NumBeds           int32   `json:"num_beds"`
	BedStdDev         float64 `json:"bed_std_dev"`
	BedWidth          float64 `json:"bed_width"`
	BedSlopeWidth     float64 `json:"bed_slope_width"`
	NumCL             int32   `json:"num_cl"`
	SpaceCL           float64 `json:"space_cl"`
	SpaceCLStdDev     float64 `json:"space_cl_std_dev"`
	SpaceC2C          float64 `json:"space_c2c"`
	SpaceC2CStdDev    float64 `json:"space_c2c_std_dev"`
	NumDrops          int32   `json:"num_drops"`
	SpaceDrop         float64 `json:"space_drops"`
	NumSL             int32   `json:"num_sl"`
	SpaceSL           float64 `json:"space_sl"`
	SpaceSLStdDev     float64 `json:"space_sl_std_dev"`
	BedHeight         float64 `json:"bed_height"`
	BedHeightStdDev   float64 `json:"bed_height_std_dev"`
	XYZPlantingStdDev float64 `json:"xyz_planting_std_dev"`
	CropSize          float64 `json:"crop_size"`
	CropSizeStdDev    float64 `json:"crop_size_std_dev"`
	ProbGermination   float64 `json:"prob_germ"`

	// weed params
	WeedData       map[string]*WeedData `json:"weed_data"`
	WeedDensity    []density            `json:"weed_density"`
	ProbWeedIsCrop float64              `json:"prob_weed_is_crop"`
	RatioWeedOnBed float64              `json:"ratio_weed_on_bed"`
	RatioWeedInCL  float64              `json:"ratio_weed_in_cl"`

	// model params
	ProbCropDoubleDetect  float64     `json:"prob_crop_double_detect"`
	ProbWeedDoubleDetect  float64     `json:"prob_weed_double_detect"`
	ProbClassChange       float64     `json:"prob_class_change"`
	ProbDoubleClassed     float64     `json:"prob_double_class"`
	ProbDetectCrop        float64     `json:"prob_detect_crop"`
	ProbDetectCropStdDev  float64     `json:"prob_detect_crop_std_dev"`
	ProbDetectWeed        float64     `json:"prob_detect_weed"`
	ProbDetectWeedStdDev  float64     `json:"prob_detect_weed_std_dev"`
	ProbIntersectDriptape float64     `json:"prob_intersect_driptape"`
	WeedConfidence        []wr.Choice `json:"weed_confidence"`
	CropConfidence        []wr.Choice `json:"crop_confidence"`
	FakeWeedConfidence    []wr.Choice `json:"fake_weed_confidence"`
	FakeCropConfidence    []wr.Choice `json:"fake_crop_confidence"`
	WeedConfidenceForCrop []wr.Choice `json:"weed_confidence_for_crop"`
	CropConfidenceForWeed []wr.Choice `json:"crop_confidence_for_weed"`

	chunkQueue   []*FieldChunk
	queueLock    sync.Mutex
	fieldObjects []*FieldObject
	objLock      sync.Mutex
	lastBedPosY  []float64
	lastBedPosZ  []float64

	TerminateChannel1 chan bool `json:"-"`
	TerminateChannel2 chan bool `json:"-"`
	StoppedChannel1   chan bool `json:"-"`
	StoppedChannel2   chan bool `json:"-"`
	stopped           bool

	configNode *config.ConfigTree

	// calculated values from field params
	rowCenter      float64
	cropSliceSpace float64

	PresetMap *PresetMap `json:"preset_map"`

	fieldLock sync.Mutex
}

func NewField(configSubscriber *config.ConfigSubscriber) *Field {
	f := &Field{
		rowCenter:         (2454.2 + 2911.4) / 2,
		fieldObjects:      make([]*FieldObject, 0),
		chunkQueue:        make([]*FieldChunk, 0),
		WeedData:          make(map[string]*WeedData),
		TerminateChannel1: make(chan bool, 1),
		TerminateChannel2: make(chan bool, 1),
		StoppedChannel1:   make(chan bool, 1),
		StoppedChannel2:   make(chan bool, 1),
		stopped:           false,
		configNode:        configSubscriber.GetConfigNode("simulator", ""),

		PresetMap: NewPresetMap(),
	}
	f.initWeedData()
	f.configNode.RegisterCallback(f.readConfig)

	if err := f.LoadField(); err != nil {
		logrus.Errorf("SimFieldGen: Error loading field from cache: %v", err)
		f.readConfig() // no need to read default values if able to load from cache
	}

	return f
}

func (f *Field) Start() {
	logrus.Infof("SimFieldGen: Started generating random field")
	rand.Seed(time.Now().UnixNano())
	f.GenerateField(f.TerminateChannel1, f.StoppedChannel1)
	f.AnimateField(f.TerminateChannel2, f.StoppedChannel2)
}

func (f *Field) Stop() {
	logrus.Infof("SimFieldGen: Terminating")
	if !f.stopped {
		f.TerminateChannel1 <- true
		f.TerminateChannel2 <- true
		<-f.StoppedChannel1
		<-f.StoppedChannel2
	}
	logrus.Infof("SimFieldGen: Done terminating")
}

func (f *Field) StopOnErr(err error) {
	logrus.WithError(err).Error("SimFieldGen: Error generating field with config parameters:")
	logrus.Infof("SimFieldGen: Terminating animate")
	f.TerminateChannel2 <- true
	f.objLock.Lock()
	f.fieldObjects = make([]*FieldObject, 0)
	f.objLock.Unlock()
	f.queueLock.Lock()
	f.chunkQueue = make([]*FieldChunk, 0)
	f.queueLock.Unlock()
	f.stopped = <-f.StoppedChannel2
	logrus.Infof("SimFieldGen: Done terminating animate")
}

func (f *Field) waitForTerminateSignal(sleepTimeMs uint64, channel chan bool) bool {
	select {
	case <-channel:
		logrus.Infof("SimFieldGen: Received request to terminate replay")
		return true
	case <-time.After(time.Duration(sleepTimeMs) * time.Millisecond):
		return false
	}
}

func (f *Field) initWeedData() {
	weedNode := f.configNode.GetNode("weeds")
	classesNode := weedNode.GetNode("weed_classes")
	for _, w := range WeedClasses {
		if hasNode := classesNode.HasNode(w); !hasNode {
			logrus.Error(fmt.Sprintf("weed_classes list in config is missing node: %v", w))
			continue
		}
		f.WeedData[w] = &WeedData{
			Ratio:  0,
			Small:  &WeedSize{},
			Medium: &WeedSize{},
			Large:  &WeedSize{},
		}
	}
}

func (f *Field) updateField(syncFunc func()) {
	f.fieldLock.Lock()

	syncFunc()
	f.CacheField()

	f.lastBedPosY = make([]float64, f.NumBeds)
	f.lastBedPosZ = make([]float64, f.NumBeds)
	f.fieldLock.Unlock()
	f.objLock.Lock()
	f.fieldObjects = make([]*FieldObject, 0)
	f.objLock.Unlock()
	f.queueLock.Lock()
	f.chunkQueue = make([]*FieldChunk, 0)
	f.queueLock.Unlock()
	if f.stopped {
		f.stopped = false
		logrus.Infof("SimFieldGen: Restarting random field generation")
		f.GenerateField(f.TerminateChannel1, f.StoppedChannel1)
		f.AnimateField(f.TerminateChannel2, f.StoppedChannel2)
	}
}

func (f *Field) readConfig() {
	logrus.Infof("SimFieldGen: Recieved request to update parameters")
	f.updateField(
		func() {
			f.velocityMmSec = f.configNode.GetNode("velocity_mph").GetFloatValue() * mph2mmps
			f.bbh = in2mm(f.configNode.GetNode("bed_to_btl_height_in").GetFloatValue())
			f.genEmbeddings = f.configNode.GetNode("model").GetNode("generate_embeddings").GetBoolValue()
			f.PresetMap.lock.Lock()
			defer f.PresetMap.lock.Unlock()
			for k, function := range configSetterMap {
				presetData, ok := f.PresetMap.Presets[k]
				if !ok || presetData.Value == CustomPreset {
					function(f)
				} else {
					logrus.Warnf("Unable to set from config, %v is not in custom mode", k)
				}
			}
		},
	)
}

func (f *Field) CacheField() {
	logrus.Infof("SimFieldGen: Saving State")
	jsonBytes, err := json.MarshalIndent(f, "", "   ")
	if err != nil {
		logrus.Errorf("SimFieldGen: Error saving: %v", err)
		return
	}
	err = os.WriteFile(cache_file, jsonBytes, os.ModePerm)
	if err != nil {
		logrus.Errorf("SimFieldGen: Error saving: %v", err)
	}
}

func (f *Field) LoadField() error {
	if _, err := os.Stat(cache_file); err != nil {
		return err
	}
	logrus.Infof("SimFieldGen: loading previous state from %v", cache_file)
	jsonData, err := os.ReadFile(cache_file)
	if err != nil {
		return err
	}
	f.updateField(
		func() {
			f.velocityMmSec = f.configNode.GetNode("velocity_mph").GetFloatValue() * mph2mmps
			f.bbh = in2mm(f.configNode.GetNode("bed_to_btl_height_in").GetFloatValue())
			f.genEmbeddings = f.configNode.GetNode("model").GetNode("generate_embeddings").GetBoolValue()
			err = json.Unmarshal(jsonData, f)
		},
	)
	return err
}

func (f *Field) GetPredictObjects() []*FieldObject {
	f.objLock.Lock()
	defer f.objLock.Unlock()
	var objects []*FieldObject
	for _, obj := range f.fieldObjects {
		// only returning stuff in predict space
		if obj.y > PredictSpaceStart && obj.y < PredictSpaceEnd {
			detectionClasses := make(map[string]float32)
			weedDetectionClasses := make(map[string]float32)
			embedding := make([]float32, len(obj.embedding))
			var prob float64
			if obj.isWeed {
				prob = f.ProbDetectWeed + rand.NormFloat64()*f.ProbDetectWeedStdDev
			} else {
				prob = f.ProbDetectCrop + rand.NormFloat64()*f.ProbDetectCropStdDev
			}
			isDetected := rand.Float64() < prob
			if isDetected {
				detectionClasses = f.skewDetectionClasses(copyMap(obj.detectionClasses))
				weedDetectionClasses = f.skewDetectionClasses(copyMap(obj.weedDetectionClasses))
				copy(embedding, obj.embedding)
			}
			driptapeIntersection := rand.Float64() < f.ProbIntersectDriptape
			maskIntersectionClasses := []string{}
			if driptapeIntersection {
				maskIntersectionClasses = append(maskIntersectionClasses, "DRIPTAPE")
			}

			z := f.bbh + (f.BedHeight - obj.z)
			objects = append(objects, &FieldObject{
				x:                       obj.x,
				y:                       obj.y,
				z:                       z,
				size:                    obj.size,
				score:                   obj.score,
				detectionClasses:        detectionClasses,
				weedDetectionClasses:    weedDetectionClasses,
				maskIntersectionClasses: maskIntersectionClasses,
				isWeed:                  obj.isWeed,
				isReal:                  obj.isReal,
				weedScore:               obj.weedScore,
				cropScore:               obj.cropScore,
				plantScore:              obj.plantScore,
				realType:                obj.realType,
				embedding:               embedding,
			})
			if isDetected {
				if obj.isWeed {
					if rand.Float32() < float32(f.ProbWeedDoubleDetect) {
						objects = append(objects, f.makeCrop(obj.x, obj.y, z, false))
					}
				} else {
					if rand.Float32() < float32(f.ProbCropDoubleDetect) {
						objects = append(objects, f.makeWeed(obj.x, obj.y, z, false))
					}
				}
			}
		}
	}
	return objects
}

// returns all objects in the generated space
func (f *Field) GetObjects() []*FieldObject {
	f.objLock.Lock()
	defer f.objLock.Unlock()
	var objects []*FieldObject
	for _, obj := range f.fieldObjects {
		detectionClasses := make(map[string]float32)
		weedDetectionClasses := make(map[string]float32)
		var prob float64
		if obj.isWeed {
			prob = f.ProbDetectWeed + rand.NormFloat64()*f.ProbDetectWeedStdDev
		} else {
			prob = f.ProbDetectCrop + rand.NormFloat64()*f.ProbDetectCropStdDev
		}
		isDetected := rand.Float64() < prob
		if isDetected {
			detectionClasses = f.skewDetectionClasses(copyMap(obj.detectionClasses))
			weedDetectionClasses = f.skewDetectionClasses(copyMap(obj.weedDetectionClasses))
		}
		z := f.bbh + (f.BedHeight - obj.z)
		objects = append(objects, &FieldObject{
			x:                    obj.x,
			y:                    obj.y,
			z:                    z,
			size:                 obj.size,
			score:                obj.score,
			detectionClasses:     detectionClasses,
			weedDetectionClasses: weedDetectionClasses,
			isWeed:               obj.isWeed,
			isReal:               obj.isReal,
			weedScore:            obj.weedScore,
			cropScore:            obj.cropScore,
			plantScore:           obj.plantScore,
			realType:             obj.realType,
		})
		if isDetected {
			if obj.isWeed {
				if rand.Float32() < float32(f.ProbWeedDoubleDetect) {
					objects = append(objects, f.makeCrop(obj.x, obj.y, z, false))
				}
			} else {
				if rand.Float32() < float32(f.ProbCropDoubleDetect) {
					objects = append(objects, f.makeWeed(obj.x, obj.y, z, false))
				}
			}
		}

	}
	return objects
}

func (f *Field) AnimateField(termChannel chan bool, stoppedChan chan bool) {
	go func() {
		distSincePop := 0.0
		sleepTime := 25
		for {
			timeStartNano := time.Now().UnixNano()
			if f.waitForTerminateSignal(uint64(sleepTime), termChannel) {
				break
			}
			timeEndNano := time.Now().UnixNano()
			deltaTimeNano := timeEndNano - timeStartNano
			f.objLock.Lock()
			f.queueLock.Lock()
			queueLen := len(f.chunkQueue)
			objIsEmpty := len(f.fieldObjects) == 0
			if queueLen != 0 && (objIsEmpty || distSincePop >= ChunkSize) {
				distSincePop -= ChunkSize
				newChunk := f.chunkQueue[0].objects
				f.chunkQueue = f.chunkQueue[1:]
				f.queueLock.Unlock()
				if distSincePop > 0 {
					// need to adjust the new chunk to match the total movement
					for _, obj := range newChunk {
						obj.y += distSincePop
					}
				}
				f.fieldObjects = append(f.fieldObjects, newChunk...)
				distSincePop = 0
			} else {
				f.queueLock.Unlock()
			}

			deltaY := float64(deltaTimeNano) * float64(f.velocityMmSec) / 1_000_000_000

			for i := 0; i < len(f.fieldObjects); i++ {
				obj := f.fieldObjects[i]
				obj.y += deltaY
				if obj.y > float64(MaxY) {
					//remove elements as they pass detection area
					f.fieldObjects = append(f.fieldObjects[:i], f.fieldObjects[i+1:]...)
					i--
					continue
				}
			}
			if len(f.fieldObjects) != 0 {
				distSincePop += deltaY
			}
			f.objLock.Unlock()
		}
		stoppedChan <- true
	}()
}

func (f *Field) GenerateField(termChannel chan bool, stoppedChan chan bool) {
	go func() {
		sleepTime := 10
		leftOver := 0.0
		for {
			if f.waitForTerminateSignal(uint64(sleepTime), termChannel) {
				break
			}
			f.queueLock.Lock()
			queueLen := len(f.chunkQueue)
			f.queueLock.Unlock()
			if queueLen > MaxQueueSize {
				continue
			}
			newChunk := FieldChunk{
				objects: make([]*FieldObject, 0),
			}
			leadingEdge := float64(MinY) - float64(BufferSpace)
			y := leftOver
			f.fieldLock.Lock()
			simLeftEdge := f.rowCenter - f.RowWidth/2
			f.cropSliceSpace = f.SpaceC2C + float64(f.NumDrops-1)*f.SpaceDrop
			for ; y < ChunkSize; y += f.cropSliceSpace + rand.NormFloat64()*f.SpaceC2CStdDev {
				objectsRow, err := f.genRow(leadingEdge-y, simLeftEdge)
				if err != nil {
					f.fieldLock.Unlock()
					f.StopOnErr(err)
					return
				}
				newChunk.objects = append(newChunk.objects, objectsRow...)
			}
			f.fieldLock.Unlock()
			leftOver = y - ChunkSize
			f.queueLock.Lock()
			f.chunkQueue = append(f.chunkQueue, &newChunk)
			f.queueLock.Unlock()
		}
		stoppedChan <- true
	}()
}

func (f *Field) genRow(y float64, rowLeftEdge float64) ([]*FieldObject, error) {
	fw := (f.RowWidth - float64(f.NumBeds)*f.BedWidth) / float64(f.NumBeds)
	if fw < 0 {
		err := fmt.Errorf("Furrow width is negative, bed tops don't fit in specified row width")
		return nil, err
	}

	var objectsRow []*FieldObject

	trueBedCenter := rowLeftEdge + fw/2 + f.BedWidth/2

	// place in beds
	for i := int32(0); i < f.NumBeds; i++ {
		if f.lastBedPosY[i] == 0 {
			f.lastBedPosY[i] = trueBedCenter
		}
		f.lastBedPosY[i] = applyPosStdDev(trueBedCenter, f.lastBedPosY[i], f.BedStdDev, f.cropSliceSpace)
		if f.lastBedPosZ[i] == 0 {
			f.lastBedPosZ[i] = f.BedHeight
		}
		f.lastBedPosZ[i] = applyPosStdDev(f.BedHeight, f.lastBedPosZ[i], f.BedHeightStdDev, f.cropSliceSpace)
		objectsBed, err := f.genBed(y, f.lastBedPosY[i], f.lastBedPosZ[i])
		if err != nil {
			return nil, err
		}
		objectsRow = append(objectsRow, objectsBed...)

		trueBedCenter += fw + f.BedWidth
	}

	clusterBottom := y - f.cropSliceSpace/2

	// fill furrows with weeds
	probInFurrow := 2 * (1 - f.RatioWeedOnBed)
	var furrowStart, furrowEnd float64
	for i := int32(0); i < f.NumBeds; i++ {
		if i == 0 {
			// weeds for the left most furrow
			furrowEnd = f.lastBedPosY[i] - f.BedWidth/2
			furrowStart = furrowEnd - fw/2
			width := furrowEnd - furrowStart
			objectsRow = f.makeWeedCluster(furrowStart, clusterBottom, width, f.cropSliceSpace, 0, objectsRow, probInFurrow)
		}

		if i < f.NumBeds-1 {
			// weeds in furrows between beds
			furrowStart = f.lastBedPosY[i] + f.BedWidth/2
			furrowEnd = f.lastBedPosY[i+1] - f.BedWidth/2
			width := furrowEnd - furrowStart
			if width > 0 {
				objectsRow = f.makeWeedCluster(furrowStart, clusterBottom, width, f.cropSliceSpace, 0, objectsRow, probInFurrow)
			} else {
				logrus.Warn(fmt.Sprintf("SimFieldGen: Decrease bed positional std dev, beds are contacting"))
			}
		}

		if i == f.NumBeds-1 {
			// weeds on right most furrow
			furrowStart = f.lastBedPosY[i] + f.BedWidth/2
			furrowEnd = furrowStart + fw/2
			width := furrowEnd - furrowStart
			objectsRow = f.makeWeedCluster(furrowStart, clusterBottom, width, f.cropSliceSpace, 0, objectsRow, probInFurrow)
		}
	}

	return objectsRow, nil
}

func (f *Field) genBed(y, bedCenter, bedHeight float64) ([]*FieldObject, error) {
	wcl := float64(f.NumSL-1) * f.SpaceSL
	scl := f.SpaceCL
	usableEdge := bedCenter + f.BedWidth/2 - f.BedSlopeWidth
	probOutsideCL := 4 * f.RatioWeedOnBed * (1 - f.RatioWeedInCL)
	clusterBottom := y - f.cropSliceSpace/2
	var objectsBed []*FieldObject
	var clRight, clLeft float64
	if f.NumCL%2 == 0 {
		clLeft = bedCenter + scl/2
		clRight = bedCenter - wcl - scl/2
	} else {
		clLeft = bedCenter - wcl/2
		clRight = bedCenter - wcl/2
	}
	iters := (f.NumCL + 1) / 2
	for i := int32(0); i < iters; i++ {
		leftOverBed := usableEdge - clLeft - wcl
		if leftOverBed < 0 {
			err := fmt.Errorf("Crop lines exceed usable bed top width, over by: %v ", math.Abs(leftOverBed))
			return nil, err
		}
		clEdgeLeft := clLeft + rand.NormFloat64()*f.SpaceCLStdDev
		clEdgeRight := clRight + rand.NormFloat64()*f.SpaceCLStdDev
		if clLeft == clRight {
			objectsBed = append(objectsBed, f.genCL(y, bedHeight, clEdgeLeft)...)
		} else {
			objectsBed = append(objectsBed, f.genCL(y, bedHeight, clEdgeLeft)...)
			objectsBed = append(objectsBed, f.genCL(y, bedHeight, clEdgeRight)...)
			if i == 0 {
				objectsBed = f.makeWeedCluster(clEdgeLeft-scl, clusterBottom, scl, f.cropSliceSpace, bedHeight, objectsBed, probOutsideCL)
			} else {
				objectsBed = f.makeWeedCluster(clEdgeLeft-scl, clusterBottom, scl, f.cropSliceSpace, bedHeight, objectsBed, probOutsideCL)
				objectsBed = f.makeWeedCluster(clEdgeRight+wcl, clusterBottom, scl, f.cropSliceSpace, bedHeight, objectsBed, probOutsideCL)
			}
		}
		if i == iters-1 {
			// on the last iteration, place weeds the from the last crop line to the edge of the bed
			objectsBed = f.makeWeedCluster(clEdgeLeft+wcl, clusterBottom, leftOverBed, f.cropSliceSpace, bedHeight, objectsBed, probOutsideCL)
			objectsBed = f.makeWeedCluster(clEdgeRight-leftOverBed, clusterBottom, leftOverBed, f.cropSliceSpace, bedHeight, objectsBed, probOutsideCL)
		}
		clLeft += wcl + scl
		clRight -= wcl + scl
	}
	// place weeds on the slope of the bed
	objectsBed = append(objectsBed, f.makeWeedsOnSlope(bedCenter, bedHeight, y, probOutsideCL, 1)...)
	objectsBed = append(objectsBed, f.makeWeedsOnSlope(bedCenter, bedHeight, y, probOutsideCL, -1)...)
	return objectsBed, nil
}

func (f *Field) genCL(y, z, CLLeftEdge float64) []*FieldObject {
	wcl := float64(f.NumSL-1) * f.SpaceSL
	var objectsCL []*FieldObject
	clusterBottom := y - f.cropSliceSpace/2
	dropY := y
	for drop := int32(0); drop < f.NumDrops; drop++ {
		for i := int32(0); i < f.NumSL; i++ {
			x := CLLeftEdge + float64(i)*f.SpaceSL + rand.NormFloat64()*f.SpaceSLStdDev
			xLoc := x + rand.NormFloat64()*f.XYZPlantingStdDev
			yLoc := dropY + rand.NormFloat64()*f.XYZPlantingStdDev
			zLoc := z + rand.NormFloat64()*f.XYZPlantingStdDev
			objectsCL = append(objectsCL, f.placeCrop(xLoc, yLoc, zLoc)...)
		}
		dropY -= f.SpaceDrop
	}
	objectsCL = f.makeWeedCluster(CLLeftEdge, clusterBottom, wcl, f.cropSliceSpace, z, objectsCL, 4*f.RatioWeedOnBed*f.RatioWeedInCL)
	return objectsCL
}

func (f *Field) makeWeedCluster(leftEdge, bottomEdge, width, height, z float64, existingObjects []*FieldObject, placementProb float64) []*FieldObject {
	if width < 0 {
		panic(fmt.Sprintf("makeWeedCluster: width was negative: %v", width))
	}
	rightEdge := leftEdge + width
	densities := f.getDensitySections(leftEdge, rightEdge)
	for i, den := range densities {
		start := float64(den.X)
		if i == 0 {
			start = leftEdge
		}
		end := rightEdge
		if i < len(densities)-1 {
			end = float64(densities[i+1].X)
		}
		subWidth := end - start
		existingObjects = f.makeWeedClusterWithDensity(start, bottomEdge, subWidth, height, z, existingObjects, placementProb, float64(den.Val))
	}

	return existingObjects
}

// places weeds in a square area avoiding the objects already in the area
func (f *Field) makeWeedClusterWithDensity(leftEdge, bottomEdge, width, height, z float64, existingObjects []*FieldObject, placementProb, density float64) []*FieldObject {
	var objectsCluster []*FieldObject
	objectsCluster = append(objectsCluster, existingObjects...)
	area := width * height
	goalNumWeeds := int(area * density)
	if goalNumWeeds == 0 && density > 0 {
		if rand.Float64() < (area * density) {
			goalNumWeeds = 1
		}
	}
	numWeedsPlaced := 0
	weedsSkipped := 0
	maxAttempts := 100
	for attempt := 0; attempt < maxAttempts && numWeedsPlaced < (goalNumWeeds-weedsSkipped); attempt++ {
		place := rand.Float32() < float32(placementProb)
		if place {
			var xLoc, yLoc, zLoc float64
			xLoc = leftEdge + rand.Float64()*width
			yLoc = bottomEdge + rand.Float64()*height
			zLoc = z
			freeSpot := true
			for _, obj := range objectsCluster {
				if pointInRadius(float64(obj.size), obj.x, obj.y, xLoc, yLoc) {
					freeSpot = false
					break
				}
			}
			if freeSpot {
				objectsCluster = append(objectsCluster, f.placeWeed(xLoc, yLoc, zLoc)...)
				numWeedsPlaced++
				maxAttempts += 100
			}
		} else {
			weedsSkipped++
		}
	}
	return objectsCluster
}

// side: 1 is left slope (ascending), -1 is right slope (descending)
func (f *Field) makeWeedsOnSlope(bedCenter, bedHeight, y, placementProb, side float64) []*FieldObject {
	var objectsSlope []*FieldObject
	angle := math.Atan2(bedHeight, f.BedSlopeWidth)
	bedEdge := bedCenter - side*f.BedWidth/2
	leftEdge := bedEdge
	rightEdge := bedEdge + f.BedSlopeWidth
	if side == -1 {
		leftEdge = bedEdge - f.BedSlopeWidth
		rightEdge = bedEdge
	}
	densities := f.getDensitySections(leftEdge, rightEdge)
	if len(densities) == 0 {
		logrus.Warn("SimFieldGen: No weed densities for slope")
		return objectsSlope
	}
	area := (f.BedSlopeWidth) * float64(f.cropSliceSpace)
	density := float64(densities[0].Val)
	goalNumWeeds := int(area * density)
	if goalNumWeeds == 0 && density > 0 {
		if rand.Float64() < (area * density) {
			goalNumWeeds = 1
		}
	}
	for i := 0; i < goalNumWeeds; i++ {
		place := rand.Float32() < float32(placementProb)
		if place {
			var xLoc, yLoc, zLoc float64
			xLoc = bedEdge + side*rand.Float64()*(f.BedSlopeWidth)
			yLoc = y + rand.Float64()*(f.cropSliceSpace)
			zLoc = (side * (xLoc - bedEdge)) * math.Tan(angle)
			objectsSlope = append(objectsSlope, f.placeWeed(xLoc, yLoc, zLoc)...)
		}
	}
	return objectsSlope
}

func (f *Field) placeCrop(x, y, z float64) []*FieldObject {
	var objects []*FieldObject
	isGerm := rand.Float32() < float32(f.ProbGermination)
	if isGerm {
		objects = append(objects, f.makeCrop(x, y, z, true))
	}
	return objects
}

func (f *Field) makeCrop(x, y, z float64, isReal bool) *FieldObject {
	detectionClasses := make(map[string]float32)
	detectionClasses[CropClass] = float32(0.85 + rand.Float64()*.15)
	var score float32
	var plantConfidence float32
	if isReal {
		score = sampleDistribution(f.CropConfidence)
		plantConfidence = sampleDistribution(f.CropConfidence)
	} else {
		score = sampleDistribution(f.FakeCropConfidence)
		plantConfidence = sampleDistribution(f.FakeCropConfidence)
	}
	weedConfidence := sampleDistribution((f.WeedConfidenceForCrop))
	embedding := f.generateEmbedding()
	weedDetectionClasses := make(map[string]float32)
	selectedWeed := f.pickWeedClass()
	fillDetectionClasses(weedDetectionClasses, selectedWeed)
	size := float32(f.CropSize + rand.NormFloat64()*f.CropSizeStdDev)
	if size < 0 {
		size = minCropSize
	}
	obj := FieldObject{
		x:                    x,
		y:                    y,
		z:                    z,
		size:                 size,
		isWeed:               false,
		detectionClasses:     detectionClasses,
		weedDetectionClasses: weedDetectionClasses,
		score:                score,
		isReal:               isReal,
		cropScore:            score,
		weedScore:            weedConfidence,
		plantScore:           plantConfidence,
		realType:             "CROP",
		embedding:            embedding,
	}
	return &obj
}

func (f *Field) placeWeed(x, y, z float64) []*FieldObject {
	var objects []*FieldObject
	isCrop := rand.Float32() < float32(f.ProbWeedIsCrop)
	if isCrop {
		objects = append(objects, f.placeCrop(x, y, z)...)
	} else {
		objects = append(objects, f.makeWeed(x, y, z, true))
	}
	return objects
}

func (f *Field) makeWeed(x, y, z float64, isReal bool) *FieldObject {
	detectionClasses := make(map[string]float32)
	selectedWeed := f.pickWeedClass()
	fillDetectionClasses(detectionClasses, selectedWeed)
	doubleClass(detectionClasses, selectedWeed, f.ProbDoubleClassed)
	weedDetectionClasses := make(map[string]float32)
	fillDetectionClasses(weedDetectionClasses, selectedWeed)
	var score float32
	var plantConfidence float32
	if isReal {
		score = sampleDistribution(f.WeedConfidence)
		plantConfidence = sampleDistribution(f.WeedConfidence)
	} else {
		score = sampleDistribution(f.FakeWeedConfidence)
		plantConfidence = sampleDistribution(f.FakeWeedConfidence)
	}
	cropConfidence := sampleDistribution(f.CropConfidenceForWeed)
	embedding := f.generateEmbedding()
	obj := FieldObject{
		x:                    x,
		y:                    y,
		z:                    z,
		size:                 f.WeedData[selectedWeed].getWeedSize(),
		isWeed:               true,
		detectionClasses:     detectionClasses,
		weedDetectionClasses: detectionClasses,
		score:                score,
		isReal:               isReal,
		weedScore:            score,
		cropScore:            cropConfidence,
		plantScore:           plantConfidence,
		realType:             selectedWeed,
		embedding:            embedding,
	}
	return &obj
}

func (f *Field) pickWeedClass() string {
	var totalProb float32
	for _, w := range WeedClasses {
		totalProb += f.WeedData[w].Ratio
	}
	weedRoll := rand.Float32() * totalProb

	var runningTotal float32
	for _, w := range WeedClasses {
		runningTotal += f.WeedData[w].Ratio
		if weedRoll < runningTotal {
			return w
		}
	}
	return ""
}

func (f *Field) skewDetectionClasses(detectionClasses map[string]float32) map[string]float32 {
	if rand.Float32() < float32(f.ProbClassChange) {
		for k, v := range detectionClasses {
			newPCT := float64(v) + rand.NormFloat64()*(0.05)
			newPCT = math.Max(math.Min(newPCT, 1.0), 0.0)
			detectionClasses[k] = float32(newPCT)
		}
	}
	return detectionClasses
}

func (f *Field) buildWeedDensities(densities string) {
	list := strings.Split(densities, ",")
	numDen := len(list)
	arr := make([]density, numDen)
	stepSize := f.RowWidth / float64(numDen)
	x := f.rowCenter - (f.RowWidth / 2)
	for i, str := range list {
		val, _ := strconv.ParseFloat(strings.TrimSpace(str), 32)
		arr[i] = density{float32(x), float32(math.Abs(sqft2sqmm(val)))}
		x += stepSize
	}
	f.WeedDensity = arr
}

func (f *Field) getDensitySections(leftEdge, rightEdge float64) []density {
	if rightEdge < leftEdge {
		panic("getDensitySections: left is less than right")
	}

	sections := make([]density, 0)
	numDen := len(f.WeedDensity)
	stepSize := f.RowWidth / float64(numDen)
	rowLeftEdge := f.rowCenter - f.RowWidth/2
	rowRightEdge := f.rowCenter + f.RowWidth/2

	startIndex := int((leftEdge - rowLeftEdge) / stepSize)
	if leftEdge < rowLeftEdge {
		startIndex = 0
		sections = append(sections, density{float32(leftEdge), f.WeedDensity[0].Val})
	} else if leftEdge > rowRightEdge {
		startIndex = numDen
		sections = append(sections, density{float32(rowRightEdge), f.WeedDensity[numDen-1].Val})
	}

	endIndex := int((rightEdge-rowLeftEdge)/stepSize) + 1
	if rightEdge < rowLeftEdge {
		endIndex = 0
	} else if rightEdge > rowRightEdge || endIndex > numDen {
		endIndex = numDen
	}

	sub := f.WeedDensity[startIndex:endIndex]
	sections = append(sections, sub...)

	return sections
}

// todo: have this sample a settable distribution
func (f *Field) generateEmbedding() []float32 {
	res := make([]float32, 0)
	if f.genEmbeddings {
		for i := 0; i < numEmbeddingDimensions; i++ {
			res = append(res, rand.Float32())
		}
	}
	return res
}

/*
 *  H E L P E R   F U N C T I O N S
 */

func in2mm(inch float64) float64 { return (inch * 25.4) }

func sqft2sqmm(ft float64) float64 { return ft / 92903 }

func applyPosStdDev(expected, last, StdDev, stepSizeY float64) float64 {
	max := expected + 3*StdDev
	min := expected - 3*StdDev
	// max step in the x direction (left to right) is based on the amount of deviation
	// and the size of the step to the next set of seeds (step in y direction)
	// this helps smooth the normal function outputs
	maxStep := stepSizeY * math.Tan(math.Atan2(StdDev, 254))
	step := rand.NormFloat64() * StdDev
	step = math.Min(step, maxStep)
	step = math.Max(step, -maxStep)
	augmentedCenter := last + step
	augmentedCenter = math.Min(augmentedCenter, max)
	augmentedCenter = math.Max(augmentedCenter, min)
	return augmentedCenter
}

func fillDetectionClasses(detectionClasses map[string]float32, selectedWeed string) {
	for _, w := range WeedClasses {
		if w == selectedWeed {
			detectionClasses[w] = float32(0.85 + rand.Float64()*.15)
		} else {
			detectionClasses[w] = float32(rand.Float64() * .2)
		}
	}
}

func doubleClass(detectionClasses map[string]float32, selectedWeed string, probDoubleClassed float64) {
	doubleClass := rand.Float32() < float32(probDoubleClassed)
	if doubleClass {
		otherWeedClasses := make([]string, 0)
		for _, w := range WeedClasses {
			if w != selectedWeed {
				otherWeedClasses = append(otherWeedClasses, w)
			}
		}
		randWeedClass := otherWeedClasses[rand.Intn(len(otherWeedClasses))]
		detectionClasses[randWeedClass] = float32(0.85 + rand.Float64()*.15)
	}
}

// center point: (x1, y1), poi: (x2, y2)
func pointInRadius(radius, x1, y1, x2, y2 float64) bool {
	x := x2 - x1
	y := y2 - y1
	dist := math.Sqrt((x * x) + (y * y))
	return dist <= radius
}

// given a list of 20 weights as a string, builds an array of weighted probabilities
// 0.0 to 0.95, every 0.05
func buildDistribution(densities string) []wr.Choice {
	list := strings.Split(densities, ",")
	if len(list) != 20 {
		logrus.Warn(fmt.Sprintf("Weed or crop confidence distribution has %v elements, should be 20", len(list)))
	}
	confidenceDist := make([]wr.Choice, len(list))
	for i, str := range list {
		prob := float32(float64(i) * 0.05)
		rawWeight, _ := strconv.ParseFloat(strings.TrimSpace(str), 64)
		weight := uint(100.0 * rawWeight)
		choice := wr.NewChoice(prob, weight)
		confidenceDist[i] = choice
	}
	return confidenceDist
}

func sampleDistribution(confidenceDist []wr.Choice) float32 {
	cp := make([]wr.Choice, len(confidenceDist))
	copy(cp, confidenceDist)
	chooser, _ := wr.NewChooser(cp...)
	var pct float32
	if v, ok := chooser.Pick().(float32); ok {
		pct = v
	} else if v, ok := chooser.Pick().(float64); ok {
		pct = float32(v)
	} else {
		logrus.Error("non float in chooser")
	}
	return pct + rand.Float32()*0.05
}

func copyMap[K, V comparable](m map[K]V) map[K]V {
	result := make(map[K]V)
	for k, v := range m {
		result[k] = v
	}
	return result
}

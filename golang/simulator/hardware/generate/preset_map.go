package generate

import (
	"fmt"
	"gopkg.in/yaml.v3"
	"io/ioutil"
	"path"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/sim_UI"
	"github.com/sirupsen/logrus"
)

const CustomPreset = "custom"
const PresetFilePath = "./golang/simulator/hardware/generate/presets/presets.yaml"

func contains[T comparable](s []T, e T) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

type PresetNode struct {
	Value         string                 `yaml:"selected"`
	AllowedValues []string               `yaml:"choices"`
	ChildNodes    map[string]*PresetNode `yaml:"children"`
}

type PresetTree struct {
	Root *PresetNode
	lock sync.Mutex
}

var globalPresets *PresetTree = NewPresetTreeFromSchema()

func NewPresetTreeFromSchema() *PresetTree {
	yfile, err1 := ioutil.ReadFile(PresetFilePath)
	if err1 != nil {
		logrus.WithError(err1).Error("failed to open/read")
	}
	root := PresetNode{}
	err2 := yaml.Unmarshal(yfile, &root)
	if err2 != nil {
		logrus.WithError(err2).Error("failed to unmarshal")
	}
	return &PresetTree{Root: &root}
}

func (root *PresetNode) toMap(res map[string]*PresetMapValue, pathArray []string) {
	if len(root.ChildNodes) == 0 {
		res[path.Join(pathArray...)] = &PresetMapValue{
			Value:         root.Value,
			AllowedValues: root.AllowedValues,
			Path:          pathArray,
		}
	} else {
		for k, v := range root.ChildNodes {
			p := append(pathArray, k)
			v.toMap(res, p)
		}
	}
}

func (tree *PresetTree) ToMap() map[string]*PresetMapValue {
	tree.lock.Lock()
	defer tree.lock.Unlock()
	res := make(map[string]*PresetMapValue)
	tree.Root.toMap(res, []string{})
	return res
}

type PresetMapValue struct {
	Value         string   `json:"value"`
	AllowedValues []string `json:"allowed_values"`
	Path          []string `json:"path"`
}

type PresetMap struct {
	Presets map[string]*PresetMapValue `json:"presets"`
	lock    sync.Mutex
}

func NewPresetMap() *PresetMap {
	presetMap := &PresetMap{
		Presets: globalPresets.ToMap(),
	}
	return presetMap
}

func (pm *PresetMap) SetPresetValue(key, value string) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	presetData, ok := pm.Presets[key]
	if !ok {
		return fmt.Errorf("No valid preset named %v", key)
	}
	if !contains(presetData.AllowedValues, value) {
		return fmt.Errorf("No valid preset value named %v for %v", value, key)
	}
	presetData.Value = value
	return nil
}

func (pm *PresetMap) Equal(other *PresetMap) bool {
	pm.lock.Lock()
	other.lock.Lock()
	defer pm.lock.Unlock()
	defer other.lock.Unlock()

	if len(pm.Presets) != len(other.Presets) {
		return false
	}
	for k, data := range pm.Presets {
		otherData, ok := other.Presets[k]
		if !ok || data.Value != otherData.Value {
			return false
		}
	}
	return true
}

// syncs shared keys only
func (pm *PresetMap) Sync(other *PresetMap) {
	pm.lock.Lock()
	other.lock.Lock()
	defer pm.lock.Unlock()
	defer other.lock.Unlock()
	for k, otherData := range other.Presets {
		if data, ok := pm.Presets[k]; ok {
			data.Value = otherData.Value
		}
	}
}

func (pm *PresetMap) ToMessage() []*sim_UI.PresetSetting {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	res := make([]*sim_UI.PresetSetting, 0)
	for _, data := range pm.Presets {
		res = append(res, &sim_UI.PresetSetting{
			Path:  data.Path,
			Value: data.Value,
		})
	}
	return res
}

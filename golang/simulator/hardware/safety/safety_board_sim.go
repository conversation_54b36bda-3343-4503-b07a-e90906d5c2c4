package safety

import (
	"io"
	"net"
	"sync"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/sirupsen/logrus"
)

type SafetyBoardSim struct {
	InCabEStop         bool
	LeftEStop          bool
	RightEStop         bool
	Lifted             bool
	LaserKey           bool
	Interlock          bool
	WaterProtect       bool
	OverallEStop       bool
	ResetRequired      bool
	CenterEstop        bool
	PowerButtonEstop   bool
	LeftLPSUInterlock  bool
	RightLPSUInterlock bool
	DebugMode          bool
	EUCompliantVersion bool

	config *config.ConfigTree
	Lock   sync.Mutex
}

func NewSafetyBoardSim(config *config.ConfigTree) *SafetyBoardSim {
	s := &SafetyBoardSim{
		config: config,
	}
	s.processConfig()
	config.RegisterCallback(s.processConfig)
	return s
}

func (s *SafetyBoardSim) processConfig() {
	s.Lock.Lock()
	defer s.Lock.Unlock()
	s.OverallEStop = s.config.GetNode("overall_estop").GetBoolValue()
	s.LeftEStop = s.config.GetNode("left_estop").GetBoolValue()
	s.InCabEStop = s.config.GetNode("in_cab_estop").GetBoolValue()
	s.Interlock = s.config.GetNode("interlock").GetBoolValue()
	s.LaserKey = s.config.GetNode("laser_key").GetBoolValue()
	s.Lifted = s.config.GetNode("lifted").GetBoolValue()
	s.RightEStop = s.config.GetNode("right_estop").GetBoolValue()
	s.WaterProtect = s.config.GetNode("water_protect").GetBoolValue()
	s.ResetRequired = s.config.GetNode("reset_required").GetBoolValue()
	s.CenterEstop = s.config.GetNode("center_estop").GetBoolValue()
	s.PowerButtonEstop = s.config.GetNode("power_button_estop").GetBoolValue()
	s.LeftLPSUInterlock = s.config.GetNode("left_lpsu_interlock").GetBoolValue()
	s.RightLPSUInterlock = s.config.GetNode("right_lpsu_interlock").GetBoolValue()
	s.DebugMode = s.config.GetNode("debug_mode").GetBoolValue()
	s.EUCompliantVersion = s.config.GetNode("eu_compliant_version").GetBoolValue()
}

func (s *SafetyBoardSim) Start() {
	addr := "0.0.0.0:64546"
	l, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.Fatalf("Error listening to %v: %v", addr, err.Error())
	}
	defer l.Close()

	logrus.Infof("Listening on %v", addr)
	for {
		conn, err := l.Accept()
		if err != nil {
			logrus.Errorf("Error accepting: %v", err.Error())
		}
		go s.handleRequest(conn)
	}
}

func (s *SafetyBoardSim) handleRequest(conn net.Conn) {
	for {
		buf := make([]byte, 1024)
		reqLen, err := conn.Read(buf)
		if err == io.EOF {
			logrus.Infof("Safety: Connection closed")
			conn.Close()
			break
		}

		if reqLen == 0 {
			logrus.Errorf("Request has size 0")
			continue
		}

		if err != nil {
			logrus.Errorf("Safety: Error reading: %v", err.Error())
		}

		logrus.Debugf("Safety board sim: received req %v", buf[:reqLen])

		s.Lock.Lock()
		state := byte(0)
		euState := byte(0)
		if !s.InCabEStop {
			state = 1
		}
		if !s.LeftEStop {
			state = state | 0x2
		}
		if !s.RightEStop {
			state = state | 0x4
		}
		if s.Lifted {
			state = state | 0x8
		}
		if s.LaserKey {
			state = state | 0x10
		}
		if s.Interlock {
			state = state | 0x20
		}
		if s.WaterProtect {
			state = state | 0x40
		}
		if !s.OverallEStop {
			state = state | 0x80
		}
		if s.ResetRequired {
			euState = euState | 0x1
		}
		if !s.CenterEstop {
			euState = euState | 0x2
		}
		if !s.PowerButtonEstop {
			euState = euState | 0x4
		}
		if s.LeftLPSUInterlock {
			euState = euState | 0x8
		}
		if s.RightLPSUInterlock {
			euState = euState | 0x10
		}
		if s.DebugMode {
			euState = euState | 0x20
		}

		s.Lock.Unlock()

		/*
			Per https://www.prosoft-technology.com/kb/assets/intro_modbustcp.pdf
			Modbus Response ADU Example Header
			MBAP Header Fields 					| Example Decimal (Hexadecimal)
			Transaction ID High Order 			| 0 (00) Echoed back, no change
			Transaction ID Low Order 			| 1 (01) Echoed back, no change
			Protocol Identifier High Order 		| 0 (00) Echoed back, no change
			Protocol Identifier Low Order 		| 0 (00) Echoed back, no change
			Length High Order 					| 0 (00) Server calculates
			Length Low Order 					| 4 (04) Server calculates.
			Unit Identifier 					| 255 (FF) or 0 (00) No change

			Modbus Response ADU Example - Read Coil Status Response
			Field Name 				| Example Decimal (Hexadecimal)
			Function Code 			| 1 (01)
			Byte Count 				| 1 (01)
			Data (Coils 3-0) 		| 10 (0A)
		*/
		resp := []byte{}
		if s.EUCompliantVersion {
			resp = []byte{buf[0], buf[1], buf[2], buf[3], 0, 5, 0, 1, 2, state, euState}

		} else {
			resp = []byte{buf[0], buf[1], buf[2], buf[3], 0, 4, 0, 1, 1, state}
		}
		_, err = conn.Write(resp)

		if err != nil {
			logrus.WithError(err).Errorf("Failed to Write to modbus connection")
		}
	}
}

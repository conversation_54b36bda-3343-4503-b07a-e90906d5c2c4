package service

import (
	"context"

	"github.com/carbonrobotics/robot/golang/generated/proto/sim"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/cameras"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/safety"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/supervisory"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

type SimulatorService struct {
	sim.UnimplementedSimulatorServiceServer

	Supervisory *supervisory.SupervisoryBoardSim
	Safety      *safety.SafetyBoardSim
	Predicts    *cameras.SimulationSpace
}

func NewSimulatorService(grpcServer *grpc.Server, supervisory *supervisory.SupervisoryBoardSim, safety *safety.SafetyBoardSim, predicts *cameras.SimulationSpace) *SimulatorService {
	service := &SimulatorService{Supervisory: supervisory, Safety: safety, Predicts: predicts}
	sim.RegisterSimulatorServiceServer(grpcServer, service)
	return service
}

func (s *SimulatorService) Ping(context.Context, *sim.Empty) (*sim.Empty, error) {
	logrus.Info("PING")
	return &sim.Empty{}, nil
}

func (s *SimulatorService) GetNextPredictions(ctx context.Context, r *sim.GetNextPredictionsRequest) (*sim.GetNextPredictionsResponse, error) {
	return s.Predicts.GetNextPredictions(r)
}

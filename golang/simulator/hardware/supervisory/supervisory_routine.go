package supervisory

import (
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
)

const simACDegrees = 100
const simChillerDegrees = 10

const chillerMin = 17.0
const chillerMax = 23.0

const plcMinOff = 0.0
const plcMaxOff = 33.0

const plcMinOn = -2.0
const plcMaxOn = 35.0

const startDelay = 5
const endDelay = 10

type SupervisoryRoutine struct {
	configClient *config.ConfigClient

	startTempAC    float64
	endTempAC      float64
	startTempStaus bool
	durationAC     int

	startTempChiller float64
	endTempChiller   float64
	durationChiller  int
}

func NewSupervisoryRoutine(configClient *config.ConfigClient) *SupervisoryRoutine {
	s := &SupervisoryRoutine{
		configClient: configClient,
	}
	return s
}

type Routine struct {
	AC      string
	Chiller string
}

func (s *SupervisoryRoutine) Run(routines []Routine) {
	for _, r := range routines {
		switch r.AC {
		case "Good":
			s.ACGood()
		case "Hot":
			s.<PERSON>Hot()
		case "Cold":
			s.ACCold()
		default:
			continue
		}
		switch r.<PERSON>ller {
		case "Good":
			s.ChillerGood()
		case "Hot":
			s.ChillerHot()
		case "Cold":
			s.ChillerCold()
		default:
			continue
		}
		var wg sync.WaitGroup
		s.MainContactorOff()
		s.SetStarting()
		time.Sleep(startDelay * time.Second)
		s.MainContactorOn()
		wg.Add(2)
		go func() {
			defer wg.Done()
			s.ACTempereratureChange()
		}()
		go func() {
			defer wg.Done()
			s.ChillerTempereratureChange()
		}()
		wg.Wait()
		time.Sleep(endDelay * time.Second)
	}
}

func (s *SupervisoryRoutine) MainContactorOn() {
	s.configClient.SetBoolValue("simulator/supervisory/main_contactor_status_fb", true)
}

func (s *SupervisoryRoutine) MainContactorOff() {
	s.configClient.SetBoolValue("simulator/supervisory/main_contactor_status_fb", false)
}

func (s *SupervisoryRoutine) SetStarting() {
	s.configClient.SetDoubleValue("simulator/supervisory/server_cabinet_temp", s.startTempAC*simACDegrees)
	s.configClient.SetBoolValue("simulator/supervisory/temp_status", s.startTempStaus)

	s.configClient.SetDoubleValue("simulator/supervisory/chiller_temp", s.startTempChiller*simChillerDegrees)
	s.setChillerAlarms(s.startTempChiller)
}

func (s *SupervisoryRoutine) ACTempereratureChange() {

	step := (s.endTempAC - s.startTempAC) / float64(s.durationAC)
	temp := s.startTempAC
	for i := 0; i < s.durationAC; i++ {
		temp += step
		s.configClient.SetDoubleValue("simulator/supervisory/server_cabinet_temp", temp*simACDegrees)

		var newStatus bool
		if s.startTempStaus {
			newStatus = (temp > plcMinOn && temp < plcMaxOn)
		} else {
			newStatus = (temp > plcMinOff && temp < plcMaxOff)
		}
		s.configClient.SetBoolValue("simulator/supervisory/temp_status", newStatus)
		time.Sleep(time.Second)
	}
}

func (s *SupervisoryRoutine) ChillerTempereratureChange() {

	step := (s.endTempChiller - s.startTempChiller) / float64(s.durationChiller)
	temp := s.startTempChiller
	for i := 0; i < s.durationChiller; i++ {
		s.configClient.SetDoubleValue("simulator/supervisory/chiller_temp", temp*simChillerDegrees)
		s.setChillerAlarms(temp)
		temp += step
		time.Sleep(time.Second)
	}
}

func (s *SupervisoryRoutine) setChillerAlarms(chillerTemp float64) {
	// bit | alarm
	//   1 | High circulating fluid discharge temp
	//   2 | Circulating fluid discharge temp. rise
	//   3 | Circulating fluid discharge temp. drop
	alarmCode := 0.0
	if chillerTemp < chillerMin {
		alarmCode = 8.0
	} else if chillerTemp > chillerMax {
		alarmCode = 6.0 // first two alarms above
	} // else -> reset codes with 0
	s.configClient.SetDoubleValue("simulator/supervisory/chiller_alarm_1", alarmCode)
}

func (s *SupervisoryRoutine) ACGood() {
	s.startTempAC = 27.0
	s.endTempAC = 27.0
	s.startTempStaus = true
	s.durationAC = 0
}

func (s *SupervisoryRoutine) ACHot() {
	s.startTempAC = 37.0
	s.endTempAC = 27.0
	s.startTempStaus = false
	s.durationAC = 30
}

func (s *SupervisoryRoutine) ACCold() {
	s.startTempAC = -3.0
	s.endTempAC = -3.0
	s.startTempStaus = false
	s.durationAC = 0
}

func (s *SupervisoryRoutine) ChillerGood() {
	s.startTempChiller = 20.0
	s.endTempChiller = 20.0
	s.durationChiller = 0
}

func (s *SupervisoryRoutine) ChillerHot() {
	s.startTempChiller = 30.0
	s.endTempChiller = 20.0
	s.durationChiller = 50
}

func (s *SupervisoryRoutine) ChillerCold() {
	s.startTempChiller = 10.0
	s.endTempChiller = 20.0
	s.durationChiller = 50
}

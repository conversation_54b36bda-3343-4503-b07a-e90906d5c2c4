package supervisory

import (
	"fmt"
	"log"
	"net"
	"sync"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/sirupsen/logrus"
)

// boolean statuses
const WATER_PROTECT_STATUS = "RD R37100\r"
const MAIN_CONTACTOR_STATUS_FB = "RD R38001\r"
const POWER_GOOD = "RD R013\r"
const POWER_BAD = "RD R200\r"
const POWER_VERY_BAD = "RD R201\r"
const LIFTED_STATUS = "RD R011\r"
const TEMP_HUMIDITY_BYPASS_STATUS = "RD R204\r"
const TEMP_BYPASS_STATUS = "RD R211\r"
const HUMIDITY_BYPASS_STATUS = "RD R209\r"
const TEMP_HUMIDITY_STATUS = "RD R010\r"
const TEMP_STATUS = "RD R212\r"
const HUMIDITY_STATUS = "RD R213\r"
const TRACTOR_POWER = "RD R38006\r"

const STATUS_ROW_1_BTL_DISABLE = "RD R001\r"
const STATUS_ROW_2_BTL_DISABLE = "RD R002\r"
const STATUS_ROW_3_BTL_DISABLE = "RD R003\r"
const STATUS_ROW_1_SERVER_DISABLE = "RD R006\r"
const STATUS_ROW_2_SERVER_DISABLE = "RD R007\r"
const STATUS_ROW_3_SERVER_DISABLE = "RD R008\r"
const STATUS_ROW_1_SCANNERS_DISABLE = "RD R103\r"
const STATUS_ROW_2_SCANNERS_DISABLE = "RD R104\r"
const STATUS_ROW_3_SCANNERS_DISABLE = "RD R105\r"
const STATUS_WHEEL_ENCODER_DISABLE = "RD R115\r"
const STATUS_GPS_DISABLE = "RD R114\r"
const STATUS_STROBE_DISABLE = "RD R113\r"
const STATUS_AIR_CONDITIONER_DISABLE = "RD R107\r"
const STATUS_CHILLER_DISABLE = "RD R106\r"
const STATUS_TEMP_HUMIDITY_BYPASS_DISABLE = "RD R106\r"

// measurements
const AC_FREQUENCY = "RD DM10612\r"
const AC_VOLTAGE_A_B = "RD DM10437\r"
const AC_VOLTAGE_B_C = "RD DM10439\r"
const AC_VOLTAGE_A_C = "RD DM10441\r"
const AC_VOLTAGE_A = "RD DM10426\r"
const AC_VOLTAGE_B = "RD DM10428\r"
const AC_VOLTAGE_C = "RD DM10430\r"
const PHASE_POWER_W_3 = "RD DM10459\r"
const PHASE_POWER_VA_3 = "RD DM10463\r"
const POWER_FACTOR = "RD DM10470\r"

const SERVER_CABINET_TEMP = "RD DM13401.S\r"
const SERVER_CABINET_HUMIDITY = "RD DM13406\r"
const BATTERY_VOLTAGE_12V = "RD DM13411\r"

const CHILLER_TEMP = "RD DM400.S\r"
const CHILLER_FLOW = "RD DM401\r"
const CHILLER_PRESSURE = "RD DM402\r"
const CHILLER_CONDUCTIVITY = "RD DM403\r"
const CHILLER_STATUS = "RD DM404\r"
const CHILLER_ALARM1 = "RD DM405\r"
const CHILLER_ALARM2 = "RD DM406\r"
const CHILLER_ALARM3 = "RD DM407\r"
const CHILLER_ALARM4 = "RD DM408\r"
const CHILLER_SET_TEMP = "RD DM411\r"

const RUNTIME_240V_HOURS = "RD DM6\r"

// toggles
const ROW_1_SERVER_DISABLE = "WR R006 1\r"
const ROW_1_SERVER_ENABLE = "WR R006 0\r"
const ROW_2_SERVER_DISABLE = "WR R007 1\r"
const ROW_2_SERVER_ENABLE = "WR R007 0\r"
const ROW_3_SERVER_DISABLE = "WR R008 1\r"
const ROW_3_SERVER_ENABLE = "WR R008 0\r"
const ROW_1_SCANNERS_DISABLE = "WR R103 1\r"
const ROW_1_SCANNERS_ENABLE = "WR R103 0\r"
const ROW_2_SCANNERS_DISABLE = "WR R104 1\r"
const ROW_2_SCANNERS_ENABLE = "WR R104 0\r"
const ROW_3_SCANNERS_DISABLE = "WR R105 1\r"
const ROW_3_SCANNERS_ENABLE = "WR R105 0\r"
const ROW_1_BTL_DISABLE = "WR R001 1\r"
const ROW_1_BTL_ENABLE = "WR R001 0\r"
const ROW_2_BTL_DISABLE = "WR R002 1\r"
const ROW_2_BTL_ENABLE = "WR R002 0\r"
const ROW_3_BTL_DISABLE = "WR R003 1\r"
const ROW_3_BTL_ENABLE = "WR R003 0\r"
const WHEEL_ENCODER_DISABLE = "WR R115 1\r"
const WHEEL_ENCODER_ENABLE = "WR R115 0\r"
const GPS_DISABLE = "WR R114 1\r"
const GPS_ENABLE = "WR R114 0\r"
const STROBE_DISABLE = "WR R113 1\r"
const STROBE_ENABLE = "WR R113 0\r"
const AIR_CONDITIONER_DISABLE = "WR R107 1\r"
const AIR_CONDITIONER_ENABLE = "WR R107 0\r"
const CHILLER_DISABLE = "WR R106 1\r"
const CHILLER_ENABLE = "WR R106 0\r"
const TEMP_BYPASS_DISABLE = "WR R211 0\r"
const TEMP_BYPASS_ENABLE = "WR R211 1\r"
const HUMIDITY_BYPASS_DISABLE = "WR R209 0\r"
const HUMIDITY_BYPASS_ENABLE = "WR R209 1\r"
const TEMP_HUMIDITY_BYPASS_DISABLE = "WR R204 0\r"
const TEMP_HUMIDITY_BYPASS_ENABLE = "WR R204 1\r"

type Statuslet struct {
	Name       string
	Request    string
	State      string
	ConfigFunc func(string) string

	Lock sync.Mutex
}

func NewStatuslet(name string, req string, configFunc func(string) string) *Statuslet {
	s := &Statuslet{
		Name:       name,
		Request:    req,
		ConfigFunc: configFunc,
	}
	return s
}

type ToggleFunction func(*Toggle)

type Toggle struct {
	Name           string
	EnableRequest  string
	DisableRequest string
	StatusRequest  string
	State          string
	ConfigFunc     func(string) string

	Lock sync.Mutex
}

func NewToggle(name string, disableReq string, enableReq string, statusReq string, configFunc func(string) string) *Toggle {
	return &Toggle{
		Name:           name,
		EnableRequest:  enableReq,
		DisableRequest: disableReq,
		StatusRequest:  statusReq,
		ConfigFunc:     configFunc,
	}
}

type SupervisoryBoardSim struct {
	Statuslets map[string]*Statuslet
	Toggles    map[string]*Toggle

	config *config.ConfigTree
}

func (s *SupervisoryBoardSim) readBoolConfig(name string) string {
	val := s.config.GetNode(name).GetBoolValue()
	if val {
		return "1"
	}
	return "0"
}

func (s *SupervisoryBoardSim) readFloatConfig(name string) string {
	val := s.config.GetNode(name).GetFloatValue()
	return fmt.Sprintf("%v", val)
}

func NewSupervisoryBoardSim(config *config.ConfigTree) *SupervisoryBoardSim {
	svc := &SupervisoryBoardSim{
		Statuslets: make(map[string]*Statuslet),
		Toggles:    make(map[string]*Toggle),
		config:     config,
	}

	statuslets := []*Statuslet{
		NewStatuslet("water_protect_status", WATER_PROTECT_STATUS, svc.readBoolConfig),
		NewStatuslet("main_contactor_status_fb", MAIN_CONTACTOR_STATUS_FB, svc.readBoolConfig),
		NewStatuslet("power_good", POWER_GOOD, svc.readBoolConfig),
		NewStatuslet("power_bad", POWER_BAD, svc.readBoolConfig),
		NewStatuslet("power_very_bad", POWER_VERY_BAD, svc.readBoolConfig),
		NewStatuslet("lifted_status", LIFTED_STATUS, svc.readBoolConfig),
		// NewStatuslet("temp_humidity_bypass_status", TEMP_HUMIDITY_BYPASS_STATUS, "1"),
		// NewStatuslet("temp_bypass_status", TEMP_BYPASS_STATUS, "1"),
		// NewStatuslet("humidity_bypass_status", HUMIDITY_BYPASS_STATUS, "1"),
		NewStatuslet("temp_humidity_status", TEMP_HUMIDITY_STATUS, svc.readBoolConfig),
		NewStatuslet("temp_status", TEMP_STATUS, svc.readBoolConfig),
		NewStatuslet("humidity_status", HUMIDITY_STATUS, svc.readBoolConfig),
		NewStatuslet("tractor_power", TRACTOR_POWER, svc.readBoolConfig),

		// measurements
		NewStatuslet("ac_frequency", AC_FREQUENCY, svc.readFloatConfig),
		NewStatuslet("ac_voltage_a_b", AC_VOLTAGE_A_B, svc.readFloatConfig),
		NewStatuslet("ac_voltage_b_c", AC_VOLTAGE_B_C, svc.readFloatConfig),
		NewStatuslet("ac_voltage_a_c", AC_VOLTAGE_A_C, svc.readFloatConfig),
		NewStatuslet("ac_voltage_a", AC_VOLTAGE_A, svc.readFloatConfig),
		NewStatuslet("ac_voltage_b", AC_VOLTAGE_B, svc.readFloatConfig),
		NewStatuslet("ac_voltage_c", AC_VOLTAGE_C, svc.readFloatConfig),
		NewStatuslet("phase_power_w_3", PHASE_POWER_W_3, svc.readFloatConfig),
		NewStatuslet("phase_power_va_3", PHASE_POWER_VA_3, svc.readFloatConfig),
		NewStatuslet("power_factor", POWER_FACTOR, svc.readFloatConfig),
		NewStatuslet("server_cabinet_temp", SERVER_CABINET_TEMP, svc.readFloatConfig),
		NewStatuslet("server_cabinet_humidity", SERVER_CABINET_HUMIDITY, svc.readFloatConfig),
		NewStatuslet("battery_voltage_12v", BATTERY_VOLTAGE_12V, svc.readFloatConfig),
		NewStatuslet("chiller_temp", CHILLER_TEMP, svc.readFloatConfig),
		NewStatuslet("chiller_flow", CHILLER_FLOW, svc.readFloatConfig),
		NewStatuslet("chiller_pressure", CHILLER_PRESSURE, svc.readFloatConfig),
		NewStatuslet("chiller_conductivity", CHILLER_CONDUCTIVITY, svc.readFloatConfig),
		NewStatuslet("chiller_alarm_1", CHILLER_ALARM1, svc.readFloatConfig),
		NewStatuslet("chiller_alarm_2", CHILLER_ALARM2, svc.readFloatConfig),
		NewStatuslet("chiller_alarm_3", CHILLER_ALARM3, svc.readFloatConfig),
		NewStatuslet("chiller_alarm_4", CHILLER_ALARM4, svc.readFloatConfig),
		NewStatuslet("chiller_set_temp", CHILLER_SET_TEMP, svc.readFloatConfig),
		NewStatuslet("runtime_240v_hours", RUNTIME_240V_HOURS, svc.readFloatConfig),
	}
	toggles := []*Toggle{
		NewToggle("row_1_server_disable", ROW_1_SERVER_DISABLE, ROW_1_SERVER_ENABLE, STATUS_ROW_1_SERVER_DISABLE, svc.readBoolConfig),
		NewToggle("row_2_server_disable", ROW_2_SERVER_DISABLE, ROW_2_SERVER_ENABLE, STATUS_ROW_2_SERVER_DISABLE, svc.readBoolConfig),
		NewToggle("row_3_server_disable", ROW_3_SERVER_DISABLE, ROW_3_SERVER_ENABLE, STATUS_ROW_3_SERVER_DISABLE, svc.readBoolConfig),
		NewToggle("row_1_scanners_disable", ROW_1_SCANNERS_DISABLE, ROW_1_SCANNERS_ENABLE, STATUS_ROW_1_SCANNERS_DISABLE, svc.readBoolConfig),
		NewToggle("row_2_scanners_disable", ROW_2_SCANNERS_DISABLE, ROW_2_SCANNERS_ENABLE, STATUS_ROW_2_SCANNERS_DISABLE, svc.readBoolConfig),
		NewToggle("row_3_scanners_disable", ROW_3_SCANNERS_DISABLE, ROW_3_SCANNERS_ENABLE, STATUS_ROW_3_SCANNERS_DISABLE, svc.readBoolConfig),
		NewToggle("row_1_btl_disable", ROW_1_BTL_DISABLE, ROW_1_BTL_ENABLE, STATUS_ROW_1_BTL_DISABLE, svc.readBoolConfig),
		NewToggle("row_2_btl_disable", ROW_2_BTL_DISABLE, ROW_2_BTL_ENABLE, STATUS_ROW_2_BTL_DISABLE, svc.readBoolConfig),
		NewToggle("row_3_btl_disable", ROW_3_BTL_DISABLE, ROW_3_BTL_ENABLE, STATUS_ROW_3_BTL_DISABLE, svc.readBoolConfig),
		NewToggle("wheel_encoder_disable", WHEEL_ENCODER_DISABLE, WHEEL_ENCODER_ENABLE, STATUS_WHEEL_ENCODER_DISABLE, svc.readBoolConfig),
		NewToggle("gps_disable", GPS_DISABLE, GPS_ENABLE, STATUS_GPS_DISABLE, svc.readBoolConfig),
		NewToggle("strobe_disable", STROBE_DISABLE, STROBE_ENABLE, STATUS_STROBE_DISABLE, svc.readBoolConfig),
		NewToggle("air_conditioner_disable", AIR_CONDITIONER_DISABLE, AIR_CONDITIONER_ENABLE, STATUS_AIR_CONDITIONER_DISABLE, svc.readBoolConfig),
		NewToggle("chiller_disable", CHILLER_DISABLE, CHILLER_ENABLE, STATUS_CHILLER_DISABLE, svc.readBoolConfig),
		NewToggle("temp_bypass_disable", TEMP_BYPASS_DISABLE, TEMP_BYPASS_ENABLE, TEMP_BYPASS_STATUS, svc.readBoolConfig),
		NewToggle("humidity_bypass_disable", HUMIDITY_BYPASS_DISABLE, HUMIDITY_BYPASS_ENABLE, HUMIDITY_BYPASS_STATUS, svc.readBoolConfig),
		NewToggle("temp_humidity_bypass_disable", TEMP_HUMIDITY_BYPASS_DISABLE, TEMP_HUMIDITY_BYPASS_ENABLE, TEMP_HUMIDITY_BYPASS_STATUS, svc.readBoolConfig),
	}
	for _, s := range statuslets {
		svc.Statuslets[s.Name] = s
	}
	for _, t := range toggles {
		svc.Toggles[t.Name] = t
	}
	svc.processConfig()
	config.RegisterCallback(svc.processConfig)
	return svc
}

func (s *SupervisoryBoardSim) processConfig() {
	for _, s := range s.Statuslets {
		s.State = s.ConfigFunc(s.Name)
		logrus.Debugf("Supervisory config: read %v for %v", s.State, s.Name)
	}
	for _, t := range s.Toggles {
		t.State = t.ConfigFunc(t.Name)
		logrus.Debugf("Supervisory config: read %v for %v", t.State, t.Name)
	}
}

func (s *SupervisoryBoardSim) Start() {
	logrus.Info("Supervisory board simulation started")

	pc, err := net.ListenPacket("udp", ":64545")
	if err != nil {
		log.Fatal(err)
	}
	defer pc.Close()

	for {
		buf := make([]byte, 1024)
		n, addr, err := pc.ReadFrom(buf)
		if err != nil {
			continue
		}
		go s.serve(pc, addr, buf[:n])
	}
}

func (s *SupervisoryBoardSim) serve(pc net.PacketConn, addr net.Addr, buf []byte) {
	sbuf := string(buf)
	logrus.Debugf("Supervisory_sim: received request from %v, content=%v", addr, sbuf)

	for _, t := range s.Toggles {
		if sbuf == t.DisableRequest {
			t.Lock.Lock()
			t.State = "1"
			t.Lock.Unlock()
			logrus.Debugf("Supervisory: set 0 to %v", t.Name)
			return
		}
		if sbuf == t.EnableRequest {
			t.Lock.Lock()
			t.State = "0"
			t.Lock.Unlock()
			logrus.Debugf("Supervisory: set 1 to %v", t.Name)
			return
		}
		if sbuf == t.StatusRequest {
			t.Lock.Lock()
			pc.WriteTo([]byte(t.State), addr)
			t.Lock.Unlock()
			logrus.Debugf("Supervisory: returned %v to %v", t.State, t.Name)
			return
		}
	}

	for _, st := range s.Statuslets {
		if st.Request == sbuf {
			pc.WriteTo([]byte(st.State), addr)
			return
		}
	}

	pc.WriteTo([]byte("0"), addr)
}

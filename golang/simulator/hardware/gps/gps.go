package gps

import (
	"log"
	"math"
	"net"
	"sync"
	"time"

	"github.com/StefanSchroeder/Golang-Ellipsoid/ellipsoid"
	nanopb "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/benjamin_gps_board"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/gps"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/sirupsen/logrus"
	"gonum.org/v1/gonum/mat"
	"google.golang.org/protobuf/proto"
)

const mph2mps = 0.44704
const mToMm = 1000
const readTimeout = 5 * time.Second

type LLA struct {
	lat float64 // degrees
	lon float64 // degrees
	alt float64 // meters
}

// Earth-centered, Earth-fixed coordinate system
type ECEF struct {
	x float64 // meters
	y float64
	z float64
}

// East North UP, plane tangent to the reference ellipsoid
// origin at ECEF point
type ENU struct {
	x float64 // meters
	y float64
	z float64
}

type GPSBoardSim struct {
	geo        *ellipsoid.Ellipsoid
	curENU     *ENU
	originECEF *ECEF
	convData   []float64

	fieldLenM  float64
	xOffM      float64
	yOffM      float64
	zOffM      float64
	curHeading float64
	doTurns    bool

	configNode        *config.ConfigTree
	velocityMSec      float64
	headingRadians    float64
	TerminateChannel1 chan bool
	StoppedChannel1   chan bool
	TerminateChannel2 chan bool
	StoppedChannel2   chan bool
	enabled           bool

	lock sync.Mutex
}

func (s *GPSBoardSim) readConfig() {
	s.lock.Lock()
	defer s.lock.Unlock()

	s.velocityMSec = s.configNode.GetNode("velocity_mph").GetFloatValue() * mph2mps
	s.headingRadians = Deg2Rad(s.configNode.GetNode("heading_degrees").GetFloatValue())
	s.fieldLenM = s.configNode.GetNode("field_len").GetFloatValue()
	s.xOffM = s.configNode.GetNode("gps_offset_x").GetFloatValue() / 1000.0
	s.yOffM = s.configNode.GetNode("gps_offset_y").GetFloatValue() / 1000.0
	s.zOffM = s.configNode.GetNode("gps_offset_z").GetFloatValue() / 1000.0
	s.doTurns = s.configNode.GetNode("robot_turns").GetBoolValue()

	lat := s.configNode.GetNode("gps_origin_lat").GetFloatValue()
	lon := s.configNode.GetNode("gps_origin_lon").GetFloatValue()
	alt := s.configNode.GetNode("gps_origin_alt").GetFloatValue()
	s.setOriginLocked(LLA{lat, lon, alt})
}

func (s *GPSBoardSim) SetVelocity(velocityMPH float64) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.velocityMSec = velocityMPH * mph2mps
}

func NewGPSBoardSim(configSubscriber *config.ConfigSubscriber) *GPSBoardSim {
	ellipse := ellipsoid.Init("WGS84", ellipsoid.Degrees, ellipsoid.Meter,
		ellipsoid.LongitudeIsSymmetric, ellipsoid.BearingIsSymmetric)
	simConfig := configSubscriber.GetConfigNode("simulator", "")
	s := &GPSBoardSim{
		geo:               &ellipse,
		curENU:            &ENU{0, 0, 0},
		configNode:        simConfig,
		TerminateChannel1: make(chan bool, 1),
		StoppedChannel1:   make(chan bool, 1),
		TerminateChannel2: make(chan bool, 1),
		StoppedChannel2:   make(chan bool, 1),
		enabled:           simConfig.GetNode("gps_sim_enabled").GetBoolValue(),
	}

	s.configNode.RegisterCallback(s.readConfig)
	s.readConfig()
	return s
}

func (s *GPSBoardSim) Start() {
	if !s.enabled {
		logrus.Info("GPS board is not enabled, not starting.")
		return
	}
	logrus.Info("GPS board simulation started")
	s.moveBot()
	s.listenAndServe()
}

func (s *GPSBoardSim) Stop() {
	if !s.enabled {
		// Not enabled so never started
		return
	}
	s.TerminateChannel1 <- true
	s.TerminateChannel2 <- true
	<-s.StoppedChannel1
	<-s.StoppedChannel2
	logrus.Info("GPS board simulation stopped")
}

func (s *GPSBoardSim) waitForTerminateSignal(sleepTimeMs uint64, channel chan bool) bool {
	select {
	case <-channel:
		logrus.Infof("GPS_Sim: received request to terminate replay")
		return true
	case <-time.After(time.Duration(sleepTimeMs) * time.Millisecond):
		return false
	}
}

func (s *GPSBoardSim) moveBot() {
	distTrav := 0.0
	robotWidthM := 240.0 * 0.0254 // TODO: use config
	turnDirection := 1.0          // right
	s.lock.Lock()
	s.curHeading = s.headingRadians
	s.lock.Unlock()
	go func() {
		sleepTime := 10
		for {
			timeStartNano := time.Now().UnixNano()
			if s.waitForTerminateSignal(uint64(sleepTime), s.TerminateChannel1) {
				break
			}
			s.lock.Lock()
			timeEndNano := time.Now().UnixNano()
			deltaTimeNano := timeEndNano - timeStartNano
			delta := float64(deltaTimeNano) * float64(s.velocityMSec) / 1_000_000_000
			deltaY := delta * math.Cos(s.curHeading)
			deltaX := delta * math.Sin(s.curHeading)
			s.curENU.y += deltaY
			s.curENU.x += deltaX
			fieldLenM := s.fieldLenM
			doTurns := s.doTurns
			s.lock.Unlock()
			distTrav += delta
			if distTrav > fieldLenM && doTurns {
				if turnDirection == 1 {
					logrus.Info("start turning right")
				} else {
					logrus.Info("start turning left")
				}
				radius := robotWidthM / 2
				s.lock.Lock()
				logrus.Infof("cur heading: %v", s.curHeading)
				x0 := s.curENU.x + radius*math.Cos(-s.headingRadians)
				y0 := s.curENU.y + radius*math.Sin(-s.headingRadians)
				logrus.Infof("start: x: %v, y: %v", s.curENU.x, s.curENU.y)
				logrus.Infof("center: x: %v, y: %v", x0, y0)
				s.lock.Unlock()
				for curAngle := math.Pi; curAngle > 0.0 && curAngle < 2*math.Pi; {
					timeStartNano = time.Now().UnixNano()
					if s.waitForTerminateSignal(uint64(sleepTime), s.TerminateChannel1) {
						s.StoppedChannel1 <- true
						return
					}
					timeEndNano = time.Now().UnixNano()
					s.lock.Lock()
					angVel := s.velocityMSec / radius
					deltaTimeNano := timeEndNano - timeStartNano
					deltaRad := float64(deltaTimeNano) * float64(angVel) / 1_000_000_000
					curAngle -= deltaRad * turnDirection
					s.curHeading = math.Mod(s.curHeading+(deltaRad*turnDirection), 2*math.Pi)
					if turnDirection == 1 && curAngle < 0.0 {
						// turning right, overshot
						curAngle = 0.0
						s.curHeading = s.headingRadians + math.Pi
					} else if turnDirection == -1 && curAngle > 2*math.Pi {
						// turning left, overshot
						curAngle = 2 * math.Pi
						s.curHeading = s.headingRadians
					}
					newX := x0 + radius*math.Cos(curAngle-s.headingRadians)
					newY := y0 + radius*math.Sin(curAngle-s.headingRadians)
					// logrus.Infof("x: %v, y: %v", newX, newY)
					s.curENU.y = newY
					s.curENU.x = newX

					s.lock.Unlock()
				}
				s.lock.Lock()
				logrus.Infof("end: x: %v, y: %v", s.curENU.x, s.curENU.y)
				logrus.Infof("cur heading: %v", s.curHeading)
				s.lock.Unlock()
				logrus.Info("done turning")
				distTrav = 0.0
				turnDirection *= -1
			}

		}
		s.StoppedChannel1 <- true
	}()
}

func (s *GPSBoardSim) listenAndServe() {
	go func() {
		sleepTime := 10

		pc, err := net.ListenPacket("udp", ":64547")
		if err != nil {
			log.Fatal(err)
		}
		defer pc.Close()

		for {
			if s.waitForTerminateSignal(uint64(sleepTime), s.TerminateChannel2) {
				break
			}
			buf := make([]byte, 1024)
			pc.SetReadDeadline(time.Now().Add(readTimeout))
			n, addr, err := pc.ReadFrom(buf)
			if err != nil {
				continue
			}
			go s.serve(pc, addr, buf[:n])
		}
		s.StoppedChannel2 <- true
	}()
}

func (s *GPSBoardSim) serve(pc net.PacketConn, addr net.Addr, buf []byte) {
	logrus.Debugf("GPS_Sim: received request from %v, content_len=(%v)", addr, len(buf))

	req := &nanopb.Request{}
	proto.Unmarshal(buf, req)

	logrus.Debugf("GPS_Sim: unmarshalled to id=%v, nils gps %v heading %v ping %v", req.GetHeader().RequestId, req.GetGps() == nil, req.GetHeading() == nil, req.GetPing() == nil)
	if req.GetGps() != nil {
		// convert the ENU location to LLA before reporting out
		s.lock.Lock()
		curECEF := s.ENUtoECEF(*s.curENU)
		s.lock.Unlock()
		curLLA := s.toLLA(curECEF)
		reply := &nanopb.Reply{
			Header: &request.RequestHeader{
				RequestId: req.GetHeader().RequestId,
			},
			Reply: &nanopb.Reply_Gps{
				Gps: &gps.Reply{
					Reply: &gps.Reply_Position{
						Position: &gps.Position_Reply{
							Latitude:      curLLA.lat,
							Longitude:     curLLA.lon,
							HeightMm:      int32(curLLA.alt * mToMm),
							TimestampMs:   uint64(time.Now().UnixMilli()),
							HaveApproxFix: true,
							HaveFix:       true,
						},
					},
				},
			},
		}
		msg, err := proto.Marshal(reply)
		if err != nil {
			logrus.Error(err)
		} else {
			n, err := pc.WriteTo(msg, addr)
			logrus.Debugf("GPS_sim: Write done to %v with %v and err=%v", addr, n, err)
		}
	}
}

func (s *GPSBoardSim) setOriginLocked(lla LLA) {
	lat, lon, alt := lla.lat, lla.lon, lla.alt
	s.convData = []float64{
		-sin(lon), -sin(lat) * cos(lon), cos(lat) * cos(lon),
		cos(lon), -sin(lat) * sin(lon), cos(lat) * sin(lon),
		0, cos(lat), sin(lat),
	}

	x, y, z := s.geo.ToECEF(lat, lon, alt)
	s.originECEF = &ECEF{x, y, z}
}

func (s *GPSBoardSim) toLLA(ecef ECEF) LLA {
	lat, lon, alt := s.geo.ToLLA(ecef.x, ecef.y, ecef.z)
	return LLA{lat, lon, alt}
}

func (s *GPSBoardSim) ENUtoECEF(enu ENU) ECEF {
	// https://gis.stackexchange.com/questions/308445/local-enu-point-of-interest-to-ecef

	// XYZ = convMat * xyz + XYZ_r

	xOffENU := s.xOffM*math.Cos(-s.curHeading) + s.yOffM*math.Sin(s.curHeading)
	yOffENU := s.xOffM*math.Sin(-s.curHeading) + s.yOffM*math.Cos(s.curHeading)
	zOffENU := -s.zOffM

	C := mat.NewDense(3, 1, nil)
	XYZ := mat.NewDense(3, 1, nil)
	xyz := mat.NewDense(3, 1, []float64{enu.x + xOffENU, enu.y + yOffENU, enu.z + zOffENU})
	XYZ_r := mat.NewDense(3, 1, []float64{s.originECEF.x, s.originECEF.y, s.originECEF.z})
	convMat := mat.NewDense(3, 3, s.convData)

	C.Product(convMat, xyz)
	XYZ.Add(C, XYZ_r)
	return ECEF{XYZ.At(0, 0), XYZ.At(1, 0), XYZ.At(2, 0)}
}

func Deg2Rad(deg float64) float64 {
	return deg * (math.Pi / 180)
}

func sin(deg float64) float64 {
	return math.Sin(Deg2Rad(deg))
}

func cos(deg float64) float64 {
	return math.Cos(Deg2Rad(deg))
}

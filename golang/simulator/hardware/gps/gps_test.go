package gps

import (
	"testing"
	"time"

	"github.com/StefanSchroeder/Golang-Ellipsoid/ellipsoid"
	"github.com/stretchr/testify/assert"
)

const distanceTol = 1.5
const headingTol = 0.05

func TestDistanceVsTime(t *testing.T) {
	origin := LLA{47.62684451147215, -122.34409499118749, 53.0} // 807
	ellipse := ellipsoid.Init("WGS84", ellipsoid.Degrees, ellipsoid.Meter,
		ellipsoid.LongitudeIsSymmetric, ellipsoid.BearingIsSymmetric)

	tests := []struct {
		name     string
		heading  float64       // degree
		velocity float64       // mps
		duration time.Duration // sec
	}{
		{
			"North: 60 mph for 500 ms",
			0,
			26.8224,
			500,
		},
		{
			"East: 60 mps for 250 ms",
			90,
			60,
			250,
		},
		{
			"South: 45 mps for 90 ms",
			180,
			45,
			90,
		},
		{
			"West: 75 mps for 100 ms",
			270,
			75,
			100,
		},
		{
			"NorthEast: 15 mps for 300 ms",
			45,
			15,
			300,
		},
		{
			"NorthWest: 5 mps for 150 ms",
			215,
			5,
			150,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			s := &GPSBoardSim{
				geo:               &ellipse,
				curENU:            &ENU{0, 0, 0},
				velocityMSec:      test.velocity,
				headingRadians:    Deg2Rad(test.heading),
				doTurns:           false,
				TerminateChannel1: make(chan bool, 1),
				StoppedChannel1:   make(chan bool, 1),
				TerminateChannel2: make(chan bool, 1),
				StoppedChannel2:   make(chan bool, 1),
			}
			s.setOriginLocked(origin)
			curECEF := s.ENUtoECEF(*s.curENU)
			startLLA := s.toLLA(curECEF)

			s.moveBot()

			timeStartMilli := time.Now().UnixMilli()
			time.Sleep(test.duration * time.Millisecond)
			timeEndMilli := time.Now().UnixMilli()

			curECEF = s.ENUtoECEF(*s.curENU)
			endLLA := s.toLLA(curECEF)

			s.TerminateChannel1 <- true
			<-s.StoppedChannel1

			distance, bearing := s.geo.To(startLLA.lat, startLLA.lon, endLLA.lat, endLLA.lon)
			if bearing < 0 {
				bearing += 360
			}
			deltaTimeMilli := float64(timeEndMilli - timeStartMilli)
			expected := test.velocity * deltaTimeMilli / 1000

			assert.InDelta(t, expected, distance, distanceTol)
			assert.InDelta(t, test.heading, bearing, headingTol)
		})
	}

}

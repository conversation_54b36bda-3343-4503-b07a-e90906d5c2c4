package client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/sim_UI"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

type HWSimClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client sim_UI.SimulatorUIServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *HWSimClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewHWSimClientWithAddr(addr string) *HWSimClient {
	client := &HWSimClient{
		addr: addr,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}
func NewHWSimClient() *HWSimClient {
	return NewHWSimClientWithAddr("localhost:8090")
}

func (c *HWSimClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func getDefaultClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
}

func (c *HWSimClient) getClient() (sim_UI.SimulatorUIServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.NewClient(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = sim_UI.NewSimulatorUIServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *HWSimClient) sendRequest(f func(sim_UI.SimulatorUIServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.resetConnection()
			case codes.DeadlineExceeded:
				logrus.Warningf("Resetting hw manager connection due to deadline exceeded error.")
				c.resetConnection()
			default:
			}
		}
		return nil, err
	}

	return result, nil
}

func (c *HWSimClient) SetVelocity(velocity float32) error {
	ctx, cancel := getDefaultClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client sim_UI.SimulatorUIServiceClient) (interface{}, error) {
		return client.SetVelocity(ctx, &sim_UI.SetVelocityRequest{Velocity: velocity})
	})
	return err
}

syntax = "proto3";

package carbon.simulator;
option go_package = "proto/sim";

message Empty {

}

message GetNextPredictionsRequest {
    string name = 1;
    int64 timestamp_ms = 2;
}

message Prediction {
    uint32 x_px = 1;
    uint32 y_px = 2;
    uint32 size_px = 3;
    float score = 4;
    map<string, float> detection_classes = 5;
    bool is_weed = 6;
    bool is_real = 7;
    float weed_score = 8;
    float crop_score = 9;
    string type = 10;
    map<string, float> weed_detection_classes = 11;
    repeated float embedding = 12;
    float plant_score = 13;
    repeated string mask_intersection_classes = 14;
}

message GetNextPredictionsResponse {
    repeated Prediction predictions = 1;
    int64 timestamp_ms = 2;
}

service SimulatorService {
    rpc Ping(Empty) returns (Empty);
    rpc GetNextPredictions(GetNextPredictionsRequest) returns (GetNextPredictionsResponse);
}

package cameras

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/recorder"
	"github.com/carbonrobotics/robot/golang/generated/proto/sim"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/simulation"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/types"
	"github.com/sirupsen/logrus"
)

// TODO Set via config,
const targetFPS = 40
const targetsPerPredict = 6

type Prediction struct {
	x                    float32
	y                    float32
	size                 float32
	score                float32
	weedScore            float32
	cropScore            float32
	plantScore           float32
	detectionClasses     map[string]float32
	weedDetectionClasses map[string]float32
	isWeed               bool
	fromCam              string
	Type                 string
}

type Image struct {
	ts          int64
	predictions []*sim.Prediction
}

func (s *SimulationSpace) GetPredictionsFromReplay(req *sim.GetNextPredictionsRequest, replayer *simulation.RecordingReplayer, camId int) (*sim.GetNextPredictionsResponse, error) {
	frame, err := replayer.GetNextDeepweed(camId, req.GetTimestampMs())

	if err != nil {
		logrus.WithError(err).Error("Error retrieving Deepweed Prediction")
		return nil, err
	}

	resp := &sim.GetNextPredictionsResponse{
		Predictions: make([]*sim.Prediction, 0),
		TimestampMs: frame.GetTimestampMs(),
	}

	for _, d := range frame.Detections {
		detectionClasses := make(map[string]float32)
		for _, cls := range d.DetectionClasses {
			detectionClasses[cls.Class] = cls.Score
		}

		detection_type := ""
		if d.HitClass == recorder.DeepweedDetection_WEED {
			maxScore := float32(-1)
			for class, score := range detectionClasses {
				if score > maxScore {
					maxScore = score
					detection_type = class
				}
			}
		} else if d.HitClass == recorder.DeepweedDetection_CROP {
			detection_type = "CROP"
		}

		resp.Predictions = append(resp.Predictions, &sim.Prediction{
			XPx:                  uint32(d.X),
			YPx:                  uint32(d.Y),
			SizePx:               uint32(d.Size), // TODO why is this a uint?
			Score:                d.Score,
			IsWeed:               d.HitClass == recorder.DeepweedDetection_WEED,
			DetectionClasses:     detectionClasses,
			WeedDetectionClasses: detectionClasses,
			IsReal:               true,
			Type:                 detection_type,
		})
	}

	return resp, nil
}

func (s *SimulationSpace) GrabImages(termChannel chan bool, stoppedChan chan bool) {
	// currently only serves predicts, loop runs at 6.7 fps or ~149 ms intervals
	// intended to simulate the strobe board triggering the predict cameras at a const rate
	go func() {
		var wg sync.WaitGroup
		timeBetweenPics := int64(1000.0 / s.predictFPS)
		for {
			startTime := time.Now().UnixMilli()

			// process image
			newImages := make(map[Camera]*Image, 0)
			for _, cam := range s.cams {
				newImages[cam] = &Image{
					0,
					make([]*sim.Prediction, 0),
				}
			}

			fieldObjects := s.field.GetPredictObjects()
			for cam, image := range newImages {
				cam := cam
				image := image
				geoCam := *(cam.geoCam)
				wg.Add(1)
				go func() {
					// since there isn't a good way to tell if an object, based on its array index, is going to be in the camera's view
					// other than converting it into pixel space, we have to scan through each object for each camera.
					defer wg.Done()
					for _, obj := range fieldObjects {
						// translate the (x, y, z) location of the object to the (x, y) location in camera's 2d space
						var dx, dy float64
						geoCam.Get_distorted_px_from_abs_position_swig(obj.GetX(), obj.GetY(), obj.GetZ(), &dx, &dy)
						if (dx >= 0) && (dx < cam.roiX) && (dy >= 0) && (dy < cam.roiY) && (obj.GetSize() > 0) {
							sizePx := geoCam.Get_size_px_from_size_mm(float64(obj.GetSize()), obj.GetZ())
							image.predictions = append(image.predictions, &sim.Prediction{
								XPx:                     uint32(dx),
								YPx:                     uint32(dy),
								SizePx:                  uint32(sizePx),
								Score:                   obj.GetScore(),
								WeedScore:               obj.GetWeedScore(),
								CropScore:               obj.GetCropScore(),
								PlantScore:              obj.GetPlantScore(),
								IsWeed:                  obj.GetIsWeed(),
								DetectionClasses:        obj.GetDetectionClasses(),
								WeedDetectionClasses:    obj.GetWeedDetectionClasses(),
								IsReal:                  obj.GetIsReal(),
								Type:                    obj.GetRealType(),
								Embedding:               obj.GetEmbedding(),
								MaskIntersectionClasses: obj.GetMaskIntersectionClasses(),
							})
						}
					}
				}()
			}
			wg.Wait()

			endTime := time.Now().UnixMilli()
			elapsedTime := endTime - startTime
			sleepTime := timeBetweenPics - elapsedTime

			// wait rest of time between images
			if sleepTime > 0 && s.waitForTerminateSignal(uint64(sleepTime), termChannel) {
				break
			}

			// publish images
			s.imgCond.L.Lock()
			ts := time.Now().UnixMilli()
			for cam, image := range newImages {
				image.ts = ts
				s.images[cam.name] = image
			}
			s.imgCond.Broadcast()
			s.imgCond.L.Unlock()
		}
		stoppedChan <- true
	}()
}

func (s *SimulationSpace) GetNextPredictions(req *sim.GetNextPredictionsRequest) (*sim.GetNextPredictionsResponse, error) {
	if !strings.HasPrefix(req.Name, "predict") {
		time.Sleep(100 * time.Millisecond)
		return &sim.GetNextPredictionsResponse{
			Predictions: make([]*sim.Prediction, 0),
			TimestampMs: time.Now().UnixMilli(),
		}, nil
	}

	id, err := strconv.Atoi(req.Name[7:8])
	if err != nil {
		return nil, err
	}
	s.lock.Lock()
	replayer := s.replayer
	s.lock.Unlock()

	if s.mode.Load() == types.Replay {
		if replayer == nil {
			return nil, fmt.Errorf("Replayer is nil")
		}
		return s.GetPredictionsFromReplay(req, replayer, id)
	}

	s.imgCond.L.Lock()
	defer s.imgCond.L.Unlock()
	if _, ok := s.images[req.Name]; !ok {
		err := fmt.Errorf("no image available for %v", req.Name)
		logrus.WithError(err).Errorf("Error serving predictions.")
		return &sim.GetNextPredictionsResponse{}, err
	}
	for req.TimestampMs >= s.images[req.Name].ts && s.mode.Load() == types.Generate {
		s.imgCond.Wait()
	}
	if s.mode.Load() == types.Replay {
		return nil, fmt.Errorf("Replay Mode changed")
	}

	resp := &sim.GetNextPredictionsResponse{
		Predictions: s.images[req.Name].predictions,
		TimestampMs: s.images[req.Name].ts,
	}
	return resp, nil
}

package main

import (
	"fmt"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/stretchr/testify/assert"
)

func Test_generateRobotBroadcasterID(t *testing.T) {
	osHostnameSave := osHostname
	timeNowSave := timeNow
	defer func() {
		osHostname = osHostnameSave
		timeNow = timeNowSave
	}()

	testTime := time.Now()
	testHost := "test-hostname"

	tests := []struct {
		name                    string
		hostNameErr             error
		makaRobotName           string
		makaRole                string
		makaRow                 string
		expectedBroadcasterID   string
		expectedBroadcasterName string
	}{
		{
			"no robot name, not robot",
			nil,
			"",
			"slayer",
			"",
			fmt.Sprintf("carbon-bcr-%s-%d", testHost, testTime.Unix()),
			testHost,
		},
		{
			"no robot role, not robot",
			nil,
			"slayer1",
			"",
			"",
			fmt.Sprintf("carbon-bcr-%s-%d", testHost, testTime.Unix()),
			testHost,
		},
		{
			"slayer",
			nil,
			"slayer1",
			"slayer",
			"1",
			"slayer1-slayer",
			"slayer1-slayer",
		},
		{
			"row computer",
			nil,
			"slayer1",
			"row",
			"1",
			"slayer1-row-1",
			"slayer1-row-1",
		},
		{
			"mini module",
			nil,
			"slayer1",
			"row-primary",
			"1",
			"slayer1-row-primary-1",
			"slayer1-row-primary-1",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			osHostname = func() (name string, err error) {
				return testHost, test.hostNameErr
			}
			timeNow = func() time.Time {
				return testTime
			}

			broadcaster := &Broadcaster{
				robotEnv: environment.Robot{
					MakaRobotName: test.makaRobotName,
					MakaRole:      test.makaRole,
					MakaRow:       test.makaRow,
				},
			}
			broadcasterID, broadcasterName := broadcaster.generateRobotBroadcasterID()
			assert.Equal(t, test.expectedBroadcasterID, broadcasterID)
			assert.Equal(t, test.expectedBroadcasterName, broadcasterName)
		})
	}
}

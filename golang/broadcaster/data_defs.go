package main

import "github.com/carbonrobotics/crgo/rtc"

const (
	CONTROL_REQUEST  rtc.MessageType = "CONTROL_REQUEST"
	CONTROL_RESPONSE rtc.MessageType = "CONTROL_RESPONSE"
	CONTROL_STATUS   rtc.MessageType = "CONTROL_STATUS"
)

type ControlRequest struct {
	InControl bool `json:"inControl"`
}

type ControlStatus struct {
	Controller *string `json:"controller,omitempty"`
}

type ChannelIF interface {
	Label() string
	Send([]byte) error
}

type DataBusIF interface {
	OnMessage(connId string, channel ChannelIF, trusted bool, encoded []byte, respRequired bool)
	OnConnect(connId string, channel ChannelIF)
	OnDisconnect(connId string, label string)
	GetActiveController() string
}

package main

import (
	"path/filepath"

	"github.com/blackjack/webcam"
	"github.com/sirupsen/logrus"
)

type CamDev struct {
	Name    string
	BusInfo string
}

func GetCams() map[string]CamDev {
	cams := make(map[string]CamDev)
	devices, _ := filepath.Glob("/dev/video*")
	for _, dev := range devices {
		cam, err := webcam.Open(dev)
		if err != nil {
			logrus.Debugf("Failed to open %v", dev)
			continue
		}
		name, _ := cam.GetName()
		busInfo, _ := cam.GetBusInfo()
		cams[dev] = CamDev{
			Name:    name,
			BusInfo: busInfo,
		}
		cam.Close()
	}
	return cams
}

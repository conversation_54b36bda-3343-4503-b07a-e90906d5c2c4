package main

import (
	"context"
	"log"
	"os/signal"
	"syscall"

	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/sirupsen/logrus"
)

func main() {
	customFormatter := new(logrus.TextFormatter)
	customFormatter.TimestampFormat = "2006-01-02 15:04:05.000"
	customFormatter.FullTimestamp = true
	logrus.SetFormatter(customFormatter)
	logrus.SetLevel(logrus.InfoLevel)
	logging.SetLogrusOutput("broadcaster.log")

	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()
	if err := new(Broadcaster).Run(ctx); err != nil {
		log.Fatalln(err)
	}
}

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/crgo/rtc"
	rtcpb "github.com/carbonrobotics/robot/golang/generated/proto/rtc"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

// TODO talk with willow what do we want here should we use meta channel?
const serviceStatusMsg rtc.MessageType = "SERVICE_STATUS"

type serviceOnlineMsg struct {
	Online    bool `json:"online"`
	InControl bool `json:"inControl"` // Deprecated To be removed
}

var BROADCAST_MSG_CLIENT_ID string = "BROADCAST"

type MsgBroadcaster func(label string, msg json.RawMessage)
type CacheAdd func(clientId string)
type CacheLookup func(clientId string) (string, bool)

type noOpChannel struct {
	label string
}

func (c *noOpChannel) Label() string {
	return c.label
}
func (c *noOpChannel) Send([]byte) error {
	return nil
}

type BroadcasterService struct {
	rtcpb.UnimplementedBroadcasterServer
	ctx            context.Context
	controlMut     sync.Mutex
	controller     string
	msgBroadcaster MsgBroadcaster
	metaMsgId      atomic.Int32
	busId          atomic.Uint64
	reqMut         sync.Mutex
	msgReq         map[string]chan *rtcpb.RtcMessage
	msgResp        map[uint64]chan *rtcpb.RtcMessage
	respMut        sync.Mutex
	processor      ProducerProcessor
	cacheAdd       CacheAdd
	cacheLookup    CacheLookup
}

func (s *BroadcasterService) String() string {
	resp := "Connected services: "
	func() {
		s.reqMut.Lock()
		defer s.reqMut.Unlock()
		for label, _ := range s.msgReq {
			resp += fmt.Sprintf("%v, ", label)
		}
	}()
	resp += "; "
	controller := s.GetActiveController()
	if controller != "" {
		userId, found := s.cacheLookup(controller)
		if !found {
			userId = fmt.Sprintf("%v (clientId)", s.controller)
		}
		resp += fmt.Sprintf("Active Controller: %v;", userId)
	}
	return resp
}

func NewBroadcasterService(ctx context.Context, grpcServer *grpc.Server, msgBroadcaster MsgBroadcaster, processor ProducerProcessor, cacheAdd CacheAdd, cacheLookup CacheLookup) *BroadcasterService {
	service := &BroadcasterService{
		ctx:            ctx,
		msgReq:         make(map[string]chan *rtcpb.RtcMessage),
		msgResp:        make(map[uint64]chan *rtcpb.RtcMessage),
		msgBroadcaster: msgBroadcaster,
		processor:      processor,
		cacheAdd:       cacheAdd,
		cacheLookup:    cacheLookup,
	}
	rtcpb.RegisterBroadcasterServer(grpcServer, service)
	return service
}
func (s *BroadcasterService) GetActiveController() string {
	s.controlMut.Lock()
	defer s.controlMut.Unlock()
	return s.controller
}

func (s *BroadcasterService) getAuthForClient(clientId string) *rtcpb.AuthStatus {
	s.controlMut.Lock()
	defer s.controlMut.Unlock()
	return &rtcpb.AuthStatus{
		Read:  true,
		Write: clientId == s.controller,
	}
}
func (s *BroadcasterService) getServiceState(label string, clientId *string) (json.RawMessage, error) {
	// Always called with lock already held
	_, ok := s.msgReq[label]
	msg, err := rtc.NewMessageWithContent(serviceStatusMsg, serviceOnlineMsg{Online: ok, InControl: false}, rtc.SetClientId(clientId))
	if err != nil {
		return nil, err
	}
	data, err := json.Marshal(msg)
	if err != nil {
		return nil, err
	}
	return data, nil
}
func (s *BroadcasterService) broadcastServiceState(label string) {
	// Always called with lock already held
	data, err := s.getServiceState(label, &BROADCAST_MSG_CLIENT_ID)
	if err != nil {
		logrus.Warnf("failed to get state err: %v", err)
		return
	}
	s.msgBroadcaster(label, data)
}
func (s *BroadcasterService) addDataChannel(label string) chan *rtcpb.RtcMessage {
	send_chan := make(chan *rtcpb.RtcMessage, 1000) // 1 channel per controllable, need larger buffer to reduce chance of blocking
	s.reqMut.Lock()
	defer s.reqMut.Unlock()
	s.msgReq[label] = send_chan
	s.broadcastServiceState(label)
	return send_chan
}
func (s *BroadcasterService) rmDataChannel(label string, send_chan chan *rtcpb.RtcMessage) {
	s.reqMut.Lock()
	defer s.reqMut.Unlock()
	delete(s.msgReq, label)
	s.broadcastServiceState(label)
}
func (s *BroadcasterService) readFromMsgBusStream(label string, stream rtcpb.Broadcaster_MessageBusServer, wg *sync.WaitGroup) {
	defer wg.Done()
	for {
		in, err := stream.Recv()
		if err != nil {
			if err != io.EOF {
				e, ok := status.FromError(err)
				if !ok || e.Code() != codes.Canceled {
					logrus.Errorf("data stream failed streamChannel=%v err: %v", label, err)
				}
			}
			return
		}
		if in.GetId() == 0 {
			s.msgBroadcaster(label, in.Msg)
		} else {
			func() {
				s.respMut.Lock()
				defer s.respMut.Unlock()
				if ch, ok := s.msgResp[in.GetId()]; ok {
					ch <- in
				} else {
					logrus.Warnf("No-one waiting on response msgID=%v, streamChannel=%v", in.GetId(), label)
				}
			}()
		}
	}
}
func (s *BroadcasterService) MessageBus(stream rtcpb.Broadcaster_MessageBusServer) error {
	md, ok := metadata.FromIncomingContext(stream.Context())
	if !ok {
		return fmt.Errorf("Failed to get metadata")
	}
	dc_info := md.Get("data_channel")
	if len(dc_info) < 1 {
		return fmt.Errorf("Failed to get data channel from metadata")
	}
	label := dc_info[0]
	logrus.Infof("Have data connection to %v", label)
	// Set chan to take input from data channel
	send_chan := s.addDataChannel(label)
	// On exit remove this data_channel label
	defer s.rmDataChannel(label, send_chan)
	var wg sync.WaitGroup
	wg.Add(1)
	defer wg.Wait()
	go s.readFromMsgBusStream(label, stream, &wg)
	for {
		select {
		case <-stream.Context().Done():
			return nil
		case <-s.ctx.Done():
			return nil
		case toSend := <-send_chan:
			err := stream.Send(toSend)
			if err != nil {
				return err
			}
		}
	}
}
func (s *BroadcasterService) awaitResponse(respChan chan *rtcpb.RtcMessage, msgId uint64, channel ChannelIF, respRequired bool, orig []byte) {
	defer func() {
		s.respMut.Lock()
		defer s.respMut.Unlock()
		delete(s.msgResp, msgId)
	}()
	select {
	case <-time.After(time.Second * 30):
		if respRequired {
			msg, _ := rtc.NewMessageFromJSON(orig)
			logrus.Warnf("No response received msgID=%v, orig=%v", msgId, msg)
		}
	case resp := <-respChan:
		channel.Send(resp.Msg)
	}
}
func (s *BroadcasterService) sendOnBus(encoded []byte, auth *rtcpb.AuthStatus, channel ChannelIF, respRequired bool) {
	s.reqMut.Lock()
	reqChan, ok := s.msgReq[channel.Label()]
	s.reqMut.Unlock()
	if !ok {
		logrus.Warnf("No data provider found for msg on channel %v", channel.Label())
		return
	}
	msgId := s.busId.Add(1)
	if msgId == 0 {
		// Unlikely overflow case
		msgId = s.busId.Add(1)
	}
	respChan := make(chan *rtcpb.RtcMessage, 1)
	s.respMut.Lock()
	s.msgResp[msgId] = respChan
	s.respMut.Unlock()
	go s.awaitResponse(respChan, msgId, channel, respRequired, encoded)
	reqChan <- &rtcpb.RtcMessage{Id: msgId, Msg: encoded, Auth: auth}
}

func (s *BroadcasterService) OnMessage(connId string, channel ChannelIF, trusted bool, encoded []byte, respRequired bool) {
	msg, err := rtc.NewMessageFromJSON(encoded)
	if err != nil {
		logrus.Errorf("Failed to decode msg on channel %v err: %v", channel.Label(), err)
		return
	}
	if trusted && msg.ClientId != nil {
		connId = *msg.ClientId
	}

	if channel.Label() == rtc.MetaChannelLabel {
		msg.ClientId = &connId
		s.handleMetaMsg(channel, &msg)
	} else {
		if msg.Type == rtc.PROXY_CLIENT_CONNECTED {
			s.sendServiceStatus(channel, &connId)
			respRequired = false
		} else if msg.Type == rtc.PROXY_CLIENT_DISCONNECTED {
			respRequired = false
		}
		auth := s.getAuthForClient(connId)
		s.sendOnBus(encoded, auth, channel, respRequired)
	}
}
func (s *BroadcasterService) sendServiceStatus(channel ChannelIF, connId *string) {
	s.reqMut.Lock()
	stateMsg, err := s.getServiceState(channel.Label(), connId)
	s.reqMut.Unlock()
	if err != nil {
		logrus.Warnf("Failed to get service state err: %v", err)
		return
	}
	channel.Send(stateMsg)

}
func (s *BroadcasterService) OnDisconnect(connId string, label string) {
	msgId := int(-1)
	proxyMsg, err := rtc.NewMessageWithContent(rtc.PROXY_CLIENT_DISCONNECTED, nil, rtc.SetClientId(&connId), rtc.SetId(&msgId))
	if err != nil {
		logrus.Warnf("Failed to make msg err: %v", err)
	} else {
		byt, err := json.Marshal(proxyMsg)
		if err != nil {
			logrus.Warnf("Failed to marshal msg err: %v", err)
		} else {
			s.OnMessage(connId, &noOpChannel{label: label}, false, byt, false)
		}
	}
}
func (s *BroadcasterService) OnConnect(connId string, channel ChannelIF) {
	msgId := int(-1)
	proxyMsg, err := rtc.NewMessageWithContent(rtc.PROXY_CLIENT_CONNECTED, nil, rtc.SetClientId(&connId), rtc.SetId(&msgId))
	if err != nil {
		logrus.Warnf("Failed to make msg err: %v", err)
	} else {
		byt, err := json.Marshal(proxyMsg)
		if err != nil {
			logrus.Warnf("Failed to marshal msg err: %v", err)
		} else {
			s.OnMessage(connId, channel, false, byt, false)
		}
	}
}
func (s *BroadcasterService) relinquishControl(clientId *string) (bool, *rtc.ErrorMsg) {
	s.controlMut.Lock()
	defer s.controlMut.Unlock()
	if clientId != nil && *clientId == s.controller {
		s.controller = ""
		logrus.Infof("%v has relinquished control", *clientId)
		return true, nil
	} else {
		return false, &rtc.ErrorMsg{Msg: "Cannot relinquish controls as you are not currently in control"}
	}
}
func (s *BroadcasterService) takeControl(clientId *string) (bool, *rtc.ErrorMsg) {
	s.controlMut.Lock()
	defer s.controlMut.Unlock()
	s.controller = *clientId
	userId, _ := s.cacheLookup(s.controller)
	logrus.Infof("New Controller clientID=%v, userID=%v", s.controller, userId)
	return true, nil
}

func (s *BroadcasterService) sendControlStatus() {
	controller := s.GetActiveController()
	var ctlPtr *string = nil
	if controller != "" {
		ctlPtr = &controller
	}
	nextId := int(s.metaMsgId.Add(1))
	updateMsg, err := rtc.NewMessageWithContent(CONTROL_STATUS, ControlStatus{Controller: ctlPtr}, rtc.SetId(&nextId), rtc.SetClientId(&BROADCAST_MSG_CLIENT_ID))
	if err != nil {
		logrus.Error("Failed to create control status update msg")
	} else {
		encoded, err := json.Marshal(updateMsg)
		if err != nil {
			logrus.Error("Failed to marshal control status update msg")
		} else {
			s.msgBroadcaster(rtc.MetaChannelLabel, encoded)
		}
	}
}
func (s *BroadcasterService) handleMetaMsg(channel ChannelIF, msg *rtc.Message) {
	if msg.Type == CONTROL_REQUEST {
		req := ControlRequest{}
		if err := json.Unmarshal(msg.Content, &req); err != nil {
			logrus.Info("got bad control request msg")
			nextId := int(s.metaMsgId.Add(1))
			resp, err := json.Marshal(rtc.Message{Error: &rtc.ErrorMsg{Msg: "Failed to parse control request msg"}, ResponseTo: msg.Id, Id: &nextId, ClientId: msg.ClientId})
			if err != nil {
				logrus.Error("Failed to marshal response")
			} else {
				channel.Send(resp)
			}
		} else {
			sendUpdate := false
			var errMsg *rtc.ErrorMsg = nil
			if req.InControl {
				sendUpdate, errMsg = s.takeControl(msg.ClientId)
			} else {
				sendUpdate, errMsg = s.relinquishControl(msg.ClientId)
			}
			nextId := int(s.metaMsgId.Add(1))
			decodedResp := rtc.Message{Type: CONTROL_RESPONSE, Error: errMsg, ResponseTo: msg.Id, Id: &nextId, ClientId: msg.ClientId}
			if decodedResp.Error == nil {
				decodedResp.Content = []byte("{}")
			}
			resp, err := json.Marshal(decodedResp)
			if err != nil {
				logrus.Error("Failed to marshal response")
			} else {
				channel.Send(resp)
			}
			if sendUpdate {
				s.sendControlStatus()
			}
		}
	} else if msg.Type == rtc.PROXY_CLIENT_CONNECTED {
		// Send control state msg to client so they can synchronize
		controller := s.GetActiveController()
		var ctlPtr *string = nil
		if controller != "" {
			ctlPtr = &controller
		}
		nextId := int(s.metaMsgId.Add(1))
		initMsg, err := rtc.NewMessageWithContent(CONTROL_STATUS, ControlStatus{Controller: ctlPtr}, rtc.SetId(&nextId), rtc.SetClientId(msg.ClientId))
		if err == nil {
			encoded, err := json.Marshal(initMsg)
			if err == nil {
				channel.Send(encoded)
			}
		}
		if msg.ClientId != nil {
			s.cacheAdd(*msg.ClientId)
		}

	} else if msg.Type == rtc.PROXY_CLIENT_DISCONNECTED {
		sendUpdate, _ := s.relinquishControl(msg.ClientId)
		if sendUpdate {
			go s.sendControlStatus() // needs to run in Background as we can deadlock on closing clients
		}
	} else {
		logrus.Warnf("Received unknown meta channel message type='%v'", msg.Type)
	}
}

type sigClient struct {
	send_chan      chan *rtcpb.SignalingMsg
	done_chan      chan struct{}
	mediaSupported bool
}

func newSigClient(mediaSupported bool) *sigClient {
	return &sigClient{
		send_chan:      make(chan *rtcpb.SignalingMsg, 50), // 1 chan per local client, this is generally low BW so don't need large buffer
		done_chan:      make(chan struct{}),
		mediaSupported: mediaSupported,
	}
}

func (s *sigClient) Send(msg rtc.Message) {
	encoded, err := json.Marshal(msg)
	if err != nil {
		logrus.Errorf("Failed to marshal msg to local signal server err: %v", err)
		return
	}
	select {
	case s.send_chan <- &rtcpb.SignalingMsg{Msg: encoded}:
	case <-s.done_chan:
		logrus.Warn("Connection has already closed cannot send on this stream")
	}
}
func (s *BroadcasterService) readSignalStream(client *sigClient, stream rtcpb.Broadcaster_LocalSignalServerServer, wg *sync.WaitGroup) {
	defer wg.Done()
	for {
		in, err := stream.Recv()
		if err != nil {
			if err != io.EOF {
				e, ok := status.FromError(err)
				if !ok || e.Code() != codes.Canceled {
					logrus.Errorf("local signal server failed err: %v", err)
				}
			}
			return
		}
		msg := rtc.Message{}
		err = json.Unmarshal(in.Msg, &msg)
		if err != nil {
			logrus.Errorf("Failed to unmarshal rtc msg from local signal server err: %v", err)
			continue
		}
		switch msg.Type {
		case rtc.VIEW_REQUEST:
			s.processor.ProcessViewRequest(client, msg.Content, client.mediaSupported)
		case rtc.SDP:
			s.processor.ProcessSDP(client, msg.Content)
		case rtc.CANDIDATE:
			s.processor.ProcessCandidate(client, msg.Content)
		}
	}
}
func (s *BroadcasterService) LocalSignalServer(stream rtcpb.Broadcaster_LocalSignalServerServer) error {
	mediaSupported := true
	md, ok := metadata.FromIncomingContext(stream.Context())
	if !ok {
		return fmt.Errorf("Failed to get metadata")
	}
	supportInfo := md.Get("media_support")
	if len(supportInfo) >= 1 {
		mediaSupported = supportInfo[0] == "true"
	}
	client := newSigClient(mediaSupported)
	defer close(client.done_chan)
	var wg sync.WaitGroup
	wg.Add(1)
	defer wg.Wait()
	go s.readSignalStream(client, stream, &wg)
	for {
		select {
		case <-stream.Context().Done():
			return nil
		case <-s.ctx.Done():
			return nil
		case toSend := <-client.send_chan:
			err := stream.Send(toSend)
			if err != nil {
				return err
			}
		}
	}
}
func (s *BroadcasterService) GetStreamList(ctx context.Context, req *rtcpb.StreamListRequest) (*rtcpb.StreamListResponse, error) {
	return &rtcpb.StreamListResponse{Streams: s.processor.GetStreamList()}, nil
}

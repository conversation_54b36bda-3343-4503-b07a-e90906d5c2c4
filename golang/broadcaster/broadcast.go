package main

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/broadcaster/internal/gst"
	"github.com/sirupsen/logrus"

	"github.com/pion/webrtc/v4"
)

type Broadcast struct {
	verbose       bool
	broadcasterID string
	Name          string
	StreamID      string
	DeviceID      string

	rtcPeerConnections map[string]*RTCPeerConnection
	connMu             sync.Mutex
	lastUpdated        time.Time
	pipeline           *gst.Pipeline
	dataBus            DataBusIF
}

func NewBroadcast(ctx context.Context, verbose bool, broadcasterID string, name string, streamId string, deviceId string, path string, updateTime time.Time, cfg *gst.ConfigData, dataBus DataBusIF) *Broadcast {
	b := &Broadcast{
		verbose:            verbose,
		broadcasterID:      broadcasterID, // AKA hostID
		Name:               name,
		StreamID:           streamId,
		DeviceID:           deviceId,
		rtcPeerConnections: make(map[string]*RTCPeerConnection),
		lastUpdated:        updateTime,
		pipeline:           gst.CreatePipeline(cfg, fmt.Sprintf("v4l2src device=%v", path), name),
		dataBus:            dataBus,
	}
	return b
}

func (b *Broadcast) AddTrack() (int, webrtc.TrackLocal, error) {
	return b.pipeline.AddTrack()
}
func (b *Broadcast) RemoveTrack(track_id int) {
	b.pipeline.RemoveTrack(track_id)
}

func (b *Broadcast) Failed() bool {
	return b.pipeline.Failed.Load()
}

func (b *Broadcast) AddConnection(api *webrtc.API, signalClient SignalingClient, peerID string, turnUrls []string, trustedForwarder bool, mediaSupported bool, userIdLookup UserIdLookup) error {
	conn, err := NewRTCPeerConnection(
		api,
		signalClient,
		&ConnectionDetails{HostID: b.broadcasterID, PeerID: peerID, StreamID: b.StreamID},
		b,
		b.dataBus,
		mediaSupported,
		userIdLookup,
		WithVerboseLogging(b.verbose),
		WithTurnUrls(turnUrls),
		WithTrustedForwarder(trustedForwarder),
	)
	if err != nil {
		return err
	}
	b.connMu.Lock()
	defer b.connMu.Unlock()
	b.rtcPeerConnections[peerID] = conn
	return nil
}

func (b *Broadcast) AddPendingIceCandidate(peerID string, candidate *webrtc.ICECandidate) {
	b.connMu.Lock()
	defer b.connMu.Unlock()
	conn, exist := b.rtcPeerConnections[peerID]
	if !exist {
		logrus.Errorf("No connection found peerID=%v", peerID)
		return
	}
	conn.AddPendingIceCandidate(candidate)
}

func (b *Broadcast) String() string {
	str := fmt.Sprintf("Name: %s, StreamID: %s", b.Name, b.StreamID)
	b.connMu.Lock()
	defer b.connMu.Unlock()
	for _, conn := range b.rtcPeerConnections {
		str += fmt.Sprintf(" %v;", conn)
	}
	str += fmt.Sprintf(" lastUpdated: %s", b.lastUpdated.Format(time.RFC3339))
	return str
}

func (b *Broadcast) Close() {
	gst.ClosePipeline(b.pipeline)
}

func (b *Broadcast) CleanConnections() {
	b.connMu.Lock()
	defer b.connMu.Unlock()

	for peerID, conn := range b.rtcPeerConnections {
		if conn.HasEnded() {
			logrus.Infof("Dead peer connection Name=%v, peer=%v", b.Name, peerID)
			conn.CloseConn()
			delete(b.rtcPeerConnections, peerID)
		}
	}
}

func (b *Broadcast) RmConnection(peerID string) {
	b.connMu.Lock()
	defer b.connMu.Unlock()
	if conn, exist := b.rtcPeerConnections[peerID]; exist {
		if conn.IsConnected() {
			logrus.Infof("Closing peer connection peer=%v", peerID)
		}
		conn.CloseConn()
	}
	delete(b.rtcPeerConnections, peerID)
}

func (b *Broadcast) AddICECandidate(peerId string, candidate webrtc.ICECandidateInit) {
	b.connMu.Lock()
	defer b.connMu.Unlock()
	peerConnection, exist := b.rtcPeerConnections[peerId]
	if !exist {
		logrus.Warnf("Stream (%v) has no peer connection to host '%v'", b.Name, peerId)
		return
	}
	if err := peerConnection.AddICECandidate(candidate); err != nil {
		logrus.Errorf("Failed to add Ice candidate. stream=%v, host=%v, err:%v", b.Name, peerId, err)
	}
}

func (b *Broadcast) SetRemoteDescription(peerId string, sdp webrtc.SessionDescription) error {
	b.connMu.Lock()
	defer b.connMu.Unlock()
	conn, exist := b.rtcPeerConnections[peerId]
	if !exist {
		return fmt.Errorf("stream %v has no connection with host %v", b.Name, peerId)
	}
	return conn.SetRemoteDescription(sdp)
}

func (b *Broadcast) BroadcastMsg(label string, msg json.RawMessage) {
	b.connMu.Lock()
	defer b.connMu.Unlock()
	for _, conn := range b.rtcPeerConnections {
		conn.SendMsgOnChannel(label, msg)
	}
}

package carbon_nack

import (
	"github.com/pion/interceptor"
	"github.com/pion/webrtc/v4"
)

func streamSupportNack(info *interceptor.StreamInfo) bool {
	for _, fb := range info.RTCPFeedback {
		if fb.Type == "nack" && fb.Parameter == "" {
			return true
		}
	}

	return false
}
func ConfigureNack(mediaEngine *webrtc.MediaEngine, interceptorRegistry *interceptor.Registry, opts ...ResponderOption) error {
	responder, err := NewResponderInterceptor(opts...)
	if err != nil {
		return err
	}

	mediaEngine.RegisterFeedback(webrtc.RTCPFeedback{Type: "nack"}, webrtc.RTPCodecTypeVideo)
	mediaEngine.RegisterFeedback(webrtc.RTCPFeedback{Type: "nack", Parameter: "pli"}, webrtc.RTPCodecTypeVideo)
	interceptorRegistry.Add(responder)
	return nil
}

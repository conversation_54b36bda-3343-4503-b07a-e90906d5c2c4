# Broadcaster

RTC stream broadcaster

## Configuration

| Environment Variable           | Command Flag   | Default Value | Description                                                                                 |
|--------------------------------|----------------|---------------|---------------------------------------------------------------------------------------------|
| MAKA_GEN                       |                |               | Robot generation                                                                            |
| MAKA_ROBOT_NAME                |                |               | Robot name                                                                                  |
| MAKA_ROLE                      |                |               | Robot role                                                                                  |
| MAKA_ROW                       |                |               | Robot row                                                                                   |
| CARBON_IDM_HOST                |                |               | IDM Host for auth                                                                           |
| CARBON_RTC_SIGNAL_HOST         |                |               | Remote Signal Host (note including a port that is not 443, will force "ws" instead of "wss" |
| CARBON_ROBOT_USERNAME          |                |               | Username for auth                                                                           |
| CARBON_ROBOT_PASSWORD          |                |               | Password for auth                                                                           |
| BROADCASTER_AUTH_DOMAIN        | auth_domain    |               | Auth domain for direct auth (deprecated)                                                    |
| BROADCASTER_AUTH_CLIENT_ID     | auth_client_id |               | Auth client id for direct auth (deprecated)                                                 |
| BROADCASTER_AUTH_CLIENT_SECRET | auth_secret    |               | Auth client secret for direct auth (deprecated)                                             |
| BROADCASTER_AUTH_SCOPES        |                |               | Auth scopes for direct auth (deprecated)                                                    |
| SECURE_TOKEN_URL               | stu            |               | Flag to switch token url to https                                                           |
| TOKEN_FILE                     | tf             |               | Token filename for caching token to disk                                                    |

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net"
	"net/url"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/carbonrobotics/robot/golang/lib/metrics"

	"github.com/pion/mediadevices/pkg/codec"

	"github.com/carbonrobotics/robot/golang/broadcaster/carbon_nack"
	"github.com/carbonrobotics/robot/golang/broadcaster/internal/gst"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/kelseyhightower/envconfig"
	"golang.org/x/oauth2/clientcredentials"

	"github.com/carbonrobotics/crgo/build"
	"github.com/carbonrobotics/crgo/rtc"
	"github.com/carbonrobotics/crgo/websocket"
	"github.com/pion/interceptor"
	"github.com/pion/webrtc/v4"
)

var banner = strings.ReplaceAll(`
  ___                  _            _           
 | _ )_ _ ___  __ _ __| |__ __ _ __| |_ ___ _ _ 
 | _ \ '_/ _ \/ _q / _q / _/ _q (_-<  _/ -_) '_|
 |___/_| \___/\__,_\__,_\__\__,_/__/\__\___|_|`, "q", "`")

// Need to install https://github.com/pion/mediadevices#vpx

type Broadcaster struct {
	CarbonIDMHost               string   `envconfig:"CARBON_IDM_HOST"`
	CarbonRTCSignalHost         string   `envconfig:"CARBON_RTC_SIGNAL_HOST"`
	CarbonRobotUsername         string   `envconfig:"CARBON_ROBOT_USERNAME"`
	CarbonRobotPassword         string   `envconfig:"CARBON_ROBOT_PASSWORD"`
	BroadcasterAuthDomain       string   `envconfig:"BROADCASTER_AUTH_DOMAIN"`
	BroadcasterAuthClientID     string   `envconfig:"BROADCASTER_AUTH_CLIENT_ID"`
	BroadcasterAuthClientSecret string   `envconfig:"BROADCASTER_AUTH_CLIENT_SECRET"`
	BroadcasterAuthScopes       []string `envconfig:"BROADCASTER_AUTH_SCOPES"`
	SecureTokenURL              bool     `envconfig:"SECURE_TOKEN_URL"` // true makes token url use HTTPS
	TokenEndpointPort           int      `envconfig:"TOKEN_ENDPOINT_PORT"`
	TokenFile                   string   `envconfig:"TOKEN_FILE"`
	encConfig                   *gst.ConfigData
	ctx                         context.Context
	robotEnv                    environment.Robot

	turnURLs  []string
	Verbose   bool
	filterCfg *config.ConfigTree

	broadcasterID         string
	name                  string
	signalServers         []websocket.Client
	broadcastMu           sync.Mutex
	broadcasts            map[string]*Broadcast
	deviceIdToBroadcastID map[string]string
	clientIdToUID         map[string]string
	dataBus               DataBusIF
	controlsType          rtc.ControlsType

	dynamicTurnUrls  []string
	turnMu           sync.Mutex
	cuidMu           sync.Mutex
	configSubscriber *config.ConfigSubscriber
}

func (b *Broadcaster) processConfig() (err error) {
	if err := envconfig.Process("", b); err != nil {
		return err
	}
	broadcasterAuthDomain := flag.String("auth_domain", b.BroadcasterAuthDomain, "target auth domain")
	broadcasterAuthClientID := flag.String("auth_client_id", b.BroadcasterAuthClientID, "auth client id")
	broadcasterAuthClientSecret := flag.String("auth_secret", b.BroadcasterAuthClientSecret, "auth client secret")
	tokenFile := flag.String("tf", b.TokenFile, "file in which to store auth token to survive restart")
	secureTokenUrl := flag.Bool("stu", b.SecureTokenURL, "enable to set token url to use https (default is http)")
	flag.Parse()

	b.BroadcasterAuthDomain = *broadcasterAuthDomain
	b.BroadcasterAuthClientID = *broadcasterAuthClientID
	b.BroadcasterAuthClientSecret = *broadcasterAuthClientSecret
	b.TokenFile = *tokenFile
	b.SecureTokenURL = *secureTokenUrl
	b.robotEnv, err = environment.GetRobot()
	if err != nil {
		return err
	}
	return nil
}

// test points
var osHostname = os.Hostname
var timeNow = time.Now

func (b *Broadcaster) generateRobotBroadcasterID() (broadcasterID string, broadcasterName string) {
	fmt.Printf("Have role = %v", environment.GetCarbonRole())
	switch {
	case b.robotEnv.MakaRobotName == "" || b.robotEnv.MakaRole == "":
		host, err := osHostname()
		if err != nil {
			host = "unknown"
		}
		broadcasterID = fmt.Sprintf("carbon-bcr-%s-%d", host, timeNow().Unix())
		broadcasterName = host
	case b.robotEnv.IsModule():
		switch {
		case b.robotEnv.CarbonModuleID != environment.UnsetCarbonModuleID:
			broadcasterID = fmt.Sprintf("%s-%s-%s", b.robotEnv.MakaRobotName, b.robotEnv.MakaRole, b.robotEnv.CarbonModuleID)
		case b.robotEnv.CarbonModuleSerial != environment.UnsetCarbonModuleSerial:
			broadcasterID = fmt.Sprintf("%s-%s", b.robotEnv.MakaRobotName, b.robotEnv.CarbonModuleSerial)
		default:
			broadcasterID = fmt.Sprintf("%s-%s", b.robotEnv.MakaRobotName, b.robotEnv.MakaRole)
		}
	case b.robotEnv.IsRow():
		broadcasterID = fmt.Sprintf("%s-%s-%s", b.robotEnv.MakaRobotName, b.robotEnv.MakaRole, b.robotEnv.MakaRow)
	default:
		broadcasterID = fmt.Sprintf("%s-%s", b.robotEnv.MakaRobotName, b.robotEnv.MakaRole)
	}
	if broadcasterName == "" {
		broadcasterName = broadcasterID
	}
	return
}

func buildWsUrl(conf *config.ConfigTree) (string, bool) {
	prefix := "ws"
	if conf.GetChild("secure").GetBoolValue() {
		prefix = "wss"
	}
	return fmt.Sprintf("%v://%v:%v", prefix, conf.GetChild("uri").GetStringValue(), conf.GetChild("port").GetIntValue()), conf.GetChild("enabled").GetBoolValue()
}

func (b *Broadcaster) encodeCfg(cfgSubscriber *config.ConfigSubscriber) {
	encCfg := cfgSubscriber.GetConfigNode("broadcaster", "encoding")
	defer config.PreDeleteConfigTree(encCfg)
	b.encConfig = &gst.ConfigData{
		Codec:            encCfg.GetNode("codec").GetStringValue(),
		Bitrate:          int(encCfg.GetNode("bitrate").GetIntValue()),
		Framerate:        encCfg.GetNode("framerate").GetStringValue(),
		KeyframeInterval: int(encCfg.GetNode("keyframe_interval").GetIntValue()),
		Verbose:          b.Verbose,
	}
}
func (b *Broadcaster) Run(ctx context.Context) error {
	b.ctx = ctx
	b.broadcasts = make(map[string]*Broadcast)
	b.deviceIdToBroadcastID = make(map[string]string)
	b.clientIdToUID = make(map[string]string)
	b.turnURLs = make([]string, 0)
	b.signalServers = make([]websocket.Client, 0)

	fmt.Println(banner)
	log.Println("Version:", build.Version)
	log.Println("Commit:", build.Commit)
	log.Println("BuiltOn:", build.BuiltOn)
	if err := b.processConfig(); err != nil {
		return err
	}
	b.broadcasterID, b.name = b.generateRobotBroadcasterID()
	if b.robotEnv.IsRtc() {
		b.controlsType = rtc.AUTOTRACTOR_CONTROLS
	} else {
		b.controlsType = rtc.NO_CONTROLS_SUPPORTED
	}

	metrics.Serve("", 62009)
	configSubscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	configSubscriber.AddConfigTree("broadcaster", fmt.Sprintf("%s/broadcaster", config.GetComputerConfigPrefix()), "services/broadcaster.yaml")
	configSubscriber.Start()
	logrus.Info("Waiting for config")
	configSubscriber.WaitUntilReady()
	b.configSubscriber = configSubscriber
	logrus.Infof("Starting Broadcaster id=%v", b.broadcasterID)
	b.Verbose = configSubscriber.GetConfigNode("broadcaster", "verbose").GetBoolValue()
	b.filterCfg = configSubscriber.GetConfigNode("broadcaster", "device_filtering")
	var opts []grpc.ServerOption
	addr := fmt.Sprintf("0.0.0.0:%d", 61019)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return err
	}
	grpcServer := grpc.NewServer(opts...)
	b.encodeCfg(configSubscriber)

	var remoteSigServer websocket.Client = nil
	if b.CarbonRTCSignalHost != "" {
		scheme := "wss"
		_, port, _ := net.SplitHostPort(b.CarbonRTCSignalHost)
		if port != "" && port != "443" {
			scheme = "ws"
		}
		u := &url.URL{
			Scheme: scheme,
			Host:   b.CarbonRTCSignalHost,
			Path:   "/",
		}
		remoteSigServer = websocket.New(u.String(), "", 15*time.Second, b.createHBMessage)
		b.signalServers = append(b.signalServers, remoteSigServer)
		options := b.configureAuthHandler()
		if err := remoteSigServer.RegisterAuthHandler(options...); err != nil {
			return err
		}
		remoteSigServer.RegisterHandler(rtc.PEER_CONFIG_RESPONSE, b.processConfigUpdate)
	}
	lss := configSubscriber.GetConfigNode("broadcaster", "local_signal_server")
	defer config.PreDeleteConfigTree(lss)
	lsURL, enabled := buildWsUrl(lss)
	if enabled {
		ws := websocket.New(lsURL, "", 15*time.Second, b.createHBMessage)
		b.signalServers = append(b.signalServers, ws)
		// Local does not use auth
	}
	if len(b.signalServers) == 0 {
		panic("Broadcaster requires at least 1 signal server.")
	}
	for _, sigServer := range b.signalServers {
		sigServer.RegisterHandler(rtc.CONTROL_REQUEST, b.processControlRequest)
		sigServer.RegisterHandler(rtc.VIEW_REQUEST, func(c websocket.Client, rm json.RawMessage) { b.ProcessViewRequest(c, rm, true) })
		sigServer.RegisterHandler(rtc.SDP, func(c websocket.Client, rm json.RawMessage) { b.ProcessSDP(c, rm) })
		sigServer.RegisterHandler(rtc.CANDIDATE, func(c websocket.Client, rm json.RawMessage) { b.ProcessCandidate(c, rm) })
		sigServer.RegisterHandler(rtc.CLIENT_INFO_RESPONSE, func(c websocket.Client, rm json.RawMessage) { b.processClientInfoResp(c, rm) })
	}
	b.dataBus = NewBroadcasterService(b.ctx, grpcServer, b.BroadcastMsg, b, func(clientId string) { b.addClientIdToCache(clientId, b.signalServers[0]) }, b.getUserIdForClient)
	turnConf := configSubscriber.GetConfigNode("broadcaster", "turn_servers")
	defer config.PreDeleteConfigTree(turnConf)
	for _, turnUrl := range turnConf.GetChildrenNodes() {
		defer config.PreDeleteConfigTree(turnUrl)
		b.turnURLs = append(b.turnURLs, turnUrl.GetStringValue())
	}
	go func() {
		err = grpcServer.Serve(lis)
		if err != nil {
			panic(err)
		}
	}()
	defer grpcServer.GracefulStop()

	for _, sigServer := range b.signalServers {
		for {
			err := sigServer.Connect()
			if err == nil {
				break
			}
			logrus.Infof("Failed to connect err:%v", err)
			select {
			case <-ctx.Done():
				return nil
			case <-time.After(1 * time.Second):
			}
		}
		defer sigServer.Close()
	}

	go b.broadcastUpdater(ctx)

	statusTicker := time.NewTicker(5 * time.Minute)
	defer statusTicker.Stop()
	for {
		select {
		case <-ctx.Done():
			for _, broadcast := range b.broadcasts {
				broadcast.Close()
			}
			return nil
		case <-statusTicker.C:
			logrus.Info("Known Broadcasts...")
			for _, broadcast := range b.broadcasts {
				logrus.Infof("broadcast=%v", broadcast)
			}
			logrus.Info("DataBus...")
			logrus.Infof("DataBus=%v", b.dataBus)
		case <-time.After(10 * time.Second):
			for _, sigServer := range b.signalServers {
				if sigServer.IsClosed() {
					sigServer.Connect()
				}
			}
		}
	}
}

func (b *Broadcaster) configureAuthHandler() []websocket.AuthOption {
	result := make([]websocket.AuthOption, 0)
	if b.TokenEndpointPort > 0 {
		result = append(result, websocket.WithTokenEndpoint(b.TokenEndpointPort))
	}
	if len(b.TokenFile) > 0 {
		result = append(result, websocket.WithFileOption(b.TokenFile))
	}

	if len(b.BroadcasterAuthDomain) > 0 && len(b.BroadcasterAuthClientSecret) > 0 && len(b.BroadcasterAuthClientID) > 0 {
		logrus.Infof("registering auth handler for clientID=%v", b.BroadcasterAuthClientID)
		tokenUrlScheme := "http"
		if b.SecureTokenURL {
			tokenUrlScheme = "https"
		}
		clientCreds := &clientcredentials.Config{
			ClientID:     b.BroadcasterAuthClientID,
			ClientSecret: b.BroadcasterAuthClientSecret,
			Scopes:       b.BroadcasterAuthScopes,
			TokenURL:     fmt.Sprintf("%s://%s/oauth/token", tokenUrlScheme, b.BroadcasterAuthDomain),
		}
		result = append(result, websocket.WithClientCredentialsConfig(clientCreds))
	} else if len(b.CarbonIDMHost) > 0 && len(b.CarbonRobotUsername) > 0 && len(b.CarbonRobotPassword) > 0 {
		logrus.Infof("registering auth handler for username=%v", b.CarbonRobotUsername)
		userPassConf := &websocket.RobotUserPassConfig{
			IDMHost:    b.CarbonIDMHost,
			Robot:      b.robotEnv.MakaRobotName,
			Generation: b.robotEnv.MakaGen,
			Username:   b.CarbonRobotUsername,
			Password:   b.CarbonRobotPassword,
		}
		result = append(result, websocket.WithUserPassCredentials(userPassConf))
	}
	return result
}

func (b *Broadcaster) broadcastUpdater(ctx context.Context) {
	b.updateBroadcasts()
	updateTicker := time.NewTicker(15 * time.Second)
	defer updateTicker.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-updateTicker.C:
			b.updateBroadcasts()
		}

	}
}

type FilterCfg struct {
	enforceLoopback  bool
	hideDummyDevices bool
	whiteList        []*regexp.Regexp
}

func (b *Broadcaster) makeFilterCfg() *FilterCfg {
	f := &FilterCfg{
		enforceLoopback:  b.filterCfg.GetChild("enforce_loopback").GetBoolValue(),
		hideDummyDevices: b.filterCfg.GetChild("hide_dummy_devices").GetBoolValue(),
		whiteList:        make([]*regexp.Regexp, 0),
	}
	for _, pattern := range b.filterCfg.GetChild("white_list").GetChildrenNodes() {
		defer config.PreDeleteConfigTree(pattern)
		re, err := regexp.Compile(pattern.GetStringValue())
		if err != nil {
			logrus.Warnf("Skipping whitelist pattern as invalid pattern='%v', err:%v", pattern.GetStringValue(), err)
		} else {
			f.whiteList = append(f.whiteList, re)
		}
	}
	return f
}
func (b *Broadcaster) deviceFilter(filter *FilterCfg, device *CamDev) bool {
	if filter.enforceLoopback && !strings.HasPrefix(device.BusInfo, "platform:v4l2loopback-") {
		return false
	}
	if filter.hideDummyDevices && strings.HasPrefix(device.Name, "Dummy video device") {
		return false
	}
	if len(filter.whiteList) == 0 {
		// No white list is allow all
		return true
	}
	for _, re := range filter.whiteList {
		if re.MatchString(device.Name) {
			return true
		}
	}
	return false

}

func (b *Broadcaster) updateBroadcasts() {
	now := time.Now()
	filter := b.makeFilterCfg()
	b.broadcastMu.Lock()
	defer b.broadcastMu.Unlock()
	updated := false
	for path, device := range GetCams() {
		if !b.deviceFilter(filter, &device) {
			continue
		}
		streamId, exists := b.deviceIdToBroadcastID[path]
		if !exists {
			newBroadcast, err := b.createBroadcast(path, &device, now)
			if err != nil {
				if b.Verbose {
					logrus.Errorf("failed to create broadcast for device %v. err:%v", device, err)
				}
				continue
			}
			logrus.Infof("adding broadcast device=%v", newBroadcast.Name)
			b.broadcasts[newBroadcast.StreamID] = newBroadcast
			b.deviceIdToBroadcastID[path] = newBroadcast.StreamID
			updated = true
		} else {
			bc, exists := b.broadcasts[streamId]
			if exists {
				if !bc.Failed() {
					bc.lastUpdated = now
				} else {
					logrus.Warnf("Reading from video device failed, not updating streamId %v", streamId)
				}
			} else {
				logrus.Warnf("Have entry for mapping, but no broadcaster device=%v, streamId=%v", device, streamId)
				delete(b.deviceIdToBroadcastID, path)
			}
		}
	}
	for _, broadcast := range b.broadcasts {
		if broadcast.lastUpdated.Before(now) {
			logrus.Infof("Media '%v' is no longer available.", broadcast.Name)
			broadcast.Close()
			delete(b.broadcasts, broadcast.StreamID)
			delete(b.deviceIdToBroadcastID, broadcast.DeviceID)
			updated = true
		} else {
			broadcast.CleanConnections()
		}
	}
	if updated {
		for _, sigServer := range b.signalServers {
			sigServer.ForceHeartBeat()
		}
	}
}
func (b *Broadcaster) makeStreamId(device string) string {
	return fmt.Sprintf("%s:%s", b.broadcasterID, device)
}

func (b *Broadcaster) createBroadcast(path string, device *CamDev, updateTime time.Time) (*Broadcast, error) {
	streamID := b.makeStreamId(path)
	return NewBroadcast(b.ctx, b.Verbose, b.broadcasterID, device.Name, streamID, path, path, updateTime, b.encConfig, b.dataBus), nil
}

func (b *Broadcaster) createHBMessage() rtc.Message {
	content := rtc.ProducerHeartBeat{
		HostName:         b.name,
		HostID:           b.broadcasterID,
		CarbonID:         b.robotEnv.MakaRobotName,
		ControlSupported: true, // Deprecated field, leave true for backwards compatibility, remove soon
		VideoStreams:     make([]rtc.VideoStreamDef, 0),
		Controls:         b.controlsType,
		ActiveController: b.dataBus.GetActiveController(),
	}
	b.broadcastMu.Lock()
	defer b.broadcastMu.Unlock()
	for _, bc := range b.broadcasts {
		content.VideoStreams = append(content.VideoStreams, rtc.VideoStreamDef{ID: bc.StreamID, Name: bc.Name})
	}
	if b.Verbose {
		logrus.Infof("Sending HEARTBEAT: %v", content)
	}
	hbMsg, err := rtc.NewMessageWithContent(rtc.PRODUCER_HEARTBEAT, content)
	if err != nil {
		logrus.Warn("Failed to marshal HB")
	}
	return hbMsg
}
func (b *Broadcaster) processClientInfoResp(client SignalingClient, content json.RawMessage) {
	if content == nil {
		logrus.Warn("Client Info Response missing content")
		return
	}
	msg := rtc.ClientInfoResponse{}
	if err := json.Unmarshal(content, &msg); err != nil {
		logrus.Warn("Failed to unmarshal client info response")
		return
	}
	if msg.UserID == "" || msg.ClientID == "" {
		logrus.Warnf("Client Info Response missing data clientID=%v, userID=%v", msg.ClientID, msg.UserID)
		return
	}
	logrus.Infof("Caching client info clientID=%v, userID=%v", msg.ClientID, msg.UserID)
	b.cuidMu.Lock()
	defer b.cuidMu.Unlock()
	b.clientIdToUID[msg.ClientID] = msg.UserID
}

func (b *Broadcaster) addClientIdToCache(clientId string, signalClient SignalingClient) {
	b.cuidMu.Lock()
	defer b.cuidMu.Unlock()
	if _, found := b.clientIdToUID[clientId]; !found {
		msg, err := rtc.NewMessageWithContent(rtc.CLIENT_INFO_REQUEST, rtc.ClientInfoRequest{
			HostID:   b.broadcasterID,
			ClientID: clientId,
		})
		if err != nil {
			logrus.Error("Failed to marshal client info request")
			return
		}
		signalClient.Send(msg)
	}
}
func (b *Broadcaster) getUserIdForClient(clientId string) (string, bool) {
	b.cuidMu.Lock()
	defer b.cuidMu.Unlock()
	userId, found := b.clientIdToUID[clientId]
	return userId, found
}

func (b *Broadcaster) makePeerConnection(client SignalingClient, msg *rtc.ViewRequest, mediaSupported bool) error {
	b.broadcastMu.Lock()
	defer b.broadcastMu.Unlock()
	broadcast, ok := b.broadcasts[msg.StreamID]
	if !ok {
		return fmt.Errorf("Stream id %v does not exists", msg.StreamID)
	}
	broadcast.RmConnection(msg.HostID)

	settingsEngine := webrtc.SettingEngine{}
	settingsEngine.DisableSRTCPReplayProtection(true)
	settingsEngine.DisableSRTPReplayProtection(true)

	m := &webrtc.MediaEngine{}
	interceptorRegistry := &interceptor.Registry{}
	if mediaSupported {
		switch b.encConfig.Codec {
		case "vp8":
			m.RegisterCodec(codec.NewRTPVP8Codec(0).RTPCodecParameters, webrtc.RTPCodecTypeVideo)
		case "h264":
			m.RegisterCodec(codec.NewRTPH264Codec(0).RTPCodecParameters, webrtc.RTPCodecTypeVideo)
		case "av1":
			params := webrtc.RTPCodecParameters{
				RTPCodecCapability: webrtc.RTPCodecCapability{
					MimeType:     webrtc.MimeTypeAV1,
					ClockRate:    90000,
					Channels:     0,
					SDPFmtpLine:  "",
					RTCPFeedback: nil,
				},
				PayloadType: 45,
			}
			m.RegisterCodec(params, webrtc.RTPCodecTypeVideo)
		default:
			panic(fmt.Errorf("Unknown codec %v", b.encConfig.Codec))
		}
		nackTree := b.configSubscriber.GetConfigNode("broadcaster", "interceptor_configs/nack")
		defer config.PreDeleteConfigTree(nackTree)
		if err := carbon_nack.ConfigureNack(m, interceptorRegistry,
			carbon_nack.SetMaxResendCount(uint32(nackTree.GetNode("max_resend_count").GetUIntValue())),
			carbon_nack.SetResendBackoff(time.Millisecond*time.Duration(nackTree.GetNode("backoff_time").GetIntValue()))); err != nil {
			return err
		}
		if err := webrtc.ConfigureRTCPReports(interceptorRegistry); err != nil {
			return err
		}

		if err := webrtc.ConfigureTWCCHeaderExtensionSender(m, interceptorRegistry); err != nil {
			return err
		}

		if err := webrtc.ConfigureTWCCSender(m, interceptorRegistry); err != nil {
			return err
		}
	}

	api := webrtc.NewAPI(
		webrtc.WithSettingEngine(settingsEngine),
		webrtc.WithMediaEngine(m),
		webrtc.WithInterceptorRegistry(interceptorRegistry))

	b.addClientIdToCache(msg.HostID, client)
	return broadcast.AddConnection(api, client, msg.HostID, b.getTurn(), msg.TrustedForwarder, mediaSupported, b.getUserIdForClient)
}
func (b *Broadcaster) ProcessViewRequest(client SignalingClient, content json.RawMessage, mediaSupported bool) {
	msg := rtc.ViewRequest{}
	if err := json.Unmarshal(content, &msg); err != nil {
		logrus.Warn("Failed to unmarshal view request")
		return
	}
	if b.Verbose {
		logrus.Infof("View Request: %v", msg)
	}
	err := b.makePeerConnection(client, &msg, mediaSupported)
	if err != nil {
		logrus.Errorf("Failed to make peer connection err: %v", err)
	}
}

// DEPRECATED Delete soon
func (b *Broadcaster) processControlRequest(client websocket.Client, content json.RawMessage) {
	msg := rtc.ControlRequest{}
	if err := json.Unmarshal(content, &msg); err != nil {
		logrus.Warn("Failed to unmarshal control request request")
		return
	}
	if b.Verbose {
		logrus.Infof("control Request: %v", msg)
	}
	viewReq := rtc.ViewRequest{
		HostID:           msg.HostID,
		StreamID:         msg.StreamID,
		TrustedForwarder: false,
	}
	err := b.makePeerConnection(client, &viewReq, true)
	if err != nil {
		logrus.Errorf("Failed to make peer connection err: %v", err)
	}
}

func (b *Broadcaster) ProcessSDP(client SignalingClient, content json.RawMessage) {
	msg := rtc.Sdp{}
	if err := json.Unmarshal(content, &msg); err != nil {
		logrus.Errorf("invalid sdp json err: %v", err)
		return
	}
	if b.Verbose {
		logrus.Infof("SDP msg: %v", msg)
	}

	if msg.SDP.Type == webrtc.SDPTypeAnswer {
		b.broadcastMu.Lock()
		defer b.broadcastMu.Unlock()
		broadcast, exist := b.broadcasts[msg.ConnectionID]
		if !exist {
			logrus.Warnf("Got SDP message for unknown connection '%v'.", msg.ConnectionID)
			return
		}
		err := broadcast.SetRemoteDescription(msg.HostID, msg.SDP)
		if err != nil {
			logrus.Errorf("Failed to set remote description stream=%v, host=%v, err: %v", msg.ConnectionID, msg.HostID, err)
			return
		}
	}
}

func (b *Broadcaster) ProcessCandidate(client SignalingClient, content json.RawMessage) {
	msg := rtc.Candidate{}
	if err := json.Unmarshal(content, &msg); err != nil {
		logrus.Errorf("invalid candidate json err: %v", err)
		return
	}
	if b.Verbose {
		logrus.Infof("candidate msg: %v", msg)
	}
	b.broadcastMu.Lock()
	defer b.broadcastMu.Unlock()
	broadcast, exist := b.broadcasts[msg.ConnectionID]
	if !exist {
		logrus.Warnf("Got Candidate message for unknown connection '%v'.", msg.ConnectionID)
		return
	}
	broadcast.AddICECandidate(msg.HostID, *msg.Candidate)
}

func (b *Broadcaster) processConfigUpdate(client websocket.Client, content json.RawMessage) {
	msg := rtc.PeerConfigResponse{}
	if err := json.Unmarshal(content, &msg); err != nil {
		logrus.Errorf("invalid config response err: %v", err)
		return
	}
	if b.Verbose {
		logrus.Infof("peer config msg: %v", msg)
	}
	b.turnMu.Lock()
	defer b.turnMu.Unlock()
	b.dynamicTurnUrls = msg.TurnURLs
}

func (b *Broadcaster) getTurn() []string {
	b.turnMu.Lock()
	defer b.turnMu.Unlock()
	return append(b.turnURLs, b.dynamicTurnUrls...)
}

func (b *Broadcaster) BroadcastMsg(label string, msg json.RawMessage) {
	b.broadcastMu.Lock()
	defer b.broadcastMu.Unlock()
	for _, broadcast := range b.broadcasts {
		broadcast.BroadcastMsg(label, msg)
	}
}

func (b *Broadcaster) GetStreamList() map[string]string {
	streams := make(map[string]string)
	b.broadcastMu.Lock()
	defer b.broadcastMu.Unlock()
	for _, bc := range b.broadcasts {
		streams[bc.Name] = bc.StreamID
	}
	return streams
}

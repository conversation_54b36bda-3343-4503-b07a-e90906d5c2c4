// SPDX-FileCopyrightText: 2023 The Pion community <https://pion.ly>
// SPDX-License-Identifier: MIT

// Package gst provides an easy API to create an appsink pipeline
package gst

/*
#cgo pkg-config: gstreamer-1.0 gstreamer-app-1.0

#include "gst.h"

*/
import "C"

import (
	"fmt"
	"maps"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
	"unsafe"

	"github.com/pion/webrtc/v4"
	"github.com/pion/webrtc/v4/pkg/media"
	"github.com/sirupsen/logrus"
)

// nolint
func init() {
	go C.gstreamer_send_start_mainloop()
}

// Pipeline is a wrapper for a GStreamer Pipeline
type Pipeline struct {
	Pipeline   *C.GstElement
	name       string
	tracks     map[int]webrtc.TrackLocal
	trackCount int
	id         int
	codecName  string
	clockRate  float32
	trackLock  sync.Mutex
	started    atomic.Bool
	Failed     atomic.Bool
}

type ConfigData struct {
	Codec            string
	Framerate        string
	Bitrate          int
	KeyframeInterval int
	Verbose          bool
}

// nolint
var (
	pipelines     = make(map[int]*Pipeline)
	pipelinesLock sync.Mutex
	pipelineUID   = 0
)

const (
	videoClockRate = 90000
	audioClockRate = 48000
	pcmClockRate   = 8000
)

// CreatePipeline creates a GStreamer Pipeline
func CreatePipeline(config *ConfigData, pipelineSrc string, name string) *Pipeline {
	pipelineStr := "appsink name=appsink"
	var clockRate float32

	switch config.Codec {
	case "vp8":
		pipelineStr = fmt.Sprintf("%v ! videorate ! video/x-raw,framerate=%v ! vp8enc error-resilient=partitions keyframe-max-dist=%v target-bitrate=%v auto-alt-ref=true cpu-used=5 deadline=1 ! %v",
			pipelineSrc, config.Framerate, config.KeyframeInterval, config.Bitrate, pipelineStr)
		clockRate = videoClockRate

	case "vp9":
		pipelineStr = fmt.Sprintf("%v ! videorate ! video/x-raw,framerate=%v ! vp9enc keyframe-max-dist=%v target-bitrate=%v max-intra-bitrate=%v cpu-used=5 deadline=1 ! %v",
			pipelineSrc, config.Framerate, config.KeyframeInterval, config.Bitrate, config.Bitrate, pipelineStr)
		clockRate = videoClockRate

	case "h264":
		if runtime.GOARCH == "arm64" {

			pipelineStr = fmt.Sprintf("%v ! videorate ! video/x-raw,framerate=%v ! videoconvert ! nvvidconv ! nvv4l2h264enc idrinterval=%v profile=4 bitrate=%v insert-vui=1 insert-aud=1 insert-sps-pps=1 bit-packetization=1 maxperf-enable=1 vbv-size=%v ! video/x-h264,stream-format=byte-stream,alignment=(string)au ! %v",
				pipelineSrc, config.Framerate, config.KeyframeInterval, config.Bitrate, config.Bitrate, pipelineStr)
		} else {
			pipelineStr = fmt.Sprintf("%v ! videorate ! video/x-raw,framerate=%v ! videoconvert ! x264enc speed-preset=medium tune=zerolatency key-int-max=%v bitrate=%v ! video/x-h264,stream-format=byte-stream ! %v",
				pipelineSrc, config.Framerate, config.KeyframeInterval, config.Bitrate/1024, pipelineStr)
		}
		clockRate = videoClockRate

	case "av1":
		/*
			Notes AV1 support REQUIRES JP6 and a non standard libtegrav4l2.so
			See https://forums.developer.nvidia.com/t/av1-rtsp-streaming-not-working/291882/14
			use the SO available https://forums.developer.nvidia.com/uploads/short-url/7cJBqsCPr4HCqgVG4l6lt6hNVHK.zip
		*/
		if runtime.GOARCH == "arm64" {
			pipelineStr = fmt.Sprintf("%v ! videorate ! video/x-raw,framerate=%v ! videoconvert ! nvvidconv ! video/x-raw(memory:NVMM),format=NV12 ! nvv4l2av1enc bitrate=%v idrinterval=%v enable-srdo=1 ! av1parse ! rtpav1pay ! %v",
				pipelineSrc, config.Framerate, config.Bitrate, config.KeyframeInterval, pipelineStr)
		} else {
			panic("AV1 not supported")
		}
		clockRate = videoClockRate

	default:
		panic("Unhandled codec " + config.Codec) //nolint
	}
	if config.Verbose {
		fmt.Printf("Created pipeline %v\n", pipelineStr)
	}
	pipelineStrUnsafe := C.CString(pipelineStr)
	defer C.free(unsafe.Pointer(pipelineStrUnsafe))

	pipelinesLock.Lock()
	defer pipelinesLock.Unlock()
	pipelineUID++

	pipeline := &Pipeline{
		Pipeline:   C.gstreamer_send_create_pipeline(pipelineStrUnsafe, C.int(pipelineUID)),
		name:       name,
		tracks:     make(map[int]webrtc.TrackLocal),
		trackCount: 0,
		id:         pipelineUID,
		codecName:  config.Codec,
		clockRate:  clockRate,
	}
	pipeline.started.Store(false)
	pipeline.Failed.Store(false)

	pipelines[pipeline.id] = pipeline
	return pipeline
}

func ClosePipeline(pipeline *Pipeline) {
	logrus.Infof("Closing pipeline %v", pipeline.name)
	pipeline.stop()
	pipeline.removeAllTracks()
	pipelinesLock.Lock()
	defer pipelinesLock.Unlock()
	delete(pipelines, pipeline.id)
}

// start starts the GStreamer Pipeline
func (p *Pipeline) start() {
	if !p.started.Swap(true) {
		C.gstreamer_send_start_pipeline(p.Pipeline)
	}
}

// stop stops the GStreamer Pipeline
func (p *Pipeline) stop() {
	if p.started.Swap(false) {
		C.gstreamer_send_stop_pipeline(p.Pipeline)
	}
}

func (p *Pipeline) AddTrack() (int, webrtc.TrackLocal, error) {
	var track webrtc.TrackLocal
	var err error
	if p.codecName == "av1" {
		track, err = webrtc.NewTrackLocalStaticRTP(webrtc.RTPCodecCapability{MimeType: fmt.Sprintf("video/%v", p.codecName)}, "video", p.name)
	} else {
		track, err = webrtc.NewTrackLocalStaticSample(webrtc.RTPCodecCapability{MimeType: fmt.Sprintf("video/%v", p.codecName)}, "video", p.name)
	}
	if err != nil {
		return 0, nil, err
	}
	p.trackLock.Lock()
	defer p.trackLock.Unlock()
	p.trackCount++
	p.tracks[p.trackCount] = track
	p.start()
	logrus.Infof("Adding connection to stream %v, now sending to %v clients", p.name, len(p.tracks))
	return p.trackCount, track, nil
}

func (p *Pipeline) removeAllTracks() {
	p.trackLock.Lock()
	defer p.trackLock.Unlock()
	p.tracks = make(map[int]webrtc.TrackLocal)
}
func (p *Pipeline) RemoveTrack(id int) {
	p.trackLock.Lock()
	defer p.trackLock.Unlock()
	delete(p.tracks, id)
	if len(p.tracks) == 0 {
		p.stop() // Dont waste time sending info if no tracks exist to receive it
	}
	logrus.Infof("Removing connection to stream %v, now sending to %v clients", p.name, len(p.tracks))
}

//export goMarkPipelineBad
func goMarkPipelineBad(pipelineID C.int) {
	pipelinesLock.Lock()
	pipeline, ok := pipelines[int(pipelineID)]
	pipelinesLock.Unlock()
	if ok {
		pipeline.trackLock.Lock()
		defer pipeline.trackLock.Unlock()
		pipeline.Failed.Store(true)
	}
}

//export goHandlePipelineBuffer
func goHandlePipelineBuffer(buffer unsafe.Pointer, bufferLen C.int, duration C.int, pipelineID C.int) {
	pipelinesLock.Lock()
	pipeline, ok := pipelines[int(pipelineID)]
	pipelinesLock.Unlock()

	if ok {

		pipeline.trackLock.Lock()
		tracks := maps.Clone(pipeline.tracks)
		pipeline.trackLock.Unlock()
		for _, t := range tracks {
			if t == nil {
				continue
			}
			switch t.(type) {
			case *webrtc.TrackLocalStaticSample:
				sample := media.Sample{Data: C.GoBytes(buffer, bufferLen), Duration: time.Duration(duration)}
				if err := t.(*webrtc.TrackLocalStaticSample).WriteSample(sample); err != nil {
					fmt.Printf("Got Error %v trying to write sample.\n", err)
				}
			case *webrtc.TrackLocalStaticRTP:
				if _, err := t.(*webrtc.TrackLocalStaticRTP).Write(C.GoBytes(buffer, bufferLen)); err != nil {
					fmt.Printf("Got Error %v trying to write rtp bytes.\n", err)
				}
			default:
				fmt.Printf("Unkown track type")
			}
		}
	} else {
		fmt.Printf("discarding buffer, no pipeline with id %d", int(pipelineID)) //nolint
	}
	C.free(buffer)
}

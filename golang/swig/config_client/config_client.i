// This is an example on how to use SWIG for C++ GO bindings

%module config_client

%include <typemaps.i>
%include "std_string.i"
%include "stdint.i"
%include "std_vector.i"

%{
#include "config/client/cpp/config_subscriber.hpp"
#include "config/client/cpp/c_api.h"
using namespace carbon::config; // SWIG is kinda shitty and doesn't realize some of the places where namespaces are needed
%}

namespace std {
   %template(ConfigTreeVector) vector<std::shared_ptr<carbon::config::ConfigTree>>;
   %template(StringVector) vector<string>;
}

%include "config/tree/cpp/config_tree.hpp"
%include "config/client/cpp/config_subscriber.hpp"
%include "config/client/cpp/c_api.h"
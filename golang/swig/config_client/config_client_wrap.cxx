/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.2
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// source: /robot/golang/swig/config_client/config_client.i

#define SWIGMODULE config_client

#ifdef __cplusplus
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigMovePointer {
    T *ptr;
    SwigMovePointer(T *p) : ptr(p) { }
    ~SwigMovePointer() { delete ptr; }
    SwigMovePointer& operator=(SwigMovePointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigMovePointer tmp(new T(t)); pointer = tmp; return *this; }
  operator T&() const { return *pointer.ptr; }
  T *operator&() { return pointer.ptr; }
};

template <typename T> T SwigValueInit() {
  return T();
}
#endif

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif


#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>



typedef long long intgo;
typedef unsigned long long uintgo;


# if !defined(__clang__) && (defined(__i386__) || defined(__x86_64__))
#   define SWIGSTRUCTPACKED __attribute__((__packed__, __gcc_struct__))
# else
#   define SWIGSTRUCTPACKED __attribute__((__packed__))
# endif



typedef struct { char *p; intgo n; } _gostring_;
typedef struct { void* array; intgo len; intgo cap; } _goslice_;




#define swiggo_size_assert_eq(x, y, name) typedef char name[(x-y)*(x-y)*-2+1];
#define swiggo_size_assert(t, n) swiggo_size_assert_eq(sizeof(t), n, swiggo_sizeof_##t##_is_not_##n)

swiggo_size_assert(char, 1)
swiggo_size_assert(short, 2)
swiggo_size_assert(int, 4)
typedef long long swiggo_long_long;
swiggo_size_assert(swiggo_long_long, 8)
swiggo_size_assert(float, 4)
swiggo_size_assert(double, 8)

#ifdef __cplusplus
extern "C" {
#endif
extern void crosscall2(void (*fn)(void *, int), void *, int);
extern char* _cgo_topofstack(void) __attribute__ ((weak));
extern void _cgo_allocate(void *, int);
extern void _cgo_panic(void *, int);
#ifdef __cplusplus
}
#endif

static char *_swig_topofstack() {
  if (_cgo_topofstack) {
    return _cgo_topofstack();
  } else {
    return 0;
  }
}

static void _swig_gopanic(const char *p) {
  struct {
    const char *p;
  } SWIGSTRUCTPACKED a;
  a.p = p;
  crosscall2(_cgo_panic, &a, (int) sizeof a);
}




#define SWIG_contract_assert(expr, msg) \
  if (!(expr)) { _swig_gopanic(msg); } else


static _gostring_ Swig_AllocateString(const char *p, size_t l) {
  _gostring_ ret;
  ret.p = (char*)malloc(l);
  memcpy(ret.p, p, l);
  ret.n = l;
  return ret;
}


static void Swig_free(void* p) {
  free(p);
}

static void* Swig_malloc(int c) {
  return malloc(c);
}


#include <string>


#include <stdint.h>		// Use the C99 official header


#include <vector>
#include <stdexcept>


#include "config/client/cpp/config_subscriber.hpp"
#include "config/client/cpp/c_api.h"
using namespace carbon::config; // SWIG is kinda shitty and doesn't realize some of the places where namespaces are needed

SWIGINTERN std::vector< std::shared_ptr< carbon::config::ConfigTree > >::const_reference std_vector_Sl_std_shared_ptr_Sl_carbon_config_ConfigTree_Sg__Sg__get(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *self,int i){
                int size = int(self->size());
                if (i>=0 && i<size)
                    return (*self)[i];
                else
                    throw std::out_of_range("vector index out of range");
            }
SWIGINTERN void std_vector_Sl_std_shared_ptr_Sl_carbon_config_ConfigTree_Sg__Sg__set(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *self,int i,std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type const &val){
                int size = int(self->size());
                if (i>=0 && i<size)
                    (*self)[i] = val;
                else
                    throw std::out_of_range("vector index out of range");
            }
SWIGINTERN std::vector< std::string >::const_reference std_vector_Sl_std_string_Sg__get(std::vector< std::string > *self,int i){
                int size = int(self->size());
                if (i>=0 && i<size)
                    return (*self)[i];
                else
                    throw std::out_of_range("vector index out of range");
            }
SWIGINTERN void std_vector_Sl_std_string_Sg__set(std::vector< std::string > *self,int i,std::vector< std::string >::value_type const &val){
                int size = int(self->size());
                if (i>=0 && i<size)
                    (*self)[i] = val;
                else
                    throw std::out_of_range("vector index out of range");
            }
#ifdef __cplusplus
extern "C" {
#endif

void _wrap_Swig_free_config_client_7859b44c846b63b4(void *_swig_go_0) {
  void *arg1 = (void *) 0 ;
  
  arg1 = *(void **)&_swig_go_0; 
  
  Swig_free(arg1);
  
}


void *_wrap_Swig_malloc_config_client_7859b44c846b63b4(intgo _swig_go_0) {
  int arg1 ;
  void *result = 0 ;
  void *_swig_go_result;
  
  arg1 = (int)_swig_go_0; 
  
  result = (void *)Swig_malloc(arg1);
  *(void **)&_swig_go_result = (void *)result; 
  return _swig_go_result;
}


std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_wrap_new_ConfigTreeVector__SWIG_0_config_client_7859b44c846b63b4() {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *result = 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_result;
  
  
  result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *)new std::vector< std::shared_ptr< carbon::config::ConfigTree > >();
  *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *)result; 
  return _swig_go_result;
}


std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_wrap_new_ConfigTreeVector__SWIG_1_config_client_7859b44c846b63b4(long long _swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::size_type arg1 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *result = 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_result;
  
  arg1 = (size_t)_swig_go_0; 
  
  result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *)new std::vector< std::shared_ptr< carbon::config::ConfigTree > >(arg1);
  *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *)result; 
  return _swig_go_result;
}


std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_wrap_new_ConfigTreeVector__SWIG_2_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *result = 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_result;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  
  result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *)new std::vector< std::shared_ptr< carbon::config::ConfigTree > >((std::vector< std::shared_ptr< carbon::config::ConfigTree > > const &)*arg1);
  *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *)result; 
  return _swig_go_result;
}


long long _wrap_ConfigTreeVector_size_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  
  result = ((std::vector< std::shared_ptr< carbon::config::ConfigTree > > const *)arg1)->size();
  _swig_go_result = result; 
  return _swig_go_result;
}


long long _wrap_ConfigTreeVector_capacity_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  
  result = ((std::vector< std::shared_ptr< carbon::config::ConfigTree > > const *)arg1)->capacity();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_ConfigTreeVector_reserve_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0, long long _swig_go_1) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::size_type arg2 ;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  arg2 = (size_t)_swig_go_1; 
  
  (arg1)->reserve(arg2);
  
}


bool _wrap_ConfigTreeVector_isEmpty_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  
  result = (bool)((std::vector< std::shared_ptr< carbon::config::ConfigTree > > const *)arg1)->empty();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_ConfigTreeVector_clear_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  
  (arg1)->clear();
  
}


void _wrap_ConfigTreeVector_add_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0, std::shared_ptr< carbon::config::ConfigTree > *_swig_go_1) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type *arg2 = 0 ;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  arg2 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type **)&_swig_go_1; 
  
  (arg1)->push_back((std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type const &)*arg2);
  
}


std::shared_ptr< carbon::config::ConfigTree > *_wrap_ConfigTreeVector_get_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0, intgo _swig_go_1) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  int arg2 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type *result = 0 ;
  std::shared_ptr< carbon::config::ConfigTree > *_swig_go_result;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  
  try {
    result = (std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type *) &std_vector_Sl_std_shared_ptr_Sl_carbon_config_ConfigTree_Sg__Sg__get(arg1,arg2);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  *(std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type **)&_swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_ConfigTreeVector_set_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0, intgo _swig_go_1, std::shared_ptr< carbon::config::ConfigTree > *_swig_go_2) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  int arg2 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type *arg3 = 0 ;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  arg3 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > >::value_type **)&_swig_go_2; 
  
  try {
    std_vector_Sl_std_shared_ptr_Sl_carbon_config_ConfigTree_Sg__Sg__set(arg1,arg2,(std::shared_ptr< carbon::config::ConfigTree > const &)*arg3);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  
}


void _wrap_delete_ConfigTreeVector_config_client_7859b44c846b63b4(std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_0) {
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *arg1 = (std::vector< std::shared_ptr< carbon::config::ConfigTree > > *) 0 ;
  
  arg1 = *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_0; 
  
  delete arg1;
  
}


std::vector< std::string > *_wrap_new_StringVector__SWIG_0_config_client_7859b44c846b63b4() {
  std::vector< std::string > *result = 0 ;
  std::vector< std::string > *_swig_go_result;
  
  
  result = (std::vector< std::string > *)new std::vector< std::string >();
  *(std::vector< std::string > **)&_swig_go_result = (std::vector< std::string > *)result; 
  return _swig_go_result;
}


std::vector< std::string > *_wrap_new_StringVector__SWIG_1_config_client_7859b44c846b63b4(long long _swig_go_0) {
  std::vector< std::string >::size_type arg1 ;
  std::vector< std::string > *result = 0 ;
  std::vector< std::string > *_swig_go_result;
  
  arg1 = (size_t)_swig_go_0; 
  
  result = (std::vector< std::string > *)new std::vector< std::string >(arg1);
  *(std::vector< std::string > **)&_swig_go_result = (std::vector< std::string > *)result; 
  return _swig_go_result;
}


std::vector< std::string > *_wrap_new_StringVector__SWIG_2_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0) {
  std::vector< std::string > *arg1 = 0 ;
  std::vector< std::string > *result = 0 ;
  std::vector< std::string > *_swig_go_result;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  result = (std::vector< std::string > *)new std::vector< std::string >((std::vector< std::string > const &)*arg1);
  *(std::vector< std::string > **)&_swig_go_result = (std::vector< std::string > *)result; 
  return _swig_go_result;
}


long long _wrap_StringVector_size_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  std::vector< std::string >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  result = ((std::vector< std::string > const *)arg1)->size();
  _swig_go_result = result; 
  return _swig_go_result;
}


long long _wrap_StringVector_capacity_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  std::vector< std::string >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  result = ((std::vector< std::string > const *)arg1)->capacity();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_StringVector_reserve_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0, long long _swig_go_1) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  std::vector< std::string >::size_type arg2 ;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  arg2 = (size_t)_swig_go_1; 
  
  (arg1)->reserve(arg2);
  
}


bool _wrap_StringVector_isEmpty_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  result = (bool)((std::vector< std::string > const *)arg1)->empty();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_StringVector_clear_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  (arg1)->clear();
  
}


void _wrap_StringVector_add_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0, _gostring_ _swig_go_1) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  std::vector< std::string >::value_type *arg2 = 0 ;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  std::vector< std::string >::value_type arg2_str(_swig_go_1.p, _swig_go_1.n);
  arg2 = &arg2_str;
  
  
  (arg1)->push_back((std::vector< std::string >::value_type const &)*arg2);
  
}


_gostring_ _wrap_StringVector_get_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0, intgo _swig_go_1) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  int arg2 ;
  std::vector< std::string >::value_type *result = 0 ;
  _gostring_ _swig_go_result;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  
  try {
    result = (std::vector< std::string >::value_type *) &std_vector_Sl_std_string_Sg__get(arg1,arg2);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  _swig_go_result = Swig_AllocateString((*result).data(), (*result).length()); 
  return _swig_go_result;
}


void _wrap_StringVector_set_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0, intgo _swig_go_1, _gostring_ _swig_go_2) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  int arg2 ;
  std::vector< std::string >::value_type *arg3 = 0 ;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  
  std::vector< std::string >::value_type arg3_str(_swig_go_2.p, _swig_go_2.n);
  arg3 = &arg3_str;
  
  
  try {
    std_vector_Sl_std_string_Sg__set(arg1,arg2,(std::string const &)*arg3);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  
}


void _wrap_delete_StringVector_config_client_7859b44c846b63b4(std::vector< std::string > *_swig_go_0) {
  std::vector< std::string > *arg1 = (std::vector< std::string > *) 0 ;
  
  arg1 = *(std::vector< std::string > **)&_swig_go_0; 
  
  delete arg1;
  
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_0_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, carbon::config::ConfigTree *_swig_go_2) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  carbon::config::ConfigTree *arg3 = (carbon::config::ConfigTree *) 0 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  arg3 = *(carbon::config::ConfigTree **)&_swig_go_2; 
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,arg3);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_1_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_2_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, _gostring_ _swig_go_2, carbon::config::ConfigTree *_swig_go_3) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  std::string arg3 ;
  carbon::config::ConfigTree *arg4 = (carbon::config::ConfigTree *) 0 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  (&arg3)->assign(_swig_go_2.p, _swig_go_2.n); 
  arg4 = *(carbon::config::ConfigTree **)&_swig_go_3; 
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,arg3,arg4);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_3_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, _gostring_ _swig_go_2) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  std::string arg3 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  (&arg3)->assign(_swig_go_2.p, _swig_go_2.n); 
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,arg3);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_4_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, YAML::Node *_swig_go_2, std::shared_ptr< std::shared_mutex > *_swig_go_3, carbon::config::ConfigTree *_swig_go_4) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  YAML::Node *arg3 = 0 ;
  std::shared_ptr< std::shared_mutex > arg4 ;
  carbon::config::ConfigTree *arg5 = (carbon::config::ConfigTree *) 0 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  std::shared_ptr< std::shared_mutex > *argp4 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  arg3 = *(YAML::Node **)&_swig_go_2; 
  
  argp4 = (std::shared_ptr< std::shared_mutex > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< std::shared_mutex >");
  }
  arg4 = (std::shared_ptr< std::shared_mutex >)*argp4;
  
  arg5 = *(carbon::config::ConfigTree **)&_swig_go_4; 
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,(YAML::Node const &)*arg3,arg4,arg5);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_5_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, YAML::Node *_swig_go_2, std::shared_ptr< std::shared_mutex > *_swig_go_3) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  YAML::Node *arg3 = 0 ;
  std::shared_ptr< std::shared_mutex > arg4 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  std::shared_ptr< std::shared_mutex > *argp4 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  arg3 = *(YAML::Node **)&_swig_go_2; 
  
  argp4 = (std::shared_ptr< std::shared_mutex > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< std::shared_mutex >");
  }
  arg4 = (std::shared_ptr< std::shared_mutex >)*argp4;
  
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,(YAML::Node const &)*arg3,arg4);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_6_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, std::shared_ptr< std::shared_mutex > *_swig_go_2, carbon::config::ConfigTree *_swig_go_3) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  std::shared_ptr< std::shared_mutex > arg3 ;
  carbon::config::ConfigTree *arg4 = (carbon::config::ConfigTree *) 0 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  std::shared_ptr< std::shared_mutex > *argp3 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  
  argp3 = (std::shared_ptr< std::shared_mutex > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< std::shared_mutex >");
  }
  arg3 = (std::shared_ptr< std::shared_mutex >)*argp3;
  
  arg4 = *(carbon::config::ConfigTree **)&_swig_go_3; 
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,arg3,arg4);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


carbon::config::ConfigTree *_wrap_new_ConfigTree__SWIG_7_config_client_7859b44c846b63b4(_gostring_ _swig_go_0, std::shared_ptr< ConfigDefTree > *_swig_go_1, std::shared_ptr< std::shared_mutex > *_swig_go_2) {
  std::string arg1 ;
  std::shared_ptr< ConfigDefTree > arg2 ;
  std::shared_ptr< std::shared_mutex > arg3 ;
  std::shared_ptr< ConfigDefTree > *argp2 ;
  std::shared_ptr< std::shared_mutex > *argp3 ;
  carbon::config::ConfigTree *result = 0 ;
  carbon::config::ConfigTree *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (std::shared_ptr< ConfigDefTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< ConfigDefTree >");
  }
  arg2 = (std::shared_ptr< ConfigDefTree >)*argp2;
  
  
  argp3 = (std::shared_ptr< std::shared_mutex > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< std::shared_mutex >");
  }
  arg3 = (std::shared_ptr< std::shared_mutex >)*argp3;
  
  
  result = (carbon::config::ConfigTree *)new carbon::config::ConfigTree(arg1,arg2,arg3);
  *(carbon::config::ConfigTree **)&_swig_go_result = (carbon::config::ConfigTree *)result; 
  return _swig_go_result;
}


void _wrap_delete_ConfigTree_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  delete arg1;
  
}


void _wrap_ConfigTree_add_child_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, std::shared_ptr< carbon::config::ConfigTree > *_swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp2 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  argp2 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg2 = (std::shared_ptr< carbon::config::ConfigTree >)*argp2;
  
  
  (arg1)->add_child(arg2);
  
}


void _wrap_ConfigTree_remove_child_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string arg2 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  (arg1)->remove_child(arg2);
  
}


void _wrap_ConfigTree_save_to_file_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string arg2 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  (arg1)->save_to_file(arg2);
  
}


intgo _wrap_ConfigTree_register_callback_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, std::function< void (void) > *_swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  carbon::config::ConfigTreeCallback arg2 ;
  carbon::config::ConfigTreeCallback *argp2 ;
  uint32_t result;
  intgo _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  argp2 = (carbon::config::ConfigTreeCallback *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null carbon::config::ConfigTreeCallback");
  }
  arg2 = (carbon::config::ConfigTreeCallback)*argp2;
  
  
  result = (uint32_t)(arg1)->register_callback(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_ConfigTree_unregister_callback_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, intgo _swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  uint32_t arg2 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  arg2 = (uint32_t)_swig_go_1; 
  
  (arg1)->unregister_callback(arg2);
  
}


std::shared_ptr< carbon::config::ConfigTree > *_wrap_ConfigTree_get_node_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string arg2 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > result;
  std::shared_ptr< carbon::config::ConfigTree > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  result = (arg1)->get_node(arg2);
  *(std::shared_ptr< carbon::config::ConfigTree > **)&_swig_go_result = new std::shared_ptr< carbon::config::ConfigTree >(result); 
  return _swig_go_result;
}


bool _wrap_ConfigTree_has_node_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string arg2 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  result = (bool)(arg1)->has_node(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_wrap_ConfigTree_get_children_nodes_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > result;
  std::vector< std::shared_ptr< carbon::config::ConfigTree > > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = (arg1)->get_children_nodes();
  *(std::vector< std::shared_ptr< carbon::config::ConfigTree > > **)&_swig_go_result = new std::vector< std::shared_ptr< carbon::config::ConfigTree > >(result); 
  return _swig_go_result;
}


std::vector< std::string > *_wrap_ConfigTree_get_children_names_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::vector< std::string > result;
  std::vector< std::string > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = (arg1)->get_children_names();
  *(std::vector< std::string > **)&_swig_go_result = new std::vector< std::string >(result); 
  return _swig_go_result;
}


void _wrap_ConfigTree_touch_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1, long long _swig_go_2) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string arg2 ;
  uint64_t arg3 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  arg3 = (uint64_t)_swig_go_2; 
  
  (arg1)->touch(arg2,arg3);
  
}


_gostring_ _wrap_ConfigTree_get_name_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = ((carbon::config::ConfigTree const *)arg1)->get_name();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


std::shared_ptr< ConfigDefTree > *_wrap_ConfigTree_get_def_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::shared_ptr< ConfigDefTree > result;
  std::shared_ptr< ConfigDefTree > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = ((carbon::config::ConfigTree const *)arg1)->get_def();
  *(std::shared_ptr< ConfigDefTree > **)&_swig_go_result = new std::shared_ptr< ConfigDefTree >(result); 
  return _swig_go_result;
}


long long _wrap_ConfigTree_get_timestamp_ms_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  uint64_t result;
  long long _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = (uint64_t)((carbon::config::ConfigTree const *)arg1)->get_timestamp_ms();
  _swig_go_result = result; 
  return _swig_go_result;
}


std::shared_ptr< carbon::config::ConfigTree > *_wrap_ConfigTree_get_child_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string arg2 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > result;
  std::shared_ptr< carbon::config::ConfigTree > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  result = (arg1)->get_child(arg2);
  *(std::shared_ptr< carbon::config::ConfigTree > **)&_swig_go_result = new std::shared_ptr< carbon::config::ConfigTree >(result); 
  return _swig_go_result;
}


void _wrap_ConfigTree_touch_subtree_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  (arg1)->touch_subtree();
  
}


_gostring_ _wrap_ConfigTree_to_string_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = (arg1)->to_string();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


_gostring_ _wrap_ConfigTree_get_path_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  result = (arg1)->get_path();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


bool _wrap_ConfigTree_child_has_been_removed_after_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, _gostring_ _swig_go_1, long long _swig_go_2) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  std::string *arg2 = 0 ;
  uint64_t arg3 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  std::string arg2_str(_swig_go_1.p, _swig_go_1.n);
  arg2 = &arg2_str;
  
  arg3 = (uint64_t)_swig_go_2; 
  
  result = (bool)(arg1)->child_has_been_removed_after((std::string const &)*arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_ConfigTree_syncronized_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  (arg1)->syncronized();
  
}


void _wrap_ConfigTree_upgrade_config_client_7859b44c846b63b4(carbon::config::ConfigTree *_swig_go_0, std::shared_ptr< carbon::config::ConfigTree > *_swig_go_1, std::shared_ptr< carbon::config::ConfigTree > *_swig_go_2, std::vector< std::string > *_swig_go_3) {
  carbon::config::ConfigTree *arg1 = (carbon::config::ConfigTree *) 0 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg2 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg3 ;
  std::vector< std::string > *arg4 = (std::vector< std::string > *) 0 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp3 ;
  
  arg1 = *(carbon::config::ConfigTree **)&_swig_go_0; 
  
  argp2 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg2 = (std::shared_ptr< carbon::config::ConfigTree >)*argp2;
  
  
  argp3 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg3 = (std::shared_ptr< carbon::config::ConfigTree >)*argp3;
  
  arg4 = *(std::vector< std::string > **)&_swig_go_3; 
  
  (arg1)->upgrade(arg2,arg3,arg4);
  
}


bool _wrap_ConfigTree_is_protected_config_client_7859b44c846b63b4(_gostring_ _swig_go_0) {
  std::string *arg1 = 0 ;
  bool result;
  bool _swig_go_result;
  
  
  std::string arg1_str(_swig_go_0.p, _swig_go_0.n);
  arg1 = &arg1_str;
  
  
  result = (bool)carbon::config::ConfigTree::is_protected((std::string const &)*arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


_gostring_ _wrap_get_string_value_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = carbon::config::get_string_value(arg1);
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


long long _wrap_get_int_value_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  int64_t result;
  long long _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = (int64_t)carbon::config::get_int_value(arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


long long _wrap_get_uint_value_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  uint64_t result;
  long long _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = (uint64_t)carbon::config::get_uint_value(arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


bool _wrap_get_bool_value_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  bool result;
  bool _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = (bool)carbon::config::get_bool_value(arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_get_double_value_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  double result;
  double _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = (double)carbon::config::get_double_value(arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


intgo _wrap_register_callback_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0, std::function< void (void) > *_swig_go_1) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  carbon::config::ConfigTreeCallback arg2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  carbon::config::ConfigTreeCallback *argp2 ;
  uint32_t result;
  intgo _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  argp2 = (carbon::config::ConfigTreeCallback *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null carbon::config::ConfigTreeCallback");
  }
  arg2 = (carbon::config::ConfigTreeCallback)*argp2;
  
  
  result = (uint32_t)carbon::config::register_callback(arg1,arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_unregister_callback_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0, intgo _swig_go_1) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  uint32_t arg2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  arg2 = (uint32_t)_swig_go_1; 
  
  carbon::config::unregister_callback(arg1,arg2);
  
}


_gostring_ _wrap_get_name_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = carbon::config::get_name(arg1);
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


std::vector< std::string > *_wrap_get_children_names_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  std::vector< std::string > result;
  std::vector< std::string > *_swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  
  result = carbon::config::get_children_names(arg1);
  *(std::vector< std::string > **)&_swig_go_result = new std::vector< std::string >(result); 
  return _swig_go_result;
}


std::shared_ptr< carbon::config::ConfigTree > *_wrap_get_child_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0, _gostring_ _swig_go_1) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::string arg2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > result;
  std::shared_ptr< carbon::config::ConfigTree > *_swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  result = carbon::config::get_child(arg1,arg2);
  *(std::shared_ptr< carbon::config::ConfigTree > **)&_swig_go_result = new std::shared_ptr< carbon::config::ConfigTree >(result); 
  return _swig_go_result;
}


std::shared_ptr< carbon::config::ConfigTree > *_wrap_get_node_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0, _gostring_ _swig_go_1) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::string arg2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > result;
  std::shared_ptr< carbon::config::ConfigTree > *_swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  result = carbon::config::get_node(arg1,arg2);
  *(std::shared_ptr< carbon::config::ConfigTree > **)&_swig_go_result = new std::shared_ptr< carbon::config::ConfigTree >(result); 
  return _swig_go_result;
}


bool _wrap_has_node_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0, _gostring_ _swig_go_1) {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg1 ;
  std::string arg2 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp1 ;
  bool result;
  bool _swig_go_result;
  
  
  argp1 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_0;
  if (argp1 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg1 = (std::shared_ptr< carbon::config::ConfigTree >)*argp1;
  
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  result = (bool)carbon::config::has_node(arg1,arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_delete_config_tree_shared_ptr_config_client_7859b44c846b63b4(std::shared_ptr< carbon::config::ConfigTree > *_swig_go_0) {
  std::shared_ptr< carbon::config::ConfigTree > *arg1 = (std::shared_ptr< carbon::config::ConfigTree > *) 0 ;
  
  arg1 = *(std::shared_ptr< carbon::config::ConfigTree > **)&_swig_go_0; 
  
  carbon::config::delete_config_tree_shared_ptr(arg1);
  
}


_gostring_ _wrap_make_robot_local_addr_config_client_7859b44c846b63b4(short _swig_go_0) {
  uint16_t arg1 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = (uint16_t)_swig_go_0; 
  
  result = carbon::config::make_robot_local_addr(arg1);
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


_gostring_ _wrap_get_computer_config_prefix_config_client_7859b44c846b63b4() {
  std::string result;
  _gostring_ _swig_go_result;
  
  
  result = carbon::config::get_computer_config_prefix();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


_gostring_ _wrap_get_command_computer_config_prefix_config_client_7859b44c846b63b4() {
  std::string result;
  _gostring_ _swig_go_result;
  
  
  result = carbon::config::get_command_computer_config_prefix();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


_gostring_ _wrap_get_row_computer_config_prefix_config_client_7859b44c846b63b4(intgo _swig_go_0) {
  int arg1 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = (int)_swig_go_0; 
  
  result = carbon::config::get_row_computer_config_prefix(arg1);
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


short _wrap_DEFAULT_PORT_get_config_client_7859b44c846b63b4() {
  uint16_t result;
  short _swig_go_result;
  
  
  result = (uint16_t)carbon::config::DEFAULT_PORT;
  _swig_go_result = result; 
  return _swig_go_result;
}


carbon::config::ConfigSubscriber *_wrap_new_ConfigSubscriber__SWIG_0_config_client_7859b44c846b63b4(_gostring_ _swig_go_0) {
  std::string *arg1 = 0 ;
  carbon::config::ConfigSubscriber *result = 0 ;
  carbon::config::ConfigSubscriber *_swig_go_result;
  
  
  std::string arg1_str(_swig_go_0.p, _swig_go_0.n);
  arg1 = &arg1_str;
  
  
  result = (carbon::config::ConfigSubscriber *)new carbon::config::ConfigSubscriber((std::string const &)*arg1);
  *(carbon::config::ConfigSubscriber **)&_swig_go_result = (carbon::config::ConfigSubscriber *)result; 
  return _swig_go_result;
}


carbon::config::ConfigSubscriber *_wrap_new_ConfigSubscriber__SWIG_1_config_client_7859b44c846b63b4() {
  carbon::config::ConfigSubscriber *result = 0 ;
  carbon::config::ConfigSubscriber *_swig_go_result;
  
  
  result = (carbon::config::ConfigSubscriber *)new carbon::config::ConfigSubscriber();
  *(carbon::config::ConfigSubscriber **)&_swig_go_result = (carbon::config::ConfigSubscriber *)result; 
  return _swig_go_result;
}


void _wrap_delete_ConfigSubscriber_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  delete arg1;
  
}


void _wrap_ConfigSubscriber_add_config_tree__SWIG_0_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0, _gostring_ _swig_go_1, _gostring_ _swig_go_2, _gostring_ _swig_go_3, std::optional< std::string > *_swig_go_4) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  std::string arg2 ;
  std::string arg3 ;
  std::string arg4 ;
  SwigValueWrapper< std::optional< std::string > > arg5 ;
  std::optional< std::string > *argp5 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  (&arg3)->assign(_swig_go_2.p, _swig_go_2.n); 
  (&arg4)->assign(_swig_go_3.p, _swig_go_3.n); 
  
  argp5 = (std::optional< std::string > *)_swig_go_4;
  if (argp5 == NULL) {
    _swig_gopanic("Attempt to dereference null std::optional< std::string >");
  }
  arg5 = (std::optional< std::string >)*argp5;
  
  
  (arg1)->add_config_tree(arg2,arg3,arg4,arg5);
  
}


void _wrap_ConfigSubscriber_add_config_tree__SWIG_1_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0, _gostring_ _swig_go_1, _gostring_ _swig_go_2, _gostring_ _swig_go_3) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  std::string arg2 ;
  std::string arg3 ;
  std::string arg4 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  (&arg3)->assign(_swig_go_2.p, _swig_go_2.n); 
  (&arg4)->assign(_swig_go_3.p, _swig_go_3.n); 
  
  (arg1)->add_config_tree(arg2,arg3,arg4);
  
}


void _wrap_ConfigSubscriber_add_existing_config_tree_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0, _gostring_ _swig_go_1, _gostring_ _swig_go_2, std::shared_ptr< carbon::config::ConfigTree > *_swig_go_3) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  std::string arg2 ;
  std::string arg3 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > arg4 ;
  std::shared_ptr< carbon::config::ConfigTree > *argp4 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  (&arg3)->assign(_swig_go_2.p, _swig_go_2.n); 
  
  argp4 = (std::shared_ptr< carbon::config::ConfigTree > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::shared_ptr< carbon::config::ConfigTree >");
  }
  arg4 = (std::shared_ptr< carbon::config::ConfigTree >)*argp4;
  
  
  (arg1)->add_existing_config_tree(arg2,arg3,arg4);
  
}


void _wrap_ConfigSubscriber_start_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  (arg1)->start();
  
}


bool _wrap_ConfigSubscriber_refill_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  result = (bool)(arg1)->refill();
  _swig_go_result = result; 
  return _swig_go_result;
}


std::shared_ptr< carbon::config::ConfigTree > *_wrap_ConfigSubscriber_get_config_node_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0, _gostring_ _swig_go_1, _gostring_ _swig_go_2) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  std::string arg2 ;
  std::string arg3 ;
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigTree > > result;
  std::shared_ptr< carbon::config::ConfigTree > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  (&arg3)->assign(_swig_go_2.p, _swig_go_2.n); 
  
  result = (arg1)->get_config_node(arg2,arg3);
  *(std::shared_ptr< carbon::config::ConfigTree > **)&_swig_go_result = new std::shared_ptr< carbon::config::ConfigTree >(result); 
  return _swig_go_result;
}


void _wrap_ConfigSubscriber_wait_until_ready_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  (arg1)->wait_until_ready();
  
}


std::shared_ptr< ConfigClient > *_wrap_ConfigSubscriber_get_client_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  std::shared_ptr< ConfigClient > result;
  std::shared_ptr< ConfigClient > *_swig_go_result;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  result = (arg1)->get_client();
  *(std::shared_ptr< ConfigClient > **)&_swig_go_result = new std::shared_ptr< ConfigClient >(result); 
  return _swig_go_result;
}


bool _wrap_ConfigSubscriber_started_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  result = (bool)(arg1)->started();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_ConfigSubscriber_set_notification_callback_config_client_7859b44c846b63b4(carbon::config::ConfigSubscriber *_swig_go_0, std::function< void (std::string const) > *_swig_go_1) {
  carbon::config::ConfigSubscriber *arg1 = (carbon::config::ConfigSubscriber *) 0 ;
  SwigValueWrapper< std::function< void (std::string const) > > arg2 ;
  std::function< void (std::string const) > *argp2 ;
  
  arg1 = *(carbon::config::ConfigSubscriber **)&_swig_go_0; 
  
  argp2 = (std::function< void (std::string const) > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::function< void (std::string const) >");
  }
  arg2 = (std::function< void (std::string const) >)*argp2;
  
  
  (arg1)->set_notification_callback(arg2);
  
}


std::shared_ptr< carbon::config::ConfigSubscriber > *_wrap_get_global_config_subscriber_config_client_7859b44c846b63b4() {
  SwigValueWrapper< std::shared_ptr< carbon::config::ConfigSubscriber > > result;
  std::shared_ptr< carbon::config::ConfigSubscriber > *_swig_go_result;
  
  
  result = carbon::config::get_global_config_subscriber();
  *(std::shared_ptr< carbon::config::ConfigSubscriber > **)&_swig_go_result = new std::shared_ptr< carbon::config::ConfigSubscriber >(result); 
  return _swig_go_result;
}


void _wrap_PrintTest_config_client_7859b44c846b63b4() {
  PrintTest();
  
}


#ifdef __cplusplus
}
#endif


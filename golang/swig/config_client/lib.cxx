#include <stdint.h>

#include "config/client/cpp/config_subscriber.hpp"

#ifdef __cplusplus
extern "C" {
#endif

extern void go_callback(uintptr_t h);

uint32_t register_callback(uintptr_t tree_ptr, uintptr_t f_ptr) {
  std::shared_ptr<carbon::config::ConfigTree> *tree_shared_ptr = (std::shared_ptr<carbon::config::ConfigTree> *)tree_ptr;
  std::shared_ptr<carbon::config::ConfigTree> tree = (std::shared_ptr<carbon::config::ConfigTree>)*tree_shared_ptr;
  return tree->register_callback([f_ptr](){
    go_callback(f_ptr);
  });
}

#ifdef __cplusplus
}
#endif
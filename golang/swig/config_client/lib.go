package config_client

/*
#cgo CXXFLAGS: -g -Wall -Wno-unused-function -I../../.. -I../../../generated -std=c++17
#cgo LDFLAGS: -L/opt/hpcx/ompi/lib/ -L${SRCDIR}/../../../build/config/client/cpp -Wl,-rpath=${SRCDIR}/../../../build/config/client/cpp -lconfig_client_lib
#cgo LDFLAGS: -L/opt/hpcx/ompi/lib/ -L${SRCDIR}/../../../build/config/tree/cpp -Wl,-rpath=${SRCDIR}/../../../build/config/tree/cpp -lconfig_tree_lib

#include <stdint.h>

uint32_t register_callback(uintptr_t tree_ptr, uintptr_t f_ptr);
*/
import "C"

import (
	"runtime/cgo"
)

//export go_callback
func go_callback(h C.uintptr_t) {
	fn := cgo.Handle(h).Value().(func())
	fn()
}

func RegisterCallback(tree_ptr Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, h cgo.Handle) uint32 {
	return uint32(C.register_callback(C.uintptr_t(tree_ptr.Swigcptr()), GetCFunctionPointerFromHandle(h)))
}

func CreateCFunctionHandle(f func()) cgo.Handle {
	return cgo.NewHandle(f)
}

func DeleteCFunctionHandle(h cgo.Handle) {
	h.Delete()
}

func GetCFunctionPointerFromHandle(h cgo.Handle) C.uintptr_t {
	return C.uintptr_t(h)
}

/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.2
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// source: /robot/golang/swig/config_client/config_client.i

package config_client

/*
#define intgo swig_intgo
typedef void *swig_voidp;

#include <stdint.h>


typedef long long intgo;
typedef unsigned long long uintgo;



typedef struct { char *p; intgo n; } _gostring_;
typedef struct { void* array; intgo len; intgo cap; } _goslice_;


typedef long long swig_type_1;
typedef long long swig_type_2;
typedef long long swig_type_3;
typedef long long swig_type_4;
typedef long long swig_type_5;
typedef long long swig_type_6;
typedef long long swig_type_7;
typedef long long swig_type_8;
typedef _gostring_ swig_type_9;
typedef _gostring_ swig_type_10;
typedef _gostring_ swig_type_11;
typedef _gostring_ swig_type_12;
typedef _gostring_ swig_type_13;
typedef _gostring_ swig_type_14;
typedef _gostring_ swig_type_15;
typedef _gostring_ swig_type_16;
typedef _gostring_ swig_type_17;
typedef _gostring_ swig_type_18;
typedef _gostring_ swig_type_19;
typedef _gostring_ swig_type_20;
typedef _gostring_ swig_type_21;
typedef _gostring_ swig_type_22;
typedef _gostring_ swig_type_23;
typedef _gostring_ swig_type_24;
typedef _gostring_ swig_type_25;
typedef _gostring_ swig_type_26;
typedef long long swig_type_27;
typedef _gostring_ swig_type_28;
typedef long long swig_type_29;
typedef _gostring_ swig_type_30;
typedef _gostring_ swig_type_31;
typedef _gostring_ swig_type_32;
typedef _gostring_ swig_type_33;
typedef long long swig_type_34;
typedef _gostring_ swig_type_35;
typedef _gostring_ swig_type_36;
typedef long long swig_type_37;
typedef long long swig_type_38;
typedef _gostring_ swig_type_39;
typedef _gostring_ swig_type_40;
typedef _gostring_ swig_type_41;
typedef _gostring_ swig_type_42;
typedef _gostring_ swig_type_43;
typedef _gostring_ swig_type_44;
typedef _gostring_ swig_type_45;
typedef _gostring_ swig_type_46;
typedef _gostring_ swig_type_47;
typedef _gostring_ swig_type_48;
typedef _gostring_ swig_type_49;
typedef _gostring_ swig_type_50;
typedef _gostring_ swig_type_51;
typedef _gostring_ swig_type_52;
typedef _gostring_ swig_type_53;
typedef _gostring_ swig_type_54;
typedef _gostring_ swig_type_55;
typedef _gostring_ swig_type_56;
typedef _gostring_ swig_type_57;
extern void _wrap_Swig_free_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_Swig_malloc_config_client_7859b44c846b63b4(swig_intgo arg1);
extern uintptr_t _wrap_new_ConfigTreeVector__SWIG_0_config_client_7859b44c846b63b4(void);
extern uintptr_t _wrap_new_ConfigTreeVector__SWIG_1_config_client_7859b44c846b63b4(swig_type_1 arg1);
extern uintptr_t _wrap_new_ConfigTreeVector__SWIG_2_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_2 _wrap_ConfigTreeVector_size_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_3 _wrap_ConfigTreeVector_capacity_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigTreeVector_reserve_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_4 arg2);
extern _Bool _wrap_ConfigTreeVector_isEmpty_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigTreeVector_clear_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigTreeVector_add_config_client_7859b44c846b63b4(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_ConfigTreeVector_get_config_client_7859b44c846b63b4(uintptr_t arg1, swig_intgo arg2);
extern void _wrap_ConfigTreeVector_set_config_client_7859b44c846b63b4(uintptr_t arg1, swig_intgo arg2, uintptr_t arg3);
extern void _wrap_delete_ConfigTreeVector_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_new_StringVector__SWIG_0_config_client_7859b44c846b63b4(void);
extern uintptr_t _wrap_new_StringVector__SWIG_1_config_client_7859b44c846b63b4(swig_type_5 arg1);
extern uintptr_t _wrap_new_StringVector__SWIG_2_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_6 _wrap_StringVector_size_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_7 _wrap_StringVector_capacity_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_StringVector_reserve_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_8 arg2);
extern _Bool _wrap_StringVector_isEmpty_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_StringVector_clear_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_StringVector_add_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_9 arg2);
extern swig_type_10 _wrap_StringVector_get_config_client_7859b44c846b63b4(uintptr_t arg1, swig_intgo arg2);
extern void _wrap_StringVector_set_config_client_7859b44c846b63b4(uintptr_t arg1, swig_intgo arg2, swig_type_11 arg3);
extern void _wrap_delete_StringVector_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_new_ConfigTree__SWIG_0_config_client_7859b44c846b63b4(swig_type_12 arg1, uintptr_t arg2, uintptr_t arg3);
extern uintptr_t _wrap_new_ConfigTree__SWIG_1_config_client_7859b44c846b63b4(swig_type_13 arg1, uintptr_t arg2);
extern uintptr_t _wrap_new_ConfigTree__SWIG_2_config_client_7859b44c846b63b4(swig_type_14 arg1, uintptr_t arg2, swig_type_15 arg3, uintptr_t arg4);
extern uintptr_t _wrap_new_ConfigTree__SWIG_3_config_client_7859b44c846b63b4(swig_type_16 arg1, uintptr_t arg2, swig_type_17 arg3);
extern uintptr_t _wrap_new_ConfigTree__SWIG_4_config_client_7859b44c846b63b4(swig_type_18 arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4, uintptr_t arg5);
extern uintptr_t _wrap_new_ConfigTree__SWIG_5_config_client_7859b44c846b63b4(swig_type_19 arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4);
extern uintptr_t _wrap_new_ConfigTree__SWIG_6_config_client_7859b44c846b63b4(swig_type_20 arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4);
extern uintptr_t _wrap_new_ConfigTree__SWIG_7_config_client_7859b44c846b63b4(swig_type_21 arg1, uintptr_t arg2, uintptr_t arg3);
extern void _wrap_delete_ConfigTree_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigTree_add_child_config_client_7859b44c846b63b4(uintptr_t arg1, uintptr_t arg2);
extern void _wrap_ConfigTree_remove_child_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_22 arg2);
extern void _wrap_ConfigTree_save_to_file_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_23 arg2);
extern swig_intgo _wrap_ConfigTree_register_callback_config_client_7859b44c846b63b4(uintptr_t arg1, uintptr_t arg2);
extern void _wrap_ConfigTree_unregister_callback_config_client_7859b44c846b63b4(uintptr_t arg1, swig_intgo arg2);
extern uintptr_t _wrap_ConfigTree_get_node_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_24 arg2);
extern _Bool _wrap_ConfigTree_has_node_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_25 arg2);
extern uintptr_t _wrap_ConfigTree_get_children_nodes_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_ConfigTree_get_children_names_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigTree_touch_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_26 arg2, swig_type_27 arg3);
extern swig_type_28 _wrap_ConfigTree_get_name_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_ConfigTree_get_def_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_29 _wrap_ConfigTree_get_timestamp_ms_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_ConfigTree_get_child_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_30 arg2);
extern void _wrap_ConfigTree_touch_subtree_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_31 _wrap_ConfigTree_to_string_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_32 _wrap_ConfigTree_get_path_config_client_7859b44c846b63b4(uintptr_t arg1);
extern _Bool _wrap_ConfigTree_child_has_been_removed_after_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_33 arg2, swig_type_34 arg3);
extern void _wrap_ConfigTree_syncronized_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigTree_upgrade_config_client_7859b44c846b63b4(uintptr_t arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4);
extern _Bool _wrap_ConfigTree_is_protected_config_client_7859b44c846b63b4(swig_type_35 arg1);
extern swig_type_36 _wrap_get_string_value_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_37 _wrap_get_int_value_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_38 _wrap_get_uint_value_config_client_7859b44c846b63b4(uintptr_t arg1);
extern _Bool _wrap_get_bool_value_config_client_7859b44c846b63b4(uintptr_t arg1);
extern double _wrap_get_double_value_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_intgo _wrap_register_callback_config_client_7859b44c846b63b4(uintptr_t arg1, uintptr_t arg2);
extern void _wrap_unregister_callback_config_client_7859b44c846b63b4(uintptr_t arg1, swig_intgo arg2);
extern swig_type_39 _wrap_get_name_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_get_children_names_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_get_child_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_40 arg2);
extern uintptr_t _wrap_get_node_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_41 arg2);
extern _Bool _wrap_has_node_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_42 arg2);
extern void _wrap_delete_config_tree_shared_ptr_config_client_7859b44c846b63b4(uintptr_t arg1);
extern swig_type_43 _wrap_make_robot_local_addr_config_client_7859b44c846b63b4(short arg1);
extern swig_type_44 _wrap_get_computer_config_prefix_config_client_7859b44c846b63b4(void);
extern swig_type_45 _wrap_get_command_computer_config_prefix_config_client_7859b44c846b63b4(void);
extern swig_type_46 _wrap_get_row_computer_config_prefix_config_client_7859b44c846b63b4(swig_intgo arg1);
extern short _wrap_DEFAULT_PORT_get_config_client_7859b44c846b63b4(void);
extern uintptr_t _wrap_new_ConfigSubscriber__SWIG_0_config_client_7859b44c846b63b4(swig_type_47 arg1);
extern uintptr_t _wrap_new_ConfigSubscriber__SWIG_1_config_client_7859b44c846b63b4(void);
extern void _wrap_delete_ConfigSubscriber_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigSubscriber_add_config_tree__SWIG_0_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_48 arg2, swig_type_49 arg3, swig_type_50 arg4, uintptr_t arg5);
extern void _wrap_ConfigSubscriber_add_config_tree__SWIG_1_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_51 arg2, swig_type_52 arg3, swig_type_53 arg4);
extern void _wrap_ConfigSubscriber_add_existing_config_tree_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_54 arg2, swig_type_55 arg3, uintptr_t arg4);
extern void _wrap_ConfigSubscriber_start_config_client_7859b44c846b63b4(uintptr_t arg1);
extern _Bool _wrap_ConfigSubscriber_refill_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_ConfigSubscriber_get_config_node_config_client_7859b44c846b63b4(uintptr_t arg1, swig_type_56 arg2, swig_type_57 arg3);
extern void _wrap_ConfigSubscriber_wait_until_ready_config_client_7859b44c846b63b4(uintptr_t arg1);
extern uintptr_t _wrap_ConfigSubscriber_get_client_config_client_7859b44c846b63b4(uintptr_t arg1);
extern _Bool _wrap_ConfigSubscriber_started_config_client_7859b44c846b63b4(uintptr_t arg1);
extern void _wrap_ConfigSubscriber_set_notification_callback_config_client_7859b44c846b63b4(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_get_global_config_subscriber_config_client_7859b44c846b63b4(void);
extern void _wrap_PrintTest_config_client_7859b44c846b63b4(void);
#undef intgo
*/
import "C"

import "unsafe"
import _ "runtime/cgo"
import "sync"

type _ unsafe.Pointer

var Swig_escape_always_false bool
var Swig_escape_val interface{}

type _swig_fnptr *byte
type _swig_memberptr *byte

type _ sync.Mutex

type swig_gostring struct {
	p uintptr
	n int
}

func swigCopyString(s string) string {
	p := *(*swig_gostring)(unsafe.Pointer(&s))
	r := string((*[0x7fffffff]byte)(unsafe.Pointer(p.p))[:p.n])
	Swig_free(p.p)
	return r
}

func Swig_free(arg1 uintptr) {
	_swig_i_0 := arg1
	C._wrap_Swig_free_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func Swig_malloc(arg1 int) (_swig_ret uintptr) {
	var swig_r uintptr
	_swig_i_0 := arg1
	swig_r = (uintptr)(C._wrap_Swig_malloc_config_client_7859b44c846b63b4(C.swig_intgo(_swig_i_0)))
	return swig_r
}

type SwigcptrConfigTreeVector uintptr

func (p SwigcptrConfigTreeVector) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrConfigTreeVector) SwigIsConfigTreeVector() {
}

func NewConfigTreeVector__SWIG_0() (_swig_ret ConfigTreeVector) {
	var swig_r ConfigTreeVector
	swig_r = (ConfigTreeVector)(SwigcptrConfigTreeVector(C._wrap_new_ConfigTreeVector__SWIG_0_config_client_7859b44c846b63b4()))
	return swig_r
}

func NewConfigTreeVector__SWIG_1(arg1 int64) (_swig_ret ConfigTreeVector) {
	var swig_r ConfigTreeVector
	_swig_i_0 := arg1
	swig_r = (ConfigTreeVector)(SwigcptrConfigTreeVector(C._wrap_new_ConfigTreeVector__SWIG_1_config_client_7859b44c846b63b4(C.swig_type_1(_swig_i_0))))
	return swig_r
}

func NewConfigTreeVector__SWIG_2(arg1 ConfigTreeVector) (_swig_ret ConfigTreeVector) {
	var swig_r ConfigTreeVector
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (ConfigTreeVector)(SwigcptrConfigTreeVector(C._wrap_new_ConfigTreeVector__SWIG_2_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func NewConfigTreeVector(a ...interface{}) ConfigTreeVector {
	argc := len(a)
	if argc == 0 {
		return NewConfigTreeVector__SWIG_0()
	}
	if argc == 1 {
		if _, ok := a[0].(int64); !ok {
			goto check_2
		}
		return NewConfigTreeVector__SWIG_1(a[0].(int64))
	}
check_2:
	if argc == 1 {
		return NewConfigTreeVector__SWIG_2(a[0].(ConfigTreeVector))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrConfigTreeVector) Size() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_ConfigTreeVector_size_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrConfigTreeVector) Capacity() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_ConfigTreeVector_capacity_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrConfigTreeVector) Reserve(arg2 int64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_ConfigTreeVector_reserve_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_type_4(_swig_i_1))
}

func (arg1 SwigcptrConfigTreeVector) IsEmpty() (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_ConfigTreeVector_isEmpty_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrConfigTreeVector) Clear() {
	_swig_i_0 := arg1
	C._wrap_ConfigTreeVector_clear_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigTreeVector) Add(arg2 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	C._wrap_ConfigTreeVector_add_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))
}

func (arg1 SwigcptrConfigTreeVector) Get(arg2 int) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_(C._wrap_ConfigTreeVector_get_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrConfigTreeVector) Set(arg2 int, arg3 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3.Swigcptr()
	C._wrap_ConfigTreeVector_set_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1), C.uintptr_t(_swig_i_2))
}

func DeleteConfigTreeVector(arg1 ConfigTreeVector) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_ConfigTreeVector_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

type ConfigTreeVector interface {
	Swigcptr() uintptr
	SwigIsConfigTreeVector()
	Size() (_swig_ret int64)
	Capacity() (_swig_ret int64)
	Reserve(arg2 int64)
	IsEmpty() (_swig_ret bool)
	Clear()
	Add(arg2 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Get(arg2 int) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Set(arg2 int, arg3 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
}

type SwigcptrStringVector uintptr

func (p SwigcptrStringVector) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrStringVector) SwigIsStringVector() {
}

func NewStringVector__SWIG_0() (_swig_ret StringVector) {
	var swig_r StringVector
	swig_r = (StringVector)(SwigcptrStringVector(C._wrap_new_StringVector__SWIG_0_config_client_7859b44c846b63b4()))
	return swig_r
}

func NewStringVector__SWIG_1(arg1 int64) (_swig_ret StringVector) {
	var swig_r StringVector
	_swig_i_0 := arg1
	swig_r = (StringVector)(SwigcptrStringVector(C._wrap_new_StringVector__SWIG_1_config_client_7859b44c846b63b4(C.swig_type_5(_swig_i_0))))
	return swig_r
}

func NewStringVector__SWIG_2(arg1 StringVector) (_swig_ret StringVector) {
	var swig_r StringVector
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (StringVector)(SwigcptrStringVector(C._wrap_new_StringVector__SWIG_2_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func NewStringVector(a ...interface{}) StringVector {
	argc := len(a)
	if argc == 0 {
		return NewStringVector__SWIG_0()
	}
	if argc == 1 {
		if _, ok := a[0].(int64); !ok {
			goto check_2
		}
		return NewStringVector__SWIG_1(a[0].(int64))
	}
check_2:
	if argc == 1 {
		return NewStringVector__SWIG_2(a[0].(StringVector))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrStringVector) Size() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_StringVector_size_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrStringVector) Capacity() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_StringVector_capacity_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrStringVector) Reserve(arg2 int64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_StringVector_reserve_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_type_8(_swig_i_1))
}

func (arg1 SwigcptrStringVector) IsEmpty() (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_StringVector_isEmpty_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrStringVector) Clear() {
	_swig_i_0 := arg1
	C._wrap_StringVector_clear_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrStringVector) Add(arg2 string) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_StringVector_add_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_9)(unsafe.Pointer(&_swig_i_1)))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
}

func (arg1 SwigcptrStringVector) Get(arg2 int) (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r_p := C._wrap_StringVector_get_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func (arg1 SwigcptrStringVector) Set(arg2 int, arg3 string) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	C._wrap_StringVector_set_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1), *(*C.swig_type_11)(unsafe.Pointer(&_swig_i_2)))
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
}

func DeleteStringVector(arg1 StringVector) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_StringVector_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

type StringVector interface {
	Swigcptr() uintptr
	SwigIsStringVector()
	Size() (_swig_ret int64)
	Capacity() (_swig_ret int64)
	Reserve(arg2 int64)
	IsEmpty() (_swig_ret bool)
	Clear()
	Add(arg2 string)
	Get(arg2 int) (_swig_ret string)
	Set(arg2 int, arg3 string)
}

type SwigcptrConfigTree uintptr

func (p SwigcptrConfigTree) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrConfigTree) SwigIsConfigTree() {
}

func NewConfigTree__SWIG_0(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 ConfigTree) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_0_config_client_7859b44c846b63b4(*(*C.swig_type_12)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigTree__SWIG_1(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_1_config_client_7859b44c846b63b4(*(*C.swig_type_13)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigTree__SWIG_2(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 string, arg4 ConfigTree) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	_swig_i_3 := arg4.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_2_config_client_7859b44c846b63b4(*(*C.swig_type_14)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), *(*C.swig_type_15)(unsafe.Pointer(&_swig_i_2)), C.uintptr_t(_swig_i_3))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
	return swig_r
}

func NewConfigTree__SWIG_3(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 string) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_3_config_client_7859b44c846b63b4(*(*C.swig_type_16)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), *(*C.swig_type_17)(unsafe.Pointer(&_swig_i_2)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
	return swig_r
}

func NewConfigTree__SWIG_4(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 YAML_Node, arg4 Std_shared_ptr_Sl_std_shared_mutex_Sg_, arg5 ConfigTree) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	_swig_i_4 := arg5.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_4_config_client_7859b44c846b63b4(*(*C.swig_type_18)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3), C.uintptr_t(_swig_i_4))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigTree__SWIG_5(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 YAML_Node, arg4 Std_shared_ptr_Sl_std_shared_mutex_Sg_) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_5_config_client_7859b44c846b63b4(*(*C.swig_type_19)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigTree__SWIG_6(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 Std_shared_ptr_Sl_std_shared_mutex_Sg_, arg4 ConfigTree) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_6_config_client_7859b44c846b63b4(*(*C.swig_type_20)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigTree__SWIG_7(arg1 string, arg2 Std_shared_ptr_Sl_ConfigDefTree_Sg_, arg3 Std_shared_ptr_Sl_std_shared_mutex_Sg_) (_swig_ret ConfigTree) {
	var swig_r ConfigTree
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	swig_r = (ConfigTree)(SwigcptrConfigTree(C._wrap_new_ConfigTree__SWIG_7_config_client_7859b44c846b63b4(*(*C.swig_type_21)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigTree(a ...interface{}) ConfigTree {
	argc := len(a)
	if argc == 2 {
		return NewConfigTree__SWIG_1(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_))
	}
	if argc == 3 {
		if _, ok := a[2].(SwigcptrConfigTree); !ok {
			goto check_2
		}
		return NewConfigTree__SWIG_0(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(ConfigTree))
	}
check_2:
	if argc == 3 {
		if _, ok := a[2].(string); !ok {
			goto check_3
		}
		return NewConfigTree__SWIG_3(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(string))
	}
check_3:
	if argc == 3 {
		return NewConfigTree__SWIG_7(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(Std_shared_ptr_Sl_std_shared_mutex_Sg_))
	}
	if argc == 4 {
		if _, ok := a[2].(SwigcptrYAML_Node); !ok {
			goto check_5
		}
		if _, ok := a[3].(SwigcptrStd_shared_ptr_Sl_std_shared_mutex_Sg_); !ok {
			goto check_5
		}
		return NewConfigTree__SWIG_5(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(YAML_Node), a[3].(Std_shared_ptr_Sl_std_shared_mutex_Sg_))
	}
check_5:
	if argc == 4 {
		if _, ok := a[2].(SwigcptrStd_shared_ptr_Sl_std_shared_mutex_Sg_); !ok {
			goto check_6
		}
		return NewConfigTree__SWIG_6(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(Std_shared_ptr_Sl_std_shared_mutex_Sg_), a[3].(ConfigTree))
	}
check_6:
	if argc == 4 {
		return NewConfigTree__SWIG_2(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(string), a[3].(ConfigTree))
	}
	if argc == 5 {
		return NewConfigTree__SWIG_4(a[0].(string), a[1].(Std_shared_ptr_Sl_ConfigDefTree_Sg_), a[2].(YAML_Node), a[3].(Std_shared_ptr_Sl_std_shared_mutex_Sg_), a[4].(ConfigTree))
	}
	panic("No match for overloaded function call")
}

func DeleteConfigTree(arg1 ConfigTree) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_ConfigTree_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigTree) Add_child(arg2 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	C._wrap_ConfigTree_add_child_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))
}

func (arg1 SwigcptrConfigTree) Remove_child(arg2 string) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_ConfigTree_remove_child_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_22)(unsafe.Pointer(&_swig_i_1)))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
}

func (arg1 SwigcptrConfigTree) Save_to_file(arg2 string) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_ConfigTree_save_to_file_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_23)(unsafe.Pointer(&_swig_i_1)))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
}

func (arg1 SwigcptrConfigTree) Register_callback(arg2 Std_function_Sl_void_Sp_void_SP__Sg_) (_swig_ret uint) {
	var swig_r uint
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (uint)(C._wrap_ConfigTree_register_callback_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrConfigTree) Unregister_callback(arg2 uint) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_ConfigTree_unregister_callback_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1))
}

func (arg1 SwigcptrConfigTree) Get_node(arg2 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_(C._wrap_ConfigTree_get_node_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_24)(unsafe.Pointer(&_swig_i_1)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func (arg1 SwigcptrConfigTree) Has_node(arg2 string) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (bool)(C._wrap_ConfigTree_has_node_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_25)(unsafe.Pointer(&_swig_i_1))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func (arg1 SwigcptrConfigTree) Get_children_nodes() (_swig_ret ConfigTreeVector) {
	var swig_r ConfigTreeVector
	_swig_i_0 := arg1
	swig_r = (ConfigTreeVector)(SwigcptrConfigTreeVector(C._wrap_ConfigTree_get_children_nodes_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrConfigTree) Get_children_names() (_swig_ret StringVector) {
	var swig_r StringVector
	_swig_i_0 := arg1
	swig_r = (StringVector)(SwigcptrStringVector(C._wrap_ConfigTree_get_children_names_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrConfigTree) Touch(arg2 string, arg3 uint64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	C._wrap_ConfigTree_touch_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_26)(unsafe.Pointer(&_swig_i_1)), C.swig_type_27(_swig_i_2))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
}

func (arg1 SwigcptrConfigTree) Get_name() (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	swig_r_p := C._wrap_ConfigTree_get_name_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func (arg1 SwigcptrConfigTree) Get_def() (_swig_ret Std_shared_ptr_Sl_ConfigDefTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_ConfigDefTree_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_shared_ptr_Sl_ConfigDefTree_Sg_)(SwigcptrStd_shared_ptr_Sl_ConfigDefTree_Sg_(C._wrap_ConfigTree_get_def_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrConfigTree) Get_timestamp_ms() (_swig_ret uint64) {
	var swig_r uint64
	_swig_i_0 := arg1
	swig_r = (uint64)(C._wrap_ConfigTree_get_timestamp_ms_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrConfigTree) Get_child(arg2 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_(C._wrap_ConfigTree_get_child_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_30)(unsafe.Pointer(&_swig_i_1)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func (arg1 SwigcptrConfigTree) Touch_subtree() {
	_swig_i_0 := arg1
	C._wrap_ConfigTree_touch_subtree_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigTree) To_string() (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	swig_r_p := C._wrap_ConfigTree_to_string_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func (arg1 SwigcptrConfigTree) Get_path() (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	swig_r_p := C._wrap_ConfigTree_get_path_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func (arg1 SwigcptrConfigTree) Child_has_been_removed_after(arg2 string, arg3 uint64) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	swig_r = (bool)(C._wrap_ConfigTree_child_has_been_removed_after_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_33)(unsafe.Pointer(&_swig_i_1)), C.swig_type_34(_swig_i_2)))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func (arg1 SwigcptrConfigTree) Syncronized() {
	_swig_i_0 := arg1
	C._wrap_ConfigTree_syncronized_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigTree) Upgrade(arg2 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg3 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg4 StringVector) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	C._wrap_ConfigTree_upgrade_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3))
}

func ConfigTreeIs_protected(arg1 string) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_ConfigTree_is_protected_config_client_7859b44c846b63b4(*(*C.swig_type_35)(unsafe.Pointer(&_swig_i_0))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

type ConfigTree interface {
	Swigcptr() uintptr
	SwigIsConfigTree()
	Add_child(arg2 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Remove_child(arg2 string)
	Save_to_file(arg2 string)
	Register_callback(arg2 Std_function_Sl_void_Sp_void_SP__Sg_) (_swig_ret uint)
	Unregister_callback(arg2 uint)
	Get_node(arg2 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Has_node(arg2 string) (_swig_ret bool)
	Get_children_nodes() (_swig_ret ConfigTreeVector)
	Get_children_names() (_swig_ret StringVector)
	Touch(arg2 string, arg3 uint64)
	Get_name() (_swig_ret string)
	Get_def() (_swig_ret Std_shared_ptr_Sl_ConfigDefTree_Sg_)
	Get_timestamp_ms() (_swig_ret uint64)
	Get_child(arg2 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Touch_subtree()
	To_string() (_swig_ret string)
	Get_path() (_swig_ret string)
	Child_has_been_removed_after(arg2 string, arg3 uint64) (_swig_ret bool)
	Syncronized()
	Upgrade(arg2 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg3 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg4 StringVector)
}

func Get_string_value(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1.Swigcptr()
	swig_r_p := C._wrap_get_string_value_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func Get_int_value(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (int64)(C._wrap_get_int_value_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func Get_uint_value(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret uint64) {
	var swig_r uint64
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (uint64)(C._wrap_get_uint_value_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func Get_bool_value(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (bool)(C._wrap_get_bool_value_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func Get_double_value(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (float64)(C._wrap_get_double_value_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func Register_callback(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg2 Std_function_Sl_void_Sp_void_SP__Sg_) (_swig_ret uint) {
	var swig_r uint
	_swig_i_0 := arg1.Swigcptr()
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (uint)(C._wrap_register_callback_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1)))
	return swig_r
}

func Unregister_callback(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg2 uint) {
	_swig_i_0 := arg1.Swigcptr()
	_swig_i_1 := arg2
	C._wrap_unregister_callback_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1))
}

func Get_name(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1.Swigcptr()
	swig_r_p := C._wrap_get_name_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func Get_children_names(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) (_swig_ret StringVector) {
	var swig_r StringVector
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (StringVector)(SwigcptrStringVector(C._wrap_get_children_names_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func Get_child(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg2 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	_swig_i_0 := arg1.Swigcptr()
	_swig_i_1 := arg2
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_(C._wrap_get_child_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_40)(unsafe.Pointer(&_swig_i_1)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func Get_node(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg2 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	_swig_i_0 := arg1.Swigcptr()
	_swig_i_1 := arg2
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_(C._wrap_get_node_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_41)(unsafe.Pointer(&_swig_i_1)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func Has_node(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_, arg2 string) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1.Swigcptr()
	_swig_i_1 := arg2
	swig_r = (bool)(C._wrap_has_node_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_42)(unsafe.Pointer(&_swig_i_1))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func Delete_config_tree_shared_ptr(arg1 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_config_tree_shared_ptr_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func Make_robot_local_addr(arg1 uint16) (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	swig_r_p := C._wrap_make_robot_local_addr_config_client_7859b44c846b63b4(C.short(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func Get_computer_config_prefix() (_swig_ret string) {
	var swig_r string
	swig_r_p := C._wrap_get_computer_config_prefix_config_client_7859b44c846b63b4()
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func Get_command_computer_config_prefix() (_swig_ret string) {
	var swig_r string
	swig_r_p := C._wrap_get_command_computer_config_prefix_config_client_7859b44c846b63b4()
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func Get_row_computer_config_prefix(arg1 int) (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	swig_r_p := C._wrap_get_row_computer_config_prefix_config_client_7859b44c846b63b4(C.swig_intgo(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func GetDEFAULT_PORT() (_swig_ret uint16) {
	var swig_r uint16
	swig_r = (uint16)(C._wrap_DEFAULT_PORT_get_config_client_7859b44c846b63b4())
	return swig_r
}

type SwigcptrConfigSubscriber uintptr

func (p SwigcptrConfigSubscriber) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrConfigSubscriber) SwigIsConfigSubscriber() {
}

func NewConfigSubscriber__SWIG_0(arg1 string) (_swig_ret ConfigSubscriber) {
	var swig_r ConfigSubscriber
	_swig_i_0 := arg1
	swig_r = (ConfigSubscriber)(SwigcptrConfigSubscriber(C._wrap_new_ConfigSubscriber__SWIG_0_config_client_7859b44c846b63b4(*(*C.swig_type_47)(unsafe.Pointer(&_swig_i_0)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewConfigSubscriber__SWIG_1() (_swig_ret ConfigSubscriber) {
	var swig_r ConfigSubscriber
	swig_r = (ConfigSubscriber)(SwigcptrConfigSubscriber(C._wrap_new_ConfigSubscriber__SWIG_1_config_client_7859b44c846b63b4()))
	return swig_r
}

func NewConfigSubscriber(a ...interface{}) ConfigSubscriber {
	argc := len(a)
	if argc == 0 {
		return NewConfigSubscriber__SWIG_1()
	}
	if argc == 1 {
		return NewConfigSubscriber__SWIG_0(a[0].(string))
	}
	panic("No match for overloaded function call")
}

func DeleteConfigSubscriber(arg1 ConfigSubscriber) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_ConfigSubscriber_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigSubscriber) Add_config_tree__SWIG_0(arg2 string, arg3 string, arg4 string, arg5 Std_optional_Sl_std_string_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	_swig_i_4 := arg5.Swigcptr()
	C._wrap_ConfigSubscriber_add_config_tree__SWIG_0_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_48)(unsafe.Pointer(&_swig_i_1)), *(*C.swig_type_49)(unsafe.Pointer(&_swig_i_2)), *(*C.swig_type_50)(unsafe.Pointer(&_swig_i_3)), C.uintptr_t(_swig_i_4))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg4
	}
}

func (arg1 SwigcptrConfigSubscriber) Add_config_tree__SWIG_1(arg2 string, arg3 string, arg4 string) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	C._wrap_ConfigSubscriber_add_config_tree__SWIG_1_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_51)(unsafe.Pointer(&_swig_i_1)), *(*C.swig_type_52)(unsafe.Pointer(&_swig_i_2)), *(*C.swig_type_53)(unsafe.Pointer(&_swig_i_3)))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg4
	}
}

func (p SwigcptrConfigSubscriber) Add_config_tree(a ...interface{}) {
	argc := len(a)
	if argc == 3 {
		p.Add_config_tree__SWIG_1(a[0].(string), a[1].(string), a[2].(string))
		return
	}
	if argc == 4 {
		p.Add_config_tree__SWIG_0(a[0].(string), a[1].(string), a[2].(string), a[3].(Std_optional_Sl_std_string_Sg_))
		return
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrConfigSubscriber) Add_existing_config_tree(arg2 string, arg3 string, arg4 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4.Swigcptr()
	C._wrap_ConfigSubscriber_add_existing_config_tree_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_54)(unsafe.Pointer(&_swig_i_1)), *(*C.swig_type_55)(unsafe.Pointer(&_swig_i_2)), C.uintptr_t(_swig_i_3))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
}

func (arg1 SwigcptrConfigSubscriber) Start() {
	_swig_i_0 := arg1
	C._wrap_ConfigSubscriber_start_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigSubscriber) Refill() (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_ConfigSubscriber_refill_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrConfigSubscriber) Get_config_node(arg2 string, arg3 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_(C._wrap_ConfigSubscriber_get_config_node_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), *(*C.swig_type_56)(unsafe.Pointer(&_swig_i_1)), *(*C.swig_type_57)(unsafe.Pointer(&_swig_i_2)))))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg3
	}
	return swig_r
}

func (arg1 SwigcptrConfigSubscriber) Wait_until_ready() {
	_swig_i_0 := arg1
	C._wrap_ConfigSubscriber_wait_until_ready_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrConfigSubscriber) Get_client() (_swig_ret Std_shared_ptr_Sl_ConfigClient_Sg_) {
	var swig_r Std_shared_ptr_Sl_ConfigClient_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_shared_ptr_Sl_ConfigClient_Sg_)(SwigcptrStd_shared_ptr_Sl_ConfigClient_Sg_(C._wrap_ConfigSubscriber_get_client_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrConfigSubscriber) Started() (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_ConfigSubscriber_started_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrConfigSubscriber) Set_notification_callback(arg2 Std_function_Sl_void_Sp_std_string_SS_const_SP__Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	C._wrap_ConfigSubscriber_set_notification_callback_config_client_7859b44c846b63b4(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))
}

type ConfigSubscriber interface {
	Swigcptr() uintptr
	SwigIsConfigSubscriber()
	Add_config_tree(a ...interface{})
	Add_existing_config_tree(arg2 string, arg3 string, arg4 Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Start()
	Refill() (_swig_ret bool)
	Get_config_node(arg2 string, arg3 string) (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_)
	Wait_until_ready()
	Get_client() (_swig_ret Std_shared_ptr_Sl_ConfigClient_Sg_)
	Started() (_swig_ret bool)
	Set_notification_callback(arg2 Std_function_Sl_void_Sp_std_string_SS_const_SP__Sg_)
}

func Get_global_config_subscriber() (_swig_ret Std_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_) {
	var swig_r Std_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_
	swig_r = (Std_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_)(SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_(C._wrap_get_global_config_subscriber_config_client_7859b44c846b63b4()))
	return swig_r
}

func PrintTest() {
	C._wrap_PrintTest_config_client_7859b44c846b63b4()
}

type SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_ uintptr
type Std_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigSubscriber_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrYAML_Node uintptr
type YAML_Node interface {
	Swigcptr() uintptr
}

func (p SwigcptrYAML_Node) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_shared_ptr_Sl_std_shared_mutex_Sg_ uintptr
type Std_shared_ptr_Sl_std_shared_mutex_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_shared_ptr_Sl_std_shared_mutex_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_shared_ptr_Sl_ConfigDefTree_Sg_ uintptr
type Std_shared_ptr_Sl_ConfigDefTree_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_shared_ptr_Sl_ConfigDefTree_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_ uintptr
type Std_shared_ptr_Sl_carbon_config_ConfigTree_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_shared_ptr_Sl_carbon_config_ConfigTree_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_shared_ptr_Sl_ConfigClient_Sg_ uintptr
type Std_shared_ptr_Sl_ConfigClient_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_shared_ptr_Sl_ConfigClient_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_function_Sl_void_Sp_void_SP__Sg_ uintptr
type Std_function_Sl_void_Sp_void_SP__Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_function_Sl_void_Sp_void_SP__Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_optional_Sl_std_string_Sg_ uintptr
type Std_optional_Sl_std_string_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_optional_Sl_std_string_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_function_Sl_void_Sp_std_string_SS_const_SP__Sg_ uintptr
type Std_function_Sl_void_Sp_std_string_SS_const_SP__Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_function_Sl_void_Sp_std_string_SS_const_SP__Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.2
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// source: /robot/golang/swig/metrics/metrics.i

#define SWIGMODULE metrics

#ifdef __cplusplus
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigMovePointer {
    T *ptr;
    SwigMovePointer(T *p) : ptr(p) { }
    ~SwigMovePointer() { delete ptr; }
    SwigMovePointer& operator=(SwigMovePointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigMovePointer tmp(new T(t)); pointer = tmp; return *this; }
  operator T&() const { return *pointer.ptr; }
  T *operator&() { return pointer.ptr; }
};

template <typename T> T SwigValueInit() {
  return T();
}
#endif

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif


#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>



typedef long long intgo;
typedef unsigned long long uintgo;


# if !defined(__clang__) && (defined(__i386__) || defined(__x86_64__))
#   define SWIGSTRUCTPACKED __attribute__((__packed__, __gcc_struct__))
# else
#   define SWIGSTRUCTPACKED __attribute__((__packed__))
# endif



typedef struct { char *p; intgo n; } _gostring_;
typedef struct { void* array; intgo len; intgo cap; } _goslice_;




#define swiggo_size_assert_eq(x, y, name) typedef char name[(x-y)*(x-y)*-2+1];
#define swiggo_size_assert(t, n) swiggo_size_assert_eq(sizeof(t), n, swiggo_sizeof_##t##_is_not_##n)

swiggo_size_assert(char, 1)
swiggo_size_assert(short, 2)
swiggo_size_assert(int, 4)
typedef long long swiggo_long_long;
swiggo_size_assert(swiggo_long_long, 8)
swiggo_size_assert(float, 4)
swiggo_size_assert(double, 8)

#ifdef __cplusplus
extern "C" {
#endif
extern void crosscall2(void (*fn)(void *, int), void *, int);
extern char* _cgo_topofstack(void) __attribute__ ((weak));
extern void _cgo_allocate(void *, int);
extern void _cgo_panic(void *, int);
#ifdef __cplusplus
}
#endif

static char *_swig_topofstack() {
  if (_cgo_topofstack) {
    return _cgo_topofstack();
  } else {
    return 0;
  }
}

static void _swig_gopanic(const char *p) {
  struct {
    const char *p;
  } SWIGSTRUCTPACKED a;
  a.p = p;
  crosscall2(_cgo_panic, &a, (int) sizeof a);
}




#define SWIG_contract_assert(expr, msg) \
  if (!(expr)) { _swig_gopanic(msg); } else


static void Swig_free(void* p) {
  free(p);
}

static void* Swig_malloc(int c) {
  return malloc(c);
}


#include "metrics/cpp/conclusion_types.hpp"
using namespace carbon::metrics; // SWIG is kinda shitty and doesn't realize some of the places where namespaces are needed

#ifdef __cplusplus
extern "C" {
#endif

void _wrap_Swig_free_metrics_6eb5c8c3624e626d(void *_swig_go_0) {
  void *arg1 = (void *) 0 ;
  
  arg1 = *(void **)&_swig_go_0; 
  
  Swig_free(arg1);
  
}


void *_wrap_Swig_malloc_metrics_6eb5c8c3624e626d(intgo _swig_go_0) {
  int arg1 ;
  void *result = 0 ;
  void *_swig_go_result;
  
  arg1 = (int)_swig_go_0; 
  
  result = (void *)Swig_malloc(arg1);
  *(void **)&_swig_go_result = (void *)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kNotWeeding_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kNotWeeding;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kOutOfBand_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kOutOfBand;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kIntersectsWithNonShootable_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kIntersectsWithNonShootable;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kOutOfRange_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kOutOfRange;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kUnimportant_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kUnimportant;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kNotShot_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kNotShot;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kPartiallyShot_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kPartiallyShot;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kShot_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kShot;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kP2PNotFound_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kP2PNotFound;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kError_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kError;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kFlicker_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kFlicker;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kMarkedForThinning_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kMarkedForThinning;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kNotTargeted_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kNotTargeted;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kP2PMissingContext_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kP2PMissingContext;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_ConclusionType_kConclusionTypeNumber_metrics_6eb5c8c3624e626d() {
  carbon::metrics::ConclusionType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::ConclusionType::kConclusionTypeNumber;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_SchedulingFailureType_kAlreadyShot_metrics_6eb5c8c3624e626d() {
  carbon::metrics::SchedulingFailureType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::SchedulingFailureType::kAlreadyShot;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_SchedulingFailureType_kDuplicate_metrics_6eb5c8c3624e626d() {
  carbon::metrics::SchedulingFailureType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::SchedulingFailureType::kDuplicate;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_SchedulingFailureType_kNonShootable_metrics_6eb5c8c3624e626d() {
  carbon::metrics::SchedulingFailureType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::SchedulingFailureType::kNonShootable;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_SchedulingFailureType_kUntracked_metrics_6eb5c8c3624e626d() {
  carbon::metrics::SchedulingFailureType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::SchedulingFailureType::kUntracked;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_SchedulingFailureType_kNonTargetable_metrics_6eb5c8c3624e626d() {
  carbon::metrics::SchedulingFailureType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::SchedulingFailureType::kNonTargetable;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


intgo _wrap_SchedulingFailureType_kSchedulingFailureTypeCount_metrics_6eb5c8c3624e626d() {
  carbon::metrics::SchedulingFailureType result;
  intgo _swig_go_result;
  
  
  result = carbon::metrics::SchedulingFailureType::kSchedulingFailureTypeCount;
  
  _swig_go_result = (intgo)result; 
  return _swig_go_result;
}


std::string *_wrap_conclusion_type_to_string_metrics_6eb5c8c3624e626d(intgo _swig_go_0) {
  carbon::metrics::ConclusionType arg1 ;
  std::string result;
  std::string *_swig_go_result;
  
  arg1 = (carbon::metrics::ConclusionType)_swig_go_0; 
  
  result = carbon::metrics::conclusion_type_to_string(arg1);
  *(std::string **)&_swig_go_result = new std::string(result); 
  return _swig_go_result;
}


std::string *_wrap_scheduling_failure_to_string_metrics_6eb5c8c3624e626d(intgo _swig_go_0) {
  carbon::metrics::SchedulingFailureType arg1 ;
  std::string result;
  std::string *_swig_go_result;
  
  arg1 = (carbon::metrics::SchedulingFailureType)_swig_go_0; 
  
  result = carbon::metrics::scheduling_failure_to_string(arg1);
  *(std::string **)&_swig_go_result = new std::string(result); 
  return _swig_go_result;
}


bool _wrap_conclusion_is_targetable_metrics_6eb5c8c3624e626d(intgo _swig_go_0) {
  carbon::metrics::ConclusionType arg1 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = (carbon::metrics::ConclusionType)_swig_go_0; 
  
  result = (bool)carbon::metrics::conclusion_is_targetable(arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


#ifdef __cplusplus
}
#endif


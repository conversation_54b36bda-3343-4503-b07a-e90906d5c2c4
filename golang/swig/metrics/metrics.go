/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.2
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// source: /robot/golang/swig/metrics/metrics.i

package metrics

/*
#define intgo swig_intgo
typedef void *swig_voidp;

#include <stdint.h>


typedef long long intgo;
typedef unsigned long long uintgo;



typedef struct { char *p; intgo n; } _gostring_;
typedef struct { void* array; intgo len; intgo cap; } _goslice_;


extern void _wrap_Swig_free_metrics_6eb5c8c3624e626d(uintptr_t arg1);
extern uintptr_t _wrap_Swig_malloc_metrics_6eb5c8c3624e626d(swig_intgo arg1);
extern swig_intgo _wrap_ConclusionType_kNotWeeding_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kOutOfBand_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kIntersectsWithNonShootable_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kOutOfRange_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kUnimportant_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kNotShot_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kPartiallyShot_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kShot_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kP2PNotFound_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kError_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kFlicker_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kMarkedForThinning_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kNotTargeted_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kP2PMissingContext_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_ConclusionType_kConclusionTypeNumber_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_SchedulingFailureType_kAlreadyShot_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_SchedulingFailureType_kDuplicate_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_SchedulingFailureType_kNonShootable_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_SchedulingFailureType_kUntracked_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_SchedulingFailureType_kNonTargetable_metrics_6eb5c8c3624e626d(void);
extern swig_intgo _wrap_SchedulingFailureType_kSchedulingFailureTypeCount_metrics_6eb5c8c3624e626d(void);
extern uintptr_t _wrap_conclusion_type_to_string_metrics_6eb5c8c3624e626d(swig_intgo arg1);
extern uintptr_t _wrap_scheduling_failure_to_string_metrics_6eb5c8c3624e626d(swig_intgo arg1);
extern _Bool _wrap_conclusion_is_targetable_metrics_6eb5c8c3624e626d(swig_intgo arg1);
#undef intgo
*/
import "C"

import "unsafe"
import _ "runtime/cgo"
import "sync"

type _ unsafe.Pointer

var Swig_escape_always_false bool
var Swig_escape_val interface{}

type _swig_fnptr *byte
type _swig_memberptr *byte

type _ sync.Mutex

func Swig_free(arg1 uintptr) {
	_swig_i_0 := arg1
	C._wrap_Swig_free_metrics_6eb5c8c3624e626d(C.uintptr_t(_swig_i_0))
}

func Swig_malloc(arg1 int) (_swig_ret uintptr) {
	var swig_r uintptr
	_swig_i_0 := arg1
	swig_r = (uintptr)(C._wrap_Swig_malloc_metrics_6eb5c8c3624e626d(C.swig_intgo(_swig_i_0)))
	return swig_r
}

type CarbonMetricsConclusionType int

func _swig_getConclusionType_kNotWeeding() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kNotWeeding_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kNotWeeding CarbonMetricsConclusionType = _swig_getConclusionType_kNotWeeding()

func _swig_getConclusionType_kOutOfBand() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kOutOfBand_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kOutOfBand CarbonMetricsConclusionType = _swig_getConclusionType_kOutOfBand()

func _swig_getConclusionType_kIntersectsWithNonShootable() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kIntersectsWithNonShootable_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kIntersectsWithNonShootable CarbonMetricsConclusionType = _swig_getConclusionType_kIntersectsWithNonShootable()

func _swig_getConclusionType_kOutOfRange() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kOutOfRange_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kOutOfRange CarbonMetricsConclusionType = _swig_getConclusionType_kOutOfRange()

func _swig_getConclusionType_kUnimportant() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kUnimportant_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kUnimportant CarbonMetricsConclusionType = _swig_getConclusionType_kUnimportant()

func _swig_getConclusionType_kNotShot() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kNotShot_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kNotShot CarbonMetricsConclusionType = _swig_getConclusionType_kNotShot()

func _swig_getConclusionType_kPartiallyShot() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kPartiallyShot_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kPartiallyShot CarbonMetricsConclusionType = _swig_getConclusionType_kPartiallyShot()

func _swig_getConclusionType_kShot() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kShot_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kShot CarbonMetricsConclusionType = _swig_getConclusionType_kShot()

func _swig_getConclusionType_kP2PNotFound() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kP2PNotFound_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kP2PNotFound CarbonMetricsConclusionType = _swig_getConclusionType_kP2PNotFound()

func _swig_getConclusionType_kError() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kError_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kError CarbonMetricsConclusionType = _swig_getConclusionType_kError()

func _swig_getConclusionType_kFlicker() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kFlicker_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kFlicker CarbonMetricsConclusionType = _swig_getConclusionType_kFlicker()

func _swig_getConclusionType_kMarkedForThinning() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kMarkedForThinning_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kMarkedForThinning CarbonMetricsConclusionType = _swig_getConclusionType_kMarkedForThinning()

func _swig_getConclusionType_kNotTargeted() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kNotTargeted_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kNotTargeted CarbonMetricsConclusionType = _swig_getConclusionType_kNotTargeted()

func _swig_getConclusionType_kP2PMissingContext() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kP2PMissingContext_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kP2PMissingContext CarbonMetricsConclusionType = _swig_getConclusionType_kP2PMissingContext()

func _swig_getConclusionType_kConclusionTypeNumber() (_swig_ret CarbonMetricsConclusionType) {
	var swig_r CarbonMetricsConclusionType
	swig_r = (CarbonMetricsConclusionType)(C._wrap_ConclusionType_kConclusionTypeNumber_metrics_6eb5c8c3624e626d())
	return swig_r
}

var ConclusionType_kConclusionTypeNumber CarbonMetricsConclusionType = _swig_getConclusionType_kConclusionTypeNumber()

type CarbonMetricsSchedulingFailureType int

func _swig_getSchedulingFailureType_kAlreadyShot() (_swig_ret CarbonMetricsSchedulingFailureType) {
	var swig_r CarbonMetricsSchedulingFailureType
	swig_r = (CarbonMetricsSchedulingFailureType)(C._wrap_SchedulingFailureType_kAlreadyShot_metrics_6eb5c8c3624e626d())
	return swig_r
}

var SchedulingFailureType_kAlreadyShot CarbonMetricsSchedulingFailureType = _swig_getSchedulingFailureType_kAlreadyShot()

func _swig_getSchedulingFailureType_kDuplicate() (_swig_ret CarbonMetricsSchedulingFailureType) {
	var swig_r CarbonMetricsSchedulingFailureType
	swig_r = (CarbonMetricsSchedulingFailureType)(C._wrap_SchedulingFailureType_kDuplicate_metrics_6eb5c8c3624e626d())
	return swig_r
}

var SchedulingFailureType_kDuplicate CarbonMetricsSchedulingFailureType = _swig_getSchedulingFailureType_kDuplicate()

func _swig_getSchedulingFailureType_kNonShootable() (_swig_ret CarbonMetricsSchedulingFailureType) {
	var swig_r CarbonMetricsSchedulingFailureType
	swig_r = (CarbonMetricsSchedulingFailureType)(C._wrap_SchedulingFailureType_kNonShootable_metrics_6eb5c8c3624e626d())
	return swig_r
}

var SchedulingFailureType_kNonShootable CarbonMetricsSchedulingFailureType = _swig_getSchedulingFailureType_kNonShootable()

func _swig_getSchedulingFailureType_kUntracked() (_swig_ret CarbonMetricsSchedulingFailureType) {
	var swig_r CarbonMetricsSchedulingFailureType
	swig_r = (CarbonMetricsSchedulingFailureType)(C._wrap_SchedulingFailureType_kUntracked_metrics_6eb5c8c3624e626d())
	return swig_r
}

var SchedulingFailureType_kUntracked CarbonMetricsSchedulingFailureType = _swig_getSchedulingFailureType_kUntracked()

func _swig_getSchedulingFailureType_kNonTargetable() (_swig_ret CarbonMetricsSchedulingFailureType) {
	var swig_r CarbonMetricsSchedulingFailureType
	swig_r = (CarbonMetricsSchedulingFailureType)(C._wrap_SchedulingFailureType_kNonTargetable_metrics_6eb5c8c3624e626d())
	return swig_r
}

var SchedulingFailureType_kNonTargetable CarbonMetricsSchedulingFailureType = _swig_getSchedulingFailureType_kNonTargetable()

func _swig_getSchedulingFailureType_kSchedulingFailureTypeCount() (_swig_ret CarbonMetricsSchedulingFailureType) {
	var swig_r CarbonMetricsSchedulingFailureType
	swig_r = (CarbonMetricsSchedulingFailureType)(C._wrap_SchedulingFailureType_kSchedulingFailureTypeCount_metrics_6eb5c8c3624e626d())
	return swig_r
}

var SchedulingFailureType_kSchedulingFailureTypeCount CarbonMetricsSchedulingFailureType = _swig_getSchedulingFailureType_kSchedulingFailureTypeCount()

func Conclusion_type_to_string(arg1 CarbonMetricsConclusionType) (_swig_ret Std_string) {
	var swig_r Std_string
	_swig_i_0 := arg1
	swig_r = (Std_string)(SwigcptrStd_string(C._wrap_conclusion_type_to_string_metrics_6eb5c8c3624e626d(C.swig_intgo(_swig_i_0))))
	return swig_r
}

func Scheduling_failure_to_string(arg1 CarbonMetricsSchedulingFailureType) (_swig_ret Std_string) {
	var swig_r Std_string
	_swig_i_0 := arg1
	swig_r = (Std_string)(SwigcptrStd_string(C._wrap_scheduling_failure_to_string_metrics_6eb5c8c3624e626d(C.swig_intgo(_swig_i_0))))
	return swig_r
}

func Conclusion_is_targetable(arg1 CarbonMetricsConclusionType) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_conclusion_is_targetable_metrics_6eb5c8c3624e626d(C.swig_intgo(_swig_i_0)))
	return swig_r
}

type SwigcptrStd_string uintptr
type Std_string interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_string) Swigcptr() uintptr {
	return uintptr(p)
}

package geometric_cam

/*
#cgo CXXFLAGS: -g -Wall -Wno-unused-function -I../../.. -I../../../generated -I/usr/local/include/opencv4 -I/usr/local/lib/python3.10/dist-packages/torch/include/torch/csrc/api/include -I/usr/local/lib/python3.10/dist-packages/torch/include -I/usr/lib/python3.8/site-packages/torch/include/torch/csrc/api/include -I/usr/lib/python3.8/site-packages/torch/include -std=c++17
#cgo LDFLAGS: -L/usr/lib/python3.8/site-packages/torch/lib -L/usr/local/lib/ -L${SRCDIR}/../../../build/lib/common/geometric/cpp -L${SRCDIR}/../../../build/lib/common/cpp -L/usr/local/lib/python3.10/dist-packages/torch/lib -Wl,-rpath=/usr/lib/python3.8/site-packages/torch/lib:${SRCDIR}/../../../build/lib/common/geometric/cpp:${SRCDIR}/../../../build/lib/common/cpp -lutils -lgeometric -lopencv_core -ltorch -lbackend_with_compiler -lc10 -lc10_cuda
*/
import "C"

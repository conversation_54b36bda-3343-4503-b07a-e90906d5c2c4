%module geometric_cam
%include <typemaps.i>
%include "std_string.i"
%include "stdint.i"
%include "std_vector.i"

%{
#include <vector>
#include "lib/common/geometric/cpp/geometric_device.hpp"
#include "lib/common/geometric/cpp/geometric_height_estimator.hpp"
#include <lib/common/geometric/cpp/geometric_cam.hpp>
using namespace lib::common::geometric; 

%}

namespace std {
   %template(Vector) vector<double>;
   %template(VectorVectorDouble) vector<vector<double>>;
}

%include "lib/common/geometric/cpp/geometric_device.hpp"
%include "lib/common/geometric/cpp/geometric_height_estimator.hpp"
%include "lib/common/geometric/cpp/geometric_cam.hpp"

/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.2
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// source: /robot/golang/swig/geometric_cam/geometric_cam.i

#define SWIGMODULE geometric_cam

#ifdef __cplusplus
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigMovePointer {
    T *ptr;
    SwigMovePointer(T *p) : ptr(p) { }
    ~SwigMovePointer() { delete ptr; }
    SwigMovePointer& operator=(SwigMovePointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigMovePointer tmp(new T(t)); pointer = tmp; return *this; }
  operator T&() const { return *pointer.ptr; }
  T *operator&() { return pointer.ptr; }
};

template <typename T> T SwigValueInit() {
  return T();
}
#endif

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif


#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>



typedef long long intgo;
typedef unsigned long long uintgo;


# if !defined(__clang__) && (defined(__i386__) || defined(__x86_64__))
#   define SWIGSTRUCTPACKED __attribute__((__packed__, __gcc_struct__))
# else
#   define SWIGSTRUCTPACKED __attribute__((__packed__))
# endif



typedef struct { char *p; intgo n; } _gostring_;
typedef struct { void* array; intgo len; intgo cap; } _goslice_;




#define swiggo_size_assert_eq(x, y, name) typedef char name[(x-y)*(x-y)*-2+1];
#define swiggo_size_assert(t, n) swiggo_size_assert_eq(sizeof(t), n, swiggo_sizeof_##t##_is_not_##n)

swiggo_size_assert(char, 1)
swiggo_size_assert(short, 2)
swiggo_size_assert(int, 4)
typedef long long swiggo_long_long;
swiggo_size_assert(swiggo_long_long, 8)
swiggo_size_assert(float, 4)
swiggo_size_assert(double, 8)

#ifdef __cplusplus
extern "C" {
#endif
extern void crosscall2(void (*fn)(void *, int), void *, int);
extern char* _cgo_topofstack(void) __attribute__ ((weak));
extern void _cgo_allocate(void *, int);
extern void _cgo_panic(void *, int);
#ifdef __cplusplus
}
#endif

static char *_swig_topofstack() {
  if (_cgo_topofstack) {
    return _cgo_topofstack();
  } else {
    return 0;
  }
}

static void _swig_gopanic(const char *p) {
  struct {
    const char *p;
  } SWIGSTRUCTPACKED a;
  a.p = p;
  crosscall2(_cgo_panic, &a, (int) sizeof a);
}




#define SWIG_contract_assert(expr, msg) \
  if (!(expr)) { _swig_gopanic(msg); } else


static _gostring_ Swig_AllocateString(const char *p, size_t l) {
  _gostring_ ret;
  ret.p = (char*)malloc(l);
  memcpy(ret.p, p, l);
  ret.n = l;
  return ret;
}


static void Swig_free(void* p) {
  free(p);
}

static void* Swig_malloc(int c) {
  return malloc(c);
}


#include <string>


#include <stdint.h>		// Use the C99 official header


#include <vector>
#include <stdexcept>


#include <vector>
#include "lib/common/geometric/cpp/geometric_device.hpp"
#include "lib/common/geometric/cpp/geometric_height_estimator.hpp"
#include <lib/common/geometric/cpp/geometric_cam.hpp>
using namespace lib::common::geometric; 


SWIGINTERN std::vector< double >::const_reference std_vector_Sl_double_Sg__get(std::vector< double > *self,int i){
                int size = int(self->size());
                if (i>=0 && i<size)
                    return (*self)[i];
                else
                    throw std::out_of_range("vector index out of range");
            }
SWIGINTERN void std_vector_Sl_double_Sg__set(std::vector< double > *self,int i,std::vector< double >::value_type const &val){
                int size = int(self->size());
                if (i>=0 && i<size)
                    (*self)[i] = val;
                else
                    throw std::out_of_range("vector index out of range");
            }
SWIGINTERN std::vector< std::vector< double > >::const_reference std_vector_Sl_std_vector_Sl_double_Sg__Sg__get(std::vector< std::vector< double > > *self,int i){
                int size = int(self->size());
                if (i>=0 && i<size)
                    return (*self)[i];
                else
                    throw std::out_of_range("vector index out of range");
            }
SWIGINTERN void std_vector_Sl_std_vector_Sl_double_Sg__Sg__set(std::vector< std::vector< double > > *self,int i,std::vector< std::vector< double > >::value_type const &val){
                int size = int(self->size());
                if (i>=0 && i<size)
                    (*self)[i] = val;
                else
                    throw std::out_of_range("vector index out of range");
            }
#ifdef __cplusplus
extern "C" {
#endif

void _wrap_Swig_free_geometric_cam_8835f2338e69cff5(void *_swig_go_0) {
  void *arg1 = (void *) 0 ;
  
  arg1 = *(void **)&_swig_go_0; 
  
  Swig_free(arg1);
  
}


void *_wrap_Swig_malloc_geometric_cam_8835f2338e69cff5(intgo _swig_go_0) {
  int arg1 ;
  void *result = 0 ;
  void *_swig_go_result;
  
  arg1 = (int)_swig_go_0; 
  
  result = (void *)Swig_malloc(arg1);
  *(void **)&_swig_go_result = (void *)result; 
  return _swig_go_result;
}


std::vector< double > *_wrap_new_Vector__SWIG_0_geometric_cam_8835f2338e69cff5() {
  std::vector< double > *result = 0 ;
  std::vector< double > *_swig_go_result;
  
  
  result = (std::vector< double > *)new std::vector< double >();
  *(std::vector< double > **)&_swig_go_result = (std::vector< double > *)result; 
  return _swig_go_result;
}


std::vector< double > *_wrap_new_Vector__SWIG_1_geometric_cam_8835f2338e69cff5(long long _swig_go_0) {
  std::vector< double >::size_type arg1 ;
  std::vector< double > *result = 0 ;
  std::vector< double > *_swig_go_result;
  
  arg1 = (size_t)_swig_go_0; 
  
  result = (std::vector< double > *)new std::vector< double >(arg1);
  *(std::vector< double > **)&_swig_go_result = (std::vector< double > *)result; 
  return _swig_go_result;
}


std::vector< double > *_wrap_new_Vector__SWIG_2_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0) {
  std::vector< double > *arg1 = 0 ;
  std::vector< double > *result = 0 ;
  std::vector< double > *_swig_go_result;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  
  result = (std::vector< double > *)new std::vector< double >((std::vector< double > const &)*arg1);
  *(std::vector< double > **)&_swig_go_result = (std::vector< double > *)result; 
  return _swig_go_result;
}


long long _wrap_Vector_size_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  std::vector< double >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  
  result = ((std::vector< double > const *)arg1)->size();
  _swig_go_result = result; 
  return _swig_go_result;
}


long long _wrap_Vector_capacity_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  std::vector< double >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  
  result = ((std::vector< double > const *)arg1)->capacity();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_Vector_reserve_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0, long long _swig_go_1) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  std::vector< double >::size_type arg2 ;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  arg2 = (size_t)_swig_go_1; 
  
  (arg1)->reserve(arg2);
  
}


bool _wrap_Vector_isEmpty_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  
  result = (bool)((std::vector< double > const *)arg1)->empty();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_Vector_clear_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  
  (arg1)->clear();
  
}


void _wrap_Vector_add_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0, double _swig_go_1) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  std::vector< double >::value_type *arg2 = 0 ;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  arg2 = (std::vector< double >::value_type *)&_swig_go_1; 
  
  (arg1)->push_back((std::vector< double >::value_type const &)*arg2);
  
}


double _wrap_Vector_get_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0, intgo _swig_go_1) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  int arg2 ;
  std::vector< double >::value_type *result = 0 ;
  double _swig_go_result;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  
  try {
    result = (std::vector< double >::value_type *) &std_vector_Sl_double_Sg__get(arg1,arg2);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  _swig_go_result = (double)*result; 
  return _swig_go_result;
}


void _wrap_Vector_set_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0, intgo _swig_go_1, double _swig_go_2) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  int arg2 ;
  std::vector< double >::value_type *arg3 = 0 ;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  arg3 = (std::vector< double >::value_type *)&_swig_go_2; 
  
  try {
    std_vector_Sl_double_Sg__set(arg1,arg2,(double const &)*arg3);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  
}


void _wrap_delete_Vector_geometric_cam_8835f2338e69cff5(std::vector< double > *_swig_go_0) {
  std::vector< double > *arg1 = (std::vector< double > *) 0 ;
  
  arg1 = *(std::vector< double > **)&_swig_go_0; 
  
  delete arg1;
  
}


std::vector< std::vector< double > > *_wrap_new_VectorVectorDouble__SWIG_0_geometric_cam_8835f2338e69cff5() {
  std::vector< std::vector< double > > *result = 0 ;
  std::vector< std::vector< double > > *_swig_go_result;
  
  
  result = (std::vector< std::vector< double > > *)new std::vector< std::vector< double > >();
  *(std::vector< std::vector< double > > **)&_swig_go_result = (std::vector< std::vector< double > > *)result; 
  return _swig_go_result;
}


std::vector< std::vector< double > > *_wrap_new_VectorVectorDouble__SWIG_1_geometric_cam_8835f2338e69cff5(long long _swig_go_0) {
  std::vector< std::vector< double > >::size_type arg1 ;
  std::vector< std::vector< double > > *result = 0 ;
  std::vector< std::vector< double > > *_swig_go_result;
  
  arg1 = (size_t)_swig_go_0; 
  
  result = (std::vector< std::vector< double > > *)new std::vector< std::vector< double > >(arg1);
  *(std::vector< std::vector< double > > **)&_swig_go_result = (std::vector< std::vector< double > > *)result; 
  return _swig_go_result;
}


std::vector< std::vector< double > > *_wrap_new_VectorVectorDouble__SWIG_2_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0) {
  std::vector< std::vector< double > > *arg1 = 0 ;
  std::vector< std::vector< double > > *result = 0 ;
  std::vector< std::vector< double > > *_swig_go_result;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  
  result = (std::vector< std::vector< double > > *)new std::vector< std::vector< double > >((std::vector< std::vector< double > > const &)*arg1);
  *(std::vector< std::vector< double > > **)&_swig_go_result = (std::vector< std::vector< double > > *)result; 
  return _swig_go_result;
}


long long _wrap_VectorVectorDouble_size_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  std::vector< std::vector< double > >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  
  result = ((std::vector< std::vector< double > > const *)arg1)->size();
  _swig_go_result = result; 
  return _swig_go_result;
}


long long _wrap_VectorVectorDouble_capacity_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  std::vector< std::vector< double > >::size_type result;
  long long _swig_go_result;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  
  result = ((std::vector< std::vector< double > > const *)arg1)->capacity();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_VectorVectorDouble_reserve_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0, long long _swig_go_1) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  std::vector< std::vector< double > >::size_type arg2 ;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  arg2 = (size_t)_swig_go_1; 
  
  (arg1)->reserve(arg2);
  
}


bool _wrap_VectorVectorDouble_isEmpty_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  
  result = (bool)((std::vector< std::vector< double > > const *)arg1)->empty();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_VectorVectorDouble_clear_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  
  (arg1)->clear();
  
}


void _wrap_VectorVectorDouble_add_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0, std::vector< double > *_swig_go_1) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  std::vector< std::vector< double > >::value_type *arg2 = 0 ;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  arg2 = *(std::vector< std::vector< double > >::value_type **)&_swig_go_1; 
  
  (arg1)->push_back((std::vector< std::vector< double > >::value_type const &)*arg2);
  
}


std::vector< double > *_wrap_VectorVectorDouble_get_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0, intgo _swig_go_1) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  int arg2 ;
  std::vector< std::vector< double > >::value_type *result = 0 ;
  std::vector< double > *_swig_go_result;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  
  try {
    result = (std::vector< std::vector< double > >::value_type *) &std_vector_Sl_std_vector_Sl_double_Sg__Sg__get(arg1,arg2);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  *(std::vector< std::vector< double > >::value_type **)&_swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_VectorVectorDouble_set_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0, intgo _swig_go_1, std::vector< double > *_swig_go_2) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  int arg2 ;
  std::vector< std::vector< double > >::value_type *arg3 = 0 ;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  arg2 = (int)_swig_go_1; 
  arg3 = *(std::vector< std::vector< double > >::value_type **)&_swig_go_2; 
  
  try {
    std_vector_Sl_std_vector_Sl_double_Sg__Sg__set(arg1,arg2,(std::vector< double > const &)*arg3);
  } catch(std::out_of_range &_e) {
    (void)_e;
    _swig_gopanic("C++ std::out_of_range exception thrown");
    
  }
  
}


void _wrap_delete_VectorVectorDouble_geometric_cam_8835f2338e69cff5(std::vector< std::vector< double > > *_swig_go_0) {
  std::vector< std::vector< double > > *arg1 = (std::vector< std::vector< double > > *) 0 ;
  
  arg1 = *(std::vector< std::vector< double > > **)&_swig_go_0; 
  
  delete arg1;
  
}


lib::common::geometric::GeometricDevice *_wrap_new_GeometricDevice_geometric_cam_8835f2338e69cff5(_gostring_ _swig_go_0, _gostring_ _swig_go_1, std::tuple< double,double,double > *_swig_go_2) {
  std::string arg1 ;
  std::string arg2 ;
  std::tuple< double,double,double > arg3 ;
  std::tuple< double,double,double > *argp3 ;
  lib::common::geometric::GeometricDevice *result = 0 ;
  lib::common::geometric::GeometricDevice *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  argp3 = (std::tuple< double,double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double,double >");
  }
  arg3 = (std::tuple< double,double,double >)*argp3;
  
  
  result = (lib::common::geometric::GeometricDevice *)new lib::common::geometric::GeometricDevice(arg1,arg2,arg3);
  *(lib::common::geometric::GeometricDevice **)&_swig_go_result = (lib::common::geometric::GeometricDevice *)result; 
  return _swig_go_result;
}


_gostring_ _wrap_GeometricDevice_get_name_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricDevice *_swig_go_0) {
  lib::common::geometric::GeometricDevice *arg1 = (lib::common::geometric::GeometricDevice *) 0 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricDevice **)&_swig_go_0; 
  
  result = (arg1)->get_name();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricDevice_get_abs_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricDevice *_swig_go_0) {
  lib::common::geometric::GeometricDevice *arg1 = (lib::common::geometric::GeometricDevice *) 0 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricDevice **)&_swig_go_0; 
  
  result = (arg1)->get_abs_position_mm();
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricDevice_get_offset_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricDevice *_swig_go_0) {
  lib::common::geometric::GeometricDevice *arg1 = (lib::common::geometric::GeometricDevice *) 0 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricDevice **)&_swig_go_0; 
  
  result = (arg1)->get_offset_position_mm();
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricDevice_get_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricDevice *_swig_go_0) {
  lib::common::geometric::GeometricDevice *arg1 = (lib::common::geometric::GeometricDevice *) 0 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricDevice **)&_swig_go_0; 
  
  result = (arg1)->get_position_mm();
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


void _wrap_GeometricDevice_override_offset_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricDevice *_swig_go_0, std::tuple< double,double,double > *_swig_go_1) {
  lib::common::geometric::GeometricDevice *arg1 = (lib::common::geometric::GeometricDevice *) 0 ;
  lib::common::geometric::Tuple3d arg2 ;
  lib::common::geometric::Tuple3d *argp2 ;
  
  arg1 = *(lib::common::geometric::GeometricDevice **)&_swig_go_0; 
  
  argp2 = (lib::common::geometric::Tuple3d *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null lib::common::geometric::Tuple3d");
  }
  arg2 = (lib::common::geometric::Tuple3d)*argp2;
  
  
  (arg1)->override_offset_position_mm(arg2);
  
}


void _wrap_delete_GeometricDevice_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricDevice *_swig_go_0) {
  lib::common::geometric::GeometricDevice *arg1 = (lib::common::geometric::GeometricDevice *) 0 ;
  
  arg1 = *(lib::common::geometric::GeometricDevice **)&_swig_go_0; 
  
  delete arg1;
  
}


double _wrap_IN_2_MM_geometric_cam_8835f2338e69cff5(double _swig_go_0) {
  double arg1 ;
  double result;
  double _swig_go_result;
  
  arg1 = (double)_swig_go_0; 
  
  result = (double)IN_2_MM(arg1);
  _swig_go_result = result; 
  return _swig_go_result;
}


lib::common::geometric::GeometricCamHeightCollector *_wrap_new_GeometricCamHeightCollector_geometric_cam_8835f2338e69cff5(_gostring_ _swig_go_0, double _swig_go_1, double _swig_go_2, double _swig_go_3, double _swig_go_4, intgo _swig_go_5) {
  std::string *arg1 = 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  uint32_t arg6 ;
  lib::common::geometric::GeometricCamHeightCollector *result = 0 ;
  lib::common::geometric::GeometricCamHeightCollector *_swig_go_result;
  
  
  std::string arg1_str(_swig_go_0.p, _swig_go_0.n);
  arg1 = &arg1_str;
  
  arg2 = (double)_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  arg4 = (double)_swig_go_3; 
  arg5 = (double)_swig_go_4; 
  arg6 = (uint32_t)_swig_go_5; 
  
  result = (lib::common::geometric::GeometricCamHeightCollector *)new lib::common::geometric::GeometricCamHeightCollector((std::string const &)*arg1,arg2,arg3,arg4,arg5,arg6);
  *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_result = (lib::common::geometric::GeometricCamHeightCollector *)result; 
  return _swig_go_result;
}


void _wrap_GeometricCamHeightCollector_set_columns_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, long long _swig_go_1) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  size_t arg2 ;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (size_t)_swig_go_1; 
  
  (arg1)->set_columns(arg2);
  
}


double _wrap_GeometricCamHeightCollector_average__SWIG_0_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  
  result = (double)(arg1)->average();
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCamHeightCollector_average__SWIG_1_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, intgo _swig_go_1) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  uint32_t arg2 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (uint32_t)_swig_go_1; 
  
  result = (double)(arg1)->average(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCamHeightCollector_average_for_column_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, intgo _swig_go_1) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  uint32_t arg2 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (uint32_t)_swig_go_1; 
  
  result = (double)(arg1)->average_for_column(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_GeometricCamHeightCollector_push_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, double _swig_go_1, long long _swig_go_2) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  double arg2 ;
  size_t arg3 ;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  arg3 = (size_t)_swig_go_2; 
  
  (arg1)->push_datapoint(arg2,arg3);
  
}


void _wrap_GeometricCamHeightCollector_push_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  double arg2 ;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  (arg1)->push_datapoint(arg2);
  
}


void _wrap_GeometricCamHeightCollector_force_update_height_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  double arg2 ;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  (arg1)->force_update_height(arg2);
  
}


intgo _wrap_GeometricCamHeightCollector_get_column_for_abs_pos_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, double _swig_go_1, std::tuple< double,double > *_swig_go_2) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  double arg2 ;
  std::tuple< double,double > arg3 ;
  std::tuple< double,double > *argp3 ;
  uint32_t result;
  intgo _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  argp3 = (std::tuple< double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg3 = (std::tuple< double,double >)*argp3;
  
  
  result = (uint32_t)(arg1)->get_column_for_abs_pos_mm(arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


intgo _wrap_GeometricCamHeightCollector_get_column_for_pos_x_px_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  double arg2 ;
  uint32_t result;
  intgo _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  result = (uint32_t)(arg1)->get_column_for_pos_x_px(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


std::vector< double > *_wrap_GeometricCamHeightCollector_averages_for_all_columns_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  std::vector< double > result;
  std::vector< double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  
  result = (arg1)->averages_for_all_columns();
  *(std::vector< double > **)&_swig_go_result = new std::vector< double >(result); 
  return _swig_go_result;
}


void _wrap_delete_GeometricCamHeightCollector_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCamHeightCollector *_swig_go_0) {
  lib::common::geometric::GeometricCamHeightCollector *arg1 = (lib::common::geometric::GeometricCamHeightCollector *) 0 ;
  
  arg1 = *(lib::common::geometric::GeometricCamHeightCollector **)&_swig_go_0; 
  
  delete arg1;
  
}


lib::common::geometric::GeometricHeightEstimator *_wrap_new_GeometricHeightEstimator_geometric_cam_8835f2338e69cff5(double _swig_go_0, double _swig_go_1, double _swig_go_2, double _swig_go_3, double _swig_go_4, double _swig_go_5, double _swig_go_6, double _swig_go_7) {
  double arg1 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  double arg6 ;
  double arg7 ;
  double arg8 ;
  lib::common::geometric::GeometricHeightEstimator *result = 0 ;
  lib::common::geometric::GeometricHeightEstimator *_swig_go_result;
  
  arg1 = (double)_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  arg4 = (double)_swig_go_3; 
  arg5 = (double)_swig_go_4; 
  arg6 = (double)_swig_go_5; 
  arg7 = (double)_swig_go_6; 
  arg8 = (double)_swig_go_7; 
  
  result = (lib::common::geometric::GeometricHeightEstimator *)new lib::common::geometric::GeometricHeightEstimator(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8);
  *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_result = (lib::common::geometric::GeometricHeightEstimator *)result; 
  return _swig_go_result;
}


void _wrap_GeometricHeightEstimator_configure_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, std::vector< std::string > *_swig_go_1, intgo _swig_go_2, intgo _swig_go_3) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  std::vector< std::string > *arg2 = 0 ;
  int arg3 ;
  int arg4 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(std::vector< std::string > **)&_swig_go_1; 
  arg3 = (int)_swig_go_2; 
  arg4 = (int)_swig_go_3; 
  
  (arg1)->configure((std::vector< std::string > const &)*arg2,arg3,arg4);
  
}


void _wrap_GeometricHeightEstimator_push_computed_height_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, double _swig_go_2, intgo _swig_go_3) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  double arg3 ;
  uint32_t arg4 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  arg4 = (uint32_t)_swig_go_3; 
  
  (arg1)->push_computed_height_datapoint(*arg2,arg3,arg4);
  
}


void _wrap_GeometricHeightEstimator_push_computed_height_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, double _swig_go_2) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  double arg3 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  
  (arg1)->push_computed_height_datapoint(*arg2,arg3);
  
}


void _wrap_GeometricHeightEstimator_push_height_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, std::tuple< double,double > *_swig_go_2, std::tuple< double,double > *_swig_go_3, intgo _swig_go_4) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  std::tuple< double,double > arg3 ;
  std::tuple< double,double > arg4 ;
  uint32_t arg5 ;
  std::tuple< double,double > *argp3 ;
  std::tuple< double,double > *argp4 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  
  argp3 = (std::tuple< double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg3 = (std::tuple< double,double >)*argp3;
  
  
  argp4 = (std::tuple< double,double > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg4 = (std::tuple< double,double >)*argp4;
  
  arg5 = (uint32_t)_swig_go_4; 
  
  (arg1)->push_height_datapoint(*arg2,arg3,arg4,arg5);
  
}


void _wrap_GeometricHeightEstimator_push_height_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, std::tuple< double,double > *_swig_go_2, std::tuple< double,double > *_swig_go_3) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  std::tuple< double,double > arg3 ;
  std::tuple< double,double > arg4 ;
  std::tuple< double,double > *argp3 ;
  std::tuple< double,double > *argp4 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  
  argp3 = (std::tuple< double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg3 = (std::tuple< double,double >)*argp3;
  
  
  argp4 = (std::tuple< double,double > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg4 = (std::tuple< double,double >)*argp4;
  
  
  (arg1)->push_height_datapoint(*arg2,arg3,arg4);
  
}


void _wrap_GeometricHeightEstimator_push_height_datapoint_with_pos_x_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, std::tuple< double,double > *_swig_go_2, std::tuple< double,double > *_swig_go_3, intgo _swig_go_4) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  std::tuple< double,double > arg3 ;
  std::tuple< double,double > arg4 ;
  uint32_t arg5 ;
  std::tuple< double,double > *argp3 ;
  std::tuple< double,double > *argp4 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  
  argp3 = (std::tuple< double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg3 = (std::tuple< double,double >)*argp3;
  
  
  argp4 = (std::tuple< double,double > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg4 = (std::tuple< double,double >)*argp4;
  
  arg5 = (uint32_t)_swig_go_4; 
  
  (arg1)->push_height_datapoint_with_pos_x(*arg2,arg3,arg4,arg5);
  
}


void _wrap_GeometricHeightEstimator_push_computed_height_datapoint_with_pos_x_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, double _swig_go_2, intgo _swig_go_3) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  double arg3 ;
  uint32_t arg4 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  arg4 = (uint32_t)_swig_go_3; 
  
  (arg1)->push_computed_height_datapoint_with_pos_x(*arg2,arg3,arg4);
  
}


void _wrap_GeometricHeightEstimator_force_update_height_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  double arg2 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  (arg1)->force_update_height(arg2);
  
}


void _wrap_GeometricHeightEstimator_toggle_estimation_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, bool _swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  bool arg2 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = (bool)_swig_go_1; 
  
  (arg1)->toggle_estimation(arg2);
  
}


double _wrap_GeometricHeightEstimator_get_ground_position_z_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  
  result = (double)(arg1)->get_ground_position_z_mm();
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricHeightEstimator_get_ground_position_z_mm_by_cam_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1, intgo _swig_go_2) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  uint32_t arg3 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  arg3 = (uint32_t)_swig_go_2; 
  
  result = (double)(arg1)->get_ground_position_z_mm_by_cam(*arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricHeightEstimator_get_ground_position_z_mm_by_cam_name_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, _gostring_ _swig_go_1, intgo _swig_go_2) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  std::string *arg2 = 0 ;
  uint32_t arg3 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  
  std::string arg2_str(_swig_go_1.p, _swig_go_1.n);
  arg2 = &arg2_str;
  
  arg3 = (uint32_t)_swig_go_2; 
  
  result = (double)(arg1)->get_ground_position_z_mm_by_cam_name((std::string const &)*arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


std::vector< double > *_wrap_GeometricHeightEstimator_get_averages_for_all_columns_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  std::vector< double > result;
  std::vector< double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  
  result = (arg1)->get_averages_for_all_columns(*arg2);
  *(std::vector< double > **)&_swig_go_result = new std::vector< double >(result); 
  return _swig_go_result;
}


void _wrap_GeometricHeightEstimator_set_min_max_x_mm_for_cam_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, lib::common::geometric::GeometricCam *_swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  lib::common::geometric::GeometricCam *arg2 = 0 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = *(lib::common::geometric::GeometricCam **)&_swig_go_1; 
  
  (arg1)->set_min_max_x_mm_for_cam(*arg2);
  
}


std::tuple< std::string,unsigned int > *_wrap_GeometricHeightEstimator_get_keys_for_abs_x_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  double arg2 ;
  std::tuple< std::string,uint32_t > result;
  std::tuple< std::string,unsigned int > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  result = (arg1)->get_keys_for_abs_x(arg2);
  *(std::tuple< std::string,uint32_t > **)&_swig_go_result = new std::tuple< std::string,uint32_t >(result); 
  return _swig_go_result;
}


double _wrap_GeometricHeightEstimator_get_height_mm_for_key_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, std::tuple< std::string,unsigned int > *_swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  std::tuple< std::string,uint32_t > arg2 ;
  std::tuple< std::string,uint32_t > *argp2 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  
  argp2 = (std::tuple< std::string,uint32_t > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< std::string,uint32_t >");
  }
  arg2 = (std::tuple< std::string,uint32_t >)*argp2;
  
  
  result = (double)(arg1)->get_height_mm_for_key(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricHeightEstimator_get_height_mm_for_abs_x_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  double arg2 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  result = (double)(arg1)->get_height_mm_for_abs_x(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricHeightEstimator_get_max_height_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  
  result = (double)(arg1)->get_max_height_mm();
  _swig_go_result = result; 
  return _swig_go_result;
}


void _wrap_delete_GeometricHeightEstimator_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricHeightEstimator *_swig_go_0) {
  lib::common::geometric::GeometricHeightEstimator *arg1 = (lib::common::geometric::GeometricHeightEstimator *) 0 ;
  
  arg1 = *(lib::common::geometric::GeometricHeightEstimator **)&_swig_go_0; 
  
  delete arg1;
  
}


std::shared_ptr< lib::common::geometric::GeometricHeightEstimator > *_wrap_construct_geometric_height_estimator_geometric_cam_8835f2338e69cff5() {
  SwigValueWrapper< std::shared_ptr< lib::common::geometric::GeometricHeightEstimator > > result;
  std::shared_ptr< lib::common::geometric::GeometricHeightEstimator > *_swig_go_result;
  
  
  result = lib::common::geometric::construct_geometric_height_estimator();
  *(std::shared_ptr< lib::common::geometric::GeometricHeightEstimator > **)&_swig_go_result = new std::shared_ptr< lib::common::geometric::GeometricHeightEstimator >(result); 
  return _swig_go_result;
}


lib::common::geometric::GeometricCam *_wrap_new_GeometricCam__SWIG_0_geometric_cam_8835f2338e69cff5(_gostring_ _swig_go_0, std::tuple< double,double,double > *_swig_go_1, std::tuple< unsigned int,unsigned int > *_swig_go_2, double _swig_go_3, double _swig_go_4, std::tuple< double,double > *_swig_go_5, torch::Tensor *_swig_go_6, torch::Tensor *_swig_go_7, torch::Tensor *_swig_go_8, torch::Tensor *_swig_go_9) {
  std::string arg1 ;
  lib::common::geometric::Tuple3d arg2 ;
  std::tuple< uint32_t,uint32_t > arg3 ;
  double arg4 ;
  double arg5 ;
  std::tuple< double,double > arg6 ;
  torch::Tensor arg7 ;
  torch::Tensor arg8 ;
  torch::Tensor arg9 ;
  torch::Tensor arg10 ;
  lib::common::geometric::Tuple3d *argp2 ;
  std::tuple< uint32_t,uint32_t > *argp3 ;
  std::tuple< double,double > *argp6 ;
  torch::Tensor *argp7 ;
  torch::Tensor *argp8 ;
  torch::Tensor *argp9 ;
  torch::Tensor *argp10 ;
  lib::common::geometric::GeometricCam *result = 0 ;
  lib::common::geometric::GeometricCam *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  
  argp2 = (lib::common::geometric::Tuple3d *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null lib::common::geometric::Tuple3d");
  }
  arg2 = (lib::common::geometric::Tuple3d)*argp2;
  
  
  argp3 = (std::tuple< uint32_t,uint32_t > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< uint32_t,uint32_t >");
  }
  arg3 = (std::tuple< uint32_t,uint32_t >)*argp3;
  
  arg4 = (double)_swig_go_3; 
  arg5 = (double)_swig_go_4; 
  
  argp6 = (std::tuple< double,double > *)_swig_go_5;
  if (argp6 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg6 = (std::tuple< double,double >)*argp6;
  
  
  argp7 = (torch::Tensor *)_swig_go_6;
  if (argp7 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg7 = (torch::Tensor)*argp7;
  
  
  argp8 = (torch::Tensor *)_swig_go_7;
  if (argp8 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg8 = (torch::Tensor)*argp8;
  
  
  argp9 = (torch::Tensor *)_swig_go_8;
  if (argp9 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg9 = (torch::Tensor)*argp9;
  
  
  argp10 = (torch::Tensor *)_swig_go_9;
  if (argp10 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg10 = (torch::Tensor)*argp10;
  
  
  result = (lib::common::geometric::GeometricCam *)new lib::common::geometric::GeometricCam(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10);
  *(lib::common::geometric::GeometricCam **)&_swig_go_result = (lib::common::geometric::GeometricCam *)result; 
  return _swig_go_result;
}


lib::common::geometric::GeometricCam *_wrap_new_GeometricCam__SWIG_1_geometric_cam_8835f2338e69cff5(_gostring_ _swig_go_0, _gostring_ _swig_go_1, std::tuple< double,double,double > *_swig_go_2, std::tuple< unsigned int,unsigned int > *_swig_go_3, double _swig_go_4, double _swig_go_5, std::tuple< double,double > *_swig_go_6, torch::Tensor *_swig_go_7, torch::Tensor *_swig_go_8, torch::Tensor *_swig_go_9, torch::Tensor *_swig_go_10) {
  std::string arg1 ;
  std::string arg2 ;
  lib::common::geometric::Tuple3d arg3 ;
  std::tuple< uint32_t,uint32_t > arg4 ;
  double arg5 ;
  double arg6 ;
  std::tuple< double,double > arg7 ;
  torch::Tensor arg8 ;
  torch::Tensor arg9 ;
  torch::Tensor arg10 ;
  torch::Tensor arg11 ;
  lib::common::geometric::Tuple3d *argp3 ;
  std::tuple< uint32_t,uint32_t > *argp4 ;
  std::tuple< double,double > *argp7 ;
  torch::Tensor *argp8 ;
  torch::Tensor *argp9 ;
  torch::Tensor *argp10 ;
  torch::Tensor *argp11 ;
  lib::common::geometric::GeometricCam *result = 0 ;
  lib::common::geometric::GeometricCam *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  (&arg2)->assign(_swig_go_1.p, _swig_go_1.n); 
  
  argp3 = (lib::common::geometric::Tuple3d *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null lib::common::geometric::Tuple3d");
  }
  arg3 = (lib::common::geometric::Tuple3d)*argp3;
  
  
  argp4 = (std::tuple< uint32_t,uint32_t > *)_swig_go_3;
  if (argp4 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< uint32_t,uint32_t >");
  }
  arg4 = (std::tuple< uint32_t,uint32_t >)*argp4;
  
  arg5 = (double)_swig_go_4; 
  arg6 = (double)_swig_go_5; 
  
  argp7 = (std::tuple< double,double > *)_swig_go_6;
  if (argp7 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg7 = (std::tuple< double,double >)*argp7;
  
  
  argp8 = (torch::Tensor *)_swig_go_7;
  if (argp8 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg8 = (torch::Tensor)*argp8;
  
  
  argp9 = (torch::Tensor *)_swig_go_8;
  if (argp9 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg9 = (torch::Tensor)*argp9;
  
  
  argp10 = (torch::Tensor *)_swig_go_9;
  if (argp10 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg10 = (torch::Tensor)*argp10;
  
  
  argp11 = (torch::Tensor *)_swig_go_10;
  if (argp11 == NULL) {
    _swig_gopanic("Attempt to dereference null torch::Tensor");
  }
  arg11 = (torch::Tensor)*argp11;
  
  
  result = (lib::common::geometric::GeometricCam *)new lib::common::geometric::GeometricCam(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10,arg11);
  *(lib::common::geometric::GeometricCam **)&_swig_go_result = (lib::common::geometric::GeometricCam *)result; 
  return _swig_go_result;
}


lib::common::geometric::GeometricCam *_wrap_new_GeometricCam__SWIG_2_geometric_cam_8835f2338e69cff5(_gostring_ _swig_go_0, double _swig_go_1, double _swig_go_2, double _swig_go_3, intgo _swig_go_4, intgo _swig_go_5, double _swig_go_6, double _swig_go_7, double _swig_go_8, double _swig_go_9, std::vector< std::vector< double > > *_swig_go_10, std::vector< double > *_swig_go_11) {
  std::string arg1 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  uint32_t arg5 ;
  uint32_t arg6 ;
  double arg7 ;
  double arg8 ;
  double arg9 ;
  double arg10 ;
  std::vector< std::vector< double > > arg11 ;
  std::vector< double > arg12 ;
  std::vector< std::vector< double > > const *argp11 ;
  std::vector< double > const *argp12 ;
  lib::common::geometric::GeometricCam *result = 0 ;
  lib::common::geometric::GeometricCam *_swig_go_result;
  
  (&arg1)->assign(_swig_go_0.p, _swig_go_0.n); 
  arg2 = (double)_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  arg4 = (double)_swig_go_3; 
  arg5 = (uint32_t)_swig_go_4; 
  arg6 = (uint32_t)_swig_go_5; 
  arg7 = (double)_swig_go_6; 
  arg8 = (double)_swig_go_7; 
  arg9 = (double)_swig_go_8; 
  arg10 = (double)_swig_go_9; 
  
  argp11 = (std::vector< std::vector< double > > *)_swig_go_10;
  if (argp11 == NULL) {
    _swig_gopanic("Attempt to dereference null std::vector< std::vector< double > > const");
  }
  arg11 = (std::vector< std::vector< double > >)*argp11;
  
  
  argp12 = (std::vector< double > *)_swig_go_11;
  if (argp12 == NULL) {
    _swig_gopanic("Attempt to dereference null std::vector< double > const");
  }
  arg12 = (std::vector< double >)*argp12;
  
  
  result = (lib::common::geometric::GeometricCam *)new lib::common::geometric::GeometricCam(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10,arg11,arg12);
  *(lib::common::geometric::GeometricCam **)&_swig_go_result = (lib::common::geometric::GeometricCam *)result; 
  return _swig_go_result;
}


bool _wrap_GeometricCam_is_point_in_camera_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double,double > arg2 ;
  std::tuple< double,double,double > *argp2 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double,double >");
  }
  arg2 = (std::tuple< double,double,double >)*argp2;
  
  
  result = (bool)(arg1)->is_point_in_camera(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


bool _wrap_GeometricCam_is_point_below_camera_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double,double > arg2 ;
  std::tuple< double,double,double > *argp2 ;
  bool result;
  bool _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double,double >");
  }
  arg2 = (std::tuple< double,double,double >)*argp2;
  
  
  result = (bool)(arg1)->is_point_below_camera(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_undistort_point_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->undistort_point(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_distort_point_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->distort_point(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_get_sensor_mm_from_centered_px_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->get_sensor_mm_from_centered_px(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_get_sensor_px_from_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->get_sensor_px_from_mm(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


double _wrap_GeometricCam_get_size_mm_from_size_px__SWIG_0_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, double _swig_go_1, double _swig_go_2) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  double arg2 ;
  double arg3 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  
  result = (double)(arg1)->get_size_mm_from_size_px(arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCam_get_size_mm_from_size_px__SWIG_1_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  double arg2 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  result = (double)(arg1)->get_size_mm_from_size_px(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCam_get_size_px_from_size_mm__SWIG_0_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, double _swig_go_1, double _swig_go_2) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  double arg2 ;
  double arg3 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  
  result = (double)(arg1)->get_size_px_from_size_mm(arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCam_get_size_px_from_size_mm__SWIG_1_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, double _swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  double arg2 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  
  result = (double)(arg1)->get_size_px_from_size_mm(arg2);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCam_compute_vertex_height_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1, std::tuple< double,double > *_swig_go_2) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > arg3 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > *argp3 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  argp3 = (std::tuple< double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg3 = (std::tuple< double,double >)*argp3;
  
  
  result = (double)(arg1)->compute_vertex_height(arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


double _wrap_GeometricCam_compute_ground_height_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1, std::tuple< double,double > *_swig_go_2) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > arg3 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > *argp3 ;
  double result;
  double _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  argp3 = (std::tuple< double,double > *)_swig_go_2;
  if (argp3 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg3 = (std::tuple< double,double >)*argp3;
  
  
  result = (double)(arg1)->compute_ground_height(arg2,arg3);
  _swig_go_result = result; 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_abs_position_from_px_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->get_abs_position_from_px(arg2);
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_abs_position_from_undistorted_px_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->get_abs_position_from_undistorted_px(arg2);
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_abs_position_from_px_from_height_estimate_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1, double _swig_go_2) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  double arg3 ;
  std::tuple< double,double > *argp2 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  arg3 = (double)_swig_go_2; 
  
  result = (arg1)->get_abs_position_from_px_from_height_estimate(arg2,arg3);
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_abs_position_from_undistorted_px_from_height_estimate_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1, double _swig_go_2) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  double arg3 ;
  std::tuple< double,double > *argp2 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  arg3 = (double)_swig_go_2; 
  
  result = (arg1)->get_abs_position_from_undistorted_px_from_height_estimate(arg2,arg3);
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_get_undistorted_px_from_abs_position_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  lib::common::geometric::Tuple3d arg2 ;
  lib::common::geometric::Tuple3d *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (lib::common::geometric::Tuple3d *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null lib::common::geometric::Tuple3d");
  }
  arg2 = (lib::common::geometric::Tuple3d)*argp2;
  
  
  result = (arg1)->get_undistorted_px_from_abs_position(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_get_distorted_px_from_abs_position_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  lib::common::geometric::Tuple3d arg2 ;
  lib::common::geometric::Tuple3d *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (lib::common::geometric::Tuple3d *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null lib::common::geometric::Tuple3d");
  }
  arg2 = (lib::common::geometric::Tuple3d)*argp2;
  
  
  result = (arg1)->get_distorted_px_from_abs_position(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


void _wrap_GeometricCam_get_distorted_px_from_abs_position_swig_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, double _swig_go_1, double _swig_go_2, double _swig_go_3, double *_swig_go_4, double *_swig_go_5) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double *arg5 = (double *) 0 ;
  double *arg6 = (double *) 0 ;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  arg2 = (double)_swig_go_1; 
  arg3 = (double)_swig_go_2; 
  arg4 = (double)_swig_go_3; 
  arg5 = *(double **)&_swig_go_4; 
  arg6 = *(double **)&_swig_go_5; 
  
  (arg1)->get_distorted_px_from_abs_position_swig(arg2,arg3,arg4,arg5,arg6);
  
}


std::tuple< double,double > *_wrap_GeometricCam_get_delta_mm_from_delta_px_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > arg2 ;
  std::tuple< double,double > *argp2 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (std::tuple< double,double > *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null std::tuple< double,double >");
  }
  arg2 = (std::tuple< double,double >)*argp2;
  
  
  result = (arg1)->get_delta_mm_from_delta_px(arg2);
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


std::tuple< double,double > *_wrap_GeometricCam_get_min_max_abs_x_mm_for_cam_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< double,double > result;
  std::tuple< double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  result = (arg1)->get_min_max_abs_x_mm_for_cam();
  *(std::tuple< double,double > **)&_swig_go_result = new std::tuple< double,double >(result); 
  return _swig_go_result;
}


std::tuple< unsigned int,unsigned int > *_wrap_GeometricCam_resolution_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::tuple< uint32_t,uint32_t > *result = 0 ;
  std::tuple< unsigned int,unsigned int > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  result = (std::tuple< uint32_t,uint32_t > *) &(arg1)->resolution();
  *(std::tuple< uint32_t,uint32_t > **)&_swig_go_result = result; 
  return _swig_go_result;
}


torch::Tensor *_wrap_GeometricCam_d2u_mapx_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  torch::Tensor result;
  torch::Tensor *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  result = (arg1)->d2u_mapx();
  *(torch::Tensor **)&_swig_go_result = new torch::Tensor(result); 
  return _swig_go_result;
}


torch::Tensor *_wrap_GeometricCam_d2u_mapy_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  torch::Tensor result;
  torch::Tensor *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  result = (arg1)->d2u_mapy();
  *(torch::Tensor **)&_swig_go_result = new torch::Tensor(result); 
  return _swig_go_result;
}


void _wrap_delete_GeometricCam_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  delete arg1;
  
}


_gostring_ _wrap_GeometricCam_get_name_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  std::string result;
  _gostring_ _swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  lib::common::geometric::GeometricDevice *swig_b0 = (lib::common::geometric::GeometricDevice *)arg1;
  result = (swig_b0)->get_name();
  _swig_go_result = Swig_AllocateString((&result)->data(), (&result)->length()); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_abs_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  lib::common::geometric::GeometricDevice *swig_b0 = (lib::common::geometric::GeometricDevice *)arg1;
  result = (swig_b0)->get_abs_position_mm();
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_offset_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  lib::common::geometric::GeometricDevice *swig_b0 = (lib::common::geometric::GeometricDevice *)arg1;
  result = (swig_b0)->get_offset_position_mm();
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


std::tuple< double,double,double > *_wrap_GeometricCam_get_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  lib::common::geometric::Tuple3d result;
  std::tuple< double,double,double > *_swig_go_result;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  lib::common::geometric::GeometricDevice *swig_b0 = (lib::common::geometric::GeometricDevice *)arg1;
  result = (swig_b0)->get_position_mm();
  *(lib::common::geometric::Tuple3d **)&_swig_go_result = new lib::common::geometric::Tuple3d(result); 
  return _swig_go_result;
}


void _wrap_GeometricCam_override_offset_position_mm_geometric_cam_8835f2338e69cff5(lib::common::geometric::GeometricCam *_swig_go_0, std::tuple< double,double,double > *_swig_go_1) {
  lib::common::geometric::GeometricCam *arg1 = (lib::common::geometric::GeometricCam *) 0 ;
  lib::common::geometric::Tuple3d arg2 ;
  lib::common::geometric::Tuple3d *argp2 ;
  
  arg1 = *(lib::common::geometric::GeometricCam **)&_swig_go_0; 
  
  argp2 = (lib::common::geometric::Tuple3d *)_swig_go_1;
  if (argp2 == NULL) {
    _swig_gopanic("Attempt to dereference null lib::common::geometric::Tuple3d");
  }
  arg2 = (lib::common::geometric::Tuple3d)*argp2;
  
  
  lib::common::geometric::GeometricDevice *swig_b0 = (lib::common::geometric::GeometricDevice *)arg1;
  (swig_b0)->override_offset_position_mm(arg2);
  
}


#ifdef __cplusplus
}
#endif


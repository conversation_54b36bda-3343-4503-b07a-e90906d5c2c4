/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.2
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// source: /robot/golang/swig/geometric_cam/geometric_cam.i

package geometric_cam

/*
#define intgo swig_intgo
typedef void *swig_voidp;

#include <stdint.h>


typedef long long intgo;
typedef unsigned long long uintgo;



typedef struct { char *p; intgo n; } _gostring_;
typedef struct { void* array; intgo len; intgo cap; } _goslice_;


typedef long long swig_type_1;
typedef long long swig_type_2;
typedef long long swig_type_3;
typedef long long swig_type_4;
typedef long long swig_type_5;
typedef long long swig_type_6;
typedef long long swig_type_7;
typedef long long swig_type_8;
typedef _gostring_ swig_type_9;
typedef _gostring_ swig_type_10;
typedef _gostring_ swig_type_11;
typedef _gostring_ swig_type_12;
typedef long long swig_type_13;
typedef long long swig_type_14;
typedef _gostring_ swig_type_15;
typedef _gostring_ swig_type_16;
typedef _gostring_ swig_type_17;
typedef _gostring_ swig_type_18;
typedef _gostring_ swig_type_19;
typedef _gostring_ swig_type_20;
extern void _wrap_Swig_free_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_Swig_malloc_geometric_cam_8835f2338e69cff5(swig_intgo arg1);
extern uintptr_t _wrap_new_Vector__SWIG_0_geometric_cam_8835f2338e69cff5(void);
extern uintptr_t _wrap_new_Vector__SWIG_1_geometric_cam_8835f2338e69cff5(swig_type_1 arg1);
extern uintptr_t _wrap_new_Vector__SWIG_2_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern swig_type_2 _wrap_Vector_size_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern swig_type_3 _wrap_Vector_capacity_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_Vector_reserve_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_type_4 arg2);
extern _Bool _wrap_Vector_isEmpty_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_Vector_clear_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_Vector_add_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern double _wrap_Vector_get_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_intgo arg2);
extern void _wrap_Vector_set_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_intgo arg2, double arg3);
extern void _wrap_delete_Vector_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_new_VectorVectorDouble__SWIG_0_geometric_cam_8835f2338e69cff5(void);
extern uintptr_t _wrap_new_VectorVectorDouble__SWIG_1_geometric_cam_8835f2338e69cff5(swig_type_5 arg1);
extern uintptr_t _wrap_new_VectorVectorDouble__SWIG_2_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern swig_type_6 _wrap_VectorVectorDouble_size_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern swig_type_7 _wrap_VectorVectorDouble_capacity_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_VectorVectorDouble_reserve_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_type_8 arg2);
extern _Bool _wrap_VectorVectorDouble_isEmpty_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_VectorVectorDouble_clear_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_VectorVectorDouble_add_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_VectorVectorDouble_get_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_intgo arg2);
extern void _wrap_VectorVectorDouble_set_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_intgo arg2, uintptr_t arg3);
extern void _wrap_delete_VectorVectorDouble_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_new_GeometricDevice_geometric_cam_8835f2338e69cff5(swig_type_9 arg1, swig_type_10 arg2, uintptr_t arg3);
extern swig_type_11 _wrap_GeometricDevice_get_name_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_GeometricDevice_get_abs_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_GeometricDevice_get_offset_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_GeometricDevice_get_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_GeometricDevice_override_offset_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern void _wrap_delete_GeometricDevice_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern double _wrap_IN_2_MM_geometric_cam_8835f2338e69cff5(double arg1);
extern uintptr_t _wrap_new_GeometricCamHeightCollector_geometric_cam_8835f2338e69cff5(swig_type_12 arg1, double arg2, double arg3, double arg4, double arg5, swig_intgo arg6);
extern void _wrap_GeometricCamHeightCollector_set_columns_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_type_13 arg2);
extern double _wrap_GeometricCamHeightCollector_average__SWIG_0_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern double _wrap_GeometricCamHeightCollector_average__SWIG_1_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_intgo arg2);
extern double _wrap_GeometricCamHeightCollector_average_for_column_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_intgo arg2);
extern void _wrap_GeometricCamHeightCollector_push_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2, swig_type_14 arg3);
extern void _wrap_GeometricCamHeightCollector_push_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern void _wrap_GeometricCamHeightCollector_force_update_height_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern swig_intgo _wrap_GeometricCamHeightCollector_get_column_for_abs_pos_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2, uintptr_t arg3);
extern swig_intgo _wrap_GeometricCamHeightCollector_get_column_for_pos_x_px_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern uintptr_t _wrap_GeometricCamHeightCollector_averages_for_all_columns_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_delete_GeometricCamHeightCollector_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_new_GeometricHeightEstimator_geometric_cam_8835f2338e69cff5(double arg1, double arg2, double arg3, double arg4, double arg5, double arg6, double arg7, double arg8);
extern void _wrap_GeometricHeightEstimator_configure_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, swig_intgo arg3, swig_intgo arg4);
extern void _wrap_GeometricHeightEstimator_push_computed_height_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, double arg3, swig_intgo arg4);
extern void _wrap_GeometricHeightEstimator_push_computed_height_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, double arg3);
extern void _wrap_GeometricHeightEstimator_push_height_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4, swig_intgo arg5);
extern void _wrap_GeometricHeightEstimator_push_height_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4);
extern void _wrap_GeometricHeightEstimator_push_height_datapoint_with_pos_x_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, uintptr_t arg3, uintptr_t arg4, swig_intgo arg5);
extern void _wrap_GeometricHeightEstimator_push_computed_height_datapoint_with_pos_x_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, double arg3, swig_intgo arg4);
extern void _wrap_GeometricHeightEstimator_force_update_height_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern void _wrap_GeometricHeightEstimator_toggle_estimation_geometric_cam_8835f2338e69cff5(uintptr_t arg1, _Bool arg2);
extern double _wrap_GeometricHeightEstimator_get_ground_position_z_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern double _wrap_GeometricHeightEstimator_get_ground_position_z_mm_by_cam_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, swig_intgo arg3);
extern double _wrap_GeometricHeightEstimator_get_ground_position_z_mm_by_cam_name_geometric_cam_8835f2338e69cff5(uintptr_t arg1, swig_type_15 arg2, swig_intgo arg3);
extern uintptr_t _wrap_GeometricHeightEstimator_get_averages_for_all_columns_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern void _wrap_GeometricHeightEstimator_set_min_max_x_mm_for_cam_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricHeightEstimator_get_keys_for_abs_x_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern double _wrap_GeometricHeightEstimator_get_height_mm_for_key_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern double _wrap_GeometricHeightEstimator_get_height_mm_for_abs_x_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern double _wrap_GeometricHeightEstimator_get_max_height_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_delete_GeometricHeightEstimator_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_construct_geometric_height_estimator_geometric_cam_8835f2338e69cff5(void);
extern uintptr_t _wrap_new_GeometricCam__SWIG_0_geometric_cam_8835f2338e69cff5(swig_type_16 arg1, uintptr_t arg2, uintptr_t arg3, double arg4, double arg5, uintptr_t arg6, uintptr_t arg7, uintptr_t arg8, uintptr_t arg9, uintptr_t arg10);
extern uintptr_t _wrap_new_GeometricCam__SWIG_1_geometric_cam_8835f2338e69cff5(swig_type_17 arg1, swig_type_18 arg2, uintptr_t arg3, uintptr_t arg4, double arg5, double arg6, uintptr_t arg7, uintptr_t arg8, uintptr_t arg9, uintptr_t arg10, uintptr_t arg11);
extern uintptr_t _wrap_new_GeometricCam__SWIG_2_geometric_cam_8835f2338e69cff5(swig_type_19 arg1, double arg2, double arg3, double arg4, swig_intgo arg5, swig_intgo arg6, double arg7, double arg8, double arg9, double arg10, uintptr_t arg11, uintptr_t arg12);
extern _Bool _wrap_GeometricCam_is_point_in_camera_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern _Bool _wrap_GeometricCam_is_point_below_camera_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_undistort_point_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_distort_point_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_get_sensor_mm_from_centered_px_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_get_sensor_px_from_mm_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern double _wrap_GeometricCam_get_size_mm_from_size_px__SWIG_0_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2, double arg3);
extern double _wrap_GeometricCam_get_size_mm_from_size_px__SWIG_1_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern double _wrap_GeometricCam_get_size_px_from_size_mm__SWIG_0_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2, double arg3);
extern double _wrap_GeometricCam_get_size_px_from_size_mm__SWIG_1_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2);
extern double _wrap_GeometricCam_compute_vertex_height_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, uintptr_t arg3);
extern double _wrap_GeometricCam_compute_ground_height_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, uintptr_t arg3);
extern uintptr_t _wrap_GeometricCam_get_abs_position_from_px_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_get_abs_position_from_undistorted_px_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_get_abs_position_from_px_from_height_estimate_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, double arg3);
extern uintptr_t _wrap_GeometricCam_get_abs_position_from_undistorted_px_from_height_estimate_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2, double arg3);
extern uintptr_t _wrap_GeometricCam_get_undistorted_px_from_abs_position_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_get_distorted_px_from_abs_position_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern void _wrap_GeometricCam_get_distorted_px_from_abs_position_swig_geometric_cam_8835f2338e69cff5(uintptr_t arg1, double arg2, double arg3, double arg4, swig_voidp arg5, swig_voidp arg6);
extern uintptr_t _wrap_GeometricCam_get_delta_mm_from_delta_px_geometric_cam_8835f2338e69cff5(uintptr_t arg1, uintptr_t arg2);
extern uintptr_t _wrap_GeometricCam_get_min_max_abs_x_mm_for_cam_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_GeometricCam_resolution_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_GeometricCam_d2u_mapx_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern uintptr_t _wrap_GeometricCam_d2u_mapy_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern void _wrap_delete_GeometricCam_geometric_cam_8835f2338e69cff5(uintptr_t arg1);
extern swig_type_20 _wrap_GeometricCam_get_name_geometric_cam_8835f2338e69cff5(uintptr_t _swig_base);
extern uintptr_t _wrap_GeometricCam_get_abs_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t _swig_base);
extern uintptr_t _wrap_GeometricCam_get_offset_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t _swig_base);
extern uintptr_t _wrap_GeometricCam_get_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t _swig_base);
extern void _wrap_GeometricCam_override_offset_position_mm_geometric_cam_8835f2338e69cff5(uintptr_t _swig_base, uintptr_t arg1);
#undef intgo
*/
import "C"

import "unsafe"
import _ "runtime/cgo"
import "sync"

type _ unsafe.Pointer

var Swig_escape_always_false bool
var Swig_escape_val interface{}

type _swig_fnptr *byte
type _swig_memberptr *byte

type _ sync.Mutex

type swig_gostring struct {
	p uintptr
	n int
}

func swigCopyString(s string) string {
	p := *(*swig_gostring)(unsafe.Pointer(&s))
	r := string((*[0x7fffffff]byte)(unsafe.Pointer(p.p))[:p.n])
	Swig_free(p.p)
	return r
}

func Swig_free(arg1 uintptr) {
	_swig_i_0 := arg1
	C._wrap_Swig_free_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

func Swig_malloc(arg1 int) (_swig_ret uintptr) {
	var swig_r uintptr
	_swig_i_0 := arg1
	swig_r = (uintptr)(C._wrap_Swig_malloc_geometric_cam_8835f2338e69cff5(C.swig_intgo(_swig_i_0)))
	return swig_r
}

type SwigcptrVector uintptr

func (p SwigcptrVector) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrVector) SwigIsVector() {
}

func NewVector__SWIG_0() (_swig_ret Vector) {
	var swig_r Vector
	swig_r = (Vector)(SwigcptrVector(C._wrap_new_Vector__SWIG_0_geometric_cam_8835f2338e69cff5()))
	return swig_r
}

func NewVector__SWIG_1(arg1 int64) (_swig_ret Vector) {
	var swig_r Vector
	_swig_i_0 := arg1
	swig_r = (Vector)(SwigcptrVector(C._wrap_new_Vector__SWIG_1_geometric_cam_8835f2338e69cff5(C.swig_type_1(_swig_i_0))))
	return swig_r
}

func NewVector__SWIG_2(arg1 Vector) (_swig_ret Vector) {
	var swig_r Vector
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (Vector)(SwigcptrVector(C._wrap_new_Vector__SWIG_2_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func NewVector(a ...interface{}) Vector {
	argc := len(a)
	if argc == 0 {
		return NewVector__SWIG_0()
	}
	if argc == 1 {
		if _, ok := a[0].(int64); !ok {
			goto check_2
		}
		return NewVector__SWIG_1(a[0].(int64))
	}
check_2:
	if argc == 1 {
		return NewVector__SWIG_2(a[0].(Vector))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrVector) Size() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_Vector_size_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrVector) Capacity() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_Vector_capacity_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrVector) Reserve(arg2 int64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_Vector_reserve_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_type_4(_swig_i_1))
}

func (arg1 SwigcptrVector) IsEmpty() (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_Vector_isEmpty_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrVector) Clear() {
	_swig_i_0 := arg1
	C._wrap_Vector_clear_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrVector) Add(arg2 float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_Vector_add_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1))
}

func (arg1 SwigcptrVector) Get(arg2 int) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (float64)(C._wrap_Vector_get_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrVector) Set(arg2 int, arg3 float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	C._wrap_Vector_set_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1), C.double(_swig_i_2))
}

func DeleteVector(arg1 Vector) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_Vector_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

type Vector interface {
	Swigcptr() uintptr
	SwigIsVector()
	Size() (_swig_ret int64)
	Capacity() (_swig_ret int64)
	Reserve(arg2 int64)
	IsEmpty() (_swig_ret bool)
	Clear()
	Add(arg2 float64)
	Get(arg2 int) (_swig_ret float64)
	Set(arg2 int, arg3 float64)
}

type SwigcptrVectorVectorDouble uintptr

func (p SwigcptrVectorVectorDouble) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrVectorVectorDouble) SwigIsVectorVectorDouble() {
}

func NewVectorVectorDouble__SWIG_0() (_swig_ret VectorVectorDouble) {
	var swig_r VectorVectorDouble
	swig_r = (VectorVectorDouble)(SwigcptrVectorVectorDouble(C._wrap_new_VectorVectorDouble__SWIG_0_geometric_cam_8835f2338e69cff5()))
	return swig_r
}

func NewVectorVectorDouble__SWIG_1(arg1 int64) (_swig_ret VectorVectorDouble) {
	var swig_r VectorVectorDouble
	_swig_i_0 := arg1
	swig_r = (VectorVectorDouble)(SwigcptrVectorVectorDouble(C._wrap_new_VectorVectorDouble__SWIG_1_geometric_cam_8835f2338e69cff5(C.swig_type_5(_swig_i_0))))
	return swig_r
}

func NewVectorVectorDouble__SWIG_2(arg1 VectorVectorDouble) (_swig_ret VectorVectorDouble) {
	var swig_r VectorVectorDouble
	_swig_i_0 := arg1.Swigcptr()
	swig_r = (VectorVectorDouble)(SwigcptrVectorVectorDouble(C._wrap_new_VectorVectorDouble__SWIG_2_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func NewVectorVectorDouble(a ...interface{}) VectorVectorDouble {
	argc := len(a)
	if argc == 0 {
		return NewVectorVectorDouble__SWIG_0()
	}
	if argc == 1 {
		if _, ok := a[0].(int64); !ok {
			goto check_2
		}
		return NewVectorVectorDouble__SWIG_1(a[0].(int64))
	}
check_2:
	if argc == 1 {
		return NewVectorVectorDouble__SWIG_2(a[0].(VectorVectorDouble))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrVectorVectorDouble) Size() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_VectorVectorDouble_size_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrVectorVectorDouble) Capacity() (_swig_ret int64) {
	var swig_r int64
	_swig_i_0 := arg1
	swig_r = (int64)(C._wrap_VectorVectorDouble_capacity_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrVectorVectorDouble) Reserve(arg2 int64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_VectorVectorDouble_reserve_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_type_8(_swig_i_1))
}

func (arg1 SwigcptrVectorVectorDouble) IsEmpty() (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	swig_r = (bool)(C._wrap_VectorVectorDouble_isEmpty_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrVectorVectorDouble) Clear() {
	_swig_i_0 := arg1
	C._wrap_VectorVectorDouble_clear_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

func (arg1 SwigcptrVectorVectorDouble) Add(arg2 Vector) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	C._wrap_VectorVectorDouble_add_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))
}

func (arg1 SwigcptrVectorVectorDouble) Get(arg2 int) (_swig_ret Vector) {
	var swig_r Vector
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (Vector)(SwigcptrVector(C._wrap_VectorVectorDouble_get_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrVectorVectorDouble) Set(arg2 int, arg3 Vector) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3.Swigcptr()
	C._wrap_VectorVectorDouble_set_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1), C.uintptr_t(_swig_i_2))
}

func DeleteVectorVectorDouble(arg1 VectorVectorDouble) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_VectorVectorDouble_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

type VectorVectorDouble interface {
	Swigcptr() uintptr
	SwigIsVectorVectorDouble()
	Size() (_swig_ret int64)
	Capacity() (_swig_ret int64)
	Reserve(arg2 int64)
	IsEmpty() (_swig_ret bool)
	Clear()
	Add(arg2 Vector)
	Get(arg2 int) (_swig_ret Vector)
	Set(arg2 int, arg3 Vector)
}

type SwigcptrGeometricDevice uintptr

func (p SwigcptrGeometricDevice) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrGeometricDevice) SwigIsGeometricDevice() {
}

func NewGeometricDevice(arg1 string, arg2 string, arg3 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret GeometricDevice) {
	var swig_r GeometricDevice
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3.Swigcptr()
	swig_r = (GeometricDevice)(SwigcptrGeometricDevice(C._wrap_new_GeometricDevice_geometric_cam_8835f2338e69cff5(*(*C.swig_type_9)(unsafe.Pointer(&_swig_i_0)), *(*C.swig_type_10)(unsafe.Pointer(&_swig_i_1)), C.uintptr_t(_swig_i_2))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func (arg1 SwigcptrGeometricDevice) Get_name() (_swig_ret string) {
	var swig_r string
	_swig_i_0 := arg1
	swig_r_p := C._wrap_GeometricDevice_get_name_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func (arg1 SwigcptrGeometricDevice) Get_abs_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricDevice_get_abs_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrGeometricDevice) Get_offset_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricDevice_get_offset_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrGeometricDevice) Get_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricDevice_get_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrGeometricDevice) Override_offset_position_mm(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	C._wrap_GeometricDevice_override_offset_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))
}

func DeleteGeometricDevice(arg1 GeometricDevice) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_GeometricDevice_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

type GeometricDevice interface {
	Swigcptr() uintptr
	SwigIsGeometricDevice()
	Get_name() (_swig_ret string)
	Get_abs_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_offset_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Override_offset_position_mm(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
}

const HEIGHT_46_INCHES_MM int = 1170
const HEIGHT_32_INCHES_MM int = 810
const HEIGHT_36_INCHES_MM float64 = 914.4
const HEIGHT_17_INCHES_MM float64 = 431.8
const HEIGHT_23_INCHES_MM float64 = 584.2
const HEIGHT_27_INCHES_MM float64 = 685.8
const HEIGHT_30_INCHES_MM int = 762
const MIN_DELTA_PX int = 10
const MAX_DELTA_PX int = 2000
const MIN_DELTA_MM int = 3
const MAX_DELTA_MM int = 500
const GLOBAL_HEIGHT_ESTIMATE_SMOOTHING_FACTOR float64 = 0.9

func IN_2_MM(arg1 float64) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	swig_r = (float64)(C._wrap_IN_2_MM_geometric_cam_8835f2338e69cff5(C.double(_swig_i_0)))
	return swig_r
}

type SwigcptrGeometricCamHeightCollector uintptr

func (p SwigcptrGeometricCamHeightCollector) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrGeometricCamHeightCollector) SwigIsGeometricCamHeightCollector() {
}

func NewGeometricCamHeightCollector(arg1 string, arg2 float64, arg3 float64, arg4 float64, arg5 float64, arg6 uint) (_swig_ret GeometricCamHeightCollector) {
	var swig_r GeometricCamHeightCollector
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	_swig_i_4 := arg5
	_swig_i_5 := arg6
	swig_r = (GeometricCamHeightCollector)(SwigcptrGeometricCamHeightCollector(C._wrap_new_GeometricCamHeightCollector_geometric_cam_8835f2338e69cff5(*(*C.swig_type_12)(unsafe.Pointer(&_swig_i_0)), C.double(_swig_i_1), C.double(_swig_i_2), C.double(_swig_i_3), C.double(_swig_i_4), C.swig_intgo(_swig_i_5))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func (arg1 SwigcptrGeometricCamHeightCollector) Set_columns(arg2 int64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_GeometricCamHeightCollector_set_columns_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_type_13(_swig_i_1))
}

func (arg1 SwigcptrGeometricCamHeightCollector) Average__SWIG_0() (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	swig_r = (float64)(C._wrap_GeometricCamHeightCollector_average__SWIG_0_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrGeometricCamHeightCollector) Average__SWIG_1(arg2 uint) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (float64)(C._wrap_GeometricCamHeightCollector_average__SWIG_1_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1)))
	return swig_r
}

func (p SwigcptrGeometricCamHeightCollector) Average(a ...interface{}) float64 {
	argc := len(a)
	if argc == 0 {
		return p.Average__SWIG_0()
	}
	if argc == 1 {
		return p.Average__SWIG_1(a[0].(uint))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricCamHeightCollector) Average_for_column(arg2 uint) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (float64)(C._wrap_GeometricCamHeightCollector_average_for_column_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.swig_intgo(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrGeometricCamHeightCollector) Push_datapoint__SWIG_0(arg2 float64, arg3 int64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	C._wrap_GeometricCamHeightCollector_push_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1), C.swig_type_14(_swig_i_2))
}

func (arg1 SwigcptrGeometricCamHeightCollector) Push_datapoint__SWIG_1(arg2 float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_GeometricCamHeightCollector_push_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1))
}

func (p SwigcptrGeometricCamHeightCollector) Push_datapoint(a ...interface{}) {
	argc := len(a)
	if argc == 1 {
		p.Push_datapoint__SWIG_1(a[0].(float64))
		return
	}
	if argc == 2 {
		p.Push_datapoint__SWIG_0(a[0].(float64), a[1].(int64))
		return
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricCamHeightCollector) Force_update_height(arg2 float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_GeometricCamHeightCollector_force_update_height_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1))
}

func (arg1 SwigcptrGeometricCamHeightCollector) Get_column_for_abs_pos_mm(arg2 float64, arg3 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret uint) {
	var swig_r uint
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3.Swigcptr()
	swig_r = (uint)(C._wrap_GeometricCamHeightCollector_get_column_for_abs_pos_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1), C.uintptr_t(_swig_i_2)))
	return swig_r
}

func (arg1 SwigcptrGeometricCamHeightCollector) Get_column_for_pos_x_px(arg2 float64) (_swig_ret uint) {
	var swig_r uint
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (uint)(C._wrap_GeometricCamHeightCollector_get_column_for_pos_x_px_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrGeometricCamHeightCollector) Averages_for_all_columns() (_swig_ret Vector) {
	var swig_r Vector
	_swig_i_0 := arg1
	swig_r = (Vector)(SwigcptrVector(C._wrap_GeometricCamHeightCollector_averages_for_all_columns_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func DeleteGeometricCamHeightCollector(arg1 GeometricCamHeightCollector) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_GeometricCamHeightCollector_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

type GeometricCamHeightCollector interface {
	Swigcptr() uintptr
	SwigIsGeometricCamHeightCollector()
	Set_columns(arg2 int64)
	Average(a ...interface{}) float64
	Average_for_column(arg2 uint) (_swig_ret float64)
	Push_datapoint(a ...interface{})
	Force_update_height(arg2 float64)
	Get_column_for_abs_pos_mm(arg2 float64, arg3 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret uint)
	Get_column_for_pos_x_px(arg2 float64) (_swig_ret uint)
	Averages_for_all_columns() (_swig_ret Vector)
}

type SwigcptrGeometricHeightEstimator uintptr

func (p SwigcptrGeometricHeightEstimator) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrGeometricHeightEstimator) SwigIsGeometricHeightEstimator() {
}

func NewGeometricHeightEstimator(arg1 float64, arg2 float64, arg3 float64, arg4 float64, arg5 float64, arg6 float64, arg7 float64, arg8 float64) (_swig_ret GeometricHeightEstimator) {
	var swig_r GeometricHeightEstimator
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	_swig_i_4 := arg5
	_swig_i_5 := arg6
	_swig_i_6 := arg7
	_swig_i_7 := arg8
	swig_r = (GeometricHeightEstimator)(SwigcptrGeometricHeightEstimator(C._wrap_new_GeometricHeightEstimator_geometric_cam_8835f2338e69cff5(C.double(_swig_i_0), C.double(_swig_i_1), C.double(_swig_i_2), C.double(_swig_i_3), C.double(_swig_i_4), C.double(_swig_i_5), C.double(_swig_i_6), C.double(_swig_i_7))))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Configure(arg2 Std_vector_Sl_std_string_Sg_, arg3 int, arg4 int) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	C._wrap_GeometricHeightEstimator_configure_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.swig_intgo(_swig_i_2), C.swig_intgo(_swig_i_3))
}

func (arg1 SwigcptrGeometricHeightEstimator) Push_computed_height_datapoint__SWIG_0(arg2 GeometricCam, arg3 float64, arg4 uint) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	C._wrap_GeometricHeightEstimator_push_computed_height_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.double(_swig_i_2), C.swig_intgo(_swig_i_3))
}

func (arg1 SwigcptrGeometricHeightEstimator) Push_computed_height_datapoint__SWIG_1(arg2 GeometricCam, arg3 float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	C._wrap_GeometricHeightEstimator_push_computed_height_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.double(_swig_i_2))
}

func (p SwigcptrGeometricHeightEstimator) Push_computed_height_datapoint(a ...interface{}) {
	argc := len(a)
	if argc == 2 {
		p.Push_computed_height_datapoint__SWIG_1(a[0].(GeometricCam), a[1].(float64))
		return
	}
	if argc == 3 {
		p.Push_computed_height_datapoint__SWIG_0(a[0].(GeometricCam), a[1].(float64), a[2].(uint))
		return
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricHeightEstimator) Push_height_datapoint__SWIG_0(arg2 GeometricCam, arg3 Std_tuple_Sl_double_Sc_double_Sg_, arg4 Std_tuple_Sl_double_Sc_double_Sg_, arg5 uint) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	_swig_i_4 := arg5
	C._wrap_GeometricHeightEstimator_push_height_datapoint__SWIG_0_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3), C.swig_intgo(_swig_i_4))
}

func (arg1 SwigcptrGeometricHeightEstimator) Push_height_datapoint__SWIG_1(arg2 GeometricCam, arg3 Std_tuple_Sl_double_Sc_double_Sg_, arg4 Std_tuple_Sl_double_Sc_double_Sg_) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	C._wrap_GeometricHeightEstimator_push_height_datapoint__SWIG_1_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3))
}

func (p SwigcptrGeometricHeightEstimator) Push_height_datapoint(a ...interface{}) {
	argc := len(a)
	if argc == 3 {
		p.Push_height_datapoint__SWIG_1(a[0].(GeometricCam), a[1].(Std_tuple_Sl_double_Sc_double_Sg_), a[2].(Std_tuple_Sl_double_Sc_double_Sg_))
		return
	}
	if argc == 4 {
		p.Push_height_datapoint__SWIG_0(a[0].(GeometricCam), a[1].(Std_tuple_Sl_double_Sc_double_Sg_), a[2].(Std_tuple_Sl_double_Sc_double_Sg_), a[3].(uint))
		return
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricHeightEstimator) Push_height_datapoint_with_pos_x(arg2 GeometricCam, arg3 Std_tuple_Sl_double_Sc_double_Sg_, arg4 Std_tuple_Sl_double_Sc_double_Sg_, arg5 uint) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	_swig_i_4 := arg5
	C._wrap_GeometricHeightEstimator_push_height_datapoint_with_pos_x_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3), C.swig_intgo(_swig_i_4))
}

func (arg1 SwigcptrGeometricHeightEstimator) Push_computed_height_datapoint_with_pos_x(arg2 GeometricCam, arg3 float64, arg4 uint) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	C._wrap_GeometricHeightEstimator_push_computed_height_datapoint_with_pos_x_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.double(_swig_i_2), C.swig_intgo(_swig_i_3))
}

func (arg1 SwigcptrGeometricHeightEstimator) Force_update_height(arg2 float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_GeometricHeightEstimator_force_update_height_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1))
}

func (arg1 SwigcptrGeometricHeightEstimator) Toggle_estimation(arg2 bool) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	C._wrap_GeometricHeightEstimator_toggle_estimation_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C._Bool(_swig_i_1))
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_ground_position_z_mm() (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	swig_r = (float64)(C._wrap_GeometricHeightEstimator_get_ground_position_z_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_ground_position_z_mm_by_cam(arg2 GeometricCam, arg3 uint) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	swig_r = (float64)(C._wrap_GeometricHeightEstimator_get_ground_position_z_mm_by_cam_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.swig_intgo(_swig_i_2)))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_ground_position_z_mm_by_cam_name(arg2 string, arg3 uint) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	swig_r = (float64)(C._wrap_GeometricHeightEstimator_get_ground_position_z_mm_by_cam_name_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), *(*C.swig_type_15)(unsafe.Pointer(&_swig_i_1)), C.swig_intgo(_swig_i_2)))
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_averages_for_all_columns(arg2 GeometricCam) (_swig_ret Vector) {
	var swig_r Vector
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Vector)(SwigcptrVector(C._wrap_GeometricHeightEstimator_get_averages_for_all_columns_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Set_min_max_x_mm_for_cam(arg2 GeometricCam) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	C._wrap_GeometricHeightEstimator_set_min_max_x_mm_for_cam_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_keys_for_abs_x(arg2 float64) (_swig_ret Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_) {
	var swig_r Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_)(SwigcptrStd_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_(C._wrap_GeometricHeightEstimator_get_keys_for_abs_x_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_height_mm_for_key(arg2 Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (float64)(C._wrap_GeometricHeightEstimator_get_height_mm_for_key_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_height_mm_for_abs_x(arg2 float64) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (float64)(C._wrap_GeometricHeightEstimator_get_height_mm_for_abs_x_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrGeometricHeightEstimator) Get_max_height_mm() (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	swig_r = (float64)(C._wrap_GeometricHeightEstimator_get_max_height_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0)))
	return swig_r
}

func DeleteGeometricHeightEstimator(arg1 GeometricHeightEstimator) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_GeometricHeightEstimator_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

type GeometricHeightEstimator interface {
	Swigcptr() uintptr
	SwigIsGeometricHeightEstimator()
	Configure(arg2 Std_vector_Sl_std_string_Sg_, arg3 int, arg4 int)
	Push_computed_height_datapoint(a ...interface{})
	Push_height_datapoint(a ...interface{})
	Push_height_datapoint_with_pos_x(arg2 GeometricCam, arg3 Std_tuple_Sl_double_Sc_double_Sg_, arg4 Std_tuple_Sl_double_Sc_double_Sg_, arg5 uint)
	Push_computed_height_datapoint_with_pos_x(arg2 GeometricCam, arg3 float64, arg4 uint)
	Force_update_height(arg2 float64)
	Toggle_estimation(arg2 bool)
	Get_ground_position_z_mm() (_swig_ret float64)
	Get_ground_position_z_mm_by_cam(arg2 GeometricCam, arg3 uint) (_swig_ret float64)
	Get_ground_position_z_mm_by_cam_name(arg2 string, arg3 uint) (_swig_ret float64)
	Get_averages_for_all_columns(arg2 GeometricCam) (_swig_ret Vector)
	Set_min_max_x_mm_for_cam(arg2 GeometricCam)
	Get_keys_for_abs_x(arg2 float64) (_swig_ret Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_)
	Get_height_mm_for_key(arg2 Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_) (_swig_ret float64)
	Get_height_mm_for_abs_x(arg2 float64) (_swig_ret float64)
	Get_max_height_mm() (_swig_ret float64)
}

func Construct_geometric_height_estimator() (_swig_ret Std_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_) {
	var swig_r Std_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_
	swig_r = (Std_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_)(SwigcptrStd_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_(C._wrap_construct_geometric_height_estimator_geometric_cam_8835f2338e69cff5()))
	return swig_r
}

type SwigcptrGeometricCam uintptr

func (p SwigcptrGeometricCam) Swigcptr() uintptr {
	return (uintptr)(p)
}

func (p SwigcptrGeometricCam) SwigIsGeometricCam() {
}

func NewGeometricCam__SWIG_0(arg1 string, arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_, arg3 Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_, arg4 float64, arg5 float64, arg6 Std_tuple_Sl_double_Sc_double_Sg_, arg7 Torch_Tensor, arg8 Torch_Tensor, arg9 Torch_Tensor, arg10 Torch_Tensor) (_swig_ret GeometricCam) {
	var swig_r GeometricCam
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4
	_swig_i_4 := arg5
	_swig_i_5 := arg6.Swigcptr()
	_swig_i_6 := arg7.Swigcptr()
	_swig_i_7 := arg8.Swigcptr()
	_swig_i_8 := arg9.Swigcptr()
	_swig_i_9 := arg10.Swigcptr()
	swig_r = (GeometricCam)(SwigcptrGeometricCam(C._wrap_new_GeometricCam__SWIG_0_geometric_cam_8835f2338e69cff5(*(*C.swig_type_16)(unsafe.Pointer(&_swig_i_0)), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2), C.double(_swig_i_3), C.double(_swig_i_4), C.uintptr_t(_swig_i_5), C.uintptr_t(_swig_i_6), C.uintptr_t(_swig_i_7), C.uintptr_t(_swig_i_8), C.uintptr_t(_swig_i_9))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewGeometricCam__SWIG_1(arg1 string, arg2 string, arg3 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_, arg4 Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_, arg5 float64, arg6 float64, arg7 Std_tuple_Sl_double_Sc_double_Sg_, arg8 Torch_Tensor, arg9 Torch_Tensor, arg10 Torch_Tensor, arg11 Torch_Tensor) (_swig_ret GeometricCam) {
	var swig_r GeometricCam
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3.Swigcptr()
	_swig_i_3 := arg4.Swigcptr()
	_swig_i_4 := arg5
	_swig_i_5 := arg6
	_swig_i_6 := arg7.Swigcptr()
	_swig_i_7 := arg8.Swigcptr()
	_swig_i_8 := arg9.Swigcptr()
	_swig_i_9 := arg10.Swigcptr()
	_swig_i_10 := arg11.Swigcptr()
	swig_r = (GeometricCam)(SwigcptrGeometricCam(C._wrap_new_GeometricCam__SWIG_1_geometric_cam_8835f2338e69cff5(*(*C.swig_type_17)(unsafe.Pointer(&_swig_i_0)), *(*C.swig_type_18)(unsafe.Pointer(&_swig_i_1)), C.uintptr_t(_swig_i_2), C.uintptr_t(_swig_i_3), C.double(_swig_i_4), C.double(_swig_i_5), C.uintptr_t(_swig_i_6), C.uintptr_t(_swig_i_7), C.uintptr_t(_swig_i_8), C.uintptr_t(_swig_i_9), C.uintptr_t(_swig_i_10))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	if Swig_escape_always_false {
		Swig_escape_val = arg2
	}
	return swig_r
}

func NewGeometricCam__SWIG_2(arg1 string, arg2 float64, arg3 float64, arg4 float64, arg5 uint, arg6 uint, arg7 float64, arg8 float64, arg9 float64, arg10 float64, arg11 VectorVectorDouble, arg12 Vector) (_swig_ret GeometricCam) {
	var swig_r GeometricCam
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	_swig_i_4 := arg5
	_swig_i_5 := arg6
	_swig_i_6 := arg7
	_swig_i_7 := arg8
	_swig_i_8 := arg9
	_swig_i_9 := arg10
	_swig_i_10 := arg11.Swigcptr()
	_swig_i_11 := arg12.Swigcptr()
	swig_r = (GeometricCam)(SwigcptrGeometricCam(C._wrap_new_GeometricCam__SWIG_2_geometric_cam_8835f2338e69cff5(*(*C.swig_type_19)(unsafe.Pointer(&_swig_i_0)), C.double(_swig_i_1), C.double(_swig_i_2), C.double(_swig_i_3), C.swig_intgo(_swig_i_4), C.swig_intgo(_swig_i_5), C.double(_swig_i_6), C.double(_swig_i_7), C.double(_swig_i_8), C.double(_swig_i_9), C.uintptr_t(_swig_i_10), C.uintptr_t(_swig_i_11))))
	if Swig_escape_always_false {
		Swig_escape_val = arg1
	}
	return swig_r
}

func NewGeometricCam(a ...interface{}) GeometricCam {
	argc := len(a)
	if argc == 10 {
		return NewGeometricCam__SWIG_0(a[0].(string), a[1].(Std_tuple_Sl_double_Sc_double_Sc_double_Sg_), a[2].(Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_), a[3].(float64), a[4].(float64), a[5].(Std_tuple_Sl_double_Sc_double_Sg_), a[6].(Torch_Tensor), a[7].(Torch_Tensor), a[8].(Torch_Tensor), a[9].(Torch_Tensor))
	}
	if argc == 11 {
		return NewGeometricCam__SWIG_1(a[0].(string), a[1].(string), a[2].(Std_tuple_Sl_double_Sc_double_Sc_double_Sg_), a[3].(Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_), a[4].(float64), a[5].(float64), a[6].(Std_tuple_Sl_double_Sc_double_Sg_), a[7].(Torch_Tensor), a[8].(Torch_Tensor), a[9].(Torch_Tensor), a[10].(Torch_Tensor))
	}
	if argc == 12 {
		return NewGeometricCam__SWIG_2(a[0].(string), a[1].(float64), a[2].(float64), a[3].(float64), a[4].(uint), a[5].(uint), a[6].(float64), a[7].(float64), a[8].(float64), a[9].(float64), a[10].(VectorVectorDouble), a[11].(Vector))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricCam) Is_point_in_camera(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (bool)(C._wrap_GeometricCam_is_point_in_camera_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Is_point_below_camera(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret bool) {
	var swig_r bool
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (bool)(C._wrap_GeometricCam_is_point_below_camera_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1)))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Undistort_point(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_undistort_point_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Distort_point(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_distort_point_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_sensor_mm_from_centered_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_get_sensor_mm_from_centered_px_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_sensor_px_from_mm(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_get_sensor_px_from_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_size_mm_from_size_px__SWIG_0(arg2 float64, arg3 float64) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	swig_r = (float64)(C._wrap_GeometricCam_get_size_mm_from_size_px__SWIG_0_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1), C.double(_swig_i_2)))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_size_mm_from_size_px__SWIG_1(arg2 float64) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (float64)(C._wrap_GeometricCam_get_size_mm_from_size_px__SWIG_1_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1)))
	return swig_r
}

func (p SwigcptrGeometricCam) Get_size_mm_from_size_px(a ...interface{}) float64 {
	argc := len(a)
	if argc == 1 {
		return p.Get_size_mm_from_size_px__SWIG_1(a[0].(float64))
	}
	if argc == 2 {
		return p.Get_size_mm_from_size_px__SWIG_0(a[0].(float64), a[1].(float64))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricCam) Get_size_px_from_size_mm__SWIG_0(arg2 float64, arg3 float64) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	swig_r = (float64)(C._wrap_GeometricCam_get_size_px_from_size_mm__SWIG_0_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1), C.double(_swig_i_2)))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_size_px_from_size_mm__SWIG_1(arg2 float64) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	swig_r = (float64)(C._wrap_GeometricCam_get_size_px_from_size_mm__SWIG_1_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1)))
	return swig_r
}

func (p SwigcptrGeometricCam) Get_size_px_from_size_mm(a ...interface{}) float64 {
	argc := len(a)
	if argc == 1 {
		return p.Get_size_px_from_size_mm__SWIG_1(a[0].(float64))
	}
	if argc == 2 {
		return p.Get_size_px_from_size_mm__SWIG_0(a[0].(float64), a[1].(float64))
	}
	panic("No match for overloaded function call")
}

func (arg1 SwigcptrGeometricCam) Compute_vertex_height(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	swig_r = (float64)(C._wrap_GeometricCam_compute_vertex_height_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2)))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Compute_ground_height(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret float64) {
	var swig_r float64
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3.Swigcptr()
	swig_r = (float64)(C._wrap_GeometricCam_compute_ground_height_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.uintptr_t(_swig_i_2)))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_abs_position_from_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_abs_position_from_px_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_abs_position_from_undistorted_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_abs_position_from_undistorted_px_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_abs_position_from_px_from_height_estimate(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 float64) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_abs_position_from_px_from_height_estimate_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.double(_swig_i_2))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_abs_position_from_undistorted_px_from_height_estimate(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 float64) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	_swig_i_2 := arg3
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_abs_position_from_undistorted_px_from_height_estimate_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1), C.double(_swig_i_2))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_undistorted_px_from_abs_position(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_get_undistorted_px_from_abs_position_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_distorted_px_from_abs_position(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_get_distorted_px_from_abs_position_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_distorted_px_from_abs_position_swig(arg2 float64, arg3 float64, arg4 float64, arg5 *float64, arg6 *float64) {
	_swig_i_0 := arg1
	_swig_i_1 := arg2
	_swig_i_2 := arg3
	_swig_i_3 := arg4
	_swig_i_4 := arg5
	_swig_i_5 := arg6
	C._wrap_GeometricCam_get_distorted_px_from_abs_position_swig_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.double(_swig_i_1), C.double(_swig_i_2), C.double(_swig_i_3), C.swig_voidp(_swig_i_4), C.swig_voidp(_swig_i_5))
}

func (arg1 SwigcptrGeometricCam) Get_delta_mm_from_delta_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	_swig_i_1 := arg2.Swigcptr()
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_get_delta_mm_from_delta_px_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0), C.uintptr_t(_swig_i_1))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Get_min_max_abs_x_mm_for_cam() (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_tuple_Sl_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sg_(C._wrap_GeometricCam_get_min_max_abs_x_mm_for_cam_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) Resolution() (_swig_ret Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_) {
	var swig_r Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_
	_swig_i_0 := arg1
	swig_r = (Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_)(SwigcptrStd_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_(C._wrap_GeometricCam_resolution_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) D2u_mapx() (_swig_ret Torch_Tensor) {
	var swig_r Torch_Tensor
	_swig_i_0 := arg1
	swig_r = (Torch_Tensor)(SwigcptrTorch_Tensor(C._wrap_GeometricCam_d2u_mapx_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func (arg1 SwigcptrGeometricCam) D2u_mapy() (_swig_ret Torch_Tensor) {
	var swig_r Torch_Tensor
	_swig_i_0 := arg1
	swig_r = (Torch_Tensor)(SwigcptrTorch_Tensor(C._wrap_GeometricCam_d2u_mapy_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))))
	return swig_r
}

func DeleteGeometricCam(arg1 GeometricCam) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_delete_GeometricCam_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_i_0))
}

func (_swig_base SwigcptrGeometricCam) Get_name() (_swig_ret string) {
	var swig_r string
	swig_r_p := C._wrap_GeometricCam_get_name_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_base))
	swig_r = *(*string)(unsafe.Pointer(&swig_r_p))
	var swig_r_1 string
	swig_r_1 = swigCopyString(swig_r)
	return swig_r_1
}

func (_swig_base SwigcptrGeometricCam) Get_abs_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_abs_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_base))))
	return swig_r
}

func (_swig_base SwigcptrGeometricCam) Get_offset_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_offset_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_base))))
	return swig_r
}

func (_swig_base SwigcptrGeometricCam) Get_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	var swig_r Std_tuple_Sl_double_Sc_double_Sc_double_Sg_
	swig_r = (Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)(SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_(C._wrap_GeometricCam_get_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_base))))
	return swig_r
}

func (_swig_base SwigcptrGeometricCam) Override_offset_position_mm(arg1 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) {
	_swig_i_0 := arg1.Swigcptr()
	C._wrap_GeometricCam_override_offset_position_mm_geometric_cam_8835f2338e69cff5(C.uintptr_t(_swig_base), C.uintptr_t(_swig_i_0))
}

func (p SwigcptrGeometricCam) SwigIsGeometricDevice() {
}

func (p SwigcptrGeometricCam) SwigGetGeometricDevice() GeometricDevice {
	return SwigcptrGeometricDevice(p.Swigcptr())
}

type GeometricCam interface {
	Swigcptr() uintptr
	SwigIsGeometricCam()
	Is_point_in_camera(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret bool)
	Is_point_below_camera(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret bool)
	Undistort_point(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Distort_point(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Get_sensor_mm_from_centered_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Get_sensor_px_from_mm(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Get_size_mm_from_size_px(a ...interface{}) float64
	Get_size_px_from_size_mm(a ...interface{}) float64
	Compute_vertex_height(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret float64)
	Compute_ground_height(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret float64)
	Get_abs_position_from_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_abs_position_from_undistorted_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_abs_position_from_px_from_height_estimate(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 float64) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_abs_position_from_undistorted_px_from_height_estimate(arg2 Std_tuple_Sl_double_Sc_double_Sg_, arg3 float64) (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_undistorted_px_from_abs_position(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Get_distorted_px_from_abs_position(arg2 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Get_distorted_px_from_abs_position_swig(arg2 float64, arg3 float64, arg4 float64, arg5 *float64, arg6 *float64)
	Get_delta_mm_from_delta_px(arg2 Std_tuple_Sl_double_Sc_double_Sg_) (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Get_min_max_abs_x_mm_for_cam() (_swig_ret Std_tuple_Sl_double_Sc_double_Sg_)
	Resolution() (_swig_ret Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_)
	D2u_mapx() (_swig_ret Torch_Tensor)
	D2u_mapy() (_swig_ret Torch_Tensor)
	Get_name() (_swig_ret string)
	Get_abs_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_offset_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Get_position_mm() (_swig_ret Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	Override_offset_position_mm(arg1 Std_tuple_Sl_double_Sc_double_Sc_double_Sg_)
	SwigIsGeometricDevice()
	SwigGetGeometricDevice() GeometricDevice
}

type SwigcptrStd_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_ uintptr
type Std_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_tuple_Sl_std_string_Sc_unsigned_SS_int_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_ uintptr
type Std_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_tuple_Sl_unsigned_SS_int_Sc_unsigned_SS_int_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_ uintptr
type Std_tuple_Sl_double_Sc_double_Sc_double_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_tuple_Sl_double_Sc_double_Sc_double_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_tuple_Sl_double_Sc_double_Sg_ uintptr
type Std_tuple_Sl_double_Sc_double_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_tuple_Sl_double_Sc_double_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_vector_Sl_std_string_Sg_ uintptr
type Std_vector_Sl_std_string_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_vector_Sl_std_string_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrStd_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_ uintptr
type Std_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_ interface {
	Swigcptr() uintptr
}

func (p SwigcptrStd_shared_ptr_Sl_lib_common_geometric_GeometricHeightEstimator_Sg_) Swigcptr() uintptr {
	return uintptr(p)
}

type SwigcptrTorch_Tensor uintptr
type Torch_Tensor interface {
	Swigcptr() uintptr
}

func (p SwigcptrTorch_Tensor) Swigcptr() uintptr {
	return uintptr(p)
}

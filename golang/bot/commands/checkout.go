package commands

import (
	"fmt"
	"os"
	"strings"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/util"
	"github.com/sirupsen/logrus"
)

func BotCheckout(user string) {
	if user == "" {
		user = os.Getenv("CARBON_USER")
	}

	logrus.Info("Checking out for user: ", user)

	if user == "" {
		logrus.Fatal("Invalid User")
	}

	checkout, err := bot_context.LoadCheckoutDef()
	util.LogFatalError(err)

	err = checkout.Checkout(user)
	util.LogFatalError(err)
}

func BotReturn() {
	checkout, err := bot_context.LoadCheckoutDef()
	util.LogFatalError(err)

	err = checkout.Return()
	util.LogFatalError(err)
}

func BotConfig() {
	logrus.Info("Bot Config: ")

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	output := fmt.Sprintf("Robot:%+v\nCheckout:%+v\nEnv:%+v\nVersion:%+v", botDef.RobotDef, botDef.CheckoutDef, botDef.EnvDef, botDef.VersionDef)
	output = strings.Replace(output, "&", "", -1)
	output = strings.Replace(output, " ", "\n  ", -1)
	output = strings.Replace(output, "{", "\n  ", -1)
	output = strings.Replace(output, "}", "\n", -1)
	fmt.Println(output)
}

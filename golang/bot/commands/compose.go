package commands

import (
	"fmt"
	"os"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/jedib0t/go-pretty/v6/text"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/docker"
	"github.com/carbonrobotics/robot/golang/bot/util"
	"github.com/sirupsen/logrus"
)

func describeServices(services []string) string {
	if len(services) == 0 {
		return "all services"
	}
	return fmt.Sprint(services)
}

func BotStart(services []string) {
	logrus.Info("Bot Start: ", describeServices(services))
	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	err = bot_context.SetupConfigs(botDef)
	util.LogFatalError(err)

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	service, err := docker.GetComposeService()
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	if len(services) == 0 {
		services = role.Services
	}

	err = docker.CleanupContainers(cli, services)
	util.LogFatalError(err)

	err = docker.StartServices(cli, service, project, services)
	util.LogFatalError(err)
}

func BotStop(services []string) {
	logrus.Info("Bot Stop: ", describeServices(services))
	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	service, err := docker.GetComposeService()
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	if len(services) == 0 {
		services = role.Services
	}

	err = docker.StopServices(cli, service, project, services)
	util.LogFatalError(err)
}

func BotTail(name string, lines int) {
	logrus.Info("Bot Tail: ", name)

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	err = docker.TailService(cli, name, lines)
	util.LogFatalError(err)
}

func BotStatus() {
	logrus.Info("Bot Status: ")

	botDef, err := bot_context.LoadBotDef()
	util.LogFatalError(err)

	cli, err := docker.GetDockerCLI()
	util.LogFatalError(err)

	role, err := bot_context.LoadRole(botDef.RobotDef.Computer.Role, botDef.RobotDef.Computer.ExtraRoles)
	util.LogFatalError(err)

	service, err := docker.GetComposeService()
	util.LogFatalError(err)

	project, err := bot_context.LoadComposeProject(botDef, role)
	util.LogFatalError(err)

	summary, err := docker.PsServices(cli, service, project, role.Services)
	util.LogFatalError(err)

	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.AppendHeader(table.Row{"Service", "State", "Exit Code"})

	for _, item := range summary {
		t.AppendRow(table.Row{item.Service, item.State, item.ExitCode})
	}
	t.SetStyle(table.StyleColoredRedWhiteOnBlack)
	t.Style().Color.Header = text.Colors{text.BgGreen, text.FgHiWhite, text.Bold}
	t.Style().Color.RowAlternate = text.Colors{text.BgHiBlack, text.FgHiWhite}
	t.Render()
}

package bot_context

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBotDef_GetEnvironmentMapping(t *testing.T) {
	testPlatform := "foo_platform"
	savePlatform := os.Getenv("HOST_PLATFORM")
	defer func() { os.Setenv("HOST_PLATFORM", savePlatform) }()
	if err := os.Setenv("HOST_PLATFORM", testPlatform); err != nil {
		t.Fatal(err)
	}

	testRobotDef := &RobotDef{
		Name:       "test-robot",
		Generation: "slayer",
		Computer: ComputerDef{
			Role: "testRole",
			Row:  "testRow",
		},
		AuthClientSecret:      "secret",
		AuthClientId:          "clientid",
		AuthDomain:            "foo-domain",
		CarbonRobotUsername:   "username",
		CarbonRobotPassword:   "password",
		MetricsJobFilterRegex: "^(*)$",
	}
	testCheckoutDef := &CheckoutDef{
		Mode: "testMode",
		User: "testUser",
	}
	testVersionDef := &VersionDef{
		Tag: "fooTag",
	}

	// general sanity check on values.
	expectedMapping := map[string]string{
		"MAKA_ROBOT_NAME":                    testRobotDef.Name,
		"MAKA_GEN":                           testRobotDef.Generation,
		"MAKA_ROW":                           testRobotDef.Computer.Row,
		"MAKA_ROLE":                          testRobotDef.Computer.Role,
		"MAKA_CALIBRATION_DIR":               "/calibration",
		"MAKA_CONFIG_DIR":                    "/config",
		"MAKA_ROBOT_DIR":                     "/robot",
		"MAKA_DATA_DIR":                      "/data",
		"MAKA_LOG_DIR":                       "/logs",
		"MAKA_MODEL_DIR":                     "/models",
		"MAKA_BOT_DIR":                       "/bot",
		"MAKA_MEDIA_DIR":                     "/media",
		"MAKA_BOT_CONFIG_DIR":                "/config/bot",
		"MAKA_AUTH_CLIENT_SECRET":            testRobotDef.AuthClientSecret,
		"MAKA_AUTH_CLIENT_ID":                testRobotDef.AuthClientId,
		"MAKA_AUTH_DOMAIN":                   testRobotDef.AuthDomain,
		"MAKA_AUTH_SCOPES":                   "",
		"CARBON_MODE":                        testCheckoutDef.Mode,
		"CARBON_USER":                        testCheckoutDef.User,
		"CARBON_VERSION_TAG":                 testVersionDef.Tag,
		"GOCACHE":                            "/calibration/.cache/go-build",
		"GOMODCACHE":                         "/calibration/.cache/go-mod-cache",
		"CCACHE_DIR":                         "/calibration/.cache/.ccache",
		"HOST_PLATFORM":                      testPlatform,
		"SENTRY_RELEASE":                     testVersionDef.Tag,
		"SENTRY_DSN":                         "https://<EMAIL>/6169686",
		"CARBON_ROBOT_USERNAME":              testRobotDef.CarbonRobotUsername,
		"CARBON_ROBOT_PASSWORD":              testRobotDef.CarbonRobotPassword,
		"METRICS_JOB_FILTER_REGEX":           testRobotDef.MetricsJobFilterRegex,
		"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
		"PROMETHEUS_RW_CAPACITY":             "100000",
		"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
		"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
		"PROMETHEUS_RW_MAX_SHARDS":           "20",
		"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
		"PROMETHEUS_RW_MIN_SHARDS":           "1",
		"CARBON_CONFIG_CLOUD_HOST":           "rosy-stg-grpc.cloud.carbonrobotics.com",
		"CARBON_IDM_HOST":                    "idm-stg.cloud.carbonrobotics.com",
		"CARBON_INGEST_HOST":                 "ingest-stg.cloud.carbonrobotics.com",
		"CARBON_PORTAL_HOST":                 "portal-grpc-stg.cloud.carbonrobotics.com",
		"CARBON_VESELKA_HOST":                "veselka-stg.cloud.carbonrobotics.com",
		"CARBON_VERSION_METADATA_HOST":       "versionmetadata-stg.cloud.carbonrobotics.com",
		"CARBON_CC_HOST":                     "*********",
		"CARBON_RTC_SIGNAL_HOST":             "signal-rtc-stg.cloud.carbonrobotics.com",
		"CARBON_RTC_JOBS_GRPC_HOST":          "rtc-jobs-grpc-stg.cloud.carbonrobotics.com",
		"CARBON_RTC_LOCATOR_GRPC_HOST":       "rtc-locator-grpc-stg.cloud.carbonrobotics.com",
	}

	botDef := &BotDef{
		RobotDef:    testRobotDef,
		VersionDef:  testVersionDef,
		CheckoutDef: testCheckoutDef,
	}
	got := botDef.GetEnvironmentMapping()
	assert.Equal(t, expectedMapping, got)
}

func Test_setComputedValues(t *testing.T) {
	tests := []struct {
		name          string
		authDomain    string
		environment   string
		hostOverrides map[string]string
		expected      map[string]string
	}{
		{
			"no auth domain or env",
			"",
			"",
			nil,
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           ConfigServiceCloudProdHost,
				"CARBON_IDM_HOST":                    IDMServiceProdHost,
				"CARBON_INGEST_HOST":                 IngestServiceProdHost,
				"CARBON_PORTAL_HOST":                 PortalServiceProdHost,
				"CARBON_VESELKA_HOST":                VeselkaServiceProdHost,
				"CARBON_VERSION_METADATA_HOST":       VersionMetadataServiceProdHost,
				"CARBON_RTC_SIGNAL_HOST":             RtcSignalProdHost,
				"CARBON_RTC_JOBS_GRPC_HOST":          RtcJobsGrpcProdHost,
				"CARBON_RTC_LOCATOR_GRPC_HOST":       RtcLocatorGrpcProdHost,
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
		{
			"no auth domain overrides env",
			AuthDomainProd,
			"foo",
			nil,
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           ConfigServiceCloudProdHost,
				"CARBON_IDM_HOST":                    IDMServiceProdHost,
				"CARBON_INGEST_HOST":                 IngestServiceProdHost,
				"CARBON_PORTAL_HOST":                 PortalServiceProdHost,
				"CARBON_VESELKA_HOST":                VeselkaServiceProdHost,
				"CARBON_VERSION_METADATA_HOST":       VersionMetadataServiceProdHost,
				"CARBON_RTC_SIGNAL_HOST":             RtcSignalProdHost,
				"CARBON_RTC_JOBS_GRPC_HOST":          RtcJobsGrpcProdHost,
				"CARBON_RTC_LOCATOR_GRPC_HOST":       RtcLocatorGrpcProdHost,
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
		{
			"no auth domain staging env",
			"",
			"staging",
			nil,
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           ConfigServiceCloudStageHost,
				"CARBON_IDM_HOST":                    IDMServiceStageHost,
				"CARBON_INGEST_HOST":                 IngestServiceStageHost,
				"CARBON_PORTAL_HOST":                 PortalServiceStageHost,
				"CARBON_VESELKA_HOST":                VeselkaServiceStageHost,
				"CARBON_VERSION_METADATA_HOST":       VersionMetadataServiceStageHost,
				"CARBON_RTC_SIGNAL_HOST":             RtcSignalStageHost,
				"CARBON_RTC_JOBS_GRPC_HOST":          RtcJobsGrpcStageHost,
				"CARBON_RTC_LOCATOR_GRPC_HOST":       RtcLocatorGrpcStageHost,
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
		{
			"no auth domain testing env",
			"",
			"testing",
			nil,
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           ConfigServiceCloudTestHost,
				"CARBON_IDM_HOST":                    IDMServiceTestHost,
				"CARBON_INGEST_HOST":                 IngestServiceTestHost,
				"CARBON_PORTAL_HOST":                 PortalServiceTestHost,
				"CARBON_VESELKA_HOST":                VeselkaServiceTestHost,
				"CARBON_VERSION_METADATA_HOST":       VersionMetadataServiceTestHost,
				"CARBON_RTC_SIGNAL_HOST":             RtcSignalTestHost,
				"CARBON_RTC_JOBS_GRPC_HOST":          RtcJobsGrpcTestHost,
				"CARBON_RTC_LOCATOR_GRPC_HOST":       RtcLocatorGrpcTestHost,
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
		{
			"prod with overrides",
			"",
			"",
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":     "config-foo-host",
				"CARBON_IDM_HOST":              "IDM foo host",
				"CARBON_INGEST_HOST":           "Ingest bar host",
				"CARBON_PORTAL_HOST":           "portal bar host",
				"CARBON_VESELKA_HOST":          "veselka foo host",
				"CARBON_VERSION_METADATA_HOST": "vermeta foo bar",
				"CARBON_RTC_SIGNAL_HOST":       "rtc signal foo",
				"CARBON_RTC_JOBS_GRPC_HOST":    "rtc jobs foo",
				"CARBON_RTC_LOCATOR_GRPC_HOST": "rtc locator foo",
			},
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           "config-foo-host",
				"CARBON_IDM_HOST":                    "IDM foo host",
				"CARBON_INGEST_HOST":                 "Ingest bar host",
				"CARBON_PORTAL_HOST":                 "portal bar host",
				"CARBON_VESELKA_HOST":                "veselka foo host",
				"CARBON_VERSION_METADATA_HOST":       "vermeta foo bar",
				"CARBON_RTC_SIGNAL_HOST":             "rtc signal foo",
				"CARBON_RTC_JOBS_GRPC_HOST":          "rtc jobs foo",
				"CARBON_RTC_LOCATOR_GRPC_HOST":       "rtc locator foo",
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
		{
			"stage with overrides",
			"",
			"staging",
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":     "config-foo-host",
				"CARBON_IDM_HOST":              "IDM foo host",
				"CARBON_INGEST_HOST":           "Ingest bar host",
				"CARBON_PORTAL_HOST":           "portal bar host",
				"CARBON_VESELKA_HOST":          "veselka foo host",
				"CARBON_VERSION_METADATA_HOST": "vermeta foo bar",
				"CARBON_RTC_SIGNAL_HOST":       "rtc signal foo",
				"CARBON_RTC_JOBS_GRPC_HOST":    "rtc jobs foo",
				"CARBON_RTC_LOCATOR_GRPC_HOST": "rtc locator foo",
			},
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           "config-foo-host",
				"CARBON_IDM_HOST":                    "IDM foo host",
				"CARBON_INGEST_HOST":                 "Ingest bar host",
				"CARBON_PORTAL_HOST":                 "portal bar host",
				"CARBON_VESELKA_HOST":                "veselka foo host",
				"CARBON_VERSION_METADATA_HOST":       "vermeta foo bar",
				"CARBON_RTC_SIGNAL_HOST":             "rtc signal foo",
				"CARBON_RTC_JOBS_GRPC_HOST":          "rtc jobs foo",
				"CARBON_RTC_LOCATOR_GRPC_HOST":       "rtc locator foo",
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
		{
			"test with overrides",
			"",
			"testing",
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":     "config-foo-host",
				"CARBON_IDM_HOST":              "IDM foo host",
				"CARBON_INGEST_HOST":           "Ingest bar host",
				"CARBON_PORTAL_HOST":           "portal bar host",
				"CARBON_VESELKA_HOST":          "veselka foo host",
				"CARBON_VERSION_METADATA_HOST": "vermeta foo bar",
				"CARBON_RTC_SIGNAL_HOST":       "rtc signal foo",
				"CARBON_RTC_JOBS_GRPC_HOST":    "rtc jobs foo",
				"CARBON_RTC_LOCATOR_GRPC_HOST": "rtc locator foo",
			},
			map[string]string{
				"CARBON_CONFIG_CLOUD_HOST":           "config-foo-host",
				"CARBON_IDM_HOST":                    "IDM foo host",
				"CARBON_INGEST_HOST":                 "Ingest bar host",
				"CARBON_PORTAL_HOST":                 "portal bar host",
				"CARBON_VESELKA_HOST":                "veselka foo host",
				"CARBON_VERSION_METADATA_HOST":       "vermeta foo bar",
				"CARBON_RTC_SIGNAL_HOST":             "rtc signal foo",
				"CARBON_RTC_JOBS_GRPC_HOST":          "rtc jobs foo",
				"CARBON_RTC_LOCATOR_GRPC_HOST":       "rtc locator foo",
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			robot := &RobotDef{
				AuthDomain:          test.authDomain,
				Environment:         test.environment,
				CarbonHostOverrides: test.hostOverrides,
			}
			got := setComputedValues(robot, map[string]string{})
			assert.Equal(t, test.expected, got)
		})
	}
}

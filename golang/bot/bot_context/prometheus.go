package bot_context

import (
	"strconv"
	"time"
)

type PrometheusRemoteWriteConfig struct {
	Capacity          int    `json:"capacity,omitempty"`
	MinShards         int    `json:"min_shards,omitempty"`
	MaxShards         int    `json:"max_shards,omitempty"`
	MaxSamplesPerSend int    `json:"max_samples_per_send,omitempty"`
	BatchSendDeadline string `json:"batch_send_deadline,omitempty"`
	MinBackoff        string `json:"min_backoff,omitempty"`
	MaxBackoff        string `json:"max_backoff,omitempty"`
}

func (prwc PrometheusRemoteWriteConfig) EnvConfigMapping() map[string]string {
	return map[string]string{
		"PROMETHEUS_RW_CAPACITY":             intToStringWithDefault(prwc.Capacity, 100000),
		"PROMETHEUS_RW_MIN_SHARDS":           intToStringWithDefault(prwc.MinShards, 1),
		"PROMETHEUS_RW_MAX_SHARDS":           intToStringWithDefault(prwc.MaxShards, 20),
		"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": intToStringWithDefault(prwc.MaxSamplesPerSend, 10000),
		"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  ensureDurationWithDefault(prwc.BatchSendDeadline, "5m"),
		"PROMETHEUS_RW_MIN_BACKOFF":          ensureDurationWithDefault(prwc.MinBackoff, "1s"),
		"PROMETHEUS_RW_MAX_BACKOFF":          ensureDurationWithDefault(prwc.MaxBackoff, "5m"),
	}
}

func intToStringWithDefault(input, def int) string {
	if input != 0 {
		return strconv.Itoa(input)
	}
	return strconv.Itoa(def)
}
func ensureDurationWithDefault(input, def string) string {
	_, err := time.ParseDuration(input)
	if err != nil {
		return def
	}
	return input
}

package bot_context

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/carbonrobotics/robot/golang/bot/util"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/carbonrobotics/robot/golang/software_manager/system"
	"github.com/natefinch/atomic"
	"github.com/sirupsen/logrus"
)

const (
	AuthDomainProd                 = "carbonrobotics.us.auth0.com"
	IDMServiceProdHost             = "idm.cloud.carbonrobotics.com"
	IngestServiceProdHost          = "ingest.cloud.carbonrobotics.com"
	PortalServiceProdHost          = "portal-grpc.cloud.carbonrobotics.com"
	ConfigServiceCloudProdHost     = "rosy-grpc.cloud.carbonrobotics.com"
	VeselkaServiceProdHost         = "veselka.cloud.carbonrobotics.com"
	VersionMetadataServiceProdHost = "versionmetadata.cloud.carbonrobotics.com"
	RtcSignalProdHost              = "signal-rtc.cloud.carbonrobotics.com"
	RtcJobsGrpcProdHost            = "rtc-jobs-grpc.cloud.carbonrobotics.com"
	RtcLocatorGrpcProdHost         = "rtc-locator-grpc.cloud.carbonrobotics.com"

	IDMServiceStageHost             = "idm-stg.cloud.carbonrobotics.com"
	IngestServiceStageHost          = "ingest-stg.cloud.carbonrobotics.com"
	PortalServiceStageHost          = "portal-grpc-stg.cloud.carbonrobotics.com"
	ConfigServiceCloudStageHost     = "rosy-stg-grpc.cloud.carbonrobotics.com"
	VeselkaServiceStageHost         = "veselka-stg.cloud.carbonrobotics.com"
	VersionMetadataServiceStageHost = "versionmetadata-stg.cloud.carbonrobotics.com"
	RtcSignalStageHost              = "signal-rtc-stg.cloud.carbonrobotics.com"
	RtcJobsGrpcStageHost            = "rtc-jobs-grpc-stg.cloud.carbonrobotics.com"
	RtcLocatorGrpcStageHost         = "rtc-locator-grpc-stg.cloud.carbonrobotics.com"

	IDMServiceTestHost             = "idm-test.cloud.carbonrobotics.com"
	IngestServiceTestHost          = "ingest-test.cloud.carbonrobotics.com"
	PortalServiceTestHost          = "portal-grpc-test.cloud.carbonrobotics.com"
	ConfigServiceCloudTestHost     = "rosy-test-grpc.cloud.carbonrobotics.com"
	VeselkaServiceTestHost         = "veselka-test.cloud.carbonrobotics.com"
	VersionMetadataServiceTestHost = "versionmetadata-test.cloud.carbonrobotics.com"
	RtcSignalTestHost              = "signal-rtc-test.cloud.carbonrobotics.com"
	RtcJobsGrpcTestHost            = "rtc-jobs-grpc-test.cloud.carbonrobotics.com"
	RtcLocatorGrpcTestHost         = "rtc-locator-grpc-test.cloud.carbonrobotics.com"

	Production    = "production"
	Testing       = "testing"
	RobotRepoPath = "/robot"
)

type ComputerDef struct {
	Role       string   `json:"role"`
	Row        string   `json:"row"`
	ExtraRoles []string `json:"extra_roles"`
}

type RobotDef struct {
	Name                        string                      `json:"name"`
	Generation                  string                      `json:"generation"`
	Computer                    ComputerDef                 `json:"computer"`
	AuthDomain                  string                      `json:"auth_domain"`
	AuthClientId                string                      `json:"auth_client_id"`
	AuthClientSecret            string                      `json:"auth_client_secret"`
	Environment                 string                      `json:"environment"`
	CarbonRobotUsername         string                      `json:"robot_username"`
	CarbonRobotPassword         string                      `json:"robot_password"`
	MetricsJobFilterRegex       string                      `json:"metrics_job_filter_regex"`
	PrometheusRemoteWriteConfig PrometheusRemoteWriteConfig `json:"prometheus_remote_write_config,omitempty"`
	CarbonHostOverrides         map[string]string           `json:"carbon_host_overrides"`
}

func BotSwitch(generation string) error {
	robot, err := LoadRobotDef()
	if err != nil {
		return err
	}
	robot.Generation = generation
	err = robot.Overwrite()
	if err != nil {
		return err
	}
	return nil
}

func (d *RobotDef) Overwrite() error {
	file, err := json.MarshalIndent(d, "", "  ")

	if err != nil {
		return fmt.Errorf("failed to marshal robot def file: %s", err)
	}

	reader := bytes.NewReader(file)
	err = atomic.WriteFile("/bot/robot.json", reader)

	if err != nil {
		return fmt.Errorf("failed to write robot def file: %s", err)
	}

	return nil
}

func (d *RobotDef) IdentityEqual(other *RobotDef) bool {
	return d.Name == other.Name &&
		d.Generation == other.Generation &&
		d.AuthDomain == other.AuthDomain &&
		d.AuthClientId == other.AuthClientId &&
		d.AuthClientSecret == other.AuthClientSecret &&
		d.Environment == other.Environment &&
		d.CarbonRobotUsername == other.CarbonRobotUsername &&
		d.CarbonRobotPassword == other.CarbonRobotPassword
}

func LoadRobotDef() (*RobotDef, error) {
	robotDef := &RobotDef{}
	err := util.LoadJsonFile("/bot/robot.json", robotDef)

	if err != nil {
		return nil, err
	}

	return robotDef, nil
}

func (d *RobotDef) OverwriteRobotIdentityIfNeeded() (bool, error) {
	current, err := LoadRobotDef()

	if err != nil {
		return false, nil
	}

	if !current.IdentityEqual(d) {
		current.Name = d.Name
		current.Generation = d.Generation
		current.AuthClientId = d.AuthClientId
		current.AuthClientSecret = d.AuthClientSecret
		current.AuthDomain = d.AuthDomain
		current.Environment = d.Environment
		current.CarbonRobotUsername = d.CarbonRobotUsername
		current.CarbonRobotPassword = d.CarbonRobotPassword

		err = current.Overwrite()

		if err != nil {
			return false, nil
		}

		return true, nil
	}

	return false, nil
}

func OverwriteRowIfNeeded(newRow string) (bool, error) {
	current, err := LoadRobotDef()

	if err != nil {
		return false, err
	}

	if current.Computer.Row != newRow {
		current.Computer.Row = newRow

		err = current.Overwrite()

		if err != nil {
			return false, err
		}

		return true, nil
	}
	return false, nil
}

type ModuleDef struct {
	Id     int    `json:"id"`
	Serial string `json:"serial"`
}

func LoadModuleDef() (*ModuleDef, error) {
	moduleDef := &ModuleDef{}

	if _, err := os.Stat("/bot/module_identity.json"); os.IsNotExist(err) {
		// if the file doesn't exist, assume not configured
		moduleDef.Id = 0
		moduleDef.Serial = ""
		return moduleDef, nil
	}

	err := util.LoadJsonFile("/bot/module_identity.json", moduleDef)
	if err != nil {
		return nil, err
	}

	return moduleDef, nil
}

type CheckoutDef struct {
	Mode string `json:"mode"`
	User string `json:"user"`
}

func (d *CheckoutDef) save() error {
	file, err := json.MarshalIndent(d, "", "  ")

	if err != nil {
		return fmt.Errorf("failed to marshal checkout file: %s", err)
	}

	reader := bytes.NewReader(file)
	err = atomic.WriteFile("/bot/checkout.json", reader)

	if err != nil {
		return fmt.Errorf("failed to write checkout file: %s", err)
	}

	return nil
}

func (d *CheckoutDef) Checkout(user string) error {
	d.Mode = "user"
	d.User = user
	return d.save()
}

func (d *CheckoutDef) Return() error {
	d.Mode = "prod"
	d.User = "carbon"
	return d.save()
}

func LoadCheckoutDef() (*CheckoutDef, error) {
	checkoutDef := &CheckoutDef{}
	err := util.LoadJsonFile("/bot/checkout.json", checkoutDef)

	if err != nil {
		return nil, err
	}

	return checkoutDef, nil
}

type EnvDef struct {
	Bot         string `json:"bot"`
	Robot       string `json:"robot"`
	Data        string `json:"data"`
	Config      string `json:"config"`
	Calibration string `json:"calibration"`
	Logs        string `json:"logs"`
	Models      string `json:"models"`
}

func LoadEnvDef(checkout *CheckoutDef) (*EnvDef, error) {
	envDef := &EnvDef{}
	err := util.LoadJsonFile("/bot/env.json", envDef)

	if err != nil {
		return nil, err
	}

	if checkout.Mode == "user" {
		filePath := fmt.Sprintf("/bot/envs/%s.json", checkout.User)
		err = util.LoadJsonFile(filePath, envDef)

		if err != nil {
			logrus.Error("Missing Environment file for user: ", filePath)
			return nil, err
		}
	}

	return envDef, nil
}

type SystemVersionDef struct {
	Version string `json:"version"`
}

func GetSystemVersion() (string, error) {
	env, err := environment.GetRobot()
	if err != nil {
		return "", err
	}

	if env.IsSimulator() {
		return system.UnmanagedSystemVersion, nil
	}

	if _, err := os.Stat(system.BOTSTRAP_EXEC); err != nil {
		return system.UnmanagedSystemVersion, nil
	}

	systemInfo, err := system.GetSystemInfo(&env)

	if err != nil {
		return "", err
	}

	return systemInfo.Versions.Current, nil
}

type VersionDef struct {
	Tag string `json:"tag"`
}

func (d *VersionDef) Save(systemVersion string) error {
	file, err := json.MarshalIndent(d, "", "  ")

	if err != nil {
		return fmt.Errorf("failed to marshal version file: %s", err)
	}

	reader := bytes.NewReader(file)
	filename := fmt.Sprintf("/bot/versions/%s.json", systemVersion)
	err = atomic.WriteFile(filename, reader)

	if err != nil {
		return fmt.Errorf("failed to write version file: %s", err)
	}

	// set permission=readable to be used by bot when starting on host
	err = os.Chmod(filename, 0644)

	if err != nil {
		return fmt.Errorf("failed to set read permissions for versions file: %v, %s", filename, err)
	}

	return nil
}

func LoadVersionDef(prod bool, systemVersion string) (*VersionDef, error) {
	versionDef := &VersionDef{}

	if systemVersion == "" {
		if os.Getenv("HOST_PLATFORM") == "Darwin" {
			systemVersion = system.UnmanagedSystemVersion
		} else {
			var err error
			systemVersion, err = GetSystemVersion()
			if err != nil {
				logrus.WithError(err).Error("Failed to Get System Version")
				systemVersion = system.UnmanagedSystemVersion
			}
		}
	}

	if prod {
		err := util.LoadJsonFile(fmt.Sprintf("/bot/versions/%s.json", systemVersion), versionDef)
		if err != nil {
			// TODO if this file doesn't exists, we may want to smartly figure out which one we should run
			return nil, err
		}
	} else {
		versionDef.Tag = "latest"
	}

	return versionDef, nil
}

type BotDef struct {
	RobotDef    *RobotDef
	CheckoutDef *CheckoutDef
	EnvDef      *EnvDef
	VersionDef  *VersionDef
	ModuleDef   *ModuleDef
}

func GetHostPlatform() string {
	hostPlatform := os.Getenv("HOST_PLATFORM")
	if hostPlatform == "" {
		hostPlatform = "Linux"
	}
	return hostPlatform
}

func (d *BotDef) GetEnvironmentMapping() map[string]string {
	// TODO Refactor code to switch names to Carbon

	commandComputerHost := "*********"
	switch d.RobotDef.Computer.Role {
	case "simulator":
		commandComputerHost = "127.0.0.1"
	}

	env := map[string]string{
		"MAKA_ROBOT_NAME":          d.RobotDef.Name,
		"MAKA_GEN":                 d.RobotDef.Generation,
		"MAKA_ROW":                 d.RobotDef.Computer.Row,
		"MAKA_ROLE":                d.RobotDef.Computer.Role,
		"MAKA_CALIBRATION_DIR":     "/calibration",
		"MAKA_CONFIG_DIR":          "/config",
		"MAKA_ROBOT_DIR":           "/robot",
		"MAKA_DATA_DIR":            "/data",
		"MAKA_LOG_DIR":             "/logs",
		"MAKA_MODEL_DIR":           "/models",
		"MAKA_BOT_DIR":             "/bot",
		"MAKA_MEDIA_DIR":           "/media",
		"MAKA_BOT_CONFIG_DIR":      "/config/bot",
		"MAKA_AUTH_CLIENT_SECRET":  d.RobotDef.AuthClientSecret,
		"MAKA_AUTH_CLIENT_ID":      d.RobotDef.AuthClientId,
		"MAKA_AUTH_DOMAIN":         d.RobotDef.AuthDomain,
		"MAKA_AUTH_SCOPES":         "",
		"CARBON_MODE":              d.CheckoutDef.Mode,
		"CARBON_USER":              d.CheckoutDef.User,
		"CARBON_VERSION_TAG":       d.VersionDef.Tag,
		"GOCACHE":                  "/calibration/.cache/go-build",
		"GOMODCACHE":               "/calibration/.cache/go-mod-cache",
		"CCACHE_DIR":               "/calibration/.cache/.ccache",
		"HOST_PLATFORM":            GetHostPlatform(),
		"SENTRY_RELEASE":           d.VersionDef.Tag,
		"SENTRY_DSN":               sentry_reporter.GetSentryDSN(d.RobotDef.Computer.Role),
		"CARBON_ROBOT_USERNAME":    d.RobotDef.CarbonRobotUsername,
		"CARBON_ROBOT_PASSWORD":    d.RobotDef.CarbonRobotPassword,
		"METRICS_JOB_FILTER_REGEX": d.RobotDef.MetricsJobFilterRegex,
		"CARBON_CC_HOST":           commandComputerHost,
	}

	env = setModuleValues(d.ModuleDef, env)
	return setComputedValues(d.RobotDef, env)
}

func setComputedValues(robot *RobotDef, mapping map[string]string) map[string]string {
	authDomain := robot.AuthDomain
	targetEnv := robot.Environment
	switch {
	case authDomain == AuthDomainProd || (authDomain == "" && targetEnv == "" || strings.ToLower(targetEnv) == Production):
		mapping["CARBON_IDM_HOST"] = IDMServiceProdHost
		mapping["CARBON_INGEST_HOST"] = IngestServiceProdHost
		mapping["CARBON_PORTAL_HOST"] = PortalServiceProdHost
		mapping["CARBON_CONFIG_CLOUD_HOST"] = ConfigServiceCloudProdHost
		mapping["CARBON_VESELKA_HOST"] = VeselkaServiceProdHost
		mapping["CARBON_VERSION_METADATA_HOST"] = VersionMetadataServiceProdHost
		mapping["CARBON_RTC_SIGNAL_HOST"] = RtcSignalProdHost
		mapping["CARBON_RTC_JOBS_GRPC_HOST"] = RtcJobsGrpcProdHost
		mapping["CARBON_RTC_LOCATOR_GRPC_HOST"] = RtcLocatorGrpcProdHost

	case strings.ToLower(targetEnv) == Testing:
		mapping["CARBON_IDM_HOST"] = IDMServiceTestHost
		mapping["CARBON_INGEST_HOST"] = IngestServiceTestHost
		mapping["CARBON_PORTAL_HOST"] = PortalServiceTestHost
		mapping["CARBON_CONFIG_CLOUD_HOST"] = ConfigServiceCloudTestHost
		mapping["CARBON_VESELKA_HOST"] = VeselkaServiceTestHost
		mapping["CARBON_VERSION_METADATA_HOST"] = VersionMetadataServiceTestHost
		mapping["CARBON_RTC_SIGNAL_HOST"] = RtcSignalTestHost
		mapping["CARBON_RTC_JOBS_GRPC_HOST"] = RtcJobsGrpcTestHost
		mapping["CARBON_RTC_LOCATOR_GRPC_HOST"] = RtcLocatorGrpcTestHost

	default: // default to staging
		mapping["CARBON_IDM_HOST"] = IDMServiceStageHost
		mapping["CARBON_INGEST_HOST"] = IngestServiceStageHost
		mapping["CARBON_PORTAL_HOST"] = PortalServiceStageHost
		mapping["CARBON_CONFIG_CLOUD_HOST"] = ConfigServiceCloudStageHost
		mapping["CARBON_VESELKA_HOST"] = VeselkaServiceStageHost
		mapping["CARBON_VERSION_METADATA_HOST"] = VersionMetadataServiceStageHost
		mapping["CARBON_RTC_SIGNAL_HOST"] = RtcSignalStageHost
		mapping["CARBON_RTC_JOBS_GRPC_HOST"] = RtcJobsGrpcStageHost
		mapping["CARBON_RTC_LOCATOR_GRPC_HOST"] = RtcLocatorGrpcStageHost
	}
	for key, val := range robot.CarbonHostOverrides {
		// only allow overriding host values
		if _, ok := mapping[key]; ok && strings.HasSuffix(key, "HOST") {
			mapping[key] = val
		}

		// special exception for Robot Syncer port
		if key == "CARBON_CONFIG_CLOUD_PORT" {
			mapping[key] = val
		}
	}
	for key, val := range robot.PrometheusRemoteWriteConfig.EnvConfigMapping() {
		mapping[key] = val
	}
	return mapping
}

func setModuleValues(module *ModuleDef, mapping map[string]string) map[string]string {
	if module != nil {
		mapping["CARBON_MODULE_ID"] = strconv.Itoa(module.Id)
		mapping["CARBON_MODULE_SERIAL"] = module.Serial
	}
	return mapping
}

func LoadBotDef() (*BotDef, error) {
	robotDef, err := LoadRobotDef()

	if err != nil {
		return nil, err
	}

	var moduleDef *ModuleDef
	if environment.CarbonGen(robotDef.Generation) == environment.CarbonGenReaper {
		moduleDef, err = LoadModuleDef()
		if err != nil {
			return nil, err
		}
	}

	checkoutDef, err := LoadCheckoutDef()

	if err != nil {
		return nil, err
	}

	envDef, err := LoadEnvDef(checkoutDef)

	if err != nil {
		return nil, err
	}

	versionDef, err := LoadVersionDef(checkoutDef.Mode == environment.CarbonModeProd, "") // TODO Load System Version Here

	if err != nil {
		return nil, err
	}

	return &BotDef{
		RobotDef:    robotDef,
		CheckoutDef: checkoutDef,
		EnvDef:      envDef,
		VersionDef:  versionDef,
		ModuleDef:   moduleDef,
	}, nil
}

func LoadBotDefForUser(user string) (*BotDef, error) {
	robotDef, err := LoadRobotDef()

	if err != nil {
		return nil, err
	}

	checkoutDef, err := LoadCheckoutDef()

	if err != nil {
		return nil, err
	}
	if user != "" {
		checkoutDef.Mode = environment.CarbonModeUser
		checkoutDef.User = user
	}

	var moduleDef *ModuleDef
	if environment.CarbonGen(robotDef.Generation) == environment.CarbonGenReaper {
		moduleDef, err = LoadModuleDef()
		if err != nil {
			return nil, err
		}
	}

	envDef, err := LoadEnvDef(checkoutDef)

	if err != nil {
		return nil, err
	}

	versionDef, err := LoadVersionDef(checkoutDef.Mode == environment.CarbonModeProd, "") // TODO Load System Version Here

	if err != nil {
		return nil, err
	}

	return &BotDef{
		RobotDef:    robotDef,
		CheckoutDef: checkoutDef,
		EnvDef:      envDef,
		VersionDef:  versionDef,
		ModuleDef:   moduleDef,
	}, nil
}

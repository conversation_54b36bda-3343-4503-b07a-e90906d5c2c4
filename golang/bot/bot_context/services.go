package bot_context

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"github.com/carbonrobotics/robot/golang/bot/util"
	"github.com/compose-spec/compose-go/v2/loader"
	"github.com/compose-spec/compose-go/v2/types"
	"github.com/docker/compose/v2/pkg/api"
	"github.com/flosch/pongo2/v5"
)

type ComputerRole struct {
	Services []string
}

type ComputerRoles struct {
	Roles map[string][]string
}

func getPongoContext(botDef *BotDef) pongo2.Context {
	ctx := pongo2.Context{}

	for k, v := range botDef.GetEnvironmentMapping() {
		ctx[k] = v
	}

	return ctx
}

func LoadRoles() (*ComputerRoles, error) {
	roleMap := make(map[string][]string)
	err := util.LoadYamlFile("/robot/services/roles.yaml", roleMap)

	if err != nil {
		return nil, err
	}

	return &ComputerRoles{Roles: roleMap}, nil
}

func deduplicateStrings(array []string) []string {
	results := []string{}
	lookup := map[string]bool{}

	for _, item := range array {
		if _, ok := lookup[item]; !ok {
			lookup[item] = true
			results = append(results, item)
		}
	}

	return results
}

func LoadRole(role string, extraRoles []string) (*ComputerRole, error) {
	roles, err := LoadRoles()

	if err != nil {
		return nil, err
	}

	computerRole := &ComputerRole{roles.Roles[role]}

	if extraRoles != nil {
		for _, eRole := range extraRoles {
			computerRole.Services = append(computerRole.Services, roles.Roles[eRole]...)
		}
	}

	computerRole.Services = deduplicateStrings(computerRole.Services)

	return computerRole, nil
}

func GetAllCombinedRoles() (*ComputerRole, error) {
	roles, err := LoadRoles()

	if err != nil {
		return nil, err
	}

	role := &ComputerRole{}
	unique := make(map[string]bool)

	for _, services := range roles.Roles {
		for _, service := range services {
			unique[service] = true
		}
	}

	for service := range unique {
		role.Services = append(role.Services, service)
	}

	return role, nil
}

func LoadServiceConfigFile(botDef *BotDef, path string) (*types.ConfigFile, error) {
	ctx := getPongoContext(botDef)

	out, err := util.LoadPongoFile(path, ctx)

	if err != nil {
		return nil, err
	}

	return &types.ConfigFile{
		Content: []byte(out),
	}, nil
}

func LoadAllServiceConfigFiles(botDef *BotDef) ([]types.ConfigFile, error) {
	prefix := "/robot/services/compositions"
	files, err := os.ReadDir(prefix)
	if err != nil {
		return nil, err
	}

	configs := []types.ConfigFile{}

	ext := ".yaml.j2"
	for _, file := range files {
		name := file.Name()
		if strings.HasSuffix(name, ext) {
			config, err := LoadServiceConfigFile(botDef, fmt.Sprintf("%s/%s", prefix, file.Name()))

			if err != nil {
				return nil, err
			}

			configs = append(configs, *config)
		}
	}

	return configs, nil
}

func LoadComposeProject(botDef *BotDef, role *ComputerRole) (*types.Project, error) {
	environment := botDef.GetEnvironmentMapping()

	for k, v := range map[string]string{
		"MAKA_CALIBRATION_DIR": botDef.EnvDef.Calibration,
		"MAKA_CONFIG_DIR":      botDef.EnvDef.Config,
		"MAKA_ROBOT_DIR":       botDef.EnvDef.Robot,
		"MAKA_DATA_DIR":        botDef.EnvDef.Data,
		"MAKA_LOG_DIR":         botDef.EnvDef.Logs,
		"MAKA_MODEL_DIR":       botDef.EnvDef.Models,
		"MAKA_BOT_DIR":         botDef.EnvDef.Bot,
		"MAKA_BOT_CONFIG_DIR":  fmt.Sprintf("%s/bot", botDef.EnvDef.Config),
		"CARBON_USER_DIR":      fmt.Sprintf("/home/<USER>", botDef.CheckoutDef.User),
	} {
		environment[k] = v
	}

	configs, err := LoadAllServiceConfigFiles(botDef)

	if err != nil {
		return nil, err
	}

	project, err := loader.Load(types.ConfigDetails{
		ConfigFiles: configs,
		Environment: environment,
	}, func(options *loader.Options) {
		options.SetProjectName("robot", true)
	})

	if err != nil {
		return nil, err
	}

	enabled, err := project.GetServices(role.Services...)
	if err != nil {
		return nil, err
	}

	project.DisabledServices = project.Services
	project.Services = enabled

	// Got this from: https://stackoverflow.com/questions/74830594/created-compose-project-not-listed-when-using-docker-compose-package-in-go
	// Still unsure why I have to manually do this, my closest understanding is that those thing are populated at build time in this new version and of course we don't do builds directly through compose...
	for i, s := range project.Services {
		s.CustomLabels = map[string]string{
			api.ProjectLabel:     project.Name,
			api.ServiceLabel:     s.Name,
			api.VersionLabel:     api.ComposeVersion,
			api.WorkingDirLabel:  project.WorkingDir,
			api.ConfigFilesLabel: strings.Join(project.ComposeFiles, ","),
			api.OneoffLabel:      "False", // default, will be overridden by `run` command
		}
		project.Services[i] = s
	}

	injectedEnv := botDef.GetEnvironmentMapping()
	envStrings := []string{}
	for name, val := range injectedEnv {
		envStrings = append(envStrings, fmt.Sprintf("%s=%s", name, val))
	}
	mapping := types.NewMappingWithEquals(envStrings)

	for _, service := range project.Services {
		service.Environment.OverrideBy(mapping)
	}

	return project, nil
}

func GetContainerNamesSetFromProject(project *types.Project, gobotIncluded bool) map[string]bool {
	containers := map[string]bool{}
	if gobotIncluded {
		containers["gobot"] = true // Gobot is not a Service in Project, but required in all cases
	}
	for _, service := range project.Services {
		// e.g. ghcr.io/carbonrobotics/robot/common:v0.0.18
		name_tag := strings.Split(service.Image, ":")
		split := strings.Split(name_tag[0], "/")
		containers[split[len(split)-1]] = true
	}
	return containers
}

func processPongoConfigFile(src string, dst string, botDef *BotDef) error {
	ctx := getPongoContext(botDef)

	out, err := util.LoadPongoFile(src, ctx)

	if err != nil {
		return err
	}

	err = os.WriteFile(dst, []byte(out), 0666)
	if err != nil {
		return err
	}

	return nil
}

func copyFile(src string, dst string) error {
	input, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	err = os.WriteFile(dst, input, 0666)
	if err != nil {
		return err
	}

	return nil
}

func SetupConfigs(botDef *BotDef) error {
	prefix := "/robot/services/config"
	newPrefix := "/config/bot"

	// We ignore the error for this
	os.RemoveAll(newPrefix)

	return filepath.WalkDir(prefix,
		func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}

			relativePath := strings.TrimPrefix(path, prefix)
			newPath := filepath.Join(newPrefix, relativePath)

			if d.IsDir() {
				err = os.MkdirAll(newPath, os.ModePerm)
				if err != nil {
					return err
				}
			} else if strings.Contains(path, ".yaml.j2") || strings.Contains(path, ".json.j2") {
				newPath = strings.TrimRight(newPath, ".j2")
				err = processPongoConfigFile(path, newPath, botDef)
				if err != nil {
					return err
				}
			} else {
				err = copyFile(path, newPath)
				if err != nil {
					return err
				}
			}
			return nil
		})
}

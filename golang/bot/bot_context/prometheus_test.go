package bot_context

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPrometheusRemoteWriteConfig_EnvConfigMapping(t *testing.T) {
	tests := []struct {
		name            string
		promRWConfig    PrometheusRemoteWriteConfig
		expectedMapping map[string]string
	}{
		{"default values",
			PrometheusRemoteWriteConfig{},
			map[string]string{
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
			},
		},
		{
			"override values",
			PrometheusRemoteWriteConfig{
				Capacity:          999,
				MinShards:         444,
				MaxShards:         333,
				MaxSamplesPerSend: 222,
				BatchSendDeadline: "100m",
				MinBackoff:        "10h",
				MaxBackoff:        "7s",
			},
			map[string]string{
				"PROMETHEUS_RW_CAPACITY":             "999",
				"PROMETHEUS_RW_MIN_SHARDS":           "444",
				"PROMETHEUS_RW_MAX_SHARDS":           "333",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "222",
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "100m",
				"PROMETHEUS_RW_MIN_BACKOFF":          "10h",
				"PROMETHEUS_RW_MAX_BACKOFF":          "7s",
			},
		},
		{
			"default fallbacks",
			PrometheusRemoteWriteConfig{
				Capacity:          0,
				MinShards:         0,
				MaxShards:         0,
				MaxSamplesPerSend: 0,
				BatchSendDeadline: "invalid",
				MinBackoff:        "invalid",
				MaxBackoff:        "invalid",
			},
			map[string]string{
				"PROMETHEUS_RW_CAPACITY":             "100000",
				"PROMETHEUS_RW_MIN_SHARDS":           "1",
				"PROMETHEUS_RW_MAX_SHARDS":           "20",
				"PROMETHEUS_RW_MAX_SAMPLES_PER_SEND": "10000",
				"PROMETHEUS_RW_BATCH_SEND_DEADLINE":  "5m",
				"PROMETHEUS_RW_MIN_BACKOFF":          "1s",
				"PROMETHEUS_RW_MAX_BACKOFF":          "5m",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got := test.promRWConfig.EnvConfigMapping()
			assert.Equal(t, test.expectedMapping, got)
		})
	}
}

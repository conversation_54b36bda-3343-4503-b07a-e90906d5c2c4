package docker

import (
	"archive/tar"
	"context"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/bot/parameters"

	crgit "github.com/carbonrobotics/robot/golang/lib/git"

	"github.com/carbonrobotics/robot/golang/bot/bot_context"
	"github.com/carbonrobotics/robot/golang/bot/util"
	"github.com/docker/buildx/build"
	"github.com/docker/buildx/builder"
	"github.com/docker/buildx/driver"
	"github.com/docker/buildx/store"
	"github.com/docker/buildx/store/storeutil"
	"github.com/docker/buildx/util/confutil"
	"github.com/docker/buildx/util/dockerutil"
	"github.com/docker/buildx/util/progress"
	"github.com/docker/cli/cli/command"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
	"github.com/docker/docker/pkg/archive"
	"github.com/google/uuid"
	bk_client "github.com/moby/buildkit/client"
	"github.com/moby/buildkit/session"
	"github.com/moby/buildkit/util/progress/progressui"
	specs "github.com/opencontainers/image-spec/specs-go/v1"
	"github.com/sirupsen/logrus"
	"golang.org/x/term"
)

const (
	standardTimeout = 15 * time.Second
	waitingTimeout  = 5 * time.Minute

	// Safe launch script that checks for feature flag, file existence, and canary before running the verify_image script
	// Logs can be seen by attaching to the container, unique exit codes are used to tell what errored when the container exits
	verifyImageScript = `if [ "${SUPPORTS_CHECKSUM_VALIDATION:-0}" -eq 1 ];
													then if [ -s /robot/verify_image ];
														then if tail -1 /robot/verify_image | grep -q 'DEADBEEF';
															then /robot/verify_image compare /robot;
															else echo 'Error: verify_image script has a dead canary' && exit 2;
														fi;
														else echo 'Error: verify_image script has size 0' && exit 3;
													fi;
												else echo 'verify_image script not supported, skipping' && exit 0;
												fi;`
)

type Containers struct {
	Containers []Container `json:"containers"`
}

type Container struct {
	Name             string              `json:"name"`
	BaseFile         string              `json:"base_file"`
	ProdFile         string              `json:"prod_file"`
	BuildCmd         []string            `json:"build_cmd"`
	ProdBuildCmd     []string            `json:"prod_build_cmd"`
	OverrideBuildCmd map[string][]string `json:"role_build_cmd_override"`
	Deps             []string            `json:"deps"`
	PopulatedFile    string
}

// RelativeBaseFile returns the base docker file as a relative path from inside the /robot dir
func (container *Container) RelativeBaseFile() string {
	if len(container.BaseFile) > 0 {
		return fmt.Sprintf("services/%s", container.BaseFile)
	}
	return ""
}

func (container *Container) TaggedImage(tag string) string {
	return fmt.Sprintf("ghcr.io/carbonrobotics/robot/%s:%s", container.Name, tag)
}

func Push(cli *client.Client, fullName string, verbose bool) (err error) {
	ctx := GetDockerContext(cli)
	authString, err := GetRegistryAuthString()

	if err != nil {
		return err
	}

	logrus.Info("Pushing Image: ", fullName)
	res, err := cli.ImagePush(ctx, fullName, image.PushOptions{
		RegistryAuth: authString,
	})

	if err != nil {
		return fmt.Errorf("failed to push container %s: %s", fullName, err)
	}

	defer func() {
		closeErr := res.Close()
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	dec := json.NewDecoder(res)

	for dec.More() {
		var result map[string]interface{}
		err := dec.Decode(&result)
		if err != nil {
			return fmt.Errorf("failed to decode stream: %s", err)
		}

		var status string = ""
		var id string = ""
		var progress string = ""

		if statusData, ok := result["status"]; ok {
			status = statusData.(string)
		}

		if idData, ok := result["id"]; ok {
			id = idData.(string)
		}

		if progressData, ok := result["progress"]; ok {
			progress = progressData.(string)
		}

		if verbose {
			if id == "" {
				logrus.Info(status)
			} else if progress == "" {
				logrus.Infof("[%s] -> %s", id, status)
			} else {
				logrus.Infof("[%s] -> %s: %s", id, status, progress)
			}
		} // TODO optionally maybe we can pass a channel to send progress to when called not within CLI
	}

	return nil
}

func (container *Container) Push(cli *client.Client, release string, verbose bool, remoteTag string, registry string) error {
	tag := "latest"
	if release != "" {
		tag = release
	}

	if registry == "" {
		registry = "ghcr.io/carbonrobotics/robot"
	}

	ctx := GetDockerContext(cli)

	imageName := registry + "/" + container.Name
	ogImage := imageName + ":" + tag
	finalImage := ogImage

	if remoteTag != "" {
		finalImage = imageName + ":" + remoteTag
		logrus.Infof("Tagging Image: %s -> %s", ogImage, finalImage)
		err := cli.ImageTag(ctx, ogImage, finalImage)
		if err != nil {
			return err
		}
	}

	return Push(cli, finalImage, verbose)
}

func Pull(cli *client.Client, fullName string, verbose bool) (err error) {
	ctx := GetDockerContext(cli)
	authString, err := GetRegistryAuthString()

	if err != nil {
		return err
	}

	res, err := cli.ImagePull(ctx, fullName, image.PullOptions{
		RegistryAuth: authString,
	})

	if err != nil {
		return fmt.Errorf("failed to pull container %s: %s", fullName, err)
	}

	defer func() {
		closeErr := res.Close()
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	dec := json.NewDecoder(res)

	for dec.More() {
		var result map[string]interface{}
		err := dec.Decode(&result)
		if err != nil {
			return fmt.Errorf("failed to decode stream: %s", err)
		}

		var status string = ""
		var id string = ""
		var progress string = ""

		if statusData, ok := result["status"]; ok {
			status = statusData.(string)
		}

		if idData, ok := result["id"]; ok {
			id = idData.(string)
		}

		if progressData, ok := result["progress"]; ok {
			progress = progressData.(string)
		}

		if verbose {
			if id == "" {
				logrus.Info(status)
			} else if progress == "" {
				logrus.Infof("[%s] -> %s", id, status)
			} else {
				logrus.Infof("[%s] -> %s: %s", id, status, progress)
			}
		}
	}

	return nil
}

func (container *Container) Pull(cli *client.Client, release string, verbose bool, registry string) error {
	tag := "latest"
	if release != "" {
		tag = release
	}

	name := "ghcr.io/carbonrobotics/robot/" + container.Name

	if registry != "" {
		name = registry + "/" + container.Name
	}

	return Pull(cli, name+":"+tag, verbose)
}

func (container *Container) Tag(cli *client.Client, source string, tag string, registrySrc string, registryDst string) error {
	if registrySrc == "" {
		registrySrc = "ghcr.io/carbonrobotics/robot"
	}

	if registryDst == "" {
		registryDst = "ghcr.io/carbonrobotics/robot"
	}

	if source == "" {
		source = "latest"
	}

	if tag == "" {
		tag = "latest"
	}

	ctx := GetDockerContext(cli)
	from := registrySrc + "/" + container.Name + ":" + source
	to := registryDst + "/" + container.Name + ":" + tag
	logrus.Infof("Tagging: %s -> %s", from, to)
	return cli.ImageTag(ctx, from, to)
}

func getBuildInputs(release bool, dockerfile string) (build.Inputs, io.ReadCloser, error) {
	if true || !release {
		// When not making release builds, we don't actually need to tar stuff, and it's faster to not do so
		// Update: we never actually need to anymore, though this taring code may be useful
		return build.Inputs{
			ContextPath:    "/robot",
			DockerfilePath: fmt.Sprintf("/robot/services/%s", dockerfile),
		}, nil, nil
	}

	tar, err := archive.TarWithOptions("/robot", &archive.TarOptions{
		ExcludePatterns: []string{
			".*",
			"unity/*",
			"lightning_logs",
			"notebooks",
			"obj/",
			"streamlit",
			"tmp/",
			"**/node_modules/",
			"__pycache__",
			"test",
			"**/*.o",
		},
	})

	if err != nil {
		return build.Inputs{}, nil, fmt.Errorf("failed to tar docker build context: %s", err)
	}

	return build.Inputs{
		ContextPath:    "-",
		DockerfilePath: fmt.Sprintf("services/%s", dockerfile),
		InStream:       tar,
	}, tar, nil
}

type CloseableBuildOptions struct {
	Options map[string]build.Options
	Tars    []io.ReadCloser
}

func (c *CloseableBuildOptions) Close() error {
	errs := make([]error, 0, len(c.Tars))
	for _, t := range c.Tars {
		errs = append(errs, t.Close())
	}
	return errors.Join(errs...)
}

func getMultiArchBuildOptions(release bool, dockerfile string, buildArgs map[string]string, containerName string, tag []string, push bool) (*CloseableBuildOptions, error) {
	// This code works to create multi-arch images, including production images
	// However using buildx required the images specified in FROM to be on a registry
	// Since the buildx builder cannot access the local "docker images"
	// However, since we really only need the multi-arch builds to happen on a release Github CI, we can
	// Work around that using a local registry there
	// In the meanwhile leaving this here so when we are ready to work more on the productionization part, we can pick it back up
	// Another option would be to append the Dockerfile.production to the base, hence creating a single file and no FROM bs

	if !push {
		return nil, fmt.Errorf("Push required for multi-arch build")
	}

	ret := &CloseableBuildOptions{
		Options: make(map[string]build.Options),
		Tars:    []io.ReadCloser{},
	}

	for _, key := range []string{"arm64", "amd64", "multi-arch"} {
		buildxInputs, tar, err := getBuildInputs(release, dockerfile)

		if err != nil {
			closeErr := ret.Close()
			return nil, errors.Join(err, closeErr)
		}

		if tar != nil {
			ret.Tars = append(ret.Tars, tar)
		}

		if key == "multi-arch" {
			fullTags := []string{}
			for _, tagItem := range tag {
				fullTags = append(fullTags, "ghcr.io/carbonrobotics/robot/"+containerName+":"+tagItem)
			}
			ret.Options[key] = build.Options{
				Inputs:    buildxInputs,
				Tags:      fullTags,
				BuildArgs: buildArgs,
				Platforms: []specs.Platform{
					{
						Architecture: "amd64",
						OS:           "linux",
					},
					{
						Architecture: "arm64",
						OS:           "linux",
					},
				},
				Session: []session.Attachable{GetAuthConfigsAsSession()},
				Exports: []bk_client.ExportEntry{{
					Type: "image",
					Attrs: map[string]string{
						"push": "true",
						"load": "true",
					},
				}},
			}
		} else {
			fullTags := []string{}
			for _, tagItem := range tag {
				fullTags = append(fullTags, "ghcr.io/carbonrobotics/robot/"+containerName+":"+tagItem+"-"+key)
			}
			ret.Options[key] = build.Options{
				Inputs:    buildxInputs,
				Tags:      fullTags,
				BuildArgs: buildArgs,
				Platforms: []specs.Platform{
					{
						Architecture: key,
						OS:           "linux",
					},
				},
				Session: []session.Attachable{GetAuthConfigsAsSession()},
				Exports: []bk_client.ExportEntry{{
					Type:  "docker",
					Attrs: map[string]string{},
				}},
			}
		}
	}

	return ret, nil
}

func getMultiArchBuilder(ctx context.Context, dockerCli command.Cli) (*builder.Builder, func(), error) {
	builderName := "multi-arch-builder"

	contexts, err := dockerCli.ContextStore().List()
	if err != nil {
		return nil, nil, err
	}
	for _, c := range contexts {
		if c.Name == builderName {
			logrus.Warnf("instance name %q already exists as context builder", builderName)
			break
		}
	}

	txn, release_func, err := storeutil.GetStore(dockerCli)
	if err != nil {
		return nil, nil, err
	}

	var driverName string
	ng, err := txn.NodeGroupByName(builderName)
	if err != nil {
		if os.IsNotExist(err) {
			f, err := driver.GetDefaultFactory(ctx, "", dockerCli.Client(), true, nil)
			if err != nil {
				release_func()
				return nil, nil, err
			}
			if f == nil {
				release_func()
				return nil, nil, errors.New("no valid drivers found")
			}
			driverName = f.Name()
			ng = &store.NodeGroup{
				Name:   builderName,
				Driver: driverName,
			}
		} else {
			release_func()
			return nil, nil, err
		}
	}

	driverName = ng.Driver
	if _, err := driver.GetFactory(driverName, true); err != nil {
		release_func()
		return nil, nil, err
	}

	ep, err := dockerutil.GetCurrentEndpoint(dockerCli)
	if err != nil {
		release_func()
		return nil, nil, err
	}

	if err := ng.Update("multi-arch", ep, []string{"linux/arm64", "linux/amd64"}, false, true, nil, "", nil); err != nil {
		release_func()
		return nil, nil, err
	}

	if err := txn.Save(ng); err != nil {
		release_func()
		return nil, nil, err
	}

	builderObj, err := builder.New(dockerCli, builder.WithName(ng.Name), builder.WithStore(txn))

	if err != nil {
		release_func()
		return nil, nil, err
	}

	return builderObj, release_func, nil
}

func getSingleArchBuildOptions(release bool, dockerfile string, buildArgs map[string]string, containerName string, tag []string, platform string, push bool, optionalCacheFrom []string, dockerExport bool) (*CloseableBuildOptions, error) {
	ret := &CloseableBuildOptions{
		Options: make(map[string]build.Options),
		Tars:    []io.ReadCloser{},
	}
	buildxInputs, tar, err := getBuildInputs(release, dockerfile)

	if err != nil {
		closeErr := ret.Close()
		return nil, errors.Join(err, closeErr)
	}

	if tar != nil {
		ret.Tars = append(ret.Tars, tar)
	}

	exports := []bk_client.ExportEntry{}

	fullTags := []string{}
	for _, tagItem := range tag {
		if !push || platform == "amd64" {
			fullTags = append(fullTags, "ghcr.io/carbonrobotics/robot/"+containerName+":"+tagItem)
		}
		fullTags = append(fullTags, "ghcr.io/carbonrobotics/robot/"+containerName+":"+tagItem+"-"+platform)
	}

	cacheTo := []bk_client.CacheOptionsEntry{}

	if push {
		exports = append(exports, bk_client.ExportEntry{
			Type: "image",
			Attrs: map[string]string{
				"push": "true",
				"name": strings.Join(fullTags, ","),
			},
		})
	}
	for _, tagItem := range tag {
		cacheTo = append(cacheTo, bk_client.CacheOptionsEntry{
			Type: "registry",
			Attrs: map[string]string{
				"ref":  "ghcr.io/carbonrobotics/robot/" + containerName + ":" + tagItem + "-" + platform + "-" + "cache",
				"mode": "max",
			},
		})
	}
	if dockerExport {
		// Due to docker driver performance sucking, when we push we leverage pull instead since it is faster
		// But as commented below, there are conflicts with concurrent pushes... so we're not putting this in a only non-push situation
		exports = append(exports, bk_client.ExportEntry{
			Type: "docker",
			Attrs: map[string]string{
				"name": strings.Join(fullTags, ","),
			},
		})
	}

	cacheFrom := []bk_client.CacheOptionsEntry{
		{
			Type: "registry",
			Attrs: map[string]string{
				"ref": "ghcr.io/carbonrobotics/robot/" + containerName + ":" + "latest" + "-" + platform + "-" + "cache",
			},
		},
	}

	for _, tagItem := range tag {
		cacheFrom = append(cacheFrom, bk_client.CacheOptionsEntry{
			Type: "registry",
			Attrs: map[string]string{
				"ref": "ghcr.io/carbonrobotics/robot/" + containerName + ":" + tagItem + "-" + platform + "-" + "cache",
			},
		})
	}

	for _, cacheFromTag := range optionalCacheFrom {
		cacheFrom = append(cacheFrom, bk_client.CacheOptionsEntry{
			Type: "registry",
			Attrs: map[string]string{
				"ref": "ghcr.io/carbonrobotics/robot/" + containerName + ":" + cacheFromTag + "-" + platform + "-" + "cache",
			},
		})
	}

	ret.Options["single"] = build.Options{
		Inputs:    buildxInputs,
		Tags:      fullTags,
		CacheFrom: cacheFrom,
		CacheTo:   cacheTo,
		BuildArgs: buildArgs,
		Session:   []session.Attachable{GetAuthConfigsAsSession()},
		Platforms: []specs.Platform{
			{
				Architecture: platform,
				OS:           "linux",
			},
		},
		Exports:                exports,
		ProvenanceResponseMode: confutil.MetadataProvenanceModeDisabled,
	}

	return ret, nil
}

func (container *Container) build(cli *client.Client, release string, verbose bool, optionalTags []string, bbOpts parameters.BotBuildOpts) (err error) {
	dockerfile := container.BaseFile
	if release != "" {
		if container.ProdFile != "" {
			dockerfile = container.ProdFile
		}

		if container.PopulatedFile != "" {
			dockerfile = container.PopulatedFile
		}
	}

	ctx := GetDockerContext(cli)

	logrus.Info("Building Container: ", container.Name, ":", release)

	if bbOpts.BuildArgs == nil {
		bbOpts.BuildArgs = make(map[string]string)
	}
	bbOpts.BuildArgs["C_VERSION"] = bbOpts.Version
	bbOpts.BuildArgs["C_ID"] = bbOpts.ID

	dockerCli, err := GetDockerCommandCLI()

	if err != nil {
		return fmt.Errorf("Failed to get Docker command CLI: %s", err)
	}

	var builderObj *builder.Builder
	// We could use the containerized builder in all cases
	// But it is slower when building for the current arch
	// So we keep the other option so we go fast
	// (scochrane) As of July 2024 for Build Caching we use the containerized builder all the time
	multiArchBuilder := true || bbOpts.Multiarch || bbOpts.Platform != runtime.GOARCH
	if multiArchBuilder {
		var releaseFunc func()
		builderObj, releaseFunc, err = getMultiArchBuilder(ctx, dockerCli)

		if err != nil {
			return fmt.Errorf("Failed to create new multi arch builder: %s", err)
		}

		defer releaseFunc()
	} else {
		builderObj, err = builder.New(dockerCli)

		if err != nil {
			return fmt.Errorf("Failed to create new single arch builder: %s", err)
		}
	}

	duClient := dockerutil.NewClient(dockerCli)

	nodes, err := builderObj.LoadNodes(ctx)

	if err != nil {
		return fmt.Errorf("Failed to load nodes: %s", err)
	}

	for _, node := range nodes {
		logrus.Infof("Builder Node: %v", node.Platforms)
	}

	var opts *CloseableBuildOptions
	if bbOpts.Multiarch {
		opts, err = getMultiArchBuildOptions(release != "", dockerfile, bbOpts.BuildArgs, container.Name, optionalTags, bbOpts.Push)
	} else {
		dockerExport := multiArchBuilder && !bbOpts.Push
		opts, err = getSingleArchBuildOptions(release != "", dockerfile, bbOpts.BuildArgs, container.Name, optionalTags, bbOpts.Platform, bbOpts.Push, bbOpts.OptionalCacheFrom, dockerExport)
	}

	if err != nil {
		return err
	}

	defer func() {
		closeErr := opts.Close()
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	var printer *progress.Printer
	printer, err = progress.NewPrinter(ctx, os.Stderr, progressui.PlainMode)

	// Stupid printer code above does logger shenanigans....
	// The below line can be useful for debugging
	logrus.StandardLogger().SetOutput(os.Stdout)

	if err != nil {
		return fmt.Errorf("Failed to create printer: %s", err)
	}

	for containerBuildRetries := bbOpts.ContainerBuildRetries; containerBuildRetries >= 0; containerBuildRetries-- {
		resp, err := build.Build(ctx, nodes, opts.Options, duClient, confutil.ConfigDir(dockerCli), printer)
		if err != nil {
			if containerBuildRetries > 0 {
				switch {
				case strings.Contains(err.Error(), "500 Internal Server Error") || strings.Contains(err.Error(), "failed to copy to tar"):
					logrus.Warnln("Failed to build:", err)
					logrus.Infoln("build failure is retry-able, retrying...", containerBuildRetries, "retries remaining")
					continue
				default:
				}
			}
			return fmt.Errorf("Failed to build: %s", err)
		}
		for k, v := range resp {
			logrus.Infof("Key: %v, Value: %v", k, v)
		}
		break // break for success
	}

	return nil
}

func (container *Container) populateProdDockerfile() (string, error) {
	data, err := os.ReadFile("/robot/services/" + container.BaseFile)

	if err != nil {
		return "", err
	}

	if container.ProdFile != "" {
		prodData, err := os.ReadFile("/robot/services/" + container.ProdFile)

		if err != nil {
			return "", err
		}

		data = append(data, byte('\n'))
		data = append(data, prodData...)
	}

	servicepath := "/robot/services/"
	dirpath := "tmp/dockerfiles/"
	filename := container.Name + ".Dockerfile"
	os.MkdirAll(filepath.Join(servicepath, dirpath), 0755)
	fullFilepath := filepath.Join(servicepath, dirpath, filename)
	err = os.WriteFile(fullFilepath, data, 0777)

	if err != nil {
		return "", err
	}

	container.PopulatedFile = filepath.Join(dirpath, filename)
	return fullFilepath, nil
}

func (container *Container) Build(cli *client.Client, botDef *bot_context.BotDef, release string, fast bool, verbose bool, role string, opts parameters.BotBuildOpts) error {
	tag := botDef.VersionDef.Tag
	if opts.SourceTag != "" {
		logrus.Infoln("source tag specified:", opts.SourceTag)
		tag = opts.SourceTag
	}
	optionalTags := append([]string{}, opts.OptionalTag...)
	if len(optionalTags) > 0 && opts.SourceTag == "" {
		tag = optionalTags[0]
	} else {
		optionalTags = append(optionalTags, tag)
	}

	if release != "" {
		optionalTags = append(optionalTags, release)
	}

	if !fast {
		err := container.build(cli, "", verbose, optionalTags, opts)

		if err != nil {
			return err
		}

		if opts.Push {
			for _, t := range optionalTags {
				if opts.Platform != "amd64" {
					t = t + "-" + opts.Platform
				}
				container.Pull(cli, t, verbose, "")
			}
		}
	} else if release == "" {
		for _, optTag := range optionalTags {
			if tag != optTag {
				src := container.TaggedImage(tag)
				tgt := container.TaggedImage(optTag)
				if opts.Platform != "amd64" {
					tgt = container.TaggedImage(optTag + "-" + opts.Platform)
				}
				logrus.Infoln("non-release fast build, tagging", src, "->", tgt)
				if err := cli.ImageTag(context.TODO(), src, tgt); err != nil {
					return fmt.Errorf("failed to tag image %q -> %q - %w", tag, optTag, err)
				}
				if opts.Push {
					if err := Push(cli, tgt, verbose); err != nil {
						return fmt.Errorf("failed to push %s - %w", tgt, err)
					}
				}
			}
		}
	}

	buildCmd := container.BuildCmd

	if release != "" {
		buildCmd = container.ProdBuildCmd
	}

	if roleCmd, ok := container.OverrideBuildCmd[role]; ok {
		buildCmd = roleCmd
	}

	if !opts.NoBuildCmd && len(buildCmd) > 0 {
		runTag := tag
		if opts.Platform != "amd64" {
			runTag = runTag + "-" + opts.Platform
		}
		err := RunCommandInContainer(cli, container, botDef, buildCmd, runTag, false, false, "")

		if err != nil {
			return err
		}
	}

	if release != "" {
		_, err := container.populateProdDockerfile()

		if err != nil {
			return err
		}

		err = container.build(cli, release, verbose, optionalTags, opts)

		if err != nil {
			return err
		}
	}

	return nil
}

func BuildContainers(containers []Container, cli *client.Client, botDef *bot_context.BotDef, verbose bool, role string, opts parameters.BotBuildOpts) error {
	cntrs := []string{}
	for _, c := range containers {
		cntrs = append(cntrs, c.Name)
	}
	logrus.Info("Building Containers: ", cntrs)

	if opts.Release != "" {
		logrus.Info("With Release Version: ", opts.Release)
	}

	var err error
	changedFiles := make([]string, 0)
	if opts.CiRevision != "" {
		changedFiles, err = crgit.FilesChangedFromRevision(bot_context.RobotRepoPath, opts.CiRevision)
		if err != nil {
			return err
		}
	}
	for _, container := range containers {
		fastParam := opts.Fast
		containerRelease := opts.Release
		if opts.Container != "" && container.Name != opts.Container {
			containerRelease = ""
		}
		if containerRelease == "" && opts.CiRevision != "" && !slices.Contains(changedFiles, container.RelativeBaseFile()) {
			logrus.Infoln("dockerfile for container", container.Name, "unchanged, skipping build")
			fastParam = true
		}
		err := container.Build(cli, botDef, containerRelease, fastParam, verbose, role, opts)
		if err != nil {
			return err
		}
	}

	return nil
}

type TagContainersOpts struct {
	Containers  []Container
	Cli         *client.Client
	BotDef      *bot_context.BotDef
	Source      string
	Tag         string
	RegistrySrc string
	RegistryDst string
}

func TagContainers(opts TagContainersOpts) error {
	for _, container := range opts.Containers {
		err := container.Tag(opts.Cli, opts.Source, opts.Tag, opts.RegistrySrc, opts.RegistryDst)
		if err != nil {
			return err
		}
	}

	return nil
}

func FilterContainers(containers []Container, filter_containers map[string]bool) []Container {
	out := []Container{}

	for _, container := range containers {
		if filter_containers[container.Name] {
			out = append(out, container)
		}
	}

	return out
}

func OrderContainersByDeps(containers []Container) ([]Container, error) {
	completed := make(map[string]bool)
	not_ready := containers
	var final []Container
	last_length := len(not_ready)
	for len(not_ready) > 0 {
		var next_not_ready []Container
		for _, container := range not_ready {
			deps_fulfilled := true
			for _, dep := range container.Deps {
				if _, ok := completed[dep]; !ok {
					deps_fulfilled = false
				}
			}

			if deps_fulfilled {
				completed[container.Name] = true
				final = append(final, container)
			} else {
				next_not_ready = append(next_not_ready, container)
			}
		}
		not_ready = next_not_ready

		if len(not_ready) == last_length {
			return nil, errors.New("circular dependency detected in containers")
		}
	}

	return final, nil
}

func LoadContainerDefs() ([]Container, error) {
	var containers Containers
	err := util.LoadJsonFile("/robot/services/containers.json", &containers)

	if err != nil {
		return nil, err
	}

	return containers.Containers, nil
}

func AddContainerDepsToMap(deps_map map[string]Container, container_map map[string]Container, target string) {
	deps_map[target] = container_map[target]

	for _, dep := range container_map[target].Deps {
		AddContainerDepsToMap(deps_map, container_map, dep)
	}
}

func GetOrderedContainers(containers []Container, target string) ([]Container, error) {
	var to_build []Container

	if target == "" {
		to_build = containers
	} else {
		container_map := make(map[string]Container)

		for _, container := range containers {
			container_map[container.Name] = container
		}

		deps_map := make(map[string]Container)
		AddContainerDepsToMap(deps_map, container_map, target)

		for _, val := range deps_map {
			to_build = append(to_build, val)
		}
	}

	return OrderContainersByDeps(to_build)
}

func GetContainer(containers []Container, name string) (Container, error) {
	for _, container := range containers {
		if container.Name == name {
			return container, nil
		}
	}
	return Container{}, errors.New("Container Not Found")
}

func AttachTerminal(ctx context.Context, cli *client.Client, waiter types.HijackedResponse, respID string, exec bool) error {
	wg := sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer wg.Done()
		io.Copy(os.Stdout, waiter.Reader)
	}()
	go func() {
		defer wg.Done()
		io.Copy(os.Stderr, waiter.Reader)
	}()
	go io.Copy(waiter.Conn, os.Stdin)

	if exec {
		if err := cli.ContainerExecStart(ctx, respID, container.ExecStartOptions{}); err != nil {
			return fmt.Errorf("failed to start exec: %s", err)
		}
	} else {
		if err := cli.ContainerStart(ctx, respID, container.StartOptions{}); err != nil {
			return fmt.Errorf("failed to start container: %s", err)
		}
	}

	// Terminal Attach
	fd := int(os.Stdin.Fd())
	var oldState *term.State
	if term.IsTerminal(fd) {
		var err error
		oldState, err = term.MakeRaw(fd)
		if err != nil {
			return fmt.Errorf("failed to attach terminal to container: %s", err)
		}
		defer term.Restore(fd, oldState)
	}

	if !exec {
		statusCh, errCh := cli.ContainerWait(ctx, respID, container.WaitConditionNotRunning)
		select {
		case err := <-errCh:
			if err != nil {
				return fmt.Errorf("failed to wait for container: %s", err)
			}
		case status := <-statusCh:
			if status.StatusCode != 0 {
				return fmt.Errorf("run command exited with status code: %d", status.StatusCode)
			}
		}
	}

	wg.Wait()

	return nil
}

func ExecCommandInRunningContainer(cli *client.Client, name string, cmd []string) error {
	ctx := GetDockerContext(cli)

	resp, err := cli.ContainerExecCreate(ctx, name, types.ExecConfig{
		User:         "root",
		Tty:          true,
		AttachStdin:  true,
		AttachStderr: true,
		AttachStdout: true,
		Cmd:          cmd,
	})
	if err != nil {
		return fmt.Errorf("failed to exec in container: %s", err)
	}

	waiter, err := cli.ContainerExecAttach(ctx, resp.ID, types.ExecStartCheck{})
	if err != nil {
		return fmt.Errorf("failed to attach to exec in container: %s", err)
	}
	defer waiter.Close()

	return AttachTerminal(ctx, cli, waiter, resp.ID, true)
}

func RunCommandInContainer(cli *client.Client, c *Container, botDef *bot_context.BotDef, cmd []string, tag string, gpus bool, privileged bool, containerName string) error {
	ctx := GetDockerContext(cli)

	injectedEnv := botDef.GetEnvironmentMapping()
	envStrings := []string{}
	for name, val := range injectedEnv {
		envStrings = append(envStrings, fmt.Sprintf("%s=%s", name, val))
	}

	cfg := container.Config{
		Image:        fmt.Sprintf("ghcr.io/carbonrobotics/robot/%s:%s", c.Name, tag),
		Tty:          true,
		AttachStdin:  true,
		AttachStdout: true,
		AttachStderr: true,
		OpenStdin:    true,
		Entrypoint:   cmd,
		Env:          envStrings,
	}

	binds := []string{
		fmt.Sprintf("%s:/bot:rw", botDef.EnvDef.Bot),
		fmt.Sprintf("%s:/calibration:rw", botDef.EnvDef.Calibration),
		fmt.Sprintf("%s:/config:rw", botDef.EnvDef.Config),
		fmt.Sprintf("%s:/data:rw", botDef.EnvDef.Data),
		fmt.Sprintf("%s:/logs:rw", botDef.EnvDef.Logs),
		fmt.Sprintf("%s:/models:rw", botDef.EnvDef.Models),
		"/var/run/docker.sock:/var/run/docker.sock",
		"/dev:/dev:rw",
		"/dev/shm:/dev/shm:rw",
	}

	if botDef.CheckoutDef.Mode == "user" {
		binds = append(binds, fmt.Sprintf("%s:/robot:rw", botDef.EnvDef.Robot))
		homePrefix := "home"
		if bot_context.GetHostPlatform() == "Darwin" {
			homePrefix = "Users"
		}
		awsDir := fmt.Sprintf("/%s/%s/.aws", homePrefix, botDef.CheckoutDef.User)
		err := os.MkdirAll(awsDir, os.ModePerm)
		if err == nil {
			binds = append(binds, fmt.Sprintf("%s:/root/.aws:rw", awsDir))
		}
	}

	runtime := ""
	if gpus {
		runtime = "nvidia"
	}

	hostCfg := container.HostConfig{
		NetworkMode: "host",
		Binds:       binds,
		AutoRemove:  true,
		Runtime:     runtime,
		Privileged:  privileged,
	}

	resp, err := cli.ContainerCreate(ctx, &cfg, &hostCfg, nil, nil, containerName)
	if err != nil {
		return fmt.Errorf("failed to create container: %s", err)
	}

	waiter, err := cli.ContainerAttach(ctx, resp.ID, container.AttachOptions{
		Stream: true,
		Stdout: true,
		Stderr: true,
		Stdin:  true,
	})

	if err != nil {
		return fmt.Errorf("failed to attach to container: %s", err)
	}
	defer waiter.Close()

	return AttachTerminal(ctx, cli, waiter, resp.ID, false)
}

// Save the image to a tarball
// If the tarball already exists, it will not be overwritten
// Caller is responsible for tarFilePath being a valid path
func Save(cli *client.Client, imageFullName string, tarFilePath string) (err error) {

	// check if tar already exists
	if _, err := os.Stat(tarFilePath); os.IsNotExist(err) {
		ctx := GetDockerContext(cli)

		logrus.Infof("Saving Image: %v to %v", imageFullName, tarFilePath)

		// get time that operation started
		start := time.Now()

		res, err := cli.ImageSave(ctx, []string{imageFullName})
		if err != nil {
			return fmt.Errorf("docker cli failed to save image %s: %s", imageFullName, err)
		}
		defer func() {
			closeErr := res.Close()
			if closeErr != nil {
				err = errors.Join(err, closeErr)
			}
		}()

		tmpFilePath := tarFilePath + ".tmp"

		tmpFile, err := os.Create(tmpFilePath)
		if err != nil {
			return fmt.Errorf("failed to create temporary tar file: %s", err)
		}
		defer func() {
			closeErr := tmpFile.Close()
			if closeErr != nil {
				err = errors.Join(err, closeErr)
			}
		}()

		defer func() {
			if err != nil {
				os.Remove(tmpFilePath)
			}
		}()

		_, err = io.Copy(tmpFile, res)
		if err != nil {
			return fmt.Errorf("failed to write tar file: %s", err)
		}

		err = os.Rename(tmpFilePath, tarFilePath)
		if err != nil {
			return fmt.Errorf("failed to rename temporary tar file: %s", err)
		}

		logrus.Infof("Image saved to %v in %v ms", tarFilePath, time.Since(start).Milliseconds())
	} else {
		logrus.WithError(err).Infof("Image already saved to %v", tarFilePath)
	}

	return nil
}

// load the image from a tarball
// Caller is responsible for tarFilePath being a valid path
func Load(cli *client.Client, tarFilePath string, verbose bool) (err error) {
	ctx := GetDockerContext(cli)

	logrus.Infof("Loading Image: %v", tarFilePath)

	tarFile, err := os.Open(tarFilePath)
	if err != nil {
		return fmt.Errorf("failed to open tar file: %s", err)
	}
	defer func() {
		closeErr := tarFile.Close()
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	res, err := cli.ImageLoad(ctx, tarFile, !verbose)
	if err != nil {
		return fmt.Errorf("failed to load container %s: %s", tarFilePath, err)
	}
	defer func() {
		closeErr := res.Body.Close()
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	dec := json.NewDecoder(res.Body)

	for dec.More() {
		var result map[string]interface{}
		err := dec.Decode(&result)
		if err != nil {
			return fmt.Errorf("failed to decode stream: %s", err)
		}

		var status string = ""
		var id string = ""
		var progress string = ""

		if statusData, ok := result["status"]; ok {
			status = statusData.(string)
		}

		if idData, ok := result["id"]; ok {
			id = idData.(string)
		}

		if progressData, ok := result["progress"]; ok {
			progress = progressData.(string)
		}

		if verbose {
			if id == "" {
				logrus.Info(status)
			} else if progress == "" {
				logrus.Infof("[%s] -> %s", id, status)
			} else {
				logrus.Infof("[%s] -> %s: %s", id, status, progress)
			}
		}
	}

	return nil
}

// read through the tarball and return the sha256 hash of the contents
func ComputeTarChecksum(tarFilePath string, verbose bool) (string, error) {
	tarFile, err := os.Open(tarFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to open tar file: %s", err)
	}
	defer func() {
		closeErr := tarFile.Close()
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	logrus.Infof("Calculating Checksum for Tarball: %v", tarFilePath)

	tarReader := tar.NewReader(tarFile)
	hash := sha256.New()
	start := time.Now()
	info := ""

	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", fmt.Errorf("failed to read tar header: %s", err)
		}

		hash.Write([]byte(header.Name)) // hash the name of every header

		switch header.Typeflag {
		case tar.TypeReg:
			_, err := io.Copy(hash, tarReader)
			if err != nil {
				return "", fmt.Errorf("failed to read tar file: %s", err)
			}
			info = fmt.Sprintf("Checksum: hashed file: %v", header.Name)
		case tar.TypeDir:
			info = fmt.Sprintf("Checksum: hashed dir: %v", header.Name)
		case tar.TypeSymlink:
			hash.Write([]byte(header.Linkname))
			info = fmt.Sprintf("Checksum: hashed symlink: %v -> %v", header.Name, header.Linkname)
		default:
			info = fmt.Sprintf("Checksum: ignored unknown file type content: %v", header.Name)
		}

		if verbose {
			logrus.Info(info)
		}
	}

	if verbose {
		logrus.Infof("Checksum calculated in %v ms", time.Since(start).Milliseconds())
	}

	return fmt.Sprintf("sha256:%x", hash.Sum(nil)), nil
}

func VerifyImageUsingChecksums(parentCtx context.Context, cli *client.Client, fullName string) (err error) {

	// Create a container that checks for feature flag and runs the script if supported
	// Set Entrypoint to empty array to override any existing entrypoint in the image
	createCtx, createCancel := context.WithTimeout(parentCtx, standardTimeout)
	defer createCancel()
	resp, err := cli.ContainerCreate(createCtx, &container.Config{
		Image:      fullName,
		Cmd:        []string{"sh", "-c", verifyImageScript},
		Entrypoint: []string{},
	}, nil, nil, nil, fmt.Sprintf("verify-image-%s", uuid.New().String()))
	if err != nil {
		return fmt.Errorf("create failed: %w", err)
	}
	defer func() {
		removeCtx, removeCancel := context.WithTimeout(parentCtx, standardTimeout)
		defer removeCancel()
		err2 := cli.ContainerRemove(removeCtx, resp.ID, container.RemoveOptions{Force: true})
		if err2 != nil {
			err = errors.Join(err, err2)
		}
	}()

	for _, warning := range resp.Warnings {
		logrus.Warnf("warning while creating container, %v: %v", fullName, warning)
	}

	startCtx, startCancel := context.WithTimeout(parentCtx, standardTimeout)
	defer startCancel()
	if err := cli.ContainerStart(startCtx, resp.ID, container.StartOptions{}); err != nil {
		return fmt.Errorf("start failed: %w", err)
	}
	waitCtx, waitCancel := context.WithTimeout(parentCtx, waitingTimeout) // compare can take a while
	defer waitCancel()
	statusCh, errCh := cli.ContainerWait(waitCtx, resp.ID, container.WaitConditionNextExit)
	select {
	case status := <-statusCh:
		switch status.StatusCode {
		case 0:
			logrus.Infof("Image verification succeeded for %v", fullName)
		case 2:
			return fmt.Errorf("verify_image script has a dead canary (exit code: %d)", status.StatusCode)
		case 3:
			return fmt.Errorf("verify_image script has size 0 (exit code: %d)", status.StatusCode)
		default:
			return fmt.Errorf("unknown error code: %d", status.StatusCode)
		}
	case err := <-errCh:
		return fmt.Errorf("error waiting: %w", err)
	}

	return nil
}

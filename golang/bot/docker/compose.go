package docker

import (
	"fmt"
	"io"
	"os"
	"sort"
	"sync"
	"time"

	"github.com/compose-spec/compose-go/v2/types"
	"github.com/docker/compose/v2/pkg/api"
	"github.com/docker/compose/v2/pkg/compose"
	"github.com/sirupsen/logrus"

	"github.com/docker/cli/cli/command"
	docker_cli "github.com/docker/cli/cli/command"
	cliflags "github.com/docker/cli/cli/flags"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
)

const (
	DefaultTimeout = 10 * time.Second
)

func GetDockerCommandCLI() (command.Cli, error) {
	dockerCli, err := docker_cli.NewDockerCli()
	if err != nil {
		logrus.WithError(err).Error("Error creating docker cli")
		return nil, err
	}
	err = dockerCli.Initialize(&cliflags.ClientOptions{
		Hosts: []string{"unix:///var/run/docker.sock"},
	})
	if err != nil {
		logrus.WithError(err).Error("Error intializing docker cli")
		return nil, err
	}
	return dockerCli, nil
}

func GetComposeService() (api.Service, error) {
	dockerCli, err := GetDockerCommandCLI()
	if err != nil {
		return nil, err
	}
	return compose.NewComposeService(dockerCli), nil
}

func CleanupContainers(cli *client.Client, services []string) error {
	ctx := GetDockerContext(cli)
	for _, service := range services {
		// We purposefully ignore any error here
		cli.ContainerRemove(ctx, service, container.RemoveOptions{
			RemoveVolumes: true,
			Force:         true,
		})

	}

	return nil
}

func StartServices(cli *client.Client, service api.Service, project *types.Project, services []string) error {
	ctx := GetDockerContext(cli)
	timeout := DefaultTimeout

	enabled, err := project.GetServices(services...)
	if err != nil {
		return err
	}

	x := project.Services
	project.Services = enabled

	err = service.Up(ctx, project, api.UpOptions{
		Create: api.CreateOptions{
			RemoveOrphans:        true,
			IgnoreOrphans:        false,
			Services:             services,
			Recreate:             api.RecreateForce,
			RecreateDependencies: api.RecreateDiverged,
			Timeout:              &timeout,
		},
		Start: api.StartOptions{
			Wait:     false,
			Project:  project,
			Services: services,
		},
	})

	project.Services = x

	return err
}

func StopServices(cli *client.Client, service api.Service, project *types.Project, services []string) error {
	ctx := GetDockerContext(cli)
	timeout := DefaultTimeout

	enabled, err := project.GetServices(services...)
	if err != nil {
		return err
	}

	x := project.Services
	project.Services = enabled

	err = service.Down(ctx, project.Name, api.DownOptions{
		RemoveOrphans: false,
		Project:       project,
		Timeout:       &timeout,
	})
	project.Services = x

	return err
}

func TailService(cli *client.Client, name string, lines int) error {
	ctx := GetDockerContext(cli)
	reader, err := cli.ContainerLogs(ctx, name, container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Follow:     true,
		Tail:       fmt.Sprintf("%d", lines),
	})

	if err != nil {
		return err
	}

	defer reader.Close()

	wg := sync.WaitGroup{}

	wg.Add(2)
	go func() {
		defer wg.Done()
		io.Copy(os.Stdout, reader)
	}()
	go func() {
		defer wg.Done()
		io.Copy(os.Stderr, reader)
	}()

	wg.Wait()

	return nil
}

func PsServices(cli *client.Client, service api.Service, project *types.Project, services []string) ([]api.ContainerSummary, error) {
	ctx := GetDockerContext(cli)

	enabled, err := project.GetServices(services...)
	if err != nil {
		return nil, err
	}

	x := project.Services
	project.Services = enabled

	z, err := service.Ps(ctx, project.Name, api.PsOptions{
		Services: project.ServiceNames(),
		All:      true,
	})

	sort.Slice(z, func(i, j int) bool {
		return z[i].Name < z[j].Name
	})

	project.Services = x

	return z, err
}

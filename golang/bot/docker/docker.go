package docker

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"os"

	"github.com/docker/cli/cli/config"
	"github.com/docker/cli/cli/config/types"
	"github.com/docker/docker/client"
	"github.com/moby/buildkit/session"
	"github.com/moby/buildkit/session/auth/authprovider"
)

const (
	CarbonUser = "carbon"
	CarbonPass = "****************************************"
)

func AsHttp(c *client.Client) error {
	return nil
}

func GetAuthConfigsAsSession() session.Attachable {
	configFile := config.LoadDefaultConfigFile(os.Stderr)
	configFile.AuthConfigs["ghcr.io"] = types.AuthConfig{
		Username:      CarbonUser,
		Password:      CarbonPass,
		ServerAddress: "ghcr.io",
	}
	return authprovider.NewDockerAuthProvider(configFile, nil)
}

func GetDockerCLI() (*client.Client, error) {
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return nil, err
	}

	return cli, nil
}

func GetDockerContext(cli *client.Client) context.Context {
	ctx := context.Background()
	return ctx
}

func GetRegistryAuthString() (string, error) {
	// TODO Figure out better authentication
	authConfig := types.AuthConfig{
		Username: CarbonUser,
		Password: CarbonPass,
	}
	encodedJSON, err := json.Marshal(authConfig)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(encodedJSON), nil
}

package docker

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestContainer_RelativeBaseFile(t *testing.T) {
	tests := []struct {
		name                     string
		baseFile                 string
		expectedRelativeBaseFile string
	}{
		{
			"No base file",
			"",
			"",
		},
		{
			"common container example from containers.json",
			"containers/common/Dockerfile",
			"services/containers/common/Dockerfile",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			container := &Container{BaseFile: test.baseFile}
			assert.Equal(t, test.expectedRelativeBaseFile, container.RelativeBaseFile())
		})
	}
}

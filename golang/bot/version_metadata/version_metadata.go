package version_metadata

// Note, we duplicated version metadata protos for this version metadata upload because GoBot can't import the generated protots to build
// So it's duplicated here until we can figure out how to build gobot, then just run the built version.
// for now we seem to build gobot for every run so the duplicate protos are required.

import (
	"context"
	"fmt"

	"io/ioutil"
	"os"
	"strings"

	"golang.org/x/oauth2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/oauth"

	"github.com/carbonrobotics/robot/golang/lib/auth"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/flosch/pongo2/v5"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v2"
)

type Client struct {
}

// UploadVersionMetadata posts version metadata to the metadata version service
// Expects MAKA_ROBOT_DIR, MAKA_ROBOT_NAME, MAKA_GEN, CARBON_ROBOT_USERNAME, CARBON_ROBOT_PASSWORD in env
func UploadVersionMetadata(ctx context.Context, version, systemVersion, IDMHost, serviceURL string) error {
	logrus.Infof("Starting UploadVersionMetadata")

	robot, err := environment.GetRobot()
	if err != nil {
		return err
	}

	genContainers, err := getContainersPerGen(version, robot.MakaRobotDir)
	if err != nil {
		return fmt.Errorf("error reading containers list: %v", err)
	}

	tokenURL := fmt.Sprintf("https://%v/oauth/token", IDMHost)
	token, err := auth.RobotUserPassLogin(ctx, tokenURL, robot)
	if err != nil {
		return err
	}

	opts := []grpc.DialOption{
		grpc.WithPerRPCCredentials(oauth.TokenSource{TokenSource: oauth2.StaticTokenSource(token)}),
		grpc.WithTransportCredentials(credentials.NewClientTLSFromCert(nil, "")),
	}
	conn, err := grpc.NewClient(serviceURL, opts...)
	if err != nil {
		return err
	}
	defer conn.Close()

	vmClient := NewVersionMetadataServiceClient(conn)

	for gen, containers := range genContainers {
		if _, err := vmClient.UploadVersionMetadata(ctx, &UploadVersionMetadataRequest{
			Version:         version,
			Gen:             gen,
			VersionMetadata: &VersionMetadata{SystemVersion: systemVersion, Containers: containers},
		}); err != nil {
			logrus.Fatalf("Error uploading metadata: %v", err)
		}
	}
	logrus.Infof("UploadVersionMetadata successful.")
	return nil
}

func getContainersPerGen(version string, root_dir string) (map[string][]string, error) {
	rolesContent, err := ioutil.ReadFile(root_dir + "/services/roles.yaml")
	if err != nil {
		return nil, err
	}

	roles := map[string][]string{}

	err = yaml.Unmarshal(rolesContent, &roles)
	if err != nil {
		return nil, err
	}

	servicesPerGen := make(map[string][]string)
	for _, gen := range []string{string(environment.CarbonGenBud), string(environment.CarbonGenSlayer), string(environment.CarbonGenReaper), string(environment.CarbonGenRtc)} {
		roleSet := map[string]bool{}
		serviceSet := map[string]bool{}

		switch gen {
		case string(environment.CarbonGenBud):
			roleSet[string(environment.CarbonRoleBud)] = true
		case string(environment.CarbonGenSlayer):
			roleSet[string(environment.CarbonRoleCommand)] = true
			roleSet[string(environment.CarbonRoleRow)] = true
		case string(environment.CarbonGenReaper):
			roleSet[string(environment.CarbonRoleCommand)] = true
			roleSet[string(environment.CarbonRoleModule)] = true
		case string(environment.CarbonGenRtc):
			roleSet[string(environment.CarbonRoleRtc)] = true
		default:
			roleSet[string(environment.CarbonRoleBud)] = true
		}

		for role := range roleSet {
			if services, ok := roles[role]; ok {
				for _, service := range services {
					serviceSet[service] = true
				}
			}
		}

		genResults := []string{}

		for service := range serviceSet {
			genResults = append(genResults, service)
		}

		servicesPerGen[gen] = genResults
	}

	containersPerGen := make(map[string][]string)
	for gen, services := range servicesPerGen {
		containerSet := map[string]bool{
			"gobot": true, // Gobot is always required but not in a service
		}

		for _, service := range services {
			containers, err := GetContainersForService(root_dir, service, version)

			if err != nil {
				return nil, err
			}

			for _, container := range containers {
				name := StripContainerName(container)
				if name != "" {
					containerSet[name] = true
				}
			}
		}

		containersArr := make([]string, 0)
		for name := range containerSet {
			containersArr = append(containersArr, name)
		}

		containersPerGen[gen] = containersArr
	}

	return containersPerGen, nil
}

const (
	RegistryPrefix = "ghcr.io/carbonrobotics/robot/"
)

func StripContainerName(container string) string {
	splits := strings.Split(container, ":") // Splits the Tag

	if len(splits) == 0 {
		return "" // Improper Version Tag, we discard
	}

	fullName := splits[0]

	name := strings.TrimPrefix(fullName, RegistryPrefix)

	if name == fullName {
		return "" // Improper repo, we discard
	}

	return name
}

type DockerService struct {
	Image string `yaml:"image"`
}
type DockerServices struct {
	Services map[string]DockerService `yaml:"services"`
}

func GetContainersForService(root_dir string, service string, version string) ([]string, error) {
	fileContent, err := ioutil.ReadFile(fmt.Sprintf("%s/services/compositions/%s.yaml.j2", root_dir, service))
	if err != nil {
		return nil, err
	}

	if err != nil {
		return nil, err
	}

	pongoCtx := pongo2.Context{
		"CARBON_VERSION_TAG": version,
	}

	tpl, err := pongo2.FromBytes(fileContent)
	if err != nil {
		return nil, err
	}

	out, err := tpl.Execute(pongoCtx)
	if err != nil {
		return nil, err
	}

	conf := DockerServices{}

	err = yaml.Unmarshal([]byte(out), &conf)

	if err != nil {
		return nil, err
	}

	results := []string{}

	for _, service := range conf.Services {
		result := os.Expand(service.Image, func(key string) string {
			env := map[string]string{
				"CARBON_VERSION_TAG": version,
			}

			splits := strings.Split(key, ":")

			if len(splits) == 0 {
				return ""
			}

			if val, ok := env[splits[0]]; ok {
				return val
			}
			return ""
		})
		results = append(results, result)
	}

	return results, nil
}

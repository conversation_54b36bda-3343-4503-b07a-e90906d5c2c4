package util

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/flosch/pongo2/v5"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v2"
)

func LoadJsonFile(filepath string, v interface{}) error {
	jsonFile, err := os.Open(filepath)
	if err != nil {
		return fmt.Errorf("error opening file: %s error: %w", filepath, err)
	}
	defer jsonFile.Close()

	if err := json.NewDecoder(jsonFile).Decode(v); err != nil {
		return fmt.Errorf("error unmarshalling file: %s error: %w", filepath, err)
	}
	return nil
}

func LoadYamlFile(filepath string, v interface{}) error {
	yamlFile, err := os.Open(filepath)
	if err != nil {
		return fmt.Errorf("error opening file: %s error: %w", filepath, err)
	}
	defer yamlFile.Close()

	if err := yaml.NewDecoder(yamlFile).Decode(v); err != nil {
		return fmt.Errorf("error unmarshalling file: %s erorr: %w", filepath, err)
	}

	return nil
}

func LoadPongoFile(path string, ctx pongo2.Context) (string, error) {

	tpl, err := pongo2.FromFile(path)
	if err != nil {
		return "", err
	}

	out, err := tpl.Execute(ctx)
	if err != nil {
		return "", err
	}

	return out, nil

}

func LogFatalError(err error) {
	if err != nil {
		logrus.Fatal(err)
	}
}

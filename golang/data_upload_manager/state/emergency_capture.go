package state

import (
	"sync"
)

type DataCaptureState struct {
	capturing bool
	mu        sync.Mutex
	files     []string
	completed bool
}

func NewDataCaptureState() DataCaptureState {
	return DataCaptureState{
		capturing: true,
		completed: false,
	}
}

func (dataCaptureState *DataCaptureState) SetCapturing(capturing bool) {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	dataCaptureState.capturing = capturing
}

func (dataCaptureState *DataCaptureState) AppendFile(file string) {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	dataCaptureState.files = append(dataCaptureState.files, file)
}

func (dataCaptureState *DataCaptureState) EmptyFiles() {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	dataCaptureState.files = nil
}

func (dataCaptureState *DataCaptureState) SetCompleted(completed bool) {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	dataCaptureState.completed = completed
}

func (dataCaptureState *DataCaptureState) IsCapturing() bool {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	return dataCaptureState.capturing
}

func (dataCaptureState *DataCaptureState) ImagesCaptured() uint32 {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	return uint32(len(dataCaptureState.files))
}

func (dataCaptureState *DataCaptureState) HasCompleted() bool {
	dataCaptureState.mu.Lock()
	defer dataCaptureState.mu.Unlock()
	return dataCaptureState.completed
}

package state

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestDistanceAccumulator(t *testing.T) {
	distanceAccumulator := NewDistanceAccumulator(5)

	for i := 0; i < 6; i++ {
		distanceAccumulator.Add(TimestampedDistance{
			TimestampMs: uint64(i),
			DistanceMm:  float64(i * i),
		})
	}

	distances := distanceAccumulator.Get()

	t.Run("Works as expected", func(t *testing.T) {
		assert.Equal(t, len(distances), 5)
		for i := 0; i < 5; i++ {
			assert.Equal(t, distances[i].TimestampMs, uint64(i+1))
		}
	})
}

package state

import (
	"sync"
)

type Method int32

const (
	USB Method = iota
	Wireless
)

type DataUploadState struct {
	uploading bool
	method    Method
	mu        sync.Mutex
	files     []string
	completed bool
}

func NewDataUploadState(method Method) DataUploadState {
	return DataUploadState{
		uploading: false,
		method:    method,
		completed: false,
	}
}

func (dataUploadState *DataUploadState) SetUploading(uploading bool) {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	dataUploadState.uploading = uploading
}

func (dataUploadState *DataUploadState) SetMethod(method Method) {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	dataUploadState.method = method
}

func (dataUploadState *DataUploadState) AppendFile(file string) {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	dataUploadState.files = append(dataUploadState.files, file)
}

func (dataUploadState *DataUploadState) EmptyFiles() {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	dataUploadState.files = nil
}

func (dataUploadState *DataUploadState) SetCompleted(completed bool) {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	dataUploadState.completed = completed
}

func (dataUploadState *DataUploadState) IsUploading() bool {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	return dataUploadState.uploading
}

func (dataUploadState *DataUploadState) Method() Method {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	return dataUploadState.method
}

func (dataUploadState *DataUploadState) ImagesUploaded() uint32 {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	return uint32(len(dataUploadState.files))
}

func (dataUploadState *DataUploadState) HasCompleted() bool {
	dataUploadState.mu.Lock()
	defer dataUploadState.mu.Unlock()
	return dataUploadState.completed
}

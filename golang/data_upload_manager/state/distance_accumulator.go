package state

import (
	"sync"
)

type TimestampedDistance struct {
	TimestampMs uint64
	DistanceMm  float64
}

type DistanceAccumulator struct {
	distances    []TimestampedDistance
	maxDistances uint64
	mu           sync.Mutex
}

func NewDistanceAccumulator(maxDistances uint64) *DistanceAccumulator {
	distanceAccumulator := DistanceAccumulator{
		distances:    make([]TimestampedDistance, maxDistances),
		maxDistances: maxDistances,
	}

	return &distanceAccumulator
}

func (d *DistanceAccumulator) Add(timestampedDistance TimestampedDistance) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.distances = append(d.distances, timestampedDistance)
	for uint64(len(d.distances)) > d.maxDistances {
		d.distances = d.distances[1:]
	}
}

func (d *DistanceAccumulator) Get() []TimestampedDistance {
	d.mu.Lock()
	defer d.mu.Unlock()
	distances := d.distances
	return distances
}

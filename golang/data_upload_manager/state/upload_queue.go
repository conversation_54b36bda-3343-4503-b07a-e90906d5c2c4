package state

import (
	"sync"

	pq "github.com/carbonrobotics/go-priority-queue"
	"github.com/sirupsen/logrus"
)

type UploadQueue struct {
	mu      sync.Mutex
	queue   pq.PriorityQueue
	maxSize int64
}

func NewUploadQueue(maxSize int64) UploadQueue {
	return UploadQueue{
		queue:   pq.New(),
		maxSize: maxSize,
	}
}

func (uq *UploadQueue) Push(score float64, filename string) {
	uq.mu.Lock()
	defer uq.mu.Unlock()
	// The underlying heap prioritizes lower scores but we want to prioritize higher scores, so we invert here
	score = -1 * score
	uq.queue.Insert(filename, score)
	qlen := int64(uq.queue.Len())
	if qlen > uq.maxSize*2 {
		queue := pq.New()
		var i int64
		for i = 0; i < uq.maxSize; i++ {
			popFileName, popScore, err := uq.queue.Pop()
			if err != nil {
				logrus.WithError(err).Warn("failed to pop queue")
			} else {
				if str, ok := popFileName.(string); ok {
					queue.Insert(str, popScore)
				}
			}
		}

		uq.queue = queue
		logrus.Infof("rebalanced upload queue from %v to %v", qlen, uq.queue.Len())
	}
}

func (uq *UploadQueue) PopMax() (string, float64, bool) {
	uq.mu.Lock()
	defer uq.mu.Unlock()
	filename, score, err := uq.queue.Pop()
	// The underlying heap prioritizes lower scores, so we invert here to get the score back in its original space
	score = -1 * score
	if err != nil {
		return "", 0.0, false
	}
	res, ok := filename.(string)
	if ok {
		return res, score, ok
	}
	return "", 0.0, ok
}

func (uq *UploadQueue) Len() int {
	uq.mu.Lock()
	defer uq.mu.Unlock()
	return uq.queue.Len()
}

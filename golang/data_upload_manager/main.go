package main

import (
	"context"
	"fmt"
	"net"
	"os/signal"
	"sync"
	"syscall"

	"time"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/services"
	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/client_owner"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/carbonrobotics/robot/golang/lib/logging"
	"github.com/carbonrobotics/robot/golang/lib/metrics"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/sentry_reporter"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

func main() {
	logrus.Infof("Starting Data Upload Manager")
	stopCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	metrics.Serve("", 62004)
	env, err := environment.GetRobot()
	if err != nil {
		logrus.Fatalln("failed to parse environment:", err)
	}

	// Configuration

	configSubscriber := config.NewConfigSubscriber(config.MakeRobotLocalAddr(61001))
	configSubscriber.AddConfigTree(
		"data_upload_manager",
		fmt.Sprintf("%s/data_upload_manager", config.GetComputerConfigPrefix()),
		"services/data_upload_manager.yaml")
	configSubscriber.AddConfigTree("common", "common", "services/common.yaml")
	configSubscriber.AddConfigTree("commander", fmt.Sprintf("%s/commander", config.GetComputerConfigPrefix()), "services/commander.yaml")
	configSubscriber.Start()
	configSubscriber.WaitUntilReady()

	if configSubscriber.GetConfigNode("common", "environment").GetStringValue() == "production" {
		sentry_reporter.InitializeSentry()
		defer sentry_reporter.HandlePanic()
	}

	dumConfig := configSubscriber.GetConfigNode("data_upload_manager", "")

	imageRetrievalInterval := configSubscriber.GetConfigNode("data_upload_manager", "retrieval_interval_seconds")
	maxImagesPerHour := configSubscriber.GetConfigNode("data_upload_manager", "max_images_per_hour")
	toUploadImageExpirationHours := configSubscriber.GetConfigNode("data_upload_manager", "to_upload_expiration_hours")
	emergencyImageExpirationHours := configSubscriber.GetConfigNode("data_upload_manager", "emergency_image_expiration_hours")
	completedTransferImageExpirationHours := configSubscriber.GetConfigNode("data_upload_manager", "completed_transfer_expiration_hours")
	use_online_upload_flow := configSubscriber.GetConfigNode("data_upload_manager", "use_online_upload_flow")
	use_offline_upload_flow := configSubscriber.GetConfigNode("data_upload_manager", "use_offline_upload_flow")
	imageTransferInterval := configSubscriber.GetConfigNode("data_upload_manager", "transfer_interval_seconds")
	offlineAgentMaxLoad := configSubscriber.GetConfigNode("data_upload_manager", "offline_agent_max_load")

	emergencyUploadProportions := configSubscriber.GetConfigNode("data_upload_manager", "emergency_capture_proportions")

	emergencySessionName := configSubscriber.GetConfigNode("data_upload_manager", "emergency_session_name")
	emergencyCaptureTarget := configSubscriber.GetConfigNode("data_upload_manager", "emergency_capture_target")
	emergencyUploadTarget := configSubscriber.GetConfigNode("data_upload_manager", "emergency_upload_target")
	maxQueueSize := configSubscriber.GetConfigNode("data_upload_manager", "max_queue_size")

	// Create State

	weed_tracking_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	recency_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	weed_margin_max_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	crop_margin_max_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	ambiguous_weed_count_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	ambiguous_crop_count_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	driptape_upload_queue := state.NewUploadQueue(maxQueueSize.GetIntValue())
	uploadQueues := make(map[string]*state.UploadQueue)
	uploadQueues[services.WeedTracking] = &weed_tracking_upload_queue
	uploadQueues[services.Recency] = &recency_upload_queue
	uploadQueues[services.WeedMarginMax] = &weed_margin_max_upload_queue
	uploadQueues[services.CropMarginMax] = &crop_margin_max_upload_queue
	uploadQueues[services.AmbiguousWeedCount] = &ambiguous_weed_count_upload_queue
	uploadQueues[services.AmbiguousCropCount] = &ambiguous_crop_count_upload_queue
	uploadQueues[services.Driptape] = &driptape_upload_queue

	chipUploadQueueObj := state.NewUploadQueue(maxQueueSize.GetIntValue())
	chipUploadQueue := &chipUploadQueueObj

	distanceAccumulationNode := configSubscriber.GetConfigNode("data_upload_manager", "distance_accumulation")

	distanceAccumulator := state.NewDistanceAccumulator(distanceAccumulationNode.GetNode("max_distances").GetUIntValue())

	minWeedTrackingScore := configSubscriber.GetConfigNode("data_upload_manager", "min_weed_tracking_score")
	minRecencyScore := configSubscriber.GetConfigNode("data_upload_manager", "min_recency_score")
	minWeedMarginMaxScore := configSubscriber.GetConfigNode("data_upload_manager", "min_weed_margin_max_score")
	minCropMarginMaxScore := configSubscriber.GetConfigNode("data_upload_manager", "min_crop_margin_max_score")
	minAmbiguousWeedCount := configSubscriber.GetConfigNode("data_upload_manager", "min_ambiguous_weed_count_score")
	minAmbiguousCropCount := configSubscriber.GetConfigNode("data_upload_manager", "min_ambiguous_crop_count_score")
	minDriptapeScore := configSubscriber.GetConfigNode("data_upload_manager", "min_driptape_score")
	minUnknownPlantScore := configSubscriber.GetConfigNode("data_upload_manager", "min_unknown_plant_score")

	crop := configSubscriber.GetConfigNode("commander", "current_crop")
	currentCropIDNode := configSubscriber.GetConfigNode("commander", "current_crop_id")
	admin := configSubscriber.GetConfigNode("data_upload_manager", "admin")
	enableEmergencyPriority := configSubscriber.GetConfigNode("data_upload_manager", "enable_emergency_priority")
	predictAperture := configSubscriber.GetConfigNode("data_upload_manager", "predict_camera_aperture")
	weedingDiagnosticsNode := configSubscriber.GetConfigNode("data_upload_manager", "weeding_diagnostics")
	plantCaptchaNode := configSubscriber.GetConfigNode("data_upload_manager", "plant_captcha")
	softwareVersion := env.CarbonVersionTag

	minScores := make(map[string]*config.ConfigTree)
	minScores[services.WeedTracking] = minWeedTrackingScore
	minScores[services.Recency] = minRecencyScore
	minScores[services.WeedMarginMax] = minWeedMarginMaxScore
	minScores[services.CropMarginMax] = minCropMarginMaxScore
	minScores[services.AmbiguousWeedCount] = minAmbiguousWeedCount
	minScores[services.AmbiguousCropCount] = minAmbiguousCropCount
	minScores[services.Driptape] = minDriptapeScore
	minScores[services.UnknownPlant] = minUnknownPlantScore

	// Create GRPC Server
	var opts []grpc.ServerOption
	opts = append(opts, grpc.UnaryInterceptor(sentry_reporter.PanicInterceptor))
	grpcServer := grpc.NewServer(opts...)

	// Auth & Clients

	lightweightBurstRecordConfig := configSubscriber.GetConfigNode("data_upload_manager", "lightweight_burst_records")

	a0cfg := auth.NewRobotAuth0Config(env, env.MakaAuthScopes, "http://127.0.0.1:61900/oauth/token")
	httpClient := a0cfg.AuthenticatedHttpClient(context.Background())
	httpClient.Timeout = time.Hour
	robotClient := services.RobotClient{HttpClient: httpClient, Environment: env}
	robotName := env.MakaRobotName

	configClient := config.NewConfigClient(config.MakeRobotLocalAddr(61001))
	hardwareManagerClient := hardware_manager.NewHardwareManagerClient(config.MakeRobotLocalAddr(61006))

	redisClient := redis.New(env)
	_ = logging.NewLoggingService(grpcServer, redisClient, "data_upload_manager", &env)

	clientOwner := client_owner.NewClientOwner(redisClient, configSubscriber.GetConfigNode("common", ""), &env)

	emergencyService := services.NewEmergencyService(
		grpcServer,
		stopCtx,
		clientOwner.GetRowClients(),
		robotClient,
		robotName,
		configClient,
		emergencySessionName,
		emergencyCaptureTarget,
		emergencyUploadTarget,
		admin,
		enableEmergencyPriority,
		hardwareManagerClient,
		emergencyUploadProportions,
		use_offline_upload_flow,
		offlineAgentMaxLoad,
		maxImagesPerHour,
		predictAperture,
		softwareVersion,
		redisClient,
		weedingDiagnosticsNode,
		plantCaptchaNode,
	)

	if err := emergencyService.CleanOldDirectories(time.Duration(emergencyImageExpirationHours.GetIntValue()) * time.Hour); err != nil {
		logrus.Errorf("Couldn't remove old files: %v", err)
	}

	if err := emergencyService.CleanOldSnapshots(time.Duration(emergencyImageExpirationHours.GetIntValue()) * time.Hour); err != nil {
		logrus.Errorf("Couldn't remove old files: %v", err)
	}

	_ = emergencyService

	var wg sync.WaitGroup

	wg.Add(1)
	wdUploader := services.NewWeeedingDiagnosticsUploader()
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		wdUploader.ResumeUploads(robotClient, redisClient, weedingDiagnosticsNode)
	}()

	wg.Add(1)
	pcUploader := services.NewPlantCaptchaUploader()
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		pcUploader.ResumeUploads(robotClient, redisClient, plantCaptchaNode)
	}()

	wg.Add(1)
	addr := fmt.Sprintf("0.0.0.0:%d", 61003)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logrus.Fatalf("failed to listen: %v", err)
	}

	logrus.Infof("Started Data Upload Manager Server at: %s", addr)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil {
			logrus.Fatalf("Failed to Serve GRPC Server: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunLightweightBurstRecordRetrieval(stopCtx, clientOwner.GetRowClients(), lightweightBurstRecordConfig); err != nil {
			logrus.Errorf("Failed to run artifact retrieval for lightweight burst records: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunRetrieve(stopCtx, uploadQueues, imageRetrievalInterval, minScores, toUploadImageExpirationHours, robotName, clientOwner.GetRowClients(), crop, currentCropIDNode, admin, predictAperture, softwareVersion, redisClient); err != nil {
			logrus.Errorf("Failed to run retrieve: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunP2PRetrieval(stopCtx, clientOwner.GetRowClients(), configSubscriber.GetConfigNode("data_upload_manager", ""), configSubscriber.GetConfigNode("commander", ""), robotName, softwareVersion); err != nil {
			logrus.Errorf("Failed to run retrieve P2P: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunUpload(services.UploadParams{
			StopCtx:      stopCtx,
			UploadQueues: uploadQueues,
			DumConfig:    dumConfig,
			RobotClient:  robotClient,
		}); err != nil {
			logrus.Errorf("Failed to run upload: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err := services.RunBurstRecordUpload(stopCtx, lightweightBurstRecordConfig, use_online_upload_flow, robotClient)
		if err != nil {
			logrus.Errorf("Failed to run burst record upload: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		err := services.RunP2PUpload(stopCtx, configSubscriber.GetConfigNode("data_upload_manager", ""), use_online_upload_flow, robotClient)
		if err != nil {
			logrus.Errorf("Failed to run p2p upload: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunTransfer(stopCtx, imageTransferInterval, completedTransferImageExpirationHours, use_offline_upload_flow, configClient, robotName, hardwareManagerClient, offlineAgentMaxLoad); err != nil {
			logrus.Errorf("Failed to run transfer: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunDistanceAccumulation(stopCtx, distanceAccumulator, configSubscriber.GetConfigNode("data_upload_manager", ""), hardwareManagerClient); err != nil {
			logrus.Errorf("Failed to run distance accumulator: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunPredictBurstRetrieval(stopCtx, clientOwner.GetRowClients(), configSubscriber.GetConfigNode("data_upload_manager", ""), redisClient, robotName, distanceAccumulator, configSubscriber.GetConfigNode("commander", ""), softwareVersion); err != nil {
			logrus.Errorf("Failed to run predict burst retrieval: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunPredictBurstUpload(stopCtx, configSubscriber.GetConfigNode("data_upload_manager", ""), robotClient); err != nil {
			logrus.Errorf("Failed to run predict burst upload: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunChipRetrieval(stopCtx, clientOwner.GetRowClients(), configSubscriber.GetConfigNode("data_upload_manager", ""), configSubscriber.GetConfigNode("commander", ""), robotName, softwareVersion, redisClient, chipUploadQueue); err != nil {
			logrus.Errorf("Failed to run chip retrieval: %v", err)
		}
	}()

	wg.Add(1)
	go func() {
		defer sentry_reporter.HandlePanic()
		defer wg.Done()
		if err := services.RunChipUpload(stopCtx, robotClient, configSubscriber.GetConfigNode("data_upload_manager", ""), chipUploadQueue); err != nil {
			logrus.Errorf("Failed to run chip upload: %v", err)
		}
	}()

	<-stopCtx.Done()
	wg.Wait()
}

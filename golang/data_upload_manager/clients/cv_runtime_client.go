package clients

import (
	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type CVRuntimeClient struct {
	Identifier int
	Conn       *grpc.ClientConn
	Client     pb.CVRuntimeServiceClient
}

func GetCVRuntimeClient(identifier int, server_address string) *CVRuntimeClient {
	logrus.Infof("Creating client with server_address %v", server_address)

	var opts []grpc.DialOption
	opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	max_message_size := 1024 * 1024 * 50

	call_opts := grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(max_message_size), grpc.MaxCallSendMsgSize(max_message_size))
	opts = append(opts, call_opts)
	conn, err := grpc.Dial(server_address, opts...)
	if err != nil {
		logrus.Infof("Failed to dial: %v", err)
	}
	return &CVRuntimeClient{
		Identifier: identifier,
		Conn:       conn,
		Client:     pb.NewCVRuntimeServiceClient(conn),
	}
}

func (c *CVRuntimeClient) Close() {
	c.Conn.Close()
}

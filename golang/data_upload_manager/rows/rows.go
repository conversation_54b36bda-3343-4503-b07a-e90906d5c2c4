package rows

import (
	"github.com/carbonrobotics/robot/golang/data_upload_manager/clients"
	"github.com/carbonrobotics/robot/golang/lib/aimbot_client"
)

type RowClients struct {
	AimbotClient    *aimbot_client.AimbotClient
	CVRuntimeClient *clients.CVRuntimeClient
}

func GetNewRowClients(cvRuntimeClient *clients.CVRuntimeClient, aimbotClient *aimbot_client.AimbotClient) *RowClients {
	return &RowClients{
		AimbotClient:    aimbotClient,
		CVRuntimeClient: cvRuntimeClient,
	}
}

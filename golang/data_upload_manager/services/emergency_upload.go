package services

import (
	"context"
	"io/ioutil"
	"os"
	"path/filepath"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

type EmergencyUploadSession struct {
	stopCtx               context.Context
	sessionStopChan       chan bool
	dataUploadState       *state.DataUploadState
	robotClient           RobotClient
	emergencySessionName  string
	emergencyUploadTarget *config.ConfigTree
	robotId               string
	useOfflineFlow        *config.ConfigTree
	hardwareManagerClient *hardware_manager.HardwareManagerClient
	offlineAgentMaxLoad   *config.ConfigTree
}

func NewEmergencyUploadSession(stopCtx context.Context, robotClient RobotClient, emergencySessionName string, emergencyUploadTarget *config.ConfigTree, method state.Method, robotId string, useOfflineFlow *config.ConfigTree, hardwareManagerClient *hardware_manager.HardwareManagerClient, offlineAgentMaxLoad *config.ConfigTree) *EmergencyUploadSession {
	uploadState := state.NewDataUploadState(method)
	state := EmergencyUploadSession{
		stopCtx:               stopCtx,
		sessionStopChan:       make(chan bool, 1),
		dataUploadState:       &uploadState,
		robotClient:           robotClient,
		emergencySessionName:  emergencySessionName,
		emergencyUploadTarget: emergencyUploadTarget,
		robotId:               robotId,
		useOfflineFlow:        useOfflineFlow,
		hardwareManagerClient: hardwareManagerClient,
		offlineAgentMaxLoad:   offlineAgentMaxLoad,
	}

	return &state
}

func getUSBSessionDir(robotId string, sessionName string) string {
	return filepath.Join(getUSBDir(), robotId, sessionName)
}

func (s *EmergencyUploadSession) Start() {
	s.dataUploadState.SetUploading(true)
	go s.runEmergencyUploadSession()
}

func (s *EmergencyUploadSession) Resume() {
	s.dataUploadState.SetUploading(true)
}

func (s *EmergencyUploadSession) Pause() {
	s.dataUploadState.SetUploading(false)
}

func (s *EmergencyUploadSession) Stop() {
	s.dataUploadState.SetUploading(false)
	s.dataUploadState.EmptyFiles()
	s.sessionStopChan <- true
}

func (s *EmergencyUploadSession) runEmergencyUploadSession() (err error) {
	defer func() {
		if err != nil {
			uploadSessionsFailedCounterVec.WithLabelValues(emergency).Inc()
		}
	}()
	sessionName := s.emergencySessionName
	sessionFromDirectory := getSessionDir(sessionName)
	logrus.Infof("Running emergency upload session: %v", sessionFromDirectory)

	if _, err := appFs.Stat(sessionFromDirectory); os.IsNotExist(err) {
		logrus.Errorf("Session Directory %v does not exist, cannot upload", sessionFromDirectory)
		return err
	}

	if s.dataUploadState.Method() == state.USB {
		sessionToDirectory := getUSBSessionDir(s.robotId, sessionName)
		if err := appFs.MkdirAll(sessionToDirectory, 0777); err != nil {
			return err
		}

		if _, err := appFs.Stat(sessionToDirectory); os.IsNotExist(err) {
			logrus.Infof("%v doesn't exists, is the USB mounted?", sessionToDirectory)
			return err
		}

		filesToUpload, err := ioutil.ReadDir(sessionFromDirectory)
		if err != nil {
			return err
		}

		sortedUploadList, err := getSortedFilesForUpload(filesToUpload, sessionFromDirectory, sessionToDirectory)
		if err != nil {
			return err
		}

		for {
			if len(sortedUploadList) == 0 {
				break
			}
			select {
			case <-s.stopCtx.Done():
				s.dataUploadState.SetUploading(false)
				s.dataUploadState.SetCompleted(true)
				return nil
			case <-s.sessionStopChan:
				s.dataUploadState.SetUploading(false)
				s.dataUploadState.SetCompleted(true)
				return nil
			default:
				if uint64(s.dataUploadState.ImagesUploaded()) > s.emergencyUploadTarget.GetUIntValue() {
					s.dataUploadState.SetUploading(false)
					s.dataUploadState.SetCompleted(true)
					return nil
				}
				if s.dataUploadState.IsUploading() {
					if err := appFs.MkdirAll(sessionToDirectory, 0777); err != nil {
						return err
					}
					item := sortedUploadList[0]
					file := item.File
					err := transfer(sessionFromDirectory, sessionToDirectory, file, true, s.useOfflineFlow, s.hardwareManagerClient, s.offlineAgentMaxLoad)
					if err != nil {
						logrus.Errorf("Error transferring file %v", err)
						s.dataUploadState.SetUploading(false)
						s.dataUploadState.SetCompleted(false)
					} else {
						sortedUploadList = sortedUploadList[1:]
						s.dataUploadState.AppendFile(file)
					}
				}
			}
		}

		s.dataUploadState.SetUploading(false)
		s.dataUploadState.SetCompleted(true)

		return nil
	} else if s.dataUploadState.Method() == state.Wireless {
		filesInFromDirectory, err := afero.ReadDir(appFs, sessionFromDirectory)
		if err != nil {
			return err
		}

		filesToTransfer, err := getSortedFilesForUpload(filesInFromDirectory, sessionFromDirectory, "")
		if err != nil {
			return err
		}

		var timestampedFiles []TimestampedFile

		for {
			if len(filesToTransfer) == 0 {
				break
			}
			select {
			case <-s.stopCtx.Done():
				s.dataUploadState.SetUploading(false)
				s.dataUploadState.SetCompleted(true)
				return nil
			case <-s.sessionStopChan:
				s.dataUploadState.SetUploading(false)
				s.dataUploadState.SetCompleted(true)
				return nil
			default:
				if uint64(s.dataUploadState.ImagesUploaded()) >= s.emergencyUploadTarget.GetUIntValue() {
					s.dataUploadState.SetUploading(false)
					s.dataUploadState.SetCompleted(true)
					return nil
				}
				if s.dataUploadState.IsUploading() {
					item := filesToTransfer[0]
					file := item.File

					// Try uploading image three times
					for i := 0; i < 3; i++ {
						err := upload(s.robotClient, file, false, timestampedFiles, sessionFromDirectory, "", UploadPriorityEmergency, getUploadedTimesLog())
						if err != nil {
							logrus.Errorf("Failed uploading %v, attempt %v. Couldn't upload image %v", file, i+1, err)
						} else {
							s.dataUploadState.AppendFile(file)
							break
						}
					}
					filesToTransfer = filesToTransfer[1:]
				}
			}
		}
	}

	s.dataUploadState.SetUploading(false)
	s.dataUploadState.SetCompleted(true)
	uploadSessionsCompletedCounterVec.WithLabelValues(emergency).Inc()
	return nil
}

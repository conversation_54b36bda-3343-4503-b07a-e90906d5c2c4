package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/png"
	"io/ioutil"
	"math"
	"math/rand"
	"os"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
)

const to_upload_dir string = "data_upload_manager/predict-images/to-upload"
const metadataExtension string = "metadata.json"
const WeedTracking string = "weed_tracking"
const Recency string = "recency"
const WeedMarginMax string = "weed_margin_max"
const CropMarginMax string = "crop_margin_max"
const AmbiguousWeedCount string = "ambiguous_weed_count"
const AmbiguousCropCount string = "ambiguous_crop_count"
const Driptape string = "driptape"
const UnknownPlant string = "unknown_plant"

type ScorePair struct {
	score float64
	pcID  uint32
}

type IndexPair struct {
	rowID int
	pcID  uint32
}

func getToUploadDir() string {
	return fmt.Sprintf("%v/%v", getDataDir(), to_upload_dir)
}

func removeOldFiles(time_hours time.Duration) error {
	upload_dir := getToUploadDir()
	files, err := ioutil.ReadDir(upload_dir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if time.Now().Sub(file.ModTime()) > time_hours*time.Hour {
			filepath := fmt.Sprintf("%v/%v", upload_dir, file.Name())
			os.Remove(filepath)
		}
	}
	return nil
}

func addItemsToQueue(uploadQueues map[string]*state.UploadQueue, minScores map[string]*config.ConfigTree) error {
	upload_dir := getToUploadDir()
	files, err := ioutil.ReadDir(upload_dir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if strings.HasSuffix(file.Name(), metadataExtension) {
			data, err := ioutil.ReadFile(fmt.Sprintf("%v/%v", upload_dir, file.Name()))
			if err != nil {
				logrus.Infof("Couldn't read file %v with error %v", file.Name(), err)
				continue
			}

			var metadata PredictImageMetadata

			err = json.Unmarshal(data, &metadata)
			if err != nil {
				logrus.Infof("Couldn't unmarshal data in file %v with error %v", file.Name(), err)
				continue
			}

			for _, score := range metadata.Reason.InterestScores {
				if _, ok := minScores[score.Type]; ok {
					if _, ok := uploadQueues[score.Type]; ok { // If we're here, this should always be true, but we'll double check for safety
						if score.Value > minScores[score.Type].GetFloatValue() {
							common_filename := strings.TrimSuffix(file.Name(), fmt.Sprintf(".%v", metadataExtension))
							uploadQueues[score.Type].Push(score.Value, common_filename)
						}
					}
				}
			}
		}
	}
	return nil
}

func getInd(scores map[int]ScorePair, minScore float64) IndexPair {
	var inds []IndexPair
	ind := IndexPair{rowID: -1, pcID: 0}
	maxScore := 0.0

	for _, scorePair := range scores {
		if scorePair.score > maxScore && scorePair.score > minScore {
			maxScore = scorePair.score
		}
	}

	if maxScore > 0 {
		for rowIdx, scorePair := range scores {
			if math.Abs(scorePair.score-maxScore) < 1e-5 {
				inds = append(inds, IndexPair{rowID: rowIdx, pcID: scorePair.pcID})
			}
		}
	}

	if len(inds) > 0 {
		randomInd := rand.Intn(len(inds))
		ind = inds[randomInd]
	}

	return ind
}

type grabPushInput struct {
	uploadQueue     *state.UploadQueue
	rowClients      map[int]*rows.RowClients
	scoreType       string
	minScore        float64
	robotName       string
	crop            string
	cropID          string
	admin           int
	predictAperture string
	softwareVersion string
	redis           *redis.Client
}

func grabAndPush(input grabPushInput) error {
	scores, valid_score := getMaxScores(input.rowClients, input.scoreType)

	if !valid_score {
		return errors.New("No valid scores")
	}

	indexPair := getInd(scores, input.minScore)

	if indexPair.rowID >= 0 && indexPair.pcID > 0 {
		cvClient := input.rowClients[indexPair.rowID].CVRuntimeClients[uint32(indexPair.pcID)]
		client, err := cvClient.GetRawClient()
		if err != nil {
			return err
		}
		max_image, err_get_max := getMaxImage(client, input.scoreType)
		if err_get_max != nil {
			return err_get_max
		}

		job_id, job_name := getCurrentJobIdAndName(input.redis)
		filepath, error_writing := writeImageAndMetadata(max_image, indexPair.rowID, input.scoreType, input.robotName, getToUploadDir(), input.crop, input.cropID, input.admin, UploadPriorityNormal, "predict", "", input.predictAperture, input.softwareVersion, job_id, job_name, input.redis, false)

		if error_writing == nil {
			input.uploadQueue.Push(max_image.Score, filepath)
		} else {
			return error_writing
		}
	}

	return nil
}

func getCurrentJobIdAndName(redis *redis.Client) (job_id, name string) {
	active, err := redis.ReadActiveJobId()
	if err == nil && active != "" {
		job, err := redis.ReadJob(active)
		if err == nil {
			return job.JobDescription.JobId, job.JobDescription.Name
		}
	}
	return "", ""
}

func RunRetrieve(stopCtx context.Context, uploadQueues map[string]*state.UploadQueue, retrievalIntervalConfigNode *config.ConfigTree, minScores map[string]*config.ConfigTree, imageExpirationConfigNode *config.ConfigTree, robotName string, rowClients map[int]*rows.RowClients, crop, currentCropIDNode *config.ConfigTree, admin *config.ConfigTree, predictAperture *config.ConfigTree, softwareVersion string, redis *redis.Client) error {
	logrus.Infof("Running Retrieve...")

	upload_dir := getToUploadDir()

	if err := os.MkdirAll(upload_dir, 0777); err != nil {
		return err
	}

	errRemove := removeOldFiles(time.Duration(imageExpirationConfigNode.GetIntValue()))
	if errRemove != nil {
		logrus.Warningf("Couldn't remove files from %v, %v", getToUploadDir(), errRemove)
	}

	// Add all items in to-upload directory with min score
	errAdd := addItemsToQueue(uploadQueues, minScores)

	if errAdd != nil {
		logrus.Warningf("Couldn't add files to %v, %v", getToUploadDir(), errAdd)
	} else {
		logrus.Infof("Added %v items to weed_tracking upload queue", uploadQueues[WeedTracking].Len())
		logrus.Infof("Added %v items to recency upload queue", uploadQueues[Recency].Len())
		logrus.Infof("Added %v items to weed_margin_max upload queue", uploadQueues[WeedMarginMax].Len())
		logrus.Infof("Added %v items to crop_margin_max upload queue", uploadQueues[CropMarginMax].Len())
		logrus.Infof("Added %v items to ambiguous_weed_count upload queue", uploadQueues[AmbiguousWeedCount].Len())
		logrus.Infof("Added %v items to ambiguous_crop_count upload queue", uploadQueues[AmbiguousCropCount].Len())
		logrus.Infof("Added %v items to driptape upload queue", uploadQueues[Driptape].Len())
	}

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(retrievalIntervalConfigNode.GetIntValue()) * time.Second):
		}

		logrus.Infof("Retrieving images")

		for queueName, queue := range uploadQueues {
			input := grabPushInput{
				uploadQueue:     queue,
				rowClients:      rowClients,
				scoreType:       queueName,
				minScore:        minScores[queueName].GetFloatValue(),
				robotName:       robotName,
				crop:            crop.GetStringValue(),
				cropID:          currentCropIDNode.GetStringValue(),
				admin:           int(admin.GetIntValue()),
				predictAperture: predictAperture.GetStringValue(),
				softwareVersion: softwareVersion,
				redis:           redis,
			}
			if err := grabAndPush(input); err != nil {
				logrus.Errorf("Error grabbing and pushing to %v: %v", queueName, err)
			}
		}
	}
}

func getCommonFilename(robotId string, rowId int, camId string, isoFormattedTime string, captureMethod string) string {
	if captureMethod != "" {
		return fmt.Sprintf("%v_%v_row%v_%v_%v", captureMethod, robotId, rowId, camId, isoFormattedTime)
	}
	return fmt.Sprintf("%v_row%v_%v_%v", robotId, rowId, camId, isoFormattedTime)
}

func getImageMetadata(
	artifactType string,
	maxImage *pb.ImageAndMetadataResponse,
	cropID,
	robotName string,
	rowId int,
	priority,
	imageFilename,
	md5 string,
	admin int,
	predictAperture,
	softwareVersion,
	jobId,
	jobName string,
) ImageMetadata {
	return ImageMetadata{
		ArtifactType: artifactType,
		Crop:         "",
		CropID:       cropID,
		RobotId:      robotName,
		RowId:        fmt.Sprintf("row%v", rowId),
		CamId:        maxImage.GetCamId(),
		ImageType:    maxImage.GetImageType(),
		TimestampMs:  maxImage.GetTimestampMs(),
		Ppi:          maxImage.GetPpi(),
		Width:        int(maxImage.GetWidth()),
		Height:       int(maxImage.GetHeight()),
		Geo: Geo{
			Lla: LLA{
				Lat:         maxImage.GetLlaLat(),
				Lng:         maxImage.GetLlaLng(),
				Alt:         maxImage.GetLlaAlt(),
				TimestampMs: maxImage.GetLlaTimestampMs(),
			},
			Ecef: ECEF{
				X:           maxImage.GetEcefX(),
				Y:           maxImage.GetEcefY(),
				Z:           maxImage.GetEcefZ(),
				TimestampMs: maxImage.GetEcefTimestampMs(),
			},
		},
		Admin:    admin,
		Priority: priority,
		Files: []File{
			{
				Name: imageFilename,
				MD5:  md5,
			},
		},
		FocusMetric:        float32(maxImage.GetFocusMetric()),
		ExposureUs:         float32(maxImage.GetExposureUs()),
		GainDb:             float32(maxImage.GetGainDb()),
		Aperture:           predictAperture,
		SoftwareVersion:    softwareVersion,
		JobId:              jobId,
		JobName:            jobName,
		WeedingEnabled:     maxImage.GetWeedingEnabled(),
		ThinningEnabled:    maxImage.GetThinningEnabled(),
		DeepweedId:         maxImage.GetDeepweedId(),
		P2PId:              maxImage.GetP2PId(),
		SimulatorGenerated: maxImage.GetSimulatorGenerated(),
	}
}

func writeImage(maxImage *pb.ImageAndMetadataResponse, commonFilename, directory string) (string, string, error) {
	// If filename doesn't exist, create image and metadata
	rgba := image.NewRGBA(image.Rect(0, 0, int(maxImage.GetWidth()), int(maxImage.GetHeight())))
	rgba.Pix = maxImage.GetBytes()
	rgba.Stride = int(maxImage.GetWidth()) * 4

	imageFilename := fmt.Sprintf("%v.png", commonFilename)
	imageFilepath := fmt.Sprintf("%v/%v", directory, imageFilename)
	if err := writePNGImage(imageFilepath, rgba); err != nil {
		return "", "", err
	}
	md5, err := fileMD5sum(imageFilepath)
	if err != nil {
		return "", "", fmt.Errorf("failed to calculate md5sum for %s error %w", imageFilepath, err)
	}

	return imageFilename, md5, nil
}

func writeImageAndMetadata(
	maxImage *pb.ImageAndMetadataResponse,
	rowId int,
	scoreType,
	robotName,
	directory,
	crop,
	cropID string,
	admin int,
	priority,
	captureMethod,
	sessionName,
	predictAperture,
	softwareVersion,
	jobId,
	jobName string,
	redis *redis.Client,
	isEmergency bool,
) (string, error) {
	// If neither crop nor crop_id is selected, don't grab image. This will prevent any non-set crop images from being uploaded
	if crop == "" && cropID == "" {
		return "", errors.New("Both crop & crop_id are blank, must be set for capture.")
	}

	if len(cropID) > 0 {
		crop = ""
	}

	// If gps coordinates aren't legit, don't save image
	if maxImage.GetEcefTimestampMs() == 0 || maxImage.GetLlaTimestampMs() == 0 {
		return "", errors.New("GPS coordinate for image isn't legitimate. Skipping save.")
	}

	commonFilename := getCommonFilename(robotName, rowId, maxImage.GetCamId(), maxImage.GetIsoFormattedTime(), captureMethod)

	metadataFilename := fmt.Sprintf("%v.%v", commonFilename, metadataExtension)
	metadataFilepath := fmt.Sprintf("%v/%v", directory, metadataFilename)

	if _, e := os.Stat(metadataFilepath); !os.IsNotExist(e) {
		// If metadata exists and there's a score type, just update the metadata file with score

		if scoreType == "" {
			return commonFilename, nil
		}
		data, err := ioutil.ReadFile(metadataFilepath)
		if err != nil {
			return "", fmt.Errorf("failed to read file %v with error %w", metadataFilepath, err)
		}

		var metadata PredictImageMetadata
		err = json.Unmarshal(data, &metadata)
		if err != nil {
			return "", fmt.Errorf("failed to unmarshal data in file %v with error %w", metadataFilepath, err)
		}

		score := Score{
			Type:     scoreType,
			Value:    maxImage.GetScore(),
			ModelUrl: maxImage.GetModelUrl(),
		}

		metadata.Reason.InterestScores = append(metadata.Reason.InterestScores, score)

		metadataBytes, err := json.MarshalIndent(metadata, "", "	")
		if err != nil {
			return "", fmt.Errorf("failed to marshal metadata %v with error %w", metadata, err)
		}

		err = ioutil.WriteFile(metadataFilepath, metadataBytes, 0777)
		if err != nil {
			return "", fmt.Errorf("failed to write bytes with error %w", err)
		}

		return commonFilename, nil
	}

	var scores []Score
	if scoreType != "" {
		score := Score{
			Type:     scoreType,
			Value:    maxImage.GetScore(),
			ModelUrl: maxImage.GetModelUrl(),
		}
		scores = append(scores, score)
	}

	imageFilename, md5, err := writeImage(maxImage, commonFilename, directory)
	if err != nil {
		return "", fmt.Errorf("Failed to write image: %v", err)
	}

	var weedHeightProfile []float64
	var cropHeightProfile []float64
	var bbhWeedHeightProfile []float64
	var bbhCropHeightProfile []float64

	for _, height := range maxImage.GetWeedHeightColumns() {
		weedHeightProfile = append(weedHeightProfile, height)
		bbhWeedHeightProfile = append(bbhWeedHeightProfile, height+maxImage.GetBbhOffsetMm())
	}

	for _, height := range maxImage.GetCropHeightColumns() {
		cropHeightProfile = append(cropHeightProfile, height)
		bbhCropHeightProfile = append(bbhCropHeightProfile, height+maxImage.GetBbhOffsetMm())
	}

	almanac := loadAlmanac(redis)
	modelinator := loadModelinator(redis, maxImage.GetDeepweedId(), cropID)
	metadata := PredictImageMetadata{
		ImageMetadata: getImageMetadata(
			"predict_image",
			maxImage,
			cropID,
			robotName,
			rowId,
			priority,
			imageFilename,
			md5,
			admin,
			predictAperture,
			softwareVersion,
			jobId,
			jobName,
		),
		Reason: Reason{
			Version:        "1",
			InterestScores: scores,
		},
		SessionName:           sessionName,
		WeedHeightProfile:     weedHeightProfile,
		CropHeightProfile:     cropHeightProfile,
		BbhWeedHeightProfile:  bbhWeedHeightProfile,
		BbhCropHeightProfile:  bbhCropHeightProfile,
		WeedPointThreshold:    maxImage.GetWeedPointThreshold(),
		CropPointThreshold:    maxImage.GetCropPointThreshold(),
		SegmentationThreshold: maxImage.GetSegmentationThreshold(),
		DeepweedDetections:    maxImage.GetDeepweedDetections(),
		Almanac:               almanac,
		Modelinator:           modelinator,
		IsEmergencyCapture:    isEmergency,
	}

	if err := writeMeta(metadataFilepath, metadata); err != nil {
		return "", err
	}

	return commonFilename, nil
}

func loadAlmanac(redis *redis.Client) *almanac.AlmanacConfig {
	activeId, err := redis.ReadActiveAlmanacId()
	if err != nil {
		logrus.Warn("Could not read active almanac id")
		return nil
	}
	almanac, err := redis.LoadAlmanacConfig(activeId)
	if err != nil {
		logrus.WithError(err).Warnf("Could not load almanac config %v", activeId)
		return nil
	}
	return almanac
}

func loadModelinator(redis *redis.Client, modelId string, cropId string) *almanac.ModelinatorConfig {
	id := lib.BuildModelinatorId(modelId, cropId)
	modelinator, err := redis.FetchModelinatorConfigById(id)
	if err != nil {
		logrus.WithError(err).Warnf("Could not load modelinator config %v", id)
		return nil
	}
	return modelinator
}

func writeMeta(dest string, meta interface{}) error {
	switch typ := meta.(type) {
	case P2PImagePairMetadata, PredictImageMetadata, LightweightBurstRecordMetadata, PredictBurstMetadata, ChipImageMetadata:
	default:
		return fmt.Errorf("unsupported metadata type: %s", typ)
	}

	metaBytes, err := json.MarshalIndent(meta, "", "	")
	if err != nil {
		return fmt.Errorf("failed to marshal metadata %v with error %w", meta, err)
	}

	if err = ioutil.WriteFile(dest, metaBytes, 0777); err != nil {
		return fmt.Errorf("failed to write metadata file %s with error %w", dest, err)
	}

	return nil
}

func writePNGImage(dest string, rgba *image.RGBA) error {
	f, err := os.Create(dest)
	if err != nil {
		return fmt.Errorf("failed to create image_filepath %v with error %w", dest, err)
	}
	defer f.Close()

	if err := png.Encode(f, rgba); err != nil {
		return fmt.Errorf("failed to encode image with error %w", err)
	}

	return nil
}

func getMaxScores(rowClients map[int]*rows.RowClients, scoreType string) (map[int]ScorePair, bool) {
	scores := make(map[int]ScorePair)
	valid_score := false

	for rowID, rowClient := range rowClients {
		scores[rowID] = ScorePair{
			score: -1,
			pcID:  0,
		}
		for pcID, cvClient := range rowClient.CVRuntimeClients {
			client, err := cvClient.GetRawClient()
			if err != nil {
				logrus.Errorf("Failed to get raw client: %v", err)
				continue
			}

			score := getMaxScore(client, scoreType)

			if score > scores[rowID].score {
				scores[rowID] = ScorePair{
					score: score,
					pcID:  pcID,
				}
			}
			if score >= 0 {
				valid_score = true
			}
		}
	}

	return scores, valid_score
}

func getMaxScore(client pb.CVRuntimeServiceClient, scoreType string) float64 {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	score, err := client.GetMaxImageScore(ctx, &pb.GetMaxImageScoreRequest{ScoreType: scoreType})

	if err != nil {
		logrus.Infof("%T.GetMaxImageScore(_) = _, %v: ", client, err)
		return -1
	}

	return score.GetScore()
}

func getMaxImage(client pb.CVRuntimeServiceClient, scoreType string) (*pb.ImageAndMetadataResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	image, err := client.GetMaxScoredImage(ctx, &pb.GetMaxScoredImageRequest{ScoreType: scoreType})

	return image, err
}

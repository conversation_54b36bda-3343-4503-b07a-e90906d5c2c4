package services

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

const (
	subsystem = "dum"
	emergency = "emergency"
)

var (
	imageUploadTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: subsystem,
			Name:      "image_upload_total",
			Help:      "total number of images uploaded.",
		}, []string{"priority"})
	imageSkippedUploadTotalVec = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: subsystem,
			Name:      "image_skipped_upload_total",
			Help:      "total number of images that skipped uploaded.",
		}, []string{"code"})
	metaUploadTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: subsystem,
			Name:      "meta_upload_total",
			Help:      "total number of metadata files uploaded.",
		}, []string{"priority"})
	imageUploadErrorTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: subsystem,
			Name:      "image_upload_error_total",
			Help:      "total number of errors encountered when uploading.",
		}, []string{"priority"})
	imageUploadTimeHist = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Subsystem: subsystem,
			Name:      "image_upload_duration_seconds",
			Help:      "image upload duration histogram.",
			Buckets:   []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10}, // TODO:(smt) not sure if we care about all these
		})
	metaUploadTimeHist = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Subsystem: subsystem,
			Name:      "meta_upload_duration_seconds",
			Help:      "meta upload duration histogram.",
			Buckets:   []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10}, // TODO:(smt) not sure if we care about all these
		})
	uploadSessionsCompletedCounterVec = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem:   subsystem,
		Name:        "upload_sessions_completed_total",
		Help:        "Total number of completed upload sessions.",
		ConstLabels: nil,
	}, []string{"type"})
	uploadSessionsFailedCounterVec = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem:   subsystem,
		Name:        "upload_sessions_failed_total",
		Help:        "Total number of failed upload sessions.",
		ConstLabels: nil,
	}, []string{"type"})
)

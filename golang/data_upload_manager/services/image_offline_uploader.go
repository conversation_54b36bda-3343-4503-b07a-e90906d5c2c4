package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"io/ioutil"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
)

type UploadObject struct {
	File        string
	TimestampMs int64
}

type UploadObjectList []UploadObject

func (u UploadObjectList) Len() int           { return len(u) }
func (u UploadObjectList) Swap(i, j int)      { u[i], u[j] = u[j], u[i] }
func (u UploadObjectList) Less(i, j int) bool { return u[i].TimestampMs > u[j].TimestampMs }

func getMediaDir() string {
	media_dir := os.Getenv("MAKA_MEDIA_DIR")
	if len(media_dir) == 0 {
		return "/media"
	}
	return media_dir
}

func getUSBDir() string {
	return fmt.Sprintf("%v/usb-device", getMediaDir())
}

func getCompletedTransferDir(robotName string) string {
	return fmt.Sprintf("%v/%v/regular-captures", getUSBDir(), robotName)
}

func getSnapshotTransferDir(robotName string) string {
	return fmt.Sprintf("%v/%v/snapshots", getUSBDir(), robotName)
}

func getSortedFilesForUpload(files_to_upload []fs.FileInfo, fromDir string, toDir string) ([]UploadObject, error) {
	metadataExtensionWithPeriod := fmt.Sprintf(".%v", metadataExtension)

	filesToTransfer := make(map[string]int64)

	for _, file := range files_to_upload {
		if strings.HasSuffix(file.Name(), metadataExtensionWithPeriod) {
			data, err := ioutil.ReadFile(fmt.Sprintf("%v/%v", fromDir, file.Name()))
			if err != nil {
				logrus.Infof("Couldn't read file %v with error %v", file.Name(), err)
				continue
			}
			var metadata PredictImageMetadata

			err = json.Unmarshal(data, &metadata)
			if err != nil {
				logrus.Infof("Couldn't unmarshal data in file %v with error %v", file.Name(), err)
				continue
			}

			filesToTransfer[strings.TrimSuffix(file.Name(), metadataExtensionWithPeriod)] = metadata.TimestampMs
		}
	}

	if toDir != "" {
		files_completed_transfer, err := ioutil.ReadDir(toDir)
		if err != nil {
			return make([]UploadObject, 0), err
		}

		for _, file := range files_completed_transfer {
			if strings.HasSuffix(file.Name(), metadataExtensionWithPeriod) {
				delete(filesToTransfer, strings.TrimSuffix(file.Name(), metadataExtensionWithPeriod))
			}
		}
	}

	uploadList := make(UploadObjectList, len(filesToTransfer))
	i := 0
	for k, v := range filesToTransfer {
		uploadList[i] = UploadObject{k, v}
		i++
	}

	sort.Sort(uploadList)

	return uploadList, nil
}

func transferImages(fromDir string, toDir string, maxImages int, dataUploadState *state.DataUploadState, deleteAfterCopy bool, useOfflineFlow *config.ConfigTree, hardwareManagerClient *hardware_manager.HardwareManagerClient, offlineAgentMaxLoad *config.ConfigTree) error {
	var numberTransfered = 0
	if _, err := os.Stat(toDir); os.IsNotExist(err) {
		logrus.Infof("%v doesn't exists, is the USB mounted?", toDir)
		return nil
	}

	files_to_upload, err := ioutil.ReadDir(fromDir)
	if err != nil {
		return err
	}

	sortedUploadList, err := getSortedFilesForUpload(files_to_upload, fromDir, toDir)
	if err != nil {
		return err
	}

	for _, item := range sortedUploadList {
		file := item.File
		err := transfer(fromDir, toDir, file, deleteAfterCopy, useOfflineFlow, hardwareManagerClient, offlineAgentMaxLoad)
		if err != nil {
			logrus.Errorf("Error transferring file %v", err)
			return err
		} else {
			if dataUploadState != nil {
				dataUploadState.AppendFile(file)
			}
			numberTransfered += 1
		}
		if maxImages > 0 && numberTransfered > maxImages {
			break
		}
	}

	return nil
}

func RunTransfer(stopCtx context.Context, transfer_interval_config_node *config.ConfigTree, image_expiration_config_node *config.ConfigTree, use_offline_flow *config.ConfigTree, configClient *config.ConfigClient, robotName string, hardwareManagerClient *hardware_manager.HardwareManagerClient, offlineAgentMaxLoad *config.ConfigTree) error {
	logrus.Infof("Running Transfer...")

	transfer_dir := getCompletedTransferDir(robotName)

	if err := os.MkdirAll(getSnapDirectory(), 0777); err != nil {
		return errors.New(fmt.Sprintf("Couldn't make directory for snaps: %v", err))
	}

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(transfer_interval_config_node.GetIntValue()) * time.Second):
		}

		// If the USB device is mounted but the transfer directory doesn't exist, create it
		if !use_offline_flow.GetBoolValue() {
			continue
		}

		if _, err_os := os.Stat(getUSBDir()); !os.IsNotExist(err_os) {
			if err := os.MkdirAll(transfer_dir, 0777); err != nil {
				logrus.Errorf("Error making transfer directory %v", err)
				continue
			}

			if err := os.MkdirAll(getSnapshotTransferDir(robotName), 0777); err != nil {
				logrus.Errorf("Couldn't make directory for snaps on usb: %v", err)
				continue
			}
		}

		err := transferImages(getToUploadDir(), transfer_dir, -1, nil, false, use_offline_flow, hardwareManagerClient, offlineAgentMaxLoad)
		if err != nil {
			logrus.Errorf("Error transferring %v", err)
			continue
		}
		errSnapshots := transferImages(getSnapDirectory(), getSnapshotTransferDir(robotName), -1, nil, true, use_offline_flow, hardwareManagerClient, offlineAgentMaxLoad)
		if errSnapshots != nil {
			logrus.Errorf("Error transferring %v", errSnapshots)
			continue
		}
	}
}

func transfer(fromDir string, toDir string, filename string, deleteAfterCopy bool, useOfflineFlow *config.ConfigTree, hardwareManagerClient *hardware_manager.HardwareManagerClient, offlineAgentMaxLoad *config.ConfigTree) error {
	if !useOfflineFlow.GetBoolValue() {
		return errors.New("USB device not detected for image transfer")
	}

	used, success, _, err := hardwareManagerClient.GetAvailableUSBStorage()

	if err != nil {
		return err
	}

	if success && used >= float32(offlineAgentMaxLoad.GetFloatValue()) {
		return errors.New(fmt.Sprintf("USB storage %f%%, must clear items before transferring", used*100))
	}

	from_image := fmt.Sprintf("%v/%v.png", fromDir, filename)
	to_image := fmt.Sprintf("%v/%v.png", toDir, filename)

	from_meta := fmt.Sprintf("%v/%v.%v", fromDir, filename, metadataExtension)
	to_meta := fmt.Sprintf("%v/%v.%v", toDir, filename, metadataExtension)

	if err := copy(from_meta, to_meta); err != nil {
		return err
	}
	if err := copy(from_image, to_image); err != nil {
		return err
	}

	if deleteAfterCopy {
		if err := os.Remove(from_meta); err != nil {
			return err
		}
		if err := os.Remove(from_image); err != nil {
			return err
		}
	}
	return nil
}

func copy(src string, dst string) error {
	f_src, err := os.Open(src)
	defer f_src.Close()
	if err != nil {
		return err
	}

	f_dst, err := os.Create(dst)
	defer f_dst.Close()
	if err != nil {
		return err
	}

	_, err = io.Copy(f_dst, f_src)

	if err != nil {
		return err
	}

	if err := f_dst.Sync(); err != nil {
		return err
	}

	return nil
}

package services

import (
	"math"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetInd(t *testing.T) {
	scores1 := map[int]ScorePair{
		1: {0.1, 1},
		2: {0.3, 1},
		3: {0.2, 1},
	}
	scores2 := map[int]ScorePair{
		1: {0.3, 1},
		2: {0.3, 1},
		3: {0.2, 1},
	}

	t.Run("happy path", func(t *testing.T) {
		ind := getInd(scores1, 0.0)
		assert.Equal(t, ind, IndexPair{2, 1})

		ind = getInd(scores1, 0.6)
		assert.Equal(t, ind, IndexPair{-1, 0})
	})

	t.Run("two equal scores", func(t *testing.T) {
		countRow1 := 0
		countRow2 := 0
		countRow3 := 0
		countNone := 0

		for i := 1; i < 10000; i++ {
			indPair := getInd(scores2, 0.1)
			if indPair.rowID == 1 {
				countRow1 += 1
			} else if indPair.rowID == 2 {
				countRow2 += 1
			} else if indPair.rowID == 3 {
				countRow3 += 1
			} else {
				countNone += 1
			}
		}
		assert.Equal(t, countNone, 0)
		assert.Equal(t, countRow3, 0)
		percentage := float64(countRow1) / float64(countRow1+countRow2)
		assert.Equal(t, math.Abs(percentage-0.5) < 0.1, true)
	})

}

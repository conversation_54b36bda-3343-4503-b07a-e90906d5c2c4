package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
)

const uploadedChipTimesLog string = "data_upload_manager/chips/uploaded.json"
const uploadChipsDirectory string = "data_upload_manager/chips/to-upload"

const randomChip string = "random_chip"

func getChipsUploadDir() string {
	return fmt.Sprintf("%v/%v", getDataDir(), uploadChipsDirectory)
}

func getChipsUploadFilePath() string {
	return fmt.Sprintf("%v/%v", getDataDir(), uploadedChipTimesLog)
}

func writeChipImageAndMetadata(
	maxImage *pb.ImageAndMetadataResponse,
	predictionMetadata *pb.ChipPrediction,
	cropID,
	robotName string,
	rowID int,
	priority string,
	admin int,
	predictAperture,
	softwareVersion,
	jobId,
	jobName,
	captureMethod string,
) (string, error) {
	if cropID == "" {
		return "", errors.New("Crop ID is blank, must be set for capture.")
	}

	// If gps coordinates aren't legit, don't save image
	if maxImage.GetEcefTimestampMs() == 0 || maxImage.GetLlaTimestampMs() == 0 {
		return "", errors.New("GPS coordinate for image isn't legitimate. Skipping save.")
	}

	// Appending timestamp to captureMethod so that if we have multiple chips from the same image, they will have unique filenames
	captureMethod = fmt.Sprintf("%v-%v", captureMethod, time.Now().UnixNano()/int64(time.Millisecond))
	commonFilename := getCommonFilename(robotName, rowID, maxImage.GetCamId(), maxImage.GetIsoFormattedTime(), captureMethod)

	metadataFilename := fmt.Sprintf("%v.%v", commonFilename, metadataExtension)
	metadataFilepath := fmt.Sprintf("%v/%v", getChipsUploadDir(), metadataFilename)

	imageFilename, md5, err := writeImage(maxImage, commonFilename, getChipsUploadDir())
	if err != nil {
		return "", fmt.Errorf("Failed to write image: %v", err)
	}

	scores := make(map[string]float64)

	for _, scoreItem := range predictionMetadata.GetScore() {
		scores[scoreItem.GetCategory()] = scoreItem.GetScore()
	}

	embeddingDistances := make(map[string]float64)

	for _, embeddingDistanceItem := range predictionMetadata.GetEmbeddingDistance() {
		embeddingDistances[embeddingDistanceItem.GetCategory()] = embeddingDistanceItem.GetScore()
	}

	reasonScore := Score{
		Type:     maxImage.GetScoreType(),
		Value:    maxImage.GetScore(),
		ModelUrl: maxImage.GetModelUrl(),
		ModelId:  predictionMetadata.GetModelId(),
	}

	metadata := ChipImageMetadata{
		ImageMetadata: getImageMetadata(
			"chip_image",
			maxImage,
			cropID,
			robotName,
			rowID,
			priority,
			imageFilename,
			md5,
			admin,
			predictAperture,
			softwareVersion,
			jobId,
			jobName,
		),
		Prediction: PredictionMetadata{
			X:                  predictionMetadata.GetX(),
			Y:                  predictionMetadata.GetY(),
			Radius:             predictionMetadata.GetRadius(),
			ModelId:            predictionMetadata.GetModelId(),
			Scores:             scores,
			EmbeddingDistances: embeddingDistances,
			BandStatus:         predictionMetadata.GetBandStatus(),
		},
		Reason: Reason{
			Version:        "1",
			InterestScores: []Score{reasonScore},
		},
	}

	if err := writeMeta(metadataFilepath, metadata); err != nil {
		return "", err
	}

	return commonFilename, nil
}

func RunChipUpload(stopCtx context.Context, robotClient RobotClient, dumConfigNode *config.ConfigTree, uploadQueue *state.UploadQueue) error {
	logrus.Infof("Running Chip Upload...")

	expirationHours := dumConfigNode.GetNode("chip_capture/expiration_hours")
	toUploadDir := getChipsUploadDir()
	err := appFs.MkdirAll(toUploadDir, os.ModePerm)
	if err != nil {
		return err
	}
	err = CleanDir(toUploadDir, time.Duration(expirationHours.GetIntValue())*time.Hour)
	if err != nil {
		return err
	}

	duration := time.Duration(0)

	populateUploadQueue(uploadQueue)

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(int(dumConfigNode.GetNode("chip_capture/upload_interval_seconds").GetIntValue()))*time.Second - duration):
		}

		if !dumConfigNode.GetNode("use_online_upload_flow").GetBoolValue() {
			logrus.Warnf("ChipUpload: use_online_upload_flow is false")
			continue
		}

		if !dumConfigNode.GetNode("chip_capture/enabled").GetBoolValue() {
			logrus.Warnf("ChipUpload: chip_catpure/enabled is false")
			continue
		}
		start := time.Now()

		var timestamps []TimestampedFile
		data, err := ioutil.ReadFile(getChipsUploadFilePath())
		if err == nil {
			if err := json.Unmarshal(data, &timestamps); err != nil {
				logrus.Errorf("ChipUpload: could not load timestamps %v", err)
			}
		}

		var ind int = 0
		var timestampedFile TimestampedFile
		for ind, timestampedFile = range timestamps {
			if time.Now().Sub(time.UnixMilli(timestampedFile.TimestampMs)) <= time.Hour {
				break
			}
		}

		timestamps = timestamps[ind:]

		var maxUploads int = int(dumConfigNode.GetNode("chip_capture/max_uploads_per_hour").GetIntValue())

		if len(timestamps) >= maxUploads {
			logrus.Infof("ChipUpload: Reached the limit of hourly chip image uploads: Pushed %v/%v", len(timestamps), maxUploads)
			if err := SaveUploadedLogs(timestamps, uploadedChipTimesLog); err != nil {
				logrus.Warnf("ChipUpload: Couldn't save log file %v", err)
			}
			continue
		}

		filename, _, success := uploadQueue.PopMax()

		if !success {
			logrus.Warn("ChipUpload: Could not get chip filename")
			continue
		}

		if err := upload(robotClient, filename, true, timestamps, getChipsUploadDir(), "", UploadPriorityNormal, getChipsUploadFilePath()); err != nil {
			logrus.Warnf("ChipUpload: Failed to upload chip: %v", err)
			continue
		}

		end := time.Now()
		logrus.Infof("ChipUpload: Success %v in %v", filename, end.Sub(start))
		duration = end.Sub(start)
	}
}

type queueChannelItem struct {
	queueScore *pb.CategoryPrediction
	rowID      int
	ind        uint32
}

func populateUploadQueue(uploadQueue *state.UploadQueue) {
	toUploadDir := getChipsUploadDir()

	files, err := ioutil.ReadDir(toUploadDir)
	if err != nil {
		logrus.Errorf("ChipUpload: could not read directory %v", toUploadDir)
		return
	}

	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".metadata.json") {
			continue
		}

		data, err := ioutil.ReadFile(fmt.Sprintf("%v/%v", toUploadDir, file.Name()))
		if err != nil {
			logrus.Errorf("ChipUpload: could not read file %v", file.Name())
			continue
		}

		var metadata ChipImageMetadata
		if err := json.Unmarshal(data, &metadata); err != nil {
			logrus.Errorf("ChipUpload: could not unmarshal file %v", file.Name())
			continue
		}

		filename := filepath.Base(file.Name())

		suffix := ".metadata.json"
		if strings.HasSuffix(filename, suffix) {
			filename = strings.TrimSuffix(filename, suffix)
		}

		uploadQueue.Push(float64(metadata.ImageMetadata.TimestampMs), filename)
	}
}

func RunChipRetrieval(stopCtx context.Context, rowClients map[int]*rows.RowClients, dumConfigNode *config.ConfigTree, commanderConfigNode *config.ConfigTree, robotName string, softwareVersion string, redis *redis.Client, uploadQueue *state.UploadQueue) error {
	logrus.Infof("Running Chip Retrieval...")

	duration := time.Duration(0)

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(int(dumConfigNode.GetNode("chip_capture/retrieval_interval_seconds").GetIntValue()))*time.Second - duration):
		}

		if !dumConfigNode.GetNode("chip_capture/enabled").GetBoolValue() {
			logrus.Warnf("ChipRetrieval: chip_capture/enabled is false")
			continue
		}
		start := time.Now()

		var wg sync.WaitGroup
		queueInformationChannel := make(chan queueChannelItem)
		for rowID, rowClient := range rowClients {
			for ind, cvClient := range rowClient.CVRuntimeClients {
				wg.Add(1)
				go func(cvClient_ *cv_runtime_client.CVRuntimeClient, rowID_ int, ind_ uint32) {
					defer wg.Done()
					client, err := cvClient_.GetRawClient()
					if err != nil {
						logrus.Warnf("ChipRetrieval: Failed to get raw client: %v", err)
						return
					}

					ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
					defer cancel()
					var response *pb.ChipQueueInformationResponse
					response, err = client.GetChipQueueInformation(ctx, &pb.ChipQueueInformationRequest{})

					if err != nil {
						logrus.Warnf("ChipRetrieval: Failed to get chip queue information from row %v, client %v: %v", rowID, ind, err)
						return
					}

					for _, queueScore := range response.GetQueueScore() {
						queueScoreItem := queueChannelItem{
							queueScore: queueScore,
							rowID:      rowID_,
							ind:        ind_,
						}
						queueInformationChannel <- queueScoreItem
					}
				}(cvClient, rowID, ind)

			}
		}

		go func() {
			wg.Wait()
			close(queueInformationChannel)
		}()

		queueScores := make(map[string]map[string]float64)

		for queueScoreItem := range queueInformationChannel {
			queueScore := queueScoreItem.queueScore
			rowID := queueScoreItem.rowID
			ind := queueScoreItem.ind

			_, ok := queueScores[queueScore.GetCategory()]

			if !ok {
				queueScores[queueScore.GetCategory()] = make(map[string]float64)
			}

			rowInformation, _ := queueScores[queueScore.GetCategory()]

			rowInformation[fmt.Sprintf("%v::%v", rowID, ind)] = queueScore.GetScore()
		}

		if len(queueScores) > 0 {
			keys := make([]string, len(queueScores))
			i := 0
			for key := range queueScores {
				keys[i] = key
				i++
			}

			selectedKey := keys[rand.Intn(len(keys))]
			if dumConfigNode.GetNode("chip_capture/only_retrieve_random").GetBoolValue() {
				if slices.Contains(keys, randomChip) {
					selectedKey = randomChip
				}
			}

			values := queueScores[selectedKey]

			maxRow := ""
			if dumConfigNode.GetNode("chip_capture/retrieval_row_selection").GetStringValue() == "max" {
				maxScore := 0.0
				for rowID, score := range values {
					if maxRow == "" {
						maxRow = rowID
						maxScore = score
					} else if score > maxScore {
						maxRow = rowID
						maxScore = score
					}
				}
			} else {
				randomRowKeys := make([]string, len(values))
				i := 0
				for key := range values {
					randomRowKeys[i] = key
					i++
				}

				selectedRowKey := randomRowKeys[rand.Intn(len(randomRowKeys))]

				maxRow = selectedRowKey
			}

			rowInformation := strings.Split(maxRow, "::")
			rowID, err := strconv.Atoi(rowInformation[0])
			clientID, err := strconv.Atoi(rowInformation[1])

			rowClient := rowClients[rowID]
			cvClient := rowClient.CVRuntimeClients[uint32(clientID)]
			// Now query client for selected key
			client, err := cvClient.GetRawClient()
			if err != nil {
				logrus.Warnf("ChipRetrieval: Failed to get raw client: %v", err)
				continue
			}

			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()
			chipImage, err := client.GetChipImage(ctx, &pb.GetChipImageRequest{ScoreType: selectedKey})
			if err != nil {
				logrus.Warnf("ChipRetrieval: Failed to retrieve image: %v", err)
				continue
			}

			if dumConfigNode.GetNode("chip_capture/flush_queue_after_retrieval").GetBoolValue() {
				scoreQueueType := pb.ScoreQueueType_CHIP
				_, err = client.FlushQueues(ctx, &pb.FlushQueuesRequest{ScoreType: keys, ScoreQueueType: &scoreQueueType})
				if err != nil {
					logrus.Warnf("ChipRetrieval: Could not flush queue: %v", err)
				}
			}

			jobID, jobName := getCurrentJobIdAndName(redis)

			filepath, err := writeChipImageAndMetadata(
				chipImage.GetImageAndMetadata(),
				chipImage.GetPredictionMetadata(),
				commanderConfigNode.GetNode("current_crop_id").GetStringValue(),
				robotName,
				rowID,
				UploadPriorityNormal,
				int(dumConfigNode.GetNode("admin").GetIntValue()),
				dumConfigNode.GetNode("predict_camera_aperture").GetStringValue(),
				softwareVersion,
				jobID,
				jobName,
				"chip",
			)

			if err != nil {
				logrus.Warnf("ChipRetrieval: Failed to write chip image: %v", err)
				continue
			}

			chipMetadata := chipImage.GetImageAndMetadata()
			timestampMs := chipMetadata.GetTimestampMs()
			uploadQueue.Push(float64(timestampMs), filepath)

			end := time.Now()
			logrus.Infof("ChipRetrieval: Success retrieving %v with score key %v in %v", filepath, selectedKey, end.Sub(start))
		}

		duration = time.Now().Sub(start)
	}
}

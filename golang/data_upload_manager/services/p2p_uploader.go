package services

import (
	"archive/zip"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/png"
	"io/ioutil"
	"math/rand"
	"os"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"

	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
)

const uploadedP2PTimesLog string = "data_upload_manager/p2p/uploaded.json"

func RunP2PRetrieval(stopCtx context.Context, rowClients map[int]*rows.RowClients, dumConfigNode *config.ConfigTree, commanderConfigNode *config.ConfigTree, robotName string, softwareVersion string) error {
	logrus.Infof("Running P2P Retrieval...")

	ratiosNode := dumConfigNode.GetNode("p2p_capture/reason_ratios")
	defer config.PreDeleteConfigTree(ratiosNode)
	toUploadDir := fmt.Sprintf("%v/%v", getDataDir(), P2PUploadDir)
	err := appFs.MkdirAll(toUploadDir, os.ModePerm)
	if err != nil {
		return err
	}
	err = CleanDir(toUploadDir, 24*time.Hour)
	if err != nil {
		return err
	}

	randSource := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(randSource)

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(int(dumConfigNode.GetNode("p2p_capture/retrieval_interval_seconds").GetIntValue())) * time.Second):
		}

		for rowID, rowClient := range rowClients {
			for _, cvClient := range rowClient.CVRuntimeClients {
				client, err := cvClient.GetRawClient()
				if err != nil {
					logrus.Warnf("Failed to get raw client: %v", err)
					continue
				}

				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				randFloat := randGen.Float64()

				var response *pb.P2PImageAndMetadataResponse

				sum_ratios := float64(0.0)
				ratios := make(map[string]float64)
				ratioNodes := ratiosNode.GetChildrenNodes()
				for _, child := range ratioNodes {
					if _, ok := pb.P2PCaptureReason_value[child.GetName()]; ok {
						ratios[child.GetName()] = child.GetFloatValue()
						sum_ratios += child.GetFloatValue()
					}
				}
				cur_ratio := float64(0.0)
				reason := pb.P2PCaptureReason_P2PCaptureReason_MISS
				for name, val := range ratios {
					cur_ratio += val
					if randFloat < (cur_ratio / sum_ratios) {
						tmp_reason, _ := pb.P2PCaptureReason_value[name]
						reason = pb.P2PCaptureReason(tmp_reason)
						break
					}
				}

				response, err = client.GetLatestP2PImage(ctx, &pb.GetLatestP2PImageRequest{Reason: reason})
				if err != nil {
					name, _ := pb.P2PCaptureReason_name[int32(reason)]
					logrus.Infof("%T.GetLatestP2PImage(Reason=%v) = _, %v: ", client, name, err)
					continue
				}

				ecef := ECEF{X: response.GetEcefX(), Y: response.GetEcefY(), Z: response.GetEcefZ(), TimestampMs: response.GetEcefTimestampMs()}
				lla := LLA{Lat: response.GetLlaLat(), Lng: response.GetLlaLng(), Alt: response.GetLlaAlt(), TimestampMs: response.GetLlaTimestampMs()}

				geo := Geo{Lla: lla, Ecef: ecef}

				var metadata P2PImagePairMetadata
				metadata.Admin = int(dumConfigNode.GetNode("admin").GetIntValue())
				metadata.ArtifactType = "p2p"
				metadata.CamId = response.CamId
				cropID := commanderConfigNode.GetNode("current_crop_id").GetStringValue()
				if len(cropID) > 0 {
					metadata.CropID = cropID
				} else {
					metadata.Crop = commanderConfigNode.GetNode("current_crop").GetStringValue()
				}
				metadata.Geo = geo
				metadata.Height = int(response.TargetHeight)
				metadata.Width = int(response.TargetWidth)
				metadata.ImageType = "target"
				metadata.PerspectiveHeight = int(response.PerspectiveHeight)
				metadata.PerspectiveWidth = int(response.PerspectiveWidth)
				metadata.PerspectivePpi = response.PerspectivePpi
				metadata.Ppi = response.Ppi
				metadata.Priority = "normal"
				metadata.RobotId = robotName
				metadata.RowId = fmt.Sprintf("row%v", rowID)
				metadata.TimestampMs = response.TimestampMs
				metadata.Aperture = dumConfigNode.GetNode("target_camera_aperture").GetStringValue()
				metadata.SoftwareVersion = softwareVersion
				metadata.ExposureUs = float32(response.GetExposureUs())
				metadata.FocusMetric = float32(response.GetFocusMetric())
				metadata.WeedingEnabled = response.GetWeedingEnabled()
				metadata.ThinningEnabled = response.GetThinningEnabled()
				metadata.DeepweedId = response.GetDeepweedId()
				metadata.P2PId = response.GetP2PId()
				metadata.Reason = reason.String()

				commonFilename := getCommonFilename(metadata.RobotId, rowID, metadata.CamId, response.GetIsoFormattedTime(), "p2p")
				logrus.Infof("Saving p2p with base filename: %v", commonFilename)

				metadataFilename := fmt.Sprintf("%v.%v", commonFilename, metadataExtension)
				metadataFilepath := fmt.Sprintf("%v/%v", toUploadDir, metadataFilename)
				zipFilepath := fmt.Sprintf("%v/%v.zip", toUploadDir, commonFilename)

				zipInputs := make([]zipInput, 0)
				targetImageFilepath := fmt.Sprintf("%v.image.png", commonFilename)
				targetRgba := image.NewRGBA(image.Rect(0, 0, int(response.GetTargetWidth()), int(response.GetTargetHeight())))
				targetRgba.Pix = response.GetTargetBytes()
				targetRgba.Stride = int(response.GetTargetWidth()) * 4

				zipInputs = append(zipInputs, zipInput{
					filePath: targetImageFilepath,
					image:    targetRgba,
				})

				perspectiveImageFilepath := fmt.Sprintf("%v.perspective.png", commonFilename)
				perspectiveRgba := image.NewRGBA(image.Rect(0, 0, int(response.GetPerspectiveWidth()), int(response.GetPerspectiveHeight())))
				perspectiveRgba.Pix = response.GetPerspectiveBytes()
				perspectiveRgba.Stride = int(response.GetPerspectiveWidth()) * 4

				zipInputs = append(zipInputs, zipInput{
					filePath: perspectiveImageFilepath,
					image:    perspectiveRgba,
				})

				annotatedTargetImageFilepath := fmt.Sprintf("%v.image_annotated.png", commonFilename)
				annotatedTargetRgba := image.NewRGBA(image.Rect(0, 0, int(response.GetAnnotatedTargetWidth()), int(response.GetAnnotatedTargetHeight())))
				annotatedTargetRgba.Pix = response.GetAnnotatedTargetBytes()
				annotatedTargetRgba.Stride = int(response.GetAnnotatedTargetWidth()) * 4
				zipInputs = append(zipInputs, zipInput{
					filePath: annotatedTargetImageFilepath,
					image:    annotatedTargetRgba,
				})

				if err := writeZipImageBundle(zipFilepath, zipInputs); err != nil {
					return err
				}
				md5, err := fileMD5sum(zipFilepath)
				if err != nil {
					logrus.Error(fmt.Errorf("failed to calculate md5sum for %s error %w", zipFilepath, err))
					continue
				}
				metadata.Files = []File{
					{
						Name: zipFilepath,
						MD5:  md5,
					},
				}
				// Save p2p metadata file to upload directory
				if err := writeMeta(metadataFilepath, metadata); err != nil {
					logrus.Error(err)
					continue
				}
			}
		}
	}
}

type zipInput struct {
	filePath string
	image    *image.RGBA
}

func writeZipImageBundle(dest string, inputs []zipInput) error {
	f, err := appFs.Create(dest)
	if err != nil {
		return err
	}
	zr := zip.NewWriter(f)
	defer func() {
		zr.Close()
		f.Close()
	}()
	for _, zi := range inputs {
		zf, err := zr.Create(zi.filePath)
		if err != nil {
			return err
		}
		if err := png.Encode(zf, zi.image); err != nil {
			return err
		}
	}
	return nil
}

func RunP2PUpload(stopCtx context.Context, configNode *config.ConfigTree, use_online_upload_flow *config.ConfigTree, robotClient RobotClient) error {
	var elapsed time.Duration = 0
	for {
		time_to_wait := time.Duration(int(configNode.GetNode("p2p_capture/upload_interval_seconds").GetIntValue()))*time.Second - elapsed
		if time_to_wait < 0 {
			time_to_wait = 0
		}
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time_to_wait):
		}

		if !use_online_upload_flow.GetBoolValue() {
			continue
		}

		start := time.Now()

		var timestamps []TimestampedFile
		data, err := ioutil.ReadFile(getUploadedLogPath(uploadedP2PTimesLog))
		if err == nil {
			if err := json.Unmarshal(data, &timestamps); err != nil {
				logrus.Errorf("P2PUpload: could not load timestamps %v", err)
			}
		}

		var ind int = 0
		var timestamped_file TimestampedFile
		for ind, timestamped_file = range timestamps {
			if time.Now().Sub(time.UnixMilli(timestamped_file.TimestampMs)) <= 24*time.Hour {
				break
			}
		}

		timestamps = timestamps[ind:]

		var maxUploads int = int(configNode.GetNode("p2p_capture/max_uploads").GetIntValue())

		if len(timestamps) >= maxUploads {
			logrus.Infof("Reached the limit of daily p2p image uploads: Pushed %v/%v", len(timestamps), maxUploads)
			if err := SaveUploadedLogs(timestamps, uploadedP2PTimesLog); err != nil {
				logrus.Warnf("Couldn't save log file %v", err)
			}
			end := time.Now()
			elapsed = end.Sub(start)
			continue
		}

		var toUploadDir string = fmt.Sprintf("%v/%v", getDataDir(), P2PUploadDir)

		// Select filename from to upload directory
		filename, err := getFilenameFromDir(toUploadDir, "")
		if err != nil {
			error_string := fmt.Sprintf("Failed to upload p2p: %v", err)
			logrus.Warn(error_string)
			end := time.Now()
			elapsed = end.Sub(start)
			continue
		} else if len(filename) == 0 {
			end := time.Now()
			elapsed = end.Sub(start)
			continue
		}

		err = uploadP2P(robotClient, filename, toUploadDir)
		if err != nil {
			error_string := fmt.Sprintf("Failed to upload p2p: %v", err)
			logrus.Warn(error_string)
			end := time.Now()
			elapsed = end.Sub(start)
			continue
		}
		var new_object TimestampedFile
		new_object.TimestampMs = time.Now().UnixMilli()
		new_object.Filename = filename
		timestamps = append(timestamps, new_object)
		if err := SaveUploadedLogs(timestamps, uploadedP2PTimesLog); err != nil {
			logrus.Warnf("Couldn't save log file %v", err)
		}

		end := time.Now()
		elapsed = end.Sub(start)
	}
}

func uploadP2P(robotClient RobotClient, filename string, fromDirectory string) error {
	fromZip := fmt.Sprintf("%v/%v.zip", fromDirectory, filename)
	fromMeta := fmt.Sprintf("%v/%v.metadata.json", fromDirectory, filename)
	defer func() {
		for _, fil := range []string{fromZip, fromMeta} {
			if err := appFs.Remove(fil); err != nil {
				logrus.Warnf("failed to remove: %s - %v", fromZip, err)
			}
		}
	}()

	if err := postImageAndMetadata(robotClient, fromZip, fromMeta, UploadPriorityNormal); err != nil {
		logrus.Warnf("Could not post image and metadata, %v", err)
		return err
	}
	return nil
}

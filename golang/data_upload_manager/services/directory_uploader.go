package services

import (
	"fmt"
	"io"
	"os/exec"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/sirupsen/logrus"
)

type DirectoryUploader struct {
	logPrefix        string
	redisUploadsKey  string
	redisProgressKey string
	parentDir        string
	fileNamePrefix   string
}

func NewWeeedingDiagnosticsUploader() *DirectoryUploader {
	return &DirectoryUploader{
		logPrefix:        "WeedingDiagnostics",
		redisUploadsKey:  "weeding_diagnostics/uploads",
		redisProgressKey: "weeding_diagnostics/upload_progress",
		parentDir:        "/data/diagnostics",
		fileNamePrefix:   "diagnostics",
	}
}

func NewPlantCaptchaUploader() *DirectoryUploader {
	return &DirectoryUploader{
		logPrefix:        "PlantCaptcha",
		redisUploadsKey:  "plant_captcha/uploads",
		redisProgressKey: "plant_captcha/upload_progress",
		parentDir:        "/data/plant_captcha",
		fileNamePrefix:   "plant-captcha",
	}
}

func (u *DirectoryUploader) uploadDirectory(name string, robotClient RobotClient, redisClient *redis.Client, configNode *config.ConfigTree) error {
	maxRetries := configNode.GetChild("max_retries").GetUIntValue()
	maxTimeoutSec := configNode.GetChild("max_timeout_sec").GetUIntValue()
	status, err := redisClient.HReadStringSafe(u.redisUploadsKey, name, "NONE")
	if err != nil {
		return err
	}

	partNumber := 1
	if status == "DONE" {
		logrus.Infof("%v: recording %v already uploaded", u.logPrefix, name)
		return nil
	}
	if status != "NONE" {
		partNumber, err = strconv.Atoi(status)
		if err != nil {
			return err
		}
		partNumber++
	} else {
		redisClient.HSet(u.redisUploadsKey, name, "0")
	}

	diagnosticDir, err := appFs.Stat(u.parentDir + "/" + name)
	if err != nil {
		return err
	}
	diagnosticTime := diagnosticDir.ModTime()
	diagnosticTimeStr := fmt.Sprintf("%d-%02d-%02d_%02d-%02d-%02d", diagnosticTime.Year(), diagnosticTime.Month(), diagnosticTime.Day(), diagnosticTime.Hour(), diagnosticTime.Minute(), diagnosticTime.Second())

	filename := fmt.Sprintf("%v_%v_%v_%v.zip", u.fileNamePrefix, robotClient.Environment.MakaRobotName, name, diagnosticTimeStr)
	cmd := exec.Command("bash", "-c", fmt.Sprintf("cd %v; zip -r %v %v", u.parentDir, filename, name))
	out, err := cmd.Output()
	if err != nil {
		logrus.Errorf("%v: could not zip diagnostics dir %v for upload, out=%v, err=%v", u.logPrefix, name, string(out), err)
		return err
	}

	fullFilename := u.parentDir + "/" + filename
	f, err := appFs.Open(fullFilename)
	if err != nil {
		return err
	}

	fi, err := f.Stat()
	if err != nil {
		return err
	}

	fullSize := fi.Size()

	f.Seek(int64(chunkSize)*int64(partNumber-1), io.SeekStart)

	for ; ; partNumber++ {
		var end bool
		timeoutSec := 10
		for retry := 0; ; retry++ {
			end, err = chunkedImageUploadPart(robotClient, f, fullFilename, partNumber, false)
			if err == nil {
				break
			}
			if retry >= int(maxRetries) {
				redisClient.HDel(u.redisUploadsKey, name)
				logrus.WithError(err).Errorf("%v: could not upload %v part %v, no more retries left", u.logPrefix, filename, partNumber)
				f.Close()
				appFs.RemoveAll(fullFilename)
				return err
			}
			timeoutSec *= 2
			if timeoutSec > int(maxTimeoutSec) {
				timeoutSec = int(maxTimeoutSec)
			}
			logrus.WithError(err).Warnf("%v: could not upload %v part %v, will sleep for %vsec and retry (%v/%v)", u.logPrefix, filename, partNumber, timeoutSec, retry+1, maxRetries)
			time.Sleep(time.Duration(timeoutSec) * time.Second)
			f.Seek(int64(chunkSize)*int64(partNumber-1), io.SeekStart)
		}

		if end {
			redisClient.HSet(u.redisUploadsKey, name, "DONE")
			redisClient.HSet(u.redisProgressKey, name, "100")
			f.Close()
			appFs.RemoveAll(fullFilename)
			return nil
		} else {
			redisClient.HSet(u.redisUploadsKey, name, fmt.Sprintf("%v", partNumber))
			percent := uint32(100 * (float64(partNumber*int(chunkSize)) / float64(fullSize)))
			redisClient.HSet(u.redisProgressKey, name, fmt.Sprintf("%v", percent))
		}
	}
}

func (u *DirectoryUploader) ResumeUploads(robotClient RobotClient, redisClient *redis.Client, configNode *config.ConfigTree) {
	uploads, err := redisClient.HGetAll(u.redisUploadsKey)
	if err != nil {
		logrus.WithError(err).Errorf("%v: could not read uploads information from redis", u.logPrefix)
	}

	for name, state := range uploads {
		if state == "DONE" {
			continue
		}
		u.uploadDirectory(name, robotClient, redisClient, configNode)
	}
}

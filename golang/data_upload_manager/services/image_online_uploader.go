package services

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/config"
	crerr "github.com/carbonrobotics/robot/golang/lib/errors"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

const (
	completed_upload_dir string = "data_upload_manager/completed-upload"
	uploadedTimesLog     string = "data_upload_manager/predict-images/uploaded.json"

	chunkSize             uint64 = 5 << 20 // 5 MegaByte (aws requires a minimum of 5MB chunks)
	uploadCompletionToken        = "EOF"
)

var appFs = afero.NewOsFs()

type TimestampedFile struct {
	TimestampMs int64
	Filename    string
}

func getDataDir() string {
	data_dir := os.Getenv("MAKA_DATA_DIR")
	if len(data_dir) == 0 {
		return "/data"
	}
	return data_dir
}

func getCompletedUploadDir() string {
	return fmt.Sprintf("%v/%v", getDataDir(), completed_upload_dir)
}

func getUploadedTimesLog() string {
	return fmt.Sprintf("%v/%v", getDataDir(), uploadedTimesLog)
}

func saveUploadedTimesLog(timestamps []TimestampedFile, uploadedTimesLogPath string) error {
	timestamps_bytes, err := json.Marshal(timestamps)
	if err = ioutil.WriteFile(uploadedTimesLogPath, timestamps_bytes, 0777); err != nil {
		return err
	}
	return nil
}

func selectScoreType(wt_proportion, r_proportion, wmm_proportion, cmm_proportion, ambiguous_weed_proportion, ambiguous_crop_proportion, driptape_proportion, unknown_plant_proportion float64) string {
	random := rand.Float64()

	// Adjust to make true percentages in case config proportions don't sum to 1
	sum_proportion := wt_proportion + r_proportion + wmm_proportion + cmm_proportion + ambiguous_weed_proportion + ambiguous_crop_proportion + driptape_proportion + unknown_plant_proportion
	wt_percentage := wt_proportion / sum_proportion
	r_percentage := r_proportion / sum_proportion
	ambiguous_weed_percentage := ambiguous_weed_proportion / sum_proportion
	driptape_percentage := driptape_proportion / sum_proportion
	unknown_plant_percentage := unknown_plant_proportion / sum_proportion

	wmm_percentage := wmm_proportion / sum_proportion
	cmm_percentage := cmm_proportion / sum_proportion

	score_type := WeedTracking
	if random <= wt_percentage {
		score_type = WeedTracking
	} else if random <= wt_percentage+r_percentage {
		score_type = Recency
	} else if random <= wt_percentage+r_percentage+wmm_percentage {
		score_type = WeedMarginMax
	} else if random <= wt_percentage+r_percentage+wmm_percentage+cmm_percentage {
		score_type = CropMarginMax
	} else if random <= wt_percentage+r_percentage+wmm_percentage+cmm_percentage+ambiguous_weed_percentage {
		score_type = AmbiguousWeedCount
	} else if random <= wt_percentage+r_percentage+wmm_percentage+cmm_percentage+ambiguous_weed_percentage+driptape_percentage {
		score_type = Driptape
	} else if random <= wt_percentage+r_percentage+wmm_percentage+cmm_percentage+ambiguous_weed_percentage+driptape_percentage+unknown_plant_percentage {
		score_type = UnknownPlant
	} else {
		score_type = AmbiguousCropCount
	}

	return score_type
}

type UploadParams struct {
	StopCtx      context.Context
	UploadQueues map[string]*state.UploadQueue
	DumConfig    *config.ConfigTree
	RobotClient  RobotClient
}

func RunUpload(params UploadParams) error {
	logrus.Infof("Running Upload...")
	rand.New(rand.NewSource(time.Now().UnixNano()))
	completedDir := getCompletedUploadDir()

	if _, err := appFs.Stat(completedDir); !os.IsNotExist(err) {
		appFs.RemoveAll(completedDir)
	}

	if err := appFs.MkdirAll(completedDir, 0777); err != nil {
		return err
	}

	if _, err := appFs.Stat(getUploadedTimesLog()); os.IsNotExist(err) {
		var timestamps []TimestampedFile
		logBytes, errMarshal := json.Marshal(timestamps)
		if errMarshal != nil {
			return errMarshal
		}
		if errWrite := afero.WriteFile(appFs, getUploadedTimesLog(), logBytes, 0777); errWrite != nil {
			return errWrite
		}
	} else if err != nil {
		return err
	}

	var elapsed time.Duration = 0
	for {
		timeToWait := time.Duration(params.DumConfig.GetNode("upload_interval_seconds").GetIntValue())*time.Second - elapsed

		if timeToWait < 0 {
			timeToWait = 0
		}

		select {
		case <-params.StopCtx.Done():
			return nil
		case <-time.After(timeToWait):
		}

		if !params.DumConfig.GetNode("use_online_upload_flow").GetBoolValue() {
			continue
		}

		start := time.Now()

		data, err := afero.ReadFile(appFs, getUploadedTimesLog())
		if err != nil {
			return err
		}

		var timestamps []TimestampedFile
		if err := json.Unmarshal(data, &timestamps); err != nil {
			return err
		}

		for len(timestamps) > 0 && time.Now().Sub(time.UnixMilli(timestamps[0].TimestampMs)) > time.Hour {
			timestamps = timestamps[1:]
		}

		if int64(len(timestamps)) >= params.DumConfig.GetNode("max_images_per_hour").GetIntValue() {
			logrus.Warningf("Reached the limit of hourly image uploads: Pushed %v/%v", len(timestamps), params.DumConfig.GetNode("max_images_per_hour").GetIntValue())
			if err := saveUploadedTimesLog(timestamps, getUploadedTimesLog()); err != nil {
				logrus.Errorf("Couldn't save log file %v", err)
			}
			end := time.Now()
			elapsed = end.Sub(start)
			continue
		}

		uploadProportionsConfig := params.DumConfig.GetNode("upload_proportions")
		scoreType := selectScoreType(uploadProportionsConfig.GetNode("weed_tracking").GetFloatValue(), uploadProportionsConfig.GetNode("recency").GetFloatValue(), uploadProportionsConfig.GetNode("weed_margin_max").GetFloatValue(), uploadProportionsConfig.GetNode("crop_margin_max").GetFloatValue(), uploadProportionsConfig.GetNode("ambiguous_weed_count").GetFloatValue(), uploadProportionsConfig.GetNode("ambiguous_crop_count").GetFloatValue(), uploadProportionsConfig.GetNode("driptape").GetFloatValue(), uploadProportionsConfig.GetNode("unknown_plant").GetFloatValue())

		uploadQueue, ok := params.UploadQueues[scoreType]
		if !ok {
			logrus.Errorf("no queue with scoreType: %s", scoreType)
			continue
		}
		fileName, _, ok := uploadQueue.PopMax()
		if !ok {
			logrus.Errorf("failed to pop queue scoreType: %s", scoreType)
			continue
		}
		filePath := fmt.Sprintf("%v/%v.png", getToUploadDir(), fileName)

		if _, err = appFs.Stat(filePath); os.IsNotExist(err) && uploadQueue.Len() > 0 {
			// Pop until you have an item that exists: it may have been added to multiple upload queues and been previously uploaded
			fileName, _, ok = uploadQueue.PopMax()
			if !ok {
				logrus.Errorf("failed to pop queue scoreType: %s", scoreType)
				continue
			}
			filePath = fmt.Sprintf("%v/%v.png", getToUploadDir(), fileName)
		}

		if !ok {
			logrus.Warningf("File pop %v from upload queue failed", fileName)
		} else {
			if err := upload(params.RobotClient, fileName, true, timestamps, getToUploadDir(), getCompletedUploadDir(), UploadPriorityNormal, getUploadedTimesLog()); err != nil {
				logrus.Errorf("Failed upload procedure for %v", scoreType)
			}
		}
		end := time.Now()
		elapsed = end.Sub(start)
	}
}

func upload(robotClient RobotClient, filename string, countTowardsLimit bool, timestampedFiles []TimestampedFile, fromDirectory, toDirectory, uploadPriority, uploadedLogPath string) error {
	fromImage := fmt.Sprintf("%v/%v.png", fromDirectory, filename)
	fromMeta := fmt.Sprintf("%v/%v.%v", fromDirectory, filename, metadataExtension)

	if err := postImageAndMetadata(robotClient, fromImage, fromMeta, uploadPriority); err != nil {
		imageUploadErrorTotal.WithLabelValues(uploadPriority).Inc()
		logrus.Errorf("Could not post image and metadata, %v", err)
		return err
	}

	if countTowardsLimit {
		timestampedFiles = append(timestampedFiles, TimestampedFile{
			TimestampMs: time.Now().UnixMilli(),
			Filename:    filename,
		})
		if err := saveUploadedTimesLog(timestampedFiles, uploadedLogPath); err != nil {
			logrus.Errorf("Couldn't save log file %v", err)
		}
	}

	if toDirectory != "" {
		toImage := fmt.Sprintf("%v/%v.png", toDirectory, filename)
		toMeta := fmt.Sprintf("%v/%v.%v", toDirectory, filename, metadataExtension)

		// Passing a toDirectory is helpful for debugging what has been sent, but shouldn't be required
		appFs.Rename(fromImage, toImage)
		appFs.Rename(fromMeta, toMeta)
	} else {
		appFs.Remove(fromImage)
		appFs.Remove(fromMeta)
	}
	return nil
}

func postImageAndMetadata(robotClient RobotClient, imageFilename, metaFilename, uploadPriority string) error {
	switch uploadPriority {
	case UploadPriorityNormal, UploadPriorityEmergency:
	default:
		return fmt.Errorf("unsupported upload priority %s", uploadPriority)
	}

	err := metaUpload(robotClient, metaFilename)
	if err != nil {
		switch e := err.(type) {
		case *crerr.UploadError:
			// in the case where the image exists, skip uploading but don't count as error.
			if e.Code() == 409 {
				imageSkippedUploadTotalVec.WithLabelValues("409").Inc()
				return nil
			}
		default:
			return err
		}
	}
	metaUploadTotal.WithLabelValues(uploadPriority).Inc()

	if err := chunkedImageUpload(robotClient, imageFilename); err != nil {
		return err
	}
	imageUploadTotal.WithLabelValues(uploadPriority).Inc()

	return nil
}

func metaUpload(robotClient RobotClient, fileName string) error {
	start := time.Now()
	_, fName := filepath.Split(fileName)
	contents, err := afero.ReadFile(appFs, fileName)
	if err != nil {
		return err
	}
	body := &bytes.Buffer{}
	w := multipart.NewWriter(body)

	fileWriter, err := w.CreateFormFile("metadata", fName)
	if err != nil {
		return err
	}
	if _, err := fileWriter.Write(contents); err != nil {
		return err
	}
	w.Close()
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("https://%v/robot/chunked", robotClient.Environment.CarbonIngestHost), body)
	if err != nil {
		return err
	}
	req.Header.Set("User-Agent", fmt.Sprintf("dum-client-%s", robotClient.Environment.MakaRobotName))
	req.Header.Set("Content-Type", w.FormDataContentType())
	req.Header.Set("Content-MD5", md5sum(contents))
	req.Header.Set(environment.RobotHTTPHeader, robotClient.Environment.MakaRobotName)
	req.Header.Set(environment.GenerationHTTPHeader, robotClient.Environment.MakaGen)
	req.ContentLength = -1

	resp, err := robotClient.HttpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	duration := time.Since(start)
	metaUploadTimeHist.Observe(duration.Seconds())
	logrus.Infof("Request status: %v, %v, duration_ms: %v, meta %v", resp.Status, string(b), duration.Milliseconds(), fileName)

	if resp.StatusCode != http.StatusOK {
		return crerr.NewUploadError(resp.StatusCode, fmt.Sprintf("Request error %v", resp.Status))
	}

	return nil
}

func chunkedImageUploadPart(robotClient RobotClient, f afero.File, fileName string, partNumber int, reportToGrafana bool) (bool, error) {
	partStr := strconv.Itoa(partNumber)
	buf := make([]byte, chunkSize)
	bytesRead, err := f.Read(buf)
	if err != nil {
		if errors.Is(err, io.EOF) {
			partStr = uploadCompletionToken // special last part to notify for completion
		} else {
			return false, err
		}
	}
	data := buf[:bytesRead]

	body := &bytes.Buffer{}
	contentType, err := addBodyPart(body, data, fileName, partStr)
	if err != nil {
		return false, err
	}

	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("https://%v/robot/chunked", robotClient.Environment.CarbonIngestHost), body)
	if err != nil {
		return false, err
	}

	req.Header.Set("User-Agent", fmt.Sprintf("dum-client-%s", robotClient.Environment.MakaRobotName))
	req.Header.Set("Content-Type", contentType)
	req.Header.Set("Content-MD5", md5sum(data))
	req.Header.Set(environment.RobotHTTPHeader, robotClient.Environment.MakaRobotName)
	req.Header.Set(environment.GenerationHTTPHeader, robotClient.Environment.MakaGen)
	start := time.Now()
	resp, err := robotClient.HttpClient.Do(req)
	if err != nil {
		return false, err
	}
	if resp.StatusCode != http.StatusOK {
		b, _ := ioutil.ReadAll(resp.Body)
		return false, errors.New(fmt.Sprintf("Request error %v: %s", resp.Status, b))
	}
	duration := time.Since(start)
	if reportToGrafana {
		imageUploadTimeHist.Observe(duration.Seconds())
	}
	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}
	logrus.Infof("Request status: %v, %v, duration_ms: %v, file: %v (chunk: %s)", resp.Status, string(b), duration.Milliseconds(), fileName, partStr)
	if partStr == uploadCompletionToken {
		return true, nil
	}
	return false, nil
}

func chunkedImageUpload(robotClient RobotClient, fileName string) error {
	f, err := appFs.Open(fileName)
	if err != nil {
		return err
	}
	defer f.Close()

	for partNumber := 1; ; partNumber++ {
		end, err := chunkedImageUploadPart(robotClient, f, fileName, partNumber, true)
		if err != nil {
			return err
		}
		if end {
			return nil
		}
	}
}

func addBodyPart(body io.Writer, data []byte, filename string, partNumber string) (string, error) {
	w := multipart.NewWriter(body)
	defer w.Close()
	h := make(textproto.MIMEHeader)
	field := fmt.Sprintf("chunk-%s", partNumber)
	h.Set("Content-Disposition", fmt.Sprintf(`form-data; name=%q; filename=%q`, field, filename))
	part, err := w.CreatePart(h)
	if err != nil {
		return "", err
	}
	if _, err := part.Write(data); err != nil {
		return "", err
	}
	return w.FormDataContentType(), nil
}

func md5sum(data []byte) string {
	h := md5.New()
	h.Write(data)
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func fileMD5sum(filename string) (string, error) {
	f, err := appFs.Open(filename)
	if err != nil {
		return "", err
	}
	defer f.Close()
	h := md5.New()
	if _, err := io.Copy(h, f); err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(h.Sum(nil)), nil
}

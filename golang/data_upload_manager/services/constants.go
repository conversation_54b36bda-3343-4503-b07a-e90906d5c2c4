package services

import (
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
)

// Constants used by the DUM services
const (
	BurstRecordUploadDir  = "data_upload_manager/burst-records/to-upload"
	PredictImageUploadDir = "data_upload_manager/predict/to-upload"
	P2PUploadDir          = "data_upload_manager/p2p/to-upload"
	PredictBurstUploadDir = "data_upload_manager/predict-burst/to-upload"
	ChipUploadDir         = "data_upload_manager/chips/to-upload"

	UploadPriorityEmergency = "emergency"
	UploadPriorityNormal    = "normal"
)

// Metadata struct objects
type ImageMetadata struct {
	ArtifactType       string  `json:"artifact_type"`
	Crop               string  `json:"crop"`
	CropID             string  `json:"crop_id"`
	RobotId            string  `json:"robot_id"`
	RowId              string  `json:"row_id"`
	CamId              string  `json:"cam_id"`
	ImageType          string  `json:"image_type"`
	TimestampMs        int64   `json:"timestamp_ms"`
	Ppi                float32 `json:"ppi"`
	Height             int     `json:"height"`
	Width              int     `json:"width"`
	Geo                Geo     `json:"geo"`
	Admin              int     `json:"admin"`
	Priority           string  `json:"priority"`
	Files              []File  `json:"files,omitempty"`
	FocusMetric        float32 `json:"focus_metric"`
	ExposureUs         float32 `json:"exposure_us"`
	GainDb             float32 `json:"gain_db"`
	Aperture           string  `json:"aperture"`
	SoftwareVersion    string  `json:"software_version"`
	JobId              string  `json:"job_id"`
	JobName            string  `json:"job_name"`
	WeedingEnabled     bool    `json:"weeding_enabled"`
	ThinningEnabled    bool    `json:"thinning_enabled"`
	DeepweedId         string  `json:"deepweed_id"`
	P2PId              string  `json:"p2p_id"`
	SimulatorGenerated bool    `json:"simulator_generated"`
}

type File struct {
	Name string `json:"name"`
	MD5  string `json:"md5"`
}

type PredictImageMetadata struct {
	ImageMetadata
	Reason                Reason                     `json:"reason"`
	SessionName           string                     `json:"session_name"`
	WeedHeightProfile     []float64                  `json:"weed_height_profile"`
	CropHeightProfile     []float64                  `json:"crop_height_profile"`
	BbhWeedHeightProfile  []float64                  `json:"bbh_weed_height_profile"`
	BbhCropHeightProfile  []float64                  `json:"bbh_crop_height_profile"`
	WeedPointThreshold    float64                    `json:"weed_point_threshold"`
	CropPointThreshold    float64                    `json:"crop_point_threshold"`
	SegmentationThreshold float64                    `json:"segmentation_threshold"`
	DeepweedDetections    []*weed_tracking.Detection `json:"deepweed_detections"`
	Almanac               *almanac.AlmanacConfig     `json:"almanac"`
	Modelinator           *almanac.ModelinatorConfig `json:"modelinator"`
	IsEmergencyCapture    bool                       `json:"is_emergency_capture"`
}

type PredictionMetadata struct {
	X                  float64            `json:"x"`
	Y                  float64            `json:"y"`
	Radius             float64            `json:"radius"`
	PointCategoryId    string             `json:"point_category_id"`
	ModelId            string             `json:"model_id"`
	Scores             map[string]float64 `json:"scores"`
	EmbeddingDistances map[string]float64 `json:"embedding_distances"`
	BandStatus         string             `json:"band_status"`
}

type ChipImageMetadata struct {
	ImageMetadata
	Prediction PredictionMetadata `json:"prediction"`
	Reason     Reason             `json:"reason"`
}

type P2PImagePairMetadata struct {
	ImageMetadata
	Reason            string  `json:"reason"`
	PerspectivePpi    float32 `json:"perspective_ppi"`
	PerspectiveWidth  int     `json:"perspective_width"`
	PerspectiveHeight int     `json:"perspective_height"`
}

type LightweightBurstRecordMetadata struct {
	ArtifactType    string `json:"artifact_type"`
	Crop            string `json:"crop"`
	RobotId         string `json:"robot_id"`
	RowId           string `json:"row_id"`
	CameraId        string `json:"camera_id"`
	TimestampMs     string `json:"timestamp_ms"`
	Files           []File `json:"files,omitempty"`
	WeedingEnabled  bool   `json:"weeding_enabled"`
	ThinningEnabled bool   `json:"thinning_enabled"`
}

type PredictBurstMetadata struct {
	ArtifactType string `json:"artifact_type"`
	RobotId      string `json:"robot_id"`
	TimestampMs  int64  `json:"timestamp_ms"`
	CropId       string `json:"crop_id"`
	Geo          Geo    `json:"geo"`
}

// Substruct objects
type ECEF struct {
	X           float64 `json:"x"`
	Y           float64 `json:"y"`
	Z           float64 `json:"z"`
	TimestampMs int64   `json:"timestamp_ms"`
}

type LLA struct {
	Lat         float64 `json:"lat"`
	Lng         float64 `json:"lng"`
	Alt         float64 `json:"alt"`
	TimestampMs int64   `json:"timestamp_ms"`
}

type Geo struct {
	Lla  LLA  `json:"lla"`
	Ecef ECEF `json:"ecef"`
}

type Score struct {
	Type     string  `json:"type"`
	Value    float64 `json:"value"`
	ModelUrl string  `json:"model_url"`
	ModelId  string  `json:"model_id"`
}

type Reason struct {
	Version        string  `json:"version"`
	InterestScores []Score `json:"interest_scores"`
}

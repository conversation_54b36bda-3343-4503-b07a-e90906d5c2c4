package services

import (
	"archive/zip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"

	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
)

const uploadedPredictBurstsLog string = "data_upload_manager/predict-burst/uploaded.json"

func GetCameras(rowClient *rows.RowClients) (map[uint32]([]string), error) {
	camIdsForPC := make(map[uint32]([]string))
	for pc, client := range rowClient.CVRuntimeClients {
		camIds := make([]string, 0)
		cams, err := client.GetCameraInfo()
		if err != nil {
			logrus.Errorf("Predict-Burst: Failed to get camera info for pc %v: %v", pc, err)
			continue
		}
		camInfo := cams.GetCameraInfo()
		for _, cInfo := range camInfo {
			id := cInfo.GetCamId()
			if id != "" && strings.HasPrefix(id, "predict") && cInfo.GetConnected() {
				camIds = append(camIds, id)
			}
		}
		if len(camIds) != 0 {
			camIdsForPC[pc] = camIds
		}
	}

	if len(camIdsForPC) == 0 {
		return nil, errors.New("failed to get camera info for all pcs")
	}

	return camIdsForPC, nil
}

func LastNImages(cvClient *cv_runtime_client.CVRuntimeClient, camId string, n uint64) ([]*pb.ImageAndMetadataResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	var returnedItems []*pb.ImageAndMetadataResponse

	req := &pb.LastNImageRequest{
		CamId:     camId,
		NumImages: n,
	}

	client, err := cvClient.GetRawClient()
	if err != nil {
		logrus.Errorf("Predict-Burst: Could not get response: %v", err)
		return returnedItems, err
	}

	stream, err := client.GetLastNImages(ctx, req)
	if err != nil {
		logrus.Errorf("Predict-Burst: Could not get response: %v", err)
		return returnedItems, err
	}

	done := make(chan bool)

	go func() {
		for {
			resp, streamErr := stream.Recv()
			if streamErr == io.EOF {
				done <- true
				return
			}
			if streamErr != nil {
				logrus.Errorf("Predict-Burst: Could not get next chunk: %v", streamErr)
				done <- false
				return
			}
			returnedItems = append(returnedItems, resp)
		}
	}()

	if !(<-done) {
		err = errors.New("Couldn't stream image")
	}

	return returnedItems, err
}

func zipFiles(imagesDir string) error {
	files, err := ioutil.ReadDir(imagesDir)
	if err != nil {
		logrus.Errorf("Predict-Burst: Could not read directory %v: %v", imagesDir, err)
		return err
	}
	logrus.Infof("Predict-Burst: zipFiles: %v files in imagesDir %v", len(files), imagesDir)

	f, err := appFs.Create(fmt.Sprintf("%v.zip", imagesDir))
	if err != nil {
		logrus.Errorf("Predict-Burst: Could not create zip file: %v", err)
		return err
	}
	zipWriter := zip.NewWriter(f)
	defer func() {
		err := zipWriter.Close()
		if err != nil {
			logrus.Errorf("Predict-Burst: Could not close zip writer: %v", err)
		}
		err = f.Close()
		if err != nil {
			logrus.Errorf("Predict-Burst: Could not close zip file: %v", err)
		}
	}()

	for _, file := range files {
		fileBytes, err := ioutil.ReadFile(fmt.Sprintf("%v/%v", imagesDir, file.Name()))
		if err != nil {
			logrus.Errorf("Predict-Burst: Could not read file %v: %v", file.Name(), err)
			return err
		}

		f, err := zipWriter.Create(fmt.Sprintf("%v/%v", filepath.Base(imagesDir), file.Name()))
		if err != nil {
			logrus.Errorf("Predict-Burst: Could not create zip writer for file %v to zip: %v", file.Name(), err)
			return err
		}

		_, err = f.Write(fileBytes)
		if err != nil {
			logrus.Errorf("Predict-Burst: Couldn't write to file: %v", err)
			return err
		}
	}

	return nil
}

func RunPredictBurstRetrieval(stopCtx context.Context, rowClients map[int]*rows.RowClients, dumConfigNode *config.ConfigTree, redis *redis.Client, robotName string, distanceAccumulator *state.DistanceAccumulator, commanderConfigNode *config.ConfigTree, softwareVersion string) error {
	logrus.Infof("Running Predict Burst Retrieval...")

	predictBurstConfig := dumConfigNode.GetNode("predict_burst_capture")
	toUploadDir := fmt.Sprintf("%v/%v", getDataDir(), PredictBurstUploadDir)
	err := appFs.MkdirAll(toUploadDir, os.ModePerm)
	if err != nil {
		logrus.Errorf("Predict-Burst: Could not create directory %v: %v", toUploadDir, err)
		return err
	}
	err = CleanDir(toUploadDir, 24*time.Hour)
	if err != nil {
		logrus.Errorf("Predict-Burst: Could not clean directory %v: %v", toUploadDir, err)
		return err
	}

	randSource := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(randSource)

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(int(predictBurstConfig.GetNode("retrieval_interval_seconds").GetIntValue())) * time.Second):
		}

		enabled := predictBurstConfig.GetNode("enabled").GetBoolValue()
		useOnlineUploadFlow := dumConfigNode.GetNode("use_online_upload_flow").GetBoolValue()
		if !(enabled && useOnlineUploadFlow) {
			logrus.Warnf("Predict-Burst: Won't attempt predict-burst retrieval: enabled %v, useOnlineUploadFlow %v", enabled, useOnlineUploadFlow)
			continue
		}

		rowID := randGen.Intn(len(rowClients)) + 1
		rowClient := rowClients[rowID]

		camIdsForPC, err := GetCameras(rowClient)
		if err != nil {
			logrus.Errorf("Predict-Burst: Error getting camera info for row %v: %v", rowID, err)
			continue
		}

		randPC := getRandomMapKey(camIdsForPC)
		randCam := randGen.Intn(len(camIdsForPC[randPC]))
		cvClient := rowClient.CVRuntimeClients[randPC]
		camId := camIdsForPC[randPC][randCam]

		timeNow := time.Now()
		timestampFormatted := strings.Replace(time.Now().Format(time.RFC3339), ":", "-", -1)
		timestampFormattedWithoutZ, _ := strings.CutSuffix(timestampFormatted, "Z")
		timestampFormattedWithMilliseconds := fmt.Sprintf("%v-000000Z", timestampFormattedWithoutZ)
		imagesDirName := fmt.Sprintf("predict-burst-record_%v_row%v_%v_%v", robotName, rowID, camId, timestampFormattedWithMilliseconds)
		imagesDir := fmt.Sprintf("%v/%v", toUploadDir, imagesDirName)
		err = appFs.MkdirAll(imagesDir, os.ModePerm)

		gotImages := make(chan bool)
		gotDistances := make(chan bool)
		gotDetections := make(chan bool)
		geos := make(chan Geo, 1)

		go func(client_ *cv_runtime_client.CVRuntimeClient, rowID_ int, camId_ string) {
			images, err := LastNImages(client_, camId_, uint64(predictBurstConfig.GetNode("num_images").GetIntValue()))
			if err != nil {
				logrus.Infof("Predict-Burst: Failed to get images: %v", err)
				gotImages <- false
				return
			}

			jobId, jobName := getCurrentJobIdAndName(redis)
			logrus.Infof("Predict-Burst: Images retrieved %v for %v", len(images), imagesDir)
			if len(images) == 0 {
				gotImages <- false
				return
			}
			for _, image := range images {
				if len(geos) == 0 {
					ecef := ECEF{X: image.GetEcefX(), Y: image.GetEcefY(), Z: image.GetEcefZ(), TimestampMs: image.GetEcefTimestampMs()}
					lla := LLA{Lat: image.GetLlaLat(), Lng: image.GetLlaLng(), Alt: image.GetLlaAlt(), TimestampMs: image.GetLlaTimestampMs()}
					geos <- Geo{Lla: lla, Ecef: ecef}
				}
				filepath, errorWriting := writeImageAndMetadata(
					image,
					rowID_,
					"",
					robotName,
					imagesDir,
					commanderConfigNode.GetNode("current_crop").GetStringValue(),
					commanderConfigNode.GetNode("current_crop_id").GetStringValue(),
					int(dumConfigNode.GetNode("admin").GetIntValue()),
					UploadPriorityNormal,
					"predict_burst",
					"",
					dumConfigNode.GetNode("predict_camera_aperture").GetStringValue(),
					softwareVersion,
					jobId,
					jobName,
					redis,
					false,
				)

				if errorWriting != nil {
					logrus.Errorf("Predict-Burst: Error writing image %v: %v", filepath, errorWriting)
					gotImages <- false
					return
				}
			}
			gotImages <- true
		}(cvClient, rowID, camId)

		var distances []state.TimestampedDistance
		go func() {
			distances = distanceAccumulator.Get()

			distancesBytes, err := json.Marshal(distances)
			if err = ioutil.WriteFile(fmt.Sprintf("%v/distances.json", imagesDir), distancesBytes, 0777); err != nil {
				gotDistances <- false
				return
			}
			gotDistances <- true
		}()

		go func() {
			trackedItems, err := rowClient.AimbotClient.GetDistanceTrackedItems(camId)
			if err != nil {
				logrus.Infof("Predict-Burst: Failed to get tracked items: %v", err)
				gotDetections <- false
				return
			}

			detectionBytes, err := json.Marshal(trackedItems)
			if err = ioutil.WriteFile(fmt.Sprintf("%v/tracked_items.json", imagesDir), detectionBytes, 0777); err != nil {
				gotDetections <- false
				return
			}
			gotDetections <- true
		}()

		if !(<-gotImages) {
			logrus.Info("Predict-Burst: Failed to get images")
			os.RemoveAll(imagesDir)
			continue
		}

		if !(<-gotDistances) {
			logrus.Info("Predict-Burst: Failed to get distances")
			os.RemoveAll(imagesDir)
			continue
		}

		if !(<-gotDetections) {
			logrus.Info("Predict-Burst: Failed to get detections")
			os.RemoveAll(imagesDir)
			continue
		}

		err = zipFiles(imagesDir)
		if err != nil {
			logrus.Errorf("Predict-Burst: Failed zipping %v: %v", imagesDir, err)
			os.RemoveAll(imagesDir)
			continue
		}

		metadataFilepath := fmt.Sprintf("%v.metadata.json", imagesDir)

		geo := <-geos
		for len(geos) > 0 {
			<-geos
		}
		metadata := PredictBurstMetadata{
			ArtifactType: "predict_burst",
			CropId:       commanderConfigNode.GetNode("current_crop_id").GetStringValue(),
			RobotId:      robotName,
			TimestampMs:  timeNow.UnixMilli(),
			Geo:          geo,
		}

		if err := writeMeta(metadataFilepath, metadata); err != nil {
			logrus.Errorf("Predict-Burst: Could not write metadata %v: %v", metadataFilepath, err)
			os.RemoveAll(imagesDir)
			continue
		}

		if err := os.RemoveAll(imagesDir); err != nil {
			logrus.Errorf("Predict-Burst: Could not remove directory %v: %v", imagesDir, err)
			continue
		}

		logrus.Infof("Predict-Burst: Successfully retrieved predict burst: %v", imagesDir)
	}
}

func RunPredictBurstUpload(stopCtx context.Context, dumConfigNode *config.ConfigTree, robotClient RobotClient) error {
	predictBurstConfig := dumConfigNode.GetNode("predict_burst_capture")

	uploadedPath := fmt.Sprintf("%v/%v", getDataDir(), uploadedPredictBurstsLog)

	err := appFs.MkdirAll(filepath.Dir(uploadedPath), os.ModePerm)
	if err != nil {
		return err
	}

	if _, err := appFs.Stat(uploadedPath); os.IsNotExist(err) {
		var timestamps []TimestampedFile
		logBytes, errMarshal := json.Marshal(timestamps)
		if errMarshal != nil {
			logrus.Errorf("Failed to marshal timestamps: %v", errMarshal)
			return errMarshal
		}
		if errWrite := afero.WriteFile(appFs, uploadedPath, logBytes, 0777); errWrite != nil {
			logrus.Errorf("Failed to write uploadedPath %v: %v", uploadedPath, errWrite)
			return errWrite
		}
		logrus.Infof("Created file %v", uploadedPath)
	} else if err != nil {
		logrus.Errorf("Failed to stat uploadedPath %v: %v", uploadedPath, err)
		return err
	}

	logrus.Infof("Predict-Burst: Ready to start uploading")

	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(int(predictBurstConfig.GetNode("upload_interval_seconds").GetIntValue())) * time.Second):
		}

		data, err := afero.ReadFile(appFs, uploadedPath)
		if err != nil {
			logrus.Errorf("Predict-Burst: Could not read uploaded timestamps file %v: %v", uploadedPath, err)
			continue
		}

		var timestamps []TimestampedFile
		if err := json.Unmarshal(data, &timestamps); err != nil {
			logrus.Errorf("Predict-Burst: Could not unmarshal timestamps from %v: %v", uploadedPath, err)
			continue
		}

		for len(timestamps) > 0 && time.Now().Sub(time.UnixMilli(timestamps[0].TimestampMs)) > time.Hour {
			timestamps = timestamps[1:]
		}

		if int64(len(timestamps)) >= predictBurstConfig.GetNode("max_images_per_hour").GetIntValue() {
			logrus.Warningf("Reached the limit of hourly predict-burst uploads: Pushed %v/%v", len(timestamps), predictBurstConfig.GetNode("max_images_per_hour").GetIntValue())
			if err := saveUploadedTimesLog(timestamps, uploadedPath); err != nil {
				logrus.Errorf("Couldn't save log file %v", err)
			}
			continue
		}

		enabled := predictBurstConfig.GetNode("enabled").GetBoolValue()
		useOnlineUploadFlow := dumConfigNode.GetNode("use_online_upload_flow").GetBoolValue()
		if !(enabled && useOnlineUploadFlow) {
			logrus.Warnf("Predict-Burst: Won't attempt predict-burst upload: enabled %v, useOnlineUploadFlow %v", enabled, useOnlineUploadFlow)
			continue
		}

		toUploadDir := fmt.Sprintf("%v/%v", getDataDir(), PredictBurstUploadDir)

		// Select filename from to upload directory
		filename, err := getFilenameFromDir(toUploadDir, "")
		if err != nil {
			logrus.Errorf("Predict-Burst: Failed to get filename from dir %v: %v", toUploadDir, err)
			continue
		}

		// Upload burst record
		err = uploadZipMetaPair(robotClient, filename, toUploadDir, "", timestamps, uploadedPath)
		if err != nil {
			logrus.Errorf("Predict-Burst: Failed to upload predict-burst %v: %v", filename, err)
			os.Remove(fmt.Sprintf("%v/%v", toUploadDir, filename))
			continue
		}

		os.Remove(fmt.Sprintf("%v/%v", toUploadDir, filename))

		logrus.Infof("Predict-Burst: Successfully uploaded predict-burst %v", filename)
	}
}

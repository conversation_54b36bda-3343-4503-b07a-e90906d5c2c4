package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"time"

	pb "github.com/carbonrobotics/robot/golang/generated/proto/cv"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

func CleanBurstRecordUploadDir(directory string, duration time.Duration) error {
	logrus.Infof("Removing directories older than %s from %v", duration, directory)
	files, err := afero.ReadDir(appFs, directory)
	if err != nil {
		return err
	}

	for _, file := range files {
		if time.Now().Sub(file.ModTime()) > duration {
			filepath := fmt.Sprintf("%v/%v", directory, file.Name())
			logrus.Infof("Removing file or directory: %v", filepath)
			appFs.RemoveAll(filepath)
		}
	}

	return nil
}

func RunLightweightBurstRecordRetrieval(stopCtx context.Context, rowClients map[int]*rows.RowClients, configNode *config.ConfigTree) error {
	logrus.Infof("Running Lightweight Burst Record Retrieval...")

	// Get upload directory
	var toUploadDir string = fmt.Sprintf("%v/%v", getDataDir(), BurstRecordUploadDir)
	// ensure upload dir exists
	if err := appFs.MkdirAll(toUploadDir, os.ModePerm); err != nil {
		return fmt.Errorf("failed to create to upload dir %s error %w", toUploadDir, err)
	}

	err := CleanBurstRecordUploadDir(toUploadDir, 24*time.Hour)
	if err != nil {
		return err
	}

	// Get the upload rate config node based on the artifact type
	var upload_interval int = int(configNode.GetNode("upload_interval").GetIntValue())

	// TODO: Implement a check to make sure we are not over uploading burst records
	// Get the maximum uploads config node based on the artifact type
	// var max_uploads int = int(configNode.GetNode("max_uploads").GetIntValue())

	// Run for-loop and try and get the specified artifact using the specified upload rate
	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Duration(upload_interval) * time.Second):
		}

		// Loop over the cv runtime clients and request an artifact from each of them
		for _, rowClient := range rowClients {
			for _, cvClient := range rowClient.CVRuntimeClients {
				client, err := cvClient.GetRawClient()
				if err != nil {
					logrus.Errorf("Failed to get raw client: %v", err)
					continue
				}
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				// Request lightweight burst recording from cv runtime
				response, err := client.GetLightweightBurstRecord(ctx, &pb.GetLightweightBurstRecordRequest{})
				if err != nil {
					logrus.Infof("%T.GetLightweighBurstRecord(_) = _, %v: ", client, err)
					continue
				}

				var metadata LightweightBurstRecordMetadata
				if err := json.Unmarshal(response.GetMetadataFile(), &metadata); err != nil {
					logrus.Error(err)
					continue
				}

				basename := fmt.Sprintf("burst-record_%v_row%v_%v_%v", metadata.RobotId, metadata.RowId, metadata.CameraId, metadata.TimestampMs)
				logrus.Infof("Saving burst record with base filename: %v", basename)

				zipFilepath := fmt.Sprintf("%v/%v.zip", toUploadDir, basename)
				metadataFilepath := fmt.Sprintf("%v/%v.metadata.json", toUploadDir, basename)

				// Save artifact to upload directory
				err = afero.WriteFile(appFs, zipFilepath, response.GetZipFile(), 0777)
				if err != nil {
					logrus.Errorf("failed to save zip file for upload: %v error %v", zipFilepath, err)
					continue
				}

				md5, err := fileMD5sum(zipFilepath)
				if err != nil {
					logrus.Errorf("failed to calculate md5sum for %s error %v", zipFilepath, err)
					continue
				}
				metadata.Files = []File{
					{
						Name: zipFilepath,
						MD5:  md5,
					},
				}
				// Save artifact metadata file to upload directory
				if err := writeMeta(metadataFilepath, metadata); err != nil {
					logrus.Error(err)
					continue
				}
			}
		}
	}
}

func getFilenameFromDir(toUploadDir string, selection string) (string, error) {
	files, err := afero.ReadDir(appFs, toUploadDir)

	if err != nil {
		return "", nil
	}

	var metadataFiles []os.FileInfo

	for _, file := range files {
		if strings.HasSuffix(file.Name(), ".metadata.json") {
			metadataFiles = append(metadataFiles, file)
		}
	}

	if len(metadataFiles) == 0 {
		return "", errors.New("No available files for upload.")
	}

	if selection == "latest" {
		var basename string = ""
		var maxModTime time.Time
		for _, file := range files {
			if basename == "" || file.ModTime().After(maxModTime) {
				basename = strings.TrimSuffix(file.Name(), ".metadata.json")
				maxModTime = file.ModTime()
			}
		}

		return basename, nil
	} else {
		basename := metadataFiles[rand.Int()%len(metadataFiles)].Name()
		basename = strings.TrimSuffix(basename, ".metadata.json")
		return basename, nil
	}
}

func RunBurstRecordUpload(stopCtx context.Context, configNode *config.ConfigTree, use_online_upload_flow *config.ConfigTree, robotClient RobotClient) error {
	var timestampedFiles []TimestampedFile
	var elapsed time.Duration = 0
	for {
		time_to_wait := time.Duration(500*1000000000) - elapsed
		if time_to_wait < 0 {
			time_to_wait = 0
		}
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time_to_wait):
		}

		if !use_online_upload_flow.GetBoolValue() {
			continue
		}

		start := time.Now()

		// Get upload directory based on artifact config node
		var toUploadDir string = fmt.Sprintf("%v/%v", getDataDir(), BurstRecordUploadDir)

		// Select filename from to upload directory
		filename, err := getFilenameFromDir(toUploadDir, "")
		if err != nil {
			error_string := fmt.Sprintf("Failed to upload bursts: %v", err)
			logrus.Error(error_string)
			return errors.New(error_string)
		}

		// Upload burst record
		err = uploadZipMetaPair(robotClient, filename, toUploadDir, "", timestampedFiles, "")
		if err != nil {
			error_string := fmt.Sprintf("Failed to upload bursts: %v", err)
			logrus.Error(error_string)
			return errors.New(error_string)
		}
		end := time.Now()
		elapsed = end.Sub(start)
	}
}

func uploadZipMetaPair(robotClient RobotClient, filename string, fromDirectory string, toDirectory string, timestampedFiles []TimestampedFile, uploadedFilesPath string) error {
	fromZip := fmt.Sprintf("%v/%v.zip", fromDirectory, filename)
	fromMeta := fmt.Sprintf("%v/%v.metadata.json", fromDirectory, filename)

	err := postImageAndMetadata(robotClient, fromZip, fromMeta, UploadPriorityNormal)

	if err != nil {
		logrus.Errorf("Could not post image and metadata, %v", err)
		return err
	}

	if uploadedFilesPath != "" {
		timestampedFiles = append(timestampedFiles, TimestampedFile{
			TimestampMs: time.Now().UnixMilli(),
			Filename:    filename,
		})
		if err := saveUploadedTimesLog(timestampedFiles, uploadedFilesPath); err != nil {
			logrus.Errorf("Couldn't save log file %v", err)
		}
	}

	if toDirectory != "" {
		toZip := fmt.Sprintf("%v/%v.zip", toDirectory, filename)
		toMeta := fmt.Sprintf("%v/%v.%v", toDirectory, filename, metadataExtension)

		// Passing a toDirectory is helpful for debugging what has been sent, but shouldn't be required
		appFs.Rename(fromZip, toZip)
		appFs.Rename(fromMeta, toMeta)
	} else {
		appFs.Remove(fromZip)
		appFs.Remove(fromMeta)
	}
	return nil
}

package services

import (
	"context"
	"github.com/carbonrobotics/robot/golang/data_upload_manager/state"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
	"time"
)

func RunDistanceAccumulation(stopCtx context.Context, distanceAccumulator *state.DistanceAccumulator, dumConfigNode *config.ConfigTree, hardwareManagerClient *hardware_manager.HardwareManagerClient) error {
	for {
		select {
		case <-stopCtx.Done():
			return nil
		case <-time.After(time.Millisecond * time.Duration(int(dumConfigNode.GetNode("distance_accumulation/retrieval_interval_milliseconds").GetIntValue()))):
			distanceTravelled, timestampMs, err := hardwareManagerClient.GetNextDistance()
			if err != nil {
				logrus.Errorf("Error getting distance %v", err)
				continue
			}

			distanceAccumulator.Add(state.TimestampedDistance{
				TimestampMs: timestampMs,
				DistanceMm:  distanceTravelled,
			})
		}
	}
}

font_attributes:
  - name: default
    color: 842700
    size: 10
    style: 0
  - name: menuitem
    color: 842700
    size: 2
    style: 0
  - name: softkey
    color: ffffff
    size: 2
    style: 1
  - name: italic
    color: 842700
    size: 6
    style: 8
  - name: buttonblack
    color: 000000
    size: 3
    style: 1
  - name: buttonred
    color: ff0000
    size: 3
    style: 1
  - name: buttonblackmedium
    color: 000000
    size: 2
    style: 1
  - name: buttonblacksmall
    color: 000000
    size: 1
    style: 1
  - name: tableblack
    color: 000000
    size: 2
    style: 1
  - name: buttonwhitehuge
    color: ffffff
    size: 6
    style: 1
line_attributes:
  - name: bgline
    color: 535353
    thickness: 4
    pattern: ffff
  - name: buttonrect
    color: 333333
    thickness: 3
    pattern: ffff
fill_attributes:
  - name: buttonrect
    color: d1d1d1
  - name: tableheader
    color: aaaaaa
menuitem:
  default_datamask: main
  bg_color: adadad
  layout:
    - type: image
      image: CarbonLogo
      x_loc: 5px
      y_loc: 15px
datamasks:
  - name: checklist
    bg_color: adadad
    softkeys: main
    layout:

      - type: checkbox
        x_loc: 10%
        y_loc: 10%
        font: buttonblack
        bg_color: d1d1d1
        width: 6%
      - type: string
        x_loc: 19%
        y_loc: 11%
        value: "Secure Rear Hitch Implement"
        font: buttonblack

      - type: checkbox
        x_loc: 10%
        y_loc: 18%
        font: buttonblack
        bg_color: d1d1d1
        width: 6%
      - type: string
        x_loc: 19%
        y_loc: 19%
        value: "Secure Front Mount Generator"
        font: buttonblack

      - type: checkbox
        x_loc: 10%
        y_loc: 26%
        font: buttonblack
        bg_color: d1d1d1
        width: 6%
      - type: string
        x_loc: 19%
        y_loc: 27%
        value: "Verify Chiller"
        font: buttonblack

      - type: checkbox
        x_loc: 10%
        y_loc: 34%
        font: buttonblack
        bg_color: d1d1d1
        width: 6%
      - type: string
        x_loc: 19%
        y_loc: 35%
        value: "Target Alignment"
        font: buttonblack

      - type: checkbox
        x_loc: 10%
        y_loc: 42%
        font: buttonblack
        bg_color: d1d1d1
        width: 6%
      - type: string
        x_loc: 19%
        y_loc: 43%
        value: "Clear Environment"
        font: buttonblack

  - name: maintenance
    bg_color: adadad
    softkeys: main
    layout:
      - type: string
        value: "MAINTENANCE PAGE" 
        font: buttonblack
        x_loc: 10%
        y_loc: 10%

  - name: alarms_alarms
    bg_color: adadad
    softkeys: alarms
    layout:
      - type: image
        image: CarbonLogo200
        x_loc: 56%
        y_loc: 2%
      - type: button
        name: "estop"
        x_loc: 2%
        y_loc: 2%
        width: 34%
        height: 16%
        bg_color: be2c1c
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            value: "E-STOP"
            font: buttonwhitehuge
            x_loc: 3.5%
            y_loc: 22%
      - type: table
        name: alarm_alarms
        x_loc: 2%
        y_loc: 21%
        width: 98%
        height: 75%
        line_attributes: buttonrect
        fill_attributes: buttonrect
        header_fill_attributes: tableheader
        rows: 5
        font: tableblack
        upscroll:
          width: 48px
          height: 48px
          bg_color: d1d1d1
          border_color: 535353
          layout:
          - type: image
            image: uparrow
            x_loc: 1%
            y_loc: 1%
        downscroll:
          width: 48px
          height: 48px
          bg_color: d1d1d1
          border_color: 535353
          layout:
          - type: image
            image: downarrow
            x_loc: 1%
            y_loc: 1%
        columns:
          - title_layout:
              type: string
              value: "DATE /\r\nTIME"
              font: buttonblackmedium
            width: 14%
          - title_layout:
              type: string
              value: "ALARM\r\nNUMBER"
              font: buttonblackmedium
            width: 12%
          - title_layout:
              type: string
              value: "SYSTEM"
              font: buttonblackmedium
            width: 12%
          - title_layout:
              type: string
              value: "DESCRIPTION"
              font: buttonblackmedium
            width: 45%
          - title_layout:
              type: string
              value: "OPS\r\nBLOCKER"
              font: buttonblackmedium
            width: 17%

  - name: alarms_logs
    bg_color: adadad
    softkeys: alarms
    layout:
      - type: image
        image: CarbonLogo200
        x_loc: 56%
        y_loc: 2%
      - type: button
        name: "estop"
        x_loc: 2%
        y_loc: 2%
        width: 34%
        height: 16%
        bg_color: be2c1c
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            value: "E-STOP"
            font: buttonwhitehuge
            x_loc: 3.5%
            y_loc: 22%
      - type: table
        name: alarm_logs
        x_loc: 2%
        y_loc: 21%
        width: 98%
        height: 75%
        line_attributes: buttonrect
        fill_attributes: buttonrect
        font: tableblack
        rows: 10
        hide_row_dividers: true
        upscroll:
          width: 48px
          height: 48px
          bg_color: d1d1d1
          border_color: 535353
          layout:
          - type: image
            image: uparrow
            x_loc: 1%
            y_loc: 1%
        downscroll:
          width: 48px
          height: 48px
          bg_color: d1d1d1
          border_color: 535353
          layout:
          - type: image
            image: downarrow
            x_loc: 1%
            y_loc: 1%

  - name: main
    bg_color: adadad
    softkeys: main
    layout:
      - type: image
        image: CarbonLogo200
        x_loc: 4.2% 
        y_loc: 4.2%
      - type: rectangle
        x_loc: 0%
        y_loc: 0%
        width: 100%
        height: 100%
        line_attributes: bgline
      - type: button
        name: laser_state
        x_loc: 52%
        y_loc: 2%
        width: 43%
        height: 10%
        bg_color: d1d1d1
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            name: status
            value: "LASERS: DISABLED"
            font: buttonblack
            x_loc: 2%
            y_loc: 10%
          - type: string
            name: directions
            value: "CLICK TO ENABLE "
            font: buttonblacksmall
            x_loc: 18%
            y_loc: 52%
      - type: container
        x_loc: 52%
        y_loc: 14%
        width: 43%
        height: 10%
        layout:
          - type: rectangle
            line_attributes: buttonrect
            fill_attributes: buttonrect
            x_loc: 0%
            y_loc: 0%
            width: 100%
            height: 100%
          - type: string
            value: "CROP:"
            font: buttonblack
            x_loc: 10%
            y_loc: 20%
          - type: inputlist
            name: crop_select
            x_loc: 40%
            y_loc: 20%
            width: 100%
            height: 50%
            layout:
              - type: string
                value: Lettuce
                font: buttonblack
              - type: string
                value: Onions
                font: buttonblack
              - type: string
                value: Carrots
                font: buttonblack
              - type: string
                value: Broccoli
                font: buttonblack
              - type: string
                value: Cauliflower
                font: buttonblack
              - type: string
                value: Spinach
                font: buttonblack
      - type: button
        name: row1_enable
        x_loc: 2%
        y_loc: 28%
        width: 28%
        height: 16%
        bg_color: d1d1d1
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            value: "ROW 1:"
            font: buttonblack
            x_loc: 20%
            y_loc: 10%
          - type: string
            name: status
            value: "DISABLED"
            font: buttonred
            x_loc: 10%
            y_loc: 35%
          - type: string
            name: directions
            value: "CLICK TO ENABLE "
            font: buttonblacksmall
            x_loc: 2%
            y_loc: 62%
      - type: button
        name: row2_enable
        x_loc: 32%
        y_loc: 28%
        width: 28%
        height: 16%
        bg_color: d1d1d1
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            value: "ROW 2:"
            font: buttonblack
            x_loc: 20%
            y_loc: 10%
          - type: string
            name: status
            value: "DISABLED"
            font: buttonred
            x_loc: 10%
            y_loc: 35%
          - type: string
            name: directions
            value: "CLICK TO ENABLE "
            font: buttonblacksmall
            x_loc: 2%
            y_loc: 62%
      - type: button
        name: row3_enable
        x_loc: 62%
        y_loc: 28%
        width: 28%
        height: 16%
        bg_color: d1d1d1
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            value: "ROW 3:"
            font: buttonblack
            x_loc: 20%
            y_loc: 10%
          - type: string
            name: status
            value: "DISABLED"
            font: buttonred
            x_loc: 10%
            y_loc: 35%
          - type: string
            name: directions
            value: "CLICK TO ENABLE "
            font: buttonblacksmall
            x_loc: 2%
            y_loc: 62%
      - type: container
        x_loc: 2%
        y_loc: 47.6%
        width: 55%
        height: 16%
        layout:
          - type: rectangle
            line_attributes: buttonrect
            fill_attributes: buttonrect
            x_loc: 0%
            y_loc: 0%
            width: 100%
            height: 100%
          - type: string
            value: "WEED DETECTION:SELECT CROP"
            font: buttonblackmedium
            x_loc: 8%
            y_loc: 10%
          - type: string
            value: "IMPLEMENT LIFT: "
            font: buttonblackmedium
            x_loc: 8%
            y_loc: 24%
          - type: string
            name: hitch_position
            value: "000 %"
            font: buttonblackmedium
            x_loc: 58%
            y_loc: 24%
      - type: button
        name: "estop"
        x_loc: 62%
        y_loc: 47.6%
        width: 34%
        height: 16%
        bg_color: be2c1c
        border_color: 535353
        key_code: 12
        layout:
          - type: string
            value: "E-STOP"
            font: buttonwhitehuge
            x_loc: 3.5%
            y_loc: 22%
      - type: meter
        name: speed_meter
        x_loc: 2%
        y_loc: 80%
        width: 30%
        needle_color: ffffff
        border_color: 535353
        draw_border: false
        arc_color: 000000
        ticks: 5
        start_angle: 0
        end_angle: 180
        min_value: 0
        max_value: 100
        value: 0
      - type: container
        x_loc: 2%
        y_loc: 66.8%
        width: 45%
        height: 10%
        layout:
          - type: rectangle
            line_attributes: buttonrect
            fill_attributes: buttonrect
            x_loc: 0%
            y_loc: 0%
            width: 100%
            height: 100%
          - type: string
            value: "CURRENT SPEED:"
            font: buttonblack
            x_loc: 5%
            y_loc: 10%
          - type: string
            name: current_speed
            value: "0.00 MPH"
            font: buttonblack
            x_loc: 22%
            y_loc: 46%
      - type: container
        x_loc: 50%
        y_loc: 66.8%
        width: 45%
        height: 10%
        layout:
          - type: rectangle
            line_attributes: buttonrect
            fill_attributes: buttonrect
            x_loc: 0%
            y_loc: 0%
            width: 100%
            height: 100%
          - type: string
            value: "TARGET SPEED:"
            font: buttonblack
            x_loc: 6.4%
            y_loc: 10%
          - type: string
            name: target_speed
            value: "0.00 MPH"
            font: buttonblack
            x_loc: 22%
            y_loc: 46%
softkeys:
  - name: main
    bg_color: 535353
    buttons:
      - bg_color: adadad
        name: checklist
        layout:
          - type: string
            value: CHECKLIST
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: main
        layout:
          - type: string
            value: MAIN
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: alarms
        layout:
          - type: string
            value: ALARMS
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: maintenance
        layout:
          - type: string
            value: "MAINT-\r\nENANCE"
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: screenswitch
        layout:
          - type: string
            value: "SWITCH\r\nSCREEN"
            font: softkey
            x_loc: 1px
            y_loc: 20px
  - name: alarms
    bg_color: 535353
    buttons:
      - bg_color: adadad
        name: checklist
        layout:
          - type: string
            value: CHECKLIST
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: main
        layout:
          - type: string
            value: MAIN
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: alarms
        layout:
          - type: string
            value: ALARMS
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: maintenance
        layout:
          - type: string
            value: "MAINT-\r\nENANCE"
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: screenswitch
        layout:
          - type: string
            value: "SWITCH\r\nSCREEN"
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: alarms_alarms
        layout:
          - type: string
            value: ALARMS
            font: softkey
            x_loc: 1px
            y_loc: 20px
      - bg_color: adadad
        name: alarms_logs
        layout:
          - type: string
            value: LOGS
            font: softkey
            x_loc: 1px
            y_loc: 20px
        
images:
  - name: CarbonLogo200
    path: assets/menuitem_logo_200.png
  - name: CarbonLogo
    path: assets/menuitem_logo_140.png
  - name: uparrow
    path: assets/uparrow.png
    resize:
      width: 40
      height: 40
  - name: downarrow
    path: assets/downarrow.png
    resize:
      width: 40
      height: 40

//go:build ignore

package main

// #include <stdlib.h>
import "C"

import (
	"flag"
	"fmt"
	"regexp"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/isobus-vt/vt"
)

const (
	SELF_ASSIGNED_ADDRESS = 1    // Self assigned
	INDUSTRY_GROUP        = 2    // Agricultural and Forestry Equipment
	DEVICE_CLASS_INSTANCE = 0    // We are the only one
	DEVICE_CLASS          = 13   // Powered auxilliary devices
	FUNCTION              = 128  // Reservied for Future Use (not sure this is right)
	FUNCTION_INSTANCE     = 0    // We are the only one
	ECU_INSTANCE          = 0    // We are the only one
	MANUFACTURER_CODE     = 1205 // SAE ISOBUS Carbon Code
	IDENTITY_NUMBER       = 1    // This is our first product
)

type RowModule struct {
	name       string
	state      string
	directions string
	font       string
}

func (rm *RowModule) off() {
	rm.state = "DISABLED"
	rm.directions = "CLICK TO ENABLE"
	rm.font = "buttonred"
}

func (rm *RowModule) on() {
	rm.state = "ENABLED"
	rm.directions = "CLICK TO DISABLE"
	rm.font = "buttonblack"
}

func (rm *RowModule) switch_state() {
	if rm.state == "DISABLED" {
		rm.on()
	} else {
		rm.off()
	}
}

func make_rowmodule(name string) *RowModule {
	rm := RowModule{name: name}
	rm.off()
	return &rm
}

type Laser struct {
	state      string
	directions string
	bgcolor    string
}

func (l *Laser) off() {
	l.state = "DISABLED"
	l.directions = "CLICK TO ENABLE"
	l.bgcolor = "d1d1d1"
}

func (l *Laser) on() {
	l.state = "ENABLED"
	l.directions = "CLICK TO DISABLE"
	l.bgcolor = "be2c1c"
}

func (l *Laser) switch_state() {
	if l.state == "DISABLED" {
		l.on()
	} else {
		l.off()
	}
}

func make_laser() *Laser {
	l := Laser{}
	l.off()
	return &l
}

var laser = make_laser()
var rows = []*RowModule{make_rowmodule("row1"), make_rowmodule("row2"), make_rowmodule("row3")}

func inputlist_cb(name string, val string) {
	if name == "main/crop_select" {
		fmt.Println("CROP: " + val)
	}
}

func write_laser(name string, laser *Laser) {
	vt.SetStringValue(name+"/status", laser.state)
	vt.SetStringValue(name+"/directions", laser.directions)
	vt.SetButtonBGColor(name, laser.bgcolor)
}

func write_row(name string, rm *RowModule) {
	vt.SetStringValue(name+"/status", rm.state)
	vt.SetStringFont(name+"/status", rm.font)
	vt.SetStringValue(name+"/directions", rm.directions)
}

func sync_state() {
	write_laser("main/laser_state", laser)
	for _, rm := range rows {
		write_row(fmt.Sprintf("main/%s_enable", rm.name), rm)
	}
}

func button_cb(name string, code int) {
	if code != vt.BUTTON_PRESSED {
		return
	}

	if name == "maintenance" {
		vt.SetActiveMask("maintenance")
	} else if name == "main" {
		vt.SetActiveMask("main")
	} else if name == "alarms" || name == "alarms_alarms" {
		vt.SetActiveMask("alarms_alarms")
	} else if name == "alarms_logs" {
		vt.SetActiveMask("alarms_logs")
	} else if name == "checklist" {
		vt.SetActiveMask("checklist")
	} else if name == "screenswitch" {
		vt.SwitchScreen()
		sync_state()
	} else if name == "main/laser_state" {
		laser.switch_state()
		write_laser(name, laser)
	} else if name == "main/estop" {
		laser.off()
		for _, rm := range rows {
			rm.off()
		}
		sync_state()
	} else {
		re := regexp.MustCompile("main/row([0-9])_enable")
		if res := re.FindStringSubmatch(name); res != nil && len(res) > 1 {
			which, _ := strconv.Atoi(res[1])
			rm := rows[which-1]
			rm.switch_state()
			write_row(name, rm)
		}
	}
}

func speed_cb(speed float32) {
	var fake_target_speed float32 = 1.31415926535897932384
	vt.SetStringValue("main/current_speed", fmt.Sprintf("%.2f MPH", speed))
	vt.SetStringValue("main/target_speed", fmt.Sprintf("%.2f MPH", fake_target_speed))
	var speed_val float32 = (speed / fake_target_speed) * 50 // 50 is exactly on target, 100 is 2x, 0 is 0x
	if speed_val > 100 {
		speed_val = 100
	}
	vt.SetMeterValue("main/speed_meter", int(speed_val))
}

func hitch_cb(position int) {
	vt.SetStringValue("main/hitch_position", fmt.Sprintf("%d %%", position))
}

func test_table_cells() {
	for i := 0; i < 3; i++ {
		vt.AppendTableRow("alarms_alarms/alarm_alarms",
			[]string{time.Now().Local().Format(time.UnixDate), "COL1", "COL2", "Here is my message", "X"})
	}
}

func main() {
	layout_dir := ""
	flag.StringVar(&layout_dir, "l", "", "Path to layout directory.")
	flag.Parse()

	vt.SetInputlistDispatch(inputlist_cb)
	vt.SetButtonDispatch(button_cb)
	vt.SetSpeedDispatch(speed_cb)
	vt.SetHitchDispatch(hitch_cb)

	vt.SetISOBusName(SELF_ASSIGNED_ADDRESS, INDUSTRY_GROUP, DEVICE_CLASS_INSTANCE, DEVICE_CLASS,
		FUNCTION, FUNCTION_INSTANCE, ECU_INSTANCE, MANUFACTURER_CODE, IDENTITY_NUMBER)
	vt.StartISOBus(0, layout_dir, vt.LOG_INFO, "-")
	go test_table_cells()

	for {
		time.Sleep(999999)
	}
}

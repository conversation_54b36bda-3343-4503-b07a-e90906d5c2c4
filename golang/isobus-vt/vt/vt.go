//go:build ignore

package vt

// #cgo LDFLAGS: -lvt
//
// #include <stdlib.h>
// #include <libvt/vt.h>
//
// static void go_start_isobus(int canid, const char *layout_dir, unsigned int log_level, const char *filename) {
//   start_isobus(canid, layout_dir, log_level, filename);
// }
//
// extern int wakeup_cb();
// static int wakeup_cb_c() {
//   return wakeup_cb();
// }
// static void set_go_wakeup_cb() {
//   set_wakeup_dispatch(wakeup_cb_c);
// }
//
// extern int dispatch_inputlist(char *, char *);
// static int inputlist_dispatch_c(const char *name, const char *val) {
//   return dispatch_inputlist((char *)name, (char *)val);
// }
// static void set_go_inputlist_dispatch() {
//   set_inputlist_dispatch(inputlist_dispatch_c);
// }
//
// extern int dispatch_button(char *, int);
// static int button_dispatch_c(const char *name, int code) {
//   return dispatch_button((char *)name, code);
// }
// static void set_go_button_dispatch() {
//   set_button_dispatch(button_dispatch_c);
// }
//
// extern int dispatch_speed(float);
// static int speed_dispatch_c(float speed) {
//   return dispatch_speed(speed);
// }
// static void set_go_speed_dispatch() {
//   set_speed_dispatch(speed_dispatch_c);
// }
//
// extern int dispatch_hitch(int);
// static int hitch_dispatch_c(int position) {
//   return dispatch_hitch(position);
// }
// static void set_go_hitch_dispatch() {
//   set_hitch_dispatch(hitch_dispatch_c);
// }
//
import "C" /* This line must have no blank lines above it */

import "unsafe"

type LogLevel int

const (
	LOG_OFF         LogLevel = 0
	LOG_ERROR                = 1
	LOG_WARN                 = 2
	LOG_INFO                 = 3
	LOG_DEBUG                = 4
	LOG_DEBUG_PROTO          = 5
)

const (
	BUTTON_RELEASED = 0
	BUTTON_PRESSED  = 1
	BUTTON_HELD     = 2
	BUTTON_ABORTED  = 3
)

// our channel for waking up from c
var wait_chan = make(chan int)

// inputlist
type inputlist_cb_type func(string, string)

var inputlist_cb_ptr inputlist_cb_type

func SetInputlistDispatch(inputlist_cb inputlist_cb_type) {
	inputlist_cb_ptr = inputlist_cb
	C.set_go_inputlist_dispatch()
}

//export dispatch_inputlist
func dispatch_inputlist(name *C.char, val *C.char) C.int {
	if inputlist_cb_ptr != nil {
		inputlist_cb_ptr(C.GoString(name), C.GoString(val))
	}
	return C.int(0)
}

// button
type button_cb_type func(string, int)

var button_cb_ptr button_cb_type

func SetButtonDispatch(button_cb button_cb_type) {
	button_cb_ptr = button_cb
	C.set_go_button_dispatch()
}

// speed
type speed_cb_type func(float32)

var speed_cb_ptr speed_cb_type

func SetSpeedDispatch(speed_cb speed_cb_type) {
	speed_cb_ptr = speed_cb
	C.set_go_speed_dispatch()
}

// hitch
type hitch_cb_type func(int)

var hitch_cb_ptr hitch_cb_type

func SetHitchDispatch(hitch_cb hitch_cb_type) {
	hitch_cb_ptr = hitch_cb
	C.set_go_hitch_dispatch()
}

//export dispatch_button
func dispatch_button(name *C.char, code C.int) C.int {
	if button_cb_ptr != nil {
		go button_cb_ptr(C.GoString(name), int(code))
	}
	return C.int(0)
}

//export dispatch_speed
func dispatch_speed(speed C.float) C.int {
	if speed_cb_ptr != nil {
		go speed_cb_ptr(float32(speed))
	}
	return C.int(0)
}

//export dispatch_hitch
func dispatch_hitch(position C.int) C.int {
	if hitch_cb_ptr != nil {
		go hitch_cb_ptr(int(position))
	}
	return C.int(0)
}

//export wakeup_cb
func wakeup_cb() C.int {
	wait_chan <- 0
	return C.int(0)
}

func SetActiveMask(maskname string) {
	mask := C.CString(maskname)
	defer C.free(unsafe.Pointer(mask))
	C.set_active_mask(mask)
	<-wait_chan
}

func SetStringValue(name string, val string) {
	n := C.CString(name)
	defer C.free(unsafe.Pointer(n))
	v := C.CString(val)
	defer C.free(unsafe.Pointer(v))

	C.set_string_value(n, v)
	<-wait_chan
}

func SetStringFont(name string, font string) {
	n := C.CString(name)
	defer C.free(unsafe.Pointer(n))
	f := C.CString(font)
	defer C.free(unsafe.Pointer(f))

	C.set_string_font(n, f)
	<-wait_chan
}

func SetButtonBGColor(name string, color string) {
	n := C.CString(name)
	defer C.free(unsafe.Pointer(n))
	c := C.CString(color)
	defer C.free(unsafe.Pointer(c))

	C.set_button_bgcolor(n, c)
	<-wait_chan
}

func SetMeterValue(name string, value int) {
	n := C.CString(name)
	defer C.free(unsafe.Pointer(n))

	C.set_meter_value(n, C.int(value))
	<-wait_chan
}

func AppendTableRow(name string, cols []string) {
	n := C.CString(name)
	var converted []*C.char
	for _, vstr := range cols {
		v := C.CString(vstr)
		defer C.free(unsafe.Pointer(v))
		converted = append(converted, v)
	}
	C.append_table_row(n, &converted[0], C.int(len(converted)))
	<-wait_chan
}

func StartISOBus(can_id int, layout_dir string, log_level int, logfile string) {
	C.set_go_wakeup_cb()

	ld := C.CString(layout_dir)
	defer C.free(unsafe.Pointer(ld))

	lf := C.CString(logfile)
	defer C.free(unsafe.Pointer(lf))

	C.go_start_isobus(C.int(can_id), ld, C.uint(log_level), lf)
	<-wait_chan
}

func SetISOBusName(self_assigned_address int, industry_group int, device_class_instance int, device_class int,
	function int, function_instance int, ecu_instance int, manufacturer_code int,
	identity_number int) {
	C.set_isobus_name(C.int(self_assigned_address), C.int(industry_group), C.int(device_class_instance),
		C.int(device_class), C.int(function), C.int(function_instance), C.int(ecu_instance),
		C.int(manufacturer_code), C.int(identity_number))
}

func SwitchScreen() {
	C.switch_screen()
	<-wait_chan
}

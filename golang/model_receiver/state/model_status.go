package state

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/spf13/afero"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	"github.com/sirupsen/logrus"
)

var appRecieverFs = afero.NewOsFs()

type ModelStatus struct {
	env      environment.Robot
	mu       sync.RWMutex
	models   map[string]*model_manager.Model
	cacheDir string
}

func NewModelStatus(env environment.Robot) *ModelStatus {
	return &ModelStatus{
		cacheDir: model_manager.ModelCacheDir(env),
	}
}

func (ms *ModelStatus) ListModels() []*model_manager.Model {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	list := make([]*model_manager.Model, 0)
	for _, model := range ms.models {
		list = append(list, model)
	}

	return list
}

func (ms *ModelStatus) WriteModelArtifact(modelID, artifactFileName string, artifactContents []byte) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if err := appRecieverFs.MkdirAll(ms.cacheDir, 0777); err != nil {
		return err
	}

	modelFolderPath := filepath.Join(ms.cacheDir, modelID)
	if err := appRecieverFs.MkdirAll(modelFolderPath, 0777); err != nil {
		return err
	}

	artifactFilePath := filepath.Join(modelFolderPath, artifactFileName)
	if err := afero.WriteFile(appRecieverFs, artifactFilePath, artifactContents, 0666); err != nil {
		return err
	}

	logrus.Infof("Wrote model artifact to %s", artifactFilePath)

	return nil
}

func (ms *ModelStatus) WriteModelMetadata(modelID string, MetadataContents []byte) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if err := appRecieverFs.MkdirAll(ms.cacheDir, 0777); err != nil {
		return err
	}

	metadataFilePath := filepath.Join(ms.cacheDir, modelID+model_manager.MetaExt)
	if err := afero.WriteFile(appRecieverFs, metadataFilePath, MetadataContents, 0666); err != nil {
		return err
	}

	logrus.Infof("Wrote model metadata to %s", metadataFilePath)
	return nil
}

func (ms *ModelStatus) CleanupModels(modelIDs []string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	for _, modelID := range modelIDs {
		modelFolder := filepath.Join(ms.cacheDir, modelID)

		err := appRecieverFs.RemoveAll(modelFolder)
		if err != nil {
			return fmt.Errorf("failed to remove model folder %w", err)
		}
		removeFiles(filepath.Join(ms.cacheDir, modelID+model_manager.MetaExt))
		delete(ms.models, modelID)
	}
	return nil
}

func (ms *ModelStatus) UpdateModels() {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	metadataFiles, err := afero.ReadDir(appRecieverFs, ms.cacheDir)
	if err != nil {
		logrus.WithError(err).Error("Failed to read model receiver cache directory")
		return
	}

	models := make(map[string]*model_manager.Model)
	for _, metadataFile := range metadataFiles {
		// skip model directories
		if metadataFile.IsDir() {
			continue
		}

		// skip pre-artifact model files
		if !strings.HasSuffix(metadataFile.Name(), model_manager.MetaExt) {
			continue
		}

		metaFilePath := filepath.Join(ms.cacheDir, metadataFile.Name())
		model, err := model_manager.GetVerifyMetadata(metaFilePath)
		if err != nil {
			logrus.Errorf("failed reading metadata: %s - %v", metaFilePath, err)
			removeFiles(metaFilePath)
			continue
		}

		modelDir := filepath.Join(ms.cacheDir, model.ID)
		if !model.IsValid() {
			logrus.Errorf("Model meta: %s is not valid", metaFilePath)
			removeFiles(metaFilePath)
			appRecieverFs.RemoveAll(modelDir)
			continue
		}

		artifactError := false
		for _, artifact := range model.ModelArtifacts {
			artifactFilePath := filepath.Join(modelDir, model_manager.ComposeModelArtifactFilename(artifact.ComputeCapability, artifact.TensorRTVersion))
			if err := model_manager.VerifyModelArtifactDownload(artifact, artifactFilePath); err != nil {
				logrus.Errorf("malformed artifact download: %s - %v", artifactFilePath, err)
				removeFiles(artifactFilePath)
				artifactError = true
				break
			}
		}

		if artifactError {
			continue
		}

		models[model.ID] = model
	}

	ms.models = models
}

func removeFiles(files ...string) {
	logrus.Infoln("removing files:", files)
	for _, fil := range files {
		if err := appRecieverFs.Remove(fil); err != nil {
			logrus.Warnf("failed to remove %s - %v", fil, err)
		}
	}
}

func (ms *ModelStatus) RunModelWatcher(stopCtx context.Context, triggerChan <-chan bool) {
	for {
		ms.UpdateModels()
		select {
		case <-stopCtx.Done():
			return
		case <-time.After(5 * time.Minute):
		case <-triggerChan:
		}
	}
}

package git

import (
	"crypto/sha1"
	"encoding/hex"
	"fmt"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
)

// FilesChangedFromRevision returns a slice of file paths that have been added, deleted, or modified
// from the current HEAD compared to a given base revision (branch)
// repoPath should be a git directory (containing .git)
// baseRevision can be a branch or hash identifier (ex: master)
func FilesChangedFromRevision(repoPath, baseRevision string) ([]string, error) {
	repo, err := git.PlainOpen(repoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open: %w", err)
	}
	head, err := repo.Head()
	if err != nil {
		return nil, fmt.Errorf("failed to get head: %w", err)
	}
	headCommit, err := repo.CommitObject(head.Hash())
	if err != nil {
		return nil, fmt.Errorf("failed to get head commit: %w", err)
	}
	revisionHash, err := repo.ResolveRevision(plumbing.Revision(baseRevision))
	if err != nil {
		return nil, fmt.Errorf("failed to resolve revision %q - %w", baseRevision, err)
	}
	baseRevCommit, err := repo.CommitObject(*revisionHash)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve commit for %q - %w", *revisionHash, err)
	}
	p, err := baseRevCommit.Patch(headCommit)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve patch comparison: %w", err)
	}
	changed := make([]string, 0)
	for _, fil := range p.FilePatches() {
		from, to := fil.Files()
		switch {
		case from == nil && to != nil: // creation
			changed = append(changed, to.Path())
		case from != nil: // changed or deleted
			changed = append(changed, from.Path())
		default:
			// skip submodules/directories/etc...
		}
	}
	return changed, nil
}

func BranchSHA(repoPath string) (string, error) {
	r, err := git.PlainOpen(repoPath)
	if err != nil {
		return "", err
	}

	ref, err := r.Head()
	if err != nil {
		return "", err
	}

	hash := sha1.Sum([]byte(ref.Name().Short()))
	return hex.EncodeToString(hash[:]), nil
}

func CommitHash(repoPath string) (string, error) {
	r, err := git.PlainOpen(repoPath)
	if err != nil {
		return "", err
	}

	ref, err := r.Head()
	if err != nil {
		return "", err
	}

	return ref.Hash().String(), nil
}

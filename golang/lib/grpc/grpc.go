package grpc

import (
	"context"
	"fmt"

	"golang.org/x/oauth2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

func RobotMetadataInjector(mapperFn func() map[string]string) grpc.DialOption {
	return grpc.WithUnaryInterceptor(func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		kv := make([]string, 0)
		for k, v := range mapperFn() {
			kv = append(kv, k, v)
		}
		ctx = metadata.AppendToOutgoingContext(ctx, kv...)
		return invoker(ctx, method, req, reply, cc, opts...)
	})
}

func RobotMetadataServerInjectorInterceptorFactory(mapperFn func() map[string]string) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		for k, v := range mapperFn() {
			md := metadata.Pairs(k, v)
			grpc.SendHeader(ctx, md)
		}
		return handler(ctx, req)
	}
}

func RobotMetadataServerInjector(mapperFn func() map[string]string) grpc.ServerOption {
	return grpc.UnaryInterceptor(RobotMetadataServerInjectorInterceptorFactory(mapperFn))
}

func AuthorizationInjector(tokenSource oauth2.TokenSource) grpc.DialOption {
	return grpc.WithUnaryInterceptor(func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		token, err := tokenSource.Token()
		if err != nil {
			return err
		}
		ctx = metadata.AppendToOutgoingContext(ctx, "authorization", fmt.Sprintf("Bearer %s", token.AccessToken))
		return invoker(ctx, method, req, reply, cc, opts...)
	})
}

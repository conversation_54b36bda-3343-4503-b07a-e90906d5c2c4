package robot_syncer_client

import (
	"net"
	"strings"
	"sync"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/carbonrobotics/robot/golang/lib/auth"
	crgrpc "github.com/carbonrobotics/robot/golang/lib/grpc"
	log "github.com/sirupsen/logrus"
	"golang.org/x/oauth2"
	"google.golang.org/grpc"
)

type Client struct {
	Addr  string
	mutex sync.Mutex
	conn  *grpc.ClientConn
	opts  []grpc.DialOption
}

func NewRoSyClient(addr string, port string, tokenSource oauth2.TokenSource, opts ...grpc.DialOption) *Client {
	log.Infof("[RoSy] Creating new rosy client for %s", addr)
	if port == "" {
		addr = net.JoinHostPort(addr, "443") // default to 443
	} else {
		addr = net.JoinHostPort(addr, port)
	}
	secure := strings.HasSuffix(addr, "443")
	bundle := auth.NewCredentialsBundle(tokenSource, secure)

	client := &Client{
		Addr: addr,
		opts: []grpc.DialOption{
			grpc.WithCredentialsBundle(bundle),
		},
	}
	if !secure {
		// if it's not on port 443, use an insecure channel but hack in the credentials
		client.opts = append(client.opts, crgrpc.AuthorizationInjector(tokenSource))
	}
	client.opts = append(client.opts, opts...)
	return client
}

func (c *Client) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
}

func (c *Client) getConnected() (*grpc.ClientConn, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.Addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	return c.conn, nil
}

func (c *Client) sendRequest(f func(con *grpc.ClientConn) (any, error)) (any, error) {
	con, err := c.getConnected()
	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(con)

	if err != nil {
		if isNetworkError(err) {
			// if it's a network error, reset the connection
			log.WithError(err).Errorf("[RoSy] Network error, resetting connection...")
			c.resetConnection()
			return nil, err
		} else {
			// don't log application errors, the caller should and handle
			return result, err
		}
	}

	return result, nil
}

func isNetworkError(err error) bool {
	code := status.Code(err)
	for _, networkErrorCode := range []codes.Code{codes.Aborted, codes.Canceled, codes.DataLoss, codes.DeadlineExceeded, codes.Unavailable} {
		if code == networkErrorCode {
			return true
		}
	}
	return false
}

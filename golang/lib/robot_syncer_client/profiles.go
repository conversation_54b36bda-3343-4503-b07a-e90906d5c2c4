package robot_syncer_client

import (
	"context"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"

	"github.com/carbonrobotics/robot/golang/generated/proto/robot_syncer"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

// Active status of profiles is reported to portal via the healthlog field_config

func (c *Client) GetProfilesData(robotSerial string) (map[string]*frontend.ProfileSyncData, error) {
	resp, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := robot_syncer.NewRoSyProfileSyncServiceClient(conn)
		return client.GetProfileSyncData(context.Background(), &robot_syncer.GetProfileSyncDataRequest{
			RobotSerial: robotSerial,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[RoSy] Failed get profiles")
		return nil, err
	}

	return resp.(*robot_syncer.GetProfileSyncDataResponse).Profiles, nil
}

func (c *Client) UploadProfile(req *robot_syncer.UploadProfileRequest) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := robot_syncer.NewRoSyProfileSyncServiceClient(conn)
		return client.UploadProfile(context.Background(), req)
	})

	if err != nil {
		log.WithError(err).Errorf("[RoSy] Failed upload profile %v", req.Profile)
		return err
	}

	return nil
}

func (c *Client) GetProfile(uuid string) (*robot_syncer.GetProfileResponse, error) {
	resp, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := robot_syncer.NewRoSyProfileSyncServiceClient(conn)
		return client.GetProfile(context.Background(), &robot_syncer.GetProfileRequest{Uuid: uuid})
	})

	if err != nil {
		log.WithError(err).Errorf("[RoSy] Failed get profile %s", uuid)
		return nil, err
	}

	return resp.(*robot_syncer.GetProfileResponse), nil
}

func (c *Client) DeleteProfile(uuid string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := robot_syncer.NewRoSyProfileSyncServiceClient(conn)
		return client.DeleteProfile(context.Background(), &robot_syncer.DeleteProfileRequest{Uuid: uuid})
	})

	if err != nil {
		log.WithError(err).Errorf("[RoSy] Failed delete profile %s", uuid)
		return err
	}

	return nil
}

func (c *Client) PurgeProfile(uuid string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := robot_syncer.NewRoSyProfileSyncServiceClient(conn)
		return client.PurgeProfile(context.Background(), &robot_syncer.PurgeProfileRequest{Uuid: uuid})
	})

	if err != nil {
		log.WithError(err).Errorf("[RoSy] Failed purge profile %s", uuid)
		return err
	}

	return nil
}

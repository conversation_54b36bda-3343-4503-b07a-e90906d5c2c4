package metrics_aggregator

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	"github.com/carbonrobotics/robot/golang/generated/proto/metrics_aggregator"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

type MetricsAggregatorClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client metrics_aggregator.MetricsAggregatorServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *MetricsAggregatorClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewMetricsAggregatorClient(addr string) *MetricsAggregatorClient {
	client := &MetricsAggregatorClient{
		addr: addr,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *MetricsAggregatorClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func getDefaultMetricsAggregatorClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
}

func (c *MetricsAggregatorClient) getClient() (metrics_aggregator.MetricsAggregatorServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = metrics_aggregator.NewMetricsAggregatorServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *MetricsAggregatorClient) sendRequest(f func(metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.resetConnection()
			default:
			}
		}
		return nil, err
	}

	return result, nil
}

func (c *MetricsAggregatorClient) GetMetrics() (*metrics_aggregator.GetMetricsResponse, error) {
	result, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.GetMetrics(context.Background(), &metrics_aggregator.GetMetricsRequest{})
	})
	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return nil, err
	}

	return result.(*metrics_aggregator.GetMetricsResponse), nil
}

func (c *MetricsAggregatorClient) AcknowledgeDailyMetric(days []string) error {
	_, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.AcknowledgeDailyMetric(context.Background(), &metrics_aggregator.AcknowledgeDailyMetricRequest{
			Days: days,
		})
	})
	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return err
	}
	return nil
}

func (c *MetricsAggregatorClient) GetJobMetrics() (*metrics_aggregator.GetJobMetricsResponse, error) {
	result, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.GetJobMetrics(context.Background(), &metrics_aggregator.GetJobMetricsRequest{})
	})

	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return nil, err
	}

	return result.(*metrics_aggregator.GetJobMetricsResponse), nil
}

func (c *MetricsAggregatorClient) GetLaserLifeTimes() (*metrics.LaserLifeTimes, error) {
	result, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.GetLaserLifeTimes(context.Background(), &metrics_aggregator.GetLaserLifeTimesRequest{})
	})
	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return nil, err
	}
	return result.(*metrics.LaserLifeTimes), nil
}

func (c *MetricsAggregatorClient) GetLaserDetails() (*metrics.LaserChangeTimes, error) {
	result, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.GetLaserChangeTimes(context.Background(), &metrics_aggregator.GetLaserChangeTimesRequest{})
	})
	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return nil, err
	}
	return result.(*metrics.LaserChangeTimes), nil
}

func (c *MetricsAggregatorClient) SetLaser(serial string, row uint32, slot uint32) error {
	_, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.SetLaser(context.Background(), &metrics.LaserIdentifier{
			Position: &metrics.LaserPosition{
				Row:  row,
				Slot: slot,
			},
			Serial: serial,
		})
	})
	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return err
	}
	return nil
}

func (c *MetricsAggregatorClient) OverrideLaser(serial string, row uint32, slot uint32, lifetimeS uint64) error {
	_, err := c.sendRequest(func(client metrics_aggregator.MetricsAggregatorServiceClient) (interface{}, error) {
		return client.OverrideLaser(context.Background(), &metrics_aggregator.OverrideLaserRequest{
			Laser: &metrics.LaserIdentifier{
				Position: &metrics.LaserPosition{
					Row:  row,
					Slot: slot,
				},
				Serial: serial,
			},
			LifetimeS: lifetimeS,
		})
	})
	if err != nil {
		logrus.Warnf("Got err: %v", err)
		return err
	}
	return nil
}

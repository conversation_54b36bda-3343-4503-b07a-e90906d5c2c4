package auth

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/clientcredentials"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
)

const GitHubAccessToken = "****************************************"

const RedisPassword = "T2Yw28ctIZ3gwI1t"

const FTPPassword = "4hXNE9fHuyPnJ3wTFzYb"

type Auth0Config struct {
	ClientID     string   `envconfig:"AUTH0_CLIENT_ID"`
	ClientSecret string   `envconfig:"AUTH0_CLIENT_SECRET"`
	Scopes       []string `envconfig:"AUTH0_SCOPES"`
	TokenURL     string   `envconfig:"AUTH0_TOKEN_URL"`
}

func NewRobotAuth0Config(env environment.Robot, scopes []string, tokenURL string) *Auth0Config {
	return &Auth0Config{
		ClientID:     env.MakaAuthClientID,
		ClientSecret: env.MakaAuthClientSecret,
		Scopes:       scopes,
		TokenURL:     tokenURL,
	}
}

func (ac *Auth0Config) AuthenticatedHttpClient(ctx context.Context) *http.Client {
	config := &clientcredentials.Config{
		ClientID:     ac.ClientID,
		ClientSecret: ac.ClientSecret,
		Scopes:       ac.Scopes,
		TokenURL:     ac.TokenURL,
	}
	return config.Client(ctx)
}

func (ac *Auth0Config) TokenSource(ctx context.Context) oauth2.TokenSource {
	config := &clientcredentials.Config{
		ClientID:     ac.ClientID,
		ClientSecret: ac.ClientSecret,
		Scopes:       ac.Scopes,
		TokenURL:     ac.TokenURL,
	}
	return config.TokenSource(ctx)
}

type Bundle struct {
	secure      bool
	tokenSource oauth2.TokenSource
}

func NewCredentialsBundle(tokenSource oauth2.TokenSource, secure bool) *Bundle {
	return &Bundle{
		tokenSource: tokenSource,
		secure:      secure,
	}
}

func (b *Bundle) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
	token, err := b.tokenSource.Token()
	if err != nil {
		return nil, err
	}
	return map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token.AccessToken),
	}, nil
}

func (b *Bundle) RequireTransportSecurity() bool {
	return b.secure
}

func (b *Bundle) TransportCredentials() credentials.TransportCredentials {
	if b.secure {
		return credentials.NewTLS(&tls.Config{})
	} else {
		return insecure.NewCredentials()
	}
}

func (b *Bundle) PerRPCCredentials() credentials.PerRPCCredentials {
	return b
}

func (b *Bundle) NewWithMode(mode string) (credentials.Bundle, error) {
	return &Bundle{
		secure:      mode == "secure",
		tokenSource: b.tokenSource,
	}, nil
}

func RobotUserPassLogin(ctx context.Context, tokenURL string, robot environment.Robot) (*oauth2.Token, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, tokenURL, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set(environment.RobotHTTPHeader, robot.MakaRobotName)
	req.Header.Set(environment.GenerationHTTPHeader, robot.MakaGen)
	req.SetBasicAuth(robot.CarbonRobotUsername, robot.CarbonRobotPassword)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	token := new(oauth2.Token)
	if err := json.NewDecoder(resp.Body).Decode(&token); err != nil {
		return nil, err
	}

	return token, nil
}

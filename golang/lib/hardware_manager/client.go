package hardware_manager

import (
	"context"
	"errors"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

var LongPollTimeoutError = errors.New("Long poll timed out")

type HardwareManagerClient struct {
	addr                        string
	mutex                       sync.Mutex
	conn                        *grpc.ClientConn
	client                      hardware_manager.HardwareManagerServiceClient
	opts                        []grpc.DialOption
	previousDistanceTimestampMs uint64
	previousDistance            float64
	previousVelocityTimestampMs uint64
	previousVelocity            float64
}

func destroyConnection(c *HardwareManagerClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewHardwareManagerClient(addr string) *HardwareManagerClient {
	client := &HardwareManagerClient{
		addr:                        addr,
		previousDistanceTimestampMs: 0,
		previousDistance:            0,
		previousVelocityTimestampMs: 0,
		previousVelocity:            0,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *HardwareManagerClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func getDefaultHardwareManagerClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
}

func (c *HardwareManagerClient) getClient() (hardware_manager.HardwareManagerServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = hardware_manager.NewHardwareManagerServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *HardwareManagerClient) sendRequest(f func(hardware_manager.HardwareManagerServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.resetConnection()
			case codes.DeadlineExceeded:
				logrus.Warningf("Resetting hw manager connection due to deadline exceeded error.")
				c.resetConnection()
			default:
			}
		}
		return nil, err
	}

	return result, nil
}
func (c *HardwareManagerClient) sendRequestLongPoll(f func(hardware_manager.HardwareManagerServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.resetConnection()
			case codes.DeadlineExceeded:
				return nil, LongPollTimeoutError
			default:
			}
		}
		return nil, err
	}

	return result, nil
}

func (c *HardwareManagerClient) GetReady() (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetReady(ctx, &hardware_manager.GetReadyRequest{})
	})

	if err != nil {
		return false, err
	}

	return result.(*hardware_manager.GetReadyResponse).Ready, nil
}

func (c *HardwareManagerClient) GetGPSData(validate bool) (*hardware_manager.GetGPSDataResponse, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetGPSData(ctx, &hardware_manager.GetGPSDataRequest{Validate: validate})
	})
	if err != nil {
		return nil, err
	}

	return result.(*hardware_manager.GetGPSDataResponse), nil
}
func (c *HardwareManagerClient) GetNextRawGPSData(timestampMs int64) (*hardware_manager.GetNextRawGPSDataResponse, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetNextRawGPSData(ctx, &hardware_manager.GetNextRawGPSDataRequest{TimestampMs: timestampMs})
	})
	if err != nil {
		return nil, err
	}

	return result.(*hardware_manager.GetNextRawGPSDataResponse), nil
}

func (c *HardwareManagerClient) GetNextDistance() (float64, uint64, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetNextDistance(ctx, &hardware_manager.GetNextDistanceRequest{TimestampMs: c.previousDistanceTimestampMs})
	})

	if err != nil {
		return 0, 0, err
	}

	c.previousDistanceTimestampMs = result.(*hardware_manager.GetNextDistanceResponse).GetTimestampMs()

	return result.(*hardware_manager.GetNextDistanceResponse).GetDistance(), result.(*hardware_manager.GetNextDistanceResponse).GetTimestampMs(), nil
}

func (c *HardwareManagerClient) GetDeltaTravelMM(id string) (float64, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetDeltaTravelMM(ctx, &hardware_manager.GetDeltaTravelMMRequest{Id: id})
	})

	if err != nil {
		return 0, err
	}

	return result.(*hardware_manager.GetDeltaTravelMMResponse).DeltaMm, nil
}

func (c *HardwareManagerClient) GetVelocity() (float64, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetNextVelocity(ctx, &hardware_manager.GetNextVelocityRequest{TimestampMs: c.previousDistanceTimestampMs})
	})

	if err != nil {
		return 0, err
	}

	var mmPerMs float64

	if result.(*hardware_manager.GetNextVelocityResponse).GetLifted() {
		mmPerMs = c.previousVelocity
	} else {
		mmPerMs = result.(*hardware_manager.GetNextVelocityResponse).GetMmPerMs()
		c.previousVelocity = mmPerMs
	}

	c.previousVelocityTimestampMs = result.(*hardware_manager.GetNextVelocityResponse).GetTimestampMs()

	return mmPerMs, nil
}

func (c *HardwareManagerClient) GetRotaryTicks() (*hardware_manager.GetRotaryTicksResponse, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetRotaryTicks(ctx, &hardware_manager.GetRotaryTicksRequest{})
	})

	if err != nil {
		return nil, err
	}

	return result.(*hardware_manager.GetRotaryTicksResponse), nil
}

func (c *HardwareManagerClient) GetSafetyStatus() (*hardware_manager.GetSafetyStatusResponse, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetSafetyStatus(ctx, &hardware_manager.GetSafetyStatusRequest{})
	})

	if err != nil {
		return nil, err
	}

	return result.(*hardware_manager.GetSafetyStatusResponse), nil
}

func (c *HardwareManagerClient) GetSupervisoryStatus() (*hardware_manager.GetSupervisoryStatusResponse, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetSupervisoryStatus(ctx, &hardware_manager.GetSupervisoryStatusRequest{})
	})

	if err != nil {
		if e, ok := status.FromError(err); ok {
			logrus.Info("Grpc error code: ", e.Code(), " ", e.Details())
		}
		return nil, err
	}

	return result.(*hardware_manager.GetSupervisoryStatusResponse), nil
}

func (c *HardwareManagerClient) GetManagedBoardErrors() ([]string, string, bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetManagedBoardErrors(ctx, &hardware_manager.GetManagedBoardErrorsRequest{})
	})
	if err != nil {
		return nil, "", false, err
	}
	return result.(*hardware_manager.GetManagedBoardErrorsResponse).Board, result.(*hardware_manager.GetManagedBoardErrorsResponse).EncoderErrorMsg, result.(*hardware_manager.GetManagedBoardErrorsResponse).GpsHasFix, nil
}

func (c *HardwareManagerClient) GetAvailableUSBStorage() (float32, bool, bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetAvailableUSBStorage(context.Background(), &hardware_manager.GetAvailableUSBStorageRequest{})
	})
	if err != nil {
		return 0, false, false, err
	}
	return result.(*hardware_manager.GetAvailableUSBStorageResponse).Used, result.(*hardware_manager.GetAvailableUSBStorageResponse).Success, result.(*hardware_manager.GetAvailableUSBStorageResponse).UsbAvailable, nil
}

func (c *HardwareManagerClient) SetServerDisable(row int64, disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetServerDisable(context.Background(), &hardware_manager.SetServerDisableRequest{
			RowId:   row,
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetServerDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetBTLDisable(row int64, disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetBTLDisable(context.Background(), &hardware_manager.SetBTLDisableRequest{
			RowId:   row,
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetBTLDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetScannersDisable(row int64, disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetScannersDisable(context.Background(), &hardware_manager.SetScannersDisableRequest{
			RowId:   row,
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetScannersDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetWheelEncoderBoardDisable(disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetWheelEncoderBoardDisable(context.Background(), &hardware_manager.SetWheelEncoderBoardDisableRequest{
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetWheelEncoderBoardDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetWheelEncoderDisable(front bool, left bool, disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetWheelEncoderDisable(context.Background(), &hardware_manager.SetWheelEncoderDisableRequest{
			Disable: disable,
			Front:   front,
			Left:    left,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetWheelEncoderDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetGPSDisable(disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetGPSDisable(context.Background(), &hardware_manager.SetGPSDisableRequest{
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetGPSDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetStrobeDisable(disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetStrobeDisable(context.Background(), &hardware_manager.SetStrobeDisableRequest{
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetStrobeDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetAirConditionerDisable(disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetAirConditionerDisable(context.Background(), &hardware_manager.SetAirConditionerDisableRequest{
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetAirConditionerDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetChillerDisable(disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetChillerDisable(context.Background(), &hardware_manager.SetChillerDisableRequest{
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetChillerDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetMainContactorDisable(disable bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetMainContactorDisable(context.Background(), &hardware_manager.SetMainContactorDisableRequest{
			Disable: disable,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetMainContactorDisableResponse).Success, nil
}

func (c *HardwareManagerClient) SetJimboxSpeed(target_speed float64, ground_speed float64) (float64, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetJimboxSpeed(context.Background(), &hardware_manager.SetJimboxSpeedRequest{
			TargetSpeed:       target_speed,
			ActualGroundSpeed: ground_speed,
		})
	})
	if err != nil {
		return 0.0, err
	}
	return result.(*hardware_manager.SetJimboxSpeedResponse).SpeedSetpoint, nil
}

func (c *HardwareManagerClient) GetCruiseStatus(timeout time.Duration) (*hardware_manager.GetCruiseStatusResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetCruiseStatus(ctx, &hardware_manager.GetCruiseStatusRequest{})
	})
	if err != nil {
		return nil, err
	}
	return result.(*hardware_manager.GetCruiseStatusResponse), nil
}

func (c *HardwareManagerClient) SetCruiseEnabled(enabled bool) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetCruiseEnabled(context.Background(), &hardware_manager.SetCruiseEnabledRequest{
			Enabled: enabled,
		})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetCruiseEnabledResponse).Success, nil
}
func (c *HardwareManagerClient) SetImplementStateOnTractor(active bool, implementError bool) error {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetImplementStateOnTractor(ctx, &hardware_manager.SetImplementStateRequest{
			Active: active,
			Error:  implementError,
		})
	})
	if err != nil {
		return err
	}
	return nil
}
func (c *HardwareManagerClient) SetSafeStateEnforcement(enforced bool) error {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetSafeStateEnforcement(ctx, &hardware_manager.SetSafeStateEnforcementRequest{
			Enforced: enforced,
		})
	})
	if err != nil {
		return err
	}
	return nil
}
func (c *HardwareManagerClient) GetTractorSafetyState(timestampMs int64, ctx context.Context) (*hardware_manager.GetTractorSafetyStateResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, time.Duration(time.Millisecond*500))
	defer cancel()
	result, err := c.sendRequestLongPoll(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetTractorSafetyState(ctx, &hardware_manager.GetTractorSafetyStateRequest{
			TimestampMs: timestampMs,
		})
	})
	if err != nil {
		return nil, err
	}
	return result.(*hardware_manager.GetTractorSafetyStateResponse), nil
}
func (c *HardwareManagerClient) GetTractorIFState() (*hardware_manager.GetTractorIFStateResponse, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetTractorIFState(ctx, &hardware_manager.GetTractorIFStateRequest{})
	})
	if err != nil {
		return nil, err
	}
	return result.(*hardware_manager.GetTractorIFStateResponse), nil
}

func (c *HardwareManagerClient) CommandComputerPowerCycle() (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.CommandComputerPowerCycle(context.Background(), &hardware_manager.CommandComputerPowerCycleRequest{})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.CommandComputerPowerCycleResponse).Success, nil
}

func (c *HardwareManagerClient) SuicideSwitch() (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SuicideSwitch(context.Background(), &hardware_manager.SuicideSwitchRequest{})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SuicideSwitchResponse).Success, nil
}

func (c *HardwareManagerClient) GetRuntime() (uint32, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetRuntime(context.Background(), &hardware_manager.GetRuntimeRequest{})
	})
	if err != nil {
		return 0, err
	}
	return result.(*hardware_manager.GetRuntimeResponse).GetRuntime_240V(), nil
}

func (c *HardwareManagerClient) GetWheelEncoderResolution() (uint32, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetWheelEncoderResolution(context.Background(), &hardware_manager.GetWheelEncoderResolutionRequest{})
	})
	if err != nil {
		return 0, err
	}
	return result.(*hardware_manager.GetWheelEncoderResolutionResponse).GetResolution(), nil
}

func (c *HardwareManagerClient) SetStrobeSettings(exposure_us, period_us, targets_per_predict_ratio *uint32) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetStrobeSettings(context.Background(), &hardware_manager.StrobeSettings{ExposureUs: exposure_us, PeriodUs: period_us, TargetsPerPredictRatio: targets_per_predict_ratio})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetStrobeSettingsResponse).Success, nil
}

func (c *HardwareManagerClient) GetReaperEnclosureSensors() (*hardware_manager.ReaperCenterEnclosureData, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetReaperEnclosureSensors(ctx, &hardware_manager.GetReaperEnclosureSensorsRequest{})
	})
	if err != nil {
		return nil, err
	}
	return result.(*hardware_manager.GetReaperEnclosureSensorsResponse).GetSensors(), nil
}

func (c *HardwareManagerClient) GetReaperModuleSensors() ([]*hardware_manager.ReaperModuleSensorData, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.GetReaperModuleSensors(ctx, &hardware_manager.GetReaperModuleSensorsRequest{})
	})
	if err != nil {
		return nil, err
	}
	return result.(*hardware_manager.GetReaperModuleSensorsResponse).GetModuleSensors(), nil
}

func (c *HardwareManagerClient) SetReaperScannerPower(moduleId uint32, scannerAPower *bool, scannerBPower *bool) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperScannerPower(ctx, &hardware_manager.SetReaperScannerPowerRequest{ModuleId: moduleId, ScannerAPower: scannerAPower, ScannerBPower: scannerBPower})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperScannerPowerResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperTargetPower(moduleId uint32, targetAPower, targetBPower *bool) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperTargetPower(ctx, &hardware_manager.SetReaperTargetPowerRequest{ModuleId: moduleId, TargetAPower: targetAPower, TargetBPower: targetBPower})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperTargetPowerResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperPredictCamPower(moduleId uint32, enabled bool) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperPredictCamPower(ctx, &hardware_manager.SetReaperPredictCamPowerRequest{ModuleId: moduleId, Enabled: enabled})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperPredictCamPowerResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperStrobeConfig(moduleId *uint32, exposureUs uint32, periodUs uint32, targetsPerPredictRatio uint32) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperStrobeConfig(ctx, &hardware_manager.SetReaperStrobeConfigRequest{ModuleId: moduleId, Settings: &hardware_manager.StrobeSettings{ExposureUs: &exposureUs, PeriodUs: &periodUs, TargetsPerPredictRatio: &targetsPerPredictRatio}})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperStrobeConfigResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperStrobeEnable(moduleIds []uint32, enabled bool, durationMs *uint32) (bool, error) {
	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperStrobeEnable(context.Background(), &hardware_manager.SetReaperStrobeEnableRequest{ModuleIds: moduleIds, Enabled: enabled, DurationMs: durationMs})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperStrobeEnableResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperModulePcPower(moduleId uint32, enabled bool) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperModulePcPower(ctx, &hardware_manager.SetReaperModulePcPowerRequest{ModuleId: moduleId, Enabled: enabled})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperModulePcPowerResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperModuleLaserPower(moduleId uint32, enabled bool) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperModuleLaserPower(ctx, &hardware_manager.SetReaperModuleLaserPowerRequest{ModuleId: moduleId, Enabled: enabled})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperModuleLaserPowerResponse).Success, nil
}

func (c *HardwareManagerClient) SetReaperModuleStrobePower(moduleId uint32, enabled bool) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.SetReaperModuleStrobePower(ctx, &hardware_manager.SetReaperModuleStrobePowerRequest{ModuleId: moduleId, Enabled: enabled})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.SetReaperModuleStrobePowerResponse).Success, nil
}

func (c *HardwareManagerClient) IdentifyModule(toIdIp string, turnOffIps []string) (bool, error) {
	ctx, cancel := getDefaultHardwareManagerClientContext()
	defer cancel()

	result, err := c.sendRequest(func(client hardware_manager.HardwareManagerServiceClient) (interface{}, error) {
		return client.IdentifyModule(ctx, &hardware_manager.IdentifyModuleRequest{ToIdIp: toIdIp, TurnOffIps: turnOffIps})
	})
	if err != nil {
		return false, err
	}
	return result.(*hardware_manager.IdentifyModuleResponse).Success, nil
}

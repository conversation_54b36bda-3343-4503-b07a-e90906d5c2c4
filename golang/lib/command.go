package lib

import (
	"fmt"
	"io"
	"os/exec"
)

func RunCommand(executable string, args ...string) (string, error) {
	cmd := exec.Command(executable, args...)
	var stderr, stdout io.ReadCloser
	var err error
	if stderr, err = cmd.StderrPipe(); err != nil {
		return "", err
	}
	if stdout, err = cmd.StdoutPipe(); err != nil {
		return "", err
	}
	if err := cmd.Start(); err != nil {
		return "", err
	}
	stderrSlurp, _ := io.ReadAll(stderr)
	stdoutSlurp, _ := io.ReadAll(stdout)
	if len(stderrSlurp) > 0 {
		fmt.Printf("%s\n", stderrSlurp)
	}
	if err := cmd.Wait(); err != nil {
		return "", err
	}
	return string(stdoutSlurp), nil
}

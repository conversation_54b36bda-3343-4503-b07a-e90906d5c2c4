package robot_definition

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"math"
	"sort"
	"time"
)

const (
	RobotDefinitionStateKey   = "robot_definition/"
	CurrentRobotDefinitionKey = "current"
	redisTimeout              = 10 * time.Second
)

type RedisClient interface {
	HGetWithContext(ctx context.Context, hash string, key string) (string, error)
	HExistsWithCtx(ctx context.Context, hash string, key string) (bool, error)
}

type ModuleDefinition struct {
	ModuleId        uint32  `json:"module_id"`
	ModuleSpacingMm float32 `json:"module_spacing_mm"`
	Disabled        bool    `json:"disabled"`
	AimbotHost      bool    `json:"aimbot_host"`
}

func (m *ModuleDefinition) ToProto() *frontend.ModuleDefinition {
	if m == nil {
		return nil
	}
	return &frontend.ModuleDefinition{
		ModuleId:        m.ModuleId,
		ModuleSpacingMm: m.ModuleSpacingMm,
		Disabled:        m.Disabled,
	}
}

func (m *ModuleDefinition) FromProto(p *frontend.ModuleDefinition) {
	if p == nil {
		return
	}
	m.ModuleId = p.ModuleId
	m.ModuleSpacingMm = p.ModuleSpacingMm
	m.Disabled = p.Disabled
	m.AimbotHost = false
}

type CameraLocation struct {
	ModuleId uint32
	Slot     uint32
}

type RowDefinition struct {
	RowId        uint32              `json:"row_id"`
	Modules      []*ModuleDefinition `json:"modules"`
	RowSpacingMm float32             `json:"row_spacing_mm"`

	// NOTE: Keep these fields private such that they do not get marshalled to Redis
	cameraModuleMap  map[string]CameraLocation
	scannerModuleMap map[uint32]CameraLocation
}

func (r *RowDefinition) ToProto() *frontend.RowDefinition {
	if r == nil {
		return nil
	}
	modules := make([]*frontend.ModuleDefinition, len(r.Modules))
	for i, m := range r.Modules {
		modules[i] = m.ToProto()
	}

	return &frontend.RowDefinition{
		RowId:        r.RowId,
		Modules:      modules,
		RowSpacingMm: r.RowSpacingMm,
	}
}

func (r *RowDefinition) FromProto(p *frontend.RowDefinition) {
	if p == nil {
		return
	}
	r.RowId = p.RowId
	r.RowSpacingMm = p.RowSpacingMm
	r.Modules = make([]*ModuleDefinition, len(p.Modules))
	for i, m := range p.Modules {
		md := &ModuleDefinition{}
		md.FromProto(m)
		r.Modules[i] = md
	}
	r.populateCameraScannerMaps()
}

func (r *RowDefinition) SortByModuleID() {
	// sort by module ID
	sort.Slice(r.Modules, func(i, j int) bool {
		return r.Modules[i].ModuleId < r.Modules[j].ModuleId
	})
}

func (r *RowDefinition) AssignAimbotHost() {
	// future optimization: use the current aimbot host to determine the next aimbot host

	r.SortByModuleID()
	// assign aimbot host to the first enabled module
	for _, m := range r.Modules {
		if !m.Disabled {
			m.AimbotHost = true
			break
		}
	}
}

func (r *RowDefinition) populateCameraScannerMaps() {
	cameraMap := make(map[string]CameraLocation)
	scannerMap := make(map[uint32]CameraLocation)

	for idx, module := range r.Modules {
		if module == nil {
			continue
		}
		cameraMap[fmt.Sprintf("predict%d", idx+1)] = CameraLocation{module.ModuleId, math.MaxUint32}
		cameraMap[fmt.Sprintf("target%d", (idx*2)+1)] = CameraLocation{module.ModuleId, 0}
		cameraMap[fmt.Sprintf("target%d", (idx*2)+2)] = CameraLocation{module.ModuleId, 1}

		scannerMap[uint32((idx*2)+1)] = CameraLocation{module.ModuleId, 0}
		scannerMap[uint32((idx*2)+2)] = CameraLocation{module.ModuleId, 1}
	}

	r.cameraModuleMap = cameraMap
	r.scannerModuleMap = scannerMap
}

func (r *RowDefinition) GetModuleForCamera(cameraName string) CameraLocation {
	return r.cameraModuleMap[cameraName]
}

func (r *RowDefinition) GetModuleForScanner(sid uint32) CameraLocation {
	return r.scannerModuleMap[sid]
}

type BarDefinition struct {
	BarLengthMm uint32 `json:"bar_length_mm"`
	Folding     bool   `json:"folding"`
}

func (b *BarDefinition) ToProto() *frontend.BarDefinition {
	if b == nil {
		return nil
	}
	return &frontend.BarDefinition{
		BarLengthMm: b.BarLengthMm,
		Folding:     b.Folding,
	}
}

func (b *BarDefinition) FromProto(p *frontend.BarDefinition) {
	if p == nil {
		return
	}
	b.BarLengthMm = p.BarLengthMm
	b.Folding = p.Folding
}

type RobotDefinition struct {
	Rows          []*RowDefinition `json:"rows"`
	BarDefinition *BarDefinition   `json:"bar_definition"`
	Number        uint32           `json:"number"`
}

func (r *RobotDefinition) ToProto() *frontend.RobotDefinition {
	if r == nil {
		return nil
	}
	rows := make([]*frontend.RowDefinition, len(r.Rows))
	for i, row := range r.Rows {
		rows[i] = row.ToProto()
	}
	return &frontend.RobotDefinition{
		Rows:          rows,
		BarDefinition: r.BarDefinition.ToProto(),
	}
}

func (r *RobotDefinition) FromProto(p *frontend.RobotDefinition) {
	if p == nil {
		return
	}
	r.Rows = make([]*RowDefinition, len(p.Rows))
	for i, row := range p.Rows {
		rd := &RowDefinition{}
		rd.FromProto(row)
		r.Rows[i] = rd
	}
	r.BarDefinition = &BarDefinition{}
	r.BarDefinition.FromProto(p.BarDefinition)
	r.Number = 0
}

func (s *RobotDefinition) AssignAimbotHosts() {
	for _, row := range s.Rows {
		row.AssignAimbotHost()
	}
}

func (s *RobotDefinition) GetRow(rowId uint32) *RowDefinition {
	for _, row := range s.Rows {
		if row.RowId == rowId {
			return row
		}
	}
	return nil
}

func (s *RobotDefinition) GetRowForModule(moduleID uint32) *RowDefinition {
	for _, row := range s.Rows {
		for _, module := range row.Modules {
			if module.ModuleId == moduleID {
				return row
			}
		}
	}
	return nil
}

func (s *RobotDefinition) IsValid() bool {
	if s == nil {
		return false
	} else if s.Number == 0 {
		return false
	} else if s.BarDefinition == nil {
		return false
	} else {
		for _, row := range s.Rows {
			if row == nil {
				return false
			}
			for _, module := range row.Modules {
				if module == nil {
					return false
				}
			}
		}
	}
	return true
}

func (s *RobotDefinition) NumberOfModules() int {
	count := 0
	for _, row := range s.Rows {
		count += len(row.Modules)
	}
	return count
}

func ReadCurrentDefFromRedis(redisClient RedisClient) (*RobotDefinition, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	data, err := redisClient.HGetWithContext(ctx, RobotDefinitionStateKey, CurrentRobotDefinitionKey)
	if err != nil {
		return nil, err
	}

	def := &RobotDefinition{}
	err = json.Unmarshal([]byte(data), def)
	if err != nil {
		return nil, err
	}

	for _, row := range def.Rows {
		row.populateCameraScannerMaps()
	}

	return def, nil
}

func CurrentDefExistsInRedis(redisClient RedisClient) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), redisTimeout)
	defer cancel()

	exists, err := redisClient.HExistsWithCtx(ctx, RobotDefinitionStateKey, CurrentRobotDefinitionKey)
	if err != nil {
		return false, err
	}
	return exists, nil
}

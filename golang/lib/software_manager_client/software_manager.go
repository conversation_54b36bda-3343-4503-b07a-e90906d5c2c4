package software_manager_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/software_manager"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

const (
	defaultTimeout  = time.Duration(time.Second)
	extendedTimeout = time.Duration(time.Minute) // for calls that read from or write to disk, 1 minute might overkill but it's safe
)

type SoftwareManagerClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client software_manager.SoftwareManagerServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *SoftwareManagerClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewSoftwareManagerClient(addr string) *SoftwareManagerClient {
	client := &SoftwareManagerClient{
		addr: addr,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *SoftwareManagerClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *SoftwareManagerClient) getClient() (software_manager.SoftwareManagerServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = software_manager.NewSoftwareManagerServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *SoftwareManagerClient) sendRequest(f func(software_manager.SoftwareManagerServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

func (c *SoftwareManagerClient) GetSoftwareVersionMetadata(tag string) (*software_manager.SoftwareVersionMetadata, error) {
	ctx, cancel := context.WithTimeout(context.Background(), extendedTimeout)
	defer cancel()
	metadata, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.GetSoftwareVersionMetadata(ctx, &software_manager.SoftwareVersionMetadataRequest{
			Tag: tag,
		})
	})

	if err != nil {
		return nil, err
	}

	return metadata.(*software_manager.SoftwareVersionMetadata), err
}

func (c *SoftwareManagerClient) GetVersionsSummary() (*software_manager.VersionSummaryReply, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	summary, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.GetVersionsSummary(ctx, &software_manager.VersionSummaryRequest{})
	})

	if err != nil {
		return nil, err
	}

	return summary.(*software_manager.VersionSummaryReply), err
}

func (c *SoftwareManagerClient) PrepareUpdate(tag, reqId string) error {
	ctx, cancel := context.WithTimeout(context.Background(), extendedTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.PrepareUpdate(ctx, &software_manager.PrepareUpdateRequest{
			Tag:   tag,
			ReqId: reqId,
		})
	})

	return err
}

func (c *SoftwareManagerClient) AbortUpdate(tag, reqId string) error {
	ctx, cancel := context.WithTimeout(context.Background(), extendedTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.AbortUpdate(ctx, &software_manager.AbortUpdateRequest{
			Tag:   tag,
			ReqId: reqId,
		})
	})

	return err
}

func (c *SoftwareManagerClient) TriggerUpdate(tag, reqId string) error {
	ctx, cancel := context.WithTimeout(context.Background(), extendedTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.TriggerUpdate(ctx, &software_manager.TriggerUpdateRequest{
			Tag:   tag,
			ReqId: reqId,
		})
	})

	return err
}

func (c *SoftwareManagerClient) Reboot() error {
	ctx, cancel := context.WithTimeout(context.Background(), extendedTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.Reboot(ctx, &software_manager.Empty{})
	})

	return err
}

func (c *SoftwareManagerClient) GetIdentity() (*software_manager.IdentityInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	identity, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.GetIdentity(ctx, &software_manager.GetIdentityRequest{})
	})

	if err != nil {
		return nil, err
	}

	return identity.(*software_manager.IdentityInfo), err
}

func (c *SoftwareManagerClient) ClearPackagesCache() error {
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.ClearPackagesCache(context.Background(), &software_manager.ClearPackagesCacheRequest{})
	})

	return err
}

func (c *SoftwareManagerClient) PushRobotDefinitionUpdate() error {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.PushRobotDefinitionUpdate(ctx, &software_manager.Empty{})
	})

	return err
}

func (c *SoftwareManagerClient) RestartDependentServices() error {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client software_manager.SoftwareManagerServiceClient) (interface{}, error) {
		return client.RestartDependentServices(ctx, &software_manager.Empty{})
	})

	return err
}

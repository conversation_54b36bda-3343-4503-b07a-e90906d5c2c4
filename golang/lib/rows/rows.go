package rows

import (
	"github.com/carbonrobotics/robot/golang/lib/aimbot_client"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	"github.com/carbonrobotics/robot/golang/lib/weed_tracking_client"
)

const secondaryMiniPCIdxOffset uint32 = 1000

type RowClients struct {
	AimbotClient         *aimbot_client.AimbotClient
	CVRuntimeClients     map[uint32]*cv_runtime_client.CVRuntimeClient
	ModelReceiverClients map[uint32]*model_manager.ModelReceiverClient
	WeedTrackingClient   *weed_tracking_client.WeedTrackingClient
	StreamHosts          map[uint32]string
	StreamHostNames      map[uint32]string
	StreamPort           uint32
	PrimaryPCId          uint32
	SecondaryPCIds       map[uint32]struct{}
}

func GetMiniPCSecondaryId(primaryId uint32) uint32 {
	return primaryId + secondaryMiniPCIdxOffset
}

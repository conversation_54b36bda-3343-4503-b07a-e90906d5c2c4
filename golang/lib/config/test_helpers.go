package config

// Tree is an abstraction wrapper for the *ConfigTree so we can mock internals for testing.
type Tree interface {
	GetName() string
	GetNode(key string) Tree
	GetStringValue() string
	GetBoolValue() bool
	RegisterCallback(callback func()) uint32
	HasNode(key string) bool
	GetChildrenNodes() []Tree
	GetFloatValue() float64
	GetUIntValue() uint64
	GetIntValue() int64
}

type TreeWrap struct {
	*ConfigTree
}

func WrapConfigTree(tree *ConfigTree) Tree {
	if tree == nil {
		return nil
	}
	return &TreeWrap{tree}
}

func (cw *TreeWrap) GetNode(key string) Tree {
	node := cw.ConfigTree.GetNode(key)
	if node == nil {
		return nil
	}
	return &TreeWrap{node}
}

func (cw *TreeWrap) GetChildrenNodes() []Tree {
	wrappedChildren := make([]Tree, 0)
	for _, child := range cw.ConfigTree.GetChildrenNodes() {
		wrappedChildren = append(wrappedChildren, WrapConfigTree(child))
	}
	return wrappedChildren
}

var PreDeleteConfigTree = func(node any) {
	switch tree := node.(type) {
	case *ConfigTree:
		preDeleteConfigTree(tree)
	case *TreeWrap:
		if tree.ConfigTree != nil {
			preDeleteConfigTree(tree.ConfigTree)
		}
	}
}

var FeatureFlagEnabledIfExists = func(commonNode any, feature string) bool {
	switch node := commonNode.(type) {
	case *ConfigTree:
		return featureFlagEnabledIfExists(node, feature)
	case *TreeWrap:
		if node.ConfigTree != nil {
			return featureFlagEnabledIfExists(node.ConfigTree, feature)
		}
	}
	return false
}

type MockConfigTree struct {
	Name     string
	Value    any
	Data     map[string]any
	SubTrees map[string]MockConfigTree
	Callback func()
}

func (m MockConfigTree) GetName() string {
	return m.Name
}

func (m MockConfigTree) GetNode(key string) Tree {
	if val, ok := m.SubTrees[key]; ok {
		return val
	}
	return nil
}

func (m MockConfigTree) GetStringValue() string {
	return m.Value.(string)
}

func (m MockConfigTree) GetBoolValue() bool {
	return m.Value.(bool)
}

func (m MockConfigTree) RegisterCallback(callback func()) uint32 {
	// ** NO-OP **
	return 0
}

func (m MockConfigTree) HasNode(key string) bool {
	_, ok := m.SubTrees[key]
	return ok
}

func (m MockConfigTree) GetChildrenNodes() []Tree {
	children := make([]Tree, 0)
	for _, child := range m.SubTrees {
		children = append(children, child)
	}
	return children
}

func (m MockConfigTree) GetFloatValue() float64 {
	return m.Value.(float64)
}

func (m MockConfigTree) GetUIntValue() uint64 {
	return m.Value.(uint64)
}

func (m MockConfigTree) GetIntValue() int64 {
	return m.Value.(int64)
}

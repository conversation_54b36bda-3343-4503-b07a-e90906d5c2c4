package config

import (
	"context"
	"fmt"
	"runtime"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/config_service"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type ConfigClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client config_service.ConfigServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *ConfigClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewConfigClient(addr string) *ConfigClient {
	client := &ConfigClient{
		addr: addr,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *ConfigClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *ConfigClient) getClient() (config_service.ConfigServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = config_service.NewConfigServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *ConfigClient) sendRequest(f func(config_service.ConfigServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

func (c *ConfigClient) setValueInternal(req *config_service.SetValueRequest) error {
	_, err := c.sendRequest(func(client config_service.ConfigServiceClient) (interface{}, error) {
		return client.SetValue(context.Background(), req)
	})
	return err
}

func (c *ConfigClient) GetTree() (*config_service.GetTreeResponse, error) {
	response, err := c.sendRequest(func(client config_service.ConfigServiceClient) (interface{}, error) {
		return client.GetTree(context.Background(), &config_service.GetTreeRequest{})
	})
	tree := response.(*config_service.GetTreeResponse)
	return tree, err
}

func (c *ConfigClient) GetNode(key string) (*config_service.GetTreeResponse, error) {
	response, err := c.sendRequest(func(client config_service.ConfigServiceClient) (interface{}, error) {
		return client.GetTree(context.Background(), &config_service.GetTreeRequest{Key: key})
	})
	tree := response.(*config_service.GetTreeResponse)
	return tree, err
}

func (c *ConfigClient) GetNodeSafe(key string) (*config_service.GetTreeResponse, error) {
	response, err := c.sendRequest(func(client config_service.ConfigServiceClient) (interface{}, error) {
		return client.GetTree(context.Background(), &config_service.GetTreeRequest{Key: key})
	})
	if response == nil {
		return nil, fmt.Errorf("Node not found: %v", key)
	}
	tree := response.(*config_service.GetTreeResponse)
	return tree, err
}

func (c *ConfigClient) SetStringValue(key string, val string) error {
	value := &config_service.SetValueRequest{
		Key: key, Value: &config_service.ConfigValue{Value: &config_service.ConfigValue_StringVal{StringVal: val}},
	}
	err := c.setValueInternal(value)
	return err
}

func (c *ConfigClient) SetIntValue(key string, val int64) error {
	value := &config_service.SetValueRequest{
		Key: key, Value: &config_service.ConfigValue{Value: &config_service.ConfigValue_Int64Val{Int64Val: val}},
	}
	err := c.setValueInternal(value)
	return err
}

func (c *ConfigClient) SetUIntValue(key string, val uint64) error {
	value := &config_service.SetValueRequest{
		Key: key, Value: &config_service.ConfigValue{Value: &config_service.ConfigValue_Uint64Val{Uint64Val: val}},
	}
	err := c.setValueInternal(value)
	return err
}

func (c *ConfigClient) SetDoubleValue(key string, val float64) error {
	value := &config_service.SetValueRequest{
		Key: key, Value: &config_service.ConfigValue{Value: &config_service.ConfigValue_FloatVal{FloatVal: val}},
	}
	err := c.setValueInternal(value)
	return err
}

func (c *ConfigClient) SetBoolValue(key string, val bool) error {
	value := &config_service.SetValueRequest{
		Key: key, Value: &config_service.ConfigValue{Value: &config_service.ConfigValue_BoolVal{BoolVal: val}},
	}
	err := c.setValueInternal(value)
	return err
}

func (c *ConfigClient) AddToList(key string, name string) error {
	req := &config_service.AddToListRequest{
		Key:  key,
		Name: name,
	}
	_, err := c.sendRequest(func(client config_service.ConfigServiceClient) (interface{}, error) {
		return client.AddToList(context.Background(), req)
	})
	return err
}

func (c *ConfigClient) RemoveFromList(key string, name string) error {
	req := &config_service.RemoveFromListRequest{
		Key:  key,
		Name: name,
	}
	_, err := c.sendRequest(func(client config_service.ConfigServiceClient) (interface{}, error) {
		return client.RemoveFromList(context.Background(), req)
	})
	return err
}

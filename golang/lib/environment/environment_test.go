package environment

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRobot_GetHost(t *testing.T) {
	tests := []struct {
		name         string
		MakaGen      string
		MakaRole     string
		MakaRow      string
		expectedHost string
	}{
		{
			"bud",
			"bud",
			"bud",
			"foo",
			"bud",
		},
		{
			"slayer command",
			"slayer",
			"command",
			"foo",
			"command",
		},
		{
			"slayer row",
			"slayer",
			"row",
			"1",
			"row1",
		},
		{
			"slayer primary row",
			"slayer",
			"row-primary",
			"1",
			"row-primary1",
		},
		{
			"slayer secondary row",
			"slayer",
			"row-secondary",
			"1",
			"row-secondary1",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			robot := &Robot{
				MakaGen:  test.MakaGen,
				MakaRole: test.MakaRole,
				MakaRow:  test.MakaRow,
			}
			got := robot.GetHost()
			assert.Equal(t, test.expectedHost, got)
		})
	}
}

func TestRobot_IsProduction(t *testing.T) {
	tests := []struct {
		name             string
		carbonMode       string
		expectProduction bool
	}{
		{
			"prod mode",
			CarbonModeProd,
			true,
		},
		{
			"user mode",
			CarbonModeUser,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			robot := Robot{CarbonMode: test.carbonMode}
			got := robot.IsProduction()
			assert.Equal(t, test.expectProduction, got)
		})
	}
}

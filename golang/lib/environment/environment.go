package environment

import (
	"fmt"
	"strings"

	"github.com/kelseyhightower/envconfig"
)

type CarbonMode string

const (
	CarbonModeProd = "prod"
	CarbonModeUser = "user"
)

type CarbonRole string

const (
	CarbonRoleBud                    CarbonRole = "bud"
	CarbonRoleCommand                CarbonRole = "command"
	CarbonRoleRow                    CarbonRole = "row"
	CarbonRoleModule                 CarbonRole = "module"
	CarbonRoleRowPrimary             CarbonRole = "row-primary"
	CarbonRoleRowSecondary           CarbonRole = "row-secondary"
	CarbonRoleSimulator              CarbonRole = "simulator"
	CarbonRoleSimulatorMinicomputers CarbonRole = "simulator_minicomputers"
	CarbonRoleRtc                    CarbonRole = "rtc"

	UnsetCarbonModuleID     = "0"
	UnsetCarbonModuleSerial = ""

	HTTPHeaderPrefix     = "X-CARBON"
	RobotHTTPHeader      = HTTPHeaderPrefix + "-robot"
	GenerationHTTPHeader = HTTPHeaderPrefix + "-generation"
)

type CarbonGen string

const (
	CarbonGenBud    CarbonGen = "bud"
	CarbonGenSlayer CarbonGen = "slayer"
	CarbonGenReaper CarbonGen = "reaper"
	CarbonGenRtc    CarbonGen = "rtc"
)

type Robot struct {
	MakaCalibrationDir        string   `envconfig:"MAKA_CALIBRATION_DIR"`
	MakaConfigDir             string   `envconfig:"MAKA_CONFIG_DIR"`
	MakaDataDir               string   `envconfig:"MAKA_DATA_DIR"`
	MakaLogDir                string   `envconfig:"MAKA_LOG_DIR"`
	MakaModelDir              string   `envconfig:"MAKA_MODEL_DIR"`
	MakaRobotDir              string   `envconfig:"MAKA_ROBOT_DIR"`
	MakaBotDir                string   `envconfig:"MAKA_BOT_DIR"`
	MakaMediaDir              string   `envconfig:"MAKA_MEDIA_DIR"`
	MakaBotConfigDir          string   `envconfig:"MAKA_BOT_CONFIG_DIR"`
	MakaRobotName             string   `envconfig:"MAKA_ROBOT_NAME"`
	MakaDockerTag             string   `envconfig:"MAKA_DOCKER_TAG"`
	MakaRole                  string   `envconfig:"MAKA_ROLE"`
	MakaRow                   string   `envconfig:"MAKA_ROW"`
	MakaGen                   string   `envconfig:"MAKA_GEN"`
	MakaAuthClientID          string   `envconfig:"MAKA_AUTH_CLIENT_ID"`
	MakaAuthClientSecret      string   `envconfig:"MAKA_AUTH_CLIENT_SECRET"`
	MakaAuthScopes            []string `envconfig:"MAKA_AUTH_SCOPES"`
	MakaAuthDomain            string   `envconfig:"MAKA_AUTH_DOMAIN"`
	CarbonUser                string   `envconfig:"CARBON_USER"`
	CarbonMode                string   `envconfig:"CARBON_MODE"`
	CarbonVersionTag          string   `envconfig:"CARBON_VERSION_TAG"`
	CarbonIDMHost             string   `envconfig:"CARBON_IDM_HOST"`
	CarbonIngestHost          string   `envconfig:"CARBON_INGEST_HOST"`
	CarbonPortalHost          string   `envconfig:"CARBON_PORTAL_HOST"`
	CarbonConfigCloudHost     string   `envconfig:"CARBON_CONFIG_CLOUD_HOST"`
	CarbonConfigCloudPort     string   `envconfig:"CARBON_CONFIG_CLOUD_PORT"`
	CarbonVeselkaHost         string   `envconfig:"CARBON_VESELKA_HOST"`
	CarbonVersionMetadataHost string   `envconfig:"CARBON_VERSION_METADATA_HOST"`
	CarbonRobotUsername       string   `envconfig:"CARBON_ROBOT_USERNAME"`
	CarbonRobotPassword       string   `envconfig:"CARBON_ROBOT_PASSWORD"`
	CarbonModuleID            string   `envconfig:"CARBON_MODULE_ID"`
	CarbonModuleSerial        string   `envconfig:"CARBON_MODULE_SERIAL"`
}

var robotGlobal Robot

func init() {
	err := envconfig.Process("", &robotGlobal)
	if err != nil {
		panic(err)
	}
}

func (r *Robot) IsUnmanagedSystem() bool {
	return r.IsSimulator() || r.IsRtc() // RTC is unmanaged until we settle on production HW
}

func (r *Robot) GetHost() string {
	if r.IsBud() {
		return "bud"
	}

	role := r.GetCarbonRole()
	if r.IsSlayer() {
		switch {
		case r.IsCommand():
			return "command"
		case r.IsRow():
			return fmt.Sprint(role, r.MakaRow)
		}
	}

	if r.IsReaper() {
		if r.IsCommand() {
			return "command"
		}
		if r.IsModule() {
			return fmt.Sprint("module", r.CarbonModuleID)
		}
	}

	return "invalid"
}

func (r *Robot) IsProduction() bool {
	return r.CarbonMode == CarbonModeProd
}

func (r *Robot) GetCarbonRole() CarbonRole {
	return CarbonRole(r.MakaRole)
}

func (r *Robot) IsSimulator() bool {
	return CarbonRoleSimulator == r.GetCarbonRole() || CarbonRoleSimulatorMinicomputers == r.GetCarbonRole()
}
func (r *Robot) GetCarbonGen() CarbonGen {
	return CarbonGen(r.MakaGen)
}

func (r *Robot) IsBud() bool {
	return CarbonGenBud == r.GetCarbonGen()
}

func (r *Robot) IsSlayer() bool {
	return CarbonGenSlayer == r.GetCarbonGen()
}

func (r *Robot) IsReaper() bool {
	return CarbonGenReaper == r.GetCarbonGen()
}

func (r *Robot) IsRtc() bool {
	return CarbonGenRtc == r.GetCarbonGen()
}
func (r *Robot) IsCommand() bool {
	return CarbonRoleCommand == r.GetCarbonRole()
}

func (r *Robot) IsModule() bool {
	return CarbonRoleModule == r.GetCarbonRole()
}
func (r *Robot) IsRow() bool {
	return strings.HasPrefix(string(r.GetCarbonRole()), "row")
}

func (r *Robot) IsNumericRole() bool {
	return r.IsRow() || r.IsModule()
}

func GetRobot() (Robot, error) {
	env := Robot{}
	err := envconfig.Process("", &env)
	return env, err
}

func GetCarbonGen() CarbonGen {
	return robotGlobal.GetCarbonGen()
}

func IsBud() bool {
	return robotGlobal.IsBud()
}

func IsSlayer() bool {
	return robotGlobal.IsSlayer()
}

func IsReaper() bool {
	return robotGlobal.IsReaper()
}

func IsRtc() bool {
	return robotGlobal.IsRtc()
}

func GetCarbonRole() CarbonRole {
	return robotGlobal.GetCarbonRole()
}

func IsCommand() bool {
	return robotGlobal.IsCommand()
}

func IsModule() bool {
	return robotGlobal.IsModule()
}
func IsRow() bool {
	return robotGlobal.IsRow()
}

func IsNumericRole() bool {
	return robotGlobal.IsNumericRole()
}

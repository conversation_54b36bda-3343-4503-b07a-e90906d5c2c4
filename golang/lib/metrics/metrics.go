package metrics

import (
	"fmt"
	"net/http"

	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// Serve is a wrapper to start a simple metrics server that will serve the default registry of prometheus metrics
func Serve(route string, port int) {
	if route == "" {
		route = "/metrics"
	}
	http.Handle(route, promhttp.Handler())
	go http.ListenAndServe(fmt.Sprintf("0.0.0.0:%d", port), nil)
}

func BoolToFloat(b bool) float64 {
	if b {
		return 1
	}
	return 0
}

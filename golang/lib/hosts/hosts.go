package hosts

import (
	"github.com/carbonrobotics/robot/golang/lib/host_client"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
)

type HostClients struct {
	Row                   uint32 // the row this host is in
	PcId                  uint32 // pc id is unique for each host
	IpAddress             string
	HostClient            *host_client.HostClient
	SoftwareManagerClient *software_manager_client.SoftwareManagerClient
	IsSecondary           bool // used for slayer minicomputers
}

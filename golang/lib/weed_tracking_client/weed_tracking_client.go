package weed_tracking_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

type WeedTrackingClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client weed_tracking.WeedTrackingServiceClient
	opts   []grpc.DialOption
	logger *logrus.Entry
}

func destroyConnection(c *WeedTrackingClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewWeedTrackingClient(addr string) *WeedTrackingClient {
	client := &WeedTrackingClient{
		addr:   addr,
		logger: logrus.New().WithFields(logrus.Fields{"module": "WeedTrackingClient"}),
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}
func (c *WeedTrackingClient) Copy() *WeedTrackingClient {
	return NewWeedTrackingClient(c.addr)
}

func (c *WeedTrackingClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.conn.ResetConnectBackoff()
}

func getDefaultWeedTrackingClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
}

func (c *WeedTrackingClient) getClient() (weed_tracking.WeedTrackingServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
		defer cancel()
		conn, err := grpc.DialContext(ctx, c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = weed_tracking.NewWeedTrackingServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *WeedTrackingClient) sendRequest(f func(weed_tracking.WeedTrackingServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.logger.Warningf("Resetting connection due to unavailable error.")
				c.resetConnection()
			case codes.DeadlineExceeded:
				c.logger.Warningf("Resetting connection due to deadline exceeded error.")
				c.resetConnection()
			default:
			}
		}
		return nil, err
	}

	return result, nil
}

func (c *WeedTrackingClient) GetDetections(camId string, ts int64) (*weed_tracking.GetDetectionsResponse, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetDetections(ctx, &weed_tracking.GetDetectionsRequest{
			CamId:       camId,
			TimestampMs: ts,
		})
	})

	if err != nil {
		return nil, err
	}

	return result.(*weed_tracking.GetDetectionsResponse), nil
}

func (c *WeedTrackingClient) UpdateBands() error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.UpdateBands(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *WeedTrackingClient) StartRecordingDiagnostics(ttl_sec uint32, cropImagesPerSec float32, weedImagesPerSec float32) error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.StartRecordingDiagnostics(ctx, &weed_tracking.RecordDiagnosticsRequest{
			TtlSec: ttl_sec, CropImagesPerSec: cropImagesPerSec, WeedImagesPerSec: weedImagesPerSec,
		})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *WeedTrackingClient) RemoveRecordingsDirectory() error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.RemoveRecordingsDirectory(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *WeedTrackingClient) StartSavingCropLineDetectionReplay(req *weed_tracking.StartSavingCropLineDetectionReplayRequest) error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.StartSavingCropLineDetectionReplay(ctx, req)
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *WeedTrackingClient) StartRecordingAimbotInputs(req *weed_tracking.RecordAimbotInputRequest) error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.StartRecordingAimbotInputs(ctx, req)
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *WeedTrackingClient) GetCurrentTrajectories() (*weed_tracking.DiagnosticsSnapshot, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetCurrentTrajectories(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return nil, err
	}
	return result.(*weed_tracking.DiagnosticsSnapshot), nil
}

func (c *WeedTrackingClient) GetConclusionCounter() (*weed_tracking.ConclusionCounter, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetConclusionCounter(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return nil, err
	}
	return response.(*weed_tracking.ConclusionCounter), nil
}

func (c *WeedTrackingClient) GetDiagnosticsRecordingStatus() (*weed_tracking.GetRecordingStatusResponse, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetDiagnosticsRecordingStatus(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return nil, err
	}
	return response.(*weed_tracking.GetRecordingStatusResponse), nil
}

func (c *WeedTrackingClient) GetBands() (*weed_tracking.BandDefinitions, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetBands(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return nil, err
	}
	return response.(*weed_tracking.BandDefinitions), nil
}

func (c *WeedTrackingClient) StartPlantCaptcha() error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.StartPlantCaptcha(ctx, &weed_tracking.Empty{})
	})

	return err
}

func (c *WeedTrackingClient) GetPlantCaptchaStatus() (*weed_tracking.PlantCaptchaStatusResponse, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	resp, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetPlantCaptchaStatus(ctx, &weed_tracking.Empty{})
	})

	if err != nil {
		return nil, err
	}

	return resp.(*weed_tracking.PlantCaptchaStatusResponse), nil
}

func (c *WeedTrackingClient) RemovePlantCaptchaDirectory() error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.RemovePlantCaptchaDirectory(ctx, &weed_tracking.Empty{})
	})

	return err
}

func (c *WeedTrackingClient) CancelPlantCaptcha() error {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.CancelPlantCaptcha(ctx, &weed_tracking.Empty{})
	})

	return err
}

func (c *WeedTrackingClient) GetTargetingEnabled() (*weed_tracking.GetTargetingEnabledResponse, error) {
	ctx, cancel := getDefaultWeedTrackingClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetTargetingEnabled(ctx, &weed_tracking.GetTargetingEnabledRequest{})
	})

	if err != nil {
		return nil, err
	}
	return response.(*weed_tracking.GetTargetingEnabledResponse), nil
}

func (c *WeedTrackingClient) GetBooted() (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 1000*time.Millisecond)
	defer cancel()
	response, err := c.sendRequest(func(client weed_tracking.WeedTrackingServiceClient) (interface{}, error) {
		return client.GetBooted(ctx, &weed_tracking.GetBootedRequest{})
	})

	if err != nil {
		return false, err
	}
	return response.(*weed_tracking.GetBootedResponse).Booted, nil
}

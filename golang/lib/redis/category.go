package redis

import (
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/category"
	"google.golang.org/protobuf/proto"
)

func (c *Client) LoadCategoryCollectionConfig(id string) (*category.CategoryCollection, error) {
	config, err := c.HGet(CategoryCollectionCfgs, id)
	if err != nil || config == "" {
		return nil, fmt.Errorf("failed to find category collection id: %v error: %w", id, err)
	}
	conf := &category.CategoryCollection{}
	err = proto.Unmarshal([]byte(config), conf)
	if err != nil {
		return nil, fmt.Errorf("failed to parse category collection config id: %v error: %w", id, err)
	}
	return conf, nil
}

func (c *Client) LoadCategoryConfig(id string) (*category.Category, error) {
	config, err := c.HGet(CategoryCfgs, id)
	if err != nil || config == "" {
		return nil, fmt.Errorf("failed to find category id: %v error: %w", id, err)
	}
	conf := &category.Category{}
	err = proto.Unmarshal([]byte(config), conf)
	if err != nil {
		return nil, fmt.Errorf("failed to parse category config id: %v error: %w", id, err)
	}
	return conf, nil
}

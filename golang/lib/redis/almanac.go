package redis

import (
	"fmt"

	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	"google.golang.org/protobuf/proto"
)

func (c *Client) ReadActiveAlmanacId() (string, error) {
	return c.ReadString(AlmanacActive, "")
}

func (c *Client) LoadAlmanacConfig(id string) (*almanac.AlmanacConfig, error) {
	config, err := c.HGet(AlmanacCfgs, id)
	if err != nil || config == "" {
		return nil, fmt.Errorf("failed to find almanac id: %v error: %w", id, err)
	}
	conf := &almanac.AlmanacConfig{}
	err = proto.Unmarshal([]byte(config), conf)
	if err != nil {
		return nil, fmt.Errorf("failed to parse almanac config id: %v error: %w", id, err)
	}
	return conf, nil
}

func (c *Client) FetchModelinatorConfigById(id string) (*almanac.ModelinatorConfig, error) {
	data, err := c.HGet(ModelinatorCfgs, id)
	if err != nil {
		return nil, err
	}
	conf := &almanac.ModelinatorConfig{}
	if err := proto.Unmarshal([]byte(data), conf); err != nil {
		return nil, fmt.Errorf("failed to unmarshal modelinator config id: %v error: %w", id, err)
	}
	return conf, nil
}

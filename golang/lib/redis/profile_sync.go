package redis

import (
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/golang/protobuf/jsonpb"
)

// Returns nil, nil when the key does not exist
func (c *Client) ReadProfileSyncData(uuid string) (*frontend.ProfileSyncData, error) {
	exists, err := c.HExists(ProfileSyncItems, uuid)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, nil
	}
	profileSyncDataString, err := c.HGet(ProfileSyncItems, uuid)
	if err != nil {
		return nil, err
	}
	data := &frontend.ProfileSyncData{}
	err = jsonpb.UnmarshalString(profileSyncDataString, data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

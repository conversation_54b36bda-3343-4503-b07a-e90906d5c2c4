package redis

const (
	// jobs
	JobDefsRedisKey      = "jobs/job_defs"
	JobActiveIDRedisKey  = "jobs/active_job"
	JobSyncTimesRedisKey = "jobs/sync_times"
	JobConfigsRedisKey   = "jobs/config_dumps"

	// almanac & discriminator
	AlmanacActive       = "/almanac/almanac/active"
	AlmanacCfgs         = "/almanac/almanac/config"
	ModelinatorCfgs     = "/almanac/modelinator/config"
	ActiveCropId        = "/almanac/active_crop_id"
	ActiveModelId       = "/almanac/active_model_id"
	DiscriminatorCfgs   = "/almanac/discriminator/config"
	DiscriminatorActive = "/almanac/discriminator/active"

	// point categories
	PointCategoryIds  = "/point_categories/ids"  // UUID -> Name for fast lookup
	PointCategoryDefs = "/point_categories/defs" // UUID -> JSON for complete definition

	// categories
	CategoryCollectionActive = "/category_collection/active"
	CategoryCollectionCfgs   = "/category_collection/config"
	CategoryCfgs             = "/category/config"

	// profile sync
	ProfileSyncItems = "profile_sync/items"
)

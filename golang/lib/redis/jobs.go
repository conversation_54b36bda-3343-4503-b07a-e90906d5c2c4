package redis

import (
	"encoding/json"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
)

func (c *Client) WriteJob(job *frontend.Job) error {
	byt, err := json.Marshal(job)
	if err != nil {
		return err
	}

	err = c.HSet(JobDefsRedisKey, job.JobDescription.JobId, string(byt))
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) ReadActiveJobId() (string, error) {
	return c.ReadString(JobActiveIDRedisKey, "")
}

func (c *Client) ReadJob(id string) (*frontend.Job, error) {
	jsonJob, err := c.HGet(JobDefsRedisKey, id)
	if err != nil {
		return nil, err
	}

	j := &frontend.Job{}
	if err := json.Unmarshal([]byte(jsonJob), j); err != nil {
		return nil, err
	}
	return j, nil
}

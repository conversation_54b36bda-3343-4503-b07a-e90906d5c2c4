package redis

import (
	"context"
	"strconv"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/auth"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

const (
	strTrue  = "true"
	strFalse = "false"

	Infinity    = "inf"
	NegInfinity = "-inf"
)

type Client struct {
	redisClient *redis.Client
}

func New(env environment.Robot) *Client {
	var addr string
	if environment.IsCommand() || env.MakaGen == "bud" {
		addr = "127.0.0.1:6379"
	} else {
		addr = "10.10.3.1:6379"
	}

	cl := &Client{
		redisClient: redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: auth.RedisPassword,
			DB:       0,
		}),
	}
	cl.WaitUntilReady()
	return cl
}

func NewLocalClient() *Client {
	cl := &Client{
		redisClient: redis.NewClient(&redis.Options{
			Addr:     "127.0.0.1:6379",
			Password: auth.RedisPassword,
			DB:       0,
		}),
	}
	cl.WaitUntilReady()
	return cl
}

func (c *Client) Keys(pattern string) ([]string, error) {
	return c.redisClient.Keys(context.Background(), pattern).Result()
}

func (c *Client) KeysWithContext(ctx context.Context, pattern string) ([]string, error) {
	return c.redisClient.Keys(ctx, pattern).Result()
}

func (c *Client) Get(key string) (string, error) {
	str, err := c.redisClient.Get(context.Background(), key).Result()
	if err != nil {
		return "", err
	}
	return str, nil
}

func (c *Client) SetWithContext(ctx context.Context, key, val string, ex time.Duration) error {
	return c.redisClient.Set(ctx, key, val, ex).Err()
}

func (c *Client) GetWithContext(ctx context.Context, key string) (string, error) {
	return c.redisClient.Get(ctx, key).Result()
}

func (c *Client) Del(key string) error {
	_, err := c.redisClient.Del(context.Background(), key).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) DelWithContext(ctx context.Context, key string) error {
	_, err := c.redisClient.Del(ctx, key).Result()
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) ReadString(key string, def string) (string, error) {
	str, err := c.Get(key)
	if err != nil {
		// dont log if key not found
		if err != redis.Nil {
			logrus.WithError(err).Errorf("Could not read %v from redis", key)
			return def, err
		}
		return def, nil
	}
	return str, nil
}

func (c *Client) ReadStringWithContext(ctx context.Context, key string, def string) (string, error) {
	str, err := c.GetWithContext(ctx, key)
	if err != nil {
		// dont log if key not found
		if err != redis.Nil {
			logrus.WithError(err).Errorf("Could not read %v from redis", key)
			return def, err
		}
		return def, nil
	}
	return str, nil
}

func (c *Client) ReadBool(key string, def bool) (bool, error) {
	str, err := c.ReadString(key, "")
	if err != nil {
		return def, err
	}

	return str == strTrue, nil
}

func (c *Client) ReadFloat(key string, def float64) (float64, error) {
	str, err := c.ReadString(key, "")
	if err != nil {
		return def, err
	}

	f, _ := strconv.ParseFloat(str, 64)
	return f, nil
}

func (c *Client) ReadInt64(key string, def int64) (int64, error) {
	str, err := c.ReadString(key, "")
	if err != nil {
		return def, err
	}

	f, _ := strconv.ParseInt(str, 10, 64)
	return f, nil
}

func (c *Client) ReadInt64WithContext(ctx context.Context, key string, def int64) (int64, error) {
	str, err := c.ReadStringWithContext(ctx, key, "")
	if err != nil {
		return def, err
	}

	f, _ := strconv.ParseInt(str, 10, 64)
	return f, nil
}

func (c *Client) WriteString(key string, val string) error {
	err := c.redisClient.Set(context.Background(), key, val, 0).Err()
	if err != nil {
		logrus.WithError(err).Errorf("Could not write %v to redis", key)
		return err
	}
	return nil
}

func (c *Client) WriteStringWithContext(ctx context.Context, key string, val string) error {
	err := c.redisClient.Set(context.Background(), key, val, 0).Err()
	if err != nil {
		logrus.WithError(err).Errorf("Could not write %v to redis", key)
		return err
	}
	return nil
}

func (c *Client) WriteBool(key string, b bool) error {
	var value string
	if b {
		value = strTrue
	} else {
		value = strFalse
	}
	return c.WriteString(key, value)
}

func (c *Client) WriteFloat(key string, f float64) error {
	return c.WriteString(key, strconv.FormatFloat(f, 'e', 5, 64))
}

func (c *Client) WriteInt64(key string, i int64) error {
	return c.WriteString(key, strconv.FormatInt(i, 10))
}

func (c *Client) WriteInt64WithContext(ctx context.Context, key string, i int64) error {
	return c.WriteStringWithContext(ctx, key, strconv.FormatInt(i, 10))
}

func (c *Client) HSet(hash string, key string, val string) error {
	return c.HSetWithContext(context.Background(), hash, key, val)
}

func (c *Client) HSetWithContext(ctx context.Context, hash string, val ...any) error {
	err := c.redisClient.HSet(ctx, hash, val...).Err()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.HSet for hash %v - %v", hash, val)
		return err
	}
	return nil
}

func (c *Client) HDel(key string, member string) error {
	return c.HDelWithContext(context.Background(), key, member)
}

func (c *Client) HDelWithContext(ctx context.Context, key string, member ...string) error {
	_, err := c.redisClient.HDel(ctx, key, member...).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.HDel for key %v member %v", key, member)
		return err
	}
	return nil
}

func (c *Client) HGetAll(hash string) (map[string]string, error) {
	m, err := c.redisClient.HGetAll(context.Background(), hash).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.HGetAll for hash %v", hash)
		return nil, err
	}
	return m, nil
}

func (c *Client) HGetAllWithContext(ctx context.Context, hash string) (map[string]string, error) {
	m, err := c.redisClient.HGetAll(ctx, hash).Result()
	if err != nil {
		return nil, err
	}
	return m, nil
}

func (c *Client) HGet(hash string, key string) (string, error) {
	return c.HGetWithContext(context.Background(), hash, key)
}

func (c *Client) HGetWithContext(ctx context.Context, hash string, key string) (string, error) {
	str, err := c.redisClient.HGet(ctx, hash, key).Result()
	if err != nil {
		return "", err
	}
	return str, nil
}

func (c *Client) HReadStringSafe(hash string, key string, def string) (string, error) {
	str, err := c.redisClient.HGet(context.Background(), hash, key).Result()
	if err != nil {
		if err != redis.Nil {
			logrus.WithError(err).Errorf("Could not read %v: %v from redis", hash, key)
			return def, err
		}
		return def, nil
	}
	return str, nil
}

func (c *Client) HReadInt64(hash string, key string, def int64) (int64, error) {
	str, err := c.HGet(hash, key)
	if err != nil {
		return def, err
	}

	f, _ := strconv.ParseInt(str, 10, 64)
	return f, nil
}
func (c *Client) HReadFloat64(hash string, key string, def float64) (float64, error) {
	str, err := c.HGet(hash, key)
	if err != nil {
		return def, err
	}
	if len(str) == 0 {
		return def, nil
	}

	f, _ := strconv.ParseFloat(str, 64)
	return f, nil
}

func (c *Client) HExists(hash string, key string) (bool, error) {
	b, err := c.redisClient.HExists(context.Background(), hash, key).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.HExists for hash %v key %v", hash, key)
		return false, err
	}

	return b, nil
}

func (c *Client) HExistsWithCtx(ctx context.Context, hash string, key string) (bool, error) {
	b, err := c.redisClient.HExists(ctx, hash, key).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.HExists for hash %v key %v", hash, key)
		return false, err
	}

	return b, nil
}

func (c *Client) ExistsWithCtx(ctx context.Context, key string) (bool, error) {
	b, err := c.redisClient.Exists(ctx, key).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.Exists for key %v", key)
		return false, err
	}

	return b > 0, nil
}

func (c *Client) HKeys(hash string) ([]string, error) {
	return c.redisClient.HKeys(context.Background(), hash).Result()
}

func (c *Client) HKeysWithContext(ctx context.Context, hash string) ([]string, error) {
	return c.redisClient.HKeys(ctx, hash).Result()
}

func (c *Client) HLen(hash string) (int64, error) {
	return c.redisClient.HLen(context.Background(), hash).Result()
}

func (c *Client) SMembers(key string) ([]string, error) {
	m, err := c.redisClient.SMembers(context.Background(), key).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.SMembers for key %v", key)
		return nil, err
	}
	return m, nil
}

func (c *Client) SIsMember(key string, member string) (bool, error) {
	m, err := c.redisClient.SIsMember(context.Background(), key, member).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.SIsMember for key %v member %v", key, member)
		return false, err
	}
	return m, nil
}

func (c *Client) SAdd(key string, member string) error {
	_, err := c.redisClient.SAdd(context.Background(), key, member).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.SAdd for key %v member %v", key, member)
		return err
	}
	return nil
}

func (c *Client) SRem(key string, member string) error {
	_, err := c.redisClient.SRem(context.Background(), key, member).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.SRem for key %v member %v", key, member)
		return err
	}
	return nil
}

func (c *Client) SRemWithContext(ctx context.Context, key string, members ...string) error {
	_, err := c.redisClient.SRem(ctx, key, members).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.SRem for key %v member %v", key, members)
		return err
	}
	return nil
}

func (c *Client) ZAdd(ctx context.Context, key string, members ...*redis.Z) (int64, error) {
	n, err := c.redisClient.ZAdd(ctx, key, members...).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.ZAdd for key %s members %v", key, members)
	}
	return n, err
}

func (c *Client) ZRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error) {
	zSlice, err := c.redisClient.ZRangeByScoreWithScores(ctx, key, opt).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.ZRangeByScoreWithScores for key %s ZRangeBy %v", key, opt)
	}
	return zSlice, err
}

func (c *Client) ZRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error) {
	zSlice, err := c.redisClient.ZRevRangeByScoreWithScores(ctx, key, opt).Result()
	if err != nil {
		logrus.WithError(err).Errorf("Could not redis.ZRevRangeByScoreWithScores for key %s ZRangeBy %v", key, opt)
	}
	return zSlice, err
}

func (c *Client) ZRemRangeByScore(ctx context.Context, key, min, max string) (int64, error) {
	return c.redisClient.ZRemRangeByScore(ctx, key, min, max).Result()
}

func (c *Client) WaitUntilReady() {
	for {
		_, err := c.redisClient.Ping(context.Background()).Result()
		if err == nil {
			break
		}
		time.Sleep(100 * time.Millisecond)
		logrus.Warn("Awaiting connection to Redis")
	}
}

package module_server_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/module/server"
	"github.com/carbonrobotics/robot/golang/generated/proto/module/types"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

const (
	defaultTimeout = 500 * time.Millisecond
)

type ModuleServerClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client server.ModuleServerServiceClient
	opts   []grpc.DialOption
	logger *logrus.Entry
}

func destroyConnection(c *ModuleServerClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}
}

func NewModuleServerClient(addr string) *ModuleServerClient {
	client := &ModuleServerClient{
		addr:   addr,
		logger: logrus.WithField("module", "ModuleServerClient"),
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *ModuleServerClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *ModuleServerClient) getClient() (server.ModuleServerServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
		defer cancel()
		conn, err := grpc.DialContext(ctx, c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = server.NewModuleServerServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *ModuleServerClient) sendRequest(f func(server.ModuleServerServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.logger.WithError(err).Info("Failed to get client, resetting connection")
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.logger.WithError(err).Info("Send request failed, resetting connection")
				c.resetConnection()
			default:
			}
		}
		return nil, err

	}

	return result, nil
}

func (c *ModuleServerClient) GetModuleIdentity() (id uint32, serial string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	id = 0
	serial = ""
	resp, err := c.sendRequest(func(client server.ModuleServerServiceClient) (interface{}, error) {
		return client.GetModuleIdentity(ctx, &types.Empty{})
	})

	if err != nil {
		return
	}
	moduleIdentity := resp.(*server.GetModuleIdentityResponse).Identity
	id = moduleIdentity.Id
	serial = moduleIdentity.Serial
	return
}

func (c *ModuleServerClient) SetModuleIdentity(id uint32, serial string) error {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client server.ModuleServerServiceClient) (interface{}, error) {
		return client.SetModuleIdentity(ctx, &server.SetModuleIdentityRequest{
			Identity: &types.ModuleIdentity{
				Id:     id,
				Serial: serial,
			},
		})
	})

	return err
}

func (c *ModuleServerClient) SetModuleSerialNumber(serial string, force bool) error {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client server.ModuleServerServiceClient) (interface{}, error) {
		return client.SetModuleSerialNumber(ctx, &server.SetModuleSerialNumberRequest{
			SerialNumber: serial,
			Force:        force,
		})
	})

	return err
}

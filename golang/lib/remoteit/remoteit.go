package remoteit

import (
	"encoding/base64"
	"encoding/json"
	"strings"
	"time"

	"github.com/spf13/afero"
)

var appFS = afero.NewOsFs()

type Configuration struct {
	Timestamp time.Time `json:"timestamp"`
	Device    `json:"device"`
	Services  []Service `json:"services"`
}

type Device struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Config string `json:"config"`
}

type Service struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Config string `json:"config"`
}

func DecodeConfigFields(config string) (map[string]string, error) {
	data, err := base64.StdEncoding.DecodeString(config)
	if err != nil {
		return nil, err
	}
	fields := make(map[string]string)
	for _, line := range strings.Split(string(data), "\n") {
		parts := strings.SplitN(line, " ", 2)
		if len(parts) > 1 {
			fields[parts[0]] = parts[1]
		}
	}
	return fields, nil
}

func ParseConfig(filename string) (*Configuration, error) {
	data, err := afero.ReadFile(appFS, filename)
	if err != nil {
		return nil, err
	}

	config := new(Configuration)
	if err := json.Unmarshal(data, config); err != nil {
		return nil, err
	}
	return config, nil
}

package remoteit

import (
	"fmt"
	"io/ioutil"
	"testing"

	"github.com/spf13/afero"
	"github.com/stretchr/testify/assert"
)

func TestParseConfig(t *testing.T) {
	saveAppFS := appFS
	defer func() { appFS = saveAppFS }()
	configData, err := ioutil.ReadFile("testdata/config.json")
	if err != nil {
		t.Fatal(err)
	}
	appFS = afero.NewMemMapFs()
	if err := afero.WriteFile(appFS, "testdata/config.json", configData, 0755); err != nil {
		t.<PERSON>al(err)
	}

	cfg, err := ParseConfig("testdata/config.json")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(cfg)
	assert.Equal(t, "80:00:00:00:01:21:09:6B", cfg.ID)
	assert.Equal(t, "bud3", cfg.Name)
	m, err := DecodeConfigFields(cfg.Config)
	if err != nil {
		t.<PERSON>al(err)
	}
	fmt.Println(m)

}

package model_manager

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/model_receiver"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type ModelReceiverClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client model_receiver.ModelReceiverClient
	opts   []grpc.DialOption
}

func destroyConnection(c *ModelReceiverClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewModelReceiverClient(addr string) *ModelReceiverClient {
	client := &ModelReceiverClient{
		addr: addr,
	}
	maxSizeBytes := 1024 * 1024 * 1024 * 1024
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	client.opts = append(client.opts, grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(maxSizeBytes), grpc.MaxCallSendMsgSize(maxSizeBytes)))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *ModelReceiverClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *ModelReceiverClient) getClient() (model_receiver.ModelReceiverClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = model_receiver.NewModelReceiverClient(c.conn)
	}

	return c.client, nil
}

func (c *ModelReceiverClient) sendRequest(f func(model_receiver.ModelReceiverClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

func getDefaultModelReceiverClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Minute*2))
}

func (c *ModelReceiverClient) GetDownloadedModels() (*model_receiver.DownloadedModelResponse, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.GetDownloadedModels(ctx, &model_receiver.Empty{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.DownloadedModelResponse), err
}

func (c *ModelReceiverClient) DownloadModelArtifact(request *model_receiver.DownloadModelArtifactRequest) (*model_receiver.Empty, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.DownloadModelArtifact(ctx, request)
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.Empty), err
}

func (c *ModelReceiverClient) DownloadModelMetadata(request *model_receiver.DownloadModelMetadataRequest) (*model_receiver.Empty, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.DownloadModelMetadata(ctx, request)
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.Empty), err
}

func (c *ModelReceiverClient) CleanupModels(modelIds []string) (*model_receiver.Empty, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.CleanupModels(ctx, &model_receiver.CleanupModelsRequest{ModelId: modelIds})
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.Empty), err
}

func (c *ModelReceiverClient) DownloadChip(request *model_receiver.DownloadChipRequest) (*model_receiver.Empty, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.DownloadChip(ctx, request)
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.Empty), err
}

func (c *ModelReceiverClient) DownloadChipMetadata(request *model_receiver.DownloadChipMetadataRequest) (*model_receiver.Empty, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.DownloadChipMetadata(ctx, request)
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.Empty), err
}

func (c *ModelReceiverClient) GetDownloadedChips() (*model_receiver.GetDownloadedChipsResponse, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.GetDownloadedChips(ctx, &model_receiver.Empty{})
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.GetDownloadedChipsResponse), err
}

func (c *ModelReceiverClient) RemoveChips(request *model_receiver.RemoveChipsRequest) (*model_receiver.Empty, error) {
	ctx, cancel := getDefaultModelReceiverClientContext()
	defer cancel()
	response, err := c.sendRequest(func(client model_receiver.ModelReceiverClient) (interface{}, error) {
		return client.RemoveChips(ctx, request)
	})
	if response == nil {
		return nil, err
	}

	return response.(*model_receiver.Empty), err
}

package model_manager

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/spf13/afero"
	"github.com/stretchr/testify/assert"
)

func TestModelArtifact_Filename(t *testing.T) {
	testComputeCapability := "infinite"
	testModelID := "foo-bar-xyz"
	testTensorRTVersion := "4"
	modelArtifact := veselka.ModelArtifact{
		ComputeCapability: testComputeCapability,
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
	}
	assert.Equal(t, modelArtifact.Filename(), testComputeCapability+"_"+testModelID+"_"+testTensorRTVersion+ModelExt)
}

func TestModelArtifact_ID(t *testing.T) {
	testComputeCapability := "infinite"
	testModelID := "foo-bar-xyz"
	testTensorRTVersion := "4"
	modelArtifact := veselka.ModelArtifact{
		ComputeCapability: testComputeCapability,
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
	}
	assert.Equal(t, modelArtifact.ModelManagerArtifactID(), testComputeCapability+"_"+testModelID+"_"+testTensorRTVersion)
}

func TestModel_MetaFilename(t *testing.T) {
	testModelID := "foo-bar-xyz"
	model := Model{
		Model: veselka.Model{ID: testModelID},
	}
	assert.Equal(t, model.MetaFilename(), "foo-bar-xyz"+MetaExt)
}

func TestModel_IsExpired(t *testing.T) {
	saveTimeNow := timeNow
	defer func() { timeNow = saveTimeNow }()

	testTime := time.Unix(1682875781, 0)
	timeNow = func() time.Time {
		return testTime
	}

	tests := []struct {
		name               string
		lastUsed           time.Time
		downloaded         time.Time
		expirationDuration time.Duration
		expectExpired      bool
	}{
		{
			"happy not expired",
			testTime,
			testTime,
			time.Hour,
			false,
		},
		{
			"recently used",
			testTime,
			testTime.Add(-2 * time.Hour),
			time.Hour,
			false,
		},
		{
			"recently downloaded, but never used",
			testTime.Add(-2 * time.Hour),
			testTime,
			time.Hour,
			false,
		},
		{
			"expired",
			testTime.Add(-2 * time.Hour),
			testTime.Add(-2 * time.Hour),
			time.Hour,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			model := &Model{
				LastUsed:   test.lastUsed,
				Downloaded: test.downloaded,
			}
			got := model.IsExpired(test.expirationDuration)
			assert.Equal(t, test.expectExpired, got)
		})
	}
}

func TestDeleteModel(t *testing.T) {
	saveAppFs := AppFs
	defer func() { AppFs = saveAppFs }()

	AppFs = afero.NewMemMapFs()
	testModelCacheDir := "/models_cache"
	testModelArtifactCacheDir := "/artifacts_cache"
	testComputeCapability := "infinite"
	testModelID := "foo-bar-xyz"
	testTensorRTVersion := "4"

	metaFilename := filepath.Join(testModelCacheDir, testModelID+MetaExt)
	if err := afero.WriteFile(AppFs, metaFilename, []byte("data..."), os.ModePerm); err != nil {
		t.Fatal(err)
	}
	modelArtifactFile := filepath.Join(testModelArtifactCacheDir, testComputeCapability+"_"+testModelID+"_"+testTensorRTVersion+ModelExt)
	if err := afero.WriteFile(AppFs, modelArtifactFile, []byte("data..."), os.ModePerm); err != nil {
		t.Fatal(err)
	}

	modelArtifact := &veselka.ModelArtifact{
		ComputeCapability: testComputeCapability,
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
	}

	model := &Model{
		Model: veselka.Model{ID: testModelID,
			ModelArtifacts: []veselka.ModelArtifact{*modelArtifact},
		},
	}

	// Since we just warn for error, happy path is the only real test.
	DeleteModel(model, testModelCacheDir, testModelArtifactCacheDir)

	metaExists, err := afero.Exists(AppFs, metaFilename)
	if err != nil {
		t.Fatal(err)
	}
	modelExists, err := afero.Exists(AppFs, modelArtifactFile)
	if err != nil {
		t.Fatal(err)
	}

	assert.False(t, metaExists)
	assert.False(t, modelExists)
}

func TestGetVerifyMetadata(t *testing.T) {
	saveAppFs := AppFs
	defer func() { AppFs = saveAppFs }()
	testTime := time.Now()
	testDir := "/cache"
	testModelID := "foo-bar-xyz"
	testGoodArtifact := &veselka.ModelArtifact{
		ComputeCapability: "8.6",
		ModelID:           testModelID,
		TensorRTVersion:   "8.0.1.6",
		Checksum:          "9898989898",
	}
	testGoodModel := &Model{
		Model:                      veselka.Model{ID: testModelID, URL: "s3://some url", Type: veselka.ModelTypeDeepweed, Checksum: "9898989898", ModelArtifacts: []veselka.ModelArtifact{*testGoodArtifact}},
		Downloaded:                 testTime,
		LastUsed:                   testTime,
		LastUsedWeedPointThreshold: 123,
		LastUsedCropPointThreshold: 455,
	}
	goodModelJson := testGoodModel.String()
	testBadModel := &Model{
		Model:                      veselka.Model{ID: testModelID},
		Downloaded:                 testTime,
		LastUsed:                   testTime,
		LastUsedWeedPointThreshold: 123,
		LastUsedCropPointThreshold: 455,
	}
	badModelJson := testBadModel.String()
	badJson := `}{`

	tests := []struct {
		name        string
		fileExists  bool
		modelJson   string
		expectError bool
	}{
		{
			"happy path",
			true,
			goodModelJson,
			false,
		},
		{
			"invalid model, good json",
			true,
			badModelJson,
			true,
		},
		{
			"file doesn't exist",
			false,
			goodModelJson,
			true,
		},
		{
			"file has invalid json",
			true,
			badJson,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			AppFs = afero.NewMemMapFs()
			metaFilepath := filepath.Join(testDir, testModelID+MetaExt)
			if test.fileExists {
				if err := afero.WriteFile(AppFs, metaFilepath, []byte(test.modelJson), os.ModePerm); err != nil {
					t.Fatal(err)
				}
			}
			model, err := GetVerifyMetadata(metaFilepath)
			if test.expectError {
				assert.Error(t, err)
				assert.Nil(t, model)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, goodModelJson, model.String())
			}
		})
	}
}

func TestVerifyModelDownload(t *testing.T) {
	saveAppFs := AppFs
	defer func() { AppFs = saveAppFs }()

	testModelBody := []byte("some body data for length and checksum blah blah blah")
	testModelArtifactCacheDir := "/artifacts_cache"
	testComputeCapability := "infinite"
	testModelID := "foo-bar-xyz"
	testTensorRTVersion := "4"

	modelArtifact := &veselka.ModelArtifact{
		ComputeCapability: testComputeCapability,
		ModelID:           testModelID,
		TensorRTVersion:   testTensorRTVersion,
	}

	modelArtifactFile := filepath.Join(testModelArtifactCacheDir, modelArtifact.Filename())

	tests := []struct {
		name        string
		modelExists bool
		modelBody   []byte
		expectError bool
	}{
		{
			"happy path",
			true,
			testModelBody,
			false,
		},
		{
			"model file doesn't exist",
			false,
			testModelBody,
			true,
		},
		{
			"bad model data",
			false,
			[]byte("bad data"),
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			AppFs = afero.NewMemMapFs()
			if test.modelExists {
				if err := afero.WriteFile(AppFs, modelArtifactFile, test.modelBody, os.ModePerm); err != nil {
					t.Fatal(err)
				}
			}

			err := VerifyModelArtifactDownload(*modelArtifact, modelArtifactFile)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestModelCacheDir(t *testing.T) {
	saveAppFs := AppFs
	defer func() { AppFs = saveAppFs }()

	tests := []struct {
		name     string
		robot    environment.Robot
		expected string
	}{
		{
			"with maka data dir",
			environment.Robot{MakaDataDir: "/maka_data"},
			"/maka_data/model_manager/model_cache",
		},
		{
			"no maka data",
			environment.Robot{MakaDataDir: ""},
			"/data/model_manager/model_cache",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			AppFs = afero.NewMemMapFs()

			got := ModelCacheDir(test.robot)
			exists, err := afero.DirExists(AppFs, got)
			if err != nil {
				t.Fatal(err)
			}
			assert.Equal(t, test.expected, got)
			assert.True(t, exists)
		})
	}
}

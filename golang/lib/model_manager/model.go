package model_manager

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"path/filepath"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

var (
	AppFs = afero.NewOsFs()

	// test point
	timeNow = time.Now
)

const (
	ModelExt = ".trt"
	MetaExt  = ".json"
)

type Model struct {
	veselka.Model
	DownloadedTimestamp        uint64
	LastUsedTimestamp          uint64
	Downloaded                 time.Time
	LastUsed                   time.Time
	LastUsedWeedPointThreshold float64
	LastUsedCropPointThreshold float64
}

func (m *Model) String() string {
	b, err := json.Marshal(m)
	if err != nil {
		logrus.WithError(err).Errorf("failed to unmarshal model: %s", m.ID)
		return ""
	}
	return string(b)
}

func (m *Model) Merge(other *Model) {
	m.DownloadedTimestamp = other.DownloadedTimestamp
	m.LastUsedTimestamp = other.LastUsedTimestamp
	m.Downloaded = other.Downloaded
	m.LastUsed = other.LastUsed
	m.LastUsedCropPointThreshold = other.LastUsedCropPointThreshold
	m.LastUsedWeedPointThreshold = other.LastUsedWeedPointThreshold
}

func (m *Model) MetaFilename() string {
	return m.ID + MetaExt
}

func (m *Model) IsExpired(duration time.Duration) bool {
	now := timeNow()
	return now.Sub(m.LastUsed) > duration && now.Sub(m.Downloaded) > duration
}

func DeleteModel(model *Model, metadataDir string, artifactDir string) {
	metaFile := filepath.Join(metadataDir, model.MetaFilename())
	if err := AppFs.Remove(metaFile); err != nil {
		logrus.Warnf("failed to remove %s - %v", metaFile, err)
	}

	for _, artifact := range model.ModelArtifacts {
		modelArtifactFile := filepath.Join(artifactDir, artifact.Filename())
		if err := AppFs.Remove(modelArtifactFile); err != nil {
			logrus.Warnf("failed to remove %s - %v", modelArtifactFile, err)
		}
	}
}

func GetVerifyMetadata(metaPath string) (*Model, error) {
	metaBytes, err := afero.ReadFile(AppFs, metaPath)
	if err != nil {
		return nil, err
	}
	model := &Model{}
	if err := json.Unmarshal(metaBytes, model); err != nil {
		return nil, err
	}
	if model.ID == "" || model.Type == "" {
		return nil, fmt.Errorf("invalid metadata [%s] id and type are required", metaPath)
	}

	for _, artifact := range model.ModelArtifacts {
		if artifact.Checksum == "" || artifact.ComputeCapability == "" || artifact.ModelID == "" || artifact.TensorRTVersion == "" {
			return nil, fmt.Errorf("metadata contains incomplete artifact [%s]", artifact.ModelManagerArtifactID())
		}
	}

	return model, nil
}

func VerifyModelArtifactDownload(artifact veselka.ModelArtifact, filePath string) error {
	info, err := AppFs.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to stat file: %w", err)
	}
	size := info.Size()
	if artifact.ContentLength > 0 && artifact.ContentLength != size {
		return fmt.Errorf("model size mismatch, expected:%d, actual:%d", artifact.ContentLength, size)
	}
	if time.Since(info.ModTime()) < 15*time.Minute {
		modelArtifactFile, err := afero.ReadFile(AppFs, filePath)
		if err != nil {
			return fmt.Errorf("failed to read file: %s - %w", filePath, err)
		}
		fileChecksum := fmt.Sprintf("%x", md5.Sum(modelArtifactFile))
		if artifact.Checksum != "" && artifact.Checksum != fileChecksum {
			return fmt.Errorf("file: %s checksum mismatch: %s != %s", filePath, artifact.Checksum, fileChecksum)
		}
	}
	return nil
}

func ModelCacheDir(env environment.Robot) string {
	dataDir := env.MakaDataDir
	if len(dataDir) == 0 {
		dataDir = "/data"
	}
	mcd := filepath.Join(dataDir, "model_manager", "model_cache")
	if err := AppFs.MkdirAll(mcd, 0777); err != nil {
		logrus.Error("failed to create cache dir", mcd, err)
	}
	return mcd
}

func ComposeModelArtifactFilename(computeCapability, tensorRTVersion string) string {
	return fmt.Sprintf("sm%s_trt%s.trt", computeCapability, tensorRTVersion)
}

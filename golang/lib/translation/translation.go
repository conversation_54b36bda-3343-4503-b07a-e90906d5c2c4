package translation

import "github.com/carbonrobotics/robot/golang/generated/proto/frontend"

func Params(ps ...*frontend.TranslationParameter) []*frontend.TranslationParameter {
	lst := make([]*frontend.TranslationParameter, 0)
	for _, p := range ps {
		lst = append(lst, p)
	}
	return lst
}

func StringParam(name string, val string) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_StringValue{
			StringValue: &frontend.StringValue{
				Value: val,
			},
		},
	}
}

func Int64Param(name string, val int64) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_IntValue{
			IntValue: &frontend.IntegerValue{
				Value: val,
			},
		},
	}
}

func IntParam(name string, val int) *frontend.TranslationParameter {
	return Int64Param(name, int64(val))
}

func Uint32Param(name string, val uint32) *frontend.TranslationParameter {
	return Int64Param(name, int64(val))
}

func DoubleParam(name string, val float64) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_DoubleValue{
			DoubleValue: &frontend.DoubleValue{
				Value: val,
			},
		},
	}
}

func TemperatureCelciusParam(name string, val float64) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_TemperatureValue{
			TemperatureValue: &frontend.TemperatureValue{
				Value: &frontend.TemperatureValue_Celcius{
					Celcius: val,
				},
			},
		},
	}
}

func TemperatureFahrenheitParam(name string, val float64) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_TemperatureValue{
			TemperatureValue: &frontend.TemperatureValue{
				Value: &frontend.TemperatureValue_Fahrenheit{
					Fahrenheit: val,
				},
			},
		},
	}
}

func VoltageParam(name string, val float64) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_VoltageValue{
			VoltageValue: &frontend.VoltageValue{
				Volts: val,
			},
		},
	}
}

func FrequencyParam(name string, val float64) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_FrequencyValue{
			FrequencyValue: &frontend.FrequencyValue{
				Hertz: val,
			},
		},
	}
}

func PercentParam(name string, val uint32) *frontend.TranslationParameter {
	return &frontend.TranslationParameter{
		Name: name,
		Value: &frontend.TranslationParameter_PercentValue{
			PercentValue: &frontend.PercentValue{
				Percent: val,
			},
		},
	}
}

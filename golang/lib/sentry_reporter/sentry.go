package sentry_reporter

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/getsentry/sentry-go"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

var initialized bool = false

const sentryKey = "1749bd88d1e1418b9065e7fe6cf60060"
const projectId = "6169686"

func GetSentryDSN(role string) string {
	if role == "row" || role == "module" {
		return fmt.Sprintf("http://%v@*********:3000/%v", sentryKey, projectId)
	} else {
		return fmt.Sprintf("https://%<EMAIL>/%v", sentryKey, projectId)
	}
}

func InitializeSentry() {
	if initialized {
		return
	}

	robot, err := environment.GetRobot()
	if err != nil {
		logrus.Errorf("Failed to parse environment: %v", err)
		return
	}

	err = sentry.Init(sentry.ClientOptions{})
	if err != nil {
		logrus.Warnf("sentry.Init: %s", err)
		return
	}
	hostname, err := os.Hostname()
	if err != nil {
		logrus.Warnf("Failed to get hostname: %v", err)
		hostname = "unknown"
	}
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetTag("role", robot.MakaRole)
		scope.SetTag("row", robot.MakaRow)
		scope.SetTag("name", robot.MakaRobotName)
		scope.SetTag("gen", robot.MakaGen)
		scope.SetTag("service", hostname)
	})
	initialized = true
}

func HandlePanic() {
	defer sentry.Flush(2 * time.Second)
	if err := recover(); err != nil {
		if initialized {
			if err_val, ok := err.(error); ok {
				sentry.CaptureException(err_val)
			} else if str_val, ok := err.(string); ok {
				sentry.CaptureMessage(str_val)
			}
		} else {
			logrus.Warn("Sentry not initialized, not handling panic")
		}
		panic(err)
	}
}

func PanicInterceptor(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (any, error) {
	if initialized {
		defer HandlePanic()
	}
	return handler(ctx, req)
}

func CaptureMessage(message string) {
	if !initialized {
		logrus.Warn("Sentry not initialized, not sending message")
		return
	}
	sentry.CaptureMessage(message)
}

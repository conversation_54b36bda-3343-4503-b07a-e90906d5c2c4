package veselka

import (
	"github.com/carbonrobotics/robot/golang/generated/proto/almanac"
)

const (
	ModelTypeP2P      ModelType = "p2p"
	ModelTypeDeepweed ModelType = "deepweed"

	ModelFineTuneSubType  = "fine_tune"
	ModelFullTrainSubType = "full_train"
	ModelExt              = ".trt"
)

type ModelArtifact struct {
	ID                string `json:"id"`
	ModelID           string `json:"model_id"`
	TensorRTVersion   string `json:"tensorrt_version"`
	ComputeCapability string `json:"compute_capability"`
	URL               string `json:"url"`
	Checksum          string `json:"checksum"`
	ContentLength     int64  `json:"content_length,omitempty"`
}

func (m *ModelArtifact) Filename() string {
	return m.ModelManagerArtifactID() + ModelExt
}

func (m *ModelArtifact) ModelManagerArtifactID() string {
	return m.ComputeCapability + "_" + m.ModelID + "_" + m.TensorRTVersion
}

type Model struct {
	ID                            string                     `json:"id"`
	URL                           string                     `json:"url"`
	Crop                          string                     `json:"crop"`
	ViableCropIDs                 []string                   `json:"viable_crop_ids"`
	Location                      string                     `json:"location"`
	Created                       int64                      `json:"created"`
	Version                       int                        `json:"version"`
	Customer                      string                     `json:"customer"`
	RobotName                     string                     `json:"robot_name"`
	Type                          ModelType                  `json:"type"`
	Checksum                      string                     `json:"checksum"`
	IsGoodToDeploy                bool                       `json:"is_good_to_deploy"`
	RecommendedWeedPointThreshold float64                    `json:"weed_point_threshold"`
	RecommendedCropPointThreshold float64                    `json:"crop_point_threshold"`
	ModelParameters               *almanac.ModelinatorConfig `json:"model_parameters,omitempty"`
	ModelArtifacts                []ModelArtifact            `json:"model_artifacts"`
}

func (m *Model) IsValid() bool {
	return len(m.ID) > 0
}

func (m *Model) ViableForCropID(cropID string) bool {
	for _, cid := range m.ViableCropIDs {
		if cropID == cid {
			return true
		}
	}
	return false
}

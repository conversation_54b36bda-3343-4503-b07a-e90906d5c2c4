package veselka

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
)

type ImageMetadata struct {
	Images map[string]*ChipDefinition `json:"images"`
}

type ChipDefinition struct {
	Id            string `json:"id"`
	URL           string `json:"url,omitempty"`
	Geohash       string `json:"geohash,omitempty"`
	PreSignedURL  string `json:"pre_signed_url,omitempty"`
	Checksum      string `json:"checksum"`
	ContentLength int64  `json:"content_length,omitempty"`
}

func (v *Client) GetImageMetadata(ctx context.Context, ids []string) (map[string]*ChipDefinition, error) {
	tm := timer()
	v.logger.Debugln("retrieving chip metadata for:", ids)
	chips := make(map[string]*ChipDefinition)
	if len(ids) == 0 {
		return chips, nil
	}

	jsonData, err := json.Marshal(map[string]interface{}{
		"image_ids":           ids,
		"get_pre_signed_urls": true,
	})
	if err != nil {
		v.logger.WithError(err).Error("failed to marshal data")
		return nil, err
	}

	reader := bytes.NewReader(jsonData)
	u := v.getApiBaseURL()
	u.Path = "/images/ids_to_meta"

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u.String(), reader)
	if err != nil {
		return nil, err
	}
	v.setRequestCommonHeaders(req)
	response, err := v.httpClient.Do(req)
	if err != nil {
		v.logger.Info("failed to send get request")
		return nil, err
	}
	defer response.Body.Close()

	v.logger.Debugln("get chip metadata response:", response.Status, "duration:", tm())
	switch response.StatusCode {
	case http.StatusOK:
	case http.StatusNotFound:
		return nil, fmt.Errorf("chip not found")
	default:
		return nil, fmt.Errorf("failed to get chip info for %v", ids)
	}

	imageMetadata := &ImageMetadata{}
	decoder := json.NewDecoder(response.Body)
	err = decoder.Decode(imageMetadata)
	if err != nil {
		v.logger.WithError(err).Error("failed to decode response")
		return nil, err
	}

	return imageMetadata.Images, nil
}

package veselka

type Crop struct {
	ID           string            `json:"id"`
	Created      int64             `json:"created"`
	CommonName   string            `json:"common_name"`
	Description  string            `json:"description"`
	CaptureOnly  bool              `json:"capture_only"`
	Translations []CropTranslation `json:"translations"`
}

type CropTranslation struct {
	Name        string
	Description string
	Language    string
	Version     int
}

type CropEnvironment struct {
	Name     string `json:"name"`
	HasModel bool   `json:"has_model"`
}

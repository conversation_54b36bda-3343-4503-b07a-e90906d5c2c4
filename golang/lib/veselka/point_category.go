package veselka

type PointCategory struct {
	Archived     bool                       `json:"archived,omitempty"`
	Created      int64                      `json:"created,omitempty"`
	Description  string                     `json:"description,omitempty"`
	DisplayName  string                     `json:"display_name,omitempty"`
	ID           string                     `json:"id,omitempty"`
	Name         string                     `json:"name,omitempty"`
	Translations []PointCategoryTranslation `json:"translations,omitempty"`
	Updated      int64                      `json:"updated,omitempty"`
}

type PointCategoryTranslation struct {
	Name     string `json:"name,omitempty"`
	Language string `json:"language,omitempty"`
	Version  int    `json:"version,omitempty"`
}

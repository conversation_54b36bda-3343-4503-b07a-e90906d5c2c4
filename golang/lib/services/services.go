package services

import (
	"sync"
)

var lock = &sync.Mutex{}

type Service struct {
	MetricsPort     int
	RunsOnSecondary bool
}

type Services struct {
	services map[string]Service
}

var servicesInstance *Services

func (s *Services) init() {
	s.services = map[string]Service{
		"cadvisor":              {MetricsPort: 61007, RunsOnSecondary: true},
		"node":                  {MetricsPort: 9100, RunsOnSecondary: true},
		"docker":                {MetricsPort: 9323, RunsOnSecondary: true},
		"registry":              {MetricsPort: 5001, RunsOnSecondary: true},
		"syslog_server":         {MetricsPort: 62003, RunsOnSecondary: false},
		"aimbot":                {MetricsPort: 62101, RunsOnSecondary: false},
		"metrics_aggregator":    {MetricsPort: 62108, RunsOnSecondary: false},
		"cv":                    {MetricsPort: 62103, RunsOnSecondary: true},
		"host_check":            {MetricsPort: 62106, RunsOnSecondary: true},
		"software_manager":      {MetricsPort: 62006, RunsOnSecondary: true},
		"broadcaster":           {MetricsPort: 62009, RunsOnSecondary: true},
		"blackbox_command_icmp": {MetricsPort: 0, RunsOnSecondary: true},
	}
}

func GetServices() map[string]Service {
	lock.Lock()
	defer lock.Unlock()

	if servicesInstance == nil {
		servicesInstance = &Services{}
		servicesInstance.init()
	}
	return servicesInstance.services
}

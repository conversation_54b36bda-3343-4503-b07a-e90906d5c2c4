package aimbot_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

type AimbotClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client aimbot.AimbotServiceClient
	opts   []grpc.DialOption
	logger *logrus.Entry
}

func destroyConnection(c *AimbotClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewAimbotClient(addr string) *AimbotClient {
	client := &AimbotClient{
		addr:   addr,
		logger: logrus.New().WithFields(logrus.Fields{"module": "AimbotClient"}),
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *AimbotClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func getDefaultAimbotClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), 500*time.Millisecond)
}

func (c *AimbotClient) getClient() (aimbot.AimbotServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		conn, err := grpc.DialContext(ctx, c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = aimbot.NewAimbotServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *AimbotClient) sendRequest(f func(aimbot.AimbotServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.logger.WithError(err).Info("Failed to get client, resetting connection")
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.logger.WithError(err).Info("Send request failed, resetting connection")
				c.resetConnection()
			default:
			}
		}
		return nil, err

	}

	return result, nil
}

func (c *AimbotClient) GetBooted() bool {
	ctx, cancel := context.WithTimeout(context.Background(), 1000*time.Millisecond)
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetBooted(ctx, &aimbot.Empty{})
	})

	if err != nil {
		return false
	}

	return result.(*aimbot.BootedReply).Booted
}

func (c *AimbotClient) StartActuationTask(request *aimbot.ActuationTaskRequest) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.StartActuationTask(ctx, request)
	})

	return err
}

func (c *AimbotClient) CancelActuationTask() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.CancelActuationTask(ctx, &aimbot.Empty{})
	})

	return err
}

func (c *AimbotClient) SetTargetingState(weedingEnabled bool, thinningEnabled bool) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.SetTargetingState(ctx, &aimbot.TargetingState{
			WeedingEnabled:  weedingEnabled,
			ThinningEnabled: thinningEnabled,
		})
	})

	return err
}

func (c *AimbotClient) ArmLasers() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ArmLasers(ctx, &aimbot.Empty{})
	})

	return err
}

func (c *AimbotClient) DisarmLasers() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.DisarmLasers(ctx, &aimbot.Empty{})
	})

	return err
}

func (c *AimbotClient) GetAimbotState() (*aimbot.AimbotState, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetAimbotState(ctx, &aimbot.Empty{})
	})

	if err != nil {
		return nil, err
	}

	return result.(*aimbot.AimbotState), nil
}

func (c *AimbotClient) StartAutoCalibrateCrosshair(scannerId uint32) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.StartAutoCalibrateCrosshair(ctx, &aimbot.ScannerDescriptor{
			Id: scannerId,
		})
	})

	return err
}

func (c *AimbotClient) StartAutoCalibrateAllCrosshairs() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.StartAutoCalibrateAllCrosshairs(ctx, &aimbot.Empty{})
	})

	return err
}

func (c *AimbotClient) StopAutoCalibrate() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.StopAutoCalibrate(ctx, &aimbot.Empty{})
	})

	return err
}

func (c *AimbotClient) SetCrosshairPosition(scannerId uint32, x uint32, y uint32) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.SetCrosshairPosition(ctx, &aimbot.ScannerTargetPosition{
			ScannerDescriptor: &aimbot.ScannerDescriptor{
				Id: scannerId,
			},
			X: x,
			Y: y,
		})
	})

	return err
}

func (c *AimbotClient) MoveScanner(scannerId uint32, x uint32, y uint32) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.MoveScanner(ctx, &aimbot.ScannerTargetPosition{
			ScannerDescriptor: &aimbot.ScannerDescriptor{
				Id: scannerId,
			},
			X: x,
			Y: y,
		})
	})

	return err
}

func (c *AimbotClient) LensGet(scannerId uint32) (*aimbot.LensGetReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.LensGet(ctx, &aimbot.LensGetRequest{ScannerId: scannerId})
	})

	if err != nil {
		return nil, err
	}

	return result.(*aimbot.LensGetReply), nil
}

func (c *AimbotClient) LensGetAll() (*aimbot.LensGetAllReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.LensGetAll(ctx, &aimbot.Empty{})
	})

	if err != nil {
		return nil, err
	}

	return result.(*aimbot.LensGetAllReply), nil
}

func (c *AimbotClient) LensSet(scannerId uint32, value uint32) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.LensSet(ctx, &aimbot.LensSetRequest{ScannerId: scannerId, Value: value})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) LensAutoFocus(scannerId uint32) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.LensAutoFocus(ctx, &aimbot.LensAutoFocusRequest{ScannerId: scannerId})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) StopLensAutoFocus(scannerId uint32) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.StopLensAutoFocus(ctx, &aimbot.StopLensAutoFocusRequest{ScannerId: scannerId})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) GetScannerStatus() (*aimbot.ScannerStatusReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetScannerStatus(ctx, &aimbot.Empty{})
	})

	if err != nil {
		return nil, err
	}

	return result.(*aimbot.ScannerStatusReply), nil
}

func (c *AimbotClient) LaserEnable(scannerId uint32, enabled bool) (*aimbot.LaserEnableReply, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(time.Millisecond*5000))
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.LaserEnable(ctx, &aimbot.LaserEnableRequest{
			ScannerId: scannerId, Enabled: enabled,
		})
	})

	if err != nil {
		return nil, err
	}

	return result.(*aimbot.LaserEnableReply), nil
}

func (c *AimbotClient) LaserFire(scannerId uint32, fire bool) (*aimbot.LaserFireReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.LaserFire(ctx, &aimbot.LaserFireRequest{
			ScannerId: scannerId, Fire: fire,
		})
	})

	if err != nil {
		return nil, err
	}

	return result.(*aimbot.LaserFireReply), nil
}

func (c *AimbotClient) ResetLaserMetrics(scanner *aimbot.ScannerDescriptor) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ResetLaserMetrics(ctx, scanner)
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) ResetScanner(req *aimbot.ResetScannerRequest) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ResetScanner(ctx, req)
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) FixLaserMetrics(req *aimbot.FixLaserMetricsRequest) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.FixLaserMetrics(ctx, req)
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) GetTargetVelocity() (*aimbot.TargetVelocityReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetTargetVelocity(ctx, &aimbot.TargetVelocityRequest{})
	})

	if err != nil {
		return nil, err
	}
	return result.(*aimbot.TargetVelocityReply), nil
}

func (c *AimbotClient) GetTrackingState() (*aimbot.TrackingState, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetTrackingState(ctx, &aimbot.Empty{})
	})

	if err != nil {
		return nil, err
	}
	return result.(*aimbot.TrackingState), nil
}

func (c *AimbotClient) GetDimensions() (*aimbot.GetDimensionsResponse, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetDimensions(ctx, &aimbot.Empty{})
	})

	if err != nil {
		return nil, err
	}
	return result.(*aimbot.GetDimensionsResponse), nil
}

func (c *AimbotClient) ServoGetLimits(scannerId uint32) (*aimbot.ServoGetLimitsReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ServoGetLimits(ctx, &aimbot.ServoGetLimitsRequest{ScannerId: scannerId})
	})

	if err != nil {
		return nil, err
	}
	return result.(*aimbot.ServoGetLimitsReply), nil
}

func (c *AimbotClient) ServoGoTo(scannerId uint32, servoType string, position int32, timeMs uint32, awaitSettle bool) (*aimbot.ServoGoToReply, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	sType := aimbot.ServoGoToRequest_TILT
	if servoType == "pan" {
		sType = aimbot.ServoGoToRequest_PAN
	}

	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ServoGoTo(ctx, &aimbot.ServoGoToRequest{ScannerId: scannerId, ServoType: sType, Position: position, TimeMs: timeMs, AwaitSettle: awaitSettle})
	})

	if err != nil {
		return nil, err
	}
	return result.(*aimbot.ServoGoToReply), nil
}

func (c *AimbotClient) ReloadThinningConf() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ReloadThinningConf(ctx, &aimbot.ReloadThinningConfRequest{})
	})

	if err != nil {
		return err
	}

	return nil
}

func (c *AimbotClient) ReloadAlmanacConf() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ReloadAlmanacConf(ctx, &aimbot.ReloadAlmanacConfRequest{})
	})
	if err != nil {
		return err
	}
	return nil
}
func (c *AimbotClient) ReloadDiscriminatorConf() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ReloadDiscriminatorConf(ctx, &aimbot.ReloadDiscriminatorConfRequest{})
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *AimbotClient) ReloadModelinatorConf() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ReloadModelinatorConf(ctx, &aimbot.ReloadModelinatorConfRequest{})
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *AimbotClient) BurnIdividualImage(scannerIds []uint32, speedMmps float32, intensity float32, jsonImg string) error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.BurnIdividualImage(ctx, &aimbot.BurnIdividualImagesRequest{ScannerId: scannerIds, SpeedMmps: speedMmps, Intensity: intensity, JsonImg: jsonImg})
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *AimbotClient) ReloadTVEProfile() error {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.ReloadTVEProfile(ctx, &aimbot.ReloadTVEProfileRequest{})
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *AimbotClient) GetDistanceTrackedItems(camId string) (*aimbot.TrackedItemsResponse, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetDistanceTrackedItems(ctx, &aimbot.TrackedItemsRequest{CamId: camId})
	})
	if err != nil {
		return nil, err
	}
	return result.(*aimbot.TrackedItemsResponse), nil
}

func (c *AimbotClient) GetParticipation() (*aimbot.ParticipationResponse, error) {
	ctx, cancel := getDefaultAimbotClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client aimbot.AimbotServiceClient) (interface{}, error) {
		return client.GetParticipation(ctx, &aimbot.Empty{})
	})
	if err != nil {
		return nil, err
	}
	return result.(*aimbot.ParticipationResponse), nil
}

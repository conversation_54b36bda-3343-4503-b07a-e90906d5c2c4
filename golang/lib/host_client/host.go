package host_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/host_check"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

const (
	defaultTimeout = 5 * time.Second
)

type HostClient struct {
	Addr     string
	Hostname string
	mutex    sync.Mutex
	conn     *grpc.ClientConn
	client   host_check.HostServiceClient
	opts     []grpc.DialOption
}

func destroyConnection(c *HostClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewHostClient(addr string, hostname string) *HostClient {
	client := &HostClient{
		Addr:     addr,
		Hostname: hostname,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *HostClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *HostClient) getClient() (host_check.HostServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.Addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = host_check.NewHostServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *HostClient) sendRequest(f func(host_check.HostServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

func (c *HostClient) GetHostState() (*host_check.HostStatus, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	result, err := c.sendRequest(func(client host_check.HostServiceClient) (interface{}, error) {
		return client.GetHostStatus(ctx, &host_check.HostStatusRequest{})
	})

	if err != nil {
		return nil, err
	}

	return result.(*host_check.HostStatus), nil
}

package chip_manager

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/veselka"
	"github.com/sirupsen/logrus"
	"github.com/spf13/afero"
)

const (
	GraphicExt = ".png"
	MetaExt    = ".json"
)

var (
	AppFs = afero.NewOsFs()
)

type Chip struct {
	veselka.ChipDefinition
	DownloadedTimestamp uint64
	LastUsedTimestamp   uint64
}

func (c *Chip) Filename() string {
	return c.Id + GraphicExt
}

func (c *Chip) MetaFilename() string {
	return c.Id + MetaExt
}

func (c *Chip) IsValid() bool {
	return len(c.Id) > 0
}

func GetVerifyMetadata(metaPath string) (*Chip, error) {
	metaBytes, err := afero.ReadFile(AppFs, metaPath)
	if err != nil {
		return nil, err
	}
	chip := &Chip{}
	if err := json.Unmarshal(metaBytes, chip); err != nil {
		return nil, err
	}
	if chip.Id == "" {
		return nil, fmt.Errorf("invalid metadata [%s] id is required", metaPath)
	}

	return chip, nil
}

func VerifyChipDownload(chip *Chip, filePath string) error {
	info, err := AppFs.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to stat file: %w", err)
	}
	size := info.Size()
	if chip.ContentLength > 0 && chip.ContentLength != size {
		return fmt.Errorf("model size mismatch, expected:%d, actual:%d", chip.ContentLength, size)
	}
	if time.Since(info.ModTime()) < 15*time.Minute {
		chipFile, err := afero.ReadFile(AppFs, filePath)
		if err != nil {
			return fmt.Errorf("failed to read file: %s - %w", filePath, err)
		}
		fileChecksum := fmt.Sprintf("%x", md5.Sum(chipFile))
		if chip.Checksum != "" && chip.Checksum != fileChecksum {
			return fmt.Errorf("file: %s checksum mismatch: %s != %s", filePath, chip.Checksum, fileChecksum)
		}
	}
	return nil
}

func WriteChipMetadata(chipCachePath string, chip *Chip, chipCacheWriteMutex *sync.Mutex) error {
	chipCacheWriteMutex.Lock()
	defer chipCacheWriteMutex.Unlock()

	metaBytes, err := json.Marshal(chip)
	if err != nil {
		return err
	}

	chipMetadataFileName := filepath.Join(chipCachePath, chip.MetaFilename())
	if err := afero.WriteFile(AppFs, chipMetadataFileName+".tmp", metaBytes, 0666); err != nil {
		return err
	}

	return AppFs.Rename(chipMetadataFileName+".tmp", chipMetadataFileName)
}

func ChipCacheDir(env environment.Robot) string {
	dataDir := env.MakaDataDir
	if len(dataDir) == 0 {
		dataDir = "/data"
	}
	mcd := filepath.Join(dataDir, "embeddings", "chip_cache")
	if err := AppFs.MkdirAll(mcd, 0777); err != nil {
		logrus.Error("failed to create cache dir", mcd, err)
	}
	return mcd
}

package formatting

import (
	"testing"
)

func TestNumberFormat(t *testing.T) {
	// float
	got := NumberFormat(1.2345, 2)
	want := "1.23"
	if got != want {
		t.<PERSON>rrorf("NumberFormat(1.2345, 2) = %s; want %s", got, want)
	}

	// long float
	got = NumberFormat(1.23456789, 5)
	want = "1.23457"
	if got != want {
		t.<PERSON><PERSON><PERSON>("NumberFormat(1.23456789, 5) = %s; want %s", got, want)
	}

	// large float
	got = NumberFormat(123456.789, 2)
	want = "123,456.79"
	if got != want {
		t.<PERSON>rro<PERSON>("NumberFormat(123456.789, 2) = %s; want %s", got, want)
	}

	// negative
	got = NumberFormat(-1.2345, 2)
	want = "-1.23"
	if got != want {
		t.Errorf("NumberFormat(-1.2345, 2) = %s; want %s", got, want)
	}

	// int
	got = NumberFormat(1, 2)
	want = "1"
	if got != want {
		t.Errorf("NumberFormat(1, 2) = %s; want %s", got, want)
	}

	// thousands
	got = NumberFormat(1234, 0)
	want = "1,234"
	if got != want {
		t.Errorf("NumberFormat(1234, 0) = %s; want %s", got, want)
	}

	// millions
	got = NumberFormat(1234567, 0)
	want = "1,234,567"
	if got != want {
		t.Errorf("NumberFormat(1234567, 0) = %s; want %s", got, want)
	}

	// trailing zeros
	got = NumberFormat(1000000, 0)
	want = "1,000,000"
	if got != want {
		t.Errorf("NumberFormat(1000000, 0) = %s; want %s", got, want)
	}
}

func TestNearestThousandFormat(t *testing.T) {
	// hundreds
	got := NearestThousandFormat(123)
	want := "123"
	if got != want {
		t.Errorf("NearestThousandFormat(123) = %s; want %s", got, want)
	}

	// thousands
	got = NearestThousandFormat(1234)
	want = "1.2k"
	if got != want {
		t.Errorf("NearestThousandFormat(1234) = %s; want %s", got, want)
	}

	// millions
	got = NearestThousandFormat(1234567)
	want = "1.2M"
	if got != want {
		t.Errorf("NearestThousandFormat(1234567) = %s; want %s", got, want)
	}

	// billions
	got = NearestThousandFormat(1234567890)
	want = "1.2B"
	if got != want {
		t.Errorf("NearestThousandFormat(1234567890) = %s; want %s", got, want)
	}

	// trillions
	got = NearestThousandFormat(1234567890123)
	want = "1.2T"
	if got != want {
		t.Errorf("NearestThousandFormat(1234567890123) = %s; want %s", got, want)
	}
}

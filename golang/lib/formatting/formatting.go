package formatting

import (
	"fmt"
	"math"
	"strconv"
	"strings"
)

var units = [4]string{"k", "M", "B", "T"}

func NumberFormat(number float64, decimals int) string {
	if math.IsNaN(number) || math.IsInf(number, 0) {
		number = 0
	}

	var result string
	var isNegative bool

	if number < 0 {
		number *= -1
		isNegative = true
	}

	rounded, remainder := math.Modf(number)

	if decimals <= 0 {
		remainder = 0
	} else {
		exponent := math.Pow(10, float64(decimals))
		remainder = math.Round(remainder * exponent)
	}

	if rounded >= 1 {
		var x float64
		for rounded >= 1 {
			rounded, x = math.Modf(rounded / 1000)
			x = x * 1000
			segment := strconv.FormatFloat(x, 'f', 0, 64)

			if rounded >= 1 {

				// "0" pad left
				for i := len(segment); i < 3; i++ {
					segment = "0" + segment
				}

				segment = "," + segment
			}
			result = segment + result
		}
	} else {
		result = "0"
	}

	if remainder > 0 {
		fractional := strconv.FormatFloat(remainder, 'f', 0, 64)

		// "0" pad right
		for i := len(fractional); i < decimals; i++ {
			fractional = "0" + fractional
		}

		result += "." + fractional
	}

	if isNegative {
		result = "-" + result
	}
	return result
}

func NearestThousandFormat(input float64) string {
	formatted := NumberFormat(input, 2)
	if math.Abs(input) < 999.5 {
		return formatted
	}

	truncated := formatted[:len(formatted)-2]
	cleaned := strings.Replace(truncated, ",", " ", -1)
	sliced := strings.Fields(cleaned)
	count := len(sliced) - 2
	unit := ""

	if count >= 0 {
		unit = units[count]
	}

	afterDecimal := ""

	if len(sliced) >= 2 {
		value, _ := strconv.ParseFloat("0."+sliced[1], 32)

		if value != 0 {
			afterDecimal = fmt.Sprintf("%.1f", value)[1:]
		}

	}

	return sliced[0] + afterDecimal + unit
}

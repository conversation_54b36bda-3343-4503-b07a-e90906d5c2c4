package portal_clients

import (
	"context"
	"net"
	"strings"
	"sync"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	"github.com/carbonrobotics/robot/golang/generated/proto/metrics_aggregator"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/generated/proto/portal"
	"github.com/carbonrobotics/robot/golang/lib/auth"
	crgrpc "github.com/carbonrobotics/robot/golang/lib/grpc"
	log "github.com/sirupsen/logrus"
	"golang.org/x/oauth2"
	"google.golang.org/grpc"
)

type Client struct {
	Addr  string
	mutex sync.Mutex
	conn  *grpc.ClientConn
	opts  []grpc.DialOption
}

func NewPortalClient(addr string, tokenSource oauth2.TokenSource, opts ...grpc.DialOption) *Client {
	log.Infof("[PORTAL] Creating new portal client for %s", addr)
	if _, port, _ := net.SplitHostPort(addr); port == "" {
		addr = net.JoinHostPort(addr, "443") // default to 443
	}
	secure := strings.HasSuffix(addr, "443")
	bundle := auth.NewCredentialsBundle(tokenSource, secure)

	client := &Client{
		Addr: addr,
		opts: []grpc.DialOption{
			grpc.WithCredentialsBundle(bundle),
		},
	}
	if !secure {
		// if it's not on port 443, use an insecure channel but hack in the credentials
		client.opts = append(client.opts, crgrpc.AuthorizationInjector(tokenSource))
	}
	client.opts = append(client.opts, opts...)
	return client
}

func (c *Client) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
}

func (c *Client) getConnected() (*grpc.ClientConn, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.Addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	return c.conn, nil
}

func (c *Client) sendRequest(f func(con *grpc.ClientConn) (any, error)) (any, error) {
	con, err := c.getConnected()
	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(con)

	if err != nil {
		if isNetworkError(err) {
			// if it's a network error, reset the connection
			log.WithError(err).Errorf("[PORTAL] Network error, resetting connection...")
			c.resetConnection()
			return nil, err
		} else {
			// don't log application errors, the caller should and handle
			return result, err
		}
	}

	return result, nil
}

func (c *Client) SyncAlarms(alarms []*frontend.AlarmRow, robotName string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalAlarmLogServiceClient(conn)
		return client.SyncAlarms(context.Background(), &portal.SyncAlarmsRequest{
			RobotName: robotName,
			Alarms:    alarms,
		})
	})
	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed to sync %d alarms", len(alarms))
	}
	return err
}

func isNetworkError(err error) bool {
	code := status.Code(err)
	for _, networkErrorCode := range []codes.Code{codes.Aborted, codes.Canceled, codes.DataLoss, codes.DeadlineExceeded, codes.Unavailable} {
		if code == networkErrorCode {
			return true
		}
	}
	return false
}

func (c *Client) RemoteItConfigure(serviceId, deviceName string) (*portal.ConfigureResult, error) {
	conn, err := grpc.Dial(c.Addr, c.opts...)
	if err != nil {
		log.WithError(err).Errorf(`[PORTAL] Failed to configure remote.it for service "%s" & device "%s"`, serviceId, deviceName)
		return nil, err
	}
	defer conn.Close()

	client := portal.NewRemoteItManagerClient(conn)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	return client.Configure(ctx, &portal.ConfigureRequest{ServiceId: serviceId, DeviceName: deviceName})
}

func (c *Client) LogHealth(health *portal.HealthLog) (*portal.Empty, error) {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewHealthServiceClient(conn)
		return client.LogHealth(context.Background(), health)
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed to log health for %d at %d", health.ReportedAt, time.Now().UnixMilli())
		return nil, err
	}

	return &portal.Empty{}, nil
}

func (c *Client) ReportIssue(issue *portal.IssueReport) (*portal.Empty, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*5000)
	defer cancel()
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewHealthServiceClient(conn)
		return client.ReportIssue(ctx, issue)
	})

	if err != nil {
		log.WithError(err).Errorf(`[PORTAL] Failed report issue "%s"`, issue.Description)
		return nil, err
	}

	return &portal.Empty{}, nil
}

func (c *Client) UploadJob(job *frontend.Job, robotName string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalJobsServiceClient(conn)
		return client.UploadJob(context.Background(), &portal.UploadJobRequest{
			Job:   job,
			Robot: robotName,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed upload job %s", job.JobDescription.JobId)
		return err
	}

	return nil
}

func (c *Client) UploadJobConfigDump(jobId string, rootConfig *frontend.ConfigNodeSnapshot) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalJobsServiceClient(conn)
		return client.UploadJobConfigDump(context.Background(), &portal.UploadJobConfigDumpRequest{
			JobId:      jobId,
			RootConfig: rootConfig,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed upload config dump for job %s", jobId)
		return err
	}

	return nil
}

func (c *Client) UploadJobMetrics(jobId string, metrics *metrics_aggregator.Metrics) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalJobsServiceClient(conn)
		return client.UploadJobMetrics(context.Background(), &portal.UploadJobMetricsRequest{
			JobId:      jobId,
			JobMetrics: metrics,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed upload metrics for job %s", jobId)
		return err
	}

	return nil
}

func (c *Client) UploadModelInfos(robotName string, modelInfos []*portal.ModelInfo) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewModelInfoSyncServiceClient(conn)
		return client.UploadModelInfos(context.Background(), &portal.UploadModelInfosRequest{
			Robot:      robotName,
			ModelInfos: modelInfos,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed upload model %d infos", len(modelInfos))
		return err
	}

	return nil
}

func (c *Client) GetRenameModelCommands(robotName string) (*portal.GetRenameModelCommandsResponse, error) {
	resp, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewModelInfoSyncServiceClient(conn)
		return client.GetRenameModelCommands(context.Background(), &portal.GetRenameModelCommandsRequest{
			Robot: robotName,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed get rename models commands")
		return nil, err
	}

	return resp.(*portal.GetRenameModelCommandsResponse), nil
}

func (c *Client) PurgeRenameModelCommands(robotName string, commands []*portal.RenameModelCommand) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewModelInfoSyncServiceClient(conn)
		return client.PurgeRenameModelCommands(context.Background(), &portal.PurgeRenameModelCommandsRequest{
			Robot:    robotName,
			Commands: commands,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed purge rename models commands")
		return err
	}

	return nil
}

func (c *Client) GetProfilesData(robotName string) (map[string]*frontend.ProfileSyncData, error) {
	resp, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.GetProfilesData(context.Background(), &portal.GetProfilesDataRequest{
			RobotName: robotName,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed get profiles")
		return nil, err
	}

	return resp.(*portal.GetProfilesDataResponse).Profiles, nil
}

func (c *Client) UploadProfile(req *portal.UploadProfileRequest) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.UploadProfile(context.Background(), req)
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed upload profile %v", req.Profile)
		return err
	}

	return nil
}

func (c *Client) GetProfile(uuid string) (*portal.GetProfileResponse, error) {
	resp, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.GetProfile(context.Background(), &portal.GetProfileRequest{Uuid: uuid})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed get profile %s", uuid)
		return nil, err
	}

	return resp.(*portal.GetProfileResponse), nil
}

func (c *Client) DeleteProfile(uuid string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.DeleteProfile(context.Background(), &portal.DeleteProfileRequest{Uuid: uuid})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed delete profile %s", uuid)
		return err
	}

	return nil
}

func (c *Client) PurgeProfile(uuid string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.PurgeProfile(context.Background(), &portal.PurgeProfileRequest{Uuid: uuid})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed purge profile %s", uuid)
		return err
	}

	return nil
}

func (c *Client) SyncBlocks(blocks []*metrics.SpatialMetricBlock, robotName string) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewSpatialMetricsSyncServiceClient(conn)
		return client.SyncSpatialMetricBlocks(context.Background(), &portal.SyncSpatialMetricBlocksRequest{
			Blocks: blocks,
			Robot:  robotName,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed sync %d blocks", len(blocks))
		return err
	}

	return nil
}

func (c *Client) GetSetActiveProfileCommands(robotName string) ([]*portal.SetActiveProfileCommand, error) {
	resp, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.GetSetActiveProfileCommands(context.Background(), &portal.GetSetActiveProfileCommandsRequest{
			Robot: robotName,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed get set active profile commands")
		return nil, err
	}

	return resp.(*portal.GetSetActiveProfileCommandsResponse).Commands, nil
}

func (c *Client) PurgeSetActiveProfilesCommands(robotName string, commands []*portal.SetActiveProfileCommand) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewPortalProfileSyncServiceClient(conn)
		return client.PurgeSetActiveProfileCommands(context.Background(), &portal.PurgeSetActiveProfileCommandsRequest{
			Robot:    robotName,
			Commands: commands,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed purge set active profile commands")
		return err
	}

	return nil
}

func (c *Client) UploadModelEvents(robotName string, events []*portal.ModelEvent) error {
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewModelHistorySyncServiceClient(conn)
		return client.UploadModelEvents(context.Background(), &portal.UploadModelEventsRequest{
			Robot:  robotName,
			Events: events,
		})
	})

	if err != nil {
		log.WithError(err).Errorf("[PORTAL] Failed upload %d model events", len(events))
		return err
	}

	return nil
}

func (c *Client) UploadReaperConfiguration(parentCtx context.Context, assignedModules []*frontend.ModuleIdentity, currentRobotDefinition *frontend.RobotDefinition) error {
	ctx, cancel := context.WithTimeout(parentCtx, time.Minute)
	defer cancel()
	_, err := c.sendRequest(func(conn *grpc.ClientConn) (any, error) {
		client := portal.NewReaperConfigurationServiceClient(conn)
		return client.UploadReaperConfiguration(ctx, &portal.UploadReaperConfigurationRequest{
			Configuration: &portal.ReaperConfiguration{
				AssignedModules:        assignedModules,
				CurrentRobotDefinition: currentRobotDefinition,
			},
		})
	})

	// feature may go live before portal support
	if err != nil && status.Code(err) != codes.Unimplemented {
		log.WithError(err).Errorf("[PORTAL] Failed upload reaper configuration")
		return err
	}

	return nil
}

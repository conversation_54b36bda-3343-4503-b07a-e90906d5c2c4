package portal_clients

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"google.golang.org/grpc"
)

func TestNewPortalClient(t *testing.T) {
	tests := []struct {
		name string
		addr string
		opts []grpc.DialOption
	}{
		{
			"secure",
			"localhost:443",
			nil,
		},
		{
			"in-secure",
			"localhost:80",
			nil,
		},
		{
			"extra opts secure",
			"localhost:443",
			[]grpc.DialOption{grpc.EmptyDialOption{}},
		},
		{
			"extra opts insecure",
			"localhost:80",
			[]grpc.DialOption{grpc.EmptyDialOption{}},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			expectedOptsCount := 1 + len(test.opts)
			if !strings.HasSuffix(test.addr, "443") {
				expectedOptsCount++
			}
			token := &oauth2.Token{}
			ts := oauth2.StaticTokenSource(token)
			client := NewPortalClient(test.addr, ts, test.opts...)
			assert.Len(t, client.opts, expectedOptsCount)
			assert.Equal(t, test.addr, client.Addr)
		})
	}
}

package data_upload_manager

import (
	"context"
	"runtime"
	"sync"

	"github.com/carbonrobotics/robot/golang/generated/proto/data_upload_manager"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type EmergencyClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client data_upload_manager.EmergencyServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *EmergencyClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewEmergencyServiceClient(addr string, opts ...grpc.DialOption) *EmergencyClient {
	client := &EmergencyClient{
		addr: addr,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	client.opts = append(client.opts, opts...)
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *EmergencyClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *EmergencyClient) getClient() (data_upload_manager.EmergencyServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = data_upload_manager.NewEmergencyServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *EmergencyClient) sendRequest(f func(data_upload_manager.EmergencyServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

// Capture

func (c *EmergencyClient) StartDataCaptureSession(sessionName string, captureRate float64, crop, cropID string, useLatest bool, rowInd uint32, camId string, snapCapture bool) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StartDataCaptureSession(context.Background(), &data_upload_manager.StartDataCaptureSessionRequest{SessionName: sessionName, CaptureRate: captureRate, Crop: crop, CropId: cropID, EnableSinglePredict: useLatest, RowInd: rowInd, CamId: camId, SnapCapture: snapCapture})
	})

	return err
}

func (c *EmergencyClient) PauseDataCaptureSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.PauseDataCaptureSession(context.Background(), &data_upload_manager.PauseDataCaptureSessionRequest{})
	})

	return err
}

func (c *EmergencyClient) StopDataCaptureSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StopDataCaptureSession(context.Background(), &data_upload_manager.StopDataCaptureSessionRequest{})
	})

	return err
}

func (c *EmergencyClient) ResumeDataCaptureSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.ResumeDataCaptureSession(context.Background(), &data_upload_manager.ResumeDataCaptureSessionRequest{})
	})

	return err
}

func (c *EmergencyClient) CompleteDataCaptureSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.CompleteDataCaptureSession(context.Background(), &data_upload_manager.CompleteDataCaptureSessionRequest{})
	})

	return err
}

// Upload

func (c *EmergencyClient) StartDataUploadSession(method string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StartDataUploadSession(context.Background(), &data_upload_manager.StartDataUploadSessionRequest{
			Method: method,
		})
	})

	return err
}

func (c *EmergencyClient) PauseDataUploadSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.PauseDataUploadSession(context.Background(), &data_upload_manager.PauseDataUploadSessionRequest{})
	})

	return err
}

func (c *EmergencyClient) StopDataUploadSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StopDataUploadSession(context.Background(), &data_upload_manager.StopDataUploadSessionRequest{})
	})

	return err
}

func (c *EmergencyClient) ResumeDataUploadSession() error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.ResumeDataUploadSession(context.Background(), &data_upload_manager.ResumeDataUploadSessionRequest{})
	})

	return err
}

// Background Upload

func (c *EmergencyClient) StartBackgroundDataUploadSession(method string, name string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StartBackgroundDataUploadSession(context.Background(), &data_upload_manager.StartBackgroundDataUploadSessionRequest{
			Method:               method,
			EmergencySessionName: name,
		})
	})

	return err
}

func (c *EmergencyClient) PauseBackgroundDataUploadSession(name string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.PauseBackgroundDataUploadSession(context.Background(), &data_upload_manager.PauseBackgroundDataUploadSessionRequest{
			EmergencySessionName: name,
		})
	})

	return err
}

func (c *EmergencyClient) StopBackgroundDataUploadSession(name string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StopBackgroundDataUploadSession(context.Background(), &data_upload_manager.StopBackgroundDataUploadSessionRequest{
			EmergencySessionName: name,
		})
	})

	return err
}

func (c *EmergencyClient) ResumeBackgroundDataUploadSession(name string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.ResumeBackgroundDataUploadSession(context.Background(), &data_upload_manager.ResumeBackgroundDataUploadSessionRequest{
			EmergencySessionName: name,
		})
	})

	return err
}

// Progress
type CaptureProgress struct {
	Exists         bool
	IsCapturing    bool
	ImagesCaptured uint32
	HasCompleted   bool
}

func (c *EmergencyClient) GetCaptureProgress() (CaptureProgress, error) {
	client, err := c.getClient()

	progress := CaptureProgress{}

	if err != nil {
		c.resetConnection()
		return progress, err
	}

	result, err := client.GetCaptureProgress(context.Background(), &data_upload_manager.GetCaptureProgressRequest{})

	if err != nil {
		c.resetConnection()
		return progress, err
	}

	progress.Exists = result.GetExists()
	progress.IsCapturing = result.GetIsCapturing()
	progress.ImagesCaptured = result.GetImagesCaptured()
	progress.HasCompleted = result.GetHasCompleted()

	return progress, nil
}

type UploadProgress struct {
	Exists         bool
	IsUploading    bool
	ImagesUploaded uint32
	Method         string
	HasCompleted   bool
}

func (c *EmergencyClient) GetUploadProgress() (UploadProgress, error) {
	client, err := c.getClient()

	progress := UploadProgress{}

	if err != nil {
		c.resetConnection()
		return progress, err
	}

	result, err := client.GetUploadProgress(context.Background(), &data_upload_manager.GetUploadProgressRequest{})

	if err != nil {
		c.resetConnection()
		return progress, err
	}

	progress.Exists = result.GetExists()
	progress.IsUploading = result.GetIsUploading()
	progress.ImagesUploaded = result.GetImagesUploaded()
	progress.Method = result.GetMethod()
	progress.HasCompleted = result.GetHasCompleted()

	return progress, nil
}

// Snapshot

func (c *EmergencyClient) SnapImages(crop, cropID, camId string, rowInd uint32, timestampMs int64, sessionName string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.SnapImages(context.Background(), &data_upload_manager.SnapImagesRequest{Crop: crop, CropId: cropID, CamId: &camId, RowInd: &rowInd, TimestampMs: &timestampMs, SessionName: sessionName})
	})

	return err
}

// GetSessions

type Session struct {
	Name         string
	NumberImages uint32
	IsUploading  bool
	HasCompleted bool
	IsCapturing  bool
}

func (c *EmergencyClient) GetSessions() ([]Session, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		logrus.Errorf("Failed to get client %v", err)
		return nil, err
	}

	result, err := client.GetSessions(context.Background(), &data_upload_manager.GetSessionsRequest{})

	if err != nil {
		c.resetConnection()
		logrus.Errorf("Failed to get sessions %v", err)
		return nil, err
	}

	resultSessions := result.GetSessions()

	var sessions []Session

	for _, session := range resultSessions {
		newSession := Session{
			Name:         session.GetName(),
			NumberImages: session.GetNumberImages(),
			IsUploading:  session.GetIsUploading(),
			HasCompleted: session.GetHasCompleted(),
			IsCapturing:  session.GetIsCapturing(),
		}
		sessions = append(sessions, newSession)
	}

	return sessions, err
}

type RegularCaptureStatus struct {
	Uploaded            uint32
	Budget              uint32
	LastUploadTimestamp int64
}

func (c *EmergencyClient) GetRegularCaptureStatus() (RegularCaptureStatus, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		logrus.Errorf("Failed to get client %v", err)
		return RegularCaptureStatus{}, err
	}

	result, err := client.GetRegularCaptureStatus(context.Background(), &data_upload_manager.GetRegularCaptureStatusRequest{})

	if err != nil {
		c.resetConnection()
		logrus.Errorf("Failed to get sessions %v", err)
		return RegularCaptureStatus{}, err
	}

	status := RegularCaptureStatus{
		Uploaded:            result.GetUploaded(),
		Budget:              result.GetBudget(),
		LastUploadTimestamp: result.GetLastUploadTimestamp(),
	}
	return status, err
}

func (c *EmergencyClient) StartWeedingDiagnosticsUpload(recordingName string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StartWeedingDiagnosticsUpload(context.Background(), &data_upload_manager.StartWeedingDiagnosticsUploadRequest{Name: recordingName})
	})

	return err
}

func (c *EmergencyClient) StartPlantCaptchaUpload(name string) error {
	_, err := c.sendRequest(func(client data_upload_manager.EmergencyServiceClient) (interface{}, error) {
		return client.StartPlantCaptchaUpload(context.Background(), &data_upload_manager.StartPlantCaptchaUploadRequest{Name: name})
	})

	return err
}

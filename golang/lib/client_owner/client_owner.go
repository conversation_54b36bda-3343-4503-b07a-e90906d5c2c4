package client_owner

import (
	"fmt"
	"os/exec"
	"strings"
	"sync"

	"github.com/carbonrobotics/robot/golang/lib/aimbot_client"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/cv_runtime_client"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/host_client"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/carbonrobotics/robot/golang/lib/model_manager"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/carbonrobotics/robot/golang/lib/robot_definition"
	"github.com/carbonrobotics/robot/golang/lib/rows"
	"github.com/carbonrobotics/robot/golang/lib/software_manager_client"
	"github.com/carbonrobotics/robot/golang/lib/weed_tracking_client"
	"github.com/sirupsen/logrus"
)

const (
	SlayerAddressTemplate = "*********%d"
	ReaperAddressTemplate = "10.10.20.%d"
)

// todo: make this a singleton

type ClientOwner struct {
	redisClient *redis.Client
	nodeCommon  *config.ConfigTree
	robot       *environment.Robot

	role environment.CarbonRole
	gen  environment.CarbonGen

	lk          sync.RWMutex
	rowClients  map[int]*rows.RowClients
	hostClients []*hosts.HostClients

	logger *logrus.Entry
}

func NewClientOwner(redisClient *redis.Client, nodeCommon *config.ConfigTree, robot *environment.Robot) *ClientOwner {
	owner := &ClientOwner{
		redisClient: redisClient,
		nodeCommon:  nodeCommon,
		robot:       robot,
		role:        environment.CarbonRole(robot.MakaRole),
		gen:         environment.CarbonGen(robot.MakaGen),
		rowClients:  make(map[int]*rows.RowClients),
		hostClients: make([]*hosts.HostClients, 0),
		logger:      logrus.WithField("module", "ClientOwner"),
	}
	owner.ReloadClients()
	return owner
}

func (owner *ClientOwner) ReloadClients() {
	owner.lk.Lock()
	// cleanup old clients
	owner.rowClients = make(map[int]*rows.RowClients)
	owner.hostClients = make([]*hosts.HostClients, 0)
	defer owner.lk.Unlock()
	switch owner.gen {
	case environment.CarbonGenBud:
		owner.makeBudClients()
	case environment.CarbonGenSlayer:
		owner.makeSlayerClients(owner.role == environment.CarbonRoleSimulator || owner.role == environment.CarbonRoleSimulatorMinicomputers)
	case environment.CarbonGenReaper:
		owner.makeReaperClients()
	case environment.CarbonGenRtc:
		// no clients
	default:
		owner.makeBudClients()
	}

	for i := range owner.rowClients {
		if owner.nodeCommon.HasNode(fmt.Sprintf("disabled_rows/%v", i)) {
			delete(owner.rowClients, i)
		}
	}

	newHosts := make([]*hosts.HostClients, 0)
	for _, host := range owner.hostClients {
		if owner.nodeCommon.HasNode(fmt.Sprintf("disabled_rows/%v", host.Row)) {
			continue
		}
		newHosts = append(newHosts, host)
	}
	owner.hostClients = newHosts
}

func (owner *ClientOwner) GetRowClients() map[int]*rows.RowClients {
	owner.lk.RLock()
	defer owner.lk.RUnlock()
	return owner.rowClients
}

func (owner *ClientOwner) GetHostClients() []*hosts.HostClients {
	owner.lk.RLock()
	defer owner.lk.RUnlock()
	return owner.hostClients
}

func (owner *ClientOwner) makeBudClients() {
	owner.hostClients = append(owner.hostClients, &hosts.HostClients{
		Row:                   0,
		PcId:                  0,
		IpAddress:             "127.0.0.1",
		HostClient:            host_client.NewHostClient("127.0.0.1:6943", "bud"),
		SoftwareManagerClient: software_manager_client.NewSoftwareManagerClient("127.0.0.1:61005"),
	})
	owner.rowClients[0] = &rows.RowClients{
		AimbotClient:         aimbot_client.NewAimbotClient("127.0.0.1:6942"),
		CVRuntimeClients:     map[uint32]*cv_runtime_client.CVRuntimeClient{0: cv_runtime_client.NewCVRuntimeClient("127.0.0.1:15053")},
		ModelReceiverClients: nil,
		WeedTrackingClient:   weed_tracking_client.NewWeedTrackingClient("127.0.0.1:65432"),
		StreamHosts:          map[uint32]string{0: owner.robot.MakaRobotName},
		StreamPort:           15053,
	}
}

func (owner *ClientOwner) makeSlayerClients(isSimulator bool) {
	useHostnames := false
	hostPrefix := strings.Replace(owner.robot.MakaRobotName, "-command", "", -1)
	simulatorIp := ""
	if strings.HasPrefix(owner.robot.MakaRobotName, "slayertb") {
		useHostnames = true
	} else if isSimulator {
		out, err := exec.Command("bash", "-c", "ifconfig | grep 'inet 10.9'").Output()
		if err != nil {
			logrus.Fatal(err)
		}
		sout := string(out)
		splits := strings.Split(strings.TrimSpace(sout), " ")
		simulatorIp = splits[1]
	}

	owner.hostClients = append(owner.hostClients, &hosts.HostClients{
		Row:                   0,
		PcId:                  0,
		IpAddress:             "127.0.0.1",
		HostClient:            host_client.NewHostClient("127.0.0.1:6943", "command"),
		SoftwareManagerClient: software_manager_client.NewSoftwareManagerClient("127.0.0.1:61005"),
	})

	rowNames := []string{"left row", "middle row", "right row"}
	for i, rowHostname := range rowNames {
		id := i + 1

		address := fmt.Sprintf(SlayerAddressTemplate, id)

		owner.hostClients = append(owner.hostClients, &hosts.HostClients{
			Row:                   uint32(id),
			PcId:                  uint32(id),
			IpAddress:             address,
			HostClient:            host_client.NewHostClient(fmt.Sprintf("%s:%d", address, 6943), rowHostname),
			SoftwareManagerClient: software_manager_client.NewSoftwareManagerClient(fmt.Sprintf("%s:%d", address, 61005)),
		})

		clients := &rows.RowClients{
			AimbotClient:         aimbot_client.NewAimbotClient(fmt.Sprintf("%s:%d", address, 6942)),
			CVRuntimeClients:     make(map[uint32]*cv_runtime_client.CVRuntimeClient),
			ModelReceiverClients: make(map[uint32]*model_manager.ModelReceiverClient),
			WeedTrackingClient:   weed_tracking_client.NewWeedTrackingClient(fmt.Sprintf("%s:%d", address, 65432)),
			StreamHosts:          make(map[uint32]string),
			StreamHostNames:      make(map[uint32]string),
			StreamPort:           15053,
			PrimaryPCId:          uint32(id),
			SecondaryPCIds:       make(map[uint32]struct{}),
		}

		clients.CVRuntimeClients[uint32(id)] = cv_runtime_client.NewCVRuntimeClient(fmt.Sprintf("%s:%d", address, 15053))
		clients.ModelReceiverClients[uint32(id)] = model_manager.NewModelReceiverClient(fmt.Sprintf("%s:%d", address, 61004))
		streamHost := address
		if useHostnames {
			streamHost = makeStreamHostname(id, owner.nodeCommon, hostPrefix, environment.CarbonRoleRow, false)
		} else if isSimulator {
			streamHost = simulatorIp
		}
		clients.StreamHosts[uint32(id)] = streamHost
		clients.StreamHostNames[uint32(id)] = makeStreamHostname(id, owner.nodeCommon, hostPrefix, environment.CarbonRoleRow, false)

		if owner.nodeCommon.HasNode(fmt.Sprintf("minicomputer_rows/%v", id)) {
			secondaryIdx := rows.GetMiniPCSecondaryId(uint32(id))
			secondaryAddress := fmt.Sprintf("*********%v", id)
			owner.hostClients = append(owner.hostClients, &hosts.HostClients{
				Row:                   uint32(id),
				PcId:                  secondaryIdx,
				IpAddress:             secondaryAddress,
				HostClient:            host_client.NewHostClient(secondaryAddress+":6943", rowHostname+" (secondary)"),
				SoftwareManagerClient: software_manager_client.NewSoftwareManagerClient(secondaryAddress + ":61005"),
				IsSecondary:           true,
			})

			secondaryStreamHost := secondaryAddress
			if useHostnames {
				secondaryStreamHost = makeStreamHostname(id, owner.nodeCommon, hostPrefix, environment.CarbonRoleRow, true)
			} else if isSimulator {
				secondaryStreamHost = simulatorIp
			}

			clients.CVRuntimeClients[secondaryIdx] = cv_runtime_client.NewCVRuntimeClient(secondaryAddress + ":15053")
			clients.ModelReceiverClients[secondaryIdx] = model_manager.NewModelReceiverClient(secondaryAddress + ":61004")
			clients.StreamHosts[secondaryIdx] = secondaryStreamHost
			clients.SecondaryPCIds[secondaryIdx] = struct{}{}
		}
		owner.rowClients[id] = clients
	}
}

func (owner *ClientOwner) makeReaperClients() {
	owner.hostClients = append(owner.hostClients, &hosts.HostClients{
		Row:                   0,
		PcId:                  0,
		IpAddress:             "127.0.0.1",
		HostClient:            host_client.NewHostClient("127.0.0.1:6943", "command"),
		SoftwareManagerClient: software_manager_client.NewSoftwareManagerClient("127.0.0.1:61005"),
	})

	useHostnames := strings.HasPrefix(owner.robot.MakaRobotName, "reapertb")
	hostPrefix := strings.Replace(owner.robot.MakaRobotName, "-command", "", -1)

	robotDefinitionExists, err := robot_definition.CurrentDefExistsInRedis(owner.redisClient)
	if err != nil {
		owner.logger.WithError(err).Error("failed to check if robot definition exists in redis")
		return
	}

	if !robotDefinitionExists {
		owner.logger.Warn("robot definition does not exist in redis")
		return
	}

	robotDefinition, err := robot_definition.ReadCurrentDefFromRedis(owner.redisClient)
	if err != nil {
		owner.logger.WithError(err).Error("failed to read robot definition from redis")
		return
	}

	for _, row := range robotDefinition.Rows {

		clients := &rows.RowClients{
			CVRuntimeClients:     make(map[uint32]*cv_runtime_client.CVRuntimeClient),
			ModelReceiverClients: make(map[uint32]*model_manager.ModelReceiverClient),
			StreamHosts:          make(map[uint32]string),
			StreamHostNames:      make(map[uint32]string),
			StreamPort:           15053,
			SecondaryPCIds:       make(map[uint32]struct{}),
		}

		hasAimbotModule := false

		for _, module := range row.Modules {
			if module.Disabled {
				continue
			}

			id := module.ModuleId
			address := fmt.Sprintf(ReaperAddressTemplate, id)

			clients.CVRuntimeClients[uint32(id)] = cv_runtime_client.NewCVRuntimeClient(fmt.Sprintf("%s:%d", address, 15053))
			clients.ModelReceiverClients[uint32(id)] = model_manager.NewModelReceiverClient(fmt.Sprintf("%s:%d", address, 61004))
			streamHost := address
			if useHostnames {
				streamHost = makeStreamHostname(int(id), owner.nodeCommon, hostPrefix, environment.CarbonRoleModule, false)
			}
			clients.StreamHosts[id] = streamHost
			clients.StreamHostNames[id] = makeStreamHostname(int(id), owner.nodeCommon, hostPrefix, environment.CarbonRoleModule, false)

			if module.AimbotHost {
				clients.AimbotClient = aimbot_client.NewAimbotClient(fmt.Sprintf("%s:%d", address, 6942))
				clients.WeedTrackingClient = weed_tracking_client.NewWeedTrackingClient(fmt.Sprintf("%s:%d", address, 65432))
				clients.PrimaryPCId = uint32(id)
				hasAimbotModule = true
			} else {
				clients.SecondaryPCIds[uint32(id)] = struct{}{}
			}

			owner.hostClients = append(owner.hostClients, &hosts.HostClients{
				Row:                   row.RowId,
				PcId:                  id,
				IpAddress:             address,
				HostClient:            host_client.NewHostClient(fmt.Sprintf("%s:%d", address, 6943), fmt.Sprintf("module %v", id)),
				SoftwareManagerClient: software_manager_client.NewSoftwareManagerClient(fmt.Sprintf("%s:%d", address, 61005)),
				IsSecondary:           !module.AimbotHost,
			})

		}
		if hasAimbotModule {
			owner.rowClients[int(row.RowId)] = clients
		}
	}
}

func makeStreamHostname(index int, nodeCommon *config.ConfigTree, hostPrefix string, role environment.CarbonRole, forSecondary bool) string {
	if nodeCommon.HasNode(fmt.Sprintf("minicomputer_rows/%v", index)) {
		if forSecondary {
			return fmt.Sprintf("%v-%v-secondary-%v", hostPrefix, role, index)
		} else {
			return fmt.Sprintf("%v-%v-primary-%v", hostPrefix, role, index)
		}
	} else {
		return fmt.Sprintf("%v-%v-%v", hostPrefix, role, index)
	}
}

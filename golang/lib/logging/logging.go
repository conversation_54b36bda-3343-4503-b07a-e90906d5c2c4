package logging

import (
	"context"
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/logging"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var DefaultLogFormat = &logrus.TextFormatter{
	TimestampFormat: "2006-01-02 15:04:05.000",
	FullTimestamp:   true,
}

func SetLogrusOutput(logFileName string) {
	logfile, _ := os.OpenFile("/data/logs/"+logFileName, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)
	mw := io.MultiWriter(os.Stdout, logfile)
	logrus.SetOutput(mw)
}

type LoggingService struct {
	logging.UnimplementedLoggingServiceServer
	redisClient *redis.Client
	component   string
	env         *environment.Robot
}

func NewLoggingService(grpcServer *grpc.Server, redisClient *redis.Client, component string, env *environment.Robot) *LoggingService {
	l := &LoggingService{
		redisClient: redisClient,
		component:   component,
		env:         env,
	}
	logging.RegisterLoggingServiceServer(grpcServer, l)
	l.initLog(component + ".log")

	return l
}

func (l *LoggingService) initLog(logFileName string) {
	key := fmt.Sprintf("logging/%v/row%v/level", l.component, l.env.MakaRow)
	levelStr, err := l.redisClient.ReadString(key, "INFO")
	if err != nil {
		logrus.WithError(err).Errorf("Could not read key %v from redis", key)
	}
	logrus.Debugf("initLog(): read %v redis key=%v", levelStr, key)
	if levelGrpc, ok := logging.LogLevel_value[levelStr]; ok {
		level := logging.LogLevel(levelGrpc)
		logrus.SetLevel(toLogrusLevel(level))
		logrus.Debugf("initLog(): set log level to %v, redis key=%v", level, key)
	}
	SetLogrusOutput(logFileName)
}

func toLogrusLevel(i logging.LogLevel) logrus.Level {
	var l logrus.Level
	switch i {
	case logging.LogLevel_TRACE:
		l = logrus.TraceLevel
	case logging.LogLevel_DEBUG:
		l = logrus.DebugLevel
	case logging.LogLevel_WARNING:
		l = logrus.WarnLevel
	case logging.LogLevel_INFO:
		l = logrus.InfoLevel
	case logging.LogLevel_ERROR:
		l = logrus.ErrorLevel
	case logging.LogLevel_FATAL:
		l = logrus.FatalLevel
	case logging.LogLevel_PANIC:
		l = logrus.PanicLevel
	}
	return l
}

func (l *LoggingService) SetLevel(ctx context.Context, req *logging.SetLevelRequest) (*logging.Empty, error) {
	level := toLogrusLevel(req.LogLevel)
	logrus.Debugf("Logging: received request to set level to %v", level)
	logrus.SetLevel(level)

	key := fmt.Sprintf("logging/%v/row%v/level", l.component, l.env.MakaRow)
	err := l.redisClient.WriteString(key, logging.LogLevel_name[int32(req.LogLevel)])
	if err != nil {
		logrus.WithError(err).Error("Could not write log level to redis, key=" + key)
	} else {
		logrus.Debugf("setLevel(): wrote level %v to key %v", level, key)
	}

	return &logging.Empty{}, nil
}

type LoggingClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client logging.LoggingServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *LoggingClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewLoggingClient(addr string) *LoggingClient {
	client := &LoggingClient{
		addr: addr,
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *LoggingClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func getDefaultLoggingClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Second*10))
}

func (c *LoggingClient) getClient() (logging.LoggingServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
		defer cancel()
		conn, err := grpc.DialContext(ctx, c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = logging.NewLoggingServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *LoggingClient) sendRequest(f func(logging.LoggingServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	return result, nil
}

func (c *LoggingClient) SetLogLevel(level logging.LogLevel) error {
	ctx, cancel := getDefaultLoggingClientContext()
	defer cancel()
	_, err := c.sendRequest(func(client logging.LoggingServiceClient) (interface{}, error) {
		return client.SetLevel(ctx, &logging.SetLevelRequest{
			LogLevel: level,
		})
	})

	if err != nil {
		return err
	}
	return nil
}

func StringToLogLevel(level string) logrus.Level {
	switch strings.ToLower(level) {
	case "debug":
		return logrus.DebugLevel
	case "panic":
		return logrus.PanicLevel
	case "fatal":
		return logrus.FatalLevel
	case "error":
		return logrus.ErrorLevel
	case "trace":
		return logrus.TraceLevel
	}
	return logrus.InfoLevel
}

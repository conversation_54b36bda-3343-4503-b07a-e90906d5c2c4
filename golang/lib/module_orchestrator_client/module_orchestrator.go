package module_orchestrator_client

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/module/orchestrator"
	"github.com/carbonrobotics/robot/golang/generated/proto/module/types"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

const (
	defaultTimeout = 500 * time.Millisecond
)

type ModuleOrchestratorClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client orchestrator.ModuleOrchestratorServiceClient
	opts   []grpc.DialOption
	logger *logrus.Entry
}

func destroyConnection(c *ModuleOrchestratorClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}
}

func NewModuleOrchestratorClient(addr string) *ModuleOrchestratorClient {
	client := &ModuleOrchestratorClient{
		addr:   addr,
		logger: logrus.WithField("module", "ModuleOrchestratorClient"),
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *ModuleOrchestratorClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func (c *ModuleOrchestratorClient) getClient() (orchestrator.ModuleOrchestratorServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = orchestrator.NewModuleOrchestratorServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *ModuleOrchestratorClient) sendRequest(f func(orchestrator.ModuleOrchestratorServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	resp, err := f(client)
	if err != nil {
		if s, ok := status.FromError(err); ok {
			if s.Code() == codes.Unavailable {
				c.resetConnection()
			}
		}
	}

	return resp, err
}

func (c *ModuleOrchestratorClient) Heartbeat(id uint32, serial, mcbIP, pcIP, ipmiIP string) error {
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()
	_, err := c.sendRequest(func(client orchestrator.ModuleOrchestratorServiceClient) (interface{}, error) {
		return client.Heartbeat(ctx, &orchestrator.HeartbeatRequest{
			Identity: &types.ModuleIdentity{
				Id:     id,
				Serial: serial,
			},
			ModuleIps: &types.ModuleIPs{
				McbIp:  mcbIP,
				PcIp:   pcIP,
				IpmiIp: ipmiIP,
			},
		})
	})

	return err
}

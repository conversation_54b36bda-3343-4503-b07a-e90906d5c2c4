// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/metrics/metrics.proto

package metrics

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LaserPosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Row  uint32 `protobuf:"varint,1,opt,name=row,proto3" json:"row,omitempty"`
	Slot uint32 `protobuf:"varint,2,opt,name=slot,proto3" json:"slot,omitempty"`
}

func (x *LaserPosition) Reset() {
	*x = LaserPosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserPosition) ProtoMessage() {}

func (x *LaserPosition) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserPosition.ProtoReflect.Descriptor instead.
func (*LaserPosition) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{0}
}

func (x *LaserPosition) GetRow() uint32 {
	if x != nil {
		return x.Row
	}
	return 0
}

func (x *LaserPosition) GetSlot() uint32 {
	if x != nil {
		return x.Slot
	}
	return 0
}

type LaserIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position *LaserPosition `protobuf:"bytes,1,opt,name=position,proto3" json:"position,omitempty"`
	Serial   string         `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
}

func (x *LaserIdentifier) Reset() {
	*x = LaserIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserIdentifier) ProtoMessage() {}

func (x *LaserIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserIdentifier.ProtoReflect.Descriptor instead.
func (*LaserIdentifier) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{1}
}

func (x *LaserIdentifier) GetPosition() *LaserPosition {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *LaserIdentifier) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

type LaserLifeTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          *LaserIdentifier `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LifetimeSec uint64           `protobuf:"varint,2,opt,name=lifetime_sec,json=lifetimeSec,proto3" json:"lifetime_sec,omitempty"`
}

func (x *LaserLifeTime) Reset() {
	*x = LaserLifeTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserLifeTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserLifeTime) ProtoMessage() {}

func (x *LaserLifeTime) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserLifeTime.ProtoReflect.Descriptor instead.
func (*LaserLifeTime) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{2}
}

func (x *LaserLifeTime) GetId() *LaserIdentifier {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *LaserLifeTime) GetLifetimeSec() uint64 {
	if x != nil {
		return x.LifetimeSec
	}
	return 0
}

type LaserEventTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           *LaserIdentifier `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TimestampSec int64            `protobuf:"varint,2,opt,name=timestamp_sec,json=timestampSec,proto3" json:"timestamp_sec,omitempty"`
}

func (x *LaserEventTime) Reset() {
	*x = LaserEventTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserEventTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserEventTime) ProtoMessage() {}

func (x *LaserEventTime) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserEventTime.ProtoReflect.Descriptor instead.
func (*LaserEventTime) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{3}
}

func (x *LaserEventTime) GetId() *LaserIdentifier {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *LaserEventTime) GetTimestampSec() int64 {
	if x != nil {
		return x.TimestampSec
	}
	return 0
}

type LaserLifeTimes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lifetimes []*LaserLifeTime `protobuf:"bytes,1,rep,name=lifetimes,proto3" json:"lifetimes,omitempty"`
}

func (x *LaserLifeTimes) Reset() {
	*x = LaserLifeTimes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserLifeTimes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserLifeTimes) ProtoMessage() {}

func (x *LaserLifeTimes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserLifeTimes.ProtoReflect.Descriptor instead.
func (*LaserLifeTimes) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{4}
}

func (x *LaserLifeTimes) GetLifetimes() []*LaserLifeTime {
	if x != nil {
		return x.Lifetimes
	}
	return nil
}

type LaserChangeTimes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Installs []*LaserEventTime `protobuf:"bytes,1,rep,name=installs,proto3" json:"installs,omitempty"`
	Removals []*LaserEventTime `protobuf:"bytes,2,rep,name=removals,proto3" json:"removals,omitempty"`
}

func (x *LaserChangeTimes) Reset() {
	*x = LaserChangeTimes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserChangeTimes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserChangeTimes) ProtoMessage() {}

func (x *LaserChangeTimes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserChangeTimes.ProtoReflect.Descriptor instead.
func (*LaserChangeTimes) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{5}
}

func (x *LaserChangeTimes) GetInstalls() []*LaserEventTime {
	if x != nil {
		return x.Installs
	}
	return nil
}

func (x *LaserChangeTimes) GetRemovals() []*LaserEventTime {
	if x != nil {
		return x.Removals
	}
	return nil
}

type CountsByConclusionType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisarmedWeed []uint32 `protobuf:"varint,1,rep,packed,name=disarmed_weed,json=disarmedWeed,proto3" json:"disarmed_weed,omitempty"`
	ArmedWeed    []uint32 `protobuf:"varint,2,rep,packed,name=armed_weed,json=armedWeed,proto3" json:"armed_weed,omitempty"`
	DisarmedCrop []uint32 `protobuf:"varint,3,rep,packed,name=disarmed_crop,json=disarmedCrop,proto3" json:"disarmed_crop,omitempty"`
	ArmedCrop    []uint32 `protobuf:"varint,4,rep,packed,name=armed_crop,json=armedCrop,proto3" json:"armed_crop,omitempty"`
}

func (x *CountsByConclusionType) Reset() {
	*x = CountsByConclusionType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountsByConclusionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountsByConclusionType) ProtoMessage() {}

func (x *CountsByConclusionType) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountsByConclusionType.ProtoReflect.Descriptor instead.
func (*CountsByConclusionType) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{6}
}

func (x *CountsByConclusionType) GetDisarmedWeed() []uint32 {
	if x != nil {
		return x.DisarmedWeed
	}
	return nil
}

func (x *CountsByConclusionType) GetArmedWeed() []uint32 {
	if x != nil {
		return x.ArmedWeed
	}
	return nil
}

func (x *CountsByConclusionType) GetDisarmedCrop() []uint32 {
	if x != nil {
		return x.DisarmedCrop
	}
	return nil
}

func (x *CountsByConclusionType) GetArmedCrop() []uint32 {
	if x != nil {
		return x.ArmedCrop
	}
	return nil
}

type TargetSizeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CumulativeSize float64 `protobuf:"fixed64,1,opt,name=cumulative_size,json=cumulativeSize,proto3" json:"cumulative_size,omitempty"`
	Count          uint64  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *TargetSizeData) Reset() {
	*x = TargetSizeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetSizeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetSizeData) ProtoMessage() {}

func (x *TargetSizeData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetSizeData.ProtoReflect.Descriptor instead.
func (*TargetSizeData) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{7}
}

func (x *TargetSizeData) GetCumulativeSize() float64 {
	if x != nil {
		return x.CumulativeSize
	}
	return 0
}

func (x *TargetSizeData) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type RequiredLaserTimeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CumulativeTime uint64 `protobuf:"varint,1,opt,name=cumulative_time,json=cumulativeTime,proto3" json:"cumulative_time,omitempty"`
	Count          uint64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *RequiredLaserTimeData) Reset() {
	*x = RequiredLaserTimeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequiredLaserTimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequiredLaserTimeData) ProtoMessage() {}

func (x *RequiredLaserTimeData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequiredLaserTimeData.ProtoReflect.Descriptor instead.
func (*RequiredLaserTimeData) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{8}
}

func (x *RequiredLaserTimeData) GetCumulativeTime() uint64 {
	if x != nil {
		return x.CumulativeTime
	}
	return 0
}

func (x *RequiredLaserTimeData) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SpatialPosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Latitude    float64 `protobuf:"fixed64,1,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude   float64 `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty"`
	HeightMm    float64 `protobuf:"fixed64,3,opt,name=height_mm,json=heightMm,proto3" json:"height_mm,omitempty"`
	TimestampMs uint64  `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	EcefX       float64 `protobuf:"fixed64,5,opt,name=ecef_x,json=ecefX,proto3" json:"ecef_x,omitempty"`
	EcefY       float64 `protobuf:"fixed64,6,opt,name=ecef_y,json=ecefY,proto3" json:"ecef_y,omitempty"`
	EcefZ       float64 `protobuf:"fixed64,7,opt,name=ecef_z,json=ecefZ,proto3" json:"ecef_z,omitempty"`
}

func (x *SpatialPosition) Reset() {
	*x = SpatialPosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialPosition) ProtoMessage() {}

func (x *SpatialPosition) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialPosition.ProtoReflect.Descriptor instead.
func (*SpatialPosition) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{9}
}

func (x *SpatialPosition) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *SpatialPosition) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *SpatialPosition) GetHeightMm() float64 {
	if x != nil {
		return x.HeightMm
	}
	return 0
}

func (x *SpatialPosition) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *SpatialPosition) GetEcefX() float64 {
	if x != nil {
		return x.EcefX
	}
	return 0
}

func (x *SpatialPosition) GetEcefY() float64 {
	if x != nil {
		return x.EcefY
	}
	return 0
}

func (x *SpatialPosition) GetEcefZ() float64 {
	if x != nil {
		return x.EcefZ
	}
	return 0
}

type WeedCounterChunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConclusionCounts        *CountsByConclusionType `protobuf:"bytes,1,opt,name=conclusion_counts,json=conclusionCounts,proto3" json:"conclusion_counts,omitempty"`
	WeedSizeData            *TargetSizeData         `protobuf:"bytes,2,opt,name=weed_size_data,json=weedSizeData,proto3" json:"weed_size_data,omitempty"`
	CropSizeData            *TargetSizeData         `protobuf:"bytes,3,opt,name=crop_size_data,json=cropSizeData,proto3" json:"crop_size_data,omitempty"`
	CountsByCategory        map[string]uint32       `protobuf:"bytes,4,rep,name=counts_by_category,json=countsByCategory,proto3" json:"counts_by_category,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TargetedLaserTimeData   *RequiredLaserTimeData  `protobuf:"bytes,5,opt,name=targeted_laser_time_data,json=targetedLaserTimeData,proto3" json:"targeted_laser_time_data,omitempty"`
	UntargetedLaserTimeData *RequiredLaserTimeData  `protobuf:"bytes,6,opt,name=untargeted_laser_time_data,json=untargetedLaserTimeData,proto3" json:"untargeted_laser_time_data,omitempty"`
	ValidCropCount          uint64                  `protobuf:"varint,7,opt,name=valid_crop_count,json=validCropCount,proto3" json:"valid_crop_count,omitempty"`
}

func (x *WeedCounterChunk) Reset() {
	*x = WeedCounterChunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedCounterChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedCounterChunk) ProtoMessage() {}

func (x *WeedCounterChunk) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedCounterChunk.ProtoReflect.Descriptor instead.
func (*WeedCounterChunk) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{10}
}

func (x *WeedCounterChunk) GetConclusionCounts() *CountsByConclusionType {
	if x != nil {
		return x.ConclusionCounts
	}
	return nil
}

func (x *WeedCounterChunk) GetWeedSizeData() *TargetSizeData {
	if x != nil {
		return x.WeedSizeData
	}
	return nil
}

func (x *WeedCounterChunk) GetCropSizeData() *TargetSizeData {
	if x != nil {
		return x.CropSizeData
	}
	return nil
}

func (x *WeedCounterChunk) GetCountsByCategory() map[string]uint32 {
	if x != nil {
		return x.CountsByCategory
	}
	return nil
}

func (x *WeedCounterChunk) GetTargetedLaserTimeData() *RequiredLaserTimeData {
	if x != nil {
		return x.TargetedLaserTimeData
	}
	return nil
}

func (x *WeedCounterChunk) GetUntargetedLaserTimeData() *RequiredLaserTimeData {
	if x != nil {
		return x.UntargetedLaserTimeData
	}
	return nil
}

func (x *WeedCounterChunk) GetValidCropCount() uint64 {
	if x != nil {
		return x.ValidCropCount
	}
	return 0
}

type WheelEncoderSpatialData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartPosM float32 `protobuf:"fixed32,1,opt,name=start_pos_m,json=startPosM,proto3" json:"start_pos_m,omitempty"`
	EndPosM   float32 `protobuf:"fixed32,2,opt,name=end_pos_m,json=endPosM,proto3" json:"end_pos_m,omitempty"`
}

func (x *WheelEncoderSpatialData) Reset() {
	*x = WheelEncoderSpatialData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WheelEncoderSpatialData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WheelEncoderSpatialData) ProtoMessage() {}

func (x *WheelEncoderSpatialData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WheelEncoderSpatialData.ProtoReflect.Descriptor instead.
func (*WheelEncoderSpatialData) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{11}
}

func (x *WheelEncoderSpatialData) GetStartPosM() float32 {
	if x != nil {
		return x.StartPosM
	}
	return 0
}

func (x *WheelEncoderSpatialData) GetEndPosM() float32 {
	if x != nil {
		return x.EndPosM
	}
	return 0
}

type BandingSpatialData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PercentBanded float32 `protobuf:"fixed32,1,opt,name=percent_banded,json=percentBanded,proto3" json:"percent_banded,omitempty"`
}

func (x *BandingSpatialData) Reset() {
	*x = BandingSpatialData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BandingSpatialData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BandingSpatialData) ProtoMessage() {}

func (x *BandingSpatialData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BandingSpatialData.ProtoReflect.Descriptor instead.
func (*BandingSpatialData) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{12}
}

func (x *BandingSpatialData) GetPercentBanded() float32 {
	if x != nil {
		return x.PercentBanded
	}
	return 0
}

type ImplementWidthSpatialData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WidthMm float32 `protobuf:"fixed32,1,opt,name=width_mm,json=widthMm,proto3" json:"width_mm,omitempty"`
}

func (x *ImplementWidthSpatialData) Reset() {
	*x = ImplementWidthSpatialData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImplementWidthSpatialData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImplementWidthSpatialData) ProtoMessage() {}

func (x *ImplementWidthSpatialData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImplementWidthSpatialData.ProtoReflect.Descriptor instead.
func (*ImplementWidthSpatialData) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{13}
}

func (x *ImplementWidthSpatialData) GetWidthMm() float32 {
	if x != nil {
		return x.WidthMm
	}
	return 0
}

type VelocitySpatialMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvgTargetVel map[string]float32 `protobuf:"bytes,1,rep,name=avg_target_vel,json=avgTargetVel,proto3" json:"avg_target_vel,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *VelocitySpatialMetric) Reset() {
	*x = VelocitySpatialMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VelocitySpatialMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VelocitySpatialMetric) ProtoMessage() {}

func (x *VelocitySpatialMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VelocitySpatialMetric.ProtoReflect.Descriptor instead.
func (*VelocitySpatialMetric) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{14}
}

func (x *VelocitySpatialMetric) GetAvgTargetVel() map[string]float32 {
	if x != nil {
		return x.AvgTargetVel
	}
	return nil
}

type JobMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *JobMetric) Reset() {
	*x = JobMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobMetric) ProtoMessage() {}

func (x *JobMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobMetric.ProtoReflect.Descriptor instead.
func (*JobMetric) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{15}
}

func (x *JobMetric) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type HWMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lifted       bool `protobuf:"varint,1,opt,name=lifted,proto3" json:"lifted,omitempty"`
	Estopped     bool `protobuf:"varint,2,opt,name=estopped,proto3" json:"estopped,omitempty"`
	LaserKey     bool `protobuf:"varint,3,opt,name=laser_key,json=laserKey,proto3" json:"laser_key,omitempty"`
	Interlock    bool `protobuf:"varint,4,opt,name=interlock,proto3" json:"interlock,omitempty"`
	WaterProtect bool `protobuf:"varint,5,opt,name=water_protect,json=waterProtect,proto3" json:"water_protect,omitempty"`
	DebugMode    bool `protobuf:"varint,6,opt,name=debug_mode,json=debugMode,proto3" json:"debug_mode,omitempty"`
}

func (x *HWMetric) Reset() {
	*x = HWMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HWMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HWMetric) ProtoMessage() {}

func (x *HWMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HWMetric.ProtoReflect.Descriptor instead.
func (*HWMetric) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{16}
}

func (x *HWMetric) GetLifted() bool {
	if x != nil {
		return x.Lifted
	}
	return false
}

func (x *HWMetric) GetEstopped() bool {
	if x != nil {
		return x.Estopped
	}
	return false
}

func (x *HWMetric) GetLaserKey() bool {
	if x != nil {
		return x.LaserKey
	}
	return false
}

func (x *HWMetric) GetInterlock() bool {
	if x != nil {
		return x.Interlock
	}
	return false
}

func (x *HWMetric) GetWaterProtect() bool {
	if x != nil {
		return x.WaterProtect
	}
	return false
}

func (x *HWMetric) GetDebugMode() bool {
	if x != nil {
		return x.DebugMode
	}
	return false
}

type SpatialMetricBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start              *SpatialPosition           `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End                *SpatialPosition           `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
	WeedCount          *WeedCounterChunk          `protobuf:"bytes,3,opt,name=weed_count,json=weedCount,proto3" json:"weed_count,omitempty"`
	WeData             *WheelEncoderSpatialData   `protobuf:"bytes,4,opt,name=we_data,json=weData,proto3" json:"we_data,omitempty"`
	BandingData        *BandingSpatialData        `protobuf:"bytes,5,opt,name=banding_data,json=bandingData,proto3" json:"banding_data,omitempty"`
	ImplementWidthData *ImplementWidthSpatialData `protobuf:"bytes,6,opt,name=implement_width_data,json=implementWidthData,proto3" json:"implement_width_data,omitempty"`
	VelData            *VelocitySpatialMetric     `protobuf:"bytes,7,opt,name=vel_data,json=velData,proto3" json:"vel_data,omitempty"`
	StartLeft          *SpatialPosition           `protobuf:"bytes,8,opt,name=start_left,json=startLeft,proto3" json:"start_left,omitempty"`
	StartRight         *SpatialPosition           `protobuf:"bytes,9,opt,name=start_right,json=startRight,proto3" json:"start_right,omitempty"`
	EndLeft            *SpatialPosition           `protobuf:"bytes,10,opt,name=end_left,json=endLeft,proto3" json:"end_left,omitempty"`
	EndRight           *SpatialPosition           `protobuf:"bytes,11,opt,name=end_right,json=endRight,proto3" json:"end_right,omitempty"`
	JobMetric          *JobMetric                 `protobuf:"bytes,12,opt,name=job_metric,json=jobMetric,proto3" json:"job_metric,omitempty"`
	Suspicious         bool                       `protobuf:"varint,13,opt,name=suspicious,proto3" json:"suspicious,omitempty"` // May not be a valid block
	HwMetric           *HWMetric                  `protobuf:"bytes,14,opt,name=hw_metric,json=hwMetric,proto3" json:"hw_metric,omitempty"`
}

func (x *SpatialMetricBlock) Reset() {
	*x = SpatialMetricBlock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_metrics_metrics_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialMetricBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialMetricBlock) ProtoMessage() {}

func (x *SpatialMetricBlock) ProtoReflect() protoreflect.Message {
	mi := &file_proto_metrics_metrics_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialMetricBlock.ProtoReflect.Descriptor instead.
func (*SpatialMetricBlock) Descriptor() ([]byte, []int) {
	return file_proto_metrics_metrics_proto_rawDescGZIP(), []int{17}
}

func (x *SpatialMetricBlock) GetStart() *SpatialPosition {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *SpatialMetricBlock) GetEnd() *SpatialPosition {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *SpatialMetricBlock) GetWeedCount() *WeedCounterChunk {
	if x != nil {
		return x.WeedCount
	}
	return nil
}

func (x *SpatialMetricBlock) GetWeData() *WheelEncoderSpatialData {
	if x != nil {
		return x.WeData
	}
	return nil
}

func (x *SpatialMetricBlock) GetBandingData() *BandingSpatialData {
	if x != nil {
		return x.BandingData
	}
	return nil
}

func (x *SpatialMetricBlock) GetImplementWidthData() *ImplementWidthSpatialData {
	if x != nil {
		return x.ImplementWidthData
	}
	return nil
}

func (x *SpatialMetricBlock) GetVelData() *VelocitySpatialMetric {
	if x != nil {
		return x.VelData
	}
	return nil
}

func (x *SpatialMetricBlock) GetStartLeft() *SpatialPosition {
	if x != nil {
		return x.StartLeft
	}
	return nil
}

func (x *SpatialMetricBlock) GetStartRight() *SpatialPosition {
	if x != nil {
		return x.StartRight
	}
	return nil
}

func (x *SpatialMetricBlock) GetEndLeft() *SpatialPosition {
	if x != nil {
		return x.EndLeft
	}
	return nil
}

func (x *SpatialMetricBlock) GetEndRight() *SpatialPosition {
	if x != nil {
		return x.EndRight
	}
	return nil
}

func (x *SpatialMetricBlock) GetJobMetric() *JobMetric {
	if x != nil {
		return x.JobMetric
	}
	return nil
}

func (x *SpatialMetricBlock) GetSuspicious() bool {
	if x != nil {
		return x.Suspicious
	}
	return false
}

func (x *SpatialMetricBlock) GetHwMetric() *HWMetric {
	if x != nil {
		return x.HwMetric
	}
	return nil
}

var File_proto_metrics_metrics_proto protoreflect.FileDescriptor

var file_proto_metrics_metrics_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x35, 0x0a,
	0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x72, 0x6f, 0x77,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x73, 0x6c, 0x6f, 0x74, 0x22, 0x64, 0x0a, 0x0f, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0x63, 0x0a, 0x0d, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x22,
	0x66, 0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2f, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x73, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x53, 0x65, 0x63, 0x22, 0x4d, 0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x09, 0x6c, 0x69, 0x66,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x09, 0x6c, 0x69, 0x66,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x10, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x08, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x76,
	0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x76,
	0x61, 0x6c, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x42, 0x79,
	0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x69, 0x73, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x57,
	0x65, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x57, 0x65,
	0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x63,
	0x72, 0x6f, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x61, 0x72,
	0x6d, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x6d, 0x65, 0x64,
	0x5f, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x61, 0x72, 0x6d,
	0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x22, 0x4f, 0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x53, 0x69, 0x7a, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0e, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x56, 0x0a, 0x15, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x63, 0x75, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xd0, 0x01, 0x0a, 0x0f, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6d, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x4d, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x15, 0x0a,
	0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65,
	0x63, 0x65, 0x66, 0x58, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x59, 0x12, 0x15, 0x0a, 0x06, 0x65,
	0x63, 0x65, 0x66, 0x5f, 0x7a, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65,
	0x66, 0x5a, 0x22, 0x8c, 0x05, 0x0a, 0x10, 0x57, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x53, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x6e, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x0e,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x77, 0x65, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x44, 0x0a, 0x0e, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x72, 0x6f, 0x70,
	0x53, 0x69, 0x7a, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x64, 0x0a, 0x12, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65,
	0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x5e,
	0x0a, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65,
	0x64, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x62,
	0x0a, 0x1a, 0x75, 0x6e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x17, 0x75, 0x6e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x43, 0x0a, 0x15,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x55, 0x0a, 0x17, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x5f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x73, 0x4d, 0x12, 0x1a, 0x0a, 0x09,
	0x65, 0x6e, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x5f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x4d, 0x22, 0x3b, 0x0a, 0x12, 0x42, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x42,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x22, 0x36, 0x0a, 0x19, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x57, 0x69, 0x64, 0x74, 0x68, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x6d, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x77, 0x69, 0x64, 0x74, 0x68, 0x4d, 0x6d, 0x22, 0xb7, 0x01,
	0x0a, 0x15, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61,
	0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x5d, 0x0a, 0x0e, 0x61, 0x76, 0x67, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2e, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x41, 0x76, 0x67, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x56, 0x65, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x61, 0x76, 0x67, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x1a, 0x3f, 0x0a, 0x11, 0x41, 0x76, 0x67, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x22, 0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0xbd, 0x01, 0x0a, 0x08,
	0x48, 0x57, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x66, 0x74,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x61, 0x74, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x77, 0x61, 0x74, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x64, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0xf4, 0x06, 0x0a, 0x12,
	0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x35, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x31, 0x0a, 0x03, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x3f, 0x0a, 0x0a,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x0a,
	0x07, 0x77, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x70, 0x61, 0x74,
	0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x77, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x45, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70,
	0x61, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5b, 0x0a, 0x14, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x57,
	0x69, 0x64, 0x74, 0x68, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x12, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x57, 0x69, 0x64, 0x74, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x08, 0x76, 0x65, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x53,
	0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x07, 0x76, 0x65,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c,
	0x65, 0x66, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69,
	0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x4c, 0x65, 0x66, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x74,
	0x69, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x52, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c,
	0x65, 0x66, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69,
	0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x4c,
	0x65, 0x66, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x52, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x38, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x52, 0x09, 0x6a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x75, 0x73, 0x70, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x73, 0x75, 0x73, 0x70, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x68,
	0x77, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x48, 0x57, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x08, 0x68, 0x77, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x42, 0x0f, 0x5a, 0x0d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_metrics_metrics_proto_rawDescOnce sync.Once
	file_proto_metrics_metrics_proto_rawDescData = file_proto_metrics_metrics_proto_rawDesc
)

func file_proto_metrics_metrics_proto_rawDescGZIP() []byte {
	file_proto_metrics_metrics_proto_rawDescOnce.Do(func() {
		file_proto_metrics_metrics_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_metrics_metrics_proto_rawDescData)
	})
	return file_proto_metrics_metrics_proto_rawDescData
}

var file_proto_metrics_metrics_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_proto_metrics_metrics_proto_goTypes = []interface{}{
	(*LaserPosition)(nil),             // 0: carbon.metrics.LaserPosition
	(*LaserIdentifier)(nil),           // 1: carbon.metrics.LaserIdentifier
	(*LaserLifeTime)(nil),             // 2: carbon.metrics.LaserLifeTime
	(*LaserEventTime)(nil),            // 3: carbon.metrics.LaserEventTime
	(*LaserLifeTimes)(nil),            // 4: carbon.metrics.LaserLifeTimes
	(*LaserChangeTimes)(nil),          // 5: carbon.metrics.LaserChangeTimes
	(*CountsByConclusionType)(nil),    // 6: carbon.metrics.CountsByConclusionType
	(*TargetSizeData)(nil),            // 7: carbon.metrics.TargetSizeData
	(*RequiredLaserTimeData)(nil),     // 8: carbon.metrics.RequiredLaserTimeData
	(*SpatialPosition)(nil),           // 9: carbon.metrics.SpatialPosition
	(*WeedCounterChunk)(nil),          // 10: carbon.metrics.WeedCounterChunk
	(*WheelEncoderSpatialData)(nil),   // 11: carbon.metrics.WheelEncoderSpatialData
	(*BandingSpatialData)(nil),        // 12: carbon.metrics.BandingSpatialData
	(*ImplementWidthSpatialData)(nil), // 13: carbon.metrics.ImplementWidthSpatialData
	(*VelocitySpatialMetric)(nil),     // 14: carbon.metrics.VelocitySpatialMetric
	(*JobMetric)(nil),                 // 15: carbon.metrics.JobMetric
	(*HWMetric)(nil),                  // 16: carbon.metrics.HWMetric
	(*SpatialMetricBlock)(nil),        // 17: carbon.metrics.SpatialMetricBlock
	nil,                               // 18: carbon.metrics.WeedCounterChunk.CountsByCategoryEntry
	nil,                               // 19: carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry
}
var file_proto_metrics_metrics_proto_depIdxs = []int32{
	0,  // 0: carbon.metrics.LaserIdentifier.position:type_name -> carbon.metrics.LaserPosition
	1,  // 1: carbon.metrics.LaserLifeTime.id:type_name -> carbon.metrics.LaserIdentifier
	1,  // 2: carbon.metrics.LaserEventTime.id:type_name -> carbon.metrics.LaserIdentifier
	2,  // 3: carbon.metrics.LaserLifeTimes.lifetimes:type_name -> carbon.metrics.LaserLifeTime
	3,  // 4: carbon.metrics.LaserChangeTimes.installs:type_name -> carbon.metrics.LaserEventTime
	3,  // 5: carbon.metrics.LaserChangeTimes.removals:type_name -> carbon.metrics.LaserEventTime
	6,  // 6: carbon.metrics.WeedCounterChunk.conclusion_counts:type_name -> carbon.metrics.CountsByConclusionType
	7,  // 7: carbon.metrics.WeedCounterChunk.weed_size_data:type_name -> carbon.metrics.TargetSizeData
	7,  // 8: carbon.metrics.WeedCounterChunk.crop_size_data:type_name -> carbon.metrics.TargetSizeData
	18, // 9: carbon.metrics.WeedCounterChunk.counts_by_category:type_name -> carbon.metrics.WeedCounterChunk.CountsByCategoryEntry
	8,  // 10: carbon.metrics.WeedCounterChunk.targeted_laser_time_data:type_name -> carbon.metrics.RequiredLaserTimeData
	8,  // 11: carbon.metrics.WeedCounterChunk.untargeted_laser_time_data:type_name -> carbon.metrics.RequiredLaserTimeData
	19, // 12: carbon.metrics.VelocitySpatialMetric.avg_target_vel:type_name -> carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry
	9,  // 13: carbon.metrics.SpatialMetricBlock.start:type_name -> carbon.metrics.SpatialPosition
	9,  // 14: carbon.metrics.SpatialMetricBlock.end:type_name -> carbon.metrics.SpatialPosition
	10, // 15: carbon.metrics.SpatialMetricBlock.weed_count:type_name -> carbon.metrics.WeedCounterChunk
	11, // 16: carbon.metrics.SpatialMetricBlock.we_data:type_name -> carbon.metrics.WheelEncoderSpatialData
	12, // 17: carbon.metrics.SpatialMetricBlock.banding_data:type_name -> carbon.metrics.BandingSpatialData
	13, // 18: carbon.metrics.SpatialMetricBlock.implement_width_data:type_name -> carbon.metrics.ImplementWidthSpatialData
	14, // 19: carbon.metrics.SpatialMetricBlock.vel_data:type_name -> carbon.metrics.VelocitySpatialMetric
	9,  // 20: carbon.metrics.SpatialMetricBlock.start_left:type_name -> carbon.metrics.SpatialPosition
	9,  // 21: carbon.metrics.SpatialMetricBlock.start_right:type_name -> carbon.metrics.SpatialPosition
	9,  // 22: carbon.metrics.SpatialMetricBlock.end_left:type_name -> carbon.metrics.SpatialPosition
	9,  // 23: carbon.metrics.SpatialMetricBlock.end_right:type_name -> carbon.metrics.SpatialPosition
	15, // 24: carbon.metrics.SpatialMetricBlock.job_metric:type_name -> carbon.metrics.JobMetric
	16, // 25: carbon.metrics.SpatialMetricBlock.hw_metric:type_name -> carbon.metrics.HWMetric
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_proto_metrics_metrics_proto_init() }
func file_proto_metrics_metrics_proto_init() {
	if File_proto_metrics_metrics_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_metrics_metrics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserPosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserLifeTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserEventTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserLifeTimes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserChangeTimes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountsByConclusionType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetSizeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequiredLaserTimeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialPosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedCounterChunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WheelEncoderSpatialData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BandingSpatialData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImplementWidthSpatialData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VelocitySpatialMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HWMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_metrics_metrics_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialMetricBlock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_metrics_metrics_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_metrics_metrics_proto_goTypes,
		DependencyIndexes: file_proto_metrics_metrics_proto_depIdxs,
		MessageInfos:      file_proto_metrics_metrics_proto_msgTypes,
	}.Build()
	File_proto_metrics_metrics_proto = out.File
	file_proto_metrics_metrics_proto_rawDesc = nil
	file_proto_metrics_metrics_proto_goTypes = nil
	file_proto_metrics_metrics_proto_depIdxs = nil
}

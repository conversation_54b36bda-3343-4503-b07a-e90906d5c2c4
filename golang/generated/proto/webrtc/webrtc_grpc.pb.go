// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/webrtc/webrtc.proto

package webrtc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WebrtcService_CreateConnection_FullMethodName = "/carbon.webrtc.WebrtcService/CreateConnection"
	WebrtcService_SetRemoteSdp_FullMethodName     = "/carbon.webrtc.WebrtcService/SetRemoteSdp"
)

// WebrtcServiceClient is the client API for WebrtcService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebrtcServiceClient interface {
	CreateConnection(ctx context.Context, in *CreateConnectionRequest, opts ...grpc.CallOption) (*CreateConnectionResponse, error)
	SetRemoteSdp(ctx context.Context, in *SetRemoteSdpRequest, opts ...grpc.CallOption) (*SetRemoteSdpResponse, error)
}

type webrtcServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWebrtcServiceClient(cc grpc.ClientConnInterface) WebrtcServiceClient {
	return &webrtcServiceClient{cc}
}

func (c *webrtcServiceClient) CreateConnection(ctx context.Context, in *CreateConnectionRequest, opts ...grpc.CallOption) (*CreateConnectionResponse, error) {
	out := new(CreateConnectionResponse)
	err := c.cc.Invoke(ctx, WebrtcService_CreateConnection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webrtcServiceClient) SetRemoteSdp(ctx context.Context, in *SetRemoteSdpRequest, opts ...grpc.CallOption) (*SetRemoteSdpResponse, error) {
	out := new(SetRemoteSdpResponse)
	err := c.cc.Invoke(ctx, WebrtcService_SetRemoteSdp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebrtcServiceServer is the server API for WebrtcService service.
// All implementations must embed UnimplementedWebrtcServiceServer
// for forward compatibility
type WebrtcServiceServer interface {
	CreateConnection(context.Context, *CreateConnectionRequest) (*CreateConnectionResponse, error)
	SetRemoteSdp(context.Context, *SetRemoteSdpRequest) (*SetRemoteSdpResponse, error)
	mustEmbedUnimplementedWebrtcServiceServer()
}

// UnimplementedWebrtcServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWebrtcServiceServer struct {
}

func (UnimplementedWebrtcServiceServer) CreateConnection(context.Context, *CreateConnectionRequest) (*CreateConnectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateConnection not implemented")
}
func (UnimplementedWebrtcServiceServer) SetRemoteSdp(context.Context, *SetRemoteSdpRequest) (*SetRemoteSdpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRemoteSdp not implemented")
}
func (UnimplementedWebrtcServiceServer) mustEmbedUnimplementedWebrtcServiceServer() {}

// UnsafeWebrtcServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebrtcServiceServer will
// result in compilation errors.
type UnsafeWebrtcServiceServer interface {
	mustEmbedUnimplementedWebrtcServiceServer()
}

func RegisterWebrtcServiceServer(s grpc.ServiceRegistrar, srv WebrtcServiceServer) {
	s.RegisterService(&WebrtcService_ServiceDesc, srv)
}

func _WebrtcService_CreateConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateConnectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebrtcServiceServer).CreateConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebrtcService_CreateConnection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebrtcServiceServer).CreateConnection(ctx, req.(*CreateConnectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebrtcService_SetRemoteSdp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRemoteSdpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebrtcServiceServer).SetRemoteSdp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WebrtcService_SetRemoteSdp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebrtcServiceServer).SetRemoteSdp(ctx, req.(*SetRemoteSdpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WebrtcService_ServiceDesc is the grpc.ServiceDesc for WebrtcService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebrtcService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.webrtc.WebrtcService",
	HandlerType: (*WebrtcServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateConnection",
			Handler:    _WebrtcService_CreateConnection_Handler,
		},
		{
			MethodName: "SetRemoteSdp",
			Handler:    _WebrtcService_SetRemoteSdp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/webrtc/webrtc.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/webrtc/webrtc.proto

package webrtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateConnectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateConnectionRequest) Reset() {
	*x = CreateConnectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_webrtc_webrtc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateConnectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConnectionRequest) ProtoMessage() {}

func (x *CreateConnectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_webrtc_webrtc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConnectionRequest.ProtoReflect.Descriptor instead.
func (*CreateConnectionRequest) Descriptor() ([]byte, []int) {
	return file_proto_webrtc_webrtc_proto_rawDescGZIP(), []int{0}
}

func (x *CreateConnectionRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CreateConnectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdpJson string `protobuf:"bytes,1,opt,name=sdp_json,json=sdpJson,proto3" json:"sdp_json,omitempty"`
}

func (x *CreateConnectionResponse) Reset() {
	*x = CreateConnectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_webrtc_webrtc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateConnectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConnectionResponse) ProtoMessage() {}

func (x *CreateConnectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_webrtc_webrtc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConnectionResponse.ProtoReflect.Descriptor instead.
func (*CreateConnectionResponse) Descriptor() ([]byte, []int) {
	return file_proto_webrtc_webrtc_proto_rawDescGZIP(), []int{1}
}

func (x *CreateConnectionResponse) GetSdpJson() string {
	if x != nil {
		return x.SdpJson
	}
	return ""
}

type SetRemoteSdpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SdpJson string `protobuf:"bytes,2,opt,name=sdp_json,json=sdpJson,proto3" json:"sdp_json,omitempty"`
}

func (x *SetRemoteSdpRequest) Reset() {
	*x = SetRemoteSdpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_webrtc_webrtc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRemoteSdpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRemoteSdpRequest) ProtoMessage() {}

func (x *SetRemoteSdpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_webrtc_webrtc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRemoteSdpRequest.ProtoReflect.Descriptor instead.
func (*SetRemoteSdpRequest) Descriptor() ([]byte, []int) {
	return file_proto_webrtc_webrtc_proto_rawDescGZIP(), []int{2}
}

func (x *SetRemoteSdpRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetRemoteSdpRequest) GetSdpJson() string {
	if x != nil {
		return x.SdpJson
	}
	return ""
}

type SetRemoteSdpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetRemoteSdpResponse) Reset() {
	*x = SetRemoteSdpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_webrtc_webrtc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRemoteSdpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRemoteSdpResponse) ProtoMessage() {}

func (x *SetRemoteSdpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_webrtc_webrtc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRemoteSdpResponse.ProtoReflect.Descriptor instead.
func (*SetRemoteSdpResponse) Descriptor() ([]byte, []int) {
	return file_proto_webrtc_webrtc_proto_rawDescGZIP(), []int{3}
}

var File_proto_webrtc_webrtc_proto protoreflect.FileDescriptor

var file_proto_webrtc_webrtc_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x62, 0x72, 0x74, 0x63, 0x2f, 0x77,
	0x65, 0x62, 0x72, 0x74, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x77, 0x65, 0x62, 0x72, 0x74, 0x63, 0x22, 0x29, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x35, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x64, 0x70, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x64, 0x70, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x40, 0x0a, 0x13,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x64, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x64, 0x70, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x64, 0x70, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x16,
	0x0a, 0x14, 0x53, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x64, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xcd, 0x01, 0x0a, 0x0d, 0x57, 0x65, 0x62, 0x72, 0x74,
	0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x77, 0x65, 0x62, 0x72, 0x74, 0x63, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x77, 0x65,
	0x62, 0x72, 0x74, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a,
	0x0c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x64, 0x70, 0x12, 0x22, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x77, 0x65, 0x62, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x64, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x77, 0x65, 0x62, 0x72, 0x74,
	0x63, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x64, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x48, 0x03, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x77, 0x65, 0x62, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_webrtc_webrtc_proto_rawDescOnce sync.Once
	file_proto_webrtc_webrtc_proto_rawDescData = file_proto_webrtc_webrtc_proto_rawDesc
)

func file_proto_webrtc_webrtc_proto_rawDescGZIP() []byte {
	file_proto_webrtc_webrtc_proto_rawDescOnce.Do(func() {
		file_proto_webrtc_webrtc_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_webrtc_webrtc_proto_rawDescData)
	})
	return file_proto_webrtc_webrtc_proto_rawDescData
}

var file_proto_webrtc_webrtc_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_webrtc_webrtc_proto_goTypes = []interface{}{
	(*CreateConnectionRequest)(nil),  // 0: carbon.webrtc.CreateConnectionRequest
	(*CreateConnectionResponse)(nil), // 1: carbon.webrtc.CreateConnectionResponse
	(*SetRemoteSdpRequest)(nil),      // 2: carbon.webrtc.SetRemoteSdpRequest
	(*SetRemoteSdpResponse)(nil),     // 3: carbon.webrtc.SetRemoteSdpResponse
}
var file_proto_webrtc_webrtc_proto_depIdxs = []int32{
	0, // 0: carbon.webrtc.WebrtcService.CreateConnection:input_type -> carbon.webrtc.CreateConnectionRequest
	2, // 1: carbon.webrtc.WebrtcService.SetRemoteSdp:input_type -> carbon.webrtc.SetRemoteSdpRequest
	1, // 2: carbon.webrtc.WebrtcService.CreateConnection:output_type -> carbon.webrtc.CreateConnectionResponse
	3, // 3: carbon.webrtc.WebrtcService.SetRemoteSdp:output_type -> carbon.webrtc.SetRemoteSdpResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_webrtc_webrtc_proto_init() }
func file_proto_webrtc_webrtc_proto_init() {
	if File_proto_webrtc_webrtc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_webrtc_webrtc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateConnectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_webrtc_webrtc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateConnectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_webrtc_webrtc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRemoteSdpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_webrtc_webrtc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRemoteSdpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_webrtc_webrtc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_webrtc_webrtc_proto_goTypes,
		DependencyIndexes: file_proto_webrtc_webrtc_proto_depIdxs,
		MessageInfos:      file_proto_webrtc_webrtc_proto_msgTypes,
	}.Build()
	File_proto_webrtc_webrtc_proto = out.File
	file_proto_webrtc_webrtc_proto_rawDesc = nil
	file_proto_webrtc_webrtc_proto_goTypes = nil
	file_proto_webrtc_webrtc_proto_depIdxs = nil
}

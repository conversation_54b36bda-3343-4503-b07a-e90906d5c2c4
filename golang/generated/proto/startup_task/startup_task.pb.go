// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/startup_task/startup_task.proto

package startup_task

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskState int32

const (
	TaskState_QUEUED      TaskState = 0
	TaskState_IN_PROGRESS TaskState = 1
	TaskState_COMPLETE    TaskState = 2
	TaskState_ERROR       TaskState = 3
	TaskState_UNKNOWN     TaskState = 4
)

// Enum value maps for TaskState.
var (
	TaskState_name = map[int32]string{
		0: "QUEUED",
		1: "IN_PROGRESS",
		2: "COMPLETE",
		3: "ERROR",
		4: "UNKNOWN",
	}
	TaskState_value = map[string]int32{
		"QUEUED":      0,
		"IN_PROGRESS": 1,
		"COMPLETE":    2,
		"ERROR":       3,
		"UNKNOWN":     4,
	}
)

func (x TaskState) Enum() *TaskState {
	p := new(TaskState)
	*p = x
	return p
}

func (x TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_startup_task_startup_task_proto_enumTypes[0].Descriptor()
}

func (TaskState) Type() protoreflect.EnumType {
	return &file_proto_startup_task_startup_task_proto_enumTypes[0]
}

func (x TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskState.Descriptor instead.
func (TaskState) EnumDescriptor() ([]byte, []int) {
	return file_proto_startup_task_startup_task_proto_rawDescGZIP(), []int{0}
}

type CrosshairCalibrationTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowNumber uint32 `protobuf:"varint,1,opt,name=row_number,json=rowNumber,proto3" json:"row_number,omitempty"`
	LaserId   uint32 `protobuf:"varint,2,opt,name=laser_id,json=laserId,proto3" json:"laser_id,omitempty"`
}

func (x *CrosshairCalibrationTask) Reset() {
	*x = CrosshairCalibrationTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_startup_task_startup_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrosshairCalibrationTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrosshairCalibrationTask) ProtoMessage() {}

func (x *CrosshairCalibrationTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_startup_task_startup_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrosshairCalibrationTask.ProtoReflect.Descriptor instead.
func (*CrosshairCalibrationTask) Descriptor() ([]byte, []int) {
	return file_proto_startup_task_startup_task_proto_rawDescGZIP(), []int{0}
}

func (x *CrosshairCalibrationTask) GetRowNumber() uint32 {
	if x != nil {
		return x.RowNumber
	}
	return 0
}

func (x *CrosshairCalibrationTask) GetLaserId() uint32 {
	if x != nil {
		return x.LaserId
	}
	return 0
}

type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Label       string    `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	Description string    `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	State       TaskState `protobuf:"varint,4,opt,name=state,proto3,enum=carbon.startup_task.TaskState" json:"state,omitempty"`
	// Types that are assignable to TaskDetails:
	//
	//	*Task_CrosshairCalTask
	TaskDetails isTask_TaskDetails `protobuf_oneof:"task_details"`
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_startup_task_startup_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_proto_startup_task_startup_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_proto_startup_task_startup_task_proto_rawDescGZIP(), []int{1}
}

func (x *Task) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Task) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Task) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Task) GetState() TaskState {
	if x != nil {
		return x.State
	}
	return TaskState_QUEUED
}

func (m *Task) GetTaskDetails() isTask_TaskDetails {
	if m != nil {
		return m.TaskDetails
	}
	return nil
}

func (x *Task) GetCrosshairCalTask() *CrosshairCalibrationTask {
	if x, ok := x.GetTaskDetails().(*Task_CrosshairCalTask); ok {
		return x.CrosshairCalTask
	}
	return nil
}

type isTask_TaskDetails interface {
	isTask_TaskDetails()
}

type Task_CrosshairCalTask struct {
	CrosshairCalTask *CrosshairCalibrationTask `protobuf:"bytes,5,opt,name=crosshair_cal_task,json=crosshairCalTask,proto3,oneof"`
}

func (*Task_CrosshairCalTask) isTask_TaskDetails() {}

var File_proto_startup_task_startup_task_proto protoreflect.FileDescriptor

var file_proto_startup_task_startup_task_proto_rawDesc = []byte{
	0x0a, 0x25, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x22, 0x54, 0x0a, 0x18,
	0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x6f,
	0x77, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0xf3, 0x01, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x63, 0x72, 0x6f,
	0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x68, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x10, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69,
	0x72, 0x43, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x0e, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2a, 0x4e, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x51, 0x55, 0x45, 0x55, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02,
	0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x42, 0x14, 0x5a, 0x12, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_startup_task_startup_task_proto_rawDescOnce sync.Once
	file_proto_startup_task_startup_task_proto_rawDescData = file_proto_startup_task_startup_task_proto_rawDesc
)

func file_proto_startup_task_startup_task_proto_rawDescGZIP() []byte {
	file_proto_startup_task_startup_task_proto_rawDescOnce.Do(func() {
		file_proto_startup_task_startup_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_startup_task_startup_task_proto_rawDescData)
	})
	return file_proto_startup_task_startup_task_proto_rawDescData
}

var file_proto_startup_task_startup_task_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_startup_task_startup_task_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_startup_task_startup_task_proto_goTypes = []interface{}{
	(TaskState)(0),                   // 0: carbon.startup_task.TaskState
	(*CrosshairCalibrationTask)(nil), // 1: carbon.startup_task.CrosshairCalibrationTask
	(*Task)(nil),                     // 2: carbon.startup_task.Task
}
var file_proto_startup_task_startup_task_proto_depIdxs = []int32{
	0, // 0: carbon.startup_task.Task.state:type_name -> carbon.startup_task.TaskState
	1, // 1: carbon.startup_task.Task.crosshair_cal_task:type_name -> carbon.startup_task.CrosshairCalibrationTask
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_startup_task_startup_task_proto_init() }
func file_proto_startup_task_startup_task_proto_init() {
	if File_proto_startup_task_startup_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_startup_task_startup_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrosshairCalibrationTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_startup_task_startup_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_startup_task_startup_task_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Task_CrosshairCalTask)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_startup_task_startup_task_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_startup_task_startup_task_proto_goTypes,
		DependencyIndexes: file_proto_startup_task_startup_task_proto_depIdxs,
		EnumInfos:         file_proto_startup_task_startup_task_proto_enumTypes,
		MessageInfos:      file_proto_startup_task_startup_task_proto_msgTypes,
	}.Build()
	File_proto_startup_task_startup_task_proto = out.File
	file_proto_startup_task_startup_task_proto_rawDesc = nil
	file_proto_startup_task_startup_task_proto_goTypes = nil
	file_proto_startup_task_startup_task_proto_depIdxs = nil
}

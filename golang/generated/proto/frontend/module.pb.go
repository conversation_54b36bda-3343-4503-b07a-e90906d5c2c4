// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/module.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModuleIdentity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Serial string `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
}

func (x *ModuleIdentity) Reset() {
	*x = ModuleIdentity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleIdentity) ProtoMessage() {}

func (x *ModuleIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleIdentity.ProtoReflect.Descriptor instead.
func (*ModuleIdentity) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{0}
}

func (x *ModuleIdentity) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ModuleIdentity) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

type GetNextModulesListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextModulesListRequest) Reset() {
	*x = GetNextModulesListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextModulesListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextModulesListRequest) ProtoMessage() {}

func (x *GetNextModulesListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextModulesListRequest.ProtoReflect.Descriptor instead.
func (*GetNextModulesListRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextModulesListRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextModulesListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                 *Timestamp        `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	AssignedModules    []*ModuleIdentity `protobuf:"bytes,2,rep,name=assigned_modules,json=assignedModules,proto3" json:"assigned_modules,omitempty"`
	UnassignedModules  []*ModuleIdentity `protobuf:"bytes,3,rep,name=unassigned_modules,json=unassignedModules,proto3" json:"unassigned_modules,omitempty"`
	UnsetSerialModules []*ModuleIdentity `protobuf:"bytes,4,rep,name=unset_serial_modules,json=unsetSerialModules,proto3" json:"unset_serial_modules,omitempty"`
}

func (x *GetNextModulesListResponse) Reset() {
	*x = GetNextModulesListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextModulesListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextModulesListResponse) ProtoMessage() {}

func (x *GetNextModulesListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextModulesListResponse.ProtoReflect.Descriptor instead.
func (*GetNextModulesListResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{2}
}

func (x *GetNextModulesListResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextModulesListResponse) GetAssignedModules() []*ModuleIdentity {
	if x != nil {
		return x.AssignedModules
	}
	return nil
}

func (x *GetNextModulesListResponse) GetUnassignedModules() []*ModuleIdentity {
	if x != nil {
		return x.UnassignedModules
	}
	return nil
}

func (x *GetNextModulesListResponse) GetUnsetSerialModules() []*ModuleIdentity {
	if x != nil {
		return x.UnsetSerialModules
	}
	return nil
}

type GetNextActiveModulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextActiveModulesRequest) Reset() {
	*x = GetNextActiveModulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveModulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveModulesRequest) ProtoMessage() {}

func (x *GetNextActiveModulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveModulesRequest.ProtoReflect.Descriptor instead.
func (*GetNextActiveModulesRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextActiveModulesRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextActiveModulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts            *Timestamp        `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	ActiveModules []*ModuleIdentity `protobuf:"bytes,2,rep,name=active_modules,json=activeModules,proto3" json:"active_modules,omitempty"`
}

func (x *GetNextActiveModulesResponse) Reset() {
	*x = GetNextActiveModulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveModulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveModulesResponse) ProtoMessage() {}

func (x *GetNextActiveModulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveModulesResponse.ProtoReflect.Descriptor instead.
func (*GetNextActiveModulesResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{4}
}

func (x *GetNextActiveModulesResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextActiveModulesResponse) GetActiveModules() []*ModuleIdentity {
	if x != nil {
		return x.ActiveModules
	}
	return nil
}

type IdentifyModuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleIdentity *ModuleIdentity `protobuf:"bytes,1,opt,name=module_identity,json=moduleIdentity,proto3" json:"module_identity,omitempty"`
}

func (x *IdentifyModuleRequest) Reset() {
	*x = IdentifyModuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentifyModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentifyModuleRequest) ProtoMessage() {}

func (x *IdentifyModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentifyModuleRequest.ProtoReflect.Descriptor instead.
func (*IdentifyModuleRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{5}
}

func (x *IdentifyModuleRequest) GetModuleIdentity() *ModuleIdentity {
	if x != nil {
		return x.ModuleIdentity
	}
	return nil
}

type AssignModuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleIdentity *ModuleIdentity `protobuf:"bytes,1,opt,name=module_identity,json=moduleIdentity,proto3" json:"module_identity,omitempty"`
}

func (x *AssignModuleRequest) Reset() {
	*x = AssignModuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignModuleRequest) ProtoMessage() {}

func (x *AssignModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignModuleRequest.ProtoReflect.Descriptor instead.
func (*AssignModuleRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{6}
}

func (x *AssignModuleRequest) GetModuleIdentity() *ModuleIdentity {
	if x != nil {
		return x.ModuleIdentity
	}
	return nil
}

type ClearModuleAssignmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleIdentity *ModuleIdentity `protobuf:"bytes,1,opt,name=module_identity,json=moduleIdentity,proto3" json:"module_identity,omitempty"`
}

func (x *ClearModuleAssignmentRequest) Reset() {
	*x = ClearModuleAssignmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearModuleAssignmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearModuleAssignmentRequest) ProtoMessage() {}

func (x *ClearModuleAssignmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearModuleAssignmentRequest.ProtoReflect.Descriptor instead.
func (*ClearModuleAssignmentRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{7}
}

func (x *ClearModuleAssignmentRequest) GetModuleIdentity() *ModuleIdentity {
	if x != nil {
		return x.ModuleIdentity
	}
	return nil
}

type SetModuleSerialRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleIdentity *ModuleIdentity `protobuf:"bytes,1,opt,name=module_identity,json=moduleIdentity,proto3" json:"module_identity,omitempty"`
	NewSerial      string          `protobuf:"bytes,2,opt,name=new_serial,json=newSerial,proto3" json:"new_serial,omitempty"`
}

func (x *SetModuleSerialRequest) Reset() {
	*x = SetModuleSerialRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetModuleSerialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetModuleSerialRequest) ProtoMessage() {}

func (x *SetModuleSerialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetModuleSerialRequest.ProtoReflect.Descriptor instead.
func (*SetModuleSerialRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{8}
}

func (x *SetModuleSerialRequest) GetModuleIdentity() *ModuleIdentity {
	if x != nil {
		return x.ModuleIdentity
	}
	return nil
}

func (x *SetModuleSerialRequest) GetNewSerial() string {
	if x != nil {
		return x.NewSerial
	}
	return ""
}

type ModuleDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId        uint32  `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	ModuleSpacingMm float32 `protobuf:"fixed32,2,opt,name=module_spacing_mm,json=moduleSpacingMm,proto3" json:"module_spacing_mm,omitempty"` // spacing between this module and the one to its
	// left. 0 for the first module in a row
	Disabled bool `protobuf:"varint,3,opt,name=disabled,proto3" json:"disabled,omitempty"` // always false for presets
}

func (x *ModuleDefinition) Reset() {
	*x = ModuleDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleDefinition) ProtoMessage() {}

func (x *ModuleDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleDefinition.ProtoReflect.Descriptor instead.
func (*ModuleDefinition) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{9}
}

func (x *ModuleDefinition) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *ModuleDefinition) GetModuleSpacingMm() float32 {
	if x != nil {
		return x.ModuleSpacingMm
	}
	return 0
}

func (x *ModuleDefinition) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

type RowDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId        uint32              `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	Modules      []*ModuleDefinition `protobuf:"bytes,2,rep,name=modules,proto3" json:"modules,omitempty"`
	RowSpacingMm float32             `protobuf:"fixed32,3,opt,name=row_spacing_mm,json=rowSpacingMm,proto3" json:"row_spacing_mm,omitempty"` // spacing between this row and the one to its left. 0
}

func (x *RowDefinition) Reset() {
	*x = RowDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowDefinition) ProtoMessage() {}

func (x *RowDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowDefinition.ProtoReflect.Descriptor instead.
func (*RowDefinition) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{10}
}

func (x *RowDefinition) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *RowDefinition) GetModules() []*ModuleDefinition {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *RowDefinition) GetRowSpacingMm() float32 {
	if x != nil {
		return x.RowSpacingMm
	}
	return 0
}

type BarDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BarLengthMm uint32 `protobuf:"varint,1,opt,name=bar_length_mm,json=barLengthMm,proto3" json:"bar_length_mm,omitempty"`
	Folding     bool   `protobuf:"varint,2,opt,name=folding,proto3" json:"folding,omitempty"`
}

func (x *BarDefinition) Reset() {
	*x = BarDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarDefinition) ProtoMessage() {}

func (x *BarDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarDefinition.ProtoReflect.Descriptor instead.
func (*BarDefinition) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{11}
}

func (x *BarDefinition) GetBarLengthMm() uint32 {
	if x != nil {
		return x.BarLengthMm
	}
	return 0
}

func (x *BarDefinition) GetFolding() bool {
	if x != nil {
		return x.Folding
	}
	return false
}

type RobotDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rows          []*RowDefinition `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	BarDefinition *BarDefinition   `protobuf:"bytes,3,opt,name=bar_definition,json=barDefinition,proto3" json:"bar_definition,omitempty"`
}

func (x *RobotDefinition) Reset() {
	*x = RobotDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotDefinition) ProtoMessage() {}

func (x *RobotDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotDefinition.ProtoReflect.Descriptor instead.
func (*RobotDefinition) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{12}
}

func (x *RobotDefinition) GetRows() []*RowDefinition {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *RobotDefinition) GetBarDefinition() *BarDefinition {
	if x != nil {
		return x.BarDefinition
	}
	return nil
}

type Preset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid        string           `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	DisplayName string           `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Definition  *RobotDefinition `protobuf:"bytes,3,opt,name=definition,proto3" json:"definition,omitempty"`
}

func (x *Preset) Reset() {
	*x = Preset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Preset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Preset) ProtoMessage() {}

func (x *Preset) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Preset.ProtoReflect.Descriptor instead.
func (*Preset) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{13}
}

func (x *Preset) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Preset) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Preset) GetDefinition() *RobotDefinition {
	if x != nil {
		return x.Definition
	}
	return nil
}

type GetPresetsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *GetPresetsListRequest) Reset() {
	*x = GetPresetsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPresetsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetsListRequest) ProtoMessage() {}

func (x *GetPresetsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetsListRequest.ProtoReflect.Descriptor instead.
func (*GetPresetsListRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{14}
}

func (x *GetPresetsListRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type GetPresetsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Presets []*Preset `protobuf:"bytes,1,rep,name=presets,proto3" json:"presets,omitempty"`
}

func (x *GetPresetsListResponse) Reset() {
	*x = GetPresetsListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPresetsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetsListResponse) ProtoMessage() {}

func (x *GetPresetsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetsListResponse.ProtoReflect.Descriptor instead.
func (*GetPresetsListResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{15}
}

func (x *GetPresetsListResponse) GetPresets() []*Preset {
	if x != nil {
		return x.Presets
	}
	return nil
}

type GetCurrentRobotDefinitionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentDefinition *RobotDefinition `protobuf:"bytes,1,opt,name=current_definition,json=currentDefinition,proto3,oneof" json:"current_definition,omitempty"`
}

func (x *GetCurrentRobotDefinitionResponse) Reset() {
	*x = GetCurrentRobotDefinitionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrentRobotDefinitionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentRobotDefinitionResponse) ProtoMessage() {}

func (x *GetCurrentRobotDefinitionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentRobotDefinitionResponse.ProtoReflect.Descriptor instead.
func (*GetCurrentRobotDefinitionResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{16}
}

func (x *GetCurrentRobotDefinitionResponse) GetCurrentDefinition() *RobotDefinition {
	if x != nil {
		return x.CurrentDefinition
	}
	return nil
}

type SetCurrentRobotDefinitionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentDefinition *RobotDefinition `protobuf:"bytes,1,opt,name=current_definition,json=currentDefinition,proto3" json:"current_definition,omitempty"`
}

func (x *SetCurrentRobotDefinitionRequest) Reset() {
	*x = SetCurrentRobotDefinitionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_module_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCurrentRobotDefinitionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCurrentRobotDefinitionRequest) ProtoMessage() {}

func (x *SetCurrentRobotDefinitionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_module_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCurrentRobotDefinitionRequest.ProtoReflect.Descriptor instead.
func (*SetCurrentRobotDefinitionRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_module_proto_rawDescGZIP(), []int{17}
}

func (x *SetCurrentRobotDefinitionRequest) GetCurrentDefinition() *RobotDefinition {
	if x != nil {
		return x.CurrentDefinition
	}
	return nil
}

var File_frontend_proto_module_proto protoreflect.FileDescriptor

var file_frontend_proto_module_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x38, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0x4c, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0xd1, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x51, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x55, 0x0a, 0x12, 0x75,
	0x6e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x11, 0x75, 0x6e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x58, 0x0a, 0x14, 0x75, 0x6e, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x12, 0x75, 0x6e, 0x73, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x4e, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0x9e, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x4d,
	0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0d,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x68, 0x0a,
	0x15, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x66, 0x0a, 0x13, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f,
	0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22,
	0x6f, 0x0a, 0x1c, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x4f, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x22, 0x88, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x0f, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0e, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x6e, 0x65, 0x77, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0x77, 0x0a, 0x10, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x6d,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53,
	0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x0d, 0x52, 0x6f, 0x77, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x42, 0x0a,
	0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x72, 0x6f, 0x77, 0x53, 0x70,
	0x61, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x6d, 0x22, 0x4d, 0x0a, 0x0d, 0x42, 0x61, 0x72, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x72, 0x5f,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x6d, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x62, 0x61, 0x72, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x4d, 0x6d, 0x12, 0x18, 0x0a, 0x07,
	0x66, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x66,
	0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x9a, 0x01, 0x0a, 0x0f, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x04, 0x72, 0x6f,
	0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x2e, 0x52, 0x6f, 0x77, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x04, 0x72, 0x6f, 0x77, 0x73, 0x12, 0x4c, 0x0a, 0x0e, 0x62, 0x61, 0x72, 0x5f, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x42, 0x61, 0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x62, 0x61, 0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x88, 0x01, 0x0a, 0x06, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x22, 0x52, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a,
	0x07, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x52, 0x07,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a,
	0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x7a, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xf8, 0x07,
	0x0a, 0x17, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x2d, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x58, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x6a, 0x0a, 0x15, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5e,
	0x0a, 0x0f, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6f,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x73, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_frontend_proto_module_proto_rawDescOnce sync.Once
	file_frontend_proto_module_proto_rawDescData = file_frontend_proto_module_proto_rawDesc
)

func file_frontend_proto_module_proto_rawDescGZIP() []byte {
	file_frontend_proto_module_proto_rawDescOnce.Do(func() {
		file_frontend_proto_module_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_module_proto_rawDescData)
	})
	return file_frontend_proto_module_proto_rawDescData
}

var file_frontend_proto_module_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_frontend_proto_module_proto_goTypes = []interface{}{
	(*ModuleIdentity)(nil),                    // 0: carbon.frontend.module.ModuleIdentity
	(*GetNextModulesListRequest)(nil),         // 1: carbon.frontend.module.GetNextModulesListRequest
	(*GetNextModulesListResponse)(nil),        // 2: carbon.frontend.module.GetNextModulesListResponse
	(*GetNextActiveModulesRequest)(nil),       // 3: carbon.frontend.module.GetNextActiveModulesRequest
	(*GetNextActiveModulesResponse)(nil),      // 4: carbon.frontend.module.GetNextActiveModulesResponse
	(*IdentifyModuleRequest)(nil),             // 5: carbon.frontend.module.IdentifyModuleRequest
	(*AssignModuleRequest)(nil),               // 6: carbon.frontend.module.AssignModuleRequest
	(*ClearModuleAssignmentRequest)(nil),      // 7: carbon.frontend.module.ClearModuleAssignmentRequest
	(*SetModuleSerialRequest)(nil),            // 8: carbon.frontend.module.SetModuleSerialRequest
	(*ModuleDefinition)(nil),                  // 9: carbon.frontend.module.ModuleDefinition
	(*RowDefinition)(nil),                     // 10: carbon.frontend.module.RowDefinition
	(*BarDefinition)(nil),                     // 11: carbon.frontend.module.BarDefinition
	(*RobotDefinition)(nil),                   // 12: carbon.frontend.module.RobotDefinition
	(*Preset)(nil),                            // 13: carbon.frontend.module.Preset
	(*GetPresetsListRequest)(nil),             // 14: carbon.frontend.module.GetPresetsListRequest
	(*GetPresetsListResponse)(nil),            // 15: carbon.frontend.module.GetPresetsListResponse
	(*GetCurrentRobotDefinitionResponse)(nil), // 16: carbon.frontend.module.GetCurrentRobotDefinitionResponse
	(*SetCurrentRobotDefinitionRequest)(nil),  // 17: carbon.frontend.module.SetCurrentRobotDefinitionRequest
	(*Timestamp)(nil),                         // 18: carbon.frontend.util.Timestamp
	(*Empty)(nil),                             // 19: carbon.frontend.util.Empty
}
var file_frontend_proto_module_proto_depIdxs = []int32{
	18, // 0: carbon.frontend.module.GetNextModulesListRequest.ts:type_name -> carbon.frontend.util.Timestamp
	18, // 1: carbon.frontend.module.GetNextModulesListResponse.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 2: carbon.frontend.module.GetNextModulesListResponse.assigned_modules:type_name -> carbon.frontend.module.ModuleIdentity
	0,  // 3: carbon.frontend.module.GetNextModulesListResponse.unassigned_modules:type_name -> carbon.frontend.module.ModuleIdentity
	0,  // 4: carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules:type_name -> carbon.frontend.module.ModuleIdentity
	18, // 5: carbon.frontend.module.GetNextActiveModulesRequest.ts:type_name -> carbon.frontend.util.Timestamp
	18, // 6: carbon.frontend.module.GetNextActiveModulesResponse.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 7: carbon.frontend.module.GetNextActiveModulesResponse.active_modules:type_name -> carbon.frontend.module.ModuleIdentity
	0,  // 8: carbon.frontend.module.IdentifyModuleRequest.module_identity:type_name -> carbon.frontend.module.ModuleIdentity
	0,  // 9: carbon.frontend.module.AssignModuleRequest.module_identity:type_name -> carbon.frontend.module.ModuleIdentity
	0,  // 10: carbon.frontend.module.ClearModuleAssignmentRequest.module_identity:type_name -> carbon.frontend.module.ModuleIdentity
	0,  // 11: carbon.frontend.module.SetModuleSerialRequest.module_identity:type_name -> carbon.frontend.module.ModuleIdentity
	9,  // 12: carbon.frontend.module.RowDefinition.modules:type_name -> carbon.frontend.module.ModuleDefinition
	10, // 13: carbon.frontend.module.RobotDefinition.rows:type_name -> carbon.frontend.module.RowDefinition
	11, // 14: carbon.frontend.module.RobotDefinition.bar_definition:type_name -> carbon.frontend.module.BarDefinition
	12, // 15: carbon.frontend.module.Preset.definition:type_name -> carbon.frontend.module.RobotDefinition
	13, // 16: carbon.frontend.module.GetPresetsListResponse.presets:type_name -> carbon.frontend.module.Preset
	12, // 17: carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition:type_name -> carbon.frontend.module.RobotDefinition
	12, // 18: carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition:type_name -> carbon.frontend.module.RobotDefinition
	1,  // 19: carbon.frontend.module.ModuleAssignmentService.GetNextModulesList:input_type -> carbon.frontend.module.GetNextModulesListRequest
	3,  // 20: carbon.frontend.module.ModuleAssignmentService.GetNextActiveModules:input_type -> carbon.frontend.module.GetNextActiveModulesRequest
	5,  // 21: carbon.frontend.module.ModuleAssignmentService.IdentifyModule:input_type -> carbon.frontend.module.IdentifyModuleRequest
	6,  // 22: carbon.frontend.module.ModuleAssignmentService.AssignModule:input_type -> carbon.frontend.module.AssignModuleRequest
	7,  // 23: carbon.frontend.module.ModuleAssignmentService.ClearModuleAssignment:input_type -> carbon.frontend.module.ClearModuleAssignmentRequest
	8,  // 24: carbon.frontend.module.ModuleAssignmentService.SetModuleSerial:input_type -> carbon.frontend.module.SetModuleSerialRequest
	14, // 25: carbon.frontend.module.ModuleAssignmentService.GetPresetsList:input_type -> carbon.frontend.module.GetPresetsListRequest
	19, // 26: carbon.frontend.module.ModuleAssignmentService.GetCurrentRobotDefinition:input_type -> carbon.frontend.util.Empty
	17, // 27: carbon.frontend.module.ModuleAssignmentService.SetCurrentRobotDefinition:input_type -> carbon.frontend.module.SetCurrentRobotDefinitionRequest
	2,  // 28: carbon.frontend.module.ModuleAssignmentService.GetNextModulesList:output_type -> carbon.frontend.module.GetNextModulesListResponse
	4,  // 29: carbon.frontend.module.ModuleAssignmentService.GetNextActiveModules:output_type -> carbon.frontend.module.GetNextActiveModulesResponse
	19, // 30: carbon.frontend.module.ModuleAssignmentService.IdentifyModule:output_type -> carbon.frontend.util.Empty
	19, // 31: carbon.frontend.module.ModuleAssignmentService.AssignModule:output_type -> carbon.frontend.util.Empty
	19, // 32: carbon.frontend.module.ModuleAssignmentService.ClearModuleAssignment:output_type -> carbon.frontend.util.Empty
	19, // 33: carbon.frontend.module.ModuleAssignmentService.SetModuleSerial:output_type -> carbon.frontend.util.Empty
	15, // 34: carbon.frontend.module.ModuleAssignmentService.GetPresetsList:output_type -> carbon.frontend.module.GetPresetsListResponse
	16, // 35: carbon.frontend.module.ModuleAssignmentService.GetCurrentRobotDefinition:output_type -> carbon.frontend.module.GetCurrentRobotDefinitionResponse
	19, // 36: carbon.frontend.module.ModuleAssignmentService.SetCurrentRobotDefinition:output_type -> carbon.frontend.util.Empty
	28, // [28:37] is the sub-list for method output_type
	19, // [19:28] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_frontend_proto_module_proto_init() }
func file_frontend_proto_module_proto_init() {
	if File_frontend_proto_module_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_module_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleIdentity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextModulesListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextModulesListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveModulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveModulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentifyModuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignModuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearModuleAssignmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetModuleSerialRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Preset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPresetsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPresetsListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrentRobotDefinitionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_module_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCurrentRobotDefinitionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_module_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_module_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_module_proto_goTypes,
		DependencyIndexes: file_frontend_proto_module_proto_depIdxs,
		MessageInfos:      file_frontend_proto_module_proto_msgTypes,
	}.Build()
	File_frontend_proto_module_proto = out.File
	file_frontend_proto_module_proto_rawDesc = nil
	file_frontend_proto_module_proto_goTypes = nil
	file_frontend_proto_module_proto_depIdxs = nil
}

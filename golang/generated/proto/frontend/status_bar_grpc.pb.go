// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/status_bar.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	StatusBarService_GetNextStatus_FullMethodName   = "/carbon.frontend.status_bar.StatusBarService/GetNextStatus"
	StatusBarService_ReportIssue_FullMethodName     = "/carbon.frontend.status_bar.StatusBarService/ReportIssue"
	StatusBarService_GetSupportPhone_FullMethodName = "/carbon.frontend.status_bar.StatusBarService/GetSupportPhone"
)

// StatusBarServiceClient is the client API for StatusBarService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StatusBarServiceClient interface {
	GetNextStatus(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*StatusBarMessage, error)
	ReportIssue(ctx context.Context, in *ReportIssueRequest, opts ...grpc.CallOption) (*Empty, error)
	GetSupportPhone(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*SupportPhoneMessage, error)
}

type statusBarServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStatusBarServiceClient(cc grpc.ClientConnInterface) StatusBarServiceClient {
	return &statusBarServiceClient{cc}
}

func (c *statusBarServiceClient) GetNextStatus(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*StatusBarMessage, error) {
	out := new(StatusBarMessage)
	err := c.cc.Invoke(ctx, StatusBarService_GetNextStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusBarServiceClient) ReportIssue(ctx context.Context, in *ReportIssueRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, StatusBarService_ReportIssue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statusBarServiceClient) GetSupportPhone(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*SupportPhoneMessage, error) {
	out := new(SupportPhoneMessage)
	err := c.cc.Invoke(ctx, StatusBarService_GetSupportPhone_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatusBarServiceServer is the server API for StatusBarService service.
// All implementations must embed UnimplementedStatusBarServiceServer
// for forward compatibility
type StatusBarServiceServer interface {
	GetNextStatus(context.Context, *Timestamp) (*StatusBarMessage, error)
	ReportIssue(context.Context, *ReportIssueRequest) (*Empty, error)
	GetSupportPhone(context.Context, *Empty) (*SupportPhoneMessage, error)
	mustEmbedUnimplementedStatusBarServiceServer()
}

// UnimplementedStatusBarServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStatusBarServiceServer struct {
}

func (UnimplementedStatusBarServiceServer) GetNextStatus(context.Context, *Timestamp) (*StatusBarMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextStatus not implemented")
}
func (UnimplementedStatusBarServiceServer) ReportIssue(context.Context, *ReportIssueRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportIssue not implemented")
}
func (UnimplementedStatusBarServiceServer) GetSupportPhone(context.Context, *Empty) (*SupportPhoneMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportPhone not implemented")
}
func (UnimplementedStatusBarServiceServer) mustEmbedUnimplementedStatusBarServiceServer() {}

// UnsafeStatusBarServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatusBarServiceServer will
// result in compilation errors.
type UnsafeStatusBarServiceServer interface {
	mustEmbedUnimplementedStatusBarServiceServer()
}

func RegisterStatusBarServiceServer(s grpc.ServiceRegistrar, srv StatusBarServiceServer) {
	s.RegisterService(&StatusBarService_ServiceDesc, srv)
}

func _StatusBarService_GetNextStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusBarServiceServer).GetNextStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusBarService_GetNextStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusBarServiceServer).GetNextStatus(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusBarService_ReportIssue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportIssueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusBarServiceServer).ReportIssue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusBarService_ReportIssue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusBarServiceServer).ReportIssue(ctx, req.(*ReportIssueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatusBarService_GetSupportPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatusBarServiceServer).GetSupportPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatusBarService_GetSupportPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatusBarServiceServer).GetSupportPhone(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// StatusBarService_ServiceDesc is the grpc.ServiceDesc for StatusBarService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StatusBarService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.status_bar.StatusBarService",
	HandlerType: (*StatusBarServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextStatus",
			Handler:    _StatusBarService_GetNextStatus_Handler,
		},
		{
			MethodName: "ReportIssue",
			Handler:    _StatusBarService_ReportIssue_Handler,
		},
		{
			MethodName: "GetSupportPhone",
			Handler:    _StatusBarService_GetSupportPhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/status_bar.proto",
}

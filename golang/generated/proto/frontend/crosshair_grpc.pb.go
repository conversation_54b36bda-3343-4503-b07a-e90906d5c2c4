// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/crosshair.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CrosshairService_StartAutoCalibrateCrosshair_FullMethodName     = "/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateCrosshair"
	CrosshairService_StartAutoCalibrateAllCrosshairs_FullMethodName = "/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateAllCrosshairs"
	CrosshairService_StopAutoCalibrate_FullMethodName               = "/carbon.frontend.crosshair.CrosshairService/StopAutoCalibrate"
	CrosshairService_GetNextCrosshairState_FullMethodName           = "/carbon.frontend.crosshair.CrosshairService/GetNextCrosshairState"
	CrosshairService_SetCrosshairPosition_FullMethodName            = "/carbon.frontend.crosshair.CrosshairService/SetCrosshairPosition"
	CrosshairService_MoveScanner_FullMethodName                     = "/carbon.frontend.crosshair.CrosshairService/MoveScanner"
	CrosshairService_GetNextAutoCrossHairCalState_FullMethodName    = "/carbon.frontend.crosshair.CrosshairService/GetNextAutoCrossHairCalState"
)

// CrosshairServiceClient is the client API for CrosshairService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CrosshairServiceClient interface {
	StartAutoCalibrateCrosshair(ctx context.Context, in *CameraRequest, opts ...grpc.CallOption) (*Empty, error)
	StartAutoCalibrateAllCrosshairs(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StopAutoCalibrate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetNextCrosshairState(ctx context.Context, in *CrosshairPositionRequest, opts ...grpc.CallOption) (*CrosshairPositionState, error)
	SetCrosshairPosition(ctx context.Context, in *SetCrosshairPositionRequest, opts ...grpc.CallOption) (*Empty, error)
	MoveScanner(ctx context.Context, in *MoveScannerRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextAutoCrossHairCalState(ctx context.Context, in *AutoCrossHairCalStateRequest, opts ...grpc.CallOption) (*AutoCrossHairCalStateResponse, error)
}

type crosshairServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCrosshairServiceClient(cc grpc.ClientConnInterface) CrosshairServiceClient {
	return &crosshairServiceClient{cc}
}

func (c *crosshairServiceClient) StartAutoCalibrateCrosshair(ctx context.Context, in *CameraRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CrosshairService_StartAutoCalibrateCrosshair_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crosshairServiceClient) StartAutoCalibrateAllCrosshairs(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CrosshairService_StartAutoCalibrateAllCrosshairs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crosshairServiceClient) StopAutoCalibrate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CrosshairService_StopAutoCalibrate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crosshairServiceClient) GetNextCrosshairState(ctx context.Context, in *CrosshairPositionRequest, opts ...grpc.CallOption) (*CrosshairPositionState, error) {
	out := new(CrosshairPositionState)
	err := c.cc.Invoke(ctx, CrosshairService_GetNextCrosshairState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crosshairServiceClient) SetCrosshairPosition(ctx context.Context, in *SetCrosshairPositionRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CrosshairService_SetCrosshairPosition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crosshairServiceClient) MoveScanner(ctx context.Context, in *MoveScannerRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CrosshairService_MoveScanner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crosshairServiceClient) GetNextAutoCrossHairCalState(ctx context.Context, in *AutoCrossHairCalStateRequest, opts ...grpc.CallOption) (*AutoCrossHairCalStateResponse, error) {
	out := new(AutoCrossHairCalStateResponse)
	err := c.cc.Invoke(ctx, CrosshairService_GetNextAutoCrossHairCalState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CrosshairServiceServer is the server API for CrosshairService service.
// All implementations must embed UnimplementedCrosshairServiceServer
// for forward compatibility
type CrosshairServiceServer interface {
	StartAutoCalibrateCrosshair(context.Context, *CameraRequest) (*Empty, error)
	StartAutoCalibrateAllCrosshairs(context.Context, *Empty) (*Empty, error)
	StopAutoCalibrate(context.Context, *Empty) (*Empty, error)
	GetNextCrosshairState(context.Context, *CrosshairPositionRequest) (*CrosshairPositionState, error)
	SetCrosshairPosition(context.Context, *SetCrosshairPositionRequest) (*Empty, error)
	MoveScanner(context.Context, *MoveScannerRequest) (*Empty, error)
	GetNextAutoCrossHairCalState(context.Context, *AutoCrossHairCalStateRequest) (*AutoCrossHairCalStateResponse, error)
	mustEmbedUnimplementedCrosshairServiceServer()
}

// UnimplementedCrosshairServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCrosshairServiceServer struct {
}

func (UnimplementedCrosshairServiceServer) StartAutoCalibrateCrosshair(context.Context, *CameraRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAutoCalibrateCrosshair not implemented")
}
func (UnimplementedCrosshairServiceServer) StartAutoCalibrateAllCrosshairs(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAutoCalibrateAllCrosshairs not implemented")
}
func (UnimplementedCrosshairServiceServer) StopAutoCalibrate(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopAutoCalibrate not implemented")
}
func (UnimplementedCrosshairServiceServer) GetNextCrosshairState(context.Context, *CrosshairPositionRequest) (*CrosshairPositionState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextCrosshairState not implemented")
}
func (UnimplementedCrosshairServiceServer) SetCrosshairPosition(context.Context, *SetCrosshairPositionRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCrosshairPosition not implemented")
}
func (UnimplementedCrosshairServiceServer) MoveScanner(context.Context, *MoveScannerRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MoveScanner not implemented")
}
func (UnimplementedCrosshairServiceServer) GetNextAutoCrossHairCalState(context.Context, *AutoCrossHairCalStateRequest) (*AutoCrossHairCalStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAutoCrossHairCalState not implemented")
}
func (UnimplementedCrosshairServiceServer) mustEmbedUnimplementedCrosshairServiceServer() {}

// UnsafeCrosshairServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CrosshairServiceServer will
// result in compilation errors.
type UnsafeCrosshairServiceServer interface {
	mustEmbedUnimplementedCrosshairServiceServer()
}

func RegisterCrosshairServiceServer(s grpc.ServiceRegistrar, srv CrosshairServiceServer) {
	s.RegisterService(&CrosshairService_ServiceDesc, srv)
}

func _CrosshairService_StartAutoCalibrateCrosshair_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CameraRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).StartAutoCalibrateCrosshair(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_StartAutoCalibrateCrosshair_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).StartAutoCalibrateCrosshair(ctx, req.(*CameraRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrosshairService_StartAutoCalibrateAllCrosshairs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).StartAutoCalibrateAllCrosshairs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_StartAutoCalibrateAllCrosshairs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).StartAutoCalibrateAllCrosshairs(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrosshairService_StopAutoCalibrate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).StopAutoCalibrate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_StopAutoCalibrate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).StopAutoCalibrate(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrosshairService_GetNextCrosshairState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CrosshairPositionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).GetNextCrosshairState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_GetNextCrosshairState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).GetNextCrosshairState(ctx, req.(*CrosshairPositionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrosshairService_SetCrosshairPosition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCrosshairPositionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).SetCrosshairPosition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_SetCrosshairPosition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).SetCrosshairPosition(ctx, req.(*SetCrosshairPositionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrosshairService_MoveScanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveScannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).MoveScanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_MoveScanner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).MoveScanner(ctx, req.(*MoveScannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrosshairService_GetNextAutoCrossHairCalState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoCrossHairCalStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrosshairServiceServer).GetNextAutoCrossHairCalState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrosshairService_GetNextAutoCrossHairCalState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrosshairServiceServer).GetNextAutoCrossHairCalState(ctx, req.(*AutoCrossHairCalStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CrosshairService_ServiceDesc is the grpc.ServiceDesc for CrosshairService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CrosshairService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.crosshair.CrosshairService",
	HandlerType: (*CrosshairServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartAutoCalibrateCrosshair",
			Handler:    _CrosshairService_StartAutoCalibrateCrosshair_Handler,
		},
		{
			MethodName: "StartAutoCalibrateAllCrosshairs",
			Handler:    _CrosshairService_StartAutoCalibrateAllCrosshairs_Handler,
		},
		{
			MethodName: "StopAutoCalibrate",
			Handler:    _CrosshairService_StopAutoCalibrate_Handler,
		},
		{
			MethodName: "GetNextCrosshairState",
			Handler:    _CrosshairService_GetNextCrosshairState_Handler,
		},
		{
			MethodName: "SetCrosshairPosition",
			Handler:    _CrosshairService_SetCrosshairPosition_Handler,
		},
		{
			MethodName: "MoveScanner",
			Handler:    _CrosshairService_MoveScanner_Handler,
		},
		{
			MethodName: "GetNextAutoCrossHairCalState",
			Handler:    _CrosshairService_GetNextAutoCrossHairCalState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/crosshair.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/category_collection.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	category "github.com/carbonrobotics/robot/golang/generated/proto/category"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNextCategoryCollectionsDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextCategoryCollectionsDataRequest) Reset() {
	*x = GetNextCategoryCollectionsDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_collection_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCategoryCollectionsDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCategoryCollectionsDataRequest) ProtoMessage() {}

func (x *GetNextCategoryCollectionsDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_collection_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCategoryCollectionsDataRequest.ProtoReflect.Descriptor instead.
func (*GetNextCategoryCollectionsDataRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_collection_proto_rawDescGZIP(), []int{0}
}

func (x *GetNextCategoryCollectionsDataRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextCategoryCollectionsDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                  *Timestamp                     `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	CategoryCollections []*category.CategoryCollection `protobuf:"bytes,3,rep,name=category_collections,json=categoryCollections,proto3" json:"category_collections,omitempty"`
}

func (x *GetNextCategoryCollectionsDataResponse) Reset() {
	*x = GetNextCategoryCollectionsDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_collection_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCategoryCollectionsDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCategoryCollectionsDataResponse) ProtoMessage() {}

func (x *GetNextCategoryCollectionsDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_collection_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCategoryCollectionsDataResponse.ProtoReflect.Descriptor instead.
func (*GetNextCategoryCollectionsDataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_collection_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextCategoryCollectionsDataResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextCategoryCollectionsDataResponse) GetCategoryCollections() []*category.CategoryCollection {
	if x != nil {
		return x.CategoryCollections
	}
	return nil
}

type GetNextActiveCategoryCollectionIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextActiveCategoryCollectionIdRequest) Reset() {
	*x = GetNextActiveCategoryCollectionIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_collection_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveCategoryCollectionIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveCategoryCollectionIdRequest) ProtoMessage() {}

func (x *GetNextActiveCategoryCollectionIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_collection_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveCategoryCollectionIdRequest.ProtoReflect.Descriptor instead.
func (*GetNextActiveCategoryCollectionIdRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_collection_proto_rawDescGZIP(), []int{2}
}

func (x *GetNextActiveCategoryCollectionIdRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextActiveCategoryCollectionIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid                   string     `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Ts                     *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	ReloadRequired         bool       `protobuf:"varint,3,opt,name=reload_required,json=reloadRequired,proto3" json:"reload_required,omitempty"`
	LastUpdatedTimestampMs int64      `protobuf:"varint,4,opt,name=last_updated_timestamp_ms,json=lastUpdatedTimestampMs,proto3" json:"last_updated_timestamp_ms,omitempty"`
}

func (x *GetNextActiveCategoryCollectionIdResponse) Reset() {
	*x = GetNextActiveCategoryCollectionIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_collection_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveCategoryCollectionIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveCategoryCollectionIdResponse) ProtoMessage() {}

func (x *GetNextActiveCategoryCollectionIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_collection_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveCategoryCollectionIdResponse.ProtoReflect.Descriptor instead.
func (*GetNextActiveCategoryCollectionIdResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_collection_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextActiveCategoryCollectionIdResponse) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *GetNextActiveCategoryCollectionIdResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextActiveCategoryCollectionIdResponse) GetReloadRequired() bool {
	if x != nil {
		return x.ReloadRequired
	}
	return false
}

func (x *GetNextActiveCategoryCollectionIdResponse) GetLastUpdatedTimestampMs() int64 {
	if x != nil {
		return x.LastUpdatedTimestampMs
	}
	return 0
}

type SetActiveCategoryCollectionIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string     `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Ts   *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *SetActiveCategoryCollectionIdRequest) Reset() {
	*x = SetActiveCategoryCollectionIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_collection_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveCategoryCollectionIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveCategoryCollectionIdRequest) ProtoMessage() {}

func (x *SetActiveCategoryCollectionIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_collection_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveCategoryCollectionIdRequest.ProtoReflect.Descriptor instead.
func (*SetActiveCategoryCollectionIdRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_collection_proto_rawDescGZIP(), []int{4}
}

func (x *SetActiveCategoryCollectionIdRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *SetActiveCategoryCollectionIdRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

var File_frontend_proto_category_collection_proto protoreflect.FileDescriptor

var file_frontend_proto_category_collection_proto_rawDesc = []byte{
	0x0a, 0x28, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x23, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x25, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x02, 0x74, 0x73, 0x22, 0xb1, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12,
	0x56, 0x0a, 0x14, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x13, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x5b, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x22, 0xd4, 0x01, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x72, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x39, 0x0a, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x6b, 0x0a, 0x24, 0x53,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x32, 0xfc, 0x04, 0x0a, 0x19, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb9, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xc2, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x4d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x49, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x54, 0x0a, 0x18, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_frontend_proto_category_collection_proto_rawDescOnce sync.Once
	file_frontend_proto_category_collection_proto_rawDescData = file_frontend_proto_category_collection_proto_rawDesc
)

func file_frontend_proto_category_collection_proto_rawDescGZIP() []byte {
	file_frontend_proto_category_collection_proto_rawDescOnce.Do(func() {
		file_frontend_proto_category_collection_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_category_collection_proto_rawDescData)
	})
	return file_frontend_proto_category_collection_proto_rawDescData
}

var file_frontend_proto_category_collection_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_frontend_proto_category_collection_proto_goTypes = []interface{}{
	(*GetNextCategoryCollectionsDataRequest)(nil),     // 0: carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest
	(*GetNextCategoryCollectionsDataResponse)(nil),    // 1: carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse
	(*GetNextActiveCategoryCollectionIdRequest)(nil),  // 2: carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest
	(*GetNextActiveCategoryCollectionIdResponse)(nil), // 3: carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse
	(*SetActiveCategoryCollectionIdRequest)(nil),      // 4: carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest
	(*Timestamp)(nil),                   // 5: carbon.frontend.util.Timestamp
	(*category.CategoryCollection)(nil), // 6: carbon.category.CategoryCollection
	(*Empty)(nil),                       // 7: carbon.frontend.util.Empty
}
var file_frontend_proto_category_collection_proto_depIdxs = []int32{
	5,  // 0: carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts:type_name -> carbon.frontend.util.Timestamp
	5,  // 1: carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts:type_name -> carbon.frontend.util.Timestamp
	6,  // 2: carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections:type_name -> carbon.category.CategoryCollection
	5,  // 3: carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts:type_name -> carbon.frontend.util.Timestamp
	5,  // 4: carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts:type_name -> carbon.frontend.util.Timestamp
	5,  // 5: carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 6: carbon.frontend.category_collection.CategoryCollectionService.GetNextCategoryCollectionsData:input_type -> carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest
	2,  // 7: carbon.frontend.category_collection.CategoryCollectionService.GetNextActiveCategoryCollectionId:input_type -> carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest
	4,  // 8: carbon.frontend.category_collection.CategoryCollectionService.SetActiveCategoryCollectionId:input_type -> carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest
	7,  // 9: carbon.frontend.category_collection.CategoryCollectionService.ReloadCategoryCollection:input_type -> carbon.frontend.util.Empty
	1,  // 10: carbon.frontend.category_collection.CategoryCollectionService.GetNextCategoryCollectionsData:output_type -> carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse
	3,  // 11: carbon.frontend.category_collection.CategoryCollectionService.GetNextActiveCategoryCollectionId:output_type -> carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse
	7,  // 12: carbon.frontend.category_collection.CategoryCollectionService.SetActiveCategoryCollectionId:output_type -> carbon.frontend.util.Empty
	7,  // 13: carbon.frontend.category_collection.CategoryCollectionService.ReloadCategoryCollection:output_type -> carbon.frontend.util.Empty
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_frontend_proto_category_collection_proto_init() }
func file_frontend_proto_category_collection_proto_init() {
	if File_frontend_proto_category_collection_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_category_collection_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCategoryCollectionsDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_category_collection_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCategoryCollectionsDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_category_collection_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveCategoryCollectionIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_category_collection_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveCategoryCollectionIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_category_collection_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveCategoryCollectionIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_category_collection_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_category_collection_proto_goTypes,
		DependencyIndexes: file_frontend_proto_category_collection_proto_depIdxs,
		MessageInfos:      file_frontend_proto_category_collection_proto_msgTypes,
	}.Build()
	File_frontend_proto_category_collection_proto = out.File
	file_frontend_proto_category_collection_proto_rawDesc = nil
	file_frontend_proto_category_collection_proto_goTypes = nil
	file_frontend_proto_category_collection_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/banding.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	aimbot "github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	BandingService_LoadBandingDefs_FullMethodName                    = "/carbon.frontend.banding.BandingService/LoadBandingDefs"
	BandingService_SaveBandingDef_FullMethodName                     = "/carbon.frontend.banding.BandingService/SaveBandingDef"
	BandingService_DeleteBandingDef_FullMethodName                   = "/carbon.frontend.banding.BandingService/DeleteBandingDef"
	BandingService_SetActiveBandingDef_FullMethodName                = "/carbon.frontend.banding.BandingService/SetActiveBandingDef"
	BandingService_GetActiveBandingDef_FullMethodName                = "/carbon.frontend.banding.BandingService/GetActiveBandingDef"
	BandingService_GetNextVisualizationData_FullMethodName           = "/carbon.frontend.banding.BandingService/GetNextVisualizationData"
	BandingService_GetNextVisualizationData2_FullMethodName          = "/carbon.frontend.banding.BandingService/GetNextVisualizationData2"
	BandingService_GetNextVisualizationDataForAllRows_FullMethodName = "/carbon.frontend.banding.BandingService/GetNextVisualizationDataForAllRows"
	BandingService_GetDimensions_FullMethodName                      = "/carbon.frontend.banding.BandingService/GetDimensions"
	BandingService_SetBandingEnabled_FullMethodName                  = "/carbon.frontend.banding.BandingService/SetBandingEnabled"
	BandingService_IsBandingEnabled_FullMethodName                   = "/carbon.frontend.banding.BandingService/IsBandingEnabled"
	BandingService_SetDynamicBandingEnabled_FullMethodName           = "/carbon.frontend.banding.BandingService/SetDynamicBandingEnabled"
	BandingService_IsDynamicBandingEnabled_FullMethodName            = "/carbon.frontend.banding.BandingService/IsDynamicBandingEnabled"
	BandingService_GetVisualizationMetadata_FullMethodName           = "/carbon.frontend.banding.BandingService/GetVisualizationMetadata"
	BandingService_GetNextBandingState_FullMethodName                = "/carbon.frontend.banding.BandingService/GetNextBandingState"
)

// BandingServiceClient is the client API for BandingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BandingServiceClient interface {
	LoadBandingDefs(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LoadBandingDefsResponse, error)
	SaveBandingDef(ctx context.Context, in *SaveBandingDefRequest, opts ...grpc.CallOption) (*Empty, error)
	DeleteBandingDef(ctx context.Context, in *DeleteBandingDefRequest, opts ...grpc.CallOption) (*Empty, error)
	SetActiveBandingDef(ctx context.Context, in *SetActiveBandingDefRequest, opts ...grpc.CallOption) (*Empty, error)
	GetActiveBandingDef(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetActiveBandingDefResponse, error)
	GetNextVisualizationData(ctx context.Context, in *GetNextVisualizationDataRequest, opts ...grpc.CallOption) (*GetNextVisualizationDataResponse, error)
	GetNextVisualizationData2(ctx context.Context, in *GetNextVisualizationDataRequest, opts ...grpc.CallOption) (*GetNextVisualizationData2Response, error)
	GetNextVisualizationDataForAllRows(ctx context.Context, in *GetNextVisualizationDataForAllRowsRequest, opts ...grpc.CallOption) (*GetNextVisualizationDataForAllRowsResponse, error)
	GetDimensions(ctx context.Context, in *GetDimensionsRequest, opts ...grpc.CallOption) (*aimbot.GetDimensionsResponse, error)
	SetBandingEnabled(ctx context.Context, in *SetBandingEnabledRequest, opts ...grpc.CallOption) (*SetBandingEnabledResponse, error)
	IsBandingEnabled(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*IsBandingEnabledResponse, error)
	SetDynamicBandingEnabled(ctx context.Context, in *SetBandingEnabledRequest, opts ...grpc.CallOption) (*SetBandingEnabledResponse, error)
	IsDynamicBandingEnabled(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*IsBandingEnabledResponse, error)
	GetVisualizationMetadata(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetVisualizationMetadataResponse, error)
	GetNextBandingState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextBandingStateResponse, error)
}

type bandingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBandingServiceClient(cc grpc.ClientConnInterface) BandingServiceClient {
	return &bandingServiceClient{cc}
}

func (c *bandingServiceClient) LoadBandingDefs(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LoadBandingDefsResponse, error) {
	out := new(LoadBandingDefsResponse)
	err := c.cc.Invoke(ctx, BandingService_LoadBandingDefs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) SaveBandingDef(ctx context.Context, in *SaveBandingDefRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, BandingService_SaveBandingDef_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) DeleteBandingDef(ctx context.Context, in *DeleteBandingDefRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, BandingService_DeleteBandingDef_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) SetActiveBandingDef(ctx context.Context, in *SetActiveBandingDefRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, BandingService_SetActiveBandingDef_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetActiveBandingDef(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetActiveBandingDefResponse, error) {
	out := new(GetActiveBandingDefResponse)
	err := c.cc.Invoke(ctx, BandingService_GetActiveBandingDef_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetNextVisualizationData(ctx context.Context, in *GetNextVisualizationDataRequest, opts ...grpc.CallOption) (*GetNextVisualizationDataResponse, error) {
	out := new(GetNextVisualizationDataResponse)
	err := c.cc.Invoke(ctx, BandingService_GetNextVisualizationData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetNextVisualizationData2(ctx context.Context, in *GetNextVisualizationDataRequest, opts ...grpc.CallOption) (*GetNextVisualizationData2Response, error) {
	out := new(GetNextVisualizationData2Response)
	err := c.cc.Invoke(ctx, BandingService_GetNextVisualizationData2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetNextVisualizationDataForAllRows(ctx context.Context, in *GetNextVisualizationDataForAllRowsRequest, opts ...grpc.CallOption) (*GetNextVisualizationDataForAllRowsResponse, error) {
	out := new(GetNextVisualizationDataForAllRowsResponse)
	err := c.cc.Invoke(ctx, BandingService_GetNextVisualizationDataForAllRows_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetDimensions(ctx context.Context, in *GetDimensionsRequest, opts ...grpc.CallOption) (*aimbot.GetDimensionsResponse, error) {
	out := new(aimbot.GetDimensionsResponse)
	err := c.cc.Invoke(ctx, BandingService_GetDimensions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) SetBandingEnabled(ctx context.Context, in *SetBandingEnabledRequest, opts ...grpc.CallOption) (*SetBandingEnabledResponse, error) {
	out := new(SetBandingEnabledResponse)
	err := c.cc.Invoke(ctx, BandingService_SetBandingEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) IsBandingEnabled(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*IsBandingEnabledResponse, error) {
	out := new(IsBandingEnabledResponse)
	err := c.cc.Invoke(ctx, BandingService_IsBandingEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) SetDynamicBandingEnabled(ctx context.Context, in *SetBandingEnabledRequest, opts ...grpc.CallOption) (*SetBandingEnabledResponse, error) {
	out := new(SetBandingEnabledResponse)
	err := c.cc.Invoke(ctx, BandingService_SetDynamicBandingEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) IsDynamicBandingEnabled(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*IsBandingEnabledResponse, error) {
	out := new(IsBandingEnabledResponse)
	err := c.cc.Invoke(ctx, BandingService_IsDynamicBandingEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetVisualizationMetadata(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetVisualizationMetadataResponse, error) {
	out := new(GetVisualizationMetadataResponse)
	err := c.cc.Invoke(ctx, BandingService_GetVisualizationMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bandingServiceClient) GetNextBandingState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextBandingStateResponse, error) {
	out := new(GetNextBandingStateResponse)
	err := c.cc.Invoke(ctx, BandingService_GetNextBandingState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BandingServiceServer is the server API for BandingService service.
// All implementations must embed UnimplementedBandingServiceServer
// for forward compatibility
type BandingServiceServer interface {
	LoadBandingDefs(context.Context, *Empty) (*LoadBandingDefsResponse, error)
	SaveBandingDef(context.Context, *SaveBandingDefRequest) (*Empty, error)
	DeleteBandingDef(context.Context, *DeleteBandingDefRequest) (*Empty, error)
	SetActiveBandingDef(context.Context, *SetActiveBandingDefRequest) (*Empty, error)
	GetActiveBandingDef(context.Context, *Empty) (*GetActiveBandingDefResponse, error)
	GetNextVisualizationData(context.Context, *GetNextVisualizationDataRequest) (*GetNextVisualizationDataResponse, error)
	GetNextVisualizationData2(context.Context, *GetNextVisualizationDataRequest) (*GetNextVisualizationData2Response, error)
	GetNextVisualizationDataForAllRows(context.Context, *GetNextVisualizationDataForAllRowsRequest) (*GetNextVisualizationDataForAllRowsResponse, error)
	GetDimensions(context.Context, *GetDimensionsRequest) (*aimbot.GetDimensionsResponse, error)
	SetBandingEnabled(context.Context, *SetBandingEnabledRequest) (*SetBandingEnabledResponse, error)
	IsBandingEnabled(context.Context, *Empty) (*IsBandingEnabledResponse, error)
	SetDynamicBandingEnabled(context.Context, *SetBandingEnabledRequest) (*SetBandingEnabledResponse, error)
	IsDynamicBandingEnabled(context.Context, *Empty) (*IsBandingEnabledResponse, error)
	GetVisualizationMetadata(context.Context, *Empty) (*GetVisualizationMetadataResponse, error)
	GetNextBandingState(context.Context, *Timestamp) (*GetNextBandingStateResponse, error)
	mustEmbedUnimplementedBandingServiceServer()
}

// UnimplementedBandingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBandingServiceServer struct {
}

func (UnimplementedBandingServiceServer) LoadBandingDefs(context.Context, *Empty) (*LoadBandingDefsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadBandingDefs not implemented")
}
func (UnimplementedBandingServiceServer) SaveBandingDef(context.Context, *SaveBandingDefRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveBandingDef not implemented")
}
func (UnimplementedBandingServiceServer) DeleteBandingDef(context.Context, *DeleteBandingDefRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBandingDef not implemented")
}
func (UnimplementedBandingServiceServer) SetActiveBandingDef(context.Context, *SetActiveBandingDefRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetActiveBandingDef not implemented")
}
func (UnimplementedBandingServiceServer) GetActiveBandingDef(context.Context, *Empty) (*GetActiveBandingDefResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveBandingDef not implemented")
}
func (UnimplementedBandingServiceServer) GetNextVisualizationData(context.Context, *GetNextVisualizationDataRequest) (*GetNextVisualizationDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextVisualizationData not implemented")
}
func (UnimplementedBandingServiceServer) GetNextVisualizationData2(context.Context, *GetNextVisualizationDataRequest) (*GetNextVisualizationData2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextVisualizationData2 not implemented")
}
func (UnimplementedBandingServiceServer) GetNextVisualizationDataForAllRows(context.Context, *GetNextVisualizationDataForAllRowsRequest) (*GetNextVisualizationDataForAllRowsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextVisualizationDataForAllRows not implemented")
}
func (UnimplementedBandingServiceServer) GetDimensions(context.Context, *GetDimensionsRequest) (*aimbot.GetDimensionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDimensions not implemented")
}
func (UnimplementedBandingServiceServer) SetBandingEnabled(context.Context, *SetBandingEnabledRequest) (*SetBandingEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetBandingEnabled not implemented")
}
func (UnimplementedBandingServiceServer) IsBandingEnabled(context.Context, *Empty) (*IsBandingEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsBandingEnabled not implemented")
}
func (UnimplementedBandingServiceServer) SetDynamicBandingEnabled(context.Context, *SetBandingEnabledRequest) (*SetBandingEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDynamicBandingEnabled not implemented")
}
func (UnimplementedBandingServiceServer) IsDynamicBandingEnabled(context.Context, *Empty) (*IsBandingEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsDynamicBandingEnabled not implemented")
}
func (UnimplementedBandingServiceServer) GetVisualizationMetadata(context.Context, *Empty) (*GetVisualizationMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVisualizationMetadata not implemented")
}
func (UnimplementedBandingServiceServer) GetNextBandingState(context.Context, *Timestamp) (*GetNextBandingStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextBandingState not implemented")
}
func (UnimplementedBandingServiceServer) mustEmbedUnimplementedBandingServiceServer() {}

// UnsafeBandingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BandingServiceServer will
// result in compilation errors.
type UnsafeBandingServiceServer interface {
	mustEmbedUnimplementedBandingServiceServer()
}

func RegisterBandingServiceServer(s grpc.ServiceRegistrar, srv BandingServiceServer) {
	s.RegisterService(&BandingService_ServiceDesc, srv)
}

func _BandingService_LoadBandingDefs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).LoadBandingDefs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_LoadBandingDefs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).LoadBandingDefs(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_SaveBandingDef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveBandingDefRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).SaveBandingDef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_SaveBandingDef_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).SaveBandingDef(ctx, req.(*SaveBandingDefRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_DeleteBandingDef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBandingDefRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).DeleteBandingDef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_DeleteBandingDef_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).DeleteBandingDef(ctx, req.(*DeleteBandingDefRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_SetActiveBandingDef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActiveBandingDefRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).SetActiveBandingDef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_SetActiveBandingDef_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).SetActiveBandingDef(ctx, req.(*SetActiveBandingDefRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetActiveBandingDef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetActiveBandingDef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetActiveBandingDef_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetActiveBandingDef(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetNextVisualizationData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextVisualizationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetNextVisualizationData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetNextVisualizationData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetNextVisualizationData(ctx, req.(*GetNextVisualizationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetNextVisualizationData2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextVisualizationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetNextVisualizationData2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetNextVisualizationData2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetNextVisualizationData2(ctx, req.(*GetNextVisualizationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetNextVisualizationDataForAllRows_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextVisualizationDataForAllRowsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetNextVisualizationDataForAllRows(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetNextVisualizationDataForAllRows_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetNextVisualizationDataForAllRows(ctx, req.(*GetNextVisualizationDataForAllRowsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetDimensions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDimensionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetDimensions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetDimensions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetDimensions(ctx, req.(*GetDimensionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_SetBandingEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBandingEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).SetBandingEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_SetBandingEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).SetBandingEnabled(ctx, req.(*SetBandingEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_IsBandingEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).IsBandingEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_IsBandingEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).IsBandingEnabled(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_SetDynamicBandingEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBandingEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).SetDynamicBandingEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_SetDynamicBandingEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).SetDynamicBandingEnabled(ctx, req.(*SetBandingEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_IsDynamicBandingEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).IsDynamicBandingEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_IsDynamicBandingEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).IsDynamicBandingEnabled(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetVisualizationMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetVisualizationMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetVisualizationMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetVisualizationMetadata(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BandingService_GetNextBandingState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BandingServiceServer).GetNextBandingState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BandingService_GetNextBandingState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BandingServiceServer).GetNextBandingState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

// BandingService_ServiceDesc is the grpc.ServiceDesc for BandingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BandingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.banding.BandingService",
	HandlerType: (*BandingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LoadBandingDefs",
			Handler:    _BandingService_LoadBandingDefs_Handler,
		},
		{
			MethodName: "SaveBandingDef",
			Handler:    _BandingService_SaveBandingDef_Handler,
		},
		{
			MethodName: "DeleteBandingDef",
			Handler:    _BandingService_DeleteBandingDef_Handler,
		},
		{
			MethodName: "SetActiveBandingDef",
			Handler:    _BandingService_SetActiveBandingDef_Handler,
		},
		{
			MethodName: "GetActiveBandingDef",
			Handler:    _BandingService_GetActiveBandingDef_Handler,
		},
		{
			MethodName: "GetNextVisualizationData",
			Handler:    _BandingService_GetNextVisualizationData_Handler,
		},
		{
			MethodName: "GetNextVisualizationData2",
			Handler:    _BandingService_GetNextVisualizationData2_Handler,
		},
		{
			MethodName: "GetNextVisualizationDataForAllRows",
			Handler:    _BandingService_GetNextVisualizationDataForAllRows_Handler,
		},
		{
			MethodName: "GetDimensions",
			Handler:    _BandingService_GetDimensions_Handler,
		},
		{
			MethodName: "SetBandingEnabled",
			Handler:    _BandingService_SetBandingEnabled_Handler,
		},
		{
			MethodName: "IsBandingEnabled",
			Handler:    _BandingService_IsBandingEnabled_Handler,
		},
		{
			MethodName: "SetDynamicBandingEnabled",
			Handler:    _BandingService_SetDynamicBandingEnabled_Handler,
		},
		{
			MethodName: "IsDynamicBandingEnabled",
			Handler:    _BandingService_IsDynamicBandingEnabled_Handler,
		},
		{
			MethodName: "GetVisualizationMetadata",
			Handler:    _BandingService_GetVisualizationMetadata_Handler,
		},
		{
			MethodName: "GetNextBandingState",
			Handler:    _BandingService_GetNextBandingState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/banding.proto",
}

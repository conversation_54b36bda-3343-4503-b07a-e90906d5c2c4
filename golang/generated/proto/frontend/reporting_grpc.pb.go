// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/reporting.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReportingService_GetNextLocationHistory_FullMethodName = "/carbon.frontend.features.ReportingService/GetNextLocationHistory"
)

// ReportingServiceClient is the client API for ReportingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportingServiceClient interface {
	GetNextLocationHistory(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*LocationHistory, error)
}

type reportingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReportingServiceClient(cc grpc.ClientConnInterface) ReportingServiceClient {
	return &reportingServiceClient{cc}
}

func (c *reportingServiceClient) GetNextLocationHistory(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*LocationHistory, error) {
	out := new(LocationHistory)
	err := c.cc.Invoke(ctx, ReportingService_GetNextLocationHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportingServiceServer is the server API for ReportingService service.
// All implementations must embed UnimplementedReportingServiceServer
// for forward compatibility
type ReportingServiceServer interface {
	GetNextLocationHistory(context.Context, *Timestamp) (*LocationHistory, error)
	mustEmbedUnimplementedReportingServiceServer()
}

// UnimplementedReportingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedReportingServiceServer struct {
}

func (UnimplementedReportingServiceServer) GetNextLocationHistory(context.Context, *Timestamp) (*LocationHistory, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextLocationHistory not implemented")
}
func (UnimplementedReportingServiceServer) mustEmbedUnimplementedReportingServiceServer() {}

// UnsafeReportingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportingServiceServer will
// result in compilation errors.
type UnsafeReportingServiceServer interface {
	mustEmbedUnimplementedReportingServiceServer()
}

func RegisterReportingServiceServer(s grpc.ServiceRegistrar, srv ReportingServiceServer) {
	s.RegisterService(&ReportingService_ServiceDesc, srv)
}

func _ReportingService_GetNextLocationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportingServiceServer).GetNextLocationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportingService_GetNextLocationHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportingServiceServer).GetNextLocationHistory(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportingService_ServiceDesc is the grpc.ServiceDesc for ReportingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.features.ReportingService",
	HandlerType: (*ReportingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextLocationHistory",
			Handler:    _ReportingService_GetNextLocationHistory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/reporting.proto",
}

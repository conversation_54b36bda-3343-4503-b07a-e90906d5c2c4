// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/software.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SoftwareService_GetNextSoftwareVersionState_FullMethodName = "/carbon.frontend.software.SoftwareService/GetNextSoftwareVersionState"
	SoftwareService_UpdateHost_FullMethodName                  = "/carbon.frontend.software.SoftwareService/UpdateHost"
	SoftwareService_Update_FullMethodName                      = "/carbon.frontend.software.SoftwareService/Update"
	SoftwareService_Revert_FullMethodName                      = "/carbon.frontend.software.SoftwareService/Revert"
	SoftwareService_FixVersionMismatch_FullMethodName          = "/carbon.frontend.software.SoftwareService/FixVersionMismatch"
)

// SoftwareServiceClient is the client API for SoftwareService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SoftwareServiceClient interface {
	GetNextSoftwareVersionState(ctx context.Context, in *SoftwareVersionStateRequest, opts ...grpc.CallOption) (*SoftwareVersionState, error)
	UpdateHost(ctx context.Context, in *UpdateHostRequest, opts ...grpc.CallOption) (*Empty, error)
	Update(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	Revert(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	FixVersionMismatch(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type softwareServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSoftwareServiceClient(cc grpc.ClientConnInterface) SoftwareServiceClient {
	return &softwareServiceClient{cc}
}

func (c *softwareServiceClient) GetNextSoftwareVersionState(ctx context.Context, in *SoftwareVersionStateRequest, opts ...grpc.CallOption) (*SoftwareVersionState, error) {
	out := new(SoftwareVersionState)
	err := c.cc.Invoke(ctx, SoftwareService_GetNextSoftwareVersionState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareServiceClient) UpdateHost(ctx context.Context, in *UpdateHostRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareService_UpdateHost_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareServiceClient) Update(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareService_Update_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareServiceClient) Revert(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareService_Revert_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareServiceClient) FixVersionMismatch(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareService_FixVersionMismatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SoftwareServiceServer is the server API for SoftwareService service.
// All implementations must embed UnimplementedSoftwareServiceServer
// for forward compatibility
type SoftwareServiceServer interface {
	GetNextSoftwareVersionState(context.Context, *SoftwareVersionStateRequest) (*SoftwareVersionState, error)
	UpdateHost(context.Context, *UpdateHostRequest) (*Empty, error)
	Update(context.Context, *Empty) (*Empty, error)
	Revert(context.Context, *Empty) (*Empty, error)
	FixVersionMismatch(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedSoftwareServiceServer()
}

// UnimplementedSoftwareServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSoftwareServiceServer struct {
}

func (UnimplementedSoftwareServiceServer) GetNextSoftwareVersionState(context.Context, *SoftwareVersionStateRequest) (*SoftwareVersionState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextSoftwareVersionState not implemented")
}
func (UnimplementedSoftwareServiceServer) UpdateHost(context.Context, *UpdateHostRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHost not implemented")
}
func (UnimplementedSoftwareServiceServer) Update(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedSoftwareServiceServer) Revert(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Revert not implemented")
}
func (UnimplementedSoftwareServiceServer) FixVersionMismatch(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FixVersionMismatch not implemented")
}
func (UnimplementedSoftwareServiceServer) mustEmbedUnimplementedSoftwareServiceServer() {}

// UnsafeSoftwareServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SoftwareServiceServer will
// result in compilation errors.
type UnsafeSoftwareServiceServer interface {
	mustEmbedUnimplementedSoftwareServiceServer()
}

func RegisterSoftwareServiceServer(s grpc.ServiceRegistrar, srv SoftwareServiceServer) {
	s.RegisterService(&SoftwareService_ServiceDesc, srv)
}

func _SoftwareService_GetNextSoftwareVersionState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SoftwareVersionStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareServiceServer).GetNextSoftwareVersionState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareService_GetNextSoftwareVersionState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareServiceServer).GetNextSoftwareVersionState(ctx, req.(*SoftwareVersionStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareService_UpdateHost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareServiceServer).UpdateHost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareService_UpdateHost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareServiceServer).UpdateHost(ctx, req.(*UpdateHostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareServiceServer).Update(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareService_Revert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareServiceServer).Revert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareService_Revert_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareServiceServer).Revert(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareService_FixVersionMismatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareServiceServer).FixVersionMismatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareService_FixVersionMismatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareServiceServer).FixVersionMismatch(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// SoftwareService_ServiceDesc is the grpc.ServiceDesc for SoftwareService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SoftwareService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.software.SoftwareService",
	HandlerType: (*SoftwareServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextSoftwareVersionState",
			Handler:    _SoftwareService_GetNextSoftwareVersionState_Handler,
		},
		{
			MethodName: "UpdateHost",
			Handler:    _SoftwareService_UpdateHost_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _SoftwareService_Update_Handler,
		},
		{
			MethodName: "Revert",
			Handler:    _SoftwareService_Revert_Handler,
		},
		{
			MethodName: "FixVersionMismatch",
			Handler:    _SoftwareService_FixVersionMismatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/software.proto",
}

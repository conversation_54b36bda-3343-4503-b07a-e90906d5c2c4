// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/tractor.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TractorService_GetNextTractorIfState_FullMethodName     = "/carbon.frontend.tractor.TractorService/GetNextTractorIfState"
	TractorService_GetNextTractorSafetyState_FullMethodName = "/carbon.frontend.tractor.TractorService/GetNextTractorSafetyState"
	TractorService_SetEnforcementPolicy_FullMethodName      = "/carbon.frontend.tractor.TractorService/SetEnforcementPolicy"
)

// TractorServiceClient is the client API for TractorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TractorServiceClient interface {
	GetNextTractorIfState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextTractorIfStateResponse, error)
	GetNextTractorSafetyState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextTractorSafetyStateResponse, error)
	SetEnforcementPolicy(ctx context.Context, in *SetEnforcementPolicyRequest, opts ...grpc.CallOption) (*SetEnforcementPolicyResponse, error)
}

type tractorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTractorServiceClient(cc grpc.ClientConnInterface) TractorServiceClient {
	return &tractorServiceClient{cc}
}

func (c *tractorServiceClient) GetNextTractorIfState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextTractorIfStateResponse, error) {
	out := new(GetNextTractorIfStateResponse)
	err := c.cc.Invoke(ctx, TractorService_GetNextTractorIfState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tractorServiceClient) GetNextTractorSafetyState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextTractorSafetyStateResponse, error) {
	out := new(GetNextTractorSafetyStateResponse)
	err := c.cc.Invoke(ctx, TractorService_GetNextTractorSafetyState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tractorServiceClient) SetEnforcementPolicy(ctx context.Context, in *SetEnforcementPolicyRequest, opts ...grpc.CallOption) (*SetEnforcementPolicyResponse, error) {
	out := new(SetEnforcementPolicyResponse)
	err := c.cc.Invoke(ctx, TractorService_SetEnforcementPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TractorServiceServer is the server API for TractorService service.
// All implementations must embed UnimplementedTractorServiceServer
// for forward compatibility
type TractorServiceServer interface {
	GetNextTractorIfState(context.Context, *Timestamp) (*GetNextTractorIfStateResponse, error)
	GetNextTractorSafetyState(context.Context, *Timestamp) (*GetNextTractorSafetyStateResponse, error)
	SetEnforcementPolicy(context.Context, *SetEnforcementPolicyRequest) (*SetEnforcementPolicyResponse, error)
	mustEmbedUnimplementedTractorServiceServer()
}

// UnimplementedTractorServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTractorServiceServer struct {
}

func (UnimplementedTractorServiceServer) GetNextTractorIfState(context.Context, *Timestamp) (*GetNextTractorIfStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextTractorIfState not implemented")
}
func (UnimplementedTractorServiceServer) GetNextTractorSafetyState(context.Context, *Timestamp) (*GetNextTractorSafetyStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextTractorSafetyState not implemented")
}
func (UnimplementedTractorServiceServer) SetEnforcementPolicy(context.Context, *SetEnforcementPolicyRequest) (*SetEnforcementPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetEnforcementPolicy not implemented")
}
func (UnimplementedTractorServiceServer) mustEmbedUnimplementedTractorServiceServer() {}

// UnsafeTractorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TractorServiceServer will
// result in compilation errors.
type UnsafeTractorServiceServer interface {
	mustEmbedUnimplementedTractorServiceServer()
}

func RegisterTractorServiceServer(s grpc.ServiceRegistrar, srv TractorServiceServer) {
	s.RegisterService(&TractorService_ServiceDesc, srv)
}

func _TractorService_GetNextTractorIfState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorServiceServer).GetNextTractorIfState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorService_GetNextTractorIfState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorServiceServer).GetNextTractorIfState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _TractorService_GetNextTractorSafetyState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorServiceServer).GetNextTractorSafetyState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorService_GetNextTractorSafetyState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorServiceServer).GetNextTractorSafetyState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _TractorService_SetEnforcementPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetEnforcementPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorServiceServer).SetEnforcementPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorService_SetEnforcementPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorServiceServer).SetEnforcementPolicy(ctx, req.(*SetEnforcementPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TractorService_ServiceDesc is the grpc.ServiceDesc for TractorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TractorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.tractor.TractorService",
	HandlerType: (*TractorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextTractorIfState",
			Handler:    _TractorService_GetNextTractorIfState_Handler,
		},
		{
			MethodName: "GetNextTractorSafetyState",
			Handler:    _TractorService_GetNextTractorSafetyState_Handler,
		},
		{
			MethodName: "SetEnforcementPolicy",
			Handler:    _TractorService_SetEnforcementPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/tractor.proto",
}

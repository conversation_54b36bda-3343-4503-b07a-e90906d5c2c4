// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/profile_sync.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProfileType int32

const (
	ProfileType_ALMANAC                   ProfileType = 0
	ProfileType_DISCRIMINATOR             ProfileType = 1
	ProfileType_MODELINATOR               ProfileType = 3
	ProfileType_BANDING                   ProfileType = 4
	ProfileType_THINNING                  ProfileType = 5
	ProfileType_TARGET_VELOCITY_ESTIMATOR ProfileType = 6
	ProfileType_CATEGORY_COLLECTION       ProfileType = 7
	ProfileType_CATEGORY                  ProfileType = 8
)

// Enum value maps for ProfileType.
var (
	ProfileType_name = map[int32]string{
		0: "ALMANAC",
		1: "DISCRIMINATOR",
		3: "MODELINATOR",
		4: "BANDING",
		5: "THINNING",
		6: "TARGET_VELOCITY_ESTIMATOR",
		7: "CATEGORY_COLLECTION",
		8: "CATEGORY",
	}
	ProfileType_value = map[string]int32{
		"ALMANAC":                   0,
		"DISCRIMINATOR":             1,
		"MODELINATOR":               3,
		"BANDING":                   4,
		"THINNING":                  5,
		"TARGET_VELOCITY_ESTIMATOR": 6,
		"CATEGORY_COLLECTION":       7,
		"CATEGORY":                  8,
	}
)

func (x ProfileType) Enum() *ProfileType {
	p := new(ProfileType)
	*p = x
	return p
}

func (x ProfileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProfileType) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_profile_sync_proto_enumTypes[0].Descriptor()
}

func (ProfileType) Type() protoreflect.EnumType {
	return &file_frontend_proto_profile_sync_proto_enumTypes[0]
}

func (x ProfileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProfileType.Descriptor instead.
func (ProfileType) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_profile_sync_proto_rawDescGZIP(), []int{0}
}

type ProfileSyncData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProfileType     ProfileType `protobuf:"varint,1,opt,name=profile_type,json=profileType,proto3,enum=carbon.frontend.profile_sync.ProfileType" json:"profile_type,omitempty"`
	LastUpdatedTsMs int64       `protobuf:"varint,2,opt,name=last_updated_ts_ms,json=lastUpdatedTsMs,proto3" json:"last_updated_ts_ms,omitempty"`
	Deleted         bool        `protobuf:"varint,3,opt,name=deleted,proto3" json:"deleted,omitempty"`
	Protected       bool        `protobuf:"varint,4,opt,name=protected,proto3" json:"protected,omitempty"`
}

func (x *ProfileSyncData) Reset() {
	*x = ProfileSyncData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_profile_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileSyncData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileSyncData) ProtoMessage() {}

func (x *ProfileSyncData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_profile_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileSyncData.ProtoReflect.Descriptor instead.
func (*ProfileSyncData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_profile_sync_proto_rawDescGZIP(), []int{0}
}

func (x *ProfileSyncData) GetProfileType() ProfileType {
	if x != nil {
		return x.ProfileType
	}
	return ProfileType_ALMANAC
}

func (x *ProfileSyncData) GetLastUpdatedTsMs() int64 {
	if x != nil {
		return x.LastUpdatedTsMs
	}
	return 0
}

func (x *ProfileSyncData) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *ProfileSyncData) GetProtected() bool {
	if x != nil {
		return x.Protected
	}
	return false
}

var File_frontend_proto_profile_sync_proto protoreflect.FileDescriptor

var file_frontend_proto_profile_sync_proto_rawDesc = []byte{
	0x0a, 0x21, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e,
	0x63, 0x22, 0xc4, 0x01, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4c, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x73, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x73, 0x4d, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x2a, 0x9f, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4c, 0x4d, 0x41,
	0x4e, 0x41, 0x43, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x49, 0x53, 0x43, 0x52, 0x49, 0x4d,
	0x49, 0x4e, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x49, 0x4e, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x41, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49,
	0x4e, 0x47, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x56,
	0x45, 0x4c, 0x4f, 0x43, 0x49, 0x54, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x4f,
	0x52, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x08, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_profile_sync_proto_rawDescOnce sync.Once
	file_frontend_proto_profile_sync_proto_rawDescData = file_frontend_proto_profile_sync_proto_rawDesc
)

func file_frontend_proto_profile_sync_proto_rawDescGZIP() []byte {
	file_frontend_proto_profile_sync_proto_rawDescOnce.Do(func() {
		file_frontend_proto_profile_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_profile_sync_proto_rawDescData)
	})
	return file_frontend_proto_profile_sync_proto_rawDescData
}

var file_frontend_proto_profile_sync_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_frontend_proto_profile_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_frontend_proto_profile_sync_proto_goTypes = []interface{}{
	(ProfileType)(0),        // 0: carbon.frontend.profile_sync.ProfileType
	(*ProfileSyncData)(nil), // 1: carbon.frontend.profile_sync.ProfileSyncData
}
var file_frontend_proto_profile_sync_proto_depIdxs = []int32{
	0, // 0: carbon.frontend.profile_sync.ProfileSyncData.profile_type:type_name -> carbon.frontend.profile_sync.ProfileType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_frontend_proto_profile_sync_proto_init() }
func file_frontend_proto_profile_sync_proto_init() {
	if File_frontend_proto_profile_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_profile_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileSyncData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_profile_sync_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_frontend_proto_profile_sync_proto_goTypes,
		DependencyIndexes: file_frontend_proto_profile_sync_proto_depIdxs,
		EnumInfos:         file_frontend_proto_profile_sync_proto_enumTypes,
		MessageInfos:      file_frontend_proto_profile_sync_proto_msgTypes,
	}.Build()
	File_frontend_proto_profile_sync_proto = out.File
	file_frontend_proto_profile_sync_proto_rawDesc = nil
	file_frontend_proto_profile_sync_proto_goTypes = nil
	file_frontend_proto_profile_sync_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/target_velocity_estimator.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TargetVelocityEstimatorService_GetNextAvailableProfiles_FullMethodName = "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextAvailableProfiles"
	TargetVelocityEstimatorService_GetNextActiveProfile_FullMethodName     = "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextActiveProfile"
	TargetVelocityEstimatorService_LoadProfile_FullMethodName              = "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/LoadProfile"
	TargetVelocityEstimatorService_SaveProfile_FullMethodName              = "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SaveProfile"
	TargetVelocityEstimatorService_SetActive_FullMethodName                = "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SetActive"
	TargetVelocityEstimatorService_DeleteProfile_FullMethodName            = "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/DeleteProfile"
)

// TargetVelocityEstimatorServiceClient is the client API for TargetVelocityEstimatorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TargetVelocityEstimatorServiceClient interface {
	GetNextAvailableProfiles(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextAvailableTVEProfilesResponse, error)
	GetNextActiveProfile(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextActiveTVEProfileResponse, error)
	LoadProfile(ctx context.Context, in *LoadTVEProfileRequest, opts ...grpc.CallOption) (*LoadTVEProfileResponse, error)
	SaveProfile(ctx context.Context, in *SaveTVEProfileRequest, opts ...grpc.CallOption) (*SaveTVEProfileResponse, error)
	SetActive(ctx context.Context, in *SetActiveTVEProfileRequest, opts ...grpc.CallOption) (*SetActiveTVEProfileResponse, error)
	DeleteProfile(ctx context.Context, in *DeleteTVEProfileRequest, opts ...grpc.CallOption) (*DeleteTVEProfileResponse, error)
}

type targetVelocityEstimatorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTargetVelocityEstimatorServiceClient(cc grpc.ClientConnInterface) TargetVelocityEstimatorServiceClient {
	return &targetVelocityEstimatorServiceClient{cc}
}

func (c *targetVelocityEstimatorServiceClient) GetNextAvailableProfiles(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextAvailableTVEProfilesResponse, error) {
	out := new(GetNextAvailableTVEProfilesResponse)
	err := c.cc.Invoke(ctx, TargetVelocityEstimatorService_GetNextAvailableProfiles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *targetVelocityEstimatorServiceClient) GetNextActiveProfile(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextActiveTVEProfileResponse, error) {
	out := new(GetNextActiveTVEProfileResponse)
	err := c.cc.Invoke(ctx, TargetVelocityEstimatorService_GetNextActiveProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *targetVelocityEstimatorServiceClient) LoadProfile(ctx context.Context, in *LoadTVEProfileRequest, opts ...grpc.CallOption) (*LoadTVEProfileResponse, error) {
	out := new(LoadTVEProfileResponse)
	err := c.cc.Invoke(ctx, TargetVelocityEstimatorService_LoadProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *targetVelocityEstimatorServiceClient) SaveProfile(ctx context.Context, in *SaveTVEProfileRequest, opts ...grpc.CallOption) (*SaveTVEProfileResponse, error) {
	out := new(SaveTVEProfileResponse)
	err := c.cc.Invoke(ctx, TargetVelocityEstimatorService_SaveProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *targetVelocityEstimatorServiceClient) SetActive(ctx context.Context, in *SetActiveTVEProfileRequest, opts ...grpc.CallOption) (*SetActiveTVEProfileResponse, error) {
	out := new(SetActiveTVEProfileResponse)
	err := c.cc.Invoke(ctx, TargetVelocityEstimatorService_SetActive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *targetVelocityEstimatorServiceClient) DeleteProfile(ctx context.Context, in *DeleteTVEProfileRequest, opts ...grpc.CallOption) (*DeleteTVEProfileResponse, error) {
	out := new(DeleteTVEProfileResponse)
	err := c.cc.Invoke(ctx, TargetVelocityEstimatorService_DeleteProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TargetVelocityEstimatorServiceServer is the server API for TargetVelocityEstimatorService service.
// All implementations must embed UnimplementedTargetVelocityEstimatorServiceServer
// for forward compatibility
type TargetVelocityEstimatorServiceServer interface {
	GetNextAvailableProfiles(context.Context, *Timestamp) (*GetNextAvailableTVEProfilesResponse, error)
	GetNextActiveProfile(context.Context, *Timestamp) (*GetNextActiveTVEProfileResponse, error)
	LoadProfile(context.Context, *LoadTVEProfileRequest) (*LoadTVEProfileResponse, error)
	SaveProfile(context.Context, *SaveTVEProfileRequest) (*SaveTVEProfileResponse, error)
	SetActive(context.Context, *SetActiveTVEProfileRequest) (*SetActiveTVEProfileResponse, error)
	DeleteProfile(context.Context, *DeleteTVEProfileRequest) (*DeleteTVEProfileResponse, error)
	mustEmbedUnimplementedTargetVelocityEstimatorServiceServer()
}

// UnimplementedTargetVelocityEstimatorServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTargetVelocityEstimatorServiceServer struct {
}

func (UnimplementedTargetVelocityEstimatorServiceServer) GetNextAvailableProfiles(context.Context, *Timestamp) (*GetNextAvailableTVEProfilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAvailableProfiles not implemented")
}
func (UnimplementedTargetVelocityEstimatorServiceServer) GetNextActiveProfile(context.Context, *Timestamp) (*GetNextActiveTVEProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActiveProfile not implemented")
}
func (UnimplementedTargetVelocityEstimatorServiceServer) LoadProfile(context.Context, *LoadTVEProfileRequest) (*LoadTVEProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadProfile not implemented")
}
func (UnimplementedTargetVelocityEstimatorServiceServer) SaveProfile(context.Context, *SaveTVEProfileRequest) (*SaveTVEProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveProfile not implemented")
}
func (UnimplementedTargetVelocityEstimatorServiceServer) SetActive(context.Context, *SetActiveTVEProfileRequest) (*SetActiveTVEProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetActive not implemented")
}
func (UnimplementedTargetVelocityEstimatorServiceServer) DeleteProfile(context.Context, *DeleteTVEProfileRequest) (*DeleteTVEProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProfile not implemented")
}
func (UnimplementedTargetVelocityEstimatorServiceServer) mustEmbedUnimplementedTargetVelocityEstimatorServiceServer() {
}

// UnsafeTargetVelocityEstimatorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TargetVelocityEstimatorServiceServer will
// result in compilation errors.
type UnsafeTargetVelocityEstimatorServiceServer interface {
	mustEmbedUnimplementedTargetVelocityEstimatorServiceServer()
}

func RegisterTargetVelocityEstimatorServiceServer(s grpc.ServiceRegistrar, srv TargetVelocityEstimatorServiceServer) {
	s.RegisterService(&TargetVelocityEstimatorService_ServiceDesc, srv)
}

func _TargetVelocityEstimatorService_GetNextAvailableProfiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetVelocityEstimatorServiceServer).GetNextAvailableProfiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TargetVelocityEstimatorService_GetNextAvailableProfiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetVelocityEstimatorServiceServer).GetNextAvailableProfiles(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _TargetVelocityEstimatorService_GetNextActiveProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetVelocityEstimatorServiceServer).GetNextActiveProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TargetVelocityEstimatorService_GetNextActiveProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetVelocityEstimatorServiceServer).GetNextActiveProfile(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _TargetVelocityEstimatorService_LoadProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadTVEProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetVelocityEstimatorServiceServer).LoadProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TargetVelocityEstimatorService_LoadProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetVelocityEstimatorServiceServer).LoadProfile(ctx, req.(*LoadTVEProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TargetVelocityEstimatorService_SaveProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveTVEProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetVelocityEstimatorServiceServer).SaveProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TargetVelocityEstimatorService_SaveProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetVelocityEstimatorServiceServer).SaveProfile(ctx, req.(*SaveTVEProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TargetVelocityEstimatorService_SetActive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActiveTVEProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetVelocityEstimatorServiceServer).SetActive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TargetVelocityEstimatorService_SetActive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetVelocityEstimatorServiceServer).SetActive(ctx, req.(*SetActiveTVEProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TargetVelocityEstimatorService_DeleteProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTVEProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetVelocityEstimatorServiceServer).DeleteProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TargetVelocityEstimatorService_DeleteProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetVelocityEstimatorServiceServer).DeleteProfile(ctx, req.(*DeleteTVEProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TargetVelocityEstimatorService_ServiceDesc is the grpc.ServiceDesc for TargetVelocityEstimatorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TargetVelocityEstimatorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService",
	HandlerType: (*TargetVelocityEstimatorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextAvailableProfiles",
			Handler:    _TargetVelocityEstimatorService_GetNextAvailableProfiles_Handler,
		},
		{
			MethodName: "GetNextActiveProfile",
			Handler:    _TargetVelocityEstimatorService_GetNextActiveProfile_Handler,
		},
		{
			MethodName: "LoadProfile",
			Handler:    _TargetVelocityEstimatorService_LoadProfile_Handler,
		},
		{
			MethodName: "SaveProfile",
			Handler:    _TargetVelocityEstimatorService_SaveProfile_Handler,
		},
		{
			MethodName: "SetActive",
			Handler:    _TargetVelocityEstimatorService_SetActive_Handler,
		},
		{
			MethodName: "DeleteProfile",
			Handler:    _TargetVelocityEstimatorService_DeleteProfile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/target_velocity_estimator.proto",
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/camera.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CameraService_GetCameraList_FullMethodName     = "/carbon.frontend.camera.CameraService/GetCameraList"
	CameraService_GetNextCameraList_FullMethodName = "/carbon.frontend.camera.CameraService/GetNextCameraList"
)

// CameraServiceClient is the client API for CameraService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CameraServiceClient interface {
	GetCameraList(ctx context.Context, in *CameraListRequest, opts ...grpc.CallOption) (*CameraList, error)
	GetNextCameraList(ctx context.Context, in *NextCameraListRequest, opts ...grpc.CallOption) (*CameraList, error)
}

type cameraServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCameraServiceClient(cc grpc.ClientConnInterface) CameraServiceClient {
	return &cameraServiceClient{cc}
}

func (c *cameraServiceClient) GetCameraList(ctx context.Context, in *CameraListRequest, opts ...grpc.CallOption) (*CameraList, error) {
	out := new(CameraList)
	err := c.cc.Invoke(ctx, CameraService_GetCameraList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cameraServiceClient) GetNextCameraList(ctx context.Context, in *NextCameraListRequest, opts ...grpc.CallOption) (*CameraList, error) {
	out := new(CameraList)
	err := c.cc.Invoke(ctx, CameraService_GetNextCameraList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CameraServiceServer is the server API for CameraService service.
// All implementations must embed UnimplementedCameraServiceServer
// for forward compatibility
type CameraServiceServer interface {
	GetCameraList(context.Context, *CameraListRequest) (*CameraList, error)
	GetNextCameraList(context.Context, *NextCameraListRequest) (*CameraList, error)
	mustEmbedUnimplementedCameraServiceServer()
}

// UnimplementedCameraServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCameraServiceServer struct {
}

func (UnimplementedCameraServiceServer) GetCameraList(context.Context, *CameraListRequest) (*CameraList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCameraList not implemented")
}
func (UnimplementedCameraServiceServer) GetNextCameraList(context.Context, *NextCameraListRequest) (*CameraList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextCameraList not implemented")
}
func (UnimplementedCameraServiceServer) mustEmbedUnimplementedCameraServiceServer() {}

// UnsafeCameraServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CameraServiceServer will
// result in compilation errors.
type UnsafeCameraServiceServer interface {
	mustEmbedUnimplementedCameraServiceServer()
}

func RegisterCameraServiceServer(s grpc.ServiceRegistrar, srv CameraServiceServer) {
	s.RegisterService(&CameraService_ServiceDesc, srv)
}

func _CameraService_GetCameraList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CameraListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CameraServiceServer).GetCameraList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CameraService_GetCameraList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CameraServiceServer).GetCameraList(ctx, req.(*CameraListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CameraService_GetNextCameraList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NextCameraListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CameraServiceServer).GetNextCameraList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CameraService_GetNextCameraList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CameraServiceServer).GetNextCameraList(ctx, req.(*NextCameraListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CameraService_ServiceDesc is the grpc.ServiceDesc for CameraService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CameraService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.camera.CameraService",
	HandlerType: (*CameraServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCameraList",
			Handler:    _CameraService_GetCameraList_Handler,
		},
		{
			MethodName: "GetNextCameraList",
			Handler:    _CameraService_GetNextCameraList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/camera.proto",
}

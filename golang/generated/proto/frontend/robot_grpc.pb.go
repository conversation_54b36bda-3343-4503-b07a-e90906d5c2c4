// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/robot.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RobotDiagnosticService_GetNextRobotState_FullMethodName = "/carbon.frontend.robot.RobotDiagnosticService/GetNextRobotState"
)

// RobotDiagnosticServiceClient is the client API for RobotDiagnosticService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RobotDiagnosticServiceClient interface {
	GetNextRobotState(ctx context.Context, in *RobotStateRequest, opts ...grpc.CallOption) (*TimestampedRobotState, error)
}

type robotDiagnosticServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRobotDiagnosticServiceClient(cc grpc.ClientConnInterface) RobotDiagnosticServiceClient {
	return &robotDiagnosticServiceClient{cc}
}

func (c *robotDiagnosticServiceClient) GetNextRobotState(ctx context.Context, in *RobotStateRequest, opts ...grpc.CallOption) (*TimestampedRobotState, error) {
	out := new(TimestampedRobotState)
	err := c.cc.Invoke(ctx, RobotDiagnosticService_GetNextRobotState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RobotDiagnosticServiceServer is the server API for RobotDiagnosticService service.
// All implementations must embed UnimplementedRobotDiagnosticServiceServer
// for forward compatibility
type RobotDiagnosticServiceServer interface {
	GetNextRobotState(context.Context, *RobotStateRequest) (*TimestampedRobotState, error)
	mustEmbedUnimplementedRobotDiagnosticServiceServer()
}

// UnimplementedRobotDiagnosticServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRobotDiagnosticServiceServer struct {
}

func (UnimplementedRobotDiagnosticServiceServer) GetNextRobotState(context.Context, *RobotStateRequest) (*TimestampedRobotState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextRobotState not implemented")
}
func (UnimplementedRobotDiagnosticServiceServer) mustEmbedUnimplementedRobotDiagnosticServiceServer() {
}

// UnsafeRobotDiagnosticServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RobotDiagnosticServiceServer will
// result in compilation errors.
type UnsafeRobotDiagnosticServiceServer interface {
	mustEmbedUnimplementedRobotDiagnosticServiceServer()
}

func RegisterRobotDiagnosticServiceServer(s grpc.ServiceRegistrar, srv RobotDiagnosticServiceServer) {
	s.RegisterService(&RobotDiagnosticService_ServiceDesc, srv)
}

func _RobotDiagnosticService_GetNextRobotState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RobotStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotDiagnosticServiceServer).GetNextRobotState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RobotDiagnosticService_GetNextRobotState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotDiagnosticServiceServer).GetNextRobotState(ctx, req.(*RobotStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RobotDiagnosticService_ServiceDesc is the grpc.ServiceDesc for RobotDiagnosticService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RobotDiagnosticService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.robot.RobotDiagnosticService",
	HandlerType: (*RobotDiagnosticServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextRobotState",
			Handler:    _RobotDiagnosticService_GetNextRobotState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/robot.proto",
}

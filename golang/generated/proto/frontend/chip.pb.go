// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/chip.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChipData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url           string     `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Geohash       string     `protobuf:"bytes,3,opt,name=geohash,proto3" json:"geohash,omitempty"`
	Checksum      string     `protobuf:"bytes,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
	ContentLength uint32     `protobuf:"varint,5,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
	DownloadedTs  *Timestamp `protobuf:"bytes,6,opt,name=downloaded_ts,json=downloadedTs,proto3" json:"downloaded_ts,omitempty"`
	LastUsedTs    *Timestamp `protobuf:"bytes,7,opt,name=last_used_ts,json=lastUsedTs,proto3" json:"last_used_ts,omitempty"`
}

func (x *ChipData) Reset() {
	*x = ChipData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_chip_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChipData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChipData) ProtoMessage() {}

func (x *ChipData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_chip_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChipData.ProtoReflect.Descriptor instead.
func (*ChipData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_chip_proto_rawDescGZIP(), []int{0}
}

func (x *ChipData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ChipData) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ChipData) GetGeohash() string {
	if x != nil {
		return x.Geohash
	}
	return ""
}

func (x *ChipData) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *ChipData) GetContentLength() uint32 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

func (x *ChipData) GetDownloadedTs() *Timestamp {
	if x != nil {
		return x.DownloadedTs
	}
	return nil
}

func (x *ChipData) GetLastUsedTs() *Timestamp {
	if x != nil {
		return x.LastUsedTs
	}
	return nil
}

type GetChipMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chips []*ChipData `protobuf:"bytes,1,rep,name=chips,proto3" json:"chips,omitempty"`
}

func (x *GetChipMetadataResponse) Reset() {
	*x = GetChipMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_chip_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChipMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChipMetadataResponse) ProtoMessage() {}

func (x *GetChipMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_chip_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChipMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetChipMetadataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_chip_proto_rawDescGZIP(), []int{1}
}

func (x *GetChipMetadataResponse) GetChips() []*ChipData {
	if x != nil {
		return x.Chips
	}
	return nil
}

type ChipIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChipIds []string `protobuf:"bytes,1,rep,name=chip_ids,json=chipIds,proto3" json:"chip_ids,omitempty"`
}

func (x *ChipIdsResponse) Reset() {
	*x = ChipIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_chip_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChipIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChipIdsResponse) ProtoMessage() {}

func (x *ChipIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_chip_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChipIdsResponse.ProtoReflect.Descriptor instead.
func (*ChipIdsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_chip_proto_rawDescGZIP(), []int{2}
}

func (x *ChipIdsResponse) GetChipIds() []string {
	if x != nil {
		return x.ChipIds
	}
	return nil
}

var File_frontend_proto_chip_proto protoreflect.FileDescriptor

var file_frontend_proto_chip_proto_rawDesc = []byte{
	0x0a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x68, 0x69,
	0x70, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x02, 0x0a,
	0x08, 0x43, 0x68, 0x69, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x67,
	0x65, 0x6f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x65,
	0x6f, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75,
	0x6d, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x44, 0x0a, 0x0d, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x73, 0x12, 0x41,
	0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x73, 0x65, 0x64, 0x54,
	0x73, 0x22, 0x4f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x05,
	0x63, 0x68, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x68,
	0x69, 0x70, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x63, 0x68, 0x69,
	0x70, 0x73, 0x22, 0x2c, 0x0a, 0x0f, 0x43, 0x68, 0x69, 0x70, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x69, 0x70, 0x49, 0x64, 0x73,
	0x32, 0xa0, 0x02, 0x0a, 0x0b, 0x43, 0x68, 0x69, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x5d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x68, 0x69, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64,
	0x43, 0x68, 0x69, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x68, 0x69, 0x70, 0x2e, 0x43, 0x68, 0x69, 0x70,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x68, 0x69, 0x70, 0x49, 0x64, 0x73, 0x12,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x68, 0x69, 0x70, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_chip_proto_rawDescOnce sync.Once
	file_frontend_proto_chip_proto_rawDescData = file_frontend_proto_chip_proto_rawDesc
)

func file_frontend_proto_chip_proto_rawDescGZIP() []byte {
	file_frontend_proto_chip_proto_rawDescOnce.Do(func() {
		file_frontend_proto_chip_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_chip_proto_rawDescData)
	})
	return file_frontend_proto_chip_proto_rawDescData
}

var file_frontend_proto_chip_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_frontend_proto_chip_proto_goTypes = []interface{}{
	(*ChipData)(nil),                // 0: carbon.frontend.chip.ChipData
	(*GetChipMetadataResponse)(nil), // 1: carbon.frontend.chip.GetChipMetadataResponse
	(*ChipIdsResponse)(nil),         // 2: carbon.frontend.chip.ChipIdsResponse
	(*Timestamp)(nil),               // 3: carbon.frontend.util.Timestamp
	(*Empty)(nil),                   // 4: carbon.frontend.util.Empty
}
var file_frontend_proto_chip_proto_depIdxs = []int32{
	3, // 0: carbon.frontend.chip.ChipData.downloaded_ts:type_name -> carbon.frontend.util.Timestamp
	3, // 1: carbon.frontend.chip.ChipData.last_used_ts:type_name -> carbon.frontend.util.Timestamp
	0, // 2: carbon.frontend.chip.GetChipMetadataResponse.chips:type_name -> carbon.frontend.chip.ChipData
	4, // 3: carbon.frontend.chip.ChipService.GetChipMetadata:input_type -> carbon.frontend.util.Empty
	4, // 4: carbon.frontend.chip.ChipService.GetDownloadedChipIds:input_type -> carbon.frontend.util.Empty
	4, // 5: carbon.frontend.chip.ChipService.GetSyncedChipIds:input_type -> carbon.frontend.util.Empty
	1, // 6: carbon.frontend.chip.ChipService.GetChipMetadata:output_type -> carbon.frontend.chip.GetChipMetadataResponse
	2, // 7: carbon.frontend.chip.ChipService.GetDownloadedChipIds:output_type -> carbon.frontend.chip.ChipIdsResponse
	2, // 8: carbon.frontend.chip.ChipService.GetSyncedChipIds:output_type -> carbon.frontend.chip.ChipIdsResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_frontend_proto_chip_proto_init() }
func file_frontend_proto_chip_proto_init() {
	if File_frontend_proto_chip_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_chip_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChipData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_chip_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChipMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_chip_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChipIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_chip_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_chip_proto_goTypes,
		DependencyIndexes: file_frontend_proto_chip_proto_depIdxs,
		MessageInfos:      file_frontend_proto_chip_proto_msgTypes,
	}.Build()
	File_frontend_proto_chip_proto = out.File
	file_frontend_proto_chip_proto_rawDesc = nil
	file_frontend_proto_chip_proto_goTypes = nil
	file_frontend_proto_chip_proto_depIdxs = nil
}

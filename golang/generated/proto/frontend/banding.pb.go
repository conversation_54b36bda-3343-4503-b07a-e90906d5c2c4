// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/banding.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	aimbot "github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VisualizationTypeToInclude int32

const (
	VisualizationTypeToInclude_DUPLICATE_WEED VisualizationTypeToInclude = 0
	VisualizationTypeToInclude_DUPLICATE_CROP VisualizationTypeToInclude = 1
	VisualizationTypeToInclude_KILLED_WEED    VisualizationTypeToInclude = 2
	VisualizationTypeToInclude_KILLED_CROP    VisualizationTypeToInclude = 3
	VisualizationTypeToInclude_KILLING_WEED   VisualizationTypeToInclude = 4
	VisualizationTypeToInclude_IGNORED_WEED   VisualizationTypeToInclude = 5
	VisualizationTypeToInclude_KILLING_CROP   VisualizationTypeToInclude = 6
	VisualizationTypeToInclude_ERROR_WEED     VisualizationTypeToInclude = 7
	VisualizationTypeToInclude_ERROR_CROP     VisualizationTypeToInclude = 8
	VisualizationTypeToInclude_IGNORED_CROP   VisualizationTypeToInclude = 9
	VisualizationTypeToInclude_WEED           VisualizationTypeToInclude = 10
	VisualizationTypeToInclude_CROP           VisualizationTypeToInclude = 11
	VisualizationTypeToInclude_CROP_RADIUS    VisualizationTypeToInclude = 12
	VisualizationTypeToInclude_CROP_KEPT      VisualizationTypeToInclude = 13
	VisualizationTypeToInclude_THINNING_BOX   VisualizationTypeToInclude = 14
)

// Enum value maps for VisualizationTypeToInclude.
var (
	VisualizationTypeToInclude_name = map[int32]string{
		0:  "DUPLICATE_WEED",
		1:  "DUPLICATE_CROP",
		2:  "KILLED_WEED",
		3:  "KILLED_CROP",
		4:  "KILLING_WEED",
		5:  "IGNORED_WEED",
		6:  "KILLING_CROP",
		7:  "ERROR_WEED",
		8:  "ERROR_CROP",
		9:  "IGNORED_CROP",
		10: "WEED",
		11: "CROP",
		12: "CROP_RADIUS",
		13: "CROP_KEPT",
		14: "THINNING_BOX",
	}
	VisualizationTypeToInclude_value = map[string]int32{
		"DUPLICATE_WEED": 0,
		"DUPLICATE_CROP": 1,
		"KILLED_WEED":    2,
		"KILLED_CROP":    3,
		"KILLING_WEED":   4,
		"IGNORED_WEED":   5,
		"KILLING_CROP":   6,
		"ERROR_WEED":     7,
		"ERROR_CROP":     8,
		"IGNORED_CROP":   9,
		"WEED":           10,
		"CROP":           11,
		"CROP_RADIUS":    12,
		"CROP_KEPT":      13,
		"THINNING_BOX":   14,
	}
)

func (x VisualizationTypeToInclude) Enum() *VisualizationTypeToInclude {
	p := new(VisualizationTypeToInclude)
	*p = x
	return p
}

func (x VisualizationTypeToInclude) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VisualizationTypeToInclude) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_banding_proto_enumTypes[0].Descriptor()
}

func (VisualizationTypeToInclude) Type() protoreflect.EnumType {
	return &file_frontend_proto_banding_proto_enumTypes[0]
}

func (x VisualizationTypeToInclude) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VisualizationTypeToInclude.Descriptor instead.
func (VisualizationTypeToInclude) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{0}
}

type ThresholdState int32

const (
	ThresholdState_ANY  ThresholdState = 0
	ThresholdState_PASS ThresholdState = 1
	ThresholdState_FAIL ThresholdState = 2
)

// Enum value maps for ThresholdState.
var (
	ThresholdState_name = map[int32]string{
		0: "ANY",
		1: "PASS",
		2: "FAIL",
	}
	ThresholdState_value = map[string]int32{
		"ANY":  0,
		"PASS": 1,
		"FAIL": 2,
	}
)

func (x ThresholdState) Enum() *ThresholdState {
	p := new(ThresholdState)
	*p = x
	return p
}

func (x ThresholdState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ThresholdState) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_banding_proto_enumTypes[1].Descriptor()
}

func (ThresholdState) Type() protoreflect.EnumType {
	return &file_frontend_proto_banding_proto_enumTypes[1]
}

func (x ThresholdState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ThresholdState.Descriptor instead.
func (ThresholdState) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{1}
}

type BandingRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId int32                           `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	Bands []*weed_tracking.BandDefinition `protobuf:"bytes,2,rep,name=bands,proto3" json:"bands,omitempty"`
}

func (x *BandingRow) Reset() {
	*x = BandingRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BandingRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BandingRow) ProtoMessage() {}

func (x *BandingRow) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BandingRow.ProtoReflect.Descriptor instead.
func (*BandingRow) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{0}
}

func (x *BandingRow) GetRowId() int32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *BandingRow) GetBands() []*weed_tracking.BandDefinition {
	if x != nil {
		return x.Bands
	}
	return nil
}

type BandingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Rows []*BandingRow `protobuf:"bytes,2,rep,name=rows,proto3" json:"rows,omitempty"`
	Uuid string        `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *BandingDef) Reset() {
	*x = BandingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BandingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BandingDef) ProtoMessage() {}

func (x *BandingDef) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BandingDef.ProtoReflect.Descriptor instead.
func (*BandingDef) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{1}
}

func (x *BandingDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BandingDef) GetRows() []*BandingRow {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *BandingDef) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type SaveBandingDefRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BandingDef *BandingDef `protobuf:"bytes,1,opt,name=bandingDef,proto3" json:"bandingDef,omitempty"`
	SetActive  bool        `protobuf:"varint,2,opt,name=setActive,proto3" json:"setActive,omitempty"`
}

func (x *SaveBandingDefRequest) Reset() {
	*x = SaveBandingDefRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveBandingDefRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveBandingDefRequest) ProtoMessage() {}

func (x *SaveBandingDefRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveBandingDefRequest.ProtoReflect.Descriptor instead.
func (*SaveBandingDefRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{2}
}

func (x *SaveBandingDefRequest) GetBandingDef() *BandingDef {
	if x != nil {
		return x.BandingDef
	}
	return nil
}

func (x *SaveBandingDefRequest) GetSetActive() bool {
	if x != nil {
		return x.SetActive
	}
	return false
}

type LoadBandingDefsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BandingDefs []*BandingDef `protobuf:"bytes,1,rep,name=bandingDefs,proto3" json:"bandingDefs,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
	ActiveDef     string `protobuf:"bytes,2,opt,name=activeDef,proto3" json:"activeDef,omitempty"`
	ActiveDefUUID string `protobuf:"bytes,3,opt,name=activeDefUUID,proto3" json:"activeDefUUID,omitempty"`
}

func (x *LoadBandingDefsResponse) Reset() {
	*x = LoadBandingDefsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBandingDefsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBandingDefsResponse) ProtoMessage() {}

func (x *LoadBandingDefsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBandingDefsResponse.ProtoReflect.Descriptor instead.
func (*LoadBandingDefsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{3}
}

func (x *LoadBandingDefsResponse) GetBandingDefs() []*BandingDef {
	if x != nil {
		return x.BandingDefs
	}
	return nil
}

// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
func (x *LoadBandingDefsResponse) GetActiveDef() string {
	if x != nil {
		return x.ActiveDef
	}
	return ""
}

func (x *LoadBandingDefsResponse) GetActiveDefUUID() string {
	if x != nil {
		return x.ActiveDefUUID
	}
	return ""
}

type SetActiveBandingDefRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *SetActiveBandingDefRequest) Reset() {
	*x = SetActiveBandingDefRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveBandingDefRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveBandingDefRequest) ProtoMessage() {}

func (x *SetActiveBandingDefRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveBandingDefRequest.ProtoReflect.Descriptor instead.
func (*SetActiveBandingDefRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{4}
}

// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
func (x *SetActiveBandingDefRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SetActiveBandingDefRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type GetActiveBandingDefResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *GetActiveBandingDefResponse) Reset() {
	*x = GetActiveBandingDefResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveBandingDefResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveBandingDefResponse) ProtoMessage() {}

func (x *GetActiveBandingDefResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveBandingDefResponse.ProtoReflect.Descriptor instead.
func (*GetActiveBandingDefResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
func (x *GetActiveBandingDefResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetActiveBandingDefResponse) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type DeleteBandingDefRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *DeleteBandingDefRequest) Reset() {
	*x = DeleteBandingDefRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBandingDefRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBandingDefRequest) ProtoMessage() {}

func (x *DeleteBandingDefRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBandingDefRequest.ProtoReflect.Descriptor instead.
func (*DeleteBandingDefRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{6}
}

// Deprecated: Marked as deprecated in frontend/proto/banding.proto.
func (x *DeleteBandingDefRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteBandingDefRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type VisualizationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XMm    int32 `protobuf:"varint,1,opt,name=x_mm,json=xMm,proto3" json:"x_mm,omitempty"`
	YMm    int32 `protobuf:"varint,2,opt,name=y_mm,json=yMm,proto3" json:"y_mm,omitempty"`
	ZMm    int32 `protobuf:"varint,3,opt,name=z_mm,json=zMm,proto3" json:"z_mm,omitempty"`
	IsWeed bool  `protobuf:"varint,4,opt,name=is_weed,json=isWeed,proto3" json:"is_weed,omitempty"`
}

func (x *VisualizationData) Reset() {
	*x = VisualizationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualizationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualizationData) ProtoMessage() {}

func (x *VisualizationData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualizationData.ProtoReflect.Descriptor instead.
func (*VisualizationData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{7}
}

func (x *VisualizationData) GetXMm() int32 {
	if x != nil {
		return x.XMm
	}
	return 0
}

func (x *VisualizationData) GetYMm() int32 {
	if x != nil {
		return x.YMm
	}
	return 0
}

func (x *VisualizationData) GetZMm() int32 {
	if x != nil {
		return x.ZMm
	}
	return 0
}

func (x *VisualizationData) GetIsWeed() bool {
	if x != nil {
		return x.IsWeed
	}
	return false
}

type GetNextVisualizationDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts               *Timestamp                   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	RowId            int32                        `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	TypesToInclude   []VisualizationTypeToInclude `protobuf:"varint,3,rep,packed,name=types_to_include,json=typesToInclude,proto3,enum=carbon.frontend.banding.VisualizationTypeToInclude" json:"types_to_include,omitempty"`
	ThresholdFilters *ThresholdFilters            `protobuf:"bytes,4,opt,name=threshold_filters,json=thresholdFilters,proto3" json:"threshold_filters,omitempty"`
}

func (x *GetNextVisualizationDataRequest) Reset() {
	*x = GetNextVisualizationDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVisualizationDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVisualizationDataRequest) ProtoMessage() {}

func (x *GetNextVisualizationDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVisualizationDataRequest.ProtoReflect.Descriptor instead.
func (*GetNextVisualizationDataRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{8}
}

func (x *GetNextVisualizationDataRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextVisualizationDataRequest) GetRowId() int32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetNextVisualizationDataRequest) GetTypesToInclude() []VisualizationTypeToInclude {
	if x != nil {
		return x.TypesToInclude
	}
	return nil
}

func (x *GetNextVisualizationDataRequest) GetThresholdFilters() *ThresholdFilters {
	if x != nil {
		return x.ThresholdFilters
	}
	return nil
}

type GetNextVisualizationDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp                      `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Data  []*VisualizationData            `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	Bands []*weed_tracking.BandDefinition `protobuf:"bytes,3,rep,name=bands,proto3" json:"bands,omitempty"`
}

func (x *GetNextVisualizationDataResponse) Reset() {
	*x = GetNextVisualizationDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVisualizationDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVisualizationDataResponse) ProtoMessage() {}

func (x *GetNextVisualizationDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVisualizationDataResponse.ProtoReflect.Descriptor instead.
func (*GetNextVisualizationDataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{9}
}

func (x *GetNextVisualizationDataResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextVisualizationDataResponse) GetData() []*VisualizationData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetNextVisualizationDataResponse) GetBands() []*weed_tracking.BandDefinition {
	if x != nil {
		return x.Bands
	}
	return nil
}

type GetNextVisualizationData2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *weed_tracking.DiagnosticsSnapshot `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Ts   *Timestamp                         `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextVisualizationData2Response) Reset() {
	*x = GetNextVisualizationData2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVisualizationData2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVisualizationData2Response) ProtoMessage() {}

func (x *GetNextVisualizationData2Response) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVisualizationData2Response.ProtoReflect.Descriptor instead.
func (*GetNextVisualizationData2Response) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{10}
}

func (x *GetNextVisualizationData2Response) GetData() *weed_tracking.DiagnosticsSnapshot {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetNextVisualizationData2Response) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetDimensionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId int32 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
}

func (x *GetDimensionsRequest) Reset() {
	*x = GetDimensionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDimensionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDimensionsRequest) ProtoMessage() {}

func (x *GetDimensionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDimensionsRequest.ProtoReflect.Descriptor instead.
func (*GetDimensionsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{11}
}

func (x *GetDimensionsRequest) GetRowId() int32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

type SetBandingEnabledRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetBandingEnabledRequest) Reset() {
	*x = SetBandingEnabledRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBandingEnabledRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBandingEnabledRequest) ProtoMessage() {}

func (x *SetBandingEnabledRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBandingEnabledRequest.ProtoReflect.Descriptor instead.
func (*SetBandingEnabledRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{12}
}

func (x *SetBandingEnabledRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetBandingEnabledResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetBandingEnabledResponse) Reset() {
	*x = SetBandingEnabledResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBandingEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBandingEnabledResponse) ProtoMessage() {}

func (x *SetBandingEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBandingEnabledResponse.ProtoReflect.Descriptor instead.
func (*SetBandingEnabledResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{13}
}

type IsBandingEnabledResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *IsBandingEnabledResponse) Reset() {
	*x = IsBandingEnabledResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsBandingEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsBandingEnabledResponse) ProtoMessage() {}

func (x *IsBandingEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsBandingEnabledResponse.ProtoReflect.Descriptor instead.
func (*IsBandingEnabledResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{14}
}

func (x *IsBandingEnabledResponse) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type GetVisualizationMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CropSafetyRadiusMmPerRow map[int32]float32 `protobuf:"bytes,1,rep,name=crop_safety_radius_mm_per_row,json=cropSafetyRadiusMmPerRow,proto3" json:"crop_safety_radius_mm_per_row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *GetVisualizationMetadataResponse) Reset() {
	*x = GetVisualizationMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisualizationMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisualizationMetadataResponse) ProtoMessage() {}

func (x *GetVisualizationMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisualizationMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetVisualizationMetadataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{15}
}

func (x *GetVisualizationMetadataResponse) GetCropSafetyRadiusMmPerRow() map[int32]float32 {
	if x != nil {
		return x.CropSafetyRadiusMmPerRow
	}
	return nil
}

type ThresholdFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Weeding  ThresholdState `protobuf:"varint,1,opt,name=weeding,proto3,enum=carbon.frontend.banding.ThresholdState" json:"weeding,omitempty"`
	Thinning ThresholdState `protobuf:"varint,2,opt,name=thinning,proto3,enum=carbon.frontend.banding.ThresholdState" json:"thinning,omitempty"`
	Banding  ThresholdState `protobuf:"varint,3,opt,name=banding,proto3,enum=carbon.frontend.banding.ThresholdState" json:"banding,omitempty"`
}

func (x *ThresholdFilter) Reset() {
	*x = ThresholdFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThresholdFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThresholdFilter) ProtoMessage() {}

func (x *ThresholdFilter) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThresholdFilter.ProtoReflect.Descriptor instead.
func (*ThresholdFilter) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{16}
}

func (x *ThresholdFilter) GetWeeding() ThresholdState {
	if x != nil {
		return x.Weeding
	}
	return ThresholdState_ANY
}

func (x *ThresholdFilter) GetThinning() ThresholdState {
	if x != nil {
		return x.Thinning
	}
	return ThresholdState_ANY
}

func (x *ThresholdFilter) GetBanding() ThresholdState {
	if x != nil {
		return x.Banding
	}
	return ThresholdState_ANY
}

type ThresholdFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crop *ThresholdFilter `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"`
	Weed *ThresholdFilter `protobuf:"bytes,2,opt,name=weed,proto3" json:"weed,omitempty"`
}

func (x *ThresholdFilters) Reset() {
	*x = ThresholdFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThresholdFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThresholdFilters) ProtoMessage() {}

func (x *ThresholdFilters) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThresholdFilters.ProtoReflect.Descriptor instead.
func (*ThresholdFilters) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{17}
}

func (x *ThresholdFilters) GetCrop() *ThresholdFilter {
	if x != nil {
		return x.Crop
	}
	return nil
}

func (x *ThresholdFilters) GetWeed() *ThresholdFilter {
	if x != nil {
		return x.Weed
	}
	return nil
}

type GetNextVisualizationDataForAllRowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts               *Timestamp                   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	TypesToInclude   []VisualizationTypeToInclude `protobuf:"varint,2,rep,packed,name=types_to_include,json=typesToInclude,proto3,enum=carbon.frontend.banding.VisualizationTypeToInclude" json:"types_to_include,omitempty"`
	ThresholdFilters *ThresholdFilters            `protobuf:"bytes,3,opt,name=threshold_filters,json=thresholdFilters,proto3" json:"threshold_filters,omitempty"`
}

func (x *GetNextVisualizationDataForAllRowsRequest) Reset() {
	*x = GetNextVisualizationDataForAllRowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVisualizationDataForAllRowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVisualizationDataForAllRowsRequest) ProtoMessage() {}

func (x *GetNextVisualizationDataForAllRowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVisualizationDataForAllRowsRequest.ProtoReflect.Descriptor instead.
func (*GetNextVisualizationDataForAllRowsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{18}
}

func (x *GetNextVisualizationDataForAllRowsRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextVisualizationDataForAllRowsRequest) GetTypesToInclude() []VisualizationTypeToInclude {
	if x != nil {
		return x.TypesToInclude
	}
	return nil
}

func (x *GetNextVisualizationDataForAllRowsRequest) GetThresholdFilters() *ThresholdFilters {
	if x != nil {
		return x.ThresholdFilters
	}
	return nil
}

type GetNextVisualizationDataForAllRowsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataPerRow     map[int32]*weed_tracking.DiagnosticsSnapshot `protobuf:"bytes,1,rep,name=data_per_row,json=dataPerRow,proto3" json:"data_per_row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Ts             *Timestamp                                   `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	TypesToInclude []VisualizationTypeToInclude                 `protobuf:"varint,3,rep,packed,name=types_to_include,json=typesToInclude,proto3,enum=carbon.frontend.banding.VisualizationTypeToInclude" json:"types_to_include,omitempty"`
}

func (x *GetNextVisualizationDataForAllRowsResponse) Reset() {
	*x = GetNextVisualizationDataForAllRowsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVisualizationDataForAllRowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVisualizationDataForAllRowsResponse) ProtoMessage() {}

func (x *GetNextVisualizationDataForAllRowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVisualizationDataForAllRowsResponse.ProtoReflect.Descriptor instead.
func (*GetNextVisualizationDataForAllRowsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{19}
}

func (x *GetNextVisualizationDataForAllRowsResponse) GetDataPerRow() map[int32]*weed_tracking.DiagnosticsSnapshot {
	if x != nil {
		return x.DataPerRow
	}
	return nil
}

func (x *GetNextVisualizationDataForAllRowsResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextVisualizationDataForAllRowsResponse) GetTypesToInclude() []VisualizationTypeToInclude {
	if x != nil {
		return x.TypesToInclude
	}
	return nil
}

type GetNextBandingStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                      *Timestamp    `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	BandingDefs             []*BandingDef `protobuf:"bytes,2,rep,name=bandingDefs,proto3" json:"bandingDefs,omitempty"`
	ActiveDefUUID           string        `protobuf:"bytes,3,opt,name=activeDefUUID,proto3" json:"activeDefUUID,omitempty"`
	IsBandingEnabled        bool          `protobuf:"varint,4,opt,name=is_banding_enabled,json=isBandingEnabled,proto3" json:"is_banding_enabled,omitempty"`
	IsDynamicBandingEnabled bool          `protobuf:"varint,5,opt,name=is_dynamic_banding_enabled,json=isDynamicBandingEnabled,proto3" json:"is_dynamic_banding_enabled,omitempty"`
}

func (x *GetNextBandingStateResponse) Reset() {
	*x = GetNextBandingStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_banding_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextBandingStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextBandingStateResponse) ProtoMessage() {}

func (x *GetNextBandingStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_banding_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextBandingStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextBandingStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_banding_proto_rawDescGZIP(), []int{20}
}

func (x *GetNextBandingStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextBandingStateResponse) GetBandingDefs() []*BandingDef {
	if x != nil {
		return x.BandingDefs
	}
	return nil
}

func (x *GetNextBandingStateResponse) GetActiveDefUUID() string {
	if x != nil {
		return x.ActiveDefUUID
	}
	return ""
}

func (x *GetNextBandingStateResponse) GetIsBandingEnabled() bool {
	if x != nil {
		return x.IsBandingEnabled
	}
	return false
}

func (x *GetNextBandingStateResponse) GetIsDynamicBandingEnabled() bool {
	if x != nil {
		return x.IsDynamicBandingEnabled
	}
	return false
}

var File_frontend_proto_banding_proto protoreflect.FileDescriptor

var file_frontend_proto_banding_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x48, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x73, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x2f, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x6f, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x62, 0x61,
	0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x22,
	0x6d, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x37, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x6f, 0x77, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x7a,
	0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0a, 0x62, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x52, 0x0a, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x73, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x17, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x52, 0x0b, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x73, 0x12, 0x20, 0x0a,
	0x09, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x24, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65, 0x66, 0x55, 0x55, 0x49, 0x44,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65,
	0x66, 0x55, 0x55, 0x49, 0x44, 0x22, 0x48, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22,
	0x49, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x45, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x22, 0x65, 0x0a, 0x11, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x11, 0x0a, 0x04, 0x78, 0x5f, 0x6d, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x78, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x6d,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x79, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04,
	0x7a, 0x5f, 0x6d, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x7a, 0x4d, 0x6d, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x22, 0xa0, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x15, 0x0a,
	0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72,
	0x6f, 0x77, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x10, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f, 0x74, 0x6f,
	0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x33,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x52, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x54, 0x6f, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x10, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74,
	0x73, 0x12, 0x3e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x33, 0x0a, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0x2d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72,
	0x6f, 0x77, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x53, 0x65,
	0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x34, 0x0a, 0x18, 0x49, 0x73, 0x42, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x8a, 0x02,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x1d, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x6d, 0x6d, 0x5f, 0x70, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4d, 0x6d, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x18, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x52,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x4d, 0x6d, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x1a, 0x4b, 0x0a,
	0x1d, 0x43, 0x72, 0x6f, 0x70, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x52, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x4d, 0x6d, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdc, 0x01, 0x0a, 0x0f, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x41,
	0x0a, 0x07, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x43, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x68,
	0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x41, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x8e, 0x01, 0x0a, 0x10, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3c,
	0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x3c, 0x0a, 0x04,
	0x77, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x04, 0x77, 0x65, 0x65, 0x64, 0x22, 0x93, 0x02, 0x0a, 0x29, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x6f, 0x77,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x5d, 0x0a, 0x10, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x6f, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x52, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x54,
	0x6f, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x10,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x22, 0x96, 0x03, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72,
	0x41, 0x6c, 0x6c, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x75, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x6f,
	0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x50,
	0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x5d, 0x0a, 0x10, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x5f, 0x74, 0x6f, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x49,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x52, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x54, 0x6f, 0x49,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x1a, 0x61, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x61, 0x50, 0x65,
	0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa6, 0x02, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x73, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x65, 0x66, 0x55, 0x55,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x65, 0x66, 0x55, 0x55, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x2a, 0x94, 0x02, 0x0a, 0x1a, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x57,
	0x45, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x45, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4b, 0x49, 0x4c,
	0x4c, 0x45, 0x44, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4b, 0x49,
	0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4b,
	0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x04, 0x12, 0x10, 0x0a,
	0x0c, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x44, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x05, 0x12,
	0x10, 0x0a, 0x0c, 0x4b, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x10,
	0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10,
	0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x10,
	0x08, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x4f,
	0x50, 0x10, 0x09, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x45, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x08, 0x0a,
	0x04, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x4f, 0x50, 0x5f,
	0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x52, 0x4f, 0x50,
	0x5f, 0x4b, 0x45, 0x50, 0x54, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x48, 0x49, 0x4e, 0x4e,
	0x49, 0x4e, 0x47, 0x5f, 0x42, 0x4f, 0x58, 0x10, 0x0e, 0x2a, 0x2d, 0x0a, 0x0e, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41,
	0x4e, 0x59, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x32, 0xed, 0x0d, 0x0a, 0x0e, 0x42, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60, 0x0a, 0x0f, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x73, 0x12, 0x1b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x30, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x66, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a,
	0x0e, 0x53, 0x61, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12,
	0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x42, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x12, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x67, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x68, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x34, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x32, 0x12, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x32,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x6f, 0x77, 0x73, 0x12,
	0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x6f, 0x77, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x42, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x31, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x10, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49,
	0x73, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x65, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x17, 0x49,
	0x73, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73,
	0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x1a, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_frontend_proto_banding_proto_rawDescOnce sync.Once
	file_frontend_proto_banding_proto_rawDescData = file_frontend_proto_banding_proto_rawDesc
)

func file_frontend_proto_banding_proto_rawDescGZIP() []byte {
	file_frontend_proto_banding_proto_rawDescOnce.Do(func() {
		file_frontend_proto_banding_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_banding_proto_rawDescData)
	})
	return file_frontend_proto_banding_proto_rawDescData
}

var file_frontend_proto_banding_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_proto_banding_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_frontend_proto_banding_proto_goTypes = []interface{}{
	(VisualizationTypeToInclude)(0),                    // 0: carbon.frontend.banding.VisualizationTypeToInclude
	(ThresholdState)(0),                                // 1: carbon.frontend.banding.ThresholdState
	(*BandingRow)(nil),                                 // 2: carbon.frontend.banding.BandingRow
	(*BandingDef)(nil),                                 // 3: carbon.frontend.banding.BandingDef
	(*SaveBandingDefRequest)(nil),                      // 4: carbon.frontend.banding.SaveBandingDefRequest
	(*LoadBandingDefsResponse)(nil),                    // 5: carbon.frontend.banding.LoadBandingDefsResponse
	(*SetActiveBandingDefRequest)(nil),                 // 6: carbon.frontend.banding.SetActiveBandingDefRequest
	(*GetActiveBandingDefResponse)(nil),                // 7: carbon.frontend.banding.GetActiveBandingDefResponse
	(*DeleteBandingDefRequest)(nil),                    // 8: carbon.frontend.banding.DeleteBandingDefRequest
	(*VisualizationData)(nil),                          // 9: carbon.frontend.banding.VisualizationData
	(*GetNextVisualizationDataRequest)(nil),            // 10: carbon.frontend.banding.GetNextVisualizationDataRequest
	(*GetNextVisualizationDataResponse)(nil),           // 11: carbon.frontend.banding.GetNextVisualizationDataResponse
	(*GetNextVisualizationData2Response)(nil),          // 12: carbon.frontend.banding.GetNextVisualizationData2Response
	(*GetDimensionsRequest)(nil),                       // 13: carbon.frontend.banding.GetDimensionsRequest
	(*SetBandingEnabledRequest)(nil),                   // 14: carbon.frontend.banding.SetBandingEnabledRequest
	(*SetBandingEnabledResponse)(nil),                  // 15: carbon.frontend.banding.SetBandingEnabledResponse
	(*IsBandingEnabledResponse)(nil),                   // 16: carbon.frontend.banding.IsBandingEnabledResponse
	(*GetVisualizationMetadataResponse)(nil),           // 17: carbon.frontend.banding.GetVisualizationMetadataResponse
	(*ThresholdFilter)(nil),                            // 18: carbon.frontend.banding.ThresholdFilter
	(*ThresholdFilters)(nil),                           // 19: carbon.frontend.banding.ThresholdFilters
	(*GetNextVisualizationDataForAllRowsRequest)(nil),  // 20: carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest
	(*GetNextVisualizationDataForAllRowsResponse)(nil), // 21: carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse
	(*GetNextBandingStateResponse)(nil),                // 22: carbon.frontend.banding.GetNextBandingStateResponse
	nil,                                                // 23: carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry
	nil,                                                // 24: carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry
	(*weed_tracking.BandDefinition)(nil),               // 25: weed_tracking.BandDefinition
	(*Timestamp)(nil),                                  // 26: carbon.frontend.util.Timestamp
	(*weed_tracking.DiagnosticsSnapshot)(nil),          // 27: weed_tracking.DiagnosticsSnapshot
	(*Empty)(nil),                                      // 28: carbon.frontend.util.Empty
	(*aimbot.GetDimensionsResponse)(nil),               // 29: aimbot.GetDimensionsResponse
}
var file_frontend_proto_banding_proto_depIdxs = []int32{
	25, // 0: carbon.frontend.banding.BandingRow.bands:type_name -> weed_tracking.BandDefinition
	2,  // 1: carbon.frontend.banding.BandingDef.rows:type_name -> carbon.frontend.banding.BandingRow
	3,  // 2: carbon.frontend.banding.SaveBandingDefRequest.bandingDef:type_name -> carbon.frontend.banding.BandingDef
	3,  // 3: carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs:type_name -> carbon.frontend.banding.BandingDef
	26, // 4: carbon.frontend.banding.GetNextVisualizationDataRequest.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 5: carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include:type_name -> carbon.frontend.banding.VisualizationTypeToInclude
	19, // 6: carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters:type_name -> carbon.frontend.banding.ThresholdFilters
	26, // 7: carbon.frontend.banding.GetNextVisualizationDataResponse.ts:type_name -> carbon.frontend.util.Timestamp
	9,  // 8: carbon.frontend.banding.GetNextVisualizationDataResponse.data:type_name -> carbon.frontend.banding.VisualizationData
	25, // 9: carbon.frontend.banding.GetNextVisualizationDataResponse.bands:type_name -> weed_tracking.BandDefinition
	27, // 10: carbon.frontend.banding.GetNextVisualizationData2Response.data:type_name -> weed_tracking.DiagnosticsSnapshot
	26, // 11: carbon.frontend.banding.GetNextVisualizationData2Response.ts:type_name -> carbon.frontend.util.Timestamp
	23, // 12: carbon.frontend.banding.GetVisualizationMetadataResponse.crop_safety_radius_mm_per_row:type_name -> carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry
	1,  // 13: carbon.frontend.banding.ThresholdFilter.weeding:type_name -> carbon.frontend.banding.ThresholdState
	1,  // 14: carbon.frontend.banding.ThresholdFilter.thinning:type_name -> carbon.frontend.banding.ThresholdState
	1,  // 15: carbon.frontend.banding.ThresholdFilter.banding:type_name -> carbon.frontend.banding.ThresholdState
	18, // 16: carbon.frontend.banding.ThresholdFilters.crop:type_name -> carbon.frontend.banding.ThresholdFilter
	18, // 17: carbon.frontend.banding.ThresholdFilters.weed:type_name -> carbon.frontend.banding.ThresholdFilter
	26, // 18: carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 19: carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include:type_name -> carbon.frontend.banding.VisualizationTypeToInclude
	19, // 20: carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters:type_name -> carbon.frontend.banding.ThresholdFilters
	24, // 21: carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.data_per_row:type_name -> carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry
	26, // 22: carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 23: carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include:type_name -> carbon.frontend.banding.VisualizationTypeToInclude
	26, // 24: carbon.frontend.banding.GetNextBandingStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	3,  // 25: carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs:type_name -> carbon.frontend.banding.BandingDef
	27, // 26: carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry.value:type_name -> weed_tracking.DiagnosticsSnapshot
	28, // 27: carbon.frontend.banding.BandingService.LoadBandingDefs:input_type -> carbon.frontend.util.Empty
	4,  // 28: carbon.frontend.banding.BandingService.SaveBandingDef:input_type -> carbon.frontend.banding.SaveBandingDefRequest
	8,  // 29: carbon.frontend.banding.BandingService.DeleteBandingDef:input_type -> carbon.frontend.banding.DeleteBandingDefRequest
	6,  // 30: carbon.frontend.banding.BandingService.SetActiveBandingDef:input_type -> carbon.frontend.banding.SetActiveBandingDefRequest
	28, // 31: carbon.frontend.banding.BandingService.GetActiveBandingDef:input_type -> carbon.frontend.util.Empty
	10, // 32: carbon.frontend.banding.BandingService.GetNextVisualizationData:input_type -> carbon.frontend.banding.GetNextVisualizationDataRequest
	10, // 33: carbon.frontend.banding.BandingService.GetNextVisualizationData2:input_type -> carbon.frontend.banding.GetNextVisualizationDataRequest
	20, // 34: carbon.frontend.banding.BandingService.GetNextVisualizationDataForAllRows:input_type -> carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest
	13, // 35: carbon.frontend.banding.BandingService.GetDimensions:input_type -> carbon.frontend.banding.GetDimensionsRequest
	14, // 36: carbon.frontend.banding.BandingService.SetBandingEnabled:input_type -> carbon.frontend.banding.SetBandingEnabledRequest
	28, // 37: carbon.frontend.banding.BandingService.IsBandingEnabled:input_type -> carbon.frontend.util.Empty
	14, // 38: carbon.frontend.banding.BandingService.SetDynamicBandingEnabled:input_type -> carbon.frontend.banding.SetBandingEnabledRequest
	28, // 39: carbon.frontend.banding.BandingService.IsDynamicBandingEnabled:input_type -> carbon.frontend.util.Empty
	28, // 40: carbon.frontend.banding.BandingService.GetVisualizationMetadata:input_type -> carbon.frontend.util.Empty
	26, // 41: carbon.frontend.banding.BandingService.GetNextBandingState:input_type -> carbon.frontend.util.Timestamp
	5,  // 42: carbon.frontend.banding.BandingService.LoadBandingDefs:output_type -> carbon.frontend.banding.LoadBandingDefsResponse
	28, // 43: carbon.frontend.banding.BandingService.SaveBandingDef:output_type -> carbon.frontend.util.Empty
	28, // 44: carbon.frontend.banding.BandingService.DeleteBandingDef:output_type -> carbon.frontend.util.Empty
	28, // 45: carbon.frontend.banding.BandingService.SetActiveBandingDef:output_type -> carbon.frontend.util.Empty
	7,  // 46: carbon.frontend.banding.BandingService.GetActiveBandingDef:output_type -> carbon.frontend.banding.GetActiveBandingDefResponse
	11, // 47: carbon.frontend.banding.BandingService.GetNextVisualizationData:output_type -> carbon.frontend.banding.GetNextVisualizationDataResponse
	12, // 48: carbon.frontend.banding.BandingService.GetNextVisualizationData2:output_type -> carbon.frontend.banding.GetNextVisualizationData2Response
	21, // 49: carbon.frontend.banding.BandingService.GetNextVisualizationDataForAllRows:output_type -> carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse
	29, // 50: carbon.frontend.banding.BandingService.GetDimensions:output_type -> aimbot.GetDimensionsResponse
	15, // 51: carbon.frontend.banding.BandingService.SetBandingEnabled:output_type -> carbon.frontend.banding.SetBandingEnabledResponse
	16, // 52: carbon.frontend.banding.BandingService.IsBandingEnabled:output_type -> carbon.frontend.banding.IsBandingEnabledResponse
	15, // 53: carbon.frontend.banding.BandingService.SetDynamicBandingEnabled:output_type -> carbon.frontend.banding.SetBandingEnabledResponse
	16, // 54: carbon.frontend.banding.BandingService.IsDynamicBandingEnabled:output_type -> carbon.frontend.banding.IsBandingEnabledResponse
	17, // 55: carbon.frontend.banding.BandingService.GetVisualizationMetadata:output_type -> carbon.frontend.banding.GetVisualizationMetadataResponse
	22, // 56: carbon.frontend.banding.BandingService.GetNextBandingState:output_type -> carbon.frontend.banding.GetNextBandingStateResponse
	42, // [42:57] is the sub-list for method output_type
	27, // [27:42] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_frontend_proto_banding_proto_init() }
func file_frontend_proto_banding_proto_init() {
	if File_frontend_proto_banding_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_banding_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BandingRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BandingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveBandingDefRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBandingDefsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveBandingDefRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveBandingDefResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBandingDefRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualizationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVisualizationDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVisualizationDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVisualizationData2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDimensionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBandingEnabledRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBandingEnabledResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsBandingEnabledResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVisualizationMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThresholdFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThresholdFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVisualizationDataForAllRowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVisualizationDataForAllRowsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_banding_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextBandingStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_banding_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_banding_proto_goTypes,
		DependencyIndexes: file_frontend_proto_banding_proto_depIdxs,
		EnumInfos:         file_frontend_proto_banding_proto_enumTypes,
		MessageInfos:      file_frontend_proto_banding_proto_msgTypes,
	}.Build()
	File_frontend_proto_banding_proto = out.File
	file_frontend_proto_banding_proto_rawDesc = nil
	file_frontend_proto_banding_proto_goTypes = nil
	file_frontend_proto_banding_proto_depIdxs = nil
}

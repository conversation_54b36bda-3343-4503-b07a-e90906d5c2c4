// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/almanac.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AlmanacConfigService_GetConfigData_FullMethodName                = "/carbon.frontend.almanac.AlmanacConfigService/GetConfigData"
	AlmanacConfigService_GetNextConfigData_FullMethodName            = "/carbon.frontend.almanac.AlmanacConfigService/GetNextConfigData"
	AlmanacConfigService_LoadAlmanacConfig_FullMethodName            = "/carbon.frontend.almanac.AlmanacConfigService/LoadAlmanacConfig"
	AlmanacConfigService_SaveAlmanacConfig_FullMethodName            = "/carbon.frontend.almanac.AlmanacConfigService/SaveAlmanacConfig"
	AlmanacConfigService_SetActiveAlmanacConfig_FullMethodName       = "/carbon.frontend.almanac.AlmanacConfigService/SetActiveAlmanacConfig"
	AlmanacConfigService_DeleteAlmanacConfig_FullMethodName          = "/carbon.frontend.almanac.AlmanacConfigService/DeleteAlmanacConfig"
	AlmanacConfigService_GetNextAlmanacConfig_FullMethodName         = "/carbon.frontend.almanac.AlmanacConfigService/GetNextAlmanacConfig"
	AlmanacConfigService_LoadDiscriminatorConfig_FullMethodName      = "/carbon.frontend.almanac.AlmanacConfigService/LoadDiscriminatorConfig"
	AlmanacConfigService_SaveDiscriminatorConfig_FullMethodName      = "/carbon.frontend.almanac.AlmanacConfigService/SaveDiscriminatorConfig"
	AlmanacConfigService_SetActiveDiscriminatorConfig_FullMethodName = "/carbon.frontend.almanac.AlmanacConfigService/SetActiveDiscriminatorConfig"
	AlmanacConfigService_DeleteDiscriminatorConfig_FullMethodName    = "/carbon.frontend.almanac.AlmanacConfigService/DeleteDiscriminatorConfig"
	AlmanacConfigService_GetNextDiscriminatorConfig_FullMethodName   = "/carbon.frontend.almanac.AlmanacConfigService/GetNextDiscriminatorConfig"
	AlmanacConfigService_GetNextModelinatorConfig_FullMethodName     = "/carbon.frontend.almanac.AlmanacConfigService/GetNextModelinatorConfig"
	AlmanacConfigService_SaveModelinatorConfig_FullMethodName        = "/carbon.frontend.almanac.AlmanacConfigService/SaveModelinatorConfig"
	AlmanacConfigService_FetchModelinatorConfig_FullMethodName       = "/carbon.frontend.almanac.AlmanacConfigService/FetchModelinatorConfig"
	AlmanacConfigService_ResetModelinatorConfig_FullMethodName       = "/carbon.frontend.almanac.AlmanacConfigService/ResetModelinatorConfig"
)

// AlmanacConfigServiceClient is the client API for AlmanacConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AlmanacConfigServiceClient interface {
	// Deprecated: Do not use.
	GetConfigData(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetConfigDataResponse, error)
	GetNextConfigData(ctx context.Context, in *GetNextConfigDataRequest, opts ...grpc.CallOption) (*GetNextConfigDataResponse, error)
	LoadAlmanacConfig(ctx context.Context, in *LoadAlmanacConfigRequest, opts ...grpc.CallOption) (*LoadAlmanacConfigResponse, error)
	SaveAlmanacConfig(ctx context.Context, in *SaveAlmanacConfigRequest, opts ...grpc.CallOption) (*SaveAlmanacConfigResponse, error)
	SetActiveAlmanacConfig(ctx context.Context, in *SetActiveAlmanacConfigRequest, opts ...grpc.CallOption) (*Empty, error)
	DeleteAlmanacConfig(ctx context.Context, in *DeleteAlmanacConfigRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextAlmanacConfig(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextAlmanacConfigResponse, error)
	LoadDiscriminatorConfig(ctx context.Context, in *LoadDiscriminatorConfigRequest, opts ...grpc.CallOption) (*LoadDiscriminatorConfigResponse, error)
	SaveDiscriminatorConfig(ctx context.Context, in *SaveDiscriminatorConfigRequest, opts ...grpc.CallOption) (*SaveDiscriminatorConfigResponse, error)
	SetActiveDiscriminatorConfig(ctx context.Context, in *SetActiveDiscriminatorConfigRequest, opts ...grpc.CallOption) (*Empty, error)
	DeleteDiscriminatorConfig(ctx context.Context, in *DeleteDiscriminatorConfigRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextDiscriminatorConfig(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextDiscriminatorConfigResponse, error)
	GetNextModelinatorConfig(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextModelinatorConfigResponse, error)
	SaveModelinatorConfig(ctx context.Context, in *SaveModelinatorConfigRequest, opts ...grpc.CallOption) (*Empty, error)
	FetchModelinatorConfig(ctx context.Context, in *FetchModelinatorConfigRequest, opts ...grpc.CallOption) (*FetchModelinatorConfigResponse, error)
	ResetModelinatorConfig(ctx context.Context, in *ResetModelinatorConfigRequest, opts ...grpc.CallOption) (*Empty, error)
}

type almanacConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAlmanacConfigServiceClient(cc grpc.ClientConnInterface) AlmanacConfigServiceClient {
	return &almanacConfigServiceClient{cc}
}

// Deprecated: Do not use.
func (c *almanacConfigServiceClient) GetConfigData(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetConfigDataResponse, error) {
	out := new(GetConfigDataResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_GetConfigData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) GetNextConfigData(ctx context.Context, in *GetNextConfigDataRequest, opts ...grpc.CallOption) (*GetNextConfigDataResponse, error) {
	out := new(GetNextConfigDataResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_GetNextConfigData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) LoadAlmanacConfig(ctx context.Context, in *LoadAlmanacConfigRequest, opts ...grpc.CallOption) (*LoadAlmanacConfigResponse, error) {
	out := new(LoadAlmanacConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_LoadAlmanacConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) SaveAlmanacConfig(ctx context.Context, in *SaveAlmanacConfigRequest, opts ...grpc.CallOption) (*SaveAlmanacConfigResponse, error) {
	out := new(SaveAlmanacConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_SaveAlmanacConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) SetActiveAlmanacConfig(ctx context.Context, in *SetActiveAlmanacConfigRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlmanacConfigService_SetActiveAlmanacConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) DeleteAlmanacConfig(ctx context.Context, in *DeleteAlmanacConfigRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlmanacConfigService_DeleteAlmanacConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) GetNextAlmanacConfig(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextAlmanacConfigResponse, error) {
	out := new(GetNextAlmanacConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_GetNextAlmanacConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) LoadDiscriminatorConfig(ctx context.Context, in *LoadDiscriminatorConfigRequest, opts ...grpc.CallOption) (*LoadDiscriminatorConfigResponse, error) {
	out := new(LoadDiscriminatorConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_LoadDiscriminatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) SaveDiscriminatorConfig(ctx context.Context, in *SaveDiscriminatorConfigRequest, opts ...grpc.CallOption) (*SaveDiscriminatorConfigResponse, error) {
	out := new(SaveDiscriminatorConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_SaveDiscriminatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) SetActiveDiscriminatorConfig(ctx context.Context, in *SetActiveDiscriminatorConfigRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlmanacConfigService_SetActiveDiscriminatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) DeleteDiscriminatorConfig(ctx context.Context, in *DeleteDiscriminatorConfigRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlmanacConfigService_DeleteDiscriminatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) GetNextDiscriminatorConfig(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextDiscriminatorConfigResponse, error) {
	out := new(GetNextDiscriminatorConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_GetNextDiscriminatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) GetNextModelinatorConfig(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextModelinatorConfigResponse, error) {
	out := new(GetNextModelinatorConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_GetNextModelinatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) SaveModelinatorConfig(ctx context.Context, in *SaveModelinatorConfigRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlmanacConfigService_SaveModelinatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) FetchModelinatorConfig(ctx context.Context, in *FetchModelinatorConfigRequest, opts ...grpc.CallOption) (*FetchModelinatorConfigResponse, error) {
	out := new(FetchModelinatorConfigResponse)
	err := c.cc.Invoke(ctx, AlmanacConfigService_FetchModelinatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *almanacConfigServiceClient) ResetModelinatorConfig(ctx context.Context, in *ResetModelinatorConfigRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlmanacConfigService_ResetModelinatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AlmanacConfigServiceServer is the server API for AlmanacConfigService service.
// All implementations must embed UnimplementedAlmanacConfigServiceServer
// for forward compatibility
type AlmanacConfigServiceServer interface {
	// Deprecated: Do not use.
	GetConfigData(context.Context, *Empty) (*GetConfigDataResponse, error)
	GetNextConfigData(context.Context, *GetNextConfigDataRequest) (*GetNextConfigDataResponse, error)
	LoadAlmanacConfig(context.Context, *LoadAlmanacConfigRequest) (*LoadAlmanacConfigResponse, error)
	SaveAlmanacConfig(context.Context, *SaveAlmanacConfigRequest) (*SaveAlmanacConfigResponse, error)
	SetActiveAlmanacConfig(context.Context, *SetActiveAlmanacConfigRequest) (*Empty, error)
	DeleteAlmanacConfig(context.Context, *DeleteAlmanacConfigRequest) (*Empty, error)
	GetNextAlmanacConfig(context.Context, *Timestamp) (*GetNextAlmanacConfigResponse, error)
	LoadDiscriminatorConfig(context.Context, *LoadDiscriminatorConfigRequest) (*LoadDiscriminatorConfigResponse, error)
	SaveDiscriminatorConfig(context.Context, *SaveDiscriminatorConfigRequest) (*SaveDiscriminatorConfigResponse, error)
	SetActiveDiscriminatorConfig(context.Context, *SetActiveDiscriminatorConfigRequest) (*Empty, error)
	DeleteDiscriminatorConfig(context.Context, *DeleteDiscriminatorConfigRequest) (*Empty, error)
	GetNextDiscriminatorConfig(context.Context, *Timestamp) (*GetNextDiscriminatorConfigResponse, error)
	GetNextModelinatorConfig(context.Context, *Timestamp) (*GetNextModelinatorConfigResponse, error)
	SaveModelinatorConfig(context.Context, *SaveModelinatorConfigRequest) (*Empty, error)
	FetchModelinatorConfig(context.Context, *FetchModelinatorConfigRequest) (*FetchModelinatorConfigResponse, error)
	ResetModelinatorConfig(context.Context, *ResetModelinatorConfigRequest) (*Empty, error)
	mustEmbedUnimplementedAlmanacConfigServiceServer()
}

// UnimplementedAlmanacConfigServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAlmanacConfigServiceServer struct {
}

func (UnimplementedAlmanacConfigServiceServer) GetConfigData(context.Context, *Empty) (*GetConfigDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfigData not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) GetNextConfigData(context.Context, *GetNextConfigDataRequest) (*GetNextConfigDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextConfigData not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) LoadAlmanacConfig(context.Context, *LoadAlmanacConfigRequest) (*LoadAlmanacConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadAlmanacConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) SaveAlmanacConfig(context.Context, *SaveAlmanacConfigRequest) (*SaveAlmanacConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAlmanacConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) SetActiveAlmanacConfig(context.Context, *SetActiveAlmanacConfigRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetActiveAlmanacConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) DeleteAlmanacConfig(context.Context, *DeleteAlmanacConfigRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAlmanacConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) GetNextAlmanacConfig(context.Context, *Timestamp) (*GetNextAlmanacConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAlmanacConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) LoadDiscriminatorConfig(context.Context, *LoadDiscriminatorConfigRequest) (*LoadDiscriminatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadDiscriminatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) SaveDiscriminatorConfig(context.Context, *SaveDiscriminatorConfigRequest) (*SaveDiscriminatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveDiscriminatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) SetActiveDiscriminatorConfig(context.Context, *SetActiveDiscriminatorConfigRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetActiveDiscriminatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) DeleteDiscriminatorConfig(context.Context, *DeleteDiscriminatorConfigRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDiscriminatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) GetNextDiscriminatorConfig(context.Context, *Timestamp) (*GetNextDiscriminatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextDiscriminatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) GetNextModelinatorConfig(context.Context, *Timestamp) (*GetNextModelinatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextModelinatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) SaveModelinatorConfig(context.Context, *SaveModelinatorConfigRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveModelinatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) FetchModelinatorConfig(context.Context, *FetchModelinatorConfigRequest) (*FetchModelinatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchModelinatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) ResetModelinatorConfig(context.Context, *ResetModelinatorConfigRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetModelinatorConfig not implemented")
}
func (UnimplementedAlmanacConfigServiceServer) mustEmbedUnimplementedAlmanacConfigServiceServer() {}

// UnsafeAlmanacConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AlmanacConfigServiceServer will
// result in compilation errors.
type UnsafeAlmanacConfigServiceServer interface {
	mustEmbedUnimplementedAlmanacConfigServiceServer()
}

func RegisterAlmanacConfigServiceServer(s grpc.ServiceRegistrar, srv AlmanacConfigServiceServer) {
	s.RegisterService(&AlmanacConfigService_ServiceDesc, srv)
}

func _AlmanacConfigService_GetConfigData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).GetConfigData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_GetConfigData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).GetConfigData(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_GetNextConfigData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextConfigDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).GetNextConfigData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_GetNextConfigData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).GetNextConfigData(ctx, req.(*GetNextConfigDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_LoadAlmanacConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadAlmanacConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).LoadAlmanacConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_LoadAlmanacConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).LoadAlmanacConfig(ctx, req.(*LoadAlmanacConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_SaveAlmanacConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAlmanacConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).SaveAlmanacConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_SaveAlmanacConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).SaveAlmanacConfig(ctx, req.(*SaveAlmanacConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_SetActiveAlmanacConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActiveAlmanacConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).SetActiveAlmanacConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_SetActiveAlmanacConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).SetActiveAlmanacConfig(ctx, req.(*SetActiveAlmanacConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_DeleteAlmanacConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAlmanacConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).DeleteAlmanacConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_DeleteAlmanacConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).DeleteAlmanacConfig(ctx, req.(*DeleteAlmanacConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_GetNextAlmanacConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).GetNextAlmanacConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_GetNextAlmanacConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).GetNextAlmanacConfig(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_LoadDiscriminatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadDiscriminatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).LoadDiscriminatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_LoadDiscriminatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).LoadDiscriminatorConfig(ctx, req.(*LoadDiscriminatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_SaveDiscriminatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveDiscriminatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).SaveDiscriminatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_SaveDiscriminatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).SaveDiscriminatorConfig(ctx, req.(*SaveDiscriminatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_SetActiveDiscriminatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActiveDiscriminatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).SetActiveDiscriminatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_SetActiveDiscriminatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).SetActiveDiscriminatorConfig(ctx, req.(*SetActiveDiscriminatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_DeleteDiscriminatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDiscriminatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).DeleteDiscriminatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_DeleteDiscriminatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).DeleteDiscriminatorConfig(ctx, req.(*DeleteDiscriminatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_GetNextDiscriminatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).GetNextDiscriminatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_GetNextDiscriminatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).GetNextDiscriminatorConfig(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_GetNextModelinatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).GetNextModelinatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_GetNextModelinatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).GetNextModelinatorConfig(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_SaveModelinatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveModelinatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).SaveModelinatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_SaveModelinatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).SaveModelinatorConfig(ctx, req.(*SaveModelinatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_FetchModelinatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchModelinatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).FetchModelinatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_FetchModelinatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).FetchModelinatorConfig(ctx, req.(*FetchModelinatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlmanacConfigService_ResetModelinatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetModelinatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlmanacConfigServiceServer).ResetModelinatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlmanacConfigService_ResetModelinatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlmanacConfigServiceServer).ResetModelinatorConfig(ctx, req.(*ResetModelinatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AlmanacConfigService_ServiceDesc is the grpc.ServiceDesc for AlmanacConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AlmanacConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.almanac.AlmanacConfigService",
	HandlerType: (*AlmanacConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConfigData",
			Handler:    _AlmanacConfigService_GetConfigData_Handler,
		},
		{
			MethodName: "GetNextConfigData",
			Handler:    _AlmanacConfigService_GetNextConfigData_Handler,
		},
		{
			MethodName: "LoadAlmanacConfig",
			Handler:    _AlmanacConfigService_LoadAlmanacConfig_Handler,
		},
		{
			MethodName: "SaveAlmanacConfig",
			Handler:    _AlmanacConfigService_SaveAlmanacConfig_Handler,
		},
		{
			MethodName: "SetActiveAlmanacConfig",
			Handler:    _AlmanacConfigService_SetActiveAlmanacConfig_Handler,
		},
		{
			MethodName: "DeleteAlmanacConfig",
			Handler:    _AlmanacConfigService_DeleteAlmanacConfig_Handler,
		},
		{
			MethodName: "GetNextAlmanacConfig",
			Handler:    _AlmanacConfigService_GetNextAlmanacConfig_Handler,
		},
		{
			MethodName: "LoadDiscriminatorConfig",
			Handler:    _AlmanacConfigService_LoadDiscriminatorConfig_Handler,
		},
		{
			MethodName: "SaveDiscriminatorConfig",
			Handler:    _AlmanacConfigService_SaveDiscriminatorConfig_Handler,
		},
		{
			MethodName: "SetActiveDiscriminatorConfig",
			Handler:    _AlmanacConfigService_SetActiveDiscriminatorConfig_Handler,
		},
		{
			MethodName: "DeleteDiscriminatorConfig",
			Handler:    _AlmanacConfigService_DeleteDiscriminatorConfig_Handler,
		},
		{
			MethodName: "GetNextDiscriminatorConfig",
			Handler:    _AlmanacConfigService_GetNextDiscriminatorConfig_Handler,
		},
		{
			MethodName: "GetNextModelinatorConfig",
			Handler:    _AlmanacConfigService_GetNextModelinatorConfig_Handler,
		},
		{
			MethodName: "SaveModelinatorConfig",
			Handler:    _AlmanacConfigService_SaveModelinatorConfig_Handler,
		},
		{
			MethodName: "FetchModelinatorConfig",
			Handler:    _AlmanacConfigService_FetchModelinatorConfig_Handler,
		},
		{
			MethodName: "ResetModelinatorConfig",
			Handler:    _AlmanacConfigService_ResetModelinatorConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/almanac.proto",
}

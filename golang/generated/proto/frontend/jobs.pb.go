// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/jobs.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	metrics_aggregator "github.com/carbonrobotics/robot/golang/generated/proto/metrics_aggregator"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JobDescription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId       string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TimestampMs int64  `protobuf:"varint,3,opt,name=timestampMs,proto3" json:"timestampMs,omitempty"`
}

func (x *JobDescription) Reset() {
	*x = JobDescription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobDescription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobDescription) ProtoMessage() {}

func (x *JobDescription) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobDescription.ProtoReflect.Descriptor instead.
func (*JobDescription) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{0}
}

func (x *JobDescription) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobDescription) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JobDescription) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type ActiveProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProfileType ProfileType `protobuf:"varint,1,opt,name=profile_type,json=profileType,proto3,enum=carbon.frontend.profile_sync.ProfileType" json:"profile_type,omitempty"`
	Id          string      `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name        string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ActiveProfile) Reset() {
	*x = ActiveProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActiveProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveProfile) ProtoMessage() {}

func (x *ActiveProfile) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveProfile.ProtoReflect.Descriptor instead.
func (*ActiveProfile) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{1}
}

func (x *ActiveProfile) GetProfileType() ProfileType {
	if x != nil {
		return x.ProfileType
	}
	return ProfileType_ALMANAC
}

func (x *ActiveProfile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActiveProfile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobDescription           *JobDescription          `protobuf:"bytes,1,opt,name=jobDescription,proto3" json:"jobDescription,omitempty"`
	BandingProfile           string                   `protobuf:"bytes,2,opt,name=bandingProfile,proto3" json:"bandingProfile,omitempty"`
	ThinningProfile          string                   `protobuf:"bytes,3,opt,name=thinningProfile,proto3" json:"thinningProfile,omitempty"`
	StopTimeMs               int64                    `protobuf:"varint,4,opt,name=stopTimeMs,proto3" json:"stopTimeMs,omitempty"`
	LastUpdateTimeMs         int64                    `protobuf:"varint,5,opt,name=lastUpdateTimeMs,proto3" json:"lastUpdateTimeMs,omitempty"`
	ExpectedAcreage          float32                  `protobuf:"fixed32,6,opt,name=expectedAcreage,proto3" json:"expectedAcreage,omitempty"`
	Completed                bool                     `protobuf:"varint,7,opt,name=completed,proto3" json:"completed,omitempty"`
	Almanac                  string                   `protobuf:"bytes,8,opt,name=almanac,proto3" json:"almanac,omitempty"`
	Discriminator            string                   `protobuf:"bytes,9,opt,name=discriminator,proto3" json:"discriminator,omitempty"`
	CropId                   string                   `protobuf:"bytes,10,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	BandingProfileUUID       string                   `protobuf:"bytes,11,opt,name=bandingProfileUUID,proto3" json:"bandingProfileUUID,omitempty"`
	ThinningProfileUUID      string                   `protobuf:"bytes,12,opt,name=thinningProfileUUID,proto3" json:"thinningProfileUUID,omitempty"`
	AlmanacProfileUUID       string                   `protobuf:"bytes,13,opt,name=almanacProfileUUID,proto3" json:"almanacProfileUUID,omitempty"`
	DiscriminatorProfileUUID string                   `protobuf:"bytes,14,opt,name=discriminatorProfileUUID,proto3" json:"discriminatorProfileUUID,omitempty"`
	ActiveProfiles           map[int32]*ActiveProfile `protobuf:"bytes,15,rep,name=active_profiles,json=activeProfiles,proto3" json:"active_profiles,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Hack since you cant use enum as key type we place enum in object as well
	LastUsedTimeMs           int64                    `protobuf:"varint,16,opt,name=lastUsedTimeMs,proto3" json:"lastUsedTimeMs,omitempty"`
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{2}
}

func (x *Job) GetJobDescription() *JobDescription {
	if x != nil {
		return x.JobDescription
	}
	return nil
}

func (x *Job) GetBandingProfile() string {
	if x != nil {
		return x.BandingProfile
	}
	return ""
}

func (x *Job) GetThinningProfile() string {
	if x != nil {
		return x.ThinningProfile
	}
	return ""
}

func (x *Job) GetStopTimeMs() int64 {
	if x != nil {
		return x.StopTimeMs
	}
	return 0
}

func (x *Job) GetLastUpdateTimeMs() int64 {
	if x != nil {
		return x.LastUpdateTimeMs
	}
	return 0
}

func (x *Job) GetExpectedAcreage() float32 {
	if x != nil {
		return x.ExpectedAcreage
	}
	return 0
}

func (x *Job) GetCompleted() bool {
	if x != nil {
		return x.Completed
	}
	return false
}

func (x *Job) GetAlmanac() string {
	if x != nil {
		return x.Almanac
	}
	return ""
}

func (x *Job) GetDiscriminator() string {
	if x != nil {
		return x.Discriminator
	}
	return ""
}

func (x *Job) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *Job) GetBandingProfileUUID() string {
	if x != nil {
		return x.BandingProfileUUID
	}
	return ""
}

func (x *Job) GetThinningProfileUUID() string {
	if x != nil {
		return x.ThinningProfileUUID
	}
	return ""
}

func (x *Job) GetAlmanacProfileUUID() string {
	if x != nil {
		return x.AlmanacProfileUUID
	}
	return ""
}

func (x *Job) GetDiscriminatorProfileUUID() string {
	if x != nil {
		return x.DiscriminatorProfileUUID
	}
	return ""
}

func (x *Job) GetActiveProfiles() map[int32]*ActiveProfile {
	if x != nil {
		return x.ActiveProfiles
	}
	return nil
}

func (x *Job) GetLastUsedTimeMs() int64 {
	if x != nil {
		return x.LastUsedTimeMs
	}
	return 0
}

type CreateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Active          bool    `protobuf:"varint,2,opt,name=active,proto3" json:"active,omitempty"`
	ExpectedAcreage float32 `protobuf:"fixed32,3,opt,name=expectedAcreage,proto3" json:"expectedAcreage,omitempty"`
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{3}
}

func (x *CreateJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateJobRequest) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *CreateJobRequest) GetExpectedAcreage() float32 {
	if x != nil {
		return x.ExpectedAcreage
	}
	return 0
}

type CreateJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *CreateJobResponse) Reset() {
	*x = CreateJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobResponse) ProtoMessage() {}

func (x *CreateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobResponse.ProtoReflect.Descriptor instead.
func (*CreateJobResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{4}
}

func (x *CreateJobResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type UpdateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobDescription  *JobDescription `protobuf:"bytes,1,opt,name=jobDescription,proto3" json:"jobDescription,omitempty"`
	ExpectedAcreage float32         `protobuf:"fixed32,2,opt,name=expectedAcreage,proto3" json:"expectedAcreage,omitempty"`
}

func (x *UpdateJobRequest) Reset() {
	*x = UpdateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRequest) ProtoMessage() {}

func (x *UpdateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateJobRequest) GetJobDescription() *JobDescription {
	if x != nil {
		return x.JobDescription
	}
	return nil
}

func (x *UpdateJobRequest) GetExpectedAcreage() float32 {
	if x != nil {
		return x.ExpectedAcreage
	}
	return 0
}

type GetNextJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp *Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *GetNextJobsRequest) Reset() {
	*x = GetNextJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextJobsRequest) ProtoMessage() {}

func (x *GetNextJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextJobsRequest.ProtoReflect.Descriptor instead.
func (*GetNextJobsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{6}
}

func (x *GetNextJobsRequest) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type JobWithMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job     *Job                        `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	Metrics *metrics_aggregator.Metrics `protobuf:"bytes,2,opt,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *JobWithMetrics) Reset() {
	*x = JobWithMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobWithMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobWithMetrics) ProtoMessage() {}

func (x *JobWithMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobWithMetrics.ProtoReflect.Descriptor instead.
func (*JobWithMetrics) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{7}
}

func (x *JobWithMetrics) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *JobWithMetrics) GetMetrics() *metrics_aggregator.Metrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type GetNextJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jobs        []*JobWithMetrics `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	ActiveJobId string            `protobuf:"bytes,2,opt,name=activeJobId,proto3" json:"activeJobId,omitempty"`
	Timestamp   *Timestamp        `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *GetNextJobsResponse) Reset() {
	*x = GetNextJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextJobsResponse) ProtoMessage() {}

func (x *GetNextJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextJobsResponse.ProtoReflect.Descriptor instead.
func (*GetNextJobsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{8}
}

func (x *GetNextJobsResponse) GetJobs() []*JobWithMetrics {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *GetNextJobsResponse) GetActiveJobId() string {
	if x != nil {
		return x.ActiveJobId
	}
	return ""
}

func (x *GetNextJobsResponse) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type GetNextActiveJobIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp *Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *GetNextActiveJobIdRequest) Reset() {
	*x = GetNextActiveJobIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveJobIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveJobIdRequest) ProtoMessage() {}

func (x *GetNextActiveJobIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveJobIdRequest.ProtoReflect.Descriptor instead.
func (*GetNextActiveJobIdRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{9}
}

func (x *GetNextActiveJobIdRequest) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type GetNextActiveJobIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActiveJobId string     `protobuf:"bytes,1,opt,name=activeJobId,proto3" json:"activeJobId,omitempty"`
	Timestamp   *Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *GetNextActiveJobIdResponse) Reset() {
	*x = GetNextActiveJobIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveJobIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveJobIdResponse) ProtoMessage() {}

func (x *GetNextActiveJobIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveJobIdResponse.ProtoReflect.Descriptor instead.
func (*GetNextActiveJobIdResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{10}
}

func (x *GetNextActiveJobIdResponse) GetActiveJobId() string {
	if x != nil {
		return x.ActiveJobId
	}
	return ""
}

func (x *GetNextActiveJobIdResponse) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type GetJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{11}
}

func (x *GetJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job *Job `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *GetJobResponse) Reset() {
	*x = GetJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobResponse) ProtoMessage() {}

func (x *GetJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobResponse.ProtoReflect.Descriptor instead.
func (*GetJobResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{12}
}

func (x *GetJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type StartJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *StartJobRequest) Reset() {
	*x = StartJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartJobRequest) ProtoMessage() {}

func (x *StartJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartJobRequest.ProtoReflect.Descriptor instead.
func (*StartJobRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{13}
}

func (x *StartJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetConfigDumpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *GetConfigDumpRequest) Reset() {
	*x = GetConfigDumpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigDumpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigDumpRequest) ProtoMessage() {}

func (x *GetConfigDumpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigDumpRequest.ProtoReflect.Descriptor instead.
func (*GetConfigDumpRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{14}
}

func (x *GetConfigDumpRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetConfigDumpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RootConfig *ConfigNodeSnapshot `protobuf:"bytes,1,opt,name=rootConfig,proto3" json:"rootConfig,omitempty"`
}

func (x *GetConfigDumpResponse) Reset() {
	*x = GetConfigDumpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigDumpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigDumpResponse) ProtoMessage() {}

func (x *GetConfigDumpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigDumpResponse.ProtoReflect.Descriptor instead.
func (*GetConfigDumpResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{15}
}

func (x *GetConfigDumpResponse) GetRootConfig() *ConfigNodeSnapshot {
	if x != nil {
		return x.RootConfig
	}
	return nil
}

type GetActiveJobMetricsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobMetrics *metrics_aggregator.Metrics `protobuf:"bytes,1,opt,name=jobMetrics,proto3" json:"jobMetrics,omitempty"`
}

func (x *GetActiveJobMetricsResponse) Reset() {
	*x = GetActiveJobMetricsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveJobMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveJobMetricsResponse) ProtoMessage() {}

func (x *GetActiveJobMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveJobMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetActiveJobMetricsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{16}
}

func (x *GetActiveJobMetricsResponse) GetJobMetrics() *metrics_aggregator.Metrics {
	if x != nil {
		return x.JobMetrics
	}
	return nil
}

type DeleteJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *DeleteJobRequest) Reset() {
	*x = DeleteJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobRequest) ProtoMessage() {}

func (x *DeleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type MarkJobCompletedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *MarkJobCompletedRequest) Reset() {
	*x = MarkJobCompletedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkJobCompletedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkJobCompletedRequest) ProtoMessage() {}

func (x *MarkJobCompletedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkJobCompletedRequest.ProtoReflect.Descriptor instead.
func (*MarkJobCompletedRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{18}
}

func (x *MarkJobCompletedRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type MarkJobIncompleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *MarkJobIncompleteRequest) Reset() {
	*x = MarkJobIncompleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkJobIncompleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkJobIncompleteRequest) ProtoMessage() {}

func (x *MarkJobIncompleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkJobIncompleteRequest.ProtoReflect.Descriptor instead.
func (*MarkJobIncompleteRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{19}
}

func (x *MarkJobIncompleteRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetNextJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	JobId string     `protobuf:"bytes,2,opt,name=jobId,proto3" json:"jobId,omitempty"`
}

func (x *GetNextJobRequest) Reset() {
	*x = GetNextJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextJobRequest) ProtoMessage() {}

func (x *GetNextJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextJobRequest.ProtoReflect.Descriptor instead.
func (*GetNextJobRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{20}
}

func (x *GetNextJobRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetNextJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts  *Timestamp      `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Job *JobWithMetrics `protobuf:"bytes,2,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *GetNextJobResponse) Reset() {
	*x = GetNextJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_jobs_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextJobResponse) ProtoMessage() {}

func (x *GetNextJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_jobs_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextJobResponse.ProtoReflect.Descriptor instead.
func (*GetNextJobResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_jobs_proto_rawDescGZIP(), []int{21}
}

func (x *GetNextJobResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextJobResponse) GetJob() *JobWithMetrics {
	if x != nil {
		return x.Job
	}
	return nil
}

var File_frontend_proto_jobs_proto protoreflect.FileDescriptor

var file_frontend_proto_jobs_proto_rawDesc = []byte{
	0x0a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62,
	0x73, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5c, 0x0a, 0x0e, 0x4a, 0x6f, 0x62,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6a,
	0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x4c, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc8, 0x06, 0x0a, 0x03,
	0x4a, 0x6f, 0x62, 0x12, 0x4c, 0x0a, 0x0e, 0x6a, 0x6f, 0x62, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f,
	0x62, 0x73, 0x2e, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0e, 0x6a, 0x6f, 0x62, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x68, 0x69,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6c,
	0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12,
	0x28, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x72, 0x65, 0x61,
	0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x41, 0x63, 0x72, 0x65, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x63, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x12, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x55, 0x49, 0x44,
	0x12, 0x30, 0x0a, 0x13, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74,
	0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x55,
	0x49, 0x44, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x55,
	0x49, 0x44, 0x12, 0x3a, 0x0a, 0x18, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x55, 0x49, 0x44, 0x12, 0x56,
	0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x4a,
	0x6f, 0x62, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x73,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x1a, 0x66,
	0x0a, 0x13, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x68, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x41, 0x63, 0x72, 0x65, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x72, 0x65, 0x61, 0x67, 0x65,
	0x22, 0x29, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x10,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x4c, 0x0a, 0x0e, 0x6a, 0x6f, 0x62, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e,
	0x4a, 0x6f, 0x62, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e,
	0x6a, 0x6f, 0x62, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28,
	0x0a, 0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x72, 0x65, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x72, 0x65, 0x61, 0x67, 0x65, 0x22, 0x53, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x74, 0x0a,
	0x0e, 0x4a, 0x6f, 0x62, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12,
	0x2b, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a,
	0x6f, 0x62, 0x73, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x35, 0x0a, 0x07,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x22, 0xb0, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x6a,
	0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73,
	0x2e, 0x4a, 0x6f, 0x62, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52,
	0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a,
	0x6f, 0x62, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x5a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x22, 0x7d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x25, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x03, 0x6a, 0x6f,
	0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x4a,
	0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0x27, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f,
	0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64,
	0x22, 0x2c, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x75, 0x6d,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x70,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x75, 0x6d, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0a, 0x72, 0x6f, 0x6f, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x52, 0x0a, 0x72, 0x6f, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x5a, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3b, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x52, 0x0a, 0x6a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x28, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x2f, 0x0a, 0x17, 0x4d, 0x61, 0x72, 0x6b, 0x4a, 0x6f,
	0x62, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x30, 0x0a, 0x18, 0x4d, 0x61, 0x72, 0x6b, 0x4a,
	0x6f, 0x62, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x36, 0x0a, 0x03,
	0x6a, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73,
	0x2e, 0x4a, 0x6f, 0x62, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52,
	0x03, 0x6a, 0x6f, 0x62, 0x32, 0xd0, 0x09, 0x0a, 0x0b, 0x4a, 0x6f, 0x62, 0x73, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x62, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x12, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4a, 0x6f, 0x62, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4e, 0x0a, 0x08, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x4a, 0x6f, 0x62, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x70,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x77, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f,
	0x62, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a,
	0x6f, 0x62, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x06,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f,
	0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x68, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x75,
	0x6d, 0x70, 0x12, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x44, 0x75, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44,
	0x75, 0x6d, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12,
	0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x10, 0x4d, 0x61, 0x72, 0x6b, 0x4a, 0x6f, 0x62, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e,
	0x4d, 0x61, 0x72, 0x6b, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x11, 0x4d, 0x61, 0x72, 0x6b, 0x4a, 0x6f, 0x62, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73,
	0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5f, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x4a, 0x6f, 0x62, 0x12, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_frontend_proto_jobs_proto_rawDescOnce sync.Once
	file_frontend_proto_jobs_proto_rawDescData = file_frontend_proto_jobs_proto_rawDesc
)

func file_frontend_proto_jobs_proto_rawDescGZIP() []byte {
	file_frontend_proto_jobs_proto_rawDescOnce.Do(func() {
		file_frontend_proto_jobs_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_jobs_proto_rawDescData)
	})
	return file_frontend_proto_jobs_proto_rawDescData
}

var file_frontend_proto_jobs_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_frontend_proto_jobs_proto_goTypes = []interface{}{
	(*JobDescription)(nil),              // 0: carbon.frontend.jobs.JobDescription
	(*ActiveProfile)(nil),               // 1: carbon.frontend.jobs.ActiveProfile
	(*Job)(nil),                         // 2: carbon.frontend.jobs.Job
	(*CreateJobRequest)(nil),            // 3: carbon.frontend.jobs.CreateJobRequest
	(*CreateJobResponse)(nil),           // 4: carbon.frontend.jobs.CreateJobResponse
	(*UpdateJobRequest)(nil),            // 5: carbon.frontend.jobs.UpdateJobRequest
	(*GetNextJobsRequest)(nil),          // 6: carbon.frontend.jobs.GetNextJobsRequest
	(*JobWithMetrics)(nil),              // 7: carbon.frontend.jobs.JobWithMetrics
	(*GetNextJobsResponse)(nil),         // 8: carbon.frontend.jobs.GetNextJobsResponse
	(*GetNextActiveJobIdRequest)(nil),   // 9: carbon.frontend.jobs.GetNextActiveJobIdRequest
	(*GetNextActiveJobIdResponse)(nil),  // 10: carbon.frontend.jobs.GetNextActiveJobIdResponse
	(*GetJobRequest)(nil),               // 11: carbon.frontend.jobs.GetJobRequest
	(*GetJobResponse)(nil),              // 12: carbon.frontend.jobs.GetJobResponse
	(*StartJobRequest)(nil),             // 13: carbon.frontend.jobs.StartJobRequest
	(*GetConfigDumpRequest)(nil),        // 14: carbon.frontend.jobs.GetConfigDumpRequest
	(*GetConfigDumpResponse)(nil),       // 15: carbon.frontend.jobs.GetConfigDumpResponse
	(*GetActiveJobMetricsResponse)(nil), // 16: carbon.frontend.jobs.GetActiveJobMetricsResponse
	(*DeleteJobRequest)(nil),            // 17: carbon.frontend.jobs.DeleteJobRequest
	(*MarkJobCompletedRequest)(nil),     // 18: carbon.frontend.jobs.MarkJobCompletedRequest
	(*MarkJobIncompleteRequest)(nil),    // 19: carbon.frontend.jobs.MarkJobIncompleteRequest
	(*GetNextJobRequest)(nil),           // 20: carbon.frontend.jobs.GetNextJobRequest
	(*GetNextJobResponse)(nil),          // 21: carbon.frontend.jobs.GetNextJobResponse
	nil,                                 // 22: carbon.frontend.jobs.Job.ActiveProfilesEntry
	(ProfileType)(0),                    // 23: carbon.frontend.profile_sync.ProfileType
	(*Timestamp)(nil),                   // 24: carbon.frontend.util.Timestamp
	(*metrics_aggregator.Metrics)(nil),  // 25: metrics_aggregator.Metrics
	(*ConfigNodeSnapshot)(nil),          // 26: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	(*Empty)(nil),                       // 27: carbon.frontend.util.Empty
}
var file_frontend_proto_jobs_proto_depIdxs = []int32{
	23, // 0: carbon.frontend.jobs.ActiveProfile.profile_type:type_name -> carbon.frontend.profile_sync.ProfileType
	0,  // 1: carbon.frontend.jobs.Job.jobDescription:type_name -> carbon.frontend.jobs.JobDescription
	22, // 2: carbon.frontend.jobs.Job.active_profiles:type_name -> carbon.frontend.jobs.Job.ActiveProfilesEntry
	0,  // 3: carbon.frontend.jobs.UpdateJobRequest.jobDescription:type_name -> carbon.frontend.jobs.JobDescription
	24, // 4: carbon.frontend.jobs.GetNextJobsRequest.timestamp:type_name -> carbon.frontend.util.Timestamp
	2,  // 5: carbon.frontend.jobs.JobWithMetrics.job:type_name -> carbon.frontend.jobs.Job
	25, // 6: carbon.frontend.jobs.JobWithMetrics.metrics:type_name -> metrics_aggregator.Metrics
	7,  // 7: carbon.frontend.jobs.GetNextJobsResponse.jobs:type_name -> carbon.frontend.jobs.JobWithMetrics
	24, // 8: carbon.frontend.jobs.GetNextJobsResponse.timestamp:type_name -> carbon.frontend.util.Timestamp
	24, // 9: carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp:type_name -> carbon.frontend.util.Timestamp
	24, // 10: carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp:type_name -> carbon.frontend.util.Timestamp
	2,  // 11: carbon.frontend.jobs.GetJobResponse.job:type_name -> carbon.frontend.jobs.Job
	26, // 12: carbon.frontend.jobs.GetConfigDumpResponse.rootConfig:type_name -> carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	25, // 13: carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics:type_name -> metrics_aggregator.Metrics
	24, // 14: carbon.frontend.jobs.GetNextJobRequest.ts:type_name -> carbon.frontend.util.Timestamp
	24, // 15: carbon.frontend.jobs.GetNextJobResponse.ts:type_name -> carbon.frontend.util.Timestamp
	7,  // 16: carbon.frontend.jobs.GetNextJobResponse.job:type_name -> carbon.frontend.jobs.JobWithMetrics
	1,  // 17: carbon.frontend.jobs.Job.ActiveProfilesEntry.value:type_name -> carbon.frontend.jobs.ActiveProfile
	6,  // 18: carbon.frontend.jobs.JobsService.GetNextJobs:input_type -> carbon.frontend.jobs.GetNextJobsRequest
	3,  // 19: carbon.frontend.jobs.JobsService.CreateJob:input_type -> carbon.frontend.jobs.CreateJobRequest
	5,  // 20: carbon.frontend.jobs.JobsService.UpdateJob:input_type -> carbon.frontend.jobs.UpdateJobRequest
	13, // 21: carbon.frontend.jobs.JobsService.StartJob:input_type -> carbon.frontend.jobs.StartJobRequest
	27, // 22: carbon.frontend.jobs.JobsService.StopActiveJob:input_type -> carbon.frontend.util.Empty
	9,  // 23: carbon.frontend.jobs.JobsService.GetNextActiveJobId:input_type -> carbon.frontend.jobs.GetNextActiveJobIdRequest
	11, // 24: carbon.frontend.jobs.JobsService.GetJob:input_type -> carbon.frontend.jobs.GetJobRequest
	14, // 25: carbon.frontend.jobs.JobsService.GetConfigDump:input_type -> carbon.frontend.jobs.GetConfigDumpRequest
	27, // 26: carbon.frontend.jobs.JobsService.GetActiveJobMetrics:input_type -> carbon.frontend.util.Empty
	17, // 27: carbon.frontend.jobs.JobsService.DeleteJob:input_type -> carbon.frontend.jobs.DeleteJobRequest
	18, // 28: carbon.frontend.jobs.JobsService.MarkJobCompleted:input_type -> carbon.frontend.jobs.MarkJobCompletedRequest
	19, // 29: carbon.frontend.jobs.JobsService.MarkJobIncomplete:input_type -> carbon.frontend.jobs.MarkJobIncompleteRequest
	20, // 30: carbon.frontend.jobs.JobsService.GetNextJob:input_type -> carbon.frontend.jobs.GetNextJobRequest
	8,  // 31: carbon.frontend.jobs.JobsService.GetNextJobs:output_type -> carbon.frontend.jobs.GetNextJobsResponse
	4,  // 32: carbon.frontend.jobs.JobsService.CreateJob:output_type -> carbon.frontend.jobs.CreateJobResponse
	27, // 33: carbon.frontend.jobs.JobsService.UpdateJob:output_type -> carbon.frontend.util.Empty
	27, // 34: carbon.frontend.jobs.JobsService.StartJob:output_type -> carbon.frontend.util.Empty
	27, // 35: carbon.frontend.jobs.JobsService.StopActiveJob:output_type -> carbon.frontend.util.Empty
	10, // 36: carbon.frontend.jobs.JobsService.GetNextActiveJobId:output_type -> carbon.frontend.jobs.GetNextActiveJobIdResponse
	12, // 37: carbon.frontend.jobs.JobsService.GetJob:output_type -> carbon.frontend.jobs.GetJobResponse
	15, // 38: carbon.frontend.jobs.JobsService.GetConfigDump:output_type -> carbon.frontend.jobs.GetConfigDumpResponse
	16, // 39: carbon.frontend.jobs.JobsService.GetActiveJobMetrics:output_type -> carbon.frontend.jobs.GetActiveJobMetricsResponse
	27, // 40: carbon.frontend.jobs.JobsService.DeleteJob:output_type -> carbon.frontend.util.Empty
	27, // 41: carbon.frontend.jobs.JobsService.MarkJobCompleted:output_type -> carbon.frontend.util.Empty
	27, // 42: carbon.frontend.jobs.JobsService.MarkJobIncomplete:output_type -> carbon.frontend.util.Empty
	21, // 43: carbon.frontend.jobs.JobsService.GetNextJob:output_type -> carbon.frontend.jobs.GetNextJobResponse
	31, // [31:44] is the sub-list for method output_type
	18, // [18:31] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_frontend_proto_jobs_proto_init() }
func file_frontend_proto_jobs_proto_init() {
	if File_frontend_proto_jobs_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	file_frontend_proto_weeding_diagnostics_proto_init()
	file_frontend_proto_profile_sync_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_jobs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobDescription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActiveProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobWithMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveJobIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveJobIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigDumpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigDumpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveJobMetricsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkJobCompletedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkJobIncompleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_jobs_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_jobs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_jobs_proto_goTypes,
		DependencyIndexes: file_frontend_proto_jobs_proto_depIdxs,
		MessageInfos:      file_frontend_proto_jobs_proto_msgTypes,
	}.Build()
	File_frontend_proto_jobs_proto = out.File
	file_frontend_proto_jobs_proto_rawDesc = nil
	file_frontend_proto_jobs_proto_goTypes = nil
	file_frontend_proto_jobs_proto_depIdxs = nil
}

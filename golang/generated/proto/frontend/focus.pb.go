// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/focus.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TargetFocusState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LiquidLensValue  uint32  `protobuf:"varint,1,opt,name=liquid_lens_value,json=liquidLensValue,proto3" json:"liquid_lens_value,omitempty"`
	FocusProgressPct float64 `protobuf:"fixed64,2,opt,name=focus_progress_pct,json=focusProgressPct,proto3" json:"focus_progress_pct,omitempty"`
	MaxLensValue     uint32  `protobuf:"varint,3,opt,name=max_lens_value,json=maxLensValue,proto3" json:"max_lens_value,omitempty"`
	MinLensValue     uint32  `protobuf:"varint,4,opt,name=min_lens_value,json=minLensValue,proto3" json:"min_lens_value,omitempty"`
	FocusInProgress  bool    `protobuf:"varint,5,opt,name=focus_in_progress,json=focusInProgress,proto3" json:"focus_in_progress,omitempty"`
}

func (x *TargetFocusState) Reset() {
	*x = TargetFocusState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_focus_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetFocusState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetFocusState) ProtoMessage() {}

func (x *TargetFocusState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_focus_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetFocusState.ProtoReflect.Descriptor instead.
func (*TargetFocusState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_focus_proto_rawDescGZIP(), []int{0}
}

func (x *TargetFocusState) GetLiquidLensValue() uint32 {
	if x != nil {
		return x.LiquidLensValue
	}
	return 0
}

func (x *TargetFocusState) GetFocusProgressPct() float64 {
	if x != nil {
		return x.FocusProgressPct
	}
	return 0
}

func (x *TargetFocusState) GetMaxLensValue() uint32 {
	if x != nil {
		return x.MaxLensValue
	}
	return 0
}

func (x *TargetFocusState) GetMinLensValue() uint32 {
	if x != nil {
		return x.MinLensValue
	}
	return 0
}

func (x *TargetFocusState) GetFocusInProgress() bool {
	if x != nil {
		return x.FocusInProgress
	}
	return false
}

type PredictFocusState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PredictFocusState) Reset() {
	*x = PredictFocusState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_focus_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictFocusState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictFocusState) ProtoMessage() {}

func (x *PredictFocusState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_focus_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictFocusState.ProtoReflect.Descriptor instead.
func (*PredictFocusState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_focus_proto_rawDescGZIP(), []int{1}
}

type FocusState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	// Types that are assignable to TypeState:
	//
	//	*FocusState_Target
	//	*FocusState_Predict
	TypeState              isFocusState_TypeState `protobuf_oneof:"type_state"`
	GlobalFocusProgressPct float64                `protobuf:"fixed64,4,opt,name=global_focus_progress_pct,json=globalFocusProgressPct,proto3" json:"global_focus_progress_pct,omitempty"`
	GridViewEnabled        bool                   `protobuf:"varint,5,opt,name=grid_view_enabled,json=gridViewEnabled,proto3" json:"grid_view_enabled,omitempty"`
	FocusInProgress        bool                   `protobuf:"varint,6,opt,name=focus_in_progress,json=focusInProgress,proto3" json:"focus_in_progress,omitempty"`
}

func (x *FocusState) Reset() {
	*x = FocusState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_focus_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FocusState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FocusState) ProtoMessage() {}

func (x *FocusState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_focus_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FocusState.ProtoReflect.Descriptor instead.
func (*FocusState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_focus_proto_rawDescGZIP(), []int{2}
}

func (x *FocusState) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (m *FocusState) GetTypeState() isFocusState_TypeState {
	if m != nil {
		return m.TypeState
	}
	return nil
}

func (x *FocusState) GetTarget() *TargetFocusState {
	if x, ok := x.GetTypeState().(*FocusState_Target); ok {
		return x.Target
	}
	return nil
}

func (x *FocusState) GetPredict() *PredictFocusState {
	if x, ok := x.GetTypeState().(*FocusState_Predict); ok {
		return x.Predict
	}
	return nil
}

func (x *FocusState) GetGlobalFocusProgressPct() float64 {
	if x != nil {
		return x.GlobalFocusProgressPct
	}
	return 0
}

func (x *FocusState) GetGridViewEnabled() bool {
	if x != nil {
		return x.GridViewEnabled
	}
	return false
}

func (x *FocusState) GetFocusInProgress() bool {
	if x != nil {
		return x.FocusInProgress
	}
	return false
}

type isFocusState_TypeState interface {
	isFocusState_TypeState()
}

type FocusState_Target struct {
	Target *TargetFocusState `protobuf:"bytes,2,opt,name=target,proto3,oneof"`
}

type FocusState_Predict struct {
	Predict *PredictFocusState `protobuf:"bytes,3,opt,name=predict,proto3,oneof"`
}

func (*FocusState_Target) isFocusState_TypeState() {}

func (*FocusState_Predict) isFocusState_TypeState() {}

type LensSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId     string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	LensValue uint32 `protobuf:"varint,2,opt,name=lens_value,json=lensValue,proto3" json:"lens_value,omitempty"`
}

func (x *LensSetRequest) Reset() {
	*x = LensSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_focus_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensSetRequest) ProtoMessage() {}

func (x *LensSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_focus_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensSetRequest.ProtoReflect.Descriptor instead.
func (*LensSetRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_focus_proto_rawDescGZIP(), []int{3}
}

func (x *LensSetRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *LensSetRequest) GetLensValue() uint32 {
	if x != nil {
		return x.LensValue
	}
	return 0
}

type FocusStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string     `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Ts    *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *FocusStateRequest) Reset() {
	*x = FocusStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_focus_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FocusStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FocusStateRequest) ProtoMessage() {}

func (x *FocusStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_focus_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FocusStateRequest.ProtoReflect.Descriptor instead.
func (*FocusStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_focus_proto_rawDescGZIP(), []int{4}
}

func (x *FocusStateRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *FocusStateRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

var File_frontend_proto_focus_proto protoreflect.FileDescriptor

var file_frontend_proto_focus_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x6f,
	0x63, 0x75, 0x73, 0x1a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x01, 0x0a, 0x10,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x69, 0x71, 0x75, 0x69, 0x64, 0x5f, 0x6c, 0x65, 0x6e, 0x73, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x6c, 0x69, 0x71,
	0x75, 0x69, 0x64, 0x4c, 0x65, 0x6e, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2c, 0x0a, 0x12,
	0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x50, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61,
	0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x73, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e,
	0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f,
	0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x13, 0x0a, 0x11, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x46, 0x6f, 0x63,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0xe7, 0x02, 0x0a, 0x0a, 0x46, 0x6f, 0x63, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x48, 0x00, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x6f,
	0x63, 0x75, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x12, 0x39, 0x0a, 0x19, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x63, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x16, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x46, 0x6f, 0x63, 0x75, 0x73,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x50, 0x63, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x67,
	0x72, 0x69, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x67, 0x72, 0x69, 0x64, 0x56, 0x69, 0x65, 0x77,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x22, 0x46, 0x0a, 0x0e, 0x4c, 0x65, 0x6e, 0x73, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x65,
	0x6e, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x6c, 0x65, 0x6e, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5b, 0x0a, 0x11, 0x46, 0x6f, 0x63,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x32, 0x8f, 0x04, 0x0a, 0x0c, 0x46, 0x6f, 0x63, 0x75, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x51, 0x0a, 0x15, 0x54, 0x6f, 0x67, 0x67, 0x6c,
	0x65, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x47, 0x72, 0x69, 0x64, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x2e, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x6f, 0x63, 0x75,
	0x73, 0x2e, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x5c, 0x0a, 0x16,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4d, 0x0a, 0x11, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x41, 0x6c, 0x6c, 0x12,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x0d, 0x53, 0x74, 0x6f,
	0x70, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x52, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x4c, 0x65, 0x6e, 0x73, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x2e, 0x4c, 0x65, 0x6e,
	0x73, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_frontend_proto_focus_proto_rawDescOnce sync.Once
	file_frontend_proto_focus_proto_rawDescData = file_frontend_proto_focus_proto_rawDesc
)

func file_frontend_proto_focus_proto_rawDescGZIP() []byte {
	file_frontend_proto_focus_proto_rawDescOnce.Do(func() {
		file_frontend_proto_focus_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_focus_proto_rawDescData)
	})
	return file_frontend_proto_focus_proto_rawDescData
}

var file_frontend_proto_focus_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_frontend_proto_focus_proto_goTypes = []interface{}{
	(*TargetFocusState)(nil),  // 0: carbon.frontend.focus.TargetFocusState
	(*PredictFocusState)(nil), // 1: carbon.frontend.focus.PredictFocusState
	(*FocusState)(nil),        // 2: carbon.frontend.focus.FocusState
	(*LensSetRequest)(nil),    // 3: carbon.frontend.focus.LensSetRequest
	(*FocusStateRequest)(nil), // 4: carbon.frontend.focus.FocusStateRequest
	(*Timestamp)(nil),         // 5: carbon.frontend.util.Timestamp
	(*Empty)(nil),             // 6: carbon.frontend.util.Empty
	(*CameraRequest)(nil),     // 7: carbon.frontend.camera.CameraRequest
}
var file_frontend_proto_focus_proto_depIdxs = []int32{
	5,  // 0: carbon.frontend.focus.FocusState.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 1: carbon.frontend.focus.FocusState.target:type_name -> carbon.frontend.focus.TargetFocusState
	1,  // 2: carbon.frontend.focus.FocusState.predict:type_name -> carbon.frontend.focus.PredictFocusState
	5,  // 3: carbon.frontend.focus.FocusStateRequest.ts:type_name -> carbon.frontend.util.Timestamp
	6,  // 4: carbon.frontend.focus.FocusService.TogglePredictGridView:input_type -> carbon.frontend.util.Empty
	4,  // 5: carbon.frontend.focus.FocusService.GetNextFocusState:input_type -> carbon.frontend.focus.FocusStateRequest
	7,  // 6: carbon.frontend.focus.FocusService.StartAutoFocusSpecific:input_type -> carbon.frontend.camera.CameraRequest
	6,  // 7: carbon.frontend.focus.FocusService.StartAutoFocusAll:input_type -> carbon.frontend.util.Empty
	6,  // 8: carbon.frontend.focus.FocusService.StopAutoFocus:input_type -> carbon.frontend.util.Empty
	3,  // 9: carbon.frontend.focus.FocusService.SetLensValue:input_type -> carbon.frontend.focus.LensSetRequest
	6,  // 10: carbon.frontend.focus.FocusService.TogglePredictGridView:output_type -> carbon.frontend.util.Empty
	2,  // 11: carbon.frontend.focus.FocusService.GetNextFocusState:output_type -> carbon.frontend.focus.FocusState
	6,  // 12: carbon.frontend.focus.FocusService.StartAutoFocusSpecific:output_type -> carbon.frontend.util.Empty
	6,  // 13: carbon.frontend.focus.FocusService.StartAutoFocusAll:output_type -> carbon.frontend.util.Empty
	6,  // 14: carbon.frontend.focus.FocusService.StopAutoFocus:output_type -> carbon.frontend.util.Empty
	6,  // 15: carbon.frontend.focus.FocusService.SetLensValue:output_type -> carbon.frontend.util.Empty
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_frontend_proto_focus_proto_init() }
func file_frontend_proto_focus_proto_init() {
	if File_frontend_proto_focus_proto != nil {
		return
	}
	file_frontend_proto_camera_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_focus_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetFocusState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_focus_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictFocusState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_focus_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FocusState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_focus_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_focus_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FocusStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_focus_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*FocusState_Target)(nil),
		(*FocusState_Predict)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_focus_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_focus_proto_goTypes,
		DependencyIndexes: file_frontend_proto_focus_proto_depIdxs,
		MessageInfos:      file_frontend_proto_focus_proto_msgTypes,
	}.Build()
	File_frontend_proto_focus_proto = out.File
	file_frontend_proto_focus_proto_rawDesc = nil
	file_frontend_proto_focus_proto_goTypes = nil
	file_frontend_proto_focus_proto_depIdxs = nil
}

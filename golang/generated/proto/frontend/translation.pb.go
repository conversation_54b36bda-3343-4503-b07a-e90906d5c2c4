// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/translation.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IntegerValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value int64 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *IntegerValue) Reset() {
	*x = IntegerValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntegerValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegerValue) ProtoMessage() {}

func (x *IntegerValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegerValue.ProtoReflect.Descriptor instead.
func (*IntegerValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{0}
}

func (x *IntegerValue) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type DoubleValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *DoubleValue) Reset() {
	*x = DoubleValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleValue) ProtoMessage() {}

func (x *DoubleValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleValue.ProtoReflect.Descriptor instead.
func (*DoubleValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{1}
}

func (x *DoubleValue) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type StringValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringValue) Reset() {
	*x = StringValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringValue) ProtoMessage() {}

func (x *StringValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringValue.ProtoReflect.Descriptor instead.
func (*StringValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{2}
}

func (x *StringValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TemperatureValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*TemperatureValue_Celcius
	//	*TemperatureValue_Fahrenheit
	Value isTemperatureValue_Value `protobuf_oneof:"value"`
}

func (x *TemperatureValue) Reset() {
	*x = TemperatureValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemperatureValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemperatureValue) ProtoMessage() {}

func (x *TemperatureValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemperatureValue.ProtoReflect.Descriptor instead.
func (*TemperatureValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{3}
}

func (m *TemperatureValue) GetValue() isTemperatureValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *TemperatureValue) GetCelcius() float64 {
	if x, ok := x.GetValue().(*TemperatureValue_Celcius); ok {
		return x.Celcius
	}
	return 0
}

func (x *TemperatureValue) GetFahrenheit() float64 {
	if x, ok := x.GetValue().(*TemperatureValue_Fahrenheit); ok {
		return x.Fahrenheit
	}
	return 0
}

type isTemperatureValue_Value interface {
	isTemperatureValue_Value()
}

type TemperatureValue_Celcius struct {
	Celcius float64 `protobuf:"fixed64,1,opt,name=celcius,proto3,oneof"`
}

type TemperatureValue_Fahrenheit struct {
	Fahrenheit float64 `protobuf:"fixed64,2,opt,name=fahrenheit,proto3,oneof"`
}

func (*TemperatureValue_Celcius) isTemperatureValue_Value() {}

func (*TemperatureValue_Fahrenheit) isTemperatureValue_Value() {}

type PercentValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Percent uint32 `protobuf:"varint,1,opt,name=percent,proto3" json:"percent,omitempty"`
}

func (x *PercentValue) Reset() {
	*x = PercentValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PercentValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PercentValue) ProtoMessage() {}

func (x *PercentValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PercentValue.ProtoReflect.Descriptor instead.
func (*PercentValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{4}
}

func (x *PercentValue) GetPercent() uint32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

type VoltageValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volts float64 `protobuf:"fixed64,1,opt,name=volts,proto3" json:"volts,omitempty"`
}

func (x *VoltageValue) Reset() {
	*x = VoltageValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoltageValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoltageValue) ProtoMessage() {}

func (x *VoltageValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoltageValue.ProtoReflect.Descriptor instead.
func (*VoltageValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{5}
}

func (x *VoltageValue) GetVolts() float64 {
	if x != nil {
		return x.Volts
	}
	return 0
}

type FrequencyValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hertz float64 `protobuf:"fixed64,1,opt,name=hertz,proto3" json:"hertz,omitempty"`
}

func (x *FrequencyValue) Reset() {
	*x = FrequencyValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FrequencyValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrequencyValue) ProtoMessage() {}

func (x *FrequencyValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrequencyValue.ProtoReflect.Descriptor instead.
func (*FrequencyValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{6}
}

func (x *FrequencyValue) GetHertz() float64 {
	if x != nil {
		return x.Hertz
	}
	return 0
}

type AreaValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*AreaValue_Acres
	//	*AreaValue_Hectares
	//	*AreaValue_SquareFeet
	//	*AreaValue_SquareMeters
	Value isAreaValue_Value `protobuf_oneof:"value"`
}

func (x *AreaValue) Reset() {
	*x = AreaValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaValue) ProtoMessage() {}

func (x *AreaValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaValue.ProtoReflect.Descriptor instead.
func (*AreaValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{7}
}

func (m *AreaValue) GetValue() isAreaValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *AreaValue) GetAcres() float64 {
	if x, ok := x.GetValue().(*AreaValue_Acres); ok {
		return x.Acres
	}
	return 0
}

func (x *AreaValue) GetHectares() float64 {
	if x, ok := x.GetValue().(*AreaValue_Hectares); ok {
		return x.Hectares
	}
	return 0
}

func (x *AreaValue) GetSquareFeet() float64 {
	if x, ok := x.GetValue().(*AreaValue_SquareFeet); ok {
		return x.SquareFeet
	}
	return 0
}

func (x *AreaValue) GetSquareMeters() float64 {
	if x, ok := x.GetValue().(*AreaValue_SquareMeters); ok {
		return x.SquareMeters
	}
	return 0
}

type isAreaValue_Value interface {
	isAreaValue_Value()
}

type AreaValue_Acres struct {
	Acres float64 `protobuf:"fixed64,1,opt,name=acres,proto3,oneof"`
}

type AreaValue_Hectares struct {
	Hectares float64 `protobuf:"fixed64,2,opt,name=hectares,proto3,oneof"`
}

type AreaValue_SquareFeet struct {
	SquareFeet float64 `protobuf:"fixed64,3,opt,name=square_feet,json=squareFeet,proto3,oneof"`
}

type AreaValue_SquareMeters struct {
	SquareMeters float64 `protobuf:"fixed64,4,opt,name=square_meters,json=squareMeters,proto3,oneof"`
}

func (*AreaValue_Acres) isAreaValue_Value() {}

func (*AreaValue_Hectares) isAreaValue_Value() {}

func (*AreaValue_SquareFeet) isAreaValue_Value() {}

func (*AreaValue_SquareMeters) isAreaValue_Value() {}

type DurationValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*DurationValue_Milliseconds
	//	*DurationValue_Seconds
	//	*DurationValue_Minutes
	//	*DurationValue_Hours
	Value isDurationValue_Value `protobuf_oneof:"value"`
}

func (x *DurationValue) Reset() {
	*x = DurationValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DurationValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DurationValue) ProtoMessage() {}

func (x *DurationValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DurationValue.ProtoReflect.Descriptor instead.
func (*DurationValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{8}
}

func (m *DurationValue) GetValue() isDurationValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *DurationValue) GetMilliseconds() uint64 {
	if x, ok := x.GetValue().(*DurationValue_Milliseconds); ok {
		return x.Milliseconds
	}
	return 0
}

func (x *DurationValue) GetSeconds() uint64 {
	if x, ok := x.GetValue().(*DurationValue_Seconds); ok {
		return x.Seconds
	}
	return 0
}

func (x *DurationValue) GetMinutes() uint64 {
	if x, ok := x.GetValue().(*DurationValue_Minutes); ok {
		return x.Minutes
	}
	return 0
}

func (x *DurationValue) GetHours() uint64 {
	if x, ok := x.GetValue().(*DurationValue_Hours); ok {
		return x.Hours
	}
	return 0
}

type isDurationValue_Value interface {
	isDurationValue_Value()
}

type DurationValue_Milliseconds struct {
	Milliseconds uint64 `protobuf:"varint,1,opt,name=milliseconds,proto3,oneof"`
}

type DurationValue_Seconds struct {
	Seconds uint64 `protobuf:"varint,2,opt,name=seconds,proto3,oneof"`
}

type DurationValue_Minutes struct {
	Minutes uint64 `protobuf:"varint,3,opt,name=minutes,proto3,oneof"`
}

type DurationValue_Hours struct {
	Hours uint64 `protobuf:"varint,4,opt,name=hours,proto3,oneof"`
}

func (*DurationValue_Milliseconds) isDurationValue_Value() {}

func (*DurationValue_Seconds) isDurationValue_Value() {}

func (*DurationValue_Minutes) isDurationValue_Value() {}

func (*DurationValue_Hours) isDurationValue_Value() {}

type DistanceValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*DistanceValue_Millimeters
	//	*DistanceValue_Meters
	//	*DistanceValue_Kilometers
	//	*DistanceValue_Inches
	//	*DistanceValue_Feet
	//	*DistanceValue_Miles
	//	*DistanceValue_Centimeters
	Value isDistanceValue_Value `protobuf_oneof:"value"`
}

func (x *DistanceValue) Reset() {
	*x = DistanceValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DistanceValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistanceValue) ProtoMessage() {}

func (x *DistanceValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistanceValue.ProtoReflect.Descriptor instead.
func (*DistanceValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{9}
}

func (m *DistanceValue) GetValue() isDistanceValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *DistanceValue) GetMillimeters() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Millimeters); ok {
		return x.Millimeters
	}
	return 0
}

func (x *DistanceValue) GetMeters() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Meters); ok {
		return x.Meters
	}
	return 0
}

func (x *DistanceValue) GetKilometers() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Kilometers); ok {
		return x.Kilometers
	}
	return 0
}

func (x *DistanceValue) GetInches() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Inches); ok {
		return x.Inches
	}
	return 0
}

func (x *DistanceValue) GetFeet() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Feet); ok {
		return x.Feet
	}
	return 0
}

func (x *DistanceValue) GetMiles() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Miles); ok {
		return x.Miles
	}
	return 0
}

func (x *DistanceValue) GetCentimeters() float64 {
	if x, ok := x.GetValue().(*DistanceValue_Centimeters); ok {
		return x.Centimeters
	}
	return 0
}

type isDistanceValue_Value interface {
	isDistanceValue_Value()
}

type DistanceValue_Millimeters struct {
	Millimeters float64 `protobuf:"fixed64,1,opt,name=millimeters,proto3,oneof"`
}

type DistanceValue_Meters struct {
	Meters float64 `protobuf:"fixed64,2,opt,name=meters,proto3,oneof"`
}

type DistanceValue_Kilometers struct {
	Kilometers float64 `protobuf:"fixed64,3,opt,name=kilometers,proto3,oneof"`
}

type DistanceValue_Inches struct {
	Inches float64 `protobuf:"fixed64,4,opt,name=inches,proto3,oneof"`
}

type DistanceValue_Feet struct {
	Feet float64 `protobuf:"fixed64,5,opt,name=feet,proto3,oneof"`
}

type DistanceValue_Miles struct {
	Miles float64 `protobuf:"fixed64,6,opt,name=miles,proto3,oneof"`
}

type DistanceValue_Centimeters struct {
	Centimeters float64 `protobuf:"fixed64,7,opt,name=centimeters,proto3,oneof"`
}

func (*DistanceValue_Millimeters) isDistanceValue_Value() {}

func (*DistanceValue_Meters) isDistanceValue_Value() {}

func (*DistanceValue_Kilometers) isDistanceValue_Value() {}

func (*DistanceValue_Inches) isDistanceValue_Value() {}

func (*DistanceValue_Feet) isDistanceValue_Value() {}

func (*DistanceValue_Miles) isDistanceValue_Value() {}

func (*DistanceValue_Centimeters) isDistanceValue_Value() {}

type SpeedValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*SpeedValue_KilometersPerHour
	//	*SpeedValue_MilesPerHour
	Value isSpeedValue_Value `protobuf_oneof:"value"`
}

func (x *SpeedValue) Reset() {
	*x = SpeedValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeedValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedValue) ProtoMessage() {}

func (x *SpeedValue) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedValue.ProtoReflect.Descriptor instead.
func (*SpeedValue) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{10}
}

func (m *SpeedValue) GetValue() isSpeedValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *SpeedValue) GetKilometersPerHour() float64 {
	if x, ok := x.GetValue().(*SpeedValue_KilometersPerHour); ok {
		return x.KilometersPerHour
	}
	return 0
}

func (x *SpeedValue) GetMilesPerHour() float64 {
	if x, ok := x.GetValue().(*SpeedValue_MilesPerHour); ok {
		return x.MilesPerHour
	}
	return 0
}

type isSpeedValue_Value interface {
	isSpeedValue_Value()
}

type SpeedValue_KilometersPerHour struct {
	KilometersPerHour float64 `protobuf:"fixed64,1,opt,name=kilometers_per_hour,json=kilometersPerHour,proto3,oneof"`
}

type SpeedValue_MilesPerHour struct {
	MilesPerHour float64 `protobuf:"fixed64,2,opt,name=miles_per_hour,json=milesPerHour,proto3,oneof"`
}

func (*SpeedValue_KilometersPerHour) isSpeedValue_Value() {}

func (*SpeedValue_MilesPerHour) isSpeedValue_Value() {}

type TranslationParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are assignable to Value:
	//
	//	*TranslationParameter_IntValue
	//	*TranslationParameter_DoubleValue
	//	*TranslationParameter_StringValue
	//	*TranslationParameter_TemperatureValue
	//	*TranslationParameter_PercentValue
	//	*TranslationParameter_VoltageValue
	//	*TranslationParameter_FrequencyValue
	//	*TranslationParameter_AreaValue
	//	*TranslationParameter_DurationValue
	//	*TranslationParameter_DistanceValue
	//	*TranslationParameter_SpeedValue
	Value isTranslationParameter_Value `protobuf_oneof:"value"`
}

func (x *TranslationParameter) Reset() {
	*x = TranslationParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_translation_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslationParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslationParameter) ProtoMessage() {}

func (x *TranslationParameter) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_translation_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslationParameter.ProtoReflect.Descriptor instead.
func (*TranslationParameter) Descriptor() ([]byte, []int) {
	return file_frontend_proto_translation_proto_rawDescGZIP(), []int{11}
}

func (x *TranslationParameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (m *TranslationParameter) GetValue() isTranslationParameter_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *TranslationParameter) GetIntValue() *IntegerValue {
	if x, ok := x.GetValue().(*TranslationParameter_IntValue); ok {
		return x.IntValue
	}
	return nil
}

func (x *TranslationParameter) GetDoubleValue() *DoubleValue {
	if x, ok := x.GetValue().(*TranslationParameter_DoubleValue); ok {
		return x.DoubleValue
	}
	return nil
}

func (x *TranslationParameter) GetStringValue() *StringValue {
	if x, ok := x.GetValue().(*TranslationParameter_StringValue); ok {
		return x.StringValue
	}
	return nil
}

func (x *TranslationParameter) GetTemperatureValue() *TemperatureValue {
	if x, ok := x.GetValue().(*TranslationParameter_TemperatureValue); ok {
		return x.TemperatureValue
	}
	return nil
}

func (x *TranslationParameter) GetPercentValue() *PercentValue {
	if x, ok := x.GetValue().(*TranslationParameter_PercentValue); ok {
		return x.PercentValue
	}
	return nil
}

func (x *TranslationParameter) GetVoltageValue() *VoltageValue {
	if x, ok := x.GetValue().(*TranslationParameter_VoltageValue); ok {
		return x.VoltageValue
	}
	return nil
}

func (x *TranslationParameter) GetFrequencyValue() *FrequencyValue {
	if x, ok := x.GetValue().(*TranslationParameter_FrequencyValue); ok {
		return x.FrequencyValue
	}
	return nil
}

func (x *TranslationParameter) GetAreaValue() *AreaValue {
	if x, ok := x.GetValue().(*TranslationParameter_AreaValue); ok {
		return x.AreaValue
	}
	return nil
}

func (x *TranslationParameter) GetDurationValue() *DurationValue {
	if x, ok := x.GetValue().(*TranslationParameter_DurationValue); ok {
		return x.DurationValue
	}
	return nil
}

func (x *TranslationParameter) GetDistanceValue() *DistanceValue {
	if x, ok := x.GetValue().(*TranslationParameter_DistanceValue); ok {
		return x.DistanceValue
	}
	return nil
}

func (x *TranslationParameter) GetSpeedValue() *SpeedValue {
	if x, ok := x.GetValue().(*TranslationParameter_SpeedValue); ok {
		return x.SpeedValue
	}
	return nil
}

type isTranslationParameter_Value interface {
	isTranslationParameter_Value()
}

type TranslationParameter_IntValue struct {
	IntValue *IntegerValue `protobuf:"bytes,2,opt,name=int_value,json=intValue,proto3,oneof"`
}

type TranslationParameter_DoubleValue struct {
	DoubleValue *DoubleValue `protobuf:"bytes,3,opt,name=double_value,json=doubleValue,proto3,oneof"`
}

type TranslationParameter_StringValue struct {
	StringValue *StringValue `protobuf:"bytes,4,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type TranslationParameter_TemperatureValue struct {
	TemperatureValue *TemperatureValue `protobuf:"bytes,5,opt,name=temperature_value,json=temperatureValue,proto3,oneof"`
}

type TranslationParameter_PercentValue struct {
	PercentValue *PercentValue `protobuf:"bytes,6,opt,name=percent_value,json=percentValue,proto3,oneof"`
}

type TranslationParameter_VoltageValue struct {
	VoltageValue *VoltageValue `protobuf:"bytes,7,opt,name=voltage_value,json=voltageValue,proto3,oneof"`
}

type TranslationParameter_FrequencyValue struct {
	FrequencyValue *FrequencyValue `protobuf:"bytes,8,opt,name=frequency_value,json=frequencyValue,proto3,oneof"`
}

type TranslationParameter_AreaValue struct {
	AreaValue *AreaValue `protobuf:"bytes,9,opt,name=area_value,json=areaValue,proto3,oneof"`
}

type TranslationParameter_DurationValue struct {
	DurationValue *DurationValue `protobuf:"bytes,10,opt,name=duration_value,json=durationValue,proto3,oneof"`
}

type TranslationParameter_DistanceValue struct {
	DistanceValue *DistanceValue `protobuf:"bytes,11,opt,name=distance_value,json=distanceValue,proto3,oneof"`
}

type TranslationParameter_SpeedValue struct {
	SpeedValue *SpeedValue `protobuf:"bytes,12,opt,name=speed_value,json=speedValue,proto3,oneof"`
}

func (*TranslationParameter_IntValue) isTranslationParameter_Value() {}

func (*TranslationParameter_DoubleValue) isTranslationParameter_Value() {}

func (*TranslationParameter_StringValue) isTranslationParameter_Value() {}

func (*TranslationParameter_TemperatureValue) isTranslationParameter_Value() {}

func (*TranslationParameter_PercentValue) isTranslationParameter_Value() {}

func (*TranslationParameter_VoltageValue) isTranslationParameter_Value() {}

func (*TranslationParameter_FrequencyValue) isTranslationParameter_Value() {}

func (*TranslationParameter_AreaValue) isTranslationParameter_Value() {}

func (*TranslationParameter_DurationValue) isTranslationParameter_Value() {}

func (*TranslationParameter_DistanceValue) isTranslationParameter_Value() {}

func (*TranslationParameter_SpeedValue) isTranslationParameter_Value() {}

var File_frontend_proto_translation_proto protoreflect.FileDescriptor

var file_frontend_proto_translation_proto_rawDesc = []byte{
	0x0a, 0x20, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x24, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x59, 0x0a, 0x10, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x07, 0x63, 0x65, 0x6c, 0x63, 0x69, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x07, 0x63, 0x65, 0x6c, 0x63, 0x69, 0x75, 0x73, 0x12,
	0x20, 0x0a, 0x0a, 0x66, 0x61, 0x68, 0x72, 0x65, 0x6e, 0x68, 0x65, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x61, 0x68, 0x72, 0x65, 0x6e, 0x68, 0x65, 0x69,
	0x74, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x28, 0x0a, 0x0c, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x22, 0x24, 0x0a, 0x0c, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x6f, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x6f, 0x6c, 0x74, 0x73, 0x22, 0x26, 0x0a, 0x0e, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x68, 0x65, 0x72, 0x74, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x68, 0x65, 0x72,
	0x74, 0x7a, 0x22, 0x94, 0x01, 0x0a, 0x09, 0x41, 0x72, 0x65, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x16, 0x0a, 0x05, 0x61, 0x63, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x48,
	0x00, 0x52, 0x05, 0x61, 0x63, 0x72, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x08, 0x68, 0x65, 0x63, 0x74,
	0x61, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x08, 0x68, 0x65,
	0x63, 0x74, 0x61, 0x72, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65,
	0x5f, 0x66, 0x65, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x73,
	0x71, 0x75, 0x61, 0x72, 0x65, 0x46, 0x65, 0x65, 0x74, 0x12, 0x25, 0x0a, 0x0d, 0x73, 0x71, 0x75,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x48, 0x00, 0x52, 0x0c, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x24, 0x0a, 0x0c, 0x6d,
	0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x48, 0x00, 0x52, 0x0c, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x12, 0x1a, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x1a, 0x0a,
	0x07, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00,
	0x52, 0x07, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x05, 0x68, 0x6f, 0x75,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x05, 0x68, 0x6f, 0x75, 0x72,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xe4, 0x01, 0x0a, 0x0d, 0x44,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x22, 0x0a, 0x0b,
	0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x18, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x48, 0x00, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0a, 0x6b, 0x69,
	0x6c, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00,
	0x52, 0x0a, 0x6b, 0x69, 0x6c, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x06,
	0x69, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x66, 0x65, 0x65, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x04, 0x66, 0x65, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x05,
	0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x05, 0x6d,
	0x69, 0x6c, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x65, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x6f, 0x0a, 0x0a, 0x53, 0x70, 0x65, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x30, 0x0a, 0x13, 0x6b, 0x69, 0x6c, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x70, 0x65,
	0x72, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x11,
	0x6b, 0x69, 0x6c, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75,
	0x72, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x68,
	0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0c, 0x6d, 0x69, 0x6c,
	0x65, 0x73, 0x50, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0xb4, 0x07, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x48, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52,
	0x08, 0x69, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x64, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x5c, 0x0a, 0x11, 0x74, 0x65, 0x6d, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x48, 0x00, 0x52, 0x10, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x50, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x50, 0x0a, 0x0d, 0x76, 0x6f, 0x6c, 0x74, 0x61,
	0x67, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c,
	0x74, 0x61, 0x67, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x76, 0x6f, 0x6c,
	0x74, 0x61, 0x67, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x66, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x0e, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x47, 0x0a, 0x0a, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52,
	0x09, 0x61, 0x72, 0x65, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00,
	0x52, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x53, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x4a, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_translation_proto_rawDescOnce sync.Once
	file_frontend_proto_translation_proto_rawDescData = file_frontend_proto_translation_proto_rawDesc
)

func file_frontend_proto_translation_proto_rawDescGZIP() []byte {
	file_frontend_proto_translation_proto_rawDescOnce.Do(func() {
		file_frontend_proto_translation_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_translation_proto_rawDescData)
	})
	return file_frontend_proto_translation_proto_rawDescData
}

var file_frontend_proto_translation_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_frontend_proto_translation_proto_goTypes = []interface{}{
	(*IntegerValue)(nil),         // 0: carbon.frontend.translation.IntegerValue
	(*DoubleValue)(nil),          // 1: carbon.frontend.translation.DoubleValue
	(*StringValue)(nil),          // 2: carbon.frontend.translation.StringValue
	(*TemperatureValue)(nil),     // 3: carbon.frontend.translation.TemperatureValue
	(*PercentValue)(nil),         // 4: carbon.frontend.translation.PercentValue
	(*VoltageValue)(nil),         // 5: carbon.frontend.translation.VoltageValue
	(*FrequencyValue)(nil),       // 6: carbon.frontend.translation.FrequencyValue
	(*AreaValue)(nil),            // 7: carbon.frontend.translation.AreaValue
	(*DurationValue)(nil),        // 8: carbon.frontend.translation.DurationValue
	(*DistanceValue)(nil),        // 9: carbon.frontend.translation.DistanceValue
	(*SpeedValue)(nil),           // 10: carbon.frontend.translation.SpeedValue
	(*TranslationParameter)(nil), // 11: carbon.frontend.translation.TranslationParameter
}
var file_frontend_proto_translation_proto_depIdxs = []int32{
	0,  // 0: carbon.frontend.translation.TranslationParameter.int_value:type_name -> carbon.frontend.translation.IntegerValue
	1,  // 1: carbon.frontend.translation.TranslationParameter.double_value:type_name -> carbon.frontend.translation.DoubleValue
	2,  // 2: carbon.frontend.translation.TranslationParameter.string_value:type_name -> carbon.frontend.translation.StringValue
	3,  // 3: carbon.frontend.translation.TranslationParameter.temperature_value:type_name -> carbon.frontend.translation.TemperatureValue
	4,  // 4: carbon.frontend.translation.TranslationParameter.percent_value:type_name -> carbon.frontend.translation.PercentValue
	5,  // 5: carbon.frontend.translation.TranslationParameter.voltage_value:type_name -> carbon.frontend.translation.VoltageValue
	6,  // 6: carbon.frontend.translation.TranslationParameter.frequency_value:type_name -> carbon.frontend.translation.FrequencyValue
	7,  // 7: carbon.frontend.translation.TranslationParameter.area_value:type_name -> carbon.frontend.translation.AreaValue
	8,  // 8: carbon.frontend.translation.TranslationParameter.duration_value:type_name -> carbon.frontend.translation.DurationValue
	9,  // 9: carbon.frontend.translation.TranslationParameter.distance_value:type_name -> carbon.frontend.translation.DistanceValue
	10, // 10: carbon.frontend.translation.TranslationParameter.speed_value:type_name -> carbon.frontend.translation.SpeedValue
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_frontend_proto_translation_proto_init() }
func file_frontend_proto_translation_proto_init() {
	if File_frontend_proto_translation_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_translation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntegerValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemperatureValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PercentValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoltageValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FrequencyValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DurationValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DistanceValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpeedValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_translation_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslationParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_translation_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*TemperatureValue_Celcius)(nil),
		(*TemperatureValue_Fahrenheit)(nil),
	}
	file_frontend_proto_translation_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*AreaValue_Acres)(nil),
		(*AreaValue_Hectares)(nil),
		(*AreaValue_SquareFeet)(nil),
		(*AreaValue_SquareMeters)(nil),
	}
	file_frontend_proto_translation_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*DurationValue_Milliseconds)(nil),
		(*DurationValue_Seconds)(nil),
		(*DurationValue_Minutes)(nil),
		(*DurationValue_Hours)(nil),
	}
	file_frontend_proto_translation_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*DistanceValue_Millimeters)(nil),
		(*DistanceValue_Meters)(nil),
		(*DistanceValue_Kilometers)(nil),
		(*DistanceValue_Inches)(nil),
		(*DistanceValue_Feet)(nil),
		(*DistanceValue_Miles)(nil),
		(*DistanceValue_Centimeters)(nil),
	}
	file_frontend_proto_translation_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*SpeedValue_KilometersPerHour)(nil),
		(*SpeedValue_MilesPerHour)(nil),
	}
	file_frontend_proto_translation_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*TranslationParameter_IntValue)(nil),
		(*TranslationParameter_DoubleValue)(nil),
		(*TranslationParameter_StringValue)(nil),
		(*TranslationParameter_TemperatureValue)(nil),
		(*TranslationParameter_PercentValue)(nil),
		(*TranslationParameter_VoltageValue)(nil),
		(*TranslationParameter_FrequencyValue)(nil),
		(*TranslationParameter_AreaValue)(nil),
		(*TranslationParameter_DurationValue)(nil),
		(*TranslationParameter_DistanceValue)(nil),
		(*TranslationParameter_SpeedValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_translation_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_frontend_proto_translation_proto_goTypes,
		DependencyIndexes: file_frontend_proto_translation_proto_depIdxs,
		MessageInfos:      file_frontend_proto_translation_proto_msgTypes,
	}.Build()
	File_frontend_proto_translation_proto = out.File
	file_frontend_proto_translation_proto_rawDesc = nil
	file_frontend_proto_translation_proto_goTypes = nil
	file_frontend_proto_translation_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/actuation_tasks.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	aimbot "github.com/carbonrobotics/robot/golang/generated/proto/aimbot"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GlobalAimbotActuationTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId uint32                       `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"` // 0 means all of them
	Task  *aimbot.ActuationTaskRequest `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`
}

func (x *GlobalAimbotActuationTaskRequest) Reset() {
	*x = GlobalAimbotActuationTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_actuation_tasks_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalAimbotActuationTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalAimbotActuationTaskRequest) ProtoMessage() {}

func (x *GlobalAimbotActuationTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_actuation_tasks_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalAimbotActuationTaskRequest.ProtoReflect.Descriptor instead.
func (*GlobalAimbotActuationTaskRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_actuation_tasks_proto_rawDescGZIP(), []int{0}
}

func (x *GlobalAimbotActuationTaskRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GlobalAimbotActuationTaskRequest) GetTask() *aimbot.ActuationTaskRequest {
	if x != nil {
		return x.Task
	}
	return nil
}

type GlobalActuationTaskState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts             *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Running        bool       `protobuf:"varint,2,opt,name=running,proto3" json:"running,omitempty"`
	ElapsedTimeMs  uint32     `protobuf:"varint,3,opt,name=elapsed_time_ms,json=elapsedTimeMs,proto3" json:"elapsed_time_ms,omitempty"`
	ExpectedTimeMs uint32     `protobuf:"varint,4,opt,name=expected_time_ms,json=expectedTimeMs,proto3" json:"expected_time_ms,omitempty"`
}

func (x *GlobalActuationTaskState) Reset() {
	*x = GlobalActuationTaskState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_actuation_tasks_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalActuationTaskState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalActuationTaskState) ProtoMessage() {}

func (x *GlobalActuationTaskState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_actuation_tasks_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalActuationTaskState.ProtoReflect.Descriptor instead.
func (*GlobalActuationTaskState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_actuation_tasks_proto_rawDescGZIP(), []int{1}
}

func (x *GlobalActuationTaskState) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GlobalActuationTaskState) GetRunning() bool {
	if x != nil {
		return x.Running
	}
	return false
}

func (x *GlobalActuationTaskState) GetElapsedTimeMs() uint32 {
	if x != nil {
		return x.ElapsedTimeMs
	}
	return 0
}

func (x *GlobalActuationTaskState) GetExpectedTimeMs() uint32 {
	if x != nil {
		return x.ExpectedTimeMs
	}
	return 0
}

var File_frontend_proto_actuation_tasks_proto protoreflect.FileDescriptor

var file_frontend_proto_actuation_tasks_proto_rawDesc = []byte{
	0x0a, 0x24, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x1a, 0x48, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x2f,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x20,
	0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x41,
	0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0xb7, 0x01, 0x0a, 0x18, 0x47, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x65, 0x6c, 0x61, 0x70,
	0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x32, 0xf6, 0x02, 0x0a, 0x15, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7d, 0x0a,
	0x1f, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x63,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x1a, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x73, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x80, 0x01, 0x0a,
	0x1e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x41, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x73, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x41, 0x63,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x5b, 0x0a, 0x1f, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_actuation_tasks_proto_rawDescOnce sync.Once
	file_frontend_proto_actuation_tasks_proto_rawDescData = file_frontend_proto_actuation_tasks_proto_rawDesc
)

func file_frontend_proto_actuation_tasks_proto_rawDescGZIP() []byte {
	file_frontend_proto_actuation_tasks_proto_rawDescOnce.Do(func() {
		file_frontend_proto_actuation_tasks_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_actuation_tasks_proto_rawDescData)
	})
	return file_frontend_proto_actuation_tasks_proto_rawDescData
}

var file_frontend_proto_actuation_tasks_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_frontend_proto_actuation_tasks_proto_goTypes = []interface{}{
	(*GlobalAimbotActuationTaskRequest)(nil), // 0: carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest
	(*GlobalActuationTaskState)(nil),         // 1: carbon.frontend.actuation_tasks.GlobalActuationTaskState
	(*aimbot.ActuationTaskRequest)(nil),      // 2: aimbot.ActuationTaskRequest
	(*Timestamp)(nil),                        // 3: carbon.frontend.util.Timestamp
	(*Empty)(nil),                            // 4: carbon.frontend.util.Empty
}
var file_frontend_proto_actuation_tasks_proto_depIdxs = []int32{
	2, // 0: carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task:type_name -> aimbot.ActuationTaskRequest
	3, // 1: carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts:type_name -> carbon.frontend.util.Timestamp
	3, // 2: carbon.frontend.actuation_tasks.ActuationTasksService.GetNextGlobalActuationTaskState:input_type -> carbon.frontend.util.Timestamp
	0, // 3: carbon.frontend.actuation_tasks.ActuationTasksService.StartGlobalAimbotActuationTask:input_type -> carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest
	4, // 4: carbon.frontend.actuation_tasks.ActuationTasksService.CancelGlobalAimbotActuationTask:input_type -> carbon.frontend.util.Empty
	1, // 5: carbon.frontend.actuation_tasks.ActuationTasksService.GetNextGlobalActuationTaskState:output_type -> carbon.frontend.actuation_tasks.GlobalActuationTaskState
	4, // 6: carbon.frontend.actuation_tasks.ActuationTasksService.StartGlobalAimbotActuationTask:output_type -> carbon.frontend.util.Empty
	4, // 7: carbon.frontend.actuation_tasks.ActuationTasksService.CancelGlobalAimbotActuationTask:output_type -> carbon.frontend.util.Empty
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_frontend_proto_actuation_tasks_proto_init() }
func file_frontend_proto_actuation_tasks_proto_init() {
	if File_frontend_proto_actuation_tasks_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_actuation_tasks_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalAimbotActuationTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_actuation_tasks_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalActuationTaskState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_actuation_tasks_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_actuation_tasks_proto_goTypes,
		DependencyIndexes: file_frontend_proto_actuation_tasks_proto_depIdxs,
		MessageInfos:      file_frontend_proto_actuation_tasks_proto_msgTypes,
	}.Build()
	File_frontend_proto_actuation_tasks_proto = out.File
	file_frontend_proto_actuation_tasks_proto_rawDesc = nil
	file_frontend_proto_actuation_tasks_proto_goTypes = nil
	file_frontend_proto_actuation_tasks_proto_depIdxs = nil
}

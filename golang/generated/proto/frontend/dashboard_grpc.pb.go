// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/dashboard.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DashboardService_ToggleRow_FullMethodName              = "/carbon.frontend.dashboard.DashboardService/ToggleRow"
	DashboardService_ToggleLasers_FullMethodName           = "/carbon.frontend.dashboard.DashboardService/ToggleLasers"
	DashboardService_GetNextDashboardState_FullMethodName  = "/carbon.frontend.dashboard.DashboardService/GetNextDashboardState"
	DashboardService_GetCropModelOptions_FullMethodName    = "/carbon.frontend.dashboard.DashboardService/GetCropModelOptions"
	DashboardService_SetCropModel_FullMethodName           = "/carbon.frontend.dashboard.DashboardService/SetCropModel"
	DashboardService_GetNextWeedingVelocity_FullMethodName = "/carbon.frontend.dashboard.DashboardService/GetNextWeedingVelocity"
	DashboardService_SetTargetingState_FullMethodName      = "/carbon.frontend.dashboard.DashboardService/SetTargetingState"
	DashboardService_SetRowSpacing_FullMethodName          = "/carbon.frontend.dashboard.DashboardService/SetRowSpacing"
	DashboardService_SetCruiseEnabled_FullMethodName       = "/carbon.frontend.dashboard.DashboardService/SetCruiseEnabled"
)

// DashboardServiceClient is the client API for DashboardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DashboardServiceClient interface {
	ToggleRow(ctx context.Context, in *RowId, opts ...grpc.CallOption) (*Empty, error)
	ToggleLasers(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetNextDashboardState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*DashboardStateMessage, error)
	// Deprecated: Do not use.
	GetCropModelOptions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*CropModelOptions, error)
	// Deprecated: Do not use.
	SetCropModel(ctx context.Context, in *CropModel, opts ...grpc.CallOption) (*Empty, error)
	GetNextWeedingVelocity(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*WeedingVelocity, error)
	SetTargetingState(ctx context.Context, in *TargetingState, opts ...grpc.CallOption) (*Empty, error)
	SetRowSpacing(ctx context.Context, in *RowSpacing, opts ...grpc.CallOption) (*Empty, error)
	SetCruiseEnabled(ctx context.Context, in *CruiseEnable, opts ...grpc.CallOption) (*Empty, error)
}

type dashboardServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDashboardServiceClient(cc grpc.ClientConnInterface) DashboardServiceClient {
	return &dashboardServiceClient{cc}
}

func (c *dashboardServiceClient) ToggleRow(ctx context.Context, in *RowId, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DashboardService_ToggleRow_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) ToggleLasers(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DashboardService_ToggleLasers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) GetNextDashboardState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*DashboardStateMessage, error) {
	out := new(DashboardStateMessage)
	err := c.cc.Invoke(ctx, DashboardService_GetNextDashboardState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *dashboardServiceClient) GetCropModelOptions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*CropModelOptions, error) {
	out := new(CropModelOptions)
	err := c.cc.Invoke(ctx, DashboardService_GetCropModelOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *dashboardServiceClient) SetCropModel(ctx context.Context, in *CropModel, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DashboardService_SetCropModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) GetNextWeedingVelocity(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*WeedingVelocity, error) {
	out := new(WeedingVelocity)
	err := c.cc.Invoke(ctx, DashboardService_GetNextWeedingVelocity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) SetTargetingState(ctx context.Context, in *TargetingState, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DashboardService_SetTargetingState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) SetRowSpacing(ctx context.Context, in *RowSpacing, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DashboardService_SetRowSpacing_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) SetCruiseEnabled(ctx context.Context, in *CruiseEnable, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DashboardService_SetCruiseEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DashboardServiceServer is the server API for DashboardService service.
// All implementations must embed UnimplementedDashboardServiceServer
// for forward compatibility
type DashboardServiceServer interface {
	ToggleRow(context.Context, *RowId) (*Empty, error)
	ToggleLasers(context.Context, *Empty) (*Empty, error)
	GetNextDashboardState(context.Context, *Timestamp) (*DashboardStateMessage, error)
	// Deprecated: Do not use.
	GetCropModelOptions(context.Context, *Empty) (*CropModelOptions, error)
	// Deprecated: Do not use.
	SetCropModel(context.Context, *CropModel) (*Empty, error)
	GetNextWeedingVelocity(context.Context, *Timestamp) (*WeedingVelocity, error)
	SetTargetingState(context.Context, *TargetingState) (*Empty, error)
	SetRowSpacing(context.Context, *RowSpacing) (*Empty, error)
	SetCruiseEnabled(context.Context, *CruiseEnable) (*Empty, error)
	mustEmbedUnimplementedDashboardServiceServer()
}

// UnimplementedDashboardServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDashboardServiceServer struct {
}

func (UnimplementedDashboardServiceServer) ToggleRow(context.Context, *RowId) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ToggleRow not implemented")
}
func (UnimplementedDashboardServiceServer) ToggleLasers(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ToggleLasers not implemented")
}
func (UnimplementedDashboardServiceServer) GetNextDashboardState(context.Context, *Timestamp) (*DashboardStateMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextDashboardState not implemented")
}
func (UnimplementedDashboardServiceServer) GetCropModelOptions(context.Context, *Empty) (*CropModelOptions, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCropModelOptions not implemented")
}
func (UnimplementedDashboardServiceServer) SetCropModel(context.Context, *CropModel) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCropModel not implemented")
}
func (UnimplementedDashboardServiceServer) GetNextWeedingVelocity(context.Context, *Timestamp) (*WeedingVelocity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextWeedingVelocity not implemented")
}
func (UnimplementedDashboardServiceServer) SetTargetingState(context.Context, *TargetingState) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTargetingState not implemented")
}
func (UnimplementedDashboardServiceServer) SetRowSpacing(context.Context, *RowSpacing) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRowSpacing not implemented")
}
func (UnimplementedDashboardServiceServer) SetCruiseEnabled(context.Context, *CruiseEnable) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCruiseEnabled not implemented")
}
func (UnimplementedDashboardServiceServer) mustEmbedUnimplementedDashboardServiceServer() {}

// UnsafeDashboardServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DashboardServiceServer will
// result in compilation errors.
type UnsafeDashboardServiceServer interface {
	mustEmbedUnimplementedDashboardServiceServer()
}

func RegisterDashboardServiceServer(s grpc.ServiceRegistrar, srv DashboardServiceServer) {
	s.RegisterService(&DashboardService_ServiceDesc, srv)
}

func _DashboardService_ToggleRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RowId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).ToggleRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_ToggleRow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).ToggleRow(ctx, req.(*RowId))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_ToggleLasers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).ToggleLasers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_ToggleLasers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).ToggleLasers(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_GetNextDashboardState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).GetNextDashboardState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_GetNextDashboardState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).GetNextDashboardState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_GetCropModelOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).GetCropModelOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_GetCropModelOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).GetCropModelOptions(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_SetCropModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CropModel)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).SetCropModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_SetCropModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).SetCropModel(ctx, req.(*CropModel))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_GetNextWeedingVelocity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).GetNextWeedingVelocity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_GetNextWeedingVelocity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).GetNextWeedingVelocity(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_SetTargetingState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TargetingState)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).SetTargetingState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_SetTargetingState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).SetTargetingState(ctx, req.(*TargetingState))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_SetRowSpacing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RowSpacing)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).SetRowSpacing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_SetRowSpacing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).SetRowSpacing(ctx, req.(*RowSpacing))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_SetCruiseEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CruiseEnable)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).SetCruiseEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_SetCruiseEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).SetCruiseEnabled(ctx, req.(*CruiseEnable))
	}
	return interceptor(ctx, in, info, handler)
}

// DashboardService_ServiceDesc is the grpc.ServiceDesc for DashboardService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DashboardService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.dashboard.DashboardService",
	HandlerType: (*DashboardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ToggleRow",
			Handler:    _DashboardService_ToggleRow_Handler,
		},
		{
			MethodName: "ToggleLasers",
			Handler:    _DashboardService_ToggleLasers_Handler,
		},
		{
			MethodName: "GetNextDashboardState",
			Handler:    _DashboardService_GetNextDashboardState_Handler,
		},
		{
			MethodName: "GetCropModelOptions",
			Handler:    _DashboardService_GetCropModelOptions_Handler,
		},
		{
			MethodName: "SetCropModel",
			Handler:    _DashboardService_SetCropModel_Handler,
		},
		{
			MethodName: "GetNextWeedingVelocity",
			Handler:    _DashboardService_GetNextWeedingVelocity_Handler,
		},
		{
			MethodName: "SetTargetingState",
			Handler:    _DashboardService_SetTargetingState_Handler,
		},
		{
			MethodName: "SetRowSpacing",
			Handler:    _DashboardService_SetRowSpacing_Handler,
		},
		{
			MethodName: "SetCruiseEnabled",
			Handler:    _DashboardService_SetCruiseEnabled_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/dashboard.proto",
}

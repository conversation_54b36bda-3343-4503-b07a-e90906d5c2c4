// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/almanac.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	almanac "github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Deprecated: Marked as deprecated in frontend/proto/almanac.proto.
type GetConfigDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumSizeCategories uint32            `protobuf:"varint,1,opt,name=num_size_categories,json=numSizeCategories,proto3" json:"num_size_categories,omitempty"`
	CropCategoryNames map[string]string `protobuf:"bytes,2,rep,name=crop_category_names,json=cropCategoryNames,proto3" json:"crop_category_names,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WeedCategoryNames map[string]string `protobuf:"bytes,3,rep,name=weed_category_names,json=weedCategoryNames,proto3" json:"weed_category_names,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetConfigDataResponse) Reset() {
	*x = GetConfigDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigDataResponse) ProtoMessage() {}

func (x *GetConfigDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigDataResponse.ProtoReflect.Descriptor instead.
func (*GetConfigDataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{0}
}

func (x *GetConfigDataResponse) GetNumSizeCategories() uint32 {
	if x != nil {
		return x.NumSizeCategories
	}
	return 0
}

func (x *GetConfigDataResponse) GetCropCategoryNames() map[string]string {
	if x != nil {
		return x.CropCategoryNames
	}
	return nil
}

func (x *GetConfigDataResponse) GetWeedCategoryNames() map[string]string {
	if x != nil {
		return x.WeedCategoryNames
	}
	return nil
}

type LoadAlmanacConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *LoadAlmanacConfigRequest) Reset() {
	*x = LoadAlmanacConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadAlmanacConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadAlmanacConfigRequest) ProtoMessage() {}

func (x *LoadAlmanacConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadAlmanacConfigRequest.ProtoReflect.Descriptor instead.
func (*LoadAlmanacConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{1}
}

func (x *LoadAlmanacConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LoadAlmanacConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *almanac.AlmanacConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *LoadAlmanacConfigResponse) Reset() {
	*x = LoadAlmanacConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadAlmanacConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadAlmanacConfigResponse) ProtoMessage() {}

func (x *LoadAlmanacConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadAlmanacConfigResponse.ProtoReflect.Descriptor instead.
func (*LoadAlmanacConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{2}
}

func (x *LoadAlmanacConfigResponse) GetConfig() *almanac.AlmanacConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type SaveAlmanacConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config    *almanac.AlmanacConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	SetActive bool                   `protobuf:"varint,2,opt,name=set_active,json=setActive,proto3" json:"set_active,omitempty"`
}

func (x *SaveAlmanacConfigRequest) Reset() {
	*x = SaveAlmanacConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAlmanacConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAlmanacConfigRequest) ProtoMessage() {}

func (x *SaveAlmanacConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAlmanacConfigRequest.ProtoReflect.Descriptor instead.
func (*SaveAlmanacConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{3}
}

func (x *SaveAlmanacConfigRequest) GetConfig() *almanac.AlmanacConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *SaveAlmanacConfigRequest) GetSetActive() bool {
	if x != nil {
		return x.SetActive
	}
	return false
}

type SaveAlmanacConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // if save created a new config this will be the new uuid
}

func (x *SaveAlmanacConfigResponse) Reset() {
	*x = SaveAlmanacConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAlmanacConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAlmanacConfigResponse) ProtoMessage() {}

func (x *SaveAlmanacConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAlmanacConfigResponse.ProtoReflect.Descriptor instead.
func (*SaveAlmanacConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{4}
}

func (x *SaveAlmanacConfigResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SetActiveAlmanacConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SetActiveAlmanacConfigRequest) Reset() {
	*x = SetActiveAlmanacConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveAlmanacConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveAlmanacConfigRequest) ProtoMessage() {}

func (x *SetActiveAlmanacConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveAlmanacConfigRequest.ProtoReflect.Descriptor instead.
func (*SetActiveAlmanacConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{5}
}

func (x *SetActiveAlmanacConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteAlmanacConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	NewActiveId string `protobuf:"bytes,2,opt,name=new_active_id,json=newActiveId,proto3" json:"new_active_id,omitempty"` // required if id is currently active
}

func (x *DeleteAlmanacConfigRequest) Reset() {
	*x = DeleteAlmanacConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAlmanacConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAlmanacConfigRequest) ProtoMessage() {}

func (x *DeleteAlmanacConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAlmanacConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteAlmanacConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteAlmanacConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteAlmanacConfigRequest) GetNewActiveId() string {
	if x != nil {
		return x.NewActiveId
	}
	return ""
}

type GetNextAlmanacConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts        *Timestamp        `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Active    string            `protobuf:"bytes,2,opt,name=active,proto3" json:"active,omitempty"`
	Available map[string]string `protobuf:"bytes,3,rep,name=available,proto3" json:"available,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //ID -> Name
}

func (x *GetNextAlmanacConfigResponse) Reset() {
	*x = GetNextAlmanacConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAlmanacConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAlmanacConfigResponse) ProtoMessage() {}

func (x *GetNextAlmanacConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAlmanacConfigResponse.ProtoReflect.Descriptor instead.
func (*GetNextAlmanacConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{7}
}

func (x *GetNextAlmanacConfigResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextAlmanacConfigResponse) GetActive() string {
	if x != nil {
		return x.Active
	}
	return ""
}

func (x *GetNextAlmanacConfigResponse) GetAvailable() map[string]string {
	if x != nil {
		return x.Available
	}
	return nil
}

type LoadDiscriminatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *LoadDiscriminatorConfigRequest) Reset() {
	*x = LoadDiscriminatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadDiscriminatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadDiscriminatorConfigRequest) ProtoMessage() {}

func (x *LoadDiscriminatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadDiscriminatorConfigRequest.ProtoReflect.Descriptor instead.
func (*LoadDiscriminatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{8}
}

func (x *LoadDiscriminatorConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LoadDiscriminatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *almanac.DiscriminatorConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *LoadDiscriminatorConfigResponse) Reset() {
	*x = LoadDiscriminatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadDiscriminatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadDiscriminatorConfigResponse) ProtoMessage() {}

func (x *LoadDiscriminatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadDiscriminatorConfigResponse.ProtoReflect.Descriptor instead.
func (*LoadDiscriminatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{9}
}

func (x *LoadDiscriminatorConfigResponse) GetConfig() *almanac.DiscriminatorConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type SaveDiscriminatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config                  *almanac.DiscriminatorConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	AssociateWithActiveCrop bool                         `protobuf:"varint,2,opt,name=associate_with_active_crop,json=associateWithActiveCrop,proto3" json:"associate_with_active_crop,omitempty"`
}

func (x *SaveDiscriminatorConfigRequest) Reset() {
	*x = SaveDiscriminatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveDiscriminatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveDiscriminatorConfigRequest) ProtoMessage() {}

func (x *SaveDiscriminatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveDiscriminatorConfigRequest.ProtoReflect.Descriptor instead.
func (*SaveDiscriminatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{10}
}

func (x *SaveDiscriminatorConfigRequest) GetConfig() *almanac.DiscriminatorConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *SaveDiscriminatorConfigRequest) GetAssociateWithActiveCrop() bool {
	if x != nil {
		return x.AssociateWithActiveCrop
	}
	return false
}

type SaveDiscriminatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // if save created a new config this will be the new uuid
}

func (x *SaveDiscriminatorConfigResponse) Reset() {
	*x = SaveDiscriminatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveDiscriminatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveDiscriminatorConfigResponse) ProtoMessage() {}

func (x *SaveDiscriminatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveDiscriminatorConfigResponse.ProtoReflect.Descriptor instead.
func (*SaveDiscriminatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{11}
}

func (x *SaveDiscriminatorConfigResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SetActiveDiscriminatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CropId *string `protobuf:"bytes,2,opt,name=crop_id,json=cropId,proto3,oneof" json:"crop_id,omitempty"` // If set associate with this crop id instead of active crop id
}

func (x *SetActiveDiscriminatorConfigRequest) Reset() {
	*x = SetActiveDiscriminatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveDiscriminatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveDiscriminatorConfigRequest) ProtoMessage() {}

func (x *SetActiveDiscriminatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveDiscriminatorConfigRequest.ProtoReflect.Descriptor instead.
func (*SetActiveDiscriminatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{12}
}

func (x *SetActiveDiscriminatorConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetActiveDiscriminatorConfigRequest) GetCropId() string {
	if x != nil && x.CropId != nil {
		return *x.CropId
	}
	return ""
}

type DeleteDiscriminatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteDiscriminatorConfigRequest) Reset() {
	*x = DeleteDiscriminatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDiscriminatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDiscriminatorConfigRequest) ProtoMessage() {}

func (x *DeleteDiscriminatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDiscriminatorConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteDiscriminatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteDiscriminatorConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetNextDiscriminatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts        *Timestamp        `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Active    string            `protobuf:"bytes,2,opt,name=active,proto3" json:"active,omitempty"`
	Available map[string]string `protobuf:"bytes,3,rep,name=available,proto3" json:"available,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //ID -> Name
}

func (x *GetNextDiscriminatorConfigResponse) Reset() {
	*x = GetNextDiscriminatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextDiscriminatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextDiscriminatorConfigResponse) ProtoMessage() {}

func (x *GetNextDiscriminatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextDiscriminatorConfigResponse.ProtoReflect.Descriptor instead.
func (*GetNextDiscriminatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{14}
}

func (x *GetNextDiscriminatorConfigResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextDiscriminatorConfigResponse) GetActive() string {
	if x != nil {
		return x.Active
	}
	return ""
}

func (x *GetNextDiscriminatorConfigResponse) GetAvailable() map[string]string {
	if x != nil {
		return x.Available
	}
	return nil
}

type GetNextModelinatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts     *Timestamp                 `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Config *almanac.ModelinatorConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetNextModelinatorConfigResponse) Reset() {
	*x = GetNextModelinatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextModelinatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextModelinatorConfigResponse) ProtoMessage() {}

func (x *GetNextModelinatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextModelinatorConfigResponse.ProtoReflect.Descriptor instead.
func (*GetNextModelinatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{15}
}

func (x *GetNextModelinatorConfigResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextModelinatorConfigResponse) GetConfig() *almanac.ModelinatorConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type SaveModelinatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *almanac.ModelinatorConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *SaveModelinatorConfigRequest) Reset() {
	*x = SaveModelinatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveModelinatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveModelinatorConfigRequest) ProtoMessage() {}

func (x *SaveModelinatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveModelinatorConfigRequest.ProtoReflect.Descriptor instead.
func (*SaveModelinatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{16}
}

func (x *SaveModelinatorConfigRequest) GetConfig() *almanac.ModelinatorConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type FetchModelinatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	CropId  string `protobuf:"bytes,2,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *FetchModelinatorConfigRequest) Reset() {
	*x = FetchModelinatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchModelinatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchModelinatorConfigRequest) ProtoMessage() {}

func (x *FetchModelinatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchModelinatorConfigRequest.ProtoReflect.Descriptor instead.
func (*FetchModelinatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{17}
}

func (x *FetchModelinatorConfigRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *FetchModelinatorConfigRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

type FetchModelinatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *almanac.ModelinatorConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *FetchModelinatorConfigResponse) Reset() {
	*x = FetchModelinatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchModelinatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchModelinatorConfigResponse) ProtoMessage() {}

func (x *FetchModelinatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchModelinatorConfigResponse.ProtoReflect.Descriptor instead.
func (*FetchModelinatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{18}
}

func (x *FetchModelinatorConfigResponse) GetConfig() *almanac.ModelinatorConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type ResetModelinatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetModelinatorConfigRequest) Reset() {
	*x = ResetModelinatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetModelinatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetModelinatorConfigRequest) ProtoMessage() {}

func (x *ResetModelinatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetModelinatorConfigRequest.ProtoReflect.Descriptor instead.
func (*ResetModelinatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{19}
}

type GetNextConfigDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts   *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Lang string     `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetNextConfigDataRequest) Reset() {
	*x = GetNextConfigDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextConfigDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextConfigDataRequest) ProtoMessage() {}

func (x *GetNextConfigDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextConfigDataRequest.ProtoReflect.Descriptor instead.
func (*GetNextConfigDataRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{20}
}

func (x *GetNextConfigDataRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextConfigDataRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GetNextConfigDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                *Timestamp        `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	NumSizeCategories uint32            `protobuf:"varint,2,opt,name=num_size_categories,json=numSizeCategories,proto3" json:"num_size_categories,omitempty"`
	CropCategoryNames map[string]string `protobuf:"bytes,3,rep,name=crop_category_names,json=cropCategoryNames,proto3" json:"crop_category_names,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WeedCategoryNames map[string]string `protobuf:"bytes,4,rep,name=weed_category_names,json=weedCategoryNames,proto3" json:"weed_category_names,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetNextConfigDataResponse) Reset() {
	*x = GetNextConfigDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_almanac_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextConfigDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextConfigDataResponse) ProtoMessage() {}

func (x *GetNextConfigDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_almanac_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextConfigDataResponse.ProtoReflect.Descriptor instead.
func (*GetNextConfigDataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_almanac_proto_rawDescGZIP(), []int{21}
}

func (x *GetNextConfigDataResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextConfigDataResponse) GetNumSizeCategories() uint32 {
	if x != nil {
		return x.NumSizeCategories
	}
	return 0
}

func (x *GetNextConfigDataResponse) GetCropCategoryNames() map[string]string {
	if x != nil {
		return x.CropCategoryNames
	}
	return nil
}

func (x *GetNextConfigDataResponse) GetWeedCategoryNames() map[string]string {
	if x != nil {
		return x.WeedCategoryNames
	}
	return nil
}

var File_frontend_proto_almanac_proto protoreflect.FileDescriptor

var file_frontend_proto_almanac_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x63, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xc5, 0x03, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75, 0x6d,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x53, 0x69, 0x7a, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x75, 0x0a, 0x13, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63,
	0x72, 0x6f, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x75, 0x0a, 0x13, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x57, 0x65,
	0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x77, 0x65, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x1a, 0x44, 0x0a, 0x16, 0x43, 0x72, 0x6f, 0x70, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a,
	0x16, 0x57, 0x65, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x2a, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x64, 0x41,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x59, 0x0a, 0x19, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3c, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x77,
	0x0a, 0x18, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0x2b, 0x0a, 0x19, 0x53, 0x61, 0x76, 0x65, 0x41,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x2f, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x89, 0x02, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x62, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x3c, 0x0a, 0x0e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x30, 0x0a, 0x1e, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x63, 0x72,
	0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x65, 0x0a, 0x1f, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73,
	0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xa1, 0x01, 0x0a,
	0x1e, 0x53, 0x61, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x42, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x72, 0x6f,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x72, 0x6f, 0x70,
	0x22, 0x31, 0x0a, 0x1f, 0x53, 0x61, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x5f, 0x0a, 0x23, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x07, 0x63, 0x72,
	0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x63,
	0x72, 0x6f, 0x70, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69,
	0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x95, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x68, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73,
	0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x1a, 0x3c, 0x0a, 0x0e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x95, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x60, 0x0a, 0x1c, 0x53, 0x61, 0x76, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x53, 0x0a, 0x1d, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x22,
	0x62, 0x0a, 0x1e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69,
	0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0x1f, 0x0a, 0x1d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x5f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xfe, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x53, 0x69, 0x7a, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x79, 0x0a, 0x13, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x72,
	0x6f, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12,
	0x79, 0x0a, 0x13, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x57, 0x65, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x77, 0x65, 0x65, 0x64, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x1a, 0x44, 0x0a, 0x16, 0x43, 0x72,
	0x6f, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x44, 0x0a, 0x16, 0x57, 0x65, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x9f, 0x0f, 0x0a, 0x14, 0x41, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x61, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2e, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x12, 0x7a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a,
	0x0a, 0x11, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4c, 0x6f,
	0x61, 0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x11, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x67, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6e,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c,
	0x01, 0x0a, 0x17, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4c, 0x6f,
	0x61, 0x64, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01,
	0x0a, 0x17, 0x53, 0x61, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x1c,
	0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x73, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x7a, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x3b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73,
	0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6b, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x89, 0x01,
	0x0a, 0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x16, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_frontend_proto_almanac_proto_rawDescOnce sync.Once
	file_frontend_proto_almanac_proto_rawDescData = file_frontend_proto_almanac_proto_rawDesc
)

func file_frontend_proto_almanac_proto_rawDescGZIP() []byte {
	file_frontend_proto_almanac_proto_rawDescOnce.Do(func() {
		file_frontend_proto_almanac_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_almanac_proto_rawDescData)
	})
	return file_frontend_proto_almanac_proto_rawDescData
}

var file_frontend_proto_almanac_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_frontend_proto_almanac_proto_goTypes = []interface{}{
	(*GetConfigDataResponse)(nil),               // 0: carbon.frontend.almanac.GetConfigDataResponse
	(*LoadAlmanacConfigRequest)(nil),            // 1: carbon.frontend.almanac.LoadAlmanacConfigRequest
	(*LoadAlmanacConfigResponse)(nil),           // 2: carbon.frontend.almanac.LoadAlmanacConfigResponse
	(*SaveAlmanacConfigRequest)(nil),            // 3: carbon.frontend.almanac.SaveAlmanacConfigRequest
	(*SaveAlmanacConfigResponse)(nil),           // 4: carbon.frontend.almanac.SaveAlmanacConfigResponse
	(*SetActiveAlmanacConfigRequest)(nil),       // 5: carbon.frontend.almanac.SetActiveAlmanacConfigRequest
	(*DeleteAlmanacConfigRequest)(nil),          // 6: carbon.frontend.almanac.DeleteAlmanacConfigRequest
	(*GetNextAlmanacConfigResponse)(nil),        // 7: carbon.frontend.almanac.GetNextAlmanacConfigResponse
	(*LoadDiscriminatorConfigRequest)(nil),      // 8: carbon.frontend.almanac.LoadDiscriminatorConfigRequest
	(*LoadDiscriminatorConfigResponse)(nil),     // 9: carbon.frontend.almanac.LoadDiscriminatorConfigResponse
	(*SaveDiscriminatorConfigRequest)(nil),      // 10: carbon.frontend.almanac.SaveDiscriminatorConfigRequest
	(*SaveDiscriminatorConfigResponse)(nil),     // 11: carbon.frontend.almanac.SaveDiscriminatorConfigResponse
	(*SetActiveDiscriminatorConfigRequest)(nil), // 12: carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest
	(*DeleteDiscriminatorConfigRequest)(nil),    // 13: carbon.frontend.almanac.DeleteDiscriminatorConfigRequest
	(*GetNextDiscriminatorConfigResponse)(nil),  // 14: carbon.frontend.almanac.GetNextDiscriminatorConfigResponse
	(*GetNextModelinatorConfigResponse)(nil),    // 15: carbon.frontend.almanac.GetNextModelinatorConfigResponse
	(*SaveModelinatorConfigRequest)(nil),        // 16: carbon.frontend.almanac.SaveModelinatorConfigRequest
	(*FetchModelinatorConfigRequest)(nil),       // 17: carbon.frontend.almanac.FetchModelinatorConfigRequest
	(*FetchModelinatorConfigResponse)(nil),      // 18: carbon.frontend.almanac.FetchModelinatorConfigResponse
	(*ResetModelinatorConfigRequest)(nil),       // 19: carbon.frontend.almanac.ResetModelinatorConfigRequest
	(*GetNextConfigDataRequest)(nil),            // 20: carbon.frontend.almanac.GetNextConfigDataRequest
	(*GetNextConfigDataResponse)(nil),           // 21: carbon.frontend.almanac.GetNextConfigDataResponse
	nil,                                         // 22: carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry
	nil,                                         // 23: carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry
	nil,                                         // 24: carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry
	nil,                                         // 25: carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry
	nil,                                         // 26: carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry
	nil,                                         // 27: carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry
	(*almanac.AlmanacConfig)(nil),               // 28: carbon.aimbot.almanac.AlmanacConfig
	(*Timestamp)(nil),                           // 29: carbon.frontend.util.Timestamp
	(*almanac.DiscriminatorConfig)(nil),         // 30: carbon.aimbot.almanac.DiscriminatorConfig
	(*almanac.ModelinatorConfig)(nil),           // 31: carbon.aimbot.almanac.ModelinatorConfig
	(*Empty)(nil),                               // 32: carbon.frontend.util.Empty
}
var file_frontend_proto_almanac_proto_depIdxs = []int32{
	22, // 0: carbon.frontend.almanac.GetConfigDataResponse.crop_category_names:type_name -> carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry
	23, // 1: carbon.frontend.almanac.GetConfigDataResponse.weed_category_names:type_name -> carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry
	28, // 2: carbon.frontend.almanac.LoadAlmanacConfigResponse.config:type_name -> carbon.aimbot.almanac.AlmanacConfig
	28, // 3: carbon.frontend.almanac.SaveAlmanacConfigRequest.config:type_name -> carbon.aimbot.almanac.AlmanacConfig
	29, // 4: carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts:type_name -> carbon.frontend.util.Timestamp
	24, // 5: carbon.frontend.almanac.GetNextAlmanacConfigResponse.available:type_name -> carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry
	30, // 6: carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config:type_name -> carbon.aimbot.almanac.DiscriminatorConfig
	30, // 7: carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config:type_name -> carbon.aimbot.almanac.DiscriminatorConfig
	29, // 8: carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts:type_name -> carbon.frontend.util.Timestamp
	25, // 9: carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.available:type_name -> carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry
	29, // 10: carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts:type_name -> carbon.frontend.util.Timestamp
	31, // 11: carbon.frontend.almanac.GetNextModelinatorConfigResponse.config:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	31, // 12: carbon.frontend.almanac.SaveModelinatorConfigRequest.config:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	31, // 13: carbon.frontend.almanac.FetchModelinatorConfigResponse.config:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	29, // 14: carbon.frontend.almanac.GetNextConfigDataRequest.ts:type_name -> carbon.frontend.util.Timestamp
	29, // 15: carbon.frontend.almanac.GetNextConfigDataResponse.ts:type_name -> carbon.frontend.util.Timestamp
	26, // 16: carbon.frontend.almanac.GetNextConfigDataResponse.crop_category_names:type_name -> carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry
	27, // 17: carbon.frontend.almanac.GetNextConfigDataResponse.weed_category_names:type_name -> carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry
	32, // 18: carbon.frontend.almanac.AlmanacConfigService.GetConfigData:input_type -> carbon.frontend.util.Empty
	20, // 19: carbon.frontend.almanac.AlmanacConfigService.GetNextConfigData:input_type -> carbon.frontend.almanac.GetNextConfigDataRequest
	1,  // 20: carbon.frontend.almanac.AlmanacConfigService.LoadAlmanacConfig:input_type -> carbon.frontend.almanac.LoadAlmanacConfigRequest
	3,  // 21: carbon.frontend.almanac.AlmanacConfigService.SaveAlmanacConfig:input_type -> carbon.frontend.almanac.SaveAlmanacConfigRequest
	5,  // 22: carbon.frontend.almanac.AlmanacConfigService.SetActiveAlmanacConfig:input_type -> carbon.frontend.almanac.SetActiveAlmanacConfigRequest
	6,  // 23: carbon.frontend.almanac.AlmanacConfigService.DeleteAlmanacConfig:input_type -> carbon.frontend.almanac.DeleteAlmanacConfigRequest
	29, // 24: carbon.frontend.almanac.AlmanacConfigService.GetNextAlmanacConfig:input_type -> carbon.frontend.util.Timestamp
	8,  // 25: carbon.frontend.almanac.AlmanacConfigService.LoadDiscriminatorConfig:input_type -> carbon.frontend.almanac.LoadDiscriminatorConfigRequest
	10, // 26: carbon.frontend.almanac.AlmanacConfigService.SaveDiscriminatorConfig:input_type -> carbon.frontend.almanac.SaveDiscriminatorConfigRequest
	12, // 27: carbon.frontend.almanac.AlmanacConfigService.SetActiveDiscriminatorConfig:input_type -> carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest
	13, // 28: carbon.frontend.almanac.AlmanacConfigService.DeleteDiscriminatorConfig:input_type -> carbon.frontend.almanac.DeleteDiscriminatorConfigRequest
	29, // 29: carbon.frontend.almanac.AlmanacConfigService.GetNextDiscriminatorConfig:input_type -> carbon.frontend.util.Timestamp
	29, // 30: carbon.frontend.almanac.AlmanacConfigService.GetNextModelinatorConfig:input_type -> carbon.frontend.util.Timestamp
	16, // 31: carbon.frontend.almanac.AlmanacConfigService.SaveModelinatorConfig:input_type -> carbon.frontend.almanac.SaveModelinatorConfigRequest
	17, // 32: carbon.frontend.almanac.AlmanacConfigService.FetchModelinatorConfig:input_type -> carbon.frontend.almanac.FetchModelinatorConfigRequest
	19, // 33: carbon.frontend.almanac.AlmanacConfigService.ResetModelinatorConfig:input_type -> carbon.frontend.almanac.ResetModelinatorConfigRequest
	0,  // 34: carbon.frontend.almanac.AlmanacConfigService.GetConfigData:output_type -> carbon.frontend.almanac.GetConfigDataResponse
	21, // 35: carbon.frontend.almanac.AlmanacConfigService.GetNextConfigData:output_type -> carbon.frontend.almanac.GetNextConfigDataResponse
	2,  // 36: carbon.frontend.almanac.AlmanacConfigService.LoadAlmanacConfig:output_type -> carbon.frontend.almanac.LoadAlmanacConfigResponse
	4,  // 37: carbon.frontend.almanac.AlmanacConfigService.SaveAlmanacConfig:output_type -> carbon.frontend.almanac.SaveAlmanacConfigResponse
	32, // 38: carbon.frontend.almanac.AlmanacConfigService.SetActiveAlmanacConfig:output_type -> carbon.frontend.util.Empty
	32, // 39: carbon.frontend.almanac.AlmanacConfigService.DeleteAlmanacConfig:output_type -> carbon.frontend.util.Empty
	7,  // 40: carbon.frontend.almanac.AlmanacConfigService.GetNextAlmanacConfig:output_type -> carbon.frontend.almanac.GetNextAlmanacConfigResponse
	9,  // 41: carbon.frontend.almanac.AlmanacConfigService.LoadDiscriminatorConfig:output_type -> carbon.frontend.almanac.LoadDiscriminatorConfigResponse
	11, // 42: carbon.frontend.almanac.AlmanacConfigService.SaveDiscriminatorConfig:output_type -> carbon.frontend.almanac.SaveDiscriminatorConfigResponse
	32, // 43: carbon.frontend.almanac.AlmanacConfigService.SetActiveDiscriminatorConfig:output_type -> carbon.frontend.util.Empty
	32, // 44: carbon.frontend.almanac.AlmanacConfigService.DeleteDiscriminatorConfig:output_type -> carbon.frontend.util.Empty
	14, // 45: carbon.frontend.almanac.AlmanacConfigService.GetNextDiscriminatorConfig:output_type -> carbon.frontend.almanac.GetNextDiscriminatorConfigResponse
	15, // 46: carbon.frontend.almanac.AlmanacConfigService.GetNextModelinatorConfig:output_type -> carbon.frontend.almanac.GetNextModelinatorConfigResponse
	32, // 47: carbon.frontend.almanac.AlmanacConfigService.SaveModelinatorConfig:output_type -> carbon.frontend.util.Empty
	18, // 48: carbon.frontend.almanac.AlmanacConfigService.FetchModelinatorConfig:output_type -> carbon.frontend.almanac.FetchModelinatorConfigResponse
	32, // 49: carbon.frontend.almanac.AlmanacConfigService.ResetModelinatorConfig:output_type -> carbon.frontend.util.Empty
	34, // [34:50] is the sub-list for method output_type
	18, // [18:34] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_frontend_proto_almanac_proto_init() }
func file_frontend_proto_almanac_proto_init() {
	if File_frontend_proto_almanac_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_almanac_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadAlmanacConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadAlmanacConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAlmanacConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAlmanacConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveAlmanacConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAlmanacConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAlmanacConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadDiscriminatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadDiscriminatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveDiscriminatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveDiscriminatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveDiscriminatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDiscriminatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextDiscriminatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextModelinatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveModelinatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchModelinatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchModelinatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetModelinatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextConfigDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_almanac_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextConfigDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_almanac_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_almanac_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_almanac_proto_goTypes,
		DependencyIndexes: file_frontend_proto_almanac_proto_depIdxs,
		MessageInfos:      file_frontend_proto_almanac_proto_msgTypes,
	}.Build()
	File_frontend_proto_almanac_proto = out.File
	file_frontend_proto_almanac_proto_rawDesc = nil
	file_frontend_proto_almanac_proto_goTypes = nil
	file_frontend_proto_almanac_proto_depIdxs = nil
}

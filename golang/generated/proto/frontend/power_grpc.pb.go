// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/power.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PowerService_GetNextPowerStatus_FullMethodName             = "/carbon.frontend.power.PowerService/GetNextPowerStatus"
	PowerService_TurnOffDevice_FullMethodName                  = "/carbon.frontend.power.PowerService/TurnOffDevice"
	PowerService_TurnOnDevice_FullMethodName                   = "/carbon.frontend.power.PowerService/TurnOnDevice"
	PowerService_GetNextReaperAllHardwareStatus_FullMethodName = "/carbon.frontend.power.PowerService/GetNextReaperAllHardwareStatus"
	PowerService_GetNextReaperHardwareStatus_FullMethodName    = "/carbon.frontend.power.PowerService/GetNextReaperHardwareStatus"
	PowerService_SetReaperScannerPower_FullMethodName          = "/carbon.frontend.power.PowerService/SetReaperScannerPower"
	PowerService_SetReaperTargetPower_FullMethodName           = "/carbon.frontend.power.PowerService/SetReaperTargetPower"
	PowerService_SetReaperPredictCamPower_FullMethodName       = "/carbon.frontend.power.PowerService/SetReaperPredictCamPower"
	PowerService_SetReaperStrobeEnable_FullMethodName          = "/carbon.frontend.power.PowerService/SetReaperStrobeEnable"
	PowerService_SetReaperAllStrobesEnable_FullMethodName      = "/carbon.frontend.power.PowerService/SetReaperAllStrobesEnable"
	PowerService_SetReaperModulePcPower_FullMethodName         = "/carbon.frontend.power.PowerService/SetReaperModulePcPower"
	PowerService_SetReaperModuleLaserPower_FullMethodName      = "/carbon.frontend.power.PowerService/SetReaperModuleLaserPower"
)

// PowerServiceClient is the client API for PowerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PowerServiceClient interface {
	GetNextPowerStatus(ctx context.Context, in *PowerStatusRequest, opts ...grpc.CallOption) (*PowerStatusResponse, error)
	TurnOffDevice(ctx context.Context, in *RelayRequest, opts ...grpc.CallOption) (*RelayResponse, error)
	TurnOnDevice(ctx context.Context, in *RelayRequest, opts ...grpc.CallOption) (*RelayResponse, error)
	GetNextReaperAllHardwareStatus(ctx context.Context, in *GetNextReaperAllHardwareStatusRequest, opts ...grpc.CallOption) (*GetNextReaperAllHardwareStatusResponse, error)
	GetNextReaperHardwareStatus(ctx context.Context, in *GetNextReaperHardwareStatusRequest, opts ...grpc.CallOption) (*GetNextReaperHardwareStatusResponse, error)
	SetReaperScannerPower(ctx context.Context, in *SetReaperScannerPowerRequest, opts ...grpc.CallOption) (*SetReaperScannerPowerResponse, error)
	SetReaperTargetPower(ctx context.Context, in *SetReaperTargetPowerRequest, opts ...grpc.CallOption) (*SetReaperTargetPowerResponse, error)
	SetReaperPredictCamPower(ctx context.Context, in *SetReaperPredictCamPowerRequest, opts ...grpc.CallOption) (*SetReaperPredictCamPowerResponse, error)
	SetReaperStrobeEnable(ctx context.Context, in *SetReaperStrobeEnableRequest, opts ...grpc.CallOption) (*SetReaperStrobeEnableResponse, error)
	SetReaperAllStrobesEnable(ctx context.Context, in *SetReaperAllStrobesEnableRequest, opts ...grpc.CallOption) (*SetReaperAllStrobesEnableResponse, error)
	SetReaperModulePcPower(ctx context.Context, in *SetReaperModulePcPowerRequest, opts ...grpc.CallOption) (*SetReaperModulePcPowerResponse, error)
	SetReaperModuleLaserPower(ctx context.Context, in *SetReaperModuleLaserPowerRequest, opts ...grpc.CallOption) (*SetReaperModuleLaserPowerResponse, error)
}

type powerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPowerServiceClient(cc grpc.ClientConnInterface) PowerServiceClient {
	return &powerServiceClient{cc}
}

func (c *powerServiceClient) GetNextPowerStatus(ctx context.Context, in *PowerStatusRequest, opts ...grpc.CallOption) (*PowerStatusResponse, error) {
	out := new(PowerStatusResponse)
	err := c.cc.Invoke(ctx, PowerService_GetNextPowerStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) TurnOffDevice(ctx context.Context, in *RelayRequest, opts ...grpc.CallOption) (*RelayResponse, error) {
	out := new(RelayResponse)
	err := c.cc.Invoke(ctx, PowerService_TurnOffDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) TurnOnDevice(ctx context.Context, in *RelayRequest, opts ...grpc.CallOption) (*RelayResponse, error) {
	out := new(RelayResponse)
	err := c.cc.Invoke(ctx, PowerService_TurnOnDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) GetNextReaperAllHardwareStatus(ctx context.Context, in *GetNextReaperAllHardwareStatusRequest, opts ...grpc.CallOption) (*GetNextReaperAllHardwareStatusResponse, error) {
	out := new(GetNextReaperAllHardwareStatusResponse)
	err := c.cc.Invoke(ctx, PowerService_GetNextReaperAllHardwareStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) GetNextReaperHardwareStatus(ctx context.Context, in *GetNextReaperHardwareStatusRequest, opts ...grpc.CallOption) (*GetNextReaperHardwareStatusResponse, error) {
	out := new(GetNextReaperHardwareStatusResponse)
	err := c.cc.Invoke(ctx, PowerService_GetNextReaperHardwareStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperScannerPower(ctx context.Context, in *SetReaperScannerPowerRequest, opts ...grpc.CallOption) (*SetReaperScannerPowerResponse, error) {
	out := new(SetReaperScannerPowerResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperScannerPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperTargetPower(ctx context.Context, in *SetReaperTargetPowerRequest, opts ...grpc.CallOption) (*SetReaperTargetPowerResponse, error) {
	out := new(SetReaperTargetPowerResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperTargetPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperPredictCamPower(ctx context.Context, in *SetReaperPredictCamPowerRequest, opts ...grpc.CallOption) (*SetReaperPredictCamPowerResponse, error) {
	out := new(SetReaperPredictCamPowerResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperPredictCamPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperStrobeEnable(ctx context.Context, in *SetReaperStrobeEnableRequest, opts ...grpc.CallOption) (*SetReaperStrobeEnableResponse, error) {
	out := new(SetReaperStrobeEnableResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperStrobeEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperAllStrobesEnable(ctx context.Context, in *SetReaperAllStrobesEnableRequest, opts ...grpc.CallOption) (*SetReaperAllStrobesEnableResponse, error) {
	out := new(SetReaperAllStrobesEnableResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperAllStrobesEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperModulePcPower(ctx context.Context, in *SetReaperModulePcPowerRequest, opts ...grpc.CallOption) (*SetReaperModulePcPowerResponse, error) {
	out := new(SetReaperModulePcPowerResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperModulePcPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *powerServiceClient) SetReaperModuleLaserPower(ctx context.Context, in *SetReaperModuleLaserPowerRequest, opts ...grpc.CallOption) (*SetReaperModuleLaserPowerResponse, error) {
	out := new(SetReaperModuleLaserPowerResponse)
	err := c.cc.Invoke(ctx, PowerService_SetReaperModuleLaserPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PowerServiceServer is the server API for PowerService service.
// All implementations must embed UnimplementedPowerServiceServer
// for forward compatibility
type PowerServiceServer interface {
	GetNextPowerStatus(context.Context, *PowerStatusRequest) (*PowerStatusResponse, error)
	TurnOffDevice(context.Context, *RelayRequest) (*RelayResponse, error)
	TurnOnDevice(context.Context, *RelayRequest) (*RelayResponse, error)
	GetNextReaperAllHardwareStatus(context.Context, *GetNextReaperAllHardwareStatusRequest) (*GetNextReaperAllHardwareStatusResponse, error)
	GetNextReaperHardwareStatus(context.Context, *GetNextReaperHardwareStatusRequest) (*GetNextReaperHardwareStatusResponse, error)
	SetReaperScannerPower(context.Context, *SetReaperScannerPowerRequest) (*SetReaperScannerPowerResponse, error)
	SetReaperTargetPower(context.Context, *SetReaperTargetPowerRequest) (*SetReaperTargetPowerResponse, error)
	SetReaperPredictCamPower(context.Context, *SetReaperPredictCamPowerRequest) (*SetReaperPredictCamPowerResponse, error)
	SetReaperStrobeEnable(context.Context, *SetReaperStrobeEnableRequest) (*SetReaperStrobeEnableResponse, error)
	SetReaperAllStrobesEnable(context.Context, *SetReaperAllStrobesEnableRequest) (*SetReaperAllStrobesEnableResponse, error)
	SetReaperModulePcPower(context.Context, *SetReaperModulePcPowerRequest) (*SetReaperModulePcPowerResponse, error)
	SetReaperModuleLaserPower(context.Context, *SetReaperModuleLaserPowerRequest) (*SetReaperModuleLaserPowerResponse, error)
	mustEmbedUnimplementedPowerServiceServer()
}

// UnimplementedPowerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPowerServiceServer struct {
}

func (UnimplementedPowerServiceServer) GetNextPowerStatus(context.Context, *PowerStatusRequest) (*PowerStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextPowerStatus not implemented")
}
func (UnimplementedPowerServiceServer) TurnOffDevice(context.Context, *RelayRequest) (*RelayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TurnOffDevice not implemented")
}
func (UnimplementedPowerServiceServer) TurnOnDevice(context.Context, *RelayRequest) (*RelayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TurnOnDevice not implemented")
}
func (UnimplementedPowerServiceServer) GetNextReaperAllHardwareStatus(context.Context, *GetNextReaperAllHardwareStatusRequest) (*GetNextReaperAllHardwareStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextReaperAllHardwareStatus not implemented")
}
func (UnimplementedPowerServiceServer) GetNextReaperHardwareStatus(context.Context, *GetNextReaperHardwareStatusRequest) (*GetNextReaperHardwareStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextReaperHardwareStatus not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperScannerPower(context.Context, *SetReaperScannerPowerRequest) (*SetReaperScannerPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperScannerPower not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperTargetPower(context.Context, *SetReaperTargetPowerRequest) (*SetReaperTargetPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperTargetPower not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperPredictCamPower(context.Context, *SetReaperPredictCamPowerRequest) (*SetReaperPredictCamPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperPredictCamPower not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperStrobeEnable(context.Context, *SetReaperStrobeEnableRequest) (*SetReaperStrobeEnableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperStrobeEnable not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperAllStrobesEnable(context.Context, *SetReaperAllStrobesEnableRequest) (*SetReaperAllStrobesEnableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperAllStrobesEnable not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperModulePcPower(context.Context, *SetReaperModulePcPowerRequest) (*SetReaperModulePcPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperModulePcPower not implemented")
}
func (UnimplementedPowerServiceServer) SetReaperModuleLaserPower(context.Context, *SetReaperModuleLaserPowerRequest) (*SetReaperModuleLaserPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperModuleLaserPower not implemented")
}
func (UnimplementedPowerServiceServer) mustEmbedUnimplementedPowerServiceServer() {}

// UnsafePowerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PowerServiceServer will
// result in compilation errors.
type UnsafePowerServiceServer interface {
	mustEmbedUnimplementedPowerServiceServer()
}

func RegisterPowerServiceServer(s grpc.ServiceRegistrar, srv PowerServiceServer) {
	s.RegisterService(&PowerService_ServiceDesc, srv)
}

func _PowerService_GetNextPowerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PowerStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).GetNextPowerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_GetNextPowerStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).GetNextPowerStatus(ctx, req.(*PowerStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_TurnOffDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).TurnOffDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_TurnOffDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).TurnOffDevice(ctx, req.(*RelayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_TurnOnDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).TurnOnDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_TurnOnDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).TurnOnDevice(ctx, req.(*RelayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_GetNextReaperAllHardwareStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextReaperAllHardwareStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).GetNextReaperAllHardwareStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_GetNextReaperAllHardwareStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).GetNextReaperAllHardwareStatus(ctx, req.(*GetNextReaperAllHardwareStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_GetNextReaperHardwareStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextReaperHardwareStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).GetNextReaperHardwareStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_GetNextReaperHardwareStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).GetNextReaperHardwareStatus(ctx, req.(*GetNextReaperHardwareStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperScannerPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperScannerPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperScannerPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperScannerPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperScannerPower(ctx, req.(*SetReaperScannerPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperTargetPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperTargetPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperTargetPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperTargetPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperTargetPower(ctx, req.(*SetReaperTargetPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperPredictCamPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperPredictCamPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperPredictCamPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperPredictCamPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperPredictCamPower(ctx, req.(*SetReaperPredictCamPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperStrobeEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperStrobeEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperStrobeEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperStrobeEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperStrobeEnable(ctx, req.(*SetReaperStrobeEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperAllStrobesEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperAllStrobesEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperAllStrobesEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperAllStrobesEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperAllStrobesEnable(ctx, req.(*SetReaperAllStrobesEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperModulePcPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperModulePcPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperModulePcPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperModulePcPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperModulePcPower(ctx, req.(*SetReaperModulePcPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PowerService_SetReaperModuleLaserPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperModuleLaserPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PowerServiceServer).SetReaperModuleLaserPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PowerService_SetReaperModuleLaserPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PowerServiceServer).SetReaperModuleLaserPower(ctx, req.(*SetReaperModuleLaserPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PowerService_ServiceDesc is the grpc.ServiceDesc for PowerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PowerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.power.PowerService",
	HandlerType: (*PowerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextPowerStatus",
			Handler:    _PowerService_GetNextPowerStatus_Handler,
		},
		{
			MethodName: "TurnOffDevice",
			Handler:    _PowerService_TurnOffDevice_Handler,
		},
		{
			MethodName: "TurnOnDevice",
			Handler:    _PowerService_TurnOnDevice_Handler,
		},
		{
			MethodName: "GetNextReaperAllHardwareStatus",
			Handler:    _PowerService_GetNextReaperAllHardwareStatus_Handler,
		},
		{
			MethodName: "GetNextReaperHardwareStatus",
			Handler:    _PowerService_GetNextReaperHardwareStatus_Handler,
		},
		{
			MethodName: "SetReaperScannerPower",
			Handler:    _PowerService_SetReaperScannerPower_Handler,
		},
		{
			MethodName: "SetReaperTargetPower",
			Handler:    _PowerService_SetReaperTargetPower_Handler,
		},
		{
			MethodName: "SetReaperPredictCamPower",
			Handler:    _PowerService_SetReaperPredictCamPower_Handler,
		},
		{
			MethodName: "SetReaperStrobeEnable",
			Handler:    _PowerService_SetReaperStrobeEnable_Handler,
		},
		{
			MethodName: "SetReaperAllStrobesEnable",
			Handler:    _PowerService_SetReaperAllStrobesEnable_Handler,
		},
		{
			MethodName: "SetReaperModulePcPower",
			Handler:    _PowerService_SetReaperModulePcPower_Handler,
		},
		{
			MethodName: "SetReaperModuleLaserPower",
			Handler:    _PowerService_SetReaperModuleLaserPower_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/power.proto",
}

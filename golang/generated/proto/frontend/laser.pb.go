// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/laser.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LaserDescriptor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowNumber uint32 `protobuf:"varint,1,opt,name=row_number,json=rowNumber,proto3" json:"row_number,omitempty"`
	LaserId   uint32 `protobuf:"varint,2,opt,name=laser_id,json=laserId,proto3" json:"laser_id,omitempty"`
	CameraId  string `protobuf:"bytes,3,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	Serial    string `protobuf:"bytes,4,opt,name=serial,proto3" json:"serial,omitempty"`
}

func (x *LaserDescriptor) Reset() {
	*x = LaserDescriptor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_laser_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserDescriptor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserDescriptor) ProtoMessage() {}

func (x *LaserDescriptor) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_laser_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserDescriptor.ProtoReflect.Descriptor instead.
func (*LaserDescriptor) Descriptor() ([]byte, []int) {
	return file_frontend_proto_laser_proto_rawDescGZIP(), []int{0}
}

func (x *LaserDescriptor) GetRowNumber() uint32 {
	if x != nil {
		return x.RowNumber
	}
	return 0
}

func (x *LaserDescriptor) GetLaserId() uint32 {
	if x != nil {
		return x.LaserId
	}
	return 0
}

func (x *LaserDescriptor) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *LaserDescriptor) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

type LaserState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LaserDescriptor    *LaserDescriptor `protobuf:"bytes,1,opt,name=laser_descriptor,json=laserDescriptor,proto3" json:"laser_descriptor,omitempty"`
	Firing             bool             `protobuf:"varint,2,opt,name=firing,proto3" json:"firing,omitempty"`
	Enabled            bool             `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Error              bool             `protobuf:"varint,4,opt,name=error,proto3" json:"error,omitempty"`
	TotalFireCount     int64            `protobuf:"varint,5,opt,name=total_fire_count,json=totalFireCount,proto3" json:"total_fire_count,omitempty"`
	TotalFireTimeMs    int64            `protobuf:"varint,6,opt,name=total_fire_time_ms,json=totalFireTimeMs,proto3" json:"total_fire_time_ms,omitempty"`
	DeltaTemp          float32          `protobuf:"fixed32,7,opt,name=delta_temp,json=deltaTemp,proto3" json:"delta_temp,omitempty"`
	Current            float32          `protobuf:"fixed32,9,opt,name=current,proto3" json:"current,omitempty"`
	TargetTrajectoryId uint32           `protobuf:"varint,10,opt,name=target_trajectory_id,json=targetTrajectoryId,proto3" json:"target_trajectory_id,omitempty"`
	LifetimeSec        uint64           `protobuf:"varint,11,opt,name=lifetime_sec,json=lifetimeSec,proto3" json:"lifetime_sec,omitempty"`
	PowerLevel         float32          `protobuf:"fixed32,12,opt,name=power_level,json=powerLevel,proto3" json:"power_level,omitempty"`
	InstalledAt        int64            `protobuf:"varint,13,opt,name=installed_at,json=installedAt,proto3" json:"installed_at,omitempty"`
}

func (x *LaserState) Reset() {
	*x = LaserState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_laser_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserState) ProtoMessage() {}

func (x *LaserState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_laser_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserState.ProtoReflect.Descriptor instead.
func (*LaserState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_laser_proto_rawDescGZIP(), []int{1}
}

func (x *LaserState) GetLaserDescriptor() *LaserDescriptor {
	if x != nil {
		return x.LaserDescriptor
	}
	return nil
}

func (x *LaserState) GetFiring() bool {
	if x != nil {
		return x.Firing
	}
	return false
}

func (x *LaserState) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *LaserState) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *LaserState) GetTotalFireCount() int64 {
	if x != nil {
		return x.TotalFireCount
	}
	return 0
}

func (x *LaserState) GetTotalFireTimeMs() int64 {
	if x != nil {
		return x.TotalFireTimeMs
	}
	return 0
}

func (x *LaserState) GetDeltaTemp() float32 {
	if x != nil {
		return x.DeltaTemp
	}
	return 0
}

func (x *LaserState) GetCurrent() float32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *LaserState) GetTargetTrajectoryId() uint32 {
	if x != nil {
		return x.TargetTrajectoryId
	}
	return 0
}

func (x *LaserState) GetLifetimeSec() uint64 {
	if x != nil {
		return x.LifetimeSec
	}
	return 0
}

func (x *LaserState) GetPowerLevel() float32 {
	if x != nil {
		return x.PowerLevel
	}
	return 0
}

func (x *LaserState) GetInstalledAt() int64 {
	if x != nil {
		return x.InstalledAt
	}
	return 0
}

type LaserStateList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts     *Timestamp    `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Lasers []*LaserState `protobuf:"bytes,2,rep,name=lasers,proto3" json:"lasers,omitempty"`
}

func (x *LaserStateList) Reset() {
	*x = LaserStateList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_laser_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserStateList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserStateList) ProtoMessage() {}

func (x *LaserStateList) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_laser_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserStateList.ProtoReflect.Descriptor instead.
func (*LaserStateList) Descriptor() ([]byte, []int) {
	return file_frontend_proto_laser_proto_rawDescGZIP(), []int{2}
}

func (x *LaserStateList) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *LaserStateList) GetLasers() []*LaserState {
	if x != nil {
		return x.Lasers
	}
	return nil
}

type RowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowNumber uint32 `protobuf:"varint,1,opt,name=row_number,json=rowNumber,proto3" json:"row_number,omitempty"`
}

func (x *RowRequest) Reset() {
	*x = RowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_laser_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowRequest) ProtoMessage() {}

func (x *RowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_laser_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowRequest.ProtoReflect.Descriptor instead.
func (*RowRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_laser_proto_rawDescGZIP(), []int{3}
}

func (x *RowRequest) GetRowNumber() uint32 {
	if x != nil {
		return x.RowNumber
	}
	return 0
}

type SetLaserPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LaserDescriptor *LaserDescriptor `protobuf:"bytes,1,opt,name=laser_descriptor,json=laserDescriptor,proto3" json:"laser_descriptor,omitempty"`
	PowerLevel      float32          `protobuf:"fixed32,2,opt,name=power_level,json=powerLevel,proto3" json:"power_level,omitempty"`
}

func (x *SetLaserPowerRequest) Reset() {
	*x = SetLaserPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_laser_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLaserPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLaserPowerRequest) ProtoMessage() {}

func (x *SetLaserPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_laser_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLaserPowerRequest.ProtoReflect.Descriptor instead.
func (*SetLaserPowerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_laser_proto_rawDescGZIP(), []int{4}
}

func (x *SetLaserPowerRequest) GetLaserDescriptor() *LaserDescriptor {
	if x != nil {
		return x.LaserDescriptor
	}
	return nil
}

func (x *SetLaserPowerRequest) GetPowerLevel() float32 {
	if x != nil {
		return x.PowerLevel
	}
	return 0
}

type FixLaserMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LaserDescriptor *LaserDescriptor `protobuf:"bytes,1,opt,name=laser_descriptor,json=laserDescriptor,proto3" json:"laser_descriptor,omitempty"`
	TotalFireCount  int64            `protobuf:"varint,2,opt,name=total_fire_count,json=totalFireCount,proto3" json:"total_fire_count,omitempty"`
	TotalFireTimeMs int64            `protobuf:"varint,3,opt,name=total_fire_time_ms,json=totalFireTimeMs,proto3" json:"total_fire_time_ms,omitempty"`
	LifetimeSec     uint64           `protobuf:"varint,4,opt,name=lifetime_sec,json=lifetimeSec,proto3" json:"lifetime_sec,omitempty"`
}

func (x *FixLaserMetricsRequest) Reset() {
	*x = FixLaserMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_laser_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FixLaserMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixLaserMetricsRequest) ProtoMessage() {}

func (x *FixLaserMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_laser_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixLaserMetricsRequest.ProtoReflect.Descriptor instead.
func (*FixLaserMetricsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_laser_proto_rawDescGZIP(), []int{5}
}

func (x *FixLaserMetricsRequest) GetLaserDescriptor() *LaserDescriptor {
	if x != nil {
		return x.LaserDescriptor
	}
	return nil
}

func (x *FixLaserMetricsRequest) GetTotalFireCount() int64 {
	if x != nil {
		return x.TotalFireCount
	}
	return 0
}

func (x *FixLaserMetricsRequest) GetTotalFireTimeMs() int64 {
	if x != nil {
		return x.TotalFireTimeMs
	}
	return 0
}

func (x *FixLaserMetricsRequest) GetLifetimeSec() uint64 {
	if x != nil {
		return x.LifetimeSec
	}
	return 0
}

var File_frontend_proto_laser_proto protoreflect.FileDescriptor

var file_frontend_proto_laser_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x1a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x01, 0x0a, 0x0f,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0xd0,
	0x03, 0x0a, 0x0a, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x51, 0x0a,
	0x10, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x52,
	0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x72, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x69,
	0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x7c, 0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x2b, 0x0a, 0x0a, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x8a, 0x01, 0x0a,
	0x14, 0x53, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0xe5, 0x01, 0x0a, 0x16, 0x46, 0x69,
	0x78, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x66, 0x69, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x72, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x65,
	0x63, 0x32, 0xc8, 0x05, 0x0a, 0x0c, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x51, 0x0a, 0x09, 0x46, 0x69, 0x72, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x12,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x28, 0x01, 0x12, 0x5b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x25, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x59, 0x0a, 0x12, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4b, 0x0a,
	0x09, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x21, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x2e, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x0a, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x2e, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x58, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x26, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x5d, 0x0a, 0x0f, 0x46, 0x69, 0x78, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x46, 0x69,
	0x78, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x59, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_laser_proto_rawDescOnce sync.Once
	file_frontend_proto_laser_proto_rawDescData = file_frontend_proto_laser_proto_rawDesc
)

func file_frontend_proto_laser_proto_rawDescGZIP() []byte {
	file_frontend_proto_laser_proto_rawDescOnce.Do(func() {
		file_frontend_proto_laser_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_laser_proto_rawDescData)
	})
	return file_frontend_proto_laser_proto_rawDescData
}

var file_frontend_proto_laser_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_frontend_proto_laser_proto_goTypes = []interface{}{
	(*LaserDescriptor)(nil),        // 0: carbon.frontend.laser.LaserDescriptor
	(*LaserState)(nil),             // 1: carbon.frontend.laser.LaserState
	(*LaserStateList)(nil),         // 2: carbon.frontend.laser.LaserStateList
	(*RowRequest)(nil),             // 3: carbon.frontend.laser.RowRequest
	(*SetLaserPowerRequest)(nil),   // 4: carbon.frontend.laser.SetLaserPowerRequest
	(*FixLaserMetricsRequest)(nil), // 5: carbon.frontend.laser.FixLaserMetricsRequest
	(*Timestamp)(nil),              // 6: carbon.frontend.util.Timestamp
	(*CameraRequest)(nil),          // 7: carbon.frontend.camera.CameraRequest
	(*Empty)(nil),                  // 8: carbon.frontend.util.Empty
}
var file_frontend_proto_laser_proto_depIdxs = []int32{
	0,  // 0: carbon.frontend.laser.LaserState.laser_descriptor:type_name -> carbon.frontend.laser.LaserDescriptor
	6,  // 1: carbon.frontend.laser.LaserStateList.ts:type_name -> carbon.frontend.util.Timestamp
	1,  // 2: carbon.frontend.laser.LaserStateList.lasers:type_name -> carbon.frontend.laser.LaserState
	0,  // 3: carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor:type_name -> carbon.frontend.laser.LaserDescriptor
	0,  // 4: carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor:type_name -> carbon.frontend.laser.LaserDescriptor
	7,  // 5: carbon.frontend.laser.LaserService.FireLaser:input_type -> carbon.frontend.camera.CameraRequest
	6,  // 6: carbon.frontend.laser.LaserService.GetNextLaserState:input_type -> carbon.frontend.util.Timestamp
	0,  // 7: carbon.frontend.laser.LaserService.ToggleLaserEnabled:input_type -> carbon.frontend.laser.LaserDescriptor
	3,  // 8: carbon.frontend.laser.LaserService.EnableRow:input_type -> carbon.frontend.laser.RowRequest
	3,  // 9: carbon.frontend.laser.LaserService.DisableRow:input_type -> carbon.frontend.laser.RowRequest
	0,  // 10: carbon.frontend.laser.LaserService.ResetLaserMetrics:input_type -> carbon.frontend.laser.LaserDescriptor
	5,  // 11: carbon.frontend.laser.LaserService.FixLaserMetrics:input_type -> carbon.frontend.laser.FixLaserMetricsRequest
	4,  // 12: carbon.frontend.laser.LaserService.SetLaserPower:input_type -> carbon.frontend.laser.SetLaserPowerRequest
	8,  // 13: carbon.frontend.laser.LaserService.FireLaser:output_type -> carbon.frontend.util.Empty
	2,  // 14: carbon.frontend.laser.LaserService.GetNextLaserState:output_type -> carbon.frontend.laser.LaserStateList
	8,  // 15: carbon.frontend.laser.LaserService.ToggleLaserEnabled:output_type -> carbon.frontend.util.Empty
	8,  // 16: carbon.frontend.laser.LaserService.EnableRow:output_type -> carbon.frontend.util.Empty
	8,  // 17: carbon.frontend.laser.LaserService.DisableRow:output_type -> carbon.frontend.util.Empty
	8,  // 18: carbon.frontend.laser.LaserService.ResetLaserMetrics:output_type -> carbon.frontend.util.Empty
	8,  // 19: carbon.frontend.laser.LaserService.FixLaserMetrics:output_type -> carbon.frontend.util.Empty
	8,  // 20: carbon.frontend.laser.LaserService.SetLaserPower:output_type -> carbon.frontend.util.Empty
	13, // [13:21] is the sub-list for method output_type
	5,  // [5:13] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_frontend_proto_laser_proto_init() }
func file_frontend_proto_laser_proto_init() {
	if File_frontend_proto_laser_proto != nil {
		return
	}
	file_frontend_proto_camera_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_laser_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserDescriptor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_laser_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_laser_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserStateList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_laser_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_laser_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLaserPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_laser_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FixLaserMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_laser_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_laser_proto_goTypes,
		DependencyIndexes: file_frontend_proto_laser_proto_depIdxs,
		MessageInfos:      file_frontend_proto_laser_proto_msgTypes,
	}.Build()
	File_frontend_proto_laser_proto = out.File
	file_frontend_proto_laser_proto_rawDesc = nil
	file_frontend_proto_laser_proto_goTypes = nil
	file_frontend_proto_laser_proto_depIdxs = nil
}

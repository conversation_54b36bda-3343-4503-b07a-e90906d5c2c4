// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/module.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModuleAssignmentService_GetNextModulesList_FullMethodName        = "/carbon.frontend.module.ModuleAssignmentService/GetNextModulesList"
	ModuleAssignmentService_GetNextActiveModules_FullMethodName      = "/carbon.frontend.module.ModuleAssignmentService/GetNextActiveModules"
	ModuleAssignmentService_IdentifyModule_FullMethodName            = "/carbon.frontend.module.ModuleAssignmentService/IdentifyModule"
	ModuleAssignmentService_AssignModule_FullMethodName              = "/carbon.frontend.module.ModuleAssignmentService/AssignModule"
	ModuleAssignmentService_ClearModuleAssignment_FullMethodName     = "/carbon.frontend.module.ModuleAssignmentService/ClearModuleAssignment"
	ModuleAssignmentService_SetModuleSerial_FullMethodName           = "/carbon.frontend.module.ModuleAssignmentService/SetModuleSerial"
	ModuleAssignmentService_GetPresetsList_FullMethodName            = "/carbon.frontend.module.ModuleAssignmentService/GetPresetsList"
	ModuleAssignmentService_GetCurrentRobotDefinition_FullMethodName = "/carbon.frontend.module.ModuleAssignmentService/GetCurrentRobotDefinition"
	ModuleAssignmentService_SetCurrentRobotDefinition_FullMethodName = "/carbon.frontend.module.ModuleAssignmentService/SetCurrentRobotDefinition"
)

// ModuleAssignmentServiceClient is the client API for ModuleAssignmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModuleAssignmentServiceClient interface {
	// Module Identity
	GetNextModulesList(ctx context.Context, in *GetNextModulesListRequest, opts ...grpc.CallOption) (*GetNextModulesListResponse, error)
	GetNextActiveModules(ctx context.Context, in *GetNextActiveModulesRequest, opts ...grpc.CallOption) (*GetNextActiveModulesResponse, error)
	IdentifyModule(ctx context.Context, in *IdentifyModuleRequest, opts ...grpc.CallOption) (*Empty, error)
	AssignModule(ctx context.Context, in *AssignModuleRequest, opts ...grpc.CallOption) (*Empty, error)
	ClearModuleAssignment(ctx context.Context, in *ClearModuleAssignmentRequest, opts ...grpc.CallOption) (*Empty, error)
	SetModuleSerial(ctx context.Context, in *SetModuleSerialRequest, opts ...grpc.CallOption) (*Empty, error)
	// Robot Definition
	GetPresetsList(ctx context.Context, in *GetPresetsListRequest, opts ...grpc.CallOption) (*GetPresetsListResponse, error)
	GetCurrentRobotDefinition(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetCurrentRobotDefinitionResponse, error)
	SetCurrentRobotDefinition(ctx context.Context, in *SetCurrentRobotDefinitionRequest, opts ...grpc.CallOption) (*Empty, error)
}

type moduleAssignmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModuleAssignmentServiceClient(cc grpc.ClientConnInterface) ModuleAssignmentServiceClient {
	return &moduleAssignmentServiceClient{cc}
}

func (c *moduleAssignmentServiceClient) GetNextModulesList(ctx context.Context, in *GetNextModulesListRequest, opts ...grpc.CallOption) (*GetNextModulesListResponse, error) {
	out := new(GetNextModulesListResponse)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_GetNextModulesList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) GetNextActiveModules(ctx context.Context, in *GetNextActiveModulesRequest, opts ...grpc.CallOption) (*GetNextActiveModulesResponse, error) {
	out := new(GetNextActiveModulesResponse)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_GetNextActiveModules_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) IdentifyModule(ctx context.Context, in *IdentifyModuleRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_IdentifyModule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) AssignModule(ctx context.Context, in *AssignModuleRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_AssignModule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) ClearModuleAssignment(ctx context.Context, in *ClearModuleAssignmentRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_ClearModuleAssignment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) SetModuleSerial(ctx context.Context, in *SetModuleSerialRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_SetModuleSerial_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) GetPresetsList(ctx context.Context, in *GetPresetsListRequest, opts ...grpc.CallOption) (*GetPresetsListResponse, error) {
	out := new(GetPresetsListResponse)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_GetPresetsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) GetCurrentRobotDefinition(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetCurrentRobotDefinitionResponse, error) {
	out := new(GetCurrentRobotDefinitionResponse)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_GetCurrentRobotDefinition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleAssignmentServiceClient) SetCurrentRobotDefinition(ctx context.Context, in *SetCurrentRobotDefinitionRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleAssignmentService_SetCurrentRobotDefinition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModuleAssignmentServiceServer is the server API for ModuleAssignmentService service.
// All implementations must embed UnimplementedModuleAssignmentServiceServer
// for forward compatibility
type ModuleAssignmentServiceServer interface {
	// Module Identity
	GetNextModulesList(context.Context, *GetNextModulesListRequest) (*GetNextModulesListResponse, error)
	GetNextActiveModules(context.Context, *GetNextActiveModulesRequest) (*GetNextActiveModulesResponse, error)
	IdentifyModule(context.Context, *IdentifyModuleRequest) (*Empty, error)
	AssignModule(context.Context, *AssignModuleRequest) (*Empty, error)
	ClearModuleAssignment(context.Context, *ClearModuleAssignmentRequest) (*Empty, error)
	SetModuleSerial(context.Context, *SetModuleSerialRequest) (*Empty, error)
	// Robot Definition
	GetPresetsList(context.Context, *GetPresetsListRequest) (*GetPresetsListResponse, error)
	GetCurrentRobotDefinition(context.Context, *Empty) (*GetCurrentRobotDefinitionResponse, error)
	SetCurrentRobotDefinition(context.Context, *SetCurrentRobotDefinitionRequest) (*Empty, error)
	mustEmbedUnimplementedModuleAssignmentServiceServer()
}

// UnimplementedModuleAssignmentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModuleAssignmentServiceServer struct {
}

func (UnimplementedModuleAssignmentServiceServer) GetNextModulesList(context.Context, *GetNextModulesListRequest) (*GetNextModulesListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextModulesList not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) GetNextActiveModules(context.Context, *GetNextActiveModulesRequest) (*GetNextActiveModulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActiveModules not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) IdentifyModule(context.Context, *IdentifyModuleRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IdentifyModule not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) AssignModule(context.Context, *AssignModuleRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignModule not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) ClearModuleAssignment(context.Context, *ClearModuleAssignmentRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearModuleAssignment not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) SetModuleSerial(context.Context, *SetModuleSerialRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetModuleSerial not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) GetPresetsList(context.Context, *GetPresetsListRequest) (*GetPresetsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPresetsList not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) GetCurrentRobotDefinition(context.Context, *Empty) (*GetCurrentRobotDefinitionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrentRobotDefinition not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) SetCurrentRobotDefinition(context.Context, *SetCurrentRobotDefinitionRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCurrentRobotDefinition not implemented")
}
func (UnimplementedModuleAssignmentServiceServer) mustEmbedUnimplementedModuleAssignmentServiceServer() {
}

// UnsafeModuleAssignmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModuleAssignmentServiceServer will
// result in compilation errors.
type UnsafeModuleAssignmentServiceServer interface {
	mustEmbedUnimplementedModuleAssignmentServiceServer()
}

func RegisterModuleAssignmentServiceServer(s grpc.ServiceRegistrar, srv ModuleAssignmentServiceServer) {
	s.RegisterService(&ModuleAssignmentService_ServiceDesc, srv)
}

func _ModuleAssignmentService_GetNextModulesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextModulesListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).GetNextModulesList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_GetNextModulesList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).GetNextModulesList(ctx, req.(*GetNextModulesListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_GetNextActiveModules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextActiveModulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).GetNextActiveModules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_GetNextActiveModules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).GetNextActiveModules(ctx, req.(*GetNextActiveModulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_IdentifyModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdentifyModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).IdentifyModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_IdentifyModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).IdentifyModule(ctx, req.(*IdentifyModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_AssignModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).AssignModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_AssignModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).AssignModule(ctx, req.(*AssignModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_ClearModuleAssignment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearModuleAssignmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).ClearModuleAssignment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_ClearModuleAssignment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).ClearModuleAssignment(ctx, req.(*ClearModuleAssignmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_SetModuleSerial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetModuleSerialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).SetModuleSerial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_SetModuleSerial_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).SetModuleSerial(ctx, req.(*SetModuleSerialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_GetPresetsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresetsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).GetPresetsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_GetPresetsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).GetPresetsList(ctx, req.(*GetPresetsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_GetCurrentRobotDefinition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).GetCurrentRobotDefinition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_GetCurrentRobotDefinition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).GetCurrentRobotDefinition(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleAssignmentService_SetCurrentRobotDefinition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCurrentRobotDefinitionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleAssignmentServiceServer).SetCurrentRobotDefinition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleAssignmentService_SetCurrentRobotDefinition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleAssignmentServiceServer).SetCurrentRobotDefinition(ctx, req.(*SetCurrentRobotDefinitionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ModuleAssignmentService_ServiceDesc is the grpc.ServiceDesc for ModuleAssignmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModuleAssignmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.module.ModuleAssignmentService",
	HandlerType: (*ModuleAssignmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextModulesList",
			Handler:    _ModuleAssignmentService_GetNextModulesList_Handler,
		},
		{
			MethodName: "GetNextActiveModules",
			Handler:    _ModuleAssignmentService_GetNextActiveModules_Handler,
		},
		{
			MethodName: "IdentifyModule",
			Handler:    _ModuleAssignmentService_IdentifyModule_Handler,
		},
		{
			MethodName: "AssignModule",
			Handler:    _ModuleAssignmentService_AssignModule_Handler,
		},
		{
			MethodName: "ClearModuleAssignment",
			Handler:    _ModuleAssignmentService_ClearModuleAssignment_Handler,
		},
		{
			MethodName: "SetModuleSerial",
			Handler:    _ModuleAssignmentService_SetModuleSerial_Handler,
		},
		{
			MethodName: "GetPresetsList",
			Handler:    _ModuleAssignmentService_GetPresetsList_Handler,
		},
		{
			MethodName: "GetCurrentRobotDefinition",
			Handler:    _ModuleAssignmentService_GetCurrentRobotDefinition_Handler,
		},
		{
			MethodName: "SetCurrentRobotDefinition",
			Handler:    _ModuleAssignmentService_SetCurrentRobotDefinition_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/module.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/alarm.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AlarmLevel int32

const (
	AlarmLevel_AL_UNKNOWN  AlarmLevel = 0
	AlarmLevel_AL_CRITICAL AlarmLevel = 1
	AlarmLevel_AL_HIGH     AlarmLevel = 2
	AlarmLevel_AL_MEDIUM   AlarmLevel = 3
	AlarmLevel_AL_LOW      AlarmLevel = 4
	AlarmLevel_AL_HIDDEN   AlarmLevel = 5
)

// Enum value maps for AlarmLevel.
var (
	AlarmLevel_name = map[int32]string{
		0: "AL_UNKNOWN",
		1: "AL_CRITICAL",
		2: "AL_HIGH",
		3: "AL_MEDIUM",
		4: "AL_LOW",
		5: "AL_HIDDEN",
	}
	AlarmLevel_value = map[string]int32{
		"AL_UNKNOWN":  0,
		"AL_CRITICAL": 1,
		"AL_HIGH":     2,
		"AL_MEDIUM":   3,
		"AL_LOW":      4,
		"AL_HIDDEN":   5,
	}
)

func (x AlarmLevel) Enum() *AlarmLevel {
	p := new(AlarmLevel)
	*p = x
	return p
}

func (x AlarmLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_alarm_proto_enumTypes[0].Descriptor()
}

func (AlarmLevel) Type() protoreflect.EnumType {
	return &file_frontend_proto_alarm_proto_enumTypes[0]
}

func (x AlarmLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmLevel.Descriptor instead.
func (AlarmLevel) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{0}
}

type AlarmImpact int32

const (
	AlarmImpact_AI_UNKNOWN  AlarmImpact = 0
	AlarmImpact_AI_CRITICAL AlarmImpact = 1
	AlarmImpact_AI_OFFLINE  AlarmImpact = 2
	AlarmImpact_AI_DEGRADED AlarmImpact = 3
	AlarmImpact_AI_NONE     AlarmImpact = 4
)

// Enum value maps for AlarmImpact.
var (
	AlarmImpact_name = map[int32]string{
		0: "AI_UNKNOWN",
		1: "AI_CRITICAL",
		2: "AI_OFFLINE",
		3: "AI_DEGRADED",
		4: "AI_NONE",
	}
	AlarmImpact_value = map[string]int32{
		"AI_UNKNOWN":  0,
		"AI_CRITICAL": 1,
		"AI_OFFLINE":  2,
		"AI_DEGRADED": 3,
		"AI_NONE":     4,
	}
)

func (x AlarmImpact) Enum() *AlarmImpact {
	p := new(AlarmImpact)
	*p = x
	return p
}

func (x AlarmImpact) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmImpact) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_alarm_proto_enumTypes[1].Descriptor()
}

func (AlarmImpact) Type() protoreflect.EnumType {
	return &file_frontend_proto_alarm_proto_enumTypes[1]
}

func (x AlarmImpact) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmImpact.Descriptor instead.
func (AlarmImpact) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{1}
}

type AlarmRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs           int64                   `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	AlarmCode             string                  `protobuf:"bytes,2,opt,name=alarm_code,json=alarmCode,proto3" json:"alarm_code,omitempty"`
	Subsystem             string                  `protobuf:"bytes,3,opt,name=subsystem,proto3" json:"subsystem,omitempty"`
	Description           string                  `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Level                 AlarmLevel              `protobuf:"varint,5,opt,name=level,proto3,enum=carbon.frontend.alarm.AlarmLevel" json:"level,omitempty"`
	Identifier            string                  `protobuf:"bytes,6,opt,name=identifier,proto3" json:"identifier,omitempty"`
	Acknowledged          bool                    `protobuf:"varint,7,opt,name=acknowledged,proto3" json:"acknowledged,omitempty"`
	Impact                AlarmImpact             `protobuf:"varint,8,opt,name=impact,proto3,enum=carbon.frontend.alarm.AlarmImpact" json:"impact,omitempty"`
	StopTimestampMs       int64                   `protobuf:"varint,9,opt,name=stop_timestamp_ms,json=stopTimestampMs,proto3" json:"stop_timestamp_ms,omitempty"`
	AutofixAvailable      bool                    `protobuf:"varint,10,opt,name=autofix_available,json=autofixAvailable,proto3" json:"autofix_available,omitempty"`
	AutofixAttempted      bool                    `protobuf:"varint,11,opt,name=autofix_attempted,json=autofixAttempted,proto3" json:"autofix_attempted,omitempty"`
	AutofixDurationSec    uint32                  `protobuf:"varint,12,opt,name=autofix_duration_sec,json=autofixDurationSec,proto3" json:"autofix_duration_sec,omitempty"`
	DescriptionKey        string                  `protobuf:"bytes,13,opt,name=description_key,json=descriptionKey,proto3" json:"description_key,omitempty"`
	TranslationParameters []*TranslationParameter `protobuf:"bytes,14,rep,name=translation_parameters,json=translationParameters,proto3" json:"translation_parameters,omitempty"`
}

func (x *AlarmRow) Reset() {
	*x = AlarmRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmRow) ProtoMessage() {}

func (x *AlarmRow) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmRow.ProtoReflect.Descriptor instead.
func (*AlarmRow) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{0}
}

func (x *AlarmRow) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *AlarmRow) GetAlarmCode() string {
	if x != nil {
		return x.AlarmCode
	}
	return ""
}

func (x *AlarmRow) GetSubsystem() string {
	if x != nil {
		return x.Subsystem
	}
	return ""
}

func (x *AlarmRow) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AlarmRow) GetLevel() AlarmLevel {
	if x != nil {
		return x.Level
	}
	return AlarmLevel_AL_UNKNOWN
}

func (x *AlarmRow) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *AlarmRow) GetAcknowledged() bool {
	if x != nil {
		return x.Acknowledged
	}
	return false
}

func (x *AlarmRow) GetImpact() AlarmImpact {
	if x != nil {
		return x.Impact
	}
	return AlarmImpact_AI_UNKNOWN
}

func (x *AlarmRow) GetStopTimestampMs() int64 {
	if x != nil {
		return x.StopTimestampMs
	}
	return 0
}

func (x *AlarmRow) GetAutofixAvailable() bool {
	if x != nil {
		return x.AutofixAvailable
	}
	return false
}

func (x *AlarmRow) GetAutofixAttempted() bool {
	if x != nil {
		return x.AutofixAttempted
	}
	return false
}

func (x *AlarmRow) GetAutofixDurationSec() uint32 {
	if x != nil {
		return x.AutofixDurationSec
	}
	return 0
}

func (x *AlarmRow) GetDescriptionKey() string {
	if x != nil {
		return x.DescriptionKey
	}
	return ""
}

func (x *AlarmRow) GetTranslationParameters() []*TranslationParameter {
	if x != nil {
		return x.TranslationParameters
	}
	return nil
}

type AlarmTable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts     *Timestamp  `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Alarms []*AlarmRow `protobuf:"bytes,2,rep,name=alarms,proto3" json:"alarms,omitempty"`
}

func (x *AlarmTable) Reset() {
	*x = AlarmTable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmTable) ProtoMessage() {}

func (x *AlarmTable) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmTable.ProtoReflect.Descriptor instead.
func (*AlarmTable) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{1}
}

func (x *AlarmTable) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *AlarmTable) GetAlarms() []*AlarmRow {
	if x != nil {
		return x.Alarms
	}
	return nil
}

type AlarmCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Count uint32     `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *AlarmCount) Reset() {
	*x = AlarmCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmCount) ProtoMessage() {}

func (x *AlarmCount) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmCount.ProtoReflect.Descriptor instead.
func (*AlarmCount) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{2}
}

func (x *AlarmCount) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *AlarmCount) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type AcknowledgeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
}

func (x *AcknowledgeRequest) Reset() {
	*x = AcknowledgeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcknowledgeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcknowledgeRequest) ProtoMessage() {}

func (x *AcknowledgeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcknowledgeRequest.ProtoReflect.Descriptor instead.
func (*AcknowledgeRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{3}
}

func (x *AcknowledgeRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

type GetNextAlarmLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromIdx     int32      `protobuf:"varint,1,opt,name=from_idx,json=fromIdx,proto3" json:"from_idx,omitempty"`
	ToIdx       int32      `protobuf:"varint,2,opt,name=to_idx,json=toIdx,proto3" json:"to_idx,omitempty"`
	Ts          *Timestamp `protobuf:"bytes,3,opt,name=ts,proto3" json:"ts,omitempty"`
	VisibleOnly bool       `protobuf:"varint,4,opt,name=visible_only,json=visibleOnly,proto3" json:"visible_only,omitempty"`
}

func (x *GetNextAlarmLogRequest) Reset() {
	*x = GetNextAlarmLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAlarmLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAlarmLogRequest) ProtoMessage() {}

func (x *GetNextAlarmLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAlarmLogRequest.ProtoReflect.Descriptor instead.
func (*GetNextAlarmLogRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{4}
}

func (x *GetNextAlarmLogRequest) GetFromIdx() int32 {
	if x != nil {
		return x.FromIdx
	}
	return 0
}

func (x *GetNextAlarmLogRequest) GetToIdx() int32 {
	if x != nil {
		return x.ToIdx
	}
	return 0
}

func (x *GetNextAlarmLogRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextAlarmLogRequest) GetVisibleOnly() bool {
	if x != nil {
		return x.VisibleOnly
	}
	return false
}

type GetNextAlarmLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alarms []*AlarmRow `protobuf:"bytes,1,rep,name=alarms,proto3" json:"alarms,omitempty"`
	Ts     *Timestamp  `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextAlarmLogResponse) Reset() {
	*x = GetNextAlarmLogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAlarmLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAlarmLogResponse) ProtoMessage() {}

func (x *GetNextAlarmLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAlarmLogResponse.ProtoReflect.Descriptor instead.
func (*GetNextAlarmLogResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{5}
}

func (x *GetNextAlarmLogResponse) GetAlarms() []*AlarmRow {
	if x != nil {
		return x.Alarms
	}
	return nil
}

func (x *GetNextAlarmLogResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextAlarmLogCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts          *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	VisibleOnly bool       `protobuf:"varint,2,opt,name=visible_only,json=visibleOnly,proto3" json:"visible_only,omitempty"`
}

func (x *GetNextAlarmLogCountRequest) Reset() {
	*x = GetNextAlarmLogCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAlarmLogCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAlarmLogCountRequest) ProtoMessage() {}

func (x *GetNextAlarmLogCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAlarmLogCountRequest.ProtoReflect.Descriptor instead.
func (*GetNextAlarmLogCountRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{6}
}

func (x *GetNextAlarmLogCountRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextAlarmLogCountRequest) GetVisibleOnly() bool {
	if x != nil {
		return x.VisibleOnly
	}
	return false
}

type GetNextAlarmLogCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumAlarms int32      `protobuf:"varint,1,opt,name=num_alarms,json=numAlarms,proto3" json:"num_alarms,omitempty"`
	Ts        *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextAlarmLogCountResponse) Reset() {
	*x = GetNextAlarmLogCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAlarmLogCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAlarmLogCountResponse) ProtoMessage() {}

func (x *GetNextAlarmLogCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAlarmLogCountResponse.ProtoReflect.Descriptor instead.
func (*GetNextAlarmLogCountResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{7}
}

func (x *GetNextAlarmLogCountResponse) GetNumAlarms() int32 {
	if x != nil {
		return x.NumAlarms
	}
	return 0
}

func (x *GetNextAlarmLogCountResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type AttemptAutofixAlarmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
}

func (x *AttemptAutofixAlarmRequest) Reset() {
	*x = AttemptAutofixAlarmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttemptAutofixAlarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttemptAutofixAlarmRequest) ProtoMessage() {}

func (x *AttemptAutofixAlarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttemptAutofixAlarmRequest.ProtoReflect.Descriptor instead.
func (*AttemptAutofixAlarmRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{8}
}

func (x *AttemptAutofixAlarmRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

type GetNextAutofixAlarmStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextAutofixAlarmStatusRequest) Reset() {
	*x = GetNextAutofixAlarmStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAutofixAlarmStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAutofixAlarmStatusRequest) ProtoMessage() {}

func (x *GetNextAutofixAlarmStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAutofixAlarmStatusRequest.ProtoReflect.Descriptor instead.
func (*GetNextAutofixAlarmStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{9}
}

func (x *GetNextAutofixAlarmStatusRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextAutofixAlarmStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts           *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Completed    bool       `protobuf:"varint,2,opt,name=completed,proto3" json:"completed,omitempty"`
	ErrorMessage string     `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *GetNextAutofixAlarmStatusResponse) Reset() {
	*x = GetNextAutofixAlarmStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_alarm_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAutofixAlarmStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAutofixAlarmStatusResponse) ProtoMessage() {}

func (x *GetNextAutofixAlarmStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_alarm_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAutofixAlarmStatusResponse.ProtoReflect.Descriptor instead.
func (*GetNextAutofixAlarmStatusResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_alarm_proto_rawDescGZIP(), []int{10}
}

func (x *GetNextAutofixAlarmStatusResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextAutofixAlarmStatusResponse) GetCompleted() bool {
	if x != nil {
		return x.Completed
	}
	return false
}

func (x *GetNextAutofixAlarmStatusResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_frontend_proto_alarm_proto protoreflect.FileDescriptor

var file_frontend_proto_alarm_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x1a, 0x20, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x90, 0x05, 0x0a, 0x08, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x6f, 0x77, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x62, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x61, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x12, 0x3a, 0x0a, 0x06,
	0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x49, 0x6d, 0x70, 0x61, 0x63, 0x74,
	0x52, 0x06, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x6f, 0x70,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x61, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x5f, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x75,
	0x74, 0x6f, 0x66, 0x69, 0x78, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x61, 0x75,
	0x74, 0x6f, 0x66, 0x69, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63,
	0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x68, 0x0a, 0x16, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x15, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x22, 0x76, 0x0a, 0x0a, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02,
	0x74, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d,
	0x52, 0x6f, 0x77, 0x52, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x22, 0x53, 0x0a, 0x0a, 0x41,
	0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x34, 0x0a, 0x12, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x9e, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x66, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x78, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x6f, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f,
	0x49, 0x64, 0x78, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x76, 0x69, 0x73, 0x69,
	0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x22, 0x83, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72,
	0x6d, 0x52, 0x6f, 0x77, 0x52, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0x71, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x79,
	0x22, 0x6e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d,
	0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12,
	0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x22, 0x3c, 0x0a, 0x1a, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x66,
	0x69, 0x78, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x53,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x02, 0x74, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41,
	0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x64, 0x0a,
	0x0a, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x0a, 0x41,
	0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x4c, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x41, 0x4c, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x5f,
	0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4c, 0x5f, 0x4c,
	0x4f, 0x57, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x5f, 0x48, 0x49, 0x44, 0x44, 0x45,
	0x4e, 0x10, 0x05, 0x2a, 0x5c, 0x0a, 0x0b, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x49, 0x6d, 0x70, 0x61,
	0x63, 0x74, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x49, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x49, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x49, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e,
	0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x49, 0x5f, 0x44, 0x45, 0x47, 0x52, 0x41, 0x44,
	0x45, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x49, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10,
	0x04, 0x32, 0xaa, 0x07, 0x0a, 0x0c, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x56, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x57, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x1a, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x65,
	0x77, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x21, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x5a,
	0x0a, 0x10, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x63, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x0b, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x70, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c,
	0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x13, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x41, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x12, 0x31, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x41, 0x75, 0x74,
	0x6f, 0x66, 0x69, 0x78, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x8e, 0x01,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x66,
	0x69, 0x78, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x66, 0x69, 0x78, 0x41, 0x6c, 0x61, 0x72, 0x6d,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10,
	0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_alarm_proto_rawDescOnce sync.Once
	file_frontend_proto_alarm_proto_rawDescData = file_frontend_proto_alarm_proto_rawDesc
)

func file_frontend_proto_alarm_proto_rawDescGZIP() []byte {
	file_frontend_proto_alarm_proto_rawDescOnce.Do(func() {
		file_frontend_proto_alarm_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_alarm_proto_rawDescData)
	})
	return file_frontend_proto_alarm_proto_rawDescData
}

var file_frontend_proto_alarm_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_proto_alarm_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_frontend_proto_alarm_proto_goTypes = []interface{}{
	(AlarmLevel)(0),                           // 0: carbon.frontend.alarm.AlarmLevel
	(AlarmImpact)(0),                          // 1: carbon.frontend.alarm.AlarmImpact
	(*AlarmRow)(nil),                          // 2: carbon.frontend.alarm.AlarmRow
	(*AlarmTable)(nil),                        // 3: carbon.frontend.alarm.AlarmTable
	(*AlarmCount)(nil),                        // 4: carbon.frontend.alarm.AlarmCount
	(*AcknowledgeRequest)(nil),                // 5: carbon.frontend.alarm.AcknowledgeRequest
	(*GetNextAlarmLogRequest)(nil),            // 6: carbon.frontend.alarm.GetNextAlarmLogRequest
	(*GetNextAlarmLogResponse)(nil),           // 7: carbon.frontend.alarm.GetNextAlarmLogResponse
	(*GetNextAlarmLogCountRequest)(nil),       // 8: carbon.frontend.alarm.GetNextAlarmLogCountRequest
	(*GetNextAlarmLogCountResponse)(nil),      // 9: carbon.frontend.alarm.GetNextAlarmLogCountResponse
	(*AttemptAutofixAlarmRequest)(nil),        // 10: carbon.frontend.alarm.AttemptAutofixAlarmRequest
	(*GetNextAutofixAlarmStatusRequest)(nil),  // 11: carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest
	(*GetNextAutofixAlarmStatusResponse)(nil), // 12: carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse
	(*TranslationParameter)(nil),              // 13: carbon.frontend.translation.TranslationParameter
	(*Timestamp)(nil),                         // 14: carbon.frontend.util.Timestamp
	(*Empty)(nil),                             // 15: carbon.frontend.util.Empty
}
var file_frontend_proto_alarm_proto_depIdxs = []int32{
	0,  // 0: carbon.frontend.alarm.AlarmRow.level:type_name -> carbon.frontend.alarm.AlarmLevel
	1,  // 1: carbon.frontend.alarm.AlarmRow.impact:type_name -> carbon.frontend.alarm.AlarmImpact
	13, // 2: carbon.frontend.alarm.AlarmRow.translation_parameters:type_name -> carbon.frontend.translation.TranslationParameter
	14, // 3: carbon.frontend.alarm.AlarmTable.ts:type_name -> carbon.frontend.util.Timestamp
	2,  // 4: carbon.frontend.alarm.AlarmTable.alarms:type_name -> carbon.frontend.alarm.AlarmRow
	14, // 5: carbon.frontend.alarm.AlarmCount.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 6: carbon.frontend.alarm.GetNextAlarmLogRequest.ts:type_name -> carbon.frontend.util.Timestamp
	2,  // 7: carbon.frontend.alarm.GetNextAlarmLogResponse.alarms:type_name -> carbon.frontend.alarm.AlarmRow
	14, // 8: carbon.frontend.alarm.GetNextAlarmLogResponse.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 9: carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 10: carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 11: carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 12: carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 13: carbon.frontend.alarm.AlarmService.GetNextAlarmList:input_type -> carbon.frontend.util.Timestamp
	14, // 14: carbon.frontend.alarm.AlarmService.GetNextAlarmCount:input_type -> carbon.frontend.util.Timestamp
	14, // 15: carbon.frontend.alarm.AlarmService.GetNextNewAlarmList:input_type -> carbon.frontend.util.Timestamp
	5,  // 16: carbon.frontend.alarm.AlarmService.AcknowledgeAlarm:input_type -> carbon.frontend.alarm.AcknowledgeRequest
	15, // 17: carbon.frontend.alarm.AlarmService.ResetAlarms:input_type -> carbon.frontend.util.Empty
	6,  // 18: carbon.frontend.alarm.AlarmService.GetNextAlarmLog:input_type -> carbon.frontend.alarm.GetNextAlarmLogRequest
	8,  // 19: carbon.frontend.alarm.AlarmService.GetNextAlarmLogCount:input_type -> carbon.frontend.alarm.GetNextAlarmLogCountRequest
	10, // 20: carbon.frontend.alarm.AlarmService.AttemptAutofixAlarm:input_type -> carbon.frontend.alarm.AttemptAutofixAlarmRequest
	11, // 21: carbon.frontend.alarm.AlarmService.GetNextAutofixAlarmStatus:input_type -> carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest
	3,  // 22: carbon.frontend.alarm.AlarmService.GetNextAlarmList:output_type -> carbon.frontend.alarm.AlarmTable
	4,  // 23: carbon.frontend.alarm.AlarmService.GetNextAlarmCount:output_type -> carbon.frontend.alarm.AlarmCount
	3,  // 24: carbon.frontend.alarm.AlarmService.GetNextNewAlarmList:output_type -> carbon.frontend.alarm.AlarmTable
	15, // 25: carbon.frontend.alarm.AlarmService.AcknowledgeAlarm:output_type -> carbon.frontend.util.Empty
	15, // 26: carbon.frontend.alarm.AlarmService.ResetAlarms:output_type -> carbon.frontend.util.Empty
	7,  // 27: carbon.frontend.alarm.AlarmService.GetNextAlarmLog:output_type -> carbon.frontend.alarm.GetNextAlarmLogResponse
	9,  // 28: carbon.frontend.alarm.AlarmService.GetNextAlarmLogCount:output_type -> carbon.frontend.alarm.GetNextAlarmLogCountResponse
	15, // 29: carbon.frontend.alarm.AlarmService.AttemptAutofixAlarm:output_type -> carbon.frontend.util.Empty
	12, // 30: carbon.frontend.alarm.AlarmService.GetNextAutofixAlarmStatus:output_type -> carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse
	22, // [22:31] is the sub-list for method output_type
	13, // [13:22] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_frontend_proto_alarm_proto_init() }
func file_frontend_proto_alarm_proto_init() {
	if File_frontend_proto_alarm_proto != nil {
		return
	}
	file_frontend_proto_translation_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_alarm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmTable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcknowledgeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAlarmLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAlarmLogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAlarmLogCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAlarmLogCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttemptAutofixAlarmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAutofixAlarmStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_alarm_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAutofixAlarmStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_alarm_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_alarm_proto_goTypes,
		DependencyIndexes: file_frontend_proto_alarm_proto_depIdxs,
		EnumInfos:         file_frontend_proto_alarm_proto_enumTypes,
		MessageInfos:      file_frontend_proto_alarm_proto_msgTypes,
	}.Build()
	File_frontend_proto_alarm_proto = out.File
	file_frontend_proto_alarm_proto_rawDesc = nil
	file_frontend_proto_alarm_proto_goTypes = nil
	file_frontend_proto_alarm_proto_depIdxs = nil
}

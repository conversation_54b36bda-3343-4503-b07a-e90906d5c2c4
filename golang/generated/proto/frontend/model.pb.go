// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/model.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                                  string     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Crop                                string     `protobuf:"bytes,2,opt,name=crop,proto3" json:"crop,omitempty"`
	Ts                                  *Timestamp `protobuf:"bytes,3,opt,name=ts,proto3" json:"ts,omitempty"`
	Custom                              bool       `protobuf:"varint,4,opt,name=custom,proto3" json:"custom,omitempty"`
	Pinned                              bool       `protobuf:"varint,5,opt,name=pinned,proto3" json:"pinned,omitempty"`
	Active                              bool       `protobuf:"varint,6,opt,name=active,proto3" json:"active,omitempty"`
	Synced                              bool       `protobuf:"varint,7,opt,name=synced,proto3" json:"synced,omitempty"`
	SyncedToRows                        []bool     `protobuf:"varint,8,rep,packed,name=synced_to_rows,json=syncedToRows,proto3" json:"synced_to_rows,omitempty"`
	Downloading                         bool       `protobuf:"varint,9,opt,name=downloading,proto3" json:"downloading,omitempty"`
	Type                                string     `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	LastUsedTimestamp                   *Timestamp `protobuf:"bytes,11,opt,name=last_used_timestamp,json=lastUsedTimestamp,proto3" json:"last_used_timestamp,omitempty"`
	DownloadingProgress                 float32    `protobuf:"fixed32,12,opt,name=downloading_progress,json=downloadingProgress,proto3" json:"downloading_progress,omitempty"`
	EstimatedDownloadingRemainingTimeMs uint64     `protobuf:"varint,13,opt,name=estimated_downloading_remaining_time_ms,json=estimatedDownloadingRemainingTimeMs,proto3" json:"estimated_downloading_remaining_time_ms,omitempty"`
	DownloadedTimestamp                 *Timestamp `protobuf:"bytes,14,opt,name=downloaded_timestamp,json=downloadedTimestamp,proto3" json:"downloaded_timestamp,omitempty"`
	Recommended                         bool       `protobuf:"varint,15,opt,name=recommended,proto3" json:"recommended,omitempty"`
	ViableCropIds                       []string   `protobuf:"bytes,16,rep,name=viable_crop_ids,json=viableCropIds,proto3" json:"viable_crop_ids,omitempty"`
	Maintained                          bool       `protobuf:"varint,17,opt,name=maintained,proto3" json:"maintained,omitempty"`
	Nickname                            string     `protobuf:"bytes,18,opt,name=nickname,proto3" json:"nickname,omitempty"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{0}
}

func (x *Model) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Model) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *Model) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *Model) GetCustom() bool {
	if x != nil {
		return x.Custom
	}
	return false
}

func (x *Model) GetPinned() bool {
	if x != nil {
		return x.Pinned
	}
	return false
}

func (x *Model) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *Model) GetSynced() bool {
	if x != nil {
		return x.Synced
	}
	return false
}

func (x *Model) GetSyncedToRows() []bool {
	if x != nil {
		return x.SyncedToRows
	}
	return nil
}

func (x *Model) GetDownloading() bool {
	if x != nil {
		return x.Downloading
	}
	return false
}

func (x *Model) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Model) GetLastUsedTimestamp() *Timestamp {
	if x != nil {
		return x.LastUsedTimestamp
	}
	return nil
}

func (x *Model) GetDownloadingProgress() float32 {
	if x != nil {
		return x.DownloadingProgress
	}
	return 0
}

func (x *Model) GetEstimatedDownloadingRemainingTimeMs() uint64 {
	if x != nil {
		return x.EstimatedDownloadingRemainingTimeMs
	}
	return 0
}

func (x *Model) GetDownloadedTimestamp() *Timestamp {
	if x != nil {
		return x.DownloadedTimestamp
	}
	return nil
}

func (x *Model) GetRecommended() bool {
	if x != nil {
		return x.Recommended
	}
	return false
}

func (x *Model) GetViableCropIds() []string {
	if x != nil {
		return x.ViableCropIds
	}
	return nil
}

func (x *Model) GetMaintained() bool {
	if x != nil {
		return x.Maintained
	}
	return false
}

func (x *Model) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type SelectCropRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CropId string `protobuf:"bytes,1,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *SelectCropRequest) Reset() {
	*x = SelectCropRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectCropRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectCropRequest) ProtoMessage() {}

func (x *SelectCropRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectCropRequest.ProtoReflect.Descriptor instead.
func (*SelectCropRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{1}
}

func (x *SelectCropRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

type ListCropParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *ListCropParameters) Reset() {
	*x = ListCropParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCropParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCropParameters) ProtoMessage() {}

func (x *ListCropParameters) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCropParameters.ProtoReflect.Descriptor instead.
func (*ListCropParameters) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{2}
}

func (x *ListCropParameters) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type EnabledCrop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Created int64  `protobuf:"varint,2,opt,name=created,proto3" json:"created,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/model.proto.
	CarbonName       string `protobuf:"bytes,3,opt,name=carbon_name,json=carbonName,proto3" json:"carbon_name,omitempty"` // Deprecated
	CommonName       string `protobuf:"bytes,4,opt,name=common_name,json=commonName,proto3" json:"common_name,omitempty"`
	Description      string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Notes            string `protobuf:"bytes,6,opt,name=notes,proto3" json:"notes,omitempty"`
	PinnedModelId    string `protobuf:"bytes,7,opt,name=pinned_model_id,json=pinnedModelId,proto3" json:"pinned_model_id,omitempty"`
	RecommendedModel string `protobuf:"bytes,8,opt,name=recommended_model,json=recommendedModel,proto3" json:"recommended_model,omitempty"`
}

func (x *EnabledCrop) Reset() {
	*x = EnabledCrop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnabledCrop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnabledCrop) ProtoMessage() {}

func (x *EnabledCrop) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnabledCrop.ProtoReflect.Descriptor instead.
func (*EnabledCrop) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{3}
}

func (x *EnabledCrop) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EnabledCrop) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

// Deprecated: Marked as deprecated in frontend/proto/model.proto.
func (x *EnabledCrop) GetCarbonName() string {
	if x != nil {
		return x.CarbonName
	}
	return ""
}

func (x *EnabledCrop) GetCommonName() string {
	if x != nil {
		return x.CommonName
	}
	return ""
}

func (x *EnabledCrop) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EnabledCrop) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *EnabledCrop) GetPinnedModelId() string {
	if x != nil {
		return x.PinnedModelId
	}
	return ""
}

func (x *EnabledCrop) GetRecommendedModel() string {
	if x != nil {
		return x.RecommendedModel
	}
	return ""
}

type EnabledCropList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnabledCrops []*EnabledCrop `protobuf:"bytes,1,rep,name=enabledCrops,proto3" json:"enabledCrops,omitempty"`
}

func (x *EnabledCropList) Reset() {
	*x = EnabledCropList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnabledCropList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnabledCropList) ProtoMessage() {}

func (x *EnabledCropList) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnabledCropList.ProtoReflect.Descriptor instead.
func (*EnabledCropList) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{4}
}

func (x *EnabledCropList) GetEnabledCrops() []*EnabledCrop {
	if x != nil {
		return x.EnabledCrops
	}
	return nil
}

type GetNextSelectedCropIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts     *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	CropId string     `protobuf:"bytes,2,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *GetNextSelectedCropIDResponse) Reset() {
	*x = GetNextSelectedCropIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextSelectedCropIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextSelectedCropIDResponse) ProtoMessage() {}

func (x *GetNextSelectedCropIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextSelectedCropIDResponse.ProtoReflect.Descriptor instead.
func (*GetNextSelectedCropIDResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{5}
}

func (x *GetNextSelectedCropIDResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextSelectedCropIDResponse) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

type PinModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/model.proto.
	Crop                    string `protobuf:"bytes,2,opt,name=crop,proto3" json:"crop,omitempty"` // Deprecated, use crop_id
	AllowPinnedCropOverride bool   `protobuf:"varint,3,opt,name=allow_pinned_crop_override,json=allowPinnedCropOverride,proto3" json:"allow_pinned_crop_override,omitempty"`
	CropId                  string `protobuf:"bytes,4,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	P2P                     bool   `protobuf:"varint,5,opt,name=p2p,proto3" json:"p2p,omitempty"`
}

func (x *PinModelRequest) Reset() {
	*x = PinModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinModelRequest) ProtoMessage() {}

func (x *PinModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinModelRequest.ProtoReflect.Descriptor instead.
func (*PinModelRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{6}
}

func (x *PinModelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Deprecated: Marked as deprecated in frontend/proto/model.proto.
func (x *PinModelRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *PinModelRequest) GetAllowPinnedCropOverride() bool {
	if x != nil {
		return x.AllowPinnedCropOverride
	}
	return false
}

func (x *PinModelRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *PinModelRequest) GetP2P() bool {
	if x != nil {
		return x.P2P
	}
	return false
}

type UnpinModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/model.proto.
	Crop   string `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"` // Deprecated, use crop_id
	CropId string `protobuf:"bytes,4,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	P2P    bool   `protobuf:"varint,5,opt,name=p2p,proto3" json:"p2p,omitempty"`
}

func (x *UnpinModelRequest) Reset() {
	*x = UnpinModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnpinModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnpinModelRequest) ProtoMessage() {}

func (x *UnpinModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnpinModelRequest.ProtoReflect.Descriptor instead.
func (*UnpinModelRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{7}
}

// Deprecated: Marked as deprecated in frontend/proto/model.proto.
func (x *UnpinModelRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *UnpinModelRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *UnpinModelRequest) GetP2P() bool {
	if x != nil {
		return x.P2P
	}
	return false
}

type GetNextModelStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/model.proto.
	Crop   string     `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"` // Deprecated, use crop_id
	Ts     *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	CropId string     `protobuf:"bytes,3,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *GetNextModelStateRequest) Reset() {
	*x = GetNextModelStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextModelStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextModelStateRequest) ProtoMessage() {}

func (x *GetNextModelStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextModelStateRequest.ProtoReflect.Descriptor instead.
func (*GetNextModelStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{8}
}

// Deprecated: Marked as deprecated in frontend/proto/model.proto.
func (x *GetNextModelStateRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *GetNextModelStateRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextModelStateRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

type GetNextModelStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models                 []*Model   `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
	Ts                     *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	CurrentP2PModelId      string     `protobuf:"bytes,3,opt,name=current_p2p_model_id,json=currentP2pModelId,proto3" json:"current_p2p_model_id,omitempty"`
	CurrentDeepweedModelId string     `protobuf:"bytes,4,opt,name=current_deepweed_model_id,json=currentDeepweedModelId,proto3" json:"current_deepweed_model_id,omitempty"`
}

func (x *GetNextModelStateResponse) Reset() {
	*x = GetNextModelStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextModelStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextModelStateResponse) ProtoMessage() {}

func (x *GetNextModelStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextModelStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextModelStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{9}
}

func (x *GetNextModelStateResponse) GetModels() []*Model {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *GetNextModelStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextModelStateResponse) GetCurrentP2PModelId() string {
	if x != nil {
		return x.CurrentP2PModelId
	}
	return ""
}

func (x *GetNextModelStateResponse) GetCurrentDeepweedModelId() string {
	if x != nil {
		return x.CurrentDeepweedModelId
	}
	return ""
}

type DownloadModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
}

func (x *DownloadModelRequest) Reset() {
	*x = DownloadModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadModelRequest) ProtoMessage() {}

func (x *DownloadModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadModelRequest.ProtoReflect.Descriptor instead.
func (*DownloadModelRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{10}
}

func (x *DownloadModelRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

type ModelHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTimestamp   int64                  `protobuf:"varint,1,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	Count            int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Reverse          bool                   `protobuf:"varint,3,opt,name=reverse,proto3" json:"reverse,omitempty"`
	MatchFilter      *ModelEvent            `protobuf:"bytes,4,opt,name=match_filter,json=matchFilter,proto3" json:"match_filter,omitempty"`
	Ts               *Timestamp             `protobuf:"bytes,5,opt,name=ts,proto3" json:"ts,omitempty"`
	EventTypeMatcher *ModelEventTypeMatcher `protobuf:"bytes,6,opt,name=event_type_matcher,json=eventTypeMatcher,proto3" json:"event_type_matcher,omitempty"`
}

func (x *ModelHistoryRequest) Reset() {
	*x = ModelHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelHistoryRequest) ProtoMessage() {}

func (x *ModelHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelHistoryRequest.ProtoReflect.Descriptor instead.
func (*ModelHistoryRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{11}
}

func (x *ModelHistoryRequest) GetStartTimestamp() int64 {
	if x != nil {
		return x.StartTimestamp
	}
	return 0
}

func (x *ModelHistoryRequest) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ModelHistoryRequest) GetReverse() bool {
	if x != nil {
		return x.Reverse
	}
	return false
}

func (x *ModelHistoryRequest) GetMatchFilter() *ModelEvent {
	if x != nil {
		return x.MatchFilter
	}
	return nil
}

func (x *ModelHistoryRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *ModelHistoryRequest) GetEventTypeMatcher() *ModelEventTypeMatcher {
	if x != nil {
		return x.EventTypeMatcher
	}
	return nil
}

type ModelEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	ModelId         string `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelType       string `protobuf:"bytes,3,opt,name=model_type,json=modelType,proto3" json:"model_type,omitempty"`
	CropId          string `protobuf:"bytes,4,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	JobName         string `protobuf:"bytes,5,opt,name=job_name,json=jobName,proto3" json:"job_name,omitempty"`
	Time            int64  `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	ModelNickname   string `protobuf:"bytes,7,opt,name=model_nickname,json=modelNickname,proto3" json:"model_nickname,omitempty"`
	ModelParameters string `protobuf:"bytes,8,opt,name=model_parameters,json=modelParameters,proto3" json:"model_parameters,omitempty"`
}

func (x *ModelEvent) Reset() {
	*x = ModelEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelEvent) ProtoMessage() {}

func (x *ModelEvent) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelEvent.ProtoReflect.Descriptor instead.
func (*ModelEvent) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{12}
}

func (x *ModelEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ModelEvent) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelEvent) GetModelType() string {
	if x != nil {
		return x.ModelType
	}
	return ""
}

func (x *ModelEvent) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *ModelEvent) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *ModelEvent) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *ModelEvent) GetModelNickname() string {
	if x != nil {
		return x.ModelNickname
	}
	return ""
}

func (x *ModelEvent) GetModelParameters() string {
	if x != nil {
		return x.ModelParameters
	}
	return ""
}

type ModelEventTypeMatcher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotStart             bool `protobuf:"varint,1,opt,name=robot_start,json=robotStart,proto3" json:"robot_start,omitempty"`
	Pinned                 bool `protobuf:"varint,2,opt,name=pinned,proto3" json:"pinned,omitempty"`
	Unpinned               bool `protobuf:"varint,3,opt,name=unpinned,proto3" json:"unpinned,omitempty"`
	Recommended            bool `protobuf:"varint,4,opt,name=recommended,proto3" json:"recommended,omitempty"`
	Activated              bool `protobuf:"varint,5,opt,name=activated,proto3" json:"activated,omitempty"`
	NicknameChange         bool `protobuf:"varint,6,opt,name=nickname_change,json=nicknameChange,proto3" json:"nickname_change,omitempty"`
	NicknameDelete         bool `protobuf:"varint,7,opt,name=nickname_delete,json=nicknameDelete,proto3" json:"nickname_delete,omitempty"`
	DefaultParameterChange bool `protobuf:"varint,8,opt,name=default_parameter_change,json=defaultParameterChange,proto3" json:"default_parameter_change,omitempty"`
	ParameterChange        bool `protobuf:"varint,9,opt,name=parameter_change,json=parameterChange,proto3" json:"parameter_change,omitempty"`
}

func (x *ModelEventTypeMatcher) Reset() {
	*x = ModelEventTypeMatcher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelEventTypeMatcher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelEventTypeMatcher) ProtoMessage() {}

func (x *ModelEventTypeMatcher) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelEventTypeMatcher.ProtoReflect.Descriptor instead.
func (*ModelEventTypeMatcher) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{13}
}

func (x *ModelEventTypeMatcher) GetRobotStart() bool {
	if x != nil {
		return x.RobotStart
	}
	return false
}

func (x *ModelEventTypeMatcher) GetPinned() bool {
	if x != nil {
		return x.Pinned
	}
	return false
}

func (x *ModelEventTypeMatcher) GetUnpinned() bool {
	if x != nil {
		return x.Unpinned
	}
	return false
}

func (x *ModelEventTypeMatcher) GetRecommended() bool {
	if x != nil {
		return x.Recommended
	}
	return false
}

func (x *ModelEventTypeMatcher) GetActivated() bool {
	if x != nil {
		return x.Activated
	}
	return false
}

func (x *ModelEventTypeMatcher) GetNicknameChange() bool {
	if x != nil {
		return x.NicknameChange
	}
	return false
}

func (x *ModelEventTypeMatcher) GetNicknameDelete() bool {
	if x != nil {
		return x.NicknameDelete
	}
	return false
}

func (x *ModelEventTypeMatcher) GetDefaultParameterChange() bool {
	if x != nil {
		return x.DefaultParameterChange
	}
	return false
}

func (x *ModelEventTypeMatcher) GetParameterChange() bool {
	if x != nil {
		return x.ParameterChange
	}
	return false
}

type ModelHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*ModelEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	Ts     *Timestamp    `protobuf:"bytes,5,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *ModelHistoryResponse) Reset() {
	*x = ModelHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelHistoryResponse) ProtoMessage() {}

func (x *ModelHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelHistoryResponse.ProtoReflect.Descriptor instead.
func (*ModelHistoryResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{14}
}

func (x *ModelHistoryResponse) GetEvents() []*ModelEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

func (x *ModelHistoryResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetModelNicknamesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelIds []string   `protobuf:"bytes,1,rep,name=model_ids,json=modelIds,proto3" json:"model_ids,omitempty"`
	Ts       *Timestamp `protobuf:"bytes,5,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetModelNicknamesRequest) Reset() {
	*x = GetModelNicknamesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelNicknamesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelNicknamesRequest) ProtoMessage() {}

func (x *GetModelNicknamesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelNicknamesRequest.ProtoReflect.Descriptor instead.
func (*GetModelNicknamesRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{15}
}

func (x *GetModelNicknamesRequest) GetModelIds() []string {
	if x != nil {
		return x.ModelIds
	}
	return nil
}

func (x *GetModelNicknamesRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetModelNicknamesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelNicknames map[string]string `protobuf:"bytes,1,rep,name=model_nicknames,json=modelNicknames,proto3" json:"model_nicknames,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Ts             *Timestamp        `protobuf:"bytes,5,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetModelNicknamesResponse) Reset() {
	*x = GetModelNicknamesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelNicknamesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelNicknamesResponse) ProtoMessage() {}

func (x *GetModelNicknamesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelNicknamesResponse.ProtoReflect.Descriptor instead.
func (*GetModelNicknamesResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{16}
}

func (x *GetModelNicknamesResponse) GetModelNicknames() map[string]string {
	if x != nil {
		return x.ModelNicknames
	}
	return nil
}

func (x *GetModelNicknamesResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type SetModelNicknameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId       string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelNickname string `protobuf:"bytes,2,opt,name=model_nickname,json=modelNickname,proto3" json:"model_nickname,omitempty"`
}

func (x *SetModelNicknameRequest) Reset() {
	*x = SetModelNicknameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetModelNicknameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetModelNicknameRequest) ProtoMessage() {}

func (x *SetModelNicknameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetModelNicknameRequest.ProtoReflect.Descriptor instead.
func (*SetModelNicknameRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{17}
}

func (x *SetModelNicknameRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *SetModelNicknameRequest) GetModelNickname() string {
	if x != nil {
		return x.ModelNickname
	}
	return ""
}

type CropModelPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CropId  string `protobuf:"bytes,1,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	ModelId string `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
}

func (x *CropModelPair) Reset() {
	*x = CropModelPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CropModelPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CropModelPair) ProtoMessage() {}

func (x *CropModelPair) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CropModelPair.ProtoReflect.Descriptor instead.
func (*CropModelPair) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{18}
}

func (x *CropModelPair) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *CropModelPair) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

type RefreshDefaultModelParametersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CropModelPairs []*CropModelPair `protobuf:"bytes,1,rep,name=cropModelPairs,proto3" json:"cropModelPairs,omitempty"`
}

func (x *RefreshDefaultModelParametersRequest) Reset() {
	*x = RefreshDefaultModelParametersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDefaultModelParametersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDefaultModelParametersRequest) ProtoMessage() {}

func (x *RefreshDefaultModelParametersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDefaultModelParametersRequest.ProtoReflect.Descriptor instead.
func (*RefreshDefaultModelParametersRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{19}
}

func (x *RefreshDefaultModelParametersRequest) GetCropModelPairs() []*CropModelPair {
	if x != nil {
		return x.CropModelPairs
	}
	return nil
}

type SyncCropIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ForceCacheRefresh bool `protobuf:"varint,1,opt,name=force_cache_refresh,json=forceCacheRefresh,proto3" json:"force_cache_refresh,omitempty"`
}

func (x *SyncCropIDsRequest) Reset() {
	*x = SyncCropIDsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncCropIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCropIDsRequest) ProtoMessage() {}

func (x *SyncCropIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCropIDsRequest.ProtoReflect.Descriptor instead.
func (*SyncCropIDsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{20}
}

func (x *SyncCropIDsRequest) GetForceCacheRefresh() bool {
	if x != nil {
		return x.ForceCacheRefresh
	}
	return false
}

type GetNextEnabledCropsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts   *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Lang string     `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetNextEnabledCropsRequest) Reset() {
	*x = GetNextEnabledCropsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextEnabledCropsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextEnabledCropsRequest) ProtoMessage() {}

func (x *GetNextEnabledCropsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextEnabledCropsRequest.ProtoReflect.Descriptor instead.
func (*GetNextEnabledCropsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{21}
}

func (x *GetNextEnabledCropsRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextEnabledCropsRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GetNextEnabledCropsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnabledCrops []*EnabledCrop `protobuf:"bytes,1,rep,name=enabledCrops,proto3" json:"enabledCrops,omitempty"`
	Ts           *Timestamp     `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextEnabledCropsResponse) Reset() {
	*x = GetNextEnabledCropsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextEnabledCropsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextEnabledCropsResponse) ProtoMessage() {}

func (x *GetNextEnabledCropsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextEnabledCropsResponse.ProtoReflect.Descriptor instead.
func (*GetNextEnabledCropsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{22}
}

func (x *GetNextEnabledCropsResponse) GetEnabledCrops() []*EnabledCrop {
	if x != nil {
		return x.EnabledCrops
	}
	return nil
}

func (x *GetNextEnabledCropsResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextCaptureCropsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts   *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Lang string     `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetNextCaptureCropsRequest) Reset() {
	*x = GetNextCaptureCropsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCaptureCropsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCaptureCropsRequest) ProtoMessage() {}

func (x *GetNextCaptureCropsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCaptureCropsRequest.ProtoReflect.Descriptor instead.
func (*GetNextCaptureCropsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{23}
}

func (x *GetNextCaptureCropsRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextCaptureCropsRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GetNextCaptureCropsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnabledCrops []*EnabledCrop `protobuf:"bytes,1,rep,name=enabledCrops,proto3" json:"enabledCrops,omitempty"`
	Ts           *Timestamp     `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextCaptureCropsResponse) Reset() {
	*x = GetNextCaptureCropsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_model_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCaptureCropsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCaptureCropsResponse) ProtoMessage() {}

func (x *GetNextCaptureCropsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_model_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCaptureCropsResponse.ProtoReflect.Descriptor instead.
func (*GetNextCaptureCropsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_model_proto_rawDescGZIP(), []int{24}
}

func (x *GetNextCaptureCropsResponse) GetEnabledCrops() []*EnabledCrop {
	if x != nil {
		return x.EnabledCrops
	}
	return nil
}

func (x *GetNextCaptureCropsResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

var File_frontend_proto_model_proto protoreflect.FileDescriptor

var file_frontend_proto_model_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc,
	0x05, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x6f, 0x52,
	0x6f, 0x77, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x13, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x73, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x31, 0x0a, 0x14, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x54, 0x0a,
	0x27, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x23,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x12, 0x52, 0x0a, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x49, 0x64,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2c, 0x0a,
	0x11, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x8a, 0x02, 0x0a, 0x0b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x23, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x22, 0x59, 0x0a, 0x0f, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x43, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x52,
	0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x22, 0x69, 0x0a,
	0x1d, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x43, 0x72, 0x6f, 0x70, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x0f, 0x50, 0x69, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x04,
	0x63, 0x72, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04,
	0x63, 0x72, 0x6f, 0x70, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x69,
	0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x50,
	0x69, 0x6e, 0x6e, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x32,
	0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x70, 0x32, 0x70, 0x22, 0x56, 0x0a, 0x11,
	0x55, 0x6e, 0x70, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x32, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x03, 0x70, 0x32, 0x70, 0x22, 0x7c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70,
	0x49, 0x64, 0x22, 0xee, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x34, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x32, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x32,
	0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x14, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x22, 0xc1, 0x02, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x5a,
	0x0a, 0x12, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x10, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x22, 0xf4, 0x01, 0x0a, 0x0a, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x22, 0xe3, 0x02, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x69,
	0x6e, 0x6e, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x6e, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x14, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x39, 0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0x68, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0xfe, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5b, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x0d, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x50, 0x61, 0x69, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x24, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x4c, 0x0a, 0x0e, 0x63, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61,
	0x69, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x52,
	0x0e, 0x63, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x73, 0x22,
	0x44, 0x0a, 0x12, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x72, 0x6f, 0x70, 0x49, 0x44, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x22, 0x61, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x96, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72,
	0x6f, 0x70, 0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73,
	0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74,
	0x73, 0x22, 0x61, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x22, 0x96, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43,
	0x72, 0x6f, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x52, 0x0c,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x32, 0xaa, 0x10,
	0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4f,
	0x0a, 0x08, 0x50, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x50, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x53, 0x0a, 0x0a, 0x55, 0x6e, 0x70, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x28, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55, 0x6e, 0x70, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x76, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x65, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43,
	0x72, 0x6f, 0x70, 0x73, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x72, 0x6f, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x1a,
	0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43,
	0x72, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x7c, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x31,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x1a, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x7c, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x43, 0x72,
	0x6f, 0x70, 0x73, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x43, 0x72, 0x6f,
	0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x72, 0x6f,
	0x70, 0x49, 0x44, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x1a, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x59, 0x0a, 0x0d, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6e, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2a, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x10, 0x53, 0x65,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x79, 0x0a, 0x1d, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x0b, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x72,
	0x6f, 0x70, 0x49, 0x44, 0x73, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x43, 0x72, 0x6f, 0x70, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4b, 0x0a,
	0x0f, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_model_proto_rawDescOnce sync.Once
	file_frontend_proto_model_proto_rawDescData = file_frontend_proto_model_proto_rawDesc
)

func file_frontend_proto_model_proto_rawDescGZIP() []byte {
	file_frontend_proto_model_proto_rawDescOnce.Do(func() {
		file_frontend_proto_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_model_proto_rawDescData)
	})
	return file_frontend_proto_model_proto_rawDescData
}

var file_frontend_proto_model_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_frontend_proto_model_proto_goTypes = []interface{}{
	(*Model)(nil),                                // 0: carbon.frontend.model.Model
	(*SelectCropRequest)(nil),                    // 1: carbon.frontend.model.SelectCropRequest
	(*ListCropParameters)(nil),                   // 2: carbon.frontend.model.ListCropParameters
	(*EnabledCrop)(nil),                          // 3: carbon.frontend.model.EnabledCrop
	(*EnabledCropList)(nil),                      // 4: carbon.frontend.model.EnabledCropList
	(*GetNextSelectedCropIDResponse)(nil),        // 5: carbon.frontend.model.GetNextSelectedCropIDResponse
	(*PinModelRequest)(nil),                      // 6: carbon.frontend.model.PinModelRequest
	(*UnpinModelRequest)(nil),                    // 7: carbon.frontend.model.UnpinModelRequest
	(*GetNextModelStateRequest)(nil),             // 8: carbon.frontend.model.GetNextModelStateRequest
	(*GetNextModelStateResponse)(nil),            // 9: carbon.frontend.model.GetNextModelStateResponse
	(*DownloadModelRequest)(nil),                 // 10: carbon.frontend.model.DownloadModelRequest
	(*ModelHistoryRequest)(nil),                  // 11: carbon.frontend.model.ModelHistoryRequest
	(*ModelEvent)(nil),                           // 12: carbon.frontend.model.ModelEvent
	(*ModelEventTypeMatcher)(nil),                // 13: carbon.frontend.model.ModelEventTypeMatcher
	(*ModelHistoryResponse)(nil),                 // 14: carbon.frontend.model.ModelHistoryResponse
	(*GetModelNicknamesRequest)(nil),             // 15: carbon.frontend.model.GetModelNicknamesRequest
	(*GetModelNicknamesResponse)(nil),            // 16: carbon.frontend.model.GetModelNicknamesResponse
	(*SetModelNicknameRequest)(nil),              // 17: carbon.frontend.model.SetModelNicknameRequest
	(*CropModelPair)(nil),                        // 18: carbon.frontend.model.CropModelPair
	(*RefreshDefaultModelParametersRequest)(nil), // 19: carbon.frontend.model.RefreshDefaultModelParametersRequest
	(*SyncCropIDsRequest)(nil),                   // 20: carbon.frontend.model.SyncCropIDsRequest
	(*GetNextEnabledCropsRequest)(nil),           // 21: carbon.frontend.model.GetNextEnabledCropsRequest
	(*GetNextEnabledCropsResponse)(nil),          // 22: carbon.frontend.model.GetNextEnabledCropsResponse
	(*GetNextCaptureCropsRequest)(nil),           // 23: carbon.frontend.model.GetNextCaptureCropsRequest
	(*GetNextCaptureCropsResponse)(nil),          // 24: carbon.frontend.model.GetNextCaptureCropsResponse
	nil,                                          // 25: carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry
	(*Timestamp)(nil),                            // 26: carbon.frontend.util.Timestamp
	(*Empty)(nil),                                // 27: carbon.frontend.util.Empty
}
var file_frontend_proto_model_proto_depIdxs = []int32{
	26, // 0: carbon.frontend.model.Model.ts:type_name -> carbon.frontend.util.Timestamp
	26, // 1: carbon.frontend.model.Model.last_used_timestamp:type_name -> carbon.frontend.util.Timestamp
	26, // 2: carbon.frontend.model.Model.downloaded_timestamp:type_name -> carbon.frontend.util.Timestamp
	3,  // 3: carbon.frontend.model.EnabledCropList.enabledCrops:type_name -> carbon.frontend.model.EnabledCrop
	26, // 4: carbon.frontend.model.GetNextSelectedCropIDResponse.ts:type_name -> carbon.frontend.util.Timestamp
	26, // 5: carbon.frontend.model.GetNextModelStateRequest.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 6: carbon.frontend.model.GetNextModelStateResponse.models:type_name -> carbon.frontend.model.Model
	26, // 7: carbon.frontend.model.GetNextModelStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	12, // 8: carbon.frontend.model.ModelHistoryRequest.match_filter:type_name -> carbon.frontend.model.ModelEvent
	26, // 9: carbon.frontend.model.ModelHistoryRequest.ts:type_name -> carbon.frontend.util.Timestamp
	13, // 10: carbon.frontend.model.ModelHistoryRequest.event_type_matcher:type_name -> carbon.frontend.model.ModelEventTypeMatcher
	12, // 11: carbon.frontend.model.ModelHistoryResponse.events:type_name -> carbon.frontend.model.ModelEvent
	26, // 12: carbon.frontend.model.ModelHistoryResponse.ts:type_name -> carbon.frontend.util.Timestamp
	26, // 13: carbon.frontend.model.GetModelNicknamesRequest.ts:type_name -> carbon.frontend.util.Timestamp
	25, // 14: carbon.frontend.model.GetModelNicknamesResponse.model_nicknames:type_name -> carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry
	26, // 15: carbon.frontend.model.GetModelNicknamesResponse.ts:type_name -> carbon.frontend.util.Timestamp
	18, // 16: carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs:type_name -> carbon.frontend.model.CropModelPair
	26, // 17: carbon.frontend.model.GetNextEnabledCropsRequest.ts:type_name -> carbon.frontend.util.Timestamp
	3,  // 18: carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops:type_name -> carbon.frontend.model.EnabledCrop
	26, // 19: carbon.frontend.model.GetNextEnabledCropsResponse.ts:type_name -> carbon.frontend.util.Timestamp
	26, // 20: carbon.frontend.model.GetNextCaptureCropsRequest.ts:type_name -> carbon.frontend.util.Timestamp
	3,  // 21: carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops:type_name -> carbon.frontend.model.EnabledCrop
	26, // 22: carbon.frontend.model.GetNextCaptureCropsResponse.ts:type_name -> carbon.frontend.util.Timestamp
	6,  // 23: carbon.frontend.model.ModelService.PinModel:input_type -> carbon.frontend.model.PinModelRequest
	7,  // 24: carbon.frontend.model.ModelService.UnpinModel:input_type -> carbon.frontend.model.UnpinModelRequest
	8,  // 25: carbon.frontend.model.ModelService.GetNextModelState:input_type -> carbon.frontend.model.GetNextModelStateRequest
	8,  // 26: carbon.frontend.model.ModelService.GetNextAllModelState:input_type -> carbon.frontend.model.GetNextModelStateRequest
	27, // 27: carbon.frontend.model.ModelService.UpdateModel:input_type -> carbon.frontend.util.Empty
	2,  // 28: carbon.frontend.model.ModelService.ListEnabledCrops:input_type -> carbon.frontend.model.ListCropParameters
	21, // 29: carbon.frontend.model.ModelService.GetNextEnabledCrops:input_type -> carbon.frontend.model.GetNextEnabledCropsRequest
	2,  // 30: carbon.frontend.model.ModelService.ListCaptureCrops:input_type -> carbon.frontend.model.ListCropParameters
	23, // 31: carbon.frontend.model.ModelService.GetNextCaptureCrops:input_type -> carbon.frontend.model.GetNextCaptureCropsRequest
	26, // 32: carbon.frontend.model.ModelService.GetNextSelectedCropID:input_type -> carbon.frontend.util.Timestamp
	1,  // 33: carbon.frontend.model.ModelService.SelectCrop:input_type -> carbon.frontend.model.SelectCropRequest
	10, // 34: carbon.frontend.model.ModelService.DownloadModel:input_type -> carbon.frontend.model.DownloadModelRequest
	11, // 35: carbon.frontend.model.ModelService.GetNextModelHistory:input_type -> carbon.frontend.model.ModelHistoryRequest
	11, // 36: carbon.frontend.model.ModelService.GetModelHistory:input_type -> carbon.frontend.model.ModelHistoryRequest
	15, // 37: carbon.frontend.model.ModelService.GetModelNicknames:input_type -> carbon.frontend.model.GetModelNicknamesRequest
	15, // 38: carbon.frontend.model.ModelService.GetNextModelNicknames:input_type -> carbon.frontend.model.GetModelNicknamesRequest
	17, // 39: carbon.frontend.model.ModelService.SetModelNickname:input_type -> carbon.frontend.model.SetModelNicknameRequest
	19, // 40: carbon.frontend.model.ModelService.RefreshDefaultModelParameters:input_type -> carbon.frontend.model.RefreshDefaultModelParametersRequest
	20, // 41: carbon.frontend.model.ModelService.SyncCropIDs:input_type -> carbon.frontend.model.SyncCropIDsRequest
	27, // 42: carbon.frontend.model.ModelService.TriggerDownload:input_type -> carbon.frontend.util.Empty
	27, // 43: carbon.frontend.model.ModelService.PinModel:output_type -> carbon.frontend.util.Empty
	27, // 44: carbon.frontend.model.ModelService.UnpinModel:output_type -> carbon.frontend.util.Empty
	9,  // 45: carbon.frontend.model.ModelService.GetNextModelState:output_type -> carbon.frontend.model.GetNextModelStateResponse
	9,  // 46: carbon.frontend.model.ModelService.GetNextAllModelState:output_type -> carbon.frontend.model.GetNextModelStateResponse
	27, // 47: carbon.frontend.model.ModelService.UpdateModel:output_type -> carbon.frontend.util.Empty
	4,  // 48: carbon.frontend.model.ModelService.ListEnabledCrops:output_type -> carbon.frontend.model.EnabledCropList
	22, // 49: carbon.frontend.model.ModelService.GetNextEnabledCrops:output_type -> carbon.frontend.model.GetNextEnabledCropsResponse
	4,  // 50: carbon.frontend.model.ModelService.ListCaptureCrops:output_type -> carbon.frontend.model.EnabledCropList
	24, // 51: carbon.frontend.model.ModelService.GetNextCaptureCrops:output_type -> carbon.frontend.model.GetNextCaptureCropsResponse
	5,  // 52: carbon.frontend.model.ModelService.GetNextSelectedCropID:output_type -> carbon.frontend.model.GetNextSelectedCropIDResponse
	27, // 53: carbon.frontend.model.ModelService.SelectCrop:output_type -> carbon.frontend.util.Empty
	27, // 54: carbon.frontend.model.ModelService.DownloadModel:output_type -> carbon.frontend.util.Empty
	14, // 55: carbon.frontend.model.ModelService.GetNextModelHistory:output_type -> carbon.frontend.model.ModelHistoryResponse
	14, // 56: carbon.frontend.model.ModelService.GetModelHistory:output_type -> carbon.frontend.model.ModelHistoryResponse
	16, // 57: carbon.frontend.model.ModelService.GetModelNicknames:output_type -> carbon.frontend.model.GetModelNicknamesResponse
	16, // 58: carbon.frontend.model.ModelService.GetNextModelNicknames:output_type -> carbon.frontend.model.GetModelNicknamesResponse
	27, // 59: carbon.frontend.model.ModelService.SetModelNickname:output_type -> carbon.frontend.util.Empty
	27, // 60: carbon.frontend.model.ModelService.RefreshDefaultModelParameters:output_type -> carbon.frontend.util.Empty
	27, // 61: carbon.frontend.model.ModelService.SyncCropIDs:output_type -> carbon.frontend.util.Empty
	27, // 62: carbon.frontend.model.ModelService.TriggerDownload:output_type -> carbon.frontend.util.Empty
	43, // [43:63] is the sub-list for method output_type
	23, // [23:43] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_frontend_proto_model_proto_init() }
func file_frontend_proto_model_proto_init() {
	if File_frontend_proto_model_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectCropRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCropParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnabledCrop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnabledCropList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextSelectedCropIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnpinModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextModelStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextModelStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelEventTypeMatcher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelNicknamesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelNicknamesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetModelNicknameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CropModelPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDefaultModelParametersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncCropIDsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextEnabledCropsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextEnabledCropsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCaptureCropsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_model_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCaptureCropsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_model_proto_goTypes,
		DependencyIndexes: file_frontend_proto_model_proto_depIdxs,
		MessageInfos:      file_frontend_proto_model_proto_msgTypes,
	}.Build()
	File_frontend_proto_model_proto = out.File
	file_frontend_proto_model_proto_rawDesc = nil
	file_frontend_proto_model_proto_goTypes = nil
	file_frontend_proto_model_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/calibration.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ColorCalibrationValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Red   float32 `protobuf:"fixed32,1,opt,name=red,proto3" json:"red,omitempty"`
	Green float32 `protobuf:"fixed32,2,opt,name=green,proto3" json:"green,omitempty"`
	Blue  float32 `protobuf:"fixed32,3,opt,name=blue,proto3" json:"blue,omitempty"`
	CamId string  `protobuf:"bytes,4,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *ColorCalibrationValues) Reset() {
	*x = ColorCalibrationValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_calibration_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ColorCalibrationValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ColorCalibrationValues) ProtoMessage() {}

func (x *ColorCalibrationValues) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_calibration_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ColorCalibrationValues.ProtoReflect.Descriptor instead.
func (*ColorCalibrationValues) Descriptor() ([]byte, []int) {
	return file_frontend_proto_calibration_proto_rawDescGZIP(), []int{0}
}

func (x *ColorCalibrationValues) GetRed() float32 {
	if x != nil {
		return x.Red
	}
	return 0
}

func (x *ColorCalibrationValues) GetGreen() float32 {
	if x != nil {
		return x.Green
	}
	return 0
}

func (x *ColorCalibrationValues) GetBlue() float32 {
	if x != nil {
		return x.Blue
	}
	return 0
}

func (x *ColorCalibrationValues) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

var File_frontend_proto_calibration_proto protoreflect.FileDescriptor

var file_frontend_proto_calibration_proto_rawDesc = []byte{
	0x0a, 0x20, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x21, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a,
	0x16, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x72, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x65,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x62, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x62,
	0x6c, 0x75, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x32, 0xff, 0x01, 0x0a, 0x12, 0x43,
	0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x79, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43,
	0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x6e, 0x0a, 0x14,
	0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x61, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x61,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_calibration_proto_rawDescOnce sync.Once
	file_frontend_proto_calibration_proto_rawDescData = file_frontend_proto_calibration_proto_rawDesc
)

func file_frontend_proto_calibration_proto_rawDescGZIP() []byte {
	file_frontend_proto_calibration_proto_rawDescOnce.Do(func() {
		file_frontend_proto_calibration_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_calibration_proto_rawDescData)
	})
	return file_frontend_proto_calibration_proto_rawDescData
}

var file_frontend_proto_calibration_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_frontend_proto_calibration_proto_goTypes = []interface{}{
	(*ColorCalibrationValues)(nil), // 0: carbon.frontend.color_calibration.ColorCalibrationValues
	(*CameraRequest)(nil),          // 1: carbon.frontend.camera.CameraRequest
	(*Empty)(nil),                  // 2: carbon.frontend.util.Empty
}
var file_frontend_proto_calibration_proto_depIdxs = []int32{
	1, // 0: carbon.frontend.color_calibration.CalibrationService.StartColorCalibration:input_type -> carbon.frontend.camera.CameraRequest
	0, // 1: carbon.frontend.color_calibration.CalibrationService.SaveColorCalibration:input_type -> carbon.frontend.color_calibration.ColorCalibrationValues
	0, // 2: carbon.frontend.color_calibration.CalibrationService.StartColorCalibration:output_type -> carbon.frontend.color_calibration.ColorCalibrationValues
	2, // 3: carbon.frontend.color_calibration.CalibrationService.SaveColorCalibration:output_type -> carbon.frontend.util.Empty
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_frontend_proto_calibration_proto_init() }
func file_frontend_proto_calibration_proto_init() {
	if File_frontend_proto_calibration_proto != nil {
		return
	}
	file_frontend_proto_camera_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_calibration_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ColorCalibrationValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_calibration_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_calibration_proto_goTypes,
		DependencyIndexes: file_frontend_proto_calibration_proto_depIdxs,
		MessageInfos:      file_frontend_proto_calibration_proto_msgTypes,
	}.Build()
	File_frontend_proto_calibration_proto = out.File
	file_frontend_proto_calibration_proto_rawDesc = nil
	file_frontend_proto_calibration_proto_goTypes = nil
	file_frontend_proto_calibration_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/plant_captcha.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	almanac "github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlantCaptchaUploadState int32

const (
	PlantCaptchaUploadState_NONE        PlantCaptchaUploadState = 0
	PlantCaptchaUploadState_IN_PROGRESS PlantCaptchaUploadState = 1
	PlantCaptchaUploadState_DONE        PlantCaptchaUploadState = 2
)

// Enum value maps for PlantCaptchaUploadState.
var (
	PlantCaptchaUploadState_name = map[int32]string{
		0: "NONE",
		1: "IN_PROGRESS",
		2: "DONE",
	}
	PlantCaptchaUploadState_value = map[string]int32{
		"NONE":        0,
		"IN_PROGRESS": 1,
		"DONE":        2,
	}
)

func (x PlantCaptchaUploadState) Enum() *PlantCaptchaUploadState {
	p := new(PlantCaptchaUploadState)
	*p = x
	return p
}

func (x PlantCaptchaUploadState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlantCaptchaUploadState) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_plant_captcha_proto_enumTypes[0].Descriptor()
}

func (PlantCaptchaUploadState) Type() protoreflect.EnumType {
	return &file_frontend_proto_plant_captcha_proto_enumTypes[0]
}

func (x PlantCaptchaUploadState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlantCaptchaUploadState.Descriptor instead.
func (PlantCaptchaUploadState) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{0}
}

type PlantLabelAlgorithmFailureReason int32

const (
	PlantLabelAlgorithmFailureReason_NO_FAILURE       PlantLabelAlgorithmFailureReason = 0
	PlantLabelAlgorithmFailureReason_METRICS_NOT_MET  PlantLabelAlgorithmFailureReason = 1
	PlantLabelAlgorithmFailureReason_NOT_ENOUGH_ITEMS PlantLabelAlgorithmFailureReason = 2
)

// Enum value maps for PlantLabelAlgorithmFailureReason.
var (
	PlantLabelAlgorithmFailureReason_name = map[int32]string{
		0: "NO_FAILURE",
		1: "METRICS_NOT_MET",
		2: "NOT_ENOUGH_ITEMS",
	}
	PlantLabelAlgorithmFailureReason_value = map[string]int32{
		"NO_FAILURE":       0,
		"METRICS_NOT_MET":  1,
		"NOT_ENOUGH_ITEMS": 2,
	}
)

func (x PlantLabelAlgorithmFailureReason) Enum() *PlantLabelAlgorithmFailureReason {
	p := new(PlantLabelAlgorithmFailureReason)
	*p = x
	return p
}

func (x PlantLabelAlgorithmFailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlantLabelAlgorithmFailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_plant_captcha_proto_enumTypes[1].Descriptor()
}

func (PlantLabelAlgorithmFailureReason) Type() protoreflect.EnumType {
	return &file_frontend_proto_plant_captcha_proto_enumTypes[1]
}

func (x PlantLabelAlgorithmFailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlantLabelAlgorithmFailureReason.Descriptor instead.
func (PlantLabelAlgorithmFailureReason) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{1}
}

type PlantCaptcha struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ModelId     string  `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	CropId      string  `protobuf:"bytes,3,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	CropName    string  `protobuf:"bytes,4,opt,name=crop_name,json=cropName,proto3" json:"crop_name,omitempty"`
	StartTimeMs int64   `protobuf:"varint,5,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`
	RowsUsed    []int32 `protobuf:"varint,6,rep,packed,name=rows_used,json=rowsUsed,proto3" json:"rows_used,omitempty"`
}

func (x *PlantCaptcha) Reset() {
	*x = PlantCaptcha{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptcha) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptcha) ProtoMessage() {}

func (x *PlantCaptcha) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptcha.ProtoReflect.Descriptor instead.
func (*PlantCaptcha) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{0}
}

func (x *PlantCaptcha) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlantCaptcha) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *PlantCaptcha) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *PlantCaptcha) GetCropName() string {
	if x != nil {
		return x.CropName
	}
	return ""
}

func (x *PlantCaptcha) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *PlantCaptcha) GetRowsUsed() []int32 {
	if x != nil {
		return x.RowsUsed
	}
	return nil
}

type StartPlantCaptchaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlantCaptcha *PlantCaptcha `protobuf:"bytes,1,opt,name=plant_captcha,json=plantCaptcha,proto3" json:"plant_captcha,omitempty"`
}

func (x *StartPlantCaptchaRequest) Reset() {
	*x = StartPlantCaptchaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartPlantCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPlantCaptchaRequest) ProtoMessage() {}

func (x *StartPlantCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPlantCaptchaRequest.ProtoReflect.Descriptor instead.
func (*StartPlantCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{1}
}

func (x *StartPlantCaptchaRequest) GetPlantCaptcha() *PlantCaptcha {
	if x != nil {
		return x.PlantCaptcha
	}
	return nil
}

type StartPlantCaptchaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartPlantCaptchaResponse) Reset() {
	*x = StartPlantCaptchaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartPlantCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPlantCaptchaResponse) ProtoMessage() {}

func (x *StartPlantCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPlantCaptchaResponse.ProtoReflect.Descriptor instead.
func (*StartPlantCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{2}
}

type GetNextPlantCaptchaStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextPlantCaptchaStatusRequest) Reset() {
	*x = GetNextPlantCaptchaStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPlantCaptchaStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPlantCaptchaStatusRequest) ProtoMessage() {}

func (x *GetNextPlantCaptchaStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPlantCaptchaStatusRequest.ProtoReflect.Descriptor instead.
func (*GetNextPlantCaptchaStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextPlantCaptchaStatusRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextPlantCaptchaStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts            *Timestamp                       `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Status        weed_tracking.PlantCaptchaStatus `protobuf:"varint,2,opt,name=status,proto3,enum=weed_tracking.PlantCaptchaStatus" json:"status,omitempty"`
	TotalImages   int32                            `protobuf:"varint,3,opt,name=total_images,json=totalImages,proto3" json:"total_images,omitempty"`
	ImagesTaken   int32                            `protobuf:"varint,4,opt,name=images_taken,json=imagesTaken,proto3" json:"images_taken,omitempty"`
	MetadataTaken int32                            `protobuf:"varint,5,opt,name=metadata_taken,json=metadataTaken,proto3" json:"metadata_taken,omitempty"`
}

func (x *GetNextPlantCaptchaStatusResponse) Reset() {
	*x = GetNextPlantCaptchaStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPlantCaptchaStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPlantCaptchaStatusResponse) ProtoMessage() {}

func (x *GetNextPlantCaptchaStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPlantCaptchaStatusResponse.ProtoReflect.Descriptor instead.
func (*GetNextPlantCaptchaStatusResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{4}
}

func (x *GetNextPlantCaptchaStatusResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextPlantCaptchaStatusResponse) GetStatus() weed_tracking.PlantCaptchaStatus {
	if x != nil {
		return x.Status
	}
	return weed_tracking.PlantCaptchaStatus(0)
}

func (x *GetNextPlantCaptchaStatusResponse) GetTotalImages() int32 {
	if x != nil {
		return x.TotalImages
	}
	return 0
}

func (x *GetNextPlantCaptchaStatusResponse) GetImagesTaken() int32 {
	if x != nil {
		return x.ImagesTaken
	}
	return 0
}

func (x *GetNextPlantCaptchaStatusResponse) GetMetadataTaken() int32 {
	if x != nil {
		return x.MetadataTaken
	}
	return 0
}

type GetNextPlantCaptchasListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextPlantCaptchasListRequest) Reset() {
	*x = GetNextPlantCaptchasListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPlantCaptchasListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPlantCaptchasListRequest) ProtoMessage() {}

func (x *GetNextPlantCaptchasListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPlantCaptchasListRequest.ProtoReflect.Descriptor instead.
func (*GetNextPlantCaptchasListRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{5}
}

func (x *GetNextPlantCaptchasListRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type PlantCaptchaListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlantCaptcha    *PlantCaptcha `protobuf:"bytes,1,opt,name=plant_captcha,json=plantCaptcha,proto3" json:"plant_captcha,omitempty"`
	ImagesTaken     int32         `protobuf:"varint,2,opt,name=images_taken,json=imagesTaken,proto3" json:"images_taken,omitempty"`
	ImagesProcessed int32         `protobuf:"varint,3,opt,name=images_processed,json=imagesProcessed,proto3" json:"images_processed,omitempty"`
}

func (x *PlantCaptchaListItem) Reset() {
	*x = PlantCaptchaListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaListItem) ProtoMessage() {}

func (x *PlantCaptchaListItem) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaListItem.ProtoReflect.Descriptor instead.
func (*PlantCaptchaListItem) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{6}
}

func (x *PlantCaptchaListItem) GetPlantCaptcha() *PlantCaptcha {
	if x != nil {
		return x.PlantCaptcha
	}
	return nil
}

func (x *PlantCaptchaListItem) GetImagesTaken() int32 {
	if x != nil {
		return x.ImagesTaken
	}
	return 0
}

func (x *PlantCaptchaListItem) GetImagesProcessed() int32 {
	if x != nil {
		return x.ImagesProcessed
	}
	return 0
}

type GetNextPlantCaptchasListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts            *Timestamp              `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	PlantCaptchas []*PlantCaptchaListItem `protobuf:"bytes,2,rep,name=plant_captchas,json=plantCaptchas,proto3" json:"plant_captchas,omitempty"`
}

func (x *GetNextPlantCaptchasListResponse) Reset() {
	*x = GetNextPlantCaptchasListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPlantCaptchasListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPlantCaptchasListResponse) ProtoMessage() {}

func (x *GetNextPlantCaptchasListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPlantCaptchasListResponse.ProtoReflect.Descriptor instead.
func (*GetNextPlantCaptchasListResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{7}
}

func (x *GetNextPlantCaptchasListResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextPlantCaptchasListResponse) GetPlantCaptchas() []*PlantCaptchaListItem {
	if x != nil {
		return x.PlantCaptchas
	}
	return nil
}

type DeletePlantCaptchaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeletePlantCaptchaRequest) Reset() {
	*x = DeletePlantCaptchaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePlantCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlantCaptchaRequest) ProtoMessage() {}

func (x *DeletePlantCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlantCaptchaRequest.ProtoReflect.Descriptor instead.
func (*DeletePlantCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePlantCaptchaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetPlantCaptchaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetPlantCaptchaRequest) Reset() {
	*x = GetPlantCaptchaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlantCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlantCaptchaRequest) ProtoMessage() {}

func (x *GetPlantCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlantCaptchaRequest.ProtoReflect.Descriptor instead.
func (*GetPlantCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{9}
}

func (x *GetPlantCaptchaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type PlantCaptchaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl            string                                    `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Metadata            *weed_tracking.PlantCaptchaItemMetadata   `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	AdditionalImageUrls []string                                  `protobuf:"bytes,3,rep,name=additional_image_urls,json=additionalImageUrls,proto3" json:"additional_image_urls,omitempty"`
	AdditionalMetadatas []*weed_tracking.PlantCaptchaItemMetadata `protobuf:"bytes,4,rep,name=additional_metadatas,json=additionalMetadatas,proto3" json:"additional_metadatas,omitempty"`
}

func (x *PlantCaptchaItem) Reset() {
	*x = PlantCaptchaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaItem) ProtoMessage() {}

func (x *PlantCaptchaItem) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaItem.ProtoReflect.Descriptor instead.
func (*PlantCaptchaItem) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{10}
}

func (x *PlantCaptchaItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *PlantCaptchaItem) GetMetadata() *weed_tracking.PlantCaptchaItemMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *PlantCaptchaItem) GetAdditionalImageUrls() []string {
	if x != nil {
		return x.AdditionalImageUrls
	}
	return nil
}

func (x *PlantCaptchaItem) GetAdditionalMetadatas() []*weed_tracking.PlantCaptchaItemMetadata {
	if x != nil {
		return x.AdditionalMetadatas
	}
	return nil
}

type GetPlantCaptchaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlantCaptcha *PlantCaptcha       `protobuf:"bytes,1,opt,name=plant_captcha,json=plantCaptcha,proto3" json:"plant_captcha,omitempty"`
	Items        []*PlantCaptchaItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetPlantCaptchaResponse) Reset() {
	*x = GetPlantCaptchaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlantCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlantCaptchaResponse) ProtoMessage() {}

func (x *GetPlantCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlantCaptchaResponse.ProtoReflect.Descriptor instead.
func (*GetPlantCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{11}
}

func (x *GetPlantCaptchaResponse) GetPlantCaptcha() *PlantCaptcha {
	if x != nil {
		return x.PlantCaptcha
	}
	return nil
}

func (x *GetPlantCaptchaResponse) GetItems() []*PlantCaptchaItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type StartPlantCaptchaUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StartPlantCaptchaUploadRequest) Reset() {
	*x = StartPlantCaptchaUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartPlantCaptchaUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPlantCaptchaUploadRequest) ProtoMessage() {}

func (x *StartPlantCaptchaUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPlantCaptchaUploadRequest.ProtoReflect.Descriptor instead.
func (*StartPlantCaptchaUploadRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{12}
}

func (x *StartPlantCaptchaUploadRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetNextPlantCaptchaUploadStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts   *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Name string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetNextPlantCaptchaUploadStateRequest) Reset() {
	*x = GetNextPlantCaptchaUploadStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPlantCaptchaUploadStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPlantCaptchaUploadStateRequest) ProtoMessage() {}

func (x *GetNextPlantCaptchaUploadStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPlantCaptchaUploadStateRequest.ProtoReflect.Descriptor instead.
func (*GetNextPlantCaptchaUploadStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{13}
}

func (x *GetNextPlantCaptchaUploadStateRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextPlantCaptchaUploadStateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetNextPlantCaptchaUploadStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts          *Timestamp              `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	UploadState PlantCaptchaUploadState `protobuf:"varint,2,opt,name=upload_state,json=uploadState,proto3,enum=carbon.frontend.plant_captcha.PlantCaptchaUploadState" json:"upload_state,omitempty"`
	Percent     int32                   `protobuf:"varint,3,opt,name=percent,proto3" json:"percent,omitempty"`
}

func (x *GetNextPlantCaptchaUploadStateResponse) Reset() {
	*x = GetNextPlantCaptchaUploadStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPlantCaptchaUploadStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPlantCaptchaUploadStateResponse) ProtoMessage() {}

func (x *GetNextPlantCaptchaUploadStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPlantCaptchaUploadStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextPlantCaptchaUploadStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{14}
}

func (x *GetNextPlantCaptchaUploadStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextPlantCaptchaUploadStateResponse) GetUploadState() PlantCaptchaUploadState {
	if x != nil {
		return x.UploadState
	}
	return PlantCaptchaUploadState_NONE
}

func (x *GetNextPlantCaptchaUploadStateResponse) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

type PlantCaptchaItemResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string                                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserPrediction weed_tracking.PlantCaptchaUserPrediction `protobuf:"varint,2,opt,name=user_prediction,json=userPrediction,proto3,enum=weed_tracking.PlantCaptchaUserPrediction" json:"user_prediction,omitempty"`
}

func (x *PlantCaptchaItemResult) Reset() {
	*x = PlantCaptchaItemResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaItemResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaItemResult) ProtoMessage() {}

func (x *PlantCaptchaItemResult) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaItemResult.ProtoReflect.Descriptor instead.
func (*PlantCaptchaItemResult) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{15}
}

func (x *PlantCaptchaItemResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PlantCaptchaItemResult) GetUserPrediction() weed_tracking.PlantCaptchaUserPrediction {
	if x != nil {
		return x.UserPrediction
	}
	return weed_tracking.PlantCaptchaUserPrediction(0)
}

type SubmitPlantCaptchaResultsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string                    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Results []*PlantCaptchaItemResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *SubmitPlantCaptchaResultsRequest) Reset() {
	*x = SubmitPlantCaptchaResultsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitPlantCaptchaResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitPlantCaptchaResultsRequest) ProtoMessage() {}

func (x *SubmitPlantCaptchaResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitPlantCaptchaResultsRequest.ProtoReflect.Descriptor instead.
func (*SubmitPlantCaptchaResultsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{16}
}

func (x *SubmitPlantCaptchaResultsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubmitPlantCaptchaResultsRequest) GetResults() []*PlantCaptchaItemResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type GetPlantCaptchaItemResultsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id   []string `protobuf:"bytes,2,rep,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPlantCaptchaItemResultsRequest) Reset() {
	*x = GetPlantCaptchaItemResultsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlantCaptchaItemResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlantCaptchaItemResultsRequest) ProtoMessage() {}

func (x *GetPlantCaptchaItemResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlantCaptchaItemResultsRequest.ProtoReflect.Descriptor instead.
func (*GetPlantCaptchaItemResultsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{17}
}

func (x *GetPlantCaptchaItemResultsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPlantCaptchaItemResultsRequest) GetId() []string {
	if x != nil {
		return x.Id
	}
	return nil
}

type GetPlantCaptchaItemResultsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*PlantCaptchaItemResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *GetPlantCaptchaItemResultsResponse) Reset() {
	*x = GetPlantCaptchaItemResultsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlantCaptchaItemResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlantCaptchaItemResultsResponse) ProtoMessage() {}

func (x *GetPlantCaptchaItemResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlantCaptchaItemResultsResponse.ProtoReflect.Descriptor instead.
func (*GetPlantCaptchaItemResultsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{18}
}

func (x *GetPlantCaptchaItemResultsResponse) GetResults() []*PlantCaptchaItemResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type CalculatePlantCaptchaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CalculatePlantCaptchaRequest) Reset() {
	*x = CalculatePlantCaptchaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePlantCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePlantCaptchaRequest) ProtoMessage() {}

func (x *CalculatePlantCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePlantCaptchaRequest.ProtoReflect.Descriptor instead.
func (*CalculatePlantCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{19}
}

func (x *CalculatePlantCaptchaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CalculatePlantCaptchaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelinatorConfig *almanac.ModelinatorConfig       `protobuf:"bytes,1,opt,name=modelinator_config,json=modelinatorConfig,proto3" json:"modelinator_config,omitempty"`
	Succeeded         bool                             `protobuf:"varint,2,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	FailureReason     PlantLabelAlgorithmFailureReason `protobuf:"varint,3,opt,name=failure_reason,json=failureReason,proto3,enum=carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason" json:"failure_reason,omitempty"`
}

func (x *CalculatePlantCaptchaResponse) Reset() {
	*x = CalculatePlantCaptchaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePlantCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePlantCaptchaResponse) ProtoMessage() {}

func (x *CalculatePlantCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePlantCaptchaResponse.ProtoReflect.Descriptor instead.
func (*CalculatePlantCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{20}
}

func (x *CalculatePlantCaptchaResponse) GetModelinatorConfig() *almanac.ModelinatorConfig {
	if x != nil {
		return x.ModelinatorConfig
	}
	return nil
}

func (x *CalculatePlantCaptchaResponse) GetSucceeded() bool {
	if x != nil {
		return x.Succeeded
	}
	return false
}

func (x *CalculatePlantCaptchaResponse) GetFailureReason() PlantLabelAlgorithmFailureReason {
	if x != nil {
		return x.FailureReason
	}
	return PlantLabelAlgorithmFailureReason_NO_FAILURE
}

type PlantCaptchaResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label    weed_tracking.PlantCaptchaUserPrediction `protobuf:"varint,1,opt,name=label,proto3,enum=weed_tracking.PlantCaptchaUserPrediction" json:"label,omitempty"`
	Metadata *weed_tracking.PlantCaptchaItemMetadata  `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *PlantCaptchaResult) Reset() {
	*x = PlantCaptchaResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaResult) ProtoMessage() {}

func (x *PlantCaptchaResult) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaResult.ProtoReflect.Descriptor instead.
func (*PlantCaptchaResult) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{21}
}

func (x *PlantCaptchaResult) GetLabel() weed_tracking.PlantCaptchaUserPrediction {
	if x != nil {
		return x.Label
	}
	return weed_tracking.PlantCaptchaUserPrediction(0)
}

func (x *PlantCaptchaResult) GetMetadata() *weed_tracking.PlantCaptchaItemMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type PlantCaptchaResults struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentParameters               *almanac.ModelinatorConfig `protobuf:"bytes,1,opt,name=current_parameters,json=currentParameters,proto3" json:"current_parameters,omitempty"`
	CaptchaResults                  []*PlantCaptchaResult      `protobuf:"bytes,2,rep,name=captcha_results,json=captchaResults,proto3" json:"captcha_results,omitempty"`
	Algorithm                       string                     `protobuf:"bytes,3,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	GoalCropsTargeted               float32                    `protobuf:"fixed32,4,opt,name=goal_crops_targeted,json=goalCropsTargeted,proto3" json:"goal_crops_targeted,omitempty"`
	GoalWeedsTargeted               float32                    `protobuf:"fixed32,5,opt,name=goal_weeds_targeted,json=goalWeedsTargeted,proto3" json:"goal_weeds_targeted,omitempty"`
	GoalUnknownTargeted             float32                    `protobuf:"fixed32,6,opt,name=goal_unknown_targeted,json=goalUnknownTargeted,proto3" json:"goal_unknown_targeted,omitempty"`
	Almanac                         *almanac.AlmanacConfig     `protobuf:"bytes,7,opt,name=almanac,proto3" json:"almanac,omitempty"`
	MaxRecommendedMindoo            float32                    `protobuf:"fixed32,8,opt,name=max_recommended_mindoo,json=maxRecommendedMindoo,proto3" json:"max_recommended_mindoo,omitempty"`
	MinItemsForRecommendation       int32                      `protobuf:"varint,9,opt,name=min_items_for_recommendation,json=minItemsForRecommendation,proto3" json:"min_items_for_recommendation,omitempty"`
	UseWeedCategoriesForWeedLabels  bool                       `protobuf:"varint,10,opt,name=use_weed_categories_for_weed_labels,json=useWeedCategoriesForWeedLabels,proto3" json:"use_weed_categories_for_weed_labels,omitempty"`
	MinRecommendedMindoo            float32                    `protobuf:"fixed32,11,opt,name=min_recommended_mindoo,json=minRecommendedMindoo,proto3" json:"min_recommended_mindoo,omitempty"`
	MinRecommendedWeedThreshold     float32                    `protobuf:"fixed32,12,opt,name=min_recommended_weed_threshold,json=minRecommendedWeedThreshold,proto3" json:"min_recommended_weed_threshold,omitempty"`
	MaxRecommendedWeedThreshold     float32                    `protobuf:"fixed32,13,opt,name=max_recommended_weed_threshold,json=maxRecommendedWeedThreshold,proto3" json:"max_recommended_weed_threshold,omitempty"`
	MinRecommendedCropThreshold     float32                    `protobuf:"fixed32,14,opt,name=min_recommended_crop_threshold,json=minRecommendedCropThreshold,proto3" json:"min_recommended_crop_threshold,omitempty"`
	MaxRecommendedCropThreshold     float32                    `protobuf:"fixed32,15,opt,name=max_recommended_crop_threshold,json=maxRecommendedCropThreshold,proto3" json:"max_recommended_crop_threshold,omitempty"`
	MinDooForRecommendation         float32                    `protobuf:"fixed32,16,opt,name=min_doo_for_recommendation,json=minDooForRecommendation,proto3" json:"min_doo_for_recommendation,omitempty"`
	UseOtherAsTiebreaker            bool                       `protobuf:"varint,17,opt,name=use_other_as_tiebreaker,json=useOtherAsTiebreaker,proto3" json:"use_other_as_tiebreaker,omitempty"`
	LimitByCropsMissed              bool                       `protobuf:"varint,18,opt,name=limit_by_crops_missed,json=limitByCropsMissed,proto3" json:"limit_by_crops_missed,omitempty"`
	NumberOfCropConfigurations      int32                      `protobuf:"varint,19,opt,name=number_of_crop_configurations,json=numberOfCropConfigurations,proto3" json:"number_of_crop_configurations,omitempty"`
	Tiebreaker                      string                     `protobuf:"bytes,20,opt,name=tiebreaker,proto3" json:"tiebreaker,omitempty"`
	PadCropConfigurations           bool                       `protobuf:"varint,21,opt,name=pad_crop_configurations,json=padCropConfigurations,proto3" json:"pad_crop_configurations,omitempty"`
	MindooTiebreaker                string                     `protobuf:"bytes,22,opt,name=mindoo_tiebreaker,json=mindooTiebreaker,proto3" json:"mindoo_tiebreaker,omitempty"`
	UseBeneficialsAsCrops           bool                       `protobuf:"varint,23,opt,name=use_beneficials_as_crops,json=useBeneficialsAsCrops,proto3" json:"use_beneficials_as_crops,omitempty"`
	UseVolunteersAsWeeds            bool                       `protobuf:"varint,24,opt,name=use_volunteers_as_weeds,json=useVolunteersAsWeeds,proto3" json:"use_volunteers_as_weeds,omitempty"`
	TiebreakerStrategyThresholdWeed string                     `protobuf:"bytes,25,opt,name=tiebreaker_strategy_threshold_weed,json=tiebreakerStrategyThresholdWeed,proto3" json:"tiebreaker_strategy_threshold_weed,omitempty"`
	TiebreakerStrategyThresholdCrop string                     `protobuf:"bytes,26,opt,name=tiebreaker_strategy_threshold_crop,json=tiebreakerStrategyThresholdCrop,proto3" json:"tiebreaker_strategy_threshold_crop,omitempty"`
	TiebreakerStrategyMindooWeed    string                     `protobuf:"bytes,27,opt,name=tiebreaker_strategy_mindoo_weed,json=tiebreakerStrategyMindooWeed,proto3" json:"tiebreaker_strategy_mindoo_weed,omitempty"`
	TiebreakerStrategyMindooCrop    string                     `protobuf:"bytes,28,opt,name=tiebreaker_strategy_mindoo_crop,json=tiebreakerStrategyMindooCrop,proto3" json:"tiebreaker_strategy_mindoo_crop,omitempty"`
}

func (x *PlantCaptchaResults) Reset() {
	*x = PlantCaptchaResults{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaResults) ProtoMessage() {}

func (x *PlantCaptchaResults) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaResults.ProtoReflect.Descriptor instead.
func (*PlantCaptchaResults) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{22}
}

func (x *PlantCaptchaResults) GetCurrentParameters() *almanac.ModelinatorConfig {
	if x != nil {
		return x.CurrentParameters
	}
	return nil
}

func (x *PlantCaptchaResults) GetCaptchaResults() []*PlantCaptchaResult {
	if x != nil {
		return x.CaptchaResults
	}
	return nil
}

func (x *PlantCaptchaResults) GetAlgorithm() string {
	if x != nil {
		return x.Algorithm
	}
	return ""
}

func (x *PlantCaptchaResults) GetGoalCropsTargeted() float32 {
	if x != nil {
		return x.GoalCropsTargeted
	}
	return 0
}

func (x *PlantCaptchaResults) GetGoalWeedsTargeted() float32 {
	if x != nil {
		return x.GoalWeedsTargeted
	}
	return 0
}

func (x *PlantCaptchaResults) GetGoalUnknownTargeted() float32 {
	if x != nil {
		return x.GoalUnknownTargeted
	}
	return 0
}

func (x *PlantCaptchaResults) GetAlmanac() *almanac.AlmanacConfig {
	if x != nil {
		return x.Almanac
	}
	return nil
}

func (x *PlantCaptchaResults) GetMaxRecommendedMindoo() float32 {
	if x != nil {
		return x.MaxRecommendedMindoo
	}
	return 0
}

func (x *PlantCaptchaResults) GetMinItemsForRecommendation() int32 {
	if x != nil {
		return x.MinItemsForRecommendation
	}
	return 0
}

func (x *PlantCaptchaResults) GetUseWeedCategoriesForWeedLabels() bool {
	if x != nil {
		return x.UseWeedCategoriesForWeedLabels
	}
	return false
}

func (x *PlantCaptchaResults) GetMinRecommendedMindoo() float32 {
	if x != nil {
		return x.MinRecommendedMindoo
	}
	return 0
}

func (x *PlantCaptchaResults) GetMinRecommendedWeedThreshold() float32 {
	if x != nil {
		return x.MinRecommendedWeedThreshold
	}
	return 0
}

func (x *PlantCaptchaResults) GetMaxRecommendedWeedThreshold() float32 {
	if x != nil {
		return x.MaxRecommendedWeedThreshold
	}
	return 0
}

func (x *PlantCaptchaResults) GetMinRecommendedCropThreshold() float32 {
	if x != nil {
		return x.MinRecommendedCropThreshold
	}
	return 0
}

func (x *PlantCaptchaResults) GetMaxRecommendedCropThreshold() float32 {
	if x != nil {
		return x.MaxRecommendedCropThreshold
	}
	return 0
}

func (x *PlantCaptchaResults) GetMinDooForRecommendation() float32 {
	if x != nil {
		return x.MinDooForRecommendation
	}
	return 0
}

func (x *PlantCaptchaResults) GetUseOtherAsTiebreaker() bool {
	if x != nil {
		return x.UseOtherAsTiebreaker
	}
	return false
}

func (x *PlantCaptchaResults) GetLimitByCropsMissed() bool {
	if x != nil {
		return x.LimitByCropsMissed
	}
	return false
}

func (x *PlantCaptchaResults) GetNumberOfCropConfigurations() int32 {
	if x != nil {
		return x.NumberOfCropConfigurations
	}
	return 0
}

func (x *PlantCaptchaResults) GetTiebreaker() string {
	if x != nil {
		return x.Tiebreaker
	}
	return ""
}

func (x *PlantCaptchaResults) GetPadCropConfigurations() bool {
	if x != nil {
		return x.PadCropConfigurations
	}
	return false
}

func (x *PlantCaptchaResults) GetMindooTiebreaker() string {
	if x != nil {
		return x.MindooTiebreaker
	}
	return ""
}

func (x *PlantCaptchaResults) GetUseBeneficialsAsCrops() bool {
	if x != nil {
		return x.UseBeneficialsAsCrops
	}
	return false
}

func (x *PlantCaptchaResults) GetUseVolunteersAsWeeds() bool {
	if x != nil {
		return x.UseVolunteersAsWeeds
	}
	return false
}

func (x *PlantCaptchaResults) GetTiebreakerStrategyThresholdWeed() string {
	if x != nil {
		return x.TiebreakerStrategyThresholdWeed
	}
	return ""
}

func (x *PlantCaptchaResults) GetTiebreakerStrategyThresholdCrop() string {
	if x != nil {
		return x.TiebreakerStrategyThresholdCrop
	}
	return ""
}

func (x *PlantCaptchaResults) GetTiebreakerStrategyMindooWeed() string {
	if x != nil {
		return x.TiebreakerStrategyMindooWeed
	}
	return ""
}

func (x *PlantCaptchaResults) GetTiebreakerStrategyMindooCrop() string {
	if x != nil {
		return x.TiebreakerStrategyMindooCrop
	}
	return ""
}

type VeselkaPlantCaptchaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewModelParameters *almanac.ModelinatorConfig `protobuf:"bytes,1,opt,name=new_model_parameters,json=newModelParameters,proto3" json:"new_model_parameters,omitempty"`
	Succeeded          bool                       `protobuf:"varint,2,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
}

func (x *VeselkaPlantCaptchaResponse) Reset() {
	*x = VeselkaPlantCaptchaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VeselkaPlantCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VeselkaPlantCaptchaResponse) ProtoMessage() {}

func (x *VeselkaPlantCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VeselkaPlantCaptchaResponse.ProtoReflect.Descriptor instead.
func (*VeselkaPlantCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{23}
}

func (x *VeselkaPlantCaptchaResponse) GetNewModelParameters() *almanac.ModelinatorConfig {
	if x != nil {
		return x.NewModelParameters
	}
	return nil
}

func (x *VeselkaPlantCaptchaResponse) GetSucceeded() bool {
	if x != nil {
		return x.Succeeded
	}
	return false
}

type GetOriginalModelinatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetOriginalModelinatorConfigRequest) Reset() {
	*x = GetOriginalModelinatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOriginalModelinatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOriginalModelinatorConfigRequest) ProtoMessage() {}

func (x *GetOriginalModelinatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOriginalModelinatorConfigRequest.ProtoReflect.Descriptor instead.
func (*GetOriginalModelinatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{24}
}

func (x *GetOriginalModelinatorConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetOriginalModelinatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelinatorConfig *almanac.ModelinatorConfig `protobuf:"bytes,1,opt,name=modelinator_config,json=modelinatorConfig,proto3" json:"modelinator_config,omitempty"`
}

func (x *GetOriginalModelinatorConfigResponse) Reset() {
	*x = GetOriginalModelinatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOriginalModelinatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOriginalModelinatorConfigResponse) ProtoMessage() {}

func (x *GetOriginalModelinatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOriginalModelinatorConfigResponse.ProtoReflect.Descriptor instead.
func (*GetOriginalModelinatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{25}
}

func (x *GetOriginalModelinatorConfigResponse) GetModelinatorConfig() *almanac.ModelinatorConfig {
	if x != nil {
		return x.ModelinatorConfig
	}
	return nil
}

type GetCaptchaRowStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowStatus map[int32]*weed_tracking.PlantCaptchaStatusResponse `protobuf:"bytes,1,rep,name=row_status,json=rowStatus,proto3" json:"row_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetCaptchaRowStatusResponse) Reset() {
	*x = GetCaptchaRowStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaptchaRowStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaptchaRowStatusResponse) ProtoMessage() {}

func (x *GetCaptchaRowStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaptchaRowStatusResponse.ProtoReflect.Descriptor instead.
func (*GetCaptchaRowStatusResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{26}
}

func (x *GetCaptchaRowStatusResponse) GetRowStatus() map[int32]*weed_tracking.PlantCaptchaStatusResponse {
	if x != nil {
		return x.RowStatus
	}
	return nil
}

type CancelPlantCaptchaOnRowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId int32 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
}

func (x *CancelPlantCaptchaOnRowRequest) Reset() {
	*x = CancelPlantCaptchaOnRowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_plant_captcha_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPlantCaptchaOnRowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPlantCaptchaOnRowRequest) ProtoMessage() {}

func (x *CancelPlantCaptchaOnRowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_plant_captcha_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPlantCaptchaOnRowRequest.ProtoReflect.Descriptor instead.
func (*CancelPlantCaptchaOnRowRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_plant_captcha_proto_rawDescGZIP(), []int{27}
}

func (x *CancelPlantCaptchaOnRowRequest) GetRowId() int32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

var File_frontend_proto_plant_captcha_proto protoreflect.FileDescriptor

var file_frontend_proto_plant_captcha_proto_rawDesc = []byte{
	0x0a, 0x22, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x01, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x72, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x6f, 0x77, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x08, 0x72, 0x6f, 0x77, 0x73, 0x55, 0x73, 0x65, 0x64, 0x22, 0x6c, 0x0a, 0x18, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x0c, 0x70, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x22, 0x1b, 0x0a, 0x19, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x53, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0xfc, 0x01, 0x0a, 0x21,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02,
	0x74, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x54, 0x61,
	0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x61, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x54, 0x61, 0x6b, 0x65, 0x6e, 0x22, 0x52, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0xb6,
	0x01, 0x0a, 0x14, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x4c,
	0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x50, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x0c, 0x70, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x54, 0x61, 0x6b, 0x65, 0x6e, 0x12, 0x29, 0x0a, 0x10,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x22, 0xaf, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x5a, 0x0a,
	0x0e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e,
	0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x73, 0x22, 0x2f, 0x0a, 0x19, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2c, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x84, 0x02, 0x0a, 0x10, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x43, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x32, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x5a, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x13, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x73, 0x22,
	0xb2, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0d, 0x70,
	0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52,
	0x0c, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x45, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x34, 0x0a, 0x1e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6c, 0x0a, 0x25, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x59, 0x0a, 0x0c, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61,
	0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74,
	0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0x7c, 0x0a, 0x16, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x52, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x20, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x4f, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x22, 0x47, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70,
	0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x75, 0x0a, 0x22, 0x47, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4f, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x22, 0x32, 0x0a, 0x1c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xfe, 0x01, 0x0a, 0x1d, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x66,
	0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x12, 0x50, 0x6c, 0x61, 0x6e, 0x74,
	0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3f, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x43,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65,
	0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xd1, 0x0d, 0x0a, 0x13, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70,
	0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x5a, 0x0a, 0x0f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x50, 0x6c,
	0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x2e,
	0x0a, 0x13, 0x67, 0x6f, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x67, 0x6f, 0x61,
	0x6c, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x67, 0x6f, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x67, 0x6f, 0x61,
	0x6c, 0x57, 0x65, 0x65, 0x64, 0x73, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x12, 0x32,
	0x0a, 0x15, 0x67, 0x6f, 0x61, 0x6c, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x67,
	0x6f, 0x61, 0x6c, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x65, 0x64, 0x12, 0x3e, 0x0a, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x12, 0x34, 0x0a, 0x16, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x14, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x4d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x12, 0x3f, 0x0a, 0x1c, 0x6d, 0x69, 0x6e, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19,
	0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x23, 0x75, 0x73, 0x65,
	0x5f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x75, 0x73, 0x65, 0x57, 0x65, 0x65, 0x64, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x57, 0x65, 0x65, 0x64,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x64, 0x6f, 0x6f,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x12, 0x43, 0x0a, 0x1e,
	0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x1b, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x57, 0x65, 0x65, 0x64, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x12, 0x43, 0x0a, 0x1e, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1b, 0x6d, 0x61, 0x78, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x57, 0x65, 0x65, 0x64, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x43, 0x0a, 0x1e, 0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1b,
	0x6d, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x72,
	0x6f, 0x70, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x43, 0x0a, 0x1e, 0x6d,
	0x61, 0x78, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x63,
	0x72, 0x6f, 0x70, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x1b, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x12, 0x3b, 0x0a, 0x1a, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x6f, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x6d, 0x69, 0x6e, 0x44, 0x6f, 0x6f, 0x46, 0x6f, 0x72, 0x52,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a,
	0x17, 0x75, 0x73, 0x65, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x73, 0x5f, 0x74, 0x69,
	0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x75, 0x73, 0x65, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x73, 0x54, 0x69, 0x65, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x62, 0x79,
	0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x79, 0x43, 0x72, 0x6f, 0x70,
	0x73, 0x4d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x12, 0x41, 0x0a, 0x1d, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1a,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x72, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69,
	0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x74, 0x69, 0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x61,
	0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x70, 0x61, 0x64,
	0x43, 0x72, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x5f, 0x74, 0x69, 0x65,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d,
	0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x54, 0x69, 0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12,
	0x37, 0x0a, 0x18, 0x75, 0x73, 0x65, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61,
	0x6c, 0x73, 0x5f, 0x61, 0x73, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x15, 0x75, 0x73, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c,
	0x73, 0x41, 0x73, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x75, 0x73, 0x65, 0x5f,
	0x76, 0x6f, 0x6c, 0x75, 0x6e, 0x74, 0x65, 0x65, 0x72, 0x73, 0x5f, 0x61, 0x73, 0x5f, 0x77, 0x65,
	0x65, 0x64, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x75, 0x73, 0x65, 0x56, 0x6f,
	0x6c, 0x75, 0x6e, 0x74, 0x65, 0x65, 0x72, 0x73, 0x41, 0x73, 0x57, 0x65, 0x65, 0x64, 0x73, 0x12,
	0x4b, 0x0a, 0x22, 0x74, 0x69, 0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1f, 0x74, 0x69, 0x65,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x57, 0x65, 0x65, 0x64, 0x12, 0x4b, 0x0a, 0x22,
	0x74, 0x69, 0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x72,
	0x6f, 0x70, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1f, 0x74, 0x69, 0x65, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x45, 0x0a, 0x1f, 0x74, 0x69, 0x65,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x5f, 0x6d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1c, 0x74, 0x69, 0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x57, 0x65, 0x65, 0x64,
	0x12, 0x45, 0x0a, 0x1f, 0x74, 0x69, 0x65, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x6d, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x5f, 0x63,
	0x72, 0x6f, 0x70, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x74, 0x69, 0x65, 0x62, 0x72,
	0x65, 0x61, 0x6b, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4d, 0x69, 0x6e,
	0x64, 0x6f, 0x6f, 0x43, 0x72, 0x6f, 0x70, 0x22, 0x97, 0x01, 0x0a, 0x1b, 0x56, 0x65, 0x73, 0x65,
	0x6c, 0x6b, 0x61, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x6e, 0x65, 0x77, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x12, 0x6e, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65,
	0x64, 0x22, 0x39, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x7f, 0x0a, 0x24,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e,
	0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xf0, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a,
	0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x72, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x67, 0x0a, 0x0e, 0x52, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3f, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74,
	0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x37, 0x0a, 0x1e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x4f, 0x6e, 0x52, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x2a, 0x3e, 0x0a, 0x17, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x44, 0x4f, 0x4e, 0x45, 0x10, 0x02, 0x2a, 0x5d, 0x0a, 0x20, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x0e, 0x0a,
	0x0a, 0x4e, 0x4f, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x45, 0x54,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48,
	0x5f, 0x49, 0x54, 0x45, 0x4d, 0x53, 0x10, 0x02, 0x32, 0x89, 0x0f, 0x0a, 0x13, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x86, 0x01, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e,
	0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9e, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f,
	0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f,
	0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f,
	0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x38,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x80, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e,
	0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x1b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x75, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0x3d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0xad, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74,
	0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x44, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x79, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x3f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e,
	0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c,
	0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92,
	0x01, 0x0a, 0x15, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x6e,
	0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70,
	0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a,
	0x17, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x4f, 0x6e, 0x52, 0x6f, 0x77, 0x12, 0x3d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x4f, 0x6e, 0x52, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_plant_captcha_proto_rawDescOnce sync.Once
	file_frontend_proto_plant_captcha_proto_rawDescData = file_frontend_proto_plant_captcha_proto_rawDesc
)

func file_frontend_proto_plant_captcha_proto_rawDescGZIP() []byte {
	file_frontend_proto_plant_captcha_proto_rawDescOnce.Do(func() {
		file_frontend_proto_plant_captcha_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_plant_captcha_proto_rawDescData)
	})
	return file_frontend_proto_plant_captcha_proto_rawDescData
}

var file_frontend_proto_plant_captcha_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_proto_plant_captcha_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_frontend_proto_plant_captcha_proto_goTypes = []interface{}{
	(PlantCaptchaUploadState)(0),                     // 0: carbon.frontend.plant_captcha.PlantCaptchaUploadState
	(PlantLabelAlgorithmFailureReason)(0),            // 1: carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason
	(*PlantCaptcha)(nil),                             // 2: carbon.frontend.plant_captcha.PlantCaptcha
	(*StartPlantCaptchaRequest)(nil),                 // 3: carbon.frontend.plant_captcha.StartPlantCaptchaRequest
	(*StartPlantCaptchaResponse)(nil),                // 4: carbon.frontend.plant_captcha.StartPlantCaptchaResponse
	(*GetNextPlantCaptchaStatusRequest)(nil),         // 5: carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest
	(*GetNextPlantCaptchaStatusResponse)(nil),        // 6: carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse
	(*GetNextPlantCaptchasListRequest)(nil),          // 7: carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest
	(*PlantCaptchaListItem)(nil),                     // 8: carbon.frontend.plant_captcha.PlantCaptchaListItem
	(*GetNextPlantCaptchasListResponse)(nil),         // 9: carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse
	(*DeletePlantCaptchaRequest)(nil),                // 10: carbon.frontend.plant_captcha.DeletePlantCaptchaRequest
	(*GetPlantCaptchaRequest)(nil),                   // 11: carbon.frontend.plant_captcha.GetPlantCaptchaRequest
	(*PlantCaptchaItem)(nil),                         // 12: carbon.frontend.plant_captcha.PlantCaptchaItem
	(*GetPlantCaptchaResponse)(nil),                  // 13: carbon.frontend.plant_captcha.GetPlantCaptchaResponse
	(*StartPlantCaptchaUploadRequest)(nil),           // 14: carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest
	(*GetNextPlantCaptchaUploadStateRequest)(nil),    // 15: carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest
	(*GetNextPlantCaptchaUploadStateResponse)(nil),   // 16: carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse
	(*PlantCaptchaItemResult)(nil),                   // 17: carbon.frontend.plant_captcha.PlantCaptchaItemResult
	(*SubmitPlantCaptchaResultsRequest)(nil),         // 18: carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest
	(*GetPlantCaptchaItemResultsRequest)(nil),        // 19: carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest
	(*GetPlantCaptchaItemResultsResponse)(nil),       // 20: carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse
	(*CalculatePlantCaptchaRequest)(nil),             // 21: carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest
	(*CalculatePlantCaptchaResponse)(nil),            // 22: carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse
	(*PlantCaptchaResult)(nil),                       // 23: carbon.frontend.plant_captcha.PlantCaptchaResult
	(*PlantCaptchaResults)(nil),                      // 24: carbon.frontend.plant_captcha.PlantCaptchaResults
	(*VeselkaPlantCaptchaResponse)(nil),              // 25: carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse
	(*GetOriginalModelinatorConfigRequest)(nil),      // 26: carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest
	(*GetOriginalModelinatorConfigResponse)(nil),     // 27: carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse
	(*GetCaptchaRowStatusResponse)(nil),              // 28: carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse
	(*CancelPlantCaptchaOnRowRequest)(nil),           // 29: carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest
	nil,                                              // 30: carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry
	(*Timestamp)(nil),                                // 31: carbon.frontend.util.Timestamp
	(weed_tracking.PlantCaptchaStatus)(0),            // 32: weed_tracking.PlantCaptchaStatus
	(*weed_tracking.PlantCaptchaItemMetadata)(nil),   // 33: weed_tracking.PlantCaptchaItemMetadata
	(weed_tracking.PlantCaptchaUserPrediction)(0),    // 34: weed_tracking.PlantCaptchaUserPrediction
	(*almanac.ModelinatorConfig)(nil),                // 35: carbon.aimbot.almanac.ModelinatorConfig
	(*almanac.AlmanacConfig)(nil),                    // 36: carbon.aimbot.almanac.AlmanacConfig
	(*weed_tracking.PlantCaptchaStatusResponse)(nil), // 37: weed_tracking.PlantCaptchaStatusResponse
	(*Empty)(nil),                                    // 38: carbon.frontend.util.Empty
}
var file_frontend_proto_plant_captcha_proto_depIdxs = []int32{
	2,  // 0: carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha:type_name -> carbon.frontend.plant_captcha.PlantCaptcha
	31, // 1: carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts:type_name -> carbon.frontend.util.Timestamp
	31, // 2: carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts:type_name -> carbon.frontend.util.Timestamp
	32, // 3: carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.status:type_name -> weed_tracking.PlantCaptchaStatus
	31, // 4: carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts:type_name -> carbon.frontend.util.Timestamp
	2,  // 5: carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha:type_name -> carbon.frontend.plant_captcha.PlantCaptcha
	31, // 6: carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts:type_name -> carbon.frontend.util.Timestamp
	8,  // 7: carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas:type_name -> carbon.frontend.plant_captcha.PlantCaptchaListItem
	33, // 8: carbon.frontend.plant_captcha.PlantCaptchaItem.metadata:type_name -> weed_tracking.PlantCaptchaItemMetadata
	33, // 9: carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas:type_name -> weed_tracking.PlantCaptchaItemMetadata
	2,  // 10: carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha:type_name -> carbon.frontend.plant_captcha.PlantCaptcha
	12, // 11: carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items:type_name -> carbon.frontend.plant_captcha.PlantCaptchaItem
	31, // 12: carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts:type_name -> carbon.frontend.util.Timestamp
	31, // 13: carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 14: carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.upload_state:type_name -> carbon.frontend.plant_captcha.PlantCaptchaUploadState
	34, // 15: carbon.frontend.plant_captcha.PlantCaptchaItemResult.user_prediction:type_name -> weed_tracking.PlantCaptchaUserPrediction
	17, // 16: carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results:type_name -> carbon.frontend.plant_captcha.PlantCaptchaItemResult
	17, // 17: carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results:type_name -> carbon.frontend.plant_captcha.PlantCaptchaItemResult
	35, // 18: carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	1,  // 19: carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.failure_reason:type_name -> carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason
	34, // 20: carbon.frontend.plant_captcha.PlantCaptchaResult.label:type_name -> weed_tracking.PlantCaptchaUserPrediction
	33, // 21: carbon.frontend.plant_captcha.PlantCaptchaResult.metadata:type_name -> weed_tracking.PlantCaptchaItemMetadata
	35, // 22: carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	23, // 23: carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results:type_name -> carbon.frontend.plant_captcha.PlantCaptchaResult
	36, // 24: carbon.frontend.plant_captcha.PlantCaptchaResults.almanac:type_name -> carbon.aimbot.almanac.AlmanacConfig
	35, // 25: carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	35, // 26: carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	30, // 27: carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.row_status:type_name -> carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry
	37, // 28: carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry.value:type_name -> weed_tracking.PlantCaptchaStatusResponse
	3,  // 29: carbon.frontend.plant_captcha.PlantCaptchaService.StartPlantCaptcha:input_type -> carbon.frontend.plant_captcha.StartPlantCaptchaRequest
	5,  // 30: carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchaStatus:input_type -> carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest
	7,  // 31: carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchasList:input_type -> carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest
	10, // 32: carbon.frontend.plant_captcha.PlantCaptchaService.DeletePlantCaptcha:input_type -> carbon.frontend.plant_captcha.DeletePlantCaptchaRequest
	11, // 33: carbon.frontend.plant_captcha.PlantCaptchaService.GetPlantCaptcha:input_type -> carbon.frontend.plant_captcha.GetPlantCaptchaRequest
	38, // 34: carbon.frontend.plant_captcha.PlantCaptchaService.CancelPlantCaptcha:input_type -> carbon.frontend.util.Empty
	14, // 35: carbon.frontend.plant_captcha.PlantCaptchaService.StartPlantCaptchaUpload:input_type -> carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest
	15, // 36: carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchaUploadState:input_type -> carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest
	18, // 37: carbon.frontend.plant_captcha.PlantCaptchaService.SubmitPlantCaptchaResults:input_type -> carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest
	19, // 38: carbon.frontend.plant_captcha.PlantCaptchaService.GetPlantCaptchaItemResults:input_type -> carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest
	21, // 39: carbon.frontend.plant_captcha.PlantCaptchaService.CalculatePlantCaptcha:input_type -> carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest
	26, // 40: carbon.frontend.plant_captcha.PlantCaptchaService.GetOriginalModelinatorConfig:input_type -> carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest
	38, // 41: carbon.frontend.plant_captcha.PlantCaptchaService.GetCaptchaRowStatus:input_type -> carbon.frontend.util.Empty
	29, // 42: carbon.frontend.plant_captcha.PlantCaptchaService.CancelPlantCaptchaOnRow:input_type -> carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest
	4,  // 43: carbon.frontend.plant_captcha.PlantCaptchaService.StartPlantCaptcha:output_type -> carbon.frontend.plant_captcha.StartPlantCaptchaResponse
	6,  // 44: carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchaStatus:output_type -> carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse
	9,  // 45: carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchasList:output_type -> carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse
	38, // 46: carbon.frontend.plant_captcha.PlantCaptchaService.DeletePlantCaptcha:output_type -> carbon.frontend.util.Empty
	13, // 47: carbon.frontend.plant_captcha.PlantCaptchaService.GetPlantCaptcha:output_type -> carbon.frontend.plant_captcha.GetPlantCaptchaResponse
	38, // 48: carbon.frontend.plant_captcha.PlantCaptchaService.CancelPlantCaptcha:output_type -> carbon.frontend.util.Empty
	38, // 49: carbon.frontend.plant_captcha.PlantCaptchaService.StartPlantCaptchaUpload:output_type -> carbon.frontend.util.Empty
	16, // 50: carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchaUploadState:output_type -> carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse
	38, // 51: carbon.frontend.plant_captcha.PlantCaptchaService.SubmitPlantCaptchaResults:output_type -> carbon.frontend.util.Empty
	20, // 52: carbon.frontend.plant_captcha.PlantCaptchaService.GetPlantCaptchaItemResults:output_type -> carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse
	22, // 53: carbon.frontend.plant_captcha.PlantCaptchaService.CalculatePlantCaptcha:output_type -> carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse
	27, // 54: carbon.frontend.plant_captcha.PlantCaptchaService.GetOriginalModelinatorConfig:output_type -> carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse
	28, // 55: carbon.frontend.plant_captcha.PlantCaptchaService.GetCaptchaRowStatus:output_type -> carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse
	38, // 56: carbon.frontend.plant_captcha.PlantCaptchaService.CancelPlantCaptchaOnRow:output_type -> carbon.frontend.util.Empty
	43, // [43:57] is the sub-list for method output_type
	29, // [29:43] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_frontend_proto_plant_captcha_proto_init() }
func file_frontend_proto_plant_captcha_proto_init() {
	if File_frontend_proto_plant_captcha_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_plant_captcha_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptcha); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartPlantCaptchaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartPlantCaptchaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPlantCaptchaStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPlantCaptchaStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPlantCaptchasListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPlantCaptchasListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePlantCaptchaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlantCaptchaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlantCaptchaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartPlantCaptchaUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPlantCaptchaUploadStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPlantCaptchaUploadStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaItemResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitPlantCaptchaResultsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlantCaptchaItemResultsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlantCaptchaItemResultsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePlantCaptchaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePlantCaptchaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaResults); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VeselkaPlantCaptchaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOriginalModelinatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOriginalModelinatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaptchaRowStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_plant_captcha_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPlantCaptchaOnRowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_plant_captcha_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_plant_captcha_proto_goTypes,
		DependencyIndexes: file_frontend_proto_plant_captcha_proto_depIdxs,
		EnumInfos:         file_frontend_proto_plant_captcha_proto_enumTypes,
		MessageInfos:      file_frontend_proto_plant_captcha_proto_msgTypes,
	}.Build()
	File_frontend_proto_plant_captcha_proto = out.File
	file_frontend_proto_plant_captcha_proto_rawDesc = nil
	file_frontend_proto_plant_captcha_proto_goTypes = nil
	file_frontend_proto_plant_captcha_proto_depIdxs = nil
}

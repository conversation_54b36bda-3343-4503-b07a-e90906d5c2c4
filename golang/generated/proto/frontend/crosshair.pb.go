// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/crosshair.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CrosshairPosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float32 `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float32 `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *CrosshairPosition) Reset() {
	*x = CrosshairPosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrosshairPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrosshairPosition) ProtoMessage() {}

func (x *CrosshairPosition) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrosshairPosition.ProtoReflect.Descriptor instead.
func (*CrosshairPosition) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{0}
}

func (x *CrosshairPosition) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *CrosshairPosition) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type CrosshairPositionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                *Timestamp         `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Pos               *CrosshairPosition `protobuf:"bytes,2,opt,name=pos,proto3" json:"pos,omitempty"`
	Calibrating       bool               `protobuf:"varint,3,opt,name=calibrating,proto3" json:"calibrating,omitempty"`
	CalibrationFailed bool               `protobuf:"varint,4,opt,name=calibration_failed,json=calibrationFailed,proto3" json:"calibration_failed,omitempty"`
}

func (x *CrosshairPositionState) Reset() {
	*x = CrosshairPositionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrosshairPositionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrosshairPositionState) ProtoMessage() {}

func (x *CrosshairPositionState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrosshairPositionState.ProtoReflect.Descriptor instead.
func (*CrosshairPositionState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{1}
}

func (x *CrosshairPositionState) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *CrosshairPositionState) GetPos() *CrosshairPosition {
	if x != nil {
		return x.Pos
	}
	return nil
}

func (x *CrosshairPositionState) GetCalibrating() bool {
	if x != nil {
		return x.Calibrating
	}
	return false
}

func (x *CrosshairPositionState) GetCalibrationFailed() bool {
	if x != nil {
		return x.CalibrationFailed
	}
	return false
}

type CrosshairPositionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string     `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Ts    *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *CrosshairPositionRequest) Reset() {
	*x = CrosshairPositionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrosshairPositionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrosshairPositionRequest) ProtoMessage() {}

func (x *CrosshairPositionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrosshairPositionRequest.ProtoReflect.Descriptor instead.
func (*CrosshairPositionRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{2}
}

func (x *CrosshairPositionRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *CrosshairPositionRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type SetCrosshairPositionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string             `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Pos   *CrosshairPosition `protobuf:"bytes,2,opt,name=pos,proto3" json:"pos,omitempty"`
}

func (x *SetCrosshairPositionRequest) Reset() {
	*x = SetCrosshairPositionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCrosshairPositionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCrosshairPositionRequest) ProtoMessage() {}

func (x *SetCrosshairPositionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCrosshairPositionRequest.ProtoReflect.Descriptor instead.
func (*SetCrosshairPositionRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{3}
}

func (x *SetCrosshairPositionRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *SetCrosshairPositionRequest) GetPos() *CrosshairPosition {
	if x != nil {
		return x.Pos
	}
	return nil
}

type MoveScannerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string  `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	X     float32 `protobuf:"fixed32,2,opt,name=x,proto3" json:"x,omitempty"`
	Y     float32 `protobuf:"fixed32,3,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *MoveScannerRequest) Reset() {
	*x = MoveScannerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveScannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveScannerRequest) ProtoMessage() {}

func (x *MoveScannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveScannerRequest.ProtoReflect.Descriptor instead.
func (*MoveScannerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{4}
}

func (x *MoveScannerRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *MoveScannerRequest) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *MoveScannerRequest) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type AutoCrossHairCalStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *AutoCrossHairCalStateRequest) Reset() {
	*x = AutoCrossHairCalStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoCrossHairCalStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoCrossHairCalStateRequest) ProtoMessage() {}

func (x *AutoCrossHairCalStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoCrossHairCalStateRequest.ProtoReflect.Descriptor instead.
func (*AutoCrossHairCalStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{5}
}

func (x *AutoCrossHairCalStateRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type AutoCrossHairCalStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts         *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	InProgress bool       `protobuf:"varint,2,opt,name=in_progress,json=inProgress,proto3" json:"in_progress,omitempty"`
	Progress   float32    `protobuf:"fixed32,3,opt,name=progress,proto3" json:"progress,omitempty"`
	Failed     []string   `protobuf:"bytes,4,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *AutoCrossHairCalStateResponse) Reset() {
	*x = AutoCrossHairCalStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_crosshair_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoCrossHairCalStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoCrossHairCalStateResponse) ProtoMessage() {}

func (x *AutoCrossHairCalStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_crosshair_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoCrossHairCalStateResponse.ProtoReflect.Descriptor instead.
func (*AutoCrossHairCalStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_crosshair_proto_rawDescGZIP(), []int{6}
}

func (x *AutoCrossHairCalStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *AutoCrossHairCalStateResponse) GetInProgress() bool {
	if x != nil {
		return x.InProgress
	}
	return false
}

func (x *AutoCrossHairCalStateResponse) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *AutoCrossHairCalStateResponse) GetFailed() []string {
	if x != nil {
		return x.Failed
	}
	return nil
}

var File_frontend_proto_crosshair_proto protoreflect.FileDescriptor

var file_frontend_proto_crosshair_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x19, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x1a, 0x1b, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a, 0x11, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x01, 0x79, 0x22, 0xda, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61,
	0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x12, 0x3e, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68,
	0x61, 0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70, 0x6f, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x22, 0x62, 0x0a, 0x18, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0x74, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x68, 0x61, 0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x03, 0x70,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73,
	0x68, 0x61, 0x69, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x22, 0x47, 0x0a, 0x12, 0x4d,
	0x6f, 0x76, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x01, 0x79, 0x22, 0x4f, 0x0a, 0x1c, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0xa5, 0x01, 0x0a, 0x1d, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x72,
	0x6f, 0x73, 0x73, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x32, 0xfe, 0x05,
	0x0a, 0x10, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x61, 0x0a, 0x1b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43,
	0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69,
	0x72, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5b, 0x0a, 0x1f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x75,
	0x74, 0x6f, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x72,
	0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x4d, 0x0a, 0x11, 0x53, 0x74, 0x6f, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x61,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x7f, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x68, 0x61, 0x69, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72, 0x6f,
	0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x68, 0x61, 0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x6b, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61,
	0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72, 0x6f,
	0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68,
	0x61, 0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x59, 0x0a, 0x0b, 0x4d, 0x6f, 0x76, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x2d,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x91, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x48,
	0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72,
	0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72,
	0x2e, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10,
	0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_crosshair_proto_rawDescOnce sync.Once
	file_frontend_proto_crosshair_proto_rawDescData = file_frontend_proto_crosshair_proto_rawDesc
)

func file_frontend_proto_crosshair_proto_rawDescGZIP() []byte {
	file_frontend_proto_crosshair_proto_rawDescOnce.Do(func() {
		file_frontend_proto_crosshair_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_crosshair_proto_rawDescData)
	})
	return file_frontend_proto_crosshair_proto_rawDescData
}

var file_frontend_proto_crosshair_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_frontend_proto_crosshair_proto_goTypes = []interface{}{
	(*CrosshairPosition)(nil),             // 0: carbon.frontend.crosshair.CrosshairPosition
	(*CrosshairPositionState)(nil),        // 1: carbon.frontend.crosshair.CrosshairPositionState
	(*CrosshairPositionRequest)(nil),      // 2: carbon.frontend.crosshair.CrosshairPositionRequest
	(*SetCrosshairPositionRequest)(nil),   // 3: carbon.frontend.crosshair.SetCrosshairPositionRequest
	(*MoveScannerRequest)(nil),            // 4: carbon.frontend.crosshair.MoveScannerRequest
	(*AutoCrossHairCalStateRequest)(nil),  // 5: carbon.frontend.crosshair.AutoCrossHairCalStateRequest
	(*AutoCrossHairCalStateResponse)(nil), // 6: carbon.frontend.crosshair.AutoCrossHairCalStateResponse
	(*Timestamp)(nil),                     // 7: carbon.frontend.util.Timestamp
	(*CameraRequest)(nil),                 // 8: carbon.frontend.camera.CameraRequest
	(*Empty)(nil),                         // 9: carbon.frontend.util.Empty
}
var file_frontend_proto_crosshair_proto_depIdxs = []int32{
	7,  // 0: carbon.frontend.crosshair.CrosshairPositionState.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 1: carbon.frontend.crosshair.CrosshairPositionState.pos:type_name -> carbon.frontend.crosshair.CrosshairPosition
	7,  // 2: carbon.frontend.crosshair.CrosshairPositionRequest.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 3: carbon.frontend.crosshair.SetCrosshairPositionRequest.pos:type_name -> carbon.frontend.crosshair.CrosshairPosition
	7,  // 4: carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts:type_name -> carbon.frontend.util.Timestamp
	7,  // 5: carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	8,  // 6: carbon.frontend.crosshair.CrosshairService.StartAutoCalibrateCrosshair:input_type -> carbon.frontend.camera.CameraRequest
	9,  // 7: carbon.frontend.crosshair.CrosshairService.StartAutoCalibrateAllCrosshairs:input_type -> carbon.frontend.util.Empty
	9,  // 8: carbon.frontend.crosshair.CrosshairService.StopAutoCalibrate:input_type -> carbon.frontend.util.Empty
	2,  // 9: carbon.frontend.crosshair.CrosshairService.GetNextCrosshairState:input_type -> carbon.frontend.crosshair.CrosshairPositionRequest
	3,  // 10: carbon.frontend.crosshair.CrosshairService.SetCrosshairPosition:input_type -> carbon.frontend.crosshair.SetCrosshairPositionRequest
	4,  // 11: carbon.frontend.crosshair.CrosshairService.MoveScanner:input_type -> carbon.frontend.crosshair.MoveScannerRequest
	5,  // 12: carbon.frontend.crosshair.CrosshairService.GetNextAutoCrossHairCalState:input_type -> carbon.frontend.crosshair.AutoCrossHairCalStateRequest
	9,  // 13: carbon.frontend.crosshair.CrosshairService.StartAutoCalibrateCrosshair:output_type -> carbon.frontend.util.Empty
	9,  // 14: carbon.frontend.crosshair.CrosshairService.StartAutoCalibrateAllCrosshairs:output_type -> carbon.frontend.util.Empty
	9,  // 15: carbon.frontend.crosshair.CrosshairService.StopAutoCalibrate:output_type -> carbon.frontend.util.Empty
	1,  // 16: carbon.frontend.crosshair.CrosshairService.GetNextCrosshairState:output_type -> carbon.frontend.crosshair.CrosshairPositionState
	9,  // 17: carbon.frontend.crosshair.CrosshairService.SetCrosshairPosition:output_type -> carbon.frontend.util.Empty
	9,  // 18: carbon.frontend.crosshair.CrosshairService.MoveScanner:output_type -> carbon.frontend.util.Empty
	6,  // 19: carbon.frontend.crosshair.CrosshairService.GetNextAutoCrossHairCalState:output_type -> carbon.frontend.crosshair.AutoCrossHairCalStateResponse
	13, // [13:20] is the sub-list for method output_type
	6,  // [6:13] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_frontend_proto_crosshair_proto_init() }
func file_frontend_proto_crosshair_proto_init() {
	if File_frontend_proto_crosshair_proto != nil {
		return
	}
	file_frontend_proto_camera_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_crosshair_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrosshairPosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_crosshair_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrosshairPositionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_crosshair_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrosshairPositionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_crosshair_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCrosshairPositionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_crosshair_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveScannerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_crosshair_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoCrossHairCalStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_crosshair_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoCrossHairCalStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_crosshair_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_crosshair_proto_goTypes,
		DependencyIndexes: file_frontend_proto_crosshair_proto_depIdxs,
		MessageInfos:      file_frontend_proto_crosshair_proto_msgTypes,
	}.Build()
	File_frontend_proto_crosshair_proto = out.File
	file_frontend_proto_crosshair_proto_rawDesc = nil
	file_frontend_proto_crosshair_proto_goTypes = nil
	file_frontend_proto_crosshair_proto_depIdxs = nil
}

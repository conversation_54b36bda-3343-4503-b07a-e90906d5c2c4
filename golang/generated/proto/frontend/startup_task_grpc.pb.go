// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/startup_task.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	StartupTaskService_GetNextTasks_FullMethodName     = "/carbon.frontend.startup_task.StartupTaskService/GetNextTasks"
	StartupTaskService_MarkTaskComplete_FullMethodName = "/carbon.frontend.startup_task.StartupTaskService/MarkTaskComplete"
)

// StartupTaskServiceClient is the client API for StartupTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StartupTaskServiceClient interface {
	GetNextTasks(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextTasksResponse, error)
	MarkTaskComplete(ctx context.Context, in *MarkTaskCompleteRequest, opts ...grpc.CallOption) (*MarkTaskCompleteResponse, error)
}

type startupTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStartupTaskServiceClient(cc grpc.ClientConnInterface) StartupTaskServiceClient {
	return &startupTaskServiceClient{cc}
}

func (c *startupTaskServiceClient) GetNextTasks(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextTasksResponse, error) {
	out := new(GetNextTasksResponse)
	err := c.cc.Invoke(ctx, StartupTaskService_GetNextTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *startupTaskServiceClient) MarkTaskComplete(ctx context.Context, in *MarkTaskCompleteRequest, opts ...grpc.CallOption) (*MarkTaskCompleteResponse, error) {
	out := new(MarkTaskCompleteResponse)
	err := c.cc.Invoke(ctx, StartupTaskService_MarkTaskComplete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StartupTaskServiceServer is the server API for StartupTaskService service.
// All implementations must embed UnimplementedStartupTaskServiceServer
// for forward compatibility
type StartupTaskServiceServer interface {
	GetNextTasks(context.Context, *Timestamp) (*GetNextTasksResponse, error)
	MarkTaskComplete(context.Context, *MarkTaskCompleteRequest) (*MarkTaskCompleteResponse, error)
	mustEmbedUnimplementedStartupTaskServiceServer()
}

// UnimplementedStartupTaskServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStartupTaskServiceServer struct {
}

func (UnimplementedStartupTaskServiceServer) GetNextTasks(context.Context, *Timestamp) (*GetNextTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextTasks not implemented")
}
func (UnimplementedStartupTaskServiceServer) MarkTaskComplete(context.Context, *MarkTaskCompleteRequest) (*MarkTaskCompleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkTaskComplete not implemented")
}
func (UnimplementedStartupTaskServiceServer) mustEmbedUnimplementedStartupTaskServiceServer() {}

// UnsafeStartupTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StartupTaskServiceServer will
// result in compilation errors.
type UnsafeStartupTaskServiceServer interface {
	mustEmbedUnimplementedStartupTaskServiceServer()
}

func RegisterStartupTaskServiceServer(s grpc.ServiceRegistrar, srv StartupTaskServiceServer) {
	s.RegisterService(&StartupTaskService_ServiceDesc, srv)
}

func _StartupTaskService_GetNextTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StartupTaskServiceServer).GetNextTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StartupTaskService_GetNextTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StartupTaskServiceServer).GetNextTasks(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _StartupTaskService_MarkTaskComplete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkTaskCompleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StartupTaskServiceServer).MarkTaskComplete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StartupTaskService_MarkTaskComplete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StartupTaskServiceServer).MarkTaskComplete(ctx, req.(*MarkTaskCompleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StartupTaskService_ServiceDesc is the grpc.ServiceDesc for StartupTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StartupTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.startup_task.StartupTaskService",
	HandlerType: (*StartupTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextTasks",
			Handler:    _StartupTaskService_GetNextTasks_Handler,
		},
		{
			MethodName: "MarkTaskComplete",
			Handler:    _StartupTaskService_MarkTaskComplete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/startup_task.proto",
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/laser.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LaserService_FireLaser_FullMethodName          = "/carbon.frontend.laser.LaserService/FireLaser"
	LaserService_GetNextLaserState_FullMethodName  = "/carbon.frontend.laser.LaserService/GetNextLaserState"
	LaserService_ToggleLaserEnabled_FullMethodName = "/carbon.frontend.laser.LaserService/ToggleLaserEnabled"
	LaserService_EnableRow_FullMethodName          = "/carbon.frontend.laser.LaserService/EnableRow"
	LaserService_DisableRow_FullMethodName         = "/carbon.frontend.laser.LaserService/DisableRow"
	LaserService_ResetLaserMetrics_FullMethodName  = "/carbon.frontend.laser.LaserService/ResetLaserMetrics"
	LaserService_FixLaserMetrics_FullMethodName    = "/carbon.frontend.laser.LaserService/FixLaserMetrics"
	LaserService_SetLaserPower_FullMethodName      = "/carbon.frontend.laser.LaserService/SetLaserPower"
)

// LaserServiceClient is the client API for LaserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LaserServiceClient interface {
	FireLaser(ctx context.Context, opts ...grpc.CallOption) (LaserService_FireLaserClient, error)
	GetNextLaserState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*LaserStateList, error)
	ToggleLaserEnabled(ctx context.Context, in *LaserDescriptor, opts ...grpc.CallOption) (*Empty, error)
	EnableRow(ctx context.Context, in *RowRequest, opts ...grpc.CallOption) (*Empty, error)
	DisableRow(ctx context.Context, in *RowRequest, opts ...grpc.CallOption) (*Empty, error)
	ResetLaserMetrics(ctx context.Context, in *LaserDescriptor, opts ...grpc.CallOption) (*Empty, error)
	FixLaserMetrics(ctx context.Context, in *FixLaserMetricsRequest, opts ...grpc.CallOption) (*Empty, error)
	SetLaserPower(ctx context.Context, in *SetLaserPowerRequest, opts ...grpc.CallOption) (*Empty, error)
}

type laserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLaserServiceClient(cc grpc.ClientConnInterface) LaserServiceClient {
	return &laserServiceClient{cc}
}

func (c *laserServiceClient) FireLaser(ctx context.Context, opts ...grpc.CallOption) (LaserService_FireLaserClient, error) {
	stream, err := c.cc.NewStream(ctx, &LaserService_ServiceDesc.Streams[0], LaserService_FireLaser_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &laserServiceFireLaserClient{stream}
	return x, nil
}

type LaserService_FireLaserClient interface {
	Send(*CameraRequest) error
	CloseAndRecv() (*Empty, error)
	grpc.ClientStream
}

type laserServiceFireLaserClient struct {
	grpc.ClientStream
}

func (x *laserServiceFireLaserClient) Send(m *CameraRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *laserServiceFireLaserClient) CloseAndRecv() (*Empty, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(Empty)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *laserServiceClient) GetNextLaserState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*LaserStateList, error) {
	out := new(LaserStateList)
	err := c.cc.Invoke(ctx, LaserService_GetNextLaserState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *laserServiceClient) ToggleLaserEnabled(ctx context.Context, in *LaserDescriptor, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LaserService_ToggleLaserEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *laserServiceClient) EnableRow(ctx context.Context, in *RowRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LaserService_EnableRow_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *laserServiceClient) DisableRow(ctx context.Context, in *RowRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LaserService_DisableRow_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *laserServiceClient) ResetLaserMetrics(ctx context.Context, in *LaserDescriptor, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LaserService_ResetLaserMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *laserServiceClient) FixLaserMetrics(ctx context.Context, in *FixLaserMetricsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LaserService_FixLaserMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *laserServiceClient) SetLaserPower(ctx context.Context, in *SetLaserPowerRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LaserService_SetLaserPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LaserServiceServer is the server API for LaserService service.
// All implementations must embed UnimplementedLaserServiceServer
// for forward compatibility
type LaserServiceServer interface {
	FireLaser(LaserService_FireLaserServer) error
	GetNextLaserState(context.Context, *Timestamp) (*LaserStateList, error)
	ToggleLaserEnabled(context.Context, *LaserDescriptor) (*Empty, error)
	EnableRow(context.Context, *RowRequest) (*Empty, error)
	DisableRow(context.Context, *RowRequest) (*Empty, error)
	ResetLaserMetrics(context.Context, *LaserDescriptor) (*Empty, error)
	FixLaserMetrics(context.Context, *FixLaserMetricsRequest) (*Empty, error)
	SetLaserPower(context.Context, *SetLaserPowerRequest) (*Empty, error)
	mustEmbedUnimplementedLaserServiceServer()
}

// UnimplementedLaserServiceServer must be embedded to have forward compatible implementations.
type UnimplementedLaserServiceServer struct {
}

func (UnimplementedLaserServiceServer) FireLaser(LaserService_FireLaserServer) error {
	return status.Errorf(codes.Unimplemented, "method FireLaser not implemented")
}
func (UnimplementedLaserServiceServer) GetNextLaserState(context.Context, *Timestamp) (*LaserStateList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextLaserState not implemented")
}
func (UnimplementedLaserServiceServer) ToggleLaserEnabled(context.Context, *LaserDescriptor) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ToggleLaserEnabled not implemented")
}
func (UnimplementedLaserServiceServer) EnableRow(context.Context, *RowRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableRow not implemented")
}
func (UnimplementedLaserServiceServer) DisableRow(context.Context, *RowRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableRow not implemented")
}
func (UnimplementedLaserServiceServer) ResetLaserMetrics(context.Context, *LaserDescriptor) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetLaserMetrics not implemented")
}
func (UnimplementedLaserServiceServer) FixLaserMetrics(context.Context, *FixLaserMetricsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FixLaserMetrics not implemented")
}
func (UnimplementedLaserServiceServer) SetLaserPower(context.Context, *SetLaserPowerRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLaserPower not implemented")
}
func (UnimplementedLaserServiceServer) mustEmbedUnimplementedLaserServiceServer() {}

// UnsafeLaserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LaserServiceServer will
// result in compilation errors.
type UnsafeLaserServiceServer interface {
	mustEmbedUnimplementedLaserServiceServer()
}

func RegisterLaserServiceServer(s grpc.ServiceRegistrar, srv LaserServiceServer) {
	s.RegisterService(&LaserService_ServiceDesc, srv)
}

func _LaserService_FireLaser_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(LaserServiceServer).FireLaser(&laserServiceFireLaserServer{stream})
}

type LaserService_FireLaserServer interface {
	SendAndClose(*Empty) error
	Recv() (*CameraRequest, error)
	grpc.ServerStream
}

type laserServiceFireLaserServer struct {
	grpc.ServerStream
}

func (x *laserServiceFireLaserServer) SendAndClose(m *Empty) error {
	return x.ServerStream.SendMsg(m)
}

func (x *laserServiceFireLaserServer) Recv() (*CameraRequest, error) {
	m := new(CameraRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _LaserService_GetNextLaserState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).GetNextLaserState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_GetNextLaserState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).GetNextLaserState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _LaserService_ToggleLaserEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaserDescriptor)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).ToggleLaserEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_ToggleLaserEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).ToggleLaserEnabled(ctx, req.(*LaserDescriptor))
	}
	return interceptor(ctx, in, info, handler)
}

func _LaserService_EnableRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).EnableRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_EnableRow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).EnableRow(ctx, req.(*RowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LaserService_DisableRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).DisableRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_DisableRow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).DisableRow(ctx, req.(*RowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LaserService_ResetLaserMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaserDescriptor)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).ResetLaserMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_ResetLaserMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).ResetLaserMetrics(ctx, req.(*LaserDescriptor))
	}
	return interceptor(ctx, in, info, handler)
}

func _LaserService_FixLaserMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixLaserMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).FixLaserMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_FixLaserMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).FixLaserMetrics(ctx, req.(*FixLaserMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LaserService_SetLaserPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLaserPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LaserServiceServer).SetLaserPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LaserService_SetLaserPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LaserServiceServer).SetLaserPower(ctx, req.(*SetLaserPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LaserService_ServiceDesc is the grpc.ServiceDesc for LaserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LaserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.laser.LaserService",
	HandlerType: (*LaserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextLaserState",
			Handler:    _LaserService_GetNextLaserState_Handler,
		},
		{
			MethodName: "ToggleLaserEnabled",
			Handler:    _LaserService_ToggleLaserEnabled_Handler,
		},
		{
			MethodName: "EnableRow",
			Handler:    _LaserService_EnableRow_Handler,
		},
		{
			MethodName: "DisableRow",
			Handler:    _LaserService_DisableRow_Handler,
		},
		{
			MethodName: "ResetLaserMetrics",
			Handler:    _LaserService_ResetLaserMetrics_Handler,
		},
		{
			MethodName: "FixLaserMetrics",
			Handler:    _LaserService_FixLaserMetrics_Handler,
		},
		{
			MethodName: "SetLaserPower",
			Handler:    _LaserService_SetLaserPower_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "FireLaser",
			Handler:       _LaserService_FireLaser_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "frontend/proto/laser.proto",
}

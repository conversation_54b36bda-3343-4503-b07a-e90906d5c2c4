// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/image_stream.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Annotations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detections *weed_tracking.Detections `protobuf:"bytes,1,opt,name=detections,proto3" json:"detections,omitempty"`
	Bands      *weed_tracking.Bands      `protobuf:"bytes,2,opt,name=bands,proto3" json:"bands,omitempty"`
	CrosshairX int32                     `protobuf:"varint,3,opt,name=crosshair_x,json=crosshairX,proto3" json:"crosshair_x,omitempty"`
	CrosshairY int32                     `protobuf:"varint,4,opt,name=crosshair_y,json=crosshairY,proto3" json:"crosshair_y,omitempty"`
}

func (x *Annotations) Reset() {
	*x = Annotations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Annotations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Annotations) ProtoMessage() {}

func (x *Annotations) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Annotations.ProtoReflect.Descriptor instead.
func (*Annotations) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{0}
}

func (x *Annotations) GetDetections() *weed_tracking.Detections {
	if x != nil {
		return x.Detections
	}
	return nil
}

func (x *Annotations) GetBands() *weed_tracking.Bands {
	if x != nil {
		return x.Bands
	}
	return nil
}

func (x *Annotations) GetCrosshairX() int32 {
	if x != nil {
		return x.CrosshairX
	}
	return 0
}

func (x *Annotations) GetCrosshairY() int32 {
	if x != nil {
		return x.CrosshairY
	}
	return 0
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts          *Timestamp   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Width       uint32       `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height      uint32       `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Focus       float64      `protobuf:"fixed64,4,opt,name=focus,proto3" json:"focus,omitempty"`
	Data        []byte       `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	Annotations *Annotations `protobuf:"bytes,6,opt,name=annotations,proto3" json:"annotations,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{1}
}

func (x *Image) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *Image) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Image) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Image) GetFocus() float64 {
	if x != nil {
		return x.Focus
	}
	return 0
}

func (x *Image) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Image) GetAnnotations() *Annotations {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type CameraImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId                      string     `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Ts                         *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Annotated                  bool       `protobuf:"varint,3,opt,name=annotated,proto3" json:"annotated,omitempty"`
	IncludeAnnotationsMetadata bool       `protobuf:"varint,4,opt,name=include_annotations_metadata,json=includeAnnotationsMetadata,proto3" json:"include_annotations_metadata,omitempty"`
	DontDownsample             bool       `protobuf:"varint,5,opt,name=dont_downsample,json=dontDownsample,proto3" json:"dont_downsample,omitempty"`
	EncodeAsPng                bool       `protobuf:"varint,6,opt,name=encode_as_png,json=encodeAsPng,proto3" json:"encode_as_png,omitempty"`
	EncodeAsRaw                bool       `protobuf:"varint,7,opt,name=encode_as_raw,json=encodeAsRaw,proto3" json:"encode_as_raw,omitempty"`
}

func (x *CameraImageRequest) Reset() {
	*x = CameraImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraImageRequest) ProtoMessage() {}

func (x *CameraImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraImageRequest.ProtoReflect.Descriptor instead.
func (*CameraImageRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{2}
}

func (x *CameraImageRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *CameraImageRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *CameraImageRequest) GetAnnotated() bool {
	if x != nil {
		return x.Annotated
	}
	return false
}

func (x *CameraImageRequest) GetIncludeAnnotationsMetadata() bool {
	if x != nil {
		return x.IncludeAnnotationsMetadata
	}
	return false
}

func (x *CameraImageRequest) GetDontDownsample() bool {
	if x != nil {
		return x.DontDownsample
	}
	return false
}

func (x *CameraImageRequest) GetEncodeAsPng() bool {
	if x != nil {
		return x.EncodeAsPng
	}
	return false
}

func (x *CameraImageRequest) GetEncodeAsRaw() bool {
	if x != nil {
		return x.EncodeAsRaw
	}
	return false
}

type GetPredictImageByTimestampRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string     `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Ts          *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	CropAroundX int32      `protobuf:"varint,3,opt,name=crop_around_x,json=cropAroundX,proto3" json:"crop_around_x,omitempty"`
	CropAroundY int32      `protobuf:"varint,4,opt,name=crop_around_y,json=cropAroundY,proto3" json:"crop_around_y,omitempty"`
}

func (x *GetPredictImageByTimestampRequest) Reset() {
	*x = GetPredictImageByTimestampRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictImageByTimestampRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictImageByTimestampRequest) ProtoMessage() {}

func (x *GetPredictImageByTimestampRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictImageByTimestampRequest.ProtoReflect.Descriptor instead.
func (*GetPredictImageByTimestampRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{3}
}

func (x *GetPredictImageByTimestampRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetPredictImageByTimestampRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetPredictImageByTimestampRequest) GetCropAroundX() int32 {
	if x != nil {
		return x.CropAroundX
	}
	return 0
}

func (x *GetPredictImageByTimestampRequest) GetCropAroundY() int32 {
	if x != nil {
		return x.CropAroundY
	}
	return 0
}

type GetPredictImageByTimestampResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data    []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	CenterX int32  `protobuf:"varint,2,opt,name=center_x,json=centerX,proto3" json:"center_x,omitempty"`
	CenterY int32  `protobuf:"varint,3,opt,name=center_y,json=centerY,proto3" json:"center_y,omitempty"`
}

func (x *GetPredictImageByTimestampResponse) Reset() {
	*x = GetPredictImageByTimestampResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictImageByTimestampResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictImageByTimestampResponse) ProtoMessage() {}

func (x *GetPredictImageByTimestampResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictImageByTimestampResponse.ProtoReflect.Descriptor instead.
func (*GetPredictImageByTimestampResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{4}
}

func (x *GetPredictImageByTimestampResponse) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetPredictImageByTimestampResponse) GetCenterX() int32 {
	if x != nil {
		return x.CenterX
	}
	return 0
}

func (x *GetPredictImageByTimestampResponse) GetCenterY() int32 {
	if x != nil {
		return x.CenterY
	}
	return 0
}

type PossiblePerspective struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts          *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	CropAroundX int32      `protobuf:"varint,2,opt,name=crop_around_x,json=cropAroundX,proto3" json:"crop_around_x,omitempty"`
	CropAroundY int32      `protobuf:"varint,3,opt,name=crop_around_y,json=cropAroundY,proto3" json:"crop_around_y,omitempty"`
}

func (x *PossiblePerspective) Reset() {
	*x = PossiblePerspective{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PossiblePerspective) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PossiblePerspective) ProtoMessage() {}

func (x *PossiblePerspective) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PossiblePerspective.ProtoReflect.Descriptor instead.
func (*PossiblePerspective) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{5}
}

func (x *PossiblePerspective) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *PossiblePerspective) GetCropAroundX() int32 {
	if x != nil {
		return x.CropAroundX
	}
	return 0
}

func (x *PossiblePerspective) GetCropAroundY() int32 {
	if x != nil {
		return x.CropAroundY
	}
	return 0
}

type GetMultiPredictPerspectivesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId                 string                 `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Perspectives          []*PossiblePerspective `protobuf:"bytes,2,rep,name=perspectives,proto3" json:"perspectives,omitempty"`
	RequestedPerspectives int32                  `protobuf:"varint,3,opt,name=requested_perspectives,json=requestedPerspectives,proto3" json:"requested_perspectives,omitempty"`
}

func (x *GetMultiPredictPerspectivesRequest) Reset() {
	*x = GetMultiPredictPerspectivesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMultiPredictPerspectivesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMultiPredictPerspectivesRequest) ProtoMessage() {}

func (x *GetMultiPredictPerspectivesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMultiPredictPerspectivesRequest.ProtoReflect.Descriptor instead.
func (*GetMultiPredictPerspectivesRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{6}
}

func (x *GetMultiPredictPerspectivesRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetMultiPredictPerspectivesRequest) GetPerspectives() []*PossiblePerspective {
	if x != nil {
		return x.Perspectives
	}
	return nil
}

func (x *GetMultiPredictPerspectivesRequest) GetRequestedPerspectives() int32 {
	if x != nil {
		return x.RequestedPerspectives
	}
	return 0
}

type CentroidPerspective struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts      *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	CenterX int32      `protobuf:"varint,2,opt,name=center_x,json=centerX,proto3" json:"center_x,omitempty"`
	CenterY int32      `protobuf:"varint,3,opt,name=center_y,json=centerY,proto3" json:"center_y,omitempty"`
	Data    []byte     `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CentroidPerspective) Reset() {
	*x = CentroidPerspective{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CentroidPerspective) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CentroidPerspective) ProtoMessage() {}

func (x *CentroidPerspective) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CentroidPerspective.ProtoReflect.Descriptor instead.
func (*CentroidPerspective) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{7}
}

func (x *CentroidPerspective) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *CentroidPerspective) GetCenterX() int32 {
	if x != nil {
		return x.CenterX
	}
	return 0
}

func (x *CentroidPerspective) GetCenterY() int32 {
	if x != nil {
		return x.CenterY
	}
	return 0
}

func (x *CentroidPerspective) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetMultiPredictPerspectivesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Perspectives []*CentroidPerspective `protobuf:"bytes,1,rep,name=perspectives,proto3" json:"perspectives,omitempty"`
}

func (x *GetMultiPredictPerspectivesResponse) Reset() {
	*x = GetMultiPredictPerspectivesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_image_stream_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMultiPredictPerspectivesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMultiPredictPerspectivesResponse) ProtoMessage() {}

func (x *GetMultiPredictPerspectivesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_image_stream_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMultiPredictPerspectivesResponse.ProtoReflect.Descriptor instead.
func (*GetMultiPredictPerspectivesResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_image_stream_proto_rawDescGZIP(), []int{8}
}

func (x *GetMultiPredictPerspectivesResponse) GetPerspectives() []*CentroidPerspective {
	if x != nil {
		return x.Perspectives
	}
	return nil
}

var File_frontend_proto_image_stream_proto protoreflect.FileDescriptor

var file_frontend_proto_image_stream_proto_rawDesc = []byte{
	0x0a, 0x21, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x01, 0x0a, 0x0b, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x42, 0x61, 0x6e, 0x64, 0x73, 0x52, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x58, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x59, 0x22, 0xdd,
	0x01, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x4b, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xad,
	0x02, 0x0a, 0x12, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x1c, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x1a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a,
	0x0f, 0x64, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x64, 0x6f, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x61, 0x73, 0x5f, 0x70, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x41, 0x73, 0x50, 0x6e, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x73, 0x5f, 0x72, 0x61, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x41, 0x73, 0x52, 0x61, 0x77, 0x22, 0xb3,
	0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0d,
	0x63, 0x72, 0x6f, 0x70, 0x5f, 0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x6f, 0x70, 0x41, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x58,
	0x12, 0x22, 0x0a, 0x0d, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x6f, 0x70, 0x41, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x59, 0x22, 0x6e, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x58, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x5f, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x59, 0x22, 0x8e, 0x01, 0x0a, 0x13, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x6f, 0x70, 0x41, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x58, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x5f, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x6f, 0x70, 0x41, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x59, 0x22, 0xc9, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06,
	0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61,
	0x6d, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x0c, 0x70, 0x65,
	0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x16, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x73, 0x22, 0x90, 0x01, 0x0a, 0x13, 0x43, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x69, 0x64, 0x50, 0x65,
	0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x5f, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x58, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x59,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x7c, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0c, 0x70,
	0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x2e, 0x43, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x69, 0x64, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x73, 0x32, 0xc8, 0x03, 0x0a, 0x12, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x9f, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa2, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a,
	0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_image_stream_proto_rawDescOnce sync.Once
	file_frontend_proto_image_stream_proto_rawDescData = file_frontend_proto_image_stream_proto_rawDesc
)

func file_frontend_proto_image_stream_proto_rawDescGZIP() []byte {
	file_frontend_proto_image_stream_proto_rawDescOnce.Do(func() {
		file_frontend_proto_image_stream_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_image_stream_proto_rawDescData)
	})
	return file_frontend_proto_image_stream_proto_rawDescData
}

var file_frontend_proto_image_stream_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_frontend_proto_image_stream_proto_goTypes = []interface{}{
	(*Annotations)(nil),                         // 0: carbon.frontend.image_stream.Annotations
	(*Image)(nil),                               // 1: carbon.frontend.image_stream.Image
	(*CameraImageRequest)(nil),                  // 2: carbon.frontend.image_stream.CameraImageRequest
	(*GetPredictImageByTimestampRequest)(nil),   // 3: carbon.frontend.image_stream.GetPredictImageByTimestampRequest
	(*GetPredictImageByTimestampResponse)(nil),  // 4: carbon.frontend.image_stream.GetPredictImageByTimestampResponse
	(*PossiblePerspective)(nil),                 // 5: carbon.frontend.image_stream.PossiblePerspective
	(*GetMultiPredictPerspectivesRequest)(nil),  // 6: carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest
	(*CentroidPerspective)(nil),                 // 7: carbon.frontend.image_stream.CentroidPerspective
	(*GetMultiPredictPerspectivesResponse)(nil), // 8: carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse
	(*weed_tracking.Detections)(nil),            // 9: weed_tracking.Detections
	(*weed_tracking.Bands)(nil),                 // 10: weed_tracking.Bands
	(*Timestamp)(nil),                           // 11: carbon.frontend.util.Timestamp
}
var file_frontend_proto_image_stream_proto_depIdxs = []int32{
	9,  // 0: carbon.frontend.image_stream.Annotations.detections:type_name -> weed_tracking.Detections
	10, // 1: carbon.frontend.image_stream.Annotations.bands:type_name -> weed_tracking.Bands
	11, // 2: carbon.frontend.image_stream.Image.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 3: carbon.frontend.image_stream.Image.annotations:type_name -> carbon.frontend.image_stream.Annotations
	11, // 4: carbon.frontend.image_stream.CameraImageRequest.ts:type_name -> carbon.frontend.util.Timestamp
	11, // 5: carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts:type_name -> carbon.frontend.util.Timestamp
	11, // 6: carbon.frontend.image_stream.PossiblePerspective.ts:type_name -> carbon.frontend.util.Timestamp
	5,  // 7: carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives:type_name -> carbon.frontend.image_stream.PossiblePerspective
	11, // 8: carbon.frontend.image_stream.CentroidPerspective.ts:type_name -> carbon.frontend.util.Timestamp
	7,  // 9: carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives:type_name -> carbon.frontend.image_stream.CentroidPerspective
	2,  // 10: carbon.frontend.image_stream.ImageStreamService.GetNextCameraImage:input_type -> carbon.frontend.image_stream.CameraImageRequest
	3,  // 11: carbon.frontend.image_stream.ImageStreamService.GetPredictImageByTimestamp:input_type -> carbon.frontend.image_stream.GetPredictImageByTimestampRequest
	6,  // 12: carbon.frontend.image_stream.ImageStreamService.GetMultiPredictPerspectives:input_type -> carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest
	1,  // 13: carbon.frontend.image_stream.ImageStreamService.GetNextCameraImage:output_type -> carbon.frontend.image_stream.Image
	4,  // 14: carbon.frontend.image_stream.ImageStreamService.GetPredictImageByTimestamp:output_type -> carbon.frontend.image_stream.GetPredictImageByTimestampResponse
	8,  // 15: carbon.frontend.image_stream.ImageStreamService.GetMultiPredictPerspectives:output_type -> carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_frontend_proto_image_stream_proto_init() }
func file_frontend_proto_image_stream_proto_init() {
	if File_frontend_proto_image_stream_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_image_stream_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Annotations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictImageByTimestampRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictImageByTimestampResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PossiblePerspective); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMultiPredictPerspectivesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CentroidPerspective); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_image_stream_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMultiPredictPerspectivesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_image_stream_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_image_stream_proto_goTypes,
		DependencyIndexes: file_frontend_proto_image_stream_proto_depIdxs,
		MessageInfos:      file_frontend_proto_image_stream_proto_msgTypes,
	}.Build()
	File_frontend_proto_image_stream_proto = out.File
	file_frontend_proto_image_stream_proto_rawDesc = nil
	file_frontend_proto_image_stream_proto_goTypes = nil
	file_frontend_proto_image_stream_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/focus.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FocusService_TogglePredictGridView_FullMethodName  = "/carbon.frontend.focus.FocusService/TogglePredictGridView"
	FocusService_GetNextFocusState_FullMethodName      = "/carbon.frontend.focus.FocusService/GetNextFocusState"
	FocusService_StartAutoFocusSpecific_FullMethodName = "/carbon.frontend.focus.FocusService/StartAutoFocusSpecific"
	FocusService_StartAutoFocusAll_FullMethodName      = "/carbon.frontend.focus.FocusService/StartAutoFocusAll"
	FocusService_StopAutoFocus_FullMethodName          = "/carbon.frontend.focus.FocusService/StopAutoFocus"
	FocusService_SetLensValue_FullMethodName           = "/carbon.frontend.focus.FocusService/SetLensValue"
)

// FocusServiceClient is the client API for FocusService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FocusServiceClient interface {
	TogglePredictGridView(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetNextFocusState(ctx context.Context, in *FocusStateRequest, opts ...grpc.CallOption) (*FocusState, error)
	StartAutoFocusSpecific(ctx context.Context, in *CameraRequest, opts ...grpc.CallOption) (*Empty, error)
	StartAutoFocusAll(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StopAutoFocus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	SetLensValue(ctx context.Context, in *LensSetRequest, opts ...grpc.CallOption) (*Empty, error)
}

type focusServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFocusServiceClient(cc grpc.ClientConnInterface) FocusServiceClient {
	return &focusServiceClient{cc}
}

func (c *focusServiceClient) TogglePredictGridView(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, FocusService_TogglePredictGridView_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *focusServiceClient) GetNextFocusState(ctx context.Context, in *FocusStateRequest, opts ...grpc.CallOption) (*FocusState, error) {
	out := new(FocusState)
	err := c.cc.Invoke(ctx, FocusService_GetNextFocusState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *focusServiceClient) StartAutoFocusSpecific(ctx context.Context, in *CameraRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, FocusService_StartAutoFocusSpecific_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *focusServiceClient) StartAutoFocusAll(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, FocusService_StartAutoFocusAll_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *focusServiceClient) StopAutoFocus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, FocusService_StopAutoFocus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *focusServiceClient) SetLensValue(ctx context.Context, in *LensSetRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, FocusService_SetLensValue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FocusServiceServer is the server API for FocusService service.
// All implementations must embed UnimplementedFocusServiceServer
// for forward compatibility
type FocusServiceServer interface {
	TogglePredictGridView(context.Context, *Empty) (*Empty, error)
	GetNextFocusState(context.Context, *FocusStateRequest) (*FocusState, error)
	StartAutoFocusSpecific(context.Context, *CameraRequest) (*Empty, error)
	StartAutoFocusAll(context.Context, *Empty) (*Empty, error)
	StopAutoFocus(context.Context, *Empty) (*Empty, error)
	SetLensValue(context.Context, *LensSetRequest) (*Empty, error)
	mustEmbedUnimplementedFocusServiceServer()
}

// UnimplementedFocusServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFocusServiceServer struct {
}

func (UnimplementedFocusServiceServer) TogglePredictGridView(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TogglePredictGridView not implemented")
}
func (UnimplementedFocusServiceServer) GetNextFocusState(context.Context, *FocusStateRequest) (*FocusState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextFocusState not implemented")
}
func (UnimplementedFocusServiceServer) StartAutoFocusSpecific(context.Context, *CameraRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAutoFocusSpecific not implemented")
}
func (UnimplementedFocusServiceServer) StartAutoFocusAll(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAutoFocusAll not implemented")
}
func (UnimplementedFocusServiceServer) StopAutoFocus(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopAutoFocus not implemented")
}
func (UnimplementedFocusServiceServer) SetLensValue(context.Context, *LensSetRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLensValue not implemented")
}
func (UnimplementedFocusServiceServer) mustEmbedUnimplementedFocusServiceServer() {}

// UnsafeFocusServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FocusServiceServer will
// result in compilation errors.
type UnsafeFocusServiceServer interface {
	mustEmbedUnimplementedFocusServiceServer()
}

func RegisterFocusServiceServer(s grpc.ServiceRegistrar, srv FocusServiceServer) {
	s.RegisterService(&FocusService_ServiceDesc, srv)
}

func _FocusService_TogglePredictGridView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FocusServiceServer).TogglePredictGridView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FocusService_TogglePredictGridView_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FocusServiceServer).TogglePredictGridView(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _FocusService_GetNextFocusState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FocusStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FocusServiceServer).GetNextFocusState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FocusService_GetNextFocusState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FocusServiceServer).GetNextFocusState(ctx, req.(*FocusStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FocusService_StartAutoFocusSpecific_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CameraRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FocusServiceServer).StartAutoFocusSpecific(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FocusService_StartAutoFocusSpecific_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FocusServiceServer).StartAutoFocusSpecific(ctx, req.(*CameraRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FocusService_StartAutoFocusAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FocusServiceServer).StartAutoFocusAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FocusService_StartAutoFocusAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FocusServiceServer).StartAutoFocusAll(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _FocusService_StopAutoFocus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FocusServiceServer).StopAutoFocus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FocusService_StopAutoFocus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FocusServiceServer).StopAutoFocus(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _FocusService_SetLensValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LensSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FocusServiceServer).SetLensValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FocusService_SetLensValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FocusServiceServer).SetLensValue(ctx, req.(*LensSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FocusService_ServiceDesc is the grpc.ServiceDesc for FocusService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FocusService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.focus.FocusService",
	HandlerType: (*FocusServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TogglePredictGridView",
			Handler:    _FocusService_TogglePredictGridView_Handler,
		},
		{
			MethodName: "GetNextFocusState",
			Handler:    _FocusService_GetNextFocusState_Handler,
		},
		{
			MethodName: "StartAutoFocusSpecific",
			Handler:    _FocusService_StartAutoFocusSpecific_Handler,
		},
		{
			MethodName: "StartAutoFocusAll",
			Handler:    _FocusService_StartAutoFocusAll_Handler,
		},
		{
			MethodName: "StopAutoFocus",
			Handler:    _FocusService_StopAutoFocus_Handler,
		},
		{
			MethodName: "SetLensValue",
			Handler:    _FocusService_SetLensValue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/focus.proto",
}

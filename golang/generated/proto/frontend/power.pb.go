// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/power.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Device int32

const (
	Device_SENSOR_LIFTED              Device = 0
	Device_SENSOR_SERVER_TEMPERATURE  Device = 1
	Device_SENSOR_SERVER_HUMIDITY     Device = 2
	Device_SENSOR_WATER               Device = 3
	Device_SENSOR_12V_BATTERY         Device = 4
	Device_SENSOR_POWER_QUALITY       Device = 5
	Device_SENSOR_TRACTOR             Device = 6
	Device_SENSOR_AC_FREQUENCY        Device = 7
	Device_SENSOR_AB_VOLTAGE          Device = 8
	Device_SENSOR_BC_VOLTAGE          Device = 9
	Device_SENSOR_AC_VOLTAGE          Device = 10
	Device_SENSOR_A_CURRENT           Device = 11
	Device_SENSOR_B_CURRENT           Device = 12
	Device_SENSOR_C_CURRENT           Device = 13
	Device_RELAY_SUICIDE              Device = 14
	Device_RELAY_REBOOT               Device = 15
	Device_RELAY_MAIN                 Device = 16
	Device_RELAY_ROW_1                Device = 17
	Device_RELAY_ROW_2                Device = 18
	Device_RELAY_ROW_3                Device = 19
	Device_RELAY_LIGHTS_1             Device = 20
	Device_RELAY_LIGHTS_2             Device = 21
	Device_RELAY_LIGHTS_3             Device = 22
	Device_RELAY_SCANNER_1            Device = 23
	Device_RELAY_SCANNER_2            Device = 24
	Device_RELAY_SCANNER_3            Device = 25
	Device_RELAY_AC                   Device = 26
	Device_RELAY_CHILLER              Device = 27
	Device_RELAY_STROBE               Device = 28
	Device_RELAY_ENCODER_FRONT_LEFT   Device = 29
	Device_RELAY_ENCODER_FRONT_RIGHT  Device = 30
	Device_RELAY_ENCODER_BACK_LEFT    Device = 31
	Device_RELAY_ENCODER_BACK_RIGHT   Device = 32
	Device_SENSOR_ENCODER_FRONT_LEFT  Device = 33
	Device_SENSOR_ENCODER_FRONT_RIGHT Device = 34
	Device_SENSOR_ENCODER_BACK_LEFT   Device = 35
	Device_SENSOR_ENCODER_BACK_RIGHT  Device = 36
	Device_RELAY_GPS                  Device = 37
	Device_SENSOR_LATITUDE            Device = 38
	Device_SENSOR_LONGITUDE           Device = 39
	Device_SENSOR_KEY                 Device = 40
	Device_SENSOR_INTERLOCK           Device = 41
	Device_RELAY_ENCODER_BOARD        Device = 42
)

// Enum value maps for Device.
var (
	Device_name = map[int32]string{
		0:  "SENSOR_LIFTED",
		1:  "SENSOR_SERVER_TEMPERATURE",
		2:  "SENSOR_SERVER_HUMIDITY",
		3:  "SENSOR_WATER",
		4:  "SENSOR_12V_BATTERY",
		5:  "SENSOR_POWER_QUALITY",
		6:  "SENSOR_TRACTOR",
		7:  "SENSOR_AC_FREQUENCY",
		8:  "SENSOR_AB_VOLTAGE",
		9:  "SENSOR_BC_VOLTAGE",
		10: "SENSOR_AC_VOLTAGE",
		11: "SENSOR_A_CURRENT",
		12: "SENSOR_B_CURRENT",
		13: "SENSOR_C_CURRENT",
		14: "RELAY_SUICIDE",
		15: "RELAY_REBOOT",
		16: "RELAY_MAIN",
		17: "RELAY_ROW_1",
		18: "RELAY_ROW_2",
		19: "RELAY_ROW_3",
		20: "RELAY_LIGHTS_1",
		21: "RELAY_LIGHTS_2",
		22: "RELAY_LIGHTS_3",
		23: "RELAY_SCANNER_1",
		24: "RELAY_SCANNER_2",
		25: "RELAY_SCANNER_3",
		26: "RELAY_AC",
		27: "RELAY_CHILLER",
		28: "RELAY_STROBE",
		29: "RELAY_ENCODER_FRONT_LEFT",
		30: "RELAY_ENCODER_FRONT_RIGHT",
		31: "RELAY_ENCODER_BACK_LEFT",
		32: "RELAY_ENCODER_BACK_RIGHT",
		33: "SENSOR_ENCODER_FRONT_LEFT",
		34: "SENSOR_ENCODER_FRONT_RIGHT",
		35: "SENSOR_ENCODER_BACK_LEFT",
		36: "SENSOR_ENCODER_BACK_RIGHT",
		37: "RELAY_GPS",
		38: "SENSOR_LATITUDE",
		39: "SENSOR_LONGITUDE",
		40: "SENSOR_KEY",
		41: "SENSOR_INTERLOCK",
		42: "RELAY_ENCODER_BOARD",
	}
	Device_value = map[string]int32{
		"SENSOR_LIFTED":              0,
		"SENSOR_SERVER_TEMPERATURE":  1,
		"SENSOR_SERVER_HUMIDITY":     2,
		"SENSOR_WATER":               3,
		"SENSOR_12V_BATTERY":         4,
		"SENSOR_POWER_QUALITY":       5,
		"SENSOR_TRACTOR":             6,
		"SENSOR_AC_FREQUENCY":        7,
		"SENSOR_AB_VOLTAGE":          8,
		"SENSOR_BC_VOLTAGE":          9,
		"SENSOR_AC_VOLTAGE":          10,
		"SENSOR_A_CURRENT":           11,
		"SENSOR_B_CURRENT":           12,
		"SENSOR_C_CURRENT":           13,
		"RELAY_SUICIDE":              14,
		"RELAY_REBOOT":               15,
		"RELAY_MAIN":                 16,
		"RELAY_ROW_1":                17,
		"RELAY_ROW_2":                18,
		"RELAY_ROW_3":                19,
		"RELAY_LIGHTS_1":             20,
		"RELAY_LIGHTS_2":             21,
		"RELAY_LIGHTS_3":             22,
		"RELAY_SCANNER_1":            23,
		"RELAY_SCANNER_2":            24,
		"RELAY_SCANNER_3":            25,
		"RELAY_AC":                   26,
		"RELAY_CHILLER":              27,
		"RELAY_STROBE":               28,
		"RELAY_ENCODER_FRONT_LEFT":   29,
		"RELAY_ENCODER_FRONT_RIGHT":  30,
		"RELAY_ENCODER_BACK_LEFT":    31,
		"RELAY_ENCODER_BACK_RIGHT":   32,
		"SENSOR_ENCODER_FRONT_LEFT":  33,
		"SENSOR_ENCODER_FRONT_RIGHT": 34,
		"SENSOR_ENCODER_BACK_LEFT":   35,
		"SENSOR_ENCODER_BACK_RIGHT":  36,
		"RELAY_GPS":                  37,
		"SENSOR_LATITUDE":            38,
		"SENSOR_LONGITUDE":           39,
		"SENSOR_KEY":                 40,
		"SENSOR_INTERLOCK":           41,
		"RELAY_ENCODER_BOARD":        42,
	}
)

func (x Device) Enum() *Device {
	p := new(Device)
	*p = x
	return p
}

func (x Device) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Device) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_power_proto_enumTypes[0].Descriptor()
}

func (Device) Type() protoreflect.EnumType {
	return &file_frontend_proto_power_proto_enumTypes[0]
}

func (x Device) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Device.Descriptor instead.
func (Device) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{0}
}

type DeviceValueColor int32

const (
	DeviceValueColor_COLOR_GRAY   DeviceValueColor = 0
	DeviceValueColor_COLOR_GREEN  DeviceValueColor = 1
	DeviceValueColor_COLOR_ORANGE DeviceValueColor = 2
	DeviceValueColor_COLOR_RED    DeviceValueColor = 3
)

// Enum value maps for DeviceValueColor.
var (
	DeviceValueColor_name = map[int32]string{
		0: "COLOR_GRAY",
		1: "COLOR_GREEN",
		2: "COLOR_ORANGE",
		3: "COLOR_RED",
	}
	DeviceValueColor_value = map[string]int32{
		"COLOR_GRAY":   0,
		"COLOR_GREEN":  1,
		"COLOR_ORANGE": 2,
		"COLOR_RED":    3,
	}
)

func (x DeviceValueColor) Enum() *DeviceValueColor {
	p := new(DeviceValueColor)
	*p = x
	return p
}

func (x DeviceValueColor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceValueColor) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_power_proto_enumTypes[1].Descriptor()
}

func (DeviceValueColor) Type() protoreflect.EnumType {
	return &file_frontend_proto_power_proto_enumTypes[1]
}

func (x DeviceValueColor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceValueColor.Descriptor instead.
func (DeviceValueColor) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{1}
}

type NetworkLinkSpeed int32

const (
	NetworkLinkSpeed_UNKNOWN         NetworkLinkSpeed = 0
	NetworkLinkSpeed_SPEED_10M_HALF  NetworkLinkSpeed = 1
	NetworkLinkSpeed_SPEED_10M_FULL  NetworkLinkSpeed = 2
	NetworkLinkSpeed_SPEED_100M_HALF NetworkLinkSpeed = 3
	NetworkLinkSpeed_SPEED_100M_FULL NetworkLinkSpeed = 4
	NetworkLinkSpeed_SPEED_1G_FULL   NetworkLinkSpeed = 5
	NetworkLinkSpeed_SPEED_2G5_FULL  NetworkLinkSpeed = 6
	NetworkLinkSpeed_SPEED_5G_FULL   NetworkLinkSpeed = 7
	NetworkLinkSpeed_SPEED_10G_FULL  NetworkLinkSpeed = 8
)

// Enum value maps for NetworkLinkSpeed.
var (
	NetworkLinkSpeed_name = map[int32]string{
		0: "UNKNOWN",
		1: "SPEED_10M_HALF",
		2: "SPEED_10M_FULL",
		3: "SPEED_100M_HALF",
		4: "SPEED_100M_FULL",
		5: "SPEED_1G_FULL",
		6: "SPEED_2G5_FULL",
		7: "SPEED_5G_FULL",
		8: "SPEED_10G_FULL",
	}
	NetworkLinkSpeed_value = map[string]int32{
		"UNKNOWN":         0,
		"SPEED_10M_HALF":  1,
		"SPEED_10M_FULL":  2,
		"SPEED_100M_HALF": 3,
		"SPEED_100M_FULL": 4,
		"SPEED_1G_FULL":   5,
		"SPEED_2G5_FULL":  6,
		"SPEED_5G_FULL":   7,
		"SPEED_10G_FULL":  8,
	}
)

func (x NetworkLinkSpeed) Enum() *NetworkLinkSpeed {
	p := new(NetworkLinkSpeed)
	*p = x
	return p
}

func (x NetworkLinkSpeed) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkLinkSpeed) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_power_proto_enumTypes[2].Descriptor()
}

func (NetworkLinkSpeed) Type() protoreflect.EnumType {
	return &file_frontend_proto_power_proto_enumTypes[2]
}

func (x NetworkLinkSpeed) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkLinkSpeed.Descriptor instead.
func (NetworkLinkSpeed) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{2}
}

type AcPowerStatus int32

const (
	AcPowerStatus_POWER_UNKNOWN  AcPowerStatus = 0
	AcPowerStatus_POWER_GOOD     AcPowerStatus = 1
	AcPowerStatus_POWER_BAD      AcPowerStatus = 2
	AcPowerStatus_POWER_VERY_BAD AcPowerStatus = 3
)

// Enum value maps for AcPowerStatus.
var (
	AcPowerStatus_name = map[int32]string{
		0: "POWER_UNKNOWN",
		1: "POWER_GOOD",
		2: "POWER_BAD",
		3: "POWER_VERY_BAD",
	}
	AcPowerStatus_value = map[string]int32{
		"POWER_UNKNOWN":  0,
		"POWER_GOOD":     1,
		"POWER_BAD":      2,
		"POWER_VERY_BAD": 3,
	}
)

func (x AcPowerStatus) Enum() *AcPowerStatus {
	p := new(AcPowerStatus)
	*p = x
	return p
}

func (x AcPowerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AcPowerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_power_proto_enumTypes[3].Descriptor()
}

func (AcPowerStatus) Type() protoreflect.EnumType {
	return &file_frontend_proto_power_proto_enumTypes[3]
}

func (x AcPowerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AcPowerStatus.Descriptor instead.
func (AcPowerStatus) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{3}
}

type ModuleStatus int32

const (
	ModuleStatus_OK    ModuleStatus = 0
	ModuleStatus_Error ModuleStatus = 1
)

// Enum value maps for ModuleStatus.
var (
	ModuleStatus_name = map[int32]string{
		0: "OK",
		1: "Error",
	}
	ModuleStatus_value = map[string]int32{
		"OK":    0,
		"Error": 1,
	}
)

func (x ModuleStatus) Enum() *ModuleStatus {
	p := new(ModuleStatus)
	*p = x
	return p
}

func (x ModuleStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_power_proto_enumTypes[4].Descriptor()
}

func (ModuleStatus) Type() protoreflect.EnumType {
	return &file_frontend_proto_power_proto_enumTypes[4]
}

func (x ModuleStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleStatus.Descriptor instead.
func (ModuleStatus) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{4}
}

type DeviceStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Device Device `protobuf:"varint,1,opt,name=device,proto3,enum=carbon.frontend.power.Device" json:"device,omitempty"`
	Label  string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// Types that are assignable to Type:
	//
	//	*DeviceStatus_RelayType
	//	*DeviceStatus_SensorType
	Type isDeviceStatus_Type `protobuf_oneof:"type"`
}

func (x *DeviceStatus) Reset() {
	*x = DeviceStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceStatus) ProtoMessage() {}

func (x *DeviceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceStatus.ProtoReflect.Descriptor instead.
func (*DeviceStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceStatus) GetDevice() Device {
	if x != nil {
		return x.Device
	}
	return Device_SENSOR_LIFTED
}

func (x *DeviceStatus) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (m *DeviceStatus) GetType() isDeviceStatus_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (x *DeviceStatus) GetRelayType() *DeviceStatus_RelayStatus {
	if x, ok := x.GetType().(*DeviceStatus_RelayType); ok {
		return x.RelayType
	}
	return nil
}

func (x *DeviceStatus) GetSensorType() *DeviceStatus_SensorStatus {
	if x, ok := x.GetType().(*DeviceStatus_SensorType); ok {
		return x.SensorType
	}
	return nil
}

type isDeviceStatus_Type interface {
	isDeviceStatus_Type()
}

type DeviceStatus_RelayType struct {
	RelayType *DeviceStatus_RelayStatus `protobuf:"bytes,6,opt,name=relay_type,json=relayType,proto3,oneof"`
}

type DeviceStatus_SensorType struct {
	SensorType *DeviceStatus_SensorStatus `protobuf:"bytes,7,opt,name=sensor_type,json=sensorType,proto3,oneof"`
}

func (*DeviceStatus_RelayType) isDeviceStatus_Type() {}

func (*DeviceStatus_SensorType) isDeviceStatus_Type() {}

type PowerStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *PowerStatusRequest) Reset() {
	*x = PowerStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerStatusRequest) ProtoMessage() {}

func (x *PowerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerStatusRequest.ProtoReflect.Descriptor instead.
func (*PowerStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{1}
}

func (x *PowerStatusRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type PowerStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Devices []*DeviceStatus `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`
	Ts      *Timestamp      `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *PowerStatusResponse) Reset() {
	*x = PowerStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerStatusResponse) ProtoMessage() {}

func (x *PowerStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerStatusResponse.ProtoReflect.Descriptor instead.
func (*PowerStatusResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{2}
}

func (x *PowerStatusResponse) GetDevices() []*DeviceStatus {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *PowerStatusResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type ValueWithRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	IsOk  bool    `protobuf:"varint,2,opt,name=is_ok,json=isOk,proto3" json:"is_ok,omitempty"`
}

func (x *ValueWithRange) Reset() {
	*x = ValueWithRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueWithRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueWithRange) ProtoMessage() {}

func (x *ValueWithRange) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueWithRange.ProtoReflect.Descriptor instead.
func (*ValueWithRange) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{3}
}

func (x *ValueWithRange) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ValueWithRange) GetIsOk() bool {
	if x != nil {
		return x.IsOk
	}
	return false
}

type EnvironmentalSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemperatureC *ValueWithRange `protobuf:"bytes,1,opt,name=temperature_c,json=temperatureC,proto3" json:"temperature_c,omitempty"`
	HumidityRh   *ValueWithRange `protobuf:"bytes,2,opt,name=humidity_rh,json=humidityRh,proto3" json:"humidity_rh,omitempty"`
	PressureHpa  *ValueWithRange `protobuf:"bytes,3,opt,name=pressure_hpa,json=pressureHpa,proto3" json:"pressure_hpa,omitempty"`
}

func (x *EnvironmentalSensorData) Reset() {
	*x = EnvironmentalSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnvironmentalSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvironmentalSensorData) ProtoMessage() {}

func (x *EnvironmentalSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvironmentalSensorData.ProtoReflect.Descriptor instead.
func (*EnvironmentalSensorData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{4}
}

func (x *EnvironmentalSensorData) GetTemperatureC() *ValueWithRange {
	if x != nil {
		return x.TemperatureC
	}
	return nil
}

func (x *EnvironmentalSensorData) GetHumidityRh() *ValueWithRange {
	if x != nil {
		return x.HumidityRh
	}
	return nil
}

func (x *EnvironmentalSensorData) GetPressureHpa() *ValueWithRange {
	if x != nil {
		return x.PressureHpa
	}
	return nil
}

type CoolantSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemperatureC *ValueWithRange `protobuf:"bytes,1,opt,name=temperature_c,json=temperatureC,proto3" json:"temperature_c,omitempty"`
	PressureKpa  *ValueWithRange `protobuf:"bytes,2,opt,name=pressure_kpa,json=pressureKpa,proto3" json:"pressure_kpa,omitempty"`
}

func (x *CoolantSensorData) Reset() {
	*x = CoolantSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoolantSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoolantSensorData) ProtoMessage() {}

func (x *CoolantSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoolantSensorData.ProtoReflect.Descriptor instead.
func (*CoolantSensorData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{5}
}

func (x *CoolantSensorData) GetTemperatureC() *ValueWithRange {
	if x != nil {
		return x.TemperatureC
	}
	return nil
}

func (x *CoolantSensorData) GetPressureKpa() *ValueWithRange {
	if x != nil {
		return x.PressureKpa
	}
	return nil
}

type NetworkPortState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LinkUp            bool             `protobuf:"varint,1,opt,name=link_up,json=linkUp,proto3" json:"link_up,omitempty"`
	ActualLinkSpeed   NetworkLinkSpeed `protobuf:"varint,2,opt,name=actual_link_speed,json=actualLinkSpeed,proto3,enum=carbon.frontend.power.NetworkLinkSpeed" json:"actual_link_speed,omitempty"`
	ExpectedLinkSpeed NetworkLinkSpeed `protobuf:"varint,3,opt,name=expected_link_speed,json=expectedLinkSpeed,proto3,enum=carbon.frontend.power.NetworkLinkSpeed" json:"expected_link_speed,omitempty"`
}

func (x *NetworkPortState) Reset() {
	*x = NetworkPortState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkPortState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPortState) ProtoMessage() {}

func (x *NetworkPortState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPortState.ProtoReflect.Descriptor instead.
func (*NetworkPortState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{6}
}

func (x *NetworkPortState) GetLinkUp() bool {
	if x != nil {
		return x.LinkUp
	}
	return false
}

func (x *NetworkPortState) GetActualLinkSpeed() NetworkLinkSpeed {
	if x != nil {
		return x.ActualLinkSpeed
	}
	return NetworkLinkSpeed_UNKNOWN
}

func (x *NetworkPortState) GetExpectedLinkSpeed() NetworkLinkSpeed {
	if x != nil {
		return x.ExpectedLinkSpeed
	}
	return NetworkLinkSpeed_UNKNOWN
}

type ReaperPcSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemperatureCpuCoreC *ValueWithRange `protobuf:"bytes,1,opt,name=temperature_cpu_core_c,json=temperatureCpuCoreC,proto3" json:"temperature_cpu_core_c,omitempty"`
	TemperatureSystemC  *ValueWithRange `protobuf:"bytes,2,opt,name=temperature_system_c,json=temperatureSystemC,proto3" json:"temperature_system_c,omitempty"`
	TemperatureGpu_1C   *ValueWithRange `protobuf:"bytes,3,opt,name=temperature_gpu_1_c,json=temperatureGpu1C,proto3,oneof" json:"temperature_gpu_1_c,omitempty"`
	TemperatureGpu_2C   *ValueWithRange `protobuf:"bytes,4,opt,name=temperature_gpu_2_c,json=temperatureGpu2C,proto3,oneof" json:"temperature_gpu_2_c,omitempty"`
	Psu_12V             *ValueWithRange `protobuf:"bytes,5,opt,name=psu_12v,json=psu12v,proto3" json:"psu_12v,omitempty"`
	Psu_5V              *ValueWithRange `protobuf:"bytes,6,opt,name=psu_5v,json=psu5v,proto3" json:"psu_5v,omitempty"`
	Psu_3V3             *ValueWithRange `protobuf:"bytes,7,opt,name=psu_3v3,json=psu3v3,proto3" json:"psu_3v3,omitempty"`
	// CPU load average
	Load *ValueWithRange `protobuf:"bytes,8,opt,name=load,proto3" json:"load,omitempty"`
	// system uptime, in seconds
	Uptime uint32 `protobuf:"varint,9,opt,name=uptime,proto3" json:"uptime,omitempty"`
	// Memory (RAM) utilization
	RamUsagePercent *ValueWithRange `protobuf:"bytes,10,opt,name=ram_usage_percent,json=ramUsagePercent,proto3" json:"ram_usage_percent,omitempty"`
	// Disk space utilization
	DiskUsagePercent *ValueWithRange `protobuf:"bytes,11,opt,name=disk_usage_percent,json=diskUsagePercent,proto3" json:"disk_usage_percent,omitempty"`
	// Link state for scanner PCBs
	ScannerALink *NetworkPortState `protobuf:"bytes,12,opt,name=scanner_a_link,json=scannerALink,proto3" json:"scanner_a_link,omitempty"`
	ScannerBLink *NetworkPortState `protobuf:"bytes,13,opt,name=scanner_b_link,json=scannerBLink,proto3" json:"scanner_b_link,omitempty"`
	// Link state for target cam
	TargetCamALink *NetworkPortState `protobuf:"bytes,14,opt,name=target_cam_a_link,json=targetCamALink,proto3" json:"target_cam_a_link,omitempty"`
	TargetCamBLink *NetworkPortState `protobuf:"bytes,15,opt,name=target_cam_b_link,json=targetCamBLink,proto3" json:"target_cam_b_link,omitempty"`
	// Link state for predict cam
	PredictCamLink *NetworkPortState `protobuf:"bytes,16,opt,name=predict_cam_link,json=predictCamLink,proto3" json:"predict_cam_link,omitempty"`
	// IPMI port link state
	IpmiLink *NetworkPortState `protobuf:"bytes,17,opt,name=ipmi_link,json=ipmiLink,proto3" json:"ipmi_link,omitempty"`
	// Global network uplink port link state (ext port)
	ExtLink *NetworkPortState `protobuf:"bytes,18,opt,name=ext_link,json=extLink,proto3" json:"ext_link,omitempty"`
}

func (x *ReaperPcSensorData) Reset() {
	*x = ReaperPcSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperPcSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperPcSensorData) ProtoMessage() {}

func (x *ReaperPcSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperPcSensorData.ProtoReflect.Descriptor instead.
func (*ReaperPcSensorData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{7}
}

func (x *ReaperPcSensorData) GetTemperatureCpuCoreC() *ValueWithRange {
	if x != nil {
		return x.TemperatureCpuCoreC
	}
	return nil
}

func (x *ReaperPcSensorData) GetTemperatureSystemC() *ValueWithRange {
	if x != nil {
		return x.TemperatureSystemC
	}
	return nil
}

func (x *ReaperPcSensorData) GetTemperatureGpu_1C() *ValueWithRange {
	if x != nil {
		return x.TemperatureGpu_1C
	}
	return nil
}

func (x *ReaperPcSensorData) GetTemperatureGpu_2C() *ValueWithRange {
	if x != nil {
		return x.TemperatureGpu_2C
	}
	return nil
}

func (x *ReaperPcSensorData) GetPsu_12V() *ValueWithRange {
	if x != nil {
		return x.Psu_12V
	}
	return nil
}

func (x *ReaperPcSensorData) GetPsu_5V() *ValueWithRange {
	if x != nil {
		return x.Psu_5V
	}
	return nil
}

func (x *ReaperPcSensorData) GetPsu_3V3() *ValueWithRange {
	if x != nil {
		return x.Psu_3V3
	}
	return nil
}

func (x *ReaperPcSensorData) GetLoad() *ValueWithRange {
	if x != nil {
		return x.Load
	}
	return nil
}

func (x *ReaperPcSensorData) GetUptime() uint32 {
	if x != nil {
		return x.Uptime
	}
	return 0
}

func (x *ReaperPcSensorData) GetRamUsagePercent() *ValueWithRange {
	if x != nil {
		return x.RamUsagePercent
	}
	return nil
}

func (x *ReaperPcSensorData) GetDiskUsagePercent() *ValueWithRange {
	if x != nil {
		return x.DiskUsagePercent
	}
	return nil
}

func (x *ReaperPcSensorData) GetScannerALink() *NetworkPortState {
	if x != nil {
		return x.ScannerALink
	}
	return nil
}

func (x *ReaperPcSensorData) GetScannerBLink() *NetworkPortState {
	if x != nil {
		return x.ScannerBLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetTargetCamALink() *NetworkPortState {
	if x != nil {
		return x.TargetCamALink
	}
	return nil
}

func (x *ReaperPcSensorData) GetTargetCamBLink() *NetworkPortState {
	if x != nil {
		return x.TargetCamBLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetPredictCamLink() *NetworkPortState {
	if x != nil {
		return x.PredictCamLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetIpmiLink() *NetworkPortState {
	if x != nil {
		return x.IpmiLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetExtLink() *NetworkPortState {
	if x != nil {
		return x.ExtLink
	}
	return nil
}

// Info about the BWT laser connected to Reaper scanner
type ReaperScannerLaserStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Laser model number
	Model string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	// Serial number reported by laser
	Sn string `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	// Rated laser power (W)
	RatedPower uint32 `protobuf:"varint,3,opt,name=rated_power,json=ratedPower,proto3" json:"rated_power,omitempty"`
	// Internal temperature
	TemperatureC *ValueWithRange `protobuf:"bytes,4,opt,name=temperature_c,json=temperatureC,proto3" json:"temperature_c,omitempty"`
	// Laser humidity
	Humidity *ValueWithRange `protobuf:"bytes,5,opt,name=humidity,proto3" json:"humidity,omitempty"`
	// Current through laser diodes
	LaserCurrentMa *ValueWithRange `protobuf:"bytes,6,opt,name=laser_current_ma,json=laserCurrentMa,proto3" json:"laser_current_ma,omitempty"`
	// Laser faults (if any)
	Faults []string `protobuf:"bytes,7,rep,name=faults,proto3" json:"faults,omitempty"`
}

func (x *ReaperScannerLaserStatus) Reset() {
	*x = ReaperScannerLaserStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperScannerLaserStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperScannerLaserStatus) ProtoMessage() {}

func (x *ReaperScannerLaserStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperScannerLaserStatus.ProtoReflect.Descriptor instead.
func (*ReaperScannerLaserStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{8}
}

func (x *ReaperScannerLaserStatus) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ReaperScannerLaserStatus) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ReaperScannerLaserStatus) GetRatedPower() uint32 {
	if x != nil {
		return x.RatedPower
	}
	return 0
}

func (x *ReaperScannerLaserStatus) GetTemperatureC() *ValueWithRange {
	if x != nil {
		return x.TemperatureC
	}
	return nil
}

func (x *ReaperScannerLaserStatus) GetHumidity() *ValueWithRange {
	if x != nil {
		return x.Humidity
	}
	return nil
}

func (x *ReaperScannerLaserStatus) GetLaserCurrentMa() *ValueWithRange {
	if x != nil {
		return x.LaserCurrentMa
	}
	return nil
}

func (x *ReaperScannerLaserStatus) GetFaults() []string {
	if x != nil {
		return x.Faults
	}
	return nil
}

// Information for each of the motors/controllers
type ReaperScannerMotorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Serial number of motor controller
	ControllerSn string `protobuf:"bytes,1,opt,name=controller_sn,json=controllerSn,proto3" json:"controller_sn,omitempty"`
	// Output/driver stage temperature
	TemperatureOutputC *ValueWithRange `protobuf:"bytes,2,opt,name=temperature_output_c,json=temperatureOutputC,proto3" json:"temperature_output_c,omitempty"`
	// Motor supply voltage
	MotorSupplyV *ValueWithRange `protobuf:"bytes,3,opt,name=motor_supply_v,json=motorSupplyV,proto3" json:"motor_supply_v,omitempty"`
	// Instantaneous motor current
	MotorCurrentA *ValueWithRange `protobuf:"bytes,4,opt,name=motor_current_a,json=motorCurrentA,proto3" json:"motor_current_a,omitempty"`
	// Current encoder position, ticks
	EncoderPosition int64 `protobuf:"varint,5,opt,name=encoder_position,json=encoderPosition,proto3" json:"encoder_position,omitempty"`
}

func (x *ReaperScannerMotorData) Reset() {
	*x = ReaperScannerMotorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperScannerMotorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperScannerMotorData) ProtoMessage() {}

func (x *ReaperScannerMotorData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperScannerMotorData.ProtoReflect.Descriptor instead.
func (*ReaperScannerMotorData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{9}
}

func (x *ReaperScannerMotorData) GetControllerSn() string {
	if x != nil {
		return x.ControllerSn
	}
	return ""
}

func (x *ReaperScannerMotorData) GetTemperatureOutputC() *ValueWithRange {
	if x != nil {
		return x.TemperatureOutputC
	}
	return nil
}

func (x *ReaperScannerMotorData) GetMotorSupplyV() *ValueWithRange {
	if x != nil {
		return x.MotorSupplyV
	}
	return nil
}

func (x *ReaperScannerMotorData) GetMotorCurrentA() *ValueWithRange {
	if x != nil {
		return x.MotorCurrentA
	}
	return nil
}

func (x *ReaperScannerMotorData) GetEncoderPosition() int64 {
	if x != nil {
		return x.EncoderPosition
	}
	return 0
}

// Sensor data readings for the Reaper scanners
type ReaperScannerSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Scanner assembly serial number
	ScannerSn string `protobuf:"bytes,1,opt,name=scanner_sn,json=scannerSn,proto3" json:"scanner_sn,omitempty"`
	// Scanner current consumption
	CurrentA *ValueWithRange `protobuf:"bytes,2,opt,name=current_a,json=currentA,proto3" json:"current_a,omitempty"`
	// Whether fuse to scanner is tripped
	FuseTripped bool `protobuf:"varint,3,opt,name=fuse_tripped,json=fuseTripped,proto3" json:"fuse_tripped,omitempty"`
	// Temperature of collimator (°C)
	TemperatureCollimatorC *ValueWithRange `protobuf:"bytes,4,opt,name=temperature_collimator_c,json=temperatureCollimatorC,proto3" json:"temperature_collimator_c,omitempty"`
	// laser fiber connection temperature (°C)
	TemperatureFiberC *ValueWithRange `protobuf:"bytes,5,opt,name=temperature_fiber_c,json=temperatureFiberC,proto3" json:"temperature_fiber_c,omitempty"`
	// Approximate laser power reading from photodiode
	LaserPowerW *ValueWithRange `protobuf:"bytes,6,opt,name=laser_power_w,json=laserPowerW,proto3" json:"laser_power_w,omitempty"`
	// Whether laser is connected
	LaserConnected bool `protobuf:"varint,7,opt,name=laser_connected,json=laserConnected,proto3" json:"laser_connected,omitempty"`
	// Additional laser status information
	LaserStatus *ReaperScannerLaserStatus `protobuf:"bytes,8,opt,name=laser_status,json=laserStatus,proto3,oneof" json:"laser_status,omitempty"`
	// If target cam is connected
	TargetConnected bool `protobuf:"varint,9,opt,name=target_connected,json=targetConnected,proto3" json:"target_connected,omitempty"`
	// Target camera's serial number
	TargetSn *string `protobuf:"bytes,10,opt,name=target_sn,json=targetSn,proto3,oneof" json:"target_sn,omitempty"`
	// Temperature of target camera
	TemperatureTargetC *ValueWithRange `protobuf:"bytes,11,opt,name=temperature_target_c,json=temperatureTargetC,proto3,oneof" json:"temperature_target_c,omitempty"`
	// Individual motor controller info
	MotorPan  *ReaperScannerMotorData `protobuf:"bytes,12,opt,name=motor_pan,json=motorPan,proto3,oneof" json:"motor_pan,omitempty"`
	MotorTilt *ReaperScannerMotorData `protobuf:"bytes,13,opt,name=motor_tilt,json=motorTilt,proto3,oneof" json:"motor_tilt,omitempty"`
	// whether switchable power to the scanner itself is enabled
	ScannerPowerEnabled bool `protobuf:"varint,14,opt,name=scanner_power_enabled,json=scannerPowerEnabled,proto3" json:"scanner_power_enabled,omitempty"`
	// whether power to the target cam (controlled via scanner) is enabled
	TargetCamPowerEnabled bool `protobuf:"varint,15,opt,name=target_cam_power_enabled,json=targetCamPowerEnabled,proto3" json:"target_cam_power_enabled,omitempty"`
}

func (x *ReaperScannerSensorData) Reset() {
	*x = ReaperScannerSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperScannerSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperScannerSensorData) ProtoMessage() {}

func (x *ReaperScannerSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperScannerSensorData.ProtoReflect.Descriptor instead.
func (*ReaperScannerSensorData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{10}
}

func (x *ReaperScannerSensorData) GetScannerSn() string {
	if x != nil {
		return x.ScannerSn
	}
	return ""
}

func (x *ReaperScannerSensorData) GetCurrentA() *ValueWithRange {
	if x != nil {
		return x.CurrentA
	}
	return nil
}

func (x *ReaperScannerSensorData) GetFuseTripped() bool {
	if x != nil {
		return x.FuseTripped
	}
	return false
}

func (x *ReaperScannerSensorData) GetTemperatureCollimatorC() *ValueWithRange {
	if x != nil {
		return x.TemperatureCollimatorC
	}
	return nil
}

func (x *ReaperScannerSensorData) GetTemperatureFiberC() *ValueWithRange {
	if x != nil {
		return x.TemperatureFiberC
	}
	return nil
}

func (x *ReaperScannerSensorData) GetLaserPowerW() *ValueWithRange {
	if x != nil {
		return x.LaserPowerW
	}
	return nil
}

func (x *ReaperScannerSensorData) GetLaserConnected() bool {
	if x != nil {
		return x.LaserConnected
	}
	return false
}

func (x *ReaperScannerSensorData) GetLaserStatus() *ReaperScannerLaserStatus {
	if x != nil {
		return x.LaserStatus
	}
	return nil
}

func (x *ReaperScannerSensorData) GetTargetConnected() bool {
	if x != nil {
		return x.TargetConnected
	}
	return false
}

func (x *ReaperScannerSensorData) GetTargetSn() string {
	if x != nil && x.TargetSn != nil {
		return *x.TargetSn
	}
	return ""
}

func (x *ReaperScannerSensorData) GetTemperatureTargetC() *ValueWithRange {
	if x != nil {
		return x.TemperatureTargetC
	}
	return nil
}

func (x *ReaperScannerSensorData) GetMotorPan() *ReaperScannerMotorData {
	if x != nil {
		return x.MotorPan
	}
	return nil
}

func (x *ReaperScannerSensorData) GetMotorTilt() *ReaperScannerMotorData {
	if x != nil {
		return x.MotorTilt
	}
	return nil
}

func (x *ReaperScannerSensorData) GetScannerPowerEnabled() bool {
	if x != nil {
		return x.ScannerPowerEnabled
	}
	return false
}

func (x *ReaperScannerSensorData) GetTargetCamPowerEnabled() bool {
	if x != nil {
		return x.TargetCamPowerEnabled
	}
	return false
}

type ReaperGpsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasFix    bool    `protobuf:"varint,1,opt,name=has_fix,json=hasFix,proto3" json:"has_fix,omitempty"`
	Latitude  float32 `protobuf:"fixed32,2,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude float32 `protobuf:"fixed32,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
}

func (x *ReaperGpsData) Reset() {
	*x = ReaperGpsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperGpsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperGpsData) ProtoMessage() {}

func (x *ReaperGpsData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperGpsData.ProtoReflect.Descriptor instead.
func (*ReaperGpsData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{11}
}

func (x *ReaperGpsData) GetHasFix() bool {
	if x != nil {
		return x.HasFix
	}
	return false
}

func (x *ReaperGpsData) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *ReaperGpsData) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

type ReaperWheelEncoderData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FrontLeft  int64 `protobuf:"varint,1,opt,name=front_left,json=frontLeft,proto3" json:"front_left,omitempty"`
	FrontRight int64 `protobuf:"varint,2,opt,name=front_right,json=frontRight,proto3" json:"front_right,omitempty"`
}

func (x *ReaperWheelEncoderData) Reset() {
	*x = ReaperWheelEncoderData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperWheelEncoderData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperWheelEncoderData) ProtoMessage() {}

func (x *ReaperWheelEncoderData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperWheelEncoderData.ProtoReflect.Descriptor instead.
func (*ReaperWheelEncoderData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{12}
}

func (x *ReaperWheelEncoderData) GetFrontLeft() int64 {
	if x != nil {
		return x.FrontLeft
	}
	return 0
}

func (x *ReaperWheelEncoderData) GetFrontRight() int64 {
	if x != nil {
		return x.FrontRight
	}
	return 0
}

// All readings from stuff in the center enclosure
// Similar to GetSupervisoryStatusResponse
type ReaperCenterEnclosureData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WaterProtectStatus    bool          `protobuf:"varint,1,opt,name=water_protect_status,json=waterProtectStatus,proto3" json:"water_protect_status,omitempty"`
	MainContactorStatusFb bool          `protobuf:"varint,2,opt,name=main_contactor_status_fb,json=mainContactorStatusFb,proto3" json:"main_contactor_status_fb,omitempty"`
	PowerStatus           AcPowerStatus `protobuf:"varint,3,opt,name=power_status,json=powerStatus,proto3,enum=carbon.frontend.power.AcPowerStatus" json:"power_status,omitempty"`
	LiftedStatus          bool          `protobuf:"varint,4,opt,name=lifted_status,json=liftedStatus,proto3" json:"lifted_status,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/power.proto.
	TempHumidityStatus    bool            `protobuf:"varint,5,opt,name=temp_humidity_status,json=tempHumidityStatus,proto3" json:"temp_humidity_status,omitempty"`
	TractorPower          bool            `protobuf:"varint,6,opt,name=tractor_power,json=tractorPower,proto3" json:"tractor_power,omitempty"`
	AcFrequency           *ValueWithRange `protobuf:"bytes,7,opt,name=ac_frequency,json=acFrequency,proto3" json:"ac_frequency,omitempty"`
	AcVoltageAB           *ValueWithRange `protobuf:"bytes,8,opt,name=ac_voltage_a_b,json=acVoltageAB,proto3" json:"ac_voltage_a_b,omitempty"`
	AcVoltageBC           *ValueWithRange `protobuf:"bytes,9,opt,name=ac_voltage_b_c,json=acVoltageBC,proto3" json:"ac_voltage_b_c,omitempty"`
	AcVoltageAC           *ValueWithRange `protobuf:"bytes,10,opt,name=ac_voltage_a_c,json=acVoltageAC,proto3" json:"ac_voltage_a_c,omitempty"`
	AcVoltageA            *ValueWithRange `protobuf:"bytes,11,opt,name=ac_voltage_a,json=acVoltageA,proto3" json:"ac_voltage_a,omitempty"`
	AcVoltageB            *ValueWithRange `protobuf:"bytes,12,opt,name=ac_voltage_b,json=acVoltageB,proto3" json:"ac_voltage_b,omitempty"`
	AcVoltageC            *ValueWithRange `protobuf:"bytes,13,opt,name=ac_voltage_c,json=acVoltageC,proto3" json:"ac_voltage_c,omitempty"`
	PhasePowerW_3         int64           `protobuf:"varint,14,opt,name=phase_power_w_3,json=phasePowerW3,proto3" json:"phase_power_w_3,omitempty"`
	PhasePowerVa_3        int64           `protobuf:"varint,15,opt,name=phase_power_va_3,json=phasePowerVa3,proto3" json:"phase_power_va_3,omitempty"`
	PowerFactor           *ValueWithRange `protobuf:"bytes,16,opt,name=power_factor,json=powerFactor,proto3" json:"power_factor,omitempty"`
	ServerCabinetTemp     *ValueWithRange `protobuf:"bytes,17,opt,name=server_cabinet_temp,json=serverCabinetTemp,proto3" json:"server_cabinet_temp,omitempty"`
	ServerCabinetHumidity *ValueWithRange `protobuf:"bytes,18,opt,name=server_cabinet_humidity,json=serverCabinetHumidity,proto3" json:"server_cabinet_humidity,omitempty"`
	BatteryVoltage_12V    *ValueWithRange `protobuf:"bytes,19,opt,name=battery_voltage_12v,json=batteryVoltage12v,proto3" json:"battery_voltage_12v,omitempty"`
	WheelEncoderDisabled  bool            `protobuf:"varint,20,opt,name=wheel_encoder_disabled,json=wheelEncoderDisabled,proto3" json:"wheel_encoder_disabled,omitempty"`
	// per module, this should be removed
	//
	// Deprecated: Marked as deprecated in frontend/proto/power.proto.
	StrobeDisabled         bool     `protobuf:"varint,21,opt,name=strobe_disabled,json=strobeDisabled,proto3" json:"strobe_disabled,omitempty"`
	GpsDisabled            bool     `protobuf:"varint,22,opt,name=gps_disabled,json=gpsDisabled,proto3" json:"gps_disabled,omitempty"`
	MainContactorDisabled  bool     `protobuf:"varint,23,opt,name=main_contactor_disabled,json=mainContactorDisabled,proto3" json:"main_contactor_disabled,omitempty"`
	AirConditionerDisabled bool     `protobuf:"varint,24,opt,name=air_conditioner_disabled,json=airConditionerDisabled,proto3" json:"air_conditioner_disabled,omitempty"`
	ChillerDisabled        bool     `protobuf:"varint,25,opt,name=chiller_disabled,json=chillerDisabled,proto3" json:"chiller_disabled,omitempty"`
	ChillerAlarms          []string `protobuf:"bytes,26,rep,name=chiller_alarms,json=chillerAlarms,proto3" json:"chiller_alarms,omitempty"`
	// actual chiller circulating fluid temp
	ChillerTempC *ValueWithRange `protobuf:"bytes,27,opt,name=chiller_temp_c,json=chillerTempC,proto3" json:"chiller_temp_c,omitempty"`
	// litres per minute
	ChillerFlowLMin *ValueWithRange `protobuf:"bytes,28,opt,name=chiller_flow_l_min,json=chillerFlowLMin,proto3" json:"chiller_flow_l_min,omitempty"`
	// circulating fluid pressure at chiller
	ChillerPressurePsi *ValueWithRange `protobuf:"bytes,29,opt,name=chiller_pressure_psi,json=chillerPressurePsi,proto3" json:"chiller_pressure_psi,omitempty"`
	// microsiemens per cm
	ChillerConductivityUsCm *ValueWithRange `protobuf:"bytes,30,opt,name=chiller_conductivity_us_cm,json=chillerConductivityUsCm,proto3" json:"chiller_conductivity_us_cm,omitempty"`
	// circulating fluid target temperature
	ChillerSetTempC         *ValueWithRange         `protobuf:"bytes,31,opt,name=chiller_set_temp_c,json=chillerSetTempC,proto3" json:"chiller_set_temp_c,omitempty"`
	ChillerHeatTransferKbtu *ValueWithRange         `protobuf:"bytes,32,opt,name=chiller_heat_transfer_kbtu,json=chillerHeatTransferKbtu,proto3" json:"chiller_heat_transfer_kbtu,omitempty"`
	ChillerFluidDeltaTempC  *ValueWithRange         `protobuf:"bytes,33,opt,name=chiller_fluid_delta_temp_c,json=chillerFluidDeltaTempC,proto3" json:"chiller_fluid_delta_temp_c,omitempty"`
	Gps                     *ReaperGpsData          `protobuf:"bytes,34,opt,name=gps,proto3" json:"gps,omitempty"`
	WheelEncoder            *ReaperWheelEncoderData `protobuf:"bytes,35,opt,name=wheel_encoder,json=wheelEncoder,proto3" json:"wheel_encoder,omitempty"`
}

func (x *ReaperCenterEnclosureData) Reset() {
	*x = ReaperCenterEnclosureData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperCenterEnclosureData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperCenterEnclosureData) ProtoMessage() {}

func (x *ReaperCenterEnclosureData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperCenterEnclosureData.ProtoReflect.Descriptor instead.
func (*ReaperCenterEnclosureData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{13}
}

func (x *ReaperCenterEnclosureData) GetWaterProtectStatus() bool {
	if x != nil {
		return x.WaterProtectStatus
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetMainContactorStatusFb() bool {
	if x != nil {
		return x.MainContactorStatusFb
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetPowerStatus() AcPowerStatus {
	if x != nil {
		return x.PowerStatus
	}
	return AcPowerStatus_POWER_UNKNOWN
}

func (x *ReaperCenterEnclosureData) GetLiftedStatus() bool {
	if x != nil {
		return x.LiftedStatus
	}
	return false
}

// Deprecated: Marked as deprecated in frontend/proto/power.proto.
func (x *ReaperCenterEnclosureData) GetTempHumidityStatus() bool {
	if x != nil {
		return x.TempHumidityStatus
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetTractorPower() bool {
	if x != nil {
		return x.TractorPower
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetAcFrequency() *ValueWithRange {
	if x != nil {
		return x.AcFrequency
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetAcVoltageAB() *ValueWithRange {
	if x != nil {
		return x.AcVoltageAB
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetAcVoltageBC() *ValueWithRange {
	if x != nil {
		return x.AcVoltageBC
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetAcVoltageAC() *ValueWithRange {
	if x != nil {
		return x.AcVoltageAC
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetAcVoltageA() *ValueWithRange {
	if x != nil {
		return x.AcVoltageA
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetAcVoltageB() *ValueWithRange {
	if x != nil {
		return x.AcVoltageB
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetAcVoltageC() *ValueWithRange {
	if x != nil {
		return x.AcVoltageC
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetPhasePowerW_3() int64 {
	if x != nil {
		return x.PhasePowerW_3
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetPhasePowerVa_3() int64 {
	if x != nil {
		return x.PhasePowerVa_3
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetPowerFactor() *ValueWithRange {
	if x != nil {
		return x.PowerFactor
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetServerCabinetTemp() *ValueWithRange {
	if x != nil {
		return x.ServerCabinetTemp
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetServerCabinetHumidity() *ValueWithRange {
	if x != nil {
		return x.ServerCabinetHumidity
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetBatteryVoltage_12V() *ValueWithRange {
	if x != nil {
		return x.BatteryVoltage_12V
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetWheelEncoderDisabled() bool {
	if x != nil {
		return x.WheelEncoderDisabled
	}
	return false
}

// Deprecated: Marked as deprecated in frontend/proto/power.proto.
func (x *ReaperCenterEnclosureData) GetStrobeDisabled() bool {
	if x != nil {
		return x.StrobeDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetGpsDisabled() bool {
	if x != nil {
		return x.GpsDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetMainContactorDisabled() bool {
	if x != nil {
		return x.MainContactorDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetAirConditionerDisabled() bool {
	if x != nil {
		return x.AirConditionerDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetChillerDisabled() bool {
	if x != nil {
		return x.ChillerDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetChillerAlarms() []string {
	if x != nil {
		return x.ChillerAlarms
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerTempC() *ValueWithRange {
	if x != nil {
		return x.ChillerTempC
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerFlowLMin() *ValueWithRange {
	if x != nil {
		return x.ChillerFlowLMin
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerPressurePsi() *ValueWithRange {
	if x != nil {
		return x.ChillerPressurePsi
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerConductivityUsCm() *ValueWithRange {
	if x != nil {
		return x.ChillerConductivityUsCm
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerSetTempC() *ValueWithRange {
	if x != nil {
		return x.ChillerSetTempC
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerHeatTransferKbtu() *ValueWithRange {
	if x != nil {
		return x.ChillerHeatTransferKbtu
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetChillerFluidDeltaTempC() *ValueWithRange {
	if x != nil {
		return x.ChillerFluidDeltaTempC
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetGps() *ReaperGpsData {
	if x != nil {
		return x.Gps
	}
	return nil
}

func (x *ReaperCenterEnclosureData) GetWheelEncoder() *ReaperWheelEncoderData {
	if x != nil {
		return x.WheelEncoder
	}
	return nil
}

// Sensor readings from a single module
type ReaperModuleSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// module ID from which this data was read
	ModuleId int32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	// serial number of the ID from which this data was read
	ModuleSn string `protobuf:"bytes,2,opt,name=module_sn,json=moduleSn,proto3" json:"module_sn,omitempty"`
	// Sensor on MCB (general enclosure area)
	EnviroEnclosure *EnvironmentalSensorData `protobuf:"bytes,3,opt,name=enviro_enclosure,json=enviroEnclosure,proto3" json:"enviro_enclosure,omitempty"`
	// Remote sensor in PC enclosure area
	EnviroPc *EnvironmentalSensorData `protobuf:"bytes,4,opt,name=enviro_pc,json=enviroPc,proto3" json:"enviro_pc,omitempty"`
	// Coolant inlet sensor
	CoolantInlet *CoolantSensorData `protobuf:"bytes,5,opt,name=coolant_inlet,json=coolantInlet,proto3" json:"coolant_inlet,omitempty"`
	// Coolant outlet sensor
	CoolantOutlet *CoolantSensorData `protobuf:"bytes,6,opt,name=coolant_outlet,json=coolantOutlet,proto3" json:"coolant_outlet,omitempty"`
	// Strobe board temperature
	StrobeTemperatureC *ValueWithRange `protobuf:"bytes,7,opt,name=strobe_temperature_c,json=strobeTemperatureC,proto3" json:"strobe_temperature_c,omitempty"`
	// Strobe capacitor voltage
	StrobeCapVoltage *ValueWithRange `protobuf:"bytes,8,opt,name=strobe_cap_voltage,json=strobeCapVoltage,proto3" json:"strobe_cap_voltage,omitempty"`
	// Current (averaged) through the LEDs
	StrobeCurrent *ValueWithRange `protobuf:"bytes,9,opt,name=strobe_current,json=strobeCurrent,proto3" json:"strobe_current,omitempty"`
	// Detailed information from sensors on the module PC
	Pc *ReaperPcSensorData `protobuf:"bytes,10,opt,name=pc,proto3,oneof" json:"pc,omitempty"`
	// Info about each of the scanners and target cams
	ScannerA               *ReaperScannerSensorData `protobuf:"bytes,11,opt,name=scanner_a,json=scannerA,proto3,oneof" json:"scanner_a,omitempty"`
	ScannerB               *ReaperScannerSensorData `protobuf:"bytes,12,opt,name=scanner_b,json=scannerB,proto3,oneof" json:"scanner_b,omitempty"`
	PcPowerEnabled         bool                     `protobuf:"varint,13,opt,name=pc_power_enabled,json=pcPowerEnabled,proto3" json:"pc_power_enabled,omitempty"`
	LasersPowerEnabled     bool                     `protobuf:"varint,14,opt,name=lasers_power_enabled,json=lasersPowerEnabled,proto3" json:"lasers_power_enabled,omitempty"`
	PredictCamPowerEnabled bool                     `protobuf:"varint,15,opt,name=predict_cam_power_enabled,json=predictCamPowerEnabled,proto3" json:"predict_cam_power_enabled,omitempty"`
	// whether the relay for BTL power supply is on
	StrobePowerEnabled bool `protobuf:"varint,16,opt,name=strobe_power_enabled,json=strobePowerEnabled,proto3" json:"strobe_power_enabled,omitempty"`
	// whether software is requesting strobe light triggering
	StrobeEnabled bool         `protobuf:"varint,17,opt,name=strobe_enabled,json=strobeEnabled,proto3" json:"strobe_enabled,omitempty"`
	Status        ModuleStatus `protobuf:"varint,18,opt,name=status,proto3,enum=carbon.frontend.power.ModuleStatus" json:"status,omitempty"`
}

func (x *ReaperModuleSensorData) Reset() {
	*x = ReaperModuleSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperModuleSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperModuleSensorData) ProtoMessage() {}

func (x *ReaperModuleSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperModuleSensorData.ProtoReflect.Descriptor instead.
func (*ReaperModuleSensorData) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{14}
}

func (x *ReaperModuleSensorData) GetModuleId() int32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *ReaperModuleSensorData) GetModuleSn() string {
	if x != nil {
		return x.ModuleSn
	}
	return ""
}

func (x *ReaperModuleSensorData) GetEnviroEnclosure() *EnvironmentalSensorData {
	if x != nil {
		return x.EnviroEnclosure
	}
	return nil
}

func (x *ReaperModuleSensorData) GetEnviroPc() *EnvironmentalSensorData {
	if x != nil {
		return x.EnviroPc
	}
	return nil
}

func (x *ReaperModuleSensorData) GetCoolantInlet() *CoolantSensorData {
	if x != nil {
		return x.CoolantInlet
	}
	return nil
}

func (x *ReaperModuleSensorData) GetCoolantOutlet() *CoolantSensorData {
	if x != nil {
		return x.CoolantOutlet
	}
	return nil
}

func (x *ReaperModuleSensorData) GetStrobeTemperatureC() *ValueWithRange {
	if x != nil {
		return x.StrobeTemperatureC
	}
	return nil
}

func (x *ReaperModuleSensorData) GetStrobeCapVoltage() *ValueWithRange {
	if x != nil {
		return x.StrobeCapVoltage
	}
	return nil
}

func (x *ReaperModuleSensorData) GetStrobeCurrent() *ValueWithRange {
	if x != nil {
		return x.StrobeCurrent
	}
	return nil
}

func (x *ReaperModuleSensorData) GetPc() *ReaperPcSensorData {
	if x != nil {
		return x.Pc
	}
	return nil
}

func (x *ReaperModuleSensorData) GetScannerA() *ReaperScannerSensorData {
	if x != nil {
		return x.ScannerA
	}
	return nil
}

func (x *ReaperModuleSensorData) GetScannerB() *ReaperScannerSensorData {
	if x != nil {
		return x.ScannerB
	}
	return nil
}

func (x *ReaperModuleSensorData) GetPcPowerEnabled() bool {
	if x != nil {
		return x.PcPowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetLasersPowerEnabled() bool {
	if x != nil {
		return x.LasersPowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetPredictCamPowerEnabled() bool {
	if x != nil {
		return x.PredictCamPowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetStrobePowerEnabled() bool {
	if x != nil {
		return x.StrobePowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetStrobeEnabled() bool {
	if x != nil {
		return x.StrobeEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetStatus() ModuleStatus {
	if x != nil {
		return x.Status
	}
	return ModuleStatus_OK
}

type CenterEnclosureStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CenterEnclosureStatusRequest) Reset() {
	*x = CenterEnclosureStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CenterEnclosureStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CenterEnclosureStatusRequest) ProtoMessage() {}

func (x *CenterEnclosureStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CenterEnclosureStatusRequest.ProtoReflect.Descriptor instead.
func (*CenterEnclosureStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{15}
}

type ModuleHardwareStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of a single module for which hardware status will be returned
	ModuleId int32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
}

func (x *ModuleHardwareStatusRequest) Reset() {
	*x = ModuleHardwareStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleHardwareStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleHardwareStatusRequest) ProtoMessage() {}

func (x *ModuleHardwareStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleHardwareStatusRequest.ProtoReflect.Descriptor instead.
func (*ModuleHardwareStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{16}
}

func (x *ModuleHardwareStatusRequest) GetModuleId() int32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

type GetNextReaperHardwareStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	// Types that are assignable to Request:
	//
	//	*GetNextReaperHardwareStatusRequest_CenterEnclosureStatus
	//	*GetNextReaperHardwareStatusRequest_ModuleStatus
	Request isGetNextReaperHardwareStatusRequest_Request `protobuf_oneof:"request"`
}

func (x *GetNextReaperHardwareStatusRequest) Reset() {
	*x = GetNextReaperHardwareStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextReaperHardwareStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextReaperHardwareStatusRequest) ProtoMessage() {}

func (x *GetNextReaperHardwareStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextReaperHardwareStatusRequest.ProtoReflect.Descriptor instead.
func (*GetNextReaperHardwareStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{17}
}

func (x *GetNextReaperHardwareStatusRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (m *GetNextReaperHardwareStatusRequest) GetRequest() isGetNextReaperHardwareStatusRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *GetNextReaperHardwareStatusRequest) GetCenterEnclosureStatus() *CenterEnclosureStatusRequest {
	if x, ok := x.GetRequest().(*GetNextReaperHardwareStatusRequest_CenterEnclosureStatus); ok {
		return x.CenterEnclosureStatus
	}
	return nil
}

func (x *GetNextReaperHardwareStatusRequest) GetModuleStatus() *ModuleHardwareStatusRequest {
	if x, ok := x.GetRequest().(*GetNextReaperHardwareStatusRequest_ModuleStatus); ok {
		return x.ModuleStatus
	}
	return nil
}

type isGetNextReaperHardwareStatusRequest_Request interface {
	isGetNextReaperHardwareStatusRequest_Request()
}

type GetNextReaperHardwareStatusRequest_CenterEnclosureStatus struct {
	CenterEnclosureStatus *CenterEnclosureStatusRequest `protobuf:"bytes,2,opt,name=center_enclosure_status,json=centerEnclosureStatus,proto3,oneof"`
}

type GetNextReaperHardwareStatusRequest_ModuleStatus struct {
	ModuleStatus *ModuleHardwareStatusRequest `protobuf:"bytes,3,opt,name=module_status,json=moduleStatus,proto3,oneof"`
}

func (*GetNextReaperHardwareStatusRequest_CenterEnclosureStatus) isGetNextReaperHardwareStatusRequest_Request() {
}

func (*GetNextReaperHardwareStatusRequest_ModuleStatus) isGetNextReaperHardwareStatusRequest_Request() {
}

type GetNextReaperHardwareStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	// Types that are assignable to Response:
	//
	//	*GetNextReaperHardwareStatusResponse_CenterEnclosureStatus
	//	*GetNextReaperHardwareStatusResponse_ModuleStatus
	Response isGetNextReaperHardwareStatusResponse_Response `protobuf_oneof:"response"`
}

func (x *GetNextReaperHardwareStatusResponse) Reset() {
	*x = GetNextReaperHardwareStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextReaperHardwareStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextReaperHardwareStatusResponse) ProtoMessage() {}

func (x *GetNextReaperHardwareStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextReaperHardwareStatusResponse.ProtoReflect.Descriptor instead.
func (*GetNextReaperHardwareStatusResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{18}
}

func (x *GetNextReaperHardwareStatusResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (m *GetNextReaperHardwareStatusResponse) GetResponse() isGetNextReaperHardwareStatusResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *GetNextReaperHardwareStatusResponse) GetCenterEnclosureStatus() *ReaperCenterEnclosureData {
	if x, ok := x.GetResponse().(*GetNextReaperHardwareStatusResponse_CenterEnclosureStatus); ok {
		return x.CenterEnclosureStatus
	}
	return nil
}

func (x *GetNextReaperHardwareStatusResponse) GetModuleStatus() *ReaperModuleSensorData {
	if x, ok := x.GetResponse().(*GetNextReaperHardwareStatusResponse_ModuleStatus); ok {
		return x.ModuleStatus
	}
	return nil
}

type isGetNextReaperHardwareStatusResponse_Response interface {
	isGetNextReaperHardwareStatusResponse_Response()
}

type GetNextReaperHardwareStatusResponse_CenterEnclosureStatus struct {
	CenterEnclosureStatus *ReaperCenterEnclosureData `protobuf:"bytes,2,opt,name=center_enclosure_status,json=centerEnclosureStatus,proto3,oneof"`
}

type GetNextReaperHardwareStatusResponse_ModuleStatus struct {
	ModuleStatus *ReaperModuleSensorData `protobuf:"bytes,3,opt,name=module_status,json=moduleStatus,proto3,oneof"`
}

func (*GetNextReaperHardwareStatusResponse_CenterEnclosureStatus) isGetNextReaperHardwareStatusResponse_Response() {
}

func (*GetNextReaperHardwareStatusResponse_ModuleStatus) isGetNextReaperHardwareStatusResponse_Response() {
}

type GetNextReaperAllHardwareStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextReaperAllHardwareStatusRequest) Reset() {
	*x = GetNextReaperAllHardwareStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextReaperAllHardwareStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextReaperAllHardwareStatusRequest) ProtoMessage() {}

func (x *GetNextReaperAllHardwareStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextReaperAllHardwareStatusRequest.ProtoReflect.Descriptor instead.
func (*GetNextReaperAllHardwareStatusRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{19}
}

func (x *GetNextReaperAllHardwareStatusRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextReaperAllHardwareStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	// center enclosure stuff
	CenterEnclosureStatus *ReaperCenterEnclosureData `protobuf:"bytes,2,opt,name=center_enclosure_status,json=centerEnclosureStatus,proto3" json:"center_enclosure_status,omitempty"`
	// sensors from all modules
	ModuleStatus []*ReaperModuleSensorData `protobuf:"bytes,3,rep,name=module_status,json=moduleStatus,proto3" json:"module_status,omitempty"`
}

func (x *GetNextReaperAllHardwareStatusResponse) Reset() {
	*x = GetNextReaperAllHardwareStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextReaperAllHardwareStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextReaperAllHardwareStatusResponse) ProtoMessage() {}

func (x *GetNextReaperAllHardwareStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextReaperAllHardwareStatusResponse.ProtoReflect.Descriptor instead.
func (*GetNextReaperAllHardwareStatusResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{20}
}

func (x *GetNextReaperAllHardwareStatusResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextReaperAllHardwareStatusResponse) GetCenterEnclosureStatus() *ReaperCenterEnclosureData {
	if x != nil {
		return x.CenterEnclosureStatus
	}
	return nil
}

func (x *GetNextReaperAllHardwareStatusResponse) GetModuleStatus() []*ReaperModuleSensorData {
	if x != nil {
		return x.ModuleStatus
	}
	return nil
}

type RelayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Device Device `protobuf:"varint,1,opt,name=device,proto3,enum=carbon.frontend.power.Device" json:"device,omitempty"`
}

func (x *RelayRequest) Reset() {
	*x = RelayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayRequest) ProtoMessage() {}

func (x *RelayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayRequest.ProtoReflect.Descriptor instead.
func (*RelayRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{21}
}

func (x *RelayRequest) GetDevice() Device {
	if x != nil {
		return x.Device
	}
	return Device_SENSOR_LIFTED
}

type RelayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *RelayResponse) Reset() {
	*x = RelayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayResponse) ProtoMessage() {}

func (x *RelayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayResponse.ProtoReflect.Descriptor instead.
func (*RelayResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{22}
}

func (x *RelayResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set switched scanner power
type SetReaperScannerPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId      uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	ScannerAPower *bool  `protobuf:"varint,2,opt,name=scanner_a_power,json=scannerAPower,proto3,oneof" json:"scanner_a_power,omitempty"`
	ScannerBPower *bool  `protobuf:"varint,3,opt,name=scanner_b_power,json=scannerBPower,proto3,oneof" json:"scanner_b_power,omitempty"`
}

func (x *SetReaperScannerPowerRequest) Reset() {
	*x = SetReaperScannerPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperScannerPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperScannerPowerRequest) ProtoMessage() {}

func (x *SetReaperScannerPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperScannerPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperScannerPowerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{23}
}

func (x *SetReaperScannerPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperScannerPowerRequest) GetScannerAPower() bool {
	if x != nil && x.ScannerAPower != nil {
		return *x.ScannerAPower
	}
	return false
}

func (x *SetReaperScannerPowerRequest) GetScannerBPower() bool {
	if x != nil && x.ScannerBPower != nil {
		return *x.ScannerBPower
	}
	return false
}

type SetReaperScannerPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperScannerPowerResponse) Reset() {
	*x = SetReaperScannerPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperScannerPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperScannerPowerResponse) ProtoMessage() {}

func (x *SetReaperScannerPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperScannerPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperScannerPowerResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{24}
}

func (x *SetReaperScannerPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set switched target cam power (via scanner)
type SetReaperTargetPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId     uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	TargetAPower *bool  `protobuf:"varint,2,opt,name=target_a_power,json=targetAPower,proto3,oneof" json:"target_a_power,omitempty"`
	TargetBPower *bool  `protobuf:"varint,3,opt,name=target_b_power,json=targetBPower,proto3,oneof" json:"target_b_power,omitempty"`
}

func (x *SetReaperTargetPowerRequest) Reset() {
	*x = SetReaperTargetPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperTargetPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperTargetPowerRequest) ProtoMessage() {}

func (x *SetReaperTargetPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperTargetPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperTargetPowerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{25}
}

func (x *SetReaperTargetPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperTargetPowerRequest) GetTargetAPower() bool {
	if x != nil && x.TargetAPower != nil {
		return *x.TargetAPower
	}
	return false
}

func (x *SetReaperTargetPowerRequest) GetTargetBPower() bool {
	if x != nil && x.TargetBPower != nil {
		return *x.TargetBPower
	}
	return false
}

type SetReaperTargetPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperTargetPowerResponse) Reset() {
	*x = SetReaperTargetPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperTargetPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperTargetPowerResponse) ProtoMessage() {}

func (x *SetReaperTargetPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperTargetPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperTargetPowerResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{26}
}

func (x *SetReaperTargetPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set the switchde power to a module's predict cam
type SetReaperPredictCamPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperPredictCamPowerRequest) Reset() {
	*x = SetReaperPredictCamPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperPredictCamPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperPredictCamPowerRequest) ProtoMessage() {}

func (x *SetReaperPredictCamPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperPredictCamPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperPredictCamPowerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{27}
}

func (x *SetReaperPredictCamPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperPredictCamPowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperPredictCamPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperPredictCamPowerResponse) Reset() {
	*x = SetReaperPredictCamPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperPredictCamPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperPredictCamPowerResponse) ProtoMessage() {}

func (x *SetReaperPredictCamPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperPredictCamPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperPredictCamPowerResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{28}
}

func (x *SetReaperPredictCamPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set whether strobe firing is enabled
type SetReaperStrobeEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperStrobeEnableRequest) Reset() {
	*x = SetReaperStrobeEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperStrobeEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperStrobeEnableRequest) ProtoMessage() {}

func (x *SetReaperStrobeEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperStrobeEnableRequest.ProtoReflect.Descriptor instead.
func (*SetReaperStrobeEnableRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{29}
}

func (x *SetReaperStrobeEnableRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperStrobeEnableRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperStrobeEnableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperStrobeEnableResponse) Reset() {
	*x = SetReaperStrobeEnableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperStrobeEnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperStrobeEnableResponse) ProtoMessage() {}

func (x *SetReaperStrobeEnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperStrobeEnableResponse.ProtoReflect.Descriptor instead.
func (*SetReaperStrobeEnableResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{30}
}

func (x *SetReaperStrobeEnableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Control strobes for _all_ modules on the machine
type SetReaperAllStrobesEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperAllStrobesEnableRequest) Reset() {
	*x = SetReaperAllStrobesEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperAllStrobesEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperAllStrobesEnableRequest) ProtoMessage() {}

func (x *SetReaperAllStrobesEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperAllStrobesEnableRequest.ProtoReflect.Descriptor instead.
func (*SetReaperAllStrobesEnableRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{31}
}

func (x *SetReaperAllStrobesEnableRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperAllStrobesEnableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperAllStrobesEnableResponse) Reset() {
	*x = SetReaperAllStrobesEnableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperAllStrobesEnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperAllStrobesEnableResponse) ProtoMessage() {}

func (x *SetReaperAllStrobesEnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperAllStrobesEnableResponse.ProtoReflect.Descriptor instead.
func (*SetReaperAllStrobesEnableResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{32}
}

func (x *SetReaperAllStrobesEnableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set module PC 240V input power
type SetReaperModulePcPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperModulePcPowerRequest) Reset() {
	*x = SetReaperModulePcPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModulePcPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModulePcPowerRequest) ProtoMessage() {}

func (x *SetReaperModulePcPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModulePcPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperModulePcPowerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{33}
}

func (x *SetReaperModulePcPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperModulePcPowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperModulePcPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperModulePcPowerResponse) Reset() {
	*x = SetReaperModulePcPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModulePcPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModulePcPowerResponse) ProtoMessage() {}

func (x *SetReaperModulePcPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModulePcPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperModulePcPowerResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{34}
}

func (x *SetReaperModulePcPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set module lasers 240V input power; all lasers are controlled together
type SetReaperModuleLaserPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperModuleLaserPowerRequest) Reset() {
	*x = SetReaperModuleLaserPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModuleLaserPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModuleLaserPowerRequest) ProtoMessage() {}

func (x *SetReaperModuleLaserPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModuleLaserPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperModuleLaserPowerRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{35}
}

func (x *SetReaperModuleLaserPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperModuleLaserPowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperModuleLaserPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperModuleLaserPowerResponse) Reset() {
	*x = SetReaperModuleLaserPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModuleLaserPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModuleLaserPowerResponse) ProtoMessage() {}

func (x *SetReaperModuleLaserPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModuleLaserPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperModuleLaserPowerResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{36}
}

func (x *SetReaperModuleLaserPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type DeviceStatus_RelayStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disabled bool `protobuf:"varint,3,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (x *DeviceStatus_RelayStatus) Reset() {
	*x = DeviceStatus_RelayStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceStatus_RelayStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceStatus_RelayStatus) ProtoMessage() {}

func (x *DeviceStatus_RelayStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceStatus_RelayStatus.ProtoReflect.Descriptor instead.
func (*DeviceStatus_RelayStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{0, 0}
}

func (x *DeviceStatus_RelayStatus) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

type DeviceStatus_SensorStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string           `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Color  DeviceValueColor `protobuf:"varint,5,opt,name=color,proto3,enum=carbon.frontend.power.DeviceValueColor" json:"color,omitempty"`
}

func (x *DeviceStatus_SensorStatus) Reset() {
	*x = DeviceStatus_SensorStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_power_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceStatus_SensorStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceStatus_SensorStatus) ProtoMessage() {}

func (x *DeviceStatus_SensorStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_power_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceStatus_SensorStatus.ProtoReflect.Descriptor instead.
func (*DeviceStatus_SensorStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_power_proto_rawDescGZIP(), []int{0, 1}
}

func (x *DeviceStatus_SensorStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeviceStatus_SensorStatus) GetColor() DeviceValueColor {
	if x != nil {
		return x.Color
	}
	return DeviceValueColor_COLOR_GRAY
}

var File_frontend_proto_power_proto protoreflect.FileDescriptor

var file_frontend_proto_power_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c,
	0x03, 0x0a, 0x0c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x35, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x50, 0x0a, 0x0a,
	0x72, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53,
	0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x1a, 0x29, 0x0a, 0x0b, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x1a, 0x65,
	0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x45, 0x0a,
	0x12, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x13, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x07,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22, 0x3b, 0x0a, 0x0e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x69, 0x73, 0x5f, 0x6f, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x4f, 0x6b, 0x22, 0xf7, 0x01, 0x0a, 0x17, 0x45, 0x6e,
	0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x43, 0x12, 0x46, 0x0a, 0x0b, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x68,
	0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x52, 0x68, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x68, 0x70, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65,
	0x48, 0x70, 0x61, 0x22, 0xa9, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74, 0x53,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x0d, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x43, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72,
	0x65, 0x5f, 0x6b, 0x70, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x4b, 0x70, 0x61, 0x22,
	0xd9, 0x01, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x12, 0x53, 0x0a,
	0x11, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x70, 0x65,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x12, 0x57, 0x0a, 0x13, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c,
	0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x11, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x8b, 0x0b, 0x0a, 0x12,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x5a, 0x0a, 0x16, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x63, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x43, 0x70, 0x75, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x12, 0x57,
	0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x12, 0x59, 0x0a, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x31, 0x5f, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x10, 0x74,
	0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x70, 0x75, 0x31, 0x43, 0x88,
	0x01, 0x01, 0x12, 0x59, 0x0a, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x32, 0x5f, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x01, 0x52, 0x10, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x70, 0x75, 0x32, 0x43, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a,
	0x07, 0x70, 0x73, 0x75, 0x5f, 0x31, 0x32, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x70, 0x73, 0x75, 0x31, 0x32, 0x76, 0x12, 0x3c, 0x0a,
	0x06, 0x70, 0x73, 0x75, 0x5f, 0x35, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x05, 0x70, 0x73, 0x75, 0x35, 0x76, 0x12, 0x3e, 0x0a, 0x07, 0x70,
	0x73, 0x75, 0x5f, 0x33, 0x76, 0x33, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x06, 0x70, 0x73, 0x75, 0x33, 0x76, 0x33, 0x12, 0x39, 0x0a, 0x04, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x04, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x51,
	0x0a, 0x11, 0x72, 0x61, 0x6d, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x0f, 0x72, 0x61, 0x6d, 0x55, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x12, 0x53, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x10, 0x64, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x61, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f,
	0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x41, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x4d, 0x0a, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x62, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x52, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x6d, 0x5f, 0x61, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50,
	0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x61, 0x6d, 0x41, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x52, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x62, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x42, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x51, 0x0a, 0x10,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x4c, 0x69, 0x6e, 0x6b, 0x12,
	0x44, 0x0a, 0x09, 0x69, 0x70, 0x6d, 0x69, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x69, 0x70, 0x6d,
	0x69, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x42, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x07, 0x65, 0x78, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x31, 0x5f,
	0x63, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x32, 0x5f, 0x63, 0x22, 0xd9, 0x02, 0x0a, 0x18, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x4a, 0x0a,
	0x0d, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x43, 0x12, 0x41, 0x0a, 0x08, 0x68, 0x75, 0x6d,
	0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x4f, 0x0a, 0x10,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0e, 0x6c,
	0x61, 0x73, 0x65, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x12, 0x16, 0x0a,
	0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xdd, 0x02, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x73,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x53, 0x6e, 0x12, 0x57, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x43, 0x12, 0x4b,
	0x0a, 0x0e, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x76,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x6d,
	0x6f, 0x74, 0x6f, 0x72, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x56, 0x12, 0x4d, 0x0a, 0x0f, 0x6d,
	0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0d, 0x6d, 0x6f, 0x74,
	0x6f, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb5, 0x08, 0x0a, 0x17, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x6e,
	0x12, 0x42, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x72, 0x69,
	0x70, 0x70, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x75, 0x73, 0x65,
	0x54, 0x72, 0x69, 0x70, 0x70, 0x65, 0x64, 0x12, 0x5f, 0x0a, 0x18, 0x74, 0x65, 0x6d, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x16, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x6c,
	0x6c, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x12, 0x55, 0x0a, 0x13, 0x74, 0x65, 0x6d, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x69, 0x62, 0x65, 0x72, 0x5f, 0x63, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x11, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x69, 0x62, 0x65, 0x72, 0x43, 0x12,
	0x49, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x77,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x6c,
	0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x57, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x12, 0x57, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x10,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x73, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x53, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x14, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x02,
	0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x43, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x09, 0x6d, 0x6f, 0x74, 0x6f, 0x72,
	0x5f, 0x70, 0x61, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x74,
	0x6f, 0x72, 0x50, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x0a, 0x6d, 0x6f, 0x74, 0x6f,
	0x72, 0x5f, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x04, 0x52, 0x09, 0x6d,
	0x6f, 0x74, 0x6f, 0x72, 0x54, 0x69, 0x6c, 0x74, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x15, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x37, 0x0a, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x6e, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x61, 0x6e, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x69, 0x6c, 0x74, 0x22, 0x62, 0x0a,
	0x0d, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x47, 0x70, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17,
	0x0a, 0x07, 0x68, 0x61, 0x73, 0x5f, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x68, 0x61, 0x73, 0x46, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x22, 0x58, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x57, 0x68, 0x65, 0x65, 0x6c,
	0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x4c, 0x65, 0x66, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x52, 0x69, 0x67, 0x68, 0x74, 0x22, 0xce, 0x12, 0x0a, 0x19,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x61, 0x74,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x77, 0x61, 0x74, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d,
	0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x46, 0x62, 0x12, 0x47, 0x0a, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x41, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x34, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x68, 0x75, 0x6d, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x48, 0x0a,
	0x0c, 0x61, 0x63, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x4a, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x5f, 0x62, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67,
	0x65, 0x41, 0x42, 0x12, 0x4a, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x62, 0x5f, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x42, 0x43, 0x12,
	0x4a, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x5f,
	0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b,
	0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x43, 0x12, 0x47, 0x0a, 0x0c, 0x61,
	0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57,
	0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x41, 0x12, 0x47, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61,
	0x67, 0x65, 0x5f, 0x62, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x42, 0x12, 0x47, 0x0a,
	0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x43, 0x12, 0x25, 0x0a, 0x0f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x77, 0x5f, 0x33, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x70, 0x68, 0x61, 0x73, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x57, 0x33, 0x12, 0x27, 0x0a,
	0x10, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x5f,
	0x33, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x68, 0x61, 0x73, 0x65, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x56, 0x61, 0x33, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x55, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x62, 0x69, 0x6e,
	0x65, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69,
	0x6e, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x5d, 0x0a, 0x17, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x5f, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x5f, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x15, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x48, 0x75,
	0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x13, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x31, 0x32, 0x76, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x11, 0x62, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x31, 0x32, 0x76, 0x12, 0x34, 0x0a,
	0x16, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x77,
	0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x67, 0x70, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x67, 0x70, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x61,
	0x69, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61,
	0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x61, 0x6c, 0x61, 0x72,
	0x6d, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x63, 0x68, 0x69, 0x6c, 0x6c,
	0x65, 0x72, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x63, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x54,
	0x65, 0x6d, 0x70, 0x43, 0x12, 0x52, 0x0a, 0x12, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0f, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x46, 0x6c, 0x6f, 0x77, 0x4c, 0x4d, 0x69, 0x6e, 0x12, 0x57, 0x0a, 0x14, 0x63, 0x68, 0x69, 0x6c,
	0x6c, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x73, 0x69,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x12, 0x63,
	0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x50, 0x73,
	0x69, 0x12, 0x62, 0x0a, 0x1a, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x75, 0x73, 0x5f, 0x63, 0x6d, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x17, 0x63, 0x68,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x75, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x55, 0x73, 0x43, 0x6d, 0x12, 0x52, 0x0a, 0x12, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x63, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57,
	0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0f, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x53, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x12, 0x62, 0x0a, 0x1a, 0x63, 0x68, 0x69,
	0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x5f, 0x6b, 0x62, 0x74, 0x75, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x17, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x48, 0x65, 0x61,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x4b, 0x62, 0x74, 0x75, 0x12, 0x61, 0x0a,
	0x1a, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64,
	0x65, 0x6c, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x63, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57,
	0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x16, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x65, 0x6d, 0x70, 0x43,
	0x12, 0x36, 0x0a, 0x03, 0x67, 0x70, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x47, 0x70, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x03, 0x67, 0x70, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x77, 0x68, 0x65, 0x65,
	0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x57, 0x68,
	0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c,
	0x77, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x22, 0xca, 0x09, 0x0a,
	0x16, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53,
	0x6e, 0x12, 0x59, 0x0a, 0x10, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x5f, 0x65, 0x6e, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x65, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x4b, 0x0a, 0x09,
	0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x5f, 0x70, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x50, 0x63, 0x12, 0x4d, 0x0a, 0x0d, 0x63, 0x6f, 0x6f,
	0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x6f, 0x6f, 0x6c,
	0x61, 0x6e, 0x74, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x12, 0x4f, 0x0a, 0x0e, 0x63, 0x6f, 0x6f, 0x6c,
	0x61, 0x6e, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x6c, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x6f, 0x6f, 0x6c,
	0x61, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x6c, 0x65, 0x74, 0x12, 0x57, 0x0a, 0x14, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x12,
	0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x43, 0x12, 0x53, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x61, 0x70,
	0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x10, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x61, 0x70,
	0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x12, 0x4c, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x02, 0x70, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x50, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x02,
	0x70, 0x63, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x01, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x41, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x5f, 0x62, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x02, 0x52, 0x08, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x63, 0x5f,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x70, 0x63, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x22, 0x1e, 0x0a, 0x1c, 0x43, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x3a, 0x0a, 0x1b, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xaa, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x6d, 0x0a,
	0x17, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x15, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0d,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0xa4, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x6a, 0x0a, 0x17, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x15, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x58, 0x0a, 0x25, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x48, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x02, 0x74, 0x73, 0x22, 0x97, 0x02, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12,
	0x68, 0x0a, 0x17, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x15, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x45, 0x0a,
	0x0c, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x22, 0x29, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22,
	0xbd, 0x01, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x0f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x41, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x22,
	0x39, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x1b, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x88,
	0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x5f, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x42, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x5f, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x22, 0x38, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x58, 0x0a,
	0x1f, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3c, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x55, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x39, 0x0a, 0x1d,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x3c, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3d, 0x0a, 0x21, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x22, 0x56, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3a, 0x0a, 0x1e,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50,
	0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x59, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x22, 0x3d, 0x0a, 0x21, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x2a, 0xd1, 0x07, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x11, 0x0a,
	0x0d, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x46, 0x54, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x45, 0x52, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x01, 0x12,
	0x1a, 0x0a, 0x16, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52,
	0x5f, 0x48, 0x55, 0x4d, 0x49, 0x44, 0x49, 0x54, 0x59, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x10, 0x03, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x31, 0x32, 0x56, 0x5f, 0x42, 0x41, 0x54, 0x54,
	0x45, 0x52, 0x59, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x05, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f,
	0x52, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x41, 0x43,
	0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x41, 0x42, 0x5f, 0x56, 0x4f, 0x4c, 0x54, 0x41, 0x47,
	0x45, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x42, 0x43,
	0x5f, 0x56, 0x4f, 0x4c, 0x54, 0x41, 0x47, 0x45, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45,
	0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x5f, 0x56, 0x4f, 0x4c, 0x54, 0x41, 0x47, 0x45, 0x10,
	0x0a, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x41, 0x5f, 0x43, 0x55,
	0x52, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x45, 0x4e, 0x53, 0x4f,
	0x52, 0x5f, 0x42, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x14, 0x0a,
	0x10, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x43, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e,
	0x54, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x55, 0x49,
	0x43, 0x49, 0x44, 0x45, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f,
	0x52, 0x45, 0x42, 0x4f, 0x4f, 0x54, 0x10, 0x0f, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x4c, 0x41,
	0x59, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x10, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x4c, 0x41,
	0x59, 0x5f, 0x52, 0x4f, 0x57, 0x5f, 0x31, 0x10, 0x11, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x4c,
	0x41, 0x59, 0x5f, 0x52, 0x4f, 0x57, 0x5f, 0x32, 0x10, 0x12, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45,
	0x4c, 0x41, 0x59, 0x5f, 0x52, 0x4f, 0x57, 0x5f, 0x33, 0x10, 0x13, 0x12, 0x12, 0x0a, 0x0e, 0x52,
	0x45, 0x4c, 0x41, 0x59, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x31, 0x10, 0x14, 0x12,
	0x12, 0x0a, 0x0e, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f,
	0x32, 0x10, 0x15, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x4c, 0x49, 0x47,
	0x48, 0x54, 0x53, 0x5f, 0x33, 0x10, 0x16, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x4c, 0x41, 0x59,
	0x5f, 0x53, 0x43, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x31, 0x10, 0x17, 0x12, 0x13, 0x0a, 0x0f,
	0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x32, 0x10,
	0x18, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x4e,
	0x45, 0x52, 0x5f, 0x33, 0x10, 0x19, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f,
	0x41, 0x43, 0x10, 0x1a, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x48,
	0x49, 0x4c, 0x4c, 0x45, 0x52, 0x10, 0x1b, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x4c, 0x41, 0x59,
	0x5f, 0x53, 0x54, 0x52, 0x4f, 0x42, 0x45, 0x10, 0x1c, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x4c,
	0x41, 0x59, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x4e, 0x54,
	0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x1d, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x4c, 0x41, 0x59,
	0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x4e, 0x54, 0x5f, 0x52,
	0x49, 0x47, 0x48, 0x54, 0x10, 0x1e, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f,
	0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4c, 0x45, 0x46,
	0x54, 0x10, 0x1f, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x45, 0x4e, 0x43,
	0x4f, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10,
	0x20, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f,
	0x44, 0x45, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x4e, 0x54, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x21,
	0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44,
	0x45, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x4e, 0x54, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x22,
	0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44,
	0x45, 0x52, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x23, 0x12, 0x1d,
	0x0a, 0x19, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x52,
	0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x24, 0x12, 0x0d, 0x0a,
	0x09, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x47, 0x50, 0x53, 0x10, 0x25, 0x12, 0x13, 0x0a, 0x0f,
	0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x4c, 0x41, 0x54, 0x49, 0x54, 0x55, 0x44, 0x45, 0x10,
	0x26, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x4e, 0x47,
	0x49, 0x54, 0x55, 0x44, 0x45, 0x10, 0x27, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x4e, 0x53, 0x4f,
	0x52, 0x5f, 0x4b, 0x45, 0x59, 0x10, 0x28, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x45, 0x4e, 0x53, 0x4f,
	0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x29, 0x12, 0x17, 0x0a,
	0x13, 0x52, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x52, 0x5f, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x10, 0x2a, 0x2a, 0x54, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f,
	0x4c, 0x4f, 0x52, 0x5f, 0x47, 0x52, 0x41, 0x59, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4f,
	0x4c, 0x4f, 0x52, 0x5f, 0x47, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x43,
	0x4f, 0x4c, 0x4f, 0x52, 0x5f, 0x4f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xbf, 0x01, 0x0a,
	0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x30, 0x4d, 0x5f, 0x48, 0x41, 0x4c, 0x46,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x30, 0x4d, 0x5f,
	0x46, 0x55, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f,
	0x31, 0x30, 0x30, 0x4d, 0x5f, 0x48, 0x41, 0x4c, 0x46, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x30, 0x30, 0x4d, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x04,
	0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x47, 0x5f, 0x46, 0x55, 0x4c,
	0x4c, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x32, 0x47, 0x35,
	0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50, 0x45, 0x45, 0x44,
	0x5f, 0x35, 0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50,
	0x45, 0x45, 0x44, 0x5f, 0x31, 0x30, 0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x08, 0x2a, 0x55,
	0x0a, 0x0d, 0x41, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x4f, 0x4f, 0x44,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x44, 0x10,
	0x02, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x59, 0x5f,
	0x42, 0x41, 0x44, 0x10, 0x03, 0x2a, 0x21, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x01, 0x32, 0xbb, 0x0c, 0x0a, 0x0c, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x66,
	0x66, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x59, 0x0a, 0x0c, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x41,
	0x6c, 0x6c, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x48,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x48, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x33,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x14,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x8d, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x36, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x84, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x41,
	0x6c, 0x6c, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x63, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_power_proto_rawDescOnce sync.Once
	file_frontend_proto_power_proto_rawDescData = file_frontend_proto_power_proto_rawDesc
)

func file_frontend_proto_power_proto_rawDescGZIP() []byte {
	file_frontend_proto_power_proto_rawDescOnce.Do(func() {
		file_frontend_proto_power_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_power_proto_rawDescData)
	})
	return file_frontend_proto_power_proto_rawDescData
}

var file_frontend_proto_power_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_frontend_proto_power_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_frontend_proto_power_proto_goTypes = []interface{}{
	(Device)(0),                                    // 0: carbon.frontend.power.Device
	(DeviceValueColor)(0),                          // 1: carbon.frontend.power.DeviceValueColor
	(NetworkLinkSpeed)(0),                          // 2: carbon.frontend.power.NetworkLinkSpeed
	(AcPowerStatus)(0),                             // 3: carbon.frontend.power.AcPowerStatus
	(ModuleStatus)(0),                              // 4: carbon.frontend.power.ModuleStatus
	(*DeviceStatus)(nil),                           // 5: carbon.frontend.power.DeviceStatus
	(*PowerStatusRequest)(nil),                     // 6: carbon.frontend.power.PowerStatusRequest
	(*PowerStatusResponse)(nil),                    // 7: carbon.frontend.power.PowerStatusResponse
	(*ValueWithRange)(nil),                         // 8: carbon.frontend.power.ValueWithRange
	(*EnvironmentalSensorData)(nil),                // 9: carbon.frontend.power.EnvironmentalSensorData
	(*CoolantSensorData)(nil),                      // 10: carbon.frontend.power.CoolantSensorData
	(*NetworkPortState)(nil),                       // 11: carbon.frontend.power.NetworkPortState
	(*ReaperPcSensorData)(nil),                     // 12: carbon.frontend.power.ReaperPcSensorData
	(*ReaperScannerLaserStatus)(nil),               // 13: carbon.frontend.power.ReaperScannerLaserStatus
	(*ReaperScannerMotorData)(nil),                 // 14: carbon.frontend.power.ReaperScannerMotorData
	(*ReaperScannerSensorData)(nil),                // 15: carbon.frontend.power.ReaperScannerSensorData
	(*ReaperGpsData)(nil),                          // 16: carbon.frontend.power.ReaperGpsData
	(*ReaperWheelEncoderData)(nil),                 // 17: carbon.frontend.power.ReaperWheelEncoderData
	(*ReaperCenterEnclosureData)(nil),              // 18: carbon.frontend.power.ReaperCenterEnclosureData
	(*ReaperModuleSensorData)(nil),                 // 19: carbon.frontend.power.ReaperModuleSensorData
	(*CenterEnclosureStatusRequest)(nil),           // 20: carbon.frontend.power.CenterEnclosureStatusRequest
	(*ModuleHardwareStatusRequest)(nil),            // 21: carbon.frontend.power.ModuleHardwareStatusRequest
	(*GetNextReaperHardwareStatusRequest)(nil),     // 22: carbon.frontend.power.GetNextReaperHardwareStatusRequest
	(*GetNextReaperHardwareStatusResponse)(nil),    // 23: carbon.frontend.power.GetNextReaperHardwareStatusResponse
	(*GetNextReaperAllHardwareStatusRequest)(nil),  // 24: carbon.frontend.power.GetNextReaperAllHardwareStatusRequest
	(*GetNextReaperAllHardwareStatusResponse)(nil), // 25: carbon.frontend.power.GetNextReaperAllHardwareStatusResponse
	(*RelayRequest)(nil),                           // 26: carbon.frontend.power.RelayRequest
	(*RelayResponse)(nil),                          // 27: carbon.frontend.power.RelayResponse
	(*SetReaperScannerPowerRequest)(nil),           // 28: carbon.frontend.power.SetReaperScannerPowerRequest
	(*SetReaperScannerPowerResponse)(nil),          // 29: carbon.frontend.power.SetReaperScannerPowerResponse
	(*SetReaperTargetPowerRequest)(nil),            // 30: carbon.frontend.power.SetReaperTargetPowerRequest
	(*SetReaperTargetPowerResponse)(nil),           // 31: carbon.frontend.power.SetReaperTargetPowerResponse
	(*SetReaperPredictCamPowerRequest)(nil),        // 32: carbon.frontend.power.SetReaperPredictCamPowerRequest
	(*SetReaperPredictCamPowerResponse)(nil),       // 33: carbon.frontend.power.SetReaperPredictCamPowerResponse
	(*SetReaperStrobeEnableRequest)(nil),           // 34: carbon.frontend.power.SetReaperStrobeEnableRequest
	(*SetReaperStrobeEnableResponse)(nil),          // 35: carbon.frontend.power.SetReaperStrobeEnableResponse
	(*SetReaperAllStrobesEnableRequest)(nil),       // 36: carbon.frontend.power.SetReaperAllStrobesEnableRequest
	(*SetReaperAllStrobesEnableResponse)(nil),      // 37: carbon.frontend.power.SetReaperAllStrobesEnableResponse
	(*SetReaperModulePcPowerRequest)(nil),          // 38: carbon.frontend.power.SetReaperModulePcPowerRequest
	(*SetReaperModulePcPowerResponse)(nil),         // 39: carbon.frontend.power.SetReaperModulePcPowerResponse
	(*SetReaperModuleLaserPowerRequest)(nil),       // 40: carbon.frontend.power.SetReaperModuleLaserPowerRequest
	(*SetReaperModuleLaserPowerResponse)(nil),      // 41: carbon.frontend.power.SetReaperModuleLaserPowerResponse
	(*DeviceStatus_RelayStatus)(nil),               // 42: carbon.frontend.power.DeviceStatus.RelayStatus
	(*DeviceStatus_SensorStatus)(nil),              // 43: carbon.frontend.power.DeviceStatus.SensorStatus
	(*Timestamp)(nil),                              // 44: carbon.frontend.util.Timestamp
}
var file_frontend_proto_power_proto_depIdxs = []int32{
	0,   // 0: carbon.frontend.power.DeviceStatus.device:type_name -> carbon.frontend.power.Device
	42,  // 1: carbon.frontend.power.DeviceStatus.relay_type:type_name -> carbon.frontend.power.DeviceStatus.RelayStatus
	43,  // 2: carbon.frontend.power.DeviceStatus.sensor_type:type_name -> carbon.frontend.power.DeviceStatus.SensorStatus
	44,  // 3: carbon.frontend.power.PowerStatusRequest.ts:type_name -> carbon.frontend.util.Timestamp
	5,   // 4: carbon.frontend.power.PowerStatusResponse.devices:type_name -> carbon.frontend.power.DeviceStatus
	44,  // 5: carbon.frontend.power.PowerStatusResponse.ts:type_name -> carbon.frontend.util.Timestamp
	8,   // 6: carbon.frontend.power.EnvironmentalSensorData.temperature_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 7: carbon.frontend.power.EnvironmentalSensorData.humidity_rh:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 8: carbon.frontend.power.EnvironmentalSensorData.pressure_hpa:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 9: carbon.frontend.power.CoolantSensorData.temperature_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 10: carbon.frontend.power.CoolantSensorData.pressure_kpa:type_name -> carbon.frontend.power.ValueWithRange
	2,   // 11: carbon.frontend.power.NetworkPortState.actual_link_speed:type_name -> carbon.frontend.power.NetworkLinkSpeed
	2,   // 12: carbon.frontend.power.NetworkPortState.expected_link_speed:type_name -> carbon.frontend.power.NetworkLinkSpeed
	8,   // 13: carbon.frontend.power.ReaperPcSensorData.temperature_cpu_core_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 14: carbon.frontend.power.ReaperPcSensorData.temperature_system_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 15: carbon.frontend.power.ReaperPcSensorData.temperature_gpu_1_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 16: carbon.frontend.power.ReaperPcSensorData.temperature_gpu_2_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 17: carbon.frontend.power.ReaperPcSensorData.psu_12v:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 18: carbon.frontend.power.ReaperPcSensorData.psu_5v:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 19: carbon.frontend.power.ReaperPcSensorData.psu_3v3:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 20: carbon.frontend.power.ReaperPcSensorData.load:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 21: carbon.frontend.power.ReaperPcSensorData.ram_usage_percent:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 22: carbon.frontend.power.ReaperPcSensorData.disk_usage_percent:type_name -> carbon.frontend.power.ValueWithRange
	11,  // 23: carbon.frontend.power.ReaperPcSensorData.scanner_a_link:type_name -> carbon.frontend.power.NetworkPortState
	11,  // 24: carbon.frontend.power.ReaperPcSensorData.scanner_b_link:type_name -> carbon.frontend.power.NetworkPortState
	11,  // 25: carbon.frontend.power.ReaperPcSensorData.target_cam_a_link:type_name -> carbon.frontend.power.NetworkPortState
	11,  // 26: carbon.frontend.power.ReaperPcSensorData.target_cam_b_link:type_name -> carbon.frontend.power.NetworkPortState
	11,  // 27: carbon.frontend.power.ReaperPcSensorData.predict_cam_link:type_name -> carbon.frontend.power.NetworkPortState
	11,  // 28: carbon.frontend.power.ReaperPcSensorData.ipmi_link:type_name -> carbon.frontend.power.NetworkPortState
	11,  // 29: carbon.frontend.power.ReaperPcSensorData.ext_link:type_name -> carbon.frontend.power.NetworkPortState
	8,   // 30: carbon.frontend.power.ReaperScannerLaserStatus.temperature_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 31: carbon.frontend.power.ReaperScannerLaserStatus.humidity:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 32: carbon.frontend.power.ReaperScannerLaserStatus.laser_current_ma:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 33: carbon.frontend.power.ReaperScannerMotorData.temperature_output_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 34: carbon.frontend.power.ReaperScannerMotorData.motor_supply_v:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 35: carbon.frontend.power.ReaperScannerMotorData.motor_current_a:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 36: carbon.frontend.power.ReaperScannerSensorData.current_a:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 37: carbon.frontend.power.ReaperScannerSensorData.temperature_collimator_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 38: carbon.frontend.power.ReaperScannerSensorData.temperature_fiber_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 39: carbon.frontend.power.ReaperScannerSensorData.laser_power_w:type_name -> carbon.frontend.power.ValueWithRange
	13,  // 40: carbon.frontend.power.ReaperScannerSensorData.laser_status:type_name -> carbon.frontend.power.ReaperScannerLaserStatus
	8,   // 41: carbon.frontend.power.ReaperScannerSensorData.temperature_target_c:type_name -> carbon.frontend.power.ValueWithRange
	14,  // 42: carbon.frontend.power.ReaperScannerSensorData.motor_pan:type_name -> carbon.frontend.power.ReaperScannerMotorData
	14,  // 43: carbon.frontend.power.ReaperScannerSensorData.motor_tilt:type_name -> carbon.frontend.power.ReaperScannerMotorData
	3,   // 44: carbon.frontend.power.ReaperCenterEnclosureData.power_status:type_name -> carbon.frontend.power.AcPowerStatus
	8,   // 45: carbon.frontend.power.ReaperCenterEnclosureData.ac_frequency:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 46: carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_a_b:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 47: carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_b_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 48: carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_a_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 49: carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_a:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 50: carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_b:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 51: carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 52: carbon.frontend.power.ReaperCenterEnclosureData.power_factor:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 53: carbon.frontend.power.ReaperCenterEnclosureData.server_cabinet_temp:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 54: carbon.frontend.power.ReaperCenterEnclosureData.server_cabinet_humidity:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 55: carbon.frontend.power.ReaperCenterEnclosureData.battery_voltage_12v:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 56: carbon.frontend.power.ReaperCenterEnclosureData.chiller_temp_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 57: carbon.frontend.power.ReaperCenterEnclosureData.chiller_flow_l_min:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 58: carbon.frontend.power.ReaperCenterEnclosureData.chiller_pressure_psi:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 59: carbon.frontend.power.ReaperCenterEnclosureData.chiller_conductivity_us_cm:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 60: carbon.frontend.power.ReaperCenterEnclosureData.chiller_set_temp_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 61: carbon.frontend.power.ReaperCenterEnclosureData.chiller_heat_transfer_kbtu:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 62: carbon.frontend.power.ReaperCenterEnclosureData.chiller_fluid_delta_temp_c:type_name -> carbon.frontend.power.ValueWithRange
	16,  // 63: carbon.frontend.power.ReaperCenterEnclosureData.gps:type_name -> carbon.frontend.power.ReaperGpsData
	17,  // 64: carbon.frontend.power.ReaperCenterEnclosureData.wheel_encoder:type_name -> carbon.frontend.power.ReaperWheelEncoderData
	9,   // 65: carbon.frontend.power.ReaperModuleSensorData.enviro_enclosure:type_name -> carbon.frontend.power.EnvironmentalSensorData
	9,   // 66: carbon.frontend.power.ReaperModuleSensorData.enviro_pc:type_name -> carbon.frontend.power.EnvironmentalSensorData
	10,  // 67: carbon.frontend.power.ReaperModuleSensorData.coolant_inlet:type_name -> carbon.frontend.power.CoolantSensorData
	10,  // 68: carbon.frontend.power.ReaperModuleSensorData.coolant_outlet:type_name -> carbon.frontend.power.CoolantSensorData
	8,   // 69: carbon.frontend.power.ReaperModuleSensorData.strobe_temperature_c:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 70: carbon.frontend.power.ReaperModuleSensorData.strobe_cap_voltage:type_name -> carbon.frontend.power.ValueWithRange
	8,   // 71: carbon.frontend.power.ReaperModuleSensorData.strobe_current:type_name -> carbon.frontend.power.ValueWithRange
	12,  // 72: carbon.frontend.power.ReaperModuleSensorData.pc:type_name -> carbon.frontend.power.ReaperPcSensorData
	15,  // 73: carbon.frontend.power.ReaperModuleSensorData.scanner_a:type_name -> carbon.frontend.power.ReaperScannerSensorData
	15,  // 74: carbon.frontend.power.ReaperModuleSensorData.scanner_b:type_name -> carbon.frontend.power.ReaperScannerSensorData
	4,   // 75: carbon.frontend.power.ReaperModuleSensorData.status:type_name -> carbon.frontend.power.ModuleStatus
	44,  // 76: carbon.frontend.power.GetNextReaperHardwareStatusRequest.ts:type_name -> carbon.frontend.util.Timestamp
	20,  // 77: carbon.frontend.power.GetNextReaperHardwareStatusRequest.center_enclosure_status:type_name -> carbon.frontend.power.CenterEnclosureStatusRequest
	21,  // 78: carbon.frontend.power.GetNextReaperHardwareStatusRequest.module_status:type_name -> carbon.frontend.power.ModuleHardwareStatusRequest
	44,  // 79: carbon.frontend.power.GetNextReaperHardwareStatusResponse.ts:type_name -> carbon.frontend.util.Timestamp
	18,  // 80: carbon.frontend.power.GetNextReaperHardwareStatusResponse.center_enclosure_status:type_name -> carbon.frontend.power.ReaperCenterEnclosureData
	19,  // 81: carbon.frontend.power.GetNextReaperHardwareStatusResponse.module_status:type_name -> carbon.frontend.power.ReaperModuleSensorData
	44,  // 82: carbon.frontend.power.GetNextReaperAllHardwareStatusRequest.ts:type_name -> carbon.frontend.util.Timestamp
	44,  // 83: carbon.frontend.power.GetNextReaperAllHardwareStatusResponse.ts:type_name -> carbon.frontend.util.Timestamp
	18,  // 84: carbon.frontend.power.GetNextReaperAllHardwareStatusResponse.center_enclosure_status:type_name -> carbon.frontend.power.ReaperCenterEnclosureData
	19,  // 85: carbon.frontend.power.GetNextReaperAllHardwareStatusResponse.module_status:type_name -> carbon.frontend.power.ReaperModuleSensorData
	0,   // 86: carbon.frontend.power.RelayRequest.device:type_name -> carbon.frontend.power.Device
	1,   // 87: carbon.frontend.power.DeviceStatus.SensorStatus.color:type_name -> carbon.frontend.power.DeviceValueColor
	6,   // 88: carbon.frontend.power.PowerService.GetNextPowerStatus:input_type -> carbon.frontend.power.PowerStatusRequest
	26,  // 89: carbon.frontend.power.PowerService.TurnOffDevice:input_type -> carbon.frontend.power.RelayRequest
	26,  // 90: carbon.frontend.power.PowerService.TurnOnDevice:input_type -> carbon.frontend.power.RelayRequest
	24,  // 91: carbon.frontend.power.PowerService.GetNextReaperAllHardwareStatus:input_type -> carbon.frontend.power.GetNextReaperAllHardwareStatusRequest
	22,  // 92: carbon.frontend.power.PowerService.GetNextReaperHardwareStatus:input_type -> carbon.frontend.power.GetNextReaperHardwareStatusRequest
	28,  // 93: carbon.frontend.power.PowerService.SetReaperScannerPower:input_type -> carbon.frontend.power.SetReaperScannerPowerRequest
	30,  // 94: carbon.frontend.power.PowerService.SetReaperTargetPower:input_type -> carbon.frontend.power.SetReaperTargetPowerRequest
	32,  // 95: carbon.frontend.power.PowerService.SetReaperPredictCamPower:input_type -> carbon.frontend.power.SetReaperPredictCamPowerRequest
	34,  // 96: carbon.frontend.power.PowerService.SetReaperStrobeEnable:input_type -> carbon.frontend.power.SetReaperStrobeEnableRequest
	36,  // 97: carbon.frontend.power.PowerService.SetReaperAllStrobesEnable:input_type -> carbon.frontend.power.SetReaperAllStrobesEnableRequest
	38,  // 98: carbon.frontend.power.PowerService.SetReaperModulePcPower:input_type -> carbon.frontend.power.SetReaperModulePcPowerRequest
	40,  // 99: carbon.frontend.power.PowerService.SetReaperModuleLaserPower:input_type -> carbon.frontend.power.SetReaperModuleLaserPowerRequest
	7,   // 100: carbon.frontend.power.PowerService.GetNextPowerStatus:output_type -> carbon.frontend.power.PowerStatusResponse
	27,  // 101: carbon.frontend.power.PowerService.TurnOffDevice:output_type -> carbon.frontend.power.RelayResponse
	27,  // 102: carbon.frontend.power.PowerService.TurnOnDevice:output_type -> carbon.frontend.power.RelayResponse
	25,  // 103: carbon.frontend.power.PowerService.GetNextReaperAllHardwareStatus:output_type -> carbon.frontend.power.GetNextReaperAllHardwareStatusResponse
	23,  // 104: carbon.frontend.power.PowerService.GetNextReaperHardwareStatus:output_type -> carbon.frontend.power.GetNextReaperHardwareStatusResponse
	29,  // 105: carbon.frontend.power.PowerService.SetReaperScannerPower:output_type -> carbon.frontend.power.SetReaperScannerPowerResponse
	31,  // 106: carbon.frontend.power.PowerService.SetReaperTargetPower:output_type -> carbon.frontend.power.SetReaperTargetPowerResponse
	33,  // 107: carbon.frontend.power.PowerService.SetReaperPredictCamPower:output_type -> carbon.frontend.power.SetReaperPredictCamPowerResponse
	35,  // 108: carbon.frontend.power.PowerService.SetReaperStrobeEnable:output_type -> carbon.frontend.power.SetReaperStrobeEnableResponse
	37,  // 109: carbon.frontend.power.PowerService.SetReaperAllStrobesEnable:output_type -> carbon.frontend.power.SetReaperAllStrobesEnableResponse
	39,  // 110: carbon.frontend.power.PowerService.SetReaperModulePcPower:output_type -> carbon.frontend.power.SetReaperModulePcPowerResponse
	41,  // 111: carbon.frontend.power.PowerService.SetReaperModuleLaserPower:output_type -> carbon.frontend.power.SetReaperModuleLaserPowerResponse
	100, // [100:112] is the sub-list for method output_type
	88,  // [88:100] is the sub-list for method input_type
	88,  // [88:88] is the sub-list for extension type_name
	88,  // [88:88] is the sub-list for extension extendee
	0,   // [0:88] is the sub-list for field type_name
}

func init() { file_frontend_proto_power_proto_init() }
func file_frontend_proto_power_proto_init() {
	if File_frontend_proto_power_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_power_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueWithRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnvironmentalSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoolantSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkPortState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperPcSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperScannerLaserStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperScannerMotorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperScannerSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperGpsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperWheelEncoderData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperCenterEnclosureData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperModuleSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CenterEnclosureStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleHardwareStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextReaperHardwareStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextReaperHardwareStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextReaperAllHardwareStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextReaperAllHardwareStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperScannerPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperScannerPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperTargetPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperTargetPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperPredictCamPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperPredictCamPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperStrobeEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperStrobeEnableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperAllStrobesEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperAllStrobesEnableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModulePcPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModulePcPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModuleLaserPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModuleLaserPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceStatus_RelayStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_power_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceStatus_SensorStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_power_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*DeviceStatus_RelayType)(nil),
		(*DeviceStatus_SensorType)(nil),
	}
	file_frontend_proto_power_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_frontend_proto_power_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_frontend_proto_power_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_frontend_proto_power_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*GetNextReaperHardwareStatusRequest_CenterEnclosureStatus)(nil),
		(*GetNextReaperHardwareStatusRequest_ModuleStatus)(nil),
	}
	file_frontend_proto_power_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*GetNextReaperHardwareStatusResponse_CenterEnclosureStatus)(nil),
		(*GetNextReaperHardwareStatusResponse_ModuleStatus)(nil),
	}
	file_frontend_proto_power_proto_msgTypes[23].OneofWrappers = []interface{}{}
	file_frontend_proto_power_proto_msgTypes[25].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_power_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_power_proto_goTypes,
		DependencyIndexes: file_frontend_proto_power_proto_depIdxs,
		EnumInfos:         file_frontend_proto_power_proto_enumTypes,
		MessageInfos:      file_frontend_proto_power_proto_msgTypes,
	}.Build()
	File_frontend_proto_power_proto = out.File
	file_frontend_proto_power_proto_rawDesc = nil
	file_frontend_proto_power_proto_goTypes = nil
	file_frontend_proto_power_proto_depIdxs = nil
}

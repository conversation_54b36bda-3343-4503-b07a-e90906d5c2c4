// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/robot.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SoftwareProcessStage int32

const (
	SoftwareProcessStage_SPS_UNKNOWN SoftwareProcessStage = 0
	SoftwareProcessStage_SPS_ERROR   SoftwareProcessStage = 1
	SoftwareProcessStage_SPS_BOOTING SoftwareProcessStage = 2
	SoftwareProcessStage_SPS_READY   SoftwareProcessStage = 3
)

// Enum value maps for SoftwareProcessStage.
var (
	SoftwareProcessStage_name = map[int32]string{
		0: "SPS_UNKNOWN",
		1: "SPS_ERROR",
		2: "SPS_BOOTING",
		3: "SPS_READY",
	}
	SoftwareProcessStage_value = map[string]int32{
		"SPS_UNKNOWN": 0,
		"SPS_ERROR":   1,
		"SPS_BOOTING": 2,
		"SPS_READY":   3,
	}
)

func (x SoftwareProcessStage) Enum() *SoftwareProcessStage {
	p := new(SoftwareProcessStage)
	*p = x
	return p
}

func (x SoftwareProcessStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SoftwareProcessStage) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_robot_proto_enumTypes[0].Descriptor()
}

func (SoftwareProcessStage) Type() protoreflect.EnumType {
	return &file_frontend_proto_robot_proto_enumTypes[0]
}

func (x SoftwareProcessStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SoftwareProcessStage.Descriptor instead.
func (SoftwareProcessStage) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{0}
}

type BootStage int32

const (
	BootStage_BS_UPDATE_INSTALLING BootStage = 0
	BootStage_BS_POWERED_DOWN      BootStage = 1
	BootStage_BS_POWERING_UP       BootStage = 2
	BootStage_BS_SOFTWARE_LOADING  BootStage = 3
	BootStage_BS_BOOT_FAILURE      BootStage = 4
	BootStage_BS_MODEL_LOADING     BootStage = 5
	BootStage_BS_CRITICAL_FAILURE  BootStage = 6
	BootStage_BS_LIFTED            BootStage = 7
	BootStage_BS_ESTOPPED          BootStage = 8
	BootStage_BS_WEEDING           BootStage = 9
	BootStage_BS_STANDBY           BootStage = 10
)

// Enum value maps for BootStage.
var (
	BootStage_name = map[int32]string{
		0:  "BS_UPDATE_INSTALLING",
		1:  "BS_POWERED_DOWN",
		2:  "BS_POWERING_UP",
		3:  "BS_SOFTWARE_LOADING",
		4:  "BS_BOOT_FAILURE",
		5:  "BS_MODEL_LOADING",
		6:  "BS_CRITICAL_FAILURE",
		7:  "BS_LIFTED",
		8:  "BS_ESTOPPED",
		9:  "BS_WEEDING",
		10: "BS_STANDBY",
	}
	BootStage_value = map[string]int32{
		"BS_UPDATE_INSTALLING": 0,
		"BS_POWERED_DOWN":      1,
		"BS_POWERING_UP":       2,
		"BS_SOFTWARE_LOADING":  3,
		"BS_BOOT_FAILURE":      4,
		"BS_MODEL_LOADING":     5,
		"BS_CRITICAL_FAILURE":  6,
		"BS_LIFTED":            7,
		"BS_ESTOPPED":          8,
		"BS_WEEDING":           9,
		"BS_STANDBY":           10,
	}
)

func (x BootStage) Enum() *BootStage {
	p := new(BootStage)
	*p = x
	return p
}

func (x BootStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BootStage) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_robot_proto_enumTypes[1].Descriptor()
}

func (BootStage) Type() protoreflect.EnumType {
	return &file_frontend_proto_robot_proto_enumTypes[1]
}

func (x BootStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BootStage.Descriptor instead.
func (BootStage) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{1}
}

type BoardState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BoardRev        string `protobuf:"bytes,1,opt,name=board_rev,json=boardRev,proto3" json:"board_rev,omitempty"`
	Connected       bool   `protobuf:"varint,3,opt,name=connected,proto3" json:"connected,omitempty"`
	Booted          bool   `protobuf:"varint,4,opt,name=booted,proto3" json:"booted,omitempty"`
	Error           bool   `protobuf:"varint,5,opt,name=error,proto3" json:"error,omitempty"`
	Timestamp       uint64 `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FirmwareVersion string `protobuf:"bytes,7,opt,name=firmware_version,json=firmwareVersion,proto3" json:"firmware_version,omitempty"`
}

func (x *BoardState) Reset() {
	*x = BoardState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardState) ProtoMessage() {}

func (x *BoardState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardState.ProtoReflect.Descriptor instead.
func (*BoardState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{0}
}

func (x *BoardState) GetBoardRev() string {
	if x != nil {
		return x.BoardRev
	}
	return ""
}

func (x *BoardState) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *BoardState) GetBooted() bool {
	if x != nil {
		return x.Booted
	}
	return false
}

func (x *BoardState) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *BoardState) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *BoardState) GetFirmwareVersion() string {
	if x != nil {
		return x.FirmwareVersion
	}
	return ""
}

type GPSCoord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Latitude   float64 `protobuf:"fixed64,2,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude  float64 `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
	AltitudeMm float64 `protobuf:"fixed64,4,opt,name=altitude_mm,json=altitudeMm,proto3" json:"altitude_mm,omitempty"`
	EcefX      float64 `protobuf:"fixed64,5,opt,name=ecef_x,json=ecefX,proto3" json:"ecef_x,omitempty"`
	EcefY      float64 `protobuf:"fixed64,6,opt,name=ecef_y,json=ecefY,proto3" json:"ecef_y,omitempty"`
	EcefZ      float64 `protobuf:"fixed64,7,opt,name=ecef_z,json=ecefZ,proto3" json:"ecef_z,omitempty"`
}

func (x *GPSCoord) Reset() {
	*x = GPSCoord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPSCoord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPSCoord) ProtoMessage() {}

func (x *GPSCoord) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPSCoord.ProtoReflect.Descriptor instead.
func (*GPSCoord) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{1}
}

func (x *GPSCoord) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GPSCoord) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GPSCoord) GetAltitudeMm() float64 {
	if x != nil {
		return x.AltitudeMm
	}
	return 0
}

func (x *GPSCoord) GetEcefX() float64 {
	if x != nil {
		return x.EcefX
	}
	return 0
}

func (x *GPSCoord) GetEcefY() float64 {
	if x != nil {
		return x.EcefY
	}
	return 0
}

func (x *GPSCoord) GetEcefZ() float64 {
	if x != nil {
		return x.EcefZ
	}
	return 0
}

type GPSBoardState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Board         *BoardState `protobuf:"bytes,1,opt,name=board,proto3" json:"board,omitempty"`
	HaveFix       bool        `protobuf:"varint,2,opt,name=have_fix,json=haveFix,proto3" json:"have_fix,omitempty"`
	HaveApproxFix bool        `protobuf:"varint,3,opt,name=have_approx_fix,json=haveApproxFix,proto3" json:"have_approx_fix,omitempty"`
	NumSats       int32       `protobuf:"varint,4,opt,name=num_sats,json=numSats,proto3" json:"num_sats,omitempty"`
	Hdop          float32     `protobuf:"fixed32,5,opt,name=hdop,proto3" json:"hdop,omitempty"`
	Position      *GPSCoord   `protobuf:"bytes,6,opt,name=position,proto3" json:"position,omitempty"`
}

func (x *GPSBoardState) Reset() {
	*x = GPSBoardState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPSBoardState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPSBoardState) ProtoMessage() {}

func (x *GPSBoardState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPSBoardState.ProtoReflect.Descriptor instead.
func (*GPSBoardState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{2}
}

func (x *GPSBoardState) GetBoard() *BoardState {
	if x != nil {
		return x.Board
	}
	return nil
}

func (x *GPSBoardState) GetHaveFix() bool {
	if x != nil {
		return x.HaveFix
	}
	return false
}

func (x *GPSBoardState) GetHaveApproxFix() bool {
	if x != nil {
		return x.HaveApproxFix
	}
	return false
}

func (x *GPSBoardState) GetNumSats() int32 {
	if x != nil {
		return x.NumSats
	}
	return 0
}

func (x *GPSBoardState) GetHdop() float32 {
	if x != nil {
		return x.Hdop
	}
	return 0
}

func (x *GPSBoardState) GetPosition() *GPSCoord {
	if x != nil {
		return x.Position
	}
	return nil
}

type WheelState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastTickPosition int32   `protobuf:"varint,1,opt,name=last_tick_position,json=lastTickPosition,proto3" json:"last_tick_position,omitempty"`
	VelMph           float32 `protobuf:"fixed32,2,opt,name=vel_mph,json=velMph,proto3" json:"vel_mph,omitempty"`
	Filtered         bool    `protobuf:"varint,3,opt,name=filtered,proto3" json:"filtered,omitempty"`
	Enabled          bool    `protobuf:"varint,4,opt,name=enabled,proto3" json:"enabled,omitempty"`
	DiameterIn       float32 `protobuf:"fixed32,5,opt,name=diameter_in,json=diameterIn,proto3" json:"diameter_in,omitempty"`
}

func (x *WheelState) Reset() {
	*x = WheelState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WheelState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WheelState) ProtoMessage() {}

func (x *WheelState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WheelState.ProtoReflect.Descriptor instead.
func (*WheelState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{3}
}

func (x *WheelState) GetLastTickPosition() int32 {
	if x != nil {
		return x.LastTickPosition
	}
	return 0
}

func (x *WheelState) GetVelMph() float32 {
	if x != nil {
		return x.VelMph
	}
	return 0
}

func (x *WheelState) GetFiltered() bool {
	if x != nil {
		return x.Filtered
	}
	return false
}

func (x *WheelState) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *WheelState) GetDiameterIn() float32 {
	if x != nil {
		return x.DiameterIn
	}
	return 0
}

type WheelEncoderBoardState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Board  *BoardState   `protobuf:"bytes,1,opt,name=board,proto3" json:"board,omitempty"`
	Wheels []*WheelState `protobuf:"bytes,2,rep,name=wheels,proto3" json:"wheels,omitempty"`
}

func (x *WheelEncoderBoardState) Reset() {
	*x = WheelEncoderBoardState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WheelEncoderBoardState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WheelEncoderBoardState) ProtoMessage() {}

func (x *WheelEncoderBoardState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WheelEncoderBoardState.ProtoReflect.Descriptor instead.
func (*WheelEncoderBoardState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{4}
}

func (x *WheelEncoderBoardState) GetBoard() *BoardState {
	if x != nil {
		return x.Board
	}
	return nil
}

func (x *WheelEncoderBoardState) GetWheels() []*WheelState {
	if x != nil {
		return x.Wheels
	}
	return nil
}

type StrobeBoardState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Board                  *BoardState `protobuf:"bytes,1,opt,name=board,proto3" json:"board,omitempty"`
	ExposureUs             int32       `protobuf:"varint,2,opt,name=exposure_us,json=exposureUs,proto3" json:"exposure_us,omitempty"`
	PeriodUs               int32       `protobuf:"varint,3,opt,name=period_us,json=periodUs,proto3" json:"period_us,omitempty"`
	TargetsPerPredictRatio int32       `protobuf:"varint,4,opt,name=targets_per_predict_ratio,json=targetsPerPredictRatio,proto3" json:"targets_per_predict_ratio,omitempty"`
}

func (x *StrobeBoardState) Reset() {
	*x = StrobeBoardState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrobeBoardState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrobeBoardState) ProtoMessage() {}

func (x *StrobeBoardState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrobeBoardState.ProtoReflect.Descriptor instead.
func (*StrobeBoardState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{5}
}

func (x *StrobeBoardState) GetBoard() *BoardState {
	if x != nil {
		return x.Board
	}
	return nil
}

func (x *StrobeBoardState) GetExposureUs() int32 {
	if x != nil {
		return x.ExposureUs
	}
	return 0
}

func (x *StrobeBoardState) GetPeriodUs() int32 {
	if x != nil {
		return x.PeriodUs
	}
	return 0
}

func (x *StrobeBoardState) GetTargetsPerPredictRatio() int32 {
	if x != nil {
		return x.TargetsPerPredictRatio
	}
	return 0
}

type ISOBusState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ISOBusState) Reset() {
	*x = ISOBusState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ISOBusState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ISOBusState) ProtoMessage() {}

func (x *ISOBusState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ISOBusState.ProtoReflect.Descriptor instead.
func (*ISOBusState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{6}
}

type ChillerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChillerState) Reset() {
	*x = ChillerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChillerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChillerState) ProtoMessage() {}

func (x *ChillerState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChillerState.ProtoReflect.Descriptor instead.
func (*ChillerState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{7}
}

type AirConditionerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AirConditionerState) Reset() {
	*x = AirConditionerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AirConditionerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AirConditionerState) ProtoMessage() {}

func (x *AirConditionerState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AirConditionerState.ProtoReflect.Descriptor instead.
func (*AirConditionerState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{8}
}

type USBDriveState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Connected      bool    `protobuf:"varint,1,opt,name=connected,proto3" json:"connected,omitempty"`
	UtilizationPct float32 `protobuf:"fixed32,2,opt,name=utilization_pct,json=utilizationPct,proto3" json:"utilization_pct,omitempty"`
}

func (x *USBDriveState) Reset() {
	*x = USBDriveState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *USBDriveState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*USBDriveState) ProtoMessage() {}

func (x *USBDriveState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use USBDriveState.ProtoReflect.Descriptor instead.
func (*USBDriveState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{9}
}

func (x *USBDriveState) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *USBDriveState) GetUtilizationPct() float32 {
	if x != nil {
		return x.UtilizationPct
	}
	return 0
}

type SupervisoryPLCState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AcFrequency            float32 `protobuf:"fixed32,1,opt,name=ac_frequency,json=acFrequency,proto3" json:"ac_frequency,omitempty"`
	AcVoltageA             float32 `protobuf:"fixed32,2,opt,name=ac_voltage_a,json=acVoltageA,proto3" json:"ac_voltage_a,omitempty"`
	AcVoltageAB            float32 `protobuf:"fixed32,3,opt,name=ac_voltage_a_b,json=acVoltageAB,proto3" json:"ac_voltage_a_b,omitempty"`
	AcVoltageAC            float32 `protobuf:"fixed32,4,opt,name=ac_voltage_a_c,json=acVoltageAC,proto3" json:"ac_voltage_a_c,omitempty"`
	AcVoltageB             float32 `protobuf:"fixed32,5,opt,name=ac_voltage_b,json=acVoltageB,proto3" json:"ac_voltage_b,omitempty"`
	AcVoltageBC            float32 `protobuf:"fixed32,6,opt,name=ac_voltage_b_c,json=acVoltageBC,proto3" json:"ac_voltage_b_c,omitempty"`
	AcVoltageC             float32 `protobuf:"fixed32,7,opt,name=ac_voltage_c,json=acVoltageC,proto3" json:"ac_voltage_c,omitempty"`
	PhasePowerVa_3         float32 `protobuf:"fixed32,8,opt,name=phase_power_va_3,json=phasePowerVa3,proto3" json:"phase_power_va_3,omitempty"`
	PhasePowerW_3          float32 `protobuf:"fixed32,9,opt,name=phase_power_w_3,json=phasePowerW3,proto3" json:"phase_power_w_3,omitempty"`
	PowerFactor            float32 `protobuf:"fixed32,10,opt,name=power_factor,json=powerFactor,proto3" json:"power_factor,omitempty"`
	PowerBad               bool    `protobuf:"varint,11,opt,name=power_bad,json=powerBad,proto3" json:"power_bad,omitempty"`
	PowerGood              bool    `protobuf:"varint,12,opt,name=power_good,json=powerGood,proto3" json:"power_good,omitempty"`
	PowerVeryBad           bool    `protobuf:"varint,13,opt,name=power_very_bad,json=powerVeryBad,proto3" json:"power_very_bad,omitempty"`
	BatteryVoltage_12V     float32 `protobuf:"fixed32,14,opt,name=battery_voltage_12v,json=batteryVoltage12v,proto3" json:"battery_voltage_12v,omitempty"`
	AirConditionerDisabled bool    `protobuf:"varint,15,opt,name=air_conditioner_disabled,json=airConditionerDisabled,proto3" json:"air_conditioner_disabled,omitempty"`
	ChillerDisabled        bool    `protobuf:"varint,16,opt,name=chiller_disabled,json=chillerDisabled,proto3" json:"chiller_disabled,omitempty"`
	GpsDisabled            bool    `protobuf:"varint,17,opt,name=gps_disabled,json=gpsDisabled,proto3" json:"gps_disabled,omitempty"`
	StrobeDisabled         bool    `protobuf:"varint,18,opt,name=strobe_disabled,json=strobeDisabled,proto3" json:"strobe_disabled,omitempty"`
	WheelEncoderDisabled   bool    `protobuf:"varint,19,opt,name=wheel_encoder_disabled,json=wheelEncoderDisabled,proto3" json:"wheel_encoder_disabled,omitempty"`
	BtlDisabled            []bool  `protobuf:"varint,20,rep,packed,name=btl_disabled,json=btlDisabled,proto3" json:"btl_disabled,omitempty"`
	ServerDisabled         []bool  `protobuf:"varint,21,rep,packed,name=server_disabled,json=serverDisabled,proto3" json:"server_disabled,omitempty"`
	ScannersDisabled       []bool  `protobuf:"varint,22,rep,packed,name=scanners_disabled,json=scannersDisabled,proto3" json:"scanners_disabled,omitempty"`
	MainContactorDisabled  bool    `protobuf:"varint,23,opt,name=main_contactor_disabled,json=mainContactorDisabled,proto3" json:"main_contactor_disabled,omitempty"`
	MainContactorStatusFb  bool    `protobuf:"varint,24,opt,name=main_contactor_status_fb,json=mainContactorStatusFb,proto3" json:"main_contactor_status_fb,omitempty"`
	ServerCabinetHumidity  float32 `protobuf:"fixed32,25,opt,name=server_cabinet_humidity,json=serverCabinetHumidity,proto3" json:"server_cabinet_humidity,omitempty"`
	ServerCabinetTemp      float32 `protobuf:"fixed32,26,opt,name=server_cabinet_temp,json=serverCabinetTemp,proto3" json:"server_cabinet_temp,omitempty"`
	HumidityBypassed       bool    `protobuf:"varint,27,opt,name=humidity_bypassed,json=humidityBypassed,proto3" json:"humidity_bypassed,omitempty"`
	TempBypassed           bool    `protobuf:"varint,28,opt,name=temp_bypassed,json=tempBypassed,proto3" json:"temp_bypassed,omitempty"`
	HumidityStatus         bool    `protobuf:"varint,29,opt,name=humidity_status,json=humidityStatus,proto3" json:"humidity_status,omitempty"`
	TempStatus             bool    `protobuf:"varint,30,opt,name=temp_status,json=tempStatus,proto3" json:"temp_status,omitempty"`
	TempHumidityStatus     bool    `protobuf:"varint,31,opt,name=temp_humidity_status,json=tempHumidityStatus,proto3" json:"temp_humidity_status,omitempty"`
	LiftedStatus           bool    `protobuf:"varint,32,opt,name=lifted_status,json=liftedStatus,proto3" json:"lifted_status,omitempty"`
	TractorPower           bool    `protobuf:"varint,33,opt,name=tractor_power,json=tractorPower,proto3" json:"tractor_power,omitempty"`
	WaterProtectStatus     bool    `protobuf:"varint,34,opt,name=water_protect_status,json=waterProtectStatus,proto3" json:"water_protect_status,omitempty"`
}

func (x *SupervisoryPLCState) Reset() {
	*x = SupervisoryPLCState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupervisoryPLCState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupervisoryPLCState) ProtoMessage() {}

func (x *SupervisoryPLCState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupervisoryPLCState.ProtoReflect.Descriptor instead.
func (*SupervisoryPLCState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{10}
}

func (x *SupervisoryPLCState) GetAcFrequency() float32 {
	if x != nil {
		return x.AcFrequency
	}
	return 0
}

func (x *SupervisoryPLCState) GetAcVoltageA() float32 {
	if x != nil {
		return x.AcVoltageA
	}
	return 0
}

func (x *SupervisoryPLCState) GetAcVoltageAB() float32 {
	if x != nil {
		return x.AcVoltageAB
	}
	return 0
}

func (x *SupervisoryPLCState) GetAcVoltageAC() float32 {
	if x != nil {
		return x.AcVoltageAC
	}
	return 0
}

func (x *SupervisoryPLCState) GetAcVoltageB() float32 {
	if x != nil {
		return x.AcVoltageB
	}
	return 0
}

func (x *SupervisoryPLCState) GetAcVoltageBC() float32 {
	if x != nil {
		return x.AcVoltageBC
	}
	return 0
}

func (x *SupervisoryPLCState) GetAcVoltageC() float32 {
	if x != nil {
		return x.AcVoltageC
	}
	return 0
}

func (x *SupervisoryPLCState) GetPhasePowerVa_3() float32 {
	if x != nil {
		return x.PhasePowerVa_3
	}
	return 0
}

func (x *SupervisoryPLCState) GetPhasePowerW_3() float32 {
	if x != nil {
		return x.PhasePowerW_3
	}
	return 0
}

func (x *SupervisoryPLCState) GetPowerFactor() float32 {
	if x != nil {
		return x.PowerFactor
	}
	return 0
}

func (x *SupervisoryPLCState) GetPowerBad() bool {
	if x != nil {
		return x.PowerBad
	}
	return false
}

func (x *SupervisoryPLCState) GetPowerGood() bool {
	if x != nil {
		return x.PowerGood
	}
	return false
}

func (x *SupervisoryPLCState) GetPowerVeryBad() bool {
	if x != nil {
		return x.PowerVeryBad
	}
	return false
}

func (x *SupervisoryPLCState) GetBatteryVoltage_12V() float32 {
	if x != nil {
		return x.BatteryVoltage_12V
	}
	return 0
}

func (x *SupervisoryPLCState) GetAirConditionerDisabled() bool {
	if x != nil {
		return x.AirConditionerDisabled
	}
	return false
}

func (x *SupervisoryPLCState) GetChillerDisabled() bool {
	if x != nil {
		return x.ChillerDisabled
	}
	return false
}

func (x *SupervisoryPLCState) GetGpsDisabled() bool {
	if x != nil {
		return x.GpsDisabled
	}
	return false
}

func (x *SupervisoryPLCState) GetStrobeDisabled() bool {
	if x != nil {
		return x.StrobeDisabled
	}
	return false
}

func (x *SupervisoryPLCState) GetWheelEncoderDisabled() bool {
	if x != nil {
		return x.WheelEncoderDisabled
	}
	return false
}

func (x *SupervisoryPLCState) GetBtlDisabled() []bool {
	if x != nil {
		return x.BtlDisabled
	}
	return nil
}

func (x *SupervisoryPLCState) GetServerDisabled() []bool {
	if x != nil {
		return x.ServerDisabled
	}
	return nil
}

func (x *SupervisoryPLCState) GetScannersDisabled() []bool {
	if x != nil {
		return x.ScannersDisabled
	}
	return nil
}

func (x *SupervisoryPLCState) GetMainContactorDisabled() bool {
	if x != nil {
		return x.MainContactorDisabled
	}
	return false
}

func (x *SupervisoryPLCState) GetMainContactorStatusFb() bool {
	if x != nil {
		return x.MainContactorStatusFb
	}
	return false
}

func (x *SupervisoryPLCState) GetServerCabinetHumidity() float32 {
	if x != nil {
		return x.ServerCabinetHumidity
	}
	return 0
}

func (x *SupervisoryPLCState) GetServerCabinetTemp() float32 {
	if x != nil {
		return x.ServerCabinetTemp
	}
	return 0
}

func (x *SupervisoryPLCState) GetHumidityBypassed() bool {
	if x != nil {
		return x.HumidityBypassed
	}
	return false
}

func (x *SupervisoryPLCState) GetTempBypassed() bool {
	if x != nil {
		return x.TempBypassed
	}
	return false
}

func (x *SupervisoryPLCState) GetHumidityStatus() bool {
	if x != nil {
		return x.HumidityStatus
	}
	return false
}

func (x *SupervisoryPLCState) GetTempStatus() bool {
	if x != nil {
		return x.TempStatus
	}
	return false
}

func (x *SupervisoryPLCState) GetTempHumidityStatus() bool {
	if x != nil {
		return x.TempHumidityStatus
	}
	return false
}

func (x *SupervisoryPLCState) GetLiftedStatus() bool {
	if x != nil {
		return x.LiftedStatus
	}
	return false
}

func (x *SupervisoryPLCState) GetTractorPower() bool {
	if x != nil {
		return x.TractorPower
	}
	return false
}

func (x *SupervisoryPLCState) GetWaterProtectStatus() bool {
	if x != nil {
		return x.WaterProtectStatus
	}
	return false
}

type SafetyPLCState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Estopped           bool `protobuf:"varint,1,opt,name=estopped,proto3" json:"estopped,omitempty"`
	InCabEstopped      bool `protobuf:"varint,2,opt,name=in_cab_estopped,json=inCabEstopped,proto3" json:"in_cab_estopped,omitempty"`
	Interlock          bool `protobuf:"varint,3,opt,name=interlock,proto3" json:"interlock,omitempty"`
	LaserKey           bool `protobuf:"varint,4,opt,name=laser_key,json=laserKey,proto3" json:"laser_key,omitempty"`
	LeftEstopped       bool `protobuf:"varint,5,opt,name=left_estopped,json=leftEstopped,proto3" json:"left_estopped,omitempty"`
	RightEstopped      bool `protobuf:"varint,6,opt,name=right_estopped,json=rightEstopped,proto3" json:"right_estopped,omitempty"`
	Lifted             bool `protobuf:"varint,7,opt,name=lifted,proto3" json:"lifted,omitempty"`
	WaterProtect       bool `protobuf:"varint,8,opt,name=water_protect,json=waterProtect,proto3" json:"water_protect,omitempty"`
	LiftSensorBypassed bool `protobuf:"varint,9,opt,name=lift_sensor_bypassed,json=liftSensorBypassed,proto3" json:"lift_sensor_bypassed,omitempty"`
}

func (x *SafetyPLCState) Reset() {
	*x = SafetyPLCState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafetyPLCState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafetyPLCState) ProtoMessage() {}

func (x *SafetyPLCState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafetyPLCState.ProtoReflect.Descriptor instead.
func (*SafetyPLCState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{11}
}

func (x *SafetyPLCState) GetEstopped() bool {
	if x != nil {
		return x.Estopped
	}
	return false
}

func (x *SafetyPLCState) GetInCabEstopped() bool {
	if x != nil {
		return x.InCabEstopped
	}
	return false
}

func (x *SafetyPLCState) GetInterlock() bool {
	if x != nil {
		return x.Interlock
	}
	return false
}

func (x *SafetyPLCState) GetLaserKey() bool {
	if x != nil {
		return x.LaserKey
	}
	return false
}

func (x *SafetyPLCState) GetLeftEstopped() bool {
	if x != nil {
		return x.LeftEstopped
	}
	return false
}

func (x *SafetyPLCState) GetRightEstopped() bool {
	if x != nil {
		return x.RightEstopped
	}
	return false
}

func (x *SafetyPLCState) GetLifted() bool {
	if x != nil {
		return x.Lifted
	}
	return false
}

func (x *SafetyPLCState) GetWaterProtect() bool {
	if x != nil {
		return x.WaterProtect
	}
	return false
}

func (x *SafetyPLCState) GetLiftSensorBypassed() bool {
	if x != nil {
		return x.LiftSensorBypassed
	}
	return false
}

type NicState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PtpOnline   bool  `protobuf:"varint,1,opt,name=ptp_online,json=ptpOnline,proto3" json:"ptp_online,omitempty"`
	PtpOffset   int64 `protobuf:"varint,2,opt,name=ptp_offset,json=ptpOffset,proto3" json:"ptp_offset,omitempty"`
	LinkOnline  bool  `protobuf:"varint,3,opt,name=link_online,json=linkOnline,proto3" json:"link_online,omitempty"`
	LinkCorrect bool  `protobuf:"varint,4,opt,name=link_correct,json=linkCorrect,proto3" json:"link_correct,omitempty"`
	LinkSpeed   int32 `protobuf:"varint,5,opt,name=link_speed,json=linkSpeed,proto3" json:"link_speed,omitempty"`
}

func (x *NicState) Reset() {
	*x = NicState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NicState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NicState) ProtoMessage() {}

func (x *NicState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NicState.ProtoReflect.Descriptor instead.
func (*NicState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{12}
}

func (x *NicState) GetPtpOnline() bool {
	if x != nil {
		return x.PtpOnline
	}
	return false
}

func (x *NicState) GetPtpOffset() int64 {
	if x != nil {
		return x.PtpOffset
	}
	return 0
}

func (x *NicState) GetLinkOnline() bool {
	if x != nil {
		return x.LinkOnline
	}
	return false
}

func (x *NicState) GetLinkCorrect() bool {
	if x != nil {
		return x.LinkCorrect
	}
	return false
}

func (x *NicState) GetLinkSpeed() int32 {
	if x != nil {
		return x.LinkSpeed
	}
	return 0
}

type StorageDriveState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capacity uint64 `protobuf:"varint,1,opt,name=capacity,proto3" json:"capacity,omitempty"`
	Used     uint64 `protobuf:"varint,2,opt,name=used,proto3" json:"used,omitempty"`
}

func (x *StorageDriveState) Reset() {
	*x = StorageDriveState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StorageDriveState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageDriveState) ProtoMessage() {}

func (x *StorageDriveState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageDriveState.ProtoReflect.Descriptor instead.
func (*StorageDriveState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{13}
}

func (x *StorageDriveState) GetCapacity() uint64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *StorageDriveState) GetUsed() uint64 {
	if x != nil {
		return x.Used
	}
	return 0
}

type GPUState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Online         bool    `protobuf:"varint,1,opt,name=online,proto3" json:"online,omitempty"`
	Index          uint32  `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Model          string  `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	Temperature_C  float32 `protobuf:"fixed32,4,opt,name=temperature_C,json=temperatureC,proto3" json:"temperature_C,omitempty"`
	PowerUsed      float32 `protobuf:"fixed32,5,opt,name=power_used,json=powerUsed,proto3" json:"power_used,omitempty"`
	PowerCapacity  float32 `protobuf:"fixed32,6,opt,name=power_capacity,json=powerCapacity,proto3" json:"power_capacity,omitempty"`
	MemoryUsed     float32 `protobuf:"fixed32,7,opt,name=memory_used,json=memoryUsed,proto3" json:"memory_used,omitempty"`
	MemoryCapacity float32 `protobuf:"fixed32,8,opt,name=memory_capacity,json=memoryCapacity,proto3" json:"memory_capacity,omitempty"`
	Fan            float32 `protobuf:"fixed32,9,opt,name=fan,proto3" json:"fan,omitempty"`
	GpuUtil        float32 `protobuf:"fixed32,10,opt,name=gpu_util,json=gpuUtil,proto3" json:"gpu_util,omitempty"`
}

func (x *GPUState) Reset() {
	*x = GPUState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GPUState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUState) ProtoMessage() {}

func (x *GPUState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUState.ProtoReflect.Descriptor instead.
func (*GPUState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{14}
}

func (x *GPUState) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *GPUState) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *GPUState) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *GPUState) GetTemperature_C() float32 {
	if x != nil {
		return x.Temperature_C
	}
	return 0
}

func (x *GPUState) GetPowerUsed() float32 {
	if x != nil {
		return x.PowerUsed
	}
	return 0
}

func (x *GPUState) GetPowerCapacity() float32 {
	if x != nil {
		return x.PowerCapacity
	}
	return 0
}

func (x *GPUState) GetMemoryUsed() float32 {
	if x != nil {
		return x.MemoryUsed
	}
	return 0
}

func (x *GPUState) GetMemoryCapacity() float32 {
	if x != nil {
		return x.MemoryCapacity
	}
	return 0
}

func (x *GPUState) GetFan() float32 {
	if x != nil {
		return x.Fan
	}
	return 0
}

func (x *GPUState) GetGpuUtil() float32 {
	if x != nil {
		return x.GpuUtil
	}
	return 0
}

type HostState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Online                bool                 `protobuf:"varint,1,opt,name=online,proto3" json:"online,omitempty"`
	PtpCheckerOnline      bool                 `protobuf:"varint,2,opt,name=ptp_checker_online,json=ptpCheckerOnline,proto3" json:"ptp_checker_online,omitempty"`
	EthernetCheckerOnline bool                 `protobuf:"varint,3,opt,name=ethernet_checker_online,json=ethernetCheckerOnline,proto3" json:"ethernet_checker_online,omitempty"`
	StorageCheckerOnline  bool                 `protobuf:"varint,4,opt,name=storage_checker_online,json=storageCheckerOnline,proto3" json:"storage_checker_online,omitempty"`
	GpuCheckerOnline      bool                 `protobuf:"varint,5,opt,name=gpu_checker_online,json=gpuCheckerOnline,proto3" json:"gpu_checker_online,omitempty"`
	Nics                  map[string]*NicState `protobuf:"bytes,6,rep,name=nics,proto3" json:"nics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MainPartition         *StorageDriveState   `protobuf:"bytes,7,opt,name=main_partition,json=mainPartition,proto3" json:"main_partition,omitempty"`
	DataPartition         *StorageDriveState   `protobuf:"bytes,8,opt,name=data_partition,json=dataPartition,proto3" json:"data_partition,omitempty"`
	Gpus                  []*GPUState          `protobuf:"bytes,9,rep,name=gpus,proto3" json:"gpus,omitempty"`
}

func (x *HostState) Reset() {
	*x = HostState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostState) ProtoMessage() {}

func (x *HostState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostState.ProtoReflect.Descriptor instead.
func (*HostState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{15}
}

func (x *HostState) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *HostState) GetPtpCheckerOnline() bool {
	if x != nil {
		return x.PtpCheckerOnline
	}
	return false
}

func (x *HostState) GetEthernetCheckerOnline() bool {
	if x != nil {
		return x.EthernetCheckerOnline
	}
	return false
}

func (x *HostState) GetStorageCheckerOnline() bool {
	if x != nil {
		return x.StorageCheckerOnline
	}
	return false
}

func (x *HostState) GetGpuCheckerOnline() bool {
	if x != nil {
		return x.GpuCheckerOnline
	}
	return false
}

func (x *HostState) GetNics() map[string]*NicState {
	if x != nil {
		return x.Nics
	}
	return nil
}

func (x *HostState) GetMainPartition() *StorageDriveState {
	if x != nil {
		return x.MainPartition
	}
	return nil
}

func (x *HostState) GetDataPartition() *StorageDriveState {
	if x != nil {
		return x.DataPartition
	}
	return nil
}

func (x *HostState) GetGpus() []*GPUState {
	if x != nil {
		return x.Gpus
	}
	return nil
}

type SoftwareUpdateVersionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag            string          `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	System         string          `protobuf:"bytes,2,opt,name=system,proto3" json:"system,omitempty"`
	Available      bool            `protobuf:"varint,3,opt,name=available,proto3" json:"available,omitempty"`
	Ready          bool            `protobuf:"varint,4,opt,name=ready,proto3" json:"ready,omitempty"`
	ImagesRequired map[string]bool `protobuf:"bytes,5,rep,name=images_required,json=imagesRequired,proto3" json:"images_required,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // True if done downloading
}

func (x *SoftwareUpdateVersionState) Reset() {
	*x = SoftwareUpdateVersionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareUpdateVersionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareUpdateVersionState) ProtoMessage() {}

func (x *SoftwareUpdateVersionState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareUpdateVersionState.ProtoReflect.Descriptor instead.
func (*SoftwareUpdateVersionState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{16}
}

func (x *SoftwareUpdateVersionState) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *SoftwareUpdateVersionState) GetSystem() string {
	if x != nil {
		return x.System
	}
	return ""
}

func (x *SoftwareUpdateVersionState) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *SoftwareUpdateVersionState) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *SoftwareUpdateVersionState) GetImagesRequired() map[string]bool {
	if x != nil {
		return x.ImagesRequired
	}
	return nil
}

type OperatingSystemState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Partition string `protobuf:"bytes,1,opt,name=partition,proto3" json:"partition,omitempty"`
	Version   string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *OperatingSystemState) Reset() {
	*x = OperatingSystemState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperatingSystemState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperatingSystemState) ProtoMessage() {}

func (x *OperatingSystemState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperatingSystemState.ProtoReflect.Descriptor instead.
func (*OperatingSystemState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{17}
}

func (x *OperatingSystemState) GetPartition() string {
	if x != nil {
		return x.Partition
	}
	return ""
}

func (x *OperatingSystemState) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type SystemVersionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current *OperatingSystemState `protobuf:"bytes,1,opt,name=current,proto3" json:"current,omitempty"`
	Other   *OperatingSystemState `protobuf:"bytes,2,opt,name=other,proto3" json:"other,omitempty"`
}

func (x *SystemVersionState) Reset() {
	*x = SystemVersionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemVersionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemVersionState) ProtoMessage() {}

func (x *SystemVersionState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemVersionState.ProtoReflect.Descriptor instead.
func (*SystemVersionState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{18}
}

func (x *SystemVersionState) GetCurrent() *OperatingSystemState {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *SystemVersionState) GetOther() *OperatingSystemState {
	if x != nil {
		return x.Other
	}
	return nil
}

type SoftwareVersionsState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current  *SoftwareUpdateVersionState `protobuf:"bytes,1,opt,name=current,proto3" json:"current,omitempty"`
	Previous *SoftwareUpdateVersionState `protobuf:"bytes,2,opt,name=previous,proto3" json:"previous,omitempty"`
	Target   *SoftwareUpdateVersionState `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	System   *SystemVersionState         `protobuf:"bytes,4,opt,name=system,proto3" json:"system,omitempty"`
}

func (x *SoftwareVersionsState) Reset() {
	*x = SoftwareVersionsState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersionsState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersionsState) ProtoMessage() {}

func (x *SoftwareVersionsState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersionsState.ProtoReflect.Descriptor instead.
func (*SoftwareVersionsState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{19}
}

func (x *SoftwareVersionsState) GetCurrent() *SoftwareUpdateVersionState {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *SoftwareVersionsState) GetPrevious() *SoftwareUpdateVersionState {
	if x != nil {
		return x.Previous
	}
	return nil
}

func (x *SoftwareVersionsState) GetTarget() *SoftwareUpdateVersionState {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *SoftwareVersionsState) GetSystem() *SystemVersionState {
	if x != nil {
		return x.System
	}
	return nil
}

type SoftwareProcessState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Stage SoftwareProcessStage `protobuf:"varint,2,opt,name=stage,proto3,enum=carbon.frontend.robot.SoftwareProcessStage" json:"stage,omitempty"`
}

func (x *SoftwareProcessState) Reset() {
	*x = SoftwareProcessState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareProcessState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareProcessState) ProtoMessage() {}

func (x *SoftwareProcessState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareProcessState.ProtoReflect.Descriptor instead.
func (*SoftwareProcessState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{20}
}

func (x *SoftwareProcessState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SoftwareProcessState) GetStage() SoftwareProcessStage {
	if x != nil {
		return x.Stage
	}
	return SoftwareProcessStage_SPS_UNKNOWN
}

type SoftwareState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Processes         map[string]*SoftwareProcessState `protobuf:"bytes,1,rep,name=processes,proto3" json:"processes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Versions          *SoftwareVersionsState           `protobuf:"bytes,2,opt,name=versions,proto3" json:"versions,omitempty"`
	Restarting        bool                             `protobuf:"varint,3,opt,name=restarting,proto3" json:"restarting,omitempty"`
	LastRestartTimeMs int64                            `protobuf:"varint,4,opt,name=last_restart_time_ms,json=lastRestartTimeMs,proto3" json:"last_restart_time_ms,omitempty"`
	Updating          bool                             `protobuf:"varint,5,opt,name=updating,proto3" json:"updating,omitempty"`
	LastUpdateTimeMs  int64                            `protobuf:"varint,6,opt,name=last_update_time_ms,json=lastUpdateTimeMs,proto3" json:"last_update_time_ms,omitempty"`
}

func (x *SoftwareState) Reset() {
	*x = SoftwareState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareState) ProtoMessage() {}

func (x *SoftwareState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareState.ProtoReflect.Descriptor instead.
func (*SoftwareState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{21}
}

func (x *SoftwareState) GetProcesses() map[string]*SoftwareProcessState {
	if x != nil {
		return x.Processes
	}
	return nil
}

func (x *SoftwareState) GetVersions() *SoftwareVersionsState {
	if x != nil {
		return x.Versions
	}
	return nil
}

func (x *SoftwareState) GetRestarting() bool {
	if x != nil {
		return x.Restarting
	}
	return false
}

func (x *SoftwareState) GetLastRestartTimeMs() int64 {
	if x != nil {
		return x.LastRestartTimeMs
	}
	return 0
}

func (x *SoftwareState) GetUpdating() bool {
	if x != nil {
		return x.Updating
	}
	return false
}

func (x *SoftwareState) GetLastUpdateTimeMs() int64 {
	if x != nil {
		return x.LastUpdateTimeMs
	}
	return 0
}

type DeepweedModelState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViableCropIds []string `protobuf:"bytes,1,rep,name=viable_crop_ids,json=viableCropIds,proto3" json:"viable_crop_ids,omitempty"`
}

func (x *DeepweedModelState) Reset() {
	*x = DeepweedModelState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeepweedModelState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepweedModelState) ProtoMessage() {}

func (x *DeepweedModelState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepweedModelState.ProtoReflect.Descriptor instead.
func (*DeepweedModelState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{22}
}

func (x *DeepweedModelState) GetViableCropIds() []string {
	if x != nil {
		return x.ViableCropIds
	}
	return nil
}

type P2PModelState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *P2PModelState) Reset() {
	*x = P2PModelState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PModelState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PModelState) ProtoMessage() {}

func (x *P2PModelState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PModelState.ProtoReflect.Descriptor instead.
func (*P2PModelState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{23}
}

type ModelState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	Type    string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// Types that are assignable to Specific:
	//
	//	*ModelState_Deepweed
	//	*ModelState_P2P
	Specific isModelState_Specific `protobuf_oneof:"specific"`
	Ready    bool                  `protobuf:"varint,5,opt,name=ready,proto3" json:"ready,omitempty"`
	Loaded   bool                  `protobuf:"varint,6,opt,name=loaded,proto3" json:"loaded,omitempty"`
}

func (x *ModelState) Reset() {
	*x = ModelState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelState) ProtoMessage() {}

func (x *ModelState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelState.ProtoReflect.Descriptor instead.
func (*ModelState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{24}
}

func (x *ModelState) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelState) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (m *ModelState) GetSpecific() isModelState_Specific {
	if m != nil {
		return m.Specific
	}
	return nil
}

func (x *ModelState) GetDeepweed() *DeepweedModelState {
	if x, ok := x.GetSpecific().(*ModelState_Deepweed); ok {
		return x.Deepweed
	}
	return nil
}

func (x *ModelState) GetP2P() *P2PModelState {
	if x, ok := x.GetSpecific().(*ModelState_P2P); ok {
		return x.P2P
	}
	return nil
}

func (x *ModelState) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *ModelState) GetLoaded() bool {
	if x != nil {
		return x.Loaded
	}
	return false
}

type isModelState_Specific interface {
	isModelState_Specific()
}

type ModelState_Deepweed struct {
	Deepweed *DeepweedModelState `protobuf:"bytes,3,opt,name=deepweed,proto3,oneof"`
}

type ModelState_P2P struct {
	P2P *P2PModelState `protobuf:"bytes,4,opt,name=p2p,proto3,oneof"`
}

func (*ModelState_Deepweed) isModelState_Specific() {}

func (*ModelState_P2P) isModelState_Specific() {}

type RowModelState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*ModelState `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *RowModelState) Reset() {
	*x = RowModelState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowModelState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowModelState) ProtoMessage() {}

func (x *RowModelState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowModelState.ProtoReflect.Descriptor instead.
func (*RowModelState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{25}
}

func (x *RowModelState) GetModels() []*ModelState {
	if x != nil {
		return x.Models
	}
	return nil
}

type ModelManagerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalModels         []*ModelState    `protobuf:"bytes,1,rep,name=local_models,json=localModels,proto3" json:"local_models,omitempty"`
	RowModels           []*RowModelState `protobuf:"bytes,2,rep,name=row_models,json=rowModels,proto3" json:"row_models,omitempty"`
	ActiveCropId        string           `protobuf:"bytes,3,opt,name=active_crop_id,json=activeCropId,proto3" json:"active_crop_id,omitempty"`
	ActiveDeepweedModel string           `protobuf:"bytes,4,opt,name=active_deepweed_model,json=activeDeepweedModel,proto3" json:"active_deepweed_model,omitempty"`
	ActiveP2PModel      string           `protobuf:"bytes,5,opt,name=active_p2p_model,json=activeP2pModel,proto3" json:"active_p2p_model,omitempty"`
}

func (x *ModelManagerState) Reset() {
	*x = ModelManagerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelManagerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelManagerState) ProtoMessage() {}

func (x *ModelManagerState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelManagerState.ProtoReflect.Descriptor instead.
func (*ModelManagerState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{26}
}

func (x *ModelManagerState) GetLocalModels() []*ModelState {
	if x != nil {
		return x.LocalModels
	}
	return nil
}

func (x *ModelManagerState) GetRowModels() []*RowModelState {
	if x != nil {
		return x.RowModels
	}
	return nil
}

func (x *ModelManagerState) GetActiveCropId() string {
	if x != nil {
		return x.ActiveCropId
	}
	return ""
}

func (x *ModelManagerState) GetActiveDeepweedModel() string {
	if x != nil {
		return x.ActiveDeepweedModel
	}
	return ""
}

func (x *ModelManagerState) GetActiveP2PModel() string {
	if x != nil {
		return x.ActiveP2PModel
	}
	return ""
}

type ComputerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Software *SoftwareState `protobuf:"bytes,1,opt,name=software,proto3" json:"software,omitempty"`
	Host     *HostState     `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
}

func (x *ComputerState) Reset() {
	*x = ComputerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputerState) ProtoMessage() {}

func (x *ComputerState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputerState.ProtoReflect.Descriptor instead.
func (*ComputerState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{27}
}

func (x *ComputerState) GetSoftware() *SoftwareState {
	if x != nil {
		return x.Software
	}
	return nil
}

func (x *ComputerState) GetHost() *HostState {
	if x != nil {
		return x.Host
	}
	return nil
}

type PowerTimeState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommanderOnTimeMs           int64   `protobuf:"varint,1,opt,name=commander_on_time_ms,json=commanderOnTimeMs,proto3" json:"commander_on_time_ms,omitempty"`
	PowerOnTimeMs               int64   `protobuf:"varint,2,opt,name=power_on_time_ms,json=powerOnTimeMs,proto3" json:"power_on_time_ms,omitempty"`
	AirConditionerEnabledTimeMs int64   `protobuf:"varint,3,opt,name=air_conditioner_enabled_time_ms,json=airConditionerEnabledTimeMs,proto3" json:"air_conditioner_enabled_time_ms,omitempty"`
	ChillerEnabledTimeMs        int64   `protobuf:"varint,4,opt,name=chiller_enabled_time_ms,json=chillerEnabledTimeMs,proto3" json:"chiller_enabled_time_ms,omitempty"`
	GpsEnabledTimeMs            int64   `protobuf:"varint,5,opt,name=gps_enabled_time_ms,json=gpsEnabledTimeMs,proto3" json:"gps_enabled_time_ms,omitempty"`
	StrobeEnabledTimeMs         int64   `protobuf:"varint,6,opt,name=strobe_enabled_time_ms,json=strobeEnabledTimeMs,proto3" json:"strobe_enabled_time_ms,omitempty"`
	WheelEncoderEnabledTimeMs   int64   `protobuf:"varint,7,opt,name=wheel_encoder_enabled_time_ms,json=wheelEncoderEnabledTimeMs,proto3" json:"wheel_encoder_enabled_time_ms,omitempty"`
	BtlEnabledTimeMs            []int64 `protobuf:"varint,8,rep,packed,name=btl_enabled_time_ms,json=btlEnabledTimeMs,proto3" json:"btl_enabled_time_ms,omitempty"`
	ServerEnabledTimeMs         []int64 `protobuf:"varint,9,rep,packed,name=server_enabled_time_ms,json=serverEnabledTimeMs,proto3" json:"server_enabled_time_ms,omitempty"`
	ScannersEnabledTimeMs       []int64 `protobuf:"varint,10,rep,packed,name=scanners_enabled_time_ms,json=scannersEnabledTimeMs,proto3" json:"scanners_enabled_time_ms,omitempty"`
	MainContactorEnabledTimeMs  int64   `protobuf:"varint,11,opt,name=main_contactor_enabled_time_ms,json=mainContactorEnabledTimeMs,proto3" json:"main_contactor_enabled_time_ms,omitempty"`
}

func (x *PowerTimeState) Reset() {
	*x = PowerTimeState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerTimeState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerTimeState) ProtoMessage() {}

func (x *PowerTimeState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerTimeState.ProtoReflect.Descriptor instead.
func (*PowerTimeState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{28}
}

func (x *PowerTimeState) GetCommanderOnTimeMs() int64 {
	if x != nil {
		return x.CommanderOnTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetPowerOnTimeMs() int64 {
	if x != nil {
		return x.PowerOnTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetAirConditionerEnabledTimeMs() int64 {
	if x != nil {
		return x.AirConditionerEnabledTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetChillerEnabledTimeMs() int64 {
	if x != nil {
		return x.ChillerEnabledTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetGpsEnabledTimeMs() int64 {
	if x != nil {
		return x.GpsEnabledTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetStrobeEnabledTimeMs() int64 {
	if x != nil {
		return x.StrobeEnabledTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetWheelEncoderEnabledTimeMs() int64 {
	if x != nil {
		return x.WheelEncoderEnabledTimeMs
	}
	return 0
}

func (x *PowerTimeState) GetBtlEnabledTimeMs() []int64 {
	if x != nil {
		return x.BtlEnabledTimeMs
	}
	return nil
}

func (x *PowerTimeState) GetServerEnabledTimeMs() []int64 {
	if x != nil {
		return x.ServerEnabledTimeMs
	}
	return nil
}

func (x *PowerTimeState) GetScannersEnabledTimeMs() []int64 {
	if x != nil {
		return x.ScannersEnabledTimeMs
	}
	return nil
}

func (x *PowerTimeState) GetMainContactorEnabledTimeMs() int64 {
	if x != nil {
		return x.MainContactorEnabledTimeMs
	}
	return 0
}

type VelocityState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentVelocityMph   float32   `protobuf:"fixed32,1,opt,name=current_velocity_mph,json=currentVelocityMph,proto3" json:"current_velocity_mph,omitempty"`
	TargetVelocityMph    float32   `protobuf:"fixed32,2,opt,name=target_velocity_mph,json=targetVelocityMph,proto3" json:"target_velocity_mph,omitempty"`
	RowTargetVelocityMph []float32 `protobuf:"fixed32,3,rep,packed,name=row_target_velocity_mph,json=rowTargetVelocityMph,proto3" json:"row_target_velocity_mph,omitempty"`
}

func (x *VelocityState) Reset() {
	*x = VelocityState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VelocityState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VelocityState) ProtoMessage() {}

func (x *VelocityState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VelocityState.ProtoReflect.Descriptor instead.
func (*VelocityState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{29}
}

func (x *VelocityState) GetCurrentVelocityMph() float32 {
	if x != nil {
		return x.CurrentVelocityMph
	}
	return 0
}

func (x *VelocityState) GetTargetVelocityMph() float32 {
	if x != nil {
		return x.TargetVelocityMph
	}
	return 0
}

func (x *VelocityState) GetRowTargetVelocityMph() []float32 {
	if x != nil {
		return x.RowTargetVelocityMph
	}
	return nil
}

type WeedingState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Weeding  []bool         `protobuf:"varint,1,rep,packed,name=weeding,proto3" json:"weeding,omitempty"`
	Stage    BootStage      `protobuf:"varint,2,opt,name=stage,proto3,enum=carbon.frontend.robot.BootStage" json:"stage,omitempty"`
	Velocity *VelocityState `protobuf:"bytes,3,opt,name=velocity,proto3" json:"velocity,omitempty"`
}

func (x *WeedingState) Reset() {
	*x = WeedingState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedingState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedingState) ProtoMessage() {}

func (x *WeedingState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedingState.ProtoReflect.Descriptor instead.
func (*WeedingState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{30}
}

func (x *WeedingState) GetWeeding() []bool {
	if x != nil {
		return x.Weeding
	}
	return nil
}

func (x *WeedingState) GetStage() BootStage {
	if x != nil {
		return x.Stage
	}
	return BootStage_BS_UPDATE_INSTALLING
}

func (x *WeedingState) GetVelocity() *VelocityState {
	if x != nil {
		return x.Velocity
	}
	return nil
}

type GlobalState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Computer       *ComputerState          `protobuf:"bytes,1,opt,name=computer,proto3" json:"computer,omitempty"`
	Gps            *GPSBoardState          `protobuf:"bytes,2,opt,name=gps,proto3" json:"gps,omitempty"`
	Wheel          *WheelEncoderBoardState `protobuf:"bytes,3,opt,name=wheel,proto3" json:"wheel,omitempty"`
	Strobe         *StrobeBoardState       `protobuf:"bytes,4,opt,name=strobe,proto3" json:"strobe,omitempty"`
	Supervisory    *SupervisoryPLCState    `protobuf:"bytes,5,opt,name=supervisory,proto3" json:"supervisory,omitempty"`
	Safety         *SafetyPLCState         `protobuf:"bytes,6,opt,name=safety,proto3" json:"safety,omitempty"`
	Chiller        *ChillerState           `protobuf:"bytes,8,opt,name=chiller,proto3" json:"chiller,omitempty"`
	AirConditioner *AirConditionerState    `protobuf:"bytes,9,opt,name=air_conditioner,json=airConditioner,proto3" json:"air_conditioner,omitempty"`
	Isobus         *ISOBusState            `protobuf:"bytes,10,opt,name=isobus,proto3" json:"isobus,omitempty"`
	PowerTime      *PowerTimeState         `protobuf:"bytes,11,opt,name=power_time,json=powerTime,proto3" json:"power_time,omitempty"`
	Models         *ModelManagerState      `protobuf:"bytes,12,opt,name=models,proto3" json:"models,omitempty"`
	Weeding        *WeedingState           `protobuf:"bytes,13,opt,name=weeding,proto3" json:"weeding,omitempty"`
	Usb            *USBDriveState          `protobuf:"bytes,14,opt,name=usb,proto3" json:"usb,omitempty"`
}

func (x *GlobalState) Reset() {
	*x = GlobalState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalState) ProtoMessage() {}

func (x *GlobalState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalState.ProtoReflect.Descriptor instead.
func (*GlobalState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{31}
}

func (x *GlobalState) GetComputer() *ComputerState {
	if x != nil {
		return x.Computer
	}
	return nil
}

func (x *GlobalState) GetGps() *GPSBoardState {
	if x != nil {
		return x.Gps
	}
	return nil
}

func (x *GlobalState) GetWheel() *WheelEncoderBoardState {
	if x != nil {
		return x.Wheel
	}
	return nil
}

func (x *GlobalState) GetStrobe() *StrobeBoardState {
	if x != nil {
		return x.Strobe
	}
	return nil
}

func (x *GlobalState) GetSupervisory() *SupervisoryPLCState {
	if x != nil {
		return x.Supervisory
	}
	return nil
}

func (x *GlobalState) GetSafety() *SafetyPLCState {
	if x != nil {
		return x.Safety
	}
	return nil
}

func (x *GlobalState) GetChiller() *ChillerState {
	if x != nil {
		return x.Chiller
	}
	return nil
}

func (x *GlobalState) GetAirConditioner() *AirConditionerState {
	if x != nil {
		return x.AirConditioner
	}
	return nil
}

func (x *GlobalState) GetIsobus() *ISOBusState {
	if x != nil {
		return x.Isobus
	}
	return nil
}

func (x *GlobalState) GetPowerTime() *PowerTimeState {
	if x != nil {
		return x.PowerTime
	}
	return nil
}

func (x *GlobalState) GetModels() *ModelManagerState {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *GlobalState) GetWeeding() *WeedingState {
	if x != nil {
		return x.Weeding
	}
	return nil
}

func (x *GlobalState) GetUsb() *USBDriveState {
	if x != nil {
		return x.Usb
	}
	return nil
}

type CameraState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vendor     string  `protobuf:"bytes,1,opt,name=vendor,proto3" json:"vendor,omitempty"`
	Model      string  `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
	Ip         string  `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Serial     string  `protobuf:"bytes,4,opt,name=serial,proto3" json:"serial,omitempty"`
	Id         string  `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	Connected  bool    `protobuf:"varint,6,opt,name=connected,proto3" json:"connected,omitempty"`
	ExposureUs float32 `protobuf:"fixed32,7,opt,name=exposure_us,json=exposureUs,proto3" json:"exposure_us,omitempty"`
	GpuId      int32   `protobuf:"varint,8,opt,name=gpu_id,json=gpuId,proto3" json:"gpu_id,omitempty"`
}

func (x *CameraState) Reset() {
	*x = CameraState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraState) ProtoMessage() {}

func (x *CameraState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraState.ProtoReflect.Descriptor instead.
func (*CameraState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{32}
}

func (x *CameraState) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *CameraState) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CameraState) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CameraState) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *CameraState) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CameraState) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *CameraState) GetExposureUs() float32 {
	if x != nil {
		return x.ExposureUs
	}
	return 0
}

func (x *CameraState) GetGpuId() int32 {
	if x != nil {
		return x.GpuId
	}
	return 0
}

type MaxonPidState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentP    uint32 `protobuf:"varint,1,opt,name=current_p,json=currentP,proto3" json:"current_p,omitempty"`
	CurrentI    uint32 `protobuf:"varint,2,opt,name=current_i,json=currentI,proto3" json:"current_i,omitempty"`
	PositionP   uint32 `protobuf:"varint,3,opt,name=position_p,json=positionP,proto3" json:"position_p,omitempty"`
	PositionI   uint32 `protobuf:"varint,4,opt,name=position_i,json=positionI,proto3" json:"position_i,omitempty"`
	PositionD   uint32 `protobuf:"varint,5,opt,name=position_d,json=positionD,proto3" json:"position_d,omitempty"`
	PositionFfv uint32 `protobuf:"varint,6,opt,name=position_ffv,json=positionFfv,proto3" json:"position_ffv,omitempty"`
	PositionFfa uint32 `protobuf:"varint,7,opt,name=position_ffa,json=positionFfa,proto3" json:"position_ffa,omitempty"`
}

func (x *MaxonPidState) Reset() {
	*x = MaxonPidState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaxonPidState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaxonPidState) ProtoMessage() {}

func (x *MaxonPidState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaxonPidState.ProtoReflect.Descriptor instead.
func (*MaxonPidState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{33}
}

func (x *MaxonPidState) GetCurrentP() uint32 {
	if x != nil {
		return x.CurrentP
	}
	return 0
}

func (x *MaxonPidState) GetCurrentI() uint32 {
	if x != nil {
		return x.CurrentI
	}
	return 0
}

func (x *MaxonPidState) GetPositionP() uint32 {
	if x != nil {
		return x.PositionP
	}
	return 0
}

func (x *MaxonPidState) GetPositionI() uint32 {
	if x != nil {
		return x.PositionI
	}
	return 0
}

func (x *MaxonPidState) GetPositionD() uint32 {
	if x != nil {
		return x.PositionD
	}
	return 0
}

func (x *MaxonPidState) GetPositionFfv() uint32 {
	if x != nil {
		return x.PositionFfv
	}
	return 0
}

func (x *MaxonPidState) GetPositionFfa() uint32 {
	if x != nil {
		return x.PositionFfa
	}
	return 0
}

type ServoState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                        string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Min                         int32  `protobuf:"varint,2,opt,name=min,proto3" json:"min,omitempty"`
	Max                         int32  `protobuf:"varint,3,opt,name=max,proto3" json:"max,omitempty"`
	LastReadPosition            int32  `protobuf:"varint,4,opt,name=last_read_position,json=lastReadPosition,proto3" json:"last_read_position,omitempty"`
	LastRequestedPosition       int32  `protobuf:"varint,5,opt,name=last_requested_position,json=lastRequestedPosition,proto3" json:"last_requested_position,omitempty"`
	LastRequestedVelocity       uint32 `protobuf:"varint,6,opt,name=last_requested_velocity,json=lastRequestedVelocity,proto3" json:"last_requested_velocity,omitempty"`
	LastRequestedFollowVelocity uint32 `protobuf:"varint,7,opt,name=last_requested_follow_velocity,json=lastRequestedFollowVelocity,proto3" json:"last_requested_follow_velocity,omitempty"`
	// Types that are assignable to Pids:
	//
	//	*ServoState_Maxon
	Pids isServoState_Pids `protobuf_oneof:"pids"`
}

func (x *ServoState) Reset() {
	*x = ServoState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoState) ProtoMessage() {}

func (x *ServoState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoState.ProtoReflect.Descriptor instead.
func (*ServoState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{34}
}

func (x *ServoState) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ServoState) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *ServoState) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *ServoState) GetLastReadPosition() int32 {
	if x != nil {
		return x.LastReadPosition
	}
	return 0
}

func (x *ServoState) GetLastRequestedPosition() int32 {
	if x != nil {
		return x.LastRequestedPosition
	}
	return 0
}

func (x *ServoState) GetLastRequestedVelocity() uint32 {
	if x != nil {
		return x.LastRequestedVelocity
	}
	return 0
}

func (x *ServoState) GetLastRequestedFollowVelocity() uint32 {
	if x != nil {
		return x.LastRequestedFollowVelocity
	}
	return 0
}

func (m *ServoState) GetPids() isServoState_Pids {
	if m != nil {
		return m.Pids
	}
	return nil
}

func (x *ServoState) GetMaxon() *MaxonPidState {
	if x, ok := x.GetPids().(*ServoState_Maxon); ok {
		return x.Maxon
	}
	return nil
}

type isServoState_Pids interface {
	isServoState_Pids()
}

type ServoState_Maxon struct {
	Maxon *MaxonPidState `protobuf:"bytes,8,opt,name=maxon,proto3,oneof"`
}

func (*ServoState_Maxon) isServoState_Pids() {}

type ScannerMetricState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NEntitiesTargeted  float32 `protobuf:"fixed32,1,opt,name=n_entities_targeted,json=nEntitiesTargeted,proto3" json:"n_entities_targeted,omitempty"`
	AvgAccuracy        float32 `protobuf:"fixed32,2,opt,name=avg_accuracy,json=avgAccuracy,proto3" json:"avg_accuracy,omitempty"`                        // At 2 P2P
	AvgInitialAccuracy float32 `protobuf:"fixed32,3,opt,name=avg_initial_accuracy,json=avgInitialAccuracy,proto3" json:"avg_initial_accuracy,omitempty"` // At first P2P
	AvgOverheadTime    float32 `protobuf:"fixed32,4,opt,name=avg_overhead_time,json=avgOverheadTime,proto3" json:"avg_overhead_time,omitempty"`
	ErrorRate          float32 `protobuf:"fixed32,5,opt,name=error_rate,json=errorRate,proto3" json:"error_rate,omitempty"`
	NotFoundRate       float32 `protobuf:"fixed32,6,opt,name=not_found_rate,json=notFoundRate,proto3" json:"not_found_rate,omitempty"`
	OutOfRangeRate     float32 `protobuf:"fixed32,7,opt,name=out_of_range_rate,json=outOfRangeRate,proto3" json:"out_of_range_rate,omitempty"`
	TargetChangedRate  float32 `protobuf:"fixed32,8,opt,name=target_changed_rate,json=targetChangedRate,proto3" json:"target_changed_rate,omitempty"`
}

func (x *ScannerMetricState) Reset() {
	*x = ScannerMetricState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerMetricState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerMetricState) ProtoMessage() {}

func (x *ScannerMetricState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerMetricState.ProtoReflect.Descriptor instead.
func (*ScannerMetricState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{35}
}

func (x *ScannerMetricState) GetNEntitiesTargeted() float32 {
	if x != nil {
		return x.NEntitiesTargeted
	}
	return 0
}

func (x *ScannerMetricState) GetAvgAccuracy() float32 {
	if x != nil {
		return x.AvgAccuracy
	}
	return 0
}

func (x *ScannerMetricState) GetAvgInitialAccuracy() float32 {
	if x != nil {
		return x.AvgInitialAccuracy
	}
	return 0
}

func (x *ScannerMetricState) GetAvgOverheadTime() float32 {
	if x != nil {
		return x.AvgOverheadTime
	}
	return 0
}

func (x *ScannerMetricState) GetErrorRate() float32 {
	if x != nil {
		return x.ErrorRate
	}
	return 0
}

func (x *ScannerMetricState) GetNotFoundRate() float32 {
	if x != nil {
		return x.NotFoundRate
	}
	return 0
}

func (x *ScannerMetricState) GetOutOfRangeRate() float32 {
	if x != nil {
		return x.OutOfRangeRate
	}
	return 0
}

func (x *ScannerMetricState) GetTargetChangedRate() float32 {
	if x != nil {
		return x.TargetChangedRate
	}
	return 0
}

type ScannerLaserState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial           string  `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	NShots           int64   `protobuf:"varint,2,opt,name=n_shots,json=nShots,proto3" json:"n_shots,omitempty"`
	FireTimeMs       int64   `protobuf:"varint,3,opt,name=fire_time_ms,json=fireTimeMs,proto3" json:"fire_time_ms,omitempty"`
	ControlledTimeMs int64   `protobuf:"varint,4,opt,name=controlled_time_ms,json=controlledTimeMs,proto3" json:"controlled_time_ms,omitempty"`
	LpsuPower        bool    `protobuf:"varint,5,opt,name=lpsu_power,json=lpsuPower,proto3" json:"lpsu_power,omitempty"`
	LpsuCurrent      float32 `protobuf:"fixed32,6,opt,name=lpsu_current,json=lpsuCurrent,proto3" json:"lpsu_current,omitempty"`
	ThermSensor      float32 `protobuf:"fixed32,7,opt,name=therm_sensor,json=thermSensor,proto3" json:"therm_sensor,omitempty"`
	ThermRef         float32 `protobuf:"fixed32,8,opt,name=therm_ref,json=thermRef,proto3" json:"therm_ref,omitempty"`
	DeltaTemp        float32 `protobuf:"fixed32,9,opt,name=delta_temp,json=deltaTemp,proto3" json:"delta_temp,omitempty"`
	PowerBypassed    bool    `protobuf:"varint,10,opt,name=power_bypassed,json=powerBypassed,proto3" json:"power_bypassed,omitempty"`
	Armed            bool    `protobuf:"varint,11,opt,name=armed,proto3" json:"armed,omitempty"`
	Watchdog         bool    `protobuf:"varint,12,opt,name=watchdog,proto3" json:"watchdog,omitempty"`
	FiringRequested  bool    `protobuf:"varint,13,opt,name=firing_requested,json=firingRequested,proto3" json:"firing_requested,omitempty"`
	Firing           bool    `protobuf:"varint,14,opt,name=firing,proto3" json:"firing,omitempty"`
	Intensity        float32 `protobuf:"fixed32,15,opt,name=intensity,proto3" json:"intensity,omitempty"`
	Wattage          float32 `protobuf:"fixed32,16,opt,name=wattage,proto3" json:"wattage,omitempty"`
	NominalWattage   float32 `protobuf:"fixed32,17,opt,name=nominal_wattage,json=nominalWattage,proto3" json:"nominal_wattage,omitempty"`
}

func (x *ScannerLaserState) Reset() {
	*x = ScannerLaserState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerLaserState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerLaserState) ProtoMessage() {}

func (x *ScannerLaserState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerLaserState.ProtoReflect.Descriptor instead.
func (*ScannerLaserState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{36}
}

func (x *ScannerLaserState) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *ScannerLaserState) GetNShots() int64 {
	if x != nil {
		return x.NShots
	}
	return 0
}

func (x *ScannerLaserState) GetFireTimeMs() int64 {
	if x != nil {
		return x.FireTimeMs
	}
	return 0
}

func (x *ScannerLaserState) GetControlledTimeMs() int64 {
	if x != nil {
		return x.ControlledTimeMs
	}
	return 0
}

func (x *ScannerLaserState) GetLpsuPower() bool {
	if x != nil {
		return x.LpsuPower
	}
	return false
}

func (x *ScannerLaserState) GetLpsuCurrent() float32 {
	if x != nil {
		return x.LpsuCurrent
	}
	return 0
}

func (x *ScannerLaserState) GetThermSensor() float32 {
	if x != nil {
		return x.ThermSensor
	}
	return 0
}

func (x *ScannerLaserState) GetThermRef() float32 {
	if x != nil {
		return x.ThermRef
	}
	return 0
}

func (x *ScannerLaserState) GetDeltaTemp() float32 {
	if x != nil {
		return x.DeltaTemp
	}
	return 0
}

func (x *ScannerLaserState) GetPowerBypassed() bool {
	if x != nil {
		return x.PowerBypassed
	}
	return false
}

func (x *ScannerLaserState) GetArmed() bool {
	if x != nil {
		return x.Armed
	}
	return false
}

func (x *ScannerLaserState) GetWatchdog() bool {
	if x != nil {
		return x.Watchdog
	}
	return false
}

func (x *ScannerLaserState) GetFiringRequested() bool {
	if x != nil {
		return x.FiringRequested
	}
	return false
}

func (x *ScannerLaserState) GetFiring() bool {
	if x != nil {
		return x.Firing
	}
	return false
}

func (x *ScannerLaserState) GetIntensity() float32 {
	if x != nil {
		return x.Intensity
	}
	return 0
}

func (x *ScannerLaserState) GetWattage() float32 {
	if x != nil {
		return x.Wattage
	}
	return 0
}

func (x *ScannerLaserState) GetNominalWattage() float32 {
	if x != nil {
		return x.NominalWattage
	}
	return 0
}

type ScannerCrosshairState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastUpdateTimestampMs int64   `protobuf:"varint,1,opt,name=last_update_timestamp_ms,json=lastUpdateTimestampMs,proto3" json:"last_update_timestamp_ms,omitempty"`
	X                     float32 `protobuf:"fixed32,2,opt,name=x,proto3" json:"x,omitempty"` // As percentages
	Y                     float32 `protobuf:"fixed32,3,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *ScannerCrosshairState) Reset() {
	*x = ScannerCrosshairState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerCrosshairState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerCrosshairState) ProtoMessage() {}

func (x *ScannerCrosshairState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerCrosshairState.ProtoReflect.Descriptor instead.
func (*ScannerCrosshairState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{37}
}

func (x *ScannerCrosshairState) GetLastUpdateTimestampMs() int64 {
	if x != nil {
		return x.LastUpdateTimestampMs
	}
	return 0
}

func (x *ScannerCrosshairState) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *ScannerCrosshairState) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type ScannerLensState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastRequestedValue float32 `protobuf:"fixed32,1,opt,name=last_requested_value,json=lastRequestedValue,proto3" json:"last_requested_value,omitempty"`
	ActualValue        float32 `protobuf:"fixed32,2,opt,name=actual_value,json=actualValue,proto3" json:"actual_value,omitempty"`
}

func (x *ScannerLensState) Reset() {
	*x = ScannerLensState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerLensState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerLensState) ProtoMessage() {}

func (x *ScannerLensState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerLensState.ProtoReflect.Descriptor instead.
func (*ScannerLensState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{38}
}

func (x *ScannerLensState) GetLastRequestedValue() float32 {
	if x != nil {
		return x.LastRequestedValue
	}
	return 0
}

func (x *ScannerLensState) GetActualValue() float32 {
	if x != nil {
		return x.ActualValue
	}
	return 0
}

type ScannerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Board     *BoardState            `protobuf:"bytes,2,opt,name=board,proto3" json:"board,omitempty"`
	Enabled   bool                   `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Pan       *ServoState            `protobuf:"bytes,4,opt,name=pan,proto3" json:"pan,omitempty"`
	Tilt      *ServoState            `protobuf:"bytes,5,opt,name=tilt,proto3" json:"tilt,omitempty"`
	Crosshair *ScannerCrosshairState `protobuf:"bytes,6,opt,name=crosshair,proto3" json:"crosshair,omitempty"`
	Lens      *ScannerLensState      `protobuf:"bytes,7,opt,name=lens,proto3" json:"lens,omitempty"`
	Laser     *ScannerLaserState     `protobuf:"bytes,8,opt,name=laser,proto3" json:"laser,omitempty"`
	Metrics   *ScannerMetricState    `protobuf:"bytes,9,opt,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *ScannerState) Reset() {
	*x = ScannerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerState) ProtoMessage() {}

func (x *ScannerState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerState.ProtoReflect.Descriptor instead.
func (*ScannerState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{39}
}

func (x *ScannerState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ScannerState) GetBoard() *BoardState {
	if x != nil {
		return x.Board
	}
	return nil
}

func (x *ScannerState) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *ScannerState) GetPan() *ServoState {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *ScannerState) GetTilt() *ServoState {
	if x != nil {
		return x.Tilt
	}
	return nil
}

func (x *ScannerState) GetCrosshair() *ScannerCrosshairState {
	if x != nil {
		return x.Crosshair
	}
	return nil
}

func (x *ScannerState) GetLens() *ScannerLensState {
	if x != nil {
		return x.Lens
	}
	return nil
}

func (x *ScannerState) GetLaser() *ScannerLaserState {
	if x != nil {
		return x.Laser
	}
	return nil
}

func (x *ScannerState) GetMetrics() *ScannerMetricState {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type CVNodeState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MeanFps           float32 `protobuf:"fixed32,2,opt,name=mean_fps,json=meanFps,proto3" json:"mean_fps,omitempty"`
	MeanOutputLatency float32 `protobuf:"fixed32,3,opt,name=mean_output_latency,json=meanOutputLatency,proto3" json:"mean_output_latency,omitempty"`
	P99OutputLatency  float32 `protobuf:"fixed32,4,opt,name=p99_output_latency,json=p99OutputLatency,proto3" json:"p99_output_latency,omitempty"`
	MeanRealLatency   float32 `protobuf:"fixed32,5,opt,name=mean_real_latency,json=meanRealLatency,proto3" json:"mean_real_latency,omitempty"`
	P99RealLatency    float32 `protobuf:"fixed32,6,opt,name=p99_real_latency,json=p99RealLatency,proto3" json:"p99_real_latency,omitempty"`
	State             string  `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	PullMs            float32 `protobuf:"fixed32,8,opt,name=pull_ms,json=pullMs,proto3" json:"pull_ms,omitempty"`
	ProcessingMs      float32 `protobuf:"fixed32,9,opt,name=processing_ms,json=processingMs,proto3" json:"processing_ms,omitempty"`
	PushingMs         float32 `protobuf:"fixed32,10,opt,name=pushing_ms,json=pushingMs,proto3" json:"pushing_ms,omitempty"`
}

func (x *CVNodeState) Reset() {
	*x = CVNodeState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CVNodeState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CVNodeState) ProtoMessage() {}

func (x *CVNodeState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CVNodeState.ProtoReflect.Descriptor instead.
func (*CVNodeState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{40}
}

func (x *CVNodeState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CVNodeState) GetMeanFps() float32 {
	if x != nil {
		return x.MeanFps
	}
	return 0
}

func (x *CVNodeState) GetMeanOutputLatency() float32 {
	if x != nil {
		return x.MeanOutputLatency
	}
	return 0
}

func (x *CVNodeState) GetP99OutputLatency() float32 {
	if x != nil {
		return x.P99OutputLatency
	}
	return 0
}

func (x *CVNodeState) GetMeanRealLatency() float32 {
	if x != nil {
		return x.MeanRealLatency
	}
	return 0
}

func (x *CVNodeState) GetP99RealLatency() float32 {
	if x != nil {
		return x.P99RealLatency
	}
	return 0
}

func (x *CVNodeState) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *CVNodeState) GetPullMs() float32 {
	if x != nil {
		return x.PullMs
	}
	return 0
}

func (x *CVNodeState) GetProcessingMs() float32 {
	if x != nil {
		return x.ProcessingMs
	}
	return 0
}

func (x *CVNodeState) GetPushingMs() float32 {
	if x != nil {
		return x.PushingMs
	}
	return 0
}

type CVState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*CVNodeState `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
}

func (x *CVState) Reset() {
	*x = CVState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CVState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CVState) ProtoMessage() {}

func (x *CVState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CVState.ProtoReflect.Descriptor instead.
func (*CVState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{41}
}

func (x *CVState) GetNodes() []*CVNodeState {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type TrackerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	NTrajectories uint32 `protobuf:"varint,2,opt,name=n_trajectories,json=nTrajectories,proto3" json:"n_trajectories,omitempty"`
	Capacity      uint32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *TrackerState) Reset() {
	*x = TrackerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackerState) ProtoMessage() {}

func (x *TrackerState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackerState.ProtoReflect.Descriptor instead.
func (*TrackerState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{42}
}

func (x *TrackerState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrackerState) GetNTrajectories() uint32 {
	if x != nil {
		return x.NTrajectories
	}
	return 0
}

func (x *TrackerState) GetCapacity() uint32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

type IngestClientState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	OverCapacity    bool   `protobuf:"varint,2,opt,name=over_capacity,json=overCapacity,proto3" json:"over_capacity,omitempty"`
	OverConstraints bool   `protobuf:"varint,3,opt,name=over_constraints,json=overConstraints,proto3" json:"over_constraints,omitempty"`
}

func (x *IngestClientState) Reset() {
	*x = IngestClientState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IngestClientState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngestClientState) ProtoMessage() {}

func (x *IngestClientState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngestClientState.ProtoReflect.Descriptor instead.
func (*IngestClientState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{43}
}

func (x *IngestClientState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IngestClientState) GetOverCapacity() bool {
	if x != nil {
		return x.OverCapacity
	}
	return false
}

func (x *IngestClientState) GetOverConstraints() bool {
	if x != nil {
		return x.OverConstraints
	}
	return false
}

type AimbotState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trackers []*TrackerState      `protobuf:"bytes,1,rep,name=trackers,proto3" json:"trackers,omitempty"`
	Ingests  []*IngestClientState `protobuf:"bytes,2,rep,name=ingests,proto3" json:"ingests,omitempty"`
}

func (x *AimbotState) Reset() {
	*x = AimbotState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AimbotState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AimbotState) ProtoMessage() {}

func (x *AimbotState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AimbotState.ProtoReflect.Descriptor instead.
func (*AimbotState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{44}
}

func (x *AimbotState) GetTrackers() []*TrackerState {
	if x != nil {
		return x.Trackers
	}
	return nil
}

func (x *AimbotState) GetIngests() []*IngestClientState {
	if x != nil {
		return x.Ingests
	}
	return nil
}

type RowState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Computer *ComputerState  `protobuf:"bytes,1,opt,name=computer,proto3" json:"computer,omitempty"`
	Scanners []*ScannerState `protobuf:"bytes,2,rep,name=scanners,proto3" json:"scanners,omitempty"`
	Predicts []*CameraState  `protobuf:"bytes,3,rep,name=predicts,proto3" json:"predicts,omitempty"`
	Targets  []*CameraState  `protobuf:"bytes,4,rep,name=targets,proto3" json:"targets,omitempty"`
	Extras   []*CameraState  `protobuf:"bytes,5,rep,name=extras,proto3" json:"extras,omitempty"`
	Cv       *CVState        `protobuf:"bytes,6,opt,name=cv,proto3" json:"cv,omitempty"`
	Aimbot   *AimbotState    `protobuf:"bytes,7,opt,name=aimbot,proto3" json:"aimbot,omitempty"`
}

func (x *RowState) Reset() {
	*x = RowState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowState) ProtoMessage() {}

func (x *RowState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowState.ProtoReflect.Descriptor instead.
func (*RowState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{45}
}

func (x *RowState) GetComputer() *ComputerState {
	if x != nil {
		return x.Computer
	}
	return nil
}

func (x *RowState) GetScanners() []*ScannerState {
	if x != nil {
		return x.Scanners
	}
	return nil
}

func (x *RowState) GetPredicts() []*CameraState {
	if x != nil {
		return x.Predicts
	}
	return nil
}

func (x *RowState) GetTargets() []*CameraState {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *RowState) GetExtras() []*CameraState {
	if x != nil {
		return x.Extras
	}
	return nil
}

func (x *RowState) GetCv() *CVState {
	if x != nil {
		return x.Cv
	}
	return nil
}

func (x *RowState) GetAimbot() *AimbotState {
	if x != nil {
		return x.Aimbot
	}
	return nil
}

type RobotState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Global *GlobalState `protobuf:"bytes,1,opt,name=global,proto3" json:"global,omitempty"`
	Rows   []*RowState  `protobuf:"bytes,2,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *RobotState) Reset() {
	*x = RobotState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotState) ProtoMessage() {}

func (x *RobotState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotState.ProtoReflect.Descriptor instead.
func (*RobotState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{46}
}

func (x *RobotState) GetGlobal() *GlobalState {
	if x != nil {
		return x.Global
	}
	return nil
}

func (x *RobotState) GetRows() []*RowState {
	if x != nil {
		return x.Rows
	}
	return nil
}

type TimestampedRobotState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp  `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	State *RobotState `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *TimestampedRobotState) Reset() {
	*x = TimestampedRobotState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimestampedRobotState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimestampedRobotState) ProtoMessage() {}

func (x *TimestampedRobotState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimestampedRobotState.ProtoReflect.Descriptor instead.
func (*TimestampedRobotState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{47}
}

func (x *TimestampedRobotState) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *TimestampedRobotState) GetState() *RobotState {
	if x != nil {
		return x.State
	}
	return nil
}

type RobotStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *RobotStateRequest) Reset() {
	*x = RobotStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_robot_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotStateRequest) ProtoMessage() {}

func (x *RobotStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_robot_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotStateRequest.ProtoReflect.Descriptor instead.
func (*RobotStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_robot_proto_rawDescGZIP(), []int{48}
}

func (x *RobotStateRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

var File_frontend_proto_robot_proto protoreflect.FileDescriptor

var file_frontend_proto_robot_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbe,
	0x01, 0x0a, 0x0a, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x76, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6f, 0x6f, 0x74,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x65, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xaa, 0x01, 0x0a, 0x08, 0x47, 0x50, 0x53, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08,
	0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e,
	0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x5f, 0x6d, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x6c, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x4d, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f,
	0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x58, 0x12, 0x15,
	0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x65, 0x63, 0x65, 0x66, 0x59, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x63, 0x65, 0x66, 0x5f, 0x7a, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x65, 0x63, 0x65, 0x66, 0x5a, 0x22, 0xf7, 0x01, 0x0a,
	0x0d, 0x47, 0x50, 0x53, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x76, 0x65, 0x5f,
	0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x76, 0x65, 0x46,
	0x69, 0x78, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x78, 0x5f, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x76,
	0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x46, 0x69, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75,
	0x6d, 0x5f, 0x73, 0x61, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x75,
	0x6d, 0x53, 0x61, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x64, 0x6f, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x04, 0x68, 0x64, 0x6f, 0x70, 0x12, 0x3b, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x2e, 0x47, 0x50, 0x53, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x0a, 0x57, 0x68, 0x65, 0x65, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x69,
	0x63, 0x6b, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x76, 0x65, 0x6c, 0x4d, 0x70, 0x68, 0x12, 0x1a, 0x0a, 0x08,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x49, 0x6e, 0x22, 0x8c, 0x01, 0x0a, 0x16, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x39, 0x0a, 0x06, 0x77, 0x68, 0x65, 0x65, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e,
	0x57, 0x68, 0x65, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x77, 0x68, 0x65, 0x65,
	0x6c, 0x73, 0x22, 0xc4, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x73, 0x12, 0x39,
	0x0a, 0x19, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x0d, 0x0a, 0x0b, 0x49, 0x53, 0x4f,
	0x42, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x43, 0x68, 0x69, 0x6c,
	0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x41, 0x69, 0x72, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22,
	0x56, 0x0a, 0x0d, 0x55, 0x53, 0x42, 0x44, 0x72, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x27,
	0x0a, 0x0f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x63,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x63, 0x74, 0x22, 0x95, 0x0b, 0x0a, 0x13, 0x53, 0x75, 0x70, 0x65,
	0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x50, 0x4c, 0x43, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x63, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x41, 0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61,
	0x67, 0x65, 0x5f, 0x61, 0x5f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x63,
	0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x42, 0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f,
	0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x5f, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x43, 0x12, 0x20,
	0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x42,
	0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x62,
	0x5f, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x42, 0x43, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x61, 0x63, 0x56,
	0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x43, 0x12, 0x27, 0x0a, 0x10, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x5f, 0x33, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0d, 0x70, 0x68, 0x61, 0x73, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x56, 0x61, 0x33,
	0x12, 0x25, 0x0a, 0x0f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x77, 0x5f, 0x33, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x57, 0x33, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x42, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x67, 0x6f, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x76, 0x65, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x56, 0x65, 0x72, 0x79, 0x42, 0x61, 0x64, 0x12, 0x2e, 0x0a, 0x13,
	0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x31, 0x32, 0x76, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x62, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x31, 0x32, 0x76, 0x12, 0x38, 0x0a, 0x18,
	0x61, 0x69, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x5f,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x61, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x70, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x67, 0x70, 0x73, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x34, 0x0a,
	0x16, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x77,
	0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x74, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x14, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x74, 0x6c, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x15, 0x20, 0x03, 0x28, 0x08, 0x52,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x2b, 0x0a, 0x11, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x16, 0x20, 0x03, 0x28, 0x08, 0x52, 0x10, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x17,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d,
	0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x62,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x62, 0x12, 0x36, 0x0a,
	0x17, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x5f,
	0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x48, 0x75, 0x6d,
	0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f,
	0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69, 0x6e, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74,
	0x79, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73,
	0x73, 0x65, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x42,
	0x79, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x68, 0x75, 0x6d, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x74, 0x65, 0x6d, 0x70, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6c, 0x69, 0x66, 0x74,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x21, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x30, 0x0a,
	0x14, 0x77, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x77, 0x61, 0x74,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xca, 0x02, 0x0a, 0x0e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x50, 0x4c, 0x43, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x69, 0x6e, 0x5f, 0x63, 0x61, 0x62, 0x5f, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x43, 0x61, 0x62, 0x45, 0x73,
	0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6c,
	0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x4b, 0x65,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70,
	0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6c, 0x65, 0x66, 0x74, 0x45, 0x73,
	0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x45, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c,
	0x69, 0x66, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x69,
	0x66, 0x74, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6c, 0x69, 0x66, 0x74, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x22, 0xab, 0x01, 0x0a,
	0x08, 0x4e, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x74, 0x70,
	0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70,
	0x74, 0x70, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x74, 0x70, 0x5f,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x74,
	0x70, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x69,
	0x6e, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x6c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x43, 0x0a, 0x11, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x22,
	0xb0, 0x02, 0x0a, 0x08, 0x47, 0x50, 0x55, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x43, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x43, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x75,
	0x73, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x55, 0x73, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x61, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x03, 0x66, 0x61, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x70, 0x75, 0x5f, 0x75,
	0x74, 0x69, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x67, 0x70, 0x75, 0x55, 0x74,
	0x69, 0x6c, 0x22, 0xde, 0x04, 0x0a, 0x09, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x74, 0x70, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x74, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x65, 0x74, 0x68, 0x65, 0x72, 0x6e,
	0x65, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x65, 0x74, 0x68, 0x65, 0x72, 0x6e, 0x65,
	0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x34,
	0x0a, 0x16, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65,
	0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x67, 0x70, 0x75, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x6e, 0x69, 0x63, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x4e, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6e, 0x69,
	0x63, 0x73, 0x12, 0x4f, 0x0a, 0x0e, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x61, 0x72, 0x74,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x04, 0x67, 0x70, 0x75, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x50, 0x55, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x04, 0x67, 0x70, 0x75, 0x73, 0x1a, 0x58, 0x0a, 0x09, 0x4e, 0x69, 0x63,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e,
	0x4e, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xad, 0x02, 0x0a, 0x1a, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x74, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x12, 0x6e, 0x0a, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x1a, 0x41, 0x0a, 0x13, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x4e, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x9e, 0x01, 0x0a, 0x12, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x07, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x41, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x22, 0xc1, 0x02, 0x0a, 0x15, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4b,
	0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x08, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x22, 0x6d, 0x0a, 0x14, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0xb3, 0x03, 0x0a, 0x0d, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x51, 0x0a, 0x09, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x08,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2f, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x1a, 0x69, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3c, 0x0a,
	0x12, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x72,
	0x6f, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x73, 0x22, 0x0f, 0x0a, 0x0d, 0x50,
	0x32, 0x50, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0xf8, 0x01, 0x0a,
	0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x77,
	0x65, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x03, 0x70, 0x32, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x32, 0x50, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x03, 0x70, 0x32, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x22, 0x4a, 0x0a, 0x0d, 0x52, 0x6f, 0x77, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12,
	0x43, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x6f, 0x77, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63,
	0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x28,
	0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x32, 0x70, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x32, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x87, 0x01, 0x0a, 0x0d, 0x43, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x73, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x08, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x22, 0xee, 0x04, 0x0a, 0x0e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x65, 0x72, 0x4f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x27, 0x0a, 0x10, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x4f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12,
	0x44, 0x0a, 0x1f, 0x61, 0x69, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1b, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x2d, 0x0a, 0x13,
	0x67, 0x70, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x67, 0x70, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73,
	0x12, 0x40, 0x0a, 0x1d, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x62, 0x74, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x10, 0x62, 0x74, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x12, 0x33, 0x0a, 0x16, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x15, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12,
	0x42, 0x0a, 0x1e, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1a, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x0d, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x4d, 0x70, 0x68, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x4d, 0x70, 0x68, 0x12, 0x35, 0x0a, 0x17, 0x72, 0x6f, 0x77, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d,
	0x70, 0x68, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x14, 0x72, 0x6f, 0x77, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x4d, 0x70, 0x68, 0x22, 0xa2,
	0x01, 0x0a, 0x0c, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x08,
	0x52, 0x07, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x42, 0x6f, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x12, 0x40, 0x0a, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x56, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x22, 0xef, 0x06, 0x0a, 0x0b, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x03, 0x67, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x50, 0x53, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x03, 0x67, 0x70, 0x73, 0x12, 0x43, 0x0a,
	0x05, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x77, 0x68, 0x65,
	0x65, 0x6c, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x73, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f,
	0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x50, 0x4c, 0x43, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f, 0x72,
	0x79, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x50, 0x4c, 0x43, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x12, 0x3d, 0x0a, 0x07, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x12,
	0x53, 0x0a, 0x0f, 0x61, 0x69, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x06, 0x69, 0x73, 0x6f, 0x62, 0x75, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x49, 0x53, 0x4f,
	0x42, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x69, 0x73, 0x6f, 0x62, 0x75, 0x73,
	0x12, 0x44, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x07, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x0a, 0x03, 0x75, 0x73, 0x62, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x55, 0x53, 0x42,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x03, 0x75, 0x73, 0x62, 0x4a,
	0x04, 0x08, 0x07, 0x10, 0x08, 0x22, 0xc9, 0x01, 0x0a, 0x0b, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x67, 0x70,
	0x75, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x67, 0x70, 0x75, 0x49,
	0x64, 0x22, 0xec, 0x01, 0x0a, 0x0d, 0x4d, 0x61, 0x78, 0x6f, 0x6e, 0x50, 0x69, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x66, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x66, 0x76, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x66, 0x61, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x66, 0x61,
	0x22, 0xed, 0x02, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x72, 0x65, 0x61, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x61, 0x64, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a,
	0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15,
	0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x43, 0x0a, 0x1e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1b, 0x6c,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x6d, 0x61,
	0x78, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2e, 0x4d, 0x61, 0x78, 0x6f, 0x6e, 0x50, 0x69, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48,
	0x00, 0x52, 0x05, 0x6d, 0x61, 0x78, 0x6f, 0x6e, 0x42, 0x06, 0x0a, 0x04, 0x70, 0x69, 0x64, 0x73,
	0x22, 0xe5, 0x02, 0x0a, 0x12, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x5f, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x6e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76, 0x67, 0x5f, 0x61,
	0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61,
	0x76, 0x67, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x76,
	0x67, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x61, 0x76, 0x67, 0x49, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x76, 0x67, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x68, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x61, 0x76, 0x67, 0x4f, 0x76, 0x65, 0x72,
	0x68, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x6f, 0x74, 0x5f, 0x66,
	0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0c, 0x6e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a,
	0x11, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6f, 0x75, 0x74, 0x4f, 0x66, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x22, 0xb2, 0x04, 0x0a, 0x11, 0x53, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x5f, 0x73, 0x68, 0x6f, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x53, 0x68, 0x6f, 0x74, 0x73, 0x12,
	0x20, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x6c, 0x70, 0x73, 0x75, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x6c, 0x70, 0x73, 0x75, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x6c, 0x70, 0x73, 0x75, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6c, 0x70, 0x73, 0x75, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x5f, 0x72, 0x65,
	0x66, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x52, 0x65,
	0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x65, 0x6d, 0x70,
	0x12, 0x25, 0x0a, 0x0e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42,
	0x79, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x72, 0x6d, 0x65, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x77, 0x61, 0x74, 0x63, 0x68, 0x64, 0x6f, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x77, 0x61, 0x74, 0x63, 0x68, 0x64, 0x6f, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x61,
	0x74, 0x74, 0x61, 0x67, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x77, 0x61, 0x74,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x77, 0x61, 0x74, 0x74, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6e,
	0x6f, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x57, 0x61, 0x74, 0x74, 0x61, 0x67, 0x65, 0x22, 0x6c, 0x0a,
	0x15, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12,
	0x0c, 0x0a, 0x01, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a,
	0x01, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x22, 0x67, 0x0a, 0x10, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x65, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x6c,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0xef, 0x03, 0x0a, 0x0c, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x33, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x03, 0x70, 0x61,
	0x6e, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x09, 0x63, 0x72, 0x6f, 0x73,
	0x73, 0x68, 0x61, 0x69, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x72, 0x6f, 0x73, 0x73,
	0x68, 0x61, 0x69, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x63, 0x72, 0x6f, 0x73, 0x73,
	0x68, 0x61, 0x69, 0x72, 0x12, 0x3b, 0x0a, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x4c, 0x65, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x04, 0x6c, 0x65, 0x6e,
	0x73, 0x12, 0x3e, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x12, 0x43, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0xe3, 0x02, 0x0a, 0x0b, 0x43, 0x56, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x65,
	0x61, 0x6e, 0x5f, 0x66, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x6d, 0x65,
	0x61, 0x6e, 0x46, 0x70, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x65, 0x61, 0x6e, 0x5f, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x11, 0x6d, 0x65, 0x61, 0x6e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4c, 0x61,
	0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x39, 0x39, 0x5f, 0x6f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x10, 0x70, 0x39, 0x39, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x6c,
	0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f,
	0x6d, 0x65, 0x61, 0x6e, 0x52, 0x65, 0x61, 0x6c, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x39, 0x39, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6c, 0x61, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x70, 0x39, 0x39, 0x52, 0x65,
	0x61, 0x6c, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x06, 0x70, 0x75, 0x6c, 0x6c, 0x4d, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x75, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x70, 0x75, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x22, 0x43, 0x0a, 0x07,
	0x43, 0x56, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x43,
	0x56, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65,
	0x73, 0x22, 0x65, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6a, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6e,
	0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0x77, 0x0a, 0x11, 0x49, 0x6e, 0x67, 0x65,
	0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x73, 0x22, 0x92, 0x01, 0x0a, 0x0b, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3f, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x69, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x49, 0x6e, 0x67, 0x65,
	0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x69,
	0x6e, 0x67, 0x65, 0x73, 0x74, 0x73, 0x22, 0xb3, 0x03, 0x0a, 0x08, 0x52, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x07, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73,
	0x12, 0x2e, 0x0a, 0x02, 0x63, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x43, 0x56, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x02, 0x63, 0x76,
	0x12, 0x3a, 0x0a, 0x06, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x22, 0x7d, 0x0a, 0x0a,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x67, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06,
	0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x12, 0x33, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x6f, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x15,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x65, 0x64, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22,
	0x44, 0x0a, 0x11, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x02, 0x74, 0x73, 0x2a, 0x56, 0x0a, 0x14, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0f, 0x0a,
	0x0b, 0x53, 0x50, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x53, 0x50, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x53, 0x50, 0x53, 0x5f, 0x42, 0x4f, 0x4f, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0d,
	0x0a, 0x09, 0x53, 0x50, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x03, 0x2a, 0xeb, 0x01,
	0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x42,
	0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c,
	0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x53, 0x5f, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x53,
	0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x10, 0x02, 0x12, 0x17,
	0x0a, 0x13, 0x42, 0x53, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x5f, 0x4c, 0x4f,
	0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x53, 0x5f, 0x42, 0x4f,
	0x4f, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10,
	0x42, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x53, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41,
	0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x42,
	0x53, 0x5f, 0x4c, 0x49, 0x46, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x53,
	0x5f, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x42,
	0x53, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x0e, 0x0a, 0x0a, 0x42,
	0x53, 0x5f, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x42, 0x59, 0x10, 0x0a, 0x32, 0x85, 0x01, 0x0a, 0x16,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x65, 0x64, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_robot_proto_rawDescOnce sync.Once
	file_frontend_proto_robot_proto_rawDescData = file_frontend_proto_robot_proto_rawDesc
)

func file_frontend_proto_robot_proto_rawDescGZIP() []byte {
	file_frontend_proto_robot_proto_rawDescOnce.Do(func() {
		file_frontend_proto_robot_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_robot_proto_rawDescData)
	})
	return file_frontend_proto_robot_proto_rawDescData
}

var file_frontend_proto_robot_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_proto_robot_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_frontend_proto_robot_proto_goTypes = []interface{}{
	(SoftwareProcessStage)(0),          // 0: carbon.frontend.robot.SoftwareProcessStage
	(BootStage)(0),                     // 1: carbon.frontend.robot.BootStage
	(*BoardState)(nil),                 // 2: carbon.frontend.robot.BoardState
	(*GPSCoord)(nil),                   // 3: carbon.frontend.robot.GPSCoord
	(*GPSBoardState)(nil),              // 4: carbon.frontend.robot.GPSBoardState
	(*WheelState)(nil),                 // 5: carbon.frontend.robot.WheelState
	(*WheelEncoderBoardState)(nil),     // 6: carbon.frontend.robot.WheelEncoderBoardState
	(*StrobeBoardState)(nil),           // 7: carbon.frontend.robot.StrobeBoardState
	(*ISOBusState)(nil),                // 8: carbon.frontend.robot.ISOBusState
	(*ChillerState)(nil),               // 9: carbon.frontend.robot.ChillerState
	(*AirConditionerState)(nil),        // 10: carbon.frontend.robot.AirConditionerState
	(*USBDriveState)(nil),              // 11: carbon.frontend.robot.USBDriveState
	(*SupervisoryPLCState)(nil),        // 12: carbon.frontend.robot.SupervisoryPLCState
	(*SafetyPLCState)(nil),             // 13: carbon.frontend.robot.SafetyPLCState
	(*NicState)(nil),                   // 14: carbon.frontend.robot.NicState
	(*StorageDriveState)(nil),          // 15: carbon.frontend.robot.StorageDriveState
	(*GPUState)(nil),                   // 16: carbon.frontend.robot.GPUState
	(*HostState)(nil),                  // 17: carbon.frontend.robot.HostState
	(*SoftwareUpdateVersionState)(nil), // 18: carbon.frontend.robot.SoftwareUpdateVersionState
	(*OperatingSystemState)(nil),       // 19: carbon.frontend.robot.OperatingSystemState
	(*SystemVersionState)(nil),         // 20: carbon.frontend.robot.SystemVersionState
	(*SoftwareVersionsState)(nil),      // 21: carbon.frontend.robot.SoftwareVersionsState
	(*SoftwareProcessState)(nil),       // 22: carbon.frontend.robot.SoftwareProcessState
	(*SoftwareState)(nil),              // 23: carbon.frontend.robot.SoftwareState
	(*DeepweedModelState)(nil),         // 24: carbon.frontend.robot.DeepweedModelState
	(*P2PModelState)(nil),              // 25: carbon.frontend.robot.P2PModelState
	(*ModelState)(nil),                 // 26: carbon.frontend.robot.ModelState
	(*RowModelState)(nil),              // 27: carbon.frontend.robot.RowModelState
	(*ModelManagerState)(nil),          // 28: carbon.frontend.robot.ModelManagerState
	(*ComputerState)(nil),              // 29: carbon.frontend.robot.ComputerState
	(*PowerTimeState)(nil),             // 30: carbon.frontend.robot.PowerTimeState
	(*VelocityState)(nil),              // 31: carbon.frontend.robot.VelocityState
	(*WeedingState)(nil),               // 32: carbon.frontend.robot.WeedingState
	(*GlobalState)(nil),                // 33: carbon.frontend.robot.GlobalState
	(*CameraState)(nil),                // 34: carbon.frontend.robot.CameraState
	(*MaxonPidState)(nil),              // 35: carbon.frontend.robot.MaxonPidState
	(*ServoState)(nil),                 // 36: carbon.frontend.robot.ServoState
	(*ScannerMetricState)(nil),         // 37: carbon.frontend.robot.ScannerMetricState
	(*ScannerLaserState)(nil),          // 38: carbon.frontend.robot.ScannerLaserState
	(*ScannerCrosshairState)(nil),      // 39: carbon.frontend.robot.ScannerCrosshairState
	(*ScannerLensState)(nil),           // 40: carbon.frontend.robot.ScannerLensState
	(*ScannerState)(nil),               // 41: carbon.frontend.robot.ScannerState
	(*CVNodeState)(nil),                // 42: carbon.frontend.robot.CVNodeState
	(*CVState)(nil),                    // 43: carbon.frontend.robot.CVState
	(*TrackerState)(nil),               // 44: carbon.frontend.robot.TrackerState
	(*IngestClientState)(nil),          // 45: carbon.frontend.robot.IngestClientState
	(*AimbotState)(nil),                // 46: carbon.frontend.robot.AimbotState
	(*RowState)(nil),                   // 47: carbon.frontend.robot.RowState
	(*RobotState)(nil),                 // 48: carbon.frontend.robot.RobotState
	(*TimestampedRobotState)(nil),      // 49: carbon.frontend.robot.TimestampedRobotState
	(*RobotStateRequest)(nil),          // 50: carbon.frontend.robot.RobotStateRequest
	nil,                                // 51: carbon.frontend.robot.HostState.NicsEntry
	nil,                                // 52: carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry
	nil,                                // 53: carbon.frontend.robot.SoftwareState.ProcessesEntry
	(*Timestamp)(nil),                  // 54: carbon.frontend.util.Timestamp
}
var file_frontend_proto_robot_proto_depIdxs = []int32{
	2,  // 0: carbon.frontend.robot.GPSBoardState.board:type_name -> carbon.frontend.robot.BoardState
	3,  // 1: carbon.frontend.robot.GPSBoardState.position:type_name -> carbon.frontend.robot.GPSCoord
	2,  // 2: carbon.frontend.robot.WheelEncoderBoardState.board:type_name -> carbon.frontend.robot.BoardState
	5,  // 3: carbon.frontend.robot.WheelEncoderBoardState.wheels:type_name -> carbon.frontend.robot.WheelState
	2,  // 4: carbon.frontend.robot.StrobeBoardState.board:type_name -> carbon.frontend.robot.BoardState
	51, // 5: carbon.frontend.robot.HostState.nics:type_name -> carbon.frontend.robot.HostState.NicsEntry
	15, // 6: carbon.frontend.robot.HostState.main_partition:type_name -> carbon.frontend.robot.StorageDriveState
	15, // 7: carbon.frontend.robot.HostState.data_partition:type_name -> carbon.frontend.robot.StorageDriveState
	16, // 8: carbon.frontend.robot.HostState.gpus:type_name -> carbon.frontend.robot.GPUState
	52, // 9: carbon.frontend.robot.SoftwareUpdateVersionState.images_required:type_name -> carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry
	19, // 10: carbon.frontend.robot.SystemVersionState.current:type_name -> carbon.frontend.robot.OperatingSystemState
	19, // 11: carbon.frontend.robot.SystemVersionState.other:type_name -> carbon.frontend.robot.OperatingSystemState
	18, // 12: carbon.frontend.robot.SoftwareVersionsState.current:type_name -> carbon.frontend.robot.SoftwareUpdateVersionState
	18, // 13: carbon.frontend.robot.SoftwareVersionsState.previous:type_name -> carbon.frontend.robot.SoftwareUpdateVersionState
	18, // 14: carbon.frontend.robot.SoftwareVersionsState.target:type_name -> carbon.frontend.robot.SoftwareUpdateVersionState
	20, // 15: carbon.frontend.robot.SoftwareVersionsState.system:type_name -> carbon.frontend.robot.SystemVersionState
	0,  // 16: carbon.frontend.robot.SoftwareProcessState.stage:type_name -> carbon.frontend.robot.SoftwareProcessStage
	53, // 17: carbon.frontend.robot.SoftwareState.processes:type_name -> carbon.frontend.robot.SoftwareState.ProcessesEntry
	21, // 18: carbon.frontend.robot.SoftwareState.versions:type_name -> carbon.frontend.robot.SoftwareVersionsState
	24, // 19: carbon.frontend.robot.ModelState.deepweed:type_name -> carbon.frontend.robot.DeepweedModelState
	25, // 20: carbon.frontend.robot.ModelState.p2p:type_name -> carbon.frontend.robot.P2PModelState
	26, // 21: carbon.frontend.robot.RowModelState.models:type_name -> carbon.frontend.robot.ModelState
	26, // 22: carbon.frontend.robot.ModelManagerState.local_models:type_name -> carbon.frontend.robot.ModelState
	27, // 23: carbon.frontend.robot.ModelManagerState.row_models:type_name -> carbon.frontend.robot.RowModelState
	23, // 24: carbon.frontend.robot.ComputerState.software:type_name -> carbon.frontend.robot.SoftwareState
	17, // 25: carbon.frontend.robot.ComputerState.host:type_name -> carbon.frontend.robot.HostState
	1,  // 26: carbon.frontend.robot.WeedingState.stage:type_name -> carbon.frontend.robot.BootStage
	31, // 27: carbon.frontend.robot.WeedingState.velocity:type_name -> carbon.frontend.robot.VelocityState
	29, // 28: carbon.frontend.robot.GlobalState.computer:type_name -> carbon.frontend.robot.ComputerState
	4,  // 29: carbon.frontend.robot.GlobalState.gps:type_name -> carbon.frontend.robot.GPSBoardState
	6,  // 30: carbon.frontend.robot.GlobalState.wheel:type_name -> carbon.frontend.robot.WheelEncoderBoardState
	7,  // 31: carbon.frontend.robot.GlobalState.strobe:type_name -> carbon.frontend.robot.StrobeBoardState
	12, // 32: carbon.frontend.robot.GlobalState.supervisory:type_name -> carbon.frontend.robot.SupervisoryPLCState
	13, // 33: carbon.frontend.robot.GlobalState.safety:type_name -> carbon.frontend.robot.SafetyPLCState
	9,  // 34: carbon.frontend.robot.GlobalState.chiller:type_name -> carbon.frontend.robot.ChillerState
	10, // 35: carbon.frontend.robot.GlobalState.air_conditioner:type_name -> carbon.frontend.robot.AirConditionerState
	8,  // 36: carbon.frontend.robot.GlobalState.isobus:type_name -> carbon.frontend.robot.ISOBusState
	30, // 37: carbon.frontend.robot.GlobalState.power_time:type_name -> carbon.frontend.robot.PowerTimeState
	28, // 38: carbon.frontend.robot.GlobalState.models:type_name -> carbon.frontend.robot.ModelManagerState
	32, // 39: carbon.frontend.robot.GlobalState.weeding:type_name -> carbon.frontend.robot.WeedingState
	11, // 40: carbon.frontend.robot.GlobalState.usb:type_name -> carbon.frontend.robot.USBDriveState
	35, // 41: carbon.frontend.robot.ServoState.maxon:type_name -> carbon.frontend.robot.MaxonPidState
	2,  // 42: carbon.frontend.robot.ScannerState.board:type_name -> carbon.frontend.robot.BoardState
	36, // 43: carbon.frontend.robot.ScannerState.pan:type_name -> carbon.frontend.robot.ServoState
	36, // 44: carbon.frontend.robot.ScannerState.tilt:type_name -> carbon.frontend.robot.ServoState
	39, // 45: carbon.frontend.robot.ScannerState.crosshair:type_name -> carbon.frontend.robot.ScannerCrosshairState
	40, // 46: carbon.frontend.robot.ScannerState.lens:type_name -> carbon.frontend.robot.ScannerLensState
	38, // 47: carbon.frontend.robot.ScannerState.laser:type_name -> carbon.frontend.robot.ScannerLaserState
	37, // 48: carbon.frontend.robot.ScannerState.metrics:type_name -> carbon.frontend.robot.ScannerMetricState
	42, // 49: carbon.frontend.robot.CVState.nodes:type_name -> carbon.frontend.robot.CVNodeState
	44, // 50: carbon.frontend.robot.AimbotState.trackers:type_name -> carbon.frontend.robot.TrackerState
	45, // 51: carbon.frontend.robot.AimbotState.ingests:type_name -> carbon.frontend.robot.IngestClientState
	29, // 52: carbon.frontend.robot.RowState.computer:type_name -> carbon.frontend.robot.ComputerState
	41, // 53: carbon.frontend.robot.RowState.scanners:type_name -> carbon.frontend.robot.ScannerState
	34, // 54: carbon.frontend.robot.RowState.predicts:type_name -> carbon.frontend.robot.CameraState
	34, // 55: carbon.frontend.robot.RowState.targets:type_name -> carbon.frontend.robot.CameraState
	34, // 56: carbon.frontend.robot.RowState.extras:type_name -> carbon.frontend.robot.CameraState
	43, // 57: carbon.frontend.robot.RowState.cv:type_name -> carbon.frontend.robot.CVState
	46, // 58: carbon.frontend.robot.RowState.aimbot:type_name -> carbon.frontend.robot.AimbotState
	33, // 59: carbon.frontend.robot.RobotState.global:type_name -> carbon.frontend.robot.GlobalState
	47, // 60: carbon.frontend.robot.RobotState.rows:type_name -> carbon.frontend.robot.RowState
	54, // 61: carbon.frontend.robot.TimestampedRobotState.ts:type_name -> carbon.frontend.util.Timestamp
	48, // 62: carbon.frontend.robot.TimestampedRobotState.state:type_name -> carbon.frontend.robot.RobotState
	54, // 63: carbon.frontend.robot.RobotStateRequest.ts:type_name -> carbon.frontend.util.Timestamp
	14, // 64: carbon.frontend.robot.HostState.NicsEntry.value:type_name -> carbon.frontend.robot.NicState
	22, // 65: carbon.frontend.robot.SoftwareState.ProcessesEntry.value:type_name -> carbon.frontend.robot.SoftwareProcessState
	50, // 66: carbon.frontend.robot.RobotDiagnosticService.GetNextRobotState:input_type -> carbon.frontend.robot.RobotStateRequest
	49, // 67: carbon.frontend.robot.RobotDiagnosticService.GetNextRobotState:output_type -> carbon.frontend.robot.TimestampedRobotState
	67, // [67:68] is the sub-list for method output_type
	66, // [66:67] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_frontend_proto_robot_proto_init() }
func file_frontend_proto_robot_proto_init() {
	if File_frontend_proto_robot_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_robot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPSCoord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPSBoardState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WheelState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WheelEncoderBoardState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrobeBoardState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ISOBusState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChillerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AirConditionerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*USBDriveState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupervisoryPLCState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafetyPLCState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NicState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StorageDriveState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GPUState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareUpdateVersionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperatingSystemState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemVersionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersionsState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareProcessState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeepweedModelState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PModelState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowModelState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelManagerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerTimeState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VelocityState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedingState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaxonPidState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerMetricState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerLaserState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerCrosshairState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerLensState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CVNodeState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CVState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IngestClientState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AimbotState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimestampedRobotState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_robot_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_robot_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*ModelState_Deepweed)(nil),
		(*ModelState_P2P)(nil),
	}
	file_frontend_proto_robot_proto_msgTypes[34].OneofWrappers = []interface{}{
		(*ServoState_Maxon)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_robot_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_robot_proto_goTypes,
		DependencyIndexes: file_frontend_proto_robot_proto_depIdxs,
		EnumInfos:         file_frontend_proto_robot_proto_enumTypes,
		MessageInfos:      file_frontend_proto_robot_proto_msgTypes,
	}.Build()
	File_frontend_proto_robot_proto = out.File
	file_frontend_proto_robot_proto_rawDesc = nil
	file_frontend_proto_robot_proto_goTypes = nil
	file_frontend_proto_robot_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/calibration.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CalibrationService_StartColorCalibration_FullMethodName = "/carbon.frontend.color_calibration.CalibrationService/StartColorCalibration"
	CalibrationService_SaveColorCalibration_FullMethodName  = "/carbon.frontend.color_calibration.CalibrationService/SaveColorCalibration"
)

// CalibrationServiceClient is the client API for CalibrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CalibrationServiceClient interface {
	StartColorCalibration(ctx context.Context, in *CameraRequest, opts ...grpc.CallOption) (*ColorCalibrationValues, error)
	SaveColorCalibration(ctx context.Context, in *ColorCalibrationValues, opts ...grpc.CallOption) (*Empty, error)
}

type calibrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCalibrationServiceClient(cc grpc.ClientConnInterface) CalibrationServiceClient {
	return &calibrationServiceClient{cc}
}

func (c *calibrationServiceClient) StartColorCalibration(ctx context.Context, in *CameraRequest, opts ...grpc.CallOption) (*ColorCalibrationValues, error) {
	out := new(ColorCalibrationValues)
	err := c.cc.Invoke(ctx, CalibrationService_StartColorCalibration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *calibrationServiceClient) SaveColorCalibration(ctx context.Context, in *ColorCalibrationValues, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CalibrationService_SaveColorCalibration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CalibrationServiceServer is the server API for CalibrationService service.
// All implementations must embed UnimplementedCalibrationServiceServer
// for forward compatibility
type CalibrationServiceServer interface {
	StartColorCalibration(context.Context, *CameraRequest) (*ColorCalibrationValues, error)
	SaveColorCalibration(context.Context, *ColorCalibrationValues) (*Empty, error)
	mustEmbedUnimplementedCalibrationServiceServer()
}

// UnimplementedCalibrationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCalibrationServiceServer struct {
}

func (UnimplementedCalibrationServiceServer) StartColorCalibration(context.Context, *CameraRequest) (*ColorCalibrationValues, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartColorCalibration not implemented")
}
func (UnimplementedCalibrationServiceServer) SaveColorCalibration(context.Context, *ColorCalibrationValues) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveColorCalibration not implemented")
}
func (UnimplementedCalibrationServiceServer) mustEmbedUnimplementedCalibrationServiceServer() {}

// UnsafeCalibrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CalibrationServiceServer will
// result in compilation errors.
type UnsafeCalibrationServiceServer interface {
	mustEmbedUnimplementedCalibrationServiceServer()
}

func RegisterCalibrationServiceServer(s grpc.ServiceRegistrar, srv CalibrationServiceServer) {
	s.RegisterService(&CalibrationService_ServiceDesc, srv)
}

func _CalibrationService_StartColorCalibration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CameraRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalibrationServiceServer).StartColorCalibration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CalibrationService_StartColorCalibration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalibrationServiceServer).StartColorCalibration(ctx, req.(*CameraRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CalibrationService_SaveColorCalibration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ColorCalibrationValues)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalibrationServiceServer).SaveColorCalibration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CalibrationService_SaveColorCalibration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalibrationServiceServer).SaveColorCalibration(ctx, req.(*ColorCalibrationValues))
	}
	return interceptor(ctx, in, info, handler)
}

// CalibrationService_ServiceDesc is the grpc.ServiceDesc for CalibrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CalibrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.color_calibration.CalibrationService",
	HandlerType: (*CalibrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartColorCalibration",
			Handler:    _CalibrationService_StartColorCalibration_Handler,
		},
		{
			MethodName: "SaveColorCalibration",
			Handler:    _CalibrationService_SaveColorCalibration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/calibration.proto",
}

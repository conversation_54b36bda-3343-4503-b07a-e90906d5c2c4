// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/debug.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DebugService_GetRobot_FullMethodName                           = "/carbon.frontend.debug.DebugService/GetRobot"
	DebugService_SetLogLevel_FullMethodName                        = "/carbon.frontend.debug.DebugService/SetLogLevel"
	DebugService_StartSavingCropLineDetectionReplay_FullMethodName = "/carbon.frontend.debug.DebugService/StartSavingCropLineDetectionReplay"
	DebugService_StartRecordingAimbotInputs_FullMethodName         = "/carbon.frontend.debug.DebugService/StartRecordingAimbotInputs"
	DebugService_AddMockSpatialMetricsBlock_FullMethodName         = "/carbon.frontend.debug.DebugService/AddMockSpatialMetricsBlock"
	DebugService_DeleteProfileSyncData_FullMethodName              = "/carbon.frontend.debug.DebugService/DeleteProfileSyncData"
)

// DebugServiceClient is the client API for DebugService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DebugServiceClient interface {
	GetRobot(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*RobotMessage, error)
	SetLogLevel(ctx context.Context, in *SetLogLevelRequest, opts ...grpc.CallOption) (*Empty, error)
	StartSavingCropLineDetectionReplay(ctx context.Context, in *weed_tracking.StartSavingCropLineDetectionReplayRequest, opts ...grpc.CallOption) (*weed_tracking.Empty, error)
	StartRecordingAimbotInputs(ctx context.Context, in *weed_tracking.RecordAimbotInputRequest, opts ...grpc.CallOption) (*weed_tracking.Empty, error)
	AddMockSpatialMetricsBlock(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	DeleteProfileSyncData(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type debugServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDebugServiceClient(cc grpc.ClientConnInterface) DebugServiceClient {
	return &debugServiceClient{cc}
}

func (c *debugServiceClient) GetRobot(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*RobotMessage, error) {
	out := new(RobotMessage)
	err := c.cc.Invoke(ctx, DebugService_GetRobot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugServiceClient) SetLogLevel(ctx context.Context, in *SetLogLevelRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DebugService_SetLogLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugServiceClient) StartSavingCropLineDetectionReplay(ctx context.Context, in *weed_tracking.StartSavingCropLineDetectionReplayRequest, opts ...grpc.CallOption) (*weed_tracking.Empty, error) {
	out := new(weed_tracking.Empty)
	err := c.cc.Invoke(ctx, DebugService_StartSavingCropLineDetectionReplay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugServiceClient) StartRecordingAimbotInputs(ctx context.Context, in *weed_tracking.RecordAimbotInputRequest, opts ...grpc.CallOption) (*weed_tracking.Empty, error) {
	out := new(weed_tracking.Empty)
	err := c.cc.Invoke(ctx, DebugService_StartRecordingAimbotInputs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugServiceClient) AddMockSpatialMetricsBlock(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DebugService_AddMockSpatialMetricsBlock_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugServiceClient) DeleteProfileSyncData(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DebugService_DeleteProfileSyncData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DebugServiceServer is the server API for DebugService service.
// All implementations must embed UnimplementedDebugServiceServer
// for forward compatibility
type DebugServiceServer interface {
	GetRobot(context.Context, *Empty) (*RobotMessage, error)
	SetLogLevel(context.Context, *SetLogLevelRequest) (*Empty, error)
	StartSavingCropLineDetectionReplay(context.Context, *weed_tracking.StartSavingCropLineDetectionReplayRequest) (*weed_tracking.Empty, error)
	StartRecordingAimbotInputs(context.Context, *weed_tracking.RecordAimbotInputRequest) (*weed_tracking.Empty, error)
	AddMockSpatialMetricsBlock(context.Context, *Empty) (*Empty, error)
	DeleteProfileSyncData(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedDebugServiceServer()
}

// UnimplementedDebugServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDebugServiceServer struct {
}

func (UnimplementedDebugServiceServer) GetRobot(context.Context, *Empty) (*RobotMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRobot not implemented")
}
func (UnimplementedDebugServiceServer) SetLogLevel(context.Context, *SetLogLevelRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLogLevel not implemented")
}
func (UnimplementedDebugServiceServer) StartSavingCropLineDetectionReplay(context.Context, *weed_tracking.StartSavingCropLineDetectionReplayRequest) (*weed_tracking.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartSavingCropLineDetectionReplay not implemented")
}
func (UnimplementedDebugServiceServer) StartRecordingAimbotInputs(context.Context, *weed_tracking.RecordAimbotInputRequest) (*weed_tracking.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartRecordingAimbotInputs not implemented")
}
func (UnimplementedDebugServiceServer) AddMockSpatialMetricsBlock(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMockSpatialMetricsBlock not implemented")
}
func (UnimplementedDebugServiceServer) DeleteProfileSyncData(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProfileSyncData not implemented")
}
func (UnimplementedDebugServiceServer) mustEmbedUnimplementedDebugServiceServer() {}

// UnsafeDebugServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DebugServiceServer will
// result in compilation errors.
type UnsafeDebugServiceServer interface {
	mustEmbedUnimplementedDebugServiceServer()
}

func RegisterDebugServiceServer(s grpc.ServiceRegistrar, srv DebugServiceServer) {
	s.RegisterService(&DebugService_ServiceDesc, srv)
}

func _DebugService_GetRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServiceServer).GetRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebugService_GetRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServiceServer).GetRobot(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DebugService_SetLogLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLogLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServiceServer).SetLogLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebugService_SetLogLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServiceServer).SetLogLevel(ctx, req.(*SetLogLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DebugService_StartSavingCropLineDetectionReplay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(weed_tracking.StartSavingCropLineDetectionReplayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServiceServer).StartSavingCropLineDetectionReplay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebugService_StartSavingCropLineDetectionReplay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServiceServer).StartSavingCropLineDetectionReplay(ctx, req.(*weed_tracking.StartSavingCropLineDetectionReplayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DebugService_StartRecordingAimbotInputs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(weed_tracking.RecordAimbotInputRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServiceServer).StartRecordingAimbotInputs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebugService_StartRecordingAimbotInputs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServiceServer).StartRecordingAimbotInputs(ctx, req.(*weed_tracking.RecordAimbotInputRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DebugService_AddMockSpatialMetricsBlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServiceServer).AddMockSpatialMetricsBlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebugService_AddMockSpatialMetricsBlock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServiceServer).AddMockSpatialMetricsBlock(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DebugService_DeleteProfileSyncData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServiceServer).DeleteProfileSyncData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DebugService_DeleteProfileSyncData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServiceServer).DeleteProfileSyncData(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// DebugService_ServiceDesc is the grpc.ServiceDesc for DebugService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DebugService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.debug.DebugService",
	HandlerType: (*DebugServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRobot",
			Handler:    _DebugService_GetRobot_Handler,
		},
		{
			MethodName: "SetLogLevel",
			Handler:    _DebugService_SetLogLevel_Handler,
		},
		{
			MethodName: "StartSavingCropLineDetectionReplay",
			Handler:    _DebugService_StartSavingCropLineDetectionReplay_Handler,
		},
		{
			MethodName: "StartRecordingAimbotInputs",
			Handler:    _DebugService_StartRecordingAimbotInputs_Handler,
		},
		{
			MethodName: "AddMockSpatialMetricsBlock",
			Handler:    _DebugService_AddMockSpatialMetricsBlock_Handler,
		},
		{
			MethodName: "DeleteProfileSyncData",
			Handler:    _DebugService_DeleteProfileSyncData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/debug.proto",
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/data_capture.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DataCaptureService_StartDataCapture_FullMethodName                         = "/carbon.frontend.data_capture.DataCaptureService/StartDataCapture"
	DataCaptureService_PauseDataCapture_FullMethodName                         = "/carbon.frontend.data_capture.DataCaptureService/PauseDataCapture"
	DataCaptureService_StopDataCapture_FullMethodName                          = "/carbon.frontend.data_capture.DataCaptureService/StopDataCapture"
	DataCaptureService_ResumeDataCapture_FullMethodName                        = "/carbon.frontend.data_capture.DataCaptureService/ResumeDataCapture"
	DataCaptureService_CompleteDataCapture_FullMethodName                      = "/carbon.frontend.data_capture.DataCaptureService/CompleteDataCapture"
	DataCaptureService_StartDataCaptureWirelessUpload_FullMethodName           = "/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureWirelessUpload"
	DataCaptureService_StartDataCaptureUSBUpload_FullMethodName                = "/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureUSBUpload"
	DataCaptureService_StopDataCaptureUpload_FullMethodName                    = "/carbon.frontend.data_capture.DataCaptureService/StopDataCaptureUpload"
	DataCaptureService_PauseDataCaptureUpload_FullMethodName                   = "/carbon.frontend.data_capture.DataCaptureService/PauseDataCaptureUpload"
	DataCaptureService_ResumeDataCaptureUpload_FullMethodName                  = "/carbon.frontend.data_capture.DataCaptureService/ResumeDataCaptureUpload"
	DataCaptureService_StartBackgroundDataCaptureWirelessUpload_FullMethodName = "/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureWirelessUpload"
	DataCaptureService_StartBackgroundDataCaptureUSBUpload_FullMethodName      = "/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureUSBUpload"
	DataCaptureService_StopBackgroundDataCaptureUpload_FullMethodName          = "/carbon.frontend.data_capture.DataCaptureService/StopBackgroundDataCaptureUpload"
	DataCaptureService_PauseBackgroundDataCaptureUpload_FullMethodName         = "/carbon.frontend.data_capture.DataCaptureService/PauseBackgroundDataCaptureUpload"
	DataCaptureService_ResumeBackgroundDataCaptureUpload_FullMethodName        = "/carbon.frontend.data_capture.DataCaptureService/ResumeBackgroundDataCaptureUpload"
	DataCaptureService_GetNextDataCaptureState_FullMethodName                  = "/carbon.frontend.data_capture.DataCaptureService/GetNextDataCaptureState"
	DataCaptureService_SnapImages_FullMethodName                               = "/carbon.frontend.data_capture.DataCaptureService/SnapImages"
	DataCaptureService_GetSessions_FullMethodName                              = "/carbon.frontend.data_capture.DataCaptureService/GetSessions"
	DataCaptureService_GetRegularCaptureStatus_FullMethodName                  = "/carbon.frontend.data_capture.DataCaptureService/GetRegularCaptureStatus"
)

// DataCaptureServiceClient is the client API for DataCaptureService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataCaptureServiceClient interface {
	StartDataCapture(ctx context.Context, in *StartDataCaptureRequest, opts ...grpc.CallOption) (*Empty, error)
	PauseDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StopDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	ResumeDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	CompleteDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StartDataCaptureWirelessUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StartDataCaptureUSBUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StopDataCaptureUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	PauseDataCaptureUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	ResumeDataCaptureUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StartBackgroundDataCaptureWirelessUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error)
	StartBackgroundDataCaptureUSBUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error)
	StopBackgroundDataCaptureUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error)
	PauseBackgroundDataCaptureUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error)
	ResumeBackgroundDataCaptureUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error)
	GetNextDataCaptureState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*DataCaptureState, error)
	SnapImages(ctx context.Context, in *SnapImagesRequest, opts ...grpc.CallOption) (*Empty, error)
	GetSessions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AvailableSessionResponse, error)
	GetRegularCaptureStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*RegularCaptureStatus, error)
}

type dataCaptureServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataCaptureServiceClient(cc grpc.ClientConnInterface) DataCaptureServiceClient {
	return &dataCaptureServiceClient{cc}
}

func (c *dataCaptureServiceClient) StartDataCapture(ctx context.Context, in *StartDataCaptureRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StartDataCapture_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) PauseDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_PauseDataCapture_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StopDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StopDataCapture_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) ResumeDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_ResumeDataCapture_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) CompleteDataCapture(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_CompleteDataCapture_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StartDataCaptureWirelessUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StartDataCaptureWirelessUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StartDataCaptureUSBUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StartDataCaptureUSBUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StopDataCaptureUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StopDataCaptureUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) PauseDataCaptureUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_PauseDataCaptureUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) ResumeDataCaptureUpload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_ResumeDataCaptureUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StartBackgroundDataCaptureWirelessUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StartBackgroundDataCaptureWirelessUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StartBackgroundDataCaptureUSBUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StartBackgroundDataCaptureUSBUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) StopBackgroundDataCaptureUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_StopBackgroundDataCaptureUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) PauseBackgroundDataCaptureUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_PauseBackgroundDataCaptureUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) ResumeBackgroundDataCaptureUpload(ctx context.Context, in *SessionName, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_ResumeBackgroundDataCaptureUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) GetNextDataCaptureState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*DataCaptureState, error) {
	out := new(DataCaptureState)
	err := c.cc.Invoke(ctx, DataCaptureService_GetNextDataCaptureState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) SnapImages(ctx context.Context, in *SnapImagesRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataCaptureService_SnapImages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) GetSessions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AvailableSessionResponse, error) {
	out := new(AvailableSessionResponse)
	err := c.cc.Invoke(ctx, DataCaptureService_GetSessions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataCaptureServiceClient) GetRegularCaptureStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*RegularCaptureStatus, error) {
	out := new(RegularCaptureStatus)
	err := c.cc.Invoke(ctx, DataCaptureService_GetRegularCaptureStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataCaptureServiceServer is the server API for DataCaptureService service.
// All implementations must embed UnimplementedDataCaptureServiceServer
// for forward compatibility
type DataCaptureServiceServer interface {
	StartDataCapture(context.Context, *StartDataCaptureRequest) (*Empty, error)
	PauseDataCapture(context.Context, *Empty) (*Empty, error)
	StopDataCapture(context.Context, *Empty) (*Empty, error)
	ResumeDataCapture(context.Context, *Empty) (*Empty, error)
	CompleteDataCapture(context.Context, *Empty) (*Empty, error)
	StartDataCaptureWirelessUpload(context.Context, *Empty) (*Empty, error)
	StartDataCaptureUSBUpload(context.Context, *Empty) (*Empty, error)
	StopDataCaptureUpload(context.Context, *Empty) (*Empty, error)
	PauseDataCaptureUpload(context.Context, *Empty) (*Empty, error)
	ResumeDataCaptureUpload(context.Context, *Empty) (*Empty, error)
	StartBackgroundDataCaptureWirelessUpload(context.Context, *SessionName) (*Empty, error)
	StartBackgroundDataCaptureUSBUpload(context.Context, *SessionName) (*Empty, error)
	StopBackgroundDataCaptureUpload(context.Context, *SessionName) (*Empty, error)
	PauseBackgroundDataCaptureUpload(context.Context, *SessionName) (*Empty, error)
	ResumeBackgroundDataCaptureUpload(context.Context, *SessionName) (*Empty, error)
	GetNextDataCaptureState(context.Context, *Timestamp) (*DataCaptureState, error)
	SnapImages(context.Context, *SnapImagesRequest) (*Empty, error)
	GetSessions(context.Context, *Empty) (*AvailableSessionResponse, error)
	GetRegularCaptureStatus(context.Context, *Empty) (*RegularCaptureStatus, error)
	mustEmbedUnimplementedDataCaptureServiceServer()
}

// UnimplementedDataCaptureServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDataCaptureServiceServer struct {
}

func (UnimplementedDataCaptureServiceServer) StartDataCapture(context.Context, *StartDataCaptureRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDataCapture not implemented")
}
func (UnimplementedDataCaptureServiceServer) PauseDataCapture(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseDataCapture not implemented")
}
func (UnimplementedDataCaptureServiceServer) StopDataCapture(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDataCapture not implemented")
}
func (UnimplementedDataCaptureServiceServer) ResumeDataCapture(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeDataCapture not implemented")
}
func (UnimplementedDataCaptureServiceServer) CompleteDataCapture(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteDataCapture not implemented")
}
func (UnimplementedDataCaptureServiceServer) StartDataCaptureWirelessUpload(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDataCaptureWirelessUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) StartDataCaptureUSBUpload(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDataCaptureUSBUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) StopDataCaptureUpload(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDataCaptureUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) PauseDataCaptureUpload(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseDataCaptureUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) ResumeDataCaptureUpload(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeDataCaptureUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) StartBackgroundDataCaptureWirelessUpload(context.Context, *SessionName) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBackgroundDataCaptureWirelessUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) StartBackgroundDataCaptureUSBUpload(context.Context, *SessionName) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBackgroundDataCaptureUSBUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) StopBackgroundDataCaptureUpload(context.Context, *SessionName) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopBackgroundDataCaptureUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) PauseBackgroundDataCaptureUpload(context.Context, *SessionName) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseBackgroundDataCaptureUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) ResumeBackgroundDataCaptureUpload(context.Context, *SessionName) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeBackgroundDataCaptureUpload not implemented")
}
func (UnimplementedDataCaptureServiceServer) GetNextDataCaptureState(context.Context, *Timestamp) (*DataCaptureState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextDataCaptureState not implemented")
}
func (UnimplementedDataCaptureServiceServer) SnapImages(context.Context, *SnapImagesRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SnapImages not implemented")
}
func (UnimplementedDataCaptureServiceServer) GetSessions(context.Context, *Empty) (*AvailableSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessions not implemented")
}
func (UnimplementedDataCaptureServiceServer) GetRegularCaptureStatus(context.Context, *Empty) (*RegularCaptureStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegularCaptureStatus not implemented")
}
func (UnimplementedDataCaptureServiceServer) mustEmbedUnimplementedDataCaptureServiceServer() {}

// UnsafeDataCaptureServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataCaptureServiceServer will
// result in compilation errors.
type UnsafeDataCaptureServiceServer interface {
	mustEmbedUnimplementedDataCaptureServiceServer()
}

func RegisterDataCaptureServiceServer(s grpc.ServiceRegistrar, srv DataCaptureServiceServer) {
	s.RegisterService(&DataCaptureService_ServiceDesc, srv)
}

func _DataCaptureService_StartDataCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDataCaptureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StartDataCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StartDataCapture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StartDataCapture(ctx, req.(*StartDataCaptureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_PauseDataCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).PauseDataCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_PauseDataCapture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).PauseDataCapture(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StopDataCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StopDataCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StopDataCapture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StopDataCapture(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_ResumeDataCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).ResumeDataCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_ResumeDataCapture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).ResumeDataCapture(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_CompleteDataCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).CompleteDataCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_CompleteDataCapture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).CompleteDataCapture(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StartDataCaptureWirelessUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StartDataCaptureWirelessUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StartDataCaptureWirelessUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StartDataCaptureWirelessUpload(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StartDataCaptureUSBUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StartDataCaptureUSBUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StartDataCaptureUSBUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StartDataCaptureUSBUpload(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StopDataCaptureUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StopDataCaptureUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StopDataCaptureUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StopDataCaptureUpload(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_PauseDataCaptureUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).PauseDataCaptureUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_PauseDataCaptureUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).PauseDataCaptureUpload(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_ResumeDataCaptureUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).ResumeDataCaptureUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_ResumeDataCaptureUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).ResumeDataCaptureUpload(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StartBackgroundDataCaptureWirelessUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StartBackgroundDataCaptureWirelessUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StartBackgroundDataCaptureWirelessUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StartBackgroundDataCaptureWirelessUpload(ctx, req.(*SessionName))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StartBackgroundDataCaptureUSBUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StartBackgroundDataCaptureUSBUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StartBackgroundDataCaptureUSBUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StartBackgroundDataCaptureUSBUpload(ctx, req.(*SessionName))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_StopBackgroundDataCaptureUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).StopBackgroundDataCaptureUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_StopBackgroundDataCaptureUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).StopBackgroundDataCaptureUpload(ctx, req.(*SessionName))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_PauseBackgroundDataCaptureUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).PauseBackgroundDataCaptureUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_PauseBackgroundDataCaptureUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).PauseBackgroundDataCaptureUpload(ctx, req.(*SessionName))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_ResumeBackgroundDataCaptureUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SessionName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).ResumeBackgroundDataCaptureUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_ResumeBackgroundDataCaptureUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).ResumeBackgroundDataCaptureUpload(ctx, req.(*SessionName))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_GetNextDataCaptureState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).GetNextDataCaptureState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_GetNextDataCaptureState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).GetNextDataCaptureState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_SnapImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SnapImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).SnapImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_SnapImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).SnapImages(ctx, req.(*SnapImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_GetSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).GetSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_GetSessions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).GetSessions(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataCaptureService_GetRegularCaptureStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataCaptureServiceServer).GetRegularCaptureStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataCaptureService_GetRegularCaptureStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataCaptureServiceServer).GetRegularCaptureStatus(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// DataCaptureService_ServiceDesc is the grpc.ServiceDesc for DataCaptureService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataCaptureService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.data_capture.DataCaptureService",
	HandlerType: (*DataCaptureServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartDataCapture",
			Handler:    _DataCaptureService_StartDataCapture_Handler,
		},
		{
			MethodName: "PauseDataCapture",
			Handler:    _DataCaptureService_PauseDataCapture_Handler,
		},
		{
			MethodName: "StopDataCapture",
			Handler:    _DataCaptureService_StopDataCapture_Handler,
		},
		{
			MethodName: "ResumeDataCapture",
			Handler:    _DataCaptureService_ResumeDataCapture_Handler,
		},
		{
			MethodName: "CompleteDataCapture",
			Handler:    _DataCaptureService_CompleteDataCapture_Handler,
		},
		{
			MethodName: "StartDataCaptureWirelessUpload",
			Handler:    _DataCaptureService_StartDataCaptureWirelessUpload_Handler,
		},
		{
			MethodName: "StartDataCaptureUSBUpload",
			Handler:    _DataCaptureService_StartDataCaptureUSBUpload_Handler,
		},
		{
			MethodName: "StopDataCaptureUpload",
			Handler:    _DataCaptureService_StopDataCaptureUpload_Handler,
		},
		{
			MethodName: "PauseDataCaptureUpload",
			Handler:    _DataCaptureService_PauseDataCaptureUpload_Handler,
		},
		{
			MethodName: "ResumeDataCaptureUpload",
			Handler:    _DataCaptureService_ResumeDataCaptureUpload_Handler,
		},
		{
			MethodName: "StartBackgroundDataCaptureWirelessUpload",
			Handler:    _DataCaptureService_StartBackgroundDataCaptureWirelessUpload_Handler,
		},
		{
			MethodName: "StartBackgroundDataCaptureUSBUpload",
			Handler:    _DataCaptureService_StartBackgroundDataCaptureUSBUpload_Handler,
		},
		{
			MethodName: "StopBackgroundDataCaptureUpload",
			Handler:    _DataCaptureService_StopBackgroundDataCaptureUpload_Handler,
		},
		{
			MethodName: "PauseBackgroundDataCaptureUpload",
			Handler:    _DataCaptureService_PauseBackgroundDataCaptureUpload_Handler,
		},
		{
			MethodName: "ResumeBackgroundDataCaptureUpload",
			Handler:    _DataCaptureService_ResumeBackgroundDataCaptureUpload_Handler,
		},
		{
			MethodName: "GetNextDataCaptureState",
			Handler:    _DataCaptureService_GetNextDataCaptureState_Handler,
		},
		{
			MethodName: "SnapImages",
			Handler:    _DataCaptureService_SnapImages_Handler,
		},
		{
			MethodName: "GetSessions",
			Handler:    _DataCaptureService_GetSessions_Handler,
		},
		{
			MethodName: "GetRegularCaptureStatus",
			Handler:    _DataCaptureService_GetRegularCaptureStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/data_capture.proto",
}

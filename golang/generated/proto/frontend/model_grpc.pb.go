// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/model.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelService_PinModel_FullMethodName                      = "/carbon.frontend.model.ModelService/PinModel"
	ModelService_UnpinModel_FullMethodName                    = "/carbon.frontend.model.ModelService/UnpinModel"
	ModelService_GetNextModelState_FullMethodName             = "/carbon.frontend.model.ModelService/GetNextModelState"
	ModelService_GetNextAllModelState_FullMethodName          = "/carbon.frontend.model.ModelService/GetNextAllModelState"
	ModelService_UpdateModel_FullMethodName                   = "/carbon.frontend.model.ModelService/UpdateModel"
	ModelService_ListEnabledCrops_FullMethodName              = "/carbon.frontend.model.ModelService/ListEnabledCrops"
	ModelService_GetNextEnabledCrops_FullMethodName           = "/carbon.frontend.model.ModelService/GetNextEnabledCrops"
	ModelService_ListCaptureCrops_FullMethodName              = "/carbon.frontend.model.ModelService/ListCaptureCrops"
	ModelService_GetNextCaptureCrops_FullMethodName           = "/carbon.frontend.model.ModelService/GetNextCaptureCrops"
	ModelService_GetNextSelectedCropID_FullMethodName         = "/carbon.frontend.model.ModelService/GetNextSelectedCropID"
	ModelService_SelectCrop_FullMethodName                    = "/carbon.frontend.model.ModelService/SelectCrop"
	ModelService_DownloadModel_FullMethodName                 = "/carbon.frontend.model.ModelService/DownloadModel"
	ModelService_GetNextModelHistory_FullMethodName           = "/carbon.frontend.model.ModelService/GetNextModelHistory"
	ModelService_GetModelHistory_FullMethodName               = "/carbon.frontend.model.ModelService/GetModelHistory"
	ModelService_GetModelNicknames_FullMethodName             = "/carbon.frontend.model.ModelService/GetModelNicknames"
	ModelService_GetNextModelNicknames_FullMethodName         = "/carbon.frontend.model.ModelService/GetNextModelNicknames"
	ModelService_SetModelNickname_FullMethodName              = "/carbon.frontend.model.ModelService/SetModelNickname"
	ModelService_RefreshDefaultModelParameters_FullMethodName = "/carbon.frontend.model.ModelService/RefreshDefaultModelParameters"
	ModelService_SyncCropIDs_FullMethodName                   = "/carbon.frontend.model.ModelService/SyncCropIDs"
	ModelService_TriggerDownload_FullMethodName               = "/carbon.frontend.model.ModelService/TriggerDownload"
)

// ModelServiceClient is the client API for ModelService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelServiceClient interface {
	PinModel(ctx context.Context, in *PinModelRequest, opts ...grpc.CallOption) (*Empty, error)
	UnpinModel(ctx context.Context, in *UnpinModelRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextModelState(ctx context.Context, in *GetNextModelStateRequest, opts ...grpc.CallOption) (*GetNextModelStateResponse, error)
	GetNextAllModelState(ctx context.Context, in *GetNextModelStateRequest, opts ...grpc.CallOption) (*GetNextModelStateResponse, error)
	UpdateModel(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	ListEnabledCrops(ctx context.Context, in *ListCropParameters, opts ...grpc.CallOption) (*EnabledCropList, error)
	GetNextEnabledCrops(ctx context.Context, in *GetNextEnabledCropsRequest, opts ...grpc.CallOption) (*GetNextEnabledCropsResponse, error)
	ListCaptureCrops(ctx context.Context, in *ListCropParameters, opts ...grpc.CallOption) (*EnabledCropList, error)
	GetNextCaptureCrops(ctx context.Context, in *GetNextCaptureCropsRequest, opts ...grpc.CallOption) (*GetNextCaptureCropsResponse, error)
	GetNextSelectedCropID(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextSelectedCropIDResponse, error)
	SelectCrop(ctx context.Context, in *SelectCropRequest, opts ...grpc.CallOption) (*Empty, error)
	DownloadModel(ctx context.Context, in *DownloadModelRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextModelHistory(ctx context.Context, in *ModelHistoryRequest, opts ...grpc.CallOption) (*ModelHistoryResponse, error)
	GetModelHistory(ctx context.Context, in *ModelHistoryRequest, opts ...grpc.CallOption) (*ModelHistoryResponse, error)
	GetModelNicknames(ctx context.Context, in *GetModelNicknamesRequest, opts ...grpc.CallOption) (*GetModelNicknamesResponse, error)
	GetNextModelNicknames(ctx context.Context, in *GetModelNicknamesRequest, opts ...grpc.CallOption) (*GetModelNicknamesResponse, error)
	SetModelNickname(ctx context.Context, in *SetModelNicknameRequest, opts ...grpc.CallOption) (*Empty, error)
	RefreshDefaultModelParameters(ctx context.Context, in *RefreshDefaultModelParametersRequest, opts ...grpc.CallOption) (*Empty, error)
	SyncCropIDs(ctx context.Context, in *SyncCropIDsRequest, opts ...grpc.CallOption) (*Empty, error)
	TriggerDownload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type modelServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModelServiceClient(cc grpc.ClientConnInterface) ModelServiceClient {
	return &modelServiceClient{cc}
}

func (c *modelServiceClient) PinModel(ctx context.Context, in *PinModelRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_PinModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) UnpinModel(ctx context.Context, in *UnpinModelRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_UnpinModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextModelState(ctx context.Context, in *GetNextModelStateRequest, opts ...grpc.CallOption) (*GetNextModelStateResponse, error) {
	out := new(GetNextModelStateResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextModelState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextAllModelState(ctx context.Context, in *GetNextModelStateRequest, opts ...grpc.CallOption) (*GetNextModelStateResponse, error) {
	out := new(GetNextModelStateResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextAllModelState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) UpdateModel(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_UpdateModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) ListEnabledCrops(ctx context.Context, in *ListCropParameters, opts ...grpc.CallOption) (*EnabledCropList, error) {
	out := new(EnabledCropList)
	err := c.cc.Invoke(ctx, ModelService_ListEnabledCrops_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextEnabledCrops(ctx context.Context, in *GetNextEnabledCropsRequest, opts ...grpc.CallOption) (*GetNextEnabledCropsResponse, error) {
	out := new(GetNextEnabledCropsResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextEnabledCrops_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) ListCaptureCrops(ctx context.Context, in *ListCropParameters, opts ...grpc.CallOption) (*EnabledCropList, error) {
	out := new(EnabledCropList)
	err := c.cc.Invoke(ctx, ModelService_ListCaptureCrops_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextCaptureCrops(ctx context.Context, in *GetNextCaptureCropsRequest, opts ...grpc.CallOption) (*GetNextCaptureCropsResponse, error) {
	out := new(GetNextCaptureCropsResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextCaptureCrops_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextSelectedCropID(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextSelectedCropIDResponse, error) {
	out := new(GetNextSelectedCropIDResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextSelectedCropID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) SelectCrop(ctx context.Context, in *SelectCropRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_SelectCrop_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) DownloadModel(ctx context.Context, in *DownloadModelRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_DownloadModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextModelHistory(ctx context.Context, in *ModelHistoryRequest, opts ...grpc.CallOption) (*ModelHistoryResponse, error) {
	out := new(ModelHistoryResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextModelHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetModelHistory(ctx context.Context, in *ModelHistoryRequest, opts ...grpc.CallOption) (*ModelHistoryResponse, error) {
	out := new(ModelHistoryResponse)
	err := c.cc.Invoke(ctx, ModelService_GetModelHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetModelNicknames(ctx context.Context, in *GetModelNicknamesRequest, opts ...grpc.CallOption) (*GetModelNicknamesResponse, error) {
	out := new(GetModelNicknamesResponse)
	err := c.cc.Invoke(ctx, ModelService_GetModelNicknames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) GetNextModelNicknames(ctx context.Context, in *GetModelNicknamesRequest, opts ...grpc.CallOption) (*GetModelNicknamesResponse, error) {
	out := new(GetModelNicknamesResponse)
	err := c.cc.Invoke(ctx, ModelService_GetNextModelNicknames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) SetModelNickname(ctx context.Context, in *SetModelNicknameRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_SetModelNickname_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) RefreshDefaultModelParameters(ctx context.Context, in *RefreshDefaultModelParametersRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_RefreshDefaultModelParameters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) SyncCropIDs(ctx context.Context, in *SyncCropIDsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_SyncCropIDs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelServiceClient) TriggerDownload(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelService_TriggerDownload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelServiceServer is the server API for ModelService service.
// All implementations must embed UnimplementedModelServiceServer
// for forward compatibility
type ModelServiceServer interface {
	PinModel(context.Context, *PinModelRequest) (*Empty, error)
	UnpinModel(context.Context, *UnpinModelRequest) (*Empty, error)
	GetNextModelState(context.Context, *GetNextModelStateRequest) (*GetNextModelStateResponse, error)
	GetNextAllModelState(context.Context, *GetNextModelStateRequest) (*GetNextModelStateResponse, error)
	UpdateModel(context.Context, *Empty) (*Empty, error)
	ListEnabledCrops(context.Context, *ListCropParameters) (*EnabledCropList, error)
	GetNextEnabledCrops(context.Context, *GetNextEnabledCropsRequest) (*GetNextEnabledCropsResponse, error)
	ListCaptureCrops(context.Context, *ListCropParameters) (*EnabledCropList, error)
	GetNextCaptureCrops(context.Context, *GetNextCaptureCropsRequest) (*GetNextCaptureCropsResponse, error)
	GetNextSelectedCropID(context.Context, *Timestamp) (*GetNextSelectedCropIDResponse, error)
	SelectCrop(context.Context, *SelectCropRequest) (*Empty, error)
	DownloadModel(context.Context, *DownloadModelRequest) (*Empty, error)
	GetNextModelHistory(context.Context, *ModelHistoryRequest) (*ModelHistoryResponse, error)
	GetModelHistory(context.Context, *ModelHistoryRequest) (*ModelHistoryResponse, error)
	GetModelNicknames(context.Context, *GetModelNicknamesRequest) (*GetModelNicknamesResponse, error)
	GetNextModelNicknames(context.Context, *GetModelNicknamesRequest) (*GetModelNicknamesResponse, error)
	SetModelNickname(context.Context, *SetModelNicknameRequest) (*Empty, error)
	RefreshDefaultModelParameters(context.Context, *RefreshDefaultModelParametersRequest) (*Empty, error)
	SyncCropIDs(context.Context, *SyncCropIDsRequest) (*Empty, error)
	TriggerDownload(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedModelServiceServer()
}

// UnimplementedModelServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModelServiceServer struct {
}

func (UnimplementedModelServiceServer) PinModel(context.Context, *PinModelRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PinModel not implemented")
}
func (UnimplementedModelServiceServer) UnpinModel(context.Context, *UnpinModelRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnpinModel not implemented")
}
func (UnimplementedModelServiceServer) GetNextModelState(context.Context, *GetNextModelStateRequest) (*GetNextModelStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextModelState not implemented")
}
func (UnimplementedModelServiceServer) GetNextAllModelState(context.Context, *GetNextModelStateRequest) (*GetNextModelStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAllModelState not implemented")
}
func (UnimplementedModelServiceServer) UpdateModel(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateModel not implemented")
}
func (UnimplementedModelServiceServer) ListEnabledCrops(context.Context, *ListCropParameters) (*EnabledCropList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledCrops not implemented")
}
func (UnimplementedModelServiceServer) GetNextEnabledCrops(context.Context, *GetNextEnabledCropsRequest) (*GetNextEnabledCropsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextEnabledCrops not implemented")
}
func (UnimplementedModelServiceServer) ListCaptureCrops(context.Context, *ListCropParameters) (*EnabledCropList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCaptureCrops not implemented")
}
func (UnimplementedModelServiceServer) GetNextCaptureCrops(context.Context, *GetNextCaptureCropsRequest) (*GetNextCaptureCropsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextCaptureCrops not implemented")
}
func (UnimplementedModelServiceServer) GetNextSelectedCropID(context.Context, *Timestamp) (*GetNextSelectedCropIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextSelectedCropID not implemented")
}
func (UnimplementedModelServiceServer) SelectCrop(context.Context, *SelectCropRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectCrop not implemented")
}
func (UnimplementedModelServiceServer) DownloadModel(context.Context, *DownloadModelRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadModel not implemented")
}
func (UnimplementedModelServiceServer) GetNextModelHistory(context.Context, *ModelHistoryRequest) (*ModelHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextModelHistory not implemented")
}
func (UnimplementedModelServiceServer) GetModelHistory(context.Context, *ModelHistoryRequest) (*ModelHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelHistory not implemented")
}
func (UnimplementedModelServiceServer) GetModelNicknames(context.Context, *GetModelNicknamesRequest) (*GetModelNicknamesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelNicknames not implemented")
}
func (UnimplementedModelServiceServer) GetNextModelNicknames(context.Context, *GetModelNicknamesRequest) (*GetModelNicknamesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextModelNicknames not implemented")
}
func (UnimplementedModelServiceServer) SetModelNickname(context.Context, *SetModelNicknameRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetModelNickname not implemented")
}
func (UnimplementedModelServiceServer) RefreshDefaultModelParameters(context.Context, *RefreshDefaultModelParametersRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshDefaultModelParameters not implemented")
}
func (UnimplementedModelServiceServer) SyncCropIDs(context.Context, *SyncCropIDsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncCropIDs not implemented")
}
func (UnimplementedModelServiceServer) TriggerDownload(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerDownload not implemented")
}
func (UnimplementedModelServiceServer) mustEmbedUnimplementedModelServiceServer() {}

// UnsafeModelServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelServiceServer will
// result in compilation errors.
type UnsafeModelServiceServer interface {
	mustEmbedUnimplementedModelServiceServer()
}

func RegisterModelServiceServer(s grpc.ServiceRegistrar, srv ModelServiceServer) {
	s.RegisterService(&ModelService_ServiceDesc, srv)
}

func _ModelService_PinModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PinModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).PinModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_PinModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).PinModel(ctx, req.(*PinModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_UnpinModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnpinModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).UnpinModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_UnpinModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).UnpinModel(ctx, req.(*UnpinModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextModelState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextModelStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextModelState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextModelState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextModelState(ctx, req.(*GetNextModelStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextAllModelState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextModelStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextAllModelState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextAllModelState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextAllModelState(ctx, req.(*GetNextModelStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_UpdateModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).UpdateModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_UpdateModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).UpdateModel(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_ListEnabledCrops_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCropParameters)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).ListEnabledCrops(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_ListEnabledCrops_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).ListEnabledCrops(ctx, req.(*ListCropParameters))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextEnabledCrops_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextEnabledCropsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextEnabledCrops(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextEnabledCrops_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextEnabledCrops(ctx, req.(*GetNextEnabledCropsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_ListCaptureCrops_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCropParameters)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).ListCaptureCrops(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_ListCaptureCrops_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).ListCaptureCrops(ctx, req.(*ListCropParameters))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextCaptureCrops_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextCaptureCropsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextCaptureCrops(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextCaptureCrops_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextCaptureCrops(ctx, req.(*GetNextCaptureCropsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextSelectedCropID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextSelectedCropID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextSelectedCropID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextSelectedCropID(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_SelectCrop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectCropRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).SelectCrop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_SelectCrop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).SelectCrop(ctx, req.(*SelectCropRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_DownloadModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).DownloadModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_DownloadModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).DownloadModel(ctx, req.(*DownloadModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextModelHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModelHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextModelHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextModelHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextModelHistory(ctx, req.(*ModelHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetModelHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModelHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetModelHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetModelHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetModelHistory(ctx, req.(*ModelHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetModelNicknames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelNicknamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetModelNicknames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetModelNicknames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetModelNicknames(ctx, req.(*GetModelNicknamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_GetNextModelNicknames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelNicknamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).GetNextModelNicknames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_GetNextModelNicknames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).GetNextModelNicknames(ctx, req.(*GetModelNicknamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_SetModelNickname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetModelNicknameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).SetModelNickname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_SetModelNickname_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).SetModelNickname(ctx, req.(*SetModelNicknameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_RefreshDefaultModelParameters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshDefaultModelParametersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).RefreshDefaultModelParameters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_RefreshDefaultModelParameters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).RefreshDefaultModelParameters(ctx, req.(*RefreshDefaultModelParametersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_SyncCropIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncCropIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).SyncCropIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_SyncCropIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).SyncCropIDs(ctx, req.(*SyncCropIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelService_TriggerDownload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServiceServer).TriggerDownload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelService_TriggerDownload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServiceServer).TriggerDownload(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelService_ServiceDesc is the grpc.ServiceDesc for ModelService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.model.ModelService",
	HandlerType: (*ModelServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PinModel",
			Handler:    _ModelService_PinModel_Handler,
		},
		{
			MethodName: "UnpinModel",
			Handler:    _ModelService_UnpinModel_Handler,
		},
		{
			MethodName: "GetNextModelState",
			Handler:    _ModelService_GetNextModelState_Handler,
		},
		{
			MethodName: "GetNextAllModelState",
			Handler:    _ModelService_GetNextAllModelState_Handler,
		},
		{
			MethodName: "UpdateModel",
			Handler:    _ModelService_UpdateModel_Handler,
		},
		{
			MethodName: "ListEnabledCrops",
			Handler:    _ModelService_ListEnabledCrops_Handler,
		},
		{
			MethodName: "GetNextEnabledCrops",
			Handler:    _ModelService_GetNextEnabledCrops_Handler,
		},
		{
			MethodName: "ListCaptureCrops",
			Handler:    _ModelService_ListCaptureCrops_Handler,
		},
		{
			MethodName: "GetNextCaptureCrops",
			Handler:    _ModelService_GetNextCaptureCrops_Handler,
		},
		{
			MethodName: "GetNextSelectedCropID",
			Handler:    _ModelService_GetNextSelectedCropID_Handler,
		},
		{
			MethodName: "SelectCrop",
			Handler:    _ModelService_SelectCrop_Handler,
		},
		{
			MethodName: "DownloadModel",
			Handler:    _ModelService_DownloadModel_Handler,
		},
		{
			MethodName: "GetNextModelHistory",
			Handler:    _ModelService_GetNextModelHistory_Handler,
		},
		{
			MethodName: "GetModelHistory",
			Handler:    _ModelService_GetModelHistory_Handler,
		},
		{
			MethodName: "GetModelNicknames",
			Handler:    _ModelService_GetModelNicknames_Handler,
		},
		{
			MethodName: "GetNextModelNicknames",
			Handler:    _ModelService_GetNextModelNicknames_Handler,
		},
		{
			MethodName: "SetModelNickname",
			Handler:    _ModelService_SetModelNickname_Handler,
		},
		{
			MethodName: "RefreshDefaultModelParameters",
			Handler:    _ModelService_RefreshDefaultModelParameters_Handler,
		},
		{
			MethodName: "SyncCropIDs",
			Handler:    _ModelService_SyncCropIDs_Handler,
		},
		{
			MethodName: "TriggerDownload",
			Handler:    _ModelService_TriggerDownload_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/model.proto",
}

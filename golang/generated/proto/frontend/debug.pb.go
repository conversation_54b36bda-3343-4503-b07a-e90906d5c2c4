// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/debug.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	logging "github.com/carbonrobotics/robot/golang/generated/proto/logging"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RobotMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial string      `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Alarms []*AlarmRow `protobuf:"bytes,2,rep,name=alarms,proto3" json:"alarms,omitempty"`
}

func (x *RobotMessage) Reset() {
	*x = RobotMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_debug_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotMessage) ProtoMessage() {}

func (x *RobotMessage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_debug_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotMessage.ProtoReflect.Descriptor instead.
func (*RobotMessage) Descriptor() ([]byte, []int) {
	return file_frontend_proto_debug_proto_rawDescGZIP(), []int{0}
}

func (x *RobotMessage) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *RobotMessage) GetAlarms() []*AlarmRow {
	if x != nil {
		return x.Alarms
	}
	return nil
}

type SetLogLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level     logging.LogLevel `protobuf:"varint,1,opt,name=level,proto3,enum=carbon.logging.LogLevel" json:"level,omitempty"`
	Component string           `protobuf:"bytes,2,opt,name=component,proto3" json:"component,omitempty"`
	RowNum    int32            `protobuf:"varint,3,opt,name=row_num,json=rowNum,proto3" json:"row_num,omitempty"`
}

func (x *SetLogLevelRequest) Reset() {
	*x = SetLogLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_debug_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLogLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLogLevelRequest) ProtoMessage() {}

func (x *SetLogLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_debug_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLogLevelRequest.ProtoReflect.Descriptor instead.
func (*SetLogLevelRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_debug_proto_rawDescGZIP(), []int{1}
}

func (x *SetLogLevelRequest) GetLevel() logging.LogLevel {
	if x != nil {
		return x.Level
	}
	return logging.LogLevel(0)
}

func (x *SetLogLevelRequest) GetComponent() string {
	if x != nil {
		return x.Component
	}
	return ""
}

func (x *SetLogLevelRequest) GetRowNum() int32 {
	if x != nil {
		return x.RowNum
	}
	return 0
}

var File_frontend_proto_debug_proto protoreflect.FileDescriptor

var file_frontend_proto_debug_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x62, 0x75, 0x67, 0x1a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x5f, 0x0a, 0x0c, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x6c, 0x61, 0x72,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d,
	0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x6f, 0x77, 0x52, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d,
	0x73, 0x22, 0x7b, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x32, 0xb1,
	0x04, 0x0a, 0x0c, 0x44, 0x65, 0x62, 0x75, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x4c, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x62, 0x75, 0x67,
	0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x55, 0x0a,
	0x0b, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x29, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x62, 0x75, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x74, 0x0a, 0x22, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x38, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5b, 0x0a, 0x1a, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x27, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x56, 0x0a, 0x1a, 0x41, 0x64, 0x64, 0x4d, 0x6f,
	0x63, 0x6b, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x51, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x42, 0x12, 0x48, 0x03, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_debug_proto_rawDescOnce sync.Once
	file_frontend_proto_debug_proto_rawDescData = file_frontend_proto_debug_proto_rawDesc
)

func file_frontend_proto_debug_proto_rawDescGZIP() []byte {
	file_frontend_proto_debug_proto_rawDescOnce.Do(func() {
		file_frontend_proto_debug_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_debug_proto_rawDescData)
	})
	return file_frontend_proto_debug_proto_rawDescData
}

var file_frontend_proto_debug_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_frontend_proto_debug_proto_goTypes = []interface{}{
	(*RobotMessage)(nil),       // 0: carbon.frontend.debug.RobotMessage
	(*SetLogLevelRequest)(nil), // 1: carbon.frontend.debug.SetLogLevelRequest
	(*AlarmRow)(nil),           // 2: carbon.frontend.alarm.AlarmRow
	(logging.LogLevel)(0),      // 3: carbon.logging.LogLevel
	(*Empty)(nil),              // 4: carbon.frontend.util.Empty
	(*weed_tracking.StartSavingCropLineDetectionReplayRequest)(nil), // 5: weed_tracking.StartSavingCropLineDetectionReplayRequest
	(*weed_tracking.RecordAimbotInputRequest)(nil),                  // 6: weed_tracking.RecordAimbotInputRequest
	(*weed_tracking.Empty)(nil),                                     // 7: weed_tracking.Empty
}
var file_frontend_proto_debug_proto_depIdxs = []int32{
	2, // 0: carbon.frontend.debug.RobotMessage.alarms:type_name -> carbon.frontend.alarm.AlarmRow
	3, // 1: carbon.frontend.debug.SetLogLevelRequest.level:type_name -> carbon.logging.LogLevel
	4, // 2: carbon.frontend.debug.DebugService.GetRobot:input_type -> carbon.frontend.util.Empty
	1, // 3: carbon.frontend.debug.DebugService.SetLogLevel:input_type -> carbon.frontend.debug.SetLogLevelRequest
	5, // 4: carbon.frontend.debug.DebugService.StartSavingCropLineDetectionReplay:input_type -> weed_tracking.StartSavingCropLineDetectionReplayRequest
	6, // 5: carbon.frontend.debug.DebugService.StartRecordingAimbotInputs:input_type -> weed_tracking.RecordAimbotInputRequest
	4, // 6: carbon.frontend.debug.DebugService.AddMockSpatialMetricsBlock:input_type -> carbon.frontend.util.Empty
	4, // 7: carbon.frontend.debug.DebugService.DeleteProfileSyncData:input_type -> carbon.frontend.util.Empty
	0, // 8: carbon.frontend.debug.DebugService.GetRobot:output_type -> carbon.frontend.debug.RobotMessage
	4, // 9: carbon.frontend.debug.DebugService.SetLogLevel:output_type -> carbon.frontend.util.Empty
	7, // 10: carbon.frontend.debug.DebugService.StartSavingCropLineDetectionReplay:output_type -> weed_tracking.Empty
	7, // 11: carbon.frontend.debug.DebugService.StartRecordingAimbotInputs:output_type -> weed_tracking.Empty
	4, // 12: carbon.frontend.debug.DebugService.AddMockSpatialMetricsBlock:output_type -> carbon.frontend.util.Empty
	4, // 13: carbon.frontend.debug.DebugService.DeleteProfileSyncData:output_type -> carbon.frontend.util.Empty
	8, // [8:14] is the sub-list for method output_type
	2, // [2:8] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_frontend_proto_debug_proto_init() }
func file_frontend_proto_debug_proto_init() {
	if File_frontend_proto_debug_proto != nil {
		return
	}
	file_frontend_proto_alarm_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_debug_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_debug_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLogLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_debug_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_debug_proto_goTypes,
		DependencyIndexes: file_frontend_proto_debug_proto_depIdxs,
		MessageInfos:      file_frontend_proto_debug_proto_msgTypes,
	}.Build()
	File_frontend_proto_debug_proto = out.File
	file_frontend_proto_debug_proto_rawDesc = nil
	file_frontend_proto_debug_proto_goTypes = nil
	file_frontend_proto_debug_proto_depIdxs = nil
}

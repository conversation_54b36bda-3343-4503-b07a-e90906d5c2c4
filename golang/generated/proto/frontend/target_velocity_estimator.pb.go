// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/target_velocity_estimator.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	target_velocity_estimator "github.com/carbonrobotics/robot/golang/generated/proto/target_velocity_estimator"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNextAvailableTVEProfilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts       *Timestamp                                  `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Profiles []*target_velocity_estimator.ProfileDetails `protobuf:"bytes,2,rep,name=profiles,proto3" json:"profiles,omitempty"`
}

func (x *GetNextAvailableTVEProfilesResponse) Reset() {
	*x = GetNextAvailableTVEProfilesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextAvailableTVEProfilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextAvailableTVEProfilesResponse) ProtoMessage() {}

func (x *GetNextAvailableTVEProfilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextAvailableTVEProfilesResponse.ProtoReflect.Descriptor instead.
func (*GetNextAvailableTVEProfilesResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{0}
}

func (x *GetNextAvailableTVEProfilesResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextAvailableTVEProfilesResponse) GetProfiles() []*target_velocity_estimator.ProfileDetails {
	if x != nil {
		return x.Profiles
	}
	return nil
}

type GetNextActiveTVEProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts      *Timestamp                            `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Profile *target_velocity_estimator.TVEProfile `protobuf:"bytes,2,opt,name=profile,proto3" json:"profile,omitempty"`
}

func (x *GetNextActiveTVEProfileResponse) Reset() {
	*x = GetNextActiveTVEProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveTVEProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveTVEProfileResponse) ProtoMessage() {}

func (x *GetNextActiveTVEProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveTVEProfileResponse.ProtoReflect.Descriptor instead.
func (*GetNextActiveTVEProfileResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextActiveTVEProfileResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextActiveTVEProfileResponse) GetProfile() *target_velocity_estimator.TVEProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

type LoadTVEProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *LoadTVEProfileRequest) Reset() {
	*x = LoadTVEProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadTVEProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadTVEProfileRequest) ProtoMessage() {}

func (x *LoadTVEProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadTVEProfileRequest.ProtoReflect.Descriptor instead.
func (*LoadTVEProfileRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{2}
}

func (x *LoadTVEProfileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LoadTVEProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile *target_velocity_estimator.TVEProfile `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
}

func (x *LoadTVEProfileResponse) Reset() {
	*x = LoadTVEProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadTVEProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadTVEProfileResponse) ProtoMessage() {}

func (x *LoadTVEProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadTVEProfileResponse.ProtoReflect.Descriptor instead.
func (*LoadTVEProfileResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{3}
}

func (x *LoadTVEProfileResponse) GetProfile() *target_velocity_estimator.TVEProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

type SaveTVEProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile   *target_velocity_estimator.TVEProfile `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	SetActive bool                                  `protobuf:"varint,2,opt,name=set_active,json=setActive,proto3" json:"set_active,omitempty"`
}

func (x *SaveTVEProfileRequest) Reset() {
	*x = SaveTVEProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTVEProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTVEProfileRequest) ProtoMessage() {}

func (x *SaveTVEProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTVEProfileRequest.ProtoReflect.Descriptor instead.
func (*SaveTVEProfileRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{4}
}

func (x *SaveTVEProfileRequest) GetProfile() *target_velocity_estimator.TVEProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *SaveTVEProfileRequest) GetSetActive() bool {
	if x != nil {
		return x.SetActive
	}
	return false
}

type SaveTVEProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SaveTVEProfileResponse) Reset() {
	*x = SaveTVEProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTVEProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTVEProfileResponse) ProtoMessage() {}

func (x *SaveTVEProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTVEProfileResponse.ProtoReflect.Descriptor instead.
func (*SaveTVEProfileResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{5}
}

func (x *SaveTVEProfileResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SetActiveTVEProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SetActiveTVEProfileRequest) Reset() {
	*x = SetActiveTVEProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveTVEProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveTVEProfileRequest) ProtoMessage() {}

func (x *SetActiveTVEProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveTVEProfileRequest.ProtoReflect.Descriptor instead.
func (*SetActiveTVEProfileRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{6}
}

func (x *SetActiveTVEProfileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SetActiveTVEProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetActiveTVEProfileResponse) Reset() {
	*x = SetActiveTVEProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveTVEProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveTVEProfileResponse) ProtoMessage() {}

func (x *SetActiveTVEProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveTVEProfileResponse.ProtoReflect.Descriptor instead.
func (*SetActiveTVEProfileResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{7}
}

type DeleteTVEProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	NewActiveId string `protobuf:"bytes,2,opt,name=new_active_id,json=newActiveId,proto3" json:"new_active_id,omitempty"` // Only looked at if id is currently active, and this must be a valid id in such case else can be empty
}

func (x *DeleteTVEProfileRequest) Reset() {
	*x = DeleteTVEProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTVEProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTVEProfileRequest) ProtoMessage() {}

func (x *DeleteTVEProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTVEProfileRequest.ProtoReflect.Descriptor instead.
func (*DeleteTVEProfileRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteTVEProfileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteTVEProfileRequest) GetNewActiveId() string {
	if x != nil {
		return x.NewActiveId
	}
	return ""
}

type DeleteTVEProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteTVEProfileResponse) Reset() {
	*x = DeleteTVEProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTVEProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTVEProfileResponse) ProtoMessage() {}

func (x *DeleteTVEProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_target_velocity_estimator_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTVEProfileResponse.ProtoReflect.Descriptor instead.
func (*DeleteTVEProfileResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP(), []int{9}
}

var File_frontend_proto_target_velocity_estimator_proto protoreflect.FileDescriptor

var file_frontend_proto_target_velocity_estimator_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x29, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x19, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x56, 0x45, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x12, 0x53, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x4d, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x27, 0x0a, 0x15, 0x4c, 0x6f, 0x61,
	0x64, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x67, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x64, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x07,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x15,
	0x53, 0x61, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x22, 0x28, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2c, 0x0a,
	0x1a, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x53,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4d, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65,
	0x77, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x96, 0x07, 0x0a, 0x1e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f,
	0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x4e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x1a, 0x4a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a,
	0x0b, 0x4c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x40, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x54, 0x56, 0x45,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x54,
	0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x92, 0x01, 0x0a, 0x0b, 0x53, 0x61, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x61,
	0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x53, 0x61, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x56, 0x45, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10,
	0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_target_velocity_estimator_proto_rawDescOnce sync.Once
	file_frontend_proto_target_velocity_estimator_proto_rawDescData = file_frontend_proto_target_velocity_estimator_proto_rawDesc
)

func file_frontend_proto_target_velocity_estimator_proto_rawDescGZIP() []byte {
	file_frontend_proto_target_velocity_estimator_proto_rawDescOnce.Do(func() {
		file_frontend_proto_target_velocity_estimator_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_target_velocity_estimator_proto_rawDescData)
	})
	return file_frontend_proto_target_velocity_estimator_proto_rawDescData
}

var file_frontend_proto_target_velocity_estimator_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_frontend_proto_target_velocity_estimator_proto_goTypes = []interface{}{
	(*GetNextAvailableTVEProfilesResponse)(nil),      // 0: carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse
	(*GetNextActiveTVEProfileResponse)(nil),          // 1: carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse
	(*LoadTVEProfileRequest)(nil),                    // 2: carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest
	(*LoadTVEProfileResponse)(nil),                   // 3: carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse
	(*SaveTVEProfileRequest)(nil),                    // 4: carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest
	(*SaveTVEProfileResponse)(nil),                   // 5: carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse
	(*SetActiveTVEProfileRequest)(nil),               // 6: carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest
	(*SetActiveTVEProfileResponse)(nil),              // 7: carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse
	(*DeleteTVEProfileRequest)(nil),                  // 8: carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest
	(*DeleteTVEProfileResponse)(nil),                 // 9: carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse
	(*Timestamp)(nil),                                // 10: carbon.frontend.util.Timestamp
	(*target_velocity_estimator.ProfileDetails)(nil), // 11: carbon.aimbot.target_velocity_estimator.ProfileDetails
	(*target_velocity_estimator.TVEProfile)(nil),     // 12: carbon.aimbot.target_velocity_estimator.TVEProfile
}
var file_frontend_proto_target_velocity_estimator_proto_depIdxs = []int32{
	10, // 0: carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts:type_name -> carbon.frontend.util.Timestamp
	11, // 1: carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles:type_name -> carbon.aimbot.target_velocity_estimator.ProfileDetails
	10, // 2: carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts:type_name -> carbon.frontend.util.Timestamp
	12, // 3: carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	12, // 4: carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	12, // 5: carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	10, // 6: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.GetNextAvailableProfiles:input_type -> carbon.frontend.util.Timestamp
	10, // 7: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.GetNextActiveProfile:input_type -> carbon.frontend.util.Timestamp
	2,  // 8: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.LoadProfile:input_type -> carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest
	4,  // 9: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.SaveProfile:input_type -> carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest
	6,  // 10: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.SetActive:input_type -> carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest
	8,  // 11: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.DeleteProfile:input_type -> carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest
	0,  // 12: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.GetNextAvailableProfiles:output_type -> carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse
	1,  // 13: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.GetNextActiveProfile:output_type -> carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse
	3,  // 14: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.LoadProfile:output_type -> carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse
	5,  // 15: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.SaveProfile:output_type -> carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse
	7,  // 16: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.SetActive:output_type -> carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse
	9,  // 17: carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.DeleteProfile:output_type -> carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_frontend_proto_target_velocity_estimator_proto_init() }
func file_frontend_proto_target_velocity_estimator_proto_init() {
	if File_frontend_proto_target_velocity_estimator_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextAvailableTVEProfilesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveTVEProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadTVEProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadTVEProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTVEProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTVEProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveTVEProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveTVEProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTVEProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_target_velocity_estimator_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTVEProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_target_velocity_estimator_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_target_velocity_estimator_proto_goTypes,
		DependencyIndexes: file_frontend_proto_target_velocity_estimator_proto_depIdxs,
		MessageInfos:      file_frontend_proto_target_velocity_estimator_proto_msgTypes,
	}.Build()
	File_frontend_proto_target_velocity_estimator_proto = out.File
	file_frontend_proto_target_velocity_estimator_proto_rawDesc = nil
	file_frontend_proto_target_velocity_estimator_proto_goTypes = nil
	file_frontend_proto_target_velocity_estimator_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/startup_task.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	startup_task "github.com/carbonrobotics/robot/golang/generated/proto/startup_task"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNextTasksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp           `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Tasks []*startup_task.Task `protobuf:"bytes,2,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *GetNextTasksResponse) Reset() {
	*x = GetNextTasksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_startup_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextTasksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextTasksResponse) ProtoMessage() {}

func (x *GetNextTasksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_startup_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextTasksResponse.ProtoReflect.Descriptor instead.
func (*GetNextTasksResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_startup_task_proto_rawDescGZIP(), []int{0}
}

func (x *GetNextTasksResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextTasksResponse) GetTasks() []*startup_task.Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type MarkTaskCompleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *MarkTaskCompleteRequest) Reset() {
	*x = MarkTaskCompleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_startup_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkTaskCompleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkTaskCompleteRequest) ProtoMessage() {}

func (x *MarkTaskCompleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_startup_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkTaskCompleteRequest.ProtoReflect.Descriptor instead.
func (*MarkTaskCompleteRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_startup_task_proto_rawDescGZIP(), []int{1}
}

func (x *MarkTaskCompleteRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type MarkTaskCompleteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MarkTaskCompleteResponse) Reset() {
	*x = MarkTaskCompleteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_startup_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkTaskCompleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkTaskCompleteResponse) ProtoMessage() {}

func (x *MarkTaskCompleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_startup_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkTaskCompleteResponse.ProtoReflect.Descriptor instead.
func (*MarkTaskCompleteResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_startup_task_proto_rawDescGZIP(), []int{2}
}

var File_frontend_proto_startup_task_proto protoreflect.FileDescriptor

var file_frontend_proto_startup_task_proto_rawDesc = []byte{
	0x0a, 0x21, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x1a, 0x25, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x78, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x05,
	0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x32, 0x0a,
	0x17, 0x4d, 0x61, 0x72, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x22, 0x1a, 0x0a, 0x18, 0x4d, 0x61, 0x72, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xfd, 0x01,
	0x0a, 0x12, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x10, 0x4d, 0x61,
	0x72, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x35,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4d, 0x61,
	0x72, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a,
	0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_startup_task_proto_rawDescOnce sync.Once
	file_frontend_proto_startup_task_proto_rawDescData = file_frontend_proto_startup_task_proto_rawDesc
)

func file_frontend_proto_startup_task_proto_rawDescGZIP() []byte {
	file_frontend_proto_startup_task_proto_rawDescOnce.Do(func() {
		file_frontend_proto_startup_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_startup_task_proto_rawDescData)
	})
	return file_frontend_proto_startup_task_proto_rawDescData
}

var file_frontend_proto_startup_task_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_frontend_proto_startup_task_proto_goTypes = []interface{}{
	(*GetNextTasksResponse)(nil),     // 0: carbon.frontend.startup_task.GetNextTasksResponse
	(*MarkTaskCompleteRequest)(nil),  // 1: carbon.frontend.startup_task.MarkTaskCompleteRequest
	(*MarkTaskCompleteResponse)(nil), // 2: carbon.frontend.startup_task.MarkTaskCompleteResponse
	(*Timestamp)(nil),                // 3: carbon.frontend.util.Timestamp
	(*startup_task.Task)(nil),        // 4: carbon.startup_task.Task
}
var file_frontend_proto_startup_task_proto_depIdxs = []int32{
	3, // 0: carbon.frontend.startup_task.GetNextTasksResponse.ts:type_name -> carbon.frontend.util.Timestamp
	4, // 1: carbon.frontend.startup_task.GetNextTasksResponse.tasks:type_name -> carbon.startup_task.Task
	3, // 2: carbon.frontend.startup_task.StartupTaskService.GetNextTasks:input_type -> carbon.frontend.util.Timestamp
	1, // 3: carbon.frontend.startup_task.StartupTaskService.MarkTaskComplete:input_type -> carbon.frontend.startup_task.MarkTaskCompleteRequest
	0, // 4: carbon.frontend.startup_task.StartupTaskService.GetNextTasks:output_type -> carbon.frontend.startup_task.GetNextTasksResponse
	2, // 5: carbon.frontend.startup_task.StartupTaskService.MarkTaskComplete:output_type -> carbon.frontend.startup_task.MarkTaskCompleteResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_frontend_proto_startup_task_proto_init() }
func file_frontend_proto_startup_task_proto_init() {
	if File_frontend_proto_startup_task_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_startup_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextTasksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_startup_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkTaskCompleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_startup_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkTaskCompleteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_startup_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_startup_task_proto_goTypes,
		DependencyIndexes: file_frontend_proto_startup_task_proto_depIdxs,
		MessageInfos:      file_frontend_proto_startup_task_proto_msgTypes,
	}.Build()
	File_frontend_proto_startup_task_proto = out.File
	file_frontend_proto_startup_task_proto_rawDesc = nil
	file_frontend_proto_startup_task_proto_goTypes = nil
	file_frontend_proto_startup_task_proto_depIdxs = nil
}

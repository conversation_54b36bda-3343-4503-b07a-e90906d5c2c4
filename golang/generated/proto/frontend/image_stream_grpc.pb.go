// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/image_stream.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ImageStreamService_GetNextCameraImage_FullMethodName          = "/carbon.frontend.image_stream.ImageStreamService/GetNextCameraImage"
	ImageStreamService_GetPredictImageByTimestamp_FullMethodName  = "/carbon.frontend.image_stream.ImageStreamService/GetPredictImageByTimestamp"
	ImageStreamService_GetMultiPredictPerspectives_FullMethodName = "/carbon.frontend.image_stream.ImageStreamService/GetMultiPredictPerspectives"
)

// ImageStreamServiceClient is the client API for ImageStreamService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ImageStreamServiceClient interface {
	GetNextCameraImage(ctx context.Context, in *CameraImageRequest, opts ...grpc.CallOption) (*Image, error)
	GetPredictImageByTimestamp(ctx context.Context, in *GetPredictImageByTimestampRequest, opts ...grpc.CallOption) (*GetPredictImageByTimestampResponse, error)
	GetMultiPredictPerspectives(ctx context.Context, in *GetMultiPredictPerspectivesRequest, opts ...grpc.CallOption) (*GetMultiPredictPerspectivesResponse, error)
}

type imageStreamServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewImageStreamServiceClient(cc grpc.ClientConnInterface) ImageStreamServiceClient {
	return &imageStreamServiceClient{cc}
}

func (c *imageStreamServiceClient) GetNextCameraImage(ctx context.Context, in *CameraImageRequest, opts ...grpc.CallOption) (*Image, error) {
	out := new(Image)
	err := c.cc.Invoke(ctx, ImageStreamService_GetNextCameraImage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageStreamServiceClient) GetPredictImageByTimestamp(ctx context.Context, in *GetPredictImageByTimestampRequest, opts ...grpc.CallOption) (*GetPredictImageByTimestampResponse, error) {
	out := new(GetPredictImageByTimestampResponse)
	err := c.cc.Invoke(ctx, ImageStreamService_GetPredictImageByTimestamp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageStreamServiceClient) GetMultiPredictPerspectives(ctx context.Context, in *GetMultiPredictPerspectivesRequest, opts ...grpc.CallOption) (*GetMultiPredictPerspectivesResponse, error) {
	out := new(GetMultiPredictPerspectivesResponse)
	err := c.cc.Invoke(ctx, ImageStreamService_GetMultiPredictPerspectives_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImageStreamServiceServer is the server API for ImageStreamService service.
// All implementations must embed UnimplementedImageStreamServiceServer
// for forward compatibility
type ImageStreamServiceServer interface {
	GetNextCameraImage(context.Context, *CameraImageRequest) (*Image, error)
	GetPredictImageByTimestamp(context.Context, *GetPredictImageByTimestampRequest) (*GetPredictImageByTimestampResponse, error)
	GetMultiPredictPerspectives(context.Context, *GetMultiPredictPerspectivesRequest) (*GetMultiPredictPerspectivesResponse, error)
	mustEmbedUnimplementedImageStreamServiceServer()
}

// UnimplementedImageStreamServiceServer must be embedded to have forward compatible implementations.
type UnimplementedImageStreamServiceServer struct {
}

func (UnimplementedImageStreamServiceServer) GetNextCameraImage(context.Context, *CameraImageRequest) (*Image, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextCameraImage not implemented")
}
func (UnimplementedImageStreamServiceServer) GetPredictImageByTimestamp(context.Context, *GetPredictImageByTimestampRequest) (*GetPredictImageByTimestampResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPredictImageByTimestamp not implemented")
}
func (UnimplementedImageStreamServiceServer) GetMultiPredictPerspectives(context.Context, *GetMultiPredictPerspectivesRequest) (*GetMultiPredictPerspectivesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMultiPredictPerspectives not implemented")
}
func (UnimplementedImageStreamServiceServer) mustEmbedUnimplementedImageStreamServiceServer() {}

// UnsafeImageStreamServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ImageStreamServiceServer will
// result in compilation errors.
type UnsafeImageStreamServiceServer interface {
	mustEmbedUnimplementedImageStreamServiceServer()
}

func RegisterImageStreamServiceServer(s grpc.ServiceRegistrar, srv ImageStreamServiceServer) {
	s.RegisterService(&ImageStreamService_ServiceDesc, srv)
}

func _ImageStreamService_GetNextCameraImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CameraImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageStreamServiceServer).GetNextCameraImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageStreamService_GetNextCameraImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageStreamServiceServer).GetNextCameraImage(ctx, req.(*CameraImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageStreamService_GetPredictImageByTimestamp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPredictImageByTimestampRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageStreamServiceServer).GetPredictImageByTimestamp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageStreamService_GetPredictImageByTimestamp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageStreamServiceServer).GetPredictImageByTimestamp(ctx, req.(*GetPredictImageByTimestampRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageStreamService_GetMultiPredictPerspectives_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiPredictPerspectivesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageStreamServiceServer).GetMultiPredictPerspectives(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ImageStreamService_GetMultiPredictPerspectives_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageStreamServiceServer).GetMultiPredictPerspectives(ctx, req.(*GetMultiPredictPerspectivesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ImageStreamService_ServiceDesc is the grpc.ServiceDesc for ImageStreamService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ImageStreamService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.image_stream.ImageStreamService",
	HandlerType: (*ImageStreamServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextCameraImage",
			Handler:    _ImageStreamService_GetNextCameraImage_Handler,
		},
		{
			MethodName: "GetPredictImageByTimestamp",
			Handler:    _ImageStreamService_GetPredictImageByTimestamp_Handler,
		},
		{
			MethodName: "GetMultiPredictPerspectives",
			Handler:    _ImageStreamService_GetMultiPredictPerspectives_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/image_stream.proto",
}

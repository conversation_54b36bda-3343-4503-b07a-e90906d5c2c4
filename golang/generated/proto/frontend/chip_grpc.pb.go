// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/chip.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ChipService_GetChipMetadata_FullMethodName      = "/carbon.frontend.chip.ChipService/GetChipMetadata"
	ChipService_GetDownloadedChipIds_FullMethodName = "/carbon.frontend.chip.ChipService/GetDownloadedChipIds"
	ChipService_GetSyncedChipIds_FullMethodName     = "/carbon.frontend.chip.ChipService/GetSyncedChipIds"
)

// ChipServiceClient is the client API for ChipService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChipServiceClient interface {
	GetChipMetadata(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetChipMetadataResponse, error)
	GetDownloadedChipIds(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ChipIdsResponse, error)
	GetSyncedChipIds(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ChipIdsResponse, error)
}

type chipServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewChipServiceClient(cc grpc.ClientConnInterface) ChipServiceClient {
	return &chipServiceClient{cc}
}

func (c *chipServiceClient) GetChipMetadata(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetChipMetadataResponse, error) {
	out := new(GetChipMetadataResponse)
	err := c.cc.Invoke(ctx, ChipService_GetChipMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chipServiceClient) GetDownloadedChipIds(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ChipIdsResponse, error) {
	out := new(ChipIdsResponse)
	err := c.cc.Invoke(ctx, ChipService_GetDownloadedChipIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chipServiceClient) GetSyncedChipIds(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ChipIdsResponse, error) {
	out := new(ChipIdsResponse)
	err := c.cc.Invoke(ctx, ChipService_GetSyncedChipIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChipServiceServer is the server API for ChipService service.
// All implementations must embed UnimplementedChipServiceServer
// for forward compatibility
type ChipServiceServer interface {
	GetChipMetadata(context.Context, *Empty) (*GetChipMetadataResponse, error)
	GetDownloadedChipIds(context.Context, *Empty) (*ChipIdsResponse, error)
	GetSyncedChipIds(context.Context, *Empty) (*ChipIdsResponse, error)
	mustEmbedUnimplementedChipServiceServer()
}

// UnimplementedChipServiceServer must be embedded to have forward compatible implementations.
type UnimplementedChipServiceServer struct {
}

func (UnimplementedChipServiceServer) GetChipMetadata(context.Context, *Empty) (*GetChipMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChipMetadata not implemented")
}
func (UnimplementedChipServiceServer) GetDownloadedChipIds(context.Context, *Empty) (*ChipIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDownloadedChipIds not implemented")
}
func (UnimplementedChipServiceServer) GetSyncedChipIds(context.Context, *Empty) (*ChipIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncedChipIds not implemented")
}
func (UnimplementedChipServiceServer) mustEmbedUnimplementedChipServiceServer() {}

// UnsafeChipServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChipServiceServer will
// result in compilation errors.
type UnsafeChipServiceServer interface {
	mustEmbedUnimplementedChipServiceServer()
}

func RegisterChipServiceServer(s grpc.ServiceRegistrar, srv ChipServiceServer) {
	s.RegisterService(&ChipService_ServiceDesc, srv)
}

func _ChipService_GetChipMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChipServiceServer).GetChipMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChipService_GetChipMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChipServiceServer).GetChipMetadata(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChipService_GetDownloadedChipIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChipServiceServer).GetDownloadedChipIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChipService_GetDownloadedChipIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChipServiceServer).GetDownloadedChipIds(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChipService_GetSyncedChipIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChipServiceServer).GetSyncedChipIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChipService_GetSyncedChipIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChipServiceServer).GetSyncedChipIds(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ChipService_ServiceDesc is the grpc.ServiceDesc for ChipService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChipService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.chip.ChipService",
	HandlerType: (*ChipServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChipMetadata",
			Handler:    _ChipService_GetChipMetadata_Handler,
		},
		{
			MethodName: "GetDownloadedChipIds",
			Handler:    _ChipService_GetDownloadedChipIds_Handler,
		},
		{
			MethodName: "GetSyncedChipIds",
			Handler:    _ChipService_GetSyncedChipIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/chip.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/software.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SoftwareVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag       string `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Available bool   `protobuf:"varint,2,opt,name=available,proto3" json:"available,omitempty"`
	Ready     bool   `protobuf:"varint,3,opt,name=ready,proto3" json:"ready,omitempty"` // TODO Implement this
}

func (x *SoftwareVersion) Reset() {
	*x = SoftwareVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_software_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersion) ProtoMessage() {}

func (x *SoftwareVersion) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_software_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersion.ProtoReflect.Descriptor instead.
func (*SoftwareVersion) Descriptor() ([]byte, []int) {
	return file_frontend_proto_software_proto_rawDescGZIP(), []int{0}
}

func (x *SoftwareVersion) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *SoftwareVersion) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *SoftwareVersion) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

type HostSoftwareVersionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostName string           `protobuf:"bytes,1,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
	HostId   uint32           `protobuf:"varint,2,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`
	Active   bool             `protobuf:"varint,3,opt,name=active,proto3" json:"active,omitempty"`
	Current  *SoftwareVersion `protobuf:"bytes,4,opt,name=current,proto3" json:"current,omitempty"`
	Target   *SoftwareVersion `protobuf:"bytes,5,opt,name=target,proto3" json:"target,omitempty"`
	Previous *SoftwareVersion `protobuf:"bytes,6,opt,name=previous,proto3" json:"previous,omitempty"`
	Updating bool             `protobuf:"varint,7,opt,name=updating,proto3" json:"updating,omitempty"`
}

func (x *HostSoftwareVersionState) Reset() {
	*x = HostSoftwareVersionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_software_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostSoftwareVersionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostSoftwareVersionState) ProtoMessage() {}

func (x *HostSoftwareVersionState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_software_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostSoftwareVersionState.ProtoReflect.Descriptor instead.
func (*HostSoftwareVersionState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_software_proto_rawDescGZIP(), []int{1}
}

func (x *HostSoftwareVersionState) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *HostSoftwareVersionState) GetHostId() uint32 {
	if x != nil {
		return x.HostId
	}
	return 0
}

func (x *HostSoftwareVersionState) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *HostSoftwareVersionState) GetCurrent() *SoftwareVersion {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *HostSoftwareVersionState) GetTarget() *SoftwareVersion {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *HostSoftwareVersionState) GetPrevious() *SoftwareVersion {
	if x != nil {
		return x.Previous
	}
	return nil
}

func (x *HostSoftwareVersionState) GetUpdating() bool {
	if x != nil {
		return x.Updating
	}
	return false
}

type SoftwareVersionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                       *Timestamp                  `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Current                  *SoftwareVersion            `protobuf:"bytes,2,opt,name=current,proto3" json:"current,omitempty"`
	Target                   *SoftwareVersion            `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	Previous                 *SoftwareVersion            `protobuf:"bytes,4,opt,name=previous,proto3" json:"previous,omitempty"`
	Updating                 bool                        `protobuf:"varint,5,opt,name=updating,proto3" json:"updating,omitempty"`
	ShowSoftwareUpdateToUser bool                        `protobuf:"varint,6,opt,name=show_software_update_to_user,json=showSoftwareUpdateToUser,proto3" json:"show_software_update_to_user,omitempty"`
	HostStates               []*HostSoftwareVersionState `protobuf:"bytes,7,rep,name=host_states,json=hostStates,proto3" json:"host_states,omitempty"`
	VersionMismatch          bool                        `protobuf:"varint,8,opt,name=version_mismatch,json=versionMismatch,proto3" json:"version_mismatch,omitempty"`
}

func (x *SoftwareVersionState) Reset() {
	*x = SoftwareVersionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_software_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersionState) ProtoMessage() {}

func (x *SoftwareVersionState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_software_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersionState.ProtoReflect.Descriptor instead.
func (*SoftwareVersionState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_software_proto_rawDescGZIP(), []int{2}
}

func (x *SoftwareVersionState) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *SoftwareVersionState) GetCurrent() *SoftwareVersion {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *SoftwareVersionState) GetTarget() *SoftwareVersion {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *SoftwareVersionState) GetPrevious() *SoftwareVersion {
	if x != nil {
		return x.Previous
	}
	return nil
}

func (x *SoftwareVersionState) GetUpdating() bool {
	if x != nil {
		return x.Updating
	}
	return false
}

func (x *SoftwareVersionState) GetShowSoftwareUpdateToUser() bool {
	if x != nil {
		return x.ShowSoftwareUpdateToUser
	}
	return false
}

func (x *SoftwareVersionState) GetHostStates() []*HostSoftwareVersionState {
	if x != nil {
		return x.HostStates
	}
	return nil
}

func (x *SoftwareVersionState) GetVersionMismatch() bool {
	if x != nil {
		return x.VersionMismatch
	}
	return false
}

type SoftwareVersionStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts            *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	GetHostStates bool       `protobuf:"varint,2,opt,name=get_host_states,json=getHostStates,proto3" json:"get_host_states,omitempty"`
}

func (x *SoftwareVersionStateRequest) Reset() {
	*x = SoftwareVersionStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_software_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersionStateRequest) ProtoMessage() {}

func (x *SoftwareVersionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_software_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersionStateRequest.ProtoReflect.Descriptor instead.
func (*SoftwareVersionStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_software_proto_rawDescGZIP(), []int{3}
}

func (x *SoftwareVersionStateRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *SoftwareVersionStateRequest) GetGetHostStates() bool {
	if x != nil {
		return x.GetHostStates
	}
	return false
}

type UpdateHostRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostId uint32 `protobuf:"varint,1,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`
}

func (x *UpdateHostRequest) Reset() {
	*x = UpdateHostRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_software_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateHostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateHostRequest) ProtoMessage() {}

func (x *UpdateHostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_software_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateHostRequest.ProtoReflect.Descriptor instead.
func (*UpdateHostRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_software_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateHostRequest) GetHostId() uint32 {
	if x != nil {
		return x.HostId
	}
	return 0
}

var File_frontend_proto_software_proto protoreflect.FileDescriptor

var file_frontend_proto_software_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x0f, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x22, 0xd3, 0x02,
	0x0a, 0x18, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x43, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x41, 0x0a,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x12, 0x45, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x22, 0xf2, 0x03, 0x0a, 0x14, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x43, 0x0a,
	0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x12, 0x41, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3e, 0x0a, 0x1c, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18,
	0x73, 0x68, 0x6f, 0x77, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x0b, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x29, 0x0a,
	0x10, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x22, 0x76, 0x0a, 0x1b, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x65, 0x74, 0x5f,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x67, 0x65, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x22, 0x2c, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x32, 0xc8,
	0x03, 0x0a, 0x0f, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x56, 0x0a, 0x0a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x42, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x42, 0x0a, 0x06, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x12,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4e, 0x0a, 0x12, 0x46, 0x69, 0x78,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_software_proto_rawDescOnce sync.Once
	file_frontend_proto_software_proto_rawDescData = file_frontend_proto_software_proto_rawDesc
)

func file_frontend_proto_software_proto_rawDescGZIP() []byte {
	file_frontend_proto_software_proto_rawDescOnce.Do(func() {
		file_frontend_proto_software_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_software_proto_rawDescData)
	})
	return file_frontend_proto_software_proto_rawDescData
}

var file_frontend_proto_software_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_frontend_proto_software_proto_goTypes = []interface{}{
	(*SoftwareVersion)(nil),             // 0: carbon.frontend.software.SoftwareVersion
	(*HostSoftwareVersionState)(nil),    // 1: carbon.frontend.software.HostSoftwareVersionState
	(*SoftwareVersionState)(nil),        // 2: carbon.frontend.software.SoftwareVersionState
	(*SoftwareVersionStateRequest)(nil), // 3: carbon.frontend.software.SoftwareVersionStateRequest
	(*UpdateHostRequest)(nil),           // 4: carbon.frontend.software.UpdateHostRequest
	(*Timestamp)(nil),                   // 5: carbon.frontend.util.Timestamp
	(*Empty)(nil),                       // 6: carbon.frontend.util.Empty
}
var file_frontend_proto_software_proto_depIdxs = []int32{
	0,  // 0: carbon.frontend.software.HostSoftwareVersionState.current:type_name -> carbon.frontend.software.SoftwareVersion
	0,  // 1: carbon.frontend.software.HostSoftwareVersionState.target:type_name -> carbon.frontend.software.SoftwareVersion
	0,  // 2: carbon.frontend.software.HostSoftwareVersionState.previous:type_name -> carbon.frontend.software.SoftwareVersion
	5,  // 3: carbon.frontend.software.SoftwareVersionState.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 4: carbon.frontend.software.SoftwareVersionState.current:type_name -> carbon.frontend.software.SoftwareVersion
	0,  // 5: carbon.frontend.software.SoftwareVersionState.target:type_name -> carbon.frontend.software.SoftwareVersion
	0,  // 6: carbon.frontend.software.SoftwareVersionState.previous:type_name -> carbon.frontend.software.SoftwareVersion
	1,  // 7: carbon.frontend.software.SoftwareVersionState.host_states:type_name -> carbon.frontend.software.HostSoftwareVersionState
	5,  // 8: carbon.frontend.software.SoftwareVersionStateRequest.ts:type_name -> carbon.frontend.util.Timestamp
	3,  // 9: carbon.frontend.software.SoftwareService.GetNextSoftwareVersionState:input_type -> carbon.frontend.software.SoftwareVersionStateRequest
	4,  // 10: carbon.frontend.software.SoftwareService.UpdateHost:input_type -> carbon.frontend.software.UpdateHostRequest
	6,  // 11: carbon.frontend.software.SoftwareService.Update:input_type -> carbon.frontend.util.Empty
	6,  // 12: carbon.frontend.software.SoftwareService.Revert:input_type -> carbon.frontend.util.Empty
	6,  // 13: carbon.frontend.software.SoftwareService.FixVersionMismatch:input_type -> carbon.frontend.util.Empty
	2,  // 14: carbon.frontend.software.SoftwareService.GetNextSoftwareVersionState:output_type -> carbon.frontend.software.SoftwareVersionState
	6,  // 15: carbon.frontend.software.SoftwareService.UpdateHost:output_type -> carbon.frontend.util.Empty
	6,  // 16: carbon.frontend.software.SoftwareService.Update:output_type -> carbon.frontend.util.Empty
	6,  // 17: carbon.frontend.software.SoftwareService.Revert:output_type -> carbon.frontend.util.Empty
	6,  // 18: carbon.frontend.software.SoftwareService.FixVersionMismatch:output_type -> carbon.frontend.util.Empty
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_frontend_proto_software_proto_init() }
func file_frontend_proto_software_proto_init() {
	if File_frontend_proto_software_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_software_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_software_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostSoftwareVersionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_software_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_software_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersionStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_software_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateHostRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_software_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_software_proto_goTypes,
		DependencyIndexes: file_frontend_proto_software_proto_depIdxs,
		MessageInfos:      file_frontend_proto_software_proto_msgTypes,
	}.Build()
	File_frontend_proto_software_proto = out.File
	file_frontend_proto_software_proto_rawDesc = nil
	file_frontend_proto_software_proto_goTypes = nil
	file_frontend_proto_software_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/alarm.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AlarmService_GetNextAlarmList_FullMethodName          = "/carbon.frontend.alarm.AlarmService/GetNextAlarmList"
	AlarmService_GetNextAlarmCount_FullMethodName         = "/carbon.frontend.alarm.AlarmService/GetNextAlarmCount"
	AlarmService_GetNextNewAlarmList_FullMethodName       = "/carbon.frontend.alarm.AlarmService/GetNextNewAlarmList"
	AlarmService_AcknowledgeAlarm_FullMethodName          = "/carbon.frontend.alarm.AlarmService/AcknowledgeAlarm"
	AlarmService_ResetAlarms_FullMethodName               = "/carbon.frontend.alarm.AlarmService/ResetAlarms"
	AlarmService_GetNextAlarmLog_FullMethodName           = "/carbon.frontend.alarm.AlarmService/GetNextAlarmLog"
	AlarmService_GetNextAlarmLogCount_FullMethodName      = "/carbon.frontend.alarm.AlarmService/GetNextAlarmLogCount"
	AlarmService_AttemptAutofixAlarm_FullMethodName       = "/carbon.frontend.alarm.AlarmService/AttemptAutofixAlarm"
	AlarmService_GetNextAutofixAlarmStatus_FullMethodName = "/carbon.frontend.alarm.AlarmService/GetNextAutofixAlarmStatus"
)

// AlarmServiceClient is the client API for AlarmService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AlarmServiceClient interface {
	GetNextAlarmList(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*AlarmTable, error)
	GetNextAlarmCount(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*AlarmCount, error)
	GetNextNewAlarmList(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*AlarmTable, error)
	AcknowledgeAlarm(ctx context.Context, in *AcknowledgeRequest, opts ...grpc.CallOption) (*Empty, error)
	ResetAlarms(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetNextAlarmLog(ctx context.Context, in *GetNextAlarmLogRequest, opts ...grpc.CallOption) (*GetNextAlarmLogResponse, error)
	GetNextAlarmLogCount(ctx context.Context, in *GetNextAlarmLogCountRequest, opts ...grpc.CallOption) (*GetNextAlarmLogCountResponse, error)
	AttemptAutofixAlarm(ctx context.Context, in *AttemptAutofixAlarmRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextAutofixAlarmStatus(ctx context.Context, in *GetNextAutofixAlarmStatusRequest, opts ...grpc.CallOption) (*GetNextAutofixAlarmStatusResponse, error)
}

type alarmServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAlarmServiceClient(cc grpc.ClientConnInterface) AlarmServiceClient {
	return &alarmServiceClient{cc}
}

func (c *alarmServiceClient) GetNextAlarmList(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*AlarmTable, error) {
	out := new(AlarmTable)
	err := c.cc.Invoke(ctx, AlarmService_GetNextAlarmList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) GetNextAlarmCount(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*AlarmCount, error) {
	out := new(AlarmCount)
	err := c.cc.Invoke(ctx, AlarmService_GetNextAlarmCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) GetNextNewAlarmList(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*AlarmTable, error) {
	out := new(AlarmTable)
	err := c.cc.Invoke(ctx, AlarmService_GetNextNewAlarmList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) AcknowledgeAlarm(ctx context.Context, in *AcknowledgeRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlarmService_AcknowledgeAlarm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) ResetAlarms(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlarmService_ResetAlarms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) GetNextAlarmLog(ctx context.Context, in *GetNextAlarmLogRequest, opts ...grpc.CallOption) (*GetNextAlarmLogResponse, error) {
	out := new(GetNextAlarmLogResponse)
	err := c.cc.Invoke(ctx, AlarmService_GetNextAlarmLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) GetNextAlarmLogCount(ctx context.Context, in *GetNextAlarmLogCountRequest, opts ...grpc.CallOption) (*GetNextAlarmLogCountResponse, error) {
	out := new(GetNextAlarmLogCountResponse)
	err := c.cc.Invoke(ctx, AlarmService_GetNextAlarmLogCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) AttemptAutofixAlarm(ctx context.Context, in *AttemptAutofixAlarmRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AlarmService_AttemptAutofixAlarm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alarmServiceClient) GetNextAutofixAlarmStatus(ctx context.Context, in *GetNextAutofixAlarmStatusRequest, opts ...grpc.CallOption) (*GetNextAutofixAlarmStatusResponse, error) {
	out := new(GetNextAutofixAlarmStatusResponse)
	err := c.cc.Invoke(ctx, AlarmService_GetNextAutofixAlarmStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AlarmServiceServer is the server API for AlarmService service.
// All implementations must embed UnimplementedAlarmServiceServer
// for forward compatibility
type AlarmServiceServer interface {
	GetNextAlarmList(context.Context, *Timestamp) (*AlarmTable, error)
	GetNextAlarmCount(context.Context, *Timestamp) (*AlarmCount, error)
	GetNextNewAlarmList(context.Context, *Timestamp) (*AlarmTable, error)
	AcknowledgeAlarm(context.Context, *AcknowledgeRequest) (*Empty, error)
	ResetAlarms(context.Context, *Empty) (*Empty, error)
	GetNextAlarmLog(context.Context, *GetNextAlarmLogRequest) (*GetNextAlarmLogResponse, error)
	GetNextAlarmLogCount(context.Context, *GetNextAlarmLogCountRequest) (*GetNextAlarmLogCountResponse, error)
	AttemptAutofixAlarm(context.Context, *AttemptAutofixAlarmRequest) (*Empty, error)
	GetNextAutofixAlarmStatus(context.Context, *GetNextAutofixAlarmStatusRequest) (*GetNextAutofixAlarmStatusResponse, error)
	mustEmbedUnimplementedAlarmServiceServer()
}

// UnimplementedAlarmServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAlarmServiceServer struct {
}

func (UnimplementedAlarmServiceServer) GetNextAlarmList(context.Context, *Timestamp) (*AlarmTable, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAlarmList not implemented")
}
func (UnimplementedAlarmServiceServer) GetNextAlarmCount(context.Context, *Timestamp) (*AlarmCount, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAlarmCount not implemented")
}
func (UnimplementedAlarmServiceServer) GetNextNewAlarmList(context.Context, *Timestamp) (*AlarmTable, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextNewAlarmList not implemented")
}
func (UnimplementedAlarmServiceServer) AcknowledgeAlarm(context.Context, *AcknowledgeRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcknowledgeAlarm not implemented")
}
func (UnimplementedAlarmServiceServer) ResetAlarms(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetAlarms not implemented")
}
func (UnimplementedAlarmServiceServer) GetNextAlarmLog(context.Context, *GetNextAlarmLogRequest) (*GetNextAlarmLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAlarmLog not implemented")
}
func (UnimplementedAlarmServiceServer) GetNextAlarmLogCount(context.Context, *GetNextAlarmLogCountRequest) (*GetNextAlarmLogCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAlarmLogCount not implemented")
}
func (UnimplementedAlarmServiceServer) AttemptAutofixAlarm(context.Context, *AttemptAutofixAlarmRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AttemptAutofixAlarm not implemented")
}
func (UnimplementedAlarmServiceServer) GetNextAutofixAlarmStatus(context.Context, *GetNextAutofixAlarmStatusRequest) (*GetNextAutofixAlarmStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAutofixAlarmStatus not implemented")
}
func (UnimplementedAlarmServiceServer) mustEmbedUnimplementedAlarmServiceServer() {}

// UnsafeAlarmServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AlarmServiceServer will
// result in compilation errors.
type UnsafeAlarmServiceServer interface {
	mustEmbedUnimplementedAlarmServiceServer()
}

func RegisterAlarmServiceServer(s grpc.ServiceRegistrar, srv AlarmServiceServer) {
	s.RegisterService(&AlarmService_ServiceDesc, srv)
}

func _AlarmService_GetNextAlarmList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).GetNextAlarmList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_GetNextAlarmList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).GetNextAlarmList(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_GetNextAlarmCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).GetNextAlarmCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_GetNextAlarmCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).GetNextAlarmCount(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_GetNextNewAlarmList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).GetNextNewAlarmList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_GetNextNewAlarmList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).GetNextNewAlarmList(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_AcknowledgeAlarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcknowledgeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).AcknowledgeAlarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_AcknowledgeAlarm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).AcknowledgeAlarm(ctx, req.(*AcknowledgeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_ResetAlarms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).ResetAlarms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_ResetAlarms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).ResetAlarms(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_GetNextAlarmLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextAlarmLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).GetNextAlarmLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_GetNextAlarmLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).GetNextAlarmLog(ctx, req.(*GetNextAlarmLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_GetNextAlarmLogCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextAlarmLogCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).GetNextAlarmLogCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_GetNextAlarmLogCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).GetNextAlarmLogCount(ctx, req.(*GetNextAlarmLogCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_AttemptAutofixAlarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttemptAutofixAlarmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).AttemptAutofixAlarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_AttemptAutofixAlarm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).AttemptAutofixAlarm(ctx, req.(*AttemptAutofixAlarmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AlarmService_GetNextAutofixAlarmStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextAutofixAlarmStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlarmServiceServer).GetNextAutofixAlarmStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AlarmService_GetNextAutofixAlarmStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlarmServiceServer).GetNextAutofixAlarmStatus(ctx, req.(*GetNextAutofixAlarmStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AlarmService_ServiceDesc is the grpc.ServiceDesc for AlarmService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AlarmService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.alarm.AlarmService",
	HandlerType: (*AlarmServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextAlarmList",
			Handler:    _AlarmService_GetNextAlarmList_Handler,
		},
		{
			MethodName: "GetNextAlarmCount",
			Handler:    _AlarmService_GetNextAlarmCount_Handler,
		},
		{
			MethodName: "GetNextNewAlarmList",
			Handler:    _AlarmService_GetNextNewAlarmList_Handler,
		},
		{
			MethodName: "AcknowledgeAlarm",
			Handler:    _AlarmService_AcknowledgeAlarm_Handler,
		},
		{
			MethodName: "ResetAlarms",
			Handler:    _AlarmService_ResetAlarms_Handler,
		},
		{
			MethodName: "GetNextAlarmLog",
			Handler:    _AlarmService_GetNextAlarmLog_Handler,
		},
		{
			MethodName: "GetNextAlarmLogCount",
			Handler:    _AlarmService_GetNextAlarmLogCount_Handler,
		},
		{
			MethodName: "AttemptAutofixAlarm",
			Handler:    _AlarmService_AttemptAutofixAlarm_Handler,
		},
		{
			MethodName: "GetNextAutofixAlarmStatus",
			Handler:    _AlarmService_GetNextAutofixAlarmStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/alarm.proto",
}

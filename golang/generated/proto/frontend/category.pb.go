// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/category.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	category "github.com/carbonrobotics/robot/golang/generated/proto/category"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNextCategoryDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextCategoryDataRequest) Reset() {
	*x = GetNextCategoryDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCategoryDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCategoryDataRequest) ProtoMessage() {}

func (x *GetNextCategoryDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCategoryDataRequest.ProtoReflect.Descriptor instead.
func (*GetNextCategoryDataRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_proto_rawDescGZIP(), []int{0}
}

func (x *GetNextCategoryDataRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextCategoryDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts         *Timestamp           `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Categories []*category.Category `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *GetNextCategoryDataResponse) Reset() {
	*x = GetNextCategoryDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_category_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCategoryDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCategoryDataResponse) ProtoMessage() {}

func (x *GetNextCategoryDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_category_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCategoryDataResponse.ProtoReflect.Descriptor instead.
func (*GetNextCategoryDataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_category_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextCategoryDataResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextCategoryDataResponse) GetCategories() []*category.Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_frontend_proto_category_proto protoreflect.FileDescriptor

var file_frontend_proto_category_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x4d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02,
	0x74, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x02, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x32, 0x96,
	0x01, 0x0a, 0x0f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_frontend_proto_category_proto_rawDescOnce sync.Once
	file_frontend_proto_category_proto_rawDescData = file_frontend_proto_category_proto_rawDesc
)

func file_frontend_proto_category_proto_rawDescGZIP() []byte {
	file_frontend_proto_category_proto_rawDescOnce.Do(func() {
		file_frontend_proto_category_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_category_proto_rawDescData)
	})
	return file_frontend_proto_category_proto_rawDescData
}

var file_frontend_proto_category_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_frontend_proto_category_proto_goTypes = []interface{}{
	(*GetNextCategoryDataRequest)(nil),  // 0: carbon.frontend.category.GetNextCategoryDataRequest
	(*GetNextCategoryDataResponse)(nil), // 1: carbon.frontend.category.GetNextCategoryDataResponse
	(*Timestamp)(nil),                   // 2: carbon.frontend.util.Timestamp
	(*category.Category)(nil),           // 3: carbon.category.Category
}
var file_frontend_proto_category_proto_depIdxs = []int32{
	2, // 0: carbon.frontend.category.GetNextCategoryDataRequest.ts:type_name -> carbon.frontend.util.Timestamp
	2, // 1: carbon.frontend.category.GetNextCategoryDataResponse.ts:type_name -> carbon.frontend.util.Timestamp
	3, // 2: carbon.frontend.category.GetNextCategoryDataResponse.categories:type_name -> carbon.category.Category
	0, // 3: carbon.frontend.category.CategoryService.GetNextCategoryData:input_type -> carbon.frontend.category.GetNextCategoryDataRequest
	1, // 4: carbon.frontend.category.CategoryService.GetNextCategoryData:output_type -> carbon.frontend.category.GetNextCategoryDataResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_frontend_proto_category_proto_init() }
func file_frontend_proto_category_proto_init() {
	if File_frontend_proto_category_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_category_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCategoryDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_category_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCategoryDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_category_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_category_proto_goTypes,
		DependencyIndexes: file_frontend_proto_category_proto_depIdxs,
		MessageInfos:      file_frontend_proto_category_proto_msgTypes,
	}.Build()
	File_frontend_proto_category_proto = out.File
	file_frontend_proto_category_proto_rawDesc = nil
	file_frontend_proto_category_proto_goTypes = nil
	file_frontend_proto_category_proto_depIdxs = nil
}

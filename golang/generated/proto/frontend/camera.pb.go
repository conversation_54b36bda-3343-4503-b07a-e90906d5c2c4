// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/camera.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CameraType int32

const (
	CameraType_ANY     CameraType = 0
	CameraType_PREDICT CameraType = 1
	CameraType_TARGET  CameraType = 2
	CameraType_KILLCAM CameraType = 3
)

// Enum value maps for CameraType.
var (
	CameraType_name = map[int32]string{
		0: "ANY",
		1: "PREDICT",
		2: "TARGET",
		3: "KILLCAM",
	}
	CameraType_value = map[string]int32{
		"ANY":     0,
		"PREDICT": 1,
		"TARGET":  2,
		"KILLCAM": 3,
	}
)

func (x CameraType) Enum() *CameraType {
	p := new(CameraType)
	*p = x
	return p
}

func (x CameraType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CameraType) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_camera_proto_enumTypes[0].Descriptor()
}

func (CameraType) Type() protoreflect.EnumType {
	return &file_frontend_proto_camera_proto_enumTypes[0]
}

func (x CameraType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CameraType.Descriptor instead.
func (CameraType) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{0}
}

type CameraRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *CameraRequest) Reset() {
	*x = CameraRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_camera_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraRequest) ProtoMessage() {}

func (x *CameraRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_camera_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraRequest.ProtoReflect.Descriptor instead.
func (*CameraRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{0}
}

func (x *CameraRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type Camera struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowNumber     uint32     `protobuf:"varint,1,opt,name=row_number,json=rowNumber,proto3" json:"row_number,omitempty"`
	CameraId      string     `protobuf:"bytes,2,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	Type          CameraType `protobuf:"varint,3,opt,name=type,proto3,enum=carbon.frontend.camera.CameraType" json:"type,omitempty"`
	AutoFocusable bool       `protobuf:"varint,4,opt,name=auto_focusable,json=autoFocusable,proto3" json:"auto_focusable,omitempty"`
	StreamHost    string     `protobuf:"bytes,5,opt,name=stream_host,json=streamHost,proto3" json:"stream_host,omitempty"`
	StreamPort    uint32     `protobuf:"varint,6,opt,name=stream_port,json=streamPort,proto3" json:"stream_port,omitempty"`
	Width         uint32     `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`
	Height        uint32     `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"`
	Transpose     bool       `protobuf:"varint,9,opt,name=transpose,proto3" json:"transpose,omitempty"`
	Connected     bool       `protobuf:"varint,10,opt,name=connected,proto3" json:"connected,omitempty"`
	RtcInfo       *RTCInfo   `protobuf:"bytes,11,opt,name=rtc_info,json=rtcInfo,proto3" json:"rtc_info,omitempty"`
}

func (x *Camera) Reset() {
	*x = Camera{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_camera_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Camera) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Camera) ProtoMessage() {}

func (x *Camera) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_camera_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Camera.ProtoReflect.Descriptor instead.
func (*Camera) Descriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{1}
}

func (x *Camera) GetRowNumber() uint32 {
	if x != nil {
		return x.RowNumber
	}
	return 0
}

func (x *Camera) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *Camera) GetType() CameraType {
	if x != nil {
		return x.Type
	}
	return CameraType_ANY
}

func (x *Camera) GetAutoFocusable() bool {
	if x != nil {
		return x.AutoFocusable
	}
	return false
}

func (x *Camera) GetStreamHost() string {
	if x != nil {
		return x.StreamHost
	}
	return ""
}

func (x *Camera) GetStreamPort() uint32 {
	if x != nil {
		return x.StreamPort
	}
	return 0
}

func (x *Camera) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Camera) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Camera) GetTranspose() bool {
	if x != nil {
		return x.Transpose
	}
	return false
}

func (x *Camera) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *Camera) GetRtcInfo() *RTCInfo {
	if x != nil {
		return x.RtcInfo
	}
	return nil
}

type RTCInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostId   string `protobuf:"bytes,1,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`
	StreamId string `protobuf:"bytes,2,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
}

func (x *RTCInfo) Reset() {
	*x = RTCInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_camera_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTCInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCInfo) ProtoMessage() {}

func (x *RTCInfo) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_camera_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCInfo.ProtoReflect.Descriptor instead.
func (*RTCInfo) Descriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{2}
}

func (x *RTCInfo) GetHostId() string {
	if x != nil {
		return x.HostId
	}
	return ""
}

func (x *RTCInfo) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

type CameraList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cameras []*Camera  `protobuf:"bytes,1,rep,name=cameras,proto3" json:"cameras,omitempty"`
	Ts      *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *CameraList) Reset() {
	*x = CameraList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_camera_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraList) ProtoMessage() {}

func (x *CameraList) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_camera_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraList.ProtoReflect.Descriptor instead.
func (*CameraList) Descriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{3}
}

func (x *CameraList) GetCameras() []*Camera {
	if x != nil {
		return x.Cameras
	}
	return nil
}

func (x *CameraList) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type CameraListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                CameraType `protobuf:"varint,1,opt,name=type,proto3,enum=carbon.frontend.camera.CameraType" json:"type,omitempty"`
	IncludeDisconnected bool       `protobuf:"varint,2,opt,name=include_disconnected,json=includeDisconnected,proto3" json:"include_disconnected,omitempty"`
}

func (x *CameraListRequest) Reset() {
	*x = CameraListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_camera_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraListRequest) ProtoMessage() {}

func (x *CameraListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_camera_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraListRequest.ProtoReflect.Descriptor instead.
func (*CameraListRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{4}
}

func (x *CameraListRequest) GetType() CameraType {
	if x != nil {
		return x.Type
	}
	return CameraType_ANY
}

func (x *CameraListRequest) GetIncludeDisconnected() bool {
	if x != nil {
		return x.IncludeDisconnected
	}
	return false
}

type NextCameraListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                CameraType `protobuf:"varint,1,opt,name=type,proto3,enum=carbon.frontend.camera.CameraType" json:"type,omitempty"`
	Ts                  *Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	IncludeDisconnected bool       `protobuf:"varint,3,opt,name=include_disconnected,json=includeDisconnected,proto3" json:"include_disconnected,omitempty"`
}

func (x *NextCameraListRequest) Reset() {
	*x = NextCameraListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_camera_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NextCameraListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NextCameraListRequest) ProtoMessage() {}

func (x *NextCameraListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_camera_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NextCameraListRequest.ProtoReflect.Descriptor instead.
func (*NextCameraListRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_camera_proto_rawDescGZIP(), []int{5}
}

func (x *NextCameraListRequest) GetType() CameraType {
	if x != nil {
		return x.Type
	}
	return CameraType_ANY
}

func (x *NextCameraListRequest) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *NextCameraListRequest) GetIncludeDisconnected() bool {
	if x != nil {
		return x.IncludeDisconnected
	}
	return false
}

var File_frontend_proto_camera_proto protoreflect.FileDescriptor

var file_frontend_proto_camera_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x26, 0x0a, 0x0d, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x8b, 0x03, 0x0a, 0x06, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x66, 0x6f, 0x63, 0x75, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x61, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x48, 0x6f, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x50, 0x6f, 0x72, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x3a, 0x0a, 0x08, 0x72, 0x74,
	0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x52, 0x54, 0x43, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72,
	0x74, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x3f, 0x0a, 0x07, 0x52, 0x54, 0x43, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x0a, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x12,
	0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x22, 0x7e, 0x0a, 0x11, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a,
	0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x22, 0xb3, 0x01, 0x0a, 0x15, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x02, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x2a, 0x3b, 0x0a, 0x0a, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x59, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x50, 0x52, 0x45, 0x44, 0x49, 0x43, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41,
	0x52, 0x47, 0x45, 0x54, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4b, 0x49, 0x4c, 0x4c, 0x43, 0x41,
	0x4d, 0x10, 0x03, 0x32, 0xd7, 0x01, 0x0a, 0x0d, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x66, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x2e, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x10, 0x5a,
	0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_camera_proto_rawDescOnce sync.Once
	file_frontend_proto_camera_proto_rawDescData = file_frontend_proto_camera_proto_rawDesc
)

func file_frontend_proto_camera_proto_rawDescGZIP() []byte {
	file_frontend_proto_camera_proto_rawDescOnce.Do(func() {
		file_frontend_proto_camera_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_camera_proto_rawDescData)
	})
	return file_frontend_proto_camera_proto_rawDescData
}

var file_frontend_proto_camera_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_frontend_proto_camera_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_frontend_proto_camera_proto_goTypes = []interface{}{
	(CameraType)(0),               // 0: carbon.frontend.camera.CameraType
	(*CameraRequest)(nil),         // 1: carbon.frontend.camera.CameraRequest
	(*Camera)(nil),                // 2: carbon.frontend.camera.Camera
	(*RTCInfo)(nil),               // 3: carbon.frontend.camera.RTCInfo
	(*CameraList)(nil),            // 4: carbon.frontend.camera.CameraList
	(*CameraListRequest)(nil),     // 5: carbon.frontend.camera.CameraListRequest
	(*NextCameraListRequest)(nil), // 6: carbon.frontend.camera.NextCameraListRequest
	(*Timestamp)(nil),             // 7: carbon.frontend.util.Timestamp
}
var file_frontend_proto_camera_proto_depIdxs = []int32{
	0, // 0: carbon.frontend.camera.Camera.type:type_name -> carbon.frontend.camera.CameraType
	3, // 1: carbon.frontend.camera.Camera.rtc_info:type_name -> carbon.frontend.camera.RTCInfo
	2, // 2: carbon.frontend.camera.CameraList.cameras:type_name -> carbon.frontend.camera.Camera
	7, // 3: carbon.frontend.camera.CameraList.ts:type_name -> carbon.frontend.util.Timestamp
	0, // 4: carbon.frontend.camera.CameraListRequest.type:type_name -> carbon.frontend.camera.CameraType
	0, // 5: carbon.frontend.camera.NextCameraListRequest.type:type_name -> carbon.frontend.camera.CameraType
	7, // 6: carbon.frontend.camera.NextCameraListRequest.ts:type_name -> carbon.frontend.util.Timestamp
	5, // 7: carbon.frontend.camera.CameraService.GetCameraList:input_type -> carbon.frontend.camera.CameraListRequest
	6, // 8: carbon.frontend.camera.CameraService.GetNextCameraList:input_type -> carbon.frontend.camera.NextCameraListRequest
	4, // 9: carbon.frontend.camera.CameraService.GetCameraList:output_type -> carbon.frontend.camera.CameraList
	4, // 10: carbon.frontend.camera.CameraService.GetNextCameraList:output_type -> carbon.frontend.camera.CameraList
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_frontend_proto_camera_proto_init() }
func file_frontend_proto_camera_proto_init() {
	if File_frontend_proto_camera_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_camera_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_camera_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Camera); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_camera_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTCInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_camera_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_camera_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_camera_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NextCameraListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_camera_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_camera_proto_goTypes,
		DependencyIndexes: file_frontend_proto_camera_proto_depIdxs,
		EnumInfos:         file_frontend_proto_camera_proto_enumTypes,
		MessageInfos:      file_frontend_proto_camera_proto_msgTypes,
	}.Build()
	File_frontend_proto_camera_proto = out.File
	file_frontend_proto_camera_proto_rawDesc = nil
	file_frontend_proto_camera_proto_goTypes = nil
	file_frontend_proto_camera_proto_depIdxs = nil
}

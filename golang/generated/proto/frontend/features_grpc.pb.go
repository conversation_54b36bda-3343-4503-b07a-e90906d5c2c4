// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/features.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FeatureService_GetNextFeatureFlags_FullMethodName   = "/carbon.frontend.features.FeatureService/GetNextFeatureFlags"
	FeatureService_GetRobotConfiguration_FullMethodName = "/carbon.frontend.features.FeatureService/GetRobotConfiguration"
)

// FeatureServiceClient is the client API for FeatureService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FeatureServiceClient interface {
	GetNextFeatureFlags(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*FeatureFlags, error)
	GetRobotConfiguration(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*RobotConfiguration, error)
}

type featureServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFeatureServiceClient(cc grpc.ClientConnInterface) FeatureServiceClient {
	return &featureServiceClient{cc}
}

func (c *featureServiceClient) GetNextFeatureFlags(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*FeatureFlags, error) {
	out := new(FeatureFlags)
	err := c.cc.Invoke(ctx, FeatureService_GetNextFeatureFlags_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *featureServiceClient) GetRobotConfiguration(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*RobotConfiguration, error) {
	out := new(RobotConfiguration)
	err := c.cc.Invoke(ctx, FeatureService_GetRobotConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeatureServiceServer is the server API for FeatureService service.
// All implementations must embed UnimplementedFeatureServiceServer
// for forward compatibility
type FeatureServiceServer interface {
	GetNextFeatureFlags(context.Context, *Timestamp) (*FeatureFlags, error)
	GetRobotConfiguration(context.Context, *Empty) (*RobotConfiguration, error)
	mustEmbedUnimplementedFeatureServiceServer()
}

// UnimplementedFeatureServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFeatureServiceServer struct {
}

func (UnimplementedFeatureServiceServer) GetNextFeatureFlags(context.Context, *Timestamp) (*FeatureFlags, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextFeatureFlags not implemented")
}
func (UnimplementedFeatureServiceServer) GetRobotConfiguration(context.Context, *Empty) (*RobotConfiguration, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRobotConfiguration not implemented")
}
func (UnimplementedFeatureServiceServer) mustEmbedUnimplementedFeatureServiceServer() {}

// UnsafeFeatureServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FeatureServiceServer will
// result in compilation errors.
type UnsafeFeatureServiceServer interface {
	mustEmbedUnimplementedFeatureServiceServer()
}

func RegisterFeatureServiceServer(s grpc.ServiceRegistrar, srv FeatureServiceServer) {
	s.RegisterService(&FeatureService_ServiceDesc, srv)
}

func _FeatureService_GetNextFeatureFlags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeatureServiceServer).GetNextFeatureFlags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FeatureService_GetNextFeatureFlags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeatureServiceServer).GetNextFeatureFlags(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeatureService_GetRobotConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeatureServiceServer).GetRobotConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FeatureService_GetRobotConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeatureServiceServer).GetRobotConfiguration(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// FeatureService_ServiceDesc is the grpc.ServiceDesc for FeatureService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FeatureService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.features.FeatureService",
	HandlerType: (*FeatureServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextFeatureFlags",
			Handler:    _FeatureService_GetNextFeatureFlags_Handler,
		},
		{
			MethodName: "GetRobotConfiguration",
			Handler:    _FeatureService_GetRobotConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/features.proto",
}

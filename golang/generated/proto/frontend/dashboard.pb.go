// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/dashboard.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SafetyOverrideState int32

const (
	SafetyOverrideState_SafetyOverrideNone         SafetyOverrideState = 0
	SafetyOverrideState_SafetyOverrideVelocityStop SafetyOverrideState = 1
)

// Enum value maps for SafetyOverrideState.
var (
	SafetyOverrideState_name = map[int32]string{
		0: "SafetyOverrideNone",
		1: "SafetyOverrideVelocityStop",
	}
	SafetyOverrideState_value = map[string]int32{
		"SafetyOverrideNone":         0,
		"SafetyOverrideVelocityStop": 1,
	}
)

func (x SafetyOverrideState) Enum() *SafetyOverrideState {
	p := new(SafetyOverrideState)
	*p = x
	return p
}

func (x SafetyOverrideState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SafetyOverrideState) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_dashboard_proto_enumTypes[0].Descriptor()
}

func (SafetyOverrideState) Type() protoreflect.EnumType {
	return &file_frontend_proto_dashboard_proto_enumTypes[0]
}

func (x SafetyOverrideState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SafetyOverrideState.Descriptor instead.
func (SafetyOverrideState) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{0}
}

type ImplementState int32

const (
	ImplementState_RAISED  ImplementState = 0
	ImplementState_LOWERED ImplementState = 1
)

// Enum value maps for ImplementState.
var (
	ImplementState_name = map[int32]string{
		0: "RAISED",
		1: "LOWERED",
	}
	ImplementState_value = map[string]int32{
		"RAISED":  0,
		"LOWERED": 1,
	}
)

func (x ImplementState) Enum() *ImplementState {
	p := new(ImplementState)
	*p = x
	return p
}

func (x ImplementState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImplementState) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_dashboard_proto_enumTypes[1].Descriptor()
}

func (ImplementState) Type() protoreflect.EnumType {
	return &file_frontend_proto_dashboard_proto_enumTypes[1]
}

func (x ImplementState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImplementState.Descriptor instead.
func (ImplementState) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{1}
}

type ExtraStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       string  `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`                                // top text
	IconName    string  `protobuf:"bytes,2,opt,name=icon_name,json=iconName,proto3" json:"icon_name,omitempty"`          // center icon
	IconColor   string  `protobuf:"bytes,3,opt,name=icon_color,json=iconColor,proto3" json:"icon_color,omitempty"`       // center icon color
	StatusText  string  `protobuf:"bytes,4,opt,name=status_text,json=statusText,proto3" json:"status_text,omitempty"`    // middle text if no icon
	StatusColor string  `protobuf:"bytes,5,opt,name=status_color,json=statusColor,proto3" json:"status_color,omitempty"` // middle text color if no icon
	GroupId     string  `protobuf:"bytes,6,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`             // group to cycle through in a single slot
	SectionId   string  `protobuf:"bytes,7,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`       // section to display this status in
	Progress    float64 `protobuf:"fixed64,8,opt,name=progress,proto3" json:"progress,omitempty"`                        // show background progress if set
	Width       uint32  `protobuf:"varint,9,opt,name=width,proto3" json:"width,omitempty"`                               // column width, 1 by default
	BottomText  string  `protobuf:"bytes,10,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`   // bottom text if no icon
}

func (x *ExtraStatus) Reset() {
	*x = ExtraStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraStatus) ProtoMessage() {}

func (x *ExtraStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraStatus.ProtoReflect.Descriptor instead.
func (*ExtraStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{0}
}

func (x *ExtraStatus) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExtraStatus) GetIconName() string {
	if x != nil {
		return x.IconName
	}
	return ""
}

func (x *ExtraStatus) GetIconColor() string {
	if x != nil {
		return x.IconColor
	}
	return ""
}

func (x *ExtraStatus) GetStatusText() string {
	if x != nil {
		return x.StatusText
	}
	return ""
}

func (x *ExtraStatus) GetStatusColor() string {
	if x != nil {
		return x.StatusColor
	}
	return ""
}

func (x *ExtraStatus) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *ExtraStatus) GetSectionId() string {
	if x != nil {
		return x.SectionId
	}
	return ""
}

func (x *ExtraStatus) GetProgress() float64 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *ExtraStatus) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ExtraStatus) GetBottomText() string {
	if x != nil {
		return x.BottomText
	}
	return ""
}

type WeedTargeting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *WeedTargeting) Reset() {
	*x = WeedTargeting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedTargeting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedTargeting) ProtoMessage() {}

func (x *WeedTargeting) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedTargeting.ProtoReflect.Descriptor instead.
func (*WeedTargeting) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{1}
}

func (x *WeedTargeting) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type ThinningTargeting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	Algorithm uint64 `protobuf:"varint,2,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
}

func (x *ThinningTargeting) Reset() {
	*x = ThinningTargeting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThinningTargeting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThinningTargeting) ProtoMessage() {}

func (x *ThinningTargeting) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThinningTargeting.ProtoReflect.Descriptor instead.
func (*ThinningTargeting) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{2}
}

func (x *ThinningTargeting) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *ThinningTargeting) GetAlgorithm() uint64 {
	if x != nil {
		return x.Algorithm
	}
	return 0
}

type TargetingState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedState     *WeedTargeting     `protobuf:"bytes,1,opt,name=weed_state,json=weedState,proto3" json:"weed_state,omitempty"`
	ThinningState *ThinningTargeting `protobuf:"bytes,2,opt,name=thinning_state,json=thinningState,proto3" json:"thinning_state,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	Enabled     []bool         `protobuf:"varint,3,rep,packed,name=enabled,proto3" json:"enabled,omitempty"`
	EnabledRows map[int32]bool `protobuf:"bytes,4,rep,name=enabled_rows,json=enabledRows,proto3" json:"enabled_rows,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *TargetingState) Reset() {
	*x = TargetingState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetingState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetingState) ProtoMessage() {}

func (x *TargetingState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetingState.ProtoReflect.Descriptor instead.
func (*TargetingState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{3}
}

func (x *TargetingState) GetWeedState() *WeedTargeting {
	if x != nil {
		return x.WeedState
	}
	return nil
}

func (x *TargetingState) GetThinningState() *ThinningTargeting {
	if x != nil {
		return x.ThinningState
	}
	return nil
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *TargetingState) GetEnabled() []bool {
	if x != nil {
		return x.Enabled
	}
	return nil
}

func (x *TargetingState) GetEnabledRows() map[int32]bool {
	if x != nil {
		return x.EnabledRows
	}
	return nil
}

type ExtraConclusion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                  string        `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FlipThresholds         bool          `protobuf:"varint,2,opt,name=flip_thresholds,json=flipThresholds,proto3" json:"flip_thresholds,omitempty"`
	GoodThresholdPercent   uint32        `protobuf:"varint,3,opt,name=good_threshold_percent,json=goodThresholdPercent,proto3" json:"good_threshold_percent,omitempty"`
	MediumThresholdPercent uint32        `protobuf:"varint,4,opt,name=medium_threshold_percent,json=mediumThresholdPercent,proto3" json:"medium_threshold_percent,omitempty"`
	Percent                *PercentValue `protobuf:"bytes,5,opt,name=percent,proto3" json:"percent,omitempty"`
}

func (x *ExtraConclusion) Reset() {
	*x = ExtraConclusion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraConclusion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraConclusion) ProtoMessage() {}

func (x *ExtraConclusion) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraConclusion.ProtoReflect.Descriptor instead.
func (*ExtraConclusion) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{4}
}

func (x *ExtraConclusion) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExtraConclusion) GetFlipThresholds() bool {
	if x != nil {
		return x.FlipThresholds
	}
	return false
}

func (x *ExtraConclusion) GetGoodThresholdPercent() uint32 {
	if x != nil {
		return x.GoodThresholdPercent
	}
	return 0
}

func (x *ExtraConclusion) GetMediumThresholdPercent() uint32 {
	if x != nil {
		return x.MediumThresholdPercent
	}
	return 0
}

func (x *ExtraConclusion) GetPercent() *PercentValue {
	if x != nil {
		return x.Percent
	}
	return nil
}

type RowStateMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled             bool                `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	TargetStateMismatch bool                `protobuf:"varint,2,opt,name=target_state_mismatch,json=targetStateMismatch,proto3" json:"target_state_mismatch,omitempty"`
	Ready               bool                `protobuf:"varint,3,opt,name=ready,proto3" json:"ready,omitempty"`
	SafetyOverrideState SafetyOverrideState `protobuf:"varint,4,opt,name=safety_override_state,json=safetyOverrideState,proto3,enum=carbon.frontend.dashboard.SafetyOverrideState" json:"safety_override_state,omitempty"`
}

func (x *RowStateMessage) Reset() {
	*x = RowStateMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowStateMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowStateMessage) ProtoMessage() {}

func (x *RowStateMessage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowStateMessage.ProtoReflect.Descriptor instead.
func (*RowStateMessage) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{5}
}

func (x *RowStateMessage) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *RowStateMessage) GetTargetStateMismatch() bool {
	if x != nil {
		return x.TargetStateMismatch
	}
	return false
}

func (x *RowStateMessage) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *RowStateMessage) GetSafetyOverrideState() SafetyOverrideState {
	if x != nil {
		return x.SafetyOverrideState
	}
	return SafetyOverrideState_SafetyOverrideNone
}

type DashboardStateMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts            *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	LasersEnabled bool       `protobuf:"varint,2,opt,name=lasers_enabled,json=lasersEnabled,proto3" json:"lasers_enabled,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	RowEnabled     []bool          `protobuf:"varint,3,rep,packed,name=row_enabled,json=rowEnabled,proto3" json:"row_enabled,omitempty"` // Use row_states.enabled
	Extras         []*ExtraStatus  `protobuf:"bytes,4,rep,name=extras,proto3" json:"extras,omitempty"`
	SelectedModel  *CropModel      `protobuf:"bytes,5,opt,name=selected_model,json=selectedModel,proto3" json:"selected_model,omitempty"`
	TargetingState *TargetingState `protobuf:"bytes,6,opt,name=targeting_state,json=targetingState,proto3" json:"targeting_state,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	TargetStateMismatch []bool `protobuf:"varint,7,rep,packed,name=target_state_mismatch,json=targetStateMismatch,proto3" json:"target_state_mismatch,omitempty"` // use row_states.target_state_mismatch
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	RowReady []bool `protobuf:"varint,8,rep,packed,name=row_ready,json=rowReady,proto3" json:"row_ready,omitempty"` // use row_states.ready
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	RowExists  []bool  `protobuf:"varint,9,rep,packed,name=row_exists,json=rowExists,proto3" json:"row_exists,omitempty"` // row exists if key exists in row_states
	RowWidthIn float64 `protobuf:"fixed64,10,opt,name=row_width_in,json=rowWidthIn,proto3" json:"row_width_in,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	SafetyOverrideState    []SafetyOverrideState      `protobuf:"varint,11,rep,packed,name=safety_override_state,json=safetyOverrideState,proto3,enum=carbon.frontend.dashboard.SafetyOverrideState" json:"safety_override_state,omitempty"` //use row_states.safety_override_state
	ImplementState         ImplementState             `protobuf:"varint,13,opt,name=implement_state,json=implementState,proto3,enum=carbon.frontend.dashboard.ImplementState" json:"implement_state,omitempty"`
	EfficiencyEnabled      bool                       `protobuf:"varint,14,opt,name=efficiency_enabled,json=efficiencyEnabled,proto3" json:"efficiency_enabled,omitempty"`
	EfficiencyPercent      *PercentValue              `protobuf:"bytes,15,opt,name=efficiency_percent,json=efficiencyPercent,proto3" json:"efficiency_percent,omitempty"`
	ErrorRateEnabled       bool                       `protobuf:"varint,16,opt,name=error_rate_enabled,json=errorRateEnabled,proto3" json:"error_rate_enabled,omitempty"`
	ErrorRate              *PercentValue              `protobuf:"bytes,17,opt,name=error_rate,json=errorRate,proto3" json:"error_rate,omitempty"`
	ExtraConclusions       []*ExtraConclusion         `protobuf:"bytes,18,rep,name=extra_conclusions,json=extraConclusions,proto3" json:"extra_conclusions,omitempty"`
	AreaWeededToday        *AreaValue                 `protobuf:"bytes,19,opt,name=area_weeded_today,json=areaWeededToday,proto3" json:"area_weeded_today,omitempty"`
	AreaWeededTotal        *AreaValue                 `protobuf:"bytes,20,opt,name=area_weeded_total,json=areaWeededTotal,proto3" json:"area_weeded_total,omitempty"`
	WeedsKilledToday       *IntegerValue              `protobuf:"bytes,21,opt,name=weeds_killed_today,json=weedsKilledToday,proto3" json:"weeds_killed_today,omitempty"`
	WeedsKilledTotal       *IntegerValue              `protobuf:"bytes,22,opt,name=weeds_killed_total,json=weedsKilledTotal,proto3" json:"weeds_killed_total,omitempty"`
	TimeWeededToday        *DurationValue             `protobuf:"bytes,23,opt,name=time_weeded_today,json=timeWeededToday,proto3" json:"time_weeded_today,omitempty"`
	TimeWeededTotal        *DurationValue             `protobuf:"bytes,24,opt,name=time_weeded_total,json=timeWeededTotal,proto3" json:"time_weeded_total,omitempty"`
	WeedingEnabled         bool                       `protobuf:"varint,25,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	DebugMode              bool                       `protobuf:"varint,26,opt,name=debug_mode,json=debugMode,proto3" json:"debug_mode,omitempty"`
	CropsKilledToday       *IntegerValue              `protobuf:"bytes,27,opt,name=crops_killed_today,json=cropsKilledToday,proto3" json:"crops_killed_today,omitempty"`
	CropsKilledTotal       *IntegerValue              `protobuf:"bytes,28,opt,name=crops_killed_total,json=cropsKilledTotal,proto3" json:"crops_killed_total,omitempty"`
	CruiseEnabled          bool                       `protobuf:"varint,29,opt,name=cruise_enabled,json=cruiseEnabled,proto3" json:"cruise_enabled,omitempty"`
	RowStates              map[int32]*RowStateMessage `protobuf:"bytes,30,rep,name=row_states,json=rowStates,proto3" json:"row_states,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CruiseAllowEnable      bool                       `protobuf:"varint,31,opt,name=cruise_allow_enable,json=cruiseAllowEnable,proto3" json:"cruise_allow_enable,omitempty"`
	CruiseDisallowedReason string                     `protobuf:"bytes,32,opt,name=cruise_disallowed_reason,json=cruiseDisallowedReason,proto3" json:"cruise_disallowed_reason,omitempty"`
}

func (x *DashboardStateMessage) Reset() {
	*x = DashboardStateMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardStateMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardStateMessage) ProtoMessage() {}

func (x *DashboardStateMessage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardStateMessage.ProtoReflect.Descriptor instead.
func (*DashboardStateMessage) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{6}
}

func (x *DashboardStateMessage) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *DashboardStateMessage) GetLasersEnabled() bool {
	if x != nil {
		return x.LasersEnabled
	}
	return false
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *DashboardStateMessage) GetRowEnabled() []bool {
	if x != nil {
		return x.RowEnabled
	}
	return nil
}

func (x *DashboardStateMessage) GetExtras() []*ExtraStatus {
	if x != nil {
		return x.Extras
	}
	return nil
}

func (x *DashboardStateMessage) GetSelectedModel() *CropModel {
	if x != nil {
		return x.SelectedModel
	}
	return nil
}

func (x *DashboardStateMessage) GetTargetingState() *TargetingState {
	if x != nil {
		return x.TargetingState
	}
	return nil
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *DashboardStateMessage) GetTargetStateMismatch() []bool {
	if x != nil {
		return x.TargetStateMismatch
	}
	return nil
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *DashboardStateMessage) GetRowReady() []bool {
	if x != nil {
		return x.RowReady
	}
	return nil
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *DashboardStateMessage) GetRowExists() []bool {
	if x != nil {
		return x.RowExists
	}
	return nil
}

func (x *DashboardStateMessage) GetRowWidthIn() float64 {
	if x != nil {
		return x.RowWidthIn
	}
	return 0
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *DashboardStateMessage) GetSafetyOverrideState() []SafetyOverrideState {
	if x != nil {
		return x.SafetyOverrideState
	}
	return nil
}

func (x *DashboardStateMessage) GetImplementState() ImplementState {
	if x != nil {
		return x.ImplementState
	}
	return ImplementState_RAISED
}

func (x *DashboardStateMessage) GetEfficiencyEnabled() bool {
	if x != nil {
		return x.EfficiencyEnabled
	}
	return false
}

func (x *DashboardStateMessage) GetEfficiencyPercent() *PercentValue {
	if x != nil {
		return x.EfficiencyPercent
	}
	return nil
}

func (x *DashboardStateMessage) GetErrorRateEnabled() bool {
	if x != nil {
		return x.ErrorRateEnabled
	}
	return false
}

func (x *DashboardStateMessage) GetErrorRate() *PercentValue {
	if x != nil {
		return x.ErrorRate
	}
	return nil
}

func (x *DashboardStateMessage) GetExtraConclusions() []*ExtraConclusion {
	if x != nil {
		return x.ExtraConclusions
	}
	return nil
}

func (x *DashboardStateMessage) GetAreaWeededToday() *AreaValue {
	if x != nil {
		return x.AreaWeededToday
	}
	return nil
}

func (x *DashboardStateMessage) GetAreaWeededTotal() *AreaValue {
	if x != nil {
		return x.AreaWeededTotal
	}
	return nil
}

func (x *DashboardStateMessage) GetWeedsKilledToday() *IntegerValue {
	if x != nil {
		return x.WeedsKilledToday
	}
	return nil
}

func (x *DashboardStateMessage) GetWeedsKilledTotal() *IntegerValue {
	if x != nil {
		return x.WeedsKilledTotal
	}
	return nil
}

func (x *DashboardStateMessage) GetTimeWeededToday() *DurationValue {
	if x != nil {
		return x.TimeWeededToday
	}
	return nil
}

func (x *DashboardStateMessage) GetTimeWeededTotal() *DurationValue {
	if x != nil {
		return x.TimeWeededTotal
	}
	return nil
}

func (x *DashboardStateMessage) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *DashboardStateMessage) GetDebugMode() bool {
	if x != nil {
		return x.DebugMode
	}
	return false
}

func (x *DashboardStateMessage) GetCropsKilledToday() *IntegerValue {
	if x != nil {
		return x.CropsKilledToday
	}
	return nil
}

func (x *DashboardStateMessage) GetCropsKilledTotal() *IntegerValue {
	if x != nil {
		return x.CropsKilledTotal
	}
	return nil
}

func (x *DashboardStateMessage) GetCruiseEnabled() bool {
	if x != nil {
		return x.CruiseEnabled
	}
	return false
}

func (x *DashboardStateMessage) GetRowStates() map[int32]*RowStateMessage {
	if x != nil {
		return x.RowStates
	}
	return nil
}

func (x *DashboardStateMessage) GetCruiseAllowEnable() bool {
	if x != nil {
		return x.CruiseAllowEnable
	}
	return false
}

func (x *DashboardStateMessage) GetCruiseDisallowedReason() string {
	if x != nil {
		return x.CruiseDisallowedReason
	}
	return ""
}

type CropModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
	Crop      string `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"` // Deprecated, use crop_id
	HasModel  bool   `protobuf:"varint,2,opt,name=has_model,json=hasModel,proto3" json:"has_model,omitempty"`
	Preferred bool   `protobuf:"varint,3,opt,name=preferred,proto3" json:"preferred,omitempty"`
	CropId    string `protobuf:"bytes,4,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *CropModel) Reset() {
	*x = CropModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CropModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CropModel) ProtoMessage() {}

func (x *CropModel) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CropModel.ProtoReflect.Descriptor instead.
func (*CropModel) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{7}
}

// Deprecated: Marked as deprecated in frontend/proto/dashboard.proto.
func (x *CropModel) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *CropModel) GetHasModel() bool {
	if x != nil {
		return x.HasModel
	}
	return false
}

func (x *CropModel) GetPreferred() bool {
	if x != nil {
		return x.Preferred
	}
	return false
}

func (x *CropModel) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

type CropModelOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*CropModel `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *CropModelOptions) Reset() {
	*x = CropModelOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CropModelOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CropModelOptions) ProtoMessage() {}

func (x *CropModelOptions) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CropModelOptions.ProtoReflect.Descriptor instead.
func (*CropModelOptions) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{8}
}

func (x *CropModelOptions) GetModels() []*CropModel {
	if x != nil {
		return x.Models
	}
	return nil
}

type RowId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowNumber uint32 `protobuf:"varint,1,opt,name=row_number,json=rowNumber,proto3" json:"row_number,omitempty"`
}

func (x *RowId) Reset() {
	*x = RowId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowId) ProtoMessage() {}

func (x *RowId) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowId.ProtoReflect.Descriptor instead.
func (*RowId) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{9}
}

func (x *RowId) GetRowNumber() uint32 {
	if x != nil {
		return x.RowNumber
	}
	return 0
}

type WeedingVelocity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                               *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	CurrentVelocityMph               float64    `protobuf:"fixed64,2,opt,name=current_velocity_mph,json=currentVelocityMph,proto3" json:"current_velocity_mph,omitempty"`
	TargetVelocityMph                float64    `protobuf:"fixed64,3,opt,name=target_velocity_mph,json=targetVelocityMph,proto3" json:"target_velocity_mph,omitempty"`                                                  // Will be deprecated once we move to ve profiles
	ToleranceMph                     float64    `protobuf:"fixed64,4,opt,name=tolerance_mph,json=toleranceMph,proto3" json:"tolerance_mph,omitempty"`                                                                   // Will be deprecated once we move to ve profiles
	PrimaryTargetVelocityTopMph      float64    `protobuf:"fixed64,5,opt,name=primary_target_velocity_top_mph,json=primaryTargetVelocityTopMph,proto3" json:"primary_target_velocity_top_mph,omitempty"`                //will be used once we move to ve profiles
	PrimaryTargetVelocityBottomMph   float64    `protobuf:"fixed64,6,opt,name=primary_target_velocity_bottom_mph,json=primaryTargetVelocityBottomMph,proto3" json:"primary_target_velocity_bottom_mph,omitempty"`       //will be used once we move to ve profiles
	SecondaryTargetVelocityTopMph    float64    `protobuf:"fixed64,7,opt,name=secondary_target_velocity_top_mph,json=secondaryTargetVelocityTopMph,proto3" json:"secondary_target_velocity_top_mph,omitempty"`          //will be used once we move to ve profiles
	SecondaryTargetVelocityBottomMph float64    `protobuf:"fixed64,8,opt,name=secondary_target_velocity_bottom_mph,json=secondaryTargetVelocityBottomMph,proto3" json:"secondary_target_velocity_bottom_mph,omitempty"` //will be used once we move to ve profiles
	CruiseControlVelocityMph         float64    `protobuf:"fixed64,9,opt,name=cruise_control_velocity_mph,json=cruiseControlVelocityMph,proto3" json:"cruise_control_velocity_mph,omitempty"`                           //will be used once we move to ve profiles
}

func (x *WeedingVelocity) Reset() {
	*x = WeedingVelocity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedingVelocity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedingVelocity) ProtoMessage() {}

func (x *WeedingVelocity) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedingVelocity.ProtoReflect.Descriptor instead.
func (*WeedingVelocity) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{10}
}

func (x *WeedingVelocity) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *WeedingVelocity) GetCurrentVelocityMph() float64 {
	if x != nil {
		return x.CurrentVelocityMph
	}
	return 0
}

func (x *WeedingVelocity) GetTargetVelocityMph() float64 {
	if x != nil {
		return x.TargetVelocityMph
	}
	return 0
}

func (x *WeedingVelocity) GetToleranceMph() float64 {
	if x != nil {
		return x.ToleranceMph
	}
	return 0
}

func (x *WeedingVelocity) GetPrimaryTargetVelocityTopMph() float64 {
	if x != nil {
		return x.PrimaryTargetVelocityTopMph
	}
	return 0
}

func (x *WeedingVelocity) GetPrimaryTargetVelocityBottomMph() float64 {
	if x != nil {
		return x.PrimaryTargetVelocityBottomMph
	}
	return 0
}

func (x *WeedingVelocity) GetSecondaryTargetVelocityTopMph() float64 {
	if x != nil {
		return x.SecondaryTargetVelocityTopMph
	}
	return 0
}

func (x *WeedingVelocity) GetSecondaryTargetVelocityBottomMph() float64 {
	if x != nil {
		return x.SecondaryTargetVelocityBottomMph
	}
	return 0
}

func (x *WeedingVelocity) GetCruiseControlVelocityMph() float64 {
	if x != nil {
		return x.CruiseControlVelocityMph
	}
	return 0
}

type RowSpacing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width float64 `protobuf:"fixed64,1,opt,name=width,proto3" json:"width,omitempty"`
}

func (x *RowSpacing) Reset() {
	*x = RowSpacing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowSpacing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowSpacing) ProtoMessage() {}

func (x *RowSpacing) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowSpacing.ProtoReflect.Descriptor instead.
func (*RowSpacing) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{11}
}

func (x *RowSpacing) GetWidth() float64 {
	if x != nil {
		return x.Width
	}
	return 0
}

type CruiseEnable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *CruiseEnable) Reset() {
	*x = CruiseEnable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_dashboard_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CruiseEnable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CruiseEnable) ProtoMessage() {}

func (x *CruiseEnable) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_dashboard_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CruiseEnable.ProtoReflect.Descriptor instead.
func (*CruiseEnable) Descriptor() ([]byte, []int) {
	return file_frontend_proto_dashboard_proto_rawDescGZIP(), []int{12}
}

func (x *CruiseEnable) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

var File_frontend_proto_dashboard_proto protoreflect.FileDescriptor

var file_frontend_proto_dashboard_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x19, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x20, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb0, 0x02, 0x0a, 0x0b, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x63, 0x6f, 0x6e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x22, 0x29, 0x0a, 0x0d, 0x57,
	0x65, 0x65, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x4f, 0x0a, 0x11, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74,
	0x68, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x61, 0x6c,
	0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x22, 0xeb, 0x02, 0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x47, 0x0a, 0x0a, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0d, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x5d, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52,
	0x6f, 0x77, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x52, 0x6f, 0x77, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x52, 0x6f, 0x77, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x85, 0x02, 0x0a, 0x0f, 0x45, 0x78, 0x74, 0x72, 0x61, 0x43,
	0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x66, 0x6c, 0x69, 0x70, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x66, 0x6c, 0x69, 0x70, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x67, 0x6f, 0x6f, 0x64,
	0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x67, 0x6f, 0x6f, 0x64, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x38,
	0x0a, 0x18, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x16, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0xd9, 0x01,
	0x0a, 0x0f, 0x52, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x69, 0x73, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x72, 0x65, 0x61, 0x64, 0x79, 0x12, 0x62, 0x0a, 0x15, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x13, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x8e, 0x11, 0x0a, 0x15, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0b, 0x72,
	0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x08,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x72, 0x6f, 0x77, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x3e, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73,
	0x12, 0x4b, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x52, 0x0a,
	0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x36, 0x0a, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x07, 0x20, 0x03, 0x28, 0x08,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1f, 0x0a, 0x09, 0x72, 0x6f, 0x77,
	0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x08, 0x20, 0x03, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x08, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x21, 0x0a, 0x0a, 0x72, 0x6f,
	0x77, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x08, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x20, 0x0a,
	0x0c, 0x72, 0x6f, 0x77, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x72, 0x6f, 0x77, 0x57, 0x69, 0x64, 0x74, 0x68, 0x49, 0x6e, 0x12,
	0x66, 0x0a, 0x15, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x13, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x49, 0x6d, 0x70,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6d, 0x70,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x65,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65,
	0x6e, 0x63, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x58, 0x0a, 0x12, 0x65, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x11, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x48, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x11,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x10, 0x65, 0x78, 0x74, 0x72, 0x61, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x52, 0x0a, 0x11, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x77, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41,
	0x72, 0x65, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x61, 0x72, 0x65, 0x61, 0x57, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12, 0x52, 0x0a, 0x11, 0x61, 0x72, 0x65,
	0x61, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x61, 0x72,
	0x65, 0x61, 0x57, 0x65, 0x65, 0x64, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x57, 0x0a,
	0x12, 0x77, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x6b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x6f,
	0x64, 0x61, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x77, 0x65, 0x65, 0x64, 0x73, 0x4b, 0x69, 0x6c, 0x6c, 0x65,
	0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12, 0x57, 0x0a, 0x12, 0x77, 0x65, 0x65, 0x64, 0x73, 0x5f,
	0x6b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x77,
	0x65, 0x65, 0x64, 0x73, 0x4b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x56, 0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x74,
	0x6f, 0x64, 0x61, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x65, 0x65, 0x64,
	0x65, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12, 0x56, 0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f,
	0x74, 0x69, 0x6d, 0x65, 0x57, 0x65, 0x65, 0x64, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x27, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x62, 0x75,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x65,
	0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x63, 0x72, 0x6f, 0x70, 0x73,
	0x5f, 0x6b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10,
	0x63, 0x72, 0x6f, 0x70, 0x73, 0x4b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79,
	0x12, 0x57, 0x0a, 0x12, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x5f, 0x6b, 0x69, 0x6c, 0x6c, 0x65, 0x64,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x4b, 0x69,
	0x6c, 0x6c, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x72, 0x75,
	0x69, 0x73, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x5e, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x1e,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x52, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63,
	0x72, 0x75, 0x69, 0x73, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x38, 0x0a, 0x18, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x44, 0x69, 0x73, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0x68, 0x0a, 0x0e, 0x52, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x4a, 0x04, 0x08, 0x0c, 0x10, 0x0d, 0x22, 0x77, 0x0a, 0x09, 0x43, 0x72,
	0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72,
	0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f,
	0x70, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x10, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x26, 0x0a, 0x05, 0x52, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xb4, 0x04,
	0x0a, 0x0f, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02,
	0x74, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x4d, 0x70, 0x68, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x4d, 0x70, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x74, 0x6f, 0x6c,
	0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x70, 0x68, 0x12, 0x44, 0x0a, 0x1f, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x1b, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x54, 0x6f, 0x70, 0x4d, 0x70, 0x68, 0x12,
	0x4a, 0x0a, 0x22, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f,
	0x6d, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1e, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x4d, 0x70, 0x68, 0x12, 0x48, 0x0a, 0x21, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x6d, 0x70, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1d, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x54,
	0x6f, 0x70, 0x4d, 0x70, 0x68, 0x12, 0x4e, 0x0a, 0x24, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x20, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x4d, 0x70, 0x68, 0x12, 0x3d, 0x0a, 0x1b, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x6d, 0x70, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x63, 0x72, 0x75, 0x69,
	0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x4d, 0x70, 0x68, 0x22, 0x22, 0x0a, 0x0a, 0x52, 0x6f, 0x77, 0x53, 0x70, 0x61, 0x63, 0x69,
	0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x22, 0x28, 0x0a, 0x0c, 0x43, 0x72, 0x75, 0x69,
	0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x2a, 0x4d, 0x0a, 0x13, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10,
	0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x53, 0x74, 0x6f, 0x70, 0x10,
	0x01, 0x2a, 0x29, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x49, 0x53, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x32, 0xc5, 0x06, 0x0a,
	0x10, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x4a, 0x0a, 0x09, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x20,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x6f, 0x77, 0x49, 0x64,
	0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x48, 0x0a,
	0x0c, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x64, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x56, 0x0a, 0x0c, 0x53, 0x65, 0x74,
	0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x1a,
	0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x03, 0x88, 0x02,
	0x01, 0x12, 0x65, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x57, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x2a, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x5b, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x29, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x53, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x52, 0x6f, 0x77, 0x53,
	0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2e, 0x52, 0x6f, 0x77, 0x53, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x1a, 0x1b, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x58, 0x0a, 0x10, 0x53, 0x65,
	0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x75, 0x69, 0x73,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_dashboard_proto_rawDescOnce sync.Once
	file_frontend_proto_dashboard_proto_rawDescData = file_frontend_proto_dashboard_proto_rawDesc
)

func file_frontend_proto_dashboard_proto_rawDescGZIP() []byte {
	file_frontend_proto_dashboard_proto_rawDescOnce.Do(func() {
		file_frontend_proto_dashboard_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_dashboard_proto_rawDescData)
	})
	return file_frontend_proto_dashboard_proto_rawDescData
}

var file_frontend_proto_dashboard_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_proto_dashboard_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_frontend_proto_dashboard_proto_goTypes = []interface{}{
	(SafetyOverrideState)(0),      // 0: carbon.frontend.dashboard.SafetyOverrideState
	(ImplementState)(0),           // 1: carbon.frontend.dashboard.ImplementState
	(*ExtraStatus)(nil),           // 2: carbon.frontend.dashboard.ExtraStatus
	(*WeedTargeting)(nil),         // 3: carbon.frontend.dashboard.WeedTargeting
	(*ThinningTargeting)(nil),     // 4: carbon.frontend.dashboard.ThinningTargeting
	(*TargetingState)(nil),        // 5: carbon.frontend.dashboard.TargetingState
	(*ExtraConclusion)(nil),       // 6: carbon.frontend.dashboard.ExtraConclusion
	(*RowStateMessage)(nil),       // 7: carbon.frontend.dashboard.RowStateMessage
	(*DashboardStateMessage)(nil), // 8: carbon.frontend.dashboard.DashboardStateMessage
	(*CropModel)(nil),             // 9: carbon.frontend.dashboard.CropModel
	(*CropModelOptions)(nil),      // 10: carbon.frontend.dashboard.CropModelOptions
	(*RowId)(nil),                 // 11: carbon.frontend.dashboard.RowId
	(*WeedingVelocity)(nil),       // 12: carbon.frontend.dashboard.WeedingVelocity
	(*RowSpacing)(nil),            // 13: carbon.frontend.dashboard.RowSpacing
	(*CruiseEnable)(nil),          // 14: carbon.frontend.dashboard.CruiseEnable
	nil,                           // 15: carbon.frontend.dashboard.TargetingState.EnabledRowsEntry
	nil,                           // 16: carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry
	(*PercentValue)(nil),          // 17: carbon.frontend.translation.PercentValue
	(*Timestamp)(nil),             // 18: carbon.frontend.util.Timestamp
	(*AreaValue)(nil),             // 19: carbon.frontend.translation.AreaValue
	(*IntegerValue)(nil),          // 20: carbon.frontend.translation.IntegerValue
	(*DurationValue)(nil),         // 21: carbon.frontend.translation.DurationValue
	(*Empty)(nil),                 // 22: carbon.frontend.util.Empty
}
var file_frontend_proto_dashboard_proto_depIdxs = []int32{
	3,  // 0: carbon.frontend.dashboard.TargetingState.weed_state:type_name -> carbon.frontend.dashboard.WeedTargeting
	4,  // 1: carbon.frontend.dashboard.TargetingState.thinning_state:type_name -> carbon.frontend.dashboard.ThinningTargeting
	15, // 2: carbon.frontend.dashboard.TargetingState.enabled_rows:type_name -> carbon.frontend.dashboard.TargetingState.EnabledRowsEntry
	17, // 3: carbon.frontend.dashboard.ExtraConclusion.percent:type_name -> carbon.frontend.translation.PercentValue
	0,  // 4: carbon.frontend.dashboard.RowStateMessage.safety_override_state:type_name -> carbon.frontend.dashboard.SafetyOverrideState
	18, // 5: carbon.frontend.dashboard.DashboardStateMessage.ts:type_name -> carbon.frontend.util.Timestamp
	2,  // 6: carbon.frontend.dashboard.DashboardStateMessage.extras:type_name -> carbon.frontend.dashboard.ExtraStatus
	9,  // 7: carbon.frontend.dashboard.DashboardStateMessage.selected_model:type_name -> carbon.frontend.dashboard.CropModel
	5,  // 8: carbon.frontend.dashboard.DashboardStateMessage.targeting_state:type_name -> carbon.frontend.dashboard.TargetingState
	0,  // 9: carbon.frontend.dashboard.DashboardStateMessage.safety_override_state:type_name -> carbon.frontend.dashboard.SafetyOverrideState
	1,  // 10: carbon.frontend.dashboard.DashboardStateMessage.implement_state:type_name -> carbon.frontend.dashboard.ImplementState
	17, // 11: carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent:type_name -> carbon.frontend.translation.PercentValue
	17, // 12: carbon.frontend.dashboard.DashboardStateMessage.error_rate:type_name -> carbon.frontend.translation.PercentValue
	6,  // 13: carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions:type_name -> carbon.frontend.dashboard.ExtraConclusion
	19, // 14: carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today:type_name -> carbon.frontend.translation.AreaValue
	19, // 15: carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total:type_name -> carbon.frontend.translation.AreaValue
	20, // 16: carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today:type_name -> carbon.frontend.translation.IntegerValue
	20, // 17: carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total:type_name -> carbon.frontend.translation.IntegerValue
	21, // 18: carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today:type_name -> carbon.frontend.translation.DurationValue
	21, // 19: carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total:type_name -> carbon.frontend.translation.DurationValue
	20, // 20: carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today:type_name -> carbon.frontend.translation.IntegerValue
	20, // 21: carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total:type_name -> carbon.frontend.translation.IntegerValue
	16, // 22: carbon.frontend.dashboard.DashboardStateMessage.row_states:type_name -> carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry
	9,  // 23: carbon.frontend.dashboard.CropModelOptions.models:type_name -> carbon.frontend.dashboard.CropModel
	18, // 24: carbon.frontend.dashboard.WeedingVelocity.ts:type_name -> carbon.frontend.util.Timestamp
	7,  // 25: carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry.value:type_name -> carbon.frontend.dashboard.RowStateMessage
	11, // 26: carbon.frontend.dashboard.DashboardService.ToggleRow:input_type -> carbon.frontend.dashboard.RowId
	22, // 27: carbon.frontend.dashboard.DashboardService.ToggleLasers:input_type -> carbon.frontend.util.Empty
	18, // 28: carbon.frontend.dashboard.DashboardService.GetNextDashboardState:input_type -> carbon.frontend.util.Timestamp
	22, // 29: carbon.frontend.dashboard.DashboardService.GetCropModelOptions:input_type -> carbon.frontend.util.Empty
	9,  // 30: carbon.frontend.dashboard.DashboardService.SetCropModel:input_type -> carbon.frontend.dashboard.CropModel
	18, // 31: carbon.frontend.dashboard.DashboardService.GetNextWeedingVelocity:input_type -> carbon.frontend.util.Timestamp
	5,  // 32: carbon.frontend.dashboard.DashboardService.SetTargetingState:input_type -> carbon.frontend.dashboard.TargetingState
	13, // 33: carbon.frontend.dashboard.DashboardService.SetRowSpacing:input_type -> carbon.frontend.dashboard.RowSpacing
	14, // 34: carbon.frontend.dashboard.DashboardService.SetCruiseEnabled:input_type -> carbon.frontend.dashboard.CruiseEnable
	22, // 35: carbon.frontend.dashboard.DashboardService.ToggleRow:output_type -> carbon.frontend.util.Empty
	22, // 36: carbon.frontend.dashboard.DashboardService.ToggleLasers:output_type -> carbon.frontend.util.Empty
	8,  // 37: carbon.frontend.dashboard.DashboardService.GetNextDashboardState:output_type -> carbon.frontend.dashboard.DashboardStateMessage
	10, // 38: carbon.frontend.dashboard.DashboardService.GetCropModelOptions:output_type -> carbon.frontend.dashboard.CropModelOptions
	22, // 39: carbon.frontend.dashboard.DashboardService.SetCropModel:output_type -> carbon.frontend.util.Empty
	12, // 40: carbon.frontend.dashboard.DashboardService.GetNextWeedingVelocity:output_type -> carbon.frontend.dashboard.WeedingVelocity
	22, // 41: carbon.frontend.dashboard.DashboardService.SetTargetingState:output_type -> carbon.frontend.util.Empty
	22, // 42: carbon.frontend.dashboard.DashboardService.SetRowSpacing:output_type -> carbon.frontend.util.Empty
	22, // 43: carbon.frontend.dashboard.DashboardService.SetCruiseEnabled:output_type -> carbon.frontend.util.Empty
	35, // [35:44] is the sub-list for method output_type
	26, // [26:35] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_frontend_proto_dashboard_proto_init() }
func file_frontend_proto_dashboard_proto_init() {
	if File_frontend_proto_dashboard_proto != nil {
		return
	}
	file_frontend_proto_translation_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_dashboard_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedTargeting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThinningTargeting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetingState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraConclusion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowStateMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardStateMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CropModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CropModelOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedingVelocity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowSpacing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_dashboard_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CruiseEnable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_dashboard_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_dashboard_proto_goTypes,
		DependencyIndexes: file_frontend_proto_dashboard_proto_depIdxs,
		EnumInfos:         file_frontend_proto_dashboard_proto_enumTypes,
		MessageInfos:      file_frontend_proto_dashboard_proto_msgTypes,
	}.Build()
	File_frontend_proto_dashboard_proto = out.File
	file_frontend_proto_dashboard_proto_rawDesc = nil
	file_frontend_proto_dashboard_proto_goTypes = nil
	file_frontend_proto_dashboard_proto_depIdxs = nil
}

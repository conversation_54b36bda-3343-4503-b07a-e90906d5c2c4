// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/plant_captcha.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PlantCaptchaService_StartPlantCaptcha_FullMethodName              = "/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptcha"
	PlantCaptchaService_GetNextPlantCaptchaStatus_FullMethodName      = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaStatus"
	PlantCaptchaService_GetNextPlantCaptchasList_FullMethodName       = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchasList"
	PlantCaptchaService_DeletePlantCaptcha_FullMethodName             = "/carbon.frontend.plant_captcha.PlantCaptchaService/DeletePlantCaptcha"
	PlantCaptchaService_GetPlantCaptcha_FullMethodName                = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptcha"
	PlantCaptchaService_CancelPlantCaptcha_FullMethodName             = "/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptcha"
	PlantCaptchaService_StartPlantCaptchaUpload_FullMethodName        = "/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptchaUpload"
	PlantCaptchaService_GetNextPlantCaptchaUploadState_FullMethodName = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaUploadState"
	PlantCaptchaService_SubmitPlantCaptchaResults_FullMethodName      = "/carbon.frontend.plant_captcha.PlantCaptchaService/SubmitPlantCaptchaResults"
	PlantCaptchaService_GetPlantCaptchaItemResults_FullMethodName     = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptchaItemResults"
	PlantCaptchaService_CalculatePlantCaptcha_FullMethodName          = "/carbon.frontend.plant_captcha.PlantCaptchaService/CalculatePlantCaptcha"
	PlantCaptchaService_GetOriginalModelinatorConfig_FullMethodName   = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetOriginalModelinatorConfig"
	PlantCaptchaService_GetCaptchaRowStatus_FullMethodName            = "/carbon.frontend.plant_captcha.PlantCaptchaService/GetCaptchaRowStatus"
	PlantCaptchaService_CancelPlantCaptchaOnRow_FullMethodName        = "/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptchaOnRow"
)

// PlantCaptchaServiceClient is the client API for PlantCaptchaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PlantCaptchaServiceClient interface {
	StartPlantCaptcha(ctx context.Context, in *StartPlantCaptchaRequest, opts ...grpc.CallOption) (*StartPlantCaptchaResponse, error)
	GetNextPlantCaptchaStatus(ctx context.Context, in *GetNextPlantCaptchaStatusRequest, opts ...grpc.CallOption) (*GetNextPlantCaptchaStatusResponse, error)
	GetNextPlantCaptchasList(ctx context.Context, in *GetNextPlantCaptchasListRequest, opts ...grpc.CallOption) (*GetNextPlantCaptchasListResponse, error)
	DeletePlantCaptcha(ctx context.Context, in *DeletePlantCaptchaRequest, opts ...grpc.CallOption) (*Empty, error)
	GetPlantCaptcha(ctx context.Context, in *GetPlantCaptchaRequest, opts ...grpc.CallOption) (*GetPlantCaptchaResponse, error)
	CancelPlantCaptcha(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StartPlantCaptchaUpload(ctx context.Context, in *StartPlantCaptchaUploadRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextPlantCaptchaUploadState(ctx context.Context, in *GetNextPlantCaptchaUploadStateRequest, opts ...grpc.CallOption) (*GetNextPlantCaptchaUploadStateResponse, error)
	SubmitPlantCaptchaResults(ctx context.Context, in *SubmitPlantCaptchaResultsRequest, opts ...grpc.CallOption) (*Empty, error)
	GetPlantCaptchaItemResults(ctx context.Context, in *GetPlantCaptchaItemResultsRequest, opts ...grpc.CallOption) (*GetPlantCaptchaItemResultsResponse, error)
	CalculatePlantCaptcha(ctx context.Context, in *CalculatePlantCaptchaRequest, opts ...grpc.CallOption) (*CalculatePlantCaptchaResponse, error)
	GetOriginalModelinatorConfig(ctx context.Context, in *GetOriginalModelinatorConfigRequest, opts ...grpc.CallOption) (*GetOriginalModelinatorConfigResponse, error)
	GetCaptchaRowStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetCaptchaRowStatusResponse, error)
	CancelPlantCaptchaOnRow(ctx context.Context, in *CancelPlantCaptchaOnRowRequest, opts ...grpc.CallOption) (*Empty, error)
}

type plantCaptchaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlantCaptchaServiceClient(cc grpc.ClientConnInterface) PlantCaptchaServiceClient {
	return &plantCaptchaServiceClient{cc}
}

func (c *plantCaptchaServiceClient) StartPlantCaptcha(ctx context.Context, in *StartPlantCaptchaRequest, opts ...grpc.CallOption) (*StartPlantCaptchaResponse, error) {
	out := new(StartPlantCaptchaResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_StartPlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetNextPlantCaptchaStatus(ctx context.Context, in *GetNextPlantCaptchaStatusRequest, opts ...grpc.CallOption) (*GetNextPlantCaptchaStatusResponse, error) {
	out := new(GetNextPlantCaptchaStatusResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetNextPlantCaptchaStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetNextPlantCaptchasList(ctx context.Context, in *GetNextPlantCaptchasListRequest, opts ...grpc.CallOption) (*GetNextPlantCaptchasListResponse, error) {
	out := new(GetNextPlantCaptchasListResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetNextPlantCaptchasList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) DeletePlantCaptcha(ctx context.Context, in *DeletePlantCaptchaRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PlantCaptchaService_DeletePlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetPlantCaptcha(ctx context.Context, in *GetPlantCaptchaRequest, opts ...grpc.CallOption) (*GetPlantCaptchaResponse, error) {
	out := new(GetPlantCaptchaResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetPlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) CancelPlantCaptcha(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PlantCaptchaService_CancelPlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) StartPlantCaptchaUpload(ctx context.Context, in *StartPlantCaptchaUploadRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PlantCaptchaService_StartPlantCaptchaUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetNextPlantCaptchaUploadState(ctx context.Context, in *GetNextPlantCaptchaUploadStateRequest, opts ...grpc.CallOption) (*GetNextPlantCaptchaUploadStateResponse, error) {
	out := new(GetNextPlantCaptchaUploadStateResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetNextPlantCaptchaUploadState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) SubmitPlantCaptchaResults(ctx context.Context, in *SubmitPlantCaptchaResultsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PlantCaptchaService_SubmitPlantCaptchaResults_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetPlantCaptchaItemResults(ctx context.Context, in *GetPlantCaptchaItemResultsRequest, opts ...grpc.CallOption) (*GetPlantCaptchaItemResultsResponse, error) {
	out := new(GetPlantCaptchaItemResultsResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetPlantCaptchaItemResults_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) CalculatePlantCaptcha(ctx context.Context, in *CalculatePlantCaptchaRequest, opts ...grpc.CallOption) (*CalculatePlantCaptchaResponse, error) {
	out := new(CalculatePlantCaptchaResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_CalculatePlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetOriginalModelinatorConfig(ctx context.Context, in *GetOriginalModelinatorConfigRequest, opts ...grpc.CallOption) (*GetOriginalModelinatorConfigResponse, error) {
	out := new(GetOriginalModelinatorConfigResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetOriginalModelinatorConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) GetCaptchaRowStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetCaptchaRowStatusResponse, error) {
	out := new(GetCaptchaRowStatusResponse)
	err := c.cc.Invoke(ctx, PlantCaptchaService_GetCaptchaRowStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plantCaptchaServiceClient) CancelPlantCaptchaOnRow(ctx context.Context, in *CancelPlantCaptchaOnRowRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PlantCaptchaService_CancelPlantCaptchaOnRow_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlantCaptchaServiceServer is the server API for PlantCaptchaService service.
// All implementations must embed UnimplementedPlantCaptchaServiceServer
// for forward compatibility
type PlantCaptchaServiceServer interface {
	StartPlantCaptcha(context.Context, *StartPlantCaptchaRequest) (*StartPlantCaptchaResponse, error)
	GetNextPlantCaptchaStatus(context.Context, *GetNextPlantCaptchaStatusRequest) (*GetNextPlantCaptchaStatusResponse, error)
	GetNextPlantCaptchasList(context.Context, *GetNextPlantCaptchasListRequest) (*GetNextPlantCaptchasListResponse, error)
	DeletePlantCaptcha(context.Context, *DeletePlantCaptchaRequest) (*Empty, error)
	GetPlantCaptcha(context.Context, *GetPlantCaptchaRequest) (*GetPlantCaptchaResponse, error)
	CancelPlantCaptcha(context.Context, *Empty) (*Empty, error)
	StartPlantCaptchaUpload(context.Context, *StartPlantCaptchaUploadRequest) (*Empty, error)
	GetNextPlantCaptchaUploadState(context.Context, *GetNextPlantCaptchaUploadStateRequest) (*GetNextPlantCaptchaUploadStateResponse, error)
	SubmitPlantCaptchaResults(context.Context, *SubmitPlantCaptchaResultsRequest) (*Empty, error)
	GetPlantCaptchaItemResults(context.Context, *GetPlantCaptchaItemResultsRequest) (*GetPlantCaptchaItemResultsResponse, error)
	CalculatePlantCaptcha(context.Context, *CalculatePlantCaptchaRequest) (*CalculatePlantCaptchaResponse, error)
	GetOriginalModelinatorConfig(context.Context, *GetOriginalModelinatorConfigRequest) (*GetOriginalModelinatorConfigResponse, error)
	GetCaptchaRowStatus(context.Context, *Empty) (*GetCaptchaRowStatusResponse, error)
	CancelPlantCaptchaOnRow(context.Context, *CancelPlantCaptchaOnRowRequest) (*Empty, error)
	mustEmbedUnimplementedPlantCaptchaServiceServer()
}

// UnimplementedPlantCaptchaServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPlantCaptchaServiceServer struct {
}

func (UnimplementedPlantCaptchaServiceServer) StartPlantCaptcha(context.Context, *StartPlantCaptchaRequest) (*StartPlantCaptchaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPlantCaptcha not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetNextPlantCaptchaStatus(context.Context, *GetNextPlantCaptchaStatusRequest) (*GetNextPlantCaptchaStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextPlantCaptchaStatus not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetNextPlantCaptchasList(context.Context, *GetNextPlantCaptchasListRequest) (*GetNextPlantCaptchasListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextPlantCaptchasList not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) DeletePlantCaptcha(context.Context, *DeletePlantCaptchaRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlantCaptcha not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetPlantCaptcha(context.Context, *GetPlantCaptchaRequest) (*GetPlantCaptchaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlantCaptcha not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) CancelPlantCaptcha(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPlantCaptcha not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) StartPlantCaptchaUpload(context.Context, *StartPlantCaptchaUploadRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPlantCaptchaUpload not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetNextPlantCaptchaUploadState(context.Context, *GetNextPlantCaptchaUploadStateRequest) (*GetNextPlantCaptchaUploadStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextPlantCaptchaUploadState not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) SubmitPlantCaptchaResults(context.Context, *SubmitPlantCaptchaResultsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitPlantCaptchaResults not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetPlantCaptchaItemResults(context.Context, *GetPlantCaptchaItemResultsRequest) (*GetPlantCaptchaItemResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlantCaptchaItemResults not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) CalculatePlantCaptcha(context.Context, *CalculatePlantCaptchaRequest) (*CalculatePlantCaptchaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePlantCaptcha not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetOriginalModelinatorConfig(context.Context, *GetOriginalModelinatorConfigRequest) (*GetOriginalModelinatorConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOriginalModelinatorConfig not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) GetCaptchaRowStatus(context.Context, *Empty) (*GetCaptchaRowStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaptchaRowStatus not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) CancelPlantCaptchaOnRow(context.Context, *CancelPlantCaptchaOnRowRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPlantCaptchaOnRow not implemented")
}
func (UnimplementedPlantCaptchaServiceServer) mustEmbedUnimplementedPlantCaptchaServiceServer() {}

// UnsafePlantCaptchaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlantCaptchaServiceServer will
// result in compilation errors.
type UnsafePlantCaptchaServiceServer interface {
	mustEmbedUnimplementedPlantCaptchaServiceServer()
}

func RegisterPlantCaptchaServiceServer(s grpc.ServiceRegistrar, srv PlantCaptchaServiceServer) {
	s.RegisterService(&PlantCaptchaService_ServiceDesc, srv)
}

func _PlantCaptchaService_StartPlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPlantCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).StartPlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_StartPlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).StartPlantCaptcha(ctx, req.(*StartPlantCaptchaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetNextPlantCaptchaStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextPlantCaptchaStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetNextPlantCaptchaStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetNextPlantCaptchaStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetNextPlantCaptchaStatus(ctx, req.(*GetNextPlantCaptchaStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetNextPlantCaptchasList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextPlantCaptchasListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetNextPlantCaptchasList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetNextPlantCaptchasList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetNextPlantCaptchasList(ctx, req.(*GetNextPlantCaptchasListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_DeletePlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePlantCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).DeletePlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_DeletePlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).DeletePlantCaptcha(ctx, req.(*DeletePlantCaptchaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetPlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlantCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetPlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetPlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetPlantCaptcha(ctx, req.(*GetPlantCaptchaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_CancelPlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).CancelPlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_CancelPlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).CancelPlantCaptcha(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_StartPlantCaptchaUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPlantCaptchaUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).StartPlantCaptchaUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_StartPlantCaptchaUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).StartPlantCaptchaUpload(ctx, req.(*StartPlantCaptchaUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetNextPlantCaptchaUploadState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextPlantCaptchaUploadStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetNextPlantCaptchaUploadState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetNextPlantCaptchaUploadState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetNextPlantCaptchaUploadState(ctx, req.(*GetNextPlantCaptchaUploadStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_SubmitPlantCaptchaResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitPlantCaptchaResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).SubmitPlantCaptchaResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_SubmitPlantCaptchaResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).SubmitPlantCaptchaResults(ctx, req.(*SubmitPlantCaptchaResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetPlantCaptchaItemResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlantCaptchaItemResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetPlantCaptchaItemResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetPlantCaptchaItemResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetPlantCaptchaItemResults(ctx, req.(*GetPlantCaptchaItemResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_CalculatePlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePlantCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).CalculatePlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_CalculatePlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).CalculatePlantCaptcha(ctx, req.(*CalculatePlantCaptchaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetOriginalModelinatorConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOriginalModelinatorConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetOriginalModelinatorConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetOriginalModelinatorConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetOriginalModelinatorConfig(ctx, req.(*GetOriginalModelinatorConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_GetCaptchaRowStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).GetCaptchaRowStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_GetCaptchaRowStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).GetCaptchaRowStatus(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlantCaptchaService_CancelPlantCaptchaOnRow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPlantCaptchaOnRowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlantCaptchaServiceServer).CancelPlantCaptchaOnRow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlantCaptchaService_CancelPlantCaptchaOnRow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlantCaptchaServiceServer).CancelPlantCaptchaOnRow(ctx, req.(*CancelPlantCaptchaOnRowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PlantCaptchaService_ServiceDesc is the grpc.ServiceDesc for PlantCaptchaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlantCaptchaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.plant_captcha.PlantCaptchaService",
	HandlerType: (*PlantCaptchaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartPlantCaptcha",
			Handler:    _PlantCaptchaService_StartPlantCaptcha_Handler,
		},
		{
			MethodName: "GetNextPlantCaptchaStatus",
			Handler:    _PlantCaptchaService_GetNextPlantCaptchaStatus_Handler,
		},
		{
			MethodName: "GetNextPlantCaptchasList",
			Handler:    _PlantCaptchaService_GetNextPlantCaptchasList_Handler,
		},
		{
			MethodName: "DeletePlantCaptcha",
			Handler:    _PlantCaptchaService_DeletePlantCaptcha_Handler,
		},
		{
			MethodName: "GetPlantCaptcha",
			Handler:    _PlantCaptchaService_GetPlantCaptcha_Handler,
		},
		{
			MethodName: "CancelPlantCaptcha",
			Handler:    _PlantCaptchaService_CancelPlantCaptcha_Handler,
		},
		{
			MethodName: "StartPlantCaptchaUpload",
			Handler:    _PlantCaptchaService_StartPlantCaptchaUpload_Handler,
		},
		{
			MethodName: "GetNextPlantCaptchaUploadState",
			Handler:    _PlantCaptchaService_GetNextPlantCaptchaUploadState_Handler,
		},
		{
			MethodName: "SubmitPlantCaptchaResults",
			Handler:    _PlantCaptchaService_SubmitPlantCaptchaResults_Handler,
		},
		{
			MethodName: "GetPlantCaptchaItemResults",
			Handler:    _PlantCaptchaService_GetPlantCaptchaItemResults_Handler,
		},
		{
			MethodName: "CalculatePlantCaptcha",
			Handler:    _PlantCaptchaService_CalculatePlantCaptcha_Handler,
		},
		{
			MethodName: "GetOriginalModelinatorConfig",
			Handler:    _PlantCaptchaService_GetOriginalModelinatorConfig_Handler,
		},
		{
			MethodName: "GetCaptchaRowStatus",
			Handler:    _PlantCaptchaService_GetCaptchaRowStatus_Handler,
		},
		{
			MethodName: "CancelPlantCaptchaOnRow",
			Handler:    _PlantCaptchaService_CancelPlantCaptchaOnRow_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/plant_captcha.proto",
}

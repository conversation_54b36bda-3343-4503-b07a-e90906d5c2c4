// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/thinning.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	thinning "github.com/carbonrobotics/robot/golang/generated/proto/thinning"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ThinningConfVer int32

const (
	ThinningConfVer_THIN_CONF_V1 ThinningConfVer = 0
	ThinningConfVer_THIN_CONF_V2 ThinningConfVer = 1
)

// Enum value maps for ThinningConfVer.
var (
	ThinningConfVer_name = map[int32]string{
		0: "THIN_CONF_V1",
		1: "THIN_CONF_V2",
	}
	ThinningConfVer_value = map[string]int32{
		"THIN_CONF_V1": 0,
		"THIN_CONF_V2": 1,
	}
)

func (x ThinningConfVer) Enum() *ThinningConfVer {
	p := new(ThinningConfVer)
	*p = x
	return p
}

func (x ThinningConfVer) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ThinningConfVer) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_thinning_proto_enumTypes[0].Descriptor()
}

func (ThinningConfVer) Type() protoreflect.EnumType {
	return &file_frontend_proto_thinning_proto_enumTypes[0]
}

func (x ThinningConfVer) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ThinningConfVer.Descriptor instead.
func (ThinningConfVer) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{0}
}

type GetNextConfigurationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts          *Timestamp                   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Definitions []*thinning.ConfigDefinition `protobuf:"bytes,2,rep,name=definitions,proto3" json:"definitions,omitempty"`
	ActiveId    string                       `protobuf:"bytes,3,opt,name=active_id,json=activeId,proto3" json:"active_id,omitempty"`
}

func (x *GetNextConfigurationsResponse) Reset() {
	*x = GetNextConfigurationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextConfigurationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextConfigurationsResponse) ProtoMessage() {}

func (x *GetNextConfigurationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextConfigurationsResponse.ProtoReflect.Descriptor instead.
func (*GetNextConfigurationsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{0}
}

func (x *GetNextConfigurationsResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextConfigurationsResponse) GetDefinitions() []*thinning.ConfigDefinition {
	if x != nil {
		return x.Definitions
	}
	return nil
}

func (x *GetNextConfigurationsResponse) GetActiveId() string {
	if x != nil {
		return x.ActiveId
	}
	return ""
}

type GetNextActiveConfResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	// Deprecated: Marked as deprecated in frontend/proto/thinning.proto.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Id   string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetNextActiveConfResponse) Reset() {
	*x = GetNextActiveConfResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveConfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveConfResponse) ProtoMessage() {}

func (x *GetNextActiveConfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveConfResponse.ProtoReflect.Descriptor instead.
func (*GetNextActiveConfResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextActiveConfResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

// Deprecated: Marked as deprecated in frontend/proto/thinning.proto.
func (x *GetNextActiveConfResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetNextActiveConfResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DefineConfigurationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Definition *thinning.ConfigDefinition `protobuf:"bytes,1,opt,name=definition,proto3" json:"definition,omitempty"`
	SetActive  bool                       `protobuf:"varint,2,opt,name=set_active,json=setActive,proto3" json:"set_active,omitempty"`
	Ver        ThinningConfVer            `protobuf:"varint,3,opt,name=ver,proto3,enum=carbon.frontend.thinning.ThinningConfVer" json:"ver,omitempty"`
}

func (x *DefineConfigurationRequest) Reset() {
	*x = DefineConfigurationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DefineConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefineConfigurationRequest) ProtoMessage() {}

func (x *DefineConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefineConfigurationRequest.ProtoReflect.Descriptor instead.
func (*DefineConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{2}
}

func (x *DefineConfigurationRequest) GetDefinition() *thinning.ConfigDefinition {
	if x != nil {
		return x.Definition
	}
	return nil
}

func (x *DefineConfigurationRequest) GetSetActive() bool {
	if x != nil {
		return x.SetActive
	}
	return false
}

func (x *DefineConfigurationRequest) GetVer() ThinningConfVer {
	if x != nil {
		return x.Ver
	}
	return ThinningConfVer_THIN_CONF_V1
}

type DefineConfigurationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // If new config then this will be the newly generated id for that config
}

func (x *DefineConfigurationResponse) Reset() {
	*x = DefineConfigurationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DefineConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefineConfigurationResponse) ProtoMessage() {}

func (x *DefineConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefineConfigurationResponse.ProtoReflect.Descriptor instead.
func (*DefineConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{3}
}

func (x *DefineConfigurationResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SetActiveConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/thinning.proto.
	Name string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id   string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Ver  ThinningConfVer `protobuf:"varint,3,opt,name=ver,proto3,enum=carbon.frontend.thinning.ThinningConfVer" json:"ver,omitempty"`
}

func (x *SetActiveConfigRequest) Reset() {
	*x = SetActiveConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveConfigRequest) ProtoMessage() {}

func (x *SetActiveConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveConfigRequest.ProtoReflect.Descriptor instead.
func (*SetActiveConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{4}
}

// Deprecated: Marked as deprecated in frontend/proto/thinning.proto.
func (x *SetActiveConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SetActiveConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetActiveConfigRequest) GetVer() ThinningConfVer {
	if x != nil {
		return x.Ver
	}
	return ThinningConfVer_THIN_CONF_V1
}

type SetActiveConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetActiveConfigResponse) Reset() {
	*x = SetActiveConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveConfigResponse) ProtoMessage() {}

func (x *SetActiveConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveConfigResponse.ProtoReflect.Descriptor instead.
func (*SetActiveConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{5}
}

type DeleteConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in frontend/proto/thinning.proto.
	Name        string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Id          string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Ver         ThinningConfVer `protobuf:"varint,3,opt,name=ver,proto3,enum=carbon.frontend.thinning.ThinningConfVer" json:"ver,omitempty"`
	NewActiveId string          `protobuf:"bytes,4,opt,name=new_active_id,json=newActiveId,proto3" json:"new_active_id,omitempty"` // If id was active must set this to a valid
}

func (x *DeleteConfigRequest) Reset() {
	*x = DeleteConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConfigRequest) ProtoMessage() {}

func (x *DeleteConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteConfigRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{6}
}

// Deprecated: Marked as deprecated in frontend/proto/thinning.proto.
func (x *DeleteConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteConfigRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteConfigRequest) GetVer() ThinningConfVer {
	if x != nil {
		return x.Ver
	}
	return ThinningConfVer_THIN_CONF_V1
}

func (x *DeleteConfigRequest) GetNewActiveId() string {
	if x != nil {
		return x.NewActiveId
	}
	return ""
}

type DeleteConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteConfigResponse) Reset() {
	*x = DeleteConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_thinning_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConfigResponse) ProtoMessage() {}

func (x *DeleteConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_thinning_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConfigResponse.ProtoReflect.Descriptor instead.
func (*DeleteConfigResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_thinning_proto_rawDescGZIP(), []int{7}
}

var File_frontend_proto_thinning_proto protoreflect.FileDescriptor

var file_frontend_proto_thinning_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x1a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x43, 0x0a, 0x0b, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xbb,
	0x01, 0x0a, 0x1a, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a,
	0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x3b, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x66, 0x56, 0x65, 0x72, 0x52, 0x03, 0x76, 0x65, 0x72, 0x22, 0x2d, 0x0a, 0x1b,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7d, 0x0a, 0x16, 0x53,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a,
	0x03, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f,
	0x6e, 0x66, 0x56, 0x65, 0x72, 0x52, 0x03, 0x76, 0x65, 0x72, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68,
	0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x56, 0x65, 0x72, 0x52, 0x03, 0x76,
	0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x35,
	0x0a, 0x0f, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x56, 0x65,
	0x72, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x48, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x5f, 0x56,
	0x31, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x48, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x5f, 0x56, 0x32, 0x10, 0x01, 0x32, 0xdb, 0x04, 0x0a, 0x0f, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x1a, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x1a, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0f,
	0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_thinning_proto_rawDescOnce sync.Once
	file_frontend_proto_thinning_proto_rawDescData = file_frontend_proto_thinning_proto_rawDesc
)

func file_frontend_proto_thinning_proto_rawDescGZIP() []byte {
	file_frontend_proto_thinning_proto_rawDescOnce.Do(func() {
		file_frontend_proto_thinning_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_thinning_proto_rawDescData)
	})
	return file_frontend_proto_thinning_proto_rawDescData
}

var file_frontend_proto_thinning_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_frontend_proto_thinning_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_frontend_proto_thinning_proto_goTypes = []interface{}{
	(ThinningConfVer)(0),                  // 0: carbon.frontend.thinning.ThinningConfVer
	(*GetNextConfigurationsResponse)(nil), // 1: carbon.frontend.thinning.GetNextConfigurationsResponse
	(*GetNextActiveConfResponse)(nil),     // 2: carbon.frontend.thinning.GetNextActiveConfResponse
	(*DefineConfigurationRequest)(nil),    // 3: carbon.frontend.thinning.DefineConfigurationRequest
	(*DefineConfigurationResponse)(nil),   // 4: carbon.frontend.thinning.DefineConfigurationResponse
	(*SetActiveConfigRequest)(nil),        // 5: carbon.frontend.thinning.SetActiveConfigRequest
	(*SetActiveConfigResponse)(nil),       // 6: carbon.frontend.thinning.SetActiveConfigResponse
	(*DeleteConfigRequest)(nil),           // 7: carbon.frontend.thinning.DeleteConfigRequest
	(*DeleteConfigResponse)(nil),          // 8: carbon.frontend.thinning.DeleteConfigResponse
	(*Timestamp)(nil),                     // 9: carbon.frontend.util.Timestamp
	(*thinning.ConfigDefinition)(nil),     // 10: carbon.thinning.ConfigDefinition
}
var file_frontend_proto_thinning_proto_depIdxs = []int32{
	9,  // 0: carbon.frontend.thinning.GetNextConfigurationsResponse.ts:type_name -> carbon.frontend.util.Timestamp
	10, // 1: carbon.frontend.thinning.GetNextConfigurationsResponse.definitions:type_name -> carbon.thinning.ConfigDefinition
	9,  // 2: carbon.frontend.thinning.GetNextActiveConfResponse.ts:type_name -> carbon.frontend.util.Timestamp
	10, // 3: carbon.frontend.thinning.DefineConfigurationRequest.definition:type_name -> carbon.thinning.ConfigDefinition
	0,  // 4: carbon.frontend.thinning.DefineConfigurationRequest.ver:type_name -> carbon.frontend.thinning.ThinningConfVer
	0,  // 5: carbon.frontend.thinning.SetActiveConfigRequest.ver:type_name -> carbon.frontend.thinning.ThinningConfVer
	0,  // 6: carbon.frontend.thinning.DeleteConfigRequest.ver:type_name -> carbon.frontend.thinning.ThinningConfVer
	9,  // 7: carbon.frontend.thinning.ThinningService.GetNextConfigurations:input_type -> carbon.frontend.util.Timestamp
	9,  // 8: carbon.frontend.thinning.ThinningService.GetNextActiveConf:input_type -> carbon.frontend.util.Timestamp
	3,  // 9: carbon.frontend.thinning.ThinningService.DefineConfiguration:input_type -> carbon.frontend.thinning.DefineConfigurationRequest
	5,  // 10: carbon.frontend.thinning.ThinningService.SetActiveConfig:input_type -> carbon.frontend.thinning.SetActiveConfigRequest
	7,  // 11: carbon.frontend.thinning.ThinningService.DeleteConfig:input_type -> carbon.frontend.thinning.DeleteConfigRequest
	1,  // 12: carbon.frontend.thinning.ThinningService.GetNextConfigurations:output_type -> carbon.frontend.thinning.GetNextConfigurationsResponse
	2,  // 13: carbon.frontend.thinning.ThinningService.GetNextActiveConf:output_type -> carbon.frontend.thinning.GetNextActiveConfResponse
	4,  // 14: carbon.frontend.thinning.ThinningService.DefineConfiguration:output_type -> carbon.frontend.thinning.DefineConfigurationResponse
	6,  // 15: carbon.frontend.thinning.ThinningService.SetActiveConfig:output_type -> carbon.frontend.thinning.SetActiveConfigResponse
	8,  // 16: carbon.frontend.thinning.ThinningService.DeleteConfig:output_type -> carbon.frontend.thinning.DeleteConfigResponse
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_frontend_proto_thinning_proto_init() }
func file_frontend_proto_thinning_proto_init() {
	if File_frontend_proto_thinning_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_thinning_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextConfigurationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveConfResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DefineConfigurationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DefineConfigurationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_thinning_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_thinning_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_thinning_proto_goTypes,
		DependencyIndexes: file_frontend_proto_thinning_proto_depIdxs,
		EnumInfos:         file_frontend_proto_thinning_proto_enumTypes,
		MessageInfos:      file_frontend_proto_thinning_proto_msgTypes,
	}.Build()
	File_frontend_proto_thinning_proto = out.File
	file_frontend_proto_thinning_proto_rawDesc = nil
	file_frontend_proto_thinning_proto_goTypes = nil
	file_frontend_proto_thinning_proto_depIdxs = nil
}

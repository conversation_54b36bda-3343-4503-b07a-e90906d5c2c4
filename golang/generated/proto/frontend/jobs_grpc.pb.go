// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/jobs.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	JobsService_GetNextJobs_FullMethodName         = "/carbon.frontend.jobs.JobsService/GetNextJobs"
	JobsService_CreateJob_FullMethodName           = "/carbon.frontend.jobs.JobsService/CreateJob"
	JobsService_UpdateJob_FullMethodName           = "/carbon.frontend.jobs.JobsService/UpdateJob"
	JobsService_StartJob_FullMethodName            = "/carbon.frontend.jobs.JobsService/StartJob"
	JobsService_StopActiveJob_FullMethodName       = "/carbon.frontend.jobs.JobsService/StopActiveJob"
	JobsService_GetNextActiveJobId_FullMethodName  = "/carbon.frontend.jobs.JobsService/GetNextActiveJobId"
	JobsService_GetJob_FullMethodName              = "/carbon.frontend.jobs.JobsService/GetJob"
	JobsService_GetConfigDump_FullMethodName       = "/carbon.frontend.jobs.JobsService/GetConfigDump"
	JobsService_GetActiveJobMetrics_FullMethodName = "/carbon.frontend.jobs.JobsService/GetActiveJobMetrics"
	JobsService_DeleteJob_FullMethodName           = "/carbon.frontend.jobs.JobsService/DeleteJob"
	JobsService_MarkJobCompleted_FullMethodName    = "/carbon.frontend.jobs.JobsService/MarkJobCompleted"
	JobsService_MarkJobIncomplete_FullMethodName   = "/carbon.frontend.jobs.JobsService/MarkJobIncomplete"
	JobsService_GetNextJob_FullMethodName          = "/carbon.frontend.jobs.JobsService/GetNextJob"
)

// JobsServiceClient is the client API for JobsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JobsServiceClient interface {
	GetNextJobs(ctx context.Context, in *GetNextJobsRequest, opts ...grpc.CallOption) (*GetNextJobsResponse, error)
	CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error)
	UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*Empty, error)
	StartJob(ctx context.Context, in *StartJobRequest, opts ...grpc.CallOption) (*Empty, error)
	StopActiveJob(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetNextActiveJobId(ctx context.Context, in *GetNextActiveJobIdRequest, opts ...grpc.CallOption) (*GetNextActiveJobIdResponse, error)
	GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*GetJobResponse, error)
	GetConfigDump(ctx context.Context, in *GetConfigDumpRequest, opts ...grpc.CallOption) (*GetConfigDumpResponse, error)
	GetActiveJobMetrics(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetActiveJobMetricsResponse, error)
	DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*Empty, error)
	MarkJobCompleted(ctx context.Context, in *MarkJobCompletedRequest, opts ...grpc.CallOption) (*Empty, error)
	MarkJobIncomplete(ctx context.Context, in *MarkJobIncompleteRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextJob(ctx context.Context, in *GetNextJobRequest, opts ...grpc.CallOption) (*GetNextJobResponse, error)
}

type jobsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJobsServiceClient(cc grpc.ClientConnInterface) JobsServiceClient {
	return &jobsServiceClient{cc}
}

func (c *jobsServiceClient) GetNextJobs(ctx context.Context, in *GetNextJobsRequest, opts ...grpc.CallOption) (*GetNextJobsResponse, error) {
	out := new(GetNextJobsResponse)
	err := c.cc.Invoke(ctx, JobsService_GetNextJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error) {
	out := new(CreateJobResponse)
	err := c.cc.Invoke(ctx, JobsService_CreateJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, JobsService_UpdateJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) StartJob(ctx context.Context, in *StartJobRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, JobsService_StartJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) StopActiveJob(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, JobsService_StopActiveJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) GetNextActiveJobId(ctx context.Context, in *GetNextActiveJobIdRequest, opts ...grpc.CallOption) (*GetNextActiveJobIdResponse, error) {
	out := new(GetNextActiveJobIdResponse)
	err := c.cc.Invoke(ctx, JobsService_GetNextActiveJobId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) GetJob(ctx context.Context, in *GetJobRequest, opts ...grpc.CallOption) (*GetJobResponse, error) {
	out := new(GetJobResponse)
	err := c.cc.Invoke(ctx, JobsService_GetJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) GetConfigDump(ctx context.Context, in *GetConfigDumpRequest, opts ...grpc.CallOption) (*GetConfigDumpResponse, error) {
	out := new(GetConfigDumpResponse)
	err := c.cc.Invoke(ctx, JobsService_GetConfigDump_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) GetActiveJobMetrics(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetActiveJobMetricsResponse, error) {
	out := new(GetActiveJobMetricsResponse)
	err := c.cc.Invoke(ctx, JobsService_GetActiveJobMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, JobsService_DeleteJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) MarkJobCompleted(ctx context.Context, in *MarkJobCompletedRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, JobsService_MarkJobCompleted_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) MarkJobIncomplete(ctx context.Context, in *MarkJobIncompleteRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, JobsService_MarkJobIncomplete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) GetNextJob(ctx context.Context, in *GetNextJobRequest, opts ...grpc.CallOption) (*GetNextJobResponse, error) {
	out := new(GetNextJobResponse)
	err := c.cc.Invoke(ctx, JobsService_GetNextJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobsServiceServer is the server API for JobsService service.
// All implementations must embed UnimplementedJobsServiceServer
// for forward compatibility
type JobsServiceServer interface {
	GetNextJobs(context.Context, *GetNextJobsRequest) (*GetNextJobsResponse, error)
	CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	UpdateJob(context.Context, *UpdateJobRequest) (*Empty, error)
	StartJob(context.Context, *StartJobRequest) (*Empty, error)
	StopActiveJob(context.Context, *Empty) (*Empty, error)
	GetNextActiveJobId(context.Context, *GetNextActiveJobIdRequest) (*GetNextActiveJobIdResponse, error)
	GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error)
	GetConfigDump(context.Context, *GetConfigDumpRequest) (*GetConfigDumpResponse, error)
	GetActiveJobMetrics(context.Context, *Empty) (*GetActiveJobMetricsResponse, error)
	DeleteJob(context.Context, *DeleteJobRequest) (*Empty, error)
	MarkJobCompleted(context.Context, *MarkJobCompletedRequest) (*Empty, error)
	MarkJobIncomplete(context.Context, *MarkJobIncompleteRequest) (*Empty, error)
	GetNextJob(context.Context, *GetNextJobRequest) (*GetNextJobResponse, error)
	mustEmbedUnimplementedJobsServiceServer()
}

// UnimplementedJobsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedJobsServiceServer struct {
}

func (UnimplementedJobsServiceServer) GetNextJobs(context.Context, *GetNextJobsRequest) (*GetNextJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextJobs not implemented")
}
func (UnimplementedJobsServiceServer) CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJob not implemented")
}
func (UnimplementedJobsServiceServer) UpdateJob(context.Context, *UpdateJobRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJob not implemented")
}
func (UnimplementedJobsServiceServer) StartJob(context.Context, *StartJobRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartJob not implemented")
}
func (UnimplementedJobsServiceServer) StopActiveJob(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopActiveJob not implemented")
}
func (UnimplementedJobsServiceServer) GetNextActiveJobId(context.Context, *GetNextActiveJobIdRequest) (*GetNextActiveJobIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActiveJobId not implemented")
}
func (UnimplementedJobsServiceServer) GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJob not implemented")
}
func (UnimplementedJobsServiceServer) GetConfigDump(context.Context, *GetConfigDumpRequest) (*GetConfigDumpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfigDump not implemented")
}
func (UnimplementedJobsServiceServer) GetActiveJobMetrics(context.Context, *Empty) (*GetActiveJobMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveJobMetrics not implemented")
}
func (UnimplementedJobsServiceServer) DeleteJob(context.Context, *DeleteJobRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJob not implemented")
}
func (UnimplementedJobsServiceServer) MarkJobCompleted(context.Context, *MarkJobCompletedRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkJobCompleted not implemented")
}
func (UnimplementedJobsServiceServer) MarkJobIncomplete(context.Context, *MarkJobIncompleteRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkJobIncomplete not implemented")
}
func (UnimplementedJobsServiceServer) GetNextJob(context.Context, *GetNextJobRequest) (*GetNextJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextJob not implemented")
}
func (UnimplementedJobsServiceServer) mustEmbedUnimplementedJobsServiceServer() {}

// UnsafeJobsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobsServiceServer will
// result in compilation errors.
type UnsafeJobsServiceServer interface {
	mustEmbedUnimplementedJobsServiceServer()
}

func RegisterJobsServiceServer(s grpc.ServiceRegistrar, srv JobsServiceServer) {
	s.RegisterService(&JobsService_ServiceDesc, srv)
}

func _JobsService_GetNextJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).GetNextJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_GetNextJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).GetNextJobs(ctx, req.(*GetNextJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_CreateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).CreateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_CreateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).CreateJob(ctx, req.(*CreateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_UpdateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).UpdateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_UpdateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).UpdateJob(ctx, req.(*UpdateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_StartJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).StartJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_StartJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).StartJob(ctx, req.(*StartJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_StopActiveJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).StopActiveJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_StopActiveJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).StopActiveJob(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_GetNextActiveJobId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextActiveJobIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).GetNextActiveJobId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_GetNextActiveJobId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).GetNextActiveJobId(ctx, req.(*GetNextActiveJobIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_GetJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).GetJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_GetJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).GetJob(ctx, req.(*GetJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_GetConfigDump_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigDumpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).GetConfigDump(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_GetConfigDump_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).GetConfigDump(ctx, req.(*GetConfigDumpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_GetActiveJobMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).GetActiveJobMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_GetActiveJobMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).GetActiveJobMetrics(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_DeleteJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).DeleteJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_DeleteJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).DeleteJob(ctx, req.(*DeleteJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_MarkJobCompleted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkJobCompletedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).MarkJobCompleted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_MarkJobCompleted_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).MarkJobCompleted(ctx, req.(*MarkJobCompletedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_MarkJobIncomplete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkJobIncompleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).MarkJobIncomplete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_MarkJobIncomplete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).MarkJobIncomplete(ctx, req.(*MarkJobIncompleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_GetNextJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).GetNextJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_GetNextJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).GetNextJob(ctx, req.(*GetNextJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JobsService_ServiceDesc is the grpc.ServiceDesc for JobsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JobsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.jobs.JobsService",
	HandlerType: (*JobsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextJobs",
			Handler:    _JobsService_GetNextJobs_Handler,
		},
		{
			MethodName: "CreateJob",
			Handler:    _JobsService_CreateJob_Handler,
		},
		{
			MethodName: "UpdateJob",
			Handler:    _JobsService_UpdateJob_Handler,
		},
		{
			MethodName: "StartJob",
			Handler:    _JobsService_StartJob_Handler,
		},
		{
			MethodName: "StopActiveJob",
			Handler:    _JobsService_StopActiveJob_Handler,
		},
		{
			MethodName: "GetNextActiveJobId",
			Handler:    _JobsService_GetNextActiveJobId_Handler,
		},
		{
			MethodName: "GetJob",
			Handler:    _JobsService_GetJob_Handler,
		},
		{
			MethodName: "GetConfigDump",
			Handler:    _JobsService_GetConfigDump_Handler,
		},
		{
			MethodName: "GetActiveJobMetrics",
			Handler:    _JobsService_GetActiveJobMetrics_Handler,
		},
		{
			MethodName: "DeleteJob",
			Handler:    _JobsService_DeleteJob_Handler,
		},
		{
			MethodName: "MarkJobCompleted",
			Handler:    _JobsService_MarkJobCompleted_Handler,
		},
		{
			MethodName: "MarkJobIncomplete",
			Handler:    _JobsService_MarkJobIncomplete_Handler,
		},
		{
			MethodName: "GetNextJob",
			Handler:    _JobsService_GetNextJob_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/jobs.proto",
}

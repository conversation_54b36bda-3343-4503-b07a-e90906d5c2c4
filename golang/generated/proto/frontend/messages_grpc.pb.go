// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/messages.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessagesService_ReadMessage_FullMethodName     = "/carbon.frontend.debug.MessagesService/ReadMessage"
	MessagesService_SendMessage_FullMethodName     = "/carbon.frontend.debug.MessagesService/SendMessage"
	MessagesService_GetNextMessages_FullMethodName = "/carbon.frontend.debug.MessagesService/GetNextMessages"
)

// MessagesServiceClient is the client API for MessagesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessagesServiceClient interface {
	ReadMessage(ctx context.Context, in *ReadRequest, opts ...grpc.CallOption) (*Empty, error)
	SendMessage(ctx context.Context, in *MessageRequest, opts ...grpc.CallOption) (*Empty, error)
	GetNextMessages(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*MessagesResponse, error)
}

type messagesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessagesServiceClient(cc grpc.ClientConnInterface) MessagesServiceClient {
	return &messagesServiceClient{cc}
}

func (c *messagesServiceClient) ReadMessage(ctx context.Context, in *ReadRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MessagesService_ReadMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messagesServiceClient) SendMessage(ctx context.Context, in *MessageRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MessagesService_SendMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messagesServiceClient) GetNextMessages(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*MessagesResponse, error) {
	out := new(MessagesResponse)
	err := c.cc.Invoke(ctx, MessagesService_GetNextMessages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessagesServiceServer is the server API for MessagesService service.
// All implementations must embed UnimplementedMessagesServiceServer
// for forward compatibility
type MessagesServiceServer interface {
	ReadMessage(context.Context, *ReadRequest) (*Empty, error)
	SendMessage(context.Context, *MessageRequest) (*Empty, error)
	GetNextMessages(context.Context, *Timestamp) (*MessagesResponse, error)
	mustEmbedUnimplementedMessagesServiceServer()
}

// UnimplementedMessagesServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMessagesServiceServer struct {
}

func (UnimplementedMessagesServiceServer) ReadMessage(context.Context, *ReadRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadMessage not implemented")
}
func (UnimplementedMessagesServiceServer) SendMessage(context.Context, *MessageRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedMessagesServiceServer) GetNextMessages(context.Context, *Timestamp) (*MessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextMessages not implemented")
}
func (UnimplementedMessagesServiceServer) mustEmbedUnimplementedMessagesServiceServer() {}

// UnsafeMessagesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessagesServiceServer will
// result in compilation errors.
type UnsafeMessagesServiceServer interface {
	mustEmbedUnimplementedMessagesServiceServer()
}

func RegisterMessagesServiceServer(s grpc.ServiceRegistrar, srv MessagesServiceServer) {
	s.RegisterService(&MessagesService_ServiceDesc, srv)
}

func _MessagesService_ReadMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessagesServiceServer).ReadMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessagesService_ReadMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessagesServiceServer).ReadMessage(ctx, req.(*ReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessagesService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessagesServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessagesService_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessagesServiceServer).SendMessage(ctx, req.(*MessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessagesService_GetNextMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessagesServiceServer).GetNextMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessagesService_GetNextMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessagesServiceServer).GetNextMessages(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

// MessagesService_ServiceDesc is the grpc.ServiceDesc for MessagesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessagesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.debug.MessagesService",
	HandlerType: (*MessagesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReadMessage",
			Handler:    _MessagesService_ReadMessage_Handler,
		},
		{
			MethodName: "SendMessage",
			Handler:    _MessagesService_SendMessage_Handler,
		},
		{
			MethodName: "GetNextMessages",
			Handler:    _MessagesService_GetNextMessages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/messages.proto",
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/category_collection.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CategoryCollectionService_GetNextCategoryCollectionsData_FullMethodName    = "/carbon.frontend.category_collection.CategoryCollectionService/GetNextCategoryCollectionsData"
	CategoryCollectionService_GetNextActiveCategoryCollectionId_FullMethodName = "/carbon.frontend.category_collection.CategoryCollectionService/GetNextActiveCategoryCollectionId"
	CategoryCollectionService_SetActiveCategoryCollectionId_FullMethodName     = "/carbon.frontend.category_collection.CategoryCollectionService/SetActiveCategoryCollectionId"
	CategoryCollectionService_ReloadCategoryCollection_FullMethodName          = "/carbon.frontend.category_collection.CategoryCollectionService/ReloadCategoryCollection"
)

// CategoryCollectionServiceClient is the client API for CategoryCollectionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CategoryCollectionServiceClient interface {
	GetNextCategoryCollectionsData(ctx context.Context, in *GetNextCategoryCollectionsDataRequest, opts ...grpc.CallOption) (*GetNextCategoryCollectionsDataResponse, error)
	GetNextActiveCategoryCollectionId(ctx context.Context, in *GetNextActiveCategoryCollectionIdRequest, opts ...grpc.CallOption) (*GetNextActiveCategoryCollectionIdResponse, error)
	SetActiveCategoryCollectionId(ctx context.Context, in *SetActiveCategoryCollectionIdRequest, opts ...grpc.CallOption) (*Empty, error)
	ReloadCategoryCollection(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type categoryCollectionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCategoryCollectionServiceClient(cc grpc.ClientConnInterface) CategoryCollectionServiceClient {
	return &categoryCollectionServiceClient{cc}
}

func (c *categoryCollectionServiceClient) GetNextCategoryCollectionsData(ctx context.Context, in *GetNextCategoryCollectionsDataRequest, opts ...grpc.CallOption) (*GetNextCategoryCollectionsDataResponse, error) {
	out := new(GetNextCategoryCollectionsDataResponse)
	err := c.cc.Invoke(ctx, CategoryCollectionService_GetNextCategoryCollectionsData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryCollectionServiceClient) GetNextActiveCategoryCollectionId(ctx context.Context, in *GetNextActiveCategoryCollectionIdRequest, opts ...grpc.CallOption) (*GetNextActiveCategoryCollectionIdResponse, error) {
	out := new(GetNextActiveCategoryCollectionIdResponse)
	err := c.cc.Invoke(ctx, CategoryCollectionService_GetNextActiveCategoryCollectionId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryCollectionServiceClient) SetActiveCategoryCollectionId(ctx context.Context, in *SetActiveCategoryCollectionIdRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CategoryCollectionService_SetActiveCategoryCollectionId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *categoryCollectionServiceClient) ReloadCategoryCollection(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CategoryCollectionService_ReloadCategoryCollection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CategoryCollectionServiceServer is the server API for CategoryCollectionService service.
// All implementations must embed UnimplementedCategoryCollectionServiceServer
// for forward compatibility
type CategoryCollectionServiceServer interface {
	GetNextCategoryCollectionsData(context.Context, *GetNextCategoryCollectionsDataRequest) (*GetNextCategoryCollectionsDataResponse, error)
	GetNextActiveCategoryCollectionId(context.Context, *GetNextActiveCategoryCollectionIdRequest) (*GetNextActiveCategoryCollectionIdResponse, error)
	SetActiveCategoryCollectionId(context.Context, *SetActiveCategoryCollectionIdRequest) (*Empty, error)
	ReloadCategoryCollection(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedCategoryCollectionServiceServer()
}

// UnimplementedCategoryCollectionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCategoryCollectionServiceServer struct {
}

func (UnimplementedCategoryCollectionServiceServer) GetNextCategoryCollectionsData(context.Context, *GetNextCategoryCollectionsDataRequest) (*GetNextCategoryCollectionsDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextCategoryCollectionsData not implemented")
}
func (UnimplementedCategoryCollectionServiceServer) GetNextActiveCategoryCollectionId(context.Context, *GetNextActiveCategoryCollectionIdRequest) (*GetNextActiveCategoryCollectionIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActiveCategoryCollectionId not implemented")
}
func (UnimplementedCategoryCollectionServiceServer) SetActiveCategoryCollectionId(context.Context, *SetActiveCategoryCollectionIdRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetActiveCategoryCollectionId not implemented")
}
func (UnimplementedCategoryCollectionServiceServer) ReloadCategoryCollection(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadCategoryCollection not implemented")
}
func (UnimplementedCategoryCollectionServiceServer) mustEmbedUnimplementedCategoryCollectionServiceServer() {
}

// UnsafeCategoryCollectionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CategoryCollectionServiceServer will
// result in compilation errors.
type UnsafeCategoryCollectionServiceServer interface {
	mustEmbedUnimplementedCategoryCollectionServiceServer()
}

func RegisterCategoryCollectionServiceServer(s grpc.ServiceRegistrar, srv CategoryCollectionServiceServer) {
	s.RegisterService(&CategoryCollectionService_ServiceDesc, srv)
}

func _CategoryCollectionService_GetNextCategoryCollectionsData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextCategoryCollectionsDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryCollectionServiceServer).GetNextCategoryCollectionsData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryCollectionService_GetNextCategoryCollectionsData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryCollectionServiceServer).GetNextCategoryCollectionsData(ctx, req.(*GetNextCategoryCollectionsDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryCollectionService_GetNextActiveCategoryCollectionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextActiveCategoryCollectionIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryCollectionServiceServer).GetNextActiveCategoryCollectionId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryCollectionService_GetNextActiveCategoryCollectionId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryCollectionServiceServer).GetNextActiveCategoryCollectionId(ctx, req.(*GetNextActiveCategoryCollectionIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryCollectionService_SetActiveCategoryCollectionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActiveCategoryCollectionIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryCollectionServiceServer).SetActiveCategoryCollectionId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryCollectionService_SetActiveCategoryCollectionId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryCollectionServiceServer).SetActiveCategoryCollectionId(ctx, req.(*SetActiveCategoryCollectionIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CategoryCollectionService_ReloadCategoryCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryCollectionServiceServer).ReloadCategoryCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryCollectionService_ReloadCategoryCollection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryCollectionServiceServer).ReloadCategoryCollection(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// CategoryCollectionService_ServiceDesc is the grpc.ServiceDesc for CategoryCollectionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CategoryCollectionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.category_collection.CategoryCollectionService",
	HandlerType: (*CategoryCollectionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextCategoryCollectionsData",
			Handler:    _CategoryCollectionService_GetNextCategoryCollectionsData_Handler,
		},
		{
			MethodName: "GetNextActiveCategoryCollectionId",
			Handler:    _CategoryCollectionService_GetNextActiveCategoryCollectionId_Handler,
		},
		{
			MethodName: "SetActiveCategoryCollectionId",
			Handler:    _CategoryCollectionService_SetActiveCategoryCollectionId_Handler,
		},
		{
			MethodName: "ReloadCategoryCollection",
			Handler:    _CategoryCollectionService_ReloadCategoryCollection_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/category_collection.proto",
}

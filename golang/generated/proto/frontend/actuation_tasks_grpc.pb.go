// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/actuation_tasks.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ActuationTasksService_GetNextGlobalActuationTaskState_FullMethodName = "/carbon.frontend.actuation_tasks.ActuationTasksService/GetNextGlobalActuationTaskState"
	ActuationTasksService_StartGlobalAimbotActuationTask_FullMethodName  = "/carbon.frontend.actuation_tasks.ActuationTasksService/StartGlobalAimbotActuationTask"
	ActuationTasksService_CancelGlobalAimbotActuationTask_FullMethodName = "/carbon.frontend.actuation_tasks.ActuationTasksService/CancelGlobalAimbotActuationTask"
)

// ActuationTasksServiceClient is the client API for ActuationTasksService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ActuationTasksServiceClient interface {
	GetNextGlobalActuationTaskState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GlobalActuationTaskState, error)
	StartGlobalAimbotActuationTask(ctx context.Context, in *GlobalAimbotActuationTaskRequest, opts ...grpc.CallOption) (*Empty, error)
	CancelGlobalAimbotActuationTask(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type actuationTasksServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewActuationTasksServiceClient(cc grpc.ClientConnInterface) ActuationTasksServiceClient {
	return &actuationTasksServiceClient{cc}
}

func (c *actuationTasksServiceClient) GetNextGlobalActuationTaskState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GlobalActuationTaskState, error) {
	out := new(GlobalActuationTaskState)
	err := c.cc.Invoke(ctx, ActuationTasksService_GetNextGlobalActuationTaskState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *actuationTasksServiceClient) StartGlobalAimbotActuationTask(ctx context.Context, in *GlobalAimbotActuationTaskRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ActuationTasksService_StartGlobalAimbotActuationTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *actuationTasksServiceClient) CancelGlobalAimbotActuationTask(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ActuationTasksService_CancelGlobalAimbotActuationTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ActuationTasksServiceServer is the server API for ActuationTasksService service.
// All implementations must embed UnimplementedActuationTasksServiceServer
// for forward compatibility
type ActuationTasksServiceServer interface {
	GetNextGlobalActuationTaskState(context.Context, *Timestamp) (*GlobalActuationTaskState, error)
	StartGlobalAimbotActuationTask(context.Context, *GlobalAimbotActuationTaskRequest) (*Empty, error)
	CancelGlobalAimbotActuationTask(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedActuationTasksServiceServer()
}

// UnimplementedActuationTasksServiceServer must be embedded to have forward compatible implementations.
type UnimplementedActuationTasksServiceServer struct {
}

func (UnimplementedActuationTasksServiceServer) GetNextGlobalActuationTaskState(context.Context, *Timestamp) (*GlobalActuationTaskState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextGlobalActuationTaskState not implemented")
}
func (UnimplementedActuationTasksServiceServer) StartGlobalAimbotActuationTask(context.Context, *GlobalAimbotActuationTaskRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartGlobalAimbotActuationTask not implemented")
}
func (UnimplementedActuationTasksServiceServer) CancelGlobalAimbotActuationTask(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelGlobalAimbotActuationTask not implemented")
}
func (UnimplementedActuationTasksServiceServer) mustEmbedUnimplementedActuationTasksServiceServer() {}

// UnsafeActuationTasksServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ActuationTasksServiceServer will
// result in compilation errors.
type UnsafeActuationTasksServiceServer interface {
	mustEmbedUnimplementedActuationTasksServiceServer()
}

func RegisterActuationTasksServiceServer(s grpc.ServiceRegistrar, srv ActuationTasksServiceServer) {
	s.RegisterService(&ActuationTasksService_ServiceDesc, srv)
}

func _ActuationTasksService_GetNextGlobalActuationTaskState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActuationTasksServiceServer).GetNextGlobalActuationTaskState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActuationTasksService_GetNextGlobalActuationTaskState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActuationTasksServiceServer).GetNextGlobalActuationTaskState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActuationTasksService_StartGlobalAimbotActuationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GlobalAimbotActuationTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActuationTasksServiceServer).StartGlobalAimbotActuationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActuationTasksService_StartGlobalAimbotActuationTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActuationTasksServiceServer).StartGlobalAimbotActuationTask(ctx, req.(*GlobalAimbotActuationTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActuationTasksService_CancelGlobalAimbotActuationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActuationTasksServiceServer).CancelGlobalAimbotActuationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActuationTasksService_CancelGlobalAimbotActuationTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActuationTasksServiceServer).CancelGlobalAimbotActuationTask(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ActuationTasksService_ServiceDesc is the grpc.ServiceDesc for ActuationTasksService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ActuationTasksService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.actuation_tasks.ActuationTasksService",
	HandlerType: (*ActuationTasksServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextGlobalActuationTaskState",
			Handler:    _ActuationTasksService_GetNextGlobalActuationTaskState_Handler,
		},
		{
			MethodName: "StartGlobalAimbotActuationTask",
			Handler:    _ActuationTasksService_StartGlobalAimbotActuationTask_Handler,
		},
		{
			MethodName: "CancelGlobalAimbotActuationTask",
			Handler:    _ActuationTasksService_CancelGlobalAimbotActuationTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/actuation_tasks.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/status_bar.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Status int32

const (
	Status_STATUS_ERROR                     Status = 0
	Status_STATUS_ESTOPPED                  Status = 1
	Status_STATUS_PRE_ARMED                 Status = 2
	Status_STATUS_POWERED_DOWN              Status = 3
	Status_STATUS_POWERING_UP               Status = 4
	Status_STATUS_UPDATE_INSTALLING         Status = 5
	Status_STATUS_MODEL_LOADING             Status = 6
	Status_STATUS_MODEL_INSTALLING          Status = 7
	Status_STATUS_WEEDING                   Status = 8
	Status_STATUS_STANDBY                   Status = 9
	Status_STATUS_UNKNOWN                   Status = 10
	Status_STATUS_DISCONNECTED              Status = 11
	Status_STATUS_LIFTED                    Status = 12
	Status_STATUS_LOADING                   Status = 13
	Status_STATUS_ALARM_AUTOFIX_IN_PROGRESS Status = 14
	Status_STATUS_FAILED_TO_POWER_UP        Status = 15
	Status_STATUS_SERVER_CABINET_COOLDOWN   Status = 16
	Status_STATUS_CHILLER_COOLDOWN          Status = 17
	Status_STATUS_TRACTOR_NOT_SAFE          Status = 18
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0:  "STATUS_ERROR",
		1:  "STATUS_ESTOPPED",
		2:  "STATUS_PRE_ARMED",
		3:  "STATUS_POWERED_DOWN",
		4:  "STATUS_POWERING_UP",
		5:  "STATUS_UPDATE_INSTALLING",
		6:  "STATUS_MODEL_LOADING",
		7:  "STATUS_MODEL_INSTALLING",
		8:  "STATUS_WEEDING",
		9:  "STATUS_STANDBY",
		10: "STATUS_UNKNOWN",
		11: "STATUS_DISCONNECTED",
		12: "STATUS_LIFTED",
		13: "STATUS_LOADING",
		14: "STATUS_ALARM_AUTOFIX_IN_PROGRESS",
		15: "STATUS_FAILED_TO_POWER_UP",
		16: "STATUS_SERVER_CABINET_COOLDOWN",
		17: "STATUS_CHILLER_COOLDOWN",
		18: "STATUS_TRACTOR_NOT_SAFE",
	}
	Status_value = map[string]int32{
		"STATUS_ERROR":                     0,
		"STATUS_ESTOPPED":                  1,
		"STATUS_PRE_ARMED":                 2,
		"STATUS_POWERED_DOWN":              3,
		"STATUS_POWERING_UP":               4,
		"STATUS_UPDATE_INSTALLING":         5,
		"STATUS_MODEL_LOADING":             6,
		"STATUS_MODEL_INSTALLING":          7,
		"STATUS_WEEDING":                   8,
		"STATUS_STANDBY":                   9,
		"STATUS_UNKNOWN":                   10,
		"STATUS_DISCONNECTED":              11,
		"STATUS_LIFTED":                    12,
		"STATUS_LOADING":                   13,
		"STATUS_ALARM_AUTOFIX_IN_PROGRESS": 14,
		"STATUS_FAILED_TO_POWER_UP":        15,
		"STATUS_SERVER_CABINET_COOLDOWN":   16,
		"STATUS_CHILLER_COOLDOWN":          17,
		"STATUS_TRACTOR_NOT_SAFE":          18,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_status_bar_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_frontend_proto_status_bar_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{0}
}

type StatusLevel int32

const (
	StatusLevel_INVALID StatusLevel = 0
	StatusLevel_READY   StatusLevel = 1
	StatusLevel_LOADING StatusLevel = 2
	// Deprecated: Marked as deprecated in frontend/proto/status_bar.proto.
	StatusLevel_ESTOPPED StatusLevel = 3
)

// Enum value maps for StatusLevel.
var (
	StatusLevel_name = map[int32]string{
		0: "INVALID",
		1: "READY",
		2: "LOADING",
		3: "ESTOPPED",
	}
	StatusLevel_value = map[string]int32{
		"INVALID":  0,
		"READY":    1,
		"LOADING":  2,
		"ESTOPPED": 3,
	}
)

func (x StatusLevel) Enum() *StatusLevel {
	p := new(StatusLevel)
	*p = x
	return p
}

func (x StatusLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_status_bar_proto_enumTypes[1].Descriptor()
}

func (StatusLevel) Type() protoreflect.EnumType {
	return &file_frontend_proto_status_bar_proto_enumTypes[1]
}

func (x StatusLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusLevel.Descriptor instead.
func (StatusLevel) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{1}
}

type GlobalStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hint      string `protobuf:"bytes,1,opt,name=hint,proto3" json:"hint,omitempty"`
	IconName  string `protobuf:"bytes,2,opt,name=icon_name,json=iconName,proto3" json:"icon_name,omitempty"`
	IconColor string `protobuf:"bytes,3,opt,name=icon_color,json=iconColor,proto3" json:"icon_color,omitempty"`
}

func (x *GlobalStatus) Reset() {
	*x = GlobalStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalStatus) ProtoMessage() {}

func (x *GlobalStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalStatus.ProtoReflect.Descriptor instead.
func (*GlobalStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{0}
}

func (x *GlobalStatus) GetHint() string {
	if x != nil {
		return x.Hint
	}
	return ""
}

func (x *GlobalStatus) GetIconName() string {
	if x != nil {
		return x.IconName
	}
	return ""
}

func (x *GlobalStatus) GetIconColor() string {
	if x != nil {
		return x.IconColor
	}
	return ""
}

type ServiceStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	StatusLevel StatusLevel `protobuf:"varint,2,opt,name=status_level,json=statusLevel,proto3,enum=carbon.frontend.status_bar.StatusLevel" json:"status_level,omitempty"`
}

func (x *ServiceStatus) Reset() {
	*x = ServiceStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceStatus) ProtoMessage() {}

func (x *ServiceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceStatus.ProtoReflect.Descriptor instead.
func (*ServiceStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceStatus) GetStatusLevel() StatusLevel {
	if x != nil {
		return x.StatusLevel
	}
	return StatusLevel_INVALID
}

type ServerStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusLevel   StatusLevel      `protobuf:"varint,1,opt,name=status_level,json=statusLevel,proto3,enum=carbon.frontend.status_bar.StatusLevel" json:"status_level,omitempty"`
	ServiceStatus []*ServiceStatus `protobuf:"bytes,2,rep,name=service_status,json=serviceStatus,proto3" json:"service_status,omitempty"`
}

func (x *ServerStatus) Reset() {
	*x = ServerStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerStatus) ProtoMessage() {}

func (x *ServerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerStatus.ProtoReflect.Descriptor instead.
func (*ServerStatus) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{2}
}

func (x *ServerStatus) GetStatusLevel() StatusLevel {
	if x != nil {
		return x.StatusLevel
	}
	return StatusLevel_INVALID
}

func (x *ServerStatus) GetServiceStatus() []*ServiceStatus {
	if x != nil {
		return x.ServiceStatus
	}
	return nil
}

type TranslatedStatusMessageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*TranslatedStatusMessageDetails_DetailsStringKey
	//	*TranslatedStatusMessageDetails_Timer
	Details isTranslatedStatusMessageDetails_Details `protobuf_oneof:"details"`
}

func (x *TranslatedStatusMessageDetails) Reset() {
	*x = TranslatedStatusMessageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslatedStatusMessageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslatedStatusMessageDetails) ProtoMessage() {}

func (x *TranslatedStatusMessageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslatedStatusMessageDetails.ProtoReflect.Descriptor instead.
func (*TranslatedStatusMessageDetails) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{3}
}

func (m *TranslatedStatusMessageDetails) GetDetails() isTranslatedStatusMessageDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *TranslatedStatusMessageDetails) GetDetailsStringKey() string {
	if x, ok := x.GetDetails().(*TranslatedStatusMessageDetails_DetailsStringKey); ok {
		return x.DetailsStringKey
	}
	return ""
}

func (x *TranslatedStatusMessageDetails) GetTimer() *DurationValue {
	if x, ok := x.GetDetails().(*TranslatedStatusMessageDetails_Timer); ok {
		return x.Timer
	}
	return nil
}

type isTranslatedStatusMessageDetails_Details interface {
	isTranslatedStatusMessageDetails_Details()
}

type TranslatedStatusMessageDetails_DetailsStringKey struct {
	DetailsStringKey string `protobuf:"bytes,1,opt,name=details_string_key,json=detailsStringKey,proto3,oneof"`
}

type TranslatedStatusMessageDetails_Timer struct {
	Timer *DurationValue `protobuf:"bytes,2,opt,name=timer,proto3,oneof"`
}

func (*TranslatedStatusMessageDetails_DetailsStringKey) isTranslatedStatusMessageDetails_Details() {}

func (*TranslatedStatusMessageDetails_Timer) isTranslatedStatusMessageDetails_Details() {}

type TranslatedStatusMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prefix  string                          `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Details *TranslatedStatusMessageDetails `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *TranslatedStatusMessage) Reset() {
	*x = TranslatedStatusMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslatedStatusMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslatedStatusMessage) ProtoMessage() {}

func (x *TranslatedStatusMessage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslatedStatusMessage.ProtoReflect.Descriptor instead.
func (*TranslatedStatusMessage) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{4}
}

func (x *TranslatedStatusMessage) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *TranslatedStatusMessage) GetDetails() *TranslatedStatusMessageDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

type StatusBarMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts *Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	// bool estopped = 2; Deprecated
	LasersEnabled           bool                     `protobuf:"varint,3,opt,name=lasers_enabled,json=lasersEnabled,proto3" json:"lasers_enabled,omitempty"`
	WeedingEnabled          bool                     `protobuf:"varint,4,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	StatusLevel             Status                   `protobuf:"varint,5,opt,name=status_level,json=statusLevel,proto3,enum=carbon.frontend.status_bar.Status" json:"status_level,omitempty"`
	StatusMessage           string                   `protobuf:"bytes,6,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	Serial                  string                   `protobuf:"bytes,7,opt,name=serial,proto3" json:"serial,omitempty"`
	RowStatus               map[int32]*ServerStatus  `protobuf:"bytes,8,rep,name=row_status,json=rowStatus,proto3" json:"row_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CommandStatus           *ServerStatus            `protobuf:"bytes,9,opt,name=command_status,json=commandStatus,proto3" json:"command_status,omitempty"`
	GlobalStatuses          []*GlobalStatus          `protobuf:"bytes,10,rep,name=global_statuses,json=globalStatuses,proto3" json:"global_statuses,omitempty"`
	TranslatedStatusMessage *TranslatedStatusMessage `protobuf:"bytes,11,opt,name=translated_status_message,json=translatedStatusMessage,proto3" json:"translated_status_message,omitempty"`
}

func (x *StatusBarMessage) Reset() {
	*x = StatusBarMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusBarMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusBarMessage) ProtoMessage() {}

func (x *StatusBarMessage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusBarMessage.ProtoReflect.Descriptor instead.
func (*StatusBarMessage) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{5}
}

func (x *StatusBarMessage) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *StatusBarMessage) GetLasersEnabled() bool {
	if x != nil {
		return x.LasersEnabled
	}
	return false
}

func (x *StatusBarMessage) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *StatusBarMessage) GetStatusLevel() Status {
	if x != nil {
		return x.StatusLevel
	}
	return Status_STATUS_ERROR
}

func (x *StatusBarMessage) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *StatusBarMessage) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *StatusBarMessage) GetRowStatus() map[int32]*ServerStatus {
	if x != nil {
		return x.RowStatus
	}
	return nil
}

func (x *StatusBarMessage) GetCommandStatus() *ServerStatus {
	if x != nil {
		return x.CommandStatus
	}
	return nil
}

func (x *StatusBarMessage) GetGlobalStatuses() []*GlobalStatus {
	if x != nil {
		return x.GlobalStatuses
	}
	return nil
}

func (x *StatusBarMessage) GetTranslatedStatusMessage() *TranslatedStatusMessage {
	if x != nil {
		return x.TranslatedStatusMessage
	}
	return nil
}

type ReportIssueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *ReportIssueRequest) Reset() {
	*x = ReportIssueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportIssueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportIssueRequest) ProtoMessage() {}

func (x *ReportIssueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportIssueRequest.ProtoReflect.Descriptor instead.
func (*ReportIssueRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{6}
}

func (x *ReportIssueRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ReportIssueRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type SupportPhoneMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SupportPhone string `protobuf:"bytes,1,opt,name=support_phone,json=supportPhone,proto3" json:"support_phone,omitempty"`
}

func (x *SupportPhoneMessage) Reset() {
	*x = SupportPhoneMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_status_bar_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupportPhoneMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportPhoneMessage) ProtoMessage() {}

func (x *SupportPhoneMessage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_status_bar_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportPhoneMessage.ProtoReflect.Descriptor instead.
func (*SupportPhoneMessage) Descriptor() ([]byte, []int) {
	return file_frontend_proto_status_bar_proto_rawDescGZIP(), []int{7}
}

func (x *SupportPhoneMessage) GetSupportPhone() string {
	if x != nil {
		return x.SupportPhone
	}
	return ""
}

var File_frontend_proto_status_bar_proto protoreflect.FileDescriptor

var file_frontend_proto_status_bar_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x1a, 0x20, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5e, 0x0a, 0x0c, 0x47, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x69,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x69, 0x6e, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x63, 0x6f, 0x6e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x6f, 0x0a, 0x0d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x4a, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62,
	0x61, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x0b,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0xac, 0x01, 0x0a, 0x0c,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0c,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x50, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x1e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a,
	0x12, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x10, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x12, 0x42, 0x0a,
	0x05, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65,
	0x72, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x87, 0x01, 0x0a,
	0x17, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x12, 0x54, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xf2, 0x05, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x61, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x45, 0x0a, 0x0c,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x12, 0x5a, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x62, 0x61, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x61, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x52, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x62, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x51, 0x0a, 0x0f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0e, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x12, 0x6f, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62,
	0x61, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x17, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x1a, 0x66, 0x0a, 0x0e, 0x52, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x62, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x59, 0x0a, 0x12, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x3a, 0x0a, 0x13, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x2a, 0xea, 0x03, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a,
	0x0c, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x50,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50,
	0x52, 0x45, 0x5f, 0x41, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x57,
	0x4e, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x07,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x41, 0x4e, 0x44, 0x42, 0x59, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4c, 0x49, 0x46, 0x54, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0d, 0x12, 0x24, 0x0a, 0x20,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x5f, 0x41, 0x55, 0x54,
	0x4f, 0x46, 0x49, 0x58, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x0e, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x55, 0x50, 0x10,
	0x0f, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x45, 0x52, 0x5f, 0x43, 0x41, 0x42, 0x49, 0x4e, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x44,
	0x4f, 0x57, 0x4e, 0x10, 0x10, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x48, 0x49, 0x4c, 0x4c, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x44, 0x4f, 0x57, 0x4e,
	0x10, 0x11, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x10, 0x12, 0x2a,
	0x44, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x08, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10,
	0x03, 0x1a, 0x02, 0x08, 0x01, 0x32, 0xaf, 0x02, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x61, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x2c, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x61, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5a, 0x0a, 0x0b, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x12, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x62, 0x61, 0x72, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_frontend_proto_status_bar_proto_rawDescOnce sync.Once
	file_frontend_proto_status_bar_proto_rawDescData = file_frontend_proto_status_bar_proto_rawDesc
)

func file_frontend_proto_status_bar_proto_rawDescGZIP() []byte {
	file_frontend_proto_status_bar_proto_rawDescOnce.Do(func() {
		file_frontend_proto_status_bar_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_status_bar_proto_rawDescData)
	})
	return file_frontend_proto_status_bar_proto_rawDescData
}

var file_frontend_proto_status_bar_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_proto_status_bar_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_frontend_proto_status_bar_proto_goTypes = []interface{}{
	(Status)(0),                            // 0: carbon.frontend.status_bar.Status
	(StatusLevel)(0),                       // 1: carbon.frontend.status_bar.StatusLevel
	(*GlobalStatus)(nil),                   // 2: carbon.frontend.status_bar.GlobalStatus
	(*ServiceStatus)(nil),                  // 3: carbon.frontend.status_bar.ServiceStatus
	(*ServerStatus)(nil),                   // 4: carbon.frontend.status_bar.ServerStatus
	(*TranslatedStatusMessageDetails)(nil), // 5: carbon.frontend.status_bar.TranslatedStatusMessageDetails
	(*TranslatedStatusMessage)(nil),        // 6: carbon.frontend.status_bar.TranslatedStatusMessage
	(*StatusBarMessage)(nil),               // 7: carbon.frontend.status_bar.StatusBarMessage
	(*ReportIssueRequest)(nil),             // 8: carbon.frontend.status_bar.ReportIssueRequest
	(*SupportPhoneMessage)(nil),            // 9: carbon.frontend.status_bar.SupportPhoneMessage
	nil,                                    // 10: carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry
	(*DurationValue)(nil),                  // 11: carbon.frontend.translation.DurationValue
	(*Timestamp)(nil),                      // 12: carbon.frontend.util.Timestamp
	(*Empty)(nil),                          // 13: carbon.frontend.util.Empty
}
var file_frontend_proto_status_bar_proto_depIdxs = []int32{
	1,  // 0: carbon.frontend.status_bar.ServiceStatus.status_level:type_name -> carbon.frontend.status_bar.StatusLevel
	1,  // 1: carbon.frontend.status_bar.ServerStatus.status_level:type_name -> carbon.frontend.status_bar.StatusLevel
	3,  // 2: carbon.frontend.status_bar.ServerStatus.service_status:type_name -> carbon.frontend.status_bar.ServiceStatus
	11, // 3: carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer:type_name -> carbon.frontend.translation.DurationValue
	5,  // 4: carbon.frontend.status_bar.TranslatedStatusMessage.details:type_name -> carbon.frontend.status_bar.TranslatedStatusMessageDetails
	12, // 5: carbon.frontend.status_bar.StatusBarMessage.ts:type_name -> carbon.frontend.util.Timestamp
	0,  // 6: carbon.frontend.status_bar.StatusBarMessage.status_level:type_name -> carbon.frontend.status_bar.Status
	10, // 7: carbon.frontend.status_bar.StatusBarMessage.row_status:type_name -> carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry
	4,  // 8: carbon.frontend.status_bar.StatusBarMessage.command_status:type_name -> carbon.frontend.status_bar.ServerStatus
	2,  // 9: carbon.frontend.status_bar.StatusBarMessage.global_statuses:type_name -> carbon.frontend.status_bar.GlobalStatus
	6,  // 10: carbon.frontend.status_bar.StatusBarMessage.translated_status_message:type_name -> carbon.frontend.status_bar.TranslatedStatusMessage
	4,  // 11: carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry.value:type_name -> carbon.frontend.status_bar.ServerStatus
	12, // 12: carbon.frontend.status_bar.StatusBarService.GetNextStatus:input_type -> carbon.frontend.util.Timestamp
	8,  // 13: carbon.frontend.status_bar.StatusBarService.ReportIssue:input_type -> carbon.frontend.status_bar.ReportIssueRequest
	13, // 14: carbon.frontend.status_bar.StatusBarService.GetSupportPhone:input_type -> carbon.frontend.util.Empty
	7,  // 15: carbon.frontend.status_bar.StatusBarService.GetNextStatus:output_type -> carbon.frontend.status_bar.StatusBarMessage
	13, // 16: carbon.frontend.status_bar.StatusBarService.ReportIssue:output_type -> carbon.frontend.util.Empty
	9,  // 17: carbon.frontend.status_bar.StatusBarService.GetSupportPhone:output_type -> carbon.frontend.status_bar.SupportPhoneMessage
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_frontend_proto_status_bar_proto_init() }
func file_frontend_proto_status_bar_proto_init() {
	if File_frontend_proto_status_bar_proto != nil {
		return
	}
	file_frontend_proto_translation_proto_init()
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_status_bar_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslatedStatusMessageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslatedStatusMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusBarMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportIssueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_status_bar_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupportPhoneMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_proto_status_bar_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*TranslatedStatusMessageDetails_DetailsStringKey)(nil),
		(*TranslatedStatusMessageDetails_Timer)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_status_bar_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_status_bar_proto_goTypes,
		DependencyIndexes: file_frontend_proto_status_bar_proto_depIdxs,
		EnumInfos:         file_frontend_proto_status_bar_proto_enumTypes,
		MessageInfos:      file_frontend_proto_status_bar_proto_msgTypes,
	}.Build()
	File_frontend_proto_status_bar_proto = out.File
	file_frontend_proto_status_bar_proto_rawDesc = nil
	file_frontend_proto_status_bar_proto_goTypes = nil
	file_frontend_proto_status_bar_proto_depIdxs = nil
}

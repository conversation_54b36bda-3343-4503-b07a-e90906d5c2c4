// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/thinning.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ThinningService_GetNextConfigurations_FullMethodName = "/carbon.frontend.thinning.ThinningService/GetNextConfigurations"
	ThinningService_GetNextActiveConf_FullMethodName     = "/carbon.frontend.thinning.ThinningService/GetNextActiveConf"
	ThinningService_DefineConfiguration_FullMethodName   = "/carbon.frontend.thinning.ThinningService/DefineConfiguration"
	ThinningService_SetActiveConfig_FullMethodName       = "/carbon.frontend.thinning.ThinningService/SetActiveConfig"
	ThinningService_DeleteConfig_FullMethodName          = "/carbon.frontend.thinning.ThinningService/DeleteConfig"
)

// ThinningServiceClient is the client API for ThinningService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ThinningServiceClient interface {
	GetNextConfigurations(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextConfigurationsResponse, error)
	GetNextActiveConf(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextActiveConfResponse, error)
	DefineConfiguration(ctx context.Context, in *DefineConfigurationRequest, opts ...grpc.CallOption) (*DefineConfigurationResponse, error)
	SetActiveConfig(ctx context.Context, in *SetActiveConfigRequest, opts ...grpc.CallOption) (*SetActiveConfigResponse, error)
	DeleteConfig(ctx context.Context, in *DeleteConfigRequest, opts ...grpc.CallOption) (*DeleteConfigResponse, error)
}

type thinningServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewThinningServiceClient(cc grpc.ClientConnInterface) ThinningServiceClient {
	return &thinningServiceClient{cc}
}

func (c *thinningServiceClient) GetNextConfigurations(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextConfigurationsResponse, error) {
	out := new(GetNextConfigurationsResponse)
	err := c.cc.Invoke(ctx, ThinningService_GetNextConfigurations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thinningServiceClient) GetNextActiveConf(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextActiveConfResponse, error) {
	out := new(GetNextActiveConfResponse)
	err := c.cc.Invoke(ctx, ThinningService_GetNextActiveConf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thinningServiceClient) DefineConfiguration(ctx context.Context, in *DefineConfigurationRequest, opts ...grpc.CallOption) (*DefineConfigurationResponse, error) {
	out := new(DefineConfigurationResponse)
	err := c.cc.Invoke(ctx, ThinningService_DefineConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thinningServiceClient) SetActiveConfig(ctx context.Context, in *SetActiveConfigRequest, opts ...grpc.CallOption) (*SetActiveConfigResponse, error) {
	out := new(SetActiveConfigResponse)
	err := c.cc.Invoke(ctx, ThinningService_SetActiveConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thinningServiceClient) DeleteConfig(ctx context.Context, in *DeleteConfigRequest, opts ...grpc.CallOption) (*DeleteConfigResponse, error) {
	out := new(DeleteConfigResponse)
	err := c.cc.Invoke(ctx, ThinningService_DeleteConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ThinningServiceServer is the server API for ThinningService service.
// All implementations must embed UnimplementedThinningServiceServer
// for forward compatibility
type ThinningServiceServer interface {
	GetNextConfigurations(context.Context, *Timestamp) (*GetNextConfigurationsResponse, error)
	GetNextActiveConf(context.Context, *Timestamp) (*GetNextActiveConfResponse, error)
	DefineConfiguration(context.Context, *DefineConfigurationRequest) (*DefineConfigurationResponse, error)
	SetActiveConfig(context.Context, *SetActiveConfigRequest) (*SetActiveConfigResponse, error)
	DeleteConfig(context.Context, *DeleteConfigRequest) (*DeleteConfigResponse, error)
	mustEmbedUnimplementedThinningServiceServer()
}

// UnimplementedThinningServiceServer must be embedded to have forward compatible implementations.
type UnimplementedThinningServiceServer struct {
}

func (UnimplementedThinningServiceServer) GetNextConfigurations(context.Context, *Timestamp) (*GetNextConfigurationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextConfigurations not implemented")
}
func (UnimplementedThinningServiceServer) GetNextActiveConf(context.Context, *Timestamp) (*GetNextActiveConfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActiveConf not implemented")
}
func (UnimplementedThinningServiceServer) DefineConfiguration(context.Context, *DefineConfigurationRequest) (*DefineConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DefineConfiguration not implemented")
}
func (UnimplementedThinningServiceServer) SetActiveConfig(context.Context, *SetActiveConfigRequest) (*SetActiveConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetActiveConfig not implemented")
}
func (UnimplementedThinningServiceServer) DeleteConfig(context.Context, *DeleteConfigRequest) (*DeleteConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteConfig not implemented")
}
func (UnimplementedThinningServiceServer) mustEmbedUnimplementedThinningServiceServer() {}

// UnsafeThinningServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ThinningServiceServer will
// result in compilation errors.
type UnsafeThinningServiceServer interface {
	mustEmbedUnimplementedThinningServiceServer()
}

func RegisterThinningServiceServer(s grpc.ServiceRegistrar, srv ThinningServiceServer) {
	s.RegisterService(&ThinningService_ServiceDesc, srv)
}

func _ThinningService_GetNextConfigurations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThinningServiceServer).GetNextConfigurations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThinningService_GetNextConfigurations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThinningServiceServer).GetNextConfigurations(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThinningService_GetNextActiveConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThinningServiceServer).GetNextActiveConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThinningService_GetNextActiveConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThinningServiceServer).GetNextActiveConf(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThinningService_DefineConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DefineConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThinningServiceServer).DefineConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThinningService_DefineConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThinningServiceServer).DefineConfiguration(ctx, req.(*DefineConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThinningService_SetActiveConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActiveConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThinningServiceServer).SetActiveConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThinningService_SetActiveConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThinningServiceServer).SetActiveConfig(ctx, req.(*SetActiveConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThinningService_DeleteConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThinningServiceServer).DeleteConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThinningService_DeleteConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThinningServiceServer).DeleteConfig(ctx, req.(*DeleteConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ThinningService_ServiceDesc is the grpc.ServiceDesc for ThinningService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ThinningService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.thinning.ThinningService",
	HandlerType: (*ThinningServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextConfigurations",
			Handler:    _ThinningService_GetNextConfigurations_Handler,
		},
		{
			MethodName: "GetNextActiveConf",
			Handler:    _ThinningService_GetNextActiveConf_Handler,
		},
		{
			MethodName: "DefineConfiguration",
			Handler:    _ThinningService_DefineConfiguration_Handler,
		},
		{
			MethodName: "SetActiveConfig",
			Handler:    _ThinningService_SetActiveConfig_Handler,
		},
		{
			MethodName: "DeleteConfig",
			Handler:    _ThinningService_DeleteConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/thinning.proto",
}

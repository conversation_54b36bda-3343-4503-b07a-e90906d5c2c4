// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/features.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Generation int32

const (
	Generation_Undefined Generation = 0
	Generation_Slayer    Generation = 1
	Generation_Reaper    Generation = 2
)

// Enum value maps for Generation.
var (
	Generation_name = map[int32]string{
		0: "Undefined",
		1: "Slayer",
		2: "Reaper",
	}
	Generation_value = map[string]int32{
		"Undefined": 0,
		"Slayer":    1,
		"Reaper":    2,
	}
)

func (x Generation) Enum() *Generation {
	p := new(Generation)
	*p = x
	return p
}

func (x Generation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Generation) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_features_proto_enumTypes[0].Descriptor()
}

func (Generation) Type() protoreflect.EnumType {
	return &file_frontend_proto_features_proto_enumTypes[0]
}

func (x Generation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Generation.Descriptor instead.
func (Generation) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_features_proto_rawDescGZIP(), []int{0}
}

type RowConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumPredicts int32 `protobuf:"varint,1,opt,name=num_predicts,json=numPredicts,proto3" json:"num_predicts,omitempty"`
	NumTargets  int32 `protobuf:"varint,2,opt,name=num_targets,json=numTargets,proto3" json:"num_targets,omitempty"`
}

func (x *RowConfiguration) Reset() {
	*x = RowConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_features_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowConfiguration) ProtoMessage() {}

func (x *RowConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_features_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowConfiguration.ProtoReflect.Descriptor instead.
func (*RowConfiguration) Descriptor() ([]byte, []int) {
	return file_frontend_proto_features_proto_rawDescGZIP(), []int{0}
}

func (x *RowConfiguration) GetNumPredicts() int32 {
	if x != nil {
		return x.NumPredicts
	}
	return 0
}

func (x *RowConfiguration) GetNumTargets() int32 {
	if x != nil {
		return x.NumTargets
	}
	return 0
}

type RobotConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumRows          int32                       `protobuf:"varint,1,opt,name=num_rows,json=numRows,proto3" json:"num_rows,omitempty"`
	RowConfiguration map[int32]*RowConfiguration `protobuf:"bytes,2,rep,name=row_configuration,json=rowConfiguration,proto3" json:"row_configuration,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Generation       Generation                  `protobuf:"varint,3,opt,name=generation,proto3,enum=carbon.frontend.features.Generation" json:"generation,omitempty"`
}

func (x *RobotConfiguration) Reset() {
	*x = RobotConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_features_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotConfiguration) ProtoMessage() {}

func (x *RobotConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_features_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotConfiguration.ProtoReflect.Descriptor instead.
func (*RobotConfiguration) Descriptor() ([]byte, []int) {
	return file_frontend_proto_features_proto_rawDescGZIP(), []int{1}
}

func (x *RobotConfiguration) GetNumRows() int32 {
	if x != nil {
		return x.NumRows
	}
	return 0
}

func (x *RobotConfiguration) GetRowConfiguration() map[int32]*RowConfiguration {
	if x != nil {
		return x.RowConfiguration
	}
	return nil
}

func (x *RobotConfiguration) GetGeneration() Generation {
	if x != nil {
		return x.Generation
	}
	return Generation_Undefined
}

var File_frontend_proto_features_proto protoreflect.FileDescriptor

var file_frontend_proto_features_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x56, 0x0a, 0x10, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x6e, 0x75, 0x6d, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e,
	0x75, 0x6d, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0xd7, 0x02, 0x0a,
	0x12, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x52, 0x6f, 0x77, 0x73, 0x12, 0x6f,
	0x0a, 0x11, 0x72, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x72,
	0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x44, 0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x6f, 0x0a, 0x15, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x33, 0x0a, 0x0a, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x10, 0x02, 0x32, 0xd0, 0x01, 0x0a, 0x0e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5a,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x62, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10,
	0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_features_proto_rawDescOnce sync.Once
	file_frontend_proto_features_proto_rawDescData = file_frontend_proto_features_proto_rawDesc
)

func file_frontend_proto_features_proto_rawDescGZIP() []byte {
	file_frontend_proto_features_proto_rawDescOnce.Do(func() {
		file_frontend_proto_features_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_features_proto_rawDescData)
	})
	return file_frontend_proto_features_proto_rawDescData
}

var file_frontend_proto_features_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_frontend_proto_features_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_frontend_proto_features_proto_goTypes = []interface{}{
	(Generation)(0),            // 0: carbon.frontend.features.Generation
	(*RowConfiguration)(nil),   // 1: carbon.frontend.features.RowConfiguration
	(*RobotConfiguration)(nil), // 2: carbon.frontend.features.RobotConfiguration
	nil,                        // 3: carbon.frontend.features.RobotConfiguration.RowConfigurationEntry
	(*Timestamp)(nil),          // 4: carbon.frontend.util.Timestamp
	(*Empty)(nil),              // 5: carbon.frontend.util.Empty
	(*FeatureFlags)(nil),       // 6: carbon.frontend.util.FeatureFlags
}
var file_frontend_proto_features_proto_depIdxs = []int32{
	3, // 0: carbon.frontend.features.RobotConfiguration.row_configuration:type_name -> carbon.frontend.features.RobotConfiguration.RowConfigurationEntry
	0, // 1: carbon.frontend.features.RobotConfiguration.generation:type_name -> carbon.frontend.features.Generation
	1, // 2: carbon.frontend.features.RobotConfiguration.RowConfigurationEntry.value:type_name -> carbon.frontend.features.RowConfiguration
	4, // 3: carbon.frontend.features.FeatureService.GetNextFeatureFlags:input_type -> carbon.frontend.util.Timestamp
	5, // 4: carbon.frontend.features.FeatureService.GetRobotConfiguration:input_type -> carbon.frontend.util.Empty
	6, // 5: carbon.frontend.features.FeatureService.GetNextFeatureFlags:output_type -> carbon.frontend.util.FeatureFlags
	2, // 6: carbon.frontend.features.FeatureService.GetRobotConfiguration:output_type -> carbon.frontend.features.RobotConfiguration
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_frontend_proto_features_proto_init() }
func file_frontend_proto_features_proto_init() {
	if File_frontend_proto_features_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_features_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_features_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_features_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_features_proto_goTypes,
		DependencyIndexes: file_frontend_proto_features_proto_depIdxs,
		EnumInfos:         file_frontend_proto_features_proto_enumTypes,
		MessageInfos:      file_frontend_proto_features_proto_msgTypes,
	}.Build()
	File_frontend_proto_features_proto = out.File
	file_frontend_proto_features_proto_rawDesc = nil
	file_frontend_proto_features_proto_goTypes = nil
	file_frontend_proto_features_proto_depIdxs = nil
}

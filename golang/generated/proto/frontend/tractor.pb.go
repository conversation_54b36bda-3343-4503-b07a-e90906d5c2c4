// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/tractor.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TractorIfState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expected  bool `protobuf:"varint,1,opt,name=expected,proto3" json:"expected,omitempty"`
	Connected bool `protobuf:"varint,2,opt,name=connected,proto3" json:"connected,omitempty"`
}

func (x *TractorIfState) Reset() {
	*x = TractorIfState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_tractor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorIfState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorIfState) ProtoMessage() {}

func (x *TractorIfState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_tractor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorIfState.ProtoReflect.Descriptor instead.
func (*TractorIfState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_tractor_proto_rawDescGZIP(), []int{0}
}

func (x *TractorIfState) GetExpected() bool {
	if x != nil {
		return x.Expected
	}
	return false
}

func (x *TractorIfState) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

type TractorSafetyState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSafe   bool `protobuf:"varint,1,opt,name=is_safe,json=isSafe,proto3" json:"is_safe,omitempty"`
	Enforced bool `protobuf:"varint,2,opt,name=enforced,proto3" json:"enforced,omitempty"`
}

func (x *TractorSafetyState) Reset() {
	*x = TractorSafetyState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_tractor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorSafetyState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorSafetyState) ProtoMessage() {}

func (x *TractorSafetyState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_tractor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorSafetyState.ProtoReflect.Descriptor instead.
func (*TractorSafetyState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_tractor_proto_rawDescGZIP(), []int{1}
}

func (x *TractorSafetyState) GetIsSafe() bool {
	if x != nil {
		return x.IsSafe
	}
	return false
}

func (x *TractorSafetyState) GetEnforced() bool {
	if x != nil {
		return x.Enforced
	}
	return false
}

type GetNextTractorIfStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp      `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	State *TractorIfState `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GetNextTractorIfStateResponse) Reset() {
	*x = GetNextTractorIfStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_tractor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextTractorIfStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextTractorIfStateResponse) ProtoMessage() {}

func (x *GetNextTractorIfStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_tractor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextTractorIfStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextTractorIfStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_tractor_proto_rawDescGZIP(), []int{2}
}

func (x *GetNextTractorIfStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextTractorIfStateResponse) GetState() *TractorIfState {
	if x != nil {
		return x.State
	}
	return nil
}

type GetNextTractorSafetyStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp          `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	State *TractorSafetyState `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GetNextTractorSafetyStateResponse) Reset() {
	*x = GetNextTractorSafetyStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_tractor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextTractorSafetyStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextTractorSafetyStateResponse) ProtoMessage() {}

func (x *GetNextTractorSafetyStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_tractor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextTractorSafetyStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextTractorSafetyStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_tractor_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextTractorSafetyStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextTractorSafetyStateResponse) GetState() *TractorSafetyState {
	if x != nil {
		return x.State
	}
	return nil
}

type SetEnforcementPolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enforced bool `protobuf:"varint,1,opt,name=enforced,proto3" json:"enforced,omitempty"`
}

func (x *SetEnforcementPolicyRequest) Reset() {
	*x = SetEnforcementPolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_tractor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetEnforcementPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetEnforcementPolicyRequest) ProtoMessage() {}

func (x *SetEnforcementPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_tractor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetEnforcementPolicyRequest.ProtoReflect.Descriptor instead.
func (*SetEnforcementPolicyRequest) Descriptor() ([]byte, []int) {
	return file_frontend_proto_tractor_proto_rawDescGZIP(), []int{4}
}

func (x *SetEnforcementPolicyRequest) GetEnforced() bool {
	if x != nil {
		return x.Enforced
	}
	return false
}

type SetEnforcementPolicyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetEnforcementPolicyResponse) Reset() {
	*x = SetEnforcementPolicyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_tractor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetEnforcementPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetEnforcementPolicyResponse) ProtoMessage() {}

func (x *SetEnforcementPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_tractor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetEnforcementPolicyResponse.ProtoReflect.Descriptor instead.
func (*SetEnforcementPolicyResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_tractor_proto_rawDescGZIP(), []int{5}
}

var File_frontend_proto_tractor_proto protoreflect.FileDescriptor

var file_frontend_proto_tractor_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x66, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x49,
	0x0a, 0x12, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x66, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x53, 0x61, 0x66, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x66, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x66, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x21,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x61,
	0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02,
	0x74, 0x73, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x39, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64,
	0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x32, 0x82, 0x03, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x70, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x36, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x83, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_tractor_proto_rawDescOnce sync.Once
	file_frontend_proto_tractor_proto_rawDescData = file_frontend_proto_tractor_proto_rawDesc
)

func file_frontend_proto_tractor_proto_rawDescGZIP() []byte {
	file_frontend_proto_tractor_proto_rawDescOnce.Do(func() {
		file_frontend_proto_tractor_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_tractor_proto_rawDescData)
	})
	return file_frontend_proto_tractor_proto_rawDescData
}

var file_frontend_proto_tractor_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_frontend_proto_tractor_proto_goTypes = []interface{}{
	(*TractorIfState)(nil),                    // 0: carbon.frontend.tractor.TractorIfState
	(*TractorSafetyState)(nil),                // 1: carbon.frontend.tractor.TractorSafetyState
	(*GetNextTractorIfStateResponse)(nil),     // 2: carbon.frontend.tractor.GetNextTractorIfStateResponse
	(*GetNextTractorSafetyStateResponse)(nil), // 3: carbon.frontend.tractor.GetNextTractorSafetyStateResponse
	(*SetEnforcementPolicyRequest)(nil),       // 4: carbon.frontend.tractor.SetEnforcementPolicyRequest
	(*SetEnforcementPolicyResponse)(nil),      // 5: carbon.frontend.tractor.SetEnforcementPolicyResponse
	(*Timestamp)(nil),                         // 6: carbon.frontend.util.Timestamp
}
var file_frontend_proto_tractor_proto_depIdxs = []int32{
	6, // 0: carbon.frontend.tractor.GetNextTractorIfStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	0, // 1: carbon.frontend.tractor.GetNextTractorIfStateResponse.state:type_name -> carbon.frontend.tractor.TractorIfState
	6, // 2: carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	1, // 3: carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state:type_name -> carbon.frontend.tractor.TractorSafetyState
	6, // 4: carbon.frontend.tractor.TractorService.GetNextTractorIfState:input_type -> carbon.frontend.util.Timestamp
	6, // 5: carbon.frontend.tractor.TractorService.GetNextTractorSafetyState:input_type -> carbon.frontend.util.Timestamp
	4, // 6: carbon.frontend.tractor.TractorService.SetEnforcementPolicy:input_type -> carbon.frontend.tractor.SetEnforcementPolicyRequest
	2, // 7: carbon.frontend.tractor.TractorService.GetNextTractorIfState:output_type -> carbon.frontend.tractor.GetNextTractorIfStateResponse
	3, // 8: carbon.frontend.tractor.TractorService.GetNextTractorSafetyState:output_type -> carbon.frontend.tractor.GetNextTractorSafetyStateResponse
	5, // 9: carbon.frontend.tractor.TractorService.SetEnforcementPolicy:output_type -> carbon.frontend.tractor.SetEnforcementPolicyResponse
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_frontend_proto_tractor_proto_init() }
func file_frontend_proto_tractor_proto_init() {
	if File_frontend_proto_tractor_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_tractor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorIfState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_tractor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorSafetyState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_tractor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextTractorIfStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_tractor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextTractorSafetyStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_tractor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetEnforcementPolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_tractor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetEnforcementPolicyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_tractor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_tractor_proto_goTypes,
		DependencyIndexes: file_frontend_proto_tractor_proto_depIdxs,
		MessageInfos:      file_frontend_proto_tractor_proto_msgTypes,
	}.Build()
	File_frontend_proto_tractor_proto = out.File
	file_frontend_proto_tractor_proto_rawDesc = nil
	file_frontend_proto_tractor_proto_goTypes = nil
	file_frontend_proto_tractor_proto_depIdxs = nil
}

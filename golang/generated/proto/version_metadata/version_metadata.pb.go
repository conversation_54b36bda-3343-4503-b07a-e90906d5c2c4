// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/version_metadata/version_metadata.proto

package version_metadata

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VersionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Containers    []string `protobuf:"bytes,1,rep,name=containers,proto3" json:"containers,omitempty"`
	SystemVersion string   `protobuf:"bytes,2,opt,name=systemVersion,proto3" json:"systemVersion,omitempty"`
}

func (x *VersionMetadata) Reset() {
	*x = VersionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionMetadata) ProtoMessage() {}

func (x *VersionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionMetadata.ProtoReflect.Descriptor instead.
func (*VersionMetadata) Descriptor() ([]byte, []int) {
	return file_proto_version_metadata_version_metadata_proto_rawDescGZIP(), []int{0}
}

func (x *VersionMetadata) GetContainers() []string {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *VersionMetadata) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

type GetVersionMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Gen     string `protobuf:"bytes,2,opt,name=gen,proto3" json:"gen,omitempty"`
}

func (x *GetVersionMetadataRequest) Reset() {
	*x = GetVersionMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVersionMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVersionMetadataRequest) ProtoMessage() {}

func (x *GetVersionMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVersionMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetVersionMetadataRequest) Descriptor() ([]byte, []int) {
	return file_proto_version_metadata_version_metadata_proto_rawDescGZIP(), []int{1}
}

func (x *GetVersionMetadataRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetVersionMetadataRequest) GetGen() string {
	if x != nil {
		return x.Gen
	}
	return ""
}

type UploadVersionMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version         string           `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Gen             string           `protobuf:"bytes,2,opt,name=gen,proto3" json:"gen,omitempty"`
	VersionMetadata *VersionMetadata `protobuf:"bytes,3,opt,name=versionMetadata,proto3" json:"versionMetadata,omitempty"`
}

func (x *UploadVersionMetadataRequest) Reset() {
	*x = UploadVersionMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadVersionMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadVersionMetadataRequest) ProtoMessage() {}

func (x *UploadVersionMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadVersionMetadataRequest.ProtoReflect.Descriptor instead.
func (*UploadVersionMetadataRequest) Descriptor() ([]byte, []int) {
	return file_proto_version_metadata_version_metadata_proto_rawDescGZIP(), []int{2}
}

func (x *UploadVersionMetadataRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *UploadVersionMetadataRequest) GetGen() string {
	if x != nil {
		return x.Gen
	}
	return ""
}

func (x *UploadVersionMetadataRequest) GetVersionMetadata() *VersionMetadata {
	if x != nil {
		return x.VersionMetadata
	}
	return nil
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_version_metadata_version_metadata_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_version_metadata_version_metadata_proto_rawDescGZIP(), []int{3}
}

var File_proto_version_metadata_version_metadata_proto protoreflect.FileDescriptor

var file_proto_version_metadata_version_metadata_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x17, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x0f, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x47, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x67, 0x65, 0x6e, 0x22, 0x9e, 0x01, 0x0a, 0x1c, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x67, 0x65, 0x6e, 0x12, 0x52, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x07, 0x0a, 0x05, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x32, 0xfc, 0x01, 0x0a, 0x16, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x72, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x6e, 0x0a, 0x15, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x35, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x42, 0x18, 0x5a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_version_metadata_version_metadata_proto_rawDescOnce sync.Once
	file_proto_version_metadata_version_metadata_proto_rawDescData = file_proto_version_metadata_version_metadata_proto_rawDesc
)

func file_proto_version_metadata_version_metadata_proto_rawDescGZIP() []byte {
	file_proto_version_metadata_version_metadata_proto_rawDescOnce.Do(func() {
		file_proto_version_metadata_version_metadata_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_version_metadata_version_metadata_proto_rawDescData)
	})
	return file_proto_version_metadata_version_metadata_proto_rawDescData
}

var file_proto_version_metadata_version_metadata_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_version_metadata_version_metadata_proto_goTypes = []interface{}{
	(*VersionMetadata)(nil),              // 0: carbon.version_metadata.VersionMetadata
	(*GetVersionMetadataRequest)(nil),    // 1: carbon.version_metadata.GetVersionMetadataRequest
	(*UploadVersionMetadataRequest)(nil), // 2: carbon.version_metadata.UploadVersionMetadataRequest
	(*Empty)(nil),                        // 3: carbon.version_metadata.Empty
}
var file_proto_version_metadata_version_metadata_proto_depIdxs = []int32{
	0, // 0: carbon.version_metadata.UploadVersionMetadataRequest.versionMetadata:type_name -> carbon.version_metadata.VersionMetadata
	1, // 1: carbon.version_metadata.VersionMetadataService.GetVersionMetadata:input_type -> carbon.version_metadata.GetVersionMetadataRequest
	2, // 2: carbon.version_metadata.VersionMetadataService.UploadVersionMetadata:input_type -> carbon.version_metadata.UploadVersionMetadataRequest
	0, // 3: carbon.version_metadata.VersionMetadataService.GetVersionMetadata:output_type -> carbon.version_metadata.VersionMetadata
	3, // 4: carbon.version_metadata.VersionMetadataService.UploadVersionMetadata:output_type -> carbon.version_metadata.Empty
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_version_metadata_version_metadata_proto_init() }
func file_proto_version_metadata_version_metadata_proto_init() {
	if File_proto_version_metadata_version_metadata_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_version_metadata_version_metadata_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_version_metadata_version_metadata_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVersionMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_version_metadata_version_metadata_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadVersionMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_version_metadata_version_metadata_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_version_metadata_version_metadata_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_version_metadata_version_metadata_proto_goTypes,
		DependencyIndexes: file_proto_version_metadata_version_metadata_proto_depIdxs,
		MessageInfos:      file_proto_version_metadata_version_metadata_proto_msgTypes,
	}.Build()
	File_proto_version_metadata_version_metadata_proto = out.File
	file_proto_version_metadata_version_metadata_proto_rawDesc = nil
	file_proto_version_metadata_version_metadata_proto_goTypes = nil
	file_proto_version_metadata_version_metadata_proto_depIdxs = nil
}

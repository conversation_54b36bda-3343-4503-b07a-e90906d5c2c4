// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/version_metadata/version_metadata.proto

package version_metadata

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	VersionMetadataService_GetVersionMetadata_FullMethodName    = "/carbon.version_metadata.VersionMetadataService/GetVersionMetadata"
	VersionMetadataService_UploadVersionMetadata_FullMethodName = "/carbon.version_metadata.VersionMetadataService/UploadVersionMetadata"
)

// VersionMetadataServiceClient is the client API for VersionMetadataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VersionMetadataServiceClient interface {
	GetVersionMetadata(ctx context.Context, in *GetVersionMetadataRequest, opts ...grpc.CallOption) (*VersionMetadata, error)
	UploadVersionMetadata(ctx context.Context, in *UploadVersionMetadataRequest, opts ...grpc.CallOption) (*Empty, error)
}

type versionMetadataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVersionMetadataServiceClient(cc grpc.ClientConnInterface) VersionMetadataServiceClient {
	return &versionMetadataServiceClient{cc}
}

func (c *versionMetadataServiceClient) GetVersionMetadata(ctx context.Context, in *GetVersionMetadataRequest, opts ...grpc.CallOption) (*VersionMetadata, error) {
	out := new(VersionMetadata)
	err := c.cc.Invoke(ctx, VersionMetadataService_GetVersionMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *versionMetadataServiceClient) UploadVersionMetadata(ctx context.Context, in *UploadVersionMetadataRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, VersionMetadataService_UploadVersionMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VersionMetadataServiceServer is the server API for VersionMetadataService service.
// All implementations must embed UnimplementedVersionMetadataServiceServer
// for forward compatibility
type VersionMetadataServiceServer interface {
	GetVersionMetadata(context.Context, *GetVersionMetadataRequest) (*VersionMetadata, error)
	UploadVersionMetadata(context.Context, *UploadVersionMetadataRequest) (*Empty, error)
	mustEmbedUnimplementedVersionMetadataServiceServer()
}

// UnimplementedVersionMetadataServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVersionMetadataServiceServer struct {
}

func (UnimplementedVersionMetadataServiceServer) GetVersionMetadata(context.Context, *GetVersionMetadataRequest) (*VersionMetadata, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersionMetadata not implemented")
}
func (UnimplementedVersionMetadataServiceServer) UploadVersionMetadata(context.Context, *UploadVersionMetadataRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadVersionMetadata not implemented")
}
func (UnimplementedVersionMetadataServiceServer) mustEmbedUnimplementedVersionMetadataServiceServer() {
}

// UnsafeVersionMetadataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VersionMetadataServiceServer will
// result in compilation errors.
type UnsafeVersionMetadataServiceServer interface {
	mustEmbedUnimplementedVersionMetadataServiceServer()
}

func RegisterVersionMetadataServiceServer(s grpc.ServiceRegistrar, srv VersionMetadataServiceServer) {
	s.RegisterService(&VersionMetadataService_ServiceDesc, srv)
}

func _VersionMetadataService_GetVersionMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VersionMetadataServiceServer).GetVersionMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VersionMetadataService_GetVersionMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VersionMetadataServiceServer).GetVersionMetadata(ctx, req.(*GetVersionMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VersionMetadataService_UploadVersionMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadVersionMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VersionMetadataServiceServer).UploadVersionMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VersionMetadataService_UploadVersionMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VersionMetadataServiceServer).UploadVersionMetadata(ctx, req.(*UploadVersionMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VersionMetadataService_ServiceDesc is the grpc.ServiceDesc for VersionMetadataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VersionMetadataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.version_metadata.VersionMetadataService",
	HandlerType: (*VersionMetadataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVersionMetadata",
			Handler:    _VersionMetadataService_GetVersionMetadata_Handler,
		},
		{
			MethodName: "UploadVersionMetadata",
			Handler:    _VersionMetadataService_UploadVersionMetadata_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/version_metadata/version_metadata.proto",
}

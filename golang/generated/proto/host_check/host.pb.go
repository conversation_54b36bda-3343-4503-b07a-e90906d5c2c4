// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: golang/host_check/proto/host.proto

package host_check

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HostStatusState int32

const (
	HostStatusState_OK       HostStatusState = 0
	HostStatusState_DEGRADED HostStatusState = 1
	HostStatusState_FAILED   HostStatusState = 2
)

// Enum value maps for HostStatusState.
var (
	HostStatusState_name = map[int32]string{
		0: "OK",
		1: "DEGRADED",
		2: "FAILED",
	}
	HostStatusState_value = map[string]int32{
		"OK":       0,
		"DEGRADED": 1,
		"FAILED":   2,
	}
)

func (x HostStatusState) Enum() *HostStatusState {
	p := new(HostStatusState)
	*p = x
	return p
}

func (x HostStatusState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostStatusState) Descriptor() protoreflect.EnumDescriptor {
	return file_golang_host_check_proto_host_proto_enumTypes[0].Descriptor()
}

func (HostStatusState) Type() protoreflect.EnumType {
	return &file_golang_host_check_proto_host_proto_enumTypes[0]
}

func (x HostStatusState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostStatusState.Descriptor instead.
func (HostStatusState) EnumDescriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{0}
}

type AlarmCode int32

const (
	AlarmCode_A_NOALARM     AlarmCode = 0
	AlarmCode_A_GPUFAIL     AlarmCode = 1
	AlarmCode_A_GPUOVERTEMP AlarmCode = 2
	// Deprecated: Marked as deprecated in golang/host_check/proto/host.proto.
	AlarmCode_A_DISKSPACE            AlarmCode = 3
	AlarmCode_A_PTPCLOCK             AlarmCode = 4
	AlarmCode_A_NETWORK              AlarmCode = 5
	AlarmCode_A_DISKSPACE_WARN       AlarmCode = 6
	AlarmCode_A_DISKSPACE_ERROR      AlarmCode = 7
	AlarmCode_A_GPU_CHECKER_FAIL     AlarmCode = 8
	AlarmCode_A_NETWORK_CHECKER_FAIL AlarmCode = 9
)

// Enum value maps for AlarmCode.
var (
	AlarmCode_name = map[int32]string{
		0: "A_NOALARM",
		1: "A_GPUFAIL",
		2: "A_GPUOVERTEMP",
		3: "A_DISKSPACE",
		4: "A_PTPCLOCK",
		5: "A_NETWORK",
		6: "A_DISKSPACE_WARN",
		7: "A_DISKSPACE_ERROR",
		8: "A_GPU_CHECKER_FAIL",
		9: "A_NETWORK_CHECKER_FAIL",
	}
	AlarmCode_value = map[string]int32{
		"A_NOALARM":              0,
		"A_GPUFAIL":              1,
		"A_GPUOVERTEMP":          2,
		"A_DISKSPACE":            3,
		"A_PTPCLOCK":             4,
		"A_NETWORK":              5,
		"A_DISKSPACE_WARN":       6,
		"A_DISKSPACE_ERROR":      7,
		"A_GPU_CHECKER_FAIL":     8,
		"A_NETWORK_CHECKER_FAIL": 9,
	}
)

func (x AlarmCode) Enum() *AlarmCode {
	p := new(AlarmCode)
	*p = x
	return p
}

func (x AlarmCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmCode) Descriptor() protoreflect.EnumDescriptor {
	return file_golang_host_check_proto_host_proto_enumTypes[1].Descriptor()
}

func (AlarmCode) Type() protoreflect.EnumType {
	return &file_golang_host_check_proto_host_proto_enumTypes[1]
}

func (x AlarmCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmCode.Descriptor instead.
func (AlarmCode) EnumDescriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{1}
}

type AlarmImpact int32

const (
	AlarmImpact_AI_None     AlarmImpact = 0
	AlarmImpact_AI_Degraded AlarmImpact = 1
	AlarmImpact_AI_Offline  AlarmImpact = 2
	AlarmImpact_AI_Critical AlarmImpact = 3
)

// Enum value maps for AlarmImpact.
var (
	AlarmImpact_name = map[int32]string{
		0: "AI_None",
		1: "AI_Degraded",
		2: "AI_Offline",
		3: "AI_Critical",
	}
	AlarmImpact_value = map[string]int32{
		"AI_None":     0,
		"AI_Degraded": 1,
		"AI_Offline":  2,
		"AI_Critical": 3,
	}
)

func (x AlarmImpact) Enum() *AlarmImpact {
	p := new(AlarmImpact)
	*p = x
	return p
}

func (x AlarmImpact) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmImpact) Descriptor() protoreflect.EnumDescriptor {
	return file_golang_host_check_proto_host_proto_enumTypes[2].Descriptor()
}

func (AlarmImpact) Type() protoreflect.EnumType {
	return &file_golang_host_check_proto_host_proto_enumTypes[2]
}

func (x AlarmImpact) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmImpact.Descriptor instead.
func (AlarmImpact) EnumDescriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{2}
}

type AlarmLevel int32

const (
	AlarmLevel_AL_Unknown  AlarmLevel = 0
	AlarmLevel_AL_Critical AlarmLevel = 1
	AlarmLevel_AL_High     AlarmLevel = 2
	AlarmLevel_AL_Medium   AlarmLevel = 3
	AlarmLevel_AL_Low      AlarmLevel = 4
	AlarmLevel_AL_Hidden   AlarmLevel = 5
)

// Enum value maps for AlarmLevel.
var (
	AlarmLevel_name = map[int32]string{
		0: "AL_Unknown",
		1: "AL_Critical",
		2: "AL_High",
		3: "AL_Medium",
		4: "AL_Low",
		5: "AL_Hidden",
	}
	AlarmLevel_value = map[string]int32{
		"AL_Unknown":  0,
		"AL_Critical": 1,
		"AL_High":     2,
		"AL_Medium":   3,
		"AL_Low":      4,
		"AL_Hidden":   5,
	}
)

func (x AlarmLevel) Enum() *AlarmLevel {
	p := new(AlarmLevel)
	*p = x
	return p
}

func (x AlarmLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_golang_host_check_proto_host_proto_enumTypes[3].Descriptor()
}

func (AlarmLevel) Type() protoreflect.EnumType {
	return &file_golang_host_check_proto_host_proto_enumTypes[3]
}

func (x AlarmLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmLevel.Descriptor instead.
func (AlarmLevel) EnumDescriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{3}
}

type HostStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HostStatusRequest) Reset() {
	*x = HostStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_host_check_proto_host_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostStatusRequest) ProtoMessage() {}

func (x *HostStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_host_check_proto_host_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostStatusRequest.ProtoReflect.Descriptor instead.
func (*HostStatusRequest) Descriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{0}
}

type Alarm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                  AlarmCode                        `protobuf:"varint,1,opt,name=code,proto3,enum=carbon.host_check.host.AlarmCode" json:"code,omitempty"`
	Subsystem             string                           `protobuf:"bytes,2,opt,name=subsystem,proto3" json:"subsystem,omitempty"`
	Description           string                           `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Level                 AlarmLevel                       `protobuf:"varint,4,opt,name=level,proto3,enum=carbon.host_check.host.AlarmLevel" json:"level,omitempty"`
	Impact                AlarmImpact                      `protobuf:"varint,5,opt,name=impact,proto3,enum=carbon.host_check.host.AlarmImpact" json:"impact,omitempty"`
	Param                 int32                            `protobuf:"varint,6,opt,name=param,proto3" json:"param,omitempty"`
	TranslationParameters []*frontend.TranslationParameter `protobuf:"bytes,7,rep,name=translation_parameters,json=translationParameters,proto3" json:"translation_parameters,omitempty"`
}

func (x *Alarm) Reset() {
	*x = Alarm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_host_check_proto_host_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Alarm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Alarm) ProtoMessage() {}

func (x *Alarm) ProtoReflect() protoreflect.Message {
	mi := &file_golang_host_check_proto_host_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Alarm.ProtoReflect.Descriptor instead.
func (*Alarm) Descriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{1}
}

func (x *Alarm) GetCode() AlarmCode {
	if x != nil {
		return x.Code
	}
	return AlarmCode_A_NOALARM
}

func (x *Alarm) GetSubsystem() string {
	if x != nil {
		return x.Subsystem
	}
	return ""
}

func (x *Alarm) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Alarm) GetLevel() AlarmLevel {
	if x != nil {
		return x.Level
	}
	return AlarmLevel_AL_Unknown
}

func (x *Alarm) GetImpact() AlarmImpact {
	if x != nil {
		return x.Impact
	}
	return AlarmImpact_AI_None
}

func (x *Alarm) GetParam() int32 {
	if x != nil {
		return x.Param
	}
	return 0
}

func (x *Alarm) GetTranslationParameters() []*frontend.TranslationParameter {
	if x != nil {
		return x.TranslationParameters
	}
	return nil
}

type SysInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hostname string `protobuf:"bytes,1,opt,name=hostname,proto3" json:"hostname,omitempty"`
	Serial   string `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
}

func (x *SysInfo) Reset() {
	*x = SysInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_host_check_proto_host_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SysInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SysInfo) ProtoMessage() {}

func (x *SysInfo) ProtoReflect() protoreflect.Message {
	mi := &file_golang_host_check_proto_host_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SysInfo.ProtoReflect.Descriptor instead.
func (*SysInfo) Descriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{2}
}

func (x *SysInfo) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *SysInfo) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

type HostStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     HostStatusState `protobuf:"varint,1,opt,name=status,proto3,enum=carbon.host_check.host.HostStatusState" json:"status,omitempty"`
	Alarms     []*Alarm        `protobuf:"bytes,2,rep,name=alarms,proto3" json:"alarms,omitempty"`
	SystemInfo *SysInfo        `protobuf:"bytes,3,opt,name=system_info,json=systemInfo,proto3" json:"system_info,omitempty"`
}

func (x *HostStatus) Reset() {
	*x = HostStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_host_check_proto_host_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostStatus) ProtoMessage() {}

func (x *HostStatus) ProtoReflect() protoreflect.Message {
	mi := &file_golang_host_check_proto_host_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostStatus.ProtoReflect.Descriptor instead.
func (*HostStatus) Descriptor() ([]byte, []int) {
	return file_golang_host_check_proto_host_proto_rawDescGZIP(), []int{3}
}

func (x *HostStatus) GetStatus() HostStatusState {
	if x != nil {
		return x.Status
	}
	return HostStatusState_OK
}

func (x *HostStatus) GetAlarms() []*Alarm {
	if x != nil {
		return x.Alarms
	}
	return nil
}

func (x *HostStatus) GetSystemInfo() *SysInfo {
	if x != nil {
		return x.SystemInfo
	}
	return nil
}

var File_golang_host_check_proto_host_proto protoreflect.FileDescriptor

var file_golang_host_check_proto_host_proto_rawDesc = []byte{
	0x0a, 0x22, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73,
	0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x1a, 0x20, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x13,
	0x0a, 0x11, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0xf5, 0x02, 0x0a, 0x05, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x12, 0x35, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e,
	0x68, 0x6f, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x62, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73,
	0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x3b,
	0x0a, 0x06, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x49, 0x6d, 0x70,
	0x61, 0x63, 0x74, 0x52, 0x06, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x68, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x52, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x3d, 0x0a, 0x07, 0x53,
	0x79, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0xc6, 0x01, 0x0a, 0x0a, 0x48,
	0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x68, 0x6f,
	0x73, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x68,
	0x6f, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d,
	0x73, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x2e,
	0x53, 0x79, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x2a, 0x33, 0x0a, 0x0f, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x45, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xd1, 0x01, 0x0a, 0x09, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x5f, 0x4e, 0x4f, 0x41, 0x4c,
	0x41, 0x52, 0x4d, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x5f, 0x47, 0x50, 0x55, 0x46, 0x41,
	0x49, 0x4c, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x5f, 0x47, 0x50, 0x55, 0x4f, 0x56, 0x45,
	0x52, 0x54, 0x45, 0x4d, 0x50, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0b, 0x41, 0x5f, 0x44, 0x49, 0x53,
	0x4b, 0x53, 0x50, 0x41, 0x43, 0x45, 0x10, 0x03, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x41, 0x5f, 0x50, 0x54, 0x50, 0x43, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09,
	0x41, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x41,
	0x5f, 0x44, 0x49, 0x53, 0x4b, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x10,
	0x06, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x53, 0x50, 0x41, 0x43, 0x45,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x5f, 0x47, 0x50,
	0x55, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x08,
	0x12, 0x1a, 0x0a, 0x16, 0x41, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x09, 0x2a, 0x4c, 0x0a, 0x0b,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x49, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x12, 0x0b, 0x0a, 0x07, 0x41,
	0x49, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x49, 0x5f, 0x44,
	0x65, 0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x49, 0x5f,
	0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x49, 0x5f,
	0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x10, 0x03, 0x2a, 0x64, 0x0a, 0x0a, 0x41, 0x6c,
	0x61, 0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4c, 0x5f, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x4c, 0x5f, 0x43,
	0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4c, 0x5f,
	0x48, 0x69, 0x67, 0x68, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x5f, 0x4d, 0x65, 0x64,
	0x69, 0x75, 0x6d, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4c, 0x5f, 0x4c, 0x6f, 0x77, 0x10,
	0x04, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x5f, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x05,
	0x32, 0x6d, 0x0a, 0x0b, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x5e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e,
	0x68, 0x6f, 0x73, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x12, 0x5a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_golang_host_check_proto_host_proto_rawDescOnce sync.Once
	file_golang_host_check_proto_host_proto_rawDescData = file_golang_host_check_proto_host_proto_rawDesc
)

func file_golang_host_check_proto_host_proto_rawDescGZIP() []byte {
	file_golang_host_check_proto_host_proto_rawDescOnce.Do(func() {
		file_golang_host_check_proto_host_proto_rawDescData = protoimpl.X.CompressGZIP(file_golang_host_check_proto_host_proto_rawDescData)
	})
	return file_golang_host_check_proto_host_proto_rawDescData
}

var file_golang_host_check_proto_host_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_golang_host_check_proto_host_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_golang_host_check_proto_host_proto_goTypes = []interface{}{
	(HostStatusState)(0),                  // 0: carbon.host_check.host.HostStatusState
	(AlarmCode)(0),                        // 1: carbon.host_check.host.AlarmCode
	(AlarmImpact)(0),                      // 2: carbon.host_check.host.AlarmImpact
	(AlarmLevel)(0),                       // 3: carbon.host_check.host.AlarmLevel
	(*HostStatusRequest)(nil),             // 4: carbon.host_check.host.HostStatusRequest
	(*Alarm)(nil),                         // 5: carbon.host_check.host.Alarm
	(*SysInfo)(nil),                       // 6: carbon.host_check.host.SysInfo
	(*HostStatus)(nil),                    // 7: carbon.host_check.host.HostStatus
	(*frontend.TranslationParameter)(nil), // 8: carbon.frontend.translation.TranslationParameter
}
var file_golang_host_check_proto_host_proto_depIdxs = []int32{
	1, // 0: carbon.host_check.host.Alarm.code:type_name -> carbon.host_check.host.AlarmCode
	3, // 1: carbon.host_check.host.Alarm.level:type_name -> carbon.host_check.host.AlarmLevel
	2, // 2: carbon.host_check.host.Alarm.impact:type_name -> carbon.host_check.host.AlarmImpact
	8, // 3: carbon.host_check.host.Alarm.translation_parameters:type_name -> carbon.frontend.translation.TranslationParameter
	0, // 4: carbon.host_check.host.HostStatus.status:type_name -> carbon.host_check.host.HostStatusState
	5, // 5: carbon.host_check.host.HostStatus.alarms:type_name -> carbon.host_check.host.Alarm
	6, // 6: carbon.host_check.host.HostStatus.system_info:type_name -> carbon.host_check.host.SysInfo
	4, // 7: carbon.host_check.host.HostService.GetHostStatus:input_type -> carbon.host_check.host.HostStatusRequest
	7, // 8: carbon.host_check.host.HostService.GetHostStatus:output_type -> carbon.host_check.host.HostStatus
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_golang_host_check_proto_host_proto_init() }
func file_golang_host_check_proto_host_proto_init() {
	if File_golang_host_check_proto_host_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_golang_host_check_proto_host_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_host_check_proto_host_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Alarm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_host_check_proto_host_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SysInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_host_check_proto_host_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_golang_host_check_proto_host_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_golang_host_check_proto_host_proto_goTypes,
		DependencyIndexes: file_golang_host_check_proto_host_proto_depIdxs,
		EnumInfos:         file_golang_host_check_proto_host_proto_enumTypes,
		MessageInfos:      file_golang_host_check_proto_host_proto_msgTypes,
	}.Build()
	File_golang_host_check_proto_host_proto = out.File
	file_golang_host_check_proto_host_proto_rawDesc = nil
	file_golang_host_check_proto_host_proto_goTypes = nil
	file_golang_host_check_proto_host_proto_depIdxs = nil
}

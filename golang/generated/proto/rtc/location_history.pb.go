// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/rtc/location_history.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	geo "github.com/carbonrobotics/robot/golang/generated/proto/geo"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RobotData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId uint64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 0 is not a valid task_id and can signify no task.
	// Indicates whether the robot was making progress on its task, using a
	// task-specific definition of "active" that is determined by the robot. For
	// example, for a laserweeding task, the state might be determined by whether
	// the hitch is lowered.
	Active bool `protobuf:"varint,2,opt,name=active,proto3" json:"active,omitempty"`
	// Objective ID is for tracking purposes and ease of querying contiguous
	// location data. 0 is not a valid objective_id and signifies no objective.
	ObjectiveId uint64 `protobuf:"varint,3,opt,name=objective_id,json=objectiveId,proto3" json:"objective_id,omitempty"`
}

func (x *RobotData) Reset() {
	*x = RobotData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotData) ProtoMessage() {}

func (x *RobotData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotData.ProtoReflect.Descriptor instead.
func (*RobotData) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{0}
}

func (x *RobotData) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *RobotData) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *RobotData) GetObjectiveId() uint64 {
	if x != nil {
		return x.ObjectiveId
	}
	return 0
}

type LocationHistoryRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Point *geo.Point `protobuf:"bytes,1,opt,name=point,proto3" json:"point,omitempty"`
	// Facing direction of the robot, if available from hardware.
	HeadingDegrees *float64               `protobuf:"fixed64,4,opt,name=heading_degrees,json=headingDegrees,proto3,oneof" json:"heading_degrees,omitempty"`
	Timestamp      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Data           *RobotData             `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *LocationHistoryRecord) Reset() {
	*x = LocationHistoryRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationHistoryRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationHistoryRecord) ProtoMessage() {}

func (x *LocationHistoryRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationHistoryRecord.ProtoReflect.Descriptor instead.
func (*LocationHistoryRecord) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{1}
}

func (x *LocationHistoryRecord) GetPoint() *geo.Point {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *LocationHistoryRecord) GetHeadingDegrees() float64 {
	if x != nil && x.HeadingDegrees != nil {
		return *x.HeadingDegrees
	}
	return 0
}

func (x *LocationHistoryRecord) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *LocationHistoryRecord) GetData() *RobotData {
	if x != nil {
		return x.Data
	}
	return nil
}

type LocationHistoryRecordList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*LocationHistoryRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *LocationHistoryRecordList) Reset() {
	*x = LocationHistoryRecordList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationHistoryRecordList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationHistoryRecordList) ProtoMessage() {}

func (x *LocationHistoryRecordList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationHistoryRecordList.ProtoReflect.Descriptor instead.
func (*LocationHistoryRecordList) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{2}
}

func (x *LocationHistoryRecordList) GetRecords() []*LocationHistoryRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

type LogLocationHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	History *LocationHistoryRecordList `protobuf:"bytes,1,opt,name=history,proto3" json:"history,omitempty"`
}

func (x *LogLocationHistoryRequest) Reset() {
	*x = LogLocationHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogLocationHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogLocationHistoryRequest) ProtoMessage() {}

func (x *LogLocationHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogLocationHistoryRequest.ProtoReflect.Descriptor instead.
func (*LogLocationHistoryRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{3}
}

func (x *LogLocationHistoryRequest) GetHistory() *LocationHistoryRecordList {
	if x != nil {
		return x.History
	}
	return nil
}

type ListRobotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize  int32  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// If not empty, only list robots with the given serials.
	RobotSerials []string `protobuf:"bytes,3,rep,name=robot_serials,json=robotSerials,proto3" json:"robot_serials,omitempty"`
}

func (x *ListRobotsRequest) Reset() {
	*x = ListRobotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRobotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRobotsRequest) ProtoMessage() {}

func (x *ListRobotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRobotsRequest.ProtoReflect.Descriptor instead.
func (*ListRobotsRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{4}
}

func (x *ListRobotsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRobotsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListRobotsRequest) GetRobotSerials() []string {
	if x != nil {
		return x.RobotSerials
	}
	return nil
}

type ListRobotsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextPageToken string          `protobuf:"bytes,1,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	Robots        []*RobotSummary `protobuf:"bytes,2,rep,name=robots,proto3" json:"robots,omitempty"`
}

func (x *ListRobotsResponse) Reset() {
	*x = ListRobotsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRobotsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRobotsResponse) ProtoMessage() {}

func (x *ListRobotsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRobotsResponse.ProtoReflect.Descriptor instead.
func (*ListRobotsResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{5}
}

func (x *ListRobotsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListRobotsResponse) GetRobots() []*RobotSummary {
	if x != nil {
		return x.Robots
	}
	return nil
}

type RobotSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string                 `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	LastSeen *LocationHistoryRecord `protobuf:"bytes,2,opt,name=last_seen,json=lastSeen,proto3" json:"last_seen,omitempty"`
}

func (x *RobotSummary) Reset() {
	*x = RobotSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotSummary) ProtoMessage() {}

func (x *RobotSummary) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotSummary.ProtoReflect.Descriptor instead.
func (*RobotSummary) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{6}
}

func (x *RobotSummary) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *RobotSummary) GetLastSeen() *LocationHistoryRecord {
	if x != nil {
		return x.LastSeen
	}
	return nil
}

type ListLocationHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize       int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken      string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	RobotSerial    string                 `protobuf:"bytes,3,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	Start          *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start,proto3" json:"start,omitempty"`
	End            *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end,proto3" json:"end,omitempty"`
	Desc           bool                   `protobuf:"varint,6,opt,name=desc,proto3" json:"desc,omitempty"`
	IncludeClosest bool                   `protobuf:"varint,7,opt,name=include_closest,json=includeClosest,proto3" json:"include_closest,omitempty"`
	TaskId         uint64                 `protobuf:"varint,8,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	ObjectiveId    uint64                 `protobuf:"varint,9,opt,name=objective_id,json=objectiveId,proto3" json:"objective_id,omitempty"`
}

func (x *ListLocationHistoryRequest) Reset() {
	*x = ListLocationHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationHistoryRequest) ProtoMessage() {}

func (x *ListLocationHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationHistoryRequest.ProtoReflect.Descriptor instead.
func (*ListLocationHistoryRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{7}
}

func (x *ListLocationHistoryRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLocationHistoryRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListLocationHistoryRequest) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *ListLocationHistoryRequest) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *ListLocationHistoryRequest) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *ListLocationHistoryRequest) GetDesc() bool {
	if x != nil {
		return x.Desc
	}
	return false
}

func (x *ListLocationHistoryRequest) GetIncludeClosest() bool {
	if x != nil {
		return x.IncludeClosest
	}
	return false
}

func (x *ListLocationHistoryRequest) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ListLocationHistoryRequest) GetObjectiveId() uint64 {
	if x != nil {
		return x.ObjectiveId
	}
	return 0
}

type ListLocationHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextPageToken string                     `protobuf:"bytes,1,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	History       *LocationHistoryRecordList `protobuf:"bytes,2,opt,name=history,proto3" json:"history,omitempty"`
}

func (x *ListLocationHistoryResponse) Reset() {
	*x = ListLocationHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_location_history_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationHistoryResponse) ProtoMessage() {}

func (x *ListLocationHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_location_history_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationHistoryResponse.ProtoReflect.Descriptor instead.
func (*ListLocationHistoryResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_location_history_proto_rawDescGZIP(), []int{8}
}

func (x *ListLocationHistoryResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListLocationHistoryResponse) GetHistory() *LocationHistoryRecordList {
	if x != nil {
		return x.History
	}
	return nil
}

var File_proto_rtc_location_history_proto protoreflect.FileDescriptor

var file_proto_rtc_location_history_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x5f, 0x0a, 0x09, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x49, 0x64, 0x22, 0xe7, 0x01, 0x0a, 0x15, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x0a, 0x05,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x05,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x0f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00,
	0x52, 0x0e, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x29, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73, 0x22, 0x58, 0x0a, 0x19,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x07, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x5c, 0x0a, 0x19, 0x4c, 0x6f, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x22, 0x74, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x22, 0x6e, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x06, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x73, 0x22, 0x66, 0x0a, 0x0c, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x12, 0x3e, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x65,
	0x65, 0x6e, 0x22, 0xd4, 0x02, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x1b, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x3f, 0x0a, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x32, 0xea, 0x02, 0x0a, 0x0f, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x53, 0x0a, 0x12, 0x4c, 0x6f, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4b, 0x0a, 0x0a, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4d, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x28, 0x01, 0x42,
	0x0b, 0x5a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rtc_location_history_proto_rawDescOnce sync.Once
	file_proto_rtc_location_history_proto_rawDescData = file_proto_rtc_location_history_proto_rawDesc
)

func file_proto_rtc_location_history_proto_rawDescGZIP() []byte {
	file_proto_rtc_location_history_proto_rawDescOnce.Do(func() {
		file_proto_rtc_location_history_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rtc_location_history_proto_rawDescData)
	})
	return file_proto_rtc_location_history_proto_rawDescData
}

var file_proto_rtc_location_history_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proto_rtc_location_history_proto_goTypes = []interface{}{
	(*RobotData)(nil),                   // 0: carbon.rtc.RobotData
	(*LocationHistoryRecord)(nil),       // 1: carbon.rtc.LocationHistoryRecord
	(*LocationHistoryRecordList)(nil),   // 2: carbon.rtc.LocationHistoryRecordList
	(*LogLocationHistoryRequest)(nil),   // 3: carbon.rtc.LogLocationHistoryRequest
	(*ListRobotsRequest)(nil),           // 4: carbon.rtc.ListRobotsRequest
	(*ListRobotsResponse)(nil),          // 5: carbon.rtc.ListRobotsResponse
	(*RobotSummary)(nil),                // 6: carbon.rtc.RobotSummary
	(*ListLocationHistoryRequest)(nil),  // 7: carbon.rtc.ListLocationHistoryRequest
	(*ListLocationHistoryResponse)(nil), // 8: carbon.rtc.ListLocationHistoryResponse
	(*geo.Point)(nil),                   // 9: carbon.geo.Point
	(*timestamppb.Timestamp)(nil),       // 10: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),               // 11: google.protobuf.Empty
}
var file_proto_rtc_location_history_proto_depIdxs = []int32{
	9,  // 0: carbon.rtc.LocationHistoryRecord.point:type_name -> carbon.geo.Point
	10, // 1: carbon.rtc.LocationHistoryRecord.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 2: carbon.rtc.LocationHistoryRecord.data:type_name -> carbon.rtc.RobotData
	1,  // 3: carbon.rtc.LocationHistoryRecordList.records:type_name -> carbon.rtc.LocationHistoryRecord
	2,  // 4: carbon.rtc.LogLocationHistoryRequest.history:type_name -> carbon.rtc.LocationHistoryRecordList
	6,  // 5: carbon.rtc.ListRobotsResponse.robots:type_name -> carbon.rtc.RobotSummary
	1,  // 6: carbon.rtc.RobotSummary.last_seen:type_name -> carbon.rtc.LocationHistoryRecord
	10, // 7: carbon.rtc.ListLocationHistoryRequest.start:type_name -> google.protobuf.Timestamp
	10, // 8: carbon.rtc.ListLocationHistoryRequest.end:type_name -> google.protobuf.Timestamp
	2,  // 9: carbon.rtc.ListLocationHistoryResponse.history:type_name -> carbon.rtc.LocationHistoryRecordList
	3,  // 10: carbon.rtc.LocationHistory.LogLocationHistory:input_type -> carbon.rtc.LogLocationHistoryRequest
	4,  // 11: carbon.rtc.LocationHistory.ListRobots:input_type -> carbon.rtc.ListRobotsRequest
	7,  // 12: carbon.rtc.LocationHistory.ListLocationHistory:input_type -> carbon.rtc.ListLocationHistoryRequest
	1,  // 13: carbon.rtc.LocationHistory.StreamLocation:input_type -> carbon.rtc.LocationHistoryRecord
	11, // 14: carbon.rtc.LocationHistory.LogLocationHistory:output_type -> google.protobuf.Empty
	5,  // 15: carbon.rtc.LocationHistory.ListRobots:output_type -> carbon.rtc.ListRobotsResponse
	8,  // 16: carbon.rtc.LocationHistory.ListLocationHistory:output_type -> carbon.rtc.ListLocationHistoryResponse
	11, // 17: carbon.rtc.LocationHistory.StreamLocation:output_type -> google.protobuf.Empty
	14, // [14:18] is the sub-list for method output_type
	10, // [10:14] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proto_rtc_location_history_proto_init() }
func file_proto_rtc_location_history_proto_init() {
	if File_proto_rtc_location_history_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rtc_location_history_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationHistoryRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationHistoryRecordList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogLocationHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRobotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRobotsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_location_history_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_rtc_location_history_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rtc_location_history_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rtc_location_history_proto_goTypes,
		DependencyIndexes: file_proto_rtc_location_history_proto_depIdxs,
		MessageInfos:      file_proto_rtc_location_history_proto_msgTypes,
	}.Build()
	File_proto_rtc_location_history_proto = out.File
	file_proto_rtc_location_history_proto_rawDesc = nil
	file_proto_rtc_location_history_proto_goTypes = nil
	file_proto_rtc_location_history_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/rtc/broadcaster.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Read  bool `protobuf:"varint,1,opt,name=read,proto3" json:"read,omitempty"`
	Write bool `protobuf:"varint,2,opt,name=write,proto3" json:"write,omitempty"`
}

func (x *AuthStatus) Reset() {
	*x = AuthStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_broadcaster_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthStatus) ProtoMessage() {}

func (x *AuthStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_broadcaster_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthStatus.ProtoReflect.Descriptor instead.
func (*AuthStatus) Descriptor() ([]byte, []int) {
	return file_proto_rtc_broadcaster_proto_rawDescGZIP(), []int{0}
}

func (x *AuthStatus) GetRead() bool {
	if x != nil {
		return x.Read
	}
	return false
}

func (x *AuthStatus) GetWrite() bool {
	if x != nil {
		return x.Write
	}
	return false
}

type RtcMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Msg  []byte      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Auth *AuthStatus `protobuf:"bytes,3,opt,name=auth,proto3" json:"auth,omitempty"`
}

func (x *RtcMessage) Reset() {
	*x = RtcMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_broadcaster_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtcMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtcMessage) ProtoMessage() {}

func (x *RtcMessage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_broadcaster_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtcMessage.ProtoReflect.Descriptor instead.
func (*RtcMessage) Descriptor() ([]byte, []int) {
	return file_proto_rtc_broadcaster_proto_rawDescGZIP(), []int{1}
}

func (x *RtcMessage) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RtcMessage) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

func (x *RtcMessage) GetAuth() *AuthStatus {
	if x != nil {
		return x.Auth
	}
	return nil
}

type SignalingMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg []byte `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SignalingMsg) Reset() {
	*x = SignalingMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_broadcaster_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignalingMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignalingMsg) ProtoMessage() {}

func (x *SignalingMsg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_broadcaster_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignalingMsg.ProtoReflect.Descriptor instead.
func (*SignalingMsg) Descriptor() ([]byte, []int) {
	return file_proto_rtc_broadcaster_proto_rawDescGZIP(), []int{2}
}

func (x *SignalingMsg) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

type StreamListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StreamListRequest) Reset() {
	*x = StreamListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_broadcaster_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamListRequest) ProtoMessage() {}

func (x *StreamListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_broadcaster_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamListRequest.ProtoReflect.Descriptor instead.
func (*StreamListRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_broadcaster_proto_rawDescGZIP(), []int{3}
}

type StreamListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Streams map[string]string `protobuf:"bytes,1,rep,name=streams,proto3" json:"streams,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // map of human readable name to globally unique stream id
}

func (x *StreamListResponse) Reset() {
	*x = StreamListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_broadcaster_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamListResponse) ProtoMessage() {}

func (x *StreamListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_broadcaster_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamListResponse.ProtoReflect.Descriptor instead.
func (*StreamListResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_broadcaster_proto_rawDescGZIP(), []int{4}
}

func (x *StreamListResponse) GetStreams() map[string]string {
	if x != nil {
		return x.Streams
	}
	return nil
}

var File_proto_rtc_broadcaster_proto protoreflect.FileDescriptor

var file_proto_rtc_broadcaster_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x2f, 0x62, 0x72, 0x6f, 0x61,
	0x64, 0x63, 0x61, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x22, 0x36, 0x0a, 0x0a, 0x41, 0x75, 0x74,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x61, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x72, 0x65, 0x61, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x22, 0x5a, 0x0a, 0x0a, 0x52, 0x74, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x2a, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x22, 0x20, 0x0a,
	0x0c, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x13, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x97, 0x01, 0x0a, 0x12, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x07, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xec,
	0x01, 0x0a, 0x0b, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x65, 0x72, 0x12, 0x40,
	0x0a, 0x0a, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x74, 0x63, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x52, 0x74, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x28, 0x01, 0x30, 0x01,
	0x12, 0x4b, 0x0a, 0x11, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x1a,
	0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x28, 0x01, 0x30, 0x01, 0x12, 0x4e, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0b, 0x5a,
	0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_rtc_broadcaster_proto_rawDescOnce sync.Once
	file_proto_rtc_broadcaster_proto_rawDescData = file_proto_rtc_broadcaster_proto_rawDesc
)

func file_proto_rtc_broadcaster_proto_rawDescGZIP() []byte {
	file_proto_rtc_broadcaster_proto_rawDescOnce.Do(func() {
		file_proto_rtc_broadcaster_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rtc_broadcaster_proto_rawDescData)
	})
	return file_proto_rtc_broadcaster_proto_rawDescData
}

var file_proto_rtc_broadcaster_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_rtc_broadcaster_proto_goTypes = []interface{}{
	(*AuthStatus)(nil),         // 0: carbon.rtc.AuthStatus
	(*RtcMessage)(nil),         // 1: carbon.rtc.RtcMessage
	(*SignalingMsg)(nil),       // 2: carbon.rtc.SignalingMsg
	(*StreamListRequest)(nil),  // 3: carbon.rtc.StreamListRequest
	(*StreamListResponse)(nil), // 4: carbon.rtc.StreamListResponse
	nil,                        // 5: carbon.rtc.StreamListResponse.StreamsEntry
}
var file_proto_rtc_broadcaster_proto_depIdxs = []int32{
	0, // 0: carbon.rtc.RtcMessage.auth:type_name -> carbon.rtc.AuthStatus
	5, // 1: carbon.rtc.StreamListResponse.streams:type_name -> carbon.rtc.StreamListResponse.StreamsEntry
	1, // 2: carbon.rtc.Broadcaster.MessageBus:input_type -> carbon.rtc.RtcMessage
	2, // 3: carbon.rtc.Broadcaster.LocalSignalServer:input_type -> carbon.rtc.SignalingMsg
	3, // 4: carbon.rtc.Broadcaster.GetStreamList:input_type -> carbon.rtc.StreamListRequest
	1, // 5: carbon.rtc.Broadcaster.MessageBus:output_type -> carbon.rtc.RtcMessage
	2, // 6: carbon.rtc.Broadcaster.LocalSignalServer:output_type -> carbon.rtc.SignalingMsg
	4, // 7: carbon.rtc.Broadcaster.GetStreamList:output_type -> carbon.rtc.StreamListResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_rtc_broadcaster_proto_init() }
func file_proto_rtc_broadcaster_proto_init() {
	if File_proto_rtc_broadcaster_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rtc_broadcaster_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_broadcaster_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtcMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_broadcaster_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignalingMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_broadcaster_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_broadcaster_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rtc_broadcaster_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rtc_broadcaster_proto_goTypes,
		DependencyIndexes: file_proto_rtc_broadcaster_proto_depIdxs,
		MessageInfos:      file_proto_rtc_broadcaster_proto_msgTypes,
	}.Build()
	File_proto_rtc_broadcaster_proto = out.File
	file_proto_rtc_broadcaster_proto_rawDesc = nil
	file_proto_rtc_broadcaster_proto_goTypes = nil
	file_proto_rtc_broadcaster_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/rtc/jobs.proto

package rtc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	JobService_CreateIntervention_FullMethodName     = "/carbon.rtc.JobService/CreateIntervention"
	JobService_GetActiveTask_FullMethodName          = "/carbon.rtc.JobService/GetActiveTask"
	JobService_GetTask_FullMethodName                = "/carbon.rtc.JobService/GetTask"
	JobService_GetNextActiveObjective_FullMethodName = "/carbon.rtc.JobService/GetNextActiveObjective"
	JobService_UpdateTask_FullMethodName             = "/carbon.rtc.JobService/UpdateTask"
)

// JobServiceClient is the client API for JobService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JobServiceClient interface {
	// Robot API
	CreateIntervention(ctx context.Context, in *CreateInterventionRequest, opts ...grpc.CallOption) (*CreateInterventionResponse, error)
	GetActiveTask(ctx context.Context, in *GetActiveTaskRequest, opts ...grpc.CallOption) (*GetActiveTaskResponse, error)
	GetTask(ctx context.Context, in *GetTaskRequest, opts ...grpc.CallOption) (*GetTaskResponse, error)
	GetNextActiveObjective(ctx context.Context, in *GetNextActiveObjectiveRequest, opts ...grpc.CallOption) (*GetNextActiveObjectiveResponse, error)
	UpdateTask(ctx context.Context, in *UpdateTaskRequest, opts ...grpc.CallOption) (*UpdateTaskResponse, error)
}

type jobServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJobServiceClient(cc grpc.ClientConnInterface) JobServiceClient {
	return &jobServiceClient{cc}
}

func (c *jobServiceClient) CreateIntervention(ctx context.Context, in *CreateInterventionRequest, opts ...grpc.CallOption) (*CreateInterventionResponse, error) {
	out := new(CreateInterventionResponse)
	err := c.cc.Invoke(ctx, JobService_CreateIntervention_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetActiveTask(ctx context.Context, in *GetActiveTaskRequest, opts ...grpc.CallOption) (*GetActiveTaskResponse, error) {
	out := new(GetActiveTaskResponse)
	err := c.cc.Invoke(ctx, JobService_GetActiveTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetTask(ctx context.Context, in *GetTaskRequest, opts ...grpc.CallOption) (*GetTaskResponse, error) {
	out := new(GetTaskResponse)
	err := c.cc.Invoke(ctx, JobService_GetTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetNextActiveObjective(ctx context.Context, in *GetNextActiveObjectiveRequest, opts ...grpc.CallOption) (*GetNextActiveObjectiveResponse, error) {
	out := new(GetNextActiveObjectiveResponse)
	err := c.cc.Invoke(ctx, JobService_GetNextActiveObjective_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateTask(ctx context.Context, in *UpdateTaskRequest, opts ...grpc.CallOption) (*UpdateTaskResponse, error) {
	out := new(UpdateTaskResponse)
	err := c.cc.Invoke(ctx, JobService_UpdateTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobServiceServer is the server API for JobService service.
// All implementations must embed UnimplementedJobServiceServer
// for forward compatibility
type JobServiceServer interface {
	// Robot API
	CreateIntervention(context.Context, *CreateInterventionRequest) (*CreateInterventionResponse, error)
	GetActiveTask(context.Context, *GetActiveTaskRequest) (*GetActiveTaskResponse, error)
	GetTask(context.Context, *GetTaskRequest) (*GetTaskResponse, error)
	GetNextActiveObjective(context.Context, *GetNextActiveObjectiveRequest) (*GetNextActiveObjectiveResponse, error)
	UpdateTask(context.Context, *UpdateTaskRequest) (*UpdateTaskResponse, error)
	mustEmbedUnimplementedJobServiceServer()
}

// UnimplementedJobServiceServer must be embedded to have forward compatible implementations.
type UnimplementedJobServiceServer struct {
}

func (UnimplementedJobServiceServer) CreateIntervention(context.Context, *CreateInterventionRequest) (*CreateInterventionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIntervention not implemented")
}
func (UnimplementedJobServiceServer) GetActiveTask(context.Context, *GetActiveTaskRequest) (*GetActiveTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveTask not implemented")
}
func (UnimplementedJobServiceServer) GetTask(context.Context, *GetTaskRequest) (*GetTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTask not implemented")
}
func (UnimplementedJobServiceServer) GetNextActiveObjective(context.Context, *GetNextActiveObjectiveRequest) (*GetNextActiveObjectiveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActiveObjective not implemented")
}
func (UnimplementedJobServiceServer) UpdateTask(context.Context, *UpdateTaskRequest) (*UpdateTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTask not implemented")
}
func (UnimplementedJobServiceServer) mustEmbedUnimplementedJobServiceServer() {}

// UnsafeJobServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobServiceServer will
// result in compilation errors.
type UnsafeJobServiceServer interface {
	mustEmbedUnimplementedJobServiceServer()
}

func RegisterJobServiceServer(s grpc.ServiceRegistrar, srv JobServiceServer) {
	s.RegisterService(&JobService_ServiceDesc, srv)
}

func _JobService_CreateIntervention_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInterventionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CreateIntervention(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CreateIntervention_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CreateIntervention(ctx, req.(*CreateInterventionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetActiveTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetActiveTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetActiveTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetActiveTask(ctx, req.(*GetActiveTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetTask(ctx, req.(*GetTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetNextActiveObjective_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextActiveObjectiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetNextActiveObjective(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetNextActiveObjective_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetNextActiveObjective(ctx, req.(*GetNextActiveObjectiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateTask(ctx, req.(*UpdateTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JobService_ServiceDesc is the grpc.ServiceDesc for JobService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JobService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.rtc.JobService",
	HandlerType: (*JobServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateIntervention",
			Handler:    _JobService_CreateIntervention_Handler,
		},
		{
			MethodName: "GetActiveTask",
			Handler:    _JobService_GetActiveTask_Handler,
		},
		{
			MethodName: "GetTask",
			Handler:    _JobService_GetTask_Handler,
		},
		{
			MethodName: "GetNextActiveObjective",
			Handler:    _JobService_GetNextActiveObjective_Handler,
		},
		{
			MethodName: "UpdateTask",
			Handler:    _JobService_UpdateTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rtc/jobs.proto",
}

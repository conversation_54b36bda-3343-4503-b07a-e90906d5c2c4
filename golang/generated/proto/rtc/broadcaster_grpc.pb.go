// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/rtc/broadcaster.proto

package rtc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Broadcaster_MessageBus_FullMethodName        = "/carbon.rtc.Broadcaster/MessageBus"
	Broadcaster_LocalSignalServer_FullMethodName = "/carbon.rtc.Broadcaster/LocalSignalServer"
	Broadcaster_GetStreamList_FullMethodName     = "/carbon.rtc.Broadcaster/GetStreamList"
)

// BroadcasterClient is the client API for Broadcaster service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BroadcasterClient interface {
	MessageBus(ctx context.Context, opts ...grpc.CallOption) (Broadcaster_MessageBusClient, error)
	LocalSignalServer(ctx context.Context, opts ...grpc.CallOption) (Broadcaster_LocalSignalServerClient, error)
	GetStreamList(ctx context.Context, in *StreamListRequest, opts ...grpc.CallOption) (*StreamListResponse, error)
}

type broadcasterClient struct {
	cc grpc.ClientConnInterface
}

func NewBroadcasterClient(cc grpc.ClientConnInterface) BroadcasterClient {
	return &broadcasterClient{cc}
}

func (c *broadcasterClient) MessageBus(ctx context.Context, opts ...grpc.CallOption) (Broadcaster_MessageBusClient, error) {
	stream, err := c.cc.NewStream(ctx, &Broadcaster_ServiceDesc.Streams[0], Broadcaster_MessageBus_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &broadcasterMessageBusClient{stream}
	return x, nil
}

type Broadcaster_MessageBusClient interface {
	Send(*RtcMessage) error
	Recv() (*RtcMessage, error)
	grpc.ClientStream
}

type broadcasterMessageBusClient struct {
	grpc.ClientStream
}

func (x *broadcasterMessageBusClient) Send(m *RtcMessage) error {
	return x.ClientStream.SendMsg(m)
}

func (x *broadcasterMessageBusClient) Recv() (*RtcMessage, error) {
	m := new(RtcMessage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *broadcasterClient) LocalSignalServer(ctx context.Context, opts ...grpc.CallOption) (Broadcaster_LocalSignalServerClient, error) {
	stream, err := c.cc.NewStream(ctx, &Broadcaster_ServiceDesc.Streams[1], Broadcaster_LocalSignalServer_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &broadcasterLocalSignalServerClient{stream}
	return x, nil
}

type Broadcaster_LocalSignalServerClient interface {
	Send(*SignalingMsg) error
	Recv() (*SignalingMsg, error)
	grpc.ClientStream
}

type broadcasterLocalSignalServerClient struct {
	grpc.ClientStream
}

func (x *broadcasterLocalSignalServerClient) Send(m *SignalingMsg) error {
	return x.ClientStream.SendMsg(m)
}

func (x *broadcasterLocalSignalServerClient) Recv() (*SignalingMsg, error) {
	m := new(SignalingMsg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *broadcasterClient) GetStreamList(ctx context.Context, in *StreamListRequest, opts ...grpc.CallOption) (*StreamListResponse, error) {
	out := new(StreamListResponse)
	err := c.cc.Invoke(ctx, Broadcaster_GetStreamList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BroadcasterServer is the server API for Broadcaster service.
// All implementations must embed UnimplementedBroadcasterServer
// for forward compatibility
type BroadcasterServer interface {
	MessageBus(Broadcaster_MessageBusServer) error
	LocalSignalServer(Broadcaster_LocalSignalServerServer) error
	GetStreamList(context.Context, *StreamListRequest) (*StreamListResponse, error)
	mustEmbedUnimplementedBroadcasterServer()
}

// UnimplementedBroadcasterServer must be embedded to have forward compatible implementations.
type UnimplementedBroadcasterServer struct {
}

func (UnimplementedBroadcasterServer) MessageBus(Broadcaster_MessageBusServer) error {
	return status.Errorf(codes.Unimplemented, "method MessageBus not implemented")
}
func (UnimplementedBroadcasterServer) LocalSignalServer(Broadcaster_LocalSignalServerServer) error {
	return status.Errorf(codes.Unimplemented, "method LocalSignalServer not implemented")
}
func (UnimplementedBroadcasterServer) GetStreamList(context.Context, *StreamListRequest) (*StreamListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStreamList not implemented")
}
func (UnimplementedBroadcasterServer) mustEmbedUnimplementedBroadcasterServer() {}

// UnsafeBroadcasterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BroadcasterServer will
// result in compilation errors.
type UnsafeBroadcasterServer interface {
	mustEmbedUnimplementedBroadcasterServer()
}

func RegisterBroadcasterServer(s grpc.ServiceRegistrar, srv BroadcasterServer) {
	s.RegisterService(&Broadcaster_ServiceDesc, srv)
}

func _Broadcaster_MessageBus_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(BroadcasterServer).MessageBus(&broadcasterMessageBusServer{stream})
}

type Broadcaster_MessageBusServer interface {
	Send(*RtcMessage) error
	Recv() (*RtcMessage, error)
	grpc.ServerStream
}

type broadcasterMessageBusServer struct {
	grpc.ServerStream
}

func (x *broadcasterMessageBusServer) Send(m *RtcMessage) error {
	return x.ServerStream.SendMsg(m)
}

func (x *broadcasterMessageBusServer) Recv() (*RtcMessage, error) {
	m := new(RtcMessage)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Broadcaster_LocalSignalServer_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(BroadcasterServer).LocalSignalServer(&broadcasterLocalSignalServerServer{stream})
}

type Broadcaster_LocalSignalServerServer interface {
	Send(*SignalingMsg) error
	Recv() (*SignalingMsg, error)
	grpc.ServerStream
}

type broadcasterLocalSignalServerServer struct {
	grpc.ServerStream
}

func (x *broadcasterLocalSignalServerServer) Send(m *SignalingMsg) error {
	return x.ServerStream.SendMsg(m)
}

func (x *broadcasterLocalSignalServerServer) Recv() (*SignalingMsg, error) {
	m := new(SignalingMsg)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Broadcaster_GetStreamList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StreamListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BroadcasterServer).GetStreamList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Broadcaster_GetStreamList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BroadcasterServer).GetStreamList(ctx, req.(*StreamListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Broadcaster_ServiceDesc is the grpc.ServiceDesc for Broadcaster service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Broadcaster_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.rtc.Broadcaster",
	HandlerType: (*BroadcasterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStreamList",
			Handler:    _Broadcaster_GetStreamList_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "MessageBus",
			Handler:       _Broadcaster_MessageBus_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "LocalSignalServer",
			Handler:       _Broadcaster_LocalSignalServer_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "proto/rtc/broadcaster.proto",
}

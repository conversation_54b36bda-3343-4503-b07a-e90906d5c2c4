// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/rtc/location_history.proto

package rtc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LocationHistory_LogLocationHistory_FullMethodName  = "/carbon.rtc.LocationHistory/LogLocationHistory"
	LocationHistory_ListRobots_FullMethodName          = "/carbon.rtc.LocationHistory/ListRobots"
	LocationHistory_ListLocationHistory_FullMethodName = "/carbon.rtc.LocationHistory/ListLocationHistory"
	LocationHistory_StreamLocation_FullMethodName      = "/carbon.rtc.LocationHistory/StreamLocation"
)

// LocationHistoryClient is the client API for LocationHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LocationHistoryClient interface {
	LogLocationHistory(ctx context.Context, in *LogLocationHistoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListRobots(ctx context.Context, in *ListRobotsRequest, opts ...grpc.CallOption) (*ListRobotsResponse, error)
	ListLocationHistory(ctx context.Context, in *ListLocationHistoryRequest, opts ...grpc.CallOption) (*ListLocationHistoryResponse, error)
	StreamLocation(ctx context.Context, opts ...grpc.CallOption) (LocationHistory_StreamLocationClient, error)
}

type locationHistoryClient struct {
	cc grpc.ClientConnInterface
}

func NewLocationHistoryClient(cc grpc.ClientConnInterface) LocationHistoryClient {
	return &locationHistoryClient{cc}
}

func (c *locationHistoryClient) LogLocationHistory(ctx context.Context, in *LogLocationHistoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, LocationHistory_LogLocationHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *locationHistoryClient) ListRobots(ctx context.Context, in *ListRobotsRequest, opts ...grpc.CallOption) (*ListRobotsResponse, error) {
	out := new(ListRobotsResponse)
	err := c.cc.Invoke(ctx, LocationHistory_ListRobots_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *locationHistoryClient) ListLocationHistory(ctx context.Context, in *ListLocationHistoryRequest, opts ...grpc.CallOption) (*ListLocationHistoryResponse, error) {
	out := new(ListLocationHistoryResponse)
	err := c.cc.Invoke(ctx, LocationHistory_ListLocationHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *locationHistoryClient) StreamLocation(ctx context.Context, opts ...grpc.CallOption) (LocationHistory_StreamLocationClient, error) {
	stream, err := c.cc.NewStream(ctx, &LocationHistory_ServiceDesc.Streams[0], LocationHistory_StreamLocation_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &locationHistoryStreamLocationClient{stream}
	return x, nil
}

type LocationHistory_StreamLocationClient interface {
	Send(*LocationHistoryRecord) error
	CloseAndRecv() (*emptypb.Empty, error)
	grpc.ClientStream
}

type locationHistoryStreamLocationClient struct {
	grpc.ClientStream
}

func (x *locationHistoryStreamLocationClient) Send(m *LocationHistoryRecord) error {
	return x.ClientStream.SendMsg(m)
}

func (x *locationHistoryStreamLocationClient) CloseAndRecv() (*emptypb.Empty, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(emptypb.Empty)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// LocationHistoryServer is the server API for LocationHistory service.
// All implementations must embed UnimplementedLocationHistoryServer
// for forward compatibility
type LocationHistoryServer interface {
	LogLocationHistory(context.Context, *LogLocationHistoryRequest) (*emptypb.Empty, error)
	ListRobots(context.Context, *ListRobotsRequest) (*ListRobotsResponse, error)
	ListLocationHistory(context.Context, *ListLocationHistoryRequest) (*ListLocationHistoryResponse, error)
	StreamLocation(LocationHistory_StreamLocationServer) error
	mustEmbedUnimplementedLocationHistoryServer()
}

// UnimplementedLocationHistoryServer must be embedded to have forward compatible implementations.
type UnimplementedLocationHistoryServer struct {
}

func (UnimplementedLocationHistoryServer) LogLocationHistory(context.Context, *LogLocationHistoryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogLocationHistory not implemented")
}
func (UnimplementedLocationHistoryServer) ListRobots(context.Context, *ListRobotsRequest) (*ListRobotsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRobots not implemented")
}
func (UnimplementedLocationHistoryServer) ListLocationHistory(context.Context, *ListLocationHistoryRequest) (*ListLocationHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLocationHistory not implemented")
}
func (UnimplementedLocationHistoryServer) StreamLocation(LocationHistory_StreamLocationServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamLocation not implemented")
}
func (UnimplementedLocationHistoryServer) mustEmbedUnimplementedLocationHistoryServer() {}

// UnsafeLocationHistoryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LocationHistoryServer will
// result in compilation errors.
type UnsafeLocationHistoryServer interface {
	mustEmbedUnimplementedLocationHistoryServer()
}

func RegisterLocationHistoryServer(s grpc.ServiceRegistrar, srv LocationHistoryServer) {
	s.RegisterService(&LocationHistory_ServiceDesc, srv)
}

func _LocationHistory_LogLocationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogLocationHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LocationHistoryServer).LogLocationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LocationHistory_LogLocationHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LocationHistoryServer).LogLocationHistory(ctx, req.(*LogLocationHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LocationHistory_ListRobots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRobotsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LocationHistoryServer).ListRobots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LocationHistory_ListRobots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LocationHistoryServer).ListRobots(ctx, req.(*ListRobotsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LocationHistory_ListLocationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLocationHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LocationHistoryServer).ListLocationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LocationHistory_ListLocationHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LocationHistoryServer).ListLocationHistory(ctx, req.(*ListLocationHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LocationHistory_StreamLocation_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(LocationHistoryServer).StreamLocation(&locationHistoryStreamLocationServer{stream})
}

type LocationHistory_StreamLocationServer interface {
	SendAndClose(*emptypb.Empty) error
	Recv() (*LocationHistoryRecord, error)
	grpc.ServerStream
}

type locationHistoryStreamLocationServer struct {
	grpc.ServerStream
}

func (x *locationHistoryStreamLocationServer) SendAndClose(m *emptypb.Empty) error {
	return x.ServerStream.SendMsg(m)
}

func (x *locationHistoryStreamLocationServer) Recv() (*LocationHistoryRecord, error) {
	m := new(LocationHistoryRecord)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// LocationHistory_ServiceDesc is the grpc.ServiceDesc for LocationHistory service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LocationHistory_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.rtc.LocationHistory",
	HandlerType: (*LocationHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LogLocationHistory",
			Handler:    _LocationHistory_LogLocationHistory_Handler,
		},
		{
			MethodName: "ListRobots",
			Handler:    _LocationHistory_ListRobots_Handler,
		},
		{
			MethodName: "ListLocationHistory",
			Handler:    _LocationHistory_ListLocationHistory_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamLocation",
			Handler:       _LocationHistory_StreamLocation_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "proto/rtc/location_history.proto",
}

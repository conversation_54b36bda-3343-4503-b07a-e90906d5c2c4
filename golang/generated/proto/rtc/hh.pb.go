// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/rtc/hh.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TODO:(smt) this should mirror nanopb messages for hh, we need to link back
// with comments or docs.
type HHStateStatus int32

const (
	HHStateStatus_HH_UNKNOWN     HHStateStatus = 0
	HHStateStatus_HH_DISABLED    HHStateStatus = 1
	HHStateStatus_HH_OPERATIONAL HHStateStatus = 2
	HHStateStatus_HH_STOPPED     HHStateStatus = 3
	HHStateStatus_HH_SAFE        HHStateStatus = 4
	HHStateStatus_HH_ESTOP       HHStateStatus = 5
)

// Enum value maps for HHStateStatus.
var (
	HHStateStatus_name = map[int32]string{
		0: "HH_UNKNOWN",
		1: "HH_DISABLED",
		2: "HH_OPERATIONAL",
		3: "HH_STOPPED",
		4: "HH_SAFE",
		5: "HH_ESTOP",
	}
	HHStateStatus_value = map[string]int32{
		"HH_UNKNOWN":     0,
		"HH_DISABLED":    1,
		"HH_OPERATIONAL": 2,
		"HH_STOPPED":     3,
		"HH_SAFE":        4,
		"HH_ESTOP":       5,
	}
)

func (x HHStateStatus) Enum() *HHStateStatus {
	p := new(HHStateStatus)
	*p = x
	return p
}

func (x HHStateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HHStateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_hh_proto_enumTypes[0].Descriptor()
}

func (HHStateStatus) Type() protoreflect.EnumType {
	return &file_proto_rtc_hh_proto_enumTypes[0]
}

func (x HHStateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HHStateStatus.Descriptor instead.
func (HHStateStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_hh_proto_rawDescGZIP(), []int{0}
}

// RobotRequiredState denotes requirements from the cloud that should be
// enforced on the robot.
type RobotRequiredState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoppedState bool `protobuf:"varint,1,opt,name=stopped_state,json=stoppedState,proto3" json:"stopped_state,omitempty"`
}

func (x *RobotRequiredState) Reset() {
	*x = RobotRequiredState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_hh_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotRequiredState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotRequiredState) ProtoMessage() {}

func (x *RobotRequiredState) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_hh_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotRequiredState.ProtoReflect.Descriptor instead.
func (*RobotRequiredState) Descriptor() ([]byte, []int) {
	return file_proto_rtc_hh_proto_rawDescGZIP(), []int{0}
}

func (x *RobotRequiredState) GetStoppedState() bool {
	if x != nil {
		return x.StoppedState
	}
	return false
}

// RobotStatusInfo is the robot returning info that is relevant for the
// requirements. Ex: robot in DISABLED state would qualify as success for
// requesting 'stopped state'
type RobotStatusInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State HHStateStatus `protobuf:"varint,1,opt,name=state,proto3,enum=carbon.rtc.HHStateStatus" json:"state,omitempty"`
}

func (x *RobotStatusInfo) Reset() {
	*x = RobotStatusInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_hh_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotStatusInfo) ProtoMessage() {}

func (x *RobotStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_hh_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotStatusInfo.ProtoReflect.Descriptor instead.
func (*RobotStatusInfo) Descriptor() ([]byte, []int) {
	return file_proto_rtc_hh_proto_rawDescGZIP(), []int{1}
}

func (x *RobotStatusInfo) GetState() HHStateStatus {
	if x != nil {
		return x.State
	}
	return HHStateStatus_HH_UNKNOWN
}

type SetRobotRequiredStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs   int64               `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	RobotSerial   string              `protobuf:"bytes,2,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	RequiredState *RobotRequiredState `protobuf:"bytes,3,opt,name=required_state,json=requiredState,proto3" json:"required_state,omitempty"`
}

func (x *SetRobotRequiredStateRequest) Reset() {
	*x = SetRobotRequiredStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_hh_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRobotRequiredStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRobotRequiredStateRequest) ProtoMessage() {}

func (x *SetRobotRequiredStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_hh_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRobotRequiredStateRequest.ProtoReflect.Descriptor instead.
func (*SetRobotRequiredStateRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_hh_proto_rawDescGZIP(), []int{2}
}

func (x *SetRobotRequiredStateRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *SetRobotRequiredStateRequest) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *SetRobotRequiredStateRequest) GetRequiredState() *RobotRequiredState {
	if x != nil {
		return x.RequiredState
	}
	return nil
}

type GetRobotRequiredStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64  `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	RobotSerial string `protobuf:"bytes,2,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
}

func (x *GetRobotRequiredStateRequest) Reset() {
	*x = GetRobotRequiredStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_hh_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRobotRequiredStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRobotRequiredStateRequest) ProtoMessage() {}

func (x *GetRobotRequiredStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_hh_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRobotRequiredStateRequest.ProtoReflect.Descriptor instead.
func (*GetRobotRequiredStateRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_hh_proto_rawDescGZIP(), []int{3}
}

func (x *GetRobotRequiredStateRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetRobotRequiredStateRequest) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

type GetRobotRequiredStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs   int64               `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	RequiredState *RobotRequiredState `protobuf:"bytes,2,opt,name=required_state,json=requiredState,proto3" json:"required_state,omitempty"`
}

func (x *GetRobotRequiredStateResponse) Reset() {
	*x = GetRobotRequiredStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_hh_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRobotRequiredStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRobotRequiredStateResponse) ProtoMessage() {}

func (x *GetRobotRequiredStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_hh_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRobotRequiredStateResponse.ProtoReflect.Descriptor instead.
func (*GetRobotRequiredStateResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_hh_proto_rawDescGZIP(), []int{4}
}

func (x *GetRobotRequiredStateResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetRobotRequiredStateResponse) GetRequiredState() *RobotRequiredState {
	if x != nil {
		return x.RequiredState
	}
	return nil
}

var File_proto_rtc_hh_proto protoreflect.FileDescriptor

var file_proto_rtc_hh_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x2f, 0x68, 0x68, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a,
	0x12, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x70,
	0x70, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x42, 0x0a, 0x0f, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xab, 0x01, 0x0a,
	0x1c, 0x53, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0x45, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x64, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x22, 0x89, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x45, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2a, 0x6f, 0x0a, 0x0d,
	0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a,
	0x0a, 0x48, 0x48, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a,
	0x0b, 0x48, 0x48, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x48, 0x48, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c,
	0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x48, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x48, 0x48, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x10, 0x04, 0x12,
	0x0c, 0x0a, 0x08, 0x48, 0x48, 0x5f, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x10, 0x05, 0x32, 0xaf, 0x02,
	0x0a, 0x0a, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x59, 0x0a, 0x15,
	0x53, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x16, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x1b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x28, 0x01, 0x30, 0x01, 0x42,
	0x0b, 0x5a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rtc_hh_proto_rawDescOnce sync.Once
	file_proto_rtc_hh_proto_rawDescData = file_proto_rtc_hh_proto_rawDesc
)

func file_proto_rtc_hh_proto_rawDescGZIP() []byte {
	file_proto_rtc_hh_proto_rawDescOnce.Do(func() {
		file_proto_rtc_hh_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rtc_hh_proto_rawDescData)
	})
	return file_proto_rtc_hh_proto_rawDescData
}

var file_proto_rtc_hh_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_rtc_hh_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_rtc_hh_proto_goTypes = []interface{}{
	(HHStateStatus)(0),                    // 0: carbon.rtc.HHStateStatus
	(*RobotRequiredState)(nil),            // 1: carbon.rtc.RobotRequiredState
	(*RobotStatusInfo)(nil),               // 2: carbon.rtc.RobotStatusInfo
	(*SetRobotRequiredStateRequest)(nil),  // 3: carbon.rtc.SetRobotRequiredStateRequest
	(*GetRobotRequiredStateRequest)(nil),  // 4: carbon.rtc.GetRobotRequiredStateRequest
	(*GetRobotRequiredStateResponse)(nil), // 5: carbon.rtc.GetRobotRequiredStateResponse
	(*emptypb.Empty)(nil),                 // 6: google.protobuf.Empty
}
var file_proto_rtc_hh_proto_depIdxs = []int32{
	0, // 0: carbon.rtc.RobotStatusInfo.state:type_name -> carbon.rtc.HHStateStatus
	1, // 1: carbon.rtc.SetRobotRequiredStateRequest.required_state:type_name -> carbon.rtc.RobotRequiredState
	1, // 2: carbon.rtc.GetRobotRequiredStateResponse.required_state:type_name -> carbon.rtc.RobotRequiredState
	3, // 3: carbon.rtc.RobotState.SetRobotRequiredState:input_type -> carbon.rtc.SetRobotRequiredStateRequest
	4, // 4: carbon.rtc.RobotState.GetNextRequiredState:input_type -> carbon.rtc.GetRobotRequiredStateRequest
	2, // 5: carbon.rtc.RobotState.RobotRequirementStream:input_type -> carbon.rtc.RobotStatusInfo
	6, // 6: carbon.rtc.RobotState.SetRobotRequiredState:output_type -> google.protobuf.Empty
	5, // 7: carbon.rtc.RobotState.GetNextRequiredState:output_type -> carbon.rtc.GetRobotRequiredStateResponse
	1, // 8: carbon.rtc.RobotState.RobotRequirementStream:output_type -> carbon.rtc.RobotRequiredState
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proto_rtc_hh_proto_init() }
func file_proto_rtc_hh_proto_init() {
	if File_proto_rtc_hh_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rtc_hh_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotRequiredState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_hh_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotStatusInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_hh_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRobotRequiredStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_hh_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRobotRequiredStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_hh_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRobotRequiredStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rtc_hh_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rtc_hh_proto_goTypes,
		DependencyIndexes: file_proto_rtc_hh_proto_depIdxs,
		EnumInfos:         file_proto_rtc_hh_proto_enumTypes,
		MessageInfos:      file_proto_rtc_hh_proto_msgTypes,
	}.Build()
	File_proto_rtc_hh_proto = out.File
	file_proto_rtc_hh_proto_rawDesc = nil
	file_proto_rtc_hh_proto_goTypes = nil
	file_proto_rtc_hh_proto_depIdxs = nil
}

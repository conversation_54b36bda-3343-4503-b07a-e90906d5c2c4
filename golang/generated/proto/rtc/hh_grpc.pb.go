// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/rtc/hh.proto

package rtc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RobotState_SetRobotRequiredState_FullMethodName  = "/carbon.rtc.RobotState/SetRobotRequiredState"
	RobotState_GetNextRequiredState_FullMethodName   = "/carbon.rtc.RobotState/GetNextRequiredState"
	RobotState_RobotRequirementStream_FullMethodName = "/carbon.rtc.RobotState/RobotRequirementStream"
)

// RobotStateClient is the client API for RobotState service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RobotStateClient interface {
	SetRobotRequiredState(ctx context.Context, in *SetRobotRequiredStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetNextRequiredState(ctx context.Context, in *GetRobotRequiredStateRequest, opts ...grpc.CallOption) (*GetRobotRequiredStateResponse, error)
	RobotRequirementStream(ctx context.Context, opts ...grpc.CallOption) (RobotState_RobotRequirementStreamClient, error)
}

type robotStateClient struct {
	cc grpc.ClientConnInterface
}

func NewRobotStateClient(cc grpc.ClientConnInterface) RobotStateClient {
	return &robotStateClient{cc}
}

func (c *robotStateClient) SetRobotRequiredState(ctx context.Context, in *SetRobotRequiredStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, RobotState_SetRobotRequiredState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *robotStateClient) GetNextRequiredState(ctx context.Context, in *GetRobotRequiredStateRequest, opts ...grpc.CallOption) (*GetRobotRequiredStateResponse, error) {
	out := new(GetRobotRequiredStateResponse)
	err := c.cc.Invoke(ctx, RobotState_GetNextRequiredState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *robotStateClient) RobotRequirementStream(ctx context.Context, opts ...grpc.CallOption) (RobotState_RobotRequirementStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &RobotState_ServiceDesc.Streams[0], RobotState_RobotRequirementStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &robotStateRobotRequirementStreamClient{stream}
	return x, nil
}

type RobotState_RobotRequirementStreamClient interface {
	Send(*RobotStatusInfo) error
	Recv() (*RobotRequiredState, error)
	grpc.ClientStream
}

type robotStateRobotRequirementStreamClient struct {
	grpc.ClientStream
}

func (x *robotStateRobotRequirementStreamClient) Send(m *RobotStatusInfo) error {
	return x.ClientStream.SendMsg(m)
}

func (x *robotStateRobotRequirementStreamClient) Recv() (*RobotRequiredState, error) {
	m := new(RobotRequiredState)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RobotStateServer is the server API for RobotState service.
// All implementations must embed UnimplementedRobotStateServer
// for forward compatibility
type RobotStateServer interface {
	SetRobotRequiredState(context.Context, *SetRobotRequiredStateRequest) (*emptypb.Empty, error)
	GetNextRequiredState(context.Context, *GetRobotRequiredStateRequest) (*GetRobotRequiredStateResponse, error)
	RobotRequirementStream(RobotState_RobotRequirementStreamServer) error
	mustEmbedUnimplementedRobotStateServer()
}

// UnimplementedRobotStateServer must be embedded to have forward compatible implementations.
type UnimplementedRobotStateServer struct {
}

func (UnimplementedRobotStateServer) SetRobotRequiredState(context.Context, *SetRobotRequiredStateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRobotRequiredState not implemented")
}
func (UnimplementedRobotStateServer) GetNextRequiredState(context.Context, *GetRobotRequiredStateRequest) (*GetRobotRequiredStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextRequiredState not implemented")
}
func (UnimplementedRobotStateServer) RobotRequirementStream(RobotState_RobotRequirementStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method RobotRequirementStream not implemented")
}
func (UnimplementedRobotStateServer) mustEmbedUnimplementedRobotStateServer() {}

// UnsafeRobotStateServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RobotStateServer will
// result in compilation errors.
type UnsafeRobotStateServer interface {
	mustEmbedUnimplementedRobotStateServer()
}

func RegisterRobotStateServer(s grpc.ServiceRegistrar, srv RobotStateServer) {
	s.RegisterService(&RobotState_ServiceDesc, srv)
}

func _RobotState_SetRobotRequiredState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRobotRequiredStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotStateServer).SetRobotRequiredState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RobotState_SetRobotRequiredState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotStateServer).SetRobotRequiredState(ctx, req.(*SetRobotRequiredStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RobotState_GetNextRequiredState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRobotRequiredStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotStateServer).GetNextRequiredState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RobotState_GetNextRequiredState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotStateServer).GetNextRequiredState(ctx, req.(*GetRobotRequiredStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RobotState_RobotRequirementStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RobotStateServer).RobotRequirementStream(&robotStateRobotRequirementStreamServer{stream})
}

type RobotState_RobotRequirementStreamServer interface {
	Send(*RobotRequiredState) error
	Recv() (*RobotStatusInfo, error)
	grpc.ServerStream
}

type robotStateRobotRequirementStreamServer struct {
	grpc.ServerStream
}

func (x *robotStateRobotRequirementStreamServer) Send(m *RobotRequiredState) error {
	return x.ServerStream.SendMsg(m)
}

func (x *robotStateRobotRequirementStreamServer) Recv() (*RobotStatusInfo, error) {
	m := new(RobotStatusInfo)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RobotState_ServiceDesc is the grpc.ServiceDesc for RobotState service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RobotState_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.rtc.RobotState",
	HandlerType: (*RobotStateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetRobotRequiredState",
			Handler:    _RobotState_SetRobotRequiredState_Handler,
		},
		{
			MethodName: "GetNextRequiredState",
			Handler:    _RobotState_GetNextRequiredState_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "RobotRequirementStream",
			Handler:       _RobotState_RobotRequirementStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "proto/rtc/hh.proto",
}

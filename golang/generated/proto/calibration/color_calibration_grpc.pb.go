// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: calibration/proto/color_calibration.proto

package calibration

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ColorCalibrationService_InitializeColorCalibration_FullMethodName = "/calibration.proto.ColorCalibrationService/InitializeColorCalibration"
	ColorCalibrationService_CenterTargetCameras_FullMethodName        = "/calibration.proto.ColorCalibrationService/CenterTargetCameras"
	ColorCalibrationService_SetAutoWhitebalance_FullMethodName        = "/calibration.proto.ColorCalibrationService/SetAutoWhitebalance"
	ColorCalibrationService_SaveToConfig_FullMethodName               = "/calibration.proto.ColorCalibrationService/SaveToConfig"
	ColorCalibrationService_Reset_FullMethodName                      = "/calibration.proto.ColorCalibrationService/Reset"
)

// ColorCalibrationServiceClient is the client API for ColorCalibrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ColorCalibrationServiceClient interface {
	InitializeColorCalibration(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	CenterTargetCameras(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	SetAutoWhitebalance(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	SaveToConfig(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	Reset(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Response, error)
}

type colorCalibrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewColorCalibrationServiceClient(cc grpc.ClientConnInterface) ColorCalibrationServiceClient {
	return &colorCalibrationServiceClient{cc}
}

func (c *colorCalibrationServiceClient) InitializeColorCalibration(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, ColorCalibrationService_InitializeColorCalibration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *colorCalibrationServiceClient) CenterTargetCameras(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, ColorCalibrationService_CenterTargetCameras_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *colorCalibrationServiceClient) SetAutoWhitebalance(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, ColorCalibrationService_SetAutoWhitebalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *colorCalibrationServiceClient) SaveToConfig(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, ColorCalibrationService_SaveToConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *colorCalibrationServiceClient) Reset(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, ColorCalibrationService_Reset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ColorCalibrationServiceServer is the server API for ColorCalibrationService service.
// All implementations must embed UnimplementedColorCalibrationServiceServer
// for forward compatibility
type ColorCalibrationServiceServer interface {
	InitializeColorCalibration(context.Context, *Request) (*Response, error)
	CenterTargetCameras(context.Context, *Request) (*Response, error)
	SetAutoWhitebalance(context.Context, *Request) (*Response, error)
	SaveToConfig(context.Context, *Request) (*Response, error)
	Reset(context.Context, *Empty) (*Response, error)
	mustEmbedUnimplementedColorCalibrationServiceServer()
}

// UnimplementedColorCalibrationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedColorCalibrationServiceServer struct {
}

func (UnimplementedColorCalibrationServiceServer) InitializeColorCalibration(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitializeColorCalibration not implemented")
}
func (UnimplementedColorCalibrationServiceServer) CenterTargetCameras(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CenterTargetCameras not implemented")
}
func (UnimplementedColorCalibrationServiceServer) SetAutoWhitebalance(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAutoWhitebalance not implemented")
}
func (UnimplementedColorCalibrationServiceServer) SaveToConfig(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveToConfig not implemented")
}
func (UnimplementedColorCalibrationServiceServer) Reset(context.Context, *Empty) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Reset not implemented")
}
func (UnimplementedColorCalibrationServiceServer) mustEmbedUnimplementedColorCalibrationServiceServer() {
}

// UnsafeColorCalibrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ColorCalibrationServiceServer will
// result in compilation errors.
type UnsafeColorCalibrationServiceServer interface {
	mustEmbedUnimplementedColorCalibrationServiceServer()
}

func RegisterColorCalibrationServiceServer(s grpc.ServiceRegistrar, srv ColorCalibrationServiceServer) {
	s.RegisterService(&ColorCalibrationService_ServiceDesc, srv)
}

func _ColorCalibrationService_InitializeColorCalibration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ColorCalibrationServiceServer).InitializeColorCalibration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ColorCalibrationService_InitializeColorCalibration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ColorCalibrationServiceServer).InitializeColorCalibration(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ColorCalibrationService_CenterTargetCameras_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ColorCalibrationServiceServer).CenterTargetCameras(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ColorCalibrationService_CenterTargetCameras_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ColorCalibrationServiceServer).CenterTargetCameras(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ColorCalibrationService_SetAutoWhitebalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ColorCalibrationServiceServer).SetAutoWhitebalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ColorCalibrationService_SetAutoWhitebalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ColorCalibrationServiceServer).SetAutoWhitebalance(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ColorCalibrationService_SaveToConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ColorCalibrationServiceServer).SaveToConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ColorCalibrationService_SaveToConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ColorCalibrationServiceServer).SaveToConfig(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ColorCalibrationService_Reset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ColorCalibrationServiceServer).Reset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ColorCalibrationService_Reset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ColorCalibrationServiceServer).Reset(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ColorCalibrationService_ServiceDesc is the grpc.ServiceDesc for ColorCalibrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ColorCalibrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "calibration.proto.ColorCalibrationService",
	HandlerType: (*ColorCalibrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitializeColorCalibration",
			Handler:    _ColorCalibrationService_InitializeColorCalibration_Handler,
		},
		{
			MethodName: "CenterTargetCameras",
			Handler:    _ColorCalibrationService_CenterTargetCameras_Handler,
		},
		{
			MethodName: "SetAutoWhitebalance",
			Handler:    _ColorCalibrationService_SetAutoWhitebalance_Handler,
		},
		{
			MethodName: "SaveToConfig",
			Handler:    _ColorCalibrationService_SaveToConfig_Handler,
		},
		{
			MethodName: "Reset",
			Handler:    _ColorCalibrationService_Reset_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "calibration/proto/color_calibration.proto",
}

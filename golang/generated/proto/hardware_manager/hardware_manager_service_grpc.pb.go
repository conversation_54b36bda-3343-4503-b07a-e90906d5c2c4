// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: hardware_manager/proto/hardware_manager_service.proto

package hardware_manager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HardwareManagerService_Ping_FullMethodName                        = "/hardware_manager.HardwareManagerService/Ping"
	HardwareManagerService_GetReady_FullMethodName                    = "/hardware_manager.HardwareManagerService/GetReady"
	HardwareManagerService_GetNextDistance_FullMethodName             = "/hardware_manager.HardwareManagerService/GetNextDistance"
	HardwareManagerService_GetNextVelocity_FullMethodName             = "/hardware_manager.HardwareManagerService/GetNextVelocity"
	HardwareManagerService_GetRotaryTicks_FullMethodName              = "/hardware_manager.HardwareManagerService/GetRotaryTicks"
	HardwareManagerService_GetDeltaTravelMM_FullMethodName            = "/hardware_manager.HardwareManagerService/GetDeltaTravelMM"
	HardwareManagerService_GetWheelEncoderResolution_FullMethodName   = "/hardware_manager.HardwareManagerService/GetWheelEncoderResolution"
	HardwareManagerService_GetSafetyStatus_FullMethodName             = "/hardware_manager.HardwareManagerService/GetSafetyStatus"
	HardwareManagerService_GetGPSData_FullMethodName                  = "/hardware_manager.HardwareManagerService/GetGPSData"
	HardwareManagerService_GetNextGPSData_FullMethodName              = "/hardware_manager.HardwareManagerService/GetNextGPSData"
	HardwareManagerService_GetNextRawGPSData_FullMethodName           = "/hardware_manager.HardwareManagerService/GetNextRawGPSData"
	HardwareManagerService_GetGPSFixedPos_FullMethodName              = "/hardware_manager.HardwareManagerService/GetGPSFixedPos"
	HardwareManagerService_SetStrobeSettings_FullMethodName           = "/hardware_manager.HardwareManagerService/SetStrobeSettings"
	HardwareManagerService_GetStrobeSettings_FullMethodName           = "/hardware_manager.HardwareManagerService/GetStrobeSettings"
	HardwareManagerService_GetManagedBoardErrors_FullMethodName       = "/hardware_manager.HardwareManagerService/GetManagedBoardErrors"
	HardwareManagerService_GetSupervisoryStatus_FullMethodName        = "/hardware_manager.HardwareManagerService/GetSupervisoryStatus"
	HardwareManagerService_GetReaperSupervisoryStatus_FullMethodName  = "/hardware_manager.HardwareManagerService/GetReaperSupervisoryStatus"
	HardwareManagerService_SetServerDisable_FullMethodName            = "/hardware_manager.HardwareManagerService/SetServerDisable"
	HardwareManagerService_SetBTLDisable_FullMethodName               = "/hardware_manager.HardwareManagerService/SetBTLDisable"
	HardwareManagerService_SetScannersDisable_FullMethodName          = "/hardware_manager.HardwareManagerService/SetScannersDisable"
	HardwareManagerService_SetWheelEncoderBoardDisable_FullMethodName = "/hardware_manager.HardwareManagerService/SetWheelEncoderBoardDisable"
	HardwareManagerService_SetWheelEncoderDisable_FullMethodName      = "/hardware_manager.HardwareManagerService/SetWheelEncoderDisable"
	HardwareManagerService_SetGPSDisable_FullMethodName               = "/hardware_manager.HardwareManagerService/SetGPSDisable"
	HardwareManagerService_SuicideSwitch_FullMethodName               = "/hardware_manager.HardwareManagerService/SuicideSwitch"
	HardwareManagerService_CommandComputerPowerCycle_FullMethodName   = "/hardware_manager.HardwareManagerService/CommandComputerPowerCycle"
	HardwareManagerService_SetMainContactorDisable_FullMethodName     = "/hardware_manager.HardwareManagerService/SetMainContactorDisable"
	HardwareManagerService_SetStrobeDisable_FullMethodName            = "/hardware_manager.HardwareManagerService/SetStrobeDisable"
	HardwareManagerService_SetAirConditionerDisable_FullMethodName    = "/hardware_manager.HardwareManagerService/SetAirConditionerDisable"
	HardwareManagerService_SetChillerDisable_FullMethodName           = "/hardware_manager.HardwareManagerService/SetChillerDisable"
	HardwareManagerService_SetTempBypassDisable_FullMethodName        = "/hardware_manager.HardwareManagerService/SetTempBypassDisable"
	HardwareManagerService_SetHumidityBypassDisable_FullMethodName    = "/hardware_manager.HardwareManagerService/SetHumidityBypassDisable"
	HardwareManagerService_Get240VUptime_FullMethodName               = "/hardware_manager.HardwareManagerService/Get240vUptime"
	HardwareManagerService_GetRuntime_FullMethodName                  = "/hardware_manager.HardwareManagerService/GetRuntime"
	HardwareManagerService_GetAvailableUSBStorage_FullMethodName      = "/hardware_manager.HardwareManagerService/GetAvailableUSBStorage"
	HardwareManagerService_SetJimboxSpeed_FullMethodName              = "/hardware_manager.HardwareManagerService/SetJimboxSpeed"
	HardwareManagerService_SetCruiseEnabled_FullMethodName            = "/hardware_manager.HardwareManagerService/SetCruiseEnabled"
	HardwareManagerService_GetCruiseStatus_FullMethodName             = "/hardware_manager.HardwareManagerService/GetCruiseStatus"
	HardwareManagerService_SetImplementStateOnTractor_FullMethodName  = "/hardware_manager.HardwareManagerService/SetImplementStateOnTractor"
	HardwareManagerService_SetSafeStateEnforcement_FullMethodName     = "/hardware_manager.HardwareManagerService/SetSafeStateEnforcement"
	HardwareManagerService_GetTractorSafetyState_FullMethodName       = "/hardware_manager.HardwareManagerService/GetTractorSafetyState"
	HardwareManagerService_GetTractorIFState_FullMethodName           = "/hardware_manager.HardwareManagerService/GetTractorIFState"
	HardwareManagerService_GetReaperEnclosureSensors_FullMethodName   = "/hardware_manager.HardwareManagerService/GetReaperEnclosureSensors"
	HardwareManagerService_GetReaperModuleSensors_FullMethodName      = "/hardware_manager.HardwareManagerService/GetReaperModuleSensors"
	HardwareManagerService_SetReaperScannerPower_FullMethodName       = "/hardware_manager.HardwareManagerService/SetReaperScannerPower"
	HardwareManagerService_SetReaperTargetPower_FullMethodName        = "/hardware_manager.HardwareManagerService/SetReaperTargetPower"
	HardwareManagerService_SetReaperPredictCamPower_FullMethodName    = "/hardware_manager.HardwareManagerService/SetReaperPredictCamPower"
	HardwareManagerService_SetReaperStrobeConfig_FullMethodName       = "/hardware_manager.HardwareManagerService/SetReaperStrobeConfig"
	HardwareManagerService_SetReaperStrobeEnable_FullMethodName       = "/hardware_manager.HardwareManagerService/SetReaperStrobeEnable"
	HardwareManagerService_SetReaperModulePcPower_FullMethodName      = "/hardware_manager.HardwareManagerService/SetReaperModulePcPower"
	HardwareManagerService_SetReaperModuleLaserPower_FullMethodName   = "/hardware_manager.HardwareManagerService/SetReaperModuleLaserPower"
	HardwareManagerService_SetReaperModuleStrobePower_FullMethodName  = "/hardware_manager.HardwareManagerService/SetReaperModuleStrobePower"
	HardwareManagerService_IdentifyModule_FullMethodName              = "/hardware_manager.HardwareManagerService/IdentifyModule"
)

// HardwareManagerServiceClient is the client API for HardwareManagerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HardwareManagerServiceClient interface {
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error)
	GetReady(ctx context.Context, in *GetReadyRequest, opts ...grpc.CallOption) (*GetReadyResponse, error)
	// Rotary
	GetNextDistance(ctx context.Context, in *GetNextDistanceRequest, opts ...grpc.CallOption) (*GetNextDistanceResponse, error)
	GetNextVelocity(ctx context.Context, in *GetNextVelocityRequest, opts ...grpc.CallOption) (*GetNextVelocityResponse, error)
	GetRotaryTicks(ctx context.Context, in *GetRotaryTicksRequest, opts ...grpc.CallOption) (*GetRotaryTicksResponse, error)
	GetDeltaTravelMM(ctx context.Context, in *GetDeltaTravelMMRequest, opts ...grpc.CallOption) (*GetDeltaTravelMMResponse, error)
	GetWheelEncoderResolution(ctx context.Context, in *GetWheelEncoderResolutionRequest, opts ...grpc.CallOption) (*GetWheelEncoderResolutionResponse, error)
	// Safety
	GetSafetyStatus(ctx context.Context, in *GetSafetyStatusRequest, opts ...grpc.CallOption) (*GetSafetyStatusResponse, error)
	// GPS
	GetGPSData(ctx context.Context, in *GetGPSDataRequest, opts ...grpc.CallOption) (*GetGPSDataResponse, error)
	GetNextGPSData(ctx context.Context, in *GetNextGPSDataRequest, opts ...grpc.CallOption) (*GetNextGPSDataResponse, error)
	GetNextRawGPSData(ctx context.Context, in *GetNextRawGPSDataRequest, opts ...grpc.CallOption) (*GetNextRawGPSDataResponse, error)
	GetGPSFixedPos(ctx context.Context, in *GetGPSFixedPosRequest, opts ...grpc.CallOption) (*GetGPSFixedPosResponse, error)
	// Strobe
	SetStrobeSettings(ctx context.Context, in *StrobeSettings, opts ...grpc.CallOption) (*SetStrobeSettingsResponse, error)
	GetStrobeSettings(ctx context.Context, in *GetStrobeSettingsRequest, opts ...grpc.CallOption) (*StrobeSettings, error)
	// Managed Boards
	GetManagedBoardErrors(ctx context.Context, in *GetManagedBoardErrorsRequest, opts ...grpc.CallOption) (*GetManagedBoardErrorsResponse, error)
	// Supervisory PLC
	GetSupervisoryStatus(ctx context.Context, in *GetSupervisoryStatusRequest, opts ...grpc.CallOption) (*GetSupervisoryStatusResponse, error)
	GetReaperSupervisoryStatus(ctx context.Context, in *GetReaperSupervisoryStatusRequest, opts ...grpc.CallOption) (*ReaperCenterEnclosureData, error)
	SetServerDisable(ctx context.Context, in *SetServerDisableRequest, opts ...grpc.CallOption) (*SetServerDisableResponse, error)
	SetBTLDisable(ctx context.Context, in *SetBTLDisableRequest, opts ...grpc.CallOption) (*SetBTLDisableResponse, error)
	SetScannersDisable(ctx context.Context, in *SetScannersDisableRequest, opts ...grpc.CallOption) (*SetScannersDisableResponse, error)
	SetWheelEncoderBoardDisable(ctx context.Context, in *SetWheelEncoderBoardDisableRequest, opts ...grpc.CallOption) (*SetWheelEncoderBoardDisableResponse, error)
	SetWheelEncoderDisable(ctx context.Context, in *SetWheelEncoderDisableRequest, opts ...grpc.CallOption) (*SetWheelEncoderDisableResponse, error)
	SetGPSDisable(ctx context.Context, in *SetGPSDisableRequest, opts ...grpc.CallOption) (*SetGPSDisableResponse, error)
	SuicideSwitch(ctx context.Context, in *SuicideSwitchRequest, opts ...grpc.CallOption) (*SuicideSwitchResponse, error)
	CommandComputerPowerCycle(ctx context.Context, in *CommandComputerPowerCycleRequest, opts ...grpc.CallOption) (*CommandComputerPowerCycleResponse, error)
	SetMainContactorDisable(ctx context.Context, in *SetMainContactorDisableRequest, opts ...grpc.CallOption) (*SetMainContactorDisableResponse, error)
	SetStrobeDisable(ctx context.Context, in *SetStrobeDisableRequest, opts ...grpc.CallOption) (*SetStrobeDisableResponse, error)
	SetAirConditionerDisable(ctx context.Context, in *SetAirConditionerDisableRequest, opts ...grpc.CallOption) (*SetAirConditionerDisableResponse, error)
	SetChillerDisable(ctx context.Context, in *SetChillerDisableRequest, opts ...grpc.CallOption) (*SetChillerDisableResponse, error)
	SetTempBypassDisable(ctx context.Context, in *SetTempBypassDisableRequest, opts ...grpc.CallOption) (*SetTempBypassDisableResponse, error)
	SetHumidityBypassDisable(ctx context.Context, in *SetHumidityBypassDisableRequest, opts ...grpc.CallOption) (*SetHumidityBypassDisableResponse, error)
	Get240VUptime(ctx context.Context, in *Get240VUptimeRequest, opts ...grpc.CallOption) (*Get240VUptimeResponse, error)
	GetRuntime(ctx context.Context, in *GetRuntimeRequest, opts ...grpc.CallOption) (*GetRuntimeResponse, error)
	// USB
	GetAvailableUSBStorage(ctx context.Context, in *GetAvailableUSBStorageRequest, opts ...grpc.CallOption) (*GetAvailableUSBStorageResponse, error)
	// Jimbox
	SetJimboxSpeed(ctx context.Context, in *SetJimboxSpeedRequest, opts ...grpc.CallOption) (*SetJimboxSpeedResponse, error)
	SetCruiseEnabled(ctx context.Context, in *SetCruiseEnabledRequest, opts ...grpc.CallOption) (*SetCruiseEnabledResponse, error)
	GetCruiseStatus(ctx context.Context, in *GetCruiseStatusRequest, opts ...grpc.CallOption) (*GetCruiseStatusResponse, error)
	// Tractor
	SetImplementStateOnTractor(ctx context.Context, in *SetImplementStateRequest, opts ...grpc.CallOption) (*SetImplementStateResponse, error)
	SetSafeStateEnforcement(ctx context.Context, in *SetSafeStateEnforcementRequest, opts ...grpc.CallOption) (*SetSafeStateEnforcementResponse, error)
	GetTractorSafetyState(ctx context.Context, in *GetTractorSafetyStateRequest, opts ...grpc.CallOption) (*GetTractorSafetyStateResponse, error)
	GetTractorIFState(ctx context.Context, in *GetTractorIFStateRequest, opts ...grpc.CallOption) (*GetTractorIFStateResponse, error)
	// Hardware/board status
	GetReaperEnclosureSensors(ctx context.Context, in *GetReaperEnclosureSensorsRequest, opts ...grpc.CallOption) (*GetReaperEnclosureSensorsResponse, error)
	GetReaperModuleSensors(ctx context.Context, in *GetReaperModuleSensorsRequest, opts ...grpc.CallOption) (*GetReaperModuleSensorsResponse, error)
	SetReaperScannerPower(ctx context.Context, in *SetReaperScannerPowerRequest, opts ...grpc.CallOption) (*SetReaperScannerPowerResponse, error)
	SetReaperTargetPower(ctx context.Context, in *SetReaperTargetPowerRequest, opts ...grpc.CallOption) (*SetReaperTargetPowerResponse, error)
	SetReaperPredictCamPower(ctx context.Context, in *SetReaperPredictCamPowerRequest, opts ...grpc.CallOption) (*SetReaperPredictCamPowerResponse, error)
	SetReaperStrobeConfig(ctx context.Context, in *SetReaperStrobeConfigRequest, opts ...grpc.CallOption) (*SetReaperStrobeConfigResponse, error)
	SetReaperStrobeEnable(ctx context.Context, in *SetReaperStrobeEnableRequest, opts ...grpc.CallOption) (*SetReaperStrobeEnableResponse, error)
	SetReaperModulePcPower(ctx context.Context, in *SetReaperModulePcPowerRequest, opts ...grpc.CallOption) (*SetReaperModulePcPowerResponse, error)
	SetReaperModuleLaserPower(ctx context.Context, in *SetReaperModuleLaserPowerRequest, opts ...grpc.CallOption) (*SetReaperModuleLaserPowerResponse, error)
	SetReaperModuleStrobePower(ctx context.Context, in *SetReaperModuleStrobePowerRequest, opts ...grpc.CallOption) (*SetReaperModuleStrobePowerResponse, error)
	IdentifyModule(ctx context.Context, in *IdentifyModuleRequest, opts ...grpc.CallOption) (*IdentifyModuleResponse, error)
}

type hardwareManagerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHardwareManagerServiceClient(cc grpc.ClientConnInterface) HardwareManagerServiceClient {
	return &hardwareManagerServiceClient{cc}
}

func (c *hardwareManagerServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error) {
	out := new(PingResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetReady(ctx context.Context, in *GetReadyRequest, opts ...grpc.CallOption) (*GetReadyResponse, error) {
	out := new(GetReadyResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetReady_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetNextDistance(ctx context.Context, in *GetNextDistanceRequest, opts ...grpc.CallOption) (*GetNextDistanceResponse, error) {
	out := new(GetNextDistanceResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetNextDistance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetNextVelocity(ctx context.Context, in *GetNextVelocityRequest, opts ...grpc.CallOption) (*GetNextVelocityResponse, error) {
	out := new(GetNextVelocityResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetNextVelocity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetRotaryTicks(ctx context.Context, in *GetRotaryTicksRequest, opts ...grpc.CallOption) (*GetRotaryTicksResponse, error) {
	out := new(GetRotaryTicksResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetRotaryTicks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetDeltaTravelMM(ctx context.Context, in *GetDeltaTravelMMRequest, opts ...grpc.CallOption) (*GetDeltaTravelMMResponse, error) {
	out := new(GetDeltaTravelMMResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetDeltaTravelMM_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetWheelEncoderResolution(ctx context.Context, in *GetWheelEncoderResolutionRequest, opts ...grpc.CallOption) (*GetWheelEncoderResolutionResponse, error) {
	out := new(GetWheelEncoderResolutionResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetWheelEncoderResolution_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetSafetyStatus(ctx context.Context, in *GetSafetyStatusRequest, opts ...grpc.CallOption) (*GetSafetyStatusResponse, error) {
	out := new(GetSafetyStatusResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetSafetyStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetGPSData(ctx context.Context, in *GetGPSDataRequest, opts ...grpc.CallOption) (*GetGPSDataResponse, error) {
	out := new(GetGPSDataResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetGPSData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetNextGPSData(ctx context.Context, in *GetNextGPSDataRequest, opts ...grpc.CallOption) (*GetNextGPSDataResponse, error) {
	out := new(GetNextGPSDataResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetNextGPSData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetNextRawGPSData(ctx context.Context, in *GetNextRawGPSDataRequest, opts ...grpc.CallOption) (*GetNextRawGPSDataResponse, error) {
	out := new(GetNextRawGPSDataResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetNextRawGPSData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetGPSFixedPos(ctx context.Context, in *GetGPSFixedPosRequest, opts ...grpc.CallOption) (*GetGPSFixedPosResponse, error) {
	out := new(GetGPSFixedPosResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetGPSFixedPos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetStrobeSettings(ctx context.Context, in *StrobeSettings, opts ...grpc.CallOption) (*SetStrobeSettingsResponse, error) {
	out := new(SetStrobeSettingsResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetStrobeSettings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetStrobeSettings(ctx context.Context, in *GetStrobeSettingsRequest, opts ...grpc.CallOption) (*StrobeSettings, error) {
	out := new(StrobeSettings)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetStrobeSettings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetManagedBoardErrors(ctx context.Context, in *GetManagedBoardErrorsRequest, opts ...grpc.CallOption) (*GetManagedBoardErrorsResponse, error) {
	out := new(GetManagedBoardErrorsResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetManagedBoardErrors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetSupervisoryStatus(ctx context.Context, in *GetSupervisoryStatusRequest, opts ...grpc.CallOption) (*GetSupervisoryStatusResponse, error) {
	out := new(GetSupervisoryStatusResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetSupervisoryStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetReaperSupervisoryStatus(ctx context.Context, in *GetReaperSupervisoryStatusRequest, opts ...grpc.CallOption) (*ReaperCenterEnclosureData, error) {
	out := new(ReaperCenterEnclosureData)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetReaperSupervisoryStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetServerDisable(ctx context.Context, in *SetServerDisableRequest, opts ...grpc.CallOption) (*SetServerDisableResponse, error) {
	out := new(SetServerDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetServerDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetBTLDisable(ctx context.Context, in *SetBTLDisableRequest, opts ...grpc.CallOption) (*SetBTLDisableResponse, error) {
	out := new(SetBTLDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetBTLDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetScannersDisable(ctx context.Context, in *SetScannersDisableRequest, opts ...grpc.CallOption) (*SetScannersDisableResponse, error) {
	out := new(SetScannersDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetScannersDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetWheelEncoderBoardDisable(ctx context.Context, in *SetWheelEncoderBoardDisableRequest, opts ...grpc.CallOption) (*SetWheelEncoderBoardDisableResponse, error) {
	out := new(SetWheelEncoderBoardDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetWheelEncoderBoardDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetWheelEncoderDisable(ctx context.Context, in *SetWheelEncoderDisableRequest, opts ...grpc.CallOption) (*SetWheelEncoderDisableResponse, error) {
	out := new(SetWheelEncoderDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetWheelEncoderDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetGPSDisable(ctx context.Context, in *SetGPSDisableRequest, opts ...grpc.CallOption) (*SetGPSDisableResponse, error) {
	out := new(SetGPSDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetGPSDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SuicideSwitch(ctx context.Context, in *SuicideSwitchRequest, opts ...grpc.CallOption) (*SuicideSwitchResponse, error) {
	out := new(SuicideSwitchResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SuicideSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) CommandComputerPowerCycle(ctx context.Context, in *CommandComputerPowerCycleRequest, opts ...grpc.CallOption) (*CommandComputerPowerCycleResponse, error) {
	out := new(CommandComputerPowerCycleResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_CommandComputerPowerCycle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetMainContactorDisable(ctx context.Context, in *SetMainContactorDisableRequest, opts ...grpc.CallOption) (*SetMainContactorDisableResponse, error) {
	out := new(SetMainContactorDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetMainContactorDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetStrobeDisable(ctx context.Context, in *SetStrobeDisableRequest, opts ...grpc.CallOption) (*SetStrobeDisableResponse, error) {
	out := new(SetStrobeDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetStrobeDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetAirConditionerDisable(ctx context.Context, in *SetAirConditionerDisableRequest, opts ...grpc.CallOption) (*SetAirConditionerDisableResponse, error) {
	out := new(SetAirConditionerDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetAirConditionerDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetChillerDisable(ctx context.Context, in *SetChillerDisableRequest, opts ...grpc.CallOption) (*SetChillerDisableResponse, error) {
	out := new(SetChillerDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetChillerDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetTempBypassDisable(ctx context.Context, in *SetTempBypassDisableRequest, opts ...grpc.CallOption) (*SetTempBypassDisableResponse, error) {
	out := new(SetTempBypassDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetTempBypassDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetHumidityBypassDisable(ctx context.Context, in *SetHumidityBypassDisableRequest, opts ...grpc.CallOption) (*SetHumidityBypassDisableResponse, error) {
	out := new(SetHumidityBypassDisableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetHumidityBypassDisable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) Get240VUptime(ctx context.Context, in *Get240VUptimeRequest, opts ...grpc.CallOption) (*Get240VUptimeResponse, error) {
	out := new(Get240VUptimeResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_Get240VUptime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetRuntime(ctx context.Context, in *GetRuntimeRequest, opts ...grpc.CallOption) (*GetRuntimeResponse, error) {
	out := new(GetRuntimeResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetRuntime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetAvailableUSBStorage(ctx context.Context, in *GetAvailableUSBStorageRequest, opts ...grpc.CallOption) (*GetAvailableUSBStorageResponse, error) {
	out := new(GetAvailableUSBStorageResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetAvailableUSBStorage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetJimboxSpeed(ctx context.Context, in *SetJimboxSpeedRequest, opts ...grpc.CallOption) (*SetJimboxSpeedResponse, error) {
	out := new(SetJimboxSpeedResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetJimboxSpeed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetCruiseEnabled(ctx context.Context, in *SetCruiseEnabledRequest, opts ...grpc.CallOption) (*SetCruiseEnabledResponse, error) {
	out := new(SetCruiseEnabledResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetCruiseEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetCruiseStatus(ctx context.Context, in *GetCruiseStatusRequest, opts ...grpc.CallOption) (*GetCruiseStatusResponse, error) {
	out := new(GetCruiseStatusResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetCruiseStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetImplementStateOnTractor(ctx context.Context, in *SetImplementStateRequest, opts ...grpc.CallOption) (*SetImplementStateResponse, error) {
	out := new(SetImplementStateResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetImplementStateOnTractor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetSafeStateEnforcement(ctx context.Context, in *SetSafeStateEnforcementRequest, opts ...grpc.CallOption) (*SetSafeStateEnforcementResponse, error) {
	out := new(SetSafeStateEnforcementResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetSafeStateEnforcement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetTractorSafetyState(ctx context.Context, in *GetTractorSafetyStateRequest, opts ...grpc.CallOption) (*GetTractorSafetyStateResponse, error) {
	out := new(GetTractorSafetyStateResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetTractorSafetyState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetTractorIFState(ctx context.Context, in *GetTractorIFStateRequest, opts ...grpc.CallOption) (*GetTractorIFStateResponse, error) {
	out := new(GetTractorIFStateResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetTractorIFState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetReaperEnclosureSensors(ctx context.Context, in *GetReaperEnclosureSensorsRequest, opts ...grpc.CallOption) (*GetReaperEnclosureSensorsResponse, error) {
	out := new(GetReaperEnclosureSensorsResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetReaperEnclosureSensors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) GetReaperModuleSensors(ctx context.Context, in *GetReaperModuleSensorsRequest, opts ...grpc.CallOption) (*GetReaperModuleSensorsResponse, error) {
	out := new(GetReaperModuleSensorsResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_GetReaperModuleSensors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperScannerPower(ctx context.Context, in *SetReaperScannerPowerRequest, opts ...grpc.CallOption) (*SetReaperScannerPowerResponse, error) {
	out := new(SetReaperScannerPowerResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperScannerPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperTargetPower(ctx context.Context, in *SetReaperTargetPowerRequest, opts ...grpc.CallOption) (*SetReaperTargetPowerResponse, error) {
	out := new(SetReaperTargetPowerResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperTargetPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperPredictCamPower(ctx context.Context, in *SetReaperPredictCamPowerRequest, opts ...grpc.CallOption) (*SetReaperPredictCamPowerResponse, error) {
	out := new(SetReaperPredictCamPowerResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperPredictCamPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperStrobeConfig(ctx context.Context, in *SetReaperStrobeConfigRequest, opts ...grpc.CallOption) (*SetReaperStrobeConfigResponse, error) {
	out := new(SetReaperStrobeConfigResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperStrobeConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperStrobeEnable(ctx context.Context, in *SetReaperStrobeEnableRequest, opts ...grpc.CallOption) (*SetReaperStrobeEnableResponse, error) {
	out := new(SetReaperStrobeEnableResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperStrobeEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperModulePcPower(ctx context.Context, in *SetReaperModulePcPowerRequest, opts ...grpc.CallOption) (*SetReaperModulePcPowerResponse, error) {
	out := new(SetReaperModulePcPowerResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperModulePcPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperModuleLaserPower(ctx context.Context, in *SetReaperModuleLaserPowerRequest, opts ...grpc.CallOption) (*SetReaperModuleLaserPowerResponse, error) {
	out := new(SetReaperModuleLaserPowerResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperModuleLaserPower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) SetReaperModuleStrobePower(ctx context.Context, in *SetReaperModuleStrobePowerRequest, opts ...grpc.CallOption) (*SetReaperModuleStrobePowerResponse, error) {
	out := new(SetReaperModuleStrobePowerResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_SetReaperModuleStrobePower_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hardwareManagerServiceClient) IdentifyModule(ctx context.Context, in *IdentifyModuleRequest, opts ...grpc.CallOption) (*IdentifyModuleResponse, error) {
	out := new(IdentifyModuleResponse)
	err := c.cc.Invoke(ctx, HardwareManagerService_IdentifyModule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HardwareManagerServiceServer is the server API for HardwareManagerService service.
// All implementations must embed UnimplementedHardwareManagerServiceServer
// for forward compatibility
type HardwareManagerServiceServer interface {
	Ping(context.Context, *PingRequest) (*PingResponse, error)
	GetReady(context.Context, *GetReadyRequest) (*GetReadyResponse, error)
	// Rotary
	GetNextDistance(context.Context, *GetNextDistanceRequest) (*GetNextDistanceResponse, error)
	GetNextVelocity(context.Context, *GetNextVelocityRequest) (*GetNextVelocityResponse, error)
	GetRotaryTicks(context.Context, *GetRotaryTicksRequest) (*GetRotaryTicksResponse, error)
	GetDeltaTravelMM(context.Context, *GetDeltaTravelMMRequest) (*GetDeltaTravelMMResponse, error)
	GetWheelEncoderResolution(context.Context, *GetWheelEncoderResolutionRequest) (*GetWheelEncoderResolutionResponse, error)
	// Safety
	GetSafetyStatus(context.Context, *GetSafetyStatusRequest) (*GetSafetyStatusResponse, error)
	// GPS
	GetGPSData(context.Context, *GetGPSDataRequest) (*GetGPSDataResponse, error)
	GetNextGPSData(context.Context, *GetNextGPSDataRequest) (*GetNextGPSDataResponse, error)
	GetNextRawGPSData(context.Context, *GetNextRawGPSDataRequest) (*GetNextRawGPSDataResponse, error)
	GetGPSFixedPos(context.Context, *GetGPSFixedPosRequest) (*GetGPSFixedPosResponse, error)
	// Strobe
	SetStrobeSettings(context.Context, *StrobeSettings) (*SetStrobeSettingsResponse, error)
	GetStrobeSettings(context.Context, *GetStrobeSettingsRequest) (*StrobeSettings, error)
	// Managed Boards
	GetManagedBoardErrors(context.Context, *GetManagedBoardErrorsRequest) (*GetManagedBoardErrorsResponse, error)
	// Supervisory PLC
	GetSupervisoryStatus(context.Context, *GetSupervisoryStatusRequest) (*GetSupervisoryStatusResponse, error)
	GetReaperSupervisoryStatus(context.Context, *GetReaperSupervisoryStatusRequest) (*ReaperCenterEnclosureData, error)
	SetServerDisable(context.Context, *SetServerDisableRequest) (*SetServerDisableResponse, error)
	SetBTLDisable(context.Context, *SetBTLDisableRequest) (*SetBTLDisableResponse, error)
	SetScannersDisable(context.Context, *SetScannersDisableRequest) (*SetScannersDisableResponse, error)
	SetWheelEncoderBoardDisable(context.Context, *SetWheelEncoderBoardDisableRequest) (*SetWheelEncoderBoardDisableResponse, error)
	SetWheelEncoderDisable(context.Context, *SetWheelEncoderDisableRequest) (*SetWheelEncoderDisableResponse, error)
	SetGPSDisable(context.Context, *SetGPSDisableRequest) (*SetGPSDisableResponse, error)
	SuicideSwitch(context.Context, *SuicideSwitchRequest) (*SuicideSwitchResponse, error)
	CommandComputerPowerCycle(context.Context, *CommandComputerPowerCycleRequest) (*CommandComputerPowerCycleResponse, error)
	SetMainContactorDisable(context.Context, *SetMainContactorDisableRequest) (*SetMainContactorDisableResponse, error)
	SetStrobeDisable(context.Context, *SetStrobeDisableRequest) (*SetStrobeDisableResponse, error)
	SetAirConditionerDisable(context.Context, *SetAirConditionerDisableRequest) (*SetAirConditionerDisableResponse, error)
	SetChillerDisable(context.Context, *SetChillerDisableRequest) (*SetChillerDisableResponse, error)
	SetTempBypassDisable(context.Context, *SetTempBypassDisableRequest) (*SetTempBypassDisableResponse, error)
	SetHumidityBypassDisable(context.Context, *SetHumidityBypassDisableRequest) (*SetHumidityBypassDisableResponse, error)
	Get240VUptime(context.Context, *Get240VUptimeRequest) (*Get240VUptimeResponse, error)
	GetRuntime(context.Context, *GetRuntimeRequest) (*GetRuntimeResponse, error)
	// USB
	GetAvailableUSBStorage(context.Context, *GetAvailableUSBStorageRequest) (*GetAvailableUSBStorageResponse, error)
	// Jimbox
	SetJimboxSpeed(context.Context, *SetJimboxSpeedRequest) (*SetJimboxSpeedResponse, error)
	SetCruiseEnabled(context.Context, *SetCruiseEnabledRequest) (*SetCruiseEnabledResponse, error)
	GetCruiseStatus(context.Context, *GetCruiseStatusRequest) (*GetCruiseStatusResponse, error)
	// Tractor
	SetImplementStateOnTractor(context.Context, *SetImplementStateRequest) (*SetImplementStateResponse, error)
	SetSafeStateEnforcement(context.Context, *SetSafeStateEnforcementRequest) (*SetSafeStateEnforcementResponse, error)
	GetTractorSafetyState(context.Context, *GetTractorSafetyStateRequest) (*GetTractorSafetyStateResponse, error)
	GetTractorIFState(context.Context, *GetTractorIFStateRequest) (*GetTractorIFStateResponse, error)
	// Hardware/board status
	GetReaperEnclosureSensors(context.Context, *GetReaperEnclosureSensorsRequest) (*GetReaperEnclosureSensorsResponse, error)
	GetReaperModuleSensors(context.Context, *GetReaperModuleSensorsRequest) (*GetReaperModuleSensorsResponse, error)
	SetReaperScannerPower(context.Context, *SetReaperScannerPowerRequest) (*SetReaperScannerPowerResponse, error)
	SetReaperTargetPower(context.Context, *SetReaperTargetPowerRequest) (*SetReaperTargetPowerResponse, error)
	SetReaperPredictCamPower(context.Context, *SetReaperPredictCamPowerRequest) (*SetReaperPredictCamPowerResponse, error)
	SetReaperStrobeConfig(context.Context, *SetReaperStrobeConfigRequest) (*SetReaperStrobeConfigResponse, error)
	SetReaperStrobeEnable(context.Context, *SetReaperStrobeEnableRequest) (*SetReaperStrobeEnableResponse, error)
	SetReaperModulePcPower(context.Context, *SetReaperModulePcPowerRequest) (*SetReaperModulePcPowerResponse, error)
	SetReaperModuleLaserPower(context.Context, *SetReaperModuleLaserPowerRequest) (*SetReaperModuleLaserPowerResponse, error)
	SetReaperModuleStrobePower(context.Context, *SetReaperModuleStrobePowerRequest) (*SetReaperModuleStrobePowerResponse, error)
	IdentifyModule(context.Context, *IdentifyModuleRequest) (*IdentifyModuleResponse, error)
	mustEmbedUnimplementedHardwareManagerServiceServer()
}

// UnimplementedHardwareManagerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedHardwareManagerServiceServer struct {
}

func (UnimplementedHardwareManagerServiceServer) Ping(context.Context, *PingRequest) (*PingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetReady(context.Context, *GetReadyRequest) (*GetReadyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReady not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetNextDistance(context.Context, *GetNextDistanceRequest) (*GetNextDistanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextDistance not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetNextVelocity(context.Context, *GetNextVelocityRequest) (*GetNextVelocityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextVelocity not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetRotaryTicks(context.Context, *GetRotaryTicksRequest) (*GetRotaryTicksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRotaryTicks not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetDeltaTravelMM(context.Context, *GetDeltaTravelMMRequest) (*GetDeltaTravelMMResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeltaTravelMM not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetWheelEncoderResolution(context.Context, *GetWheelEncoderResolutionRequest) (*GetWheelEncoderResolutionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWheelEncoderResolution not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetSafetyStatus(context.Context, *GetSafetyStatusRequest) (*GetSafetyStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSafetyStatus not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetGPSData(context.Context, *GetGPSDataRequest) (*GetGPSDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGPSData not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetNextGPSData(context.Context, *GetNextGPSDataRequest) (*GetNextGPSDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextGPSData not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetNextRawGPSData(context.Context, *GetNextRawGPSDataRequest) (*GetNextRawGPSDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextRawGPSData not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetGPSFixedPos(context.Context, *GetGPSFixedPosRequest) (*GetGPSFixedPosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGPSFixedPos not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetStrobeSettings(context.Context, *StrobeSettings) (*SetStrobeSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStrobeSettings not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetStrobeSettings(context.Context, *GetStrobeSettingsRequest) (*StrobeSettings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStrobeSettings not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetManagedBoardErrors(context.Context, *GetManagedBoardErrorsRequest) (*GetManagedBoardErrorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetManagedBoardErrors not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetSupervisoryStatus(context.Context, *GetSupervisoryStatusRequest) (*GetSupervisoryStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupervisoryStatus not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetReaperSupervisoryStatus(context.Context, *GetReaperSupervisoryStatusRequest) (*ReaperCenterEnclosureData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReaperSupervisoryStatus not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetServerDisable(context.Context, *SetServerDisableRequest) (*SetServerDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetServerDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetBTLDisable(context.Context, *SetBTLDisableRequest) (*SetBTLDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetBTLDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetScannersDisable(context.Context, *SetScannersDisableRequest) (*SetScannersDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetScannersDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetWheelEncoderBoardDisable(context.Context, *SetWheelEncoderBoardDisableRequest) (*SetWheelEncoderBoardDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetWheelEncoderBoardDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetWheelEncoderDisable(context.Context, *SetWheelEncoderDisableRequest) (*SetWheelEncoderDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetWheelEncoderDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetGPSDisable(context.Context, *SetGPSDisableRequest) (*SetGPSDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGPSDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SuicideSwitch(context.Context, *SuicideSwitchRequest) (*SuicideSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuicideSwitch not implemented")
}
func (UnimplementedHardwareManagerServiceServer) CommandComputerPowerCycle(context.Context, *CommandComputerPowerCycleRequest) (*CommandComputerPowerCycleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommandComputerPowerCycle not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetMainContactorDisable(context.Context, *SetMainContactorDisableRequest) (*SetMainContactorDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMainContactorDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetStrobeDisable(context.Context, *SetStrobeDisableRequest) (*SetStrobeDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStrobeDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetAirConditionerDisable(context.Context, *SetAirConditionerDisableRequest) (*SetAirConditionerDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAirConditionerDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetChillerDisable(context.Context, *SetChillerDisableRequest) (*SetChillerDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetChillerDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetTempBypassDisable(context.Context, *SetTempBypassDisableRequest) (*SetTempBypassDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTempBypassDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetHumidityBypassDisable(context.Context, *SetHumidityBypassDisableRequest) (*SetHumidityBypassDisableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHumidityBypassDisable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) Get240VUptime(context.Context, *Get240VUptimeRequest) (*Get240VUptimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get240VUptime not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetRuntime(context.Context, *GetRuntimeRequest) (*GetRuntimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRuntime not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetAvailableUSBStorage(context.Context, *GetAvailableUSBStorageRequest) (*GetAvailableUSBStorageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableUSBStorage not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetJimboxSpeed(context.Context, *SetJimboxSpeedRequest) (*SetJimboxSpeedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetJimboxSpeed not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetCruiseEnabled(context.Context, *SetCruiseEnabledRequest) (*SetCruiseEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCruiseEnabled not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetCruiseStatus(context.Context, *GetCruiseStatusRequest) (*GetCruiseStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCruiseStatus not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetImplementStateOnTractor(context.Context, *SetImplementStateRequest) (*SetImplementStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetImplementStateOnTractor not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetSafeStateEnforcement(context.Context, *SetSafeStateEnforcementRequest) (*SetSafeStateEnforcementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSafeStateEnforcement not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetTractorSafetyState(context.Context, *GetTractorSafetyStateRequest) (*GetTractorSafetyStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTractorSafetyState not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetTractorIFState(context.Context, *GetTractorIFStateRequest) (*GetTractorIFStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTractorIFState not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetReaperEnclosureSensors(context.Context, *GetReaperEnclosureSensorsRequest) (*GetReaperEnclosureSensorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReaperEnclosureSensors not implemented")
}
func (UnimplementedHardwareManagerServiceServer) GetReaperModuleSensors(context.Context, *GetReaperModuleSensorsRequest) (*GetReaperModuleSensorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReaperModuleSensors not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperScannerPower(context.Context, *SetReaperScannerPowerRequest) (*SetReaperScannerPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperScannerPower not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperTargetPower(context.Context, *SetReaperTargetPowerRequest) (*SetReaperTargetPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperTargetPower not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperPredictCamPower(context.Context, *SetReaperPredictCamPowerRequest) (*SetReaperPredictCamPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperPredictCamPower not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperStrobeConfig(context.Context, *SetReaperStrobeConfigRequest) (*SetReaperStrobeConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperStrobeConfig not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperStrobeEnable(context.Context, *SetReaperStrobeEnableRequest) (*SetReaperStrobeEnableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperStrobeEnable not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperModulePcPower(context.Context, *SetReaperModulePcPowerRequest) (*SetReaperModulePcPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperModulePcPower not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperModuleLaserPower(context.Context, *SetReaperModuleLaserPowerRequest) (*SetReaperModuleLaserPowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperModuleLaserPower not implemented")
}
func (UnimplementedHardwareManagerServiceServer) SetReaperModuleStrobePower(context.Context, *SetReaperModuleStrobePowerRequest) (*SetReaperModuleStrobePowerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReaperModuleStrobePower not implemented")
}
func (UnimplementedHardwareManagerServiceServer) IdentifyModule(context.Context, *IdentifyModuleRequest) (*IdentifyModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IdentifyModule not implemented")
}
func (UnimplementedHardwareManagerServiceServer) mustEmbedUnimplementedHardwareManagerServiceServer() {
}

// UnsafeHardwareManagerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HardwareManagerServiceServer will
// result in compilation errors.
type UnsafeHardwareManagerServiceServer interface {
	mustEmbedUnimplementedHardwareManagerServiceServer()
}

func RegisterHardwareManagerServiceServer(s grpc.ServiceRegistrar, srv HardwareManagerServiceServer) {
	s.RegisterService(&HardwareManagerService_ServiceDesc, srv)
}

func _HardwareManagerService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetReady_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReadyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetReady(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetReady_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetReady(ctx, req.(*GetReadyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetNextDistance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextDistanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetNextDistance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetNextDistance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetNextDistance(ctx, req.(*GetNextDistanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetNextVelocity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextVelocityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetNextVelocity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetNextVelocity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetNextVelocity(ctx, req.(*GetNextVelocityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetRotaryTicks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRotaryTicksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetRotaryTicks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetRotaryTicks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetRotaryTicks(ctx, req.(*GetRotaryTicksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetDeltaTravelMM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeltaTravelMMRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetDeltaTravelMM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetDeltaTravelMM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetDeltaTravelMM(ctx, req.(*GetDeltaTravelMMRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetWheelEncoderResolution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWheelEncoderResolutionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetWheelEncoderResolution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetWheelEncoderResolution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetWheelEncoderResolution(ctx, req.(*GetWheelEncoderResolutionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetSafetyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSafetyStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetSafetyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetSafetyStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetSafetyStatus(ctx, req.(*GetSafetyStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetGPSData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGPSDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetGPSData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetGPSData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetGPSData(ctx, req.(*GetGPSDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetNextGPSData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextGPSDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetNextGPSData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetNextGPSData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetNextGPSData(ctx, req.(*GetNextGPSDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetNextRawGPSData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextRawGPSDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetNextRawGPSData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetNextRawGPSData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetNextRawGPSData(ctx, req.(*GetNextRawGPSDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetGPSFixedPos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGPSFixedPosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetGPSFixedPos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetGPSFixedPos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetGPSFixedPos(ctx, req.(*GetGPSFixedPosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetStrobeSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrobeSettings)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetStrobeSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetStrobeSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetStrobeSettings(ctx, req.(*StrobeSettings))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetStrobeSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStrobeSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetStrobeSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetStrobeSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetStrobeSettings(ctx, req.(*GetStrobeSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetManagedBoardErrors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetManagedBoardErrorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetManagedBoardErrors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetManagedBoardErrors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetManagedBoardErrors(ctx, req.(*GetManagedBoardErrorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetSupervisoryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupervisoryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetSupervisoryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetSupervisoryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetSupervisoryStatus(ctx, req.(*GetSupervisoryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetReaperSupervisoryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReaperSupervisoryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetReaperSupervisoryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetReaperSupervisoryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetReaperSupervisoryStatus(ctx, req.(*GetReaperSupervisoryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetServerDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetServerDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetServerDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetServerDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetServerDisable(ctx, req.(*SetServerDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetBTLDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBTLDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetBTLDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetBTLDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetBTLDisable(ctx, req.(*SetBTLDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetScannersDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetScannersDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetScannersDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetScannersDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetScannersDisable(ctx, req.(*SetScannersDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetWheelEncoderBoardDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWheelEncoderBoardDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetWheelEncoderBoardDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetWheelEncoderBoardDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetWheelEncoderBoardDisable(ctx, req.(*SetWheelEncoderBoardDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetWheelEncoderDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWheelEncoderDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetWheelEncoderDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetWheelEncoderDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetWheelEncoderDisable(ctx, req.(*SetWheelEncoderDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetGPSDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGPSDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetGPSDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetGPSDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetGPSDisable(ctx, req.(*SetGPSDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SuicideSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuicideSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SuicideSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SuicideSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SuicideSwitch(ctx, req.(*SuicideSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_CommandComputerPowerCycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommandComputerPowerCycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).CommandComputerPowerCycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_CommandComputerPowerCycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).CommandComputerPowerCycle(ctx, req.(*CommandComputerPowerCycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetMainContactorDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMainContactorDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetMainContactorDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetMainContactorDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetMainContactorDisable(ctx, req.(*SetMainContactorDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetStrobeDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStrobeDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetStrobeDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetStrobeDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetStrobeDisable(ctx, req.(*SetStrobeDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetAirConditionerDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAirConditionerDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetAirConditionerDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetAirConditionerDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetAirConditionerDisable(ctx, req.(*SetAirConditionerDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetChillerDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChillerDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetChillerDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetChillerDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetChillerDisable(ctx, req.(*SetChillerDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetTempBypassDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTempBypassDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetTempBypassDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetTempBypassDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetTempBypassDisable(ctx, req.(*SetTempBypassDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetHumidityBypassDisable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHumidityBypassDisableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetHumidityBypassDisable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetHumidityBypassDisable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetHumidityBypassDisable(ctx, req.(*SetHumidityBypassDisableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_Get240VUptime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Get240VUptimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).Get240VUptime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_Get240VUptime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).Get240VUptime(ctx, req.(*Get240VUptimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetRuntime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRuntimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetRuntime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetRuntime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetRuntime(ctx, req.(*GetRuntimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetAvailableUSBStorage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableUSBStorageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetAvailableUSBStorage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetAvailableUSBStorage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetAvailableUSBStorage(ctx, req.(*GetAvailableUSBStorageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetJimboxSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetJimboxSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetJimboxSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetJimboxSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetJimboxSpeed(ctx, req.(*SetJimboxSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetCruiseEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCruiseEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetCruiseEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetCruiseEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetCruiseEnabled(ctx, req.(*SetCruiseEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetCruiseStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCruiseStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetCruiseStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetCruiseStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetCruiseStatus(ctx, req.(*GetCruiseStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetImplementStateOnTractor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetImplementStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetImplementStateOnTractor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetImplementStateOnTractor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetImplementStateOnTractor(ctx, req.(*SetImplementStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetSafeStateEnforcement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSafeStateEnforcementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetSafeStateEnforcement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetSafeStateEnforcement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetSafeStateEnforcement(ctx, req.(*SetSafeStateEnforcementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetTractorSafetyState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTractorSafetyStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetTractorSafetyState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetTractorSafetyState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetTractorSafetyState(ctx, req.(*GetTractorSafetyStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetTractorIFState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTractorIFStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetTractorIFState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetTractorIFState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetTractorIFState(ctx, req.(*GetTractorIFStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetReaperEnclosureSensors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReaperEnclosureSensorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetReaperEnclosureSensors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetReaperEnclosureSensors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetReaperEnclosureSensors(ctx, req.(*GetReaperEnclosureSensorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_GetReaperModuleSensors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReaperModuleSensorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).GetReaperModuleSensors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_GetReaperModuleSensors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).GetReaperModuleSensors(ctx, req.(*GetReaperModuleSensorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperScannerPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperScannerPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperScannerPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperScannerPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperScannerPower(ctx, req.(*SetReaperScannerPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperTargetPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperTargetPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperTargetPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperTargetPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperTargetPower(ctx, req.(*SetReaperTargetPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperPredictCamPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperPredictCamPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperPredictCamPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperPredictCamPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperPredictCamPower(ctx, req.(*SetReaperPredictCamPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperStrobeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperStrobeConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperStrobeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperStrobeConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperStrobeConfig(ctx, req.(*SetReaperStrobeConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperStrobeEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperStrobeEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperStrobeEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperStrobeEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperStrobeEnable(ctx, req.(*SetReaperStrobeEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperModulePcPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperModulePcPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperModulePcPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperModulePcPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperModulePcPower(ctx, req.(*SetReaperModulePcPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperModuleLaserPower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperModuleLaserPowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperModuleLaserPower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperModuleLaserPower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperModuleLaserPower(ctx, req.(*SetReaperModuleLaserPowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_SetReaperModuleStrobePower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReaperModuleStrobePowerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).SetReaperModuleStrobePower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_SetReaperModuleStrobePower_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).SetReaperModuleStrobePower(ctx, req.(*SetReaperModuleStrobePowerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HardwareManagerService_IdentifyModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdentifyModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HardwareManagerServiceServer).IdentifyModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HardwareManagerService_IdentifyModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HardwareManagerServiceServer).IdentifyModule(ctx, req.(*IdentifyModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HardwareManagerService_ServiceDesc is the grpc.ServiceDesc for HardwareManagerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HardwareManagerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hardware_manager.HardwareManagerService",
	HandlerType: (*HardwareManagerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _HardwareManagerService_Ping_Handler,
		},
		{
			MethodName: "GetReady",
			Handler:    _HardwareManagerService_GetReady_Handler,
		},
		{
			MethodName: "GetNextDistance",
			Handler:    _HardwareManagerService_GetNextDistance_Handler,
		},
		{
			MethodName: "GetNextVelocity",
			Handler:    _HardwareManagerService_GetNextVelocity_Handler,
		},
		{
			MethodName: "GetRotaryTicks",
			Handler:    _HardwareManagerService_GetRotaryTicks_Handler,
		},
		{
			MethodName: "GetDeltaTravelMM",
			Handler:    _HardwareManagerService_GetDeltaTravelMM_Handler,
		},
		{
			MethodName: "GetWheelEncoderResolution",
			Handler:    _HardwareManagerService_GetWheelEncoderResolution_Handler,
		},
		{
			MethodName: "GetSafetyStatus",
			Handler:    _HardwareManagerService_GetSafetyStatus_Handler,
		},
		{
			MethodName: "GetGPSData",
			Handler:    _HardwareManagerService_GetGPSData_Handler,
		},
		{
			MethodName: "GetNextGPSData",
			Handler:    _HardwareManagerService_GetNextGPSData_Handler,
		},
		{
			MethodName: "GetNextRawGPSData",
			Handler:    _HardwareManagerService_GetNextRawGPSData_Handler,
		},
		{
			MethodName: "GetGPSFixedPos",
			Handler:    _HardwareManagerService_GetGPSFixedPos_Handler,
		},
		{
			MethodName: "SetStrobeSettings",
			Handler:    _HardwareManagerService_SetStrobeSettings_Handler,
		},
		{
			MethodName: "GetStrobeSettings",
			Handler:    _HardwareManagerService_GetStrobeSettings_Handler,
		},
		{
			MethodName: "GetManagedBoardErrors",
			Handler:    _HardwareManagerService_GetManagedBoardErrors_Handler,
		},
		{
			MethodName: "GetSupervisoryStatus",
			Handler:    _HardwareManagerService_GetSupervisoryStatus_Handler,
		},
		{
			MethodName: "GetReaperSupervisoryStatus",
			Handler:    _HardwareManagerService_GetReaperSupervisoryStatus_Handler,
		},
		{
			MethodName: "SetServerDisable",
			Handler:    _HardwareManagerService_SetServerDisable_Handler,
		},
		{
			MethodName: "SetBTLDisable",
			Handler:    _HardwareManagerService_SetBTLDisable_Handler,
		},
		{
			MethodName: "SetScannersDisable",
			Handler:    _HardwareManagerService_SetScannersDisable_Handler,
		},
		{
			MethodName: "SetWheelEncoderBoardDisable",
			Handler:    _HardwareManagerService_SetWheelEncoderBoardDisable_Handler,
		},
		{
			MethodName: "SetWheelEncoderDisable",
			Handler:    _HardwareManagerService_SetWheelEncoderDisable_Handler,
		},
		{
			MethodName: "SetGPSDisable",
			Handler:    _HardwareManagerService_SetGPSDisable_Handler,
		},
		{
			MethodName: "SuicideSwitch",
			Handler:    _HardwareManagerService_SuicideSwitch_Handler,
		},
		{
			MethodName: "CommandComputerPowerCycle",
			Handler:    _HardwareManagerService_CommandComputerPowerCycle_Handler,
		},
		{
			MethodName: "SetMainContactorDisable",
			Handler:    _HardwareManagerService_SetMainContactorDisable_Handler,
		},
		{
			MethodName: "SetStrobeDisable",
			Handler:    _HardwareManagerService_SetStrobeDisable_Handler,
		},
		{
			MethodName: "SetAirConditionerDisable",
			Handler:    _HardwareManagerService_SetAirConditionerDisable_Handler,
		},
		{
			MethodName: "SetChillerDisable",
			Handler:    _HardwareManagerService_SetChillerDisable_Handler,
		},
		{
			MethodName: "SetTempBypassDisable",
			Handler:    _HardwareManagerService_SetTempBypassDisable_Handler,
		},
		{
			MethodName: "SetHumidityBypassDisable",
			Handler:    _HardwareManagerService_SetHumidityBypassDisable_Handler,
		},
		{
			MethodName: "Get240vUptime",
			Handler:    _HardwareManagerService_Get240VUptime_Handler,
		},
		{
			MethodName: "GetRuntime",
			Handler:    _HardwareManagerService_GetRuntime_Handler,
		},
		{
			MethodName: "GetAvailableUSBStorage",
			Handler:    _HardwareManagerService_GetAvailableUSBStorage_Handler,
		},
		{
			MethodName: "SetJimboxSpeed",
			Handler:    _HardwareManagerService_SetJimboxSpeed_Handler,
		},
		{
			MethodName: "SetCruiseEnabled",
			Handler:    _HardwareManagerService_SetCruiseEnabled_Handler,
		},
		{
			MethodName: "GetCruiseStatus",
			Handler:    _HardwareManagerService_GetCruiseStatus_Handler,
		},
		{
			MethodName: "SetImplementStateOnTractor",
			Handler:    _HardwareManagerService_SetImplementStateOnTractor_Handler,
		},
		{
			MethodName: "SetSafeStateEnforcement",
			Handler:    _HardwareManagerService_SetSafeStateEnforcement_Handler,
		},
		{
			MethodName: "GetTractorSafetyState",
			Handler:    _HardwareManagerService_GetTractorSafetyState_Handler,
		},
		{
			MethodName: "GetTractorIFState",
			Handler:    _HardwareManagerService_GetTractorIFState_Handler,
		},
		{
			MethodName: "GetReaperEnclosureSensors",
			Handler:    _HardwareManagerService_GetReaperEnclosureSensors_Handler,
		},
		{
			MethodName: "GetReaperModuleSensors",
			Handler:    _HardwareManagerService_GetReaperModuleSensors_Handler,
		},
		{
			MethodName: "SetReaperScannerPower",
			Handler:    _HardwareManagerService_SetReaperScannerPower_Handler,
		},
		{
			MethodName: "SetReaperTargetPower",
			Handler:    _HardwareManagerService_SetReaperTargetPower_Handler,
		},
		{
			MethodName: "SetReaperPredictCamPower",
			Handler:    _HardwareManagerService_SetReaperPredictCamPower_Handler,
		},
		{
			MethodName: "SetReaperStrobeConfig",
			Handler:    _HardwareManagerService_SetReaperStrobeConfig_Handler,
		},
		{
			MethodName: "SetReaperStrobeEnable",
			Handler:    _HardwareManagerService_SetReaperStrobeEnable_Handler,
		},
		{
			MethodName: "SetReaperModulePcPower",
			Handler:    _HardwareManagerService_SetReaperModulePcPower_Handler,
		},
		{
			MethodName: "SetReaperModuleLaserPower",
			Handler:    _HardwareManagerService_SetReaperModuleLaserPower_Handler,
		},
		{
			MethodName: "SetReaperModuleStrobePower",
			Handler:    _HardwareManagerService_SetReaperModuleStrobePower_Handler,
		},
		{
			MethodName: "IdentifyModule",
			Handler:    _HardwareManagerService_IdentifyModule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "hardware_manager/proto/hardware_manager_service.proto",
}

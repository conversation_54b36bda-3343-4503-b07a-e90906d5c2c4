// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: hardware_manager/proto/hardware_manager_service.proto

package hardware_manager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CarrierPhaseSoln int32

const (
	CarrierPhaseSoln_NONE     CarrierPhaseSoln = 0
	CarrierPhaseSoln_FLOATING CarrierPhaseSoln = 1
	CarrierPhaseSoln_FIXED    CarrierPhaseSoln = 2
)

// Enum value maps for CarrierPhaseSoln.
var (
	CarrierPhaseSoln_name = map[int32]string{
		0: "NONE",
		1: "FLOATING",
		2: "FIXED",
	}
	CarrierPhaseSoln_value = map[string]int32{
		"NONE":     0,
		"FLOATING": 1,
		"FIXED":    2,
	}
)

func (x CarrierPhaseSoln) Enum() *CarrierPhaseSoln {
	p := new(CarrierPhaseSoln)
	*p = x
	return p
}

func (x CarrierPhaseSoln) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CarrierPhaseSoln) Descriptor() protoreflect.EnumDescriptor {
	return file_hardware_manager_proto_hardware_manager_service_proto_enumTypes[0].Descriptor()
}

func (CarrierPhaseSoln) Type() protoreflect.EnumType {
	return &file_hardware_manager_proto_hardware_manager_service_proto_enumTypes[0]
}

func (x CarrierPhaseSoln) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CarrierPhaseSoln.Descriptor instead.
func (CarrierPhaseSoln) EnumDescriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{0}
}

type FixType int32

const (
	FixType_NO_FIX              FixType = 0
	FixType_DEAD_RECKONING_ONLY FixType = 1
	FixType_FIX_2D              FixType = 2
	FixType_FIX_3D              FixType = 3
	FixType_GNSS_DR             FixType = 4
	FixType_TIME_ONLY           FixType = 5
)

// Enum value maps for FixType.
var (
	FixType_name = map[int32]string{
		0: "NO_FIX",
		1: "DEAD_RECKONING_ONLY",
		2: "FIX_2D",
		3: "FIX_3D",
		4: "GNSS_DR",
		5: "TIME_ONLY",
	}
	FixType_value = map[string]int32{
		"NO_FIX":              0,
		"DEAD_RECKONING_ONLY": 1,
		"FIX_2D":              2,
		"FIX_3D":              3,
		"GNSS_DR":             4,
		"TIME_ONLY":           5,
	}
)

func (x FixType) Enum() *FixType {
	p := new(FixType)
	*p = x
	return p
}

func (x FixType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FixType) Descriptor() protoreflect.EnumDescriptor {
	return file_hardware_manager_proto_hardware_manager_service_proto_enumTypes[1].Descriptor()
}

func (FixType) Type() protoreflect.EnumType {
	return &file_hardware_manager_proto_hardware_manager_service_proto_enumTypes[1]
}

func (x FixType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FixType.Descriptor instead.
func (FixType) EnumDescriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{1}
}

type NetworkLinkSpeed int32

const (
	NetworkLinkSpeed_UNKNOWN         NetworkLinkSpeed = 0
	NetworkLinkSpeed_SPEED_10M_HALF  NetworkLinkSpeed = 1
	NetworkLinkSpeed_SPEED_10M_FULL  NetworkLinkSpeed = 2
	NetworkLinkSpeed_SPEED_100M_HALF NetworkLinkSpeed = 3
	NetworkLinkSpeed_SPEED_100M_FULL NetworkLinkSpeed = 4
	NetworkLinkSpeed_SPEED_1G_FULL   NetworkLinkSpeed = 5
	NetworkLinkSpeed_SPEED_2G5_FULL  NetworkLinkSpeed = 6
	NetworkLinkSpeed_SPEED_5G_FULL   NetworkLinkSpeed = 7
	NetworkLinkSpeed_SPEED_10G_FULL  NetworkLinkSpeed = 8
)

// Enum value maps for NetworkLinkSpeed.
var (
	NetworkLinkSpeed_name = map[int32]string{
		0: "UNKNOWN",
		1: "SPEED_10M_HALF",
		2: "SPEED_10M_FULL",
		3: "SPEED_100M_HALF",
		4: "SPEED_100M_FULL",
		5: "SPEED_1G_FULL",
		6: "SPEED_2G5_FULL",
		7: "SPEED_5G_FULL",
		8: "SPEED_10G_FULL",
	}
	NetworkLinkSpeed_value = map[string]int32{
		"UNKNOWN":         0,
		"SPEED_10M_HALF":  1,
		"SPEED_10M_FULL":  2,
		"SPEED_100M_HALF": 3,
		"SPEED_100M_FULL": 4,
		"SPEED_1G_FULL":   5,
		"SPEED_2G5_FULL":  6,
		"SPEED_5G_FULL":   7,
		"SPEED_10G_FULL":  8,
	}
)

func (x NetworkLinkSpeed) Enum() *NetworkLinkSpeed {
	p := new(NetworkLinkSpeed)
	*p = x
	return p
}

func (x NetworkLinkSpeed) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkLinkSpeed) Descriptor() protoreflect.EnumDescriptor {
	return file_hardware_manager_proto_hardware_manager_service_proto_enumTypes[2].Descriptor()
}

func (NetworkLinkSpeed) Type() protoreflect.EnumType {
	return &file_hardware_manager_proto_hardware_manager_service_proto_enumTypes[2]
}

func (x NetworkLinkSpeed) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkLinkSpeed.Descriptor instead.
func (NetworkLinkSpeed) EnumDescriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{2}
}

type PingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{0}
}

func (x *PingRequest) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingResponse) Reset() {
	*x = PingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingResponse) ProtoMessage() {}

func (x *PingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingResponse.ProtoReflect.Descriptor instead.
func (*PingResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{1}
}

func (x *PingResponse) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type GetRotaryTicksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRotaryTicksRequest) Reset() {
	*x = GetRotaryTicksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRotaryTicksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRotaryTicksRequest) ProtoMessage() {}

func (x *GetRotaryTicksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRotaryTicksRequest.ProtoReflect.Descriptor instead.
func (*GetRotaryTicksRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{2}
}

type GetRotaryTicksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs       uint64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	FrontLeftTicks    int64  `protobuf:"varint,2,opt,name=front_left_ticks,json=frontLeftTicks,proto3" json:"front_left_ticks,omitempty"`
	FrontRightTicks   int64  `protobuf:"varint,3,opt,name=front_right_ticks,json=frontRightTicks,proto3" json:"front_right_ticks,omitempty"`
	BackLeftTicks     int64  `protobuf:"varint,4,opt,name=back_left_ticks,json=backLeftTicks,proto3" json:"back_left_ticks,omitempty"`
	BackRightTicks    int64  `protobuf:"varint,5,opt,name=back_right_ticks,json=backRightTicks,proto3" json:"back_right_ticks,omitempty"`
	FrontLeftEnabled  bool   `protobuf:"varint,6,opt,name=front_left_enabled,json=frontLeftEnabled,proto3" json:"front_left_enabled,omitempty"`
	FrontRightEnabled bool   `protobuf:"varint,7,opt,name=front_right_enabled,json=frontRightEnabled,proto3" json:"front_right_enabled,omitempty"`
	BackLeftEnabled   bool   `protobuf:"varint,8,opt,name=back_left_enabled,json=backLeftEnabled,proto3" json:"back_left_enabled,omitempty"`
	BackRightEnabled  bool   `protobuf:"varint,9,opt,name=back_right_enabled,json=backRightEnabled,proto3" json:"back_right_enabled,omitempty"`
}

func (x *GetRotaryTicksResponse) Reset() {
	*x = GetRotaryTicksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRotaryTicksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRotaryTicksResponse) ProtoMessage() {}

func (x *GetRotaryTicksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRotaryTicksResponse.ProtoReflect.Descriptor instead.
func (*GetRotaryTicksResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetRotaryTicksResponse) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetRotaryTicksResponse) GetFrontLeftTicks() int64 {
	if x != nil {
		return x.FrontLeftTicks
	}
	return 0
}

func (x *GetRotaryTicksResponse) GetFrontRightTicks() int64 {
	if x != nil {
		return x.FrontRightTicks
	}
	return 0
}

func (x *GetRotaryTicksResponse) GetBackLeftTicks() int64 {
	if x != nil {
		return x.BackLeftTicks
	}
	return 0
}

func (x *GetRotaryTicksResponse) GetBackRightTicks() int64 {
	if x != nil {
		return x.BackRightTicks
	}
	return 0
}

func (x *GetRotaryTicksResponse) GetFrontLeftEnabled() bool {
	if x != nil {
		return x.FrontLeftEnabled
	}
	return false
}

func (x *GetRotaryTicksResponse) GetFrontRightEnabled() bool {
	if x != nil {
		return x.FrontRightEnabled
	}
	return false
}

func (x *GetRotaryTicksResponse) GetBackLeftEnabled() bool {
	if x != nil {
		return x.BackLeftEnabled
	}
	return false
}

func (x *GetRotaryTicksResponse) GetBackRightEnabled() bool {
	if x != nil {
		return x.BackRightEnabled
	}
	return false
}

type GetNextDistanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs uint64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextDistanceRequest) Reset() {
	*x = GetNextDistanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextDistanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextDistanceRequest) ProtoMessage() {}

func (x *GetNextDistanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextDistanceRequest.ProtoReflect.Descriptor instead.
func (*GetNextDistanceRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetNextDistanceRequest) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetNextDistanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs uint64  `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Distance    float64 `protobuf:"fixed64,2,opt,name=distance,proto3" json:"distance,omitempty"`
}

func (x *GetNextDistanceResponse) Reset() {
	*x = GetNextDistanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextDistanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextDistanceResponse) ProtoMessage() {}

func (x *GetNextDistanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextDistanceResponse.ProtoReflect.Descriptor instead.
func (*GetNextDistanceResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetNextDistanceResponse) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetNextDistanceResponse) GetDistance() float64 {
	if x != nil {
		return x.Distance
	}
	return 0
}

type GetNextVelocityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs uint64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextVelocityRequest) Reset() {
	*x = GetNextVelocityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVelocityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVelocityRequest) ProtoMessage() {}

func (x *GetNextVelocityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVelocityRequest.ProtoReflect.Descriptor instead.
func (*GetNextVelocityRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetNextVelocityRequest) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetNextVelocityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs uint64  `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	MmPerMs     float64 `protobuf:"fixed64,2,opt,name=mm_per_ms,json=mmPerMs,proto3" json:"mm_per_ms,omitempty"`
	Lifted      bool    `protobuf:"varint,3,opt,name=lifted,proto3" json:"lifted,omitempty"`
}

func (x *GetNextVelocityResponse) Reset() {
	*x = GetNextVelocityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextVelocityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextVelocityResponse) ProtoMessage() {}

func (x *GetNextVelocityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextVelocityResponse.ProtoReflect.Descriptor instead.
func (*GetNextVelocityResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetNextVelocityResponse) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetNextVelocityResponse) GetMmPerMs() float64 {
	if x != nil {
		return x.MmPerMs
	}
	return 0
}

func (x *GetNextVelocityResponse) GetLifted() bool {
	if x != nil {
		return x.Lifted
	}
	return false
}

type SetJimboxSpeedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetSpeed       float64 `protobuf:"fixed64,1,opt,name=target_speed,json=targetSpeed,proto3" json:"target_speed,omitempty"`
	ActualGroundSpeed float64 `protobuf:"fixed64,2,opt,name=actual_ground_speed,json=actualGroundSpeed,proto3" json:"actual_ground_speed,omitempty"`
}

func (x *SetJimboxSpeedRequest) Reset() {
	*x = SetJimboxSpeedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetJimboxSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetJimboxSpeedRequest) ProtoMessage() {}

func (x *SetJimboxSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetJimboxSpeedRequest.ProtoReflect.Descriptor instead.
func (*SetJimboxSpeedRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{8}
}

func (x *SetJimboxSpeedRequest) GetTargetSpeed() float64 {
	if x != nil {
		return x.TargetSpeed
	}
	return 0
}

func (x *SetJimboxSpeedRequest) GetActualGroundSpeed() float64 {
	if x != nil {
		return x.ActualGroundSpeed
	}
	return 0
}

type SetJimboxSpeedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpeedSetpoint float64 `protobuf:"fixed64,1,opt,name=speed_setpoint,json=speedSetpoint,proto3" json:"speed_setpoint,omitempty"`
}

func (x *SetJimboxSpeedResponse) Reset() {
	*x = SetJimboxSpeedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetJimboxSpeedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetJimboxSpeedResponse) ProtoMessage() {}

func (x *SetJimboxSpeedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetJimboxSpeedResponse.ProtoReflect.Descriptor instead.
func (*SetJimboxSpeedResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{9}
}

func (x *SetJimboxSpeedResponse) GetSpeedSetpoint() float64 {
	if x != nil {
		return x.SpeedSetpoint
	}
	return 0
}

type SetCruiseEnabledRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetCruiseEnabledRequest) Reset() {
	*x = SetCruiseEnabledRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCruiseEnabledRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCruiseEnabledRequest) ProtoMessage() {}

func (x *SetCruiseEnabledRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCruiseEnabledRequest.ProtoReflect.Descriptor instead.
func (*SetCruiseEnabledRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{10}
}

func (x *SetCruiseEnabledRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetCruiseEnabledResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetCruiseEnabledResponse) Reset() {
	*x = SetCruiseEnabledResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCruiseEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCruiseEnabledResponse) ProtoMessage() {}

func (x *SetCruiseEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCruiseEnabledResponse.ProtoReflect.Descriptor instead.
func (*SetCruiseEnabledResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{11}
}

func (x *SetCruiseEnabledResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetCruiseStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCruiseStatusRequest) Reset() {
	*x = GetCruiseStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCruiseStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCruiseStatusRequest) ProtoMessage() {}

func (x *GetCruiseStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCruiseStatusRequest.ProtoReflect.Descriptor instead.
func (*GetCruiseStatusRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{12}
}

type GetCruiseStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled     bool    `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Installed   bool    `protobuf:"varint,2,opt,name=installed,proto3" json:"installed,omitempty"`
	Speed       float64 `protobuf:"fixed64,3,opt,name=speed,proto3" json:"speed,omitempty"`
	AllowEnable bool    `protobuf:"varint,4,opt,name=allow_enable,json=allowEnable,proto3" json:"allow_enable,omitempty"`
}

func (x *GetCruiseStatusResponse) Reset() {
	*x = GetCruiseStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCruiseStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCruiseStatusResponse) ProtoMessage() {}

func (x *GetCruiseStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCruiseStatusResponse.ProtoReflect.Descriptor instead.
func (*GetCruiseStatusResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetCruiseStatusResponse) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *GetCruiseStatusResponse) GetInstalled() bool {
	if x != nil {
		return x.Installed
	}
	return false
}

func (x *GetCruiseStatusResponse) GetSpeed() float64 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *GetCruiseStatusResponse) GetAllowEnable() bool {
	if x != nil {
		return x.AllowEnable
	}
	return false
}

type SetImplementStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Active       bool    `protobuf:"varint,1,opt,name=active,proto3" json:"active,omitempty"`
	Error        bool    `protobuf:"varint,2,opt,name=error,proto3" json:"error,omitempty"`
	ErrorMessage *string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3,oneof" json:"error_message,omitempty"`
}

func (x *SetImplementStateRequest) Reset() {
	*x = SetImplementStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImplementStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImplementStateRequest) ProtoMessage() {}

func (x *SetImplementStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImplementStateRequest.ProtoReflect.Descriptor instead.
func (*SetImplementStateRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{14}
}

func (x *SetImplementStateRequest) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *SetImplementStateRequest) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *SetImplementStateRequest) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

type SetImplementStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetImplementStateResponse) Reset() {
	*x = SetImplementStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetImplementStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetImplementStateResponse) ProtoMessage() {}

func (x *SetImplementStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetImplementStateResponse.ProtoReflect.Descriptor instead.
func (*SetImplementStateResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{15}
}

type SetSafeStateEnforcementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enforced bool `protobuf:"varint,1,opt,name=enforced,proto3" json:"enforced,omitempty"`
}

func (x *SetSafeStateEnforcementRequest) Reset() {
	*x = SetSafeStateEnforcementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSafeStateEnforcementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSafeStateEnforcementRequest) ProtoMessage() {}

func (x *SetSafeStateEnforcementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSafeStateEnforcementRequest.ProtoReflect.Descriptor instead.
func (*SetSafeStateEnforcementRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{16}
}

func (x *SetSafeStateEnforcementRequest) GetEnforced() bool {
	if x != nil {
		return x.Enforced
	}
	return false
}

type SetSafeStateEnforcementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetSafeStateEnforcementResponse) Reset() {
	*x = SetSafeStateEnforcementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSafeStateEnforcementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSafeStateEnforcementResponse) ProtoMessage() {}

func (x *SetSafeStateEnforcementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSafeStateEnforcementResponse.ProtoReflect.Descriptor instead.
func (*SetSafeStateEnforcementResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{17}
}

type GetTractorSafetyStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetTractorSafetyStateRequest) Reset() {
	*x = GetTractorSafetyStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTractorSafetyStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTractorSafetyStateRequest) ProtoMessage() {}

func (x *GetTractorSafetyStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTractorSafetyStateRequest.ProtoReflect.Descriptor instead.
func (*GetTractorSafetyStateRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetTractorSafetyStateRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetTractorSafetyStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	IsSafe      bool  `protobuf:"varint,2,opt,name=is_safe,json=isSafe,proto3" json:"is_safe,omitempty"`
	Enforced    bool  `protobuf:"varint,3,opt,name=enforced,proto3" json:"enforced,omitempty"`
}

func (x *GetTractorSafetyStateResponse) Reset() {
	*x = GetTractorSafetyStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTractorSafetyStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTractorSafetyStateResponse) ProtoMessage() {}

func (x *GetTractorSafetyStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTractorSafetyStateResponse.ProtoReflect.Descriptor instead.
func (*GetTractorSafetyStateResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetTractorSafetyStateResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetTractorSafetyStateResponse) GetIsSafe() bool {
	if x != nil {
		return x.IsSafe
	}
	return false
}

func (x *GetTractorSafetyStateResponse) GetEnforced() bool {
	if x != nil {
		return x.Enforced
	}
	return false
}

type GetTractorIFStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTractorIFStateRequest) Reset() {
	*x = GetTractorIFStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTractorIFStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTractorIFStateRequest) ProtoMessage() {}

func (x *GetTractorIFStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTractorIFStateRequest.ProtoReflect.Descriptor instead.
func (*GetTractorIFStateRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{20}
}

type GetTractorIFStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expected  bool `protobuf:"varint,1,opt,name=expected,proto3" json:"expected,omitempty"`
	Connected bool `protobuf:"varint,2,opt,name=connected,proto3" json:"connected,omitempty"`
}

func (x *GetTractorIFStateResponse) Reset() {
	*x = GetTractorIFStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTractorIFStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTractorIFStateResponse) ProtoMessage() {}

func (x *GetTractorIFStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTractorIFStateResponse.ProtoReflect.Descriptor instead.
func (*GetTractorIFStateResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetTractorIFStateResponse) GetExpected() bool {
	if x != nil {
		return x.Expected
	}
	return false
}

func (x *GetTractorIFStateResponse) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

type GetSafetyStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSafetyStatusRequest) Reset() {
	*x = GetSafetyStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSafetyStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSafetyStatusRequest) ProtoMessage() {}

func (x *GetSafetyStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSafetyStatusRequest.ProtoReflect.Descriptor instead.
func (*GetSafetyStatusRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{22}
}

type GetSafetyStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lifted             bool `protobuf:"varint,1,opt,name=lifted,proto3" json:"lifted,omitempty"`
	Estopped           bool `protobuf:"varint,2,opt,name=estopped,proto3" json:"estopped,omitempty"`
	InCabEstopped      bool `protobuf:"varint,3,opt,name=in_cab_estopped,json=inCabEstopped,proto3" json:"in_cab_estopped,omitempty"`
	LeftEstopped       bool `protobuf:"varint,4,opt,name=left_estopped,json=leftEstopped,proto3" json:"left_estopped,omitempty"`
	RightEstopped      bool `protobuf:"varint,5,opt,name=right_estopped,json=rightEstopped,proto3" json:"right_estopped,omitempty"`
	LaserKey           bool `protobuf:"varint,6,opt,name=laser_key,json=laserKey,proto3" json:"laser_key,omitempty"`
	Interlock          bool `protobuf:"varint,7,opt,name=interlock,proto3" json:"interlock,omitempty"`
	WaterProtect       bool `protobuf:"varint,8,opt,name=water_protect,json=waterProtect,proto3" json:"water_protect,omitempty"`
	ResetRequired      bool `protobuf:"varint,9,opt,name=reset_required,json=resetRequired,proto3" json:"reset_required,omitempty"`
	CenterEstop        bool `protobuf:"varint,10,opt,name=center_estop,json=centerEstop,proto3" json:"center_estop,omitempty"`
	PowerButtonEstop   bool `protobuf:"varint,11,opt,name=power_button_estop,json=powerButtonEstop,proto3" json:"power_button_estop,omitempty"`
	LeftLpsuInterlock  bool `protobuf:"varint,12,opt,name=left_lpsu_interlock,json=leftLpsuInterlock,proto3" json:"left_lpsu_interlock,omitempty"`
	RightLpsuInterlock bool `protobuf:"varint,13,opt,name=right_lpsu_interlock,json=rightLpsuInterlock,proto3" json:"right_lpsu_interlock,omitempty"`
	DebugMode          bool `protobuf:"varint,14,opt,name=debug_mode,json=debugMode,proto3" json:"debug_mode,omitempty"`
}

func (x *GetSafetyStatusResponse) Reset() {
	*x = GetSafetyStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSafetyStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSafetyStatusResponse) ProtoMessage() {}

func (x *GetSafetyStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSafetyStatusResponse.ProtoReflect.Descriptor instead.
func (*GetSafetyStatusResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetSafetyStatusResponse) GetLifted() bool {
	if x != nil {
		return x.Lifted
	}
	return false
}

func (x *GetSafetyStatusResponse) GetEstopped() bool {
	if x != nil {
		return x.Estopped
	}
	return false
}

func (x *GetSafetyStatusResponse) GetInCabEstopped() bool {
	if x != nil {
		return x.InCabEstopped
	}
	return false
}

func (x *GetSafetyStatusResponse) GetLeftEstopped() bool {
	if x != nil {
		return x.LeftEstopped
	}
	return false
}

func (x *GetSafetyStatusResponse) GetRightEstopped() bool {
	if x != nil {
		return x.RightEstopped
	}
	return false
}

func (x *GetSafetyStatusResponse) GetLaserKey() bool {
	if x != nil {
		return x.LaserKey
	}
	return false
}

func (x *GetSafetyStatusResponse) GetInterlock() bool {
	if x != nil {
		return x.Interlock
	}
	return false
}

func (x *GetSafetyStatusResponse) GetWaterProtect() bool {
	if x != nil {
		return x.WaterProtect
	}
	return false
}

func (x *GetSafetyStatusResponse) GetResetRequired() bool {
	if x != nil {
		return x.ResetRequired
	}
	return false
}

func (x *GetSafetyStatusResponse) GetCenterEstop() bool {
	if x != nil {
		return x.CenterEstop
	}
	return false
}

func (x *GetSafetyStatusResponse) GetPowerButtonEstop() bool {
	if x != nil {
		return x.PowerButtonEstop
	}
	return false
}

func (x *GetSafetyStatusResponse) GetLeftLpsuInterlock() bool {
	if x != nil {
		return x.LeftLpsuInterlock
	}
	return false
}

func (x *GetSafetyStatusResponse) GetRightLpsuInterlock() bool {
	if x != nil {
		return x.RightLpsuInterlock
	}
	return false
}

func (x *GetSafetyStatusResponse) GetDebugMode() bool {
	if x != nil {
		return x.DebugMode
	}
	return false
}

type GeoLLA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat         float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng         float64 `protobuf:"fixed64,2,opt,name=lng,proto3" json:"lng,omitempty"`
	Alt         float64 `protobuf:"fixed64,3,opt,name=alt,proto3" json:"alt,omitempty"`
	TimestampMs int64   `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GeoLLA) Reset() {
	*x = GeoLLA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeoLLA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoLLA) ProtoMessage() {}

func (x *GeoLLA) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoLLA.ProtoReflect.Descriptor instead.
func (*GeoLLA) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{24}
}

func (x *GeoLLA) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *GeoLLA) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

func (x *GeoLLA) GetAlt() float64 {
	if x != nil {
		return x.Alt
	}
	return 0
}

func (x *GeoLLA) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GeoECEF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X           float64 `protobuf:"fixed64,1,opt,name=x,proto3" json:"x,omitempty"`
	Y           float64 `protobuf:"fixed64,2,opt,name=y,proto3" json:"y,omitempty"`
	Z           float64 `protobuf:"fixed64,3,opt,name=z,proto3" json:"z,omitempty"`
	TimestampMs int64   `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GeoECEF) Reset() {
	*x = GeoECEF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeoECEF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoECEF) ProtoMessage() {}

func (x *GeoECEF) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoECEF.ProtoReflect.Descriptor instead.
func (*GeoECEF) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{25}
}

func (x *GeoECEF) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *GeoECEF) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *GeoECEF) GetZ() float64 {
	if x != nil {
		return x.Z
	}
	return 0
}

func (x *GeoECEF) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetGPSDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Validate bool `protobuf:"varint,1,opt,name=validate,proto3" json:"validate,omitempty"`
}

func (x *GetGPSDataRequest) Reset() {
	*x = GetGPSDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGPSDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGPSDataRequest) ProtoMessage() {}

func (x *GetGPSDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGPSDataRequest.ProtoReflect.Descriptor instead.
func (*GetGPSDataRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetGPSDataRequest) GetValidate() bool {
	if x != nil {
		return x.Validate
	}
	return false
}

type GetGPSDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lla  *GeoLLA  `protobuf:"bytes,1,opt,name=lla,proto3" json:"lla,omitempty"`
	Ecef *GeoECEF `protobuf:"bytes,2,opt,name=ecef,proto3" json:"ecef,omitempty"`
}

func (x *GetGPSDataResponse) Reset() {
	*x = GetGPSDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGPSDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGPSDataResponse) ProtoMessage() {}

func (x *GetGPSDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGPSDataResponse.ProtoReflect.Descriptor instead.
func (*GetGPSDataResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetGPSDataResponse) GetLla() *GeoLLA {
	if x != nil {
		return x.Lla
	}
	return nil
}

func (x *GetGPSDataResponse) GetEcef() *GeoECEF {
	if x != nil {
		return x.Ecef
	}
	return nil
}

type GetNextGPSDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextGPSDataRequest) Reset() {
	*x = GetNextGPSDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextGPSDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextGPSDataRequest) ProtoMessage() {}

func (x *GetNextGPSDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextGPSDataRequest.ProtoReflect.Descriptor instead.
func (*GetNextGPSDataRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetNextGPSDataRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type GetNextGPSDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lla *GeoLLA `protobuf:"bytes,1,opt,name=lla,proto3" json:"lla,omitempty"`
}

func (x *GetNextGPSDataResponse) Reset() {
	*x = GetNextGPSDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextGPSDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextGPSDataResponse) ProtoMessage() {}

func (x *GetNextGPSDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextGPSDataResponse.ProtoReflect.Descriptor instead.
func (*GetNextGPSDataResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetNextGPSDataResponse) GetLla() *GeoLLA {
	if x != nil {
		return x.Lla
	}
	return nil
}

type GetNextRawGPSDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs int64 `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextRawGPSDataRequest) Reset() {
	*x = GetNextRawGPSDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextRawGPSDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextRawGPSDataRequest) ProtoMessage() {}

func (x *GetNextRawGPSDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextRawGPSDataRequest.ProtoReflect.Descriptor instead.
func (*GetNextRawGPSDataRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetNextRawGPSDataRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type ValueWithAccuracy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	Accuracy float64 `protobuf:"fixed64,2,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
}

func (x *ValueWithAccuracy) Reset() {
	*x = ValueWithAccuracy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueWithAccuracy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueWithAccuracy) ProtoMessage() {}

func (x *ValueWithAccuracy) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueWithAccuracy.ProtoReflect.Descriptor instead.
func (*ValueWithAccuracy) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{31}
}

func (x *ValueWithAccuracy) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ValueWithAccuracy) GetAccuracy() float64 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

// Relative position information from secondary gps on dual gps board
type DualGpsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GnssValid       bool             `protobuf:"varint,1,opt,name=gnss_valid,json=gnssValid,proto3" json:"gnss_valid,omitempty"`
	DiffCorrections bool             `protobuf:"varint,2,opt,name=diff_corrections,json=diffCorrections,proto3" json:"diff_corrections,omitempty"`
	IsMovingBase    bool             `protobuf:"varint,3,opt,name=is_moving_base,json=isMovingBase,proto3" json:"is_moving_base,omitempty"`
	CarrierPhase    CarrierPhaseSoln `protobuf:"varint,4,opt,name=carrier_phase,json=carrierPhase,proto3,enum=hardware_manager.CarrierPhaseSoln" json:"carrier_phase,omitempty"`
	// Timestamp of this fix (if valid)
	TimestampMs uint64 `protobuf:"varint,5,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	// North component of relative position vector (mm)
	North *ValueWithAccuracy `protobuf:"bytes,6,opt,name=north,proto3,oneof" json:"north,omitempty"`
	// East component of relative position vector (mm)
	East *ValueWithAccuracy `protobuf:"bytes,7,opt,name=east,proto3,oneof" json:"east,omitempty"`
	// Down component of relative position vector (mm)
	Down *ValueWithAccuracy `protobuf:"bytes,8,opt,name=down,proto3,oneof" json:"down,omitempty"`
	// Relative position vector, the distance between antennas (mm)
	Length *ValueWithAccuracy `protobuf:"bytes,9,opt,name=length,proto3,oneof" json:"length,omitempty"`
	// Heading of travel (°)
	Heading *ValueWithAccuracy `protobuf:"bytes,10,opt,name=heading,proto3,oneof" json:"heading,omitempty"`
}

func (x *DualGpsData) Reset() {
	*x = DualGpsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DualGpsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DualGpsData) ProtoMessage() {}

func (x *DualGpsData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DualGpsData.ProtoReflect.Descriptor instead.
func (*DualGpsData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{32}
}

func (x *DualGpsData) GetGnssValid() bool {
	if x != nil {
		return x.GnssValid
	}
	return false
}

func (x *DualGpsData) GetDiffCorrections() bool {
	if x != nil {
		return x.DiffCorrections
	}
	return false
}

func (x *DualGpsData) GetIsMovingBase() bool {
	if x != nil {
		return x.IsMovingBase
	}
	return false
}

func (x *DualGpsData) GetCarrierPhase() CarrierPhaseSoln {
	if x != nil {
		return x.CarrierPhase
	}
	return CarrierPhaseSoln_NONE
}

func (x *DualGpsData) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *DualGpsData) GetNorth() *ValueWithAccuracy {
	if x != nil {
		return x.North
	}
	return nil
}

func (x *DualGpsData) GetEast() *ValueWithAccuracy {
	if x != nil {
		return x.East
	}
	return nil
}

func (x *DualGpsData) GetDown() *ValueWithAccuracy {
	if x != nil {
		return x.Down
	}
	return nil
}

func (x *DualGpsData) GetLength() *ValueWithAccuracy {
	if x != nil {
		return x.Length
	}
	return nil
}

func (x *DualGpsData) GetHeading() *ValueWithAccuracy {
	if x != nil {
		return x.Heading
	}
	return nil
}

type GetNextRawGPSDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HaveFix   bool    `protobuf:"varint,1,opt,name=have_fix,json=haveFix,proto3" json:"have_fix,omitempty"`
	Latitude  float64 `protobuf:"fixed64,2,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude float64 `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
	NumSats   int32   `protobuf:"varint,4,opt,name=num_sats,json=numSats,proto3" json:"num_sats,omitempty"`
	Hdop      float32 `protobuf:"fixed32,5,opt,name=hdop,proto3" json:"hdop,omitempty"`
	// Only valid if fix is present
	TimestampMs     uint64           `protobuf:"varint,6,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	HeightMm        int32            `protobuf:"varint,7,opt,name=height_mm,json=heightMm,proto3" json:"height_mm,omitempty"`
	HaveApproxFix   bool             `protobuf:"varint,8,opt,name=have_approx_fix,json=haveApproxFix,proto3" json:"have_approx_fix,omitempty"`
	FixType         FixType          `protobuf:"varint,9,opt,name=fix_type,json=fixType,proto3,enum=hardware_manager.FixType" json:"fix_type,omitempty"`
	GnssValid       bool             `protobuf:"varint,10,opt,name=gnss_valid,json=gnssValid,proto3" json:"gnss_valid,omitempty"`
	DiffCorrections bool             `protobuf:"varint,11,opt,name=diff_corrections,json=diffCorrections,proto3" json:"diff_corrections,omitempty"`
	CarrierPhase    CarrierPhaseSoln `protobuf:"varint,12,opt,name=carrier_phase,json=carrierPhase,proto3,enum=hardware_manager.CarrierPhaseSoln" json:"carrier_phase,omitempty"`
	Dual            *DualGpsData     `protobuf:"bytes,13,opt,name=dual,proto3,oneof" json:"dual,omitempty"`
}

func (x *GetNextRawGPSDataResponse) Reset() {
	*x = GetNextRawGPSDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextRawGPSDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextRawGPSDataResponse) ProtoMessage() {}

func (x *GetNextRawGPSDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextRawGPSDataResponse.ProtoReflect.Descriptor instead.
func (*GetNextRawGPSDataResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetNextRawGPSDataResponse) GetHaveFix() bool {
	if x != nil {
		return x.HaveFix
	}
	return false
}

func (x *GetNextRawGPSDataResponse) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetNextRawGPSDataResponse) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetNextRawGPSDataResponse) GetNumSats() int32 {
	if x != nil {
		return x.NumSats
	}
	return 0
}

func (x *GetNextRawGPSDataResponse) GetHdop() float32 {
	if x != nil {
		return x.Hdop
	}
	return 0
}

func (x *GetNextRawGPSDataResponse) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetNextRawGPSDataResponse) GetHeightMm() int32 {
	if x != nil {
		return x.HeightMm
	}
	return 0
}

func (x *GetNextRawGPSDataResponse) GetHaveApproxFix() bool {
	if x != nil {
		return x.HaveApproxFix
	}
	return false
}

func (x *GetNextRawGPSDataResponse) GetFixType() FixType {
	if x != nil {
		return x.FixType
	}
	return FixType_NO_FIX
}

func (x *GetNextRawGPSDataResponse) GetGnssValid() bool {
	if x != nil {
		return x.GnssValid
	}
	return false
}

func (x *GetNextRawGPSDataResponse) GetDiffCorrections() bool {
	if x != nil {
		return x.DiffCorrections
	}
	return false
}

func (x *GetNextRawGPSDataResponse) GetCarrierPhase() CarrierPhaseSoln {
	if x != nil {
		return x.CarrierPhase
	}
	return CarrierPhaseSoln_NONE
}

func (x *GetNextRawGPSDataResponse) GetDual() *DualGpsData {
	if x != nil {
		return x.Dual
	}
	return nil
}

type GetGPSFixedPosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetGPSFixedPosRequest) Reset() {
	*x = GetGPSFixedPosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGPSFixedPosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGPSFixedPosRequest) ProtoMessage() {}

func (x *GetGPSFixedPosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGPSFixedPosRequest.ProtoReflect.Descriptor instead.
func (*GetGPSFixedPosRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{34}
}

type GetGPSFixedPosResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XMm float32 `protobuf:"fixed32,1,opt,name=x_mm,json=xMm,proto3" json:"x_mm,omitempty"`
	YMm float32 `protobuf:"fixed32,2,opt,name=y_mm,json=yMm,proto3" json:"y_mm,omitempty"`
}

func (x *GetGPSFixedPosResponse) Reset() {
	*x = GetGPSFixedPosResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGPSFixedPosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGPSFixedPosResponse) ProtoMessage() {}

func (x *GetGPSFixedPosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGPSFixedPosResponse.ProtoReflect.Descriptor instead.
func (*GetGPSFixedPosResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetGPSFixedPosResponse) GetXMm() float32 {
	if x != nil {
		return x.XMm
	}
	return 0
}

func (x *GetGPSFixedPosResponse) GetYMm() float32 {
	if x != nil {
		return x.YMm
	}
	return 0
}

type GetManagedBoardErrorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetManagedBoardErrorsRequest) Reset() {
	*x = GetManagedBoardErrorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetManagedBoardErrorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManagedBoardErrorsRequest) ProtoMessage() {}

func (x *GetManagedBoardErrorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManagedBoardErrorsRequest.ProtoReflect.Descriptor instead.
func (*GetManagedBoardErrorsRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{36}
}

type GetManagedBoardErrorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Board            []string `protobuf:"bytes,1,rep,name=board,proto3" json:"board,omitempty"`
	EncoderErrorFlag bool     `protobuf:"varint,2,opt,name=encoder_error_flag,json=encoderErrorFlag,proto3" json:"encoder_error_flag,omitempty"`
	EncoderErrorMsg  string   `protobuf:"bytes,3,opt,name=encoder_error_msg,json=encoderErrorMsg,proto3" json:"encoder_error_msg,omitempty"`
	GpsHasFix        bool     `protobuf:"varint,4,opt,name=gps_has_fix,json=gpsHasFix,proto3" json:"gps_has_fix,omitempty"`
}

func (x *GetManagedBoardErrorsResponse) Reset() {
	*x = GetManagedBoardErrorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetManagedBoardErrorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManagedBoardErrorsResponse) ProtoMessage() {}

func (x *GetManagedBoardErrorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManagedBoardErrorsResponse.ProtoReflect.Descriptor instead.
func (*GetManagedBoardErrorsResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetManagedBoardErrorsResponse) GetBoard() []string {
	if x != nil {
		return x.Board
	}
	return nil
}

func (x *GetManagedBoardErrorsResponse) GetEncoderErrorFlag() bool {
	if x != nil {
		return x.EncoderErrorFlag
	}
	return false
}

func (x *GetManagedBoardErrorsResponse) GetEncoderErrorMsg() string {
	if x != nil {
		return x.EncoderErrorMsg
	}
	return ""
}

func (x *GetManagedBoardErrorsResponse) GetGpsHasFix() bool {
	if x != nil {
		return x.GpsHasFix
	}
	return false
}

type GetSupervisoryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSupervisoryStatusRequest) Reset() {
	*x = GetSupervisoryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupervisoryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupervisoryStatusRequest) ProtoMessage() {}

func (x *GetSupervisoryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupervisoryStatusRequest.ProtoReflect.Descriptor instead.
func (*GetSupervisoryStatusRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{38}
}

type GetReaperSupervisoryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetReaperSupervisoryStatusRequest) Reset() {
	*x = GetReaperSupervisoryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReaperSupervisoryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReaperSupervisoryStatusRequest) ProtoMessage() {}

func (x *GetReaperSupervisoryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReaperSupervisoryStatusRequest.ProtoReflect.Descriptor instead.
func (*GetReaperSupervisoryStatusRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{39}
}

type ChillerAlarms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LowLevelInTank                                 bool `protobuf:"varint,1,opt,name=low_level_in_tank,json=lowLevelInTank,proto3" json:"low_level_in_tank,omitempty"`
	HighCirculatingFluidDischargeTemp              bool `protobuf:"varint,2,opt,name=high_circulating_fluid_discharge_temp,json=highCirculatingFluidDischargeTemp,proto3" json:"high_circulating_fluid_discharge_temp,omitempty"`
	CirculatingFluidDischargeTempRise              bool `protobuf:"varint,3,opt,name=circulating_fluid_discharge_temp_rise,json=circulatingFluidDischargeTempRise,proto3" json:"circulating_fluid_discharge_temp_rise,omitempty"`
	CirculatingFluidDischargeTempDrop              bool `protobuf:"varint,4,opt,name=circulating_fluid_discharge_temp_drop,json=circulatingFluidDischargeTempDrop,proto3" json:"circulating_fluid_discharge_temp_drop,omitempty"`
	HighCirculatingFluidReturnTemp                 bool `protobuf:"varint,5,opt,name=high_circulating_fluid_return_temp,json=highCirculatingFluidReturnTemp,proto3" json:"high_circulating_fluid_return_temp,omitempty"`
	CirculatingFluidDischargePressureRise          bool `protobuf:"varint,6,opt,name=circulating_fluid_discharge_pressure_rise,json=circulatingFluidDischargePressureRise,proto3" json:"circulating_fluid_discharge_pressure_rise,omitempty"`
	CirculatingFluidDischargePressureDrop          bool `protobuf:"varint,7,opt,name=circulating_fluid_discharge_pressure_drop,json=circulatingFluidDischargePressureDrop,proto3" json:"circulating_fluid_discharge_pressure_drop,omitempty"`
	HighCompressorSuctionTemp                      bool `protobuf:"varint,8,opt,name=high_compressor_suction_temp,json=highCompressorSuctionTemp,proto3" json:"high_compressor_suction_temp,omitempty"`
	LowCompressorSuctionTemp                       bool `protobuf:"varint,9,opt,name=low_compressor_suction_temp,json=lowCompressorSuctionTemp,proto3" json:"low_compressor_suction_temp,omitempty"`
	LowSuperHeatTemp                               bool `protobuf:"varint,10,opt,name=low_super_heat_temp,json=lowSuperHeatTemp,proto3" json:"low_super_heat_temp,omitempty"`
	HighCompressorDischargePressure                bool `protobuf:"varint,11,opt,name=high_compressor_discharge_pressure,json=highCompressorDischargePressure,proto3" json:"high_compressor_discharge_pressure,omitempty"`
	RefrigerantCircutPressureHighDrop              bool `protobuf:"varint,12,opt,name=refrigerant_circut_pressure_high_drop,json=refrigerantCircutPressureHighDrop,proto3" json:"refrigerant_circut_pressure_high_drop,omitempty"`
	RefrigerantCircutPressureLowRise               bool `protobuf:"varint,13,opt,name=refrigerant_circut_pressure_low_rise,json=refrigerantCircutPressureLowRise,proto3" json:"refrigerant_circut_pressure_low_rise,omitempty"`
	RefrigerantCircutPressureLowDrop               bool `protobuf:"varint,14,opt,name=refrigerant_circut_pressure_low_drop,json=refrigerantCircutPressureLowDrop,proto3" json:"refrigerant_circut_pressure_low_drop,omitempty"`
	CompressorRunningFailure                       bool `protobuf:"varint,15,opt,name=compressor_running_failure,json=compressorRunningFailure,proto3" json:"compressor_running_failure,omitempty"`
	CommunicationError                             bool `protobuf:"varint,16,opt,name=communication_error,json=communicationError,proto3" json:"communication_error,omitempty"`
	MemoryError                                    bool `protobuf:"varint,17,opt,name=memory_error,json=memoryError,proto3" json:"memory_error,omitempty"`
	DcLineFuseCut                                  bool `protobuf:"varint,18,opt,name=dc_line_fuse_cut,json=dcLineFuseCut,proto3" json:"dc_line_fuse_cut,omitempty"`
	CirculatingFluidDischargeTempSensorFailure     bool `protobuf:"varint,19,opt,name=circulating_fluid_discharge_temp_sensor_failure,json=circulatingFluidDischargeTempSensorFailure,proto3" json:"circulating_fluid_discharge_temp_sensor_failure,omitempty"`
	CirculatingFluidReturnTempSensorFailure        bool `protobuf:"varint,20,opt,name=circulating_fluid_return_temp_sensor_failure,json=circulatingFluidReturnTempSensorFailure,proto3" json:"circulating_fluid_return_temp_sensor_failure,omitempty"`
	CirculatingFluidSuctionTempSensorFailure       bool `protobuf:"varint,21,opt,name=circulating_fluid_suction_temp_sensor_failure,json=circulatingFluidSuctionTempSensorFailure,proto3" json:"circulating_fluid_suction_temp_sensor_failure,omitempty"`
	CirculatingFluidDischargePressureSensorFailure bool `protobuf:"varint,22,opt,name=circulating_fluid_discharge_pressure_sensor_failure,json=circulatingFluidDischargePressureSensorFailure,proto3" json:"circulating_fluid_discharge_pressure_sensor_failure,omitempty"`
	CompressorDischargePressureSensorFailure       bool `protobuf:"varint,23,opt,name=compressor_discharge_pressure_sensor_failure,json=compressorDischargePressureSensorFailure,proto3" json:"compressor_discharge_pressure_sensor_failure,omitempty"`
	CompressorSuctionPressureSensorFailure         bool `protobuf:"varint,24,opt,name=compressor_suction_pressure_sensor_failure,json=compressorSuctionPressureSensorFailure,proto3" json:"compressor_suction_pressure_sensor_failure,omitempty"`
	PumpMaintenance                                bool `protobuf:"varint,25,opt,name=pump_maintenance,json=pumpMaintenance,proto3" json:"pump_maintenance,omitempty"`
	FanMaintenance                                 bool `protobuf:"varint,26,opt,name=fan_maintenance,json=fanMaintenance,proto3" json:"fan_maintenance,omitempty"`
	CompressorMaintenance                          bool `protobuf:"varint,27,opt,name=compressor_maintenance,json=compressorMaintenance,proto3" json:"compressor_maintenance,omitempty"`
	ContactInput_1SignalDetection                  bool `protobuf:"varint,28,opt,name=contact_input_1_signal_detection,json=contactInput1SignalDetection,proto3" json:"contact_input_1_signal_detection,omitempty"`
	ContactInput_2SignalDetection                  bool `protobuf:"varint,29,opt,name=contact_input_2_signal_detection,json=contactInput2SignalDetection,proto3" json:"contact_input_2_signal_detection,omitempty"`
	CompressorDischargeTempSensorFailure           bool `protobuf:"varint,30,opt,name=compressor_discharge_temp_sensor_failure,json=compressorDischargeTempSensorFailure,proto3" json:"compressor_discharge_temp_sensor_failure,omitempty"`
	CompressorDischargeTempRise                    bool `protobuf:"varint,31,opt,name=compressor_discharge_temp_rise,json=compressorDischargeTempRise,proto3" json:"compressor_discharge_temp_rise,omitempty"`
	DustproofFilterMaintenance                     bool `protobuf:"varint,32,opt,name=dustproof_filter_maintenance,json=dustproofFilterMaintenance,proto3" json:"dustproof_filter_maintenance,omitempty"`
	PowerStoppage                                  bool `protobuf:"varint,33,opt,name=power_stoppage,json=powerStoppage,proto3" json:"power_stoppage,omitempty"`
	CompressorWaiting                              bool `protobuf:"varint,34,opt,name=compressor_waiting,json=compressorWaiting,proto3" json:"compressor_waiting,omitempty"`
	FanFailure                                     bool `protobuf:"varint,35,opt,name=fan_failure,json=fanFailure,proto3" json:"fan_failure,omitempty"`
	CompressorOverCurrent                          bool `protobuf:"varint,36,opt,name=compressor_over_current,json=compressorOverCurrent,proto3" json:"compressor_over_current,omitempty"`
	PumpOverCurrent                                bool `protobuf:"varint,37,opt,name=pump_over_current,json=pumpOverCurrent,proto3" json:"pump_over_current,omitempty"`
	AirExhaustFanStoppage                          bool `protobuf:"varint,38,opt,name=air_exhaust_fan_stoppage,json=airExhaustFanStoppage,proto3" json:"air_exhaust_fan_stoppage,omitempty"`
	IncorrectPhaseError                            bool `protobuf:"varint,39,opt,name=incorrect_phase_error,json=incorrectPhaseError,proto3" json:"incorrect_phase_error,omitempty"`
	PhaseBoardOverCurrent                          bool `protobuf:"varint,40,opt,name=phase_board_over_current,json=phaseBoardOverCurrent,proto3" json:"phase_board_over_current,omitempty"`
}

func (x *ChillerAlarms) Reset() {
	*x = ChillerAlarms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChillerAlarms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChillerAlarms) ProtoMessage() {}

func (x *ChillerAlarms) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChillerAlarms.ProtoReflect.Descriptor instead.
func (*ChillerAlarms) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{40}
}

func (x *ChillerAlarms) GetLowLevelInTank() bool {
	if x != nil {
		return x.LowLevelInTank
	}
	return false
}

func (x *ChillerAlarms) GetHighCirculatingFluidDischargeTemp() bool {
	if x != nil {
		return x.HighCirculatingFluidDischargeTemp
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidDischargeTempRise() bool {
	if x != nil {
		return x.CirculatingFluidDischargeTempRise
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidDischargeTempDrop() bool {
	if x != nil {
		return x.CirculatingFluidDischargeTempDrop
	}
	return false
}

func (x *ChillerAlarms) GetHighCirculatingFluidReturnTemp() bool {
	if x != nil {
		return x.HighCirculatingFluidReturnTemp
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidDischargePressureRise() bool {
	if x != nil {
		return x.CirculatingFluidDischargePressureRise
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidDischargePressureDrop() bool {
	if x != nil {
		return x.CirculatingFluidDischargePressureDrop
	}
	return false
}

func (x *ChillerAlarms) GetHighCompressorSuctionTemp() bool {
	if x != nil {
		return x.HighCompressorSuctionTemp
	}
	return false
}

func (x *ChillerAlarms) GetLowCompressorSuctionTemp() bool {
	if x != nil {
		return x.LowCompressorSuctionTemp
	}
	return false
}

func (x *ChillerAlarms) GetLowSuperHeatTemp() bool {
	if x != nil {
		return x.LowSuperHeatTemp
	}
	return false
}

func (x *ChillerAlarms) GetHighCompressorDischargePressure() bool {
	if x != nil {
		return x.HighCompressorDischargePressure
	}
	return false
}

func (x *ChillerAlarms) GetRefrigerantCircutPressureHighDrop() bool {
	if x != nil {
		return x.RefrigerantCircutPressureHighDrop
	}
	return false
}

func (x *ChillerAlarms) GetRefrigerantCircutPressureLowRise() bool {
	if x != nil {
		return x.RefrigerantCircutPressureLowRise
	}
	return false
}

func (x *ChillerAlarms) GetRefrigerantCircutPressureLowDrop() bool {
	if x != nil {
		return x.RefrigerantCircutPressureLowDrop
	}
	return false
}

func (x *ChillerAlarms) GetCompressorRunningFailure() bool {
	if x != nil {
		return x.CompressorRunningFailure
	}
	return false
}

func (x *ChillerAlarms) GetCommunicationError() bool {
	if x != nil {
		return x.CommunicationError
	}
	return false
}

func (x *ChillerAlarms) GetMemoryError() bool {
	if x != nil {
		return x.MemoryError
	}
	return false
}

func (x *ChillerAlarms) GetDcLineFuseCut() bool {
	if x != nil {
		return x.DcLineFuseCut
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidDischargeTempSensorFailure() bool {
	if x != nil {
		return x.CirculatingFluidDischargeTempSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidReturnTempSensorFailure() bool {
	if x != nil {
		return x.CirculatingFluidReturnTempSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidSuctionTempSensorFailure() bool {
	if x != nil {
		return x.CirculatingFluidSuctionTempSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetCirculatingFluidDischargePressureSensorFailure() bool {
	if x != nil {
		return x.CirculatingFluidDischargePressureSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetCompressorDischargePressureSensorFailure() bool {
	if x != nil {
		return x.CompressorDischargePressureSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetCompressorSuctionPressureSensorFailure() bool {
	if x != nil {
		return x.CompressorSuctionPressureSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetPumpMaintenance() bool {
	if x != nil {
		return x.PumpMaintenance
	}
	return false
}

func (x *ChillerAlarms) GetFanMaintenance() bool {
	if x != nil {
		return x.FanMaintenance
	}
	return false
}

func (x *ChillerAlarms) GetCompressorMaintenance() bool {
	if x != nil {
		return x.CompressorMaintenance
	}
	return false
}

func (x *ChillerAlarms) GetContactInput_1SignalDetection() bool {
	if x != nil {
		return x.ContactInput_1SignalDetection
	}
	return false
}

func (x *ChillerAlarms) GetContactInput_2SignalDetection() bool {
	if x != nil {
		return x.ContactInput_2SignalDetection
	}
	return false
}

func (x *ChillerAlarms) GetCompressorDischargeTempSensorFailure() bool {
	if x != nil {
		return x.CompressorDischargeTempSensorFailure
	}
	return false
}

func (x *ChillerAlarms) GetCompressorDischargeTempRise() bool {
	if x != nil {
		return x.CompressorDischargeTempRise
	}
	return false
}

func (x *ChillerAlarms) GetDustproofFilterMaintenance() bool {
	if x != nil {
		return x.DustproofFilterMaintenance
	}
	return false
}

func (x *ChillerAlarms) GetPowerStoppage() bool {
	if x != nil {
		return x.PowerStoppage
	}
	return false
}

func (x *ChillerAlarms) GetCompressorWaiting() bool {
	if x != nil {
		return x.CompressorWaiting
	}
	return false
}

func (x *ChillerAlarms) GetFanFailure() bool {
	if x != nil {
		return x.FanFailure
	}
	return false
}

func (x *ChillerAlarms) GetCompressorOverCurrent() bool {
	if x != nil {
		return x.CompressorOverCurrent
	}
	return false
}

func (x *ChillerAlarms) GetPumpOverCurrent() bool {
	if x != nil {
		return x.PumpOverCurrent
	}
	return false
}

func (x *ChillerAlarms) GetAirExhaustFanStoppage() bool {
	if x != nil {
		return x.AirExhaustFanStoppage
	}
	return false
}

func (x *ChillerAlarms) GetIncorrectPhaseError() bool {
	if x != nil {
		return x.IncorrectPhaseError
	}
	return false
}

func (x *ChillerAlarms) GetPhaseBoardOverCurrent() bool {
	if x != nil {
		return x.PhaseBoardOverCurrent
	}
	return false
}

type GetSupervisoryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WaterProtectStatus    bool    `protobuf:"varint,1,opt,name=water_protect_status,json=waterProtectStatus,proto3" json:"water_protect_status,omitempty"`
	MainContactorStatusFb bool    `protobuf:"varint,2,opt,name=main_contactor_status_fb,json=mainContactorStatusFb,proto3" json:"main_contactor_status_fb,omitempty"`
	PowerGood             bool    `protobuf:"varint,3,opt,name=power_good,json=powerGood,proto3" json:"power_good,omitempty"`
	PowerBad              bool    `protobuf:"varint,4,opt,name=power_bad,json=powerBad,proto3" json:"power_bad,omitempty"`
	PowerVeryBad          bool    `protobuf:"varint,5,opt,name=power_very_bad,json=powerVeryBad,proto3" json:"power_very_bad,omitempty"`
	LiftedStatus          bool    `protobuf:"varint,6,opt,name=lifted_status,json=liftedStatus,proto3" json:"lifted_status,omitempty"`
	TempHumidityStatus    bool    `protobuf:"varint,7,opt,name=temp_humidity_status,json=tempHumidityStatus,proto3" json:"temp_humidity_status,omitempty"`
	TractorPower          bool    `protobuf:"varint,8,opt,name=tractor_power,json=tractorPower,proto3" json:"tractor_power,omitempty"`
	AcFrequency           float64 `protobuf:"fixed64,9,opt,name=ac_frequency,json=acFrequency,proto3" json:"ac_frequency,omitempty"`
	AcVoltageAB           float64 `protobuf:"fixed64,10,opt,name=ac_voltage_a_b,json=acVoltageAB,proto3" json:"ac_voltage_a_b,omitempty"`
	AcVoltageBC           float64 `protobuf:"fixed64,11,opt,name=ac_voltage_b_c,json=acVoltageBC,proto3" json:"ac_voltage_b_c,omitempty"`
	AcVoltageAC           float64 `protobuf:"fixed64,12,opt,name=ac_voltage_a_c,json=acVoltageAC,proto3" json:"ac_voltage_a_c,omitempty"`
	AcVoltageA            float64 `protobuf:"fixed64,13,opt,name=ac_voltage_a,json=acVoltageA,proto3" json:"ac_voltage_a,omitempty"`
	AcVoltageB            float64 `protobuf:"fixed64,14,opt,name=ac_voltage_b,json=acVoltageB,proto3" json:"ac_voltage_b,omitempty"`
	AcVoltageC            float64 `protobuf:"fixed64,15,opt,name=ac_voltage_c,json=acVoltageC,proto3" json:"ac_voltage_c,omitempty"`
	PhasePowerW_3         int64   `protobuf:"varint,16,opt,name=phase_power_w_3,json=phasePowerW3,proto3" json:"phase_power_w_3,omitempty"`
	PhasePowerVa_3        int64   `protobuf:"varint,17,opt,name=phase_power_va_3,json=phasePowerVa3,proto3" json:"phase_power_va_3,omitempty"`
	PowerFactor           float64 `protobuf:"fixed64,18,opt,name=power_factor,json=powerFactor,proto3" json:"power_factor,omitempty"`
	ServerCabinetTemp     float64 `protobuf:"fixed64,19,opt,name=server_cabinet_temp,json=serverCabinetTemp,proto3" json:"server_cabinet_temp,omitempty"`
	ServerCabinetHumidity float64 `protobuf:"fixed64,20,opt,name=server_cabinet_humidity,json=serverCabinetHumidity,proto3" json:"server_cabinet_humidity,omitempty"`
	BatteryVoltage_12V    float64 `protobuf:"fixed64,21,opt,name=battery_voltage_12v,json=batteryVoltage12v,proto3" json:"battery_voltage_12v,omitempty"`
	// Deprecated: Marked as deprecated in hardware_manager/proto/hardware_manager_service.proto.
	TempHumidityBypassStatus bool           `protobuf:"varint,22,opt,name=temp_humidity_bypass_status,json=tempHumidityBypassStatus,proto3" json:"temp_humidity_bypass_status,omitempty"` // Deprecated
	TempBypassStatus         bool           `protobuf:"varint,23,opt,name=temp_bypass_status,json=tempBypassStatus,proto3" json:"temp_bypass_status,omitempty"`
	HumidityBypassStatus     bool           `protobuf:"varint,24,opt,name=humidity_bypass_status,json=humidityBypassStatus,proto3" json:"humidity_bypass_status,omitempty"`
	TempStatus               bool           `protobuf:"varint,25,opt,name=temp_status,json=tempStatus,proto3" json:"temp_status,omitempty"`
	HumidityStatus           bool           `protobuf:"varint,26,opt,name=humidity_status,json=humidityStatus,proto3" json:"humidity_status,omitempty"`
	BtlDisabled              []bool         `protobuf:"varint,27,rep,packed,name=btl_disabled,json=btlDisabled,proto3" json:"btl_disabled,omitempty"`
	ServerDisabled           []bool         `protobuf:"varint,28,rep,packed,name=server_disabled,json=serverDisabled,proto3" json:"server_disabled,omitempty"`
	ScannersDisabled         []bool         `protobuf:"varint,29,rep,packed,name=scanners_disabled,json=scannersDisabled,proto3" json:"scanners_disabled,omitempty"`
	WheelEncoderDisabled     bool           `protobuf:"varint,30,opt,name=wheel_encoder_disabled,json=wheelEncoderDisabled,proto3" json:"wheel_encoder_disabled,omitempty"`
	StrobeDisabled           bool           `protobuf:"varint,31,opt,name=strobe_disabled,json=strobeDisabled,proto3" json:"strobe_disabled,omitempty"`
	GpsDisabled              bool           `protobuf:"varint,32,opt,name=gps_disabled,json=gpsDisabled,proto3" json:"gps_disabled,omitempty"`
	MainContactorDisabled    bool           `protobuf:"varint,33,opt,name=main_contactor_disabled,json=mainContactorDisabled,proto3" json:"main_contactor_disabled,omitempty"`
	AirConditionerDisabled   bool           `protobuf:"varint,34,opt,name=air_conditioner_disabled,json=airConditionerDisabled,proto3" json:"air_conditioner_disabled,omitempty"`
	ChillerDisabled          bool           `protobuf:"varint,35,opt,name=chiller_disabled,json=chillerDisabled,proto3" json:"chiller_disabled,omitempty"`
	ChillerTemp              float64        `protobuf:"fixed64,36,opt,name=chiller_temp,json=chillerTemp,proto3" json:"chiller_temp,omitempty"`
	ChillerFlow              float64        `protobuf:"fixed64,37,opt,name=chiller_flow,json=chillerFlow,proto3" json:"chiller_flow,omitempty"`
	ChillerPressure          float64        `protobuf:"fixed64,38,opt,name=chiller_pressure,json=chillerPressure,proto3" json:"chiller_pressure,omitempty"`
	ChillerConductivity      float64        `protobuf:"fixed64,39,opt,name=chiller_conductivity,json=chillerConductivity,proto3" json:"chiller_conductivity,omitempty"`
	ChillerSetTemp           float64        `protobuf:"fixed64,40,opt,name=chiller_set_temp,json=chillerSetTemp,proto3" json:"chiller_set_temp,omitempty"`
	ChillerAlarms            *ChillerAlarms `protobuf:"bytes,41,opt,name=chiller_alarms,json=chillerAlarms,proto3" json:"chiller_alarms,omitempty"`
}

func (x *GetSupervisoryStatusResponse) Reset() {
	*x = GetSupervisoryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupervisoryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupervisoryStatusResponse) ProtoMessage() {}

func (x *GetSupervisoryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupervisoryStatusResponse.ProtoReflect.Descriptor instead.
func (*GetSupervisoryStatusResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetSupervisoryStatusResponse) GetWaterProtectStatus() bool {
	if x != nil {
		return x.WaterProtectStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetMainContactorStatusFb() bool {
	if x != nil {
		return x.MainContactorStatusFb
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetPowerGood() bool {
	if x != nil {
		return x.PowerGood
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetPowerBad() bool {
	if x != nil {
		return x.PowerBad
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetPowerVeryBad() bool {
	if x != nil {
		return x.PowerVeryBad
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetLiftedStatus() bool {
	if x != nil {
		return x.LiftedStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetTempHumidityStatus() bool {
	if x != nil {
		return x.TempHumidityStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetTractorPower() bool {
	if x != nil {
		return x.TractorPower
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetAcFrequency() float64 {
	if x != nil {
		return x.AcFrequency
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetAcVoltageAB() float64 {
	if x != nil {
		return x.AcVoltageAB
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetAcVoltageBC() float64 {
	if x != nil {
		return x.AcVoltageBC
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetAcVoltageAC() float64 {
	if x != nil {
		return x.AcVoltageAC
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetAcVoltageA() float64 {
	if x != nil {
		return x.AcVoltageA
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetAcVoltageB() float64 {
	if x != nil {
		return x.AcVoltageB
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetAcVoltageC() float64 {
	if x != nil {
		return x.AcVoltageC
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetPhasePowerW_3() int64 {
	if x != nil {
		return x.PhasePowerW_3
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetPhasePowerVa_3() int64 {
	if x != nil {
		return x.PhasePowerVa_3
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetPowerFactor() float64 {
	if x != nil {
		return x.PowerFactor
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetServerCabinetTemp() float64 {
	if x != nil {
		return x.ServerCabinetTemp
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetServerCabinetHumidity() float64 {
	if x != nil {
		return x.ServerCabinetHumidity
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetBatteryVoltage_12V() float64 {
	if x != nil {
		return x.BatteryVoltage_12V
	}
	return 0
}

// Deprecated: Marked as deprecated in hardware_manager/proto/hardware_manager_service.proto.
func (x *GetSupervisoryStatusResponse) GetTempHumidityBypassStatus() bool {
	if x != nil {
		return x.TempHumidityBypassStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetTempBypassStatus() bool {
	if x != nil {
		return x.TempBypassStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetHumidityBypassStatus() bool {
	if x != nil {
		return x.HumidityBypassStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetTempStatus() bool {
	if x != nil {
		return x.TempStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetHumidityStatus() bool {
	if x != nil {
		return x.HumidityStatus
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetBtlDisabled() []bool {
	if x != nil {
		return x.BtlDisabled
	}
	return nil
}

func (x *GetSupervisoryStatusResponse) GetServerDisabled() []bool {
	if x != nil {
		return x.ServerDisabled
	}
	return nil
}

func (x *GetSupervisoryStatusResponse) GetScannersDisabled() []bool {
	if x != nil {
		return x.ScannersDisabled
	}
	return nil
}

func (x *GetSupervisoryStatusResponse) GetWheelEncoderDisabled() bool {
	if x != nil {
		return x.WheelEncoderDisabled
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetStrobeDisabled() bool {
	if x != nil {
		return x.StrobeDisabled
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetGpsDisabled() bool {
	if x != nil {
		return x.GpsDisabled
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetMainContactorDisabled() bool {
	if x != nil {
		return x.MainContactorDisabled
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetAirConditionerDisabled() bool {
	if x != nil {
		return x.AirConditionerDisabled
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetChillerDisabled() bool {
	if x != nil {
		return x.ChillerDisabled
	}
	return false
}

func (x *GetSupervisoryStatusResponse) GetChillerTemp() float64 {
	if x != nil {
		return x.ChillerTemp
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetChillerFlow() float64 {
	if x != nil {
		return x.ChillerFlow
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetChillerPressure() float64 {
	if x != nil {
		return x.ChillerPressure
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetChillerConductivity() float64 {
	if x != nil {
		return x.ChillerConductivity
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetChillerSetTemp() float64 {
	if x != nil {
		return x.ChillerSetTemp
	}
	return 0
}

func (x *GetSupervisoryStatusResponse) GetChillerAlarms() *ChillerAlarms {
	if x != nil {
		return x.ChillerAlarms
	}
	return nil
}

type SetServerDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId   int64 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	Disable bool  `protobuf:"varint,2,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetServerDisableRequest) Reset() {
	*x = SetServerDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetServerDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServerDisableRequest) ProtoMessage() {}

func (x *SetServerDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServerDisableRequest.ProtoReflect.Descriptor instead.
func (*SetServerDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{42}
}

func (x *SetServerDisableRequest) GetRowId() int64 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *SetServerDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetServerDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetServerDisableResponse) Reset() {
	*x = SetServerDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetServerDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetServerDisableResponse) ProtoMessage() {}

func (x *SetServerDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetServerDisableResponse.ProtoReflect.Descriptor instead.
func (*SetServerDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{43}
}

func (x *SetServerDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetBTLDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId   int64 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	Disable bool  `protobuf:"varint,2,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetBTLDisableRequest) Reset() {
	*x = SetBTLDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBTLDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBTLDisableRequest) ProtoMessage() {}

func (x *SetBTLDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBTLDisableRequest.ProtoReflect.Descriptor instead.
func (*SetBTLDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{44}
}

func (x *SetBTLDisableRequest) GetRowId() int64 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *SetBTLDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetBTLDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetBTLDisableResponse) Reset() {
	*x = SetBTLDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBTLDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBTLDisableResponse) ProtoMessage() {}

func (x *SetBTLDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBTLDisableResponse.ProtoReflect.Descriptor instead.
func (*SetBTLDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{45}
}

func (x *SetBTLDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetScannersDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId   int64 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	Disable bool  `protobuf:"varint,2,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetScannersDisableRequest) Reset() {
	*x = SetScannersDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetScannersDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetScannersDisableRequest) ProtoMessage() {}

func (x *SetScannersDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetScannersDisableRequest.ProtoReflect.Descriptor instead.
func (*SetScannersDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{46}
}

func (x *SetScannersDisableRequest) GetRowId() int64 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *SetScannersDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetScannersDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetScannersDisableResponse) Reset() {
	*x = SetScannersDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetScannersDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetScannersDisableResponse) ProtoMessage() {}

func (x *SetScannersDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetScannersDisableResponse.ProtoReflect.Descriptor instead.
func (*SetScannersDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{47}
}

func (x *SetScannersDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetWheelEncoderBoardDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetWheelEncoderBoardDisableRequest) Reset() {
	*x = SetWheelEncoderBoardDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetWheelEncoderBoardDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetWheelEncoderBoardDisableRequest) ProtoMessage() {}

func (x *SetWheelEncoderBoardDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetWheelEncoderBoardDisableRequest.ProtoReflect.Descriptor instead.
func (*SetWheelEncoderBoardDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{48}
}

func (x *SetWheelEncoderBoardDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetWheelEncoderBoardDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetWheelEncoderBoardDisableResponse) Reset() {
	*x = SetWheelEncoderBoardDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetWheelEncoderBoardDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetWheelEncoderBoardDisableResponse) ProtoMessage() {}

func (x *SetWheelEncoderBoardDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetWheelEncoderBoardDisableResponse.ProtoReflect.Descriptor instead.
func (*SetWheelEncoderBoardDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{49}
}

func (x *SetWheelEncoderBoardDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetWheelEncoderDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
	Front   bool `protobuf:"varint,2,opt,name=front,proto3" json:"front,omitempty"`
	Left    bool `protobuf:"varint,3,opt,name=left,proto3" json:"left,omitempty"`
}

func (x *SetWheelEncoderDisableRequest) Reset() {
	*x = SetWheelEncoderDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetWheelEncoderDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetWheelEncoderDisableRequest) ProtoMessage() {}

func (x *SetWheelEncoderDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetWheelEncoderDisableRequest.ProtoReflect.Descriptor instead.
func (*SetWheelEncoderDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{50}
}

func (x *SetWheelEncoderDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *SetWheelEncoderDisableRequest) GetFront() bool {
	if x != nil {
		return x.Front
	}
	return false
}

func (x *SetWheelEncoderDisableRequest) GetLeft() bool {
	if x != nil {
		return x.Left
	}
	return false
}

type SetWheelEncoderDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetWheelEncoderDisableResponse) Reset() {
	*x = SetWheelEncoderDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetWheelEncoderDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetWheelEncoderDisableResponse) ProtoMessage() {}

func (x *SetWheelEncoderDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetWheelEncoderDisableResponse.ProtoReflect.Descriptor instead.
func (*SetWheelEncoderDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{51}
}

func (x *SetWheelEncoderDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetStrobeDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetStrobeDisableRequest) Reset() {
	*x = SetStrobeDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetStrobeDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStrobeDisableRequest) ProtoMessage() {}

func (x *SetStrobeDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStrobeDisableRequest.ProtoReflect.Descriptor instead.
func (*SetStrobeDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{52}
}

func (x *SetStrobeDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetStrobeDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetStrobeDisableResponse) Reset() {
	*x = SetStrobeDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetStrobeDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStrobeDisableResponse) ProtoMessage() {}

func (x *SetStrobeDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStrobeDisableResponse.ProtoReflect.Descriptor instead.
func (*SetStrobeDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{53}
}

func (x *SetStrobeDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetGPSDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetGPSDisableRequest) Reset() {
	*x = SetGPSDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGPSDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGPSDisableRequest) ProtoMessage() {}

func (x *SetGPSDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGPSDisableRequest.ProtoReflect.Descriptor instead.
func (*SetGPSDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{54}
}

func (x *SetGPSDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetGPSDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetGPSDisableResponse) Reset() {
	*x = SetGPSDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGPSDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGPSDisableResponse) ProtoMessage() {}

func (x *SetGPSDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGPSDisableResponse.ProtoReflect.Descriptor instead.
func (*SetGPSDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{55}
}

func (x *SetGPSDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type CommandComputerPowerCycleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommandComputerPowerCycleRequest) Reset() {
	*x = CommandComputerPowerCycleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommandComputerPowerCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommandComputerPowerCycleRequest) ProtoMessage() {}

func (x *CommandComputerPowerCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommandComputerPowerCycleRequest.ProtoReflect.Descriptor instead.
func (*CommandComputerPowerCycleRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{56}
}

type CommandComputerPowerCycleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *CommandComputerPowerCycleResponse) Reset() {
	*x = CommandComputerPowerCycleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommandComputerPowerCycleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommandComputerPowerCycleResponse) ProtoMessage() {}

func (x *CommandComputerPowerCycleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommandComputerPowerCycleResponse.ProtoReflect.Descriptor instead.
func (*CommandComputerPowerCycleResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{57}
}

func (x *CommandComputerPowerCycleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SuicideSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SuicideSwitchRequest) Reset() {
	*x = SuicideSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuicideSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuicideSwitchRequest) ProtoMessage() {}

func (x *SuicideSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuicideSwitchRequest.ProtoReflect.Descriptor instead.
func (*SuicideSwitchRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{58}
}

type SuicideSwitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SuicideSwitchResponse) Reset() {
	*x = SuicideSwitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuicideSwitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuicideSwitchResponse) ProtoMessage() {}

func (x *SuicideSwitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuicideSwitchResponse.ProtoReflect.Descriptor instead.
func (*SuicideSwitchResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{59}
}

func (x *SuicideSwitchResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetMainContactorDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetMainContactorDisableRequest) Reset() {
	*x = SetMainContactorDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMainContactorDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMainContactorDisableRequest) ProtoMessage() {}

func (x *SetMainContactorDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMainContactorDisableRequest.ProtoReflect.Descriptor instead.
func (*SetMainContactorDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{60}
}

func (x *SetMainContactorDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetMainContactorDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetMainContactorDisableResponse) Reset() {
	*x = SetMainContactorDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMainContactorDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMainContactorDisableResponse) ProtoMessage() {}

func (x *SetMainContactorDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMainContactorDisableResponse.ProtoReflect.Descriptor instead.
func (*SetMainContactorDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{61}
}

func (x *SetMainContactorDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetAirConditionerDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetAirConditionerDisableRequest) Reset() {
	*x = SetAirConditionerDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAirConditionerDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAirConditionerDisableRequest) ProtoMessage() {}

func (x *SetAirConditionerDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAirConditionerDisableRequest.ProtoReflect.Descriptor instead.
func (*SetAirConditionerDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{62}
}

func (x *SetAirConditionerDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetAirConditionerDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetAirConditionerDisableResponse) Reset() {
	*x = SetAirConditionerDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAirConditionerDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAirConditionerDisableResponse) ProtoMessage() {}

func (x *SetAirConditionerDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAirConditionerDisableResponse.ProtoReflect.Descriptor instead.
func (*SetAirConditionerDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{63}
}

func (x *SetAirConditionerDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetChillerDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetChillerDisableRequest) Reset() {
	*x = SetChillerDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetChillerDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetChillerDisableRequest) ProtoMessage() {}

func (x *SetChillerDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetChillerDisableRequest.ProtoReflect.Descriptor instead.
func (*SetChillerDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{64}
}

func (x *SetChillerDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetChillerDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetChillerDisableResponse) Reset() {
	*x = SetChillerDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetChillerDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetChillerDisableResponse) ProtoMessage() {}

func (x *SetChillerDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetChillerDisableResponse.ProtoReflect.Descriptor instead.
func (*SetChillerDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{65}
}

func (x *SetChillerDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetTempBypassDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetTempBypassDisableRequest) Reset() {
	*x = SetTempBypassDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTempBypassDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTempBypassDisableRequest) ProtoMessage() {}

func (x *SetTempBypassDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTempBypassDisableRequest.ProtoReflect.Descriptor instead.
func (*SetTempBypassDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{66}
}

func (x *SetTempBypassDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetTempBypassDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetTempBypassDisableResponse) Reset() {
	*x = SetTempBypassDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTempBypassDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTempBypassDisableResponse) ProtoMessage() {}

func (x *SetTempBypassDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTempBypassDisableResponse.ProtoReflect.Descriptor instead.
func (*SetTempBypassDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{67}
}

func (x *SetTempBypassDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetHumidityBypassDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disable bool `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *SetHumidityBypassDisableRequest) Reset() {
	*x = SetHumidityBypassDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetHumidityBypassDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetHumidityBypassDisableRequest) ProtoMessage() {}

func (x *SetHumidityBypassDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetHumidityBypassDisableRequest.ProtoReflect.Descriptor instead.
func (*SetHumidityBypassDisableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{68}
}

func (x *SetHumidityBypassDisableRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type SetHumidityBypassDisableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetHumidityBypassDisableResponse) Reset() {
	*x = SetHumidityBypassDisableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetHumidityBypassDisableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetHumidityBypassDisableResponse) ProtoMessage() {}

func (x *SetHumidityBypassDisableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetHumidityBypassDisableResponse.ProtoReflect.Descriptor instead.
func (*SetHumidityBypassDisableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{69}
}

func (x *SetHumidityBypassDisableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetAvailableUSBStorageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAvailableUSBStorageRequest) Reset() {
	*x = GetAvailableUSBStorageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableUSBStorageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableUSBStorageRequest) ProtoMessage() {}

func (x *GetAvailableUSBStorageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableUSBStorageRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableUSBStorageRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{70}
}

type GetAvailableUSBStorageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Used         float32 `protobuf:"fixed32,1,opt,name=used,proto3" json:"used,omitempty"`
	Success      bool    `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	UsbAvailable bool    `protobuf:"varint,3,opt,name=usb_available,json=usbAvailable,proto3" json:"usb_available,omitempty"`
}

func (x *GetAvailableUSBStorageResponse) Reset() {
	*x = GetAvailableUSBStorageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableUSBStorageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableUSBStorageResponse) ProtoMessage() {}

func (x *GetAvailableUSBStorageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableUSBStorageResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableUSBStorageResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{71}
}

func (x *GetAvailableUSBStorageResponse) GetUsed() float32 {
	if x != nil {
		return x.Used
	}
	return 0
}

func (x *GetAvailableUSBStorageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetAvailableUSBStorageResponse) GetUsbAvailable() bool {
	if x != nil {
		return x.UsbAvailable
	}
	return false
}

type GetReadyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetReadyRequest) Reset() {
	*x = GetReadyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReadyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReadyRequest) ProtoMessage() {}

func (x *GetReadyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReadyRequest.ProtoReflect.Descriptor instead.
func (*GetReadyRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{72}
}

type GetReadyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ready bool `protobuf:"varint,1,opt,name=ready,proto3" json:"ready,omitempty"`
}

func (x *GetReadyResponse) Reset() {
	*x = GetReadyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReadyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReadyResponse) ProtoMessage() {}

func (x *GetReadyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReadyResponse.ProtoReflect.Descriptor instead.
func (*GetReadyResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{73}
}

func (x *GetReadyResponse) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

type Get240VUptimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get240VUptimeRequest) Reset() {
	*x = Get240VUptimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get240VUptimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get240VUptimeRequest) ProtoMessage() {}

func (x *Get240VUptimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get240VUptimeRequest.ProtoReflect.Descriptor instead.
func (*Get240VUptimeRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{74}
}

type Get240VUptimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UptimeS int64 `protobuf:"varint,1,opt,name=uptime_s,json=uptimeS,proto3" json:"uptime_s,omitempty"`
}

func (x *Get240VUptimeResponse) Reset() {
	*x = Get240VUptimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get240VUptimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get240VUptimeResponse) ProtoMessage() {}

func (x *Get240VUptimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get240VUptimeResponse.ProtoReflect.Descriptor instead.
func (*Get240VUptimeResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{75}
}

func (x *Get240VUptimeResponse) GetUptimeS() int64 {
	if x != nil {
		return x.UptimeS
	}
	return 0
}

type GetDeltaTravelMMRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` //uuid
}

func (x *GetDeltaTravelMMRequest) Reset() {
	*x = GetDeltaTravelMMRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeltaTravelMMRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeltaTravelMMRequest) ProtoMessage() {}

func (x *GetDeltaTravelMMRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeltaTravelMMRequest.ProtoReflect.Descriptor instead.
func (*GetDeltaTravelMMRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{76}
}

func (x *GetDeltaTravelMMRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetDeltaTravelMMResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeltaMm float64 `protobuf:"fixed64,1,opt,name=delta_mm,json=deltaMm,proto3" json:"delta_mm,omitempty"`
}

func (x *GetDeltaTravelMMResponse) Reset() {
	*x = GetDeltaTravelMMResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeltaTravelMMResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeltaTravelMMResponse) ProtoMessage() {}

func (x *GetDeltaTravelMMResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeltaTravelMMResponse.ProtoReflect.Descriptor instead.
func (*GetDeltaTravelMMResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{77}
}

func (x *GetDeltaTravelMMResponse) GetDeltaMm() float64 {
	if x != nil {
		return x.DeltaMm
	}
	return 0
}

type GetRuntimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRuntimeRequest) Reset() {
	*x = GetRuntimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRuntimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRuntimeRequest) ProtoMessage() {}

func (x *GetRuntimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRuntimeRequest.ProtoReflect.Descriptor instead.
func (*GetRuntimeRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{78}
}

type GetRuntimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Runtime_240V uint32 `protobuf:"varint,1,opt,name=runtime_240v,json=runtime240v,proto3" json:"runtime_240v,omitempty"`
}

func (x *GetRuntimeResponse) Reset() {
	*x = GetRuntimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRuntimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRuntimeResponse) ProtoMessage() {}

func (x *GetRuntimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRuntimeResponse.ProtoReflect.Descriptor instead.
func (*GetRuntimeResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{79}
}

func (x *GetRuntimeResponse) GetRuntime_240V() uint32 {
	if x != nil {
		return x.Runtime_240V
	}
	return 0
}

type GetWheelEncoderResolutionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetWheelEncoderResolutionRequest) Reset() {
	*x = GetWheelEncoderResolutionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWheelEncoderResolutionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWheelEncoderResolutionRequest) ProtoMessage() {}

func (x *GetWheelEncoderResolutionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWheelEncoderResolutionRequest.ProtoReflect.Descriptor instead.
func (*GetWheelEncoderResolutionRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{80}
}

type GetWheelEncoderResolutionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resolution uint32 `protobuf:"varint,1,opt,name=resolution,proto3" json:"resolution,omitempty"`
}

func (x *GetWheelEncoderResolutionResponse) Reset() {
	*x = GetWheelEncoderResolutionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWheelEncoderResolutionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWheelEncoderResolutionResponse) ProtoMessage() {}

func (x *GetWheelEncoderResolutionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWheelEncoderResolutionResponse.ProtoReflect.Descriptor instead.
func (*GetWheelEncoderResolutionResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{81}
}

func (x *GetWheelEncoderResolutionResponse) GetResolution() uint32 {
	if x != nil {
		return x.Resolution
	}
	return 0
}

type StrobeSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposureUs             *uint32 `protobuf:"varint,1,opt,name=exposure_us,json=exposureUs,proto3,oneof" json:"exposure_us,omitempty"`
	PeriodUs               *uint32 `protobuf:"varint,2,opt,name=period_us,json=periodUs,proto3,oneof" json:"period_us,omitempty"`
	TargetsPerPredictRatio *uint32 `protobuf:"varint,3,opt,name=targets_per_predict_ratio,json=targetsPerPredictRatio,proto3,oneof" json:"targets_per_predict_ratio,omitempty"`
}

func (x *StrobeSettings) Reset() {
	*x = StrobeSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrobeSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrobeSettings) ProtoMessage() {}

func (x *StrobeSettings) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrobeSettings.ProtoReflect.Descriptor instead.
func (*StrobeSettings) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{82}
}

func (x *StrobeSettings) GetExposureUs() uint32 {
	if x != nil && x.ExposureUs != nil {
		return *x.ExposureUs
	}
	return 0
}

func (x *StrobeSettings) GetPeriodUs() uint32 {
	if x != nil && x.PeriodUs != nil {
		return *x.PeriodUs
	}
	return 0
}

func (x *StrobeSettings) GetTargetsPerPredictRatio() uint32 {
	if x != nil && x.TargetsPerPredictRatio != nil {
		return *x.TargetsPerPredictRatio
	}
	return 0
}

type SetStrobeSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetStrobeSettingsResponse) Reset() {
	*x = SetStrobeSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetStrobeSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStrobeSettingsResponse) ProtoMessage() {}

func (x *SetStrobeSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStrobeSettingsResponse.ProtoReflect.Descriptor instead.
func (*SetStrobeSettingsResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{83}
}

func (x *SetStrobeSettingsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetStrobeSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetStrobeSettingsRequest) Reset() {
	*x = GetStrobeSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStrobeSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStrobeSettingsRequest) ProtoMessage() {}

func (x *GetStrobeSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStrobeSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetStrobeSettingsRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{84}
}

type EnvironmentalSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemperatureC float64 `protobuf:"fixed64,1,opt,name=temperature_c,json=temperatureC,proto3" json:"temperature_c,omitempty"`
	HumidityRh   float64 `protobuf:"fixed64,2,opt,name=humidity_rh,json=humidityRh,proto3" json:"humidity_rh,omitempty"`
	PressureHpa  float64 `protobuf:"fixed64,3,opt,name=pressure_hpa,json=pressureHpa,proto3" json:"pressure_hpa,omitempty"`
}

func (x *EnvironmentalSensorData) Reset() {
	*x = EnvironmentalSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnvironmentalSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvironmentalSensorData) ProtoMessage() {}

func (x *EnvironmentalSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvironmentalSensorData.ProtoReflect.Descriptor instead.
func (*EnvironmentalSensorData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{85}
}

func (x *EnvironmentalSensorData) GetTemperatureC() float64 {
	if x != nil {
		return x.TemperatureC
	}
	return 0
}

func (x *EnvironmentalSensorData) GetHumidityRh() float64 {
	if x != nil {
		return x.HumidityRh
	}
	return 0
}

func (x *EnvironmentalSensorData) GetPressureHpa() float64 {
	if x != nil {
		return x.PressureHpa
	}
	return 0
}

type CoolantSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemperatureC float64 `protobuf:"fixed64,1,opt,name=temperature_c,json=temperatureC,proto3" json:"temperature_c,omitempty"`
	PressureKpa  float64 `protobuf:"fixed64,2,opt,name=pressure_kpa,json=pressureKpa,proto3" json:"pressure_kpa,omitempty"`
}

func (x *CoolantSensorData) Reset() {
	*x = CoolantSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoolantSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoolantSensorData) ProtoMessage() {}

func (x *CoolantSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoolantSensorData.ProtoReflect.Descriptor instead.
func (*CoolantSensorData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{86}
}

func (x *CoolantSensorData) GetTemperatureC() float64 {
	if x != nil {
		return x.TemperatureC
	}
	return 0
}

func (x *CoolantSensorData) GetPressureKpa() float64 {
	if x != nil {
		return x.PressureKpa
	}
	return 0
}

type NetworkPortState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LinkUp            bool             `protobuf:"varint,1,opt,name=link_up,json=linkUp,proto3" json:"link_up,omitempty"`
	ActualLinkSpeed   NetworkLinkSpeed `protobuf:"varint,2,opt,name=actual_link_speed,json=actualLinkSpeed,proto3,enum=hardware_manager.NetworkLinkSpeed" json:"actual_link_speed,omitempty"`
	ExpectedLinkSpeed NetworkLinkSpeed `protobuf:"varint,3,opt,name=expected_link_speed,json=expectedLinkSpeed,proto3,enum=hardware_manager.NetworkLinkSpeed" json:"expected_link_speed,omitempty"`
}

func (x *NetworkPortState) Reset() {
	*x = NetworkPortState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkPortState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPortState) ProtoMessage() {}

func (x *NetworkPortState) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPortState.ProtoReflect.Descriptor instead.
func (*NetworkPortState) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{87}
}

func (x *NetworkPortState) GetLinkUp() bool {
	if x != nil {
		return x.LinkUp
	}
	return false
}

func (x *NetworkPortState) GetActualLinkSpeed() NetworkLinkSpeed {
	if x != nil {
		return x.ActualLinkSpeed
	}
	return NetworkLinkSpeed_UNKNOWN
}

func (x *NetworkPortState) GetExpectedLinkSpeed() NetworkLinkSpeed {
	if x != nil {
		return x.ExpectedLinkSpeed
	}
	return NetworkLinkSpeed_UNKNOWN
}

type ReaperPcSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemperatureCpuCoreC float64  `protobuf:"fixed64,1,opt,name=temperature_cpu_core_c,json=temperatureCpuCoreC,proto3" json:"temperature_cpu_core_c,omitempty"`
	TemperatureSystemC  float64  `protobuf:"fixed64,2,opt,name=temperature_system_c,json=temperatureSystemC,proto3" json:"temperature_system_c,omitempty"`
	TemperatureGpu_1C   *float64 `protobuf:"fixed64,3,opt,name=temperature_gpu_1_c,json=temperatureGpu1C,proto3,oneof" json:"temperature_gpu_1_c,omitempty"`
	TemperatureGpu_2C   *float64 `protobuf:"fixed64,4,opt,name=temperature_gpu_2_c,json=temperatureGpu2C,proto3,oneof" json:"temperature_gpu_2_c,omitempty"`
	Psu_12V             float64  `protobuf:"fixed64,5,opt,name=psu_12v,json=psu12v,proto3" json:"psu_12v,omitempty"`
	Psu_5V              float64  `protobuf:"fixed64,6,opt,name=psu_5v,json=psu5v,proto3" json:"psu_5v,omitempty"`
	Psu_3V3             float64  `protobuf:"fixed64,7,opt,name=psu_3v3,json=psu3v3,proto3" json:"psu_3v3,omitempty"`
	// CPU load average
	Load float64 `protobuf:"fixed64,8,opt,name=load,proto3" json:"load,omitempty"`
	// system uptime, in seconds
	Uptime uint32 `protobuf:"varint,9,opt,name=uptime,proto3" json:"uptime,omitempty"`
	// Memory (RAM) utilization
	RamUsagePercent float64 `protobuf:"fixed64,10,opt,name=ram_usage_percent,json=ramUsagePercent,proto3" json:"ram_usage_percent,omitempty"`
	// Disk space utilization
	DiskUsagePercent float64 `protobuf:"fixed64,11,opt,name=disk_usage_percent,json=diskUsagePercent,proto3" json:"disk_usage_percent,omitempty"`
	// Link state for scanner PCBs (A, B)
	ScannerLink []*NetworkPortState `protobuf:"bytes,12,rep,name=scanner_link,json=scannerLink,proto3" json:"scanner_link,omitempty"`
	// Link state for target cam (A, B)
	TargetCamLink []*NetworkPortState `protobuf:"bytes,13,rep,name=target_cam_link,json=targetCamLink,proto3" json:"target_cam_link,omitempty"`
	// Link state for predict cam
	PredictCamLink *NetworkPortState `protobuf:"bytes,14,opt,name=predict_cam_link,json=predictCamLink,proto3" json:"predict_cam_link,omitempty"`
	// IPMI port link state
	IpmiLink *NetworkPortState `protobuf:"bytes,15,opt,name=ipmi_link,json=ipmiLink,proto3" json:"ipmi_link,omitempty"`
	// Global network uplink port link state
	GlobalLink *NetworkPortState `protobuf:"bytes,16,opt,name=global_link,json=globalLink,proto3" json:"global_link,omitempty"`
	// External (internet) uplink
	ExtLink *NetworkPortState `protobuf:"bytes,17,opt,name=ext_link,json=extLink,proto3" json:"ext_link,omitempty"`
}

func (x *ReaperPcSensorData) Reset() {
	*x = ReaperPcSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperPcSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperPcSensorData) ProtoMessage() {}

func (x *ReaperPcSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperPcSensorData.ProtoReflect.Descriptor instead.
func (*ReaperPcSensorData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{88}
}

func (x *ReaperPcSensorData) GetTemperatureCpuCoreC() float64 {
	if x != nil {
		return x.TemperatureCpuCoreC
	}
	return 0
}

func (x *ReaperPcSensorData) GetTemperatureSystemC() float64 {
	if x != nil {
		return x.TemperatureSystemC
	}
	return 0
}

func (x *ReaperPcSensorData) GetTemperatureGpu_1C() float64 {
	if x != nil && x.TemperatureGpu_1C != nil {
		return *x.TemperatureGpu_1C
	}
	return 0
}

func (x *ReaperPcSensorData) GetTemperatureGpu_2C() float64 {
	if x != nil && x.TemperatureGpu_2C != nil {
		return *x.TemperatureGpu_2C
	}
	return 0
}

func (x *ReaperPcSensorData) GetPsu_12V() float64 {
	if x != nil {
		return x.Psu_12V
	}
	return 0
}

func (x *ReaperPcSensorData) GetPsu_5V() float64 {
	if x != nil {
		return x.Psu_5V
	}
	return 0
}

func (x *ReaperPcSensorData) GetPsu_3V3() float64 {
	if x != nil {
		return x.Psu_3V3
	}
	return 0
}

func (x *ReaperPcSensorData) GetLoad() float64 {
	if x != nil {
		return x.Load
	}
	return 0
}

func (x *ReaperPcSensorData) GetUptime() uint32 {
	if x != nil {
		return x.Uptime
	}
	return 0
}

func (x *ReaperPcSensorData) GetRamUsagePercent() float64 {
	if x != nil {
		return x.RamUsagePercent
	}
	return 0
}

func (x *ReaperPcSensorData) GetDiskUsagePercent() float64 {
	if x != nil {
		return x.DiskUsagePercent
	}
	return 0
}

func (x *ReaperPcSensorData) GetScannerLink() []*NetworkPortState {
	if x != nil {
		return x.ScannerLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetTargetCamLink() []*NetworkPortState {
	if x != nil {
		return x.TargetCamLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetPredictCamLink() *NetworkPortState {
	if x != nil {
		return x.PredictCamLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetIpmiLink() *NetworkPortState {
	if x != nil {
		return x.IpmiLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetGlobalLink() *NetworkPortState {
	if x != nil {
		return x.GlobalLink
	}
	return nil
}

func (x *ReaperPcSensorData) GetExtLink() *NetworkPortState {
	if x != nil {
		return x.ExtLink
	}
	return nil
}

// Info about the BWT laser connected to Reaper scanner
type ReaperScannerLaserStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Laser model number
	Model string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	// Serial number reported by laser
	Sn string `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	// Rated laser power (W)
	RatedPower uint32 `protobuf:"varint,3,opt,name=rated_power,json=ratedPower,proto3" json:"rated_power,omitempty"`
	// Internal temperature
	TemperatureC float64 `protobuf:"fixed64,4,opt,name=temperature_c,json=temperatureC,proto3" json:"temperature_c,omitempty"`
	// Laser humidity
	Humidity float64 `protobuf:"fixed64,5,opt,name=humidity,proto3" json:"humidity,omitempty"`
	// Current through laser diodes
	LaserCurrentMa float64 `protobuf:"fixed64,6,opt,name=laser_current_ma,json=laserCurrentMa,proto3" json:"laser_current_ma,omitempty"`
	// Laser faults (if any)
	Faults []string `protobuf:"bytes,7,rep,name=faults,proto3" json:"faults,omitempty"`
}

func (x *ReaperScannerLaserStatus) Reset() {
	*x = ReaperScannerLaserStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperScannerLaserStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperScannerLaserStatus) ProtoMessage() {}

func (x *ReaperScannerLaserStatus) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperScannerLaserStatus.ProtoReflect.Descriptor instead.
func (*ReaperScannerLaserStatus) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{89}
}

func (x *ReaperScannerLaserStatus) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ReaperScannerLaserStatus) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ReaperScannerLaserStatus) GetRatedPower() uint32 {
	if x != nil {
		return x.RatedPower
	}
	return 0
}

func (x *ReaperScannerLaserStatus) GetTemperatureC() float64 {
	if x != nil {
		return x.TemperatureC
	}
	return 0
}

func (x *ReaperScannerLaserStatus) GetHumidity() float64 {
	if x != nil {
		return x.Humidity
	}
	return 0
}

func (x *ReaperScannerLaserStatus) GetLaserCurrentMa() float64 {
	if x != nil {
		return x.LaserCurrentMa
	}
	return 0
}

func (x *ReaperScannerLaserStatus) GetFaults() []string {
	if x != nil {
		return x.Faults
	}
	return nil
}

// Information for each of the motors/controllers
type ReaperScannerMotorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Serial number of motor controller
	ControllerSn string `protobuf:"bytes,1,opt,name=controller_sn,json=controllerSn,proto3" json:"controller_sn,omitempty"`
	// Output/driver stage temperature
	TemperatureOutputC float64 `protobuf:"fixed64,2,opt,name=temperature_output_c,json=temperatureOutputC,proto3" json:"temperature_output_c,omitempty"`
	// Motor supply voltage
	MotorSupplyV float64 `protobuf:"fixed64,3,opt,name=motor_supply_v,json=motorSupplyV,proto3" json:"motor_supply_v,omitempty"`
	// Instantaneous motor current
	MotorCurrentA float64 `protobuf:"fixed64,4,opt,name=motor_current_a,json=motorCurrentA,proto3" json:"motor_current_a,omitempty"`
	// Current encoder position, ticks
	EncoderPosition int64 `protobuf:"varint,5,opt,name=encoder_position,json=encoderPosition,proto3" json:"encoder_position,omitempty"`
}

func (x *ReaperScannerMotorData) Reset() {
	*x = ReaperScannerMotorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperScannerMotorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperScannerMotorData) ProtoMessage() {}

func (x *ReaperScannerMotorData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperScannerMotorData.ProtoReflect.Descriptor instead.
func (*ReaperScannerMotorData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{90}
}

func (x *ReaperScannerMotorData) GetControllerSn() string {
	if x != nil {
		return x.ControllerSn
	}
	return ""
}

func (x *ReaperScannerMotorData) GetTemperatureOutputC() float64 {
	if x != nil {
		return x.TemperatureOutputC
	}
	return 0
}

func (x *ReaperScannerMotorData) GetMotorSupplyV() float64 {
	if x != nil {
		return x.MotorSupplyV
	}
	return 0
}

func (x *ReaperScannerMotorData) GetMotorCurrentA() float64 {
	if x != nil {
		return x.MotorCurrentA
	}
	return 0
}

func (x *ReaperScannerMotorData) GetEncoderPosition() int64 {
	if x != nil {
		return x.EncoderPosition
	}
	return 0
}

// Sensor data readings for the Reaper scanners
type ReaperScannerSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Scanner assembly serial number
	ScannerSn string `protobuf:"bytes,1,opt,name=scanner_sn,json=scannerSn,proto3" json:"scanner_sn,omitempty"`
	// Whether power to the scanner is on
	PowerOn bool `protobuf:"varint,2,opt,name=power_on,json=powerOn,proto3" json:"power_on,omitempty"`
	// Scanner current consumption
	CurrentA float64 `protobuf:"fixed64,3,opt,name=current_a,json=currentA,proto3" json:"current_a,omitempty"`
	// Whether fuse to scanner is tripped
	FuseTripped bool `protobuf:"varint,4,opt,name=fuse_tripped,json=fuseTripped,proto3" json:"fuse_tripped,omitempty"`
	// Temperature of collimator (°C)
	TemperatureCollimatorC float64 `protobuf:"fixed64,5,opt,name=temperature_collimator_c,json=temperatureCollimatorC,proto3" json:"temperature_collimator_c,omitempty"`
	// laser fiber connection temperature (°C)
	TemperatureFiberC float64 `protobuf:"fixed64,6,opt,name=temperature_fiber_c,json=temperatureFiberC,proto3" json:"temperature_fiber_c,omitempty"`
	// Approximate laser power reading from photodiode
	LaserPowerW float64 `protobuf:"fixed64,7,opt,name=laser_power_w,json=laserPowerW,proto3" json:"laser_power_w,omitempty"`
	// Raw laser power reading in millivolts
	LaserPowerRawMv float64 `protobuf:"fixed64,16,opt,name=laser_power_raw_mv,json=laserPowerRawMv,proto3" json:"laser_power_raw_mv,omitempty"`
	// Whether laser is connected
	LaserConnected bool `protobuf:"varint,8,opt,name=laser_connected,json=laserConnected,proto3" json:"laser_connected,omitempty"`
	// Additional laser status information
	LaserStatus *ReaperScannerLaserStatus `protobuf:"bytes,9,opt,name=laser_status,json=laserStatus,proto3,oneof" json:"laser_status,omitempty"`
	// If target cam is connected
	TargetConnected bool `protobuf:"varint,10,opt,name=target_connected,json=targetConnected,proto3" json:"target_connected,omitempty"`
	// Target camera's serial number
	TargetSn *string `protobuf:"bytes,11,opt,name=target_sn,json=targetSn,proto3,oneof" json:"target_sn,omitempty"`
	// Temperature of target camera
	TemperatureTargetC *float64 `protobuf:"fixed64,12,opt,name=temperature_target_c,json=temperatureTargetC,proto3,oneof" json:"temperature_target_c,omitempty"`
	// Individual motor controller info
	MotorPan  *ReaperScannerMotorData `protobuf:"bytes,13,opt,name=motor_pan,json=motorPan,proto3,oneof" json:"motor_pan,omitempty"`
	MotorTilt *ReaperScannerMotorData `protobuf:"bytes,14,opt,name=motor_tilt,json=motorTilt,proto3,oneof" json:"motor_tilt,omitempty"`
	// whether power to the target cam (controlled via scanner) is enabled
	TargetCamPowerEnabled bool `protobuf:"varint,15,opt,name=target_cam_power_enabled,json=targetCamPowerEnabled,proto3" json:"target_cam_power_enabled,omitempty"`
}

func (x *ReaperScannerSensorData) Reset() {
	*x = ReaperScannerSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperScannerSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperScannerSensorData) ProtoMessage() {}

func (x *ReaperScannerSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperScannerSensorData.ProtoReflect.Descriptor instead.
func (*ReaperScannerSensorData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{91}
}

func (x *ReaperScannerSensorData) GetScannerSn() string {
	if x != nil {
		return x.ScannerSn
	}
	return ""
}

func (x *ReaperScannerSensorData) GetPowerOn() bool {
	if x != nil {
		return x.PowerOn
	}
	return false
}

func (x *ReaperScannerSensorData) GetCurrentA() float64 {
	if x != nil {
		return x.CurrentA
	}
	return 0
}

func (x *ReaperScannerSensorData) GetFuseTripped() bool {
	if x != nil {
		return x.FuseTripped
	}
	return false
}

func (x *ReaperScannerSensorData) GetTemperatureCollimatorC() float64 {
	if x != nil {
		return x.TemperatureCollimatorC
	}
	return 0
}

func (x *ReaperScannerSensorData) GetTemperatureFiberC() float64 {
	if x != nil {
		return x.TemperatureFiberC
	}
	return 0
}

func (x *ReaperScannerSensorData) GetLaserPowerW() float64 {
	if x != nil {
		return x.LaserPowerW
	}
	return 0
}

func (x *ReaperScannerSensorData) GetLaserPowerRawMv() float64 {
	if x != nil {
		return x.LaserPowerRawMv
	}
	return 0
}

func (x *ReaperScannerSensorData) GetLaserConnected() bool {
	if x != nil {
		return x.LaserConnected
	}
	return false
}

func (x *ReaperScannerSensorData) GetLaserStatus() *ReaperScannerLaserStatus {
	if x != nil {
		return x.LaserStatus
	}
	return nil
}

func (x *ReaperScannerSensorData) GetTargetConnected() bool {
	if x != nil {
		return x.TargetConnected
	}
	return false
}

func (x *ReaperScannerSensorData) GetTargetSn() string {
	if x != nil && x.TargetSn != nil {
		return *x.TargetSn
	}
	return ""
}

func (x *ReaperScannerSensorData) GetTemperatureTargetC() float64 {
	if x != nil && x.TemperatureTargetC != nil {
		return *x.TemperatureTargetC
	}
	return 0
}

func (x *ReaperScannerSensorData) GetMotorPan() *ReaperScannerMotorData {
	if x != nil {
		return x.MotorPan
	}
	return nil
}

func (x *ReaperScannerSensorData) GetMotorTilt() *ReaperScannerMotorData {
	if x != nil {
		return x.MotorTilt
	}
	return nil
}

func (x *ReaperScannerSensorData) GetTargetCamPowerEnabled() bool {
	if x != nil {
		return x.TargetCamPowerEnabled
	}
	return false
}

// All readings from stuff in the center enclosure
// Similar to GetSupervisoryStatusResponse
type ReaperCenterEnclosureData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WaterProtectStatus    bool    `protobuf:"varint,1,opt,name=water_protect_status,json=waterProtectStatus,proto3" json:"water_protect_status,omitempty"`
	MainContactorStatusFb bool    `protobuf:"varint,2,opt,name=main_contactor_status_fb,json=mainContactorStatusFb,proto3" json:"main_contactor_status_fb,omitempty"`
	PowerGood             bool    `protobuf:"varint,3,opt,name=power_good,json=powerGood,proto3" json:"power_good,omitempty"`
	PowerBad              bool    `protobuf:"varint,4,opt,name=power_bad,json=powerBad,proto3" json:"power_bad,omitempty"`
	PowerVeryBad          bool    `protobuf:"varint,5,opt,name=power_very_bad,json=powerVeryBad,proto3" json:"power_very_bad,omitempty"`
	LiftedStatus          bool    `protobuf:"varint,6,opt,name=lifted_status,json=liftedStatus,proto3" json:"lifted_status,omitempty"`
	TractorPower          bool    `protobuf:"varint,7,opt,name=tractor_power,json=tractorPower,proto3" json:"tractor_power,omitempty"`
	AcFrequency           float64 `protobuf:"fixed64,8,opt,name=ac_frequency,json=acFrequency,proto3" json:"ac_frequency,omitempty"`
	AcVoltageAB           float64 `protobuf:"fixed64,9,opt,name=ac_voltage_a_b,json=acVoltageAB,proto3" json:"ac_voltage_a_b,omitempty"`
	AcVoltageBC           float64 `protobuf:"fixed64,10,opt,name=ac_voltage_b_c,json=acVoltageBC,proto3" json:"ac_voltage_b_c,omitempty"`
	AcVoltageAC           float64 `protobuf:"fixed64,11,opt,name=ac_voltage_a_c,json=acVoltageAC,proto3" json:"ac_voltage_a_c,omitempty"`
	AcVoltageA            float64 `protobuf:"fixed64,12,opt,name=ac_voltage_a,json=acVoltageA,proto3" json:"ac_voltage_a,omitempty"`
	AcVoltageB            float64 `protobuf:"fixed64,13,opt,name=ac_voltage_b,json=acVoltageB,proto3" json:"ac_voltage_b,omitempty"`
	AcVoltageC            float64 `protobuf:"fixed64,14,opt,name=ac_voltage_c,json=acVoltageC,proto3" json:"ac_voltage_c,omitempty"`
	PhasePowerW_3         int64   `protobuf:"varint,15,opt,name=phase_power_w_3,json=phasePowerW3,proto3" json:"phase_power_w_3,omitempty"`
	PhasePowerVa_3        int64   `protobuf:"varint,16,opt,name=phase_power_va_3,json=phasePowerVa3,proto3" json:"phase_power_va_3,omitempty"`
	PowerFactor           float64 `protobuf:"fixed64,17,opt,name=power_factor,json=powerFactor,proto3" json:"power_factor,omitempty"`
	ServerCabinetTemp     float64 `protobuf:"fixed64,18,opt,name=server_cabinet_temp,json=serverCabinetTemp,proto3" json:"server_cabinet_temp,omitempty"`
	ServerCabinetHumidity float64 `protobuf:"fixed64,19,opt,name=server_cabinet_humidity,json=serverCabinetHumidity,proto3" json:"server_cabinet_humidity,omitempty"`
	// ISOBUS voltage
	BatteryVoltage_12V     float64        `protobuf:"fixed64,20,opt,name=battery_voltage_12v,json=batteryVoltage12v,proto3" json:"battery_voltage_12v,omitempty"`
	WheelEncoderDisabled   bool           `protobuf:"varint,21,opt,name=wheel_encoder_disabled,json=wheelEncoderDisabled,proto3" json:"wheel_encoder_disabled,omitempty"`
	StrobeDisabled         bool           `protobuf:"varint,22,opt,name=strobe_disabled,json=strobeDisabled,proto3" json:"strobe_disabled,omitempty"`
	GpsDisabled            bool           `protobuf:"varint,23,opt,name=gps_disabled,json=gpsDisabled,proto3" json:"gps_disabled,omitempty"`
	MainContactorDisabled  bool           `protobuf:"varint,24,opt,name=main_contactor_disabled,json=mainContactorDisabled,proto3" json:"main_contactor_disabled,omitempty"`
	AirConditionerDisabled bool           `protobuf:"varint,25,opt,name=air_conditioner_disabled,json=airConditionerDisabled,proto3" json:"air_conditioner_disabled,omitempty"`
	ChillerDisabled        bool           `protobuf:"varint,26,opt,name=chiller_disabled,json=chillerDisabled,proto3" json:"chiller_disabled,omitempty"`
	ChillerTemp            float64        `protobuf:"fixed64,27,opt,name=chiller_temp,json=chillerTemp,proto3" json:"chiller_temp,omitempty"`
	ChillerFlow            float64        `protobuf:"fixed64,28,opt,name=chiller_flow,json=chillerFlow,proto3" json:"chiller_flow,omitempty"`
	ChillerPressure        float64        `protobuf:"fixed64,29,opt,name=chiller_pressure,json=chillerPressure,proto3" json:"chiller_pressure,omitempty"`
	ChillerConductivity    float64        `protobuf:"fixed64,30,opt,name=chiller_conductivity,json=chillerConductivity,proto3" json:"chiller_conductivity,omitempty"`
	ChillerSetTemp         float64        `protobuf:"fixed64,31,opt,name=chiller_set_temp,json=chillerSetTemp,proto3" json:"chiller_set_temp,omitempty"`
	ChillerHeatTransfer    float64        `protobuf:"fixed64,32,opt,name=chiller_heat_transfer,json=chillerHeatTransfer,proto3" json:"chiller_heat_transfer,omitempty"`
	ChillerFluidDeltaTemp  float64        `protobuf:"fixed64,33,opt,name=chiller_fluid_delta_temp,json=chillerFluidDeltaTemp,proto3" json:"chiller_fluid_delta_temp,omitempty"`
	ChillerAlarms          *ChillerAlarms `protobuf:"bytes,34,opt,name=chiller_alarms,json=chillerAlarms,proto3" json:"chiller_alarms,omitempty"`
}

func (x *ReaperCenterEnclosureData) Reset() {
	*x = ReaperCenterEnclosureData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperCenterEnclosureData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperCenterEnclosureData) ProtoMessage() {}

func (x *ReaperCenterEnclosureData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperCenterEnclosureData.ProtoReflect.Descriptor instead.
func (*ReaperCenterEnclosureData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{92}
}

func (x *ReaperCenterEnclosureData) GetWaterProtectStatus() bool {
	if x != nil {
		return x.WaterProtectStatus
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetMainContactorStatusFb() bool {
	if x != nil {
		return x.MainContactorStatusFb
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetPowerGood() bool {
	if x != nil {
		return x.PowerGood
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetPowerBad() bool {
	if x != nil {
		return x.PowerBad
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetPowerVeryBad() bool {
	if x != nil {
		return x.PowerVeryBad
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetLiftedStatus() bool {
	if x != nil {
		return x.LiftedStatus
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetTractorPower() bool {
	if x != nil {
		return x.TractorPower
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetAcFrequency() float64 {
	if x != nil {
		return x.AcFrequency
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetAcVoltageAB() float64 {
	if x != nil {
		return x.AcVoltageAB
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetAcVoltageBC() float64 {
	if x != nil {
		return x.AcVoltageBC
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetAcVoltageAC() float64 {
	if x != nil {
		return x.AcVoltageAC
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetAcVoltageA() float64 {
	if x != nil {
		return x.AcVoltageA
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetAcVoltageB() float64 {
	if x != nil {
		return x.AcVoltageB
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetAcVoltageC() float64 {
	if x != nil {
		return x.AcVoltageC
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetPhasePowerW_3() int64 {
	if x != nil {
		return x.PhasePowerW_3
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetPhasePowerVa_3() int64 {
	if x != nil {
		return x.PhasePowerVa_3
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetPowerFactor() float64 {
	if x != nil {
		return x.PowerFactor
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetServerCabinetTemp() float64 {
	if x != nil {
		return x.ServerCabinetTemp
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetServerCabinetHumidity() float64 {
	if x != nil {
		return x.ServerCabinetHumidity
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetBatteryVoltage_12V() float64 {
	if x != nil {
		return x.BatteryVoltage_12V
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetWheelEncoderDisabled() bool {
	if x != nil {
		return x.WheelEncoderDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetStrobeDisabled() bool {
	if x != nil {
		return x.StrobeDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetGpsDisabled() bool {
	if x != nil {
		return x.GpsDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetMainContactorDisabled() bool {
	if x != nil {
		return x.MainContactorDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetAirConditionerDisabled() bool {
	if x != nil {
		return x.AirConditionerDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetChillerDisabled() bool {
	if x != nil {
		return x.ChillerDisabled
	}
	return false
}

func (x *ReaperCenterEnclosureData) GetChillerTemp() float64 {
	if x != nil {
		return x.ChillerTemp
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerFlow() float64 {
	if x != nil {
		return x.ChillerFlow
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerPressure() float64 {
	if x != nil {
		return x.ChillerPressure
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerConductivity() float64 {
	if x != nil {
		return x.ChillerConductivity
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerSetTemp() float64 {
	if x != nil {
		return x.ChillerSetTemp
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerHeatTransfer() float64 {
	if x != nil {
		return x.ChillerHeatTransfer
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerFluidDeltaTemp() float64 {
	if x != nil {
		return x.ChillerFluidDeltaTemp
	}
	return 0
}

func (x *ReaperCenterEnclosureData) GetChillerAlarms() *ChillerAlarms {
	if x != nil {
		return x.ChillerAlarms
	}
	return nil
}

// Sensor readings from a single module
type ReaperModuleSensorData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// module ID from which this data was read
	ModuleId int32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	// serial number of the ID from which this data was read
	ModuleSn *string `protobuf:"bytes,2,opt,name=module_sn,json=moduleSn,proto3,oneof" json:"module_sn,omitempty"`
	// Sensor on MCB (general enclosure area)
	EnviroEnclosure *EnvironmentalSensorData `protobuf:"bytes,3,opt,name=enviro_enclosure,json=enviroEnclosure,proto3" json:"enviro_enclosure,omitempty"`
	// Remote sensor in PC enclosure area
	EnviroPc *EnvironmentalSensorData `protobuf:"bytes,4,opt,name=enviro_pc,json=enviroPc,proto3" json:"enviro_pc,omitempty"`
	// Coolant inlet sensor
	CoolantInlet *CoolantSensorData `protobuf:"bytes,5,opt,name=coolant_inlet,json=coolantInlet,proto3" json:"coolant_inlet,omitempty"`
	// Coolant outlet sensor
	CoolantOutlet *CoolantSensorData `protobuf:"bytes,6,opt,name=coolant_outlet,json=coolantOutlet,proto3" json:"coolant_outlet,omitempty"`
	// Strobe board temperature
	StrobeTemperatureC float64 `protobuf:"fixed64,7,opt,name=strobe_temperature_c,json=strobeTemperatureC,proto3" json:"strobe_temperature_c,omitempty"`
	// Strobe capacitor voltage
	StrobeCapVoltage float64 `protobuf:"fixed64,8,opt,name=strobe_cap_voltage,json=strobeCapVoltage,proto3" json:"strobe_cap_voltage,omitempty"`
	// Current (averaged) through the LEDs
	StrobeCurrent float64 `protobuf:"fixed64,9,opt,name=strobe_current,json=strobeCurrent,proto3" json:"strobe_current,omitempty"`
	// Detailed information from sensors on the module PC
	Pc *ReaperPcSensorData `protobuf:"bytes,10,opt,name=pc,proto3,oneof" json:"pc,omitempty"`
	// Info about each of the scanners and target cams
	ScannerA               *ReaperScannerSensorData `protobuf:"bytes,11,opt,name=scanner_a,json=scannerA,proto3,oneof" json:"scanner_a,omitempty"`
	ScannerB               *ReaperScannerSensorData `protobuf:"bytes,12,opt,name=scanner_b,json=scannerB,proto3,oneof" json:"scanner_b,omitempty"`
	PcPowerEnabled         bool                     `protobuf:"varint,13,opt,name=pc_power_enabled,json=pcPowerEnabled,proto3" json:"pc_power_enabled,omitempty"`
	LasersPowerEnabled     bool                     `protobuf:"varint,14,opt,name=lasers_power_enabled,json=lasersPowerEnabled,proto3" json:"lasers_power_enabled,omitempty"`
	PredictCamPowerEnabled bool                     `protobuf:"varint,15,opt,name=predict_cam_power_enabled,json=predictCamPowerEnabled,proto3" json:"predict_cam_power_enabled,omitempty"`
	// whether the relay for BTL power supply is on
	StrobePowerEnabled bool `protobuf:"varint,16,opt,name=strobe_power_enabled,json=strobePowerEnabled,proto3" json:"strobe_power_enabled,omitempty"`
	// whether software is requesting strobe light triggering
	StrobeEnabled bool `protobuf:"varint,17,opt,name=strobe_enabled,json=strobeEnabled,proto3" json:"strobe_enabled,omitempty"`
}

func (x *ReaperModuleSensorData) Reset() {
	*x = ReaperModuleSensorData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperModuleSensorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperModuleSensorData) ProtoMessage() {}

func (x *ReaperModuleSensorData) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperModuleSensorData.ProtoReflect.Descriptor instead.
func (*ReaperModuleSensorData) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{93}
}

func (x *ReaperModuleSensorData) GetModuleId() int32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *ReaperModuleSensorData) GetModuleSn() string {
	if x != nil && x.ModuleSn != nil {
		return *x.ModuleSn
	}
	return ""
}

func (x *ReaperModuleSensorData) GetEnviroEnclosure() *EnvironmentalSensorData {
	if x != nil {
		return x.EnviroEnclosure
	}
	return nil
}

func (x *ReaperModuleSensorData) GetEnviroPc() *EnvironmentalSensorData {
	if x != nil {
		return x.EnviroPc
	}
	return nil
}

func (x *ReaperModuleSensorData) GetCoolantInlet() *CoolantSensorData {
	if x != nil {
		return x.CoolantInlet
	}
	return nil
}

func (x *ReaperModuleSensorData) GetCoolantOutlet() *CoolantSensorData {
	if x != nil {
		return x.CoolantOutlet
	}
	return nil
}

func (x *ReaperModuleSensorData) GetStrobeTemperatureC() float64 {
	if x != nil {
		return x.StrobeTemperatureC
	}
	return 0
}

func (x *ReaperModuleSensorData) GetStrobeCapVoltage() float64 {
	if x != nil {
		return x.StrobeCapVoltage
	}
	return 0
}

func (x *ReaperModuleSensorData) GetStrobeCurrent() float64 {
	if x != nil {
		return x.StrobeCurrent
	}
	return 0
}

func (x *ReaperModuleSensorData) GetPc() *ReaperPcSensorData {
	if x != nil {
		return x.Pc
	}
	return nil
}

func (x *ReaperModuleSensorData) GetScannerA() *ReaperScannerSensorData {
	if x != nil {
		return x.ScannerA
	}
	return nil
}

func (x *ReaperModuleSensorData) GetScannerB() *ReaperScannerSensorData {
	if x != nil {
		return x.ScannerB
	}
	return nil
}

func (x *ReaperModuleSensorData) GetPcPowerEnabled() bool {
	if x != nil {
		return x.PcPowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetLasersPowerEnabled() bool {
	if x != nil {
		return x.LasersPowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetPredictCamPowerEnabled() bool {
	if x != nil {
		return x.PredictCamPowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetStrobePowerEnabled() bool {
	if x != nil {
		return x.StrobePowerEnabled
	}
	return false
}

func (x *ReaperModuleSensorData) GetStrobeEnabled() bool {
	if x != nil {
		return x.StrobeEnabled
	}
	return false
}

// Request center enclosure sensors
type GetReaperEnclosureSensorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetReaperEnclosureSensorsRequest) Reset() {
	*x = GetReaperEnclosureSensorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReaperEnclosureSensorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReaperEnclosureSensorsRequest) ProtoMessage() {}

func (x *GetReaperEnclosureSensorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReaperEnclosureSensorsRequest.ProtoReflect.Descriptor instead.
func (*GetReaperEnclosureSensorsRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{94}
}

type GetReaperEnclosureSensorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sensors *ReaperCenterEnclosureData `protobuf:"bytes,1,opt,name=sensors,proto3" json:"sensors,omitempty"`
}

func (x *GetReaperEnclosureSensorsResponse) Reset() {
	*x = GetReaperEnclosureSensorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReaperEnclosureSensorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReaperEnclosureSensorsResponse) ProtoMessage() {}

func (x *GetReaperEnclosureSensorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReaperEnclosureSensorsResponse.ProtoReflect.Descriptor instead.
func (*GetReaperEnclosureSensorsResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{95}
}

func (x *GetReaperEnclosureSensorsResponse) GetSensors() *ReaperCenterEnclosureData {
	if x != nil {
		return x.Sensors
	}
	return nil
}

// Request data for one or more modules
type GetReaperModuleSensorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Module IDs to return data for; empty list = all assigned modules
	ModuleIds []uint32 `protobuf:"varint,1,rep,packed,name=module_ids,json=moduleIds,proto3" json:"module_ids,omitempty"`
}

func (x *GetReaperModuleSensorsRequest) Reset() {
	*x = GetReaperModuleSensorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReaperModuleSensorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReaperModuleSensorsRequest) ProtoMessage() {}

func (x *GetReaperModuleSensorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReaperModuleSensorsRequest.ProtoReflect.Descriptor instead.
func (*GetReaperModuleSensorsRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{96}
}

func (x *GetReaperModuleSensorsRequest) GetModuleIds() []uint32 {
	if x != nil {
		return x.ModuleIds
	}
	return nil
}

type GetReaperModuleSensorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleSensors []*ReaperModuleSensorData `protobuf:"bytes,1,rep,name=module_sensors,json=moduleSensors,proto3" json:"module_sensors,omitempty"`
}

func (x *GetReaperModuleSensorsResponse) Reset() {
	*x = GetReaperModuleSensorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReaperModuleSensorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReaperModuleSensorsResponse) ProtoMessage() {}

func (x *GetReaperModuleSensorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReaperModuleSensorsResponse.ProtoReflect.Descriptor instead.
func (*GetReaperModuleSensorsResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{97}
}

func (x *GetReaperModuleSensorsResponse) GetModuleSensors() []*ReaperModuleSensorData {
	if x != nil {
		return x.ModuleSensors
	}
	return nil
}

// Set the switched power to one or both scanners in a module
type SetReaperScannerPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId      uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	ScannerAPower *bool  `protobuf:"varint,2,opt,name=scanner_a_power,json=scannerAPower,proto3,oneof" json:"scanner_a_power,omitempty"`
	ScannerBPower *bool  `protobuf:"varint,3,opt,name=scanner_b_power,json=scannerBPower,proto3,oneof" json:"scanner_b_power,omitempty"`
}

func (x *SetReaperScannerPowerRequest) Reset() {
	*x = SetReaperScannerPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperScannerPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperScannerPowerRequest) ProtoMessage() {}

func (x *SetReaperScannerPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperScannerPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperScannerPowerRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{98}
}

func (x *SetReaperScannerPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperScannerPowerRequest) GetScannerAPower() bool {
	if x != nil && x.ScannerAPower != nil {
		return *x.ScannerAPower
	}
	return false
}

func (x *SetReaperScannerPowerRequest) GetScannerBPower() bool {
	if x != nil && x.ScannerBPower != nil {
		return *x.ScannerBPower
	}
	return false
}

type SetReaperScannerPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperScannerPowerResponse) Reset() {
	*x = SetReaperScannerPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperScannerPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperScannerPowerResponse) ProtoMessage() {}

func (x *SetReaperScannerPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperScannerPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperScannerPowerResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{99}
}

func (x *SetReaperScannerPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set switched target cam power (via scanner)
type SetReaperTargetPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId     uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	TargetAPower *bool  `protobuf:"varint,2,opt,name=target_a_power,json=targetAPower,proto3,oneof" json:"target_a_power,omitempty"`
	TargetBPower *bool  `protobuf:"varint,3,opt,name=target_b_power,json=targetBPower,proto3,oneof" json:"target_b_power,omitempty"`
}

func (x *SetReaperTargetPowerRequest) Reset() {
	*x = SetReaperTargetPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperTargetPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperTargetPowerRequest) ProtoMessage() {}

func (x *SetReaperTargetPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperTargetPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperTargetPowerRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{100}
}

func (x *SetReaperTargetPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperTargetPowerRequest) GetTargetAPower() bool {
	if x != nil && x.TargetAPower != nil {
		return *x.TargetAPower
	}
	return false
}

func (x *SetReaperTargetPowerRequest) GetTargetBPower() bool {
	if x != nil && x.TargetBPower != nil {
		return *x.TargetBPower
	}
	return false
}

type SetReaperTargetPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperTargetPowerResponse) Reset() {
	*x = SetReaperTargetPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperTargetPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperTargetPowerResponse) ProtoMessage() {}

func (x *SetReaperTargetPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperTargetPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperTargetPowerResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{101}
}

func (x *SetReaperTargetPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set the switched power to a module's predict cam
type SetReaperPredictCamPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperPredictCamPowerRequest) Reset() {
	*x = SetReaperPredictCamPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperPredictCamPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperPredictCamPowerRequest) ProtoMessage() {}

func (x *SetReaperPredictCamPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperPredictCamPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperPredictCamPowerRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{102}
}

func (x *SetReaperPredictCamPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperPredictCamPowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperPredictCamPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperPredictCamPowerResponse) Reset() {
	*x = SetReaperPredictCamPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperPredictCamPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperPredictCamPowerResponse) ProtoMessage() {}

func (x *SetReaperPredictCamPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperPredictCamPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperPredictCamPowerResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{103}
}

func (x *SetReaperPredictCamPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set the strobe configuration for one or all modules
type SetReaperStrobeConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Module ID to update settings for, otherwise all modules
	ModuleId *uint32         `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3,oneof" json:"module_id,omitempty"`
	Settings *StrobeSettings `protobuf:"bytes,2,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *SetReaperStrobeConfigRequest) Reset() {
	*x = SetReaperStrobeConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperStrobeConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperStrobeConfigRequest) ProtoMessage() {}

func (x *SetReaperStrobeConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperStrobeConfigRequest.ProtoReflect.Descriptor instead.
func (*SetReaperStrobeConfigRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{104}
}

func (x *SetReaperStrobeConfigRequest) GetModuleId() uint32 {
	if x != nil && x.ModuleId != nil {
		return *x.ModuleId
	}
	return 0
}

func (x *SetReaperStrobeConfigRequest) GetSettings() *StrobeSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

type SetReaperStrobeConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperStrobeConfigResponse) Reset() {
	*x = SetReaperStrobeConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperStrobeConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperStrobeConfigResponse) ProtoMessage() {}

func (x *SetReaperStrobeConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperStrobeConfigResponse.ProtoReflect.Descriptor instead.
func (*SetReaperStrobeConfigResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{105}
}

func (x *SetReaperStrobeConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set whether strobe firing is enabled
type SetReaperStrobeEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Module IDs to set strobe state for, or empty for ALL known modules
	ModuleIds []uint32 `protobuf:"varint,1,rep,packed,name=module_ids,json=moduleIds,proto3" json:"module_ids,omitempty"`
	Enabled   bool     `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// if set, how long to disable strobes for, otherwise it's permanent
	DurationMs *uint32 `protobuf:"varint,3,opt,name=duration_ms,json=durationMs,proto3,oneof" json:"duration_ms,omitempty"`
}

func (x *SetReaperStrobeEnableRequest) Reset() {
	*x = SetReaperStrobeEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperStrobeEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperStrobeEnableRequest) ProtoMessage() {}

func (x *SetReaperStrobeEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperStrobeEnableRequest.ProtoReflect.Descriptor instead.
func (*SetReaperStrobeEnableRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{106}
}

func (x *SetReaperStrobeEnableRequest) GetModuleIds() []uint32 {
	if x != nil {
		return x.ModuleIds
	}
	return nil
}

func (x *SetReaperStrobeEnableRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *SetReaperStrobeEnableRequest) GetDurationMs() uint32 {
	if x != nil && x.DurationMs != nil {
		return *x.DurationMs
	}
	return 0
}

type SetReaperStrobeEnableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperStrobeEnableResponse) Reset() {
	*x = SetReaperStrobeEnableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperStrobeEnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperStrobeEnableResponse) ProtoMessage() {}

func (x *SetReaperStrobeEnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperStrobeEnableResponse.ProtoReflect.Descriptor instead.
func (*SetReaperStrobeEnableResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{107}
}

func (x *SetReaperStrobeEnableResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set module PC 240V input power
type SetReaperModulePcPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperModulePcPowerRequest) Reset() {
	*x = SetReaperModulePcPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModulePcPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModulePcPowerRequest) ProtoMessage() {}

func (x *SetReaperModulePcPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModulePcPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperModulePcPowerRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{108}
}

func (x *SetReaperModulePcPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperModulePcPowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperModulePcPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperModulePcPowerResponse) Reset() {
	*x = SetReaperModulePcPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModulePcPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModulePcPowerResponse) ProtoMessage() {}

func (x *SetReaperModulePcPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModulePcPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperModulePcPowerResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{109}
}

func (x *SetReaperModulePcPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set module lasers 240V input power
type SetReaperModuleLaserPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperModuleLaserPowerRequest) Reset() {
	*x = SetReaperModuleLaserPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModuleLaserPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModuleLaserPowerRequest) ProtoMessage() {}

func (x *SetReaperModuleLaserPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModuleLaserPowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperModuleLaserPowerRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{110}
}

func (x *SetReaperModuleLaserPowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperModuleLaserPowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperModuleLaserPowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperModuleLaserPowerResponse) Reset() {
	*x = SetReaperModuleLaserPowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModuleLaserPowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModuleLaserPowerResponse) ProtoMessage() {}

func (x *SetReaperModuleLaserPowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModuleLaserPowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperModuleLaserPowerResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{111}
}

func (x *SetReaperModuleLaserPowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Set BTL (strobe board) 240V input power
type SetReaperModuleStrobePowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleId uint32 `protobuf:"varint,1,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	Enabled  bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetReaperModuleStrobePowerRequest) Reset() {
	*x = SetReaperModuleStrobePowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModuleStrobePowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModuleStrobePowerRequest) ProtoMessage() {}

func (x *SetReaperModuleStrobePowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModuleStrobePowerRequest.ProtoReflect.Descriptor instead.
func (*SetReaperModuleStrobePowerRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{112}
}

func (x *SetReaperModuleStrobePowerRequest) GetModuleId() uint32 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *SetReaperModuleStrobePowerRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetReaperModuleStrobePowerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SetReaperModuleStrobePowerResponse) Reset() {
	*x = SetReaperModuleStrobePowerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReaperModuleStrobePowerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReaperModuleStrobePowerResponse) ProtoMessage() {}

func (x *SetReaperModuleStrobePowerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReaperModuleStrobePowerResponse.ProtoReflect.Descriptor instead.
func (*SetReaperModuleStrobePowerResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{113}
}

func (x *SetReaperModuleStrobePowerResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type IdentifyModuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToIdIp     string   `protobuf:"bytes,1,opt,name=to_id_ip,json=toIdIp,proto3" json:"to_id_ip,omitempty"`
	TurnOffIps []string `protobuf:"bytes,2,rep,name=turn_off_ips,json=turnOffIps,proto3" json:"turn_off_ips,omitempty"`
}

func (x *IdentifyModuleRequest) Reset() {
	*x = IdentifyModuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentifyModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentifyModuleRequest) ProtoMessage() {}

func (x *IdentifyModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentifyModuleRequest.ProtoReflect.Descriptor instead.
func (*IdentifyModuleRequest) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{114}
}

func (x *IdentifyModuleRequest) GetToIdIp() string {
	if x != nil {
		return x.ToIdIp
	}
	return ""
}

func (x *IdentifyModuleRequest) GetTurnOffIps() []string {
	if x != nil {
		return x.TurnOffIps
	}
	return nil
}

type IdentifyModuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *IdentifyModuleResponse) Reset() {
	*x = IdentifyModuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentifyModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentifyModuleResponse) ProtoMessage() {}

func (x *IdentifyModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentifyModuleResponse.ProtoReflect.Descriptor instead.
func (*IdentifyModuleResponse) Descriptor() ([]byte, []int) {
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP(), []int{115}
}

func (x *IdentifyModuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_hardware_manager_proto_hardware_manager_service_proto protoreflect.FileDescriptor

var file_hardware_manager_proto_hardware_manager_service_proto_rawDesc = []byte{
	0x0a, 0x35, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0x1b, 0x0a, 0x0b, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x1c, 0x0a, 0x0c, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x01, 0x78, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x74, 0x61, 0x72,
	0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9b, 0x03,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x4c, 0x65, 0x66, 0x74,
	0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x52, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x74,
	0x69, 0x63, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x62, 0x61, 0x63, 0x6b,
	0x4c, 0x65, 0x66, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69,
	0x63, 0x6b, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x66,
	0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x4c, 0x65, 0x66, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x52, 0x69, 0x67, 0x68, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x62, 0x61,
	0x63, 0x6b, 0x4c, 0x65, 0x66, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2c, 0x0a,
	0x12, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x69, 0x67, 0x68, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3b, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x58, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x22, 0x3b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22,
	0x70, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1a, 0x0a,
	0x09, 0x6d, 0x6d, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x6d, 0x6d, 0x50, 0x65, 0x72, 0x4d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x66,
	0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x66, 0x74, 0x65,
	0x64, 0x22, 0x6a, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x4a, 0x69, 0x6d, 0x62, 0x6f, 0x78, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x2e, 0x0a,
	0x13, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x3f, 0x0a,
	0x16, 0x53, 0x65, 0x74, 0x4a, 0x69, 0x6d, 0x62, 0x6f, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x5f, 0x73, 0x65, 0x74, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x53, 0x65, 0x74, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x33,
	0x0a, 0x17, 0x53, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x18, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x8a, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x84, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x0d, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x1b, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x49, 0x6d,
	0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x0a, 0x1e, 0x53, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x64, 0x22, 0x21, 0x0a, 0x1f, 0x53, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x41, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x77, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x73, 0x5f, 0x73, 0x61, 0x66, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69,
	0x73, 0x53, 0x61, 0x66, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x64, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x46, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x55, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x46, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78,
	0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78,
	0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9a,
	0x04, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69,
	0x66, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x66, 0x74,
	0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x69, 0x6e, 0x5f, 0x63, 0x61, 0x62, 0x5f, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x43, 0x61, 0x62, 0x45, 0x73,
	0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x65,
	0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6c,
	0x65, 0x66, 0x74, 0x45, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x69, 0x67, 0x68, 0x74, 0x45, 0x73, 0x74, 0x6f, 0x70, 0x70,
	0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x23, 0x0a,
	0x0d, 0x77, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x77, 0x61, 0x74, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x5f, 0x65, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x2c, 0x0a, 0x12,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x65, 0x73, 0x74,
	0x6f, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42,
	0x75, 0x74, 0x74, 0x6f, 0x6e, 0x45, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x65,
	0x66, 0x74, 0x5f, 0x6c, 0x70, 0x73, 0x75, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x4c, 0x70, 0x73,
	0x75, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x6c, 0x70, 0x73, 0x75, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4c,
	0x70, 0x73, 0x75, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x64, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x61, 0x0a, 0x06, 0x47,
	0x65, 0x6f, 0x4c, 0x4c, 0x41, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6c, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x61, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x56,
	0x0a, 0x07, 0x47, 0x65, 0x6f, 0x45, 0x43, 0x45, 0x46, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x01, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x01, 0x7a, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x2f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x47, 0x50, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x22, 0x6f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x50,
	0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a,
	0x03, 0x6c, 0x6c, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x6f, 0x4c, 0x4c, 0x41, 0x52, 0x03, 0x6c, 0x6c, 0x61, 0x12, 0x2d, 0x0a, 0x04, 0x65, 0x63, 0x65,
	0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x6f, 0x45, 0x43,
	0x45, 0x46, 0x52, 0x04, 0x65, 0x63, 0x65, 0x66, 0x22, 0x3a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x22, 0x44, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x47,
	0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a,
	0x0a, 0x03, 0x6c, 0x6c, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x6f, 0x4c, 0x4c, 0x41, 0x52, 0x03, 0x6c, 0x6c, 0x61, 0x22, 0x3d, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x61, 0x77, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x45, 0x0a, 0x11, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x22, 0xde, 0x04, 0x0a, 0x0b, 0x44, 0x75, 0x61, 0x6c, 0x47, 0x70, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6e, 0x73, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x67, 0x6e, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x29, 0x0a, 0x10, 0x64, 0x69, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x64, 0x69, 0x66, 0x66, 0x43,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73,
	0x5f, 0x6d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x73, 0x65,
	0x12, 0x47, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x50, 0x68, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x6c, 0x6e, 0x52, 0x0c, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x3e, 0x0a, 0x05,
	0x6e, 0x6f, 0x72, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x48, 0x00, 0x52, 0x05, 0x6e, 0x6f, 0x72, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x04,
	0x65, 0x61, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x48,
	0x01, 0x52, 0x04, 0x65, 0x61, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x6f,
	0x77, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x48, 0x02, 0x52,
	0x04, 0x64, 0x6f, 0x77, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x48, 0x03, 0x52,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x48, 0x04, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x6e, 0x6f, 0x72, 0x74, 0x68, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x65, 0x61, 0x73,
	0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x22, 0x91, 0x04, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x61, 0x77,
	0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x68, 0x61, 0x76, 0x65, 0x46, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61,
	0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x61, 0x74, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x53, 0x61, 0x74, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x64, 0x6f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x68,
	0x64, 0x6f, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x6d, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x4d, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x78, 0x5f, 0x66, 0x69, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61,
	0x76, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x46, 0x69, 0x78, 0x12, 0x34, 0x0a, 0x08, 0x66,
	0x69, 0x78, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x46, 0x69, 0x78, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x66, 0x69, 0x78, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6e, 0x73, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x67, 0x6e, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x64, 0x69, 0x66, 0x66,
	0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x50, 0x68, 0x61,
	0x73, 0x65, 0x53, 0x6f, 0x6c, 0x6e, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x50,
	0x68, 0x61, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x75, 0x61, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x75, 0x61, 0x6c, 0x47, 0x70, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x04, 0x64, 0x75, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x64, 0x75, 0x61, 0x6c, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x47, 0x50, 0x53, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x3e,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x47, 0x50, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x6f, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x11, 0x0a, 0x04, 0x78, 0x5f, 0x6d, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x78, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x79,
	0x5f, 0x6d, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x79, 0x4d, 0x6d, 0x22, 0x1e,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xaf,
	0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67,
	0x12, 0x1e, 0x0a, 0x0b, 0x67, 0x70, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x66, 0x69, 0x78, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x67, 0x70, 0x73, 0x48, 0x61, 0x73, 0x46, 0x69, 0x78,
	0x22, 0x1d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x23, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x75, 0x70, 0x65,
	0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0xa2, 0x15, 0x0a, 0x0d, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x6c, 0x6f, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x6e, 0x54, 0x61, 0x6e,
	0x6b, 0x12, 0x50, 0x0a, 0x25, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x21, 0x68, 0x69, 0x67, 0x68, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x12, 0x50, 0x0a, 0x25, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x72, 0x69, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x21, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x46,
	0x6c, 0x75, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x52, 0x69, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x25, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x64, 0x72, 0x6f, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x21, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x4a, 0x0a, 0x22, 0x68, 0x69, 0x67, 0x68, 0x5f,
	0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69,
	0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1e, 0x68, 0x69, 0x67, 0x68, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x54,
	0x65, 0x6d, 0x70, 0x12, 0x58, 0x0a, 0x29, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x25, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x52, 0x69, 0x73, 0x65, 0x12, 0x58, 0x0a,
	0x29, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75,
	0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x72, 0x6f, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x25, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x75,
	0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x72, 0x65, 0x73, 0x73,
	0x75, 0x72, 0x65, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x3f, 0x0a, 0x1c, 0x68, 0x69, 0x67, 0x68, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x68,
	0x69, 0x67, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x3d, 0x0a, 0x1b, 0x6c, 0x6f, 0x77, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x2d, 0x0a, 0x13, 0x6c, 0x6f, 0x77, 0x5f, 0x73,
	0x75, 0x70, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x6c, 0x6f, 0x77, 0x53, 0x75, 0x70, 0x65, 0x72, 0x48, 0x65,
	0x61, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x4b, 0x0a, 0x22, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1f, 0x68, 0x69, 0x67, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x72, 0x65, 0x73, 0x73,
	0x75, 0x72, 0x65, 0x12, 0x50, 0x0a, 0x25, 0x72, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61,
	0x6e, 0x74, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75,
	0x72, 0x65, 0x5f, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x64, 0x72, 0x6f, 0x70, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x21, 0x72, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x6e, 0x74, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x74, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x48, 0x69, 0x67,
	0x68, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x4e, 0x0a, 0x24, 0x72, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65,
	0x72, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x69, 0x73, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x20, 0x72, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x6e, 0x74,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x74, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x6f,
	0x77, 0x52, 0x69, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x24, 0x72, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65,
	0x72, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x72, 0x6f, 0x70, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x20, 0x72, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x6e, 0x74,
	0x43, 0x69, 0x72, 0x63, 0x75, 0x74, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x6f,
	0x77, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x3c, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x63, 0x6f, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x10, 0x64, 0x63, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x66, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x75, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x64, 0x63, 0x4c, 0x69, 0x6e, 0x65, 0x46, 0x75, 0x73, 0x65, 0x43, 0x75, 0x74,
	0x12, 0x63, 0x0a, 0x2f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x2a, 0x63, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x5d, 0x0a, 0x2c, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x27, 0x63, 0x69, 0x72,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x12, 0x5f, 0x0a, 0x2d, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x73, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x28, 0x63, 0x69, 0x72,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x53, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x6b, 0x0a, 0x33, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x2e, 0x63, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x46,
	0x6c, 0x75, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x72, 0x65,
	0x73, 0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x12, 0x5e, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x28, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x72, 0x65,
	0x73, 0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x12, 0x5a, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72,
	0x5f, 0x73, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72,
	0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x26, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x53, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72,
	0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x29,
	0x0a, 0x10, 0x70, 0x75, 0x6d, 0x70, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x70, 0x75, 0x6d, 0x70, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x61, 0x6e,
	0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x66, 0x61, 0x6e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72,
	0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x20, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x31, 0x5f, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x31, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x46, 0x0a, 0x20, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x5f, 0x32, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x32, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x28, 0x63, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x24, 0x63, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x12, 0x43, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f,
	0x64, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x72,
	0x69, 0x73, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x63, 0x6f, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x52, 0x69, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x1c, 0x64, 0x75, 0x73, 0x74, 0x70, 0x72,
	0x6f, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x64, 0x75,
	0x73, 0x74, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x61, 0x67, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x6f, 0x70, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f, 0x77, 0x61,
	0x69, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1f,
	0x0a, 0x0b, 0x66, 0x61, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x66, 0x61, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12,
	0x36, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x24, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x4f, 0x76, 0x65, 0x72,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x75, 0x6d, 0x70, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x70, 0x75, 0x6d, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x69, 0x72, 0x5f, 0x65, 0x78, 0x68, 0x61, 0x75,
	0x73, 0x74, 0x5f, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x61, 0x69, 0x72, 0x45, 0x78, 0x68, 0x61, 0x75, 0x73,
	0x74, 0x46, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x70, 0x70, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x15,
	0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x6e, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x50, 0x68, 0x61, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x37, 0x0a, 0x18, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x70, 0x68, 0x61, 0x73, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x4f, 0x76,
	0x65, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x22, 0x89, 0x0e, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x77, 0x61, 0x74, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x18,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x46, 0x62, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x67,
	0x6f, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x47, 0x6f, 0x6f, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x61,
	0x64, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x62, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x56, 0x65, 0x72, 0x79, 0x42, 0x61, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x66, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x74, 0x65, 0x6d, 0x70, 0x5f, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70,
	0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x61, 0x63, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x5f, 0x62, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b,
	0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x42, 0x12, 0x23, 0x0a, 0x0e, 0x61,
	0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x5f, 0x63, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x42, 0x43,
	0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61,
	0x5f, 0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x41, 0x43, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x56,
	0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x61,
	0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x42, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x63, 0x5f,
	0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x43, 0x12, 0x25, 0x0a, 0x0f, 0x70,
	0x68, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x77, 0x5f, 0x33, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x68, 0x61, 0x73, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x57, 0x33, 0x12, 0x27, 0x0a, 0x10, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x76, 0x61, 0x5f, 0x33, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x68,
	0x61, 0x73, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x56, 0x61, 0x33, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e,
	0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x36,
	0x0a, 0x17, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74,
	0x5f, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x15, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x48, 0x75,
	0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x31, 0x32, 0x76, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x11, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x56, 0x6f, 0x6c, 0x74,
	0x61, 0x67, 0x65, 0x31, 0x32, 0x76, 0x12, 0x41, 0x0a, 0x1b, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x68,
	0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x18, 0x74, 0x65, 0x6d, 0x70, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70,
	0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x65, 0x6d,
	0x70, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x74, 0x65, 0x6d, 0x70, 0x42, 0x79, 0x70, 0x61, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x68, 0x75, 0x6d, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74,
	0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x74, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0b, 0x62,
	0x74, 0x6c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1c, 0x20,
	0x03, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x5f,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x08, 0x52, 0x10,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x34, 0x0a, 0x16, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x14, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65,
	0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x67, 0x70, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x67, 0x70, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x15, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x69,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x5f, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x69,
	0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x65,
	0x6d, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x25, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0f, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65,
	0x12, 0x31, 0x0a, 0x14, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x27, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13,
	0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x75, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x73,
	0x65, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x28, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63,
	0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x46, 0x0a,
	0x0e, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18,
	0x29, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x0d, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x41,
	0x6c, 0x61, 0x72, 0x6d, 0x73, 0x22, 0x4a, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0x34, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x47, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x42, 0x54,
	0x4c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x31, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x42, 0x54, 0x4c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x4c, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0x36, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x3e, 0x0a, 0x22, 0x53, 0x65, 0x74,
	0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x3f, 0x0a, 0x23, 0x53, 0x65, 0x74,
	0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x63, 0x0a, 0x1d, 0x53, 0x65,
	0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c,
	0x65, 0x66, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x22,
	0x3a, 0x0a, 0x1e, 0x53, 0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x33, 0x0a, 0x17, 0x53,
	0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x34, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x30, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x47, 0x50, 0x53,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x31, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x47,
	0x50, 0x53, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x22, 0x0a, 0x20, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x3d, 0x0a, 0x21, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x16,
	0x0a, 0x14, 0x53, 0x75, 0x69, 0x63, 0x69, 0x64, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a, 0x15, 0x53, 0x75, 0x69, 0x63, 0x69, 0x64,
	0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x3a, 0x0a, 0x1e, 0x53, 0x65, 0x74,
	0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x3b, 0x0a, 0x1f, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x3b, 0x0a, 0x1f, 0x53, 0x65, 0x74, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22,
	0x3c, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x34, 0x0a,
	0x18, 0x53, 0x65, 0x74, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x35, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x37, 0x0a, 0x1b, 0x53, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x38, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x42, 0x79,
	0x70, 0x61, 0x73, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x3b, 0x0a,
	0x1f, 0x53, 0x65, 0x74, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61,
	0x73, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x3c, 0x0a, 0x20, 0x53, 0x65,
	0x74, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x1f, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x53, 0x42, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x73, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x53, 0x42, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x73, 0x62,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x75, 0x73, 0x62, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x11,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x28, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x22, 0x16, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x32, 0x34, 0x30, 0x76, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x32, 0x34, 0x30, 0x76, 0x55, 0x70,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x22, 0x29, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x6c, 0x74, 0x61, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x4d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x35, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x4d, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x6d, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x4d, 0x6d, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x37,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x32, 0x34, 0x30, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x32, 0x34, 0x30, 0x76, 0x22, 0x22, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x57, 0x68,
	0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x43, 0x0a, 0x21, 0x47,
	0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xd4, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x5f, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x08,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x19, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x02,
	0x52, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x75, 0x73, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x35, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x53, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x1a,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x17, 0x45,
	0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x74,
	0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x43, 0x12, 0x1f, 0x0a, 0x0b, 0x68,
	0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x52, 0x68, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x68, 0x70, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x48, 0x70, 0x61, 0x22,
	0x5b, 0x0a, 0x11, 0x43, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x43, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x6b, 0x70, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x4b, 0x70, 0x61, 0x22, 0xcf, 0x01, 0x0a,
	0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x12, 0x4e, 0x0a, 0x11, 0x61, 0x63,
	0x74, 0x75, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x13, 0x65, 0x78,
	0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x11, 0x65, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x88,
	0x07, 0x0a, 0x12, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x16, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x43, 0x70, 0x75, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x12, 0x32, 0x0a, 0x13,
	0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f,
	0x31, 0x5f, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x10, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x70, 0x75, 0x31, 0x43, 0x88, 0x01, 0x01,
	0x12, 0x32, 0x0a, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x67, 0x70, 0x75, 0x5f, 0x32, 0x5f, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52,
	0x10, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x70, 0x75, 0x32,
	0x43, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x73, 0x75, 0x5f, 0x31, 0x32, 0x76, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x73, 0x75, 0x31, 0x32, 0x76, 0x12, 0x15, 0x0a,
	0x06, 0x70, 0x73, 0x75, 0x5f, 0x35, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70,
	0x73, 0x75, 0x35, 0x76, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x73, 0x75, 0x5f, 0x33, 0x76, 0x33, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x73, 0x75, 0x33, 0x76, 0x33, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x61, 0x6d,
	0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x72, 0x61, 0x6d, 0x55, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x75, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x10, 0x64, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x0c, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x4a, 0x0a, 0x0f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f,
	0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43,
	0x61, 0x6d, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x4c, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x3f, 0x0a, 0x09, 0x69, 0x70, 0x6d, 0x69, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x69, 0x70, 0x6d,
	0x69, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x43, 0x0a, 0x0b, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a,
	0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x3d, 0x0a, 0x08, 0x65, 0x78,
	0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x07, 0x65, 0x78, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x31, 0x5f,
	0x63, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x67, 0x70, 0x75, 0x5f, 0x32, 0x5f, 0x63, 0x22, 0xe4, 0x01, 0x0a, 0x18, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x43, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x28,
	0x0a, 0x10, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73,
	0x22, 0xe8, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x6e,
	0x12, 0x30, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12,
	0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x43, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d, 0x6f, 0x74, 0x6f,
	0x72, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x56, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x6f, 0x74, 0x6f,
	0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0d, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x6f,
	0x64, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf7, 0x06, 0x0a, 0x17,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x5f, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x53, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x4f,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x12, 0x21,
	0x0a, 0x0c, 0x66, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x70, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x75, 0x73, 0x65, 0x54, 0x72, 0x69, 0x70, 0x70, 0x65,
	0x64, 0x12, 0x38, 0x0a, 0x18, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x16, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x43, 0x6f, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x12, 0x2e, 0x0a, 0x13, 0x74,
	0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x69, 0x62, 0x65, 0x72,
	0x5f, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x69, 0x62, 0x65, 0x72, 0x43, 0x12, 0x22, 0x0a, 0x0d, 0x6c,
	0x61, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x77, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x57, 0x12,
	0x2b, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x72,
	0x61, 0x77, 0x5f, 0x6d, 0x76, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x61, 0x77, 0x4d, 0x76, 0x12, 0x27, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x53, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a,
	0x09, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x03, 0x52, 0x08, 0x6d, 0x6f,
	0x74, 0x6f, 0x72, 0x50, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x4c, 0x0a, 0x0a, 0x6d, 0x6f, 0x74,
	0x6f, 0x72, 0x5f, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4d, 0x6f,
	0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x04, 0x52, 0x09, 0x6d, 0x6f, 0x74, 0x6f, 0x72,
	0x54, 0x69, 0x6c, 0x74, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x6e, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x6f, 0x74,
	0x6f, 0x72, 0x5f, 0x70, 0x61, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x6f, 0x74, 0x6f, 0x72,
	0x5f, 0x74, 0x69, 0x6c, 0x74, 0x22, 0xd7, 0x0b, 0x0a, 0x19, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x77, 0x61, 0x74, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66,
	0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x62, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x67, 0x6f, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x61, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x56, 0x65, 0x72, 0x79, 0x42, 0x61, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6c, 0x69, 0x66, 0x74, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x61, 0x63, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a,
	0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x5f, 0x62, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65,
	0x41, 0x42, 0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x62, 0x5f, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x61, 0x63, 0x56, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x42, 0x43, 0x12, 0x23, 0x0a, 0x0e, 0x61, 0x63, 0x5f, 0x76, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x5f, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x43, 0x12, 0x20, 0x0a, 0x0c,
	0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x41, 0x12, 0x20,
	0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x42,
	0x12, 0x20, 0x0a, 0x0c, 0x61, 0x63, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67,
	0x65, 0x43, 0x12, 0x25, 0x0a, 0x0f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x77, 0x5f, 0x33, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x57, 0x33, 0x12, 0x27, 0x0a, 0x10, 0x70, 0x68, 0x61,
	0x73, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x5f, 0x33, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x68, 0x61, 0x73, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x56,
	0x61, 0x33, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f,
	0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61, 0x62, 0x69, 0x6e, 0x65,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x36, 0x0a, 0x17, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f,
	0x63, 0x61, 0x62, 0x69, 0x6e, 0x65, 0x74, 0x5f, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x61,
	0x62, 0x69, 0x6e, 0x65, 0x74, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a,
	0x13, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x31, 0x32, 0x76, 0x18, 0x14, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x62, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x31, 0x32, 0x76, 0x12, 0x34, 0x0a,
	0x16, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x77,
	0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x67, 0x70, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x67, 0x70, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x36, 0x0a, 0x17, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x15, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x69, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x61, 0x69, 0x72, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x63, 0x68, 0x69,
	0x6c, 0x6c, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x65, 0x6d, 0x70, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x46, 0x6c,
	0x6f, 0x77, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x68,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x12, 0x31, 0x0a,
	0x14, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x63, 0x68, 0x69,
	0x6c, 0x6c, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x75, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x12, 0x28, 0x0a, 0x10, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x68, 0x69, 0x6c,
	0x6c, 0x65, 0x72, 0x53, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x68,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x63, 0x68, 0x69, 0x6c, 0x6c,
	0x65, 0x72, 0x48, 0x65, 0x61, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x37,
	0x0a, 0x18, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x66, 0x6c, 0x75, 0x69, 0x64, 0x5f,
	0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x21, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x15, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x46, 0x6c, 0x75, 0x69, 0x64, 0x44, 0x65,
	0x6c, 0x74, 0x61, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x68, 0x69, 0x6c, 0x6c,
	0x65, 0x72, 0x5f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73,
	0x52, 0x0d, 0x63, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x22,
	0x88, 0x08, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x53, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x10, 0x65, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x5f, 0x65, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f,
	0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12,
	0x46, 0x0a, 0x09, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x5f, 0x70, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x6c, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x65,
	0x6e, 0x76, 0x69, 0x72, 0x6f, 0x50, 0x63, 0x12, 0x48, 0x0a, 0x0d, 0x63, 0x6f, 0x6f, 0x6c, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x6c, 0x65,
	0x74, 0x12, 0x4a, 0x0a, 0x0e, 0x63, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x6f, 0x75, 0x74,
	0x6c, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6f,
	0x6c, 0x61, 0x6e, 0x74, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d,
	0x63, 0x6f, 0x6f, 0x6c, 0x61, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x6c, 0x65, 0x74, 0x12, 0x30, 0x0a,
	0x14, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x43, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x76, 0x6f,
	0x6c, 0x74, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x43, 0x61, 0x70, 0x56, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x02, 0x70, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x63, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x01, 0x52, 0x02, 0x70, 0x63, 0x88, 0x01, 0x01, 0x12,
	0x4b, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x02, 0x52,
	0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x09,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x03, 0x52, 0x08, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x63, 0x5f,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x6e, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x70, 0x63, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x22, 0x22, 0x0a, 0x20, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6a,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x22, 0x3e, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x22, 0x71, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0e,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x22, 0xbd, 0x01,
	0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0f, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x62, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x22, 0x39, 0x0a,
	0x1d, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x29, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x5f, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x42, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x5f, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x22, 0x38, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x58, 0x0a, 0x1f, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43,
	0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3c, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x22, 0x39, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x8d, 0x01,
	0x0a, 0x1c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x0a,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x73, 0x22, 0x39, 0x0a,
	0x1d, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x56, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x22, 0x3a, 0x0a, 0x1e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x59, 0x0a, 0x20,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3d, 0x0a, 0x21, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x5a, 0x0a, 0x21, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x22, 0x3e, 0x0a, 0x22, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x53, 0x0a, 0x15, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x08, 0x74,
	0x6f, 0x5f, 0x69, 0x64, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x6f, 0x49, 0x64, 0x49, 0x70, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x6f, 0x66,
	0x66, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x75, 0x72,
	0x6e, 0x4f, 0x66, 0x66, 0x49, 0x70, 0x73, 0x22, 0x32, 0x0a, 0x16, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2a, 0x35, 0x0a, 0x10, 0x43,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x50, 0x68, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x6c, 0x6e, 0x12,
	0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x4c, 0x4f,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x49, 0x58, 0x45, 0x44,
	0x10, 0x02, 0x2a, 0x62, 0x0a, 0x07, 0x46, 0x69, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a,
	0x06, 0x4e, 0x4f, 0x5f, 0x46, 0x49, 0x58, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x41,
	0x44, 0x5f, 0x52, 0x45, 0x43, 0x4b, 0x4f, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x4c, 0x59,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x49, 0x58, 0x5f, 0x32, 0x44, 0x10, 0x02, 0x12, 0x0a,
	0x0a, 0x06, 0x46, 0x49, 0x58, 0x5f, 0x33, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x4e,
	0x53, 0x53, 0x5f, 0x44, 0x52, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x49, 0x4d, 0x45, 0x5f,
	0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x05, 0x2a, 0xbf, 0x01, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x45, 0x45,
	0x44, 0x5f, 0x31, 0x30, 0x4d, 0x5f, 0x48, 0x41, 0x4c, 0x46, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e,
	0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x30, 0x4d, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x02,
	0x12, 0x13, 0x0a, 0x0f, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x30, 0x30, 0x4d, 0x5f, 0x48,
	0x41, 0x4c, 0x46, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31,
	0x30, 0x30, 0x4d, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50,
	0x45, 0x45, 0x44, 0x5f, 0x31, 0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x05, 0x12, 0x12, 0x0a,
	0x0e, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x32, 0x47, 0x35, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10,
	0x06, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x35, 0x47, 0x5f, 0x46, 0x55,
	0x4c, 0x4c, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x31, 0x30,
	0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x08, 0x32, 0x8f, 0x2f, 0x0a, 0x16, 0x48, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x08,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x21, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x68, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x28,
	0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x74, 0x61,
	0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63,
	0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x4d,
	0x12, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x72, 0x61, 0x76,
	0x65, 0x6c, 0x4d, 0x4d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x4d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x68,
	0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x68, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x78, 0x74, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x47, 0x50, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6e,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x61, 0x77, 0x47, 0x50, 0x53, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x2a, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x61,
	0x77, 0x47, 0x50, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x61, 0x77, 0x47, 0x50, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x50, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x6f, 0x73,
	0x12, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x50, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50,
	0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x47, 0x50, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x20, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x2b, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x2a, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x00,
	0x12, 0x7a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x65, 0x72,
	0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76,
	0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x53, 0x75, 0x70, 0x65, 0x72, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x29, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x42, 0x54, 0x4c, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x54, 0x4c,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x54, 0x4c, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x12, 0x53, 0x65, 0x74,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a,
	0x1b, 0x53, 0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x34, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x35, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x16, 0x53,
	0x65, 0x74, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x57, 0x68, 0x65, 0x65,
	0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x57, 0x68, 0x65,
	0x65, 0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x0d, 0x53, 0x65,
	0x74, 0x47, 0x50, 0x53, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x47, 0x50, 0x53, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x47, 0x50, 0x53, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62,
	0x0a, 0x0d, 0x53, 0x75, 0x69, 0x63, 0x69, 0x64, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12,
	0x26, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x75, 0x69, 0x63, 0x69, 0x64, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x69, 0x63, 0x69,
	0x64, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x12, 0x32, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43,
	0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x17,
	0x53, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x61,
	0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6b,
	0x0a, 0x10, 0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x18,
	0x53, 0x65, 0x74, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65,
	0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x41,
	0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6e, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2a, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x68, 0x69,
	0x6c, 0x6c, 0x65, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x68, 0x69, 0x6c, 0x6c, 0x65, 0x72,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x77, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x42, 0x79, 0x70, 0x61,
	0x73, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x18, 0x53,
	0x65, 0x74, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x48, 0x75,
	0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65,
	0x74, 0x48, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x62, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x32, 0x34, 0x30, 0x76, 0x55, 0x70, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x26, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x32, 0x34, 0x30, 0x76, 0x55, 0x70, 0x74, 0x69,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x32, 0x34, 0x30, 0x76, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x23, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x7d, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x55,
	0x53, 0x42, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x53, 0x42, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x53, 0x42, 0x53, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65,
	0x0a, 0x0e, 0x53, 0x65, 0x74, 0x4a, 0x69, 0x6d, 0x62, 0x6f, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x12, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x4a, 0x69, 0x6d, 0x62, 0x6f, 0x78, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x4a, 0x69, 0x6d, 0x62, 0x6f, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69,
	0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x29, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x68, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x1a,
	0x53, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x4f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2a, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65,
	0x74, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6d, 0x70,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x53, 0x61, 0x66,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x30, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53,
	0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53,
	0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x6e, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x46, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x46, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x46, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x73, 0x12, 0x32, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x45,
	0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x45, 0x6e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x12, 0x2d, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x83, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x31,
	0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x2e, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2e, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x7d, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x63, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x63, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86,
	0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x32, 0x2e, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x33, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x18, 0x5a, 0x16, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hardware_manager_proto_hardware_manager_service_proto_rawDescOnce sync.Once
	file_hardware_manager_proto_hardware_manager_service_proto_rawDescData = file_hardware_manager_proto_hardware_manager_service_proto_rawDesc
)

func file_hardware_manager_proto_hardware_manager_service_proto_rawDescGZIP() []byte {
	file_hardware_manager_proto_hardware_manager_service_proto_rawDescOnce.Do(func() {
		file_hardware_manager_proto_hardware_manager_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_hardware_manager_proto_hardware_manager_service_proto_rawDescData)
	})
	return file_hardware_manager_proto_hardware_manager_service_proto_rawDescData
}

var file_hardware_manager_proto_hardware_manager_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_hardware_manager_proto_hardware_manager_service_proto_msgTypes = make([]protoimpl.MessageInfo, 116)
var file_hardware_manager_proto_hardware_manager_service_proto_goTypes = []interface{}{
	(CarrierPhaseSoln)(0),                       // 0: hardware_manager.CarrierPhaseSoln
	(FixType)(0),                                // 1: hardware_manager.FixType
	(NetworkLinkSpeed)(0),                       // 2: hardware_manager.NetworkLinkSpeed
	(*PingRequest)(nil),                         // 3: hardware_manager.PingRequest
	(*PingResponse)(nil),                        // 4: hardware_manager.PingResponse
	(*GetRotaryTicksRequest)(nil),               // 5: hardware_manager.GetRotaryTicksRequest
	(*GetRotaryTicksResponse)(nil),              // 6: hardware_manager.GetRotaryTicksResponse
	(*GetNextDistanceRequest)(nil),              // 7: hardware_manager.GetNextDistanceRequest
	(*GetNextDistanceResponse)(nil),             // 8: hardware_manager.GetNextDistanceResponse
	(*GetNextVelocityRequest)(nil),              // 9: hardware_manager.GetNextVelocityRequest
	(*GetNextVelocityResponse)(nil),             // 10: hardware_manager.GetNextVelocityResponse
	(*SetJimboxSpeedRequest)(nil),               // 11: hardware_manager.SetJimboxSpeedRequest
	(*SetJimboxSpeedResponse)(nil),              // 12: hardware_manager.SetJimboxSpeedResponse
	(*SetCruiseEnabledRequest)(nil),             // 13: hardware_manager.SetCruiseEnabledRequest
	(*SetCruiseEnabledResponse)(nil),            // 14: hardware_manager.SetCruiseEnabledResponse
	(*GetCruiseStatusRequest)(nil),              // 15: hardware_manager.GetCruiseStatusRequest
	(*GetCruiseStatusResponse)(nil),             // 16: hardware_manager.GetCruiseStatusResponse
	(*SetImplementStateRequest)(nil),            // 17: hardware_manager.SetImplementStateRequest
	(*SetImplementStateResponse)(nil),           // 18: hardware_manager.SetImplementStateResponse
	(*SetSafeStateEnforcementRequest)(nil),      // 19: hardware_manager.SetSafeStateEnforcementRequest
	(*SetSafeStateEnforcementResponse)(nil),     // 20: hardware_manager.SetSafeStateEnforcementResponse
	(*GetTractorSafetyStateRequest)(nil),        // 21: hardware_manager.GetTractorSafetyStateRequest
	(*GetTractorSafetyStateResponse)(nil),       // 22: hardware_manager.GetTractorSafetyStateResponse
	(*GetTractorIFStateRequest)(nil),            // 23: hardware_manager.GetTractorIFStateRequest
	(*GetTractorIFStateResponse)(nil),           // 24: hardware_manager.GetTractorIFStateResponse
	(*GetSafetyStatusRequest)(nil),              // 25: hardware_manager.GetSafetyStatusRequest
	(*GetSafetyStatusResponse)(nil),             // 26: hardware_manager.GetSafetyStatusResponse
	(*GeoLLA)(nil),                              // 27: hardware_manager.GeoLLA
	(*GeoECEF)(nil),                             // 28: hardware_manager.GeoECEF
	(*GetGPSDataRequest)(nil),                   // 29: hardware_manager.GetGPSDataRequest
	(*GetGPSDataResponse)(nil),                  // 30: hardware_manager.GetGPSDataResponse
	(*GetNextGPSDataRequest)(nil),               // 31: hardware_manager.GetNextGPSDataRequest
	(*GetNextGPSDataResponse)(nil),              // 32: hardware_manager.GetNextGPSDataResponse
	(*GetNextRawGPSDataRequest)(nil),            // 33: hardware_manager.GetNextRawGPSDataRequest
	(*ValueWithAccuracy)(nil),                   // 34: hardware_manager.ValueWithAccuracy
	(*DualGpsData)(nil),                         // 35: hardware_manager.DualGpsData
	(*GetNextRawGPSDataResponse)(nil),           // 36: hardware_manager.GetNextRawGPSDataResponse
	(*GetGPSFixedPosRequest)(nil),               // 37: hardware_manager.GetGPSFixedPosRequest
	(*GetGPSFixedPosResponse)(nil),              // 38: hardware_manager.GetGPSFixedPosResponse
	(*GetManagedBoardErrorsRequest)(nil),        // 39: hardware_manager.GetManagedBoardErrorsRequest
	(*GetManagedBoardErrorsResponse)(nil),       // 40: hardware_manager.GetManagedBoardErrorsResponse
	(*GetSupervisoryStatusRequest)(nil),         // 41: hardware_manager.GetSupervisoryStatusRequest
	(*GetReaperSupervisoryStatusRequest)(nil),   // 42: hardware_manager.GetReaperSupervisoryStatusRequest
	(*ChillerAlarms)(nil),                       // 43: hardware_manager.ChillerAlarms
	(*GetSupervisoryStatusResponse)(nil),        // 44: hardware_manager.GetSupervisoryStatusResponse
	(*SetServerDisableRequest)(nil),             // 45: hardware_manager.SetServerDisableRequest
	(*SetServerDisableResponse)(nil),            // 46: hardware_manager.SetServerDisableResponse
	(*SetBTLDisableRequest)(nil),                // 47: hardware_manager.SetBTLDisableRequest
	(*SetBTLDisableResponse)(nil),               // 48: hardware_manager.SetBTLDisableResponse
	(*SetScannersDisableRequest)(nil),           // 49: hardware_manager.SetScannersDisableRequest
	(*SetScannersDisableResponse)(nil),          // 50: hardware_manager.SetScannersDisableResponse
	(*SetWheelEncoderBoardDisableRequest)(nil),  // 51: hardware_manager.SetWheelEncoderBoardDisableRequest
	(*SetWheelEncoderBoardDisableResponse)(nil), // 52: hardware_manager.SetWheelEncoderBoardDisableResponse
	(*SetWheelEncoderDisableRequest)(nil),       // 53: hardware_manager.SetWheelEncoderDisableRequest
	(*SetWheelEncoderDisableResponse)(nil),      // 54: hardware_manager.SetWheelEncoderDisableResponse
	(*SetStrobeDisableRequest)(nil),             // 55: hardware_manager.SetStrobeDisableRequest
	(*SetStrobeDisableResponse)(nil),            // 56: hardware_manager.SetStrobeDisableResponse
	(*SetGPSDisableRequest)(nil),                // 57: hardware_manager.SetGPSDisableRequest
	(*SetGPSDisableResponse)(nil),               // 58: hardware_manager.SetGPSDisableResponse
	(*CommandComputerPowerCycleRequest)(nil),    // 59: hardware_manager.CommandComputerPowerCycleRequest
	(*CommandComputerPowerCycleResponse)(nil),   // 60: hardware_manager.CommandComputerPowerCycleResponse
	(*SuicideSwitchRequest)(nil),                // 61: hardware_manager.SuicideSwitchRequest
	(*SuicideSwitchResponse)(nil),               // 62: hardware_manager.SuicideSwitchResponse
	(*SetMainContactorDisableRequest)(nil),      // 63: hardware_manager.SetMainContactorDisableRequest
	(*SetMainContactorDisableResponse)(nil),     // 64: hardware_manager.SetMainContactorDisableResponse
	(*SetAirConditionerDisableRequest)(nil),     // 65: hardware_manager.SetAirConditionerDisableRequest
	(*SetAirConditionerDisableResponse)(nil),    // 66: hardware_manager.SetAirConditionerDisableResponse
	(*SetChillerDisableRequest)(nil),            // 67: hardware_manager.SetChillerDisableRequest
	(*SetChillerDisableResponse)(nil),           // 68: hardware_manager.SetChillerDisableResponse
	(*SetTempBypassDisableRequest)(nil),         // 69: hardware_manager.SetTempBypassDisableRequest
	(*SetTempBypassDisableResponse)(nil),        // 70: hardware_manager.SetTempBypassDisableResponse
	(*SetHumidityBypassDisableRequest)(nil),     // 71: hardware_manager.SetHumidityBypassDisableRequest
	(*SetHumidityBypassDisableResponse)(nil),    // 72: hardware_manager.SetHumidityBypassDisableResponse
	(*GetAvailableUSBStorageRequest)(nil),       // 73: hardware_manager.GetAvailableUSBStorageRequest
	(*GetAvailableUSBStorageResponse)(nil),      // 74: hardware_manager.GetAvailableUSBStorageResponse
	(*GetReadyRequest)(nil),                     // 75: hardware_manager.GetReadyRequest
	(*GetReadyResponse)(nil),                    // 76: hardware_manager.GetReadyResponse
	(*Get240VUptimeRequest)(nil),                // 77: hardware_manager.Get240vUptimeRequest
	(*Get240VUptimeResponse)(nil),               // 78: hardware_manager.Get240vUptimeResponse
	(*GetDeltaTravelMMRequest)(nil),             // 79: hardware_manager.GetDeltaTravelMMRequest
	(*GetDeltaTravelMMResponse)(nil),            // 80: hardware_manager.GetDeltaTravelMMResponse
	(*GetRuntimeRequest)(nil),                   // 81: hardware_manager.GetRuntimeRequest
	(*GetRuntimeResponse)(nil),                  // 82: hardware_manager.GetRuntimeResponse
	(*GetWheelEncoderResolutionRequest)(nil),    // 83: hardware_manager.GetWheelEncoderResolutionRequest
	(*GetWheelEncoderResolutionResponse)(nil),   // 84: hardware_manager.GetWheelEncoderResolutionResponse
	(*StrobeSettings)(nil),                      // 85: hardware_manager.StrobeSettings
	(*SetStrobeSettingsResponse)(nil),           // 86: hardware_manager.SetStrobeSettingsResponse
	(*GetStrobeSettingsRequest)(nil),            // 87: hardware_manager.GetStrobeSettingsRequest
	(*EnvironmentalSensorData)(nil),             // 88: hardware_manager.EnvironmentalSensorData
	(*CoolantSensorData)(nil),                   // 89: hardware_manager.CoolantSensorData
	(*NetworkPortState)(nil),                    // 90: hardware_manager.NetworkPortState
	(*ReaperPcSensorData)(nil),                  // 91: hardware_manager.ReaperPcSensorData
	(*ReaperScannerLaserStatus)(nil),            // 92: hardware_manager.ReaperScannerLaserStatus
	(*ReaperScannerMotorData)(nil),              // 93: hardware_manager.ReaperScannerMotorData
	(*ReaperScannerSensorData)(nil),             // 94: hardware_manager.ReaperScannerSensorData
	(*ReaperCenterEnclosureData)(nil),           // 95: hardware_manager.ReaperCenterEnclosureData
	(*ReaperModuleSensorData)(nil),              // 96: hardware_manager.ReaperModuleSensorData
	(*GetReaperEnclosureSensorsRequest)(nil),    // 97: hardware_manager.GetReaperEnclosureSensorsRequest
	(*GetReaperEnclosureSensorsResponse)(nil),   // 98: hardware_manager.GetReaperEnclosureSensorsResponse
	(*GetReaperModuleSensorsRequest)(nil),       // 99: hardware_manager.GetReaperModuleSensorsRequest
	(*GetReaperModuleSensorsResponse)(nil),      // 100: hardware_manager.GetReaperModuleSensorsResponse
	(*SetReaperScannerPowerRequest)(nil),        // 101: hardware_manager.SetReaperScannerPowerRequest
	(*SetReaperScannerPowerResponse)(nil),       // 102: hardware_manager.SetReaperScannerPowerResponse
	(*SetReaperTargetPowerRequest)(nil),         // 103: hardware_manager.SetReaperTargetPowerRequest
	(*SetReaperTargetPowerResponse)(nil),        // 104: hardware_manager.SetReaperTargetPowerResponse
	(*SetReaperPredictCamPowerRequest)(nil),     // 105: hardware_manager.SetReaperPredictCamPowerRequest
	(*SetReaperPredictCamPowerResponse)(nil),    // 106: hardware_manager.SetReaperPredictCamPowerResponse
	(*SetReaperStrobeConfigRequest)(nil),        // 107: hardware_manager.SetReaperStrobeConfigRequest
	(*SetReaperStrobeConfigResponse)(nil),       // 108: hardware_manager.SetReaperStrobeConfigResponse
	(*SetReaperStrobeEnableRequest)(nil),        // 109: hardware_manager.SetReaperStrobeEnableRequest
	(*SetReaperStrobeEnableResponse)(nil),       // 110: hardware_manager.SetReaperStrobeEnableResponse
	(*SetReaperModulePcPowerRequest)(nil),       // 111: hardware_manager.SetReaperModulePcPowerRequest
	(*SetReaperModulePcPowerResponse)(nil),      // 112: hardware_manager.SetReaperModulePcPowerResponse
	(*SetReaperModuleLaserPowerRequest)(nil),    // 113: hardware_manager.SetReaperModuleLaserPowerRequest
	(*SetReaperModuleLaserPowerResponse)(nil),   // 114: hardware_manager.SetReaperModuleLaserPowerResponse
	(*SetReaperModuleStrobePowerRequest)(nil),   // 115: hardware_manager.SetReaperModuleStrobePowerRequest
	(*SetReaperModuleStrobePowerResponse)(nil),  // 116: hardware_manager.SetReaperModuleStrobePowerResponse
	(*IdentifyModuleRequest)(nil),               // 117: hardware_manager.IdentifyModuleRequest
	(*IdentifyModuleResponse)(nil),              // 118: hardware_manager.IdentifyModuleResponse
}
var file_hardware_manager_proto_hardware_manager_service_proto_depIdxs = []int32{
	27,  // 0: hardware_manager.GetGPSDataResponse.lla:type_name -> hardware_manager.GeoLLA
	28,  // 1: hardware_manager.GetGPSDataResponse.ecef:type_name -> hardware_manager.GeoECEF
	27,  // 2: hardware_manager.GetNextGPSDataResponse.lla:type_name -> hardware_manager.GeoLLA
	0,   // 3: hardware_manager.DualGpsData.carrier_phase:type_name -> hardware_manager.CarrierPhaseSoln
	34,  // 4: hardware_manager.DualGpsData.north:type_name -> hardware_manager.ValueWithAccuracy
	34,  // 5: hardware_manager.DualGpsData.east:type_name -> hardware_manager.ValueWithAccuracy
	34,  // 6: hardware_manager.DualGpsData.down:type_name -> hardware_manager.ValueWithAccuracy
	34,  // 7: hardware_manager.DualGpsData.length:type_name -> hardware_manager.ValueWithAccuracy
	34,  // 8: hardware_manager.DualGpsData.heading:type_name -> hardware_manager.ValueWithAccuracy
	1,   // 9: hardware_manager.GetNextRawGPSDataResponse.fix_type:type_name -> hardware_manager.FixType
	0,   // 10: hardware_manager.GetNextRawGPSDataResponse.carrier_phase:type_name -> hardware_manager.CarrierPhaseSoln
	35,  // 11: hardware_manager.GetNextRawGPSDataResponse.dual:type_name -> hardware_manager.DualGpsData
	43,  // 12: hardware_manager.GetSupervisoryStatusResponse.chiller_alarms:type_name -> hardware_manager.ChillerAlarms
	2,   // 13: hardware_manager.NetworkPortState.actual_link_speed:type_name -> hardware_manager.NetworkLinkSpeed
	2,   // 14: hardware_manager.NetworkPortState.expected_link_speed:type_name -> hardware_manager.NetworkLinkSpeed
	90,  // 15: hardware_manager.ReaperPcSensorData.scanner_link:type_name -> hardware_manager.NetworkPortState
	90,  // 16: hardware_manager.ReaperPcSensorData.target_cam_link:type_name -> hardware_manager.NetworkPortState
	90,  // 17: hardware_manager.ReaperPcSensorData.predict_cam_link:type_name -> hardware_manager.NetworkPortState
	90,  // 18: hardware_manager.ReaperPcSensorData.ipmi_link:type_name -> hardware_manager.NetworkPortState
	90,  // 19: hardware_manager.ReaperPcSensorData.global_link:type_name -> hardware_manager.NetworkPortState
	90,  // 20: hardware_manager.ReaperPcSensorData.ext_link:type_name -> hardware_manager.NetworkPortState
	92,  // 21: hardware_manager.ReaperScannerSensorData.laser_status:type_name -> hardware_manager.ReaperScannerLaserStatus
	93,  // 22: hardware_manager.ReaperScannerSensorData.motor_pan:type_name -> hardware_manager.ReaperScannerMotorData
	93,  // 23: hardware_manager.ReaperScannerSensorData.motor_tilt:type_name -> hardware_manager.ReaperScannerMotorData
	43,  // 24: hardware_manager.ReaperCenterEnclosureData.chiller_alarms:type_name -> hardware_manager.ChillerAlarms
	88,  // 25: hardware_manager.ReaperModuleSensorData.enviro_enclosure:type_name -> hardware_manager.EnvironmentalSensorData
	88,  // 26: hardware_manager.ReaperModuleSensorData.enviro_pc:type_name -> hardware_manager.EnvironmentalSensorData
	89,  // 27: hardware_manager.ReaperModuleSensorData.coolant_inlet:type_name -> hardware_manager.CoolantSensorData
	89,  // 28: hardware_manager.ReaperModuleSensorData.coolant_outlet:type_name -> hardware_manager.CoolantSensorData
	91,  // 29: hardware_manager.ReaperModuleSensorData.pc:type_name -> hardware_manager.ReaperPcSensorData
	94,  // 30: hardware_manager.ReaperModuleSensorData.scanner_a:type_name -> hardware_manager.ReaperScannerSensorData
	94,  // 31: hardware_manager.ReaperModuleSensorData.scanner_b:type_name -> hardware_manager.ReaperScannerSensorData
	95,  // 32: hardware_manager.GetReaperEnclosureSensorsResponse.sensors:type_name -> hardware_manager.ReaperCenterEnclosureData
	96,  // 33: hardware_manager.GetReaperModuleSensorsResponse.module_sensors:type_name -> hardware_manager.ReaperModuleSensorData
	85,  // 34: hardware_manager.SetReaperStrobeConfigRequest.settings:type_name -> hardware_manager.StrobeSettings
	3,   // 35: hardware_manager.HardwareManagerService.Ping:input_type -> hardware_manager.PingRequest
	75,  // 36: hardware_manager.HardwareManagerService.GetReady:input_type -> hardware_manager.GetReadyRequest
	7,   // 37: hardware_manager.HardwareManagerService.GetNextDistance:input_type -> hardware_manager.GetNextDistanceRequest
	9,   // 38: hardware_manager.HardwareManagerService.GetNextVelocity:input_type -> hardware_manager.GetNextVelocityRequest
	5,   // 39: hardware_manager.HardwareManagerService.GetRotaryTicks:input_type -> hardware_manager.GetRotaryTicksRequest
	79,  // 40: hardware_manager.HardwareManagerService.GetDeltaTravelMM:input_type -> hardware_manager.GetDeltaTravelMMRequest
	83,  // 41: hardware_manager.HardwareManagerService.GetWheelEncoderResolution:input_type -> hardware_manager.GetWheelEncoderResolutionRequest
	25,  // 42: hardware_manager.HardwareManagerService.GetSafetyStatus:input_type -> hardware_manager.GetSafetyStatusRequest
	29,  // 43: hardware_manager.HardwareManagerService.GetGPSData:input_type -> hardware_manager.GetGPSDataRequest
	31,  // 44: hardware_manager.HardwareManagerService.GetNextGPSData:input_type -> hardware_manager.GetNextGPSDataRequest
	33,  // 45: hardware_manager.HardwareManagerService.GetNextRawGPSData:input_type -> hardware_manager.GetNextRawGPSDataRequest
	37,  // 46: hardware_manager.HardwareManagerService.GetGPSFixedPos:input_type -> hardware_manager.GetGPSFixedPosRequest
	85,  // 47: hardware_manager.HardwareManagerService.SetStrobeSettings:input_type -> hardware_manager.StrobeSettings
	87,  // 48: hardware_manager.HardwareManagerService.GetStrobeSettings:input_type -> hardware_manager.GetStrobeSettingsRequest
	39,  // 49: hardware_manager.HardwareManagerService.GetManagedBoardErrors:input_type -> hardware_manager.GetManagedBoardErrorsRequest
	41,  // 50: hardware_manager.HardwareManagerService.GetSupervisoryStatus:input_type -> hardware_manager.GetSupervisoryStatusRequest
	42,  // 51: hardware_manager.HardwareManagerService.GetReaperSupervisoryStatus:input_type -> hardware_manager.GetReaperSupervisoryStatusRequest
	45,  // 52: hardware_manager.HardwareManagerService.SetServerDisable:input_type -> hardware_manager.SetServerDisableRequest
	47,  // 53: hardware_manager.HardwareManagerService.SetBTLDisable:input_type -> hardware_manager.SetBTLDisableRequest
	49,  // 54: hardware_manager.HardwareManagerService.SetScannersDisable:input_type -> hardware_manager.SetScannersDisableRequest
	51,  // 55: hardware_manager.HardwareManagerService.SetWheelEncoderBoardDisable:input_type -> hardware_manager.SetWheelEncoderBoardDisableRequest
	53,  // 56: hardware_manager.HardwareManagerService.SetWheelEncoderDisable:input_type -> hardware_manager.SetWheelEncoderDisableRequest
	57,  // 57: hardware_manager.HardwareManagerService.SetGPSDisable:input_type -> hardware_manager.SetGPSDisableRequest
	61,  // 58: hardware_manager.HardwareManagerService.SuicideSwitch:input_type -> hardware_manager.SuicideSwitchRequest
	59,  // 59: hardware_manager.HardwareManagerService.CommandComputerPowerCycle:input_type -> hardware_manager.CommandComputerPowerCycleRequest
	63,  // 60: hardware_manager.HardwareManagerService.SetMainContactorDisable:input_type -> hardware_manager.SetMainContactorDisableRequest
	55,  // 61: hardware_manager.HardwareManagerService.SetStrobeDisable:input_type -> hardware_manager.SetStrobeDisableRequest
	65,  // 62: hardware_manager.HardwareManagerService.SetAirConditionerDisable:input_type -> hardware_manager.SetAirConditionerDisableRequest
	67,  // 63: hardware_manager.HardwareManagerService.SetChillerDisable:input_type -> hardware_manager.SetChillerDisableRequest
	69,  // 64: hardware_manager.HardwareManagerService.SetTempBypassDisable:input_type -> hardware_manager.SetTempBypassDisableRequest
	71,  // 65: hardware_manager.HardwareManagerService.SetHumidityBypassDisable:input_type -> hardware_manager.SetHumidityBypassDisableRequest
	77,  // 66: hardware_manager.HardwareManagerService.Get240vUptime:input_type -> hardware_manager.Get240vUptimeRequest
	81,  // 67: hardware_manager.HardwareManagerService.GetRuntime:input_type -> hardware_manager.GetRuntimeRequest
	73,  // 68: hardware_manager.HardwareManagerService.GetAvailableUSBStorage:input_type -> hardware_manager.GetAvailableUSBStorageRequest
	11,  // 69: hardware_manager.HardwareManagerService.SetJimboxSpeed:input_type -> hardware_manager.SetJimboxSpeedRequest
	13,  // 70: hardware_manager.HardwareManagerService.SetCruiseEnabled:input_type -> hardware_manager.SetCruiseEnabledRequest
	15,  // 71: hardware_manager.HardwareManagerService.GetCruiseStatus:input_type -> hardware_manager.GetCruiseStatusRequest
	17,  // 72: hardware_manager.HardwareManagerService.SetImplementStateOnTractor:input_type -> hardware_manager.SetImplementStateRequest
	19,  // 73: hardware_manager.HardwareManagerService.SetSafeStateEnforcement:input_type -> hardware_manager.SetSafeStateEnforcementRequest
	21,  // 74: hardware_manager.HardwareManagerService.GetTractorSafetyState:input_type -> hardware_manager.GetTractorSafetyStateRequest
	23,  // 75: hardware_manager.HardwareManagerService.GetTractorIFState:input_type -> hardware_manager.GetTractorIFStateRequest
	97,  // 76: hardware_manager.HardwareManagerService.GetReaperEnclosureSensors:input_type -> hardware_manager.GetReaperEnclosureSensorsRequest
	99,  // 77: hardware_manager.HardwareManagerService.GetReaperModuleSensors:input_type -> hardware_manager.GetReaperModuleSensorsRequest
	101, // 78: hardware_manager.HardwareManagerService.SetReaperScannerPower:input_type -> hardware_manager.SetReaperScannerPowerRequest
	103, // 79: hardware_manager.HardwareManagerService.SetReaperTargetPower:input_type -> hardware_manager.SetReaperTargetPowerRequest
	105, // 80: hardware_manager.HardwareManagerService.SetReaperPredictCamPower:input_type -> hardware_manager.SetReaperPredictCamPowerRequest
	107, // 81: hardware_manager.HardwareManagerService.SetReaperStrobeConfig:input_type -> hardware_manager.SetReaperStrobeConfigRequest
	109, // 82: hardware_manager.HardwareManagerService.SetReaperStrobeEnable:input_type -> hardware_manager.SetReaperStrobeEnableRequest
	111, // 83: hardware_manager.HardwareManagerService.SetReaperModulePcPower:input_type -> hardware_manager.SetReaperModulePcPowerRequest
	113, // 84: hardware_manager.HardwareManagerService.SetReaperModuleLaserPower:input_type -> hardware_manager.SetReaperModuleLaserPowerRequest
	115, // 85: hardware_manager.HardwareManagerService.SetReaperModuleStrobePower:input_type -> hardware_manager.SetReaperModuleStrobePowerRequest
	117, // 86: hardware_manager.HardwareManagerService.IdentifyModule:input_type -> hardware_manager.IdentifyModuleRequest
	4,   // 87: hardware_manager.HardwareManagerService.Ping:output_type -> hardware_manager.PingResponse
	76,  // 88: hardware_manager.HardwareManagerService.GetReady:output_type -> hardware_manager.GetReadyResponse
	8,   // 89: hardware_manager.HardwareManagerService.GetNextDistance:output_type -> hardware_manager.GetNextDistanceResponse
	10,  // 90: hardware_manager.HardwareManagerService.GetNextVelocity:output_type -> hardware_manager.GetNextVelocityResponse
	6,   // 91: hardware_manager.HardwareManagerService.GetRotaryTicks:output_type -> hardware_manager.GetRotaryTicksResponse
	80,  // 92: hardware_manager.HardwareManagerService.GetDeltaTravelMM:output_type -> hardware_manager.GetDeltaTravelMMResponse
	84,  // 93: hardware_manager.HardwareManagerService.GetWheelEncoderResolution:output_type -> hardware_manager.GetWheelEncoderResolutionResponse
	26,  // 94: hardware_manager.HardwareManagerService.GetSafetyStatus:output_type -> hardware_manager.GetSafetyStatusResponse
	30,  // 95: hardware_manager.HardwareManagerService.GetGPSData:output_type -> hardware_manager.GetGPSDataResponse
	32,  // 96: hardware_manager.HardwareManagerService.GetNextGPSData:output_type -> hardware_manager.GetNextGPSDataResponse
	36,  // 97: hardware_manager.HardwareManagerService.GetNextRawGPSData:output_type -> hardware_manager.GetNextRawGPSDataResponse
	38,  // 98: hardware_manager.HardwareManagerService.GetGPSFixedPos:output_type -> hardware_manager.GetGPSFixedPosResponse
	86,  // 99: hardware_manager.HardwareManagerService.SetStrobeSettings:output_type -> hardware_manager.SetStrobeSettingsResponse
	85,  // 100: hardware_manager.HardwareManagerService.GetStrobeSettings:output_type -> hardware_manager.StrobeSettings
	40,  // 101: hardware_manager.HardwareManagerService.GetManagedBoardErrors:output_type -> hardware_manager.GetManagedBoardErrorsResponse
	44,  // 102: hardware_manager.HardwareManagerService.GetSupervisoryStatus:output_type -> hardware_manager.GetSupervisoryStatusResponse
	95,  // 103: hardware_manager.HardwareManagerService.GetReaperSupervisoryStatus:output_type -> hardware_manager.ReaperCenterEnclosureData
	46,  // 104: hardware_manager.HardwareManagerService.SetServerDisable:output_type -> hardware_manager.SetServerDisableResponse
	48,  // 105: hardware_manager.HardwareManagerService.SetBTLDisable:output_type -> hardware_manager.SetBTLDisableResponse
	50,  // 106: hardware_manager.HardwareManagerService.SetScannersDisable:output_type -> hardware_manager.SetScannersDisableResponse
	52,  // 107: hardware_manager.HardwareManagerService.SetWheelEncoderBoardDisable:output_type -> hardware_manager.SetWheelEncoderBoardDisableResponse
	54,  // 108: hardware_manager.HardwareManagerService.SetWheelEncoderDisable:output_type -> hardware_manager.SetWheelEncoderDisableResponse
	58,  // 109: hardware_manager.HardwareManagerService.SetGPSDisable:output_type -> hardware_manager.SetGPSDisableResponse
	62,  // 110: hardware_manager.HardwareManagerService.SuicideSwitch:output_type -> hardware_manager.SuicideSwitchResponse
	60,  // 111: hardware_manager.HardwareManagerService.CommandComputerPowerCycle:output_type -> hardware_manager.CommandComputerPowerCycleResponse
	64,  // 112: hardware_manager.HardwareManagerService.SetMainContactorDisable:output_type -> hardware_manager.SetMainContactorDisableResponse
	56,  // 113: hardware_manager.HardwareManagerService.SetStrobeDisable:output_type -> hardware_manager.SetStrobeDisableResponse
	66,  // 114: hardware_manager.HardwareManagerService.SetAirConditionerDisable:output_type -> hardware_manager.SetAirConditionerDisableResponse
	68,  // 115: hardware_manager.HardwareManagerService.SetChillerDisable:output_type -> hardware_manager.SetChillerDisableResponse
	70,  // 116: hardware_manager.HardwareManagerService.SetTempBypassDisable:output_type -> hardware_manager.SetTempBypassDisableResponse
	72,  // 117: hardware_manager.HardwareManagerService.SetHumidityBypassDisable:output_type -> hardware_manager.SetHumidityBypassDisableResponse
	78,  // 118: hardware_manager.HardwareManagerService.Get240vUptime:output_type -> hardware_manager.Get240vUptimeResponse
	82,  // 119: hardware_manager.HardwareManagerService.GetRuntime:output_type -> hardware_manager.GetRuntimeResponse
	74,  // 120: hardware_manager.HardwareManagerService.GetAvailableUSBStorage:output_type -> hardware_manager.GetAvailableUSBStorageResponse
	12,  // 121: hardware_manager.HardwareManagerService.SetJimboxSpeed:output_type -> hardware_manager.SetJimboxSpeedResponse
	14,  // 122: hardware_manager.HardwareManagerService.SetCruiseEnabled:output_type -> hardware_manager.SetCruiseEnabledResponse
	16,  // 123: hardware_manager.HardwareManagerService.GetCruiseStatus:output_type -> hardware_manager.GetCruiseStatusResponse
	18,  // 124: hardware_manager.HardwareManagerService.SetImplementStateOnTractor:output_type -> hardware_manager.SetImplementStateResponse
	20,  // 125: hardware_manager.HardwareManagerService.SetSafeStateEnforcement:output_type -> hardware_manager.SetSafeStateEnforcementResponse
	22,  // 126: hardware_manager.HardwareManagerService.GetTractorSafetyState:output_type -> hardware_manager.GetTractorSafetyStateResponse
	24,  // 127: hardware_manager.HardwareManagerService.GetTractorIFState:output_type -> hardware_manager.GetTractorIFStateResponse
	98,  // 128: hardware_manager.HardwareManagerService.GetReaperEnclosureSensors:output_type -> hardware_manager.GetReaperEnclosureSensorsResponse
	100, // 129: hardware_manager.HardwareManagerService.GetReaperModuleSensors:output_type -> hardware_manager.GetReaperModuleSensorsResponse
	102, // 130: hardware_manager.HardwareManagerService.SetReaperScannerPower:output_type -> hardware_manager.SetReaperScannerPowerResponse
	104, // 131: hardware_manager.HardwareManagerService.SetReaperTargetPower:output_type -> hardware_manager.SetReaperTargetPowerResponse
	106, // 132: hardware_manager.HardwareManagerService.SetReaperPredictCamPower:output_type -> hardware_manager.SetReaperPredictCamPowerResponse
	108, // 133: hardware_manager.HardwareManagerService.SetReaperStrobeConfig:output_type -> hardware_manager.SetReaperStrobeConfigResponse
	110, // 134: hardware_manager.HardwareManagerService.SetReaperStrobeEnable:output_type -> hardware_manager.SetReaperStrobeEnableResponse
	112, // 135: hardware_manager.HardwareManagerService.SetReaperModulePcPower:output_type -> hardware_manager.SetReaperModulePcPowerResponse
	114, // 136: hardware_manager.HardwareManagerService.SetReaperModuleLaserPower:output_type -> hardware_manager.SetReaperModuleLaserPowerResponse
	116, // 137: hardware_manager.HardwareManagerService.SetReaperModuleStrobePower:output_type -> hardware_manager.SetReaperModuleStrobePowerResponse
	118, // 138: hardware_manager.HardwareManagerService.IdentifyModule:output_type -> hardware_manager.IdentifyModuleResponse
	87,  // [87:139] is the sub-list for method output_type
	35,  // [35:87] is the sub-list for method input_type
	35,  // [35:35] is the sub-list for extension type_name
	35,  // [35:35] is the sub-list for extension extendee
	0,   // [0:35] is the sub-list for field type_name
}

func init() { file_hardware_manager_proto_hardware_manager_service_proto_init() }
func file_hardware_manager_proto_hardware_manager_service_proto_init() {
	if File_hardware_manager_proto_hardware_manager_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRotaryTicksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRotaryTicksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextDistanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextDistanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVelocityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextVelocityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetJimboxSpeedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetJimboxSpeedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCruiseEnabledRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCruiseEnabledResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCruiseStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCruiseStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImplementStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetImplementStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSafeStateEnforcementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSafeStateEnforcementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTractorSafetyStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTractorSafetyStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTractorIFStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTractorIFStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSafetyStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSafetyStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeoLLA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeoECEF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGPSDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGPSDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextGPSDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextGPSDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextRawGPSDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueWithAccuracy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DualGpsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextRawGPSDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGPSFixedPosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGPSFixedPosResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetManagedBoardErrorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetManagedBoardErrorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupervisoryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReaperSupervisoryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChillerAlarms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupervisoryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetServerDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetServerDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBTLDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBTLDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetScannersDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetScannersDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetWheelEncoderBoardDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetWheelEncoderBoardDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetWheelEncoderDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetWheelEncoderDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetStrobeDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetStrobeDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGPSDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGPSDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommandComputerPowerCycleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommandComputerPowerCycleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuicideSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuicideSwitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMainContactorDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMainContactorDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAirConditionerDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAirConditionerDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetChillerDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetChillerDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTempBypassDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTempBypassDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetHumidityBypassDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetHumidityBypassDisableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableUSBStorageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableUSBStorageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReadyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReadyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get240VUptimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get240VUptimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeltaTravelMMRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeltaTravelMMResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRuntimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRuntimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWheelEncoderResolutionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWheelEncoderResolutionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrobeSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetStrobeSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStrobeSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnvironmentalSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoolantSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkPortState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperPcSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperScannerLaserStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperScannerMotorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperScannerSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperCenterEnclosureData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperModuleSensorData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReaperEnclosureSensorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReaperEnclosureSensorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReaperModuleSensorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReaperModuleSensorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperScannerPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperScannerPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperTargetPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperTargetPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperPredictCamPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperPredictCamPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperStrobeConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperStrobeConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperStrobeEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperStrobeEnableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModulePcPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModulePcPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModuleLaserPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModuleLaserPowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModuleStrobePowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[113].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReaperModuleStrobePowerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentifyModuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[115].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentifyModuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[32].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[33].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[82].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[88].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[91].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[93].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[98].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[100].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[104].OneofWrappers = []interface{}{}
	file_hardware_manager_proto_hardware_manager_service_proto_msgTypes[106].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hardware_manager_proto_hardware_manager_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   116,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hardware_manager_proto_hardware_manager_service_proto_goTypes,
		DependencyIndexes: file_hardware_manager_proto_hardware_manager_service_proto_depIdxs,
		EnumInfos:         file_hardware_manager_proto_hardware_manager_service_proto_enumTypes,
		MessageInfos:      file_hardware_manager_proto_hardware_manager_service_proto_msgTypes,
	}.Build()
	File_hardware_manager_proto_hardware_manager_service_proto = out.File
	file_hardware_manager_proto_hardware_manager_service_proto_rawDesc = nil
	file_hardware_manager_proto_hardware_manager_service_proto_goTypes = nil
	file_hardware_manager_proto_hardware_manager_service_proto_depIdxs = nil
}

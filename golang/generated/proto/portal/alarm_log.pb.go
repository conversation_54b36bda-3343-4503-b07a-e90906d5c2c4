// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/alarm_log.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncAlarmsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alarms    []*frontend.AlarmRow `protobuf:"bytes,1,rep,name=alarms,proto3" json:"alarms,omitempty"`
	RobotName string               `protobuf:"bytes,2,opt,name=robot_name,json=robotName,proto3" json:"robot_name,omitempty"`
}

func (x *SyncAlarmsRequest) Reset() {
	*x = SyncAlarmsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_alarm_log_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAlarmsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAlarmsRequest) ProtoMessage() {}

func (x *SyncAlarmsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_alarm_log_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAlarmsRequest.ProtoReflect.Descriptor instead.
func (*SyncAlarmsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_alarm_log_proto_rawDescGZIP(), []int{0}
}

func (x *SyncAlarmsRequest) GetAlarms() []*frontend.AlarmRow {
	if x != nil {
		return x.Alarms
	}
	return nil
}

func (x *SyncAlarmsRequest) GetRobotName() string {
	if x != nil {
		return x.RobotName
	}
	return ""
}

var File_portal_proto_alarm_log_proto protoreflect.FileDescriptor

var file_portal_proto_alarm_log_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61,
	0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x5f, 0x6c, 0x6f, 0x67, 0x1a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x11,
	0x53, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52,
	0x6f, 0x77, 0x52, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0x6c, 0x0a, 0x15, 0x50, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x6f, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x73,
	0x12, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41,
	0x6c, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_alarm_log_proto_rawDescOnce sync.Once
	file_portal_proto_alarm_log_proto_rawDescData = file_portal_proto_alarm_log_proto_rawDesc
)

func file_portal_proto_alarm_log_proto_rawDescGZIP() []byte {
	file_portal_proto_alarm_log_proto_rawDescOnce.Do(func() {
		file_portal_proto_alarm_log_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_alarm_log_proto_rawDescData)
	})
	return file_portal_proto_alarm_log_proto_rawDescData
}

var file_portal_proto_alarm_log_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_portal_proto_alarm_log_proto_goTypes = []interface{}{
	(*SyncAlarmsRequest)(nil), // 0: carbon.portal.alarm_log.SyncAlarmsRequest
	(*frontend.AlarmRow)(nil), // 1: carbon.frontend.alarm.AlarmRow
	(*Empty)(nil),             // 2: carbon.portal.util.Empty
}
var file_portal_proto_alarm_log_proto_depIdxs = []int32{
	1, // 0: carbon.portal.alarm_log.SyncAlarmsRequest.alarms:type_name -> carbon.frontend.alarm.AlarmRow
	0, // 1: carbon.portal.alarm_log.PortalAlarmLogService.SyncAlarms:input_type -> carbon.portal.alarm_log.SyncAlarmsRequest
	2, // 2: carbon.portal.alarm_log.PortalAlarmLogService.SyncAlarms:output_type -> carbon.portal.util.Empty
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_portal_proto_alarm_log_proto_init() }
func file_portal_proto_alarm_log_proto_init() {
	if File_portal_proto_alarm_log_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_alarm_log_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAlarmsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_alarm_log_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_alarm_log_proto_goTypes,
		DependencyIndexes: file_portal_proto_alarm_log_proto_depIdxs,
		MessageInfos:      file_portal_proto_alarm_log_proto_msgTypes,
	}.Build()
	File_portal_proto_alarm_log_proto = out.File
	file_portal_proto_alarm_log_proto_rawDesc = nil
	file_portal_proto_alarm_log_proto_goTypes = nil
	file_portal_proto_alarm_log_proto_depIdxs = nil
}

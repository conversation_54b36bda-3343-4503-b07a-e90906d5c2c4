// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/alarm_log.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PortalAlarmLogService_SyncAlarms_FullMethodName = "/carbon.portal.alarm_log.PortalAlarmLogService/SyncAlarms"
)

// PortalAlarmLogServiceClient is the client API for PortalAlarmLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PortalAlarmLogServiceClient interface {
	SyncAlarms(ctx context.Context, in *SyncAlarmsRequest, opts ...grpc.CallOption) (*Empty, error)
}

type portalAlarmLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPortalAlarmLogServiceClient(cc grpc.ClientConnInterface) PortalAlarmLogServiceClient {
	return &portalAlarmLogServiceClient{cc}
}

func (c *portalAlarmLogServiceClient) SyncAlarms(ctx context.Context, in *SyncAlarmsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalAlarmLogService_SyncAlarms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PortalAlarmLogServiceServer is the server API for PortalAlarmLogService service.
// All implementations must embed UnimplementedPortalAlarmLogServiceServer
// for forward compatibility
type PortalAlarmLogServiceServer interface {
	SyncAlarms(context.Context, *SyncAlarmsRequest) (*Empty, error)
	mustEmbedUnimplementedPortalAlarmLogServiceServer()
}

// UnimplementedPortalAlarmLogServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPortalAlarmLogServiceServer struct {
}

func (UnimplementedPortalAlarmLogServiceServer) SyncAlarms(context.Context, *SyncAlarmsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncAlarms not implemented")
}
func (UnimplementedPortalAlarmLogServiceServer) mustEmbedUnimplementedPortalAlarmLogServiceServer() {}

// UnsafePortalAlarmLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PortalAlarmLogServiceServer will
// result in compilation errors.
type UnsafePortalAlarmLogServiceServer interface {
	mustEmbedUnimplementedPortalAlarmLogServiceServer()
}

func RegisterPortalAlarmLogServiceServer(s grpc.ServiceRegistrar, srv PortalAlarmLogServiceServer) {
	s.RegisterService(&PortalAlarmLogService_ServiceDesc, srv)
}

func _PortalAlarmLogService_SyncAlarms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncAlarmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalAlarmLogServiceServer).SyncAlarms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalAlarmLogService_SyncAlarms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalAlarmLogServiceServer).SyncAlarms(ctx, req.(*SyncAlarmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PortalAlarmLogService_ServiceDesc is the grpc.ServiceDesc for PortalAlarmLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PortalAlarmLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.alarm_log.PortalAlarmLogService",
	HandlerType: (*PortalAlarmLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncAlarms",
			Handler:    _PortalAlarmLogService_SyncAlarms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/alarm_log.proto",
}

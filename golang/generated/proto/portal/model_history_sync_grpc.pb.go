// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/model_history_sync.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelHistorySyncService_UploadModelEvents_FullMethodName = "/carbon.portal.model_history.ModelHistorySyncService/UploadModelEvents"
)

// ModelHistorySyncServiceClient is the client API for ModelHistorySyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelHistorySyncServiceClient interface {
	UploadModelEvents(ctx context.Context, in *UploadModelEventsRequest, opts ...grpc.CallOption) (*Empty, error)
}

type modelHistorySyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModelHistorySyncServiceClient(cc grpc.ClientConnInterface) ModelHistorySyncServiceClient {
	return &modelHistorySyncServiceClient{cc}
}

func (c *modelHistorySyncServiceClient) UploadModelEvents(ctx context.Context, in *UploadModelEventsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelHistorySyncService_UploadModelEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelHistorySyncServiceServer is the server API for ModelHistorySyncService service.
// All implementations must embed UnimplementedModelHistorySyncServiceServer
// for forward compatibility
type ModelHistorySyncServiceServer interface {
	UploadModelEvents(context.Context, *UploadModelEventsRequest) (*Empty, error)
	mustEmbedUnimplementedModelHistorySyncServiceServer()
}

// UnimplementedModelHistorySyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModelHistorySyncServiceServer struct {
}

func (UnimplementedModelHistorySyncServiceServer) UploadModelEvents(context.Context, *UploadModelEventsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadModelEvents not implemented")
}
func (UnimplementedModelHistorySyncServiceServer) mustEmbedUnimplementedModelHistorySyncServiceServer() {
}

// UnsafeModelHistorySyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelHistorySyncServiceServer will
// result in compilation errors.
type UnsafeModelHistorySyncServiceServer interface {
	mustEmbedUnimplementedModelHistorySyncServiceServer()
}

func RegisterModelHistorySyncServiceServer(s grpc.ServiceRegistrar, srv ModelHistorySyncServiceServer) {
	s.RegisterService(&ModelHistorySyncService_ServiceDesc, srv)
}

func _ModelHistorySyncService_UploadModelEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadModelEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelHistorySyncServiceServer).UploadModelEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelHistorySyncService_UploadModelEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelHistorySyncServiceServer).UploadModelEvents(ctx, req.(*UploadModelEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelHistorySyncService_ServiceDesc is the grpc.ServiceDesc for ModelHistorySyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelHistorySyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.model_history.ModelHistorySyncService",
	HandlerType: (*ModelHistorySyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadModelEvents",
			Handler:    _ModelHistorySyncService_UploadModelEvents_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/model_history_sync.proto",
}

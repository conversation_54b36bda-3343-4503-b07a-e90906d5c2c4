// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/jobs_portal.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	metrics_aggregator "github.com/carbonrobotics/robot/golang/generated/proto/metrics_aggregator"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job   *frontend.Job `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	Robot string        `protobuf:"bytes,2,opt,name=robot,proto3" json:"robot,omitempty"`
}

func (x *UploadJobRequest) Reset() {
	*x = UploadJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_jobs_portal_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadJobRequest) ProtoMessage() {}

func (x *UploadJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_jobs_portal_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadJobRequest.ProtoReflect.Descriptor instead.
func (*UploadJobRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_jobs_portal_proto_rawDescGZIP(), []int{0}
}

func (x *UploadJobRequest) GetJob() *frontend.Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *UploadJobRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

type UploadJobConfigDumpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId      string                       `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
	RootConfig *frontend.ConfigNodeSnapshot `protobuf:"bytes,2,opt,name=rootConfig,proto3" json:"rootConfig,omitempty"`
}

func (x *UploadJobConfigDumpRequest) Reset() {
	*x = UploadJobConfigDumpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_jobs_portal_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadJobConfigDumpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadJobConfigDumpRequest) ProtoMessage() {}

func (x *UploadJobConfigDumpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_jobs_portal_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadJobConfigDumpRequest.ProtoReflect.Descriptor instead.
func (*UploadJobConfigDumpRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_jobs_portal_proto_rawDescGZIP(), []int{1}
}

func (x *UploadJobConfigDumpRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *UploadJobConfigDumpRequest) GetRootConfig() *frontend.ConfigNodeSnapshot {
	if x != nil {
		return x.RootConfig
	}
	return nil
}

type UploadJobMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId      string                      `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
	JobMetrics *metrics_aggregator.Metrics `protobuf:"bytes,2,opt,name=jobMetrics,proto3" json:"jobMetrics,omitempty"`
}

func (x *UploadJobMetricsRequest) Reset() {
	*x = UploadJobMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_jobs_portal_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadJobMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadJobMetricsRequest) ProtoMessage() {}

func (x *UploadJobMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_jobs_portal_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadJobMetricsRequest.ProtoReflect.Descriptor instead.
func (*UploadJobMetricsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_jobs_portal_proto_rawDescGZIP(), []int{2}
}

func (x *UploadJobMetricsRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *UploadJobMetricsRequest) GetJobMetrics() *metrics_aggregator.Metrics {
	if x != nil {
		return x.JobMetrics
	}
	return nil
}

var File_portal_proto_jobs_portal_proto protoreflect.FileDescriptor

var file_portal_proto_jobs_portal_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6a,
	0x6f, 0x62, 0x73, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x6a, 0x6f, 0x62, 0x73, 0x1a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6a, 0x6f,
	0x62, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x55, 0x0a, 0x10, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03,
	0x6a, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x1a, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x75, 0x6d,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x57,
	0x0a, 0x0a, 0x72, 0x6f, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e,
	0x6f, 0x64, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x0a, 0x72, 0x6f, 0x6f,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x6c, 0x0a, 0x17, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0a, 0x6a, 0x6f, 0x62, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x32, 0x9f, 0x02, 0x0a, 0x11, 0x50, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x4a, 0x6f, 0x62, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x12, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x13, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x75, 0x6d, 0x70,
	0x12, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x75, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5a, 0x0a, 0x10, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12,
	0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x6a, 0x6f, 0x62, 0x73, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_jobs_portal_proto_rawDescOnce sync.Once
	file_portal_proto_jobs_portal_proto_rawDescData = file_portal_proto_jobs_portal_proto_rawDesc
)

func file_portal_proto_jobs_portal_proto_rawDescGZIP() []byte {
	file_portal_proto_jobs_portal_proto_rawDescOnce.Do(func() {
		file_portal_proto_jobs_portal_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_jobs_portal_proto_rawDescData)
	})
	return file_portal_proto_jobs_portal_proto_rawDescData
}

var file_portal_proto_jobs_portal_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_portal_proto_jobs_portal_proto_goTypes = []interface{}{
	(*UploadJobRequest)(nil),            // 0: carbon.portal.jobs.UploadJobRequest
	(*UploadJobConfigDumpRequest)(nil),  // 1: carbon.portal.jobs.UploadJobConfigDumpRequest
	(*UploadJobMetricsRequest)(nil),     // 2: carbon.portal.jobs.UploadJobMetricsRequest
	(*frontend.Job)(nil),                // 3: carbon.frontend.jobs.Job
	(*frontend.ConfigNodeSnapshot)(nil), // 4: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	(*metrics_aggregator.Metrics)(nil),  // 5: metrics_aggregator.Metrics
	(*Empty)(nil),                       // 6: carbon.portal.util.Empty
}
var file_portal_proto_jobs_portal_proto_depIdxs = []int32{
	3, // 0: carbon.portal.jobs.UploadJobRequest.job:type_name -> carbon.frontend.jobs.Job
	4, // 1: carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig:type_name -> carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	5, // 2: carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics:type_name -> metrics_aggregator.Metrics
	0, // 3: carbon.portal.jobs.PortalJobsService.UploadJob:input_type -> carbon.portal.jobs.UploadJobRequest
	1, // 4: carbon.portal.jobs.PortalJobsService.UploadJobConfigDump:input_type -> carbon.portal.jobs.UploadJobConfigDumpRequest
	2, // 5: carbon.portal.jobs.PortalJobsService.UploadJobMetrics:input_type -> carbon.portal.jobs.UploadJobMetricsRequest
	6, // 6: carbon.portal.jobs.PortalJobsService.UploadJob:output_type -> carbon.portal.util.Empty
	6, // 7: carbon.portal.jobs.PortalJobsService.UploadJobConfigDump:output_type -> carbon.portal.util.Empty
	6, // 8: carbon.portal.jobs.PortalJobsService.UploadJobMetrics:output_type -> carbon.portal.util.Empty
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_portal_proto_jobs_portal_proto_init() }
func file_portal_proto_jobs_portal_proto_init() {
	if File_portal_proto_jobs_portal_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_jobs_portal_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_jobs_portal_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadJobConfigDumpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_jobs_portal_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadJobMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_jobs_portal_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_jobs_portal_proto_goTypes,
		DependencyIndexes: file_portal_proto_jobs_portal_proto_depIdxs,
		MessageInfos:      file_portal_proto_jobs_portal_proto_msgTypes,
	}.Build()
	File_portal_proto_jobs_portal_proto = out.File
	file_portal_proto_jobs_portal_proto_rawDesc = nil
	file_portal_proto_jobs_portal_proto_goTypes = nil
	file_portal_proto_jobs_portal_proto_depIdxs = nil
}

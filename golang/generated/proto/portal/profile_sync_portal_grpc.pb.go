// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/profile_sync_portal.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PortalProfileSyncService_GetProfilesData_FullMethodName               = "/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData"
	PortalProfileSyncService_UploadProfile_FullMethodName                 = "/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile"
	PortalProfileSyncService_GetProfile_FullMethodName                    = "/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile"
	PortalProfileSyncService_DeleteProfile_FullMethodName                 = "/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile"
	PortalProfileSyncService_PurgeProfile_FullMethodName                  = "/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile"
	PortalProfileSyncService_GetSetActiveProfileCommands_FullMethodName   = "/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands"
	PortalProfileSyncService_PurgeSetActiveProfileCommands_FullMethodName = "/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands"
)

// PortalProfileSyncServiceClient is the client API for PortalProfileSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PortalProfileSyncServiceClient interface {
	GetProfilesData(ctx context.Context, in *GetProfilesDataRequest, opts ...grpc.CallOption) (*GetProfilesDataResponse, error)
	UploadProfile(ctx context.Context, in *UploadProfileRequest, opts ...grpc.CallOption) (*Empty, error)
	GetProfile(ctx context.Context, in *GetProfileRequest, opts ...grpc.CallOption) (*GetProfileResponse, error)
	DeleteProfile(ctx context.Context, in *DeleteProfileRequest, opts ...grpc.CallOption) (*Empty, error)
	PurgeProfile(ctx context.Context, in *PurgeProfileRequest, opts ...grpc.CallOption) (*Empty, error)
	GetSetActiveProfileCommands(ctx context.Context, in *GetSetActiveProfileCommandsRequest, opts ...grpc.CallOption) (*GetSetActiveProfileCommandsResponse, error)
	PurgeSetActiveProfileCommands(ctx context.Context, in *PurgeSetActiveProfileCommandsRequest, opts ...grpc.CallOption) (*Empty, error)
}

type portalProfileSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPortalProfileSyncServiceClient(cc grpc.ClientConnInterface) PortalProfileSyncServiceClient {
	return &portalProfileSyncServiceClient{cc}
}

func (c *portalProfileSyncServiceClient) GetProfilesData(ctx context.Context, in *GetProfilesDataRequest, opts ...grpc.CallOption) (*GetProfilesDataResponse, error) {
	out := new(GetProfilesDataResponse)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_GetProfilesData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalProfileSyncServiceClient) UploadProfile(ctx context.Context, in *UploadProfileRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_UploadProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalProfileSyncServiceClient) GetProfile(ctx context.Context, in *GetProfileRequest, opts ...grpc.CallOption) (*GetProfileResponse, error) {
	out := new(GetProfileResponse)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_GetProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalProfileSyncServiceClient) DeleteProfile(ctx context.Context, in *DeleteProfileRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_DeleteProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalProfileSyncServiceClient) PurgeProfile(ctx context.Context, in *PurgeProfileRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_PurgeProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalProfileSyncServiceClient) GetSetActiveProfileCommands(ctx context.Context, in *GetSetActiveProfileCommandsRequest, opts ...grpc.CallOption) (*GetSetActiveProfileCommandsResponse, error) {
	out := new(GetSetActiveProfileCommandsResponse)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_GetSetActiveProfileCommands_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalProfileSyncServiceClient) PurgeSetActiveProfileCommands(ctx context.Context, in *PurgeSetActiveProfileCommandsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalProfileSyncService_PurgeSetActiveProfileCommands_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PortalProfileSyncServiceServer is the server API for PortalProfileSyncService service.
// All implementations must embed UnimplementedPortalProfileSyncServiceServer
// for forward compatibility
type PortalProfileSyncServiceServer interface {
	GetProfilesData(context.Context, *GetProfilesDataRequest) (*GetProfilesDataResponse, error)
	UploadProfile(context.Context, *UploadProfileRequest) (*Empty, error)
	GetProfile(context.Context, *GetProfileRequest) (*GetProfileResponse, error)
	DeleteProfile(context.Context, *DeleteProfileRequest) (*Empty, error)
	PurgeProfile(context.Context, *PurgeProfileRequest) (*Empty, error)
	GetSetActiveProfileCommands(context.Context, *GetSetActiveProfileCommandsRequest) (*GetSetActiveProfileCommandsResponse, error)
	PurgeSetActiveProfileCommands(context.Context, *PurgeSetActiveProfileCommandsRequest) (*Empty, error)
	mustEmbedUnimplementedPortalProfileSyncServiceServer()
}

// UnimplementedPortalProfileSyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPortalProfileSyncServiceServer struct {
}

func (UnimplementedPortalProfileSyncServiceServer) GetProfilesData(context.Context, *GetProfilesDataRequest) (*GetProfilesDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfilesData not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) UploadProfile(context.Context, *UploadProfileRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadProfile not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) GetProfile(context.Context, *GetProfileRequest) (*GetProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfile not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) DeleteProfile(context.Context, *DeleteProfileRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProfile not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) PurgeProfile(context.Context, *PurgeProfileRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurgeProfile not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) GetSetActiveProfileCommands(context.Context, *GetSetActiveProfileCommandsRequest) (*GetSetActiveProfileCommandsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSetActiveProfileCommands not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) PurgeSetActiveProfileCommands(context.Context, *PurgeSetActiveProfileCommandsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurgeSetActiveProfileCommands not implemented")
}
func (UnimplementedPortalProfileSyncServiceServer) mustEmbedUnimplementedPortalProfileSyncServiceServer() {
}

// UnsafePortalProfileSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PortalProfileSyncServiceServer will
// result in compilation errors.
type UnsafePortalProfileSyncServiceServer interface {
	mustEmbedUnimplementedPortalProfileSyncServiceServer()
}

func RegisterPortalProfileSyncServiceServer(s grpc.ServiceRegistrar, srv PortalProfileSyncServiceServer) {
	s.RegisterService(&PortalProfileSyncService_ServiceDesc, srv)
}

func _PortalProfileSyncService_GetProfilesData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfilesDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).GetProfilesData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_GetProfilesData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).GetProfilesData(ctx, req.(*GetProfilesDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalProfileSyncService_UploadProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).UploadProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_UploadProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).UploadProfile(ctx, req.(*UploadProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalProfileSyncService_GetProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).GetProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_GetProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).GetProfile(ctx, req.(*GetProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalProfileSyncService_DeleteProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).DeleteProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_DeleteProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).DeleteProfile(ctx, req.(*DeleteProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalProfileSyncService_PurgeProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurgeProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).PurgeProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_PurgeProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).PurgeProfile(ctx, req.(*PurgeProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalProfileSyncService_GetSetActiveProfileCommands_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSetActiveProfileCommandsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).GetSetActiveProfileCommands(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_GetSetActiveProfileCommands_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).GetSetActiveProfileCommands(ctx, req.(*GetSetActiveProfileCommandsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalProfileSyncService_PurgeSetActiveProfileCommands_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurgeSetActiveProfileCommandsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalProfileSyncServiceServer).PurgeSetActiveProfileCommands(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalProfileSyncService_PurgeSetActiveProfileCommands_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalProfileSyncServiceServer).PurgeSetActiveProfileCommands(ctx, req.(*PurgeSetActiveProfileCommandsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PortalProfileSyncService_ServiceDesc is the grpc.ServiceDesc for PortalProfileSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PortalProfileSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.profile_sync.PortalProfileSyncService",
	HandlerType: (*PortalProfileSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProfilesData",
			Handler:    _PortalProfileSyncService_GetProfilesData_Handler,
		},
		{
			MethodName: "UploadProfile",
			Handler:    _PortalProfileSyncService_UploadProfile_Handler,
		},
		{
			MethodName: "GetProfile",
			Handler:    _PortalProfileSyncService_GetProfile_Handler,
		},
		{
			MethodName: "DeleteProfile",
			Handler:    _PortalProfileSyncService_DeleteProfile_Handler,
		},
		{
			MethodName: "PurgeProfile",
			Handler:    _PortalProfileSyncService_PurgeProfile_Handler,
		},
		{
			MethodName: "GetSetActiveProfileCommands",
			Handler:    _PortalProfileSyncService_GetSetActiveProfileCommands_Handler,
		},
		{
			MethodName: "PurgeSetActiveProfileCommands",
			Handler:    _PortalProfileSyncService_PurgeSetActiveProfileCommands_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/profile_sync_portal.proto",
}

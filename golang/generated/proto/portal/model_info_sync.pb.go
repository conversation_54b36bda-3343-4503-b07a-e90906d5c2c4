// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/model_info_sync.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId  string   `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	CropIds  []string `protobuf:"bytes,2,rep,name=crop_ids,json=cropIds,proto3" json:"crop_ids,omitempty"`
	Nickname string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
}

func (x *ModelInfo) Reset() {
	*x = ModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_info_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInfo) ProtoMessage() {}

func (x *ModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_info_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInfo.ProtoReflect.Descriptor instead.
func (*ModelInfo) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_info_sync_proto_rawDescGZIP(), []int{0}
}

func (x *ModelInfo) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelInfo) GetCropIds() []string {
	if x != nil {
		return x.CropIds
	}
	return nil
}

func (x *ModelInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type UploadModelInfosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot      string       `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	ModelInfos []*ModelInfo `protobuf:"bytes,2,rep,name=model_infos,json=modelInfos,proto3" json:"model_infos,omitempty"`
}

func (x *UploadModelInfosRequest) Reset() {
	*x = UploadModelInfosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_info_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadModelInfosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadModelInfosRequest) ProtoMessage() {}

func (x *UploadModelInfosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_info_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadModelInfosRequest.ProtoReflect.Descriptor instead.
func (*UploadModelInfosRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_info_sync_proto_rawDescGZIP(), []int{1}
}

func (x *UploadModelInfosRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

func (x *UploadModelInfosRequest) GetModelInfos() []*ModelInfo {
	if x != nil {
		return x.ModelInfos
	}
	return nil
}

type RenameModelCommand struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId     string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	NewNickname string `protobuf:"bytes,2,opt,name=new_nickname,json=newNickname,proto3" json:"new_nickname,omitempty"`
}

func (x *RenameModelCommand) Reset() {
	*x = RenameModelCommand{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_info_sync_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenameModelCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenameModelCommand) ProtoMessage() {}

func (x *RenameModelCommand) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_info_sync_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenameModelCommand.ProtoReflect.Descriptor instead.
func (*RenameModelCommand) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_info_sync_proto_rawDescGZIP(), []int{2}
}

func (x *RenameModelCommand) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *RenameModelCommand) GetNewNickname() string {
	if x != nil {
		return x.NewNickname
	}
	return ""
}

type GetRenameModelCommandsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot string `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
}

func (x *GetRenameModelCommandsRequest) Reset() {
	*x = GetRenameModelCommandsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_info_sync_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRenameModelCommandsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRenameModelCommandsRequest) ProtoMessage() {}

func (x *GetRenameModelCommandsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_info_sync_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRenameModelCommandsRequest.ProtoReflect.Descriptor instead.
func (*GetRenameModelCommandsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_info_sync_proto_rawDescGZIP(), []int{3}
}

func (x *GetRenameModelCommandsRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

type GetRenameModelCommandsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commands []*RenameModelCommand `protobuf:"bytes,1,rep,name=commands,proto3" json:"commands,omitempty"`
}

func (x *GetRenameModelCommandsResponse) Reset() {
	*x = GetRenameModelCommandsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_info_sync_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRenameModelCommandsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRenameModelCommandsResponse) ProtoMessage() {}

func (x *GetRenameModelCommandsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_info_sync_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRenameModelCommandsResponse.ProtoReflect.Descriptor instead.
func (*GetRenameModelCommandsResponse) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_info_sync_proto_rawDescGZIP(), []int{4}
}

func (x *GetRenameModelCommandsResponse) GetCommands() []*RenameModelCommand {
	if x != nil {
		return x.Commands
	}
	return nil
}

type PurgeRenameModelCommandsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot    string                `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	Commands []*RenameModelCommand `protobuf:"bytes,2,rep,name=commands,proto3" json:"commands,omitempty"`
}

func (x *PurgeRenameModelCommandsRequest) Reset() {
	*x = PurgeRenameModelCommandsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_info_sync_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurgeRenameModelCommandsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurgeRenameModelCommandsRequest) ProtoMessage() {}

func (x *PurgeRenameModelCommandsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_info_sync_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurgeRenameModelCommandsRequest.ProtoReflect.Descriptor instead.
func (*PurgeRenameModelCommandsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_info_sync_proto_rawDescGZIP(), []int{5}
}

func (x *PurgeRenameModelCommandsRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

func (x *PurgeRenameModelCommandsRequest) GetCommands() []*RenameModelCommand {
	if x != nil {
		return x.Commands
	}
	return nil
}

var File_portal_proto_model_info_sync_proto protoreflect.FileDescriptor

var file_portal_proto_model_info_sync_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x17,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x75, 0x0a, 0x17, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x44, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x52, 0x0a,
	0x12, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x35, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x22, 0x6a, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x63, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x1f, 0x50, 0x75, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x48,
	0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x6e, 0x61,
	0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x32, 0xf8, 0x02, 0x0a, 0x14, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x60, 0x0a, 0x10, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x8b, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x37,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x70, 0x0a, 0x18, 0x50, 0x75, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x39, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x75, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_model_info_sync_proto_rawDescOnce sync.Once
	file_portal_proto_model_info_sync_proto_rawDescData = file_portal_proto_model_info_sync_proto_rawDesc
)

func file_portal_proto_model_info_sync_proto_rawDescGZIP() []byte {
	file_portal_proto_model_info_sync_proto_rawDescOnce.Do(func() {
		file_portal_proto_model_info_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_model_info_sync_proto_rawDescData)
	})
	return file_portal_proto_model_info_sync_proto_rawDescData
}

var file_portal_proto_model_info_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_portal_proto_model_info_sync_proto_goTypes = []interface{}{
	(*ModelInfo)(nil),                       // 0: carbon.portal.model_info.ModelInfo
	(*UploadModelInfosRequest)(nil),         // 1: carbon.portal.model_info.UploadModelInfosRequest
	(*RenameModelCommand)(nil),              // 2: carbon.portal.model_info.RenameModelCommand
	(*GetRenameModelCommandsRequest)(nil),   // 3: carbon.portal.model_info.GetRenameModelCommandsRequest
	(*GetRenameModelCommandsResponse)(nil),  // 4: carbon.portal.model_info.GetRenameModelCommandsResponse
	(*PurgeRenameModelCommandsRequest)(nil), // 5: carbon.portal.model_info.PurgeRenameModelCommandsRequest
	(*Empty)(nil),                           // 6: carbon.portal.util.Empty
}
var file_portal_proto_model_info_sync_proto_depIdxs = []int32{
	0, // 0: carbon.portal.model_info.UploadModelInfosRequest.model_infos:type_name -> carbon.portal.model_info.ModelInfo
	2, // 1: carbon.portal.model_info.GetRenameModelCommandsResponse.commands:type_name -> carbon.portal.model_info.RenameModelCommand
	2, // 2: carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands:type_name -> carbon.portal.model_info.RenameModelCommand
	1, // 3: carbon.portal.model_info.ModelInfoSyncService.UploadModelInfos:input_type -> carbon.portal.model_info.UploadModelInfosRequest
	3, // 4: carbon.portal.model_info.ModelInfoSyncService.GetRenameModelCommands:input_type -> carbon.portal.model_info.GetRenameModelCommandsRequest
	5, // 5: carbon.portal.model_info.ModelInfoSyncService.PurgeRenameModelCommands:input_type -> carbon.portal.model_info.PurgeRenameModelCommandsRequest
	6, // 6: carbon.portal.model_info.ModelInfoSyncService.UploadModelInfos:output_type -> carbon.portal.util.Empty
	4, // 7: carbon.portal.model_info.ModelInfoSyncService.GetRenameModelCommands:output_type -> carbon.portal.model_info.GetRenameModelCommandsResponse
	6, // 8: carbon.portal.model_info.ModelInfoSyncService.PurgeRenameModelCommands:output_type -> carbon.portal.util.Empty
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_portal_proto_model_info_sync_proto_init() }
func file_portal_proto_model_info_sync_proto_init() {
	if File_portal_proto_model_info_sync_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_model_info_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_model_info_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadModelInfosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_model_info_sync_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenameModelCommand); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_model_info_sync_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRenameModelCommandsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_model_info_sync_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRenameModelCommandsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_model_info_sync_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurgeRenameModelCommandsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_model_info_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_model_info_sync_proto_goTypes,
		DependencyIndexes: file_portal_proto_model_info_sync_proto_depIdxs,
		MessageInfos:      file_portal_proto_model_info_sync_proto_msgTypes,
	}.Build()
	File_portal_proto_model_info_sync_proto = out.File
	file_portal_proto_model_info_sync_proto_rawDesc = nil
	file_portal_proto_model_info_sync_proto_goTypes = nil
	file_portal_proto_model_info_sync_proto_depIdxs = nil
}

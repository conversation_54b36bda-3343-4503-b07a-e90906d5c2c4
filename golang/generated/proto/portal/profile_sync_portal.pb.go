// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/profile_sync_portal.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	almanac "github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	target_velocity_estimator "github.com/carbonrobotics/robot/golang/generated/proto/target_velocity_estimator"
	thinning "github.com/carbonrobotics/robot/golang/generated/proto/thinning"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetProfilesDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotName string `protobuf:"bytes,1,opt,name=robot_name,json=robotName,proto3" json:"robot_name,omitempty"`
}

func (x *GetProfilesDataRequest) Reset() {
	*x = GetProfilesDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfilesDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfilesDataRequest) ProtoMessage() {}

func (x *GetProfilesDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfilesDataRequest.ProtoReflect.Descriptor instead.
func (*GetProfilesDataRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{0}
}

func (x *GetProfilesDataRequest) GetRobotName() string {
	if x != nil {
		return x.RobotName
	}
	return ""
}

type GetProfilesDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profiles map[string]*frontend.ProfileSyncData `protobuf:"bytes,1,rep,name=profiles,proto3" json:"profiles,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetProfilesDataResponse) Reset() {
	*x = GetProfilesDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfilesDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfilesDataResponse) ProtoMessage() {}

func (x *GetProfilesDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfilesDataResponse.ProtoReflect.Descriptor instead.
func (*GetProfilesDataResponse) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{1}
}

func (x *GetProfilesDataResponse) GetProfiles() map[string]*frontend.ProfileSyncData {
	if x != nil {
		return x.Profiles
	}
	return nil
}

type UploadProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastUpdateTimeMs int64  `protobuf:"varint,1,opt,name=last_update_time_ms,json=lastUpdateTimeMs,proto3" json:"last_update_time_ms,omitempty"`
	RobotName        string `protobuf:"bytes,2,opt,name=robot_name,json=robotName,proto3" json:"robot_name,omitempty"`
	// Types that are assignable to Profile:
	//
	//	*UploadProfileRequest_Almanac
	//	*UploadProfileRequest_Discriminator
	//	*UploadProfileRequest_Modelinator
	//	*UploadProfileRequest_Banding
	//	*UploadProfileRequest_Thinning
	//	*UploadProfileRequest_TargetVel
	Profile isUploadProfileRequest_Profile `protobuf_oneof:"profile"`
}

func (x *UploadProfileRequest) Reset() {
	*x = UploadProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadProfileRequest) ProtoMessage() {}

func (x *UploadProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadProfileRequest.ProtoReflect.Descriptor instead.
func (*UploadProfileRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{2}
}

func (x *UploadProfileRequest) GetLastUpdateTimeMs() int64 {
	if x != nil {
		return x.LastUpdateTimeMs
	}
	return 0
}

func (x *UploadProfileRequest) GetRobotName() string {
	if x != nil {
		return x.RobotName
	}
	return ""
}

func (m *UploadProfileRequest) GetProfile() isUploadProfileRequest_Profile {
	if m != nil {
		return m.Profile
	}
	return nil
}

func (x *UploadProfileRequest) GetAlmanac() *almanac.AlmanacConfig {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Almanac); ok {
		return x.Almanac
	}
	return nil
}

func (x *UploadProfileRequest) GetDiscriminator() *almanac.DiscriminatorConfig {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Discriminator); ok {
		return x.Discriminator
	}
	return nil
}

func (x *UploadProfileRequest) GetModelinator() *almanac.ModelinatorConfig {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Modelinator); ok {
		return x.Modelinator
	}
	return nil
}

func (x *UploadProfileRequest) GetBanding() *frontend.BandingDef {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Banding); ok {
		return x.Banding
	}
	return nil
}

func (x *UploadProfileRequest) GetThinning() *thinning.ConfigDefinition {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Thinning); ok {
		return x.Thinning
	}
	return nil
}

func (x *UploadProfileRequest) GetTargetVel() *target_velocity_estimator.TVEProfile {
	if x, ok := x.GetProfile().(*UploadProfileRequest_TargetVel); ok {
		return x.TargetVel
	}
	return nil
}

type isUploadProfileRequest_Profile interface {
	isUploadProfileRequest_Profile()
}

type UploadProfileRequest_Almanac struct {
	Almanac *almanac.AlmanacConfig `protobuf:"bytes,3,opt,name=almanac,proto3,oneof"`
}

type UploadProfileRequest_Discriminator struct {
	Discriminator *almanac.DiscriminatorConfig `protobuf:"bytes,4,opt,name=discriminator,proto3,oneof"`
}

type UploadProfileRequest_Modelinator struct {
	Modelinator *almanac.ModelinatorConfig `protobuf:"bytes,5,opt,name=modelinator,proto3,oneof"`
}

type UploadProfileRequest_Banding struct {
	Banding *frontend.BandingDef `protobuf:"bytes,6,opt,name=banding,proto3,oneof"`
}

type UploadProfileRequest_Thinning struct {
	Thinning *thinning.ConfigDefinition `protobuf:"bytes,7,opt,name=thinning,proto3,oneof"`
}

type UploadProfileRequest_TargetVel struct {
	TargetVel *target_velocity_estimator.TVEProfile `protobuf:"bytes,8,opt,name=target_vel,json=targetVel,proto3,oneof"`
}

func (*UploadProfileRequest_Almanac) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Discriminator) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Modelinator) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Banding) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Thinning) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_TargetVel) isUploadProfileRequest_Profile() {}

type GetProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *GetProfileRequest) Reset() {
	*x = GetProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileRequest) ProtoMessage() {}

func (x *GetProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileRequest.ProtoReflect.Descriptor instead.
func (*GetProfileRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{3}
}

func (x *GetProfileRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type GetProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Profile:
	//
	//	*GetProfileResponse_Almanac
	//	*GetProfileResponse_Discriminator
	//	*GetProfileResponse_Modelinator
	//	*GetProfileResponse_Banding
	//	*GetProfileResponse_Thinning
	//	*GetProfileResponse_TargetVel
	Profile isGetProfileResponse_Profile `protobuf_oneof:"profile"`
}

func (x *GetProfileResponse) Reset() {
	*x = GetProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileResponse) ProtoMessage() {}

func (x *GetProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileResponse.ProtoReflect.Descriptor instead.
func (*GetProfileResponse) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{4}
}

func (m *GetProfileResponse) GetProfile() isGetProfileResponse_Profile {
	if m != nil {
		return m.Profile
	}
	return nil
}

func (x *GetProfileResponse) GetAlmanac() *almanac.AlmanacConfig {
	if x, ok := x.GetProfile().(*GetProfileResponse_Almanac); ok {
		return x.Almanac
	}
	return nil
}

func (x *GetProfileResponse) GetDiscriminator() *almanac.DiscriminatorConfig {
	if x, ok := x.GetProfile().(*GetProfileResponse_Discriminator); ok {
		return x.Discriminator
	}
	return nil
}

func (x *GetProfileResponse) GetModelinator() *almanac.ModelinatorConfig {
	if x, ok := x.GetProfile().(*GetProfileResponse_Modelinator); ok {
		return x.Modelinator
	}
	return nil
}

func (x *GetProfileResponse) GetBanding() *frontend.BandingDef {
	if x, ok := x.GetProfile().(*GetProfileResponse_Banding); ok {
		return x.Banding
	}
	return nil
}

func (x *GetProfileResponse) GetThinning() *thinning.ConfigDefinition {
	if x, ok := x.GetProfile().(*GetProfileResponse_Thinning); ok {
		return x.Thinning
	}
	return nil
}

func (x *GetProfileResponse) GetTargetVel() *target_velocity_estimator.TVEProfile {
	if x, ok := x.GetProfile().(*GetProfileResponse_TargetVel); ok {
		return x.TargetVel
	}
	return nil
}

type isGetProfileResponse_Profile interface {
	isGetProfileResponse_Profile()
}

type GetProfileResponse_Almanac struct {
	Almanac *almanac.AlmanacConfig `protobuf:"bytes,1,opt,name=almanac,proto3,oneof"`
}

type GetProfileResponse_Discriminator struct {
	Discriminator *almanac.DiscriminatorConfig `protobuf:"bytes,2,opt,name=discriminator,proto3,oneof"`
}

type GetProfileResponse_Modelinator struct {
	Modelinator *almanac.ModelinatorConfig `protobuf:"bytes,3,opt,name=modelinator,proto3,oneof"`
}

type GetProfileResponse_Banding struct {
	Banding *frontend.BandingDef `protobuf:"bytes,4,opt,name=banding,proto3,oneof"`
}

type GetProfileResponse_Thinning struct {
	Thinning *thinning.ConfigDefinition `protobuf:"bytes,5,opt,name=thinning,proto3,oneof"`
}

type GetProfileResponse_TargetVel struct {
	TargetVel *target_velocity_estimator.TVEProfile `protobuf:"bytes,6,opt,name=target_vel,json=targetVel,proto3,oneof"`
}

func (*GetProfileResponse_Almanac) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Discriminator) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Modelinator) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Banding) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Thinning) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_TargetVel) isGetProfileResponse_Profile() {}

type DeleteProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *DeleteProfileRequest) Reset() {
	*x = DeleteProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProfileRequest) ProtoMessage() {}

func (x *DeleteProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProfileRequest.ProtoReflect.Descriptor instead.
func (*DeleteProfileRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteProfileRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type PurgeProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *PurgeProfileRequest) Reset() {
	*x = PurgeProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurgeProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurgeProfileRequest) ProtoMessage() {}

func (x *PurgeProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurgeProfileRequest.ProtoReflect.Descriptor instead.
func (*PurgeProfileRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{6}
}

func (x *PurgeProfileRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type SetActiveProfileCommand struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProfileType frontend.ProfileType `protobuf:"varint,1,opt,name=profile_type,json=profileType,proto3,enum=carbon.frontend.profile_sync.ProfileType" json:"profile_type,omitempty"`
	Uuid        string               `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *SetActiveProfileCommand) Reset() {
	*x = SetActiveProfileCommand{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActiveProfileCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActiveProfileCommand) ProtoMessage() {}

func (x *SetActiveProfileCommand) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActiveProfileCommand.ProtoReflect.Descriptor instead.
func (*SetActiveProfileCommand) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{7}
}

func (x *SetActiveProfileCommand) GetProfileType() frontend.ProfileType {
	if x != nil {
		return x.ProfileType
	}
	return frontend.ProfileType(0)
}

func (x *SetActiveProfileCommand) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type GetSetActiveProfileCommandsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot string `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
}

func (x *GetSetActiveProfileCommandsRequest) Reset() {
	*x = GetSetActiveProfileCommandsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSetActiveProfileCommandsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSetActiveProfileCommandsRequest) ProtoMessage() {}

func (x *GetSetActiveProfileCommandsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSetActiveProfileCommandsRequest.ProtoReflect.Descriptor instead.
func (*GetSetActiveProfileCommandsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{8}
}

func (x *GetSetActiveProfileCommandsRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

type GetSetActiveProfileCommandsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commands []*SetActiveProfileCommand `protobuf:"bytes,1,rep,name=commands,proto3" json:"commands,omitempty"`
}

func (x *GetSetActiveProfileCommandsResponse) Reset() {
	*x = GetSetActiveProfileCommandsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSetActiveProfileCommandsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSetActiveProfileCommandsResponse) ProtoMessage() {}

func (x *GetSetActiveProfileCommandsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSetActiveProfileCommandsResponse.ProtoReflect.Descriptor instead.
func (*GetSetActiveProfileCommandsResponse) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{9}
}

func (x *GetSetActiveProfileCommandsResponse) GetCommands() []*SetActiveProfileCommand {
	if x != nil {
		return x.Commands
	}
	return nil
}

type PurgeSetActiveProfileCommandsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot    string                     `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	Commands []*SetActiveProfileCommand `protobuf:"bytes,2,rep,name=commands,proto3" json:"commands,omitempty"`
}

func (x *PurgeSetActiveProfileCommandsRequest) Reset() {
	*x = PurgeSetActiveProfileCommandsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurgeSetActiveProfileCommandsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurgeSetActiveProfileCommandsRequest) ProtoMessage() {}

func (x *PurgeSetActiveProfileCommandsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_profile_sync_portal_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurgeSetActiveProfileCommandsRequest.ProtoReflect.Descriptor instead.
func (*PurgeSetActiveProfileCommandsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_profile_sync_portal_proto_rawDescGZIP(), []int{10}
}

func (x *PurgeSetActiveProfileCommandsRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

func (x *PurgeSetActiveProfileCommandsRequest) GetCommands() []*SetActiveProfileCommand {
	if x != nil {
		return x.Commands
	}
	return nil
}

var File_portal_proto_profile_sync_portal_proto protoreflect.FileDescriptor

var file_portal_proto_profile_sync_portal_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x1a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2f,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x74, 0x68,
	0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe4, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5d, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x1a,
	0x6a, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63,
	0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xab, 0x04, 0x0a, 0x14,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x07, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x12, 0x52, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72,
	0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x4c, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3f, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07,
	0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x08,
	0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x54, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x48, 0x00, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x42, 0x09,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x27, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x22, 0xdb, 0x03, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x12, 0x52, 0x0a, 0x0d, 0x64,
	0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x72,
	0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x4c, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3f, 0x0a,
	0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3f,
	0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12,
	0x54, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56,
	0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x56, 0x65, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x22, 0x2a, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x13,
	0x50, 0x75, 0x72, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x7b, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x22, 0x3a, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x22, 0x76, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x24, 0x50, 0x75, 0x72,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x4f, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x32, 0xba, 0x06, 0x0a, 0x18, 0x50, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5c, 0x0a, 0x0d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x6b, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2d, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0d,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x30, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5a, 0x0a, 0x0c, 0x50, 0x75,
	0x72, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50, 0x75, 0x72, 0x67, 0x65, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x9e, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x3e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x1d, 0x50, 0x75, 0x72, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50, 0x75, 0x72, 0x67, 0x65, 0x53, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_profile_sync_portal_proto_rawDescOnce sync.Once
	file_portal_proto_profile_sync_portal_proto_rawDescData = file_portal_proto_profile_sync_portal_proto_rawDesc
)

func file_portal_proto_profile_sync_portal_proto_rawDescGZIP() []byte {
	file_portal_proto_profile_sync_portal_proto_rawDescOnce.Do(func() {
		file_portal_proto_profile_sync_portal_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_profile_sync_portal_proto_rawDescData)
	})
	return file_portal_proto_profile_sync_portal_proto_rawDescData
}

var file_portal_proto_profile_sync_portal_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_portal_proto_profile_sync_portal_proto_goTypes = []interface{}{
	(*GetProfilesDataRequest)(nil),               // 0: carbon.portal.profile_sync.GetProfilesDataRequest
	(*GetProfilesDataResponse)(nil),              // 1: carbon.portal.profile_sync.GetProfilesDataResponse
	(*UploadProfileRequest)(nil),                 // 2: carbon.portal.profile_sync.UploadProfileRequest
	(*GetProfileRequest)(nil),                    // 3: carbon.portal.profile_sync.GetProfileRequest
	(*GetProfileResponse)(nil),                   // 4: carbon.portal.profile_sync.GetProfileResponse
	(*DeleteProfileRequest)(nil),                 // 5: carbon.portal.profile_sync.DeleteProfileRequest
	(*PurgeProfileRequest)(nil),                  // 6: carbon.portal.profile_sync.PurgeProfileRequest
	(*SetActiveProfileCommand)(nil),              // 7: carbon.portal.profile_sync.SetActiveProfileCommand
	(*GetSetActiveProfileCommandsRequest)(nil),   // 8: carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest
	(*GetSetActiveProfileCommandsResponse)(nil),  // 9: carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse
	(*PurgeSetActiveProfileCommandsRequest)(nil), // 10: carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest
	nil,                                          // 11: carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry
	(*almanac.AlmanacConfig)(nil),                // 12: carbon.aimbot.almanac.AlmanacConfig
	(*almanac.DiscriminatorConfig)(nil),          // 13: carbon.aimbot.almanac.DiscriminatorConfig
	(*almanac.ModelinatorConfig)(nil),            // 14: carbon.aimbot.almanac.ModelinatorConfig
	(*frontend.BandingDef)(nil),                  // 15: carbon.frontend.banding.BandingDef
	(*thinning.ConfigDefinition)(nil),            // 16: carbon.thinning.ConfigDefinition
	(*target_velocity_estimator.TVEProfile)(nil), // 17: carbon.aimbot.target_velocity_estimator.TVEProfile
	(frontend.ProfileType)(0),                    // 18: carbon.frontend.profile_sync.ProfileType
	(*frontend.ProfileSyncData)(nil),             // 19: carbon.frontend.profile_sync.ProfileSyncData
	(*Empty)(nil),                                // 20: carbon.portal.util.Empty
}
var file_portal_proto_profile_sync_portal_proto_depIdxs = []int32{
	11, // 0: carbon.portal.profile_sync.GetProfilesDataResponse.profiles:type_name -> carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry
	12, // 1: carbon.portal.profile_sync.UploadProfileRequest.almanac:type_name -> carbon.aimbot.almanac.AlmanacConfig
	13, // 2: carbon.portal.profile_sync.UploadProfileRequest.discriminator:type_name -> carbon.aimbot.almanac.DiscriminatorConfig
	14, // 3: carbon.portal.profile_sync.UploadProfileRequest.modelinator:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	15, // 4: carbon.portal.profile_sync.UploadProfileRequest.banding:type_name -> carbon.frontend.banding.BandingDef
	16, // 5: carbon.portal.profile_sync.UploadProfileRequest.thinning:type_name -> carbon.thinning.ConfigDefinition
	17, // 6: carbon.portal.profile_sync.UploadProfileRequest.target_vel:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	12, // 7: carbon.portal.profile_sync.GetProfileResponse.almanac:type_name -> carbon.aimbot.almanac.AlmanacConfig
	13, // 8: carbon.portal.profile_sync.GetProfileResponse.discriminator:type_name -> carbon.aimbot.almanac.DiscriminatorConfig
	14, // 9: carbon.portal.profile_sync.GetProfileResponse.modelinator:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	15, // 10: carbon.portal.profile_sync.GetProfileResponse.banding:type_name -> carbon.frontend.banding.BandingDef
	16, // 11: carbon.portal.profile_sync.GetProfileResponse.thinning:type_name -> carbon.thinning.ConfigDefinition
	17, // 12: carbon.portal.profile_sync.GetProfileResponse.target_vel:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	18, // 13: carbon.portal.profile_sync.SetActiveProfileCommand.profile_type:type_name -> carbon.frontend.profile_sync.ProfileType
	7,  // 14: carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands:type_name -> carbon.portal.profile_sync.SetActiveProfileCommand
	7,  // 15: carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands:type_name -> carbon.portal.profile_sync.SetActiveProfileCommand
	19, // 16: carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry.value:type_name -> carbon.frontend.profile_sync.ProfileSyncData
	0,  // 17: carbon.portal.profile_sync.PortalProfileSyncService.GetProfilesData:input_type -> carbon.portal.profile_sync.GetProfilesDataRequest
	2,  // 18: carbon.portal.profile_sync.PortalProfileSyncService.UploadProfile:input_type -> carbon.portal.profile_sync.UploadProfileRequest
	3,  // 19: carbon.portal.profile_sync.PortalProfileSyncService.GetProfile:input_type -> carbon.portal.profile_sync.GetProfileRequest
	5,  // 20: carbon.portal.profile_sync.PortalProfileSyncService.DeleteProfile:input_type -> carbon.portal.profile_sync.DeleteProfileRequest
	6,  // 21: carbon.portal.profile_sync.PortalProfileSyncService.PurgeProfile:input_type -> carbon.portal.profile_sync.PurgeProfileRequest
	8,  // 22: carbon.portal.profile_sync.PortalProfileSyncService.GetSetActiveProfileCommands:input_type -> carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest
	10, // 23: carbon.portal.profile_sync.PortalProfileSyncService.PurgeSetActiveProfileCommands:input_type -> carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest
	1,  // 24: carbon.portal.profile_sync.PortalProfileSyncService.GetProfilesData:output_type -> carbon.portal.profile_sync.GetProfilesDataResponse
	20, // 25: carbon.portal.profile_sync.PortalProfileSyncService.UploadProfile:output_type -> carbon.portal.util.Empty
	4,  // 26: carbon.portal.profile_sync.PortalProfileSyncService.GetProfile:output_type -> carbon.portal.profile_sync.GetProfileResponse
	20, // 27: carbon.portal.profile_sync.PortalProfileSyncService.DeleteProfile:output_type -> carbon.portal.util.Empty
	20, // 28: carbon.portal.profile_sync.PortalProfileSyncService.PurgeProfile:output_type -> carbon.portal.util.Empty
	9,  // 29: carbon.portal.profile_sync.PortalProfileSyncService.GetSetActiveProfileCommands:output_type -> carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse
	20, // 30: carbon.portal.profile_sync.PortalProfileSyncService.PurgeSetActiveProfileCommands:output_type -> carbon.portal.util.Empty
	24, // [24:31] is the sub-list for method output_type
	17, // [17:24] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_portal_proto_profile_sync_portal_proto_init() }
func file_portal_proto_profile_sync_portal_proto_init() {
	if File_portal_proto_profile_sync_portal_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_profile_sync_portal_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfilesDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfilesDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurgeProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActiveProfileCommand); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSetActiveProfileCommandsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSetActiveProfileCommandsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_profile_sync_portal_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurgeSetActiveProfileCommandsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_portal_proto_profile_sync_portal_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*UploadProfileRequest_Almanac)(nil),
		(*UploadProfileRequest_Discriminator)(nil),
		(*UploadProfileRequest_Modelinator)(nil),
		(*UploadProfileRequest_Banding)(nil),
		(*UploadProfileRequest_Thinning)(nil),
		(*UploadProfileRequest_TargetVel)(nil),
	}
	file_portal_proto_profile_sync_portal_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetProfileResponse_Almanac)(nil),
		(*GetProfileResponse_Discriminator)(nil),
		(*GetProfileResponse_Modelinator)(nil),
		(*GetProfileResponse_Banding)(nil),
		(*GetProfileResponse_Thinning)(nil),
		(*GetProfileResponse_TargetVel)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_profile_sync_portal_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_profile_sync_portal_proto_goTypes,
		DependencyIndexes: file_portal_proto_profile_sync_portal_proto_depIdxs,
		MessageInfos:      file_portal_proto_profile_sync_portal_proto_msgTypes,
	}.Build()
	File_portal_proto_profile_sync_portal_proto = out.File
	file_portal_proto_profile_sync_portal_proto_rawDesc = nil
	file_portal_proto_profile_sync_portal_proto_goTypes = nil
	file_portal_proto_profile_sync_portal_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/spatial_metrics_sync.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	metrics "github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncSpatialMetricBlocksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Blocks []*metrics.SpatialMetricBlock `protobuf:"bytes,1,rep,name=blocks,proto3" json:"blocks,omitempty"`
	Robot  string                        `protobuf:"bytes,2,opt,name=robot,proto3" json:"robot,omitempty"`
}

func (x *SyncSpatialMetricBlocksRequest) Reset() {
	*x = SyncSpatialMetricBlocksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_spatial_metrics_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSpatialMetricBlocksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSpatialMetricBlocksRequest) ProtoMessage() {}

func (x *SyncSpatialMetricBlocksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_spatial_metrics_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSpatialMetricBlocksRequest.ProtoReflect.Descriptor instead.
func (*SyncSpatialMetricBlocksRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_spatial_metrics_sync_proto_rawDescGZIP(), []int{0}
}

func (x *SyncSpatialMetricBlocksRequest) GetBlocks() []*metrics.SpatialMetricBlock {
	if x != nil {
		return x.Blocks
	}
	return nil
}

func (x *SyncSpatialMetricBlocksRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

var File_portal_proto_spatial_metrics_sync_proto protoreflect.FileDescriptor

var file_portal_proto_spatial_metrics_sync_proto_rawDesc = []byte{
	0x0a, 0x27, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73,
	0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x73, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c,
	0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72,
	0x0a, 0x1e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x32, 0x90, 0x01, 0x0a, 0x19, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x73, 0x0a, 0x17, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x3d, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x73, 0x70, 0x61, 0x74,
	0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x79, 0x6e, 0x63,
	0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_spatial_metrics_sync_proto_rawDescOnce sync.Once
	file_portal_proto_spatial_metrics_sync_proto_rawDescData = file_portal_proto_spatial_metrics_sync_proto_rawDesc
)

func file_portal_proto_spatial_metrics_sync_proto_rawDescGZIP() []byte {
	file_portal_proto_spatial_metrics_sync_proto_rawDescOnce.Do(func() {
		file_portal_proto_spatial_metrics_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_spatial_metrics_sync_proto_rawDescData)
	})
	return file_portal_proto_spatial_metrics_sync_proto_rawDescData
}

var file_portal_proto_spatial_metrics_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_portal_proto_spatial_metrics_sync_proto_goTypes = []interface{}{
	(*SyncSpatialMetricBlocksRequest)(nil), // 0: carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest
	(*metrics.SpatialMetricBlock)(nil),     // 1: carbon.metrics.SpatialMetricBlock
	(*Empty)(nil),                          // 2: carbon.portal.util.Empty
}
var file_portal_proto_spatial_metrics_sync_proto_depIdxs = []int32{
	1, // 0: carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks:type_name -> carbon.metrics.SpatialMetricBlock
	0, // 1: carbon.portal.spatial_metrics.SpatialMetricsSyncService.SyncSpatialMetricBlocks:input_type -> carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest
	2, // 2: carbon.portal.spatial_metrics.SpatialMetricsSyncService.SyncSpatialMetricBlocks:output_type -> carbon.portal.util.Empty
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_portal_proto_spatial_metrics_sync_proto_init() }
func file_portal_proto_spatial_metrics_sync_proto_init() {
	if File_portal_proto_spatial_metrics_sync_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_spatial_metrics_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSpatialMetricBlocksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_spatial_metrics_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_spatial_metrics_sync_proto_goTypes,
		DependencyIndexes: file_portal_proto_spatial_metrics_sync_proto_depIdxs,
		MessageInfos:      file_portal_proto_spatial_metrics_sync_proto_msgTypes,
	}.Build()
	File_portal_proto_spatial_metrics_sync_proto = out.File
	file_portal_proto_spatial_metrics_sync_proto_rawDesc = nil
	file_portal_proto_spatial_metrics_sync_proto_goTypes = nil
	file_portal_proto_spatial_metrics_sync_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/model_info_sync.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelInfoSyncService_UploadModelInfos_FullMethodName         = "/carbon.portal.model_info.ModelInfoSyncService/UploadModelInfos"
	ModelInfoSyncService_GetRenameModelCommands_FullMethodName   = "/carbon.portal.model_info.ModelInfoSyncService/GetRenameModelCommands"
	ModelInfoSyncService_PurgeRenameModelCommands_FullMethodName = "/carbon.portal.model_info.ModelInfoSyncService/PurgeRenameModelCommands"
)

// ModelInfoSyncServiceClient is the client API for ModelInfoSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelInfoSyncServiceClient interface {
	UploadModelInfos(ctx context.Context, in *UploadModelInfosRequest, opts ...grpc.CallOption) (*Empty, error)
	GetRenameModelCommands(ctx context.Context, in *GetRenameModelCommandsRequest, opts ...grpc.CallOption) (*GetRenameModelCommandsResponse, error)
	PurgeRenameModelCommands(ctx context.Context, in *PurgeRenameModelCommandsRequest, opts ...grpc.CallOption) (*Empty, error)
}

type modelInfoSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModelInfoSyncServiceClient(cc grpc.ClientConnInterface) ModelInfoSyncServiceClient {
	return &modelInfoSyncServiceClient{cc}
}

func (c *modelInfoSyncServiceClient) UploadModelInfos(ctx context.Context, in *UploadModelInfosRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelInfoSyncService_UploadModelInfos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelInfoSyncServiceClient) GetRenameModelCommands(ctx context.Context, in *GetRenameModelCommandsRequest, opts ...grpc.CallOption) (*GetRenameModelCommandsResponse, error) {
	out := new(GetRenameModelCommandsResponse)
	err := c.cc.Invoke(ctx, ModelInfoSyncService_GetRenameModelCommands_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelInfoSyncServiceClient) PurgeRenameModelCommands(ctx context.Context, in *PurgeRenameModelCommandsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelInfoSyncService_PurgeRenameModelCommands_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelInfoSyncServiceServer is the server API for ModelInfoSyncService service.
// All implementations must embed UnimplementedModelInfoSyncServiceServer
// for forward compatibility
type ModelInfoSyncServiceServer interface {
	UploadModelInfos(context.Context, *UploadModelInfosRequest) (*Empty, error)
	GetRenameModelCommands(context.Context, *GetRenameModelCommandsRequest) (*GetRenameModelCommandsResponse, error)
	PurgeRenameModelCommands(context.Context, *PurgeRenameModelCommandsRequest) (*Empty, error)
	mustEmbedUnimplementedModelInfoSyncServiceServer()
}

// UnimplementedModelInfoSyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModelInfoSyncServiceServer struct {
}

func (UnimplementedModelInfoSyncServiceServer) UploadModelInfos(context.Context, *UploadModelInfosRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadModelInfos not implemented")
}
func (UnimplementedModelInfoSyncServiceServer) GetRenameModelCommands(context.Context, *GetRenameModelCommandsRequest) (*GetRenameModelCommandsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRenameModelCommands not implemented")
}
func (UnimplementedModelInfoSyncServiceServer) PurgeRenameModelCommands(context.Context, *PurgeRenameModelCommandsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurgeRenameModelCommands not implemented")
}
func (UnimplementedModelInfoSyncServiceServer) mustEmbedUnimplementedModelInfoSyncServiceServer() {}

// UnsafeModelInfoSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelInfoSyncServiceServer will
// result in compilation errors.
type UnsafeModelInfoSyncServiceServer interface {
	mustEmbedUnimplementedModelInfoSyncServiceServer()
}

func RegisterModelInfoSyncServiceServer(s grpc.ServiceRegistrar, srv ModelInfoSyncServiceServer) {
	s.RegisterService(&ModelInfoSyncService_ServiceDesc, srv)
}

func _ModelInfoSyncService_UploadModelInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadModelInfosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelInfoSyncServiceServer).UploadModelInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelInfoSyncService_UploadModelInfos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelInfoSyncServiceServer).UploadModelInfos(ctx, req.(*UploadModelInfosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelInfoSyncService_GetRenameModelCommands_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRenameModelCommandsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelInfoSyncServiceServer).GetRenameModelCommands(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelInfoSyncService_GetRenameModelCommands_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelInfoSyncServiceServer).GetRenameModelCommands(ctx, req.(*GetRenameModelCommandsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelInfoSyncService_PurgeRenameModelCommands_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurgeRenameModelCommandsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelInfoSyncServiceServer).PurgeRenameModelCommands(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelInfoSyncService_PurgeRenameModelCommands_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelInfoSyncServiceServer).PurgeRenameModelCommands(ctx, req.(*PurgeRenameModelCommandsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelInfoSyncService_ServiceDesc is the grpc.ServiceDesc for ModelInfoSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelInfoSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.model_info.ModelInfoSyncService",
	HandlerType: (*ModelInfoSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadModelInfos",
			Handler:    _ModelInfoSyncService_UploadModelInfos_Handler,
		},
		{
			MethodName: "GetRenameModelCommands",
			Handler:    _ModelInfoSyncService_GetRenameModelCommands_Handler,
		},
		{
			MethodName: "PurgeRenameModelCommands",
			Handler:    _ModelInfoSyncService_PurgeRenameModelCommands_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/model_info_sync.proto",
}

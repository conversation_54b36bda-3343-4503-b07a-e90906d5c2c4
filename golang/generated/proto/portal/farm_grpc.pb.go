// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/farm.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FarmsService_GetFarm_FullMethodName = "/carbon.portal.farm.FarmsService/GetFarm"
)

// FarmsServiceClient is the client API for FarmsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FarmsServiceClient interface {
	// Gets a farm by ID. If `if_modified_since` is present and none of the
	// farm's descendant entities has been modified at a strictly later time than
	// that, then the RPC will succeed with code OK and no `farm` on the
	// response.
	GetFarm(ctx context.Context, in *GetFarmRequest, opts ...grpc.CallOption) (*GetFarmResponse, error)
}

type farmsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFarmsServiceClient(cc grpc.ClientConnInterface) FarmsServiceClient {
	return &farmsServiceClient{cc}
}

func (c *farmsServiceClient) GetFarm(ctx context.Context, in *GetFarmRequest, opts ...grpc.CallOption) (*GetFarmResponse, error) {
	out := new(GetFarmResponse)
	err := c.cc.Invoke(ctx, FarmsService_GetFarm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FarmsServiceServer is the server API for FarmsService service.
// All implementations must embed UnimplementedFarmsServiceServer
// for forward compatibility
type FarmsServiceServer interface {
	// Gets a farm by ID. If `if_modified_since` is present and none of the
	// farm's descendant entities has been modified at a strictly later time than
	// that, then the RPC will succeed with code OK and no `farm` on the
	// response.
	GetFarm(context.Context, *GetFarmRequest) (*GetFarmResponse, error)
	mustEmbedUnimplementedFarmsServiceServer()
}

// UnimplementedFarmsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFarmsServiceServer struct {
}

func (UnimplementedFarmsServiceServer) GetFarm(context.Context, *GetFarmRequest) (*GetFarmResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFarm not implemented")
}
func (UnimplementedFarmsServiceServer) mustEmbedUnimplementedFarmsServiceServer() {}

// UnsafeFarmsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FarmsServiceServer will
// result in compilation errors.
type UnsafeFarmsServiceServer interface {
	mustEmbedUnimplementedFarmsServiceServer()
}

func RegisterFarmsServiceServer(s grpc.ServiceRegistrar, srv FarmsServiceServer) {
	s.RegisterService(&FarmsService_ServiceDesc, srv)
}

func _FarmsService_GetFarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFarmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FarmsServiceServer).GetFarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FarmsService_GetFarm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FarmsServiceServer).GetFarm(ctx, req.(*GetFarmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FarmsService_ServiceDesc is the grpc.ServiceDesc for FarmsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FarmsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.farm.FarmsService",
	HandlerType: (*FarmsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFarm",
			Handler:    _FarmsService_GetFarm_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/farm.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/health.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	metrics "github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ########### START DEPRECATED ALARMS
type AlarmLevel int32

const (
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmLevel_ALARM_UNKNOWN AlarmLevel = 0
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmLevel_ALARM_CRITICAL AlarmLevel = 1
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmLevel_ALARM_HIGH AlarmLevel = 2
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmLevel_ALARM_MEDIUM AlarmLevel = 3
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmLevel_ALARM_LOW AlarmLevel = 4
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmLevel_ALARM_HIDDEN AlarmLevel = 5
)

// Enum value maps for AlarmLevel.
var (
	AlarmLevel_name = map[int32]string{
		0: "ALARM_UNKNOWN",
		1: "ALARM_CRITICAL",
		2: "ALARM_HIGH",
		3: "ALARM_MEDIUM",
		4: "ALARM_LOW",
		5: "ALARM_HIDDEN",
	}
	AlarmLevel_value = map[string]int32{
		"ALARM_UNKNOWN":  0,
		"ALARM_CRITICAL": 1,
		"ALARM_HIGH":     2,
		"ALARM_MEDIUM":   3,
		"ALARM_LOW":      4,
		"ALARM_HIDDEN":   5,
	}
)

func (x AlarmLevel) Enum() *AlarmLevel {
	p := new(AlarmLevel)
	*p = x
	return p
}

func (x AlarmLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_proto_health_proto_enumTypes[0].Descriptor()
}

func (AlarmLevel) Type() protoreflect.EnumType {
	return &file_portal_proto_health_proto_enumTypes[0]
}

func (x AlarmLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmLevel.Descriptor instead.
func (AlarmLevel) EnumDescriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{0}
}

type AlarmImpact int32

const (
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmImpact_IMPACT_UNDEFINED AlarmImpact = 0
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmImpact_IMPACT_CRITICAL AlarmImpact = 1
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmImpact_IMPACT_OFFLINE AlarmImpact = 2
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmImpact_IMPACT_DEGRADED AlarmImpact = 3
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	AlarmImpact_IMPACT_NONE AlarmImpact = 4
)

// Enum value maps for AlarmImpact.
var (
	AlarmImpact_name = map[int32]string{
		0: "IMPACT_UNDEFINED",
		1: "IMPACT_CRITICAL",
		2: "IMPACT_OFFLINE",
		3: "IMPACT_DEGRADED",
		4: "IMPACT_NONE",
	}
	AlarmImpact_value = map[string]int32{
		"IMPACT_UNDEFINED": 0,
		"IMPACT_CRITICAL":  1,
		"IMPACT_OFFLINE":   2,
		"IMPACT_DEGRADED":  3,
		"IMPACT_NONE":      4,
	}
)

func (x AlarmImpact) Enum() *AlarmImpact {
	p := new(AlarmImpact)
	*p = x
	return p
}

func (x AlarmImpact) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmImpact) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_proto_health_proto_enumTypes[1].Descriptor()
}

func (AlarmImpact) Type() protoreflect.EnumType {
	return &file_portal_proto_health_proto_enumTypes[1]
}

func (x AlarmImpact) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmImpact.Descriptor instead.
func (AlarmImpact) EnumDescriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in portal/proto/health.proto.
type AlarmRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs  int64       `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	AlarmCode    string      `protobuf:"bytes,2,opt,name=alarm_code,json=alarmCode,proto3" json:"alarm_code,omitempty"`
	Description  string      `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Level        AlarmLevel  `protobuf:"varint,4,opt,name=level,proto3,enum=carbon.portal.health.AlarmLevel" json:"level,omitempty"`
	Identifier   string      `protobuf:"bytes,5,opt,name=identifier,proto3" json:"identifier,omitempty"`
	Acknowledged bool        `protobuf:"varint,6,opt,name=acknowledged,proto3" json:"acknowledged,omitempty"`
	Impact       AlarmImpact `protobuf:"varint,7,opt,name=impact,proto3,enum=carbon.portal.health.AlarmImpact" json:"impact,omitempty"`
}

func (x *AlarmRow) Reset() {
	*x = AlarmRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmRow) ProtoMessage() {}

func (x *AlarmRow) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmRow.ProtoReflect.Descriptor instead.
func (*AlarmRow) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{0}
}

func (x *AlarmRow) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *AlarmRow) GetAlarmCode() string {
	if x != nil {
		return x.AlarmCode
	}
	return ""
}

func (x *AlarmRow) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AlarmRow) GetLevel() AlarmLevel {
	if x != nil {
		return x.Level
	}
	return AlarmLevel_ALARM_UNKNOWN
}

func (x *AlarmRow) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *AlarmRow) GetAcknowledged() bool {
	if x != nil {
		return x.Acknowledged
	}
	return false
}

func (x *AlarmRow) GetImpact() AlarmImpact {
	if x != nil {
		return x.Impact
	}
	return AlarmImpact_IMPACT_UNDEFINED
}

type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float64 `protobuf:"fixed64,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float64 `protobuf:"fixed64,2,opt,name=y,proto3" json:"y,omitempty"`
	Z float64 `protobuf:"fixed64,3,opt,name=z,proto3" json:"z,omitempty"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{1}
}

func (x *Location) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Location) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *Location) GetZ() float64 {
	if x != nil {
		return x.Z
	}
	return 0
}

type FieldConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BandingEnabled              bool   `protobuf:"varint,1,opt,name=banding_enabled,json=bandingEnabled,proto3" json:"banding_enabled,omitempty"`
	BandingDynamic              bool   `protobuf:"varint,2,opt,name=banding_dynamic,json=bandingDynamic,proto3" json:"banding_dynamic,omitempty"`
	ActiveBandConfig            string `protobuf:"bytes,3,opt,name=active_band_config,json=activeBandConfig,proto3" json:"active_band_config,omitempty"`
	ActiveThinningConfigId      string `protobuf:"bytes,4,opt,name=active_thinning_config_id,json=activeThinningConfigId,proto3" json:"active_thinning_config_id,omitempty"`
	ActiveJobId                 string `protobuf:"bytes,5,opt,name=active_job_id,json=activeJobId,proto3" json:"active_job_id,omitempty"`
	ActiveAlmanacId             string `protobuf:"bytes,6,opt,name=active_almanac_id,json=activeAlmanacId,proto3" json:"active_almanac_id,omitempty"`
	ActiveDiscriminatorId       string `protobuf:"bytes,7,opt,name=active_discriminator_id,json=activeDiscriminatorId,proto3" json:"active_discriminator_id,omitempty"`
	IsWeeding                   bool   `protobuf:"varint,8,opt,name=is_weeding,json=isWeeding,proto3" json:"is_weeding,omitempty"`
	IsThinning                  bool   `protobuf:"varint,9,opt,name=is_thinning,json=isThinning,proto3" json:"is_thinning,omitempty"`
	ActiveBandConfigName        string `protobuf:"bytes,10,opt,name=active_band_config_name,json=activeBandConfigName,proto3" json:"active_band_config_name,omitempty"`
	ActiveThinningConfigName    string `protobuf:"bytes,11,opt,name=active_thinning_config_name,json=activeThinningConfigName,proto3" json:"active_thinning_config_name,omitempty"`
	ActiveJobName               string `protobuf:"bytes,12,opt,name=active_job_name,json=activeJobName,proto3" json:"active_job_name,omitempty"`
	ActiveAlmanacName           string `protobuf:"bytes,13,opt,name=active_almanac_name,json=activeAlmanacName,proto3" json:"active_almanac_name,omitempty"`
	ActiveDiscriminatorName     string `protobuf:"bytes,14,opt,name=active_discriminator_name,json=activeDiscriminatorName,proto3" json:"active_discriminator_name,omitempty"`
	ActiveModelinatorId         string `protobuf:"bytes,15,opt,name=active_modelinator_id,json=activeModelinatorId,proto3" json:"active_modelinator_id,omitempty"`
	ActiveVelocityEstimatorId   string `protobuf:"bytes,16,opt,name=active_velocity_estimator_id,json=activeVelocityEstimatorId,proto3" json:"active_velocity_estimator_id,omitempty"`
	ActiveVelocityEstimatorName string `protobuf:"bytes,17,opt,name=active_velocity_estimator_name,json=activeVelocityEstimatorName,proto3" json:"active_velocity_estimator_name,omitempty"`
	ActiveCategoryCollectionId  string `protobuf:"bytes,18,opt,name=active_category_collection_id,json=activeCategoryCollectionId,proto3" json:"active_category_collection_id,omitempty"`
}

func (x *FieldConfig) Reset() {
	*x = FieldConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldConfig) ProtoMessage() {}

func (x *FieldConfig) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldConfig.ProtoReflect.Descriptor instead.
func (*FieldConfig) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{2}
}

func (x *FieldConfig) GetBandingEnabled() bool {
	if x != nil {
		return x.BandingEnabled
	}
	return false
}

func (x *FieldConfig) GetBandingDynamic() bool {
	if x != nil {
		return x.BandingDynamic
	}
	return false
}

func (x *FieldConfig) GetActiveBandConfig() string {
	if x != nil {
		return x.ActiveBandConfig
	}
	return ""
}

func (x *FieldConfig) GetActiveThinningConfigId() string {
	if x != nil {
		return x.ActiveThinningConfigId
	}
	return ""
}

func (x *FieldConfig) GetActiveJobId() string {
	if x != nil {
		return x.ActiveJobId
	}
	return ""
}

func (x *FieldConfig) GetActiveAlmanacId() string {
	if x != nil {
		return x.ActiveAlmanacId
	}
	return ""
}

func (x *FieldConfig) GetActiveDiscriminatorId() string {
	if x != nil {
		return x.ActiveDiscriminatorId
	}
	return ""
}

func (x *FieldConfig) GetIsWeeding() bool {
	if x != nil {
		return x.IsWeeding
	}
	return false
}

func (x *FieldConfig) GetIsThinning() bool {
	if x != nil {
		return x.IsThinning
	}
	return false
}

func (x *FieldConfig) GetActiveBandConfigName() string {
	if x != nil {
		return x.ActiveBandConfigName
	}
	return ""
}

func (x *FieldConfig) GetActiveThinningConfigName() string {
	if x != nil {
		return x.ActiveThinningConfigName
	}
	return ""
}

func (x *FieldConfig) GetActiveJobName() string {
	if x != nil {
		return x.ActiveJobName
	}
	return ""
}

func (x *FieldConfig) GetActiveAlmanacName() string {
	if x != nil {
		return x.ActiveAlmanacName
	}
	return ""
}

func (x *FieldConfig) GetActiveDiscriminatorName() string {
	if x != nil {
		return x.ActiveDiscriminatorName
	}
	return ""
}

func (x *FieldConfig) GetActiveModelinatorId() string {
	if x != nil {
		return x.ActiveModelinatorId
	}
	return ""
}

func (x *FieldConfig) GetActiveVelocityEstimatorId() string {
	if x != nil {
		return x.ActiveVelocityEstimatorId
	}
	return ""
}

func (x *FieldConfig) GetActiveVelocityEstimatorName() string {
	if x != nil {
		return x.ActiveVelocityEstimatorName
	}
	return ""
}

func (x *FieldConfig) GetActiveCategoryCollectionId() string {
	if x != nil {
		return x.ActiveCategoryCollectionId
	}
	return ""
}

type Versions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current string `protobuf:"bytes,1,opt,name=current,proto3" json:"current,omitempty"`
	Latest  string `protobuf:"bytes,2,opt,name=latest,proto3" json:"latest,omitempty"`
}

func (x *Versions) Reset() {
	*x = Versions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Versions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Versions) ProtoMessage() {}

func (x *Versions) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Versions.ProtoReflect.Descriptor instead.
func (*Versions) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{3}
}

func (x *Versions) GetCurrent() string {
	if x != nil {
		return x.Current
	}
	return ""
}

func (x *Versions) GetLatest() string {
	if x != nil {
		return x.Latest
	}
	return ""
}

type WeedingPerformance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AreaWeededTotal float64 `protobuf:"fixed64,1,opt,name=area_weeded_total,json=areaWeededTotal,proto3" json:"area_weeded_total,omitempty"`
	AreaWeededToday float64 `protobuf:"fixed64,2,opt,name=area_weeded_today,json=areaWeededToday,proto3" json:"area_weeded_today,omitempty"`
	TimeWeededToday int64   `protobuf:"varint,3,opt,name=time_weeded_today,json=timeWeededToday,proto3" json:"time_weeded_today,omitempty"`
}

func (x *WeedingPerformance) Reset() {
	*x = WeedingPerformance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedingPerformance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedingPerformance) ProtoMessage() {}

func (x *WeedingPerformance) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedingPerformance.ProtoReflect.Descriptor instead.
func (*WeedingPerformance) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{4}
}

func (x *WeedingPerformance) GetAreaWeededTotal() float64 {
	if x != nil {
		return x.AreaWeededTotal
	}
	return 0
}

func (x *WeedingPerformance) GetAreaWeededToday() float64 {
	if x != nil {
		return x.AreaWeededToday
	}
	return 0
}

func (x *WeedingPerformance) GetTimeWeededToday() int64 {
	if x != nil {
		return x.TimeWeededToday
	}
	return 0
}

type Performance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Weeding *WeedingPerformance `protobuf:"bytes,1,opt,name=weeding,proto3" json:"weeding,omitempty"`
}

func (x *Performance) Reset() {
	*x = Performance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Performance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Performance) ProtoMessage() {}

func (x *Performance) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Performance.ProtoReflect.Descriptor instead.
func (*Performance) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{5}
}

func (x *Performance) GetWeeding() *WeedingPerformance {
	if x != nil {
		return x.Weeding
	}
	return nil
}

type DailyMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metrics map[string]string `protobuf:"bytes,1,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DailyMetrics) Reset() {
	*x = DailyMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyMetrics) ProtoMessage() {}

func (x *DailyMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyMetrics.ProtoReflect.Descriptor instead.
func (*DailyMetrics) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{6}
}

func (x *DailyMetrics) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type Metrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DailyMetrics map[string]*DailyMetrics `protobuf:"bytes,1,rep,name=daily_metrics,json=dailyMetrics,proto3" json:"daily_metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Metrics) Reset() {
	*x = Metrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metrics) ProtoMessage() {}

func (x *Metrics) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metrics.ProtoReflect.Descriptor instead.
func (*Metrics) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{7}
}

func (x *Metrics) GetDailyMetrics() map[string]*DailyMetrics {
	if x != nil {
		return x.DailyMetrics
	}
	return nil
}

type HealthLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	Alarms          []*AlarmRow        `protobuf:"bytes,1,rep,name=alarms,proto3" json:"alarms,omitempty"`
	Location        *Location          `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	Model           string             `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	Models          []string           `protobuf:"bytes,4,rep,name=models,proto3" json:"models,omitempty"`
	Performance     *Performance       `protobuf:"bytes,5,opt,name=performance,proto3" json:"performance,omitempty"`
	ReportedAt      int64              `protobuf:"varint,6,opt,name=reported_at,json=reportedAt,proto3" json:"reported_at,omitempty"`
	RobotSerial     string             `protobuf:"bytes,7,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	Systems         []*structpb.Struct `protobuf:"bytes,8,rep,name=systems,proto3" json:"systems,omitempty"`
	Status          frontend.Status    `protobuf:"varint,9,opt,name=status,proto3,enum=carbon.frontend.status_bar.Status" json:"status,omitempty"`
	StatusChangedAt int64              `protobuf:"varint,10,opt,name=status_changed_at,json=statusChangedAt,proto3" json:"status_changed_at,omitempty"`
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	Crop                    string                            `protobuf:"bytes,11,opt,name=crop,proto3" json:"crop,omitempty"` // DEPRECATED: use crop_id
	P2P                     string                            `protobuf:"bytes,12,opt,name=p2p,proto3" json:"p2p,omitempty"`
	SoftwareVersion         string                            `protobuf:"bytes,13,opt,name=software_version,json=softwareVersion,proto3" json:"software_version,omitempty"`
	TargetVersion           string                            `protobuf:"bytes,14,opt,name=target_version,json=targetVersion,proto3" json:"target_version,omitempty"`
	TargetVersionReady      bool                              `protobuf:"varint,15,opt,name=target_version_ready,json=targetVersionReady,proto3" json:"target_version_ready,omitempty"`
	StatusMessage           string                            `protobuf:"bytes,16,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	MetricTotals            map[string]uint64                 `protobuf:"bytes,17,rep,name=metric_totals,json=metricTotals,proto3" json:"metric_totals,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	AlarmList               []*frontend.AlarmRow              `protobuf:"bytes,18,rep,name=alarm_list,json=alarmList,proto3" json:"alarm_list,omitempty"`
	FieldConfig             *FieldConfig                      `protobuf:"bytes,19,opt,name=field_config,json=fieldConfig,proto3" json:"field_config,omitempty"`
	Metrics                 *Metrics                          `protobuf:"bytes,20,opt,name=metrics,proto3" json:"metrics,omitempty"`
	CropId                  string                            `protobuf:"bytes,21,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	RobotRuntime_240V       uint32                            `protobuf:"varint,22,opt,name=robot_runtime_240v,json=robotRuntime240v,proto3" json:"robot_runtime_240v,omitempty"`
	LaserState              *frontend.LaserStateList          `protobuf:"bytes,23,opt,name=laser_state,json=laserState,proto3" json:"laser_state,omitempty"`
	LaserChangeTimes        *metrics.LaserChangeTimes         `protobuf:"bytes,24,opt,name=laser_change_times,json=laserChangeTimes,proto3" json:"laser_change_times,omitempty"`
	HostSerials             map[string]string                 `protobuf:"bytes,25,rep,name=host_serials,json=hostSerials,proto3" json:"host_serials,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FeatureFlags            map[string]bool                   `protobuf:"bytes,26,rep,name=feature_flags,json=featureFlags,proto3" json:"feature_flags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TranslatedStatusMessage *frontend.TranslatedStatusMessage `protobuf:"bytes,27,opt,name=translated_status_message,json=translatedStatusMessage,proto3" json:"translated_status_message,omitempty"`
}

func (x *HealthLog) Reset() {
	*x = HealthLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthLog) ProtoMessage() {}

func (x *HealthLog) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthLog.ProtoReflect.Descriptor instead.
func (*HealthLog) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{8}
}

// Deprecated: Marked as deprecated in portal/proto/health.proto.
func (x *HealthLog) GetAlarms() []*AlarmRow {
	if x != nil {
		return x.Alarms
	}
	return nil
}

func (x *HealthLog) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *HealthLog) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *HealthLog) GetModels() []string {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *HealthLog) GetPerformance() *Performance {
	if x != nil {
		return x.Performance
	}
	return nil
}

func (x *HealthLog) GetReportedAt() int64 {
	if x != nil {
		return x.ReportedAt
	}
	return 0
}

func (x *HealthLog) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *HealthLog) GetSystems() []*structpb.Struct {
	if x != nil {
		return x.Systems
	}
	return nil
}

func (x *HealthLog) GetStatus() frontend.Status {
	if x != nil {
		return x.Status
	}
	return frontend.Status(0)
}

func (x *HealthLog) GetStatusChangedAt() int64 {
	if x != nil {
		return x.StatusChangedAt
	}
	return 0
}

// Deprecated: Marked as deprecated in portal/proto/health.proto.
func (x *HealthLog) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *HealthLog) GetP2P() string {
	if x != nil {
		return x.P2P
	}
	return ""
}

func (x *HealthLog) GetSoftwareVersion() string {
	if x != nil {
		return x.SoftwareVersion
	}
	return ""
}

func (x *HealthLog) GetTargetVersion() string {
	if x != nil {
		return x.TargetVersion
	}
	return ""
}

func (x *HealthLog) GetTargetVersionReady() bool {
	if x != nil {
		return x.TargetVersionReady
	}
	return false
}

func (x *HealthLog) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *HealthLog) GetMetricTotals() map[string]uint64 {
	if x != nil {
		return x.MetricTotals
	}
	return nil
}

func (x *HealthLog) GetAlarmList() []*frontend.AlarmRow {
	if x != nil {
		return x.AlarmList
	}
	return nil
}

func (x *HealthLog) GetFieldConfig() *FieldConfig {
	if x != nil {
		return x.FieldConfig
	}
	return nil
}

func (x *HealthLog) GetMetrics() *Metrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *HealthLog) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *HealthLog) GetRobotRuntime_240V() uint32 {
	if x != nil {
		return x.RobotRuntime_240V
	}
	return 0
}

func (x *HealthLog) GetLaserState() *frontend.LaserStateList {
	if x != nil {
		return x.LaserState
	}
	return nil
}

func (x *HealthLog) GetLaserChangeTimes() *metrics.LaserChangeTimes {
	if x != nil {
		return x.LaserChangeTimes
	}
	return nil
}

func (x *HealthLog) GetHostSerials() map[string]string {
	if x != nil {
		return x.HostSerials
	}
	return nil
}

func (x *HealthLog) GetFeatureFlags() map[string]bool {
	if x != nil {
		return x.FeatureFlags
	}
	return nil
}

func (x *HealthLog) GetTranslatedStatusMessage() *frontend.TranslatedStatusMessage {
	if x != nil {
		return x.TranslatedStatusMessage
	}
	return nil
}

type IssueReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	RobotSerial string `protobuf:"bytes,3,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	ReportedAt  int64  `protobuf:"varint,4,opt,name=reported_at,json=reportedAt,proto3" json:"reported_at,omitempty"`
	// Deprecated: Marked as deprecated in portal/proto/health.proto.
	Crop            string `protobuf:"bytes,5,opt,name=crop,proto3" json:"crop,omitempty"` // Deprecated, use crop_id
	ModelId         string `protobuf:"bytes,6,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	SoftwareVersion string `protobuf:"bytes,7,opt,name=software_version,json=softwareVersion,proto3" json:"software_version,omitempty"`
	CropId          string `protobuf:"bytes,8,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *IssueReport) Reset() {
	*x = IssueReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_health_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IssueReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IssueReport) ProtoMessage() {}

func (x *IssueReport) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_health_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IssueReport.ProtoReflect.Descriptor instead.
func (*IssueReport) Descriptor() ([]byte, []int) {
	return file_portal_proto_health_proto_rawDescGZIP(), []int{9}
}

func (x *IssueReport) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *IssueReport) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *IssueReport) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *IssueReport) GetReportedAt() int64 {
	if x != nil {
		return x.ReportedAt
	}
	return 0
}

// Deprecated: Marked as deprecated in portal/proto/health.proto.
func (x *IssueReport) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *IssueReport) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *IssueReport) GetSoftwareVersion() string {
	if x != nil {
		return x.SoftwareVersion
	}
	return ""
}

func (x *IssueReport) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

var File_portal_proto_health_proto protoreflect.FileDescriptor

var file_portal_proto_health_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9,
	0x02, 0x0a, 0x08, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x6f, 0x77, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x36, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61,
	0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x06, 0x69,
	0x6d, 0x70, 0x61, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x49, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x52, 0x06,
	0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x34, 0x0a, 0x08, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x01, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x7a,
	0x22, 0x97, 0x07, 0x0a, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x27, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x61, 0x6e,
	0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x39, 0x0a, 0x19, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x16, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x63, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x54, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x61,
	0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6e, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x3a, 0x0a, 0x19, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x69, 0x73, 0x63,
	0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a,
	0x15, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x3f, 0x0a, 0x1c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x76, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x56,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x43, 0x0a, 0x1e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x76, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x1d, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x08, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x22, 0x98, 0x01, 0x0a, 0x12, 0x57, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x61, 0x72, 0x65, 0x61,
	0x57, 0x65, 0x65, 0x64, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x61,
	0x72, 0x65, 0x61, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x64, 0x61, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x61, 0x72, 0x65, 0x61, 0x57, 0x65, 0x65, 0x64,
	0x65, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x65, 0x65, 0x64, 0x65, 0x64, 0x54, 0x6f,
	0x64, 0x61, 0x79, 0x22, 0x51, 0x0a, 0x0b, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x07, 0x77,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x95, 0x01, 0x0a, 0x0c, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc4,
	0x01, 0x0a, 0x07, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x64, 0x61,
	0x69, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x1a, 0x63, 0x0a, 0x11, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x80, 0x0d, 0x0a, 0x09, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x4c, 0x6f, 0x67, 0x12, 0x3a, 0x0a, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d,
	0x52, 0x6f, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x12,
	0x3a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x43, 0x0a, 0x0b, 0x70, 0x65, 0x72,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x12, 0x31, 0x0a, 0x07, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x07, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62,
	0x61, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a,
	0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x32, 0x70, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x70, 0x32, 0x70, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6f, 0x66, 0x74, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x6f, 0x67, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x61, 0x6c,
	0x61, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x6f, 0x77, 0x52,
	0x09, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0c, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x37, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x32, 0x34, 0x30, 0x76, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x32, 0x34, 0x30, 0x76,
	0x12, 0x46, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0a, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x6f, 0x67, 0x2e,
	0x48, 0x6f, 0x73, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x56, 0x0a,
	0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x1a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x4c, 0x6f, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x6f, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x17, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x3f, 0x0a, 0x11, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x48, 0x6f, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8d, 0x02, 0x0a, 0x0b, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x16, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x2a, 0x8e, 0x01, 0x0a, 0x0a, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x15, 0x0a, 0x0d, 0x41, 0x4c, 0x41, 0x52, 0x4d,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x16,
	0x0a, 0x0e, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c,
	0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x12, 0x0a, 0x0a, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x5f,
	0x48, 0x49, 0x47, 0x48, 0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x14, 0x0a, 0x0c, 0x41, 0x4c,
	0x41, 0x52, 0x4d, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x10, 0x03, 0x1a, 0x02, 0x08, 0x01,
	0x12, 0x11, 0x0a, 0x09, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x04, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x14, 0x0a, 0x0c, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x5f, 0x48, 0x49, 0x44,
	0x44, 0x45, 0x4e, 0x10, 0x05, 0x1a, 0x02, 0x08, 0x01, 0x2a, 0x86, 0x01, 0x0a, 0x0b, 0x41, 0x6c,
	0x61, 0x72, 0x6d, 0x49, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x10, 0x49, 0x4d, 0x50,
	0x41, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x17, 0x0a, 0x0f, 0x49, 0x4d, 0x50, 0x41, 0x43, 0x54, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x16, 0x0a, 0x0e,
	0x49, 0x4d, 0x50, 0x41, 0x43, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02,
	0x1a, 0x02, 0x08, 0x01, 0x12, 0x17, 0x0a, 0x0f, 0x49, 0x4d, 0x50, 0x41, 0x43, 0x54, 0x5f, 0x44,
	0x45, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44, 0x10, 0x03, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x13, 0x0a,
	0x0b, 0x49, 0x4d, 0x50, 0x41, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x1a, 0x02,
	0x08, 0x01, 0x32, 0xa5, 0x01, 0x0a, 0x0d, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c,
	0x6f, 0x67, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4b, 0x0a,
	0x0b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x2e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x1a,
	0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_portal_proto_health_proto_rawDescOnce sync.Once
	file_portal_proto_health_proto_rawDescData = file_portal_proto_health_proto_rawDesc
)

func file_portal_proto_health_proto_rawDescGZIP() []byte {
	file_portal_proto_health_proto_rawDescOnce.Do(func() {
		file_portal_proto_health_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_health_proto_rawDescData)
	})
	return file_portal_proto_health_proto_rawDescData
}

var file_portal_proto_health_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_portal_proto_health_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_portal_proto_health_proto_goTypes = []interface{}{
	(AlarmLevel)(0),                          // 0: carbon.portal.health.AlarmLevel
	(AlarmImpact)(0),                         // 1: carbon.portal.health.AlarmImpact
	(*AlarmRow)(nil),                         // 2: carbon.portal.health.AlarmRow
	(*Location)(nil),                         // 3: carbon.portal.health.Location
	(*FieldConfig)(nil),                      // 4: carbon.portal.health.FieldConfig
	(*Versions)(nil),                         // 5: carbon.portal.health.Versions
	(*WeedingPerformance)(nil),               // 6: carbon.portal.health.WeedingPerformance
	(*Performance)(nil),                      // 7: carbon.portal.health.Performance
	(*DailyMetrics)(nil),                     // 8: carbon.portal.health.DailyMetrics
	(*Metrics)(nil),                          // 9: carbon.portal.health.Metrics
	(*HealthLog)(nil),                        // 10: carbon.portal.health.HealthLog
	(*IssueReport)(nil),                      // 11: carbon.portal.health.IssueReport
	nil,                                      // 12: carbon.portal.health.DailyMetrics.MetricsEntry
	nil,                                      // 13: carbon.portal.health.Metrics.DailyMetricsEntry
	nil,                                      // 14: carbon.portal.health.HealthLog.MetricTotalsEntry
	nil,                                      // 15: carbon.portal.health.HealthLog.HostSerialsEntry
	nil,                                      // 16: carbon.portal.health.HealthLog.FeatureFlagsEntry
	(*structpb.Struct)(nil),                  // 17: google.protobuf.Struct
	(frontend.Status)(0),                     // 18: carbon.frontend.status_bar.Status
	(*frontend.AlarmRow)(nil),                // 19: carbon.frontend.alarm.AlarmRow
	(*frontend.LaserStateList)(nil),          // 20: carbon.frontend.laser.LaserStateList
	(*metrics.LaserChangeTimes)(nil),         // 21: carbon.metrics.LaserChangeTimes
	(*frontend.TranslatedStatusMessage)(nil), // 22: carbon.frontend.status_bar.TranslatedStatusMessage
	(*Empty)(nil),                            // 23: carbon.portal.util.Empty
}
var file_portal_proto_health_proto_depIdxs = []int32{
	0,  // 0: carbon.portal.health.AlarmRow.level:type_name -> carbon.portal.health.AlarmLevel
	1,  // 1: carbon.portal.health.AlarmRow.impact:type_name -> carbon.portal.health.AlarmImpact
	6,  // 2: carbon.portal.health.Performance.weeding:type_name -> carbon.portal.health.WeedingPerformance
	12, // 3: carbon.portal.health.DailyMetrics.metrics:type_name -> carbon.portal.health.DailyMetrics.MetricsEntry
	13, // 4: carbon.portal.health.Metrics.daily_metrics:type_name -> carbon.portal.health.Metrics.DailyMetricsEntry
	2,  // 5: carbon.portal.health.HealthLog.alarms:type_name -> carbon.portal.health.AlarmRow
	3,  // 6: carbon.portal.health.HealthLog.location:type_name -> carbon.portal.health.Location
	7,  // 7: carbon.portal.health.HealthLog.performance:type_name -> carbon.portal.health.Performance
	17, // 8: carbon.portal.health.HealthLog.systems:type_name -> google.protobuf.Struct
	18, // 9: carbon.portal.health.HealthLog.status:type_name -> carbon.frontend.status_bar.Status
	14, // 10: carbon.portal.health.HealthLog.metric_totals:type_name -> carbon.portal.health.HealthLog.MetricTotalsEntry
	19, // 11: carbon.portal.health.HealthLog.alarm_list:type_name -> carbon.frontend.alarm.AlarmRow
	4,  // 12: carbon.portal.health.HealthLog.field_config:type_name -> carbon.portal.health.FieldConfig
	9,  // 13: carbon.portal.health.HealthLog.metrics:type_name -> carbon.portal.health.Metrics
	20, // 14: carbon.portal.health.HealthLog.laser_state:type_name -> carbon.frontend.laser.LaserStateList
	21, // 15: carbon.portal.health.HealthLog.laser_change_times:type_name -> carbon.metrics.LaserChangeTimes
	15, // 16: carbon.portal.health.HealthLog.host_serials:type_name -> carbon.portal.health.HealthLog.HostSerialsEntry
	16, // 17: carbon.portal.health.HealthLog.feature_flags:type_name -> carbon.portal.health.HealthLog.FeatureFlagsEntry
	22, // 18: carbon.portal.health.HealthLog.translated_status_message:type_name -> carbon.frontend.status_bar.TranslatedStatusMessage
	8,  // 19: carbon.portal.health.Metrics.DailyMetricsEntry.value:type_name -> carbon.portal.health.DailyMetrics
	10, // 20: carbon.portal.health.HealthService.LogHealth:input_type -> carbon.portal.health.HealthLog
	11, // 21: carbon.portal.health.HealthService.ReportIssue:input_type -> carbon.portal.health.IssueReport
	23, // 22: carbon.portal.health.HealthService.LogHealth:output_type -> carbon.portal.util.Empty
	23, // 23: carbon.portal.health.HealthService.ReportIssue:output_type -> carbon.portal.util.Empty
	22, // [22:24] is the sub-list for method output_type
	20, // [20:22] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_portal_proto_health_proto_init() }
func file_portal_proto_health_proto_init() {
	if File_portal_proto_health_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_health_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Versions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedingPerformance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Performance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_health_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IssueReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_health_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_health_proto_goTypes,
		DependencyIndexes: file_portal_proto_health_proto_depIdxs,
		EnumInfos:         file_portal_proto_health_proto_enumTypes,
		MessageInfos:      file_portal_proto_health_proto_msgTypes,
	}.Build()
	File_portal_proto_health_proto = out.File
	file_portal_proto_health_proto_rawDesc = nil
	file_portal_proto_health_proto_goTypes = nil
	file_portal_proto_health_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/remoteit.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfigureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId  string `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	DeviceName string `protobuf:"bytes,2,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
}

func (x *ConfigureRequest) Reset() {
	*x = ConfigureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_remoteit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigureRequest) ProtoMessage() {}

func (x *ConfigureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_remoteit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigureRequest.ProtoReflect.Descriptor instead.
func (*ConfigureRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_remoteit_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigureRequest) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ConfigureRequest) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

type ConfigureResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ConfigureResult) Reset() {
	*x = ConfigureResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_remoteit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigureResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigureResult) ProtoMessage() {}

func (x *ConfigureResult) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_remoteit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigureResult.ProtoReflect.Descriptor instead.
func (*ConfigureResult) Descriptor() ([]byte, []int) {
	return file_portal_proto_remoteit_proto_rawDescGZIP(), []int{1}
}

func (x *ConfigureResult) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_portal_proto_remoteit_proto protoreflect.FileDescriptor

var file_portal_proto_remoteit_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x69, 0x74, 0x22, 0x50, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x29, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x32, 0x73, 0x0a, 0x0f, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x74, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x60, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x65, 0x12, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x69, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x69, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_remoteit_proto_rawDescOnce sync.Once
	file_portal_proto_remoteit_proto_rawDescData = file_portal_proto_remoteit_proto_rawDesc
)

func file_portal_proto_remoteit_proto_rawDescGZIP() []byte {
	file_portal_proto_remoteit_proto_rawDescOnce.Do(func() {
		file_portal_proto_remoteit_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_remoteit_proto_rawDescData)
	})
	return file_portal_proto_remoteit_proto_rawDescData
}

var file_portal_proto_remoteit_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_portal_proto_remoteit_proto_goTypes = []interface{}{
	(*ConfigureRequest)(nil), // 0: carbon.portal.remoteit.ConfigureRequest
	(*ConfigureResult)(nil),  // 1: carbon.portal.remoteit.ConfigureResult
}
var file_portal_proto_remoteit_proto_depIdxs = []int32{
	0, // 0: carbon.portal.remoteit.RemoteItManager.Configure:input_type -> carbon.portal.remoteit.ConfigureRequest
	1, // 1: carbon.portal.remoteit.RemoteItManager.Configure:output_type -> carbon.portal.remoteit.ConfigureResult
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_portal_proto_remoteit_proto_init() }
func file_portal_proto_remoteit_proto_init() {
	if File_portal_proto_remoteit_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_remoteit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_remoteit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigureResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_remoteit_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_remoteit_proto_goTypes,
		DependencyIndexes: file_portal_proto_remoteit_proto_depIdxs,
		MessageInfos:      file_portal_proto_remoteit_proto_msgTypes,
	}.Build()
	File_portal_proto_remoteit_proto = out.File
	file_portal_proto_remoteit_proto_rawDesc = nil
	file_portal_proto_remoteit_proto_goTypes = nil
	file_portal_proto_remoteit_proto_depIdxs = nil
}

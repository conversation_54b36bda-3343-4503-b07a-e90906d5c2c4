// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/spatial_metrics_sync.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SpatialMetricsSyncService_SyncSpatialMetricBlocks_FullMethodName = "/carbon.portal.spatial_metrics.SpatialMetricsSyncService/SyncSpatialMetricBlocks"
)

// SpatialMetricsSyncServiceClient is the client API for SpatialMetricsSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SpatialMetricsSyncServiceClient interface {
	SyncSpatialMetricBlocks(ctx context.Context, in *SyncSpatialMetricBlocksRequest, opts ...grpc.CallOption) (*Empty, error)
}

type spatialMetricsSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSpatialMetricsSyncServiceClient(cc grpc.ClientConnInterface) SpatialMetricsSyncServiceClient {
	return &spatialMetricsSyncServiceClient{cc}
}

func (c *spatialMetricsSyncServiceClient) SyncSpatialMetricBlocks(ctx context.Context, in *SyncSpatialMetricBlocksRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SpatialMetricsSyncService_SyncSpatialMetricBlocks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpatialMetricsSyncServiceServer is the server API for SpatialMetricsSyncService service.
// All implementations must embed UnimplementedSpatialMetricsSyncServiceServer
// for forward compatibility
type SpatialMetricsSyncServiceServer interface {
	SyncSpatialMetricBlocks(context.Context, *SyncSpatialMetricBlocksRequest) (*Empty, error)
	mustEmbedUnimplementedSpatialMetricsSyncServiceServer()
}

// UnimplementedSpatialMetricsSyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSpatialMetricsSyncServiceServer struct {
}

func (UnimplementedSpatialMetricsSyncServiceServer) SyncSpatialMetricBlocks(context.Context, *SyncSpatialMetricBlocksRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncSpatialMetricBlocks not implemented")
}
func (UnimplementedSpatialMetricsSyncServiceServer) mustEmbedUnimplementedSpatialMetricsSyncServiceServer() {
}

// UnsafeSpatialMetricsSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SpatialMetricsSyncServiceServer will
// result in compilation errors.
type UnsafeSpatialMetricsSyncServiceServer interface {
	mustEmbedUnimplementedSpatialMetricsSyncServiceServer()
}

func RegisterSpatialMetricsSyncServiceServer(s grpc.ServiceRegistrar, srv SpatialMetricsSyncServiceServer) {
	s.RegisterService(&SpatialMetricsSyncService_ServiceDesc, srv)
}

func _SpatialMetricsSyncService_SyncSpatialMetricBlocks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncSpatialMetricBlocksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpatialMetricsSyncServiceServer).SyncSpatialMetricBlocks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SpatialMetricsSyncService_SyncSpatialMetricBlocks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpatialMetricsSyncServiceServer).SyncSpatialMetricBlocks(ctx, req.(*SyncSpatialMetricBlocksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SpatialMetricsSyncService_ServiceDesc is the grpc.ServiceDesc for SpatialMetricsSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SpatialMetricsSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.spatial_metrics.SpatialMetricsSyncService",
	HandlerType: (*SpatialMetricsSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncSpatialMetricBlocks",
			Handler:    _SpatialMetricsSyncService_SyncSpatialMetricBlocks_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/spatial_metrics_sync.proto",
}

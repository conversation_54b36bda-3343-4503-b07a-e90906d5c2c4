// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/remoteit.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RemoteItManager_Configure_FullMethodName = "/carbon.portal.remoteit.RemoteItManager/Configure"
)

// RemoteItManagerClient is the client API for RemoteItManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RemoteItManagerClient interface {
	Configure(ctx context.Context, in *ConfigureRequest, opts ...grpc.CallOption) (*ConfigureResult, error)
}

type remoteItManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewRemoteItManagerClient(cc grpc.ClientConnInterface) RemoteItManagerClient {
	return &remoteItManagerClient{cc}
}

func (c *remoteItManagerClient) Configure(ctx context.Context, in *ConfigureRequest, opts ...grpc.CallOption) (*ConfigureResult, error) {
	out := new(ConfigureResult)
	err := c.cc.Invoke(ctx, RemoteItManager_Configure_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RemoteItManagerServer is the server API for RemoteItManager service.
// All implementations must embed UnimplementedRemoteItManagerServer
// for forward compatibility
type RemoteItManagerServer interface {
	Configure(context.Context, *ConfigureRequest) (*ConfigureResult, error)
	mustEmbedUnimplementedRemoteItManagerServer()
}

// UnimplementedRemoteItManagerServer must be embedded to have forward compatible implementations.
type UnimplementedRemoteItManagerServer struct {
}

func (UnimplementedRemoteItManagerServer) Configure(context.Context, *ConfigureRequest) (*ConfigureResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Configure not implemented")
}
func (UnimplementedRemoteItManagerServer) mustEmbedUnimplementedRemoteItManagerServer() {}

// UnsafeRemoteItManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RemoteItManagerServer will
// result in compilation errors.
type UnsafeRemoteItManagerServer interface {
	mustEmbedUnimplementedRemoteItManagerServer()
}

func RegisterRemoteItManagerServer(s grpc.ServiceRegistrar, srv RemoteItManagerServer) {
	s.RegisterService(&RemoteItManager_ServiceDesc, srv)
}

func _RemoteItManager_Configure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RemoteItManagerServer).Configure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RemoteItManager_Configure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RemoteItManagerServer).Configure(ctx, req.(*ConfigureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RemoteItManager_ServiceDesc is the grpc.ServiceDesc for RemoteItManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RemoteItManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.remoteit.RemoteItManager",
	HandlerType: (*RemoteItManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Configure",
			Handler:    _RemoteItManager_Configure_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/remoteit.proto",
}

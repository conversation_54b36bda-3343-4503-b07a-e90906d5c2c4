// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/jobs_portal.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PortalJobsService_UploadJob_FullMethodName           = "/carbon.portal.jobs.PortalJobsService/UploadJob"
	PortalJobsService_UploadJobConfigDump_FullMethodName = "/carbon.portal.jobs.PortalJobsService/UploadJobConfigDump"
	PortalJobsService_UploadJobMetrics_FullMethodName    = "/carbon.portal.jobs.PortalJobsService/UploadJobMetrics"
)

// PortalJobsServiceClient is the client API for PortalJobsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PortalJobsServiceClient interface {
	UploadJob(ctx context.Context, in *UploadJobRequest, opts ...grpc.CallOption) (*Empty, error)
	UploadJobConfigDump(ctx context.Context, in *UploadJobConfigDumpRequest, opts ...grpc.CallOption) (*Empty, error)
	UploadJobMetrics(ctx context.Context, in *UploadJobMetricsRequest, opts ...grpc.CallOption) (*Empty, error)
}

type portalJobsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPortalJobsServiceClient(cc grpc.ClientConnInterface) PortalJobsServiceClient {
	return &portalJobsServiceClient{cc}
}

func (c *portalJobsServiceClient) UploadJob(ctx context.Context, in *UploadJobRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalJobsService_UploadJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalJobsServiceClient) UploadJobConfigDump(ctx context.Context, in *UploadJobConfigDumpRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalJobsService_UploadJobConfigDump_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalJobsServiceClient) UploadJobMetrics(ctx context.Context, in *UploadJobMetricsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PortalJobsService_UploadJobMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PortalJobsServiceServer is the server API for PortalJobsService service.
// All implementations must embed UnimplementedPortalJobsServiceServer
// for forward compatibility
type PortalJobsServiceServer interface {
	UploadJob(context.Context, *UploadJobRequest) (*Empty, error)
	UploadJobConfigDump(context.Context, *UploadJobConfigDumpRequest) (*Empty, error)
	UploadJobMetrics(context.Context, *UploadJobMetricsRequest) (*Empty, error)
	mustEmbedUnimplementedPortalJobsServiceServer()
}

// UnimplementedPortalJobsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPortalJobsServiceServer struct {
}

func (UnimplementedPortalJobsServiceServer) UploadJob(context.Context, *UploadJobRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadJob not implemented")
}
func (UnimplementedPortalJobsServiceServer) UploadJobConfigDump(context.Context, *UploadJobConfigDumpRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadJobConfigDump not implemented")
}
func (UnimplementedPortalJobsServiceServer) UploadJobMetrics(context.Context, *UploadJobMetricsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadJobMetrics not implemented")
}
func (UnimplementedPortalJobsServiceServer) mustEmbedUnimplementedPortalJobsServiceServer() {}

// UnsafePortalJobsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PortalJobsServiceServer will
// result in compilation errors.
type UnsafePortalJobsServiceServer interface {
	mustEmbedUnimplementedPortalJobsServiceServer()
}

func RegisterPortalJobsServiceServer(s grpc.ServiceRegistrar, srv PortalJobsServiceServer) {
	s.RegisterService(&PortalJobsService_ServiceDesc, srv)
}

func _PortalJobsService_UploadJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalJobsServiceServer).UploadJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalJobsService_UploadJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalJobsServiceServer).UploadJob(ctx, req.(*UploadJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalJobsService_UploadJobConfigDump_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadJobConfigDumpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalJobsServiceServer).UploadJobConfigDump(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalJobsService_UploadJobConfigDump_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalJobsServiceServer).UploadJobConfigDump(ctx, req.(*UploadJobConfigDumpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortalJobsService_UploadJobMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadJobMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalJobsServiceServer).UploadJobMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PortalJobsService_UploadJobMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalJobsServiceServer).UploadJobMetrics(ctx, req.(*UploadJobMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PortalJobsService_ServiceDesc is the grpc.ServiceDesc for PortalJobsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PortalJobsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.jobs.PortalJobsService",
	HandlerType: (*PortalJobsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadJob",
			Handler:    _PortalJobsService_UploadJob_Handler,
		},
		{
			MethodName: "UploadJobConfigDump",
			Handler:    _PortalJobsService_UploadJobConfigDump_Handler,
		},
		{
			MethodName: "UploadJobMetrics",
			Handler:    _PortalJobsService_UploadJobMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/jobs_portal.proto",
}

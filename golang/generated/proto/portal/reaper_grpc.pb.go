// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: portal/proto/reaper.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReaperConfigurationService_UploadReaperConfiguration_FullMethodName = "/carbon.portal.reaper.ReaperConfigurationService/UploadReaperConfiguration"
)

// ReaperConfigurationServiceClient is the client API for ReaperConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReaperConfigurationServiceClient interface {
	UploadReaperConfiguration(ctx context.Context, in *UploadReaperConfigurationRequest, opts ...grpc.CallOption) (*UploadReaperConfigurationResponse, error)
}

type reaperConfigurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReaperConfigurationServiceClient(cc grpc.ClientConnInterface) ReaperConfigurationServiceClient {
	return &reaperConfigurationServiceClient{cc}
}

func (c *reaperConfigurationServiceClient) UploadReaperConfiguration(ctx context.Context, in *UploadReaperConfigurationRequest, opts ...grpc.CallOption) (*UploadReaperConfigurationResponse, error) {
	out := new(UploadReaperConfigurationResponse)
	err := c.cc.Invoke(ctx, ReaperConfigurationService_UploadReaperConfiguration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReaperConfigurationServiceServer is the server API for ReaperConfigurationService service.
// All implementations must embed UnimplementedReaperConfigurationServiceServer
// for forward compatibility
type ReaperConfigurationServiceServer interface {
	UploadReaperConfiguration(context.Context, *UploadReaperConfigurationRequest) (*UploadReaperConfigurationResponse, error)
	mustEmbedUnimplementedReaperConfigurationServiceServer()
}

// UnimplementedReaperConfigurationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedReaperConfigurationServiceServer struct {
}

func (UnimplementedReaperConfigurationServiceServer) UploadReaperConfiguration(context.Context, *UploadReaperConfigurationRequest) (*UploadReaperConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadReaperConfiguration not implemented")
}
func (UnimplementedReaperConfigurationServiceServer) mustEmbedUnimplementedReaperConfigurationServiceServer() {
}

// UnsafeReaperConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReaperConfigurationServiceServer will
// result in compilation errors.
type UnsafeReaperConfigurationServiceServer interface {
	mustEmbedUnimplementedReaperConfigurationServiceServer()
}

func RegisterReaperConfigurationServiceServer(s grpc.ServiceRegistrar, srv ReaperConfigurationServiceServer) {
	s.RegisterService(&ReaperConfigurationService_ServiceDesc, srv)
}

func _ReaperConfigurationService_UploadReaperConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadReaperConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReaperConfigurationServiceServer).UploadReaperConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReaperConfigurationService_UploadReaperConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReaperConfigurationServiceServer).UploadReaperConfiguration(ctx, req.(*UploadReaperConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReaperConfigurationService_ServiceDesc is the grpc.ServiceDesc for ReaperConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReaperConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.reaper.ReaperConfigurationService",
	HandlerType: (*ReaperConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadReaperConfiguration",
			Handler:    _ReaperConfigurationService_UploadReaperConfiguration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/proto/reaper.proto",
}

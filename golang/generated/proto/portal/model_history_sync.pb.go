// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/model_history_sync.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelEventType int32

const (
	ModelEventType_UNKNOWN                  ModelEventType = 0
	ModelEventType_ROBOT_START              ModelEventType = 1
	ModelEventType_PINNED                   ModelEventType = 2
	ModelEventType_UNPINNED                 ModelEventType = 3
	ModelEventType_RECOMMENDED              ModelEventType = 4
	ModelEventType_ACTIVATED                ModelEventType = 5
	ModelEventType_NICKNAME_CHANGE          ModelEventType = 6
	ModelEventType_NICKNAME_DELETE          ModelEventType = 7
	ModelEventType_DEFAULT_PARAMETER_CHANGE ModelEventType = 8
	ModelEventType_PARAMETER_CHANGE         ModelEventType = 9
)

// Enum value maps for ModelEventType.
var (
	ModelEventType_name = map[int32]string{
		0: "UNKNOWN",
		1: "ROBOT_START",
		2: "PINNED",
		3: "UNPINNED",
		4: "RECOMMENDED",
		5: "ACTIVATED",
		6: "NICKNAME_CHANGE",
		7: "NICKNAME_DELETE",
		8: "DEFAULT_PARAMETER_CHANGE",
		9: "PARAMETER_CHANGE",
	}
	ModelEventType_value = map[string]int32{
		"UNKNOWN":                  0,
		"ROBOT_START":              1,
		"PINNED":                   2,
		"UNPINNED":                 3,
		"RECOMMENDED":              4,
		"ACTIVATED":                5,
		"NICKNAME_CHANGE":          6,
		"NICKNAME_DELETE":          7,
		"DEFAULT_PARAMETER_CHANGE": 8,
		"PARAMETER_CHANGE":         9,
	}
)

func (x ModelEventType) Enum() *ModelEventType {
	p := new(ModelEventType)
	*p = x
	return p
}

func (x ModelEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_proto_model_history_sync_proto_enumTypes[0].Descriptor()
}

func (ModelEventType) Type() protoreflect.EnumType {
	return &file_portal_proto_model_history_sync_proto_enumTypes[0]
}

func (x ModelEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelEventType.Descriptor instead.
func (ModelEventType) EnumDescriptor() ([]byte, []int) {
	return file_portal_proto_model_history_sync_proto_rawDescGZIP(), []int{0}
}

type ModelEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            ModelEventType `protobuf:"varint,1,opt,name=type,proto3,enum=carbon.portal.model_history.ModelEventType" json:"type,omitempty"`
	ModelId         string         `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelNickname   string         `protobuf:"bytes,3,opt,name=model_nickname,json=modelNickname,proto3" json:"model_nickname,omitempty"`
	ModelParameters string         `protobuf:"bytes,4,opt,name=model_parameters,json=modelParameters,proto3" json:"model_parameters,omitempty"`
	ModelType       string         `protobuf:"bytes,5,opt,name=model_type,json=modelType,proto3" json:"model_type,omitempty"`
	CropId          string         `protobuf:"bytes,6,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	JobId           string         `protobuf:"bytes,7,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	TimestampMs     int64          `protobuf:"varint,8,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *ModelEvent) Reset() {
	*x = ModelEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_history_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelEvent) ProtoMessage() {}

func (x *ModelEvent) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_history_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelEvent.ProtoReflect.Descriptor instead.
func (*ModelEvent) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_history_sync_proto_rawDescGZIP(), []int{0}
}

func (x *ModelEvent) GetType() ModelEventType {
	if x != nil {
		return x.Type
	}
	return ModelEventType_UNKNOWN
}

func (x *ModelEvent) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelEvent) GetModelNickname() string {
	if x != nil {
		return x.ModelNickname
	}
	return ""
}

func (x *ModelEvent) GetModelParameters() string {
	if x != nil {
		return x.ModelParameters
	}
	return ""
}

func (x *ModelEvent) GetModelType() string {
	if x != nil {
		return x.ModelType
	}
	return ""
}

func (x *ModelEvent) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *ModelEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *ModelEvent) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type UploadModelEventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot  string        `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	Events []*ModelEvent `protobuf:"bytes,2,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *UploadModelEventsRequest) Reset() {
	*x = UploadModelEventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_model_history_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadModelEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadModelEventsRequest) ProtoMessage() {}

func (x *UploadModelEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_model_history_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadModelEventsRequest.ProtoReflect.Descriptor instead.
func (*UploadModelEventsRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_model_history_sync_proto_rawDescGZIP(), []int{1}
}

func (x *UploadModelEventsRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

func (x *UploadModelEventsRequest) GetEvents() []*ModelEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

var File_portal_proto_model_history_sync_proto protoreflect.FileDescriptor

var file_portal_proto_model_history_sync_proto_rawDesc = []byte{
	0x0a, 0x25, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x79, 0x6e,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x1a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x02,
	0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x71, 0x0a, 0x18,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x3f,
	0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2a,
	0xc6, 0x01, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x52, 0x4f, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x50, 0x49, 0x4e, 0x4e, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08,
	0x55, 0x4e, 0x50, 0x49, 0x4e, 0x4e, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45,
	0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x49,
	0x43, 0x4b, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x06, 0x12,
	0x13, 0x0a, 0x0f, 0x4e, 0x49, 0x43, 0x4b, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f,
	0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x09, 0x32, 0x80, 0x01, 0x0a, 0x17, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x65, 0x0a, 0x11, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0e, 0x5a, 0x0c, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_model_history_sync_proto_rawDescOnce sync.Once
	file_portal_proto_model_history_sync_proto_rawDescData = file_portal_proto_model_history_sync_proto_rawDesc
)

func file_portal_proto_model_history_sync_proto_rawDescGZIP() []byte {
	file_portal_proto_model_history_sync_proto_rawDescOnce.Do(func() {
		file_portal_proto_model_history_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_model_history_sync_proto_rawDescData)
	})
	return file_portal_proto_model_history_sync_proto_rawDescData
}

var file_portal_proto_model_history_sync_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_portal_proto_model_history_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_portal_proto_model_history_sync_proto_goTypes = []interface{}{
	(ModelEventType)(0),              // 0: carbon.portal.model_history.ModelEventType
	(*ModelEvent)(nil),               // 1: carbon.portal.model_history.ModelEvent
	(*UploadModelEventsRequest)(nil), // 2: carbon.portal.model_history.UploadModelEventsRequest
	(*Empty)(nil),                    // 3: carbon.portal.util.Empty
}
var file_portal_proto_model_history_sync_proto_depIdxs = []int32{
	0, // 0: carbon.portal.model_history.ModelEvent.type:type_name -> carbon.portal.model_history.ModelEventType
	1, // 1: carbon.portal.model_history.UploadModelEventsRequest.events:type_name -> carbon.portal.model_history.ModelEvent
	2, // 2: carbon.portal.model_history.ModelHistorySyncService.UploadModelEvents:input_type -> carbon.portal.model_history.UploadModelEventsRequest
	3, // 3: carbon.portal.model_history.ModelHistorySyncService.UploadModelEvents:output_type -> carbon.portal.util.Empty
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_portal_proto_model_history_sync_proto_init() }
func file_portal_proto_model_history_sync_proto_init() {
	if File_portal_proto_model_history_sync_proto != nil {
		return
	}
	file_portal_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_model_history_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_model_history_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadModelEventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_model_history_sync_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_model_history_sync_proto_goTypes,
		DependencyIndexes: file_portal_proto_model_history_sync_proto_depIdxs,
		EnumInfos:         file_portal_proto_model_history_sync_proto_enumTypes,
		MessageInfos:      file_portal_proto_model_history_sync_proto_msgTypes,
	}.Build()
	File_portal_proto_model_history_sync_proto = out.File
	file_portal_proto_model_history_sync_proto_rawDesc = nil
	file_portal_proto_model_history_sync_proto_goTypes = nil
	file_portal_proto_model_history_sync_proto_depIdxs = nil
}

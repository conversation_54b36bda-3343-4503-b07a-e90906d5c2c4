// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: portal/proto/reaper.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReaperConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssignedModules        []*frontend.ModuleIdentity `protobuf:"bytes,1,rep,name=assigned_modules,json=assignedModules,proto3" json:"assigned_modules,omitempty"`
	CurrentRobotDefinition *frontend.RobotDefinition  `protobuf:"bytes,2,opt,name=current_robot_definition,json=currentRobotDefinition,proto3" json:"current_robot_definition,omitempty"`
}

func (x *ReaperConfiguration) Reset() {
	*x = ReaperConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_reaper_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperConfiguration) ProtoMessage() {}

func (x *ReaperConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_reaper_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperConfiguration.ProtoReflect.Descriptor instead.
func (*ReaperConfiguration) Descriptor() ([]byte, []int) {
	return file_portal_proto_reaper_proto_rawDescGZIP(), []int{0}
}

func (x *ReaperConfiguration) GetAssignedModules() []*frontend.ModuleIdentity {
	if x != nil {
		return x.AssignedModules
	}
	return nil
}

func (x *ReaperConfiguration) GetCurrentRobotDefinition() *frontend.RobotDefinition {
	if x != nil {
		return x.CurrentRobotDefinition
	}
	return nil
}

type UploadReaperConfigurationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Configuration *ReaperConfiguration `protobuf:"bytes,1,opt,name=configuration,proto3" json:"configuration,omitempty"`
}

func (x *UploadReaperConfigurationRequest) Reset() {
	*x = UploadReaperConfigurationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_reaper_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadReaperConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadReaperConfigurationRequest) ProtoMessage() {}

func (x *UploadReaperConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_reaper_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadReaperConfigurationRequest.ProtoReflect.Descriptor instead.
func (*UploadReaperConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_portal_proto_reaper_proto_rawDescGZIP(), []int{1}
}

func (x *UploadReaperConfigurationRequest) GetConfiguration() *ReaperConfiguration {
	if x != nil {
		return x.Configuration
	}
	return nil
}

type UploadReaperConfigurationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UploadReaperConfigurationResponse) Reset() {
	*x = UploadReaperConfigurationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_proto_reaper_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadReaperConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadReaperConfigurationResponse) ProtoMessage() {}

func (x *UploadReaperConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_proto_reaper_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadReaperConfigurationResponse.ProtoReflect.Descriptor instead.
func (*UploadReaperConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_portal_proto_reaper_proto_rawDescGZIP(), []int{2}
}

var File_portal_proto_reaper_proto protoreflect.FileDescriptor

var file_portal_proto_reaper_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x1a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcb,
	0x01, 0x0a, 0x13, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x61, 0x0a, 0x18, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x73, 0x0a, 0x20,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x4f, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x23, 0x0a, 0x21, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xab, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x72, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_proto_reaper_proto_rawDescOnce sync.Once
	file_portal_proto_reaper_proto_rawDescData = file_portal_proto_reaper_proto_rawDesc
)

func file_portal_proto_reaper_proto_rawDescGZIP() []byte {
	file_portal_proto_reaper_proto_rawDescOnce.Do(func() {
		file_portal_proto_reaper_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_proto_reaper_proto_rawDescData)
	})
	return file_portal_proto_reaper_proto_rawDescData
}

var file_portal_proto_reaper_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_portal_proto_reaper_proto_goTypes = []interface{}{
	(*ReaperConfiguration)(nil),               // 0: carbon.portal.reaper.ReaperConfiguration
	(*UploadReaperConfigurationRequest)(nil),  // 1: carbon.portal.reaper.UploadReaperConfigurationRequest
	(*UploadReaperConfigurationResponse)(nil), // 2: carbon.portal.reaper.UploadReaperConfigurationResponse
	(*frontend.ModuleIdentity)(nil),           // 3: carbon.frontend.module.ModuleIdentity
	(*frontend.RobotDefinition)(nil),          // 4: carbon.frontend.module.RobotDefinition
}
var file_portal_proto_reaper_proto_depIdxs = []int32{
	3, // 0: carbon.portal.reaper.ReaperConfiguration.assigned_modules:type_name -> carbon.frontend.module.ModuleIdentity
	4, // 1: carbon.portal.reaper.ReaperConfiguration.current_robot_definition:type_name -> carbon.frontend.module.RobotDefinition
	0, // 2: carbon.portal.reaper.UploadReaperConfigurationRequest.configuration:type_name -> carbon.portal.reaper.ReaperConfiguration
	1, // 3: carbon.portal.reaper.ReaperConfigurationService.UploadReaperConfiguration:input_type -> carbon.portal.reaper.UploadReaperConfigurationRequest
	2, // 4: carbon.portal.reaper.ReaperConfigurationService.UploadReaperConfiguration:output_type -> carbon.portal.reaper.UploadReaperConfigurationResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_portal_proto_reaper_proto_init() }
func file_portal_proto_reaper_proto_init() {
	if File_portal_proto_reaper_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_portal_proto_reaper_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_reaper_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadReaperConfigurationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_proto_reaper_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadReaperConfigurationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_proto_reaper_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_proto_reaper_proto_goTypes,
		DependencyIndexes: file_portal_proto_reaper_proto_depIdxs,
		MessageInfos:      file_portal_proto_reaper_proto_msgTypes,
	}.Build()
	File_portal_proto_reaper_proto = out.File
	file_portal_proto_reaper_proto_rawDesc = nil
	file_portal_proto_reaper_proto_goTypes = nil
	file_portal_proto_reaper_proto_depIdxs = nil
}

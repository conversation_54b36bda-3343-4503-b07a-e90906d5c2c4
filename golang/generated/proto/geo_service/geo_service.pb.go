// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: geo_service/proto/geo_service.proto

package geo_service

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GeoLL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng float64 `protobuf:"fixed64,2,opt,name=lng,proto3" json:"lng,omitempty"`
}

func (x *GeoLL) Reset() {
	*x = GeoLL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_geo_service_proto_geo_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeoLL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoLL) ProtoMessage() {}

func (x *GeoLL) ProtoReflect() protoreflect.Message {
	mi := &file_geo_service_proto_geo_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoLL.ProtoReflect.Descriptor instead.
func (*GeoLL) Descriptor() ([]byte, []int) {
	return file_geo_service_proto_geo_service_proto_rawDescGZIP(), []int{0}
}

func (x *GeoLL) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *GeoLL) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

type GeodFwdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start      *GeoLL  `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	DistMeters float64 `protobuf:"fixed64,2,opt,name=dist_meters,json=distMeters,proto3" json:"dist_meters,omitempty"`
	AzimuthDeg float64 `protobuf:"fixed64,3,opt,name=azimuth_deg,json=azimuthDeg,proto3" json:"azimuth_deg,omitempty"`
}

func (x *GeodFwdRequest) Reset() {
	*x = GeodFwdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_geo_service_proto_geo_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeodFwdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeodFwdRequest) ProtoMessage() {}

func (x *GeodFwdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_geo_service_proto_geo_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeodFwdRequest.ProtoReflect.Descriptor instead.
func (*GeodFwdRequest) Descriptor() ([]byte, []int) {
	return file_geo_service_proto_geo_service_proto_rawDescGZIP(), []int{1}
}

func (x *GeodFwdRequest) GetStart() *GeoLL {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *GeodFwdRequest) GetDistMeters() float64 {
	if x != nil {
		return x.DistMeters
	}
	return 0
}

func (x *GeodFwdRequest) GetAzimuthDeg() float64 {
	if x != nil {
		return x.AzimuthDeg
	}
	return 0
}

type GeodFwdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pos *GeoLL `protobuf:"bytes,1,opt,name=pos,proto3" json:"pos,omitempty"`
}

func (x *GeodFwdResponse) Reset() {
	*x = GeodFwdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_geo_service_proto_geo_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeodFwdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeodFwdResponse) ProtoMessage() {}

func (x *GeodFwdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_geo_service_proto_geo_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeodFwdResponse.ProtoReflect.Descriptor instead.
func (*GeodFwdResponse) Descriptor() ([]byte, []int) {
	return file_geo_service_proto_geo_service_proto_rawDescGZIP(), []int{2}
}

func (x *GeodFwdResponse) GetPos() *GeoLL {
	if x != nil {
		return x.Pos
	}
	return nil
}

var File_geo_service_proto_geo_service_proto protoreflect.FileDescriptor

var file_geo_service_proto_geo_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x22, 0x2b, 0x0a, 0x05, 0x47, 0x65, 0x6f, 0x4c, 0x4c, 0x12, 0x10, 0x0a, 0x03, 0x6c,
	0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x22,
	0x7c, 0x0a, 0x0e, 0x47, 0x65, 0x6f, 0x64, 0x46, 0x77, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47,
	0x65, 0x6f, 0x4c, 0x4c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64,
	0x69, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x7a, 0x69, 0x6d, 0x75, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0a, 0x61, 0x7a, 0x69, 0x6d, 0x75, 0x74, 0x68, 0x44, 0x65, 0x67, 0x22, 0x37, 0x0a,
	0x0f, 0x47, 0x65, 0x6f, 0x64, 0x46, 0x77, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x24, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x4c,
	0x4c, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x32, 0x54, 0x0a, 0x0a, 0x47, 0x65, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x47, 0x65, 0x6f, 0x64, 0x46, 0x77, 0x64, 0x12,
	0x1b, 0x2e, 0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65,
	0x6f, 0x64, 0x46, 0x77, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67,
	0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x64, 0x46,
	0x77, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x13, 0x5a, 0x11,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_geo_service_proto_geo_service_proto_rawDescOnce sync.Once
	file_geo_service_proto_geo_service_proto_rawDescData = file_geo_service_proto_geo_service_proto_rawDesc
)

func file_geo_service_proto_geo_service_proto_rawDescGZIP() []byte {
	file_geo_service_proto_geo_service_proto_rawDescOnce.Do(func() {
		file_geo_service_proto_geo_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_geo_service_proto_geo_service_proto_rawDescData)
	})
	return file_geo_service_proto_geo_service_proto_rawDescData
}

var file_geo_service_proto_geo_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_geo_service_proto_geo_service_proto_goTypes = []interface{}{
	(*GeoLL)(nil),           // 0: geo_service.GeoLL
	(*GeodFwdRequest)(nil),  // 1: geo_service.GeodFwdRequest
	(*GeodFwdResponse)(nil), // 2: geo_service.GeodFwdResponse
}
var file_geo_service_proto_geo_service_proto_depIdxs = []int32{
	0, // 0: geo_service.GeodFwdRequest.start:type_name -> geo_service.GeoLL
	0, // 1: geo_service.GeodFwdResponse.pos:type_name -> geo_service.GeoLL
	1, // 2: geo_service.GeoService.GeodFwd:input_type -> geo_service.GeodFwdRequest
	2, // 3: geo_service.GeoService.GeodFwd:output_type -> geo_service.GeodFwdResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_geo_service_proto_geo_service_proto_init() }
func file_geo_service_proto_geo_service_proto_init() {
	if File_geo_service_proto_geo_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_geo_service_proto_geo_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeoLL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_geo_service_proto_geo_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeodFwdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_geo_service_proto_geo_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeodFwdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_geo_service_proto_geo_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_geo_service_proto_geo_service_proto_goTypes,
		DependencyIndexes: file_geo_service_proto_geo_service_proto_depIdxs,
		MessageInfos:      file_geo_service_proto_geo_service_proto_msgTypes,
	}.Build()
	File_geo_service_proto_geo_service_proto = out.File
	file_geo_service_proto_geo_service_proto_rawDesc = nil
	file_geo_service_proto_geo_service_proto_goTypes = nil
	file_geo_service_proto_geo_service_proto_depIdxs = nil
}

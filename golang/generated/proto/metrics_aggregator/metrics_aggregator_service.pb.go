// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: metrics/proto/metrics_aggregator_service.proto

package metrics_aggregator

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	metrics "github.com/carbonrobotics/robot/golang/generated/proto/metrics"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{0}
}

func (x *PingRequest) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingResponse) Reset() {
	*x = PingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingResponse) ProtoMessage() {}

func (x *PingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingResponse.ProtoReflect.Descriptor instead.
func (*PingResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{1}
}

func (x *PingResponse) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type Metrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metrics map[string]string `protobuf:"bytes,1,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Metrics) Reset() {
	*x = Metrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metrics) ProtoMessage() {}

func (x *Metrics) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metrics.ProtoReflect.Descriptor instead.
func (*Metrics) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{2}
}

func (x *Metrics) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type GetMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetMetricsRequest) Reset() {
	*x = GetMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetricsRequest) ProtoMessage() {}

func (x *GetMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetMetricsRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{3}
}

type GetMetricsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DailyMetrics map[string]*Metrics `protobuf:"bytes,1,rep,name=daily_metrics,json=dailyMetrics,proto3" json:"daily_metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetMetricsResponse) Reset() {
	*x = GetMetricsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetricsResponse) ProtoMessage() {}

func (x *GetMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetMetricsResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetMetricsResponse) GetDailyMetrics() map[string]*Metrics {
	if x != nil {
		return x.DailyMetrics
	}
	return nil
}

type AcknowledgeDailyMetricRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Days []string `protobuf:"bytes,1,rep,name=days,proto3" json:"days,omitempty"`
}

func (x *AcknowledgeDailyMetricRequest) Reset() {
	*x = AcknowledgeDailyMetricRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcknowledgeDailyMetricRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcknowledgeDailyMetricRequest) ProtoMessage() {}

func (x *AcknowledgeDailyMetricRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcknowledgeDailyMetricRequest.ProtoReflect.Descriptor instead.
func (*AcknowledgeDailyMetricRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{5}
}

func (x *AcknowledgeDailyMetricRequest) GetDays() []string {
	if x != nil {
		return x.Days
	}
	return nil
}

type AcknowledgeDailyMetricResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AcknowledgeDailyMetricResponse) Reset() {
	*x = AcknowledgeDailyMetricResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcknowledgeDailyMetricResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcknowledgeDailyMetricResponse) ProtoMessage() {}

func (x *AcknowledgeDailyMetricResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcknowledgeDailyMetricResponse.ProtoReflect.Descriptor instead.
func (*AcknowledgeDailyMetricResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{6}
}

type GetJobMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetJobMetricsRequest) Reset() {
	*x = GetJobMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobMetricsRequest) ProtoMessage() {}

func (x *GetJobMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetJobMetricsRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{7}
}

type GetJobMetricsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobMetrics *Metrics `protobuf:"bytes,1,opt,name=jobMetrics,proto3" json:"jobMetrics,omitempty"`
}

func (x *GetJobMetricsResponse) Reset() {
	*x = GetJobMetricsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobMetricsResponse) ProtoMessage() {}

func (x *GetJobMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetJobMetricsResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetJobMetricsResponse) GetJobMetrics() *Metrics {
	if x != nil {
		return x.JobMetrics
	}
	return nil
}

type GetLaserLifeTimesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLaserLifeTimesRequest) Reset() {
	*x = GetLaserLifeTimesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLaserLifeTimesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLaserLifeTimesRequest) ProtoMessage() {}

func (x *GetLaserLifeTimesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLaserLifeTimesRequest.ProtoReflect.Descriptor instead.
func (*GetLaserLifeTimesRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{9}
}

type SetLaserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetLaserResponse) Reset() {
	*x = SetLaserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLaserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLaserResponse) ProtoMessage() {}

func (x *SetLaserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLaserResponse.ProtoReflect.Descriptor instead.
func (*SetLaserResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{10}
}

type GetLaserChangeTimesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLaserChangeTimesRequest) Reset() {
	*x = GetLaserChangeTimesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLaserChangeTimesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLaserChangeTimesRequest) ProtoMessage() {}

func (x *GetLaserChangeTimesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLaserChangeTimesRequest.ProtoReflect.Descriptor instead.
func (*GetLaserChangeTimesRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{11}
}

type RegisterSpatialClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RegisterSpatialClientRequest) Reset() {
	*x = RegisterSpatialClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterSpatialClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterSpatialClientRequest) ProtoMessage() {}

func (x *RegisterSpatialClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterSpatialClientRequest.ProtoReflect.Descriptor instead.
func (*RegisterSpatialClientRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{12}
}

type RegisterSpatialClientResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RegisterSpatialClientResponse) Reset() {
	*x = RegisterSpatialClientResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterSpatialClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterSpatialClientResponse) ProtoMessage() {}

func (x *RegisterSpatialClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterSpatialClientResponse.ProtoReflect.Descriptor instead.
func (*RegisterSpatialClientResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{13}
}

func (x *RegisterSpatialClientResponse) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SpatialClientBeatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SpatialClientBeatRequest) Reset() {
	*x = SpatialClientBeatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialClientBeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialClientBeatRequest) ProtoMessage() {}

func (x *SpatialClientBeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialClientBeatRequest.ProtoReflect.Descriptor instead.
func (*SpatialClientBeatRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{14}
}

func (x *SpatialClientBeatRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SpatialClientBeatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SpatialClientBeatResponse) Reset() {
	*x = SpatialClientBeatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialClientBeatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialClientBeatResponse) ProtoMessage() {}

func (x *SpatialClientBeatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialClientBeatResponse.ProtoReflect.Descriptor instead.
func (*SpatialClientBeatResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{15}
}

func (x *SpatialClientBeatResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SpatialClientAckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId uint32 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	BlockId  uint64 `protobuf:"varint,2,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
}

func (x *SpatialClientAckRequest) Reset() {
	*x = SpatialClientAckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialClientAckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialClientAckRequest) ProtoMessage() {}

func (x *SpatialClientAckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialClientAckRequest.ProtoReflect.Descriptor instead.
func (*SpatialClientAckRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{16}
}

func (x *SpatialClientAckRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SpatialClientAckRequest) GetBlockId() uint64 {
	if x != nil {
		return x.BlockId
	}
	return 0
}

type SpatialClientAckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SpatialClientAckResponse) Reset() {
	*x = SpatialClientAckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialClientAckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialClientAckResponse) ProtoMessage() {}

func (x *SpatialClientAckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialClientAckResponse.ProtoReflect.Descriptor instead.
func (*SpatialClientAckResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{17}
}

func (x *SpatialClientAckResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetNextSpatialBlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockId uint64 `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
}

func (x *GetNextSpatialBlockRequest) Reset() {
	*x = GetNextSpatialBlockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextSpatialBlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextSpatialBlockRequest) ProtoMessage() {}

func (x *GetNextSpatialBlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextSpatialBlockRequest.ProtoReflect.Descriptor instead.
func (*GetNextSpatialBlockRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetNextSpatialBlockRequest) GetBlockId() uint64 {
	if x != nil {
		return x.BlockId
	}
	return 0
}

type GetNextSpatialBlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockId uint64 `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
}

func (x *GetNextSpatialBlockResponse) Reset() {
	*x = GetNextSpatialBlockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextSpatialBlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextSpatialBlockResponse) ProtoMessage() {}

func (x *GetNextSpatialBlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextSpatialBlockResponse.ProtoReflect.Descriptor instead.
func (*GetNextSpatialBlockResponse) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetNextSpatialBlockResponse) GetBlockId() uint64 {
	if x != nil {
		return x.BlockId
	}
	return 0
}

type OverrideLaserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Laser     *metrics.LaserIdentifier `protobuf:"bytes,1,opt,name=laser,proto3" json:"laser,omitempty"`
	LifetimeS uint64                   `protobuf:"varint,2,opt,name=lifetime_s,json=lifetimeS,proto3" json:"lifetime_s,omitempty"`
}

func (x *OverrideLaserRequest) Reset() {
	*x = OverrideLaserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverrideLaserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideLaserRequest) ProtoMessage() {}

func (x *OverrideLaserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metrics_proto_metrics_aggregator_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideLaserRequest.ProtoReflect.Descriptor instead.
func (*OverrideLaserRequest) Descriptor() ([]byte, []int) {
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP(), []int{20}
}

func (x *OverrideLaserRequest) GetLaser() *metrics.LaserIdentifier {
	if x != nil {
		return x.Laser
	}
	return nil
}

func (x *OverrideLaserRequest) GetLifetimeS() uint64 {
	if x != nil {
		return x.LifetimeS
	}
	return 0
}

var File_metrics_proto_metrics_aggregator_service_proto protoreflect.FileDescriptor

var file_metrics_proto_metrics_aggregator_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x6f, 0x72, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x1b, 0x0a, 0x0b, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x1c,
	0x0a, 0x0c, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0c,
	0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x89, 0x01, 0x0a,
	0x07, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0x3a, 0x0a, 0x0c,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xd1, 0x01,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x0d, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x1a, 0x5c, 0x0a, 0x11, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x33, 0x0a, 0x1d, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x22, 0x20, 0x0a, 0x1e, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x54, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x6a, 0x6f, 0x62,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x6f, 0x72, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0a, 0x6a, 0x6f, 0x62, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73,
	0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73,
	0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x1e, 0x0a, 0x1c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x2f, 0x0a, 0x1d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2a, 0x0a, 0x18, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x35, 0x0a, 0x19, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x51, 0x0a, 0x17, 0x53, 0x70, 0x61, 0x74,
	0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x53,
	0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0x37, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x74,
	0x69, 0x61, 0x6c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x14, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x05,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x05, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6c, 0x69, 0x66, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x53, 0x32, 0x97, 0x0a, 0x0a, 0x18, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x4b, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x25, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x16,
	0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x31, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x63, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x41,
	0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x64, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x12, 0x28, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x2c, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4c,
	0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x08, 0x53, 0x65,
	0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a, 0x24, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x61, 0x0a, 0x0d, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x12, 0x28, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x69, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x00, 0x12, 0x7e, 0x0a,
	0x15, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x72, 0x0a,
	0x11, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x65,
	0x61, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6f, 0x0a, 0x10, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x41, 0x63, 0x6b, 0x12, 0x2b, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f,
	0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69,
	0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x78, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61,
	0x74, 0x69, 0x61, 0x6c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2e, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x1a, 0x5a, 0x18,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_metrics_proto_metrics_aggregator_service_proto_rawDescOnce sync.Once
	file_metrics_proto_metrics_aggregator_service_proto_rawDescData = file_metrics_proto_metrics_aggregator_service_proto_rawDesc
)

func file_metrics_proto_metrics_aggregator_service_proto_rawDescGZIP() []byte {
	file_metrics_proto_metrics_aggregator_service_proto_rawDescOnce.Do(func() {
		file_metrics_proto_metrics_aggregator_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_metrics_proto_metrics_aggregator_service_proto_rawDescData)
	})
	return file_metrics_proto_metrics_aggregator_service_proto_rawDescData
}

var file_metrics_proto_metrics_aggregator_service_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_metrics_proto_metrics_aggregator_service_proto_goTypes = []interface{}{
	(*PingRequest)(nil),                    // 0: metrics_aggregator.PingRequest
	(*PingResponse)(nil),                   // 1: metrics_aggregator.PingResponse
	(*Metrics)(nil),                        // 2: metrics_aggregator.Metrics
	(*GetMetricsRequest)(nil),              // 3: metrics_aggregator.GetMetricsRequest
	(*GetMetricsResponse)(nil),             // 4: metrics_aggregator.GetMetricsResponse
	(*AcknowledgeDailyMetricRequest)(nil),  // 5: metrics_aggregator.AcknowledgeDailyMetricRequest
	(*AcknowledgeDailyMetricResponse)(nil), // 6: metrics_aggregator.AcknowledgeDailyMetricResponse
	(*GetJobMetricsRequest)(nil),           // 7: metrics_aggregator.GetJobMetricsRequest
	(*GetJobMetricsResponse)(nil),          // 8: metrics_aggregator.GetJobMetricsResponse
	(*GetLaserLifeTimesRequest)(nil),       // 9: metrics_aggregator.GetLaserLifeTimesRequest
	(*SetLaserResponse)(nil),               // 10: metrics_aggregator.SetLaserResponse
	(*GetLaserChangeTimesRequest)(nil),     // 11: metrics_aggregator.GetLaserChangeTimesRequest
	(*RegisterSpatialClientRequest)(nil),   // 12: metrics_aggregator.RegisterSpatialClientRequest
	(*RegisterSpatialClientResponse)(nil),  // 13: metrics_aggregator.RegisterSpatialClientResponse
	(*SpatialClientBeatRequest)(nil),       // 14: metrics_aggregator.SpatialClientBeatRequest
	(*SpatialClientBeatResponse)(nil),      // 15: metrics_aggregator.SpatialClientBeatResponse
	(*SpatialClientAckRequest)(nil),        // 16: metrics_aggregator.SpatialClientAckRequest
	(*SpatialClientAckResponse)(nil),       // 17: metrics_aggregator.SpatialClientAckResponse
	(*GetNextSpatialBlockRequest)(nil),     // 18: metrics_aggregator.GetNextSpatialBlockRequest
	(*GetNextSpatialBlockResponse)(nil),    // 19: metrics_aggregator.GetNextSpatialBlockResponse
	(*OverrideLaserRequest)(nil),           // 20: metrics_aggregator.OverrideLaserRequest
	nil,                                    // 21: metrics_aggregator.Metrics.MetricsEntry
	nil,                                    // 22: metrics_aggregator.GetMetricsResponse.DailyMetricsEntry
	(*metrics.LaserIdentifier)(nil),        // 23: carbon.metrics.LaserIdentifier
	(*metrics.LaserLifeTimes)(nil),         // 24: carbon.metrics.LaserLifeTimes
	(*metrics.LaserChangeTimes)(nil),       // 25: carbon.metrics.LaserChangeTimes
}
var file_metrics_proto_metrics_aggregator_service_proto_depIdxs = []int32{
	21, // 0: metrics_aggregator.Metrics.metrics:type_name -> metrics_aggregator.Metrics.MetricsEntry
	22, // 1: metrics_aggregator.GetMetricsResponse.daily_metrics:type_name -> metrics_aggregator.GetMetricsResponse.DailyMetricsEntry
	2,  // 2: metrics_aggregator.GetJobMetricsResponse.jobMetrics:type_name -> metrics_aggregator.Metrics
	23, // 3: metrics_aggregator.OverrideLaserRequest.laser:type_name -> carbon.metrics.LaserIdentifier
	2,  // 4: metrics_aggregator.GetMetricsResponse.DailyMetricsEntry.value:type_name -> metrics_aggregator.Metrics
	0,  // 5: metrics_aggregator.MetricsAggregatorService.Ping:input_type -> metrics_aggregator.PingRequest
	3,  // 6: metrics_aggregator.MetricsAggregatorService.GetMetrics:input_type -> metrics_aggregator.GetMetricsRequest
	5,  // 7: metrics_aggregator.MetricsAggregatorService.AcknowledgeDailyMetric:input_type -> metrics_aggregator.AcknowledgeDailyMetricRequest
	7,  // 8: metrics_aggregator.MetricsAggregatorService.GetJobMetrics:input_type -> metrics_aggregator.GetJobMetricsRequest
	9,  // 9: metrics_aggregator.MetricsAggregatorService.GetLaserLifeTimes:input_type -> metrics_aggregator.GetLaserLifeTimesRequest
	23, // 10: metrics_aggregator.MetricsAggregatorService.SetLaser:input_type -> carbon.metrics.LaserIdentifier
	20, // 11: metrics_aggregator.MetricsAggregatorService.OverrideLaser:input_type -> metrics_aggregator.OverrideLaserRequest
	11, // 12: metrics_aggregator.MetricsAggregatorService.GetLaserChangeTimes:input_type -> metrics_aggregator.GetLaserChangeTimesRequest
	12, // 13: metrics_aggregator.MetricsAggregatorService.RegisterSpatialClient:input_type -> metrics_aggregator.RegisterSpatialClientRequest
	14, // 14: metrics_aggregator.MetricsAggregatorService.SpatialClientBeat:input_type -> metrics_aggregator.SpatialClientBeatRequest
	16, // 15: metrics_aggregator.MetricsAggregatorService.SpatialClientAck:input_type -> metrics_aggregator.SpatialClientAckRequest
	18, // 16: metrics_aggregator.MetricsAggregatorService.GetNextSpatialBlock:input_type -> metrics_aggregator.GetNextSpatialBlockRequest
	1,  // 17: metrics_aggregator.MetricsAggregatorService.Ping:output_type -> metrics_aggregator.PingResponse
	4,  // 18: metrics_aggregator.MetricsAggregatorService.GetMetrics:output_type -> metrics_aggregator.GetMetricsResponse
	6,  // 19: metrics_aggregator.MetricsAggregatorService.AcknowledgeDailyMetric:output_type -> metrics_aggregator.AcknowledgeDailyMetricResponse
	8,  // 20: metrics_aggregator.MetricsAggregatorService.GetJobMetrics:output_type -> metrics_aggregator.GetJobMetricsResponse
	24, // 21: metrics_aggregator.MetricsAggregatorService.GetLaserLifeTimes:output_type -> carbon.metrics.LaserLifeTimes
	10, // 22: metrics_aggregator.MetricsAggregatorService.SetLaser:output_type -> metrics_aggregator.SetLaserResponse
	10, // 23: metrics_aggregator.MetricsAggregatorService.OverrideLaser:output_type -> metrics_aggregator.SetLaserResponse
	25, // 24: metrics_aggregator.MetricsAggregatorService.GetLaserChangeTimes:output_type -> carbon.metrics.LaserChangeTimes
	13, // 25: metrics_aggregator.MetricsAggregatorService.RegisterSpatialClient:output_type -> metrics_aggregator.RegisterSpatialClientResponse
	15, // 26: metrics_aggregator.MetricsAggregatorService.SpatialClientBeat:output_type -> metrics_aggregator.SpatialClientBeatResponse
	17, // 27: metrics_aggregator.MetricsAggregatorService.SpatialClientAck:output_type -> metrics_aggregator.SpatialClientAckResponse
	19, // 28: metrics_aggregator.MetricsAggregatorService.GetNextSpatialBlock:output_type -> metrics_aggregator.GetNextSpatialBlockResponse
	17, // [17:29] is the sub-list for method output_type
	5,  // [5:17] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_metrics_proto_metrics_aggregator_service_proto_init() }
func file_metrics_proto_metrics_aggregator_service_proto_init() {
	if File_metrics_proto_metrics_aggregator_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMetricsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcknowledgeDailyMetricRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcknowledgeDailyMetricResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobMetricsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLaserLifeTimesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLaserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLaserChangeTimesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterSpatialClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterSpatialClientResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialClientBeatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialClientBeatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialClientAckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialClientAckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextSpatialBlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextSpatialBlockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metrics_proto_metrics_aggregator_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverrideLaserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metrics_proto_metrics_aggregator_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_metrics_proto_metrics_aggregator_service_proto_goTypes,
		DependencyIndexes: file_metrics_proto_metrics_aggregator_service_proto_depIdxs,
		MessageInfos:      file_metrics_proto_metrics_aggregator_service_proto_msgTypes,
	}.Build()
	File_metrics_proto_metrics_aggregator_service_proto = out.File
	file_metrics_proto_metrics_aggregator_service_proto_rawDesc = nil
	file_metrics_proto_metrics_aggregator_service_proto_goTypes = nil
	file_metrics_proto_metrics_aggregator_service_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: metrics/proto/metrics_aggregator_service.proto

package metrics_aggregator

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	metrics "github.com/carbonrobotics/robot/golang/generated/proto/metrics"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MetricsAggregatorService_Ping_FullMethodName                   = "/metrics_aggregator.MetricsAggregatorService/Ping"
	MetricsAggregatorService_GetMetrics_FullMethodName             = "/metrics_aggregator.MetricsAggregatorService/GetMetrics"
	MetricsAggregatorService_AcknowledgeDailyMetric_FullMethodName = "/metrics_aggregator.MetricsAggregatorService/AcknowledgeDailyMetric"
	MetricsAggregatorService_GetJobMetrics_FullMethodName          = "/metrics_aggregator.MetricsAggregatorService/GetJobMetrics"
	MetricsAggregatorService_GetLaserLifeTimes_FullMethodName      = "/metrics_aggregator.MetricsAggregatorService/GetLaserLifeTimes"
	MetricsAggregatorService_SetLaser_FullMethodName               = "/metrics_aggregator.MetricsAggregatorService/SetLaser"
	MetricsAggregatorService_OverrideLaser_FullMethodName          = "/metrics_aggregator.MetricsAggregatorService/OverrideLaser"
	MetricsAggregatorService_GetLaserChangeTimes_FullMethodName    = "/metrics_aggregator.MetricsAggregatorService/GetLaserChangeTimes"
	MetricsAggregatorService_RegisterSpatialClient_FullMethodName  = "/metrics_aggregator.MetricsAggregatorService/RegisterSpatialClient"
	MetricsAggregatorService_SpatialClientBeat_FullMethodName      = "/metrics_aggregator.MetricsAggregatorService/SpatialClientBeat"
	MetricsAggregatorService_SpatialClientAck_FullMethodName       = "/metrics_aggregator.MetricsAggregatorService/SpatialClientAck"
	MetricsAggregatorService_GetNextSpatialBlock_FullMethodName    = "/metrics_aggregator.MetricsAggregatorService/GetNextSpatialBlock"
)

// MetricsAggregatorServiceClient is the client API for MetricsAggregatorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MetricsAggregatorServiceClient interface {
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error)
	GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error)
	AcknowledgeDailyMetric(ctx context.Context, in *AcknowledgeDailyMetricRequest, opts ...grpc.CallOption) (*AcknowledgeDailyMetricResponse, error)
	GetJobMetrics(ctx context.Context, in *GetJobMetricsRequest, opts ...grpc.CallOption) (*GetJobMetricsResponse, error)
	GetLaserLifeTimes(ctx context.Context, in *GetLaserLifeTimesRequest, opts ...grpc.CallOption) (*metrics.LaserLifeTimes, error)
	SetLaser(ctx context.Context, in *metrics.LaserIdentifier, opts ...grpc.CallOption) (*SetLaserResponse, error)
	OverrideLaser(ctx context.Context, in *OverrideLaserRequest, opts ...grpc.CallOption) (*SetLaserResponse, error)
	GetLaserChangeTimes(ctx context.Context, in *GetLaserChangeTimesRequest, opts ...grpc.CallOption) (*metrics.LaserChangeTimes, error)
	RegisterSpatialClient(ctx context.Context, in *RegisterSpatialClientRequest, opts ...grpc.CallOption) (*RegisterSpatialClientResponse, error)
	SpatialClientBeat(ctx context.Context, in *SpatialClientBeatRequest, opts ...grpc.CallOption) (*SpatialClientBeatResponse, error)
	SpatialClientAck(ctx context.Context, in *SpatialClientAckRequest, opts ...grpc.CallOption) (*SpatialClientAckResponse, error)
	GetNextSpatialBlock(ctx context.Context, in *GetNextSpatialBlockRequest, opts ...grpc.CallOption) (*GetNextSpatialBlockResponse, error)
}

type metricsAggregatorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMetricsAggregatorServiceClient(cc grpc.ClientConnInterface) MetricsAggregatorServiceClient {
	return &metricsAggregatorServiceClient{cc}
}

func (c *metricsAggregatorServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error) {
	out := new(PingResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error) {
	out := new(GetMetricsResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_GetMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) AcknowledgeDailyMetric(ctx context.Context, in *AcknowledgeDailyMetricRequest, opts ...grpc.CallOption) (*AcknowledgeDailyMetricResponse, error) {
	out := new(AcknowledgeDailyMetricResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_AcknowledgeDailyMetric_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) GetJobMetrics(ctx context.Context, in *GetJobMetricsRequest, opts ...grpc.CallOption) (*GetJobMetricsResponse, error) {
	out := new(GetJobMetricsResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_GetJobMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) GetLaserLifeTimes(ctx context.Context, in *GetLaserLifeTimesRequest, opts ...grpc.CallOption) (*metrics.LaserLifeTimes, error) {
	out := new(metrics.LaserLifeTimes)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_GetLaserLifeTimes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) SetLaser(ctx context.Context, in *metrics.LaserIdentifier, opts ...grpc.CallOption) (*SetLaserResponse, error) {
	out := new(SetLaserResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_SetLaser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) OverrideLaser(ctx context.Context, in *OverrideLaserRequest, opts ...grpc.CallOption) (*SetLaserResponse, error) {
	out := new(SetLaserResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_OverrideLaser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) GetLaserChangeTimes(ctx context.Context, in *GetLaserChangeTimesRequest, opts ...grpc.CallOption) (*metrics.LaserChangeTimes, error) {
	out := new(metrics.LaserChangeTimes)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_GetLaserChangeTimes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) RegisterSpatialClient(ctx context.Context, in *RegisterSpatialClientRequest, opts ...grpc.CallOption) (*RegisterSpatialClientResponse, error) {
	out := new(RegisterSpatialClientResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_RegisterSpatialClient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) SpatialClientBeat(ctx context.Context, in *SpatialClientBeatRequest, opts ...grpc.CallOption) (*SpatialClientBeatResponse, error) {
	out := new(SpatialClientBeatResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_SpatialClientBeat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) SpatialClientAck(ctx context.Context, in *SpatialClientAckRequest, opts ...grpc.CallOption) (*SpatialClientAckResponse, error) {
	out := new(SpatialClientAckResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_SpatialClientAck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricsAggregatorServiceClient) GetNextSpatialBlock(ctx context.Context, in *GetNextSpatialBlockRequest, opts ...grpc.CallOption) (*GetNextSpatialBlockResponse, error) {
	out := new(GetNextSpatialBlockResponse)
	err := c.cc.Invoke(ctx, MetricsAggregatorService_GetNextSpatialBlock_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetricsAggregatorServiceServer is the server API for MetricsAggregatorService service.
// All implementations must embed UnimplementedMetricsAggregatorServiceServer
// for forward compatibility
type MetricsAggregatorServiceServer interface {
	Ping(context.Context, *PingRequest) (*PingResponse, error)
	GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error)
	AcknowledgeDailyMetric(context.Context, *AcknowledgeDailyMetricRequest) (*AcknowledgeDailyMetricResponse, error)
	GetJobMetrics(context.Context, *GetJobMetricsRequest) (*GetJobMetricsResponse, error)
	GetLaserLifeTimes(context.Context, *GetLaserLifeTimesRequest) (*metrics.LaserLifeTimes, error)
	SetLaser(context.Context, *metrics.LaserIdentifier) (*SetLaserResponse, error)
	OverrideLaser(context.Context, *OverrideLaserRequest) (*SetLaserResponse, error)
	GetLaserChangeTimes(context.Context, *GetLaserChangeTimesRequest) (*metrics.LaserChangeTimes, error)
	RegisterSpatialClient(context.Context, *RegisterSpatialClientRequest) (*RegisterSpatialClientResponse, error)
	SpatialClientBeat(context.Context, *SpatialClientBeatRequest) (*SpatialClientBeatResponse, error)
	SpatialClientAck(context.Context, *SpatialClientAckRequest) (*SpatialClientAckResponse, error)
	GetNextSpatialBlock(context.Context, *GetNextSpatialBlockRequest) (*GetNextSpatialBlockResponse, error)
	mustEmbedUnimplementedMetricsAggregatorServiceServer()
}

// UnimplementedMetricsAggregatorServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMetricsAggregatorServiceServer struct {
}

func (UnimplementedMetricsAggregatorServiceServer) Ping(context.Context, *PingRequest) (*PingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetrics not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) AcknowledgeDailyMetric(context.Context, *AcknowledgeDailyMetricRequest) (*AcknowledgeDailyMetricResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcknowledgeDailyMetric not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) GetJobMetrics(context.Context, *GetJobMetricsRequest) (*GetJobMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobMetrics not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) GetLaserLifeTimes(context.Context, *GetLaserLifeTimesRequest) (*metrics.LaserLifeTimes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLaserLifeTimes not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) SetLaser(context.Context, *metrics.LaserIdentifier) (*SetLaserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLaser not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) OverrideLaser(context.Context, *OverrideLaserRequest) (*SetLaserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OverrideLaser not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) GetLaserChangeTimes(context.Context, *GetLaserChangeTimesRequest) (*metrics.LaserChangeTimes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLaserChangeTimes not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) RegisterSpatialClient(context.Context, *RegisterSpatialClientRequest) (*RegisterSpatialClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterSpatialClient not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) SpatialClientBeat(context.Context, *SpatialClientBeatRequest) (*SpatialClientBeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpatialClientBeat not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) SpatialClientAck(context.Context, *SpatialClientAckRequest) (*SpatialClientAckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpatialClientAck not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) GetNextSpatialBlock(context.Context, *GetNextSpatialBlockRequest) (*GetNextSpatialBlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextSpatialBlock not implemented")
}
func (UnimplementedMetricsAggregatorServiceServer) mustEmbedUnimplementedMetricsAggregatorServiceServer() {
}

// UnsafeMetricsAggregatorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetricsAggregatorServiceServer will
// result in compilation errors.
type UnsafeMetricsAggregatorServiceServer interface {
	mustEmbedUnimplementedMetricsAggregatorServiceServer()
}

func RegisterMetricsAggregatorServiceServer(s grpc.ServiceRegistrar, srv MetricsAggregatorServiceServer) {
	s.RegisterService(&MetricsAggregatorService_ServiceDesc, srv)
}

func _MetricsAggregatorService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_GetMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).GetMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_GetMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).GetMetrics(ctx, req.(*GetMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_AcknowledgeDailyMetric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcknowledgeDailyMetricRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).AcknowledgeDailyMetric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_AcknowledgeDailyMetric_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).AcknowledgeDailyMetric(ctx, req.(*AcknowledgeDailyMetricRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_GetJobMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).GetJobMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_GetJobMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).GetJobMetrics(ctx, req.(*GetJobMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_GetLaserLifeTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLaserLifeTimesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).GetLaserLifeTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_GetLaserLifeTimes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).GetLaserLifeTimes(ctx, req.(*GetLaserLifeTimesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_SetLaser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(metrics.LaserIdentifier)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).SetLaser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_SetLaser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).SetLaser(ctx, req.(*metrics.LaserIdentifier))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_OverrideLaser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverrideLaserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).OverrideLaser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_OverrideLaser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).OverrideLaser(ctx, req.(*OverrideLaserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_GetLaserChangeTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLaserChangeTimesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).GetLaserChangeTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_GetLaserChangeTimes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).GetLaserChangeTimes(ctx, req.(*GetLaserChangeTimesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_RegisterSpatialClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterSpatialClientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).RegisterSpatialClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_RegisterSpatialClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).RegisterSpatialClient(ctx, req.(*RegisterSpatialClientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_SpatialClientBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SpatialClientBeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).SpatialClientBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_SpatialClientBeat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).SpatialClientBeat(ctx, req.(*SpatialClientBeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_SpatialClientAck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SpatialClientAckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).SpatialClientAck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_SpatialClientAck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).SpatialClientAck(ctx, req.(*SpatialClientAckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricsAggregatorService_GetNextSpatialBlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextSpatialBlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAggregatorServiceServer).GetNextSpatialBlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAggregatorService_GetNextSpatialBlock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAggregatorServiceServer).GetNextSpatialBlock(ctx, req.(*GetNextSpatialBlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MetricsAggregatorService_ServiceDesc is the grpc.ServiceDesc for MetricsAggregatorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetricsAggregatorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "metrics_aggregator.MetricsAggregatorService",
	HandlerType: (*MetricsAggregatorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _MetricsAggregatorService_Ping_Handler,
		},
		{
			MethodName: "GetMetrics",
			Handler:    _MetricsAggregatorService_GetMetrics_Handler,
		},
		{
			MethodName: "AcknowledgeDailyMetric",
			Handler:    _MetricsAggregatorService_AcknowledgeDailyMetric_Handler,
		},
		{
			MethodName: "GetJobMetrics",
			Handler:    _MetricsAggregatorService_GetJobMetrics_Handler,
		},
		{
			MethodName: "GetLaserLifeTimes",
			Handler:    _MetricsAggregatorService_GetLaserLifeTimes_Handler,
		},
		{
			MethodName: "SetLaser",
			Handler:    _MetricsAggregatorService_SetLaser_Handler,
		},
		{
			MethodName: "OverrideLaser",
			Handler:    _MetricsAggregatorService_OverrideLaser_Handler,
		},
		{
			MethodName: "GetLaserChangeTimes",
			Handler:    _MetricsAggregatorService_GetLaserChangeTimes_Handler,
		},
		{
			MethodName: "RegisterSpatialClient",
			Handler:    _MetricsAggregatorService_RegisterSpatialClient_Handler,
		},
		{
			MethodName: "SpatialClientBeat",
			Handler:    _MetricsAggregatorService_SpatialClientBeat_Handler,
		},
		{
			MethodName: "SpatialClientAck",
			Handler:    _MetricsAggregatorService_SpatialClientAck_Handler,
		},
		{
			MethodName: "GetNextSpatialBlock",
			Handler:    _MetricsAggregatorService_GetNextSpatialBlock_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "metrics/proto/metrics_aggregator_service.proto",
}

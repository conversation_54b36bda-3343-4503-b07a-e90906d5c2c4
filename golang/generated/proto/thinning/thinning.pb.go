// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/thinning/thinning.proto

package thinning

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Box struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  float64 `protobuf:"fixed64,1,opt,name=width,proto3" json:"width,omitempty"`
	Height float64 `protobuf:"fixed64,2,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *Box) Reset() {
	*x = Box{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_thinning_thinning_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Box) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Box) ProtoMessage() {}

func (x *Box) ProtoReflect() protoreflect.Message {
	mi := &file_proto_thinning_thinning_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Box.ProtoReflect.Descriptor instead.
func (*Box) Descriptor() ([]byte, []int) {
	return file_proto_thinning_thinning_proto_rawDescGZIP(), []int{0}
}

func (x *Box) GetWidth() float64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Box) GetHeight() float64 {
	if x != nil {
		return x.Height
	}
	return 0
}

// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
type DoubleBox struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Box_1      *Box    `protobuf:"bytes,1,opt,name=box_1,json=box1,proto3" json:"box_1,omitempty"`
	Box_2      *Box    `protobuf:"bytes,2,opt,name=box_2,json=box2,proto3" json:"box_2,omitempty"`
	IdealYDist float64 `protobuf:"fixed64,3,opt,name=ideal_y_dist,json=idealYDist,proto3" json:"ideal_y_dist,omitempty"`
}

func (x *DoubleBox) Reset() {
	*x = DoubleBox{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_thinning_thinning_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleBox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleBox) ProtoMessage() {}

func (x *DoubleBox) ProtoReflect() protoreflect.Message {
	mi := &file_proto_thinning_thinning_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleBox.ProtoReflect.Descriptor instead.
func (*DoubleBox) Descriptor() ([]byte, []int) {
	return file_proto_thinning_thinning_proto_rawDescGZIP(), []int{1}
}

func (x *DoubleBox) GetBox_1() *Box {
	if x != nil {
		return x.Box_1
	}
	return nil
}

func (x *DoubleBox) GetBox_2() *Box {
	if x != nil {
		return x.Box_2
	}
	return nil
}

func (x *DoubleBox) GetIdealYDist() float64 {
	if x != nil {
		return x.IdealYDist
	}
	return 0
}

type SizedNotSoGreedyCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinKeepout       *Box    `protobuf:"bytes,1,opt,name=min_keepout,json=minKeepout,proto3" json:"min_keepout,omitempty"`
	MaxYSearchRadius float64 `protobuf:"fixed64,2,opt,name=max_y_search_radius,json=maxYSearchRadius,proto3" json:"max_y_search_radius,omitempty"`
	IdealYDist       float64 `protobuf:"fixed64,3,opt,name=ideal_y_dist,json=idealYDist,proto3" json:"ideal_y_dist,omitempty"`
	SizeWeight       float64 `protobuf:"fixed64,4,opt,name=size_weight,json=sizeWeight,proto3" json:"size_weight,omitempty"`
	DistWeight       float64 `protobuf:"fixed64,5,opt,name=dist_weight,json=distWeight,proto3" json:"dist_weight,omitempty"`
}

func (x *SizedNotSoGreedyCfg) Reset() {
	*x = SizedNotSoGreedyCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_thinning_thinning_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SizedNotSoGreedyCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SizedNotSoGreedyCfg) ProtoMessage() {}

func (x *SizedNotSoGreedyCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_thinning_thinning_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SizedNotSoGreedyCfg.ProtoReflect.Descriptor instead.
func (*SizedNotSoGreedyCfg) Descriptor() ([]byte, []int) {
	return file_proto_thinning_thinning_proto_rawDescGZIP(), []int{2}
}

func (x *SizedNotSoGreedyCfg) GetMinKeepout() *Box {
	if x != nil {
		return x.MinKeepout
	}
	return nil
}

func (x *SizedNotSoGreedyCfg) GetMaxYSearchRadius() float64 {
	if x != nil {
		return x.MaxYSearchRadius
	}
	return 0
}

func (x *SizedNotSoGreedyCfg) GetIdealYDist() float64 {
	if x != nil {
		return x.IdealYDist
	}
	return 0
}

func (x *SizedNotSoGreedyCfg) GetSizeWeight() float64 {
	if x != nil {
		return x.SizeWeight
	}
	return 0
}

func (x *SizedNotSoGreedyCfg) GetDistWeight() float64 {
	if x != nil {
		return x.DistWeight
	}
	return 0
}

type Bounds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Bounds:
	//
	//	*Bounds_Box
	//	*Bounds_DoubleBox
	//	*Bounds_SizedCfg
	Bounds isBounds_Bounds `protobuf_oneof:"bounds"`
}

func (x *Bounds) Reset() {
	*x = Bounds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_thinning_thinning_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bounds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bounds) ProtoMessage() {}

func (x *Bounds) ProtoReflect() protoreflect.Message {
	mi := &file_proto_thinning_thinning_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bounds.ProtoReflect.Descriptor instead.
func (*Bounds) Descriptor() ([]byte, []int) {
	return file_proto_thinning_thinning_proto_rawDescGZIP(), []int{3}
}

func (m *Bounds) GetBounds() isBounds_Bounds {
	if m != nil {
		return m.Bounds
	}
	return nil
}

func (x *Bounds) GetBox() *Box {
	if x, ok := x.GetBounds().(*Bounds_Box); ok {
		return x.Box
	}
	return nil
}

// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
func (x *Bounds) GetDoubleBox() *DoubleBox {
	if x, ok := x.GetBounds().(*Bounds_DoubleBox); ok {
		return x.DoubleBox
	}
	return nil
}

func (x *Bounds) GetSizedCfg() *SizedNotSoGreedyCfg {
	if x, ok := x.GetBounds().(*Bounds_SizedCfg); ok {
		return x.SizedCfg
	}
	return nil
}

type isBounds_Bounds interface {
	isBounds_Bounds()
}

type Bounds_Box struct {
	Box *Box `protobuf:"bytes,1,opt,name=box,proto3,oneof"`
}

type Bounds_DoubleBox struct {
	// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
	DoubleBox *DoubleBox `protobuf:"bytes,2,opt,name=double_box,json=doubleBox,proto3,oneof"`
}

type Bounds_SizedCfg struct {
	SizedCfg *SizedNotSoGreedyCfg `protobuf:"bytes,3,opt,name=sized_cfg,json=sizedCfg,proto3,oneof"`
}

func (*Bounds_Box) isBounds_Bounds() {}

func (*Bounds_DoubleBox) isBounds_Bounds() {}

func (*Bounds_SizedCfg) isBounds_Bounds() {}

// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
type SizeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled            bool    `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	SamplesSize        uint64  `protobuf:"varint,2,opt,name=samples_size,json=samplesSize,proto3" json:"samples_size,omitempty"`
	AcceptableVariance float64 `protobuf:"fixed64,3,opt,name=acceptable_variance,json=acceptableVariance,proto3" json:"acceptable_variance,omitempty"`
}

func (x *SizeFilter) Reset() {
	*x = SizeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_thinning_thinning_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SizeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SizeFilter) ProtoMessage() {}

func (x *SizeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_proto_thinning_thinning_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SizeFilter.ProtoReflect.Descriptor instead.
func (*SizeFilter) Descriptor() ([]byte, []int) {
	return file_proto_thinning_thinning_proto_rawDescGZIP(), []int{4}
}

func (x *SizeFilter) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *SizeFilter) GetSamplesSize() uint64 {
	if x != nil {
		return x.SamplesSize
	}
	return 0
}

func (x *SizeFilter) GetAcceptableVariance() float64 {
	if x != nil {
		return x.AcceptableVariance
	}
	return 0
}

type ConfigDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
	Active bool    `protobuf:"varint,2,opt,name=active,proto3" json:"active,omitempty"`
	Row_1  *Bounds `protobuf:"bytes,3,opt,name=row_1,json=row1,proto3" json:"row_1,omitempty"`
	Row_2  *Bounds `protobuf:"bytes,4,opt,name=row_2,json=row2,proto3" json:"row_2,omitempty"`
	Row_3  *Bounds `protobuf:"bytes,5,opt,name=row_3,json=row3,proto3" json:"row_3,omitempty"`
	// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
	SizeFilter *SizeFilter       `protobuf:"bytes,6,opt,name=size_filter,json=sizeFilter,proto3" json:"size_filter,omitempty"`
	Id         string            `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"` //Auto generated uuid if new
	Rows       map[int32]*Bounds `protobuf:"bytes,8,rep,name=rows,proto3" json:"rows,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ConfigDefinition) Reset() {
	*x = ConfigDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_thinning_thinning_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigDefinition) ProtoMessage() {}

func (x *ConfigDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_proto_thinning_thinning_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigDefinition.ProtoReflect.Descriptor instead.
func (*ConfigDefinition) Descriptor() ([]byte, []int) {
	return file_proto_thinning_thinning_proto_rawDescGZIP(), []int{5}
}

func (x *ConfigDefinition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
func (x *ConfigDefinition) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *ConfigDefinition) GetRow_1() *Bounds {
	if x != nil {
		return x.Row_1
	}
	return nil
}

func (x *ConfigDefinition) GetRow_2() *Bounds {
	if x != nil {
		return x.Row_2
	}
	return nil
}

func (x *ConfigDefinition) GetRow_3() *Bounds {
	if x != nil {
		return x.Row_3
	}
	return nil
}

// Deprecated: Marked as deprecated in proto/thinning/thinning.proto.
func (x *ConfigDefinition) GetSizeFilter() *SizeFilter {
	if x != nil {
		return x.SizeFilter
	}
	return nil
}

func (x *ConfigDefinition) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ConfigDefinition) GetRows() map[int32]*Bounds {
	if x != nil {
		return x.Rows
	}
	return nil
}

var File_proto_thinning_thinning_proto protoreflect.FileDescriptor

var file_proto_thinning_thinning_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x22, 0x33, 0x0a, 0x03, 0x42, 0x6f, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x87, 0x01, 0x0a, 0x09, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x42, 0x6f, 0x78, 0x12, 0x29, 0x0a, 0x05, 0x62, 0x6f, 0x78, 0x5f, 0x31, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x78, 0x52, 0x04, 0x62, 0x6f, 0x78, 0x31, 0x12, 0x29,
	0x0a, 0x05, 0x62, 0x6f, 0x78, 0x5f, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x42, 0x6f, 0x78, 0x52, 0x04, 0x62, 0x6f, 0x78, 0x32, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x64, 0x65,
	0x61, 0x6c, 0x5f, 0x79, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x69, 0x64, 0x65, 0x61, 0x6c, 0x59, 0x44, 0x69, 0x73, 0x74, 0x3a, 0x02, 0x18, 0x01, 0x22,
	0xdf, 0x01, 0x0a, 0x13, 0x53, 0x69, 0x7a, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x53, 0x6f, 0x47, 0x72,
	0x65, 0x65, 0x64, 0x79, 0x43, 0x66, 0x67, 0x12, 0x35, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x5f, 0x6b,
	0x65, 0x65, 0x70, 0x6f, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x6f, 0x78, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x4b, 0x65, 0x65, 0x70, 0x6f, 0x75, 0x74, 0x12, 0x2d,
	0x0a, 0x13, 0x6d, 0x61, 0x78, 0x5f, 0x79, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x6d, 0x61, 0x78,
	0x59, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x79, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x61, 0x6c, 0x59, 0x44, 0x69, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x73, 0x69, 0x7a, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x74, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x22, 0xc2, 0x01, 0x0a, 0x06, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x03,
	0x62, 0x6f, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x78, 0x48,
	0x00, 0x52, 0x03, 0x62, 0x6f, 0x78, 0x12, 0x3f, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x5f, 0x62, 0x6f, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x09, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x12, 0x43, 0x0a, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x64,
	0x5f, 0x63, 0x66, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x69, 0x7a,
	0x65, 0x64, 0x4e, 0x6f, 0x74, 0x53, 0x6f, 0x47, 0x72, 0x65, 0x65, 0x64, 0x79, 0x43, 0x66, 0x67,
	0x48, 0x00, 0x52, 0x08, 0x73, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x66, 0x67, 0x42, 0x08, 0x0a, 0x06,
	0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x22, 0x7e, 0x0a, 0x0a, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x2f, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x3a, 0x02, 0x18, 0x01, 0x22, 0xb1, 0x03, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x72,
	0x6f, 0x77, 0x5f, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x31, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x6f, 0x77,
	0x5f, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64,
	0x73, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x32, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x6f, 0x77, 0x5f, 0x33,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x52,
	0x04, 0x72, 0x6f, 0x77, 0x33, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x69, 0x7a,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x73, 0x69, 0x7a,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74,
	0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x77, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x1a, 0x50, 0x0a, 0x09, 0x52, 0x6f, 0x77, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x10, 0x5a, 0x0e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_thinning_thinning_proto_rawDescOnce sync.Once
	file_proto_thinning_thinning_proto_rawDescData = file_proto_thinning_thinning_proto_rawDesc
)

func file_proto_thinning_thinning_proto_rawDescGZIP() []byte {
	file_proto_thinning_thinning_proto_rawDescOnce.Do(func() {
		file_proto_thinning_thinning_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_thinning_thinning_proto_rawDescData)
	})
	return file_proto_thinning_thinning_proto_rawDescData
}

var file_proto_thinning_thinning_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proto_thinning_thinning_proto_goTypes = []interface{}{
	(*Box)(nil),                 // 0: carbon.thinning.Box
	(*DoubleBox)(nil),           // 1: carbon.thinning.DoubleBox
	(*SizedNotSoGreedyCfg)(nil), // 2: carbon.thinning.SizedNotSoGreedyCfg
	(*Bounds)(nil),              // 3: carbon.thinning.Bounds
	(*SizeFilter)(nil),          // 4: carbon.thinning.SizeFilter
	(*ConfigDefinition)(nil),    // 5: carbon.thinning.ConfigDefinition
	nil,                         // 6: carbon.thinning.ConfigDefinition.RowsEntry
}
var file_proto_thinning_thinning_proto_depIdxs = []int32{
	0,  // 0: carbon.thinning.DoubleBox.box_1:type_name -> carbon.thinning.Box
	0,  // 1: carbon.thinning.DoubleBox.box_2:type_name -> carbon.thinning.Box
	0,  // 2: carbon.thinning.SizedNotSoGreedyCfg.min_keepout:type_name -> carbon.thinning.Box
	0,  // 3: carbon.thinning.Bounds.box:type_name -> carbon.thinning.Box
	1,  // 4: carbon.thinning.Bounds.double_box:type_name -> carbon.thinning.DoubleBox
	2,  // 5: carbon.thinning.Bounds.sized_cfg:type_name -> carbon.thinning.SizedNotSoGreedyCfg
	3,  // 6: carbon.thinning.ConfigDefinition.row_1:type_name -> carbon.thinning.Bounds
	3,  // 7: carbon.thinning.ConfigDefinition.row_2:type_name -> carbon.thinning.Bounds
	3,  // 8: carbon.thinning.ConfigDefinition.row_3:type_name -> carbon.thinning.Bounds
	4,  // 9: carbon.thinning.ConfigDefinition.size_filter:type_name -> carbon.thinning.SizeFilter
	6,  // 10: carbon.thinning.ConfigDefinition.rows:type_name -> carbon.thinning.ConfigDefinition.RowsEntry
	3,  // 11: carbon.thinning.ConfigDefinition.RowsEntry.value:type_name -> carbon.thinning.Bounds
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_proto_thinning_thinning_proto_init() }
func file_proto_thinning_thinning_proto_init() {
	if File_proto_thinning_thinning_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_thinning_thinning_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Box); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_thinning_thinning_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleBox); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_thinning_thinning_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SizedNotSoGreedyCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_thinning_thinning_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bounds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_thinning_thinning_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SizeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_thinning_thinning_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_thinning_thinning_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*Bounds_Box)(nil),
		(*Bounds_DoubleBox)(nil),
		(*Bounds_SizedCfg)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_thinning_thinning_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_thinning_thinning_proto_goTypes,
		DependencyIndexes: file_proto_thinning_thinning_proto_depIdxs,
		MessageInfos:      file_proto_thinning_thinning_proto_msgTypes,
	}.Build()
	File_proto_thinning_thinning_proto = out.File
	file_proto_thinning_thinning_proto_rawDesc = nil
	file_proto_thinning_thinning_proto_goTypes = nil
	file_proto_thinning_thinning_proto_depIdxs = nil
}

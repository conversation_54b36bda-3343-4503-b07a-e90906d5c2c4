// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: golang/simulator/hardware/proto/sim/sim.proto

package sim

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SimulatorService_Ping_FullMethodName               = "/carbon.simulator.SimulatorService/Ping"
	SimulatorService_GetNextPredictions_FullMethodName = "/carbon.simulator.SimulatorService/GetNextPredictions"
)

// SimulatorServiceClient is the client API for SimulatorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SimulatorServiceClient interface {
	Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetNextPredictions(ctx context.Context, in *GetNextPredictionsRequest, opts ...grpc.CallOption) (*GetNextPredictionsResponse, error)
}

type simulatorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSimulatorServiceClient(cc grpc.ClientConnInterface) SimulatorServiceClient {
	return &simulatorServiceClient{cc}
}

func (c *simulatorServiceClient) Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SimulatorService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulatorServiceClient) GetNextPredictions(ctx context.Context, in *GetNextPredictionsRequest, opts ...grpc.CallOption) (*GetNextPredictionsResponse, error) {
	out := new(GetNextPredictionsResponse)
	err := c.cc.Invoke(ctx, SimulatorService_GetNextPredictions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SimulatorServiceServer is the server API for SimulatorService service.
// All implementations must embed UnimplementedSimulatorServiceServer
// for forward compatibility
type SimulatorServiceServer interface {
	Ping(context.Context, *Empty) (*Empty, error)
	GetNextPredictions(context.Context, *GetNextPredictionsRequest) (*GetNextPredictionsResponse, error)
	mustEmbedUnimplementedSimulatorServiceServer()
}

// UnimplementedSimulatorServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSimulatorServiceServer struct {
}

func (UnimplementedSimulatorServiceServer) Ping(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedSimulatorServiceServer) GetNextPredictions(context.Context, *GetNextPredictionsRequest) (*GetNextPredictionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextPredictions not implemented")
}
func (UnimplementedSimulatorServiceServer) mustEmbedUnimplementedSimulatorServiceServer() {}

// UnsafeSimulatorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SimulatorServiceServer will
// result in compilation errors.
type UnsafeSimulatorServiceServer interface {
	mustEmbedUnimplementedSimulatorServiceServer()
}

func RegisterSimulatorServiceServer(s grpc.ServiceRegistrar, srv SimulatorServiceServer) {
	s.RegisterService(&SimulatorService_ServiceDesc, srv)
}

func _SimulatorService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulatorServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulatorService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulatorServiceServer).Ping(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulatorService_GetNextPredictions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextPredictionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulatorServiceServer).GetNextPredictions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulatorService_GetNextPredictions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulatorServiceServer).GetNextPredictions(ctx, req.(*GetNextPredictionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SimulatorService_ServiceDesc is the grpc.ServiceDesc for SimulatorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SimulatorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.simulator.SimulatorService",
	HandlerType: (*SimulatorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _SimulatorService_Ping_Handler,
		},
		{
			MethodName: "GetNextPredictions",
			Handler:    _SimulatorService_GetNextPredictions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "golang/simulator/hardware/proto/sim/sim.proto",
}

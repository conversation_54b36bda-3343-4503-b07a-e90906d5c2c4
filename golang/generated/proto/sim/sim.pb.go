// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: golang/simulator/hardware/proto/sim/sim.proto

package sim

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_sim_proto_rawDescGZIP(), []int{0}
}

type GetNextPredictionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextPredictionsRequest) Reset() {
	*x = GetNextPredictionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPredictionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPredictionsRequest) ProtoMessage() {}

func (x *GetNextPredictionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPredictionsRequest.ProtoReflect.Descriptor instead.
func (*GetNextPredictionsRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_sim_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextPredictionsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetNextPredictionsRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type Prediction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XPx                     uint32             `protobuf:"varint,1,opt,name=x_px,json=xPx,proto3" json:"x_px,omitempty"`
	YPx                     uint32             `protobuf:"varint,2,opt,name=y_px,json=yPx,proto3" json:"y_px,omitempty"`
	SizePx                  uint32             `protobuf:"varint,3,opt,name=size_px,json=sizePx,proto3" json:"size_px,omitempty"`
	Score                   float32            `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	DetectionClasses        map[string]float32 `protobuf:"bytes,5,rep,name=detection_classes,json=detectionClasses,proto3" json:"detection_classes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	IsWeed                  bool               `protobuf:"varint,6,opt,name=is_weed,json=isWeed,proto3" json:"is_weed,omitempty"`
	IsReal                  bool               `protobuf:"varint,7,opt,name=is_real,json=isReal,proto3" json:"is_real,omitempty"`
	WeedScore               float32            `protobuf:"fixed32,8,opt,name=weed_score,json=weedScore,proto3" json:"weed_score,omitempty"`
	CropScore               float32            `protobuf:"fixed32,9,opt,name=crop_score,json=cropScore,proto3" json:"crop_score,omitempty"`
	Type                    string             `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	WeedDetectionClasses    map[string]float32 `protobuf:"bytes,11,rep,name=weed_detection_classes,json=weedDetectionClasses,proto3" json:"weed_detection_classes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	Embedding               []float32          `protobuf:"fixed32,12,rep,packed,name=embedding,proto3" json:"embedding,omitempty"`
	PlantScore              float32            `protobuf:"fixed32,13,opt,name=plant_score,json=plantScore,proto3" json:"plant_score,omitempty"`
	MaskIntersectionClasses []string           `protobuf:"bytes,14,rep,name=mask_intersection_classes,json=maskIntersectionClasses,proto3" json:"mask_intersection_classes,omitempty"`
}

func (x *Prediction) Reset() {
	*x = Prediction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Prediction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Prediction) ProtoMessage() {}

func (x *Prediction) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Prediction.ProtoReflect.Descriptor instead.
func (*Prediction) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_sim_proto_rawDescGZIP(), []int{2}
}

func (x *Prediction) GetXPx() uint32 {
	if x != nil {
		return x.XPx
	}
	return 0
}

func (x *Prediction) GetYPx() uint32 {
	if x != nil {
		return x.YPx
	}
	return 0
}

func (x *Prediction) GetSizePx() uint32 {
	if x != nil {
		return x.SizePx
	}
	return 0
}

func (x *Prediction) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Prediction) GetDetectionClasses() map[string]float32 {
	if x != nil {
		return x.DetectionClasses
	}
	return nil
}

func (x *Prediction) GetIsWeed() bool {
	if x != nil {
		return x.IsWeed
	}
	return false
}

func (x *Prediction) GetIsReal() bool {
	if x != nil {
		return x.IsReal
	}
	return false
}

func (x *Prediction) GetWeedScore() float32 {
	if x != nil {
		return x.WeedScore
	}
	return 0
}

func (x *Prediction) GetCropScore() float32 {
	if x != nil {
		return x.CropScore
	}
	return 0
}

func (x *Prediction) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Prediction) GetWeedDetectionClasses() map[string]float32 {
	if x != nil {
		return x.WeedDetectionClasses
	}
	return nil
}

func (x *Prediction) GetEmbedding() []float32 {
	if x != nil {
		return x.Embedding
	}
	return nil
}

func (x *Prediction) GetPlantScore() float32 {
	if x != nil {
		return x.PlantScore
	}
	return 0
}

func (x *Prediction) GetMaskIntersectionClasses() []string {
	if x != nil {
		return x.MaskIntersectionClasses
	}
	return nil
}

type GetNextPredictionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Predictions []*Prediction `protobuf:"bytes,1,rep,name=predictions,proto3" json:"predictions,omitempty"`
	TimestampMs int64         `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *GetNextPredictionsResponse) Reset() {
	*x = GetNextPredictionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextPredictionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextPredictionsResponse) ProtoMessage() {}

func (x *GetNextPredictionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextPredictionsResponse.ProtoReflect.Descriptor instead.
func (*GetNextPredictionsResponse) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_proto_sim_sim_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextPredictionsResponse) GetPredictions() []*Prediction {
	if x != nil {
		return x.Predictions
	}
	return nil
}

func (x *GetNextPredictionsResponse) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

var File_golang_simulator_hardware_proto_sim_sim_proto protoreflect.FileDescriptor

var file_golang_simulator_hardware_proto_sim_sim_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x2f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x73, 0x69, 0x6d, 0x2f, 0x73, 0x69, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x10, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x52, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0xbd,
	0x05, 0x0a, 0x0a, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x11, 0x0a,
	0x04, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x78, 0x50, 0x78,
	0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x70, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x79, 0x50, 0x78, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x70, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x69, 0x7a, 0x65, 0x50, 0x78, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x5f, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x10, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69,
	0x73, 0x52, 0x65, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x6c, 0x0a, 0x16, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x14, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x6d, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73,
	0x1a, 0x43, 0x0a, 0x15, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x47, 0x0a, 0x19, 0x57, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7f,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0b,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0b, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x32,
	0xbd, 0x01, 0x0a, 0x10, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x17, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6f,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x0b, 0x5a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x69, 0x6d, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_golang_simulator_hardware_proto_sim_sim_proto_rawDescOnce sync.Once
	file_golang_simulator_hardware_proto_sim_sim_proto_rawDescData = file_golang_simulator_hardware_proto_sim_sim_proto_rawDesc
)

func file_golang_simulator_hardware_proto_sim_sim_proto_rawDescGZIP() []byte {
	file_golang_simulator_hardware_proto_sim_sim_proto_rawDescOnce.Do(func() {
		file_golang_simulator_hardware_proto_sim_sim_proto_rawDescData = protoimpl.X.CompressGZIP(file_golang_simulator_hardware_proto_sim_sim_proto_rawDescData)
	})
	return file_golang_simulator_hardware_proto_sim_sim_proto_rawDescData
}

var file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_golang_simulator_hardware_proto_sim_sim_proto_goTypes = []interface{}{
	(*Empty)(nil),                      // 0: carbon.simulator.Empty
	(*GetNextPredictionsRequest)(nil),  // 1: carbon.simulator.GetNextPredictionsRequest
	(*Prediction)(nil),                 // 2: carbon.simulator.Prediction
	(*GetNextPredictionsResponse)(nil), // 3: carbon.simulator.GetNextPredictionsResponse
	nil,                                // 4: carbon.simulator.Prediction.DetectionClassesEntry
	nil,                                // 5: carbon.simulator.Prediction.WeedDetectionClassesEntry
}
var file_golang_simulator_hardware_proto_sim_sim_proto_depIdxs = []int32{
	4, // 0: carbon.simulator.Prediction.detection_classes:type_name -> carbon.simulator.Prediction.DetectionClassesEntry
	5, // 1: carbon.simulator.Prediction.weed_detection_classes:type_name -> carbon.simulator.Prediction.WeedDetectionClassesEntry
	2, // 2: carbon.simulator.GetNextPredictionsResponse.predictions:type_name -> carbon.simulator.Prediction
	0, // 3: carbon.simulator.SimulatorService.Ping:input_type -> carbon.simulator.Empty
	1, // 4: carbon.simulator.SimulatorService.GetNextPredictions:input_type -> carbon.simulator.GetNextPredictionsRequest
	0, // 5: carbon.simulator.SimulatorService.Ping:output_type -> carbon.simulator.Empty
	3, // 6: carbon.simulator.SimulatorService.GetNextPredictions:output_type -> carbon.simulator.GetNextPredictionsResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_golang_simulator_hardware_proto_sim_sim_proto_init() }
func file_golang_simulator_hardware_proto_sim_sim_proto_init() {
	if File_golang_simulator_hardware_proto_sim_sim_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPredictionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Prediction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextPredictionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_golang_simulator_hardware_proto_sim_sim_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_golang_simulator_hardware_proto_sim_sim_proto_goTypes,
		DependencyIndexes: file_golang_simulator_hardware_proto_sim_sim_proto_depIdxs,
		MessageInfos:      file_golang_simulator_hardware_proto_sim_sim_proto_msgTypes,
	}.Build()
	File_golang_simulator_hardware_proto_sim_sim_proto = out.File
	file_golang_simulator_hardware_proto_sim_sim_proto_rawDesc = nil
	file_golang_simulator_hardware_proto_sim_sim_proto_goTypes = nil
	file_golang_simulator_hardware_proto_sim_sim_proto_depIdxs = nil
}

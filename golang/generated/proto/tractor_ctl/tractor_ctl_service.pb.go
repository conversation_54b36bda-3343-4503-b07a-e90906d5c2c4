// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: tractor_ctl/proto/tractor_ctl_service.proto

package tractor_ctl

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PingMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingMsg) Reset() {
	*x = PingMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingMsg) ProtoMessage() {}

func (x *PingMsg) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingMsg.ProtoReflect.Descriptor instead.
func (*PingMsg) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{0}
}

func (x *PingMsg) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PongMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PongMsg) Reset() {
	*x = PongMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PongMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PongMsg) ProtoMessage() {}

func (x *PongMsg) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PongMsg.ProtoReflect.Descriptor instead.
func (*PongMsg) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{1}
}

func (x *PongMsg) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type ReloadWheelPosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadWheelPosRequest) Reset() {
	*x = ReloadWheelPosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadWheelPosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadWheelPosRequest) ProtoMessage() {}

func (x *ReloadWheelPosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadWheelPosRequest.ProtoReflect.Descriptor instead.
func (*ReloadWheelPosRequest) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{2}
}

type ReloadWheelPosResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadWheelPosResponse) Reset() {
	*x = ReloadWheelPosResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadWheelPosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadWheelPosResponse) ProtoMessage() {}

func (x *ReloadWheelPosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadWheelPosResponse.ProtoReflect.Descriptor instead.
func (*ReloadWheelPosResponse) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{3}
}

type GetSpeedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSpeedRequest) Reset() {
	*x = GetSpeedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpeedRequest) ProtoMessage() {}

func (x *GetSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpeedRequest.ProtoReflect.Descriptor instead.
func (*GetSpeedRequest) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{4}
}

type GetSpeedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpeedMph float32 `protobuf:"fixed32,1,opt,name=speed_mph,json=speedMph,proto3" json:"speed_mph,omitempty"`
}

func (x *GetSpeedResponse) Reset() {
	*x = GetSpeedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpeedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpeedResponse) ProtoMessage() {}

func (x *GetSpeedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpeedResponse.ProtoReflect.Descriptor instead.
func (*GetSpeedResponse) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetSpeedResponse) GetSpeedMph() float32 {
	if x != nil {
		return x.SpeedMph
	}
	return 0
}

type TractorStateWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg []byte `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *TractorStateWrapper) Reset() {
	*x = TractorStateWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorStateWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorStateWrapper) ProtoMessage() {}

func (x *TractorStateWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorStateWrapper.ProtoReflect.Descriptor instead.
func (*TractorStateWrapper) Descriptor() ([]byte, []int) {
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP(), []int{6}
}

func (x *TractorStateWrapper) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

var File_tractor_ctl_proto_tractor_ctl_service_proto protoreflect.FileDescriptor

var file_tractor_ctl_proto_tractor_ctl_service_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x17, 0x0a, 0x07, 0x50, 0x69, 0x6e, 0x67, 0x4d,
	0x73, 0x67, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78,
	0x22, 0x17, 0x0a, 0x07, 0x50, 0x6f, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x12, 0x0c, 0x0a, 0x01, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x17, 0x0a, 0x15, 0x52, 0x65, 0x6c,
	0x6f, 0x61, 0x64, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x18, 0x0a, 0x16, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x68, 0x65, 0x65,
	0x6c, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x11, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x2f, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x70, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4d, 0x70, 0x68,
	0x22, 0x27, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0x89, 0x03, 0x0a, 0x11, 0x54, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x74, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x34, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x1a, 0x14, 0x2e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x50, 0x6f, 0x6e, 0x67,
	0x4d, 0x73, 0x67, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x57,
	0x68, 0x65, 0x65, 0x6c, 0x50, 0x6f, 0x73, 0x12, 0x22, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x68, 0x65, 0x65,
	0x6c, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64,
	0x57, 0x68, 0x65, 0x65, 0x6c, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x49, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x1c,
	0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a,
	0x15, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x20,
	0x2e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x2e, 0x54, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x22, 0x00, 0x30, 0x01, 0x12, 0x3f, 0x0a, 0x0b, 0x46, 0x61, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x64, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x13, 0x5a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x74, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescOnce sync.Once
	file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescData = file_tractor_ctl_proto_tractor_ctl_service_proto_rawDesc
)

func file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescGZIP() []byte {
	file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescOnce.Do(func() {
		file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescData)
	})
	return file_tractor_ctl_proto_tractor_ctl_service_proto_rawDescData
}

var file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_tractor_ctl_proto_tractor_ctl_service_proto_goTypes = []interface{}{
	(*PingMsg)(nil),                // 0: tractor_ctl.PingMsg
	(*PongMsg)(nil),                // 1: tractor_ctl.PongMsg
	(*ReloadWheelPosRequest)(nil),  // 2: tractor_ctl.ReloadWheelPosRequest
	(*ReloadWheelPosResponse)(nil), // 3: tractor_ctl.ReloadWheelPosResponse
	(*GetSpeedRequest)(nil),        // 4: tractor_ctl.GetSpeedRequest
	(*GetSpeedResponse)(nil),       // 5: tractor_ctl.GetSpeedResponse
	(*TractorStateWrapper)(nil),    // 6: tractor_ctl.TractorStateWrapper
	(*emptypb.Empty)(nil),          // 7: google.protobuf.Empty
}
var file_tractor_ctl_proto_tractor_ctl_service_proto_depIdxs = []int32{
	0, // 0: tractor_ctl.TractorCtlService.Ping:input_type -> tractor_ctl.PingMsg
	2, // 1: tractor_ctl.TractorCtlService.ReloadWheelPos:input_type -> tractor_ctl.ReloadWheelPosRequest
	4, // 2: tractor_ctl.TractorCtlService.GetSpeed:input_type -> tractor_ctl.GetSpeedRequest
	7, // 3: tractor_ctl.TractorCtlService.SubscribeTractorState:input_type -> google.protobuf.Empty
	7, // 4: tractor_ctl.TractorCtlService.FarmChanged:input_type -> google.protobuf.Empty
	1, // 5: tractor_ctl.TractorCtlService.Ping:output_type -> tractor_ctl.PongMsg
	3, // 6: tractor_ctl.TractorCtlService.ReloadWheelPos:output_type -> tractor_ctl.ReloadWheelPosResponse
	5, // 7: tractor_ctl.TractorCtlService.GetSpeed:output_type -> tractor_ctl.GetSpeedResponse
	6, // 8: tractor_ctl.TractorCtlService.SubscribeTractorState:output_type -> tractor_ctl.TractorStateWrapper
	7, // 9: tractor_ctl.TractorCtlService.FarmChanged:output_type -> google.protobuf.Empty
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tractor_ctl_proto_tractor_ctl_service_proto_init() }
func file_tractor_ctl_proto_tractor_ctl_service_proto_init() {
	if File_tractor_ctl_proto_tractor_ctl_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PongMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadWheelPosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadWheelPosResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpeedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpeedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorStateWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tractor_ctl_proto_tractor_ctl_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tractor_ctl_proto_tractor_ctl_service_proto_goTypes,
		DependencyIndexes: file_tractor_ctl_proto_tractor_ctl_service_proto_depIdxs,
		MessageInfos:      file_tractor_ctl_proto_tractor_ctl_service_proto_msgTypes,
	}.Build()
	File_tractor_ctl_proto_tractor_ctl_service_proto = out.File
	file_tractor_ctl_proto_tractor_ctl_service_proto_rawDesc = nil
	file_tractor_ctl_proto_tractor_ctl_service_proto_goTypes = nil
	file_tractor_ctl_proto_tractor_ctl_service_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: tractor_ctl/proto/tractor_ctl_service.proto

package tractor_ctl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TractorCtlService_Ping_FullMethodName                  = "/tractor_ctl.TractorCtlService/Ping"
	TractorCtlService_ReloadWheelPos_FullMethodName        = "/tractor_ctl.TractorCtlService/ReloadWheelPos"
	TractorCtlService_GetSpeed_FullMethodName              = "/tractor_ctl.TractorCtlService/GetSpeed"
	TractorCtlService_SubscribeTractorState_FullMethodName = "/tractor_ctl.TractorCtlService/SubscribeTractorState"
	TractorCtlService_FarmChanged_FullMethodName           = "/tractor_ctl.TractorCtlService/FarmChanged"
)

// TractorCtlServiceClient is the client API for TractorCtlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TractorCtlServiceClient interface {
	Ping(ctx context.Context, in *PingMsg, opts ...grpc.CallOption) (*PongMsg, error)
	ReloadWheelPos(ctx context.Context, in *ReloadWheelPosRequest, opts ...grpc.CallOption) (*ReloadWheelPosResponse, error)
	GetSpeed(ctx context.Context, in *GetSpeedRequest, opts ...grpc.CallOption) (*GetSpeedResponse, error)
	SubscribeTractorState(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (TractorCtlService_SubscribeTractorStateClient, error)
	FarmChanged(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type tractorCtlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTractorCtlServiceClient(cc grpc.ClientConnInterface) TractorCtlServiceClient {
	return &tractorCtlServiceClient{cc}
}

func (c *tractorCtlServiceClient) Ping(ctx context.Context, in *PingMsg, opts ...grpc.CallOption) (*PongMsg, error) {
	out := new(PongMsg)
	err := c.cc.Invoke(ctx, TractorCtlService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tractorCtlServiceClient) ReloadWheelPos(ctx context.Context, in *ReloadWheelPosRequest, opts ...grpc.CallOption) (*ReloadWheelPosResponse, error) {
	out := new(ReloadWheelPosResponse)
	err := c.cc.Invoke(ctx, TractorCtlService_ReloadWheelPos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tractorCtlServiceClient) GetSpeed(ctx context.Context, in *GetSpeedRequest, opts ...grpc.CallOption) (*GetSpeedResponse, error) {
	out := new(GetSpeedResponse)
	err := c.cc.Invoke(ctx, TractorCtlService_GetSpeed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tractorCtlServiceClient) SubscribeTractorState(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (TractorCtlService_SubscribeTractorStateClient, error) {
	stream, err := c.cc.NewStream(ctx, &TractorCtlService_ServiceDesc.Streams[0], TractorCtlService_SubscribeTractorState_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &tractorCtlServiceSubscribeTractorStateClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type TractorCtlService_SubscribeTractorStateClient interface {
	Recv() (*TractorStateWrapper, error)
	grpc.ClientStream
}

type tractorCtlServiceSubscribeTractorStateClient struct {
	grpc.ClientStream
}

func (x *tractorCtlServiceSubscribeTractorStateClient) Recv() (*TractorStateWrapper, error) {
	m := new(TractorStateWrapper)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tractorCtlServiceClient) FarmChanged(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TractorCtlService_FarmChanged_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TractorCtlServiceServer is the server API for TractorCtlService service.
// All implementations must embed UnimplementedTractorCtlServiceServer
// for forward compatibility
type TractorCtlServiceServer interface {
	Ping(context.Context, *PingMsg) (*PongMsg, error)
	ReloadWheelPos(context.Context, *ReloadWheelPosRequest) (*ReloadWheelPosResponse, error)
	GetSpeed(context.Context, *GetSpeedRequest) (*GetSpeedResponse, error)
	SubscribeTractorState(*emptypb.Empty, TractorCtlService_SubscribeTractorStateServer) error
	FarmChanged(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedTractorCtlServiceServer()
}

// UnimplementedTractorCtlServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTractorCtlServiceServer struct {
}

func (UnimplementedTractorCtlServiceServer) Ping(context.Context, *PingMsg) (*PongMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedTractorCtlServiceServer) ReloadWheelPos(context.Context, *ReloadWheelPosRequest) (*ReloadWheelPosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadWheelPos not implemented")
}
func (UnimplementedTractorCtlServiceServer) GetSpeed(context.Context, *GetSpeedRequest) (*GetSpeedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpeed not implemented")
}
func (UnimplementedTractorCtlServiceServer) SubscribeTractorState(*emptypb.Empty, TractorCtlService_SubscribeTractorStateServer) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeTractorState not implemented")
}
func (UnimplementedTractorCtlServiceServer) FarmChanged(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FarmChanged not implemented")
}
func (UnimplementedTractorCtlServiceServer) mustEmbedUnimplementedTractorCtlServiceServer() {}

// UnsafeTractorCtlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TractorCtlServiceServer will
// result in compilation errors.
type UnsafeTractorCtlServiceServer interface {
	mustEmbedUnimplementedTractorCtlServiceServer()
}

func RegisterTractorCtlServiceServer(s grpc.ServiceRegistrar, srv TractorCtlServiceServer) {
	s.RegisterService(&TractorCtlService_ServiceDesc, srv)
}

func _TractorCtlService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorCtlServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorCtlService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorCtlServiceServer).Ping(ctx, req.(*PingMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _TractorCtlService_ReloadWheelPos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadWheelPosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorCtlServiceServer).ReloadWheelPos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorCtlService_ReloadWheelPos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorCtlServiceServer).ReloadWheelPos(ctx, req.(*ReloadWheelPosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TractorCtlService_GetSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorCtlServiceServer).GetSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorCtlService_GetSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorCtlServiceServer).GetSpeed(ctx, req.(*GetSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TractorCtlService_SubscribeTractorState_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(emptypb.Empty)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TractorCtlServiceServer).SubscribeTractorState(m, &tractorCtlServiceSubscribeTractorStateServer{stream})
}

type TractorCtlService_SubscribeTractorStateServer interface {
	Send(*TractorStateWrapper) error
	grpc.ServerStream
}

type tractorCtlServiceSubscribeTractorStateServer struct {
	grpc.ServerStream
}

func (x *tractorCtlServiceSubscribeTractorStateServer) Send(m *TractorStateWrapper) error {
	return x.ServerStream.SendMsg(m)
}

func _TractorCtlService_FarmChanged_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TractorCtlServiceServer).FarmChanged(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TractorCtlService_FarmChanged_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TractorCtlServiceServer).FarmChanged(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// TractorCtlService_ServiceDesc is the grpc.ServiceDesc for TractorCtlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TractorCtlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tractor_ctl.TractorCtlService",
	HandlerType: (*TractorCtlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _TractorCtlService_Ping_Handler,
		},
		{
			MethodName: "ReloadWheelPos",
			Handler:    _TractorCtlService_ReloadWheelPos_Handler,
		},
		{
			MethodName: "GetSpeed",
			Handler:    _TractorCtlService_GetSpeed_Handler,
		},
		{
			MethodName: "FarmChanged",
			Handler:    _TractorCtlService_FarmChanged_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SubscribeTractorState",
			Handler:       _TractorCtlService_SubscribeTractorState_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "tractor_ctl/proto/tractor_ctl_service.proto",
}

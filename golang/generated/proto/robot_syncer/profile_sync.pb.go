// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: robot_syncer/proto/profile_sync.proto

package robot_syncer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	almanac "github.com/carbonrobotics/robot/golang/generated/proto/almanac"
	category "github.com/carbonrobotics/robot/golang/generated/proto/category"
	frontend "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	portal "github.com/carbonrobotics/robot/golang/generated/proto/portal"
	target_velocity_estimator "github.com/carbonrobotics/robot/golang/generated/proto/target_velocity_estimator"
	thinning "github.com/carbonrobotics/robot/golang/generated/proto/thinning"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetProfileSyncDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial string `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
}

func (x *GetProfileSyncDataRequest) Reset() {
	*x = GetProfileSyncDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileSyncDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileSyncDataRequest) ProtoMessage() {}

func (x *GetProfileSyncDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileSyncDataRequest.ProtoReflect.Descriptor instead.
func (*GetProfileSyncDataRequest) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{0}
}

func (x *GetProfileSyncDataRequest) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

type GetProfileSyncDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profiles map[string]*frontend.ProfileSyncData `protobuf:"bytes,1,rep,name=profiles,proto3" json:"profiles,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetProfileSyncDataResponse) Reset() {
	*x = GetProfileSyncDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileSyncDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileSyncDataResponse) ProtoMessage() {}

func (x *GetProfileSyncDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileSyncDataResponse.ProtoReflect.Descriptor instead.
func (*GetProfileSyncDataResponse) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{1}
}

func (x *GetProfileSyncDataResponse) GetProfiles() map[string]*frontend.ProfileSyncData {
	if x != nil {
		return x.Profiles
	}
	return nil
}

type UploadProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastUpdateTimeMs int64  `protobuf:"varint,1,opt,name=last_update_time_ms,json=lastUpdateTimeMs,proto3" json:"last_update_time_ms,omitempty"`
	RobotSerial      string `protobuf:"bytes,2,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	// Types that are assignable to Profile:
	//
	//	*UploadProfileRequest_Almanac
	//	*UploadProfileRequest_Discriminator
	//	*UploadProfileRequest_Modelinator
	//	*UploadProfileRequest_Banding
	//	*UploadProfileRequest_Thinning
	//	*UploadProfileRequest_TargetVelocityEstimator
	//	*UploadProfileRequest_CategoryCollection
	//	*UploadProfileRequest_Category
	Profile isUploadProfileRequest_Profile `protobuf_oneof:"profile"`
}

func (x *UploadProfileRequest) Reset() {
	*x = UploadProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadProfileRequest) ProtoMessage() {}

func (x *UploadProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadProfileRequest.ProtoReflect.Descriptor instead.
func (*UploadProfileRequest) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{2}
}

func (x *UploadProfileRequest) GetLastUpdateTimeMs() int64 {
	if x != nil {
		return x.LastUpdateTimeMs
	}
	return 0
}

func (x *UploadProfileRequest) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (m *UploadProfileRequest) GetProfile() isUploadProfileRequest_Profile {
	if m != nil {
		return m.Profile
	}
	return nil
}

func (x *UploadProfileRequest) GetAlmanac() *almanac.AlmanacConfig {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Almanac); ok {
		return x.Almanac
	}
	return nil
}

func (x *UploadProfileRequest) GetDiscriminator() *almanac.DiscriminatorConfig {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Discriminator); ok {
		return x.Discriminator
	}
	return nil
}

func (x *UploadProfileRequest) GetModelinator() *almanac.ModelinatorConfig {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Modelinator); ok {
		return x.Modelinator
	}
	return nil
}

func (x *UploadProfileRequest) GetBanding() *frontend.BandingDef {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Banding); ok {
		return x.Banding
	}
	return nil
}

func (x *UploadProfileRequest) GetThinning() *thinning.ConfigDefinition {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Thinning); ok {
		return x.Thinning
	}
	return nil
}

func (x *UploadProfileRequest) GetTargetVelocityEstimator() *target_velocity_estimator.TVEProfile {
	if x, ok := x.GetProfile().(*UploadProfileRequest_TargetVelocityEstimator); ok {
		return x.TargetVelocityEstimator
	}
	return nil
}

func (x *UploadProfileRequest) GetCategoryCollection() *category.CategoryCollection {
	if x, ok := x.GetProfile().(*UploadProfileRequest_CategoryCollection); ok {
		return x.CategoryCollection
	}
	return nil
}

func (x *UploadProfileRequest) GetCategory() *category.Category {
	if x, ok := x.GetProfile().(*UploadProfileRequest_Category); ok {
		return x.Category
	}
	return nil
}

type isUploadProfileRequest_Profile interface {
	isUploadProfileRequest_Profile()
}

type UploadProfileRequest_Almanac struct {
	Almanac *almanac.AlmanacConfig `protobuf:"bytes,3,opt,name=almanac,proto3,oneof"`
}

type UploadProfileRequest_Discriminator struct {
	Discriminator *almanac.DiscriminatorConfig `protobuf:"bytes,4,opt,name=discriminator,proto3,oneof"`
}

type UploadProfileRequest_Modelinator struct {
	Modelinator *almanac.ModelinatorConfig `protobuf:"bytes,5,opt,name=modelinator,proto3,oneof"`
}

type UploadProfileRequest_Banding struct {
	Banding *frontend.BandingDef `protobuf:"bytes,6,opt,name=banding,proto3,oneof"`
}

type UploadProfileRequest_Thinning struct {
	Thinning *thinning.ConfigDefinition `protobuf:"bytes,7,opt,name=thinning,proto3,oneof"`
}

type UploadProfileRequest_TargetVelocityEstimator struct {
	TargetVelocityEstimator *target_velocity_estimator.TVEProfile `protobuf:"bytes,8,opt,name=target_velocity_estimator,json=targetVelocityEstimator,proto3,oneof"`
}

type UploadProfileRequest_CategoryCollection struct {
	CategoryCollection *category.CategoryCollection `protobuf:"bytes,9,opt,name=categoryCollection,proto3,oneof"`
}

type UploadProfileRequest_Category struct {
	Category *category.Category `protobuf:"bytes,10,opt,name=category,proto3,oneof"`
}

func (*UploadProfileRequest_Almanac) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Discriminator) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Modelinator) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Banding) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Thinning) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_TargetVelocityEstimator) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_CategoryCollection) isUploadProfileRequest_Profile() {}

func (*UploadProfileRequest_Category) isUploadProfileRequest_Profile() {}

type GetProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *GetProfileRequest) Reset() {
	*x = GetProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileRequest) ProtoMessage() {}

func (x *GetProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileRequest.ProtoReflect.Descriptor instead.
func (*GetProfileRequest) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{3}
}

func (x *GetProfileRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type GetProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Profile:
	//
	//	*GetProfileResponse_Almanac
	//	*GetProfileResponse_Discriminator
	//	*GetProfileResponse_Modelinator
	//	*GetProfileResponse_Banding
	//	*GetProfileResponse_Thinning
	//	*GetProfileResponse_TargetVelocityEstimator
	//	*GetProfileResponse_CategoryCollection
	//	*GetProfileResponse_Category
	Profile isGetProfileResponse_Profile `protobuf_oneof:"profile"`
}

func (x *GetProfileResponse) Reset() {
	*x = GetProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileResponse) ProtoMessage() {}

func (x *GetProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileResponse.ProtoReflect.Descriptor instead.
func (*GetProfileResponse) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{4}
}

func (m *GetProfileResponse) GetProfile() isGetProfileResponse_Profile {
	if m != nil {
		return m.Profile
	}
	return nil
}

func (x *GetProfileResponse) GetAlmanac() *almanac.AlmanacConfig {
	if x, ok := x.GetProfile().(*GetProfileResponse_Almanac); ok {
		return x.Almanac
	}
	return nil
}

func (x *GetProfileResponse) GetDiscriminator() *almanac.DiscriminatorConfig {
	if x, ok := x.GetProfile().(*GetProfileResponse_Discriminator); ok {
		return x.Discriminator
	}
	return nil
}

func (x *GetProfileResponse) GetModelinator() *almanac.ModelinatorConfig {
	if x, ok := x.GetProfile().(*GetProfileResponse_Modelinator); ok {
		return x.Modelinator
	}
	return nil
}

func (x *GetProfileResponse) GetBanding() *frontend.BandingDef {
	if x, ok := x.GetProfile().(*GetProfileResponse_Banding); ok {
		return x.Banding
	}
	return nil
}

func (x *GetProfileResponse) GetThinning() *thinning.ConfigDefinition {
	if x, ok := x.GetProfile().(*GetProfileResponse_Thinning); ok {
		return x.Thinning
	}
	return nil
}

func (x *GetProfileResponse) GetTargetVelocityEstimator() *target_velocity_estimator.TVEProfile {
	if x, ok := x.GetProfile().(*GetProfileResponse_TargetVelocityEstimator); ok {
		return x.TargetVelocityEstimator
	}
	return nil
}

func (x *GetProfileResponse) GetCategoryCollection() *category.CategoryCollection {
	if x, ok := x.GetProfile().(*GetProfileResponse_CategoryCollection); ok {
		return x.CategoryCollection
	}
	return nil
}

func (x *GetProfileResponse) GetCategory() *category.Category {
	if x, ok := x.GetProfile().(*GetProfileResponse_Category); ok {
		return x.Category
	}
	return nil
}

type isGetProfileResponse_Profile interface {
	isGetProfileResponse_Profile()
}

type GetProfileResponse_Almanac struct {
	Almanac *almanac.AlmanacConfig `protobuf:"bytes,1,opt,name=almanac,proto3,oneof"`
}

type GetProfileResponse_Discriminator struct {
	Discriminator *almanac.DiscriminatorConfig `protobuf:"bytes,2,opt,name=discriminator,proto3,oneof"`
}

type GetProfileResponse_Modelinator struct {
	Modelinator *almanac.ModelinatorConfig `protobuf:"bytes,3,opt,name=modelinator,proto3,oneof"`
}

type GetProfileResponse_Banding struct {
	Banding *frontend.BandingDef `protobuf:"bytes,4,opt,name=banding,proto3,oneof"`
}

type GetProfileResponse_Thinning struct {
	Thinning *thinning.ConfigDefinition `protobuf:"bytes,5,opt,name=thinning,proto3,oneof"`
}

type GetProfileResponse_TargetVelocityEstimator struct {
	TargetVelocityEstimator *target_velocity_estimator.TVEProfile `protobuf:"bytes,6,opt,name=target_velocity_estimator,json=targetVelocityEstimator,proto3,oneof"`
}

type GetProfileResponse_CategoryCollection struct {
	CategoryCollection *category.CategoryCollection `protobuf:"bytes,7,opt,name=categoryCollection,proto3,oneof"`
}

type GetProfileResponse_Category struct {
	Category *category.Category `protobuf:"bytes,8,opt,name=category,proto3,oneof"`
}

func (*GetProfileResponse_Almanac) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Discriminator) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Modelinator) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Banding) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Thinning) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_TargetVelocityEstimator) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_CategoryCollection) isGetProfileResponse_Profile() {}

func (*GetProfileResponse_Category) isGetProfileResponse_Profile() {}

type DeleteProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *DeleteProfileRequest) Reset() {
	*x = DeleteProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProfileRequest) ProtoMessage() {}

func (x *DeleteProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProfileRequest.ProtoReflect.Descriptor instead.
func (*DeleteProfileRequest) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteProfileRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type PurgeProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *PurgeProfileRequest) Reset() {
	*x = PurgeProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurgeProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurgeProfileRequest) ProtoMessage() {}

func (x *PurgeProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_proto_profile_sync_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurgeProfileRequest.ProtoReflect.Descriptor instead.
func (*PurgeProfileRequest) Descriptor() ([]byte, []int) {
	return file_robot_syncer_proto_profile_sync_proto_rawDescGZIP(), []int{6}
}

func (x *PurgeProfileRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

var File_robot_syncer_proto_profile_sync_proto protoreflect.FileDescriptor

var file_robot_syncer_proto_profile_sync_proto_rawDesc = []byte{
	0x0a, 0x25, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x1a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x21, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72,
	0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x3e, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22,
	0xf0, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79,
	0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79,
	0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x1a, 0x6a, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xdc, 0x05, 0x0a, 0x14, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x40, 0x0a,
	0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61,
	0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x12,
	0x52, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x4c, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x3f, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74, 0x68,
	0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x71, 0x0a, 0x19, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x17, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x45, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x55, 0x0a, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x48, 0x00, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x22, 0x27, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x88, 0x05, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x07, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x12, 0x52, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x63, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x4c, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x69,
	0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3f, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x08, 0x74,
	0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x71, 0x0a, 0x19, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48,
	0x00, 0x52, 0x17, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x55, 0x0a, 0x12, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x12, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x48, 0x00,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x2a, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x22, 0x29, 0x0a, 0x13, 0x50, 0x75, 0x72, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x32, 0xcd, 0x04, 0x0a,
	0x16, 0x52, 0x6f, 0x53, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e, 0x63,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e, 0x63,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x0d, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x77, 0x0a,
	0x0a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x0c, 0x50, 0x75,
	0x72, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x50, 0x75,
	0x72, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x14, 0x5a, 0x12,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63,
	0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_robot_syncer_proto_profile_sync_proto_rawDescOnce sync.Once
	file_robot_syncer_proto_profile_sync_proto_rawDescData = file_robot_syncer_proto_profile_sync_proto_rawDesc
)

func file_robot_syncer_proto_profile_sync_proto_rawDescGZIP() []byte {
	file_robot_syncer_proto_profile_sync_proto_rawDescOnce.Do(func() {
		file_robot_syncer_proto_profile_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_robot_syncer_proto_profile_sync_proto_rawDescData)
	})
	return file_robot_syncer_proto_profile_sync_proto_rawDescData
}

var file_robot_syncer_proto_profile_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_robot_syncer_proto_profile_sync_proto_goTypes = []interface{}{
	(*GetProfileSyncDataRequest)(nil),            // 0: carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest
	(*GetProfileSyncDataResponse)(nil),           // 1: carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse
	(*UploadProfileRequest)(nil),                 // 2: carbon.robot_syncer.profile_sync.UploadProfileRequest
	(*GetProfileRequest)(nil),                    // 3: carbon.robot_syncer.profile_sync.GetProfileRequest
	(*GetProfileResponse)(nil),                   // 4: carbon.robot_syncer.profile_sync.GetProfileResponse
	(*DeleteProfileRequest)(nil),                 // 5: carbon.robot_syncer.profile_sync.DeleteProfileRequest
	(*PurgeProfileRequest)(nil),                  // 6: carbon.robot_syncer.profile_sync.PurgeProfileRequest
	nil,                                          // 7: carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry
	(*almanac.AlmanacConfig)(nil),                // 8: carbon.aimbot.almanac.AlmanacConfig
	(*almanac.DiscriminatorConfig)(nil),          // 9: carbon.aimbot.almanac.DiscriminatorConfig
	(*almanac.ModelinatorConfig)(nil),            // 10: carbon.aimbot.almanac.ModelinatorConfig
	(*frontend.BandingDef)(nil),                  // 11: carbon.frontend.banding.BandingDef
	(*thinning.ConfigDefinition)(nil),            // 12: carbon.thinning.ConfigDefinition
	(*target_velocity_estimator.TVEProfile)(nil), // 13: carbon.aimbot.target_velocity_estimator.TVEProfile
	(*category.CategoryCollection)(nil),          // 14: carbon.category.CategoryCollection
	(*category.Category)(nil),                    // 15: carbon.category.Category
	(*frontend.ProfileSyncData)(nil),             // 16: carbon.frontend.profile_sync.ProfileSyncData
	(*portal.Empty)(nil),                         // 17: carbon.portal.util.Empty
}
var file_robot_syncer_proto_profile_sync_proto_depIdxs = []int32{
	7,  // 0: carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.profiles:type_name -> carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry
	8,  // 1: carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac:type_name -> carbon.aimbot.almanac.AlmanacConfig
	9,  // 2: carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator:type_name -> carbon.aimbot.almanac.DiscriminatorConfig
	10, // 3: carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	11, // 4: carbon.robot_syncer.profile_sync.UploadProfileRequest.banding:type_name -> carbon.frontend.banding.BandingDef
	12, // 5: carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning:type_name -> carbon.thinning.ConfigDefinition
	13, // 6: carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	14, // 7: carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection:type_name -> carbon.category.CategoryCollection
	15, // 8: carbon.robot_syncer.profile_sync.UploadProfileRequest.category:type_name -> carbon.category.Category
	8,  // 9: carbon.robot_syncer.profile_sync.GetProfileResponse.almanac:type_name -> carbon.aimbot.almanac.AlmanacConfig
	9,  // 10: carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator:type_name -> carbon.aimbot.almanac.DiscriminatorConfig
	10, // 11: carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator:type_name -> carbon.aimbot.almanac.ModelinatorConfig
	11, // 12: carbon.robot_syncer.profile_sync.GetProfileResponse.banding:type_name -> carbon.frontend.banding.BandingDef
	12, // 13: carbon.robot_syncer.profile_sync.GetProfileResponse.thinning:type_name -> carbon.thinning.ConfigDefinition
	13, // 14: carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile
	14, // 15: carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection:type_name -> carbon.category.CategoryCollection
	15, // 16: carbon.robot_syncer.profile_sync.GetProfileResponse.category:type_name -> carbon.category.Category
	16, // 17: carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry.value:type_name -> carbon.frontend.profile_sync.ProfileSyncData
	0,  // 18: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.GetProfileSyncData:input_type -> carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest
	2,  // 19: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.UploadProfile:input_type -> carbon.robot_syncer.profile_sync.UploadProfileRequest
	3,  // 20: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.GetProfile:input_type -> carbon.robot_syncer.profile_sync.GetProfileRequest
	5,  // 21: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.DeleteProfile:input_type -> carbon.robot_syncer.profile_sync.DeleteProfileRequest
	6,  // 22: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.PurgeProfile:input_type -> carbon.robot_syncer.profile_sync.PurgeProfileRequest
	1,  // 23: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.GetProfileSyncData:output_type -> carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse
	17, // 24: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.UploadProfile:output_type -> carbon.portal.util.Empty
	4,  // 25: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.GetProfile:output_type -> carbon.robot_syncer.profile_sync.GetProfileResponse
	17, // 26: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.DeleteProfile:output_type -> carbon.portal.util.Empty
	17, // 27: carbon.robot_syncer.profile_sync.RoSyProfileSyncService.PurgeProfile:output_type -> carbon.portal.util.Empty
	23, // [23:28] is the sub-list for method output_type
	18, // [18:23] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_robot_syncer_proto_profile_sync_proto_init() }
func file_robot_syncer_proto_profile_sync_proto_init() {
	if File_robot_syncer_proto_profile_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_robot_syncer_proto_profile_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileSyncDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_proto_profile_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileSyncDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_proto_profile_sync_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_proto_profile_sync_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_proto_profile_sync_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_proto_profile_sync_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_proto_profile_sync_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurgeProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_robot_syncer_proto_profile_sync_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*UploadProfileRequest_Almanac)(nil),
		(*UploadProfileRequest_Discriminator)(nil),
		(*UploadProfileRequest_Modelinator)(nil),
		(*UploadProfileRequest_Banding)(nil),
		(*UploadProfileRequest_Thinning)(nil),
		(*UploadProfileRequest_TargetVelocityEstimator)(nil),
		(*UploadProfileRequest_CategoryCollection)(nil),
		(*UploadProfileRequest_Category)(nil),
	}
	file_robot_syncer_proto_profile_sync_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetProfileResponse_Almanac)(nil),
		(*GetProfileResponse_Discriminator)(nil),
		(*GetProfileResponse_Modelinator)(nil),
		(*GetProfileResponse_Banding)(nil),
		(*GetProfileResponse_Thinning)(nil),
		(*GetProfileResponse_TargetVelocityEstimator)(nil),
		(*GetProfileResponse_CategoryCollection)(nil),
		(*GetProfileResponse_Category)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_robot_syncer_proto_profile_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_robot_syncer_proto_profile_sync_proto_goTypes,
		DependencyIndexes: file_robot_syncer_proto_profile_sync_proto_depIdxs,
		MessageInfos:      file_robot_syncer_proto_profile_sync_proto_msgTypes,
	}.Build()
	File_robot_syncer_proto_profile_sync_proto = out.File
	file_robot_syncer_proto_profile_sync_proto_rawDesc = nil
	file_robot_syncer_proto_profile_sync_proto_goTypes = nil
	file_robot_syncer_proto_profile_sync_proto_depIdxs = nil
}

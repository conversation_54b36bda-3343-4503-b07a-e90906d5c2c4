// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: robot_syncer/proto/profile_sync.proto

package robot_syncer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	portal "github.com/carbonrobotics/robot/golang/generated/proto/portal"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RoSyProfileSyncService_GetProfileSyncData_FullMethodName = "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData"
	RoSyProfileSyncService_UploadProfile_FullMethodName      = "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile"
	RoSyProfileSyncService_GetProfile_FullMethodName         = "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile"
	RoSyProfileSyncService_DeleteProfile_FullMethodName      = "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile"
	RoSyProfileSyncService_PurgeProfile_FullMethodName       = "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile"
)

// RoSyProfileSyncServiceClient is the client API for RoSyProfileSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoSyProfileSyncServiceClient interface {
	GetProfileSyncData(ctx context.Context, in *GetProfileSyncDataRequest, opts ...grpc.CallOption) (*GetProfileSyncDataResponse, error)
	UploadProfile(ctx context.Context, in *UploadProfileRequest, opts ...grpc.CallOption) (*portal.Empty, error)
	GetProfile(ctx context.Context, in *GetProfileRequest, opts ...grpc.CallOption) (*GetProfileResponse, error)
	DeleteProfile(ctx context.Context, in *DeleteProfileRequest, opts ...grpc.CallOption) (*portal.Empty, error)
	PurgeProfile(ctx context.Context, in *PurgeProfileRequest, opts ...grpc.CallOption) (*portal.Empty, error)
}

type roSyProfileSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRoSyProfileSyncServiceClient(cc grpc.ClientConnInterface) RoSyProfileSyncServiceClient {
	return &roSyProfileSyncServiceClient{cc}
}

func (c *roSyProfileSyncServiceClient) GetProfileSyncData(ctx context.Context, in *GetProfileSyncDataRequest, opts ...grpc.CallOption) (*GetProfileSyncDataResponse, error) {
	out := new(GetProfileSyncDataResponse)
	err := c.cc.Invoke(ctx, RoSyProfileSyncService_GetProfileSyncData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roSyProfileSyncServiceClient) UploadProfile(ctx context.Context, in *UploadProfileRequest, opts ...grpc.CallOption) (*portal.Empty, error) {
	out := new(portal.Empty)
	err := c.cc.Invoke(ctx, RoSyProfileSyncService_UploadProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roSyProfileSyncServiceClient) GetProfile(ctx context.Context, in *GetProfileRequest, opts ...grpc.CallOption) (*GetProfileResponse, error) {
	out := new(GetProfileResponse)
	err := c.cc.Invoke(ctx, RoSyProfileSyncService_GetProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roSyProfileSyncServiceClient) DeleteProfile(ctx context.Context, in *DeleteProfileRequest, opts ...grpc.CallOption) (*portal.Empty, error) {
	out := new(portal.Empty)
	err := c.cc.Invoke(ctx, RoSyProfileSyncService_DeleteProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roSyProfileSyncServiceClient) PurgeProfile(ctx context.Context, in *PurgeProfileRequest, opts ...grpc.CallOption) (*portal.Empty, error) {
	out := new(portal.Empty)
	err := c.cc.Invoke(ctx, RoSyProfileSyncService_PurgeProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoSyProfileSyncServiceServer is the server API for RoSyProfileSyncService service.
// All implementations must embed UnimplementedRoSyProfileSyncServiceServer
// for forward compatibility
type RoSyProfileSyncServiceServer interface {
	GetProfileSyncData(context.Context, *GetProfileSyncDataRequest) (*GetProfileSyncDataResponse, error)
	UploadProfile(context.Context, *UploadProfileRequest) (*portal.Empty, error)
	GetProfile(context.Context, *GetProfileRequest) (*GetProfileResponse, error)
	DeleteProfile(context.Context, *DeleteProfileRequest) (*portal.Empty, error)
	PurgeProfile(context.Context, *PurgeProfileRequest) (*portal.Empty, error)
	mustEmbedUnimplementedRoSyProfileSyncServiceServer()
}

// UnimplementedRoSyProfileSyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRoSyProfileSyncServiceServer struct {
}

func (UnimplementedRoSyProfileSyncServiceServer) GetProfileSyncData(context.Context, *GetProfileSyncDataRequest) (*GetProfileSyncDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfileSyncData not implemented")
}
func (UnimplementedRoSyProfileSyncServiceServer) UploadProfile(context.Context, *UploadProfileRequest) (*portal.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadProfile not implemented")
}
func (UnimplementedRoSyProfileSyncServiceServer) GetProfile(context.Context, *GetProfileRequest) (*GetProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfile not implemented")
}
func (UnimplementedRoSyProfileSyncServiceServer) DeleteProfile(context.Context, *DeleteProfileRequest) (*portal.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProfile not implemented")
}
func (UnimplementedRoSyProfileSyncServiceServer) PurgeProfile(context.Context, *PurgeProfileRequest) (*portal.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurgeProfile not implemented")
}
func (UnimplementedRoSyProfileSyncServiceServer) mustEmbedUnimplementedRoSyProfileSyncServiceServer() {
}

// UnsafeRoSyProfileSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoSyProfileSyncServiceServer will
// result in compilation errors.
type UnsafeRoSyProfileSyncServiceServer interface {
	mustEmbedUnimplementedRoSyProfileSyncServiceServer()
}

func RegisterRoSyProfileSyncServiceServer(s grpc.ServiceRegistrar, srv RoSyProfileSyncServiceServer) {
	s.RegisterService(&RoSyProfileSyncService_ServiceDesc, srv)
}

func _RoSyProfileSyncService_GetProfileSyncData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileSyncDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoSyProfileSyncServiceServer).GetProfileSyncData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoSyProfileSyncService_GetProfileSyncData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoSyProfileSyncServiceServer).GetProfileSyncData(ctx, req.(*GetProfileSyncDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoSyProfileSyncService_UploadProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoSyProfileSyncServiceServer).UploadProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoSyProfileSyncService_UploadProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoSyProfileSyncServiceServer).UploadProfile(ctx, req.(*UploadProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoSyProfileSyncService_GetProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoSyProfileSyncServiceServer).GetProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoSyProfileSyncService_GetProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoSyProfileSyncServiceServer).GetProfile(ctx, req.(*GetProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoSyProfileSyncService_DeleteProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoSyProfileSyncServiceServer).DeleteProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoSyProfileSyncService_DeleteProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoSyProfileSyncServiceServer).DeleteProfile(ctx, req.(*DeleteProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoSyProfileSyncService_PurgeProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurgeProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoSyProfileSyncServiceServer).PurgeProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoSyProfileSyncService_PurgeProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoSyProfileSyncServiceServer).PurgeProfile(ctx, req.(*PurgeProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RoSyProfileSyncService_ServiceDesc is the grpc.ServiceDesc for RoSyProfileSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RoSyProfileSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.robot_syncer.profile_sync.RoSyProfileSyncService",
	HandlerType: (*RoSyProfileSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProfileSyncData",
			Handler:    _RoSyProfileSyncService_GetProfileSyncData_Handler,
		},
		{
			MethodName: "UploadProfile",
			Handler:    _RoSyProfileSyncService_UploadProfile_Handler,
		},
		{
			MethodName: "GetProfile",
			Handler:    _RoSyProfileSyncService_GetProfile_Handler,
		},
		{
			MethodName: "DeleteProfile",
			Handler:    _RoSyProfileSyncService_DeleteProfile_Handler,
		},
		{
			MethodName: "PurgeProfile",
			Handler:    _RoSyProfileSyncService_PurgeProfile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "robot_syncer/proto/profile_sync.proto",
}

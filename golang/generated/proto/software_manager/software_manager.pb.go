// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/software_manager/software_manager.proto

package software_manager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SoftwareVersionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag        string   `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Containers []string `protobuf:"bytes,2,rep,name=containers,proto3" json:"containers,omitempty"`
	System     string   `protobuf:"bytes,3,opt,name=system,proto3" json:"system,omitempty"`
	Ready      bool     `protobuf:"varint,4,opt,name=ready,proto3" json:"ready,omitempty"`
	Available  bool     `protobuf:"varint,5,opt,name=available,proto3" json:"available,omitempty"`
}

func (x *SoftwareVersionMetadata) Reset() {
	*x = SoftwareVersionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersionMetadata) ProtoMessage() {}

func (x *SoftwareVersionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersionMetadata.ProtoReflect.Descriptor instead.
func (*SoftwareVersionMetadata) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{0}
}

func (x *SoftwareVersionMetadata) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *SoftwareVersionMetadata) GetContainers() []string {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *SoftwareVersionMetadata) GetSystem() string {
	if x != nil {
		return x.System
	}
	return ""
}

func (x *SoftwareVersionMetadata) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *SoftwareVersionMetadata) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

type SoftwareVersionMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag string `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *SoftwareVersionMetadataRequest) Reset() {
	*x = SoftwareVersionMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersionMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersionMetadataRequest) ProtoMessage() {}

func (x *SoftwareVersionMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersionMetadataRequest.ProtoReflect.Descriptor instead.
func (*SoftwareVersionMetadataRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{1}
}

func (x *SoftwareVersionMetadataRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type VersionSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VersionSummaryRequest) Reset() {
	*x = VersionSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionSummaryRequest) ProtoMessage() {}

func (x *VersionSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionSummaryRequest.ProtoReflect.Descriptor instead.
func (*VersionSummaryRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{2}
}

type VersionSummaryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current  *SoftwareVersionMetadata `protobuf:"bytes,1,opt,name=current,proto3" json:"current,omitempty"`
	Target   *SoftwareVersionMetadata `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	Previous *SoftwareVersionMetadata `protobuf:"bytes,3,opt,name=previous,proto3" json:"previous,omitempty"`
	Updating bool                     `protobuf:"varint,4,opt,name=updating,proto3" json:"updating,omitempty"`
}

func (x *VersionSummaryReply) Reset() {
	*x = VersionSummaryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionSummaryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionSummaryReply) ProtoMessage() {}

func (x *VersionSummaryReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionSummaryReply.ProtoReflect.Descriptor instead.
func (*VersionSummaryReply) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{3}
}

func (x *VersionSummaryReply) GetCurrent() *SoftwareVersionMetadata {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *VersionSummaryReply) GetTarget() *SoftwareVersionMetadata {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *VersionSummaryReply) GetPrevious() *SoftwareVersionMetadata {
	if x != nil {
		return x.Previous
	}
	return nil
}

func (x *VersionSummaryReply) GetUpdating() bool {
	if x != nil {
		return x.Updating
	}
	return false
}

type GetIdentityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetIdentityRequest) Reset() {
	*x = GetIdentityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityRequest) ProtoMessage() {}

func (x *GetIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{4}
}

type ComputerRole struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role       string   `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	Row        string   `protobuf:"bytes,2,opt,name=row,proto3" json:"row,omitempty"`
	ExtraRoles []string `protobuf:"bytes,3,rep,name=extra_roles,json=extraRoles,proto3" json:"extra_roles,omitempty"`
}

func (x *ComputerRole) Reset() {
	*x = ComputerRole{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputerRole) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputerRole) ProtoMessage() {}

func (x *ComputerRole) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputerRole.ProtoReflect.Descriptor instead.
func (*ComputerRole) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{5}
}

func (x *ComputerRole) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *ComputerRole) GetRow() string {
	if x != nil {
		return x.Row
	}
	return ""
}

func (x *ComputerRole) GetExtraRoles() []string {
	if x != nil {
		return x.ExtraRoles
	}
	return nil
}

type IdentityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Generation          string        `protobuf:"bytes,2,opt,name=generation,proto3" json:"generation,omitempty"`
	Role                *ComputerRole `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	AuthClientId        string        `protobuf:"bytes,4,opt,name=auth_client_id,json=authClientId,proto3" json:"auth_client_id,omitempty"`
	AuthClientSecret    string        `protobuf:"bytes,5,opt,name=auth_client_secret,json=authClientSecret,proto3" json:"auth_client_secret,omitempty"`
	AuthDomain          string        `protobuf:"bytes,6,opt,name=auth_domain,json=authDomain,proto3" json:"auth_domain,omitempty"`
	CarbonRobotUsername string        `protobuf:"bytes,7,opt,name=carbon_robot_username,json=carbonRobotUsername,proto3" json:"carbon_robot_username,omitempty"`
	CarbonRobotPassword string        `protobuf:"bytes,8,opt,name=carbon_robot_password,json=carbonRobotPassword,proto3" json:"carbon_robot_password,omitempty"`
	Environment         string        `protobuf:"bytes,9,opt,name=environment,proto3" json:"environment,omitempty"`
}

func (x *IdentityInfo) Reset() {
	*x = IdentityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityInfo) ProtoMessage() {}

func (x *IdentityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityInfo.ProtoReflect.Descriptor instead.
func (*IdentityInfo) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{6}
}

func (x *IdentityInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IdentityInfo) GetGeneration() string {
	if x != nil {
		return x.Generation
	}
	return ""
}

func (x *IdentityInfo) GetRole() *ComputerRole {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *IdentityInfo) GetAuthClientId() string {
	if x != nil {
		return x.AuthClientId
	}
	return ""
}

func (x *IdentityInfo) GetAuthClientSecret() string {
	if x != nil {
		return x.AuthClientSecret
	}
	return ""
}

func (x *IdentityInfo) GetAuthDomain() string {
	if x != nil {
		return x.AuthDomain
	}
	return ""
}

func (x *IdentityInfo) GetCarbonRobotUsername() string {
	if x != nil {
		return x.CarbonRobotUsername
	}
	return ""
}

func (x *IdentityInfo) GetCarbonRobotPassword() string {
	if x != nil {
		return x.CarbonRobotPassword
	}
	return ""
}

func (x *IdentityInfo) GetEnvironment() string {
	if x != nil {
		return x.Environment
	}
	return ""
}

type ClearPackagesCacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearPackagesCacheRequest) Reset() {
	*x = ClearPackagesCacheRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearPackagesCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearPackagesCacheRequest) ProtoMessage() {}

func (x *ClearPackagesCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearPackagesCacheRequest.ProtoReflect.Descriptor instead.
func (*ClearPackagesCacheRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{7}
}

type ClearPackagesCacheResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearPackagesCacheResponse) Reset() {
	*x = ClearPackagesCacheResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearPackagesCacheResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearPackagesCacheResponse) ProtoMessage() {}

func (x *ClearPackagesCacheResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearPackagesCacheResponse.ProtoReflect.Descriptor instead.
func (*ClearPackagesCacheResponse) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{8}
}

type PrepareUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag   string `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
}

func (x *PrepareUpdateRequest) Reset() {
	*x = PrepareUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrepareUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepareUpdateRequest) ProtoMessage() {}

func (x *PrepareUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepareUpdateRequest.ProtoReflect.Descriptor instead.
func (*PrepareUpdateRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{9}
}

func (x *PrepareUpdateRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *PrepareUpdateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

type PrepareUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrepareUpdateResponse) Reset() {
	*x = PrepareUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrepareUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepareUpdateResponse) ProtoMessage() {}

func (x *PrepareUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepareUpdateResponse.ProtoReflect.Descriptor instead.
func (*PrepareUpdateResponse) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{10}
}

type AbortUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag   string `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
}

func (x *AbortUpdateRequest) Reset() {
	*x = AbortUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbortUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbortUpdateRequest) ProtoMessage() {}

func (x *AbortUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbortUpdateRequest.ProtoReflect.Descriptor instead.
func (*AbortUpdateRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{11}
}

func (x *AbortUpdateRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *AbortUpdateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

type AbortUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AbortUpdateResponse) Reset() {
	*x = AbortUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbortUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbortUpdateResponse) ProtoMessage() {}

func (x *AbortUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbortUpdateResponse.ProtoReflect.Descriptor instead.
func (*AbortUpdateResponse) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{12}
}

type TriggerUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag   string `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
}

func (x *TriggerUpdateRequest) Reset() {
	*x = TriggerUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerUpdateRequest) ProtoMessage() {}

func (x *TriggerUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerUpdateRequest.ProtoReflect.Descriptor instead.
func (*TriggerUpdateRequest) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{13}
}

func (x *TriggerUpdateRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *TriggerUpdateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

type TriggerUpdateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TriggerUpdateReply) Reset() {
	*x = TriggerUpdateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerUpdateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerUpdateReply) ProtoMessage() {}

func (x *TriggerUpdateReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerUpdateReply.ProtoReflect.Descriptor instead.
func (*TriggerUpdateReply) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{14}
}

type SystemVersionStateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Confirmed bool `protobuf:"varint,1,opt,name=confirmed,proto3" json:"confirmed,omitempty"`
}

func (x *SystemVersionStateReply) Reset() {
	*x = SystemVersionStateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemVersionStateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemVersionStateReply) ProtoMessage() {}

func (x *SystemVersionStateReply) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemVersionStateReply.ProtoReflect.Descriptor instead.
func (*SystemVersionStateReply) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{15}
}

func (x *SystemVersionStateReply) GetConfirmed() bool {
	if x != nil {
		return x.Confirmed
	}
	return false
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_software_manager_software_manager_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_software_manager_software_manager_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_software_manager_software_manager_proto_rawDescGZIP(), []int{16}
}

var File_proto_software_manager_software_manager_proto protoreflect.FileDescriptor

var file_proto_software_manager_software_manager_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x17, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x17, 0x53, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72,
	0x65, 0x61, 0x64, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0x32, 0x0a, 0x1e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x22, 0x17, 0x0a, 0x15, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x95, 0x02, 0x0a, 0x13, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4a, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x4c, 0x0a,
	0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x55, 0x0a,
	0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x72, 0x6f, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x6f, 0x6c, 0x65, 0x73, 0x22, 0xfc, 0x02, 0x0a, 0x0c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75,
	0x74, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x75, 0x74, 0x68, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a,
	0x15, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x1c, 0x0a, 0x1a, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f,
	0x0a, 0x14, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x22,
	0x17, 0x0a, 0x15, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3d, 0x0a, 0x12, 0x41, 0x62, 0x6f, 0x72,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x41, 0x62, 0x6f, 0x72, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f,
	0x0a, 0x14, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x22,
	0x14, 0x0a, 0x12, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x0a, 0x17, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x22, 0x07,
	0x0a, 0x05, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x32, 0x8a, 0x09, 0x0a, 0x16, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x72, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x61, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12,
	0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x7d, 0x0a, 0x12, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x65, 0x70, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65,
	0x70, 0x61, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x68, 0x0a, 0x0b, 0x41, 0x62, 0x6f, 0x72, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x62, 0x6f, 0x72,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x62, 0x6f, 0x72, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x0d,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x48, 0x0a, 0x06, 0x52, 0x65, 0x62,
	0x6f, 0x6f, 0x74, 0x12, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x5b, 0x0a, 0x19, 0x50, 0x75, 0x73, 0x68, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x5a, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x46, 0x0a, 0x04,
	0x50, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x73, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x42, 0x18, 0x5a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_software_manager_software_manager_proto_rawDescOnce sync.Once
	file_proto_software_manager_software_manager_proto_rawDescData = file_proto_software_manager_software_manager_proto_rawDesc
)

func file_proto_software_manager_software_manager_proto_rawDescGZIP() []byte {
	file_proto_software_manager_software_manager_proto_rawDescOnce.Do(func() {
		file_proto_software_manager_software_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_software_manager_software_manager_proto_rawDescData)
	})
	return file_proto_software_manager_software_manager_proto_rawDescData
}

var file_proto_software_manager_software_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_proto_software_manager_software_manager_proto_goTypes = []interface{}{
	(*SoftwareVersionMetadata)(nil),        // 0: carbon.software_manager.SoftwareVersionMetadata
	(*SoftwareVersionMetadataRequest)(nil), // 1: carbon.software_manager.SoftwareVersionMetadataRequest
	(*VersionSummaryRequest)(nil),          // 2: carbon.software_manager.VersionSummaryRequest
	(*VersionSummaryReply)(nil),            // 3: carbon.software_manager.VersionSummaryReply
	(*GetIdentityRequest)(nil),             // 4: carbon.software_manager.GetIdentityRequest
	(*ComputerRole)(nil),                   // 5: carbon.software_manager.ComputerRole
	(*IdentityInfo)(nil),                   // 6: carbon.software_manager.IdentityInfo
	(*ClearPackagesCacheRequest)(nil),      // 7: carbon.software_manager.ClearPackagesCacheRequest
	(*ClearPackagesCacheResponse)(nil),     // 8: carbon.software_manager.ClearPackagesCacheResponse
	(*PrepareUpdateRequest)(nil),           // 9: carbon.software_manager.PrepareUpdateRequest
	(*PrepareUpdateResponse)(nil),          // 10: carbon.software_manager.PrepareUpdateResponse
	(*AbortUpdateRequest)(nil),             // 11: carbon.software_manager.AbortUpdateRequest
	(*AbortUpdateResponse)(nil),            // 12: carbon.software_manager.AbortUpdateResponse
	(*TriggerUpdateRequest)(nil),           // 13: carbon.software_manager.TriggerUpdateRequest
	(*TriggerUpdateReply)(nil),             // 14: carbon.software_manager.TriggerUpdateReply
	(*SystemVersionStateReply)(nil),        // 15: carbon.software_manager.SystemVersionStateReply
	(*Empty)(nil),                          // 16: carbon.software_manager.empty
}
var file_proto_software_manager_software_manager_proto_depIdxs = []int32{
	0,  // 0: carbon.software_manager.VersionSummaryReply.current:type_name -> carbon.software_manager.SoftwareVersionMetadata
	0,  // 1: carbon.software_manager.VersionSummaryReply.target:type_name -> carbon.software_manager.SoftwareVersionMetadata
	0,  // 2: carbon.software_manager.VersionSummaryReply.previous:type_name -> carbon.software_manager.SoftwareVersionMetadata
	5,  // 3: carbon.software_manager.IdentityInfo.role:type_name -> carbon.software_manager.ComputerRole
	1,  // 4: carbon.software_manager.SoftwareManagerService.GetSoftwareVersionMetadata:input_type -> carbon.software_manager.SoftwareVersionMetadataRequest
	2,  // 5: carbon.software_manager.SoftwareManagerService.GetVersionsSummary:input_type -> carbon.software_manager.VersionSummaryRequest
	4,  // 6: carbon.software_manager.SoftwareManagerService.GetIdentity:input_type -> carbon.software_manager.GetIdentityRequest
	7,  // 7: carbon.software_manager.SoftwareManagerService.ClearPackagesCache:input_type -> carbon.software_manager.ClearPackagesCacheRequest
	9,  // 8: carbon.software_manager.SoftwareManagerService.PrepareUpdate:input_type -> carbon.software_manager.PrepareUpdateRequest
	11, // 9: carbon.software_manager.SoftwareManagerService.AbortUpdate:input_type -> carbon.software_manager.AbortUpdateRequest
	13, // 10: carbon.software_manager.SoftwareManagerService.TriggerUpdate:input_type -> carbon.software_manager.TriggerUpdateRequest
	16, // 11: carbon.software_manager.SoftwareManagerService.Reboot:input_type -> carbon.software_manager.empty
	16, // 12: carbon.software_manager.SoftwareManagerService.PushRobotDefinitionUpdate:input_type -> carbon.software_manager.empty
	16, // 13: carbon.software_manager.SoftwareManagerService.RestartDependentServices:input_type -> carbon.software_manager.empty
	16, // 14: carbon.software_manager.SoftwareManagerService.Ping:input_type -> carbon.software_manager.empty
	0,  // 15: carbon.software_manager.SoftwareManagerService.GetSoftwareVersionMetadata:output_type -> carbon.software_manager.SoftwareVersionMetadata
	3,  // 16: carbon.software_manager.SoftwareManagerService.GetVersionsSummary:output_type -> carbon.software_manager.VersionSummaryReply
	6,  // 17: carbon.software_manager.SoftwareManagerService.GetIdentity:output_type -> carbon.software_manager.IdentityInfo
	8,  // 18: carbon.software_manager.SoftwareManagerService.ClearPackagesCache:output_type -> carbon.software_manager.ClearPackagesCacheResponse
	10, // 19: carbon.software_manager.SoftwareManagerService.PrepareUpdate:output_type -> carbon.software_manager.PrepareUpdateResponse
	12, // 20: carbon.software_manager.SoftwareManagerService.AbortUpdate:output_type -> carbon.software_manager.AbortUpdateResponse
	14, // 21: carbon.software_manager.SoftwareManagerService.TriggerUpdate:output_type -> carbon.software_manager.TriggerUpdateReply
	16, // 22: carbon.software_manager.SoftwareManagerService.Reboot:output_type -> carbon.software_manager.empty
	16, // 23: carbon.software_manager.SoftwareManagerService.PushRobotDefinitionUpdate:output_type -> carbon.software_manager.empty
	16, // 24: carbon.software_manager.SoftwareManagerService.RestartDependentServices:output_type -> carbon.software_manager.empty
	16, // 25: carbon.software_manager.SoftwareManagerService.Ping:output_type -> carbon.software_manager.empty
	15, // [15:26] is the sub-list for method output_type
	4,  // [4:15] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_proto_software_manager_software_manager_proto_init() }
func file_proto_software_manager_software_manager_proto_init() {
	if File_proto_software_manager_software_manager_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_software_manager_software_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersionMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionSummaryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputerRole); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearPackagesCacheRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearPackagesCacheResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrepareUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrepareUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbortUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbortUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerUpdateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemVersionStateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_software_manager_software_manager_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_software_manager_software_manager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_software_manager_software_manager_proto_goTypes,
		DependencyIndexes: file_proto_software_manager_software_manager_proto_depIdxs,
		MessageInfos:      file_proto_software_manager_software_manager_proto_msgTypes,
	}.Build()
	File_proto_software_manager_software_manager_proto = out.File
	file_proto_software_manager_software_manager_proto_rawDesc = nil
	file_proto_software_manager_software_manager_proto_goTypes = nil
	file_proto_software_manager_software_manager_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/software_manager/software_manager.proto

package software_manager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SoftwareManagerService_GetSoftwareVersionMetadata_FullMethodName = "/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata"
	SoftwareManagerService_GetVersionsSummary_FullMethodName         = "/carbon.software_manager.SoftwareManagerService/GetVersionsSummary"
	SoftwareManagerService_GetIdentity_FullMethodName                = "/carbon.software_manager.SoftwareManagerService/GetIdentity"
	SoftwareManagerService_ClearPackagesCache_FullMethodName         = "/carbon.software_manager.SoftwareManagerService/ClearPackagesCache"
	SoftwareManagerService_PrepareUpdate_FullMethodName              = "/carbon.software_manager.SoftwareManagerService/PrepareUpdate"
	SoftwareManagerService_AbortUpdate_FullMethodName                = "/carbon.software_manager.SoftwareManagerService/AbortUpdate"
	SoftwareManagerService_TriggerUpdate_FullMethodName              = "/carbon.software_manager.SoftwareManagerService/TriggerUpdate"
	SoftwareManagerService_Reboot_FullMethodName                     = "/carbon.software_manager.SoftwareManagerService/Reboot"
	SoftwareManagerService_PushRobotDefinitionUpdate_FullMethodName  = "/carbon.software_manager.SoftwareManagerService/PushRobotDefinitionUpdate"
	SoftwareManagerService_RestartDependentServices_FullMethodName   = "/carbon.software_manager.SoftwareManagerService/RestartDependentServices"
	SoftwareManagerService_Ping_FullMethodName                       = "/carbon.software_manager.SoftwareManagerService/Ping"
)

// SoftwareManagerServiceClient is the client API for SoftwareManagerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SoftwareManagerServiceClient interface {
	GetSoftwareVersionMetadata(ctx context.Context, in *SoftwareVersionMetadataRequest, opts ...grpc.CallOption) (*SoftwareVersionMetadata, error)
	GetVersionsSummary(ctx context.Context, in *VersionSummaryRequest, opts ...grpc.CallOption) (*VersionSummaryReply, error)
	GetIdentity(ctx context.Context, in *GetIdentityRequest, opts ...grpc.CallOption) (*IdentityInfo, error)
	ClearPackagesCache(ctx context.Context, in *ClearPackagesCacheRequest, opts ...grpc.CallOption) (*ClearPackagesCacheResponse, error)
	PrepareUpdate(ctx context.Context, in *PrepareUpdateRequest, opts ...grpc.CallOption) (*PrepareUpdateResponse, error)
	AbortUpdate(ctx context.Context, in *AbortUpdateRequest, opts ...grpc.CallOption) (*AbortUpdateResponse, error)
	TriggerUpdate(ctx context.Context, in *TriggerUpdateRequest, opts ...grpc.CallOption) (*TriggerUpdateReply, error)
	Reboot(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	PushRobotDefinitionUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	RestartDependentServices(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type softwareManagerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSoftwareManagerServiceClient(cc grpc.ClientConnInterface) SoftwareManagerServiceClient {
	return &softwareManagerServiceClient{cc}
}

func (c *softwareManagerServiceClient) GetSoftwareVersionMetadata(ctx context.Context, in *SoftwareVersionMetadataRequest, opts ...grpc.CallOption) (*SoftwareVersionMetadata, error) {
	out := new(SoftwareVersionMetadata)
	err := c.cc.Invoke(ctx, SoftwareManagerService_GetSoftwareVersionMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) GetVersionsSummary(ctx context.Context, in *VersionSummaryRequest, opts ...grpc.CallOption) (*VersionSummaryReply, error) {
	out := new(VersionSummaryReply)
	err := c.cc.Invoke(ctx, SoftwareManagerService_GetVersionsSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) GetIdentity(ctx context.Context, in *GetIdentityRequest, opts ...grpc.CallOption) (*IdentityInfo, error) {
	out := new(IdentityInfo)
	err := c.cc.Invoke(ctx, SoftwareManagerService_GetIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) ClearPackagesCache(ctx context.Context, in *ClearPackagesCacheRequest, opts ...grpc.CallOption) (*ClearPackagesCacheResponse, error) {
	out := new(ClearPackagesCacheResponse)
	err := c.cc.Invoke(ctx, SoftwareManagerService_ClearPackagesCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) PrepareUpdate(ctx context.Context, in *PrepareUpdateRequest, opts ...grpc.CallOption) (*PrepareUpdateResponse, error) {
	out := new(PrepareUpdateResponse)
	err := c.cc.Invoke(ctx, SoftwareManagerService_PrepareUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) AbortUpdate(ctx context.Context, in *AbortUpdateRequest, opts ...grpc.CallOption) (*AbortUpdateResponse, error) {
	out := new(AbortUpdateResponse)
	err := c.cc.Invoke(ctx, SoftwareManagerService_AbortUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) TriggerUpdate(ctx context.Context, in *TriggerUpdateRequest, opts ...grpc.CallOption) (*TriggerUpdateReply, error) {
	out := new(TriggerUpdateReply)
	err := c.cc.Invoke(ctx, SoftwareManagerService_TriggerUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) Reboot(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareManagerService_Reboot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) PushRobotDefinitionUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareManagerService_PushRobotDefinitionUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) RestartDependentServices(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareManagerService_RestartDependentServices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *softwareManagerServiceClient) Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SoftwareManagerService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SoftwareManagerServiceServer is the server API for SoftwareManagerService service.
// All implementations must embed UnimplementedSoftwareManagerServiceServer
// for forward compatibility
type SoftwareManagerServiceServer interface {
	GetSoftwareVersionMetadata(context.Context, *SoftwareVersionMetadataRequest) (*SoftwareVersionMetadata, error)
	GetVersionsSummary(context.Context, *VersionSummaryRequest) (*VersionSummaryReply, error)
	GetIdentity(context.Context, *GetIdentityRequest) (*IdentityInfo, error)
	ClearPackagesCache(context.Context, *ClearPackagesCacheRequest) (*ClearPackagesCacheResponse, error)
	PrepareUpdate(context.Context, *PrepareUpdateRequest) (*PrepareUpdateResponse, error)
	AbortUpdate(context.Context, *AbortUpdateRequest) (*AbortUpdateResponse, error)
	TriggerUpdate(context.Context, *TriggerUpdateRequest) (*TriggerUpdateReply, error)
	Reboot(context.Context, *Empty) (*Empty, error)
	PushRobotDefinitionUpdate(context.Context, *Empty) (*Empty, error)
	RestartDependentServices(context.Context, *Empty) (*Empty, error)
	Ping(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedSoftwareManagerServiceServer()
}

// UnimplementedSoftwareManagerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSoftwareManagerServiceServer struct {
}

func (UnimplementedSoftwareManagerServiceServer) GetSoftwareVersionMetadata(context.Context, *SoftwareVersionMetadataRequest) (*SoftwareVersionMetadata, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSoftwareVersionMetadata not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) GetVersionsSummary(context.Context, *VersionSummaryRequest) (*VersionSummaryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersionsSummary not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) GetIdentity(context.Context, *GetIdentityRequest) (*IdentityInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentity not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) ClearPackagesCache(context.Context, *ClearPackagesCacheRequest) (*ClearPackagesCacheResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearPackagesCache not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) PrepareUpdate(context.Context, *PrepareUpdateRequest) (*PrepareUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrepareUpdate not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) AbortUpdate(context.Context, *AbortUpdateRequest) (*AbortUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AbortUpdate not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) TriggerUpdate(context.Context, *TriggerUpdateRequest) (*TriggerUpdateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerUpdate not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) Reboot(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Reboot not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) PushRobotDefinitionUpdate(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushRobotDefinitionUpdate not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) RestartDependentServices(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestartDependentServices not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) Ping(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedSoftwareManagerServiceServer) mustEmbedUnimplementedSoftwareManagerServiceServer() {
}

// UnsafeSoftwareManagerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SoftwareManagerServiceServer will
// result in compilation errors.
type UnsafeSoftwareManagerServiceServer interface {
	mustEmbedUnimplementedSoftwareManagerServiceServer()
}

func RegisterSoftwareManagerServiceServer(s grpc.ServiceRegistrar, srv SoftwareManagerServiceServer) {
	s.RegisterService(&SoftwareManagerService_ServiceDesc, srv)
}

func _SoftwareManagerService_GetSoftwareVersionMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SoftwareVersionMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).GetSoftwareVersionMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_GetSoftwareVersionMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).GetSoftwareVersionMetadata(ctx, req.(*SoftwareVersionMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_GetVersionsSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VersionSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).GetVersionsSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_GetVersionsSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).GetVersionsSummary(ctx, req.(*VersionSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_GetIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).GetIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_GetIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).GetIdentity(ctx, req.(*GetIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_ClearPackagesCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearPackagesCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).ClearPackagesCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_ClearPackagesCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).ClearPackagesCache(ctx, req.(*ClearPackagesCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_PrepareUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrepareUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).PrepareUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_PrepareUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).PrepareUpdate(ctx, req.(*PrepareUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_AbortUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AbortUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).AbortUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_AbortUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).AbortUpdate(ctx, req.(*AbortUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_TriggerUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).TriggerUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_TriggerUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).TriggerUpdate(ctx, req.(*TriggerUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_Reboot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).Reboot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_Reboot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).Reboot(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_PushRobotDefinitionUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).PushRobotDefinitionUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_PushRobotDefinitionUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).PushRobotDefinitionUpdate(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_RestartDependentServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).RestartDependentServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_RestartDependentServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).RestartDependentServices(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _SoftwareManagerService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SoftwareManagerServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SoftwareManagerService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SoftwareManagerServiceServer).Ping(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// SoftwareManagerService_ServiceDesc is the grpc.ServiceDesc for SoftwareManagerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SoftwareManagerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.software_manager.SoftwareManagerService",
	HandlerType: (*SoftwareManagerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSoftwareVersionMetadata",
			Handler:    _SoftwareManagerService_GetSoftwareVersionMetadata_Handler,
		},
		{
			MethodName: "GetVersionsSummary",
			Handler:    _SoftwareManagerService_GetVersionsSummary_Handler,
		},
		{
			MethodName: "GetIdentity",
			Handler:    _SoftwareManagerService_GetIdentity_Handler,
		},
		{
			MethodName: "ClearPackagesCache",
			Handler:    _SoftwareManagerService_ClearPackagesCache_Handler,
		},
		{
			MethodName: "PrepareUpdate",
			Handler:    _SoftwareManagerService_PrepareUpdate_Handler,
		},
		{
			MethodName: "AbortUpdate",
			Handler:    _SoftwareManagerService_AbortUpdate_Handler,
		},
		{
			MethodName: "TriggerUpdate",
			Handler:    _SoftwareManagerService_TriggerUpdate_Handler,
		},
		{
			MethodName: "Reboot",
			Handler:    _SoftwareManagerService_Reboot_Handler,
		},
		{
			MethodName: "PushRobotDefinitionUpdate",
			Handler:    _SoftwareManagerService_PushRobotDefinitionUpdate_Handler,
		},
		{
			MethodName: "RestartDependentServices",
			Handler:    _SoftwareManagerService_RestartDependentServices_Handler,
		},
		{
			MethodName: "Ping",
			Handler:    _SoftwareManagerService_Ping_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/software_manager/software_manager.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/geo/geo.proto

// Geospatial types: these correspond to standard data types used across GIS
// systems. For example, see the GeoJSON spec for details:
// https://www.rfc-editor.org/rfc/rfc7946#section-3.1

package geo

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type of "fix", indicating the method by which a point was captured. This
// informs the precision of the measurement.
//
// These categories map to fix types for GGA messages under the NMEA 0183 spec,
// though the numeric enum values differ.
type FixType int32

const (
	FixType_FIX_TYPE_UNSPECIFIED FixType = 0
	FixType_NO_FIX               FixType = 1
	FixType_GNSS                 FixType = 2
	FixType_DIFFERENTIAL_GNSS    FixType = 3
	FixType_RTK_FIXED            FixType = 4
	FixType_RTK_FLOAT            FixType = 5
	FixType_DEAD_RECKONING       FixType = 6
)

// Enum value maps for FixType.
var (
	FixType_name = map[int32]string{
		0: "FIX_TYPE_UNSPECIFIED",
		1: "NO_FIX",
		2: "GNSS",
		3: "DIFFERENTIAL_GNSS",
		4: "RTK_FIXED",
		5: "RTK_FLOAT",
		6: "DEAD_RECKONING",
	}
	FixType_value = map[string]int32{
		"FIX_TYPE_UNSPECIFIED": 0,
		"NO_FIX":               1,
		"GNSS":                 2,
		"DIFFERENTIAL_GNSS":    3,
		"RTK_FIXED":            4,
		"RTK_FLOAT":            5,
		"DEAD_RECKONING":       6,
	}
)

func (x FixType) Enum() *FixType {
	p := new(FixType)
	*p = x
	return p
}

func (x FixType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FixType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_geo_geo_proto_enumTypes[0].Descriptor()
}

func (FixType) Type() protoreflect.EnumType {
	return &file_proto_geo_geo_proto_enumTypes[0]
}

func (x FixType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FixType.Descriptor instead.
func (FixType) EnumDescriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{0}
}

// Information about how a point was recorded.
type CaptureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FixType     FixType                `protobuf:"varint,1,opt,name=fix_type,json=fixType,proto3,enum=carbon.geo.FixType" json:"fix_type,omitempty"`
	CaptureTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=capture_time,json=captureTime,proto3" json:"capture_time,omitempty"`
}

func (x *CaptureInfo) Reset() {
	*x = CaptureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaptureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptureInfo) ProtoMessage() {}

func (x *CaptureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptureInfo.ProtoReflect.Descriptor instead.
func (*CaptureInfo) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{0}
}

func (x *CaptureInfo) GetFixType() FixType {
	if x != nil {
		return x.FixType
	}
	return FixType_FIX_TYPE_UNSPECIFIED
}

func (x *CaptureInfo) GetCaptureTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CaptureTime
	}
	return nil
}

type Id struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Globally unique ID for this entity. Intended to be a short, high-entropy
	// string, like base62 over ~65 bits (e.g., `JVU8yUVxbf9`).
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *Id) Reset() {
	*x = Id{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Id) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Id) ProtoMessage() {}

func (x *Id) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Id.ProtoReflect.Descriptor instead.
func (*Id) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{1}
}

func (x *Id) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// A point on the earth represented in the WGS 84 geodetic coordinate system.
type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Longitude, as degrees east of the prime meridian.
	Lng float64 `protobuf:"fixed64,1,opt,name=lng,proto3" json:"lng,omitempty"`
	// Latitude, as degrees north of the equator.
	Lat float64 `protobuf:"fixed64,2,opt,name=lat,proto3" json:"lat,omitempty"`
	// Altitude, as height in meters above the reference ellipsoid.
	Alt         float64      `protobuf:"fixed64,3,opt,name=alt,proto3" json:"alt,omitempty"`
	CaptureInfo *CaptureInfo `protobuf:"bytes,4,opt,name=capture_info,json=captureInfo,proto3" json:"capture_info,omitempty"`
	// Globally unique identifier for this point.
	//
	// Geometries containing points can include only the ID to incorporate
	// points by reference; e.g., the following `LineString` could
	// represent a closed path:
	//
	//	{
	//	  points { id { id: "Xb8M8INv9bo" } }
	//	  points { id { id: "B8pNtE3frX4" } }
	//	  points { id { id: "a78CkdKl7gk" } }
	//	  points { id { id: "Xb8M8INv9bo" } }
	//	}
	Id *Id `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	// Optional human-readable name for this point.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{2}
}

func (x *Point) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

func (x *Point) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Point) GetAlt() float64 {
	if x != nil {
		return x.Alt
	}
	return 0
}

func (x *Point) GetCaptureInfo() *CaptureInfo {
	if x != nil {
		return x.CaptureInfo
	}
	return nil
}

func (x *Point) GetId() *Id {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Point) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type LineString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Points []*Point `protobuf:"bytes,1,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *LineString) Reset() {
	*x = LineString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineString) ProtoMessage() {}

func (x *LineString) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineString.ProtoReflect.Descriptor instead.
func (*LineString) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{3}
}

func (x *LineString) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

// An A-B segment, typically indicating a ray, a line, or a heading.
type AbLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The start of the line.
	A *Point `protobuf:"bytes,1,opt,name=a,proto3" json:"a,omitempty"`
	// The end of the line.
	B *Point `protobuf:"bytes,2,opt,name=b,proto3" json:"b,omitempty"`
}

func (x *AbLine) Reset() {
	*x = AbLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbLine) ProtoMessage() {}

func (x *AbLine) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbLine.ProtoReflect.Descriptor instead.
func (*AbLine) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{4}
}

func (x *AbLine) GetA() *Point {
	if x != nil {
		return x.A
	}
	return nil
}

func (x *AbLine) GetB() *Point {
	if x != nil {
		return x.B
	}
	return nil
}

// A region on the earth, which has an exterior boundary and possible some
// interior holes. The exterior boundary must be wound counterclockwise and
// each hole must be wound clockwise.
type Polygon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Boundary *PolygonRing   `protobuf:"bytes,1,opt,name=boundary,proto3" json:"boundary,omitempty"`
	Holes    []*PolygonRing `protobuf:"bytes,2,rep,name=holes,proto3" json:"holes,omitempty"`
}

func (x *Polygon) Reset() {
	*x = Polygon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Polygon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Polygon) ProtoMessage() {}

func (x *Polygon) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Polygon.ProtoReflect.Descriptor instead.
func (*Polygon) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{5}
}

func (x *Polygon) GetBoundary() *PolygonRing {
	if x != nil {
		return x.Boundary
	}
	return nil
}

func (x *Polygon) GetHoles() []*PolygonRing {
	if x != nil {
		return x.Holes
	}
	return nil
}

// A linear ring on the boundary of the polygon, forming an exterior boundary
// or interior hole. The ring must contain at least four points, and the first
// and last points must be identical.
type PolygonRing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Points []*Point `protobuf:"bytes,1,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *PolygonRing) Reset() {
	*x = PolygonRing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolygonRing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolygonRing) ProtoMessage() {}

func (x *PolygonRing) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolygonRing.ProtoReflect.Descriptor instead.
func (*PolygonRing) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{6}
}

func (x *PolygonRing) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

// A union of disjoint `Polygon`s.
type MultiPolygon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Polygons []*Polygon `protobuf:"bytes,1,rep,name=polygons,proto3" json:"polygons,omitempty"`
}

func (x *MultiPolygon) Reset() {
	*x = MultiPolygon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_geo_geo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiPolygon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiPolygon) ProtoMessage() {}

func (x *MultiPolygon) ProtoReflect() protoreflect.Message {
	mi := &file_proto_geo_geo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiPolygon.ProtoReflect.Descriptor instead.
func (*MultiPolygon) Descriptor() ([]byte, []int) {
	return file_proto_geo_geo_proto_rawDescGZIP(), []int{7}
}

func (x *MultiPolygon) GetPolygons() []*Polygon {
	if x != nil {
		return x.Polygons
	}
	return nil
}

var File_proto_geo_geo_proto protoreflect.FileDescriptor

var file_proto_geo_geo_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x7c, 0x0a, 0x0b, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2e, 0x0a, 0x08, 0x66, 0x69, 0x78, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f,
	0x2e, 0x46, 0x69, 0x78, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x66, 0x69, 0x78, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x14, 0x0a, 0x02, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c,
	0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x61, 0x6c, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x49, 0x64, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x37, 0x0a, 0x0a, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65,
	0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22,
	0x4a, 0x0a, 0x06, 0x41, 0x62, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x01, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65,
	0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x01, 0x61, 0x12, 0x1f, 0x0a, 0x01, 0x62, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67,
	0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x01, 0x62, 0x22, 0x6d, 0x0a, 0x07, 0x50,
	0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x08, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x52, 0x69, 0x6e,
	0x67, 0x52, 0x08, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x68,
	0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x52,
	0x69, 0x6e, 0x67, 0x52, 0x05, 0x68, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x38, 0x0a, 0x0b, 0x50, 0x6f,
	0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x52, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x22, 0x3f, 0x0a, 0x0c, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x6f, 0x6c,
	0x79, 0x67, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x6c,
	0x79, 0x67, 0x6f, 0x6e, 0x73, 0x2a, 0x82, 0x01, 0x0a, 0x07, 0x46, 0x69, 0x78, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x58, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e,
	0x4f, 0x5f, 0x46, 0x49, 0x58, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x47, 0x4e, 0x53, 0x53, 0x10,
	0x02, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x54, 0x49, 0x41,
	0x4c, 0x5f, 0x47, 0x4e, 0x53, 0x53, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x54, 0x4b, 0x5f,
	0x46, 0x49, 0x58, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x54, 0x4b, 0x5f, 0x46,
	0x4c, 0x4f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x41, 0x44, 0x5f, 0x52,
	0x45, 0x43, 0x4b, 0x4f, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x42, 0x0b, 0x5a, 0x09, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_geo_geo_proto_rawDescOnce sync.Once
	file_proto_geo_geo_proto_rawDescData = file_proto_geo_geo_proto_rawDesc
)

func file_proto_geo_geo_proto_rawDescGZIP() []byte {
	file_proto_geo_geo_proto_rawDescOnce.Do(func() {
		file_proto_geo_geo_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_geo_geo_proto_rawDescData)
	})
	return file_proto_geo_geo_proto_rawDescData
}

var file_proto_geo_geo_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_geo_geo_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_geo_geo_proto_goTypes = []interface{}{
	(FixType)(0),                  // 0: carbon.geo.FixType
	(*CaptureInfo)(nil),           // 1: carbon.geo.CaptureInfo
	(*Id)(nil),                    // 2: carbon.geo.Id
	(*Point)(nil),                 // 3: carbon.geo.Point
	(*LineString)(nil),            // 4: carbon.geo.LineString
	(*AbLine)(nil),                // 5: carbon.geo.AbLine
	(*Polygon)(nil),               // 6: carbon.geo.Polygon
	(*PolygonRing)(nil),           // 7: carbon.geo.PolygonRing
	(*MultiPolygon)(nil),          // 8: carbon.geo.MultiPolygon
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
}
var file_proto_geo_geo_proto_depIdxs = []int32{
	0,  // 0: carbon.geo.CaptureInfo.fix_type:type_name -> carbon.geo.FixType
	9,  // 1: carbon.geo.CaptureInfo.capture_time:type_name -> google.protobuf.Timestamp
	1,  // 2: carbon.geo.Point.capture_info:type_name -> carbon.geo.CaptureInfo
	2,  // 3: carbon.geo.Point.id:type_name -> carbon.geo.Id
	3,  // 4: carbon.geo.LineString.points:type_name -> carbon.geo.Point
	3,  // 5: carbon.geo.AbLine.a:type_name -> carbon.geo.Point
	3,  // 6: carbon.geo.AbLine.b:type_name -> carbon.geo.Point
	7,  // 7: carbon.geo.Polygon.boundary:type_name -> carbon.geo.PolygonRing
	7,  // 8: carbon.geo.Polygon.holes:type_name -> carbon.geo.PolygonRing
	3,  // 9: carbon.geo.PolygonRing.points:type_name -> carbon.geo.Point
	6,  // 10: carbon.geo.MultiPolygon.polygons:type_name -> carbon.geo.Polygon
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_proto_geo_geo_proto_init() }
func file_proto_geo_geo_proto_init() {
	if File_proto_geo_geo_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_geo_geo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaptureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Id); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Polygon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolygonRing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_geo_geo_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiPolygon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_geo_geo_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_geo_geo_proto_goTypes,
		DependencyIndexes: file_proto_geo_geo_proto_depIdxs,
		EnumInfos:         file_proto_geo_geo_proto_enumTypes,
		MessageInfos:      file_proto_geo_geo_proto_msgTypes,
	}.Build()
	File_proto_geo_geo_proto = out.File
	file_proto_geo_geo_proto_rawDesc = nil
	file_proto_geo_geo_proto_goTypes = nil
	file_proto_geo_geo_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: weed_tracking/proto/weed_tracking.proto

package weed_tracking

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WeedTrackingService_Ping_FullMethodName                               = "/weed_tracking.WeedTrackingService/Ping"
	WeedTrackingService_GetDetections_FullMethodName                      = "/weed_tracking.WeedTrackingService/GetDetections"
	WeedTrackingService_GetTrajectoryMetadata_FullMethodName              = "/weed_tracking.WeedTrackingService/GetTrajectoryMetadata"
	WeedTrackingService_UpdateBands_FullMethodName                        = "/weed_tracking.WeedTrackingService/UpdateBands"
	WeedTrackingService_GetBooted_FullMethodName                          = "/weed_tracking.WeedTrackingService/GetBooted"
	WeedTrackingService_GetCurrentTrajectories_FullMethodName             = "/weed_tracking.WeedTrackingService/GetCurrentTrajectories"
	WeedTrackingService_StartSavingCropLineDetectionReplay_FullMethodName = "/weed_tracking.WeedTrackingService/StartSavingCropLineDetectionReplay"
	WeedTrackingService_StartRecordingDiagnostics_FullMethodName          = "/weed_tracking.WeedTrackingService/StartRecordingDiagnostics"
	WeedTrackingService_GetDiagnosticsRecordingStatus_FullMethodName      = "/weed_tracking.WeedTrackingService/GetDiagnosticsRecordingStatus"
	WeedTrackingService_RemoveRecordingsDirectory_FullMethodName          = "/weed_tracking.WeedTrackingService/RemoveRecordingsDirectory"
	WeedTrackingService_StartRecordingAimbotInputs_FullMethodName         = "/weed_tracking.WeedTrackingService/StartRecordingAimbotInputs"
	WeedTrackingService_GetConclusionCounter_FullMethodName               = "/weed_tracking.WeedTrackingService/GetConclusionCounter"
	WeedTrackingService_GetBands_FullMethodName                           = "/weed_tracking.WeedTrackingService/GetBands"
	WeedTrackingService_StartPlantCaptcha_FullMethodName                  = "/weed_tracking.WeedTrackingService/StartPlantCaptcha"
	WeedTrackingService_GetPlantCaptchaStatus_FullMethodName              = "/weed_tracking.WeedTrackingService/GetPlantCaptchaStatus"
	WeedTrackingService_RemovePlantCaptchaDirectory_FullMethodName        = "/weed_tracking.WeedTrackingService/RemovePlantCaptchaDirectory"
	WeedTrackingService_CancelPlantCaptcha_FullMethodName                 = "/weed_tracking.WeedTrackingService/CancelPlantCaptcha"
	WeedTrackingService_GetTargetingEnabled_FullMethodName                = "/weed_tracking.WeedTrackingService/GetTargetingEnabled"
)

// WeedTrackingServiceClient is the client API for WeedTrackingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WeedTrackingServiceClient interface {
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PongReply, error)
	GetDetections(ctx context.Context, in *GetDetectionsRequest, opts ...grpc.CallOption) (*GetDetectionsResponse, error)
	GetTrajectoryMetadata(ctx context.Context, in *GetTrajectoryMetadataRequest, opts ...grpc.CallOption) (*GetTrajectoryMetadataResponse, error)
	UpdateBands(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetBooted(ctx context.Context, in *GetBootedRequest, opts ...grpc.CallOption) (*GetBootedResponse, error)
	// weeding diagnostics
	GetCurrentTrajectories(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DiagnosticsSnapshot, error)
	StartSavingCropLineDetectionReplay(ctx context.Context, in *StartSavingCropLineDetectionReplayRequest, opts ...grpc.CallOption) (*Empty, error)
	StartRecordingDiagnostics(ctx context.Context, in *RecordDiagnosticsRequest, opts ...grpc.CallOption) (*Empty, error)
	GetDiagnosticsRecordingStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetRecordingStatusResponse, error)
	RemoveRecordingsDirectory(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StartRecordingAimbotInputs(ctx context.Context, in *RecordAimbotInputRequest, opts ...grpc.CallOption) (*Empty, error)
	GetConclusionCounter(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ConclusionCounter, error)
	GetBands(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BandDefinitions, error)
	StartPlantCaptcha(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetPlantCaptchaStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*PlantCaptchaStatusResponse, error)
	RemovePlantCaptchaDirectory(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	CancelPlantCaptcha(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetTargetingEnabled(ctx context.Context, in *GetTargetingEnabledRequest, opts ...grpc.CallOption) (*GetTargetingEnabledResponse, error)
}

type weedTrackingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWeedTrackingServiceClient(cc grpc.ClientConnInterface) WeedTrackingServiceClient {
	return &weedTrackingServiceClient{cc}
}

func (c *weedTrackingServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PongReply, error) {
	out := new(PongReply)
	err := c.cc.Invoke(ctx, WeedTrackingService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetDetections(ctx context.Context, in *GetDetectionsRequest, opts ...grpc.CallOption) (*GetDetectionsResponse, error) {
	out := new(GetDetectionsResponse)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetDetections_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetTrajectoryMetadata(ctx context.Context, in *GetTrajectoryMetadataRequest, opts ...grpc.CallOption) (*GetTrajectoryMetadataResponse, error) {
	out := new(GetTrajectoryMetadataResponse)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetTrajectoryMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) UpdateBands(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_UpdateBands_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetBooted(ctx context.Context, in *GetBootedRequest, opts ...grpc.CallOption) (*GetBootedResponse, error) {
	out := new(GetBootedResponse)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetBooted_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetCurrentTrajectories(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DiagnosticsSnapshot, error) {
	out := new(DiagnosticsSnapshot)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetCurrentTrajectories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) StartSavingCropLineDetectionReplay(ctx context.Context, in *StartSavingCropLineDetectionReplayRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_StartSavingCropLineDetectionReplay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) StartRecordingDiagnostics(ctx context.Context, in *RecordDiagnosticsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_StartRecordingDiagnostics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetDiagnosticsRecordingStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetRecordingStatusResponse, error) {
	out := new(GetRecordingStatusResponse)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetDiagnosticsRecordingStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) RemoveRecordingsDirectory(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_RemoveRecordingsDirectory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) StartRecordingAimbotInputs(ctx context.Context, in *RecordAimbotInputRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_StartRecordingAimbotInputs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetConclusionCounter(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ConclusionCounter, error) {
	out := new(ConclusionCounter)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetConclusionCounter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetBands(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BandDefinitions, error) {
	out := new(BandDefinitions)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetBands_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) StartPlantCaptcha(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_StartPlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetPlantCaptchaStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*PlantCaptchaStatusResponse, error) {
	out := new(PlantCaptchaStatusResponse)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetPlantCaptchaStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) RemovePlantCaptchaDirectory(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_RemovePlantCaptchaDirectory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) CancelPlantCaptcha(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, WeedTrackingService_CancelPlantCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedTrackingServiceClient) GetTargetingEnabled(ctx context.Context, in *GetTargetingEnabledRequest, opts ...grpc.CallOption) (*GetTargetingEnabledResponse, error) {
	out := new(GetTargetingEnabledResponse)
	err := c.cc.Invoke(ctx, WeedTrackingService_GetTargetingEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WeedTrackingServiceServer is the server API for WeedTrackingService service.
// All implementations must embed UnimplementedWeedTrackingServiceServer
// for forward compatibility
type WeedTrackingServiceServer interface {
	Ping(context.Context, *PingRequest) (*PongReply, error)
	GetDetections(context.Context, *GetDetectionsRequest) (*GetDetectionsResponse, error)
	GetTrajectoryMetadata(context.Context, *GetTrajectoryMetadataRequest) (*GetTrajectoryMetadataResponse, error)
	UpdateBands(context.Context, *Empty) (*Empty, error)
	GetBooted(context.Context, *GetBootedRequest) (*GetBootedResponse, error)
	// weeding diagnostics
	GetCurrentTrajectories(context.Context, *Empty) (*DiagnosticsSnapshot, error)
	StartSavingCropLineDetectionReplay(context.Context, *StartSavingCropLineDetectionReplayRequest) (*Empty, error)
	StartRecordingDiagnostics(context.Context, *RecordDiagnosticsRequest) (*Empty, error)
	GetDiagnosticsRecordingStatus(context.Context, *Empty) (*GetRecordingStatusResponse, error)
	RemoveRecordingsDirectory(context.Context, *Empty) (*Empty, error)
	StartRecordingAimbotInputs(context.Context, *RecordAimbotInputRequest) (*Empty, error)
	GetConclusionCounter(context.Context, *Empty) (*ConclusionCounter, error)
	GetBands(context.Context, *Empty) (*BandDefinitions, error)
	StartPlantCaptcha(context.Context, *Empty) (*Empty, error)
	GetPlantCaptchaStatus(context.Context, *Empty) (*PlantCaptchaStatusResponse, error)
	RemovePlantCaptchaDirectory(context.Context, *Empty) (*Empty, error)
	CancelPlantCaptcha(context.Context, *Empty) (*Empty, error)
	GetTargetingEnabled(context.Context, *GetTargetingEnabledRequest) (*GetTargetingEnabledResponse, error)
	mustEmbedUnimplementedWeedTrackingServiceServer()
}

// UnimplementedWeedTrackingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWeedTrackingServiceServer struct {
}

func (UnimplementedWeedTrackingServiceServer) Ping(context.Context, *PingRequest) (*PongReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetDetections(context.Context, *GetDetectionsRequest) (*GetDetectionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDetections not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetTrajectoryMetadata(context.Context, *GetTrajectoryMetadataRequest) (*GetTrajectoryMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTrajectoryMetadata not implemented")
}
func (UnimplementedWeedTrackingServiceServer) UpdateBands(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBands not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetBooted(context.Context, *GetBootedRequest) (*GetBootedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBooted not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetCurrentTrajectories(context.Context, *Empty) (*DiagnosticsSnapshot, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrentTrajectories not implemented")
}
func (UnimplementedWeedTrackingServiceServer) StartSavingCropLineDetectionReplay(context.Context, *StartSavingCropLineDetectionReplayRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartSavingCropLineDetectionReplay not implemented")
}
func (UnimplementedWeedTrackingServiceServer) StartRecordingDiagnostics(context.Context, *RecordDiagnosticsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartRecordingDiagnostics not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetDiagnosticsRecordingStatus(context.Context, *Empty) (*GetRecordingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiagnosticsRecordingStatus not implemented")
}
func (UnimplementedWeedTrackingServiceServer) RemoveRecordingsDirectory(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveRecordingsDirectory not implemented")
}
func (UnimplementedWeedTrackingServiceServer) StartRecordingAimbotInputs(context.Context, *RecordAimbotInputRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartRecordingAimbotInputs not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetConclusionCounter(context.Context, *Empty) (*ConclusionCounter, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConclusionCounter not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetBands(context.Context, *Empty) (*BandDefinitions, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBands not implemented")
}
func (UnimplementedWeedTrackingServiceServer) StartPlantCaptcha(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPlantCaptcha not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetPlantCaptchaStatus(context.Context, *Empty) (*PlantCaptchaStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlantCaptchaStatus not implemented")
}
func (UnimplementedWeedTrackingServiceServer) RemovePlantCaptchaDirectory(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePlantCaptchaDirectory not implemented")
}
func (UnimplementedWeedTrackingServiceServer) CancelPlantCaptcha(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPlantCaptcha not implemented")
}
func (UnimplementedWeedTrackingServiceServer) GetTargetingEnabled(context.Context, *GetTargetingEnabledRequest) (*GetTargetingEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTargetingEnabled not implemented")
}
func (UnimplementedWeedTrackingServiceServer) mustEmbedUnimplementedWeedTrackingServiceServer() {}

// UnsafeWeedTrackingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WeedTrackingServiceServer will
// result in compilation errors.
type UnsafeWeedTrackingServiceServer interface {
	mustEmbedUnimplementedWeedTrackingServiceServer()
}

func RegisterWeedTrackingServiceServer(s grpc.ServiceRegistrar, srv WeedTrackingServiceServer) {
	s.RegisterService(&WeedTrackingService_ServiceDesc, srv)
}

func _WeedTrackingService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetDetections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDetectionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetDetections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetDetections_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetDetections(ctx, req.(*GetDetectionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetTrajectoryMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrajectoryMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetTrajectoryMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetTrajectoryMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetTrajectoryMetadata(ctx, req.(*GetTrajectoryMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_UpdateBands_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).UpdateBands(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_UpdateBands_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).UpdateBands(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetBooted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBootedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetBooted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetBooted_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetBooted(ctx, req.(*GetBootedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetCurrentTrajectories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetCurrentTrajectories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetCurrentTrajectories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetCurrentTrajectories(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_StartSavingCropLineDetectionReplay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartSavingCropLineDetectionReplayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).StartSavingCropLineDetectionReplay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_StartSavingCropLineDetectionReplay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).StartSavingCropLineDetectionReplay(ctx, req.(*StartSavingCropLineDetectionReplayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_StartRecordingDiagnostics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordDiagnosticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).StartRecordingDiagnostics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_StartRecordingDiagnostics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).StartRecordingDiagnostics(ctx, req.(*RecordDiagnosticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetDiagnosticsRecordingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetDiagnosticsRecordingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetDiagnosticsRecordingStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetDiagnosticsRecordingStatus(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_RemoveRecordingsDirectory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).RemoveRecordingsDirectory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_RemoveRecordingsDirectory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).RemoveRecordingsDirectory(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_StartRecordingAimbotInputs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordAimbotInputRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).StartRecordingAimbotInputs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_StartRecordingAimbotInputs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).StartRecordingAimbotInputs(ctx, req.(*RecordAimbotInputRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetConclusionCounter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetConclusionCounter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetConclusionCounter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetConclusionCounter(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetBands_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetBands(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetBands_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetBands(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_StartPlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).StartPlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_StartPlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).StartPlantCaptcha(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetPlantCaptchaStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetPlantCaptchaStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetPlantCaptchaStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetPlantCaptchaStatus(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_RemovePlantCaptchaDirectory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).RemovePlantCaptchaDirectory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_RemovePlantCaptchaDirectory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).RemovePlantCaptchaDirectory(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_CancelPlantCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).CancelPlantCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_CancelPlantCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).CancelPlantCaptcha(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedTrackingService_GetTargetingEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetingEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedTrackingServiceServer).GetTargetingEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WeedTrackingService_GetTargetingEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedTrackingServiceServer).GetTargetingEnabled(ctx, req.(*GetTargetingEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WeedTrackingService_ServiceDesc is the grpc.ServiceDesc for WeedTrackingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WeedTrackingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "weed_tracking.WeedTrackingService",
	HandlerType: (*WeedTrackingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _WeedTrackingService_Ping_Handler,
		},
		{
			MethodName: "GetDetections",
			Handler:    _WeedTrackingService_GetDetections_Handler,
		},
		{
			MethodName: "GetTrajectoryMetadata",
			Handler:    _WeedTrackingService_GetTrajectoryMetadata_Handler,
		},
		{
			MethodName: "UpdateBands",
			Handler:    _WeedTrackingService_UpdateBands_Handler,
		},
		{
			MethodName: "GetBooted",
			Handler:    _WeedTrackingService_GetBooted_Handler,
		},
		{
			MethodName: "GetCurrentTrajectories",
			Handler:    _WeedTrackingService_GetCurrentTrajectories_Handler,
		},
		{
			MethodName: "StartSavingCropLineDetectionReplay",
			Handler:    _WeedTrackingService_StartSavingCropLineDetectionReplay_Handler,
		},
		{
			MethodName: "StartRecordingDiagnostics",
			Handler:    _WeedTrackingService_StartRecordingDiagnostics_Handler,
		},
		{
			MethodName: "GetDiagnosticsRecordingStatus",
			Handler:    _WeedTrackingService_GetDiagnosticsRecordingStatus_Handler,
		},
		{
			MethodName: "RemoveRecordingsDirectory",
			Handler:    _WeedTrackingService_RemoveRecordingsDirectory_Handler,
		},
		{
			MethodName: "StartRecordingAimbotInputs",
			Handler:    _WeedTrackingService_StartRecordingAimbotInputs_Handler,
		},
		{
			MethodName: "GetConclusionCounter",
			Handler:    _WeedTrackingService_GetConclusionCounter_Handler,
		},
		{
			MethodName: "GetBands",
			Handler:    _WeedTrackingService_GetBands_Handler,
		},
		{
			MethodName: "StartPlantCaptcha",
			Handler:    _WeedTrackingService_StartPlantCaptcha_Handler,
		},
		{
			MethodName: "GetPlantCaptchaStatus",
			Handler:    _WeedTrackingService_GetPlantCaptchaStatus_Handler,
		},
		{
			MethodName: "RemovePlantCaptchaDirectory",
			Handler:    _WeedTrackingService_RemovePlantCaptchaDirectory_Handler,
		},
		{
			MethodName: "CancelPlantCaptcha",
			Handler:    _WeedTrackingService_CancelPlantCaptcha_Handler,
		},
		{
			MethodName: "GetTargetingEnabled",
			Handler:    _WeedTrackingService_GetTargetingEnabled_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "weed_tracking/proto/weed_tracking.proto",
}

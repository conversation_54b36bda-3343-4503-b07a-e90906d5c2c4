// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: data_upload_manager/proto/emergency.proto

package data_upload_manager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StartDataCaptureSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionName         string  `protobuf:"bytes,1,opt,name=session_name,json=sessionName,proto3" json:"session_name,omitempty"`
	CaptureRate         float64 `protobuf:"fixed64,2,opt,name=capture_rate,json=captureRate,proto3" json:"capture_rate,omitempty"`
	Crop                string  `protobuf:"bytes,3,opt,name=crop,proto3" json:"crop,omitempty"`
	EnableSinglePredict bool    `protobuf:"varint,4,opt,name=enable_single_predict,json=enableSinglePredict,proto3" json:"enable_single_predict,omitempty"` // defaults to false
	RowInd              uint32  `protobuf:"varint,5,opt,name=rowInd,proto3" json:"rowInd,omitempty"`
	CamId               string  `protobuf:"bytes,6,opt,name=camId,proto3" json:"camId,omitempty"`
	CropId              string  `protobuf:"bytes,7,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	SnapCapture         bool    `protobuf:"varint,8,opt,name=snap_capture,json=snapCapture,proto3" json:"snap_capture,omitempty"`
}

func (x *StartDataCaptureSessionRequest) Reset() {
	*x = StartDataCaptureSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDataCaptureSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDataCaptureSessionRequest) ProtoMessage() {}

func (x *StartDataCaptureSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDataCaptureSessionRequest.ProtoReflect.Descriptor instead.
func (*StartDataCaptureSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{0}
}

func (x *StartDataCaptureSessionRequest) GetSessionName() string {
	if x != nil {
		return x.SessionName
	}
	return ""
}

func (x *StartDataCaptureSessionRequest) GetCaptureRate() float64 {
	if x != nil {
		return x.CaptureRate
	}
	return 0
}

func (x *StartDataCaptureSessionRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *StartDataCaptureSessionRequest) GetEnableSinglePredict() bool {
	if x != nil {
		return x.EnableSinglePredict
	}
	return false
}

func (x *StartDataCaptureSessionRequest) GetRowInd() uint32 {
	if x != nil {
		return x.RowInd
	}
	return 0
}

func (x *StartDataCaptureSessionRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *StartDataCaptureSessionRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *StartDataCaptureSessionRequest) GetSnapCapture() bool {
	if x != nil {
		return x.SnapCapture
	}
	return false
}

type StartDataCaptureSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartDataCaptureSessionResponse) Reset() {
	*x = StartDataCaptureSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDataCaptureSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDataCaptureSessionResponse) ProtoMessage() {}

func (x *StartDataCaptureSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDataCaptureSessionResponse.ProtoReflect.Descriptor instead.
func (*StartDataCaptureSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{1}
}

type StopDataCaptureSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopDataCaptureSessionRequest) Reset() {
	*x = StopDataCaptureSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDataCaptureSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDataCaptureSessionRequest) ProtoMessage() {}

func (x *StopDataCaptureSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDataCaptureSessionRequest.ProtoReflect.Descriptor instead.
func (*StopDataCaptureSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{2}
}

type StopDataCaptureSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopDataCaptureSessionResponse) Reset() {
	*x = StopDataCaptureSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDataCaptureSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDataCaptureSessionResponse) ProtoMessage() {}

func (x *StopDataCaptureSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDataCaptureSessionResponse.ProtoReflect.Descriptor instead.
func (*StopDataCaptureSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{3}
}

type ResumeDataCaptureSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResumeDataCaptureSessionRequest) Reset() {
	*x = ResumeDataCaptureSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeDataCaptureSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeDataCaptureSessionRequest) ProtoMessage() {}

func (x *ResumeDataCaptureSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeDataCaptureSessionRequest.ProtoReflect.Descriptor instead.
func (*ResumeDataCaptureSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{4}
}

type ResumeDataCaptureSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResumeDataCaptureSessionResponse) Reset() {
	*x = ResumeDataCaptureSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeDataCaptureSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeDataCaptureSessionResponse) ProtoMessage() {}

func (x *ResumeDataCaptureSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeDataCaptureSessionResponse.ProtoReflect.Descriptor instead.
func (*ResumeDataCaptureSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{5}
}

type PauseDataCaptureSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PauseDataCaptureSessionRequest) Reset() {
	*x = PauseDataCaptureSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseDataCaptureSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseDataCaptureSessionRequest) ProtoMessage() {}

func (x *PauseDataCaptureSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseDataCaptureSessionRequest.ProtoReflect.Descriptor instead.
func (*PauseDataCaptureSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{6}
}

type PauseDataCaptureSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PauseDataCaptureSessionResponse) Reset() {
	*x = PauseDataCaptureSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseDataCaptureSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseDataCaptureSessionResponse) ProtoMessage() {}

func (x *PauseDataCaptureSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseDataCaptureSessionResponse.ProtoReflect.Descriptor instead.
func (*PauseDataCaptureSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{7}
}

type CompleteDataCaptureSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CompleteDataCaptureSessionRequest) Reset() {
	*x = CompleteDataCaptureSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteDataCaptureSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteDataCaptureSessionRequest) ProtoMessage() {}

func (x *CompleteDataCaptureSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteDataCaptureSessionRequest.ProtoReflect.Descriptor instead.
func (*CompleteDataCaptureSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{8}
}

type CompleteDataCaptureSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CompleteDataCaptureSessionResponse) Reset() {
	*x = CompleteDataCaptureSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteDataCaptureSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteDataCaptureSessionResponse) ProtoMessage() {}

func (x *CompleteDataCaptureSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteDataCaptureSessionResponse.ProtoReflect.Descriptor instead.
func (*CompleteDataCaptureSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{9}
}

type StartDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
}

func (x *StartDataUploadSessionRequest) Reset() {
	*x = StartDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDataUploadSessionRequest) ProtoMessage() {}

func (x *StartDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*StartDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{10}
}

func (x *StartDataUploadSessionRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

type StartDataUploadSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartDataUploadSessionResponse) Reset() {
	*x = StartDataUploadSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDataUploadSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDataUploadSessionResponse) ProtoMessage() {}

func (x *StartDataUploadSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDataUploadSessionResponse.ProtoReflect.Descriptor instead.
func (*StartDataUploadSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{11}
}

type StopDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopDataUploadSessionRequest) Reset() {
	*x = StopDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDataUploadSessionRequest) ProtoMessage() {}

func (x *StopDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*StopDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{12}
}

type StopDataUploadSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopDataUploadSessionResponse) Reset() {
	*x = StopDataUploadSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDataUploadSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDataUploadSessionResponse) ProtoMessage() {}

func (x *StopDataUploadSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDataUploadSessionResponse.ProtoReflect.Descriptor instead.
func (*StopDataUploadSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{13}
}

type ResumeDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResumeDataUploadSessionRequest) Reset() {
	*x = ResumeDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeDataUploadSessionRequest) ProtoMessage() {}

func (x *ResumeDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*ResumeDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{14}
}

type ResumeDataUploadSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResumeDataUploadSessionResponse) Reset() {
	*x = ResumeDataUploadSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeDataUploadSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeDataUploadSessionResponse) ProtoMessage() {}

func (x *ResumeDataUploadSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeDataUploadSessionResponse.ProtoReflect.Descriptor instead.
func (*ResumeDataUploadSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{15}
}

type PauseDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PauseDataUploadSessionRequest) Reset() {
	*x = PauseDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseDataUploadSessionRequest) ProtoMessage() {}

func (x *PauseDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*PauseDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{16}
}

type PauseDataUploadSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PauseDataUploadSessionResponse) Reset() {
	*x = PauseDataUploadSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseDataUploadSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseDataUploadSessionResponse) ProtoMessage() {}

func (x *PauseDataUploadSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseDataUploadSessionResponse.ProtoReflect.Descriptor instead.
func (*PauseDataUploadSessionResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{17}
}

type StartBackgroundDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method               string `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	EmergencySessionName string `protobuf:"bytes,2,opt,name=emergency_session_name,json=emergencySessionName,proto3" json:"emergency_session_name,omitempty"`
}

func (x *StartBackgroundDataUploadSessionRequest) Reset() {
	*x = StartBackgroundDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartBackgroundDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartBackgroundDataUploadSessionRequest) ProtoMessage() {}

func (x *StartBackgroundDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartBackgroundDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*StartBackgroundDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{18}
}

func (x *StartBackgroundDataUploadSessionRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *StartBackgroundDataUploadSessionRequest) GetEmergencySessionName() string {
	if x != nil {
		return x.EmergencySessionName
	}
	return ""
}

type StopBackgroundDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmergencySessionName string `protobuf:"bytes,1,opt,name=emergency_session_name,json=emergencySessionName,proto3" json:"emergency_session_name,omitempty"`
}

func (x *StopBackgroundDataUploadSessionRequest) Reset() {
	*x = StopBackgroundDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopBackgroundDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopBackgroundDataUploadSessionRequest) ProtoMessage() {}

func (x *StopBackgroundDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopBackgroundDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*StopBackgroundDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{19}
}

func (x *StopBackgroundDataUploadSessionRequest) GetEmergencySessionName() string {
	if x != nil {
		return x.EmergencySessionName
	}
	return ""
}

type ResumeBackgroundDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmergencySessionName string `protobuf:"bytes,1,opt,name=emergency_session_name,json=emergencySessionName,proto3" json:"emergency_session_name,omitempty"`
}

func (x *ResumeBackgroundDataUploadSessionRequest) Reset() {
	*x = ResumeBackgroundDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeBackgroundDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeBackgroundDataUploadSessionRequest) ProtoMessage() {}

func (x *ResumeBackgroundDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeBackgroundDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*ResumeBackgroundDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{20}
}

func (x *ResumeBackgroundDataUploadSessionRequest) GetEmergencySessionName() string {
	if x != nil {
		return x.EmergencySessionName
	}
	return ""
}

type PauseBackgroundDataUploadSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmergencySessionName string `protobuf:"bytes,1,opt,name=emergency_session_name,json=emergencySessionName,proto3" json:"emergency_session_name,omitempty"`
}

func (x *PauseBackgroundDataUploadSessionRequest) Reset() {
	*x = PauseBackgroundDataUploadSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseBackgroundDataUploadSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseBackgroundDataUploadSessionRequest) ProtoMessage() {}

func (x *PauseBackgroundDataUploadSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseBackgroundDataUploadSessionRequest.ProtoReflect.Descriptor instead.
func (*PauseBackgroundDataUploadSessionRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{21}
}

func (x *PauseBackgroundDataUploadSessionRequest) GetEmergencySessionName() string {
	if x != nil {
		return x.EmergencySessionName
	}
	return ""
}

type GetCaptureProgressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCaptureProgressRequest) Reset() {
	*x = GetCaptureProgressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaptureProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaptureProgressRequest) ProtoMessage() {}

func (x *GetCaptureProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaptureProgressRequest.ProtoReflect.Descriptor instead.
func (*GetCaptureProgressRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{22}
}

type GetCaptureProgressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImagesCaptured uint32 `protobuf:"varint,1,opt,name=images_captured,json=imagesCaptured,proto3" json:"images_captured,omitempty"`
	Exists         bool   `protobuf:"varint,2,opt,name=exists,proto3" json:"exists,omitempty"`
	IsCapturing    bool   `protobuf:"varint,3,opt,name=is_capturing,json=isCapturing,proto3" json:"is_capturing,omitempty"`
	HasCompleted   bool   `protobuf:"varint,4,opt,name=has_completed,json=hasCompleted,proto3" json:"has_completed,omitempty"`
}

func (x *GetCaptureProgressResponse) Reset() {
	*x = GetCaptureProgressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaptureProgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaptureProgressResponse) ProtoMessage() {}

func (x *GetCaptureProgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaptureProgressResponse.ProtoReflect.Descriptor instead.
func (*GetCaptureProgressResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{23}
}

func (x *GetCaptureProgressResponse) GetImagesCaptured() uint32 {
	if x != nil {
		return x.ImagesCaptured
	}
	return 0
}

func (x *GetCaptureProgressResponse) GetExists() bool {
	if x != nil {
		return x.Exists
	}
	return false
}

func (x *GetCaptureProgressResponse) GetIsCapturing() bool {
	if x != nil {
		return x.IsCapturing
	}
	return false
}

func (x *GetCaptureProgressResponse) GetHasCompleted() bool {
	if x != nil {
		return x.HasCompleted
	}
	return false
}

type GetUploadProgressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetUploadProgressRequest) Reset() {
	*x = GetUploadProgressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUploadProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUploadProgressRequest) ProtoMessage() {}

func (x *GetUploadProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUploadProgressRequest.ProtoReflect.Descriptor instead.
func (*GetUploadProgressRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{24}
}

type GetUploadProgressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImagesUploaded uint32 `protobuf:"varint,1,opt,name=images_uploaded,json=imagesUploaded,proto3" json:"images_uploaded,omitempty"`
	Exists         bool   `protobuf:"varint,2,opt,name=exists,proto3" json:"exists,omitempty"`
	IsUploading    bool   `protobuf:"varint,3,opt,name=is_uploading,json=isUploading,proto3" json:"is_uploading,omitempty"`
	Method         string `protobuf:"bytes,4,opt,name=method,proto3" json:"method,omitempty"`
	HasCompleted   bool   `protobuf:"varint,5,opt,name=has_completed,json=hasCompleted,proto3" json:"has_completed,omitempty"`
}

func (x *GetUploadProgressResponse) Reset() {
	*x = GetUploadProgressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUploadProgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUploadProgressResponse) ProtoMessage() {}

func (x *GetUploadProgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUploadProgressResponse.ProtoReflect.Descriptor instead.
func (*GetUploadProgressResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{25}
}

func (x *GetUploadProgressResponse) GetImagesUploaded() uint32 {
	if x != nil {
		return x.ImagesUploaded
	}
	return 0
}

func (x *GetUploadProgressResponse) GetExists() bool {
	if x != nil {
		return x.Exists
	}
	return false
}

func (x *GetUploadProgressResponse) GetIsUploading() bool {
	if x != nil {
		return x.IsUploading
	}
	return false
}

func (x *GetUploadProgressResponse) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *GetUploadProgressResponse) GetHasCompleted() bool {
	if x != nil {
		return x.HasCompleted
	}
	return false
}

type SnapImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crop        string  `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"`
	CropId      string  `protobuf:"bytes,2,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	CamId       *string `protobuf:"bytes,3,opt,name=cam_id,json=camId,proto3,oneof" json:"cam_id,omitempty"`
	TimestampMs *int64  `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3,oneof" json:"timestamp_ms,omitempty"`
	RowInd      *uint32 `protobuf:"varint,5,opt,name=row_ind,json=rowInd,proto3,oneof" json:"row_ind,omitempty"`
	SessionName string  `protobuf:"bytes,6,opt,name=session_name,json=sessionName,proto3" json:"session_name,omitempty"`
}

func (x *SnapImagesRequest) Reset() {
	*x = SnapImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapImagesRequest) ProtoMessage() {}

func (x *SnapImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapImagesRequest.ProtoReflect.Descriptor instead.
func (*SnapImagesRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{26}
}

func (x *SnapImagesRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *SnapImagesRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *SnapImagesRequest) GetCamId() string {
	if x != nil && x.CamId != nil {
		return *x.CamId
	}
	return ""
}

func (x *SnapImagesRequest) GetTimestampMs() int64 {
	if x != nil && x.TimestampMs != nil {
		return *x.TimestampMs
	}
	return 0
}

func (x *SnapImagesRequest) GetRowInd() uint32 {
	if x != nil && x.RowInd != nil {
		return *x.RowInd
	}
	return 0
}

func (x *SnapImagesRequest) GetSessionName() string {
	if x != nil {
		return x.SessionName
	}
	return ""
}

type SnapImagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SnapImagesResponse) Reset() {
	*x = SnapImagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapImagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapImagesResponse) ProtoMessage() {}

func (x *SnapImagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapImagesResponse.ProtoReflect.Descriptor instead.
func (*SnapImagesResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{27}
}

type GetSessionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSessionsRequest) Reset() {
	*x = GetSessionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionsRequest) ProtoMessage() {}

func (x *GetSessionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionsRequest.ProtoReflect.Descriptor instead.
func (*GetSessionsRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{28}
}

type Session struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	NumberImages uint32 `protobuf:"varint,2,opt,name=number_images,json=numberImages,proto3" json:"number_images,omitempty"`
	IsUploading  bool   `protobuf:"varint,3,opt,name=is_uploading,json=isUploading,proto3" json:"is_uploading,omitempty"`
	HasCompleted bool   `protobuf:"varint,4,opt,name=has_completed,json=hasCompleted,proto3" json:"has_completed,omitempty"`
	IsCapturing  bool   `protobuf:"varint,5,opt,name=is_capturing,json=isCapturing,proto3" json:"is_capturing,omitempty"`
}

func (x *Session) Reset() {
	*x = Session{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Session) ProtoMessage() {}

func (x *Session) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Session.ProtoReflect.Descriptor instead.
func (*Session) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{29}
}

func (x *Session) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Session) GetNumberImages() uint32 {
	if x != nil {
		return x.NumberImages
	}
	return 0
}

func (x *Session) GetIsUploading() bool {
	if x != nil {
		return x.IsUploading
	}
	return false
}

func (x *Session) GetHasCompleted() bool {
	if x != nil {
		return x.HasCompleted
	}
	return false
}

func (x *Session) GetIsCapturing() bool {
	if x != nil {
		return x.IsCapturing
	}
	return false
}

type GetSessionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sessions []*Session `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *GetSessionsResponse) Reset() {
	*x = GetSessionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionsResponse) ProtoMessage() {}

func (x *GetSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionsResponse.ProtoReflect.Descriptor instead.
func (*GetSessionsResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{30}
}

func (x *GetSessionsResponse) GetSessions() []*Session {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type GetRegularCaptureStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRegularCaptureStatusRequest) Reset() {
	*x = GetRegularCaptureStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRegularCaptureStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRegularCaptureStatusRequest) ProtoMessage() {}

func (x *GetRegularCaptureStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRegularCaptureStatusRequest.ProtoReflect.Descriptor instead.
func (*GetRegularCaptureStatusRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{31}
}

type GetRegularCaptureStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uploaded            uint32 `protobuf:"varint,1,opt,name=uploaded,proto3" json:"uploaded,omitempty"`
	Budget              uint32 `protobuf:"varint,2,opt,name=budget,proto3" json:"budget,omitempty"`
	LastUploadTimestamp int64  `protobuf:"varint,3,opt,name=last_upload_timestamp,json=lastUploadTimestamp,proto3" json:"last_upload_timestamp,omitempty"`
}

func (x *GetRegularCaptureStatusResponse) Reset() {
	*x = GetRegularCaptureStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRegularCaptureStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRegularCaptureStatusResponse) ProtoMessage() {}

func (x *GetRegularCaptureStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRegularCaptureStatusResponse.ProtoReflect.Descriptor instead.
func (*GetRegularCaptureStatusResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{32}
}

func (x *GetRegularCaptureStatusResponse) GetUploaded() uint32 {
	if x != nil {
		return x.Uploaded
	}
	return 0
}

func (x *GetRegularCaptureStatusResponse) GetBudget() uint32 {
	if x != nil {
		return x.Budget
	}
	return 0
}

func (x *GetRegularCaptureStatusResponse) GetLastUploadTimestamp() int64 {
	if x != nil {
		return x.LastUploadTimestamp
	}
	return 0
}

type StartWeedingDiagnosticsUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StartWeedingDiagnosticsUploadRequest) Reset() {
	*x = StartWeedingDiagnosticsUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartWeedingDiagnosticsUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartWeedingDiagnosticsUploadRequest) ProtoMessage() {}

func (x *StartWeedingDiagnosticsUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartWeedingDiagnosticsUploadRequest.ProtoReflect.Descriptor instead.
func (*StartWeedingDiagnosticsUploadRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{33}
}

func (x *StartWeedingDiagnosticsUploadRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type StartWeedingDiagnosticsUploadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartWeedingDiagnosticsUploadResponse) Reset() {
	*x = StartWeedingDiagnosticsUploadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartWeedingDiagnosticsUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartWeedingDiagnosticsUploadResponse) ProtoMessage() {}

func (x *StartWeedingDiagnosticsUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartWeedingDiagnosticsUploadResponse.ProtoReflect.Descriptor instead.
func (*StartWeedingDiagnosticsUploadResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{34}
}

type StartPlantCaptchaUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StartPlantCaptchaUploadRequest) Reset() {
	*x = StartPlantCaptchaUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartPlantCaptchaUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPlantCaptchaUploadRequest) ProtoMessage() {}

func (x *StartPlantCaptchaUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPlantCaptchaUploadRequest.ProtoReflect.Descriptor instead.
func (*StartPlantCaptchaUploadRequest) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{35}
}

func (x *StartPlantCaptchaUploadRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type StartPlantCaptchaUploadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartPlantCaptchaUploadResponse) Reset() {
	*x = StartPlantCaptchaUploadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartPlantCaptchaUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPlantCaptchaUploadResponse) ProtoMessage() {}

func (x *StartPlantCaptchaUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_upload_manager_proto_emergency_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPlantCaptchaUploadResponse.ProtoReflect.Descriptor instead.
func (*StartPlantCaptchaUploadResponse) Descriptor() ([]byte, []int) {
	return file_data_upload_manager_proto_emergency_proto_rawDescGZIP(), []int{36}
}

var File_data_upload_manager_proto_emergency_proto protoreflect.FileDescriptor

var file_data_upload_manager_proto_emergency_proto_rawDesc = []byte{
	0x0a, 0x29, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0x98, 0x02, 0x0a, 0x1e, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x72, 0x6f, 0x70, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73,
	0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x49,
	0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x49, 0x6e, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x6e, 0x61, 0x70, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x6e, 0x61, 0x70, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x22, 0x21, 0x0a, 0x1f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x20, 0x0a, 0x1e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x22, 0x0a, 0x20, 0x52,
	0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x20, 0x0a, 0x1e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x21, 0x0a, 0x1f, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x0a, 0x21, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x24, 0x0a, 0x22, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x37, 0x0a, 0x1d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x20, 0x0a, 0x1e, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x74,
	0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1f, 0x0a, 0x1d, 0x53, 0x74,
	0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x52,
	0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x21, 0x0a,
	0x1f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x1f, 0x0a, 0x1d, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x20, 0x0a, 0x1e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x77, 0x0a, 0x27, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x26,
	0x53, 0x74, 0x6f, 0x70, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x60, 0x0a, 0x28,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5f,
	0x0a, 0x27, 0x50, 0x61, 0x75, 0x73, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67,
	0x65, 0x6e, 0x63, 0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x1b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa5, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x23, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0xbc, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61,
	0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22,
	0xed, 0x01, 0x0a, 0x11, 0x53, 0x6e, 0x61, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x4d, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x6e,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x02, 0x52, 0x06, 0x72, 0x6f, 0x77, 0x49, 0x6e,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x63, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x6e, 0x64, 0x22,
	0x14, 0x0a, 0x12, 0x53, 0x6e, 0x61, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xad, 0x01, 0x0a, 0x07,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x73, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x56, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x20, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x89, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x32, 0x0a,
	0x15, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x6c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x3a, 0x0a, 0x24, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x27, 0x0a,
	0x25, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x34, 0x0a, 0x1e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x21, 0x0a, 0x1f,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32,
	0x9b, 0x17, 0x0a, 0x10, 0x45, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x16, 0x53, 0x74,
	0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x18,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x75,
	0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x1a, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x6f,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x74, 0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a,
	0x16, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa3,
	0x01, 0x0a, 0x20, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x1f, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x21, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xa3, 0x01, 0x0a, 0x20, 0x50, 0x61, 0x75, 0x73, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x35, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6b, 0x0a, 0x0a, 0x53, 0x6e, 0x61, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2d, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xa4, 0x01, 0x0a, 0x1d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0x40, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x1b, 0x5a,
	0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_data_upload_manager_proto_emergency_proto_rawDescOnce sync.Once
	file_data_upload_manager_proto_emergency_proto_rawDescData = file_data_upload_manager_proto_emergency_proto_rawDesc
)

func file_data_upload_manager_proto_emergency_proto_rawDescGZIP() []byte {
	file_data_upload_manager_proto_emergency_proto_rawDescOnce.Do(func() {
		file_data_upload_manager_proto_emergency_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_upload_manager_proto_emergency_proto_rawDescData)
	})
	return file_data_upload_manager_proto_emergency_proto_rawDescData
}

var file_data_upload_manager_proto_emergency_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_data_upload_manager_proto_emergency_proto_goTypes = []interface{}{
	(*StartDataCaptureSessionRequest)(nil),           // 0: carbon.data_upload_manager.StartDataCaptureSessionRequest
	(*StartDataCaptureSessionResponse)(nil),          // 1: carbon.data_upload_manager.StartDataCaptureSessionResponse
	(*StopDataCaptureSessionRequest)(nil),            // 2: carbon.data_upload_manager.StopDataCaptureSessionRequest
	(*StopDataCaptureSessionResponse)(nil),           // 3: carbon.data_upload_manager.StopDataCaptureSessionResponse
	(*ResumeDataCaptureSessionRequest)(nil),          // 4: carbon.data_upload_manager.ResumeDataCaptureSessionRequest
	(*ResumeDataCaptureSessionResponse)(nil),         // 5: carbon.data_upload_manager.ResumeDataCaptureSessionResponse
	(*PauseDataCaptureSessionRequest)(nil),           // 6: carbon.data_upload_manager.PauseDataCaptureSessionRequest
	(*PauseDataCaptureSessionResponse)(nil),          // 7: carbon.data_upload_manager.PauseDataCaptureSessionResponse
	(*CompleteDataCaptureSessionRequest)(nil),        // 8: carbon.data_upload_manager.CompleteDataCaptureSessionRequest
	(*CompleteDataCaptureSessionResponse)(nil),       // 9: carbon.data_upload_manager.CompleteDataCaptureSessionResponse
	(*StartDataUploadSessionRequest)(nil),            // 10: carbon.data_upload_manager.StartDataUploadSessionRequest
	(*StartDataUploadSessionResponse)(nil),           // 11: carbon.data_upload_manager.StartDataUploadSessionResponse
	(*StopDataUploadSessionRequest)(nil),             // 12: carbon.data_upload_manager.StopDataUploadSessionRequest
	(*StopDataUploadSessionResponse)(nil),            // 13: carbon.data_upload_manager.StopDataUploadSessionResponse
	(*ResumeDataUploadSessionRequest)(nil),           // 14: carbon.data_upload_manager.ResumeDataUploadSessionRequest
	(*ResumeDataUploadSessionResponse)(nil),          // 15: carbon.data_upload_manager.ResumeDataUploadSessionResponse
	(*PauseDataUploadSessionRequest)(nil),            // 16: carbon.data_upload_manager.PauseDataUploadSessionRequest
	(*PauseDataUploadSessionResponse)(nil),           // 17: carbon.data_upload_manager.PauseDataUploadSessionResponse
	(*StartBackgroundDataUploadSessionRequest)(nil),  // 18: carbon.data_upload_manager.StartBackgroundDataUploadSessionRequest
	(*StopBackgroundDataUploadSessionRequest)(nil),   // 19: carbon.data_upload_manager.StopBackgroundDataUploadSessionRequest
	(*ResumeBackgroundDataUploadSessionRequest)(nil), // 20: carbon.data_upload_manager.ResumeBackgroundDataUploadSessionRequest
	(*PauseBackgroundDataUploadSessionRequest)(nil),  // 21: carbon.data_upload_manager.PauseBackgroundDataUploadSessionRequest
	(*GetCaptureProgressRequest)(nil),                // 22: carbon.data_upload_manager.GetCaptureProgressRequest
	(*GetCaptureProgressResponse)(nil),               // 23: carbon.data_upload_manager.GetCaptureProgressResponse
	(*GetUploadProgressRequest)(nil),                 // 24: carbon.data_upload_manager.GetUploadProgressRequest
	(*GetUploadProgressResponse)(nil),                // 25: carbon.data_upload_manager.GetUploadProgressResponse
	(*SnapImagesRequest)(nil),                        // 26: carbon.data_upload_manager.SnapImagesRequest
	(*SnapImagesResponse)(nil),                       // 27: carbon.data_upload_manager.SnapImagesResponse
	(*GetSessionsRequest)(nil),                       // 28: carbon.data_upload_manager.GetSessionsRequest
	(*Session)(nil),                                  // 29: carbon.data_upload_manager.Session
	(*GetSessionsResponse)(nil),                      // 30: carbon.data_upload_manager.GetSessionsResponse
	(*GetRegularCaptureStatusRequest)(nil),           // 31: carbon.data_upload_manager.GetRegularCaptureStatusRequest
	(*GetRegularCaptureStatusResponse)(nil),          // 32: carbon.data_upload_manager.GetRegularCaptureStatusResponse
	(*StartWeedingDiagnosticsUploadRequest)(nil),     // 33: carbon.data_upload_manager.StartWeedingDiagnosticsUploadRequest
	(*StartWeedingDiagnosticsUploadResponse)(nil),    // 34: carbon.data_upload_manager.StartWeedingDiagnosticsUploadResponse
	(*StartPlantCaptchaUploadRequest)(nil),           // 35: carbon.data_upload_manager.StartPlantCaptchaUploadRequest
	(*StartPlantCaptchaUploadResponse)(nil),          // 36: carbon.data_upload_manager.StartPlantCaptchaUploadResponse
}
var file_data_upload_manager_proto_emergency_proto_depIdxs = []int32{
	29, // 0: carbon.data_upload_manager.GetSessionsResponse.sessions:type_name -> carbon.data_upload_manager.Session
	0,  // 1: carbon.data_upload_manager.EmergencyService.StartDataCaptureSession:input_type -> carbon.data_upload_manager.StartDataCaptureSessionRequest
	2,  // 2: carbon.data_upload_manager.EmergencyService.StopDataCaptureSession:input_type -> carbon.data_upload_manager.StopDataCaptureSessionRequest
	4,  // 3: carbon.data_upload_manager.EmergencyService.ResumeDataCaptureSession:input_type -> carbon.data_upload_manager.ResumeDataCaptureSessionRequest
	6,  // 4: carbon.data_upload_manager.EmergencyService.PauseDataCaptureSession:input_type -> carbon.data_upload_manager.PauseDataCaptureSessionRequest
	8,  // 5: carbon.data_upload_manager.EmergencyService.CompleteDataCaptureSession:input_type -> carbon.data_upload_manager.CompleteDataCaptureSessionRequest
	10, // 6: carbon.data_upload_manager.EmergencyService.StartDataUploadSession:input_type -> carbon.data_upload_manager.StartDataUploadSessionRequest
	12, // 7: carbon.data_upload_manager.EmergencyService.StopDataUploadSession:input_type -> carbon.data_upload_manager.StopDataUploadSessionRequest
	14, // 8: carbon.data_upload_manager.EmergencyService.ResumeDataUploadSession:input_type -> carbon.data_upload_manager.ResumeDataUploadSessionRequest
	16, // 9: carbon.data_upload_manager.EmergencyService.PauseDataUploadSession:input_type -> carbon.data_upload_manager.PauseDataUploadSessionRequest
	18, // 10: carbon.data_upload_manager.EmergencyService.StartBackgroundDataUploadSession:input_type -> carbon.data_upload_manager.StartBackgroundDataUploadSessionRequest
	19, // 11: carbon.data_upload_manager.EmergencyService.StopBackgroundDataUploadSession:input_type -> carbon.data_upload_manager.StopBackgroundDataUploadSessionRequest
	20, // 12: carbon.data_upload_manager.EmergencyService.ResumeBackgroundDataUploadSession:input_type -> carbon.data_upload_manager.ResumeBackgroundDataUploadSessionRequest
	21, // 13: carbon.data_upload_manager.EmergencyService.PauseBackgroundDataUploadSession:input_type -> carbon.data_upload_manager.PauseBackgroundDataUploadSessionRequest
	22, // 14: carbon.data_upload_manager.EmergencyService.GetCaptureProgress:input_type -> carbon.data_upload_manager.GetCaptureProgressRequest
	24, // 15: carbon.data_upload_manager.EmergencyService.GetUploadProgress:input_type -> carbon.data_upload_manager.GetUploadProgressRequest
	26, // 16: carbon.data_upload_manager.EmergencyService.SnapImages:input_type -> carbon.data_upload_manager.SnapImagesRequest
	28, // 17: carbon.data_upload_manager.EmergencyService.GetSessions:input_type -> carbon.data_upload_manager.GetSessionsRequest
	31, // 18: carbon.data_upload_manager.EmergencyService.GetRegularCaptureStatus:input_type -> carbon.data_upload_manager.GetRegularCaptureStatusRequest
	33, // 19: carbon.data_upload_manager.EmergencyService.StartWeedingDiagnosticsUpload:input_type -> carbon.data_upload_manager.StartWeedingDiagnosticsUploadRequest
	35, // 20: carbon.data_upload_manager.EmergencyService.StartPlantCaptchaUpload:input_type -> carbon.data_upload_manager.StartPlantCaptchaUploadRequest
	1,  // 21: carbon.data_upload_manager.EmergencyService.StartDataCaptureSession:output_type -> carbon.data_upload_manager.StartDataCaptureSessionResponse
	3,  // 22: carbon.data_upload_manager.EmergencyService.StopDataCaptureSession:output_type -> carbon.data_upload_manager.StopDataCaptureSessionResponse
	5,  // 23: carbon.data_upload_manager.EmergencyService.ResumeDataCaptureSession:output_type -> carbon.data_upload_manager.ResumeDataCaptureSessionResponse
	7,  // 24: carbon.data_upload_manager.EmergencyService.PauseDataCaptureSession:output_type -> carbon.data_upload_manager.PauseDataCaptureSessionResponse
	9,  // 25: carbon.data_upload_manager.EmergencyService.CompleteDataCaptureSession:output_type -> carbon.data_upload_manager.CompleteDataCaptureSessionResponse
	11, // 26: carbon.data_upload_manager.EmergencyService.StartDataUploadSession:output_type -> carbon.data_upload_manager.StartDataUploadSessionResponse
	13, // 27: carbon.data_upload_manager.EmergencyService.StopDataUploadSession:output_type -> carbon.data_upload_manager.StopDataUploadSessionResponse
	15, // 28: carbon.data_upload_manager.EmergencyService.ResumeDataUploadSession:output_type -> carbon.data_upload_manager.ResumeDataUploadSessionResponse
	17, // 29: carbon.data_upload_manager.EmergencyService.PauseDataUploadSession:output_type -> carbon.data_upload_manager.PauseDataUploadSessionResponse
	11, // 30: carbon.data_upload_manager.EmergencyService.StartBackgroundDataUploadSession:output_type -> carbon.data_upload_manager.StartDataUploadSessionResponse
	13, // 31: carbon.data_upload_manager.EmergencyService.StopBackgroundDataUploadSession:output_type -> carbon.data_upload_manager.StopDataUploadSessionResponse
	15, // 32: carbon.data_upload_manager.EmergencyService.ResumeBackgroundDataUploadSession:output_type -> carbon.data_upload_manager.ResumeDataUploadSessionResponse
	17, // 33: carbon.data_upload_manager.EmergencyService.PauseBackgroundDataUploadSession:output_type -> carbon.data_upload_manager.PauseDataUploadSessionResponse
	23, // 34: carbon.data_upload_manager.EmergencyService.GetCaptureProgress:output_type -> carbon.data_upload_manager.GetCaptureProgressResponse
	25, // 35: carbon.data_upload_manager.EmergencyService.GetUploadProgress:output_type -> carbon.data_upload_manager.GetUploadProgressResponse
	27, // 36: carbon.data_upload_manager.EmergencyService.SnapImages:output_type -> carbon.data_upload_manager.SnapImagesResponse
	30, // 37: carbon.data_upload_manager.EmergencyService.GetSessions:output_type -> carbon.data_upload_manager.GetSessionsResponse
	32, // 38: carbon.data_upload_manager.EmergencyService.GetRegularCaptureStatus:output_type -> carbon.data_upload_manager.GetRegularCaptureStatusResponse
	34, // 39: carbon.data_upload_manager.EmergencyService.StartWeedingDiagnosticsUpload:output_type -> carbon.data_upload_manager.StartWeedingDiagnosticsUploadResponse
	36, // 40: carbon.data_upload_manager.EmergencyService.StartPlantCaptchaUpload:output_type -> carbon.data_upload_manager.StartPlantCaptchaUploadResponse
	21, // [21:41] is the sub-list for method output_type
	1,  // [1:21] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_data_upload_manager_proto_emergency_proto_init() }
func file_data_upload_manager_proto_emergency_proto_init() {
	if File_data_upload_manager_proto_emergency_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_upload_manager_proto_emergency_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDataCaptureSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDataCaptureSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDataCaptureSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDataCaptureSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeDataCaptureSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeDataCaptureSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseDataCaptureSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseDataCaptureSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteDataCaptureSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteDataCaptureSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDataUploadSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDataUploadSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeDataUploadSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseDataUploadSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartBackgroundDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopBackgroundDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeBackgroundDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseBackgroundDataUploadSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaptureProgressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaptureProgressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUploadProgressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUploadProgressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapImagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Session); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRegularCaptureStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRegularCaptureStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartWeedingDiagnosticsUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartWeedingDiagnosticsUploadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartPlantCaptchaUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_upload_manager_proto_emergency_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartPlantCaptchaUploadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_data_upload_manager_proto_emergency_proto_msgTypes[26].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_upload_manager_proto_emergency_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_upload_manager_proto_emergency_proto_goTypes,
		DependencyIndexes: file_data_upload_manager_proto_emergency_proto_depIdxs,
		MessageInfos:      file_data_upload_manager_proto_emergency_proto_msgTypes,
	}.Build()
	File_data_upload_manager_proto_emergency_proto = out.File
	file_data_upload_manager_proto_emergency_proto_rawDesc = nil
	file_data_upload_manager_proto_emergency_proto_goTypes = nil
	file_data_upload_manager_proto_emergency_proto_depIdxs = nil
}

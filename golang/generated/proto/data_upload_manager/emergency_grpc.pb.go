// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: data_upload_manager/proto/emergency.proto

package data_upload_manager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	EmergencyService_StartDataCaptureSession_FullMethodName           = "/carbon.data_upload_manager.EmergencyService/StartDataCaptureSession"
	EmergencyService_StopDataCaptureSession_FullMethodName            = "/carbon.data_upload_manager.EmergencyService/StopDataCaptureSession"
	EmergencyService_ResumeDataCaptureSession_FullMethodName          = "/carbon.data_upload_manager.EmergencyService/ResumeDataCaptureSession"
	EmergencyService_PauseDataCaptureSession_FullMethodName           = "/carbon.data_upload_manager.EmergencyService/PauseDataCaptureSession"
	EmergencyService_CompleteDataCaptureSession_FullMethodName        = "/carbon.data_upload_manager.EmergencyService/CompleteDataCaptureSession"
	EmergencyService_StartDataUploadSession_FullMethodName            = "/carbon.data_upload_manager.EmergencyService/StartDataUploadSession"
	EmergencyService_StopDataUploadSession_FullMethodName             = "/carbon.data_upload_manager.EmergencyService/StopDataUploadSession"
	EmergencyService_ResumeDataUploadSession_FullMethodName           = "/carbon.data_upload_manager.EmergencyService/ResumeDataUploadSession"
	EmergencyService_PauseDataUploadSession_FullMethodName            = "/carbon.data_upload_manager.EmergencyService/PauseDataUploadSession"
	EmergencyService_StartBackgroundDataUploadSession_FullMethodName  = "/carbon.data_upload_manager.EmergencyService/StartBackgroundDataUploadSession"
	EmergencyService_StopBackgroundDataUploadSession_FullMethodName   = "/carbon.data_upload_manager.EmergencyService/StopBackgroundDataUploadSession"
	EmergencyService_ResumeBackgroundDataUploadSession_FullMethodName = "/carbon.data_upload_manager.EmergencyService/ResumeBackgroundDataUploadSession"
	EmergencyService_PauseBackgroundDataUploadSession_FullMethodName  = "/carbon.data_upload_manager.EmergencyService/PauseBackgroundDataUploadSession"
	EmergencyService_GetCaptureProgress_FullMethodName                = "/carbon.data_upload_manager.EmergencyService/GetCaptureProgress"
	EmergencyService_GetUploadProgress_FullMethodName                 = "/carbon.data_upload_manager.EmergencyService/GetUploadProgress"
	EmergencyService_SnapImages_FullMethodName                        = "/carbon.data_upload_manager.EmergencyService/SnapImages"
	EmergencyService_GetSessions_FullMethodName                       = "/carbon.data_upload_manager.EmergencyService/GetSessions"
	EmergencyService_GetRegularCaptureStatus_FullMethodName           = "/carbon.data_upload_manager.EmergencyService/GetRegularCaptureStatus"
	EmergencyService_StartWeedingDiagnosticsUpload_FullMethodName     = "/carbon.data_upload_manager.EmergencyService/StartWeedingDiagnosticsUpload"
	EmergencyService_StartPlantCaptchaUpload_FullMethodName           = "/carbon.data_upload_manager.EmergencyService/StartPlantCaptchaUpload"
)

// EmergencyServiceClient is the client API for EmergencyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmergencyServiceClient interface {
	StartDataCaptureSession(ctx context.Context, in *StartDataCaptureSessionRequest, opts ...grpc.CallOption) (*StartDataCaptureSessionResponse, error)
	StopDataCaptureSession(ctx context.Context, in *StopDataCaptureSessionRequest, opts ...grpc.CallOption) (*StopDataCaptureSessionResponse, error)
	ResumeDataCaptureSession(ctx context.Context, in *ResumeDataCaptureSessionRequest, opts ...grpc.CallOption) (*ResumeDataCaptureSessionResponse, error)
	PauseDataCaptureSession(ctx context.Context, in *PauseDataCaptureSessionRequest, opts ...grpc.CallOption) (*PauseDataCaptureSessionResponse, error)
	CompleteDataCaptureSession(ctx context.Context, in *CompleteDataCaptureSessionRequest, opts ...grpc.CallOption) (*CompleteDataCaptureSessionResponse, error)
	StartDataUploadSession(ctx context.Context, in *StartDataUploadSessionRequest, opts ...grpc.CallOption) (*StartDataUploadSessionResponse, error)
	StopDataUploadSession(ctx context.Context, in *StopDataUploadSessionRequest, opts ...grpc.CallOption) (*StopDataUploadSessionResponse, error)
	ResumeDataUploadSession(ctx context.Context, in *ResumeDataUploadSessionRequest, opts ...grpc.CallOption) (*ResumeDataUploadSessionResponse, error)
	PauseDataUploadSession(ctx context.Context, in *PauseDataUploadSessionRequest, opts ...grpc.CallOption) (*PauseDataUploadSessionResponse, error)
	StartBackgroundDataUploadSession(ctx context.Context, in *StartBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*StartDataUploadSessionResponse, error)
	StopBackgroundDataUploadSession(ctx context.Context, in *StopBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*StopDataUploadSessionResponse, error)
	ResumeBackgroundDataUploadSession(ctx context.Context, in *ResumeBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*ResumeDataUploadSessionResponse, error)
	PauseBackgroundDataUploadSession(ctx context.Context, in *PauseBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*PauseDataUploadSessionResponse, error)
	GetCaptureProgress(ctx context.Context, in *GetCaptureProgressRequest, opts ...grpc.CallOption) (*GetCaptureProgressResponse, error)
	GetUploadProgress(ctx context.Context, in *GetUploadProgressRequest, opts ...grpc.CallOption) (*GetUploadProgressResponse, error)
	SnapImages(ctx context.Context, in *SnapImagesRequest, opts ...grpc.CallOption) (*SnapImagesResponse, error)
	GetSessions(ctx context.Context, in *GetSessionsRequest, opts ...grpc.CallOption) (*GetSessionsResponse, error)
	GetRegularCaptureStatus(ctx context.Context, in *GetRegularCaptureStatusRequest, opts ...grpc.CallOption) (*GetRegularCaptureStatusResponse, error)
	StartWeedingDiagnosticsUpload(ctx context.Context, in *StartWeedingDiagnosticsUploadRequest, opts ...grpc.CallOption) (*StartWeedingDiagnosticsUploadResponse, error)
	StartPlantCaptchaUpload(ctx context.Context, in *StartPlantCaptchaUploadRequest, opts ...grpc.CallOption) (*StartPlantCaptchaUploadResponse, error)
}

type emergencyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEmergencyServiceClient(cc grpc.ClientConnInterface) EmergencyServiceClient {
	return &emergencyServiceClient{cc}
}

func (c *emergencyServiceClient) StartDataCaptureSession(ctx context.Context, in *StartDataCaptureSessionRequest, opts ...grpc.CallOption) (*StartDataCaptureSessionResponse, error) {
	out := new(StartDataCaptureSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StartDataCaptureSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StopDataCaptureSession(ctx context.Context, in *StopDataCaptureSessionRequest, opts ...grpc.CallOption) (*StopDataCaptureSessionResponse, error) {
	out := new(StopDataCaptureSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StopDataCaptureSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) ResumeDataCaptureSession(ctx context.Context, in *ResumeDataCaptureSessionRequest, opts ...grpc.CallOption) (*ResumeDataCaptureSessionResponse, error) {
	out := new(ResumeDataCaptureSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_ResumeDataCaptureSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) PauseDataCaptureSession(ctx context.Context, in *PauseDataCaptureSessionRequest, opts ...grpc.CallOption) (*PauseDataCaptureSessionResponse, error) {
	out := new(PauseDataCaptureSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_PauseDataCaptureSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) CompleteDataCaptureSession(ctx context.Context, in *CompleteDataCaptureSessionRequest, opts ...grpc.CallOption) (*CompleteDataCaptureSessionResponse, error) {
	out := new(CompleteDataCaptureSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_CompleteDataCaptureSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StartDataUploadSession(ctx context.Context, in *StartDataUploadSessionRequest, opts ...grpc.CallOption) (*StartDataUploadSessionResponse, error) {
	out := new(StartDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StartDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StopDataUploadSession(ctx context.Context, in *StopDataUploadSessionRequest, opts ...grpc.CallOption) (*StopDataUploadSessionResponse, error) {
	out := new(StopDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StopDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) ResumeDataUploadSession(ctx context.Context, in *ResumeDataUploadSessionRequest, opts ...grpc.CallOption) (*ResumeDataUploadSessionResponse, error) {
	out := new(ResumeDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_ResumeDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) PauseDataUploadSession(ctx context.Context, in *PauseDataUploadSessionRequest, opts ...grpc.CallOption) (*PauseDataUploadSessionResponse, error) {
	out := new(PauseDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_PauseDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StartBackgroundDataUploadSession(ctx context.Context, in *StartBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*StartDataUploadSessionResponse, error) {
	out := new(StartDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StartBackgroundDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StopBackgroundDataUploadSession(ctx context.Context, in *StopBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*StopDataUploadSessionResponse, error) {
	out := new(StopDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StopBackgroundDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) ResumeBackgroundDataUploadSession(ctx context.Context, in *ResumeBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*ResumeDataUploadSessionResponse, error) {
	out := new(ResumeDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_ResumeBackgroundDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) PauseBackgroundDataUploadSession(ctx context.Context, in *PauseBackgroundDataUploadSessionRequest, opts ...grpc.CallOption) (*PauseDataUploadSessionResponse, error) {
	out := new(PauseDataUploadSessionResponse)
	err := c.cc.Invoke(ctx, EmergencyService_PauseBackgroundDataUploadSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) GetCaptureProgress(ctx context.Context, in *GetCaptureProgressRequest, opts ...grpc.CallOption) (*GetCaptureProgressResponse, error) {
	out := new(GetCaptureProgressResponse)
	err := c.cc.Invoke(ctx, EmergencyService_GetCaptureProgress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) GetUploadProgress(ctx context.Context, in *GetUploadProgressRequest, opts ...grpc.CallOption) (*GetUploadProgressResponse, error) {
	out := new(GetUploadProgressResponse)
	err := c.cc.Invoke(ctx, EmergencyService_GetUploadProgress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) SnapImages(ctx context.Context, in *SnapImagesRequest, opts ...grpc.CallOption) (*SnapImagesResponse, error) {
	out := new(SnapImagesResponse)
	err := c.cc.Invoke(ctx, EmergencyService_SnapImages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) GetSessions(ctx context.Context, in *GetSessionsRequest, opts ...grpc.CallOption) (*GetSessionsResponse, error) {
	out := new(GetSessionsResponse)
	err := c.cc.Invoke(ctx, EmergencyService_GetSessions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) GetRegularCaptureStatus(ctx context.Context, in *GetRegularCaptureStatusRequest, opts ...grpc.CallOption) (*GetRegularCaptureStatusResponse, error) {
	out := new(GetRegularCaptureStatusResponse)
	err := c.cc.Invoke(ctx, EmergencyService_GetRegularCaptureStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StartWeedingDiagnosticsUpload(ctx context.Context, in *StartWeedingDiagnosticsUploadRequest, opts ...grpc.CallOption) (*StartWeedingDiagnosticsUploadResponse, error) {
	out := new(StartWeedingDiagnosticsUploadResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StartWeedingDiagnosticsUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emergencyServiceClient) StartPlantCaptchaUpload(ctx context.Context, in *StartPlantCaptchaUploadRequest, opts ...grpc.CallOption) (*StartPlantCaptchaUploadResponse, error) {
	out := new(StartPlantCaptchaUploadResponse)
	err := c.cc.Invoke(ctx, EmergencyService_StartPlantCaptchaUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmergencyServiceServer is the server API for EmergencyService service.
// All implementations must embed UnimplementedEmergencyServiceServer
// for forward compatibility
type EmergencyServiceServer interface {
	StartDataCaptureSession(context.Context, *StartDataCaptureSessionRequest) (*StartDataCaptureSessionResponse, error)
	StopDataCaptureSession(context.Context, *StopDataCaptureSessionRequest) (*StopDataCaptureSessionResponse, error)
	ResumeDataCaptureSession(context.Context, *ResumeDataCaptureSessionRequest) (*ResumeDataCaptureSessionResponse, error)
	PauseDataCaptureSession(context.Context, *PauseDataCaptureSessionRequest) (*PauseDataCaptureSessionResponse, error)
	CompleteDataCaptureSession(context.Context, *CompleteDataCaptureSessionRequest) (*CompleteDataCaptureSessionResponse, error)
	StartDataUploadSession(context.Context, *StartDataUploadSessionRequest) (*StartDataUploadSessionResponse, error)
	StopDataUploadSession(context.Context, *StopDataUploadSessionRequest) (*StopDataUploadSessionResponse, error)
	ResumeDataUploadSession(context.Context, *ResumeDataUploadSessionRequest) (*ResumeDataUploadSessionResponse, error)
	PauseDataUploadSession(context.Context, *PauseDataUploadSessionRequest) (*PauseDataUploadSessionResponse, error)
	StartBackgroundDataUploadSession(context.Context, *StartBackgroundDataUploadSessionRequest) (*StartDataUploadSessionResponse, error)
	StopBackgroundDataUploadSession(context.Context, *StopBackgroundDataUploadSessionRequest) (*StopDataUploadSessionResponse, error)
	ResumeBackgroundDataUploadSession(context.Context, *ResumeBackgroundDataUploadSessionRequest) (*ResumeDataUploadSessionResponse, error)
	PauseBackgroundDataUploadSession(context.Context, *PauseBackgroundDataUploadSessionRequest) (*PauseDataUploadSessionResponse, error)
	GetCaptureProgress(context.Context, *GetCaptureProgressRequest) (*GetCaptureProgressResponse, error)
	GetUploadProgress(context.Context, *GetUploadProgressRequest) (*GetUploadProgressResponse, error)
	SnapImages(context.Context, *SnapImagesRequest) (*SnapImagesResponse, error)
	GetSessions(context.Context, *GetSessionsRequest) (*GetSessionsResponse, error)
	GetRegularCaptureStatus(context.Context, *GetRegularCaptureStatusRequest) (*GetRegularCaptureStatusResponse, error)
	StartWeedingDiagnosticsUpload(context.Context, *StartWeedingDiagnosticsUploadRequest) (*StartWeedingDiagnosticsUploadResponse, error)
	StartPlantCaptchaUpload(context.Context, *StartPlantCaptchaUploadRequest) (*StartPlantCaptchaUploadResponse, error)
	mustEmbedUnimplementedEmergencyServiceServer()
}

// UnimplementedEmergencyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEmergencyServiceServer struct {
}

func (UnimplementedEmergencyServiceServer) StartDataCaptureSession(context.Context, *StartDataCaptureSessionRequest) (*StartDataCaptureSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDataCaptureSession not implemented")
}
func (UnimplementedEmergencyServiceServer) StopDataCaptureSession(context.Context, *StopDataCaptureSessionRequest) (*StopDataCaptureSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDataCaptureSession not implemented")
}
func (UnimplementedEmergencyServiceServer) ResumeDataCaptureSession(context.Context, *ResumeDataCaptureSessionRequest) (*ResumeDataCaptureSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeDataCaptureSession not implemented")
}
func (UnimplementedEmergencyServiceServer) PauseDataCaptureSession(context.Context, *PauseDataCaptureSessionRequest) (*PauseDataCaptureSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseDataCaptureSession not implemented")
}
func (UnimplementedEmergencyServiceServer) CompleteDataCaptureSession(context.Context, *CompleteDataCaptureSessionRequest) (*CompleteDataCaptureSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteDataCaptureSession not implemented")
}
func (UnimplementedEmergencyServiceServer) StartDataUploadSession(context.Context, *StartDataUploadSessionRequest) (*StartDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) StopDataUploadSession(context.Context, *StopDataUploadSessionRequest) (*StopDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) ResumeDataUploadSession(context.Context, *ResumeDataUploadSessionRequest) (*ResumeDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) PauseDataUploadSession(context.Context, *PauseDataUploadSessionRequest) (*PauseDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) StartBackgroundDataUploadSession(context.Context, *StartBackgroundDataUploadSessionRequest) (*StartDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBackgroundDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) StopBackgroundDataUploadSession(context.Context, *StopBackgroundDataUploadSessionRequest) (*StopDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopBackgroundDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) ResumeBackgroundDataUploadSession(context.Context, *ResumeBackgroundDataUploadSessionRequest) (*ResumeDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResumeBackgroundDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) PauseBackgroundDataUploadSession(context.Context, *PauseBackgroundDataUploadSessionRequest) (*PauseDataUploadSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseBackgroundDataUploadSession not implemented")
}
func (UnimplementedEmergencyServiceServer) GetCaptureProgress(context.Context, *GetCaptureProgressRequest) (*GetCaptureProgressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCaptureProgress not implemented")
}
func (UnimplementedEmergencyServiceServer) GetUploadProgress(context.Context, *GetUploadProgressRequest) (*GetUploadProgressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUploadProgress not implemented")
}
func (UnimplementedEmergencyServiceServer) SnapImages(context.Context, *SnapImagesRequest) (*SnapImagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SnapImages not implemented")
}
func (UnimplementedEmergencyServiceServer) GetSessions(context.Context, *GetSessionsRequest) (*GetSessionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessions not implemented")
}
func (UnimplementedEmergencyServiceServer) GetRegularCaptureStatus(context.Context, *GetRegularCaptureStatusRequest) (*GetRegularCaptureStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegularCaptureStatus not implemented")
}
func (UnimplementedEmergencyServiceServer) StartWeedingDiagnosticsUpload(context.Context, *StartWeedingDiagnosticsUploadRequest) (*StartWeedingDiagnosticsUploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartWeedingDiagnosticsUpload not implemented")
}
func (UnimplementedEmergencyServiceServer) StartPlantCaptchaUpload(context.Context, *StartPlantCaptchaUploadRequest) (*StartPlantCaptchaUploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPlantCaptchaUpload not implemented")
}
func (UnimplementedEmergencyServiceServer) mustEmbedUnimplementedEmergencyServiceServer() {}

// UnsafeEmergencyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmergencyServiceServer will
// result in compilation errors.
type UnsafeEmergencyServiceServer interface {
	mustEmbedUnimplementedEmergencyServiceServer()
}

func RegisterEmergencyServiceServer(s grpc.ServiceRegistrar, srv EmergencyServiceServer) {
	s.RegisterService(&EmergencyService_ServiceDesc, srv)
}

func _EmergencyService_StartDataCaptureSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDataCaptureSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StartDataCaptureSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StartDataCaptureSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StartDataCaptureSession(ctx, req.(*StartDataCaptureSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StopDataCaptureSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDataCaptureSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StopDataCaptureSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StopDataCaptureSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StopDataCaptureSession(ctx, req.(*StopDataCaptureSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_ResumeDataCaptureSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResumeDataCaptureSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).ResumeDataCaptureSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_ResumeDataCaptureSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).ResumeDataCaptureSession(ctx, req.(*ResumeDataCaptureSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_PauseDataCaptureSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseDataCaptureSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).PauseDataCaptureSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_PauseDataCaptureSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).PauseDataCaptureSession(ctx, req.(*PauseDataCaptureSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_CompleteDataCaptureSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteDataCaptureSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).CompleteDataCaptureSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_CompleteDataCaptureSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).CompleteDataCaptureSession(ctx, req.(*CompleteDataCaptureSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StartDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StartDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StartDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StartDataUploadSession(ctx, req.(*StartDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StopDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StopDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StopDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StopDataUploadSession(ctx, req.(*StopDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_ResumeDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResumeDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).ResumeDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_ResumeDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).ResumeDataUploadSession(ctx, req.(*ResumeDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_PauseDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).PauseDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_PauseDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).PauseDataUploadSession(ctx, req.(*PauseDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StartBackgroundDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartBackgroundDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StartBackgroundDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StartBackgroundDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StartBackgroundDataUploadSession(ctx, req.(*StartBackgroundDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StopBackgroundDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopBackgroundDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StopBackgroundDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StopBackgroundDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StopBackgroundDataUploadSession(ctx, req.(*StopBackgroundDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_ResumeBackgroundDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResumeBackgroundDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).ResumeBackgroundDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_ResumeBackgroundDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).ResumeBackgroundDataUploadSession(ctx, req.(*ResumeBackgroundDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_PauseBackgroundDataUploadSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseBackgroundDataUploadSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).PauseBackgroundDataUploadSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_PauseBackgroundDataUploadSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).PauseBackgroundDataUploadSession(ctx, req.(*PauseBackgroundDataUploadSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_GetCaptureProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCaptureProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).GetCaptureProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_GetCaptureProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).GetCaptureProgress(ctx, req.(*GetCaptureProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_GetUploadProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUploadProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).GetUploadProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_GetUploadProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).GetUploadProgress(ctx, req.(*GetUploadProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_SnapImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SnapImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).SnapImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_SnapImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).SnapImages(ctx, req.(*SnapImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_GetSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).GetSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_GetSessions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).GetSessions(ctx, req.(*GetSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_GetRegularCaptureStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegularCaptureStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).GetRegularCaptureStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_GetRegularCaptureStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).GetRegularCaptureStatus(ctx, req.(*GetRegularCaptureStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StartWeedingDiagnosticsUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartWeedingDiagnosticsUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StartWeedingDiagnosticsUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StartWeedingDiagnosticsUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StartWeedingDiagnosticsUpload(ctx, req.(*StartWeedingDiagnosticsUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmergencyService_StartPlantCaptchaUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPlantCaptchaUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmergencyServiceServer).StartPlantCaptchaUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EmergencyService_StartPlantCaptchaUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmergencyServiceServer).StartPlantCaptchaUpload(ctx, req.(*StartPlantCaptchaUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EmergencyService_ServiceDesc is the grpc.ServiceDesc for EmergencyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EmergencyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.data_upload_manager.EmergencyService",
	HandlerType: (*EmergencyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartDataCaptureSession",
			Handler:    _EmergencyService_StartDataCaptureSession_Handler,
		},
		{
			MethodName: "StopDataCaptureSession",
			Handler:    _EmergencyService_StopDataCaptureSession_Handler,
		},
		{
			MethodName: "ResumeDataCaptureSession",
			Handler:    _EmergencyService_ResumeDataCaptureSession_Handler,
		},
		{
			MethodName: "PauseDataCaptureSession",
			Handler:    _EmergencyService_PauseDataCaptureSession_Handler,
		},
		{
			MethodName: "CompleteDataCaptureSession",
			Handler:    _EmergencyService_CompleteDataCaptureSession_Handler,
		},
		{
			MethodName: "StartDataUploadSession",
			Handler:    _EmergencyService_StartDataUploadSession_Handler,
		},
		{
			MethodName: "StopDataUploadSession",
			Handler:    _EmergencyService_StopDataUploadSession_Handler,
		},
		{
			MethodName: "ResumeDataUploadSession",
			Handler:    _EmergencyService_ResumeDataUploadSession_Handler,
		},
		{
			MethodName: "PauseDataUploadSession",
			Handler:    _EmergencyService_PauseDataUploadSession_Handler,
		},
		{
			MethodName: "StartBackgroundDataUploadSession",
			Handler:    _EmergencyService_StartBackgroundDataUploadSession_Handler,
		},
		{
			MethodName: "StopBackgroundDataUploadSession",
			Handler:    _EmergencyService_StopBackgroundDataUploadSession_Handler,
		},
		{
			MethodName: "ResumeBackgroundDataUploadSession",
			Handler:    _EmergencyService_ResumeBackgroundDataUploadSession_Handler,
		},
		{
			MethodName: "PauseBackgroundDataUploadSession",
			Handler:    _EmergencyService_PauseBackgroundDataUploadSession_Handler,
		},
		{
			MethodName: "GetCaptureProgress",
			Handler:    _EmergencyService_GetCaptureProgress_Handler,
		},
		{
			MethodName: "GetUploadProgress",
			Handler:    _EmergencyService_GetUploadProgress_Handler,
		},
		{
			MethodName: "SnapImages",
			Handler:    _EmergencyService_SnapImages_Handler,
		},
		{
			MethodName: "GetSessions",
			Handler:    _EmergencyService_GetSessions_Handler,
		},
		{
			MethodName: "GetRegularCaptureStatus",
			Handler:    _EmergencyService_GetRegularCaptureStatus_Handler,
		},
		{
			MethodName: "StartWeedingDiagnosticsUpload",
			Handler:    _EmergencyService_StartWeedingDiagnosticsUpload_Handler,
		},
		{
			MethodName: "StartPlantCaptchaUpload",
			Handler:    _EmergencyService_StartPlantCaptchaUpload_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "data_upload_manager/proto/emergency.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/model_receiver/model_receiver.proto

package model_receiver

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DownloadModelArtifactRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId          string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ArtifactName     string `protobuf:"bytes,2,opt,name=artifact_name,json=artifactName,proto3" json:"artifact_name,omitempty"`
	ArtifactContents []byte `protobuf:"bytes,4,opt,name=artifact_contents,json=artifactContents,proto3" json:"artifact_contents,omitempty"`
}

func (x *DownloadModelArtifactRequest) Reset() {
	*x = DownloadModelArtifactRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadModelArtifactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadModelArtifactRequest) ProtoMessage() {}

func (x *DownloadModelArtifactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadModelArtifactRequest.ProtoReflect.Descriptor instead.
func (*DownloadModelArtifactRequest) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{0}
}

func (x *DownloadModelArtifactRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *DownloadModelArtifactRequest) GetArtifactName() string {
	if x != nil {
		return x.ArtifactName
	}
	return ""
}

func (x *DownloadModelArtifactRequest) GetArtifactContents() []byte {
	if x != nil {
		return x.ArtifactContents
	}
	return nil
}

type DownloadModelMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId          string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	MetadataContents []byte `protobuf:"bytes,2,opt,name=metadata_contents,json=metadataContents,proto3" json:"metadata_contents,omitempty"`
}

func (x *DownloadModelMetadataRequest) Reset() {
	*x = DownloadModelMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadModelMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadModelMetadataRequest) ProtoMessage() {}

func (x *DownloadModelMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadModelMetadataRequest.ProtoReflect.Descriptor instead.
func (*DownloadModelMetadataRequest) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{1}
}

func (x *DownloadModelMetadataRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *DownloadModelMetadataRequest) GetMetadataContents() []byte {
	if x != nil {
		return x.MetadataContents
	}
	return nil
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{2}
}

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ArtifactIds []string `protobuf:"bytes,4,rep,name=artifact_ids,json=artifactIds,proto3" json:"artifact_ids,omitempty"`
	ModelSha    string   `protobuf:"bytes,2,opt,name=model_sha,json=modelSha,proto3" json:"model_sha,omitempty"`
	MetadataSha string   `protobuf:"bytes,3,opt,name=metadata_sha,json=metadataSha,proto3" json:"metadata_sha,omitempty"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{3}
}

func (x *Model) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Model) GetArtifactIds() []string {
	if x != nil {
		return x.ArtifactIds
	}
	return nil
}

func (x *Model) GetModelSha() string {
	if x != nil {
		return x.ModelSha
	}
	return ""
}

func (x *Model) GetMetadataSha() string {
	if x != nil {
		return x.MetadataSha
	}
	return ""
}

type DownloadedModelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*Model `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *DownloadedModelResponse) Reset() {
	*x = DownloadedModelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadedModelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadedModelResponse) ProtoMessage() {}

func (x *DownloadedModelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadedModelResponse.ProtoReflect.Descriptor instead.
func (*DownloadedModelResponse) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{4}
}

func (x *DownloadedModelResponse) GetModels() []*Model {
	if x != nil {
		return x.Models
	}
	return nil
}

type CleanupModelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId []string `protobuf:"bytes,1,rep,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
}

func (x *CleanupModelsRequest) Reset() {
	*x = CleanupModelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupModelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupModelsRequest) ProtoMessage() {}

func (x *CleanupModelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupModelsRequest.ProtoReflect.Descriptor instead.
func (*CleanupModelsRequest) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{5}
}

func (x *CleanupModelsRequest) GetModelId() []string {
	if x != nil {
		return x.ModelId
	}
	return nil
}

// Chip related messages
type DownloadChipRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChipId   string `protobuf:"bytes,1,opt,name=chip_id,json=chipId,proto3" json:"chip_id,omitempty"`
	Contents []byte `protobuf:"bytes,2,opt,name=contents,proto3" json:"contents,omitempty"`
}

func (x *DownloadChipRequest) Reset() {
	*x = DownloadChipRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadChipRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadChipRequest) ProtoMessage() {}

func (x *DownloadChipRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadChipRequest.ProtoReflect.Descriptor instead.
func (*DownloadChipRequest) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{6}
}

func (x *DownloadChipRequest) GetChipId() string {
	if x != nil {
		return x.ChipId
	}
	return ""
}

func (x *DownloadChipRequest) GetContents() []byte {
	if x != nil {
		return x.Contents
	}
	return nil
}

type DownloadChipMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChipId           string `protobuf:"bytes,1,opt,name=chip_id,json=chipId,proto3" json:"chip_id,omitempty"`
	MetadataContents []byte `protobuf:"bytes,2,opt,name=metadata_contents,json=metadataContents,proto3" json:"metadata_contents,omitempty"`
}

func (x *DownloadChipMetadataRequest) Reset() {
	*x = DownloadChipMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadChipMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadChipMetadataRequest) ProtoMessage() {}

func (x *DownloadChipMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadChipMetadataRequest.ProtoReflect.Descriptor instead.
func (*DownloadChipMetadataRequest) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{7}
}

func (x *DownloadChipMetadataRequest) GetChipId() string {
	if x != nil {
		return x.ChipId
	}
	return ""
}

func (x *DownloadChipMetadataRequest) GetMetadataContents() []byte {
	if x != nil {
		return x.MetadataContents
	}
	return nil
}

type Chip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *Chip) Reset() {
	*x = Chip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chip) ProtoMessage() {}

func (x *Chip) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chip.ProtoReflect.Descriptor instead.
func (*Chip) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{8}
}

func (x *Chip) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetDownloadedChipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chips []*Chip `protobuf:"bytes,1,rep,name=chips,proto3" json:"chips,omitempty"`
}

func (x *GetDownloadedChipsResponse) Reset() {
	*x = GetDownloadedChipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDownloadedChipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadedChipsResponse) ProtoMessage() {}

func (x *GetDownloadedChipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadedChipsResponse.ProtoReflect.Descriptor instead.
func (*GetDownloadedChipsResponse) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{9}
}

func (x *GetDownloadedChipsResponse) GetChips() []*Chip {
	if x != nil {
		return x.Chips
	}
	return nil
}

type RemoveChipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChipId []string `protobuf:"bytes,1,rep,name=chip_id,json=chipId,proto3" json:"chip_id,omitempty"`
}

func (x *RemoveChipsRequest) Reset() {
	*x = RemoveChipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveChipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveChipsRequest) ProtoMessage() {}

func (x *RemoveChipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_model_receiver_model_receiver_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveChipsRequest.ProtoReflect.Descriptor instead.
func (*RemoveChipsRequest) Descriptor() ([]byte, []int) {
	return file_proto_model_receiver_model_receiver_proto_rawDescGZIP(), []int{10}
}

func (x *RemoveChipsRequest) GetChipId() []string {
	if x != nil {
		return x.ChipId
	}
	return nil
}

var File_proto_model_receiver_model_receiver_proto protoreflect.FileDescriptor

var file_proto_model_receiver_model_receiver_proto_rawDesc = []byte{
	0x0a, 0x29, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x22, 0x8b, 0x01, 0x0a, 0x1c, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10,
	0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x66, 0x0a, 0x1c, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x7a, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x68, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x68, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x68, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x53, 0x68, 0x61, 0x22, 0x4f, 0x0a,
	0x17, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x31,
	0x0a, 0x14, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x64, 0x22, 0x4a, 0x0a, 0x13, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x69,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x69, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x69, 0x70, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x63, 0x0a,
	0x1b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x69, 0x70, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x73, 0x22, 0x16, 0x0a, 0x04, 0x43, 0x68, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4f, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x43, 0x68, 0x69, 0x70, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x70,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e,
	0x43, 0x68, 0x69, 0x70, 0x52, 0x05, 0x63, 0x68, 0x69, 0x70, 0x73, 0x22, 0x2d, 0x0a, 0x12, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x69, 0x70, 0x49, 0x64, 0x32, 0xab, 0x06, 0x0a, 0x0d, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x6a, 0x0a, 0x15,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x72, 0x74,
	0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x72, 0x74, 0x69, 0x66,
	0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6a, 0x0a, 0x15, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x63, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x1c, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x2b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x58, 0x0a, 0x0c, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x43, 0x68, 0x69, 0x70, 0x12, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x68, 0x0a, 0x14, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x69, 0x70, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x65, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x43, 0x68, 0x69, 0x70, 0x73, 0x12,
	0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x31, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x65, 0x64, 0x43, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x56, 0x0a, 0x0b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x68, 0x69, 0x70, 0x73, 0x12,
	0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x68,
	0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x16, 0x5a, 0x14, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_model_receiver_model_receiver_proto_rawDescOnce sync.Once
	file_proto_model_receiver_model_receiver_proto_rawDescData = file_proto_model_receiver_model_receiver_proto_rawDesc
)

func file_proto_model_receiver_model_receiver_proto_rawDescGZIP() []byte {
	file_proto_model_receiver_model_receiver_proto_rawDescOnce.Do(func() {
		file_proto_model_receiver_model_receiver_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_model_receiver_model_receiver_proto_rawDescData)
	})
	return file_proto_model_receiver_model_receiver_proto_rawDescData
}

var file_proto_model_receiver_model_receiver_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_proto_model_receiver_model_receiver_proto_goTypes = []interface{}{
	(*DownloadModelArtifactRequest)(nil), // 0: carbon.model_receiver.DownloadModelArtifactRequest
	(*DownloadModelMetadataRequest)(nil), // 1: carbon.model_receiver.DownloadModelMetadataRequest
	(*Empty)(nil),                        // 2: carbon.model_receiver.Empty
	(*Model)(nil),                        // 3: carbon.model_receiver.Model
	(*DownloadedModelResponse)(nil),      // 4: carbon.model_receiver.DownloadedModelResponse
	(*CleanupModelsRequest)(nil),         // 5: carbon.model_receiver.CleanupModelsRequest
	(*DownloadChipRequest)(nil),          // 6: carbon.model_receiver.DownloadChipRequest
	(*DownloadChipMetadataRequest)(nil),  // 7: carbon.model_receiver.DownloadChipMetadataRequest
	(*Chip)(nil),                         // 8: carbon.model_receiver.Chip
	(*GetDownloadedChipsResponse)(nil),   // 9: carbon.model_receiver.GetDownloadedChipsResponse
	(*RemoveChipsRequest)(nil),           // 10: carbon.model_receiver.RemoveChipsRequest
}
var file_proto_model_receiver_model_receiver_proto_depIdxs = []int32{
	3,  // 0: carbon.model_receiver.DownloadedModelResponse.models:type_name -> carbon.model_receiver.Model
	8,  // 1: carbon.model_receiver.GetDownloadedChipsResponse.chips:type_name -> carbon.model_receiver.Chip
	0,  // 2: carbon.model_receiver.ModelReceiver.DownloadModelArtifact:input_type -> carbon.model_receiver.DownloadModelArtifactRequest
	1,  // 3: carbon.model_receiver.ModelReceiver.DownloadModelMetadata:input_type -> carbon.model_receiver.DownloadModelMetadataRequest
	2,  // 4: carbon.model_receiver.ModelReceiver.GetDownloadedModels:input_type -> carbon.model_receiver.Empty
	5,  // 5: carbon.model_receiver.ModelReceiver.CleanupModels:input_type -> carbon.model_receiver.CleanupModelsRequest
	6,  // 6: carbon.model_receiver.ModelReceiver.DownloadChip:input_type -> carbon.model_receiver.DownloadChipRequest
	7,  // 7: carbon.model_receiver.ModelReceiver.DownloadChipMetadata:input_type -> carbon.model_receiver.DownloadChipMetadataRequest
	2,  // 8: carbon.model_receiver.ModelReceiver.GetDownloadedChips:input_type -> carbon.model_receiver.Empty
	10, // 9: carbon.model_receiver.ModelReceiver.RemoveChips:input_type -> carbon.model_receiver.RemoveChipsRequest
	2,  // 10: carbon.model_receiver.ModelReceiver.DownloadModelArtifact:output_type -> carbon.model_receiver.Empty
	2,  // 11: carbon.model_receiver.ModelReceiver.DownloadModelMetadata:output_type -> carbon.model_receiver.Empty
	4,  // 12: carbon.model_receiver.ModelReceiver.GetDownloadedModels:output_type -> carbon.model_receiver.DownloadedModelResponse
	2,  // 13: carbon.model_receiver.ModelReceiver.CleanupModels:output_type -> carbon.model_receiver.Empty
	2,  // 14: carbon.model_receiver.ModelReceiver.DownloadChip:output_type -> carbon.model_receiver.Empty
	2,  // 15: carbon.model_receiver.ModelReceiver.DownloadChipMetadata:output_type -> carbon.model_receiver.Empty
	9,  // 16: carbon.model_receiver.ModelReceiver.GetDownloadedChips:output_type -> carbon.model_receiver.GetDownloadedChipsResponse
	2,  // 17: carbon.model_receiver.ModelReceiver.RemoveChips:output_type -> carbon.model_receiver.Empty
	10, // [10:18] is the sub-list for method output_type
	2,  // [2:10] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_proto_model_receiver_model_receiver_proto_init() }
func file_proto_model_receiver_model_receiver_proto_init() {
	if File_proto_model_receiver_model_receiver_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_model_receiver_model_receiver_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadModelArtifactRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadModelMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadedModelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupModelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadChipRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadChipMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDownloadedChipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_model_receiver_model_receiver_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveChipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_model_receiver_model_receiver_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_model_receiver_model_receiver_proto_goTypes,
		DependencyIndexes: file_proto_model_receiver_model_receiver_proto_depIdxs,
		MessageInfos:      file_proto_model_receiver_model_receiver_proto_msgTypes,
	}.Build()
	File_proto_model_receiver_model_receiver_proto = out.File
	file_proto_model_receiver_model_receiver_proto_rawDesc = nil
	file_proto_model_receiver_model_receiver_proto_goTypes = nil
	file_proto_model_receiver_model_receiver_proto_depIdxs = nil
}

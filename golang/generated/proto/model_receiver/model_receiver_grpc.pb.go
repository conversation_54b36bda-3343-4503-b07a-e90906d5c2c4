// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/model_receiver/model_receiver.proto

package model_receiver

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModelReceiver_DownloadModelArtifact_FullMethodName = "/carbon.model_receiver.ModelReceiver/DownloadModelArtifact"
	ModelReceiver_DownloadModelMetadata_FullMethodName = "/carbon.model_receiver.ModelReceiver/DownloadModelMetadata"
	ModelReceiver_GetDownloadedModels_FullMethodName   = "/carbon.model_receiver.ModelReceiver/GetDownloadedModels"
	ModelReceiver_CleanupModels_FullMethodName         = "/carbon.model_receiver.ModelReceiver/CleanupModels"
	ModelReceiver_DownloadChip_FullMethodName          = "/carbon.model_receiver.ModelReceiver/DownloadChip"
	ModelReceiver_DownloadChipMetadata_FullMethodName  = "/carbon.model_receiver.ModelReceiver/DownloadChipMetadata"
	ModelReceiver_GetDownloadedChips_FullMethodName    = "/carbon.model_receiver.ModelReceiver/GetDownloadedChips"
	ModelReceiver_RemoveChips_FullMethodName           = "/carbon.model_receiver.ModelReceiver/RemoveChips"
)

// ModelReceiverClient is the client API for ModelReceiver service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelReceiverClient interface {
	DownloadModelArtifact(ctx context.Context, in *DownloadModelArtifactRequest, opts ...grpc.CallOption) (*Empty, error)
	DownloadModelMetadata(ctx context.Context, in *DownloadModelMetadataRequest, opts ...grpc.CallOption) (*Empty, error)
	GetDownloadedModels(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DownloadedModelResponse, error)
	CleanupModels(ctx context.Context, in *CleanupModelsRequest, opts ...grpc.CallOption) (*Empty, error)
	// Category related functions
	DownloadChip(ctx context.Context, in *DownloadChipRequest, opts ...grpc.CallOption) (*Empty, error)
	DownloadChipMetadata(ctx context.Context, in *DownloadChipMetadataRequest, opts ...grpc.CallOption) (*Empty, error)
	GetDownloadedChips(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetDownloadedChipsResponse, error)
	RemoveChips(ctx context.Context, in *RemoveChipsRequest, opts ...grpc.CallOption) (*Empty, error)
}

type modelReceiverClient struct {
	cc grpc.ClientConnInterface
}

func NewModelReceiverClient(cc grpc.ClientConnInterface) ModelReceiverClient {
	return &modelReceiverClient{cc}
}

func (c *modelReceiverClient) DownloadModelArtifact(ctx context.Context, in *DownloadModelArtifactRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelReceiver_DownloadModelArtifact_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) DownloadModelMetadata(ctx context.Context, in *DownloadModelMetadataRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelReceiver_DownloadModelMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) GetDownloadedModels(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DownloadedModelResponse, error) {
	out := new(DownloadedModelResponse)
	err := c.cc.Invoke(ctx, ModelReceiver_GetDownloadedModels_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) CleanupModels(ctx context.Context, in *CleanupModelsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelReceiver_CleanupModels_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) DownloadChip(ctx context.Context, in *DownloadChipRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelReceiver_DownloadChip_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) DownloadChipMetadata(ctx context.Context, in *DownloadChipMetadataRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelReceiver_DownloadChipMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) GetDownloadedChips(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetDownloadedChipsResponse, error) {
	out := new(GetDownloadedChipsResponse)
	err := c.cc.Invoke(ctx, ModelReceiver_GetDownloadedChips_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelReceiverClient) RemoveChips(ctx context.Context, in *RemoveChipsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModelReceiver_RemoveChips_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelReceiverServer is the server API for ModelReceiver service.
// All implementations must embed UnimplementedModelReceiverServer
// for forward compatibility
type ModelReceiverServer interface {
	DownloadModelArtifact(context.Context, *DownloadModelArtifactRequest) (*Empty, error)
	DownloadModelMetadata(context.Context, *DownloadModelMetadataRequest) (*Empty, error)
	GetDownloadedModels(context.Context, *Empty) (*DownloadedModelResponse, error)
	CleanupModels(context.Context, *CleanupModelsRequest) (*Empty, error)
	// Category related functions
	DownloadChip(context.Context, *DownloadChipRequest) (*Empty, error)
	DownloadChipMetadata(context.Context, *DownloadChipMetadataRequest) (*Empty, error)
	GetDownloadedChips(context.Context, *Empty) (*GetDownloadedChipsResponse, error)
	RemoveChips(context.Context, *RemoveChipsRequest) (*Empty, error)
	mustEmbedUnimplementedModelReceiverServer()
}

// UnimplementedModelReceiverServer must be embedded to have forward compatible implementations.
type UnimplementedModelReceiverServer struct {
}

func (UnimplementedModelReceiverServer) DownloadModelArtifact(context.Context, *DownloadModelArtifactRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadModelArtifact not implemented")
}
func (UnimplementedModelReceiverServer) DownloadModelMetadata(context.Context, *DownloadModelMetadataRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadModelMetadata not implemented")
}
func (UnimplementedModelReceiverServer) GetDownloadedModels(context.Context, *Empty) (*DownloadedModelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDownloadedModels not implemented")
}
func (UnimplementedModelReceiverServer) CleanupModels(context.Context, *CleanupModelsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanupModels not implemented")
}
func (UnimplementedModelReceiverServer) DownloadChip(context.Context, *DownloadChipRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadChip not implemented")
}
func (UnimplementedModelReceiverServer) DownloadChipMetadata(context.Context, *DownloadChipMetadataRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadChipMetadata not implemented")
}
func (UnimplementedModelReceiverServer) GetDownloadedChips(context.Context, *Empty) (*GetDownloadedChipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDownloadedChips not implemented")
}
func (UnimplementedModelReceiverServer) RemoveChips(context.Context, *RemoveChipsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveChips not implemented")
}
func (UnimplementedModelReceiverServer) mustEmbedUnimplementedModelReceiverServer() {}

// UnsafeModelReceiverServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelReceiverServer will
// result in compilation errors.
type UnsafeModelReceiverServer interface {
	mustEmbedUnimplementedModelReceiverServer()
}

func RegisterModelReceiverServer(s grpc.ServiceRegistrar, srv ModelReceiverServer) {
	s.RegisterService(&ModelReceiver_ServiceDesc, srv)
}

func _ModelReceiver_DownloadModelArtifact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadModelArtifactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).DownloadModelArtifact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_DownloadModelArtifact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).DownloadModelArtifact(ctx, req.(*DownloadModelArtifactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_DownloadModelMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadModelMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).DownloadModelMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_DownloadModelMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).DownloadModelMetadata(ctx, req.(*DownloadModelMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_GetDownloadedModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).GetDownloadedModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_GetDownloadedModels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).GetDownloadedModels(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_CleanupModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanupModelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).CleanupModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_CleanupModels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).CleanupModels(ctx, req.(*CleanupModelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_DownloadChip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadChipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).DownloadChip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_DownloadChip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).DownloadChip(ctx, req.(*DownloadChipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_DownloadChipMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadChipMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).DownloadChipMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_DownloadChipMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).DownloadChipMetadata(ctx, req.(*DownloadChipMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_GetDownloadedChips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).GetDownloadedChips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_GetDownloadedChips_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).GetDownloadedChips(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModelReceiver_RemoveChips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelReceiverServer).RemoveChips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModelReceiver_RemoveChips_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelReceiverServer).RemoveChips(ctx, req.(*RemoveChipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ModelReceiver_ServiceDesc is the grpc.ServiceDesc for ModelReceiver service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModelReceiver_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.model_receiver.ModelReceiver",
	HandlerType: (*ModelReceiverServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DownloadModelArtifact",
			Handler:    _ModelReceiver_DownloadModelArtifact_Handler,
		},
		{
			MethodName: "DownloadModelMetadata",
			Handler:    _ModelReceiver_DownloadModelMetadata_Handler,
		},
		{
			MethodName: "GetDownloadedModels",
			Handler:    _ModelReceiver_GetDownloadedModels_Handler,
		},
		{
			MethodName: "CleanupModels",
			Handler:    _ModelReceiver_CleanupModels_Handler,
		},
		{
			MethodName: "DownloadChip",
			Handler:    _ModelReceiver_DownloadChip_Handler,
		},
		{
			MethodName: "DownloadChipMetadata",
			Handler:    _ModelReceiver_DownloadChipMetadata_Handler,
		},
		{
			MethodName: "GetDownloadedChips",
			Handler:    _ModelReceiver_GetDownloadedChips_Handler,
		},
		{
			MethodName: "RemoveChips",
			Handler:    _ModelReceiver_RemoveChips_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/model_receiver/model_receiver.proto",
}

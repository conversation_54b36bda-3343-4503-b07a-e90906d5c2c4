// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/module/types/types.proto

package types

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_types_types_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_types_types_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_module_types_types_proto_rawDescGZIP(), []int{0}
}

type ModuleIdentity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Serial string `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
}

func (x *ModuleIdentity) Reset() {
	*x = ModuleIdentity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_types_types_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleIdentity) ProtoMessage() {}

func (x *ModuleIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_types_types_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleIdentity.ProtoReflect.Descriptor instead.
func (*ModuleIdentity) Descriptor() ([]byte, []int) {
	return file_proto_module_types_types_proto_rawDescGZIP(), []int{1}
}

func (x *ModuleIdentity) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ModuleIdentity) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

type ModuleIPs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	McbIp  string `protobuf:"bytes,1,opt,name=mcb_ip,json=mcbIp,proto3" json:"mcb_ip,omitempty"`
	PcIp   string `protobuf:"bytes,2,opt,name=pc_ip,json=pcIp,proto3" json:"pc_ip,omitempty"`
	IpmiIp string `protobuf:"bytes,3,opt,name=ipmi_ip,json=ipmiIp,proto3" json:"ipmi_ip,omitempty"`
}

func (x *ModuleIPs) Reset() {
	*x = ModuleIPs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_types_types_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleIPs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleIPs) ProtoMessage() {}

func (x *ModuleIPs) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_types_types_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleIPs.ProtoReflect.Descriptor instead.
func (*ModuleIPs) Descriptor() ([]byte, []int) {
	return file_proto_module_types_types_proto_rawDescGZIP(), []int{2}
}

func (x *ModuleIPs) GetMcbIp() string {
	if x != nil {
		return x.McbIp
	}
	return ""
}

func (x *ModuleIPs) GetPcIp() string {
	if x != nil {
		return x.PcIp
	}
	return ""
}

func (x *ModuleIPs) GetIpmiIp() string {
	if x != nil {
		return x.IpmiIp
	}
	return ""
}

var File_proto_module_types_types_proto protoreflect.FileDescriptor

var file_proto_module_types_types_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x13, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x38,
	0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0x50, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x50, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x63, 0x62, 0x5f, 0x69, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x63, 0x62, 0x49, 0x70, 0x12, 0x13, 0x0a, 0x05,
	0x70, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x63, 0x49,
	0x70, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x70, 0x6d, 0x69, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x69, 0x70, 0x6d, 0x69, 0x49, 0x70, 0x42, 0x14, 0x5a, 0x12, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_module_types_types_proto_rawDescOnce sync.Once
	file_proto_module_types_types_proto_rawDescData = file_proto_module_types_types_proto_rawDesc
)

func file_proto_module_types_types_proto_rawDescGZIP() []byte {
	file_proto_module_types_types_proto_rawDescOnce.Do(func() {
		file_proto_module_types_types_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_module_types_types_proto_rawDescData)
	})
	return file_proto_module_types_types_proto_rawDescData
}

var file_proto_module_types_types_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_module_types_types_proto_goTypes = []interface{}{
	(*Empty)(nil),          // 0: carbon.module.types.Empty
	(*ModuleIdentity)(nil), // 1: carbon.module.types.ModuleIdentity
	(*ModuleIPs)(nil),      // 2: carbon.module.types.ModuleIPs
}
var file_proto_module_types_types_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_module_types_types_proto_init() }
func file_proto_module_types_types_proto_init() {
	if File_proto_module_types_types_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_module_types_types_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_module_types_types_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleIdentity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_module_types_types_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleIPs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_module_types_types_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_module_types_types_proto_goTypes,
		DependencyIndexes: file_proto_module_types_types_proto_depIdxs,
		MessageInfos:      file_proto_module_types_types_proto_msgTypes,
	}.Build()
	File_proto_module_types_types_proto = out.File
	file_proto_module_types_types_proto_rawDesc = nil
	file_proto_module_types_types_proto_goTypes = nil
	file_proto_module_types_types_proto_depIdxs = nil
}

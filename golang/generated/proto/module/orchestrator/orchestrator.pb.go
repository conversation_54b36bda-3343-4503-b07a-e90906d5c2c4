// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/module/orchestrator/orchestrator.proto

package orchestrator

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	types "github.com/carbonrobotics/robot/golang/generated/proto/module/types"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HeartbeatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identity  *types.ModuleIdentity `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
	ModuleIps *types.ModuleIPs      `protobuf:"bytes,2,opt,name=module_ips,json=moduleIps,proto3" json:"module_ips,omitempty"`
}

func (x *HeartbeatRequest) Reset() {
	*x = HeartbeatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_orchestrator_orchestrator_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatRequest) ProtoMessage() {}

func (x *HeartbeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_orchestrator_orchestrator_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatRequest.ProtoReflect.Descriptor instead.
func (*HeartbeatRequest) Descriptor() ([]byte, []int) {
	return file_proto_module_orchestrator_orchestrator_proto_rawDescGZIP(), []int{0}
}

func (x *HeartbeatRequest) GetIdentity() *types.ModuleIdentity {
	if x != nil {
		return x.Identity
	}
	return nil
}

func (x *HeartbeatRequest) GetModuleIps() *types.ModuleIPs {
	if x != nil {
		return x.ModuleIps
	}
	return nil
}

var File_proto_module_orchestrator_orchestrator_proto protoreflect.FileDescriptor

var file_proto_module_orchestrator_orchestrator_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x6f, 0x72, 0x63, 0x68,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x6f, 0x72,
	0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x1e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x01, 0x0a, 0x10, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3f, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x3d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x49, 0x50, 0x73, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x70, 0x73, 0x32,
	0x72, 0x0a, 0x19, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x55, 0x0a, 0x09,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x42, 0x1b, 0x5a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_module_orchestrator_orchestrator_proto_rawDescOnce sync.Once
	file_proto_module_orchestrator_orchestrator_proto_rawDescData = file_proto_module_orchestrator_orchestrator_proto_rawDesc
)

func file_proto_module_orchestrator_orchestrator_proto_rawDescGZIP() []byte {
	file_proto_module_orchestrator_orchestrator_proto_rawDescOnce.Do(func() {
		file_proto_module_orchestrator_orchestrator_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_module_orchestrator_orchestrator_proto_rawDescData)
	})
	return file_proto_module_orchestrator_orchestrator_proto_rawDescData
}

var file_proto_module_orchestrator_orchestrator_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_module_orchestrator_orchestrator_proto_goTypes = []interface{}{
	(*HeartbeatRequest)(nil),     // 0: carbon.module.orchestrator.HeartbeatRequest
	(*types.ModuleIdentity)(nil), // 1: carbon.module.types.ModuleIdentity
	(*types.ModuleIPs)(nil),      // 2: carbon.module.types.ModuleIPs
	(*types.Empty)(nil),          // 3: carbon.module.types.Empty
}
var file_proto_module_orchestrator_orchestrator_proto_depIdxs = []int32{
	1, // 0: carbon.module.orchestrator.HeartbeatRequest.identity:type_name -> carbon.module.types.ModuleIdentity
	2, // 1: carbon.module.orchestrator.HeartbeatRequest.module_ips:type_name -> carbon.module.types.ModuleIPs
	0, // 2: carbon.module.orchestrator.ModuleOrchestratorService.Heartbeat:input_type -> carbon.module.orchestrator.HeartbeatRequest
	3, // 3: carbon.module.orchestrator.ModuleOrchestratorService.Heartbeat:output_type -> carbon.module.types.Empty
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_module_orchestrator_orchestrator_proto_init() }
func file_proto_module_orchestrator_orchestrator_proto_init() {
	if File_proto_module_orchestrator_orchestrator_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_module_orchestrator_orchestrator_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_module_orchestrator_orchestrator_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_module_orchestrator_orchestrator_proto_goTypes,
		DependencyIndexes: file_proto_module_orchestrator_orchestrator_proto_depIdxs,
		MessageInfos:      file_proto_module_orchestrator_orchestrator_proto_msgTypes,
	}.Build()
	File_proto_module_orchestrator_orchestrator_proto = out.File
	file_proto_module_orchestrator_orchestrator_proto_rawDesc = nil
	file_proto_module_orchestrator_orchestrator_proto_goTypes = nil
	file_proto_module_orchestrator_orchestrator_proto_depIdxs = nil
}

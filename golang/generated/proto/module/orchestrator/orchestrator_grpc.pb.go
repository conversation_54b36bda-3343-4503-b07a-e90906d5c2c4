// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/module/orchestrator/orchestrator.proto

package orchestrator

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	types "github.com/carbonrobotics/robot/golang/generated/proto/module/types"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModuleOrchestratorService_Heartbeat_FullMethodName = "/carbon.module.orchestrator.ModuleOrchestratorService/Heartbeat"
)

// ModuleOrchestratorServiceClient is the client API for ModuleOrchestratorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModuleOrchestratorServiceClient interface {
	Heartbeat(ctx context.Context, in *HeartbeatRequest, opts ...grpc.CallOption) (*types.Empty, error)
}

type moduleOrchestratorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModuleOrchestratorServiceClient(cc grpc.ClientConnInterface) ModuleOrchestratorServiceClient {
	return &moduleOrchestratorServiceClient{cc}
}

func (c *moduleOrchestratorServiceClient) Heartbeat(ctx context.Context, in *HeartbeatRequest, opts ...grpc.CallOption) (*types.Empty, error) {
	out := new(types.Empty)
	err := c.cc.Invoke(ctx, ModuleOrchestratorService_Heartbeat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModuleOrchestratorServiceServer is the server API for ModuleOrchestratorService service.
// All implementations must embed UnimplementedModuleOrchestratorServiceServer
// for forward compatibility
type ModuleOrchestratorServiceServer interface {
	Heartbeat(context.Context, *HeartbeatRequest) (*types.Empty, error)
	mustEmbedUnimplementedModuleOrchestratorServiceServer()
}

// UnimplementedModuleOrchestratorServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModuleOrchestratorServiceServer struct {
}

func (UnimplementedModuleOrchestratorServiceServer) Heartbeat(context.Context, *HeartbeatRequest) (*types.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Heartbeat not implemented")
}
func (UnimplementedModuleOrchestratorServiceServer) mustEmbedUnimplementedModuleOrchestratorServiceServer() {
}

// UnsafeModuleOrchestratorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModuleOrchestratorServiceServer will
// result in compilation errors.
type UnsafeModuleOrchestratorServiceServer interface {
	mustEmbedUnimplementedModuleOrchestratorServiceServer()
}

func RegisterModuleOrchestratorServiceServer(s grpc.ServiceRegistrar, srv ModuleOrchestratorServiceServer) {
	s.RegisterService(&ModuleOrchestratorService_ServiceDesc, srv)
}

func _ModuleOrchestratorService_Heartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleOrchestratorServiceServer).Heartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleOrchestratorService_Heartbeat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleOrchestratorServiceServer).Heartbeat(ctx, req.(*HeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ModuleOrchestratorService_ServiceDesc is the grpc.ServiceDesc for ModuleOrchestratorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModuleOrchestratorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.module.orchestrator.ModuleOrchestratorService",
	HandlerType: (*ModuleOrchestratorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Heartbeat",
			Handler:    _ModuleOrchestratorService_Heartbeat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/module/orchestrator/orchestrator.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/module/server/server.proto

package server

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	hardware_manager "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	types "github.com/carbonrobotics/robot/golang/generated/proto/module/types"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetModuleIdentityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identity *types.ModuleIdentity `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
}

func (x *GetModuleIdentityResponse) Reset() {
	*x = GetModuleIdentityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_server_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModuleIdentityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModuleIdentityResponse) ProtoMessage() {}

func (x *GetModuleIdentityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_server_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModuleIdentityResponse.ProtoReflect.Descriptor instead.
func (*GetModuleIdentityResponse) Descriptor() ([]byte, []int) {
	return file_proto_module_server_server_proto_rawDescGZIP(), []int{0}
}

func (x *GetModuleIdentityResponse) GetIdentity() *types.ModuleIdentity {
	if x != nil {
		return x.Identity
	}
	return nil
}

type SetModuleSerialNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNumber string `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Force        bool   `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`
}

func (x *SetModuleSerialNumberRequest) Reset() {
	*x = SetModuleSerialNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_server_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetModuleSerialNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetModuleSerialNumberRequest) ProtoMessage() {}

func (x *SetModuleSerialNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_server_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetModuleSerialNumberRequest.ProtoReflect.Descriptor instead.
func (*SetModuleSerialNumberRequest) Descriptor() ([]byte, []int) {
	return file_proto_module_server_server_proto_rawDescGZIP(), []int{1}
}

func (x *SetModuleSerialNumberRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *SetModuleSerialNumberRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type SetModuleIdentityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identity *types.ModuleIdentity `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
}

func (x *SetModuleIdentityRequest) Reset() {
	*x = SetModuleIdentityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_module_server_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetModuleIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetModuleIdentityRequest) ProtoMessage() {}

func (x *SetModuleIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_module_server_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetModuleIdentityRequest.ProtoReflect.Descriptor instead.
func (*SetModuleIdentityRequest) Descriptor() ([]byte, []int) {
	return file_proto_module_server_server_proto_rawDescGZIP(), []int{2}
}

func (x *SetModuleIdentityRequest) GetIdentity() *types.ModuleIdentity {
	if x != nil {
		return x.Identity
	}
	return nil
}

var File_proto_module_server_server_proto protoreflect.FileDescriptor

var file_proto_module_server_server_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x5c, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x59, 0x0a,
	0x1c, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x22, 0x5b, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x32, 0x97, 0x03, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x67, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5f, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x54, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x1a, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x24, 0x2e, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x50, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x15, 0x5a, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_module_server_server_proto_rawDescOnce sync.Once
	file_proto_module_server_server_proto_rawDescData = file_proto_module_server_server_proto_rawDesc
)

func file_proto_module_server_server_proto_rawDescGZIP() []byte {
	file_proto_module_server_server_proto_rawDescOnce.Do(func() {
		file_proto_module_server_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_module_server_server_proto_rawDescData)
	})
	return file_proto_module_server_server_proto_rawDescData
}

var file_proto_module_server_server_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_module_server_server_proto_goTypes = []interface{}{
	(*GetModuleIdentityResponse)(nil),           // 0: carbon.module.server.GetModuleIdentityResponse
	(*SetModuleSerialNumberRequest)(nil),        // 1: carbon.module.server.SetModuleSerialNumberRequest
	(*SetModuleIdentityRequest)(nil),            // 2: carbon.module.server.SetModuleIdentityRequest
	(*types.ModuleIdentity)(nil),                // 3: carbon.module.types.ModuleIdentity
	(*types.Empty)(nil),                         // 4: carbon.module.types.Empty
	(*hardware_manager.ReaperPcSensorData)(nil), // 5: hardware_manager.ReaperPcSensorData
}
var file_proto_module_server_server_proto_depIdxs = []int32{
	3, // 0: carbon.module.server.GetModuleIdentityResponse.identity:type_name -> carbon.module.types.ModuleIdentity
	3, // 1: carbon.module.server.SetModuleIdentityRequest.identity:type_name -> carbon.module.types.ModuleIdentity
	4, // 2: carbon.module.server.ModuleServerService.GetModuleIdentity:input_type -> carbon.module.types.Empty
	1, // 3: carbon.module.server.ModuleServerService.SetModuleSerialNumber:input_type -> carbon.module.server.SetModuleSerialNumberRequest
	2, // 4: carbon.module.server.ModuleServerService.SetModuleIdentity:input_type -> carbon.module.server.SetModuleIdentityRequest
	4, // 5: carbon.module.server.ModuleServerService.GetModuleSensors:input_type -> carbon.module.types.Empty
	0, // 6: carbon.module.server.ModuleServerService.GetModuleIdentity:output_type -> carbon.module.server.GetModuleIdentityResponse
	4, // 7: carbon.module.server.ModuleServerService.SetModuleSerialNumber:output_type -> carbon.module.types.Empty
	4, // 8: carbon.module.server.ModuleServerService.SetModuleIdentity:output_type -> carbon.module.types.Empty
	5, // 9: carbon.module.server.ModuleServerService.GetModuleSensors:output_type -> hardware_manager.ReaperPcSensorData
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_module_server_server_proto_init() }
func file_proto_module_server_server_proto_init() {
	if File_proto_module_server_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_module_server_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModuleIdentityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_module_server_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetModuleSerialNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_module_server_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetModuleIdentityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_module_server_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_module_server_server_proto_goTypes,
		DependencyIndexes: file_proto_module_server_server_proto_depIdxs,
		MessageInfos:      file_proto_module_server_server_proto_msgTypes,
	}.Build()
	File_proto_module_server_server_proto = out.File
	file_proto_module_server_server_proto_rawDesc = nil
	file_proto_module_server_server_proto_goTypes = nil
	file_proto_module_server_server_proto_depIdxs = nil
}

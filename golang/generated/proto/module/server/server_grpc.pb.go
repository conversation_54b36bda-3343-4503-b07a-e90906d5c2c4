// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: proto/module/server/server.proto

package server

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	hardware_manager "github.com/carbonrobotics/robot/golang/generated/proto/hardware_manager"
	types "github.com/carbonrobotics/robot/golang/generated/proto/module/types"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ModuleServerService_GetModuleIdentity_FullMethodName     = "/carbon.module.server.ModuleServerService/GetModuleIdentity"
	ModuleServerService_SetModuleSerialNumber_FullMethodName = "/carbon.module.server.ModuleServerService/SetModuleSerialNumber"
	ModuleServerService_SetModuleIdentity_FullMethodName     = "/carbon.module.server.ModuleServerService/SetModuleIdentity"
	ModuleServerService_GetModuleSensors_FullMethodName      = "/carbon.module.server.ModuleServerService/GetModuleSensors"
)

// ModuleServerServiceClient is the client API for ModuleServerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModuleServerServiceClient interface {
	GetModuleIdentity(ctx context.Context, in *types.Empty, opts ...grpc.CallOption) (*GetModuleIdentityResponse, error)
	SetModuleSerialNumber(ctx context.Context, in *SetModuleSerialNumberRequest, opts ...grpc.CallOption) (*types.Empty, error)
	SetModuleIdentity(ctx context.Context, in *SetModuleIdentityRequest, opts ...grpc.CallOption) (*types.Empty, error)
	GetModuleSensors(ctx context.Context, in *types.Empty, opts ...grpc.CallOption) (*hardware_manager.ReaperPcSensorData, error)
}

type moduleServerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewModuleServerServiceClient(cc grpc.ClientConnInterface) ModuleServerServiceClient {
	return &moduleServerServiceClient{cc}
}

func (c *moduleServerServiceClient) GetModuleIdentity(ctx context.Context, in *types.Empty, opts ...grpc.CallOption) (*GetModuleIdentityResponse, error) {
	out := new(GetModuleIdentityResponse)
	err := c.cc.Invoke(ctx, ModuleServerService_GetModuleIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleServerServiceClient) SetModuleSerialNumber(ctx context.Context, in *SetModuleSerialNumberRequest, opts ...grpc.CallOption) (*types.Empty, error) {
	out := new(types.Empty)
	err := c.cc.Invoke(ctx, ModuleServerService_SetModuleSerialNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleServerServiceClient) SetModuleIdentity(ctx context.Context, in *SetModuleIdentityRequest, opts ...grpc.CallOption) (*types.Empty, error) {
	out := new(types.Empty)
	err := c.cc.Invoke(ctx, ModuleServerService_SetModuleIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleServerServiceClient) GetModuleSensors(ctx context.Context, in *types.Empty, opts ...grpc.CallOption) (*hardware_manager.ReaperPcSensorData, error) {
	out := new(hardware_manager.ReaperPcSensorData)
	err := c.cc.Invoke(ctx, ModuleServerService_GetModuleSensors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModuleServerServiceServer is the server API for ModuleServerService service.
// All implementations must embed UnimplementedModuleServerServiceServer
// for forward compatibility
type ModuleServerServiceServer interface {
	GetModuleIdentity(context.Context, *types.Empty) (*GetModuleIdentityResponse, error)
	SetModuleSerialNumber(context.Context, *SetModuleSerialNumberRequest) (*types.Empty, error)
	SetModuleIdentity(context.Context, *SetModuleIdentityRequest) (*types.Empty, error)
	GetModuleSensors(context.Context, *types.Empty) (*hardware_manager.ReaperPcSensorData, error)
	mustEmbedUnimplementedModuleServerServiceServer()
}

// UnimplementedModuleServerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedModuleServerServiceServer struct {
}

func (UnimplementedModuleServerServiceServer) GetModuleIdentity(context.Context, *types.Empty) (*GetModuleIdentityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModuleIdentity not implemented")
}
func (UnimplementedModuleServerServiceServer) SetModuleSerialNumber(context.Context, *SetModuleSerialNumberRequest) (*types.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetModuleSerialNumber not implemented")
}
func (UnimplementedModuleServerServiceServer) SetModuleIdentity(context.Context, *SetModuleIdentityRequest) (*types.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetModuleIdentity not implemented")
}
func (UnimplementedModuleServerServiceServer) GetModuleSensors(context.Context, *types.Empty) (*hardware_manager.ReaperPcSensorData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModuleSensors not implemented")
}
func (UnimplementedModuleServerServiceServer) mustEmbedUnimplementedModuleServerServiceServer() {}

// UnsafeModuleServerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModuleServerServiceServer will
// result in compilation errors.
type UnsafeModuleServerServiceServer interface {
	mustEmbedUnimplementedModuleServerServiceServer()
}

func RegisterModuleServerServiceServer(s grpc.ServiceRegistrar, srv ModuleServerServiceServer) {
	s.RegisterService(&ModuleServerService_ServiceDesc, srv)
}

func _ModuleServerService_GetModuleIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleServerServiceServer).GetModuleIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleServerService_GetModuleIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleServerServiceServer).GetModuleIdentity(ctx, req.(*types.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleServerService_SetModuleSerialNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetModuleSerialNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleServerServiceServer).SetModuleSerialNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleServerService_SetModuleSerialNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleServerServiceServer).SetModuleSerialNumber(ctx, req.(*SetModuleSerialNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleServerService_SetModuleIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetModuleIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleServerServiceServer).SetModuleIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleServerService_SetModuleIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleServerServiceServer).SetModuleIdentity(ctx, req.(*SetModuleIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleServerService_GetModuleSensors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleServerServiceServer).GetModuleSensors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleServerService_GetModuleSensors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleServerServiceServer).GetModuleSensors(ctx, req.(*types.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ModuleServerService_ServiceDesc is the grpc.ServiceDesc for ModuleServerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModuleServerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.module.server.ModuleServerService",
	HandlerType: (*ModuleServerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetModuleIdentity",
			Handler:    _ModuleServerService_GetModuleIdentity_Handler,
		},
		{
			MethodName: "SetModuleSerialNumber",
			Handler:    _ModuleServerService_SetModuleSerialNumber_Handler,
		},
		{
			MethodName: "SetModuleIdentity",
			Handler:    _ModuleServerService_SetModuleIdentity_Handler,
		},
		{
			MethodName: "GetModuleSensors",
			Handler:    _ModuleServerService_GetModuleSensors_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/module/server/server.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: lib/common/camera/proto/camera.proto

package cv

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LightSourcePreset int32

const (
	LightSourcePreset_kOff           LightSourcePreset = 0
	LightSourcePreset_kDaylight5000K LightSourcePreset = 1
	LightSourcePreset_kDaylight6500K LightSourcePreset = 2
	LightSourcePreset_kTungsten2800K LightSourcePreset = 3
)

// Enum value maps for LightSourcePreset.
var (
	LightSourcePreset_name = map[int32]string{
		0: "kOff",
		1: "kDaylight5000K",
		2: "kDaylight6500K",
		3: "kTungsten2800K",
	}
	LightSourcePreset_value = map[string]int32{
		"kOff":           0,
		"kDaylight5000K": 1,
		"kDaylight6500K": 2,
		"kTungsten2800K": 3,
	}
)

func (x LightSourcePreset) Enum() *LightSourcePreset {
	p := new(LightSourcePreset)
	*p = x
	return p
}

func (x LightSourcePreset) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LightSourcePreset) Descriptor() protoreflect.EnumDescriptor {
	return file_lib_common_camera_proto_camera_proto_enumTypes[0].Descriptor()
}

func (LightSourcePreset) Type() protoreflect.EnumType {
	return &file_lib_common_camera_proto_camera_proto_enumTypes[0]
}

func (x LightSourcePreset) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LightSourcePreset.Descriptor instead.
func (LightSourcePreset) EnumDescriptor() ([]byte, []int) {
	return file_lib_common_camera_proto_camera_proto_rawDescGZIP(), []int{0}
}

var File_lib_common_camera_proto_camera_proto protoreflect.FileDescriptor

var file_lib_common_camera_proto_camera_proto_rawDesc = []byte{
	0x0a, 0x24, 0x6c, 0x69, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x6c, 0x69, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x2a, 0x59, 0x0a, 0x11, 0x4c, 0x69, 0x67,
	0x68, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x08,
	0x0a, 0x04, 0x6b, 0x4f, 0x66, 0x66, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x6b, 0x44, 0x61, 0x79,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x35, 0x30, 0x30, 0x30, 0x4b, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e,
	0x6b, 0x44, 0x61, 0x79, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x36, 0x35, 0x30, 0x30, 0x4b, 0x10, 0x02,
	0x12, 0x12, 0x0a, 0x0e, 0x6b, 0x54, 0x75, 0x6e, 0x67, 0x73, 0x74, 0x65, 0x6e, 0x32, 0x38, 0x30,
	0x30, 0x4b, 0x10, 0x03, 0x42, 0x0a, 0x5a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x76,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_lib_common_camera_proto_camera_proto_rawDescOnce sync.Once
	file_lib_common_camera_proto_camera_proto_rawDescData = file_lib_common_camera_proto_camera_proto_rawDesc
)

func file_lib_common_camera_proto_camera_proto_rawDescGZIP() []byte {
	file_lib_common_camera_proto_camera_proto_rawDescOnce.Do(func() {
		file_lib_common_camera_proto_camera_proto_rawDescData = protoimpl.X.CompressGZIP(file_lib_common_camera_proto_camera_proto_rawDescData)
	})
	return file_lib_common_camera_proto_camera_proto_rawDescData
}

var file_lib_common_camera_proto_camera_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_lib_common_camera_proto_camera_proto_goTypes = []interface{}{
	(LightSourcePreset)(0), // 0: lib.common.camera.LightSourcePreset
}
var file_lib_common_camera_proto_camera_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_lib_common_camera_proto_camera_proto_init() }
func file_lib_common_camera_proto_camera_proto_init() {
	if File_lib_common_camera_proto_camera_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_lib_common_camera_proto_camera_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_lib_common_camera_proto_camera_proto_goTypes,
		DependencyIndexes: file_lib_common_camera_proto_camera_proto_depIdxs,
		EnumInfos:         file_lib_common_camera_proto_camera_proto_enumTypes,
	}.Build()
	File_lib_common_camera_proto_camera_proto = out.File
	file_lib_common_camera_proto_camera_proto_rawDesc = nil
	file_lib_common_camera_proto_camera_proto_goTypes = nil
	file_lib_common_camera_proto_camera_proto_depIdxs = nil
}

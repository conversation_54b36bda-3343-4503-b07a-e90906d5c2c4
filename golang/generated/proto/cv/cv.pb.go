// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/cv/cv.proto

package cv

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type P2PCaptureReason int32

const (
	// Must keep order and increment by one as we iterate based on size
	P2PCaptureReason_P2PCaptureReason_MISS      P2PCaptureReason = 0
	P2PCaptureReason_P2PCaptureReason_SUCCESS   P2PCaptureReason = 1
	P2PCaptureReason_P2PCaptureReason_JUMP      P2PCaptureReason = 2
	P2PCaptureReason_P2PCaptureReason_First_P2P P2PCaptureReason = 3
)

// Enum value maps for P2PCaptureReason.
var (
	P2PCaptureReason_name = map[int32]string{
		0: "P2PCaptureReason_MISS",
		1: "P2PCaptureReason_SUCCESS",
		2: "P2PCaptureReason_JUMP",
		3: "P2PCaptureReason_First_P2P",
	}
	P2PCaptureReason_value = map[string]int32{
		"P2PCaptureReason_MISS":      0,
		"P2PCaptureReason_SUCCESS":   1,
		"P2PCaptureReason_JUMP":      2,
		"P2PCaptureReason_First_P2P": 3,
	}
)

func (x P2PCaptureReason) Enum() *P2PCaptureReason {
	p := new(P2PCaptureReason)
	*p = x
	return p
}

func (x P2PCaptureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (P2PCaptureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_cv_cv_proto_enumTypes[0].Descriptor()
}

func (P2PCaptureReason) Type() protoreflect.EnumType {
	return &file_proto_cv_cv_proto_enumTypes[0]
}

func (x P2PCaptureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use P2PCaptureReason.Descriptor instead.
func (P2PCaptureReason) EnumDescriptor() ([]byte, []int) {
	return file_proto_cv_cv_proto_rawDescGZIP(), []int{0}
}

var File_proto_cv_cv_proto protoreflect.FileDescriptor

var file_proto_cv_cv_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x76, 0x2f, 0x63, 0x76, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x63, 0x76, 0x2a, 0x86, 0x01, 0x0a, 0x10, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x32,
	0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x4d,
	0x49, 0x53, 0x53, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x10, 0x02, 0x12, 0x1e,
	0x0a, 0x1a, 0x50, 0x32, 0x50, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x5f, 0x46, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x50, 0x32, 0x50, 0x10, 0x03, 0x42, 0x0a,
	0x5a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x76, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_cv_cv_proto_rawDescOnce sync.Once
	file_proto_cv_cv_proto_rawDescData = file_proto_cv_cv_proto_rawDesc
)

func file_proto_cv_cv_proto_rawDescGZIP() []byte {
	file_proto_cv_cv_proto_rawDescOnce.Do(func() {
		file_proto_cv_cv_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_cv_cv_proto_rawDescData)
	})
	return file_proto_cv_cv_proto_rawDescData
}

var file_proto_cv_cv_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_cv_cv_proto_goTypes = []interface{}{
	(P2PCaptureReason)(0), // 0: carbon.aimbot.cv.P2PCaptureReason
}
var file_proto_cv_cv_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_cv_cv_proto_init() }
func file_proto_cv_cv_proto_init() {
	if File_proto_cv_cv_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_cv_cv_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_cv_cv_proto_goTypes,
		DependencyIndexes: file_proto_cv_cv_proto_depIdxs,
		EnumInfos:         file_proto_cv_cv_proto_enumTypes,
	}.Build()
	File_proto_cv_cv_proto = out.File
	file_proto_cv_cv_proto_rawDesc = nil
	file_proto_cv_cv_proto_goTypes = nil
	file_proto_cv_cv_proto_depIdxs = nil
}

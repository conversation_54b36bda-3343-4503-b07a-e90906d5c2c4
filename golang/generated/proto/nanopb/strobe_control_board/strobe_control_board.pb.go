// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: strobe_control_board.proto

package strobe_control_board

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	camera_power_control "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/camera_power_control"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	strobe_control "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/strobe_control"
	time "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/time"
	version "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/version"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_StrobeControl
	//	*Reply_CameraPowerControl
	//	*Reply_Version
	//	*Reply_Time
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strobe_control_board_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_strobe_control_board_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_strobe_control_board_proto_rawDescGZIP(), []int{0}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetStrobeControl() *strobe_control.Reply {
	if x, ok := x.GetReply().(*Reply_StrobeControl); ok {
		return x.StrobeControl
	}
	return nil
}

func (x *Reply) GetCameraPowerControl() *camera_power_control.Reply {
	if x, ok := x.GetReply().(*Reply_CameraPowerControl); ok {
		return x.CameraPowerControl
	}
	return nil
}

func (x *Reply) GetVersion() *version.Version_Reply {
	if x, ok := x.GetReply().(*Reply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Reply) GetTime() *time.Reply {
	if x, ok := x.GetReply().(*Reply_Time); ok {
		return x.Time
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_StrobeControl struct {
	StrobeControl *strobe_control.Reply `protobuf:"bytes,3,opt,name=strobe_control,json=strobeControl,proto3,oneof"`
}

type Reply_CameraPowerControl struct {
	CameraPowerControl *camera_power_control.Reply `protobuf:"bytes,4,opt,name=camera_power_control,json=cameraPowerControl,proto3,oneof"`
}

type Reply_Version struct {
	Version *version.Version_Reply `protobuf:"bytes,5,opt,name=version,proto3,oneof"`
}

type Reply_Time struct {
	Time *time.Reply `protobuf:"bytes,6,opt,name=time,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_StrobeControl) isReply_Reply() {}

func (*Reply_CameraPowerControl) isReply_Reply() {}

func (*Reply_Version) isReply_Reply() {}

func (*Reply_Time) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_StrobeControl
	//	*Request_CameraPowerControl
	//	*Request_Version
	//	*Request_Reset_
	//	*Request_Time
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strobe_control_board_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_strobe_control_board_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_strobe_control_board_proto_rawDescGZIP(), []int{1}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetStrobeControl() *strobe_control.Request {
	if x, ok := x.GetRequest().(*Request_StrobeControl); ok {
		return x.StrobeControl
	}
	return nil
}

func (x *Request) GetCameraPowerControl() *camera_power_control.Request {
	if x, ok := x.GetRequest().(*Request_CameraPowerControl); ok {
		return x.CameraPowerControl
	}
	return nil
}

func (x *Request) GetVersion() *version.Version_Request {
	if x, ok := x.GetRequest().(*Request_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Request) GetReset_() *version.Reset_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *Request) GetTime() *time.Request {
	if x, ok := x.GetRequest().(*Request_Time); ok {
		return x.Time
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_StrobeControl struct {
	StrobeControl *strobe_control.Request `protobuf:"bytes,3,opt,name=strobe_control,json=strobeControl,proto3,oneof"`
}

type Request_CameraPowerControl struct {
	CameraPowerControl *camera_power_control.Request `protobuf:"bytes,4,opt,name=camera_power_control,json=cameraPowerControl,proto3,oneof"`
}

type Request_Version struct {
	Version *version.Version_Request `protobuf:"bytes,5,opt,name=version,proto3,oneof"`
}

type Request_Reset_ struct {
	Reset_ *version.Reset_Request `protobuf:"bytes,6,opt,name=reset,proto3,oneof"`
}

type Request_Time struct {
	Time *time.Request `protobuf:"bytes,7,opt,name=time,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_StrobeControl) isRequest_Request() {}

func (*Request_CameraPowerControl) isRequest_Request() {}

func (*Request_Version) isRequest_Request() {}

func (*Request_Reset_) isRequest_Request() {}

func (*Request_Time) isRequest_Request() {}

var File_strobe_control_board_proto protoreflect.FileDescriptor

var file_strobe_control_board_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x73, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e,
	0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xd0, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04,
	0x70, 0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04,
	0x70, 0x6f, 0x6e, 0x67, 0x12, 0x3e, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x4f, 0x0a, 0x14, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x12, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05,
	0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x8c, 0x03, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e,
	0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x40, 0x0a, 0x0e, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x74,
	0x72, 0x6f, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x51, 0x0a, 0x14, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x12, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x34,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x1d, 0x5a, 0x1b, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_strobe_control_board_proto_rawDescOnce sync.Once
	file_strobe_control_board_proto_rawDescData = file_strobe_control_board_proto_rawDesc
)

func file_strobe_control_board_proto_rawDescGZIP() []byte {
	file_strobe_control_board_proto_rawDescOnce.Do(func() {
		file_strobe_control_board_proto_rawDescData = protoimpl.X.CompressGZIP(file_strobe_control_board_proto_rawDescData)
	})
	return file_strobe_control_board_proto_rawDescData
}

var file_strobe_control_board_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_strobe_control_board_proto_goTypes = []interface{}{
	(*Reply)(nil),                        // 0: strobe_control_board.Reply
	(*Request)(nil),                      // 1: strobe_control_board.Request
	(*request.RequestHeader)(nil),        // 2: request.RequestHeader
	(*diagnostic.Pong)(nil),              // 3: diagnostic.Pong
	(*strobe_control.Reply)(nil),         // 4: strobe_control.Reply
	(*camera_power_control.Reply)(nil),   // 5: camera_power_control.Reply
	(*version.Version_Reply)(nil),        // 6: version.Version_Reply
	(*time.Reply)(nil),                   // 7: time.Reply
	(*diagnostic.Ping)(nil),              // 8: diagnostic.Ping
	(*strobe_control.Request)(nil),       // 9: strobe_control.Request
	(*camera_power_control.Request)(nil), // 10: camera_power_control.Request
	(*version.Version_Request)(nil),      // 11: version.Version_Request
	(*version.Reset_Request)(nil),        // 12: version.Reset_Request
	(*time.Request)(nil),                 // 13: time.Request
}
var file_strobe_control_board_proto_depIdxs = []int32{
	2,  // 0: strobe_control_board.Reply.header:type_name -> request.RequestHeader
	3,  // 1: strobe_control_board.Reply.pong:type_name -> diagnostic.Pong
	4,  // 2: strobe_control_board.Reply.strobe_control:type_name -> strobe_control.Reply
	5,  // 3: strobe_control_board.Reply.camera_power_control:type_name -> camera_power_control.Reply
	6,  // 4: strobe_control_board.Reply.version:type_name -> version.Version_Reply
	7,  // 5: strobe_control_board.Reply.time:type_name -> time.Reply
	2,  // 6: strobe_control_board.Request.header:type_name -> request.RequestHeader
	8,  // 7: strobe_control_board.Request.ping:type_name -> diagnostic.Ping
	9,  // 8: strobe_control_board.Request.strobe_control:type_name -> strobe_control.Request
	10, // 9: strobe_control_board.Request.camera_power_control:type_name -> camera_power_control.Request
	11, // 10: strobe_control_board.Request.version:type_name -> version.Version_Request
	12, // 11: strobe_control_board.Request.reset:type_name -> version.Reset_Request
	13, // 12: strobe_control_board.Request.time:type_name -> time.Request
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_strobe_control_board_proto_init() }
func file_strobe_control_board_proto_init() {
	if File_strobe_control_board_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_strobe_control_board_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_strobe_control_board_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_strobe_control_board_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_StrobeControl)(nil),
		(*Reply_CameraPowerControl)(nil),
		(*Reply_Version)(nil),
		(*Reply_Time)(nil),
	}
	file_strobe_control_board_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_StrobeControl)(nil),
		(*Request_CameraPowerControl)(nil),
		(*Request_Version)(nil),
		(*Request_Reset_)(nil),
		(*Request_Time)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_strobe_control_board_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_strobe_control_board_proto_goTypes,
		DependencyIndexes: file_strobe_control_board_proto_depIdxs,
		MessageInfos:      file_strobe_control_board_proto_msgTypes,
	}.Build()
	File_strobe_control_board_proto = out.File
	file_strobe_control_board_proto_rawDesc = nil
	file_strobe_control_board_proto_goTypes = nil
	file_strobe_control_board_proto_depIdxs = nil
}

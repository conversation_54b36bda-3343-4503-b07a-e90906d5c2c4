// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: hasselhoff_board.proto

package hasselhoff_board

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	carbon_tractor "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/carbon_tractor"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	ots_tractor "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ots_tractor"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	time "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/time"
	version "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/version"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_Time
	//	*Reply_Version
	//	*Reply_OtsTractor
	//	*Reply_CarbonTractor
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hasselhoff_board_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_hasselhoff_board_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_hasselhoff_board_proto_rawDescGZIP(), []int{0}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetTime() *time.Reply {
	if x, ok := x.GetReply().(*Reply_Time); ok {
		return x.Time
	}
	return nil
}

func (x *Reply) GetVersion() *version.Version_Reply {
	if x, ok := x.GetReply().(*Reply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Reply) GetOtsTractor() *ots_tractor.Reply {
	if x, ok := x.GetReply().(*Reply_OtsTractor); ok {
		return x.OtsTractor
	}
	return nil
}

func (x *Reply) GetCarbonTractor() *carbon_tractor.Reply {
	if x, ok := x.GetReply().(*Reply_CarbonTractor); ok {
		return x.CarbonTractor
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_Time struct {
	Time *time.Reply `protobuf:"bytes,3,opt,name=time,proto3,oneof"`
}

type Reply_Version struct {
	Version *version.Version_Reply `protobuf:"bytes,4,opt,name=version,proto3,oneof"`
}

type Reply_OtsTractor struct {
	OtsTractor *ots_tractor.Reply `protobuf:"bytes,5,opt,name=ots_tractor,json=otsTractor,proto3,oneof"`
}

type Reply_CarbonTractor struct {
	CarbonTractor *carbon_tractor.Reply `protobuf:"bytes,6,opt,name=carbon_tractor,json=carbonTractor,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_Time) isReply_Reply() {}

func (*Reply_Version) isReply_Reply() {}

func (*Reply_OtsTractor) isReply_Reply() {}

func (*Reply_CarbonTractor) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_Time
	//	*Request_Version
	//	*Request_Reset_
	//	*Request_OtsTractor
	//	*Request_CarbonTractor
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hasselhoff_board_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_hasselhoff_board_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_hasselhoff_board_proto_rawDescGZIP(), []int{1}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetTime() *time.Request {
	if x, ok := x.GetRequest().(*Request_Time); ok {
		return x.Time
	}
	return nil
}

func (x *Request) GetVersion() *version.Version_Request {
	if x, ok := x.GetRequest().(*Request_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Request) GetReset_() *version.Reset_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *Request) GetOtsTractor() *ots_tractor.Request {
	if x, ok := x.GetRequest().(*Request_OtsTractor); ok {
		return x.OtsTractor
	}
	return nil
}

func (x *Request) GetCarbonTractor() *carbon_tractor.Request {
	if x, ok := x.GetRequest().(*Request_CarbonTractor); ok {
		return x.CarbonTractor
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_Time struct {
	Time *time.Request `protobuf:"bytes,3,opt,name=time,proto3,oneof"`
}

type Request_Version struct {
	Version *version.Version_Request `protobuf:"bytes,4,opt,name=version,proto3,oneof"`
}

type Request_Reset_ struct {
	Reset_ *version.Reset_Request `protobuf:"bytes,5,opt,name=reset,proto3,oneof"`
}

type Request_OtsTractor struct {
	OtsTractor *ots_tractor.Request `protobuf:"bytes,6,opt,name=ots_tractor,json=otsTractor,proto3,oneof"`
}

type Request_CarbonTractor struct {
	CarbonTractor *carbon_tractor.Request `protobuf:"bytes,7,opt,name=carbon_tractor,json=carbonTractor,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_Time) isRequest_Request() {}

func (*Request_Version) isRequest_Request() {}

func (*Request_Reset_) isRequest_Request() {}

func (*Request_OtsTractor) isRequest_Request() {}

func (*Request_CarbonTractor) isRequest_Request() {}

var File_hasselhoff_board_proto protoreflect.FileDescriptor

var file_hasselhoff_board_proto_rawDesc = []byte{
	0x0a, 0x16, 0x68, 0x61, 0x73, 0x73, 0x65, 0x6c, 0x68, 0x6f, 0x66, 0x66, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x68, 0x61, 0x73, 0x73, 0x65, 0x6c,
	0x68, 0x6f, 0x66, 0x66, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x37, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb6, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70,
	0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70,
	0x6f, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x0b, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x74, 0x73, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x3e, 0x0a, 0x0e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xf2, 0x02, 0x0a, 0x07, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x23,
	0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x6f, 0x74, 0x73,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x74, 0x73, 0x54, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x54, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x19, 0x5a, 0x17, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x68, 0x61, 0x73, 0x73, 0x65, 0x6c,
	0x68, 0x6f, 0x66, 0x66, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_hasselhoff_board_proto_rawDescOnce sync.Once
	file_hasselhoff_board_proto_rawDescData = file_hasselhoff_board_proto_rawDesc
)

func file_hasselhoff_board_proto_rawDescGZIP() []byte {
	file_hasselhoff_board_proto_rawDescOnce.Do(func() {
		file_hasselhoff_board_proto_rawDescData = protoimpl.X.CompressGZIP(file_hasselhoff_board_proto_rawDescData)
	})
	return file_hasselhoff_board_proto_rawDescData
}

var file_hasselhoff_board_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_hasselhoff_board_proto_goTypes = []interface{}{
	(*Reply)(nil),                   // 0: hasselhoff_board.Reply
	(*Request)(nil),                 // 1: hasselhoff_board.Request
	(*request.RequestHeader)(nil),   // 2: request.RequestHeader
	(*diagnostic.Pong)(nil),         // 3: diagnostic.Pong
	(*time.Reply)(nil),              // 4: time.Reply
	(*version.Version_Reply)(nil),   // 5: version.Version_Reply
	(*ots_tractor.Reply)(nil),       // 6: ots_tractor.Reply
	(*carbon_tractor.Reply)(nil),    // 7: carbon_tractor.Reply
	(*diagnostic.Ping)(nil),         // 8: diagnostic.Ping
	(*time.Request)(nil),            // 9: time.Request
	(*version.Version_Request)(nil), // 10: version.Version_Request
	(*version.Reset_Request)(nil),   // 11: version.Reset_Request
	(*ots_tractor.Request)(nil),     // 12: ots_tractor.Request
	(*carbon_tractor.Request)(nil),  // 13: carbon_tractor.Request
}
var file_hasselhoff_board_proto_depIdxs = []int32{
	2,  // 0: hasselhoff_board.Reply.header:type_name -> request.RequestHeader
	3,  // 1: hasselhoff_board.Reply.pong:type_name -> diagnostic.Pong
	4,  // 2: hasselhoff_board.Reply.time:type_name -> time.Reply
	5,  // 3: hasselhoff_board.Reply.version:type_name -> version.Version_Reply
	6,  // 4: hasselhoff_board.Reply.ots_tractor:type_name -> ots_tractor.Reply
	7,  // 5: hasselhoff_board.Reply.carbon_tractor:type_name -> carbon_tractor.Reply
	2,  // 6: hasselhoff_board.Request.header:type_name -> request.RequestHeader
	8,  // 7: hasselhoff_board.Request.ping:type_name -> diagnostic.Ping
	9,  // 8: hasselhoff_board.Request.time:type_name -> time.Request
	10, // 9: hasselhoff_board.Request.version:type_name -> version.Version_Request
	11, // 10: hasselhoff_board.Request.reset:type_name -> version.Reset_Request
	12, // 11: hasselhoff_board.Request.ots_tractor:type_name -> ots_tractor.Request
	13, // 12: hasselhoff_board.Request.carbon_tractor:type_name -> carbon_tractor.Request
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_hasselhoff_board_proto_init() }
func file_hasselhoff_board_proto_init() {
	if File_hasselhoff_board_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hasselhoff_board_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hasselhoff_board_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_hasselhoff_board_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_Time)(nil),
		(*Reply_Version)(nil),
		(*Reply_OtsTractor)(nil),
		(*Reply_CarbonTractor)(nil),
	}
	file_hasselhoff_board_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_Time)(nil),
		(*Request_Version)(nil),
		(*Request_Reset_)(nil),
		(*Request_OtsTractor)(nil),
		(*Request_CarbonTractor)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hasselhoff_board_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_hasselhoff_board_proto_goTypes,
		DependencyIndexes: file_hasselhoff_board_proto_depIdxs,
		MessageInfos:      file_hasselhoff_board_proto_msgTypes,
	}.Build()
	File_hasselhoff_board_proto = out.File
	file_hasselhoff_board_proto_rawDesc = nil
	file_hasselhoff_board_proto_goTypes = nil
	file_hasselhoff_board_proto_depIdxs = nil
}

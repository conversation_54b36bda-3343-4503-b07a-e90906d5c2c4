// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: epos.proto

package epos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	can_open "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/can_open"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PID_Request_Type int32

const (
	PID_Request_Type_PID_REQUEST_FALLBACK PID_Request_Type = 0
	PID_Request_Type_PID_REQUEST_TEST     PID_Request_Type = 1
	PID_Request_Type_PID_REQUEST_SAVE     PID_Request_Type = 2
)

// Enum value maps for PID_Request_Type.
var (
	PID_Request_Type_name = map[int32]string{
		0: "PID_REQUEST_FALLBACK",
		1: "PID_REQUEST_TEST",
		2: "PID_REQUEST_SAVE",
	}
	PID_Request_Type_value = map[string]int32{
		"PID_REQUEST_FALLBACK": 0,
		"PID_REQUEST_TEST":     1,
		"PID_REQUEST_SAVE":     2,
	}
)

func (x PID_Request_Type) Enum() *PID_Request_Type {
	p := new(PID_Request_Type)
	*p = x
	return p
}

func (x PID_Request_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PID_Request_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_epos_proto_enumTypes[0].Descriptor()
}

func (PID_Request_Type) Type() protoreflect.EnumType {
	return &file_epos_proto_enumTypes[0]
}

func (x PID_Request_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PID_Request_Type.Descriptor instead.
func (PID_Request_Type) EnumDescriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{0}
}

type Setup_PDOs_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Setup_PDOs_Request) Reset() {
	*x = Setup_PDOs_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Setup_PDOs_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Setup_PDOs_Request) ProtoMessage() {}

func (x *Setup_PDOs_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Setup_PDOs_Request.ProtoReflect.Descriptor instead.
func (*Setup_PDOs_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{0}
}

type Enable_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Enable_Request) Reset() {
	*x = Enable_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enable_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enable_Request) ProtoMessage() {}

func (x *Enable_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enable_Request.ProtoReflect.Descriptor instead.
func (*Enable_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{1}
}

type Disable_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Disable_Request) Reset() {
	*x = Disable_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Disable_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Disable_Request) ProtoMessage() {}

func (x *Disable_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Disable_Request.ProtoReflect.Descriptor instead.
func (*Disable_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{2}
}

type Hard_Home_Params struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepSize int32  `protobuf:"varint,1,opt,name=step_size,json=stepSize,proto3" json:"step_size,omitempty"`
	Offset   uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
}

func (x *Hard_Home_Params) Reset() {
	*x = Hard_Home_Params{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Hard_Home_Params) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Hard_Home_Params) ProtoMessage() {}

func (x *Hard_Home_Params) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Hard_Home_Params.ProtoReflect.Descriptor instead.
func (*Hard_Home_Params) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{3}
}

func (x *Hard_Home_Params) GetStepSize() int32 {
	if x != nil {
		return x.StepSize
	}
	return 0
}

func (x *Hard_Home_Params) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type Switch_Home_Params struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepSize      int32  `protobuf:"varint,1,opt,name=step_size,json=stepSize,proto3" json:"step_size,omitempty"`
	ThresholdStep uint32 `protobuf:"varint,2,opt,name=threshold_step,json=thresholdStep,proto3" json:"threshold_step,omitempty"`
}

func (x *Switch_Home_Params) Reset() {
	*x = Switch_Home_Params{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Switch_Home_Params) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Switch_Home_Params) ProtoMessage() {}

func (x *Switch_Home_Params) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Switch_Home_Params.ProtoReflect.Descriptor instead.
func (*Switch_Home_Params) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{4}
}

func (x *Switch_Home_Params) GetStepSize() int32 {
	if x != nil {
		return x.StepSize
	}
	return 0
}

func (x *Switch_Home_Params) GetThresholdStep() uint32 {
	if x != nil {
		return x.ThresholdStep
	}
	return 0
}

type Actual_Position_Home_Params struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Range uint32 `protobuf:"varint,1,opt,name=range,proto3" json:"range,omitempty"`
}

func (x *Actual_Position_Home_Params) Reset() {
	*x = Actual_Position_Home_Params{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Actual_Position_Home_Params) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Actual_Position_Home_Params) ProtoMessage() {}

func (x *Actual_Position_Home_Params) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Actual_Position_Home_Params.ProtoReflect.Descriptor instead.
func (*Actual_Position_Home_Params) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{5}
}

func (x *Actual_Position_Home_Params) GetRange() uint32 {
	if x != nil {
		return x.Range
	}
	return 0
}

type Home_Params struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinPosition     int32  `protobuf:"varint,1,opt,name=min_position,json=minPosition,proto3" json:"min_position,omitempty"`
	MaxPosition     int32  `protobuf:"varint,2,opt,name=max_position,json=maxPosition,proto3" json:"max_position,omitempty"`
	ProfileVelocity uint32 `protobuf:"varint,3,opt,name=profile_velocity,json=profileVelocity,proto3" json:"profile_velocity,omitempty"`
	// Types that are assignable to Params:
	//
	//	*Home_Params_HardStop
	//	*Home_Params_LimitSwitch
	//	*Home_Params_ActualPosition
	Params isHome_Params_Params `protobuf_oneof:"params"`
	Invert bool                 `protobuf:"varint,7,opt,name=invert,proto3" json:"invert,omitempty"`
}

func (x *Home_Params) Reset() {
	*x = Home_Params{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Home_Params) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Home_Params) ProtoMessage() {}

func (x *Home_Params) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Home_Params.ProtoReflect.Descriptor instead.
func (*Home_Params) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{6}
}

func (x *Home_Params) GetMinPosition() int32 {
	if x != nil {
		return x.MinPosition
	}
	return 0
}

func (x *Home_Params) GetMaxPosition() int32 {
	if x != nil {
		return x.MaxPosition
	}
	return 0
}

func (x *Home_Params) GetProfileVelocity() uint32 {
	if x != nil {
		return x.ProfileVelocity
	}
	return 0
}

func (m *Home_Params) GetParams() isHome_Params_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *Home_Params) GetHardStop() *Hard_Home_Params {
	if x, ok := x.GetParams().(*Home_Params_HardStop); ok {
		return x.HardStop
	}
	return nil
}

func (x *Home_Params) GetLimitSwitch() *Switch_Home_Params {
	if x, ok := x.GetParams().(*Home_Params_LimitSwitch); ok {
		return x.LimitSwitch
	}
	return nil
}

func (x *Home_Params) GetActualPosition() *Actual_Position_Home_Params {
	if x, ok := x.GetParams().(*Home_Params_ActualPosition); ok {
		return x.ActualPosition
	}
	return nil
}

func (x *Home_Params) GetInvert() bool {
	if x != nil {
		return x.Invert
	}
	return false
}

type isHome_Params_Params interface {
	isHome_Params_Params()
}

type Home_Params_HardStop struct {
	HardStop *Hard_Home_Params `protobuf:"bytes,4,opt,name=hard_stop,json=hardStop,proto3,oneof"`
}

type Home_Params_LimitSwitch struct {
	LimitSwitch *Switch_Home_Params `protobuf:"bytes,5,opt,name=limit_switch,json=limitSwitch,proto3,oneof"`
}

type Home_Params_ActualPosition struct {
	ActualPosition *Actual_Position_Home_Params `protobuf:"bytes,6,opt,name=actual_position,json=actualPosition,proto3,oneof"`
}

func (*Home_Params_HardStop) isHome_Params_Params() {}

func (*Home_Params_LimitSwitch) isHome_Params_Params() {}

func (*Home_Params_ActualPosition) isHome_Params_Params() {}

type Home_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params *Home_Params `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *Home_Request) Reset() {
	*x = Home_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Home_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Home_Request) ProtoMessage() {}

func (x *Home_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Home_Request.ProtoReflect.Descriptor instead.
func (*Home_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{7}
}

func (x *Home_Request) GetParams() *Home_Params {
	if x != nil {
		return x.Params
	}
	return nil
}

type Await_Settling_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetPosition int32  `protobuf:"varint,1,opt,name=target_position,json=targetPosition,proto3" json:"target_position,omitempty"`
	Window         uint32 `protobuf:"varint,2,opt,name=window,proto3" json:"window,omitempty"`
	TimeoutMs      uint32 `protobuf:"varint,3,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
}

func (x *Await_Settling_Request) Reset() {
	*x = Await_Settling_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Await_Settling_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Await_Settling_Request) ProtoMessage() {}

func (x *Await_Settling_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Await_Settling_Request.ProtoReflect.Descriptor instead.
func (*Await_Settling_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{8}
}

func (x *Await_Settling_Request) GetTargetPosition() int32 {
	if x != nil {
		return x.TargetPosition
	}
	return 0
}

func (x *Await_Settling_Request) GetWindow() uint32 {
	if x != nil {
		return x.Window
	}
	return 0
}

func (x *Await_Settling_Request) GetTimeoutMs() uint32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

type Get_Pos_Vel_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Pos_Vel_Request) Reset() {
	*x = Get_Pos_Vel_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Pos_Vel_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Pos_Vel_Request) ProtoMessage() {}

func (x *Get_Pos_Vel_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Pos_Vel_Request.ProtoReflect.Descriptor instead.
func (*Get_Pos_Vel_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{9}
}

type Go_To_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position  int32  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	Velocity  uint32 `protobuf:"varint,2,opt,name=velocity,proto3" json:"velocity,omitempty"`
	Window    uint32 `protobuf:"varint,3,opt,name=window,proto3" json:"window,omitempty"`
	TimeoutMs uint32 `protobuf:"varint,4,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
}

func (x *Go_To_Request) Reset() {
	*x = Go_To_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Request) ProtoMessage() {}

func (x *Go_To_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{10}
}

func (x *Go_To_Request) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Go_To_Request) GetVelocity() uint32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

func (x *Go_To_Request) GetWindow() uint32 {
	if x != nil {
		return x.Window
	}
	return 0
}

func (x *Go_To_Request) GetTimeoutMs() uint32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

type Await_Status_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeoutMs   uint32 `protobuf:"varint,1,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
	Expected    uint32 `protobuf:"varint,2,opt,name=expected,proto3" json:"expected,omitempty"`
	ExpectedNeg uint32 `protobuf:"varint,3,opt,name=expected_neg,json=expectedNeg,proto3" json:"expected_neg,omitempty"`
}

func (x *Await_Status_Request) Reset() {
	*x = Await_Status_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Await_Status_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Await_Status_Request) ProtoMessage() {}

func (x *Await_Status_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Await_Status_Request.ProtoReflect.Descriptor instead.
func (*Await_Status_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{11}
}

func (x *Await_Status_Request) GetTimeoutMs() uint32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

func (x *Await_Status_Request) GetExpected() uint32 {
	if x != nil {
		return x.Expected
	}
	return 0
}

func (x *Await_Status_Request) GetExpectedNeg() uint32 {
	if x != nil {
		return x.ExpectedNeg
	}
	return 0
}

type Set_Positional_PID_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GainP   uint32 `protobuf:"varint,1,opt,name=gain_p,json=gainP,proto3" json:"gain_p,omitempty"`
	GainI   uint32 `protobuf:"varint,2,opt,name=gain_i,json=gainI,proto3" json:"gain_i,omitempty"`
	GainD   uint32 `protobuf:"varint,3,opt,name=gain_d,json=gainD,proto3" json:"gain_d,omitempty"`
	GainFfv uint32 `protobuf:"varint,4,opt,name=gain_ffv,json=gainFfv,proto3" json:"gain_ffv,omitempty"`
	GainFfa uint32 `protobuf:"varint,5,opt,name=gain_ffa,json=gainFfa,proto3" json:"gain_ffa,omitempty"`
}

func (x *Set_Positional_PID_Request) Reset() {
	*x = Set_Positional_PID_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Positional_PID_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Positional_PID_Request) ProtoMessage() {}

func (x *Set_Positional_PID_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Positional_PID_Request.ProtoReflect.Descriptor instead.
func (*Set_Positional_PID_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{12}
}

func (x *Set_Positional_PID_Request) GetGainP() uint32 {
	if x != nil {
		return x.GainP
	}
	return 0
}

func (x *Set_Positional_PID_Request) GetGainI() uint32 {
	if x != nil {
		return x.GainI
	}
	return 0
}

func (x *Set_Positional_PID_Request) GetGainD() uint32 {
	if x != nil {
		return x.GainD
	}
	return 0
}

func (x *Set_Positional_PID_Request) GetGainFfv() uint32 {
	if x != nil {
		return x.GainFfv
	}
	return 0
}

func (x *Set_Positional_PID_Request) GetGainFfa() uint32 {
	if x != nil {
		return x.GainFfa
	}
	return 0
}

type Set_PID_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentP      uint32                      `protobuf:"varint,1,opt,name=current_p,json=currentP,proto3" json:"current_p,omitempty"`
	CurrentI      uint32                      `protobuf:"varint,2,opt,name=current_i,json=currentI,proto3" json:"current_i,omitempty"`
	PositionalPid *Set_Positional_PID_Request `protobuf:"bytes,3,opt,name=positional_pid,json=positionalPid,proto3" json:"positional_pid,omitempty"`
}

func (x *Set_PID_Request) Reset() {
	*x = Set_PID_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_PID_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_PID_Request) ProtoMessage() {}

func (x *Set_PID_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_PID_Request.ProtoReflect.Descriptor instead.
func (*Set_PID_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{13}
}

func (x *Set_PID_Request) GetCurrentP() uint32 {
	if x != nil {
		return x.CurrentP
	}
	return 0
}

func (x *Set_PID_Request) GetCurrentI() uint32 {
	if x != nil {
		return x.CurrentI
	}
	return 0
}

func (x *Set_PID_Request) GetPositionalPid() *Set_Positional_PID_Request {
	if x != nil {
		return x.PositionalPid
	}
	return nil
}

type EPOS_PID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GainP    uint32 `protobuf:"varint,1,opt,name=gain_p,json=gainP,proto3" json:"gain_p,omitempty"`
	GainI    uint32 `protobuf:"varint,2,opt,name=gain_i,json=gainI,proto3" json:"gain_i,omitempty"`
	GainD    uint32 `protobuf:"varint,3,opt,name=gain_d,json=gainD,proto3" json:"gain_d,omitempty"`
	GainFfv  uint32 `protobuf:"varint,4,opt,name=gain_ffv,json=gainFfv,proto3" json:"gain_ffv,omitempty"`
	GainFfa  uint32 `protobuf:"varint,5,opt,name=gain_ffa,json=gainFfa,proto3" json:"gain_ffa,omitempty"`
	CurrentP uint32 `protobuf:"varint,6,opt,name=current_p,json=currentP,proto3" json:"current_p,omitempty"`
	CurrentI uint32 `protobuf:"varint,7,opt,name=current_i,json=currentI,proto3" json:"current_i,omitempty"`
}

func (x *EPOS_PID) Reset() {
	*x = EPOS_PID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EPOS_PID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EPOS_PID) ProtoMessage() {}

func (x *EPOS_PID) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EPOS_PID.ProtoReflect.Descriptor instead.
func (*EPOS_PID) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{14}
}

func (x *EPOS_PID) GetGainP() uint32 {
	if x != nil {
		return x.GainP
	}
	return 0
}

func (x *EPOS_PID) GetGainI() uint32 {
	if x != nil {
		return x.GainI
	}
	return 0
}

func (x *EPOS_PID) GetGainD() uint32 {
	if x != nil {
		return x.GainD
	}
	return 0
}

func (x *EPOS_PID) GetGainFfv() uint32 {
	if x != nil {
		return x.GainFfv
	}
	return 0
}

func (x *EPOS_PID) GetGainFfa() uint32 {
	if x != nil {
		return x.GainFfa
	}
	return 0
}

func (x *EPOS_PID) GetCurrentP() uint32 {
	if x != nil {
		return x.CurrentP
	}
	return 0
}

func (x *EPOS_PID) GetCurrentI() uint32 {
	if x != nil {
		return x.CurrentI
	}
	return 0
}

type Set_PID_V2_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type PID_Request_Type `protobuf:"varint,1,opt,name=type,proto3,enum=epos.PID_Request_Type" json:"type,omitempty"`
	// Types that are assignable to Pid:
	//
	//	*Set_PID_V2_Request_Epos
	Pid isSet_PID_V2_Request_Pid `protobuf_oneof:"pid"`
}

func (x *Set_PID_V2_Request) Reset() {
	*x = Set_PID_V2_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_PID_V2_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_PID_V2_Request) ProtoMessage() {}

func (x *Set_PID_V2_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_PID_V2_Request.ProtoReflect.Descriptor instead.
func (*Set_PID_V2_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{15}
}

func (x *Set_PID_V2_Request) GetType() PID_Request_Type {
	if x != nil {
		return x.Type
	}
	return PID_Request_Type_PID_REQUEST_FALLBACK
}

func (m *Set_PID_V2_Request) GetPid() isSet_PID_V2_Request_Pid {
	if m != nil {
		return m.Pid
	}
	return nil
}

func (x *Set_PID_V2_Request) GetEpos() *EPOS_PID {
	if x, ok := x.GetPid().(*Set_PID_V2_Request_Epos); ok {
		return x.Epos
	}
	return nil
}

type isSet_PID_V2_Request_Pid interface {
	isSet_PID_V2_Request_Pid()
}

type Set_PID_V2_Request_Epos struct {
	Epos *EPOS_PID `protobuf:"bytes,2,opt,name=epos,proto3,oneof"`
}

func (*Set_PID_V2_Request_Epos) isSet_PID_V2_Request_Pid() {}

type Get_PID_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_PID_Request) Reset() {
	*x = Get_PID_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_PID_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_PID_Request) ProtoMessage() {}

func (x *Get_PID_Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_PID_Request.ProtoReflect.Descriptor instead.
func (*Get_PID_Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{16}
}

type Homing_Limit_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limit int32 `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *Homing_Limit_Reply) Reset() {
	*x = Homing_Limit_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Homing_Limit_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Homing_Limit_Reply) ProtoMessage() {}

func (x *Homing_Limit_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Homing_Limit_Reply.ProtoReflect.Descriptor instead.
func (*Homing_Limit_Reply) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{17}
}

func (x *Homing_Limit_Reply) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type Settling_Time_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration uint32 `protobuf:"varint,1,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *Settling_Time_Reply) Reset() {
	*x = Settling_Time_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settling_Time_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settling_Time_Reply) ProtoMessage() {}

func (x *Settling_Time_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settling_Time_Reply.ProtoReflect.Descriptor instead.
func (*Settling_Time_Reply) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{18}
}

func (x *Settling_Time_Reply) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type Pos_Vel_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position int32 `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	Velocity int32 `protobuf:"varint,2,opt,name=velocity,proto3" json:"velocity,omitempty"`
}

func (x *Pos_Vel_Reply) Reset() {
	*x = Pos_Vel_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pos_Vel_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pos_Vel_Reply) ProtoMessage() {}

func (x *Pos_Vel_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pos_Vel_Reply.ProtoReflect.Descriptor instead.
func (*Pos_Vel_Reply) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{19}
}

func (x *Pos_Vel_Reply) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Pos_Vel_Reply) GetVelocity() int32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

type Get_PID_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Valid bool `protobuf:"varint,1,opt,name=valid,proto3" json:"valid,omitempty"`
	// Types that are assignable to Pid:
	//
	//	*Get_PID_Reply_Epos
	Pid isGet_PID_Reply_Pid `protobuf_oneof:"pid"`
}

func (x *Get_PID_Reply) Reset() {
	*x = Get_PID_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_PID_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_PID_Reply) ProtoMessage() {}

func (x *Get_PID_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_PID_Reply.ProtoReflect.Descriptor instead.
func (*Get_PID_Reply) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{20}
}

func (x *Get_PID_Reply) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (m *Get_PID_Reply) GetPid() isGet_PID_Reply_Pid {
	if m != nil {
		return m.Pid
	}
	return nil
}

func (x *Get_PID_Reply) GetEpos() *EPOS_PID {
	if x, ok := x.GetPid().(*Get_PID_Reply_Epos); ok {
		return x.Epos
	}
	return nil
}

type isGet_PID_Reply_Pid interface {
	isGet_PID_Reply_Pid()
}

type Get_PID_Reply_Epos struct {
	Epos *EPOS_PID `protobuf:"bytes,2,opt,name=epos,proto3,oneof"`
}

func (*Get_PID_Reply_Epos) isGet_PID_Reply_Pid() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Can
	//	*Request_SetupPdos
	//	*Request_Enable
	//	*Request_Disable
	//	*Request_Home
	//	*Request_Settle
	//	*Request_PosVel
	//	*Request_GoTo
	//	*Request_Status
	//	*Request_PositionalPid
	//	*Request_Pid
	//	*Request_PidV2
	//	*Request_GetPid_
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{21}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetCan() *can_open.Request {
	if x, ok := x.GetRequest().(*Request_Can); ok {
		return x.Can
	}
	return nil
}

func (x *Request) GetSetupPdos() *Setup_PDOs_Request {
	if x, ok := x.GetRequest().(*Request_SetupPdos); ok {
		return x.SetupPdos
	}
	return nil
}

func (x *Request) GetEnable() *Enable_Request {
	if x, ok := x.GetRequest().(*Request_Enable); ok {
		return x.Enable
	}
	return nil
}

func (x *Request) GetDisable() *Disable_Request {
	if x, ok := x.GetRequest().(*Request_Disable); ok {
		return x.Disable
	}
	return nil
}

func (x *Request) GetHome() *Home_Request {
	if x, ok := x.GetRequest().(*Request_Home); ok {
		return x.Home
	}
	return nil
}

func (x *Request) GetSettle() *Await_Settling_Request {
	if x, ok := x.GetRequest().(*Request_Settle); ok {
		return x.Settle
	}
	return nil
}

func (x *Request) GetPosVel() *Get_Pos_Vel_Request {
	if x, ok := x.GetRequest().(*Request_PosVel); ok {
		return x.PosVel
	}
	return nil
}

func (x *Request) GetGoTo() *Go_To_Request {
	if x, ok := x.GetRequest().(*Request_GoTo); ok {
		return x.GoTo
	}
	return nil
}

func (x *Request) GetStatus() *Await_Status_Request {
	if x, ok := x.GetRequest().(*Request_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Request) GetPositionalPid() *Set_Positional_PID_Request {
	if x, ok := x.GetRequest().(*Request_PositionalPid); ok {
		return x.PositionalPid
	}
	return nil
}

func (x *Request) GetPid() *Set_PID_Request {
	if x, ok := x.GetRequest().(*Request_Pid); ok {
		return x.Pid
	}
	return nil
}

func (x *Request) GetPidV2() *Set_PID_V2_Request {
	if x, ok := x.GetRequest().(*Request_PidV2); ok {
		return x.PidV2
	}
	return nil
}

func (x *Request) GetGetPid_() *Get_PID_Request {
	if x, ok := x.GetRequest().(*Request_GetPid_); ok {
		return x.GetPid_
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Can struct {
	Can *can_open.Request `protobuf:"bytes,1,opt,name=can,proto3,oneof"`
}

type Request_SetupPdos struct {
	SetupPdos *Setup_PDOs_Request `protobuf:"bytes,2,opt,name=setup_pdos,json=setupPdos,proto3,oneof"`
}

type Request_Enable struct {
	Enable *Enable_Request `protobuf:"bytes,3,opt,name=enable,proto3,oneof"`
}

type Request_Disable struct {
	Disable *Disable_Request `protobuf:"bytes,4,opt,name=disable,proto3,oneof"`
}

type Request_Home struct {
	Home *Home_Request `protobuf:"bytes,5,opt,name=home,proto3,oneof"`
}

type Request_Settle struct {
	Settle *Await_Settling_Request `protobuf:"bytes,6,opt,name=settle,proto3,oneof"`
}

type Request_PosVel struct {
	PosVel *Get_Pos_Vel_Request `protobuf:"bytes,7,opt,name=pos_vel,json=posVel,proto3,oneof"`
}

type Request_GoTo struct {
	GoTo *Go_To_Request `protobuf:"bytes,8,opt,name=go_to,json=goTo,proto3,oneof"`
}

type Request_Status struct {
	Status *Await_Status_Request `protobuf:"bytes,9,opt,name=status,proto3,oneof"`
}

type Request_PositionalPid struct {
	PositionalPid *Set_Positional_PID_Request `protobuf:"bytes,10,opt,name=positional_pid,json=positionalPid,proto3,oneof"`
}

type Request_Pid struct {
	Pid *Set_PID_Request `protobuf:"bytes,11,opt,name=pid,proto3,oneof"`
}

type Request_PidV2 struct {
	PidV2 *Set_PID_V2_Request `protobuf:"bytes,12,opt,name=pid_v2,json=pidV2,proto3,oneof"`
}

type Request_GetPid_ struct {
	GetPid_ *Get_PID_Request `protobuf:"bytes,13,opt,name=get_pid,json=getPid,proto3,oneof"`
}

func (*Request_Can) isRequest_Request() {}

func (*Request_SetupPdos) isRequest_Request() {}

func (*Request_Enable) isRequest_Request() {}

func (*Request_Disable) isRequest_Request() {}

func (*Request_Home) isRequest_Request() {}

func (*Request_Settle) isRequest_Request() {}

func (*Request_PosVel) isRequest_Request() {}

func (*Request_GoTo) isRequest_Request() {}

func (*Request_Status) isRequest_Request() {}

func (*Request_PositionalPid) isRequest_Request() {}

func (*Request_Pid) isRequest_Request() {}

func (*Request_PidV2) isRequest_Request() {}

func (*Request_GetPid_) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Ack
	//	*Reply_Can
	//	*Reply_Limit
	//	*Reply_Settle
	//	*Reply_PosVel
	//	*Reply_Error
	//	*Reply_Pid
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_epos_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_epos_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_epos_proto_rawDescGZIP(), []int{22}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetCan() *can_open.Reply {
	if x, ok := x.GetReply().(*Reply_Can); ok {
		return x.Can
	}
	return nil
}

func (x *Reply) GetLimit() *Homing_Limit_Reply {
	if x, ok := x.GetReply().(*Reply_Limit); ok {
		return x.Limit
	}
	return nil
}

func (x *Reply) GetSettle() *Settling_Time_Reply {
	if x, ok := x.GetReply().(*Reply_Settle); ok {
		return x.Settle
	}
	return nil
}

func (x *Reply) GetPosVel() *Pos_Vel_Reply {
	if x, ok := x.GetReply().(*Reply_PosVel); ok {
		return x.PosVel
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetPid() *Get_PID_Reply {
	if x, ok := x.GetReply().(*Reply_Pid); ok {
		return x.Pid
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,1,opt,name=ack,proto3,oneof"`
}

type Reply_Can struct {
	Can *can_open.Reply `protobuf:"bytes,2,opt,name=can,proto3,oneof"`
}

type Reply_Limit struct {
	Limit *Homing_Limit_Reply `protobuf:"bytes,3,opt,name=limit,proto3,oneof"`
}

type Reply_Settle struct {
	Settle *Settling_Time_Reply `protobuf:"bytes,4,opt,name=settle,proto3,oneof"`
}

type Reply_PosVel struct {
	PosVel *Pos_Vel_Reply `protobuf:"bytes,5,opt,name=pos_vel,json=posVel,proto3,oneof"`
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,6,opt,name=error,proto3,oneof"`
}

type Reply_Pid struct {
	Pid *Get_PID_Reply `protobuf:"bytes,7,opt,name=pid,proto3,oneof"`
}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Can) isReply_Reply() {}

func (*Reply_Limit) isReply_Reply() {}

func (*Reply_Settle) isReply_Reply() {}

func (*Reply_PosVel) isReply_Reply() {}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Pid) isReply_Reply() {}

var File_epos_proto protoreflect.FileDescriptor

var file_epos_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x65, 0x70,
	0x6f, 0x73, 0x1a, 0x31, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e,
	0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e,
	0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x50, 0x44, 0x4f,
	0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x10, 0x0a, 0x0e, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x11, 0x0a, 0x0f, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x47,
	0x0a, 0x10, 0x48, 0x61, 0x72, 0x64, 0x5f, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x58, 0x0a, 0x12, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x5f, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0d, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x65,
	0x70, 0x22, 0x33, 0x0a, 0x1b, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x22, 0xe4, 0x02, 0x0a, 0x0b, 0x48, 0x6f, 0x6d, 0x65, 0x5f,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69,
	0x6e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78,
	0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6d, 0x61, 0x78, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x56,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x35, 0x0a, 0x09, 0x68, 0x61, 0x72, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x70, 0x6f,
	0x73, 0x2e, 0x48, 0x61, 0x72, 0x64, 0x5f, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x48, 0x00, 0x52, 0x08, 0x68, 0x61, 0x72, 0x64, 0x53, 0x74, 0x6f, 0x70, 0x12, 0x3d,
	0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00,
	0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x4c, 0x0a,
	0x0f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x41, 0x63,
	0x74, 0x75, 0x61, 0x6c, 0x5f, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x48, 0x6f,
	0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x63, 0x74,
	0x75, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6e, 0x76, 0x65, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x39, 0x0a,
	0x0c, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x78, 0x0a, 0x16, 0x41, 0x77, 0x61, 0x69,
	0x74, 0x5f, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x4d, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x5f, 0x50, 0x6f, 0x73, 0x5f, 0x56, 0x65,
	0x6c, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x7e, 0x0a, 0x0d, 0x47, 0x6f, 0x5f,
	0x54, 0x6f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x73, 0x22, 0x74, 0x0a, 0x14, 0x41, 0x77, 0x61,
	0x69, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x67, 0x22,
	0x97, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x5f, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x67, 0x61, 0x69, 0x6e, 0x50, 0x12, 0x15, 0x0a, 0x06, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x67, 0x61, 0x69, 0x6e, 0x49, 0x12, 0x15, 0x0a, 0x06,
	0x67, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x67, 0x61,
	0x69, 0x6e, 0x44, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x66, 0x76, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x61, 0x69, 0x6e, 0x46, 0x66, 0x76, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x66, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x67, 0x61, 0x69, 0x6e, 0x46, 0x66, 0x61, 0x22, 0x94, 0x01, 0x0a, 0x0f, 0x53, 0x65,
	0x74, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x12, 0x47, 0x0a, 0x0e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x69, 0x64,
	0x22, 0xbf, 0x01, 0x0a, 0x08, 0x45, 0x50, 0x4f, 0x53, 0x5f, 0x50, 0x49, 0x44, 0x12, 0x15, 0x0a,
	0x06, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x67,
	0x61, 0x69, 0x6e, 0x50, 0x12, 0x15, 0x0a, 0x06, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x67, 0x61, 0x69, 0x6e, 0x49, 0x12, 0x15, 0x0a, 0x06, 0x67,
	0x61, 0x69, 0x6e, 0x5f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x67, 0x61, 0x69,
	0x6e, 0x44, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x66, 0x76, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x61, 0x69, 0x6e, 0x46, 0x66, 0x76, 0x12, 0x19, 0x0a,
	0x08, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x66, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x67, 0x61, 0x69, 0x6e, 0x46, 0x66, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x50, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x49, 0x22, 0x6d, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x56, 0x32,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x50, 0x49,
	0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x65, 0x70, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x45, 0x50, 0x4f, 0x53, 0x5f, 0x50,
	0x49, 0x44, 0x48, 0x00, 0x52, 0x04, 0x65, 0x70, 0x6f, 0x73, 0x42, 0x05, 0x0a, 0x03, 0x70, 0x69,
	0x64, 0x22, 0x11, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x2a, 0x0a, 0x12, 0x48, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0x31, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x54, 0x69, 0x6d,
	0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x0d, 0x50, 0x6f, 0x73, 0x5f, 0x56, 0x65, 0x6c, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x22, 0x52, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x04, 0x65, 0x70, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x45, 0x50, 0x4f, 0x53, 0x5f, 0x50, 0x49,
	0x44, 0x48, 0x00, 0x52, 0x04, 0x65, 0x70, 0x6f, 0x73, 0x42, 0x05, 0x0a, 0x03, 0x70, 0x69, 0x64,
	0x22, 0xae, 0x05, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x03,
	0x63, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x6e, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03,
	0x63, 0x61, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x70, 0x64, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53,
	0x65, 0x74, 0x75, 0x70, 0x5f, 0x50, 0x44, 0x4f, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x09, 0x73, 0x65, 0x74, 0x75, 0x70, 0x50, 0x64, 0x6f, 0x73, 0x12, 0x2e,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31,
	0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x68, 0x6f, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x68, 0x6f, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x65, 0x70,
	0x6f, 0x73, 0x2e, 0x41, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x70, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x5f,
	0x50, 0x6f, 0x73, 0x5f, 0x56, 0x65, 0x6c, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x06, 0x70, 0x6f, 0x73, 0x56, 0x65, 0x6c, 0x12, 0x2a, 0x0a, 0x05, 0x67, 0x6f, 0x5f,
	0x74, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e,
	0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x04, 0x67, 0x6f, 0x54, 0x6f, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x41, 0x77, 0x61,
	0x69, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0e, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x50, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x50,
	0x49, 0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x31, 0x0a, 0x06, 0x70, 0x69, 0x64, 0x5f, 0x76, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x44,
	0x5f, 0x56, 0x32, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70,
	0x69, 0x64, 0x56, 0x32, 0x12, 0x30, 0x0a, 0x07, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06,
	0x67, 0x65, 0x74, 0x50, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0xb9, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x03, 0x61,
	0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41,
	0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x23, 0x0a, 0x03, 0x63, 0x61, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x63, 0x61, 0x6e, 0x12, 0x30,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x33, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x54, 0x69, 0x6d, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x50, 0x6f,
	0x73, 0x5f, 0x56, 0x65, 0x6c, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x70,
	0x6f, 0x73, 0x56, 0x65, 0x6c, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x03, 0x70,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x58, 0x0a,
	0x10, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x46, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x10,
	0x01, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x41, 0x56, 0x45, 0x10, 0x02, 0x42, 0x0d, 0x5a, 0x0b, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x65, 0x70, 0x6f, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_epos_proto_rawDescOnce sync.Once
	file_epos_proto_rawDescData = file_epos_proto_rawDesc
)

func file_epos_proto_rawDescGZIP() []byte {
	file_epos_proto_rawDescOnce.Do(func() {
		file_epos_proto_rawDescData = protoimpl.X.CompressGZIP(file_epos_proto_rawDescData)
	})
	return file_epos_proto_rawDescData
}

var file_epos_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_epos_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_epos_proto_goTypes = []interface{}{
	(PID_Request_Type)(0),               // 0: epos.PID_Request_Type
	(*Setup_PDOs_Request)(nil),          // 1: epos.Setup_PDOs_Request
	(*Enable_Request)(nil),              // 2: epos.Enable_Request
	(*Disable_Request)(nil),             // 3: epos.Disable_Request
	(*Hard_Home_Params)(nil),            // 4: epos.Hard_Home_Params
	(*Switch_Home_Params)(nil),          // 5: epos.Switch_Home_Params
	(*Actual_Position_Home_Params)(nil), // 6: epos.Actual_Position_Home_Params
	(*Home_Params)(nil),                 // 7: epos.Home_Params
	(*Home_Request)(nil),                // 8: epos.Home_Request
	(*Await_Settling_Request)(nil),      // 9: epos.Await_Settling_Request
	(*Get_Pos_Vel_Request)(nil),         // 10: epos.Get_Pos_Vel_Request
	(*Go_To_Request)(nil),               // 11: epos.Go_To_Request
	(*Await_Status_Request)(nil),        // 12: epos.Await_Status_Request
	(*Set_Positional_PID_Request)(nil),  // 13: epos.Set_Positional_PID_Request
	(*Set_PID_Request)(nil),             // 14: epos.Set_PID_Request
	(*EPOS_PID)(nil),                    // 15: epos.EPOS_PID
	(*Set_PID_V2_Request)(nil),          // 16: epos.Set_PID_V2_Request
	(*Get_PID_Request)(nil),             // 17: epos.Get_PID_Request
	(*Homing_Limit_Reply)(nil),          // 18: epos.Homing_Limit_Reply
	(*Settling_Time_Reply)(nil),         // 19: epos.Settling_Time_Reply
	(*Pos_Vel_Reply)(nil),               // 20: epos.Pos_Vel_Reply
	(*Get_PID_Reply)(nil),               // 21: epos.Get_PID_Reply
	(*Request)(nil),                     // 22: epos.Request
	(*Reply)(nil),                       // 23: epos.Reply
	(*can_open.Request)(nil),            // 24: can_open.Request
	(*ack.Ack)(nil),                     // 25: ack.Ack
	(*can_open.Reply)(nil),              // 26: can_open.Reply
	(*error1.Error)(nil),                // 27: error.Error
}
var file_epos_proto_depIdxs = []int32{
	4,  // 0: epos.Home_Params.hard_stop:type_name -> epos.Hard_Home_Params
	5,  // 1: epos.Home_Params.limit_switch:type_name -> epos.Switch_Home_Params
	6,  // 2: epos.Home_Params.actual_position:type_name -> epos.Actual_Position_Home_Params
	7,  // 3: epos.Home_Request.params:type_name -> epos.Home_Params
	13, // 4: epos.Set_PID_Request.positional_pid:type_name -> epos.Set_Positional_PID_Request
	0,  // 5: epos.Set_PID_V2_Request.type:type_name -> epos.PID_Request_Type
	15, // 6: epos.Set_PID_V2_Request.epos:type_name -> epos.EPOS_PID
	15, // 7: epos.Get_PID_Reply.epos:type_name -> epos.EPOS_PID
	24, // 8: epos.Request.can:type_name -> can_open.Request
	1,  // 9: epos.Request.setup_pdos:type_name -> epos.Setup_PDOs_Request
	2,  // 10: epos.Request.enable:type_name -> epos.Enable_Request
	3,  // 11: epos.Request.disable:type_name -> epos.Disable_Request
	8,  // 12: epos.Request.home:type_name -> epos.Home_Request
	9,  // 13: epos.Request.settle:type_name -> epos.Await_Settling_Request
	10, // 14: epos.Request.pos_vel:type_name -> epos.Get_Pos_Vel_Request
	11, // 15: epos.Request.go_to:type_name -> epos.Go_To_Request
	12, // 16: epos.Request.status:type_name -> epos.Await_Status_Request
	13, // 17: epos.Request.positional_pid:type_name -> epos.Set_Positional_PID_Request
	14, // 18: epos.Request.pid:type_name -> epos.Set_PID_Request
	16, // 19: epos.Request.pid_v2:type_name -> epos.Set_PID_V2_Request
	17, // 20: epos.Request.get_pid:type_name -> epos.Get_PID_Request
	25, // 21: epos.Reply.ack:type_name -> ack.Ack
	26, // 22: epos.Reply.can:type_name -> can_open.Reply
	18, // 23: epos.Reply.limit:type_name -> epos.Homing_Limit_Reply
	19, // 24: epos.Reply.settle:type_name -> epos.Settling_Time_Reply
	20, // 25: epos.Reply.pos_vel:type_name -> epos.Pos_Vel_Reply
	27, // 26: epos.Reply.error:type_name -> error.Error
	21, // 27: epos.Reply.pid:type_name -> epos.Get_PID_Reply
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_epos_proto_init() }
func file_epos_proto_init() {
	if File_epos_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_epos_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Setup_PDOs_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enable_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Disable_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Hard_Home_Params); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Switch_Home_Params); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Actual_Position_Home_Params); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Home_Params); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Home_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Await_Settling_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Pos_Vel_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Await_Status_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Positional_PID_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_PID_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EPOS_PID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_PID_V2_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_PID_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Homing_Limit_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Settling_Time_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pos_Vel_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_PID_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_epos_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_epos_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*Home_Params_HardStop)(nil),
		(*Home_Params_LimitSwitch)(nil),
		(*Home_Params_ActualPosition)(nil),
	}
	file_epos_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*Set_PID_V2_Request_Epos)(nil),
	}
	file_epos_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*Get_PID_Reply_Epos)(nil),
	}
	file_epos_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*Request_Can)(nil),
		(*Request_SetupPdos)(nil),
		(*Request_Enable)(nil),
		(*Request_Disable)(nil),
		(*Request_Home)(nil),
		(*Request_Settle)(nil),
		(*Request_PosVel)(nil),
		(*Request_GoTo)(nil),
		(*Request_Status)(nil),
		(*Request_PositionalPid)(nil),
		(*Request_Pid)(nil),
		(*Request_PidV2)(nil),
		(*Request_GetPid_)(nil),
	}
	file_epos_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*Reply_Ack)(nil),
		(*Reply_Can)(nil),
		(*Reply_Limit)(nil),
		(*Reply_Settle)(nil),
		(*Reply_PosVel)(nil),
		(*Reply_Error)(nil),
		(*Reply_Pid)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_epos_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_epos_proto_goTypes,
		DependencyIndexes: file_epos_proto_depIdxs,
		EnumInfos:         file_epos_proto_enumTypes,
		MessageInfos:      file_epos_proto_msgTypes,
	}.Build()
	File_epos_proto = out.File
	file_epos_proto_rawDesc = nil
	file_epos_proto_goTypes = nil
	file_epos_proto_depIdxs = nil
}

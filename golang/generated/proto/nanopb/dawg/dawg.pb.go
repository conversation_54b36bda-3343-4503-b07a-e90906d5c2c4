// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: dawg.proto

package dawg

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeoutMs uint32 `protobuf:"varint,1,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
}

func (x *Config_Request) Reset() {
	*x = Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config_Request) ProtoMessage() {}

func (x *Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config_Request.ProtoReflect.Descriptor instead.
func (*Config_Request) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{0}
}

func (x *Config_Request) GetTimeoutMs() uint32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

type Arm_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Armed bool `protobuf:"varint,1,opt,name=armed,proto3" json:"armed,omitempty"`
}

func (x *Arm_Request) Reset() {
	*x = Arm_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Arm_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Arm_Request) ProtoMessage() {}

func (x *Arm_Request) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Arm_Request.ProtoReflect.Descriptor instead.
func (*Arm_Request) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{1}
}

func (x *Arm_Request) GetArmed() bool {
	if x != nil {
		return x.Armed
	}
	return false
}

type Pet_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Firing bool `protobuf:"varint,1,opt,name=firing,proto3" json:"firing,omitempty"`
}

func (x *Pet_Request) Reset() {
	*x = Pet_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pet_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet_Request) ProtoMessage() {}

func (x *Pet_Request) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet_Request.ProtoReflect.Descriptor instead.
func (*Pet_Request) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{2}
}

func (x *Pet_Request) GetFiring() bool {
	if x != nil {
		return x.Firing
	}
	return false
}

type Get_State_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_State_Request) Reset() {
	*x = Get_State_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_State_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_State_Request) ProtoMessage() {}

func (x *Get_State_Request) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_State_Request.ProtoReflect.Descriptor instead.
func (*Get_State_Request) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{3}
}

type State_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Armed  bool `protobuf:"varint,1,opt,name=armed,proto3" json:"armed,omitempty"`
	Petted bool `protobuf:"varint,2,opt,name=petted,proto3" json:"petted,omitempty"`
}

func (x *State_Reply) Reset() {
	*x = State_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *State_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*State_Reply) ProtoMessage() {}

func (x *State_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use State_Reply.ProtoReflect.Descriptor instead.
func (*State_Reply) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{4}
}

func (x *State_Reply) GetArmed() bool {
	if x != nil {
		return x.Armed
	}
	return false
}

func (x *State_Reply) GetPetted() bool {
	if x != nil {
		return x.Petted
	}
	return false
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Config
	//	*Request_Arm
	//	*Request_Pet
	//	*Request_State
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{5}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetConfig() *Config_Request {
	if x, ok := x.GetRequest().(*Request_Config); ok {
		return x.Config
	}
	return nil
}

func (x *Request) GetArm() *Arm_Request {
	if x, ok := x.GetRequest().(*Request_Arm); ok {
		return x.Arm
	}
	return nil
}

func (x *Request) GetPet() *Pet_Request {
	if x, ok := x.GetRequest().(*Request_Pet); ok {
		return x.Pet
	}
	return nil
}

func (x *Request) GetState() *Get_State_Request {
	if x, ok := x.GetRequest().(*Request_State); ok {
		return x.State
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Config struct {
	Config *Config_Request `protobuf:"bytes,1,opt,name=config,proto3,oneof"`
}

type Request_Arm struct {
	Arm *Arm_Request `protobuf:"bytes,2,opt,name=arm,proto3,oneof"`
}

type Request_Pet struct {
	Pet *Pet_Request `protobuf:"bytes,3,opt,name=pet,proto3,oneof"`
}

type Request_State struct {
	State *Get_State_Request `protobuf:"bytes,4,opt,name=state,proto3,oneof"`
}

func (*Request_Config) isRequest_Request() {}

func (*Request_Arm) isRequest_Request() {}

func (*Request_Pet) isRequest_Request() {}

func (*Request_State) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_State
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dawg_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_dawg_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_dawg_proto_rawDescGZIP(), []int{6}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetState() *State_Reply {
	if x, ok := x.GetReply().(*Reply_State); ok {
		return x.State
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_State struct {
	State *State_Reply `protobuf:"bytes,3,opt,name=state,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_State) isReply_Reply() {}

var File_dawg_proto protoreflect.FileDescriptor

var file_dawg_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x64, 0x61,
	0x77, 0x67, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x2f, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d,
	0x73, 0x22, 0x23, 0x0a, 0x0b, 0x41, 0x72, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x22, 0x25, 0x0a, 0x0b, 0x50, 0x65, 0x74, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x13, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x3b, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x61, 0x72, 0x6d, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x74, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x65, 0x74, 0x74, 0x65, 0x64, 0x22,
	0xc3, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61,
	0x77, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x0a, 0x03, 0x61,
	0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e,
	0x41, 0x72, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x61,
	0x72, 0x6d, 0x12, 0x25, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x50, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x7f, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61,
	0x63, 0x6b, 0x12, 0x29, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x0d, 0x5a, 0x0b, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x64, 0x61, 0x77, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dawg_proto_rawDescOnce sync.Once
	file_dawg_proto_rawDescData = file_dawg_proto_rawDesc
)

func file_dawg_proto_rawDescGZIP() []byte {
	file_dawg_proto_rawDescOnce.Do(func() {
		file_dawg_proto_rawDescData = protoimpl.X.CompressGZIP(file_dawg_proto_rawDescData)
	})
	return file_dawg_proto_rawDescData
}

var file_dawg_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_dawg_proto_goTypes = []interface{}{
	(*Config_Request)(nil),    // 0: dawg.Config_Request
	(*Arm_Request)(nil),       // 1: dawg.Arm_Request
	(*Pet_Request)(nil),       // 2: dawg.Pet_Request
	(*Get_State_Request)(nil), // 3: dawg.Get_State_Request
	(*State_Reply)(nil),       // 4: dawg.State_Reply
	(*Request)(nil),           // 5: dawg.Request
	(*Reply)(nil),             // 6: dawg.Reply
	(*error1.Error)(nil),      // 7: error.Error
	(*ack.Ack)(nil),           // 8: ack.Ack
}
var file_dawg_proto_depIdxs = []int32{
	0, // 0: dawg.Request.config:type_name -> dawg.Config_Request
	1, // 1: dawg.Request.arm:type_name -> dawg.Arm_Request
	2, // 2: dawg.Request.pet:type_name -> dawg.Pet_Request
	3, // 3: dawg.Request.state:type_name -> dawg.Get_State_Request
	7, // 4: dawg.Reply.error:type_name -> error.Error
	8, // 5: dawg.Reply.ack:type_name -> ack.Ack
	4, // 6: dawg.Reply.state:type_name -> dawg.State_Reply
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_dawg_proto_init() }
func file_dawg_proto_init() {
	if File_dawg_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_dawg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dawg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Arm_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dawg_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pet_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dawg_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_State_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dawg_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*State_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dawg_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dawg_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_dawg_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Request_Config)(nil),
		(*Request_Arm)(nil),
		(*Request_Pet)(nil),
		(*Request_State)(nil),
	}
	file_dawg_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_State)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dawg_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dawg_proto_goTypes,
		DependencyIndexes: file_dawg_proto_depIdxs,
		MessageInfos:      file_dawg_proto_msgTypes,
	}.Build()
	File_dawg_proto = out.File
	file_dawg_proto_rawDesc = nil
	file_dawg_proto_goTypes = nil
	file_dawg_proto_depIdxs = nil
}

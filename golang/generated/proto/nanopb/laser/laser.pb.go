// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: laser.proto

package laser

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Common laser
type LaserType int32

const (
	LaserType_LASERTYPE_UNKNOWN      LaserType = 0
	LaserType_LASERTYPE_CO2          LaserType = 1
	LaserType_LASERTYPE_DIODE_BWT    LaserType = 2
	LaserType_LASERTYPE_DIODE_JLIGHT LaserType = 3
)

// Enum value maps for LaserType.
var (
	LaserType_name = map[int32]string{
		0: "LASERTYPE_UNKNOWN",
		1: "LASERTYPE_CO2",
		2: "LASERTYPE_DIODE_BWT",
		3: "LASERTYPE_DIODE_JLIGHT",
	}
	LaserType_value = map[string]int32{
		"LASERTYPE_UNKNOWN":      0,
		"LASERTYPE_CO2":          1,
		"LASERTYPE_DIODE_BWT":    2,
		"LASERTYPE_DIODE_JLIGHT": 3,
	}
)

func (x LaserType) Enum() *LaserType {
	p := new(LaserType)
	*p = x
	return p
}

func (x LaserType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LaserType) Descriptor() protoreflect.EnumDescriptor {
	return file_laser_proto_enumTypes[0].Descriptor()
}

func (LaserType) Type() protoreflect.EnumType {
	return &file_laser_proto_enumTypes[0]
}

func (x LaserType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LaserType.Descriptor instead.
func (LaserType) EnumDescriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{0}
}

// Jilight laser specific
type Jlight_Fault int32

const (
	Jlight_Fault_JLIGHT_FAULT_UNKNOWN         Jlight_Fault = 0
	Jlight_Fault_JLIGHT_FAULT_INTERLOCK       Jlight_Fault = 1
	Jlight_Fault_JLIGHT_FAULT_SYSTEM          Jlight_Fault = 2
	Jlight_Fault_JLIGHT_FAULT_CH1_OVERCURRENT Jlight_Fault = 3
	Jlight_Fault_JLIGHT_FAULT_CH2_OVERCURRENT Jlight_Fault = 4
	Jlight_Fault_JLIGHT_FAULT_CH1_OVERVOLTAGE Jlight_Fault = 5
	Jlight_Fault_JLIGHT_FAULT_CH2_OVERVOLTAGE Jlight_Fault = 6
	Jlight_Fault_JLIGHT_FAULT_CH1_OVERTEMP    Jlight_Fault = 7
	Jlight_Fault_JLIGHT_FAULT_CH2_OVERTEMP    Jlight_Fault = 8
	Jlight_Fault_JLIGHT_FAULT_DRIVER_OVERTEMP Jlight_Fault = 9
)

// Enum value maps for Jlight_Fault.
var (
	Jlight_Fault_name = map[int32]string{
		0: "JLIGHT_FAULT_UNKNOWN",
		1: "JLIGHT_FAULT_INTERLOCK",
		2: "JLIGHT_FAULT_SYSTEM",
		3: "JLIGHT_FAULT_CH1_OVERCURRENT",
		4: "JLIGHT_FAULT_CH2_OVERCURRENT",
		5: "JLIGHT_FAULT_CH1_OVERVOLTAGE",
		6: "JLIGHT_FAULT_CH2_OVERVOLTAGE",
		7: "JLIGHT_FAULT_CH1_OVERTEMP",
		8: "JLIGHT_FAULT_CH2_OVERTEMP",
		9: "JLIGHT_FAULT_DRIVER_OVERTEMP",
	}
	Jlight_Fault_value = map[string]int32{
		"JLIGHT_FAULT_UNKNOWN":         0,
		"JLIGHT_FAULT_INTERLOCK":       1,
		"JLIGHT_FAULT_SYSTEM":          2,
		"JLIGHT_FAULT_CH1_OVERCURRENT": 3,
		"JLIGHT_FAULT_CH2_OVERCURRENT": 4,
		"JLIGHT_FAULT_CH1_OVERVOLTAGE": 5,
		"JLIGHT_FAULT_CH2_OVERVOLTAGE": 6,
		"JLIGHT_FAULT_CH1_OVERTEMP":    7,
		"JLIGHT_FAULT_CH2_OVERTEMP":    8,
		"JLIGHT_FAULT_DRIVER_OVERTEMP": 9,
	}
)

func (x Jlight_Fault) Enum() *Jlight_Fault {
	p := new(Jlight_Fault)
	*p = x
	return p
}

func (x Jlight_Fault) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Jlight_Fault) Descriptor() protoreflect.EnumDescriptor {
	return file_laser_proto_enumTypes[1].Descriptor()
}

func (Jlight_Fault) Type() protoreflect.EnumType {
	return &file_laser_proto_enumTypes[1]
}

func (x Jlight_Fault) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Jlight_Fault.Descriptor instead.
func (Jlight_Fault) EnumDescriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{1}
}

type Raw_Data_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Raw_Data_Request) Reset() {
	*x = Raw_Data_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Raw_Data_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Raw_Data_Request) ProtoMessage() {}

func (x *Raw_Data_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Raw_Data_Request.ProtoReflect.Descriptor instead.
func (*Raw_Data_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{0}
}

type Raw_Data_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// power meter thermistor readings
	Therm1Raw *int32 `protobuf:"varint,1,opt,name=therm1_raw,json=therm1Raw,proto3,oneof" json:"therm1_raw,omitempty"`
	Therm2Raw *int32 `protobuf:"varint,2,opt,name=therm2_raw,json=therm2Raw,proto3,oneof" json:"therm2_raw,omitempty"`
	// photodiode input analog voltage
	PhotodiodeRaw *int32 `protobuf:"varint,3,opt,name=photodiode_raw,json=photodiodeRaw,proto3,oneof" json:"photodiode_raw,omitempty"`
}

func (x *Raw_Data_Reply) Reset() {
	*x = Raw_Data_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Raw_Data_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Raw_Data_Reply) ProtoMessage() {}

func (x *Raw_Data_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Raw_Data_Reply.ProtoReflect.Descriptor instead.
func (*Raw_Data_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{1}
}

func (x *Raw_Data_Reply) GetTherm1Raw() int32 {
	if x != nil && x.Therm1Raw != nil {
		return *x.Therm1Raw
	}
	return 0
}

func (x *Raw_Data_Reply) GetTherm2Raw() int32 {
	if x != nil && x.Therm2Raw != nil {
		return *x.Therm2Raw
	}
	return 0
}

func (x *Raw_Data_Reply) GetPhotodiodeRaw() int32 {
	if x != nil && x.PhotodiodeRaw != nil {
		return *x.PhotodiodeRaw
	}
	return 0
}

type Laser_Inventory_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Laser_Inventory_Request) Reset() {
	*x = Laser_Inventory_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Inventory_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Inventory_Request) ProtoMessage() {}

func (x *Laser_Inventory_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Inventory_Request.ProtoReflect.Descriptor instead.
func (*Laser_Inventory_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{2}
}

type Laser_Inventory_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// laser controller model number
	Model string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	// laser serial number
	Serial string `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
	// rated laser power (W)
	Power uint32 `protobuf:"varint,3,opt,name=power,proto3" json:"power,omitempty"`
	// Laser type/manufacturer
	Type LaserType `protobuf:"varint,4,opt,name=type,proto3,enum=laser.LaserType" json:"type,omitempty"`
}

func (x *Laser_Inventory_Reply) Reset() {
	*x = Laser_Inventory_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Inventory_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Inventory_Reply) ProtoMessage() {}

func (x *Laser_Inventory_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Inventory_Reply.ProtoReflect.Descriptor instead.
func (*Laser_Inventory_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{3}
}

func (x *Laser_Inventory_Reply) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Laser_Inventory_Reply) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *Laser_Inventory_Reply) GetPower() uint32 {
	if x != nil {
		return x.Power
	}
	return 0
}

func (x *Laser_Inventory_Reply) GetType() LaserType {
	if x != nil {
		return x.Type
	}
	return LaserType_LASERTYPE_UNKNOWN
}

// write the type of laser to EEPROM
type Laser_Set_Type_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type LaserType `protobuf:"varint,1,opt,name=type,proto3,enum=laser.LaserType" json:"type,omitempty"`
}

func (x *Laser_Set_Type_Request) Reset() {
	*x = Laser_Set_Type_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Set_Type_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Set_Type_Request) ProtoMessage() {}

func (x *Laser_Set_Type_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Set_Type_Request.ProtoReflect.Descriptor instead.
func (*Laser_Set_Type_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{4}
}

func (x *Laser_Set_Type_Request) GetType() LaserType {
	if x != nil {
		return x.Type
	}
	return LaserType_LASERTYPE_UNKNOWN
}

// reset the laser subsystem
type Laser_Reset_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reset transport layer
	Transport bool `protobuf:"varint,1,opt,name=transport,proto3" json:"transport,omitempty"`
	// clear any alarms/faults latched in the laser
	Faults bool `protobuf:"varint,2,opt,name=faults,proto3" json:"faults,omitempty"`
}

func (x *Laser_Reset_Request) Reset() {
	*x = Laser_Reset_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Reset_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Reset_Request) ProtoMessage() {}

func (x *Laser_Reset_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Reset_Request.ProtoReflect.Descriptor instead.
func (*Laser_Reset_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{5}
}

func (x *Laser_Reset_Request) GetTransport() bool {
	if x != nil {
		return x.Transport
	}
	return false
}

func (x *Laser_Reset_Request) GetFaults() bool {
	if x != nil {
		return x.Faults
	}
	return false
}

type Jlight_Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Power spply temp
	PsuTemp float32 `protobuf:"fixed32,1,opt,name=psuTemp,proto3" json:"psuTemp,omitempty"`
	// Power supply input
	PsuInputVolts float32 `protobuf:"fixed32,2,opt,name=psuInputVolts,proto3" json:"psuInputVolts,omitempty"`
	// Output supply voltages
	PsuOutputVolts []float32 `protobuf:"fixed32,3,rep,packed,name=psuOutputVolts,proto3" json:"psuOutputVolts,omitempty"`
	// Fault conditions reported by laser
	Faults []Jlight_Fault `protobuf:"varint,4,rep,packed,name=faults,proto3,enum=laser.Jlight_Fault" json:"faults,omitempty"`
}

func (x *Jlight_Status) Reset() {
	*x = Jlight_Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Jlight_Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Jlight_Status) ProtoMessage() {}

func (x *Jlight_Status) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Jlight_Status.ProtoReflect.Descriptor instead.
func (*Jlight_Status) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{6}
}

func (x *Jlight_Status) GetPsuTemp() float32 {
	if x != nil {
		return x.PsuTemp
	}
	return 0
}

func (x *Jlight_Status) GetPsuInputVolts() float32 {
	if x != nil {
		return x.PsuInputVolts
	}
	return 0
}

func (x *Jlight_Status) GetPsuOutputVolts() []float32 {
	if x != nil {
		return x.PsuOutputVolts
	}
	return nil
}

func (x *Jlight_Status) GetFaults() []Jlight_Fault {
	if x != nil {
		return x.Faults
	}
	return nil
}

// BWT laser specific
type Bwt_Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Laser specific fault codes, if any
	Faults []int32 `protobuf:"varint,1,rep,packed,name=faults,proto3" json:"faults,omitempty"`
}

func (x *Bwt_Status) Reset() {
	*x = Bwt_Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bwt_Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bwt_Status) ProtoMessage() {}

func (x *Bwt_Status) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bwt_Status.ProtoReflect.Descriptor instead.
func (*Bwt_Status) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{7}
}

func (x *Bwt_Status) GetFaults() []int32 {
	if x != nil {
		return x.Faults
	}
	return nil
}

type Bwt_Passthrough_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Send this command to BWT laser, execute and return response
	Command string `protobuf:"bytes,1,opt,name=command,proto3" json:"command,omitempty"`
}

func (x *Bwt_Passthrough_Request) Reset() {
	*x = Bwt_Passthrough_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bwt_Passthrough_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bwt_Passthrough_Request) ProtoMessage() {}

func (x *Bwt_Passthrough_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bwt_Passthrough_Request.ProtoReflect.Descriptor instead.
func (*Bwt_Passthrough_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{8}
}

func (x *Bwt_Passthrough_Request) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

type Bwt_Passthrough_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response to a previous BWT command
	Response string `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *Bwt_Passthrough_Reply) Reset() {
	*x = Bwt_Passthrough_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bwt_Passthrough_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bwt_Passthrough_Reply) ProtoMessage() {}

func (x *Bwt_Passthrough_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bwt_Passthrough_Reply.ProtoReflect.Descriptor instead.
func (*Bwt_Passthrough_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{9}
}

func (x *Bwt_Passthrough_Reply) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

type Bwt_Transport_Get_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Bwt_Transport_Get_Config_Request) Reset() {
	*x = Bwt_Transport_Get_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bwt_Transport_Get_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bwt_Transport_Get_Config_Request) ProtoMessage() {}

func (x *Bwt_Transport_Get_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bwt_Transport_Get_Config_Request.ProtoReflect.Descriptor instead.
func (*Bwt_Transport_Get_Config_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{10}
}

type Bwt_Transport_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Print all BWT RX/TX messages to syslog
	LogMessages bool `protobuf:"varint,1,opt,name=log_messages,json=logMessages,proto3" json:"log_messages,omitempty"`
	// Force additional wait/delays between commands
	IntercommandDelay bool `protobuf:"varint,2,opt,name=intercommand_delay,json=intercommandDelay,proto3" json:"intercommand_delay,omitempty"`
}

func (x *Bwt_Transport_Config) Reset() {
	*x = Bwt_Transport_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bwt_Transport_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bwt_Transport_Config) ProtoMessage() {}

func (x *Bwt_Transport_Config) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bwt_Transport_Config.ProtoReflect.Descriptor instead.
func (*Bwt_Transport_Config) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{11}
}

func (x *Bwt_Transport_Config) GetLogMessages() bool {
	if x != nil {
		return x.LogMessages
	}
	return false
}

func (x *Bwt_Transport_Config) GetIntercommandDelay() bool {
	if x != nil {
		return x.IntercommandDelay
	}
	return false
}

// Generic diode laser status
type Diode_Status_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Diode_Status_Request) Reset() {
	*x = Diode_Status_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Diode_Status_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Diode_Status_Request) ProtoMessage() {}

func (x *Diode_Status_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Diode_Status_Request.ProtoReflect.Descriptor instead.
func (*Diode_Status_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{12}
}

type Diode_Status_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// timestamp this data was acquired (msec)
	Timestamp uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// laser system temperature and humidity readings
	Temp     []float32 `protobuf:"fixed32,2,rep,packed,name=temp,proto3" json:"temp,omitempty"`
	Humidity []float32 `protobuf:"fixed32,3,rep,packed,name=humidity,proto3" json:"humidity,omitempty"`
	// Laser diode current
	Current []float32 `protobuf:"fixed32,4,rep,packed,name=current,proto3" json:"current,omitempty"`
	// error state flag: type specific may have more info
	Faulted bool `protobuf:"varint,5,opt,name=faulted,proto3" json:"faulted,omitempty"`
	// converted thermistor temperatures (0 = collimator, 1 = fiber)
	Thermistors []float32 `protobuf:"fixed32,6,rep,packed,name=thermistors,proto3" json:"thermistors,omitempty"`
	// type specific bonus data
	//
	// Types that are assignable to Extra:
	//
	//	*Diode_Status_Reply_ExtraBwt
	//	*Diode_Status_Reply_ExtraJlight
	Extra isDiode_Status_Reply_Extra `protobuf_oneof:"extra"`
}

func (x *Diode_Status_Reply) Reset() {
	*x = Diode_Status_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Diode_Status_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Diode_Status_Reply) ProtoMessage() {}

func (x *Diode_Status_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Diode_Status_Reply.ProtoReflect.Descriptor instead.
func (*Diode_Status_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{13}
}

func (x *Diode_Status_Reply) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Diode_Status_Reply) GetTemp() []float32 {
	if x != nil {
		return x.Temp
	}
	return nil
}

func (x *Diode_Status_Reply) GetHumidity() []float32 {
	if x != nil {
		return x.Humidity
	}
	return nil
}

func (x *Diode_Status_Reply) GetCurrent() []float32 {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *Diode_Status_Reply) GetFaulted() bool {
	if x != nil {
		return x.Faulted
	}
	return false
}

func (x *Diode_Status_Reply) GetThermistors() []float32 {
	if x != nil {
		return x.Thermistors
	}
	return nil
}

func (m *Diode_Status_Reply) GetExtra() isDiode_Status_Reply_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *Diode_Status_Reply) GetExtraBwt() *Bwt_Status {
	if x, ok := x.GetExtra().(*Diode_Status_Reply_ExtraBwt); ok {
		return x.ExtraBwt
	}
	return nil
}

func (x *Diode_Status_Reply) GetExtraJlight() *Jlight_Status {
	if x, ok := x.GetExtra().(*Diode_Status_Reply_ExtraJlight); ok {
		return x.ExtraJlight
	}
	return nil
}

type isDiode_Status_Reply_Extra interface {
	isDiode_Status_Reply_Extra()
}

type Diode_Status_Reply_ExtraBwt struct {
	ExtraBwt *Bwt_Status `protobuf:"bytes,7,opt,name=extra_bwt,json=extraBwt,proto3,oneof"`
}

type Diode_Status_Reply_ExtraJlight struct {
	ExtraJlight *Jlight_Status `protobuf:"bytes,8,opt,name=extra_jlight,json=extraJlight,proto3,oneof"`
}

func (*Diode_Status_Reply_ExtraBwt) isDiode_Status_Reply_Extra() {}

func (*Diode_Status_Reply_ExtraJlight) isDiode_Status_Reply_Extra() {}

// Set diode output current
type Diode_Set_Current_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Current []uint32 `protobuf:"varint,1,rep,packed,name=current,proto3" json:"current,omitempty"`
	// when set, the currents are committed to nonvolatile memory
	Commit bool `protobuf:"varint,2,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (x *Diode_Set_Current_Request) Reset() {
	*x = Diode_Set_Current_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Diode_Set_Current_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Diode_Set_Current_Request) ProtoMessage() {}

func (x *Diode_Set_Current_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Diode_Set_Current_Request.ProtoReflect.Descriptor instead.
func (*Diode_Set_Current_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{14}
}

func (x *Diode_Set_Current_Request) GetCurrent() []uint32 {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *Diode_Set_Current_Request) GetCommit() bool {
	if x != nil {
		return x.Commit
	}
	return false
}

type Laser_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	On bool `protobuf:"varint,1,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *Laser_Request) Reset() {
	*x = Laser_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Request) ProtoMessage() {}

func (x *Laser_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Request.ProtoReflect.Descriptor instead.
func (*Laser_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{15}
}

func (x *Laser_Request) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

type Get_Laser_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Laser_Request) Reset() {
	*x = Get_Laser_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Laser_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Laser_Request) ProtoMessage() {}

func (x *Get_Laser_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Laser_Request.ProtoReflect.Descriptor instead.
func (*Get_Laser_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{16}
}

type Intensity_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intensity int32 `protobuf:"varint,1,opt,name=intensity,proto3" json:"intensity,omitempty"` // uint16_t
}

func (x *Intensity_Request) Reset() {
	*x = Intensity_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Intensity_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Intensity_Request) ProtoMessage() {}

func (x *Intensity_Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Intensity_Request.ProtoReflect.Descriptor instead.
func (*Intensity_Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{17}
}

func (x *Intensity_Request) GetIntensity() int32 {
	if x != nil {
		return x.Intensity
	}
	return 0
}

// Replies
type Laser_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawTherm1ReadingMv int32 `protobuf:"varint,1,opt,name=raw_therm1_reading_mv,json=rawTherm1ReadingMv,proto3" json:"raw_therm1_reading_mv,omitempty"`
	RawTherm2ReadingMv int32 `protobuf:"varint,2,opt,name=raw_therm2_reading_mv,json=rawTherm2ReadingMv,proto3" json:"raw_therm2_reading_mv,omitempty"`
	On                 bool  `protobuf:"varint,3,opt,name=on,proto3" json:"on,omitempty"`
	LpsuState          bool  `protobuf:"varint,4,opt,name=lpsu_state,json=lpsuState,proto3" json:"lpsu_state,omitempty"`
	Fireable           bool  `protobuf:"varint,5,opt,name=fireable,proto3" json:"fireable,omitempty"`
}

func (x *Laser_Reply) Reset() {
	*x = Laser_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Reply) ProtoMessage() {}

func (x *Laser_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Reply.ProtoReflect.Descriptor instead.
func (*Laser_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{18}
}

func (x *Laser_Reply) GetRawTherm1ReadingMv() int32 {
	if x != nil {
		return x.RawTherm1ReadingMv
	}
	return 0
}

func (x *Laser_Reply) GetRawTherm2ReadingMv() int32 {
	if x != nil {
		return x.RawTherm2ReadingMv
	}
	return 0
}

func (x *Laser_Reply) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

func (x *Laser_Reply) GetLpsuState() bool {
	if x != nil {
		return x.LpsuState
	}
	return false
}

func (x *Laser_Reply) GetFireable() bool {
	if x != nil {
		return x.Fireable
	}
	return false
}

type Laser_State_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	On bool `protobuf:"varint,1,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *Laser_State_Reply) Reset() {
	*x = Laser_State_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_State_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_State_Reply) ProtoMessage() {}

func (x *Laser_State_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_State_Reply.ProtoReflect.Descriptor instead.
func (*Laser_State_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{19}
}

func (x *Laser_State_Reply) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

type Laser_Status_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LpsuState   bool    `protobuf:"varint,1,opt,name=lpsu_state,json=lpsuState,proto3" json:"lpsu_state,omitempty"`
	LpsuCurrent float32 `protobuf:"fixed32,2,opt,name=lpsu_current,json=lpsuCurrent,proto3" json:"lpsu_current,omitempty"`
	Power       float32 `protobuf:"fixed32,3,opt,name=power,proto3" json:"power,omitempty"`
	ArcDetected bool    `protobuf:"varint,4,opt,name=arc_detected,json=arcDetected,proto3" json:"arc_detected,omitempty"`
}

func (x *Laser_Status_Reply) Reset() {
	*x = Laser_Status_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Status_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Status_Reply) ProtoMessage() {}

func (x *Laser_Status_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Status_Reply.ProtoReflect.Descriptor instead.
func (*Laser_Status_Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{20}
}

func (x *Laser_Status_Reply) GetLpsuState() bool {
	if x != nil {
		return x.LpsuState
	}
	return false
}

func (x *Laser_Status_Reply) GetLpsuCurrent() float32 {
	if x != nil {
		return x.LpsuCurrent
	}
	return 0
}

func (x *Laser_Status_Reply) GetPower() float32 {
	if x != nil {
		return x.Power
	}
	return 0
}

func (x *Laser_Status_Reply) GetArcDetected() bool {
	if x != nil {
		return x.ArcDetected
	}
	return false
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Laser
	//	*Request_GetLaser_
	//	*Request_Intensity
	//	*Request_RawData
	//	*Request_DiodeStatus
	//	*Request_LaserInventory
	//	*Request_BwtPassthrough
	//	*Request_BwtSetConfig
	//	*Request_BwtGetConfig
	//	*Request_LaserReset
	//	*Request_DiodeSetCurrent
	//	*Request_SetType
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{21}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetLaser() *Laser_Request {
	if x, ok := x.GetRequest().(*Request_Laser); ok {
		return x.Laser
	}
	return nil
}

func (x *Request) GetGetLaser_() *Get_Laser_Request {
	if x, ok := x.GetRequest().(*Request_GetLaser_); ok {
		return x.GetLaser_
	}
	return nil
}

func (x *Request) GetIntensity() *Intensity_Request {
	if x, ok := x.GetRequest().(*Request_Intensity); ok {
		return x.Intensity
	}
	return nil
}

func (x *Request) GetRawData() *Raw_Data_Request {
	if x, ok := x.GetRequest().(*Request_RawData); ok {
		return x.RawData
	}
	return nil
}

func (x *Request) GetDiodeStatus() *Diode_Status_Request {
	if x, ok := x.GetRequest().(*Request_DiodeStatus); ok {
		return x.DiodeStatus
	}
	return nil
}

func (x *Request) GetLaserInventory() *Laser_Inventory_Request {
	if x, ok := x.GetRequest().(*Request_LaserInventory); ok {
		return x.LaserInventory
	}
	return nil
}

func (x *Request) GetBwtPassthrough() *Bwt_Passthrough_Request {
	if x, ok := x.GetRequest().(*Request_BwtPassthrough); ok {
		return x.BwtPassthrough
	}
	return nil
}

func (x *Request) GetBwtSetConfig() *Bwt_Transport_Config {
	if x, ok := x.GetRequest().(*Request_BwtSetConfig); ok {
		return x.BwtSetConfig
	}
	return nil
}

func (x *Request) GetBwtGetConfig() *Bwt_Transport_Get_Config_Request {
	if x, ok := x.GetRequest().(*Request_BwtGetConfig); ok {
		return x.BwtGetConfig
	}
	return nil
}

func (x *Request) GetLaserReset() *Laser_Reset_Request {
	if x, ok := x.GetRequest().(*Request_LaserReset); ok {
		return x.LaserReset
	}
	return nil
}

func (x *Request) GetDiodeSetCurrent() *Diode_Set_Current_Request {
	if x, ok := x.GetRequest().(*Request_DiodeSetCurrent); ok {
		return x.DiodeSetCurrent
	}
	return nil
}

func (x *Request) GetSetType() *Laser_Set_Type_Request {
	if x, ok := x.GetRequest().(*Request_SetType); ok {
		return x.SetType
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Laser struct {
	Laser *Laser_Request `protobuf:"bytes,1,opt,name=laser,proto3,oneof"`
}

type Request_GetLaser_ struct {
	GetLaser_ *Get_Laser_Request `protobuf:"bytes,2,opt,name=get_laser,json=getLaser,proto3,oneof"`
}

type Request_Intensity struct {
	Intensity *Intensity_Request `protobuf:"bytes,3,opt,name=intensity,proto3,oneof"`
}

type Request_RawData struct {
	RawData *Raw_Data_Request `protobuf:"bytes,4,opt,name=raw_data,json=rawData,proto3,oneof"`
}

type Request_DiodeStatus struct {
	DiodeStatus *Diode_Status_Request `protobuf:"bytes,5,opt,name=diode_status,json=diodeStatus,proto3,oneof"`
}

type Request_LaserInventory struct {
	LaserInventory *Laser_Inventory_Request `protobuf:"bytes,6,opt,name=laser_inventory,json=laserInventory,proto3,oneof"`
}

type Request_BwtPassthrough struct {
	BwtPassthrough *Bwt_Passthrough_Request `protobuf:"bytes,7,opt,name=bwt_passthrough,json=bwtPassthrough,proto3,oneof"`
}

type Request_BwtSetConfig struct {
	BwtSetConfig *Bwt_Transport_Config `protobuf:"bytes,9,opt,name=bwt_set_config,json=bwtSetConfig,proto3,oneof"`
}

type Request_BwtGetConfig struct {
	BwtGetConfig *Bwt_Transport_Get_Config_Request `protobuf:"bytes,10,opt,name=bwt_get_config,json=bwtGetConfig,proto3,oneof"`
}

type Request_LaserReset struct {
	LaserReset *Laser_Reset_Request `protobuf:"bytes,11,opt,name=laser_reset,json=laserReset,proto3,oneof"`
}

type Request_DiodeSetCurrent struct {
	DiodeSetCurrent *Diode_Set_Current_Request `protobuf:"bytes,12,opt,name=diode_set_current,json=diodeSetCurrent,proto3,oneof"`
}

type Request_SetType struct {
	SetType *Laser_Set_Type_Request `protobuf:"bytes,13,opt,name=set_type,json=setType,proto3,oneof"`
}

func (*Request_Laser) isRequest_Request() {}

func (*Request_GetLaser_) isRequest_Request() {}

func (*Request_Intensity) isRequest_Request() {}

func (*Request_RawData) isRequest_Request() {}

func (*Request_DiodeStatus) isRequest_Request() {}

func (*Request_LaserInventory) isRequest_Request() {}

func (*Request_BwtPassthrough) isRequest_Request() {}

func (*Request_BwtSetConfig) isRequest_Request() {}

func (*Request_BwtGetConfig) isRequest_Request() {}

func (*Request_LaserReset) isRequest_Request() {}

func (*Request_DiodeSetCurrent) isRequest_Request() {}

func (*Request_SetType) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Laser
	//	*Reply_LaserReply
	//	*Reply_RawData
	//	*Reply_DiodeStatus
	//	*Reply_LaserInventory
	//	*Reply_BwtPassthrough
	//	*Reply_BwtConfig
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_laser_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_laser_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_laser_proto_rawDescGZIP(), []int{22}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetLaser() *Laser_State_Reply {
	if x, ok := x.GetReply().(*Reply_Laser); ok {
		return x.Laser
	}
	return nil
}

func (x *Reply) GetLaserReply() *Laser_Reply {
	if x, ok := x.GetReply().(*Reply_LaserReply); ok {
		return x.LaserReply
	}
	return nil
}

func (x *Reply) GetRawData() *Raw_Data_Reply {
	if x, ok := x.GetReply().(*Reply_RawData); ok {
		return x.RawData
	}
	return nil
}

func (x *Reply) GetDiodeStatus() *Diode_Status_Reply {
	if x, ok := x.GetReply().(*Reply_DiodeStatus); ok {
		return x.DiodeStatus
	}
	return nil
}

func (x *Reply) GetLaserInventory() *Laser_Inventory_Reply {
	if x, ok := x.GetReply().(*Reply_LaserInventory); ok {
		return x.LaserInventory
	}
	return nil
}

func (x *Reply) GetBwtPassthrough() *Bwt_Passthrough_Reply {
	if x, ok := x.GetReply().(*Reply_BwtPassthrough); ok {
		return x.BwtPassthrough
	}
	return nil
}

func (x *Reply) GetBwtConfig() *Bwt_Transport_Config {
	if x, ok := x.GetReply().(*Reply_BwtConfig); ok {
		return x.BwtConfig
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Laser struct {
	Laser *Laser_State_Reply `protobuf:"bytes,3,opt,name=laser,proto3,oneof"`
}

type Reply_LaserReply struct {
	LaserReply *Laser_Reply `protobuf:"bytes,4,opt,name=laser_reply,json=laserReply,proto3,oneof"`
}

type Reply_RawData struct {
	RawData *Raw_Data_Reply `protobuf:"bytes,5,opt,name=raw_data,json=rawData,proto3,oneof"`
}

type Reply_DiodeStatus struct {
	DiodeStatus *Diode_Status_Reply `protobuf:"bytes,6,opt,name=diode_status,json=diodeStatus,proto3,oneof"`
}

type Reply_LaserInventory struct {
	LaserInventory *Laser_Inventory_Reply `protobuf:"bytes,7,opt,name=laser_inventory,json=laserInventory,proto3,oneof"`
}

type Reply_BwtPassthrough struct {
	BwtPassthrough *Bwt_Passthrough_Reply `protobuf:"bytes,8,opt,name=bwt_passthrough,json=bwtPassthrough,proto3,oneof"`
}

type Reply_BwtConfig struct {
	BwtConfig *Bwt_Transport_Config `protobuf:"bytes,9,opt,name=bwt_config,json=bwtConfig,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Laser) isReply_Reply() {}

func (*Reply_LaserReply) isReply_Reply() {}

func (*Reply_RawData) isReply_Reply() {}

func (*Reply_DiodeStatus) isReply_Reply() {}

func (*Reply_LaserInventory) isReply_Reply() {}

func (*Reply_BwtPassthrough) isReply_Reply() {}

func (*Reply_BwtConfig) isReply_Reply() {}

var File_laser_proto protoreflect.FileDescriptor

var file_laser_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6c,
	0x61, 0x73, 0x65, 0x72, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f,
	0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f,
	0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x12, 0x0a, 0x10, 0x52, 0x61, 0x77, 0x5f, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb5, 0x01, 0x0a, 0x0e, 0x52, 0x61, 0x77, 0x5f, 0x44,
	0x61, 0x74, 0x61, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x68, 0x65,
	0x72, 0x6d, 0x31, 0x5f, 0x72, 0x61, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52,
	0x09, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x31, 0x52, 0x61, 0x77, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a,
	0x0a, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x32, 0x5f, 0x72, 0x61, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x01, 0x52, 0x09, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x32, 0x52, 0x61, 0x77, 0x88, 0x01,
	0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x5f,
	0x72, 0x61, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x0d, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x52, 0x61, 0x77, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x31, 0x5f, 0x72, 0x61, 0x77, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x32, 0x5f, 0x72, 0x61, 0x77, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x5f, 0x72, 0x61, 0x77, 0x22, 0x19,
	0x0a, 0x17, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72,
	0x79, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x15, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x5f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x3e, 0x0a,
	0x16, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x53, 0x65, 0x74, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x4b, 0x0a,
	0x13, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xa4, 0x01, 0x0a, 0x0d, 0x4a,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x73, 0x75, 0x54, 0x65, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x70,
	0x73, 0x75, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x73, 0x75, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x56, 0x6f, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70,
	0x73, 0x75, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x6f, 0x6c, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0e,
	0x70, 0x73, 0x75, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x6f, 0x6c, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x0e, 0x70, 0x73, 0x75, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56,
	0x6f, 0x6c, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4a, 0x6c, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x73, 0x22, 0x24, 0x0a, 0x0a, 0x42, 0x77, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x06, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x33, 0x0a, 0x17, 0x42, 0x77, 0x74, 0x5f, 0x50,
	0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x22, 0x33, 0x0a, 0x15,
	0x42, 0x77, 0x74, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x22, 0x0a, 0x20, 0x42, 0x77, 0x74, 0x5f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x68, 0x0a, 0x14, 0x42, 0x77, 0x74, 0x5f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a,
	0x0c, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x6c, 0x6f, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x2d, 0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x22,
	0x16, 0x0a, 0x14, 0x44, 0x69, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xae, 0x02, 0x0a, 0x12, 0x44, 0x69, 0x6f, 0x64,
	0x65, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x65, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x04, 0x74, 0x65, 0x6d, 0x70,
	0x12, 0x1a, 0x0a, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x02, 0x52, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x02, 0x52, 0x07, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x65, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x62, 0x77, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x77,
	0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x08, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x42, 0x77, 0x74, 0x12, 0x39, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6a, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x2e, 0x4a, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x48, 0x00, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4a, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x42,
	0x07, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x4d, 0x0a, 0x19, 0x44, 0x69, 0x6f, 0x64,
	0x65, 0x5f, 0x53, 0x65, 0x74, 0x5f, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x22, 0x1f, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6e, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x5f,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a,
	0x11, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79,
	0x22, 0xbe, 0x01, 0x0a, 0x0b, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x31, 0x0a, 0x15, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x31, 0x5f, 0x72,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x12, 0x72, 0x61, 0x77, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x31, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x4d, 0x76, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x61, 0x77, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d,
	0x32, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x76, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x72, 0x61, 0x77, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x32, 0x52, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x76, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x02, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x70, 0x73, 0x75, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6c, 0x70, 0x73, 0x75,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x72, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x69, 0x72, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0x23, 0x0a, 0x11, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x02, 0x6f, 0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x12, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x70, 0x73, 0x75, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x6c, 0x70, 0x73, 0x75, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x6c, 0x70, 0x73, 0x75, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0b, 0x6c, 0x70, 0x73, 0x75, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x72, 0x63,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xaa, 0x06, 0x0a, 0x07, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x12, 0x37, 0x0a, 0x09, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x5f, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x08, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x09, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x79, 0x12, 0x34, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e,
	0x52, 0x61, 0x77, 0x5f, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0c, 0x64,
	0x69, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x6f, 0x64, 0x65, 0x5f,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x0b, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a,
	0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x5f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x49, 0x0a, 0x0f, 0x62, 0x77, 0x74, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x77, 0x74, 0x5f, 0x50, 0x61,
	0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0e, 0x62, 0x77, 0x74, 0x50, 0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f,
	0x75, 0x67, 0x68, 0x12, 0x43, 0x0a, 0x0e, 0x62, 0x77, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x2e, 0x42, 0x77, 0x74, 0x5f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x77, 0x74, 0x53,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4f, 0x0a, 0x0e, 0x62, 0x77, 0x74, 0x5f,
	0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x77, 0x74, 0x5f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x77, 0x74,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0b, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x65, 0x74, 0x12, 0x4e, 0x0a, 0x11, 0x64, 0x69, 0x6f, 0x64,
	0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x6f, 0x64,
	0x65, 0x5f, 0x53, 0x65, 0x74, 0x5f, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x53, 0x65,
	0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x53, 0x65, 0x74, 0x5f, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4a,
	0x04, 0x08, 0x08, 0x10, 0x09, 0x22, 0x81, 0x04, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03,
	0x61, 0x63, 0x6b, 0x12, 0x30, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x70, 0x6c, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x0a, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a, 0x08,
	0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x77, 0x5f, 0x44, 0x61, 0x74, 0x61, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3e, 0x0a, 0x0c, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x44,
	0x69, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x47, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72,
	0x79, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x47, 0x0a, 0x0f, 0x62, 0x77, 0x74,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x77, 0x74, 0x5f, 0x50,
	0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x0e, 0x62, 0x77, 0x74, 0x50, 0x61, 0x73, 0x73, 0x74, 0x68, 0x72, 0x6f, 0x75,
	0x67, 0x68, 0x12, 0x3c, 0x0a, 0x0a, 0x62, 0x77, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x42,
	0x77, 0x74, 0x5f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x62, 0x77, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x6a, 0x0a, 0x09, 0x4c, 0x61, 0x73,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x32, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49,
	0x4f, 0x44, 0x45, 0x5f, 0x42, 0x57, 0x54, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x41, 0x53,
	0x45, 0x52, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x4f, 0x44, 0x45, 0x5f, 0x4a, 0x4c, 0x49,
	0x47, 0x48, 0x54, 0x10, 0x03, 0x2a, 0xc5, 0x02, 0x0a, 0x0c, 0x4a, 0x6c, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4c, 0x49, 0x47, 0x48, 0x54,
	0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x1a, 0x0a, 0x16, 0x4a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x4a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x53, 0x59, 0x53,
	0x54, 0x45, 0x4d, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x48, 0x31, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x43, 0x55,
	0x52, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x4a, 0x4c, 0x49, 0x47, 0x48,
	0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x48, 0x32, 0x5f, 0x4f, 0x56, 0x45, 0x52,
	0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x4a, 0x4c, 0x49,
	0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x48, 0x31, 0x5f, 0x4f, 0x56,
	0x45, 0x52, 0x56, 0x4f, 0x4c, 0x54, 0x41, 0x47, 0x45, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x4a,
	0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x48, 0x32, 0x5f,
	0x4f, 0x56, 0x45, 0x52, 0x56, 0x4f, 0x4c, 0x54, 0x41, 0x47, 0x45, 0x10, 0x06, 0x12, 0x1d, 0x0a,
	0x19, 0x4a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x48,
	0x31, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x54, 0x45, 0x4d, 0x50, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19,
	0x4a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x43, 0x48, 0x32,
	0x5f, 0x4f, 0x56, 0x45, 0x52, 0x54, 0x45, 0x4d, 0x50, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x4a,
	0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x44, 0x52, 0x49, 0x56,
	0x45, 0x52, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x54, 0x45, 0x4d, 0x50, 0x10, 0x09, 0x42, 0x0e, 0x5a,
	0x0c, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_laser_proto_rawDescOnce sync.Once
	file_laser_proto_rawDescData = file_laser_proto_rawDesc
)

func file_laser_proto_rawDescGZIP() []byte {
	file_laser_proto_rawDescOnce.Do(func() {
		file_laser_proto_rawDescData = protoimpl.X.CompressGZIP(file_laser_proto_rawDescData)
	})
	return file_laser_proto_rawDescData
}

var file_laser_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_laser_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_laser_proto_goTypes = []interface{}{
	(LaserType)(0),                           // 0: laser.LaserType
	(Jlight_Fault)(0),                        // 1: laser.Jlight_Fault
	(*Raw_Data_Request)(nil),                 // 2: laser.Raw_Data_Request
	(*Raw_Data_Reply)(nil),                   // 3: laser.Raw_Data_Reply
	(*Laser_Inventory_Request)(nil),          // 4: laser.Laser_Inventory_Request
	(*Laser_Inventory_Reply)(nil),            // 5: laser.Laser_Inventory_Reply
	(*Laser_Set_Type_Request)(nil),           // 6: laser.Laser_Set_Type_Request
	(*Laser_Reset_Request)(nil),              // 7: laser.Laser_Reset_Request
	(*Jlight_Status)(nil),                    // 8: laser.Jlight_Status
	(*Bwt_Status)(nil),                       // 9: laser.Bwt_Status
	(*Bwt_Passthrough_Request)(nil),          // 10: laser.Bwt_Passthrough_Request
	(*Bwt_Passthrough_Reply)(nil),            // 11: laser.Bwt_Passthrough_Reply
	(*Bwt_Transport_Get_Config_Request)(nil), // 12: laser.Bwt_Transport_Get_Config_Request
	(*Bwt_Transport_Config)(nil),             // 13: laser.Bwt_Transport_Config
	(*Diode_Status_Request)(nil),             // 14: laser.Diode_Status_Request
	(*Diode_Status_Reply)(nil),               // 15: laser.Diode_Status_Reply
	(*Diode_Set_Current_Request)(nil),        // 16: laser.Diode_Set_Current_Request
	(*Laser_Request)(nil),                    // 17: laser.Laser_Request
	(*Get_Laser_Request)(nil),                // 18: laser.Get_Laser_Request
	(*Intensity_Request)(nil),                // 19: laser.Intensity_Request
	(*Laser_Reply)(nil),                      // 20: laser.Laser_Reply
	(*Laser_State_Reply)(nil),                // 21: laser.Laser_State_Reply
	(*Laser_Status_Reply)(nil),               // 22: laser.Laser_Status_Reply
	(*Request)(nil),                          // 23: laser.Request
	(*Reply)(nil),                            // 24: laser.Reply
	(*error1.Error)(nil),                     // 25: error.Error
	(*ack.Ack)(nil),                          // 26: ack.Ack
}
var file_laser_proto_depIdxs = []int32{
	0,  // 0: laser.Laser_Inventory_Reply.type:type_name -> laser.LaserType
	0,  // 1: laser.Laser_Set_Type_Request.type:type_name -> laser.LaserType
	1,  // 2: laser.Jlight_Status.faults:type_name -> laser.Jlight_Fault
	9,  // 3: laser.Diode_Status_Reply.extra_bwt:type_name -> laser.Bwt_Status
	8,  // 4: laser.Diode_Status_Reply.extra_jlight:type_name -> laser.Jlight_Status
	17, // 5: laser.Request.laser:type_name -> laser.Laser_Request
	18, // 6: laser.Request.get_laser:type_name -> laser.Get_Laser_Request
	19, // 7: laser.Request.intensity:type_name -> laser.Intensity_Request
	2,  // 8: laser.Request.raw_data:type_name -> laser.Raw_Data_Request
	14, // 9: laser.Request.diode_status:type_name -> laser.Diode_Status_Request
	4,  // 10: laser.Request.laser_inventory:type_name -> laser.Laser_Inventory_Request
	10, // 11: laser.Request.bwt_passthrough:type_name -> laser.Bwt_Passthrough_Request
	13, // 12: laser.Request.bwt_set_config:type_name -> laser.Bwt_Transport_Config
	12, // 13: laser.Request.bwt_get_config:type_name -> laser.Bwt_Transport_Get_Config_Request
	7,  // 14: laser.Request.laser_reset:type_name -> laser.Laser_Reset_Request
	16, // 15: laser.Request.diode_set_current:type_name -> laser.Diode_Set_Current_Request
	6,  // 16: laser.Request.set_type:type_name -> laser.Laser_Set_Type_Request
	25, // 17: laser.Reply.error:type_name -> error.Error
	26, // 18: laser.Reply.ack:type_name -> ack.Ack
	21, // 19: laser.Reply.laser:type_name -> laser.Laser_State_Reply
	20, // 20: laser.Reply.laser_reply:type_name -> laser.Laser_Reply
	3,  // 21: laser.Reply.raw_data:type_name -> laser.Raw_Data_Reply
	15, // 22: laser.Reply.diode_status:type_name -> laser.Diode_Status_Reply
	5,  // 23: laser.Reply.laser_inventory:type_name -> laser.Laser_Inventory_Reply
	11, // 24: laser.Reply.bwt_passthrough:type_name -> laser.Bwt_Passthrough_Reply
	13, // 25: laser.Reply.bwt_config:type_name -> laser.Bwt_Transport_Config
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_laser_proto_init() }
func file_laser_proto_init() {
	if File_laser_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_laser_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Raw_Data_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Raw_Data_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Inventory_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Inventory_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Set_Type_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Reset_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Jlight_Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bwt_Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bwt_Passthrough_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bwt_Passthrough_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bwt_Transport_Get_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bwt_Transport_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Diode_Status_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Diode_Status_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Diode_Set_Current_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Laser_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Intensity_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_State_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Status_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_laser_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_laser_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_laser_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*Diode_Status_Reply_ExtraBwt)(nil),
		(*Diode_Status_Reply_ExtraJlight)(nil),
	}
	file_laser_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*Request_Laser)(nil),
		(*Request_GetLaser_)(nil),
		(*Request_Intensity)(nil),
		(*Request_RawData)(nil),
		(*Request_DiodeStatus)(nil),
		(*Request_LaserInventory)(nil),
		(*Request_BwtPassthrough)(nil),
		(*Request_BwtSetConfig)(nil),
		(*Request_BwtGetConfig)(nil),
		(*Request_LaserReset)(nil),
		(*Request_DiodeSetCurrent)(nil),
		(*Request_SetType)(nil),
	}
	file_laser_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Laser)(nil),
		(*Reply_LaserReply)(nil),
		(*Reply_RawData)(nil),
		(*Reply_DiodeStatus)(nil),
		(*Reply_LaserInventory)(nil),
		(*Reply_BwtPassthrough)(nil),
		(*Reply_BwtConfig)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_laser_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_laser_proto_goTypes,
		DependencyIndexes: file_laser_proto_depIdxs,
		EnumInfos:         file_laser_proto_enumTypes,
		MessageInfos:      file_laser_proto_msgTypes,
	}.Build()
	File_laser_proto = out.File
	file_laser_proto_rawDesc = nil
	file_laser_proto_goTypes = nil
	file_laser_proto_depIdxs = nil
}

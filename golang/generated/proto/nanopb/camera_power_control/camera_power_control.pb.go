// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: camera_power_control.proto

package camera_power_control

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CameraId uint32 `protobuf:"varint,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	PowerOn  bool   `protobuf:"varint,2,opt,name=power_on,json=powerOn,proto3" json:"power_on,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_camera_power_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_camera_power_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_camera_power_control_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetCameraId() uint32 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *Request) GetPowerOn() bool {
	if x != nil {
		return x.PowerOn
	}
	return false
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ok bool `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_camera_power_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_camera_power_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_camera_power_control_proto_rawDescGZIP(), []int{1}
}

func (x *Reply) GetOk() bool {
	if x != nil {
		return x.Ok
	}
	return false
}

var File_camera_power_control_proto protoreflect.FileDescriptor

var file_camera_power_control_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x22, 0x41, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x4f, 0x6e, 0x22, 0x17, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6b, 0x42, 0x1d,
	0x5a, 0x1b, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_camera_power_control_proto_rawDescOnce sync.Once
	file_camera_power_control_proto_rawDescData = file_camera_power_control_proto_rawDesc
)

func file_camera_power_control_proto_rawDescGZIP() []byte {
	file_camera_power_control_proto_rawDescOnce.Do(func() {
		file_camera_power_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_camera_power_control_proto_rawDescData)
	})
	return file_camera_power_control_proto_rawDescData
}

var file_camera_power_control_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_camera_power_control_proto_goTypes = []interface{}{
	(*Request)(nil), // 0: camera_power_control.Request
	(*Reply)(nil),   // 1: camera_power_control.Reply
}
var file_camera_power_control_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_camera_power_control_proto_init() }
func file_camera_power_control_proto_init() {
	if File_camera_power_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_camera_power_control_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_camera_power_control_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_camera_power_control_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_camera_power_control_proto_goTypes,
		DependencyIndexes: file_camera_power_control_proto_depIdxs,
		MessageInfos:      file_camera_power_control_proto_msgTypes,
	}.Build()
	File_camera_power_control_proto = out.File
	file_camera_power_control_proto_rawDesc = nil
	file_camera_power_control_proto_goTypes = nil
	file_camera_power_control_proto_depIdxs = nil
}

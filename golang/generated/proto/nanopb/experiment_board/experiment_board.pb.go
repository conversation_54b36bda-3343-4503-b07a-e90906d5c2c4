// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: experiment_board.proto

package experiment_board

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	row_module "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/row_module"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExperimentItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *ExperimentItem) Reset() {
	*x = ExperimentItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_experiment_board_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperimentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperimentItem) ProtoMessage() {}

func (x *ExperimentItem) ProtoReflect() protoreflect.Message {
	mi := &file_experiment_board_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperimentItem.ProtoReflect.Descriptor instead.
func (*ExperimentItem) Descriptor() ([]byte, []int) {
	return file_experiment_board_proto_rawDescGZIP(), []int{0}
}

func (x *ExperimentItem) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type ExperimentRepeated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ExperimentItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ExperimentRepeated) Reset() {
	*x = ExperimentRepeated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_experiment_board_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperimentRepeated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperimentRepeated) ProtoMessage() {}

func (x *ExperimentRepeated) ProtoReflect() protoreflect.Message {
	mi := &file_experiment_board_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperimentRepeated.ProtoReflect.Descriptor instead.
func (*ExperimentRepeated) Descriptor() ([]byte, []int) {
	return file_experiment_board_proto_rawDescGZIP(), []int{1}
}

func (x *ExperimentRepeated) GetItems() []*ExperimentItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type ExperimentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sum uint32 `protobuf:"varint,1,opt,name=sum,proto3" json:"sum,omitempty"`
}

func (x *ExperimentResult) Reset() {
	*x = ExperimentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_experiment_board_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperimentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperimentResult) ProtoMessage() {}

func (x *ExperimentResult) ProtoReflect() protoreflect.Message {
	mi := &file_experiment_board_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperimentResult.ProtoReflect.Descriptor instead.
func (*ExperimentResult) Descriptor() ([]byte, []int) {
	return file_experiment_board_proto_rawDescGZIP(), []int{2}
}

func (x *ExperimentResult) GetSum() uint32 {
	if x != nil {
		return x.Sum
	}
	return 0
}

type ExperimentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*ExperimentReply_Pong
	//	*ExperimentReply_Result
	//	*ExperimentReply_RowModule
	Reply isExperimentReply_Reply `protobuf_oneof:"reply"`
}

func (x *ExperimentReply) Reset() {
	*x = ExperimentReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_experiment_board_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperimentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperimentReply) ProtoMessage() {}

func (x *ExperimentReply) ProtoReflect() protoreflect.Message {
	mi := &file_experiment_board_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperimentReply.ProtoReflect.Descriptor instead.
func (*ExperimentReply) Descriptor() ([]byte, []int) {
	return file_experiment_board_proto_rawDescGZIP(), []int{3}
}

func (x *ExperimentReply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *ExperimentReply) GetReply() isExperimentReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *ExperimentReply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*ExperimentReply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *ExperimentReply) GetResult() *ExperimentResult {
	if x, ok := x.GetReply().(*ExperimentReply_Result); ok {
		return x.Result
	}
	return nil
}

func (x *ExperimentReply) GetRowModule() *row_module.Reply {
	if x, ok := x.GetReply().(*ExperimentReply_RowModule); ok {
		return x.RowModule
	}
	return nil
}

type isExperimentReply_Reply interface {
	isExperimentReply_Reply()
}

type ExperimentReply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type ExperimentReply_Result struct {
	Result *ExperimentResult `protobuf:"bytes,3,opt,name=result,proto3,oneof"`
}

type ExperimentReply_RowModule struct {
	RowModule *row_module.Reply `protobuf:"bytes,4,opt,name=row_module,json=rowModule,proto3,oneof"`
}

func (*ExperimentReply_Pong) isExperimentReply_Reply() {}

func (*ExperimentReply_Result) isExperimentReply_Reply() {}

func (*ExperimentReply_RowModule) isExperimentReply_Reply() {}

type ExperimentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*ExperimentRequest_Ping
	//	*ExperimentRequest_Items
	//	*ExperimentRequest_RowModule
	Request isExperimentRequest_Request `protobuf_oneof:"request"`
}

func (x *ExperimentRequest) Reset() {
	*x = ExperimentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_experiment_board_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperimentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperimentRequest) ProtoMessage() {}

func (x *ExperimentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_experiment_board_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperimentRequest.ProtoReflect.Descriptor instead.
func (*ExperimentRequest) Descriptor() ([]byte, []int) {
	return file_experiment_board_proto_rawDescGZIP(), []int{4}
}

func (x *ExperimentRequest) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *ExperimentRequest) GetRequest() isExperimentRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *ExperimentRequest) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*ExperimentRequest_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *ExperimentRequest) GetItems() *ExperimentRepeated {
	if x, ok := x.GetRequest().(*ExperimentRequest_Items); ok {
		return x.Items
	}
	return nil
}

func (x *ExperimentRequest) GetRowModule() *row_module.Request {
	if x, ok := x.GetRequest().(*ExperimentRequest_RowModule); ok {
		return x.RowModule
	}
	return nil
}

type isExperimentRequest_Request interface {
	isExperimentRequest_Request()
}

type ExperimentRequest_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type ExperimentRequest_Items struct {
	Items *ExperimentRepeated `protobuf:"bytes,3,opt,name=items,proto3,oneof"`
}

type ExperimentRequest_RowModule struct {
	RowModule *row_module.Request `protobuf:"bytes,4,opt,name=row_module,json=rowModule,proto3,oneof"`
}

func (*ExperimentRequest_Ping) isExperimentRequest_Request() {}

func (*ExperimentRequest_Items) isExperimentRequest_Request() {}

func (*ExperimentRequest_RowModule) isExperimentRequest_Request() {}

var File_experiment_board_proto protoreflect.FileDescriptor

var file_experiment_board_proto_rawDesc = []byte{
	0x0a, 0x16, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1e, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x4c, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x45,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x24, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x75, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x75, 0x6d, 0x22, 0xe4, 0x01, 0x0a, 0x0f, 0x45,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26,
	0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00,
	0x52, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x12, 0x3c, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x6f, 0x77, 0x5f, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x09, 0x72,
	0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0xea, 0x01, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12,
	0x3c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x48, 0x00, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x34, 0x0a,
	0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x19,
	0x5a, 0x17, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_experiment_board_proto_rawDescOnce sync.Once
	file_experiment_board_proto_rawDescData = file_experiment_board_proto_rawDesc
)

func file_experiment_board_proto_rawDescGZIP() []byte {
	file_experiment_board_proto_rawDescOnce.Do(func() {
		file_experiment_board_proto_rawDescData = protoimpl.X.CompressGZIP(file_experiment_board_proto_rawDescData)
	})
	return file_experiment_board_proto_rawDescData
}

var file_experiment_board_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_experiment_board_proto_goTypes = []interface{}{
	(*ExperimentItem)(nil),        // 0: experiment_board.ExperimentItem
	(*ExperimentRepeated)(nil),    // 1: experiment_board.ExperimentRepeated
	(*ExperimentResult)(nil),      // 2: experiment_board.ExperimentResult
	(*ExperimentReply)(nil),       // 3: experiment_board.ExperimentReply
	(*ExperimentRequest)(nil),     // 4: experiment_board.ExperimentRequest
	(*request.RequestHeader)(nil), // 5: request.RequestHeader
	(*diagnostic.Pong)(nil),       // 6: diagnostic.Pong
	(*row_module.Reply)(nil),      // 7: row_module.Reply
	(*diagnostic.Ping)(nil),       // 8: diagnostic.Ping
	(*row_module.Request)(nil),    // 9: row_module.Request
}
var file_experiment_board_proto_depIdxs = []int32{
	0, // 0: experiment_board.ExperimentRepeated.items:type_name -> experiment_board.ExperimentItem
	5, // 1: experiment_board.ExperimentReply.header:type_name -> request.RequestHeader
	6, // 2: experiment_board.ExperimentReply.pong:type_name -> diagnostic.Pong
	2, // 3: experiment_board.ExperimentReply.result:type_name -> experiment_board.ExperimentResult
	7, // 4: experiment_board.ExperimentReply.row_module:type_name -> row_module.Reply
	5, // 5: experiment_board.ExperimentRequest.header:type_name -> request.RequestHeader
	8, // 6: experiment_board.ExperimentRequest.ping:type_name -> diagnostic.Ping
	1, // 7: experiment_board.ExperimentRequest.items:type_name -> experiment_board.ExperimentRepeated
	9, // 8: experiment_board.ExperimentRequest.row_module:type_name -> row_module.Request
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_experiment_board_proto_init() }
func file_experiment_board_proto_init() {
	if File_experiment_board_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_experiment_board_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperimentItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_experiment_board_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperimentRepeated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_experiment_board_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperimentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_experiment_board_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperimentReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_experiment_board_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperimentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_experiment_board_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*ExperimentReply_Pong)(nil),
		(*ExperimentReply_Result)(nil),
		(*ExperimentReply_RowModule)(nil),
	}
	file_experiment_board_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ExperimentRequest_Ping)(nil),
		(*ExperimentRequest_Items)(nil),
		(*ExperimentRequest_RowModule)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_experiment_board_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_experiment_board_proto_goTypes,
		DependencyIndexes: file_experiment_board_proto_depIdxs,
		MessageInfos:      file_experiment_board_proto_msgTypes,
	}.Build()
	File_experiment_board_proto = out.File
	file_experiment_board_proto_rawDesc = nil
	file_experiment_board_proto_goTypes = nil
	file_experiment_board_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: rotary_encoder.proto

package rotary_encoder

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	time "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/time"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RotaryEncodersConfig_Request_Type int32

const (
	RotaryEncodersConfig_Request_TICK RotaryEncodersConfig_Request_Type = 0
	RotaryEncodersConfig_Request_QUAD RotaryEncodersConfig_Request_Type = 1
	RotaryEncodersConfig_Request_NONE RotaryEncodersConfig_Request_Type = 2
)

// Enum value maps for RotaryEncodersConfig_Request_Type.
var (
	RotaryEncodersConfig_Request_Type_name = map[int32]string{
		0: "TICK",
		1: "QUAD",
		2: "NONE",
	}
	RotaryEncodersConfig_Request_Type_value = map[string]int32{
		"TICK": 0,
		"QUAD": 1,
		"NONE": 2,
	}
)

func (x RotaryEncodersConfig_Request_Type) Enum() *RotaryEncodersConfig_Request_Type {
	p := new(RotaryEncodersConfig_Request_Type)
	*p = x
	return p
}

func (x RotaryEncodersConfig_Request_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RotaryEncodersConfig_Request_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_rotary_encoder_proto_enumTypes[0].Descriptor()
}

func (RotaryEncodersConfig_Request_Type) Type() protoreflect.EnumType {
	return &file_rotary_encoder_proto_enumTypes[0]
}

func (x RotaryEncodersConfig_Request_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RotaryEncodersConfig_Request_Type.Descriptor instead.
func (RotaryEncodersConfig_Request_Type) EnumDescriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{0, 0}
}

// Configuration
type RotaryEncodersConfig_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FLType RotaryEncodersConfig_Request_Type `protobuf:"varint,1,opt,name=FL_type,json=FLType,proto3,enum=rotary_encoder.RotaryEncodersConfig_Request_Type" json:"FL_type,omitempty"`
	FRType RotaryEncodersConfig_Request_Type `protobuf:"varint,2,opt,name=FR_type,json=FRType,proto3,enum=rotary_encoder.RotaryEncodersConfig_Request_Type" json:"FR_type,omitempty"`
	BLType RotaryEncodersConfig_Request_Type `protobuf:"varint,3,opt,name=BL_type,json=BLType,proto3,enum=rotary_encoder.RotaryEncodersConfig_Request_Type" json:"BL_type,omitempty"`
	BRType RotaryEncodersConfig_Request_Type `protobuf:"varint,4,opt,name=BR_type,json=BRType,proto3,enum=rotary_encoder.RotaryEncodersConfig_Request_Type" json:"BR_type,omitempty"`
}

func (x *RotaryEncodersConfig_Request) Reset() {
	*x = RotaryEncodersConfig_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncodersConfig_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncodersConfig_Request) ProtoMessage() {}

func (x *RotaryEncodersConfig_Request) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncodersConfig_Request.ProtoReflect.Descriptor instead.
func (*RotaryEncodersConfig_Request) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{0}
}

func (x *RotaryEncodersConfig_Request) GetFLType() RotaryEncodersConfig_Request_Type {
	if x != nil {
		return x.FLType
	}
	return RotaryEncodersConfig_Request_TICK
}

func (x *RotaryEncodersConfig_Request) GetFRType() RotaryEncodersConfig_Request_Type {
	if x != nil {
		return x.FRType
	}
	return RotaryEncodersConfig_Request_TICK
}

func (x *RotaryEncodersConfig_Request) GetBLType() RotaryEncodersConfig_Request_Type {
	if x != nil {
		return x.BLType
	}
	return RotaryEncodersConfig_Request_TICK
}

func (x *RotaryEncodersConfig_Request) GetBRType() RotaryEncodersConfig_Request_Type {
	if x != nil {
		return x.BRType
	}
	return RotaryEncodersConfig_Request_TICK
}

type RotaryEncodersConfig_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RotaryEncodersConfig_Reply) Reset() {
	*x = RotaryEncodersConfig_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncodersConfig_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncodersConfig_Reply) ProtoMessage() {}

func (x *RotaryEncodersConfig_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncodersConfig_Reply.ProtoReflect.Descriptor instead.
func (*RotaryEncodersConfig_Reply) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{1}
}

// Rotary Request
type RotaryEncoder_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RotaryEncoder_Request) Reset() {
	*x = RotaryEncoder_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncoder_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncoder_Request) ProtoMessage() {}

func (x *RotaryEncoder_Request) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncoder_Request.ProtoReflect.Descriptor instead.
func (*RotaryEncoder_Request) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{2}
}

type RotaryEncoder_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FrontLeftTicks  int64           `protobuf:"varint,1,opt,name=front_left_ticks,json=frontLeftTicks,proto3" json:"front_left_ticks,omitempty"`
	FrontRightTicks int64           `protobuf:"varint,2,opt,name=front_right_ticks,json=frontRightTicks,proto3" json:"front_right_ticks,omitempty"`
	BackLeftTicks   int64           `protobuf:"varint,3,opt,name=back_left_ticks,json=backLeftTicks,proto3" json:"back_left_ticks,omitempty"`
	BackRightTicks  int64           `protobuf:"varint,4,opt,name=back_right_ticks,json=backRightTicks,proto3" json:"back_right_ticks,omitempty"`
	Timestamp       *time.Timestamp `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *RotaryEncoder_Reply) Reset() {
	*x = RotaryEncoder_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncoder_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncoder_Reply) ProtoMessage() {}

func (x *RotaryEncoder_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncoder_Reply.ProtoReflect.Descriptor instead.
func (*RotaryEncoder_Reply) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{3}
}

func (x *RotaryEncoder_Reply) GetFrontLeftTicks() int64 {
	if x != nil {
		return x.FrontLeftTicks
	}
	return 0
}

func (x *RotaryEncoder_Reply) GetFrontRightTicks() int64 {
	if x != nil {
		return x.FrontRightTicks
	}
	return 0
}

func (x *RotaryEncoder_Reply) GetBackLeftTicks() int64 {
	if x != nil {
		return x.BackLeftTicks
	}
	return 0
}

func (x *RotaryEncoder_Reply) GetBackRightTicks() int64 {
	if x != nil {
		return x.BackRightTicks
	}
	return 0
}

func (x *RotaryEncoder_Reply) GetTimestamp() *time.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

// Snapshot Request
type RotaryEncoderSnapshot_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	First *time.Timestamp `protobuf:"bytes,1,opt,name=first,proto3" json:"first,omitempty"`
	Last  *time.Timestamp `protobuf:"bytes,2,opt,name=last,proto3" json:"last,omitempty"`
}

func (x *RotaryEncoderSnapshot_Request) Reset() {
	*x = RotaryEncoderSnapshot_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncoderSnapshot_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncoderSnapshot_Request) ProtoMessage() {}

func (x *RotaryEncoderSnapshot_Request) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncoderSnapshot_Request.ProtoReflect.Descriptor instead.
func (*RotaryEncoderSnapshot_Request) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{4}
}

func (x *RotaryEncoderSnapshot_Request) GetFirst() *time.Timestamp {
	if x != nil {
		return x.First
	}
	return nil
}

func (x *RotaryEncoderSnapshot_Request) GetLast() *time.Timestamp {
	if x != nil {
		return x.Last
	}
	return nil
}

type RotaryEncoderSnapshot_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstBefore *RotaryEncoder_Reply           `protobuf:"bytes,1,opt,name=first_before,json=firstBefore,proto3" json:"first_before,omitempty"`
	FirstAfter  *RotaryEncoder_Reply           `protobuf:"bytes,2,opt,name=first_after,json=firstAfter,proto3" json:"first_after,omitempty"`
	LastBefore  *RotaryEncoder_Reply           `protobuf:"bytes,3,opt,name=last_before,json=lastBefore,proto3" json:"last_before,omitempty"`
	LastAfter   *RotaryEncoder_Reply           `protobuf:"bytes,4,opt,name=last_after,json=lastAfter,proto3" json:"last_after,omitempty"`
	Request     *RotaryEncoderSnapshot_Request `protobuf:"bytes,5,opt,name=request,proto3" json:"request,omitempty"`
}

func (x *RotaryEncoderSnapshot_Reply) Reset() {
	*x = RotaryEncoderSnapshot_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncoderSnapshot_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncoderSnapshot_Reply) ProtoMessage() {}

func (x *RotaryEncoderSnapshot_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncoderSnapshot_Reply.ProtoReflect.Descriptor instead.
func (*RotaryEncoderSnapshot_Reply) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{5}
}

func (x *RotaryEncoderSnapshot_Reply) GetFirstBefore() *RotaryEncoder_Reply {
	if x != nil {
		return x.FirstBefore
	}
	return nil
}

func (x *RotaryEncoderSnapshot_Reply) GetFirstAfter() *RotaryEncoder_Reply {
	if x != nil {
		return x.FirstAfter
	}
	return nil
}

func (x *RotaryEncoderSnapshot_Reply) GetLastBefore() *RotaryEncoder_Reply {
	if x != nil {
		return x.LastBefore
	}
	return nil
}

func (x *RotaryEncoderSnapshot_Reply) GetLastAfter() *RotaryEncoder_Reply {
	if x != nil {
		return x.LastAfter
	}
	return nil
}

func (x *RotaryEncoderSnapshot_Reply) GetRequest() *RotaryEncoderSnapshot_Request {
	if x != nil {
		return x.Request
	}
	return nil
}

type RotaryEncoderHistoryVerify_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RotaryEncoderHistoryVerify_Request) Reset() {
	*x = RotaryEncoderHistoryVerify_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncoderHistoryVerify_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncoderHistoryVerify_Request) ProtoMessage() {}

func (x *RotaryEncoderHistoryVerify_Request) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncoderHistoryVerify_Request.ProtoReflect.Descriptor instead.
func (*RotaryEncoderHistoryVerify_Request) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{6}
}

type RotaryEncoderHistoryVerify_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumTimeWarps    uint32 `protobuf:"varint,1,opt,name=num_time_warps,json=numTimeWarps,proto3" json:"num_time_warps,omitempty"`
	NumOooElements  uint32 `protobuf:"varint,2,opt,name=num_ooo_elements,json=numOooElements,proto3" json:"num_ooo_elements,omitempty"`
	MaxUsecDistance uint32 `protobuf:"varint,3,opt,name=max_usec_distance,json=maxUsecDistance,proto3" json:"max_usec_distance,omitempty"`
	NumEpochResets  uint32 `protobuf:"varint,4,opt,name=num_epoch_resets,json=numEpochResets,proto3" json:"num_epoch_resets,omitempty"`
	NumPlus1Usec    uint32 `protobuf:"varint,5,opt,name=num_plus1_usec,json=numPlus1Usec,proto3" json:"num_plus1_usec,omitempty"`
	NumMissedSignal uint32 `protobuf:"varint,6,opt,name=num_missed_signal,json=numMissedSignal,proto3" json:"num_missed_signal,omitempty"`
	NumRejectSignal uint32 `protobuf:"varint,7,opt,name=num_reject_signal,json=numRejectSignal,proto3" json:"num_reject_signal,omitempty"`
	NumShortTime    uint32 `protobuf:"varint,8,opt,name=num_short_time,json=numShortTime,proto3" json:"num_short_time,omitempty"`
	NumLongTime     uint32 `protobuf:"varint,9,opt,name=num_long_time,json=numLongTime,proto3" json:"num_long_time,omitempty"`
}

func (x *RotaryEncoderHistoryVerify_Reply) Reset() {
	*x = RotaryEncoderHistoryVerify_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RotaryEncoderHistoryVerify_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RotaryEncoderHistoryVerify_Reply) ProtoMessage() {}

func (x *RotaryEncoderHistoryVerify_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RotaryEncoderHistoryVerify_Reply.ProtoReflect.Descriptor instead.
func (*RotaryEncoderHistoryVerify_Reply) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{7}
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumTimeWarps() uint32 {
	if x != nil {
		return x.NumTimeWarps
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumOooElements() uint32 {
	if x != nil {
		return x.NumOooElements
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetMaxUsecDistance() uint32 {
	if x != nil {
		return x.MaxUsecDistance
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumEpochResets() uint32 {
	if x != nil {
		return x.NumEpochResets
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumPlus1Usec() uint32 {
	if x != nil {
		return x.NumPlus1Usec
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumMissedSignal() uint32 {
	if x != nil {
		return x.NumMissedSignal
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumRejectSignal() uint32 {
	if x != nil {
		return x.NumRejectSignal
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumShortTime() uint32 {
	if x != nil {
		return x.NumShortTime
	}
	return 0
}

func (x *RotaryEncoderHistoryVerify_Reply) GetNumLongTime() uint32 {
	if x != nil {
		return x.NumLongTime
	}
	return 0
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Rotary
	//	*Request_Config
	//	*Request_RotarySnapshot
	//	*Request_HistoryVerify
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{8}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetRotary() *RotaryEncoder_Request {
	if x, ok := x.GetRequest().(*Request_Rotary); ok {
		return x.Rotary
	}
	return nil
}

func (x *Request) GetConfig() *RotaryEncodersConfig_Request {
	if x, ok := x.GetRequest().(*Request_Config); ok {
		return x.Config
	}
	return nil
}

func (x *Request) GetRotarySnapshot() *RotaryEncoderSnapshot_Request {
	if x, ok := x.GetRequest().(*Request_RotarySnapshot); ok {
		return x.RotarySnapshot
	}
	return nil
}

func (x *Request) GetHistoryVerify() *RotaryEncoderHistoryVerify_Request {
	if x, ok := x.GetRequest().(*Request_HistoryVerify); ok {
		return x.HistoryVerify
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Rotary struct {
	Rotary *RotaryEncoder_Request `protobuf:"bytes,1,opt,name=rotary,proto3,oneof"`
}

type Request_Config struct {
	Config *RotaryEncodersConfig_Request `protobuf:"bytes,2,opt,name=config,proto3,oneof"`
}

type Request_RotarySnapshot struct {
	RotarySnapshot *RotaryEncoderSnapshot_Request `protobuf:"bytes,3,opt,name=rotary_snapshot,json=rotarySnapshot,proto3,oneof"`
}

type Request_HistoryVerify struct {
	HistoryVerify *RotaryEncoderHistoryVerify_Request `protobuf:"bytes,4,opt,name=history_verify,json=historyVerify,proto3,oneof"`
}

func (*Request_Rotary) isRequest_Request() {}

func (*Request_Config) isRequest_Request() {}

func (*Request_RotarySnapshot) isRequest_Request() {}

func (*Request_HistoryVerify) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Rotary
	//	*Reply_Config
	//	*Reply_RotarySnapshot
	//	*Reply_HistoryVerify
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rotary_encoder_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_rotary_encoder_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_rotary_encoder_proto_rawDescGZIP(), []int{9}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetRotary() *RotaryEncoder_Reply {
	if x, ok := x.GetReply().(*Reply_Rotary); ok {
		return x.Rotary
	}
	return nil
}

func (x *Reply) GetConfig() *RotaryEncodersConfig_Reply {
	if x, ok := x.GetReply().(*Reply_Config); ok {
		return x.Config
	}
	return nil
}

func (x *Reply) GetRotarySnapshot() *RotaryEncoderSnapshot_Reply {
	if x, ok := x.GetReply().(*Reply_RotarySnapshot); ok {
		return x.RotarySnapshot
	}
	return nil
}

func (x *Reply) GetHistoryVerify() *RotaryEncoderHistoryVerify_Reply {
	if x, ok := x.GetReply().(*Reply_HistoryVerify); ok {
		return x.HistoryVerify
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Rotary struct {
	Rotary *RotaryEncoder_Reply `protobuf:"bytes,1,opt,name=rotary,proto3,oneof"`
}

type Reply_Config struct {
	Config *RotaryEncodersConfig_Reply `protobuf:"bytes,2,opt,name=config,proto3,oneof"`
}

type Reply_RotarySnapshot struct {
	RotarySnapshot *RotaryEncoderSnapshot_Reply `protobuf:"bytes,3,opt,name=rotary_snapshot,json=rotarySnapshot,proto3,oneof"`
}

type Reply_HistoryVerify struct {
	HistoryVerify *RotaryEncoderHistoryVerify_Reply `protobuf:"bytes,4,opt,name=history_verify,json=historyVerify,proto3,oneof"`
}

func (*Reply_Rotary) isReply_Reply() {}

func (*Reply_Config) isReply_Reply() {}

func (*Reply_RotarySnapshot) isReply_Reply() {}

func (*Reply_HistoryVerify) isReply_Reply() {}

var File_rotary_encoder_proto protoreflect.FileDescriptor

var file_rotary_encoder_proto_rawDesc = []byte{
	0x0a, 0x14, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf4, 0x02, 0x0a, 0x1c, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x07, 0x46, 0x4c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x46, 0x4c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x46, 0x52, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x46, 0x52, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a,
	0x0a, 0x07, 0x42, 0x4c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x06, 0x42, 0x4c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x42, 0x52,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x72, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74,
	0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06,
	0x42, 0x52, 0x54, 0x79, 0x70, 0x65, 0x22, 0x24, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08,
	0x0a, 0x04, 0x54, 0x49, 0x43, 0x4b, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x51, 0x55, 0x41, 0x44,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x02, 0x22, 0x1c, 0x0a, 0x1a,
	0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x17, 0x0a, 0x15, 0x52, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0xec, 0x01, 0x0a, 0x13, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x4c, 0x65, 0x66, 0x74,
	0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x52, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x74,
	0x69, 0x63, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x62, 0x61, 0x63, 0x6b,
	0x4c, 0x65, 0x66, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x69, 0x67, 0x68, 0x74, 0x54, 0x69,
	0x63, 0x6b, 0x73, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x22, 0x6b, 0x0a, 0x1d, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f,
	0x64, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x05, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x05, 0x66, 0x69, 0x72, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x04, 0x6c, 0x61,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x6c, 0x61, 0x73, 0x74, 0x22,
	0xfe, 0x02, 0x0a, 0x1b, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x46, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72,
	0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x44, 0x0a,
	0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x09, 0x6c, 0x61,
	0x73, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72,
	0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x24, 0x0a, 0x22, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x90, 0x03, 0x0a, 0x20, 0x52, 0x6f, 0x74, 0x61, 0x72,
	0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x6e,
	0x75, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x77, 0x61, 0x72, 0x70, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x57, 0x61, 0x72, 0x70,
	0x73, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x6f, 0x6f, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6e, 0x75, 0x6d,
	0x4f, 0x6f, 0x6f, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6d,
	0x61, 0x78, 0x5f, 0x75, 0x73, 0x65, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x6d, 0x61, 0x78, 0x55, 0x73, 0x65, 0x63, 0x44,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x75, 0x6d, 0x5f, 0x65,
	0x70, 0x6f, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0e, 0x6e, 0x75, 0x6d, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x52, 0x65, 0x73, 0x65, 0x74,
	0x73, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x5f, 0x70, 0x6c, 0x75, 0x73, 0x31, 0x5f, 0x75,
	0x73, 0x65, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x50, 0x6c,
	0x75, 0x73, 0x31, 0x55, 0x73, 0x65, 0x63, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x75, 0x6d, 0x5f, 0x6d,
	0x69, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0f, 0x6e, 0x75, 0x6d, 0x4d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f,
	0x6e, 0x75, 0x6d, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x12,
	0x24, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x53, 0x68, 0x6f, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x6f, 0x6e,
	0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6e, 0x75,
	0x6d, 0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd4, 0x02, 0x0a, 0x07, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x06, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06,
	0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x12, 0x46, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58,
	0x0a, 0x0f, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x5b, 0x0a, 0x0e, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0xc8, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3d, 0x0a, 0x06, 0x72, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x6f, 0x74,
	0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61,
	0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x06, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x12, 0x44, 0x0a, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x6f, 0x74, 0x61,
	0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72,
	0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x56, 0x0a, 0x0f, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72,
	0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79,
	0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x59, 0x0a, 0x0e, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x17, 0x5a, 0x15, 0x6e,
	0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rotary_encoder_proto_rawDescOnce sync.Once
	file_rotary_encoder_proto_rawDescData = file_rotary_encoder_proto_rawDesc
)

func file_rotary_encoder_proto_rawDescGZIP() []byte {
	file_rotary_encoder_proto_rawDescOnce.Do(func() {
		file_rotary_encoder_proto_rawDescData = protoimpl.X.CompressGZIP(file_rotary_encoder_proto_rawDescData)
	})
	return file_rotary_encoder_proto_rawDescData
}

var file_rotary_encoder_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rotary_encoder_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_rotary_encoder_proto_goTypes = []interface{}{
	(RotaryEncodersConfig_Request_Type)(0),     // 0: rotary_encoder.RotaryEncodersConfig_Request.Type
	(*RotaryEncodersConfig_Request)(nil),       // 1: rotary_encoder.RotaryEncodersConfig_Request
	(*RotaryEncodersConfig_Reply)(nil),         // 2: rotary_encoder.RotaryEncodersConfig_Reply
	(*RotaryEncoder_Request)(nil),              // 3: rotary_encoder.RotaryEncoder_Request
	(*RotaryEncoder_Reply)(nil),                // 4: rotary_encoder.RotaryEncoder_Reply
	(*RotaryEncoderSnapshot_Request)(nil),      // 5: rotary_encoder.RotaryEncoderSnapshot_Request
	(*RotaryEncoderSnapshot_Reply)(nil),        // 6: rotary_encoder.RotaryEncoderSnapshot_Reply
	(*RotaryEncoderHistoryVerify_Request)(nil), // 7: rotary_encoder.RotaryEncoderHistoryVerify_Request
	(*RotaryEncoderHistoryVerify_Reply)(nil),   // 8: rotary_encoder.RotaryEncoderHistoryVerify_Reply
	(*Request)(nil),                            // 9: rotary_encoder.Request
	(*Reply)(nil),                              // 10: rotary_encoder.Reply
	(*time.Timestamp)(nil),                     // 11: time.Timestamp
}
var file_rotary_encoder_proto_depIdxs = []int32{
	0,  // 0: rotary_encoder.RotaryEncodersConfig_Request.FL_type:type_name -> rotary_encoder.RotaryEncodersConfig_Request.Type
	0,  // 1: rotary_encoder.RotaryEncodersConfig_Request.FR_type:type_name -> rotary_encoder.RotaryEncodersConfig_Request.Type
	0,  // 2: rotary_encoder.RotaryEncodersConfig_Request.BL_type:type_name -> rotary_encoder.RotaryEncodersConfig_Request.Type
	0,  // 3: rotary_encoder.RotaryEncodersConfig_Request.BR_type:type_name -> rotary_encoder.RotaryEncodersConfig_Request.Type
	11, // 4: rotary_encoder.RotaryEncoder_Reply.timestamp:type_name -> time.Timestamp
	11, // 5: rotary_encoder.RotaryEncoderSnapshot_Request.first:type_name -> time.Timestamp
	11, // 6: rotary_encoder.RotaryEncoderSnapshot_Request.last:type_name -> time.Timestamp
	4,  // 7: rotary_encoder.RotaryEncoderSnapshot_Reply.first_before:type_name -> rotary_encoder.RotaryEncoder_Reply
	4,  // 8: rotary_encoder.RotaryEncoderSnapshot_Reply.first_after:type_name -> rotary_encoder.RotaryEncoder_Reply
	4,  // 9: rotary_encoder.RotaryEncoderSnapshot_Reply.last_before:type_name -> rotary_encoder.RotaryEncoder_Reply
	4,  // 10: rotary_encoder.RotaryEncoderSnapshot_Reply.last_after:type_name -> rotary_encoder.RotaryEncoder_Reply
	5,  // 11: rotary_encoder.RotaryEncoderSnapshot_Reply.request:type_name -> rotary_encoder.RotaryEncoderSnapshot_Request
	3,  // 12: rotary_encoder.Request.rotary:type_name -> rotary_encoder.RotaryEncoder_Request
	1,  // 13: rotary_encoder.Request.config:type_name -> rotary_encoder.RotaryEncodersConfig_Request
	5,  // 14: rotary_encoder.Request.rotary_snapshot:type_name -> rotary_encoder.RotaryEncoderSnapshot_Request
	7,  // 15: rotary_encoder.Request.history_verify:type_name -> rotary_encoder.RotaryEncoderHistoryVerify_Request
	4,  // 16: rotary_encoder.Reply.rotary:type_name -> rotary_encoder.RotaryEncoder_Reply
	2,  // 17: rotary_encoder.Reply.config:type_name -> rotary_encoder.RotaryEncodersConfig_Reply
	6,  // 18: rotary_encoder.Reply.rotary_snapshot:type_name -> rotary_encoder.RotaryEncoderSnapshot_Reply
	8,  // 19: rotary_encoder.Reply.history_verify:type_name -> rotary_encoder.RotaryEncoderHistoryVerify_Reply
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_rotary_encoder_proto_init() }
func file_rotary_encoder_proto_init() {
	if File_rotary_encoder_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rotary_encoder_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncodersConfig_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncodersConfig_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncoder_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncoder_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncoderSnapshot_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncoderSnapshot_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncoderHistoryVerify_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RotaryEncoderHistoryVerify_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rotary_encoder_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_rotary_encoder_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*Request_Rotary)(nil),
		(*Request_Config)(nil),
		(*Request_RotarySnapshot)(nil),
		(*Request_HistoryVerify)(nil),
	}
	file_rotary_encoder_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*Reply_Rotary)(nil),
		(*Reply_Config)(nil),
		(*Reply_RotarySnapshot)(nil),
		(*Reply_HistoryVerify)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rotary_encoder_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rotary_encoder_proto_goTypes,
		DependencyIndexes: file_rotary_encoder_proto_depIdxs,
		EnumInfos:         file_rotary_encoder_proto_enumTypes,
		MessageInfos:      file_rotary_encoder_proto_msgTypes,
	}.Build()
	File_rotary_encoder_proto = out.File
	file_rotary_encoder_proto_rawDesc = nil
	file_rotary_encoder_proto_goTypes = nil
	file_rotary_encoder_proto_depIdxs = nil
}

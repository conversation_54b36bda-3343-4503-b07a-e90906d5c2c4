// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: row_module.proto

package row_module

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	dawg "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/dawg"
	scanner "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/scanner"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Reset_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Reset_Request) Reset() {
	*x = Reset_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reset_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reset_Request) ProtoMessage() {}

func (x *Reset_Request) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reset_Request.ProtoReflect.Descriptor instead.
func (*Reset_Request) Descriptor() ([]byte, []int) {
	return file_row_module_proto_rawDescGZIP(), []int{0}
}

type Clear_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Clear_Config_Request) Reset() {
	*x = Clear_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Clear_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Clear_Config_Request) ProtoMessage() {}

func (x *Clear_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Clear_Config_Request.ProtoReflect.Descriptor instead.
func (*Clear_Config_Request) Descriptor() ([]byte, []int) {
	return file_row_module_proto_rawDescGZIP(), []int{1}
}

type Scanner_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32           `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	Request   *scanner.Request `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
}

func (x *Scanner_Request) Reset() {
	*x = Scanner_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scanner_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scanner_Request) ProtoMessage() {}

func (x *Scanner_Request) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scanner_Request.ProtoReflect.Descriptor instead.
func (*Scanner_Request) Descriptor() ([]byte, []int) {
	return file_row_module_proto_rawDescGZIP(), []int{2}
}

func (x *Scanner_Request) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *Scanner_Request) GetRequest() *scanner.Request {
	if x != nil {
		return x.Request
	}
	return nil
}

type ACK_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ACK_Reply) Reset() {
	*x = ACK_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACK_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACK_Reply) ProtoMessage() {}

func (x *ACK_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACK_Reply.ProtoReflect.Descriptor instead.
func (*ACK_Reply) Descriptor() ([]byte, []int) {
	return file_row_module_proto_rawDescGZIP(), []int{3}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Reset_
	//	*Request_Clear
	//	*Request_Scanner
	//	*Request_Dawg
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_row_module_proto_rawDescGZIP(), []int{4}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetReset_() *Reset_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *Request) GetClear() *Clear_Config_Request {
	if x, ok := x.GetRequest().(*Request_Clear); ok {
		return x.Clear
	}
	return nil
}

func (x *Request) GetScanner() *Scanner_Request {
	if x, ok := x.GetRequest().(*Request_Scanner); ok {
		return x.Scanner
	}
	return nil
}

func (x *Request) GetDawg() *dawg.Request {
	if x, ok := x.GetRequest().(*Request_Dawg); ok {
		return x.Dawg
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Reset_ struct {
	Reset_ *Reset_Request `protobuf:"bytes,1,opt,name=reset,proto3,oneof"`
}

type Request_Clear struct {
	Clear *Clear_Config_Request `protobuf:"bytes,2,opt,name=clear,proto3,oneof"`
}

type Request_Scanner struct {
	Scanner *Scanner_Request `protobuf:"bytes,3,opt,name=scanner,proto3,oneof"`
}

type Request_Dawg struct {
	Dawg *dawg.Request `protobuf:"bytes,4,opt,name=dawg,proto3,oneof"`
}

func (*Request_Reset_) isRequest_Request() {}

func (*Request_Clear) isRequest_Request() {}

func (*Request_Scanner) isRequest_Request() {}

func (*Request_Dawg) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Ack
	//	*Reply_Scanner
	//	*Reply_Dawg
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_row_module_proto_rawDescGZIP(), []int{5}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetAck() *ACK_Reply {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetScanner() *scanner.Reply {
	if x, ok := x.GetReply().(*Reply_Scanner); ok {
		return x.Scanner
	}
	return nil
}

func (x *Reply) GetDawg() *dawg.Reply {
	if x, ok := x.GetReply().(*Reply_Dawg); ok {
		return x.Dawg
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Ack struct {
	Ack *ACK_Reply `protobuf:"bytes,1,opt,name=ack,proto3,oneof"`
}

type Reply_Scanner struct {
	Scanner *scanner.Reply `protobuf:"bytes,2,opt,name=scanner,proto3,oneof"`
}

type Reply_Dawg struct {
	Dawg *dawg.Reply `protobuf:"bytes,3,opt,name=dawg,proto3,oneof"`
}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Scanner) isReply_Reply() {}

func (*Reply_Dawg) isReply_Reply() {}

var File_row_module_proto protoreflect.FileDescriptor

var file_row_module_proto_rawDesc = []byte{
	0x0a, 0x10, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x1a, 0x30,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x0f, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x16, 0x0a, 0x14, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5c, 0x0a, 0x0f, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x0b, 0x0a, 0x09, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0xdf, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x31, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x12, 0x38, 0x0a, 0x05, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x43,
	0x6c, 0x65, 0x61, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x12, 0x37, 0x0a, 0x07,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x04, 0x64, 0x61, 0x77, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x77, 0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x8a, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x29, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72,
	0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x2a, 0x0a, 0x07, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x77, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x77, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x42, 0x13, 0x5a, 0x11, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x72, 0x6f, 0x77,
	0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_row_module_proto_rawDescOnce sync.Once
	file_row_module_proto_rawDescData = file_row_module_proto_rawDesc
)

func file_row_module_proto_rawDescGZIP() []byte {
	file_row_module_proto_rawDescOnce.Do(func() {
		file_row_module_proto_rawDescData = protoimpl.X.CompressGZIP(file_row_module_proto_rawDescData)
	})
	return file_row_module_proto_rawDescData
}

var file_row_module_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_row_module_proto_goTypes = []interface{}{
	(*Reset_Request)(nil),        // 0: row_module.Reset_Request
	(*Clear_Config_Request)(nil), // 1: row_module.Clear_Config_Request
	(*Scanner_Request)(nil),      // 2: row_module.Scanner_Request
	(*ACK_Reply)(nil),            // 3: row_module.ACK_Reply
	(*Request)(nil),              // 4: row_module.Request
	(*Reply)(nil),                // 5: row_module.Reply
	(*scanner.Request)(nil),      // 6: scanner.Request
	(*dawg.Request)(nil),         // 7: dawg.Request
	(*scanner.Reply)(nil),        // 8: scanner.Reply
	(*dawg.Reply)(nil),           // 9: dawg.Reply
}
var file_row_module_proto_depIdxs = []int32{
	6, // 0: row_module.Scanner_Request.request:type_name -> scanner.Request
	0, // 1: row_module.Request.reset:type_name -> row_module.Reset_Request
	1, // 2: row_module.Request.clear:type_name -> row_module.Clear_Config_Request
	2, // 3: row_module.Request.scanner:type_name -> row_module.Scanner_Request
	7, // 4: row_module.Request.dawg:type_name -> dawg.Request
	3, // 5: row_module.Reply.ack:type_name -> row_module.ACK_Reply
	8, // 6: row_module.Reply.scanner:type_name -> scanner.Reply
	9, // 7: row_module.Reply.dawg:type_name -> dawg.Reply
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_row_module_proto_init() }
func file_row_module_proto_init() {
	if File_row_module_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_row_module_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reset_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_row_module_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Clear_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_row_module_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scanner_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_row_module_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACK_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_row_module_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_row_module_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_row_module_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*Request_Reset_)(nil),
		(*Request_Clear)(nil),
		(*Request_Scanner)(nil),
		(*Request_Dawg)(nil),
	}
	file_row_module_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Reply_Ack)(nil),
		(*Reply_Scanner)(nil),
		(*Reply_Dawg)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_row_module_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_row_module_proto_goTypes,
		DependencyIndexes: file_row_module_proto_depIdxs,
		MessageInfos:      file_row_module_proto_msgTypes,
	}.Build()
	File_row_module_proto = out.File
	file_row_module_proto_rawDesc = nil
	file_row_module_proto_goTypes = nil
	file_row_module_proto_depIdxs = nil
}

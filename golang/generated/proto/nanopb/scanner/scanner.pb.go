// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: scanner.proto

package scanner

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	epos "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/epos"
	servo "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/servo"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Laser_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	On bool `protobuf:"varint,1,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *Laser_Request) Reset() {
	*x = Laser_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_Request) ProtoMessage() {}

func (x *Laser_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_Request.ProtoReflect.Descriptor instead.
func (*Laser_Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{0}
}

func (x *Laser_Request) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

type Get_Laser_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Laser_Request) Reset() {
	*x = Get_Laser_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Laser_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Laser_Request) ProtoMessage() {}

func (x *Get_Laser_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Laser_Request.ProtoReflect.Descriptor instead.
func (*Get_Laser_Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{1}
}

type Intensity_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intensity int32 `protobuf:"varint,1,opt,name=intensity,proto3" json:"intensity,omitempty"` // uint16_t
}

func (x *Intensity_Request) Reset() {
	*x = Intensity_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Intensity_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Intensity_Request) ProtoMessage() {}

func (x *Intensity_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Intensity_Request.ProtoReflect.Descriptor instead.
func (*Intensity_Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{2}
}

func (x *Intensity_Request) GetIntensity() int32 {
	if x != nil {
		return x.Intensity
	}
	return 0
}

type Boot_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PanParams  *epos.Home_Params `protobuf:"bytes,1,opt,name=pan_params,json=panParams,proto3" json:"pan_params,omitempty"`
	TiltParams *epos.Home_Params `protobuf:"bytes,2,opt,name=tilt_params,json=tiltParams,proto3" json:"tilt_params,omitempty"`
}

func (x *Boot_Request) Reset() {
	*x = Boot_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Boot_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Boot_Request) ProtoMessage() {}

func (x *Boot_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Boot_Request.ProtoReflect.Descriptor instead.
func (*Boot_Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{3}
}

func (x *Boot_Request) GetPanParams() *epos.Home_Params {
	if x != nil {
		return x.PanParams
	}
	return nil
}

func (x *Boot_Request) GetTiltParams() *epos.Home_Params {
	if x != nil {
		return x.TiltParams
	}
	return nil
}

type Stop_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Stop_Request) Reset() {
	*x = Stop_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Stop_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stop_Request) ProtoMessage() {}

func (x *Stop_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stop_Request.ProtoReflect.Descriptor instead.
func (*Stop_Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{4}
}

type Gimbal_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan  *servo.Request `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	Tilt *servo.Request `protobuf:"bytes,2,opt,name=tilt,proto3" json:"tilt,omitempty"`
}

func (x *Gimbal_Request) Reset() {
	*x = Gimbal_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gimbal_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gimbal_Request) ProtoMessage() {}

func (x *Gimbal_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gimbal_Request.ProtoReflect.Descriptor instead.
func (*Gimbal_Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{5}
}

func (x *Gimbal_Request) GetPan() *servo.Request {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *Gimbal_Request) GetTilt() *servo.Request {
	if x != nil {
		return x.Tilt
	}
	return nil
}

type Error_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Error_Reply) Reset() {
	*x = Error_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Error_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error_Reply) ProtoMessage() {}

func (x *Error_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error_Reply.ProtoReflect.Descriptor instead.
func (*Error_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{6}
}

type ACK_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ACK_Reply) Reset() {
	*x = ACK_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACK_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACK_Reply) ProtoMessage() {}

func (x *ACK_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACK_Reply.ProtoReflect.Descriptor instead.
func (*ACK_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{7}
}

type Laser_State_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	On bool `protobuf:"varint,1,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *Laser_State_Reply) Reset() {
	*x = Laser_State_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser_State_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser_State_Reply) ProtoMessage() {}

func (x *Laser_State_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser_State_Reply.ProtoReflect.Descriptor instead.
func (*Laser_State_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{8}
}

func (x *Laser_State_Reply) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

type Gimbal_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan  *servo.Reply `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	Tilt *servo.Reply `protobuf:"bytes,2,opt,name=tilt,proto3" json:"tilt,omitempty"`
}

func (x *Gimbal_Reply) Reset() {
	*x = Gimbal_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gimbal_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gimbal_Reply) ProtoMessage() {}

func (x *Gimbal_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gimbal_Reply.ProtoReflect.Descriptor instead.
func (*Gimbal_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{9}
}

func (x *Gimbal_Reply) GetPan() *servo.Reply {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *Gimbal_Reply) GetTilt() *servo.Reply {
	if x != nil {
		return x.Tilt
	}
	return nil
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Laser
	//	*Request_GetLaser_
	//	*Request_Boot
	//	*Request_Stop
	//	*Request_Gimbal
	//	*Request_Intensity
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{10}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetLaser() *Laser_Request {
	if x, ok := x.GetRequest().(*Request_Laser); ok {
		return x.Laser
	}
	return nil
}

func (x *Request) GetGetLaser_() *Get_Laser_Request {
	if x, ok := x.GetRequest().(*Request_GetLaser_); ok {
		return x.GetLaser_
	}
	return nil
}

func (x *Request) GetBoot() *Boot_Request {
	if x, ok := x.GetRequest().(*Request_Boot); ok {
		return x.Boot
	}
	return nil
}

func (x *Request) GetStop() *Stop_Request {
	if x, ok := x.GetRequest().(*Request_Stop); ok {
		return x.Stop
	}
	return nil
}

func (x *Request) GetGimbal() *Gimbal_Request {
	if x, ok := x.GetRequest().(*Request_Gimbal); ok {
		return x.Gimbal
	}
	return nil
}

func (x *Request) GetIntensity() *Intensity_Request {
	if x, ok := x.GetRequest().(*Request_Intensity); ok {
		return x.Intensity
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Laser struct {
	Laser *Laser_Request `protobuf:"bytes,1,opt,name=laser,proto3,oneof"`
}

type Request_GetLaser_ struct {
	GetLaser_ *Get_Laser_Request `protobuf:"bytes,2,opt,name=get_laser,json=getLaser,proto3,oneof"`
}

type Request_Boot struct {
	Boot *Boot_Request `protobuf:"bytes,3,opt,name=boot,proto3,oneof"`
}

type Request_Stop struct {
	Stop *Stop_Request `protobuf:"bytes,4,opt,name=stop,proto3,oneof"`
}

type Request_Gimbal struct {
	Gimbal *Gimbal_Request `protobuf:"bytes,5,opt,name=gimbal,proto3,oneof"`
}

type Request_Intensity struct {
	Intensity *Intensity_Request `protobuf:"bytes,6,opt,name=intensity,proto3,oneof"`
}

func (*Request_Laser) isRequest_Request() {}

func (*Request_GetLaser_) isRequest_Request() {}

func (*Request_Boot) isRequest_Request() {}

func (*Request_Stop) isRequest_Request() {}

func (*Request_Gimbal) isRequest_Request() {}

func (*Request_Intensity) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Laser
	//	*Reply_Gimbal
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_scanner_proto_rawDescGZIP(), []int{11}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *Error_Reply {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ACK_Reply {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetLaser() *Laser_State_Reply {
	if x, ok := x.GetReply().(*Reply_Laser); ok {
		return x.Laser
	}
	return nil
}

func (x *Reply) GetGimbal() *Gimbal_Reply {
	if x, ok := x.GetReply().(*Reply_Gimbal); ok {
		return x.Gimbal
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *Error_Reply `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ACK_Reply `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Laser struct {
	Laser *Laser_State_Reply `protobuf:"bytes,3,opt,name=laser,proto3,oneof"`
}

type Reply_Gimbal struct {
	Gimbal *Gimbal_Reply `protobuf:"bytes,4,opt,name=gimbal,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Laser) isReply_Reply() {}

func (*Reply_Gimbal) isReply_Reply() {}

var File_scanner_proto protoreflect.FileDescriptor

var file_scanner_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x70, 0x6f,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1f, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6e, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x5f,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a,
	0x11, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79,
	0x22, 0x74, 0x0a, 0x0c, 0x42, 0x6f, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x65,
	0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48,
	0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0a, 0x74, 0x69, 0x6c, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x0e, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x70, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x56, 0x0a, 0x0e, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x69,
	0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x74, 0x69, 0x6c, 0x74, 0x22, 0x0d,
	0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x0b, 0x0a,
	0x09, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x0a, 0x11, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6e, 0x22,
	0x50, 0x0a, 0x0c, 0x47, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x1e, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12,
	0x20, 0x0a, 0x04, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x04, 0x74, 0x69, 0x6c,
	0x74, 0x22, 0xc8, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x39, 0x0a,
	0x09, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08,
	0x67, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x42, 0x6f, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x04, 0x62, 0x6f, 0x6f, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x6f, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x73, 0x74,
	0x6f, 0x70, 0x12, 0x31, 0x0a, 0x06, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x47, 0x69, 0x6d,
	0x62, 0x61, 0x6c, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x12, 0x3a, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x79, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xcb, 0x01, 0x0a,
	0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x41, 0x43, 0x4b, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x32, 0x0a, 0x05,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x12, 0x2f, 0x0a, 0x06, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x47, 0x69, 0x6d, 0x62, 0x61,
	0x6c, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x67, 0x69, 0x6d, 0x62, 0x61,
	0x6c, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_scanner_proto_rawDescOnce sync.Once
	file_scanner_proto_rawDescData = file_scanner_proto_rawDesc
)

func file_scanner_proto_rawDescGZIP() []byte {
	file_scanner_proto_rawDescOnce.Do(func() {
		file_scanner_proto_rawDescData = protoimpl.X.CompressGZIP(file_scanner_proto_rawDescData)
	})
	return file_scanner_proto_rawDescData
}

var file_scanner_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_scanner_proto_goTypes = []interface{}{
	(*Laser_Request)(nil),     // 0: scanner.Laser_Request
	(*Get_Laser_Request)(nil), // 1: scanner.Get_Laser_Request
	(*Intensity_Request)(nil), // 2: scanner.Intensity_Request
	(*Boot_Request)(nil),      // 3: scanner.Boot_Request
	(*Stop_Request)(nil),      // 4: scanner.Stop_Request
	(*Gimbal_Request)(nil),    // 5: scanner.Gimbal_Request
	(*Error_Reply)(nil),       // 6: scanner.Error_Reply
	(*ACK_Reply)(nil),         // 7: scanner.ACK_Reply
	(*Laser_State_Reply)(nil), // 8: scanner.Laser_State_Reply
	(*Gimbal_Reply)(nil),      // 9: scanner.Gimbal_Reply
	(*Request)(nil),           // 10: scanner.Request
	(*Reply)(nil),             // 11: scanner.Reply
	(*epos.Home_Params)(nil),  // 12: epos.Home_Params
	(*servo.Request)(nil),     // 13: servo.Request
	(*servo.Reply)(nil),       // 14: servo.Reply
}
var file_scanner_proto_depIdxs = []int32{
	12, // 0: scanner.Boot_Request.pan_params:type_name -> epos.Home_Params
	12, // 1: scanner.Boot_Request.tilt_params:type_name -> epos.Home_Params
	13, // 2: scanner.Gimbal_Request.pan:type_name -> servo.Request
	13, // 3: scanner.Gimbal_Request.tilt:type_name -> servo.Request
	14, // 4: scanner.Gimbal_Reply.pan:type_name -> servo.Reply
	14, // 5: scanner.Gimbal_Reply.tilt:type_name -> servo.Reply
	0,  // 6: scanner.Request.laser:type_name -> scanner.Laser_Request
	1,  // 7: scanner.Request.get_laser:type_name -> scanner.Get_Laser_Request
	3,  // 8: scanner.Request.boot:type_name -> scanner.Boot_Request
	4,  // 9: scanner.Request.stop:type_name -> scanner.Stop_Request
	5,  // 10: scanner.Request.gimbal:type_name -> scanner.Gimbal_Request
	2,  // 11: scanner.Request.intensity:type_name -> scanner.Intensity_Request
	6,  // 12: scanner.Reply.error:type_name -> scanner.Error_Reply
	7,  // 13: scanner.Reply.ack:type_name -> scanner.ACK_Reply
	8,  // 14: scanner.Reply.laser:type_name -> scanner.Laser_State_Reply
	9,  // 15: scanner.Reply.gimbal:type_name -> scanner.Gimbal_Reply
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_scanner_proto_init() }
func file_scanner_proto_init() {
	if File_scanner_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_scanner_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Laser_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Intensity_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Boot_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Stop_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gimbal_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Error_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACK_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser_State_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gimbal_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_scanner_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*Request_Laser)(nil),
		(*Request_GetLaser_)(nil),
		(*Request_Boot)(nil),
		(*Request_Stop)(nil),
		(*Request_Gimbal)(nil),
		(*Request_Intensity)(nil),
	}
	file_scanner_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Laser)(nil),
		(*Reply_Gimbal)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_scanner_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_scanner_proto_goTypes,
		DependencyIndexes: file_scanner_proto_depIdxs,
		MessageInfos:      file_scanner_proto_msgTypes,
	}.Build()
	File_scanner_proto = out.File
	file_scanner_proto_rawDesc = nil
	file_scanner_proto_goTypes = nil
	file_scanner_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: heading.proto

package heading

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_heading_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_heading_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_heading_proto_rawDescGZIP(), []int{0}
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HaveFix     bool    `protobuf:"varint,1,opt,name=have_fix,json=haveFix,proto3" json:"have_fix,omitempty"`
	HeadingDeg  float64 `protobuf:"fixed64,2,opt,name=heading_deg,json=headingDeg,proto3" json:"heading_deg,omitempty"`
	AccuracyDeg float64 `protobuf:"fixed64,3,opt,name=accuracy_deg,json=accuracyDeg,proto3" json:"accuracy_deg,omitempty"`
	// Only valid if fix is present
	TimestampMs   uint64 `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	HaveApproxFix bool   `protobuf:"varint,5,opt,name=have_approx_fix,json=haveApproxFix,proto3" json:"have_approx_fix,omitempty"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_heading_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_heading_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_heading_proto_rawDescGZIP(), []int{1}
}

func (x *Reply) GetHaveFix() bool {
	if x != nil {
		return x.HaveFix
	}
	return false
}

func (x *Reply) GetHeadingDeg() float64 {
	if x != nil {
		return x.HeadingDeg
	}
	return 0
}

func (x *Reply) GetAccuracyDeg() float64 {
	if x != nil {
		return x.AccuracyDeg
	}
	return 0
}

func (x *Reply) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *Reply) GetHaveApproxFix() bool {
	if x != nil {
		return x.HaveApproxFix
	}
	return false
}

var File_heading_proto protoreflect.FileDescriptor

var file_heading_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x09, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x19, 0x0a,
	0x08, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x68, 0x61, 0x76, 0x65, 0x46, 0x69, 0x78, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x68,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x75, 0x72, 0x61, 0x63, 0x79, 0x5f, 0x64, 0x65, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x44, 0x65, 0x67, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x5f, 0x66,
	0x69, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x76, 0x65, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x78, 0x46, 0x69, 0x78, 0x42, 0x10, 0x5a, 0x0e, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_heading_proto_rawDescOnce sync.Once
	file_heading_proto_rawDescData = file_heading_proto_rawDesc
)

func file_heading_proto_rawDescGZIP() []byte {
	file_heading_proto_rawDescOnce.Do(func() {
		file_heading_proto_rawDescData = protoimpl.X.CompressGZIP(file_heading_proto_rawDescData)
	})
	return file_heading_proto_rawDescData
}

var file_heading_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_heading_proto_goTypes = []interface{}{
	(*Request)(nil), // 0: heading.Request
	(*Reply)(nil),   // 1: heading.Reply
}
var file_heading_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_heading_proto_init() }
func file_heading_proto_init() {
	if File_heading_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_heading_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_heading_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_heading_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_heading_proto_goTypes,
		DependencyIndexes: file_heading_proto_depIdxs,
		MessageInfos:      file_heading_proto_msgTypes,
	}.Build()
	File_heading_proto = out.File
	file_heading_proto_rawDesc = nil
	file_heading_proto_goTypes = nil
	file_heading_proto_depIdxs = nil
}

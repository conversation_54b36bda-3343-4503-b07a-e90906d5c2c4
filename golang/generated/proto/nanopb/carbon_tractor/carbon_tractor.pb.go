// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: carbon_tractor.proto

package carbon_tractor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ots_tractor "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ots_tractor"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HHStateStatus int32

const (
	HHStateStatus_HH_UNKNOWN     HHStateStatus = 0
	HHStateStatus_HH_DISABLED    HHStateStatus = 1
	HHStateStatus_HH_OPERATIONAL HHStateStatus = 2
	HHStateStatus_HH_STOPPED     HHStateStatus = 3
	HHStateStatus_HH_SAFE        HHStateStatus = 4
	HHStateStatus_HH_ESTOP       HHStateStatus = 5
)

// Enum value maps for HHStateStatus.
var (
	HHStateStatus_name = map[int32]string{
		0: "HH_UNKNOWN",
		1: "HH_DISABLED",
		2: "HH_OPERATIONAL",
		3: "HH_STOPPED",
		4: "HH_SAFE",
		5: "HH_ESTOP",
	}
	HHStateStatus_value = map[string]int32{
		"HH_UNKNOWN":     0,
		"HH_DISABLED":    1,
		"HH_OPERATIONAL": 2,
		"HH_STOPPED":     3,
		"HH_SAFE":        4,
		"HH_ESTOP":       5,
	}
)

func (x HHStateStatus) Enum() *HHStateStatus {
	p := new(HHStateStatus)
	*p = x
	return p
}

func (x HHStateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HHStateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_carbon_tractor_proto_enumTypes[0].Descriptor()
}

func (HHStateStatus) Type() protoreflect.EnumType {
	return &file_carbon_tractor_proto_enumTypes[0]
}

func (x HHStateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HHStateStatus.Descriptor instead.
func (HHStateStatus) EnumDescriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{0}
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{0}
}

type HHState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State HHStateStatus `protobuf:"varint,1,opt,name=state,proto3,enum=carbon_tractor.HHStateStatus" json:"state,omitempty"`
}

func (x *HHState) Reset() {
	*x = HHState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HHState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HHState) ProtoMessage() {}

func (x *HHState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HHState.ProtoReflect.Descriptor instead.
func (*HHState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{1}
}

func (x *HHState) GetState() HHStateStatus {
	if x != nil {
		return x.State
	}
	return HHStateStatus_HH_UNKNOWN
}

type BrakeState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ForceLeft  int32 `protobuf:"varint,1,opt,name=force_left,json=forceLeft,proto3" json:"force_left,omitempty"`    // 0-100 force
	ForceRight int32 `protobuf:"varint,2,opt,name=force_right,json=forceRight,proto3" json:"force_right,omitempty"` // 0-100 force
}

func (x *BrakeState) Reset() {
	*x = BrakeState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrakeState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrakeState) ProtoMessage() {}

func (x *BrakeState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrakeState.ProtoReflect.Descriptor instead.
func (*BrakeState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{2}
}

func (x *BrakeState) GetForceLeft() int32 {
	if x != nil {
		return x.ForceLeft
	}
	return 0
}

func (x *BrakeState) GetForceRight() int32 {
	if x != nil {
		return x.ForceRight
	}
	return 0
}

type SafetySensorsState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggeredSensor_1 bool `protobuf:"varint,1,opt,name=triggered_sensor_1,json=triggeredSensor1,proto3" json:"triggered_sensor_1,omitempty"`
	TriggeredSensor_2 bool `protobuf:"varint,2,opt,name=triggered_sensor_2,json=triggeredSensor2,proto3" json:"triggered_sensor_2,omitempty"`
	TriggeredSensor_3 bool `protobuf:"varint,3,opt,name=triggered_sensor_3,json=triggeredSensor3,proto3" json:"triggered_sensor_3,omitempty"`
	TriggeredSensor_4 bool `protobuf:"varint,4,opt,name=triggered_sensor_4,json=triggeredSensor4,proto3" json:"triggered_sensor_4,omitempty"`
}

func (x *SafetySensorsState) Reset() {
	*x = SafetySensorsState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafetySensorsState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafetySensorsState) ProtoMessage() {}

func (x *SafetySensorsState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafetySensorsState.ProtoReflect.Descriptor instead.
func (*SafetySensorsState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{3}
}

func (x *SafetySensorsState) GetTriggeredSensor_1() bool {
	if x != nil {
		return x.TriggeredSensor_1
	}
	return false
}

func (x *SafetySensorsState) GetTriggeredSensor_2() bool {
	if x != nil {
		return x.TriggeredSensor_2
	}
	return false
}

func (x *SafetySensorsState) GetTriggeredSensor_3() bool {
	if x != nil {
		return x.TriggeredSensor_3
	}
	return false
}

func (x *SafetySensorsState) GetTriggeredSensor_4() bool {
	if x != nil {
		return x.TriggeredSensor_4
	}
	return false
}

type SafetySensorBypassState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bypass bool `protobuf:"varint,1,opt,name=bypass,proto3" json:"bypass,omitempty"`
}

func (x *SafetySensorBypassState) Reset() {
	*x = SafetySensorBypassState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafetySensorBypassState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafetySensorBypassState) ProtoMessage() {}

func (x *SafetySensorBypassState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafetySensorBypassState.ProtoReflect.Descriptor instead.
func (*SafetySensorBypassState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{4}
}

func (x *SafetySensorBypassState) GetBypass() bool {
	if x != nil {
		return x.Bypass
	}
	return false
}

type TractorStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State               *HHState               `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	ErrorFlag           int32                  `protobuf:"varint,2,opt,name=error_flag,json=errorFlag,proto3" json:"error_flag,omitempty"` // bit field for errors
	GroundSpeed         float32                `protobuf:"fixed32,3,opt,name=ground_speed,json=groundSpeed,proto3" json:"ground_speed,omitempty"`
	WheelAngle          float32                `protobuf:"fixed32,4,opt,name=wheel_angle,json=wheelAngle,proto3" json:"wheel_angle,omitempty"`
	HitchLiftPercentage float32                `protobuf:"fixed32,5,opt,name=hitch_lift_percentage,json=hitchLiftPercentage,proto3" json:"hitch_lift_percentage,omitempty"`
	Gear                *ots_tractor.GearState `protobuf:"bytes,6,opt,name=gear,proto3" json:"gear,omitempty"` // Cannot direct import enum in nanopb
	SafetyTriggered     bool                   `protobuf:"varint,7,opt,name=safety_triggered,json=safetyTriggered,proto3" json:"safety_triggered,omitempty"`
	SafetyBypass        bool                   `protobuf:"varint,8,opt,name=safety_bypass,json=safetyBypass,proto3" json:"safety_bypass,omitempty"`
	RemoteLockout       bool                   `protobuf:"varint,9,opt,name=remote_lockout,json=remoteLockout,proto3" json:"remote_lockout,omitempty"`
	Rpms                int32                  `protobuf:"varint,10,opt,name=rpms,proto3" json:"rpms,omitempty"`
}

func (x *TractorStatus) Reset() {
	*x = TractorStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorStatus) ProtoMessage() {}

func (x *TractorStatus) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorStatus.ProtoReflect.Descriptor instead.
func (*TractorStatus) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{5}
}

func (x *TractorStatus) GetState() *HHState {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *TractorStatus) GetErrorFlag() int32 {
	if x != nil {
		return x.ErrorFlag
	}
	return 0
}

func (x *TractorStatus) GetGroundSpeed() float32 {
	if x != nil {
		return x.GroundSpeed
	}
	return 0
}

func (x *TractorStatus) GetWheelAngle() float32 {
	if x != nil {
		return x.WheelAngle
	}
	return 0
}

func (x *TractorStatus) GetHitchLiftPercentage() float32 {
	if x != nil {
		return x.HitchLiftPercentage
	}
	return 0
}

func (x *TractorStatus) GetGear() *ots_tractor.GearState {
	if x != nil {
		return x.Gear
	}
	return nil
}

func (x *TractorStatus) GetSafetyTriggered() bool {
	if x != nil {
		return x.SafetyTriggered
	}
	return false
}

func (x *TractorStatus) GetSafetyBypass() bool {
	if x != nil {
		return x.SafetyBypass
	}
	return false
}

func (x *TractorStatus) GetRemoteLockout() bool {
	if x != nil {
		return x.RemoteLockout
	}
	return false
}

func (x *TractorStatus) GetRpms() int32 {
	if x != nil {
		return x.Rpms
	}
	return 0
}

type PetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PetRequest) Reset() {
	*x = PetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRequest) ProtoMessage() {}

func (x *PetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRequest.ProtoReflect.Descriptor instead.
func (*PetRequest) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{6}
}

type SteeringState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Angle float32 `protobuf:"fixed32,1,opt,name=angle,proto3" json:"angle,omitempty"`
}

func (x *SteeringState) Reset() {
	*x = SteeringState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SteeringState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SteeringState) ProtoMessage() {}

func (x *SteeringState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SteeringState.ProtoReflect.Descriptor instead.
func (*SteeringState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{7}
}

func (x *SteeringState) GetAngle() float32 {
	if x != nil {
		return x.Angle
	}
	return 0
}

type SteeringCfgState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kp                      float32 `protobuf:"fixed32,1,opt,name=kp,proto3" json:"kp,omitempty"`
	Ki                      float32 `protobuf:"fixed32,2,opt,name=ki,proto3" json:"ki,omitempty"`
	Kd                      float32 `protobuf:"fixed32,3,opt,name=kd,proto3" json:"kd,omitempty"`
	IntegralLimit           float32 `protobuf:"fixed32,4,opt,name=integral_limit,json=integralLimit,proto3" json:"integral_limit,omitempty"`
	UpdateRateHz            int32   `protobuf:"varint,5,opt,name=update_rate_hz,json=updateRateHz,proto3" json:"update_rate_hz,omitempty"`
	MinSteeringValveCurrent uint32  `protobuf:"varint,6,opt,name=min_steering_valve_current,json=minSteeringValveCurrent,proto3" json:"min_steering_valve_current,omitempty"`
	MaxSteeringValveCurrent uint32  `protobuf:"varint,7,opt,name=max_steering_valve_current,json=maxSteeringValveCurrent,proto3" json:"max_steering_valve_current,omitempty"`
}

func (x *SteeringCfgState) Reset() {
	*x = SteeringCfgState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SteeringCfgState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SteeringCfgState) ProtoMessage() {}

func (x *SteeringCfgState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SteeringCfgState.ProtoReflect.Descriptor instead.
func (*SteeringCfgState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{8}
}

func (x *SteeringCfgState) GetKp() float32 {
	if x != nil {
		return x.Kp
	}
	return 0
}

func (x *SteeringCfgState) GetKi() float32 {
	if x != nil {
		return x.Ki
	}
	return 0
}

func (x *SteeringCfgState) GetKd() float32 {
	if x != nil {
		return x.Kd
	}
	return 0
}

func (x *SteeringCfgState) GetIntegralLimit() float32 {
	if x != nil {
		return x.IntegralLimit
	}
	return 0
}

func (x *SteeringCfgState) GetUpdateRateHz() int32 {
	if x != nil {
		return x.UpdateRateHz
	}
	return 0
}

func (x *SteeringCfgState) GetMinSteeringValveCurrent() uint32 {
	if x != nil {
		return x.MinSteeringValveCurrent
	}
	return 0
}

func (x *SteeringCfgState) GetMaxSteeringValveCurrent() uint32 {
	if x != nil {
		return x.MaxSteeringValveCurrent
	}
	return 0
}

type PetLossState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseStop bool `protobuf:"varint,1,opt,name=use_stop,json=useStop,proto3" json:"use_stop,omitempty"`
}

func (x *PetLossState) Reset() {
	*x = PetLossState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetLossState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetLossState) ProtoMessage() {}

func (x *PetLossState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetLossState.ProtoReflect.Descriptor instead.
func (*PetLossState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{9}
}

func (x *PetLossState) GetUseStop() bool {
	if x != nil {
		return x.UseStop
	}
	return false
}

type SpeedLimitState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpeedLimitMph float32 `protobuf:"fixed32,1,opt,name=speed_limit_mph,json=speedLimitMph,proto3" json:"speed_limit_mph,omitempty"` // <0 is unbound otherwise max speed set here (note  0 is a valid max speed and you won't be able to move)
}

func (x *SpeedLimitState) Reset() {
	*x = SpeedLimitState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeedLimitState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedLimitState) ProtoMessage() {}

func (x *SpeedLimitState) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedLimitState.ProtoReflect.Descriptor instead.
func (*SpeedLimitState) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{10}
}

func (x *SpeedLimitState) GetSpeedLimitMph() float32 {
	if x != nil {
		return x.SpeedLimitMph
	}
	return 0
}

type SetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Set:
	//
	//	*SetRequest_HhState
	//	*SetRequest_Brakes
	//	*SetRequest_SafetyBypass
	//	*SetRequest_Steering
	//	*SetRequest_SteeringCfg
	//	*SetRequest_PetLoss
	//	*SetRequest_SpeedLimit
	Set isSetRequest_Set `protobuf_oneof:"set"`
}

func (x *SetRequest) Reset() {
	*x = SetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRequest) ProtoMessage() {}

func (x *SetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRequest.ProtoReflect.Descriptor instead.
func (*SetRequest) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{11}
}

func (m *SetRequest) GetSet() isSetRequest_Set {
	if m != nil {
		return m.Set
	}
	return nil
}

func (x *SetRequest) GetHhState() *HHState {
	if x, ok := x.GetSet().(*SetRequest_HhState); ok {
		return x.HhState
	}
	return nil
}

func (x *SetRequest) GetBrakes() *BrakeState {
	if x, ok := x.GetSet().(*SetRequest_Brakes); ok {
		return x.Brakes
	}
	return nil
}

func (x *SetRequest) GetSafetyBypass() *SafetySensorBypassState {
	if x, ok := x.GetSet().(*SetRequest_SafetyBypass); ok {
		return x.SafetyBypass
	}
	return nil
}

func (x *SetRequest) GetSteering() *SteeringState {
	if x, ok := x.GetSet().(*SetRequest_Steering); ok {
		return x.Steering
	}
	return nil
}

func (x *SetRequest) GetSteeringCfg() *SteeringCfgState {
	if x, ok := x.GetSet().(*SetRequest_SteeringCfg); ok {
		return x.SteeringCfg
	}
	return nil
}

func (x *SetRequest) GetPetLoss() *PetLossState {
	if x, ok := x.GetSet().(*SetRequest_PetLoss); ok {
		return x.PetLoss
	}
	return nil
}

func (x *SetRequest) GetSpeedLimit() *SpeedLimitState {
	if x, ok := x.GetSet().(*SetRequest_SpeedLimit); ok {
		return x.SpeedLimit
	}
	return nil
}

type isSetRequest_Set interface {
	isSetRequest_Set()
}

type SetRequest_HhState struct {
	HhState *HHState `protobuf:"bytes,1,opt,name=hh_state,json=hhState,proto3,oneof"`
}

type SetRequest_Brakes struct {
	Brakes *BrakeState `protobuf:"bytes,2,opt,name=brakes,proto3,oneof"`
}

type SetRequest_SafetyBypass struct {
	SafetyBypass *SafetySensorBypassState `protobuf:"bytes,3,opt,name=safety_bypass,json=safetyBypass,proto3,oneof"`
}

type SetRequest_Steering struct {
	Steering *SteeringState `protobuf:"bytes,4,opt,name=steering,proto3,oneof"`
}

type SetRequest_SteeringCfg struct {
	SteeringCfg *SteeringCfgState `protobuf:"bytes,5,opt,name=steering_cfg,json=steeringCfg,proto3,oneof"`
}

type SetRequest_PetLoss struct {
	PetLoss *PetLossState `protobuf:"bytes,6,opt,name=pet_loss,json=petLoss,proto3,oneof"`
}

type SetRequest_SpeedLimit struct {
	SpeedLimit *SpeedLimitState `protobuf:"bytes,7,opt,name=speed_limit,json=speedLimit,proto3,oneof"`
}

func (*SetRequest_HhState) isSetRequest_Set() {}

func (*SetRequest_Brakes) isSetRequest_Set() {}

func (*SetRequest_SafetyBypass) isSetRequest_Set() {}

func (*SetRequest_Steering) isSetRequest_Set() {}

func (*SetRequest_SteeringCfg) isSetRequest_Set() {}

func (*SetRequest_PetLoss) isSetRequest_Set() {}

func (*SetRequest_SpeedLimit) isSetRequest_Set() {}

type SetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Set:
	//
	//	*SetReply_HhState
	//	*SetReply_Brakes
	//	*SetReply_SafetyBypass
	//	*SetReply_Steering
	//	*SetReply_SteeringCfg
	//	*SetReply_PetLoss
	//	*SetReply_SpeedLimit
	Set isSetReply_Set `protobuf_oneof:"set"`
}

func (x *SetReply) Reset() {
	*x = SetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReply) ProtoMessage() {}

func (x *SetReply) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReply.ProtoReflect.Descriptor instead.
func (*SetReply) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{12}
}

func (m *SetReply) GetSet() isSetReply_Set {
	if m != nil {
		return m.Set
	}
	return nil
}

func (x *SetReply) GetHhState() *HHState {
	if x, ok := x.GetSet().(*SetReply_HhState); ok {
		return x.HhState
	}
	return nil
}

func (x *SetReply) GetBrakes() *BrakeState {
	if x, ok := x.GetSet().(*SetReply_Brakes); ok {
		return x.Brakes
	}
	return nil
}

func (x *SetReply) GetSafetyBypass() *SafetySensorBypassState {
	if x, ok := x.GetSet().(*SetReply_SafetyBypass); ok {
		return x.SafetyBypass
	}
	return nil
}

func (x *SetReply) GetSteering() *SteeringState {
	if x, ok := x.GetSet().(*SetReply_Steering); ok {
		return x.Steering
	}
	return nil
}

func (x *SetReply) GetSteeringCfg() *SteeringCfgState {
	if x, ok := x.GetSet().(*SetReply_SteeringCfg); ok {
		return x.SteeringCfg
	}
	return nil
}

func (x *SetReply) GetPetLoss() *PetLossState {
	if x, ok := x.GetSet().(*SetReply_PetLoss); ok {
		return x.PetLoss
	}
	return nil
}

func (x *SetReply) GetSpeedLimit() *SpeedLimitState {
	if x, ok := x.GetSet().(*SetReply_SpeedLimit); ok {
		return x.SpeedLimit
	}
	return nil
}

type isSetReply_Set interface {
	isSetReply_Set()
}

type SetReply_HhState struct {
	HhState *HHState `protobuf:"bytes,1,opt,name=hh_state,json=hhState,proto3,oneof"`
}

type SetReply_Brakes struct {
	Brakes *BrakeState `protobuf:"bytes,2,opt,name=brakes,proto3,oneof"`
}

type SetReply_SafetyBypass struct {
	SafetyBypass *SafetySensorBypassState `protobuf:"bytes,3,opt,name=safety_bypass,json=safetyBypass,proto3,oneof"`
}

type SetReply_Steering struct {
	Steering *SteeringState `protobuf:"bytes,4,opt,name=steering,proto3,oneof"`
}

type SetReply_SteeringCfg struct {
	SteeringCfg *SteeringCfgState `protobuf:"bytes,5,opt,name=steering_cfg,json=steeringCfg,proto3,oneof"`
}

type SetReply_PetLoss struct {
	PetLoss *PetLossState `protobuf:"bytes,6,opt,name=pet_loss,json=petLoss,proto3,oneof"`
}

type SetReply_SpeedLimit struct {
	SpeedLimit *SpeedLimitState `protobuf:"bytes,7,opt,name=speed_limit,json=speedLimit,proto3,oneof"`
}

func (*SetReply_HhState) isSetReply_Set() {}

func (*SetReply_Brakes) isSetReply_Set() {}

func (*SetReply_SafetyBypass) isSetReply_Set() {}

func (*SetReply_Steering) isSetReply_Set() {}

func (*SetReply_SteeringCfg) isSetReply_Set() {}

func (*SetReply_PetLoss) isSetReply_Set() {}

func (*SetReply_SpeedLimit) isSetReply_Set() {}

type GetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Get:
	//
	//	*GetRequest_Status
	//	*GetRequest_HhState
	//	*GetRequest_Brakes
	//	*GetRequest_Safety
	//	*GetRequest_SafetyBypass
	//	*GetRequest_Steering
	//	*GetRequest_SteeringCfg
	//	*GetRequest_PetLoss
	//	*GetRequest_SpeedLimit
	Get isGetRequest_Get `protobuf_oneof:"get"`
}

func (x *GetRequest) Reset() {
	*x = GetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequest) ProtoMessage() {}

func (x *GetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequest.ProtoReflect.Descriptor instead.
func (*GetRequest) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{13}
}

func (m *GetRequest) GetGet() isGetRequest_Get {
	if m != nil {
		return m.Get
	}
	return nil
}

func (x *GetRequest) GetStatus() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Status); ok {
		return x.Status
	}
	return nil
}

func (x *GetRequest) GetHhState() *Empty {
	if x, ok := x.GetGet().(*GetRequest_HhState); ok {
		return x.HhState
	}
	return nil
}

func (x *GetRequest) GetBrakes() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Brakes); ok {
		return x.Brakes
	}
	return nil
}

func (x *GetRequest) GetSafety() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Safety); ok {
		return x.Safety
	}
	return nil
}

func (x *GetRequest) GetSafetyBypass() *Empty {
	if x, ok := x.GetGet().(*GetRequest_SafetyBypass); ok {
		return x.SafetyBypass
	}
	return nil
}

func (x *GetRequest) GetSteering() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Steering); ok {
		return x.Steering
	}
	return nil
}

func (x *GetRequest) GetSteeringCfg() *Empty {
	if x, ok := x.GetGet().(*GetRequest_SteeringCfg); ok {
		return x.SteeringCfg
	}
	return nil
}

func (x *GetRequest) GetPetLoss() *Empty {
	if x, ok := x.GetGet().(*GetRequest_PetLoss); ok {
		return x.PetLoss
	}
	return nil
}

func (x *GetRequest) GetSpeedLimit() *Empty {
	if x, ok := x.GetGet().(*GetRequest_SpeedLimit); ok {
		return x.SpeedLimit
	}
	return nil
}

type isGetRequest_Get interface {
	isGetRequest_Get()
}

type GetRequest_Status struct {
	Status *Empty `protobuf:"bytes,1,opt,name=status,proto3,oneof"`
}

type GetRequest_HhState struct {
	HhState *Empty `protobuf:"bytes,2,opt,name=hh_state,json=hhState,proto3,oneof"`
}

type GetRequest_Brakes struct {
	Brakes *Empty `protobuf:"bytes,3,opt,name=brakes,proto3,oneof"`
}

type GetRequest_Safety struct {
	Safety *Empty `protobuf:"bytes,4,opt,name=safety,proto3,oneof"`
}

type GetRequest_SafetyBypass struct {
	SafetyBypass *Empty `protobuf:"bytes,5,opt,name=safety_bypass,json=safetyBypass,proto3,oneof"`
}

type GetRequest_Steering struct {
	Steering *Empty `protobuf:"bytes,6,opt,name=steering,proto3,oneof"`
}

type GetRequest_SteeringCfg struct {
	SteeringCfg *Empty `protobuf:"bytes,7,opt,name=steering_cfg,json=steeringCfg,proto3,oneof"`
}

type GetRequest_PetLoss struct {
	PetLoss *Empty `protobuf:"bytes,8,opt,name=pet_loss,json=petLoss,proto3,oneof"`
}

type GetRequest_SpeedLimit struct {
	SpeedLimit *Empty `protobuf:"bytes,9,opt,name=speed_limit,json=speedLimit,proto3,oneof"`
}

func (*GetRequest_Status) isGetRequest_Get() {}

func (*GetRequest_HhState) isGetRequest_Get() {}

func (*GetRequest_Brakes) isGetRequest_Get() {}

func (*GetRequest_Safety) isGetRequest_Get() {}

func (*GetRequest_SafetyBypass) isGetRequest_Get() {}

func (*GetRequest_Steering) isGetRequest_Get() {}

func (*GetRequest_SteeringCfg) isGetRequest_Get() {}

func (*GetRequest_PetLoss) isGetRequest_Get() {}

func (*GetRequest_SpeedLimit) isGetRequest_Get() {}

type GetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Get:
	//
	//	*GetReply_Status
	//	*GetReply_HhState
	//	*GetReply_Brakes
	//	*GetReply_Safety
	//	*GetReply_SafetyBypass
	//	*GetReply_Steering
	//	*GetReply_SteeringCfg
	//	*GetReply_PetLoss
	//	*GetReply_SpeedLimit
	Get isGetReply_Get `protobuf_oneof:"get"`
}

func (x *GetReply) Reset() {
	*x = GetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReply) ProtoMessage() {}

func (x *GetReply) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReply.ProtoReflect.Descriptor instead.
func (*GetReply) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{14}
}

func (m *GetReply) GetGet() isGetReply_Get {
	if m != nil {
		return m.Get
	}
	return nil
}

func (x *GetReply) GetStatus() *TractorStatus {
	if x, ok := x.GetGet().(*GetReply_Status); ok {
		return x.Status
	}
	return nil
}

func (x *GetReply) GetHhState() *HHState {
	if x, ok := x.GetGet().(*GetReply_HhState); ok {
		return x.HhState
	}
	return nil
}

func (x *GetReply) GetBrakes() *BrakeState {
	if x, ok := x.GetGet().(*GetReply_Brakes); ok {
		return x.Brakes
	}
	return nil
}

func (x *GetReply) GetSafety() *SafetySensorsState {
	if x, ok := x.GetGet().(*GetReply_Safety); ok {
		return x.Safety
	}
	return nil
}

func (x *GetReply) GetSafetyBypass() *SafetySensorBypassState {
	if x, ok := x.GetGet().(*GetReply_SafetyBypass); ok {
		return x.SafetyBypass
	}
	return nil
}

func (x *GetReply) GetSteering() *SteeringState {
	if x, ok := x.GetGet().(*GetReply_Steering); ok {
		return x.Steering
	}
	return nil
}

func (x *GetReply) GetSteeringCfg() *SteeringCfgState {
	if x, ok := x.GetGet().(*GetReply_SteeringCfg); ok {
		return x.SteeringCfg
	}
	return nil
}

func (x *GetReply) GetPetLoss() *PetLossState {
	if x, ok := x.GetGet().(*GetReply_PetLoss); ok {
		return x.PetLoss
	}
	return nil
}

func (x *GetReply) GetSpeedLimit() *SpeedLimitState {
	if x, ok := x.GetGet().(*GetReply_SpeedLimit); ok {
		return x.SpeedLimit
	}
	return nil
}

type isGetReply_Get interface {
	isGetReply_Get()
}

type GetReply_Status struct {
	Status *TractorStatus `protobuf:"bytes,1,opt,name=status,proto3,oneof"`
}

type GetReply_HhState struct {
	HhState *HHState `protobuf:"bytes,2,opt,name=hh_state,json=hhState,proto3,oneof"`
}

type GetReply_Brakes struct {
	Brakes *BrakeState `protobuf:"bytes,3,opt,name=brakes,proto3,oneof"`
}

type GetReply_Safety struct {
	Safety *SafetySensorsState `protobuf:"bytes,4,opt,name=safety,proto3,oneof"`
}

type GetReply_SafetyBypass struct {
	SafetyBypass *SafetySensorBypassState `protobuf:"bytes,5,opt,name=safety_bypass,json=safetyBypass,proto3,oneof"`
}

type GetReply_Steering struct {
	Steering *SteeringState `protobuf:"bytes,6,opt,name=steering,proto3,oneof"`
}

type GetReply_SteeringCfg struct {
	SteeringCfg *SteeringCfgState `protobuf:"bytes,7,opt,name=steering_cfg,json=steeringCfg,proto3,oneof"`
}

type GetReply_PetLoss struct {
	PetLoss *PetLossState `protobuf:"bytes,8,opt,name=pet_loss,json=petLoss,proto3,oneof"`
}

type GetReply_SpeedLimit struct {
	SpeedLimit *SpeedLimitState `protobuf:"bytes,9,opt,name=speed_limit,json=speedLimit,proto3,oneof"`
}

func (*GetReply_Status) isGetReply_Get() {}

func (*GetReply_HhState) isGetReply_Get() {}

func (*GetReply_Brakes) isGetReply_Get() {}

func (*GetReply_Safety) isGetReply_Get() {}

func (*GetReply_SafetyBypass) isGetReply_Get() {}

func (*GetReply_Steering) isGetReply_Get() {}

func (*GetReply_SteeringCfg) isGetReply_Get() {}

func (*GetReply_PetLoss) isGetReply_Get() {}

func (*GetReply_SpeedLimit) isGetReply_Get() {}

type DebugRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DebugRequest) Reset() {
	*x = DebugRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugRequest) ProtoMessage() {}

func (x *DebugRequest) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugRequest.ProtoReflect.Descriptor instead.
func (*DebugRequest) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{15}
}

type DebugReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DebugReply) Reset() {
	*x = DebugReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugReply) ProtoMessage() {}

func (x *DebugReply) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugReply.ProtoReflect.Descriptor instead.
func (*DebugReply) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{16}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Set
	//	*Request_Get
	//	*Request_Pet
	//	*Request_Debug
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{17}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSet() *SetRequest {
	if x, ok := x.GetRequest().(*Request_Set); ok {
		return x.Set
	}
	return nil
}

func (x *Request) GetGet() *GetRequest {
	if x, ok := x.GetRequest().(*Request_Get); ok {
		return x.Get
	}
	return nil
}

func (x *Request) GetPet() *PetRequest {
	if x, ok := x.GetRequest().(*Request_Pet); ok {
		return x.Pet
	}
	return nil
}

func (x *Request) GetDebug() *DebugRequest {
	if x, ok := x.GetRequest().(*Request_Debug); ok {
		return x.Debug
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Set struct {
	Set *SetRequest `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type Request_Get struct {
	Get *GetRequest `protobuf:"bytes,2,opt,name=get,proto3,oneof"`
}

type Request_Pet struct {
	Pet *PetRequest `protobuf:"bytes,3,opt,name=pet,proto3,oneof"`
}

type Request_Debug struct {
	Debug *DebugRequest `protobuf:"bytes,4,opt,name=debug,proto3,oneof"`
}

func (*Request_Set) isRequest_Request() {}

func (*Request_Get) isRequest_Request() {}

func (*Request_Pet) isRequest_Request() {}

func (*Request_Debug) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Set
	//	*Reply_Get
	//	*Reply_Pet
	//	*Reply_Debug
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_carbon_tractor_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_carbon_tractor_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_carbon_tractor_proto_rawDescGZIP(), []int{18}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetSet() *SetReply {
	if x, ok := x.GetReply().(*Reply_Set); ok {
		return x.Set
	}
	return nil
}

func (x *Reply) GetGet() *GetReply {
	if x, ok := x.GetReply().(*Reply_Get); ok {
		return x.Get
	}
	return nil
}

func (x *Reply) GetPet() *TractorStatus {
	if x, ok := x.GetReply().(*Reply_Pet); ok {
		return x.Pet
	}
	return nil
}

func (x *Reply) GetDebug() *DebugReply {
	if x, ok := x.GetReply().(*Reply_Debug); ok {
		return x.Debug
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Set struct {
	Set *SetReply `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type Reply_Get struct {
	Get *GetReply `protobuf:"bytes,2,opt,name=get,proto3,oneof"`
}

type Reply_Pet struct {
	Pet *TractorStatus `protobuf:"bytes,3,opt,name=pet,proto3,oneof"`
}

type Reply_Debug struct {
	Debug *DebugReply `protobuf:"bytes,4,opt,name=debug,proto3,oneof"`
}

func (*Reply_Set) isReply_Reply() {}

func (*Reply_Get) isReply_Reply() {}

func (*Reply_Pet) isReply_Reply() {}

func (*Reply_Debug) isReply_Reply() {}

var File_carbon_tractor_proto protoreflect.FileDescriptor

var file_carbon_tractor_proto_rawDesc = []byte{
	0x0a, 0x14, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x34, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x07, 0x0a, 0x05,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3e, 0x0a, 0x07, 0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x33, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x4c, 0x0a, 0x0a, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x65, 0x66,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x4c, 0x65,
	0x66, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x69,
	0x67, 0x68, 0x74, 0x22, 0xcc, 0x01, 0x0a, 0x12, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x31,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65,
	0x64, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x31, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x32, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x53,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x32, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x33, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x33, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65,
	0x64, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x34, 0x22, 0x31, 0x0a, 0x17, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62,
	0x79, 0x70, 0x61, 0x73, 0x73, 0x22, 0x8c, 0x03, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x68, 0x65, 0x65,
	0x6c, 0x5f, 0x61, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x77,
	0x68, 0x65, 0x65, 0x6c, 0x41, 0x6e, 0x67, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x68, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x6c, 0x69, 0x66, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x68, 0x69, 0x74, 0x63, 0x68, 0x4c,
	0x69, 0x66, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a,
	0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x65, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f, 0x62,
	0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x6b, 0x6f, 0x75, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x72, 0x70, 0x6d, 0x73, 0x22, 0x0c, 0x0a, 0x0a, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x25, 0x0a, 0x0d, 0x53, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x61, 0x6e, 0x67, 0x6c, 0x65, 0x22, 0x89, 0x02, 0x0a, 0x10, 0x53, 0x74,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x6b, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x02, 0x6b, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x6b, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x02, 0x6b, 0x69, 0x12, 0x0e,
	0x0a, 0x02, 0x6b, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x02, 0x6b, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x68, 0x7a, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x74, 0x65, 0x48, 0x7a, 0x12, 0x3b, 0x0a, 0x1a, 0x6d,
	0x69, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x76,
	0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x17, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x76,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x6d, 0x61, 0x78, 0x5f,
	0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x76, 0x65, 0x5f, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x6d, 0x61,
	0x78, 0x53, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x76, 0x65, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x22, 0x29, 0x0a, 0x0c, 0x50, 0x65, 0x74, 0x4c, 0x6f, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x6f,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x75, 0x73, 0x65, 0x53, 0x74, 0x6f, 0x70,
	0x22, 0x39, 0x0a, 0x0f, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4d, 0x70, 0x68, 0x22, 0xd2, 0x03, 0x0a, 0x0a,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x68, 0x68,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x48,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x68, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x34, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06,
	0x62, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53,
	0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x79, 0x70, 0x61, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x74, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x73, 0x74, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x45, 0x0a, 0x0c, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x66, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x74, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x73,
	0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x65,
	0x74, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x70, 0x65,
	0x74, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x73, 0x65, 0x74,
	0x22, 0xd0, 0x03, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a,
	0x08, 0x68, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x68, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48,
	0x00, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x73, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x79,
	0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x74, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x74, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x73, 0x74,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x0a, 0x0c, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x74,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00,
	0x52, 0x0b, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x12, 0x39, 0x0a,
	0x08, 0x70, 0x65, 0x74, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x50, 0x65, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00,
	0x52, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x05, 0x0a, 0x03,
	0x73, 0x65, 0x74, 0x22, 0xf7, 0x03, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x68, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x07,
	0x68, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6b, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00,
	0x52, 0x06, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12, 0x3c, 0x0a, 0x0d, 0x73, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x61, 0x66, 0x65, 0x74,
	0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x12, 0x33, 0x0a, 0x08, 0x73, 0x74, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x48, 0x00, 0x52, 0x08, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x3a, 0x0a, 0x0c,
	0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x12, 0x32, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f,
	0x6c, 0x6f, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x48, 0x00, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x38, 0x0a, 0x0b,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x22, 0xc7, 0x04,
	0x0a, 0x08, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x68, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x48, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00,
	0x52, 0x07, 0x68, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x62, 0x72, 0x61,
	0x6b, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x72, 0x61, 0x6b, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x12,
	0x3c, 0x0a, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12, 0x4e, 0x0a,
	0x0d, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x0c, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x12, 0x3b, 0x0a,
	0x08, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x53, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00,
	0x52, 0x08, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x0a, 0x0c, 0x73, 0x74,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x53, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x66,
	0x67, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x48, 0x00, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x42, 0x0a, 0x0b,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x42, 0x05, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x22, 0x0e, 0x0a, 0x0c, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x0c, 0x0a, 0x0a, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xda, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x73, 0x65,
	0x74, 0x12, 0x2e, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65,
	0x74, 0x12, 0x2e, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x70, 0x65,
	0x74, 0x12, 0x34, 0x0a, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0xd3, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x03,
	0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x73, 0x65, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x67, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65, 0x74, 0x12, 0x31, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x32, 0x0a, 0x05, 0x64,
	0x65, 0x62, 0x75, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x42,
	0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x6f, 0x0a, 0x0d, 0x48, 0x48, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x48, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x48, 0x48, 0x5f,
	0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x48,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x0e,
	0x0a, 0x0a, 0x48, 0x48, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x48, 0x48, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x48,
	0x48, 0x5f, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x10, 0x05, 0x42, 0x17, 0x5a, 0x15, 0x6e, 0x61, 0x6e,
	0x6f, 0x70, 0x62, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_carbon_tractor_proto_rawDescOnce sync.Once
	file_carbon_tractor_proto_rawDescData = file_carbon_tractor_proto_rawDesc
)

func file_carbon_tractor_proto_rawDescGZIP() []byte {
	file_carbon_tractor_proto_rawDescOnce.Do(func() {
		file_carbon_tractor_proto_rawDescData = protoimpl.X.CompressGZIP(file_carbon_tractor_proto_rawDescData)
	})
	return file_carbon_tractor_proto_rawDescData
}

var file_carbon_tractor_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_carbon_tractor_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_carbon_tractor_proto_goTypes = []interface{}{
	(HHStateStatus)(0),              // 0: carbon_tractor.HHStateStatus
	(*Empty)(nil),                   // 1: carbon_tractor.Empty
	(*HHState)(nil),                 // 2: carbon_tractor.HHState
	(*BrakeState)(nil),              // 3: carbon_tractor.BrakeState
	(*SafetySensorsState)(nil),      // 4: carbon_tractor.SafetySensorsState
	(*SafetySensorBypassState)(nil), // 5: carbon_tractor.SafetySensorBypassState
	(*TractorStatus)(nil),           // 6: carbon_tractor.TractorStatus
	(*PetRequest)(nil),              // 7: carbon_tractor.PetRequest
	(*SteeringState)(nil),           // 8: carbon_tractor.SteeringState
	(*SteeringCfgState)(nil),        // 9: carbon_tractor.SteeringCfgState
	(*PetLossState)(nil),            // 10: carbon_tractor.PetLossState
	(*SpeedLimitState)(nil),         // 11: carbon_tractor.SpeedLimitState
	(*SetRequest)(nil),              // 12: carbon_tractor.SetRequest
	(*SetReply)(nil),                // 13: carbon_tractor.SetReply
	(*GetRequest)(nil),              // 14: carbon_tractor.GetRequest
	(*GetReply)(nil),                // 15: carbon_tractor.GetReply
	(*DebugRequest)(nil),            // 16: carbon_tractor.DebugRequest
	(*DebugReply)(nil),              // 17: carbon_tractor.DebugReply
	(*Request)(nil),                 // 18: carbon_tractor.Request
	(*Reply)(nil),                   // 19: carbon_tractor.Reply
	(*ots_tractor.GearState)(nil),   // 20: ots_tractor.GearState
}
var file_carbon_tractor_proto_depIdxs = []int32{
	0,  // 0: carbon_tractor.HHState.state:type_name -> carbon_tractor.HHStateStatus
	2,  // 1: carbon_tractor.TractorStatus.state:type_name -> carbon_tractor.HHState
	20, // 2: carbon_tractor.TractorStatus.gear:type_name -> ots_tractor.GearState
	2,  // 3: carbon_tractor.SetRequest.hh_state:type_name -> carbon_tractor.HHState
	3,  // 4: carbon_tractor.SetRequest.brakes:type_name -> carbon_tractor.BrakeState
	5,  // 5: carbon_tractor.SetRequest.safety_bypass:type_name -> carbon_tractor.SafetySensorBypassState
	8,  // 6: carbon_tractor.SetRequest.steering:type_name -> carbon_tractor.SteeringState
	9,  // 7: carbon_tractor.SetRequest.steering_cfg:type_name -> carbon_tractor.SteeringCfgState
	10, // 8: carbon_tractor.SetRequest.pet_loss:type_name -> carbon_tractor.PetLossState
	11, // 9: carbon_tractor.SetRequest.speed_limit:type_name -> carbon_tractor.SpeedLimitState
	2,  // 10: carbon_tractor.SetReply.hh_state:type_name -> carbon_tractor.HHState
	3,  // 11: carbon_tractor.SetReply.brakes:type_name -> carbon_tractor.BrakeState
	5,  // 12: carbon_tractor.SetReply.safety_bypass:type_name -> carbon_tractor.SafetySensorBypassState
	8,  // 13: carbon_tractor.SetReply.steering:type_name -> carbon_tractor.SteeringState
	9,  // 14: carbon_tractor.SetReply.steering_cfg:type_name -> carbon_tractor.SteeringCfgState
	10, // 15: carbon_tractor.SetReply.pet_loss:type_name -> carbon_tractor.PetLossState
	11, // 16: carbon_tractor.SetReply.speed_limit:type_name -> carbon_tractor.SpeedLimitState
	1,  // 17: carbon_tractor.GetRequest.status:type_name -> carbon_tractor.Empty
	1,  // 18: carbon_tractor.GetRequest.hh_state:type_name -> carbon_tractor.Empty
	1,  // 19: carbon_tractor.GetRequest.brakes:type_name -> carbon_tractor.Empty
	1,  // 20: carbon_tractor.GetRequest.safety:type_name -> carbon_tractor.Empty
	1,  // 21: carbon_tractor.GetRequest.safety_bypass:type_name -> carbon_tractor.Empty
	1,  // 22: carbon_tractor.GetRequest.steering:type_name -> carbon_tractor.Empty
	1,  // 23: carbon_tractor.GetRequest.steering_cfg:type_name -> carbon_tractor.Empty
	1,  // 24: carbon_tractor.GetRequest.pet_loss:type_name -> carbon_tractor.Empty
	1,  // 25: carbon_tractor.GetRequest.speed_limit:type_name -> carbon_tractor.Empty
	6,  // 26: carbon_tractor.GetReply.status:type_name -> carbon_tractor.TractorStatus
	2,  // 27: carbon_tractor.GetReply.hh_state:type_name -> carbon_tractor.HHState
	3,  // 28: carbon_tractor.GetReply.brakes:type_name -> carbon_tractor.BrakeState
	4,  // 29: carbon_tractor.GetReply.safety:type_name -> carbon_tractor.SafetySensorsState
	5,  // 30: carbon_tractor.GetReply.safety_bypass:type_name -> carbon_tractor.SafetySensorBypassState
	8,  // 31: carbon_tractor.GetReply.steering:type_name -> carbon_tractor.SteeringState
	9,  // 32: carbon_tractor.GetReply.steering_cfg:type_name -> carbon_tractor.SteeringCfgState
	10, // 33: carbon_tractor.GetReply.pet_loss:type_name -> carbon_tractor.PetLossState
	11, // 34: carbon_tractor.GetReply.speed_limit:type_name -> carbon_tractor.SpeedLimitState
	12, // 35: carbon_tractor.Request.set:type_name -> carbon_tractor.SetRequest
	14, // 36: carbon_tractor.Request.get:type_name -> carbon_tractor.GetRequest
	7,  // 37: carbon_tractor.Request.pet:type_name -> carbon_tractor.PetRequest
	16, // 38: carbon_tractor.Request.debug:type_name -> carbon_tractor.DebugRequest
	13, // 39: carbon_tractor.Reply.set:type_name -> carbon_tractor.SetReply
	15, // 40: carbon_tractor.Reply.get:type_name -> carbon_tractor.GetReply
	6,  // 41: carbon_tractor.Reply.pet:type_name -> carbon_tractor.TractorStatus
	17, // 42: carbon_tractor.Reply.debug:type_name -> carbon_tractor.DebugReply
	43, // [43:43] is the sub-list for method output_type
	43, // [43:43] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_carbon_tractor_proto_init() }
func file_carbon_tractor_proto_init() {
	if File_carbon_tractor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_carbon_tractor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HHState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrakeState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafetySensorsState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafetySensorBypassState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SteeringState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SteeringCfgState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetLossState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpeedLimitState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_carbon_tractor_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_carbon_tractor_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*SetRequest_HhState)(nil),
		(*SetRequest_Brakes)(nil),
		(*SetRequest_SafetyBypass)(nil),
		(*SetRequest_Steering)(nil),
		(*SetRequest_SteeringCfg)(nil),
		(*SetRequest_PetLoss)(nil),
		(*SetRequest_SpeedLimit)(nil),
	}
	file_carbon_tractor_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*SetReply_HhState)(nil),
		(*SetReply_Brakes)(nil),
		(*SetReply_SafetyBypass)(nil),
		(*SetReply_Steering)(nil),
		(*SetReply_SteeringCfg)(nil),
		(*SetReply_PetLoss)(nil),
		(*SetReply_SpeedLimit)(nil),
	}
	file_carbon_tractor_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*GetRequest_Status)(nil),
		(*GetRequest_HhState)(nil),
		(*GetRequest_Brakes)(nil),
		(*GetRequest_Safety)(nil),
		(*GetRequest_SafetyBypass)(nil),
		(*GetRequest_Steering)(nil),
		(*GetRequest_SteeringCfg)(nil),
		(*GetRequest_PetLoss)(nil),
		(*GetRequest_SpeedLimit)(nil),
	}
	file_carbon_tractor_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*GetReply_Status)(nil),
		(*GetReply_HhState)(nil),
		(*GetReply_Brakes)(nil),
		(*GetReply_Safety)(nil),
		(*GetReply_SafetyBypass)(nil),
		(*GetReply_Steering)(nil),
		(*GetReply_SteeringCfg)(nil),
		(*GetReply_PetLoss)(nil),
		(*GetReply_SpeedLimit)(nil),
	}
	file_carbon_tractor_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*Request_Set)(nil),
		(*Request_Get)(nil),
		(*Request_Pet)(nil),
		(*Request_Debug)(nil),
	}
	file_carbon_tractor_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*Reply_Set)(nil),
		(*Reply_Get)(nil),
		(*Reply_Pet)(nil),
		(*Reply_Debug)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_carbon_tractor_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_carbon_tractor_proto_goTypes,
		DependencyIndexes: file_carbon_tractor_proto_depIdxs,
		EnumInfos:         file_carbon_tractor_proto_enumTypes,
		MessageInfos:      file_carbon_tractor_proto_msgTypes,
	}.Build()
	File_carbon_tractor_proto = out.File
	file_carbon_tractor_proto_rawDesc = nil
	file_carbon_tractor_proto_goTypes = nil
	file_carbon_tractor_proto_depIdxs = nil
}

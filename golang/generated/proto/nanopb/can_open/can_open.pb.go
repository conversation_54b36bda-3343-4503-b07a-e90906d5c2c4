// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: can_open.proto

package can_open

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SDO_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index     uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Subindex  uint32 `protobuf:"varint,2,opt,name=subindex,proto3" json:"subindex,omitempty"`
	Value     uint32 `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	Cs        uint32 `protobuf:"varint,4,opt,name=cs,proto3" json:"cs,omitempty"`
	Expedited uint32 `protobuf:"varint,5,opt,name=expedited,proto3" json:"expedited,omitempty"`
}

func (x *SDO_Request) Reset() {
	*x = SDO_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDO_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDO_Request) ProtoMessage() {}

func (x *SDO_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDO_Request.ProtoReflect.Descriptor instead.
func (*SDO_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{0}
}

func (x *SDO_Request) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SDO_Request) GetSubindex() uint32 {
	if x != nil {
		return x.Subindex
	}
	return 0
}

func (x *SDO_Request) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *SDO_Request) GetCs() uint32 {
	if x != nil {
		return x.Cs
	}
	return 0
}

func (x *SDO_Request) GetExpedited() uint32 {
	if x != nil {
		return x.Expedited
	}
	return 0
}

type PDO_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Func uint32 `protobuf:"varint,1,opt,name=func,proto3" json:"func,omitempty"`
	Size uint32 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *PDO_Request) Reset() {
	*x = PDO_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDO_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDO_Request) ProtoMessage() {}

func (x *PDO_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDO_Request.ProtoReflect.Descriptor instead.
func (*PDO_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{1}
}

func (x *PDO_Request) GetFunc() uint32 {
	if x != nil {
		return x.Func
	}
	return 0
}

func (x *PDO_Request) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PDO_Request) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type RTR_PDO_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Func uint32 `protobuf:"varint,1,opt,name=func,proto3" json:"func,omitempty"`
}

func (x *RTR_PDO_Request) Reset() {
	*x = RTR_PDO_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTR_PDO_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTR_PDO_Request) ProtoMessage() {}

func (x *RTR_PDO_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTR_PDO_Request.ProtoReflect.Descriptor instead.
func (*RTR_PDO_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{2}
}

func (x *RTR_PDO_Request) GetFunc() uint32 {
	if x != nil {
		return x.Func
	}
	return 0
}

type NMT_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State uint32 `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *NMT_Request) Reset() {
	*x = NMT_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NMT_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NMT_Request) ProtoMessage() {}

func (x *NMT_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NMT_Request.ProtoReflect.Descriptor instead.
func (*NMT_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{3}
}

func (x *NMT_Request) GetState() uint32 {
	if x != nil {
		return x.State
	}
	return 0
}

type Await_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeoutMs uint32 `protobuf:"varint,1,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
	Func      uint32 `protobuf:"varint,2,opt,name=func,proto3" json:"func,omitempty"`
}

func (x *Await_Request) Reset() {
	*x = Await_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Await_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Await_Request) ProtoMessage() {}

func (x *Await_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Await_Request.ProtoReflect.Descriptor instead.
func (*Await_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{4}
}

func (x *Await_Request) GetTimeoutMs() uint32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

func (x *Await_Request) GetFunc() uint32 {
	if x != nil {
		return x.Func
	}
	return 0
}

type SDO_Download_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index    uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Subindex uint32 `protobuf:"varint,2,opt,name=subindex,proto3" json:"subindex,omitempty"`
	Value    uint32 `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *SDO_Download_Request) Reset() {
	*x = SDO_Download_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDO_Download_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDO_Download_Request) ProtoMessage() {}

func (x *SDO_Download_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDO_Download_Request.ProtoReflect.Descriptor instead.
func (*SDO_Download_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{5}
}

func (x *SDO_Download_Request) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SDO_Download_Request) GetSubindex() uint32 {
	if x != nil {
		return x.Subindex
	}
	return 0
}

func (x *SDO_Download_Request) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type SDO_Upload_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index    uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Subindex uint32 `protobuf:"varint,2,opt,name=subindex,proto3" json:"subindex,omitempty"`
}

func (x *SDO_Upload_Request) Reset() {
	*x = SDO_Upload_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDO_Upload_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDO_Upload_Request) ProtoMessage() {}

func (x *SDO_Upload_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDO_Upload_Request.ProtoReflect.Descriptor instead.
func (*SDO_Upload_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{6}
}

func (x *SDO_Upload_Request) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SDO_Upload_Request) GetSubindex() uint32 {
	if x != nil {
		return x.Subindex
	}
	return 0
}

type NMT_Reset_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NMT_Reset_Request) Reset() {
	*x = NMT_Reset_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NMT_Reset_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NMT_Reset_Request) ProtoMessage() {}

func (x *NMT_Reset_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NMT_Reset_Request.ProtoReflect.Descriptor instead.
func (*NMT_Reset_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{7}
}

type NMT_Start_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NMT_Start_Request) Reset() {
	*x = NMT_Start_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NMT_Start_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NMT_Start_Request) ProtoMessage() {}

func (x *NMT_Start_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NMT_Start_Request.ProtoReflect.Descriptor instead.
func (*NMT_Start_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{8}
}

type NMT_Stop_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NMT_Stop_Request) Reset() {
	*x = NMT_Stop_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NMT_Stop_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NMT_Stop_Request) ProtoMessage() {}

func (x *NMT_Stop_Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NMT_Stop_Request.ProtoReflect.Descriptor instead.
func (*NMT_Stop_Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{9}
}

type ACK_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ACK_Reply) Reset() {
	*x = ACK_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACK_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACK_Reply) ProtoMessage() {}

func (x *ACK_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACK_Reply.ProtoReflect.Descriptor instead.
func (*ACK_Reply) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{10}
}

type SDO_Packet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Spec     uint32 `protobuf:"varint,1,opt,name=spec,proto3" json:"spec,omitempty"`
	Index    uint32 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Subindex uint32 `protobuf:"varint,3,opt,name=subindex,proto3" json:"subindex,omitempty"`
	Data     []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SDO_Packet) Reset() {
	*x = SDO_Packet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDO_Packet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDO_Packet) ProtoMessage() {}

func (x *SDO_Packet) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDO_Packet.ProtoReflect.Descriptor instead.
func (*SDO_Packet) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{11}
}

func (x *SDO_Packet) GetSpec() uint32 {
	if x != nil {
		return x.Spec
	}
	return 0
}

func (x *SDO_Packet) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SDO_Packet) GetSubindex() uint32 {
	if x != nil {
		return x.Subindex
	}
	return 0
}

func (x *SDO_Packet) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type PDO_Packet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *PDO_Packet) Reset() {
	*x = PDO_Packet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDO_Packet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDO_Packet) ProtoMessage() {}

func (x *PDO_Packet) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDO_Packet.ProtoReflect.Descriptor instead.
func (*PDO_Packet) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{12}
}

func (x *PDO_Packet) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type NMT_Packet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State  uint32 `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	NodeId uint32 `protobuf:"varint,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
}

func (x *NMT_Packet) Reset() {
	*x = NMT_Packet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NMT_Packet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NMT_Packet) ProtoMessage() {}

func (x *NMT_Packet) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NMT_Packet.ProtoReflect.Descriptor instead.
func (*NMT_Packet) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{13}
}

func (x *NMT_Packet) GetState() uint32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *NMT_Packet) GetNodeId() uint32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type Message_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Func   uint32 `protobuf:"varint,1,opt,name=func,proto3" json:"func,omitempty"`
	NodeId uint32 `protobuf:"varint,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	// Types that are assignable to Pkt:
	//
	//	*Message_Reply_Sdo
	//	*Message_Reply_Pdo
	//	*Message_Reply_Nmt
	Pkt isMessage_Reply_Pkt `protobuf_oneof:"pkt"`
}

func (x *Message_Reply) Reset() {
	*x = Message_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message_Reply) ProtoMessage() {}

func (x *Message_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message_Reply.ProtoReflect.Descriptor instead.
func (*Message_Reply) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{14}
}

func (x *Message_Reply) GetFunc() uint32 {
	if x != nil {
		return x.Func
	}
	return 0
}

func (x *Message_Reply) GetNodeId() uint32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (m *Message_Reply) GetPkt() isMessage_Reply_Pkt {
	if m != nil {
		return m.Pkt
	}
	return nil
}

func (x *Message_Reply) GetSdo() *SDO_Packet {
	if x, ok := x.GetPkt().(*Message_Reply_Sdo); ok {
		return x.Sdo
	}
	return nil
}

func (x *Message_Reply) GetPdo() *PDO_Packet {
	if x, ok := x.GetPkt().(*Message_Reply_Pdo); ok {
		return x.Pdo
	}
	return nil
}

func (x *Message_Reply) GetNmt() *NMT_Packet {
	if x, ok := x.GetPkt().(*Message_Reply_Nmt); ok {
		return x.Nmt
	}
	return nil
}

type isMessage_Reply_Pkt interface {
	isMessage_Reply_Pkt()
}

type Message_Reply_Sdo struct {
	Sdo *SDO_Packet `protobuf:"bytes,3,opt,name=sdo,proto3,oneof"`
}

type Message_Reply_Pdo struct {
	Pdo *PDO_Packet `protobuf:"bytes,4,opt,name=pdo,proto3,oneof"`
}

type Message_Reply_Nmt struct {
	Nmt *NMT_Packet `protobuf:"bytes,5,opt,name=nmt,proto3,oneof"`
}

func (*Message_Reply_Sdo) isMessage_Reply_Pkt() {}

func (*Message_Reply_Pdo) isMessage_Reply_Pkt() {}

func (*Message_Reply_Nmt) isMessage_Reply_Pkt() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Sdo
	//	*Request_Pdo
	//	*Request_Rtr
	//	*Request_Nmt
	//	*Request_Await
	//	*Request_SdoDownload
	//	*Request_SdoUpload
	//	*Request_Reset_
	//	*Request_Start
	//	*Request_Stop
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{15}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSdo() *SDO_Request {
	if x, ok := x.GetRequest().(*Request_Sdo); ok {
		return x.Sdo
	}
	return nil
}

func (x *Request) GetPdo() *PDO_Request {
	if x, ok := x.GetRequest().(*Request_Pdo); ok {
		return x.Pdo
	}
	return nil
}

func (x *Request) GetRtr() *RTR_PDO_Request {
	if x, ok := x.GetRequest().(*Request_Rtr); ok {
		return x.Rtr
	}
	return nil
}

func (x *Request) GetNmt() *NMT_Request {
	if x, ok := x.GetRequest().(*Request_Nmt); ok {
		return x.Nmt
	}
	return nil
}

func (x *Request) GetAwait() *Await_Request {
	if x, ok := x.GetRequest().(*Request_Await); ok {
		return x.Await
	}
	return nil
}

func (x *Request) GetSdoDownload() *SDO_Download_Request {
	if x, ok := x.GetRequest().(*Request_SdoDownload); ok {
		return x.SdoDownload
	}
	return nil
}

func (x *Request) GetSdoUpload() *SDO_Upload_Request {
	if x, ok := x.GetRequest().(*Request_SdoUpload); ok {
		return x.SdoUpload
	}
	return nil
}

func (x *Request) GetReset_() *NMT_Reset_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *Request) GetStart() *NMT_Start_Request {
	if x, ok := x.GetRequest().(*Request_Start); ok {
		return x.Start
	}
	return nil
}

func (x *Request) GetStop() *NMT_Stop_Request {
	if x, ok := x.GetRequest().(*Request_Stop); ok {
		return x.Stop
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Sdo struct {
	Sdo *SDO_Request `protobuf:"bytes,1,opt,name=sdo,proto3,oneof"`
}

type Request_Pdo struct {
	Pdo *PDO_Request `protobuf:"bytes,2,opt,name=pdo,proto3,oneof"`
}

type Request_Rtr struct {
	Rtr *RTR_PDO_Request `protobuf:"bytes,3,opt,name=rtr,proto3,oneof"`
}

type Request_Nmt struct {
	Nmt *NMT_Request `protobuf:"bytes,4,opt,name=nmt,proto3,oneof"`
}

type Request_Await struct {
	Await *Await_Request `protobuf:"bytes,5,opt,name=await,proto3,oneof"`
}

type Request_SdoDownload struct {
	SdoDownload *SDO_Download_Request `protobuf:"bytes,6,opt,name=sdo_download,json=sdoDownload,proto3,oneof"`
}

type Request_SdoUpload struct {
	SdoUpload *SDO_Upload_Request `protobuf:"bytes,7,opt,name=sdo_upload,json=sdoUpload,proto3,oneof"`
}

type Request_Reset_ struct {
	Reset_ *NMT_Reset_Request `protobuf:"bytes,8,opt,name=reset,proto3,oneof"`
}

type Request_Start struct {
	Start *NMT_Start_Request `protobuf:"bytes,9,opt,name=start,proto3,oneof"`
}

type Request_Stop struct {
	Stop *NMT_Stop_Request `protobuf:"bytes,10,opt,name=stop,proto3,oneof"`
}

func (*Request_Sdo) isRequest_Request() {}

func (*Request_Pdo) isRequest_Request() {}

func (*Request_Rtr) isRequest_Request() {}

func (*Request_Nmt) isRequest_Request() {}

func (*Request_Await) isRequest_Request() {}

func (*Request_SdoDownload) isRequest_Request() {}

func (*Request_SdoUpload) isRequest_Request() {}

func (*Request_Reset_) isRequest_Request() {}

func (*Request_Start) isRequest_Request() {}

func (*Request_Stop) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Ack
	//	*Reply_Msg
	//	*Reply_Error
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_can_open_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_can_open_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_can_open_proto_rawDescGZIP(), []int{16}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetMsg() *Message_Reply {
	if x, ok := x.GetReply().(*Reply_Msg); ok {
		return x.Msg
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,1,opt,name=ack,proto3,oneof"`
}

type Reply_Msg struct {
	Msg *Message_Reply `protobuf:"bytes,2,opt,name=msg,proto3,oneof"`
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,3,opt,name=error,proto3,oneof"`
}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Msg) isReply_Reply() {}

func (*Reply_Error) isReply_Reply() {}

var File_can_open_proto protoreflect.FileDescriptor

var file_can_open_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61,
	0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x01, 0x0a, 0x0b, 0x53, 0x44, 0x4f,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x63, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x63, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x78, 0x70, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64, 0x22, 0x49,
	0x0a, 0x0b, 0x50, 0x44, 0x4f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x66, 0x75, 0x6e, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x75, 0x6e,
	0x63, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x25, 0x0a, 0x0f, 0x52, 0x54, 0x52,
	0x5f, 0x50, 0x44, 0x4f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x75, 0x6e, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x63,
	0x22, 0x23, 0x0a, 0x0b, 0x4e, 0x4d, 0x54, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x42, 0x0a, 0x0d, 0x41, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x4d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x22, 0x5e, 0x0a, 0x14, 0x53, 0x44, 0x4f,
	0x5f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x46, 0x0a, 0x12, 0x53, 0x44, 0x4f,
	0x5f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0x13, 0x0a, 0x11, 0x4e, 0x4d, 0x54, 0x5f, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x13, 0x0a, 0x11, 0x4e, 0x4d, 0x54, 0x5f, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x12, 0x0a, 0x10, 0x4e,
	0x4d, 0x54, 0x5f, 0x53, 0x74, 0x6f, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x0b, 0x0a, 0x09, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x66, 0x0a, 0x0a,
	0x53, 0x44, 0x4f, 0x5f, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x75, 0x62, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x20, 0x0a, 0x0a, 0x50, 0x44, 0x4f, 0x5f, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3b, 0x0a, 0x0a, 0x4e, 0x4d, 0x54, 0x5f, 0x50, 0x61,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x22, 0xc1, 0x01, 0x0a, 0x0d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x03, 0x73, 0x64, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x44, 0x4f, 0x5f, 0x50,
	0x61, 0x63, 0x6b, 0x65, 0x74, 0x48, 0x00, 0x52, 0x03, 0x73, 0x64, 0x6f, 0x12, 0x28, 0x0a, 0x03,
	0x70, 0x64, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x6e, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x50, 0x44, 0x4f, 0x5f, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x48,
	0x00, 0x52, 0x03, 0x70, 0x64, 0x6f, 0x12, 0x28, 0x0a, 0x03, 0x6e, 0x6d, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4e,
	0x4d, 0x54, 0x5f, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x48, 0x00, 0x52, 0x03, 0x6e, 0x6d, 0x74,
	0x42, 0x05, 0x0a, 0x03, 0x70, 0x6b, 0x74, 0x22, 0x95, 0x04, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x03, 0x73, 0x64, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x44, 0x4f, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x73, 0x64, 0x6f, 0x12, 0x29,
	0x0a, 0x03, 0x70, 0x64, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61,
	0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x50, 0x44, 0x4f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x70, 0x64, 0x6f, 0x12, 0x2d, 0x0a, 0x03, 0x72, 0x74, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x52, 0x54, 0x52, 0x5f, 0x50, 0x44, 0x4f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x03, 0x72, 0x74, 0x72, 0x12, 0x29, 0x0a, 0x03, 0x6e, 0x6d, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x2e, 0x4e, 0x4d, 0x54, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03,
	0x6e, 0x6d, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x61, 0x77, 0x61, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x41, 0x77,
	0x61, 0x69, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x61,
	0x77, 0x61, 0x69, 0x74, 0x12, 0x43, 0x0a, 0x0c, 0x73, 0x64, 0x6f, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x6e,
	0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x44, 0x4f, 0x5f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x64,
	0x6f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x3d, 0x0a, 0x0a, 0x73, 0x64, 0x6f,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x44, 0x4f, 0x5f, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x73,
	0x64, 0x6f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x2e, 0x4e, 0x4d, 0x54, 0x5f, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x33, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4e, 0x4d, 0x54, 0x5f, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4e, 0x4d, 0x54, 0x5f,
	0x53, 0x74, 0x6f, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04,
	0x73, 0x74, 0x6f, 0x70, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x81, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b,
	0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x2b, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x42, 0x11, 0x5a, 0x0f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x63, 0x61,
	0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_can_open_proto_rawDescOnce sync.Once
	file_can_open_proto_rawDescData = file_can_open_proto_rawDesc
)

func file_can_open_proto_rawDescGZIP() []byte {
	file_can_open_proto_rawDescOnce.Do(func() {
		file_can_open_proto_rawDescData = protoimpl.X.CompressGZIP(file_can_open_proto_rawDescData)
	})
	return file_can_open_proto_rawDescData
}

var file_can_open_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_can_open_proto_goTypes = []interface{}{
	(*SDO_Request)(nil),          // 0: can_open.SDO_Request
	(*PDO_Request)(nil),          // 1: can_open.PDO_Request
	(*RTR_PDO_Request)(nil),      // 2: can_open.RTR_PDO_Request
	(*NMT_Request)(nil),          // 3: can_open.NMT_Request
	(*Await_Request)(nil),        // 4: can_open.Await_Request
	(*SDO_Download_Request)(nil), // 5: can_open.SDO_Download_Request
	(*SDO_Upload_Request)(nil),   // 6: can_open.SDO_Upload_Request
	(*NMT_Reset_Request)(nil),    // 7: can_open.NMT_Reset_Request
	(*NMT_Start_Request)(nil),    // 8: can_open.NMT_Start_Request
	(*NMT_Stop_Request)(nil),     // 9: can_open.NMT_Stop_Request
	(*ACK_Reply)(nil),            // 10: can_open.ACK_Reply
	(*SDO_Packet)(nil),           // 11: can_open.SDO_Packet
	(*PDO_Packet)(nil),           // 12: can_open.PDO_Packet
	(*NMT_Packet)(nil),           // 13: can_open.NMT_Packet
	(*Message_Reply)(nil),        // 14: can_open.Message_Reply
	(*Request)(nil),              // 15: can_open.Request
	(*Reply)(nil),                // 16: can_open.Reply
	(*ack.Ack)(nil),              // 17: ack.Ack
	(*error1.Error)(nil),         // 18: error.Error
}
var file_can_open_proto_depIdxs = []int32{
	11, // 0: can_open.Message_Reply.sdo:type_name -> can_open.SDO_Packet
	12, // 1: can_open.Message_Reply.pdo:type_name -> can_open.PDO_Packet
	13, // 2: can_open.Message_Reply.nmt:type_name -> can_open.NMT_Packet
	0,  // 3: can_open.Request.sdo:type_name -> can_open.SDO_Request
	1,  // 4: can_open.Request.pdo:type_name -> can_open.PDO_Request
	2,  // 5: can_open.Request.rtr:type_name -> can_open.RTR_PDO_Request
	3,  // 6: can_open.Request.nmt:type_name -> can_open.NMT_Request
	4,  // 7: can_open.Request.await:type_name -> can_open.Await_Request
	5,  // 8: can_open.Request.sdo_download:type_name -> can_open.SDO_Download_Request
	6,  // 9: can_open.Request.sdo_upload:type_name -> can_open.SDO_Upload_Request
	7,  // 10: can_open.Request.reset:type_name -> can_open.NMT_Reset_Request
	8,  // 11: can_open.Request.start:type_name -> can_open.NMT_Start_Request
	9,  // 12: can_open.Request.stop:type_name -> can_open.NMT_Stop_Request
	17, // 13: can_open.Reply.ack:type_name -> ack.Ack
	14, // 14: can_open.Reply.msg:type_name -> can_open.Message_Reply
	18, // 15: can_open.Reply.error:type_name -> error.Error
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_can_open_proto_init() }
func file_can_open_proto_init() {
	if File_can_open_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_can_open_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDO_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDO_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTR_PDO_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NMT_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Await_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDO_Download_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDO_Upload_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NMT_Reset_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NMT_Start_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NMT_Stop_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACK_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDO_Packet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDO_Packet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NMT_Packet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_can_open_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_can_open_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*Message_Reply_Sdo)(nil),
		(*Message_Reply_Pdo)(nil),
		(*Message_Reply_Nmt)(nil),
	}
	file_can_open_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*Request_Sdo)(nil),
		(*Request_Pdo)(nil),
		(*Request_Rtr)(nil),
		(*Request_Nmt)(nil),
		(*Request_Await)(nil),
		(*Request_SdoDownload)(nil),
		(*Request_SdoUpload)(nil),
		(*Request_Reset_)(nil),
		(*Request_Start)(nil),
		(*Request_Stop)(nil),
	}
	file_can_open_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*Reply_Ack)(nil),
		(*Reply_Msg)(nil),
		(*Reply_Error)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_can_open_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_can_open_proto_goTypes,
		DependencyIndexes: file_can_open_proto_depIdxs,
		MessageInfos:      file_can_open_proto_msgTypes,
	}.Build()
	File_can_open_proto = out.File
	file_can_open_proto_rawDesc = nil
	file_can_open_proto_goTypes = nil
	file_can_open_proto_depIdxs = nil
}

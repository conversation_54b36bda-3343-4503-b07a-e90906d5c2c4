// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: lens.proto

package lens

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Set_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"` // uint8_t
}

func (x *Set_Request) Reset() {
	*x = Set_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lens_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Request) ProtoMessage() {}

func (x *Set_Request) ProtoReflect() protoreflect.Message {
	mi := &file_lens_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Request.ProtoReflect.Descriptor instead.
func (*Set_Request) Descriptor() ([]byte, []int) {
	return file_lens_proto_rawDescGZIP(), []int{0}
}

func (x *Set_Request) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type Get_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Request) Reset() {
	*x = Get_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lens_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Request) ProtoMessage() {}

func (x *Get_Request) ProtoReflect() protoreflect.Message {
	mi := &file_lens_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Request.ProtoReflect.Descriptor instead.
func (*Get_Request) Descriptor() ([]byte, []int) {
	return file_lens_proto_rawDescGZIP(), []int{1}
}

type Get_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Get_Reply) Reset() {
	*x = Get_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lens_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Reply) ProtoMessage() {}

func (x *Get_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_lens_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Reply.ProtoReflect.Descriptor instead.
func (*Get_Reply) Descriptor() ([]byte, []int) {
	return file_lens_proto_rawDescGZIP(), []int{2}
}

func (x *Get_Reply) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Set
	//	*Request_Get
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lens_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_lens_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_lens_proto_rawDescGZIP(), []int{3}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSet() *Set_Request {
	if x, ok := x.GetRequest().(*Request_Set); ok {
		return x.Set
	}
	return nil
}

func (x *Request) GetGet() *Get_Request {
	if x, ok := x.GetRequest().(*Request_Get); ok {
		return x.Get
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Set struct {
	Set *Set_Request `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type Request_Get struct {
	Get *Get_Request `protobuf:"bytes,2,opt,name=get,proto3,oneof"`
}

func (*Request_Set) isRequest_Request() {}

func (*Request_Get) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Get
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lens_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_lens_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_lens_proto_rawDescGZIP(), []int{4}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetGet() *Get_Reply {
	if x, ok := x.GetReply().(*Reply_Get); ok {
		return x.Get
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Get struct {
	Get *Get_Reply `protobuf:"bytes,3,opt,name=get,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Get) isReply_Reply() {}

var File_lens_proto protoreflect.FileDescriptor

var file_lens_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x6c, 0x65, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x6c, 0x65,
	0x6e, 0x73, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x23, 0x0a, 0x0b, 0x53, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x0d, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x21, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x62, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x6c, 0x65, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x73, 0x65, 0x74, 0x12, 0x25, 0x0a, 0x03, 0x67, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6c, 0x65, 0x6e, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65, 0x74,
	0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x79, 0x0a, 0x05, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63,
	0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x23, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6c, 0x65, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65, 0x74, 0x42, 0x07, 0x0a,
	0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x0d, 0x5a, 0x0b, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x6c, 0x65, 0x6e, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_lens_proto_rawDescOnce sync.Once
	file_lens_proto_rawDescData = file_lens_proto_rawDesc
)

func file_lens_proto_rawDescGZIP() []byte {
	file_lens_proto_rawDescOnce.Do(func() {
		file_lens_proto_rawDescData = protoimpl.X.CompressGZIP(file_lens_proto_rawDescData)
	})
	return file_lens_proto_rawDescData
}

var file_lens_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_lens_proto_goTypes = []interface{}{
	(*Set_Request)(nil),  // 0: lens.Set_Request
	(*Get_Request)(nil),  // 1: lens.Get_Request
	(*Get_Reply)(nil),    // 2: lens.Get_Reply
	(*Request)(nil),      // 3: lens.Request
	(*Reply)(nil),        // 4: lens.Reply
	(*error1.Error)(nil), // 5: error.Error
	(*ack.Ack)(nil),      // 6: ack.Ack
}
var file_lens_proto_depIdxs = []int32{
	0, // 0: lens.Request.set:type_name -> lens.Set_Request
	1, // 1: lens.Request.get:type_name -> lens.Get_Request
	5, // 2: lens.Reply.error:type_name -> error.Error
	6, // 3: lens.Reply.ack:type_name -> ack.Ack
	2, // 4: lens.Reply.get:type_name -> lens.Get_Reply
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_lens_proto_init() }
func file_lens_proto_init() {
	if File_lens_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_lens_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lens_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lens_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lens_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lens_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_lens_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*Request_Set)(nil),
		(*Request_Get)(nil),
	}
	file_lens_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Get)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_lens_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_lens_proto_goTypes,
		DependencyIndexes: file_lens_proto_depIdxs,
		MessageInfos:      file_lens_proto_msgTypes,
	}.Build()
	File_lens_proto = out.File
	file_lens_proto_rawDesc = nil
	file_lens_proto_goTypes = nil
	file_lens_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: hwinfo.proto

package hwinfo

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BoardVersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BoardVersionRequest) Reset() {
	*x = BoardVersionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hwinfo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardVersionRequest) ProtoMessage() {}

func (x *BoardVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hwinfo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardVersionRequest.ProtoReflect.Descriptor instead.
func (*BoardVersionRequest) Descriptor() ([]byte, []int) {
	return file_hwinfo_proto_rawDescGZIP(), []int{0}
}

type BoardIdentityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BoardIdentityRequest) Reset() {
	*x = BoardIdentityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hwinfo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardIdentityRequest) ProtoMessage() {}

func (x *BoardIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hwinfo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardIdentityRequest.ProtoReflect.Descriptor instead.
func (*BoardIdentityRequest) Descriptor() ([]byte, []int) {
	return file_hwinfo_proto_rawDescGZIP(), []int{1}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Version
	//	*Request_Identity
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hwinfo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_hwinfo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_hwinfo_proto_rawDescGZIP(), []int{2}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetVersion() *BoardVersionRequest {
	if x, ok := x.GetRequest().(*Request_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Request) GetIdentity() *BoardIdentityRequest {
	if x, ok := x.GetRequest().(*Request_Identity); ok {
		return x.Identity
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Version struct {
	Version *BoardVersionRequest `protobuf:"bytes,1,opt,name=version,proto3,oneof"`
}

type Request_Identity struct {
	Identity *BoardIdentityRequest `protobuf:"bytes,2,opt,name=identity,proto3,oneof"`
}

func (*Request_Version) isRequest_Request() {}

func (*Request_Identity) isRequest_Request() {}

type BoardVersionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PCB form factor/release (from DTS `compatible`)
	Model string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	// Hardware revision
	Rev uint32 `protobuf:"varint,2,opt,name=rev,proto3" json:"rev,omitempty"`
}

func (x *BoardVersionReply) Reset() {
	*x = BoardVersionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hwinfo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardVersionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardVersionReply) ProtoMessage() {}

func (x *BoardVersionReply) ProtoReflect() protoreflect.Message {
	mi := &file_hwinfo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardVersionReply.ProtoReflect.Descriptor instead.
func (*BoardVersionReply) Descriptor() ([]byte, []int) {
	return file_hwinfo_proto_rawDescGZIP(), []int{3}
}

func (x *BoardVersionReply) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BoardVersionReply) GetRev() uint32 {
	if x != nil {
		return x.Rev
	}
	return 0
}

type BoardIdentityReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CBSN serial number of the board
	Cbsn *string `protobuf:"bytes,1,opt,name=cbsn,proto3,oneof" json:"cbsn,omitempty"`
	// Assembly serial number
	AssySn *string `protobuf:"bytes,2,opt,name=assySn,proto3,oneof" json:"assySn,omitempty"`
}

func (x *BoardIdentityReply) Reset() {
	*x = BoardIdentityReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hwinfo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardIdentityReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardIdentityReply) ProtoMessage() {}

func (x *BoardIdentityReply) ProtoReflect() protoreflect.Message {
	mi := &file_hwinfo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardIdentityReply.ProtoReflect.Descriptor instead.
func (*BoardIdentityReply) Descriptor() ([]byte, []int) {
	return file_hwinfo_proto_rawDescGZIP(), []int{4}
}

func (x *BoardIdentityReply) GetCbsn() string {
	if x != nil && x.Cbsn != nil {
		return *x.Cbsn
	}
	return ""
}

func (x *BoardIdentityReply) GetAssySn() string {
	if x != nil && x.AssySn != nil {
		return *x.AssySn
	}
	return ""
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Version
	//	*Reply_Identity
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hwinfo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_hwinfo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_hwinfo_proto_rawDescGZIP(), []int{5}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetVersion() *BoardVersionReply {
	if x, ok := x.GetReply().(*Reply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Reply) GetIdentity() *BoardIdentityReply {
	if x, ok := x.GetReply().(*Reply_Identity); ok {
		return x.Identity
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Version struct {
	Version *BoardVersionReply `protobuf:"bytes,1,opt,name=version,proto3,oneof"`
}

type Reply_Identity struct {
	Identity *BoardIdentityReply `protobuf:"bytes,2,opt,name=identity,proto3,oneof"`
}

func (*Reply_Version) isReply_Reply() {}

func (*Reply_Identity) isReply_Reply() {}

var File_hwinfo_proto protoreflect.FileDescriptor

var file_hwinfo_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x15, 0x0a, 0x13, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x16, 0x0a,
	0x14, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x89, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x37, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x08, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68,
	0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x3b, 0x0a, 0x11, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03,
	0x72, 0x65, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x72, 0x65, 0x76, 0x22, 0x5e,
	0x0a, 0x12, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x62, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x62, 0x73, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a,
	0x06, 0x61, 0x73, 0x73, 0x79, 0x53, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x06, 0x61, 0x73, 0x73, 0x79, 0x53, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63,
	0x62, 0x73, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x73, 0x73, 0x79, 0x53, 0x6e, 0x22, 0x81,
	0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x77, 0x69, 0x6e,
	0x66, 0x6f, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x38, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x42, 0x0f, 0x5a, 0x0d, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x68, 0x77, 0x69,
	0x6e, 0x66, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hwinfo_proto_rawDescOnce sync.Once
	file_hwinfo_proto_rawDescData = file_hwinfo_proto_rawDesc
)

func file_hwinfo_proto_rawDescGZIP() []byte {
	file_hwinfo_proto_rawDescOnce.Do(func() {
		file_hwinfo_proto_rawDescData = protoimpl.X.CompressGZIP(file_hwinfo_proto_rawDescData)
	})
	return file_hwinfo_proto_rawDescData
}

var file_hwinfo_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_hwinfo_proto_goTypes = []interface{}{
	(*BoardVersionRequest)(nil),  // 0: hwinfo.BoardVersionRequest
	(*BoardIdentityRequest)(nil), // 1: hwinfo.BoardIdentityRequest
	(*Request)(nil),              // 2: hwinfo.Request
	(*BoardVersionReply)(nil),    // 3: hwinfo.BoardVersionReply
	(*BoardIdentityReply)(nil),   // 4: hwinfo.BoardIdentityReply
	(*Reply)(nil),                // 5: hwinfo.Reply
}
var file_hwinfo_proto_depIdxs = []int32{
	0, // 0: hwinfo.Request.version:type_name -> hwinfo.BoardVersionRequest
	1, // 1: hwinfo.Request.identity:type_name -> hwinfo.BoardIdentityRequest
	3, // 2: hwinfo.Reply.version:type_name -> hwinfo.BoardVersionReply
	4, // 3: hwinfo.Reply.identity:type_name -> hwinfo.BoardIdentityReply
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_hwinfo_proto_init() }
func file_hwinfo_proto_init() {
	if File_hwinfo_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hwinfo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardVersionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hwinfo_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardIdentityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hwinfo_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hwinfo_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardVersionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hwinfo_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardIdentityReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hwinfo_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_hwinfo_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*Request_Version)(nil),
		(*Request_Identity)(nil),
	}
	file_hwinfo_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_hwinfo_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Reply_Version)(nil),
		(*Reply_Identity)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hwinfo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_hwinfo_proto_goTypes,
		DependencyIndexes: file_hwinfo_proto_depIdxs,
		MessageInfos:      file_hwinfo_proto_msgTypes,
	}.Build()
	File_hwinfo_proto = out.File
	file_hwinfo_proto_rawDesc = nil
	file_hwinfo_proto_goTypes = nil
	file_hwinfo_proto_depIdxs = nil
}

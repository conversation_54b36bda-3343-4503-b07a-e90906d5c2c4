// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: gimbal.proto

package gimbal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	epos "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/epos"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	servo "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/servo"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Boot_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PanParams  *epos.Home_Params `protobuf:"bytes,1,opt,name=pan_params,json=panParams,proto3" json:"pan_params,omitempty"`
	TiltParams *epos.Home_Params `protobuf:"bytes,2,opt,name=tilt_params,json=tiltParams,proto3" json:"tilt_params,omitempty"`
}

func (x *Boot_Request) Reset() {
	*x = Boot_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Boot_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Boot_Request) ProtoMessage() {}

func (x *Boot_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Boot_Request.ProtoReflect.Descriptor instead.
func (*Boot_Request) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{0}
}

func (x *Boot_Request) GetPanParams() *epos.Home_Params {
	if x != nil {
		return x.PanParams
	}
	return nil
}

func (x *Boot_Request) GetTiltParams() *epos.Home_Params {
	if x != nil {
		return x.TiltParams
	}
	return nil
}

type Stop_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Stop_Request) Reset() {
	*x = Stop_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Stop_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stop_Request) ProtoMessage() {}

func (x *Stop_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stop_Request.ProtoReflect.Descriptor instead.
func (*Stop_Request) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{1}
}

type Servos_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan  *servo.Request `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	Tilt *servo.Request `protobuf:"bytes,2,opt,name=tilt,proto3" json:"tilt,omitempty"`
}

func (x *Servos_Request) Reset() {
	*x = Servos_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Servos_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Servos_Request) ProtoMessage() {}

func (x *Servos_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Servos_Request.ProtoReflect.Descriptor instead.
func (*Servos_Request) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{2}
}

func (x *Servos_Request) GetPan() *servo.Request {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *Servos_Request) GetTilt() *servo.Request {
	if x != nil {
		return x.Tilt
	}
	return nil
}

type GetPositionAtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampUs uint64 `protobuf:"varint,1,opt,name=timestamp_us,json=timestampUs,proto3" json:"timestamp_us,omitempty"`
}

func (x *GetPositionAtRequest) Reset() {
	*x = GetPositionAtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPositionAtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPositionAtRequest) ProtoMessage() {}

func (x *GetPositionAtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPositionAtRequest.ProtoReflect.Descriptor instead.
func (*GetPositionAtRequest) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{3}
}

func (x *GetPositionAtRequest) GetTimestampUs() uint64 {
	if x != nil {
		return x.TimestampUs
	}
	return 0
}

type Servos_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan  *servo.Reply `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	Tilt *servo.Reply `protobuf:"bytes,2,opt,name=tilt,proto3" json:"tilt,omitempty"`
}

func (x *Servos_Reply) Reset() {
	*x = Servos_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Servos_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Servos_Reply) ProtoMessage() {}

func (x *Servos_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Servos_Reply.ProtoReflect.Descriptor instead.
func (*Servos_Reply) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{4}
}

func (x *Servos_Reply) GetPan() *servo.Reply {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *Servos_Reply) GetTilt() *servo.Reply {
	if x != nil {
		return x.Tilt
	}
	return nil
}

type PositionAt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan         int32  `protobuf:"varint,1,opt,name=pan,proto3" json:"pan,omitempty"`
	Tilt        int32  `protobuf:"varint,2,opt,name=tilt,proto3" json:"tilt,omitempty"`
	TimestampUs uint64 `protobuf:"varint,3,opt,name=timestamp_us,json=timestampUs,proto3" json:"timestamp_us,omitempty"`
	Valid       bool   `protobuf:"varint,4,opt,name=valid,proto3" json:"valid,omitempty"`
}

func (x *PositionAt) Reset() {
	*x = PositionAt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionAt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionAt) ProtoMessage() {}

func (x *PositionAt) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionAt.ProtoReflect.Descriptor instead.
func (*PositionAt) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{5}
}

func (x *PositionAt) GetPan() int32 {
	if x != nil {
		return x.Pan
	}
	return 0
}

func (x *PositionAt) GetTilt() int32 {
	if x != nil {
		return x.Tilt
	}
	return 0
}

func (x *PositionAt) GetTimestampUs() uint64 {
	if x != nil {
		return x.TimestampUs
	}
	return 0
}

func (x *PositionAt) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

type GetPositionAtReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Requested *PositionAt `protobuf:"bytes,1,opt,name=requested,proto3" json:"requested,omitempty"`
	Current   *PositionAt `protobuf:"bytes,2,opt,name=current,proto3" json:"current,omitempty"`
}

func (x *GetPositionAtReply) Reset() {
	*x = GetPositionAtReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPositionAtReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPositionAtReply) ProtoMessage() {}

func (x *GetPositionAtReply) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPositionAtReply.ProtoReflect.Descriptor instead.
func (*GetPositionAtReply) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{6}
}

func (x *GetPositionAtReply) GetRequested() *PositionAt {
	if x != nil {
		return x.Requested
	}
	return nil
}

func (x *GetPositionAtReply) GetCurrent() *PositionAt {
	if x != nil {
		return x.Current
	}
	return nil
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Boot
	//	*Request_Stop
	//	*Request_Servos
	//	*Request_Position
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{7}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetBoot() *Boot_Request {
	if x, ok := x.GetRequest().(*Request_Boot); ok {
		return x.Boot
	}
	return nil
}

func (x *Request) GetStop() *Stop_Request {
	if x, ok := x.GetRequest().(*Request_Stop); ok {
		return x.Stop
	}
	return nil
}

func (x *Request) GetServos() *Servos_Request {
	if x, ok := x.GetRequest().(*Request_Servos); ok {
		return x.Servos
	}
	return nil
}

func (x *Request) GetPosition() *GetPositionAtRequest {
	if x, ok := x.GetRequest().(*Request_Position); ok {
		return x.Position
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Boot struct {
	Boot *Boot_Request `protobuf:"bytes,1,opt,name=boot,proto3,oneof"`
}

type Request_Stop struct {
	Stop *Stop_Request `protobuf:"bytes,2,opt,name=stop,proto3,oneof"`
}

type Request_Servos struct {
	Servos *Servos_Request `protobuf:"bytes,3,opt,name=servos,proto3,oneof"`
}

type Request_Position struct {
	Position *GetPositionAtRequest `protobuf:"bytes,4,opt,name=position,proto3,oneof"`
}

func (*Request_Boot) isRequest_Request() {}

func (*Request_Stop) isRequest_Request() {}

func (*Request_Servos) isRequest_Request() {}

func (*Request_Position) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Servos
	//	*Reply_Position
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gimbal_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gimbal_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_gimbal_proto_rawDescGZIP(), []int{8}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetServos() *Servos_Reply {
	if x, ok := x.GetReply().(*Reply_Servos); ok {
		return x.Servos
	}
	return nil
}

func (x *Reply) GetPosition() *GetPositionAtReply {
	if x, ok := x.GetReply().(*Reply_Position); ok {
		return x.Position
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Servos struct {
	Servos *Servos_Reply `protobuf:"bytes,3,opt,name=servos,proto3,oneof"`
}

type Reply_Position struct {
	Position *GetPositionAtReply `protobuf:"bytes,4,opt,name=position,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Servos) isReply_Reply() {}

func (*Reply_Position) isReply_Reply() {}

var File_gimbal_proto protoreflect.FileDescriptor

var file_gimbal_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f,
	0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x74, 0x0a, 0x0c, 0x42, 0x6f, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48,
	0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x65, 0x70, 0x6f,
	0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x5f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0a, 0x74,
	0x69, 0x6c, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x0e, 0x0a, 0x0c, 0x53, 0x74, 0x6f,
	0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x56, 0x0a, 0x0e, 0x53, 0x65, 0x72,
	0x76, 0x6f, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x03, 0x70,
	0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x22, 0x0a,
	0x04, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x6f, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x74, 0x69, 0x6c,
	0x74, 0x22, 0x39, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x55, 0x73, 0x22, 0x50, 0x0a, 0x0c,
	0x53, 0x65, 0x72, 0x76, 0x6f, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x20, 0x0a, 0x04,
	0x74, 0x69, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x04, 0x74, 0x69, 0x6c, 0x74, 0x22, 0x6b,
	0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x69,
	0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x55, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x22, 0x74, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x22, 0xda, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a,
	0x04, 0x62, 0x6f, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x69,
	0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x04, 0x62, 0x6f, 0x6f, 0x74, 0x12, 0x2a, 0x0a, 0x04, 0x73, 0x74, 0x6f,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c,
	0x2e, 0x53, 0x74, 0x6f, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x04, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x6f, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x06, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x69, 0x6d, 0x62,
	0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xbe,
	0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c,
	0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63,
	0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x2e, 0x0a, 0x06,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x73, 0x5f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x73, 0x12, 0x38, 0x0a, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42,
	0x0f, 0x5a, 0x0d, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gimbal_proto_rawDescOnce sync.Once
	file_gimbal_proto_rawDescData = file_gimbal_proto_rawDesc
)

func file_gimbal_proto_rawDescGZIP() []byte {
	file_gimbal_proto_rawDescOnce.Do(func() {
		file_gimbal_proto_rawDescData = protoimpl.X.CompressGZIP(file_gimbal_proto_rawDescData)
	})
	return file_gimbal_proto_rawDescData
}

var file_gimbal_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_gimbal_proto_goTypes = []interface{}{
	(*Boot_Request)(nil),         // 0: gimbal.Boot_Request
	(*Stop_Request)(nil),         // 1: gimbal.Stop_Request
	(*Servos_Request)(nil),       // 2: gimbal.Servos_Request
	(*GetPositionAtRequest)(nil), // 3: gimbal.GetPositionAtRequest
	(*Servos_Reply)(nil),         // 4: gimbal.Servos_Reply
	(*PositionAt)(nil),           // 5: gimbal.PositionAt
	(*GetPositionAtReply)(nil),   // 6: gimbal.GetPositionAtReply
	(*Request)(nil),              // 7: gimbal.Request
	(*Reply)(nil),                // 8: gimbal.Reply
	(*epos.Home_Params)(nil),     // 9: epos.Home_Params
	(*servo.Request)(nil),        // 10: servo.Request
	(*servo.Reply)(nil),          // 11: servo.Reply
	(*error1.Error)(nil),         // 12: error.Error
	(*ack.Ack)(nil),              // 13: ack.Ack
}
var file_gimbal_proto_depIdxs = []int32{
	9,  // 0: gimbal.Boot_Request.pan_params:type_name -> epos.Home_Params
	9,  // 1: gimbal.Boot_Request.tilt_params:type_name -> epos.Home_Params
	10, // 2: gimbal.Servos_Request.pan:type_name -> servo.Request
	10, // 3: gimbal.Servos_Request.tilt:type_name -> servo.Request
	11, // 4: gimbal.Servos_Reply.pan:type_name -> servo.Reply
	11, // 5: gimbal.Servos_Reply.tilt:type_name -> servo.Reply
	5,  // 6: gimbal.GetPositionAtReply.requested:type_name -> gimbal.PositionAt
	5,  // 7: gimbal.GetPositionAtReply.current:type_name -> gimbal.PositionAt
	0,  // 8: gimbal.Request.boot:type_name -> gimbal.Boot_Request
	1,  // 9: gimbal.Request.stop:type_name -> gimbal.Stop_Request
	2,  // 10: gimbal.Request.servos:type_name -> gimbal.Servos_Request
	3,  // 11: gimbal.Request.position:type_name -> gimbal.GetPositionAtRequest
	12, // 12: gimbal.Reply.error:type_name -> error.Error
	13, // 13: gimbal.Reply.ack:type_name -> ack.Ack
	4,  // 14: gimbal.Reply.servos:type_name -> gimbal.Servos_Reply
	6,  // 15: gimbal.Reply.position:type_name -> gimbal.GetPositionAtReply
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_gimbal_proto_init() }
func file_gimbal_proto_init() {
	if File_gimbal_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gimbal_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Boot_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Stop_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Servos_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPositionAtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Servos_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionAt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPositionAtReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gimbal_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_gimbal_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*Request_Boot)(nil),
		(*Request_Stop)(nil),
		(*Request_Servos)(nil),
		(*Request_Position)(nil),
	}
	file_gimbal_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Servos)(nil),
		(*Reply_Position)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gimbal_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gimbal_proto_goTypes,
		DependencyIndexes: file_gimbal_proto_depIdxs,
		MessageInfos:      file_gimbal_proto_msgTypes,
	}.Build()
	File_gimbal_proto = out.File
	file_gimbal_proto_rawDesc = nil
	file_gimbal_proto_goTypes = nil
	file_gimbal_proto_depIdxs = nil
}

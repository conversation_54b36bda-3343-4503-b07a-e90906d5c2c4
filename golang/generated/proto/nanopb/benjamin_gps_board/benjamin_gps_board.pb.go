// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: benjamin_gps_board.proto

package benjamin_gps_board

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	gps "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/gps"
	heading "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/heading"
	hwinfo "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/hwinfo"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PtpConfig_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Set to operate as PTP grandmaster, otherwise PTP slave
	IsMaster bool `protobuf:"varint,1,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
}

func (x *PtpConfig_Request) Reset() {
	*x = PtpConfig_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_benjamin_gps_board_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PtpConfig_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PtpConfig_Request) ProtoMessage() {}

func (x *PtpConfig_Request) ProtoReflect() protoreflect.Message {
	mi := &file_benjamin_gps_board_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PtpConfig_Request.ProtoReflect.Descriptor instead.
func (*PtpConfig_Request) Descriptor() ([]byte, []int) {
	return file_benjamin_gps_board_proto_rawDescGZIP(), []int{0}
}

func (x *PtpConfig_Request) GetIsMaster() bool {
	if x != nil {
		return x.IsMaster
	}
	return false
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_Gps
	//	*Reply_Heading
	//	*Reply_Hwinfo
	//	*Reply_Error
	//	*Reply_Ack
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_benjamin_gps_board_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_benjamin_gps_board_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_benjamin_gps_board_proto_rawDescGZIP(), []int{1}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetGps() *gps.Reply {
	if x, ok := x.GetReply().(*Reply_Gps); ok {
		return x.Gps
	}
	return nil
}

func (x *Reply) GetHeading() *heading.Reply {
	if x, ok := x.GetReply().(*Reply_Heading); ok {
		return x.Heading
	}
	return nil
}

func (x *Reply) GetHwinfo() *hwinfo.Reply {
	if x, ok := x.GetReply().(*Reply_Hwinfo); ok {
		return x.Hwinfo
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_Gps struct {
	Gps *gps.Reply `protobuf:"bytes,3,opt,name=gps,proto3,oneof"`
}

type Reply_Heading struct {
	Heading *heading.Reply `protobuf:"bytes,4,opt,name=heading,proto3,oneof"`
}

type Reply_Hwinfo struct {
	Hwinfo *hwinfo.Reply `protobuf:"bytes,5,opt,name=hwinfo,proto3,oneof"`
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,6,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,7,opt,name=ack,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_Gps) isReply_Reply() {}

func (*Reply_Heading) isReply_Reply() {}

func (*Reply_Hwinfo) isReply_Reply() {}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_Gps
	//	*Request_Heading
	//	*Request_Hwinfo
	//	*Request_PtpConfig
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_benjamin_gps_board_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_benjamin_gps_board_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_benjamin_gps_board_proto_rawDescGZIP(), []int{2}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetGps() *gps.Request {
	if x, ok := x.GetRequest().(*Request_Gps); ok {
		return x.Gps
	}
	return nil
}

func (x *Request) GetHeading() *heading.Request {
	if x, ok := x.GetRequest().(*Request_Heading); ok {
		return x.Heading
	}
	return nil
}

func (x *Request) GetHwinfo() *hwinfo.Request {
	if x, ok := x.GetRequest().(*Request_Hwinfo); ok {
		return x.Hwinfo
	}
	return nil
}

func (x *Request) GetPtpConfig() *PtpConfig_Request {
	if x, ok := x.GetRequest().(*Request_PtpConfig); ok {
		return x.PtpConfig
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_Gps struct {
	Gps *gps.Request `protobuf:"bytes,3,opt,name=gps,proto3,oneof"`
}

type Request_Heading struct {
	Heading *heading.Request `protobuf:"bytes,4,opt,name=heading,proto3,oneof"`
}

type Request_Hwinfo struct {
	Hwinfo *hwinfo.Request `protobuf:"bytes,5,opt,name=hwinfo,proto3,oneof"`
}

type Request_PtpConfig struct {
	PtpConfig *PtpConfig_Request `protobuf:"bytes,6,opt,name=ptp_config,json=ptpConfig,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_Gps) isRequest_Request() {}

func (*Request_Heading) isRequest_Request() {}

func (*Request_Hwinfo) isRequest_Request() {}

func (*Request_PtpConfig) isRequest_Request() {}

var File_benjamin_gps_board_proto protoreflect.FileDescriptor

var file_benjamin_gps_board_proto_rawDesc = []byte{
	0x0a, 0x18, 0x62, 0x65, 0x6e, 0x6a, 0x61, 0x6d, 0x69, 0x6e, 0x5f, 0x67, 0x70, 0x73, 0x5f, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x62, 0x65, 0x6e, 0x6a,
	0x61, 0x6d, 0x69, 0x6e, 0x5f, 0x67, 0x70, 0x73, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x2c,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x70, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a, 0x11, 0x50, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x22, 0xa1, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x26, 0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67,
	0x48, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x03, 0x67, 0x70, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x03, 0x67, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63,
	0x6b, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xaf, 0x02, 0x0a, 0x07, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x20,
	0x0a, 0x03, 0x67, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x67, 0x70,
	0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x67, 0x70, 0x73,
	0x12, 0x2c, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x29,
	0x0a, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x74, 0x70,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x62, 0x65, 0x6e, 0x6a, 0x61, 0x6d, 0x69, 0x6e, 0x5f, 0x67, 0x70, 0x73, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2e, 0x50, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x70, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x1b, 0x5a, 0x19,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x62, 0x65, 0x6e, 0x6a, 0x61, 0x6d, 0x69, 0x6e, 0x5f,
	0x67, 0x70, 0x73, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_benjamin_gps_board_proto_rawDescOnce sync.Once
	file_benjamin_gps_board_proto_rawDescData = file_benjamin_gps_board_proto_rawDesc
)

func file_benjamin_gps_board_proto_rawDescGZIP() []byte {
	file_benjamin_gps_board_proto_rawDescOnce.Do(func() {
		file_benjamin_gps_board_proto_rawDescData = protoimpl.X.CompressGZIP(file_benjamin_gps_board_proto_rawDescData)
	})
	return file_benjamin_gps_board_proto_rawDescData
}

var file_benjamin_gps_board_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_benjamin_gps_board_proto_goTypes = []interface{}{
	(*PtpConfig_Request)(nil),     // 0: benjamin_gps_board.PtpConfig_Request
	(*Reply)(nil),                 // 1: benjamin_gps_board.Reply
	(*Request)(nil),               // 2: benjamin_gps_board.Request
	(*request.RequestHeader)(nil), // 3: request.RequestHeader
	(*diagnostic.Pong)(nil),       // 4: diagnostic.Pong
	(*gps.Reply)(nil),             // 5: gps.Reply
	(*heading.Reply)(nil),         // 6: heading.Reply
	(*hwinfo.Reply)(nil),          // 7: hwinfo.Reply
	(*error1.Error)(nil),          // 8: error.Error
	(*ack.Ack)(nil),               // 9: ack.Ack
	(*diagnostic.Ping)(nil),       // 10: diagnostic.Ping
	(*gps.Request)(nil),           // 11: gps.Request
	(*heading.Request)(nil),       // 12: heading.Request
	(*hwinfo.Request)(nil),        // 13: hwinfo.Request
}
var file_benjamin_gps_board_proto_depIdxs = []int32{
	3,  // 0: benjamin_gps_board.Reply.header:type_name -> request.RequestHeader
	4,  // 1: benjamin_gps_board.Reply.pong:type_name -> diagnostic.Pong
	5,  // 2: benjamin_gps_board.Reply.gps:type_name -> gps.Reply
	6,  // 3: benjamin_gps_board.Reply.heading:type_name -> heading.Reply
	7,  // 4: benjamin_gps_board.Reply.hwinfo:type_name -> hwinfo.Reply
	8,  // 5: benjamin_gps_board.Reply.error:type_name -> error.Error
	9,  // 6: benjamin_gps_board.Reply.ack:type_name -> ack.Ack
	3,  // 7: benjamin_gps_board.Request.header:type_name -> request.RequestHeader
	10, // 8: benjamin_gps_board.Request.ping:type_name -> diagnostic.Ping
	11, // 9: benjamin_gps_board.Request.gps:type_name -> gps.Request
	12, // 10: benjamin_gps_board.Request.heading:type_name -> heading.Request
	13, // 11: benjamin_gps_board.Request.hwinfo:type_name -> hwinfo.Request
	0,  // 12: benjamin_gps_board.Request.ptp_config:type_name -> benjamin_gps_board.PtpConfig_Request
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_benjamin_gps_board_proto_init() }
func file_benjamin_gps_board_proto_init() {
	if File_benjamin_gps_board_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_benjamin_gps_board_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PtpConfig_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_benjamin_gps_board_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_benjamin_gps_board_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_benjamin_gps_board_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_Gps)(nil),
		(*Reply_Heading)(nil),
		(*Reply_Hwinfo)(nil),
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
	}
	file_benjamin_gps_board_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_Gps)(nil),
		(*Request_Heading)(nil),
		(*Request_Hwinfo)(nil),
		(*Request_PtpConfig)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_benjamin_gps_board_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_benjamin_gps_board_proto_goTypes,
		DependencyIndexes: file_benjamin_gps_board_proto_depIdxs,
		MessageInfos:      file_benjamin_gps_board_proto_msgTypes,
	}.Build()
	File_benjamin_gps_board_proto = out.File
	file_benjamin_gps_board_proto_rawDesc = nil
	file_benjamin_gps_board_proto_goTypes = nil
	file_benjamin_gps_board_proto_depIdxs = nil
}

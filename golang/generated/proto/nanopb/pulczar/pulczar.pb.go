// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: pulczar.proto

package pulczar

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	arc_detector "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/arc_detector"
	dawg "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/dawg"
	gimbal "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/gimbal"
	laser "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/laser"
	lens "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/lens"
	scanner_config "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/scanner_config"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Ambient temperature reporting for FHs
type AmbientTempConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled       bool     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	UseThermistor bool     `protobuf:"varint,2,opt,name=use_thermistor,json=useThermistor,proto3" json:"use_thermistor,omitempty"`
	Temp          *float32 `protobuf:"fixed32,3,opt,name=temp,proto3,oneof" json:"temp,omitempty"`
}

func (x *AmbientTempConfig) Reset() {
	*x = AmbientTempConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmbientTempConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmbientTempConfig) ProtoMessage() {}

func (x *AmbientTempConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmbientTempConfig.ProtoReflect.Descriptor instead.
func (*AmbientTempConfig) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{0}
}

func (x *AmbientTempConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AmbientTempConfig) GetUseThermistor() bool {
	if x != nil {
		return x.UseThermistor
	}
	return false
}

func (x *AmbientTempConfig) GetTemp() float32 {
	if x != nil && x.Temp != nil {
		return *x.Temp
	}
	return 0
}

type AmbientTempGetStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AmbientTempGetStateRequest) Reset() {
	*x = AmbientTempGetStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmbientTempGetStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmbientTempGetStateRequest) ProtoMessage() {}

func (x *AmbientTempGetStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmbientTempGetStateRequest.ProtoReflect.Descriptor instead.
func (*AmbientTempGetStateRequest) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{1}
}

type AmbientTempRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*AmbientTempRequest_SetConfig
	//	*AmbientTempRequest_GetConfig
	Request isAmbientTempRequest_Request `protobuf_oneof:"request"`
}

func (x *AmbientTempRequest) Reset() {
	*x = AmbientTempRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmbientTempRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmbientTempRequest) ProtoMessage() {}

func (x *AmbientTempRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmbientTempRequest.ProtoReflect.Descriptor instead.
func (*AmbientTempRequest) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{2}
}

func (m *AmbientTempRequest) GetRequest() isAmbientTempRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *AmbientTempRequest) GetSetConfig() *AmbientTempConfig {
	if x, ok := x.GetRequest().(*AmbientTempRequest_SetConfig); ok {
		return x.SetConfig
	}
	return nil
}

func (x *AmbientTempRequest) GetGetConfig() *AmbientTempGetStateRequest {
	if x, ok := x.GetRequest().(*AmbientTempRequest_GetConfig); ok {
		return x.GetConfig
	}
	return nil
}

type isAmbientTempRequest_Request interface {
	isAmbientTempRequest_Request()
}

type AmbientTempRequest_SetConfig struct {
	SetConfig *AmbientTempConfig `protobuf:"bytes,1,opt,name=set_config,json=setConfig,proto3,oneof"`
}

type AmbientTempRequest_GetConfig struct {
	GetConfig *AmbientTempGetStateRequest `protobuf:"bytes,2,opt,name=get_config,json=getConfig,proto3,oneof"`
}

func (*AmbientTempRequest_SetConfig) isAmbientTempRequest_Request() {}

func (*AmbientTempRequest_GetConfig) isAmbientTempRequest_Request() {}

type AmbientTempReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// temperature used for ambient reporting (if have sensors)
	SenseTemp float32 `protobuf:"fixed32,1,opt,name=sense_temp,json=senseTemp,proto3" json:"sense_temp,omitempty"`
	// the actual configuration currently applied
	Config *AmbientTempConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *AmbientTempReply) Reset() {
	*x = AmbientTempReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmbientTempReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmbientTempReply) ProtoMessage() {}

func (x *AmbientTempReply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmbientTempReply.ProtoReflect.Descriptor instead.
func (*AmbientTempReply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{3}
}

func (x *AmbientTempReply) GetSenseTemp() float32 {
	if x != nil {
		return x.SenseTemp
	}
	return 0
}

func (x *AmbientTempReply) GetConfig() *AmbientTempConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

// Messages for hardware status
type HwStatus_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HwStatus_Request) Reset() {
	*x = HwStatus_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HwStatus_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HwStatus_Request) ProtoMessage() {}

func (x *HwStatus_Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HwStatus_Request.ProtoReflect.Descriptor instead.
func (*HwStatus_Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{4}
}

// TODO: what subset of this is available for EPOS controllers?
type HwStatus_Servo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Connected    bool   `protobuf:"varint,1,opt,name=connected,proto3" json:"connected,omitempty"`
	ControllerSn uint32 `protobuf:"varint,2,opt,name=controller_sn,json=controllerSn,proto3" json:"controller_sn,omitempty"`
	// timestamp for all of these readings
	SensorTimeMs     uint64  `protobuf:"varint,3,opt,name=sensor_time_ms,json=sensorTimeMs,proto3" json:"sensor_time_ms,omitempty"`
	OutputStageTempC float32 `protobuf:"fixed32,4,opt,name=output_stage_temp_c,json=outputStageTempC,proto3" json:"output_stage_temp_c,omitempty"`
	MotorSupplyV     float32 `protobuf:"fixed32,5,opt,name=motor_supply_v,json=motorSupplyV,proto3" json:"motor_supply_v,omitempty"`
	MotorCurrentA    float32 `protobuf:"fixed32,6,opt,name=motor_current_a,json=motorCurrentA,proto3" json:"motor_current_a,omitempty"`
	// timestamp for encoder
	EncoderTimeMs uint64 `protobuf:"varint,7,opt,name=encoder_time_ms,json=encoderTimeMs,proto3" json:"encoder_time_ms,omitempty"`
	EncoderTicks  int64  `protobuf:"varint,8,opt,name=encoder_ticks,json=encoderTicks,proto3" json:"encoder_ticks,omitempty"`
}

func (x *HwStatus_Servo) Reset() {
	*x = HwStatus_Servo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HwStatus_Servo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HwStatus_Servo) ProtoMessage() {}

func (x *HwStatus_Servo) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HwStatus_Servo.ProtoReflect.Descriptor instead.
func (*HwStatus_Servo) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{5}
}

func (x *HwStatus_Servo) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *HwStatus_Servo) GetControllerSn() uint32 {
	if x != nil {
		return x.ControllerSn
	}
	return 0
}

func (x *HwStatus_Servo) GetSensorTimeMs() uint64 {
	if x != nil {
		return x.SensorTimeMs
	}
	return 0
}

func (x *HwStatus_Servo) GetOutputStageTempC() float32 {
	if x != nil {
		return x.OutputStageTempC
	}
	return 0
}

func (x *HwStatus_Servo) GetMotorSupplyV() float32 {
	if x != nil {
		return x.MotorSupplyV
	}
	return 0
}

func (x *HwStatus_Servo) GetMotorCurrentA() float32 {
	if x != nil {
		return x.MotorCurrentA
	}
	return 0
}

func (x *HwStatus_Servo) GetEncoderTimeMs() uint64 {
	if x != nil {
		return x.EncoderTimeMs
	}
	return 0
}

func (x *HwStatus_Servo) GetEncoderTicks() int64 {
	if x != nil {
		return x.EncoderTicks
	}
	return 0
}

type HwStatus_Slayer_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Thermistor readings in beam combiner path
	LpmThermistorBeamRawMv float32 `protobuf:"fixed32,1,opt,name=lpm_thermistor_beam_raw_mv,json=lpmThermistorBeamRawMv,proto3" json:"lpm_thermistor_beam_raw_mv,omitempty"`
	LpmThermistorBeamTempC float32 `protobuf:"fixed32,2,opt,name=lpm_thermistor_beam_temp_c,json=lpmThermistorBeamTempC,proto3" json:"lpm_thermistor_beam_temp_c,omitempty"`
	// Adjacent thermistor, reading the ambient temperature
	LpmThermistorAmbientRawMv float32 `protobuf:"fixed32,3,opt,name=lpm_thermistor_ambient_raw_mv,json=lpmThermistorAmbientRawMv,proto3" json:"lpm_thermistor_ambient_raw_mv,omitempty"`
	LpmThermistorAmbientTempC float32 `protobuf:"fixed32,4,opt,name=lpm_thermistor_ambient_temp_c,json=lpmThermistorAmbientTempC,proto3" json:"lpm_thermistor_ambient_temp_c,omitempty"`
	// LPSU status and current feedback
	LpsuStatus    bool    `protobuf:"varint,5,opt,name=lpsu_status,json=lpsuStatus,proto3" json:"lpsu_status,omitempty"`
	LpsuCurrentMa float32 `protobuf:"fixed32,6,opt,name=lpsu_current_ma,json=lpsuCurrentMa,proto3" json:"lpsu_current_ma,omitempty"`
	// status for pan and tilt servos
	ServoPan  *HwStatus_Servo `protobuf:"bytes,7,opt,name=servo_pan,json=servoPan,proto3,oneof" json:"servo_pan,omitempty"`
	ServoTilt *HwStatus_Servo `protobuf:"bytes,8,opt,name=servo_tilt,json=servoTilt,proto3,oneof" json:"servo_tilt,omitempty"`
	// switched target cam power state
	TargetCamPowerOn bool `protobuf:"varint,9,opt,name=target_cam_power_on,json=targetCamPowerOn,proto3" json:"target_cam_power_on,omitempty"`
}

func (x *HwStatus_Slayer_Reply) Reset() {
	*x = HwStatus_Slayer_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HwStatus_Slayer_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HwStatus_Slayer_Reply) ProtoMessage() {}

func (x *HwStatus_Slayer_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HwStatus_Slayer_Reply.ProtoReflect.Descriptor instead.
func (*HwStatus_Slayer_Reply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{6}
}

func (x *HwStatus_Slayer_Reply) GetLpmThermistorBeamRawMv() float32 {
	if x != nil {
		return x.LpmThermistorBeamRawMv
	}
	return 0
}

func (x *HwStatus_Slayer_Reply) GetLpmThermistorBeamTempC() float32 {
	if x != nil {
		return x.LpmThermistorBeamTempC
	}
	return 0
}

func (x *HwStatus_Slayer_Reply) GetLpmThermistorAmbientRawMv() float32 {
	if x != nil {
		return x.LpmThermistorAmbientRawMv
	}
	return 0
}

func (x *HwStatus_Slayer_Reply) GetLpmThermistorAmbientTempC() float32 {
	if x != nil {
		return x.LpmThermistorAmbientTempC
	}
	return 0
}

func (x *HwStatus_Slayer_Reply) GetLpsuStatus() bool {
	if x != nil {
		return x.LpsuStatus
	}
	return false
}

func (x *HwStatus_Slayer_Reply) GetLpsuCurrentMa() float32 {
	if x != nil {
		return x.LpsuCurrentMa
	}
	return 0
}

func (x *HwStatus_Slayer_Reply) GetServoPan() *HwStatus_Servo {
	if x != nil {
		return x.ServoPan
	}
	return nil
}

func (x *HwStatus_Slayer_Reply) GetServoTilt() *HwStatus_Servo {
	if x != nil {
		return x.ServoTilt
	}
	return nil
}

func (x *HwStatus_Slayer_Reply) GetTargetCamPowerOn() bool {
	if x != nil {
		return x.TargetCamPowerOn
	}
	return false
}

type HwStatus_Reaper_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Diode laser status
	LaserConnected bool                         `protobuf:"varint,1,opt,name=laser_connected,json=laserConnected,proto3" json:"laser_connected,omitempty"`
	LaserInventory *laser.Laser_Inventory_Reply `protobuf:"bytes,2,opt,name=laser_inventory,json=laserInventory,proto3" json:"laser_inventory,omitempty"`
	LaserStatus    *laser.Diode_Status_Reply    `protobuf:"bytes,3,opt,name=laser_status,json=laserStatus,proto3" json:"laser_status,omitempty"`
	// Laser thermistors and power meter
	LaserThermTempC []float32 `protobuf:"fixed32,4,rep,packed,name=laser_therm_temp_c,json=laserThermTempC,proto3" json:"laser_therm_temp_c,omitempty"`
	LaserPowerW     float32   `protobuf:"fixed32,5,opt,name=laser_power_w,json=laserPowerW,proto3" json:"laser_power_w,omitempty"`
	LaserPowerRawMv float32   `protobuf:"fixed32,6,opt,name=laser_power_raw_mv,json=laserPowerRawMv,proto3" json:"laser_power_raw_mv,omitempty"`
	// status for pan and tilt servos
	ServoPan  *HwStatus_Servo `protobuf:"bytes,7,opt,name=servo_pan,json=servoPan,proto3,oneof" json:"servo_pan,omitempty"`
	ServoTilt *HwStatus_Servo `protobuf:"bytes,8,opt,name=servo_tilt,json=servoTilt,proto3,oneof" json:"servo_tilt,omitempty"`
	// switched target cam power state
	TargetCamPowerOn bool `protobuf:"varint,9,opt,name=target_cam_power_on,json=targetCamPowerOn,proto3" json:"target_cam_power_on,omitempty"`
}

func (x *HwStatus_Reaper_Reply) Reset() {
	*x = HwStatus_Reaper_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HwStatus_Reaper_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HwStatus_Reaper_Reply) ProtoMessage() {}

func (x *HwStatus_Reaper_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HwStatus_Reaper_Reply.ProtoReflect.Descriptor instead.
func (*HwStatus_Reaper_Reply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{7}
}

func (x *HwStatus_Reaper_Reply) GetLaserConnected() bool {
	if x != nil {
		return x.LaserConnected
	}
	return false
}

func (x *HwStatus_Reaper_Reply) GetLaserInventory() *laser.Laser_Inventory_Reply {
	if x != nil {
		return x.LaserInventory
	}
	return nil
}

func (x *HwStatus_Reaper_Reply) GetLaserStatus() *laser.Diode_Status_Reply {
	if x != nil {
		return x.LaserStatus
	}
	return nil
}

func (x *HwStatus_Reaper_Reply) GetLaserThermTempC() []float32 {
	if x != nil {
		return x.LaserThermTempC
	}
	return nil
}

func (x *HwStatus_Reaper_Reply) GetLaserPowerW() float32 {
	if x != nil {
		return x.LaserPowerW
	}
	return 0
}

func (x *HwStatus_Reaper_Reply) GetLaserPowerRawMv() float32 {
	if x != nil {
		return x.LaserPowerRawMv
	}
	return 0
}

func (x *HwStatus_Reaper_Reply) GetServoPan() *HwStatus_Servo {
	if x != nil {
		return x.ServoPan
	}
	return nil
}

func (x *HwStatus_Reaper_Reply) GetServoTilt() *HwStatus_Servo {
	if x != nil {
		return x.ServoTilt
	}
	return nil
}

func (x *HwStatus_Reaper_Reply) GetTargetCamPowerOn() bool {
	if x != nil {
		return x.TargetCamPowerOn
	}
	return false
}

type HwStatus_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*HwStatus_Reply_Slayer
	//	*HwStatus_Reply_Reaper
	Reply isHwStatus_Reply_Reply `protobuf_oneof:"reply"`
}

func (x *HwStatus_Reply) Reset() {
	*x = HwStatus_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HwStatus_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HwStatus_Reply) ProtoMessage() {}

func (x *HwStatus_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HwStatus_Reply.ProtoReflect.Descriptor instead.
func (*HwStatus_Reply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{8}
}

func (m *HwStatus_Reply) GetReply() isHwStatus_Reply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *HwStatus_Reply) GetSlayer() *HwStatus_Slayer_Reply {
	if x, ok := x.GetReply().(*HwStatus_Reply_Slayer); ok {
		return x.Slayer
	}
	return nil
}

func (x *HwStatus_Reply) GetReaper() *HwStatus_Reaper_Reply {
	if x, ok := x.GetReply().(*HwStatus_Reply_Reaper); ok {
		return x.Reaper
	}
	return nil
}

type isHwStatus_Reply_Reply interface {
	isHwStatus_Reply_Reply()
}

type HwStatus_Reply_Slayer struct {
	Slayer *HwStatus_Slayer_Reply `protobuf:"bytes,1,opt,name=slayer,proto3,oneof"`
}

type HwStatus_Reply_Reaper struct {
	Reaper *HwStatus_Reaper_Reply `protobuf:"bytes,2,opt,name=reaper,proto3,oneof"`
}

func (*HwStatus_Reply_Slayer) isHwStatus_Reply_Reply() {}

func (*HwStatus_Reply_Reaper) isHwStatus_Reply_Reply() {}

type Reset_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Reset_Request) Reset() {
	*x = Reset_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reset_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reset_Request) ProtoMessage() {}

func (x *Reset_Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reset_Request.ProtoReflect.Descriptor instead.
func (*Reset_Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{9}
}

type Clear_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Clear_Config_Request) Reset() {
	*x = Clear_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Clear_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Clear_Config_Request) ProtoMessage() {}

func (x *Clear_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Clear_Config_Request.ProtoReflect.Descriptor instead.
func (*Clear_Config_Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{10}
}

type Status_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Status_Request) Reset() {
	*x = Status_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status_Request) ProtoMessage() {}

func (x *Status_Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status_Request.ProtoReflect.Descriptor instead.
func (*Status_Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{11}
}

type Override_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Override uint32 `protobuf:"varint,1,opt,name=override,proto3" json:"override,omitempty"`
}

func (x *Override_Request) Reset() {
	*x = Override_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Override_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Override_Request) ProtoMessage() {}

func (x *Override_Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Override_Request.ProtoReflect.Descriptor instead.
func (*Override_Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{12}
}

func (x *Override_Request) GetOverride() uint32 {
	if x != nil {
		return x.Override
	}
	return 0
}

type Power_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetCam   *bool `protobuf:"varint,1,opt,name=targetCam,proto3,oneof" json:"targetCam,omitempty"`
	FiringBoard *bool `protobuf:"varint,2,opt,name=firingBoard,proto3,oneof" json:"firingBoard,omitempty"`
}

func (x *Power_Request) Reset() {
	*x = Power_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Power_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Power_Request) ProtoMessage() {}

func (x *Power_Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Power_Request.ProtoReflect.Descriptor instead.
func (*Power_Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{13}
}

func (x *Power_Request) GetTargetCam() bool {
	if x != nil && x.TargetCam != nil {
		return *x.TargetCam
	}
	return false
}

func (x *Power_Request) GetFiringBoard() bool {
	if x != nil && x.FiringBoard != nil {
		return *x.FiringBoard
	}
	return false
}

type Status_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      uint32                    `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	LaserStatus *laser.Laser_Status_Reply `protobuf:"bytes,2,opt,name=laser_status,json=laserStatus,proto3" json:"laser_status,omitempty"`
}

func (x *Status_Reply) Reset() {
	*x = Status_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status_Reply) ProtoMessage() {}

func (x *Status_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status_Reply.ProtoReflect.Descriptor instead.
func (*Status_Reply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{14}
}

func (x *Status_Reply) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Status_Reply) GetLaserStatus() *laser.Laser_Status_Reply {
	if x != nil {
		return x.LaserStatus
	}
	return nil
}

type Power_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetCam   bool `protobuf:"varint,1,opt,name=targetCam,proto3" json:"targetCam,omitempty"`
	FiringBoard bool `protobuf:"varint,2,opt,name=firingBoard,proto3" json:"firingBoard,omitempty"`
}

func (x *Power_Reply) Reset() {
	*x = Power_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Power_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Power_Reply) ProtoMessage() {}

func (x *Power_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Power_Reply.ProtoReflect.Descriptor instead.
func (*Power_Reply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{15}
}

func (x *Power_Reply) GetTargetCam() bool {
	if x != nil {
		return x.TargetCam
	}
	return false
}

func (x *Power_Reply) GetFiringBoard() bool {
	if x != nil {
		return x.FiringBoard
	}
	return false
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Reset_
	//	*Request_Clear
	//	*Request_Gimbal
	//	*Request_Dawg
	//	*Request_Laser
	//	*Request_Status
	//	*Request_Lens
	//	*Request_Override
	//	*Request_Conf
	//	*Request_Arc
	//	*Request_Power
	//	*Request_HwStatus
	//	*Request_AmbientTemp
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{16}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetReset_() *Reset_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *Request) GetClear() *Clear_Config_Request {
	if x, ok := x.GetRequest().(*Request_Clear); ok {
		return x.Clear
	}
	return nil
}

func (x *Request) GetGimbal() *gimbal.Request {
	if x, ok := x.GetRequest().(*Request_Gimbal); ok {
		return x.Gimbal
	}
	return nil
}

func (x *Request) GetDawg() *dawg.Request {
	if x, ok := x.GetRequest().(*Request_Dawg); ok {
		return x.Dawg
	}
	return nil
}

func (x *Request) GetLaser() *laser.Request {
	if x, ok := x.GetRequest().(*Request_Laser); ok {
		return x.Laser
	}
	return nil
}

func (x *Request) GetStatus() *Status_Request {
	if x, ok := x.GetRequest().(*Request_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Request) GetLens() *lens.Request {
	if x, ok := x.GetRequest().(*Request_Lens); ok {
		return x.Lens
	}
	return nil
}

func (x *Request) GetOverride() *Override_Request {
	if x, ok := x.GetRequest().(*Request_Override); ok {
		return x.Override
	}
	return nil
}

func (x *Request) GetConf() *scanner_config.Request {
	if x, ok := x.GetRequest().(*Request_Conf); ok {
		return x.Conf
	}
	return nil
}

func (x *Request) GetArc() *arc_detector.Request {
	if x, ok := x.GetRequest().(*Request_Arc); ok {
		return x.Arc
	}
	return nil
}

func (x *Request) GetPower() *Power_Request {
	if x, ok := x.GetRequest().(*Request_Power); ok {
		return x.Power
	}
	return nil
}

func (x *Request) GetHwStatus() *HwStatus_Request {
	if x, ok := x.GetRequest().(*Request_HwStatus); ok {
		return x.HwStatus
	}
	return nil
}

func (x *Request) GetAmbientTemp() *AmbientTempRequest {
	if x, ok := x.GetRequest().(*Request_AmbientTemp); ok {
		return x.AmbientTemp
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Reset_ struct {
	Reset_ *Reset_Request `protobuf:"bytes,1,opt,name=reset,proto3,oneof"`
}

type Request_Clear struct {
	Clear *Clear_Config_Request `protobuf:"bytes,2,opt,name=clear,proto3,oneof"`
}

type Request_Gimbal struct {
	Gimbal *gimbal.Request `protobuf:"bytes,3,opt,name=gimbal,proto3,oneof"`
}

type Request_Dawg struct {
	Dawg *dawg.Request `protobuf:"bytes,4,opt,name=dawg,proto3,oneof"`
}

type Request_Laser struct {
	Laser *laser.Request `protobuf:"bytes,5,opt,name=laser,proto3,oneof"`
}

type Request_Status struct {
	Status *Status_Request `protobuf:"bytes,6,opt,name=status,proto3,oneof"`
}

type Request_Lens struct {
	Lens *lens.Request `protobuf:"bytes,7,opt,name=lens,proto3,oneof"`
}

type Request_Override struct {
	Override *Override_Request `protobuf:"bytes,8,opt,name=override,proto3,oneof"`
}

type Request_Conf struct {
	Conf *scanner_config.Request `protobuf:"bytes,9,opt,name=conf,proto3,oneof"`
}

type Request_Arc struct {
	Arc *arc_detector.Request `protobuf:"bytes,10,opt,name=arc,proto3,oneof"`
}

type Request_Power struct {
	Power *Power_Request `protobuf:"bytes,11,opt,name=power,proto3,oneof"`
}

type Request_HwStatus struct {
	HwStatus *HwStatus_Request `protobuf:"bytes,12,opt,name=hw_status,json=hwStatus,proto3,oneof"`
}

type Request_AmbientTemp struct {
	AmbientTemp *AmbientTempRequest `protobuf:"bytes,13,opt,name=ambient_temp,json=ambientTemp,proto3,oneof"`
}

func (*Request_Reset_) isRequest_Request() {}

func (*Request_Clear) isRequest_Request() {}

func (*Request_Gimbal) isRequest_Request() {}

func (*Request_Dawg) isRequest_Request() {}

func (*Request_Laser) isRequest_Request() {}

func (*Request_Status) isRequest_Request() {}

func (*Request_Lens) isRequest_Request() {}

func (*Request_Override) isRequest_Request() {}

func (*Request_Conf) isRequest_Request() {}

func (*Request_Arc) isRequest_Request() {}

func (*Request_Power) isRequest_Request() {}

func (*Request_HwStatus) isRequest_Request() {}

func (*Request_AmbientTemp) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Ack
	//	*Reply_Gimbal
	//	*Reply_Dawg
	//	*Reply_Laser
	//	*Reply_Status
	//	*Reply_Lens
	//	*Reply_Conf
	//	*Reply_Arc
	//	*Reply_Power
	//	*Reply_HwStatus
	//	*Reply_AmbientTemp
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pulczar_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pulczar_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_pulczar_proto_rawDescGZIP(), []int{17}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetGimbal() *gimbal.Reply {
	if x, ok := x.GetReply().(*Reply_Gimbal); ok {
		return x.Gimbal
	}
	return nil
}

func (x *Reply) GetDawg() *dawg.Reply {
	if x, ok := x.GetReply().(*Reply_Dawg); ok {
		return x.Dawg
	}
	return nil
}

func (x *Reply) GetLaser() *laser.Reply {
	if x, ok := x.GetReply().(*Reply_Laser); ok {
		return x.Laser
	}
	return nil
}

func (x *Reply) GetStatus() *Status_Reply {
	if x, ok := x.GetReply().(*Reply_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Reply) GetLens() *lens.Reply {
	if x, ok := x.GetReply().(*Reply_Lens); ok {
		return x.Lens
	}
	return nil
}

func (x *Reply) GetConf() *scanner_config.Reply {
	if x, ok := x.GetReply().(*Reply_Conf); ok {
		return x.Conf
	}
	return nil
}

func (x *Reply) GetArc() *arc_detector.Reply {
	if x, ok := x.GetReply().(*Reply_Arc); ok {
		return x.Arc
	}
	return nil
}

func (x *Reply) GetPower() *Power_Reply {
	if x, ok := x.GetReply().(*Reply_Power); ok {
		return x.Power
	}
	return nil
}

func (x *Reply) GetHwStatus() *HwStatus_Reply {
	if x, ok := x.GetReply().(*Reply_HwStatus); ok {
		return x.HwStatus
	}
	return nil
}

func (x *Reply) GetAmbientTemp() *AmbientTempReply {
	if x, ok := x.GetReply().(*Reply_AmbientTemp); ok {
		return x.AmbientTemp
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,1,opt,name=ack,proto3,oneof"`
}

type Reply_Gimbal struct {
	Gimbal *gimbal.Reply `protobuf:"bytes,2,opt,name=gimbal,proto3,oneof"`
}

type Reply_Dawg struct {
	Dawg *dawg.Reply `protobuf:"bytes,3,opt,name=dawg,proto3,oneof"`
}

type Reply_Laser struct {
	Laser *laser.Reply `protobuf:"bytes,4,opt,name=laser,proto3,oneof"`
}

type Reply_Status struct {
	Status *Status_Reply `protobuf:"bytes,5,opt,name=status,proto3,oneof"`
}

type Reply_Lens struct {
	Lens *lens.Reply `protobuf:"bytes,6,opt,name=lens,proto3,oneof"`
}

type Reply_Conf struct {
	Conf *scanner_config.Reply `protobuf:"bytes,7,opt,name=conf,proto3,oneof"`
}

type Reply_Arc struct {
	Arc *arc_detector.Reply `protobuf:"bytes,8,opt,name=arc,proto3,oneof"`
}

type Reply_Power struct {
	Power *Power_Reply `protobuf:"bytes,9,opt,name=power,proto3,oneof"`
}

type Reply_HwStatus struct {
	HwStatus *HwStatus_Reply `protobuf:"bytes,10,opt,name=hw_status,json=hwStatus,proto3,oneof"`
}

type Reply_AmbientTemp struct {
	AmbientTemp *AmbientTempReply `protobuf:"bytes,11,opt,name=ambient_temp,json=ambientTemp,proto3,oneof"`
}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Gimbal) isReply_Reply() {}

func (*Reply_Dawg) isReply_Reply() {}

func (*Reply_Laser) isReply_Reply() {}

func (*Reply_Status) isReply_Reply() {}

func (*Reply_Lens) isReply_Reply() {}

func (*Reply_Conf) isReply_Reply() {}

func (*Reply_Arc) isReply_Reply() {}

func (*Reply_Power) isReply_Reply() {}

func (*Reply_HwStatus) isReply_Reply() {}

func (*Reply_AmbientTemp) isReply_Reply() {}

var File_pulczar_proto protoreflect.FileDescriptor

var file_pulczar_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x1a, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x69, 0x6d,
	0x62, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x65,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x61, 0x77,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e,
	0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e,
	0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x76, 0x0a, 0x11, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x68, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x75, 0x73,
	0x65, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x74,
	0x65, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x04, 0x74, 0x65, 0x6d,
	0x70, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x22, 0x1c, 0x0a,
	0x1a, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa2, 0x01, 0x0a, 0x12,
	0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72,
	0x2e, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x44, 0x0a, 0x0a, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x41, 0x6d,
	0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x67, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x65, 0x0a, 0x10, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x12, 0x32, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x41, 0x6d,
	0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x12, 0x0a, 0x10, 0x48, 0x77, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xc3, 0x02, 0x0a, 0x0e,
	0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x53,
	0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f,
	0x73, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c,
	0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x56, 0x12, 0x26, 0x0a, 0x0f,
	0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6d, 0x6f, 0x74, 0x6f, 0x72, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x54, 0x69, 0x63, 0x6b,
	0x73, 0x22, 0xa0, 0x04, 0x0a, 0x15, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x53,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3a, 0x0a, 0x1a, 0x6c,
	0x70, 0x6d, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x65,
	0x61, 0x6d, 0x5f, 0x72, 0x61, 0x77, 0x5f, 0x6d, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x16, 0x6c, 0x70, 0x6d, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x42, 0x65,
	0x61, 0x6d, 0x52, 0x61, 0x77, 0x4d, 0x76, 0x12, 0x3a, 0x0a, 0x1a, 0x6c, 0x70, 0x6d, 0x5f, 0x74,
	0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x65, 0x61, 0x6d, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x5f, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x6c, 0x70, 0x6d,
	0x54, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x61, 0x6d, 0x54, 0x65,
	0x6d, 0x70, 0x43, 0x12, 0x40, 0x0a, 0x1d, 0x6c, 0x70, 0x6d, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x61,
	0x77, 0x5f, 0x6d, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x19, 0x6c, 0x70, 0x6d, 0x54,
	0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x61, 0x77, 0x4d, 0x76, 0x12, 0x40, 0x0a, 0x1d, 0x6c, 0x70, 0x6d, 0x5f, 0x74, 0x68, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x5f, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x19, 0x6c, 0x70,
	0x6d, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x41, 0x6d, 0x62, 0x69, 0x65,
	0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x70, 0x73, 0x75, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x70,
	0x73, 0x75, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x70, 0x73, 0x75,
	0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0d, 0x6c, 0x70, 0x73, 0x75, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x61,
	0x12, 0x39, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x48, 0x00, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x50, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x6f, 0x5f, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x48, 0x01, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x6f, 0x54, 0x69, 0x6c, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x4f, 0x6e, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x6f, 0x5f, 0x70, 0x61, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x5f,
	0x74, 0x69, 0x6c, 0x74, 0x22, 0x87, 0x04, 0x0a, 0x15, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27,
	0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x45, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x49,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x0e,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3c,
	0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x6f,
	0x64, 0x65, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52,
	0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x12,
	0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x5f, 0x63, 0x18, 0x04, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x54,
	0x68, 0x65, 0x72, 0x6d, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0b, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x57, 0x12, 0x2b, 0x0a,
	0x12, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x77,
	0x5f, 0x6d, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x61, 0x77, 0x4d, 0x76, 0x12, 0x39, 0x0a, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x6f, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x50,
	0x61, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x5f, 0x74,
	0x69, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x75, 0x6c, 0x63,
	0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x53, 0x65, 0x72,
	0x76, 0x6f, 0x48, 0x01, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x54, 0x69, 0x6c, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x2d, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d,
	0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x4f,
	0x6e, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x5f, 0x70, 0x61, 0x6e, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x5f, 0x74, 0x69, 0x6c, 0x74, 0x22, 0x8d,
	0x01, 0x0a, 0x0e, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x75,
	0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x0f,
	0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x16, 0x0a, 0x14, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2e, 0x0a, 0x10, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x22, 0x77, 0x0a, 0x0d, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x0b, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x01, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43,
	0x61, 0x6d, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x22, 0x64, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x0b, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4d, 0x0a, 0x0b, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x43, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x43, 0x61, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x22, 0x8a, 0x05, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x05, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x43, 0x6c, 0x65,
	0x61, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x05, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x06, 0x67, 0x69,
	0x6d, 0x62, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x69, 0x6d,
	0x62, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x12, 0x23, 0x0a, 0x04, 0x64, 0x61, 0x77, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x77, 0x67, 0x12, 0x26, 0x0a, 0x05, 0x6c, 0x61,
	0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6c, 0x65, 0x6e, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70,
	0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f,
	0x6e, 0x66, 0x12, 0x29, 0x0a, 0x03, 0x61, 0x72, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x61, 0x72, 0x63, 0x12, 0x2e, 0x0a,
	0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70,
	0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x38, 0x0a,
	0x09, 0x68, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x68,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x61, 0x6d, 0x62, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x6d,
	0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0xf0, 0x03, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c,
	0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63,
	0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x27, 0x0a, 0x06,
	0x67, 0x69, 0x6d, 0x62, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x67,
	0x69, 0x6d, 0x62, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x77, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x64, 0x61, 0x77, 0x67, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x77, 0x67, 0x12, 0x24, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x2f,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x21, 0x0a, 0x04, 0x6c, 0x65, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x6c, 0x65, 0x6e, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x6c, 0x65,
	0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x6e, 0x66, 0x12,
	0x27, 0x0a, 0x03, 0x61, 0x72, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61,
	0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x03, 0x61, 0x72, 0x63, 0x12, 0x2c, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61,
	0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x09, 0x68, 0x77, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x75, 0x6c, 0x63,
	0x7a, 0x61, 0x72, 0x2e, 0x48, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x48, 0x00, 0x52, 0x08, 0x68, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e,
	0x0a, 0x0c, 0x61, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x2e, 0x41,
	0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x0b, 0x61, 0x6d, 0x62, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x42, 0x07,
	0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x75, 0x6c, 0x63, 0x7a, 0x61, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pulczar_proto_rawDescOnce sync.Once
	file_pulczar_proto_rawDescData = file_pulczar_proto_rawDesc
)

func file_pulczar_proto_rawDescGZIP() []byte {
	file_pulczar_proto_rawDescOnce.Do(func() {
		file_pulczar_proto_rawDescData = protoimpl.X.CompressGZIP(file_pulczar_proto_rawDescData)
	})
	return file_pulczar_proto_rawDescData
}

var file_pulczar_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_pulczar_proto_goTypes = []interface{}{
	(*AmbientTempConfig)(nil),           // 0: pulczar.AmbientTempConfig
	(*AmbientTempGetStateRequest)(nil),  // 1: pulczar.AmbientTempGetStateRequest
	(*AmbientTempRequest)(nil),          // 2: pulczar.AmbientTempRequest
	(*AmbientTempReply)(nil),            // 3: pulczar.AmbientTempReply
	(*HwStatus_Request)(nil),            // 4: pulczar.HwStatus_Request
	(*HwStatus_Servo)(nil),              // 5: pulczar.HwStatus_Servo
	(*HwStatus_Slayer_Reply)(nil),       // 6: pulczar.HwStatus_Slayer_Reply
	(*HwStatus_Reaper_Reply)(nil),       // 7: pulczar.HwStatus_Reaper_Reply
	(*HwStatus_Reply)(nil),              // 8: pulczar.HwStatus_Reply
	(*Reset_Request)(nil),               // 9: pulczar.Reset_Request
	(*Clear_Config_Request)(nil),        // 10: pulczar.Clear_Config_Request
	(*Status_Request)(nil),              // 11: pulczar.Status_Request
	(*Override_Request)(nil),            // 12: pulczar.Override_Request
	(*Power_Request)(nil),               // 13: pulczar.Power_Request
	(*Status_Reply)(nil),                // 14: pulczar.Status_Reply
	(*Power_Reply)(nil),                 // 15: pulczar.Power_Reply
	(*Request)(nil),                     // 16: pulczar.Request
	(*Reply)(nil),                       // 17: pulczar.Reply
	(*laser.Laser_Inventory_Reply)(nil), // 18: laser.Laser_Inventory_Reply
	(*laser.Diode_Status_Reply)(nil),    // 19: laser.Diode_Status_Reply
	(*laser.Laser_Status_Reply)(nil),    // 20: laser.Laser_Status_Reply
	(*gimbal.Request)(nil),              // 21: gimbal.Request
	(*dawg.Request)(nil),                // 22: dawg.Request
	(*laser.Request)(nil),               // 23: laser.Request
	(*lens.Request)(nil),                // 24: lens.Request
	(*scanner_config.Request)(nil),      // 25: scanner_config.Request
	(*arc_detector.Request)(nil),        // 26: arc_detector.Request
	(*ack.Ack)(nil),                     // 27: ack.Ack
	(*gimbal.Reply)(nil),                // 28: gimbal.Reply
	(*dawg.Reply)(nil),                  // 29: dawg.Reply
	(*laser.Reply)(nil),                 // 30: laser.Reply
	(*lens.Reply)(nil),                  // 31: lens.Reply
	(*scanner_config.Reply)(nil),        // 32: scanner_config.Reply
	(*arc_detector.Reply)(nil),          // 33: arc_detector.Reply
}
var file_pulczar_proto_depIdxs = []int32{
	0,  // 0: pulczar.AmbientTempRequest.set_config:type_name -> pulczar.AmbientTempConfig
	1,  // 1: pulczar.AmbientTempRequest.get_config:type_name -> pulczar.AmbientTempGetStateRequest
	0,  // 2: pulczar.AmbientTempReply.config:type_name -> pulczar.AmbientTempConfig
	5,  // 3: pulczar.HwStatus_Slayer_Reply.servo_pan:type_name -> pulczar.HwStatus_Servo
	5,  // 4: pulczar.HwStatus_Slayer_Reply.servo_tilt:type_name -> pulczar.HwStatus_Servo
	18, // 5: pulczar.HwStatus_Reaper_Reply.laser_inventory:type_name -> laser.Laser_Inventory_Reply
	19, // 6: pulczar.HwStatus_Reaper_Reply.laser_status:type_name -> laser.Diode_Status_Reply
	5,  // 7: pulczar.HwStatus_Reaper_Reply.servo_pan:type_name -> pulczar.HwStatus_Servo
	5,  // 8: pulczar.HwStatus_Reaper_Reply.servo_tilt:type_name -> pulczar.HwStatus_Servo
	6,  // 9: pulczar.HwStatus_Reply.slayer:type_name -> pulczar.HwStatus_Slayer_Reply
	7,  // 10: pulczar.HwStatus_Reply.reaper:type_name -> pulczar.HwStatus_Reaper_Reply
	20, // 11: pulczar.Status_Reply.laser_status:type_name -> laser.Laser_Status_Reply
	9,  // 12: pulczar.Request.reset:type_name -> pulczar.Reset_Request
	10, // 13: pulczar.Request.clear:type_name -> pulczar.Clear_Config_Request
	21, // 14: pulczar.Request.gimbal:type_name -> gimbal.Request
	22, // 15: pulczar.Request.dawg:type_name -> dawg.Request
	23, // 16: pulczar.Request.laser:type_name -> laser.Request
	11, // 17: pulczar.Request.status:type_name -> pulczar.Status_Request
	24, // 18: pulczar.Request.lens:type_name -> lens.Request
	12, // 19: pulczar.Request.override:type_name -> pulczar.Override_Request
	25, // 20: pulczar.Request.conf:type_name -> scanner_config.Request
	26, // 21: pulczar.Request.arc:type_name -> arc_detector.Request
	13, // 22: pulczar.Request.power:type_name -> pulczar.Power_Request
	4,  // 23: pulczar.Request.hw_status:type_name -> pulczar.HwStatus_Request
	2,  // 24: pulczar.Request.ambient_temp:type_name -> pulczar.AmbientTempRequest
	27, // 25: pulczar.Reply.ack:type_name -> ack.Ack
	28, // 26: pulczar.Reply.gimbal:type_name -> gimbal.Reply
	29, // 27: pulczar.Reply.dawg:type_name -> dawg.Reply
	30, // 28: pulczar.Reply.laser:type_name -> laser.Reply
	14, // 29: pulczar.Reply.status:type_name -> pulczar.Status_Reply
	31, // 30: pulczar.Reply.lens:type_name -> lens.Reply
	32, // 31: pulczar.Reply.conf:type_name -> scanner_config.Reply
	33, // 32: pulczar.Reply.arc:type_name -> arc_detector.Reply
	15, // 33: pulczar.Reply.power:type_name -> pulczar.Power_Reply
	8,  // 34: pulczar.Reply.hw_status:type_name -> pulczar.HwStatus_Reply
	3,  // 35: pulczar.Reply.ambient_temp:type_name -> pulczar.AmbientTempReply
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_pulczar_proto_init() }
func file_pulczar_proto_init() {
	if File_pulczar_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pulczar_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmbientTempConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmbientTempGetStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmbientTempRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmbientTempReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HwStatus_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HwStatus_Servo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HwStatus_Slayer_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HwStatus_Reaper_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HwStatus_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reset_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Clear_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Override_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Power_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Power_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pulczar_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pulczar_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_pulczar_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*AmbientTempRequest_SetConfig)(nil),
		(*AmbientTempRequest_GetConfig)(nil),
	}
	file_pulczar_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_pulczar_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_pulczar_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*HwStatus_Reply_Slayer)(nil),
		(*HwStatus_Reply_Reaper)(nil),
	}
	file_pulczar_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_pulczar_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*Request_Reset_)(nil),
		(*Request_Clear)(nil),
		(*Request_Gimbal)(nil),
		(*Request_Dawg)(nil),
		(*Request_Laser)(nil),
		(*Request_Status)(nil),
		(*Request_Lens)(nil),
		(*Request_Override)(nil),
		(*Request_Conf)(nil),
		(*Request_Arc)(nil),
		(*Request_Power)(nil),
		(*Request_HwStatus)(nil),
		(*Request_AmbientTemp)(nil),
	}
	file_pulczar_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*Reply_Ack)(nil),
		(*Reply_Gimbal)(nil),
		(*Reply_Dawg)(nil),
		(*Reply_Laser)(nil),
		(*Reply_Status)(nil),
		(*Reply_Lens)(nil),
		(*Reply_Conf)(nil),
		(*Reply_Arc)(nil),
		(*Reply_Power)(nil),
		(*Reply_HwStatus)(nil),
		(*Reply_AmbientTemp)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pulczar_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pulczar_proto_goTypes,
		DependencyIndexes: file_pulczar_proto_depIdxs,
		MessageInfos:      file_pulczar_proto_msgTypes,
	}.Build()
	File_pulczar_proto = out.File
	file_pulczar_proto_rawDesc = nil
	file_pulczar_proto_goTypes = nil
	file_pulczar_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: gps.proto

package gps

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CarrierPhaseSoln int32

const (
	CarrierPhaseSoln_NONE     CarrierPhaseSoln = 0
	CarrierPhaseSoln_FLOATING CarrierPhaseSoln = 1
	CarrierPhaseSoln_FIXED    CarrierPhaseSoln = 2
)

// Enum value maps for CarrierPhaseSoln.
var (
	CarrierPhaseSoln_name = map[int32]string{
		0: "NONE",
		1: "FLOATING",
		2: "FIXED",
	}
	CarrierPhaseSoln_value = map[string]int32{
		"NONE":     0,
		"FLOATING": 1,
		"FIXED":    2,
	}
)

func (x CarrierPhaseSoln) Enum() *CarrierPhaseSoln {
	p := new(CarrierPhaseSoln)
	*p = x
	return p
}

func (x CarrierPhaseSoln) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CarrierPhaseSoln) Descriptor() protoreflect.EnumDescriptor {
	return file_gps_proto_enumTypes[0].Descriptor()
}

func (CarrierPhaseSoln) Type() protoreflect.EnumType {
	return &file_gps_proto_enumTypes[0]
}

func (x CarrierPhaseSoln) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CarrierPhaseSoln.Descriptor instead.
func (CarrierPhaseSoln) EnumDescriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{0}
}

type Position_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Position_Request) Reset() {
	*x = Position_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Position_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position_Request) ProtoMessage() {}

func (x *Position_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position_Request.ProtoReflect.Descriptor instead.
func (*Position_Request) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{0}
}

type Spartn_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	End  bool   `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *Spartn_Request) Reset() {
	*x = Spartn_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Spartn_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Spartn_Request) ProtoMessage() {}

func (x *Spartn_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Spartn_Request.ProtoReflect.Descriptor instead.
func (*Spartn_Request) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{1}
}

func (x *Spartn_Request) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Spartn_Request) GetEnd() bool {
	if x != nil {
		return x.End
	}
	return false
}

type Spartn_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Spartn_Reply) Reset() {
	*x = Spartn_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Spartn_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Spartn_Reply) ProtoMessage() {}

func (x *Spartn_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Spartn_Reply.ProtoReflect.Descriptor instead.
func (*Spartn_Reply) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{2}
}

type Rtcm_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	End  bool   `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *Rtcm_Request) Reset() {
	*x = Rtcm_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rtcm_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rtcm_Request) ProtoMessage() {}

func (x *Rtcm_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rtcm_Request.ProtoReflect.Descriptor instead.
func (*Rtcm_Request) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{3}
}

func (x *Rtcm_Request) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Rtcm_Request) GetEnd() bool {
	if x != nil {
		return x.End
	}
	return false
}

type Rtcm_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Rtcm_Reply) Reset() {
	*x = Rtcm_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rtcm_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rtcm_Reply) ProtoMessage() {}

func (x *Rtcm_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rtcm_Reply.ProtoReflect.Descriptor instead.
func (*Rtcm_Reply) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{4}
}

type ValueWithAccuracy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	Accuracy float64 `protobuf:"fixed64,2,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
}

func (x *ValueWithAccuracy) Reset() {
	*x = ValueWithAccuracy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValueWithAccuracy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueWithAccuracy) ProtoMessage() {}

func (x *ValueWithAccuracy) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueWithAccuracy.ProtoReflect.Descriptor instead.
func (*ValueWithAccuracy) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{5}
}

func (x *ValueWithAccuracy) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ValueWithAccuracy) GetAccuracy() float64 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

// Relative position information from secondary gps on dual gps board
type DualGpsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GnssValid       bool             `protobuf:"varint,1,opt,name=gnss_valid,json=gnssValid,proto3" json:"gnss_valid,omitempty"`
	DiffCorrections bool             `protobuf:"varint,2,opt,name=diff_corrections,json=diffCorrections,proto3" json:"diff_corrections,omitempty"`
	IsMovingBase    bool             `protobuf:"varint,3,opt,name=is_moving_base,json=isMovingBase,proto3" json:"is_moving_base,omitempty"`
	CarrierPhase    CarrierPhaseSoln `protobuf:"varint,4,opt,name=carrier_phase,json=carrierPhase,proto3,enum=gps.CarrierPhaseSoln" json:"carrier_phase,omitempty"`
	// Timestamp of this fix (if valid)
	TimestampMs uint64 `protobuf:"varint,5,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	// North component of relative position vector (mm)
	North *ValueWithAccuracy `protobuf:"bytes,6,opt,name=north,proto3,oneof" json:"north,omitempty"`
	// East component of relative position vector (mm)
	East *ValueWithAccuracy `protobuf:"bytes,7,opt,name=east,proto3,oneof" json:"east,omitempty"`
	// Down component of relative position vector (mm)
	Down *ValueWithAccuracy `protobuf:"bytes,8,opt,name=down,proto3,oneof" json:"down,omitempty"`
	// Relative position vector, the distance between antennas (mm)
	Length *ValueWithAccuracy `protobuf:"bytes,9,opt,name=length,proto3,oneof" json:"length,omitempty"`
	// Heading of travel (°)
	Heading *ValueWithAccuracy `protobuf:"bytes,10,opt,name=heading,proto3,oneof" json:"heading,omitempty"`
}

func (x *DualGpsData) Reset() {
	*x = DualGpsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DualGpsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DualGpsData) ProtoMessage() {}

func (x *DualGpsData) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DualGpsData.ProtoReflect.Descriptor instead.
func (*DualGpsData) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{6}
}

func (x *DualGpsData) GetGnssValid() bool {
	if x != nil {
		return x.GnssValid
	}
	return false
}

func (x *DualGpsData) GetDiffCorrections() bool {
	if x != nil {
		return x.DiffCorrections
	}
	return false
}

func (x *DualGpsData) GetIsMovingBase() bool {
	if x != nil {
		return x.IsMovingBase
	}
	return false
}

func (x *DualGpsData) GetCarrierPhase() CarrierPhaseSoln {
	if x != nil {
		return x.CarrierPhase
	}
	return CarrierPhaseSoln_NONE
}

func (x *DualGpsData) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *DualGpsData) GetNorth() *ValueWithAccuracy {
	if x != nil {
		return x.North
	}
	return nil
}

func (x *DualGpsData) GetEast() *ValueWithAccuracy {
	if x != nil {
		return x.East
	}
	return nil
}

func (x *DualGpsData) GetDown() *ValueWithAccuracy {
	if x != nil {
		return x.Down
	}
	return nil
}

func (x *DualGpsData) GetLength() *ValueWithAccuracy {
	if x != nil {
		return x.Length
	}
	return nil
}

func (x *DualGpsData) GetHeading() *ValueWithAccuracy {
	if x != nil {
		return x.Heading
	}
	return nil
}

type Position_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HaveFix   bool    `protobuf:"varint,1,opt,name=have_fix,json=haveFix,proto3" json:"have_fix,omitempty"`
	Latitude  float64 `protobuf:"fixed64,2,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude float64 `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
	NumSats   int32   `protobuf:"varint,4,opt,name=num_sats,json=numSats,proto3" json:"num_sats,omitempty"`
	Hdop      float32 `protobuf:"fixed32,5,opt,name=hdop,proto3" json:"hdop,omitempty"`
	// Only valid if fix is present
	TimestampMs   uint64       `protobuf:"varint,6,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	HeightMm      int32        `protobuf:"varint,7,opt,name=height_mm,json=heightMm,proto3" json:"height_mm,omitempty"`
	HaveApproxFix bool         `protobuf:"varint,8,opt,name=have_approx_fix,json=haveApproxFix,proto3" json:"have_approx_fix,omitempty"`
	FixType       int32        `protobuf:"varint,9,opt,name=fix_type,json=fixType,proto3" json:"fix_type,omitempty"`
	FixFlags      int32        `protobuf:"varint,10,opt,name=fix_flags,json=fixFlags,proto3" json:"fix_flags,omitempty"`
	Dual          *DualGpsData `protobuf:"bytes,11,opt,name=dual,proto3,oneof" json:"dual,omitempty"`
}

func (x *Position_Reply) Reset() {
	*x = Position_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Position_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position_Reply) ProtoMessage() {}

func (x *Position_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position_Reply.ProtoReflect.Descriptor instead.
func (*Position_Reply) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{7}
}

func (x *Position_Reply) GetHaveFix() bool {
	if x != nil {
		return x.HaveFix
	}
	return false
}

func (x *Position_Reply) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Position_Reply) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *Position_Reply) GetNumSats() int32 {
	if x != nil {
		return x.NumSats
	}
	return 0
}

func (x *Position_Reply) GetHdop() float32 {
	if x != nil {
		return x.Hdop
	}
	return 0
}

func (x *Position_Reply) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *Position_Reply) GetHeightMm() int32 {
	if x != nil {
		return x.HeightMm
	}
	return 0
}

func (x *Position_Reply) GetHaveApproxFix() bool {
	if x != nil {
		return x.HaveApproxFix
	}
	return false
}

func (x *Position_Reply) GetFixType() int32 {
	if x != nil {
		return x.FixType
	}
	return 0
}

func (x *Position_Reply) GetFixFlags() int32 {
	if x != nil {
		return x.FixFlags
	}
	return 0
}

func (x *Position_Reply) GetDual() *DualGpsData {
	if x != nil {
		return x.Dual
	}
	return nil
}

type HeadingCorrection_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadingOffset float32 `protobuf:"fixed32,1,opt,name=heading_offset,json=headingOffset,proto3" json:"heading_offset,omitempty"`
}

func (x *HeadingCorrection_Request) Reset() {
	*x = HeadingCorrection_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeadingCorrection_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeadingCorrection_Request) ProtoMessage() {}

func (x *HeadingCorrection_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeadingCorrection_Request.ProtoReflect.Descriptor instead.
func (*HeadingCorrection_Request) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{8}
}

func (x *HeadingCorrection_Request) GetHeadingOffset() float32 {
	if x != nil {
		return x.HeadingOffset
	}
	return 0
}

type HeadingCorrection_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HeadingCorrection_Reply) Reset() {
	*x = HeadingCorrection_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeadingCorrection_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeadingCorrection_Reply) ProtoMessage() {}

func (x *HeadingCorrection_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeadingCorrection_Reply.ProtoReflect.Descriptor instead.
func (*HeadingCorrection_Reply) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{9}
}

type GetLastGga_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLastGga_Request) Reset() {
	*x = GetLastGga_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastGga_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastGga_Request) ProtoMessage() {}

func (x *GetLastGga_Request) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastGga_Request.ProtoReflect.Descriptor instead.
func (*GetLastGga_Request) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{10}
}

type GetLastGga_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawSentence string `protobuf:"bytes,1,opt,name=raw_sentence,json=rawSentence,proto3" json:"raw_sentence,omitempty"`
}

func (x *GetLastGga_Reply) Reset() {
	*x = GetLastGga_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastGga_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastGga_Reply) ProtoMessage() {}

func (x *GetLastGga_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastGga_Reply.ProtoReflect.Descriptor instead.
func (*GetLastGga_Reply) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{11}
}

func (x *GetLastGga_Reply) GetRawSentence() string {
	if x != nil {
		return x.RawSentence
	}
	return ""
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Position
	//	*Request_Spartn
	//	*Request_HeadingCorrection
	//	*Request_Rtcm
	//	*Request_Gga
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{12}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPosition() *Position_Request {
	if x, ok := x.GetRequest().(*Request_Position); ok {
		return x.Position
	}
	return nil
}

func (x *Request) GetSpartn() *Spartn_Request {
	if x, ok := x.GetRequest().(*Request_Spartn); ok {
		return x.Spartn
	}
	return nil
}

func (x *Request) GetHeadingCorrection() *HeadingCorrection_Request {
	if x, ok := x.GetRequest().(*Request_HeadingCorrection); ok {
		return x.HeadingCorrection
	}
	return nil
}

func (x *Request) GetRtcm() *Rtcm_Request {
	if x, ok := x.GetRequest().(*Request_Rtcm); ok {
		return x.Rtcm
	}
	return nil
}

func (x *Request) GetGga() *GetLastGga_Request {
	if x, ok := x.GetRequest().(*Request_Gga); ok {
		return x.Gga
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Position struct {
	Position *Position_Request `protobuf:"bytes,1,opt,name=position,proto3,oneof"`
}

type Request_Spartn struct {
	Spartn *Spartn_Request `protobuf:"bytes,2,opt,name=spartn,proto3,oneof"`
}

type Request_HeadingCorrection struct {
	HeadingCorrection *HeadingCorrection_Request `protobuf:"bytes,3,opt,name=heading_correction,json=headingCorrection,proto3,oneof"`
}

type Request_Rtcm struct {
	Rtcm *Rtcm_Request `protobuf:"bytes,4,opt,name=rtcm,proto3,oneof"`
}

type Request_Gga struct {
	Gga *GetLastGga_Request `protobuf:"bytes,5,opt,name=gga,proto3,oneof"` // TODO: satellite information
}

func (*Request_Position) isRequest_Request() {}

func (*Request_Spartn) isRequest_Request() {}

func (*Request_HeadingCorrection) isRequest_Request() {}

func (*Request_Rtcm) isRequest_Request() {}

func (*Request_Gga) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Position
	//	*Reply_Spartn
	//	*Reply_HeadingCorrection
	//	*Reply_Rtcm
	//	*Reply_Gga
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gps_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_gps_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_gps_proto_rawDescGZIP(), []int{13}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPosition() *Position_Reply {
	if x, ok := x.GetReply().(*Reply_Position); ok {
		return x.Position
	}
	return nil
}

func (x *Reply) GetSpartn() *Spartn_Reply {
	if x, ok := x.GetReply().(*Reply_Spartn); ok {
		return x.Spartn
	}
	return nil
}

func (x *Reply) GetHeadingCorrection() *HeadingCorrection_Reply {
	if x, ok := x.GetReply().(*Reply_HeadingCorrection); ok {
		return x.HeadingCorrection
	}
	return nil
}

func (x *Reply) GetRtcm() *Rtcm_Reply {
	if x, ok := x.GetReply().(*Reply_Rtcm); ok {
		return x.Rtcm
	}
	return nil
}

func (x *Reply) GetGga() *GetLastGga_Reply {
	if x, ok := x.GetReply().(*Reply_Gga); ok {
		return x.Gga
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Position struct {
	Position *Position_Reply `protobuf:"bytes,1,opt,name=position,proto3,oneof"`
}

type Reply_Spartn struct {
	Spartn *Spartn_Reply `protobuf:"bytes,2,opt,name=spartn,proto3,oneof"`
}

type Reply_HeadingCorrection struct {
	HeadingCorrection *HeadingCorrection_Reply `protobuf:"bytes,3,opt,name=heading_correction,json=headingCorrection,proto3,oneof"`
}

type Reply_Rtcm struct {
	Rtcm *Rtcm_Reply `protobuf:"bytes,4,opt,name=rtcm,proto3,oneof"`
}

type Reply_Gga struct {
	Gga *GetLastGga_Reply `protobuf:"bytes,5,opt,name=gga,proto3,oneof"`
}

func (*Reply_Position) isReply_Reply() {}

func (*Reply_Spartn) isReply_Reply() {}

func (*Reply_HeadingCorrection) isReply_Reply() {}

func (*Reply_Rtcm) isReply_Reply() {}

func (*Reply_Gga) isReply_Reply() {}

var File_gps_proto protoreflect.FileDescriptor

var file_gps_proto_rawDesc = []byte{
	0x0a, 0x09, 0x67, 0x70, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x67, 0x70, 0x73,
	0x22, 0x12, 0x0a, 0x10, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x0e, 0x53, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x0e, 0x0a, 0x0c,
	0x53, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x0a, 0x0c,
	0x52, 0x74, 0x63, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65,
	0x6e, 0x64, 0x22, 0x0c, 0x0a, 0x0a, 0x52, 0x74, 0x63, 0x6d, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x45, 0x0a, 0x11, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63,
	0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x61,
	0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x22, 0x90, 0x04, 0x0a, 0x0b, 0x44, 0x75, 0x61, 0x6c,
	0x47, 0x70, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6e, 0x73, 0x73, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x67, 0x6e, 0x73,
	0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x66, 0x66, 0x5f, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x64, 0x69, 0x66, 0x66, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d, 0x6f, 0x76,
	0x69, 0x6e, 0x67, 0x42, 0x61, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x67, 0x70, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x50, 0x68, 0x61, 0x73,
	0x65, 0x53, 0x6f, 0x6c, 0x6e, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x50, 0x68,
	0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x31, 0x0a, 0x05, 0x6e, 0x6f, 0x72, 0x74, 0x68, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x48, 0x00, 0x52,
	0x05, 0x6e, 0x6f, 0x72, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x04, 0x65, 0x61, 0x73,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x48,
	0x01, 0x52, 0x04, 0x65, 0x61, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x6f,
	0x77, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x48, 0x02, 0x52, 0x04, 0x64, 0x6f, 0x77, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x06, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x70,
	0x73, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x63, 0x75, 0x72,
	0x61, 0x63, 0x79, 0x48, 0x03, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x88, 0x01, 0x01,
	0x12, 0x35, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x48, 0x04, 0x52, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6e, 0x6f, 0x72, 0x74,
	0x68, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x65, 0x61, 0x73, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64,
	0x6f, 0x77, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xe8, 0x02, 0x0a, 0x0e, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x19, 0x0a,
	0x08, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x68, 0x61, 0x76, 0x65, 0x46, 0x69, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x61, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x53, 0x61, 0x74, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x64, 0x6f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x68, 0x64, 0x6f,
	0x70, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6d,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x4d,
	0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x78,
	0x5f, 0x66, 0x69, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x76, 0x65,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x46, 0x69, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x78,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x66, 0x69, 0x78,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x78, 0x5f, 0x66, 0x6c, 0x61, 0x67,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x78, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x75, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x44, 0x75, 0x61, 0x6c, 0x47, 0x70, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x04, 0x64, 0x75, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x64, 0x75, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x19, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x48, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x47,
	0x67, 0x61, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x35, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x4c, 0x61, 0x73, 0x74, 0x47, 0x67, 0x61, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x53, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x22, 0x9f, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a,
	0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x12, 0x4f, 0x0a, 0x12, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x67, 0x70, 0x73, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x11, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04, 0x72, 0x74, 0x63, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x52, 0x74, 0x63, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x72, 0x74, 0x63, 0x6d, 0x12, 0x2b, 0x0a, 0x03, 0x67,
	0x67, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x47, 0x67, 0x61, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x03, 0x67, 0x67, 0x61, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x91, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x31, 0x0a,
	0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2b, 0x0a, 0x06, 0x73, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x5f, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x12, 0x4d, 0x0a,
	0x12, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x70, 0x73, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x11, 0x68, 0x65, 0x61, 0x64, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x04,
	0x72, 0x74, 0x63, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x70, 0x73,
	0x2e, 0x52, 0x74, 0x63, 0x6d, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x72,
	0x74, 0x63, 0x6d, 0x12, 0x29, 0x0a, 0x03, 0x67, 0x67, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x70, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x47, 0x67,
	0x61, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x67, 0x67, 0x61, 0x42, 0x07,
	0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x35, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x50, 0x68, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x6c, 0x6e, 0x12, 0x08, 0x0a, 0x04, 0x4e,
	0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x49, 0x58, 0x45, 0x44, 0x10, 0x02, 0x42, 0x0c,
	0x5a, 0x0a, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x67, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gps_proto_rawDescOnce sync.Once
	file_gps_proto_rawDescData = file_gps_proto_rawDesc
)

func file_gps_proto_rawDescGZIP() []byte {
	file_gps_proto_rawDescOnce.Do(func() {
		file_gps_proto_rawDescData = protoimpl.X.CompressGZIP(file_gps_proto_rawDescData)
	})
	return file_gps_proto_rawDescData
}

var file_gps_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_gps_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_gps_proto_goTypes = []interface{}{
	(CarrierPhaseSoln)(0),             // 0: gps.CarrierPhaseSoln
	(*Position_Request)(nil),          // 1: gps.Position_Request
	(*Spartn_Request)(nil),            // 2: gps.Spartn_Request
	(*Spartn_Reply)(nil),              // 3: gps.Spartn_Reply
	(*Rtcm_Request)(nil),              // 4: gps.Rtcm_Request
	(*Rtcm_Reply)(nil),                // 5: gps.Rtcm_Reply
	(*ValueWithAccuracy)(nil),         // 6: gps.ValueWithAccuracy
	(*DualGpsData)(nil),               // 7: gps.DualGpsData
	(*Position_Reply)(nil),            // 8: gps.Position_Reply
	(*HeadingCorrection_Request)(nil), // 9: gps.HeadingCorrection_Request
	(*HeadingCorrection_Reply)(nil),   // 10: gps.HeadingCorrection_Reply
	(*GetLastGga_Request)(nil),        // 11: gps.GetLastGga_Request
	(*GetLastGga_Reply)(nil),          // 12: gps.GetLastGga_Reply
	(*Request)(nil),                   // 13: gps.Request
	(*Reply)(nil),                     // 14: gps.Reply
}
var file_gps_proto_depIdxs = []int32{
	0,  // 0: gps.DualGpsData.carrier_phase:type_name -> gps.CarrierPhaseSoln
	6,  // 1: gps.DualGpsData.north:type_name -> gps.ValueWithAccuracy
	6,  // 2: gps.DualGpsData.east:type_name -> gps.ValueWithAccuracy
	6,  // 3: gps.DualGpsData.down:type_name -> gps.ValueWithAccuracy
	6,  // 4: gps.DualGpsData.length:type_name -> gps.ValueWithAccuracy
	6,  // 5: gps.DualGpsData.heading:type_name -> gps.ValueWithAccuracy
	7,  // 6: gps.Position_Reply.dual:type_name -> gps.DualGpsData
	1,  // 7: gps.Request.position:type_name -> gps.Position_Request
	2,  // 8: gps.Request.spartn:type_name -> gps.Spartn_Request
	9,  // 9: gps.Request.heading_correction:type_name -> gps.HeadingCorrection_Request
	4,  // 10: gps.Request.rtcm:type_name -> gps.Rtcm_Request
	11, // 11: gps.Request.gga:type_name -> gps.GetLastGga_Request
	8,  // 12: gps.Reply.position:type_name -> gps.Position_Reply
	3,  // 13: gps.Reply.spartn:type_name -> gps.Spartn_Reply
	10, // 14: gps.Reply.heading_correction:type_name -> gps.HeadingCorrection_Reply
	5,  // 15: gps.Reply.rtcm:type_name -> gps.Rtcm_Reply
	12, // 16: gps.Reply.gga:type_name -> gps.GetLastGga_Reply
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_gps_proto_init() }
func file_gps_proto_init() {
	if File_gps_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gps_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Position_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Spartn_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Spartn_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rtcm_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rtcm_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValueWithAccuracy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DualGpsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Position_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeadingCorrection_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeadingCorrection_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastGga_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastGga_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gps_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_gps_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_gps_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_gps_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*Request_Position)(nil),
		(*Request_Spartn)(nil),
		(*Request_HeadingCorrection)(nil),
		(*Request_Rtcm)(nil),
		(*Request_Gga)(nil),
	}
	file_gps_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*Reply_Position)(nil),
		(*Reply_Spartn)(nil),
		(*Reply_HeadingCorrection)(nil),
		(*Reply_Rtcm)(nil),
		(*Reply_Gga)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gps_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gps_proto_goTypes,
		DependencyIndexes: file_gps_proto_depIdxs,
		EnumInfos:         file_gps_proto_enumTypes,
		MessageInfos:      file_gps_proto_msgTypes,
	}.Build()
	File_gps_proto = out.File
	file_gps_proto_rawDesc = nil
	file_gps_proto_goTypes = nil
	file_gps_proto_depIdxs = nil
}

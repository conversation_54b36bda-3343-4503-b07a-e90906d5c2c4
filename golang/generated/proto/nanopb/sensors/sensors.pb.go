// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: sensors.proto

package sensors

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FuelGauge_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FuelGauge_Request) Reset() {
	*x = FuelGauge_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sensors_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FuelGauge_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FuelGauge_Request) ProtoMessage() {}

func (x *FuelGauge_Request) ProtoReflect() protoreflect.Message {
	mi := &file_sensors_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FuelGauge_Request.ProtoReflect.Descriptor instead.
func (*FuelGauge_Request) Descriptor() ([]byte, []int) {
	return file_sensors_proto_rawDescGZIP(), []int{0}
}

type FuelGauge_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value float32 `protobuf:"fixed32,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *FuelGauge_Reply) Reset() {
	*x = FuelGauge_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sensors_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FuelGauge_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FuelGauge_Reply) ProtoMessage() {}

func (x *FuelGauge_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_sensors_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FuelGauge_Reply.ProtoReflect.Descriptor instead.
func (*FuelGauge_Reply) Descriptor() ([]byte, []int) {
	return file_sensors_proto_rawDescGZIP(), []int{1}
}

func (x *FuelGauge_Reply) GetValue() float32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_FuelGauge
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sensors_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_sensors_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_sensors_proto_rawDescGZIP(), []int{2}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetFuelGauge() *FuelGauge_Request {
	if x, ok := x.GetRequest().(*Request_FuelGauge); ok {
		return x.FuelGauge
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_FuelGauge struct {
	FuelGauge *FuelGauge_Request `protobuf:"bytes,1,opt,name=fuel_gauge,json=fuelGauge,proto3,oneof"`
}

func (*Request_FuelGauge) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_FuelGauge
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sensors_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_sensors_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_sensors_proto_rawDescGZIP(), []int{3}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetFuelGauge() *FuelGauge_Reply {
	if x, ok := x.GetReply().(*Reply_FuelGauge); ok {
		return x.FuelGauge
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_FuelGauge struct {
	FuelGauge *FuelGauge_Reply `protobuf:"bytes,1,opt,name=fuel_gauge,json=fuelGauge,proto3,oneof"`
}

func (*Reply_FuelGauge) isReply_Reply() {}

var File_sensors_proto protoreflect.FileDescriptor

var file_sensors_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x22, 0x13, 0x0a, 0x11, 0x46, 0x75, 0x65, 0x6c,
	0x47, 0x61, 0x75, 0x67, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x27, 0x0a,
	0x0f, 0x46, 0x75, 0x65, 0x6c, 0x47, 0x61, 0x75, 0x67, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x51, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x67, 0x61, 0x75, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x2e,
	0x46, 0x75, 0x65, 0x6c, 0x47, 0x61, 0x75, 0x67, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x09, 0x66, 0x75, 0x65, 0x6c, 0x47, 0x61, 0x75, 0x67, 0x65, 0x42, 0x09,
	0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4b, 0x0a, 0x05, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x67, 0x61, 0x75, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73,
	0x2e, 0x46, 0x75, 0x65, 0x6c, 0x47, 0x61, 0x75, 0x67, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x09, 0x66, 0x75, 0x65, 0x6c, 0x47, 0x61, 0x75, 0x67, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x10, 0x5a, 0x0e, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sensors_proto_rawDescOnce sync.Once
	file_sensors_proto_rawDescData = file_sensors_proto_rawDesc
)

func file_sensors_proto_rawDescGZIP() []byte {
	file_sensors_proto_rawDescOnce.Do(func() {
		file_sensors_proto_rawDescData = protoimpl.X.CompressGZIP(file_sensors_proto_rawDescData)
	})
	return file_sensors_proto_rawDescData
}

var file_sensors_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_sensors_proto_goTypes = []interface{}{
	(*FuelGauge_Request)(nil), // 0: sensors.FuelGauge_Request
	(*FuelGauge_Reply)(nil),   // 1: sensors.FuelGauge_Reply
	(*Request)(nil),           // 2: sensors.Request
	(*Reply)(nil),             // 3: sensors.Reply
}
var file_sensors_proto_depIdxs = []int32{
	0, // 0: sensors.Request.fuel_gauge:type_name -> sensors.FuelGauge_Request
	1, // 1: sensors.Reply.fuel_gauge:type_name -> sensors.FuelGauge_Reply
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_sensors_proto_init() }
func file_sensors_proto_init() {
	if File_sensors_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sensors_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FuelGauge_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sensors_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FuelGauge_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sensors_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sensors_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_sensors_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*Request_FuelGauge)(nil),
	}
	file_sensors_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*Reply_FuelGauge)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sensors_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_sensors_proto_goTypes,
		DependencyIndexes: file_sensors_proto_depIdxs,
		MessageInfos:      file_sensors_proto_msgTypes,
	}.Build()
	File_sensors_proto = out.File
	file_sensors_proto_rawDesc = nil
	file_sensors_proto_goTypes = nil
	file_sensors_proto_depIdxs = nil
}

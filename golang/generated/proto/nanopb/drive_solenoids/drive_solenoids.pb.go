// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: drive_solenoids.proto

package drive_solenoids

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Drive_Request_Direction int32

const (
	Drive_Request_forward  Drive_Request_Direction = 0
	Drive_Request_backward Drive_Request_Direction = 1
	Drive_Request_stop     Drive_Request_Direction = 3
)

// Enum value maps for Drive_Request_Direction.
var (
	Drive_Request_Direction_name = map[int32]string{
		0: "forward",
		1: "backward",
		3: "stop",
	}
	Drive_Request_Direction_value = map[string]int32{
		"forward":  0,
		"backward": 1,
		"stop":     3,
	}
)

func (x Drive_Request_Direction) Enum() *Drive_Request_Direction {
	p := new(Drive_Request_Direction)
	*p = x
	return p
}

func (x Drive_Request_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Drive_Request_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_drive_solenoids_proto_enumTypes[0].Descriptor()
}

func (Drive_Request_Direction) Type() protoreflect.EnumType {
	return &file_drive_solenoids_proto_enumTypes[0]
}

func (x Drive_Request_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Drive_Request_Direction.Descriptor instead.
func (Drive_Request_Direction) EnumDescriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{0, 0}
}

type Turn_Request_Direction int32

const (
	Turn_Request_left     Turn_Request_Direction = 0
	Turn_Request_right    Turn_Request_Direction = 1
	Turn_Request_straight Turn_Request_Direction = 2
)

// Enum value maps for Turn_Request_Direction.
var (
	Turn_Request_Direction_name = map[int32]string{
		0: "left",
		1: "right",
		2: "straight",
	}
	Turn_Request_Direction_value = map[string]int32{
		"left":     0,
		"right":    1,
		"straight": 2,
	}
)

func (x Turn_Request_Direction) Enum() *Turn_Request_Direction {
	p := new(Turn_Request_Direction)
	*p = x
	return p
}

func (x Turn_Request_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Turn_Request_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_drive_solenoids_proto_enumTypes[1].Descriptor()
}

func (Turn_Request_Direction) Type() protoreflect.EnumType {
	return &file_drive_solenoids_proto_enumTypes[1]
}

func (x Turn_Request_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Turn_Request_Direction.Descriptor instead.
func (Turn_Request_Direction) EnumDescriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{2, 0}
}

type Drive_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dir       Drive_Request_Direction `protobuf:"varint,1,opt,name=dir,proto3,enum=drive_solenoids.Drive_Request_Direction" json:"dir,omitempty"`
	DutyCycle float32                 `protobuf:"fixed32,2,opt,name=duty_cycle,json=dutyCycle,proto3" json:"duty_cycle,omitempty"`
}

func (x *Drive_Request) Reset() {
	*x = Drive_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_drive_solenoids_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Drive_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Drive_Request) ProtoMessage() {}

func (x *Drive_Request) ProtoReflect() protoreflect.Message {
	mi := &file_drive_solenoids_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Drive_Request.ProtoReflect.Descriptor instead.
func (*Drive_Request) Descriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{0}
}

func (x *Drive_Request) GetDir() Drive_Request_Direction {
	if x != nil {
		return x.Dir
	}
	return Drive_Request_forward
}

func (x *Drive_Request) GetDutyCycle() float32 {
	if x != nil {
		return x.DutyCycle
	}
	return 0
}

type Drive_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Drive_Reply) Reset() {
	*x = Drive_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_drive_solenoids_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Drive_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Drive_Reply) ProtoMessage() {}

func (x *Drive_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_drive_solenoids_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Drive_Reply.ProtoReflect.Descriptor instead.
func (*Drive_Reply) Descriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{1}
}

type Turn_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dir       Turn_Request_Direction `protobuf:"varint,1,opt,name=dir,proto3,enum=drive_solenoids.Turn_Request_Direction" json:"dir,omitempty"`
	DutyCycle float32                `protobuf:"fixed32,2,opt,name=duty_cycle,json=dutyCycle,proto3" json:"duty_cycle,omitempty"`
}

func (x *Turn_Request) Reset() {
	*x = Turn_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_drive_solenoids_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Turn_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Turn_Request) ProtoMessage() {}

func (x *Turn_Request) ProtoReflect() protoreflect.Message {
	mi := &file_drive_solenoids_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Turn_Request.ProtoReflect.Descriptor instead.
func (*Turn_Request) Descriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{2}
}

func (x *Turn_Request) GetDir() Turn_Request_Direction {
	if x != nil {
		return x.Dir
	}
	return Turn_Request_left
}

func (x *Turn_Request) GetDutyCycle() float32 {
	if x != nil {
		return x.DutyCycle
	}
	return 0
}

type Turn_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Turn_Reply) Reset() {
	*x = Turn_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_drive_solenoids_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Turn_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Turn_Reply) ProtoMessage() {}

func (x *Turn_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_drive_solenoids_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Turn_Reply.ProtoReflect.Descriptor instead.
func (*Turn_Reply) Descriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{3}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Drive
	//	*Request_Turn
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_drive_solenoids_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_drive_solenoids_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{4}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetDrive() *Drive_Request {
	if x, ok := x.GetRequest().(*Request_Drive); ok {
		return x.Drive
	}
	return nil
}

func (x *Request) GetTurn() *Turn_Request {
	if x, ok := x.GetRequest().(*Request_Turn); ok {
		return x.Turn
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Drive struct {
	Drive *Drive_Request `protobuf:"bytes,1,opt,name=drive,proto3,oneof"`
}

type Request_Turn struct {
	Turn *Turn_Request `protobuf:"bytes,2,opt,name=turn,proto3,oneof"`
}

func (*Request_Drive) isRequest_Request() {}

func (*Request_Turn) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Drive
	//	*Reply_Turn
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_drive_solenoids_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_drive_solenoids_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_drive_solenoids_proto_rawDescGZIP(), []int{5}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetDrive() *Drive_Reply {
	if x, ok := x.GetReply().(*Reply_Drive); ok {
		return x.Drive
	}
	return nil
}

func (x *Reply) GetTurn() *Turn_Reply {
	if x, ok := x.GetReply().(*Reply_Turn); ok {
		return x.Turn
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Drive struct {
	Drive *Drive_Reply `protobuf:"bytes,1,opt,name=drive,proto3,oneof"`
}

type Reply_Turn struct {
	Turn *Turn_Reply `protobuf:"bytes,2,opt,name=turn,proto3,oneof"`
}

func (*Reply_Drive) isReply_Reply() {}

func (*Reply_Turn) isReply_Reply() {}

var File_drive_solenoids_proto protoreflect.FileDescriptor

var file_drive_solenoids_proto_rawDesc = []byte{
	0x0a, 0x15, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73,
	0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x22, 0x9c, 0x01, 0x0a, 0x0d, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x64, 0x69,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f,
	0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x03, 0x64, 0x69, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x75, 0x74, 0x79, 0x5f, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x75, 0x74, 0x79,
	0x43, 0x79, 0x63, 0x6c, 0x65, 0x22, 0x30, 0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x62, 0x61, 0x63, 0x6b, 0x77, 0x61, 0x72, 0x64, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x73, 0x74, 0x6f, 0x70, 0x10, 0x03, 0x22, 0x0d, 0x0a, 0x0b, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x98, 0x01, 0x0a, 0x0c, 0x54, 0x75, 0x72, 0x6e, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c,
	0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x2e, 0x54, 0x75, 0x72, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x64,
	0x69, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x75, 0x74, 0x79, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x75, 0x74, 0x79, 0x43, 0x79, 0x63, 0x6c,
	0x65, 0x22, 0x2e, 0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08,
	0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x69, 0x67, 0x68, 0x74, 0x10,
	0x02, 0x22, 0x0c, 0x0a, 0x0a, 0x54, 0x75, 0x72, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x81, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x05, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x2e, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x75, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f,
	0x69, 0x64, 0x73, 0x2e, 0x54, 0x75, 0x72, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x04, 0x74, 0x75, 0x72, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x79, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x05,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x2e, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x75, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69,
	0x64, 0x73, 0x2e, 0x54, 0x75, 0x72, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x04, 0x74, 0x75, 0x72, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x18,
	0x5a, 0x16, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73,
	0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_drive_solenoids_proto_rawDescOnce sync.Once
	file_drive_solenoids_proto_rawDescData = file_drive_solenoids_proto_rawDesc
)

func file_drive_solenoids_proto_rawDescGZIP() []byte {
	file_drive_solenoids_proto_rawDescOnce.Do(func() {
		file_drive_solenoids_proto_rawDescData = protoimpl.X.CompressGZIP(file_drive_solenoids_proto_rawDescData)
	})
	return file_drive_solenoids_proto_rawDescData
}

var file_drive_solenoids_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_drive_solenoids_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_drive_solenoids_proto_goTypes = []interface{}{
	(Drive_Request_Direction)(0), // 0: drive_solenoids.Drive_Request.Direction
	(Turn_Request_Direction)(0),  // 1: drive_solenoids.Turn_Request.Direction
	(*Drive_Request)(nil),        // 2: drive_solenoids.Drive_Request
	(*Drive_Reply)(nil),          // 3: drive_solenoids.Drive_Reply
	(*Turn_Request)(nil),         // 4: drive_solenoids.Turn_Request
	(*Turn_Reply)(nil),           // 5: drive_solenoids.Turn_Reply
	(*Request)(nil),              // 6: drive_solenoids.Request
	(*Reply)(nil),                // 7: drive_solenoids.Reply
}
var file_drive_solenoids_proto_depIdxs = []int32{
	0, // 0: drive_solenoids.Drive_Request.dir:type_name -> drive_solenoids.Drive_Request.Direction
	1, // 1: drive_solenoids.Turn_Request.dir:type_name -> drive_solenoids.Turn_Request.Direction
	2, // 2: drive_solenoids.Request.drive:type_name -> drive_solenoids.Drive_Request
	4, // 3: drive_solenoids.Request.turn:type_name -> drive_solenoids.Turn_Request
	3, // 4: drive_solenoids.Reply.drive:type_name -> drive_solenoids.Drive_Reply
	5, // 5: drive_solenoids.Reply.turn:type_name -> drive_solenoids.Turn_Reply
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_drive_solenoids_proto_init() }
func file_drive_solenoids_proto_init() {
	if File_drive_solenoids_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_drive_solenoids_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Drive_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_drive_solenoids_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Drive_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_drive_solenoids_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Turn_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_drive_solenoids_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Turn_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_drive_solenoids_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_drive_solenoids_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_drive_solenoids_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*Request_Drive)(nil),
		(*Request_Turn)(nil),
	}
	file_drive_solenoids_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Reply_Drive)(nil),
		(*Reply_Turn)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_drive_solenoids_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_drive_solenoids_proto_goTypes,
		DependencyIndexes: file_drive_solenoids_proto_depIdxs,
		EnumInfos:         file_drive_solenoids_proto_enumTypes,
		MessageInfos:      file_drive_solenoids_proto_msgTypes,
	}.Build()
	File_drive_solenoids_proto = out.File
	file_drive_solenoids_proto_rawDesc = nil
	file_drive_solenoids_proto_goTypes = nil
	file_drive_solenoids_proto_depIdxs = nil
}

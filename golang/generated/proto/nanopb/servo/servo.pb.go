// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: servo.proto

package servo

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	epos "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/epos"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	time "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/time"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GoToMode int32

const (
	GoToMode_IMMEDIATE  GoToMode = 0
	GoToMode_REACHED    GoToMode = 1
	GoToMode_SETTLED    GoToMode = 2
	GoToMode_TRAJECTORY GoToMode = 3
)

// Enum value maps for GoToMode.
var (
	GoToMode_name = map[int32]string{
		0: "IMMEDIATE",
		1: "REACHED",
		2: "SETTLED",
		3: "TRAJECTORY",
	}
	GoToMode_value = map[string]int32{
		"IMMEDIATE":  0,
		"REACHED":    1,
		"SETTLED":    2,
		"TRAJECTORY": 3,
	}
)

func (x GoToMode) Enum() *GoToMode {
	p := new(GoToMode)
	*p = x
	return p
}

func (x GoToMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoToMode) Descriptor() protoreflect.EnumDescriptor {
	return file_servo_proto_enumTypes[0].Descriptor()
}

func (GoToMode) Type() protoreflect.EnumType {
	return &file_servo_proto_enumTypes[0]
}

func (x GoToMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoToMode.Descriptor instead.
func (GoToMode) EnumDescriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{0}
}

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxProfileVelocity uint32 `protobuf:"varint,1,opt,name=max_profile_velocity,json=maxProfileVelocity,proto3" json:"max_profile_velocity,omitempty"`
	SettleWindow       uint32 `protobuf:"varint,2,opt,name=settle_window,json=settleWindow,proto3" json:"settle_window,omitempty"`
	SettleTimeout      uint32 `protobuf:"varint,3,opt,name=settle_timeout,json=settleTimeout,proto3" json:"settle_timeout,omitempty"`
	MaxDiffMillis      uint32 `protobuf:"varint,4,opt,name=max_diff_millis,json=maxDiffMillis,proto3" json:"max_diff_millis,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetMaxProfileVelocity() uint32 {
	if x != nil {
		return x.MaxProfileVelocity
	}
	return 0
}

func (x *Config) GetSettleWindow() uint32 {
	if x != nil {
		return x.SettleWindow
	}
	return 0
}

func (x *Config) GetSettleTimeout() uint32 {
	if x != nil {
		return x.SettleTimeout
	}
	return 0
}

func (x *Config) GetMaxDiffMillis() uint32 {
	if x != nil {
		return x.MaxDiffMillis
	}
	return 0
}

type Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId uint32  `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Config *Config `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Config_Request) Reset() {
	*x = Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config_Request) ProtoMessage() {}

func (x *Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config_Request.ProtoReflect.Descriptor instead.
func (*Config_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{1}
}

func (x *Config_Request) GetNodeId() uint32 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *Config_Request) GetConfig() *Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Boot_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params *epos.Home_Params `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *Boot_Request) Reset() {
	*x = Boot_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Boot_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Boot_Request) ProtoMessage() {}

func (x *Boot_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Boot_Request.ProtoReflect.Descriptor instead.
func (*Boot_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{2}
}

func (x *Boot_Request) GetParams() *epos.Home_Params {
	if x != nil {
		return x.Params
	}
	return nil
}

type Stop_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Stop_Request) Reset() {
	*x = Stop_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Stop_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stop_Request) ProtoMessage() {}

func (x *Stop_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stop_Request.ProtoReflect.Descriptor instead.
func (*Stop_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{3}
}

type Go_To_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position    int32  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	Velocity    uint32 `protobuf:"varint,2,opt,name=velocity,proto3" json:"velocity,omitempty"`
	AwaitSettle bool   `protobuf:"varint,3,opt,name=await_settle,json=awaitSettle,proto3" json:"await_settle,omitempty"`
}

func (x *Go_To_Request) Reset() {
	*x = Go_To_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Request) ProtoMessage() {}

func (x *Go_To_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{4}
}

func (x *Go_To_Request) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Go_To_Request) GetVelocity() uint32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

func (x *Go_To_Request) GetAwaitSettle() bool {
	if x != nil {
		return x.AwaitSettle
	}
	return false
}

type Get_Limits_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Limits_Request) Reset() {
	*x = Get_Limits_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Limits_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Limits_Request) ProtoMessage() {}

func (x *Get_Limits_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Limits_Request.ProtoReflect.Descriptor instead.
func (*Get_Limits_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{5}
}

type Go_To_Delta_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeltaPosition int32    `protobuf:"varint,1,opt,name=delta_position,json=deltaPosition,proto3" json:"delta_position,omitempty"`
	Velocity      uint32   `protobuf:"varint,2,opt,name=velocity,proto3" json:"velocity,omitempty"`
	Mode          GoToMode `protobuf:"varint,3,opt,name=mode,proto3,enum=servo.GoToMode" json:"mode,omitempty"`
}

func (x *Go_To_Delta_Request) Reset() {
	*x = Go_To_Delta_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Delta_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Delta_Request) ProtoMessage() {}

func (x *Go_To_Delta_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Delta_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Delta_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{6}
}

func (x *Go_To_Delta_Request) GetDeltaPosition() int32 {
	if x != nil {
		return x.DeltaPosition
	}
	return 0
}

func (x *Go_To_Delta_Request) GetVelocity() uint32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

func (x *Go_To_Delta_Request) GetMode() GoToMode {
	if x != nil {
		return x.Mode
	}
	return GoToMode_IMMEDIATE
}

type Go_To_Delta_Follow_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Delta                *Go_To_Delta_Request `protobuf:"bytes,1,opt,name=delta,proto3" json:"delta,omitempty"`
	FollowVelocityVector int32                `protobuf:"varint,2,opt,name=follow_velocity_vector,json=followVelocityVector,proto3" json:"follow_velocity_vector,omitempty"`
	FollowVelocityMrpm   uint32               `protobuf:"varint,3,opt,name=follow_velocity_mrpm,json=followVelocityMrpm,proto3" json:"follow_velocity_mrpm,omitempty"`
	IntervalSleepTimeMs  uint32               `protobuf:"varint,4,opt,name=interval_sleep_time_ms,json=intervalSleepTimeMs,proto3" json:"interval_sleep_time_ms,omitempty"`
	FastReturn           bool                 `protobuf:"varint,5,opt,name=fast_return,json=fastReturn,proto3" json:"fast_return,omitempty"`
}

func (x *Go_To_Delta_Follow_Request) Reset() {
	*x = Go_To_Delta_Follow_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Delta_Follow_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Delta_Follow_Request) ProtoMessage() {}

func (x *Go_To_Delta_Follow_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Delta_Follow_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Delta_Follow_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{7}
}

func (x *Go_To_Delta_Follow_Request) GetDelta() *Go_To_Delta_Request {
	if x != nil {
		return x.Delta
	}
	return nil
}

func (x *Go_To_Delta_Follow_Request) GetFollowVelocityVector() int32 {
	if x != nil {
		return x.FollowVelocityVector
	}
	return 0
}

func (x *Go_To_Delta_Follow_Request) GetFollowVelocityMrpm() uint32 {
	if x != nil {
		return x.FollowVelocityMrpm
	}
	return 0
}

func (x *Go_To_Delta_Follow_Request) GetIntervalSleepTimeMs() uint32 {
	if x != nil {
		return x.IntervalSleepTimeMs
	}
	return 0
}

func (x *Go_To_Delta_Follow_Request) GetFastReturn() bool {
	if x != nil {
		return x.FastReturn
	}
	return false
}

type Go_To_Calibrate_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position     int32  `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	Velocity     uint32 `protobuf:"varint,2,opt,name=velocity,proto3" json:"velocity,omitempty"`
	Window       uint32 `protobuf:"varint,3,opt,name=window,proto3" json:"window,omitempty"`
	TimeWindowMs uint32 `protobuf:"varint,4,opt,name=time_window_ms,json=timeWindowMs,proto3" json:"time_window_ms,omitempty"`
	TimeoutMs    uint32 `protobuf:"varint,5,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`
	PeriodMs     uint32 `protobuf:"varint,6,opt,name=period_ms,json=periodMs,proto3" json:"period_ms,omitempty"`
}

func (x *Go_To_Calibrate_Request) Reset() {
	*x = Go_To_Calibrate_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Calibrate_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Calibrate_Request) ProtoMessage() {}

func (x *Go_To_Calibrate_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Calibrate_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Calibrate_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{8}
}

func (x *Go_To_Calibrate_Request) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Go_To_Calibrate_Request) GetVelocity() uint32 {
	if x != nil {
		return x.Velocity
	}
	return 0
}

func (x *Go_To_Calibrate_Request) GetWindow() uint32 {
	if x != nil {
		return x.Window
	}
	return 0
}

func (x *Go_To_Calibrate_Request) GetTimeWindowMs() uint32 {
	if x != nil {
		return x.TimeWindowMs
	}
	return 0
}

func (x *Go_To_Calibrate_Request) GetTimeoutMs() uint32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

func (x *Go_To_Calibrate_Request) GetPeriodMs() uint32 {
	if x != nil {
		return x.PeriodMs
	}
	return 0
}

type Go_To_Timestamp_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp           *time.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Mode                GoToMode        `protobuf:"varint,2,opt,name=mode,proto3,enum=servo.GoToMode" json:"mode,omitempty"`
	Position            int32           `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`
	VelocityMrpm        uint32          `protobuf:"varint,4,opt,name=velocity_mrpm,json=velocityMrpm,proto3" json:"velocity_mrpm,omitempty"`
	FollowVelocity      int32           `protobuf:"varint,5,opt,name=follow_velocity,json=followVelocity,proto3" json:"follow_velocity,omitempty"`
	FollowAccel         int32           `protobuf:"varint,6,opt,name=follow_accel,json=followAccel,proto3" json:"follow_accel,omitempty"`
	IntervalSleepTimeMs uint32          `protobuf:"varint,7,opt,name=interval_sleep_time_ms,json=intervalSleepTimeMs,proto3" json:"interval_sleep_time_ms,omitempty"`
}

func (x *Go_To_Timestamp_Request) Reset() {
	*x = Go_To_Timestamp_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Timestamp_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Timestamp_Request) ProtoMessage() {}

func (x *Go_To_Timestamp_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Timestamp_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Timestamp_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{9}
}

func (x *Go_To_Timestamp_Request) GetTimestamp() *time.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Go_To_Timestamp_Request) GetMode() GoToMode {
	if x != nil {
		return x.Mode
	}
	return GoToMode_IMMEDIATE
}

func (x *Go_To_Timestamp_Request) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Go_To_Timestamp_Request) GetVelocityMrpm() uint32 {
	if x != nil {
		return x.VelocityMrpm
	}
	return 0
}

func (x *Go_To_Timestamp_Request) GetFollowVelocity() int32 {
	if x != nil {
		return x.FollowVelocity
	}
	return 0
}

func (x *Go_To_Timestamp_Request) GetFollowAccel() int32 {
	if x != nil {
		return x.FollowAccel
	}
	return 0
}

func (x *Go_To_Timestamp_Request) GetIntervalSleepTimeMs() uint32 {
	if x != nil {
		return x.IntervalSleepTimeMs
	}
	return 0
}

type Go_To_Follow_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp           *time.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Position            int32           `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`
	VelocityMrpm        uint32          `protobuf:"varint,3,opt,name=velocity_mrpm,json=velocityMrpm,proto3" json:"velocity_mrpm,omitempty"`
	FollowVelocity      int32           `protobuf:"varint,4,opt,name=follow_velocity,json=followVelocity,proto3" json:"follow_velocity,omitempty"`
	FollowAccel         int32           `protobuf:"varint,5,opt,name=follow_accel,json=followAccel,proto3" json:"follow_accel,omitempty"`
	IntervalSleepTimeMs uint32          `protobuf:"varint,6,opt,name=interval_sleep_time_ms,json=intervalSleepTimeMs,proto3" json:"interval_sleep_time_ms,omitempty"`
}

func (x *Go_To_Follow_Request) Reset() {
	*x = Go_To_Follow_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Follow_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Follow_Request) ProtoMessage() {}

func (x *Go_To_Follow_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Follow_Request.ProtoReflect.Descriptor instead.
func (*Go_To_Follow_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{10}
}

func (x *Go_To_Follow_Request) GetTimestamp() *time.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Go_To_Follow_Request) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *Go_To_Follow_Request) GetVelocityMrpm() uint32 {
	if x != nil {
		return x.VelocityMrpm
	}
	return 0
}

func (x *Go_To_Follow_Request) GetFollowVelocity() int32 {
	if x != nil {
		return x.FollowVelocity
	}
	return 0
}

func (x *Go_To_Follow_Request) GetFollowAccel() int32 {
	if x != nil {
		return x.FollowAccel
	}
	return 0
}

func (x *Go_To_Follow_Request) GetIntervalSleepTimeMs() uint32 {
	if x != nil {
		return x.IntervalSleepTimeMs
	}
	return 0
}

type Follow_Timestamp_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp      *time.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FollowVelocity int32           `protobuf:"varint,2,opt,name=follow_velocity,json=followVelocity,proto3" json:"follow_velocity,omitempty"`
	FollowAccel    int32           `protobuf:"varint,3,opt,name=follow_accel,json=followAccel,proto3" json:"follow_accel,omitempty"`
}

func (x *Follow_Timestamp_Request) Reset() {
	*x = Follow_Timestamp_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Follow_Timestamp_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Follow_Timestamp_Request) ProtoMessage() {}

func (x *Follow_Timestamp_Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Follow_Timestamp_Request.ProtoReflect.Descriptor instead.
func (*Follow_Timestamp_Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{11}
}

func (x *Follow_Timestamp_Request) GetTimestamp() *time.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Follow_Timestamp_Request) GetFollowVelocity() int32 {
	if x != nil {
		return x.FollowVelocity
	}
	return 0
}

func (x *Follow_Timestamp_Request) GetFollowAccel() int32 {
	if x != nil {
		return x.FollowAccel
	}
	return 0
}

type Limits_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min int32 `protobuf:"varint,1,opt,name=min,proto3" json:"min,omitempty"`
	Max int32 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *Limits_Reply) Reset() {
	*x = Limits_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Limits_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Limits_Reply) ProtoMessage() {}

func (x *Limits_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Limits_Reply.ProtoReflect.Descriptor instead.
func (*Limits_Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{12}
}

func (x *Limits_Reply) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *Limits_Reply) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

type Position_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position int32 `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
}

func (x *Position_Reply) Reset() {
	*x = Position_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Position_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position_Reply) ProtoMessage() {}

func (x *Position_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position_Reply.ProtoReflect.Descriptor instead.
func (*Position_Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{13}
}

func (x *Position_Reply) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

type Settle_Time_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SettleTime uint32 `protobuf:"varint,1,opt,name=settle_time,json=settleTime,proto3" json:"settle_time,omitempty"`
}

func (x *Settle_Time_Reply) Reset() {
	*x = Settle_Time_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settle_Time_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settle_Time_Reply) ProtoMessage() {}

func (x *Settle_Time_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settle_Time_Reply.ProtoReflect.Descriptor instead.
func (*Settle_Time_Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{14}
}

func (x *Settle_Time_Reply) GetSettleTime() uint32 {
	if x != nil {
		return x.SettleTime
	}
	return 0
}

type Go_To_Timestamp_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrePosition   int32           `protobuf:"varint,1,opt,name=pre_position,json=prePosition,proto3" json:"pre_position,omitempty"`
	PostPosition  int32           `protobuf:"varint,2,opt,name=post_position,json=postPosition,proto3" json:"post_position,omitempty"`
	PreTimestamp  *time.Timestamp `protobuf:"bytes,3,opt,name=pre_timestamp,json=preTimestamp,proto3" json:"pre_timestamp,omitempty"`
	PostTimestamp *time.Timestamp `protobuf:"bytes,4,opt,name=post_timestamp,json=postTimestamp,proto3" json:"post_timestamp,omitempty"`
}

func (x *Go_To_Timestamp_Reply) Reset() {
	*x = Go_To_Timestamp_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Timestamp_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Timestamp_Reply) ProtoMessage() {}

func (x *Go_To_Timestamp_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Timestamp_Reply.ProtoReflect.Descriptor instead.
func (*Go_To_Timestamp_Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{15}
}

func (x *Go_To_Timestamp_Reply) GetPrePosition() int32 {
	if x != nil {
		return x.PrePosition
	}
	return 0
}

func (x *Go_To_Timestamp_Reply) GetPostPosition() int32 {
	if x != nil {
		return x.PostPosition
	}
	return 0
}

func (x *Go_To_Timestamp_Reply) GetPreTimestamp() *time.Timestamp {
	if x != nil {
		return x.PreTimestamp
	}
	return nil
}

func (x *Go_To_Timestamp_Reply) GetPostTimestamp() *time.Timestamp {
	if x != nil {
		return x.PostTimestamp
	}
	return nil
}

type Follow_Timestamp_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrePosition  int32           `protobuf:"varint,1,opt,name=pre_position,json=prePosition,proto3" json:"pre_position,omitempty"`
	PreTimestamp *time.Timestamp `protobuf:"bytes,2,opt,name=pre_timestamp,json=preTimestamp,proto3" json:"pre_timestamp,omitempty"`
}

func (x *Follow_Timestamp_Reply) Reset() {
	*x = Follow_Timestamp_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Follow_Timestamp_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Follow_Timestamp_Reply) ProtoMessage() {}

func (x *Follow_Timestamp_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Follow_Timestamp_Reply.ProtoReflect.Descriptor instead.
func (*Follow_Timestamp_Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{16}
}

func (x *Follow_Timestamp_Reply) GetPrePosition() int32 {
	if x != nil {
		return x.PrePosition
	}
	return 0
}

func (x *Follow_Timestamp_Reply) GetPreTimestamp() *time.Timestamp {
	if x != nil {
		return x.PreTimestamp
	}
	return nil
}

type Go_To_Follow_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrePosition  int32           `protobuf:"varint,1,opt,name=pre_position,json=prePosition,proto3" json:"pre_position,omitempty"`
	PreTimestamp *time.Timestamp `protobuf:"bytes,2,opt,name=pre_timestamp,json=preTimestamp,proto3" json:"pre_timestamp,omitempty"`
}

func (x *Go_To_Follow_Reply) Reset() {
	*x = Go_To_Follow_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Go_To_Follow_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Go_To_Follow_Reply) ProtoMessage() {}

func (x *Go_To_Follow_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Go_To_Follow_Reply.ProtoReflect.Descriptor instead.
func (*Go_To_Follow_Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{17}
}

func (x *Go_To_Follow_Reply) GetPrePosition() int32 {
	if x != nil {
		return x.PrePosition
	}
	return 0
}

func (x *Go_To_Follow_Reply) GetPreTimestamp() *time.Timestamp {
	if x != nil {
		return x.PreTimestamp
	}
	return nil
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Config
	//	*Request_Boot
	//	*Request_Stop
	//	*Request_GoTo
	//	*Request_Limit
	//	*Request_Epos
	//	*Request_Delta
	//	*Request_Follow
	//	*Request_Calibrate
	//	*Request_GoToTimestamp
	//	*Request_FollowTimestamp
	//	*Request_GoToFollow
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{18}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetConfig() *Config_Request {
	if x, ok := x.GetRequest().(*Request_Config); ok {
		return x.Config
	}
	return nil
}

func (x *Request) GetBoot() *Boot_Request {
	if x, ok := x.GetRequest().(*Request_Boot); ok {
		return x.Boot
	}
	return nil
}

func (x *Request) GetStop() *Stop_Request {
	if x, ok := x.GetRequest().(*Request_Stop); ok {
		return x.Stop
	}
	return nil
}

func (x *Request) GetGoTo() *Go_To_Request {
	if x, ok := x.GetRequest().(*Request_GoTo); ok {
		return x.GoTo
	}
	return nil
}

func (x *Request) GetLimit() *Get_Limits_Request {
	if x, ok := x.GetRequest().(*Request_Limit); ok {
		return x.Limit
	}
	return nil
}

func (x *Request) GetEpos() *epos.Request {
	if x, ok := x.GetRequest().(*Request_Epos); ok {
		return x.Epos
	}
	return nil
}

func (x *Request) GetDelta() *Go_To_Delta_Request {
	if x, ok := x.GetRequest().(*Request_Delta); ok {
		return x.Delta
	}
	return nil
}

func (x *Request) GetFollow() *Go_To_Delta_Follow_Request {
	if x, ok := x.GetRequest().(*Request_Follow); ok {
		return x.Follow
	}
	return nil
}

func (x *Request) GetCalibrate() *Go_To_Calibrate_Request {
	if x, ok := x.GetRequest().(*Request_Calibrate); ok {
		return x.Calibrate
	}
	return nil
}

func (x *Request) GetGoToTimestamp() *Go_To_Timestamp_Request {
	if x, ok := x.GetRequest().(*Request_GoToTimestamp); ok {
		return x.GoToTimestamp
	}
	return nil
}

func (x *Request) GetFollowTimestamp() *Follow_Timestamp_Request {
	if x, ok := x.GetRequest().(*Request_FollowTimestamp); ok {
		return x.FollowTimestamp
	}
	return nil
}

func (x *Request) GetGoToFollow() *Go_To_Follow_Request {
	if x, ok := x.GetRequest().(*Request_GoToFollow); ok {
		return x.GoToFollow
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Config struct {
	Config *Config_Request `protobuf:"bytes,1,opt,name=config,proto3,oneof"`
}

type Request_Boot struct {
	Boot *Boot_Request `protobuf:"bytes,2,opt,name=boot,proto3,oneof"`
}

type Request_Stop struct {
	Stop *Stop_Request `protobuf:"bytes,3,opt,name=stop,proto3,oneof"`
}

type Request_GoTo struct {
	GoTo *Go_To_Request `protobuf:"bytes,4,opt,name=go_to,json=goTo,proto3,oneof"`
}

type Request_Limit struct {
	Limit *Get_Limits_Request `protobuf:"bytes,5,opt,name=limit,proto3,oneof"`
}

type Request_Epos struct {
	Epos *epos.Request `protobuf:"bytes,6,opt,name=epos,proto3,oneof"`
}

type Request_Delta struct {
	Delta *Go_To_Delta_Request `protobuf:"bytes,7,opt,name=delta,proto3,oneof"`
}

type Request_Follow struct {
	Follow *Go_To_Delta_Follow_Request `protobuf:"bytes,8,opt,name=follow,proto3,oneof"`
}

type Request_Calibrate struct {
	Calibrate *Go_To_Calibrate_Request `protobuf:"bytes,9,opt,name=calibrate,proto3,oneof"`
}

type Request_GoToTimestamp struct {
	GoToTimestamp *Go_To_Timestamp_Request `protobuf:"bytes,10,opt,name=go_to_timestamp,json=goToTimestamp,proto3,oneof"`
}

type Request_FollowTimestamp struct {
	FollowTimestamp *Follow_Timestamp_Request `protobuf:"bytes,11,opt,name=follow_timestamp,json=followTimestamp,proto3,oneof"`
}

type Request_GoToFollow struct {
	GoToFollow *Go_To_Follow_Request `protobuf:"bytes,12,opt,name=go_to_follow,json=goToFollow,proto3,oneof"`
}

func (*Request_Config) isRequest_Request() {}

func (*Request_Boot) isRequest_Request() {}

func (*Request_Stop) isRequest_Request() {}

func (*Request_GoTo) isRequest_Request() {}

func (*Request_Limit) isRequest_Request() {}

func (*Request_Epos) isRequest_Request() {}

func (*Request_Delta) isRequest_Request() {}

func (*Request_Follow) isRequest_Request() {}

func (*Request_Calibrate) isRequest_Request() {}

func (*Request_GoToTimestamp) isRequest_Request() {}

func (*Request_FollowTimestamp) isRequest_Request() {}

func (*Request_GoToFollow) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Limit
	//	*Reply_Epos
	//	*Reply_Pos
	//	*Reply_Settle
	//	*Reply_GoToTimestamp
	//	*Reply_FollowTimestamp
	//	*Reply_GoToFollow
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servo_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_servo_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_servo_proto_rawDescGZIP(), []int{19}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetLimit() *Limits_Reply {
	if x, ok := x.GetReply().(*Reply_Limit); ok {
		return x.Limit
	}
	return nil
}

func (x *Reply) GetEpos() *epos.Reply {
	if x, ok := x.GetReply().(*Reply_Epos); ok {
		return x.Epos
	}
	return nil
}

func (x *Reply) GetPos() *Position_Reply {
	if x, ok := x.GetReply().(*Reply_Pos); ok {
		return x.Pos
	}
	return nil
}

func (x *Reply) GetSettle() *Settle_Time_Reply {
	if x, ok := x.GetReply().(*Reply_Settle); ok {
		return x.Settle
	}
	return nil
}

func (x *Reply) GetGoToTimestamp() *Go_To_Timestamp_Reply {
	if x, ok := x.GetReply().(*Reply_GoToTimestamp); ok {
		return x.GoToTimestamp
	}
	return nil
}

func (x *Reply) GetFollowTimestamp() *Follow_Timestamp_Reply {
	if x, ok := x.GetReply().(*Reply_FollowTimestamp); ok {
		return x.FollowTimestamp
	}
	return nil
}

func (x *Reply) GetGoToFollow() *Go_To_Follow_Reply {
	if x, ok := x.GetReply().(*Reply_GoToFollow); ok {
		return x.GoToFollow
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Limit struct {
	Limit *Limits_Reply `protobuf:"bytes,3,opt,name=limit,proto3,oneof"`
}

type Reply_Epos struct {
	Epos *epos.Reply `protobuf:"bytes,4,opt,name=epos,proto3,oneof"`
}

type Reply_Pos struct {
	Pos *Position_Reply `protobuf:"bytes,5,opt,name=pos,proto3,oneof"`
}

type Reply_Settle struct {
	Settle *Settle_Time_Reply `protobuf:"bytes,6,opt,name=settle,proto3,oneof"`
}

type Reply_GoToTimestamp struct {
	GoToTimestamp *Go_To_Timestamp_Reply `protobuf:"bytes,7,opt,name=go_to_timestamp,json=goToTimestamp,proto3,oneof"`
}

type Reply_FollowTimestamp struct {
	FollowTimestamp *Follow_Timestamp_Reply `protobuf:"bytes,8,opt,name=follow_timestamp,json=followTimestamp,proto3,oneof"`
}

type Reply_GoToFollow struct {
	GoToFollow *Go_To_Follow_Reply `protobuf:"bytes,9,opt,name=go_to_follow,json=goToFollow,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Limit) isReply_Reply() {}

func (*Reply_Epos) isReply_Reply() {}

func (*Reply_Pos) isReply_Reply() {}

func (*Reply_Settle) isReply_Reply() {}

func (*Reply_GoToTimestamp) isReply_Reply() {}

func (*Reply_FollowTimestamp) isReply_Reply() {}

func (*Reply_GoToFollow) isReply_Reply() {}

var File_servo_proto protoreflect.FileDescriptor

var file_servo_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x73,
	0x65, 0x72, 0x76, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f,
	0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xae, 0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x6d, 0x61, 0x78, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x57, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78,
	0x5f, 0x64, 0x69, 0x66, 0x66, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x44, 0x69, 0x66, 0x66, 0x4d, 0x69, 0x6c, 0x6c, 0x69,
	0x73, 0x22, 0x50, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0x39, 0x0a, 0x0c, 0x42, 0x6f, 0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x5f,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x0e,
	0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6a,
	0x0a, 0x0d, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x77, 0x61, 0x69, 0x74,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61,
	0x77, 0x61, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x5f, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x7d, 0x0a, 0x13, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x6c, 0x74, 0x61,
	0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f,
	0x2e, 0x47, 0x6f, 0x54, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22,
	0x8c, 0x02, 0x0a, 0x1a, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x44, 0x65, 0x6c, 0x74,
	0x61, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61,
	0x12, 0x34, 0x0a, 0x16, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x5f, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x14, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x72, 0x70, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x4d, 0x72, 0x70, 0x6d, 0x12, 0x33, 0x0a, 0x16, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x53, 0x6c, 0x65, 0x65, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x66, 0x61, 0x73, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x22, 0xcb,
	0x01, 0x0a, 0x17, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x4d, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x4d, 0x73, 0x22, 0xaf, 0x02, 0x0a,
	0x17, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f,
	0x54, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x72, 0x70, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x4d, 0x72, 0x70, 0x6d, 0x12, 0x27, 0x0a,
	0x0f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x56, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x16, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x53, 0x6c, 0x65, 0x65, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x22, 0x87,
	0x02, 0x0a, 0x14, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d,
	0x72, 0x70, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x4d, 0x72, 0x70, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x63,
	0x63, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f,
	0x73, 0x6c, 0x65, 0x65, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x53, 0x6c, 0x65,
	0x65, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x18, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a,
	0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x63, 0x63, 0x65, 0x6c,
	0x22, 0x32, 0x0a, 0x0c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d,
	0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6d, 0x61, 0x78, 0x22, 0x2c, 0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x54, 0x69, 0x6d,
	0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x15, 0x47, 0x6f, 0x5f,
	0x54, 0x6f, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x6f,
	0x73, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0d, 0x70, 0x72,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x36, 0x0a, 0x0e, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x70, 0x6f, 0x73, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x71, 0x0a, 0x16, 0x46, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x70,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x6d, 0x0a, 0x12, 0x47,
	0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xaa, 0x05, 0x0a, 0x07, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x42, 0x6f,
	0x6f, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x62, 0x6f,
	0x6f, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x2b, 0x0a,
	0x05, 0x67, 0x6f, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x67, 0x6f, 0x54, 0x6f, 0x12, 0x31, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x23, 0x0a,
	0x04, 0x65, 0x70, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x65, 0x70,
	0x6f, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x65, 0x70,
	0x6f, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f,
	0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x06, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47,
	0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x66, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x12, 0x3e, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47,
	0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x0f, 0x67, 0x6f, 0x5f, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0d,
	0x67, 0x6f, 0x54, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x4c, 0x0a,
	0x10, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3f, 0x0a, 0x0c, 0x67,
	0x6f, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x0a, 0x67, 0x6f, 0x54, 0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x09, 0x0a, 0x07,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xd6, 0x03, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00,
	0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x2b, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x21, 0x0a, 0x04, 0x65, 0x70, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x65, 0x70, 0x6f, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x04, 0x65, 0x70, 0x6f, 0x73, 0x12, 0x29, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x70, 0x6f, 0x73,
	0x12, 0x32, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f,
	0x54, 0x69, 0x6d, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x67, 0x6f, 0x5f, 0x74, 0x6f, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0d, 0x67,
	0x6f, 0x54, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x4a, 0x0a, 0x10,
	0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3d, 0x0a, 0x0c, 0x67, 0x6f, 0x5f, 0x74,
	0x6f, 0x5f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x2e, 0x47, 0x6f, 0x5f, 0x54, 0x6f, 0x5f, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x6f, 0x54,
	0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79,
	0x2a, 0x43, 0x0a, 0x08, 0x47, 0x6f, 0x54, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0d, 0x0a, 0x09,
	0x49, 0x4d, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52,
	0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x54, 0x54,
	0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x52, 0x41, 0x4a, 0x45, 0x43, 0x54,
	0x4f, 0x52, 0x59, 0x10, 0x03, 0x42, 0x0e, 0x5a, 0x0c, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_servo_proto_rawDescOnce sync.Once
	file_servo_proto_rawDescData = file_servo_proto_rawDesc
)

func file_servo_proto_rawDescGZIP() []byte {
	file_servo_proto_rawDescOnce.Do(func() {
		file_servo_proto_rawDescData = protoimpl.X.CompressGZIP(file_servo_proto_rawDescData)
	})
	return file_servo_proto_rawDescData
}

var file_servo_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_servo_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_servo_proto_goTypes = []interface{}{
	(GoToMode)(0),                      // 0: servo.GoToMode
	(*Config)(nil),                     // 1: servo.Config
	(*Config_Request)(nil),             // 2: servo.Config_Request
	(*Boot_Request)(nil),               // 3: servo.Boot_Request
	(*Stop_Request)(nil),               // 4: servo.Stop_Request
	(*Go_To_Request)(nil),              // 5: servo.Go_To_Request
	(*Get_Limits_Request)(nil),         // 6: servo.Get_Limits_Request
	(*Go_To_Delta_Request)(nil),        // 7: servo.Go_To_Delta_Request
	(*Go_To_Delta_Follow_Request)(nil), // 8: servo.Go_To_Delta_Follow_Request
	(*Go_To_Calibrate_Request)(nil),    // 9: servo.Go_To_Calibrate_Request
	(*Go_To_Timestamp_Request)(nil),    // 10: servo.Go_To_Timestamp_Request
	(*Go_To_Follow_Request)(nil),       // 11: servo.Go_To_Follow_Request
	(*Follow_Timestamp_Request)(nil),   // 12: servo.Follow_Timestamp_Request
	(*Limits_Reply)(nil),               // 13: servo.Limits_Reply
	(*Position_Reply)(nil),             // 14: servo.Position_Reply
	(*Settle_Time_Reply)(nil),          // 15: servo.Settle_Time_Reply
	(*Go_To_Timestamp_Reply)(nil),      // 16: servo.Go_To_Timestamp_Reply
	(*Follow_Timestamp_Reply)(nil),     // 17: servo.Follow_Timestamp_Reply
	(*Go_To_Follow_Reply)(nil),         // 18: servo.Go_To_Follow_Reply
	(*Request)(nil),                    // 19: servo.Request
	(*Reply)(nil),                      // 20: servo.Reply
	(*epos.Home_Params)(nil),           // 21: epos.Home_Params
	(*time.Timestamp)(nil),             // 22: time.Timestamp
	(*epos.Request)(nil),               // 23: epos.Request
	(*error1.Error)(nil),               // 24: error.Error
	(*ack.Ack)(nil),                    // 25: ack.Ack
	(*epos.Reply)(nil),                 // 26: epos.Reply
}
var file_servo_proto_depIdxs = []int32{
	1,  // 0: servo.Config_Request.config:type_name -> servo.Config
	21, // 1: servo.Boot_Request.params:type_name -> epos.Home_Params
	0,  // 2: servo.Go_To_Delta_Request.mode:type_name -> servo.GoToMode
	7,  // 3: servo.Go_To_Delta_Follow_Request.delta:type_name -> servo.Go_To_Delta_Request
	22, // 4: servo.Go_To_Timestamp_Request.timestamp:type_name -> time.Timestamp
	0,  // 5: servo.Go_To_Timestamp_Request.mode:type_name -> servo.GoToMode
	22, // 6: servo.Go_To_Follow_Request.timestamp:type_name -> time.Timestamp
	22, // 7: servo.Follow_Timestamp_Request.timestamp:type_name -> time.Timestamp
	22, // 8: servo.Go_To_Timestamp_Reply.pre_timestamp:type_name -> time.Timestamp
	22, // 9: servo.Go_To_Timestamp_Reply.post_timestamp:type_name -> time.Timestamp
	22, // 10: servo.Follow_Timestamp_Reply.pre_timestamp:type_name -> time.Timestamp
	22, // 11: servo.Go_To_Follow_Reply.pre_timestamp:type_name -> time.Timestamp
	2,  // 12: servo.Request.config:type_name -> servo.Config_Request
	3,  // 13: servo.Request.boot:type_name -> servo.Boot_Request
	4,  // 14: servo.Request.stop:type_name -> servo.Stop_Request
	5,  // 15: servo.Request.go_to:type_name -> servo.Go_To_Request
	6,  // 16: servo.Request.limit:type_name -> servo.Get_Limits_Request
	23, // 17: servo.Request.epos:type_name -> epos.Request
	7,  // 18: servo.Request.delta:type_name -> servo.Go_To_Delta_Request
	8,  // 19: servo.Request.follow:type_name -> servo.Go_To_Delta_Follow_Request
	9,  // 20: servo.Request.calibrate:type_name -> servo.Go_To_Calibrate_Request
	10, // 21: servo.Request.go_to_timestamp:type_name -> servo.Go_To_Timestamp_Request
	12, // 22: servo.Request.follow_timestamp:type_name -> servo.Follow_Timestamp_Request
	11, // 23: servo.Request.go_to_follow:type_name -> servo.Go_To_Follow_Request
	24, // 24: servo.Reply.error:type_name -> error.Error
	25, // 25: servo.Reply.ack:type_name -> ack.Ack
	13, // 26: servo.Reply.limit:type_name -> servo.Limits_Reply
	26, // 27: servo.Reply.epos:type_name -> epos.Reply
	14, // 28: servo.Reply.pos:type_name -> servo.Position_Reply
	15, // 29: servo.Reply.settle:type_name -> servo.Settle_Time_Reply
	16, // 30: servo.Reply.go_to_timestamp:type_name -> servo.Go_To_Timestamp_Reply
	17, // 31: servo.Reply.follow_timestamp:type_name -> servo.Follow_Timestamp_Reply
	18, // 32: servo.Reply.go_to_follow:type_name -> servo.Go_To_Follow_Reply
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_servo_proto_init() }
func file_servo_proto_init() {
	if File_servo_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_servo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Boot_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Stop_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Limits_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Delta_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Delta_Follow_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Calibrate_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Timestamp_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Follow_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Follow_Timestamp_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Limits_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Position_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Settle_Time_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Timestamp_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Follow_Timestamp_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Go_To_Follow_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servo_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_servo_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*Request_Config)(nil),
		(*Request_Boot)(nil),
		(*Request_Stop)(nil),
		(*Request_GoTo)(nil),
		(*Request_Limit)(nil),
		(*Request_Epos)(nil),
		(*Request_Delta)(nil),
		(*Request_Follow)(nil),
		(*Request_Calibrate)(nil),
		(*Request_GoToTimestamp)(nil),
		(*Request_FollowTimestamp)(nil),
		(*Request_GoToFollow)(nil),
	}
	file_servo_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Limit)(nil),
		(*Reply_Epos)(nil),
		(*Reply_Pos)(nil),
		(*Reply_Settle)(nil),
		(*Reply_GoToTimestamp)(nil),
		(*Reply_FollowTimestamp)(nil),
		(*Reply_GoToFollow)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_servo_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_servo_proto_goTypes,
		DependencyIndexes: file_servo_proto_depIdxs,
		EnumInfos:         file_servo_proto_enumTypes,
		MessageInfos:      file_servo_proto_msgTypes,
	}.Build()
	File_servo_proto = out.File
	file_servo_proto_rawDesc = nil
	file_servo_proto_goTypes = nil
	file_servo_proto_depIdxs = nil
}

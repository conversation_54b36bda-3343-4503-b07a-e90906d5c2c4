// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: slayertb_simulator.proto

package slayertb_simulator

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Sensor int32

const (
	Sensor_LIFTED              Sensor = 0
	Sensor_LEFT_ESTOP          Sensor = 1
	Sensor_RIGHT_ESTOP         Sensor = 2
	Sensor_CAB_ESTOP           Sensor = 3
	Sensor_LASER_KEY           Sensor = 4
	Sensor_LASER_INTERLOCK     Sensor = 5
	Sensor_WATER_PROTECT       Sensor = 6
	Sensor_TRACTOR_POWER       Sensor = 7
	Sensor_TEMP                Sensor = 8
	Sensor_HUMIDITY            Sensor = 9
	Sensor_BATTERY_VOLTAGE     Sensor = 10
	Sensor_CHILLER_RUN_SIGNAL  Sensor = 11
	Sensor_CAB_ESTOP_SIGNAL    Sensor = 12
	Sensor_BEACON_LEFT_SIGNAL  Sensor = 13
	Sensor_BEACON_RIGHT_SIGNAL Sensor = 14
)

// Enum value maps for Sensor.
var (
	Sensor_name = map[int32]string{
		0:  "LIFTED",
		1:  "LEFT_ESTOP",
		2:  "RIGHT_ESTOP",
		3:  "CAB_ESTOP",
		4:  "LASER_KEY",
		5:  "LASER_INTERLOCK",
		6:  "WATER_PROTECT",
		7:  "TRACTOR_POWER",
		8:  "TEMP",
		9:  "HUMIDITY",
		10: "BATTERY_VOLTAGE",
		11: "CHILLER_RUN_SIGNAL",
		12: "CAB_ESTOP_SIGNAL",
		13: "BEACON_LEFT_SIGNAL",
		14: "BEACON_RIGHT_SIGNAL",
	}
	Sensor_value = map[string]int32{
		"LIFTED":              0,
		"LEFT_ESTOP":          1,
		"RIGHT_ESTOP":         2,
		"CAB_ESTOP":           3,
		"LASER_KEY":           4,
		"LASER_INTERLOCK":     5,
		"WATER_PROTECT":       6,
		"TRACTOR_POWER":       7,
		"TEMP":                8,
		"HUMIDITY":            9,
		"BATTERY_VOLTAGE":     10,
		"CHILLER_RUN_SIGNAL":  11,
		"CAB_ESTOP_SIGNAL":    12,
		"BEACON_LEFT_SIGNAL":  13,
		"BEACON_RIGHT_SIGNAL": 14,
	}
)

func (x Sensor) Enum() *Sensor {
	p := new(Sensor)
	*p = x
	return p
}

func (x Sensor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Sensor) Descriptor() protoreflect.EnumDescriptor {
	return file_slayertb_simulator_proto_enumTypes[0].Descriptor()
}

func (Sensor) Type() protoreflect.EnumType {
	return &file_slayertb_simulator_proto_enumTypes[0]
}

func (x Sensor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Sensor.Descriptor instead.
func (Sensor) EnumDescriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{0}
}

type GetValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sensor Sensor `protobuf:"varint,1,opt,name=sensor,proto3,enum=slayertb_simulator.Sensor" json:"sensor,omitempty"`
}

func (x *GetValueRequest) Reset() {
	*x = GetValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValueRequest) ProtoMessage() {}

func (x *GetValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValueRequest.ProtoReflect.Descriptor instead.
func (*GetValueRequest) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{0}
}

func (x *GetValueRequest) GetSensor() Sensor {
	if x != nil {
		return x.Sensor
	}
	return Sensor_LIFTED
}

type GetValueReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*GetValueReply_BooleanValue
	//	*GetValueReply_IntValue
	Value isGetValueReply_Value `protobuf_oneof:"value"`
}

func (x *GetValueReply) Reset() {
	*x = GetValueReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValueReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValueReply) ProtoMessage() {}

func (x *GetValueReply) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValueReply.ProtoReflect.Descriptor instead.
func (*GetValueReply) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{1}
}

func (m *GetValueReply) GetValue() isGetValueReply_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *GetValueReply) GetBooleanValue() bool {
	if x, ok := x.GetValue().(*GetValueReply_BooleanValue); ok {
		return x.BooleanValue
	}
	return false
}

func (x *GetValueReply) GetIntValue() int32 {
	if x, ok := x.GetValue().(*GetValueReply_IntValue); ok {
		return x.IntValue
	}
	return 0
}

type isGetValueReply_Value interface {
	isGetValueReply_Value()
}

type GetValueReply_BooleanValue struct {
	BooleanValue bool `protobuf:"varint,1,opt,name=boolean_value,json=booleanValue,proto3,oneof"`
}

type GetValueReply_IntValue struct {
	IntValue int32 `protobuf:"varint,2,opt,name=int_value,json=intValue,proto3,oneof"`
}

func (*GetValueReply_BooleanValue) isGetValueReply_Value() {}

func (*GetValueReply_IntValue) isGetValueReply_Value() {}

type SetValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sensor Sensor `protobuf:"varint,1,opt,name=sensor,proto3,enum=slayertb_simulator.Sensor" json:"sensor,omitempty"`
	// Types that are assignable to Value:
	//
	//	*SetValueRequest_BooleanValue
	//	*SetValueRequest_IntValue
	Value isSetValueRequest_Value `protobuf_oneof:"value"`
}

func (x *SetValueRequest) Reset() {
	*x = SetValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetValueRequest) ProtoMessage() {}

func (x *SetValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetValueRequest.ProtoReflect.Descriptor instead.
func (*SetValueRequest) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{2}
}

func (x *SetValueRequest) GetSensor() Sensor {
	if x != nil {
		return x.Sensor
	}
	return Sensor_LIFTED
}

func (m *SetValueRequest) GetValue() isSetValueRequest_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *SetValueRequest) GetBooleanValue() bool {
	if x, ok := x.GetValue().(*SetValueRequest_BooleanValue); ok {
		return x.BooleanValue
	}
	return false
}

func (x *SetValueRequest) GetIntValue() int32 {
	if x, ok := x.GetValue().(*SetValueRequest_IntValue); ok {
		return x.IntValue
	}
	return 0
}

type isSetValueRequest_Value interface {
	isSetValueRequest_Value()
}

type SetValueRequest_BooleanValue struct {
	BooleanValue bool `protobuf:"varint,2,opt,name=boolean_value,json=booleanValue,proto3,oneof"`
}

type SetValueRequest_IntValue struct {
	IntValue int32 `protobuf:"varint,3,opt,name=int_value,json=intValue,proto3,oneof"`
}

func (*SetValueRequest_BooleanValue) isSetValueRequest_Value() {}

func (*SetValueRequest_IntValue) isSetValueRequest_Value() {}

type SetValueReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetValueReply) Reset() {
	*x = SetValueReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetValueReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetValueReply) ProtoMessage() {}

func (x *SetValueReply) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetValueReply.ProtoReflect.Descriptor instead.
func (*SetValueReply) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{3}
}

type UseTempSensorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	On bool `protobuf:"varint,1,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *UseTempSensorRequest) Reset() {
	*x = UseTempSensorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseTempSensorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseTempSensorRequest) ProtoMessage() {}

func (x *UseTempSensorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseTempSensorRequest.ProtoReflect.Descriptor instead.
func (*UseTempSensorRequest) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{4}
}

func (x *UseTempSensorRequest) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

type UseTempSensorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UseTempSensorReply) Reset() {
	*x = UseTempSensorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseTempSensorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseTempSensorReply) ProtoMessage() {}

func (x *UseTempSensorReply) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseTempSensorReply.ProtoReflect.Descriptor instead.
func (*UseTempSensorReply) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{5}
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_GetValue
	//	*Reply_SetValue
	//	*Reply_UseTempSensor
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{6}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetGetValue() *GetValueReply {
	if x, ok := x.GetReply().(*Reply_GetValue); ok {
		return x.GetValue
	}
	return nil
}

func (x *Reply) GetSetValue() *SetValueReply {
	if x, ok := x.GetReply().(*Reply_SetValue); ok {
		return x.SetValue
	}
	return nil
}

func (x *Reply) GetUseTempSensor() *UseTempSensorReply {
	if x, ok := x.GetReply().(*Reply_UseTempSensor); ok {
		return x.UseTempSensor
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_GetValue struct {
	GetValue *GetValueReply `protobuf:"bytes,3,opt,name=get_value,json=getValue,proto3,oneof"`
}

type Reply_SetValue struct {
	SetValue *SetValueReply `protobuf:"bytes,4,opt,name=set_value,json=setValue,proto3,oneof"`
}

type Reply_UseTempSensor struct {
	UseTempSensor *UseTempSensorReply `protobuf:"bytes,5,opt,name=use_temp_sensor,json=useTempSensor,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_GetValue) isReply_Reply() {}

func (*Reply_SetValue) isReply_Reply() {}

func (*Reply_UseTempSensor) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_GetValue
	//	*Request_SetValue
	//	*Request_UseTempSensor
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slayertb_simulator_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_slayertb_simulator_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_slayertb_simulator_proto_rawDescGZIP(), []int{7}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetGetValue() *GetValueRequest {
	if x, ok := x.GetRequest().(*Request_GetValue); ok {
		return x.GetValue
	}
	return nil
}

func (x *Request) GetSetValue() *SetValueRequest {
	if x, ok := x.GetRequest().(*Request_SetValue); ok {
		return x.SetValue
	}
	return nil
}

func (x *Request) GetUseTempSensor() *UseTempSensorRequest {
	if x, ok := x.GetRequest().(*Request_UseTempSensor); ok {
		return x.UseTempSensor
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_GetValue struct {
	GetValue *GetValueRequest `protobuf:"bytes,3,opt,name=get_value,json=getValue,proto3,oneof"`
}

type Request_SetValue struct {
	SetValue *SetValueRequest `protobuf:"bytes,4,opt,name=set_value,json=setValue,proto3,oneof"`
}

type Request_UseTempSensor struct {
	UseTempSensor *UseTempSensorRequest `protobuf:"bytes,5,opt,name=use_temp_sensor,json=useTempSensor,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_GetValue) isRequest_Request() {}

func (*Request_SetValue) isRequest_Request() {}

func (*Request_UseTempSensor) isRequest_Request() {}

var File_slayertb_simulator_proto protoreflect.FileDescriptor

var file_slayertb_simulator_proto_rawDesc = []byte{
	0x0a, 0x18, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x73, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x33,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x45, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x73, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x22, 0x5e, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x25, 0x0a,
	0x0d, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x94, 0x01, 0x0a,
	0x0f, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x06, 0x73, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0d, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x62,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x09, 0x69,
	0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x53,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6e, 0x22, 0x14, 0x0a, 0x12,
	0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0xbe, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04,
	0x70, 0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04,
	0x70, 0x6f, 0x6e, 0x67, 0x12, 0x40, 0x0a, 0x09, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08, 0x67, 0x65,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x73, 0x65, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x73, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08,
	0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x73, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0xc8, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48,
	0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x42, 0x0a, 0x09, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x08, 0x67, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x73,
	0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x52, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x73, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x74, 0x62, 0x5f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x73,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2a, 0x9a,
	0x02, 0x0a, 0x06, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x49, 0x46,
	0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x45, 0x53,
	0x54, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x45,
	0x53, 0x54, 0x4f, 0x50, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x42, 0x5f, 0x45, 0x53,
	0x54, 0x4f, 0x50, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x5f, 0x4b,
	0x45, 0x59, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x41, 0x54,
	0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d,
	0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x10, 0x07, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x45, 0x4d, 0x50, 0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x55, 0x4d,
	0x49, 0x44, 0x49, 0x54, 0x59, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x41, 0x54, 0x54, 0x45,
	0x52, 0x59, 0x5f, 0x56, 0x4f, 0x4c, 0x54, 0x41, 0x47, 0x45, 0x10, 0x0a, 0x12, 0x16, 0x0a, 0x12,
	0x43, 0x48, 0x49, 0x4c, 0x4c, 0x45, 0x52, 0x5f, 0x52, 0x55, 0x4e, 0x5f, 0x53, 0x49, 0x47, 0x4e,
	0x41, 0x4c, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x42, 0x5f, 0x45, 0x53, 0x54, 0x4f,
	0x50, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x10, 0x0c, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x45,
	0x41, 0x43, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x45, 0x41, 0x43, 0x4f, 0x4e, 0x5f, 0x52, 0x49, 0x47,
	0x48, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x10, 0x0e, 0x42, 0x1b, 0x5a, 0x19, 0x6e,
	0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x74, 0x62, 0x5f, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_slayertb_simulator_proto_rawDescOnce sync.Once
	file_slayertb_simulator_proto_rawDescData = file_slayertb_simulator_proto_rawDesc
)

func file_slayertb_simulator_proto_rawDescGZIP() []byte {
	file_slayertb_simulator_proto_rawDescOnce.Do(func() {
		file_slayertb_simulator_proto_rawDescData = protoimpl.X.CompressGZIP(file_slayertb_simulator_proto_rawDescData)
	})
	return file_slayertb_simulator_proto_rawDescData
}

var file_slayertb_simulator_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_slayertb_simulator_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_slayertb_simulator_proto_goTypes = []interface{}{
	(Sensor)(0),                   // 0: slayertb_simulator.Sensor
	(*GetValueRequest)(nil),       // 1: slayertb_simulator.GetValueRequest
	(*GetValueReply)(nil),         // 2: slayertb_simulator.GetValueReply
	(*SetValueRequest)(nil),       // 3: slayertb_simulator.SetValueRequest
	(*SetValueReply)(nil),         // 4: slayertb_simulator.SetValueReply
	(*UseTempSensorRequest)(nil),  // 5: slayertb_simulator.UseTempSensorRequest
	(*UseTempSensorReply)(nil),    // 6: slayertb_simulator.UseTempSensorReply
	(*Reply)(nil),                 // 7: slayertb_simulator.Reply
	(*Request)(nil),               // 8: slayertb_simulator.Request
	(*request.RequestHeader)(nil), // 9: request.RequestHeader
	(*diagnostic.Pong)(nil),       // 10: diagnostic.Pong
	(*diagnostic.Ping)(nil),       // 11: diagnostic.Ping
}
var file_slayertb_simulator_proto_depIdxs = []int32{
	0,  // 0: slayertb_simulator.GetValueRequest.sensor:type_name -> slayertb_simulator.Sensor
	0,  // 1: slayertb_simulator.SetValueRequest.sensor:type_name -> slayertb_simulator.Sensor
	9,  // 2: slayertb_simulator.Reply.header:type_name -> request.RequestHeader
	10, // 3: slayertb_simulator.Reply.pong:type_name -> diagnostic.Pong
	2,  // 4: slayertb_simulator.Reply.get_value:type_name -> slayertb_simulator.GetValueReply
	4,  // 5: slayertb_simulator.Reply.set_value:type_name -> slayertb_simulator.SetValueReply
	6,  // 6: slayertb_simulator.Reply.use_temp_sensor:type_name -> slayertb_simulator.UseTempSensorReply
	9,  // 7: slayertb_simulator.Request.header:type_name -> request.RequestHeader
	11, // 8: slayertb_simulator.Request.ping:type_name -> diagnostic.Ping
	1,  // 9: slayertb_simulator.Request.get_value:type_name -> slayertb_simulator.GetValueRequest
	3,  // 10: slayertb_simulator.Request.set_value:type_name -> slayertb_simulator.SetValueRequest
	5,  // 11: slayertb_simulator.Request.use_temp_sensor:type_name -> slayertb_simulator.UseTempSensorRequest
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_slayertb_simulator_proto_init() }
func file_slayertb_simulator_proto_init() {
	if File_slayertb_simulator_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_slayertb_simulator_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValueReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetValueReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseTempSensorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseTempSensorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slayertb_simulator_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_slayertb_simulator_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*GetValueReply_BooleanValue)(nil),
		(*GetValueReply_IntValue)(nil),
	}
	file_slayertb_simulator_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*SetValueRequest_BooleanValue)(nil),
		(*SetValueRequest_IntValue)(nil),
	}
	file_slayertb_simulator_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_GetValue)(nil),
		(*Reply_SetValue)(nil),
		(*Reply_UseTempSensor)(nil),
	}
	file_slayertb_simulator_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_GetValue)(nil),
		(*Request_SetValue)(nil),
		(*Request_UseTempSensor)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_slayertb_simulator_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_slayertb_simulator_proto_goTypes,
		DependencyIndexes: file_slayertb_simulator_proto_depIdxs,
		EnumInfos:         file_slayertb_simulator_proto_enumTypes,
		MessageInfos:      file_slayertb_simulator_proto_msgTypes,
	}.Build()
	File_slayertb_simulator_proto = out.File
	file_slayertb_simulator_proto_rawDesc = nil
	file_slayertb_simulator_proto_goTypes = nil
	file_slayertb_simulator_proto_depIdxs = nil
}

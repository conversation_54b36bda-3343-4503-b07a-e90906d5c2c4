// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: pin_controller.proto

package pin_controller

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	version "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/version"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DigitalPinValue int32

const (
	DigitalPinValue_LOW  DigitalPinValue = 0
	DigitalPinValue_HIGH DigitalPinValue = 1
)

// Enum value maps for DigitalPinValue.
var (
	DigitalPinValue_name = map[int32]string{
		0: "LOW",
		1: "HIGH",
	}
	DigitalPinValue_value = map[string]int32{
		"LOW":  0,
		"HIGH": 1,
	}
)

func (x DigitalPinValue) Enum() *DigitalPinValue {
	p := new(DigitalPinValue)
	*p = x
	return p
}

func (x DigitalPinValue) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DigitalPinValue) Descriptor() protoreflect.EnumDescriptor {
	return file_pin_controller_proto_enumTypes[0].Descriptor()
}

func (DigitalPinValue) Type() protoreflect.EnumType {
	return &file_pin_controller_proto_enumTypes[0]
}

func (x DigitalPinValue) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DigitalPinValue.Descriptor instead.
func (DigitalPinValue) EnumDescriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{0}
}

type PinMode int32

const (
	PinMode_INPUT         PinMode = 0
	PinMode_OUTPUT        PinMode = 1
	PinMode_INPUT_PULL_UP PinMode = 2
)

// Enum value maps for PinMode.
var (
	PinMode_name = map[int32]string{
		0: "INPUT",
		1: "OUTPUT",
		2: "INPUT_PULL_UP",
	}
	PinMode_value = map[string]int32{
		"INPUT":         0,
		"OUTPUT":        1,
		"INPUT_PULL_UP": 2,
	}
)

func (x PinMode) Enum() *PinMode {
	p := new(PinMode)
	*p = x
	return p
}

func (x PinMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinMode) Descriptor() protoreflect.EnumDescriptor {
	return file_pin_controller_proto_enumTypes[1].Descriptor()
}

func (PinMode) Type() protoreflect.EnumType {
	return &file_pin_controller_proto_enumTypes[1]
}

func (x PinMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PinMode.Descriptor instead.
func (PinMode) EnumDescriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{1}
}

type AnalogReadValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber uint32 `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
	Value     uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *AnalogReadValue) Reset() {
	*x = AnalogReadValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalogReadValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalogReadValue) ProtoMessage() {}

func (x *AnalogReadValue) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalogReadValue.ProtoReflect.Descriptor instead.
func (*AnalogReadValue) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{0}
}

func (x *AnalogReadValue) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

func (x *AnalogReadValue) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type DigitalReadValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber uint32          `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
	Value     DigitalPinValue `protobuf:"varint,2,opt,name=value,proto3,enum=pin_controller.DigitalPinValue" json:"value,omitempty"`
}

func (x *DigitalReadValue) Reset() {
	*x = DigitalReadValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigitalReadValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalReadValue) ProtoMessage() {}

func (x *DigitalReadValue) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalReadValue.ProtoReflect.Descriptor instead.
func (*DigitalReadValue) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{1}
}

func (x *DigitalReadValue) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

func (x *DigitalReadValue) GetValue() DigitalPinValue {
	if x != nil {
		return x.Value
	}
	return DigitalPinValue_LOW
}

type PinReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*PinReply_Ack
	//	*PinReply_Read
	//	*PinReply_Analog
	Reply isPinReply_Reply `protobuf_oneof:"reply"`
}

func (x *PinReply) Reset() {
	*x = PinReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinReply) ProtoMessage() {}

func (x *PinReply) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinReply.ProtoReflect.Descriptor instead.
func (*PinReply) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{2}
}

func (m *PinReply) GetReply() isPinReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *PinReply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*PinReply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *PinReply) GetRead() *DigitalReadValue {
	if x, ok := x.GetReply().(*PinReply_Read); ok {
		return x.Read
	}
	return nil
}

func (x *PinReply) GetAnalog() *AnalogReadValue {
	if x, ok := x.GetReply().(*PinReply_Analog); ok {
		return x.Analog
	}
	return nil
}

type isPinReply_Reply interface {
	isPinReply_Reply()
}

type PinReply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,1,opt,name=ack,proto3,oneof"`
}

type PinReply_Read struct {
	Read *DigitalReadValue `protobuf:"bytes,2,opt,name=read,proto3,oneof"`
}

type PinReply_Analog struct {
	Analog *AnalogReadValue `protobuf:"bytes,3,opt,name=analog,proto3,oneof"`
}

func (*PinReply_Ack) isPinReply_Reply() {}

func (*PinReply_Read) isPinReply_Reply() {}

func (*PinReply_Analog) isPinReply_Reply() {}

type AnalogRead struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber uint32 `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
}

func (x *AnalogRead) Reset() {
	*x = AnalogRead{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalogRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalogRead) ProtoMessage() {}

func (x *AnalogRead) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalogRead.ProtoReflect.Descriptor instead.
func (*AnalogRead) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{3}
}

func (x *AnalogRead) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

type DigitalRead struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber uint32 `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
}

func (x *DigitalRead) Reset() {
	*x = DigitalRead{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigitalRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalRead) ProtoMessage() {}

func (x *DigitalRead) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalRead.ProtoReflect.Descriptor instead.
func (*DigitalRead) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{4}
}

func (x *DigitalRead) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

type DigitalWrite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber uint32          `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
	Value     DigitalPinValue `protobuf:"varint,2,opt,name=value,proto3,enum=pin_controller.DigitalPinValue" json:"value,omitempty"`
}

func (x *DigitalWrite) Reset() {
	*x = DigitalWrite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigitalWrite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalWrite) ProtoMessage() {}

func (x *DigitalWrite) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalWrite.ProtoReflect.Descriptor instead.
func (*DigitalWrite) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{5}
}

func (x *DigitalWrite) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

func (x *DigitalWrite) GetValue() DigitalPinValue {
	if x != nil {
		return x.Value
	}
	return DigitalPinValue_LOW
}

type PinConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber uint32  `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
	PinMode   PinMode `protobuf:"varint,2,opt,name=pin_mode,json=pinMode,proto3,enum=pin_controller.PinMode" json:"pin_mode,omitempty"`
}

func (x *PinConfiguration) Reset() {
	*x = PinConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinConfiguration) ProtoMessage() {}

func (x *PinConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinConfiguration.ProtoReflect.Descriptor instead.
func (*PinConfiguration) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{6}
}

func (x *PinConfiguration) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

func (x *PinConfiguration) GetPinMode() PinMode {
	if x != nil {
		return x.PinMode
	}
	return PinMode_INPUT
}

type PWMConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinNumber  uint32 `protobuf:"varint,1,opt,name=pin_number,json=pinNumber,proto3" json:"pin_number,omitempty"`
	Value_8Bit uint32 `protobuf:"varint,2,opt,name=value_8bit,json=value8bit,proto3" json:"value_8bit,omitempty"`
}

func (x *PWMConfiguration) Reset() {
	*x = PWMConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PWMConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PWMConfiguration) ProtoMessage() {}

func (x *PWMConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PWMConfiguration.ProtoReflect.Descriptor instead.
func (*PWMConfiguration) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{7}
}

func (x *PWMConfiguration) GetPinNumber() uint32 {
	if x != nil {
		return x.PinNumber
	}
	return 0
}

func (x *PWMConfiguration) GetValue_8Bit() uint32 {
	if x != nil {
		return x.Value_8Bit
	}
	return 0
}

type PinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*PinRequest_Pin
	//	*PinRequest_Pwm
	//	*PinRequest_Write
	//	*PinRequest_Read
	//	*PinRequest_Analog
	Request isPinRequest_Request `protobuf_oneof:"request"`
}

func (x *PinRequest) Reset() {
	*x = PinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinRequest) ProtoMessage() {}

func (x *PinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinRequest.ProtoReflect.Descriptor instead.
func (*PinRequest) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{8}
}

func (m *PinRequest) GetRequest() isPinRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *PinRequest) GetPin() *PinConfiguration {
	if x, ok := x.GetRequest().(*PinRequest_Pin); ok {
		return x.Pin
	}
	return nil
}

func (x *PinRequest) GetPwm() *PWMConfiguration {
	if x, ok := x.GetRequest().(*PinRequest_Pwm); ok {
		return x.Pwm
	}
	return nil
}

func (x *PinRequest) GetWrite() *DigitalWrite {
	if x, ok := x.GetRequest().(*PinRequest_Write); ok {
		return x.Write
	}
	return nil
}

func (x *PinRequest) GetRead() *DigitalRead {
	if x, ok := x.GetRequest().(*PinRequest_Read); ok {
		return x.Read
	}
	return nil
}

func (x *PinRequest) GetAnalog() *AnalogRead {
	if x, ok := x.GetRequest().(*PinRequest_Analog); ok {
		return x.Analog
	}
	return nil
}

type isPinRequest_Request interface {
	isPinRequest_Request()
}

type PinRequest_Pin struct {
	Pin *PinConfiguration `protobuf:"bytes,1,opt,name=pin,proto3,oneof"`
}

type PinRequest_Pwm struct {
	Pwm *PWMConfiguration `protobuf:"bytes,2,opt,name=pwm,proto3,oneof"`
}

type PinRequest_Write struct {
	Write *DigitalWrite `protobuf:"bytes,3,opt,name=write,proto3,oneof"`
}

type PinRequest_Read struct {
	Read *DigitalRead `protobuf:"bytes,4,opt,name=read,proto3,oneof"`
}

type PinRequest_Analog struct {
	Analog *AnalogRead `protobuf:"bytes,5,opt,name=analog,proto3,oneof"`
}

func (*PinRequest_Pin) isPinRequest_Request() {}

func (*PinRequest_Pwm) isPinRequest_Request() {}

func (*PinRequest_Write) isPinRequest_Request() {}

func (*PinRequest_Read) isPinRequest_Request() {}

func (*PinRequest_Analog) isPinRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_Ack
	//	*Reply_Version
	//	*Reply_Pin
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{9}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetVersion() *version.Version_Reply {
	if x, ok := x.GetReply().(*Reply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Reply) GetPin() *PinReply {
	if x, ok := x.GetReply().(*Reply_Pin); ok {
		return x.Pin
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,3,opt,name=ack,proto3,oneof"`
}

type Reply_Version struct {
	Version *version.Version_Reply `protobuf:"bytes,4,opt,name=version,proto3,oneof"`
}

type Reply_Pin struct {
	Pin *PinReply `protobuf:"bytes,5,opt,name=pin,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Version) isReply_Reply() {}

func (*Reply_Pin) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_Version
	//	*Request_Pin
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pin_controller_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_pin_controller_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_pin_controller_proto_rawDescGZIP(), []int{10}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetVersion() *version.Version_Request {
	if x, ok := x.GetRequest().(*Request_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Request) GetPin() *PinRequest {
	if x, ok := x.GetRequest().(*Request_Pin); ok {
		return x.Pin
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_Version struct {
	Version *version.Version_Request `protobuf:"bytes,3,opt,name=version,proto3,oneof"`
}

type Request_Pin struct {
	Pin *PinRequest `protobuf:"bytes,4,opt,name=pin,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_Version) isRequest_Request() {}

func (*Request_Pin) isRequest_Request() {}

var File_pin_controller_proto protoreflect.FileDescriptor

var file_pin_controller_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a,
	0x0f, 0x41, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x61, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x68, 0x0a, 0x10, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x61, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70,
	0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c,
	0x50, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xa4, 0x01, 0x0a, 0x08, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x03,
	0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e,
	0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x36, 0x0a, 0x04, 0x72, 0x65,
	0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61,
	0x6c, 0x52, 0x65, 0x61, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x04, 0x72, 0x65,
	0x61, 0x64, 0x12, 0x39, 0x0a, 0x06, 0x61, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x61, 0x64, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x06, 0x61, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x42, 0x07, 0x0a,
	0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x0a, 0x0a, 0x41, 0x6e, 0x61, 0x6c, 0x6f, 0x67,
	0x52, 0x65, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x2c, 0x0a, 0x0b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x52, 0x65,
	0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x22, 0x64, 0x0a, 0x0c, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x2e, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x50, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x65, 0x0a, 0x10, 0x50, 0x69, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x70, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x08, 0x70, 0x69,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x69,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x50,
	0x0a, 0x10, 0x50, 0x57, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x38, 0x62, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x38, 0x62, 0x69, 0x74,
	0x22, 0xa2, 0x02, 0x0a, 0x0a, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x69,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x03, 0x70, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x03, 0x70, 0x77, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x50, 0x57, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x03, 0x70, 0x77, 0x6d, 0x12, 0x34, 0x0a, 0x05, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x48, 0x00, 0x52, 0x05, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x12, 0x31, 0x0a, 0x04, 0x72, 0x65, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x2e, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x48, 0x00, 0x52, 0x04,
	0x72, 0x65, 0x61, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x61, 0x64,
	0x48, 0x00, 0x52, 0x06, 0x61, 0x6e, 0x61, 0x6c, 0x6f, 0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xe8, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x26, 0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48,
	0x00, 0x52, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00,
	0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x03, 0x70, 0x69, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x03, 0x70, 0x69, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0xd2, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04,
	0x70, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04,
	0x70, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x03, 0x70, 0x69,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x70, 0x69, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2a, 0x24, 0x0a, 0x0f, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c,
	0x50, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x4f, 0x57, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x49, 0x47, 0x48, 0x10, 0x01, 0x2a, 0x33, 0x0a, 0x07, 0x50,
	0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10,
	0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x55, 0x54, 0x50, 0x55, 0x54, 0x10, 0x01, 0x12, 0x11, 0x0a,
	0x0d, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x10, 0x02,
	0x42, 0x17, 0x5a, 0x15, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pin_controller_proto_rawDescOnce sync.Once
	file_pin_controller_proto_rawDescData = file_pin_controller_proto_rawDesc
)

func file_pin_controller_proto_rawDescGZIP() []byte {
	file_pin_controller_proto_rawDescOnce.Do(func() {
		file_pin_controller_proto_rawDescData = protoimpl.X.CompressGZIP(file_pin_controller_proto_rawDescData)
	})
	return file_pin_controller_proto_rawDescData
}

var file_pin_controller_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pin_controller_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pin_controller_proto_goTypes = []interface{}{
	(DigitalPinValue)(0),            // 0: pin_controller.DigitalPinValue
	(PinMode)(0),                    // 1: pin_controller.PinMode
	(*AnalogReadValue)(nil),         // 2: pin_controller.AnalogReadValue
	(*DigitalReadValue)(nil),        // 3: pin_controller.DigitalReadValue
	(*PinReply)(nil),                // 4: pin_controller.PinReply
	(*AnalogRead)(nil),              // 5: pin_controller.AnalogRead
	(*DigitalRead)(nil),             // 6: pin_controller.DigitalRead
	(*DigitalWrite)(nil),            // 7: pin_controller.DigitalWrite
	(*PinConfiguration)(nil),        // 8: pin_controller.PinConfiguration
	(*PWMConfiguration)(nil),        // 9: pin_controller.PWMConfiguration
	(*PinRequest)(nil),              // 10: pin_controller.PinRequest
	(*Reply)(nil),                   // 11: pin_controller.Reply
	(*Request)(nil),                 // 12: pin_controller.Request
	(*ack.Ack)(nil),                 // 13: ack.Ack
	(*request.RequestHeader)(nil),   // 14: request.RequestHeader
	(*diagnostic.Pong)(nil),         // 15: diagnostic.Pong
	(*version.Version_Reply)(nil),   // 16: version.Version_Reply
	(*diagnostic.Ping)(nil),         // 17: diagnostic.Ping
	(*version.Version_Request)(nil), // 18: version.Version_Request
}
var file_pin_controller_proto_depIdxs = []int32{
	0,  // 0: pin_controller.DigitalReadValue.value:type_name -> pin_controller.DigitalPinValue
	13, // 1: pin_controller.PinReply.ack:type_name -> ack.Ack
	3,  // 2: pin_controller.PinReply.read:type_name -> pin_controller.DigitalReadValue
	2,  // 3: pin_controller.PinReply.analog:type_name -> pin_controller.AnalogReadValue
	0,  // 4: pin_controller.DigitalWrite.value:type_name -> pin_controller.DigitalPinValue
	1,  // 5: pin_controller.PinConfiguration.pin_mode:type_name -> pin_controller.PinMode
	8,  // 6: pin_controller.PinRequest.pin:type_name -> pin_controller.PinConfiguration
	9,  // 7: pin_controller.PinRequest.pwm:type_name -> pin_controller.PWMConfiguration
	7,  // 8: pin_controller.PinRequest.write:type_name -> pin_controller.DigitalWrite
	6,  // 9: pin_controller.PinRequest.read:type_name -> pin_controller.DigitalRead
	5,  // 10: pin_controller.PinRequest.analog:type_name -> pin_controller.AnalogRead
	14, // 11: pin_controller.Reply.header:type_name -> request.RequestHeader
	15, // 12: pin_controller.Reply.pong:type_name -> diagnostic.Pong
	13, // 13: pin_controller.Reply.ack:type_name -> ack.Ack
	16, // 14: pin_controller.Reply.version:type_name -> version.Version_Reply
	4,  // 15: pin_controller.Reply.pin:type_name -> pin_controller.PinReply
	14, // 16: pin_controller.Request.header:type_name -> request.RequestHeader
	17, // 17: pin_controller.Request.ping:type_name -> diagnostic.Ping
	18, // 18: pin_controller.Request.version:type_name -> version.Version_Request
	10, // 19: pin_controller.Request.pin:type_name -> pin_controller.PinRequest
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_pin_controller_proto_init() }
func file_pin_controller_proto_init() {
	if File_pin_controller_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pin_controller_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalogReadValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigitalReadValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalogRead); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigitalRead); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigitalWrite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PWMConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pin_controller_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pin_controller_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*PinReply_Ack)(nil),
		(*PinReply_Read)(nil),
		(*PinReply_Analog)(nil),
	}
	file_pin_controller_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*PinRequest_Pin)(nil),
		(*PinRequest_Pwm)(nil),
		(*PinRequest_Write)(nil),
		(*PinRequest_Read)(nil),
		(*PinRequest_Analog)(nil),
	}
	file_pin_controller_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Version)(nil),
		(*Reply_Pin)(nil),
	}
	file_pin_controller_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_Version)(nil),
		(*Request_Pin)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pin_controller_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pin_controller_proto_goTypes,
		DependencyIndexes: file_pin_controller_proto_depIdxs,
		EnumInfos:         file_pin_controller_proto_enumTypes,
		MessageInfos:      file_pin_controller_proto_msgTypes,
	}.Build()
	File_pin_controller_proto = out.File
	file_pin_controller_proto_rawDesc = nil
	file_pin_controller_proto_goTypes = nil
	file_pin_controller_proto_depIdxs = nil
}

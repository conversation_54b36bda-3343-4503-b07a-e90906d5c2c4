// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: reaper_module_controller.proto

package reaper_module_controller

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	hwinfo "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/hwinfo"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	strobe_control "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/strobe_control"
	time "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/time"
	version "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/version"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NetworkAddressSource int32

const (
	// Assigned manually (via UDP/serial)
	NetworkAddressSource_MANUAL NetworkAddressSource = 0
	// Statically assigned based on module ID
	NetworkAddressSource_STATIC NetworkAddressSource = 1
	// Acquirued via DHCP during boot
	NetworkAddressSource_DHCP NetworkAddressSource = 2
)

// Enum value maps for NetworkAddressSource.
var (
	NetworkAddressSource_name = map[int32]string{
		0: "MANUAL",
		1: "STATIC",
		2: "DHCP",
	}
	NetworkAddressSource_value = map[string]int32{
		"MANUAL": 0,
		"STATIC": 1,
		"DHCP":   2,
	}
)

func (x NetworkAddressSource) Enum() *NetworkAddressSource {
	p := new(NetworkAddressSource)
	*p = x
	return p
}

func (x NetworkAddressSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkAddressSource) Descriptor() protoreflect.EnumDescriptor {
	return file_reaper_module_controller_proto_enumTypes[0].Descriptor()
}

func (NetworkAddressSource) Type() protoreflect.EnumType {
	return &file_reaper_module_controller_proto_enumTypes[0]
}

func (x NetworkAddressSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkAddressSource.Descriptor instead.
func (NetworkAddressSource) EnumDescriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{0}
}

// Messages to control the circulating fans (manual + automatic control)
type ThermostatSource int32

const (
	ThermostatSource_DEFAULT         ThermostatSource = 0
	ThermostatSource_ENVIRO_INTERNAL ThermostatSource = 1
	ThermostatSource_ENVIRO_EXTERNAL ThermostatSource = 2
)

// Enum value maps for ThermostatSource.
var (
	ThermostatSource_name = map[int32]string{
		0: "DEFAULT",
		1: "ENVIRO_INTERNAL",
		2: "ENVIRO_EXTERNAL",
	}
	ThermostatSource_value = map[string]int32{
		"DEFAULT":         0,
		"ENVIRO_INTERNAL": 1,
		"ENVIRO_EXTERNAL": 2,
	}
)

func (x ThermostatSource) Enum() *ThermostatSource {
	p := new(ThermostatSource)
	*p = x
	return p
}

func (x ThermostatSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ThermostatSource) Descriptor() protoreflect.EnumDescriptor {
	return file_reaper_module_controller_proto_enumTypes[1].Descriptor()
}

func (ThermostatSource) Type() protoreflect.EnumType {
	return &file_reaper_module_controller_proto_enumTypes[1]
}

func (x ThermostatSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ThermostatSource.Descriptor instead.
func (ThermostatSource) EnumDescriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{1}
}

// Network status and configuration (UDP + OOB)
type NetworkConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NetworkConfigRequest) Reset() {
	*x = NetworkConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkConfigRequest) ProtoMessage() {}

func (x *NetworkConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkConfigRequest.ProtoReflect.Descriptor instead.
func (*NetworkConfigRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{0}
}

type NetworkResetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NetworkResetRequest) Reset() {
	*x = NetworkResetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkResetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkResetRequest) ProtoMessage() {}

func (x *NetworkResetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkResetRequest.ProtoReflect.Descriptor instead.
func (*NetworkResetRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{1}
}

type NetworkPowerCycleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NetworkPowerCycleRequest) Reset() {
	*x = NetworkPowerCycleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkPowerCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPowerCycleRequest) ProtoMessage() {}

func (x *NetworkPowerCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPowerCycleRequest.ProtoReflect.Descriptor instead.
func (*NetworkPowerCycleRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{2}
}

type NetworkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*NetworkRequest_Config
	//	*NetworkRequest_Reset_
	//	*NetworkRequest_PowerCycle
	Request isNetworkRequest_Request `protobuf_oneof:"request"`
}

func (x *NetworkRequest) Reset() {
	*x = NetworkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkRequest) ProtoMessage() {}

func (x *NetworkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkRequest.ProtoReflect.Descriptor instead.
func (*NetworkRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{3}
}

func (m *NetworkRequest) GetRequest() isNetworkRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *NetworkRequest) GetConfig() *NetworkConfigRequest {
	if x, ok := x.GetRequest().(*NetworkRequest_Config); ok {
		return x.Config
	}
	return nil
}

func (x *NetworkRequest) GetReset_() *NetworkResetRequest {
	if x, ok := x.GetRequest().(*NetworkRequest_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *NetworkRequest) GetPowerCycle() *NetworkPowerCycleRequest {
	if x, ok := x.GetRequest().(*NetworkRequest_PowerCycle); ok {
		return x.PowerCycle
	}
	return nil
}

type isNetworkRequest_Request interface {
	isNetworkRequest_Request()
}

type NetworkRequest_Config struct {
	Config *NetworkConfigRequest `protobuf:"bytes,1,opt,name=config,proto3,oneof"`
}

type NetworkRequest_Reset_ struct {
	Reset_ *NetworkResetRequest `protobuf:"bytes,2,opt,name=reset,proto3,oneof"`
}

type NetworkRequest_PowerCycle struct {
	PowerCycle *NetworkPowerCycleRequest `protobuf:"bytes,3,opt,name=powerCycle,proto3,oneof"`
}

func (*NetworkRequest_Config) isNetworkRequest_Request() {}

func (*NetworkRequest_Reset_) isNetworkRequest_Request() {}

func (*NetworkRequest_PowerCycle) isNetworkRequest_Request() {}

// A single interface IP address; IPs are sent in network byte order
type NetworkAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// How was this address assigned?
	Source NetworkAddressSource `protobuf:"varint,1,opt,name=source,proto3,enum=reaper_module_controller.NetworkAddressSource" json:"source,omitempty"`
	// Primary device address
	Unicast []byte `protobuf:"bytes,2,opt,name=unicast,proto3" json:"unicast,omitempty"`
	// Subnet mask for primary interface
	Subnet []byte `protobuf:"bytes,3,opt,name=subnet,proto3" json:"subnet,omitempty"`
	// Upstream gateway/router address, if any
	Gateway []byte `protobuf:"bytes,4,opt,name=gateway,proto3,oneof" json:"gateway,omitempty"`
}

func (x *NetworkAddress) Reset() {
	*x = NetworkAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkAddress) ProtoMessage() {}

func (x *NetworkAddress) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkAddress.ProtoReflect.Descriptor instead.
func (*NetworkAddress) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{4}
}

func (x *NetworkAddress) GetSource() NetworkAddressSource {
	if x != nil {
		return x.Source
	}
	return NetworkAddressSource_MANUAL
}

func (x *NetworkAddress) GetUnicast() []byte {
	if x != nil {
		return x.Unicast
	}
	return nil
}

func (x *NetworkAddress) GetSubnet() []byte {
	if x != nil {
		return x.Subnet
	}
	return nil
}

func (x *NetworkAddress) GetGateway() []byte {
	if x != nil {
		return x.Gateway
	}
	return nil
}

// Current config/state of the MCB network interface
type NetworkConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Link state
	LinkUp bool `protobuf:"varint,1,opt,name=linkUp,proto3" json:"linkUp,omitempty"`
	// MAC address
	Mac []byte `protobuf:"bytes,2,opt,name=mac,proto3" json:"mac,omitempty"`
	// Assigned IPv4 addresses
	Addresses []*NetworkAddress `protobuf:"bytes,3,rep,name=addresses,proto3" json:"addresses,omitempty"`
}

func (x *NetworkConfigReply) Reset() {
	*x = NetworkConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkConfigReply) ProtoMessage() {}

func (x *NetworkConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkConfigReply.ProtoReflect.Descriptor instead.
func (*NetworkConfigReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{5}
}

func (x *NetworkConfigReply) GetLinkUp() bool {
	if x != nil {
		return x.LinkUp
	}
	return false
}

func (x *NetworkConfigReply) GetMac() []byte {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *NetworkConfigReply) GetAddresses() []*NetworkAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type NetworkReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*NetworkReply_Config
	Reply isNetworkReply_Reply `protobuf_oneof:"reply"`
}

func (x *NetworkReply) Reset() {
	*x = NetworkReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkReply) ProtoMessage() {}

func (x *NetworkReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkReply.ProtoReflect.Descriptor instead.
func (*NetworkReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{6}
}

func (m *NetworkReply) GetReply() isNetworkReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *NetworkReply) GetConfig() *NetworkConfigReply {
	if x, ok := x.GetReply().(*NetworkReply_Config); ok {
		return x.Config
	}
	return nil
}

type isNetworkReply_Reply interface {
	isNetworkReply_Reply()
}

type NetworkReply_Config struct {
	Config *NetworkConfigReply `protobuf:"bytes,1,opt,name=config,proto3,oneof"`
}

func (*NetworkReply_Config) isNetworkReply_Reply() {}

// Scanner status and control
type ScannerStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is power switched on to the scanner?
	PowerEnabled bool `protobuf:"varint,1,opt,name=powerEnabled,proto3" json:"powerEnabled,omitempty"`
	// is polyfuse for scanner blown?
	FuseBlown     bool   `protobuf:"varint,2,opt,name=fuseBlown,proto3" json:"fuseBlown,omitempty"`
	FuseTimestamp uint64 `protobuf:"varint,3,opt,name=fuseTimestamp,proto3" json:"fuseTimestamp,omitempty"`
	// current consumed by the scanner
	Current          float32 `protobuf:"fixed32,4,opt,name=current,proto3" json:"current,omitempty"`
	CurrentTimestamp uint64  `protobuf:"varint,5,opt,name=currentTimestamp,proto3" json:"currentTimestamp,omitempty"`
}

func (x *ScannerStatus) Reset() {
	*x = ScannerStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerStatus) ProtoMessage() {}

func (x *ScannerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerStatus.ProtoReflect.Descriptor instead.
func (*ScannerStatus) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{7}
}

func (x *ScannerStatus) GetPowerEnabled() bool {
	if x != nil {
		return x.PowerEnabled
	}
	return false
}

func (x *ScannerStatus) GetFuseBlown() bool {
	if x != nil {
		return x.FuseBlown
	}
	return false
}

func (x *ScannerStatus) GetFuseTimestamp() uint64 {
	if x != nil {
		return x.FuseTimestamp
	}
	return 0
}

func (x *ScannerStatus) GetCurrent() float32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *ScannerStatus) GetCurrentTimestamp() uint64 {
	if x != nil {
		return x.CurrentTimestamp
	}
	return 0
}

type ScannerGetStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ScannerGetStatusRequest) Reset() {
	*x = ScannerGetStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerGetStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerGetStatusRequest) ProtoMessage() {}

func (x *ScannerGetStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerGetStatusRequest.ProtoReflect.Descriptor instead.
func (*ScannerGetStatusRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{8}
}

type ScannerSetPowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerA *bool `protobuf:"varint,1,opt,name=scannerA,proto3,oneof" json:"scannerA,omitempty"`
	ScannerB *bool `protobuf:"varint,2,opt,name=scannerB,proto3,oneof" json:"scannerB,omitempty"`
}

func (x *ScannerSetPowerRequest) Reset() {
	*x = ScannerSetPowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerSetPowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerSetPowerRequest) ProtoMessage() {}

func (x *ScannerSetPowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerSetPowerRequest.ProtoReflect.Descriptor instead.
func (*ScannerSetPowerRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{9}
}

func (x *ScannerSetPowerRequest) GetScannerA() bool {
	if x != nil && x.ScannerA != nil {
		return *x.ScannerA
	}
	return false
}

func (x *ScannerSetPowerRequest) GetScannerB() bool {
	if x != nil && x.ScannerB != nil {
		return *x.ScannerB
	}
	return false
}

// Reset the latching overcurrent protect (fuse blown) flag
type ScannerResetOcpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerA *bool `protobuf:"varint,1,opt,name=scannerA,proto3,oneof" json:"scannerA,omitempty"`
	ScannerB *bool `protobuf:"varint,2,opt,name=scannerB,proto3,oneof" json:"scannerB,omitempty"`
}

func (x *ScannerResetOcpRequest) Reset() {
	*x = ScannerResetOcpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerResetOcpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerResetOcpRequest) ProtoMessage() {}

func (x *ScannerResetOcpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerResetOcpRequest.ProtoReflect.Descriptor instead.
func (*ScannerResetOcpRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{10}
}

func (x *ScannerResetOcpRequest) GetScannerA() bool {
	if x != nil && x.ScannerA != nil {
		return *x.ScannerA
	}
	return false
}

func (x *ScannerResetOcpRequest) GetScannerB() bool {
	if x != nil && x.ScannerB != nil {
		return *x.ScannerB
	}
	return false
}

type ScannerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*ScannerRequest_Status
	//	*ScannerRequest_Power
	//	*ScannerRequest_Ocp
	Request isScannerRequest_Request `protobuf_oneof:"request"`
}

func (x *ScannerRequest) Reset() {
	*x = ScannerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerRequest) ProtoMessage() {}

func (x *ScannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerRequest.ProtoReflect.Descriptor instead.
func (*ScannerRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{11}
}

func (m *ScannerRequest) GetRequest() isScannerRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *ScannerRequest) GetStatus() *ScannerGetStatusRequest {
	if x, ok := x.GetRequest().(*ScannerRequest_Status); ok {
		return x.Status
	}
	return nil
}

func (x *ScannerRequest) GetPower() *ScannerSetPowerRequest {
	if x, ok := x.GetRequest().(*ScannerRequest_Power); ok {
		return x.Power
	}
	return nil
}

func (x *ScannerRequest) GetOcp() *ScannerResetOcpRequest {
	if x, ok := x.GetRequest().(*ScannerRequest_Ocp); ok {
		return x.Ocp
	}
	return nil
}

type isScannerRequest_Request interface {
	isScannerRequest_Request()
}

type ScannerRequest_Status struct {
	Status *ScannerGetStatusRequest `protobuf:"bytes,1,opt,name=status,proto3,oneof"`
}

type ScannerRequest_Power struct {
	Power *ScannerSetPowerRequest `protobuf:"bytes,2,opt,name=power,proto3,oneof"`
}

type ScannerRequest_Ocp struct {
	Ocp *ScannerResetOcpRequest `protobuf:"bytes,3,opt,name=ocp,proto3,oneof"`
}

func (*ScannerRequest_Status) isScannerRequest_Request() {}

func (*ScannerRequest_Power) isScannerRequest_Request() {}

func (*ScannerRequest_Ocp) isScannerRequest_Request() {}

type ScannerStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ScannerStatus `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ScannerStatusReply) Reset() {
	*x = ScannerStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerStatusReply) ProtoMessage() {}

func (x *ScannerStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerStatusReply.ProtoReflect.Descriptor instead.
func (*ScannerStatusReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{12}
}

func (x *ScannerStatusReply) GetData() []*ScannerStatus {
	if x != nil {
		return x.Data
	}
	return nil
}

type ScannerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*ScannerReply_Status
	Reply isScannerReply_Reply `protobuf_oneof:"reply"`
}

func (x *ScannerReply) Reset() {
	*x = ScannerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerReply) ProtoMessage() {}

func (x *ScannerReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerReply.ProtoReflect.Descriptor instead.
func (*ScannerReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{13}
}

func (m *ScannerReply) GetReply() isScannerReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *ScannerReply) GetStatus() *ScannerStatusReply {
	if x, ok := x.GetReply().(*ScannerReply_Status); ok {
		return x.Status
	}
	return nil
}

type isScannerReply_Reply interface {
	isScannerReply_Reply()
}

type ScannerReply_Status struct {
	Status *ScannerStatusReply `protobuf:"bytes,1,opt,name=status,proto3,oneof"`
}

func (*ScannerReply_Status) isScannerReply_Reply() {}

// Switchable off-board DC power
type PowerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RelayBoard  *bool `protobuf:"varint,1,opt,name=relayBoard,proto3,oneof" json:"relayBoard,omitempty"`
	StrobeBoard *bool `protobuf:"varint,2,opt,name=strobeBoard,proto3,oneof" json:"strobeBoard,omitempty"`
	EthSwitch   *bool `protobuf:"varint,3,opt,name=ethSwitch,proto3,oneof" json:"ethSwitch,omitempty"`
	PredictCam  *bool `protobuf:"varint,4,opt,name=predictCam,proto3,oneof" json:"predictCam,omitempty"`
}

func (x *PowerRequest) Reset() {
	*x = PowerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerRequest) ProtoMessage() {}

func (x *PowerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerRequest.ProtoReflect.Descriptor instead.
func (*PowerRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{14}
}

func (x *PowerRequest) GetRelayBoard() bool {
	if x != nil && x.RelayBoard != nil {
		return *x.RelayBoard
	}
	return false
}

func (x *PowerRequest) GetStrobeBoard() bool {
	if x != nil && x.StrobeBoard != nil {
		return *x.StrobeBoard
	}
	return false
}

func (x *PowerRequest) GetEthSwitch() bool {
	if x != nil && x.EthSwitch != nil {
		return *x.EthSwitch
	}
	return false
}

func (x *PowerRequest) GetPredictCam() bool {
	if x != nil && x.PredictCam != nil {
		return *x.PredictCam
	}
	return false
}

type PowerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RelayBoard  bool `protobuf:"varint,1,opt,name=relayBoard,proto3" json:"relayBoard,omitempty"`
	StrobeBoard bool `protobuf:"varint,2,opt,name=strobeBoard,proto3" json:"strobeBoard,omitempty"`
	EthSwitch   bool `protobuf:"varint,3,opt,name=ethSwitch,proto3" json:"ethSwitch,omitempty"`
	PredictCam  bool `protobuf:"varint,4,opt,name=predictCam,proto3" json:"predictCam,omitempty"`
}

func (x *PowerReply) Reset() {
	*x = PowerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerReply) ProtoMessage() {}

func (x *PowerReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerReply.ProtoReflect.Descriptor instead.
func (*PowerReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{15}
}

func (x *PowerReply) GetRelayBoard() bool {
	if x != nil {
		return x.RelayBoard
	}
	return false
}

func (x *PowerReply) GetStrobeBoard() bool {
	if x != nil {
		return x.StrobeBoard
	}
	return false
}

func (x *PowerReply) GetEthSwitch() bool {
	if x != nil {
		return x.EthSwitch
	}
	return false
}

func (x *PowerReply) GetPredictCam() bool {
	if x != nil {
		return x.PredictCam
	}
	return false
}

// Strobe/BTL configuration
type StrobeStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StrobeStatusRequest) Reset() {
	*x = StrobeStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrobeStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrobeStatusRequest) ProtoMessage() {}

func (x *StrobeStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrobeStatusRequest.ProtoReflect.Descriptor instead.
func (*StrobeStatusRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{16}
}

type SetStrobeStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Enable strobing and camera triggering
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *SetStrobeStateRequest) Reset() {
	*x = SetStrobeStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetStrobeStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStrobeStateRequest) ProtoMessage() {}

func (x *SetStrobeStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStrobeStateRequest.ProtoReflect.Descriptor instead.
func (*SetStrobeStateRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{17}
}

func (x *SetStrobeStateRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type TimedStrobeDisableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// How long the strobes are disabled for before restoring previous state
	DurationMsec uint32 `protobuf:"varint,1,opt,name=durationMsec,proto3" json:"durationMsec,omitempty"`
}

func (x *TimedStrobeDisableRequest) Reset() {
	*x = TimedStrobeDisableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimedStrobeDisableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimedStrobeDisableRequest) ProtoMessage() {}

func (x *TimedStrobeDisableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimedStrobeDisableRequest.ProtoReflect.Descriptor instead.
func (*TimedStrobeDisableRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{18}
}

func (x *TimedStrobeDisableRequest) GetDurationMsec() uint32 {
	if x != nil {
		return x.DurationMsec
	}
	return 0
}

type StrobeStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Voltage          float32 `protobuf:"fixed32,1,opt,name=voltage,proto3" json:"voltage,omitempty"`
	VoltageTimestamp uint64  `protobuf:"varint,2,opt,name=voltageTimestamp,proto3" json:"voltageTimestamp,omitempty"`
	Current          float32 `protobuf:"fixed32,3,opt,name=current,proto3" json:"current,omitempty"`
	CurrentTimestamp uint64  `protobuf:"varint,4,opt,name=currentTimestamp,proto3" json:"currentTimestamp,omitempty"`
	// from thermistor on strobe board (°C)
	Temperature          float32 `protobuf:"fixed32,5,opt,name=temperature,proto3" json:"temperature,omitempty"`
	TemperatureTimestamp uint64  `protobuf:"varint,6,opt,name=temperatureTimestamp,proto3" json:"temperatureTimestamp,omitempty"`
	// whether strobe board is charged/ready to fire
	Ready bool `protobuf:"varint,7,opt,name=ready,proto3" json:"ready,omitempty"`
	// software request to enable strobing (change with SetState request)
	Enabled bool `protobuf:"varint,8,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// are the strobes actually firing? (e.g. safeties satisfied & enabled & ready)
	Firing bool `protobuf:"varint,9,opt,name=firing,proto3" json:"firing,omitempty"`
	// currently configured output waveform
	ExposureUs        *uint32 `protobuf:"varint,10,opt,name=exposureUs,proto3,oneof" json:"exposureUs,omitempty"`
	PeriodUs          *uint32 `protobuf:"varint,11,opt,name=periodUs,proto3,oneof" json:"periodUs,omitempty"`
	TargetsPerPredict *uint32 `protobuf:"varint,12,opt,name=targetsPerPredict,proto3,oneof" json:"targetsPerPredict,omitempty"`
}

func (x *StrobeStatusReply) Reset() {
	*x = StrobeStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrobeStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrobeStatusReply) ProtoMessage() {}

func (x *StrobeStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrobeStatusReply.ProtoReflect.Descriptor instead.
func (*StrobeStatusReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{19}
}

func (x *StrobeStatusReply) GetVoltage() float32 {
	if x != nil {
		return x.Voltage
	}
	return 0
}

func (x *StrobeStatusReply) GetVoltageTimestamp() uint64 {
	if x != nil {
		return x.VoltageTimestamp
	}
	return 0
}

func (x *StrobeStatusReply) GetCurrent() float32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *StrobeStatusReply) GetCurrentTimestamp() uint64 {
	if x != nil {
		return x.CurrentTimestamp
	}
	return 0
}

func (x *StrobeStatusReply) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *StrobeStatusReply) GetTemperatureTimestamp() uint64 {
	if x != nil {
		return x.TemperatureTimestamp
	}
	return 0
}

func (x *StrobeStatusReply) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *StrobeStatusReply) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *StrobeStatusReply) GetFiring() bool {
	if x != nil {
		return x.Firing
	}
	return false
}

func (x *StrobeStatusReply) GetExposureUs() uint32 {
	if x != nil && x.ExposureUs != nil {
		return *x.ExposureUs
	}
	return 0
}

func (x *StrobeStatusReply) GetPeriodUs() uint32 {
	if x != nil && x.PeriodUs != nil {
		return *x.PeriodUs
	}
	return 0
}

func (x *StrobeStatusReply) GetTargetsPerPredict() uint32 {
	if x != nil && x.TargetsPerPredict != nil {
		return *x.TargetsPerPredict
	}
	return 0
}

type StrobeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*StrobeRequest_SetWaveform
	//	*StrobeRequest_GetStatus
	//	*StrobeRequest_SetState
	//	*StrobeRequest_TimedDisable
	Request isStrobeRequest_Request `protobuf_oneof:"request"`
}

func (x *StrobeRequest) Reset() {
	*x = StrobeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrobeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrobeRequest) ProtoMessage() {}

func (x *StrobeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrobeRequest.ProtoReflect.Descriptor instead.
func (*StrobeRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{20}
}

func (m *StrobeRequest) GetRequest() isStrobeRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *StrobeRequest) GetSetWaveform() *strobe_control.Request {
	if x, ok := x.GetRequest().(*StrobeRequest_SetWaveform); ok {
		return x.SetWaveform
	}
	return nil
}

func (x *StrobeRequest) GetGetStatus() *StrobeStatusRequest {
	if x, ok := x.GetRequest().(*StrobeRequest_GetStatus); ok {
		return x.GetStatus
	}
	return nil
}

func (x *StrobeRequest) GetSetState() *SetStrobeStateRequest {
	if x, ok := x.GetRequest().(*StrobeRequest_SetState); ok {
		return x.SetState
	}
	return nil
}

func (x *StrobeRequest) GetTimedDisable() *TimedStrobeDisableRequest {
	if x, ok := x.GetRequest().(*StrobeRequest_TimedDisable); ok {
		return x.TimedDisable
	}
	return nil
}

type isStrobeRequest_Request interface {
	isStrobeRequest_Request()
}

type StrobeRequest_SetWaveform struct {
	SetWaveform *strobe_control.Request `protobuf:"bytes,1,opt,name=setWaveform,proto3,oneof"`
}

type StrobeRequest_GetStatus struct {
	GetStatus *StrobeStatusRequest `protobuf:"bytes,2,opt,name=getStatus,proto3,oneof"`
}

type StrobeRequest_SetState struct {
	SetState *SetStrobeStateRequest `protobuf:"bytes,3,opt,name=setState,proto3,oneof"`
}

type StrobeRequest_TimedDisable struct {
	TimedDisable *TimedStrobeDisableRequest `protobuf:"bytes,4,opt,name=timedDisable,proto3,oneof"`
}

func (*StrobeRequest_SetWaveform) isStrobeRequest_Request() {}

func (*StrobeRequest_GetStatus) isStrobeRequest_Request() {}

func (*StrobeRequest_SetState) isStrobeRequest_Request() {}

func (*StrobeRequest_TimedDisable) isStrobeRequest_Request() {}

type StrobeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*StrobeReply_Status
	Reply isStrobeReply_Reply `protobuf_oneof:"reply"`
}

func (x *StrobeReply) Reset() {
	*x = StrobeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrobeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrobeReply) ProtoMessage() {}

func (x *StrobeReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrobeReply.ProtoReflect.Descriptor instead.
func (*StrobeReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{21}
}

func (m *StrobeReply) GetReply() isStrobeReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *StrobeReply) GetStatus() *StrobeStatusReply {
	if x, ok := x.GetReply().(*StrobeReply_Status); ok {
		return x.Status
	}
	return nil
}

type isStrobeReply_Reply interface {
	isStrobeReply_Reply()
}

type StrobeReply_Status struct {
	Status *StrobeStatusReply `protobuf:"bytes,1,opt,name=status,proto3,oneof"`
}

func (*StrobeReply_Status) isStrobeReply_Reply() {}

// Module identity configuration: e.g. module number
type ModuleIdentity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Module number within the row
	Number uint32 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *ModuleIdentity) Reset() {
	*x = ModuleIdentity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleIdentity) ProtoMessage() {}

func (x *ModuleIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleIdentity.ProtoReflect.Descriptor instead.
func (*ModuleIdentity) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{22}
}

func (x *ModuleIdentity) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

type GetModuleIdentityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetModuleIdentityRequest) Reset() {
	*x = GetModuleIdentityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModuleIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModuleIdentityRequest) ProtoMessage() {}

func (x *GetModuleIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModuleIdentityRequest.ProtoReflect.Descriptor instead.
func (*GetModuleIdentityRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{23}
}

// Lock or unlock the OTP for writing
type SetOtpLockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether to lock or unlock OTP: key is required only to unlock
	Lock bool `protobuf:"varint,1,opt,name=lock,proto3" json:"lock,omitempty"`
	// must always be 'YEET'
	Key uint32 `protobuf:"fixed32,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *SetOtpLockRequest) Reset() {
	*x = SetOtpLockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOtpLockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOtpLockRequest) ProtoMessage() {}

func (x *SetOtpLockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOtpLockRequest.ProtoReflect.Descriptor instead.
func (*SetOtpLockRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{24}
}

func (x *SetOtpLockRequest) GetLock() bool {
	if x != nil {
		return x.Lock
	}
	return false
}

func (x *SetOtpLockRequest) GetKey() uint32 {
	if x != nil {
		return x.Key
	}
	return 0
}

// Program the board identity for hwinfo endpoint (requires OTP unlock within 1 sec before)
type SetBoardIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cbsn   *string `protobuf:"bytes,1,opt,name=cbsn,proto3,oneof" json:"cbsn,omitempty"`
	AssySn *string `protobuf:"bytes,2,opt,name=assySn,proto3,oneof" json:"assySn,omitempty"`
}

func (x *SetBoardIdRequest) Reset() {
	*x = SetBoardIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBoardIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBoardIdRequest) ProtoMessage() {}

func (x *SetBoardIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBoardIdRequest.ProtoReflect.Descriptor instead.
func (*SetBoardIdRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{25}
}

func (x *SetBoardIdRequest) GetCbsn() string {
	if x != nil && x.Cbsn != nil {
		return *x.Cbsn
	}
	return ""
}

func (x *SetBoardIdRequest) GetAssySn() string {
	if x != nil && x.AssySn != nil {
		return *x.AssySn
	}
	return ""
}

type ConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*ConfigRequest_GetIdentity
	//	*ConfigRequest_SetIdentity
	//	*ConfigRequest_OtpLock
	//	*ConfigRequest_SetBoardIdentity
	Request isConfigRequest_Request `protobuf_oneof:"request"`
}

func (x *ConfigRequest) Reset() {
	*x = ConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigRequest) ProtoMessage() {}

func (x *ConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigRequest.ProtoReflect.Descriptor instead.
func (*ConfigRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{26}
}

func (m *ConfigRequest) GetRequest() isConfigRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *ConfigRequest) GetGetIdentity() *GetModuleIdentityRequest {
	if x, ok := x.GetRequest().(*ConfigRequest_GetIdentity); ok {
		return x.GetIdentity
	}
	return nil
}

func (x *ConfigRequest) GetSetIdentity() *ModuleIdentity {
	if x, ok := x.GetRequest().(*ConfigRequest_SetIdentity); ok {
		return x.SetIdentity
	}
	return nil
}

func (x *ConfigRequest) GetOtpLock() *SetOtpLockRequest {
	if x, ok := x.GetRequest().(*ConfigRequest_OtpLock); ok {
		return x.OtpLock
	}
	return nil
}

func (x *ConfigRequest) GetSetBoardIdentity() *SetBoardIdRequest {
	if x, ok := x.GetRequest().(*ConfigRequest_SetBoardIdentity); ok {
		return x.SetBoardIdentity
	}
	return nil
}

type isConfigRequest_Request interface {
	isConfigRequest_Request()
}

type ConfigRequest_GetIdentity struct {
	GetIdentity *GetModuleIdentityRequest `protobuf:"bytes,1,opt,name=getIdentity,proto3,oneof"`
}

type ConfigRequest_SetIdentity struct {
	SetIdentity *ModuleIdentity `protobuf:"bytes,2,opt,name=setIdentity,proto3,oneof"`
}

type ConfigRequest_OtpLock struct {
	OtpLock *SetOtpLockRequest `protobuf:"bytes,3,opt,name=otpLock,proto3,oneof"`
}

type ConfigRequest_SetBoardIdentity struct {
	SetBoardIdentity *SetBoardIdRequest `protobuf:"bytes,4,opt,name=setBoardIdentity,proto3,oneof"`
}

func (*ConfigRequest_GetIdentity) isConfigRequest_Request() {}

func (*ConfigRequest_SetIdentity) isConfigRequest_Request() {}

func (*ConfigRequest_OtpLock) isConfigRequest_Request() {}

func (*ConfigRequest_SetBoardIdentity) isConfigRequest_Request() {}

type ConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*ConfigReply_Identity
	Reply isConfigReply_Reply `protobuf_oneof:"reply"`
}

func (x *ConfigReply) Reset() {
	*x = ConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigReply) ProtoMessage() {}

func (x *ConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigReply.ProtoReflect.Descriptor instead.
func (*ConfigReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{27}
}

func (m *ConfigReply) GetReply() isConfigReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *ConfigReply) GetIdentity() *ModuleIdentity {
	if x, ok := x.GetReply().(*ConfigReply_Identity); ok {
		return x.Identity
	}
	return nil
}

type isConfigReply_Reply interface {
	isConfigReply_Reply()
}

type ConfigReply_Identity struct {
	Identity *ModuleIdentity `protobuf:"bytes,1,opt,name=identity,proto3,oneof"`
}

func (*ConfigReply_Identity) isConfigReply_Reply() {}

type ThermostatConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Temperature above which fans are turned on (°C)
	Setpoint float32 `protobuf:"fixed32,1,opt,name=setpoint,proto3" json:"setpoint,omitempty"`
	// Delta temp from setpoint before fans are enabled/disabled
	Hysteresis float32 `protobuf:"fixed32,2,opt,name=hysteresis,proto3" json:"hysteresis,omitempty"`
	// Which temperature sensor to use for thermostatic control
	Source ThermostatSource `protobuf:"varint,3,opt,name=source,proto3,enum=reaper_module_controller.ThermostatSource" json:"source,omitempty"`
}

func (x *ThermostatConfig) Reset() {
	*x = ThermostatConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThermostatConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThermostatConfig) ProtoMessage() {}

func (x *ThermostatConfig) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThermostatConfig.ProtoReflect.Descriptor instead.
func (*ThermostatConfig) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{28}
}

func (x *ThermostatConfig) GetSetpoint() float32 {
	if x != nil {
		return x.Setpoint
	}
	return 0
}

func (x *ThermostatConfig) GetHysteresis() float32 {
	if x != nil {
		return x.Hysteresis
	}
	return 0
}

func (x *ThermostatConfig) GetSource() ThermostatSource {
	if x != nil {
		return x.Source
	}
	return ThermostatSource_DEFAULT
}

type FanSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fan1 *bool `protobuf:"varint,1,opt,name=fan1,proto3,oneof" json:"fan1,omitempty"`
	Fan2 *bool `protobuf:"varint,2,opt,name=fan2,proto3,oneof" json:"fan2,omitempty"`
}

func (x *FanSetRequest) Reset() {
	*x = FanSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FanSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FanSetRequest) ProtoMessage() {}

func (x *FanSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FanSetRequest.ProtoReflect.Descriptor instead.
func (*FanSetRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{29}
}

func (x *FanSetRequest) GetFan1() bool {
	if x != nil && x.Fan1 != nil {
		return *x.Fan1
	}
	return false
}

func (x *FanSetRequest) GetFan2() bool {
	if x != nil && x.Fan2 != nil {
		return *x.Fan2
	}
	return false
}

type FanThermostatConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the thermostat is enabled: this will lock out manual control
	Enabled *bool `protobuf:"varint,1,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"`
	// New configuration to apply to the thermostat
	Config *ThermostatConfig `protobuf:"bytes,2,opt,name=config,proto3,oneof" json:"config,omitempty"`
}

func (x *FanThermostatConfigRequest) Reset() {
	*x = FanThermostatConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FanThermostatConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FanThermostatConfigRequest) ProtoMessage() {}

func (x *FanThermostatConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FanThermostatConfigRequest.ProtoReflect.Descriptor instead.
func (*FanThermostatConfigRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{30}
}

func (x *FanThermostatConfigRequest) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

func (x *FanThermostatConfigRequest) GetConfig() *ThermostatConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type FanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*FanRequest_Set
	//	*FanRequest_ThermoConfig
	Request isFanRequest_Request `protobuf_oneof:"request"`
}

func (x *FanRequest) Reset() {
	*x = FanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FanRequest) ProtoMessage() {}

func (x *FanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FanRequest.ProtoReflect.Descriptor instead.
func (*FanRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{31}
}

func (m *FanRequest) GetRequest() isFanRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *FanRequest) GetSet() *FanSetRequest {
	if x, ok := x.GetRequest().(*FanRequest_Set); ok {
		return x.Set
	}
	return nil
}

func (x *FanRequest) GetThermoConfig() *FanThermostatConfigRequest {
	if x, ok := x.GetRequest().(*FanRequest_ThermoConfig); ok {
		return x.ThermoConfig
	}
	return nil
}

type isFanRequest_Request interface {
	isFanRequest_Request()
}

type FanRequest_Set struct {
	Set *FanSetRequest `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type FanRequest_ThermoConfig struct {
	ThermoConfig *FanThermostatConfigRequest `protobuf:"bytes,2,opt,name=thermoConfig,proto3,oneof"`
}

func (*FanRequest_Set) isFanRequest_Request() {}

func (*FanRequest_ThermoConfig) isFanRequest_Request() {}

type FanReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fan1 bool `protobuf:"varint,1,opt,name=fan1,proto3" json:"fan1,omitempty"`
	Fan2 bool `protobuf:"varint,2,opt,name=fan2,proto3" json:"fan2,omitempty"`
	// Whether thermostat is enabled in user config
	ThermostatEnabled bool `protobuf:"varint,3,opt,name=thermostatEnabled,proto3" json:"thermostatEnabled,omitempty"`
	// Current thermostat configuration, if it's enabled
	ThermostatConfig *ThermostatConfig `protobuf:"bytes,4,opt,name=thermostatConfig,proto3,oneof" json:"thermostatConfig,omitempty"`
	// Most recently read temperature value, if thermostat is enabled
	ThermostatActual *float32 `protobuf:"fixed32,5,opt,name=thermostatActual,proto3,oneof" json:"thermostatActual,omitempty"`
}

func (x *FanReply) Reset() {
	*x = FanReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FanReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FanReply) ProtoMessage() {}

func (x *FanReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FanReply.ProtoReflect.Descriptor instead.
func (*FanReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{32}
}

func (x *FanReply) GetFan1() bool {
	if x != nil {
		return x.Fan1
	}
	return false
}

func (x *FanReply) GetFan2() bool {
	if x != nil {
		return x.Fan2
	}
	return false
}

func (x *FanReply) GetThermostatEnabled() bool {
	if x != nil {
		return x.ThermostatEnabled
	}
	return false
}

func (x *FanReply) GetThermostatConfig() *ThermostatConfig {
	if x != nil {
		return x.ThermostatConfig
	}
	return nil
}

func (x *FanReply) GetThermostatActual() float32 {
	if x != nil && x.ThermostatActual != nil {
		return *x.ThermostatActual
	}
	return 0
}

// Requests to control AC power switched via relay board
type RelayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pc    *bool `protobuf:"varint,1,opt,name=pc,proto3,oneof" json:"pc,omitempty"`
	Btl   *bool `protobuf:"varint,2,opt,name=btl,proto3,oneof" json:"btl,omitempty"`
	Laser *bool `protobuf:"varint,3,opt,name=laser,proto3,oneof" json:"laser,omitempty"`
}

func (x *RelayRequest) Reset() {
	*x = RelayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayRequest) ProtoMessage() {}

func (x *RelayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayRequest.ProtoReflect.Descriptor instead.
func (*RelayRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{33}
}

func (x *RelayRequest) GetPc() bool {
	if x != nil && x.Pc != nil {
		return *x.Pc
	}
	return false
}

func (x *RelayRequest) GetBtl() bool {
	if x != nil && x.Btl != nil {
		return *x.Btl
	}
	return false
}

func (x *RelayRequest) GetLaser() bool {
	if x != nil && x.Laser != nil {
		return *x.Laser
	}
	return false
}

type RelayReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pc    bool `protobuf:"varint,1,opt,name=pc,proto3" json:"pc,omitempty"`
	Btl   bool `protobuf:"varint,2,opt,name=btl,proto3" json:"btl,omitempty"`
	Laser bool `protobuf:"varint,3,opt,name=laser,proto3" json:"laser,omitempty"`
}

func (x *RelayReply) Reset() {
	*x = RelayReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelayReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayReply) ProtoMessage() {}

func (x *RelayReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayReply.ProtoReflect.Descriptor instead.
func (*RelayReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{34}
}

func (x *RelayReply) GetPc() bool {
	if x != nil {
		return x.Pc
	}
	return false
}

func (x *RelayReply) GetBtl() bool {
	if x != nil {
		return x.Btl
	}
	return false
}

func (x *RelayReply) GetLaser() bool {
	if x != nil {
		return x.Laser
	}
	return false
}

// Messages for interacting with the sensor module
//
// - Timestamps are in milliseconds since the epoch
type SensorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SensorRequest) Reset() {
	*x = SensorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorRequest) ProtoMessage() {}

func (x *SensorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorRequest.ProtoReflect.Descriptor instead.
func (*SensorRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{35}
}

type SensorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Environmental sensors; first is on-board, second is external
	Env []*SensorReplyEnvdata `protobuf:"bytes,1,rep,name=env,proto3" json:"env,omitempty"`
	// raw reading from IMU
	Imu *SensorReplyImudata `protobuf:"bytes,2,opt,name=imu,proto3" json:"imu,omitempty"`
	// external thermistors
	Therm []*SensorReplyThermdata `protobuf:"bytes,3,rep,name=therm,proto3" json:"therm,omitempty"`
	// leak sensors
	Leak []*SensorReplyLeakdata `protobuf:"bytes,4,rep,name=leak,proto3" json:"leak,omitempty"`
	// external pressure sensors
	Press []*SensorReplyPressdata `protobuf:"bytes,5,rep,name=press,proto3" json:"press,omitempty"`
	// flag indicating whether the external environmental sensor was detected at boot
	HasExternalEnv bool `protobuf:"varint,6,opt,name=hasExternalEnv,proto3" json:"hasExternalEnv,omitempty"`
}

func (x *SensorReply) Reset() {
	*x = SensorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorReply) ProtoMessage() {}

func (x *SensorReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorReply.ProtoReflect.Descriptor instead.
func (*SensorReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{36}
}

func (x *SensorReply) GetEnv() []*SensorReplyEnvdata {
	if x != nil {
		return x.Env
	}
	return nil
}

func (x *SensorReply) GetImu() *SensorReplyImudata {
	if x != nil {
		return x.Imu
	}
	return nil
}

func (x *SensorReply) GetTherm() []*SensorReplyThermdata {
	if x != nil {
		return x.Therm
	}
	return nil
}

func (x *SensorReply) GetLeak() []*SensorReplyLeakdata {
	if x != nil {
		return x.Leak
	}
	return nil
}

func (x *SensorReply) GetPress() []*SensorReplyPressdata {
	if x != nil {
		return x.Press
	}
	return nil
}

func (x *SensorReply) GetHasExternalEnv() bool {
	if x != nil {
		return x.HasExternalEnv
	}
	return false
}

// Hardware status endpoint, polled by hardware_manager on command
type StatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StatusRequest) Reset() {
	*x = StatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRequest) ProtoMessage() {}

func (x *StatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusRequest.ProtoReflect.Descriptor instead.
func (*StatusRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{37}
}

type StatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sensors  *SensorReply       `protobuf:"bytes,1,opt,name=sensors,proto3" json:"sensors,omitempty"`
	Relays   *RelayReply        `protobuf:"bytes,2,opt,name=relays,proto3" json:"relays,omitempty"`
	Power    *PowerReply        `protobuf:"bytes,3,opt,name=power,proto3" json:"power,omitempty"`
	Strobe   *StrobeStatusReply `protobuf:"bytes,4,opt,name=strobe,proto3" json:"strobe,omitempty"`
	Scanners []*ScannerStatus   `protobuf:"bytes,5,rep,name=scanners,proto3" json:"scanners,omitempty"`
}

func (x *StatusReply) Reset() {
	*x = StatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusReply) ProtoMessage() {}

func (x *StatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusReply.ProtoReflect.Descriptor instead.
func (*StatusReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{38}
}

func (x *StatusReply) GetSensors() *SensorReply {
	if x != nil {
		return x.Sensors
	}
	return nil
}

func (x *StatusReply) GetRelays() *RelayReply {
	if x != nil {
		return x.Relays
	}
	return nil
}

func (x *StatusReply) GetPower() *PowerReply {
	if x != nil {
		return x.Power
	}
	return nil
}

func (x *StatusReply) GetStrobe() *StrobeStatusReply {
	if x != nil {
		return x.Strobe
	}
	return nil
}

func (x *StatusReply) GetScanners() []*ScannerStatus {
	if x != nil {
		return x.Scanners
	}
	return nil
}

// Coredump output messages
//
// These are emitted WITHOUT any request from the client, via the serial (OOB) interface.
type CoreDumpStart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CoreDumpStart) Reset() {
	*x = CoreDumpStart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoreDumpStart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreDumpStart) ProtoMessage() {}

func (x *CoreDumpStart) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreDumpStart.ProtoReflect.Descriptor instead.
func (*CoreDumpStart) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{39}
}

type CoreDumpEnd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CoreDumpEnd) Reset() {
	*x = CoreDumpEnd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoreDumpEnd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreDumpEnd) ProtoMessage() {}

func (x *CoreDumpEnd) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreDumpEnd.ProtoReflect.Descriptor instead.
func (*CoreDumpEnd) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{40}
}

// Carries actual binary data for a segment of the coredump
type CoreDumpData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// / This is the last segment of data in this frame
	IsLast bool `protobuf:"varint,1,opt,name=is_last,json=isLast,proto3" json:"is_last,omitempty"`
	// / Byte offset of this data in the overall coredump
	Offset uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	// / Actual data to write out
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CoreDumpData) Reset() {
	*x = CoreDumpData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoreDumpData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreDumpData) ProtoMessage() {}

func (x *CoreDumpData) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreDumpData.ProtoReflect.Descriptor instead.
func (*CoreDumpData) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{41}
}

func (x *CoreDumpData) GetIsLast() bool {
	if x != nil {
		return x.IsLast
	}
	return false
}

func (x *CoreDumpData) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *CoreDumpData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type CoreDumpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Payload:
	//
	//	*CoreDumpReply_Start
	//	*CoreDumpReply_End
	//	*CoreDumpReply_Data
	Payload isCoreDumpReply_Payload `protobuf_oneof:"payload"`
}

func (x *CoreDumpReply) Reset() {
	*x = CoreDumpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoreDumpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreDumpReply) ProtoMessage() {}

func (x *CoreDumpReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreDumpReply.ProtoReflect.Descriptor instead.
func (*CoreDumpReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{42}
}

func (m *CoreDumpReply) GetPayload() isCoreDumpReply_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *CoreDumpReply) GetStart() *CoreDumpStart {
	if x, ok := x.GetPayload().(*CoreDumpReply_Start); ok {
		return x.Start
	}
	return nil
}

func (x *CoreDumpReply) GetEnd() *CoreDumpEnd {
	if x, ok := x.GetPayload().(*CoreDumpReply_End); ok {
		return x.End
	}
	return nil
}

func (x *CoreDumpReply) GetData() *CoreDumpData {
	if x, ok := x.GetPayload().(*CoreDumpReply_Data); ok {
		return x.Data
	}
	return nil
}

type isCoreDumpReply_Payload interface {
	isCoreDumpReply_Payload()
}

type CoreDumpReply_Start struct {
	Start *CoreDumpStart `protobuf:"bytes,1,opt,name=start,proto3,oneof"`
}

type CoreDumpReply_End struct {
	End *CoreDumpEnd `protobuf:"bytes,2,opt,name=end,proto3,oneof"`
}

type CoreDumpReply_Data struct {
	Data *CoreDumpData `protobuf:"bytes,3,opt,name=data,proto3,oneof"`
}

func (*CoreDumpReply_Start) isCoreDumpReply_Payload() {}

func (*CoreDumpReply_End) isCoreDumpReply_Payload() {}

func (*CoreDumpReply_Data) isCoreDumpReply_Payload() {}

// The message frames below are used exclusively over the UDP interface.
type UdpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*UdpRequest_Ping
	//	*UdpRequest_Version
	//	*UdpRequest_Reset_
	//	*UdpRequest_Time
	//	*UdpRequest_Sensor
	//	*UdpRequest_Relay
	//	*UdpRequest_Fan
	//	*UdpRequest_Config
	//	*UdpRequest_Strobe
	//	*UdpRequest_Power
	//	*UdpRequest_Scanner
	//	*UdpRequest_Network
	//	*UdpRequest_Hwinfo
	//	*UdpRequest_Status
	Request isUdpRequest_Request `protobuf_oneof:"request"`
}

func (x *UdpRequest) Reset() {
	*x = UdpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UdpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UdpRequest) ProtoMessage() {}

func (x *UdpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UdpRequest.ProtoReflect.Descriptor instead.
func (*UdpRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{43}
}

func (x *UdpRequest) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *UdpRequest) GetRequest() isUdpRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *UdpRequest) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*UdpRequest_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *UdpRequest) GetVersion() *version.Version_Request {
	if x, ok := x.GetRequest().(*UdpRequest_Version); ok {
		return x.Version
	}
	return nil
}

func (x *UdpRequest) GetReset_() *version.Reset_Request {
	if x, ok := x.GetRequest().(*UdpRequest_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *UdpRequest) GetTime() *time.Request {
	if x, ok := x.GetRequest().(*UdpRequest_Time); ok {
		return x.Time
	}
	return nil
}

func (x *UdpRequest) GetSensor() *SensorRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Sensor); ok {
		return x.Sensor
	}
	return nil
}

func (x *UdpRequest) GetRelay() *RelayRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Relay); ok {
		return x.Relay
	}
	return nil
}

func (x *UdpRequest) GetFan() *FanRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Fan); ok {
		return x.Fan
	}
	return nil
}

func (x *UdpRequest) GetConfig() *ConfigRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Config); ok {
		return x.Config
	}
	return nil
}

func (x *UdpRequest) GetStrobe() *StrobeRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Strobe); ok {
		return x.Strobe
	}
	return nil
}

func (x *UdpRequest) GetPower() *PowerRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Power); ok {
		return x.Power
	}
	return nil
}

func (x *UdpRequest) GetScanner() *ScannerRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Scanner); ok {
		return x.Scanner
	}
	return nil
}

func (x *UdpRequest) GetNetwork() *NetworkRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Network); ok {
		return x.Network
	}
	return nil
}

func (x *UdpRequest) GetHwinfo() *hwinfo.Request {
	if x, ok := x.GetRequest().(*UdpRequest_Hwinfo); ok {
		return x.Hwinfo
	}
	return nil
}

func (x *UdpRequest) GetStatus() *StatusRequest {
	if x, ok := x.GetRequest().(*UdpRequest_Status); ok {
		return x.Status
	}
	return nil
}

type isUdpRequest_Request interface {
	isUdpRequest_Request()
}

type UdpRequest_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type UdpRequest_Version struct {
	Version *version.Version_Request `protobuf:"bytes,3,opt,name=version,proto3,oneof"`
}

type UdpRequest_Reset_ struct {
	Reset_ *version.Reset_Request `protobuf:"bytes,4,opt,name=reset,proto3,oneof"`
}

type UdpRequest_Time struct {
	Time *time.Request `protobuf:"bytes,5,opt,name=time,proto3,oneof"`
}

type UdpRequest_Sensor struct {
	Sensor *SensorRequest `protobuf:"bytes,6,opt,name=sensor,proto3,oneof"`
}

type UdpRequest_Relay struct {
	Relay *RelayRequest `protobuf:"bytes,7,opt,name=relay,proto3,oneof"`
}

type UdpRequest_Fan struct {
	Fan *FanRequest `protobuf:"bytes,8,opt,name=fan,proto3,oneof"`
}

type UdpRequest_Config struct {
	Config *ConfigRequest `protobuf:"bytes,9,opt,name=config,proto3,oneof"`
}

type UdpRequest_Strobe struct {
	Strobe *StrobeRequest `protobuf:"bytes,10,opt,name=strobe,proto3,oneof"`
}

type UdpRequest_Power struct {
	Power *PowerRequest `protobuf:"bytes,11,opt,name=power,proto3,oneof"`
}

type UdpRequest_Scanner struct {
	Scanner *ScannerRequest `protobuf:"bytes,12,opt,name=scanner,proto3,oneof"`
}

type UdpRequest_Network struct {
	Network *NetworkRequest `protobuf:"bytes,13,opt,name=network,proto3,oneof"`
}

type UdpRequest_Hwinfo struct {
	Hwinfo *hwinfo.Request `protobuf:"bytes,14,opt,name=hwinfo,proto3,oneof"`
}

type UdpRequest_Status struct {
	Status *StatusRequest `protobuf:"bytes,15,opt,name=status,proto3,oneof"`
}

func (*UdpRequest_Ping) isUdpRequest_Request() {}

func (*UdpRequest_Version) isUdpRequest_Request() {}

func (*UdpRequest_Reset_) isUdpRequest_Request() {}

func (*UdpRequest_Time) isUdpRequest_Request() {}

func (*UdpRequest_Sensor) isUdpRequest_Request() {}

func (*UdpRequest_Relay) isUdpRequest_Request() {}

func (*UdpRequest_Fan) isUdpRequest_Request() {}

func (*UdpRequest_Config) isUdpRequest_Request() {}

func (*UdpRequest_Strobe) isUdpRequest_Request() {}

func (*UdpRequest_Power) isUdpRequest_Request() {}

func (*UdpRequest_Scanner) isUdpRequest_Request() {}

func (*UdpRequest_Network) isUdpRequest_Request() {}

func (*UdpRequest_Hwinfo) isUdpRequest_Request() {}

func (*UdpRequest_Status) isUdpRequest_Request() {}

type UdpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*UdpReply_Error
	//	*UdpReply_Ack
	//	*UdpReply_Pong
	//	*UdpReply_Version
	//	*UdpReply_Time
	//	*UdpReply_Sensor
	//	*UdpReply_Relay
	//	*UdpReply_Fan
	//	*UdpReply_Config
	//	*UdpReply_Strobe
	//	*UdpReply_Power
	//	*UdpReply_Scanner
	//	*UdpReply_Network
	//	*UdpReply_Hwinfo
	//	*UdpReply_Status
	Reply isUdpReply_Reply `protobuf_oneof:"reply"`
}

func (x *UdpReply) Reset() {
	*x = UdpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UdpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UdpReply) ProtoMessage() {}

func (x *UdpReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UdpReply.ProtoReflect.Descriptor instead.
func (*UdpReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{44}
}

func (x *UdpReply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *UdpReply) GetReply() isUdpReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *UdpReply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*UdpReply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *UdpReply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*UdpReply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *UdpReply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*UdpReply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *UdpReply) GetVersion() *version.Version_Reply {
	if x, ok := x.GetReply().(*UdpReply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *UdpReply) GetTime() *time.Reply {
	if x, ok := x.GetReply().(*UdpReply_Time); ok {
		return x.Time
	}
	return nil
}

func (x *UdpReply) GetSensor() *SensorReply {
	if x, ok := x.GetReply().(*UdpReply_Sensor); ok {
		return x.Sensor
	}
	return nil
}

func (x *UdpReply) GetRelay() *RelayReply {
	if x, ok := x.GetReply().(*UdpReply_Relay); ok {
		return x.Relay
	}
	return nil
}

func (x *UdpReply) GetFan() *FanReply {
	if x, ok := x.GetReply().(*UdpReply_Fan); ok {
		return x.Fan
	}
	return nil
}

func (x *UdpReply) GetConfig() *ConfigReply {
	if x, ok := x.GetReply().(*UdpReply_Config); ok {
		return x.Config
	}
	return nil
}

func (x *UdpReply) GetStrobe() *StrobeReply {
	if x, ok := x.GetReply().(*UdpReply_Strobe); ok {
		return x.Strobe
	}
	return nil
}

func (x *UdpReply) GetPower() *PowerReply {
	if x, ok := x.GetReply().(*UdpReply_Power); ok {
		return x.Power
	}
	return nil
}

func (x *UdpReply) GetScanner() *ScannerReply {
	if x, ok := x.GetReply().(*UdpReply_Scanner); ok {
		return x.Scanner
	}
	return nil
}

func (x *UdpReply) GetNetwork() *NetworkReply {
	if x, ok := x.GetReply().(*UdpReply_Network); ok {
		return x.Network
	}
	return nil
}

func (x *UdpReply) GetHwinfo() *hwinfo.Reply {
	if x, ok := x.GetReply().(*UdpReply_Hwinfo); ok {
		return x.Hwinfo
	}
	return nil
}

func (x *UdpReply) GetStatus() *StatusReply {
	if x, ok := x.GetReply().(*UdpReply_Status); ok {
		return x.Status
	}
	return nil
}

type isUdpReply_Reply interface {
	isUdpReply_Reply()
}

type UdpReply_Error struct {
	Error *error1.Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

type UdpReply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,3,opt,name=ack,proto3,oneof"`
}

type UdpReply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,4,opt,name=pong,proto3,oneof"`
}

type UdpReply_Version struct {
	Version *version.Version_Reply `protobuf:"bytes,5,opt,name=version,proto3,oneof"`
}

type UdpReply_Time struct {
	Time *time.Reply `protobuf:"bytes,6,opt,name=time,proto3,oneof"`
}

type UdpReply_Sensor struct {
	Sensor *SensorReply `protobuf:"bytes,7,opt,name=sensor,proto3,oneof"`
}

type UdpReply_Relay struct {
	Relay *RelayReply `protobuf:"bytes,8,opt,name=relay,proto3,oneof"`
}

type UdpReply_Fan struct {
	Fan *FanReply `protobuf:"bytes,9,opt,name=fan,proto3,oneof"`
}

type UdpReply_Config struct {
	Config *ConfigReply `protobuf:"bytes,10,opt,name=config,proto3,oneof"`
}

type UdpReply_Strobe struct {
	Strobe *StrobeReply `protobuf:"bytes,11,opt,name=strobe,proto3,oneof"`
}

type UdpReply_Power struct {
	Power *PowerReply `protobuf:"bytes,12,opt,name=power,proto3,oneof"`
}

type UdpReply_Scanner struct {
	Scanner *ScannerReply `protobuf:"bytes,13,opt,name=scanner,proto3,oneof"`
}

type UdpReply_Network struct {
	Network *NetworkReply `protobuf:"bytes,14,opt,name=network,proto3,oneof"`
}

type UdpReply_Hwinfo struct {
	Hwinfo *hwinfo.Reply `protobuf:"bytes,15,opt,name=hwinfo,proto3,oneof"`
}

type UdpReply_Status struct {
	Status *StatusReply `protobuf:"bytes,16,opt,name=status,proto3,oneof"`
}

func (*UdpReply_Error) isUdpReply_Reply() {}

func (*UdpReply_Ack) isUdpReply_Reply() {}

func (*UdpReply_Pong) isUdpReply_Reply() {}

func (*UdpReply_Version) isUdpReply_Reply() {}

func (*UdpReply_Time) isUdpReply_Reply() {}

func (*UdpReply_Sensor) isUdpReply_Reply() {}

func (*UdpReply_Relay) isUdpReply_Reply() {}

func (*UdpReply_Fan) isUdpReply_Reply() {}

func (*UdpReply_Config) isUdpReply_Reply() {}

func (*UdpReply_Strobe) isUdpReply_Reply() {}

func (*UdpReply_Power) isUdpReply_Reply() {}

func (*UdpReply_Scanner) isUdpReply_Reply() {}

func (*UdpReply_Network) isUdpReply_Reply() {}

func (*UdpReply_Hwinfo) isUdpReply_Reply() {}

func (*UdpReply_Status) isUdpReply_Reply() {}

// The message formats below are used exclusively over the RS232 interface to the host PC. A
// subset of what's available via UDP can be called over RS232.
type OobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*OobRequest_Ping
	//	*OobRequest_Version
	//	*OobRequest_Reset_
	//	*OobRequest_Config
	//	*OobRequest_Network
	//	*OobRequest_Hwinfo
	//	*OobRequest_Strobe
	//	*OobRequest_Power
	//	*OobRequest_Relay
	Request isOobRequest_Request `protobuf_oneof:"request"`
}

func (x *OobRequest) Reset() {
	*x = OobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OobRequest) ProtoMessage() {}

func (x *OobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OobRequest.ProtoReflect.Descriptor instead.
func (*OobRequest) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{45}
}

func (x *OobRequest) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *OobRequest) GetRequest() isOobRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *OobRequest) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*OobRequest_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *OobRequest) GetVersion() *version.Version_Request {
	if x, ok := x.GetRequest().(*OobRequest_Version); ok {
		return x.Version
	}
	return nil
}

func (x *OobRequest) GetReset_() *version.Reset_Request {
	if x, ok := x.GetRequest().(*OobRequest_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *OobRequest) GetConfig() *ConfigRequest {
	if x, ok := x.GetRequest().(*OobRequest_Config); ok {
		return x.Config
	}
	return nil
}

func (x *OobRequest) GetNetwork() *NetworkRequest {
	if x, ok := x.GetRequest().(*OobRequest_Network); ok {
		return x.Network
	}
	return nil
}

func (x *OobRequest) GetHwinfo() *hwinfo.Request {
	if x, ok := x.GetRequest().(*OobRequest_Hwinfo); ok {
		return x.Hwinfo
	}
	return nil
}

func (x *OobRequest) GetStrobe() *StrobeRequest {
	if x, ok := x.GetRequest().(*OobRequest_Strobe); ok {
		return x.Strobe
	}
	return nil
}

func (x *OobRequest) GetPower() *PowerRequest {
	if x, ok := x.GetRequest().(*OobRequest_Power); ok {
		return x.Power
	}
	return nil
}

func (x *OobRequest) GetRelay() *RelayRequest {
	if x, ok := x.GetRequest().(*OobRequest_Relay); ok {
		return x.Relay
	}
	return nil
}

type isOobRequest_Request interface {
	isOobRequest_Request()
}

type OobRequest_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type OobRequest_Version struct {
	Version *version.Version_Request `protobuf:"bytes,3,opt,name=version,proto3,oneof"`
}

type OobRequest_Reset_ struct {
	Reset_ *version.Reset_Request `protobuf:"bytes,4,opt,name=reset,proto3,oneof"`
}

type OobRequest_Config struct {
	Config *ConfigRequest `protobuf:"bytes,5,opt,name=config,proto3,oneof"`
}

type OobRequest_Network struct {
	Network *NetworkRequest `protobuf:"bytes,6,opt,name=network,proto3,oneof"`
}

type OobRequest_Hwinfo struct {
	Hwinfo *hwinfo.Request `protobuf:"bytes,7,opt,name=hwinfo,proto3,oneof"`
}

type OobRequest_Strobe struct {
	Strobe *StrobeRequest `protobuf:"bytes,8,opt,name=strobe,proto3,oneof"`
}

type OobRequest_Power struct {
	Power *PowerRequest `protobuf:"bytes,9,opt,name=power,proto3,oneof"`
}

type OobRequest_Relay struct {
	Relay *RelayRequest `protobuf:"bytes,10,opt,name=relay,proto3,oneof"`
}

func (*OobRequest_Ping) isOobRequest_Request() {}

func (*OobRequest_Version) isOobRequest_Request() {}

func (*OobRequest_Reset_) isOobRequest_Request() {}

func (*OobRequest_Config) isOobRequest_Request() {}

func (*OobRequest_Network) isOobRequest_Request() {}

func (*OobRequest_Hwinfo) isOobRequest_Request() {}

func (*OobRequest_Strobe) isOobRequest_Request() {}

func (*OobRequest_Power) isOobRequest_Request() {}

func (*OobRequest_Relay) isOobRequest_Request() {}

type OobReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*OobReply_Error
	//	*OobReply_Ack
	//	*OobReply_Pong
	//	*OobReply_Version
	//	*OobReply_Config
	//	*OobReply_Network
	//	*OobReply_Hwinfo
	//	*OobReply_Strobe
	//	*OobReply_Power
	//	*OobReply_Relay
	//	*OobReply_Coredump
	Reply isOobReply_Reply `protobuf_oneof:"reply"`
}

func (x *OobReply) Reset() {
	*x = OobReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OobReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OobReply) ProtoMessage() {}

func (x *OobReply) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OobReply.ProtoReflect.Descriptor instead.
func (*OobReply) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{46}
}

func (x *OobReply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *OobReply) GetReply() isOobReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *OobReply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*OobReply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *OobReply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*OobReply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *OobReply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*OobReply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *OobReply) GetVersion() *version.Version_Reply {
	if x, ok := x.GetReply().(*OobReply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *OobReply) GetConfig() *ConfigReply {
	if x, ok := x.GetReply().(*OobReply_Config); ok {
		return x.Config
	}
	return nil
}

func (x *OobReply) GetNetwork() *NetworkReply {
	if x, ok := x.GetReply().(*OobReply_Network); ok {
		return x.Network
	}
	return nil
}

func (x *OobReply) GetHwinfo() *hwinfo.Reply {
	if x, ok := x.GetReply().(*OobReply_Hwinfo); ok {
		return x.Hwinfo
	}
	return nil
}

func (x *OobReply) GetStrobe() *StrobeReply {
	if x, ok := x.GetReply().(*OobReply_Strobe); ok {
		return x.Strobe
	}
	return nil
}

func (x *OobReply) GetPower() *PowerReply {
	if x, ok := x.GetReply().(*OobReply_Power); ok {
		return x.Power
	}
	return nil
}

func (x *OobReply) GetRelay() *RelayReply {
	if x, ok := x.GetReply().(*OobReply_Relay); ok {
		return x.Relay
	}
	return nil
}

func (x *OobReply) GetCoredump() *CoreDumpReply {
	if x, ok := x.GetReply().(*OobReply_Coredump); ok {
		return x.Coredump
	}
	return nil
}

type isOobReply_Reply interface {
	isOobReply_Reply()
}

type OobReply_Error struct {
	Error *error1.Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

type OobReply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,3,opt,name=ack,proto3,oneof"`
}

type OobReply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,4,opt,name=pong,proto3,oneof"`
}

type OobReply_Version struct {
	Version *version.Version_Reply `protobuf:"bytes,5,opt,name=version,proto3,oneof"`
}

type OobReply_Config struct {
	Config *ConfigReply `protobuf:"bytes,6,opt,name=config,proto3,oneof"`
}

type OobReply_Network struct {
	Network *NetworkReply `protobuf:"bytes,7,opt,name=network,proto3,oneof"`
}

type OobReply_Hwinfo struct {
	Hwinfo *hwinfo.Reply `protobuf:"bytes,8,opt,name=hwinfo,proto3,oneof"`
}

type OobReply_Strobe struct {
	Strobe *StrobeReply `protobuf:"bytes,9,opt,name=strobe,proto3,oneof"`
}

type OobReply_Power struct {
	Power *PowerReply `protobuf:"bytes,10,opt,name=power,proto3,oneof"`
}

type OobReply_Relay struct {
	Relay *RelayReply `protobuf:"bytes,11,opt,name=relay,proto3,oneof"`
}

type OobReply_Coredump struct {
	Coredump *CoreDumpReply `protobuf:"bytes,12,opt,name=coredump,proto3,oneof"`
}

func (*OobReply_Error) isOobReply_Reply() {}

func (*OobReply_Ack) isOobReply_Reply() {}

func (*OobReply_Pong) isOobReply_Reply() {}

func (*OobReply_Version) isOobReply_Reply() {}

func (*OobReply_Config) isOobReply_Reply() {}

func (*OobReply_Network) isOobReply_Reply() {}

func (*OobReply_Hwinfo) isOobReply_Reply() {}

func (*OobReply_Strobe) isOobReply_Reply() {}

func (*OobReply_Power) isOobReply_Reply() {}

func (*OobReply_Relay) isOobReply_Reply() {}

func (*OobReply_Coredump) isOobReply_Reply() {}

// reading data from a single environmental sensor
type SensorReplyEnvdata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp at which this sensor's data was captured
	Timestamp uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// temperature (in °C)
	Temp float32 `protobuf:"fixed32,2,opt,name=temp,proto3" json:"temp,omitempty"`
	// relative humidity, [0, 1]
	Humidity float32 `protobuf:"fixed32,3,opt,name=humidity,proto3" json:"humidity,omitempty"`
	// air pressure (Pa)
	Pressure float32 `protobuf:"fixed32,4,opt,name=pressure,proto3" json:"pressure,omitempty"`
}

func (x *SensorReplyEnvdata) Reset() {
	*x = SensorReplyEnvdata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorReplyEnvdata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorReplyEnvdata) ProtoMessage() {}

func (x *SensorReplyEnvdata) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorReplyEnvdata.ProtoReflect.Descriptor instead.
func (*SensorReplyEnvdata) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{36, 0}
}

func (x *SensorReplyEnvdata) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SensorReplyEnvdata) GetTemp() float32 {
	if x != nil {
		return x.Temp
	}
	return 0
}

func (x *SensorReplyEnvdata) GetHumidity() float32 {
	if x != nil {
		return x.Humidity
	}
	return 0
}

func (x *SensorReplyEnvdata) GetPressure() float32 {
	if x != nil {
		return x.Pressure
	}
	return 0
}

// IMU reading data
type SensorReplyImudata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp at which the IMU readings were captured
	Timestamp uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Accelerometer readings, in g
	Accel []float32 `protobuf:"fixed32,2,rep,packed,name=accel,proto3" json:"accel,omitempty"`
	// Gyroscope readings, in deg/sec
	Gyro []float32 `protobuf:"fixed32,3,rep,packed,name=gyro,proto3" json:"gyro,omitempty"`
}

func (x *SensorReplyImudata) Reset() {
	*x = SensorReplyImudata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorReplyImudata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorReplyImudata) ProtoMessage() {}

func (x *SensorReplyImudata) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorReplyImudata.ProtoReflect.Descriptor instead.
func (*SensorReplyImudata) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{36, 1}
}

func (x *SensorReplyImudata) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SensorReplyImudata) GetAccel() []float32 {
	if x != nil {
		return x.Accel
	}
	return nil
}

func (x *SensorReplyImudata) GetGyro() []float32 {
	if x != nil {
		return x.Gyro
	}
	return nil
}

// External thermistor data (single reading)
type SensorReplyThermdata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp at which this thermistor was sampled
	Timestamp uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Temperature, in °C
	Temp float32 `protobuf:"fixed32,2,opt,name=temp,proto3" json:"temp,omitempty"`
}

func (x *SensorReplyThermdata) Reset() {
	*x = SensorReplyThermdata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorReplyThermdata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorReplyThermdata) ProtoMessage() {}

func (x *SensorReplyThermdata) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorReplyThermdata.ProtoReflect.Descriptor instead.
func (*SensorReplyThermdata) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{36, 2}
}

func (x *SensorReplyThermdata) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SensorReplyThermdata) GetTemp() float32 {
	if x != nil {
		return x.Temp
	}
	return 0
}

// Leak sensor (boolean toggle with timestamp)
type SensorReplyLeakdata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp when this leak sensor changed state
	Timestamp uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Current leak sensor state
	Active bool `protobuf:"varint,2,opt,name=active,proto3" json:"active,omitempty"`
}

func (x *SensorReplyLeakdata) Reset() {
	*x = SensorReplyLeakdata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorReplyLeakdata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorReplyLeakdata) ProtoMessage() {}

func (x *SensorReplyLeakdata) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorReplyLeakdata.ProtoReflect.Descriptor instead.
func (*SensorReplyLeakdata) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{36, 3}
}

func (x *SensorReplyLeakdata) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SensorReplyLeakdata) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

// Pressure transducer data
type SensorReplyPressdata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp at which this pressure transducer was sampled
	Timestamp uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Pressure value
	Pressure float32 `protobuf:"fixed32,2,opt,name=pressure,proto3" json:"pressure,omitempty"`
	// Associated temperature value (°C)
	Temperature float32 `protobuf:"fixed32,3,opt,name=temperature,proto3" json:"temperature,omitempty"`
}

func (x *SensorReplyPressdata) Reset() {
	*x = SensorReplyPressdata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_reaper_module_controller_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensorReplyPressdata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorReplyPressdata) ProtoMessage() {}

func (x *SensorReplyPressdata) ProtoReflect() protoreflect.Message {
	mi := &file_reaper_module_controller_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorReplyPressdata.ProtoReflect.Descriptor instead.
func (*SensorReplyPressdata) Descriptor() ([]byte, []int) {
	return file_reaper_module_controller_proto_rawDescGZIP(), []int{36, 4}
}

func (x *SensorReplyPressdata) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SensorReplyPressdata) GetPressure() float32 {
	if x != nil {
		return x.Pressure
	}
	return 0
}

func (x *SensorReplyPressdata) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

var File_reaper_module_controller_proto protoreflect.FileDescriptor

var file_reaper_module_controller_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61,
	0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x37, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x16, 0x0a, 0x14, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x15, 0x0a, 0x13, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1a, 0x0a, 0x18, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x82, 0x02, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x45, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x54, 0x0a, 0x0a, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42,
	0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb5, 0x01, 0x0a, 0x0e, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x46, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x6e,
	0x6b, 0x55, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x69, 0x6e, 0x6b, 0x55,
	0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03,
	0x6d, 0x61, 0x63, 0x12, 0x46, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0x5f, 0x0a, 0x0c, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x46, 0x0a, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xbd, 0x01, 0x0a,
	0x0d, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22,
	0x0a, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x75, 0x73, 0x65, 0x42, 0x6c, 0x6f, 0x77, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x75, 0x73, 0x65, 0x42, 0x6c, 0x6f, 0x77, 0x6e,
	0x12, 0x24, 0x0a, 0x0d, 0x66, 0x75, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x66, 0x75, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x12, 0x2a, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x19, 0x0a, 0x17,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x74, 0x0a, 0x16, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x53, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42,
	0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x22, 0x74, 0x0a,
	0x16, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4f, 0x63, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x41, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x41, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x42, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x08, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x42, 0x22, 0xf8, 0x01, 0x0a, 0x0e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x72, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x44, 0x0a,
	0x03, 0x6f, 0x63, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x72, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x4f, 0x63, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03,
	0x6f, 0x63, 0x70, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x51,
	0x0a, 0x12, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x5f, 0x0a, 0x0c, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0xde, 0x01, 0x0a, 0x0c, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x79,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52,
	0x0b, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x21, 0x0a, 0x09, 0x65, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x02, 0x52, 0x09, 0x65, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x43, 0x61, 0x6d, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65, 0x6c, 0x61,
	0x79, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x65, 0x74, 0x68, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x43, 0x61, 0x6d, 0x22, 0x8c, 0x01, 0x0a, 0x0a, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x65, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43,
	0x61, 0x6d, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a, 0x15, 0x53, 0x65, 0x74,
	0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3f, 0x0a, 0x19,
	0x54, 0x69, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x65, 0x63, 0x22, 0xe8, 0x03,
	0x0a, 0x11, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a,
	0x10, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x76, 0x6f, 0x6c, 0x74, 0x61, 0x67, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x32, 0x0a, 0x14, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x14, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0d, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0d, 0x48, 0x01, 0x52, 0x08, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x65,
	0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x02,
	0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x55, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x55, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x65,
	0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x22, 0xd0, 0x02, 0x0a, 0x0d, 0x53, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x65,
	0x74, 0x57, 0x61, 0x76, 0x65, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x74, 0x57,
	0x61, 0x76, 0x65, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x4d, 0x0a, 0x09, 0x67, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x67, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x73, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x59, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5d, 0x0a, 0x0b, 0x53,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x0a, 0x0e, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x39, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x4f, 0x74, 0x70, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x04, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x07, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x5d, 0x0a, 0x11, 0x53,
	0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x04, 0x63, 0x62, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x04, 0x63, 0x62, 0x73, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x61, 0x73, 0x73,
	0x79, 0x53, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x61, 0x73, 0x73,
	0x79, 0x53, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x62, 0x73, 0x6e, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x61, 0x73, 0x73, 0x79, 0x53, 0x6e, 0x22, 0xe4, 0x02, 0x0a, 0x0d, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x0b,
	0x67, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x67, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x47, 0x0a, 0x07, 0x6f, 0x74, 0x70, 0x4c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x4f, 0x74, 0x70, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x07, 0x6f, 0x74, 0x70, 0x4c, 0x6f, 0x63, 0x6b, 0x12, 0x59, 0x0a, 0x10, 0x73,
	0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x10, 0x73, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x5e, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x46, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x92, 0x01, 0x0a, 0x10, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x73, 0x65, 0x74, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x79, 0x73, 0x74, 0x65, 0x72, 0x65, 0x73, 0x69, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x68, 0x79, 0x73, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x69, 0x73, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x54, 0x68,
	0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x53, 0x0a, 0x0d, 0x46, 0x61, 0x6e, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x61, 0x6e, 0x31, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x66, 0x61, 0x6e, 0x31, 0x88, 0x01, 0x01,
	0x12, 0x17, 0x0a, 0x04, 0x66, 0x61, 0x6e, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01,
	0x52, 0x04, 0x66, 0x61, 0x6e, 0x32, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66, 0x61,
	0x6e, 0x31, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66, 0x61, 0x6e, 0x32, 0x22, 0x9b, 0x01, 0x0a, 0x1a,
	0x46, 0x61, 0x6e, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88,
	0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xb0, 0x01, 0x0a, 0x0a, 0x46, 0x61,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x2e, 0x46, 0x61, 0x6e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x03, 0x73, 0x65, 0x74, 0x12, 0x5a, 0x0a, 0x0c, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x46, 0x61, 0x6e, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x6f,
	0x73, 0x74, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x98, 0x02, 0x0a,
	0x08, 0x46, 0x61, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x61, 0x6e,
	0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x66, 0x61, 0x6e, 0x31, 0x12, 0x12, 0x0a,
	0x04, 0x66, 0x61, 0x6e, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x66, 0x61, 0x6e,
	0x32, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x74, 0x68,
	0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x5b, 0x0a, 0x10, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x10, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73,
	0x74, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10,
	0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x10, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f,
	0x73, 0x74, 0x61, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61,
	0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x22, 0x6e, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x13, 0x0a, 0x02, 0x70, 0x63, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x02, 0x70, 0x63, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x62, 0x74, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x03, 0x62, 0x74, 0x6c,
	0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x02, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x05,
	0x0a, 0x03, 0x5f, 0x70, 0x63, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x62, 0x74, 0x6c, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x22, 0x44, 0x0a, 0x0a, 0x52, 0x65, 0x6c, 0x61, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x02, 0x70, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x74, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x03, 0x62, 0x74, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x22, 0x0f, 0x0a,
	0x0d, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xbb,
	0x06, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3f,
	0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x76, 0x64, 0x61, 0x74, 0x61, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12,
	0x3f, 0x0a, 0x03, 0x69, 0x6d, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x69, 0x6d, 0x75, 0x64, 0x61, 0x74, 0x61, 0x52, 0x03, 0x69, 0x6d, 0x75,
	0x12, 0x45, 0x0a, 0x05, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x05, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x12, 0x42, 0x0a, 0x04, 0x6c, 0x65, 0x61, 0x6b, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x6c, 0x65, 0x61,
	0x6b, 0x64, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x65, 0x61, 0x6b, 0x12, 0x45, 0x0a, 0x05, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x72, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x70, 0x72, 0x65, 0x73, 0x73, 0x64, 0x61, 0x74, 0x61, 0x52, 0x05, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x61, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x45, 0x6e, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x76, 0x1a, 0x73, 0x0a, 0x07, 0x65, 0x6e,
	0x76, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x04, 0x74, 0x65, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x68, 0x75, 0x6d, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x1a,
	0x51, 0x0a, 0x07, 0x69, 0x6d, 0x75, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x63, 0x63, 0x65,
	0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x67, 0x79, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x04, 0x67, 0x79,
	0x72, 0x6f, 0x1a, 0x3d, 0x0a, 0x09, 0x74, 0x68, 0x65, 0x72, 0x6d, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x74, 0x65, 0x6d,
	0x70, 0x1a, 0x40, 0x0a, 0x08, 0x6c, 0x65, 0x61, 0x6b, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x1a, 0x67, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x73, 0x73, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x0f, 0x0a, 0x0d,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xd2, 0x02,
	0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3f, 0x0a,
	0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x3c,
	0x0a, 0x06, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x52, 0x06, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x73, 0x12, 0x3a, 0x0a, 0x05,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x43, 0x0a,
	0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x73, 0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x22, 0x0d, 0x0a, 0x0b, 0x43, 0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x45,
	0x6e, 0x64, 0x22, 0x53, 0x0a, 0x0c, 0x43, 0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd4, 0x01, 0x0a, 0x0d, 0x43, 0x6f, 0x72, 0x65,
	0x44, 0x75, 0x6d, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x39, 0x0a, 0x03, 0x65, 0x6e,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x45, 0x6e, 0x64, 0x48, 0x00,
	0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xf7,
	0x06, 0x0a, 0x0a, 0x55, 0x64, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a,
	0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52,
	0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x23, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x41, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x05, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65,
	0x6c, 0x61, 0x79, 0x12, 0x38, 0x0a, 0x03, 0x66, 0x61, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x46, 0x61, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x66, 0x61, 0x6e, 0x12, 0x41, 0x0a,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x41, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72,
	0x6f, 0x62, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x07, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x29, 0x0a, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a,
	0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xef, 0x06, 0x0a, 0x08, 0x55, 0x64, 0x70,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61,
	0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41,
	0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x6f, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x6e,
	0x67, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x3c, 0x0a, 0x05, 0x72, 0x65, 0x6c,
	0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x05, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x36, 0x0a, 0x03, 0x66, 0x61, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e,
	0x46, 0x61, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x66, 0x61, 0x6e, 0x12,
	0x3f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f,
	0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62,
	0x65, 0x12, 0x3c, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12,
	0x42, 0x0a, 0x07, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x27, 0x0a, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f,
	0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f,
	0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xcc, 0x04, 0x0a, 0x0a, 0x4f,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e,
	0x67, 0x12, 0x34, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x41, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x44, 0x0a, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x06, 0x68, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x06, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x3e,
	0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x3e,
	0x0a, 0x05, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x42, 0x09,
	0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x95, 0x05, 0x0a, 0x08, 0x4f, 0x6f,
	0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03,
	0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e,
	0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x6f,
	0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6f,
	0x6e, 0x67, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x42, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x27, 0x0a, 0x06, 0x68,
	0x77, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x68, 0x77,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x68, 0x77,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e,
	0x53, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x05, 0x72, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x6c, 0x61, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x6c, 0x61,
	0x79, 0x12, 0x45, 0x0a, 0x08, 0x63, 0x6f, 0x72, 0x65, 0x64, 0x75, 0x6d, 0x70, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08,
	0x63, 0x6f, 0x72, 0x65, 0x64, 0x75, 0x6d, 0x70, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c,
	0x79, 0x2a, 0x38, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x41, 0x54, 0x49, 0x43, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x48, 0x43, 0x50, 0x10, 0x02, 0x2a, 0x49, 0x0a, 0x10, 0x54,
	0x68, 0x65, 0x72, 0x6d, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x45, 0x4e, 0x56, 0x49, 0x52, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x56, 0x49, 0x52, 0x4f, 0x5f, 0x45, 0x58, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x02, 0x42, 0x21, 0x5a, 0x1f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_reaper_module_controller_proto_rawDescOnce sync.Once
	file_reaper_module_controller_proto_rawDescData = file_reaper_module_controller_proto_rawDesc
)

func file_reaper_module_controller_proto_rawDescGZIP() []byte {
	file_reaper_module_controller_proto_rawDescOnce.Do(func() {
		file_reaper_module_controller_proto_rawDescData = protoimpl.X.CompressGZIP(file_reaper_module_controller_proto_rawDescData)
	})
	return file_reaper_module_controller_proto_rawDescData
}

var file_reaper_module_controller_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_reaper_module_controller_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_reaper_module_controller_proto_goTypes = []interface{}{
	(NetworkAddressSource)(0),          // 0: reaper_module_controller.NetworkAddressSource
	(ThermostatSource)(0),              // 1: reaper_module_controller.ThermostatSource
	(*NetworkConfigRequest)(nil),       // 2: reaper_module_controller.NetworkConfigRequest
	(*NetworkResetRequest)(nil),        // 3: reaper_module_controller.NetworkResetRequest
	(*NetworkPowerCycleRequest)(nil),   // 4: reaper_module_controller.NetworkPowerCycleRequest
	(*NetworkRequest)(nil),             // 5: reaper_module_controller.NetworkRequest
	(*NetworkAddress)(nil),             // 6: reaper_module_controller.NetworkAddress
	(*NetworkConfigReply)(nil),         // 7: reaper_module_controller.NetworkConfigReply
	(*NetworkReply)(nil),               // 8: reaper_module_controller.NetworkReply
	(*ScannerStatus)(nil),              // 9: reaper_module_controller.ScannerStatus
	(*ScannerGetStatusRequest)(nil),    // 10: reaper_module_controller.ScannerGetStatusRequest
	(*ScannerSetPowerRequest)(nil),     // 11: reaper_module_controller.ScannerSetPowerRequest
	(*ScannerResetOcpRequest)(nil),     // 12: reaper_module_controller.ScannerResetOcpRequest
	(*ScannerRequest)(nil),             // 13: reaper_module_controller.ScannerRequest
	(*ScannerStatusReply)(nil),         // 14: reaper_module_controller.ScannerStatusReply
	(*ScannerReply)(nil),               // 15: reaper_module_controller.ScannerReply
	(*PowerRequest)(nil),               // 16: reaper_module_controller.PowerRequest
	(*PowerReply)(nil),                 // 17: reaper_module_controller.PowerReply
	(*StrobeStatusRequest)(nil),        // 18: reaper_module_controller.StrobeStatusRequest
	(*SetStrobeStateRequest)(nil),      // 19: reaper_module_controller.SetStrobeStateRequest
	(*TimedStrobeDisableRequest)(nil),  // 20: reaper_module_controller.TimedStrobeDisableRequest
	(*StrobeStatusReply)(nil),          // 21: reaper_module_controller.StrobeStatusReply
	(*StrobeRequest)(nil),              // 22: reaper_module_controller.StrobeRequest
	(*StrobeReply)(nil),                // 23: reaper_module_controller.StrobeReply
	(*ModuleIdentity)(nil),             // 24: reaper_module_controller.ModuleIdentity
	(*GetModuleIdentityRequest)(nil),   // 25: reaper_module_controller.GetModuleIdentityRequest
	(*SetOtpLockRequest)(nil),          // 26: reaper_module_controller.SetOtpLockRequest
	(*SetBoardIdRequest)(nil),          // 27: reaper_module_controller.SetBoardIdRequest
	(*ConfigRequest)(nil),              // 28: reaper_module_controller.ConfigRequest
	(*ConfigReply)(nil),                // 29: reaper_module_controller.ConfigReply
	(*ThermostatConfig)(nil),           // 30: reaper_module_controller.ThermostatConfig
	(*FanSetRequest)(nil),              // 31: reaper_module_controller.FanSetRequest
	(*FanThermostatConfigRequest)(nil), // 32: reaper_module_controller.FanThermostatConfigRequest
	(*FanRequest)(nil),                 // 33: reaper_module_controller.FanRequest
	(*FanReply)(nil),                   // 34: reaper_module_controller.FanReply
	(*RelayRequest)(nil),               // 35: reaper_module_controller.RelayRequest
	(*RelayReply)(nil),                 // 36: reaper_module_controller.RelayReply
	(*SensorRequest)(nil),              // 37: reaper_module_controller.SensorRequest
	(*SensorReply)(nil),                // 38: reaper_module_controller.SensorReply
	(*StatusRequest)(nil),              // 39: reaper_module_controller.StatusRequest
	(*StatusReply)(nil),                // 40: reaper_module_controller.StatusReply
	(*CoreDumpStart)(nil),              // 41: reaper_module_controller.CoreDumpStart
	(*CoreDumpEnd)(nil),                // 42: reaper_module_controller.CoreDumpEnd
	(*CoreDumpData)(nil),               // 43: reaper_module_controller.CoreDumpData
	(*CoreDumpReply)(nil),              // 44: reaper_module_controller.CoreDumpReply
	(*UdpRequest)(nil),                 // 45: reaper_module_controller.UdpRequest
	(*UdpReply)(nil),                   // 46: reaper_module_controller.UdpReply
	(*OobRequest)(nil),                 // 47: reaper_module_controller.OobRequest
	(*OobReply)(nil),                   // 48: reaper_module_controller.OobReply
	(*SensorReplyEnvdata)(nil),         // 49: reaper_module_controller.SensorReply.envdata
	(*SensorReplyImudata)(nil),         // 50: reaper_module_controller.SensorReply.imudata
	(*SensorReplyThermdata)(nil),       // 51: reaper_module_controller.SensorReply.thermdata
	(*SensorReplyLeakdata)(nil),        // 52: reaper_module_controller.SensorReply.leakdata
	(*SensorReplyPressdata)(nil),       // 53: reaper_module_controller.SensorReply.pressdata
	(*strobe_control.Request)(nil),     // 54: strobe_control.Request
	(*request.RequestHeader)(nil),      // 55: request.RequestHeader
	(*diagnostic.Ping)(nil),            // 56: diagnostic.Ping
	(*version.Version_Request)(nil),    // 57: version.Version_Request
	(*version.Reset_Request)(nil),      // 58: version.Reset_Request
	(*time.Request)(nil),               // 59: time.Request
	(*hwinfo.Request)(nil),             // 60: hwinfo.Request
	(*error1.Error)(nil),               // 61: error.Error
	(*ack.Ack)(nil),                    // 62: ack.Ack
	(*diagnostic.Pong)(nil),            // 63: diagnostic.Pong
	(*version.Version_Reply)(nil),      // 64: version.Version_Reply
	(*time.Reply)(nil),                 // 65: time.Reply
	(*hwinfo.Reply)(nil),               // 66: hwinfo.Reply
}
var file_reaper_module_controller_proto_depIdxs = []int32{
	2,  // 0: reaper_module_controller.NetworkRequest.config:type_name -> reaper_module_controller.NetworkConfigRequest
	3,  // 1: reaper_module_controller.NetworkRequest.reset:type_name -> reaper_module_controller.NetworkResetRequest
	4,  // 2: reaper_module_controller.NetworkRequest.powerCycle:type_name -> reaper_module_controller.NetworkPowerCycleRequest
	0,  // 3: reaper_module_controller.NetworkAddress.source:type_name -> reaper_module_controller.NetworkAddressSource
	6,  // 4: reaper_module_controller.NetworkConfigReply.addresses:type_name -> reaper_module_controller.NetworkAddress
	7,  // 5: reaper_module_controller.NetworkReply.config:type_name -> reaper_module_controller.NetworkConfigReply
	10, // 6: reaper_module_controller.ScannerRequest.status:type_name -> reaper_module_controller.ScannerGetStatusRequest
	11, // 7: reaper_module_controller.ScannerRequest.power:type_name -> reaper_module_controller.ScannerSetPowerRequest
	12, // 8: reaper_module_controller.ScannerRequest.ocp:type_name -> reaper_module_controller.ScannerResetOcpRequest
	9,  // 9: reaper_module_controller.ScannerStatusReply.data:type_name -> reaper_module_controller.ScannerStatus
	14, // 10: reaper_module_controller.ScannerReply.status:type_name -> reaper_module_controller.ScannerStatusReply
	54, // 11: reaper_module_controller.StrobeRequest.setWaveform:type_name -> strobe_control.Request
	18, // 12: reaper_module_controller.StrobeRequest.getStatus:type_name -> reaper_module_controller.StrobeStatusRequest
	19, // 13: reaper_module_controller.StrobeRequest.setState:type_name -> reaper_module_controller.SetStrobeStateRequest
	20, // 14: reaper_module_controller.StrobeRequest.timedDisable:type_name -> reaper_module_controller.TimedStrobeDisableRequest
	21, // 15: reaper_module_controller.StrobeReply.status:type_name -> reaper_module_controller.StrobeStatusReply
	25, // 16: reaper_module_controller.ConfigRequest.getIdentity:type_name -> reaper_module_controller.GetModuleIdentityRequest
	24, // 17: reaper_module_controller.ConfigRequest.setIdentity:type_name -> reaper_module_controller.ModuleIdentity
	26, // 18: reaper_module_controller.ConfigRequest.otpLock:type_name -> reaper_module_controller.SetOtpLockRequest
	27, // 19: reaper_module_controller.ConfigRequest.setBoardIdentity:type_name -> reaper_module_controller.SetBoardIdRequest
	24, // 20: reaper_module_controller.ConfigReply.identity:type_name -> reaper_module_controller.ModuleIdentity
	1,  // 21: reaper_module_controller.ThermostatConfig.source:type_name -> reaper_module_controller.ThermostatSource
	30, // 22: reaper_module_controller.FanThermostatConfigRequest.config:type_name -> reaper_module_controller.ThermostatConfig
	31, // 23: reaper_module_controller.FanRequest.set:type_name -> reaper_module_controller.FanSetRequest
	32, // 24: reaper_module_controller.FanRequest.thermoConfig:type_name -> reaper_module_controller.FanThermostatConfigRequest
	30, // 25: reaper_module_controller.FanReply.thermostatConfig:type_name -> reaper_module_controller.ThermostatConfig
	49, // 26: reaper_module_controller.SensorReply.env:type_name -> reaper_module_controller.SensorReply.envdata
	50, // 27: reaper_module_controller.SensorReply.imu:type_name -> reaper_module_controller.SensorReply.imudata
	51, // 28: reaper_module_controller.SensorReply.therm:type_name -> reaper_module_controller.SensorReply.thermdata
	52, // 29: reaper_module_controller.SensorReply.leak:type_name -> reaper_module_controller.SensorReply.leakdata
	53, // 30: reaper_module_controller.SensorReply.press:type_name -> reaper_module_controller.SensorReply.pressdata
	38, // 31: reaper_module_controller.StatusReply.sensors:type_name -> reaper_module_controller.SensorReply
	36, // 32: reaper_module_controller.StatusReply.relays:type_name -> reaper_module_controller.RelayReply
	17, // 33: reaper_module_controller.StatusReply.power:type_name -> reaper_module_controller.PowerReply
	21, // 34: reaper_module_controller.StatusReply.strobe:type_name -> reaper_module_controller.StrobeStatusReply
	9,  // 35: reaper_module_controller.StatusReply.scanners:type_name -> reaper_module_controller.ScannerStatus
	41, // 36: reaper_module_controller.CoreDumpReply.start:type_name -> reaper_module_controller.CoreDumpStart
	42, // 37: reaper_module_controller.CoreDumpReply.end:type_name -> reaper_module_controller.CoreDumpEnd
	43, // 38: reaper_module_controller.CoreDumpReply.data:type_name -> reaper_module_controller.CoreDumpData
	55, // 39: reaper_module_controller.UdpRequest.header:type_name -> request.RequestHeader
	56, // 40: reaper_module_controller.UdpRequest.ping:type_name -> diagnostic.Ping
	57, // 41: reaper_module_controller.UdpRequest.version:type_name -> version.Version_Request
	58, // 42: reaper_module_controller.UdpRequest.reset:type_name -> version.Reset_Request
	59, // 43: reaper_module_controller.UdpRequest.time:type_name -> time.Request
	37, // 44: reaper_module_controller.UdpRequest.sensor:type_name -> reaper_module_controller.SensorRequest
	35, // 45: reaper_module_controller.UdpRequest.relay:type_name -> reaper_module_controller.RelayRequest
	33, // 46: reaper_module_controller.UdpRequest.fan:type_name -> reaper_module_controller.FanRequest
	28, // 47: reaper_module_controller.UdpRequest.config:type_name -> reaper_module_controller.ConfigRequest
	22, // 48: reaper_module_controller.UdpRequest.strobe:type_name -> reaper_module_controller.StrobeRequest
	16, // 49: reaper_module_controller.UdpRequest.power:type_name -> reaper_module_controller.PowerRequest
	13, // 50: reaper_module_controller.UdpRequest.scanner:type_name -> reaper_module_controller.ScannerRequest
	5,  // 51: reaper_module_controller.UdpRequest.network:type_name -> reaper_module_controller.NetworkRequest
	60, // 52: reaper_module_controller.UdpRequest.hwinfo:type_name -> hwinfo.Request
	39, // 53: reaper_module_controller.UdpRequest.status:type_name -> reaper_module_controller.StatusRequest
	55, // 54: reaper_module_controller.UdpReply.header:type_name -> request.RequestHeader
	61, // 55: reaper_module_controller.UdpReply.error:type_name -> error.Error
	62, // 56: reaper_module_controller.UdpReply.ack:type_name -> ack.Ack
	63, // 57: reaper_module_controller.UdpReply.pong:type_name -> diagnostic.Pong
	64, // 58: reaper_module_controller.UdpReply.version:type_name -> version.Version_Reply
	65, // 59: reaper_module_controller.UdpReply.time:type_name -> time.Reply
	38, // 60: reaper_module_controller.UdpReply.sensor:type_name -> reaper_module_controller.SensorReply
	36, // 61: reaper_module_controller.UdpReply.relay:type_name -> reaper_module_controller.RelayReply
	34, // 62: reaper_module_controller.UdpReply.fan:type_name -> reaper_module_controller.FanReply
	29, // 63: reaper_module_controller.UdpReply.config:type_name -> reaper_module_controller.ConfigReply
	23, // 64: reaper_module_controller.UdpReply.strobe:type_name -> reaper_module_controller.StrobeReply
	17, // 65: reaper_module_controller.UdpReply.power:type_name -> reaper_module_controller.PowerReply
	15, // 66: reaper_module_controller.UdpReply.scanner:type_name -> reaper_module_controller.ScannerReply
	8,  // 67: reaper_module_controller.UdpReply.network:type_name -> reaper_module_controller.NetworkReply
	66, // 68: reaper_module_controller.UdpReply.hwinfo:type_name -> hwinfo.Reply
	40, // 69: reaper_module_controller.UdpReply.status:type_name -> reaper_module_controller.StatusReply
	55, // 70: reaper_module_controller.OobRequest.header:type_name -> request.RequestHeader
	56, // 71: reaper_module_controller.OobRequest.ping:type_name -> diagnostic.Ping
	57, // 72: reaper_module_controller.OobRequest.version:type_name -> version.Version_Request
	58, // 73: reaper_module_controller.OobRequest.reset:type_name -> version.Reset_Request
	28, // 74: reaper_module_controller.OobRequest.config:type_name -> reaper_module_controller.ConfigRequest
	5,  // 75: reaper_module_controller.OobRequest.network:type_name -> reaper_module_controller.NetworkRequest
	60, // 76: reaper_module_controller.OobRequest.hwinfo:type_name -> hwinfo.Request
	22, // 77: reaper_module_controller.OobRequest.strobe:type_name -> reaper_module_controller.StrobeRequest
	16, // 78: reaper_module_controller.OobRequest.power:type_name -> reaper_module_controller.PowerRequest
	35, // 79: reaper_module_controller.OobRequest.relay:type_name -> reaper_module_controller.RelayRequest
	55, // 80: reaper_module_controller.OobReply.header:type_name -> request.RequestHeader
	61, // 81: reaper_module_controller.OobReply.error:type_name -> error.Error
	62, // 82: reaper_module_controller.OobReply.ack:type_name -> ack.Ack
	63, // 83: reaper_module_controller.OobReply.pong:type_name -> diagnostic.Pong
	64, // 84: reaper_module_controller.OobReply.version:type_name -> version.Version_Reply
	29, // 85: reaper_module_controller.OobReply.config:type_name -> reaper_module_controller.ConfigReply
	8,  // 86: reaper_module_controller.OobReply.network:type_name -> reaper_module_controller.NetworkReply
	66, // 87: reaper_module_controller.OobReply.hwinfo:type_name -> hwinfo.Reply
	23, // 88: reaper_module_controller.OobReply.strobe:type_name -> reaper_module_controller.StrobeReply
	17, // 89: reaper_module_controller.OobReply.power:type_name -> reaper_module_controller.PowerReply
	36, // 90: reaper_module_controller.OobReply.relay:type_name -> reaper_module_controller.RelayReply
	44, // 91: reaper_module_controller.OobReply.coredump:type_name -> reaper_module_controller.CoreDumpReply
	92, // [92:92] is the sub-list for method output_type
	92, // [92:92] is the sub-list for method input_type
	92, // [92:92] is the sub-list for extension type_name
	92, // [92:92] is the sub-list for extension extendee
	0,  // [0:92] is the sub-list for field type_name
}

func init() { file_reaper_module_controller_proto_init() }
func file_reaper_module_controller_proto_init() {
	if File_reaper_module_controller_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_reaper_module_controller_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkResetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkPowerCycleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerGetStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerSetPowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerResetOcpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrobeStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetStrobeStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimedStrobeDisableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrobeStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrobeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrobeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleIdentity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModuleIdentityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetOtpLockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBoardIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThermostatConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FanSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FanThermostatConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FanReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelayReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoreDumpStart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoreDumpEnd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoreDumpData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoreDumpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UdpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UdpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OobReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorReplyEnvdata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorReplyImudata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorReplyThermdata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorReplyLeakdata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_reaper_module_controller_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensorReplyPressdata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_reaper_module_controller_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*NetworkRequest_Config)(nil),
		(*NetworkRequest_Reset_)(nil),
		(*NetworkRequest_PowerCycle)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*NetworkReply_Config)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*ScannerRequest_Status)(nil),
		(*ScannerRequest_Power)(nil),
		(*ScannerRequest_Ocp)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*ScannerReply_Status)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*StrobeRequest_SetWaveform)(nil),
		(*StrobeRequest_GetStatus)(nil),
		(*StrobeRequest_SetState)(nil),
		(*StrobeRequest_TimedDisable)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*StrobeReply_Status)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[26].OneofWrappers = []interface{}{
		(*ConfigRequest_GetIdentity)(nil),
		(*ConfigRequest_SetIdentity)(nil),
		(*ConfigRequest_OtpLock)(nil),
		(*ConfigRequest_SetBoardIdentity)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[27].OneofWrappers = []interface{}{
		(*ConfigReply_Identity)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[30].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[31].OneofWrappers = []interface{}{
		(*FanRequest_Set)(nil),
		(*FanRequest_ThermoConfig)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[32].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[33].OneofWrappers = []interface{}{}
	file_reaper_module_controller_proto_msgTypes[42].OneofWrappers = []interface{}{
		(*CoreDumpReply_Start)(nil),
		(*CoreDumpReply_End)(nil),
		(*CoreDumpReply_Data)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[43].OneofWrappers = []interface{}{
		(*UdpRequest_Ping)(nil),
		(*UdpRequest_Version)(nil),
		(*UdpRequest_Reset_)(nil),
		(*UdpRequest_Time)(nil),
		(*UdpRequest_Sensor)(nil),
		(*UdpRequest_Relay)(nil),
		(*UdpRequest_Fan)(nil),
		(*UdpRequest_Config)(nil),
		(*UdpRequest_Strobe)(nil),
		(*UdpRequest_Power)(nil),
		(*UdpRequest_Scanner)(nil),
		(*UdpRequest_Network)(nil),
		(*UdpRequest_Hwinfo)(nil),
		(*UdpRequest_Status)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[44].OneofWrappers = []interface{}{
		(*UdpReply_Error)(nil),
		(*UdpReply_Ack)(nil),
		(*UdpReply_Pong)(nil),
		(*UdpReply_Version)(nil),
		(*UdpReply_Time)(nil),
		(*UdpReply_Sensor)(nil),
		(*UdpReply_Relay)(nil),
		(*UdpReply_Fan)(nil),
		(*UdpReply_Config)(nil),
		(*UdpReply_Strobe)(nil),
		(*UdpReply_Power)(nil),
		(*UdpReply_Scanner)(nil),
		(*UdpReply_Network)(nil),
		(*UdpReply_Hwinfo)(nil),
		(*UdpReply_Status)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[45].OneofWrappers = []interface{}{
		(*OobRequest_Ping)(nil),
		(*OobRequest_Version)(nil),
		(*OobRequest_Reset_)(nil),
		(*OobRequest_Config)(nil),
		(*OobRequest_Network)(nil),
		(*OobRequest_Hwinfo)(nil),
		(*OobRequest_Strobe)(nil),
		(*OobRequest_Power)(nil),
		(*OobRequest_Relay)(nil),
	}
	file_reaper_module_controller_proto_msgTypes[46].OneofWrappers = []interface{}{
		(*OobReply_Error)(nil),
		(*OobReply_Ack)(nil),
		(*OobReply_Pong)(nil),
		(*OobReply_Version)(nil),
		(*OobReply_Config)(nil),
		(*OobReply_Network)(nil),
		(*OobReply_Hwinfo)(nil),
		(*OobReply_Strobe)(nil),
		(*OobReply_Power)(nil),
		(*OobReply_Relay)(nil),
		(*OobReply_Coredump)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_reaper_module_controller_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_reaper_module_controller_proto_goTypes,
		DependencyIndexes: file_reaper_module_controller_proto_depIdxs,
		EnumInfos:         file_reaper_module_controller_proto_enumTypes,
		MessageInfos:      file_reaper_module_controller_proto_msgTypes,
	}.Build()
	File_reaper_module_controller_proto = out.File
	file_reaper_module_controller_proto_rawDesc = nil
	file_reaper_module_controller_proto_goTypes = nil
	file_reaper_module_controller_proto_depIdxs = nil
}

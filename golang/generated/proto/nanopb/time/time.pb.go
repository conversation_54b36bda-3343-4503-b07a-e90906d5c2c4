// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: time.proto

package time

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Timestamp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seconds uint32 `protobuf:"varint,1,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Micros  uint32 `protobuf:"varint,2,opt,name=micros,proto3" json:"micros,omitempty"`
}

func (x *Timestamp) Reset() {
	*x = Timestamp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Timestamp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Timestamp) ProtoMessage() {}

func (x *Timestamp) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Timestamp.ProtoReflect.Descriptor instead.
func (*Timestamp) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{0}
}

func (x *Timestamp) GetSeconds() uint32 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *Timestamp) GetMicros() uint32 {
	if x != nil {
		return x.Micros
	}
	return 0
}

type Set_Epoch_Time_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp *Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *Set_Epoch_Time_Request) Reset() {
	*x = Set_Epoch_Time_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Epoch_Time_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Epoch_Time_Request) ProtoMessage() {}

func (x *Set_Epoch_Time_Request) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Epoch_Time_Request.ProtoReflect.Descriptor instead.
func (*Set_Epoch_Time_Request) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{1}
}

func (x *Set_Epoch_Time_Request) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type Get_Timestamp_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Timestamp_Request) Reset() {
	*x = Get_Timestamp_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Timestamp_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Timestamp_Request) ProtoMessage() {}

func (x *Get_Timestamp_Request) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Timestamp_Request.ProtoReflect.Descriptor instead.
func (*Get_Timestamp_Request) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{2}
}

type Get_Debug_Timestamp_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Debug_Timestamp_Request) Reset() {
	*x = Get_Debug_Timestamp_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Debug_Timestamp_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Debug_Timestamp_Request) ProtoMessage() {}

func (x *Get_Debug_Timestamp_Request) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Debug_Timestamp_Request.ProtoReflect.Descriptor instead.
func (*Get_Debug_Timestamp_Request) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{3}
}

type Get_Debug_Timestamp_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp   *Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	PpsTimerVal uint32     `protobuf:"varint,2,opt,name=pps_timer_val,json=ppsTimerVal,proto3" json:"pps_timer_val,omitempty"`
	PpsTicks    uint32     `protobuf:"varint,3,opt,name=pps_ticks,json=ppsTicks,proto3" json:"pps_ticks,omitempty"`
	FreqMul     float64    `protobuf:"fixed64,4,opt,name=freq_mul,json=freqMul,proto3" json:"freq_mul,omitempty"`
	ErrorTicks  int32      `protobuf:"varint,5,opt,name=error_ticks,json=errorTicks,proto3" json:"error_ticks,omitempty"`
	ErrorTicks2 int32      `protobuf:"varint,6,opt,name=error_ticks2,json=errorTicks2,proto3" json:"error_ticks2,omitempty"`
}

func (x *Get_Debug_Timestamp_Reply) Reset() {
	*x = Get_Debug_Timestamp_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Debug_Timestamp_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Debug_Timestamp_Reply) ProtoMessage() {}

func (x *Get_Debug_Timestamp_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Debug_Timestamp_Reply.ProtoReflect.Descriptor instead.
func (*Get_Debug_Timestamp_Reply) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{4}
}

func (x *Get_Debug_Timestamp_Reply) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Get_Debug_Timestamp_Reply) GetPpsTimerVal() uint32 {
	if x != nil {
		return x.PpsTimerVal
	}
	return 0
}

func (x *Get_Debug_Timestamp_Reply) GetPpsTicks() uint32 {
	if x != nil {
		return x.PpsTicks
	}
	return 0
}

func (x *Get_Debug_Timestamp_Reply) GetFreqMul() float64 {
	if x != nil {
		return x.FreqMul
	}
	return 0
}

func (x *Get_Debug_Timestamp_Reply) GetErrorTicks() int32 {
	if x != nil {
		return x.ErrorTicks
	}
	return 0
}

func (x *Get_Debug_Timestamp_Reply) GetErrorTicks2() int32 {
	if x != nil {
		return x.ErrorTicks2
	}
	return 0
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Set
	//	*Request_Get
	//	*Request_Debug
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{5}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSet() *Set_Epoch_Time_Request {
	if x, ok := x.GetRequest().(*Request_Set); ok {
		return x.Set
	}
	return nil
}

func (x *Request) GetGet() *Get_Timestamp_Request {
	if x, ok := x.GetRequest().(*Request_Get); ok {
		return x.Get
	}
	return nil
}

func (x *Request) GetDebug() *Get_Debug_Timestamp_Request {
	if x, ok := x.GetRequest().(*Request_Debug); ok {
		return x.Debug
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Set struct {
	Set *Set_Epoch_Time_Request `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type Request_Get struct {
	Get *Get_Timestamp_Request `protobuf:"bytes,2,opt,name=get,proto3,oneof"`
}

type Request_Debug struct {
	Debug *Get_Debug_Timestamp_Request `protobuf:"bytes,3,opt,name=debug,proto3,oneof"`
}

func (*Request_Set) isRequest_Request() {}

func (*Request_Get) isRequest_Request() {}

func (*Request_Debug) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Timestamp
	//	*Reply_Debug
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_time_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_time_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_time_proto_rawDescGZIP(), []int{6}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetTimestamp() *Timestamp {
	if x, ok := x.GetReply().(*Reply_Timestamp); ok {
		return x.Timestamp
	}
	return nil
}

func (x *Reply) GetDebug() *Get_Debug_Timestamp_Reply {
	if x, ok := x.GetReply().(*Reply_Debug); ok {
		return x.Debug
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Timestamp struct {
	Timestamp *Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3,oneof"`
}

type Reply_Debug struct {
	Debug *Get_Debug_Timestamp_Reply `protobuf:"bytes,4,opt,name=debug,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Timestamp) isReply_Reply() {}

func (*Reply_Debug) isReply_Reply() {}

var File_time_proto protoreflect.FileDescriptor

var file_time_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x3d, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x22,
	0x47, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x5f, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x5f, 0x54, 0x69, 0x6d,
	0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x5f,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x1d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0xea, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x22, 0x0a,
	0x0d, 0x70, 0x70, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x70, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x56, 0x61,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x70, 0x73, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x70, 0x73, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x66, 0x72, 0x65, 0x71, 0x5f, 0x6d, 0x75, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x66, 0x72, 0x65, 0x71, 0x4d, 0x75, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x32, 0x22, 0xb2, 0x01,
	0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x65,
	0x74, 0x5f, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x73, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x03, 0x67,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65, 0x74, 0x12, 0x39, 0x0a, 0x05,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0xbe, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b,
	0x12, 0x2f, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x37, 0x0a, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x62, 0x75,
	0x67, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x42, 0x0d, 0x5a, 0x0b, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_time_proto_rawDescOnce sync.Once
	file_time_proto_rawDescData = file_time_proto_rawDesc
)

func file_time_proto_rawDescGZIP() []byte {
	file_time_proto_rawDescOnce.Do(func() {
		file_time_proto_rawDescData = protoimpl.X.CompressGZIP(file_time_proto_rawDescData)
	})
	return file_time_proto_rawDescData
}

var file_time_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_time_proto_goTypes = []interface{}{
	(*Timestamp)(nil),                   // 0: time.Timestamp
	(*Set_Epoch_Time_Request)(nil),      // 1: time.Set_Epoch_Time_Request
	(*Get_Timestamp_Request)(nil),       // 2: time.Get_Timestamp_Request
	(*Get_Debug_Timestamp_Request)(nil), // 3: time.Get_Debug_Timestamp_Request
	(*Get_Debug_Timestamp_Reply)(nil),   // 4: time.Get_Debug_Timestamp_Reply
	(*Request)(nil),                     // 5: time.Request
	(*Reply)(nil),                       // 6: time.Reply
	(*error1.Error)(nil),                // 7: error.Error
	(*ack.Ack)(nil),                     // 8: ack.Ack
}
var file_time_proto_depIdxs = []int32{
	0, // 0: time.Set_Epoch_Time_Request.timestamp:type_name -> time.Timestamp
	0, // 1: time.Get_Debug_Timestamp_Reply.timestamp:type_name -> time.Timestamp
	1, // 2: time.Request.set:type_name -> time.Set_Epoch_Time_Request
	2, // 3: time.Request.get:type_name -> time.Get_Timestamp_Request
	3, // 4: time.Request.debug:type_name -> time.Get_Debug_Timestamp_Request
	7, // 5: time.Reply.error:type_name -> error.Error
	8, // 6: time.Reply.ack:type_name -> ack.Ack
	0, // 7: time.Reply.timestamp:type_name -> time.Timestamp
	4, // 8: time.Reply.debug:type_name -> time.Get_Debug_Timestamp_Reply
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_time_proto_init() }
func file_time_proto_init() {
	if File_time_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_time_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Timestamp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_time_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Epoch_Time_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_time_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Timestamp_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_time_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Debug_Timestamp_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_time_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Debug_Timestamp_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_time_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_time_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_time_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Request_Set)(nil),
		(*Request_Get)(nil),
		(*Request_Debug)(nil),
	}
	file_time_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Timestamp)(nil),
		(*Reply_Debug)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_time_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_time_proto_goTypes,
		DependencyIndexes: file_time_proto_depIdxs,
		MessageInfos:      file_time_proto_msgTypes,
	}.Build()
	File_time_proto = out.File
	file_time_proto_rawDesc = nil
	file_time_proto_goTypes = nil
	file_time_proto_depIdxs = nil
}

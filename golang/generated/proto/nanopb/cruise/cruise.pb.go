// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: cruise.proto

package cruise

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TractorVariantType int32

const (
	TractorVariantType_TV_UNKNOWN TractorVariantType = 0
	TractorVariantType_TV_JD_6LH  TractorVariantType = 1
	TractorVariantType_TV_JD_6LHM TractorVariantType = 2
	TractorVariantType_TV_JD_6PRO TractorVariantType = 3
	TractorVariantType_TV_JD_7RH  TractorVariantType = 4
	TractorVariantType_TV_JD_8PRO TractorVariantType = 5
)

// Enum value maps for TractorVariantType.
var (
	TractorVariantType_name = map[int32]string{
		0: "TV_UNKNOWN",
		1: "TV_JD_6LH",
		2: "TV_JD_6LHM",
		3: "TV_JD_6PRO",
		4: "TV_JD_7RH",
		5: "TV_JD_8PRO",
	}
	TractorVariantType_value = map[string]int32{
		"TV_UNKNOWN": 0,
		"TV_JD_6LH":  1,
		"TV_JD_6LHM": 2,
		"TV_JD_6PRO": 3,
		"TV_JD_7RH":  4,
		"TV_JD_8PRO": 5,
	}
)

func (x TractorVariantType) Enum() *TractorVariantType {
	p := new(TractorVariantType)
	*p = x
	return p
}

func (x TractorVariantType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TractorVariantType) Descriptor() protoreflect.EnumDescriptor {
	return file_cruise_proto_enumTypes[0].Descriptor()
}

func (TractorVariantType) Type() protoreflect.EnumType {
	return &file_cruise_proto_enumTypes[0]
}

func (x TractorVariantType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TractorVariantType.Descriptor instead.
func (TractorVariantType) EnumDescriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{0}
}

type Throttle_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Change int32 `protobuf:"varint,1,opt,name=change,proto3" json:"change,omitempty"`
}

func (x *Throttle_Request) Reset() {
	*x = Throttle_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Throttle_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Throttle_Request) ProtoMessage() {}

func (x *Throttle_Request) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Throttle_Request.ProtoReflect.Descriptor instead.
func (*Throttle_Request) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{0}
}

func (x *Throttle_Request) GetChange() int32 {
	if x != nil {
		return x.Change
	}
	return 0
}

type Throttle_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *Throttle_Reply) Reset() {
	*x = Throttle_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Throttle_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Throttle_Reply) ProtoMessage() {}

func (x *Throttle_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Throttle_Reply.ProtoReflect.Descriptor instead.
func (*Throttle_Reply) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{1}
}

func (x *Throttle_Reply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type Status_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Status_Request) Reset() {
	*x = Status_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status_Request) ProtoMessage() {}

func (x *Status_Request) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status_Request.ProtoReflect.Descriptor instead.
func (*Status_Request) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{2}
}

type Status_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled            bool  `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Unavailable        bool  `protobuf:"varint,2,opt,name=unavailable,proto3" json:"unavailable,omitempty"`
	SpeedTicks         int32 `protobuf:"varint,3,opt,name=speed_ticks,json=speedTicks,proto3" json:"speed_ticks,omitempty"`
	SpeedLeverPosition int32 `protobuf:"varint,4,opt,name=speed_lever_position,json=speedLeverPosition,proto3" json:"speed_lever_position,omitempty"`
	SpeedLeverS1       bool  `protobuf:"varint,5,opt,name=speed_lever_s1,json=speedLeverS1,proto3" json:"speed_lever_s1,omitempty"`
	SpeedLeverS2       bool  `protobuf:"varint,6,opt,name=speed_lever_s2,json=speedLeverS2,proto3" json:"speed_lever_s2,omitempty"`
}

func (x *Status_Reply) Reset() {
	*x = Status_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status_Reply) ProtoMessage() {}

func (x *Status_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status_Reply.ProtoReflect.Descriptor instead.
func (*Status_Reply) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{3}
}

func (x *Status_Reply) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Status_Reply) GetUnavailable() bool {
	if x != nil {
		return x.Unavailable
	}
	return false
}

func (x *Status_Reply) GetSpeedTicks() int32 {
	if x != nil {
		return x.SpeedTicks
	}
	return 0
}

func (x *Status_Reply) GetSpeedLeverPosition() int32 {
	if x != nil {
		return x.SpeedLeverPosition
	}
	return 0
}

func (x *Status_Reply) GetSpeedLeverS1() bool {
	if x != nil {
		return x.SpeedLeverS1
	}
	return false
}

func (x *Status_Reply) GetSpeedLeverS2() bool {
	if x != nil {
		return x.SpeedLeverS2
	}
	return false
}

type Enable_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *Enable_Request) Reset() {
	*x = Enable_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enable_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enable_Request) ProtoMessage() {}

func (x *Enable_Request) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enable_Request.ProtoReflect.Descriptor instead.
func (*Enable_Request) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{4}
}

func (x *Enable_Request) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type Enable_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *Enable_Reply) Reset() {
	*x = Enable_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Enable_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Enable_Reply) ProtoMessage() {}

func (x *Enable_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Enable_Reply.ProtoReflect.Descriptor instead.
func (*Enable_Reply) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{5}
}

func (x *Enable_Reply) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type Gear_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gear int32 `protobuf:"varint,1,opt,name=gear,proto3" json:"gear,omitempty"`
}

func (x *Gear_Request) Reset() {
	*x = Gear_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gear_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gear_Request) ProtoMessage() {}

func (x *Gear_Request) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gear_Request.ProtoReflect.Descriptor instead.
func (*Gear_Request) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{6}
}

func (x *Gear_Request) GetGear() int32 {
	if x != nil {
		return x.Gear
	}
	return 0
}

type Gear_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *Gear_Reply) Reset() {
	*x = Gear_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gear_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gear_Reply) ProtoMessage() {}

func (x *Gear_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gear_Reply.ProtoReflect.Descriptor instead.
func (*Gear_Reply) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{7}
}

func (x *Gear_Reply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type Variant_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Variant TractorVariantType `protobuf:"varint,1,opt,name=variant,proto3,enum=cruise.TractorVariantType" json:"variant,omitempty"`
}

func (x *Variant_Request) Reset() {
	*x = Variant_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Variant_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Variant_Request) ProtoMessage() {}

func (x *Variant_Request) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Variant_Request.ProtoReflect.Descriptor instead.
func (*Variant_Request) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{8}
}

func (x *Variant_Request) GetVariant() TractorVariantType {
	if x != nil {
		return x.Variant
	}
	return TractorVariantType_TV_UNKNOWN
}

type Variant_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Variant TractorVariantType `protobuf:"varint,1,opt,name=variant,proto3,enum=cruise.TractorVariantType" json:"variant,omitempty"`
}

func (x *Variant_Reply) Reset() {
	*x = Variant_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Variant_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Variant_Reply) ProtoMessage() {}

func (x *Variant_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Variant_Reply.ProtoReflect.Descriptor instead.
func (*Variant_Reply) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{9}
}

func (x *Variant_Reply) GetVariant() TractorVariantType {
	if x != nil {
		return x.Variant
	}
	return TractorVariantType_TV_UNKNOWN
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_Throttle
	//	*Request_Status
	//	*Request_Enable
	//	*Request_Gear
	//	*Request_Variant
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{10}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetThrottle() *Throttle_Request {
	if x, ok := x.GetRequest().(*Request_Throttle); ok {
		return x.Throttle
	}
	return nil
}

func (x *Request) GetStatus() *Status_Request {
	if x, ok := x.GetRequest().(*Request_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Request) GetEnable() *Enable_Request {
	if x, ok := x.GetRequest().(*Request_Enable); ok {
		return x.Enable
	}
	return nil
}

func (x *Request) GetGear() *Gear_Request {
	if x, ok := x.GetRequest().(*Request_Gear); ok {
		return x.Gear
	}
	return nil
}

func (x *Request) GetVariant() *Variant_Request {
	if x, ok := x.GetRequest().(*Request_Variant); ok {
		return x.Variant
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_Throttle struct {
	Throttle *Throttle_Request `protobuf:"bytes,3,opt,name=throttle,proto3,oneof"`
}

type Request_Status struct {
	Status *Status_Request `protobuf:"bytes,4,opt,name=status,proto3,oneof"`
}

type Request_Enable struct {
	Enable *Enable_Request `protobuf:"bytes,5,opt,name=enable,proto3,oneof"`
}

type Request_Gear struct {
	Gear *Gear_Request `protobuf:"bytes,6,opt,name=gear,proto3,oneof"`
}

type Request_Variant struct {
	Variant *Variant_Request `protobuf:"bytes,7,opt,name=variant,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_Throttle) isRequest_Request() {}

func (*Request_Status) isRequest_Request() {}

func (*Request_Enable) isRequest_Request() {}

func (*Request_Gear) isRequest_Request() {}

func (*Request_Variant) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_Throttle
	//	*Reply_Status
	//	*Reply_Enable
	//	*Reply_Gear
	//	*Reply_Variant
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cruise_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_cruise_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_cruise_proto_rawDescGZIP(), []int{11}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetThrottle() *Throttle_Reply {
	if x, ok := x.GetReply().(*Reply_Throttle); ok {
		return x.Throttle
	}
	return nil
}

func (x *Reply) GetStatus() *Status_Reply {
	if x, ok := x.GetReply().(*Reply_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Reply) GetEnable() *Enable_Reply {
	if x, ok := x.GetReply().(*Reply_Enable); ok {
		return x.Enable
	}
	return nil
}

func (x *Reply) GetGear() *Gear_Reply {
	if x, ok := x.GetReply().(*Reply_Gear); ok {
		return x.Gear
	}
	return nil
}

func (x *Reply) GetVariant() *Variant_Reply {
	if x, ok := x.GetReply().(*Reply_Variant); ok {
		return x.Variant
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_Throttle struct {
	Throttle *Throttle_Reply `protobuf:"bytes,3,opt,name=throttle,proto3,oneof"`
}

type Reply_Status struct {
	Status *Status_Reply `protobuf:"bytes,4,opt,name=status,proto3,oneof"`
}

type Reply_Enable struct {
	Enable *Enable_Reply `protobuf:"bytes,5,opt,name=enable,proto3,oneof"`
}

type Reply_Gear struct {
	Gear *Gear_Reply `protobuf:"bytes,6,opt,name=gear,proto3,oneof"`
}

type Reply_Variant struct {
	Variant *Variant_Reply `protobuf:"bytes,7,opt,name=variant,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_Throttle) isReply_Reply() {}

func (*Reply_Status) isReply_Reply() {}

func (*Reply_Enable) isReply_Reply() {}

func (*Reply_Gear) isReply_Reply() {}

func (*Reply_Variant) isReply_Reply() {}

var File_cruise_proto protoreflect.FileDescriptor

var file_cruise_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2a, 0x0a, 0x10, 0x54,
	0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x2a, 0x0a, 0x0e, 0x54, 0x68, 0x72, 0x6f, 0x74,
	0x74, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xe9, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x54, 0x69,
	0x63, 0x6b, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x12, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x72, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c,
	0x65, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x72, 0x53, 0x31, 0x12, 0x24, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x32, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x72, 0x53,
	0x32, 0x22, 0x2a, 0x0a, 0x0e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x28, 0x0a,
	0x0c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x22, 0x0a, 0x0c, 0x47, 0x65, 0x61, 0x72, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x22, 0x26, 0x0a, 0x0a, 0x47,
	0x65, 0x61, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x47, 0x0a, 0x0f, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x0d,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a,
	0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x6e, 0x74, 0x22, 0xb9, 0x02, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48,
	0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x0a, 0x08, 0x74, 0x68, 0x72, 0x6f, 0x74,
	0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x72, 0x75, 0x69,
	0x73, 0x65, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x74, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x12,
	0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x12,
	0x33, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e,
	0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x72,
	0x69, 0x61, 0x6e, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0xab, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x6f, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x6e,
	0x67, 0x12, 0x34, 0x0a, 0x08, 0x74, 0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x54, 0x68, 0x72,
	0x6f, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08, 0x74,
	0x68, 0x72, 0x6f, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65,
	0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x47,
	0x65, 0x61, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x67, 0x65, 0x61,
	0x72, 0x12, 0x31, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x6e, 0x74, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x72,
	0x69, 0x61, 0x6e, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x72, 0x0a,
	0x12, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x56, 0x5f, 0x4a, 0x44, 0x5f, 0x36, 0x4c, 0x48,
	0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x4a, 0x44, 0x5f, 0x36, 0x4c, 0x48, 0x4d,
	0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x4a, 0x44, 0x5f, 0x36, 0x50, 0x52, 0x4f,
	0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x56, 0x5f, 0x4a, 0x44, 0x5f, 0x37, 0x52, 0x48, 0x10,
	0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x4a, 0x44, 0x5f, 0x38, 0x50, 0x52, 0x4f, 0x10,
	0x05, 0x42, 0x0f, 0x5a, 0x0d, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x63, 0x72, 0x75, 0x69,
	0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cruise_proto_rawDescOnce sync.Once
	file_cruise_proto_rawDescData = file_cruise_proto_rawDesc
)

func file_cruise_proto_rawDescGZIP() []byte {
	file_cruise_proto_rawDescOnce.Do(func() {
		file_cruise_proto_rawDescData = protoimpl.X.CompressGZIP(file_cruise_proto_rawDescData)
	})
	return file_cruise_proto_rawDescData
}

var file_cruise_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cruise_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_cruise_proto_goTypes = []interface{}{
	(TractorVariantType)(0),  // 0: cruise.TractorVariantType
	(*Throttle_Request)(nil), // 1: cruise.Throttle_Request
	(*Throttle_Reply)(nil),   // 2: cruise.Throttle_Reply
	(*Status_Request)(nil),   // 3: cruise.Status_Request
	(*Status_Reply)(nil),     // 4: cruise.Status_Reply
	(*Enable_Request)(nil),   // 5: cruise.Enable_Request
	(*Enable_Reply)(nil),     // 6: cruise.Enable_Reply
	(*Gear_Request)(nil),     // 7: cruise.Gear_Request
	(*Gear_Reply)(nil),       // 8: cruise.Gear_Reply
	(*Variant_Request)(nil),  // 9: cruise.Variant_Request
	(*Variant_Reply)(nil),    // 10: cruise.Variant_Reply
	(*Request)(nil),          // 11: cruise.Request
	(*Reply)(nil),            // 12: cruise.Reply
	(*diagnostic.Ping)(nil),  // 13: diagnostic.Ping
	(*diagnostic.Pong)(nil),  // 14: diagnostic.Pong
}
var file_cruise_proto_depIdxs = []int32{
	0,  // 0: cruise.Variant_Request.variant:type_name -> cruise.TractorVariantType
	0,  // 1: cruise.Variant_Reply.variant:type_name -> cruise.TractorVariantType
	13, // 2: cruise.Request.ping:type_name -> diagnostic.Ping
	1,  // 3: cruise.Request.throttle:type_name -> cruise.Throttle_Request
	3,  // 4: cruise.Request.status:type_name -> cruise.Status_Request
	5,  // 5: cruise.Request.enable:type_name -> cruise.Enable_Request
	7,  // 6: cruise.Request.gear:type_name -> cruise.Gear_Request
	9,  // 7: cruise.Request.variant:type_name -> cruise.Variant_Request
	14, // 8: cruise.Reply.pong:type_name -> diagnostic.Pong
	2,  // 9: cruise.Reply.throttle:type_name -> cruise.Throttle_Reply
	4,  // 10: cruise.Reply.status:type_name -> cruise.Status_Reply
	6,  // 11: cruise.Reply.enable:type_name -> cruise.Enable_Reply
	8,  // 12: cruise.Reply.gear:type_name -> cruise.Gear_Reply
	10, // 13: cruise.Reply.variant:type_name -> cruise.Variant_Reply
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_cruise_proto_init() }
func file_cruise_proto_init() {
	if File_cruise_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cruise_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Throttle_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Throttle_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enable_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Enable_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gear_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gear_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Variant_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Variant_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cruise_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_cruise_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_Throttle)(nil),
		(*Request_Status)(nil),
		(*Request_Enable)(nil),
		(*Request_Gear)(nil),
		(*Request_Variant)(nil),
	}
	file_cruise_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_Throttle)(nil),
		(*Reply_Status)(nil),
		(*Reply_Enable)(nil),
		(*Reply_Gear)(nil),
		(*Reply_Variant)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cruise_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cruise_proto_goTypes,
		DependencyIndexes: file_cruise_proto_depIdxs,
		EnumInfos:         file_cruise_proto_enumTypes,
		MessageInfos:      file_cruise_proto_msgTypes,
	}.Build()
	File_cruise_proto = out.File
	file_cruise_proto_rawDesc = nil
	file_cruise_proto_goTypes = nil
	file_cruise_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: row_module_board.proto

package row_module_board

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	row_module "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/row_module"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_RowModule
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_board_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_board_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_row_module_board_proto_rawDescGZIP(), []int{0}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetRowModule() *row_module.Reply {
	if x, ok := x.GetReply().(*Reply_RowModule); ok {
		return x.RowModule
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_RowModule struct {
	RowModule *row_module.Reply `protobuf:"bytes,3,opt,name=row_module,json=rowModule,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_RowModule) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_RowModule
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_row_module_board_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_row_module_board_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_row_module_board_proto_rawDescGZIP(), []int{1}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetRowModule() *row_module.Request {
	if x, ok := x.GetRequest().(*Request_RowModule); ok {
		return x.RowModule
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_RowModule struct {
	RowModule *row_module.Request `protobuf:"bytes,3,opt,name=row_module,json=rowModule,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_RowModule) isRequest_Request() {}

var File_row_module_board_proto protoreflect.FileDescriptor

var file_row_module_board_proto_rawDesc = []byte{
	0x0a, 0x16, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x26, 0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67,
	0x48, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x0a, 0x72, 0x6f, 0x77, 0x5f,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72,
	0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x07, 0x0a, 0x05,
	0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xa2, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x69, 0x6e,
	0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x0a, 0x72, 0x6f, 0x77,
	0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x42,
	0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x19, 0x5a, 0x17, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x72, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_row_module_board_proto_rawDescOnce sync.Once
	file_row_module_board_proto_rawDescData = file_row_module_board_proto_rawDesc
)

func file_row_module_board_proto_rawDescGZIP() []byte {
	file_row_module_board_proto_rawDescOnce.Do(func() {
		file_row_module_board_proto_rawDescData = protoimpl.X.CompressGZIP(file_row_module_board_proto_rawDescData)
	})
	return file_row_module_board_proto_rawDescData
}

var file_row_module_board_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_row_module_board_proto_goTypes = []interface{}{
	(*Reply)(nil),                 // 0: row_module_board.Reply
	(*Request)(nil),               // 1: row_module_board.Request
	(*request.RequestHeader)(nil), // 2: request.RequestHeader
	(*diagnostic.Pong)(nil),       // 3: diagnostic.Pong
	(*row_module.Reply)(nil),      // 4: row_module.Reply
	(*diagnostic.Ping)(nil),       // 5: diagnostic.Ping
	(*row_module.Request)(nil),    // 6: row_module.Request
}
var file_row_module_board_proto_depIdxs = []int32{
	2, // 0: row_module_board.Reply.header:type_name -> request.RequestHeader
	3, // 1: row_module_board.Reply.pong:type_name -> diagnostic.Pong
	4, // 2: row_module_board.Reply.row_module:type_name -> row_module.Reply
	2, // 3: row_module_board.Request.header:type_name -> request.RequestHeader
	5, // 4: row_module_board.Request.ping:type_name -> diagnostic.Ping
	6, // 5: row_module_board.Request.row_module:type_name -> row_module.Request
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_row_module_board_proto_init() }
func file_row_module_board_proto_init() {
	if File_row_module_board_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_row_module_board_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_row_module_board_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_row_module_board_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_RowModule)(nil),
	}
	file_row_module_board_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_RowModule)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_row_module_board_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_row_module_board_proto_goTypes,
		DependencyIndexes: file_row_module_board_proto_depIdxs,
		MessageInfos:      file_row_module_board_proto_msgTypes,
	}.Build()
	File_row_module_board_proto = out.File
	file_row_module_board_proto_rawDesc = nil
	file_row_module_board_proto_goTypes = nil
	file_row_module_board_proto_depIdxs = nil
}

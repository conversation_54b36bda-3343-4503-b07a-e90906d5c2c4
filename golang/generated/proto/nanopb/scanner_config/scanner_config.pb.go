// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: scanner_config.proto

package scanner_config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Delta_Target_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PanSkew  float32 `protobuf:"fixed32,1,opt,name=pan_skew,json=panSkew,proto3" json:"pan_skew,omitempty"`
	TiltSkew float32 `protobuf:"fixed32,2,opt,name=tilt_skew,json=tiltSkew,proto3" json:"tilt_skew,omitempty"`
}

func (x *Delta_Target_Config) Reset() {
	*x = Delta_Target_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Delta_Target_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Delta_Target_Config) ProtoMessage() {}

func (x *Delta_Target_Config) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Delta_Target_Config.ProtoReflect.Descriptor instead.
func (*Delta_Target_Config) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{0}
}

func (x *Delta_Target_Config) GetPanSkew() float32 {
	if x != nil {
		return x.PanSkew
	}
	return 0
}

func (x *Delta_Target_Config) GetTiltSkew() float32 {
	if x != nil {
		return x.TiltSkew
	}
	return 0
}

type Color_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Red   float32 `protobuf:"fixed32,1,opt,name=red,proto3" json:"red,omitempty"`
	Green float32 `protobuf:"fixed32,2,opt,name=green,proto3" json:"green,omitempty"`
	Blue  float32 `protobuf:"fixed32,3,opt,name=blue,proto3" json:"blue,omitempty"`
}

func (x *Color_Config) Reset() {
	*x = Color_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Color_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Color_Config) ProtoMessage() {}

func (x *Color_Config) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Color_Config.ProtoReflect.Descriptor instead.
func (*Color_Config) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{1}
}

func (x *Color_Config) GetRed() float32 {
	if x != nil {
		return x.Red
	}
	return 0
}

func (x *Color_Config) GetGreen() float32 {
	if x != nil {
		return x.Green
	}
	return 0
}

func (x *Color_Config) GetBlue() float32 {
	if x != nil {
		return x.Blue
	}
	return 0
}

type Camera_Serial_Number_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNumber []byte `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
}

func (x *Camera_Serial_Number_Config) Reset() {
	*x = Camera_Serial_Number_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Camera_Serial_Number_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Camera_Serial_Number_Config) ProtoMessage() {}

func (x *Camera_Serial_Number_Config) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Camera_Serial_Number_Config.ProtoReflect.Descriptor instead.
func (*Camera_Serial_Number_Config) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{2}
}

func (x *Camera_Serial_Number_Config) GetSerialNumber() []byte {
	if x != nil {
		return x.SerialNumber
	}
	return nil
}

type Scanner_Barcode_Str_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Barcode []byte `protobuf:"bytes,1,opt,name=barcode,proto3" json:"barcode,omitempty"`
}

func (x *Scanner_Barcode_Str_Config) Reset() {
	*x = Scanner_Barcode_Str_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scanner_Barcode_Str_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scanner_Barcode_Str_Config) ProtoMessage() {}

func (x *Scanner_Barcode_Str_Config) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scanner_Barcode_Str_Config.ProtoReflect.Descriptor instead.
func (*Scanner_Barcode_Str_Config) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{3}
}

func (x *Scanner_Barcode_Str_Config) GetBarcode() []byte {
	if x != nil {
		return x.Barcode
	}
	return nil
}

type Set_Delta_Target_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Delta_Target_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Set_Delta_Target_Config_Request) Reset() {
	*x = Set_Delta_Target_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Delta_Target_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Delta_Target_Config_Request) ProtoMessage() {}

func (x *Set_Delta_Target_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Delta_Target_Config_Request.ProtoReflect.Descriptor instead.
func (*Set_Delta_Target_Config_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{4}
}

func (x *Set_Delta_Target_Config_Request) GetConfig() *Delta_Target_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_Delta_Target_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Delta_Target_Config_Request) Reset() {
	*x = Get_Delta_Target_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Delta_Target_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Delta_Target_Config_Request) ProtoMessage() {}

func (x *Get_Delta_Target_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Delta_Target_Config_Request.ProtoReflect.Descriptor instead.
func (*Get_Delta_Target_Config_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{5}
}

type Set_Color_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Color_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Set_Color_Config_Request) Reset() {
	*x = Set_Color_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Color_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Color_Config_Request) ProtoMessage() {}

func (x *Set_Color_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Color_Config_Request.ProtoReflect.Descriptor instead.
func (*Set_Color_Config_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{6}
}

func (x *Set_Color_Config_Request) GetConfig() *Color_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_Color_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Color_Config_Request) Reset() {
	*x = Get_Color_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Color_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Color_Config_Request) ProtoMessage() {}

func (x *Get_Color_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Color_Config_Request.ProtoReflect.Descriptor instead.
func (*Get_Color_Config_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{7}
}

type Set_Camera_Serial_Number_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Camera_Serial_Number_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Set_Camera_Serial_Number_Config_Request) Reset() {
	*x = Set_Camera_Serial_Number_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Camera_Serial_Number_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Camera_Serial_Number_Config_Request) ProtoMessage() {}

func (x *Set_Camera_Serial_Number_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Camera_Serial_Number_Config_Request.ProtoReflect.Descriptor instead.
func (*Set_Camera_Serial_Number_Config_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{8}
}

func (x *Set_Camera_Serial_Number_Config_Request) GetConfig() *Camera_Serial_Number_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_Camera_Serial_Number_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Camera_Serial_Number_Config_Request) Reset() {
	*x = Get_Camera_Serial_Number_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Camera_Serial_Number_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Camera_Serial_Number_Config_Request) ProtoMessage() {}

func (x *Get_Camera_Serial_Number_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Camera_Serial_Number_Config_Request.ProtoReflect.Descriptor instead.
func (*Get_Camera_Serial_Number_Config_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{9}
}

type Set_HW_Revision_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Revision uint32 `protobuf:"varint,1,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *Set_HW_Revision_Request) Reset() {
	*x = Set_HW_Revision_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_HW_Revision_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_HW_Revision_Request) ProtoMessage() {}

func (x *Set_HW_Revision_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_HW_Revision_Request.ProtoReflect.Descriptor instead.
func (*Set_HW_Revision_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{10}
}

func (x *Set_HW_Revision_Request) GetRevision() uint32 {
	if x != nil {
		return x.Revision
	}
	return 0
}

type Get_HW_Revision_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_HW_Revision_Request) Reset() {
	*x = Get_HW_Revision_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_HW_Revision_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_HW_Revision_Request) ProtoMessage() {}

func (x *Get_HW_Revision_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_HW_Revision_Request.ProtoReflect.Descriptor instead.
func (*Get_HW_Revision_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{11}
}

type Set_Scanner_Barcode_Str_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Scanner_Barcode_Str_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Set_Scanner_Barcode_Str_Request) Reset() {
	*x = Set_Scanner_Barcode_Str_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Scanner_Barcode_Str_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Scanner_Barcode_Str_Request) ProtoMessage() {}

func (x *Set_Scanner_Barcode_Str_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Scanner_Barcode_Str_Request.ProtoReflect.Descriptor instead.
func (*Set_Scanner_Barcode_Str_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{12}
}

func (x *Set_Scanner_Barcode_Str_Request) GetConfig() *Scanner_Barcode_Str_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_Scanner_Barcode_Str_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Scanner_Barcode_Str_Request) Reset() {
	*x = Get_Scanner_Barcode_Str_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Scanner_Barcode_Str_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Scanner_Barcode_Str_Request) ProtoMessage() {}

func (x *Get_Scanner_Barcode_Str_Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Scanner_Barcode_Str_Request.ProtoReflect.Descriptor instead.
func (*Get_Scanner_Barcode_Str_Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{13}
}

type Get_Delta_Target_Config_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Delta_Target_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Get_Delta_Target_Config_Reply) Reset() {
	*x = Get_Delta_Target_Config_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Delta_Target_Config_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Delta_Target_Config_Reply) ProtoMessage() {}

func (x *Get_Delta_Target_Config_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Delta_Target_Config_Reply.ProtoReflect.Descriptor instead.
func (*Get_Delta_Target_Config_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{14}
}

func (x *Get_Delta_Target_Config_Reply) GetConfig() *Delta_Target_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_Color_Config_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Color_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Get_Color_Config_Reply) Reset() {
	*x = Get_Color_Config_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Color_Config_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Color_Config_Reply) ProtoMessage() {}

func (x *Get_Color_Config_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Color_Config_Reply.ProtoReflect.Descriptor instead.
func (*Get_Color_Config_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{15}
}

func (x *Get_Color_Config_Reply) GetConfig() *Color_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_Camera_Serial_Number_Config_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Camera_Serial_Number_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Get_Camera_Serial_Number_Config_Reply) Reset() {
	*x = Get_Camera_Serial_Number_Config_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Camera_Serial_Number_Config_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Camera_Serial_Number_Config_Reply) ProtoMessage() {}

func (x *Get_Camera_Serial_Number_Config_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Camera_Serial_Number_Config_Reply.ProtoReflect.Descriptor instead.
func (*Get_Camera_Serial_Number_Config_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{16}
}

func (x *Get_Camera_Serial_Number_Config_Reply) GetConfig() *Camera_Serial_Number_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Get_HW_Revision_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Revision uint32 `protobuf:"varint,1,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (x *Get_HW_Revision_Reply) Reset() {
	*x = Get_HW_Revision_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_HW_Revision_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_HW_Revision_Reply) ProtoMessage() {}

func (x *Get_HW_Revision_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_HW_Revision_Reply.ProtoReflect.Descriptor instead.
func (*Get_HW_Revision_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{17}
}

func (x *Get_HW_Revision_Reply) GetRevision() uint32 {
	if x != nil {
		return x.Revision
	}
	return 0
}

type Get_Scanner_Barcode_Str_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Scanner_Barcode_Str_Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Get_Scanner_Barcode_Str_Reply) Reset() {
	*x = Get_Scanner_Barcode_Str_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Scanner_Barcode_Str_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Scanner_Barcode_Str_Reply) ProtoMessage() {}

func (x *Get_Scanner_Barcode_Str_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Scanner_Barcode_Str_Reply.ProtoReflect.Descriptor instead.
func (*Get_Scanner_Barcode_Str_Reply) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{18}
}

func (x *Get_Scanner_Barcode_Str_Reply) GetConfig() *Scanner_Barcode_Str_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_SetDt
	//	*Request_GetDt
	//	*Request_SetColor
	//	*Request_GetColor
	//	*Request_SetSn
	//	*Request_GetSn
	//	*Request_SetHw
	//	*Request_GetHw
	//	*Request_SetBc
	//	*Request_GetBc
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{19}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSetDt() *Set_Delta_Target_Config_Request {
	if x, ok := x.GetRequest().(*Request_SetDt); ok {
		return x.SetDt
	}
	return nil
}

func (x *Request) GetGetDt() *Get_Delta_Target_Config_Request {
	if x, ok := x.GetRequest().(*Request_GetDt); ok {
		return x.GetDt
	}
	return nil
}

func (x *Request) GetSetColor() *Set_Color_Config_Request {
	if x, ok := x.GetRequest().(*Request_SetColor); ok {
		return x.SetColor
	}
	return nil
}

func (x *Request) GetGetColor() *Get_Color_Config_Request {
	if x, ok := x.GetRequest().(*Request_GetColor); ok {
		return x.GetColor
	}
	return nil
}

func (x *Request) GetSetSn() *Set_Camera_Serial_Number_Config_Request {
	if x, ok := x.GetRequest().(*Request_SetSn); ok {
		return x.SetSn
	}
	return nil
}

func (x *Request) GetGetSn() *Get_Camera_Serial_Number_Config_Request {
	if x, ok := x.GetRequest().(*Request_GetSn); ok {
		return x.GetSn
	}
	return nil
}

func (x *Request) GetSetHw() *Set_HW_Revision_Request {
	if x, ok := x.GetRequest().(*Request_SetHw); ok {
		return x.SetHw
	}
	return nil
}

func (x *Request) GetGetHw() *Get_HW_Revision_Request {
	if x, ok := x.GetRequest().(*Request_GetHw); ok {
		return x.GetHw
	}
	return nil
}

func (x *Request) GetSetBc() *Set_Scanner_Barcode_Str_Request {
	if x, ok := x.GetRequest().(*Request_SetBc); ok {
		return x.SetBc
	}
	return nil
}

func (x *Request) GetGetBc() *Get_Scanner_Barcode_Str_Request {
	if x, ok := x.GetRequest().(*Request_GetBc); ok {
		return x.GetBc
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_SetDt struct {
	SetDt *Set_Delta_Target_Config_Request `protobuf:"bytes,1,opt,name=set_dt,json=setDt,proto3,oneof"`
}

type Request_GetDt struct {
	GetDt *Get_Delta_Target_Config_Request `protobuf:"bytes,2,opt,name=get_dt,json=getDt,proto3,oneof"`
}

type Request_SetColor struct {
	SetColor *Set_Color_Config_Request `protobuf:"bytes,3,opt,name=set_color,json=setColor,proto3,oneof"`
}

type Request_GetColor struct {
	GetColor *Get_Color_Config_Request `protobuf:"bytes,4,opt,name=get_color,json=getColor,proto3,oneof"`
}

type Request_SetSn struct {
	SetSn *Set_Camera_Serial_Number_Config_Request `protobuf:"bytes,5,opt,name=set_sn,json=setSn,proto3,oneof"`
}

type Request_GetSn struct {
	GetSn *Get_Camera_Serial_Number_Config_Request `protobuf:"bytes,6,opt,name=get_sn,json=getSn,proto3,oneof"`
}

type Request_SetHw struct {
	SetHw *Set_HW_Revision_Request `protobuf:"bytes,7,opt,name=set_hw,json=setHw,proto3,oneof"`
}

type Request_GetHw struct {
	GetHw *Get_HW_Revision_Request `protobuf:"bytes,8,opt,name=get_hw,json=getHw,proto3,oneof"`
}

type Request_SetBc struct {
	SetBc *Set_Scanner_Barcode_Str_Request `protobuf:"bytes,9,opt,name=set_bc,json=setBc,proto3,oneof"`
}

type Request_GetBc struct {
	GetBc *Get_Scanner_Barcode_Str_Request `protobuf:"bytes,10,opt,name=get_bc,json=getBc,proto3,oneof"`
}

func (*Request_SetDt) isRequest_Request() {}

func (*Request_GetDt) isRequest_Request() {}

func (*Request_SetColor) isRequest_Request() {}

func (*Request_GetColor) isRequest_Request() {}

func (*Request_SetSn) isRequest_Request() {}

func (*Request_GetSn) isRequest_Request() {}

func (*Request_SetHw) isRequest_Request() {}

func (*Request_GetHw) isRequest_Request() {}

func (*Request_SetBc) isRequest_Request() {}

func (*Request_GetBc) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Dt
	//	*Reply_Color
	//	*Reply_Sn
	//	*Reply_Hw
	//	*Reply_Bc
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_scanner_config_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_scanner_config_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_scanner_config_proto_rawDescGZIP(), []int{20}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetDt() *Get_Delta_Target_Config_Reply {
	if x, ok := x.GetReply().(*Reply_Dt); ok {
		return x.Dt
	}
	return nil
}

func (x *Reply) GetColor() *Get_Color_Config_Reply {
	if x, ok := x.GetReply().(*Reply_Color); ok {
		return x.Color
	}
	return nil
}

func (x *Reply) GetSn() *Get_Camera_Serial_Number_Config_Reply {
	if x, ok := x.GetReply().(*Reply_Sn); ok {
		return x.Sn
	}
	return nil
}

func (x *Reply) GetHw() *Get_HW_Revision_Reply {
	if x, ok := x.GetReply().(*Reply_Hw); ok {
		return x.Hw
	}
	return nil
}

func (x *Reply) GetBc() *Get_Scanner_Barcode_Str_Reply {
	if x, ok := x.GetReply().(*Reply_Bc); ok {
		return x.Bc
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Dt struct {
	Dt *Get_Delta_Target_Config_Reply `protobuf:"bytes,3,opt,name=dt,proto3,oneof"`
}

type Reply_Color struct {
	Color *Get_Color_Config_Reply `protobuf:"bytes,4,opt,name=color,proto3,oneof"`
}

type Reply_Sn struct {
	Sn *Get_Camera_Serial_Number_Config_Reply `protobuf:"bytes,5,opt,name=sn,proto3,oneof"`
}

type Reply_Hw struct {
	Hw *Get_HW_Revision_Reply `protobuf:"bytes,6,opt,name=hw,proto3,oneof"`
}

type Reply_Bc struct {
	Bc *Get_Scanner_Barcode_Str_Reply `protobuf:"bytes,7,opt,name=bc,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Dt) isReply_Reply() {}

func (*Reply_Color) isReply_Reply() {}

func (*Reply_Sn) isReply_Reply() {}

func (*Reply_Hw) isReply_Reply() {}

func (*Reply_Bc) isReply_Reply() {}

var File_scanner_config_proto protoreflect.FileDescriptor

var file_scanner_config_proto_rawDesc = []byte{
	0x0a, 0x14, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61,
	0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4d, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x61, 0x6e, 0x5f, 0x73, 0x6b, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x70,
	0x61, 0x6e, 0x53, 0x6b, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x73,
	0x6b, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x74, 0x69, 0x6c, 0x74, 0x53,
	0x6b, 0x65, 0x77, 0x22, 0x4a, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x03, 0x72, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x62,
	0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x62, 0x6c, 0x75, 0x65, 0x22,
	0x42, 0x0a, 0x1b, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x36, 0x0a, 0x1a, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x42,
	0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x62, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x5e, 0x0a, 0x1f, 0x53,
	0x65, 0x74, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b,
	0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x21, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x50,
	0x0a, 0x18, 0x53, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x27,
	0x53, 0x65, 0x74, 0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x29, 0x0a, 0x27,
	0x47, 0x65, 0x74, 0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x35, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x5f, 0x48,
	0x57, 0x5f, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x19,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x5f, 0x48, 0x57, 0x5f, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x65, 0x0a, 0x1f, 0x53, 0x65, 0x74,
	0x5f, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x42, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x53, 0x74, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x42, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74,
	0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x21, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x5f, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x42, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x5c, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61,
	0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x3b, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x22, 0x4e, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x22, 0x6c, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x33, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x5f, 0x48, 0x57, 0x5f, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x5f, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x42, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x42,
	0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xf6, 0x05, 0x0a, 0x07, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x5f, 0x64, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61,
	0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x65, 0x74, 0x44, 0x74, 0x12,
	0x48, 0x0a, 0x06, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x05, 0x67, 0x65, 0x74, 0x44, 0x74, 0x12, 0x47, 0x0a, 0x09, 0x73, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65,
	0x74, 0x5f, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x47, 0x0a, 0x09, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x08, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x50, 0x0a, 0x06, 0x73,
	0x65, 0x74, 0x5f, 0x73, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x74,
	0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x65, 0x74, 0x53, 0x6e, 0x12, 0x50, 0x0a,
	0x06, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x67, 0x65, 0x74, 0x53, 0x6e, 0x12,
	0x40, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x5f, 0x68, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x53, 0x65, 0x74, 0x5f, 0x48, 0x57, 0x5f, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x65, 0x74, 0x48,
	0x77, 0x12, 0x40, 0x0a, 0x06, 0x67, 0x65, 0x74, 0x5f, 0x68, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x48, 0x57, 0x5f, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x67, 0x65,
	0x74, 0x48, 0x77, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x5f, 0x62, 0x63, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x42, 0x61, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x65, 0x74, 0x42, 0x63, 0x12, 0x48, 0x0a,
	0x06, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x5f, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x42, 0x61, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x05, 0x67, 0x65, 0x74, 0x42, 0x63, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x98, 0x03, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61, 0x63, 0x6b,
	0x12, 0x3f, 0x0a, 0x02, 0x64, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x5f, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x02, 0x64,
	0x74, 0x12, 0x3e, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x47, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x37, 0x0a, 0x02, 0x68, 0x77,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x48, 0x57, 0x5f, 0x52,
	0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52,
	0x02, 0x68, 0x77, 0x12, 0x3f, 0x0a, 0x02, 0x62, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x5f, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x42, 0x61, 0x72,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x72, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x02, 0x62, 0x63, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x17, 0x5a,
	0x15, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_scanner_config_proto_rawDescOnce sync.Once
	file_scanner_config_proto_rawDescData = file_scanner_config_proto_rawDesc
)

func file_scanner_config_proto_rawDescGZIP() []byte {
	file_scanner_config_proto_rawDescOnce.Do(func() {
		file_scanner_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_scanner_config_proto_rawDescData)
	})
	return file_scanner_config_proto_rawDescData
}

var file_scanner_config_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_scanner_config_proto_goTypes = []interface{}{
	(*Delta_Target_Config)(nil),                     // 0: scanner_config.Delta_Target_Config
	(*Color_Config)(nil),                            // 1: scanner_config.Color_Config
	(*Camera_Serial_Number_Config)(nil),             // 2: scanner_config.Camera_Serial_Number_Config
	(*Scanner_Barcode_Str_Config)(nil),              // 3: scanner_config.Scanner_Barcode_Str_Config
	(*Set_Delta_Target_Config_Request)(nil),         // 4: scanner_config.Set_Delta_Target_Config_Request
	(*Get_Delta_Target_Config_Request)(nil),         // 5: scanner_config.Get_Delta_Target_Config_Request
	(*Set_Color_Config_Request)(nil),                // 6: scanner_config.Set_Color_Config_Request
	(*Get_Color_Config_Request)(nil),                // 7: scanner_config.Get_Color_Config_Request
	(*Set_Camera_Serial_Number_Config_Request)(nil), // 8: scanner_config.Set_Camera_Serial_Number_Config_Request
	(*Get_Camera_Serial_Number_Config_Request)(nil), // 9: scanner_config.Get_Camera_Serial_Number_Config_Request
	(*Set_HW_Revision_Request)(nil),                 // 10: scanner_config.Set_HW_Revision_Request
	(*Get_HW_Revision_Request)(nil),                 // 11: scanner_config.Get_HW_Revision_Request
	(*Set_Scanner_Barcode_Str_Request)(nil),         // 12: scanner_config.Set_Scanner_Barcode_Str_Request
	(*Get_Scanner_Barcode_Str_Request)(nil),         // 13: scanner_config.Get_Scanner_Barcode_Str_Request
	(*Get_Delta_Target_Config_Reply)(nil),           // 14: scanner_config.Get_Delta_Target_Config_Reply
	(*Get_Color_Config_Reply)(nil),                  // 15: scanner_config.Get_Color_Config_Reply
	(*Get_Camera_Serial_Number_Config_Reply)(nil),   // 16: scanner_config.Get_Camera_Serial_Number_Config_Reply
	(*Get_HW_Revision_Reply)(nil),                   // 17: scanner_config.Get_HW_Revision_Reply
	(*Get_Scanner_Barcode_Str_Reply)(nil),           // 18: scanner_config.Get_Scanner_Barcode_Str_Reply
	(*Request)(nil),                                 // 19: scanner_config.Request
	(*Reply)(nil),                                   // 20: scanner_config.Reply
	(*error1.Error)(nil),                            // 21: error.Error
	(*ack.Ack)(nil),                                 // 22: ack.Ack
}
var file_scanner_config_proto_depIdxs = []int32{
	0,  // 0: scanner_config.Set_Delta_Target_Config_Request.config:type_name -> scanner_config.Delta_Target_Config
	1,  // 1: scanner_config.Set_Color_Config_Request.config:type_name -> scanner_config.Color_Config
	2,  // 2: scanner_config.Set_Camera_Serial_Number_Config_Request.config:type_name -> scanner_config.Camera_Serial_Number_Config
	3,  // 3: scanner_config.Set_Scanner_Barcode_Str_Request.config:type_name -> scanner_config.Scanner_Barcode_Str_Config
	0,  // 4: scanner_config.Get_Delta_Target_Config_Reply.config:type_name -> scanner_config.Delta_Target_Config
	1,  // 5: scanner_config.Get_Color_Config_Reply.config:type_name -> scanner_config.Color_Config
	2,  // 6: scanner_config.Get_Camera_Serial_Number_Config_Reply.config:type_name -> scanner_config.Camera_Serial_Number_Config
	3,  // 7: scanner_config.Get_Scanner_Barcode_Str_Reply.config:type_name -> scanner_config.Scanner_Barcode_Str_Config
	4,  // 8: scanner_config.Request.set_dt:type_name -> scanner_config.Set_Delta_Target_Config_Request
	5,  // 9: scanner_config.Request.get_dt:type_name -> scanner_config.Get_Delta_Target_Config_Request
	6,  // 10: scanner_config.Request.set_color:type_name -> scanner_config.Set_Color_Config_Request
	7,  // 11: scanner_config.Request.get_color:type_name -> scanner_config.Get_Color_Config_Request
	8,  // 12: scanner_config.Request.set_sn:type_name -> scanner_config.Set_Camera_Serial_Number_Config_Request
	9,  // 13: scanner_config.Request.get_sn:type_name -> scanner_config.Get_Camera_Serial_Number_Config_Request
	10, // 14: scanner_config.Request.set_hw:type_name -> scanner_config.Set_HW_Revision_Request
	11, // 15: scanner_config.Request.get_hw:type_name -> scanner_config.Get_HW_Revision_Request
	12, // 16: scanner_config.Request.set_bc:type_name -> scanner_config.Set_Scanner_Barcode_Str_Request
	13, // 17: scanner_config.Request.get_bc:type_name -> scanner_config.Get_Scanner_Barcode_Str_Request
	21, // 18: scanner_config.Reply.error:type_name -> error.Error
	22, // 19: scanner_config.Reply.ack:type_name -> ack.Ack
	14, // 20: scanner_config.Reply.dt:type_name -> scanner_config.Get_Delta_Target_Config_Reply
	15, // 21: scanner_config.Reply.color:type_name -> scanner_config.Get_Color_Config_Reply
	16, // 22: scanner_config.Reply.sn:type_name -> scanner_config.Get_Camera_Serial_Number_Config_Reply
	17, // 23: scanner_config.Reply.hw:type_name -> scanner_config.Get_HW_Revision_Reply
	18, // 24: scanner_config.Reply.bc:type_name -> scanner_config.Get_Scanner_Barcode_Str_Reply
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_scanner_config_proto_init() }
func file_scanner_config_proto_init() {
	if File_scanner_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_scanner_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Delta_Target_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Color_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Camera_Serial_Number_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scanner_Barcode_Str_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Delta_Target_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Delta_Target_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Color_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Color_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Camera_Serial_Number_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Camera_Serial_Number_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_HW_Revision_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_HW_Revision_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Scanner_Barcode_Str_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Scanner_Barcode_Str_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Delta_Target_Config_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Color_Config_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Camera_Serial_Number_Config_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_HW_Revision_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Scanner_Barcode_Str_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_scanner_config_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_scanner_config_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*Request_SetDt)(nil),
		(*Request_GetDt)(nil),
		(*Request_SetColor)(nil),
		(*Request_GetColor)(nil),
		(*Request_SetSn)(nil),
		(*Request_GetSn)(nil),
		(*Request_SetHw)(nil),
		(*Request_GetHw)(nil),
		(*Request_SetBc)(nil),
		(*Request_GetBc)(nil),
	}
	file_scanner_config_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Dt)(nil),
		(*Reply_Color)(nil),
		(*Reply_Sn)(nil),
		(*Reply_Hw)(nil),
		(*Reply_Bc)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_scanner_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_scanner_config_proto_goTypes,
		DependencyIndexes: file_scanner_config_proto_depIdxs,
		MessageInfos:      file_scanner_config_proto_msgTypes,
	}.Build()
	File_scanner_config_proto = out.File
	file_scanner_config_proto_rawDesc = nil
	file_scanner_config_proto_goTypes = nil
	file_scanner_config_proto_depIdxs = nil
}

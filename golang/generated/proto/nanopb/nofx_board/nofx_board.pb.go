// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: nofx_board.proto

package nofx_board

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	diagnostic "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	drive_solenoids "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/drive_solenoids"
	park_brake "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/park_brake"
	request "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	rotary_encoder "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/rotary_encoder"
	sensors "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/sensors"
	time "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/time"
	version "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/version"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Reply:
	//
	//	*Reply_Pong
	//	*Reply_RotaryEncoder
	//	*Reply_ParkBrake
	//	*Reply_ParkBrakeQuery
	//	*Reply_Version
	//	*Reply_Sensors
	//	*Reply_Time
	//	*Reply_DriveSolenoids
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nofx_board_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_nofx_board_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_nofx_board_proto_rawDescGZIP(), []int{0}
}

func (x *Reply) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetPong() *diagnostic.Pong {
	if x, ok := x.GetReply().(*Reply_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Reply) GetRotaryEncoder() *rotary_encoder.Reply {
	if x, ok := x.GetReply().(*Reply_RotaryEncoder); ok {
		return x.RotaryEncoder
	}
	return nil
}

func (x *Reply) GetParkBrake() *park_brake.Reply {
	if x, ok := x.GetReply().(*Reply_ParkBrake); ok {
		return x.ParkBrake
	}
	return nil
}

func (x *Reply) GetParkBrakeQuery() *park_brake.Query_Reply {
	if x, ok := x.GetReply().(*Reply_ParkBrakeQuery); ok {
		return x.ParkBrakeQuery
	}
	return nil
}

func (x *Reply) GetVersion() *version.Version_Reply {
	if x, ok := x.GetReply().(*Reply_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Reply) GetSensors() *sensors.Reply {
	if x, ok := x.GetReply().(*Reply_Sensors); ok {
		return x.Sensors
	}
	return nil
}

func (x *Reply) GetTime() *time.Reply {
	if x, ok := x.GetReply().(*Reply_Time); ok {
		return x.Time
	}
	return nil
}

func (x *Reply) GetDriveSolenoids() *drive_solenoids.Reply {
	if x, ok := x.GetReply().(*Reply_DriveSolenoids); ok {
		return x.DriveSolenoids
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Pong struct {
	Pong *diagnostic.Pong `protobuf:"bytes,2,opt,name=pong,proto3,oneof"`
}

type Reply_RotaryEncoder struct {
	RotaryEncoder *rotary_encoder.Reply `protobuf:"bytes,3,opt,name=rotary_encoder,json=rotaryEncoder,proto3,oneof"`
}

type Reply_ParkBrake struct {
	ParkBrake *park_brake.Reply `protobuf:"bytes,4,opt,name=park_brake,json=parkBrake,proto3,oneof"`
}

type Reply_ParkBrakeQuery struct {
	ParkBrakeQuery *park_brake.Query_Reply `protobuf:"bytes,5,opt,name=park_brake_query,json=parkBrakeQuery,proto3,oneof"`
}

type Reply_Version struct {
	Version *version.Version_Reply `protobuf:"bytes,6,opt,name=version,proto3,oneof"`
}

type Reply_Sensors struct {
	Sensors *sensors.Reply `protobuf:"bytes,7,opt,name=sensors,proto3,oneof"`
}

type Reply_Time struct {
	Time *time.Reply `protobuf:"bytes,8,opt,name=time,proto3,oneof"`
}

type Reply_DriveSolenoids struct {
	DriveSolenoids *drive_solenoids.Reply `protobuf:"bytes,9,opt,name=drive_solenoids,json=driveSolenoids,proto3,oneof"`
}

func (*Reply_Pong) isReply_Reply() {}

func (*Reply_RotaryEncoder) isReply_Reply() {}

func (*Reply_ParkBrake) isReply_Reply() {}

func (*Reply_ParkBrakeQuery) isReply_Reply() {}

func (*Reply_Version) isReply_Reply() {}

func (*Reply_Sensors) isReply_Reply() {}

func (*Reply_Time) isReply_Reply() {}

func (*Reply_DriveSolenoids) isReply_Reply() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *request.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Types that are assignable to Request:
	//
	//	*Request_Ping
	//	*Request_RotaryEncoder
	//	*Request_ParkBrake
	//	*Request_ParkBrakeQuery
	//	*Request_Version
	//	*Request_Reset_
	//	*Request_Sensors
	//	*Request_Time
	//	*Request_DriveSolenoids
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nofx_board_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_nofx_board_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_nofx_board_proto_rawDescGZIP(), []int{1}
}

func (x *Request) GetHeader() *request.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetPing() *diagnostic.Ping {
	if x, ok := x.GetRequest().(*Request_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Request) GetRotaryEncoder() *rotary_encoder.Request {
	if x, ok := x.GetRequest().(*Request_RotaryEncoder); ok {
		return x.RotaryEncoder
	}
	return nil
}

func (x *Request) GetParkBrake() *park_brake.Request {
	if x, ok := x.GetRequest().(*Request_ParkBrake); ok {
		return x.ParkBrake
	}
	return nil
}

func (x *Request) GetParkBrakeQuery() *park_brake.Query_Request {
	if x, ok := x.GetRequest().(*Request_ParkBrakeQuery); ok {
		return x.ParkBrakeQuery
	}
	return nil
}

func (x *Request) GetVersion() *version.Version_Request {
	if x, ok := x.GetRequest().(*Request_Version); ok {
		return x.Version
	}
	return nil
}

func (x *Request) GetReset_() *version.Reset_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

func (x *Request) GetSensors() *sensors.Request {
	if x, ok := x.GetRequest().(*Request_Sensors); ok {
		return x.Sensors
	}
	return nil
}

func (x *Request) GetTime() *time.Request {
	if x, ok := x.GetRequest().(*Request_Time); ok {
		return x.Time
	}
	return nil
}

func (x *Request) GetDriveSolenoids() *drive_solenoids.Request {
	if x, ok := x.GetRequest().(*Request_DriveSolenoids); ok {
		return x.DriveSolenoids
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Ping struct {
	Ping *diagnostic.Ping `protobuf:"bytes,2,opt,name=ping,proto3,oneof"`
}

type Request_RotaryEncoder struct {
	RotaryEncoder *rotary_encoder.Request `protobuf:"bytes,3,opt,name=rotary_encoder,json=rotaryEncoder,proto3,oneof"`
}

type Request_ParkBrake struct {
	ParkBrake *park_brake.Request `protobuf:"bytes,4,opt,name=park_brake,json=parkBrake,proto3,oneof"`
}

type Request_ParkBrakeQuery struct {
	ParkBrakeQuery *park_brake.Query_Request `protobuf:"bytes,5,opt,name=park_brake_query,json=parkBrakeQuery,proto3,oneof"`
}

type Request_Version struct {
	Version *version.Version_Request `protobuf:"bytes,6,opt,name=version,proto3,oneof"`
}

type Request_Reset_ struct {
	Reset_ *version.Reset_Request `protobuf:"bytes,7,opt,name=reset,proto3,oneof"`
}

type Request_Sensors struct {
	Sensors *sensors.Request `protobuf:"bytes,8,opt,name=sensors,proto3,oneof"`
}

type Request_Time struct {
	Time *time.Request `protobuf:"bytes,9,opt,name=time,proto3,oneof"`
}

type Request_DriveSolenoids struct {
	DriveSolenoids *drive_solenoids.Request `protobuf:"bytes,10,opt,name=drive_solenoids,json=driveSolenoids,proto3,oneof"`
}

func (*Request_Ping) isRequest_Request() {}

func (*Request_RotaryEncoder) isRequest_Request() {}

func (*Request_ParkBrake) isRequest_Request() {}

func (*Request_ParkBrakeQuery) isRequest_Request() {}

func (*Request_Version) isRequest_Request() {}

func (*Request_Reset_) isRequest_Request() {}

func (*Request_Sensors) isRequest_Request() {}

func (*Request_Time) isRequest_Request() {}

func (*Request_DriveSolenoids) isRequest_Request() {}

var File_nofx_board_proto protoreflect.FileDescriptor

var file_nofx_board_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6e, 0x6f, 0x66, 0x78, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x6e, 0x6f, 0x66, 0x78, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x1a, 0x33,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61,
	0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f,
	0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72,
	0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c,
	0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e,
	0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e,
	0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x72, 0x69, 0x76,
	0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69, 0x62,
	0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe7, 0x03, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x6f,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6f,
	0x6e, 0x67, 0x12, 0x3e, 0x0a, 0x0e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x6f, 0x74,
	0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64,
	0x65, 0x72, 0x12, 0x32, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72,
	0x61, 0x6b, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x09, 0x70, 0x61, 0x72,
	0x6b, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62,
	0x72, 0x61, 0x6b, 0x65, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x61, 0x72,
	0x6b, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x2a, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x41,
	0x0a, 0x0f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f,
	0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48,
	0x00, 0x52, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x53, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xa9, 0x04, 0x0a, 0x07, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x40,
	0x0a, 0x0e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x5f,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x0d, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x12, 0x34, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b,
	0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x70, 0x61, 0x72,
	0x6b, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x12, 0x45, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62,
	0x72, 0x61, 0x6b, 0x65, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x70,
	0x61, 0x72, 0x6b, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x34, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x73, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f,
	0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64,
	0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x53, 0x6f, 0x6c, 0x65, 0x6e, 0x6f, 0x69, 0x64, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x13, 0x5a, 0x11, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x6e, 0x6f, 0x66, 0x78, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_nofx_board_proto_rawDescOnce sync.Once
	file_nofx_board_proto_rawDescData = file_nofx_board_proto_rawDesc
)

func file_nofx_board_proto_rawDescGZIP() []byte {
	file_nofx_board_proto_rawDescOnce.Do(func() {
		file_nofx_board_proto_rawDescData = protoimpl.X.CompressGZIP(file_nofx_board_proto_rawDescData)
	})
	return file_nofx_board_proto_rawDescData
}

var file_nofx_board_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_nofx_board_proto_goTypes = []interface{}{
	(*Reply)(nil),                    // 0: nofx_board.Reply
	(*Request)(nil),                  // 1: nofx_board.Request
	(*request.RequestHeader)(nil),    // 2: request.RequestHeader
	(*diagnostic.Pong)(nil),          // 3: diagnostic.Pong
	(*rotary_encoder.Reply)(nil),     // 4: rotary_encoder.Reply
	(*park_brake.Reply)(nil),         // 5: park_brake.Reply
	(*park_brake.Query_Reply)(nil),   // 6: park_brake.Query_Reply
	(*version.Version_Reply)(nil),    // 7: version.Version_Reply
	(*sensors.Reply)(nil),            // 8: sensors.Reply
	(*time.Reply)(nil),               // 9: time.Reply
	(*drive_solenoids.Reply)(nil),    // 10: drive_solenoids.Reply
	(*diagnostic.Ping)(nil),          // 11: diagnostic.Ping
	(*rotary_encoder.Request)(nil),   // 12: rotary_encoder.Request
	(*park_brake.Request)(nil),       // 13: park_brake.Request
	(*park_brake.Query_Request)(nil), // 14: park_brake.Query_Request
	(*version.Version_Request)(nil),  // 15: version.Version_Request
	(*version.Reset_Request)(nil),    // 16: version.Reset_Request
	(*sensors.Request)(nil),          // 17: sensors.Request
	(*time.Request)(nil),             // 18: time.Request
	(*drive_solenoids.Request)(nil),  // 19: drive_solenoids.Request
}
var file_nofx_board_proto_depIdxs = []int32{
	2,  // 0: nofx_board.Reply.header:type_name -> request.RequestHeader
	3,  // 1: nofx_board.Reply.pong:type_name -> diagnostic.Pong
	4,  // 2: nofx_board.Reply.rotary_encoder:type_name -> rotary_encoder.Reply
	5,  // 3: nofx_board.Reply.park_brake:type_name -> park_brake.Reply
	6,  // 4: nofx_board.Reply.park_brake_query:type_name -> park_brake.Query_Reply
	7,  // 5: nofx_board.Reply.version:type_name -> version.Version_Reply
	8,  // 6: nofx_board.Reply.sensors:type_name -> sensors.Reply
	9,  // 7: nofx_board.Reply.time:type_name -> time.Reply
	10, // 8: nofx_board.Reply.drive_solenoids:type_name -> drive_solenoids.Reply
	2,  // 9: nofx_board.Request.header:type_name -> request.RequestHeader
	11, // 10: nofx_board.Request.ping:type_name -> diagnostic.Ping
	12, // 11: nofx_board.Request.rotary_encoder:type_name -> rotary_encoder.Request
	13, // 12: nofx_board.Request.park_brake:type_name -> park_brake.Request
	14, // 13: nofx_board.Request.park_brake_query:type_name -> park_brake.Query_Request
	15, // 14: nofx_board.Request.version:type_name -> version.Version_Request
	16, // 15: nofx_board.Request.reset:type_name -> version.Reset_Request
	17, // 16: nofx_board.Request.sensors:type_name -> sensors.Request
	18, // 17: nofx_board.Request.time:type_name -> time.Request
	19, // 18: nofx_board.Request.drive_solenoids:type_name -> drive_solenoids.Request
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_nofx_board_proto_init() }
func file_nofx_board_proto_init() {
	if File_nofx_board_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_nofx_board_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nofx_board_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_nofx_board_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Reply_Pong)(nil),
		(*Reply_RotaryEncoder)(nil),
		(*Reply_ParkBrake)(nil),
		(*Reply_ParkBrakeQuery)(nil),
		(*Reply_Version)(nil),
		(*Reply_Sensors)(nil),
		(*Reply_Time)(nil),
		(*Reply_DriveSolenoids)(nil),
	}
	file_nofx_board_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Request_Ping)(nil),
		(*Request_RotaryEncoder)(nil),
		(*Request_ParkBrake)(nil),
		(*Request_ParkBrakeQuery)(nil),
		(*Request_Version)(nil),
		(*Request_Reset_)(nil),
		(*Request_Sensors)(nil),
		(*Request_Time)(nil),
		(*Request_DriveSolenoids)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_nofx_board_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_nofx_board_proto_goTypes,
		DependencyIndexes: file_nofx_board_proto_depIdxs,
		MessageInfos:      file_nofx_board_proto_msgTypes,
	}.Build()
	File_nofx_board_proto = out.File
	file_nofx_board_proto_rawDesc = nil
	file_nofx_board_proto_goTypes = nil
	file_nofx_board_proto_depIdxs = nil
}

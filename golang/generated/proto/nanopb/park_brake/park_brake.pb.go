// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: park_brake.proto

package park_brake

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Onoff bool `protobuf:"varint,1,opt,name=onoff,proto3" json:"onoff,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_park_brake_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_park_brake_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_park_brake_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetOnoff() bool {
	if x != nil {
		return x.Onoff
	}
	return false
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_park_brake_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_park_brake_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_park_brake_proto_rawDescGZIP(), []int{1}
}

type Query_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Query_Request) Reset() {
	*x = Query_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_park_brake_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query_Request) ProtoMessage() {}

func (x *Query_Request) ProtoReflect() protoreflect.Message {
	mi := &file_park_brake_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query_Request.ProtoReflect.Descriptor instead.
func (*Query_Request) Descriptor() ([]byte, []int) {
	return file_park_brake_proto_rawDescGZIP(), []int{2}
}

type Query_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Onoff bool `protobuf:"varint,1,opt,name=onoff,proto3" json:"onoff,omitempty"`
}

func (x *Query_Reply) Reset() {
	*x = Query_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_park_brake_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query_Reply) ProtoMessage() {}

func (x *Query_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_park_brake_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query_Reply.ProtoReflect.Descriptor instead.
func (*Query_Reply) Descriptor() ([]byte, []int) {
	return file_park_brake_proto_rawDescGZIP(), []int{3}
}

func (x *Query_Reply) GetOnoff() bool {
	if x != nil {
		return x.Onoff
	}
	return false
}

var File_park_brake_proto protoreflect.FileDescriptor

var file_park_brake_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6b, 0x65, 0x22, 0x1f,
	0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x6e, 0x6f,
	0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x6f, 0x6e, 0x6f, 0x66, 0x66, 0x22,
	0x07, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x0f, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x23, 0x0a, 0x0b, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x6e, 0x6f, 0x66,
	0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x6f, 0x6e, 0x6f, 0x66, 0x66, 0x42, 0x13,
	0x5a, 0x11, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x72, 0x6b, 0x5f, 0x62, 0x72,
	0x61, 0x6b, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_park_brake_proto_rawDescOnce sync.Once
	file_park_brake_proto_rawDescData = file_park_brake_proto_rawDesc
)

func file_park_brake_proto_rawDescGZIP() []byte {
	file_park_brake_proto_rawDescOnce.Do(func() {
		file_park_brake_proto_rawDescData = protoimpl.X.CompressGZIP(file_park_brake_proto_rawDescData)
	})
	return file_park_brake_proto_rawDescData
}

var file_park_brake_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_park_brake_proto_goTypes = []interface{}{
	(*Request)(nil),       // 0: park_brake.Request
	(*Reply)(nil),         // 1: park_brake.Reply
	(*Query_Request)(nil), // 2: park_brake.Query_Request
	(*Query_Reply)(nil),   // 3: park_brake.Query_Reply
}
var file_park_brake_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_park_brake_proto_init() }
func file_park_brake_proto_init() {
	if File_park_brake_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_park_brake_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_park_brake_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_park_brake_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_park_brake_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_park_brake_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_park_brake_proto_goTypes,
		DependencyIndexes: file_park_brake_proto_depIdxs,
		MessageInfos:      file_park_brake_proto_msgTypes,
	}.Build()
	File_park_brake_proto = out.File
	file_park_brake_proto_rawDesc = nil
	file_park_brake_proto_goTypes = nil
	file_park_brake_proto_depIdxs = nil
}

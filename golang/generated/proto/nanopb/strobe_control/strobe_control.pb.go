// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: strobe_control.proto

package strobe_control

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposureUs uint32 `protobuf:"varint,1,opt,name=exposure_us,json=exposureUs,proto3" json:"exposure_us,omitempty"`
	PeriodUs   uint32 `protobuf:"varint,2,opt,name=period_us,json=periodUs,proto3" json:"period_us,omitempty"`
	// How many target images are taken per every predict image.
	// Typical values are 5, 8, 10, etc.
	TargetsPerPredictRatio uint32 `protobuf:"varint,3,opt,name=targets_per_predict_ratio,json=targetsPerPredictRatio,proto3" json:"targets_per_predict_ratio,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strobe_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_strobe_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_strobe_control_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetExposureUs() uint32 {
	if x != nil {
		return x.ExposureUs
	}
	return 0
}

func (x *Request) GetPeriodUs() uint32 {
	if x != nil {
		return x.PeriodUs
	}
	return 0
}

func (x *Request) GetTargetsPerPredictRatio() uint32 {
	if x != nil {
		return x.TargetsPerPredictRatio
	}
	return 0
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ok bool `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strobe_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_strobe_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_strobe_control_proto_rawDescGZIP(), []int{1}
}

func (x *Reply) GetOk() bool {
	if x != nil {
		return x.Ok
	}
	return false
}

var File_strobe_control_proto protoreflect.FileDescriptor

var file_strobe_control_proto_rawDesc = []byte{
	0x0a, 0x14, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x22, 0x82, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x55, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x73,
	0x12, 0x39, 0x0a, 0x19, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x16, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x17, 0x0a, 0x05, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x02, 0x6f, 0x6b, 0x42, 0x17, 0x5a, 0x15, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x73,
	0x74, 0x72, 0x6f, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_strobe_control_proto_rawDescOnce sync.Once
	file_strobe_control_proto_rawDescData = file_strobe_control_proto_rawDesc
)

func file_strobe_control_proto_rawDescGZIP() []byte {
	file_strobe_control_proto_rawDescOnce.Do(func() {
		file_strobe_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_strobe_control_proto_rawDescData)
	})
	return file_strobe_control_proto_rawDescData
}

var file_strobe_control_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_strobe_control_proto_goTypes = []interface{}{
	(*Request)(nil), // 0: strobe_control.Request
	(*Reply)(nil),   // 1: strobe_control.Reply
}
var file_strobe_control_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_strobe_control_proto_init() }
func file_strobe_control_proto_init() {
	if File_strobe_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_strobe_control_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_strobe_control_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_strobe_control_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_strobe_control_proto_goTypes,
		DependencyIndexes: file_strobe_control_proto_depIdxs,
		MessageInfos:      file_strobe_control_proto_msgTypes,
	}.Build()
	File_strobe_control_proto = out.File
	file_strobe_control_proto_rawDesc = nil
	file_strobe_control_proto_goTypes = nil
	file_strobe_control_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: arc_detector.proto

package arc_detector

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ack "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ack"
	error1 "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/error"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Common message types
type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether arc detector is enabled and can cause alarms
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Upper and lower acceptable current limit (A)
	UpperLimit float32 `protobuf:"fixed32,2,opt,name=upperLimit,proto3" json:"upperLimit,omitempty"`
	LowerLimit float32 `protobuf:"fixed32,3,opt,name=lowerLimit,proto3" json:"lowerLimit,omitempty"`
	// Time period in over which the arc detector integrates events
	AlarmPeriod uint32 `protobuf:"varint,4,opt,name=alarmPeriod,proto3" json:"alarmPeriod,omitempty"`
	// Number of arc events to accumulate over period before alarm is raised
	AlarmThreshold uint32 `protobuf:"varint,5,opt,name=alarmThreshold,proto3" json:"alarmThreshold,omitempty"`
	// Time delay between firing start and first LPSU current sample for arc detector (ms)
	InitialDelay uint32 `protobuf:"varint,6,opt,name=initialDelay,proto3" json:"initialDelay,omitempty"` // uint16
	// Sample period for laser current after initial sample (ms)
	SampleInterval uint32 `protobuf:"varint,7,opt,name=sampleInterval,proto3" json:"sampleInterval,omitempty"` // uint16
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Config) GetUpperLimit() float32 {
	if x != nil {
		return x.UpperLimit
	}
	return 0
}

func (x *Config) GetLowerLimit() float32 {
	if x != nil {
		return x.LowerLimit
	}
	return 0
}

func (x *Config) GetAlarmPeriod() uint32 {
	if x != nil {
		return x.AlarmPeriod
	}
	return 0
}

func (x *Config) GetAlarmThreshold() uint32 {
	if x != nil {
		return x.AlarmThreshold
	}
	return 0
}

func (x *Config) GetInitialDelay() uint32 {
	if x != nil {
		return x.InitialDelay
	}
	return 0
}

func (x *Config) GetSampleInterval() uint32 {
	if x != nil {
		return x.SampleInterval
	}
	return 0
}

// Reset arc detector alarm
type Reset_Alarm_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Reset_Alarm_Request) Reset() {
	*x = Reset_Alarm_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reset_Alarm_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reset_Alarm_Request) ProtoMessage() {}

func (x *Reset_Alarm_Request) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reset_Alarm_Request.ProtoReflect.Descriptor instead.
func (*Reset_Alarm_Request) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{1}
}

// Get status
type Status_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Status_Request) Reset() {
	*x = Status_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status_Request) ProtoMessage() {}

func (x *Status_Request) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status_Request.ProtoReflect.Descriptor instead.
func (*Status_Request) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{2}
}

type Status_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the arc detector has been properly configured and enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Alarm threshold was reached and firing is inhibited
	Alarm bool `protobuf:"varint,2,opt,name=alarm,proto3" json:"alarm,omitempty"`
	// Lowest and highest LPSU current readings during firing
	MinCurrent float32 `protobuf:"fixed32,3,opt,name=minCurrent,proto3" json:"minCurrent,omitempty"`
	MaxCurrent float32 `protobuf:"fixed32,4,opt,name=maxCurrent,proto3" json:"maxCurrent,omitempty"`
}

func (x *Status_Reply) Reset() {
	*x = Status_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status_Reply) ProtoMessage() {}

func (x *Status_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status_Reply.ProtoReflect.Descriptor instead.
func (*Status_Reply) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{3}
}

func (x *Status_Reply) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Status_Reply) GetAlarm() bool {
	if x != nil {
		return x.Alarm
	}
	return false
}

func (x *Status_Reply) GetMinCurrent() float32 {
	if x != nil {
		return x.MinCurrent
	}
	return 0
}

func (x *Status_Reply) GetMaxCurrent() float32 {
	if x != nil {
		return x.MaxCurrent
	}
	return 0
}

// Get/set arc detector config
type Set_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewConfig *Config `protobuf:"bytes,1,opt,name=newConfig,proto3" json:"newConfig,omitempty"`
}

func (x *Set_Config_Request) Reset() {
	*x = Set_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Set_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Set_Config_Request) ProtoMessage() {}

func (x *Set_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Set_Config_Request.ProtoReflect.Descriptor instead.
func (*Set_Config_Request) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{4}
}

func (x *Set_Config_Request) GetNewConfig() *Config {
	if x != nil {
		return x.NewConfig
	}
	return nil
}

type Get_Config_Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Get_Config_Request) Reset() {
	*x = Get_Config_Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Get_Config_Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Get_Config_Request) ProtoMessage() {}

func (x *Get_Config_Request) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Get_Config_Request.ProtoReflect.Descriptor instead.
func (*Get_Config_Request) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{5}
}

type Config_Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Conf *Config `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
}

func (x *Config_Reply) Reset() {
	*x = Config_Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config_Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config_Reply) ProtoMessage() {}

func (x *Config_Reply) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config_Reply.ProtoReflect.Descriptor instead.
func (*Config_Reply) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{6}
}

func (x *Config_Reply) GetConf() *Config {
	if x != nil {
		return x.Conf
	}
	return nil
}

// Container messages
type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_SetConf
	//	*Request_GetConf
	//	*Request_Status
	//	*Request_Reset_
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{7}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSetConf() *Set_Config_Request {
	if x, ok := x.GetRequest().(*Request_SetConf); ok {
		return x.SetConf
	}
	return nil
}

func (x *Request) GetGetConf() *Get_Config_Request {
	if x, ok := x.GetRequest().(*Request_GetConf); ok {
		return x.GetConf
	}
	return nil
}

func (x *Request) GetStatus() *Status_Request {
	if x, ok := x.GetRequest().(*Request_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Request) GetReset_() *Reset_Alarm_Request {
	if x, ok := x.GetRequest().(*Request_Reset_); ok {
		return x.Reset_
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_SetConf struct {
	SetConf *Set_Config_Request `protobuf:"bytes,1,opt,name=setConf,proto3,oneof"`
}

type Request_GetConf struct {
	GetConf *Get_Config_Request `protobuf:"bytes,2,opt,name=getConf,proto3,oneof"`
}

type Request_Status struct {
	Status *Status_Request `protobuf:"bytes,3,opt,name=status,proto3,oneof"`
}

type Request_Reset_ struct {
	Reset_ *Reset_Alarm_Request `protobuf:"bytes,4,opt,name=reset,proto3,oneof"`
}

func (*Request_SetConf) isRequest_Request() {}

func (*Request_GetConf) isRequest_Request() {}

func (*Request_Status) isRequest_Request() {}

func (*Request_Reset_) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Error
	//	*Reply_Ack
	//	*Reply_Status
	//	*Reply_Conf
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arc_detector_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_arc_detector_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_arc_detector_proto_rawDescGZIP(), []int{8}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetError() *error1.Error {
	if x, ok := x.GetReply().(*Reply_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Reply) GetAck() *ack.Ack {
	if x, ok := x.GetReply().(*Reply_Ack); ok {
		return x.Ack
	}
	return nil
}

func (x *Reply) GetStatus() *Status_Reply {
	if x, ok := x.GetReply().(*Reply_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Reply) GetConf() *Config_Reply {
	if x, ok := x.GetReply().(*Reply_Conf); ok {
		return x.Conf
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Error struct {
	Error *error1.Error `protobuf:"bytes,1,opt,name=error,proto3,oneof"`
}

type Reply_Ack struct {
	Ack *ack.Ack `protobuf:"bytes,2,opt,name=ack,proto3,oneof"`
}

type Reply_Status struct {
	Status *Status_Reply `protobuf:"bytes,3,opt,name=status,proto3,oneof"`
}

type Reply_Conf struct {
	Conf *Config_Reply `protobuf:"bytes,4,opt,name=conf,proto3,oneof"`
}

func (*Reply_Error) isReply_Reply() {}

func (*Reply_Ack) isReply_Reply() {}

func (*Reply_Status) isReply_Reply() {}

func (*Reply_Conf) isReply_Reply() {}

var File_arc_detector_proto protoreflect.FileDescriptor

var file_arc_detector_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x1a, 0x2e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x6c, 0x69,
	0x62, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x2f, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xf8, 0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x70, 0x65, 0x72, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x75, 0x70, 0x70, 0x65, 0x72,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x6c, 0x61, 0x72,
	0x6d, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x6c, 0x61, 0x72, 0x6d,
	0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x73, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22, 0x15, 0x0a, 0x13, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x5f, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x7e, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61,
	0x6c, 0x61, 0x72, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x09, 0x6e, 0x65,
	0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x14,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x38, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x04, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0x83,
	0x02, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x07, 0x73, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x72,
	0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x5f, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x07, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x3c, 0x0a, 0x07, 0x67, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x72, 0x63, 0x5f,
	0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x5f, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x67,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39,
	0x0a, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x5f, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x73, 0x65, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0xbc, 0x01, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x08, 0x2e, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x03, 0x61,
	0x63, 0x6b, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x6e, 0x66,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x72, 0x63, 0x5f, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x6e, 0x66, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x42, 0x15, 0x5a, 0x13, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x61, 0x72,
	0x63, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_arc_detector_proto_rawDescOnce sync.Once
	file_arc_detector_proto_rawDescData = file_arc_detector_proto_rawDesc
)

func file_arc_detector_proto_rawDescGZIP() []byte {
	file_arc_detector_proto_rawDescOnce.Do(func() {
		file_arc_detector_proto_rawDescData = protoimpl.X.CompressGZIP(file_arc_detector_proto_rawDescData)
	})
	return file_arc_detector_proto_rawDescData
}

var file_arc_detector_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_arc_detector_proto_goTypes = []interface{}{
	(*Config)(nil),              // 0: arc_detector.Config
	(*Reset_Alarm_Request)(nil), // 1: arc_detector.Reset_Alarm_Request
	(*Status_Request)(nil),      // 2: arc_detector.Status_Request
	(*Status_Reply)(nil),        // 3: arc_detector.Status_Reply
	(*Set_Config_Request)(nil),  // 4: arc_detector.Set_Config_Request
	(*Get_Config_Request)(nil),  // 5: arc_detector.Get_Config_Request
	(*Config_Reply)(nil),        // 6: arc_detector.Config_Reply
	(*Request)(nil),             // 7: arc_detector.Request
	(*Reply)(nil),               // 8: arc_detector.Reply
	(*error1.Error)(nil),        // 9: error.Error
	(*ack.Ack)(nil),             // 10: ack.Ack
}
var file_arc_detector_proto_depIdxs = []int32{
	0,  // 0: arc_detector.Set_Config_Request.newConfig:type_name -> arc_detector.Config
	0,  // 1: arc_detector.Config_Reply.conf:type_name -> arc_detector.Config
	4,  // 2: arc_detector.Request.setConf:type_name -> arc_detector.Set_Config_Request
	5,  // 3: arc_detector.Request.getConf:type_name -> arc_detector.Get_Config_Request
	2,  // 4: arc_detector.Request.status:type_name -> arc_detector.Status_Request
	1,  // 5: arc_detector.Request.reset:type_name -> arc_detector.Reset_Alarm_Request
	9,  // 6: arc_detector.Reply.error:type_name -> error.Error
	10, // 7: arc_detector.Reply.ack:type_name -> ack.Ack
	3,  // 8: arc_detector.Reply.status:type_name -> arc_detector.Status_Reply
	6,  // 9: arc_detector.Reply.conf:type_name -> arc_detector.Config_Reply
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_arc_detector_proto_init() }
func file_arc_detector_proto_init() {
	if File_arc_detector_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_arc_detector_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reset_Alarm_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Set_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Get_Config_Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config_Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arc_detector_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_arc_detector_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*Request_SetConf)(nil),
		(*Request_GetConf)(nil),
		(*Request_Status)(nil),
		(*Request_Reset_)(nil),
	}
	file_arc_detector_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*Reply_Error)(nil),
		(*Reply_Ack)(nil),
		(*Reply_Status)(nil),
		(*Reply_Conf)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_arc_detector_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_arc_detector_proto_goTypes,
		DependencyIndexes: file_arc_detector_proto_depIdxs,
		MessageInfos:      file_arc_detector_proto_msgTypes,
	}.Build()
	File_arc_detector_proto = out.File
	file_arc_detector_proto_rawDesc = nil
	file_arc_detector_proto_goTypes = nil
	file_arc_detector_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: deck/proto/deck_service.proto

package deck

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Types that are assignable to Value:
	//
	//	*Config_BoolVal
	//	*Config_IntVal
	//	*Config_FloatVal
	//	*Config_StrVal
	Value isConfig_Value `protobuf_oneof:"value"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (m *Config) GetValue() isConfig_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Config) GetBoolVal() bool {
	if x, ok := x.GetValue().(*Config_BoolVal); ok {
		return x.BoolVal
	}
	return false
}

func (x *Config) GetIntVal() int64 {
	if x, ok := x.GetValue().(*Config_IntVal); ok {
		return x.IntVal
	}
	return 0
}

func (x *Config) GetFloatVal() float64 {
	if x, ok := x.GetValue().(*Config_FloatVal); ok {
		return x.FloatVal
	}
	return 0
}

func (x *Config) GetStrVal() string {
	if x, ok := x.GetValue().(*Config_StrVal); ok {
		return x.StrVal
	}
	return ""
}

type isConfig_Value interface {
	isConfig_Value()
}

type Config_BoolVal struct {
	BoolVal bool `protobuf:"varint,2,opt,name=bool_val,json=boolVal,proto3,oneof"`
}

type Config_IntVal struct {
	IntVal int64 `protobuf:"varint,3,opt,name=int_val,json=intVal,proto3,oneof"`
}

type Config_FloatVal struct {
	FloatVal float64 `protobuf:"fixed64,4,opt,name=float_val,json=floatVal,proto3,oneof"`
}

type Config_StrVal struct {
	StrVal string `protobuf:"bytes,5,opt,name=str_val,json=strVal,proto3,oneof"`
}

func (*Config_BoolVal) isConfig_Value() {}

func (*Config_IntVal) isConfig_Value() {}

func (*Config_FloatVal) isConfig_Value() {}

func (*Config_StrVal) isConfig_Value() {}

type SetConfigsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Camera  string    `protobuf:"bytes,1,opt,name=camera,proto3" json:"camera,omitempty"`
	Configs []*Config `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
}

func (x *SetConfigsRequest) Reset() {
	*x = SetConfigsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetConfigsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetConfigsRequest) ProtoMessage() {}

func (x *SetConfigsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetConfigsRequest.ProtoReflect.Descriptor instead.
func (*SetConfigsRequest) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{1}
}

func (x *SetConfigsRequest) GetCamera() string {
	if x != nil {
		return x.Camera
	}
	return ""
}

func (x *SetConfigsRequest) GetConfigs() []*Config {
	if x != nil {
		return x.Configs
	}
	return nil
}

type SetConfigsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetConfigsResponse) Reset() {
	*x = SetConfigsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetConfigsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetConfigsResponse) ProtoMessage() {}

func (x *SetConfigsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetConfigsResponse.ProtoReflect.Descriptor instead.
func (*SetConfigsResponse) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{2}
}

type GetConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Camera string `protobuf:"bytes,1,opt,name=camera,proto3" json:"camera,omitempty"`
	Key    string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Type   string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetConfigRequest) Reset() {
	*x = GetConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigRequest) ProtoMessage() {}

func (x *GetConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigRequest.ProtoReflect.Descriptor instead.
func (*GetConfigRequest) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetConfigRequest) GetCamera() string {
	if x != nil {
		return x.Camera
	}
	return ""
}

func (x *GetConfigRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *GetConfigRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type GetConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetConfigResponse) Reset() {
	*x = GetConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigResponse) ProtoMessage() {}

func (x *GetConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigResponse.ProtoReflect.Descriptor instead.
func (*GetConfigResponse) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetConfigResponse) GetConfig() *Config {
	if x != nil {
		return x.Config
	}
	return nil
}

type PingMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingMsg) Reset() {
	*x = PingMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingMsg) ProtoMessage() {}

func (x *PingMsg) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingMsg.ProtoReflect.Descriptor instead.
func (*PingMsg) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{5}
}

func (x *PingMsg) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PongMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PongMsg) Reset() {
	*x = PongMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_deck_proto_deck_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PongMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PongMsg) ProtoMessage() {}

func (x *PongMsg) ProtoReflect() protoreflect.Message {
	mi := &file_deck_proto_deck_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PongMsg.ProtoReflect.Descriptor instead.
func (*PongMsg) Descriptor() ([]byte, []int) {
	return file_deck_proto_deck_service_proto_rawDescGZIP(), []int{6}
}

func (x *PongMsg) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

var File_deck_proto_deck_service_proto protoreflect.FileDescriptor

var file_deck_proto_deck_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x64, 0x65, 0x63, 0x6b, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x65, 0x63,
	0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x04, 0x64, 0x65, 0x63, 0x6b, 0x22, 0x95, 0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x1b, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x12,
	0x19, 0x0a, 0x07, 0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52,
	0x08, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x07, 0x73, 0x74, 0x72,
	0x5f, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74,
	0x72, 0x56, 0x61, 0x6c, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x53, 0x0a,
	0x11, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x26, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x64, 0x65,
	0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x50, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x39, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x24, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x64, 0x65, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x17, 0x0a, 0x07, 0x50, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x67,
	0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x17,
	0x0a, 0x07, 0x50, 0x6f, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x32, 0xb8, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x63, 0x6b,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12,
	0x0d, 0x2e, 0x64, 0x65, 0x63, 0x6b, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x1a, 0x0d,
	0x2e, 0x64, 0x65, 0x63, 0x6b, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x4d, 0x73, 0x67, 0x22, 0x00, 0x12,
	0x41, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x17, 0x2e,
	0x64, 0x65, 0x63, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x64, 0x65, 0x63, 0x6b, 0x2e, 0x53, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x3e, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x16, 0x2e, 0x64, 0x65, 0x63, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x64, 0x65, 0x63, 0x6b, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x0c, 0x5a, 0x0a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x65, 0x63, 0x6b,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_deck_proto_deck_service_proto_rawDescOnce sync.Once
	file_deck_proto_deck_service_proto_rawDescData = file_deck_proto_deck_service_proto_rawDesc
)

func file_deck_proto_deck_service_proto_rawDescGZIP() []byte {
	file_deck_proto_deck_service_proto_rawDescOnce.Do(func() {
		file_deck_proto_deck_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_deck_proto_deck_service_proto_rawDescData)
	})
	return file_deck_proto_deck_service_proto_rawDescData
}

var file_deck_proto_deck_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_deck_proto_deck_service_proto_goTypes = []interface{}{
	(*Config)(nil),             // 0: deck.Config
	(*SetConfigsRequest)(nil),  // 1: deck.SetConfigsRequest
	(*SetConfigsResponse)(nil), // 2: deck.SetConfigsResponse
	(*GetConfigRequest)(nil),   // 3: deck.GetConfigRequest
	(*GetConfigResponse)(nil),  // 4: deck.GetConfigResponse
	(*PingMsg)(nil),            // 5: deck.PingMsg
	(*PongMsg)(nil),            // 6: deck.PongMsg
}
var file_deck_proto_deck_service_proto_depIdxs = []int32{
	0, // 0: deck.SetConfigsRequest.configs:type_name -> deck.Config
	0, // 1: deck.GetConfigResponse.config:type_name -> deck.Config
	5, // 2: deck.DeckService.Ping:input_type -> deck.PingMsg
	1, // 3: deck.DeckService.SetConfigs:input_type -> deck.SetConfigsRequest
	3, // 4: deck.DeckService.GetConfig:input_type -> deck.GetConfigRequest
	6, // 5: deck.DeckService.Ping:output_type -> deck.PongMsg
	2, // 6: deck.DeckService.SetConfigs:output_type -> deck.SetConfigsResponse
	4, // 7: deck.DeckService.GetConfig:output_type -> deck.GetConfigResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_deck_proto_deck_service_proto_init() }
func file_deck_proto_deck_service_proto_init() {
	if File_deck_proto_deck_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_deck_proto_deck_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_deck_proto_deck_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetConfigsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_deck_proto_deck_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetConfigsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_deck_proto_deck_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_deck_proto_deck_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_deck_proto_deck_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_deck_proto_deck_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PongMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_deck_proto_deck_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Config_BoolVal)(nil),
		(*Config_IntVal)(nil),
		(*Config_FloatVal)(nil),
		(*Config_StrVal)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_deck_proto_deck_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_deck_proto_deck_service_proto_goTypes,
		DependencyIndexes: file_deck_proto_deck_service_proto_depIdxs,
		MessageInfos:      file_deck_proto_deck_service_proto_msgTypes,
	}.Build()
	File_deck_proto_deck_service_proto = out.File
	file_deck_proto_deck_service_proto_rawDesc = nil
	file_deck_proto_deck_service_proto_goTypes = nil
	file_deck_proto_deck_service_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: deck/proto/deck_service.proto

package deck

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DeckService_Ping_FullMethodName       = "/deck.DeckService/Ping"
	DeckService_SetConfigs_FullMethodName = "/deck.DeckService/SetConfigs"
	DeckService_GetConfig_FullMethodName  = "/deck.DeckService/GetConfig"
)

// DeckServiceClient is the client API for DeckService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeckServiceClient interface {
	Ping(ctx context.Context, in *PingMsg, opts ...grpc.CallOption) (*PongMsg, error)
	SetConfigs(ctx context.Context, in *SetConfigsRequest, opts ...grpc.CallOption) (*SetConfigsResponse, error)
	GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error)
}

type deckServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeckServiceClient(cc grpc.ClientConnInterface) DeckServiceClient {
	return &deckServiceClient{cc}
}

func (c *deckServiceClient) Ping(ctx context.Context, in *PingMsg, opts ...grpc.CallOption) (*PongMsg, error) {
	out := new(PongMsg)
	err := c.cc.Invoke(ctx, DeckService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deckServiceClient) SetConfigs(ctx context.Context, in *SetConfigsRequest, opts ...grpc.CallOption) (*SetConfigsResponse, error) {
	out := new(SetConfigsResponse)
	err := c.cc.Invoke(ctx, DeckService_SetConfigs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deckServiceClient) GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error) {
	out := new(GetConfigResponse)
	err := c.cc.Invoke(ctx, DeckService_GetConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeckServiceServer is the server API for DeckService service.
// All implementations must embed UnimplementedDeckServiceServer
// for forward compatibility
type DeckServiceServer interface {
	Ping(context.Context, *PingMsg) (*PongMsg, error)
	SetConfigs(context.Context, *SetConfigsRequest) (*SetConfigsResponse, error)
	GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error)
	mustEmbedUnimplementedDeckServiceServer()
}

// UnimplementedDeckServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDeckServiceServer struct {
}

func (UnimplementedDeckServiceServer) Ping(context.Context, *PingMsg) (*PongMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedDeckServiceServer) SetConfigs(context.Context, *SetConfigsRequest) (*SetConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetConfigs not implemented")
}
func (UnimplementedDeckServiceServer) GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedDeckServiceServer) mustEmbedUnimplementedDeckServiceServer() {}

// UnsafeDeckServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeckServiceServer will
// result in compilation errors.
type UnsafeDeckServiceServer interface {
	mustEmbedUnimplementedDeckServiceServer()
}

func RegisterDeckServiceServer(s grpc.ServiceRegistrar, srv DeckServiceServer) {
	s.RegisterService(&DeckService_ServiceDesc, srv)
}

func _DeckService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeckServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeckService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeckServiceServer).Ping(ctx, req.(*PingMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeckService_SetConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeckServiceServer).SetConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeckService_SetConfigs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeckServiceServer).SetConfigs(ctx, req.(*SetConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeckService_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeckServiceServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeckService_GetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeckServiceServer).GetConfig(ctx, req.(*GetConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DeckService_ServiceDesc is the grpc.ServiceDesc for DeckService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeckService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "deck.DeckService",
	HandlerType: (*DeckServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _DeckService_Ping_Handler,
		},
		{
			MethodName: "SetConfigs",
			Handler:    _DeckService_SetConfigs_Handler,
		},
		{
			MethodName: "GetConfig",
			Handler:    _DeckService_GetConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "deck/proto/deck_service.proto",
}

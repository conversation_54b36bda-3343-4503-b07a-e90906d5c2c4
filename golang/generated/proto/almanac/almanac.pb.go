// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/almanac/almanac.proto

package almanac

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CategoryClassification int32

const (
	CategoryClassification_CATEGORY_WEED CategoryClassification = 0
	CategoryClassification_CATEGORY_CROP CategoryClassification = 1
)

// Enum value maps for CategoryClassification.
var (
	CategoryClassification_name = map[int32]string{
		0: "CATEGORY_WEED",
		1: "CATEGORY_CROP",
	}
	CategoryClassification_value = map[string]int32{
		"CATEGORY_WEED": 0,
		"CATEGORY_CROP": 1,
	}
)

func (x CategoryClassification) Enum() *CategoryClassification {
	p := new(CategoryClassification)
	*p = x
	return p
}

func (x CategoryClassification) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CategoryClassification) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_almanac_almanac_proto_enumTypes[0].Descriptor()
}

func (CategoryClassification) Type() protoreflect.EnumType {
	return &file_proto_almanac_almanac_proto_enumTypes[0]
}

func (x CategoryClassification) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CategoryClassification.Descriptor instead.
func (CategoryClassification) EnumDescriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{0}
}

type Formula struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Multiplier            float32 `protobuf:"fixed32,1,opt,name=multiplier,proto3" json:"multiplier,omitempty"`                                                        // A
	Offset                float32 `protobuf:"fixed32,2,opt,name=offset,proto3" json:"offset,omitempty"`                                                                // b
	Exponent              float32 `protobuf:"fixed32,3,opt,name=exponent,proto3" json:"exponent,omitempty"`                                                            // e
	FineTuneMultiplier    uint32  `protobuf:"varint,4,opt,name=fine_tune_multiplier,json=fineTuneMultiplier,proto3" json:"fine_tune_multiplier,omitempty"`             // discrete val 1-10
	MaxTime               uint32  `protobuf:"varint,5,opt,name=max_time,json=maxTime,proto3" json:"max_time,omitempty"`                                                // Time to cap all shooting at
	FineTuneMultiplierVal float32 `protobuf:"fixed32,6,opt,name=fine_tune_multiplier_val,json=fineTuneMultiplierVal,proto3" json:"fine_tune_multiplier_val,omitempty"` // how big each step is for moving ftm: F= 1.0 + (5-ftm)*ftm_val
}

func (x *Formula) Reset() {
	*x = Formula{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Formula) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Formula) ProtoMessage() {}

func (x *Formula) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Formula.ProtoReflect.Descriptor instead.
func (*Formula) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{0}
}

func (x *Formula) GetMultiplier() float32 {
	if x != nil {
		return x.Multiplier
	}
	return 0
}

func (x *Formula) GetOffset() float32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *Formula) GetExponent() float32 {
	if x != nil {
		return x.Exponent
	}
	return 0
}

func (x *Formula) GetFineTuneMultiplier() uint32 {
	if x != nil {
		return x.FineTuneMultiplier
	}
	return 0
}

func (x *Formula) GetMaxTime() uint32 {
	if x != nil {
		return x.MaxTime
	}
	return 0
}

func (x *Formula) GetFineTuneMultiplierVal() float32 {
	if x != nil {
		return x.FineTuneMultiplierVal
	}
	return 0
}

type Trust struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ignorable bool `protobuf:"varint,1,opt,name=ignorable,proto3" json:"ignorable,omitempty"` // In weeding Can be skipped if not enough time (will not be used in VE, but can be targeted still)
	Avoid     bool `protobuf:"varint,2,opt,name=avoid,proto3" json:"avoid,omitempty"`         // Do not allow shooting this
}

func (x *Trust) Reset() {
	*x = Trust{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trust) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trust) ProtoMessage() {}

func (x *Trust) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trust.ProtoReflect.Descriptor instead.
func (*Trust) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{1}
}

func (x *Trust) GetIgnorable() bool {
	if x != nil {
		return x.Ignorable
	}
	return false
}

func (x *Trust) GetAvoid() bool {
	if x != nil {
		return x.Avoid
	}
	return false
}

type ModelTrust struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinDoo            float32 `protobuf:"fixed32,1,opt,name=min_doo,json=minDoo,proto3" json:"min_doo,omitempty"`                                  // Minimum detections over opportuninty
	WeedingThreshold  float32 `protobuf:"fixed32,2,opt,name=weeding_threshold,json=weedingThreshold,proto3" json:"weeding_threshold,omitempty"`    // for weed: acts as WPT, for crop: used as crop protect threshold
	ThinningThreshold float32 `protobuf:"fixed32,3,opt,name=thinning_threshold,json=thinningThreshold,proto3" json:"thinning_threshold,omitempty"` // for crop: used as keeper theshold, for weed: used as reverse crop protect threshold
	BandingThreshold  float32 `protobuf:"fixed32,4,opt,name=banding_threshold,json=bandingThreshold,proto3" json:"banding_threshold,omitempty"`    // for crop: threshold for use in banding algo
}

func (x *ModelTrust) Reset() {
	*x = ModelTrust{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelTrust) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelTrust) ProtoMessage() {}

func (x *ModelTrust) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelTrust.ProtoReflect.Descriptor instead.
func (*ModelTrust) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{2}
}

func (x *ModelTrust) GetMinDoo() float32 {
	if x != nil {
		return x.MinDoo
	}
	return 0
}

func (x *ModelTrust) GetWeedingThreshold() float32 {
	if x != nil {
		return x.WeedingThreshold
	}
	return 0
}

func (x *ModelTrust) GetThinningThreshold() float32 {
	if x != nil {
		return x.ThinningThreshold
	}
	return 0
}

func (x *ModelTrust) GetBandingThreshold() float32 {
	if x != nil {
		return x.BandingThreshold
	}
	return 0
}

type TypeCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category       string                 `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"` // This is a uuid which maps to a crop id or weed id
	Classification CategoryClassification `protobuf:"varint,2,opt,name=classification,proto3,enum=carbon.aimbot.almanac.CategoryClassification" json:"classification,omitempty"`
}

func (x *TypeCategory) Reset() {
	*x = TypeCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeCategory) ProtoMessage() {}

func (x *TypeCategory) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeCategory.ProtoReflect.Descriptor instead.
func (*TypeCategory) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{3}
}

func (x *TypeCategory) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *TypeCategory) GetClassification() CategoryClassification {
	if x != nil {
		return x.Classification
	}
	return CategoryClassification_CATEGORY_WEED
}

type AlmanacTypeCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     *TypeCategory `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Sizes    []float32     `protobuf:"fixed32,2,rep,packed,name=sizes,proto3" json:"sizes,omitempty"` //Each size goes from [sizes[i-1], sizes[i]) for i=0 we use [0, sizes[i]) additionally we add [sizes[N], inf) to the end so for 3 categories only need 2 values
	Formulas []*Formula    `protobuf:"bytes,3,rep,name=formulas,proto3" json:"formulas,omitempty"`    // len(formulas) == len(sizes) +1
}

func (x *AlmanacTypeCategory) Reset() {
	*x = AlmanacTypeCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlmanacTypeCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlmanacTypeCategory) ProtoMessage() {}

func (x *AlmanacTypeCategory) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlmanacTypeCategory.ProtoReflect.Descriptor instead.
func (*AlmanacTypeCategory) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{4}
}

func (x *AlmanacTypeCategory) GetType() *TypeCategory {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *AlmanacTypeCategory) GetSizes() []float32 {
	if x != nil {
		return x.Sizes
	}
	return nil
}

func (x *AlmanacTypeCategory) GetFormulas() []*Formula {
	if x != nil {
		return x.Formulas
	}
	return nil
}

type DiscriminatorTypeCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   *TypeCategory `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Trusts []*Trust      `protobuf:"bytes,2,rep,name=trusts,proto3" json:"trusts,omitempty"`
}

func (x *DiscriminatorTypeCategory) Reset() {
	*x = DiscriminatorTypeCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscriminatorTypeCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscriminatorTypeCategory) ProtoMessage() {}

func (x *DiscriminatorTypeCategory) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscriminatorTypeCategory.ProtoReflect.Descriptor instead.
func (*DiscriminatorTypeCategory) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{5}
}

func (x *DiscriminatorTypeCategory) GetType() *TypeCategory {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *DiscriminatorTypeCategory) GetTrusts() []*Trust {
	if x != nil {
		return x.Trusts
	}
	return nil
}

type ModelinatorTypeCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   *TypeCategory `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Trusts []*ModelTrust `protobuf:"bytes,2,rep,name=trusts,proto3" json:"trusts,omitempty"`
}

func (x *ModelinatorTypeCategory) Reset() {
	*x = ModelinatorTypeCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelinatorTypeCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelinatorTypeCategory) ProtoMessage() {}

func (x *ModelinatorTypeCategory) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelinatorTypeCategory.ProtoReflect.Descriptor instead.
func (*ModelinatorTypeCategory) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{6}
}

func (x *ModelinatorTypeCategory) GetType() *TypeCategory {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ModelinatorTypeCategory) GetTrusts() []*ModelTrust {
	if x != nil {
		return x.Trusts
	}
	return nil
}

type AlmanacConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // auto generated uuid
	Name       string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Protected  bool                   `protobuf:"varint,3,opt,name=protected,proto3" json:"protected,omitempty"`
	UpdatedTs  uint64                 `protobuf:"varint,4,opt,name=updated_ts,json=updatedTs,proto3" json:"updated_ts,omitempty"`
	Categories []*AlmanacTypeCategory `protobuf:"bytes,5,rep,name=categories,proto3" json:"categories,omitempty"` //Must contain an instance with category="DEFAULT"
}

func (x *AlmanacConfig) Reset() {
	*x = AlmanacConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlmanacConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlmanacConfig) ProtoMessage() {}

func (x *AlmanacConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlmanacConfig.ProtoReflect.Descriptor instead.
func (*AlmanacConfig) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{7}
}

func (x *AlmanacConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AlmanacConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AlmanacConfig) GetProtected() bool {
	if x != nil {
		return x.Protected
	}
	return false
}

func (x *AlmanacConfig) GetUpdatedTs() uint64 {
	if x != nil {
		return x.UpdatedTs
	}
	return 0
}

func (x *AlmanacConfig) GetCategories() []*AlmanacTypeCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type DiscriminatorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // auto generated uuid
	Name       string                       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Protected  bool                         `protobuf:"varint,3,opt,name=protected,proto3" json:"protected,omitempty"`
	UpdatedTs  uint64                       `protobuf:"varint,4,opt,name=updated_ts,json=updatedTs,proto3" json:"updated_ts,omitempty"`
	Categories []*DiscriminatorTypeCategory `protobuf:"bytes,5,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *DiscriminatorConfig) Reset() {
	*x = DiscriminatorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscriminatorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscriminatorConfig) ProtoMessage() {}

func (x *DiscriminatorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscriminatorConfig.ProtoReflect.Descriptor instead.
func (*DiscriminatorConfig) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{8}
}

func (x *DiscriminatorConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DiscriminatorConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DiscriminatorConfig) GetProtected() bool {
	if x != nil {
		return x.Protected
	}
	return false
}

func (x *DiscriminatorConfig) GetUpdatedTs() uint64 {
	if x != nil {
		return x.UpdatedTs
	}
	return 0
}

func (x *DiscriminatorConfig) GetCategories() []*DiscriminatorTypeCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type ModelinatorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId    string                     `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"` // must match an existing model id
	CropId     string                     `protobuf:"bytes,2,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`    // must match an existing crop id
	Modified   bool                       `protobuf:"varint,3,opt,name=modified,proto3" json:"modified,omitempty"`             // Set if has been modifed by user from model suggestion
	Categories []*ModelinatorTypeCategory `protobuf:"bytes,4,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *ModelinatorConfig) Reset() {
	*x = ModelinatorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_almanac_almanac_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelinatorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelinatorConfig) ProtoMessage() {}

func (x *ModelinatorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_almanac_almanac_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelinatorConfig.ProtoReflect.Descriptor instead.
func (*ModelinatorConfig) Descriptor() ([]byte, []int) {
	return file_proto_almanac_almanac_proto_rawDescGZIP(), []int{9}
}

func (x *ModelinatorConfig) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelinatorConfig) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *ModelinatorConfig) GetModified() bool {
	if x != nil {
		return x.Modified
	}
	return false
}

func (x *ModelinatorConfig) GetCategories() []*ModelinatorTypeCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_proto_almanac_almanac_proto protoreflect.FileDescriptor

var file_proto_almanac_almanac_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2f,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x22, 0xe3, 0x01, 0x0a, 0x07, 0x46, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x75, 0x6e,
	0x65, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x12, 0x66, 0x69, 0x6e, 0x65, 0x54, 0x75, 0x6e, 0x65, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x37, 0x0a, 0x18, 0x66, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x15, 0x66, 0x69, 0x6e, 0x65, 0x54, 0x75, 0x6e, 0x65, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x22, 0x3b, 0x0a, 0x05, 0x54, 0x72,
	0x75, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x54, 0x72, 0x75, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x6f,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x44, 0x6f, 0x6f, 0x12,
	0x2b, 0x0a, 0x11, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2d, 0x0a, 0x12,
	0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x0c, 0x54, 0x79, 0x70,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x55, 0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa0, 0x01, 0x0a,
	0x13, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x73, 0x69,
	0x7a, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x46, 0x6f,
	0x72, 0x6d, 0x75, 0x6c, 0x61, 0x52, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x73, 0x22,
	0x8a, 0x01, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x74, 0x72, 0x75, 0x73, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x54,
	0x72, 0x75, 0x73, 0x74, 0x52, 0x06, 0x74, 0x72, 0x75, 0x73, 0x74, 0x73, 0x22, 0x8d, 0x01, 0x0a,
	0x17, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x39, 0x0a, 0x06, 0x74, 0x72, 0x75, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54,
	0x72, 0x75, 0x73, 0x74, 0x52, 0x06, 0x74, 0x72, 0x75, 0x73, 0x74, 0x73, 0x22, 0xbc, 0x01, 0x0a,
	0x0d, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x73, 0x12,
	0x4a, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x41, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x63, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x13,
	0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x73, 0x12, 0x50, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x4e, 0x0a, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e,
	0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x2a, 0x3e, 0x0a, 0x16,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x01, 0x42, 0x0f, 0x5a, 0x0d,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_almanac_almanac_proto_rawDescOnce sync.Once
	file_proto_almanac_almanac_proto_rawDescData = file_proto_almanac_almanac_proto_rawDesc
)

func file_proto_almanac_almanac_proto_rawDescGZIP() []byte {
	file_proto_almanac_almanac_proto_rawDescOnce.Do(func() {
		file_proto_almanac_almanac_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_almanac_almanac_proto_rawDescData)
	})
	return file_proto_almanac_almanac_proto_rawDescData
}

var file_proto_almanac_almanac_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_almanac_almanac_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proto_almanac_almanac_proto_goTypes = []interface{}{
	(CategoryClassification)(0),       // 0: carbon.aimbot.almanac.CategoryClassification
	(*Formula)(nil),                   // 1: carbon.aimbot.almanac.Formula
	(*Trust)(nil),                     // 2: carbon.aimbot.almanac.Trust
	(*ModelTrust)(nil),                // 3: carbon.aimbot.almanac.ModelTrust
	(*TypeCategory)(nil),              // 4: carbon.aimbot.almanac.TypeCategory
	(*AlmanacTypeCategory)(nil),       // 5: carbon.aimbot.almanac.AlmanacTypeCategory
	(*DiscriminatorTypeCategory)(nil), // 6: carbon.aimbot.almanac.DiscriminatorTypeCategory
	(*ModelinatorTypeCategory)(nil),   // 7: carbon.aimbot.almanac.ModelinatorTypeCategory
	(*AlmanacConfig)(nil),             // 8: carbon.aimbot.almanac.AlmanacConfig
	(*DiscriminatorConfig)(nil),       // 9: carbon.aimbot.almanac.DiscriminatorConfig
	(*ModelinatorConfig)(nil),         // 10: carbon.aimbot.almanac.ModelinatorConfig
}
var file_proto_almanac_almanac_proto_depIdxs = []int32{
	0,  // 0: carbon.aimbot.almanac.TypeCategory.classification:type_name -> carbon.aimbot.almanac.CategoryClassification
	4,  // 1: carbon.aimbot.almanac.AlmanacTypeCategory.type:type_name -> carbon.aimbot.almanac.TypeCategory
	1,  // 2: carbon.aimbot.almanac.AlmanacTypeCategory.formulas:type_name -> carbon.aimbot.almanac.Formula
	4,  // 3: carbon.aimbot.almanac.DiscriminatorTypeCategory.type:type_name -> carbon.aimbot.almanac.TypeCategory
	2,  // 4: carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts:type_name -> carbon.aimbot.almanac.Trust
	4,  // 5: carbon.aimbot.almanac.ModelinatorTypeCategory.type:type_name -> carbon.aimbot.almanac.TypeCategory
	3,  // 6: carbon.aimbot.almanac.ModelinatorTypeCategory.trusts:type_name -> carbon.aimbot.almanac.ModelTrust
	5,  // 7: carbon.aimbot.almanac.AlmanacConfig.categories:type_name -> carbon.aimbot.almanac.AlmanacTypeCategory
	6,  // 8: carbon.aimbot.almanac.DiscriminatorConfig.categories:type_name -> carbon.aimbot.almanac.DiscriminatorTypeCategory
	7,  // 9: carbon.aimbot.almanac.ModelinatorConfig.categories:type_name -> carbon.aimbot.almanac.ModelinatorTypeCategory
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proto_almanac_almanac_proto_init() }
func file_proto_almanac_almanac_proto_init() {
	if File_proto_almanac_almanac_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_almanac_almanac_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Formula); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Trust); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelTrust); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlmanacTypeCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscriminatorTypeCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelinatorTypeCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlmanacConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscriminatorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_almanac_almanac_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelinatorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_almanac_almanac_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_almanac_almanac_proto_goTypes,
		DependencyIndexes: file_proto_almanac_almanac_proto_depIdxs,
		EnumInfos:         file_proto_almanac_almanac_proto_enumTypes,
		MessageInfos:      file_proto_almanac_almanac_proto_msgTypes,
	}.Build()
	File_proto_almanac_almanac_proto = out.File
	file_proto_almanac_almanac_proto_rawDesc = nil
	file_proto_almanac_almanac_proto_goTypes = nil
	file_proto_almanac_almanac_proto_depIdxs = nil
}

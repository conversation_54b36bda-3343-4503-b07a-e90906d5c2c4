// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/logging/logging.proto

package logging

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LogLevel int32

const (
	LogLevel_TRACE   LogLevel = 0
	LogLevel_DEBUG   LogLevel = 1
	LogLevel_INFO    LogLevel = 2
	LogLevel_WARNING LogLevel = 3
	LogLevel_ERROR   LogLevel = 4
	LogLevel_FATAL   LogLevel = 5
	LogLevel_PANIC   LogLevel = 6
)

// Enum value maps for LogLevel.
var (
	LogLevel_name = map[int32]string{
		0: "TRACE",
		1: "DEBUG",
		2: "INFO",
		3: "WARNING",
		4: "ERROR",
		5: "FATAL",
		6: "PANIC",
	}
	LogLevel_value = map[string]int32{
		"TRACE":   0,
		"DEBUG":   1,
		"INFO":    2,
		"WARNING": 3,
		"ERROR":   4,
		"FATAL":   5,
		"PANIC":   6,
	}
)

func (x LogLevel) Enum() *LogLevel {
	p := new(LogLevel)
	*p = x
	return p
}

func (x LogLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_logging_logging_proto_enumTypes[0].Descriptor()
}

func (LogLevel) Type() protoreflect.EnumType {
	return &file_proto_logging_logging_proto_enumTypes[0]
}

func (x LogLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogLevel.Descriptor instead.
func (LogLevel) EnumDescriptor() ([]byte, []int) {
	return file_proto_logging_logging_proto_rawDescGZIP(), []int{0}
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_logging_logging_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_logging_logging_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_logging_logging_proto_rawDescGZIP(), []int{0}
}

type SetLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogLevel LogLevel `protobuf:"varint,1,opt,name=logLevel,proto3,enum=carbon.logging.LogLevel" json:"logLevel,omitempty"`
}

func (x *SetLevelRequest) Reset() {
	*x = SetLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_logging_logging_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLevelRequest) ProtoMessage() {}

func (x *SetLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_logging_logging_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLevelRequest.ProtoReflect.Descriptor instead.
func (*SetLevelRequest) Descriptor() ([]byte, []int) {
	return file_proto_logging_logging_proto_rawDescGZIP(), []int{1}
}

func (x *SetLevelRequest) GetLogLevel() LogLevel {
	if x != nil {
		return x.LogLevel
	}
	return LogLevel_TRACE
}

var File_proto_logging_logging_proto protoreflect.FileDescriptor

var file_proto_logging_logging_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x22, 0x07, 0x0a,
	0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x47, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x6c, 0x6f, 0x67,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x6f, 0x67,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x2a,
	0x58, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x09, 0x0a, 0x05, 0x54,
	0x52, 0x41, 0x43, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x45, 0x42, 0x55, 0x47, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x57,
	0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x41, 0x54, 0x41, 0x4c, 0x10, 0x05, 0x12, 0x09,
	0x0a, 0x05, 0x50, 0x41, 0x4e, 0x49, 0x43, 0x10, 0x06, 0x32, 0x54, 0x0a, 0x0e, 0x4c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x53,
	0x65, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42,
	0x11, 0x48, 0x03, 0x5a, 0x0d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_logging_logging_proto_rawDescOnce sync.Once
	file_proto_logging_logging_proto_rawDescData = file_proto_logging_logging_proto_rawDesc
)

func file_proto_logging_logging_proto_rawDescGZIP() []byte {
	file_proto_logging_logging_proto_rawDescOnce.Do(func() {
		file_proto_logging_logging_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_logging_logging_proto_rawDescData)
	})
	return file_proto_logging_logging_proto_rawDescData
}

var file_proto_logging_logging_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_logging_logging_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_logging_logging_proto_goTypes = []interface{}{
	(LogLevel)(0),           // 0: carbon.logging.LogLevel
	(*Empty)(nil),           // 1: carbon.logging.Empty
	(*SetLevelRequest)(nil), // 2: carbon.logging.SetLevelRequest
}
var file_proto_logging_logging_proto_depIdxs = []int32{
	0, // 0: carbon.logging.SetLevelRequest.logLevel:type_name -> carbon.logging.LogLevel
	2, // 1: carbon.logging.LoggingService.SetLevel:input_type -> carbon.logging.SetLevelRequest
	1, // 2: carbon.logging.LoggingService.SetLevel:output_type -> carbon.logging.Empty
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_logging_logging_proto_init() }
func file_proto_logging_logging_proto_init() {
	if File_proto_logging_logging_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_logging_logging_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_logging_logging_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_logging_logging_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_logging_logging_proto_goTypes,
		DependencyIndexes: file_proto_logging_logging_proto_depIdxs,
		EnumInfos:         file_proto_logging_logging_proto_enumTypes,
		MessageInfos:      file_proto_logging_logging_proto_msgTypes,
	}.Build()
	File_proto_logging_logging_proto = out.File
	file_proto_logging_logging_proto_rawDesc = nil
	file_proto_logging_logging_proto_goTypes = nil
	file_proto_logging_logging_proto_depIdxs = nil
}

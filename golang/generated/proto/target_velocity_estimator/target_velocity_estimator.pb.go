// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/target_velocity_estimator/target_velocity_estimator.proto

package target_velocity_estimator

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TVERowProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryKillRate   float32 `protobuf:"fixed32,1,opt,name=primary_kill_rate,json=primaryKillRate,proto3" json:"primary_kill_rate,omitempty"`
	SecondaryKillRate float32 `protobuf:"fixed32,2,opt,name=secondary_kill_rate,json=secondaryKillRate,proto3" json:"secondary_kill_rate,omitempty"`
}

func (x *TVERowProfile) Reset() {
	*x = TVERowProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TVERowProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TVERowProfile) ProtoMessage() {}

func (x *TVERowProfile) ProtoReflect() protoreflect.Message {
	mi := &file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TVERowProfile.ProtoReflect.Descriptor instead.
func (*TVERowProfile) Descriptor() ([]byte, []int) {
	return file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescGZIP(), []int{0}
}

func (x *TVERowProfile) GetPrimaryKillRate() float32 {
	if x != nil {
		return x.PrimaryKillRate
	}
	return 0
}

func (x *TVERowProfile) GetSecondaryKillRate() float32 {
	if x != nil {
		return x.SecondaryKillRate
	}
	return 0
}

type TVEProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                string                    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	UpdateTs            int64                     `protobuf:"varint,3,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Protected           bool                      `protobuf:"varint,4,opt,name=protected,proto3" json:"protected,omitempty"`
	CruiseOffsetPercent float32                   `protobuf:"fixed32,5,opt,name=cruise_offset_percent,json=cruiseOffsetPercent,proto3" json:"cruise_offset_percent,omitempty"`
	PrimaryRange        float32                   `protobuf:"fixed32,6,opt,name=primary_range,json=primaryRange,proto3" json:"primary_range,omitempty"`
	SecondaryRange      float32                   `protobuf:"fixed32,7,opt,name=secondary_range,json=secondaryRange,proto3" json:"secondary_range,omitempty"`
	IncreaseSmoothing   float32                   `protobuf:"fixed32,8,opt,name=increase_smoothing,json=increaseSmoothing,proto3" json:"increase_smoothing,omitempty"`
	DecreaseSmoothing   float32                   `protobuf:"fixed32,9,opt,name=decrease_smoothing,json=decreaseSmoothing,proto3" json:"decrease_smoothing,omitempty"`
	Row_1               *TVERowProfile            `protobuf:"bytes,10,opt,name=row_1,json=row1,proto3" json:"row_1,omitempty"`
	Row_2               *TVERowProfile            `protobuf:"bytes,11,opt,name=row_2,json=row2,proto3" json:"row_2,omitempty"`
	Row_3               *TVERowProfile            `protobuf:"bytes,12,opt,name=row_3,json=row3,proto3" json:"row_3,omitempty"`
	Rows                map[uint32]*TVERowProfile `protobuf:"bytes,13,rep,name=rows,proto3" json:"rows,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MinVelMph           float32                   `protobuf:"fixed32,14,opt,name=min_vel_mph,json=minVelMph,proto3" json:"min_vel_mph,omitempty"`
	MaxVelMph           float32                   `protobuf:"fixed32,15,opt,name=max_vel_mph,json=maxVelMph,proto3" json:"max_vel_mph,omitempty"`
}

func (x *TVEProfile) Reset() {
	*x = TVEProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TVEProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TVEProfile) ProtoMessage() {}

func (x *TVEProfile) ProtoReflect() protoreflect.Message {
	mi := &file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TVEProfile.ProtoReflect.Descriptor instead.
func (*TVEProfile) Descriptor() ([]byte, []int) {
	return file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescGZIP(), []int{1}
}

func (x *TVEProfile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TVEProfile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TVEProfile) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *TVEProfile) GetProtected() bool {
	if x != nil {
		return x.Protected
	}
	return false
}

func (x *TVEProfile) GetCruiseOffsetPercent() float32 {
	if x != nil {
		return x.CruiseOffsetPercent
	}
	return 0
}

func (x *TVEProfile) GetPrimaryRange() float32 {
	if x != nil {
		return x.PrimaryRange
	}
	return 0
}

func (x *TVEProfile) GetSecondaryRange() float32 {
	if x != nil {
		return x.SecondaryRange
	}
	return 0
}

func (x *TVEProfile) GetIncreaseSmoothing() float32 {
	if x != nil {
		return x.IncreaseSmoothing
	}
	return 0
}

func (x *TVEProfile) GetDecreaseSmoothing() float32 {
	if x != nil {
		return x.DecreaseSmoothing
	}
	return 0
}

func (x *TVEProfile) GetRow_1() *TVERowProfile {
	if x != nil {
		return x.Row_1
	}
	return nil
}

func (x *TVEProfile) GetRow_2() *TVERowProfile {
	if x != nil {
		return x.Row_2
	}
	return nil
}

func (x *TVEProfile) GetRow_3() *TVERowProfile {
	if x != nil {
		return x.Row_3
	}
	return nil
}

func (x *TVEProfile) GetRows() map[uint32]*TVERowProfile {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *TVEProfile) GetMinVelMph() float32 {
	if x != nil {
		return x.MinVelMph
	}
	return 0
}

func (x *TVEProfile) GetMaxVelMph() float32 {
	if x != nil {
		return x.MaxVelMph
	}
	return 0
}

type ProfileDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ProfileDetails) Reset() {
	*x = ProfileDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileDetails) ProtoMessage() {}

func (x *ProfileDetails) ProtoReflect() protoreflect.Message {
	mi := &file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileDetails.ProtoReflect.Descriptor instead.
func (*ProfileDetails) Descriptor() ([]byte, []int) {
	return file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescGZIP(), []int{2}
}

func (x *ProfileDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProfileDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proto_target_velocity_estimator_target_velocity_estimator_proto protoreflect.FileDescriptor

var file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x27, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x6b, 0x0a, 0x0d, 0x54, 0x56,
	0x45, 0x52, 0x6f, 0x77, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x69, 0x6c, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x4b,
	0x69, 0x6c, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x22, 0xb6, 0x06, 0x0a, 0x0a, 0x54, 0x56, 0x45, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x4f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0c, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x72, 0x65,
	0x61, 0x73, 0x65, 0x5f, 0x73, 0x6d, 0x6f, 0x6f, 0x74, 0x68, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x53, 0x6d, 0x6f,
	0x6f, 0x74, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x12, 0x64, 0x65, 0x63, 0x72, 0x65, 0x61,
	0x73, 0x65, 0x5f, 0x73, 0x6d, 0x6f, 0x6f, 0x74, 0x68, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x11, 0x64, 0x65, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x53, 0x6d, 0x6f, 0x6f,
	0x74, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x05, 0x72, 0x6f, 0x77, 0x5f, 0x31, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54,
	0x56, 0x45, 0x52, 0x6f, 0x77, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f,
	0x77, 0x31, 0x12, 0x4b, 0x0a, 0x05, 0x72, 0x6f, 0x77, 0x5f, 0x32, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x52,
	0x6f, 0x77, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x32, 0x12,
	0x4b, 0x0a, 0x05, 0x72, 0x6f, 0x77, 0x5f, 0x33, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x52, 0x6f, 0x77, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x33, 0x12, 0x51, 0x0a, 0x04,
	0x72, 0x6f, 0x77, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x52, 0x6f, 0x77, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x12,
	0x1e, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x56, 0x65, 0x6c, 0x4d, 0x70, 0x68, 0x12,
	0x1e, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x56, 0x65, 0x6c, 0x4d, 0x70, 0x68, 0x1a,
	0x6f, 0x0a, 0x09, 0x52, 0x6f, 0x77, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4c,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x56, 0x45, 0x52, 0x6f, 0x77, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x34, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x21, 0x5a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescOnce sync.Once
	file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescData = file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDesc
)

func file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescGZIP() []byte {
	file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescOnce.Do(func() {
		file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescData)
	})
	return file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDescData
}

var file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_target_velocity_estimator_target_velocity_estimator_proto_goTypes = []interface{}{
	(*TVERowProfile)(nil),  // 0: carbon.aimbot.target_velocity_estimator.TVERowProfile
	(*TVEProfile)(nil),     // 1: carbon.aimbot.target_velocity_estimator.TVEProfile
	(*ProfileDetails)(nil), // 2: carbon.aimbot.target_velocity_estimator.ProfileDetails
	nil,                    // 3: carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry
}
var file_proto_target_velocity_estimator_target_velocity_estimator_proto_depIdxs = []int32{
	0, // 0: carbon.aimbot.target_velocity_estimator.TVEProfile.row_1:type_name -> carbon.aimbot.target_velocity_estimator.TVERowProfile
	0, // 1: carbon.aimbot.target_velocity_estimator.TVEProfile.row_2:type_name -> carbon.aimbot.target_velocity_estimator.TVERowProfile
	0, // 2: carbon.aimbot.target_velocity_estimator.TVEProfile.row_3:type_name -> carbon.aimbot.target_velocity_estimator.TVERowProfile
	3, // 3: carbon.aimbot.target_velocity_estimator.TVEProfile.rows:type_name -> carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry
	0, // 4: carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry.value:type_name -> carbon.aimbot.target_velocity_estimator.TVERowProfile
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_target_velocity_estimator_target_velocity_estimator_proto_init() }
func file_proto_target_velocity_estimator_target_velocity_estimator_proto_init() {
	if File_proto_target_velocity_estimator_target_velocity_estimator_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TVERowProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TVEProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_target_velocity_estimator_target_velocity_estimator_proto_goTypes,
		DependencyIndexes: file_proto_target_velocity_estimator_target_velocity_estimator_proto_depIdxs,
		MessageInfos:      file_proto_target_velocity_estimator_target_velocity_estimator_proto_msgTypes,
	}.Build()
	File_proto_target_velocity_estimator_target_velocity_estimator_proto = out.File
	file_proto_target_velocity_estimator_target_velocity_estimator_proto_rawDesc = nil
	file_proto_target_velocity_estimator_target_velocity_estimator_proto_goTypes = nil
	file_proto_target_velocity_estimator_target_velocity_estimator_proto_depIdxs = nil
}

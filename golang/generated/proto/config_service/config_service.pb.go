// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: config/api/proto/config_service.proto

package config_service

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfigType int32

const (
	ConfigType_NODE   ConfigType = 0
	ConfigType_LIST   ConfigType = 1
	ConfigType_STRING ConfigType = 2
	ConfigType_INT    ConfigType = 3
	ConfigType_UINT   ConfigType = 4
	ConfigType_FLOAT  ConfigType = 5
	ConfigType_BOOL   ConfigType = 6
)

// Enum value maps for ConfigType.
var (
	ConfigType_name = map[int32]string{
		0: "NODE",
		1: "LIST",
		2: "STRING",
		3: "INT",
		4: "UINT",
		5: "FLOAT",
		6: "BOOL",
	}
	ConfigType_value = map[string]int32{
		"NODE":   0,
		"LIST":   1,
		"STRING": 2,
		"INT":    3,
		"UINT":   4,
		"FLOAT":  5,
		"BOOL":   6,
	}
)

func (x ConfigType) Enum() *ConfigType {
	p := new(ConfigType)
	*p = x
	return p
}

func (x ConfigType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigType) Descriptor() protoreflect.EnumDescriptor {
	return file_config_api_proto_config_service_proto_enumTypes[0].Descriptor()
}

func (ConfigType) Type() protoreflect.EnumType {
	return &file_config_api_proto_config_service_proto_enumTypes[0]
}

func (x ConfigType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigType.Descriptor instead.
func (ConfigType) EnumDescriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{0}
}

type ConfigComplexity int32

const (
	ConfigComplexity_USER      ConfigComplexity = 0
	ConfigComplexity_ADVANCED  ConfigComplexity = 1
	ConfigComplexity_EXPERT    ConfigComplexity = 2
	ConfigComplexity_DEVELOPER ConfigComplexity = 3
)

// Enum value maps for ConfigComplexity.
var (
	ConfigComplexity_name = map[int32]string{
		0: "USER",
		1: "ADVANCED",
		2: "EXPERT",
		3: "DEVELOPER",
	}
	ConfigComplexity_value = map[string]int32{
		"USER":      0,
		"ADVANCED":  1,
		"EXPERT":    2,
		"DEVELOPER": 3,
	}
)

func (x ConfigComplexity) Enum() *ConfigComplexity {
	p := new(ConfigComplexity)
	*p = x
	return p
}

func (x ConfigComplexity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigComplexity) Descriptor() protoreflect.EnumDescriptor {
	return file_config_api_proto_config_service_proto_enumTypes[1].Descriptor()
}

func (ConfigComplexity) Type() protoreflect.EnumType {
	return &file_config_api_proto_config_service_proto_enumTypes[1]
}

func (x ConfigComplexity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigComplexity.Descriptor instead.
func (ConfigComplexity) EnumDescriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{1}
}

type PingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{0}
}

func (x *PingRequest) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PongResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PongResponse) Reset() {
	*x = PongResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PongResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PongResponse) ProtoMessage() {}

func (x *PongResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PongResponse.ProtoReflect.Descriptor instead.
func (*PongResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{1}
}

func (x *PongResponse) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

type ConfigValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*ConfigValue_Int64Val
	//	*ConfigValue_Uint64Val
	//	*ConfigValue_BoolVal
	//	*ConfigValue_FloatVal
	//	*ConfigValue_StringVal
	Value       isConfigValue_Value `protobuf_oneof:"value"`
	TimestampMs uint64              `protobuf:"varint,6,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *ConfigValue) Reset() {
	*x = ConfigValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigValue) ProtoMessage() {}

func (x *ConfigValue) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigValue.ProtoReflect.Descriptor instead.
func (*ConfigValue) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{2}
}

func (m *ConfigValue) GetValue() isConfigValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *ConfigValue) GetInt64Val() int64 {
	if x, ok := x.GetValue().(*ConfigValue_Int64Val); ok {
		return x.Int64Val
	}
	return 0
}

func (x *ConfigValue) GetUint64Val() uint64 {
	if x, ok := x.GetValue().(*ConfigValue_Uint64Val); ok {
		return x.Uint64Val
	}
	return 0
}

func (x *ConfigValue) GetBoolVal() bool {
	if x, ok := x.GetValue().(*ConfigValue_BoolVal); ok {
		return x.BoolVal
	}
	return false
}

func (x *ConfigValue) GetFloatVal() float64 {
	if x, ok := x.GetValue().(*ConfigValue_FloatVal); ok {
		return x.FloatVal
	}
	return 0
}

func (x *ConfigValue) GetStringVal() string {
	if x, ok := x.GetValue().(*ConfigValue_StringVal); ok {
		return x.StringVal
	}
	return ""
}

func (x *ConfigValue) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type isConfigValue_Value interface {
	isConfigValue_Value()
}

type ConfigValue_Int64Val struct {
	Int64Val int64 `protobuf:"varint,1,opt,name=int64_val,json=int64Val,proto3,oneof"`
}

type ConfigValue_Uint64Val struct {
	Uint64Val uint64 `protobuf:"varint,2,opt,name=uint64_val,json=uint64Val,proto3,oneof"`
}

type ConfigValue_BoolVal struct {
	BoolVal bool `protobuf:"varint,3,opt,name=bool_val,json=boolVal,proto3,oneof"`
}

type ConfigValue_FloatVal struct {
	FloatVal float64 `protobuf:"fixed64,4,opt,name=float_val,json=floatVal,proto3,oneof"`
}

type ConfigValue_StringVal struct {
	StringVal string `protobuf:"bytes,5,opt,name=string_val,json=stringVal,proto3,oneof"`
}

func (*ConfigValue_Int64Val) isConfigValue_Value() {}

func (*ConfigValue_Uint64Val) isConfigValue_Value() {}

func (*ConfigValue_BoolVal) isConfigValue_Value() {}

func (*ConfigValue_FloatVal) isConfigValue_Value() {}

func (*ConfigValue_StringVal) isConfigValue_Value() {}

type IntConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min  int64 `protobuf:"varint,1,opt,name=min,proto3" json:"min,omitempty"`
	Max  int64 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
	Step int64 `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`
}

func (x *IntConfigDef) Reset() {
	*x = IntConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntConfigDef) ProtoMessage() {}

func (x *IntConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntConfigDef.ProtoReflect.Descriptor instead.
func (*IntConfigDef) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{3}
}

func (x *IntConfigDef) GetMin() int64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *IntConfigDef) GetMax() int64 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *IntConfigDef) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

type UIntConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min  uint64 `protobuf:"varint,1,opt,name=min,proto3" json:"min,omitempty"`
	Max  uint64 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
	Step uint64 `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`
}

func (x *UIntConfigDef) Reset() {
	*x = UIntConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIntConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIntConfigDef) ProtoMessage() {}

func (x *UIntConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIntConfigDef.ProtoReflect.Descriptor instead.
func (*UIntConfigDef) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{4}
}

func (x *UIntConfigDef) GetMin() uint64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *UIntConfigDef) GetMax() uint64 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *UIntConfigDef) GetStep() uint64 {
	if x != nil {
		return x.Step
	}
	return 0
}

type FloatConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min  float64 `protobuf:"fixed64,1,opt,name=min,proto3" json:"min,omitempty"`
	Max  float64 `protobuf:"fixed64,2,opt,name=max,proto3" json:"max,omitempty"`
	Step float64 `protobuf:"fixed64,3,opt,name=step,proto3" json:"step,omitempty"`
}

func (x *FloatConfigDef) Reset() {
	*x = FloatConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatConfigDef) ProtoMessage() {}

func (x *FloatConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatConfigDef.ProtoReflect.Descriptor instead.
func (*FloatConfigDef) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{5}
}

func (x *FloatConfigDef) GetMin() float64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *FloatConfigDef) GetMax() float64 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *FloatConfigDef) GetStep() float64 {
	if x != nil {
		return x.Step
	}
	return 0
}

type StringConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SizeLimit uint32   `protobuf:"varint,1,opt,name=size_limit,json=sizeLimit,proto3" json:"size_limit,omitempty"`
	Choices   []string `protobuf:"bytes,3,rep,name=choices,proto3" json:"choices,omitempty"`
}

func (x *StringConfigDef) Reset() {
	*x = StringConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringConfigDef) ProtoMessage() {}

func (x *StringConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringConfigDef.ProtoReflect.Descriptor instead.
func (*StringConfigDef) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{6}
}

func (x *StringConfigDef) GetSizeLimit() uint32 {
	if x != nil {
		return x.SizeLimit
	}
	return 0
}

func (x *StringConfigDef) GetChoices() []string {
	if x != nil {
		return x.Choices
	}
	return nil
}

type ConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       ConfigType       `protobuf:"varint,1,opt,name=type,proto3,enum=carbon.config.proto.ConfigType" json:"type,omitempty"`
	Complexity ConfigComplexity `protobuf:"varint,2,opt,name=complexity,proto3,enum=carbon.config.proto.ConfigComplexity" json:"complexity,omitempty"`
	// Types that are assignable to Extra:
	//
	//	*ConfigDef_IntDef
	//	*ConfigDef_UintDef
	//	*ConfigDef_FloatDef
	//	*ConfigDef_StringDef
	Extra              isConfigDef_Extra `protobuf_oneof:"extra"`
	Hint               string            `protobuf:"bytes,7,opt,name=hint,proto3" json:"hint,omitempty"`
	DefaultRecommended bool              `protobuf:"varint,8,opt,name=default_recommended,json=defaultRecommended,proto3" json:"default_recommended,omitempty"`
	Units              string            `protobuf:"bytes,9,opt,name=units,proto3" json:"units,omitempty"`
}

func (x *ConfigDef) Reset() {
	*x = ConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigDef) ProtoMessage() {}

func (x *ConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigDef.ProtoReflect.Descriptor instead.
func (*ConfigDef) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{7}
}

func (x *ConfigDef) GetType() ConfigType {
	if x != nil {
		return x.Type
	}
	return ConfigType_NODE
}

func (x *ConfigDef) GetComplexity() ConfigComplexity {
	if x != nil {
		return x.Complexity
	}
	return ConfigComplexity_USER
}

func (m *ConfigDef) GetExtra() isConfigDef_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *ConfigDef) GetIntDef() *IntConfigDef {
	if x, ok := x.GetExtra().(*ConfigDef_IntDef); ok {
		return x.IntDef
	}
	return nil
}

func (x *ConfigDef) GetUintDef() *UIntConfigDef {
	if x, ok := x.GetExtra().(*ConfigDef_UintDef); ok {
		return x.UintDef
	}
	return nil
}

func (x *ConfigDef) GetFloatDef() *FloatConfigDef {
	if x, ok := x.GetExtra().(*ConfigDef_FloatDef); ok {
		return x.FloatDef
	}
	return nil
}

func (x *ConfigDef) GetStringDef() *StringConfigDef {
	if x, ok := x.GetExtra().(*ConfigDef_StringDef); ok {
		return x.StringDef
	}
	return nil
}

func (x *ConfigDef) GetHint() string {
	if x != nil {
		return x.Hint
	}
	return ""
}

func (x *ConfigDef) GetDefaultRecommended() bool {
	if x != nil {
		return x.DefaultRecommended
	}
	return false
}

func (x *ConfigDef) GetUnits() string {
	if x != nil {
		return x.Units
	}
	return ""
}

type isConfigDef_Extra interface {
	isConfigDef_Extra()
}

type ConfigDef_IntDef struct {
	IntDef *IntConfigDef `protobuf:"bytes,3,opt,name=int_def,json=intDef,proto3,oneof"`
}

type ConfigDef_UintDef struct {
	UintDef *UIntConfigDef `protobuf:"bytes,4,opt,name=uint_def,json=uintDef,proto3,oneof"`
}

type ConfigDef_FloatDef struct {
	FloatDef *FloatConfigDef `protobuf:"bytes,5,opt,name=float_def,json=floatDef,proto3,oneof"`
}

type ConfigDef_StringDef struct {
	StringDef *StringConfigDef `protobuf:"bytes,6,opt,name=string_def,json=stringDef,proto3,oneof"`
}

func (*ConfigDef_IntDef) isConfigDef_Extra() {}

func (*ConfigDef_UintDef) isConfigDef_Extra() {}

func (*ConfigDef_FloatDef) isConfigDef_Extra() {}

func (*ConfigDef_StringDef) isConfigDef_Extra() {}

type ConfigNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value    *ConfigValue  `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Def      *ConfigDef    `protobuf:"bytes,3,opt,name=def,proto3" json:"def,omitempty"`
	Children []*ConfigNode `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
}

func (x *ConfigNode) Reset() {
	*x = ConfigNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigNode) ProtoMessage() {}

func (x *ConfigNode) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigNode.ProtoReflect.Descriptor instead.
func (*ConfigNode) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{8}
}

func (x *ConfigNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConfigNode) GetValue() *ConfigValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ConfigNode) GetDef() *ConfigDef {
	if x != nil {
		return x.Def
	}
	return nil
}

func (x *ConfigNode) GetChildren() []*ConfigNode {
	if x != nil {
		return x.Children
	}
	return nil
}

type ConfigLeaf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string       `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value *ConfigValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ConfigLeaf) Reset() {
	*x = ConfigLeaf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigLeaf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigLeaf) ProtoMessage() {}

func (x *ConfigLeaf) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigLeaf.ProtoReflect.Descriptor instead.
func (*ConfigLeaf) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{9}
}

func (x *ConfigLeaf) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ConfigLeaf) GetValue() *ConfigValue {
	if x != nil {
		return x.Value
	}
	return nil
}

type SetValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string       `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value *ConfigValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *SetValueRequest) Reset() {
	*x = SetValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetValueRequest) ProtoMessage() {}

func (x *SetValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetValueRequest.ProtoReflect.Descriptor instead.
func (*SetValueRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{10}
}

func (x *SetValueRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SetValueRequest) GetValue() *ConfigValue {
	if x != nil {
		return x.Value
	}
	return nil
}

type SetValueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetValueResponse) Reset() {
	*x = SetValueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetValueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetValueResponse) ProtoMessage() {}

func (x *SetValueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetValueResponse.ProtoReflect.Descriptor instead.
func (*SetValueResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{11}
}

type GetTreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetTreeRequest) Reset() {
	*x = GetTreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTreeRequest) ProtoMessage() {}

func (x *GetTreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTreeRequest.ProtoReflect.Descriptor instead.
func (*GetTreeRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetTreeRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GetTreeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node *ConfigNode `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
}

func (x *GetTreeResponse) Reset() {
	*x = GetTreeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTreeResponse) ProtoMessage() {}

func (x *GetTreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTreeResponse.ProtoReflect.Descriptor instead.
func (*GetTreeResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetTreeResponse) GetNode() *ConfigNode {
	if x != nil {
		return x.Node
	}
	return nil
}

type SetTreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  string      `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Node *ConfigNode `protobuf:"bytes,2,opt,name=node,proto3" json:"node,omitempty"`
}

func (x *SetTreeRequest) Reset() {
	*x = SetTreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTreeRequest) ProtoMessage() {}

func (x *SetTreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTreeRequest.ProtoReflect.Descriptor instead.
func (*SetTreeRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{14}
}

func (x *SetTreeRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SetTreeRequest) GetNode() *ConfigNode {
	if x != nil {
		return x.Node
	}
	return nil
}

type SetTreeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetTreeResponse) Reset() {
	*x = SetTreeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTreeResponse) ProtoMessage() {}

func (x *SetTreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTreeResponse.ProtoReflect.Descriptor instead.
func (*SetTreeResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{15}
}

type GetLeavesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetLeavesRequest) Reset() {
	*x = GetLeavesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLeavesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeavesRequest) ProtoMessage() {}

func (x *GetLeavesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeavesRequest.ProtoReflect.Descriptor instead.
func (*GetLeavesRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetLeavesRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GetLeavesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Leaves []*ConfigLeaf `protobuf:"bytes,1,rep,name=leaves,proto3" json:"leaves,omitempty"`
}

func (x *GetLeavesResponse) Reset() {
	*x = GetLeavesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLeavesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeavesResponse) ProtoMessage() {}

func (x *GetLeavesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeavesResponse.ProtoReflect.Descriptor instead.
func (*GetLeavesResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetLeavesResponse) GetLeaves() []*ConfigLeaf {
	if x != nil {
		return x.Leaves
	}
	return nil
}

type AddToListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *AddToListRequest) Reset() {
	*x = AddToListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddToListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddToListRequest) ProtoMessage() {}

func (x *AddToListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddToListRequest.ProtoReflect.Descriptor instead.
func (*AddToListRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{18}
}

func (x *AddToListRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AddToListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type AddToListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddToListResponse) Reset() {
	*x = AddToListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddToListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddToListResponse) ProtoMessage() {}

func (x *AddToListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddToListResponse.ProtoReflect.Descriptor instead.
func (*AddToListResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{19}
}

type RemoveFromListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RemoveFromListRequest) Reset() {
	*x = RemoveFromListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveFromListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFromListRequest) ProtoMessage() {}

func (x *RemoveFromListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFromListRequest.ProtoReflect.Descriptor instead.
func (*RemoveFromListRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{20}
}

func (x *RemoveFromListRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *RemoveFromListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RemoveFromListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveFromListResponse) Reset() {
	*x = RemoveFromListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveFromListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFromListResponse) ProtoMessage() {}

func (x *RemoveFromListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFromListResponse.ProtoReflect.Descriptor instead.
func (*RemoveFromListResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{21}
}

type SubscriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys []string `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (x *SubscriptionRequest) Reset() {
	*x = SubscriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionRequest) ProtoMessage() {}

func (x *SubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionRequest.ProtoReflect.Descriptor instead.
func (*SubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{22}
}

func (x *SubscriptionRequest) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

type SubscriptionNotifyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubscriptionKey string `protobuf:"bytes,1,opt,name=subscription_key,json=subscriptionKey,proto3" json:"subscription_key,omitempty"`
	NotifyKey       string `protobuf:"bytes,2,opt,name=notify_key,json=notifyKey,proto3" json:"notify_key,omitempty"`
}

func (x *SubscriptionNotifyMessage) Reset() {
	*x = SubscriptionNotifyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionNotifyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionNotifyMessage) ProtoMessage() {}

func (x *SubscriptionNotifyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionNotifyMessage.ProtoReflect.Descriptor instead.
func (*SubscriptionNotifyMessage) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{23}
}

func (x *SubscriptionNotifyMessage) GetSubscriptionKey() string {
	if x != nil {
		return x.SubscriptionKey
	}
	return ""
}

func (x *SubscriptionNotifyMessage) GetNotifyKey() string {
	if x != nil {
		return x.NotifyKey
	}
	return ""
}

type UpgradeCloudConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot        string `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	ToTemplate   string `protobuf:"bytes,2,opt,name=to_template,json=toTemplate,proto3" json:"to_template,omitempty"`
	FromTemplate string `protobuf:"bytes,3,opt,name=from_template,json=fromTemplate,proto3" json:"from_template,omitempty"`
}

func (x *UpgradeCloudConfigRequest) Reset() {
	*x = UpgradeCloudConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeCloudConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeCloudConfigRequest) ProtoMessage() {}

func (x *UpgradeCloudConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeCloudConfigRequest.ProtoReflect.Descriptor instead.
func (*UpgradeCloudConfigRequest) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{24}
}

func (x *UpgradeCloudConfigRequest) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

func (x *UpgradeCloudConfigRequest) GetToTemplate() string {
	if x != nil {
		return x.ToTemplate
	}
	return ""
}

func (x *UpgradeCloudConfigRequest) GetFromTemplate() string {
	if x != nil {
		return x.FromTemplate
	}
	return ""
}

type UpgradeCloudConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpgradeCloudConfigResponse) Reset() {
	*x = UpgradeCloudConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_api_proto_config_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeCloudConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeCloudConfigResponse) ProtoMessage() {}

func (x *UpgradeCloudConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_config_api_proto_config_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeCloudConfigResponse.ProtoReflect.Descriptor instead.
func (*UpgradeCloudConfigResponse) Descriptor() ([]byte, []int) {
	return file_config_api_proto_config_service_proto_rawDescGZIP(), []int{25}
}

var File_config_api_proto_config_service_proto protoreflect.FileDescriptor

var file_config_api_proto_config_service_proto_rawDesc = []byte{
	0x0a, 0x25, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1b, 0x0a, 0x0b,
	0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x22, 0x1c, 0x0a, 0x0c, 0x50, 0x6f, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x22, 0xd6, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0a, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x09, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x6c, 0x5f,
	0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x62, 0x6f, 0x6f,
	0x6c, 0x56, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x56, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x46, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d,
	0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6d, 0x61, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0x47, 0x0a, 0x0d, 0x55, 0x49, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x74, 0x65,
	0x70, 0x22, 0x48, 0x0a, 0x0e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x44, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0x4a, 0x0a, 0x0f, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22, 0xf5, 0x03, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x65, 0x66, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x78, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x78, 0x69, 0x74, 0x79, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x78, 0x69, 0x74,
	0x79, 0x12, 0x3c, 0x0a, 0x07, 0x69, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12,
	0x3f, 0x0a, 0x08, 0x75, 0x69, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x75, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x66,
	0x12, 0x42, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x44, 0x65, 0x66, 0x12, 0x45, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x48, 0x00,
	0x52, 0x09, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x69, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x69, 0x6e, 0x74, 0x12,
	0x2f, 0x0a, 0x13, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22,
	0xc7, 0x01, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x30, 0x0a, 0x03, 0x64, 0x65,
	0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x52, 0x03, 0x64, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x08,
	0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x22, 0x56, 0x0a, 0x0a, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4c, 0x65, 0x61, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x5b, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x12,
	0x0a, 0x10, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x22, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x46, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x6e, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x22, 0x57,
	0x0a, 0x0e, 0x53, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x33, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x54, 0x72,
	0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x22, 0x4c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x4c, 0x65, 0x61, 0x66, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x22, 0x38,
	0x0a, 0x10, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x54,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3d, 0x0a,
	0x15, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x18, 0x0a, 0x16,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79,
	0x73, 0x22, 0x65, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x29,
	0x0a, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x4b, 0x65, 0x79, 0x22, 0x77, 0x0a, 0x19, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x6f, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x22, 0x1c, 0x0a, 0x1a, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a,
	0x54, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a,
	0x04, 0x4e, 0x4f, 0x44, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x49, 0x53, 0x54, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x07, 0x0a,
	0x03, 0x49, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x49, 0x4e, 0x54, 0x10, 0x04,
	0x12, 0x09, 0x0a, 0x05, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x42,
	0x4f, 0x4f, 0x4c, 0x10, 0x06, 0x2a, 0x45, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x78, 0x69, 0x74, 0x79, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x53, 0x45,
	0x52, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x58, 0x50, 0x45, 0x52, 0x54, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52, 0x10, 0x03, 0x32, 0xfb, 0x05, 0x0a,
	0x0d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4b,
	0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x08, 0x53,
	0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x12,
	0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x07, 0x53, 0x65,
	0x74, 0x54, 0x72, 0x65, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x74, 0x54,
	0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x53, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5a, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x12, 0x25, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x09,
	0x41, 0x64, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x41, 0x64, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x12, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x84, 0x01, 0x0a, 0x19, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x30,
	0x01, 0x42, 0x16, 0x5a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_config_api_proto_config_service_proto_rawDescOnce sync.Once
	file_config_api_proto_config_service_proto_rawDescData = file_config_api_proto_config_service_proto_rawDesc
)

func file_config_api_proto_config_service_proto_rawDescGZIP() []byte {
	file_config_api_proto_config_service_proto_rawDescOnce.Do(func() {
		file_config_api_proto_config_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_config_api_proto_config_service_proto_rawDescData)
	})
	return file_config_api_proto_config_service_proto_rawDescData
}

var file_config_api_proto_config_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_config_api_proto_config_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_config_api_proto_config_service_proto_goTypes = []interface{}{
	(ConfigType)(0),                    // 0: carbon.config.proto.ConfigType
	(ConfigComplexity)(0),              // 1: carbon.config.proto.ConfigComplexity
	(*PingRequest)(nil),                // 2: carbon.config.proto.PingRequest
	(*PongResponse)(nil),               // 3: carbon.config.proto.PongResponse
	(*ConfigValue)(nil),                // 4: carbon.config.proto.ConfigValue
	(*IntConfigDef)(nil),               // 5: carbon.config.proto.IntConfigDef
	(*UIntConfigDef)(nil),              // 6: carbon.config.proto.UIntConfigDef
	(*FloatConfigDef)(nil),             // 7: carbon.config.proto.FloatConfigDef
	(*StringConfigDef)(nil),            // 8: carbon.config.proto.StringConfigDef
	(*ConfigDef)(nil),                  // 9: carbon.config.proto.ConfigDef
	(*ConfigNode)(nil),                 // 10: carbon.config.proto.ConfigNode
	(*ConfigLeaf)(nil),                 // 11: carbon.config.proto.ConfigLeaf
	(*SetValueRequest)(nil),            // 12: carbon.config.proto.SetValueRequest
	(*SetValueResponse)(nil),           // 13: carbon.config.proto.SetValueResponse
	(*GetTreeRequest)(nil),             // 14: carbon.config.proto.GetTreeRequest
	(*GetTreeResponse)(nil),            // 15: carbon.config.proto.GetTreeResponse
	(*SetTreeRequest)(nil),             // 16: carbon.config.proto.SetTreeRequest
	(*SetTreeResponse)(nil),            // 17: carbon.config.proto.SetTreeResponse
	(*GetLeavesRequest)(nil),           // 18: carbon.config.proto.GetLeavesRequest
	(*GetLeavesResponse)(nil),          // 19: carbon.config.proto.GetLeavesResponse
	(*AddToListRequest)(nil),           // 20: carbon.config.proto.AddToListRequest
	(*AddToListResponse)(nil),          // 21: carbon.config.proto.AddToListResponse
	(*RemoveFromListRequest)(nil),      // 22: carbon.config.proto.RemoveFromListRequest
	(*RemoveFromListResponse)(nil),     // 23: carbon.config.proto.RemoveFromListResponse
	(*SubscriptionRequest)(nil),        // 24: carbon.config.proto.SubscriptionRequest
	(*SubscriptionNotifyMessage)(nil),  // 25: carbon.config.proto.SubscriptionNotifyMessage
	(*UpgradeCloudConfigRequest)(nil),  // 26: carbon.config.proto.UpgradeCloudConfigRequest
	(*UpgradeCloudConfigResponse)(nil), // 27: carbon.config.proto.UpgradeCloudConfigResponse
}
var file_config_api_proto_config_service_proto_depIdxs = []int32{
	0,  // 0: carbon.config.proto.ConfigDef.type:type_name -> carbon.config.proto.ConfigType
	1,  // 1: carbon.config.proto.ConfigDef.complexity:type_name -> carbon.config.proto.ConfigComplexity
	5,  // 2: carbon.config.proto.ConfigDef.int_def:type_name -> carbon.config.proto.IntConfigDef
	6,  // 3: carbon.config.proto.ConfigDef.uint_def:type_name -> carbon.config.proto.UIntConfigDef
	7,  // 4: carbon.config.proto.ConfigDef.float_def:type_name -> carbon.config.proto.FloatConfigDef
	8,  // 5: carbon.config.proto.ConfigDef.string_def:type_name -> carbon.config.proto.StringConfigDef
	4,  // 6: carbon.config.proto.ConfigNode.value:type_name -> carbon.config.proto.ConfigValue
	9,  // 7: carbon.config.proto.ConfigNode.def:type_name -> carbon.config.proto.ConfigDef
	10, // 8: carbon.config.proto.ConfigNode.children:type_name -> carbon.config.proto.ConfigNode
	4,  // 9: carbon.config.proto.ConfigLeaf.value:type_name -> carbon.config.proto.ConfigValue
	4,  // 10: carbon.config.proto.SetValueRequest.value:type_name -> carbon.config.proto.ConfigValue
	10, // 11: carbon.config.proto.GetTreeResponse.node:type_name -> carbon.config.proto.ConfigNode
	10, // 12: carbon.config.proto.SetTreeRequest.node:type_name -> carbon.config.proto.ConfigNode
	11, // 13: carbon.config.proto.GetLeavesResponse.leaves:type_name -> carbon.config.proto.ConfigLeaf
	2,  // 14: carbon.config.proto.ConfigService.Ping:input_type -> carbon.config.proto.PingRequest
	12, // 15: carbon.config.proto.ConfigService.SetValue:input_type -> carbon.config.proto.SetValueRequest
	14, // 16: carbon.config.proto.ConfigService.GetTree:input_type -> carbon.config.proto.GetTreeRequest
	16, // 17: carbon.config.proto.ConfigService.SetTree:input_type -> carbon.config.proto.SetTreeRequest
	18, // 18: carbon.config.proto.ConfigService.GetLeaves:input_type -> carbon.config.proto.GetLeavesRequest
	20, // 19: carbon.config.proto.ConfigService.AddToList:input_type -> carbon.config.proto.AddToListRequest
	22, // 20: carbon.config.proto.ConfigService.RemoveFromList:input_type -> carbon.config.proto.RemoveFromListRequest
	26, // 21: carbon.config.proto.ConfigService.UpgradeCloudConfig:input_type -> carbon.config.proto.UpgradeCloudConfigRequest
	24, // 22: carbon.config.proto.ConfigNotificationService.Subscribe:input_type -> carbon.config.proto.SubscriptionRequest
	3,  // 23: carbon.config.proto.ConfigService.Ping:output_type -> carbon.config.proto.PongResponse
	13, // 24: carbon.config.proto.ConfigService.SetValue:output_type -> carbon.config.proto.SetValueResponse
	15, // 25: carbon.config.proto.ConfigService.GetTree:output_type -> carbon.config.proto.GetTreeResponse
	17, // 26: carbon.config.proto.ConfigService.SetTree:output_type -> carbon.config.proto.SetTreeResponse
	19, // 27: carbon.config.proto.ConfigService.GetLeaves:output_type -> carbon.config.proto.GetLeavesResponse
	21, // 28: carbon.config.proto.ConfigService.AddToList:output_type -> carbon.config.proto.AddToListResponse
	23, // 29: carbon.config.proto.ConfigService.RemoveFromList:output_type -> carbon.config.proto.RemoveFromListResponse
	27, // 30: carbon.config.proto.ConfigService.UpgradeCloudConfig:output_type -> carbon.config.proto.UpgradeCloudConfigResponse
	25, // 31: carbon.config.proto.ConfigNotificationService.Subscribe:output_type -> carbon.config.proto.SubscriptionNotifyMessage
	23, // [23:32] is the sub-list for method output_type
	14, // [14:23] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_config_api_proto_config_service_proto_init() }
func file_config_api_proto_config_service_proto_init() {
	if File_config_api_proto_config_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_config_api_proto_config_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PongResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIntConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FloatConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigLeaf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetValueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTreeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTreeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLeavesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLeavesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddToListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddToListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveFromListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveFromListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionNotifyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeCloudConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_api_proto_config_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeCloudConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_config_api_proto_config_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ConfigValue_Int64Val)(nil),
		(*ConfigValue_Uint64Val)(nil),
		(*ConfigValue_BoolVal)(nil),
		(*ConfigValue_FloatVal)(nil),
		(*ConfigValue_StringVal)(nil),
	}
	file_config_api_proto_config_service_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*ConfigDef_IntDef)(nil),
		(*ConfigDef_UintDef)(nil),
		(*ConfigDef_FloatDef)(nil),
		(*ConfigDef_StringDef)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_config_api_proto_config_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_config_api_proto_config_service_proto_goTypes,
		DependencyIndexes: file_config_api_proto_config_service_proto_depIdxs,
		EnumInfos:         file_config_api_proto_config_service_proto_enumTypes,
		MessageInfos:      file_config_api_proto_config_service_proto_msgTypes,
	}.Build()
	File_config_api_proto_config_service_proto = out.File
	file_config_api_proto_config_service_proto_rawDesc = nil
	file_config_api_proto_config_service_proto_goTypes = nil
	file_config_api_proto_config_service_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: config/api/proto/config_service.proto

package config_service

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ConfigService_Ping_FullMethodName               = "/carbon.config.proto.ConfigService/Ping"
	ConfigService_SetValue_FullMethodName           = "/carbon.config.proto.ConfigService/SetValue"
	ConfigService_GetTree_FullMethodName            = "/carbon.config.proto.ConfigService/GetTree"
	ConfigService_SetTree_FullMethodName            = "/carbon.config.proto.ConfigService/SetTree"
	ConfigService_GetLeaves_FullMethodName          = "/carbon.config.proto.ConfigService/GetLeaves"
	ConfigService_AddToList_FullMethodName          = "/carbon.config.proto.ConfigService/AddToList"
	ConfigService_RemoveFromList_FullMethodName     = "/carbon.config.proto.ConfigService/RemoveFromList"
	ConfigService_UpgradeCloudConfig_FullMethodName = "/carbon.config.proto.ConfigService/UpgradeCloudConfig"
)

// ConfigServiceClient is the client API for ConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigServiceClient interface {
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PongResponse, error)
	SetValue(ctx context.Context, in *SetValueRequest, opts ...grpc.CallOption) (*SetValueResponse, error)
	GetTree(ctx context.Context, in *GetTreeRequest, opts ...grpc.CallOption) (*GetTreeResponse, error)
	SetTree(ctx context.Context, in *SetTreeRequest, opts ...grpc.CallOption) (*SetTreeResponse, error)
	GetLeaves(ctx context.Context, in *GetLeavesRequest, opts ...grpc.CallOption) (*GetLeavesResponse, error)
	AddToList(ctx context.Context, in *AddToListRequest, opts ...grpc.CallOption) (*AddToListResponse, error)
	RemoveFromList(ctx context.Context, in *RemoveFromListRequest, opts ...grpc.CallOption) (*RemoveFromListResponse, error)
	UpgradeCloudConfig(ctx context.Context, in *UpgradeCloudConfigRequest, opts ...grpc.CallOption) (*UpgradeCloudConfigResponse, error)
}

type configServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigServiceClient(cc grpc.ClientConnInterface) ConfigServiceClient {
	return &configServiceClient{cc}
}

func (c *configServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PongResponse, error) {
	out := new(PongResponse)
	err := c.cc.Invoke(ctx, ConfigService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) SetValue(ctx context.Context, in *SetValueRequest, opts ...grpc.CallOption) (*SetValueResponse, error) {
	out := new(SetValueResponse)
	err := c.cc.Invoke(ctx, ConfigService_SetValue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) GetTree(ctx context.Context, in *GetTreeRequest, opts ...grpc.CallOption) (*GetTreeResponse, error) {
	out := new(GetTreeResponse)
	err := c.cc.Invoke(ctx, ConfigService_GetTree_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) SetTree(ctx context.Context, in *SetTreeRequest, opts ...grpc.CallOption) (*SetTreeResponse, error) {
	out := new(SetTreeResponse)
	err := c.cc.Invoke(ctx, ConfigService_SetTree_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) GetLeaves(ctx context.Context, in *GetLeavesRequest, opts ...grpc.CallOption) (*GetLeavesResponse, error) {
	out := new(GetLeavesResponse)
	err := c.cc.Invoke(ctx, ConfigService_GetLeaves_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) AddToList(ctx context.Context, in *AddToListRequest, opts ...grpc.CallOption) (*AddToListResponse, error) {
	out := new(AddToListResponse)
	err := c.cc.Invoke(ctx, ConfigService_AddToList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) RemoveFromList(ctx context.Context, in *RemoveFromListRequest, opts ...grpc.CallOption) (*RemoveFromListResponse, error) {
	out := new(RemoveFromListResponse)
	err := c.cc.Invoke(ctx, ConfigService_RemoveFromList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceClient) UpgradeCloudConfig(ctx context.Context, in *UpgradeCloudConfigRequest, opts ...grpc.CallOption) (*UpgradeCloudConfigResponse, error) {
	out := new(UpgradeCloudConfigResponse)
	err := c.cc.Invoke(ctx, ConfigService_UpgradeCloudConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigServiceServer is the server API for ConfigService service.
// All implementations must embed UnimplementedConfigServiceServer
// for forward compatibility
type ConfigServiceServer interface {
	Ping(context.Context, *PingRequest) (*PongResponse, error)
	SetValue(context.Context, *SetValueRequest) (*SetValueResponse, error)
	GetTree(context.Context, *GetTreeRequest) (*GetTreeResponse, error)
	SetTree(context.Context, *SetTreeRequest) (*SetTreeResponse, error)
	GetLeaves(context.Context, *GetLeavesRequest) (*GetLeavesResponse, error)
	AddToList(context.Context, *AddToListRequest) (*AddToListResponse, error)
	RemoveFromList(context.Context, *RemoveFromListRequest) (*RemoveFromListResponse, error)
	UpgradeCloudConfig(context.Context, *UpgradeCloudConfigRequest) (*UpgradeCloudConfigResponse, error)
	mustEmbedUnimplementedConfigServiceServer()
}

// UnimplementedConfigServiceServer must be embedded to have forward compatible implementations.
type UnimplementedConfigServiceServer struct {
}

func (UnimplementedConfigServiceServer) Ping(context.Context, *PingRequest) (*PongResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedConfigServiceServer) SetValue(context.Context, *SetValueRequest) (*SetValueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetValue not implemented")
}
func (UnimplementedConfigServiceServer) GetTree(context.Context, *GetTreeRequest) (*GetTreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTree not implemented")
}
func (UnimplementedConfigServiceServer) SetTree(context.Context, *SetTreeRequest) (*SetTreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTree not implemented")
}
func (UnimplementedConfigServiceServer) GetLeaves(context.Context, *GetLeavesRequest) (*GetLeavesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeaves not implemented")
}
func (UnimplementedConfigServiceServer) AddToList(context.Context, *AddToListRequest) (*AddToListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddToList not implemented")
}
func (UnimplementedConfigServiceServer) RemoveFromList(context.Context, *RemoveFromListRequest) (*RemoveFromListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveFromList not implemented")
}
func (UnimplementedConfigServiceServer) UpgradeCloudConfig(context.Context, *UpgradeCloudConfigRequest) (*UpgradeCloudConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeCloudConfig not implemented")
}
func (UnimplementedConfigServiceServer) mustEmbedUnimplementedConfigServiceServer() {}

// UnsafeConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigServiceServer will
// result in compilation errors.
type UnsafeConfigServiceServer interface {
	mustEmbedUnimplementedConfigServiceServer()
}

func RegisterConfigServiceServer(s grpc.ServiceRegistrar, srv ConfigServiceServer) {
	s.RegisterService(&ConfigService_ServiceDesc, srv)
}

func _ConfigService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_SetValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).SetValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_SetValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).SetValue(ctx, req.(*SetValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_GetTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).GetTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_GetTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).GetTree(ctx, req.(*GetTreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_SetTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).SetTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_SetTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).SetTree(ctx, req.(*SetTreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_GetLeaves_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeavesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).GetLeaves(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_GetLeaves_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).GetLeaves(ctx, req.(*GetLeavesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_AddToList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddToListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).AddToList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_AddToList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).AddToList(ctx, req.(*AddToListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_RemoveFromList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFromListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).RemoveFromList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_RemoveFromList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).RemoveFromList(ctx, req.(*RemoveFromListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigService_UpgradeCloudConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeCloudConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceServer).UpgradeCloudConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigService_UpgradeCloudConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceServer).UpgradeCloudConfig(ctx, req.(*UpgradeCloudConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ConfigService_ServiceDesc is the grpc.ServiceDesc for ConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.config.proto.ConfigService",
	HandlerType: (*ConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _ConfigService_Ping_Handler,
		},
		{
			MethodName: "SetValue",
			Handler:    _ConfigService_SetValue_Handler,
		},
		{
			MethodName: "GetTree",
			Handler:    _ConfigService_GetTree_Handler,
		},
		{
			MethodName: "SetTree",
			Handler:    _ConfigService_SetTree_Handler,
		},
		{
			MethodName: "GetLeaves",
			Handler:    _ConfigService_GetLeaves_Handler,
		},
		{
			MethodName: "AddToList",
			Handler:    _ConfigService_AddToList_Handler,
		},
		{
			MethodName: "RemoveFromList",
			Handler:    _ConfigService_RemoveFromList_Handler,
		},
		{
			MethodName: "UpgradeCloudConfig",
			Handler:    _ConfigService_UpgradeCloudConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "config/api/proto/config_service.proto",
}

const (
	ConfigNotificationService_Subscribe_FullMethodName = "/carbon.config.proto.ConfigNotificationService/Subscribe"
)

// ConfigNotificationServiceClient is the client API for ConfigNotificationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigNotificationServiceClient interface {
	Subscribe(ctx context.Context, in *SubscriptionRequest, opts ...grpc.CallOption) (ConfigNotificationService_SubscribeClient, error)
}

type configNotificationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigNotificationServiceClient(cc grpc.ClientConnInterface) ConfigNotificationServiceClient {
	return &configNotificationServiceClient{cc}
}

func (c *configNotificationServiceClient) Subscribe(ctx context.Context, in *SubscriptionRequest, opts ...grpc.CallOption) (ConfigNotificationService_SubscribeClient, error) {
	stream, err := c.cc.NewStream(ctx, &ConfigNotificationService_ServiceDesc.Streams[0], ConfigNotificationService_Subscribe_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &configNotificationServiceSubscribeClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ConfigNotificationService_SubscribeClient interface {
	Recv() (*SubscriptionNotifyMessage, error)
	grpc.ClientStream
}

type configNotificationServiceSubscribeClient struct {
	grpc.ClientStream
}

func (x *configNotificationServiceSubscribeClient) Recv() (*SubscriptionNotifyMessage, error) {
	m := new(SubscriptionNotifyMessage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ConfigNotificationServiceServer is the server API for ConfigNotificationService service.
// All implementations must embed UnimplementedConfigNotificationServiceServer
// for forward compatibility
type ConfigNotificationServiceServer interface {
	Subscribe(*SubscriptionRequest, ConfigNotificationService_SubscribeServer) error
	mustEmbedUnimplementedConfigNotificationServiceServer()
}

// UnimplementedConfigNotificationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedConfigNotificationServiceServer struct {
}

func (UnimplementedConfigNotificationServiceServer) Subscribe(*SubscriptionRequest, ConfigNotificationService_SubscribeServer) error {
	return status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}
func (UnimplementedConfigNotificationServiceServer) mustEmbedUnimplementedConfigNotificationServiceServer() {
}

// UnsafeConfigNotificationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigNotificationServiceServer will
// result in compilation errors.
type UnsafeConfigNotificationServiceServer interface {
	mustEmbedUnimplementedConfigNotificationServiceServer()
}

func RegisterConfigNotificationServiceServer(s grpc.ServiceRegistrar, srv ConfigNotificationServiceServer) {
	s.RegisterService(&ConfigNotificationService_ServiceDesc, srv)
}

func _ConfigNotificationService_Subscribe_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SubscriptionRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ConfigNotificationServiceServer).Subscribe(m, &configNotificationServiceSubscribeServer{stream})
}

type ConfigNotificationService_SubscribeServer interface {
	Send(*SubscriptionNotifyMessage) error
	grpc.ServerStream
}

type configNotificationServiceSubscribeServer struct {
	grpc.ServerStream
}

func (x *configNotificationServiceSubscribeServer) Send(m *SubscriptionNotifyMessage) error {
	return x.ServerStream.SendMsg(m)
}

// ConfigNotificationService_ServiceDesc is the grpc.ServiceDesc for ConfigNotificationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConfigNotificationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.config.proto.ConfigNotificationService",
	HandlerType: (*ConfigNotificationServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Subscribe",
			Handler:       _ConfigNotificationService_Subscribe_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "config/api/proto/config_service.proto",
}

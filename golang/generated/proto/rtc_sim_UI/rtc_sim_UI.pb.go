// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto

package rtc_sim_UI

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Gear int32

const (
	// Must match enum in lib/drivers/nanopb/proto/ots_tractor.proto
	Gear_GEAR_PARK      Gear = 0
	Gear_GEAR_REVERSE   Gear = 1
	Gear_GEAR_NEUTRAL   Gear = 2
	Gear_GEAR_FORWARD   Gear = 3
	Gear_GEAR_POWERZERO Gear = 4
)

// Enum value maps for Gear.
var (
	Gear_name = map[int32]string{
		0: "GEAR_PARK",
		1: "GEAR_REVERSE",
		2: "GEAR_NEUTRAL",
		3: "GEAR_FORWARD",
		4: "GEAR_POWERZERO",
	}
	Gear_value = map[string]int32{
		"GEAR_PARK":      0,
		"GEAR_REVERSE":   1,
		"GEAR_NEUTRAL":   2,
		"GEAR_FORWARD":   3,
		"GEAR_POWERZERO": 4,
	}
)

func (x Gear) Enum() *Gear {
	p := new(Gear)
	*p = x
	return p
}

func (x Gear) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gear) Descriptor() protoreflect.EnumDescriptor {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_enumTypes[0].Descriptor()
}

func (Gear) Type() protoreflect.EnumType {
	return &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_enumTypes[0]
}

func (x Gear) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gear.Descriptor instead.
func (Gear) EnumDescriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{0}
}

type Lights int32

const (
	// Must match enum in lib/drivers/nanopb/proto/ots_tractor.proto
	Lights_LIGHTS_OFF  Lights = 0
	Lights_LIGHTS_LOW  Lights = 1
	Lights_LIGHTS_HIGH Lights = 2
)

// Enum value maps for Lights.
var (
	Lights_name = map[int32]string{
		0: "LIGHTS_OFF",
		1: "LIGHTS_LOW",
		2: "LIGHTS_HIGH",
	}
	Lights_value = map[string]int32{
		"LIGHTS_OFF":  0,
		"LIGHTS_LOW":  1,
		"LIGHTS_HIGH": 2,
	}
)

func (x Lights) Enum() *Lights {
	p := new(Lights)
	*p = x
	return p
}

func (x Lights) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lights) Descriptor() protoreflect.EnumDescriptor {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_enumTypes[1].Descriptor()
}

func (Lights) Type() protoreflect.EnumType {
	return &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_enumTypes[1]
}

func (x Lights) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lights.Descriptor instead.
func (Lights) EnumDescriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{1}
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{0}
}

type EnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *EnableRequest) Reset() {
	*x = EnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableRequest) ProtoMessage() {}

func (x *EnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableRequest.ProtoReflect.Descriptor instead.
func (*EnableRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{1}
}

func (x *EnableRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SafetySensorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sensor_1 bool `protobuf:"varint,1,opt,name=sensor_1,json=sensor1,proto3" json:"sensor_1,omitempty"`
	Sensor_2 bool `protobuf:"varint,2,opt,name=sensor_2,json=sensor2,proto3" json:"sensor_2,omitempty"`
	Sensor_3 bool `protobuf:"varint,3,opt,name=sensor_3,json=sensor3,proto3" json:"sensor_3,omitempty"`
	Sensor_4 bool `protobuf:"varint,4,opt,name=sensor_4,json=sensor4,proto3" json:"sensor_4,omitempty"`
}

func (x *SafetySensorsRequest) Reset() {
	*x = SafetySensorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafetySensorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafetySensorsRequest) ProtoMessage() {}

func (x *SafetySensorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafetySensorsRequest.ProtoReflect.Descriptor instead.
func (*SafetySensorsRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{2}
}

func (x *SafetySensorsRequest) GetSensor_1() bool {
	if x != nil {
		return x.Sensor_1
	}
	return false
}

func (x *SafetySensorsRequest) GetSensor_2() bool {
	if x != nil {
		return x.Sensor_2
	}
	return false
}

func (x *SafetySensorsRequest) GetSensor_3() bool {
	if x != nil {
		return x.Sensor_3
	}
	return false
}

func (x *SafetySensorsRequest) GetSensor_4() bool {
	if x != nil {
		return x.Sensor_4
	}
	return false
}

type SetSpeedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpeedMph float32 `protobuf:"fixed32,1,opt,name=speed_mph,json=speedMph,proto3" json:"speed_mph,omitempty"`
}

func (x *SetSpeedRequest) Reset() {
	*x = SetSpeedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSpeedRequest) ProtoMessage() {}

func (x *SetSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSpeedRequest.ProtoReflect.Descriptor instead.
func (*SetSpeedRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{3}
}

func (x *SetSpeedRequest) GetSpeedMph() float32 {
	if x != nil {
		return x.SpeedMph
	}
	return 0
}

type SetGearRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gear Gear `protobuf:"varint,1,opt,name=gear,proto3,enum=carbon.rtc_sim_UI.Gear" json:"gear,omitempty"`
}

func (x *SetGearRequest) Reset() {
	*x = SetGearRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGearRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGearRequest) ProtoMessage() {}

func (x *SetGearRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGearRequest.ProtoReflect.Descriptor instead.
func (*SetGearRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{4}
}

func (x *SetGearRequest) GetGear() Gear {
	if x != nil {
		return x.Gear
	}
	return Gear_GEAR_PARK
}

type SetLightsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lights Lights `protobuf:"varint,1,opt,name=lights,proto3,enum=carbon.rtc_sim_UI.Lights" json:"lights,omitempty"`
}

func (x *SetLightsRequest) Reset() {
	*x = SetLightsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLightsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLightsRequest) ProtoMessage() {}

func (x *SetLightsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLightsRequest.ProtoReflect.Descriptor instead.
func (*SetLightsRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{5}
}

func (x *SetLightsRequest) GetLights() Lights {
	if x != nil {
		return x.Lights
	}
	return Lights_LIGHTS_OFF
}

type SetEngineRpmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rpms int32 `protobuf:"varint,1,opt,name=rpms,proto3" json:"rpms,omitempty"`
}

func (x *SetEngineRpmRequest) Reset() {
	*x = SetEngineRpmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetEngineRpmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetEngineRpmRequest) ProtoMessage() {}

func (x *SetEngineRpmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetEngineRpmRequest.ProtoReflect.Descriptor instead.
func (*SetEngineRpmRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{6}
}

func (x *SetEngineRpmRequest) GetRpms() int32 {
	if x != nil {
		return x.Rpms
	}
	return 0
}

type SetErrorFlagRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorFlag int32 `protobuf:"varint,1,opt,name=error_flag,json=errorFlag,proto3" json:"error_flag,omitempty"`
}

func (x *SetErrorFlagRequest) Reset() {
	*x = SetErrorFlagRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetErrorFlagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetErrorFlagRequest) ProtoMessage() {}

func (x *SetErrorFlagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetErrorFlagRequest.ProtoReflect.Descriptor instead.
func (*SetErrorFlagRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{7}
}

func (x *SetErrorFlagRequest) GetErrorFlag() int32 {
	if x != nil {
		return x.ErrorFlag
	}
	return 0
}

type SetFuelLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuelLevel float32 `protobuf:"fixed32,1,opt,name=fuel_level,json=fuelLevel,proto3" json:"fuel_level,omitempty"`
}

func (x *SetFuelLevelRequest) Reset() {
	*x = SetFuelLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetFuelLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFuelLevelRequest) ProtoMessage() {}

func (x *SetFuelLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFuelLevelRequest.ProtoReflect.Descriptor instead.
func (*SetFuelLevelRequest) Descriptor() ([]byte, []int) {
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP(), []int{8}
}

func (x *SetFuelLevelRequest) GetFuelLevel() float32 {
	if x != nil {
		return x.FuelLevel
	}
	return 0
}

var File_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto protoreflect.FileDescriptor

var file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x2f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x72, 0x74, 0x63, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49,
	0x2f, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x11, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69,
	0x6d, 0x5f, 0x55, 0x49, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x29, 0x0a,
	0x0d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x14, 0x53, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x31, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x31, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x32, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x5f, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x33, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x34, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x34, 0x22, 0x2e, 0x0a,
	0x0f, 0x53, 0x65, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x08, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4d, 0x70, 0x68, 0x22, 0x3d, 0x0a,
	0x0e, 0x53, 0x65, 0x74, 0x47, 0x65, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2b, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55,
	0x49, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x22, 0x45, 0x0a, 0x10,
	0x53, 0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x31, 0x0a, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69,
	0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x06, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x22, 0x29, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x52, 0x70, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x70,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x22, 0x34,
	0x0a, 0x13, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x46, 0x6c, 0x61, 0x67, 0x22, 0x34, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x46, 0x75, 0x65, 0x6c, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x75, 0x65, 0x6c, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x09, 0x66, 0x75, 0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x2a, 0x5f, 0x0a, 0x04, 0x47, 0x65,
	0x61, 0x72, 0x12, 0x0d, 0x0a, 0x09, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x50, 0x41, 0x52, 0x4b, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53,
	0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x4e, 0x45, 0x55, 0x54,
	0x52, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x46, 0x4f,
	0x52, 0x57, 0x41, 0x52, 0x44, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x47, 0x45, 0x41, 0x52, 0x5f,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x04, 0x2a, 0x39, 0x0a, 0x06, 0x4c,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f,
	0x4f, 0x46, 0x46, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f,
	0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f,
	0x48, 0x49, 0x47, 0x48, 0x10, 0x02, 0x32, 0x83, 0x07, 0x0a, 0x15, 0x52, 0x54, 0x43, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x49, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x4e, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x43, 0x61, 0x62, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f,
	0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x57, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x73, 0x12, 0x27, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55,
	0x49, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x08, 0x53, 0x65, 0x74,
	0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x47, 0x65, 0x61, 0x72,
	0x12, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69,
	0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x47, 0x65, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63,
	0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x4c, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49,
	0x2e, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73,
	0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x48, 0x0a,
	0x08, 0x53, 0x65, 0x74, 0x45, 0x73, 0x74, 0x6f, 0x70, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x52, 0x70, 0x6d, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x70, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d,
	0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0b, 0x53,
	0x65, 0x74, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x50, 0x74, 0x6f, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x61, 0x72, 0x50, 0x74, 0x6f, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x46,
	0x75, 0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x53, 0x65, 0x74,
	0x46, 0x75, 0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69,
	0x6d, 0x5f, 0x55, 0x49, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x12, 0x5a, 0x10,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x55, 0x49,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescOnce sync.Once
	file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescData = file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDesc
)

func file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescGZIP() []byte {
	file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescOnce.Do(func() {
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescData = protoimpl.X.CompressGZIP(file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescData)
	})
	return file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDescData
}

var file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_goTypes = []interface{}{
	(Gear)(0),                    // 0: carbon.rtc_sim_UI.Gear
	(Lights)(0),                  // 1: carbon.rtc_sim_UI.Lights
	(*Empty)(nil),                // 2: carbon.rtc_sim_UI.Empty
	(*EnableRequest)(nil),        // 3: carbon.rtc_sim_UI.EnableRequest
	(*SafetySensorsRequest)(nil), // 4: carbon.rtc_sim_UI.SafetySensorsRequest
	(*SetSpeedRequest)(nil),      // 5: carbon.rtc_sim_UI.SetSpeedRequest
	(*SetGearRequest)(nil),       // 6: carbon.rtc_sim_UI.SetGearRequest
	(*SetLightsRequest)(nil),     // 7: carbon.rtc_sim_UI.SetLightsRequest
	(*SetEngineRpmRequest)(nil),  // 8: carbon.rtc_sim_UI.SetEngineRpmRequest
	(*SetErrorFlagRequest)(nil),  // 9: carbon.rtc_sim_UI.SetErrorFlagRequest
	(*SetFuelLevelRequest)(nil),  // 10: carbon.rtc_sim_UI.SetFuelLevelRequest
}
var file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_depIdxs = []int32{
	0,  // 0: carbon.rtc_sim_UI.SetGearRequest.gear:type_name -> carbon.rtc_sim_UI.Gear
	1,  // 1: carbon.rtc_sim_UI.SetLightsRequest.lights:type_name -> carbon.rtc_sim_UI.Lights
	3,  // 2: carbon.rtc_sim_UI.RTCSimulatorUIService.SetInCabSwitch:input_type -> carbon.rtc_sim_UI.EnableRequest
	4,  // 3: carbon.rtc_sim_UI.RTCSimulatorUIService.SetSafetySensors:input_type -> carbon.rtc_sim_UI.SafetySensorsRequest
	5,  // 4: carbon.rtc_sim_UI.RTCSimulatorUIService.SetSpeed:input_type -> carbon.rtc_sim_UI.SetSpeedRequest
	6,  // 5: carbon.rtc_sim_UI.RTCSimulatorUIService.SetGear:input_type -> carbon.rtc_sim_UI.SetGearRequest
	7,  // 6: carbon.rtc_sim_UI.RTCSimulatorUIService.SetLights:input_type -> carbon.rtc_sim_UI.SetLightsRequest
	3,  // 7: carbon.rtc_sim_UI.RTCSimulatorUIService.SetEstop:input_type -> carbon.rtc_sim_UI.EnableRequest
	8,  // 8: carbon.rtc_sim_UI.RTCSimulatorUIService.SetEngineRpm:input_type -> carbon.rtc_sim_UI.SetEngineRpmRequest
	3,  // 9: carbon.rtc_sim_UI.RTCSimulatorUIService.SetFrontPto:input_type -> carbon.rtc_sim_UI.EnableRequest
	3,  // 10: carbon.rtc_sim_UI.RTCSimulatorUIService.SetRearPto:input_type -> carbon.rtc_sim_UI.EnableRequest
	9,  // 11: carbon.rtc_sim_UI.RTCSimulatorUIService.SetErrorFlag:input_type -> carbon.rtc_sim_UI.SetErrorFlagRequest
	10, // 12: carbon.rtc_sim_UI.RTCSimulatorUIService.SetFuelLevel:input_type -> carbon.rtc_sim_UI.SetFuelLevelRequest
	2,  // 13: carbon.rtc_sim_UI.RTCSimulatorUIService.SetInCabSwitch:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 14: carbon.rtc_sim_UI.RTCSimulatorUIService.SetSafetySensors:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 15: carbon.rtc_sim_UI.RTCSimulatorUIService.SetSpeed:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 16: carbon.rtc_sim_UI.RTCSimulatorUIService.SetGear:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 17: carbon.rtc_sim_UI.RTCSimulatorUIService.SetLights:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 18: carbon.rtc_sim_UI.RTCSimulatorUIService.SetEstop:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 19: carbon.rtc_sim_UI.RTCSimulatorUIService.SetEngineRpm:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 20: carbon.rtc_sim_UI.RTCSimulatorUIService.SetFrontPto:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 21: carbon.rtc_sim_UI.RTCSimulatorUIService.SetRearPto:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 22: carbon.rtc_sim_UI.RTCSimulatorUIService.SetErrorFlag:output_type -> carbon.rtc_sim_UI.Empty
	2,  // 23: carbon.rtc_sim_UI.RTCSimulatorUIService.SetFuelLevel:output_type -> carbon.rtc_sim_UI.Empty
	13, // [13:24] is the sub-list for method output_type
	2,  // [2:13] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_init() }
func file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_init() {
	if File_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafetySensorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetSpeedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGearRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLightsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetEngineRpmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetErrorFlagRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetFuelLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_goTypes,
		DependencyIndexes: file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_depIdxs,
		EnumInfos:         file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_enumTypes,
		MessageInfos:      file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_msgTypes,
	}.Build()
	File_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto = out.File
	file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_rawDesc = nil
	file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_goTypes = nil
	file_golang_simulator_hardware_rtc_proto_rtc_sim_UI_rtc_sim_UI_proto_depIdxs = nil
}

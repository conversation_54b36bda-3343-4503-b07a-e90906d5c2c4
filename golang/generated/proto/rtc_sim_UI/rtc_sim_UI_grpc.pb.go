// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto

package rtc_sim_UI

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RTCSimulatorUIService_SetInCabSwitch_FullMethodName   = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetInCabSwitch"
	RTCSimulatorUIService_SetSafetySensors_FullMethodName = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSafetySensors"
	RTCSimulatorUIService_SetSpeed_FullMethodName         = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSpeed"
	RTCSimulatorUIService_SetGear_FullMethodName          = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetGear"
	RTCSimulatorUIService_SetLights_FullMethodName        = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetLights"
	RTCSimulatorUIService_SetEstop_FullMethodName         = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEstop"
	RTCSimulatorUIService_SetEngineRpm_FullMethodName     = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEngineRpm"
	RTCSimulatorUIService_SetFrontPto_FullMethodName      = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFrontPto"
	RTCSimulatorUIService_SetRearPto_FullMethodName       = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetRearPto"
	RTCSimulatorUIService_SetErrorFlag_FullMethodName     = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetErrorFlag"
	RTCSimulatorUIService_SetFuelLevel_FullMethodName     = "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFuelLevel"
)

// RTCSimulatorUIServiceClient is the client API for RTCSimulatorUIService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RTCSimulatorUIServiceClient interface {
	SetInCabSwitch(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error)
	SetSafetySensors(ctx context.Context, in *SafetySensorsRequest, opts ...grpc.CallOption) (*Empty, error)
	SetSpeed(ctx context.Context, in *SetSpeedRequest, opts ...grpc.CallOption) (*Empty, error)
	SetGear(ctx context.Context, in *SetGearRequest, opts ...grpc.CallOption) (*Empty, error)
	SetLights(ctx context.Context, in *SetLightsRequest, opts ...grpc.CallOption) (*Empty, error)
	SetEstop(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error)
	SetEngineRpm(ctx context.Context, in *SetEngineRpmRequest, opts ...grpc.CallOption) (*Empty, error)
	SetFrontPto(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error)
	SetRearPto(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error)
	SetErrorFlag(ctx context.Context, in *SetErrorFlagRequest, opts ...grpc.CallOption) (*Empty, error)
	SetFuelLevel(ctx context.Context, in *SetFuelLevelRequest, opts ...grpc.CallOption) (*Empty, error)
}

type rTCSimulatorUIServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRTCSimulatorUIServiceClient(cc grpc.ClientConnInterface) RTCSimulatorUIServiceClient {
	return &rTCSimulatorUIServiceClient{cc}
}

func (c *rTCSimulatorUIServiceClient) SetInCabSwitch(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetInCabSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetSafetySensors(ctx context.Context, in *SafetySensorsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetSafetySensors_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetSpeed(ctx context.Context, in *SetSpeedRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetSpeed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetGear(ctx context.Context, in *SetGearRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetGear_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetLights(ctx context.Context, in *SetLightsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetLights_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetEstop(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetEstop_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetEngineRpm(ctx context.Context, in *SetEngineRpmRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetEngineRpm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetFrontPto(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetFrontPto_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetRearPto(ctx context.Context, in *EnableRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetRearPto_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetErrorFlag(ctx context.Context, in *SetErrorFlagRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetErrorFlag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rTCSimulatorUIServiceClient) SetFuelLevel(ctx context.Context, in *SetFuelLevelRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, RTCSimulatorUIService_SetFuelLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RTCSimulatorUIServiceServer is the server API for RTCSimulatorUIService service.
// All implementations must embed UnimplementedRTCSimulatorUIServiceServer
// for forward compatibility
type RTCSimulatorUIServiceServer interface {
	SetInCabSwitch(context.Context, *EnableRequest) (*Empty, error)
	SetSafetySensors(context.Context, *SafetySensorsRequest) (*Empty, error)
	SetSpeed(context.Context, *SetSpeedRequest) (*Empty, error)
	SetGear(context.Context, *SetGearRequest) (*Empty, error)
	SetLights(context.Context, *SetLightsRequest) (*Empty, error)
	SetEstop(context.Context, *EnableRequest) (*Empty, error)
	SetEngineRpm(context.Context, *SetEngineRpmRequest) (*Empty, error)
	SetFrontPto(context.Context, *EnableRequest) (*Empty, error)
	SetRearPto(context.Context, *EnableRequest) (*Empty, error)
	SetErrorFlag(context.Context, *SetErrorFlagRequest) (*Empty, error)
	SetFuelLevel(context.Context, *SetFuelLevelRequest) (*Empty, error)
	mustEmbedUnimplementedRTCSimulatorUIServiceServer()
}

// UnimplementedRTCSimulatorUIServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRTCSimulatorUIServiceServer struct {
}

func (UnimplementedRTCSimulatorUIServiceServer) SetInCabSwitch(context.Context, *EnableRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetInCabSwitch not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetSafetySensors(context.Context, *SafetySensorsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSafetySensors not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetSpeed(context.Context, *SetSpeedRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSpeed not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetGear(context.Context, *SetGearRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGear not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetLights(context.Context, *SetLightsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLights not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetEstop(context.Context, *EnableRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetEstop not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetEngineRpm(context.Context, *SetEngineRpmRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetEngineRpm not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetFrontPto(context.Context, *EnableRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetFrontPto not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetRearPto(context.Context, *EnableRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRearPto not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetErrorFlag(context.Context, *SetErrorFlagRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetErrorFlag not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) SetFuelLevel(context.Context, *SetFuelLevelRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetFuelLevel not implemented")
}
func (UnimplementedRTCSimulatorUIServiceServer) mustEmbedUnimplementedRTCSimulatorUIServiceServer() {}

// UnsafeRTCSimulatorUIServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RTCSimulatorUIServiceServer will
// result in compilation errors.
type UnsafeRTCSimulatorUIServiceServer interface {
	mustEmbedUnimplementedRTCSimulatorUIServiceServer()
}

func RegisterRTCSimulatorUIServiceServer(s grpc.ServiceRegistrar, srv RTCSimulatorUIServiceServer) {
	s.RegisterService(&RTCSimulatorUIService_ServiceDesc, srv)
}

func _RTCSimulatorUIService_SetInCabSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetInCabSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetInCabSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetInCabSwitch(ctx, req.(*EnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetSafetySensors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SafetySensorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetSafetySensors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetSafetySensors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetSafetySensors(ctx, req.(*SafetySensorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetSpeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetSpeed(ctx, req.(*SetSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetGear_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGearRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetGear(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetGear_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetGear(ctx, req.(*SetGearRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetLights_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLightsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetLights(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetLights_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetLights(ctx, req.(*SetLightsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetEstop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetEstop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetEstop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetEstop(ctx, req.(*EnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetEngineRpm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetEngineRpmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetEngineRpm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetEngineRpm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetEngineRpm(ctx, req.(*SetEngineRpmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetFrontPto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetFrontPto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetFrontPto_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetFrontPto(ctx, req.(*EnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetRearPto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetRearPto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetRearPto_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetRearPto(ctx, req.(*EnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetErrorFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetErrorFlagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetErrorFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetErrorFlag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetErrorFlag(ctx, req.(*SetErrorFlagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RTCSimulatorUIService_SetFuelLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFuelLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RTCSimulatorUIServiceServer).SetFuelLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RTCSimulatorUIService_SetFuelLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RTCSimulatorUIServiceServer).SetFuelLevel(ctx, req.(*SetFuelLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RTCSimulatorUIService_ServiceDesc is the grpc.ServiceDesc for RTCSimulatorUIService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RTCSimulatorUIService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.rtc_sim_UI.RTCSimulatorUIService",
	HandlerType: (*RTCSimulatorUIServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetInCabSwitch",
			Handler:    _RTCSimulatorUIService_SetInCabSwitch_Handler,
		},
		{
			MethodName: "SetSafetySensors",
			Handler:    _RTCSimulatorUIService_SetSafetySensors_Handler,
		},
		{
			MethodName: "SetSpeed",
			Handler:    _RTCSimulatorUIService_SetSpeed_Handler,
		},
		{
			MethodName: "SetGear",
			Handler:    _RTCSimulatorUIService_SetGear_Handler,
		},
		{
			MethodName: "SetLights",
			Handler:    _RTCSimulatorUIService_SetLights_Handler,
		},
		{
			MethodName: "SetEstop",
			Handler:    _RTCSimulatorUIService_SetEstop_Handler,
		},
		{
			MethodName: "SetEngineRpm",
			Handler:    _RTCSimulatorUIService_SetEngineRpm_Handler,
		},
		{
			MethodName: "SetFrontPto",
			Handler:    _RTCSimulatorUIService_SetFrontPto_Handler,
		},
		{
			MethodName: "SetRearPto",
			Handler:    _RTCSimulatorUIService_SetRearPto_Handler,
		},
		{
			MethodName: "SetErrorFlag",
			Handler:    _RTCSimulatorUIService_SetErrorFlag_Handler,
		},
		{
			MethodName: "SetFuelLevel",
			Handler:    _RTCSimulatorUIService_SetFuelLevel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto",
}

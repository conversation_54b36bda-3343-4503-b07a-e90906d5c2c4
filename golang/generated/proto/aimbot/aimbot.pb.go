// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto

package aimbot

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	weed_tracking "github.com/carbonrobotics/robot/golang/generated/proto/weed_tracking"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SafetyOverrideState int32

const (
	SafetyOverrideState_SafetyOverrideNone         SafetyOverrideState = 0
	SafetyOverrideState_SafetyOverrideVelocityStop SafetyOverrideState = 1
)

// Enum value maps for SafetyOverrideState.
var (
	SafetyOverrideState_name = map[int32]string{
		0: "SafetyOverrideNone",
		1: "SafetyOverrideVelocityStop",
	}
	SafetyOverrideState_value = map[string]int32{
		"SafetyOverrideNone":         0,
		"SafetyOverrideVelocityStop": 1,
	}
)

func (x SafetyOverrideState) Enum() *SafetyOverrideState {
	p := new(SafetyOverrideState)
	*p = x
	return p
}

func (x SafetyOverrideState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SafetyOverrideState) Descriptor() protoreflect.EnumDescriptor {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_enumTypes[0].Descriptor()
}

func (SafetyOverrideState) Type() protoreflect.EnumType {
	return &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_enumTypes[0]
}

func (x SafetyOverrideState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SafetyOverrideState.Descriptor instead.
func (SafetyOverrideState) EnumDescriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{0}
}

type ServoGoToRequest_ServoType int32

const (
	ServoGoToRequest_PAN  ServoGoToRequest_ServoType = 0
	ServoGoToRequest_TILT ServoGoToRequest_ServoType = 1
)

// Enum value maps for ServoGoToRequest_ServoType.
var (
	ServoGoToRequest_ServoType_name = map[int32]string{
		0: "PAN",
		1: "TILT",
	}
	ServoGoToRequest_ServoType_value = map[string]int32{
		"PAN":  0,
		"TILT": 1,
	}
)

func (x ServoGoToRequest_ServoType) Enum() *ServoGoToRequest_ServoType {
	p := new(ServoGoToRequest_ServoType)
	*p = x
	return p
}

func (x ServoGoToRequest_ServoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServoGoToRequest_ServoType) Descriptor() protoreflect.EnumDescriptor {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_enumTypes[1].Descriptor()
}

func (ServoGoToRequest_ServoType) Type() protoreflect.EnumType {
	return &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_enumTypes[1]
}

func (x ServoGoToRequest_ServoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServoGoToRequest_ServoType.Descriptor instead.
func (ServoGoToRequest_ServoType) EnumDescriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{29, 0}
}

type PingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{0}
}

func (x *PingRequest) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PongReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PongReply) Reset() {
	*x = PongReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PongReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PongReply) ProtoMessage() {}

func (x *PongReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PongReply.ProtoReflect.Descriptor instead.
func (*PongReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{1}
}

func (x *PongReply) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{2}
}

type TargetingState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedingEnabled  bool `protobuf:"varint,1,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	ThinningEnabled bool `protobuf:"varint,2,opt,name=thinning_enabled,json=thinningEnabled,proto3" json:"thinning_enabled,omitempty"`
}

func (x *TargetingState) Reset() {
	*x = TargetingState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetingState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetingState) ProtoMessage() {}

func (x *TargetingState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetingState.ProtoReflect.Descriptor instead.
func (*TargetingState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{3}
}

func (x *TargetingState) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *TargetingState) GetThinningEnabled() bool {
	if x != nil {
		return x.ThinningEnabled
	}
	return false
}

type AimbotState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Algorithm             string              `protobuf:"bytes,1,opt,name=algorithm,proto3" json:"algorithm,omitempty"`
	Running               bool                `protobuf:"varint,2,opt,name=running,proto3" json:"running,omitempty"`
	Armed                 bool                `protobuf:"varint,3,opt,name=armed,proto3" json:"armed,omitempty"`
	TargetingState        *TargetingState     `protobuf:"bytes,4,opt,name=targeting_state,json=targetingState,proto3" json:"targeting_state,omitempty"`
	Ready                 bool                `protobuf:"varint,5,opt,name=ready,proto3" json:"ready,omitempty"`
	SafetyOverrideState   SafetyOverrideState `protobuf:"varint,6,opt,name=safety_override_state,json=safetyOverrideState,proto3,enum=aimbot.SafetyOverrideState" json:"safety_override_state,omitempty"`
	ActuationTasksRunning bool                `protobuf:"varint,7,opt,name=actuation_tasks_running,json=actuationTasksRunning,proto3" json:"actuation_tasks_running,omitempty"`
}

func (x *AimbotState) Reset() {
	*x = AimbotState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AimbotState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AimbotState) ProtoMessage() {}

func (x *AimbotState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AimbotState.ProtoReflect.Descriptor instead.
func (*AimbotState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{4}
}

func (x *AimbotState) GetAlgorithm() string {
	if x != nil {
		return x.Algorithm
	}
	return ""
}

func (x *AimbotState) GetRunning() bool {
	if x != nil {
		return x.Running
	}
	return false
}

func (x *AimbotState) GetArmed() bool {
	if x != nil {
		return x.Armed
	}
	return false
}

func (x *AimbotState) GetTargetingState() *TargetingState {
	if x != nil {
		return x.TargetingState
	}
	return nil
}

func (x *AimbotState) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *AimbotState) GetSafetyOverrideState() SafetyOverrideState {
	if x != nil {
		return x.SafetyOverrideState
	}
	return SafetyOverrideState_SafetyOverrideNone
}

func (x *AimbotState) GetActuationTasksRunning() bool {
	if x != nil {
		return x.ActuationTasksRunning
	}
	return false
}

type TargetVelocityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TargetVelocityRequest) Reset() {
	*x = TargetVelocityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetVelocityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetVelocityRequest) ProtoMessage() {}

func (x *TargetVelocityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetVelocityRequest.ProtoReflect.Descriptor instead.
func (*TargetVelocityRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{5}
}

type TargetVelocityReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VelocityMin float32 `protobuf:"fixed32,1,opt,name=velocity_min,json=velocityMin,proto3" json:"velocity_min,omitempty"`
	VelocityMax float32 `protobuf:"fixed32,2,opt,name=velocity_max,json=velocityMax,proto3" json:"velocity_max,omitempty"`
}

func (x *TargetVelocityReply) Reset() {
	*x = TargetVelocityReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetVelocityReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetVelocityReply) ProtoMessage() {}

func (x *TargetVelocityReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetVelocityReply.ProtoReflect.Descriptor instead.
func (*TargetVelocityReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{6}
}

func (x *TargetVelocityReply) GetVelocityMin() float32 {
	if x != nil {
		return x.VelocityMin
	}
	return 0
}

func (x *TargetVelocityReply) GetVelocityMax() float32 {
	if x != nil {
		return x.VelocityMax
	}
	return 0
}

type LaserTestActuationTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId  uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"` // 0 Means all of them
	DurationMs uint32 `protobuf:"varint,2,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
}

func (x *LaserTestActuationTask) Reset() {
	*x = LaserTestActuationTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserTestActuationTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserTestActuationTask) ProtoMessage() {}

func (x *LaserTestActuationTask) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserTestActuationTask.ProtoReflect.Descriptor instead.
func (*LaserTestActuationTask) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{7}
}

func (x *LaserTestActuationTask) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *LaserTestActuationTask) GetDurationMs() uint32 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

type ImageDrawActuationTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32  `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`  // 0 Means all of them
	SpeedMmps float32 `protobuf:"fixed32,2,opt,name=speed_mmps,json=speedMmps,proto3" json:"speed_mmps,omitempty"` // 50 is a good default
}

func (x *ImageDrawActuationTask) Reset() {
	*x = ImageDrawActuationTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageDrawActuationTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageDrawActuationTask) ProtoMessage() {}

func (x *ImageDrawActuationTask) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageDrawActuationTask.ProtoReflect.Descriptor instead.
func (*ImageDrawActuationTask) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{8}
}

func (x *ImageDrawActuationTask) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *ImageDrawActuationTask) GetSpeedMmps() float32 {
	if x != nil {
		return x.SpeedMmps
	}
	return 0
}

type RangeDrawActuationTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32  `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`  // 0 Means all of them
	DurationS float32 `protobuf:"fixed32,2,opt,name=duration_s,json=durationS,proto3" json:"duration_s,omitempty"` // 20 is a good default
}

func (x *RangeDrawActuationTask) Reset() {
	*x = RangeDrawActuationTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RangeDrawActuationTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeDrawActuationTask) ProtoMessage() {}

func (x *RangeDrawActuationTask) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeDrawActuationTask.ProtoReflect.Descriptor instead.
func (*RangeDrawActuationTask) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{9}
}

func (x *RangeDrawActuationTask) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *RangeDrawActuationTask) GetDurationS() float32 {
	if x != nil {
		return x.DurationS
	}
	return 0
}

type ActuationTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Task:
	//
	//	*ActuationTaskRequest_LaserTest
	//	*ActuationTaskRequest_ImageDraw
	//	*ActuationTaskRequest_RangeDraw
	Task isActuationTaskRequest_Task `protobuf_oneof:"task"`
}

func (x *ActuationTaskRequest) Reset() {
	*x = ActuationTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActuationTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActuationTaskRequest) ProtoMessage() {}

func (x *ActuationTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActuationTaskRequest.ProtoReflect.Descriptor instead.
func (*ActuationTaskRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{10}
}

func (m *ActuationTaskRequest) GetTask() isActuationTaskRequest_Task {
	if m != nil {
		return m.Task
	}
	return nil
}

func (x *ActuationTaskRequest) GetLaserTest() *LaserTestActuationTask {
	if x, ok := x.GetTask().(*ActuationTaskRequest_LaserTest); ok {
		return x.LaserTest
	}
	return nil
}

func (x *ActuationTaskRequest) GetImageDraw() *ImageDrawActuationTask {
	if x, ok := x.GetTask().(*ActuationTaskRequest_ImageDraw); ok {
		return x.ImageDraw
	}
	return nil
}

func (x *ActuationTaskRequest) GetRangeDraw() *RangeDrawActuationTask {
	if x, ok := x.GetTask().(*ActuationTaskRequest_RangeDraw); ok {
		return x.RangeDraw
	}
	return nil
}

type isActuationTaskRequest_Task interface {
	isActuationTaskRequest_Task()
}

type ActuationTaskRequest_LaserTest struct {
	LaserTest *LaserTestActuationTask `protobuf:"bytes,1,opt,name=laser_test,json=laserTest,proto3,oneof"`
}

type ActuationTaskRequest_ImageDraw struct {
	ImageDraw *ImageDrawActuationTask `protobuf:"bytes,2,opt,name=image_draw,json=imageDraw,proto3,oneof"`
}

type ActuationTaskRequest_RangeDraw struct {
	RangeDraw *RangeDrawActuationTask `protobuf:"bytes,3,opt,name=range_draw,json=rangeDraw,proto3,oneof"`
}

func (*ActuationTaskRequest_LaserTest) isActuationTaskRequest_Task() {}

func (*ActuationTaskRequest_ImageDraw) isActuationTaskRequest_Task() {}

func (*ActuationTaskRequest_RangeDraw) isActuationTaskRequest_Task() {}

type LensSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	Value     uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *LensSetRequest) Reset() {
	*x = LensSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensSetRequest) ProtoMessage() {}

func (x *LensSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensSetRequest.ProtoReflect.Descriptor instead.
func (*LensSetRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{11}
}

func (x *LensSetRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *LensSetRequest) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type LensSetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LensSetReply) Reset() {
	*x = LensSetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensSetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensSetReply) ProtoMessage() {}

func (x *LensSetReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensSetReply.ProtoReflect.Descriptor instead.
func (*LensSetReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{12}
}

type LensGetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
}

func (x *LensGetRequest) Reset() {
	*x = LensGetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensGetRequest) ProtoMessage() {}

func (x *LensGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensGetRequest.ProtoReflect.Descriptor instead.
func (*LensGetRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{13}
}

func (x *LensGetRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

type LensGetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value                  uint32  `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	MinValue               uint32  `protobuf:"varint,2,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`
	MaxValue               uint32  `protobuf:"varint,3,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`
	ManualAutofocusPercent float32 `protobuf:"fixed32,4,opt,name=manual_autofocus_percent,json=manualAutofocusPercent,proto3" json:"manual_autofocus_percent,omitempty"`
	ManualAutofocusing     bool    `protobuf:"varint,5,opt,name=manual_autofocusing,json=manualAutofocusing,proto3" json:"manual_autofocusing,omitempty"`
	ScannerId              uint32  `protobuf:"varint,6,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
}

func (x *LensGetReply) Reset() {
	*x = LensGetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensGetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensGetReply) ProtoMessage() {}

func (x *LensGetReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensGetReply.ProtoReflect.Descriptor instead.
func (*LensGetReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{14}
}

func (x *LensGetReply) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *LensGetReply) GetMinValue() uint32 {
	if x != nil {
		return x.MinValue
	}
	return 0
}

func (x *LensGetReply) GetMaxValue() uint32 {
	if x != nil {
		return x.MaxValue
	}
	return 0
}

func (x *LensGetReply) GetManualAutofocusPercent() float32 {
	if x != nil {
		return x.ManualAutofocusPercent
	}
	return 0
}

func (x *LensGetReply) GetManualAutofocusing() bool {
	if x != nil {
		return x.ManualAutofocusing
	}
	return false
}

func (x *LensGetReply) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

type LensGetAllReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LensStatus []*LensGetReply `protobuf:"bytes,1,rep,name=lens_status,json=lensStatus,proto3" json:"lens_status,omitempty"`
}

func (x *LensGetAllReply) Reset() {
	*x = LensGetAllReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensGetAllReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensGetAllReply) ProtoMessage() {}

func (x *LensGetAllReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensGetAllReply.ProtoReflect.Descriptor instead.
func (*LensGetAllReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{15}
}

func (x *LensGetAllReply) GetLensStatus() []*LensGetReply {
	if x != nil {
		return x.LensStatus
	}
	return nil
}

type LensAutoFocusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
}

func (x *LensAutoFocusRequest) Reset() {
	*x = LensAutoFocusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensAutoFocusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensAutoFocusRequest) ProtoMessage() {}

func (x *LensAutoFocusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensAutoFocusRequest.ProtoReflect.Descriptor instead.
func (*LensAutoFocusRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{16}
}

func (x *LensAutoFocusRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

type LensAutoFocusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LensAutoFocusReply) Reset() {
	*x = LensAutoFocusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LensAutoFocusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LensAutoFocusReply) ProtoMessage() {}

func (x *LensAutoFocusReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LensAutoFocusReply.ProtoReflect.Descriptor instead.
func (*LensAutoFocusReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{17}
}

type StopLensAutoFocusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
}

func (x *StopLensAutoFocusRequest) Reset() {
	*x = StopLensAutoFocusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopLensAutoFocusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopLensAutoFocusRequest) ProtoMessage() {}

func (x *StopLensAutoFocusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopLensAutoFocusRequest.ProtoReflect.Descriptor instead.
func (*StopLensAutoFocusRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{18}
}

func (x *StopLensAutoFocusRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

type LaserArmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	Armed     bool   `protobuf:"varint,2,opt,name=armed,proto3" json:"armed,omitempty"`
}

func (x *LaserArmRequest) Reset() {
	*x = LaserArmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserArmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserArmRequest) ProtoMessage() {}

func (x *LaserArmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserArmRequest.ProtoReflect.Descriptor instead.
func (*LaserArmRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{19}
}

func (x *LaserArmRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *LaserArmRequest) GetArmed() bool {
	if x != nil {
		return x.Armed
	}
	return false
}

type LaserArmReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LaserArmReply) Reset() {
	*x = LaserArmReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserArmReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserArmReply) ProtoMessage() {}

func (x *LaserArmReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserArmReply.ProtoReflect.Descriptor instead.
func (*LaserArmReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{20}
}

type LaserEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	Enabled   bool   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *LaserEnableRequest) Reset() {
	*x = LaserEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserEnableRequest) ProtoMessage() {}

func (x *LaserEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserEnableRequest.ProtoReflect.Descriptor instead.
func (*LaserEnableRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{21}
}

func (x *LaserEnableRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *LaserEnableRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type LaserEnableReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  bool   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *LaserEnableReply) Reset() {
	*x = LaserEnableReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserEnableReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserEnableReply) ProtoMessage() {}

func (x *LaserEnableReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserEnableReply.ProtoReflect.Descriptor instead.
func (*LaserEnableReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{22}
}

func (x *LaserEnableReply) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *LaserEnableReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type LaserFireRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	Fire      bool   `protobuf:"varint,2,opt,name=fire,proto3" json:"fire,omitempty"`
}

func (x *LaserFireRequest) Reset() {
	*x = LaserFireRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserFireRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserFireRequest) ProtoMessage() {}

func (x *LaserFireRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserFireRequest.ProtoReflect.Descriptor instead.
func (*LaserFireRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{23}
}

func (x *LaserFireRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *LaserFireRequest) GetFire() bool {
	if x != nil {
		return x.Fire
	}
	return false
}

type LaserFireReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LaserFireReply) Reset() {
	*x = LaserFireReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserFireReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserFireReply) ProtoMessage() {}

func (x *LaserFireReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserFireReply.ProtoReflect.Descriptor instead.
func (*LaserFireReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{24}
}

type LaserSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	On        bool   `protobuf:"varint,2,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *LaserSetRequest) Reset() {
	*x = LaserSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserSetRequest) ProtoMessage() {}

func (x *LaserSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserSetRequest.ProtoReflect.Descriptor instead.
func (*LaserSetRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{25}
}

func (x *LaserSetRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *LaserSetRequest) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

type LaserSetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LaserSetReply) Reset() {
	*x = LaserSetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserSetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserSetReply) ProtoMessage() {}

func (x *LaserSetReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserSetReply.ProtoReflect.Descriptor instead.
func (*LaserSetReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{26}
}

type BurnIdividualImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId []uint32 `protobuf:"varint,1,rep,packed,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	SpeedMmps float32  `protobuf:"fixed32,2,opt,name=speed_mmps,json=speedMmps,proto3" json:"speed_mmps,omitempty"`
	Intensity float32  `protobuf:"fixed32,3,opt,name=intensity,proto3" json:"intensity,omitempty"`
	JsonImg   string   `protobuf:"bytes,4,opt,name=json_img,json=jsonImg,proto3" json:"json_img,omitempty"`
}

func (x *BurnIdividualImagesRequest) Reset() {
	*x = BurnIdividualImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BurnIdividualImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BurnIdividualImagesRequest) ProtoMessage() {}

func (x *BurnIdividualImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BurnIdividualImagesRequest.ProtoReflect.Descriptor instead.
func (*BurnIdividualImagesRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{27}
}

func (x *BurnIdividualImagesRequest) GetScannerId() []uint32 {
	if x != nil {
		return x.ScannerId
	}
	return nil
}

func (x *BurnIdividualImagesRequest) GetSpeedMmps() float32 {
	if x != nil {
		return x.SpeedMmps
	}
	return 0
}

func (x *BurnIdividualImagesRequest) GetIntensity() float32 {
	if x != nil {
		return x.Intensity
	}
	return 0
}

func (x *BurnIdividualImagesRequest) GetJsonImg() string {
	if x != nil {
		return x.JsonImg
	}
	return ""
}

type LaserState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Firing  bool `protobuf:"varint,2,opt,name=firing,proto3" json:"firing,omitempty"`
	Error   bool `protobuf:"varint,3,opt,name=error,proto3" json:"error,omitempty"`
	// Deprecated: Marked as deprecated in core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto.
	ErrorCode string `protobuf:"bytes,4,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"` // Deprecated, use scanner state
	// Deprecated: Marked as deprecated in core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto.
	ErrorMessage string  `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"` // Deprecated, use scanner state
	Power        bool    `protobuf:"varint,6,opt,name=power,proto3" json:"power,omitempty"`
	DeltaTemp    float32 `protobuf:"fixed32,7,opt,name=delta_temp,json=deltaTemp,proto3" json:"delta_temp,omitempty"`
	Current      float32 `protobuf:"fixed32,8,opt,name=current,proto3" json:"current,omitempty"`
	Arced        bool    `protobuf:"varint,9,opt,name=arced,proto3" json:"arced,omitempty"`
	PowerLevel   float32 `protobuf:"fixed32,10,opt,name=power_level,json=powerLevel,proto3" json:"power_level,omitempty"`
}

func (x *LaserState) Reset() {
	*x = LaserState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserState) ProtoMessage() {}

func (x *LaserState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserState.ProtoReflect.Descriptor instead.
func (*LaserState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{28}
}

func (x *LaserState) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *LaserState) GetFiring() bool {
	if x != nil {
		return x.Firing
	}
	return false
}

func (x *LaserState) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

// Deprecated: Marked as deprecated in core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto.
func (x *LaserState) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

// Deprecated: Marked as deprecated in core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto.
func (x *LaserState) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *LaserState) GetPower() bool {
	if x != nil {
		return x.Power
	}
	return false
}

func (x *LaserState) GetDeltaTemp() float32 {
	if x != nil {
		return x.DeltaTemp
	}
	return 0
}

func (x *LaserState) GetCurrent() float32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *LaserState) GetArced() bool {
	if x != nil {
		return x.Arced
	}
	return false
}

func (x *LaserState) GetPowerLevel() float32 {
	if x != nil {
		return x.PowerLevel
	}
	return 0
}

type ServoGoToRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId   uint32                     `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	ServoType   ServoGoToRequest_ServoType `protobuf:"varint,2,opt,name=servo_type,json=servoType,proto3,enum=aimbot.ServoGoToRequest_ServoType" json:"servo_type,omitempty"`
	Position    int32                      `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`
	TimeMs      uint32                     `protobuf:"varint,4,opt,name=time_ms,json=timeMs,proto3" json:"time_ms,omitempty"`
	AwaitSettle bool                       `protobuf:"varint,5,opt,name=await_settle,json=awaitSettle,proto3" json:"await_settle,omitempty"`
}

func (x *ServoGoToRequest) Reset() {
	*x = ServoGoToRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoGoToRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoGoToRequest) ProtoMessage() {}

func (x *ServoGoToRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoGoToRequest.ProtoReflect.Descriptor instead.
func (*ServoGoToRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{29}
}

func (x *ServoGoToRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *ServoGoToRequest) GetServoType() ServoGoToRequest_ServoType {
	if x != nil {
		return x.ServoType
	}
	return ServoGoToRequest_PAN
}

func (x *ServoGoToRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *ServoGoToRequest) GetTimeMs() uint32 {
	if x != nil {
		return x.TimeMs
	}
	return 0
}

func (x *ServoGoToRequest) GetAwaitSettle() bool {
	if x != nil {
		return x.AwaitSettle
	}
	return false
}

type ServoGoToReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ServoGoToReply) Reset() {
	*x = ServoGoToReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoGoToReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoGoToReply) ProtoMessage() {}

func (x *ServoGoToReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoGoToReply.ProtoReflect.Descriptor instead.
func (*ServoGoToReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{30}
}

type ServoGetPosVelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
}

func (x *ServoGetPosVelRequest) Reset() {
	*x = ServoGetPosVelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoGetPosVelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoGetPosVelRequest) ProtoMessage() {}

func (x *ServoGetPosVelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoGetPosVelRequest.ProtoReflect.Descriptor instead.
func (*ServoGetPosVelRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{31}
}

func (x *ServoGetPosVelRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

type ServoGetPosVelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PanPosition  int32  `protobuf:"varint,1,opt,name=pan_position,json=panPosition,proto3" json:"pan_position,omitempty"`
	TiltPosition int32  `protobuf:"varint,2,opt,name=tilt_position,json=tiltPosition,proto3" json:"tilt_position,omitempty"`
	PanVelocity  uint32 `protobuf:"varint,3,opt,name=pan_velocity,json=panVelocity,proto3" json:"pan_velocity,omitempty"`
	TiltVelocity uint32 `protobuf:"varint,4,opt,name=tilt_velocity,json=tiltVelocity,proto3" json:"tilt_velocity,omitempty"`
}

func (x *ServoGetPosVelReply) Reset() {
	*x = ServoGetPosVelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoGetPosVelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoGetPosVelReply) ProtoMessage() {}

func (x *ServoGetPosVelReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoGetPosVelReply.ProtoReflect.Descriptor instead.
func (*ServoGetPosVelReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{32}
}

func (x *ServoGetPosVelReply) GetPanPosition() int32 {
	if x != nil {
		return x.PanPosition
	}
	return 0
}

func (x *ServoGetPosVelReply) GetTiltPosition() int32 {
	if x != nil {
		return x.TiltPosition
	}
	return 0
}

func (x *ServoGetPosVelReply) GetPanVelocity() uint32 {
	if x != nil {
		return x.PanVelocity
	}
	return 0
}

func (x *ServoGetPosVelReply) GetTiltVelocity() uint32 {
	if x != nil {
		return x.TiltVelocity
	}
	return 0
}

type ServoGetLimitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
}

func (x *ServoGetLimitsRequest) Reset() {
	*x = ServoGetLimitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoGetLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoGetLimitsRequest) ProtoMessage() {}

func (x *ServoGetLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoGetLimitsRequest.ProtoReflect.Descriptor instead.
func (*ServoGetLimitsRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{33}
}

func (x *ServoGetLimitsRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

type ServoGetLimitsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PanMin  int32 `protobuf:"varint,1,opt,name=pan_min,json=panMin,proto3" json:"pan_min,omitempty"`
	PanMax  int32 `protobuf:"varint,2,opt,name=pan_max,json=panMax,proto3" json:"pan_max,omitempty"`
	TiltMin int32 `protobuf:"varint,3,opt,name=tilt_min,json=tiltMin,proto3" json:"tilt_min,omitempty"`
	TiltMax int32 `protobuf:"varint,4,opt,name=tilt_max,json=tiltMax,proto3" json:"tilt_max,omitempty"`
}

func (x *ServoGetLimitsReply) Reset() {
	*x = ServoGetLimitsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServoGetLimitsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServoGetLimitsReply) ProtoMessage() {}

func (x *ServoGetLimitsReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServoGetLimitsReply.ProtoReflect.Descriptor instead.
func (*ServoGetLimitsReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{34}
}

func (x *ServoGetLimitsReply) GetPanMin() int32 {
	if x != nil {
		return x.PanMin
	}
	return 0
}

func (x *ServoGetLimitsReply) GetPanMax() int32 {
	if x != nil {
		return x.PanMax
	}
	return 0
}

func (x *ServoGetLimitsReply) GetTiltMin() int32 {
	if x != nil {
		return x.TiltMin
	}
	return 0
}

func (x *ServoGetLimitsReply) GetTiltMax() int32 {
	if x != nil {
		return x.TiltMax
	}
	return 0
}

type TuningParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are assignable to Value:
	//
	//	*TuningParam_VUint
	//	*TuningParam_VInt
	//	*TuningParam_VBool
	//	*TuningParam_VFloat
	//	*TuningParam_VString
	Value isTuningParam_Value `protobuf_oneof:"value"`
}

func (x *TuningParam) Reset() {
	*x = TuningParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TuningParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TuningParam) ProtoMessage() {}

func (x *TuningParam) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TuningParam.ProtoReflect.Descriptor instead.
func (*TuningParam) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{35}
}

func (x *TuningParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (m *TuningParam) GetValue() isTuningParam_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *TuningParam) GetVUint() uint32 {
	if x, ok := x.GetValue().(*TuningParam_VUint); ok {
		return x.VUint
	}
	return 0
}

func (x *TuningParam) GetVInt() int32 {
	if x, ok := x.GetValue().(*TuningParam_VInt); ok {
		return x.VInt
	}
	return 0
}

func (x *TuningParam) GetVBool() bool {
	if x, ok := x.GetValue().(*TuningParam_VBool); ok {
		return x.VBool
	}
	return false
}

func (x *TuningParam) GetVFloat() float32 {
	if x, ok := x.GetValue().(*TuningParam_VFloat); ok {
		return x.VFloat
	}
	return 0
}

func (x *TuningParam) GetVString() string {
	if x, ok := x.GetValue().(*TuningParam_VString); ok {
		return x.VString
	}
	return ""
}

type isTuningParam_Value interface {
	isTuningParam_Value()
}

type TuningParam_VUint struct {
	VUint uint32 `protobuf:"varint,2,opt,name=v_uint,json=vUint,proto3,oneof"`
}

type TuningParam_VInt struct {
	VInt int32 `protobuf:"varint,3,opt,name=v_int,json=vInt,proto3,oneof"`
}

type TuningParam_VBool struct {
	VBool bool `protobuf:"varint,4,opt,name=v_bool,json=vBool,proto3,oneof"`
}

type TuningParam_VFloat struct {
	VFloat float32 `protobuf:"fixed32,5,opt,name=v_float,json=vFloat,proto3,oneof"`
}

type TuningParam_VString struct {
	VString string `protobuf:"bytes,6,opt,name=v_string,json=vString,proto3,oneof"`
}

func (*TuningParam_VUint) isTuningParam_Value() {}

func (*TuningParam_VInt) isTuningParam_Value() {}

func (*TuningParam_VBool) isTuningParam_Value() {}

func (*TuningParam_VFloat) isTuningParam_Value() {}

func (*TuningParam_VString) isTuningParam_Value() {}

type TuningParamsUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params []*TuningParam `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"`
}

func (x *TuningParamsUpdateRequest) Reset() {
	*x = TuningParamsUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TuningParamsUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TuningParamsUpdateRequest) ProtoMessage() {}

func (x *TuningParamsUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TuningParamsUpdateRequest.ProtoReflect.Descriptor instead.
func (*TuningParamsUpdateRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{36}
}

func (x *TuningParamsUpdateRequest) GetParams() []*TuningParam {
	if x != nil {
		return x.Params
	}
	return nil
}

type TuningParamsUpdateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TuningParamsUpdateReply) Reset() {
	*x = TuningParamsUpdateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TuningParamsUpdateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TuningParamsUpdateReply) ProtoMessage() {}

func (x *TuningParamsUpdateReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TuningParamsUpdateReply.ProtoReflect.Descriptor instead.
func (*TuningParamsUpdateReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{37}
}

type TuningParamsGetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *TuningParamsGetRequest) Reset() {
	*x = TuningParamsGetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TuningParamsGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TuningParamsGetRequest) ProtoMessage() {}

func (x *TuningParamsGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TuningParamsGetRequest.ProtoReflect.Descriptor instead.
func (*TuningParamsGetRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{38}
}

func (x *TuningParamsGetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TuningParamsGetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params []*TuningParam `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"`
}

func (x *TuningParamsGetReply) Reset() {
	*x = TuningParamsGetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TuningParamsGetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TuningParamsGetReply) ProtoMessage() {}

func (x *TuningParamsGetReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TuningParamsGetReply.ProtoReflect.Descriptor instead.
func (*TuningParamsGetReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{39}
}

func (x *TuningParamsGetReply) GetParams() []*TuningParam {
	if x != nil {
		return x.Params
	}
	return nil
}

type GetLoadEstimateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLoadEstimateRequest) Reset() {
	*x = GetLoadEstimateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoadEstimateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoadEstimateRequest) ProtoMessage() {}

func (x *GetLoadEstimateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoadEstimateRequest.ProtoReflect.Descriptor instead.
func (*GetLoadEstimateRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{40}
}

type GetLoadEstimateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentLoad float32 `protobuf:"fixed32,1,opt,name=current_load,json=currentLoad,proto3" json:"current_load,omitempty"`
	TargetLoad  float32 `protobuf:"fixed32,2,opt,name=target_load,json=targetLoad,proto3" json:"target_load,omitempty"`
}

func (x *GetLoadEstimateReply) Reset() {
	*x = GetLoadEstimateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoadEstimateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoadEstimateReply) ProtoMessage() {}

func (x *GetLoadEstimateReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoadEstimateReply.ProtoReflect.Descriptor instead.
func (*GetLoadEstimateReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{41}
}

func (x *GetLoadEstimateReply) GetCurrentLoad() float32 {
	if x != nil {
		return x.CurrentLoad
	}
	return 0
}

func (x *GetLoadEstimateReply) GetTargetLoad() float32 {
	if x != nil {
		return x.TargetLoad
	}
	return 0
}

type GetDiagnosticRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetDiagnosticRequest) Reset() {
	*x = GetDiagnosticRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiagnosticRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiagnosticRequest) ProtoMessage() {}

func (x *GetDiagnosticRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiagnosticRequest.ProtoReflect.Descriptor instead.
func (*GetDiagnosticRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{42}
}

type GetDiagnosticReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Diagnostic string `protobuf:"bytes,1,opt,name=diagnostic,proto3" json:"diagnostic,omitempty"`
}

func (x *GetDiagnosticReply) Reset() {
	*x = GetDiagnosticReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiagnosticReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiagnosticReply) ProtoMessage() {}

func (x *GetDiagnosticReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiagnosticReply.ProtoReflect.Descriptor instead.
func (*GetDiagnosticReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{43}
}

func (x *GetDiagnosticReply) GetDiagnostic() string {
	if x != nil {
		return x.Diagnostic
	}
	return ""
}

type ResetDevicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (x *ResetDevicesRequest) Reset() {
	*x = ResetDevicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetDevicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetDevicesRequest) ProtoMessage() {}

func (x *ResetDevicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetDevicesRequest.ProtoReflect.Descriptor instead.
func (*ResetDevicesRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{44}
}

func (x *ResetDevicesRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type ResetDevicesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetDevicesReply) Reset() {
	*x = ResetDevicesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetDevicesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetDevicesReply) ProtoMessage() {}

func (x *ResetDevicesReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetDevicesReply.ProtoReflect.Descriptor instead.
func (*ResetDevicesReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{45}
}

type ResetScannerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId   uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	MetricsOnly bool   `protobuf:"varint,2,opt,name=metrics_only,json=metricsOnly,proto3" json:"metrics_only,omitempty"`
}

func (x *ResetScannerRequest) Reset() {
	*x = ResetScannerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetScannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetScannerRequest) ProtoMessage() {}

func (x *ResetScannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetScannerRequest.ProtoReflect.Descriptor instead.
func (*ResetScannerRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{46}
}

func (x *ResetScannerRequest) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *ResetScannerRequest) GetMetricsOnly() bool {
	if x != nil {
		return x.MetricsOnly
	}
	return false
}

type ResetScannerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetScannerReply) Reset() {
	*x = ResetScannerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetScannerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetScannerReply) ProtoMessage() {}

func (x *ResetScannerReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetScannerReply.ProtoReflect.Descriptor instead.
func (*ResetScannerReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{47}
}

type ScannerDescriptor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ScannerDescriptor) Reset() {
	*x = ScannerDescriptor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerDescriptor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerDescriptor) ProtoMessage() {}

func (x *ScannerDescriptor) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerDescriptor.ProtoReflect.Descriptor instead.
func (*ScannerDescriptor) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{48}
}

func (x *ScannerDescriptor) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ScannerTargetPosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerDescriptor *ScannerDescriptor `protobuf:"bytes,1,opt,name=scanner_descriptor,json=scannerDescriptor,proto3" json:"scanner_descriptor,omitempty"`
	X                 uint32             `protobuf:"varint,2,opt,name=x,proto3" json:"x,omitempty"`
	Y                 uint32             `protobuf:"varint,3,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *ScannerTargetPosition) Reset() {
	*x = ScannerTargetPosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerTargetPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerTargetPosition) ProtoMessage() {}

func (x *ScannerTargetPosition) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerTargetPosition.ProtoReflect.Descriptor instead.
func (*ScannerTargetPosition) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{49}
}

func (x *ScannerTargetPosition) GetScannerDescriptor() *ScannerDescriptor {
	if x != nil {
		return x.ScannerDescriptor
	}
	return nil
}

func (x *ScannerTargetPosition) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *ScannerTargetPosition) GetY() uint32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type CrosshairState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X                 uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                 uint32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"`
	Calibrating       bool   `protobuf:"varint,3,opt,name=calibrating,proto3" json:"calibrating,omitempty"`
	CalibrationFailed bool   `protobuf:"varint,4,opt,name=calibration_failed,json=calibrationFailed,proto3" json:"calibration_failed,omitempty"`
}

func (x *CrosshairState) Reset() {
	*x = CrosshairState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrosshairState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrosshairState) ProtoMessage() {}

func (x *CrosshairState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrosshairState.ProtoReflect.Descriptor instead.
func (*CrosshairState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{50}
}

func (x *CrosshairState) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *CrosshairState) GetY() uint32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *CrosshairState) GetCalibrating() bool {
	if x != nil {
		return x.Calibrating
	}
	return false
}

func (x *CrosshairState) GetCalibrationFailed() bool {
	if x != nil {
		return x.CalibrationFailed
	}
	return false
}

type ScannerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerDescriptor  *ScannerDescriptor `protobuf:"bytes,1,opt,name=scanner_descriptor,json=scannerDescriptor,proto3" json:"scanner_descriptor,omitempty"`
	LaserState         *LaserState        `protobuf:"bytes,2,opt,name=laser_state,json=laserState,proto3" json:"laser_state,omitempty"`
	CrosshairState     *CrosshairState    `protobuf:"bytes,3,opt,name=crosshair_state,json=crosshairState,proto3" json:"crosshair_state,omitempty"`
	ScannerError       bool               `protobuf:"varint,4,opt,name=scanner_error,json=scannerError,proto3" json:"scanner_error,omitempty"`
	ErrorCode          string             `protobuf:"bytes,5,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage       string             `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	TargetTrajectoryId uint32             `protobuf:"varint,7,opt,name=target_trajectory_id,json=targetTrajectoryId,proto3" json:"target_trajectory_id,omitempty"`
	PanFailure         bool               `protobuf:"varint,8,opt,name=pan_failure,json=panFailure,proto3" json:"pan_failure,omitempty"`
	TiltFailure        bool               `protobuf:"varint,9,opt,name=tilt_failure,json=tiltFailure,proto3" json:"tilt_failure,omitempty"`
}

func (x *ScannerState) Reset() {
	*x = ScannerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerState) ProtoMessage() {}

func (x *ScannerState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerState.ProtoReflect.Descriptor instead.
func (*ScannerState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{51}
}

func (x *ScannerState) GetScannerDescriptor() *ScannerDescriptor {
	if x != nil {
		return x.ScannerDescriptor
	}
	return nil
}

func (x *ScannerState) GetLaserState() *LaserState {
	if x != nil {
		return x.LaserState
	}
	return nil
}

func (x *ScannerState) GetCrosshairState() *CrosshairState {
	if x != nil {
		return x.CrosshairState
	}
	return nil
}

func (x *ScannerState) GetScannerError() bool {
	if x != nil {
		return x.ScannerError
	}
	return false
}

func (x *ScannerState) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ScannerState) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ScannerState) GetTargetTrajectoryId() uint32 {
	if x != nil {
		return x.TargetTrajectoryId
	}
	return 0
}

func (x *ScannerState) GetPanFailure() bool {
	if x != nil {
		return x.PanFailure
	}
	return false
}

func (x *ScannerState) GetTiltFailure() bool {
	if x != nil {
		return x.TiltFailure
	}
	return false
}

type AutoXHairCalibrationProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InProgress bool    `protobuf:"varint,1,opt,name=in_progress,json=inProgress,proto3" json:"in_progress,omitempty"`
	Progress   float32 `protobuf:"fixed32,2,opt,name=progress,proto3" json:"progress,omitempty"`
}

func (x *AutoXHairCalibrationProgress) Reset() {
	*x = AutoXHairCalibrationProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoXHairCalibrationProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoXHairCalibrationProgress) ProtoMessage() {}

func (x *AutoXHairCalibrationProgress) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoXHairCalibrationProgress.ProtoReflect.Descriptor instead.
func (*AutoXHairCalibrationProgress) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{52}
}

func (x *AutoXHairCalibrationProgress) GetInProgress() bool {
	if x != nil {
		return x.InProgress
	}
	return false
}

func (x *AutoXHairCalibrationProgress) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

type ScannerStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	States        []*ScannerState               `protobuf:"bytes,1,rep,name=states,proto3" json:"states,omitempty"`
	XHairProgress *AutoXHairCalibrationProgress `protobuf:"bytes,2,opt,name=x_hair_progress,json=xHairProgress,proto3" json:"x_hair_progress,omitempty"`
}

func (x *ScannerStatusReply) Reset() {
	*x = ScannerStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerStatusReply) ProtoMessage() {}

func (x *ScannerStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerStatusReply.ProtoReflect.Descriptor instead.
func (*ScannerStatusReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{53}
}

func (x *ScannerStatusReply) GetStates() []*ScannerState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ScannerStatusReply) GetXHairProgress() *AutoXHairCalibrationProgress {
	if x != nil {
		return x.XHairProgress
	}
	return nil
}

type BootedReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Booted bool `protobuf:"varint,1,opt,name=booted,proto3" json:"booted,omitempty"`
}

func (x *BootedReply) Reset() {
	*x = BootedReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BootedReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BootedReply) ProtoMessage() {}

func (x *BootedReply) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BootedReply.ProtoReflect.Descriptor instead.
func (*BootedReply) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{54}
}

func (x *BootedReply) GetBooted() bool {
	if x != nil {
		return x.Booted
	}
	return false
}

type TrackerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AtWeedLimit   bool   `protobuf:"varint,2,opt,name=at_weed_limit,json=atWeedLimit,proto3" json:"at_weed_limit,omitempty"`
	RotaryTimeout bool   `protobuf:"varint,3,opt,name=rotary_timeout,json=rotaryTimeout,proto3" json:"rotary_timeout,omitempty"`
	DeepweedError bool   `protobuf:"varint,4,opt,name=deepweed_error,json=deepweedError,proto3" json:"deepweed_error,omitempty"`
}

func (x *TrackerState) Reset() {
	*x = TrackerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackerState) ProtoMessage() {}

func (x *TrackerState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackerState.ProtoReflect.Descriptor instead.
func (*TrackerState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{55}
}

func (x *TrackerState) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrackerState) GetAtWeedLimit() bool {
	if x != nil {
		return x.AtWeedLimit
	}
	return false
}

func (x *TrackerState) GetRotaryTimeout() bool {
	if x != nil {
		return x.RotaryTimeout
	}
	return false
}

func (x *TrackerState) GetDeepweedError() bool {
	if x != nil {
		return x.DeepweedError
	}
	return false
}

type SchedulerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OverCapacity bool `protobuf:"varint,1,opt,name=over_capacity,json=overCapacity,proto3" json:"over_capacity,omitempty"`
}

func (x *SchedulerState) Reset() {
	*x = SchedulerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulerState) ProtoMessage() {}

func (x *SchedulerState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulerState.ProtoReflect.Descriptor instead.
func (*SchedulerState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{56}
}

func (x *SchedulerState) GetOverCapacity() bool {
	if x != nil {
		return x.OverCapacity
	}
	return false
}

type TrackingState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	States         []*TrackerState `protobuf:"bytes,1,rep,name=states,proto3" json:"states,omitempty"`
	SchedulerState *SchedulerState `protobuf:"bytes,2,opt,name=scheduler_state,json=schedulerState,proto3" json:"scheduler_state,omitempty"`
}

func (x *TrackingState) Reset() {
	*x = TrackingState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackingState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingState) ProtoMessage() {}

func (x *TrackingState) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingState.ProtoReflect.Descriptor instead.
func (*TrackingState) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{57}
}

func (x *TrackingState) GetStates() []*TrackerState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *TrackingState) GetSchedulerState() *SchedulerState {
	if x != nil {
		return x.SchedulerState
	}
	return nil
}

type BedtopHeightProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedHeightColumns []float64 `protobuf:"fixed64,1,rep,packed,name=weed_height_columns,json=weedHeightColumns,proto3" json:"weed_height_columns,omitempty"`
	CropHeightColumns []float64 `protobuf:"fixed64,2,rep,packed,name=crop_height_columns,json=cropHeightColumns,proto3" json:"crop_height_columns,omitempty"`
	PcamId            string    `protobuf:"bytes,3,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
}

func (x *BedtopHeightProfile) Reset() {
	*x = BedtopHeightProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BedtopHeightProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BedtopHeightProfile) ProtoMessage() {}

func (x *BedtopHeightProfile) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BedtopHeightProfile.ProtoReflect.Descriptor instead.
func (*BedtopHeightProfile) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{58}
}

func (x *BedtopHeightProfile) GetWeedHeightColumns() []float64 {
	if x != nil {
		return x.WeedHeightColumns
	}
	return nil
}

func (x *BedtopHeightProfile) GetCropHeightColumns() []float64 {
	if x != nil {
		return x.CropHeightColumns
	}
	return nil
}

func (x *BedtopHeightProfile) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

type TrackerBedtopHeightProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profiles    []*BedtopHeightProfile `protobuf:"bytes,1,rep,name=profiles,proto3" json:"profiles,omitempty"`
	BbhOffsetMm float32                `protobuf:"fixed32,2,opt,name=bbh_offset_mm,json=bbhOffsetMm,proto3" json:"bbh_offset_mm,omitempty"`
}

func (x *TrackerBedtopHeightProfile) Reset() {
	*x = TrackerBedtopHeightProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackerBedtopHeightProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackerBedtopHeightProfile) ProtoMessage() {}

func (x *TrackerBedtopHeightProfile) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackerBedtopHeightProfile.ProtoReflect.Descriptor instead.
func (*TrackerBedtopHeightProfile) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{59}
}

func (x *TrackerBedtopHeightProfile) GetProfiles() []*BedtopHeightProfile {
	if x != nil {
		return x.Profiles
	}
	return nil
}

func (x *TrackerBedtopHeightProfile) GetBbhOffsetMm() float32 {
	if x != nil {
		return x.BbhOffsetMm
	}
	return 0
}

type GetDimensionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinXMm    float64 `protobuf:"fixed64,1,opt,name=min_x_mm,json=minXMm,proto3" json:"min_x_mm,omitempty"`
	MaxXMm    float64 `protobuf:"fixed64,2,opt,name=max_x_mm,json=maxXMm,proto3" json:"max_x_mm,omitempty"`
	MinYMm    float64 `protobuf:"fixed64,3,opt,name=min_y_mm,json=minYMm,proto3" json:"min_y_mm,omitempty"`
	MaxYMm    float64 `protobuf:"fixed64,4,opt,name=max_y_mm,json=maxYMm,proto3" json:"max_y_mm,omitempty"`
	CenterXMm float64 `protobuf:"fixed64,5,opt,name=center_x_mm,json=centerXMm,proto3" json:"center_x_mm,omitempty"`
}

func (x *GetDimensionsResponse) Reset() {
	*x = GetDimensionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDimensionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDimensionsResponse) ProtoMessage() {}

func (x *GetDimensionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDimensionsResponse.ProtoReflect.Descriptor instead.
func (*GetDimensionsResponse) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{60}
}

func (x *GetDimensionsResponse) GetMinXMm() float64 {
	if x != nil {
		return x.MinXMm
	}
	return 0
}

func (x *GetDimensionsResponse) GetMaxXMm() float64 {
	if x != nil {
		return x.MaxXMm
	}
	return 0
}

func (x *GetDimensionsResponse) GetMinYMm() float64 {
	if x != nil {
		return x.MinYMm
	}
	return 0
}

func (x *GetDimensionsResponse) GetMaxYMm() float64 {
	if x != nil {
		return x.MaxYMm
	}
	return 0
}

func (x *GetDimensionsResponse) GetCenterXMm() float64 {
	if x != nil {
		return x.CenterXMm
	}
	return 0
}

type GetTargetCamSNRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CameraId string `protobuf:"bytes,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
}

func (x *GetTargetCamSNRequest) Reset() {
	*x = GetTargetCamSNRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetCamSNRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetCamSNRequest) ProtoMessage() {}

func (x *GetTargetCamSNRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetCamSNRequest.ProtoReflect.Descriptor instead.
func (*GetTargetCamSNRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{61}
}

func (x *GetTargetCamSNRequest) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

type GetTargetCamSNResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNumber string `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
}

func (x *GetTargetCamSNResponse) Reset() {
	*x = GetTargetCamSNResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetCamSNResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetCamSNResponse) ProtoMessage() {}

func (x *GetTargetCamSNResponse) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetCamSNResponse.ProtoReflect.Descriptor instead.
func (*GetTargetCamSNResponse) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{62}
}

func (x *GetTargetCamSNResponse) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

type ReloadThinningConfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadThinningConfRequest) Reset() {
	*x = ReloadThinningConfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadThinningConfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadThinningConfRequest) ProtoMessage() {}

func (x *ReloadThinningConfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadThinningConfRequest.ProtoReflect.Descriptor instead.
func (*ReloadThinningConfRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{63}
}

type ReloadAlmanacConfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadAlmanacConfRequest) Reset() {
	*x = ReloadAlmanacConfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadAlmanacConfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadAlmanacConfRequest) ProtoMessage() {}

func (x *ReloadAlmanacConfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadAlmanacConfRequest.ProtoReflect.Descriptor instead.
func (*ReloadAlmanacConfRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{64}
}

type ReloadDiscriminatorConfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadDiscriminatorConfRequest) Reset() {
	*x = ReloadDiscriminatorConfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadDiscriminatorConfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadDiscriminatorConfRequest) ProtoMessage() {}

func (x *ReloadDiscriminatorConfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadDiscriminatorConfRequest.ProtoReflect.Descriptor instead.
func (*ReloadDiscriminatorConfRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{65}
}

type ReloadModelinatorConfRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadModelinatorConfRequest) Reset() {
	*x = ReloadModelinatorConfRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadModelinatorConfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadModelinatorConfRequest) ProtoMessage() {}

func (x *ReloadModelinatorConfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadModelinatorConfRequest.ProtoReflect.Descriptor instead.
func (*ReloadModelinatorConfRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{66}
}

type ReloadTVEProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReloadTVEProfileRequest) Reset() {
	*x = ReloadTVEProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadTVEProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadTVEProfileRequest) ProtoMessage() {}

func (x *ReloadTVEProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadTVEProfileRequest.ProtoReflect.Descriptor instead.
func (*ReloadTVEProfileRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{67}
}

type FixLaserMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scanner         *ScannerDescriptor `protobuf:"bytes,1,opt,name=scanner,proto3" json:"scanner,omitempty"`
	TotalFireCount  int64              `protobuf:"varint,2,opt,name=total_fire_count,json=totalFireCount,proto3" json:"total_fire_count,omitempty"`
	TotalFireTimeMs int64              `protobuf:"varint,3,opt,name=total_fire_time_ms,json=totalFireTimeMs,proto3" json:"total_fire_time_ms,omitempty"`
}

func (x *FixLaserMetricsRequest) Reset() {
	*x = FixLaserMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FixLaserMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixLaserMetricsRequest) ProtoMessage() {}

func (x *FixLaserMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixLaserMetricsRequest.ProtoReflect.Descriptor instead.
func (*FixLaserMetricsRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{68}
}

func (x *FixLaserMetricsRequest) GetScanner() *ScannerDescriptor {
	if x != nil {
		return x.Scanner
	}
	return nil
}

func (x *FixLaserMetricsRequest) GetTotalFireCount() int64 {
	if x != nil {
		return x.TotalFireCount
	}
	return 0
}

func (x *FixLaserMetricsRequest) GetTotalFireTimeMs() int64 {
	if x != nil {
		return x.TotalFireTimeMs
	}
	return 0
}

type TrackedItemsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *TrackedItemsRequest) Reset() {
	*x = TrackedItemsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItemsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItemsRequest) ProtoMessage() {}

func (x *TrackedItemsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItemsRequest.ProtoReflect.Descriptor instead.
func (*TrackedItemsRequest) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{69}
}

func (x *TrackedItemsRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

type TrackedItemHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs uint64                   `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Detection   *weed_tracking.Detection `protobuf:"bytes,2,opt,name=detection,proto3" json:"detection,omitempty"`
}

func (x *TrackedItemHistory) Reset() {
	*x = TrackedItemHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItemHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItemHistory) ProtoMessage() {}

func (x *TrackedItemHistory) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItemHistory.ProtoReflect.Descriptor instead.
func (*TrackedItemHistory) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{70}
}

func (x *TrackedItemHistory) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *TrackedItemHistory) GetDetection() *weed_tracking.Detection {
	if x != nil {
		return x.Detection
	}
	return nil
}

type TrackedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	History []*TrackedItemHistory `protobuf:"bytes,2,rep,name=history,proto3" json:"history,omitempty"`
}

func (x *TrackedItem) Reset() {
	*x = TrackedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItem) ProtoMessage() {}

func (x *TrackedItem) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItem.ProtoReflect.Descriptor instead.
func (*TrackedItem) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{71}
}

func (x *TrackedItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrackedItem) GetHistory() []*TrackedItemHistory {
	if x != nil {
		return x.History
	}
	return nil
}

type TrackedItemsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackedItems []*TrackedItem `protobuf:"bytes,1,rep,name=tracked_items,json=trackedItems,proto3" json:"tracked_items,omitempty"`
}

func (x *TrackedItemsResponse) Reset() {
	*x = TrackedItemsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItemsResponse) ProtoMessage() {}

func (x *TrackedItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItemsResponse.ProtoReflect.Descriptor instead.
func (*TrackedItemsResponse) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{72}
}

func (x *TrackedItemsResponse) GetTrackedItems() []*TrackedItem {
	if x != nil {
		return x.TrackedItems
	}
	return nil
}

type ParticipationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunningAsLeader bool `protobuf:"varint,1,opt,name=running_as_leader,json=runningAsLeader,proto3" json:"running_as_leader,omitempty"`
}

func (x *ParticipationResponse) Reset() {
	*x = ParticipationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParticipationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipationResponse) ProtoMessage() {}

func (x *ParticipationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipationResponse.ProtoReflect.Descriptor instead.
func (*ParticipationResponse) Descriptor() ([]byte, []int) {
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP(), []int{73}
}

func (x *ParticipationResponse) GetRunningAsLeader() bool {
	if x != nil {
		return x.RunningAsLeader
	}
	return false
}

var File_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto protoreflect.FileDescriptor

var file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDesc = []byte{
	0x0a, 0x48, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x2f,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x2f, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x1a, 0x27, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1b, 0x0a, 0x0b, 0x50,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x19, 0x0a, 0x09, 0x50, 0x6f, 0x6e, 0x67,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x01, 0x78, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x64, 0x0a, 0x0e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x22, 0xbb, 0x02, 0x0a, 0x0b, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x72,
	0x6d, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x65, 0x64,
	0x12, 0x3f, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x12, 0x4f, 0x0a, 0x15, 0x73, 0x61, 0x66, 0x65, 0x74,
	0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x13, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x5f, 0x72, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x61, 0x63, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x22, 0x17, 0x0a, 0x15, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5b, 0x0a, 0x13, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79,
	0x4d, 0x69, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x76, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x4d, 0x61, 0x78, 0x22, 0x58, 0x0a, 0x16, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73,
	0x22, 0x56, 0x0a, 0x16, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x72, 0x61, 0x77, 0x41, 0x63, 0x74,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x65,
	0x65, 0x64, 0x5f, 0x6d, 0x6d, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x4d, 0x6d, 0x70, 0x73, 0x22, 0x56, 0x0a, 0x16, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x44, 0x72, 0x61, 0x77, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x22, 0xe1, 0x01, 0x0a, 0x14, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x0a, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52,
	0x09, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x54, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x0a, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x72, 0x61,
	0x77, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00,
	0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x72, 0x61, 0x77, 0x12, 0x3f, 0x0a, 0x0a, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x72,
	0x61, 0x77, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x48,
	0x00, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x72, 0x61, 0x77, 0x42, 0x06, 0x0a, 0x04,
	0x74, 0x61, 0x73, 0x6b, 0x22, 0x45, 0x0a, 0x0e, 0x4c, 0x65, 0x6e, 0x73, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x4c,
	0x65, 0x6e, 0x73, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x0a, 0x0e, 0x4c,
	0x65, 0x6e, 0x73, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0xe8, 0x01, 0x0a,
	0x0c, 0x4c, 0x65, 0x6e, 0x73, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x38, 0x0a,
	0x18, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x66, 0x6f, 0x63, 0x75,
	0x73, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x16, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x6f,
	0x66, 0x6f, 0x63, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x0f, 0x4c, 0x65, 0x6e, 0x73, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x0b, 0x6c, 0x65,
	0x6e, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x0a, 0x6c, 0x65, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x35, 0x0a, 0x14, 0x4c, 0x65, 0x6e, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x4c, 0x65, 0x6e, 0x73,
	0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x39,
	0x0a, 0x18, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x65, 0x6e, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f,
	0x63, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x0f, 0x4c, 0x61, 0x73,
	0x65, 0x72, 0x41, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x72, 0x6d, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x65,
	0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x41, 0x72, 0x6d, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x4d, 0x0a, 0x12, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x22, 0x44, 0x0a, 0x10, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x45, 0x0a, 0x10, 0x4c, 0x61, 0x73, 0x65, 0x72,
	0x46, 0x69, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x66, 0x69, 0x72, 0x65, 0x22, 0x10,
	0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x46, 0x69, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x40, 0x0a, 0x0f, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02,
	0x6f, 0x6e, 0x22, 0x0f, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x93, 0x01, 0x0a, 0x1a, 0x42, 0x75, 0x72, 0x6e, 0x49, 0x64, 0x69, 0x76,
	0x69, 0x64, 0x75, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6d, 0x70, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4d, 0x6d, 0x70, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x12, 0x19,
	0x0a, 0x08, 0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6a, 0x73, 0x6f, 0x6e, 0x49, 0x6d, 0x67, 0x22, 0xa6, 0x02, 0x0a, 0x0a, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x66, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x21, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x54, 0x65, 0x6d,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x72, 0x63, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x72, 0x63, 0x65,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x22, 0xec, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x6f, 0x54, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x6f, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x6f, 0x54, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x74, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x77, 0x61, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x22, 0x1e, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07,
	0x0a, 0x03, 0x50, 0x41, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x49, 0x4c, 0x54, 0x10,
	0x01, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x6f, 0x54, 0x6f, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x36, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x73, 0x56, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x13,
	0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x56, 0x65, 0x6c, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x61, 0x6e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74,
	0x69, 0x6c, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x61, 0x6e, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x70, 0x61, 0x6e, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x69, 0x6c, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x22, 0x36, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x13, 0x53,
	0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x6e, 0x4d, 0x69, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x70,
	0x61, 0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61,
	0x6e, 0x4d, 0x61, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x6d, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6c, 0x74, 0x4d, 0x69, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x74, 0x69, 0x6c, 0x74, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x74, 0x69, 0x6c, 0x74, 0x4d, 0x61, 0x78, 0x22, 0xab, 0x01, 0x0a, 0x0b, 0x54,
	0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17,
	0x0a, 0x06, 0x76, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00,
	0x52, 0x05, 0x76, 0x55, 0x69, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x05, 0x76, 0x5f, 0x69, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x04, 0x76, 0x49, 0x6e, 0x74, 0x12, 0x17,
	0x0a, 0x06, 0x76, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x05, 0x76, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x19, 0x0a, 0x07, 0x76, 0x5f, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x06, 0x76, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x1b, 0x0a, 0x08, 0x76, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x76, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x48, 0x0a, 0x19, 0x54, 0x75, 0x6e, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54,
	0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2c, 0x0a,
	0x16, 0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x14, 0x54,
	0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x75, 0x6e,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0x18, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5a, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x34,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x22, 0x32, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x65, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x57, 0x0a,
	0x13, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x6f,
	0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x4f, 0x6e, 0x6c, 0x79, 0x22, 0x13, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x0a, 0x11, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x7d, 0x0a, 0x15, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x12, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x52, 0x11, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x6f, 0x72, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01,
	0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x79, 0x22,
	0x7d, 0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x12,
	0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x79, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x2d, 0x0a, 0x12, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x22, 0xad,
	0x03, 0x0a, 0x0c, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x48, 0x0a, 0x12, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x52, 0x11, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x61, 0x73,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3f,
	0x0a, 0x0f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0e, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61,
	0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x70, 0x61, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x69, 0x6c, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x74, 0x69, 0x6c, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x22, 0x5b,
	0x0a, 0x1c, 0x41, 0x75, 0x74, 0x6f, 0x58, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x12,
	0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x4c, 0x0a, 0x0f, 0x78, 0x5f, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x58, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x0d, 0x78, 0x48, 0x61, 0x69, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22, 0x25,
	0x0a, 0x0b, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62,
	0x6f, 0x6f, 0x74, 0x65, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x74, 0x5f, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61,
	0x74, 0x57, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x77,
	0x65, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x35, 0x0a, 0x0e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22,
	0x7e, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3f,
	0x0a, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22,
	0x8e, 0x01, 0x0a, 0x13, 0x42, 0x65, 0x64, 0x74, 0x6f, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x01, 0x52, 0x11, 0x77, 0x65, 0x65, 0x64, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x72, 0x6f, 0x70, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x01, 0x52, 0x11, 0x63, 0x72, 0x6f, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x63, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x61, 0x6d, 0x49, 0x64,
	0x22, 0x79, 0x0a, 0x1a, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x42, 0x65, 0x64, 0x74, 0x6f,
	0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x37,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x42, 0x65, 0x64, 0x74, 0x6f, 0x70,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x62, 0x68, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b,
	0x62, 0x62, 0x68, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x4d, 0x6d, 0x22, 0x9f, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x08, 0x6d, 0x69, 0x6e, 0x5f, 0x78, 0x5f, 0x6d,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x58, 0x4d, 0x6d, 0x12,
	0x18, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x78, 0x5f, 0x6d, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x58, 0x4d, 0x6d, 0x12, 0x18, 0x0a, 0x08, 0x6d, 0x69, 0x6e,
	0x5f, 0x79, 0x5f, 0x6d, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6d, 0x69, 0x6e,
	0x59, 0x4d, 0x6d, 0x12, 0x18, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x79, 0x5f, 0x6d, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x59, 0x4d, 0x6d, 0x12, 0x1e, 0x0a,
	0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x78, 0x5f, 0x6d, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x58, 0x4d, 0x6d, 0x22, 0x34, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x53, 0x4e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x61, 0x6d, 0x53, 0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x22, 0x1b, 0x0a, 0x19, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x68, 0x69, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x1a, 0x0a, 0x18, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63,
	0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x20, 0x0a, 0x1e, 0x52,
	0x65, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1e, 0x0a,
	0x1c, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x19, 0x0a,
	0x17, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa4, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x78,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x52,
	0x07, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x72, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x22,
	0x2c, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x6f, 0x0a,
	0x12, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x36, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x53,
	0x0a, 0x0b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a,
	0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x22, 0x50, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x43, 0x0a, 0x15, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x73, 0x5f, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x72, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x41, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2a, 0x4d, 0x0a, 0x13, 0x53, 0x61,
	0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x56, 0x65, 0x6c, 0x6f, 0x63,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x6f, 0x70, 0x10, 0x01, 0x32, 0xe2, 0x19, 0x0a, 0x0d, 0x41, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x50,
	0x69, 0x6e, 0x67, 0x12, 0x13, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x31, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x13, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x2b, 0x0a, 0x09, 0x41, 0x72, 0x6d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x12, 0x0d, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0d, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x2e, 0x0a,
	0x0c, 0x44, 0x69, 0x73, 0x61, 0x72, 0x6d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x12, 0x0d, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0d, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x36, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x13,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x63, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1c, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x35, 0x0a, 0x13, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0d,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x39, 0x0a, 0x07, 0x4c, 0x65, 0x6e, 0x73, 0x53, 0x65, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x14, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x07, 0x4c, 0x65,
	0x6e, 0x73, 0x47, 0x65, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c,
	0x65, 0x6e, 0x73, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x36, 0x0a, 0x0a, 0x4c, 0x65, 0x6e, 0x73, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x17, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x4b, 0x0a,
	0x0d, 0x4c, 0x65, 0x6e, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x12, 0x1c,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73, 0x41, 0x75, 0x74, 0x6f,
	0x46, 0x6f, 0x63, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x65, 0x6e, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f,
	0x63, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x11, 0x53, 0x74,
	0x6f, 0x70, 0x4c, 0x65, 0x6e, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x12,
	0x20, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x65, 0x6e,
	0x73, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x00, 0x12, 0x3c, 0x0a, 0x08, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x41, 0x72, 0x6d, 0x12, 0x17,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x41, 0x72, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x41, 0x72, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x3c, 0x0a, 0x08, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x12, 0x17, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x45,
	0x0a, 0x0b, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x09, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x46, 0x69,
	0x72, 0x65, 0x12, 0x18, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x46, 0x69, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x46, 0x69, 0x72, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x19, 0x2e, 0x61, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x0f, 0x46, 0x69, 0x78, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x46, 0x69, 0x78, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x12, 0x42,
	0x75, 0x72, 0x6e, 0x49, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x22, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x42, 0x75, 0x72, 0x6e, 0x49,
	0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47,
	0x6f, 0x54, 0x6f, 0x12, 0x18, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x6f, 0x47, 0x6f, 0x54, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x6f, 0x54, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x6f,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x56, 0x65, 0x6c, 0x12, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x56, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x56, 0x65, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x6f,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x12, 0x54, 0x75, 0x6e, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0f, 0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x47, 0x65, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x54, 0x75, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x64, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x12, 0x1c, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x65, 0x74, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x52, 0x65, 0x73, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x48, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x12, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x1b, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x12, 0x19, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x6f, 0x72, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x3f, 0x0a, 0x1f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x75, 0x74, 0x6f,
	0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x72, 0x6f, 0x73,
	0x73, 0x68, 0x61, 0x69, 0x72, 0x73, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x31, 0x0a, 0x11, 0x53, 0x74, 0x6f, 0x70, 0x41, 0x75, 0x74, 0x6f,
	0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x65, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62,
	0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x44, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x43, 0x72,
	0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x0d,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3b, 0x0a,
	0x0b, 0x4d, 0x6f, 0x76, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x61,
	0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x0d, 0x2e, 0x61, 0x69,
	0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x51, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x24, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x41, 0x75, 0x74, 0x6f, 0x58, 0x48, 0x61, 0x69, 0x72, 0x43, 0x61, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3d, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x1a, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x15, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42, 0x65,
	0x64, 0x74, 0x6f, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x22, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x42, 0x65, 0x64, 0x74, 0x6f, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x61, 0x6d, 0x53, 0x4e, 0x12, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x53, 0x4e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x53, 0x4e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x68,
	0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x21, 0x2e, 0x61, 0x69, 0x6d,
	0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x44, 0x0a, 0x11,
	0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e,
	0x66, 0x12, 0x20, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61,
	0x64, 0x41, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x50, 0x0a, 0x17, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73, 0x63,
	0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x26, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x69, 0x73,
	0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x15, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x24, 0x2e,
	0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x42, 0x0a, 0x10, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x56, 0x45, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e,
	0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x56, 0x45, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x54, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x1b, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0e,
	0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescOnce sync.Once
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescData = file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDesc
)

func file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescGZIP() []byte {
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescOnce.Do(func() {
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescData = protoimpl.X.CompressGZIP(file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescData)
	})
	return file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDescData
}

var file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes = make([]protoimpl.MessageInfo, 74)
var file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_goTypes = []interface{}{
	(SafetyOverrideState)(0),               // 0: aimbot.SafetyOverrideState
	(ServoGoToRequest_ServoType)(0),        // 1: aimbot.ServoGoToRequest.ServoType
	(*PingRequest)(nil),                    // 2: aimbot.PingRequest
	(*PongReply)(nil),                      // 3: aimbot.PongReply
	(*Empty)(nil),                          // 4: aimbot.Empty
	(*TargetingState)(nil),                 // 5: aimbot.TargetingState
	(*AimbotState)(nil),                    // 6: aimbot.AimbotState
	(*TargetVelocityRequest)(nil),          // 7: aimbot.TargetVelocityRequest
	(*TargetVelocityReply)(nil),            // 8: aimbot.TargetVelocityReply
	(*LaserTestActuationTask)(nil),         // 9: aimbot.LaserTestActuationTask
	(*ImageDrawActuationTask)(nil),         // 10: aimbot.ImageDrawActuationTask
	(*RangeDrawActuationTask)(nil),         // 11: aimbot.RangeDrawActuationTask
	(*ActuationTaskRequest)(nil),           // 12: aimbot.ActuationTaskRequest
	(*LensSetRequest)(nil),                 // 13: aimbot.LensSetRequest
	(*LensSetReply)(nil),                   // 14: aimbot.LensSetReply
	(*LensGetRequest)(nil),                 // 15: aimbot.LensGetRequest
	(*LensGetReply)(nil),                   // 16: aimbot.LensGetReply
	(*LensGetAllReply)(nil),                // 17: aimbot.LensGetAllReply
	(*LensAutoFocusRequest)(nil),           // 18: aimbot.LensAutoFocusRequest
	(*LensAutoFocusReply)(nil),             // 19: aimbot.LensAutoFocusReply
	(*StopLensAutoFocusRequest)(nil),       // 20: aimbot.StopLensAutoFocusRequest
	(*LaserArmRequest)(nil),                // 21: aimbot.LaserArmRequest
	(*LaserArmReply)(nil),                  // 22: aimbot.LaserArmReply
	(*LaserEnableRequest)(nil),             // 23: aimbot.LaserEnableRequest
	(*LaserEnableReply)(nil),               // 24: aimbot.LaserEnableReply
	(*LaserFireRequest)(nil),               // 25: aimbot.LaserFireRequest
	(*LaserFireReply)(nil),                 // 26: aimbot.LaserFireReply
	(*LaserSetRequest)(nil),                // 27: aimbot.LaserSetRequest
	(*LaserSetReply)(nil),                  // 28: aimbot.LaserSetReply
	(*BurnIdividualImagesRequest)(nil),     // 29: aimbot.BurnIdividualImagesRequest
	(*LaserState)(nil),                     // 30: aimbot.LaserState
	(*ServoGoToRequest)(nil),               // 31: aimbot.ServoGoToRequest
	(*ServoGoToReply)(nil),                 // 32: aimbot.ServoGoToReply
	(*ServoGetPosVelRequest)(nil),          // 33: aimbot.ServoGetPosVelRequest
	(*ServoGetPosVelReply)(nil),            // 34: aimbot.ServoGetPosVelReply
	(*ServoGetLimitsRequest)(nil),          // 35: aimbot.ServoGetLimitsRequest
	(*ServoGetLimitsReply)(nil),            // 36: aimbot.ServoGetLimitsReply
	(*TuningParam)(nil),                    // 37: aimbot.TuningParam
	(*TuningParamsUpdateRequest)(nil),      // 38: aimbot.TuningParamsUpdateRequest
	(*TuningParamsUpdateReply)(nil),        // 39: aimbot.TuningParamsUpdateReply
	(*TuningParamsGetRequest)(nil),         // 40: aimbot.TuningParamsGetRequest
	(*TuningParamsGetReply)(nil),           // 41: aimbot.TuningParamsGetReply
	(*GetLoadEstimateRequest)(nil),         // 42: aimbot.GetLoadEstimateRequest
	(*GetLoadEstimateReply)(nil),           // 43: aimbot.GetLoadEstimateReply
	(*GetDiagnosticRequest)(nil),           // 44: aimbot.GetDiagnosticRequest
	(*GetDiagnosticReply)(nil),             // 45: aimbot.GetDiagnosticReply
	(*ResetDevicesRequest)(nil),            // 46: aimbot.ResetDevicesRequest
	(*ResetDevicesReply)(nil),              // 47: aimbot.ResetDevicesReply
	(*ResetScannerRequest)(nil),            // 48: aimbot.ResetScannerRequest
	(*ResetScannerReply)(nil),              // 49: aimbot.ResetScannerReply
	(*ScannerDescriptor)(nil),              // 50: aimbot.ScannerDescriptor
	(*ScannerTargetPosition)(nil),          // 51: aimbot.ScannerTargetPosition
	(*CrosshairState)(nil),                 // 52: aimbot.CrosshairState
	(*ScannerState)(nil),                   // 53: aimbot.ScannerState
	(*AutoXHairCalibrationProgress)(nil),   // 54: aimbot.AutoXHairCalibrationProgress
	(*ScannerStatusReply)(nil),             // 55: aimbot.ScannerStatusReply
	(*BootedReply)(nil),                    // 56: aimbot.BootedReply
	(*TrackerState)(nil),                   // 57: aimbot.TrackerState
	(*SchedulerState)(nil),                 // 58: aimbot.SchedulerState
	(*TrackingState)(nil),                  // 59: aimbot.TrackingState
	(*BedtopHeightProfile)(nil),            // 60: aimbot.BedtopHeightProfile
	(*TrackerBedtopHeightProfile)(nil),     // 61: aimbot.TrackerBedtopHeightProfile
	(*GetDimensionsResponse)(nil),          // 62: aimbot.GetDimensionsResponse
	(*GetTargetCamSNRequest)(nil),          // 63: aimbot.GetTargetCamSNRequest
	(*GetTargetCamSNResponse)(nil),         // 64: aimbot.GetTargetCamSNResponse
	(*ReloadThinningConfRequest)(nil),      // 65: aimbot.ReloadThinningConfRequest
	(*ReloadAlmanacConfRequest)(nil),       // 66: aimbot.ReloadAlmanacConfRequest
	(*ReloadDiscriminatorConfRequest)(nil), // 67: aimbot.ReloadDiscriminatorConfRequest
	(*ReloadModelinatorConfRequest)(nil),   // 68: aimbot.ReloadModelinatorConfRequest
	(*ReloadTVEProfileRequest)(nil),        // 69: aimbot.ReloadTVEProfileRequest
	(*FixLaserMetricsRequest)(nil),         // 70: aimbot.FixLaserMetricsRequest
	(*TrackedItemsRequest)(nil),            // 71: aimbot.TrackedItemsRequest
	(*TrackedItemHistory)(nil),             // 72: aimbot.TrackedItemHistory
	(*TrackedItem)(nil),                    // 73: aimbot.TrackedItem
	(*TrackedItemsResponse)(nil),           // 74: aimbot.TrackedItemsResponse
	(*ParticipationResponse)(nil),          // 75: aimbot.ParticipationResponse
	(*weed_tracking.Detection)(nil),        // 76: weed_tracking.Detection
}
var file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_depIdxs = []int32{
	5,  // 0: aimbot.AimbotState.targeting_state:type_name -> aimbot.TargetingState
	0,  // 1: aimbot.AimbotState.safety_override_state:type_name -> aimbot.SafetyOverrideState
	9,  // 2: aimbot.ActuationTaskRequest.laser_test:type_name -> aimbot.LaserTestActuationTask
	10, // 3: aimbot.ActuationTaskRequest.image_draw:type_name -> aimbot.ImageDrawActuationTask
	11, // 4: aimbot.ActuationTaskRequest.range_draw:type_name -> aimbot.RangeDrawActuationTask
	16, // 5: aimbot.LensGetAllReply.lens_status:type_name -> aimbot.LensGetReply
	1,  // 6: aimbot.ServoGoToRequest.servo_type:type_name -> aimbot.ServoGoToRequest.ServoType
	37, // 7: aimbot.TuningParamsUpdateRequest.params:type_name -> aimbot.TuningParam
	37, // 8: aimbot.TuningParamsGetReply.params:type_name -> aimbot.TuningParam
	50, // 9: aimbot.ScannerTargetPosition.scanner_descriptor:type_name -> aimbot.ScannerDescriptor
	50, // 10: aimbot.ScannerState.scanner_descriptor:type_name -> aimbot.ScannerDescriptor
	30, // 11: aimbot.ScannerState.laser_state:type_name -> aimbot.LaserState
	52, // 12: aimbot.ScannerState.crosshair_state:type_name -> aimbot.CrosshairState
	53, // 13: aimbot.ScannerStatusReply.states:type_name -> aimbot.ScannerState
	54, // 14: aimbot.ScannerStatusReply.x_hair_progress:type_name -> aimbot.AutoXHairCalibrationProgress
	57, // 15: aimbot.TrackingState.states:type_name -> aimbot.TrackerState
	58, // 16: aimbot.TrackingState.scheduler_state:type_name -> aimbot.SchedulerState
	60, // 17: aimbot.TrackerBedtopHeightProfile.profiles:type_name -> aimbot.BedtopHeightProfile
	50, // 18: aimbot.FixLaserMetricsRequest.scanner:type_name -> aimbot.ScannerDescriptor
	76, // 19: aimbot.TrackedItemHistory.detection:type_name -> weed_tracking.Detection
	72, // 20: aimbot.TrackedItem.history:type_name -> aimbot.TrackedItemHistory
	73, // 21: aimbot.TrackedItemsResponse.tracked_items:type_name -> aimbot.TrackedItem
	2,  // 22: aimbot.AimbotService.Ping:input_type -> aimbot.PingRequest
	4,  // 23: aimbot.AimbotService.GetBooted:input_type -> aimbot.Empty
	4,  // 24: aimbot.AimbotService.ArmLasers:input_type -> aimbot.Empty
	4,  // 25: aimbot.AimbotService.DisarmLasers:input_type -> aimbot.Empty
	4,  // 26: aimbot.AimbotService.GetAimbotState:input_type -> aimbot.Empty
	5,  // 27: aimbot.AimbotService.SetTargetingState:input_type -> aimbot.TargetingState
	12, // 28: aimbot.AimbotService.StartActuationTask:input_type -> aimbot.ActuationTaskRequest
	4,  // 29: aimbot.AimbotService.CancelActuationTask:input_type -> aimbot.Empty
	13, // 30: aimbot.AimbotService.LensSet:input_type -> aimbot.LensSetRequest
	15, // 31: aimbot.AimbotService.LensGet:input_type -> aimbot.LensGetRequest
	4,  // 32: aimbot.AimbotService.LensGetAll:input_type -> aimbot.Empty
	18, // 33: aimbot.AimbotService.LensAutoFocus:input_type -> aimbot.LensAutoFocusRequest
	20, // 34: aimbot.AimbotService.StopLensAutoFocus:input_type -> aimbot.StopLensAutoFocusRequest
	21, // 35: aimbot.AimbotService.LaserArm:input_type -> aimbot.LaserArmRequest
	27, // 36: aimbot.AimbotService.LaserSet:input_type -> aimbot.LaserSetRequest
	23, // 37: aimbot.AimbotService.LaserEnable:input_type -> aimbot.LaserEnableRequest
	25, // 38: aimbot.AimbotService.LaserFire:input_type -> aimbot.LaserFireRequest
	50, // 39: aimbot.AimbotService.ResetLaserMetrics:input_type -> aimbot.ScannerDescriptor
	70, // 40: aimbot.AimbotService.FixLaserMetrics:input_type -> aimbot.FixLaserMetricsRequest
	29, // 41: aimbot.AimbotService.BurnIdividualImage:input_type -> aimbot.BurnIdividualImagesRequest
	31, // 42: aimbot.AimbotService.ServoGoTo:input_type -> aimbot.ServoGoToRequest
	33, // 43: aimbot.AimbotService.ServoGetPosVel:input_type -> aimbot.ServoGetPosVelRequest
	35, // 44: aimbot.AimbotService.ServoGetLimits:input_type -> aimbot.ServoGetLimitsRequest
	38, // 45: aimbot.AimbotService.TuningParamsUpdate:input_type -> aimbot.TuningParamsUpdateRequest
	40, // 46: aimbot.AimbotService.TuningParamsGet:input_type -> aimbot.TuningParamsGetRequest
	42, // 47: aimbot.AimbotService.GetLoadEstimate:input_type -> aimbot.GetLoadEstimateRequest
	44, // 48: aimbot.AimbotService.GetDiagnostic:input_type -> aimbot.GetDiagnosticRequest
	46, // 49: aimbot.AimbotService.ResetDevices:input_type -> aimbot.ResetDevicesRequest
	48, // 50: aimbot.AimbotService.ResetScanner:input_type -> aimbot.ResetScannerRequest
	50, // 51: aimbot.AimbotService.StartAutoCalibrateCrosshair:input_type -> aimbot.ScannerDescriptor
	4,  // 52: aimbot.AimbotService.StartAutoCalibrateAllCrosshairs:input_type -> aimbot.Empty
	4,  // 53: aimbot.AimbotService.StopAutoCalibrate:input_type -> aimbot.Empty
	51, // 54: aimbot.AimbotService.SetCrosshairPosition:input_type -> aimbot.ScannerTargetPosition
	51, // 55: aimbot.AimbotService.MoveScanner:input_type -> aimbot.ScannerTargetPosition
	4,  // 56: aimbot.AimbotService.GetAutoCalibrationProgress:input_type -> aimbot.Empty
	4,  // 57: aimbot.AimbotService.GetScannerStatus:input_type -> aimbot.Empty
	7,  // 58: aimbot.AimbotService.GetTargetVelocity:input_type -> aimbot.TargetVelocityRequest
	4,  // 59: aimbot.AimbotService.GetTrackingState:input_type -> aimbot.Empty
	4,  // 60: aimbot.AimbotService.GetBedtopHeightProfile:input_type -> aimbot.Empty
	4,  // 61: aimbot.AimbotService.GetDimensions:input_type -> aimbot.Empty
	63, // 62: aimbot.AimbotService.GetTargetCamSN:input_type -> aimbot.GetTargetCamSNRequest
	65, // 63: aimbot.AimbotService.ReloadThinningConf:input_type -> aimbot.ReloadThinningConfRequest
	66, // 64: aimbot.AimbotService.ReloadAlmanacConf:input_type -> aimbot.ReloadAlmanacConfRequest
	67, // 65: aimbot.AimbotService.ReloadDiscriminatorConf:input_type -> aimbot.ReloadDiscriminatorConfRequest
	68, // 66: aimbot.AimbotService.ReloadModelinatorConf:input_type -> aimbot.ReloadModelinatorConfRequest
	69, // 67: aimbot.AimbotService.ReloadTVEProfile:input_type -> aimbot.ReloadTVEProfileRequest
	71, // 68: aimbot.AimbotService.GetDistanceTrackedItems:input_type -> aimbot.TrackedItemsRequest
	4,  // 69: aimbot.AimbotService.GetParticipation:input_type -> aimbot.Empty
	3,  // 70: aimbot.AimbotService.Ping:output_type -> aimbot.PongReply
	56, // 71: aimbot.AimbotService.GetBooted:output_type -> aimbot.BootedReply
	4,  // 72: aimbot.AimbotService.ArmLasers:output_type -> aimbot.Empty
	4,  // 73: aimbot.AimbotService.DisarmLasers:output_type -> aimbot.Empty
	6,  // 74: aimbot.AimbotService.GetAimbotState:output_type -> aimbot.AimbotState
	4,  // 75: aimbot.AimbotService.SetTargetingState:output_type -> aimbot.Empty
	4,  // 76: aimbot.AimbotService.StartActuationTask:output_type -> aimbot.Empty
	4,  // 77: aimbot.AimbotService.CancelActuationTask:output_type -> aimbot.Empty
	14, // 78: aimbot.AimbotService.LensSet:output_type -> aimbot.LensSetReply
	16, // 79: aimbot.AimbotService.LensGet:output_type -> aimbot.LensGetReply
	17, // 80: aimbot.AimbotService.LensGetAll:output_type -> aimbot.LensGetAllReply
	19, // 81: aimbot.AimbotService.LensAutoFocus:output_type -> aimbot.LensAutoFocusReply
	4,  // 82: aimbot.AimbotService.StopLensAutoFocus:output_type -> aimbot.Empty
	22, // 83: aimbot.AimbotService.LaserArm:output_type -> aimbot.LaserArmReply
	28, // 84: aimbot.AimbotService.LaserSet:output_type -> aimbot.LaserSetReply
	24, // 85: aimbot.AimbotService.LaserEnable:output_type -> aimbot.LaserEnableReply
	26, // 86: aimbot.AimbotService.LaserFire:output_type -> aimbot.LaserFireReply
	4,  // 87: aimbot.AimbotService.ResetLaserMetrics:output_type -> aimbot.Empty
	4,  // 88: aimbot.AimbotService.FixLaserMetrics:output_type -> aimbot.Empty
	4,  // 89: aimbot.AimbotService.BurnIdividualImage:output_type -> aimbot.Empty
	32, // 90: aimbot.AimbotService.ServoGoTo:output_type -> aimbot.ServoGoToReply
	34, // 91: aimbot.AimbotService.ServoGetPosVel:output_type -> aimbot.ServoGetPosVelReply
	36, // 92: aimbot.AimbotService.ServoGetLimits:output_type -> aimbot.ServoGetLimitsReply
	39, // 93: aimbot.AimbotService.TuningParamsUpdate:output_type -> aimbot.TuningParamsUpdateReply
	41, // 94: aimbot.AimbotService.TuningParamsGet:output_type -> aimbot.TuningParamsGetReply
	43, // 95: aimbot.AimbotService.GetLoadEstimate:output_type -> aimbot.GetLoadEstimateReply
	45, // 96: aimbot.AimbotService.GetDiagnostic:output_type -> aimbot.GetDiagnosticReply
	47, // 97: aimbot.AimbotService.ResetDevices:output_type -> aimbot.ResetDevicesReply
	49, // 98: aimbot.AimbotService.ResetScanner:output_type -> aimbot.ResetScannerReply
	4,  // 99: aimbot.AimbotService.StartAutoCalibrateCrosshair:output_type -> aimbot.Empty
	4,  // 100: aimbot.AimbotService.StartAutoCalibrateAllCrosshairs:output_type -> aimbot.Empty
	4,  // 101: aimbot.AimbotService.StopAutoCalibrate:output_type -> aimbot.Empty
	4,  // 102: aimbot.AimbotService.SetCrosshairPosition:output_type -> aimbot.Empty
	4,  // 103: aimbot.AimbotService.MoveScanner:output_type -> aimbot.Empty
	54, // 104: aimbot.AimbotService.GetAutoCalibrationProgress:output_type -> aimbot.AutoXHairCalibrationProgress
	55, // 105: aimbot.AimbotService.GetScannerStatus:output_type -> aimbot.ScannerStatusReply
	8,  // 106: aimbot.AimbotService.GetTargetVelocity:output_type -> aimbot.TargetVelocityReply
	59, // 107: aimbot.AimbotService.GetTrackingState:output_type -> aimbot.TrackingState
	61, // 108: aimbot.AimbotService.GetBedtopHeightProfile:output_type -> aimbot.TrackerBedtopHeightProfile
	62, // 109: aimbot.AimbotService.GetDimensions:output_type -> aimbot.GetDimensionsResponse
	64, // 110: aimbot.AimbotService.GetTargetCamSN:output_type -> aimbot.GetTargetCamSNResponse
	4,  // 111: aimbot.AimbotService.ReloadThinningConf:output_type -> aimbot.Empty
	4,  // 112: aimbot.AimbotService.ReloadAlmanacConf:output_type -> aimbot.Empty
	4,  // 113: aimbot.AimbotService.ReloadDiscriminatorConf:output_type -> aimbot.Empty
	4,  // 114: aimbot.AimbotService.ReloadModelinatorConf:output_type -> aimbot.Empty
	4,  // 115: aimbot.AimbotService.ReloadTVEProfile:output_type -> aimbot.Empty
	74, // 116: aimbot.AimbotService.GetDistanceTrackedItems:output_type -> aimbot.TrackedItemsResponse
	75, // 117: aimbot.AimbotService.GetParticipation:output_type -> aimbot.ParticipationResponse
	70, // [70:118] is the sub-list for method output_type
	22, // [22:70] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_init() }
func file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_init() {
	if File_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PongReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetingState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AimbotState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetVelocityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetVelocityReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserTestActuationTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageDrawActuationTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RangeDrawActuationTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActuationTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensSetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensGetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensGetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensGetAllReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensAutoFocusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LensAutoFocusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopLensAutoFocusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserArmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserArmReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserEnableReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserFireRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserFireReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserSetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BurnIdividualImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoGoToRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoGoToReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoGetPosVelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoGetPosVelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoGetLimitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServoGetLimitsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TuningParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TuningParamsUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TuningParamsUpdateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TuningParamsGetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TuningParamsGetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoadEstimateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoadEstimateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiagnosticRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiagnosticReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetDevicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetDevicesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetScannerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetScannerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerDescriptor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerTargetPosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrosshairState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoXHairCalibrationProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BootedReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchedulerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackingState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BedtopHeightProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackerBedtopHeightProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDimensionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetCamSNRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetCamSNResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadThinningConfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadAlmanacConfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadDiscriminatorConfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadModelinatorConfRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadTVEProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FixLaserMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItemsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItemHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItemsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParticipationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*ActuationTaskRequest_LaserTest)(nil),
		(*ActuationTaskRequest_ImageDraw)(nil),
		(*ActuationTaskRequest_RangeDraw)(nil),
	}
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes[35].OneofWrappers = []interface{}{
		(*TuningParam_VUint)(nil),
		(*TuningParam_VInt)(nil),
		(*TuningParam_VBool)(nil),
		(*TuningParam_VFloat)(nil),
		(*TuningParam_VString)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   74,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_goTypes,
		DependencyIndexes: file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_depIdxs,
		EnumInfos:         file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_enumTypes,
		MessageInfos:      file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_msgTypes,
	}.Build()
	File_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto = out.File
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_rawDesc = nil
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_goTypes = nil
	file_core_controls_exterminator_controllers_aimbot_process_proto_aimbot_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto

package aimbot

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AimbotService_Ping_FullMethodName                            = "/aimbot.AimbotService/Ping"
	AimbotService_GetBooted_FullMethodName                       = "/aimbot.AimbotService/GetBooted"
	AimbotService_ArmLasers_FullMethodName                       = "/aimbot.AimbotService/ArmLasers"
	AimbotService_DisarmLasers_FullMethodName                    = "/aimbot.AimbotService/DisarmLasers"
	AimbotService_GetAimbotState_FullMethodName                  = "/aimbot.AimbotService/GetAimbotState"
	AimbotService_SetTargetingState_FullMethodName               = "/aimbot.AimbotService/SetTargetingState"
	AimbotService_StartActuationTask_FullMethodName              = "/aimbot.AimbotService/StartActuationTask"
	AimbotService_CancelActuationTask_FullMethodName             = "/aimbot.AimbotService/CancelActuationTask"
	AimbotService_LensSet_FullMethodName                         = "/aimbot.AimbotService/LensSet"
	AimbotService_LensGet_FullMethodName                         = "/aimbot.AimbotService/LensGet"
	AimbotService_LensGetAll_FullMethodName                      = "/aimbot.AimbotService/LensGetAll"
	AimbotService_LensAutoFocus_FullMethodName                   = "/aimbot.AimbotService/LensAutoFocus"
	AimbotService_StopLensAutoFocus_FullMethodName               = "/aimbot.AimbotService/StopLensAutoFocus"
	AimbotService_LaserArm_FullMethodName                        = "/aimbot.AimbotService/LaserArm"
	AimbotService_LaserSet_FullMethodName                        = "/aimbot.AimbotService/LaserSet"
	AimbotService_LaserEnable_FullMethodName                     = "/aimbot.AimbotService/LaserEnable"
	AimbotService_LaserFire_FullMethodName                       = "/aimbot.AimbotService/LaserFire"
	AimbotService_ResetLaserMetrics_FullMethodName               = "/aimbot.AimbotService/ResetLaserMetrics"
	AimbotService_FixLaserMetrics_FullMethodName                 = "/aimbot.AimbotService/FixLaserMetrics"
	AimbotService_BurnIdividualImage_FullMethodName              = "/aimbot.AimbotService/BurnIdividualImage"
	AimbotService_ServoGoTo_FullMethodName                       = "/aimbot.AimbotService/ServoGoTo"
	AimbotService_ServoGetPosVel_FullMethodName                  = "/aimbot.AimbotService/ServoGetPosVel"
	AimbotService_ServoGetLimits_FullMethodName                  = "/aimbot.AimbotService/ServoGetLimits"
	AimbotService_TuningParamsUpdate_FullMethodName              = "/aimbot.AimbotService/TuningParamsUpdate"
	AimbotService_TuningParamsGet_FullMethodName                 = "/aimbot.AimbotService/TuningParamsGet"
	AimbotService_GetLoadEstimate_FullMethodName                 = "/aimbot.AimbotService/GetLoadEstimate"
	AimbotService_GetDiagnostic_FullMethodName                   = "/aimbot.AimbotService/GetDiagnostic"
	AimbotService_ResetDevices_FullMethodName                    = "/aimbot.AimbotService/ResetDevices"
	AimbotService_ResetScanner_FullMethodName                    = "/aimbot.AimbotService/ResetScanner"
	AimbotService_StartAutoCalibrateCrosshair_FullMethodName     = "/aimbot.AimbotService/StartAutoCalibrateCrosshair"
	AimbotService_StartAutoCalibrateAllCrosshairs_FullMethodName = "/aimbot.AimbotService/StartAutoCalibrateAllCrosshairs"
	AimbotService_StopAutoCalibrate_FullMethodName               = "/aimbot.AimbotService/StopAutoCalibrate"
	AimbotService_SetCrosshairPosition_FullMethodName            = "/aimbot.AimbotService/SetCrosshairPosition"
	AimbotService_MoveScanner_FullMethodName                     = "/aimbot.AimbotService/MoveScanner"
	AimbotService_GetAutoCalibrationProgress_FullMethodName      = "/aimbot.AimbotService/GetAutoCalibrationProgress"
	AimbotService_GetScannerStatus_FullMethodName                = "/aimbot.AimbotService/GetScannerStatus"
	AimbotService_GetTargetVelocity_FullMethodName               = "/aimbot.AimbotService/GetTargetVelocity"
	AimbotService_GetTrackingState_FullMethodName                = "/aimbot.AimbotService/GetTrackingState"
	AimbotService_GetBedtopHeightProfile_FullMethodName          = "/aimbot.AimbotService/GetBedtopHeightProfile"
	AimbotService_GetDimensions_FullMethodName                   = "/aimbot.AimbotService/GetDimensions"
	AimbotService_GetTargetCamSN_FullMethodName                  = "/aimbot.AimbotService/GetTargetCamSN"
	AimbotService_ReloadThinningConf_FullMethodName              = "/aimbot.AimbotService/ReloadThinningConf"
	AimbotService_ReloadAlmanacConf_FullMethodName               = "/aimbot.AimbotService/ReloadAlmanacConf"
	AimbotService_ReloadDiscriminatorConf_FullMethodName         = "/aimbot.AimbotService/ReloadDiscriminatorConf"
	AimbotService_ReloadModelinatorConf_FullMethodName           = "/aimbot.AimbotService/ReloadModelinatorConf"
	AimbotService_ReloadTVEProfile_FullMethodName                = "/aimbot.AimbotService/ReloadTVEProfile"
	AimbotService_GetDistanceTrackedItems_FullMethodName         = "/aimbot.AimbotService/GetDistanceTrackedItems"
	AimbotService_GetParticipation_FullMethodName                = "/aimbot.AimbotService/GetParticipation"
)

// AimbotServiceClient is the client API for AimbotService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AimbotServiceClient interface {
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PongReply, error)
	GetBooted(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BootedReply, error)
	// Aimbot
	ArmLasers(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	DisarmLasers(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetAimbotState(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AimbotState, error)
	SetTargetingState(ctx context.Context, in *TargetingState, opts ...grpc.CallOption) (*Empty, error)
	// Actuation Tasks
	StartActuationTask(ctx context.Context, in *ActuationTaskRequest, opts ...grpc.CallOption) (*Empty, error)
	CancelActuationTask(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// Lens
	LensSet(ctx context.Context, in *LensSetRequest, opts ...grpc.CallOption) (*LensSetReply, error)
	LensGet(ctx context.Context, in *LensGetRequest, opts ...grpc.CallOption) (*LensGetReply, error)
	LensGetAll(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LensGetAllReply, error)
	LensAutoFocus(ctx context.Context, in *LensAutoFocusRequest, opts ...grpc.CallOption) (*LensAutoFocusReply, error)
	StopLensAutoFocus(ctx context.Context, in *StopLensAutoFocusRequest, opts ...grpc.CallOption) (*Empty, error)
	// Laser
	LaserArm(ctx context.Context, in *LaserArmRequest, opts ...grpc.CallOption) (*LaserArmReply, error)
	LaserSet(ctx context.Context, in *LaserSetRequest, opts ...grpc.CallOption) (*LaserSetReply, error)
	LaserEnable(ctx context.Context, in *LaserEnableRequest, opts ...grpc.CallOption) (*LaserEnableReply, error)
	LaserFire(ctx context.Context, in *LaserFireRequest, opts ...grpc.CallOption) (*LaserFireReply, error)
	ResetLaserMetrics(ctx context.Context, in *ScannerDescriptor, opts ...grpc.CallOption) (*Empty, error)
	FixLaserMetrics(ctx context.Context, in *FixLaserMetricsRequest, opts ...grpc.CallOption) (*Empty, error)
	BurnIdividualImage(ctx context.Context, in *BurnIdividualImagesRequest, opts ...grpc.CallOption) (*Empty, error)
	// Servo
	ServoGoTo(ctx context.Context, in *ServoGoToRequest, opts ...grpc.CallOption) (*ServoGoToReply, error)
	ServoGetPosVel(ctx context.Context, in *ServoGetPosVelRequest, opts ...grpc.CallOption) (*ServoGetPosVelReply, error)
	ServoGetLimits(ctx context.Context, in *ServoGetLimitsRequest, opts ...grpc.CallOption) (*ServoGetLimitsReply, error)
	// Tuning Params
	TuningParamsUpdate(ctx context.Context, in *TuningParamsUpdateRequest, opts ...grpc.CallOption) (*TuningParamsUpdateReply, error)
	TuningParamsGet(ctx context.Context, in *TuningParamsGetRequest, opts ...grpc.CallOption) (*TuningParamsGetReply, error)
	// Load Estimate
	GetLoadEstimate(ctx context.Context, in *GetLoadEstimateRequest, opts ...grpc.CallOption) (*GetLoadEstimateReply, error)
	// Management
	GetDiagnostic(ctx context.Context, in *GetDiagnosticRequest, opts ...grpc.CallOption) (*GetDiagnosticReply, error)
	ResetDevices(ctx context.Context, in *ResetDevicesRequest, opts ...grpc.CallOption) (*ResetDevicesReply, error)
	ResetScanner(ctx context.Context, in *ResetScannerRequest, opts ...grpc.CallOption) (*ResetScannerReply, error)
	// Crosshair
	StartAutoCalibrateCrosshair(ctx context.Context, in *ScannerDescriptor, opts ...grpc.CallOption) (*Empty, error)
	StartAutoCalibrateAllCrosshairs(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	StopAutoCalibrate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	SetCrosshairPosition(ctx context.Context, in *ScannerTargetPosition, opts ...grpc.CallOption) (*Empty, error)
	MoveScanner(ctx context.Context, in *ScannerTargetPosition, opts ...grpc.CallOption) (*Empty, error)
	GetAutoCalibrationProgress(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AutoXHairCalibrationProgress, error)
	GetScannerStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ScannerStatusReply, error)
	GetTargetVelocity(ctx context.Context, in *TargetVelocityRequest, opts ...grpc.CallOption) (*TargetVelocityReply, error)
	// Tracking
	GetTrackingState(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*TrackingState, error)
	GetBedtopHeightProfile(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*TrackerBedtopHeightProfile, error)
	GetDimensions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetDimensionsResponse, error)
	// TargetCam
	GetTargetCamSN(ctx context.Context, in *GetTargetCamSNRequest, opts ...grpc.CallOption) (*GetTargetCamSNResponse, error)
	ReloadThinningConf(ctx context.Context, in *ReloadThinningConfRequest, opts ...grpc.CallOption) (*Empty, error)
	// Almanac
	ReloadAlmanacConf(ctx context.Context, in *ReloadAlmanacConfRequest, opts ...grpc.CallOption) (*Empty, error)
	ReloadDiscriminatorConf(ctx context.Context, in *ReloadDiscriminatorConfRequest, opts ...grpc.CallOption) (*Empty, error)
	ReloadModelinatorConf(ctx context.Context, in *ReloadModelinatorConfRequest, opts ...grpc.CallOption) (*Empty, error)
	// Target Velocity Estimator
	ReloadTVEProfile(ctx context.Context, in *ReloadTVEProfileRequest, opts ...grpc.CallOption) (*Empty, error)
	// Tracked Items
	GetDistanceTrackedItems(ctx context.Context, in *TrackedItemsRequest, opts ...grpc.CallOption) (*TrackedItemsResponse, error)
	GetParticipation(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ParticipationResponse, error)
}

type aimbotServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAimbotServiceClient(cc grpc.ClientConnInterface) AimbotServiceClient {
	return &aimbotServiceClient{cc}
}

func (c *aimbotServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PongReply, error) {
	out := new(PongReply)
	err := c.cc.Invoke(ctx, AimbotService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetBooted(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*BootedReply, error) {
	out := new(BootedReply)
	err := c.cc.Invoke(ctx, AimbotService_GetBooted_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ArmLasers(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ArmLasers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) DisarmLasers(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_DisarmLasers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetAimbotState(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AimbotState, error) {
	out := new(AimbotState)
	err := c.cc.Invoke(ctx, AimbotService_GetAimbotState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) SetTargetingState(ctx context.Context, in *TargetingState, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_SetTargetingState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) StartActuationTask(ctx context.Context, in *ActuationTaskRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_StartActuationTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) CancelActuationTask(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_CancelActuationTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LensSet(ctx context.Context, in *LensSetRequest, opts ...grpc.CallOption) (*LensSetReply, error) {
	out := new(LensSetReply)
	err := c.cc.Invoke(ctx, AimbotService_LensSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LensGet(ctx context.Context, in *LensGetRequest, opts ...grpc.CallOption) (*LensGetReply, error) {
	out := new(LensGetReply)
	err := c.cc.Invoke(ctx, AimbotService_LensGet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LensGetAll(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LensGetAllReply, error) {
	out := new(LensGetAllReply)
	err := c.cc.Invoke(ctx, AimbotService_LensGetAll_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LensAutoFocus(ctx context.Context, in *LensAutoFocusRequest, opts ...grpc.CallOption) (*LensAutoFocusReply, error) {
	out := new(LensAutoFocusReply)
	err := c.cc.Invoke(ctx, AimbotService_LensAutoFocus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) StopLensAutoFocus(ctx context.Context, in *StopLensAutoFocusRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_StopLensAutoFocus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LaserArm(ctx context.Context, in *LaserArmRequest, opts ...grpc.CallOption) (*LaserArmReply, error) {
	out := new(LaserArmReply)
	err := c.cc.Invoke(ctx, AimbotService_LaserArm_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LaserSet(ctx context.Context, in *LaserSetRequest, opts ...grpc.CallOption) (*LaserSetReply, error) {
	out := new(LaserSetReply)
	err := c.cc.Invoke(ctx, AimbotService_LaserSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LaserEnable(ctx context.Context, in *LaserEnableRequest, opts ...grpc.CallOption) (*LaserEnableReply, error) {
	out := new(LaserEnableReply)
	err := c.cc.Invoke(ctx, AimbotService_LaserEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) LaserFire(ctx context.Context, in *LaserFireRequest, opts ...grpc.CallOption) (*LaserFireReply, error) {
	out := new(LaserFireReply)
	err := c.cc.Invoke(ctx, AimbotService_LaserFire_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ResetLaserMetrics(ctx context.Context, in *ScannerDescriptor, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ResetLaserMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) FixLaserMetrics(ctx context.Context, in *FixLaserMetricsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_FixLaserMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) BurnIdividualImage(ctx context.Context, in *BurnIdividualImagesRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_BurnIdividualImage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ServoGoTo(ctx context.Context, in *ServoGoToRequest, opts ...grpc.CallOption) (*ServoGoToReply, error) {
	out := new(ServoGoToReply)
	err := c.cc.Invoke(ctx, AimbotService_ServoGoTo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ServoGetPosVel(ctx context.Context, in *ServoGetPosVelRequest, opts ...grpc.CallOption) (*ServoGetPosVelReply, error) {
	out := new(ServoGetPosVelReply)
	err := c.cc.Invoke(ctx, AimbotService_ServoGetPosVel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ServoGetLimits(ctx context.Context, in *ServoGetLimitsRequest, opts ...grpc.CallOption) (*ServoGetLimitsReply, error) {
	out := new(ServoGetLimitsReply)
	err := c.cc.Invoke(ctx, AimbotService_ServoGetLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) TuningParamsUpdate(ctx context.Context, in *TuningParamsUpdateRequest, opts ...grpc.CallOption) (*TuningParamsUpdateReply, error) {
	out := new(TuningParamsUpdateReply)
	err := c.cc.Invoke(ctx, AimbotService_TuningParamsUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) TuningParamsGet(ctx context.Context, in *TuningParamsGetRequest, opts ...grpc.CallOption) (*TuningParamsGetReply, error) {
	out := new(TuningParamsGetReply)
	err := c.cc.Invoke(ctx, AimbotService_TuningParamsGet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetLoadEstimate(ctx context.Context, in *GetLoadEstimateRequest, opts ...grpc.CallOption) (*GetLoadEstimateReply, error) {
	out := new(GetLoadEstimateReply)
	err := c.cc.Invoke(ctx, AimbotService_GetLoadEstimate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetDiagnostic(ctx context.Context, in *GetDiagnosticRequest, opts ...grpc.CallOption) (*GetDiagnosticReply, error) {
	out := new(GetDiagnosticReply)
	err := c.cc.Invoke(ctx, AimbotService_GetDiagnostic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ResetDevices(ctx context.Context, in *ResetDevicesRequest, opts ...grpc.CallOption) (*ResetDevicesReply, error) {
	out := new(ResetDevicesReply)
	err := c.cc.Invoke(ctx, AimbotService_ResetDevices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ResetScanner(ctx context.Context, in *ResetScannerRequest, opts ...grpc.CallOption) (*ResetScannerReply, error) {
	out := new(ResetScannerReply)
	err := c.cc.Invoke(ctx, AimbotService_ResetScanner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) StartAutoCalibrateCrosshair(ctx context.Context, in *ScannerDescriptor, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_StartAutoCalibrateCrosshair_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) StartAutoCalibrateAllCrosshairs(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_StartAutoCalibrateAllCrosshairs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) StopAutoCalibrate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_StopAutoCalibrate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) SetCrosshairPosition(ctx context.Context, in *ScannerTargetPosition, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_SetCrosshairPosition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) MoveScanner(ctx context.Context, in *ScannerTargetPosition, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_MoveScanner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetAutoCalibrationProgress(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AutoXHairCalibrationProgress, error) {
	out := new(AutoXHairCalibrationProgress)
	err := c.cc.Invoke(ctx, AimbotService_GetAutoCalibrationProgress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetScannerStatus(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ScannerStatusReply, error) {
	out := new(ScannerStatusReply)
	err := c.cc.Invoke(ctx, AimbotService_GetScannerStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetTargetVelocity(ctx context.Context, in *TargetVelocityRequest, opts ...grpc.CallOption) (*TargetVelocityReply, error) {
	out := new(TargetVelocityReply)
	err := c.cc.Invoke(ctx, AimbotService_GetTargetVelocity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetTrackingState(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*TrackingState, error) {
	out := new(TrackingState)
	err := c.cc.Invoke(ctx, AimbotService_GetTrackingState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetBedtopHeightProfile(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*TrackerBedtopHeightProfile, error) {
	out := new(TrackerBedtopHeightProfile)
	err := c.cc.Invoke(ctx, AimbotService_GetBedtopHeightProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetDimensions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetDimensionsResponse, error) {
	out := new(GetDimensionsResponse)
	err := c.cc.Invoke(ctx, AimbotService_GetDimensions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetTargetCamSN(ctx context.Context, in *GetTargetCamSNRequest, opts ...grpc.CallOption) (*GetTargetCamSNResponse, error) {
	out := new(GetTargetCamSNResponse)
	err := c.cc.Invoke(ctx, AimbotService_GetTargetCamSN_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ReloadThinningConf(ctx context.Context, in *ReloadThinningConfRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ReloadThinningConf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ReloadAlmanacConf(ctx context.Context, in *ReloadAlmanacConfRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ReloadAlmanacConf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ReloadDiscriminatorConf(ctx context.Context, in *ReloadDiscriminatorConfRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ReloadDiscriminatorConf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ReloadModelinatorConf(ctx context.Context, in *ReloadModelinatorConfRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ReloadModelinatorConf_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) ReloadTVEProfile(ctx context.Context, in *ReloadTVEProfileRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AimbotService_ReloadTVEProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetDistanceTrackedItems(ctx context.Context, in *TrackedItemsRequest, opts ...grpc.CallOption) (*TrackedItemsResponse, error) {
	out := new(TrackedItemsResponse)
	err := c.cc.Invoke(ctx, AimbotService_GetDistanceTrackedItems_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimbotServiceClient) GetParticipation(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ParticipationResponse, error) {
	out := new(ParticipationResponse)
	err := c.cc.Invoke(ctx, AimbotService_GetParticipation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AimbotServiceServer is the server API for AimbotService service.
// All implementations must embed UnimplementedAimbotServiceServer
// for forward compatibility
type AimbotServiceServer interface {
	Ping(context.Context, *PingRequest) (*PongReply, error)
	GetBooted(context.Context, *Empty) (*BootedReply, error)
	// Aimbot
	ArmLasers(context.Context, *Empty) (*Empty, error)
	DisarmLasers(context.Context, *Empty) (*Empty, error)
	GetAimbotState(context.Context, *Empty) (*AimbotState, error)
	SetTargetingState(context.Context, *TargetingState) (*Empty, error)
	// Actuation Tasks
	StartActuationTask(context.Context, *ActuationTaskRequest) (*Empty, error)
	CancelActuationTask(context.Context, *Empty) (*Empty, error)
	// Lens
	LensSet(context.Context, *LensSetRequest) (*LensSetReply, error)
	LensGet(context.Context, *LensGetRequest) (*LensGetReply, error)
	LensGetAll(context.Context, *Empty) (*LensGetAllReply, error)
	LensAutoFocus(context.Context, *LensAutoFocusRequest) (*LensAutoFocusReply, error)
	StopLensAutoFocus(context.Context, *StopLensAutoFocusRequest) (*Empty, error)
	// Laser
	LaserArm(context.Context, *LaserArmRequest) (*LaserArmReply, error)
	LaserSet(context.Context, *LaserSetRequest) (*LaserSetReply, error)
	LaserEnable(context.Context, *LaserEnableRequest) (*LaserEnableReply, error)
	LaserFire(context.Context, *LaserFireRequest) (*LaserFireReply, error)
	ResetLaserMetrics(context.Context, *ScannerDescriptor) (*Empty, error)
	FixLaserMetrics(context.Context, *FixLaserMetricsRequest) (*Empty, error)
	BurnIdividualImage(context.Context, *BurnIdividualImagesRequest) (*Empty, error)
	// Servo
	ServoGoTo(context.Context, *ServoGoToRequest) (*ServoGoToReply, error)
	ServoGetPosVel(context.Context, *ServoGetPosVelRequest) (*ServoGetPosVelReply, error)
	ServoGetLimits(context.Context, *ServoGetLimitsRequest) (*ServoGetLimitsReply, error)
	// Tuning Params
	TuningParamsUpdate(context.Context, *TuningParamsUpdateRequest) (*TuningParamsUpdateReply, error)
	TuningParamsGet(context.Context, *TuningParamsGetRequest) (*TuningParamsGetReply, error)
	// Load Estimate
	GetLoadEstimate(context.Context, *GetLoadEstimateRequest) (*GetLoadEstimateReply, error)
	// Management
	GetDiagnostic(context.Context, *GetDiagnosticRequest) (*GetDiagnosticReply, error)
	ResetDevices(context.Context, *ResetDevicesRequest) (*ResetDevicesReply, error)
	ResetScanner(context.Context, *ResetScannerRequest) (*ResetScannerReply, error)
	// Crosshair
	StartAutoCalibrateCrosshair(context.Context, *ScannerDescriptor) (*Empty, error)
	StartAutoCalibrateAllCrosshairs(context.Context, *Empty) (*Empty, error)
	StopAutoCalibrate(context.Context, *Empty) (*Empty, error)
	SetCrosshairPosition(context.Context, *ScannerTargetPosition) (*Empty, error)
	MoveScanner(context.Context, *ScannerTargetPosition) (*Empty, error)
	GetAutoCalibrationProgress(context.Context, *Empty) (*AutoXHairCalibrationProgress, error)
	GetScannerStatus(context.Context, *Empty) (*ScannerStatusReply, error)
	GetTargetVelocity(context.Context, *TargetVelocityRequest) (*TargetVelocityReply, error)
	// Tracking
	GetTrackingState(context.Context, *Empty) (*TrackingState, error)
	GetBedtopHeightProfile(context.Context, *Empty) (*TrackerBedtopHeightProfile, error)
	GetDimensions(context.Context, *Empty) (*GetDimensionsResponse, error)
	// TargetCam
	GetTargetCamSN(context.Context, *GetTargetCamSNRequest) (*GetTargetCamSNResponse, error)
	ReloadThinningConf(context.Context, *ReloadThinningConfRequest) (*Empty, error)
	// Almanac
	ReloadAlmanacConf(context.Context, *ReloadAlmanacConfRequest) (*Empty, error)
	ReloadDiscriminatorConf(context.Context, *ReloadDiscriminatorConfRequest) (*Empty, error)
	ReloadModelinatorConf(context.Context, *ReloadModelinatorConfRequest) (*Empty, error)
	// Target Velocity Estimator
	ReloadTVEProfile(context.Context, *ReloadTVEProfileRequest) (*Empty, error)
	// Tracked Items
	GetDistanceTrackedItems(context.Context, *TrackedItemsRequest) (*TrackedItemsResponse, error)
	GetParticipation(context.Context, *Empty) (*ParticipationResponse, error)
	mustEmbedUnimplementedAimbotServiceServer()
}

// UnimplementedAimbotServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAimbotServiceServer struct {
}

func (UnimplementedAimbotServiceServer) Ping(context.Context, *PingRequest) (*PongReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedAimbotServiceServer) GetBooted(context.Context, *Empty) (*BootedReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBooted not implemented")
}
func (UnimplementedAimbotServiceServer) ArmLasers(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArmLasers not implemented")
}
func (UnimplementedAimbotServiceServer) DisarmLasers(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisarmLasers not implemented")
}
func (UnimplementedAimbotServiceServer) GetAimbotState(context.Context, *Empty) (*AimbotState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAimbotState not implemented")
}
func (UnimplementedAimbotServiceServer) SetTargetingState(context.Context, *TargetingState) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTargetingState not implemented")
}
func (UnimplementedAimbotServiceServer) StartActuationTask(context.Context, *ActuationTaskRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartActuationTask not implemented")
}
func (UnimplementedAimbotServiceServer) CancelActuationTask(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelActuationTask not implemented")
}
func (UnimplementedAimbotServiceServer) LensSet(context.Context, *LensSetRequest) (*LensSetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LensSet not implemented")
}
func (UnimplementedAimbotServiceServer) LensGet(context.Context, *LensGetRequest) (*LensGetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LensGet not implemented")
}
func (UnimplementedAimbotServiceServer) LensGetAll(context.Context, *Empty) (*LensGetAllReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LensGetAll not implemented")
}
func (UnimplementedAimbotServiceServer) LensAutoFocus(context.Context, *LensAutoFocusRequest) (*LensAutoFocusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LensAutoFocus not implemented")
}
func (UnimplementedAimbotServiceServer) StopLensAutoFocus(context.Context, *StopLensAutoFocusRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopLensAutoFocus not implemented")
}
func (UnimplementedAimbotServiceServer) LaserArm(context.Context, *LaserArmRequest) (*LaserArmReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LaserArm not implemented")
}
func (UnimplementedAimbotServiceServer) LaserSet(context.Context, *LaserSetRequest) (*LaserSetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LaserSet not implemented")
}
func (UnimplementedAimbotServiceServer) LaserEnable(context.Context, *LaserEnableRequest) (*LaserEnableReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LaserEnable not implemented")
}
func (UnimplementedAimbotServiceServer) LaserFire(context.Context, *LaserFireRequest) (*LaserFireReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LaserFire not implemented")
}
func (UnimplementedAimbotServiceServer) ResetLaserMetrics(context.Context, *ScannerDescriptor) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetLaserMetrics not implemented")
}
func (UnimplementedAimbotServiceServer) FixLaserMetrics(context.Context, *FixLaserMetricsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FixLaserMetrics not implemented")
}
func (UnimplementedAimbotServiceServer) BurnIdividualImage(context.Context, *BurnIdividualImagesRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BurnIdividualImage not implemented")
}
func (UnimplementedAimbotServiceServer) ServoGoTo(context.Context, *ServoGoToRequest) (*ServoGoToReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServoGoTo not implemented")
}
func (UnimplementedAimbotServiceServer) ServoGetPosVel(context.Context, *ServoGetPosVelRequest) (*ServoGetPosVelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServoGetPosVel not implemented")
}
func (UnimplementedAimbotServiceServer) ServoGetLimits(context.Context, *ServoGetLimitsRequest) (*ServoGetLimitsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServoGetLimits not implemented")
}
func (UnimplementedAimbotServiceServer) TuningParamsUpdate(context.Context, *TuningParamsUpdateRequest) (*TuningParamsUpdateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TuningParamsUpdate not implemented")
}
func (UnimplementedAimbotServiceServer) TuningParamsGet(context.Context, *TuningParamsGetRequest) (*TuningParamsGetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TuningParamsGet not implemented")
}
func (UnimplementedAimbotServiceServer) GetLoadEstimate(context.Context, *GetLoadEstimateRequest) (*GetLoadEstimateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoadEstimate not implemented")
}
func (UnimplementedAimbotServiceServer) GetDiagnostic(context.Context, *GetDiagnosticRequest) (*GetDiagnosticReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiagnostic not implemented")
}
func (UnimplementedAimbotServiceServer) ResetDevices(context.Context, *ResetDevicesRequest) (*ResetDevicesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetDevices not implemented")
}
func (UnimplementedAimbotServiceServer) ResetScanner(context.Context, *ResetScannerRequest) (*ResetScannerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetScanner not implemented")
}
func (UnimplementedAimbotServiceServer) StartAutoCalibrateCrosshair(context.Context, *ScannerDescriptor) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAutoCalibrateCrosshair not implemented")
}
func (UnimplementedAimbotServiceServer) StartAutoCalibrateAllCrosshairs(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAutoCalibrateAllCrosshairs not implemented")
}
func (UnimplementedAimbotServiceServer) StopAutoCalibrate(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopAutoCalibrate not implemented")
}
func (UnimplementedAimbotServiceServer) SetCrosshairPosition(context.Context, *ScannerTargetPosition) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCrosshairPosition not implemented")
}
func (UnimplementedAimbotServiceServer) MoveScanner(context.Context, *ScannerTargetPosition) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MoveScanner not implemented")
}
func (UnimplementedAimbotServiceServer) GetAutoCalibrationProgress(context.Context, *Empty) (*AutoXHairCalibrationProgress, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAutoCalibrationProgress not implemented")
}
func (UnimplementedAimbotServiceServer) GetScannerStatus(context.Context, *Empty) (*ScannerStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScannerStatus not implemented")
}
func (UnimplementedAimbotServiceServer) GetTargetVelocity(context.Context, *TargetVelocityRequest) (*TargetVelocityReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTargetVelocity not implemented")
}
func (UnimplementedAimbotServiceServer) GetTrackingState(context.Context, *Empty) (*TrackingState, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTrackingState not implemented")
}
func (UnimplementedAimbotServiceServer) GetBedtopHeightProfile(context.Context, *Empty) (*TrackerBedtopHeightProfile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBedtopHeightProfile not implemented")
}
func (UnimplementedAimbotServiceServer) GetDimensions(context.Context, *Empty) (*GetDimensionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDimensions not implemented")
}
func (UnimplementedAimbotServiceServer) GetTargetCamSN(context.Context, *GetTargetCamSNRequest) (*GetTargetCamSNResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTargetCamSN not implemented")
}
func (UnimplementedAimbotServiceServer) ReloadThinningConf(context.Context, *ReloadThinningConfRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadThinningConf not implemented")
}
func (UnimplementedAimbotServiceServer) ReloadAlmanacConf(context.Context, *ReloadAlmanacConfRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadAlmanacConf not implemented")
}
func (UnimplementedAimbotServiceServer) ReloadDiscriminatorConf(context.Context, *ReloadDiscriminatorConfRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadDiscriminatorConf not implemented")
}
func (UnimplementedAimbotServiceServer) ReloadModelinatorConf(context.Context, *ReloadModelinatorConfRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadModelinatorConf not implemented")
}
func (UnimplementedAimbotServiceServer) ReloadTVEProfile(context.Context, *ReloadTVEProfileRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadTVEProfile not implemented")
}
func (UnimplementedAimbotServiceServer) GetDistanceTrackedItems(context.Context, *TrackedItemsRequest) (*TrackedItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDistanceTrackedItems not implemented")
}
func (UnimplementedAimbotServiceServer) GetParticipation(context.Context, *Empty) (*ParticipationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParticipation not implemented")
}
func (UnimplementedAimbotServiceServer) mustEmbedUnimplementedAimbotServiceServer() {}

// UnsafeAimbotServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AimbotServiceServer will
// result in compilation errors.
type UnsafeAimbotServiceServer interface {
	mustEmbedUnimplementedAimbotServiceServer()
}

func RegisterAimbotServiceServer(s grpc.ServiceRegistrar, srv AimbotServiceServer) {
	s.RegisterService(&AimbotService_ServiceDesc, srv)
}

func _AimbotService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetBooted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetBooted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetBooted_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetBooted(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ArmLasers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ArmLasers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ArmLasers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ArmLasers(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_DisarmLasers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).DisarmLasers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_DisarmLasers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).DisarmLasers(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetAimbotState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetAimbotState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetAimbotState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetAimbotState(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_SetTargetingState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TargetingState)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).SetTargetingState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_SetTargetingState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).SetTargetingState(ctx, req.(*TargetingState))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_StartActuationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActuationTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).StartActuationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_StartActuationTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).StartActuationTask(ctx, req.(*ActuationTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_CancelActuationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).CancelActuationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_CancelActuationTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).CancelActuationTask(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LensSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LensSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LensSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LensSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LensSet(ctx, req.(*LensSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LensGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LensGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LensGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LensGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LensGet(ctx, req.(*LensGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LensGetAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LensGetAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LensGetAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LensGetAll(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LensAutoFocus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LensAutoFocusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LensAutoFocus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LensAutoFocus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LensAutoFocus(ctx, req.(*LensAutoFocusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_StopLensAutoFocus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopLensAutoFocusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).StopLensAutoFocus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_StopLensAutoFocus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).StopLensAutoFocus(ctx, req.(*StopLensAutoFocusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LaserArm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaserArmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LaserArm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LaserArm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LaserArm(ctx, req.(*LaserArmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LaserSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaserSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LaserSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LaserSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LaserSet(ctx, req.(*LaserSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LaserEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaserEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LaserEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LaserEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LaserEnable(ctx, req.(*LaserEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_LaserFire_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LaserFireRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).LaserFire(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_LaserFire_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).LaserFire(ctx, req.(*LaserFireRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ResetLaserMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScannerDescriptor)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ResetLaserMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ResetLaserMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ResetLaserMetrics(ctx, req.(*ScannerDescriptor))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_FixLaserMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixLaserMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).FixLaserMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_FixLaserMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).FixLaserMetrics(ctx, req.(*FixLaserMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_BurnIdividualImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BurnIdividualImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).BurnIdividualImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_BurnIdividualImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).BurnIdividualImage(ctx, req.(*BurnIdividualImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ServoGoTo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServoGoToRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ServoGoTo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ServoGoTo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ServoGoTo(ctx, req.(*ServoGoToRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ServoGetPosVel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServoGetPosVelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ServoGetPosVel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ServoGetPosVel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ServoGetPosVel(ctx, req.(*ServoGetPosVelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ServoGetLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServoGetLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ServoGetLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ServoGetLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ServoGetLimits(ctx, req.(*ServoGetLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_TuningParamsUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TuningParamsUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).TuningParamsUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_TuningParamsUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).TuningParamsUpdate(ctx, req.(*TuningParamsUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_TuningParamsGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TuningParamsGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).TuningParamsGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_TuningParamsGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).TuningParamsGet(ctx, req.(*TuningParamsGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetLoadEstimate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoadEstimateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetLoadEstimate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetLoadEstimate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetLoadEstimate(ctx, req.(*GetLoadEstimateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetDiagnostic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiagnosticRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetDiagnostic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetDiagnostic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetDiagnostic(ctx, req.(*GetDiagnosticRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ResetDevices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetDevicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ResetDevices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ResetDevices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ResetDevices(ctx, req.(*ResetDevicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ResetScanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetScannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ResetScanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ResetScanner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ResetScanner(ctx, req.(*ResetScannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_StartAutoCalibrateCrosshair_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScannerDescriptor)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).StartAutoCalibrateCrosshair(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_StartAutoCalibrateCrosshair_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).StartAutoCalibrateCrosshair(ctx, req.(*ScannerDescriptor))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_StartAutoCalibrateAllCrosshairs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).StartAutoCalibrateAllCrosshairs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_StartAutoCalibrateAllCrosshairs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).StartAutoCalibrateAllCrosshairs(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_StopAutoCalibrate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).StopAutoCalibrate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_StopAutoCalibrate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).StopAutoCalibrate(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_SetCrosshairPosition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScannerTargetPosition)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).SetCrosshairPosition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_SetCrosshairPosition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).SetCrosshairPosition(ctx, req.(*ScannerTargetPosition))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_MoveScanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScannerTargetPosition)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).MoveScanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_MoveScanner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).MoveScanner(ctx, req.(*ScannerTargetPosition))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetAutoCalibrationProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetAutoCalibrationProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetAutoCalibrationProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetAutoCalibrationProgress(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetScannerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetScannerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetScannerStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetScannerStatus(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetTargetVelocity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TargetVelocityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetTargetVelocity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetTargetVelocity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetTargetVelocity(ctx, req.(*TargetVelocityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetTrackingState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetTrackingState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetTrackingState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetTrackingState(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetBedtopHeightProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetBedtopHeightProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetBedtopHeightProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetBedtopHeightProfile(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetDimensions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetDimensions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetDimensions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetDimensions(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetTargetCamSN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetCamSNRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetTargetCamSN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetTargetCamSN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetTargetCamSN(ctx, req.(*GetTargetCamSNRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ReloadThinningConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadThinningConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ReloadThinningConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ReloadThinningConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ReloadThinningConf(ctx, req.(*ReloadThinningConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ReloadAlmanacConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadAlmanacConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ReloadAlmanacConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ReloadAlmanacConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ReloadAlmanacConf(ctx, req.(*ReloadAlmanacConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ReloadDiscriminatorConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadDiscriminatorConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ReloadDiscriminatorConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ReloadDiscriminatorConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ReloadDiscriminatorConf(ctx, req.(*ReloadDiscriminatorConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ReloadModelinatorConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadModelinatorConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ReloadModelinatorConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ReloadModelinatorConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ReloadModelinatorConf(ctx, req.(*ReloadModelinatorConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_ReloadTVEProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadTVEProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).ReloadTVEProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_ReloadTVEProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).ReloadTVEProfile(ctx, req.(*ReloadTVEProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetDistanceTrackedItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrackedItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetDistanceTrackedItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetDistanceTrackedItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetDistanceTrackedItems(ctx, req.(*TrackedItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AimbotService_GetParticipation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimbotServiceServer).GetParticipation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AimbotService_GetParticipation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimbotServiceServer).GetParticipation(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AimbotService_ServiceDesc is the grpc.ServiceDesc for AimbotService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AimbotService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "aimbot.AimbotService",
	HandlerType: (*AimbotServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _AimbotService_Ping_Handler,
		},
		{
			MethodName: "GetBooted",
			Handler:    _AimbotService_GetBooted_Handler,
		},
		{
			MethodName: "ArmLasers",
			Handler:    _AimbotService_ArmLasers_Handler,
		},
		{
			MethodName: "DisarmLasers",
			Handler:    _AimbotService_DisarmLasers_Handler,
		},
		{
			MethodName: "GetAimbotState",
			Handler:    _AimbotService_GetAimbotState_Handler,
		},
		{
			MethodName: "SetTargetingState",
			Handler:    _AimbotService_SetTargetingState_Handler,
		},
		{
			MethodName: "StartActuationTask",
			Handler:    _AimbotService_StartActuationTask_Handler,
		},
		{
			MethodName: "CancelActuationTask",
			Handler:    _AimbotService_CancelActuationTask_Handler,
		},
		{
			MethodName: "LensSet",
			Handler:    _AimbotService_LensSet_Handler,
		},
		{
			MethodName: "LensGet",
			Handler:    _AimbotService_LensGet_Handler,
		},
		{
			MethodName: "LensGetAll",
			Handler:    _AimbotService_LensGetAll_Handler,
		},
		{
			MethodName: "LensAutoFocus",
			Handler:    _AimbotService_LensAutoFocus_Handler,
		},
		{
			MethodName: "StopLensAutoFocus",
			Handler:    _AimbotService_StopLensAutoFocus_Handler,
		},
		{
			MethodName: "LaserArm",
			Handler:    _AimbotService_LaserArm_Handler,
		},
		{
			MethodName: "LaserSet",
			Handler:    _AimbotService_LaserSet_Handler,
		},
		{
			MethodName: "LaserEnable",
			Handler:    _AimbotService_LaserEnable_Handler,
		},
		{
			MethodName: "LaserFire",
			Handler:    _AimbotService_LaserFire_Handler,
		},
		{
			MethodName: "ResetLaserMetrics",
			Handler:    _AimbotService_ResetLaserMetrics_Handler,
		},
		{
			MethodName: "FixLaserMetrics",
			Handler:    _AimbotService_FixLaserMetrics_Handler,
		},
		{
			MethodName: "BurnIdividualImage",
			Handler:    _AimbotService_BurnIdividualImage_Handler,
		},
		{
			MethodName: "ServoGoTo",
			Handler:    _AimbotService_ServoGoTo_Handler,
		},
		{
			MethodName: "ServoGetPosVel",
			Handler:    _AimbotService_ServoGetPosVel_Handler,
		},
		{
			MethodName: "ServoGetLimits",
			Handler:    _AimbotService_ServoGetLimits_Handler,
		},
		{
			MethodName: "TuningParamsUpdate",
			Handler:    _AimbotService_TuningParamsUpdate_Handler,
		},
		{
			MethodName: "TuningParamsGet",
			Handler:    _AimbotService_TuningParamsGet_Handler,
		},
		{
			MethodName: "GetLoadEstimate",
			Handler:    _AimbotService_GetLoadEstimate_Handler,
		},
		{
			MethodName: "GetDiagnostic",
			Handler:    _AimbotService_GetDiagnostic_Handler,
		},
		{
			MethodName: "ResetDevices",
			Handler:    _AimbotService_ResetDevices_Handler,
		},
		{
			MethodName: "ResetScanner",
			Handler:    _AimbotService_ResetScanner_Handler,
		},
		{
			MethodName: "StartAutoCalibrateCrosshair",
			Handler:    _AimbotService_StartAutoCalibrateCrosshair_Handler,
		},
		{
			MethodName: "StartAutoCalibrateAllCrosshairs",
			Handler:    _AimbotService_StartAutoCalibrateAllCrosshairs_Handler,
		},
		{
			MethodName: "StopAutoCalibrate",
			Handler:    _AimbotService_StopAutoCalibrate_Handler,
		},
		{
			MethodName: "SetCrosshairPosition",
			Handler:    _AimbotService_SetCrosshairPosition_Handler,
		},
		{
			MethodName: "MoveScanner",
			Handler:    _AimbotService_MoveScanner_Handler,
		},
		{
			MethodName: "GetAutoCalibrationProgress",
			Handler:    _AimbotService_GetAutoCalibrationProgress_Handler,
		},
		{
			MethodName: "GetScannerStatus",
			Handler:    _AimbotService_GetScannerStatus_Handler,
		},
		{
			MethodName: "GetTargetVelocity",
			Handler:    _AimbotService_GetTargetVelocity_Handler,
		},
		{
			MethodName: "GetTrackingState",
			Handler:    _AimbotService_GetTrackingState_Handler,
		},
		{
			MethodName: "GetBedtopHeightProfile",
			Handler:    _AimbotService_GetBedtopHeightProfile_Handler,
		},
		{
			MethodName: "GetDimensions",
			Handler:    _AimbotService_GetDimensions_Handler,
		},
		{
			MethodName: "GetTargetCamSN",
			Handler:    _AimbotService_GetTargetCamSN_Handler,
		},
		{
			MethodName: "ReloadThinningConf",
			Handler:    _AimbotService_ReloadThinningConf_Handler,
		},
		{
			MethodName: "ReloadAlmanacConf",
			Handler:    _AimbotService_ReloadAlmanacConf_Handler,
		},
		{
			MethodName: "ReloadDiscriminatorConf",
			Handler:    _AimbotService_ReloadDiscriminatorConf_Handler,
		},
		{
			MethodName: "ReloadModelinatorConf",
			Handler:    _AimbotService_ReloadModelinatorConf_Handler,
		},
		{
			MethodName: "ReloadTVEProfile",
			Handler:    _AimbotService_ReloadTVEProfile_Handler,
		},
		{
			MethodName: "GetDistanceTrackedItems",
			Handler:    _AimbotService_GetDistanceTrackedItems_Handler,
		},
		{
			MethodName: "GetParticipation",
			Handler:    _AimbotService_GetParticipation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto",
}

import datetime
import os
import re
import shutil
import threading
import time
import zipfile
from collections import defaultdict
from typing import TYPE_CHECKING, Callable, Dict, List, Optional, Set, Tuple, cast

import boto3
import streamlit as st

if TYPE_CHECKING:
    from mypy_boto3_s3 import S3ServiceResource, S3Client, ObjectSummary
else:
    ObjectSummary = object
    S3ServiceResource = object
    S3Client = object


def try_parse_date(splits: List[str]) -> Optional[datetime.date]:
    try:
        date1 = splits[-2]
        date2 = splits[-1].replace(".zip", "")
        date = " ".join([date1, date2])
        date_obj = datetime.datetime.strptime(date, "%Y-%m-%d %H-%M-%S")
        return date_obj
    except Exception:
        # This is expected as in the past we didn't include the date
        pass
    return None


def try_parse_path(path: str) -> Tuple[str, str, str, datetime.date]:
    robot = ""
    name = ""
    filename = ""
    date: datetime.date = datetime.datetime.fromtimestamp(0)
    try:
        splits = path.split("/")
        robot = splits[1]
        filename = splits[2]

        name_date_splits = splits[2].split("_")
        parsed_date = try_parse_date(name_date_splits)

        if parsed_date is not None:
            date = parsed_date
            name = "_".join(name_date_splits[2:-2])
        else:
            pre = "_".join(name_date_splits[2:-1])
            name = pre + name_date_splits[-1].replace(".zip", "")
    except Exception as ex:
        print(ex)
    return robot, name, filename, date


class Zip:
    def __init__(
        self,
        s3c: S3Client,
        s3: S3ServiceResource,
        summary: ObjectSummary,
        refresh_callback: Callable[[], None],
        exists_callback: Callable[["Zip"], bool],
        local_data_path: str,
        s3_bucket_name: str,
    ) -> None:
        self._s3c = s3c
        self._s3 = s3
        self._path = summary.key
        self._summary = summary
        self._refresh_callback = refresh_callback
        self._exists_callback = exists_callback
        self._robot, self._name, self._filename, self._date = try_parse_path(self._path)
        self._temp_path = f"/dev/shm/{self.filename}"
        self._dir_name = f"{self._robot}_{self._name}_{self.date.strftime('%Y-%m-%d_%H-%M-%S') if self.date is not None else 'nodate'}"
        self._temp_data_path = f"/dev/shm/{self._dir_name}"
        self._data_path = f"{local_data_path}/{self._dir_name}"
        self._size = summary.size
        self._downloaded_bytes = 0
        self._download_lock = threading.Lock()
        self._download_thread: Optional[threading.Thread] = None
        self._new_thread_in_progress = False
        self._current_progress = 0
        self._bar = None
        self._label = None
        self._s3_bucket_name = s3_bucket_name

    def _update_progress(self) -> float:
        exists = self._exists_callback(self)
        with self._download_lock:
            self._current_progress = 1.0 if exists else min(0.99, self._downloaded_bytes / self._size)
            if self._bar is not None and self._label is not None:
                text = f"{self._size}/{self._size}" if exists else f"{self._downloaded_bytes}/{self._size}"
                if self._downloaded_bytes >= self._size and not exists:
                    text = "Extracting..."
                elif exists:
                    text = "Downloaded"
                self._bar.progress(self._current_progress)
                self._label.text(text)
                return self._current_progress
        return 0

    def draw(self, exp: st.sidebar) -> None:
        with exp:
            st.markdown("----")
            col1, col2, col3, col4, col5 = st.columns([3, 3, 1.2, 1, 2])
            with col1:
                st.write(self._name)
            with col2:
                with self._download_lock:
                    self._label = st.text("0000000000/0000000000")
                    self._bar = st.progress(value=self._current_progress)
            with col3:
                st.button("Download", on_click=self._download_async_and_update, key=f"download_{self._path}")
            with col4:
                st.button("Delete", on_click=self.delete, key=f"delete_{self._path}")
            with col5:
                st.write(self._date.isoformat() if self._date is not None else "")
        self._update_progress()

    def _download_callback(self, bytes: int) -> None:
        with self._download_lock:
            self._downloaded_bytes += bytes

    def _download(self) -> None:
        try:
            self._s3c.download_file(self._s3_bucket_name, self.path, self._temp_path, Callback=self._download_callback)
            with zipfile.ZipFile(self._temp_path, "r") as zip_ref:
                zip_ref.extractall(self._temp_data_path)
                shutil.copytree(f"{self._temp_data_path}/{self._name}", self._data_path)
        except Exception as ex:
            print(ex)
        finally:
            try:
                os.remove(self._temp_path)
                shutil.rmtree(self._temp_data_path, ignore_errors=True)
            except OSError:
                pass
            self._refresh_callback()

    def _download_progress(self) -> float:
        with self._download_lock:
            return self._downloaded_bytes / float(self._size)

    def delete(self) -> None:
        if os.path.exists(self._data_path):
            shutil.rmtree(self._data_path, ignore_errors=True)
        self._refresh_callback()
        self._update_progress()

    def _download_async_and_update(self) -> None:
        self.delete()
        self._download_async()
        while self._update_progress() < 1:
            time.sleep(0.5)

    def _download_async(self) -> None:
        must_join = False
        with self._download_lock:
            if self._new_thread_in_progress:
                raise Exception("Thread already in progress")
            if self._download_thread is not None:
                must_join = True
                self._new_thread_in_progress = True

        if must_join:
            cast(threading.Thread, self._download_thread).join()

        with self._download_lock:
            self._download_thread = threading.Thread(target=self._download)
            self._download_thread.start()
            self._new_thread_in_progress = False

    @property
    def path(self) -> str:
        return str(self._path)

    @property
    def robot(self) -> str:
        return self._robot

    @property
    def name(self) -> str:
        return self._name

    @property
    def filename(self) -> str:
        return self._filename

    @property
    def dir_name(self) -> str:
        return self._dir_name

    @property
    def date(self) -> datetime.date:
        return self._date

    def __str__(self) -> str:
        return self.__repr__()

    def __repr__(self) -> str:
        return f"{self._robot} {self._name} {self._date.isoformat() if self._date is not None else None}"


def get_all_zips(
    refresh_callback: Callable[[], None],
    exists_callback: Callable[[Zip], bool],
    s3_bucket_name: str,
    local_data_path: str,
) -> List[Zip]:
    s3c = boto3.client("s3")
    s3 = boto3.resource("s3")
    diags = []
    for x in s3.Bucket(s3_bucket_name).objects.all():
        diags.append(Zip(s3c, s3, x, refresh_callback, exists_callback, local_data_path, s3_bucket_name))
    return diags


class LocalZip:
    def __init__(self, local_data_path: str) -> None:
        self.local_data_path = local_data_path
        self._locals: Set[str] = set()
        self._lock = threading.Lock()
        self.refresh()

    def refresh(self) -> None:
        with self._lock:
            self._locals = set(os.listdir(self.local_data_path))

    def delete(self, name: str) -> None:
        shutil.rmtree(f"{self.local_data_path}/{name}", ignore_errors=True)
        self.refresh()

    def exists(self, diag: Zip) -> bool:
        with self._lock:
            return diag.dir_name in self._locals


def run(title: str, local_data_path: str, s3_bucket_name: str) -> None:
    st.title(title)
    st.markdown("----")

    local = LocalZip(local_data_path)
    diags = get_all_zips(local.refresh, local.exists, s3_bucket_name, local_data_path)

    convert = lambda text: int(text) if text.isdigit() else text
    alphanum_key = lambda key: [convert(c) for c in re.split("([0-9]+)", key)]

    diag_dict: Dict[str, List[Zip]] = defaultdict(lambda: [])
    for d in diags:
        diag_dict[d.robot].append(d)

    sorted_pairs: List[Tuple[str, List[Zip]]] = []
    for robot, diags in diag_dict.items():
        sorted_pairs.append(
            (
                robot,
                sorted(
                    diags,
                    key=lambda key: key.date if key.date is not None else datetime.datetime.fromtimestamp(0),
                    reverse=True,
                ),
            )
        )
    sorted_pairs = sorted(sorted_pairs, key=lambda key: alphanum_key(key[0]))

    for robot, diags in sorted_pairs:
        exp = st.expander(robot, expanded=False)
        for d in diags:
            d.draw(exp)


def main() -> None:
    st.set_page_config(layout="wide")
    with st.sidebar:
        select_radio = st.radio("Navigation", ["Weeding Diagnostics", "Plant Captcha", "Simulator UI"])
    if select_radio == "Weeding Diagnostics":
        run("Carbon Weeding Diagnotics Manager", "/data/diagnostics", "carbon-diagnostics")
    elif select_radio == "Plant Captcha":
        run("Carbon Plant Captcha Manager", "/data/plant_captcha", "carbon-plant-captcha")
    elif select_radio == "Simulator UI":
        with open("/hostname", "r") as f:
            hostname = f.read().strip()

        url = f"http://{hostname}:3002"
        st.markdown(
            f"""
                <a href="{url}" target = "_self">
                    Simulator UI
                </a>
            """,
            unsafe_allow_html=True,
        )


if __name__ == "__main__":
    main()

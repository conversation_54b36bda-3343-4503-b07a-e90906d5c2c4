syntax = "proto3";

package carbon.robot_syncer.profile_sync;
option go_package = "proto/robot_syncer";

import "portal/proto/util.proto";
import "frontend/proto/profile_sync.proto";
import "proto/almanac/almanac.proto";
import "proto/thinning/thinning.proto";
import "frontend/proto/banding.proto";
import "proto/target_velocity_estimator/target_velocity_estimator.proto";
import "category/proto/category.proto";

message GetProfileSyncDataRequest { string robot_serial = 1; }

message GetProfileSyncDataResponse {
  map<string, frontend.profile_sync.ProfileSyncData> profiles = 1;
}

message UploadProfileRequest {
  int64 last_update_time_ms = 1;
  string robot_serial = 2;
  oneof profile {
    carbon.aimbot.almanac.AlmanacConfig almanac = 3;
    carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
    carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
    carbon.frontend.banding.BandingDef banding = 6;
    carbon.thinning.ConfigDefinition thinning = 7;
    carbon.aimbot.target_velocity_estimator.TVEProfile
        target_velocity_estimator = 8;
    carbon.category.CategoryCollection categoryCollection = 9;
    carbon.category.Category category = 10;
  }
}

message GetProfileRequest { string uuid = 1; }

message GetProfileResponse {
  oneof profile {
    carbon.aimbot.almanac.AlmanacConfig almanac = 1;
    carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
    carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
    carbon.frontend.banding.BandingDef banding = 4;
    carbon.thinning.ConfigDefinition thinning = 5;
    carbon.aimbot.target_velocity_estimator.TVEProfile
        target_velocity_estimator = 6;
    carbon.category.CategoryCollection categoryCollection = 7;
    carbon.category.Category category = 8;
  }
}

message DeleteProfileRequest { string uuid = 1; }

message PurgeProfileRequest { string uuid = 1; }

service RoSyProfileSyncService {
  rpc GetProfileSyncData(GetProfileSyncDataRequest)
      returns (GetProfileSyncDataResponse);
  rpc UploadProfile(UploadProfileRequest) returns (carbon.portal.util.Empty);
  rpc GetProfile(GetProfileRequest) returns (GetProfileResponse);
  rpc DeleteProfile(DeleteProfileRequest) returns (carbon.portal.util.Empty);
  rpc PurgeProfile(PurgeProfileRequest) returns (carbon.portal.util.Empty);
}

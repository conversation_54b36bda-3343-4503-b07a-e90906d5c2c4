import argparse

from deeplearning.test.standard_jobs_test import call, get_prefix


def test(disable_cache_proxy: bool = False) -> None:
    prefix = get_prefix(disable_cache_proxy)
    call(
        f"{prefix} python -m deeplearning.scripts.deepweed.embed --dataset-id f44953a2-42a9-46da-8125-4bc3760f2cd3 --model-id fut-20241123-h3yab84itn --fast-run"
    )

    call(
        f"{prefix} python -m deeplearning.scripts.fewshot.embed --dataset-id f44953a2-42a9-46da-8125-4bc3760f2cd3 --model-id fst-20241231-jr3snx9plu --fast-run"
    )

    call(
        f"{prefix} python -m deeplearning.scripts.comparison.embed --dataset-id f44953a2-42a9-46da-8125-4bc3760f2cd3 --fast-run"
    )

    call(
        f"{prefix} python -m deeplearning.scripts.plant_captcha.embed --model-id fut-20241123-h3yab84itn --plant-captcha-s3-path s3://carbon-plant-captcha/plant-captcha/slayer1/plant-captcha_slayer1_Onion_4_2024-12-09_17-42-30/ --fast-run"
    )


if __name__ == "__main__":
    p = argparse.ArgumentParser()
    p.add_argument("--disable-cache-proxy", action="store_true")
    args = p.parse_args()
    test(args.disable_cache_proxy)

from deeplearning.scripts.embeddings.performance_on_uuids import get_performance


def test_get_performance() -> None:
    items = {
        "111": [{"label": "crop", "prediction": "crop"}, {"label": "weed", "prediction": "crop"}],
        "222": [{"label": "weed", "prediction": "weed"}],
    }

    performance_results = get_performance(items)

    assert abs(performance_results["per_instance_accuracy"] - (2 / 3)) < 1e-6
    assert abs(performance_results["per_point_min_accuracy"] - 0.5) < 1e-6
    assert abs(performance_results["per_point_max_accuracy"] - 1.0) < 1e-6
    assert abs(performance_results["per_point_mean_accuracy"] - 0.75) < 1e-6

from deeplearning.scripts.datasets.get_uuids import does_square_a_overlap_circle_b


def test_does_square_a_overlap_circle_b_no() -> None:
    point_a = {"x": 500, "y": 600, "radius": 300}
    point_b = {"x": 750, "y": 600, "radius": 50}
    assert not does_square_a_overlap_circle_b(point_a, point_b)


def test_does_square_a_overlap_circle_b_yes() -> None:
    point_a = {"x": 500, "y": 600, "radius": 300}
    point_b = {"x": 750, "y": 600, "radius": 100}
    assert does_square_a_overlap_circle_b(point_a, point_b)


def test_does_square_a_overlap_circle_b_no_2() -> None:
    point_a = {"x": 500, "y": 500, "radius": 300}
    point_b = {"x": 750, "y": 750, "radius": 50}
    assert not does_square_a_overlap_circle_b(point_a, point_b)


def test_does_square_a_overlap_circle_b_yes_2() -> None:
    point_a = {"x": 500, "y": 500, "radius": 300}
    point_b = {"x": 750, "y": 750, "radius": 100}
    assert does_square_a_overlap_circle_b(point_a, point_b)

import os

import torch

from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDatapointPrediction,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
)


def generate_test_embedding_dataset() -> EmbeddingDataset:
    filepath = "test-filepath.h5"

    if os.path.exists(filepath):
        os.remove(filepath)
    metadata = EmbeddingDatasetMetadata(model_id="test-model-id", embedding_size=4, dataset_id="test-dataset-id")

    dataset = EmbeddingDataset(filepath, metadata)

    meta = [
        EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=i,
            y=0,
        )
        for i in range(4)
    ]

    embedding_tensor1 = torch.tensor([1.0, 2.0, 3.0, 4.0])

    embedding_tensor2 = torch.tensor([3.0, 4.0, 5.0, 6.0])

    embedding_tensor3 = torch.tensor([6.0, 9.0, 5.0, 6.0])

    embedding_tensor4 = torch.tensor([6.0, 4.0, 4.0, 6.0])

    datapoint1 = EmbeddingDatapoint(embedding=embedding_tensor1, metadata=meta[0],)

    predictions_metadata2 = [
        EmbeddingDatapointPrediction(
            plant_score=0.2,
            weed_score=0.23,
            crop_score=0.22,
            category_scores={"broadleaf": 0.25, "grass": 0.26},
            distance_mm=2.3,
        ),
        EmbeddingDatapointPrediction(
            plant_score=0.3,
            weed_score=0.31,
            crop_score=0.32,
            category_scores={"broadleaf": 0.37, "grass": 0.36},
            distance_mm=3.3,
        ),
    ]

    predictions_embeddings2 = [
        torch.tensor([2.0, 2.0, 2.0, 4.0]),
        torch.tensor([2.5, 2.5, 2.5, 4.5]),
    ]

    datapoint2 = EmbeddingDatapoint(
        embedding=embedding_tensor2,
        metadata=meta[1],
        predictions_metadata=predictions_metadata2,
        predictions_embeddings=predictions_embeddings2,
    )

    predictions_metadata3 = [
        EmbeddingDatapointPrediction(
            plant_score=0.4,
            weed_score=0.41,
            crop_score=0.42,
            category_scores={"broadleaf": 0.45, "grass": 0.46},
            distance_mm=2.32,
        ),
    ]

    predictions_embeddings3 = [
        torch.tensor([2.99, 2.99, 2.99, 4.99]),
    ]

    datapoint3 = EmbeddingDatapoint(
        embedding=embedding_tensor3,
        metadata=meta[2],
        predictions_metadata=predictions_metadata3,
        predictions_embeddings=predictions_embeddings3,
    )

    predictions_metadata4 = [
        EmbeddingDatapointPrediction(
            plant_score=0.5,
            weed_score=0.51,
            crop_score=0.52,
            category_scores={"broadleaf": 0.55, "grass": 0.56},
            distance_mm=2.5,
        ),
    ]

    predictions_embeddings4 = [
        torch.tensor([2.15, 2.15, 2.15, 4.15]),
    ]

    datapoint4 = EmbeddingDatapoint(
        embedding=embedding_tensor4,
        metadata=meta[3],
        predictions_metadata=predictions_metadata4,
        predictions_embeddings=predictions_embeddings4,
    )

    dataset.append([datapoint1, datapoint2, datapoint3, datapoint4])

    return dataset

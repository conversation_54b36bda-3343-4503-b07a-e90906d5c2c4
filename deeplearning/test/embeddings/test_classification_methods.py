import unittest
from functools import partial

import pytest
import torch


class TestClassifiers(unittest.TestCase):
    @pytest.mark.dl_unit_test
    def setUp(self) -> None:
        import fewshot

        from deeplearning.scripts.embeddings.classifiers.centroids import CentroidClassifier
        from deeplearning.scripts.embeddings.classifiers.knn import KNNClassifier
        from deeplearning.scripts.embeddings.classifiers.medoids import MedoidClassifier
        from deeplearning.scripts.embeddings.classifiers.deepweed import DeepweedClassifier
        from deeplearning.scripts.embeddings.classifiers.distance_sums import DistanceSumClassifier

        self.encoder = fewshot.encoders.EmbeddingsReader(embedding_size=2)
        self.centroid_model = partial(CentroidClassifier, encoder=self.encoder)
        self.medoid_model = partial(MedoidClassifier, encoder=self.encoder)
        self.knn_model = partial(KNNClassifier, encoder=self.encoder)
        self.deepweed_model = partial(DeepweedClassifier)
        self.distance_sum_model = partial(DistanceSumClassifier, encoder=self.encoder)

    @pytest.mark.dl_unit_test
    def test_centroids(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(tensor=torch.tensor([1, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0, 0.8]), identifier=None, label="B", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0.8, 0.9]), identifier=None, label="B", role=DatapointRole.QUERY),
            ]
        )

        logits = self.centroid_model()(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("B"))

    @pytest.mark.dl_unit_test
    def test_medoids(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(tensor=torch.tensor([1, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.7, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0, 0.8]), identifier=None, label="B", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0.8, 0.8]), identifier=None, label="A", role=DatapointRole.QUERY),
            ]
        )

        logits = self.medoid_model()(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("A"))

    @pytest.mark.dl_unit_test
    def test_knn(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(tensor=torch.tensor([1, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.7, 0.2]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.3, 0.6]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.1, 0.8]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0, 0.8]), identifier=None, label="B", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0.8, 0.8]), identifier=None, label="B", role=DatapointRole.QUERY),
            ]
        )

        logits = self.knn_model()(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("B"))

    @pytest.mark.dl_unit_test
    def test_distance_sums(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(tensor=torch.tensor([0.8, 0.4]), identifier=None, label="A", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0, 0.2]), identifier=None, label="B", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0.5, 0.6]), identifier=None, label="B", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([0.8, 0.8]), identifier=None, label="B", role=DatapointRole.QUERY),
                Datapoint(tensor=torch.tensor([1, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.7, 0.2]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.3, 0.6]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.1, 0.8]), identifier=None, label="B", role=DatapointRole.SUPPORT),
            ]
        )

        logits = self.distance_sum_model()(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[3].argmax(), episode.class_names.index("B"))

        logits_cosine = self.distance_sum_model(metric="cosine")(episode)[0]
        self.assertEqual(logits_cosine[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits_cosine[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits_cosine[2].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits_cosine[3].argmax(), episode.class_names.index("B"))

    @pytest.mark.dl_unit_test
    def test_centroids_with_predictions(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(
                    tensor=torch.tensor([1, 0]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.SUPPORT,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT, metadata=None
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0.85, 0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0, 1.1])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0.9]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([1, -0.2])},
                ),
            ]
        )

        logits = self.centroid_model(get_embedding_from_prediction=True)(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("A"))

    @pytest.mark.dl_unit_test
    def test_medoids_with_predictions(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(tensor=torch.tensor([1, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.7, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(
                    tensor=torch.tensor([0.8, 0]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0, 1])},
                ),
                Datapoint(
                    tensor=torch.tensor([0, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0.85, 0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0.8]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0.74, 0])},
                ),
            ]
        )

        logits = self.medoid_model(get_embedding_from_prediction=True)(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("A"))

    @pytest.mark.dl_unit_test
    def test_knn_with_predictions(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(tensor=torch.tensor([1, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.7, 0.2]), identifier=None, label="A", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0, 1]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.3, 0.6]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(tensor=torch.tensor([0.1, 0.8]), identifier=None, label="B", role=DatapointRole.SUPPORT),
                Datapoint(
                    tensor=torch.tensor([0.8, 0]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0, 1])},
                ),
                Datapoint(
                    tensor=torch.tensor([0, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0, 1])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([0.7, 0])},
                ),
            ]
        )

        logits = self.knn_model(get_embedding_from_prediction=True)(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("B"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("A"))

    @pytest.mark.dl_unit_test
    def test_deepweed(self) -> None:
        from fewshot.dataset_types import Datapoint, DatapointRole, Episode

        episode = Episode(
            datapoints=[
                Datapoint(
                    tensor=torch.tensor([1, 0]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.SUPPORT,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0]), identifier=None, label="A", role=DatapointRole.SUPPORT, metadata=None
                ),
                Datapoint(
                    tensor=torch.tensor([0.7, 0.2]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.SUPPORT,
                    metadata=None,
                ),
                Datapoint(
                    tensor=torch.tensor([0, 1]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.SUPPORT,
                    metadata={"prediction_label": "B", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.3, 0.6]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.SUPPORT,
                    metadata={"prediction_label": "B", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.1, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.SUPPORT,
                    metadata={"prediction_label": "B", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0]),
                    identifier=None,
                    label="A",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "A", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
                Datapoint(
                    tensor=torch.tensor([0.8, 0.8]),
                    identifier=None,
                    label="B",
                    role=DatapointRole.QUERY,
                    metadata={"prediction_label": "B", "prediction_embedding": torch.tensor([1.0, 2.0])},
                ),
            ]
        )

        logits = self.deepweed_model()(episode)[0]
        self.assertEqual(logits[0].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[1].argmax(), episode.class_names.index("A"))
        self.assertEqual(logits[2].argmax(), episode.class_names.index("B"))


if __name__ == "__main__":
    unittest.main()

import os
import unittest

import pytest
import torch

from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDatapointPrediction,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
    EmbeddingLookupTable,
    EmbeddingLookupTables,
)


class TestEmbeddingDataset(unittest.TestCase):
    filepath = "test-filepath.h5"
    metadata = EmbeddingDatasetMetadata(
        model_id="test-model-id", embedding_size=16, dataset_id="test-dataset-id", version_id="123-123-123"
    )

    embedding_tensor = torch.tensor(
        [1.5, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0]
    )

    def setUp(self) -> None:
        if os.path.exists(self.filepath):
            os.remove(self.filepath)

    def tearDown(self) -> None:
        if os.path.exists(self.filepath):
            os.remove(self.filepath)

    @pytest.mark.dl_unit_test
    def test_initialization(self) -> None:
        EmbeddingDataset(self.filepath, self.metadata)
        assert os.path.exists(self.filepath)

    @pytest.mark.dl_unit_test
    def test_metadata(self) -> None:
        dataset = EmbeddingDataset(self.filepath, self.metadata)
        assert dataset.metadata == self.metadata

    @pytest.mark.dl_unit_test
    def test_append(self) -> None:
        dataset = EmbeddingDataset(self.filepath, self.metadata)

        metadata = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=0,
            y=0,
        )

        predictions_metadata = [
            EmbeddingDatapointPrediction(
                plant_score=0.2,
                weed_score=0.21,
                crop_score=0.22,
                category_scores={"d": 0.25, "e": 0.26},
                distance_mm=2.3,
            ),
            EmbeddingDatapointPrediction(
                plant_score=0.3,
                weed_score=0.31,
                crop_score=0.32,
                category_scores={"d": 0.35, "e": 0.36},
                distance_mm=3.3,
            ),
        ]

        predictions_embeddings = [
            torch.tensor([2.0, 2.0, 2.0, 4.0, 4.0, 4.0, 4.0, 8.0, 8.0, 8.0, 8.0, 8.0, 88.0, 89.0, 15.0, 16.0]),
            torch.tensor([2.5, 2.5, 2.5, 4.5, 4.5, 4.5, 4.5, 8.5, 8.5, 8.5, 8.5, 8.5, 88.5, 89.5, 15.5, 16.5]),
        ]

        datapoint = EmbeddingDatapoint(
            embedding=self.embedding_tensor,
            metadata=metadata,
            predictions_metadata=predictions_metadata,
            predictions_embeddings=predictions_embeddings,
        )

        dataset.append(datapoint)

        retrieved_item = dataset[0]
        assert torch.equal(retrieved_item.embedding, self.embedding_tensor)
        assert retrieved_item.metadata == metadata
        assert len(retrieved_item.predictions_embeddings) == 2
        assert retrieved_item.predictions_metadata == predictions_metadata
        assert all(
            [
                torch.equal(retrieved_predictions_embeddings, predictions_embeddings[i])
                for i, retrieved_predictions_embeddings in enumerate(retrieved_item.predictions_embeddings)
            ]
        )

    @pytest.mark.dl_unit_test
    def test_append_without_predictions(self) -> None:
        dataset = EmbeddingDataset(self.filepath, self.metadata)

        metadata = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=0,
            y=0,
        )

        datapoint = EmbeddingDatapoint(embedding=self.embedding_tensor, metadata=metadata)

        dataset.append(datapoint)

    @pytest.mark.dl_unit_test
    def test_append_list(self) -> None:
        dataset = EmbeddingDataset(self.filepath, self.metadata)

        metadata = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=0,
            y=0,
        )

        datapoint = EmbeddingDatapoint(embedding=self.embedding_tensor, metadata=metadata,)

        predictions_metadata_2 = [
            EmbeddingDatapointPrediction(
                plant_score=0.2,
                weed_score=0.21,
                crop_score=0.22,
                category_scores={"d": 0.25, "e": 0.26},
                distance_mm=2.3,
            ),
            EmbeddingDatapointPrediction(
                plant_score=0.3,
                weed_score=0.31,
                crop_score=0.32,
                category_scores={"d": 0.35, "e": 0.36},
                distance_mm=3.3,
            ),
        ]

        predictions_embeddings_2 = [
            torch.tensor([2.0, 2.0, 2.0, 4.0, 4.0, 4.0, 4.0, 8.0, 8.0, 8.0, 8.0, 8.0, 88.0, 89.0, 15.0, 16.0]),
            torch.tensor([2.5, 2.5, 2.5, 4.5, 4.5, 4.5, 4.5, 8.5, 8.5, 8.5, 8.5, 8.5, 88.5, 89.5, 15.5, 16.5]),
        ]

        datapoint2 = EmbeddingDatapoint(
            embedding=self.embedding_tensor,
            metadata=metadata,
            predictions_metadata=predictions_metadata_2,
            predictions_embeddings=predictions_embeddings_2,
        )

        predictions_metadata_3 = [
            EmbeddingDatapointPrediction(
                plant_score=0.4,
                weed_score=0.41,
                crop_score=0.42,
                category_scores={"d": 0.45, "e": 0.46},
                distance_mm=2.3,
            ),
        ]

        predictions_embeddings_3 = [
            torch.tensor(
                [2.99, 2.99, 2.99, 4.99, 4.99, 4.99, 4.99, 8.99, 8.99, 8.99, 8.99, 8.99, 88.99, 89.99, 15.99, 16.99]
            ),
        ]

        datapoint3 = EmbeddingDatapoint(
            embedding=self.embedding_tensor,
            metadata=metadata,
            predictions_metadata=predictions_metadata_3,
            predictions_embeddings=predictions_embeddings_3,
        )

        dataset.append([datapoint, datapoint2, datapoint3])

        retrieved_item_1 = dataset[0]
        assert torch.equal(retrieved_item_1.embedding, self.embedding_tensor)
        assert retrieved_item_1.metadata == metadata
        assert len(retrieved_item_1.predictions_embeddings) == 0
        assert len(retrieved_item_1.predictions_metadata) == 0

        retrieved_item_2 = dataset[1]
        assert torch.equal(retrieved_item_2.embedding, self.embedding_tensor)
        assert retrieved_item_2.metadata == metadata
        assert len(retrieved_item_2.predictions_embeddings) == 2
        assert retrieved_item_2.predictions_metadata == predictions_metadata_2
        assert all(
            [
                torch.equal(retrieved_predictions_embeddings, predictions_embeddings_2[i])
                for i, retrieved_predictions_embeddings in enumerate(retrieved_item_2.predictions_embeddings)
            ]
        )

        retrieved_item_3 = dataset[2]
        assert torch.equal(retrieved_item_3.embedding, self.embedding_tensor)
        assert retrieved_item_3.metadata == metadata
        assert len(retrieved_item_3.predictions_embeddings) == 1
        assert retrieved_item_3.predictions_metadata == predictions_metadata_3
        assert all(
            [
                torch.equal(retrieved_predictions_embeddings, predictions_embeddings_3[i])
                for i, retrieved_predictions_embeddings in enumerate(retrieved_item_3.predictions_embeddings)
            ]
        )

    @pytest.mark.dl_unit_test
    def test_append_twice(self) -> None:
        dataset = EmbeddingDataset(self.filepath, self.metadata)

        metadata = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=0,
            y=0,
        )

        metadata2 = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=1,
            y=0,
        )

        metadata3 = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=0,
            geohash="aaaa",
            point_category_id="aaaa",
            image_crop_id="test-image-crop-id",
            x=2,
            y=0,
        )

        datapoint = EmbeddingDatapoint(embedding=self.embedding_tensor, metadata=metadata,)

        predictions_metadata_2 = [
            EmbeddingDatapointPrediction(
                plant_score=0.2,
                weed_score=0.21,
                crop_score=0.22,
                category_scores={"d": 0.25, "e": 0.26},
                distance_mm=2.3,
            ),
            EmbeddingDatapointPrediction(
                plant_score=0.3,
                weed_score=0.31,
                crop_score=0.32,
                category_scores={"d": 0.35, "e": 0.36},
                distance_mm=3.3,
            ),
        ]

        predictions_embeddings_2 = [
            torch.tensor([2.0, 2.0, 2.0, 4.0, 4.0, 4.0, 4.0, 8.0, 8.0, 8.0, 8.0, 8.0, 88.0, 89.0, 15.0, 16.0]),
            torch.tensor([2.5, 2.5, 2.5, 4.5, 4.5, 4.5, 4.5, 8.5, 8.5, 8.5, 8.5, 8.5, 88.5, 89.5, 15.5, 16.5]),
        ]

        datapoint2 = EmbeddingDatapoint(
            embedding=2 * self.embedding_tensor,
            metadata=metadata2,
            predictions_metadata=predictions_metadata_2,
            predictions_embeddings=predictions_embeddings_2,
        )

        dataset.append([datapoint, datapoint2])

        predictions_metadata_3 = [
            EmbeddingDatapointPrediction(
                plant_score=0.4,
                weed_score=0.41,
                crop_score=0.42,
                category_scores={"d": 0.45, "e": 0.46},
                distance_mm=2.3,
            ),
        ]

        predictions_embeddings_3 = [
            torch.tensor(
                [2.99, 2.99, 2.99, 4.99, 4.99, 4.99, 4.99, 8.99, 8.99, 8.99, 8.99, 8.99, 88.99, 89.99, 15.99, 16.99]
            ),
        ]

        datapoint3 = EmbeddingDatapoint(
            embedding=3 * self.embedding_tensor,
            metadata=metadata3,
            predictions_metadata=predictions_metadata_3,
            predictions_embeddings=predictions_embeddings_3,
        )

        dataset.append(datapoint3)

        retrieved_item_1 = dataset[0]
        assert torch.equal(retrieved_item_1.embedding, self.embedding_tensor)
        assert retrieved_item_1.metadata == metadata
        assert len(retrieved_item_1.predictions_embeddings) == 0
        assert len(retrieved_item_1.predictions_metadata) == 0

        retrieved_item_2 = dataset[1]
        assert torch.equal(retrieved_item_2.embedding, 2 * self.embedding_tensor)
        assert retrieved_item_2.metadata == metadata2
        assert len(retrieved_item_2.predictions_embeddings) == 2
        assert retrieved_item_2.predictions_metadata == predictions_metadata_2
        assert all(
            [
                torch.equal(retrieved_predictions_embeddings, predictions_embeddings_2[i])
                for i, retrieved_predictions_embeddings in enumerate(retrieved_item_2.predictions_embeddings)
            ]
        )

        retrieved_item_3 = dataset[2]
        assert torch.equal(retrieved_item_3.embedding, 3 * self.embedding_tensor)
        assert retrieved_item_3.metadata == metadata3
        assert len(retrieved_item_3.predictions_embeddings) == 1
        assert retrieved_item_3.predictions_metadata == predictions_metadata_3
        assert all(
            [
                torch.equal(retrieved_predictions_embeddings, predictions_embeddings_3[i])
                for i, retrieved_predictions_embeddings in enumerate(retrieved_item_3.predictions_embeddings)
            ]
        )


def cleanup_table(filepath: str) -> None:
    if os.path.exists(filepath):
        os.remove(filepath)


class TestEmbeddingLookupTable(unittest.TestCase):
    filepath = "test-lookup.h5"

    def setUp(self) -> None:
        cleanup_table(self.filepath)

    @pytest.mark.dl_unit_test
    def test_happy_path(self) -> None:
        embedding_lookup_table = EmbeddingLookupTable(self.filepath, "full")

        assert embedding_lookup_table.embedding_type == "full"

        embedding_asdf = torch.tensor([1, 2, 3])
        embedding_qwer = torch.tensor([3, 4, 5])
        embedding_zxcv = torch.tensor([5, 6, 7])

        embedding_lookup_table.set("asdf", embedding_asdf)
        embedding_lookup_table.set("qwer", embedding_asdf)
        embedding_lookup_table.set("qwer", embedding_qwer)
        embedding_lookup_table.set("zxcv", embedding_zxcv)

        asdf = embedding_lookup_table.get("asdf")
        qwer = embedding_lookup_table.get("qwer")
        zxcv = embedding_lookup_table.get("zxcv")
        qw = embedding_lookup_table.get("qw")

        assert asdf is not None
        assert qwer is not None
        assert zxcv is not None
        assert qw is None

        assert torch.equal(asdf, embedding_asdf)
        assert torch.equal(qwer, embedding_qwer)
        assert torch.equal(zxcv, embedding_zxcv)

        items = embedding_lookup_table.list()
        assert len(items) == 3
        assert set(items) == set(["asdf", "qwer", "zxcv"])


class TestEmbeddingLookupTables(unittest.TestCase):
    filepath_1 = "test-filepath-1.h5"
    filepath_2 = "test-filepath-2.h5"
    filepath_3 = "test-filepath-3.h5"

    def setUp(self) -> None:
        cleanup_table(self.filepath_1)
        cleanup_table(self.filepath_2)
        cleanup_table(self.filepath_3)

    @pytest.mark.dl_unit_test
    def test_happy_path(self) -> None:
        embedding_lookup_table_1 = EmbeddingLookupTable(self.filepath_1, "full")
        embedding_lookup_table_2 = EmbeddingLookupTable(self.filepath_2, "full")

        embedding_asdf = torch.tensor([1, 2, 3])
        embedding_qwer = torch.tensor([3, 4, 5])
        embedding_zxcv = torch.tensor([5, 6, 7])

        embedding_lookup_table_1.set("asdf", embedding_asdf)
        embedding_lookup_table_2.set("qwer", embedding_qwer)
        embedding_lookup_table_2.set("zxcv", embedding_zxcv)

        embedding_lookup_tables = EmbeddingLookupTables([self.filepath_1, self.filepath_2])

        actual_asdf = embedding_lookup_tables.get("asdf")
        actual_qwer = embedding_lookup_tables.get("qwer")
        actual_zxcv = embedding_lookup_tables.get("zxcv")
        assert actual_asdf is not None
        assert actual_qwer is not None
        assert actual_zxcv is not None

        assert torch.equal(actual_asdf, embedding_asdf)
        assert torch.equal(actual_qwer, embedding_qwer)
        assert torch.equal(actual_zxcv, embedding_zxcv)
        assert embedding_lookup_tables.get("uiop") is None

    @pytest.mark.dl_unit_test
    def test_save_load(self) -> None:
        embedding_lookup_table_1 = EmbeddingLookupTable(self.filepath_1, "full")
        embedding_lookup_table_2 = EmbeddingLookupTable(self.filepath_2, "full")

        embedding_asdf = torch.tensor([1, 2, 3])
        embedding_zxcv = torch.tensor([5, 6, 7])

        embedding_lookup_table_1.set("asdf", embedding_asdf)
        embedding_lookup_table_2.set("zxcv", embedding_zxcv)

        embedding_lookup_tables = EmbeddingLookupTables([self.filepath_1, self.filepath_2])

        embedding_lookup_tables.save(self.filepath_3)
        embedding_lookup_tables_2 = EmbeddingLookupTables.load(self.filepath_3)

        actual_asdf = embedding_lookup_tables_2.get("asdf")
        actual_zxcv = embedding_lookup_tables_2.get("zxcv")

        assert actual_asdf is not None
        assert actual_zxcv is not None

        assert torch.equal(actual_asdf, embedding_asdf)
        assert torch.equal(actual_zxcv, embedding_zxcv)


if __name__ == "__main__":
    unittest.main()

import random
from typing import Any, Dict, cast

import pytest
import torch

from deeplearning.test.embeddings.utils import generate_test_embedding_dataset


@pytest.mark.dl_unit_test
def test_get_max_category() -> None:
    from deeplearning.scripts.embeddings.datasets.split_support_query import get_max_category

    categories = {"a": 0.9, "b": 0.99, "c": 0.5}
    category = get_max_category(categories)

    assert category == "b"


@pytest.mark.dl_unit_test
def test_find_closest_prediction() -> None:
    from deeplearning.scripts.embeddings.datasets.split_support_query import find_closest_prediction

    dataset = generate_test_embedding_dataset()
    datapoints = [dataset[i] for i in range(len(dataset))]

    weed_classes = ["broadleaf", "grass", "offshoot", "purslane"]

    label, embedding = find_closest_prediction(datapoints[0], 0.05, 5.0, weed_classes=weed_classes)
    assert label is None
    assert embedding is None

    label, embedding = find_closest_prediction(datapoints[1], 0.05, 5.0, weed_classes=weed_classes)
    assert label == "weed"
    assert embedding is not None
    assert torch.equal(embedding, torch.tensor([2.0, 2.0, 2.0, 4.0]))

    label, embedding = find_closest_prediction(datapoints[1], 0.22, 5.0, weed_classes=weed_classes)
    assert label == "crop"
    assert embedding is not None
    assert torch.equal(embedding, torch.tensor([2.5, 2.5, 2.5, 4.5]))

    label, embedding = find_closest_prediction(datapoints[1], 0.22, 2.7, weed_classes=weed_classes)
    assert label is None
    assert embedding is None

    label, embedding = find_closest_prediction(datapoints[0], 0.05, 5.0, weed_classes=weed_classes, multiclass=True)
    assert label is None
    assert embedding is None

    label, embedding = find_closest_prediction(datapoints[1], 0.05, 5.0, weed_classes=weed_classes, multiclass=True)
    assert label == "grass"
    assert embedding is not None
    assert torch.equal(embedding, torch.tensor([2.0, 2.0, 2.0, 4.0]))

    label, embedding = find_closest_prediction(datapoints[1], 0.22, 5.0, weed_classes=weed_classes, multiclass=True)
    assert label == "crop"
    assert embedding is not None
    assert torch.equal(embedding, torch.tensor([2.5, 2.5, 2.5, 4.5]))

    label, embedding = find_closest_prediction(datapoints[1], 0.22, 2.7, weed_classes=weed_classes, multiclass=True)
    assert label is None
    assert embedding is None


@pytest.mark.dl_unit_test
def test_split_into_roles() -> None:
    import fewshot
    from deeplearning.scripts.embeddings.datasets.split_support_query import split_into_roles

    dataset = generate_test_embedding_dataset()
    data = [1, 2, 3]
    labels = ["weed", "crop", "weed"]

    random.seed(1)
    divided_data, divided_labels, divided_metadata = split_into_roles(
        dataset, data, labels, min_score=0.05, max_distance=5.0, min_per_class_in_support_set=1
    )
    assert len(divided_data) == len(divided_labels) == len(divided_metadata) == 2

    assert set(divided_data[fewshot.dataset_types.DatapointRole.SUPPORT]) == set([1, 2])
    assert set(divided_labels[fewshot.dataset_types.DatapointRole.SUPPORT]) == set(["weed", "crop"])

    index_of_1 = divided_data[fewshot.dataset_types.DatapointRole.SUPPORT].index(1)
    index_of_2 = divided_data[fewshot.dataset_types.DatapointRole.SUPPORT].index(2)
    assert divided_metadata[fewshot.dataset_types.DatapointRole.SUPPORT][index_of_1] is not None
    assert divided_metadata[fewshot.dataset_types.DatapointRole.SUPPORT][index_of_2] is not None
    prediction_embedding_1 = cast(
        Dict[str, Any], divided_metadata[fewshot.dataset_types.DatapointRole.SUPPORT][index_of_1]
    )["prediction_embedding"]
    prediction_embedding_2 = cast(
        Dict[str, Any], divided_metadata[fewshot.dataset_types.DatapointRole.SUPPORT][index_of_2]
    )["prediction_embedding"]
    assert torch.equal(prediction_embedding_1, torch.tensor([2.0, 2.0, 2.0, 4.0]),)
    assert torch.equal(prediction_embedding_2, torch.tensor([2.99, 2.99, 2.99, 4.99]),)

    assert divided_data[fewshot.dataset_types.DatapointRole.QUERY] == [3]
    assert divided_labels[fewshot.dataset_types.DatapointRole.QUERY] == ["weed"]
    assert divided_metadata[fewshot.dataset_types.DatapointRole.QUERY][0] is not None
    assert (
        cast(Dict[str, Any], divided_metadata[fewshot.dataset_types.DatapointRole.QUERY][0])["prediction_label"]
        == "crop"
    )
    assert torch.equal(
        cast(Dict[str, Any], divided_metadata[fewshot.dataset_types.DatapointRole.QUERY][0])["prediction_embedding"],
        torch.tensor([2.15, 2.15, 2.15, 4.15]),
    )


@pytest.mark.dl_unit_test
def test_split_support_query_dataset() -> None:
    from deeplearning.scripts.embeddings.datasets.split_support_query import SplitSupportQueryDataset
    from deeplearning.scripts.embeddings.datasets.utils import EmbeddingLoader, transform_fn

    dataset = generate_test_embedding_dataset()
    data = [0, 1, 2, 3]
    labels = ["weed", "crop", "crop", "weed"]
    load_fn = EmbeddingLoader(dataset)
    split_support_query_dataset = SplitSupportQueryDataset(
        dataset, data, labels, load_fn, transform_fn, min_score=0.05, max_distance=5.0, min_per_class_in_support_set=1
    )

    random.seed(1)

    support_dataset = split_support_query_dataset.support
    query_dataset = split_support_query_dataset.query

    assert len(support_dataset) == 2
    assert len(query_dataset) == 2

    for query_point in query_dataset:
        assert query_point.metadata is not None


@pytest.mark.dl_unit_test
def test_split_support_query_sampler() -> None:
    import fewshot
    from deeplearning.scripts.embeddings.datasets.split_support_query import (
        SplitSupportQueryDataset,
        SplitSupportQuerySampler,
    )
    from deeplearning.scripts.embeddings.datasets.utils import EmbeddingLoader, transform_fn

    dataset = generate_test_embedding_dataset()
    data = [0, 1, 2, 3]
    labels = ["weed", "weed", "crop", "crop"]
    load_fn = EmbeddingLoader(dataset)
    split_support_query_dataset = SplitSupportQueryDataset(
        dataset, data, labels, load_fn, transform_fn, min_score=0.05, max_distance=5.0, min_per_class_in_support_set=1
    )

    random.seed(1)
    sampler = SplitSupportQuerySampler(
        split_support_query_dataset, num_classes=2, num_support_samples=1, num_query_samples=1, num_episodes=2, seed=1
    )

    episodes = []
    for episode in sampler:
        episodes.append(episode)

    assert len(episodes) == 2

    for item in episodes[0]:
        datapoint = split_support_query_dataset[item]
        if datapoint.role == fewshot.dataset_types.DatapointRole.SUPPORT:
            assert datapoint.identifier in [0, 2]
        else:
            assert datapoint.identifier in [1, 3]
            assert datapoint.metadata is not None

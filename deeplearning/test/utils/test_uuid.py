import uuid

from deeplearning.utils.uuid import check_if_uuid_format, get_globally_unique_id, make_unique_id


def test_check_if_uuid_format() -> None:
    assert not check_if_uuid_format("asdfasdf")
    assert not check_if_uuid_format("1234")
    a = uuid.uuid4()

    assert check_if_uuid_format(str(a))


def test_make_unique_id() -> None:
    assert make_unique_id("a_unique_image_id", "a_point_id") == "a_unique_image_id-a_point_id"


def test_get_globally_unique_id() -> None:
    annotation_dict_1 = {"image_id": "cool_image", "id": 1234}
    annotation_dict_2 = {"image_id": "awesome_image", "id": str(uuid.uuid4())}
    assert get_globally_unique_id(annotation_dict_1) == "cool_image-1234"
    assert get_globally_unique_id(annotation_dict_2) == annotation_dict_2["id"]

import copy
import json
import math
import os
from typing import Any, Dict, List, Optional, Set, Tuple, cast

import bokeh.palettes as color_palettes
import numpy as np
import numpy.typing as npt
import pandas as pd
import panel as pn
import torch
import umap
from bokeh import plotting as bplot
from bokeh.models import ColumnDataSource, HoverTool, LassoSelectTool, PanTool, ResetTool, WheelZoomTool
from bokeh.transform import factor_cmap, linear_cmap
from sklearn.preprocessing import normalize
from sqlalchemy.sql.expression import func

from deeplearning.dl_metrics import Embedding, HitClass, Image, Metric, Point, PointType, WeedsTargeted
from deeplearning.dl_metrics import get_db as get_points_db
from deeplearning.dl_metrics import get_session, metrics_list, nan_divide
from deeplearning.embeddings.scripts.calculate_cluster_scores import CLUSTERING_NAMES_TO_ALGORITHMS
from deeplearning.parametric_umap.model import ParametricUMAP
from deeplearning.utils.download_utils import download_records
from lib.common.generation import GENERATION

TOOLS = [LassoSelectTool, PanTool, WheelZoomTool, ResetTool]

pn.extension(sizing_mode="stretch_width", template="bootstrap")


loaded_embeddings: Dict[str, Any] = {
    "model_id": "",
    "number_of_embeddings": 0,
    "embeddings": [],
    "grouped_point_ids": None,
    "point_types": [],
    "umap_option": "",
    "cluster_labels_exist": False,
}
current_model_id = None


class EmbeddingPoint:
    def __init__(
        self,
        embedding: List[float],
        embedding_type: str,
        point_type: str,
        category: str,
        filepath: str,
        hit_class: str,
        x: float,
        y: float,
        point_id: int,
        cluster_label: Optional[str] = None,
    ):
        self.embedding = embedding
        self.embedding_type = embedding_type
        self.point_type = point_type
        self.category = category.upper()
        self.filepath = filepath
        self.hit_class = hit_class
        self.point_x = x
        self.point_y = y
        self.point_id = point_id
        self.cluster_label = cluster_label

        self.cache_filepath = self.get_cache_filepath(filepath, x, y)

    def get_cache_filepath(self, filepath: str, x: float, y: float) -> str:
        split_s3_path = filepath.split("/", maxsplit=3)
        assert len(split_s3_path) == 4  # Ex: ['s3:', '', 'maka-pono', 'media/test.png']
        bucket = split_s3_path[2]
        image_s3_path = split_s3_path[3]
        complete_filepath = f"http://{os.getenv('S3_CACHE_PROXY_SERVICE_HOST')}/{bucket}/{image_s3_path}"
        max_height = 3000
        max_width = 4096
        # Hacky and dumb but quick
        if GENERATION.SLAYER.to_str() not in image_s3_path:
            max_height = 4096
            max_width = 3000

        # Points db stores transposed coordinates for slayer images, so we need to transpose them back
        if GENERATION.SLAYER.to_str() in image_s3_path:
            tmp = x
            x = y
            y = tmp

        x_origin = int(min(max(x - 200, 0), max_width - 2 * 200 - 1))
        y_origin = int(min(max(y - 200, 0), max_height - 2 * 200 - 1))

        height = int(2 * 200)
        width = int(2 * 200)

        complete_filepath += f"?x={int(x_origin)}&y={int(y_origin)}&height={height}&width={width}"

        return complete_filepath


def get_cluster_information(
    cluster_labels_filepath: str, cluster_algorithm: str, number_clusters: str
) -> Tuple[Optional[Dict[str, Any]], bool]:
    cluster_labels_exist = False
    cluster_information: Optional[Dict[str, Any]] = None
    if cluster_labels_filepath != "" and cluster_algorithm != "" and number_clusters != "":
        if os.path.exists(cluster_labels_filepath):
            with open(cluster_labels_filepath) as f:
                cluster_file_contents = json.load(f)

            try:
                cluster_information = cluster_file_contents.get("clusters").get(cluster_algorithm).get(number_clusters)
                cluster_labels_exist = True
            except Exception:
                pass

    return cluster_information, cluster_labels_exist


def get_embeddings(  # noqa: C901
    model_id: str,
    number_of_embeddings: int,
    point_types: List[PointType],
    umap_option: str = "umap",
    custom_pumap_id: str = "",
    cluster_labels_filepath: str = "",
    cluster_algorithm: str = "",
    number_clusters: str = "",
    dataframe: str = "test_dataframes",
) -> Any:
    if (
        model_id == loaded_embeddings["model_id"]
        and number_of_embeddings == loaded_embeddings["number_of_embeddings"]
        and point_types == loaded_embeddings["point_types"]
        and umap_option == loaded_embeddings["umap_option"]
    ):
        return loaded_embeddings["embeddings"], loaded_embeddings["cluster_labels_exist"]

    path = f"/data/deeplearning/models/{model_id}/{dataframe}/points_v2.db"

    if not os.path.exists(path):
        print("Downloading")
        download_records(f"{model_id}/{dataframe}", include_points_db=True)

    if not os.path.exists(path):
        print(f"path: {path} doesnt exist")
        return None, False

    cluster_information, cluster_labels_exist = get_cluster_information(
        cluster_labels_filepath, cluster_algorithm, number_clusters
    )
    engine = get_points_db(path)

    image_filepaths = None
    if cluster_information is not None:
        image_filepaths = list(cluster_information.keys())

    embedding_type = "REDUCED_SCALED" if dataframe == "test_dataframes" else "FULL"

    with get_session(engine) as sess:
        query = (
            sess.query(
                Embedding.point_id,
                Embedding.type.label("embedding_type"),
                Embedding.embedding.label("embedding"),
                Point.type.label("point_type"),
                Point.category_class_name.label("category"),
                Image.filepath.label("filepath"),
                Point.hit_class.label("hit_class"),
                Point.x.label("x"),
                Point.y.label("y"),
            )
            .join(Point, Embedding.point_id == Point.id)
            .join(Image, Point.image_id == Image.id)
            .filter(Point.hit_class != HitClass.PLANT)
            .filter(Point.type.in_(point_types))
            .filter(Point.confidence > 0)
            .filter(Embedding.type == embedding_type)
        )

        if image_filepaths is not None:
            query = query.filter(Image.filepath.in_(image_filepaths))

        embeddings = query.order_by(func.random()).limit(number_of_embeddings).all()
    embeddings = [
        EmbeddingPoint(
            emb.embedding,
            emb.embedding_type.name,
            emb.point_type.name,
            emb.category,
            emb.filepath,
            emb.hit_class.name,
            emb.x,
            emb.y,
            emb.point_id,
        )
        for emb in embeddings
    ]

    embeddings_for_return = []
    embedding_values = apply_umap(embeddings, umap_option, custom_pumap_id)
    for i, embedding_val in enumerate(embedding_values):
        # Can enrich here with clustering information
        if cluster_information is not None:
            known_cluster_labels = cluster_information.get(embeddings[i].filepath)
            if known_cluster_labels is None:
                continue
            for known_cluster_label in known_cluster_labels:
                if int(known_cluster_label["x"]) == int(embeddings[i].point_x) and int(known_cluster_label["y"]) == int(
                    embeddings[i].point_y
                ):
                    embeddings[i].cluster_label = known_cluster_label["cluster_label"]
                    break

        embeddings[i].embedding = embedding_val
        if not cluster_labels_exist or (cluster_labels_exist and embeddings[i].cluster_label is not None):
            embeddings_for_return.append(embeddings[i])

    loaded_embeddings["model_id"] = model_id
    loaded_embeddings["number_of_embeddings"] = number_of_embeddings
    loaded_embeddings["embeddings"] = embeddings_for_return
    loaded_embeddings["grouped_point_ids"] = None
    loaded_embeddings["cluster_labels_exist"] = cluster_labels_exist

    return embeddings_for_return, cluster_labels_exist


def apply_umap(embeddings: List[EmbeddingPoint], umap_option: str, custom_pumap_id: str) -> List[List[float]]:
    embedding_values = [i.embedding for i in embeddings]
    if umap_option == "umap":
        reducer = umap.UMAP(n_neighbors=25, min_dist=0.1, n_components=2, metric="euclidean", spread=1)
        low_dim = reducer.fit_transform(normalize(embedding_values))
    else:
        reducer_filepath = f"/data/deeplearning/models/{custom_pumap_id}/best_checkpoint.ckpt"
        template = ParametricUMAP()
        container = torch.load(reducer_filepath)
        model = copy.deepcopy(template)
        model.load_state_dict(container["state_dict"])
        model = model.cuda()
        model.eval()
        low_dim = []
        embedding_tensor = torch.tensor(embedding_values).cuda()
        batch_size = 200
        for i in range(0, embedding_tensor.shape[0], batch_size):
            out = model(embedding_tensor[i : i + batch_size, :].unsqueeze(-1).unsqueeze(-1))
            low_dim.extend(out.squeeze().squeeze().tolist())

    return cast(List[List[float]], low_dim)


def get_grouped_point_ids(
    embedding_array: List[EmbeddingPoint], x_groups: int = 10, y_groups: int = 10
) -> Dict[str, Any]:
    if (
        loaded_embeddings["grouped_point_ids"] is not None
        and loaded_embeddings["grouped_point_ids"]["x_groups"] == x_groups
        and loaded_embeddings["grouped_point_ids"]["y_groups"] == y_groups
    ):
        return cast(Dict[str, Any], loaded_embeddings["grouped_point_ids"])
    min_x = float("inf")
    min_y = float("inf")
    max_x = float("-inf")
    max_y = float("-inf")

    low_dim = [item.embedding for item in embedding_array]

    for dim_values in low_dim:
        if dim_values[0] >= max_y:
            max_y = dim_values[0]
        if dim_values[0] <= min_y:
            min_y = dim_values[0]
        if dim_values[1] >= max_x:
            max_x = dim_values[1]
        if dim_values[1] <= min_x:
            min_x = dim_values[1]

    buckets: List[List[List[int]]] = []
    for i in range(y_groups):
        buckets.append([[] for _ in range(x_groups)])

    for i, dim_val in enumerate(low_dim):
        point_id = embedding_array[i].point_id
        y = dim_val[0]
        x = dim_val[1]

        y_ind = min(len(buckets) - 1, math.floor(y_groups * (y - min_y) / (max_y - min_y)))
        x_ind = min(len(buckets[y_ind]) - 1, math.floor(x_groups * (x - min_x) / (max_x - min_x)))

        buckets[y_ind][x_ind].append(point_id)

    bucket_size_y = (max_y - min_y) / y_groups
    bucket_size_x = (max_x - min_x) / x_groups

    y_boundaries = [min_y + i * bucket_size_y for i in range(y_groups)]
    x_boundaries = [min_x + i * bucket_size_x for i in range(x_groups)]

    loaded_embeddings["grouped_point_ids"] = {
        "buckets": buckets,
        "y_boundaries": y_boundaries,
        "x_boundaries": x_boundaries,
        "bucket_size_y": bucket_size_y,
        "bucket_size_x": bucket_size_x,
        "x_groups": x_groups,
        "y_groups": y_groups,
    }

    return cast(Dict[str, Any], loaded_embeddings["grouped_point_ids"])


def get_metrics_for_groups(
    buckets: List[List[List[int]]],
    model_id: str,
    metric: Any = WeedsTargeted,
    wpt: float = 0.5,
    cpt: float = 0.5,
    dataframe: str = "test_dataframe",
) -> Tuple[npt.NDArray[Any], npt.NDArray[Any], Set[int]]:
    met: Metric = metric(wpt=wpt, cpt=cpt)

    path = f"/data/deeplearning/models/{model_id}/{dataframe}/points_v2.db"

    engine = get_points_db(path)

    numerator = np.zeros((len(buckets), len(buckets[0])))
    denominator = np.zeros((len(buckets), len(buckets[0])))
    positive_point_ids = []

    for i, y in enumerate(buckets):
        for j, items in enumerate(y):
            if len(items) == 0:
                numerator[i, j] = 0
                denominator[i, j] = 0
                continue

            with get_session(engine) as sess:
                result = met.filter(filter_point_ids=set(items)).group_by(point_id=True)(sess)

            numerator[i, j] = result["numerator"].sum()
            denominator[i, j] = result["denominator"].sum()

            positive_point_ids.extend(result[result["numerator"] == 1]["point_id"].tolist())

    return numerator, denominator, set(positive_point_ids)


def get_embeddings_plot(
    model_id: str,
    number_of_embeddings: int,
    met: str,
    y_groups: int,
    x_groups: int,
    point_types: List[PointType],
    umap_option: str = "umap",
    color_by_field: str = "category",
    custom_pumap_id: str = "",
    cluster_labels_filepath: str = "",
    cluster_algorithm: str = "",
    number_clusters: str = "",
) -> Any:
    if model_id == "":
        return "Pass a model id to get started"

    dataframe = "test_dataframes"
    if umap_option == "umap":
        dataframe = "test_unoptimized_dataframes"
    embedding_array, cluster_labels_exist = get_embeddings(
        model_id,
        number_of_embeddings,
        point_types,
        umap_option,
        custom_pumap_id=custom_pumap_id,
        cluster_labels_filepath=cluster_labels_filepath,
        cluster_algorithm=cluster_algorithm,
        number_clusters=number_clusters,
        dataframe=dataframe,
    )

    if cluster_labels_exist:
        color_by_field = "cluster_label"

    if embedding_array is None:
        return f"Could neither find nor download point_v2.db for {model_id}"

    tooltip1 = """
        <div>
            <b>@point_type</b>
            <b>@category</b>
            <div><b>filepath:</b> @filepath</div>
            <div><b>x:</b> @point_x</div>
            <div><b>y:</b> @point_y</div>
            <div><b>Positive metric hit:</b> @positive_hit</div>
            <div><b>Cluster Label: </b> @cluster_label</div>
            <div>
                <img
                src="@cache_filepath" height="200" alt="image"
                style="float: left; margin: 0px 15px 15px 0px; image-rendering: pixelated;"
                border="2"
                ></img>
            </div>
        </div>
    """

    tooltip2 = """
        <div>
            <div><b>Metric:</b> @val%</div>
            <div><b>Denom:</b> @denom</div>
        </div>
    """

    tools = [t() for t in TOOLS]
    fig = bplot.figure(tools=tools)
    embeddings_for_df = [
        {
            "projection_x1": embedding_array[i].embedding[0],
            "projection_x2": embedding_array[i].embedding[1],
            **embedding_array[i].__dict__,
        }
        for i in range(len(embedding_array))
    ]

    if met != "None":

        loaded_group_info = get_grouped_point_ids(embedding_array, x_groups=x_groups, y_groups=y_groups)
        numerator, denominator, positive_point_ids = get_metrics_for_groups(
            loaded_group_info["buckets"], model_id, metric=metrics[met], dataframe=dataframe
        )

        for embedding_item in embeddings_for_df:
            if embedding_item["point_id"] in positive_point_ids:
                embedding_item["positive_hit"] = True
            else:
                embedding_item["positive_hit"] = False

        values_dicts = []

        y_boundaries = loaded_group_info["y_boundaries"]
        x_boundaries = loaded_group_info["x_boundaries"]
        bucket_size_y = loaded_group_info["bucket_size_y"]
        bucket_size_x = loaded_group_info["bucket_size_x"]
        for i, y_bound in enumerate(y_boundaries):
            for j, x_bound in enumerate(x_boundaries):
                val = nan_divide(numerator[i, j], denominator[i, j])
                if val != float("nan"):
                    val = 100 * val
                values_dicts.append(
                    {
                        "y_bound": y_bound + bucket_size_y / 2,
                        "x_bound": x_bound + bucket_size_x / 2,
                        "val": val,
                        "denom": denominator[i, j],
                    }
                )

        values_df = pd.DataFrame(values_dicts)
        min_val = values_df[values_df["denom"] > 5]["val"].min()
        max_val = values_df[values_df["denom"] > 5]["val"].max()

        val_mapper = linear_cmap(field_name="val", palette=color_palettes.gray(32), low=min_val, high=max_val)

        rect_plot = fig.rect(
            x="x_bound",
            y="y_bound",
            width=bucket_size_x,
            height=bucket_size_y,
            source=values_df,
            color=val_mapper,
            fill_alpha=0.1,
        )

        hover2 = HoverTool(tooltips=tooltip2, renderers=[rect_plot], attachment="left")
        fig.add_tools(hover2)

    categories = sorted(list(set([i[color_by_field] for i in embeddings_for_df])))

    mapper = factor_cmap(
        field_name=color_by_field,
        palette=color_palettes.linear_palette(color_palettes.Turbo256, len(categories) + 1),
        factors=categories,
    )
    hit_mapper = factor_cmap(
        field_name="hit_class", factors=[HitClass.WEED.name, HitClass.CROP.name], palette=color_palettes.gray(4)
    )
    df = pd.DataFrame(embeddings_for_df)
    source = ColumnDataSource(df)
    circle_plot = fig.circle(
        y="projection_x1",
        x="projection_x2",
        fill_color=mapper,
        line_color=hit_mapper,
        line_alpha=0.2,
        source=source,
        legend_field=color_by_field,
    )
    hover1 = HoverTool(tooltips=tooltip1, renderers=[circle_plot], attachment="right")
    fig.add_tools(hover1)

    fig.legend.title = "Categories"

    fig.xaxis.major_tick_line_color = None
    fig.xaxis.minor_tick_line_color = None

    fig.yaxis.major_tick_line_color = None
    fig.yaxis.minor_tick_line_color = None

    fig.xgrid.visible = False
    fig.ygrid.visible = False

    return pn.panel(fig, width=2000, height=2000)


if __name__ == "__main__":
    model_id_widget = pn.widgets.input.TextInput(name="Model ID", value="fut-20241026-u73domskra")
    number_embeddings_widget = pn.widgets.IntSlider(
        name="Number of embeddings", value=10000, start=1000, end=100000, step=1000
    )
    metrics: Dict[str, Metric] = {met.__name__: cast(Metric, met) for met in metrics_list}
    metric_widget = pn.widgets.Select(name="Metric", options=["None", *metrics])

    x_groups = pn.widgets.IntSlider(name="Number of horizontal groups", start=1, end=20, value=10)
    y_groups = pn.widgets.IntSlider(name="Number of vertical groups", start=1, end=20, value=10)

    point_types = pn.widgets.CheckBoxGroup(
        name="Point Type to Display",
        options=[PointType.LABEL, PointType.PREDICTION],
        value=[PointType.LABEL],
        inline=True,
    )

    umap_options = ["umap", "custom_parametric_umap"]

    umap_option_widget = pn.widgets.Select(name="Reduction Algorithm", options=umap_options)
    custom_pumap_id_widget = pn.widgets.TextInput(name="Custom PUMAP ID")

    cluster_labels_filepath_widget = pn.widgets.TextInput(
        name="Cluster Label Filepath", value="/data/deeplearning/cluster_analysis/testing_fut-20241026-u73domskra.txt"
    )
    cluster_algorithm_widget = pn.widgets.Select(
        name="Clustering Algorithm", options=list(CLUSTERING_NAMES_TO_ALGORITHMS.keys())
    )
    number_clusters_widget = pn.widgets.TextInput(name="Number of clusters", value="6")

    color_by_field = pn.widgets.RadioBoxGroup(name="Field for coloring", options=["category", "hit_class"], inline=True)

    app = pn.Row(
        pn.Column(
            model_id_widget,
            number_embeddings_widget,
            metric_widget,
            x_groups,
            y_groups,
            point_types,
            umap_option_widget,
            custom_pumap_id_widget,
            cluster_labels_filepath_widget,
            cluster_algorithm_widget,
            number_clusters_widget,
            color_by_field,
            width=600,
        ),
        pn.Column(
            pn.bind(
                get_embeddings_plot,
                model_id_widget,
                number_embeddings_widget,
                metric_widget,
                y_groups=y_groups,
                x_groups=x_groups,
                point_types=point_types,
                umap_option=umap_option_widget,
                color_by_field=color_by_field,
                custom_pumap_id=custom_pumap_id_widget,
                cluster_labels_filepath=cluster_labels_filepath_widget,
                cluster_algorithm=cluster_algorithm_widget,
                number_clusters=number_clusters_widget,
            )
        ),
    )
    app.servable()

import json
import os
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, <PERSON>ple

import numpy as np
import pandas as pd
import panel as pn
import plotly.express as px
import pydeck
from geolib import geohash as ghlib

MAPBOX_API_KEY = os.getenv("MAPBOX_API_KEY")

pn.extension("plotly", "deckgl", sizing_mode="stretch_width", template="bootstrap")

data: Dict[str, Any] = {"images_df": None, "model_id": None}

loading_spinner = pn.indicators.LoadingSpinner(value=False, name="loaded", size=20).servable()


def load_data(
    model_id: str,
) -> Tuple[pd.DataFrame, List[str], Any, Dict[Any, int], Dict[Any, int], Dict[str, List[str]]]:
    if data["model_id"] == model_id and data["images_df"] is not None and data["positive_categories"] is not None:
        return (
            data["images_df"],
            data["positive_categories"],
            data["embeddings_in_set"],
            data["selected_categories"],
            data["selected_embeddings"],
            data["class_data"],
        )
    filepath = f"/data/deeplearning/models/{model_id}/sampled_filepaths.json"
    with open(filepath) as f:
        sampled_filepaths = json.load(f)
        image_filepaths = sampled_filepaths["filepaths"]
        dataset_id = sampled_filepaths["dataset_id"]
        image_embeddings = sampled_filepaths.get("file_embeddings", {})
        selected_categories = sampled_filepaths.get("selected_categories", {})
        selected_embeddings = sampled_filepaths.get("selected_embeddings", {})

    train_filepath = f"/data/deeplearning/datasets/{dataset_id}/train.json"
    with open(train_filepath) as f:
        train = json.load(f)

    train_info = {image["uri"]: image for image in train["images"]}
    categories = {cat["id"]: cat for cat in train["categories"]}
    image_to_annotations = {
        annotation["image_id"]: annotation
        for annotation in train["annotations"]
        if annotation["annotation_type"] == "point"
    }
    image_to_annotations = defaultdict(list)
    for ann in train["annotations"]:
        image_to_annotations[ann["image_id"]].append(ann)
    dataframe_rows = []

    default_cat_object = {cat["name"]: False for cat in categories.values()}

    embeddings_in_set = []

    for _, embeddings in image_embeddings.items():
        if embeddings is not None and len(embeddings) > 0:
            embeddings_in_set.extend([tuple(emb) for emb in embeddings])

    embeddings_in_set = sorted(list(set(embeddings_in_set)))

    for im_filepath, count in image_filepaths.items():
        row = {
            "filepath": im_filepath,
            "geohash": train_info[im_filepath]["geohash"],
            "captured_at": train_info[im_filepath]["captured_at"],
            "captured_date": datetime.fromtimestamp(
                train_info[im_filepath]["captured_at"] // 1000, tz=timezone.utc
            ).strftime("%Y-%m-%d"),
        }

        represented_embeddings = {
            emb: (list(emb) in image_embeddings.get(im_filepath, {})) for emb in embeddings_in_set
        }
        row.update(represented_embeddings)

        image_id = train_info[im_filepath]["id"]
        has_categories = default_cat_object.copy()

        crop_radii = []
        weed_radii = []

        for ann in image_to_annotations[image_id]:
            cat = categories[ann["category_id"]]
            has_categories[cat["name"]] = True
            if cat["supercategory"] == "weed":
                weed_radii.append(ann["radius"])
            elif cat["supercategory"] == "crop":
                crop_radii.append(ann["radius"])

        row["mean_weed_radius"] = np.mean(weed_radii) if len(weed_radii) else None
        row["mean_crop_radius"] = np.mean(crop_radii) if len(crop_radii) else None

        row.update(has_categories)

        for _ in range(count):
            dataframe_rows.append(row)

    df = pd.DataFrame(dataframe_rows)
    data["model_id"] = model_id
    data["images_df"] = df
    data["positive_categories"] = list(default_cat_object.keys())
    data["embeddings_in_set"] = embeddings_in_set
    data["class_data"] = sampled_filepaths["class_data"]

    selected_categories_list = sorted(
        [{"category": category, "count": category_count} for category, category_count in selected_categories.items()],
        key=lambda x: x["category"],
    )
    selected_embeddings_list = sorted(
        [
            {"embedding_bucket": embedding_bucket, "count": embedding_bucket_count}
            for embedding_bucket, embedding_bucket_count in selected_embeddings.items()
        ],
        key=lambda x: x["embedding_bucket"],
    )

    data["selected_categories"] = pd.DataFrame(selected_categories_list) if len(selected_categories_list) > 0 else None
    data["selected_embeddings"] = pd.DataFrame(selected_embeddings_list) if len(selected_embeddings_list) > 0 else None
    return (
        df,
        list(default_cat_object.keys()),
        embeddings_in_set,
        data["selected_categories"],
        data["selected_embeddings"],
        data["class_data"],
    )


def get_image_breakdown(model_id: str) -> Any:
    images_df, categories, _, _, _, _ = load_data(model_id)
    images_df["counts"] = 1
    grouped_by_filepath = images_df[["filepath", "counts"]].groupby("filepath").sum()
    summed_values = grouped_by_filepath["counts"]
    counts, bins = np.histogram(summed_values, bins=range(summed_values.min(), summed_values.max(), 1))
    fig = None
    bins = 0.5 * (bins[:-1] + bins[1:])

    if len(counts) > 0:
        fig = px.bar(
            x=bins,
            y=counts,
            labels={"x": "number of times image was sampled", "y": "number of occurances"},
            title="Histogram of number of times individual images were sampled",
        )
    percentiles = [f"{i}th_percentile = {np.percentile(summed_values, i)}" for i in range(0, 101, 5)]

    top_percentile = np.percentile(summed_values, 100)

    df_group_by_top_file = grouped_by_filepath[grouped_by_filepath["counts"] == top_percentile].reset_index()
    top_filepath = df_group_by_top_file.iloc[0]["filepath"]

    columns = ["filepath", "geohash", "captured_date", "mean_weed_radius", "mean_crop_radius"]
    columns.extend(categories)
    top_meta = images_df[images_df["filepath"] == top_filepath].reset_index().iloc[0][columns]

    top_meta_dict = {}
    for col in columns:
        top_meta_dict[col] = top_meta[col]

    col = pn.Column(
        pn.panel(fig), pn.panel(percentiles)
    )  # , pn.panel(top_meta)) <- This was originally uncomented, it just shows an example row of the df, not sure whether to keep
    return col


def get_image_crop_size_breakdown(model_id: str, min_size: int, max_size: int, increment: int) -> Any:
    # create the bins
    images_df, _, _, _, _, _ = load_data(model_id)
    images_df = images_df[images_df["mean_crop_radius"].notna()]
    counts, bins = np.histogram(images_df.mean_crop_radius, bins=range(min_size, max_size, increment))
    bins = 0.5 * (bins[:-1] + bins[1:])

    fig = px.bar(x=bins, y=counts, labels={"x": "mean_crop_radius", "y": "count"}, title="Mean crop size sampling")
    return pn.panel(fig)


def get_image_weed_size_breakdown(model_id: str, min_size: int, max_size: int, increment: int) -> Any:
    # create the bins
    images_df, _, _, _, _, _ = load_data(model_id)
    images_df = images_df[images_df["mean_weed_radius"].notna()]
    counts, bins = np.histogram(images_df.mean_weed_radius, bins=range(min_size, max_size, increment))
    bins = 0.5 * (bins[:-1] + bins[1:])

    fig = px.bar(x=bins, y=counts, labels={"x": "mean_weed_radius", "y": "count"}, title="Mean weed size sampling")
    return pn.panel(fig)


def get_image_positive_category_breakdown(model_id: str) -> Any:
    images_df, categories, _, _, _, _ = load_data(model_id)
    positive_category_breakdown = []
    categories.sort()
    for cat in categories:
        count = images_df[images_df[cat]].shape[0]
        positive_category_breakdown.append({"category": cat.upper(), "count": count})

    pos_cat_break = pd.DataFrame(positive_category_breakdown)

    fig = px.bar(pos_cat_break, x="category", y="count", title="Positive category sampling")
    return pn.panel(fig)


def get_overall_weed_crop_breakdown(df_path: str) -> Any:
    images_df, categories, _, _, _, class_data = load_data(df_path)
    weeds = class_data["weed"]
    crops = class_data["crop"]
    weed_counts = {"category": "weeds", "count": 0}
    crop_counts = {"category": "crops", "count": 0}
    for cat in categories:
        if cat.upper() in weeds:
            weed_counts["count"] += images_df[images_df[cat]].shape[0]
        elif cat.upper() in crops:
            crop_counts["count"] += images_df[images_df[cat]].shape[0]

    fig = px.bar(
        pd.DataFrame([weed_counts, crop_counts]), x="category", y="count", title="Weeds vs Crops category sampling"
    )

    fig.update_layout(bargap=0.7)
    return pn.panel(fig)


def get_image_date_breakdown(df_path: str) -> Any:
    images_df, _, _, _, _, _ = load_data(df_path)
    fig = px.histogram(images_df, x="captured_date", title="Date sampling")

    return pn.panel(fig)


def get_local_time_breakdown(df_path: str, geohash_precision: int = 4) -> Any:
    images_df, _, _, _, _, _, = load_data(df_path)

    minute_interval = 30
    images_df["geoN"] = images_df["geohash"].map(lambda x: x[:geohash_precision])

    geocache = {}

    def unix_to_local_time(unix_time: int, hash: str) -> Any:
        utc_time = datetime.fromtimestamp(unix_time / 1000, tz=timezone.utc)
        longitude = 0
        if hash not in geocache:
            _, longitude = ghlib.decode(hash)
            geocache[hash] = longitude
        else:
            longitude = geocache[hash]
        offset_hours = round(longitude / 15)
        local_time = (utc_time + timedelta(hours=offset_hours)).time()

        return local_time.hour * 60 + local_time.minute

    images_df["captured_at"] = images_df.apply(lambda row: unix_to_local_time(row["captured_at"], row["geoN"]), axis=1)

    fig = px.histogram(images_df, x="captured_at", title="Time sampling (local time)", nbins=24 * 60 // minute_interval)
    fig.update_layout(
        xaxis_title=f"Time ({minute_interval}-minute intervals)",
        yaxis_title="Count",
        xaxis_tickvals=[i * minute_interval for i in range(24 * 60 // minute_interval)],
        xaxis_ticktext=[f"{i//2:02d}:{(i%2)*30:02d}" for i in range(24 * 60 // minute_interval)],
    )

    return pn.panel(fig)


def get_image_map_breakdown(model_id: str, geohash_precision: int = 4) -> Any:
    images_df, _, _, _, _, _ = load_data(model_id)

    geo_map_data = []

    images_df["geoN"] = images_df["geohash"].map(lambda x: x[:geohash_precision])

    geoN_group_by = images_df.groupby("geoN")

    geoN_count = {}
    for key, df in geoN_group_by:
        geoN_count[key] = df.shape[0]

    max_geoN_count = max(geoN_count.values())
    min_geoN_count = min(geoN_count.values())
    total_count = sum(geoN_count.values())

    base_red = 50
    step = 0.0
    if (max_geoN_count - min_geoN_count) > 0:
        step = 205 / float(max_geoN_count - min_geoN_count)

    for key, count in geoN_count.items():
        red = int(base_red + step * (count - min_geoN_count))
        color = [red, 120, 50]
        geobounds = ghlib.bounds(key)
        sw_lat = geobounds[0][0]
        sw_lng = geobounds[0][1]
        ne_lat = geobounds[1][0]
        ne_lng = geobounds[1][1]
        contour = [
            [sw_lng, sw_lat, 0],
            [ne_lng, sw_lat, 0],
            [ne_lng, ne_lat, 0],
            [sw_lng, ne_lat, 0],
            [sw_lng, sw_lat, 0],
        ]
        geo_map_data.append(
            {
                "geohash": key,
                "contours": contour,
                "color": color,
                "count": count,
                "percentage": f"{round((count / total_count) * 100, 2)}%",
            }
        )

    geoN_map = pydeck.Deck(
        map_style=None,
        initial_view_state=pydeck.ViewState(latitude=40, longitude=-95, zoom=4,),
        layers=[
            pydeck.Layer(
                "PolygonLayer",
                pd.DataFrame.from_dict(geo_map_data),
                id="polygon-layer-1",
                get_polygon="contours",
                get_line_color="color",
                get_fill_color="color",
                get_elevation="count",
                extruded=True,
                stroked=True,
                filled=True,
                line_width_min_pixels=3,
                pickable=True,
                opacity=1.0,
            ),
        ],
    ).to_json()

    return pn.pane.DeckGL(
        geoN_map,
        mapbox_api_key=MAPBOX_API_KEY,
        height=1000,
        stylesheets=["https://api.mapbox.com/mapbox-gl-js/v3.1.2/mapbox-gl.css"],
    )


def get_image_map_histogram_breakdown(model_id: str, geohash_precision: int = 4) -> Any:
    images_df, _, _, _, _, _ = load_data(model_id)
    images_df["geoN"] = images_df["geohash"].map(lambda x: x[:geohash_precision])
    fig = px.histogram(
        images_df, x="geoN", title=f"Geohash sampling ({len(images_df['geoN'].unique())} geo{geohash_precision})"
    ).update_xaxes(categoryorder="total descending")

    return pn.panel(fig)


def get_embeddings_breakdown(model_id: str) -> Any:
    images_df, _, embeddings_in_set, _, _, _ = load_data(model_id)
    embeddings_represented = []
    if len(embeddings_in_set) > 0:
        for emb in embeddings_in_set:
            count = 0
            if emb in images_df.columns:
                count = images_df[emb].sum(axis=0)
            embeddings_represented.append({"embedding": str(emb), "count": count})
        embedding_sampling = pd.DataFrame(embeddings_represented)

        fig = px.bar(embedding_sampling, x="embedding", y="count", title="Embedding sampling")
        return pn.panel(fig)
    return pn.panel("No embeddings in this model")


def get_sampling_targets_breakdown(model_id: str) -> Any:
    _, _, _, selected_categories, selected_embeddings, _ = load_data(model_id)
    figures = []
    if selected_categories is not None:
        figures.append(
            px.bar(
                selected_categories, x="category", y="count", title="Requested Category (via the sampling algorithm)"
            )
        )
    if selected_embeddings is not None:
        figures.append(
            px.bar(
                selected_embeddings,
                x="embedding_bucket",
                y="count",
                title="Requested Embedding (via the sampling algorithm)",
            )
        )

    return pn.Column(*figures)


def get_column(model_id: str, min_size: int, max_size: int, increment: int, geohash_precision: int) -> Any:
    if model_id == "":
        return pn.panel("Please pass a model id")
    loading_spinner.value = True
    loading_spinner.name = "loading"

    col = pn.Column(
        get_image_breakdown(model_id),
        get_image_crop_size_breakdown(model_id, min_size, max_size, increment),
        get_image_weed_size_breakdown(model_id, min_size, max_size, increment),
        get_overall_weed_crop_breakdown(model_id),
        get_image_positive_category_breakdown(model_id),
        get_sampling_targets_breakdown(model_id),
        get_embeddings_breakdown(model_id),
        get_image_date_breakdown(model_id),
        get_local_time_breakdown(model_id, geohash_precision),
        get_image_map_breakdown(model_id, geohash_precision),
        get_image_map_histogram_breakdown(model_id, geohash_precision),
    )

    loading_spinner.value = False
    loading_spinner.name = "loaded"
    return col


load_file_widget = pn.widgets.input.TextInput(name="model_id")
min_size = pn.widgets.IntSlider(name="Min PX", start=0, end=1000, value=0, step=10)
max_size = pn.widgets.IntSlider(name="Max PX", start=0, end=1000, value=500, step=10)
increment = pn.widgets.IntSlider(name="Increment PX", start=10, end=100, value=20)
geohash_precision = pn.widgets.IntSlider(name="Geohash Precision", start=1, end=12, value=4)
app = pn.Column(
    load_file_widget,
    pn.Row(min_size, max_size, increment, geohash_precision),
    pn.bind(get_column, load_file_widget, min_size, max_size, increment, geohash_precision),
)

app.servable()

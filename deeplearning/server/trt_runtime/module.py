from typing import Optional, <PERSON><PERSON>

import torch

from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.server.trt_runtime.cpp.trt_runtime_python import TRTContext, TRTRuntime
from generated.deeplearning.model_io.proto.trt_model_pb2 import TRTModelProto


class TRTModule(torch.nn.Module):
    def __init__(self) -> None:
        super().__init__()
        self.ctx: Optional[TRTContext] = None
        self.cache_context = False
        self._runtime: Optional[TRTRuntime] = None

    def forward(self, *inputs: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        # V1: create context per request, incurring few ms each time. In future, we can introduce context scopes
        # to batch multiple requests per context allocation.
        assert self._runtime is not None
        if self.cache_context:
            if self.ctx is None:
                self.ctx = self._runtime.create_context()
            ctx = self.ctx
        else:
            ctx = self._runtime.create_context()

        try:
            inputs = tuple(t.contiguous() for t in inputs)
            return tuple(self._runtime.infer(ctx, inputs))
        finally:
            if not self.cache_context:
                torch.cuda.synchronize()
                del ctx

    def set_cache_context(self, value: bool) -> None:
        self.cache_context = value

    @staticmethod
    def from_proto(proto_bytes: bytes) -> Tuple["TRTModule", ModelMetadata]:
        trt_model_proto = TRTModelProto()
        trt_model_proto.ParseFromString(proto_bytes)

        trt_module = TRTModule()
        trt_module._runtime = TRTRuntime(
            bytes=trt_model_proto.engine_bytes,
            input_names=trt_model_proto.input_names,
            output_names=trt_model_proto.output_names,
            device=torch.cuda.current_device(),
            implicit_batch_dimension=trt_model_proto.implicit_batch_dimension,
            version=trt_model_proto.tensorrt_version if trt_model_proto.tensorrt_version != "" else "8.0.1.6",
        )
        return trt_module, ModelMetadata.from_proto(trt_model_proto.metadata)

#ifndef DEEPLEARNING_SERVER_TRT_RUNTIME_RUNTIME_H
#define DEEPLEARNING_SERVER_TRT_RUNTIME_RUNTIME_H 1

#include <memory>
#include <shared_mutex>
#include <vector>

#include "compatibility/tensorrt_10.h"
#include "compatibility/tensorrt_8.h"
#include <torch/torch.h>

namespace deeplearning {
namespace server {
namespace trt_runtime {

struct TRTContext {
  std::shared_ptr<compatibility::TensorRT8Context> tensorrt8_ctx;
  std::shared_ptr<compatibility::TensorRT10Context> tensorrt10_ctx;
};

class TRTRuntime {
public:
  TRTRuntime(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
             std::string version, int device = 0, bool implicit_batch_dimension = true);
  TRTContext create_context();
  std::vector<torch::Tensor> infer(TRTContext context, std::vector<torch::Tensor> inputs);

  static std::vector<std::string> SUPPORTED_TRT_VERSIONS;

private:
  std::unique_ptr<compatibility::TensorRT8> tensorrt8_;
  std::unique_ptr<compatibility::TensorRT10> tensorrt10_;
  void* tensorrt8_lib_;
  void* tensorrt10_lib_;
};

} // namespace trt_runtime
} // namespace server
} // namespace deeplearning

#endif

from typing import Sequence

import torch

class TRTContext:
    pass

class TRTRuntime:
    def __init__(
        self,
        bytes: bytes,
        input_names: Sequence[str],
        output_names: Sequence[str],
        version: str,
        device: int = 0,
        implicit_batch_dimension: bool = True,
    ) -> None: ...
    def create_context(self) -> TRTContext: ...
    def infer(self, context: TRTContext, inputs: Sequence[torch.Tensor]) -> Sequence[torch.Tensor]: ...

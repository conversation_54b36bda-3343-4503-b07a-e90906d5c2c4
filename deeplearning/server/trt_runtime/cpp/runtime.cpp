#include <c10/cuda/CUDAStream.h>

#include "lib/common/cpp/cuda_util.h"
#include "runtime.h"

namespace deeplearning {
namespace server {
namespace trt_runtime {

using make_tensorrt8_t = std::unique_ptr<compatibility::TensorRT8> (*)(std::string, std::vector<std::string>,
                                                                       std::vector<std::string>, int, bool);
using init_trt_plugin_registry_8_t = void (*)();

using make_tensorrt10_t = std::unique_ptr<compatibility::TensorRT10> (*)(std::string, int);
using init_trt_plugin_registry_10_t = void (*)();

// Order these such that preferred versions are in the beginning.
std::vector<std::string> TRTRuntime::SUPPORTED_TRT_VERSIONS{"10.0.1", "8.0.1.6"};

TRTRuntime::TRTRuntime(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
                       std::string version, int device, bool implicit_batch_dimension) {
  auto proto =
      dlopen("/robot/build/deeplearning/trt_extensions/native/libtrt_extensions_proto.so", RTLD_NOW | RTLD_GLOBAL);
  if (!proto) {
    throw std::runtime_error(std::string(dlerror()));
  }
  if (version == "8.0.1.6") {
    tensorrt8_lib_ =
        dlopen("/robot/build/deeplearning/server/trt_runtime/cpp/compatibility/libtensorrt8_compatibility.so",
               RTLD_NOW | RTLD_LOCAL);
    if (!tensorrt8_lib_) {
      throw std::runtime_error(std::string(dlerror()));
    }

    const init_trt_plugin_registry_8_t init_trt_plugin_registry_8 =
        reinterpret_cast<init_trt_plugin_registry_8_t>(dlsym(tensorrt8_lib_, "init_trt_plugin_registry_8"));
    if (!init_trt_plugin_registry_8) {
      throw std::runtime_error(std::string(dlerror()));
    }
    init_trt_plugin_registry_8();

    const make_tensorrt8_t make_tensorrt8 = reinterpret_cast<make_tensorrt8_t>(dlsym(tensorrt8_lib_, "make_tensorrt8"));
    if (!make_tensorrt8) {
      throw std::runtime_error(std::string(dlerror()));
    }
    tensorrt8_ = make_tensorrt8(data, input_names, output_names, device, implicit_batch_dimension);
  } else if (version == "10.0.1") {
    tensorrt10_lib_ =
        dlopen("/robot/build/deeplearning/server/trt_runtime/cpp/compatibility/libtensorrt10_compatibility.so",
               RTLD_NOW | RTLD_LOCAL);
    if (!tensorrt10_lib_) {
      throw std::runtime_error("tensorrt10_lib_ " + std::string(dlerror()));
    }

    const init_trt_plugin_registry_10_t init_trt_plugin_registry_10 =
        reinterpret_cast<init_trt_plugin_registry_10_t>(dlsym(tensorrt10_lib_, "init_trt_plugin_registry_10"));
    if (!init_trt_plugin_registry_10) {
      throw std::runtime_error("init_trt_plugin_registry_10 " + std::string(dlerror()));
    }
    init_trt_plugin_registry_10();

    const make_tensorrt10_t make_tensorrt10 =
        reinterpret_cast<make_tensorrt10_t>(dlsym(tensorrt10_lib_, "make_tensorrt10"));
    if (!make_tensorrt10) {
      throw std::runtime_error("make_tensorrt10 " + std::string(dlerror()));
    }
    tensorrt10_ = make_tensorrt10(data, device);
  } else {
    throw std::runtime_error(fmt::format("Unknown TensorRT version: {}", version));
  }
}

TRTContext TRTRuntime::create_context() {
  TRTContext context;
  if (tensorrt8_) {
    context.tensorrt8_ctx = tensorrt8_->create_context();
  } else if (tensorrt10_) {
    context.tensorrt10_ctx = tensorrt10_->create_context();
  }
  return context;
}

std::vector<torch::Tensor> TRTRuntime::infer(TRTContext context, std::vector<torch::Tensor> inputs) {
  if (tensorrt8_) {
    return tensorrt8_->infer(context.tensorrt8_ctx, inputs);
  } else if (tensorrt10_) {
    return tensorrt10_->infer(context.tensorrt10_ctx, inputs);
  } else {
    throw std::runtime_error("Neither tensorrt version initialized");
  }
}
} // namespace trt_runtime
} // namespace server
} // namespace deeplearning

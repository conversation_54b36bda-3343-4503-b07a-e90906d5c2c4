#include "tensorrt_8.h"

#include <NvInferPlugin.h>
#include <NvInferRuntime.h>
#include <c10/cuda/CUDACachingAllocator.h>
#include <c10/cuda/CUDAStream.h>
#include <shared_mutex>

#include "lib/common/cpp/cuda_util.h"

namespace deeplearning::server::trt_runtime::compatibility {

class TRTLogger : public nvinfer1::ILogger {
public:
  TRTLogger(nvinfer1::ILogger::Severity minimum_severity) : minimum_severity_(minimum_severity) {}
  void log(nvinfer1::ILogger::Severity severity, const char* msg) noexcept override {
    if (severity <= minimum_severity_) {
      std::string severity_str = severity_to_string(severity);
      std::cout << "[" << severity_str << "] " << std::string(msg) << std::endl;
    }
  }

private:
  std::string severity_to_string(nvinfer1::ILogger::Severity severity) {
    switch (severity) {
    case nvinfer1::ILogger::Severity::kINTERNAL_ERROR:
      return "INTERNAL ERROR";
    case nvinfer1::ILogger::Severity::kERROR:
      return "ERROR";
    case nvinfer1::ILogger::Severity::kWARNING:
      return "WARNING";
    case nvinfer1::ILogger::Severity::kINFO:
      return "INFO";
    case nvinfer1::ILogger::Severity::kVERBOSE:
      return "VERBOSE";
    default:
      throw std::logic_error("Unknown severity: " + std::to_string(uint32_t(severity)));
    }
  }

  nvinfer1::ILogger::Severity minimum_severity_;
};

struct TensorRT8Context {
  std::shared_ptr<nvinfer1::IExecutionContext> ctx;
};

class TRTAllocator : public nvinfer1::IGpuAllocator {
public:
  TRTAllocator() {
    int device_count;
    CUDA_ERROR_CHECK(cudaGetDeviceCount(&device_count));
    // Verify allocator is initialized. No-op if already initialized.
    c10::cuda::CUDACachingAllocator::init(device_count);
  }
  void* allocate(uint64_t size, uint64_t, uint32_t) noexcept override {
    return c10::cuda::CUDACachingAllocator::raw_alloc(size);
  }
  void free(void* memory) noexcept override { c10::cuda::CUDACachingAllocator::raw_delete(memory); }
};

class TensorRT8::Implementation {
public:
  Implementation(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
                 int device, bool implicit_batch_dimension)
      : allocator_(std::make_shared<TRTAllocator>()), input_names_(input_names), output_names_(output_names),
        device_(device), implicit_batch_dimension_(implicit_batch_dimension) {
    std::unique_lock<std::shared_mutex> lck(trt_mutex_);
    if (input_names_.empty()) {
      throw std::invalid_argument("Expected at least one input name.");
    }
    if (output_names_.empty()) {
      throw std::invalid_argument("Expected at least one output name.");
    }

    with_device device_guard(device_);
    cudaDeviceSynchronize();

    // Create runtime.
    auto* runtime_ptr = nvinfer1::createInferRuntime(logger_);
    if (runtime_ptr == nullptr) {
      throw std::runtime_error("Unable to create TensorRT runtime.");
    }
    runtime_ = std::shared_ptr<nvinfer1::IRuntime>(runtime_ptr, [&](nvinfer1::IRuntime* runtime) {
      std::unique_lock<std::shared_mutex> deleter_lck(trt_mutex_);
      with_device deleter_device_guard(device_);
      cudaDeviceSynchronize();
      delete runtime;
    });
    runtime_->setGpuAllocator(allocator_.get());

    // Create engine.
    auto* engine_ptr = runtime_->deserializeCudaEngine(data.data(), data.size());
    if (engine_ptr == nullptr) {
      throw std::runtime_error("Unable to deserialize TensorRT engine.");
    }
    engine_ = std::shared_ptr<nvinfer1::ICudaEngine>(engine_ptr, [&](nvinfer1::ICudaEngine* engine) {
      std::unique_lock<std::shared_mutex> deleter_lck(trt_mutex_);
      with_device deleter_device_guard(device_);
      cudaDeviceSynchronize();
      delete engine;
    });
  }

  std::shared_ptr<TensorRT8Context> create_context() {
    std::unique_lock<std::shared_mutex> lck(trt_mutex_);
    with_device device_guard(device_);

    // Create context.
    auto* context_ptr = engine_->createExecutionContext();
    if (context_ptr == nullptr) {
      throw std::runtime_error("Unable to create execution context.");
    }
    auto context =
        std::shared_ptr<nvinfer1::IExecutionContext>(context_ptr, [&](nvinfer1::IExecutionContext* deleter_context) {
          std::unique_lock<std::shared_mutex> deleter_lck(trt_mutex_);
          with_device deleter_device_guard(device_);
          cudaDeviceSynchronize();
          delete deleter_context;
        });
    return std::shared_ptr<TensorRT8Context>(new TensorRT8Context{.ctx = context});
  }

  std::vector<torch::Tensor> infer(std::shared_ptr<TensorRT8Context> context, std::vector<torch::Tensor> inputs) {
    std::shared_lock<std::shared_mutex> lck(trt_mutex_);
    with_device device_guard(device_);

    for (auto& input : inputs) {
      if (!input.is_contiguous()) {
        throw std::invalid_argument("All input tensors must be contiguous.");
      }
    }

    auto torch_stream = c10::cuda::getCurrentCUDAStream();
    if (torch_stream.device_index() != device_) {
      throw std::invalid_argument("PyTorch CUDA stream does not match TensorRT device index.");
    }

    if (inputs.size() != input_names_.size()) {
      throw std::invalid_argument("Number of inputs must match number of input names.");
    }
    int32_t batch_size = (int32_t)inputs[0].sizes()[0];

    // Create outputs.
    std::vector<torch::Tensor> outputs{};
    for (const auto& output_name : output_names_) {
      int32_t binding_idx = engine_->getBindingIndex(output_name.c_str());

      auto dims = engine_->getBindingDimensions(binding_idx);
      std::vector<int64_t> shape;
      if (implicit_batch_dimension_) {
        shape.push_back(batch_size);
      }
      for (int64_t idx = 0; idx < dims.nbDims; idx++) {
        shape.push_back(dims.d[idx]);
      }

      torch::ScalarType dtype = trt_dtype_to_torch_dtype(engine_->getBindingDataType(binding_idx));
      torch::Device dev_info = torch::Device(torch::DeviceType::CUDA, c10::DeviceIndex(device_));
      if (engine_->getLocation(binding_idx) == nvinfer1::TensorLocation::kHOST) {
        dev_info = torch::Device(torch::DeviceType::CPU);
      }

      auto output = torch::empty(shape, torch::device(dev_info).dtype(dtype));
      outputs.push_back(std::move(output));
    }

    // Make bindings.
    std::vector<void*> bindings((size_t)engine_->getNbBindings());
    for (size_t idx = 0; idx < input_names_.size(); idx++) {
      const auto& input_name = input_names_[idx];
      const auto& input = inputs[idx];
      int32_t binding_idx = engine_->getBindingIndex(input_name.c_str());
      auto binding_dims = engine_->getBindingDimensions(binding_idx);
      int dim_offset = 0;
      if (implicit_batch_dimension_) {
        dim_offset = 1;
      }
      for (int64_t dim = 0; dim < binding_dims.nbDims; dim++) {
        if (binding_dims.d[dim] == -1) {
          continue;
        }
        if (binding_dims.d[dim] != input.size(dim + dim_offset)) {
          throw std::runtime_error(
              fmt::format("Input {} size dimensions do not match expected at dimension {}: {} != {}", binding_idx, idx,
                          binding_dims.d[dim], input.size(dim + dim_offset)));
        }
      }
      bindings[(size_t)binding_idx] = input.data_ptr();
    }
    for (size_t idx = 0; idx < output_names_.size(); idx++) {
      const auto& output_name = output_names_[idx];
      const auto& output = outputs[idx];
      int32_t binding_idx = engine_->getBindingIndex(output_name.c_str());
      bindings[(size_t)binding_idx] = output.data_ptr();
    }

    // Enqueue inference.
    cudaStream_t stream = torch_stream.stream();
    bool ok = context->ctx->enqueue(batch_size, bindings.data(), stream, nullptr);
    if (!ok) {
      throw std::runtime_error("Unable to enqueue TensorRT kernels.");
    }

    return outputs;
  }

private:
  torch::ScalarType trt_dtype_to_torch_dtype(nvinfer1::DataType dtype) {
    switch (dtype) {
    case nvinfer1::DataType::kFLOAT:
      return torch::kFloat32;
    case nvinfer1::DataType::kHALF:
      return torch::kFloat16;
    case nvinfer1::DataType::kINT8:
      return torch::kInt8;
    case nvinfer1::DataType::kINT32:
      return torch::kInt32;
    case nvinfer1::DataType::kBOOL:
      return torch::kBool;
    default:
      throw std::logic_error("Unknown binding type " + std::to_string(int(dtype)));
    }
  }

  static TRTLogger logger_;
  std::shared_ptr<TRTAllocator> allocator_;
  std::shared_ptr<nvinfer1::IRuntime> runtime_;
  std::shared_ptr<nvinfer1::ICudaEngine> engine_;
  std::vector<std::string> input_names_;
  std::vector<std::string> output_names_;
  int device_;
  bool implicit_batch_dimension_;
  static std::shared_mutex trt_mutex_;
};

TRTLogger TensorRT8::Implementation::logger_(nvinfer1::ILogger::Severity::kWARNING);
std::shared_mutex TensorRT8::Implementation::trt_mutex_;

TensorRT8::TensorRT8(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
                     int device, bool implicit_batch_dimension)
    : impl_(new TensorRT8::Implementation(data, input_names, output_names, device, implicit_batch_dimension)) {}

TensorRT8::~TensorRT8() {}

std::shared_ptr<TensorRT8Context> TensorRT8::create_context() { return impl_->create_context(); }

std::vector<torch::Tensor> TensorRT8::infer(std::shared_ptr<TensorRT8Context> context,
                                            std::vector<torch::Tensor> inputs) {
  return impl_->infer(context, inputs);
}

} // namespace deeplearning::server::trt_runtime::compatibility

extern "C" {
std::unique_ptr<deeplearning::server::trt_runtime::compatibility::TensorRT8>
make_tensorrt8(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
               int device, bool implicit_batch_dimension) {
  return std::make_unique<deeplearning::server::trt_runtime::compatibility::TensorRT8>(
      data, input_names, output_names, device, implicit_batch_dimension);
}

void init_trt_plugin_registry_8() {
  static std::mutex mutex;
  static bool initialized = false;

  std::unique_lock<std::mutex> lck(mutex);
  if (initialized) {
    return;
  }

  deeplearning::server::trt_runtime::compatibility::TRTLogger logger(nvinfer1::ILogger::Severity::kINFO);

  const std::string maka_namespace = "maka_trt_extensions";
  initLibNvInferPlugins(&logger, maka_namespace.c_str());

  int32_t num_creators;
  nvinfer1::IPluginCreator* const* creators = getPluginRegistry()->getPluginCreatorList(&num_creators);
  for (int i = 0; i < num_creators; i++) {
    if (std::string(creators[i]->getPluginNamespace()) == maka_namespace) {
      getPluginRegistry()->registerCreator(*creators[i], maka_namespace.c_str());
    }
  }
  initialized = true;
}
}
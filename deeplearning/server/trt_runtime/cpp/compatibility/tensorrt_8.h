#pragma once

#include <memory>
#include <vector>

#include <torch/torch.h>

namespace deeplearning::server::trt_runtime::compatibility {

struct TensorRT8Context;

class TensorRT8 {
public:
  __attribute__((visibility("default")))
  TensorRT8(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
            int device = 0, bool implicit_batch_dimension = true);

  __attribute__((visibility("default"))) virtual ~TensorRT8();

  __attribute__((visibility("default"))) virtual std::shared_ptr<TensorRT8Context> create_context();

  __attribute__((visibility("default"))) virtual std::vector<torch::Tensor>
  infer(std::shared_ptr<TensorRT8Context> context, std::vector<torch::Tensor> inputs);

private:
  class Implementation;
  std::unique_ptr<Implementation> impl_;
};

} // namespace deeplearning::server::trt_runtime::compatibility

extern "C" {
std::unique_ptr<deeplearning::server::trt_runtime::compatibility::TensorRT8>
make_tensorrt8(std::string data, std::vector<std::string> input_names, std::vector<std::string> output_names,
               int device = 0, bool implicit_batch_dimension = true);

void init_trt_plugin_registry_8();
}
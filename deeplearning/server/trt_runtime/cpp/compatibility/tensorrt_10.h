#pragma once

#include <memory>
#include <vector>

#include <torch/torch.h>

namespace deeplearning::server::trt_runtime::compatibility {

struct TensorRT10Context;

class TensorRT10 {
public:
  __attribute__((visibility("default"))) TensorRT10(std::string data, int device = 0);

  __attribute__((visibility("default"))) virtual ~TensorRT10();

  __attribute__((visibility("default"))) virtual std::shared_ptr<TensorRT10Context> create_context();

  __attribute__((visibility("default"))) virtual std::vector<torch::Tensor>
  infer(std::shared_ptr<TensorRT10Context> context, std::vector<torch::Tensor> inputs);

private:
  class Implementation;
  std::unique_ptr<Implementation> impl_;
};

} // namespace deeplearning::server::trt_runtime::compatibility

extern "C" {
std::unique_ptr<deeplearning::server::trt_runtime::compatibility::TensorRT10> make_tensorrt10(std::string data,
                                                                                              int device = 0);

void init_trt_plugin_registry_10();
}
#include "tensorrt_10.h"

#include <NvInferPlugin.h>
#include <NvInferRuntime.h>
#include <c10/cuda/CUDACachingAllocator.h>
#include <c10/cuda/CUDAStream.h>
#include <shared_mutex>

#include "lib/common/cpp/cuda_util.h"

namespace deeplearning::server::trt_runtime::compatibility {

class TRTLogger : public nvinfer1::ILogger {
public:
  TRTLogger(nvinfer1::ILogger::Severity minimum_severity) : minimum_severity_(minimum_severity) {}
  void log(nvinfer1::ILogger::Severity severity, const char* msg) noexcept override {
    if (severity <= minimum_severity_) {
      std::string severity_str = severity_to_string(severity);
      std::cout << "[" << severity_str << "] " << std::string(msg) << std::endl;
    }
  }

private:
  std::string severity_to_string(nvinfer1::ILogger::Severity severity) {
    switch (severity) {
    case nvinfer1::ILogger::Severity::kINTERNAL_ERROR:
      return "INTERNAL ERROR";
    case nvinfer1::ILogger::Severity::kERROR:
      return "ERROR";
    case nvinfer1::ILogger::Severity::kWARNING:
      return "WARNING";
    case nvinfer1::ILogger::Severity::kINFO:
      return "INFO";
    case nvinfer1::ILogger::Severity::kVERBOSE:
      return "VERBOSE";
    default:
      throw std::logic_error("Unknown severity: " + std::to_string(uint32_t(severity)));
    }
  }

  nvinfer1::ILogger::Severity minimum_severity_;
};

struct TensorRT10Context {
  std::shared_ptr<nvinfer1::IExecutionContext> ctx;
};

class TRTAllocator : public nvinfer1::IGpuAllocator {
public:
  TRTAllocator() {
    int device_count;
    CUDA_ERROR_CHECK(cudaGetDeviceCount(&device_count));
    // Verify allocator is initialized. No-op if already initialized.
    c10::cuda::CUDACachingAllocator::init(device_count);
  }
  void* allocateAsync(uint64_t size, uint64_t, uint32_t, cudaStream_t) noexcept override {
    return c10::cuda::CUDACachingAllocator::raw_alloc(size);
  }
  bool deallocateAsync(void* memory, cudaStream_t) noexcept override {
    c10::cuda::CUDACachingAllocator::raw_delete(memory);
    return true;
  }

  void* allocate(uint64_t size, uint64_t, uint32_t) noexcept override {
    return c10::cuda::CUDACachingAllocator::raw_alloc(size);
  }
  bool deallocate(void* memory) noexcept override {
    c10::cuda::CUDACachingAllocator::raw_delete(memory);
    return true;
  }
};

class TensorRT10::Implementation {
public:
  Implementation(std::string data, int device) : allocator_(std::make_shared<TRTAllocator>()), device_(device) {
    std::unique_lock<std::shared_mutex> lck(trt_mutex_);

    with_device device_guard(device_);
    cudaDeviceSynchronize();

    // Create runtime.
    auto* runtime_ptr = nvinfer1::createInferRuntime(logger_);
    if (runtime_ptr == nullptr) {
      throw std::runtime_error("Unable to create TensorRT runtime.");
    }
    runtime_ = std::shared_ptr<nvinfer1::IRuntime>(runtime_ptr, [&](nvinfer1::IRuntime* runtime) {
      std::unique_lock<std::shared_mutex> deleter_lck(trt_mutex_);
      with_device deleter_device_guard(device_);
      cudaDeviceSynchronize();
      delete runtime;
    });
    runtime_->setGpuAllocator(allocator_.get());

    // Create engine.
    auto* engine_ptr = runtime_->deserializeCudaEngine(data.data(), data.size());
    if (engine_ptr == nullptr) {
      throw std::runtime_error("Unable to deserialize TensorRT engine.");
    }
    engine_ = std::shared_ptr<nvinfer1::ICudaEngine>(engine_ptr, [&](nvinfer1::ICudaEngine* engine) {
      std::unique_lock<std::shared_mutex> deleter_lck(trt_mutex_);
      with_device deleter_device_guard(device_);
      cudaDeviceSynchronize();
      delete engine;
    });

    for (int32_t i = 0; i < engine_->getNbIOTensors(); i++) {
      auto name = engine_->getIOTensorName(i);
      auto io_mode = engine_->getTensorIOMode(name);
      if (io_mode == nvinfer1::TensorIOMode::kINPUT) {
        input_names_.push_back(name);
      } else if (io_mode == nvinfer1::TensorIOMode::kOUTPUT) {
        output_names_.push_back(name);
      }
    }
  }

  std::shared_ptr<TensorRT10Context> create_context() {
    std::unique_lock<std::shared_mutex> lck(trt_mutex_);
    with_device device_guard(device_);

    // Create context.
    auto* context_ptr = engine_->createExecutionContext();
    if (context_ptr == nullptr) {
      throw std::runtime_error("Unable to create execution context.");
    }
    auto context =
        std::shared_ptr<nvinfer1::IExecutionContext>(context_ptr, [&](nvinfer1::IExecutionContext* deleter_context) {
          std::unique_lock<std::shared_mutex> deleter_lck(trt_mutex_);
          with_device deleter_device_guard(device_);
          cudaDeviceSynchronize();
          delete deleter_context;
        });
    return std::shared_ptr<TensorRT10Context>(new TensorRT10Context{.ctx = context});
  }

  std::vector<torch::Tensor> infer(std::shared_ptr<TensorRT10Context> context, std::vector<torch::Tensor> inputs) {
    std::shared_lock<std::shared_mutex> lck(trt_mutex_);
    with_device device_guard(device_);

    for (auto& input : inputs) {
      if (!input.is_contiguous()) {
        throw std::invalid_argument("All input tensors must be contiguous.");
      }
    }

    auto torch_stream = c10::cuda::getCurrentCUDAStream();
    if (torch_stream.device_index() != device_) {
      throw std::invalid_argument("PyTorch CUDA stream does not match TensorRT device index.");
    }

    if (inputs.size() != input_names_.size()) {
      throw std::invalid_argument("Number of inputs must match number of input names.");
    }
    int32_t batch_size = (int32_t)inputs[0].sizes()[0];

    // Create outputs.
    std::vector<torch::Tensor> outputs{};
    for (const auto& output_name : output_names_) {
      auto dims = engine_->getTensorShape(output_name.c_str());
      std::vector<int64_t> shape;
      for (int64_t idx = 0; idx < dims.nbDims; idx++) {
        if (idx == 0 && dims.d[idx] == -1) {
          shape.push_back(batch_size);
        } else {
          shape.push_back(dims.d[idx]);
        }
      }

      torch::ScalarType dtype = trt_dtype_to_torch_dtype(engine_->getTensorDataType(output_name.c_str()));
      torch::Device dev_info = torch::Device(torch::DeviceType::CUDA, c10::DeviceIndex(device_));
      if (engine_->getTensorLocation(output_name.c_str()) == nvinfer1::TensorLocation::kHOST) {
        dev_info = torch::Device(torch::DeviceType::CPU);
      }

      auto output = torch::empty(shape, torch::device(dev_info).dtype(dtype));
      outputs.push_back(std::move(output));
    }

    // Make bindings.
    for (size_t idx = 0; idx < input_names_.size(); idx++) {
      const auto& input_name = input_names_[idx];
      const auto& input = inputs[idx];
      nvinfer1::Dims dims;
      auto shape = inputs[idx].sizes();
      dims.nbDims = (int)shape.size();
      for (size_t shape_i = 0; shape_i < shape.size(); shape_i++) {
        dims.d[shape_i] = shape[shape_i];
      }
      if (!context->ctx->setInputShape(input_name.c_str(), dims)) {
        throw std::runtime_error(fmt::format("Unable to set tensor dims {}", input_name));
      }
      if (!context->ctx->setTensorAddress(input_name.c_str(), input.data_ptr())) {
        throw std::runtime_error(fmt::format("Unable to set tensor address {}", input_name));
      }
    }
    if (!context->ctx->allInputDimensionsSpecified()) {
      throw std::runtime_error("Failed to set all input dimensions");
    }
    for (size_t idx = 0; idx < output_names_.size(); idx++) {
      const auto& output_name = output_names_[idx];
      const auto& output = outputs[idx];
      if (!context->ctx->setTensorAddress(output_name.c_str(), output.data_ptr())) {
        throw std::runtime_error(fmt::format("Unable to set tensor address {}", output_name));
      }
    }

    // Enqueue inference.
    cudaStream_t stream = torch_stream.stream();
    bool ok = context->ctx->enqueueV3(stream);
    if (!ok) {
      throw std::runtime_error("Unable to enqueue TensorRT kernels.");
    }

    return outputs;
  }

private:
  torch::ScalarType trt_dtype_to_torch_dtype(nvinfer1::DataType dtype) {
    switch (dtype) {
    case nvinfer1::DataType::kFLOAT:
      return torch::kFloat32;
    case nvinfer1::DataType::kHALF:
      return torch::kFloat16;
    case nvinfer1::DataType::kINT8:
      return torch::kInt8;
    case nvinfer1::DataType::kINT32:
      return torch::kInt32;
    case nvinfer1::DataType::kBOOL:
      return torch::kBool;
    default:
      throw std::logic_error("Unknown binding type " + std::to_string(int(dtype)));
    }
  }

  static TRTLogger logger_;
  std::shared_ptr<TRTAllocator> allocator_;
  std::shared_ptr<nvinfer1::IRuntime> runtime_;
  std::shared_ptr<nvinfer1::ICudaEngine> engine_;
  std::vector<std::string> input_names_;
  std::vector<std::string> output_names_;
  int device_;
  static std::shared_mutex trt_mutex_;
};

TRTLogger TensorRT10::Implementation::logger_(nvinfer1::ILogger::Severity::kWARNING);
std::shared_mutex TensorRT10::Implementation::trt_mutex_;

TensorRT10::TensorRT10(std::string data, int device) : impl_(new TensorRT10::Implementation(data, device)) {}

TensorRT10::~TensorRT10() {}

std::shared_ptr<TensorRT10Context> TensorRT10::create_context() { return impl_->create_context(); }

std::vector<torch::Tensor> TensorRT10::infer(std::shared_ptr<TensorRT10Context> context,
                                             std::vector<torch::Tensor> inputs) {
  return impl_->infer(context, inputs);
}

} // namespace deeplearning::server::trt_runtime::compatibility

extern "C" {
std::unique_ptr<deeplearning::server::trt_runtime::compatibility::TensorRT10> make_tensorrt10(std::string data,
                                                                                              int device) {
  return std::make_unique<deeplearning::server::trt_runtime::compatibility::TensorRT10>(data, device);
}

void init_trt_plugin_registry_10() {
  static std::mutex mutex;
  static bool initialized = false;

  std::unique_lock<std::mutex> lck(mutex);
  if (initialized) {
    return;
  }

  deeplearning::server::trt_runtime::compatibility::TRTLogger logger(nvinfer1::ILogger::Severity::kINFO);

  const std::string maka_namespace = "maka_trt_extensions";
  initLibNvInferPlugins(&logger, maka_namespace.c_str());

  int32_t num_creators;
  nvinfer1::IPluginCreatorInterface* const* creators = getPluginRegistry()->getAllCreators(&num_creators);
  for (int i = 0; i < num_creators; i++) {
    auto plugin_creator = dynamic_cast<nvinfer1::IPluginCreator*>(creators[i]);
    if (plugin_creator && std::string(plugin_creator->getPluginNamespace()) == maka_namespace) {
      getPluginRegistry()->registerCreator(*creators[i], maka_namespace.c_str());
    }
  }
  initialized = true;
}
}
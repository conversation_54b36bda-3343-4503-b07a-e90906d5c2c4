add_compile_options(-Wl,-rdynamic -fPIC -Wno-deprecated-declarations)

add_library(tensorrt8_compatibility SHARED tensorrt_8.cpp)
add_dependencies(tensorrt8_compatibility trt_extensions_8)
target_link_libraries(tensorrt8_compatibility PRIVATE torch)
target_link_libraries(tensorrt8_compatibility PRIVATE cudnn nvrtc nvrtc-builtins protobuf fmt)
if (NOT CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "aarch64")
target_include_directories(tensorrt8_compatibility SYSTEM BEFORE PRIVATE /opt/TensorRT-*******/targets/x86_64-linux-gnu/include/)
target_link_directories(tensorrt8_compatibility PRIVATE /opt/TensorRT-*******/targets/x86_64-linux-gnu/lib/)
target_link_libraries(tensorrt8_compatibility PRIVATE cudnn nvptxcompiler_static nvrtc_static nvrtc-builtins_static protobuf fmt)
# Force linker to link unused library trt_extensions for TensorRT plugins
target_link_options(tensorrt8_compatibility PRIVATE LINKER:--no-as-needed,-whole-archive,-ltrt_extensions_8,/opt/TensorRT-*******/targets/x86_64-linux-gnu/lib/libnvinfer.so,/opt/TensorRT-*******/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so,-no-whole-archive,--as-needed)
else()
target_link_directories(tensorrt8_compatibility PUBLIC /usr/local/cuda/lib64/)
target_link_libraries(tensorrt8_compatibility PRIVATE cudnn nvrtc nvrtc-builtins protobuf fmt nvinfer nvinfer_plugin)
target_link_options(tensorrt8_compatibility PRIVATE LINKER:--no-as-needed,-whole-archive,-ltrt_extensions_8,-no-whole-archive,--as-needed)
endif()

if (NOT CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "aarch64")
add_library(tensorrt10_compatibility SHARED tensorrt_10.cpp)
add_dependencies(tensorrt10_compatibility trt_extensions_10)
target_include_directories(tensorrt10_compatibility SYSTEM BEFORE PRIVATE /opt/TensorRT-********/targets/x86_64-linux-gnu/include/)
target_link_directories(tensorrt10_compatibility PRIVATE /opt/TensorRT-********/targets/x86_64-linux-gnu/lib/)
target_link_libraries(tensorrt10_compatibility PRIVATE torch)
target_link_libraries(tensorrt10_compatibility PRIVATE cudnn nvptxcompiler_static nvrtc_static nvrtc-builtins_static protobuf fmt)
# Force linker to link unused library trt_extensions for TensorRT plugins
target_link_options(tensorrt10_compatibility PRIVATE LINKER:--no-as-needed,-whole-archive,-ltrt_extensions_10,/opt/TensorRT-********/targets/x86_64-linux-gnu/lib/libnvinfer.so,/opt/TensorRT-********/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so,-no-whole-archive,--as-needed)
else()
add_custom_target(tensorrt10_compatibility)
endif()
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "runtime.h"

namespace py = pybind11;

namespace deeplearning {
namespace server {
namespace trt_runtime {
PYBIND11_MODULE(trt_runtime_python, m) {
  py::class_<TRTRuntime>(m, "TRTRuntime")
      .def(py::init<const py::bytes&, const std::vector<std::string>&, const std::vector<std::string>&,
                    const std::string&, const int, const bool>(),
           py::arg("bytes"), py::arg("input_names"), py::arg("output_names"), py::arg("version"), py::arg("device") = 0,
           py::arg("implicit_batch_dimension") = true, py::call_guard<py::gil_scoped_release>())
      .def("create_context", &TRTRuntime::create_context, py::call_guard<py::gil_scoped_release>())
      .def("infer", &TRTRuntime::infer, py::arg("context"), py::arg("inputs"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<TRTContext>(m, "TRTContext");
}
} // namespace trt_runtime
} // namespace server
} // namespace deeplearning
add_subdirectory(compatibility)

add_compile_options(-fvisibility=default -Wl,-rdynamic)

file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
list(REMOVE_ITEM SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/trt_runtime_python.cpp)

pybind11_add_module(trt_runtime_python SHARED trt_runtime_python.cpp)

add_library(trt_runtime SHARED ${SOURCES})
# https://github.com/pybind/pybind11/issues/1604
target_compile_options(trt_runtime PRIVATE -fsized-deallocation -Wno-deprecated-declarations)
target_link_directories(trt_runtime PUBLIC /usr/local/cuda/lib64/)
target_link_libraries(trt_runtime PUBLIC torch ${CMAKE_DL_LIBS} fmt)
target_link_options(trt_runtime PRIVATE LINKER:--no-as-needed,-lcudnn,-lprotobuf,--as-needed)
add_dependencies(trt_runtime tensorrt8_compatibility tensorrt10_compatibility)

target_link_libraries(trt_runtime_python PUBLIC trt_runtime torch_python)
# https://github.com/pybind/pybind11/issues/1604
target_compile_options(trt_runtime_python PRIVATE -fsized-deallocation)
target_compile_definitions(trt_runtime_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND TORCH_API_INCLUDE_EXTENSION_H=1)
set_target_properties(trt_runtime_python  PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

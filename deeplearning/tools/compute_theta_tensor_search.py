import math

import torch

from deeplearning.furrows.line_utils import compute_theta_tensor

THRESHOLD = 1e-2


def main() -> None:
    eps = [1e-8, 1e-7, 1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1, 1, math.pi, 2 * math.pi]

    theta_float = torch.arange(0, math.pi, 1e-5, dtype=torch.float).unsqueeze(1).unsqueeze(0)
    sin_float = torch.sin(theta_float)
    cos_float = torch.cos(theta_float)

    float_theta = torch.cat((cos_float, sin_float), -1)

    theta_2_float = 2 * theta_float
    sin_2_float = torch.sin(theta_2_float)
    cos_2_float = torch.cos(theta_2_float)

    float_2theta = torch.cat((cos_2_float, sin_2_float), -1)

    for ep in eps:
        # For each theta, get sin and cos of theta and 2theta, then half 2theta and compare
        float_half_2theta = compute_theta_tensor(float_2theta, ep)

        float_accuracy = ((float_half_2theta - float_theta).abs() < THRESHOLD).float().mean().item()

        print("Eps: ", ep, "float acc: ", float_accuracy)


if __name__ == "__main__":
    main()

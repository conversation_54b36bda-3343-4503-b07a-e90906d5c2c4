import argparse
import datetime
import multiprocessing
import os
import random
import time
from typing import Dict, List, Optional

import matplotlib.pyplot as plt
import torch
import tqdm

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.embeddings.utils import get_embedding_hdf5_s3_path
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

GEOHASH_PRECISION = 4
WEED_CATEGORIES = ["grass", "offshoot", "purslane", "broadleaf"]
NUM_PROCESSES = 8


def sample_distances(
    dataset: EmbeddingDataset, indices_1: List[int], indices_2: List[int], samples: int = 10000, num_examples: int = 1
) -> List[float]:

    indices_1_samples = random.choices(indices_1, k=samples * num_examples)
    indices_2_samples = random.choices(indices_2, k=samples)

    distances = []
    for sample_index in range(samples):

        support_indices = [
            indices_1_samples[index] for index in range(sample_index * num_examples, (sample_index + 1) * num_examples)
        ]
        query_index = indices_2_samples[sample_index]

        support_embeddings = [dataset[index].embedding for index in support_indices]
        query_embedding = dataset[query_index].embedding

        distance_batch = []
        for embedding in support_embeddings:
            # distance = torch.linalg.vector_norm(embedding - query_embedding)
            distance = torch.cosine_similarity(embedding, query_embedding, dim=0).item()
            distance_batch.append(distance)

        distance = max(distance_batch)
        distances.append(distance)

    return distances


def generate_plot(dataset: EmbeddingDataset, data_block: Dict[str, List[int]], filepath: str) -> None:

    if os.path.exists(filepath):
        return None

    _, axes = plt.subplots(len(data_block.keys()), len(data_block.keys()), figsize=(28, 28))

    for row_index, (category_1, indices_1) in enumerate(data_block.items()):
        for col_index, (category_2, indices_2) in enumerate(data_block.items()):

            if len(indices_1) < 50 or len(indices_2) < 50:
                return None

    max_y = 0
    max_x = 0
    min_x = 0
    for row_index, (category_1, indices_1) in enumerate(data_block.items()):
        for col_index, (category_2, indices_2) in enumerate(data_block.items()):

            distances = sample_distances(dataset, indices_1, indices_2)

            if len(data_block.items()) > 1:
                axis = axes[row_index, col_index]
            else:
                axis = axes

            y, x, _ = axis.hist(distances, bins=300, density=True)

            if max_y < y.max():
                max_y = y.max()

            if max_x < x.max():
                max_x = x.max()

            if min_x > x.min():
                min_x = x.min()
            axis.set_title(f"{category_1}\n{category_2}")

    for row_index, (category_1, indices_1) in enumerate(data_block.items()):
        for col_index, (category_2, indices_2) in enumerate(data_block.items()):

            if len(data_block.items()) > 1:
                axis = axes[row_index, col_index]
            else:
                axis = axes

            x_center = (x.min() + x.max()) / 2
            x_radius = (x.max() - x.min()) / 2
            x_radius = x_radius * 1.1

            axis.set_ylim(0, max_y * 1.1)
            axis.set_xlim(x_center - x_radius, x_center + x_radius)

    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    plt.savefig(filepath)


def add_to_data_block(
    data_blocks: Dict[str, Dict[str, List[int]]], key: str, label: str
) -> Dict[str, Dict[str, List[int]]]:

    if key not in data_blocks.keys():
        data_blocks[key] = {}

    if label not in data_blocks[key].keys():
        data_blocks[key][label] = []

    data_blocks[key][label].append(index)

    return data_blocks


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("--dataset-id", type=str)
    parser.add_argument("--model-id", type=str)
    parser.add_argument("--index", type=int)

    args = parser.parse_args()

    embeddings_bucket = "carbon-ml"
    embeddings_s3_key = get_embedding_hdf5_s3_path(args.dataset_id, args.model_id)
    embeddings_filepath = f"{CARBON_DATA_DIR}/deeplearning/{embeddings_s3_key}"

    s3_cache_proxy_host = os.getenv("S3_CACHE_PROXY_HOST")
    s3_cache_proxy_client = S3CacheProxyClient(s3_cache_proxy_host)

    if not os.path.exists(embeddings_filepath):
        s3_cache_proxy_client.download(embeddings_bucket, embeddings_s3_key, embeddings_filepath)

    dataset = EmbeddingDataset(embeddings_filepath)

    data_blocks: Dict[str, Dict[str, List[int]]] = {}
    for index in tqdm.tqdm(range(len(dataset))):
        metadata = dataset.get_metadata(index)

        crop_id = metadata.image_crop_id

        geohash = metadata.geohash[:GEOHASH_PRECISION]
        date = str(datetime.datetime.fromtimestamp(metadata.captured_at / 1000).date())

        data_blocks = add_to_data_block(
            data_blocks, f"cosine-small-{crop_id}-global-all-points-num-examples-1", "all-points"
        )
        data_blocks = add_to_data_block(
            data_blocks,
            f"cosine-small-{crop_id}-global-hits-num-examples-1",
            "weed" if metadata.point_category_id in WEED_CATEGORIES else "crop",
        )
        data_blocks = add_to_data_block(
            data_blocks, f"cosine-small-{crop_id}-global-categories-num-examples-1", metadata.point_category_id
        )

        if metadata.point_category_id in ["grass", "broadleaf"]:
            for i in range(10):
                data_blocks = add_to_data_block(data_blocks, f"one-random-grass-{i}", metadata.point_category_id)

        data_blocks = add_to_data_block(data_blocks, f"{crop_id}-local-all-points-{geohash}-{date}", "all-points")
        data_blocks = add_to_data_block(
            data_blocks,
            f"{crop_id}-local-hits-{geohash}-{date}",
            "weed" if metadata.point_category_id in WEED_CATEGORIES else "crop",
        )
        data_blocks = add_to_data_block(
            data_blocks, f"{crop_id}-local-categories-{geohash}-{date}", metadata.point_category_id
        )

    processes: Dict[int, Optional[multiprocessing.Process]] = {}
    process: Optional[multiprocessing.Process]
    for process_id, (key, data_block) in enumerate(data_blocks.items()):
        print(f"Running {key}")
        directory = f"{CARBON_DATA_DIR}/deeplearning/embeddings/{args.dataset_id}/{args.model_id}/figures"
        filepath = f"{directory}/{key}.png"
        process = multiprocessing.Process(target=generate_plot, args=(dataset, data_block, filepath))
        processes[process_id] = process

    generating_plots = True
    running_processes = 0
    finished_processes = 0
    print_time = time.time()
    while generating_plots:

        if len(processes) == 0:
            break

        for process_id in processes.keys():

            process = processes[process_id]

            generating_plots = False

            if process is None:
                continue

            if process.pid is None:
                if running_processes < NUM_PROCESSES:
                    process.start()
                    running_processes += 1
                    generating_plots = True
            else:
                if process.is_alive():
                    generating_plots = True
                else:
                    finished_processes += 1
                    running_processes -= 1

                    processes[process_id] = None

        if time.time() - print_time > 5:
            print(f"Total: {len(processes)}, Running: {running_processes}, Finished: {finished_processes}")
            print_time = time.time()

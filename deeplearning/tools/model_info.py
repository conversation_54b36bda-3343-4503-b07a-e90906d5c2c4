#!/usr/bin/env python3

import argparse

from deeplearning.model_io import load_model


def model_info(path: str) -> None:
    (model, metadata) = load_model(path)
    print("Loaded model: {}".format(path))
    print(metadata.dump())


def main() -> None:
    p = argparse.ArgumentParser()
    p.add_argument("-m", "--model", help="Model to load", required=True)
    args = p.parse_args()
    model_info(args.model)


if __name__ == "__main__":
    main()

import argparse
import logging
import os
import random
from typing import Any, Dict, List, Optional, Tuple

import matplotlib
import matplotlib.pyplot as plt
import numpy.typing as npt
import torch
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler

logging.basicConfig()
logger = logging.getLogger()
logger.setLevel(logging.INFO)


def format_size(size: List[float]) -> List[str]:
    return [f"{x//5*5} - {(x//5 + 1)*5}" for x in size]


def format_hue(hue: List[str]) -> Tuple[List[str], List[int]]:
    labels = list(set(hue))

    legend = [f"{x}:{hue.count(x)}" for x in labels]

    colors = [labels.index(x) for x in hue]
    return legend, colors


def filter_observations(
    data: Dict[str, Any], score_threshold: float = 0.5, not_wanted: Optional[List[str]] = None
) -> Tuple[torch.Tensor, List[Any], str]:
    not_wanted = not_wanted if not_wanted is not None else []
    filtered_features = []
    filtered_metadata = []
    model_id = data["model_id"]
    for features, metadata in zip(data["data"], data["metadata"]):
        if metadata["score"] > score_threshold and metadata["predicted_clz"] not in not_wanted:
            filtered_features.append(features.unsqueeze(0))
            filtered_metadata.append(metadata)

    f_features = torch.cat(filtered_features, dim=0) if len(filtered_features) > 0 else torch.tensor([])
    return f_features, filtered_metadata, model_id


def load_data(
    path: str, parameters: Dict[str, Any], not_wanted: Optional[List[str]] = None
) -> Tuple[npt.NDArray[Any], List[Dict[str, Any]], str]:
    not_wanted = not_wanted if not_wanted is not None else []
    score_threshold = parameters["score_threshold"]

    loaded_f, loaded_m, model_id = filter_observations(
        torch.load(os.path.join(path, "feature_vectors.pt")), score_threshold=score_threshold, not_wanted=not_wanted
    )

    features = do_pca(loaded_f, parameters=parameters)
    metadata = loaded_m

    return features, metadata, model_id


def do_pca(features: torch.Tensor, parameters: Dict[str, Any],) -> npt.NDArray[Any]:

    pca_dim = parameters["pca_dim"]

    scaler = StandardScaler()

    scaled_features: npt.NDArray[Any] = scaler.fit_transform(features)
    if pca_dim is not None:
        pca = PCA(n_components=pca_dim)
        scaled_features = pca.fit_transform(scaled_features)

    return scaled_features


def do_tsne(
    features: npt.NDArray[Any], metadata: List[Dict[str, Any]], parameters: Dict[str, Any], not_wanted: List[str]
) -> Tuple[npt.NDArray[Any], List[Any]]:

    perplexity = parameters["perplexity"]
    n_iter = parameters["n_iter"]
    learning_rate = parameters["learning_rate"]
    tsne = TSNE(
        n_components=2,
        method="barnes_hut",
        perplexity=perplexity,
        learning_rate=learning_rate,
        n_iter=n_iter,
        n_jobs=-1,
    )

    indices = [i for i, x in enumerate(metadata) if x["predicted_clz"] not in not_wanted]
    if parameters["num_samples"] < len(indices):
        indices = random.sample(indices, parameters["num_samples"])

    tsne_res: npt.NDArray[Any] = tsne.fit_transform(features[indices])

    return tsne_res, [metadata[idx] for idx in indices]


def make_plots(
    tsne_res: npt.NDArray[Any],
    metadata: List[Dict[str, Any]],
    parameters: Dict[str, int],
    save_dir: str,
    num_samples: int = 1000,
    title: str = "",
    graph_type: str = "type",
) -> None:
    assert num_samples <= len(
        tsne_res
    ), f"Cannot Have More Samples Than Feature_Vectors, {num_samples} vs {len(tsne_res)}"

    x = tsne_res[:, 0]
    y = tsne_res[:, 1]

    if graph_type == "type":
        legend, color = format_hue([x["predicted_clz"] for x in metadata])
    elif graph_type == "size":
        legend, color = format_hue(format_size([x["r"] for x in metadata]))
    elif graph_type == "weedcrop":
        legend, color = format_hue(
            [
                "WEED" if x["predicted_clz"] in ["BROADLEAF", "GRASS", "PURSLANE", "OFFSHOOT"] else "CROP"
                for x in metadata
            ]
        )

    matplotlib.rcParams["savefig.dpi"] = 300
    matplotlib.rcParams["font.size"] = 20
    matplotlib.rcParams["figure.figsize"] = [25, 25]

    fig, ax = plt.subplots()

    scatter = ax.scatter(x=x, y=y, c=color, cmap="tab20" if graph_type != "size" else "viridis")

    if graph_type == "size":
        plt.colorbar(scatter)
    else:
        legend = ax.legend(scatter.legend_elements()[0], legend, loc="upper right", title=graph_type)
        ax.add_artist(legend)

    ax.set_title(parameters["model_id"])
    plt.text(0, min(y), parameters, fontsize=12, ha="center")

    plt.axis("off")
    plt.tick_params(left=False, bottom=False)
    #    plt.setp(plt.get_legend().get_texts(), fontsize='22') # for legend text

    plt.ylim(min(y) - 0, max(y) + 0)
    plt.xlim(min(x) - 0, max(x) + 0)

    plt.savefig(os.path.join(save_dir, title))
    plt.close()


def main() -> None:

    parser = argparse.ArgumentParser()
    parser.add_argument("--directory", type=str, required=True)
    parser.add_argument("--save-dir", type=str, default="deeplearning/tsne/")

    parser.add_argument("--score-threshold", type=float, default=0.5)
    parser.add_argument("--num-samples", type=int, default=100000)

    parser.add_argument("--perplexity", type=int, default=10)
    parser.add_argument("--n-iter", type=int, default=500)
    parser.add_argument("--learning-rate", type=int, default=10)
    parser.add_argument("--pca-dim", type=int, default=100)
    args = parser.parse_args()

    parameters = {
        "perplexity": args.perplexity,
        "n_iter": args.n_iter,
        "learning_rate": args.learning_rate,
        "pca_dim": args.pca_dim,
        "score_threshold": args.score_threshold,
        "num_samples": args.num_samples,
    }

    not_wanted = ["PLANT", "UNKNOWN"]

    if not os.path.exists(args.save_dir):
        os.makedirs(args.save_dir)

    processed_features, metadata, model_id = load_data(args.directory, not_wanted=not_wanted, parameters=parameters)
    parameters["model_id"] = model_id

    # Generating All The Plots
    # ----All----
    logger.info("Generating Plots")
    tsne_res, processed_metadata = do_tsne(processed_features, metadata, parameters=parameters, not_wanted=not_wanted)
    make_plots(
        tsne_res,
        processed_metadata,
        num_samples=len(tsne_res),
        parameters=parameters,
        save_dir=args.save_dir,
        title="all.png",
    )
    make_plots(
        tsne_res,
        processed_metadata,
        num_samples=len(tsne_res),
        parameters=parameters,
        save_dir=args.save_dir,
        title="weedcrop.png",
        graph_type="weedcrop",
    )

    # ----Single Classes----
    classes = list(set([x["predicted_clz"] for x in metadata]))
    for clz in classes:
        if len([x for x in metadata if x["predicted_clz"] == clz]) < args.perplexity:
            continue

        tsne_res, processed_metadata = do_tsne(
            processed_features, metadata, parameters=parameters, not_wanted=[x for x in classes if x != clz]
        )
        make_plots(
            tsne_res,
            processed_metadata,
            num_samples=len(tsne_res),
            parameters=parameters,
            title=f"{clz}.png",
            save_dir=args.save_dir,
            graph_type="size",
        )

    # ----Weeds----
    tsne_res, processed_metadata = do_tsne(
        processed_features, metadata, parameters=parameters, not_wanted=not_wanted + ["CROP"]
    )
    make_plots(
        tsne_res,
        processed_metadata,
        num_samples=len(tsne_res),
        parameters=parameters,
        title="weeds.png",
        save_dir=args.save_dir,
    )

    # ----Size----
    tsne_res, processed_metadata = do_tsne(processed_features, metadata, parameters=parameters, not_wanted=not_wanted)
    make_plots(
        tsne_res,
        processed_metadata,
        parameters=parameters,
        num_samples=len(tsne_res),
        title="size.png",
        save_dir=args.save_dir,
        graph_type="size",
    )


if __name__ == "__main__":
    main()

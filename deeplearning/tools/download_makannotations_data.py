#!/usr/bin/env python3
import argparse
import fnmatch
import functools
import logging
import multiprocessing
import os
import subprocess
from typing import Any, List, Optional, Set

import boto3
import requests
import tqdm

from deeplearning.utils.dataset import get_carbon_cache_host

LOG = logging.getLogger(__name__)

bucket = "maka-pono"
aws_base = f"s3://{bucket}/"


def download_one_image(dest_path: str, file: str, label_name: str, carbon_cache_host: Optional[str] = None) -> None:
    if os.path.exists(file):
        return
    s3_filename = file[len(dest_path) :].lstrip("/")

    os.makedirs(os.path.dirname(file), exist_ok=True)
    if carbon_cache_host is not None:
        try:
            response = requests.get(f"http://{carbon_cache_host}/maka-pono/{s3_filename}", timeout=30)
        except Exception as e:
            logging.warning(f"Request to carbon cache failed: {e}. Retrying.")
            response = requests.get(f"http://{carbon_cache_host}/maka-pono/{s3_filename}", timeout=30)

        if response.status_code == 200:
            with open(file, "wb") as f:
                f.write(response.content)
        else:
            # Metadata files may not exist
            if file.endswith("metadata.json") or file.endswith(f"mask_{label_name}.png"):
                pass
            else:
                raise ConnectionError(
                    f"failed to retrieve from s3-cache-proxy: {response.status_code} {str(response.content)}"
                )
    else:
        try:
            boto3.resource("s3").Bucket("maka-pono").download_file(s3_filename, file)
        except Exception:
            # Metadata files may not exist
            if file.endswith("metadata.json") or file.endswith(f"mask_{label_name}.png"):
                pass
            else:
                raise


def add_if_not_exists(file_set: Set[str], path: str) -> None:
    if not os.path.exists(path):
        file_set.add(path)


def sync_images_with_masks_helper(prefix: str, client: boto3.client, dest_path: str, label_name: str) -> None:
    result = client.list_objects(Bucket=bucket, Prefix=os.path.join(prefix, "meta.json"), Delimiter="/")
    contents = result.get("Contents")
    if contents:
        aws_dir = os.path.join(aws_base, prefix)
        local_dir = os.path.join(dest_path, prefix)
        subprocess.check_call(
            f'python3 -m awscli s3 sync "{aws_dir}" "{local_dir}" --exclude "*" --include "*.mask_{label_name}.json" --include "**/*.mask_{label_name}.json" --include "**/meta.json" --include "meta.json"',
            shell=True,
        )

    result = client.list_objects(Bucket=bucket, Prefix=prefix, Delimiter="/")
    common_prefixes = result.get("CommonPrefixes")
    if common_prefixes is None:
        return

    for common_prefix in common_prefixes:
        sync_images_with_masks_helper(common_prefix["Prefix"], client, dest_path, label_name=label_name)


def sync_images_with_masks(directories: List[str], dest_path: str = "./", label_name: str = "match") -> None:
    client = boto3.client("s3")

    file_set: Set[str] = set()
    for directory in directories:
        sync_images_with_masks_helper(directory + "/", client, dest_path, label_name=label_name)

    for root, _, files in os.walk(dest_path):
        for filename in files:
            if not fnmatch.fnmatch(filename, "*.mask_*"):
                continue

            file_last_part = filename.split(".")
            image_name = ".".join(file_last_part[:-2])
            # For P2P data, masks are at <filename>.image_annotated.mask_match.json but metadata is at <filename>.metadata.json, so we need to strip image_annotated off
            if "image_annotated" == file_last_part[-3]:
                image_name_without_image_annotated = ".".join(file_last_part[:-3])
                add_if_not_exists(file_set, os.path.join(root, f"{image_name_without_image_annotated}.metadata.json"))
            else:
                add_if_not_exists(file_set, os.path.join(root, f"{image_name}.metadata.json"))
            add_if_not_exists(file_set, os.path.join(root, f"{image_name}.mask_{label_name}.png"))

    pool = multiprocessing.Pool(4)
    iterator = pool.imap_unordered(
        functools.partial(
            download_one_image, dest_path, carbon_cache_host=get_carbon_cache_host(), label_name=label_name
        ),
        list(file_set),
        chunksize=1,
    )

    for _ in tqdm.tqdm(iterator, total=len(file_set)):
        pass

    pool.close()

    print(f"Number of files downloaded: {len(file_set)}")


def parse() -> Any:
    parser = argparse.ArgumentParser()
    parser.add_argument("--directories", required=True, type=str)
    return parser.parse_args()


def main() -> None:
    args = parse()
    directories = args.directories.split(",")

    sync_images_with_masks(directories)


if __name__ == "__main__":
    main()

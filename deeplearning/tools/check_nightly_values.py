import json
import os
import sys
from argparse import ArgumentParser, Namespace

THRESHOLD = "threshold"
TYPE = "type"
TEST_RESULTS_FILE = "test_results.json"
NONAVERAGE_TEST_RESULTS_FILE = "nonaverage_test_results.pickle"

THRESHOLDS = {
    "furrows": {
        "test_oec": {THRESHOLD: 0.65, TYPE: ">"},
        # "avg_test_line_loss": {THRESHOLD: 0.7, TYPE: "<"},
        "avg_test_central_line_offset_mae": {THRESHOLD: 0.05, TYPE: "<"},
        "avg_test_central_line_angle_mae": {THRESHOLD: 0.3, TYPE: "<"}
        # TODO(raven) Find root cause of avg_test_angle_mae being nan
        # "avg_test_angle_mae": {THRESHOLD: 0.4, TYPE: "<"},
    },
    "deepweed": {
        "test_oec": {THRESHOLD: 0.9, TYPE: ">"},
        # "avg_test_point_hit_loss": {THRESHOLD: 0.5, TYPE: "<"},
        # "test_giou": {THRESHOLD: 0.75, TYPE: ">"},
        # "test_miou": {THRESHOLD: 0.65, TYPE: ">"},
        # "avg_test_loss": {THRESHOLD: 0.5, TYPE: "<"},
    },
}


def parse_arguments() -> Namespace:
    p = ArgumentParser()
    p.add_argument("-l", "--lightning-log-dir", help="lightning log directory", required=True)
    p.add_argument("-m", "--model", help="deepweed or furrows", required=True)
    p.add_argument("-f", "--file", help="file to log to")

    args = p.parse_args()

    return args


def main() -> None:
    args = parse_arguments()

    path = os.path.join(args.lightning_log_dir, "test_results.json")

    with open(path,) as f:
        vals = json.load(f)
        thresholds = THRESHOLDS[args.model]

        passed = []
        failed = []

        for metric, thresh in thresholds.items():
            val = vals[metric]

            if eval(f"{val} {thresh[TYPE]} {thresh[THRESHOLD]}"):
                passed.append((metric, val, thresh[TYPE], thresh[THRESHOLD]))
            else:
                failed.append((metric, val, thresh[TYPE], thresh[THRESHOLD]))

        passed_str = "\n".join([f"\t{i[0]} has value {i[1]}, {i[2]} required value of {i[3]}" for i in passed])
        failed_str = "\n".join([f"\t{i[0]} has value {i[1]}, must be {i[2]} {i[3]}" for i in failed])

        if args.file:
            with open(args.file, "a+") as fi:
                fi.write(f"Model: {args.model}\n\n")
                fi.write("Succeeded:\n")
                fi.write(passed_str)
                fi.write("\nFailed:\n")
                fi.write(failed_str)
                fi.write("\n===================\n")

        print("Succeeded: \n", passed_str)
        if len(failed):
            print("Failed: \n", failed_str)
            sys.exit("ERROR: One or more items did not meet threshold")


if __name__ == "__main__":
    main()

import argparse
import os

import matplotlib.pyplot as plt

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.embeddings.utils import get_embedding_hdf5_s3_path
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("--dataset-id", type=str)
    parser.add_argument("--model-id", type=str)
    parser.add_argument("--index", type=int)

    args = parser.parse_args()

    embeddings_bucket = "carbon-ml"
    embeddings_s3_key = get_embedding_hdf5_s3_path(args.dataset_id, args.model_id)
    embeddings_filepath = f"{CARBON_DATA_DIR}/deeplearning/{embeddings_s3_key}"

    s3_cache_proxy_host = os.getenv("S3_CACHE_PROXY_HOST")
    s3_cache_proxy_client = S3CacheProxyClient(s3_cache_proxy_host)

    if not os.path.exists(embeddings_filepath):
        s3_cache_proxy_client.download(embeddings_bucket, embeddings_s3_key, embeddings_filepath)

    dataset = EmbeddingDataset(embeddings_filepath)

    datapoint = dataset[args.index]

    figure = plt.figure()

    x = [index for index in range(len(datapoint.embedding.numpy()))]
    height = datapoint.embedding.numpy()

    plt.bar(x, height)
    plt.title(
        f"Single Embedding Visualization\nModel ID: {args.model_id}\nDataset ID: {args.dataset_id}\nIndex: {args.index}",
        fontsize=6,
        loc="left",
    )

    plt.savefig("test.png")

#!/usr/bin/env python3
import subprocess
from smtplib import SMTP

CI_USERNAME = "<EMAIL>"
CI_EMAIL = "<EMAIL>"
CI_PASSWORD = "naajczagjydujpcy"
DL_EMAILS = "<EMAIL>,<EMAIL>,<EMAIL>"


vm_name = subprocess.check_output("dmidecode --string baseboard-asset-tag", shell=True).strip().decode("ascii")
with SMTP("smtp.gmail.com:587") as smtp:
    smtp.starttls()
    smtp.login(CI_USERNAME, CI_PASSWORD)
    smtp.sendmail(
        CI_EMAIL,
        DL_EMAILS,
        f""" \
From: VM Reminder
To: {DL_EMAILS}
Subject: VM {vm_name} is running

VM {vm_name} is running. This is a reminder to check whether it should be shut down.
""",
    )

import argparse
import io
import json
import multiprocessing
import os
from typing import Optional, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt
import plotly.express as px
import plotly.graph_objects as go
import pydantic
from PIL import Image

from lib.common.s3_cache_proxy.client import S3CacheProxyClient

MAKA_DATA_DIR = os.getenv("MAKA_DATA_DIR")
S3_CACHE_PROXY_SERVICE_HOST = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")

REMOVED_PIXELS = 10


def compute_frequency_spectrum(image: Image.Image, limit: int = 30) -> Image.Image:

    image = image.convert("L")

    array = np.array(image)

    fourier_transform = np.fft.fft2(array)
    fourier_shift = np.fft.fftshift(fourier_transform)
    frequency_spectrum: npt.NDArray[np.float64] = np.log(1 + np.abs(fourier_shift))
    frequency_spectrum[frequency_spectrum > limit] = limit
    normalized_frequency_spectrum = frequency_spectrum / frequency_spectrum.max()

    normalized_frequency_spectrum_converted = np.uint8(normalized_frequency_spectrum * 255)
    frequency_spectrum_image = Image.fromarray(normalized_frequency_spectrum_converted)

    return frequency_spectrum_image


def compute_radial_average(image: Image.Image) -> Tuple[npt.NDArray[np.float64], npt.NDArray[np.float64]]:
    # Convert to grayscale and numpy array
    image_np = np.array(image.convert("L"), dtype=np.float32) / 255.0

    width, height = image.size

    # Compute FFT magnitude spectrum
    fft_image = np.fft.fft2(image_np)
    fft_spectrum = np.abs(np.fft.fftshift(fft_image))

    # Compute radial distances
    h, w = fft_spectrum.shape
    cy, cx = h // 2, w // 2
    y, x = np.indices((h, w))

    r = np.sqrt((x - cx) ** 2 + (y - cy) ** 2).astype(int)

    # Compute radial sums
    radial_sum = np.bincount(r.ravel(), weights=fft_spectrum.ravel())
    radial_count = np.bincount(r.ravel())

    # Cut off and normalize
    radial_sum = radial_sum[: min(width // 2, height // 2)]
    radial_count = radial_count[: min(width // 2, height // 2)]

    y_values = radial_sum / np.maximum(radial_count, 1)
    y_values = y_values / min(width // 2, height // 2)

    # Compute true integral: sum weighted by 2πr
    x_values = np.arange(len(y_values)) / len(y_values)

    return x_values, y_values


def get_radial_frequency_distribution(
    image: Image.Image, filename: Optional[str] = None, sample_frequency: float = 0.5,
) -> Tuple[Image.Image, float]:

    radius_values, radial_integral = compute_radial_average(image)

    index = np.argmin(np.abs(radius_values - sample_frequency))
    sample_magnitude = radial_integral[index]

    norm_method = "Density"

    if filename is None:
        title = f"Radial Frequency {norm_method} Plot"
    else:
        title = f"Radial Frequency {norm_method} Plot <br><sup>{filename}</sup>"

    figure = px.line(x=radius_values, y=radial_integral)
    figure.update_layout(margin=dict(l=50, r=50, t=50, b=50), title=dict(text=title))

    figure.update_xaxes(range=[0, 1], title="Frequency")
    figure.update_yaxes(range=[0, 1], title="Density")

    figure.add_trace(
        go.Scatter(
            x=[sample_frequency],
            y=[sample_magnitude],
            mode="markers",
            marker=dict(size=6, color="black", opacity=0.8,),
        )
    )

    figure.add_annotation(
        x=sample_frequency + 0.07,
        y=sample_magnitude + 0.007,
        text=f"({sample_magnitude:.4f}, {sample_frequency})",
        showarrow=False,
    )

    figure.update_layout(showlegend=False)

    image = Image.open(io.BytesIO(figure.to_image(format="png")))

    return image, sample_magnitude


def get_radial_frequency_sample_magnitude(image: Image.Image, sample_frequency: float) -> float:

    radius_values, radial_integral = compute_radial_average(image)

    index = np.argmin(np.abs(radius_values - sample_frequency))
    sample_magnitude = float(radial_integral[index])

    return sample_magnitude


class Results(pydantic.BaseModel):
    s3_url: str
    filename: str
    score: float


def run_sub_analysis(image: Image.Image, filename: str, s3_url: str, produce_visualizations: bool) -> None:

    if produce_visualizations:
        frequency_spectrum = compute_frequency_spectrum(image)
        radial_frequency_distribution, sample_magnitude = get_radial_frequency_distribution(
            image, filename, sample_frequency=0.4
        )

        base_filepath = f"{data_dir}/{filename}"
        image.save(f"{base_filepath}.image.png")

        if True:
            frequency_spectrum.save(f"{base_filepath}.frequency_spectrum.png")
        radial_frequency_distribution.save(f"{base_filepath}.radial_frequency_distribution.png")
    else:
        sample_magnitude = get_radial_frequency_sample_magnitude(image, 0.5)

    results = Results(s3_url=s3_url, filename=filename, score=sample_magnitude)

    with open(f"{data_dir}/results.jsonl", "a+") as f:

        f.write(json.dumps(results.model_dump()) + "\n")

    return None


def run_analysis(s3_url: str, produce_visualizations: bool = True) -> None:

    print(f"Blurry image analyzer - Analyzing image found at {s3_url}")

    client = S3CacheProxyClient(s3_cache_proxy_host=S3_CACHE_PROXY_SERVICE_HOST)
    bucket, key = client.split_uri(s3_url)
    image = client.get_image(bucket, key)
    filename, _ = os.path.splitext(key.split("/")[-1])

    run_sub_analysis(image, filename, s3_url, produce_visualizations)


if __name__ == "__main__":

    parser = argparse.ArgumentParser()

    parser.add_argument("--input-filepath", type=str, required=True)
    parser.add_argument("--num-workers", type=int, default=64)

    args = parser.parse_args()

    data_dir = f"{MAKA_DATA_DIR}/blurry_image_analyzer"
    os.makedirs(data_dir, exist_ok=True)

    print(f"Analyzing image found in {args.input_filepath}")

    with open(args.input_filepath) as f:
        s3_urls = [line.rstrip() for line in f.readlines()]

    with multiprocessing.Pool(args.num_workers) as pool:
        pool.map(run_analysis, s3_urls)

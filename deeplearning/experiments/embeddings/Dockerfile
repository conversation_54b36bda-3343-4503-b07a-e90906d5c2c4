FROM tensorflow/tensorflow:latest-gpu

# Ad-hoc only for panel plotting
RUN pip install hvplot \
    panel \
    umap-learn \
    bokeh \
    umap-learn[parametric_umap] \ 
    boto3 \ 
    alembic \
    torch \
    torchvision

# Fast version of Pillow (after all Python packages)	
RUN pip uninstall -y pillow && \
    apt-get update && apt-get install -y libjpeg-dev libz-dev && \
    pip install -U --force-reinstall --no-cache-dir pillow-simd

# LANG variables
ENV \
    LANG=C.UTF-8 \
    LANGUAGE=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    MAKA_CALIBRATION_DIR=/calibration \
    MAKA_CONFIG_DIR=/config \
    MAKA_DATA_DIR=/data \
    MAKA_LOG_DIR=/logs \
    MAKA_ROBOT_DIR=/robot \
    MAKA_BOT_DIR=/bot \
    MAKA_MODEL_DIR=/models

RUN mkdir /run/sshd && mkdir /root/.ssh

WORKDIR /robot
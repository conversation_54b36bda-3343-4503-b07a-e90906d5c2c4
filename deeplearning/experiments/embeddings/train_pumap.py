import argparse

from sklearn.preprocessing import normalize
from sqlalchemy.sql.expression import func
from umap.parametric_umap import ParametricUMAP

from deeplearning.dl_metrics import Embedding, EmbeddingType, HitClass, Point, get_db, get_session

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str, required=True)

    args = parser.parse_args()

    points = f"/data/deeplearning/models/{args.model_id}/test_unoptimized_dataframes/points_v2.db"

    engine = get_db(points)

    with get_session(engine) as sess:
        embeddings = (
            sess.query(Embedding.embedding)
            .join(Point, Embedding.point_id == Point.id)
            .filter(Point.hit_class != HitClass.PLANT)
            .filter(Point.confidence > 0)
            .filter(Embedding.type == EmbeddingType.FULL)
            .order_by(func.random())
            .all()
        )

    embedding_values = [emb.embedding for emb in embeddings]

    embedder = ParametricUMAP(n_neighbors=25, min_dist=0.1, n_components=2, metric="euclidean", spread=1)
    embedder.fit(normalize(embedding_values))

    embedder.save(f"/data/deeplearning/pumap_{args.model_id}")

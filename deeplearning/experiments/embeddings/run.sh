#!/usr/bin/env bash

set -o errexit   # exit on error
set -o nounset   # disallow unset variables
set -o pipefail  # fail if anything in pipe fails


SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

export MAKA_MODELS_DIR=${MAKA_MODELS_DIR:-}

REPO_ROOT=$(realpath "${SCRIPT_DIR}/../../..")

[[ -d ${MAKA_DATA_DIR} ]] || (echo "${MAKA_DATA_DIR} does not exist" && exit 1)
# backwards compat
if [[ ${MAKA_DATA_DIR} =~ ^/var/maka/data.* ]]; then
    echo "Legacy botstrap install detected"
    # this is an old fashioned botstrap install
    # https://github.com/carbonrobotics/botstrap/blob/test/env.sh#L8
    MAKA_DATA_DIR=${MAKA_DATA_DIR}/data
    [[ -d ${MAKA_DATA_DIR} ]] || (echo "${MAKA_DATA_DIR} does not exist" && exit 1)
fi

if [[ -n "${MAKA_MODELS_DIR}" ]]; then
    MODELS_DIR="--volume ${MAKA_MODELS_DIR}:/models"
fi

if [ -t 1 ]; then
    INTERACTIVE="-it"
fi
RM="--rm"
if [[ -n "${DETACH:-}" ]]; then
    DETACH="-d"
    RM=""
fi

S3_CACHE_HOST=""
if [[ -v S3_CACHE_PROXY_SERVICE_HOST ]]; then
  S3_CACHE_HOST="-e S3_CACHE_PROXY_SERVICE_HOST=${S3_CACHE_PROXY_SERVICE_HOST}"
fi

GPUS="all"
if [[ -n "${DEVICES_STR:-}" ]]; then
    GPUS="${DEVICES_STR}"
fi

set -x

exec docker run \
  --gpus ${GPUS} \
  -e CUDA_VISIBLE_DEVICES \
  --shm-size=32g \
  --ulimit memlock=-1 \
  --ulimit stack=67108864 \
  -e HOSTNAME="$(hostname)" \
  -e AUTH0_TOKEN_CACHE_FILE=${AUTH0_TOKEN_CACHE_FILE:-".auth_token.json"} \
  -e AUTH0_DOMAIN=${AUTH0_DOMAIN} \
  -e AUTH0_CLIENT_ID=${AUTH0_CLIENT_ID} \
  -e AUTH0_CLIENT_SECRET=${AUTH0_CLIENT_SECRET} \
  ${S3_CACHE_HOST} \
  --privileged \
  ${RM} \
  --volume /dev/shm:/dev/shm \
  --volume ~/.aws:/root/.aws \
  --volume ${REPO_ROOT}:/robot \
  --volume ${MAKA_DATA_DIR}:/data \
  --network=${DOCKER_NETWORK:-host} \
  --name=dl-${USER}-embeddings \
  ${MODELS_DIR:-} \
  ${INTERACTIVE:-} \
  ${CONTAINER:-} \
  ${DETACH:-} \
  embeddings-experimentation-container "$@"

# White-list some of the ports for production:
# -p 2222:2222 \
# -p 6006:6006 \
# -p 8888:8888 \
# -p 8866:8866 \
# -p 8501:8501 \
# -p 15051:15051 \

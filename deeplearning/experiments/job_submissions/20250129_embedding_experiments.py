from typing import Any, Dict, List, Optional

import requests


def get_job(
    pretrained_model_id: str,
    segmentation_model_id: str,
    dataset_id: str,
    additional_configs: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    additional_configs = additional_configs if additional_configs is not None else {}
    return {
        "command": f"python -m deeplearning.scripts.standard_jobs.train_spawn --nproc-per-node 8 --no-autodetect-driptape-model --no-autodetect-pretrained-model --pretrained-model {pretrained_model_id} --segmentation-model {segmentation_model_id} --dataset-id {dataset_id}",
        "failure_command": "python -m deeplearning.scripts.standard_jobs.upload",
        "container_tag": "8b34464b0c4ffbc7aa509898c84e22211bd799ed",
        "node_group": "G8g256m64c",
        "gpu_type": "NVIDIA-RTX-A5000",
        "job_type_id": "87f60703-c4d2-4a52-b072-b04a658c36a5",
        "trigger_id": "",
        "prefix": "fut",
        "priority": 70,
        "queue_limit": 1,
        "requested_start": 0,
        "retries": 0,
        "max_runtime_seconds": 432000,
        "nfs_mount": None,
        **additional_configs,
    }


def main() -> None:
    parameters: List[Dict[str, str]] = [
        {  # Carrot
            "pipeline": "bacbdf06-3c28-4c4e-9eab-4d3aafa6173d",
            "dataset": "f2d1b7a5-a775-4a1b-800e-46317de5a7e1",
            "pretrained_model": "prt-20250120-dbx2fejnu0",
            "segmentation_model": "drp-20250120-jpprp3gqww",
            "pipeline_name": "Carrot",
        },
        {  # LDGL
            "pipeline": "b0228657e-160b-4809-bebd-d0a02d61cbd4",
            "dataset": "538a3e8d-d77e-485f-a1cd-67e54b0309e7",
            "pretrained_model": "prt-20250120-dbx2fejnu0",
            "segmentation_model": "drp-20250120-jpprp3gqww",
            "pipeline_name": "Low Density Green Lettuce",
        },
        {  # Onions
            "pipeline": "1e31f2ca-34f8-4f8f-9199-5e3d5bdd81d4",
            "dataset": "7c01d7a7-4bc0-43b5-bb7e-47880ce17acc",
            "pretrained_model": "prt-20250120-dbx2fejnu0",
            "segmentation_model": "drp-20250120-jpprp3gqww",
            "pipeline_name": "Onions",
        },
        {  # Beets
            "pipeline": "8270a906-fe12-41cd-bbed-c15790c38f94",
            "dataset": "fb4693a4-f33a-4614-9527-c718bc4aba12",
            "pretrained_model": "prt-20250117-96w1yhtml4",
            "segmentation_model": "drp-20250114-z594o2wzr8",
            "pipeline_name": "Beets",
        },
        {  # Chard
            "pipeline": "ac388aa8-e72b-41c4-8553-9011b010f959",
            "dataset": "6cacb8a4-6d53-4556-8e11-0f0566d6a427",
            "pretrained_model": "prt-20250120-dbx2fejnu0",
            "segmentation_model": "drp-20250114-v5d0z1ndkc",
            "pipeline_name": "Chard",
        },
    ]

    configs: List[Dict[str, Any]] = [
        {
            "average_embeddings_across_hits": False,
            "use_comparison_similarity_loss": False,
            "embedding_loss_weight": 1e-3,
        },
        {
            "average_embeddings_across_hits": True,
            "use_comparison_similarity_loss": False,
            "embedding_loss_weight": 1e-3,
        },
        {"average_embeddings_across_hits": False, "use_comparison_similarity_loss": True, "embedding_loss_weight": 3},
        {"average_embeddings_across_hits": False, "use_comparison_similarity_loss": True, "embedding_loss_weight": 3},
        {"average_embeddings_across_hits": False, "use_comparison_similarity_loss": True, "embedding_loss_weight": 10},
        {"average_embeddings_across_hits": False, "use_comparison_similarity_loss": True, "embedding_loss_weight": 10},
    ]

    for parameter in parameters:
        for config in configs:

            data = get_job(
                pretrained_model_id=parameter["pretrained_model"],
                segmentation_model_id=parameter["segmentation_model"],
                dataset_id=parameter["dataset"],
            )

            data[
                "description"
            ] = f"Raven experiments - 1/29/25: {parameter['pipeline_name']}, average={config['average_embeddings_across_hits']}, comparison_loss={config['use_comparison_similarity_loss']}, embedding_loss_weight={config['embedding_loss_weight']}"
            data["dl_config"] = config
            data[
                "queue_key"
            ] = f"{parameter['pipeline']}-{config['average_embeddings_across_hits']}-{config['use_comparison_similarity_loss']}-{config['embedding_loss_weight']}"
            data["pipeline_id"] = parameter["pipeline"]

            response = requests.post("http://deeplearning28.dc.carbonrobotics.com/jobs", json=data)

            print(response.status_code, response.text)


if __name__ == "__main__":
    main()

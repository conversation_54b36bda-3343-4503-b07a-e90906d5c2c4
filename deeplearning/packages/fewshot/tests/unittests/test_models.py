import unittest

import torch

import fewshot


# Test class for the ProtoNets model
class TestModels(unittest.TestCase):
    def test_forward(self):
        encoder = fewshot.encoders.SimpleCNN(classify=False)
        model = fewshot.models.ProtoNets(encoder)

        episode = self._construct_random_episode(num_classes=3, num_examples=3, num_queries=2)
        logits = model(episode)
        self.assertEqual(logits.shape, torch.Size([6, 3]))
        self.assertFalse(torch.isnan(logits).any())

    def test_forward_single_datapoint(self):
        encoder = fewshot.encoders.SimpleCNN(classify=False)
        model = fewshot.models.ProtoNets(encoder)

        episode = self._construct_random_episode(num_classes=3, num_examples=1, num_queries=2)
        logits = model(episode)

        self.assertEqual(logits.shape, torch.Size([6, 3]))
        self.assertFalse(torch.isnan(logits).any())

    @staticmethod
    def _construct_random_episode(num_classes: int = 3, num_examples: int = 3, num_queries: int = 3):
        datapoints = []
        for class_index in range(num_classes):
            for example_index in range(num_examples):
                datapoint = fewshot.dataset_types.Datapoint(
                    tensor=torch.randn(3, 224, 224),
                    identifier=f"class-{class_index}-{example_index}",
                    label=f"class-{class_index}",
                    role=fewshot.dataset_types.DatapointRole.SUPPORT,
                )
                datapoints.append(datapoint)

            for query_index in range(num_queries):
                datapoint = fewshot.dataset_types.Datapoint(
                    tensor=torch.randn(3, 224, 224),
                    identifier=f"class-{class_index}-{query_index}",
                    label=f"class-{class_index}",
                    role=fewshot.dataset_types.DatapointRole.QUERY,
                )
                datapoints.append(datapoint)

        episode = fewshot.dataset_types.Episode(datapoints)

        return episode


class TestKNN(unittest.TestCase):
    def test_forward(self):
        encoder = fewshot.encoders.SimpleCNN(classify=False)
        model = fewshot.models.KNNClassifier(encoder)

        episode = self._construct_random_episode(num_classes=3, num_examples=3, num_queries=2)
        logits = model(episode)
        self.assertEqual(logits.shape, torch.Size([6, 3]))
        self.assertFalse(torch.isnan(logits).any())

    def test_forward_single_datapoint(self):
        encoder = fewshot.encoders.SimpleCNN(classify=False)
        model = fewshot.models.KNNClassifier(encoder)

        episode = self._construct_random_episode(num_classes=3, num_examples=1, num_queries=2)
        logits = model(episode)

        self.assertEqual(logits.shape, torch.Size([6, 3]))
        self.assertFalse(torch.isnan(logits).any())

    @staticmethod
    def _construct_random_episode(num_classes: int = 3, num_examples: int = 3, num_queries: int = 3):
        datapoints = []
        for class_index in range(num_classes):
            for example_index in range(num_examples):
                datapoint = fewshot.dataset_types.Datapoint(
                    tensor=torch.randn(3, 224, 224),
                    identifier=f"class-{class_index}-{example_index}",
                    label=f"class-{class_index}",
                    role=fewshot.dataset_types.DatapointRole.SUPPORT,
                )
                datapoints.append(datapoint)

            for query_index in range(num_queries):
                datapoint = fewshot.dataset_types.Datapoint(
                    tensor=torch.randn(3, 224, 224),
                    identifier=f"class-{class_index}-{query_index}",
                    label=f"class-{class_index}",
                    role=fewshot.dataset_types.DatapointRole.QUERY,
                )
                datapoints.append(datapoint)

        episode = fewshot.dataset_types.Episode(datapoints)

        return episode

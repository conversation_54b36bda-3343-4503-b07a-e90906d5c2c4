import unittest

import torch

from fewshot.encoders import ResNet


class TestResNet(unittest.TestCase):
    def setUp(self):
        # Prepare a random input tensor (e.g., a batch of 2 images with 3 channels, 224x224)
        self.input_tensor = torch.randn(2, 3, 224, 224)

    def test_resnet18_forward(self):
        model = ResNet(architecture="resnet18", pretrained=False)
        output = model(self.input_tensor)
        self.assertEqual(output.shape, torch.Size([2, model.embedding_size]))

    def test_resnet50_forward(self):
        model = ResNet(architecture="resnet50", pretrained=False)
        output = model(self.input_tensor)
        self.assertEqual(output.shape, torch.Size([2, model.embedding_size]))

    def test_resnet50_dropout(self):
        model = ResNet(architecture="resnet50", pretrained=False, dropout_p_1b1=0.5, dropout_p_3b3=0.5)
        output = model(self.input_tensor)
        self.assertEqual(output.shape, torch.Size([2, model.embedding_size]))

    def test_resnet18_dropout(self):
        model = ResNet(architecture="resnet18", pretrained=False, dropout_p_1b1=0.5, dropout_p_3b3=0.5)
        output = model(self.input_tensor)
        self.assertEqual(output.shape, torch.Size([2, model.embedding_size]))

    def test_resnet18_forward_no_final_relu(self):
        model = ResNet(architecture="resnet18", pretrained=False, final_relu=False)
        output = model(self.input_tensor)
        self.assertEqual(output.shape, torch.Size([2, model.embedding_size]))

    def test_resnet50_forward_no_final_relu(self):
        model = ResNet(architecture="resnet50", pretrained=False, final_relu=False)
        output = model(self.input_tensor)
        self.assertEqual(output.shape, torch.Size([2, model.embedding_size]))


if __name__ == "__main__":
    unittest.main()

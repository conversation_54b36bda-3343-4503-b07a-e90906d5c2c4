import unittest

import numpy as np

import fewshot
from fewshot.datasets import Dataset


class TestLockableSampler(unittest.TestCase):
    def test_base(self):
        num_classes = 5
        num_dataset_classes = 10
        num_support_samples = 5
        num_query_samples = 5
        num_episodes = 5

        dataset = self._construct_random_dataset(num_classes=num_dataset_classes, num_examples=300)
        sampler = fewshot.samplers.LockableSampler(
            dataset, num_classes, num_support_samples, num_query_samples, num_episodes=num_episodes
        )

        for query_set, support_set in sampler:
            self.assertEqual(len(query_set), num_query_samples * num_classes)
            self.assertEqual(len(support_set), num_support_samples * num_classes)
            self.assertTrue(len(set([a[0] for a in query_set]).intersection(set([a[0] for a in support_set]))) == 0)

    def test_lock(self):
        num_classes = 5
        num_dataset_classes = 10
        num_support_samples = 5
        num_query_samples = 5
        num_episodes = 5

        dataset = self._construct_random_dataset(num_classes=num_dataset_classes, num_examples=300)
        sampler = fewshot.samplers.LockableSampler(
            dataset, num_classes, num_support_samples, num_query_samples, num_episodes=num_episodes
        )

        sampler.lock_classes()
        last_query, last_support = None, None
        for query_set, support_set in sampler:
            self.assertEqual(len(query_set), num_query_samples * num_classes)
            self.assertEqual(len(support_set), num_support_samples * num_classes)
            self.assertTrue(len(set([a[0] for a in query_set]).intersection(set([a[0] for a in support_set]))) == 0)

            if last_query is not None:
                self.assertEqual(last_query, query_set)
            else:
                sampler.lock_query(query_set)

            if last_support is not None:
                self.assertEqual(last_support, support_set)
            else:
                sampler.lock_support(support_set)

            last_query = query_set
            last_support = support_set

    def test_unlock(self):
        num_classes = 5
        num_dataset_classes = 10
        num_support_samples = 5
        num_query_samples = 5
        num_episodes = 5

        dataset = self._construct_random_dataset(num_classes=num_dataset_classes, num_examples=300)
        sampler = fewshot.samplers.LockableSampler(
            dataset, num_classes, num_support_samples, num_query_samples, num_episodes=num_episodes
        )

        sampler.lock_classes()
        last_query, last_support = None, None
        for query_set, support_set in sampler:
            self.assertEqual(len(query_set), num_query_samples * num_classes)
            self.assertEqual(len(support_set), num_support_samples * num_classes)
            self.assertTrue(len(set([a[0] for a in query_set]).intersection(set([a[0] for a in support_set]))) == 0)

            if last_query is not None:
                sampler.unlock_query()
                continue
            else:
                sampler.lock_query(query_set)
                if last_query is not None:
                    self.assertNotEqual(last_query, query_set)

            self.assertNotEqual(last_support, support_set)

        for query_set, support_set in sampler:
            self.assertEqual(len(query_set), num_query_samples * num_classes)
            self.assertEqual(len(support_set), num_support_samples * num_classes)
            self.assertTrue(len(set([a[0] for a in query_set]).intersection(set([a[0] for a in support_set]))) == 0)

            if last_support is not None:
                sampler.unlock_support()
                continue
            else:
                sampler.lock_support(support_set)
                if last_support is not None:
                    self.assertNotEqual(last_support, support_set)

    @staticmethod
    def _construct_random_dataset(num_classes: int = 5, num_examples: int = 20):
        np.random.seed(0)
        d = Dataset(
            data=np.zeros((num_examples, num_classes)),  # type: ignore
            labels=np.random.randint(0, num_classes, num_examples),  # type: ignore
        )

        return d

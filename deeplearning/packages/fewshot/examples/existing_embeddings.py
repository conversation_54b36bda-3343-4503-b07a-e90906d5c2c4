import torch

import fewshot

EMBEDDING_DIMENSION = 128


def load_fn(identifier: str) -> torch.Tensor:

    return torch.randn(1, EMBEDDING_DIMENSION)


def transform_fn(x: torch.Tensor) -> torch.Tensor:
    return x


def get_model() -> fewshot.models.BaseModel:

    encoder = fewshot.encoders.EmbeddingsReader(
        embedding_size=EMBEDDING_DIMENSION,
    )

    model = fewshot.models.ProtoNets(encoder)

    return model


def get_dataset() -> fewshot.Dataset:

    data = ["some/file/path/or/identifier"] * 50 * 5
    labels = ["a", "b", "c", "d", "e"] * 50

    dataset = fewshot.Dataset(data=data, labels=labels, load_fn=load_fn, transform_fn=transform_fn)

    return dataset


if __name__ == "__main__":

    model = get_model()

    dataset = get_dataset()

    fewshot.evaluate(
        model,
        dataset,
        model_id="example-model-id",
        data_dir="data",
        logging_prefix="data-block-1",
        distributed_data_parallel=False,
    )

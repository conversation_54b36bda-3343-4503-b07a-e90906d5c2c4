name: Lint

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

env:
  PROJECT_DESCRIPTION: A Python package for few-shot learning

jobs:
  test:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Upgrade pip
        run: python -m pip install --upgrade pip
      
      - name: Install dependencies
        run: pip install -r requirements.txt && pip install -r requirements.lint.txt

      - name: Install package in development mode
        run: pip install -e .

      - name: Run lint
        run: make lint-ci
      
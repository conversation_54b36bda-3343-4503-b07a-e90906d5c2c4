import torch

from .base import BaseEncoder


class EmbeddingsReader(BaseEncoder):
    def __init__(self, embedding_size: int):
        super().__init__()
        self._embedding_size = embedding_size

    @property
    def embedding_size(self) -> int:
        return self._embedding_size

    def forward(self, x: torch.Tensor) -> torch.Tensor:

        x = x.view(-1, self._embedding_size)

        return x

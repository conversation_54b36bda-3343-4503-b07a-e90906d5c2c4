from typing import Any, Callable, Optional

import torch
import torchvision  # type: ignore
from torch import nn


class BasicBlockDropout(torchvision.models.resnet.BasicBlock):
    expansion: int = 1

    def __init__(
        self,
        inplanes: int,
        planes: int,
        stride: int = 1,
        downsample: Optional[nn.<PERSON>dule] = None,
        groups: int = 1,
        base_width: int = 64,
        dilation: int = 1,
        norm_layer: Optional[Callable[..., nn.Module]] = None,
        prob_1b1: float = 0.0,
        prob_3b3: float = 0.0,
    ) -> None:
        super().__init__(inplanes, planes, stride, downsample, groups, base_width, dilation, norm_layer)

        self.dropout1b1 = nn.Dropout(prob_1b1)
        self.dropout3b3 = nn.Dropout(prob_3b3)

    def set_dropout(self, prob_1b1: float = 0.0, prob_3b3: float = 0.0):
        self.dropout1b1.p = prob_1b1
        self.dropout3b3.p = prob_3b3

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        identity = x

        out = self.conv1(x)
        out = self.dropout3b3(out)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.dropout3b3(out)
        out = self.bn2(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class BottleneckDropout(torchvision.models.resnet.Bottleneck):
    expansion: int = 4

    def __init__(
        self,
        inplanes: int,
        planes: int,
        stride: int = 1,
        downsample: Optional[nn.Module] = None,
        groups: int = 1,
        base_width: int = 64,
        dilation: int = 1,
        norm_layer: Optional[Callable[..., nn.Module]] = None,
        prob_1b1: float = 0.0,
        prob_3b3: float = 0.0,
    ) -> None:
        super().__init__(inplanes, planes, stride, downsample, groups, base_width, dilation, norm_layer)

        self.dropout1b1 = nn.Dropout(prob_1b1)
        self.dropout3b3 = nn.Dropout(prob_3b3)

    def set_dropout(self, prob_1b1: float = 0.0, prob_3b3: float = 0.0):
        self.dropout1b1.p = prob_1b1
        self.dropout3b3.p = prob_3b3

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        identity = x

        out = self.conv1(x)
        out = self.dropout1b1(out)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.dropout3b3(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.dropout1b1(out)
        out = self.bn3(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


@torchvision.models._api.register_model()
@torchvision.models._utils.handle_legacy_interface(
    weights=("pretrained", torchvision.models.resnet.ResNet18_Weights.IMAGENET1K_V1)
)
def resnet18_dropout(
    *, weights: Optional[torchvision.models.resnet.ResNet18_Weights] = None, progress: bool = True, **kwargs: Any
) -> torchvision.models.resnet.ResNet:
    weights = torchvision.models.resnet.ResNet18_Weights.verify(weights)

    return torchvision.models.resnet._resnet(BasicBlockDropout, [2, 2, 2, 2], weights, progress, **kwargs)


@torchvision.models._api.register_model()
@torchvision.models._utils.handle_legacy_interface(
    weights=("pretrained", torchvision.models.resnet.ResNet50_Weights.IMAGENET1K_V1)
)
def resnet50_dropout(
    *, weights: Optional[torchvision.models.resnet.ResNet50_Weights] = None, progress: bool = True, **kwargs: Any
) -> torchvision.models.resnet.ResNet:
    weights = torchvision.models.resnet.ResNet50_Weights.verify(weights)

    return torchvision.models.resnet._resnet(BottleneckDropout, [3, 4, 6, 3], weights, progress, **kwargs)


@torchvision.models._api.register_model()
@torchvision.models._utils.handle_legacy_interface(
    weights=("pretrained", torchvision.models.resnet.ResNet152_Weights.IMAGENET1K_V1)
)
def resnet152_dropout(
    *, weights: Optional[torchvision.models.resnet.ResNet152_Weights] = None, progress: bool = True, **kwargs: Any
) -> torchvision.models.resnet.ResNet:
    weights = torchvision.models.resnet.ResNet152_Weights.verify(weights)

    return torchvision.models.resnet._resnet(BottleneckDropout, [3, 8, 36, 3], weights, progress, **kwargs)

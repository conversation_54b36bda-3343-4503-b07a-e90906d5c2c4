from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import torch

from .dataset_types import Datapoint, DatapointRole
from .utilities import default_evaluation_transform_fn, default_load_fn


class Dataset(torch.utils.data.Dataset):
    def __init__(
        self,
        data: List[str],
        labels: List[str],
        load_fn: Callable[[Any], Any] = default_load_fn,
        transform_fn: Callable[[Any], torch.Tensor] = default_evaluation_transform_fn,
        metadata: Optional[List[Dict[str, Any]]] = None,
    ) -> None:
        self._data = data
        self._labels = labels
        self._load_fn = load_fn
        self._transform_fn = transform_fn
        self._metadata = metadata

        self._class_names = list(set(self._labels))
        self._name2indices = {
            name: [i for i, label in enumerate(self._labels) if label == name] for name in self._class_names
        }

    @property
    def class_names(self):
        return self._class_names

    @property
    def name2indices(self):
        return self._name2indices

    def __getitem__(self, item: Union[int, Tuple[int, DatapointRole]]) -> Datapoint:

        index: int
        role: DatapointRole

        if isinstance(item, int):
            index = item
            role = DatapointRole.QUERY
        else:
            index, role = item

        identifier = self._data[index]
        label = self._labels[index]
        metadata = None
        if self._metadata is not None:
            metadata = self._metadata[index]

        pil_image = self._load_fn(identifier)
        tensor_image = self._transform_fn(pil_image)

        datapoint = Datapoint(tensor_image, identifier, label, role, metadata=metadata)

        return datapoint

    def __len__(self):
        return len(self._data)

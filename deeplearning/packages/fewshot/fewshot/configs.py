from typing import Optional

import pydantic


class TrainingConfig(pydantic.BaseModel):
    model_id: str
    data_dir: str
    wandb_project: str
    num_epochs: int = 1000
    num_training_episodes: int = 250
    num_validation_episodes: int = 250
    learning_rate: float = 1e-5
    num_classes: int = 5
    num_support_samples: int = 5
    num_query_samples: int = 8
    num_validation_classes: Optional[int] = 5
    num_validation_support_samples: Optional[int] = None
    num_validation_query_samples: Optional[int] = None
    num_workers: int = 4
    prefetch_factor: int = 2
    num_gpus: int = 8
    parent_data_dir: Optional[str] = None
    parent_loss_weight: float = 0.5
    embed_parent_model: bool = False
    weight_decay: float = 0
    backbone: str = "resnet50"
    verbose: bool = False
    distill_loss: str = "kldiv"  # kldiv or crossentropy
    description: Optional[str] = None


class EvaluationConfig(pydantic.BaseModel):
    model_id: str
    data_dir: str
    wandb_project: Optional[str] = None
    num_episodes: int = 250
    num_classes: int = 5
    num_support_samples: int = 5
    num_query_samples: int = 8
    num_workers: int = 4
    prefetch_factor: int = 2
    logging_prefix: str = "test"
    distributed_data_parallel: bool = True
    num_gpus: int = 8


class EmbeddingConfig(pydantic.BaseModel):
    data_dir: str
    logging_prefix: str = "test"
    num_gpus: int = 8

import json
import os
import random
from typing import Any, Dict, List, Optional

import torch
import torchvision  # type: ignore
import wandb
from PIL import Image
from torch.distributed.launcher.api import LaunchConfig

from .configs import EvaluationConfig
from .dataset_types import Batch, Datapoint, DatapointRole, Episode

IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]


def default_load_fn(filepath: str) -> Image.Image:
    with Image.open(filepath) as image:
        image = image.convert("RGB")

    return image


def default_training_transform_fn(image: Image.Image) -> torch.Tensor:
    if random.random() > 0.5:
        image = torchvision.transforms.functional.hflip(image)

    if random.random() > 0.5:
        image = torchvision.transforms.functional.vflip(image)

    if random.random() > 0.5:
        image = torchvision.transforms.functional.gaussian_blur(image, kernel_size=5, sigma=0.5)

    image = torchvision.transforms.RandomRotation(degrees=(0, 360))(image)

    tensor_image = torchvision.transforms.functional.to_tensor(image)
    tensor_image = torchvision.transforms.functional.resize(tensor_image, (224, 224), antialias=None)
    tensor_image = torchvision.transforms.functional.normalize(tensor_image, IMAGENET_MEAN, IMAGENET_STD)

    return tensor_image


def default_evaluation_transform_fn(image: Image.Image) -> torch.Tensor:
    tensor_image = torchvision.transforms.functional.to_tensor(image)
    tensor_image = torchvision.transforms.functional.resize(tensor_image, (224, 224), antialias=None)
    tensor_image = torchvision.transforms.functional.normalize(tensor_image, IMAGENET_MEAN, IMAGENET_STD)

    return tensor_image


def episode_collate_fn(datapoints: List[Datapoint]) -> Episode:
    return Episode(datapoints)


def batch_collate_fn(datapoints: List[Datapoint]) -> Batch:
    return Batch(datapoints)


def get_elastic_launcher_config(num_gpus: int = 8) -> LaunchConfig:
    launch_config = LaunchConfig(
        min_nodes=1,
        max_nodes=1,
        nproc_per_node=num_gpus,
        run_id="none",
        role="default",
        rdzv_endpoint="127.0.0.1:29500",
        rdzv_backend="static",
        rdzv_configs={"rank": 0, "timeout": 900},
        rdzv_timeout=-1,
        max_restarts=0,
        monitor_interval=5,
        start_method="spawn",
        metrics_cfg={},
        local_addr=None,
    )
    return launch_config


def get_support_query_sample_image(episode: Episode) -> wandb.Image:
    support_sample = list(episode.support_set.values())[0][0]
    query_sample = episode.query_set[support_sample.label][0]
    sample = torch.cat((support_sample.tensor, query_sample.tensor), dim=2)
    image = wandb.Image(sample, caption=f"Class name: {support_sample.label}")
    return image


def per_episode_logging(
    episode: Episode,
    logits: torch.Tensor,
    config: EvaluationConfig,
    rank: int,
    episode_index: int,
    closest_image_ids: Optional[torch.Tensor] = None,
) -> None:
    confidences = torch.softmax(logits, dim=1)
    predictions = logits.argmax(dim=1)
    query_set = [datapoint for datapoint in episode.datapoints if datapoint.role == DatapointRole.QUERY]
    support_set = [datapoint for datapoint in episode.datapoints if datapoint.role == DatapointRole.SUPPORT]

    support_set_data: List[Dict[str, Any]] = []
    for datapoint_index, datapoint in enumerate(support_set):
        support_set_data.append(
            {
                "identifier": datapoint.identifier,
                "label": datapoint.label,
                "role": datapoint.role.value,
            }
        )

    query_set_data: List[Dict[str, Any]] = []
    for datapoint_index, datapoint in enumerate(query_set):
        prediction_index = predictions[datapoint_index].item()
        qdata = {
            "identifier": datapoint.identifier,
            "label": datapoint.label,
            "prediction": (
                "Other"
                if prediction_index >= len(episode.class_names) or confidences[datapoint_index].sum() <= 0
                else (episode.class_names[prediction_index])
            ),
            "class_names": episode.class_names,
            "logits": logits[datapoint_index].tolist(),
            "confidence": confidences[datapoint_index].max().item(),
            "role": datapoint.role.value,
        }
        if closest_image_ids is not None:
            qdata["closest_image_ids"] = [int(id) for id in closest_image_ids[:, datapoint_index].tolist()]
        query_set_data.append(qdata)

    # All rank logging to file
    os.makedirs(config.data_dir, exist_ok=True)
    with open(f"{config.data_dir}/{config.logging_prefix}-results.jsonl", "a") as f:
        f.write(
            json.dumps(
                {
                    "rank": rank,
                    "episode_index": episode_index,
                    "support_set": support_set_data,
                    "query_set": query_set_data,
                }
            )
            + "\n"
        )

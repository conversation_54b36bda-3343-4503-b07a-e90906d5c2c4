import enum
from typing import Any, Dict, List, Optional

import torch


class DatapointRole(enum.Enum):
    SUPPORT = "SUPPORT"
    QUERY = "QUERY"


class Datapoint:
    def __init__(
        self,
        tensor: torch.Tensor,
        identifier: str,
        label: str,
        role: DatapointRole,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self._tensor = tensor
        self._identifier = identifier
        self._label = label
        self._role = role
        self._metadata = metadata

    @property
    def tensor(self):
        return self._tensor

    @property
    def identifier(self):
        return self._identifier

    @property
    def label(self):
        return self._label

    @property
    def role(self):
        return self._role

    @property
    def metadata(self) -> Optional[Dict[str, Any]]:
        return self._metadata


class Batch:
    def __init__(self, datapoints: List[Datapoint]):
        self._datapoints = datapoints
        self._x = torch.stack([datapoint.tensor for datapoint in datapoints])

    @property
    def datapoints(self):
        return self._datapoints

    @property
    def x(self):
        return self._x

    @property
    def __len__(self):
        return len(self._datapoints)

    def to(self, gpu_id: int):
        self._x = self._x.to(gpu_id)
        return self


class Episode:
    def __init__(self, datapoints: List[Datapoint]):
        self._datapoints = datapoints
        self._class_names = list(set([datapoint.label for datapoint in datapoints]))
        self._roles = [datapoint.role for datapoint in datapoints]

        self._x = torch.stack([datapoint.tensor for datapoint in datapoints])
        self._y = torch.tensor(
            [
                self._class_names.index(datapoint.label)
                for datapoint in self._datapoints
                if datapoint.role == DatapointRole.QUERY
            ]
        )

    @property
    def datapoints(self):
        return self._datapoints

    @property
    def class_names(self):
        return self._class_names

    @property
    def roles(self):
        return self._roles

    @property
    def x(self):
        return self._x

    @property
    def y(self):
        return self._y

    @property
    def support_set(self) -> Dict[str, List[Datapoint]]:
        support_set: Dict[str, List[Datapoint]] = {}
        for datapoint in self._datapoints:
            if datapoint.role == DatapointRole.SUPPORT:
                if datapoint.label not in support_set.keys():
                    support_set[datapoint.label] = []
                support_set[datapoint.label].append(datapoint)
        return support_set

    @property
    def query_set(self) -> Dict[str, List[Datapoint]]:
        query_set: Dict[str, List[Datapoint]] = {}
        for datapoint in self._datapoints:
            if datapoint.role == DatapointRole.QUERY:
                if datapoint.label not in query_set.keys():
                    query_set[datapoint.label] = []
                query_set[datapoint.label].append(datapoint)
        return query_set

    def to(self, gpu_id: int):
        self._x = self._x.to(gpu_id)
        self._y = self._y.to(gpu_id)
        return self

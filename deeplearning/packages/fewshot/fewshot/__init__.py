import logging
import warnings

from . import configs, encoders, models, samplers, utilities
from .dataset_types import Datapoint, DatapointRole, Episode
from .datasets import Dataset
from .embed import embed
from .evaluate import evaluate
from .samplers import Sampler
from .train import train

logging.basicConfig()
logging.getLogger("fewshot").setLevel(logging.INFO)
warnings.filterwarnings(action="ignore", category=UserWarning, module=r"numpy.*", append=True)

__all__ = [
    "Dataset",
    "Episode",
    "encoders",
    "models",
    "train",
    "evaluate",
    "embed",
    "utilities",
    "configs",
    "samplers",
    "DatapointRole",
    "Datapoint",
]

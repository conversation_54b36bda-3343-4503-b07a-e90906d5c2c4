import logging
import os
from typing import Dict, Type, Union

import torch
import wandb
from torch.distributed.launcher.api import elastic_launch
from torch.nn.parallel import DistributedDataParallel

from . import utilities
from .configs import EvaluationConfig
from .datasets import Dataset
from .models import BaseModel
from .samplers import Sampler


def evaluate(model: BaseModel, dataset: Dataset, sampler: Type[Sampler] = Sampler, *args, **kwargs) -> None:
    LOG = logging.getLogger("fewshot")
    LOG.info("Starting evaluation...")

    evaluation_config = EvaluationConfig(*args, **kwargs)

    launch_config = utilities.get_elastic_launcher_config(num_gpus=evaluation_config.num_gpus)
    launcher = elastic_launch(launch_config, evaluate_subprocess)
    launcher(model, dataset, evaluation_config, sampler)


def evaluate_subprocess(
    model: BaseModel,
    dataset: Dataset,
    config: EvaluationConfig,
    sampler: Type[Sampler],
):
    if not torch.distributed.is_initialized():
        torch.distributed.init_process_group(backend="nccl")

    rank = int(os.environ["LOCAL_RANK"])

    if rank == 0:
        LOG = logging.getLogger("fewshot")

        if config.wandb_project is not None:
            run = wandb.init(
                project=config.wandb_project,
                id=config.model_id,
                dir=config.data_dir,
                config=config.model_dump(),
                resume="must",
            )

    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_sampler=sampler(
            dataset,
            num_classes=config.num_classes,
            num_support_samples=config.num_support_samples,
            num_query_samples=config.num_query_samples,
            num_episodes=config.num_episodes,
            seed=rank,
        ),
        collate_fn=utilities.episode_collate_fn,
        num_workers=config.num_workers,
        prefetch_factor=config.prefetch_factor,
    )

    model.to(rank)

    distributed_model: Union[DistributedDataParallel, BaseModel]
    if config.distributed_data_parallel:
        distributed_model = DistributedDataParallel(model, device_ids=[rank], find_unused_parameters=True)
    else:
        distributed_model = model

    loss_fn = torch.nn.CrossEntropyLoss()

    distributed_model.eval()
    losses = []
    accuracies = []
    image_samples: Dict[str, wandb.Image] = {}
    for episode_index, episode in enumerate(dataloader):
        episode = episode.to(rank)

        with torch.no_grad():
            logits = distributed_model(episode)
            loss = loss_fn(logits, episode.y)
            accuracy = (logits.argmax(dim=1) == episode.y).float().mean()

            torch.distributed.all_reduce(loss, op=torch.distributed.ReduceOp.AVG)
            torch.distributed.all_reduce(accuracy, op=torch.distributed.ReduceOp.AVG)

        utilities.per_episode_logging(
            episode=episode, logits=logits, config=config, rank=rank, episode_index=episode_index
        )

        losses.append(loss.item())
        accuracies.append(accuracy.item())

        if rank == 0:
            message = f"Rank {rank}: Evaluation Episode {episode_index + 1: >3} / {config.num_episodes: >3} | "
            message += f"Loss: {loss.item():2.2f} | Accuracy: {accuracy.item():2.2f}"
            LOG.info(message)

            if episode_index % 1000 == 0 and config.wandb_project is not None:
                image = utilities.get_support_query_sample_image(episode)
                image_samples[f"{config.logging_prefix}-image-sample"] = image

    loss = sum(losses) / len(losses)
    accuracy = sum(accuracies) / len(accuracies)

    if rank == 0:
        LOG.info(f"Evaluation sweep average loss: {loss:2.2f}")
        LOG.info(f"Evaluation sweep average accuracy: {accuracy:2.2f}")

        if config.wandb_project is not None:
            run.log(
                {f"{config.logging_prefix}-loss": loss, f"{config.logging_prefix}-accuracy": accuracy, **image_samples},
            )
            run.finish()

        LOG.info("Evaluation complete.")

    return loss, accuracy

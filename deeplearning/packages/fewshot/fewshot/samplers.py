from typing import Iterator, List, <PERSON><PERSON>

import numpy as np
import torch

from .dataset_types import Datapoint<PERSON><PERSON>
from .datasets import Dataset


class Sampler(torch.utils.data.Sampler):
    def __init__(
        self,
        dataset: Dataset,
        num_classes: int,
        num_support_samples: int,
        num_query_samples: int,
        num_episodes: int,
        seed=1000,
    ):
        self._dataset = dataset
        self._num_classes = num_classes
        assert self._num_classes <= len(self._dataset.class_names)

        self._num_support_samples = num_support_samples
        self._num_query_samples = num_query_samples
        self._num_episodes = num_episodes

        self._rng = np.random.RandomState(seed)

    def __iter__(self) -> Iterator[List[Tuple[int, DatapointRole]]]:
        for _ in range(self._num_episodes):
            class_names = self._rng.choice(self._dataset.class_names, self._num_classes, replace=False)

            support_roles = [DatapointRole.SUPPORT] * self._num_support_samples
            query_roles = [DatapointRole.QUERY] * self._num_query_samples

            samples_list = []
            roles_list = []
            for class_name in class_names:
                samples = []
                indices = self._dataset.name2indices[class_name]
                num_samples = self._num_support_samples + self._num_query_samples
                for sample in self._rng.choice(indices, min(len(indices), num_samples), replace=False):
                    samples.append(int(sample))

                samples_list.extend(samples)
                roles_list.extend(support_roles + query_roles)

            episode = [(a, b) for a, b in zip(samples_list, roles_list)]

            yield episode


class LockableSampler(torch.utils.data.Sampler):
    def __init__(
        self,
        dataset: Dataset,
        num_classes: int,
        num_support_samples: int,
        num_query_samples: int,
        num_episodes: int,
        seed=1000,
    ):
        self._dataset = dataset
        self._num_classes = num_classes
        assert self._num_classes <= len(self._dataset.class_names)

        self._num_support_samples = num_support_samples
        self._num_query_samples = num_query_samples
        self._num_episodes = num_episodes

        self._saved_support = None

        self._saved_query = None

        self._saved_classes = None

        self._rng = np.random.RandomState(seed)

    def __iter__(self) -> Iterator[Tuple[List[Tuple[int, DatapointRole]], List[Tuple[int, DatapointRole]]]]:
        for _ in range(self._num_episodes):
            class_names = (
                self._rng.choice(self._dataset.class_names, self._num_classes, replace=False)
                if self._saved_classes is None
                else self._saved_classes
            )

            support_set = self._saved_support
            query_set = self._saved_query

            if support_set is None and query_set is None:
                support_set = []
                query_set = []

                for class_name in class_names:
                    samples = []
                    indices = self._dataset.name2indices[class_name]
                    num_samples = self._num_support_samples + self._num_query_samples
                    for sample in self._rng.choice(indices, num_samples, replace=False):
                        samples.append(int(sample))

                    support_set.extend(samples[: self._num_support_samples])
                    query_set.extend(samples[self._num_support_samples :])

                q = [(a, DatapointRole.QUERY) for a in query_set]
                s = [(a, DatapointRole.SUPPORT) for a in support_set]

            elif support_set is None:
                support_set = []
                for class_name in class_names:
                    query_indices = [a[0] for a in self._saved_query]
                    mask = np.isin(self._dataset.name2indices[class_name], query_indices)
                    pool = np.array(self._dataset.name2indices[class_name])[~mask]

                    assert np.sum(mask) > 0, "Classes must be locked when locking Support set"

                    support_indices = self._rng.choice(pool, self._num_support_samples, replace=False)
                    support_set.extend(support_indices)

                s = [(a, DatapointRole.SUPPORT) for a in support_set]
                q = query_set

            elif query_set is None:
                query_set = []
                for class_name in class_names:
                    support_indices = [a[0] for a in self._saved_support]
                    mask = np.isin(self._dataset.name2indices[class_name], support_indices)

                    assert np.sum(mask) > 0, "Classes must be locked when locking query set"

                    pool = np.array(self._dataset.name2indices[class_name])[~mask]
                    query_indices = self._rng.choice(pool, self._num_query_samples, replace=False)
                    query_set.extend(query_indices)

                q = [(a, DatapointRole.QUERY) for a in query_set]
                s = support_set
            else:
                q_classes = {self._dataset._labels[a] for a, _ in self._saved_query}
                s_classes = {self._dataset._labels[a] for a, _ in self._saved_support}

                assert all([s in q_classes for s in s_classes]) and len(q_classes) == len(
                    s_classes
                ), "Classes must be the same for locked support and query"

                q = self._saved_query
                s = self._saved_support

            yield q, s

    def lock_classes(self):
        self._saved_classes = self._rng.choice(self._dataset.class_names, self._num_classes, replace=False)

    def unlock_classes(self):
        self._saved_classes = None

    def lock_query(self, query_set):
        self._saved_query = query_set

    def unlock_query(self):
        self._saved_query = None

    def lock_support(self, support_set):
        self._saved_support = support_set

    def unlock_support(self):
        self.saved_support = None
        self.saved_support_indices = None


class EmbeddingSampler(torch.utils.data.Sampler):
    def __init__(self, dataset: Dataset, rank: int, world_size: int):
        self._dataset = dataset
        self._rank = rank
        self._world_size = world_size
        self._num_samples = len(self._dataset) // self._world_size
        self._start_index = self._rank * self._num_samples
        self._end_index = (self._rank + 1) * self._num_samples

        if self._rank == self._world_size - 1:
            self._end_index = len(self._dataset)

    def __iter__(self) -> Iterator[Tuple[int, DatapointRole]]:
        for index in range(self._start_index, self._end_index):
            yield index, DatapointRole.QUERY

    def __len__(self) -> int:
        return self._num_samples

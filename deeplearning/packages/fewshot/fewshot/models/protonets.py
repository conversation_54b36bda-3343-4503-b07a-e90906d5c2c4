from typing import Any

import torch

from ..dataset_types import DatapointRole, Episode
from ..encoders import BaseEncoder
from .base import BaseModel


class ProtoNets(BaseModel):
    def __init__(self, encoder: BaseEncoder, distance_metric: str = "euclidean"):
        super().__init__()

        self._encoder = encoder
        self._distance_metric = distance_metric

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> Any:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        return x1_norm @ x2_norm.t()

    def forward(self, episode: Episode) -> torch.Tensor:
        embeddings = self._encoder(episode.x)

        support_set_list = []
        for class_name in episode.class_names:
            support_indices = []
            for index, datapoint in enumerate(episode.datapoints):
                if datapoint.label == class_name and datapoint.role == DatapointRole.SUPPORT:
                    support_indices.append(index)

            support_embeddings = embeddings[support_indices]

            if len(support_embeddings.shape) == 1:
                support_embeddings = support_embeddings.unsqueeze(0)

            support_centroid = torch.mean(support_embeddings, dim=0)
            support_set_list.append(support_centroid)

        support_set = torch.stack(support_set_list, dim=0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        query_set = embeddings[query_indices]

        if self._distance_metric == "euclidean":
            logits = -torch.cdist(query_set, support_set)
        else:
            logits = self.cosine_cdist(query_set, support_set)

        return logits

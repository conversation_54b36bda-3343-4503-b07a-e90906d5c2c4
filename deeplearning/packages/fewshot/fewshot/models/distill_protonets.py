import json
from typing import Optional

import torch

from ..dataset_types import DatapointRole, Episode
from ..encoders import BaseEncoder
from .base import BaseModel


class ProtoNetsFromEmbeddings(BaseModel):
    def __init__(self, encoder: BaseEncoder, train_embedding_path: str, validation_embedding_path: str):
        super().__init__()

        self._encoder = encoder

        self._train_embeddings_path = train_embedding_path
        self._validation_embeddings_path = validation_embedding_path

        self._train_id2idx = {}
        with open(self._train_embeddings_path, "r") as f:
            for i, line in enumerate(f):
                data = ""
                for ch in line:
                    if ch == ",":
                        break
                    data += ch
                identifier = int(data.split(" ")[-1])  # data is '{"identifier": <identifier>'
                self._train_id2idx[identifier] = i

        self._validation_id2idx = {}
        with open(self._validation_embeddings_path, "r") as f:
            for i, line in enumerate(f):
                data = ""
                for ch in line:
                    if ch == ",":
                        break
                    data += ch
                identifier = int(data.split(" ")[-1])  # data is '{"identifier": <identifier>'
                self._validation_id2idx[identifier] = i

        self._rank: Optional[int] = None
        self._mode = "training"

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def validation_mode(self):
        self._mode = "validation"

    def training_mode(self):
        self._mode = "training"

    def get_embeddings_from_episode(self, episode: Episode) -> torch.Tensor:
        id2idx = self._validation_id2idx if self._mode == "validation" else self._train_id2idx
        path = self._validation_embeddings_path if self._mode == "validation" else self._train_embeddings_path

        lines = [id2idx[datapoint.identifier] for datapoint in episode.datapoints]
        embeddings = [None] * len(lines)
        with open(path, "r") as f:
            for i, line in enumerate(f):
                if i in lines:
                    embeddings[lines.index(i)] = json.loads(line)["embedding"]

        assert all([e is not None for e in embeddings]), "Missing embeddings"

        return torch.tensor(embeddings)

    def set_rank(self, rank: int):
        self._rank = rank

    def forward(self, episode: Episode) -> torch.Tensor:
        embeddings = self.get_embeddings_from_episode(episode)

        if self._rank is not None:
            embeddings = embeddings.to(self._rank)

        support_set_list = []
        for class_name in episode.class_names:
            support_indices = []
            for index, datapoint in enumerate(episode.datapoints):
                if datapoint.label == class_name and datapoint.role == DatapointRole.SUPPORT:
                    support_indices.append(index)

            support_embeddings = embeddings[support_indices]

            if len(support_embeddings.shape) == 1:
                support_embeddings = support_embeddings.unsqueeze(0)

            support_centroid = torch.mean(support_embeddings, dim=0)
            support_set_list.append(support_centroid)

        support_set = torch.stack(support_set_list, dim=0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        query_set = embeddings[query_indices]

        logits = -torch.cdist(query_set, support_set)

        return logits

import json
import logging
import os
from datetime import timedelta

import torch
from torch.distributed.launcher.api import elastic_launch
from torch.nn.parallel import DistributedDataParallel

from . import utilities
from .configs import EmbeddingConfig
from .datasets import Dataset
from .encoders import BaseEncoder
from .samplers import EmbeddingSampler


def embed(model: BaseEncoder, dataset: Dataset, *args, **kwargs) -> None:
    LOG = logging.getLogger("fewshot")
    LOG.info("Starting embedding...")

    embedding_config = EmbeddingConfig(*args, **kwargs)

    launch_config = utilities.get_elastic_launcher_config(num_gpus=embedding_config.num_gpus)
    launcher = elastic_launch(launch_config, embed_subprocess)
    launcher(model, dataset, embedding_config)


def embed_subprocess(
    model: BaseEncoder,
    dataset: Dataset,
    config: EmbeddingConfig,
):
    LOG = logging.getLogger("fewshot")

    timeout = timedelta(minutes=90)

    if not torch.distributed.is_initialized():
        torch.distributed.init_process_group(backend="nccl", timeout=timeout)
    LOG.info(f"Process {torch.distributed.get_rank()} initialized")

    rank = torch.distributed.get_rank()
    world_size = torch.distributed.get_world_size()

    model.eval()
    model.to(rank)
    distributed_model = DistributedDataParallel(model, device_ids=[rank], find_unused_parameters=True)

    if rank == 0:
        LOG.info(f"Embedding {len(dataset)} datapoints")

    dataloader = torch.utils.data.DataLoader(
        dataset,
        sampler=EmbeddingSampler(dataset, rank, world_size),
        collate_fn=utilities.batch_collate_fn,
        batch_size=64,
        num_workers=4,
    )

    embeddings_dir = os.path.join(config.data_dir, "embeddings")
    filepath = os.path.join(embeddings_dir, f"{config.logging_prefix}-embeddings.jsonl")
    os.makedirs(embeddings_dir, exist_ok=True)

    for batch_index, batch in enumerate(dataloader):

        if rank == 0:
            LOG.info(f"Embedding batch {batch_index + 1} / {len(dataloader)}")

        batch = batch.to(rank)

        with torch.no_grad():
            y = distributed_model(batch.x).cpu()

        with open(filepath, "a") as f:
            for datapoint_index, datapoint in enumerate(batch.datapoints):

                line = (
                    json.dumps(
                        {
                            "identifier": datapoint.identifier,
                            "embedding": y[datapoint_index].numpy().tolist(),
                        }
                    )
                    + "\n"
                )
                f.write(line)

    LOG.info(f"Rank {torch.distributed.get_rank()} finished embedding")
    torch.distributed.barrier()
    torch.distributed.destroy_process_group()

import json
import logging
import os
import time
from typing import Callable, List, Optional, Type, Union

import torch
import wandb
from torch.distributed.launcher.api import elastic_launch
from torch.nn.parallel import DistributedDataParallel

from . import utilities
from .configs import TrainingConfig
from .datasets import Dataset
from .embed import embed
from .encoders.embeddings import EmbeddingsReader
from .models import BaseModel
from .models.distill_protonets import ProtoNetsFromEmbeddings
from .models.protonets import ProtoNets
from .samplers import Sampler


class Timer:
    def __init__(self) -> None:
        self._timestamp: Optional[float] = None
        self._delta: Optional[float] = None
        self._deltas: List[float] = []

    def reset(self) -> None:
        self._timestamp = None

    def tick(self) -> None:
        next_timestamp = time.time()

        if self._timestamp is None:
            self._delta = None
        else:
            self._delta = next_timestamp - self._timestamp
            self._deltas.append(self._delta)

        self._timestamp = next_timestamp

    @property
    def delta(self) -> Optional[float]:
        return self._delta

    @property
    def average_delta(self) -> Optional[float]:
        if len(self._deltas) == 0:
            return None
        return sum(self._deltas) / len(self._deltas)


def train(
    model: BaseModel,
    training_dataset: Dataset,
    validation_dataset: Dataset,
    sampler: Type[Sampler] = Sampler,
    parent_model: Optional[BaseModel] = None,
    *args,
    **kwargs,
) -> None:
    LOG = logging.getLogger("fewshot")

    training_config = TrainingConfig(*args, **kwargs)

    distill_model: Optional[BaseModel] = None

    if parent_model is not None:
        if training_config.embed_parent_model:
            train_emb_path = f"{training_config.parent_data_dir}/embeddings/"
            val_emb_path = f"{training_config.parent_data_dir}/embeddings/"

            train_emb_name = "train-distill-embeddings.jsonl"
            val_emb_name = "val-distill-embeddings.jsonl"

            os.makedirs(train_emb_path, exist_ok=True)
            os.makedirs(val_emb_path, exist_ok=True)

            if os.path.exists(train_emb_path + train_emb_name):
                os.remove(train_emb_path + train_emb_name)
            if os.path.exists(val_emb_path + train_emb_name):
                os.remove(val_emb_path + train_emb_name)

            LOG.info("Embedding Training set...")
            embed(
                parent_model.encoder,
                training_dataset,
                data_dir=training_config.parent_data_dir,
                logging_prefix="train-distill",
            )

            LOG.info("Embedding Validation set...")
            embed(
                parent_model.encoder,
                validation_dataset,
                data_dir=training_config.parent_data_dir,
                logging_prefix="val-distill",
            )

            LOG.info(f"Processing embeddings...")
            distill_model = ProtoNetsFromEmbeddings(
                EmbeddingsReader(1024), train_emb_path + train_emb_name, val_emb_path + val_emb_name
            )
        else:
            distill_model = ProtoNets(parent_model.encoder)
    LOG.info("Starting training...")

    launch_config = utilities.get_elastic_launcher_config(num_gpus=training_config.num_gpus)
    launcher = elastic_launch(launch_config, train_subprocess)
    launcher(model, training_dataset, validation_dataset, training_config, sampler, distill_model)

    if training_config.embed_parent_model:
        os.remove(train_emb_path + train_emb_name)
        os.remove(val_emb_path + train_emb_name)


def train_subprocess(
    model: BaseModel,
    training_dataset: Dataset,
    validation_dataset: Dataset,
    config: TrainingConfig,
    sampler: Type[Sampler] = Sampler,
    distill_model: Optional[BaseModel] = None,
) -> None:
    LOG = logging.getLogger("fewshot")
    timer = Timer()

    torch.distributed.init_process_group(backend="nccl")
    rank = int(os.environ["LOCAL_RANK"])

    if rank == 0:
        LOG.info("Fewshot training config:")
        LOG.info(json.dumps(config.__dict__, indent=4))

        os.makedirs(config.data_dir, exist_ok=True)

        description = config.description if config.description is not None else config.model_id

        run = wandb.init(
            name=description,
            project=config.wandb_project,
            id=config.model_id,
            dir=config.data_dir,
            config=config.model_dump(),
        )

    # Move model to GPU and setup model parallelism
    model.to(rank)
    if distill_model is not None:
        if isinstance(distill_model, ProtoNetsFromEmbeddings):
            distill_model.set_rank(rank)
        else:
            distill_model.eval()
            distill_model.to(rank)

    distributed_model = DistributedDataParallel(model, device_ids=[rank], find_unused_parameters=True)

    # Instantiate dataloaders
    training_dataloader = torch.utils.data.DataLoader(
        training_dataset,
        batch_sampler=sampler(
            training_dataset,
            num_classes=config.num_classes,
            num_support_samples=config.num_support_samples,
            num_query_samples=config.num_query_samples,
            num_episodes=config.num_training_episodes,
            seed=rank,
        ),
        collate_fn=utilities.episode_collate_fn,
        num_workers=config.num_workers,
        prefetch_factor=config.prefetch_factor,
    )

    # Logic handles validation sampling configuration options
    if config.num_validation_classes is not None:
        num_validation_classes = config.num_validation_classes
    else:
        num_validation_classes = config.num_classes

    if config.num_validation_support_samples is not None:
        num_validation_support_samples = config.num_validation_support_samples
    else:
        num_validation_support_samples = config.num_support_samples

    if config.num_validation_query_samples is not None:
        num_validation_query_samples = config.num_validation_query_samples
    else:
        num_validation_query_samples = config.num_query_samples

    if num_validation_classes > len(validation_dataset.class_names):
        raise RuntimeError(
            f"Number of validation classes ({num_validation_classes}) must be less than or equal to the number of classes in the dataset ({len(validation_dataset.class_names)})."
        )

    validation_dataloader = torch.utils.data.DataLoader(
        validation_dataset,
        batch_sampler=sampler(
            validation_dataset,
            num_classes=num_validation_classes,
            num_support_samples=num_validation_support_samples,
            num_query_samples=num_validation_query_samples,
            num_episodes=config.num_validation_episodes,
            seed=rank,
        ),
        collate_fn=utilities.episode_collate_fn,
        num_workers=config.num_workers,
        prefetch_factor=config.prefetch_factor,
    )

    # Instantiate loss function and optimizer
    loss_fn = torch.nn.CrossEntropyLoss()
    log_softmax = torch.nn.LogSoftmax(dim=1)

    if config.distill_loss == "kldiv":
        distill_loss_fn = torch.nn.KLDivLoss(reduction="batchmean", log_target=True)  # type: ignore
    elif config.distill_loss == "crossentropy":
        distill_loss_fn = torch.nn.CrossEntropyLoss()  # type: ignore
    else:
        raise ValueError(f"Unknown distillation loss: {config.distill_loss}")
    optimizer = torch.optim.Adam(
        distributed_model.parameters(), lr=config.learning_rate, weight_decay=config.weight_decay
    )
    global_step = 0

    # First validation loop
    distributed_model.eval()

    if isinstance(distill_model, ProtoNetsFromEmbeddings):
        distill_model.validation_mode()

    losses = []
    parent_accuracies = []
    parent_losses = []
    standard_losses = []
    accuracies = []

    timer.reset()
    for episode_index, episode in enumerate(validation_dataloader):
        timer.tick()

        episode = episode.to(rank)

        with torch.no_grad():
            logits = distributed_model(episode)

            if distill_model is not None:
                parent_logits = distill_model(episode)
                distill_loss = distill_loss_fn(log_softmax(logits), log_softmax(parent_logits))
                standard_loss = loss_fn(logits, episode.y)
                loss = (1 - config.parent_loss_weight) * standard_loss + config.parent_loss_weight * distill_loss

                parent_accuracy = (parent_logits.argmax(dim=1) == episode.y).float().mean()
                parent_accuracies.append(parent_accuracy.item())
                parent_losses.append(distill_loss.item())
                standard_losses.append(standard_loss.item())

            else:
                loss = loss_fn(logits, episode.y)

                parent_accuracy = None
            accuracy = (logits.argmax(dim=1) == episode.y).float().mean()

            torch.distributed.all_reduce(loss, op=torch.distributed.ReduceOp.AVG)
            torch.distributed.all_reduce(accuracy, op=torch.distributed.ReduceOp.AVG)

        losses.append(loss.item())
        accuracies.append(accuracy.item())

        if rank == 0:
            step_log(
                -1,
                config.num_epochs,
                episode_index,
                config.num_validation_episodes,
                loss.item(),
                accuracy.item(),
                timer.delta,
                training=False,
                parent_accuracy=parent_accuracy.item() if parent_accuracy is not None else None,
                verbose=config.verbose,
            )

    if rank == 0:
        validation_loss = sum(losses) / len(losses)
        validation_accuracy = sum(accuracies) / len(accuracies)
        if config.verbose:
            validation_end_log(validation_loss, validation_accuracy)

        records = {
            "validation-step-delta": timer.average_delta,
            "validation-loss": validation_loss,
            "validation-accuracy": validation_accuracy,
            "epoch": 0,
        }

        if distill_model is not None:
            records["distill-loss"] = distill_loss.item()
            records["distill-accuracy"] = parent_accuracy.item()

            records["standard-loss"] = standard_loss.item()

        run.log(records, step=global_step)

    best_accuracy = 0.0
    for epoch_index in range(config.num_epochs):
        distributed_model.train()
        if isinstance(distill_model, ProtoNetsFromEmbeddings):
            distill_model.training_mode()

        timer.reset()
        for episode_index, episode in enumerate(training_dataloader):
            timer.tick()
            episode = episode.to(rank)

            optimizer.zero_grad()

            logits = distributed_model(episode)

            if distill_model is not None:

                with torch.no_grad():
                    parent_logits = distill_model(episode)
                    distill_loss = distill_loss_fn(log_softmax(logits), log_softmax(parent_logits))

                standard_loss = loss_fn(logits, episode.y)
                loss = (1 - config.parent_loss_weight) * standard_loss + config.parent_loss_weight * distill_loss

                parent_accuracy = (parent_logits.argmax(dim=1) == episode.y).float().mean()
                parent_accuracies.append(parent_accuracy.item())
                parent_losses.append(distill_loss.item())
                standard_losses.append(standard_loss.item())
            else:
                loss = loss_fn(logits, episode.y)

                parent_accuracy = None

            accuracy = (logits.argmax(dim=1) == episode.y).float().mean()

            loss.backward()
            optimizer.step()

            torch.distributed.all_reduce(loss, op=torch.distributed.ReduceOp.AVG)
            torch.distributed.all_reduce(accuracy, op=torch.distributed.ReduceOp.AVG)

            if rank == 0:
                step_log(
                    epoch_index,
                    config.num_epochs,
                    episode_index,
                    config.num_training_episodes,
                    loss.item(),
                    accuracy.item(),
                    timer.delta,
                    parent_accuracy=parent_accuracy.item() if parent_accuracy is not None else None,
                    verbose=config.verbose,
                )

                records = {
                    "training-step-delta": timer.delta,
                    "training-loss": loss.item(),
                    "training-accuracy": accuracy.item(),
                    "epoch": epoch_index + 1,
                }

                if distill_model is not None:
                    records["distill-loss"] = distill_loss.item()
                    records["distill-accuracy"] = parent_accuracy.item()

                    records["standard-loss"] = standard_loss.item()

                if episode_index % 1000 == 0:
                    records["Training image sample (support and query)"] = utilities.get_support_query_sample_image(
                        episode
                    )

                run.log(records, step=global_step)

            global_step += 1

        distributed_model.eval()

        if isinstance(distill_model, ProtoNetsFromEmbeddings):
            distill_model.validation_mode()
        losses = []
        accuracies = []
        timer.reset()
        for episode_index, episode in enumerate(validation_dataloader):
            timer.tick()
            episode = episode.to(rank)

            with torch.no_grad():
                logits = distributed_model(episode)
                if distill_model is not None:
                    parent_logits = distill_model(episode)
                    distill_loss = distill_loss_fn(log_softmax(logits), log_softmax(parent_logits))
                    standard_loss = loss_fn(logits, episode.y)
                    loss = (1 - config.parent_loss_weight) * standard_loss + config.parent_loss_weight * distill_loss

                    parent_accuracy = (parent_logits.argmax(dim=1) == episode.y).float().mean()
                    parent_accuracies.append(parent_accuracy.item())
                    parent_losses.append(distill_loss.item())
                else:
                    loss = loss_fn(logits, episode.y)

                    parent_accuracy = None
                accuracy = (logits.argmax(dim=1) == episode.y).float().mean()

                torch.distributed.all_reduce(loss, op=torch.distributed.ReduceOp.AVG)
                torch.distributed.all_reduce(accuracy, op=torch.distributed.ReduceOp.AVG)

            losses.append(loss.item())
            accuracies.append(accuracy.item())

            if rank == 0:
                step_log(
                    epoch_index,
                    config.num_epochs,
                    episode_index,
                    config.num_validation_episodes,
                    loss.item(),
                    accuracy.item(),
                    timer.delta,
                    training=False,
                    parent_accuracy=parent_accuracy.item() if parent_accuracy is not None else None,
                    verbose=config.verbose,
                )

        validation_loss = sum(losses) / len(losses)
        validation_accuracy = sum(accuracies) / len(accuracies)
        if rank == 0:
            validation_end_log(validation_loss, validation_accuracy)

            new_best_accuracy = False
            if validation_accuracy > best_accuracy:
                best_accuracy = validation_accuracy
                new_best_accuracy = True
                LOG.info(f"New best accuracy: {best_accuracy:.4f}")

            record = {
                "validation-step-delta": timer.average_delta,
                "validation-loss": validation_loss,
                "validation-accuracy": validation_accuracy,
                "validation-accuracy-best": best_accuracy,
                "epoch": epoch_index + 1,
            }

            if distill_model is not None:
                records["distill-loss"] = distill_loss.item()
                records["distill-accuracy"] = parent_accuracy.item()

                records["standard-loss"] = standard_loss.item()

            run.log(record, step=global_step)

            save_model(config.data_dir, distributed_model, new_best_accuracy or epoch_index == 0)

    if rank == 0:
        run.finish()
        LOG.info("Training complete.")


def validation_end_log(loss: float, accuracy: float) -> None:
    LOG = logging.getLogger("fewshot")
    LOG.info(f"Validation sweep average loss: {loss}")
    LOG.info(f"Validation sweep average accuracy: {accuracy}")


def step_log(
    epoch_index: int,
    num_epochs: int,
    episode_index: int,
    num_episodes: int,
    loss: float,
    accuracy: float,
    time_delta: Optional[float],
    training: bool = True,
    parent_accuracy: Optional[float] = None,
    verbose: bool = False,
) -> None:
    if verbose:
        message = f"Epoch {epoch_index + 1} / {num_epochs} | "

        if training:
            message += f"Training Episode {episode_index + 1: >3} / {num_episodes} | "
        else:
            message += f"Validation Episode {episode_index + 1: >3} / {num_episodes} | "

        if time_delta is None:
            message += f"Loss: {loss:2.2f} | Accuracy: {accuracy:.3f} {f'Parent Accuracy: {parent_accuracy:.3f}' if parent_accuracy is not None else ''}"
        else:
            message += f"Loss: {loss:2.2f} | Accuracy: {accuracy:.3f} {f'Parent Accuracy: {parent_accuracy:.3f}' if parent_accuracy is not None else ''} | Elapsed time: {time_delta:.3f} (seconds)"

        LOG = logging.getLogger("fewshot")
        LOG.info(message)
    else:
        if epoch_index == 0:
            LOG = logging.getLogger("fewshot")
            LOG.info(f"Epoch {epoch_index + 1} / {num_epochs}")


def save_model(data_dir: str, model: torch.nn.Module, save_best: bool) -> None:
    LOG = logging.getLogger("fewshot")

    weights_dir = os.path.join(data_dir, "weights")
    latest_filepath = os.path.join(weights_dir, "latest.pt")
    best_filepath = os.path.join(weights_dir, "best.pt")

    os.makedirs(weights_dir, exist_ok=True)

    LOG.info(f"Saving model to {latest_filepath}")
    torch.save(model.state_dict(), latest_filepath)

    if save_best:
        LOG.info(f"Saving model to {best_filepath}")
        torch.save(model.state_dict(), best_filepath)

import argparse
import copy
import datetime
import logging
import os
from typing import Optional

from lightning.pytorch.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.clustering.utils import enable_embedding_clusters_sampling
from deeplearning.constants import CARBON_DATA_DIR, DeepweedTrainingSubtype
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import (
    DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS,
    DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH_HIT,
    DATASET_SAMPLING_ALGORITHM_UNIFORM,
)
from deeplearning.deepweed.remote_veselka_dataset import VeselkaDataset, initialize_datasets
from deeplearning.deepweed.sampling_visualization import simulate_sampling
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    OptimizeAndTestArguments,
    PumapArguments,
    TrainAndUnoptimizedTestArguments,
    cleanup_spawn_script,
    evaluate_comparison_embeddings,
    evaluate_embeddings_for_new_points,
    get_upload_model_fn,
    load_pumap_head,
    optimize_and_test,
    test_pumap,
    train_and_unoptimized_test,
    train_pumap,
)
from deeplearning.scripts.utils.elastic_launcher import elastic_launcher
from deeplearning.scripts.utils.utils import (
    CONTAINER_VERSION,
    TrainingInfo,
    add_common_arguments,
    add_embedding_training_arguments,
    autodetect_driptape_model,
    autodetect_pretrained_model,
    excluded_crop_ids_for_all_weeds,
    generate_model_id,
    get_crop_and_weed_names,
    get_dataset,
    get_dataset_split_image_count,
    get_dataset_v2,
    get_last_nth_timestamps,
    get_pipeline,
    get_project_name,
    get_segmentation_classes,
    modify_dataset,
    modify_dataset_for_all_weeds,
    pick_comparison_model_id,
    run_debuggable,
    setup_logging,
    update_dataset_version,
    update_dl_config_dict_for_embedding_balancing,
)
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.trainer import Environment, upload_directory
from deeplearning.utils.wandb_logger import make_wandb_logger
from lib.common.perf.perf_tracker import (
    duration_perf_recorder_decorator,
    set_autowrite_filename,
    set_autowrite_frequency,
)

assert CARBON_DATA_DIR is not None

setup_logging(level=logging.INFO)
LOG = logging.getLogger(__name__)


@record
@duration_perf_recorder_decorator("Training")
def main() -> None:  # noqa: C901
    try:
        logger: Optional[WandbLogger] = None
        veselka_dataset: Optional[VeselkaDataset] = None

        parser = argparse.ArgumentParser()
        parser.add_argument("--autodetect-pretrained-model", action="store_true")
        parser.add_argument(
            "--no-autodetect-pretrained-model", dest="autodetect_pretrained_model", action="store_false"
        )
        parser.add_argument("--autodetect-driptape-model", action="store_true")
        parser.add_argument("--no-autodetect-driptape-model", dest="autodetect_driptape_model", action="store_false")
        parser.add_argument("--pretrained-model", type=str, default=None)
        parser.add_argument("--segmentation-model", type=str, default=None)
        parser.add_argument(
            "--frozen-backbone-point-layers",
            type=str,
            default=None,
            help="Backbone point layers to freeze. In our use of resnet 50, layers are bn1,conv1,layer1,layer2,layer3",
        )  # layer1,layer2 seem to have negligible impact.
        parser.add_argument("--goal-percentage-new", type=float, default=None)
        parser.add_argument("--disable-crop", action="store_true", help="Disable crop predictions")
        parser.add_argument(
            "--dataset-geohashes",
            type=str,
            default=None,
            help="If specified, will limit dataset created to only images within this geohash region",
        )
        parser.add_argument(
            "--balance-geohash",
            type=int,
            default=4,
            help="If specified, will balance by geohash precision during dataset generation",
        )
        parser.add_argument(
            "--use-date-groups",
            action="store_true",
            help="Will hierarchically sample dates via date groups, if grouping was used during dataset generation",
        )
        parser.add_argument(
            "--balance-positive-classes", action="store_true", help="Balance positive classes in dataset generation"
        )
        parser.add_argument("--no-balance-positive-classes", action="store_false", dest="balance_positive_classes")
        parser.add_argument("--use-all-weeds", action="store_true", dest="use_all_weeds")
        parser.add_argument("--robot-ids", type=str, default=None)
        parser.add_argument("--geohash-max-precision", type=int, default=4)
        parser.add_argument("--geohash-min-precision", type=int, default=4)
        parser.add_argument("--predict-plants-first", action="store_true", default=False)
        parser.add_argument("--core-data-level", type=str, default="unfiltered")
        parser.add_argument("--dropout-rate", type=float, default=0.5)

        parser.add_argument("--dataset-start-ms", type=int, default=None)
        parser.add_argument("--dataset-end-ms", type=int, default=None)

        parser.add_argument("--simulate-sampling", action="store_true", default=False)

        parser.set_defaults(use_all_weeds=False)
        parser.set_defaults(autodetect_pretrained_model=True)
        parser.set_defaults(autodetect_driptape_model=True)
        parser.set_defaults(long_run=False)
        parser.set_defaults(balance_positive_classes=True)

        add_embedding_training_arguments(parser)

        add_common_arguments(parser)

        args = parser.parse_args()

        assert not (args.production and args.preview), "Run cannot be both production and preview"

        tags = ["full-training"]

        if args.tags is not None:
            tags += args.tags

        if args.pretrained_model == "" or args.pretrained_model == "none":
            args.pretrained_model = None

        if args.job_id is None:
            model_id = generate_model_id()
        else:
            model_id = args.job_id

        dl_config_dict = {
            "wandb_project": get_project_name(args.fast_run),
            "use_all_weeds": args.use_all_weeds,
            "core_data_level": args.core_data_level,
            "dropout_rate": args.dropout_rate,
            "num_samples": 64 if args.fast_run else 6000,
            "num_epochs": 1 if args.fast_run else 40,
            "pretrain_layer_prefix_skip_list": ["point_category_convs"],
            "fast_run": args.fast_run,
            "training_set_ratio": 1.0,
            "checkpoint_start_ratio": 0.8,
            "sampling_algorithm": DATASET_SAMPLING_ALGORITHM_UNIFORM,
            "sampling_geohash_precision": 6,
            "model_id": model_id,
            **args.dl_config,
        }

        if "dataset_version" not in dl_config_dict:
            dl_config_dict["dataset_version"] = 2

        dataset_version = dl_config_dict["dataset_version"]

        set_autowrite_filename(f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/timing/main")
        set_autowrite_frequency(60000)

        if args.preview and not args.fast_run:
            environment = Environment.PREVIEW
        elif args.production and not args.fast_run:
            environment = Environment.PRODUCTION
        else:
            environment = Environment.DEVELOPMENT

        if (args.production or args.preview) and not CONTAINER_VERSION:
            assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
        elif CONTAINER_VERSION:
            if not args.pretrained_model and args.autodetect_pretrained_model:
                args.pretrained_model = autodetect_pretrained_model(environment)

            if not args.segmentation_model and args.autodetect_driptape_model:
                args.segmentation_model = autodetect_driptape_model(min_test_oec=0.85, environment=environment)

        comparison_model_id = None
        training_info = None
        if args.pretrained_model is not None:
            training_info = TrainingInfo(args.pretrained_model)

        comparison_model_id = pick_comparison_model_id(
            args.comparison_model_id, training_info.comparison_model_id if training_info is not None else None,
        )
        assert comparison_model_id is not None
        dl_config_dict["comparison_model_id"] = comparison_model_id

        dl_config_dict = update_dl_config_dict_for_embedding_balancing(
            dl_config_dict,
            pipeline_id=args.pipeline_id,
            environment=environment,
            sub_type=DeepweedTrainingSubtype.FULL_TRAIN,
            fallback_sampling_algorithm=DATASET_SAMPLING_ALGORITHM_GEOHASH_MONTH_HIT,
        )

        parent_dataset_id = training_info.dataset_id if training_info is not None else None

        dataset_version = update_dataset_version(
            dataset_id=args.dataset_id, parent_dataset_id=parent_dataset_id, dataset_version=dataset_version
        )

        dl_config_dict["dataset_version"] = dataset_version
        dl_config = DeepweedConfig.from_dict(dl_config_dict)

        pipeline = get_pipeline(args.pipeline_id)

        robots = None
        if args.robot_ids is not None:
            robots = args.robot_ids.split(",")

        if args.dataset_id:
            if dataset_version == 1:
                dataset_id, _ = get_dataset(dataset_id=args.dataset_id)
            elif dataset_version == 2:
                dataset_id, _ = get_dataset_v2(dataset_id=args.dataset_id)

        else:
            geohashes = None
            if args.dataset_geohashes is not None:
                geohashes = args.dataset_geohashes.split(",")
            if args.use_all_weeds:
                excluded_crop_ids = excluded_crop_ids_for_all_weeds(pipeline)
                if dataset_version == 2:
                    dataset_id, _ = get_dataset_v2(
                        dataset_id=args.dataset_id,
                        parent_id=parent_dataset_id,
                        robots=robots,
                        exclude_crop_ids=excluded_crop_ids,
                        geohash=geohashes,
                        start_timestamp_ms=args.dataset_start_ms,
                        end_timestamp_ms=args.dataset_end_ms,
                    )
                elif dataset_version == 1:
                    dataset_id, _ = get_dataset(
                        dataset_id=args.dataset_id,
                        fast_run=args.fast_run,
                        balance_positive_classes=args.balance_positive_classes,
                        balance_geohash=args.balance_geohash,
                        robots=robots,
                        exclude_crop_ids=excluded_crop_ids,
                        parent_id=parent_dataset_id,
                        geohash=geohashes,
                        core_data_level=dl_config.core_data_level,
                        start_timestamp_ms=args.dataset_start_ms,
                        end_timestamp_ms=args.dataset_end_ms,
                    )
            else:
                if dataset_version == 2:
                    dataset_id, _ = get_dataset_v2(
                        pipeline_id=pipeline.id,
                        parent_id=parent_dataset_id,
                        robots=robots,
                        geohash=geohashes,
                        start_timestamp_ms=args.dataset_start_ms,
                        end_timestamp_ms=args.dataset_end_ms,
                    )
                elif dataset_version == 1:
                    dataset_id, _ = get_dataset(
                        crop_ids=pipeline.data_source_crop_ids,
                        parent_id=parent_dataset_id,
                        fast_run=args.fast_run,
                        balance_positive_classes=args.balance_positive_classes,
                        balance_geohash=args.balance_geohash,
                        robots=robots,
                        geohash=geohashes,
                        val_test_crop_ids=pipeline.deployment_crop_ids,
                        core_data_level=dl_config.core_data_level,
                        start_timestamp_ms=args.dataset_start_ms,
                        end_timestamp_ms=args.dataset_end_ms,
                    )

        num_training_images = get_dataset_split_image_count(dataset_id, "train")

        if args.use_all_weeds:
            modify_dataset_for_all_weeds(dataset_id, pipeline.data_source_crop_ids, pipeline.deployment_crop_ids)

        if dl_config.modify_dataset:
            modify_dataset(dl_config, dataset_id)

        if "disable_crop" in pipeline.custom_arguments:
            args.disable_crop = bool(pipeline.custom_arguments["disable_crop"])

        frozen_backbone_point_layers = None
        if args.frozen_backbone_point_layers is not None:
            frozen_backbone_point_layers = args.frozen_backbone_point_layers.split(",")

        segmentation_training_info = None
        pretrained_segmentation_model = None
        if training_info is not None:
            pretrained_model = training_info.model_weights
            segmentation_training_info = training_info
        else:
            pretrained_model = None

        if args.segmentation_model is not None:
            segmentation_training_info = TrainingInfo(args.segmentation_model)

        if segmentation_training_info is not None:
            pretrained_segmentation_model = segmentation_training_info.best_model_weights
            test_segmentation_classes = get_segmentation_classes(segmentation_training_info)
        else:
            pretrained_segmentation_model = None
            test_segmentation_classes = None

        if args.description is None:
            description = f"(train) Development run {datetime.datetime.now()}"
        else:
            description = args.description

        crops, weeds = get_crop_and_weed_names(dataset_id, dataset_version=dataset_version)
        if args.disable_crop:
            crops = []
        elif args.use_all_weeds:
            crops = ["CROP"]

        tags.append(pipeline.name.lower())
        if args.pretrained_model:
            tags.append("pretrained")

        config = {"use_all_weeds": args.use_all_weeds}

        if args.pretrained_model is not None:
            config["parent_model_id"] = args.pretrained_model
        if args.segmentation_model is not None:
            config["parent_segmentation_model_id"] = args.segmentation_model

        dataset_parameters = {
            "config": dl_config,
            "train_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json",
            "validation_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/validation.json",
            "test_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json",
            "num_samples": (
                max(int(num_training_images * dl_config.training_set_ratio), dl_config.num_samples)
                if dl_config.training_set_ratio is not None
                else dl_config.num_samples
            ),
            "crop_classes": tuple(crops),
            "weed_classes": tuple(weeds),
            "test_segm_classes": tuple(test_segmentation_classes) if test_segmentation_classes is not None else None,
            "train_ppi": 200,
            "geohash_max_precision": args.geohash_max_precision,
            "geohash_min_precision": args.geohash_min_precision,
            "use_date_groups": args.use_date_groups,
            "calibration_dataset_size": 16 if args.fast_run else 1024,
            "embedding_balancing_evaluation_path": f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dl_config.embedding_balancing_model}/image_point_embeddings"
            if dl_config.embedding_balancing_model is not None
            else None,
            "embedding_type": EmbeddingType.FULL
            if dl_config.evaluate_full_embeddings
            else EmbeddingType.REDUCED_SCALED,
        }

        if dataset_version == 2:
            dataset_parameters["train_filepath"] += "l"  # jsonl
            dataset_parameters["validation_filepath"] += "l"
            dataset_parameters["test_filepath"] += "l"

        if args.simulate_sampling:
            LOG.info("Running dataset sampling simulation")
            LOG.info(f"model_id={model_id}")

            sampling_params = copy.deepcopy(dataset_parameters)
            sampling_params["config_dict"] = sampling_params["config"].to_json()
            del sampling_params["config"]

            train_dataset, _, _, _ = initialize_datasets(**sampling_params)
            simulate_sampling(
                train_dataset,
                number_samples=dl_config.num_samples,
                epochs=dl_config.num_epochs,
                dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
            )

            if os.path.exists(f"/data/deeplearning/models/{model_id}"):
                upload_directory(f"/data/deeplearning/models/{model_id}")

            return

        if dl_config.train_embeddings and dl_config.evaluate_new_comparison_data:
            elastic_launcher(
                args.nproc_per_node,
                evaluate_comparison_embeddings,
                get_upload_model_fn(model_id),
                EvaluateComparisonEmbeddingsArguments(
                    comparison_model_id=comparison_model_id,
                    environment=environment,
                    dataset_id=dataset_id,
                    fast_run=args.fast_run,
                    dataset_version=dataset_version,
                    eval_files=["train.jsonl"] if dataset_version == 2 else ["train.json"],
                ),
            )

        if dl_config.evaluate_embeddings_for_new_points:
            evaluate_embeddings_for_new_points(
                dl_config=dl_config,
                model_id=model_id,
                dataset_id=dataset_id,
                nproc_per_node=args.nproc_per_node,
                crops=crops,
                weeds=weeds,
            )

        if dl_config.sampling_algorithm == DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS:
            enable_embedding_clusters_sampling(
                dl_config=dl_config, dataset_id=dataset_id, dataset_parameters=dataset_parameters
            )

        dw_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/{model_id}")

        logger = make_wandb_logger(
            name=description, tags=safe_split(tags), project=dl_config.wandb_project, exp_dir=dw_experiment_dir,
        )
        assert logger is not None

        veselka_dataset = VeselkaDataset(**dataset_parameters)
        assert veselka_dataset is not None
        last_nth_timestamps = get_last_nth_timestamps(veselka_dataset.datasets.new_data_captured_ats)

        elastic_launcher(
            args.nproc_per_node,
            train_and_unoptimized_test,
            get_upload_model_fn(model_id),
            TrainAndUnoptimizedTestArguments(
                dl_config=dl_config,
                datasets=veselka_dataset.datasets,
                pretrained_model=pretrained_model,
                pretrained_segmentation_model=pretrained_segmentation_model,
                description=description,
                tags=tags,
                model_id=model_id,
                resume_from=args.resume_from,
                environment=environment,
                frozen_backbone_point_layers=frozen_backbone_point_layers,
                dataset_id=dataset_id,
                last_nth_timestamps=last_nth_timestamps,
                pipeline_id=args.pipeline_id,
                data_source_crop_ids=pipeline.data_source_crop_ids,
                wandb_config=config,
                parent_model_id=args.pretrained_model,
                logger=logger,
                sub_type=DeepweedTrainingSubtype.FULL_TRAIN.name.lower(),
            ),
        )

        pumap_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/pumap-{model_id}")
        if dl_config.train_embeddings_pumap:
            elastic_launcher(
                args.nproc_per_node,
                train_pumap,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_experiment_dir=dw_experiment_dir,
                    pumap_experiment_dir=pumap_experiment_dir,
                    dw_config=dl_config,
                    logger=logger,
                ),
            )
            elastic_launcher(
                args.nproc_per_node,
                test_pumap,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_experiment_dir=dw_experiment_dir, pumap_experiment_dir=pumap_experiment_dir, dw_config=dl_config,
                ),
            )
            elastic_launcher(
                args.nproc_per_node,
                load_pumap_head,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_config=dl_config, dw_experiment_dir=dw_experiment_dir, pumap_experiment_dir=pumap_experiment_dir,
                ),
            )

        elastic_launcher(
            args.nproc_per_node,
            optimize_and_test,
            get_upload_model_fn(model_id),
            OptimizeAndTestArguments(
                dl_config=dl_config,
                datasets=veselka_dataset.datasets,
                pretrained_model=pretrained_model,
                pretrained_segmentation_model=pretrained_segmentation_model,
                description=description,
                tags=tags,
                model_id=model_id,
                wandb_config=config,
                dataset_id=dataset_id,
                last_nth_timestamps=last_nth_timestamps,
                data_source_crop_ids=pipeline.data_source_crop_ids,
                parent_model_id=args.pretrained_model,
                logger=logger,
            ),
        )
    except Exception as e:
        raise e
    finally:
        cleanup_spawn_script(logger, veselka_dataset)


if __name__ == "__main__":
    run_debuggable(main)

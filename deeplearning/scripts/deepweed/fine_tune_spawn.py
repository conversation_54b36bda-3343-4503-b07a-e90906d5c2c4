import argparse
import copy
import datetime
import json
import logging
import os
from typing import Any, Dict, List, Optional

import geolib.geohash
from lightning.pytorch.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.constants import CARBON_DATA_DIR, DeepweedTrainingSubtype
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import DATASET_SAMPLING_ALGORITHM_DEFAULT, DATASET_SAMPLING_ALGORITHM_EMBEDDINGS
from deeplearning.deepweed.dataset_utils import (
    find_recency_split,
    generate_is_in_geohashes,
    generate_is_new_default,
    generate_is_new_recency,
    generate_is_new_robot_specific,
)
from deeplearning.deepweed.remote_veselka_dataset import VeselkaDataset, initialize_datasets
from deeplearning.deepweed.sampling_visualization import simulate_sampling
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    EvaluateDeepweedEmbeddingsOnDatasetArguments,
    OptimizeAndTestArguments,
    PumapArguments,
    TrainAndUnoptimizedTestArguments,
    cleanup_spawn_script,
    evaluate_comparison_embeddings,
    evaluate_embeddings_on_dataset_id,
    get_upload_model_fn,
    load_pumap_head,
    optimize_and_test,
    test_pumap,
    train_and_unoptimized_test,
    train_pumap,
)
from deeplearning.scripts.utils.elastic_launcher import elastic_launcher
from deeplearning.scripts.utils.utils import (
    CONTAINER_VERSION,
    TrainingInfo,
    add_common_arguments,
    add_embedding_training_arguments,
    autodetect_driptape_model,
    autodetect_fine_tune_parent_model,
    generate_model_id,
    get_crop_and_weed_names,
    get_dataset,
    get_dataset_v2,
    get_last_nth_timestamps,
    get_pipeline,
    get_project_name,
    get_segmentation_classes,
    pick_comparison_model_id,
    run_debuggable,
    setup_logging,
    update_dataset_version,
    update_dl_config_dict_for_embedding_balancing,
)
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.trainer import Environment, upload_directory
from deeplearning.utils.wandb_logger import make_wandb_logger
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder, set_verbosity

set_verbosity(False)
setup_logging()
MIN_TRAINING_IMAGES = 1
LOG = logging.getLogger(__name__)


@record
def main() -> None:  # noqa: C901
    logger: Optional[WandbLogger] = None
    veselka_dataset: Optional[VeselkaDataset] = None
    try:
        parser = argparse.ArgumentParser()
        parser.add_argument("--pretrained-model", type=str, default=None)
        parser.add_argument("--num-balanced-examples", type=int, default=None)
        parser.add_argument("--epochs", type=int, default=None)
        parser.add_argument("--learning-rate", type=float, default=0.00025)
        parser.add_argument("--lr-milestones", type=str, default="10,15,20")
        parser.add_argument("--lr-gamma", type=float, default=0.5)
        parser.add_argument("--new-loss-multiplier", type=float, default=1.0)
        parser.add_argument("--positive-sample-percentage", type=float, default=10 / 11)
        parser.add_argument("--positive-sample-gamma", type=float, default=1.0)
        parser.add_argument("--positive-sample-milestones", type=str, default=None)
        parser.add_argument("--no-log", action="store_true", default=False)
        parser.add_argument("--disable-crop", action="store_true", help="Disable crop predictions")
        parser.add_argument(
            "--dataset-geohash",
            type=str,
            default=None,
            help="If specified, will limit dataset created to only images within this geohash region",
        )
        parser.add_argument("--balance-robot-ids", action="store_true", help="Balances robot id on dataset generation")
        parser.add_argument(
            "--balance-geohash", type=int, default=None, help="Balance geohash at given precision in dataset generation"
        )
        parser.add_argument("--autodetect-driptape-model", action="store_true")
        parser.add_argument("--no-autodetect-driptape-model", dest="autodetect_driptape_model", action="store_false")
        parser.add_argument("--segmentation-model", type=str, default=None)
        parser.add_argument("--geohash-max-precision", type=int, default=4)
        parser.add_argument("--geohash-min-precision", type=int, default=4)
        parser.add_argument(
            "--use-date-groups",
            action="store_true",
            help="Will hierarchically sample dates via date groups, if grouping was used during dataset generation",
        )
        parser.add_argument("--predict-plants-first", action="store_true", default=False)

        # DEPRECATED, only here until we get new triggering going to prevent current triggers from failing
        parser.add_argument("--goal-percentage-new", default=0.3, type=float)

        # DEPRECATED, here so support/Matias can easily trigger geo fine tunes for Europe and Mark in Australia
        parser.add_argument("--geohashes", type=str, default=None)
        parser.add_argument("--no-use-recency", action="store_true", default=False)
        parser.add_argument("--use-geohash", action="store_true", default=False)
        parser.add_argument("--new-data-weight", type=float, default=None)
        parser.add_argument("--simulate-sampling", action="store_true", default=False)
        parser.add_argument("--dataset-start-ms", type=int, default=None)
        parser.add_argument("--dataset-end-ms", type=int, default=None)

        parser.add_argument("--center-geohash", type=str, default=None)

        parser.set_defaults(autodetect_driptape_model=True)

        add_embedding_training_arguments(parser)
        add_common_arguments(parser)

        args = parser.parse_args()

        lr_milestones = [int(milestone) for milestone in args.lr_milestones.split(",")]

        environment = Environment.DEVELOPMENT
        if args.preview:
            environment = Environment.PREVIEW
        elif args.production:
            environment = Environment.PRODUCTION

        if args.fast_run:
            environment = Environment.DEVELOPMENT

        if args.job_id is None:
            experiment_id = generate_model_id()
        else:
            experiment_id = args.job_id

        dl_config_dict = {
            "wandb_project": get_project_name(args.fast_run),
            "lr": args.learning_rate,
            "lr_gamma": args.lr_gamma,
            "lr_milestones": lr_milestones,
            "pretrained_model": args.pretrained_model,
            "num_samples": 64 if args.fast_run else 2000,
            "num_epochs": 1 if args.fast_run else 20,
            "fast_run": args.fast_run,
            "model_id": experiment_id,
            **args.dl_config,
        }

        if "dataset_version" not in dl_config_dict:
            dl_config_dict["dataset_version"] = 2

        dataset_version = dl_config_dict["dataset_version"]

        if args.no_use_recency:
            dl_config_dict["use_recency"] = False
        if args.use_geohash:
            dl_config_dict["use_geohash"] = True

        if args.geohashes is not None:
            dl_config_dict["emphasized_geohashes"] = args.geohashes.split(",")
        elif args.center_geohash is not None:
            dl_config_dict["emphasized_geohashes"] = [args.center_geohash] + list(
                geolib.geohash.neighbours(args.center_geohash)
            )

        if args.new_data_weight is not None:
            dl_config_dict["new_data_weight"] = args.new_data_weight

        if not dl_config_dict["pretrained_model"]:
            if (args.production or args.preview) and not CONTAINER_VERSION:
                assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
            elif CONTAINER_VERSION:
                dl_config_dict["pretrained_model"] = autodetect_fine_tune_parent_model(environment, args.pipeline_id)

        assert dl_config_dict["pretrained_model"] is not None, "Cannot run fine-tuning without pretrained model"
        assert not (args.production and args.preview), "Run cannot be both production and preview"
        assert args.pipeline_id is not None

        if (args.production or args.preview) and not CONTAINER_VERSION:
            assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
        elif CONTAINER_VERSION:
            if not args.segmentation_model and args.autodetect_driptape_model:
                args.segmentation_model = autodetect_driptape_model(min_test_oec=0.85, environment=environment)

        positive_sample_milestones = None

        if args.positive_sample_milestones is not None:
            positive_sample_milestones = [int(milestone) for milestone in args.positive_sample_milestones.split(",")]

        data_source_crop_ids = None
        deployment_crop_ids = None
        dataset_id = None
        training_info = None

        training_info = TrainingInfo(dl_config_dict["pretrained_model"], include_points_db=True)

        comparison_model_id = None
        comparison_model_id = pick_comparison_model_id(
            args.comparison_model_id, training_info.comparison_model_id if training_info is not None else None,
        )

        assert comparison_model_id is not None

        dl_config_dict = update_dl_config_dict_for_embedding_balancing(
            dl_config_dict,
            pipeline_id=args.pipeline_id,
            environment=environment,
            sub_type=DeepweedTrainingSubtype.FULL_TRAIN,
            fallback_sampling_algorithm=DATASET_SAMPLING_ALGORITHM_DEFAULT,
        )

        dl_config_dict["comparison_model_id"] = comparison_model_id

        assert training_info is not None
        assert training_info.dataset_id, f"Model {dl_config_dict['pretrained_model']} does not have dataset_id"

        parent_dataset_id = training_info.dataset_id if training_info is not None else None

        dataset_version = update_dataset_version(
            dataset_id=args.dataset_id, parent_dataset_id=parent_dataset_id, dataset_version=dataset_version
        )

        dl_config_dict["dataset_version"] = dataset_version
        dl_config = DeepweedConfig.from_dict(dl_config_dict)

        assert dl_config.sampling_algorithm in [
            DATASET_SAMPLING_ALGORITHM_DEFAULT,
            DATASET_SAMPLING_ALGORITHM_EMBEDDINGS,
        ], f"Configuration error: {dl_config.sampling_algorithm} sampling algorithm is not supported by fine tunes"

        pipeline = get_pipeline(args.pipeline_id)
        data_source_crop_ids = pipeline.data_source_crop_ids
        deployment_crop_ids = pipeline.deployment_crop_ids

        if "disable_crop" in pipeline.custom_arguments:
            args.disable_crop = bool(pipeline.custom_arguments["disable_crop"])
        if args.dataset_id is None:
            val_test_geohashes = None
            test_geohashes = None
            if dl_config.use_geohash:
                assert len(dl_config.emphasized_geohashes) > 0

                # If we're here, we want to use geohash for the fine tune emphasis and limit our test split geohashes to the emphasized set.
                # If new_data_weight is not set, we assume we only care about emphasized data in the validation set, so we can limit the validation split to emphasized geohashes as well.
                # Otherwise, if new_data_weight is set, then we want to make sure to have a mix of emphasized and non-emphasized geos in the validation
                # set so we apply the formula val_oec = emphasized_split_oec * new_data_weight + de-emphasized_split_oec * (1 - new_data_weight), so we shouldn't limit the validation split.
                if dl_config.new_data_weight is None:
                    val_test_geohashes = dl_config.emphasized_geohashes
                else:
                    test_geohashes = dl_config.emphasized_geohashes

            dataset_geohashes = None
            if args.dataset_geohash is not None:
                dataset_geohashes = [args.dataset_geohash]
            if dataset_version == 2:
                dataset_id, _ = get_dataset_v2(
                    pipeline_id=args.pipeline_id,
                    parent_id=parent_dataset_id,
                    geohash=dataset_geohashes,
                    test_geohashes=test_geohashes,
                    val_geohashes=val_test_geohashes,
                    start_timestamp_ms=args.dataset_start_ms,
                    end_timestamp_ms=args.dataset_end_ms,
                )
            elif dataset_version == 1:
                dataset_id, _ = get_dataset(
                    crop_ids=data_source_crop_ids,
                    parent_id=parent_dataset_id,
                    fast_run=args.fast_run,
                    balance_positive_classes=True,
                    balance_robot_ids=dl_config.dataset_balance_robot_ids,
                    balance_geohash=dl_config.dataset_balance_geohash,
                    val_test_crop_ids=deployment_crop_ids,
                    geohash=dataset_geohashes,
                    test_geohashes=test_geohashes,
                    val_test_geohashes=val_test_geohashes,
                    start_timestamp_ms=args.dataset_start_ms,
                    end_timestamp_ms=args.dataset_end_ms,
                )
        else:
            if dataset_version == 2:
                dataset_id, _ = get_dataset_v2(dataset_id=args.dataset_id)
            else:
                dataset_id, _ = get_dataset(dataset_id=args.dataset_id,)

        if args.fast_run:
            # Hackily make some of the data new
            train_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json"
            test_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json"

            if dataset_version == 2:
                train_path += "l"
                test_path += "l"
                with open(train_path, "r") as f:
                    data = [json.loads(line) for line in f]
                for i in range(10):
                    data[i]["is_new"] = True
                for i in range(10, 20):
                    data[i]["is_new"] = False
                with open(train_path, "w") as f:
                    for line in data:
                        json.dump(line, f)
                        f.write("\n")

                with open(test_path, "w") as f:
                    for line in data:
                        json.dump(line, f)
                        f.write("\n")
            else:
                with open(train_path, "r") as f:
                    data2 = json.load(f)
                for i in range(10):
                    data2["images"][i]["is_new"] = True
                for i in range(10, 20):
                    data2["images"][i]["is_new"] = False
                with open(test_path, "w") as f:
                    json.dump(data2, f)
        LOG.info(f"Using dataset: {dataset_id}")

        assert training_info is not None
        assert dataset_id is not None

        segmentation_training_info = None
        for key, value in vars(args).items():
            LOG.info(f"{key}={value}")
        if args.segmentation_model is not None:
            segmentation_training_info = TrainingInfo(args.segmentation_model)

        pretrained_model = training_info.best_model_weights
        pretrained_segmentation_model = training_info.best_model_weights

        if segmentation_training_info is not None:
            pretrained_segmentation_model = segmentation_training_info.best_model_weights

        if args.description is None:
            description = f"(fine tune) Development run {datetime.datetime.now()}"
        else:
            description = args.description

        last_nth_timestamps = {}

        with duration_perf_recorder(f"{PerfCategory.TRAINING}", "dataset_initialization"):
            crops, weeds = get_crop_and_weed_names(dataset_id, dataset_version=dataset_version)
            if args.disable_crop:
                crops = []

            test_segmentation_classes = get_segmentation_classes(training_info)
            if segmentation_training_info is not None:
                test_segmentation_classes = get_segmentation_classes(segmentation_training_info)

            recency_split = None
            recency_split_age = None
            is_new_funcs = []
            if dl_config.use_classical:
                is_new_funcs.append(generate_is_new_default())
            if dl_config.use_robot_ids:
                assert len(dl_config.emphasized_robot_ids) > 0
                is_new_funcs.append(generate_is_new_robot_specific(dl_config.emphasized_robot_ids))
            if dl_config.use_recency:
                recency_split, recency_split_age = find_recency_split(
                    f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json"
                    if not dataset_version == 2
                    else f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.jsonl",
                    dl_config.min_recent_images,
                    dl_config.recency_candidate_h * 60 * 60 * 1000,
                    dataset_version=dataset_version,
                )
                if recency_split is not None:
                    is_new_funcs.append(generate_is_new_recency(recency_split))
                else:
                    is_new_funcs.append(generate_is_new_default())
            if dl_config.use_geohash:
                assert len(dl_config.emphasized_geohashes) > 0
                is_new_funcs.append(generate_is_in_geohashes(dl_config.emphasized_geohashes))

            def is_new_func(datapoint: Dict[str, Any]) -> bool:
                res = True
                for func in is_new_funcs:
                    res = res and func(datapoint)
                return res

            dataset_parameters = {
                "config": dl_config,
                "train_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json",
                "validation_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/validation.json",
                "test_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json",
                "num_samples": dl_config.num_samples,
                "crop_classes": tuple(crops),
                "weed_classes": tuple(weeds),
                "segm_classes": None,
                "test_segm_classes": tuple(test_segmentation_classes)
                if test_segmentation_classes is not None
                else None,
                "train_ppi": 200,
                "goal_percentage_new": dl_config.goal_percentage_new,
                "new_loss_multiplier": args.new_loss_multiplier,
                "positive_sample_percentage": args.positive_sample_percentage,
                "recency_split": recency_split,
                "recency_split_age": recency_split_age,
                "is_new_func": is_new_func if not args.fast_run else generate_is_new_default(),
                "geohash_max_precision": args.geohash_max_precision,
                "geohash_min_precision": args.geohash_min_precision,
                "use_date_groups": args.use_date_groups,
                "calibration_dataset_size": 16 if args.fast_run else 1024,
                "embedding_balancing_evaluation_path": f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dl_config.embedding_balancing_model}/image_point_embeddings"
                if dl_config.embedding_balancing_model is not None
                else None,
            }

            if dataset_version == 2:
                dataset_parameters["train_filepath"] += "l"
                dataset_parameters["validation_filepath"] += "l"
                dataset_parameters["test_filepath"] += "l"

            if args.simulate_sampling:
                LOG.info("Running dataset sampling simulation")
                LOG.info(f"model_id={experiment_id}")

                sampling_params = copy.deepcopy(dataset_parameters)
                sampling_params["config_dict"] = sampling_params["config"].to_json()
                del sampling_params["config"]

                train_dataset, _, _, _ = initialize_datasets(**sampling_params)
                simulate_sampling(
                    train_dataset,
                    number_samples=dl_config.num_samples,
                    epochs=dl_config.num_epochs,
                    dir=f"{CARBON_DATA_DIR}/deeplearning/models/{experiment_id}",
                )

                if os.path.exists(f"/data/deeplearning/models/{experiment_id}"):
                    upload_directory(f"/data/deeplearning/models/{experiment_id}")

                return

            tags: List[str] = ["fine-tuning", *crops]

            dw_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/{experiment_id}")
            logger = make_wandb_logger(
                name=description, tags=safe_split(tags), project=dl_config.wandb_project, exp_dir=dw_experiment_dir,
            )
            veselka_dataset = VeselkaDataset(**dataset_parameters)

            if dl_config.train_embeddings and dl_config.evaluate_new_comparison_data:
                assert comparison_model_id is not None
                elastic_launcher(
                    args.nproc_per_node,
                    evaluate_comparison_embeddings,
                    get_upload_model_fn(experiment_id),
                    EvaluateComparisonEmbeddingsArguments(
                        comparison_model_id=comparison_model_id,
                        environment=environment,
                        dataset_id=dataset_id,
                        fast_run=args.fast_run,
                        dataset_version=dataset_version,
                        eval_files=["train.jsonl"] if dataset_version == 2 else ["train.json"],
                    ),
                )

            if dl_config.evaluate_embeddings_for_new_points:
                elastic_launcher(
                    args.nproc_per_node,
                    evaluate_embeddings_on_dataset_id,
                    get_upload_model_fn(experiment_id),
                    EvaluateDeepweedEmbeddingsOnDatasetArguments(
                        embedding_balancing_model=dl_config.embedding_balancing_model,
                        dataset_id=dataset_id,
                        dl_config_dict=dl_config_dict,
                        fast_run=args.fast_run,
                        crops=crops,
                        weeds=weeds,
                    ),
                )

            num_new_images = len(veselka_dataset._datasets.get_training().new_data_captured_ats)
            if num_new_images < MIN_TRAINING_IMAGES:
                LOG.info(
                    f"Not enough new training images, will exit gracefully: {num_new_images} < {MIN_TRAINING_IMAGES}"
                )
                return

            last_nth_timestamps = get_last_nth_timestamps(veselka_dataset._datasets.new_data_captured_ats)

            if args.tags is not None:
                tags += args.tags

            tags.append("pretrained")
            config = {}
            tags.append("veselka-dataset-used")
            config["parent_model_id"] = dl_config.pretrained_model
            config["parent_segmentation_model_id"] = args.segmentation_model

            sub_type = (
                DeepweedTrainingSubtype.GEO_FINE_TUNE.name.lower()
                if len(dl_config.emphasized_geohashes)
                else DeepweedTrainingSubtype.FINE_TUNE.name.lower()
            )

            with duration_perf_recorder(f"{PerfCategory.TRAINING}", "train"):
                elastic_launcher(
                    args.nproc_per_node,
                    train_and_unoptimized_test,
                    get_upload_model_fn(experiment_id),
                    TrainAndUnoptimizedTestArguments(
                        dl_config=dl_config,
                        datasets=veselka_dataset.datasets,
                        pretrained_model=pretrained_model,
                        pretrained_segmentation_model=pretrained_segmentation_model,
                        description=description,
                        environment=environment,
                        tags=tags,
                        resume_from=args.resume_from,
                        model_id=experiment_id,
                        wandb_config=config,
                        positive_sample_milestones=positive_sample_milestones,
                        positive_sample_gamma=args.positive_sample_gamma,
                        dataset_id=dataset_id,
                        last_nth_timestamps=last_nth_timestamps,
                        pipeline_id=args.pipeline_id,
                        data_source_crop_ids=data_source_crop_ids,
                        parent_model_id=dl_config.pretrained_model,
                        new_data_weight=dl_config.new_data_weight,
                        sub_type=sub_type,
                        logger=logger,
                        geohash_prefix=(
                            ",".join(dl_config.emphasized_geohashes)
                            if len(dl_config.emphasized_geohashes) > 0
                            else None
                        ),
                    ),
                )
                pumap_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/pumap-{experiment_id}")

                if dl_config.train_embeddings_pumap:
                    elastic_launcher(
                        args.nproc_per_node,
                        train_pumap,
                        get_upload_model_fn(experiment_id),
                        PumapArguments(
                            dw_experiment_dir=dw_experiment_dir,
                            pumap_experiment_dir=pumap_experiment_dir,
                            dw_config=dl_config,
                            logger=logger,
                        ),
                    )
                    elastic_launcher(
                        args.nproc_per_node,
                        test_pumap,
                        get_upload_model_fn(experiment_id),
                        PumapArguments(
                            dw_experiment_dir=dw_experiment_dir,
                            pumap_experiment_dir=pumap_experiment_dir,
                            dw_config=dl_config,
                        ),
                    )
                    elastic_launcher(
                        args.nproc_per_node,
                        load_pumap_head,
                        get_upload_model_fn(experiment_id),
                        PumapArguments(
                            dw_config=dl_config,
                            dw_experiment_dir=dw_experiment_dir,
                            pumap_experiment_dir=pumap_experiment_dir,
                        ),
                    )

                elastic_launcher(
                    args.nproc_per_node,
                    optimize_and_test,
                    get_upload_model_fn(experiment_id),
                    OptimizeAndTestArguments(
                        dl_config=dl_config,
                        datasets=veselka_dataset.datasets,
                        pretrained_model=pretrained_model,
                        pretrained_segmentation_model=pretrained_segmentation_model,
                        description=description,
                        tags=tags,
                        model_id=experiment_id,
                        wandb_config=config,
                        dataset_id=dataset_id,
                        last_nth_timestamps=last_nth_timestamps,
                        data_source_crop_ids=data_source_crop_ids,
                        parent_model_id=args.pretrained_model,
                        logger=logger,
                    ),
                )
    except Exception as e:
        raise e
    finally:
        cleanup_spawn_script(logger, veselka_dataset)


if __name__ == "__main__":
    run_debuggable(main)

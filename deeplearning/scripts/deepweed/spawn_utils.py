import functools
import logging
import os
from dataclasses import asdict, dataclass, field
from typing import Any, Callable, Dict, List, Optional

import torch
from lightning.pytorch.loggers import WandbLogger

import deeplearning.dl_metrics as dl_metrics
from deeplearning.comparison.config import ComparisonConfig
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.deepweed.remote_veselka_dataset import RemoteVeselkaDatasets, VeselkaDataset
from deeplearning.deepweed.trainer import get_model_embedding_lookup_path
from deeplearning.scripts.comparison.evaluate import run_comparison_evaluate
from deeplearning.scripts.deepweed.evaluate import run_inference_on_file
from deeplearning.scripts.utils.elastic_launcher import elastic_launcher, launcher_error_handler
from deeplearning.scripts.utils.inference_utils import evaluate_embeddings_on_dataset
from deeplearning.scripts.utils.utils import TrainingInfo, remove_files_from_dataset_json
from deeplearning.utils.trainer import Environment, upload_directory
from lib.common.perf.perf_tracker import set_autowrite_filename, set_autowrite_frequency, set_verbosity

set_verbosity(False)

LOG = logging.getLogger(__name__)


def upload_model_fn(model_id: str) -> None:
    if os.path.exists(f"/data/deeplearning/models/{model_id}"):
        upload_directory(f"/data/deeplearning/models/{model_id}")


def get_upload_model_fn(model_id: str) -> Callable[[], None]:
    return functools.partial(upload_model_fn, model_id)


def cleanup_spawn_script(
    logger: Optional[WandbLogger] = None, veselka_dataset: Optional[VeselkaDataset] = None
) -> None:
    if logger is not None:
        logger.finalize("success")
    if veselka_dataset is not None:
        veselka_dataset.shutdown()


def evaluate_embeddings_for_new_points(
    dl_config: DeepweedConfig, model_id: str, dataset_id: str, nproc_per_node: int, crops: List[str], weeds: List[str],
) -> None:
    assert dl_config.embedding_balancing_model is not None, "Embedding model needs to be set."

    embedding_training_info = TrainingInfo(dl_config.embedding_balancing_model)
    eval_dir = f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dl_config.embedding_balancing_model}"
    if os.path.exists(os.path.join(eval_dir, "test_dataframes/points_v2.db")):
        os.remove(os.path.join(eval_dir, "test_dataframes/points_v2.db"))

    old_files = set()

    for _, _, files in os.walk(eval_dir):
        for file in files:
            if file.endswith(".pt"):
                old_files.add(os.path.splitext(file)[0])

    # Remove files if they already exist in the embeddings directory
    embedding_model_eval_filepath, new_number_images = remove_files_from_dataset_json(
        f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json", old_files, "id"
    )

    LOG.info(
        f"Skipping {len(old_files)} images from embedding extraction (this leaves {new_number_images} to evaluate)."
    )

    eval_config_dict = {
        "model_id": dl_config.embedding_balancing_model,
        "enable_crop_embeddings": embedding_training_info.dl_config.get("enableCropEmbeddings", False),
        **asdict(dl_config),
    }
    if "embedding_balancing_model" in eval_config_dict:
        del eval_config_dict["embedding_balancing_model"]

    if dl_config.evaluate_full_embeddings:
        eval_config_dict["save_full_embeddings_from_trt_model"] = True

    eval_config = DeepweedConfig.from_dict(eval_config_dict)

    elastic_launcher(
        nproc_per_node,
        evaluate_embeddings_on_new_data,
        get_upload_model_fn(model_id),
        EvaluateDeepweedEmbeddingsArguments(
            eval_config=eval_config,
            eval_json=embedding_model_eval_filepath,
            crops=crops,
            weeds=weeds,
            embedding_training_info_obj=embedding_training_info,
            eval_dir=eval_dir,
            dataset_id=dataset_id,
        ),
    )


@dataclass
class EvaluateComparisonEmbeddingsArguments:
    comparison_model_id: str
    environment: Environment
    dataset_id: str
    fast_run: bool
    eval_files: List[str] = field(
        default_factory=lambda: ["train.json"]
    )  # Using this instead of [] to insure eval_files gets a new, independent list.
    eval_dir: Optional[str] = None
    only_evaluate_new_data: bool = True
    save_image_point_embeddings: bool = True
    save_hdf5_embeddings: bool = False
    dataset_version: int = 1


@dataclass
class EvaluateDeepweedEmbeddingsArguments:
    eval_config: DeepweedConfig
    eval_json: str
    crops: List[str]
    weeds: List[str]
    embedding_training_info_obj: TrainingInfo
    eval_dir: str
    dataset_id: str
    save_image_point_embeddings: bool = True
    save_hdf5_embeddings: bool = False


@dataclass
class TrainAndUnoptimizedTestArguments:
    dl_config: DeepweedConfig
    datasets: RemoteVeselkaDatasets
    description: str
    tags: List[str]
    model_id: str
    resume_from: Optional[str]
    environment: Environment
    dataset_id: str
    wandb_config: Dict[str, Any]
    sub_type: str
    parent_model_id: Optional[str] = None
    frozen_backbone_point_layers: Optional[List[str]] = None
    last_nth_timestamps: Optional[Dict[int, DatapointTimestamps]] = None
    logger: Optional[WandbLogger] = None
    pretrained_model: Optional[str] = None
    pretrained_segmentation_model: Optional[str] = None
    positive_sample_milestones: Optional[List[int]] = None
    positive_sample_gamma: float = 1.0
    new_data_weight: Optional[float] = None
    geohash_prefix: Optional[str] = None
    pipeline_id: Optional[str] = None
    data_source_crop_ids: Optional[List[str]] = None


@dataclass
class PumapArguments:
    dw_experiment_dir: str
    pumap_experiment_dir: str
    dw_config: DeepweedConfig
    logger: Optional[WandbLogger] = None


@dataclass
class OptimizeAndTestArguments:
    dl_config: DeepweedConfig
    datasets: RemoteVeselkaDatasets
    description: str
    tags: List[str]
    model_id: str
    dataset_id: str
    wandb_config: Dict[str, Any]
    last_nth_timestamps: Optional[Dict[int, DatapointTimestamps]] = None
    logger: Optional[WandbLogger] = None
    pretrained_model: Optional[str] = None
    pretrained_segmentation_model: Optional[str] = None
    parent_model_id: Optional[str] = None
    data_source_crop_ids: Optional[List[str]] = None


@launcher_error_handler
def evaluate_comparison_embeddings(arguments: EvaluateComparisonEmbeddingsArguments,) -> None:
    from deeplearning.comparison.trainer import ComparisonTrainer

    with ComparisonTrainer() as comp_trainer:
        assert arguments.comparison_model_id is not None
        run_comparison_evaluate(
            trainer=comp_trainer,
            environment=arguments.environment,
            dl_config=ComparisonConfig.from_dict({}),
            job_id=None,
            model_id=arguments.comparison_model_id,
            embedding_output_path=arguments.eval_dir,
            comparison_db_output_path=arguments.eval_dir,
            dataset_id=arguments.dataset_id,
            fast_run=arguments.fast_run,
            eval_files=arguments.eval_files,
            only_evaluate_new_data=arguments.only_evaluate_new_data,
            save_image_point_embeddings=arguments.save_image_point_embeddings,
            save_hdf5_embeddings=arguments.save_hdf5_embeddings,
            dataset_version=arguments.dataset_version,
        )


@launcher_error_handler
def evaluate_embeddings_on_new_data(arguments: EvaluateDeepweedEmbeddingsArguments,) -> None:
    from deeplearning.deepweed.trainer import DeepweedTrainer

    with DeepweedTrainer() as trainer:
        run_inference_on_file(
            trainer=trainer,
            dl_config=arguments.eval_config,
            eval_json=arguments.eval_json,
            crops=arguments.crops,
            weeds=arguments.weeds,
            segmentation_classes=None,
            training_info=arguments.embedding_training_info_obj,
            eval_dir=arguments.eval_dir,
            save_image_point_embeddings=arguments.save_image_point_embeddings,
            save_hdf5_embeddings=arguments.save_hdf5_embeddings,
            dataset_id=arguments.dataset_id,
        )


@dataclass
class EvaluateDeepweedEmbeddingsOnDatasetArguments:
    embedding_balancing_model: str
    dataset_id: str
    crops: List[str]
    weeds: List[str]
    fast_run: bool
    dl_config_dict: Dict[str, Any]
    save_image_point_embeddings: bool = True
    save_hdf5_embeddings: bool = False


@launcher_error_handler
def evaluate_embeddings_on_dataset_id(arguments: EvaluateDeepweedEmbeddingsOnDatasetArguments,) -> None:
    embedding_balancing_model = arguments.embedding_balancing_model
    dataset_id = arguments.dataset_id
    dl_config_dict = arguments.dl_config_dict
    fast_run = arguments.fast_run
    crops = arguments.crops
    weeds = arguments.weeds

    evaluate_embeddings_on_dataset(
        embedding_balancing_model=embedding_balancing_model,
        dataset_id=dataset_id,
        dl_config_dict=dl_config_dict,
        fast_run=fast_run,
        crops=crops,
        weeds=weeds,
        save_image_point_embeddings=arguments.save_image_point_embeddings,
        save_hdf5_embeddings=arguments.save_hdf5_embeddings,
    )


@launcher_error_handler
def train_and_unoptimized_test(arguments: TrainAndUnoptimizedTestArguments) -> None:
    from deeplearning.deepweed.trainer import DeepweedTrainer

    with DeepweedTrainer() as trainer:

        data_dir = CARBON_DATA_DIR
        model_id = arguments.dl_config.model_id
        rank = torch.distributed.get_rank()
        pid = os.getpid()

        set_autowrite_filename(f"{data_dir}/deeplearning/models/{model_id}/timing/trainer_{rank}_{pid}")
        set_autowrite_frequency(60000)

        trainer.train_and_unoptimized_test(
            config=arguments.dl_config,
            datasets=arguments.datasets,
            pretrained_model=arguments.pretrained_model,
            pretrained_segmentation_model=arguments.pretrained_segmentation_model,
            description=arguments.description,
            tags=arguments.tags,
            checkpoint_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{arguments.model_id}",
            resume_from=arguments.resume_from,
            deploy=arguments.environment == Environment.PRODUCTION,
            environment=arguments.environment,
            additional_wandb_config=arguments.wandb_config,
            frozen_backbone_point_layers=arguments.frozen_backbone_point_layers,
            dataset_id=arguments.dataset_id,
            sub_type=arguments.sub_type,
            last_nth_timestamps=arguments.last_nth_timestamps,
            pipeline_id=arguments.pipeline_id,
            crop_ids=arguments.data_source_crop_ids,
            parent_model_id=arguments.parent_model_id,
            train_log_image_p=0.01 if arguments.environment == Environment.DEVELOPMENT else 0,
            val_log_image_p=0.01 if arguments.environment == Environment.DEVELOPMENT else 0,
            checkpoint_start_pct=int(arguments.dl_config.checkpoint_start_ratio * 100),
            logger=arguments.logger if torch.distributed.get_rank() == 0 else None,
            new_data_weight=arguments.new_data_weight,
            geohash_prefix=arguments.geohash_prefix,
        )


@launcher_error_handler
def train_pumap(arguments: PumapArguments) -> None:
    from deeplearning.parametric_umap.trainer import PUMAPTrainer

    with PUMAPTrainer() as trainer:
        trainer.dataset(
            os.path.join(arguments.dw_experiment_dir, "test_unoptimized_dataframes/points_v2.db"),
            embedding_hdf5_file=get_model_embedding_lookup_path(
                arguments.dw_experiment_dir, "test_unoptimized_", embedding_type=dl_metrics.EmbeddingType.FULL
            ),
            only_test=False,
            fast_run=arguments.dw_config.fast_run,
        )
        trainer.train(
            lr=1e-1 * torch.distributed.get_world_size(),
            epochs=2 if arguments.dw_config.fast_run else 20,
            lr_milestones=[5, 10, 15, 20],
            lr_gamma=0.1,
            checkpoint_dir=arguments.pumap_experiment_dir,
            log_experiment=False,
            fast_run=arguments.dw_config.fast_run,
            logger=arguments.logger if torch.distributed.get_rank() == 0 else None,
        )


@launcher_error_handler
def test_pumap(arguments: PumapArguments) -> None:
    from deeplearning.parametric_umap.trainer import PUMAPTrainer

    with PUMAPTrainer() as trainer:
        best_pumap_checkpoint_file = os.path.join(arguments.pumap_experiment_dir, "best_checkpoint.ckpt")
        trainer.dataset(
            os.path.join(arguments.dw_experiment_dir, "test_unoptimized_dataframes/points_v2.db"),
            embedding_hdf5_file=get_model_embedding_lookup_path(
                arguments.dw_experiment_dir, "test_unoptimized_", embedding_type=dl_metrics.EmbeddingType.FULL
            ),
            only_test=True,
            fast_run=arguments.dw_config.fast_run,
        )
        trainer.infer(
            checkpoint_file=best_pumap_checkpoint_file,
            points_db=os.path.join(arguments.dw_experiment_dir, "test_unoptimized_dataframes/points_v2.db"),
            logger=None,
            fast_run=arguments.dw_config.fast_run,
        )


@launcher_error_handler
def load_pumap_head(arguments: PumapArguments) -> None:
    from deeplearning.deepweed.trainer import DeepweedTrainer

    best_pumap_checkpoint_file = os.path.join(arguments.pumap_experiment_dir, "best_checkpoint.ckpt")
    best_dw_checkpoint_file = os.path.join(arguments.dw_experiment_dir, "best_checkpoint.ckpt")
    with DeepweedTrainer() as trainer:
        trainer.load_pumap_head(arguments.dw_config, best_dw_checkpoint_file, best_pumap_checkpoint_file)


@launcher_error_handler
def optimize_and_test(arguments: OptimizeAndTestArguments) -> None:
    from deeplearning.deepweed.trainer import DeepweedTrainer

    with DeepweedTrainer() as trainer:
        trainer.optimize_and_test(
            config=arguments.dl_config,
            datasets=arguments.datasets,
            pretrained_model=arguments.pretrained_model,
            pretrained_segmentation_model=arguments.pretrained_segmentation_model,
            description=arguments.description,
            tags=arguments.tags,
            checkpoint_dir=f"/data/deeplearning/models/{arguments.model_id}",
            additional_wandb_config=arguments.wandb_config,
            dataset_id=arguments.dataset_id,
            last_nth_timestamps=arguments.last_nth_timestamps,
            crop_ids=arguments.data_source_crop_ids,
            parent_model_id=arguments.parent_model_id,
            checkpoint_start_pct=int(arguments.dl_config.checkpoint_start_ratio * 100),
            logger=arguments.logger if torch.distributed.get_rank() == 0 else None,
        )

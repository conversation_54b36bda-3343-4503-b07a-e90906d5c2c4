import argparse
import datetime
import logging
import os
from typing import Any, Dict, Union

import torch
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.constants import CARBON_DATA_DIR, DeepweedTrainingSubtype
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import DATASET_SAMPLING_ALGORITHM_DRIPTAPE
from deeplearning.deepweed.remote_veselka_dataset import initialize_datasets
from deeplearning.deepweed.sampling_visualization import simulate_sampling
from deeplearning.deepweed.trainer import DeepweedTrainer
from deeplearning.scripts.utils.utils import (
    EXCLUDE_CROP_IDS_DRIPTAPE,
    add_common_arguments,
    generate_model_id,
    get_dataset,
    get_dataset_driptape_count,
    get_dataset_v2,
    get_project_name,
    run_debuggable,
    update_dataset_version,
)
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.trainer import Environment, upload_directory
from lib.common.veselka.client import DatasetV2

LOG = logging.getLogger(__name__)

TRAIN_LIMIT = 200000
TEST_LIMIT = 30000
VALIDATION_LIMIT = 10000


@record
def main() -> None:  # noqa: C901

    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--balance-positive-classes", action="store_true", help="Balance positive classes in dataset generation"
    )
    parser.add_argument(
        "--balance-geohash", type=int, default=4, help="Balance geohash at given precision in dataset generation"
    )
    parser.add_argument("--dataset-start-ms", type=int, default=None)
    parser.add_argument("--dataset-end-ms", type=int, default=None)
    parser.add_argument("--simulate-sampling", action="store_true", default=False)
    parser.add_argument("--geohash-max-precision", type=int, default=6)
    parser.add_argument("--geohash-min-precision", type=int, default=6)
    parser.add_argument("--positive-sample-percentage", type=float, default=0.5)

    add_common_arguments(parser)
    parser.set_defaults(balance_positive_classes=True)

    args = parser.parse_args()

    if args.job_id is None:
        model_id = generate_model_id()
    else:
        model_id = args.job_id

    dl_config_dict = {
        "wandb_project": get_project_name(args.fast_run),
        "num_samples": 64 if args.fast_run else 30000,
        "num_epochs": 1 if args.fast_run else 40,
        "gradient_checkpoint": False,
        "checkpoint_start_ratio": 0.33,
        "num_workers": 2,
        "fast_run": args.fast_run,
        "sampling_algorithm": DATASET_SAMPLING_ALGORITHM_DRIPTAPE,
        "train_embeddings": False,
        "model_id": model_id,
        "save_embeddings_to_points_db": False,
        "validation_set_size_limit": VALIDATION_LIMIT,
        "test_set_size_limit": TEST_LIMIT,
        "train_set_size_limit": None
        if args.fast_run
        else TRAIN_LIMIT,  # Fast run already does a lot of sampling to make a 64 length dataset, so don't pass TRAIN_LIMIT so we don't rebalance non-driptape images
        **args.dl_config,
    }

    if "dataset_version" not in dl_config_dict:
        dl_config_dict["dataset_version"] = 2

    if not args.pipeline_id:
        args.pipeline_id = "110261ef-8641-4b92-84ea-fb4f51ba0305"

    dataset_version = dl_config_dict["dataset_version"]

    if args.fast_run and dataset_version == 2:
        dl_config_dict["train_set_size_limit"] = 2000
        dl_config_dict["validation_set_size_limit"] = 2000
        dl_config_dict["test_set_size_limit"] = 2000

    tags = []

    if args.tags is not None:
        tags += args.tags

    if args.fast_run:
        args.develop = True

    # NOTE: torch.distributed is initialized with the DeepweedTrainer __init__ method. We need to instantiate it here
    # to get the communication between all the processes going.
    with DeepweedTrainer() as trainer:
        if torch.distributed.get_rank() == 0:
            LOG.info(f"Model ID = {model_id}")

        try:
            dataset_id = None
            dataset_info: Union[DatasetV2, Dict[str, Any]] = {}

            dataset_version = update_dataset_version(dataset_id=args.dataset_id, dataset_version=dataset_version)

            dl_config_dict["dataset_version"] = dataset_version

            dl_config = DeepweedConfig.from_dict(dl_config_dict)

            if torch.distributed.get_rank() == 0:
                for key, value in vars(args).items():
                    LOG.info(f"{key}={value}")

                LOG.info("Starting dataset creation and download")
                if dataset_version == 2:
                    dataset_id, dataset_info = get_dataset_v2(
                        dataset_id=args.dataset_id,
                        pipeline_id=args.pipeline_id,
                        exclude_crop_ids=EXCLUDE_CROP_IDS_DRIPTAPE,
                        start_timestamp_ms=args.dataset_start_ms,
                        end_timestamp_ms=args.dataset_end_ms,
                        train_set_size_limit=dl_config.train_set_size_limit,
                        validation_set_size_limit=dl_config.validation_set_size_limit,
                        test_set_size_limit=dl_config.test_set_size_limit,
                        train_driptape_percentage=dl_config.train_driptape_percentage,
                        val_driptape_percentage=dl_config.val_driptape_percentage,
                        test_driptape_percentage=dl_config.test_driptape_percentage,
                    )
                if dataset_version == 1:
                    dataset_id, dataset_info = get_dataset(
                        dataset_id=args.dataset_id,
                        fast_run=args.fast_run,
                        balance_positive_classes=args.balance_positive_classes,
                        balance_geohash=args.balance_geohash,
                        exclude_crop_ids=EXCLUDE_CROP_IDS_DRIPTAPE,
                        driptape=True,
                        start_timestamp_ms=args.dataset_start_ms,
                        end_timestamp_ms=args.dataset_end_ms,
                        train_set_size_limit=dl_config.train_set_size_limit,
                        train_no_driptape_keep_multiplier=(
                            None if args.fast_run else dl_config.train_no_driptape_keep_multiplier
                        ),
                        val_no_driptape_keep_multiplier=dl_config.val_no_driptape_keep_multiplier,
                        test_no_driptape_keep_multiplier=dl_config.test_no_driptape_keep_multiplier,
                    )
                LOG.info(f"dataset_created. dataset_id={dataset_id}")

            torch.distributed.barrier()
            LOG.info(dataset_info)
            dataset_id = broadcast_object(dataset_id)
            dl_config = broadcast_object(dl_config)
            assert dataset_id is not None
            assert dl_config is not None

            dataset_version = dl_config.dataset_version

            train_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json"
            val_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/validation.json"
            test_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json"

            if dataset_version == 2:  # When we move to exclusively dataset V2, these can be no longer hardcoded
                train_path += "l"
                val_path += "l"
                test_path += "l"

            driptape_count = get_dataset_driptape_count(train_path)

            if args.description is None:
                description = f"(driptape) Development run {datetime.datetime.now()}"
            else:
                description = args.description

            assert driptape_count > 32 or (
                args.fast_run and driptape_count > 1
            ), f"Not enough driptape: {driptape_count}"
            segmentation_classes = ("DRIPTAPE",)
            tags.append("driptape")

            dataset_parameters = {
                "config": dl_config,
                "train_filepath": train_path,
                "validation_filepath": val_path,
                "test_filepath": test_path,
                "num_samples": dl_config.num_samples,
                "segm_classes": segmentation_classes,
                "test_segm_classes": segmentation_classes,
                "train_ppi": 200,
                "dilate_mask": 50,
                "positive_sample_percentage": args.positive_sample_percentage,
                "calibration_dataset_size": 16 if args.fast_run else 1024,
                "geohash_max_precision": args.geohash_max_precision,
                "geohash_min_precision": args.geohash_min_precision,
            }

            if args.simulate_sampling:
                if torch.distributed.get_rank() == 0:
                    LOG.info(f"model_id={model_id}")
                    train_dataset, _, _, _ = initialize_datasets(**dataset_parameters)
                    simulate_sampling(
                        train_dataset,
                        number_samples=dl_config.num_samples,
                        epochs=dl_config.num_epochs,
                        dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
                    )

                    if torch.distributed.get_rank() == 0 and os.path.exists(f"/data/deeplearning/models/{model_id}"):
                        upload_directory(f"/data/deeplearning/models/{model_id}")

                return

            trainer.veselka_dataset(**dataset_parameters)

            environment = Environment.DEVELOPMENT
            if args.preview:
                environment = Environment.PREVIEW
            elif args.production:
                environment = Environment.PRODUCTION

            if args.fast_run:
                environment = Environment.DEVELOPMENT

            trainer.train(
                config=dl_config,
                tags=tags,
                checkpoint_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
                resume_from=args.resume_from,
                description=description,
                checkpoint_start_pct=int(dl_config.checkpoint_start_ratio * 100),
                deploy=environment == Environment.PRODUCTION,
                environment=environment,
                dataset_id=dataset_id,
                sub_type=DeepweedTrainingSubtype.DRIPTAPE.name.lower(),
                train_log_image_p=0.01 if environment == Environment.DEVELOPMENT else 0,
                val_log_image_p=0.01 if environment == Environment.DEVELOPMENT else 0,
                pipeline_id=args.pipeline_id,
            )

        except Exception as e:
            LOG.info(f"Exception running job on rank {torch.distributed.get_rank()}: {e}")

            if torch.distributed.get_rank() == 0 and os.path.exists(f"/data/deeplearning/models/{model_id}"):
                upload_directory(f"/data/deeplearning/models/{model_id}")

            raise e


if __name__ == "__main__":
    run_debuggable(main)

import argparse
import json
import logging
import os
import shutil
import sys
import time

LOG = logging.getLogger(__name__)


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    parser.add_argument("--pipeline-id", type=str, required=True)
    parser.add_argument("--fixed-crop-id", type=str, default=None)
    parser.add_argument("--dataset-id", type=str, default=None)

    # Job creator arguments
    parser.add_argument("--production", action="store_true", dest="production")
    parser.add_argument("--preview", action="store_true", dest="preview")
    parser.add_argument("--job-id", type=str, default=None)
    parser.add_argument("--dl-config", type=json.loads, default={})
    parser.add_argument("--description", type=str, default=None)
    parser.set_defaults(production=False, preview=False)

    args, _ = parser.parse_known_args()

    sys.path = [x for x in sys.path if not x.startswith("/opt/TensorRT")]
    sys.path.insert(0, "/opt/TensorRT-*******/python/site-packages")

    # only load deeplearning code after updating sys.path
    from deeplearning.deepweed.trt_convert import TrtConvert
    from deeplearning.scripts.utils.utils import TrainingInfo, generate_model_id
    from deeplearning.constants import DeepweedTrainingSubtype
    from deeplearning.utils.trainer import (
        S3_BUCKET,
        compute_md5sum,
        get_compute_capability,
        get_tensorrt_version,
        upload_directory,
        get_tensorrt_file_name,
    )
    from lib.common.veselka.client import VeselkaClient

    parent_model_id = args.model_id

    training_info = TrainingInfo(parent_model_id)
    deepweed_config = training_info.deepweed_config
    assert deepweed_config is not None, "Deepweed DL config is missing"
    assert deepweed_config.enable_crop_embeddings, "Parent model is not a crop embedding model"
    trt_file_name = get_tensorrt_file_name(deepweed_config.convert_int8)

    model_id = args.job_id if args.job_id is not None else generate_model_id()
    LOG.info(f"New model ID: {model_id}")
    veselka_client = VeselkaClient()

    if args.fixed_crop_id is None:
        pipeline = veselka_client.get_pipeline(args.pipeline_id)
        args.fixed_crop_id = pipeline.deployment_crop_ids[0]

    dataset_id = args.dataset_id
    if dataset_id is None:
        dataset_id = veselka_client.create_deepweed_dataset_v2(
            pipeline_id=args.pipeline_id, crop_ids=[args.fixed_crop_id], parent_dataset_id=training_info.dataset_id,
        )

        info = veselka_client.get_dataset_v2(dataset_id)
        assert info is not None, "Dataset creation failed"
        veselka_client.download_dataset_v2(info)

    stub_json = {"id": model_id}
    VeselkaClient().post_model(stub_json)

    data_dir = os.path.join(training_info.data_dir.split(parent_model_id)[0], model_id)
    save_to = os.path.join(data_dir, trt_file_name)

    os.makedirs(data_dir, exist_ok=True)
    shutil.copy(training_info.best_model_weights, data_dir)

    trt_converter = TrtConvert()
    trt_converter.load_model(training_info.best_model_weights, deepweed_config, args.fixed_crop_id)
    trt_converter.load_datasets(dataset_id)
    test_results = trt_converter.convert(
        max_batch_size=deepweed_config.evaluation_batch_size,
        save_to=save_to,
        int8=deepweed_config.convert_int8,
        fp16=deepweed_config.convert_fp16,
        calibration_batch_size=16,
        calibration_only=False,
        error_metrics=True,
        config=deepweed_config,
    )

    out_config = deepweed_config.to_dict()
    out_config["enable_crop_embeddings"] = False

    veselka_metadata = training_info._veselka_metadata
    veselka_metadata["dl_config"] = out_config
    veselka_metadata["dataset_id"] = dataset_id
    veselka_metadata["crop_ids"] = [args.fixed_crop_id]
    veselka_metadata["url"] = f"s3://{S3_BUCKET}/models/{model_id}/{trt_file_name}"

    model_metadata = training_info.model_metadata
    model_metadata["crop_embeddings"] = False
    model_metadata["crop_ids"] = [args.fixed_crop_id]
    veselka_metadata["metadata_json"] = json.dumps(model_metadata)

    with open(os.path.join(data_dir, "veselka-metadata.json"), "w") as f:
        json.dump(veselka_metadata, f)

    upload_directory(data_dir)

    model_json = {
        "id": model_id,
        "trained_at": int(time.time()),
        "url": f"s3://{S3_BUCKET}/models/{model_id}/{trt_file_name}",
        "dl_config": out_config,
        "tensorrt_version": None,
        "compute_capability": get_compute_capability(),
        "pipeline_id": args.pipeline_id,
        "metadata_json": json.dumps(model_metadata),
        "is_good_to_deploy": False,
        "is_pretraining": False,
        "dataset_id": dataset_id,
        "sub_type": DeepweedTrainingSubtype.FULL_TRAIN.name.lower(),
        "checksum": compute_md5sum(save_to),
    }
    VeselkaClient().post_model(model_json)

    veselka_client = VeselkaClient()
    veselka_client.post_model_artifact(
        model_id=model_id,
        tensorrt_version=get_tensorrt_version(),
        compute_capability=get_compute_capability(),
        url=f"s3://{S3_BUCKET}/models/{model_id}/{trt_file_name}",
        checksum=compute_md5sum(save_to),
        test_results=test_results,
    )

    LOG.info(f"Successfully converted model: {model_id}")


if __name__ == "__main__":
    main()

import argparse
import logging
import os
import uuid
from dataclasses import asdict
from typing import Any, Dict, Optional, cast

import torch

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateDeepweedEmbeddingsArguments,
    evaluate_embeddings_on_new_data,
)
from deeplearning.scripts.embeddings.utils import get_local_embedding_filepath, upload_embedding_hdf5
from deeplearning.scripts.utils.elastic_launcher import default, elastic_launcher
from deeplearning.scripts.utils.training_info import TrainingInfo
from deeplearning.scripts.utils.utils import (
    add_common_arguments,
    get_crop_and_weed_names,
    get_dataset,
    get_dataset_v2,
    get_pipeline,
)

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")


def add_args(parser: argparse.ArgumentParser) -> None:
    parser.add_argument("--model-id", type=str, required=True)
    parser.add_argument("--generate-new-dataset", action="store_true", default=False)
    parser.add_argument("--save-reduce-scaled", action="store_true", default=False)
    parser.add_argument("--dataset-version", type=int, default=2, help="Dataset version to use, 1 or 2")
    parser.add_argument("--version-id", type=str, default=None, help="Name of embedding dataset version")


def build_config(
    model_id: str,
    fast_run: bool = False,
    save_reduce_scaled: bool = False,
    dl_config: Optional[Dict[str, Any]] = None,
    dataset_version: int = 1,
) -> DeepweedConfig:
    dl_config = dl_config if dl_config is not None else {}
    eval_config_dict = {
        "model_id": model_id,
        "fast_run": fast_run,
        "save_full_embeddings_from_trt_model": not save_reduce_scaled,
        "save_embeddings_to_points_db": True,
        "dataset_version": dataset_version,
        **dl_config,
    }
    if "embedding_balancing_model" in eval_config_dict:
        del eval_config_dict["embedding_balancing_model"]
    eval_config = DeepweedConfig.from_dict(eval_config_dict)
    return cast(DeepweedConfig, eval_config)


def main() -> None:
    parser = argparse.ArgumentParser()
    add_args(parser)
    add_common_arguments(parser)

    parser.set_defaults(fast_run=False)

    args = parser.parse_args()

    eval_config = build_config(
        model_id=args.model_id,
        fast_run=args.fast_run,
        save_reduce_scaled=args.save_reduce_scaled,
        dl_config=args.dl_config,
        dataset_version=args.dataset_version,
    )

    run_embed(
        model_id=args.model_id,
        eval_config=eval_config,
        dataset_id=args.dataset_id,
        generate_new_dataset=args.generate_new_dataset,
        nproc_per_node=args.nproc_per_node,
        save_reduce_scaled=args.save_reduce_scaled,
        version_name=args.version_id,
    )


def run_embed(
    model_id: str,
    eval_config: DeepweedConfig,
    dataset_id: Optional[str] = None,
    nproc_per_node: int = torch.cuda.device_count(),
    generate_new_dataset: bool = False,
    save_reduce_scaled: bool = False,
    version_name: Optional[str] = None,
) -> str:
    training_info = TrainingInfo(model_id)

    geohashes = None
    if training_info.emphasized_geohashes is not None:
        geohashes = training_info.emphasized_geohashes

    if generate_new_dataset:
        dataset_crops = None
        deployment_crop_ids = get_pipeline(training_info.pipeline_id).deployment_crop_ids
        if eval_config.dataset_version == 1:
            dataset_id, _ = get_dataset(
                crops=dataset_crops,
                crop_ids=deployment_crop_ids,
                parent_id=training_info.dataset_id,
                fast_run=eval_config.fast_run,
                evaluation=True,
                only_new_data=False,
                geohash=geohashes,
            )
        else:
            dataset_id, _ = get_dataset_v2(pipeline_id=training_info.pipeline_id, geohash=geohashes,)
    elif dataset_id is not None:
        LOG.info(f"Using custom dataset: {dataset_id}")
        if eval_config.dataset_version == 1:
            dataset_id, _ = get_dataset(dataset_id=dataset_id)
        else:
            dataset_id, _ = get_dataset_v2(dataset_id=dataset_id)
        training_info.dataset_id = dataset_id
    else:
        LOG.info(f"Using model dataset: {training_info.dataset_id}")
        if eval_config.dataset_version == 1:
            dataset_id, _ = get_dataset(dataset_id=training_info.dataset_id)
        else:
            dataset_id, _ = get_dataset_v2(dataset_id=training_info.dataset_id)
    try:
        crops, weeds = get_crop_and_weed_names(dataset_id, dataset_version=eval_config.dataset_version)
    except FileNotFoundError as e:
        LOG.info("Dataset not found - check dataset version?")
        raise e

    if version_name is not None:
        assert not os.path.exists(
            f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dataset_id}/{model_id}/datasets/{version_name}"
        ), f"Embedding dataset version {version_name} already exists"

    eval_dir = f"{CARBON_DATA_DIR}/deeplearning/embedding_hdf5/{uuid.uuid4()}/{model_id}"
    LOG.info(f"Evaluation directory {eval_dir}")

    eval_json = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json"
    if eval_config.dataset_version == 2:
        eval_json += "l"  # jsonl

    # Update enableCropEmbeddings.
    eval_config_dict = {
        **asdict(eval_config),
        "enable_crop_embeddings": training_info.dl_config.get("enableCropEmbeddings", False),
    }
    eval_config = DeepweedConfig.from_dict(eval_config_dict)

    elastic_launcher(
        nproc_per_node,
        evaluate_embeddings_on_new_data,
        default,
        EvaluateDeepweedEmbeddingsArguments(
            eval_config=eval_config,
            eval_json=eval_json,
            crops=crops,
            weeds=weeds,
            embedding_training_info_obj=training_info,
            eval_dir=eval_dir,
            save_hdf5_embeddings=True,
            save_image_point_embeddings=True,
            dataset_id=dataset_id,
        ),
    )

    embedding_filepath_name = f"{EmbeddingType.REDUCED_SCALED.name.lower() if save_reduce_scaled else EmbeddingType.FULL.name.lower()}_embeddings.hdf5"

    version_id: Optional[str] = None
    if version_name is None:
        version_id = EmbeddingDataset(os.path.join(eval_dir, embedding_filepath_name)).metadata.version_id
    else:
        version_id = version_name
    output_path = get_local_embedding_filepath(dataset_id, model_id, version_id)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    os.rename(
        os.path.join(eval_dir, embedding_filepath_name), output_path,
    )

    if not eval_config.fast_run:
        upload_embedding_hdf5(output_path, dataset_id, model_id, version_id)

    LOG.info(
        f"Finished embedding model {model_id} with dataset: {dataset_id} and version_id: {version_id}. Saved at {output_path}"
    )
    return output_path


if __name__ == "__main__":
    main()

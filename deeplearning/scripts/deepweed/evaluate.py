import argparse
import json
import logging
import os
import shutil

import boto3

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.points_db_utils import get_points_db_filenames
from deeplearning.deepweed.trainer import Deepweed<PERSON>rainer
from deeplearning.deepweed.veselka_utils import get_reverse_sorted_evaluations_by_model_id
from deeplearning.dl_metrics import copy_db, get_db, get_session
from deeplearning.scripts.utils.inference_utils import run_inference_on_file
from deeplearning.scripts.utils.training_info import TrainingInfo
from deeplearning.scripts.utils.utils import (
    VeselkaClient,
    add_embedding_training_arguments,
    add_job_creator_arguments,
    check_for_driptape_weights,
    compare_version_a_b,
    generate_model_id,
    get_crop_and_weed_names,
    get_dataset,
    get_dataset_v2,
    get_pipeline,
    remove_files_from_dataset_json,
    update_dataset_version,
)
from deeplearning.tools.check_nightly_values import TEST_RESULTS_FILE
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.trainer import Environment

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")

TEST_JSON = "test.json"
V2_TEST_JSON = "test.jsonl"


def main() -> None:  # noqa
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str, default=None)
    parser.add_argument("--evaluation-id", type=str, default=None)
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument(
        "--only-new-data",
        action="store_true",
        help="If passed, will generate a dataset consisting of delta between previous evaluation for this model and current data",
    )
    parser.add_argument("--no-only-new-data", action="store_false", dest="only_new_data")
    parser.add_argument("--wandb-project", type=str, default="deepweed")
    parser.add_argument("--geohashes", type=str, required=False)
    parser.add_argument("--dataset-file-name", type=str, default=None)
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--save-image-point-embeddings", action="store_true", default=False)

    parser.set_defaults(only_new_data=True)

    add_job_creator_arguments(parser)
    add_embedding_training_arguments(parser)

    args = parser.parse_args()

    dl_config_dict = {
        "wandb_project": args.wandb_project if not args.fast_run else "deepweed-fast-run",
        "model_id": args.model_id,
        "fast_run": args.fast_run,
        **args.dl_config,
    }

    if "dataset_version" not in dl_config_dict:
        dl_config_dict["dataset_version"] = 2

    dataset_version = dl_config_dict["dataset_version"]

    model_id = dl_config_dict["model_id"]
    dataset_file_name = (
        args.dataset_file_name if args.dataset_file_name else (V2_TEST_JSON if dataset_version == 2 else TEST_JSON)
    )

    if args.job_id is None:
        evaluation_id = generate_model_id()
    else:
        evaluation_id = args.job_id

    if model_id is None:
        raise RuntimeError("Cannot evaluate without model ID")

    environment = Environment.DEVELOPMENT
    if args.preview:
        environment = Environment.PREVIEW
    elif args.production:
        environment = Environment.PRODUCTION

    if args.fast_run:
        environment = Environment.DEVELOPMENT

    training_info = TrainingInfo(model_id, include_points_db=True)
    assert training_info.dataset_id, f"Model {model_id} does not have dataset_id"
    evaluations = get_reverse_sorted_evaluations_by_model_id(model_id)

    evaluation_ids = [eval["id"] for eval in evaluations]

    LOG.info(f"{len(evaluation_ids)} evaluations found for model {model_id}")

    parent_dataset_id = training_info.dataset_id
    previous_evaluation_id = None
    if len(evaluation_ids) > 0:
        previous_evaluation_id = evaluation_ids[0]
        parent_dataset_id = evaluations[0]["dataset_id"]

    only_new_data = args.only_new_data
    if parent_dataset_id is None and args.only_new_data:
        LOG.info("No dataset id, going to fall back to traditional evals")
        parent_dataset_id = training_info.dataset_id
        only_new_data = False

    old_files_set = set()
    previous_metrics_path = None
    container_version = os.getenv("CONTAINER_VERSION")
    if only_new_data and container_version is not None:
        try:
            comp_values = compare_version_a_b(container_version, training_info.container_version)
            if comp_values[0] == 0 and comp_values[1] == 0 and comp_values[2] >= 0:
                if previous_evaluation_id is not None:
                    download_records(previous_evaluation_id, "evaluations", include_points_db=True)
                    previous_metrics_path = f"{CARBON_DATA_DIR}/deeplearning/evaluations/{previous_evaluation_id}"
                    if os.path.exists(previous_metrics_path):
                        old_files_set = get_points_db_filenames(f"{previous_metrics_path}/test_dataframes/points_v2.db")

                        LOG.info(
                            f"Found old evaluation id, will only run evaluations on newer data. Previous evaluation id: {previous_evaluation_id}, number of old datapoints: {len(old_files_set)}"
                        )
                    else:
                        only_new_data = False
                elif os.path.exists(f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}"):
                    previous_metrics_path = f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}"
                    old_files_set = get_points_db_filenames(f"{previous_metrics_path}/test_dataframes/points_v2.db")

                    LOG.info(
                        f"Using model test_dataframe, will only run evaluations on newer data. Number of old datapoints: {len(old_files_set)}"
                    )
                else:
                    only_new_data = False
            else:
                only_new_data = False
        except Exception as e:
            LOG.warning(f"Error trying to compare versions: {e}")
            only_new_data = False
    else:
        only_new_data = False

    if not only_new_data:
        LOG.info(
            f"Could not find data from older evaluation runs for model: {model_id}. Current container version: {container_version}, model container version: {training_info.container_version}."
        )

    new_dataset_version = update_dataset_version(
        dataset_id=args.dataset_id, parent_dataset_id=training_info.dataset_id, dataset_version=dataset_version
    )

    if dataset_version != new_dataset_version:
        dataset_file_name = (
            (dataset_file_name + "l") if dataset_file_name.endswith(".json") else dataset_file_name[:-1]
        )  # json <-> jsonl

        dataset_version = new_dataset_version

    crop = training_info.crop
    pipeline_id = training_info.pipeline_id

    assert (
        crop is not None or pipeline_id is not None
    ), "Model must have either a crop or a pipeline_id associated with it."

    dataset_crops = None
    deployment_crop_ids = None
    if crop is not None:
        dataset_crops = [crop]
    elif pipeline_id is not None:
        deployment_crop_ids = get_pipeline(pipeline_id).deployment_crop_ids

    geohashes = None
    if args.geohashes is not None:
        geohashes = args.geohashes.split(",")
    elif training_info.emphasized_geohashes is not None:
        geohashes = training_info.emphasized_geohashes

    if args.dataset_id is not None:
        if dataset_version == 1:
            dataset_id, _ = get_dataset(dataset_id=args.dataset_id)
        elif dataset_version == 2:
            dataset_id, _ = get_dataset_v2(dataset_id=args.dataset_id)
    else:
        if dataset_version == 1:
            dataset_id, _ = get_dataset(
                crops=dataset_crops,
                crop_ids=deployment_crop_ids,
                parent_id=parent_dataset_id,
                fast_run=args.fast_run,
                evaluation=True,
                only_new_data=only_new_data,
                geohash=geohashes,
            )
        elif dataset_version == 2:
            dataset_id, _ = get_dataset_v2(
                pipeline_id=pipeline_id, geohash=geohashes, parent_id=parent_dataset_id, evaluation=True,
            )

    dl_config_dict["dataset_version"] = dataset_version
    dl_config = DeepweedConfig.from_dict(dl_config_dict)

    LOG.info(f"Downloaded dataset: {dataset_id}")

    driptape = check_for_driptape_weights(training_info)
    if driptape:
        segmentation_classes = [
            "DRIPTAPE",
        ]
    else:
        segmentation_classes = None

    eval_json, number_of_new_items = remove_files_from_dataset_json(
        f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/{dataset_file_name}",
        old_files_set,
        dataset_version=dataset_version,
    )

    if number_of_new_items == 0:
        LOG.error("Did not run evaluations, there are no new images.")
        return

    crops, weeds = get_crop_and_weed_names(dataset_id, dataset_version=dataset_version)

    with DeepweedTrainer() as trainer:
        run_inference_on_file(
            trainer=trainer,
            dl_config=dl_config,
            eval_json=eval_json,
            crops=crops,
            weeds=weeds,
            segmentation_classes=segmentation_classes,
            training_info=training_info,
            eval_dir=f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}",
            save_image_point_embeddings=args.save_image_point_embeddings,
        )

    with open(f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/{TEST_RESULTS_FILE}") as f:
        metrics = json.load(f)

    deployable = False
    if (
        metrics["test_point_oec"] is not None
        and metrics["test_segmentation_oec"] is not None
        and metrics["dl_metrics_test_crops_targeted"] is not None
    ):
        if (
            metrics["test_point_oec"] > 0.8
            and metrics["test_segmentation_oec"] > 0.8
            and metrics["dl_metrics_test_crops_targeted"] < 0.02
        ):
            deployable = True

    bucket = boto3.resource("s3").Bucket("maka-pono")

    evaluation_dir = f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}"

    bucket.upload_file(
        os.path.join(evaluation_dir, TEST_RESULTS_FILE), f"evaluations/{evaluation_id}/{TEST_RESULTS_FILE}"
    )
    if os.path.exists(os.path.join(evaluation_dir, "splits/test.csv")):
        bucket.upload_file(
            os.path.join(evaluation_dir, "splits/test.csv"), f"evaluations/{evaluation_id}/splits/test.csv"
        )

    if only_new_data and previous_metrics_path is not None:
        shutil.move(
            f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_dataframes/points_v2.db",
            f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_dataframes/incremental_points_v2.db",
        )

        bucket.upload_file(
            os.path.join(evaluation_dir, "test_dataframes/incremental_points_v2.db"),
            f"evaluations/{evaluation_id}/test_dataframes/incremental_points_v2.db",
        )

        if os.path.exists(os.path.join(previous_metrics_path, "test_dataframes/points_v2.db")):
            os.makedirs(f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_dataframes", exist_ok=True)
            shutil.copyfile(
                os.path.join(previous_metrics_path, "test_dataframes/points_v2.db"),
                f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_dataframes/points_v2.db",
            )

            with get_session(
                get_db(
                    f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_dataframes/incremental_points_v2.db"
                )
            ) as source_sess:
                with get_session(
                    get_db(f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_dataframes/points_v2.db")
                ) as dest_sess:
                    copy_db(source_sess, dest_sess)

    bucket.upload_file(
        os.path.join(evaluation_dir, "test_dataframes/points_v2.db"),
        f"evaluations/{evaluation_id}/test_dataframes/points_v2.db",
    )

    if environment == Environment.PRODUCTION and args.dataset_file_name == TEST_JSON:
        client = VeselkaClient()
        client.create_evaluation(
            payload={
                "id": evaluation_id,
                "model_id": dl_config.model_id,
                "test_oec": metrics["test_oec"],
                "deployable": deployable,
                "dataset_id": dataset_id,
            }
        )


if __name__ == "__main__":  # noqa
    main()

import random

from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.scripts.utils.utils import (
    compare_version_a_b,
    get_last_nth_timestamps,
    remove_files_from_dict,
    validate_production_version,
)
from deeplearning.utils.dataset import get_recency_split_value


def test_get_last_nth_timestamps() -> None:
    random_100 = [random.randint(0, int(1e6)) for i in range(100)]

    datapoint_timestamps = [DatapointTimestamps(image_captured_timestamp_ms=rand_time) for rand_time in random_100]

    last_nth_timestamps_1 = get_last_nth_timestamps(datapoint_timestamps[:1])
    last_nth_timestamps_21 = get_last_nth_timestamps(datapoint_timestamps[:21])
    last_nth_timestamps_41 = get_last_nth_timestamps(datapoint_timestamps[:41])
    last_nth_timestamps_51 = get_last_nth_timestamps(datapoint_timestamps[:51])
    last_nth_timestamps_100 = get_last_nth_timestamps(datapoint_timestamps)

    reverse_sorted_100 = sorted(random_100, reverse=True)
    n_minus_0 = reverse_sorted_100[0]
    n_minus_20 = reverse_sorted_100[20]
    n_minus_40 = reverse_sorted_100[40]
    n_minus_50 = reverse_sorted_100[50]

    assert len(last_nth_timestamps_1) == 1
    assert len(last_nth_timestamps_21) == 3
    assert len(last_nth_timestamps_41) == 5
    assert len(last_nth_timestamps_51) == 6
    assert len(last_nth_timestamps_100) == 6

    assert last_nth_timestamps_100[0].image_captured_timestamp_ms == n_minus_0
    assert last_nth_timestamps_100[20].image_captured_timestamp_ms == n_minus_20
    assert last_nth_timestamps_100[40].image_captured_timestamp_ms == n_minus_40
    assert last_nth_timestamps_100[50].image_captured_timestamp_ms == n_minus_50


def test_remove_files_from_dict() -> None:
    dataset = {
        "images": [
            {
                "id": "a4e5038f-fd38-554a-8407-67b0bdee5a36",
                "uri": "s3://maka-pono/media/arugula-periandsons-2022-05-17/predict/record_predict2_unannotated.2022-05-17T16-48-36.909000Z.png",
                "height": None,
                "width": None,
                "crop": "arugula",
                "crop_id": None,
                "captured_at": 1652823303964,
                "city": "Unknownville",
                "latitude": None,
                "longitude": None,
                "is_new": False,
                "session_name": None,
            },
            {
                "id": "a9a1b61f-add9-5c9d-b26b-7407984f52e2",
                "uri": "s3://maka-pono/media/arugula-periandsons-2022-05-17/predict/record_predict2_unannotated.2022-05-17T16-53-32.894000Z.png",
                "height": None,
                "width": None,
                "crop": "arugula",
                "crop_id": None,
                "captured_at": 1652823745643,
                "city": "Unknownville",
                "latitude": None,
                "longitude": None,
                "is_new": False,
                "session_name": None,
            },
            {
                "id": "bc212ac2-8c71-5a84-961b-4bce29b7ba97",
                "uri": "s3://maka-pono/media/arugula-periandsons-2022-05-17/predict/record_predict1_unannotated.2022-05-17T16-57-37.195000Z.png",
                "height": None,
                "width": None,
                "crop": "arugula",
                "crop_id": None,
                "captured_at": 1652822148393,
                "city": "Unknownville",
                "latitude": None,
                "longitude": None,
                "is_new": False,
                "session_name": None,
            },
        ]
    }
    images_to_ignore = set(
        [
            "s3://maka-pono/media/arugula-periandsons-2022-05-17/predict/record_predict2_unannotated.2022-05-17T16-53-32.894000Z.png",
            "s3://maka-pono/media/arugula-periandsons-2022-05-17/predict/record_predict2_unannotated.2022-05-17T16-48-36.909000Z.png",
        ]
    )

    dataset = remove_files_from_dict(dataset, images_to_ignore)
    assert len(dataset["images"]) == 1
    assert dataset["images"][0] == {
        "id": "bc212ac2-8c71-5a84-961b-4bce29b7ba97",
        "uri": "s3://maka-pono/media/arugula-periandsons-2022-05-17/predict/record_predict1_unannotated.2022-05-17T16-57-37.195000Z.png",
        "height": None,
        "width": None,
        "crop": "arugula",
        "crop_id": None,
        "captured_at": 1652822148393,
        "city": "Unknownville",
        "latitude": None,
        "longitude": None,
        "is_new": False,
        "session_name": None,
    }


def test_get_recency_split_value() -> None:
    reverse_sorted_timestamps = list(range(10, 0, -1))
    print("reverse_sorted_timestamps", reverse_sorted_timestamps)

    # Everything after candidate
    split1, count1 = get_recency_split_value(reverse_sorted_timestamps, min_images=0, time_back=-2)

    assert split1 == 1
    assert count1 == 10

    split2, count2 = get_recency_split_value(reverse_sorted_timestamps, min_images=5, time_back=-2)

    assert split2 == 1
    assert count2 == 10

    # Everything before candidate
    split3, count3 = get_recency_split_value(reverse_sorted_timestamps, min_images=0, time_back=11)

    assert split3 == 10
    assert count3 == 1

    split4, count4 = get_recency_split_value(reverse_sorted_timestamps, min_images=5, time_back=11)

    assert split4 == 5
    assert count4 == 6

    # Half above candidate but ask for 7
    split5, count5 = get_recency_split_value(reverse_sorted_timestamps, min_images=7, time_back=5)

    assert split5 == 3
    assert count5 == 8

    # Half above candidate but ask for 2
    split6, count6 = get_recency_split_value(reverse_sorted_timestamps, min_images=2, time_back=5)

    assert split6 == 5
    assert count6 == 6


def test_validate_production_version() -> None:
    version_a = ""
    version_b = "c1.2.3"
    version_c = "c.1.1.1"
    version_d = "v9.2.1"
    version_e = "d1.2"
    version_f = "c1.2"

    assert not validate_production_version(version_a)
    assert validate_production_version(version_b)
    assert not validate_production_version(version_c)
    assert not validate_production_version(version_d)
    assert not validate_production_version(version_e)
    assert not validate_production_version(version_f)


def test_compare_version_a_b() -> None:
    for (version_a, version_b, expected) in [
        ("", "", (0, 0, 0)),
        ("c1.2.3", "c1.2.3", (0, 0, 0)),
        ("c1.2.4", "c1.2.3", (0, 0, 1)),
        ("c1.2.3", "c1.2.4", (0, 0, -1)),
        ("c2.3.3", "c2.4.4", (0, -1, -1)),
    ]:
        assert compare_version_a_b(version_a, version_b) == expected

import argparse
import datetime
import glob
import hashlib
import json
import logging
import os
import struct
import time
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, cast

import cv2
import numpy
import numpy.typing as npt
import torch
import tqdm

from deeplearning.constants import CARBON_DATA_DIR, EMBEDDING_VERSION, DeepweedTrainingSubtype, Environment
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import (
    DATASET_SAMPLING_ALGORITHM_CATEGORY_EMBEDDINGS,
    DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS,
    DATASET_SAMPLING_ALGORITHM_EMBEDDINGS,
)
from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.scripts.utils.training_info import TrainingInfo
from lib.common.perf.perf_tracker import set_verbosity
from lib.common.veselka.client import DatasetV2, <PERSON>peline, VeselkaClient

set_verbosity(False)


CONTAINER_VERSION = os.getenv("CONTAINER_VERSION")

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)

EXCLUDE_ROBOTS: List[str] = []

EXCLUDE_CROP_IDS_PRETRAIN = [
    "8eed6e63-4eb8-429f-aedf-12921e78fd1d",  # Spinach_with Beetle
    "9e2e237f-6187-4041-aed6-f9369b3d90df",  # Pellets
    "13c94760-a6e0-4e6e-baa6-910473d4f3ca",  # Test crop
    "9c829d39-af0a-409c-9912-f2d0b6ab0470",  # World Ag Expo 2023
    "5ffc2dae-7b7f-4161-b344-36ab7769f557",  # Cotton with Beetle
    "f03f5553-b143-408c-a2a1-23f33b401704",  # Carbon Crosses
    "de0819f9-cffc-41fb-88b4-2cebbe1ffb6f",  # Treadkill
    "9d48a64d-f2ab-4f19-b6d0-246b35aff1a3",  # Strawberry transplants
    "12dda818-ee04-471a-b876-cd131583a6c4",  # Onion with barley cover (beneficial labeling)
    "4aafdc99-6bda-4b5a-b64b-6c0d6970a958",  # legacy - test-thinning-lettuce
    "010eb0cc-66ab-46d1-9b42-c8e0c7404618",  # legacy - carrot
    "d701d78a-8799-41be-a49e-412f9c3466ad",  # legacy - test
    "384ce736-3801-4a76-80e1-c00866c5d05c",  # legacy - green_bean
    "689b40d9-2aeb-4f8d-8ce2-dd86c964bf82",  # legacy - green_lettuce
    "37ebab62-b76e-4bbf-b8da-ddd5523ee993",  # legacy - spinach
    "a63792a3-e8e7-48ef-a14b-867373917271",  # legacy - leek
    "ac2cf80f-d975-4769-a432-6f31ec77ece1",  # legacy - garlic
    "4ffba7bb-cf3a-4499-a256-abdec638e6dd",  # legacy - cilantro
    "09d48ef3-b697-443a-a22b-8c61eaf622c6",  # legacy - chard
    "b987b450-3cb8-439d-a2bb-b1fda45041ac",  # legacy - cauliflower
    "0c8616c9-d98e-4513-a71c-a9b726bf2a99",  # legacy - brassica
    "2b17d172-3d98-4672-8303-77ec96955317",  # legacy - carbon-crosses
    "6cd5f04a-85f2-4b76-a734-bdc3c5fefd5b",  # legacy - broccoli_cauliflower
    "bbee3cf2-76c6-4484-9e5f-1a5db89cd6c7",  # legacy - bok_choy
    "3b0a0d25-be70-4968-bffd-b7cc29749c78",  # legacy - arugula
    "3caafb8b-1a61-4690-b5b5-4cc165087cfb",  # legacy - parsley
    "47e1f09d-cfa3-45a0-8ad5-701ca7f20fc4",  # legacy - red_lettuce
    "a27cc4de-d945-4d62-9673-0ca345184aff",  # legacy - basil
    "72228f8b-a202-4036-8650-7f5e95732391",  # legacy - cotton
    "ca3f51d9-03b5-4dd5-8c75-c830cbefd66a",  # legacy - cucumber
    "39a0ff59-55b4-4bba-9eb8-c083fbbe86f1",  # legacy - dandelion
    "77af57e3-57b9-4d69-aeb7-734037dba280",  # legacy - echinacea_angustifolia
    "5b0979ba-6a93-4d51-8e42-74fe8e1e0c1c",  # legacy - fennel
    "9611d658-cac1-40b5-91d3-dc9228177900",  # legacy - test-treadkill
    "81e978de-9e7c-490f-bce7-7171b0027054",  # legacy - soybean
    "5be638a4-0644-46ff-9773-03bd1e09c776",  # legacy - shallot
    "00d346ab-3dca-4339-88ff-b02ad6bdd9fb",  # legacy - mint
    "5ab64bb4-faca-48a4-bbb4-1f1fda2c55ec",  # legacy - celery
    "8ce9f91f-1476-4484-941d-debf69f55aa4",  # legacy - beet
    "40b843ca-bb7c-40ed-9f43-e43f7d1519d2",  # legacy - onion_with_pellets
    "439fb381-b60d-4430-a082-510f6e657cdb",  # legacy - carbon_crosses
    "8869edd8-6ef7-499d-b9f5-ddc2723f171b",  # legacy - experimental-removed-crops
    "96c510c0-5586-4565-bf9b-98ab1286ece8",  # legacy - treadkill-carbon-crosses
    "851a835c-233e-4712-b52f-fd944ec822cd",  # legacy - potato
    "ba0180e4-bcc6-43ca-9a91-07670a155939",  # legacy - onion
    "82d22586-58d7-4dd6-8a7a-c0f7b7aa2488",  # legacy - lettuce
    "51d010d0-510d-429b-8321-a0ea967a719a",  # legacy - baby_kale
    "b9d5df47-3e57-420d-b375-915042236194",  # legacy - mizuna
    "88485a7a-d724-4540-bc3d-f34cbaf11953",  # legacy - spinach_with_sunflower_seeds
]

EXCLUDE_CROP_IDS_DRIPTAPE = [
    "2b17d172-3d98-4672-8303-77ec96955317",  # carbon-crosses
    "8869edd8-6ef7-499d-b9f5-ddc2723f171b",  # experimental-removed-crops
    "d701d78a-8799-41be-a49e-412f9c3466ad",  # test
    "4aafdc99-6bda-4b5a-b64b-6c0d6970a958",  # test-thinning-lettuce
    "9611d658-cac1-40b5-91d3-dc9228177900",  # test-treadkill
    "96c510c0-5586-4565-bf9b-98ab1286ece8",  # treadkill-carbon-crosses
    "40b843ca-bb7c-40ed-9f43-e43f7d1519d2",  # onion_with_pellets
    "88485a7a-d724-4540-bc3d-f34cbaf11953",  # spinach_with_sunflower_seeds
    "9e2e237f-6187-4041-aed6-f9369b3d90df",  # Pellets
    "13c94760-a6e0-4e6e-baa6-910473d4f3ca",  # Test crop
    "9c829d39-af0a-409c-9912-f2d0b6ab0470",  # World Ag Expo 2023
    "f03f5553-b143-408c-a2a1-23f33b401704",  # Carbon Crosses
    "de0819f9-cffc-41fb-88b4-2cebbe1ffb6f",  # Treadkill
]


def setup_logging(level: int = logging.INFO, format: str = "%(asctime)s - %(name)s - %(message)s") -> None:
    # Remove any existing handlers.
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Reconfigure logging.
    logging.basicConfig(level=level, format=format)


def add_embedding_training_arguments(parser: argparse.ArgumentParser) -> None:
    parser.add_argument(
        "--evaluate-new-comparison-data",
        action="store_true",
        default=False,
        help="Pass this to run the comparison model on new data in the dataset",
    )
    parser.add_argument(
        "--comparison-model-id",
        type=str,
        default=None,
        help="Pass this argument to train embeddings with a specific comparison model",
    )


def add_job_creator_arguments(parser: argparse.ArgumentParser) -> None:
    parser.add_argument("--production", action="store_true", dest="production")
    parser.add_argument("--preview", action="store_true", dest="preview")
    parser.add_argument("--job-id", type=str, default=None)
    parser.add_argument("--pipeline-id", type=str, default=None)
    parser.add_argument("--dl-config", type=json.loads, default={})
    parser.add_argument("--description", type=str, default=None)
    parser.set_defaults(production=False, preview=False)


def add_common_arguments(parser: argparse.ArgumentParser) -> None:
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument("--resume-from", type=str, default=None)
    parser.add_argument(
        "--tags", type=lambda s: [item.strip() for item in s.split(",")], help="Comma separated list of tags"
    )
    parser.add_argument(
        "--nproc-per-node",
        type=int,
        default=torch.cuda.device_count(),
        help="The number of processes for our elastic_launched instance of training",
    )
    add_job_creator_arguments(parser)


def add_distributed_launch_arguments(parser: argparse.ArgumentParser) -> None:
    parser.add_argument(
        "--server-address",
        type=str,
        default="127.0.0.1:50051",
        help="Address of the distributed launch server (host:port)",
    )
    parser.add_argument(
        "--rdzv-endpoint", type=str, default="127.0.0.1:29500", help="Address of the rendezvous endpoint (host:port)"
    )
    parser.add_argument("--nodes", type=int, default=1, help="Number of nodes to use for distributed training")


def get_project_name(fast_run: bool) -> str:
    monthly_project_name = f"deepweed-{datetime.datetime.now().year}-{datetime.datetime.now().month:02}"
    return monthly_project_name if not fast_run else f"{monthly_project_name}-fast-run"


def modify_dataset(config: DeepweedConfig, dataset_id: str) -> None:  # noqa C901

    for split in ["train", "validation", "test"]:

        if split == "train":
            min_crop_size_mm = config.train_min_average_crop_size_mm
            max_crop_size_mm = config.train_max_average_crop_size_mm
        elif split == "validation":
            min_crop_size_mm = config.validation_min_average_crop_size_mm
            max_crop_size_mm = config.validation_max_average_crop_size_mm
        elif split == "test":
            min_crop_size_mm = config.test_min_average_crop_size_mm
            max_crop_size_mm = config.test_max_average_crop_size_mm

        pixels_per_mm = 200 / 25.4

        if min_crop_size_mm is None:
            min_crop_size_mm = None
        else:
            min_crop_size_px = min_crop_size_mm * pixels_per_mm

        if max_crop_size_mm is None:
            max_crop_size_mm = None
        else:
            max_crop_size_px = max_crop_size_mm * pixels_per_mm

        filepath = f"/data/deeplearning/datasets/{dataset_id}/{split}.json"

        with open(filepath) as f:
            data = json.load(f)

        image_id2annotations: Dict[str, List[Dict[Any, Any]]] = {}
        for annotation in data["annotations"]:
            if annotation["annotation_type"] == "point":
                if annotation["image_id"] not in image_id2annotations.keys():
                    image_id2annotations[annotation["image_id"]] = []

                image_id2annotations[annotation["image_id"]].append(annotation)

        new_images = []
        for image in data["images"]:

            annotations = image_id2annotations.get(image["id"])

            if annotations is None:
                new_images.append(image)
                continue

            if len(annotations) == 0:
                new_images.append(image)
                continue

            crop_sizes = []
            for annotation in annotations:
                if annotation["label"] in ["grass", "offshoot", "broadleaf", "purslane"]:
                    continue

                crop_sizes.append(annotation["radius"])

            if len(crop_sizes) == 0:
                new_images.append(image)
                continue

            average_crop_size = sum(crop_sizes) / len(crop_sizes)

            if average_crop_size > min_crop_size_px or min_crop_size_px is None:
                if average_crop_size < max_crop_size_px or max_crop_size_px is None:
                    new_images.append(image)

        new_image_ids = [image["id"] for image in new_images]
        new_annotations = []
        for annotation in data["annotations"]:
            if annotation["annotation_type"] == "point":
                if annotation["image_id"] in new_image_ids:
                    new_annotations.append(annotation)

        data["annotations"] = new_annotations
        data["images"] = new_images

        with open(filepath, "w") as f:
            json.dump(data, f)


def modify_dataset_for_all_weeds(  # noqa: C901
    dataset_id: str, data_source_crop_ids: List[str], deployment_crop_ids: List[str]
) -> None:

    for split in ["train", "validation", "test"]:
        filepath = f"/data/deeplearning/datasets/{dataset_id}/{split}.json"

        with open(filepath) as f:
            data = json.load(f)

        # Build new images list
        new_images = []
        for image in data["images"]:

            image["point_categories"] = ["crop", "grass", "offshoot", "broadleaf", "purslane"]

            if split in ["validation", "test"]:
                if image["crop_id"] in deployment_crop_ids:
                    new_images.append(image)
            else:
                new_images.append(image)

        image_id2image = {image["id"]: image for image in new_images}

        # Build new annotations list
        new_annotations = []
        for annotation in data["annotations"]:
            image = image_id2image.get(annotation["image_id"])
            if image is not None:
                if annotation["label"] in ["grass", "offshoot", "broadleaf", "purslane"]:
                    new_annotations.append(annotation)
                else:
                    if image["crop_id"] not in data_source_crop_ids:
                        annotation["confidence"] = 3
                    annotation["label"] = "crop"
                    new_annotations.append(annotation)

        data["images"] = new_images
        data["annotations"] = new_annotations

        # For images in the training set filter out those images that have a high amount of red confidenced area.
        if split == "train":
            image_id2annotations: Dict[str, Any] = {}
            for annotation in new_annotations:
                if annotation["annotation_type"] == "point":
                    if annotation["image_id"] in image_id2annotations.keys():
                        image_id2annotations[annotation["image_id"]].append(annotation)
                    else:
                        image_id2annotations[annotation["image_id"]] = [annotation]

            new_images = []
            for image in tqdm.tqdm(data["images"]):

                if image["crop_id"] in data_source_crop_ids:
                    new_images.append(image)
                else:
                    annotations = image_id2annotations.get(image["id"])
                    downsample_value = 4
                    percentage_multiplier = (downsample_value ** 2) / (4096 * 3000)
                    if annotations is not None:
                        mask: npt.NDArray[Any] = numpy.zeros(
                            (int(4096 / downsample_value), int(4096 / downsample_value))
                        )

                        for annotation in annotations:
                            if annotation["label"] == "crop":
                                mask = cv2.circle(
                                    mask,
                                    (int(annotation["x"] / downsample_value), int(annotation["y"] / downsample_value)),
                                    int(annotation["radius"] / downsample_value),
                                    (1,),
                                    -1,
                                )

                        confidence_percentage = mask.sum() * percentage_multiplier
                    else:
                        confidence_percentage = 0.0

                    if confidence_percentage < 0.05:
                        new_images.append(image)

                image_id2image = {image["id"]: image for image in new_images}

            new_annotations = []
            for annotation in data["annotations"]:
                image = image_id2image.get(annotation["image_id"])
                if image is not None:
                    new_annotations.append(annotation)

            data["images"] = new_images
            data["annotations"] = new_annotations

        with open(filepath, "w") as f:
            json.dump(data, f)


def get_last_nth_timestamps(lst: List[DatapointTimestamps]) -> Dict[int, DatapointTimestamps]:
    sorted_list = sorted(
        lst,
        key=lambda x: x.image_captured_timestamp_ms if x.image_captured_timestamp_ms is not None else 0,
        reverse=True,
    )
    timestamps: Dict[int, DatapointTimestamps] = {}
    ns = [0, 10, 20, 30, 40, 50]

    for n in ns:
        if len(sorted_list) > n:
            timestamps[n] = sorted_list[n]

    return timestamps


def get_pipeline(pipeline_id: str) -> Pipeline:
    client = VeselkaClient(use_carbon_cache=True)
    pipeline: Pipeline = client.get_pipeline(pipeline_id)
    return pipeline


def get_crop_and_weed_names(dataset_id: str, dataset_version: int = 1) -> Tuple[List[str], List[str]]:
    train_file = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json"
    test_file = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json"
    if dataset_version == 2:
        train_file += "l"
        test_file += "l"  # jsonl

    train_crops, train_weeds = get_crop_and_weed_names_from_file(train_file, dataset_version=dataset_version)
    test_crops, test_weeds = get_crop_and_weed_names_from_file(test_file, dataset_version=dataset_version)
    train_crops.update(test_crops)
    train_weeds.update(test_weeds)

    # Fallback to old behavior if no weed categories are found.
    if len(train_weeds) == 0:
        train_crops = get_crop_names_from_file(f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json")
        test_crops = get_crop_names_from_file(f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json")
        train_crops.update(test_crops)
        train_weeds.update(["BROADLEAF", "GRASS", "OFFSHOOT", "PURSLANE"])

    return list(train_crops), list(train_weeds)


def get_crop_names_from_file(path: str) -> Set[str]:
    with open(path) as f:
        data = json.load(f)
    crops = set()
    for datapoint in data["images"]:
        crop = datapoint["crop"]
        if crop and crop.upper() not in crops:
            crops.add(crop.upper())
        if "crop_id" in datapoint:
            crop_id = datapoint["crop_id"].upper()
            if crop_id not in crops:
                crops.add(crop_id)

    return crops


def get_crop_and_weed_names_from_file(path: str, dataset_version: int = 1) -> Tuple[Set[str], Set[str]]:
    crops = set()
    weeds = set()
    if dataset_version == 1:
        with open(path) as f:
            data = json.load(f)

        for datapoint in data["images"]:
            crop = datapoint["crop"]
            if crop and crop.upper() not in crops:
                crops.add(crop.upper())

        for datapoint in data["categories"]:
            name = datapoint["name"]
            if datapoint["supercategory"] == "crop":
                crops.add(name)
            elif datapoint["supercategory"] == "weed":
                weeds.add(name)

    elif dataset_version == 2:
        with open(path) as f:
            data = [json.loads(line) for line in f]

        for datapoint in data:
            categories = json.loads(datapoint["categories"]["data"])["crownLabels"]
            for category, info in categories.items():
                if "crop" in info:  # Ask zach about this
                    crops.add("CROP")
                else:
                    weeds.add(category.upper())

    return crops, weeds


def generate_model_id() -> str:
    md5 = hashlib.md5()
    md5.update(struct.pack("d", time.time()))
    model_id = md5.hexdigest()
    return model_id


def get_dataset_v2(
    dataset_id: Optional[str] = None,
    pipeline_id: Optional[str] = None,
    parent_id: Optional[str] = None,
    robots: Optional[List[str]] = None,
    geohash: Optional[List[str]] = None,
    exclude_crop_ids: Optional[List[str]] = None,
    exclude_robots: Optional[List[str]] = None,
    train_set_size_limit: Optional[int] = None,
    validation_set_size_limit: Optional[int] = None,
    test_set_size_limit: Optional[int] = None,
    datapoints_size_limit: Optional[int] = None,
    train_geohashes: Optional[List[str]] = None,
    val_geohashes: Optional[List[str]] = None,
    test_geohashes: Optional[List[str]] = None,
    start_timestamp_ms: Optional[int] = None,
    end_timestamp_ms: Optional[int] = None,
    evaluation: bool = False,
    train_driptape_percentage: Optional[int] = None,
    val_driptape_percentage: Optional[int] = None,
    test_driptape_percentage: Optional[int] = None,
    page_size: Optional[int] = None,
) -> Tuple[str, DatasetV2]:
    client = VeselkaClient(data_dir=f"{CARBON_DATA_DIR}/deeplearning", use_carbon_cache=True)
    if dataset_id is None:
        assert pipeline_id is not None, "Must provide pipeline_id if dataset_id is not provided"

        dataset_id = client.create_deepweed_dataset_v2(
            pipeline_id=pipeline_id,
            parent_dataset_id=parent_id,
            robot_ids=robots,
            geohashes=geohash,
            excluded_crop_ids=exclude_crop_ids,
            excluded_robot_ids=exclude_robots,
            train_limit=train_set_size_limit,
            val_limit=validation_set_size_limit,
            test_limit=test_set_size_limit,
            datapoints_limit=datapoints_size_limit,
            train_geohashes=train_geohashes,
            val_geohashes=val_geohashes,
            test_geohashes=test_geohashes,
            start_timestamp=start_timestamp_ms,
            stop_timestamp=end_timestamp_ms,
            train_driptape_percentage=train_driptape_percentage,
            val_driptape_percentage=val_driptape_percentage,
            test_driptape_percentage=test_driptape_percentage,
            evaluation=evaluation,
            page_size=page_size,
        )

    dataset = client.get_dataset_v2(dataset_id=dataset_id)
    assert dataset is not None, f"Dataset {dataset_id} not found"
    client.download_dataset_v2(dataset=dataset)
    return dataset.id, dataset


def get_dataset(
    crops: Optional[List[str]] = None,
    crop_ids: Optional[List[str]] = None,
    fast_run: bool = False,
    dataset_id: Optional[str] = None,
    parent_id: Optional[str] = None,
    evaluation: bool = False,
    balance_positive_classes: Optional[bool] = None,
    balance_robot_ids: Optional[bool] = None,
    balance_geohash: Optional[int] = None,
    exclude_robots: Optional[List[str]] = None,
    exclude_crop_ids: Optional[List[str]] = None,
    robots: Optional[List[str]] = None,
    only_new_data: Optional[bool] = None,
    geohash: Optional[List[str]] = None,
    val_test_crop_ids: Optional[List[str]] = None,
    core_data_level: str = "unfiltered",
    test_geohashes: Optional[List[str]] = None,
    train_set_size_limit: Optional[int] = None,
    validation_set_size_limit: Optional[int] = None,
    test_set_size_limit: Optional[int] = None,
    val_test_geohashes: Optional[List[str]] = None,
    driptape: bool = False,
    start_timestamp_ms: Optional[int] = None,
    end_timestamp_ms: Optional[int] = None,
    train_no_driptape_keep_multiplier: Optional[int] = None,
    val_no_driptape_keep_multiplier: Optional[int] = None,
    test_no_driptape_keep_multiplier: Optional[int] = None,
) -> Tuple[str, Dict[str, Any]]:
    set_verbosity(False)

    if core_data_level == "unfiltered":
        core_data_params = None
    elif core_data_level == "low":
        core_data_params = {
            "plant_count_bounds": (-1, 100),
            "weed_count_bounds": (-1, 100),
            "crop_count_bounds": (-1, 100),
            "plant_radius_bounds": (0.0, 100.0),
            "weed_radius_bounds": (0.0, 100.0),
            "crop_radius_bounds": (0.0, 100.0),
            "plant_low_confidence_bound": 0.75,
            "weed_low_confidence_bound": 0.75,
            "crop_low_confidence_bound": 0.75,
        }
    elif core_data_level == "medium":
        core_data_params = {
            "plant_count_bounds": (0, 50),
            "weed_count_bounds": (0, 30),
            "crop_count_bounds": (0, 30),
            "plant_radius_bounds": (0.0, 100.0),
            "weed_radius_bounds": (0.0, 100.0),
            "crop_radius_bounds": (0.0, 100.0),
            "plant_low_confidence_bound": 0.25,
            "weed_low_confidence_bound": 0.25,
            "crop_low_confidence_bound": 0.25,
        }
    elif core_data_level == "high":
        core_data_params = {
            "plant_count_bounds": (5, 25),
            "weed_count_bounds": (5, 15),
            "crop_count_bounds": (5, 15),
            "plant_radius_bounds": (0.0, 100.0),
            "weed_radius_bounds": (0.0, 100.0),
            "crop_radius_bounds": (0.0, 100.0),
            "plant_low_confidence_bound": 0.0,
            "weed_low_confidence_bound": 0.0,
            "crop_low_confidence_bound": 0.0,
        }
    else:
        raise RuntimeError("Unrecognized core data level.")

    client = VeselkaClient(use_carbon_cache=True)
    if dataset_id is None:
        dataset_info = client.create_deepweed_dataset(
            start_timestamp=start_timestamp_ms,
            stop_timestamp=end_timestamp_ms,
            crops=crops,
            crop_ids=crop_ids,
            robots=robots,
            parent_id=parent_id,
            fast_run=fast_run,
            evaluation=evaluation,
            balance_positive_classes=balance_positive_classes,
            balance_robot_ids=balance_robot_ids,
            balance_geohash=balance_geohash,
            exclude_robots=exclude_robots,
            exclude_crop_ids=exclude_crop_ids,
            only_new_data=only_new_data,
            geohash=geohash,
            val_test_crop_ids=val_test_crop_ids,
            core_data_params=core_data_params,
            test_set_geohashes=test_geohashes,
            train_set_size_limit=train_set_size_limit,
            validation_set_size_limit=validation_set_size_limit,
            test_set_size_limit=test_set_size_limit,
            val_test_geohashes=val_test_geohashes,
            driptape=driptape,
            train_no_driptape_keep_multiplier=train_no_driptape_keep_multiplier,
            val_no_driptape_keep_multiplier=val_no_driptape_keep_multiplier,
            test_no_driptape_keep_multiplier=test_no_driptape_keep_multiplier,
        )
        dataset_id = dataset_info["id"]
    else:
        dataset_info = client.get_dataset(dataset_id)

    client.download_dataset(dataset_info)
    return (dataset_id, dataset_info)


def get_dataset_driptape_count(path: str) -> int:
    driptape_count = 0
    with open(path) as f:
        if path.endswith(".jsonl"):
            data2 = [json.loads(line) for line in f]

            if path.endswith(".jsonl"):
                for datapoint in data2:
                    for polygon in datapoint["polygons"]:
                        if polygon["label"] == "driptape":
                            driptape_count += 1
        else:
            data = json.load(f)

            for annotation in data["annotations"]:
                if annotation["annotation_type"] == "polygon" and annotation["label"] == "driptape":
                    driptape_count += 1

    return driptape_count


def remove_files_from_dict(dataset: Dict[str, Any], files_to_ignore: Set[str], field: str = "uri") -> Dict[str, Any]:
    dataset_images = dataset["images"]
    filtered_images = []

    for dataset_image in dataset_images:
        if dataset_image[field] not in files_to_ignore:
            filtered_images.append(dataset_image)

    dataset["images"] = filtered_images
    return dataset


def remove_files_from_dict_v2(
    dataset: List[Dict[str, Any]], files_to_ignore: Set[str], field: str = "uri"
) -> List[Dict[str, Any]]:
    filtered_dataset = []

    for image in dataset:
        if image[field] not in files_to_ignore:
            filtered_dataset.append(image)

    return filtered_dataset


def remove_files_from_dataset_json(
    dataset_json: str, files_to_ignore: Set[str], field: str = "uri", dataset_version: int = 1
) -> Tuple[str, int]:
    eval_test_json_filepath = "/".join(dataset_json.split("/")[:-1])
    _, ext = os.path.splitext(dataset_json)
    eval_test_json_filepath = os.path.join(eval_test_json_filepath, f"eval.{ext}")
    with open(dataset_json) as f:
        if dataset_version == 1:
            dataset = json.load(f)
            dataset = remove_files_from_dict(dataset, files_to_ignore, field)

        elif dataset_version == 2:
            dataset = [json.loads(line) for line in f]
            dataset = remove_files_from_dict_v2(dataset, files_to_ignore, field)

    if os.path.exists(eval_test_json_filepath):
        os.remove(eval_test_json_filepath)
    with open(eval_test_json_filepath, "w") as f:
        if dataset_version == 1:
            json.dump(dataset, f)
        elif dataset_version == 2:
            for image in dataset:
                json.dump(image, f)
                f.write("\n")

    return eval_test_json_filepath, len(dataset["images"]) if dataset_version == 1 else len(dataset)


def get_dataset_version(dataset_id: str) -> int:
    client = VeselkaClient(use_carbon_cache=True)

    dataset_info = client.get_dataset(dataset_id)

    for split in ["train", "validation", "test"]:
        if dataset_info[split] is not None:
            return 2 if dataset_info[split].endswith(".jsonl") else 1

    return 2  # If all splits are none dataset is in datapoints jsonl format


def update_dataset_version(
    dataset_id: Optional[str] = None, parent_dataset_id: Optional[str] = None, dataset_version: int = 1
) -> int:
    if dataset_id is not None:
        given_dataset_version = get_dataset_version(dataset_id)
        if dataset_version != given_dataset_version:
            LOG.info(
                f"Detected given dataset version {given_dataset_version}, using dataset version {given_dataset_version}"
            )
            dataset_version = given_dataset_version

    elif parent_dataset_id is not None:
        parent_dataset_version = get_dataset_version(parent_dataset_id)

        if dataset_version != parent_dataset_version:
            LOG.info(
                f"Detected parent dataset version {parent_dataset_version}, using dataset version {parent_dataset_version}"
            )
            dataset_version = parent_dataset_version

    return dataset_version


def get_checkpoint(training_info: TrainingInfo) -> str:
    path = f"{CARBON_DATA_DIR}/deeplearning/models/{training_info.model_id}/ckpt"
    files = [f for f in glob.glob(os.path.join(path, "epoch=*.ckpt"))]
    return files[0]


def check_for_driptape_weights(training_info: TrainingInfo) -> bool:
    try:
        ckpt = get_checkpoint(training_info)
    except IndexError:  # If there are no checkpoints
        return False
    data = torch.load(ckpt, map_location=torch.device("cpu"))
    for param_name in data["state_dict"].keys():
        if "backbone_segm" in param_name:
            return True
    return False


def get_segmentation_classes(training_info: TrainingInfo) -> Optional[List[str]]:
    driptape = check_for_driptape_weights(training_info)
    if driptape:
        segmentation_classes = ["DRIPTAPE"]
    else:
        segmentation_classes = None

    return segmentation_classes


def validate_production_version(version: str) -> bool:
    if len(version) == 0:
        return False
    starts_with_c = version[0] == "c"
    three_parts = len(version[1:].split(".")) == 3
    all_numbers = True
    for i in version[1:].split("."):
        try:
            int(i)
        except Exception as e:
            LOG.error(f"Error casting to int {e}")
            all_numbers = False
            break

    return starts_with_c and three_parts and all_numbers


def get_version_parts(version: str) -> Tuple[int, int, int]:
    is_valid = validate_production_version(version)

    if not is_valid:
        raise Exception(f"Not a valid version number: {version}")

    version_parts = tuple([int(part) for part in version[1:].split(".")])
    assert len(version_parts) == 3
    return (version_parts[0], version_parts[1], version_parts[2])


# Returns Tuple of (major_comp, minor_comp, hotfix_comp). Each _comp value indicates version_a's part - version_b's part
def compare_version_a_b(version_a: str, version_b: str) -> Tuple[int, int, int]:
    if version_a == "" and version_b == "":
        return (0, 0, 0)

    parts_a = get_version_parts(version_a)
    parts_b = get_version_parts(version_b)

    comp_values = tuple([part_a - part_b for (part_a, part_b) in zip(parts_a, parts_b)])

    assert len(comp_values) == 3
    return (comp_values[0], comp_values[1], comp_values[2])


def excluded_crop_ids_for_all_weeds(pipeline: Pipeline) -> List[str]:

    final_exclusion_list = []
    for crop_id in EXCLUDE_CROP_IDS_PRETRAIN:
        if not (crop_id in pipeline.data_source_crop_ids or crop_id in pipeline.deployment_crop_ids):
            final_exclusion_list.append(crop_id)

    return final_exclusion_list


def autodetect_fine_tune_parent_model(environment: Environment, pipeline_id: str) -> str:
    assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
    client = VeselkaClient()
    env = Environment.PRODUCTION.name.lower()
    deploy = True
    if environment == Environment.PREVIEW:
        env = Environment.PREVIEW.name.lower()
        deploy = False
    version: Tuple[str, str] = client.get_full_train_version(
        pipeline_id, container_version=CONTAINER_VERSION, environment=env, deploy=deploy
    )
    v, m = version
    LOG.info(f"Detected pretrained model {m} ({v})")
    return m


def autodetect_pretrained_model(environment: Environment = Environment.PRODUCTION) -> str:
    assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
    client = VeselkaClient()
    env = Environment.PRODUCTION.name.lower()
    if environment == Environment.PREVIEW:
        env = Environment.PREVIEW.name.lower()
    version: Tuple[str, str] = client.get_pretrain_version(CONTAINER_VERSION, environment=env)
    v, m = version
    LOG.info(f"Detected pretrained model {m} ({v})")
    return m


def autodetect_driptape_model(min_test_oec: float, environment: Environment = Environment.PRODUCTION) -> str:
    assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
    client = VeselkaClient()
    env = Environment.PRODUCTION.name.lower()
    deploy = True
    if environment == Environment.PREVIEW:
        env = Environment.PREVIEW.name.lower()
        deploy = False
    version: Tuple[str, str] = client.get_driptape_version(
        CONTAINER_VERSION, min_test_oec, environment=env, deploy=deploy
    )
    v, m = version
    LOG.info(f"Detected driptape model {m} ({v})")
    return m


def autodetect_embedding_balancing_model(
    pipeline_id: str,
    environment: Environment = Environment.PRODUCTION,
    sub_type: DeepweedTrainingSubtype = DeepweedTrainingSubtype.FULL_TRAIN,
) -> Optional[str]:
    assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
    client = VeselkaClient()
    env = Environment.PRODUCTION
    deploy = True
    if environment == Environment.PREVIEW:
        env = Environment.PREVIEW
        deploy = False
    model = client.get_embedding_balancing_model(
        container_version=CONTAINER_VERSION,
        pipeline_id=pipeline_id,
        sub_type=sub_type,
        environment=env,
        deploy=deploy,
        embedding_version=EMBEDDING_VERSION,
    )

    if model is None:
        return None

    model_id = cast(str, model["id"])
    return model_id


def get_dataset_split_image_count(dataset_id: str, split: str) -> int:
    try:
        with open(f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/{split}.json") as f:
            data = json.load(f)
        return len(data["images"])
    except FileNotFoundError:
        with open(f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.jsonl") as f:
            return sum(1 for _ in f)


def autodetect_production_comparison_model() -> Dict[str, str]:
    return VeselkaClient().get_production_comparison_model()


def pick_comparison_model_id(comparison_model_id: Optional[str], parent_comparison_model_id: Optional[str]) -> str:
    source = ""
    if comparison_model_id is not None:
        source = "passed in comparison_model_id"
        comparison_model_id = comparison_model_id
    elif parent_comparison_model_id is not None:
        source = "parent comparison_model_id"
        comparison_model_id = parent_comparison_model_id
    else:
        source = "production detected comparison_model_id"
        production_model = autodetect_production_comparison_model()
        comparison_model_id = production_model["id"] if production_model is not None else None

    LOG.info(f"Using comparison model {comparison_model_id} via {source}")

    return comparison_model_id


def run_debuggable(main: Callable[[], None]) -> None:
    try:
        main()
    except:  # noqa
        if os.getenv("TRAINER_DEBUG"):
            if torch.distributed.get_rank() == 0:
                import pdb
                import sys
                import traceback

                extype, value, tb = sys.exc_info()
                traceback.print_exc()
                pdb.post_mortem(tb)
        else:
            raise


def update_dl_config_dict_for_embedding_balancing(
    dl_config_dict: Dict[str, Any],
    pipeline_id: str,
    environment: Environment,
    sub_type: DeepweedTrainingSubtype,
    fallback_sampling_algorithm: str,
) -> Dict[str, Any]:
    if (
        dl_config_dict.get("sampling_algorithm")
        in [
            DATASET_SAMPLING_ALGORITHM_EMBEDDINGS,
            DATASET_SAMPLING_ALGORITHM_CATEGORY_EMBEDDINGS,
            DATASET_SAMPLING_ALGORITHM_EMBEDDING_CLUSTERS,
        ]
        and dl_config_dict.get("embedding_balancing_model") is None
        and CONTAINER_VERSION
    ):
        embedding_balancing_model = autodetect_embedding_balancing_model(
            pipeline_id=pipeline_id, environment=environment, sub_type=sub_type,
        )

        if embedding_balancing_model is None:
            LOG.warning(
                f"Could not find an embedding model for balancing with parameters pipeline_id={pipeline_id}, embedding_version={EMBEDDING_VERSION}, container_version={CONTAINER_VERSION}, environment={environment.name.lower()}, sub_type={sub_type.name.lower()}, falling back to {fallback_sampling_algorithm}"
            )
            dl_config_dict["evaluate_embeddings_for_new_points"] = False
            dl_config_dict["sampling_algorithm"] = fallback_sampling_algorithm
        else:
            LOG.info(f"Detected embeddings model: {embedding_balancing_model}")
            dl_config_dict["evaluate_embeddings_for_new_points"] = True
            dl_config_dict["embedding_balancing_model"] = embedding_balancing_model

    return dl_config_dict

import json
import logging
import os
from typing import Optional

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.furrows.config import FurrowsConfig
from deeplearning.p2p.config import P2PConfig
from deeplearning.utils.download_utils import download_records
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder, set_verbosity

set_verbosity(False)


LOG = logging.getLogger(__name__)


class TrainingInfo:
    def __init__(self, model_id: str, include_points_db: bool = False):
        self.model_id = model_id
        self.data_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/{model_id}")

        if os.path.exists(self.data_dir):
            LOG.info("Model directory already exists. Attempting to use pre-downloaded data.")
        else:
            with duration_perf_recorder(f"{PerfCategory.TRAINING}", "download_pretrained_records"):
                download_records(model_id, s3_directory="models", include_points_db=include_points_db)

        with open(os.path.join(self.data_dir, "veselka-metadata.json")) as f:
            self._veselka_metadata = json.load(f)

        self.crop = self._veselka_metadata.get("crop")
        self.dl_config = self._veselka_metadata.get("dl_config")
        self.pipeline_id = self._veselka_metadata.get("pipeline_id")
        self.train_csv = os.path.join(self.data_dir, "splits/train.csv")
        self.validation_csv = os.path.join(self.data_dir, "splits/validation.csv")
        self.test_csv = os.path.join(self.data_dir, "splits/test.csv")
        self.model_weights = f"/data/deeplearning/models/{self.model_id}/ckpt/latest_with_metadata.ckpt"
        self.best_model_weights = f"/data/deeplearning/models/{self.model_id}/best_checkpoint.ckpt"
        self.dataset_id = None
        self.recommended_wpt = self._veselka_metadata.get("weed_point_threshold", 0.5)
        self.recommended_cpt = self._veselka_metadata.get("crop_point_threshold", 0.5)
        self.family_id = self._veselka_metadata.get("family_id", None)
        self.comparison_model_id = self._veselka_metadata.get("comparison_model_id")
        trt_s3_path = self._veselka_metadata.get("url")
        self.trt_path = None
        if trt_s3_path is not None:
            self.trt_path = os.path.join(self.data_dir, os.path.basename(trt_s3_path))

        self.emphasized_geohashes = None
        if self._veselka_metadata.get("geohash_prefix") is not None:
            self.emphasized_geohashes = self._veselka_metadata.get("geohash_prefix").split(",")

        self.container_version = self._veselka_metadata.get("container_version", "")

        if "dataset_id" in self._veselka_metadata.keys():
            self.dataset_id = self._veselka_metadata["dataset_id"]
        self.int8_calibration = f"/data/deeplearning/models/{self.model_id}/trt_int8.calib"

        self.model_metadata = (
            json.loads(self._veselka_metadata["metadata_json"]) if "metadata_json" in self._veselka_metadata else None
        )

        self._p2p_config: Optional[P2PConfig] = None
        self._furrows_config: Optional[FurrowsConfig] = None
        self._deepweed_config: Optional[DeepweedConfig] = None

    @property
    def deepweed_config(self) -> Optional[DeepweedConfig]:
        if self._deepweed_config is None:
            if self.dl_config is None:
                return None
            self._deepweed_config = DeepweedConfig.from_dict(self.dl_config)
        return self._deepweed_config

    @property
    def furrows_config(self) -> FurrowsConfig:
        if self._furrows_config is None:
            if self.dl_config is None:
                self._furrows_config = FurrowsConfig()
            else:
                self._furrows_config = FurrowsConfig.from_dict(self.dl_config)
        return self._furrows_config

    @property
    def p2p_config(self) -> Optional[P2PConfig]:
        if self._p2p_config is None:
            if self.dl_config is None:
                return None
            self._p2p_config = P2PConfig.from_dict(self.dl_config)
        return self._p2p_config

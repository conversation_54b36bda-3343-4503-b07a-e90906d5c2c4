import logging
import os
from typing import Any, Dict, List, Optional

import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import REMOTE_VESELKA_DATASET_SERVER_PORT
from deeplearning.deepweed.trainer import DeepweedTrainer
from deeplearning.scripts.utils.training_info import CARBON_DATA_DIR, TrainingInfo
from deeplearning.scripts.utils.utils import remove_files_from_dataset_json
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.trainer import get_tensorrt_file_name

LOG = logging.getLogger(__name__)


def run_inference_on_file(
    trainer: DeepweedTrainer,
    dl_config: DeepweedConfig,
    eval_json: str,
    crops: List[str],
    weeds: List[str],
    segmentation_classes: Optional[List[str]],
    training_info: TrainingInfo,
    eval_dir: str,
    save_image_point_embeddings: bool = True,
    save_hdf5_embeddings: bool = False,
    dataset_id: str = "",
) -> None:
    trainer.veselka_dataset(
        config=dl_config,
        train_filepath=eval_json,
        validation_filepath=eval_json,
        test_filepath=eval_json,
        num_samples=64,
        crop_classes=tuple(crops),
        weed_classes=tuple(weeds),
        segm_classes=tuple(segmentation_classes) if segmentation_classes is not None else None,
        test_segm_classes=tuple(segmentation_classes) if segmentation_classes is not None else None,
        train_ppi=200,
        port=REMOTE_VESELKA_DATASET_SERVER_PORT + 1,
    )

    trt_model_path = training_info.trt_path
    if trt_model_path is None:
        trt_model_path = os.path.join(training_info.data_dir, get_tensorrt_file_name(convert_int8=True))
        if not os.path.exists(trt_model_path):
            trt_model_path = os.path.join(training_info.data_dir, get_tensorrt_file_name(convert_int8=False))

    trainer.infer(
        config=dl_config,
        ci_run=True,
        trt_path=trt_model_path,
        evaluation_dir=eval_dir,
        convert_to_sql_metrics=True,
        save_image_point_embeddings=save_image_point_embeddings,
        save_hdf5_embeddings=save_hdf5_embeddings,
        dataset_id=dataset_id,
    )


def evaluate_embeddings_on_dataset(
    embedding_balancing_model: str,
    dataset_id: str,
    dl_config_dict: Dict[str, Any],
    fast_run: bool,
    crops: List[str],
    weeds: List[str],
    save_image_point_embeddings: bool = True,
    save_hdf5_embeddings: bool = False,
) -> None:
    embedding_model_eval_filepath: Optional[str] = None
    embedding_training_info: Optional[TrainingInfo] = None

    with DeepweedTrainer() as trainer2:
        eval_dir = f"{CARBON_DATA_DIR}/deeplearning/embeddings/{embedding_balancing_model}"
        if torch.distributed.get_rank() == 0:
            embedding_training_info = TrainingInfo(embedding_balancing_model)
            assert embedding_training_info.model_metadata.get(
                "trained_embeddings"
            ) and embedding_training_info.model_metadata.get(
                "contains_pumap_head"
            ), f"embedding_balancing_model {embedding_balancing_model} was not trained for embeddings"
            if os.path.exists(os.path.join(eval_dir, "test_dataframes/points_v2.db")):
                os.remove(os.path.join(eval_dir, "test_dataframes/points_v2.db"))

            old_files = set()

            for _, _, files in os.walk(eval_dir):
                for file in files:
                    if file.endswith(".pt"):
                        old_files.add(os.path.splitext(file)[0])

            # Remove files if they already exist in the embeddings directory
            embedding_model_eval_filepath, new_number_images = remove_files_from_dataset_json(
                f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json", old_files, "id"
            )

            LOG.info(
                f"Skipping {len(old_files)} images from embedding extraction (this leaves {new_number_images} to evaluate)."
            )
        embedding_model_eval_filepath_obj = broadcast_object(embedding_model_eval_filepath)
        embedding_training_info_obj = broadcast_object(embedding_training_info)

        eval_config_dict = {
            "model_id": embedding_balancing_model,
            "fast_run": fast_run,
            **dl_config_dict,
        }
        del eval_config_dict["embedding_balancing_model"]
        eval_config = DeepweedConfig.from_dict(eval_config_dict)

        run_inference_on_file(
            trainer=trainer2,
            dl_config=eval_config,
            eval_json=embedding_model_eval_filepath_obj,
            crops=crops,
            weeds=weeds,
            segmentation_classes=None,
            training_info=embedding_training_info_obj,
            eval_dir=eval_dir,
            save_image_point_embeddings=save_image_point_embeddings,
            save_hdf5_embeddings=save_hdf5_embeddings,
        )
        del embedding_training_info
        del embedding_model_eval_filepath

import glob
import os
from argparse import Argument<PERSON><PERSON><PERSON>
from typing import Any

import matplotlib.pyplot as plt
import pandas as pd
import tqdm
from sklearn.metrics import accuracy_score, confusion_matrix, precision_recall_fscore_support

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.embeddings.utils import get_embedding_hdf5_s3_path
from lib.common.s3_cache_proxy.client import S3CacheProxyClient


def save_stats(query_df: Any, title: str, dir: str) -> None:
    print(f"{title}")
    classes = query_df["prediction"].unique()
    classes.sort()

    os.makedirs(f"{dir}/graphs", exist_ok=True)

    cm = confusion_matrix(query_df["prediction"], query_df["label"], labels=classes)
    df_cm = pd.DataFrame(
        cm / (cm.sum(axis=0) + 1),
        columns=[f"l: {c} ({t})" for t, c in zip(cm.sum(axis=0), classes)],
        index=[f"p: {c} ({t})" for t, c in zip(cm.sum(axis=1), classes)],
    )
    pd.set_option("display.max_rows", None)
    pd.set_option("display.max_columns", None)
    print(df_cm)

    precision, recall, _, _ = precision_recall_fscore_support(query_df["label"], query_df["prediction"], labels=classes)
    if os.path.exists(f"{dir}/graphs/stats.txt"):
        os.remove(f"{dir}/graphs/stats.txt")

    with open(f"{dir}/graphs/stats.txt", "w") as f:
        f.write(f"{title}\n")
        avg_acc = accuracy_score(query_df["label"], query_df["prediction"])
        print(f"Average Accuracy: {avg_acc:}")
        f.write(f"Average Accuracy: {avg_acc}\n")
        for i in range(len(classes)):
            class_prec, class_rec = precision[i], recall[i]
            f.write(f"{classes[i]} Precision: {class_prec}\n")
            f.write(f"{classes[i]} Recall: {class_rec}\n")

            print(f"\t{classes[i]} Precision: {class_prec}")
            print(f"\t{classes[i]} Recall: {class_rec}")
            print("")

        f.write("\n")
        f.write("Confusion Matrix:\n")
        f.write(df_cm.to_string())

    accuracies = (
        query_df.groupby("block_index")
        .apply(lambda x: accuracy_score(x["label"], x["prediction"]))
        .sort_values(ignore_index=True, ascending=False)
    )
    plt.plot(accuracies, label=f"std: {accuracies.std():}")
    plt.title(f"Mean Accuracy per block - {title}")
    plt.legend()
    plt.ylim(0.7, 1)
    plt.xlabel("Block Index (sorted)")
    plt.ylabel("Accuracy")
    plt.savefig(f"{dir}/graphs/accuracy.png")
    plt.clf()


def convert_csv_and_load(dir: str) -> Any:
    datablocks = glob.glob(f"{dir}/*.jsonl")
    pathq = f"{dir}/query.csv"
    paths = f"{dir}/support.csv"

    if os.path.exists(pathq) and os.path.exists(paths):
        print("Loading existing CSVs...")
        query_df = pd.read_csv(pathq)
        support_df = pd.read_csv(paths)
    elif len(datablocks):
        print("Generating CSVs...")
        support_dfs = []
        query_dfs = []

        for i in tqdm.tqdm(range(0, len(datablocks))):
            block = datablocks[i]
            block_df = pd.read_json(block, lines=True)
            block_df.episode_index
            block_df["support_set"] = block_df.apply(
                lambda row: [
                    {**d, "episode": row["episode_index"], "block_index": i, "filename": block.split("/")[-1]}
                    for d in row["support_set"]
                ],
                axis=1,
            )
            block_df["query_set"] = block_df.apply(
                lambda row: [
                    {**d, "episode": row["episode_index"], "block_index": i, "filename": block.split("/")[-1]}
                    for d in row["query_set"]
                ],
                axis=1,
            )

            support_dfs.append(pd.DataFrame(block_df.support_set.sum()))
            query_dfs.append(pd.DataFrame(block_df.query_set.sum()))

        support_df = pd.concat(support_dfs, axis=0)
        query_df = pd.concat(query_dfs, axis=0)

        query_df.to_csv(pathq)
        support_df.to_csv(paths)
    else:
        return None, None

    return support_df, query_df


if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument("--dataset-id", type=str)
    parser.add_argument("--model-id", type=str)
    parser.add_argument(
        "--dirname", type=str
    )  # Timestamp if dirname was unset during evaluation, or directory name if that was set manually during evaluation
    parser.add_argument("--title", type=str, default=None)  # Figure titles
    parser.add_argument(
        "--get-s3", action="store_true", default=False
    )  # Will grab from s3 and overwrite existing files for this run
    args = parser.parse_args()

    s3_key = f"embeddings/{args.dataset_id}/{args.model_id}/evaluations/{args.dirname}"
    s3_embedding_key = get_embedding_hdf5_s3_path(args.dataset_id, args.model_id)

    embedding_path = f"{CARBON_DATA_DIR}/deeplearning/{s3_embedding_key}"
    query_path = f"{CARBON_DATA_DIR}/deeplearning/{s3_key}/query.csv"
    support_path = f"{CARBON_DATA_DIR}/deeplearning/{s3_key}/support.csv"
    out_dir = f"{CARBON_DATA_DIR}/deeplearning/{s3_key}/"

    if args.get_s3:
        print("Downloading from S3...")
        bucket = "carbon-ml"
        s3 = S3CacheProxyClient(s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"), timeout=30)

        s3.download(bucket, f"{s3_key}/query.csv", query_path, exist_ok=True)
        s3.download(bucket, f"{s3_key}/support.csv", support_path, exist_ok=True)
        s3.download(bucket, f"{s3_embedding_key}", embedding_path, exist_ok=True)

    query_df = pd.read_csv(query_path)
    if not args.title:
        args.title = args.dirname
    save_stats(query_df, args.title, out_dir)

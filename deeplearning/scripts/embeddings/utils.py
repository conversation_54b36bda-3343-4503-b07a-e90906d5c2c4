import json
import logging
import os
from typing import List, Optional

import boto3

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.utils.download_utils import download_records
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")

EMBEDDING_S3_BUCKET = "carbon-ml"


def get_embedding_hdf5_s3_path(dataset_id: str, model_id: str, version_id: Optional[str] = None) -> str:
    path = f"embeddings/{dataset_id}/{model_id}"
    if version_id is not None:
        path += f"/datasets/{version_id}"
    path += "/test-embeddings.hdf5"

    return path


def get_local_embedding_filepath(dataset_id: str, model_id: str, version_id: Optional[str] = None) -> str:
    path = f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dataset_id}/{model_id}"
    if version_id is not None:
        path += f"/datasets/{version_id}"
    path += "/test-embeddings.hdf5"

    return path


def get_ordered_s3_dataset_versions(dataset_id: str, model_id: str) -> List[str]:
    s3 = boto3.client("s3")
    embedding_dataset_objects = s3.list_objects(
        Bucket=EMBEDDING_S3_BUCKET, Prefix=f"embeddings/{dataset_id}/{model_id}/datasets"
    )
    if "Contents" not in embedding_dataset_objects:
        return []
    sorted_keys_and_timestamps = sorted(
        [
            (obj["Key"], obj["LastModified"])
            for obj in embedding_dataset_objects["Contents"]
            if obj["Key"].endswith("test-embeddings.hdf5")
        ],
        key=lambda x: x[1],
        reverse=True,
    )
    return [tup[0].split("/")[-2] for tup in sorted_keys_and_timestamps]


def get_embedding_hdf5_file(dataset_id: str, model_id: str, version_id: Optional[str] = None) -> str:
    embeddings_s3_key = get_embedding_hdf5_s3_path(dataset_id, model_id, version_id)
    embeddings_filepath = get_local_embedding_filepath(dataset_id, model_id, version_id)

    s3_cache_proxy_host = os.getenv("S3_CACHE_PROXY_HOST")
    s3_cache_proxy_client = S3CacheProxyClient(s3_cache_proxy_host)

    if not os.path.exists(embeddings_filepath):
        s3_cache_proxy_client.download(EMBEDDING_S3_BUCKET, embeddings_s3_key, embeddings_filepath)
        LOG.info(f"Downloaded {embeddings_s3_key} -> {embeddings_filepath}")
    return embeddings_filepath


def get_embedding_evaluation_dir(dataset_id: str, model_id: str, evaluation_id: str) -> str:
    download_records(
        evaluation_id,
        f"embeddings/{dataset_id}/{model_id}/evaluations",
        bucket=EMBEDDING_S3_BUCKET,
        skip_existing_files=True,
    )
    return f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dataset_id}/{model_id}/evaluations/{evaluation_id}"


def get_latest_version_id_if_it_exists(dataset_id: str, model_id: str) -> Optional[str]:
    version_id = None
    versions = get_ordered_s3_dataset_versions(dataset_id, model_id)
    if len(versions) > 0:
        version_id = versions[0]

    return version_id


def upload_embedding_hdf5(file: str, dataset_id: str, model_id: str, version_id: Optional[str] = None) -> None:
    bucket = boto3.resource("s3").Bucket(EMBEDDING_S3_BUCKET)

    s3_path = get_embedding_hdf5_s3_path(dataset_id, model_id, version_id)
    bucket.upload_file(file, s3_path)

    LOG.info(f"Uploaded {file} -> s3://{EMBEDDING_S3_BUCKET}/{s3_path}")


def upload_embedding_evaluation_dir(directory: str, s3_prefix: str) -> None:
    bucket = boto3.resource("s3").Bucket(EMBEDDING_S3_BUCKET)

    for root, _, files in os.walk(directory):
        for file in files:
            src_file = os.path.join(root, file)
            relative_path = os.path.relpath(root, directory)
            relative_path = relative_path.replace(".", "")
            dest_file = os.path.join(s3_prefix, relative_path, file)
            dest_file = dest_file.replace(os.sep, "/")
            bucket.upload_file(src_file, dest_file)
            LOG.info(f"Uploaded {src_file} -> {dest_file}")


def log_evaluation_results(directory: str) -> None:

    filenames = os.listdir(directory)
    max_filename_length = max(len(filename) for filename in filenames)

    LOG.info(" " * (max_filename_length + 2) + "Mean    Max    Min    Median    Num Datapoints    Num Episodes")

    mean_accuracies = []
    for filename in os.listdir(directory):

        filepath = os.path.join(directory, filename)

        data = []
        with open(filepath, "r") as f:
            for line in f.readlines():
                episode = json.loads(line)

                data.append(episode)

        accuracies = []
        for episode in data:

            datapoints = episode["data"]

            num_datapoints = len(datapoints)
            num_correct = len([datapoint for datapoint in datapoints if datapoint["label"] == datapoint["prediction"]])

            accuracy = num_correct / num_datapoints

            accuracies.append(accuracy)

        mean_accuracy = sum(accuracies) / len(accuracies)
        max_accuracy = max(accuracies)
        min_accuracy = min(accuracies)
        median_accuracy = sorted(accuracies)[len(accuracies) // 2]

        mean_accuracies.append(mean_accuracy)

        LOG.info(
            f"{filename}: {mean_accuracy:1.3f}   {max_accuracy:1.3f}  {min_accuracy:1.3f}  {median_accuracy:1.3f}     {len(datapoints)}                {len(data)}"
        )
    LOG.info(f"Mean of mean accuracies: {sum(mean_accuracies) / len(mean_accuracies)}")

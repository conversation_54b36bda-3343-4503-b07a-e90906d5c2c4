import argparse
import logging
import os
import warnings

import boto3
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.metrics import accuracy_score

from deeplearning.constants import CARBON_DATA_DIR, CARBON_ML_BUCKET
from lib.common.time import maka_control_timestamp_ms

warnings.simplefilter(action="ignore", category=FutureWarning)

logging.basicConfig()
logging.getLogger().setLevel(logging.INFO)

LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")

SCATTER_POINT_SIZE = 5


def main() -> None:
    parser = argparse.ArgumentParser()

    parser.add_argument("--model-ids", type=str, required=True)
    parser.add_argument("--dataset-ids", type=str, required=True)
    parser.add_argument("--eval-names", type=str, required=True)
    parser.add_argument("--labels", type=str, required=False)  # Names for the legend
    parser.add_argument(
        "--out-dir", type=str, required=False
    )  # Default save: f"{CARBON_DATA_DIR}/deeplearning/embeddings/plots"
    parser.add_argument("--out-name", type=str, required=False)  # Name for title and filename
    parser.add_argument("--scatter", action="store_true")
    parser.add_argument("--match-datablocks", action="store_true")
    parser.add_argument("--point-size", type=float, default=SCATTER_POINT_SIZE)
    parser.add_argument("--ymax", type=float, default=None)
    parser.add_argument("--ymin", type=float, default=None)

    args = parser.parse_args()

    model_ids = args.model_ids.split(",")
    dataset_ids = args.dataset_ids.split(",")
    folders = args.eval_names.split(",")
    point_size = args.point_size

    maxlen = max(len(model_ids), len(dataset_ids), len(folders))

    model_ids = model_ids * maxlen if len(model_ids) == 1 else model_ids
    dataset_ids = dataset_ids * maxlen if len(dataset_ids) == 1 else dataset_ids
    folders = folders * maxlen if len(folders) == 1 else folders

    if args.labels is None:
        labels = [" ".join(f.split("_")[:-1]) for f in folders]
    else:
        labels = args.labels.split(",")

    if args.out_dir is None:
        args.out_dir = f"{CARBON_DATA_DIR}/deeplearning/embeddings/plots"

    s3_client = boto3.client("s3")

    keeps = None
    for dataset_id, model_id, f, label in zip(dataset_ids, model_ids, folders, labels):
        path = f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dataset_id}/{model_id}/evaluations/{f}"
        os.makedirs(path, exist_ok=True)
        qpath = f"{path}/query.csv"
        if not os.path.exists(qpath):
            try:
                s3_key = f"embeddings/{dataset_id}/{model_id}/evaluations/{f}/./query.csv"
                LOG.info(f"Downloading {s3_key} from S3...")
                s3_client.download_file(Filename=qpath, Bucket=CARBON_ML_BUCKET, Key=s3_key)
            except Exception:
                try:
                    s3_key = f"embeddings/{dataset_id}/{model_id}/evaluations/{f}/query.csv"
                    LOG.info(f"Downloading {s3_key} from S3...")
                    s3_client.download_file(Filename=qpath, Bucket=CARBON_ML_BUCKET, Key=s3_key)
                except Exception:
                    LOG.warning(f"Could not download {s3_key} from S3")
                    exit(1)
        q = pd.read_csv(qpath)

        if args.match_datablocks:
            if keeps is None:
                keeps = set(q.filename.unique().tolist())
            q = q[q.filename.isin(keeps)]

        accs = q.groupby("block_index").apply(lambda x: accuracy_score(x["label"], x["prediction"]))
        meanacc = accs.mean()
        if args.scatter:
            plt.scatter(
                range(accs.shape[0]),
                accs.sort_values(ascending=False),
                label=label + f" ({meanacc:.3f})",
                s=[point_size] * accs.shape[0],
            )
        else:
            plt.plot(range(accs.shape[0]), accs.sort_values(ascending=False), label=label + f" ({meanacc:.3f})")

    plt.ylim(args.ymin, args.ymax)

    order = np.arange(len(folders))
    handles, labels = plt.gca().get_legend_handles_labels()
    plt.legend([handles[idx] for idx in order], [labels[idx] for idx in order])
    plt.ylabel("Accuracy")
    plt.xlabel("Datablock")
    os.makedirs(args.out_dir, exist_ok=True)
    if args.out_name is not None:
        plt.title(args.out_name)
        out = os.path.join(args.out_dir, f"{args.out_name}.png")
        LOG.info(f"Saving plot to {out}")
        plt.savefig(out, format="png", dpi=500)
        return
    out = os.path.join(args.out_dir, f"{maka_control_timestamp_ms()}.png")
    LOG.info(f"Saving plot to {out}")
    plt.savefig(out, format="png", dpi=500)


if __name__ == "__main__":
    main()

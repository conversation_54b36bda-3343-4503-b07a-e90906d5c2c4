import argparse
import logging
import traceback

import torch

from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.deepweed.embed import build_config as build_dw_config
from deeplearning.scripts.deepweed.embed import run_embed as run_dw_embed
from deeplearning.scripts.embeddings.evaluate import (
    DEFAULT_DISTANCE_METRIC,
    DEFAULT_K,
    DEFAULT_MAX_DISTANCE_MM,
    DEFAULT_MIN_EXAMPLES_PER_CLASS,
    DEFAULT_MIN_PLANT_SCORE,
    DEFAULT_MODEL_TYPE,
    DEFAULT_NUM_SUPPORT_SAMPLES,
    get_ignore_classes,
    get_weed_classes,
    run_evaluation,
)
from deeplearning.scripts.embeddings.utils import get_embedding_hdf5_file, get_latest_version_id_if_it_exists
from deeplearning.scripts.utils.utils import add_job_creator_arguments
from lib.common.time.time import maka_control_timestamp_ms

logging.basicConfig()

LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")


def main() -> None:  # noqa: C901
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-ids", type=str, required=True)
    parser.add_argument("--model-ids", type=str, required=True)
    parser.add_argument("--version-ids", type=str, default=None)

    parser.add_argument("--model-types", type=str, default=f"{DEFAULT_MODEL_TYPE}")
    parser.add_argument("--distance-metrics", type=str, default=f"{DEFAULT_DISTANCE_METRIC}")
    parser.add_argument("--k-values", type=str, default=f"{DEFAULT_K}")
    parser.add_argument("--num-support-samples", type=str, default=f"{DEFAULT_NUM_SUPPORT_SAMPLES}")
    parser.add_argument("--min-plant-scores", type=str, default=f"{DEFAULT_MIN_PLANT_SCORE}")
    parser.add_argument("--max-distances", type=str, default=f"{DEFAULT_MAX_DISTANCE_MM}")
    parser.add_argument("--min-examples-per-class", type=str, default=f"{DEFAULT_MIN_EXAMPLES_PER_CLASS}")
    parser.add_argument("--prediction-based-analysis", type=str, default="False")
    parser.add_argument("--multiclass", type=str, default="False")
    parser.add_argument("--out-dir-prefix", type=str, default=f"{maka_control_timestamp_ms()}")
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument("--nproc-per-node", type=int, default=torch.cuda.device_count())

    add_job_creator_arguments(parser)

    parser.set_defaults(fast_run=False)
    parser.set_defaults(nproc_per_node=torch.cuda.device_count())

    args = parser.parse_args()

    dataset_ids = args.dataset_ids.split(",")
    model_ids = args.model_ids.split(",")

    version_ids = args.version_ids.split(",") if args.version_ids is not None else [None] * len(model_ids)

    model_types = args.model_types.split(",")
    distance_metrics = args.distance_metrics.split(",")
    ks = [int(k) for k in args.k_values.split(",")]
    num_support_samples = [int(item) for item in args.num_support_samples.split(",")]
    min_plant_scores = [float(item) for item in args.min_plant_scores.split(",")]
    max_distances = [float(item) for item in args.max_distances.split(",")]
    min_examples_per_class = [int(item) for item in args.min_examples_per_class.split(",")]
    prediction_based_analysis = [eval(item) for item in args.prediction_based_analysis.split(",")]
    multiclass = [eval(item) for item in args.multiclass.split(",")]

    assert len(dataset_ids) == len(model_ids)

    evaluated_outdirs = []

    for i, dataset_id in enumerate(dataset_ids):
        model_id = model_ids[i]
        version_id = version_ids[i]

        if version_id is None:
            get_latest_version_id_if_it_exists(dataset_id, model_id)

        LOG.info(f"Attempting to use version {version_id}")

        should_run_embed = False
        try:
            embedding_hdf5_file = get_embedding_hdf5_file(dataset_id, model_id, version_id)
            LOG.info(f"Found embedding hdf5 file {embedding_hdf5_file}")
        except Exception as e:
            LOG.warning(f"Could not find embedding file for dataset {dataset_id}, model {model_id}: {e}")
            should_run_embed = True

        if should_run_embed:
            LOG.info(f"Running embed for dataset {dataset_id}, model {model_id}")
            eval_config = build_dw_config(
                model_id=model_id, fast_run=args.fast_run, save_reduce_scaled=False, dl_config=args.dl_config
            )
            embedding_hdf5_file = run_dw_embed(
                model_id=model_id, eval_config=eval_config, nproc_per_node=args.nproc_per_node
            )

        version_id = EmbeddingDataset(embedding_hdf5_file).metadata.version_id

        logline = ""

        for model_type in model_types:
            for distance in distance_metrics:
                for k in ks:
                    for num_support_sample in num_support_samples:
                        for min_plant_score in min_plant_scores:
                            for max_dist in max_distances:
                                for min_examples_per_clas in min_examples_per_class:
                                    for pred_based_analysis in prediction_based_analysis:
                                        for multiclas in multiclass:
                                            logline = f"\tmodel_type = {model_type}\n"
                                            logline += f"\tdistance_metric = {distance}\n"
                                            logline += f"\tk = {k}\n"
                                            logline += f"\tnum_support_samples = {num_support_sample}\n"
                                            logline += f"\tmin_plant_score = {min_plant_score}\n"
                                            logline += f"\tmax_distance = {max_dist}\n"
                                            logline += f"\tmin_examples_per_class = {min_examples_per_clas}\n"
                                            logline += f"\tprediction_based_analysis = {pred_based_analysis}\n"
                                            logline += f"\tmulticlass = {multiclas}\n"
                                            outdir_name = f"{args.out_dir_prefix}_{model_type}_{distance}_{k}_{num_support_sample}_{min_plant_score}_{max_dist}_{min_examples_per_clas}_{pred_based_analysis}_{multiclas}"
                                            LOG.info(f"Running\n\toutdir_name = {outdir_name},\n{logline}")

                                            weed_classes = get_weed_classes(None, multiclas)
                                            ignore_classes = get_ignore_classes(None, multiclas)

                                            try:
                                                run_evaluation(
                                                    dataset_id=dataset_id,
                                                    model_id=model_id,
                                                    model_type=model_type,
                                                    distance_metric=distance,
                                                    k=k,
                                                    num_support_samples=num_support_sample,
                                                    max_distance=max_dist,
                                                    min_plant_score=min_plant_score,
                                                    min_examples_per_class=min_examples_per_clas,
                                                    prediction_based_analysis=pred_based_analysis,
                                                    multiclass=multiclas,
                                                    global_weeds=multiclas,
                                                    out_dirname=outdir_name,
                                                    weed_classes=weed_classes,
                                                    ignore_classes=ignore_classes,
                                                    version_id=version_id,
                                                )
                                                evaluated_outdirs.append(outdir_name)
                                            except Exception as e:
                                                LOG.error(f"Could not run {outdir_name}: {e}")
                                                LOG.error(traceback.format_exc())

    LOG.info(f"Pushed {evaluated_outdirs} evaluations")


if __name__ == "__main__":
    main()

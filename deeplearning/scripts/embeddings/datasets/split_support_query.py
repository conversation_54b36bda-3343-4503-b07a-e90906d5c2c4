import random
from collections import defaultdict
from typing import Any, Callable, Dict, Iterator, <PERSON>, Optional, Tu<PERSON>, Union, cast

import numpy as np
import torch

import fewshot
from deeplearning.embeddings.io import EmbeddingDatapoint, EmbeddingDataset
from deeplearning.scripts.embeddings.datasets.label_helpers import transform_label_to_weed_crop


def get_max_category(category_scores: Dict[str, float]) -> str:
    max_score = 0.0
    max_category = None
    for category, score in category_scores.items():
        if score > max_score:
            max_category = category
            max_score = score

    assert max_category is not None

    return max_category


def find_closest_prediction(
    datapoint: EmbeddingDatapoint,
    min_score: float,
    max_distance: float,
    multiclass: bool = False,
    weed_classes: Optional[List[str]] = None,
) -> Tuple[Optional[str], Optional[torch.Tensor]]:
    weed_classes = weed_classes if weed_classes is not None else []
    min_index = None
    min_distance = None
    for i, metadata in enumerate(datapoint.predictions_metadata):
        if metadata.distance_mm > max_distance or metadata.plant_score < min_score:
            continue
        if min_index is None or metadata.distance_mm < min_distance:
            min_index = i
            min_distance = metadata.distance_mm

    if min_index is None:
        return None, None

    metadata = datapoint.predictions_metadata[min_index]

    prediction_label = "crop"

    if metadata.weed_score > metadata.crop_score:
        prediction_label = transform_label_to_weed_crop(
            get_max_category(metadata.category_scores).lower(), multiclass=multiclass, weed_classes=weed_classes
        )

    return prediction_label, datapoint.predictions_embeddings[min_index]


def create_indices_by_class_and_metadata(
    dataset: EmbeddingDataset,
    data: List[int],
    labels: List[str],
    min_score: float,
    max_distance: float,
    multiclass: bool = False,
    weed_classes: Optional[List[str]] = None,
    enable_shuffle: bool = False,
) -> Tuple[Dict[str, List[int]], Dict[str, List[int]], List[Optional[Dict[str, Any]]], List[int], List[str]]:
    weed_classes = weed_classes if weed_classes is not None else []
    zipped = list(zip(data, labels))
    if enable_shuffle:
        random.shuffle(zipped)
    data, labels = cast(Tuple[List[int], List[str]], zip(*zipped))

    indices_by_class_with_predictions = defaultdict(list)
    indices_by_class_without_predictions = defaultdict(list)
    metadata_list: List[Optional[Dict[str, Any]]] = []

    for i in range(len(data)):
        index = data[i]
        label = labels[i]
        metadata = None
        if len(dataset[index].predictions_metadata) > 0:
            min_metadata, min_embedding = find_closest_prediction(
                dataset[index], min_score, max_distance, multiclass=multiclass, weed_classes=weed_classes
            )
            if min_metadata is not None:
                indices_by_class_with_predictions[label].append(i)
                other_meta = dataset[index].predictions_metadata[0]
                metadata = {
                    "prediction_label": min_metadata,
                    "prediction_embedding": min_embedding,
                    "plant_score": other_meta.plant_score,
                    "weed_score": other_meta.weed_score,
                    "crop_score": other_meta.crop_score,
                }
                metadata_list.append(metadata)
                continue

        indices_by_class_without_predictions[label].append(i)
        metadata_list.append(metadata)

    return indices_by_class_with_predictions, indices_by_class_without_predictions, metadata_list, data, labels


def split_into_roles(
    dataset: EmbeddingDataset,
    data: List[int],
    labels: List[str],
    min_score: float,
    max_distance: float,
    min_per_class_in_support_set: int = 5,
    multiclass: bool = False,
    weed_classes: Optional[List[str]] = None,
    min_query_examples: int = 0,
) -> Tuple[
    Dict[fewshot.dataset_types.DatapointRole, List[int]],
    Dict[fewshot.dataset_types.DatapointRole, List[str]],
    Dict[fewshot.dataset_types.DatapointRole, List[Optional[Dict[str, Any]]]],
]:
    weed_classes = weed_classes if weed_classes is not None else []
    divided_data: Dict[fewshot.dataset_types.DatapointRole, List[int]] = defaultdict(list)
    divided_labels: Dict[fewshot.dataset_types.DatapointRole, List[str]] = defaultdict(list)
    divided_metadata: Dict[fewshot.dataset_types.DatapointRole, List[Optional[Dict[str, Any]]]] = defaultdict(list)

    (
        indices_by_class_with_predictions,
        indices_by_class_without_predictions,
        metadata_list,
        data,
        labels,
    ) = create_indices_by_class_and_metadata(
        dataset=dataset,
        data=data,
        labels=labels,
        min_score=min_score,
        max_distance=max_distance,
        multiclass=multiclass,
        weed_classes=weed_classes,
        enable_shuffle=True,
    )

    unique_labels = list(set(labels))
    support_indices = []
    query_indices = []

    for label in unique_labels:
        support_points = indices_by_class_without_predictions[label]
        while len(support_points) < min_per_class_in_support_set and len(indices_by_class_with_predictions[label]):
            support_points.append(indices_by_class_with_predictions[label].pop())
        assert len(support_points) >= min_per_class_in_support_set
        query_points = indices_by_class_with_predictions[label]

        support_indices.extend(support_points)
        query_indices.extend(query_points)
    assert len(query_indices) >= min_query_examples

    for i in support_indices:
        divided_data[fewshot.dataset_types.DatapointRole.SUPPORT].append(data[i])
        divided_labels[fewshot.dataset_types.DatapointRole.SUPPORT].append(labels[i])
        divided_metadata[fewshot.dataset_types.DatapointRole.SUPPORT].append(metadata_list[i])
    for i in query_indices:
        divided_data[fewshot.dataset_types.DatapointRole.QUERY].append(data[i])
        divided_labels[fewshot.dataset_types.DatapointRole.QUERY].append(labels[i])
        divided_metadata[fewshot.dataset_types.DatapointRole.QUERY].append(metadata_list[i])

    return divided_data, divided_labels, divided_metadata


class SplitSupportQueryDataset(fewshot.Dataset):
    def __init__(
        self,
        dataset: EmbeddingDataset,
        data: List[int],
        labels: List[str],
        load_fn: Callable[[Any], Any],
        transform_fn: Callable[[Any], torch.Tensor],
        min_score: float,
        max_distance: float,
        min_per_class_in_support_set: int = 5,
        multiclass: bool = False,
        weed_classes: Optional[List[str]] = None,
        min_query_examples: int = 0,
    ) -> None:
        weed_classes = weed_classes if weed_classes is not None else []
        self._data = data
        self._labels = labels

        divided_data, divided_label, divided_metadata = split_into_roles(
            dataset,
            data,
            labels,
            min_score,
            max_distance,
            min_per_class_in_support_set,
            multiclass=multiclass,
            weed_classes=weed_classes,
            min_query_examples=min_query_examples,
        )

        self._support = fewshot.Dataset(
            data=divided_data[fewshot.dataset_types.DatapointRole.SUPPORT],
            labels=divided_label[fewshot.dataset_types.DatapointRole.SUPPORT],
            load_fn=load_fn,
            transform_fn=transform_fn,
            metadata=divided_metadata[fewshot.dataset_types.DatapointRole.SUPPORT],
        )

        self._query = fewshot.Dataset(
            data=divided_data[fewshot.dataset_types.DatapointRole.QUERY],
            labels=divided_label[fewshot.dataset_types.DatapointRole.QUERY],
            load_fn=load_fn,
            transform_fn=transform_fn,
            metadata=divided_metadata[fewshot.dataset_types.DatapointRole.QUERY],
        )

        for query_class in self._query.class_names:
            assert query_class in self._support.class_names

    @property
    def class_names(self) -> List[str]:
        return cast(List[str], self._support.class_names)

    @property
    def support(self) -> fewshot.Dataset:
        return self._support

    @property
    def query(self) -> fewshot.Dataset:
        return self._query

    def __getitem__(
        self, item: Union[int, Tuple[int, fewshot.dataset_types.DatapointRole]]
    ) -> fewshot.dataset_types.Datapoint:
        index: int
        role: fewshot.dataset_types.DatapointRole

        if isinstance(item, int):
            index = item
            role = fewshot.dataset_types.DatapointRole.QUERY
        else:
            index, role = item

        if role == fewshot.dataset_types.DatapointRole.SUPPORT:
            return self._support[(index, role)]
        else:
            return self._query[(index, role)]


class SplitSupportQuerySampler(fewshot.samplers.Sampler):
    # Samples support and query examples from disjoint sets determined by matched deepweed predictions, rather than from a shared pool like fewshot.sampler
    def __init__(
        self,
        dataset: SplitSupportQueryDataset,
        num_classes: int,
        num_support_samples: int,
        num_query_samples: int,
        num_episodes: int,
        seed: int = 1000,
    ):
        self._dataset = dataset
        self._num_classes = num_classes
        self._num_support_samples = num_support_samples
        self._num_query_samples = num_query_samples
        self._num_episodes = num_episodes

        self._rng = np.random.RandomState(seed)

    def __iter__(self) -> Iterator[List[Tuple[int, fewshot.dataset_types.DatapointRole]]]:
        for _ in range(self._num_episodes):
            class_names = self._rng.choice(self._dataset.support.class_names, self._num_classes, replace=False)
            class_names.sort()

            support_samples = []
            query_samples = []
            for class_name in class_names:
                if class_name != "Neither":
                    support_indices = self._dataset.support.name2indices[class_name]
                    query_indices = self._dataset.query.name2indices.get(class_name)

                    splits_to_iterate_on = [
                        (fewshot.dataset_types.DatapointRole.SUPPORT, support_indices, self._num_support_samples),
                    ]

                    if query_indices is not None:
                        splits_to_iterate_on.append(
                            (fewshot.dataset_types.DatapointRole.QUERY, query_indices, self._num_query_samples)
                        )

                    for (role, indices, num_samples) in splits_to_iterate_on:
                        for sample in self._rng.choice(indices, min(num_samples, len(indices)), replace=False):
                            if role == fewshot.dataset_types.DatapointRole.SUPPORT:
                                support_samples.append(int(sample))
                            else:
                                query_samples.append(int(sample))

            episode = [(a, fewshot.dataset_types.DatapointRole.QUERY) for a in query_samples] + [
                (a, fewshot.dataset_types.DatapointRole.SUPPORT) for a in support_samples
            ]

            yield episode

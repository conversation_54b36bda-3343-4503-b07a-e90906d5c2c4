from typing import List, Optional

import torch

import fewshot
from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.embeddings.constants import (
    DEFAULT_MAX_DISTANCE_MM,
    DEFAULT_MIN_PER_CLASS_IN_SUPPORT_SET,
    DEFAULT_MIN_PLANT_SCORE,
)
from deeplearning.scripts.embeddings.datasets.constants import SPLIT_SUPPORT_QUERY
from deeplearning.scripts.embeddings.datasets.label_helpers import transform_label_to_weed_crop
from deeplearning.scripts.embeddings.datasets.split_support_query import (
    SplitSupportQueryDataset,
    SplitSupportQuerySampler,
    create_indices_by_class_and_metadata,
)


class EmbeddingLoader:
    def __init__(self, dataset: EmbeddingDataset):
        self._dataset = dataset

    def __call__(self, identifier: str) -> torch.Tensor:
        return self._dataset[int(identifier)].embedding


def get_sampler(sampler: str = "default") -> fewshot.samplers.Sampler:
    if sampler == SPLIT_SUPPORT_QUERY:
        return SplitSupportQuerySampler
    return fewshot.samplers.Sampler


def transform_fn(x: torch.Tensor) -> torch.Tensor:
    return x


def get_labels(
    dataset: EmbeddingDataset,
    data_block: List[int],
    multiclass: bool = False,
    weed_classes: Optional[List[str]] = None,
) -> List[str]:
    weed_classes = weed_classes if weed_classes is not None else []
    labels = []
    for index in data_block:
        labels.append(
            transform_label_to_weed_crop(
                dataset[index].metadata.point_category_id.lower(), multiclass=multiclass, weed_classes=weed_classes
            )
        )

    return labels


def get_dataset(
    dataset: EmbeddingDataset,
    data_block: List[int],
    dataset_name: str = "default",
    multiclass: bool = False,
    weed_classes: Optional[List[str]] = None,
    min_score: float = DEFAULT_MIN_PLANT_SCORE,
    max_distance: float = DEFAULT_MAX_DISTANCE_MM,
    min_per_class_in_support_set: int = DEFAULT_MIN_PER_CLASS_IN_SUPPORT_SET,
    min_query_examples: int = 0,
    only_matches: bool = False,
) -> fewshot.Dataset:
    weed_classes = weed_classes if weed_classes is not None else []
    load_fn = EmbeddingLoader(dataset)

    labels = get_labels(dataset=dataset, data_block=data_block, multiclass=multiclass, weed_classes=weed_classes)

    if only_matches:  # Only use points with predictions attached
        (class_to_pred_indices, _, metadata_list, _, _) = create_indices_by_class_and_metadata(
            dataset=dataset,
            data=data_block,
            labels=labels,
            min_score=min_score,
            max_distance=max_distance,
            multiclass=multiclass,
            weed_classes=weed_classes,
            enable_shuffle=False,
        )
        pred_indices = sum(class_to_pred_indices.values(), [])
        return fewshot.Dataset(
            data=[data_block[i] for i in pred_indices],
            labels=[labels[i] for i in pred_indices],
            load_fn=load_fn,
            transform_fn=transform_fn,
            metadata=[metadata_list[i] for i in pred_indices],
        )
    elif dataset_name == SPLIT_SUPPORT_QUERY:
        return SplitSupportQueryDataset(
            dataset=dataset,
            data=data_block,
            labels=labels,
            load_fn=load_fn,
            transform_fn=transform_fn,
            min_score=min_score,
            max_distance=max_distance,
            min_per_class_in_support_set=min_per_class_in_support_set,
            multiclass=multiclass,
            weed_classes=weed_classes,
            min_query_examples=min_query_examples,
        )

    else:
        (_, _, metadata_list, _, _) = create_indices_by_class_and_metadata(
            dataset=dataset,
            data=data_block,
            labels=labels,
            min_score=min_score,
            max_distance=max_distance,
            multiclass=multiclass,
            weed_classes=weed_classes,
            enable_shuffle=False,
        )
        return fewshot.Dataset(
            data=data_block, labels=labels, load_fn=load_fn, transform_fn=transform_fn, metadata=metadata_list
        )

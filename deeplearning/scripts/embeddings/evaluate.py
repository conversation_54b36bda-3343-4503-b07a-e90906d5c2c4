import argparse
import datetime
import json
import logging
import multiprocessing
import os
from collections import defaultdict
from datetime import datetime as dt
from typing import Any, Dict, List, Optional, Tuple

import torch
from tqdm import tqdm

import fewshot
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
)
from deeplearning.scripts.embeddings.classifiers.sklearn_classifiers import SklearnClassifier
from deeplearning.scripts.embeddings.classifiers.utils import get_classifier
from deeplearning.scripts.embeddings.datasets.constants import SPLIT_SUPPORT_QUERY
from deeplearning.scripts.embeddings.datasets.split_support_query import SplitSupportQueryDataset
from deeplearning.scripts.embeddings.datasets.utils import get_dataset, get_sampler
from deeplearning.scripts.embeddings.get_episodic_graphs import convert_csv_and_load, save_stats
from deeplearning.scripts.embeddings.utils import (
    get_embedding_hdf5_file,
    get_ordered_s3_dataset_versions,
    upload_embedding_evaluation_dir,
)
from deeplearning.scripts.utils.utils import add_job_creator_arguments
from lib.common.time import maka_control_timestamp_ms

logging.basicConfig()
logging.getLogger().setLevel(logging.INFO)

LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")

GEOHASH_PRECISION = 4
DEFAULT_NUM_BLOCKS_PER_GPU = 4
DEFAULT_NUM_EPISODES = 250
DEFAULT_NUM_QUERY_SAMPLES = 16
DEFAULT_NUM_SUPPORT_SAMPLES = 50
DEFAULT_MIN_EXAMPLES_PER_CLASS = 75
DEFAULT_VOTE_WEIGHT = 1.0
DEFAULT_K = 1
DEFAULT_DISTANCE_METRIC = "cosine"
DEFAULT_MODEL_TYPE = "knn"
DEFAULT_OUT_DIRNAME = ""
DEFAULT_MAX_DISTANCE_MM = 5.0
DEFAULT_MIN_PLANT_SCORE = 0.5
DEFAULT_MIN_QUERY_EXAMPLES = 0


def save_metadata(output_dir: str, version_id: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> None:
    config = config if config is not None else {}
    with open(os.path.join(output_dir, "metadata.json"), "w") as f:
        json.dump({"dataset_version_id": version_id, "config": config}, f)


def generate_test_dataset(filepath: str, embedding_dimension: int = 128, num_datapoints: int = 1000) -> None:

    if os.path.exists(filepath):
        os.remove(filepath)

    dataset_metadata = EmbeddingDatasetMetadata(
        model_id="test", embedding_size=embedding_dimension, dataset_id="test-dataset-id"
    )

    dataset = EmbeddingDataset(filepath, dataset_metadata)

    for index in range(num_datapoints):

        if index < num_datapoints // 2:
            captured_at = 1715388382000  # May 10th, 2024
            geohash = "c26uxbnhccs5"  # Somewhere in eastern Washington
            point_category_id = "aaaa"
        else:
            captured_at = 1731721627000  # November 11th, 2024
            geohash = "9q3y9qpugqv3"  # Somewhere in Salinas California
            point_category_id = "bbbb"

        if index % 2 == 0:
            point_category_id = "aaaa"
        else:
            point_category_id = "bbbb"

        embedding = torch.randn(embedding_dimension)
        embedding_metadata = EmbeddingDatapointMetadata(
            image_id="test-image-id",
            label_id="test-label-id",
            captured_at=captured_at,
            geohash=geohash,
            point_category_id=point_category_id,
            image_crop_id="test-image-crop-id",
            x=0,
            y=0,
        )
        dataset.append(EmbeddingDatapoint(embedding, embedding_metadata))


def verify_arguments(args: Any) -> None:
    if args.model_type == "deepweed" or args.model_type == "knn_fallback_dw":
        assert (
            args.prediction_based_analysis or args.use_deepweed_with_embeddings
        ), f"--model-type {args.model_type} requires --prediction-based-analysis or --use-deepweed-with-embeddings"
    if args.model_type == "knn_no_prediction":
        assert (
            not args.use_split_support_query_dataset
        ), "If 'model_type' is 'knn_no_prediction', 'use_split_support_query_dataset' must be False."
    elif args.prediction_based_analysis:
        args.use_split_support_query_dataset = True


gpu_id_queue: "multiprocessing.Queue[int]" = multiprocessing.Queue()


def process_one_data_block(  # noqa: C901
    dataset: EmbeddingDataset,
    data_block: List[int],
    key: str,
    model: fewshot.models.BaseModel,
    config: fewshot.configs.EvaluationConfig,
    min_examples_per_class: int,
    weed_classes: List[str],
    multiclass: bool = False,
    weed_indices: Optional[List[int]] = None,
    min_plant_score: float = DEFAULT_MIN_PLANT_SCORE,
    max_distance: float = DEFAULT_MAX_DISTANCE_MM,
    min_query_examples: int = 0,
    use_split_support_query_dataset: bool = False,
    only_matches: bool = False,
) -> Optional[Tuple[float, float]]:

    if weed_indices is not None:
        data_block += weed_indices

    try:
        data_block_dataset = get_dataset(
            dataset,
            data_block,
            SPLIT_SUPPORT_QUERY if use_split_support_query_dataset else "default",
            multiclass=multiclass,
            weed_classes=weed_classes,
            min_score=min_plant_score,
            max_distance=max_distance,
            min_per_class_in_support_set=config.num_support_samples,
            min_query_examples=min_query_examples,
            only_matches=only_matches,
        )
    except Exception as e:
        LOG.warning(f"Couldn't get dataset: {e}")
        return None

    config.logging_prefix = key
    evaluate = True

    if type(data_block_dataset) == SplitSupportQueryDataset:
        total_query_indices = 0
        for name, indices in data_block_dataset.support.name2indices.items():
            support_size = len(indices)
            query_size = len(data_block_dataset.query.name2indices.get(name, []))

            if support_size + query_size < min_examples_per_class:
                evaluate = False
                break
        for name, support_indices in data_block_dataset.support.name2indices.items():
            log_line = f"{name} (support): {len(support_indices)} datapoints, "

            if len(support_indices) < config.num_support_samples:
                evaluate = False
                break

            number_query_indices = len(data_block_dataset.query.name2indices.get(name, []))
            log_line += f"(query): {number_query_indices} datapoints"
            LOG.debug(log_line)
            total_query_indices += number_query_indices

        # We don't require n per class query items, but we should maintain n per class in support set in query items
        if total_query_indices < len(data_block_dataset.support.name2indices) * config.num_query_samples:
            evaluate = False
    else:
        for name, indices in data_block_dataset.name2indices.items():
            if len(indices) < min_examples_per_class:
                LOG.debug("Not enough datapoints for evaluation")
                evaluate = False
                break
    if len(data_block_dataset.class_names) != config.num_classes:
        evaluate = False

    if not evaluate:
        return None

    gpu_id = gpu_id_queue.get()

    dataloader: torch.utils.data.DataLoader[fewshot.Episode] = torch.utils.data.DataLoader(
        data_block_dataset,
        batch_sampler=get_sampler(SPLIT_SUPPORT_QUERY if use_split_support_query_dataset else "default")(
            data_block_dataset,
            num_classes=config.num_classes,
            num_support_samples=config.num_support_samples,
            num_query_samples=config.num_query_samples,
            num_episodes=config.num_episodes,
            seed=gpu_id,
        ),
        collate_fn=fewshot.utilities.episode_collate_fn,
        num_workers=0,
        pin_memory=False,
    )

    if not isinstance(model, SklearnClassifier):
        model.to(gpu_id)

    loss_fn = torch.nn.CrossEntropyLoss()

    model.eval()
    losses = []
    accuracies = []
    for episode_index, episode in enumerate(dataloader):
        if not isinstance(model, SklearnClassifier):
            episode = episode.to(gpu_id)

        with torch.no_grad():
            logits, ids = model(episode)
            loss = loss_fn(logits, episode.y)
            accuracy = (logits.argmax(dim=1) == episode.y).float().mean()

        fewshot.utilities.per_episode_logging(
            episode=episode,
            logits=logits,
            config=config,
            rank=gpu_id,
            episode_index=episode_index,
            closest_image_ids=ids,
        )

        losses.append(loss.item())
        accuracies.append(accuracy.item())

    loss = sum(losses) / len(losses)
    accuracy = sum(accuracies) / len(accuracies)

    gpu_id_queue.put(gpu_id)

    return loss, accuracy


def get_weed_classes(weed_classes: Optional[str], multiclass: bool) -> List[str]:
    if weed_classes is None:
        if multiclass:
            weed_classes = "broadleaf,purslane,grass"
        else:
            weed_classes = "broadleaf,purslane,grass,offshoot"

    return weed_classes.split(",")


def get_ignore_classes(ignore_classes: Optional[str], multiclass: bool) -> List[str]:
    if ignore_classes is None:
        if multiclass:
            ignore_classes = "offshoot"
        else:
            ignore_classes = ""

    return ignore_classes.split(",")


def get_datablocks(
    dataset: EmbeddingDataset,
    crop_id: Optional[str],
    global_weeds: bool,
    multiclass: bool,
    weed_classes: List[str],
    ignore_classes: List[str],
) -> Tuple[Dict[str, List[int]], List[int]]:
    weed_indices = []
    items_discarded = 0

    data_blocks: Dict[str, List[int]] = defaultdict(
        list
    )  # We need to use defaultdict(list) to avoid empty list as a value.
    for index in tqdm(range(len(dataset))):
        metadata = dataset.get_metadata(index)
        geohash = metadata.geohash[:GEOHASH_PRECISION]
        date = datetime.datetime.fromtimestamp(metadata.captured_at / 1000).date()
        key = f"{geohash}-{date}"

        if crop_id is not None and metadata.image_crop_id != crop_id:
            items_discarded += 1
            continue
        elif global_weeds and metadata.point_category_id in weed_classes:
            weed_indices.append(index)
            continue
        elif multiclass and metadata.point_category_id in ignore_classes:
            items_discarded += 1
            continue

        data_blocks[key].append(index)

    LOG.info(f"Found {len(data_blocks)} data blocks")
    LOG.info(f"Discarded {items_discarded} items")

    return data_blocks, weed_indices


def add_args(parser: argparse.ArgumentParser) -> None:
    parser.add_argument("--model-id", type=str, required=True)
    parser.add_argument("--dataset-id", type=str, required=True)
    parser.add_argument("--num-blocks-per-gpu", type=int, default=DEFAULT_NUM_BLOCKS_PER_GPU)
    parser.add_argument("--num-query-samples", type=int, default=DEFAULT_NUM_QUERY_SAMPLES)
    parser.add_argument("--num-support-samples", type=int, default=DEFAULT_NUM_SUPPORT_SAMPLES)
    parser.add_argument("--num-episodes", type=int, default=DEFAULT_NUM_EPISODES)
    parser.add_argument(
        "--model-type", type=str, help="Classifier type, e.g. knn, centroid, deepweed", default=DEFAULT_MODEL_TYPE
    )
    parser.add_argument(
        "--distance-metric", help="cosine, euclidean, or manhattan", type=str, default=DEFAULT_DISTANCE_METRIC
    )
    parser.add_argument("--k", type=int, help="K only if metric is knn", default=DEFAULT_K)
    parser.add_argument(
        "--min-query-examples",
        help="Only relevant for prediction-based analysis, min. required query examples",
        type=int,
        default=DEFAULT_MIN_QUERY_EXAMPLES,
    )
    parser.add_argument("--out-dirname", type=str, default="")
    parser.add_argument("--vote-weight", help="Experimental KNN parameter", type=float, default=DEFAULT_VOTE_WEIGHT)
    parser.add_argument("--min-examples-per-class", type=int, default=DEFAULT_MIN_EXAMPLES_PER_CLASS)
    parser.add_argument("--multiclass", action="store_true")
    parser.add_argument("--global-weeds", help="Uses all weeds for each datablock", action="store_true")
    parser.add_argument("--weed-classes", type=str, help="Comma separated list of weed classes", default=None)
    parser.add_argument("--ignore-classes", type=str, help="Comma separated list of classes to ignore", default=None)
    parser.add_argument(
        "--prediction-based-analysis",
        help="Uses deepweed prediction locations for evaluation rather than label location",
        action="store_true",
    )
    parser.add_argument("--max-distance", type=float, default=DEFAULT_MAX_DISTANCE_MM)
    parser.add_argument("--min-plant-score", type=float, default=DEFAULT_MIN_PLANT_SCORE)
    parser.add_argument(
        "--threshold", type=float, help="Threshold if model-type is threshold", default=0.5
    )  # Threshold if model-type is threshold
    parser.add_argument(
        "--threshold-diff", help="Threshold difference if model-type is threshold", type=float, default=0.0
    )
    parser.add_argument("--normalize", action="store_true", help="Normalize embeddings (for sklearn only for now)")
    parser.add_argument("--crop-id", help="Only evaluate crops with this ID", type=str, default=None)
    parser.add_argument("--version-id", help="Evaluate on embedding dataset with this version", type=str, default=None)
    parser.add_argument(
        "--use-split-support-query-dataset", help="Use split support query dataset", action="store_true"
    )
    parser.add_argument(
        "--use-deepweed-with-embeddings",
        help="Adds deepweed predictions to the embeddings, only for use with sklearn classifiers",
        action="store_true",
    )
    parser.add_argument(
        "--fallback-threshold", help="Fallback threshold if model-type is threshold", type=float, default=0.1
    )


def main() -> None:  # noqa: C901
    parser = argparse.ArgumentParser()

    add_args(parser)
    add_job_creator_arguments(parser)
    start = dt.now()

    parser.set_defaults(test=False)

    args = parser.parse_args()

    verify_arguments(args)

    weed_classes = get_weed_classes(args.weed_classes, args.multiclass)
    ignore_classes = get_ignore_classes(args.ignore_classes, args.multiclass)

    run_evaluation(
        model_type=args.model_type,
        distance_metric=args.distance_metric,
        k=args.k,
        vote_weight=args.vote_weight,
        min_examples_per_class=args.min_examples_per_class,
        multiclass=args.multiclass,
        global_weeds=args.global_weeds,
        weed_classes=weed_classes,
        ignore_classes=ignore_classes,
        out_dirname=args.out_dirname,
        dataset_id=args.dataset_id,
        model_id=args.model_id,
        prediction_based_analysis=args.prediction_based_analysis,
        num_support_samples=args.num_support_samples,
        num_query_samples=args.num_query_samples,
        num_episodes=args.num_episodes,
        num_blocks_per_gpu=args.num_blocks_per_gpu,
        min_plant_score=args.min_plant_score,
        max_distance=args.max_distance,
        version_id=args.version_id,
        threshold=args.threshold,
        threshold_diff=args.threshold_diff,
        normalize=args.normalize,
        min_query_examples=args.min_query_examples,
        crop_id=args.crop_id,
        use_split_support_query_dataset=args.use_split_support_query_dataset,
        use_deepweed_with_embeddings=args.use_deepweed_with_embeddings,
        fallback_threshold=args.fallback_threshold,
    )

    end = dt.now()
    elapsed = end - start
    LOG.info(
        "Evaluation took: %02d:%02d:%02d:%02d"
        % (elapsed.days, elapsed.seconds // 3600, elapsed.seconds // 60 % 60, elapsed.seconds % 60)
    )


def run_evaluation(  # noqa: C901
    dataset_id: str,
    model_id: str,
    model_type: str = DEFAULT_MODEL_TYPE,
    distance_metric: str = DEFAULT_DISTANCE_METRIC,
    k: int = DEFAULT_K,
    vote_weight: float = DEFAULT_VOTE_WEIGHT,
    min_examples_per_class: int = DEFAULT_MIN_EXAMPLES_PER_CLASS,
    multiclass: bool = False,
    global_weeds: bool = False,
    weed_classes: Optional[List[str]] = None,
    ignore_classes: Optional[List[str]] = None,
    out_dirname: str = DEFAULT_OUT_DIRNAME,
    prediction_based_analysis: bool = False,
    num_support_samples: int = DEFAULT_NUM_SUPPORT_SAMPLES,
    num_query_samples: int = DEFAULT_NUM_QUERY_SAMPLES,
    num_episodes: int = DEFAULT_NUM_EPISODES,
    num_blocks_per_gpu: int = DEFAULT_NUM_BLOCKS_PER_GPU,
    min_plant_score: float = DEFAULT_MIN_PLANT_SCORE,
    max_distance: float = DEFAULT_MAX_DISTANCE_MM,
    version_id: Optional[str] = None,
    threshold: float = 0.5,
    threshold_diff: float = 0.0,
    normalize: bool = False,
    min_query_examples: int = 0,
    crop_id: Optional[str] = None,
    use_split_support_query_dataset: bool = False,
    use_deepweed_with_embeddings: bool = False,
    fallback_threshold: float = 0.1,
) -> None:
    weed_classes = weed_classes if weed_classes is not None else []
    ignore_classes = ignore_classes if ignore_classes is not None else []
    if multiclass:
        num_classes = 1 + len(weed_classes)
    else:
        num_classes = 2

    LOG.info("Running embeddings evaluation script")

    timestamp = maka_control_timestamp_ms()

    if out_dirname:
        s3_output_prefix = f"embeddings/{dataset_id}/{model_id}/evaluations/{out_dirname}"
    elif crop_id is not None:
        s3_output_prefix = f"embeddings/{dataset_id}/{model_id}/evaluations/{crop_id}-{timestamp}"
    else:
        s3_output_prefix = f"embeddings/{dataset_id}/{model_id}/evaluations/{timestamp}"

    output_dir = f"{CARBON_DATA_DIR}/deeplearning/{s3_output_prefix}"
    if os.path.exists(output_dir):
        assert len(os.listdir(output_dir)) == 0, f"Output directory {output_dir} is not empty"

    # Default to the latest version of dataset id
    if version_id is None:
        versions = get_ordered_s3_dataset_versions(dataset_id, model_id)
        if len(versions) > 0:
            version_id = versions[0]

    embeddings_filepath = get_embedding_hdf5_file(dataset_id, model_id, version_id=version_id)

    dataset = EmbeddingDataset(embeddings_filepath)

    LOG.info("Gathering data blocks for evaluation")

    data_blocks, weed_indices = get_datablocks(
        dataset, crop_id, global_weeds, multiclass, weed_classes, ignore_classes,
    )

    num_data_blocks = len(data_blocks)
    model = get_classifier(
        dataset.metadata.embedding_size,
        model_type,
        distance_metric,
        k,
        vote_weight,
        get_embedding_from_prediction=prediction_based_analysis,
        threshold=threshold,
        threshold_diff=threshold_diff,
        normalize=normalize,
        fallback_threshold=fallback_threshold,
        use_dw_embeddings_for_prediction=use_deepweed_with_embeddings,
    )

    if isinstance(model, SklearnClassifier):
        num_blocks_per_gpu = 1

    eval_config = fewshot.configs.EvaluationConfig(
        model_id=dataset.metadata.model_id,
        data_dir=output_dir,
        num_gpus=1,
        num_support_samples=num_support_samples,
        num_query_samples=num_query_samples,
        num_classes=num_classes,
        num_episodes=num_episodes,
    )

    for i in range(num_blocks_per_gpu):
        for gpu_id in range(torch.cuda.device_count()):
            gpu_id_queue.put(gpu_id)

    with multiprocessing.Pool(
        num_blocks_per_gpu * torch.cuda.device_count(), maxtasksperchild=15
    ) as pool:  # Maxtasksperchild is set to 15 to avoid OOM with global weeds
        futures = []
        for key, data_block in data_blocks.items():
            futures.append(
                (
                    pool.apply_async(
                        process_one_data_block,
                        kwds={
                            "dataset": dataset,
                            "data_block": data_block,
                            "key": key,
                            "model": model,
                            "config": eval_config,
                            "min_examples_per_class": min_examples_per_class,
                            "weed_classes": weed_classes,
                            "multiclass": multiclass,
                            "weed_indices": weed_indices if len(weed_indices) > 0 else None,
                            "min_plant_score": min_plant_score,
                            "max_distance": max_distance,
                            "min_query_examples": min_query_examples,
                            "use_split_support_query_dataset": use_split_support_query_dataset,
                            "only_matches": use_deepweed_with_embeddings,
                        },
                    ),
                    key,
                    data_block,
                )
            )

        for i, (future, key, data_block) in enumerate(futures):
            result = future.get()
            if result is not None:
                loss, accuracy = result
                LOG.info(
                    f"[ {i} / {num_data_blocks} ] {key}: {len(data_block)} datapoints | Loss: {loss} | Accuracy: {accuracy}"
                )

    _, query_df = convert_csv_and_load(output_dir)
    if query_df is None:
        LOG.info("No evaluation data found, skipping saving stats")
    else:
        save_stats(query_df, out_dirname, output_dir)
        save_metadata(output_dir=output_dir, version_id=version_id, config=eval_config.model_dump())
    if out_dirname != "test":
        upload_embedding_evaluation_dir(output_dir, s3_output_prefix)


if __name__ == "__main__":
    main()

from typing import Tuple

import sklearn.ensemble
import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.encoders import BaseEncoder
from fewshot.models import BaseModel


class RandomForestClassifier(BaseModel):
    def __init__(self, encoder: BaseEncoder, get_embedding_from_prediction: bool = False):
        super().__init__()

        self._encoder = encoder
        self.get_embedding_from_prediction = get_embedding_from_prediction

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def forward(self, episode: Episode) -> Tuple[torch.Tensor, None]:
        embeddings = self._encoder(episode.x)
        device = embeddings.device

        embeddings = embeddings.cpu()

        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        classes = []
        support_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.SUPPORT:
                support_indices.append(index)
                classes.append(class_to_num[datapoint.label])

        support_embeddings = embeddings[support_indices]  # ns, ed

        if len(support_embeddings.shape) == 1:
            support_embeddings = support_embeddings.unsqueeze(0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        if self.get_embedding_from_prediction:
            query_set = torch.stack([episode.datapoints[i].metadata["prediction_embedding"] for i in query_indices]).to(
                device=embeddings.device
            )
        else:
            query_set = embeddings[query_indices]  # nq, ed

        classifier = sklearn.ensemble.RandomForestClassifier()
        classifier.fit(support_embeddings, classes)

        prediction = classifier.predict_proba(query_set)

        return torch.from_numpy(prediction).to(device), None

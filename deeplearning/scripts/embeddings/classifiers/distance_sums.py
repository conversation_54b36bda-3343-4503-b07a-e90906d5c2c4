from typing import Any, <PERSON><PERSON>

import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.encoders import BaseEncoder
from fewshot.models import BaseModel


class DistanceSumClassifier(BaseModel):
    # https://carbonrobotics.atlassian.net/wiki/spaces/Engineering/pages/891748365/01+03+2025+-+Distance+Sum+Classifier+for+Embeddings
    def __init__(
        self, encoder: BaseEncoder, metric: str = "euclidean", get_embedding_from_prediction: bool = False,
    ):
        super().__init__()

        self._encoder = encoder
        self.metric = metric
        self.get_embedding_from_prediction = get_embedding_from_prediction

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> Any:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        return x1_norm @ x2_norm.t()

    def forward(self, episode: Episode) -> Tuple[torch.Tensor, None]:
        embeddings = self._encoder(episode.x)

        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        class_to_indices: Any = {}
        support_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.SUPPORT:
                support_indices.append(index)
                if class_to_num[datapoint.label] not in class_to_indices:
                    class_to_indices[class_to_num[datapoint.label]] = []
                class_to_indices[class_to_num[datapoint.label]].append(len(support_indices) - 1)

        c2i = {
            class_name: torch.tensor(indices).to(embeddings.device) for class_name, indices in class_to_indices.items()
        }

        len_first = len(c2i[0])
        if not sum([len(c) == len_first for c in c2i.values()]):
            print("what")

        support_embeddings = embeddings[support_indices]

        if len(support_embeddings.shape) == 1:
            support_embeddings = support_embeddings.unsqueeze(0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        if self.get_embedding_from_prediction:
            query_set = torch.stack([episode.datapoints[i].metadata["prediction_embedding"] for i in query_indices]).to(
                device=embeddings.device
            )
        else:
            query_set = embeddings[query_indices]

        if self.metric == "cosine":
            distances = -self.cosine_cdist(support_embeddings, query_set)
        elif self.metric == "manhattan":
            distances = torch.cdist(support_embeddings, query_set, p=1)
        else:
            distances = torch.cdist(support_embeddings, query_set, p=2)
        class_dists = []
        for i in range(len(episode.class_names)):
            distances[c2i[i]].sum(dim=0)
            class_dists.append(distances[c2i[i]].sum(dim=0) / len(c2i[i]))

        pred_classes = torch.argmin(torch.stack(class_dists), dim=0)
        logits = torch.zeros(query_set.shape[0], len(episode.class_names)).to(embeddings.device)  # nc, nq
        logits[torch.arange(query_set.shape[0]), pred_classes] = 1

        return logits, None

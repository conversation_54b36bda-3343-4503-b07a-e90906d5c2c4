import fewshot
from deeplearning.scripts.embeddings.classifiers.centroids import CentroidClassifier
from deeplearning.scripts.embeddings.classifiers.deepweed import DeepweedClassifier
from deeplearning.scripts.embeddings.classifiers.distance_sums import DistanceSumClassifier
from deeplearning.scripts.embeddings.classifiers.knn import KNNClassifier
from deeplearning.scripts.embeddings.classifiers.knn_fallback_dw import KNNFallbackClassifier
from deeplearning.scripts.embeddings.classifiers.knn_no_prediction import KNNNoPredictionClassifier
from deeplearning.scripts.embeddings.classifiers.medoids import MedoidClassifier
from deeplearning.scripts.embeddings.classifiers.random_forest import RandomForestClassifier
from deeplearning.scripts.embeddings.classifiers.sklearn_classifiers import CLASSIFIER_MAP, SklearnClassifier
from deeplearning.scripts.embeddings.classifiers.threshold import ThresholdClassifier


def get_classifier(
    embedding_dimension: int,
    classifier: str,
    distance_metric: str,
    k: int = 5,
    vote_weight: float = 1.0,
    get_embedding_from_prediction: bool = False,
    threshold: float = 0.5,
    threshold_diff: float = 0.0,
    normalize: bool = False,
    fallback_threshold: float = 0.1,
    use_dw_embeddings_for_prediction: bool = False,
) -> fewshot.models.BaseModel:
    encoder = fewshot.encoders.EmbeddingsReader(embedding_size=embedding_dimension)
    if classifier == "centroid":
        model = CentroidClassifier(
            encoder=encoder, metric=distance_metric, get_embedding_from_prediction=get_embedding_from_prediction
        )
    elif classifier == "medoid":
        model = MedoidClassifier(
            encoder=encoder, metric=distance_metric, get_embedding_from_prediction=get_embedding_from_prediction
        )
    elif classifier == "knn":
        model = KNNClassifier(
            encoder=encoder,
            metric=distance_metric,
            k=k,
            vote_weight=vote_weight,
            get_embedding_from_prediction=get_embedding_from_prediction,
        )
    elif classifier == "knn_no_prediction":
        model = KNNNoPredictionClassifier(encoder=encoder, metric=distance_metric, k=k, vote_weight=vote_weight)
    elif classifier == "random_forest":
        model = RandomForestClassifier(encoder=encoder, get_embedding_from_prediction=get_embedding_from_prediction)
    elif classifier == "deepweed":
        model = DeepweedClassifier()
    elif classifier == "threshold":
        model = ThresholdClassifier(threshold=threshold, threshold_diff=threshold_diff)
    elif classifier == "distance_sums" or classifier == "distance_sum":
        model = DistanceSumClassifier(
            encoder=encoder, metric=distance_metric, get_embedding_from_prediction=get_embedding_from_prediction
        )
    elif classifier in CLASSIFIER_MAP:
        model = SklearnClassifier(
            encoder=encoder,
            classifier=classifier,
            get_embedding_from_prediction=get_embedding_from_prediction,
            norm=normalize,
            dw_help=use_dw_embeddings_for_prediction,
        )
    elif classifier == "knn_fallback_dw":
        model = KNNFallbackClassifier(encoder=encoder, metric=distance_metric, fallback_threshold=fallback_threshold)
    else:
        raise ValueError(f"Unknown classifier: {classifier}")

    return model

from typing import Any, List

import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.encoders import BaseEncoder
from fewshot.models import BaseModel


class KNNNoPredictionClassifier(BaseModel):
    def __init__(
        self, encoder: BaseEncoder, k: int = 5, metric: str = "euclidean", vote_weight: float = 1.0,
    ):
        super().__init__()

        self._encoder = encoder
        self.metric = metric
        self.k = k
        self.vote_weight = vote_weight

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> Any:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        return x1_norm @ x2_norm.t()

    def no_prediction_class_handler(
        self, logits: torch.Tensor, no_prediction_idx: int, no_prediction_list: List[int]
    ) -> torch.Tensor:
        logits[no_prediction_idx, :] = 0  # Default all logits to 0.
        logits[:, no_prediction_list] = 0  # Reset logits for "no_prediction" indices.
        logits[no_prediction_idx, no_prediction_list] = 1  # Set "no_prediction" index to 1.

        return logits

    def forward(self, episode: Episode) -> torch.Tensor:
        embeddings = self._encoder(episode.x)

        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        classes = []
        support_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.SUPPORT:
                support_indices.append(index)
                classes.append(class_to_num[datapoint.label])

        support_embeddings = embeddings[support_indices]
        support_classes = torch.tensor(classes).to(embeddings.device)
        class_to_indices = {i: torch.where(support_classes == i)[0] for i in range(len(episode.class_names))}

        if len(support_embeddings.shape) == 1:
            support_embeddings = support_embeddings.unsqueeze(0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        query_list = []
        no_prediction_list = []
        for idx, query_index in enumerate(query_indices):
            if episode.datapoints[query_index].metadata is not None:
                query_list.append(
                    episode.datapoints[query_index].metadata["prediction_embedding"].to(device=embeddings.device)
                )
            else:
                query_list.append(
                    embeddings[query_index].to(device=embeddings.device)
                )  # If the metadata is None, the embeddings are used directly. However, the class of this index will be assigned to no_prediction later. This step is only to ensure dimensional matching.
                no_prediction_list.append(idx)

        query_set = torch.stack(query_list).to(device=embeddings.device)

        if self.metric == "cosine":
            distances = -self.cosine_cdist(support_embeddings, query_set)
        elif self.metric == "manhattan":
            distances = torch.cdist(support_embeddings, query_set, p=1)
        else:
            distances = torch.cdist(support_embeddings, query_set, p=2)

        if self.k == 1:
            logits = torch.zeros(len(episode.class_names) + 1, query_set.shape[0]).to(
                embeddings.device
            )  # Add one more class for "no_prediction".
            for cls in range(len(episode.class_names)):
                logits[cls, :] = -torch.min(distances[class_to_indices[cls]], dim=0).values
            logits = self.no_prediction_class_handler(
                logits=logits, no_prediction_idx=len(episode.class_names), no_prediction_list=no_prediction_list
            ).to(embeddings.device)

            return logits.t()

        topk = torch.topk(distances, self.k, dim=0, largest=False)
        topk_indices = topk.indices
        topk_values = topk.values

        class_vectors = support_classes[topk_indices]
        logits = torch.zeros(len(episode.class_names) + 1, query_set.shape[0]).to(embeddings.device)

        for i in range(class_vectors.shape[0]):
            for j in range(class_vectors.shape[1]):
                if self.metric == "cosine":
                    logits[class_vectors[i, j], j] -= topk_values[i, j] ** self.vote_weight
                else:
                    logits[class_vectors[i, j], j] += 1 / (topk_values[i, j] ** self.vote_weight)

        logits = self.no_prediction_class_handler(
            logits=logits, no_prediction_idx=len(episode.class_names), no_prediction_list=no_prediction_list
        ).to(embeddings.device)

        return logits.t()

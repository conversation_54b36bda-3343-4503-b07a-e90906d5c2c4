from typing import <PERSON>ple

import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.models import BaseModel


class ThresholdClassifier(BaseModel):
    def __init__(self, threshold: float, threshold_diff: float) -> None:
        self.crop_threshold = threshold + threshold_diff
        self.weed_threshold = threshold - threshold_diff
        super().__init__()

    def forward(self, episode: Episode) -> Tuple[torch.Tensor, None]:
        metadata = [datapoint.metadata for datapoint in episode.datapoints]

        query_set = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_set.append(metadata[index])

        class_names = episode.class_names.copy()
        class_to_num = {class_name: i for i, class_name in enumerate(class_names)}

        logits = torch.zeros(len(query_set), len(episode.class_names)).to(device=episode.x.device)
        for i, meta in enumerate(query_set):
            if meta["crop_score"] > self.crop_threshold:
                logits[i, class_to_num["crop"]] = 1
            elif meta["weed_score"] > self.weed_threshold:
                logits[i, class_to_num["weed"]] = 1

        return logits, None

from typing import <PERSON>ple

import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.models import BaseModel


class DeepweedClassifier(BaseModel):
    def __init__(self) -> None:
        super().__init__()

    def forward(self, episode: Episode) -> Tuple[torch.Tensor, None]:
        metadata = [datapoint.metadata for datapoint in episode.datapoints]

        query_set = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_set.append(metadata[index])

        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        logits = torch.zeros(len(query_set), len(episode.class_names)).to(device=episode.x.device)
        for i, meta in enumerate(query_set):
            logits[i, class_to_num[meta["prediction_label"]]] = 1

        return logits, None

from typing import Any, Optional

import torch
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.encoders import BaseEncoder
from fewshot.models import BaseModel

CLASSIFIER_MAP = {
    "svc": SVC(kernel="linear", C=0.025, probability=True),
    "svc_nl": SVC(gamma=2, C=1, probability=True),
    "sig": LogisticRegression(max_iter=1000),
    "mlp": MLPClassifier(hidden_layer_sizes=(100,), max_iter=1000),
    "mlp2l": MLPClassifier(hidden_layer_sizes=(100, 100), max_iter=1000),
    "mlp3l": MLPClassifier(hidden_layer_sizes=(100, 100, 100), max_iter=1000),
    "dt": DecisionTreeClassifier(),
    "gnb": GaussianNB(),
    "rf": RandomForestClassifier(),
}

CLASSIFIER_DEFAULTS = {
    "svc": {"norm": False},
    "svc_nl": {"norm": False},
    "sig": {"norm": False},
    "mlp": {"norm": False},
    "mlp2l": {"norm": False},
    "mlp3l": {"norm": False},
    "dt": {"norm": False},
    "gnb": {"norm": False},
    "rf": {"norm": False},
}


class SklearnClassifier(BaseModel):
    def __init__(
        self,
        encoder: BaseEncoder,
        classifier: str,
        get_embedding_from_prediction: bool = False,
        norm: Optional[bool] = None,
        dw_help: bool = False,
    ) -> None:
        super().__init__()

        self._encoder = encoder
        self.classifier = CLASSIFIER_MAP[classifier]
        self.get_embedding_from_prediction = get_embedding_from_prediction
        self.norm = norm
        if norm is None:
            self.norm = CLASSIFIER_DEFAULTS[classifier]["norm"]
        self.dw_help = dw_help

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def forward(self, episode: Episode) -> Any:
        embeddings = self._encoder(episode.x)
        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        if self.norm:
            embeddings = embeddings / embeddings.norm(dim=1, keepdim=True)

        if self.dw_help:
            dw_pred_vec_confidence = torch.tensor(
                [[dp.metadata["weed_score"], dp.metadata["crop_score"]] for dp in episode.datapoints]
            ).to(embeddings.device)
            dw_pred_vec_class = torch.zeros(embeddings.shape[0], 1).to(embeddings.device)
            for i, dp in enumerate(episode.datapoints):
                dw_pred_vec_class[i] = 1 if dp.metadata["prediction_label"] == "crop" else 0
            dw_pred_vec = torch.cat((dw_pred_vec_confidence, dw_pred_vec_class), dim=1)

            if isinstance(self.classifier, RandomForestClassifier):
                dw_pred_vec = torch.tile(dw_pred_vec, (1, 100))
            embeddings = torch.cat((embeddings, dw_pred_vec), dim=1)

        classes = []
        support_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.SUPPORT:
                support_indices.append(index)
                classes.append(class_to_num[datapoint.label])

        support_embeddings = embeddings[support_indices]

        if len(support_embeddings.shape) == 1:
            support_embeddings = support_embeddings.unsqueeze(0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        if self.get_embedding_from_prediction:
            query_set = torch.stack([episode.datapoints[i].metadata["prediction_embedding"] for i in query_indices])
        else:
            query_set = embeddings[query_indices]  # nq, ed

        self.classifier.fit(support_embeddings, classes)

        logits = self.classifier.predict_proba(query_set)

        return torch.tensor(logits), None

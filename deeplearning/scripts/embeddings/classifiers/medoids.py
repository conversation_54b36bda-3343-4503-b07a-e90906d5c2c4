from typing import Any

import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.encoders import BaseEncoder
from fewshot.models import BaseModel


class MedoidClassifier(BaseModel):
    def __init__(self, encoder: BaseEncoder, metric: str = "euclidean", get_embedding_from_prediction: bool = False):
        super().__init__()

        self._encoder = encoder
        self.metric = metric
        self.get_embedding_from_prediction = get_embedding_from_prediction

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> Any:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        return x1_norm @ x2_norm.t()

    def forward(self, episode: Episode) -> Any:
        embeddings = self._encoder(episode.x)

        support_set_list = []
        for class_name in episode.class_names:
            support_indices = []
            for index, datapoint in enumerate(episode.datapoints):
                if datapoint.label == class_name and datapoint.role == DatapointRole.SUPPORT:
                    support_indices.append(index)

            support_embeddings = embeddings[support_indices]

            if len(support_embeddings.shape) == 1:
                support_embeddings = support_embeddings.unsqueeze(0)

            if self.metric == "cosine":
                dists = -self.cosine_cdist(support_embeddings, support_embeddings)
            elif self.metric == "euclidean":
                dists = torch.cdist(support_embeddings, support_embeddings)
            elif self.metric == "manhattan":
                dists = torch.cdist(support_embeddings, support_embeddings, p=1)

            support_medoid_index = torch.argmin(dists.sum(dim=1))
            support_set_list.append(support_embeddings[support_medoid_index])

        support_set = torch.stack(support_set_list, dim=0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        if self.get_embedding_from_prediction:
            query_set = torch.stack([episode.datapoints[i].metadata["prediction_embedding"] for i in query_indices]).to(
                device=embeddings.device
            )
        else:
            query_set = embeddings[query_indices]  # nq, ed

        if self.metric == "cosine":
            logits = self.cosine_cdist(query_set, support_set)
        elif self.metric == "euclidean":
            logits = -torch.cdist(query_set, support_set)
        else:
            logits = -torch.cdist(query_set, support_set, p=1)

        return logits, None

from typing import Any, <PERSON><PERSON>

import torch

from fewshot.dataset_types import DatapointRole, Episode
from fewshot.encoders import BaseEncoder
from fewshot.models import BaseModel


class KNNFallbackClassifier(BaseModel):
    def __init__(self, encoder: BaseEncoder, k: int = 1, metric: str = "cosine", fallback_threshold: float = 0.1):
        super().__init__()

        self._encoder = encoder
        self.metric = metric
        self.k = k
        self.fallback_threshold = fallback_threshold

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> Any:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        return x1_norm @ x2_norm.t()

    # flake8: noqa: C901
    def forward(self, episode: Episode) -> Tuple[torch.Tensor, Any]:
        embeddings = self._encoder(episode.x)

        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        classes = []
        support_indices = []
        support_datapoints = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.SUPPORT:
                support_indices.append(index)
                classes.append(class_to_num[datapoint.label])
                support_datapoints.append(datapoint)

        support_embeddings = embeddings[support_indices]
        support_classes = torch.tensor(classes).to(embeddings.device)
        class_to_indices = {i: torch.where(support_classes == i)[0] for i in range(len(episode.class_names))}

        if len(support_embeddings.shape) == 1:
            support_embeddings = support_embeddings.unsqueeze(0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        query_set = embeddings[query_indices]

        if self.metric == "euclidean":
            distances = torch.cdist(support_embeddings, query_set, p=2)
        elif self.metric == "manhattan":
            distances = torch.cdist(support_embeddings, query_set, p=1)
        elif self.metric == "dot-product":
            distances = torch.mm(support_embeddings, query_set.T)
        else:
            distances = -self.cosine_cdist(support_embeddings, query_set)

        logits = torch.zeros(len(episode.class_names), query_set.shape[0]).to(embeddings.device)
        ids = torch.zeros(len(episode.class_names), query_set.shape[0]).to(embeddings.device)
        for cls in range(len(episode.class_names)):
            mins = torch.min(distances[class_to_indices[cls]], dim=0)
            class_indices = class_to_indices[cls]
            for q in range(query_set.shape[0]):
                ids[cls, q] = support_datapoints[class_indices[mins.indices[q]]].identifier

            logits[cls, :] = -mins.values

        two_closest_classes = logits.topk(2, dim=0).values
        dw_pred_indices = torch.where(
            torch.abs(two_closest_classes[0, :] - two_closest_classes[1, :]) < self.fallback_threshold, 1, 0
        )

        for i in range(dw_pred_indices.shape[0]):
            if dw_pred_indices[i] == 1:
                dp = episode.datapoints[query_indices[i]]
                logits[:, i] = 0
                logits[class_to_num[dp.metadata["prediction_label"]], i] = 1

        return logits.t(), ids

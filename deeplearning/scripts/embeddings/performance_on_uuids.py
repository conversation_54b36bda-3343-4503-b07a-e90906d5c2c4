import argparse
import json
import logging
import os
from collections import defaultdict
from typing import Any, Dict, List, Set, Tuple

import pandas as pd
import tqdm

from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.embeddings.utils import get_embedding_evaluation_dir, get_embedding_hdf5_file

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")


def get_performance(items: Dict[str, List[Dict[str, Any]]]) -> Dict[str, float]:
    performance_components = defaultdict(list)
    for uuid, results in items.items():
        for result in results:
            performance_components[uuid].append(int(result["label"] == result["prediction"]))

    number_items = 0
    sum_items = 0
    sum_min_items = 0
    sum_max_items = 0
    sum_mean_items = 0.0

    for uuid, components in performance_components.items():
        number_items += len(components)
        sum_items += sum(components)
        sum_min_items += min(components)
        sum_max_items += max(components)
        sum_mean_items += sum(components) / len(components)

    per_instance_accuracy = sum_items / number_items
    per_point_min_accuracy = sum_min_items / len(performance_components)
    per_point_max_accuracy = sum_max_items / len(performance_components)
    per_point_mean_accuracy = sum_mean_items / len(performance_components)

    return {
        "per_instance_accuracy": per_instance_accuracy,
        "per_point_min_accuracy": per_point_min_accuracy,
        "per_point_max_accuracy": per_point_max_accuracy,
        "per_point_mean_accuracy": per_point_mean_accuracy,
    }


def bucket_items_by_uuid_inclusion(
    evaluation_dir: str, embedding_dataset_filepath: str, uuids: Set[str]
) -> Tuple[Dict[str, List[Dict[str, Any]]], Dict[str, List[Dict[str, Any]]]]:
    embedding_dataset = EmbeddingDataset(embedding_dataset_filepath)
    inclusive_points = defaultdict(list)
    exclusive_points = defaultdict(list)

    for root, _, files in os.walk(evaluation_dir):
        for file in tqdm.tqdm(files):
            if file.endswith(".jsonl"):
                filepath = os.path.join(root, file)
                with open(filepath) as f:
                    for line in f.readlines():
                        episode = json.loads(line)
                        for item in episode["query_set"]:
                            uuid = embedding_dataset[item["identifier"]].metadata.point_id
                            assert uuid is not None, "Must use a dataset that has point ids"

                            if uuid in uuids:
                                inclusive_points[uuid].append(item)
                            else:
                                exclusive_points[uuid].append(item)

    return inclusive_points, exclusive_points


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-id", required=True, type=str)
    parser.add_argument("--model-id", required=True, type=str)
    parser.add_argument("--evaluation-id", required=True, type=str)
    parser.add_argument("--uuid-csv-path", required=True, type=str)

    args = parser.parse_args()

    uuid_df = pd.read_csv(args.uuid_csv_path)
    uuids = set(uuid_df["uuid"].tolist())

    LOG.info(f"Read the uuid file {len(uuids)}")

    evaluation_dir = get_embedding_evaluation_dir(args.dataset_id, args.model_id, args.evaluation_id)

    LOG.info(f"Got evaluation dir {evaluation_dir}")

    embedding_dataset_version_id = None
    metadata_path = os.path.join(evaluation_dir, "metadata.json")
    if os.path.exists(metadata_path):
        with open(metadata_path) as f:
            metadata = json.load(f)
            embedding_dataset_version_id = metadata["dataset_version_id"]

    LOG.info(f"Going to get file with version {embedding_dataset_version_id}")

    embedding_dataset_filepath = get_embedding_hdf5_file(args.dataset_id, args.model_id, embedding_dataset_version_id)

    LOG.info(f"Going to get bucket items by uuid: {embedding_dataset_filepath}")
    inclusive_points, exclusive_points = bucket_items_by_uuid_inclusion(
        evaluation_dir=evaluation_dir, embedding_dataset_filepath=embedding_dataset_filepath, uuids=uuids
    )

    inclusive_performance = get_performance(inclusive_points)
    exclusive_performance = get_performance(exclusive_points)

    print("Inclusive Set")
    print(inclusive_performance)
    print("Exclusive Set")
    print(exclusive_performance)


if __name__ == "__main__":
    main()

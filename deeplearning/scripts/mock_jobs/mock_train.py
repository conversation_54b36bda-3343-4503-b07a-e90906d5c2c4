"""
This DL mock_train job is for testing in the training pipeline, a very simple job that
will echo job-id and description for sanity, as well as allow extra configuration for testability
Arguments
--job-id - takes in the job id for direct output
--description - takes in the job description for direct output
--failure - if set, will cause the job to fail (exit with code 1)
--runtime - approximate job runtime in seconds, we echo and sleep for 10s for output validation.
"""
import argparse
import json
import logging
import time

LOG = logging.getLogger(__name__)


def main() -> None:  # noqa: C901
    logging.basicConfig(
        format="%(asctime)s %(levelname)-8s %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
    )

    parser = argparse.ArgumentParser()
    parser.add_argument("--job-id", type=str)
    parser.add_argument("--pipeline-id", type=str)
    parser.add_argument("--dl-config", type=json.loads, default={})
    parser.add_argument("--description", type=str)
    parser.add_argument("--failure", action="store_true")
    parser.add_argument("--runtime", type=int, default=60)

    args = parser.parse_args()
    LOG.info(f"Starting mock JOB: {args.job_id} - Pipeline: {args.pipeline_id}")
    LOG.info(f"Description: {args.description}")
    LOG.info(f"DLConfig: {args.dl_config}")
    LOG.info(f"Runtime: {args.runtime}s")
    LOG.info(f"Failure: {args.failure}")

    runtime = args.runtime
    for i in range(int(runtime / 10)):
        LOG.info(f"{i} working...")
        time.sleep(10)

    if args.failure:
        LOG.error("job failed!")
        exit(1)
    else:
        LOG.info("job succeeded!")
        exit(0)


if __name__ == "__main__":
    main()

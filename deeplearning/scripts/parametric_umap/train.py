import argparse
import logging
from typing import List, Optional, cast
from uuid import uuid4

import torch

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.parametric_umap.trainer import PUMAPTrainer
from deeplearning.utils.download_utils import download_records

LOG = logging.getLogger(__name__)


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--deepweed-model-id", type=str, required=True, help="Model ID from which to pull points_db")
    parser.add_argument("--lr", type=float, default=1e-1)
    parser.add_argument("--epochs", type=int, default=20)
    parser.add_argument("--lr-milestones", type=str, default="5,10,15,20")
    parser.add_argument("--lr-gamma", type=float, default=0.1)
    parser.add_argument("--description", type=str, default="")
    parser.add_argument("--fast-run", action="store_true", default=False)
    parser.add_argument("--embedding-lookup-file", type=str, required=True)

    args = parser.parse_args()

    lr_milestones = [int(i) for i in args.lr_milestones.split(",")]
    trainer = PUMAPTrainer()
    model_id_list: List[Optional[str]] = [None]
    points_db_path_list: List[Optional[str]] = [None]
    if torch.distributed.get_rank() == 0:
        download_records(args.deepweed_model_id, include_points_db=True)

        model_id_list = [str(uuid4())]

        points_db_path_list = [
            (f"{CARBON_DATA_DIR}/deeplearning/models/{args.deepweed_model_id}/test_unoptimized_dataframes/points_v2.db")
        ]

    torch.distributed.broadcast_object_list(model_id_list, src=0)
    torch.distributed.broadcast_object_list(points_db_path_list, src=0)
    model_id = cast(str, model_id_list[0])
    points_db_path = cast(str, points_db_path_list[0])
    assert points_db_path is not None

    trainer.dataset(points_db_path, embedding_hdf5_file=args.embedding_lookup_file, fast_run=args.fast_run)

    trainer.train(
        lr=args.lr * torch.distributed.get_world_size(),
        epochs=args.epochs,
        lr_milestones=lr_milestones,
        lr_gamma=args.lr_gamma,
        fast_run=args.fast_run,
        checkpoint_dir=f"/data/deeplearning/models/{model_id}",
        description=args.description,
    )


if __name__ == "__main__":
    main()

import argparse

import torch

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.comparison.comparer.points_comparer import PointsComparer
from deeplearning.scripts.utils.utils import add_job_creator_arguments
from lib.common.time.time import maka_control_timestamp_ms


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--comparison-model-id", type=str, default=None)
    parser.add_argument("--dataset-id", type=str, required=True)
    parser.add_argument("--environment", type=str, default="production")
    parser.add_argument("--device", type=str, default=torch.device("cuda" if torch.cuda.is_available() else "cpu"))
    parser.add_argument("--num-gpus", type=int, default=torch.cuda.device_count())

    parser.add_argument("--batch-size", type=int, default=1024)
    parser.add_argument("--filenames", type=str, default="train.json,validation.json,test.json")
    parser.add_argument("--weed-categories", type=str, default="broadleaf,grass,offshoot,purslane")

    parser.add_argument("--main-point-category", type=str, choices=["crop", "weed"], required=True)
    parser.add_argument("--compared-point-category", type=str, choices=["crop", "weed"], required=True)
    parser.add_argument("--main-point-mode", type=str, choices=["with_overlap", "without_overlap"], required=True)

    parser.add_argument("--no-save-df", action="store_false", dest="save_df")
    parser.add_argument("--no-save-stats", action="store_false", dest="save_stats")
    parser.add_argument("--no-plot-graph", action="store_false", dest="plot_graph")
    parser.add_argument(
        "--no-skip-existing-comparison-files", action="store_false", dest="skip_existing_comparison_files"
    )

    parser.add_argument("--save-labels", action="store_true")
    parser.add_argument(
        "--saved-labels-dir", type=str, default=f"{CARBON_DATA_DIR}/deeplearning/generated_comparison_labels"
    )
    parser.add_argument("--saved-labels-filename", type=str, default=f"{maka_control_timestamp_ms()}.csv")
    parser.add_argument(
        "--saved-labels-max-score",
        type=float,
        default=0.8,
        help="If comparing the same category, save the labels if the comparison score is lower than the max score.",
    )
    parser.add_argument(
        "--saved-labels-min-score",
        type=float,
        default=0.2,
        help="If comparing the different category, save the labels if the comparison score is higher than the min score.",
    )
    parser.add_argument("--saved-labels-max-number-items", type=int, default=None)

    parser.add_argument("--save-head-tail-k", type=int, default=None)
    parser.add_argument("--save-similarity-score-range", type=str, default="0.95,1.0")
    parser.add_argument("--find-mislabel-mode", action="store_true")
    add_job_creator_arguments(parser)

    args = parser.parse_args()

    points_comparer = PointsComparer(config=vars(args))
    points_comparer.run()


if __name__ == "__main__":
    main()

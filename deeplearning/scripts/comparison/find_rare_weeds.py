import argparse

import torch

from deeplearning.scripts.comparison.comparer.points_finder import PointsFinder
from deeplearning.scripts.utils.utils import add_job_creator_arguments


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str, default=None)
    parser.add_argument("--comparison-model-id", type=str, default=None)
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--filenames", type=str, default="train.json,validation.json,test.json")
    parser.add_argument("--weed-categories", type=str, default="broadleaf,grass,offshoot,purslane")
    parser.add_argument("--filter-key", type=str, default=None, help="Specify the key to filter the data.")
    parser.add_argument("--filter-value", type=str, default=None, help="Specify the value corresponding to filter_key.")
    parser.add_argument("--device", type=str, default=torch.device("cuda" if torch.cuda.is_available() else "cpu"))
    parser.add_argument("--num-gpus", type=int, default=torch.cuda.device_count())
    parser.add_argument("--threshold", type=float, default=0.8)
    parser.add_argument("--environment", type=str, default="production")
    parser.add_argument("--download-comparison-embeddings", action="store_true")
    parser.add_argument("--batch-size", type=int, default=1024)
    parser.add_argument(
        "--direct-retrieve-top-n-percentage",
        type=bool,
        default=True,
        help="Directly retrieve the top N percentage of rare weeds from all weeds.",
    )
    parser.add_argument(
        "--relative-rareness-percentage",
        type=float,
        default=0.005,
        help="Specify the relative-rareness-percentage as a float between 0 and 1.",
    )
    add_job_creator_arguments(parser)
    args = parser.parse_args()

    if not (0 <= args.relative_rareness_percentage <= 1):
        raise ValueError(
            f"Invalid value for relative_rareness_percentage: {args.relative_rareness_percentage}. It must be within the range [0, 1]."
        )

    points_finder = PointsFinder(config=vars(args))
    points_finder.run()


if __name__ == "__main__":
    main()

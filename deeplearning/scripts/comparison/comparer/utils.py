from pathlib import Path
from typing import Any, Dict

import boto3
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F

from deeplearning.scripts.comparison.constants import COMPARISON_EVALUATIONS_BUCKET


def upload_comparison_result_to_s3(file: str, dataset_id: str, comparison_model_id: str, environment: str) -> None:
    bucket = boto3.resource("s3").Bucket(COMPARISON_EVALUATIONS_BUCKET)
    s3_path = f"{environment}/comparison_results/{dataset_id}/{comparison_model_id}/{Path(file).name}"
    bucket.upload_file(file, s3_path)


def upload_comparison_labels_to_s3(file: str, dataset_id: str, comparison_model_id: str, environment: str) -> None:
    bucket = boto3.resource("s3").Bucket(COMPARISON_EVALUATIONS_BUCKET)
    s3_path = f"{environment}/generated_comparison_labels/{dataset_id}/{comparison_model_id}/{Path(file).name}"
    bucket.upload_file(file, s3_path)


def select_and_normalize(embeddings: torch.Tensor, index: torch.Tensor) -> torch.Tensor:
    return F.normalize(torch.index_select(embeddings, dim=0, index=index), p=2, dim=1)


def save_stats(statistics: Dict[str, Dict[str, Any]], saved_path: str) -> None:
    with open(saved_path, "w") as f:
        for key, stats in statistics.items():
            f.write(f"Statistics for {key}:\n")

            for stat_name, value in stats.items():
                log_message = f"  {stat_name}: {value:.4f}"
                f.write(log_message + "\n")
            f.write("\n")


def plot_graph(df_dict: Dict[str, pd.DataFrame], dataset_id: str, saved_path: str, combined: bool = True) -> None:
    colors = {"with_overlap": "red", "without_overlap": "blue"}
    if combined:
        plt.figure(figsize=(12, 6))
        for key, value in df_dict.items():
            plt.scatter(
                x=np.linspace(0, 1, len(value["score"])),
                y=value["score"],
                color=colors[key],
                label=f"{key.replace('_', ' ').title()}",
                s=5,
            )
        plt.xlabel("Normalized Index")
        plt.ylabel("Comparison Score")
        plt.title(f"Comparison Scores for Overlapping vs. Non-Overlapping Points for Dataset {dataset_id}")
        plt.legend()
        plt.grid(True)
    else:
        fig, axs = plt.subplots(1, 2, figsize=(18, 6), sharey=True)
        for i, (key, value) in enumerate(df_dict.items()):
            axs[i].scatter(x=range(len(value["score"])), y=value["score"], color=colors[key], s=5)
            axs[i].set_title(f"{key.replace('_', ' ').title()} Scores")
            axs[i].set_xlabel("Index")
            axs[i].grid(True)

        fig.text(0.04, 0.5, "Comparison Score", va="center", rotation="vertical")
        fig.suptitle(f"Comparison Scores for Overlapping vs. Non-Overlapping Points for Dataset {dataset_id}")
        plt.tight_layout(rect=[0.05, 0.05, 1, 0.95])
    plt.savefig(saved_path, dpi=300)
    plt.close()

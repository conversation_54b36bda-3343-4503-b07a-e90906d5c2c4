import logging
import os
from collections import defaultdict
from typing import Any, Dict, List, Set, Tuple

import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
from tqdm import tqdm

from deeplearning.comparison.data_utils import (
    cosine_similarity_normed_inputs,
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_dir,
    get_embeddings_with_image_id_by_key,
    load_embeddings_from_torch,
)
from deeplearning.constants import CARBON_DATA_DIR, Environment
from deeplearning.embeddings.scripts.calculate_embedding_scores import retrieve_comparison_annotations
from deeplearning.scripts.comparison.comparer.utils import (
    plot_graph,
    save_stats,
    select_and_normalize,
    upload_comparison_labels_to_s3,
    upload_comparison_result_to_s3,
)
from deeplearning.scripts.comparison.constants import DESCENDING_STRING, DISTANCE_THRESHOLD
from deeplearning.scripts.comparison.utils import is_within_distance
from deeplearning.scripts.datasets.get_uuids import does_square_a_overlap_center_b
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    evaluate_comparison_embeddings,
)
from deeplearning.scripts.utils.elastic_launcher import default, elastic_launcher
from deeplearning.scripts.utils.utils import get_dataset, pick_comparison_model_id
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.uuid import get_globally_unique_id

logging.basicConfig(format="%(asctime)s %(levelname)s:%(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S")
LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


class PointsComparer:
    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config
        self.comparison_model_id = pick_comparison_model_id(
            comparison_model_id=config["comparison_model_id"], parent_comparison_model_id=None,
        )
        self.dataset_id = config["dataset_id"]
        self.environment = config["environment"]
        self.device = config["device"]
        self.num_gpus = config["num_gpus"]

        self.batch_size = config["batch_size"]
        self.filenames = config["filenames"].split(",")
        self.weed_categories = set(config["weed_categories"].split(","))

        self.main_point_category = config["main_point_category"]
        self.compared_point_category = config["compared_point_category"]
        self.main_point_mode = config["main_point_mode"]
        self.sort_type = "ascending" if self.main_point_category == self.compared_point_category else "descending"

        self.save_df = config["save_df"]
        self.save_stats = config["save_stats"]
        self.plot_graph = config["plot_graph"]
        self.skip_existing_comparison_files = config["skip_existing_comparison_files"]

        self.save_labels = config["save_labels"]
        self.saved_labels_dir = config["saved_labels_dir"]
        self.saved_labels_filename = config["saved_labels_filename"]
        self.saved_labels_max_score = config["saved_labels_max_score"]
        self.saved_labels_min_score = config["saved_labels_min_score"]
        self.saved_labels_max_number_items = config["saved_labels_max_number_items"]

        self.save_head_tail_k = config["save_head_tail_k"]
        self.save_similarity_score_range = [float(score) for score in config["save_similarity_score_range"].split(",")]
        self.find_mislabel_mode = config["find_mislabel_mode"]

        if not os.path.exists(f"{CARBON_DATA_DIR}/deeplearning/datasets/{self.dataset_id}"):
            LOG.info(f"Downloading dataset: {self.dataset_id}.")
            get_dataset(dataset_id=self.dataset_id)

        # Evaluate all the comparison embeddings before estimating the score.
        elastic_launcher(
            self.num_gpus,
            evaluate_comparison_embeddings,
            default,
            EvaluateComparisonEmbeddingsArguments(
                comparison_model_id=self.comparison_model_id,
                environment=self.environment,
                dataset_id=self.dataset_id,
                fast_run=False,
                eval_files=self.filenames,
            ),
        )

        LOG.info(f"Config: {self.config}")

    def run(self,) -> None:
        (dataset_annotations_dict_all, pt_files_from_image_id_all,) = self.retrieve_annotations_and_embeddings_by_id()
        embeddings_data_all = self.create_comparison_embeddings_dict(
            comparison_embeddings_dir=f"{get_comparison_evaluation_dir()}/{Environment.PRODUCTION.name.lower()}/comparison_evaluations/{self.comparison_model_id}",
            dataset_annotations_dict=dataset_annotations_dict_all,
            pt_files_from_image_id=pt_files_from_image_id_all,
        )
        comparison_score_dict, mislabel_data_dict = self.compute_plant_comparison_scores(
            embeddings_data_all=embeddings_data_all,
        )
        self.compute_and_plot_stats(comparison_score_dict=comparison_score_dict, mislabel_data_dict=mislabel_data_dict)

    def retrieve_annotations_and_embeddings_by_id(self,) -> Tuple[Dict[str, Any], Set[str]]:
        dataset_annotations_dict_all: Dict[str, Any] = defaultdict(list)
        pt_files_from_image_id_all: Set[str] = set()

        for filename in self.filenames:
            dataset_annotations_dict, _, _ = retrieve_comparison_annotations(
                dataset_id=self.dataset_id, comparison_model_id=self.comparison_model_id, filename=filename,
            )
            pt_files_from_image_id = get_embeddings_with_image_id_by_key(
                filename=filename, dataset_id=self.dataset_id, key=None, value=None,
            )
            for key, value in dataset_annotations_dict.items():
                dataset_annotations_dict_all[key].extend(value)
            pt_files_from_image_id_all.update(pt_files_from_image_id)
            LOG.info(f"Finish retrieving {filename} with torch file size: {len(pt_files_from_image_id)}.")

        LOG.info(f"Get dataset_annotations_dict_all with size: {len(dataset_annotations_dict_all)}.")
        LOG.info(f"Get pt_files_from_image_id_all with size: {len(pt_files_from_image_id_all)}.")

        return (dataset_annotations_dict_all, pt_files_from_image_id_all)

    def create_comparison_embeddings_dict(
        self,
        comparison_embeddings_dir: str,
        dataset_annotations_dict: Dict[str, List[Dict[str, Any]]],
        pt_files_from_image_id: Set[str],
    ) -> Dict[str, Any]:
        # Download the comparison embeddings. Existing files will be skipped automatically.
        download_records(
            item_id=self.comparison_model_id,
            bucket=get_comparison_evaluation_bucket(),
            filename_filters=pt_files_from_image_id,
            s3_directory=f"{Environment.PRODUCTION.name.lower()}/comparison_evaluations",
            save_dir=comparison_embeddings_dir,
            only_use_basename_in_download=True,
            skip_existing_files=self.skip_existing_comparison_files,
        )
        # Handle the embeddings data (tensors and embeddings information).
        embeddings_data_all: Dict[str, Any] = {}
        skip_count_by_image_id = 0
        skip_count_by_not_enough_embeddings = 0

        for file in tqdm(pt_files_from_image_id):
            embeddings_dict = defaultdict(list)
            embeddings_metadata_dict = defaultdict(list)
            annotation_dict = defaultdict(list)
            image_id = file.split(".")[0]  # Get the filename without extension.
            if image_id not in dataset_annotations_dict or len(dataset_annotations_dict[image_id]) == 0:
                skip_count_by_image_id += 1
                continue

            try:
                # file with keys: "image_meta", "embeddings_data", "embeddings"
                # "image_meta" with keys: "image_id", "label_id", "image_url", "model_id", "geohash", "epoch"
                # "embeddings_data" with keys: "x", "y", "radius", "category". len == # of points
                # "embeddings": embeddings. len == # of points
                filepath = os.path.join(comparison_embeddings_dir, file)
                if not os.path.exists(filepath):
                    LOG.warning(f"File {filepath} does not exist")
                    continue
                datapoints = load_embeddings_from_torch(input=filepath)
                datapoints_embeddings_data = datapoints["embeddings_data"]
                datapoints_embeddings = datapoints["embeddings"]

                for annotation in dataset_annotations_dict[image_id]:
                    key = "weed" if annotation["label"] in self.weed_categories else "crop"
                    annotation_dict[key].append(annotation)

                for (datapoint_embeddings_data, datapoint_embeddings) in zip(
                    datapoints_embeddings_data, datapoints_embeddings
                ):
                    for annotation in dataset_annotations_dict[image_id]:
                        if is_within_distance(
                            point1=datapoint_embeddings_data, point2=annotation, distance=DISTANCE_THRESHOLD
                        ):
                            point_info = {
                                "x": datapoint_embeddings_data["x"],
                                "y": datapoint_embeddings_data["y"],
                                "radius": datapoint_embeddings_data["radius"],
                                "category": datapoint_embeddings_data[
                                    "category"
                                ],  # From the comparison model output category.
                                "uuid": get_globally_unique_id(annotation_dict=annotation),
                                "label_id": annotation.get("label_id", ""),
                                "image_id": annotation["image_id"],
                            }
                            if point_info["category"] not in self.weed_categories:
                                embeddings_dict["crop"].append(datapoint_embeddings)
                                embeddings_metadata_dict["crop"].append(point_info)
                            else:
                                embeddings_dict["weed"].append(datapoint_embeddings)
                                embeddings_metadata_dict["weed"].append(point_info)
                            break
            except FileNotFoundError as e:
                raise FileNotFoundError(f"File not found: {file}.") from e
            except Exception as e:
                raise RuntimeError(f"An error occurred while processing the file: {e}") from e

            if len(embeddings_dict["crop"]) > 0 and len(embeddings_dict["weed"]) > 0:
                embeddings_data_all[image_id] = {
                    "embeddings": embeddings_dict,
                    "embeddings_metadata": embeddings_metadata_dict,
                    "annotation": annotation_dict,
                }  # Only take the embeddings data if crop embeddings are available.
            else:
                skip_count_by_not_enough_embeddings += 1

        LOG.info(f"Get embeddings_data_all with size: {len(embeddings_data_all)}.")
        LOG.info(
            f"Skip count by no image_id: {skip_count_by_image_id}, not enough_embeddings: {skip_count_by_not_enough_embeddings}."
        )

        return embeddings_data_all

    def create_batch_overlapped_points_dict(
        self, point_a_list: List[Dict[str, Any]], point_b_list: List[Dict[str, Any]]
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        overlapped_matrix = np.zeros(
            (len(point_a_list), len(point_b_list)), dtype=bool
        )  # overlapped_matrix contains False (non-overlapped or invalid), True (overlapped) only.

        for idx_a, point_a_info in enumerate(point_a_list):
            for idx_b, point_b_info in enumerate(point_b_list):
                if point_a_info == point_b_info:
                    raise ValueError(
                        "Invalid comparison: point_a_info cannot be point_b_info. One should be a crop, and the other should be a weed."
                    )
                flag = does_square_a_overlap_center_b(
                    point_a=point_a_info, point_b=point_b_info
                )  # flag == True denotes overlapped, while flag == False denotes non-overlapped.
                overlapped_matrix[idx_a, idx_b] = flag

        points_index_with_overlap = torch.tensor(np.where(overlapped_matrix.any(axis=1))[0], device=self.device)
        points_index_without_overlap = torch.tensor(np.where(~overlapped_matrix.any(axis=1))[0], device=self.device)

        return points_index_with_overlap, points_index_without_overlap

    def compute_comparison_scores(
        self,
        image_id: str,
        embeddings_a: torch.Tensor,
        embeddings_b: torch.Tensor,
        embeddings_a_index: torch.Tensor,
        embeddings_b_index: torch.Tensor,
        points_a_metadata: List[Dict[str, Any]],
        points_b_metadata: List[Dict[str, Any]],
        compute_itself: bool = True,
    ) -> Tuple[List[Tuple[Dict[str, Any], Dict[str, Any], float]], List[Tuple[str, float, float, str]]]:
        # total_embeddings_normalized has a size of [N, 4096]. Performing matrix multiplication involves:
        # N * N * 4096 (multiplications) + N * N * (4096 - 1) (additions) = 8191 * N^2 operations.
        similarity_matrix = torch.zeros(
            (embeddings_a.shape[0], embeddings_b.shape[0]), dtype=torch.float16, device=self.device
        )
        for start in range(0, embeddings_a.shape[0], self.batch_size):
            end = min(start + self.batch_size, embeddings_a.shape[0])
            batch_embeddings_normalized = embeddings_a[start:end]
            similarity_matrix[start:end, :] = F.softplus(
                input=cosine_similarity_normed_inputs(
                    normalized_query=batch_embeddings_normalized, normalized_db=embeddings_b
                ),
                beta=20,
                threshold=2,
            )

        if compute_itself:
            # Compute lower triangular indices for self-comparison.
            x_indices, y_indices = torch.tril_indices(
                row=embeddings_a.shape[0], col=embeddings_b.shape[0], offset=-1, device=self.device
            )
        else:
            # Compute full pairwise indices for different embeddings.
            x_indices, y_indices = (
                torch.arange(embeddings_a.shape[0], device=self.device).repeat_interleave(embeddings_b.shape[0]),
                torch.arange(embeddings_b.shape[0], device=self.device).repeat(embeddings_a.shape[0]),
            )
        combined_data = []
        unique_points_set = set()
        score_min, score_max = self.save_similarity_score_range

        for x, y in zip(x_indices, y_indices):
            similarity_score = similarity_matrix[x, y].item()
            a_meta = points_a_metadata[int(embeddings_a_index[x].item())]
            b_meta = points_b_metadata[int(embeddings_b_index[y].item())]
            combined_data.append((a_meta, b_meta, similarity_score))

            if score_min <= similarity_score <= score_max:
                unique_points_set.update(
                    {
                        (image_id, a_meta["x"], a_meta["y"], a_meta["category"]),
                        (image_id, b_meta["x"], b_meta["y"], b_meta["category"]),
                    }
                )

        return combined_data, list(unique_points_set)

    def compute_plant_comparison_scores(
        self, embeddings_data_all: Dict[str, Any]
    ) -> Tuple[
        Dict[str, List[Tuple[Dict[str, Any], Dict[str, Any], float]]], Dict[str, List[Tuple[str, float, float, str]]]
    ]:
        # Compute the comparison scores.
        comparison_score_dict: Dict[str, List[Tuple[Dict[str, Any], Dict[str, Any], float]]] = defaultdict(list)
        mislabel_data_dict: Dict[str, List[Tuple[str, float, float, str]]] = defaultdict(list)
        for image_id, embeddings_data in tqdm(embeddings_data_all.items()):
            crop_embeddings, weed_embeddings = (
                torch.stack(embeddings_data["embeddings"]["crop"]).to(self.device).half(),
                torch.stack(embeddings_data["embeddings"]["weed"]).to(self.device).half(),
            )
            crop_metadata, weed_metadata = (
                embeddings_data["embeddings_metadata"]["crop"],
                embeddings_data["embeddings_metadata"]["weed"],
            )
            crop_annotations, weed_annotations = (
                embeddings_data["annotation"]["crop"],
                embeddings_data["annotation"]["weed"],
            )

            # Create overlap indices
            crop_index_with_overlap, crop_index_without_overlap = self.create_batch_overlapped_points_dict(
                point_a_list=crop_metadata, point_b_list=weed_annotations
            )
            weed_index_with_overlap, weed_index_without_overlap = self.create_batch_overlapped_points_dict(
                point_a_list=weed_metadata, point_b_list=crop_annotations
            )

            # Extract and normalize embeddings
            crop_embeddings_with_overlap = select_and_normalize(crop_embeddings, crop_index_with_overlap)
            crop_embeddings_without_overlap = select_and_normalize(crop_embeddings, crop_index_without_overlap)
            weed_embeddings_with_overlap = select_and_normalize(weed_embeddings, weed_index_with_overlap)
            weed_embeddings_without_overlap = select_and_normalize(weed_embeddings, weed_index_without_overlap)

            # Assign metadata and embeddings based on main_point_category
            category_map = {
                "crop": (
                    crop_metadata,
                    crop_embeddings_with_overlap,
                    crop_embeddings_without_overlap,
                    crop_index_with_overlap,
                    crop_index_without_overlap,
                ),
                "weed": (
                    weed_metadata,
                    weed_embeddings_with_overlap,
                    weed_embeddings_without_overlap,
                    weed_index_with_overlap,
                    weed_index_without_overlap,
                ),
            }
            (
                (
                    points_a_metadata,
                    embeddings_a_with_overlap,
                    embeddings_a_without_overlap,
                    embeddings_a_index_with_overlap,
                    embeddings_a_index_without_overlap,
                ),
                (
                    points_b_metadata,
                    embeddings_b_with_overlap,
                    embeddings_b_without_overlap,
                    embeddings_b_index_with_overlap,
                    embeddings_b_index_without_overlap,
                ),
            ) = (category_map[category] for category in [self.main_point_category, self.compared_point_category])
            embeddings_a = (
                embeddings_a_without_overlap if self.main_point_mode == "without_overlap" else embeddings_a_with_overlap
            )
            embeddings_a_index = (
                embeddings_a_index_without_overlap
                if self.main_point_mode == "without_overlap"
                else embeddings_a_index_with_overlap
            )

            for overlap_type, embeddings_b, embeddings_b_index in [
                ("with_overlap", embeddings_b_with_overlap, embeddings_b_index_with_overlap),
                ("without_overlap", embeddings_b_without_overlap, embeddings_b_index_without_overlap),
            ]:
                scores, mislabel_data = self.compute_comparison_scores(
                    image_id=image_id,
                    embeddings_a=embeddings_a,
                    embeddings_b=embeddings_b,
                    embeddings_a_index=embeddings_a_index,
                    embeddings_b_index=embeddings_b_index,
                    points_a_metadata=points_a_metadata,
                    points_b_metadata=points_b_metadata,
                    compute_itself=(
                        self.main_point_category == self.compared_point_category
                        and self.main_point_mode == overlap_type
                    ),
                )
                comparison_score_dict[overlap_type].extend(scores)
                mislabel_data_dict[overlap_type].extend(mislabel_data)

        for key in ["with_overlap", "without_overlap"]:
            comparison_score_dict[key].sort(key=lambda x: x[2], reverse=self.sort_type == DESCENDING_STRING)

        return comparison_score_dict, mislabel_data_dict

    def compute_and_plot_stats(
        self,
        comparison_score_dict: Dict[str, List[Tuple[Dict[str, Any], Dict[str, Any], float]]],
        mislabel_data_dict: Dict[str, List[Tuple[str, float, float, str]]],
    ) -> None:
        df_dict: Dict[str, pd.DataFrame] = {}
        columns_dict = {
            "with_overlap": [f"uuid_{self.main_point_mode}_1", "uuid_with_overlap_2", "score"],
            "without_overlap": [f"uuid_{self.main_point_mode}_1", "uuid_without_overlap_2", "score"],
        }
        statistics: Dict[str, Dict[str, Any]] = {}
        for key, values in comparison_score_dict.items():
            uuids = [(val[0]["uuid"], val[1]["uuid"], val[2]) for val in values]
            df_dict[key] = pd.DataFrame(uuids, columns=columns_dict[key])
            stats = df_dict[key]["score"].describe(percentiles=[0.25, 0.5, 0.75])
            statistics[key] = {
                "mean": stats["mean"],
                "25th percentile": stats["25%"],
                "50th percentile (median)": stats["50%"],
                "75th percentile": stats["75%"],
            }
            LOG.info(f"Statistics for {key}:")
            for stat_name, value in statistics[key].items():
                LOG.info(f"  {stat_name}: {value:.4f}")

        base_dir = f"{CARBON_DATA_DIR}/deeplearning/comparison/{self.dataset_id}/{self.comparison_model_id}"
        os.makedirs(base_dir, exist_ok=True)

        if self.save_df:
            filenames = {
                "with_overlap": f"{self.main_point_mode}_{self.main_point_category}_with_overlap_{self.compared_point_category}.csv",
                "without_overlap": f"{self.main_point_mode}_{self.main_point_category}_without_overlap_{self.compared_point_category}.csv",
            }

            for key, filename in filenames.items():
                file_path = os.path.join(base_dir, filename)
                df_filtered = (
                    pd.concat([df_dict[key].head(self.save_head_tail_k), df_dict[key].tail(self.save_head_tail_k)])
                    if self.save_head_tail_k is not None
                    else df_dict[key]
                )
                df_filtered.to_csv(file_path, index=False)
                LOG.info(f"Finished saving df: {file_path}.")

                upload_comparison_result_to_s3(
                    file=file_path,
                    dataset_id=self.dataset_id,
                    comparison_model_id=self.comparison_model_id,
                    environment=self.environment,
                )
                LOG.info(f"Finished uploading df: {file_path} to s3.")

        if self.save_stats:
            file_path = os.path.join(
                base_dir,
                f"{self.main_point_mode}_{self.main_point_category}_{self.compared_point_category}_analysis_stats.txt",
            )
            save_stats(statistics=statistics, saved_path=file_path)
            LOG.info(f"Finished saving stats: {file_path}.")

            upload_comparison_result_to_s3(
                file=file_path,
                dataset_id=self.dataset_id,
                comparison_model_id=self.comparison_model_id,
                environment=self.environment,
            )
            LOG.info(f"Finished uploading stats: {file_path} to s3.")

        if self.plot_graph:
            for combined, suffix in [(True, "combined"), (False, "separate")]:
                file_path = os.path.join(
                    base_dir,
                    f"{self.main_point_mode}_{self.main_point_category}_{self.compared_point_category}_analysis_{suffix}.png",
                )
                plot_graph(
                    df_dict=df_dict, dataset_id=self.dataset_id, saved_path=file_path, combined=combined,
                )
                LOG.info(f"Finish saving graphs: {file_path}.")

                upload_comparison_result_to_s3(
                    file=file_path,
                    dataset_id=self.dataset_id,
                    comparison_model_id=self.comparison_model_id,
                    environment=self.environment,
                )
                LOG.info(f"Finished uploading graphs: {file_path} to s3.")

        if self.find_mislabel_mode:
            mislabel_columns_dict = {
                "with_overlap": ["image_id", "x", "y", "category"],
                "without_overlap": ["image_id", "x", "y", "category"],
            }
            for key, mislabel_data in mislabel_data_dict.items():
                file_path = os.path.join(
                    base_dir,
                    f"debug_{self.main_point_mode}_{self.main_point_category}_{key}_{self.compared_point_category}.csv",
                )
                mislabel_df = pd.DataFrame(mislabel_data, columns=mislabel_columns_dict[key])
                mislabel_df.to_csv(
                    file_path, index=False,
                )
                LOG.info(f"Finish saving mislabel_df: {file_path}.")

                upload_comparison_result_to_s3(
                    file=file_path,
                    dataset_id=self.dataset_id,
                    comparison_model_id=self.comparison_model_id,
                    environment=self.environment,
                )
                LOG.info(f"Finished uploading mislabel_df: {file_path} to s3.")

        if self.save_labels:
            items_to_save = []
            for key, values in comparison_score_dict.items():
                for value in values:
                    if (
                        self.main_point_category != self.compared_point_category
                        and value[2] > self.saved_labels_min_score
                    ) or (
                        self.main_point_category == self.compared_point_category
                        and value[2] < self.saved_labels_max_score
                    ):
                        item_one = value[0]
                        item_two = value[1]
                        items_to_save.append(
                            (
                                {
                                    "image_one": item_one["image_id"],
                                    "x_one": item_one["x"],
                                    "y_one": item_one["y"],
                                    "radius_one": item_one["radius"],
                                    "category_one": item_one["category"],
                                    "label_one": item_one["label_id"],
                                    "image_two": item_two["image_id"],
                                    "x_two": item_two["x"],
                                    "y_two": item_two["y"],
                                    "radius_two": item_two["radius"],
                                    "category_two": item_two["category"],
                                    "label_two": item_two["label_id"],
                                },
                                value[2],
                            )
                        )

            items_to_save.sort(key=lambda x: x[1], reverse=self.sort_type == DESCENDING_STRING)

            if self.saved_labels_max_number_items is not None:
                items_to_save = items_to_save[: self.saved_labels_max_number_items]

            df_to_save = pd.DataFrame([item[0] for item in items_to_save])

            file_path = os.path.join(
                self.saved_labels_dir, self.dataset_id, self.comparison_model_id, self.saved_labels_filename
            )
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            df_to_save.to_csv(file_path, index=False)
            LOG.info(f"Finished saving labels: {file_path}.")

            upload_comparison_labels_to_s3(
                file=file_path,
                dataset_id=self.dataset_id,
                comparison_model_id=self.comparison_model_id,
                environment=self.environment,
            )
            LOG.info(f"Finished uploading labels: {file_path} to s3.")

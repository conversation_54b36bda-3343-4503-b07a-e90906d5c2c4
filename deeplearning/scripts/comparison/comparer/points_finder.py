import logging
import os
from collections import defaultdict
from typing import Any, Dict, List, Set, Tuple

import pandas as pd
import torch
import torch.nn.functional as F

from deeplearning.comparison.data_utils import (
    cosine_similarity_normed_inputs,
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_dir,
    get_embeddings_with_image_id_by_key,
    load_embeddings_from_torch,
)
from deeplearning.constants import CARBON_DATA_DIR, Environment
from deeplearning.embeddings.scripts.calculate_embedding_scores import (
    query_model_embeddings,
    retrieve_comparison_annotations,
)
from deeplearning.scripts.comparison.constants import DISTANCE_THRESHOLD
from deeplearning.scripts.comparison.utils import is_within_distance
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    evaluate_comparison_embeddings,
)
from deeplearning.scripts.utils.elastic_launcher import default, elastic_launcher
from deeplearning.scripts.utils.training_info import TrainingInfo
from deeplearning.scripts.utils.utils import get_dataset, pick_comparison_model_id
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.uuid import get_globally_unique_id

logging.basicConfig(format="%(asctime)s %(levelname)s:%(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S")
LOG = logging.getLogger(__name__)


class PointsFinder:
    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config
        self.model_id = config["model_id"]
        self.environment = config["environment"]
        self.device = config["device"]
        self.num_gpus = config["num_gpus"]
        self.dataset_id = config["dataset_id"]
        self.filenames = config["filenames"].split(",")
        self.weed_categories = set(config["weed_categories"].split(","))
        self.key = config["filter_key"]
        self.value = config["filter_value"]
        self.threshold = config["threshold"]
        self.batch_size = config["batch_size"]
        self.relative_rareness_percentage = config["relative_rareness_percentage"]
        self.direct_retrieve_top_n_percentage = config["direct_retrieve_top_n_percentage"]
        self.comparison_model_id = pick_comparison_model_id(
            comparison_model_id=config["comparison_model_id"], parent_comparison_model_id=None,
        )

        if self.model_id is None and self.dataset_id is None:
            raise ValueError("Either model_id or dsataset_id must be specified.")

        if self.key is not None and self.value is None:
            raise ValueError("Value must be provided when key is specified.")

        if self.model_id is not None:  # Use training_info if there is model_id.
            training_info = TrainingInfo(model_id=self.model_id, include_points_db=True)
            points_db_path = os.path.join(training_info.data_dir, "test_dataframes/points_v2.db")
            if not os.path.exists(points_db_path):
                download_records(item_id=self.model_id, include_points_db=True)
            self.dataset_id = training_info.dataset_id
            self.comparison_model_id = training_info.comparison_model_id
            self.embeddings_from_db = query_model_embeddings(model_id=self.model_id, embedding_type="FULL",)

        if not os.path.exists(f"{CARBON_DATA_DIR}/deeplearning/datasets/{self.dataset_id}"):
            LOG.info(f"Downloading dataset: {self.dataset_id}.")
            get_dataset(dataset_id=self.dataset_id)

        # Evaluate all the comparison embeddings before estimating the score.
        elastic_launcher(
            self.num_gpus,
            evaluate_comparison_embeddings,
            default,
            EvaluateComparisonEmbeddingsArguments(
                comparison_model_id=self.comparison_model_id,
                environment=self.environment,
                dataset_id=self.dataset_id,
                fast_run=False,
                eval_files=self.filenames,
            ),
        )

        LOG.info(f"Config: {self.config}")

    def run(self,) -> None:
        (
            dataset_annotations_dict_all,
            image_url2id_all,
            pt_files_from_image_id_all,
        ) = self.retrieve_annotations_and_embeddings_by_id()

        embeddings_list, embeddings_information_list = self.create_comparison_embeddings_dict(
            comparison_embeddings_dir=f"{get_comparison_evaluation_dir()}/{Environment.PRODUCTION.name.lower()}/comparison_evaluations/{self.comparison_model_id}",
            dataset_annotations_dict=dataset_annotations_dict_all,
            pt_files_from_image_id=pt_files_from_image_id_all,
            image_url2id=image_url2id_all,
        )

        output_list = self.compute_comparison_scores(
            embeddings_list=embeddings_list, embeddings_information_list=embeddings_information_list,
        )

        self.create_final_dataframe(output_list=output_list)

    def retrieve_annotations_and_embeddings_by_id(self,) -> Tuple[Dict[str, Any], Dict[str, str], Set[str]]:
        dataset_annotations_dict_all: Dict[str, Any] = defaultdict(list)
        image_url2id_all: Dict[str, str] = {}
        pt_files_from_image_id_all: Set[str] = set()

        for filename in self.filenames:
            dataset_annotations_dict, image_url2id, _ = retrieve_comparison_annotations(
                dataset_id=self.dataset_id, comparison_model_id=self.comparison_model_id, filename=filename,
            )
            pt_files_from_image_id = get_embeddings_with_image_id_by_key(
                filename=filename, dataset_id=self.dataset_id, key=self.key, value=self.value,
            )
            for key, value in dataset_annotations_dict.items():
                dataset_annotations_dict_all[key].extend(value)
            image_url2id_all.update(image_url2id)
            pt_files_from_image_id_all.update(pt_files_from_image_id)
            LOG.info(f"Finish retriving {filename} with torch file size: {len(pt_files_from_image_id)}.")

        LOG.info(f"Get dataset_annotations_dict_all with size: {len(dataset_annotations_dict_all)}.")
        LOG.info(f"Get pt_files_from_image_id_all with size: {len(pt_files_from_image_id_all)}.")

        return (dataset_annotations_dict_all, image_url2id_all, pt_files_from_image_id_all)

    def create_comparison_embeddings_dict(
        self,
        comparison_embeddings_dir: str,
        dataset_annotations_dict: Dict[str, List[Dict[str, Any]]],
        pt_files_from_image_id: Set[str],
        image_url2id: Dict[str, str],
    ) -> Tuple[List[torch.Tensor], List[str]]:
        # Download the comparison embeddings. Existing files will be skipped automatically.
        download_records(
            item_id=self.comparison_model_id,
            bucket=get_comparison_evaluation_bucket(),
            filename_filters=pt_files_from_image_id,
            s3_directory=f"{Environment.PRODUCTION.name.lower()}/comparison_evaluations",
            save_dir=comparison_embeddings_dir,
            only_use_basename_in_download=True,
            skip_existing_files=True,
        )

        comparison_embeddings_dict: Dict[str, Dict[str, Any]] = {}
        # Handle the embeddings data (tensors and embeddings information).
        embeddings_list: List[torch.Tensor] = []
        embeddings_information_list: List[str] = []

        for file in pt_files_from_image_id:
            image_id = file.split(".")[0]  # Get the filename without extension.
            if image_id not in dataset_annotations_dict:
                continue

            # file with keys: "image_meta", "embeddings_data", "embeddings"
            # "image_meta" with keys: "image_id", "label_id", "image_url", "model_id", "geohash", "epoch"
            # "embeddings_data" with keys: "x", "y", "radius", "category". len == # of points
            # "embeddings": embeddings. len == # of points
            datapoints = load_embeddings_from_torch(input=os.path.join(comparison_embeddings_dir, file))
            image_meta = datapoints["image_meta"]
            datapoints_embeddings_data = datapoints["embeddings_data"]
            datapoints_embeddings = datapoints["embeddings"]

            if self.model_id is not None:  # Retrieve points from points_db.
                comparison_embeddings_dict[image_meta["image_id"]] = {}
                comparison_embeddings_dict[image_meta["image_id"]]["embeddings"] = datapoints_embeddings
                comparison_embeddings_dict[image_meta["image_id"]]["embeddings_data"] = [
                    {"x": datapoint_embeddings_data["x"], "y": datapoint_embeddings_data["y"]}
                    for datapoint_embeddings_data in datapoints_embeddings_data
                ]

                for embedding_from_db in self.embeddings_from_db:
                    image_id = image_url2id[embedding_from_db["filepath"]]
                    if (
                        image_id not in comparison_embeddings_dict
                        or embedding_from_db["category"] not in self.weed_categories
                    ):
                        continue

                    comparison_embeddings = comparison_embeddings_dict[image_id]["embeddings"]
                    comparison_embeddings_data = comparison_embeddings_dict[image_id]["embeddings_data"]

                    for comparison_embedding, comparison_embedding_data in zip(
                        comparison_embeddings, comparison_embeddings_data
                    ):
                        if is_within_distance(
                            point1=embedding_from_db, point2=comparison_embedding_data, distance=DISTANCE_THRESHOLD
                        ):
                            embeddings_list.append(comparison_embedding)
                            embeddings_information_list.append(embedding_from_db["uuid"])
                            break

            else:
                for (datapoint_embeddings_data, datapoint_embeddings) in zip(
                    datapoints_embeddings_data, datapoints_embeddings
                ):
                    if datapoint_embeddings_data["category"] not in self.weed_categories:
                        continue
                    for annotation in dataset_annotations_dict[image_id]:
                        if is_within_distance(
                            point1=datapoint_embeddings_data, point2=annotation, distance=DISTANCE_THRESHOLD
                        ):
                            embeddings_list.append(datapoint_embeddings)
                            embeddings_information_list.append(get_globally_unique_id(annotation))
                            break

        LOG.info(f"Get id_to_embedding_dict with size: {len(embeddings_list)}.")
        if len(embeddings_list) == 0:
            raise ValueError("The embeddings_list cannot be empty.")

        return embeddings_list, embeddings_information_list

    def compute_comparison_scores(
        self, embeddings_list: List[torch.Tensor], embeddings_information_list: List[str]
    ) -> List[Dict[str, Any]]:
        # Compute the comparison scores.
        total_embeddings = torch.stack(embeddings_list).to(self.device).half()
        total_embeddings_normalized = F.normalize(total_embeddings, p=2, dim=1)
        # total_embeddings_normalized has a size of [N, 4096]. Performing matrix multiplication involves:
        # N * N * 4096 (multiplications) + N * N * (4096 - 1) (additions) = 8191 * N^2 operations.
        total_points_list = []
        for start in range(0, total_embeddings_normalized.shape[0], self.batch_size):
            end = min(start + self.batch_size, total_embeddings_normalized.shape[0])
            batch_embeddings_normalized = total_embeddings_normalized[start:end]
            similarity_matrix = F.softplus(
                input=cosine_similarity_normed_inputs(
                    normalized_query=batch_embeddings_normalized, normalized_db=total_embeddings_normalized
                ),
                beta=20,
                threshold=2,
            )
            threshold_mask = similarity_matrix > self.threshold
            batch_scores = threshold_mask.sum(dim=1).sub_(1)  # Subtract 1 to exclude itself.
            total_points_list.extend(batch_scores.tolist())

        output_list = [
            {"uuid": uuid, "original_num_similar_points": score}
            for uuid, score in zip(embeddings_information_list, total_points_list)
        ]

        return output_list

    def create_final_dataframe(self, output_list: List[Dict[str, Any]]) -> None:
        df = pd.DataFrame(output_list).sort_values(by="original_num_similar_points", ascending=True)
        min_value = df["original_num_similar_points"].min()
        max_value = df["original_num_similar_points"].max()

        if self.direct_retrieve_top_n_percentage:
            top_n_count = int(len(df) * self.relative_rareness_percentage)
            filtered_df = df.head(top_n_count).drop(columns=["original_num_similar_points"])
        else:
            threshold = max_value * self.relative_rareness_percentage
            filtered_df = df[df["original_num_similar_points"] <= threshold].drop(
                columns=["original_num_similar_points"]
            )

        # Store the results to output_filepath.
        saved_csv_filename = f"point_uuids_t{self.threshold}_r{self.relative_rareness_percentage}.csv"
        base_path = os.path.join(
            CARBON_DATA_DIR, f"deeplearning/comparison/{self.comparison_model_id}/{self.dataset_id}"
        )
        if self.key is not None:
            output_filepath = os.path.join(base_path, f"{self.key}/{self.value}/{saved_csv_filename}")
        else:
            output_filepath = os.path.join(base_path, saved_csv_filename)
        output_dir = os.path.dirname(output_filepath)
        os.makedirs(output_dir, exist_ok=True)
        filtered_df.to_csv(output_filepath, index=False)

        LOG.info(
            f"Enable --direct-retrieve-top-n-percentage: {self.direct_retrieve_top_n_percentage}, "
            f"CSV file saved successfully. Original size: {len(df)}, filtered size: {len(filtered_df)}, "
            f"Max value of 'original_num_similar_points': {max_value}, min value of 'original_num_similar_points': {min_value}."
        )
        if not self.direct_retrieve_top_n_percentage:
            LOG.info(
                f"Filter (percentage, value): ({self.relative_rareness_percentage}, {max_value * self.relative_rareness_percentage})."
            )

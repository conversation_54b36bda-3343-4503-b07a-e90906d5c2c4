import argparse
import json
import os
import random
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.utils.utils import get_dataset
from lib.common.time.time import maka_control_timestamp_ms


def create_annotation_buckets(dataset_id: str) -> Dict[int, List[Dict[str, Any]]]:
    radius_to_annotations = defaultdict(list)

    for split in ["train.json", "validation.json", "test.json"]:
        with open(f"/{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/{split}") as f:
            dataset_json = json.load(f)
            annotations = dataset_json["annotations"]
            category_id_to_category = {cat["id"]: cat["name"] for cat in dataset_json["categories"]}
            image_id_to_meta = {im["id"]: im for im in dataset_json["images"]}
            for annotation in annotations:
                if (
                    annotation["annotation_type"] == "point"
                    and annotation.get("confidence", 0) == 2
                    and annotation["x"] > annotation["radius"]
                    and annotation["x"] < image_id_to_meta[annotation["image_id"]]["width"] - annotation["radius"]
                    and annotation["y"] > annotation["radius"]
                    and annotation["y"] < image_id_to_meta[annotation["image_id"]]["height"] - annotation["radius"]
                ):

                    annotation["category"] = category_id_to_category[annotation["category_id"]]

                    radius_to_annotations[int(annotation["radius"])].append(annotation)

    return radius_to_annotations


def get_two_points(
    radius_to_annotations: Dict[int, List[Dict[str, Any]]], multiplier: float
) -> Optional[Tuple[Dict[str, Any], Dict[str, Any]]]:
    first_size = random.choice(list(radius_to_annotations.keys()))
    lower_size = first_size / multiplier
    upper_size = first_size * multiplier

    eligible_keys = [key for key in radius_to_annotations.keys() if key < lower_size or key > upper_size]
    if len(eligible_keys) < 0:
        return None
    second_size = random.choice(eligible_keys)

    first_point = random.choice(radius_to_annotations[first_size])
    second_point = random.choice(radius_to_annotations[second_size])

    return (first_point, second_point)


def get_n_pairs(
    radius_to_annotations: Dict[int, List[Dict[str, Any]]], multiplier: float, number_items: int
) -> List[Dict[str, Any]]:
    items: List[Dict[str, Any]] = []

    number_of_failures = 0
    while len(items) < number_items and number_of_failures < number_items:
        points = get_two_points(radius_to_annotations, multiplier)
        if points is None:
            number_of_failures += 1
            continue

        first_point, second_point = points
        items.append(
            {
                "image_one": first_point["image_id"],
                "x_one": first_point["x"],
                "y_one": first_point["y"],
                "radius_one": first_point["radius"],
                "category_one": first_point["label"],
                "label_one": first_point["label_id"],
                "image_two": second_point["image_id"],
                "x_two": second_point["x"],
                "y_two": second_point["y"],
                "radius_two": second_point["radius"],
                "category_two": second_point["label"],
                "label_two": second_point["label_id"],
            }
        )

    return items


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-id", type=str, required=True)
    parser.add_argument("--num-items", type=int, default=10000)
    parser.add_argument("--size-multiplier", type=float, default=2)
    parser.add_argument(
        "--save-dir", type=str, default=f"{CARBON_DATA_DIR}/deeplearning/different_size_comparison_labels"
    )
    parser.add_argument("--save-file", type=str, default=f"{maka_control_timestamp_ms()}.csv")
    args = parser.parse_args()

    get_dataset(dataset_id=args.dataset_id)
    radius_to_annotations = create_annotation_buckets(args.dataset_id)
    items = get_n_pairs(radius_to_annotations, args.size_multiplier, args.num_items)

    filepath = os.path.join(args.save_dir, args.save_file)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    df = pd.DataFrame(items)
    df.to_csv(filepath, index=False)


if __name__ == "__main__":
    main()

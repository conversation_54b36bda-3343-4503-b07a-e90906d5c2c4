import argparse
import logging
import os
import uuid

from deeplearning.comparison.data_utils import SAVED_H5_FILENAME
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.embeddings.io import EmbeddingDataset
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    evaluate_comparison_embeddings,
)
from deeplearning.scripts.embeddings.utils import get_local_embedding_filepath, upload_embedding_hdf5
from deeplearning.scripts.utils.elastic_launcher import default, elastic_launcher
from deeplearning.scripts.utils.utils import add_common_arguments, pick_comparison_model_id
from deeplearning.utils.trainer import Environment

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")


def main() -> None:  # noqa: C901
    parser = argparse.ArgumentParser()

    parser.add_argument("--model-id", type=str, required=False, default=None)
    parser.add_argument("--evaluate-files", type=str, default="test.json")
    parser.add_argument("--save-image-point-embedding", action="store_true", help="Save image point embeddings.")
    parser.add_argument(
        "--evaluate-all-data",
        action="store_false",
        dest="only_evaluate_new_data",
        help="Evaluate all data instead of only new data (default: only new data).",
    )
    parser.add_argument(
        "--no-save-hdf5-embedding",
        action="store_false",
        dest="save_hdf5_embedding",
        help="Do not save hdf5 embeddings (default: save).",
    )
    add_common_arguments(parser)

    parser.set_defaults(fast_run=False)

    args = parser.parse_args()

    model_id = pick_comparison_model_id(args.model_id, None)

    if args.preview and not args.fast_run:
        environment = Environment.PREVIEW
    elif args.production and not args.fast_run:
        environment = Environment.PRODUCTION
    else:
        environment = Environment.DEVELOPMENT

    dataset_id = args.dataset_id
    assert dataset_id is not None, "Please set dataset_id"
    LOG.info(f"Using model dataset: {dataset_id}")

    eval_dir = f"{CARBON_DATA_DIR}/deeplearning/embedding_hdf5/{uuid.uuid4()}/{model_id}"
    LOG.info(f"Evaluation directory {eval_dir}")

    eval_config_dict = {
        "model_id": model_id,
        "fast_run": args.fast_run,
        "dataset_id": dataset_id,
        "eval_files": args.evaluate_files.split(","),
        "eval_dir": eval_dir,
        "environment": environment,
        **args.dl_config,
    }

    elastic_launcher(
        args.nproc_per_node,
        evaluate_comparison_embeddings,
        default,
        EvaluateComparisonEmbeddingsArguments(
            comparison_model_id=eval_config_dict["model_id"],
            environment=eval_config_dict["environment"],
            dataset_id=eval_config_dict["dataset_id"],
            fast_run=eval_config_dict["fast_run"],
            eval_files=eval_config_dict["eval_files"],
            eval_dir=eval_config_dict["eval_dir"],
            only_evaluate_new_data=args.only_evaluate_new_data,
            save_image_point_embeddings=args.save_image_point_embedding,
            save_hdf5_embeddings=args.save_hdf5_embedding,
        ),
    )

    filepath = os.path.join(eval_config_dict["eval_dir"], SAVED_H5_FILENAME)
    version_id = EmbeddingDataset(filepath).metadata.version_id

    output_path = get_local_embedding_filepath(dataset_id, model_id, version_id)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    os.rename(
        filepath, output_path,
    )

    if not eval_config_dict["fast_run"]:
        upload_embedding_hdf5(
            file=output_path, dataset_id=dataset_id, model_id=eval_config_dict["model_id"], version_id=version_id
        )

    LOG.info(
        f"Finished comparison embedding with dataset: {eval_config_dict['dataset_id']}, model id: {eval_config_dict['model_id']}. Saved at {output_path}"
    )


if __name__ == "__main__":
    main()

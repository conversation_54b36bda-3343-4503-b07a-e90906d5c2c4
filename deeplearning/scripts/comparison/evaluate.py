import argparse
import concurrent.futures
import logging
import os
import random
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Type

import boto3
import torch
from sqlalchemy.orm.session import sessionmaker

from deeplearning.comparison.config import ComparisonConfig
from deeplearning.comparison.data_utils import (
    construct_comparison_data,
    get_all_image_annotation_information_from_file,
    get_comparison_db_prefix,
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_dir,
    get_comparison_evaluation_prefix,
    get_images_with_comparison_embeddings,
    get_labels_engine,
)
from deeplearning.comparison.models.utils import ComparisonModelBase
from deeplearning.comparison.models_dict import MODELS_DICT
from deeplearning.comparison.sampler import (
    ComparisonLabelSampler,
    ComparisonLabelSamplerBasic,
    ComparisonLabelSamplerCategory,
    ComparisonLabelSamplerHardExample,
    ComparisonLabelSamplerSizeGeo,
    ComparisonLabelSamplerSource,
)
from deeplearning.comparison.trainer import ComparisonTrainer
from deeplearning.comparison.trt_convert import MAX_BATCH_SIZE
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.dl_metrics.comparison_metrics import comparison_db
from deeplearning.model_io.tensorrt import load_tensorrt_model
from deeplearning.scripts.utils.utils import (
    add_job_creator_arguments,
    generate_model_id,
    get_dataset,
    get_pipeline,
    pick_comparison_model_id,
    update_dataset_version,
)
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.trainer import Environment
from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)

DEFAULT_FILES_STRING = "train.jsonl,validation.jsonl,test.jsonl"
SAMPLERS: Dict[str, Type[ComparisonLabelSampler]] = {
    "ComparisonLabelSamplerBasic": ComparisonLabelSamplerBasic,
    "ComparisonLabelSamplerSizeGeo": ComparisonLabelSamplerSizeGeo,
    "ComparisonLabelSamplerCategory": ComparisonLabelSamplerCategory,
    "ComparisonLabelSamplerHardExample": ComparisonLabelSamplerHardExample,
    "ComparisonLabelSamplerSource": ComparisonLabelSamplerSource,
}


def get_model_trt_path(model_id: str) -> str:
    return f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/trt_fp32.trt"


def upload_comparison_db_file(
    bucket: Any, comparison_db_output_path: str, model_id: str, dataset_id: Optional[str]
) -> bool:
    filename = f"comparison_{comparison_db.__version__}.db"
    try:
        bucket.upload_file(
            os.path.join(comparison_db_output_path, filename),
            f"{get_comparison_db_prefix(model_id=model_id, dataset_id=dataset_id)}/{filename}",
        )
        return True
    except Exception as e:
        LOG.warning(f"Error uploading file: {e}")
        return False


def upload_image_file(image_id: str, bucket: Any, evaluation_dir: str, model_id: str, environment: Environment) -> bool:
    filename = f"{image_id}.pt"
    try:
        bucket.upload_file(
            os.path.join(evaluation_dir, filename),
            f"{get_comparison_evaluation_prefix(model_id=model_id, environment=environment)}/{filename}",
        )
        return True
    except Exception as e:
        LOG.warning(f"Error uploading file: {e}")
        return False


def main() -> None:  # noqa: C901
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str, required=False, default=None)
    parser.add_argument("--evaluation-id", type=str, required=False)
    parser.add_argument("--dataset-id", type=str, required=False)
    parser.add_argument("--crop-id", type=str, default=None)
    parser.add_argument("--fast-run", action="store_true", default=False)
    parser.add_argument("--limit-size", type=int, required=False)
    parser.add_argument(
        "--evaluate-files",
        type=str,
        default=DEFAULT_FILES_STRING,
        help="Optionally pass a comma-separated list of splits files to evaluate",
    )
    parser.add_argument(
        "--embedding-increments",
        type=int,
        default=200000,
        help="Will save evaluate and save embeddings in increments of this value. This is helpful for large datasets.",
    )
    parser.add_argument("--only-evaluate-new-data", action="store_true", default=False)
    parser.add_argument("--embedding-output-path", type=str, required=False)
    parser.add_argument("--comparison-db-output-path", type=str, required=False)

    add_job_creator_arguments(parser)

    args = parser.parse_args()

    model_id = pick_comparison_model_id(args.model_id, None)

    assert (
        args.dataset_id is not None or args.pipeline_id is not None or args.crop_id is not None
    ), "One of dataset id or pipeline id must be set"

    if args.preview and not args.fast_run:
        environment = Environment.PREVIEW
    elif args.production and not args.fast_run:
        environment = Environment.PRODUCTION
    else:
        environment = Environment.DEVELOPMENT

    dl_config = ComparisonConfig.from_dict({**args.dl_config})

    dataset_version = update_dataset_version(dataset_id=args.dataset_id,)
    target_suffix = "jsonl" if dataset_version == 2 else "json"
    eval_files = [str(Path(file.strip()).with_suffix(f".{target_suffix}")) for file in args.evaluate_files.split(",")]

    with ComparisonTrainer() as comp_trainer:
        run_comparison_evaluate(
            trainer=comp_trainer,
            environment=environment,
            dl_config=dl_config,
            job_id=args.job_id,
            model_id=model_id,
            embedding_output_path=args.embedding_output_path,
            comparison_db_output_path=args.comparison_db_output_path,
            dataset_id=args.dataset_id,
            fast_run=args.fast_run,
            crop_id=args.crop_id,
            pipeline_id=args.pipeline_id,
            eval_files=eval_files,
            only_evaluate_new_data=args.only_evaluate_new_data,
            limit_size=args.limit_size,
            embedding_increments=args.embedding_increments,
            dataset_version=dataset_version,
        )


def download_model(model_id: str) -> Type[ComparisonModelBase]:
    if not os.path.exists(get_model_trt_path(model_id)):
        download_records(f"{model_id}/trt_fp32.trt")
    _, metadata = load_tensorrt_model(get_model_trt_path(model_id))
    assert metadata.model_class
    model = MODELS_DICT[metadata.model_class]
    return model


def run_comparison_evaluate(  # noqa: C901
    trainer: ComparisonTrainer,
    environment: Environment,
    dl_config: ComparisonConfig,
    job_id: Optional[str],
    model_id: str,
    embedding_output_path: Optional[str],
    comparison_db_output_path: Optional[str],
    dataset_id: str,
    fast_run: bool = False,
    crop_id: Optional[str] = None,
    pipeline_id: Optional[str] = None,
    eval_files: List[str] = DEFAULT_FILES_STRING.split(","),
    only_evaluate_new_data: bool = False,
    limit_size: Optional[int] = None,
    embedding_increments: int = 200000,
    save_image_point_embeddings: bool = True,
    save_hdf5_embeddings: bool = False,
    dataset_version: int = 2,
) -> None:
    db_path = None
    image_dict = None
    captured_at_dict = None
    crop_id_dict = None
    index_ids = None
    image_data = None
    model: Optional[Type[ComparisonModelBase]] = None
    evaluation_dir = None
    evaluation_id = None
    image_id_to_geohash = None
    label_id_to_uuids = None
    image_id_to_height_width = None

    start = maka_control_timestamp_ms()
    if torch.distributed.get_rank() == 0:
        # ------- Creating Evaluation ID
        evaluation_id = job_id if job_id is not None else generate_model_id()
        evaluation_dir = get_comparison_evaluation_dir()

        os.makedirs(evaluation_dir, exist_ok=True)
        LOG.info(f"Evaluation Directory: {evaluation_dir}")

        # ------- Downloading Model
        model = download_model(model_id=model_id)

        # ------- Loading Dataset
        if dataset_id is not None:
            dataset_id, _ = get_dataset(dataset_id=dataset_id, fast_run=fast_run)
        else:
            if crop_id is not None:
                crop_ids = [
                    crop_id,
                ]
            else:
                pipeline = get_pipeline(pipeline_id=pipeline_id)
                crop_ids = list(set(pipeline.data_source_crop_ids + pipeline.deployment_crop_ids))
            dataset_id, _ = get_dataset(
                crop_ids=crop_ids, fast_run=fast_run, balance_positive_classes=True, balance_geohash=4,
            )

    model = broadcast_object(model)
    evaluation_dir = broadcast_object(evaluation_dir)
    evaluation_id = broadcast_object(evaluation_id)
    assert evaluation_dir is not None
    assert evaluation_id is not None
    assert model is not None

    if fast_run:
        limit_size = 64
    for eval_file in eval_files:
        test_ids = None
        overlapped_points_by_circle_dict = None
        overlapped_points_by_center_dict = None
        if torch.distributed.get_rank() == 0:
            LOG.info(f"Evaluating file {eval_file}")
            path = os.path.join(CARBON_DATA_DIR, "deeplearning", "datasets", dataset_id, eval_file)
            LOG.info(f"Getting all image ids from file {path}")

            ignored_image_ids: Set[str] = set()
            if only_evaluate_new_data and not fast_run:
                ignored_image_ids, _ = get_images_with_comparison_embeddings(model_id=model_id)

            (
                test_ids,
                overlapped_points_by_circle_dict,
                overlapped_points_by_center_dict,
            ) = get_all_image_annotation_information_from_file(
                path=path, ignored_image_ids=ignored_image_ids, dataset_version=dataset_version
            )
            LOG.info(f"Got image-annotation ids: {len(test_ids)}, ignoring {len(ignored_image_ids)} images.")

            if limit_size is not None:
                test_ids = random.sample(list(test_ids), min(limit_size, len(test_ids)))

        file_prefix = eval_file.split(".")[0]

        test_ids = broadcast_object(test_ids)
        overlapped_points_by_circle_dict = broadcast_object(overlapped_points_by_circle_dict)
        overlapped_points_by_center_dict = broadcast_object(overlapped_points_by_center_dict)
        assert test_ids is not None
        assert overlapped_points_by_circle_dict is not None
        assert overlapped_points_by_center_dict is not None
        overlapped_points_dict = {
            "circle": overlapped_points_by_circle_dict,
            "center": overlapped_points_by_center_dict,
        }
        for i in range(0, len(test_ids), embedding_increments):
            begin = i
            end = min(i + embedding_increments, len(test_ids))
            if torch.distributed.get_rank() == 0:
                LOG.info(f"Eval file {eval_file} image_ids {begin} -> {end} of {len(test_ids)}")
            start_iteration = maka_control_timestamp_ms()
            test_ids_in_datasets = test_ids[begin:end]

            subset_identifier = None

            db_path = None
            image_dict = None
            image_id_to_geohash = None
            captured_at_dict = None
            index_ids = None
            subset_identifier = None
            crop_id_dict = None
            label_id_to_uuids = None
            image_id_to_height_width = None
            embedding_output_path = None
            comparison_db_output_path = None

            if torch.distributed.get_rank() == 0:
                (
                    image_dict,
                    image_id_to_geohash,
                    captured_at_dict,
                    crop_id_dict,
                    image_data,
                    index_ids,
                    label_id_to_uuids,
                    image_id_to_height_width,
                ) = construct_comparison_data(
                    path=path, image_annotation_ids=test_ids_in_datasets, dataset_version=dataset_version,
                )

                # ------- Setting Up Label Database
                db_path = os.path.join(
                    CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/comparison_labels_evaluate.db"
                )

                if os.path.exists(db_path):
                    os.remove(db_path)

                dataset_engine = get_labels_engine(db_path)
                dataset_session = sessionmaker()
                dataset_session.configure(bind=dataset_engine)
                with dataset_session() as sess:
                    sess.bulk_save_objects(image_data)
                    sess.commit()
                subset_identifier = f"{file_prefix}_{i}"

                embedding_output_path = embedding_output_path or os.path.join(
                    evaluation_dir,
                    get_comparison_evaluation_prefix(model_id=model_id, environment=Environment.PRODUCTION),
                )

                comparison_db_output_path = comparison_db_output_path or os.path.join(
                    evaluation_dir, get_comparison_db_prefix(model_id=model_id, dataset_id=dataset_id)
                )
            db_path = broadcast_object(db_path)
            image_dict = broadcast_object(image_dict)
            image_id_to_geohash = broadcast_object(image_id_to_geohash)
            captured_at_dict = broadcast_object(captured_at_dict)
            index_ids = broadcast_object(index_ids)
            subset_identifier = broadcast_object(subset_identifier)
            crop_id_dict = broadcast_object(crop_id_dict)
            label_id_to_uuids = broadcast_object(label_id_to_uuids)
            image_id_to_height_width = broadcast_object(image_id_to_height_width)
            embedding_output_path = broadcast_object(embedding_output_path)
            comparison_db_output_path = broadcast_object(comparison_db_output_path)

            assert db_path is not None
            assert image_dict is not None
            assert image_id_to_geohash is not None
            assert captured_at_dict is not None
            assert index_ids is not None
            assert crop_id_dict is not None
            assert label_id_to_uuids is not None
            assert image_id_to_height_width is not None
            assert embedding_output_path is not None
            assert comparison_db_output_path is not None

            comparison_evaluation(
                trainer=trainer,
                model_id=model_id,
                model=model,
                fast_run=fast_run,
                save_hdf5_embeddings=save_hdf5_embeddings,
                save_image_point_embeddings=save_image_point_embeddings,
                embedding_output_path=embedding_output_path,
                comparison_db_output_path=comparison_db_output_path,
                evaluation_dir=evaluation_dir,
                dl_config=dl_config,
                db_path=db_path,
                image_dict=image_dict,
                image_id_to_geohash=image_id_to_geohash,
                captured_at_dict=captured_at_dict,
                index_ids=index_ids,
                subset_identifier=subset_identifier,
                crop_id_dict=crop_id_dict,
                dataset_id=dataset_id,
                label_id_to_uuids=label_id_to_uuids,
                image_id_to_height_width=image_id_to_height_width,
                overlapped_points_dict=overlapped_points_dict,
            )
            if torch.distributed.get_rank() == 0:
                LOG.info(f"Total iteration time {(maka_control_timestamp_ms() - start_iteration) // 1000 / 60} minutes")

                if environment == Environment.PRODUCTION:
                    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                        bucket = boto3.resource("s3").Bucket(get_comparison_evaluation_bucket())
                        unique_image_ids = list(set([item[0] for item in test_ids_in_datasets]))
                        futures = []

                        # Upload comparison embedding files to S3.
                        for image_id in unique_image_ids:
                            futures.append(
                                executor.submit(
                                    upload_image_file, image_id, bucket, embedding_output_path, model_id, environment
                                )
                            )

                        # Upload comparison metrics files to S3.
                        futures.append(
                            executor.submit(
                                upload_comparison_db_file,
                                bucket=bucket,
                                comparison_db_output_path=comparison_db_output_path,
                                model_id=model_id,
                                dataset_id=dataset_id,
                            )
                        )

                        number_succeeded = 0
                        number_failed = 0
                        for future in concurrent.futures.as_completed(futures):
                            try:
                                data = future.result()
                                if data:
                                    number_succeeded += 1
                                if not data:
                                    number_failed += 1
                            except Exception as e:
                                LOG.warning(f"Error getting future: {e}")
                                number_failed += 1
                        LOG.info(f"Uploaded: {number_succeeded} succeeded, {number_failed} failed")

            torch.distributed.barrier()

    if torch.distributed.get_rank() == 0:
        LOG.info(f"Total runtime {(maka_control_timestamp_ms() - start) / 1000 / 60 / 60}h ")


def comparison_evaluation(
    trainer: ComparisonTrainer,
    model_id: str,
    model: Type[ComparisonModelBase],
    fast_run: bool,
    save_hdf5_embeddings: bool,
    save_image_point_embeddings: bool,
    evaluation_dir: str,
    dl_config: ComparisonConfig,
    db_path: str,
    embedding_output_path: str,
    comparison_db_output_path: str,
    predictions_and_labels_path: Optional[str] = None,
    image_dict: Optional[Dict[str, Any]] = None,
    image_id_to_geohash: Optional[Dict[str, Optional[str]]] = None,
    captured_at_dict: Optional[Dict[str, int]] = None,
    index_ids: Optional[List[str]] = None,
    subset_identifier: Optional[str] = None,
    crop_id_dict: Optional[Dict[str, str]] = None,
    dataset_id: Optional[str] = None,
    label_id_to_uuids: Optional[Dict[str, str]] = None,
    image_id_to_height_width: Optional[Dict[str, Tuple[int, int]]] = None,
    overlapped_points_dict: Optional[Dict[str, Dict[str, Dict[Tuple[float, float, str], bool]]]] = None,
) -> None:
    image_dict = image_dict if image_dict is not None else {}
    image_id_to_geohash = image_id_to_geohash if image_id_to_geohash is not None else {}
    captured_at_dict = captured_at_dict if captured_at_dict is not None else {}
    index_ids = index_ids if index_ids is not None else []
    crop_id_dict = crop_id_dict if crop_id_dict is not None else {}
    label_id_to_uuids = label_id_to_uuids if label_id_to_uuids is not None else {}
    image_id_to_height_width = image_id_to_height_width if image_id_to_height_width is not None else {}
    dataset_info_dict = {
        "db_path": db_path,
        "image_dict": image_dict,
        "image_id_to_geohash": image_id_to_geohash,
        "captured_at": captured_at_dict,
        "index_ids": index_ids,
        "subset_identifier": subset_identifier,
        "crop_id": crop_id_dict,
        "dataset_id": dataset_id,
        "label_id_to_uuids": label_id_to_uuids,
        "image_id_to_height_width": image_id_to_height_width,
    }

    trainer.explicit_split_dataset(
        label_dataset_path=db_path,
        model_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
        image_id_to_url=image_dict,
        image_id_to_geohash=image_id_to_geohash,
        train_ids_in_dataset=[],
        validation_ids_in_dataset=[],
        test_ids_in_dataset=index_ids,
        source_weights=dl_config.source_weights,
        source_sampler=SAMPLERS[dl_config.source_sampler],
        crop_on_server=False,
        label_id_to_uuids=label_id_to_uuids,
        image_id_to_height_width=image_id_to_height_width,
        overlapped_points_dict=overlapped_points_dict,
    )

    trt_path = get_model_trt_path(model_id)
    trt_path_exists = os.path.exists(trt_path)
    trainer.infer(
        config=dl_config,
        checkpoint_dir=f"/data/deeplearning/models/{model_id}",
        experiment_dir=evaluation_dir,
        fast_run=fast_run,
        model=model,
        resume_from=None if trt_path_exists else f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/ckpt/latest.ckpt",
        trt_filepath=trt_path if trt_path_exists else None,
        save_embeddings_in_db=True,
        dataset_info_dict=dataset_info_dict,
        model_id=model_id,
        embedding_output_path=embedding_output_path,
        comparison_db_output_path=comparison_db_output_path,
        image_id_to_url=image_dict,
        test_batch_size=MAX_BATCH_SIZE,
        data_pipeline_processes=4,
        save_image_point_embeddings=save_image_point_embeddings,
        save_hdf5_embeddings=save_hdf5_embeddings,
        predictions_and_labels_path=predictions_and_labels_path,
        loss_fn=torch.nn.functional.mse_loss,
    )


if __name__ == "__main__":
    main()

import argparse
import logging
import os
from typing import Any, Optional, <PERSON><PERSON>

import boto3
import torch

from deeplearning.comparison.config import ComparisonConfig
from deeplearning.comparison.data_utils import (
    download_comparison_data,
    get_comparison_db_prefix,
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_prefix,
    split_labels,
)
from deeplearning.comparison.trainer import ComparisonTrainer
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.comparison.constants import DATA_S3_KEY_PREFIX
from deeplearning.scripts.comparison.evaluate import comparison_evaluation, download_model
from deeplearning.scripts.utils.utils import add_common_arguments, pick_comparison_model_id
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.trainer import Environment
from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)

PREDICTIONS_AND_LABELS_NAME = "predictions_and_labels.db"


def download_comparison_model(model_id: Optional[str] = None) -> <PERSON><PERSON>[Any, str]:
    comparison_model_id = pick_comparison_model_id(model_id, None,)

    pytorch_model = download_model(comparison_model_id)
    return pytorch_model, comparison_model_id


def main() -> None:
    parser = argparse.ArgumentParser()

    parser.add_argument("--model-id", type=str, default=None)
    parser.add_argument("--evaluation-id", type=str, default=None)

    add_common_arguments(parser)

    args = parser.parse_args()

    trainer = ComparisonTrainer()
    pytorch_model = None
    image_id_to_url = None
    image_id_to_url = None
    db_path = None
    label_splits = None
    image_id_to_geo = None
    image_id_to_height_width = None
    comparison_model_id = None
    evaluation_id = None
    labels = None

    if torch.distributed.get_rank() == 0:
        if args.evaluation_id is not None:
            evaluation_id = args.evaluation_id
        else:
            evaluation_id = str(maka_control_timestamp_ms())
        pytorch_model, comparison_model_id = download_comparison_model(args.model_id)

        data_dir = os.path.join("/data/deeplearning/comparison_data", evaluation_id)
        image_id_to_url, labels, db_path, image_id_to_geo, image_id_to_height_width = download_comparison_data(
            DATA_S3_KEY_PREFIX,
            data_dir,
            only_verified_data=False,
            fast_run=args.fast_run,
            comparison_config=ComparisonConfig(),
        )
        label_splits = split_labels(labels, train_split_percentage=0.0, validation_split_percentage=0.0)

    evaluation_id = broadcast_object(evaluation_id)
    image_id_to_url = broadcast_object(image_id_to_url)
    image_id_to_height_width = broadcast_object(image_id_to_height_width)
    image_id_to_geo = broadcast_object(image_id_to_geo)
    db_path = broadcast_object(db_path)
    label_splits = broadcast_object(label_splits)
    pytorch_model = broadcast_object(pytorch_model)
    comparison_model_id = broadcast_object(comparison_model_id)
    labels = broadcast_object(labels)
    dl_config = ComparisonConfig.from_dict({**args.dl_config})

    assert comparison_model_id is not None
    assert db_path is not None
    assert image_id_to_url is not None
    assert label_splits is not None
    assert labels is not None
    assert image_id_to_height_width is not None

    evaluation_dir = f"{CARBON_DATA_DIR}/deeplearning/hard_example_evaluations/{evaluation_id}"
    predictions_and_labels_db_path = os.path.join(evaluation_dir, PREDICTIONS_AND_LABELS_NAME)
    number_of_steps = 100000
    for i in range(0, len(labels), number_of_steps):
        start = i
        end = min(i + number_of_steps, len(labels))
        if torch.distributed.get_rank() == 0:
            LOG.info(f"Evaluating {start} -> {end} of {len(labels)} items")
        comparison_evaluation(
            trainer=trainer,
            model_id=comparison_model_id,
            model=pytorch_model,
            fast_run=args.fast_run,
            save_hdf5_embeddings=False,
            save_image_point_embeddings=False,
            evaluation_dir=evaluation_dir,
            dl_config=dl_config,
            db_path=db_path,
            image_dict=image_id_to_url,
            index_ids=[label["id"] for label in labels[start:end]],
            image_id_to_height_width=image_id_to_height_width,
            predictions_and_labels_path=predictions_and_labels_db_path,
            embedding_output_path=os.path.join(
                evaluation_dir,
                get_comparison_evaluation_prefix(model_id=comparison_model_id, environment=Environment.DEVELOPMENT),
            ),
            comparison_db_output_path=os.path.join(
                evaluation_dir, get_comparison_db_prefix(model_id=comparison_model_id),
            ),
        )

    if torch.distributed.get_rank() == 0:
        LOG.info(f"Ran hard example miner, saved information at {evaluation_dir}")
        if not args.fast_run:
            dest = f"hard_example_mining/{comparison_model_id}/{evaluation_id}/{PREDICTIONS_AND_LABELS_NAME}"
            bucket = boto3.resource("s3").Bucket(get_comparison_evaluation_bucket())
            bucket.upload_file(predictions_and_labels_db_path, dest)

            LOG.info(f"Uploaded to s3://{get_comparison_evaluation_bucket()}/{dest}")


if __name__ == "__main__":
    main()

import argparse
import json
import logging
import os
import random
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, cast

import pandas as pd
import tqdm

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.utils.utils import get_dataset
from deeplearning.utils.uuid import get_globally_unique_id
from lib.common.time.time import maka_control_timestamp_ms
from lib.common.veselka.client import VeselkaClient

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel("INFO")


def does_square_a_overlap_circle_b(point_a: Dict[str, Any], point_b: Dict[str, Any], square_length: int = 400) -> bool:
    """
    This method should return true if the 400x400 square centered around point_a overlaps with the circle centered
    around point_b. This is done by getting the distance of the closest x and y locations of point_a and seeing if
    they are within the radius of point_b. if dist_x or y are below zero, that means that point_a's x or y is within
    half the square length distance of the circle center, so we just need to check the other dimension
    """
    dist_x = abs(point_b["x"] - point_a["x"]) - square_length // 2
    dist_y = abs(point_b["y"] - point_a["y"]) - square_length // 2
    if dist_x > 0:
        if dist_y > 0:
            return cast(bool, (dist_x ** 2 + dist_y ** 2) < (point_b["radius"] ** 2))
        return cast(bool, dist_x < point_b["radius"])
    return cast(bool, dist_y < point_b["radius"])


def does_square_a_overlap_center_b(point_a: Dict[str, Any], point_b: Dict[str, Any], square_length: int = 400) -> bool:
    """
    This method should return true if the 400x400 square centered around point_a overlaps with the center of point_b.
    """
    x_min, x_max = point_a["x"] - square_length // 2, point_a["x"] + square_length // 2
    y_min, y_max = point_a["y"] - square_length // 2, point_a["y"] + square_length // 2

    return cast(bool, x_min <= point_b["x"] <= x_max and y_min <= point_b["y"] <= y_max)


def get_image_annotations(dataset_id: str) -> Tuple[Dict[str, List[Dict[str, Any]]], List[str], List[str]]:
    dataset_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json"

    dataset_id, _ = get_dataset(dataset_id=dataset_id)
    image_to_annotations = defaultdict(list)
    crop_categories = []
    weed_categories = []

    if os.path.exists(dataset_path):
        with open(dataset_path) as f:
            dataset = json.load(f)

        crop_categories = [item["id"] for item in dataset["categories"] if item["supercategory"] == "crop"]
        weed_categories = [item["id"] for item in dataset["categories"] if item["supercategory"] == "weed"]
        LOG.info("Getting image annotations")
        for annotation in tqdm.tqdm(dataset["annotations"]):
            if annotation["annotation_type"] == "point":
                image_to_annotations[annotation["image_id"]].append(annotation)
    else:

        pc_metadata = VeselkaClient().get_point_categories()

        point_category_to_name = {pc["id"]: pc["name"].upper() for pc in pc_metadata}
        point_category_to_name.update({k.upper(): v for k, v in point_category_to_name.items()})

        dataset_items = []
        weed_category_set: Set[str] = set()
        crop_category_set: Set[str] = set()
        with open(f"{dataset_path}l") as f:
            for line in f.readlines():
                line_items = json.loads(line)
                points = line_items["points"]
                for point in points:
                    point["image_id"] = line_items["image_id"]
                    if point_category_to_name[point["point_category_id"]] == "CROP":
                        crop_category_set.add(point["point_category_id"])
                    else:
                        weed_category_set.add(point["point_category_id"])
                dataset_items.extend(points)

        for annotation in dataset_items:
            image_to_annotations[annotation["image_id"]].append(annotation)

        weed_categories = list(weed_category_set)
        crop_categories = list(crop_category_set)

    return image_to_annotations, crop_categories, weed_categories


def find_overlapping_points(
    image_annotations: Dict[str, List[Dict[str, Any]]],
    square_length: int,
    filter_fn: Optional[Callable[[Dict[str, Any], Dict[str, Any]], bool]],
    keep_overlapping: bool = True,
    spread_by_size: bool = False,
    limit: Optional[int] = None,
) -> Tuple[List[str], List[str]]:
    uuids: List[Tuple[float, str]] = []
    image_ids: List[str] = []
    LOG.info(f"Analyzing {len(image_annotations)} images for overlapping points")
    for _, annotations in tqdm.tqdm(image_annotations.items()):
        for point_a in annotations:
            for point_b in annotations:
                if point_a["id"] == point_b["id"]:
                    continue

                if filter_fn is not None:
                    if not filter_fn(point_a, point_b):
                        continue

                if keep_overlapping == does_square_a_overlap_circle_b(point_a, point_b, square_length):
                    uuids.append((point_a["radius"], get_globally_unique_id(point_a)))
                    image_ids.append(point_a["image_id"])
                    continue
    uuids = list(set(uuids))

    if spread_by_size and limit is not None:
        sorted_uuids = sorted(uuids, key=lambda x: x[0])
        n = len(sorted_uuids) // limit
        uuids = sorted_uuids[::n]

    return [uuid[1] for uuid in uuids], list(set(image_ids))


def write_to_file(items: List[str], key: str, out_path: str) -> None:
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    df = pd.DataFrame([{key: item} for item in items])
    df.to_csv(out_path, index=False)


def get_category(x: Dict[str, Any]) -> str:
    if "category_id" in x:
        return str(x["category_id"])
    else:
        return str(x["point_category_id"])


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-id", type=str, required=True)
    parser.add_argument("--out-dir", type=str, default=f"{CARBON_DATA_DIR}/deeplearning/uuids")
    parser.add_argument("--out-filename", type=str, default=f"{maka_control_timestamp_ms()}_uuid.csv")
    parser.add_argument("--out-image-ids-filename", type=str, default=None)
    parser.add_argument("--square-length", type=int, default=400)
    parser.add_argument("--filter-by-hit-disagreement", action="store_true")
    parser.add_argument("--filter-by-class-disagreement", action="store_true")
    parser.add_argument("--filter-a-only-crop", action="store_true")
    parser.add_argument("--limit", type=int, default=None)
    parser.add_argument("--keep-overlapping", action="store_true", dest="keep_overlapping")
    parser.add_argument("--keep-non-overlapping", action="store_false", dest="keep_overlapping")
    parser.add_argument(
        "--spread-by-size",
        action="store_true",
        help="Orders points by size, then gets every N th point such that we reach the --limit",
    )
    parser.set_defaults(keep_overlapping=True)

    args = parser.parse_args()

    dataset_annotations, crop_categories, weed_categories = get_image_annotations(args.dataset_id)
    filter_fns = []
    filter_fns.append(lambda x, y: True)

    if args.filter_by_hit_disagreement:
        filter_fns.append(
            lambda x, y: (get_category(x) in crop_categories and get_category(y) in weed_categories)
            or (get_category(x) in weed_categories and get_category(y) in crop_categories)
        )
    if args.filter_by_class_disagreement:
        filter_fns.append(lambda x, y: get_category(x) != get_category(y))

    if args.filter_a_only_crop:
        filter_fns.append(lambda x, y: get_category(x) in crop_categories)

    def filter_fn(point_a: Dict[str, Any], point_b: Dict[str, Any]) -> bool:
        return all([flter(point_a, point_b) for flter in filter_fns])

    if args.spread_by_size:
        assert args.limit is not None, "Must specify a limit when spreading by size"
    uuids, image_ids = find_overlapping_points(
        dataset_annotations,
        args.square_length,
        filter_fn,
        keep_overlapping=args.keep_overlapping,
        spread_by_size=args.spread_by_size,
        limit=args.limit,
    )

    if args.limit is not None:
        uuids = random.choices(uuids, k=args.limit)

    out_path = os.path.join(args.out_dir, args.out_filename)
    write_to_file(uuids, "uuid", out_path)
    LOG.info(f"Wrote {len(uuids)} uuids to {out_path}")
    if args.out_image_ids_filename is not None:
        image_out = os.path.join(args.out_dir, args.out_image_ids_filename)
        write_to_file(image_ids, "image_id", image_out)
        LOG.info(f"Wrote {len(image_ids)} image_ids to {image_out}")


if __name__ == "__main__":
    main()

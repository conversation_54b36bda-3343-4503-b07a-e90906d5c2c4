import argparse
import logging
import os
from typing import Any, Dict

import boto3

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.dl_metrics.p2p_metrics import p2p_db
from deeplearning.p2p.config import P2PConfig
from deeplearning.p2p.trainer import P2P<PERSON>rainer
from deeplearning.scripts.p2p.train import PROD_DATA_ROOTS
from deeplearning.scripts.utils.utils import TrainingInfo, add_job_creator_arguments, generate_model_id
from deeplearning.tools import download_makannotations_data

LOG = logging.getLogger(__name__)


def main() -> None:  # noqa

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str, required=True)
    parser.add_argument("--evaluation-id", type=str, default=None)
    parser.add_argument("--fast-run", action="store_true")

    add_job_creator_arguments(parser)

    args = parser.parse_args()

    if args.job_id is None:
        evaluation_id = generate_model_id()
    else:
        evaluation_id = args.job_id

    training_info = TrainingInfo(args.model_id, include_points_db=True)

    data_roots = PROD_DATA_ROOTS
    if args.fast_run:
        data_roots = PROD_DATA_ROOTS[0:1]
    download_makannotations_data.sync_images_with_masks(data_roots, dest_path="/data/deeplearning/p2p")

    trt_int8_path = os.path.join(training_info.data_dir, "trt_int8.trt")
    if os.path.exists(trt_int8_path):
        trt_model_path = trt_int8_path
    else:
        trt_model_path = os.path.join(training_info.data_dir, "trt_fp16.trt")

    trainer = P2PTrainer()

    data_roots_with_path = [os.path.join("/data/deeplearning/p2p/", x) for x in data_roots]
    trainer.uniform_dataset(tuple(data_roots_with_path), smearing=0)

    dl_config_dict: Dict[str, Any] = {"ci_run": True}
    if args.fast_run:
        dl_config_dict["wandb_project"] = "p2p-fast-run"
    dl_config = P2PConfig.from_dict(dl_config_dict)

    trainer.infer(
        config=dl_config,
        fast_run=args.fast_run,
        trt_path=trt_model_path,
        evaluation_dir=f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}",
    )

    bucket = boto3.resource("s3").Bucket("maka-pono")

    bucket.upload_file(
        f"{CARBON_DATA_DIR}/deeplearning/evaluations/{evaluation_id}/test_db/p2p_{p2p_db.__version__}.db",
        f"models/{args.model_id}/test_db/p2p_{p2p_db.__version__}.db",
    )


if __name__ == "__main__":
    main()

import argparse
import logging
import os
import sys


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    parser.add_argument("--tensorrt-version", choices=["*******", "10.0.1"], required=True)
    parser.add_argument("--convert-int8", default=False, action="store_true")
    args, _ = parser.parse_known_args()

    sys.path = [x for x in sys.path if not x.startswith("/opt/TensorRT")]
    sys.path.insert(0, f"/opt/TensorRT-{args.tensorrt_version}/python/site-packages")

    # only load deeplearning code after updating sys.path
    from deeplearning.p2p.trt_convert import TrtConvert
    from deeplearning.scripts.utils.utils import TrainingInfo
    from deeplearning.tools import download_makannotations_data
    from deeplearning.utils.trainer import (
        S3_BUCKET,
        compute_md5sum,
        get_compute_capability,
        get_tensorrt_version,
        upload_directory,
    )
    from lib.common.veselka.client import VeselkaClient

    training_info = TrainingInfo(args.model_id)
    tensorrt_version = get_tensorrt_version().replace(".", "")
    compute_capability = get_compute_capability().replace(".", "")
    trt_file_name = f"trt_{'int8' if args.convert_int8 else 'fp16'}_{tensorrt_version}_{compute_capability}.trt"
    trt_file_path = os.path.join(training_info.data_dir, trt_file_name)

    trt_converter = TrtConvert()
    trt_converter.load_model(training_info.best_model_weights)
    download_makannotations_data.sync_images_with_masks(["media/p2p"], dest_path="/data/deeplearning/p2p")
    trt_converter.load_dataset("/data/deeplearning/p2p")
    trt_converter.convert(
        max_batch_size=1, save_to=trt_file_path, int8=args.convert_int8, fp16=not args.convert_int8,
    )

    upload_directory(training_info.data_dir)

    veselka_client = VeselkaClient()
    veselka_client.post_model_artifact(
        model_id=args.model_id,
        tensorrt_version=get_tensorrt_version(),
        compute_capability=get_compute_capability(),
        url=f"s3://{S3_BUCKET}/models/{args.model_id}/{trt_file_name}",
        checksum=compute_md5sum(trt_file_path),
    )


if __name__ == "__main__":
    main()

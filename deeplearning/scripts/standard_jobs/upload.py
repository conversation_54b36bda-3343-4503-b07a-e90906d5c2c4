import argparse
import logging

from deeplearning.utils.trainer import upload_directory


def main() -> None:
    parser = argparse.ArgumentParser("Upload training files")
    parser.add_argument("model_id")

    args = parser.parse_args()

    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    upload_directory(f"/data/deeplearning/models/{args.model_id}")


if __name__ == "__main__":
    main()

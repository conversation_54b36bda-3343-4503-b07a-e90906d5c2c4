import argparse
import json
import logging

from deeplearning.semi_supervised.classification import generate_classifications
from deeplearning.semi_supervised.config import Config
from deeplearning.semi_supervised.data import compile_semi_supervised_dataset, get_standard_dataset
from deeplearning.semi_supervised.detection import generate_detections
from deeplearning.semi_supervised.train import train_deepweed
from deeplearning.semi_supervised.utils import generate_id

logging.basicConfig()
logging.getLogger().setLevel(logging.INFO)

LOG = logging.getLogger(__name__)

if __name__ == "__main__":

    parser = argparse.ArgumentParser()

    parser.add_argument("--pipeline-id", type=str, required=True)
    parser.add_argument("--dl-config", type=json.loads, default={})
    parser.add_argument("--job-id", type=str, default=None)
    parser.add_argument("--description", type=str, default=None)
    parser.add_argument("--production", action="store_true", dest="production")
    parser.add_argument("--preview", action="store_true", dest="preview")
    parser.add_argument("--fast-run", action="store_true", dest="fast_run")

    parser.set_defaults(production=False, preview=False, fast_run=False)

    args = parser.parse_args()

    # Set up DL config
    config = Config(
        model_id=args.job_id if args.job_id is not None else generate_id(),
        pipeline_id=args.pipeline_id,
        fast_run=args.fast_run,
        description=args.description,
        **args.dl_config,
    )

    LOG.info(f"Config: {config}")

    # Generate and download Veselka Dataset with and without labels
    dataset_id = get_standard_dataset(config)

    # Download most recent pre-trained model and generate predictions using it

    if config.detection_run:
        generate_detections(config, dataset_id)

        if config.classification_run:
            # Generate classifications for every prediction
            generate_classifications(config, dataset_id)

            if config.dataset_compilation_run:
                # Compile results into new dataset objects stored in model's experiment directory
                compile_semi_supervised_dataset(config, dataset_id)

                if config.deepweed_train_run:
                    # Execute standard full training based on newly generated datapoints
                    train_deepweed(config, dataset_id)

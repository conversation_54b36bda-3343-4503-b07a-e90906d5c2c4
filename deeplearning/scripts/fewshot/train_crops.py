import argparse
import json
import logging
import os
import random
import time
from datetime import datetime
from typing import Optional, <PERSON><PERSON>

import numpy as np

import fewshot
from deeplearning.constants import CARBON_DATA_DIR, S3_BUCKET
from deeplearning.scripts.fewshot.utils import Config, CropDataset, GeoDateSampler, convert_trt
from deeplearning.scripts.utils.utils import generate_model_id, get_pipeline
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.trainer import (
    compute_md5sum,
    get_compute_capability,
    get_tensorrt_file_name,
    get_tensorrt_version,
    upload_directory,
)
from lib.common.veselka.client import Pipeline, VeselkaClient

logging.basicConfig()
logging.getLogger().setLevel(logging.INFO)
LOG = logging.getLogger(__name__)


def get_datasets(  # noqa: C901
    config: Config, pipeline: Pipeline, fast_run: bool, local_dataset_path: Optional[str] = None,
) -> <PERSON><PERSON>[fewshot.Dataset, fewshot.Dataset, fewshot.Dataset]:

    np.random.seed(42)

    crops = pipeline.data_source_crop_ids
    crop_ids = crops
    random.seed(10)
    random.shuffle(crop_ids)
    train_crop_ids = crops[: round(len(crop_ids) * 0.7)]
    val_test_crop_ids = crops[round(len(crop_ids) * 0.7) :]

    LOG.info(f"train_crop_ids: \n{train_crop_ids}")
    LOG.info(f"val_test_crop_ids: \n{val_test_crop_ids}")

    client = VeselkaClient(data_dir=f"{CARBON_DATA_DIR}/deeplearning")

    if config.dataset_id:
        dataset_id = config.dataset_id
    else:
        parent_dataset_id = config.parent_dataset_id
        dataset_id = client.create_deepweed_dataset_v2(
            pipeline_id=pipeline.id,
            crop_ids=crop_ids,
            splits=False,
            parent_dataset_id=parent_dataset_id,
            datapoints_limit=5000 if fast_run else None,
        )

    LOG.info(f"Dataset ID: {dataset_id}")

    dataset = client.get_dataset_v2(dataset_id=dataset_id)
    if dataset is None:
        raise RuntimeError(f"Failed to get dataset {dataset_id}")

    dataset_directory = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}"
    os.makedirs(dataset_directory, exist_ok=True)

    if not os.path.exists(f"{dataset_directory}/datapoints.jsonl"):
        client.download_dataset_v2(dataset=dataset)

    LOG.info("Loading dataset metadata into memory...")

    with open(f"{dataset_directory}/datapoints.jsonl", "r") as f:
        lines = [json.loads(line) for line in f]

    for i in range(len(lines)):
        lines[i]["points"] = [p for p in lines[i]["points"] if p["radius"] > 3]

    LOG.info("Generating fewshot dataset splits...")

    point_categories = client.get_point_categories()
    crop_point_category_id = [pc["id"] for pc in point_categories if pc["name"] == "crop"][0]

    train_items = [img for img in lines if img["crop_id"] in train_crop_ids]
    val_test_items = [img for img in lines if img["crop_id"] in val_test_crop_ids]

    indices = np.random.choice(len(val_test_items), len(val_test_items) // 2, replace=False)
    val_items = [val_test_items[i] for i in indices]
    test_item = [val_test_items[i] for i in range(len(val_test_items)) if i not in indices]

    train_data = {img["image_id"]: img["uri"] for img in train_items}
    val_data = {img["image_id"]: img["uri"] for img in val_items}
    test_data = {img["image_id"]: img["uri"] for img in test_item}

    LOG.info(f"{len(train_data)} train images, {len(val_data)} val images, {len(test_data)} test images")

    split_labels = []
    for split_items in [train_items, val_items, test_item]:
        split_labels.append(
            [
                {
                    "x": point["x"],
                    "y": point["y"],
                    "radius": point["radius"],
                    "label": img["crop_id"],
                    "image_id": img["image_id"],
                    "point_id": point["id"],
                    "geo-date": f"{img['geohash'][:config.geo_precision]}-{datetime.fromtimestamp(img['captured_at'] // 1000).strftime('%Y-%m-%d')}",
                }
                for img in split_items
                for point in img["points"]
                if point["point_category_id"] == crop_point_category_id
            ]
        )

    train_labels = split_labels[0]
    val_labels = split_labels[1]
    test_labels = split_labels[2]

    LOG.info(f"{len(train_labels)} train labels, {len(val_labels)} val labels, {len(test_labels)} test labels")

    train_dataset = CropDataset(
        data=train_data,
        labels=train_labels,
        transform_fn=fewshot.utilities.default_training_transform_fn,
        local_dataset_path=local_dataset_path,
        geo_date_sampling=config.geo_date_sampling,
    )
    val_dataset = CropDataset(
        data=val_data,
        labels=val_labels,
        transform_fn=fewshot.utilities.default_evaluation_transform_fn,
        local_dataset_path=local_dataset_path,
        geo_date_sampling=config.geo_date_sampling,
    )
    test_dataset = CropDataset(
        data=test_data,
        labels=test_labels,
        transform_fn=fewshot.utilities.default_evaluation_transform_fn,
        local_dataset_path=local_dataset_path,
        geo_date_sampling=config.geo_date_sampling,
    )

    LOG.info(f"Number of train classes: {len(train_dataset.class_names)}")
    LOG.info(f"Number of val classes: {len(val_dataset.class_names)}")
    LOG.info(f"Number of test classes: {len(test_dataset.class_names)}")

    return train_dataset, val_dataset, test_dataset


def get_model(
    backbone: str,
    pretrained: bool,
    model_type: str = "protonets",
    distance_metric: str = "euclidean",
    dropout_1b1: float = 0.0,
    dropout_3b3: float = 0.0,
) -> fewshot.models.BaseModel:

    if backbone == "simpleCNN":
        encoder = fewshot.encoders.SimpleCNN()
    else:
        encoder = fewshot.encoders.ResNet(
            architecture=backbone,
            pretrained=pretrained,
            final_relu=False,
            dropout_p_1b1=dropout_1b1,
            dropout_p_3b3=dropout_3b3,
        )

    if model_type == "knn":
        model = fewshot.models.KNNClassifier(encoder=encoder, distance_metric=distance_metric)
    else:
        model = fewshot.models.ProtoNets(encoder, distance_metric=distance_metric)
    return model


def get_wandb_project(fast_run: bool = False) -> str:
    return f"protonets-{'fast-run' if fast_run else 'dev'}"


if __name__ == "__main__":

    parser = argparse.ArgumentParser()

    parser.add_argument("--pipeline-id", type=str, default=None)
    parser.add_argument("--dl-config", type=json.loads, default={})
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--job-id", type=str, default=None)
    parser.add_argument("--description", type=str, default=None)
    parser.add_argument("--production", action="store_true", dest="production")
    parser.add_argument("--preview", action="store_true", dest="preview")
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument("--local-dataset-path", type=str, default=None)

    parser.set_defaults(production=False, preview=False, fast_run=False)

    args = parser.parse_args()

    assert not (args.preview and args.production), "Cannot be both preview and production"

    if args.fast_run:
        args.dl_config["num_epochs"] = 1
        args.dl_config["num_training_episodes"] = 1
        args.dl_config["num_validation_episodes"] = 1
        args.dl_config["num_evaluation_episodes"] = 1

    assert args.dl_config.get("backbone", "resnet50") in [
        "simpleCNN",
        "resnet50",
        "resnet18",
        "resnet152",
    ], f"Unknown backbone: {args.dl_config['backbone']}"

    print(args.dl_config)

    config = Config(
        **{
            "model_id": args.job_id if args.job_id is not None else generate_model_id(),
            "dataset_id": args.dataset_id if args.dataset_id is not None else None,
            "description": args.description,
            **args.dl_config,
        }
    )
    LOG.info(f"Robot side trianing config: \n{json.dumps(config.__dict__, indent = 4)}")

    stub_json = {"id": config.model_id}

    VeselkaClient().post_model(stub_json)

    pipeline = get_pipeline(args.pipeline_id)

    train_data, val_data, test_data = get_datasets(
        config, pipeline, args.fast_run, local_dataset_path=args.local_dataset_path
    )

    parent_model_id = args.dl_config.get("parent_model_id", None)
    parent_model = None

    if parent_model_id is not None:
        LOG.info("Downloading parent model")
        download_records(parent_model_id, s3_directory="models", skip_existing_files=True)

        with open(f"{CARBON_DATA_DIR}/deeplearning/models/{parent_model_id}/config.json") as f:
            parent_config = json.load(f)

        config.parent_dataset_id = parent_config["dataset_id"]

        parent_backbone = parent_config.get("backbone", "resnet50")

        weights_path = f"{CARBON_DATA_DIR}/deeplearning/models/{parent_model_id}/weights/best.pt"
        parent_model = get_model(
            backbone=parent_backbone,
            pretrained=False,
            model_type=parent_config.model_type,
            distance_metric=parent_config.distance_metric,
        )
        parent_model.load_weights(weights_path)

    if config.resume_from_model_id is not None:
        LOG.info("Downloading base model")
        download_records(config.resume_from_model_id, s3_directory="models", skip_existing_files=True)

        with open(f"{CARBON_DATA_DIR}/deeplearning/models/{config.resume_from_model_id}/config.json") as f:
            res_config = Config(**json.load(f))

        weights_path = f"{CARBON_DATA_DIR}/deeplearning/models/{config.resume_from_model_id}/weights/best.pt"
        config.backbone = res_config.backbone
        model = get_model(
            backbone=res_config.backbone,
            pretrained=False,
            model_type=res_config.model_type,
            distance_metric=res_config.distance_metric,
            dropout_1b1=config.dropout_1b1,
            dropout_3b3=config.dropout_3b3,
        )
        model.load_weights(weights_path)

    else:
        model = get_model(
            backbone=config.backbone,
            pretrained=config.pretrained,
            model_type=config.model_type,
            distance_metric=config.distance_metric,
            dropout_1b1=config.dropout_1b1,
            dropout_3b3=config.dropout_3b3,
        )

    experiment_directory = f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}"

    # save config to experiment directory
    os.makedirs(experiment_directory, exist_ok=True)
    with open(f"{experiment_directory}/config.json", "w") as f:
        json.dump(config.model_dump(), f)

    fewshot.train(
        model,
        train_data,
        val_data,
        parent_model=parent_model,
        data_dir=experiment_directory,
        wandb_project=get_wandb_project(args.fast_run),
        parent_data_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{parent_model_id}"
        if parent_model_id is not None
        else None,
        sampler=GeoDateSampler if config.geo_date_sampling else fewshot.samplers.Sampler,
        **config.model_dump(),
    )

    best_weights_filepath = f"{experiment_directory}/weights/best.pt"
    model.load_weights(best_weights_filepath)

    fewshot.evaluate(
        model,
        test_data,
        wandb_project=get_wandb_project(args.fast_run),
        num_episodes=config.num_evaluation_episodes,
        num_classes=config.num_validation_classes,
        num_support_samples=config.num_support_samples,
        num_query_samples=config.num_query_samples,
        model_id=config.model_id,
        data_dir=experiment_directory,
        num_workers=config.num_workers,
        learning_rate=config.learning_rate,
        sampler=GeoDateSampler if config.geo_date_sampling else fewshot.samplers.Sampler,
    )

    trt_path = f"{experiment_directory}/{get_tensorrt_file_name(convert_int8=True)}"

    convert_trt(
        model=model,
        calib_dataset=train_data,
        validation_dataset=val_data,
        config=config,
        trt_path=trt_path,
        num_samples=500,
    )

    model_json = {
        "id": config.model_id,
        "trained_at": int(time.time()),
        "url": f"s3://{S3_BUCKET}/models/{config.model_id}/{get_tensorrt_file_name(convert_int8=True)}",
        "dl_config": config.model_dump(),
        "tensorrt_version": get_tensorrt_version(),
        "compute_capability": get_compute_capability(),
        "pipeline_id": args.pipeline_id,
        "dataset_id": args.dataset_id,
        "checksum": compute_md5sum(trt_path),
        "fast_run": args.fast_run,
        "description": args.description,
        "type": "fewshot",
    }

    VeselkaClient().post_model(model_json)

    upload_directory(experiment_directory)

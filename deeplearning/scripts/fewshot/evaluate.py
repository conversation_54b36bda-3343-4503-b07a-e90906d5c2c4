import argparse
import datetime
import json
import logging
import os
from typing import Any, Dict, List, Tuple

import numpy as np
import numpy.typing as npt
import pandas as pd
import plotly.express as px
import tqdm
import wandb

import fewshot
from deeplearning.scripts.embeddings.classifiers.centroids import CentroidClassifier
from deeplearning.scripts.embeddings.classifiers.knn import KNNClassifier
from deeplearning.scripts.embeddings.classifiers.medoids import MedoidClassifier
from deeplearning.scripts.fewshot.utils import Datapoint, get_image_from_cache
from lib.common.veselka.client import VeselkaClient

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


class Partition:
    def __init__(self, datapoints: List[Datapoint], labels: List[str], date: datetime.date, geohash: str):
        self._datapoints = datapoints
        self._labels = labels
        self._date = date
        self._geohash = geohash
        self._num_classes = len(list(set(self._labels)))

    @property
    def datapoints(self) -> List[Datapoint]:
        return self._datapoints

    @property
    def labels(self) -> List[str]:
        return self._labels

    @property
    def date(self) -> datetime.date:
        return self._date

    @property
    def geohash(self) -> str:
        return self._geohash

    @property
    def num_classes(self) -> int:
        return self._num_classes


def read_embeddings(filepath: str) -> npt.NDArray[Any]:

    embeddings_list: List[npt.NDArray[Any]] = []
    with open(filepath, "r") as f:
        for line in f.readlines():
            data = json.loads(line)
            embedding = np.array(data["embedding"])
            embeddings_list.append(embedding)

        embeddings: npt.NDArray[Any] = np.stack(embeddings_list)

    return embeddings


def make_embeddings_heatmap(embeddings: npt.NDArray[Any], filepath: str) -> None:
    embeddings = embeddings.transpose()
    embeddings = embeddings / embeddings.max()
    figure = px.imshow(embeddings)

    figure.update_layout(title="Datapoint Embedding Heatmap",)

    figure.write_image(filepath, scale=3)


def make_embeddings_distribution_heatmap(embeddings: npt.NDArray[Any], filepath: str, num_bins: int = 200) -> None:

    embedding_dim = embeddings.shape[1]
    distributions = np.zeros((num_bins, embedding_dim))

    min_value = embeddings.min()
    max_value = embeddings.max()

    bin_size = (max_value - min_value) / (num_bins - 1)
    bucket_indices = np.floor(((embeddings + min_value) // bin_size)).astype(int)
    bucket_indices[bucket_indices == num_bins] = num_bins - 1

    for index, item in enumerate(bucket_indices.T):
        bin_counts = np.bincount(item, minlength=num_bins)
        bin_counts = bin_counts / bin_counts.sum()
        distributions[:, index] = bin_counts

    figure = px.imshow(distributions, origin="lower")

    # Compute largest integer value
    max_integer = np.floor(max_value)
    max_location = max_integer / bin_size

    # Compute smallest integer value
    min_integer = np.ceil(min_value)
    min_location = min_integer / bin_size

    figure.update_layout(
        title="Embedding Distribution Heatmap",
        yaxis={"tickmode": "array", "tickvals": [min_location, max_location], "ticktext": [min_integer, max_integer]},
    )
    figure.write_image(filepath, scale=3)


def make_embeddings_mean_variance_plot(embeddings: npt.NDArray[Any], filepath: str) -> None:

    mean = np.mean(embeddings, axis=0)
    variance = np.var(embeddings, axis=0)

    df = pd.DataFrame({"mean": mean, "variance": variance})
    figure = px.line(df, x=df.index, y=["mean", "variance"])
    figure.update_traces(line=dict(width=0.5))
    figure.update_layout(yaxis_range=[0, 5])

    figure.write_image(filepath, scale=3)


def make_tsv_files(filepath: str, directory: str) -> None:

    filename = os.path.basename(filepath)

    embedding_filename = filename.replace(".jsonl", ".tsv")
    metadata_filename = filename.replace(".jsonl", "-metadata.tsv")

    metadata = []
    embeddings = []
    with open(filepath, "r") as f:
        for line in f.readlines():
            data = json.loads(line)
            embedding = np.array(data["embedding"])

            metadata.append(data["identifier"])
            embeddings.append(embedding)

    with open(f"{directory}/{embedding_filename}", "w") as f:
        for embedding in embeddings:
            f.write("\t".join([str(x) for x in embedding]) + "\n")

    with open(f"{directory}/{metadata_filename}", "w") as f:

        keys = list(metadata[0].keys())

        f.write("\t".join(keys) + "\n")

        for identifier in metadata:
            line = "\t".join([str(identifier[key]) for key in keys]) + "\n"
            f.write(line)


def download_model(model_id: str) -> None:
    pass


def get_model(embedding_dimension: int, model_type: str, distance_metric: str, k: int = 5) -> fewshot.models.BaseModel:
    encoder = fewshot.encoders.EmbeddingsReader(embedding_size=embedding_dimension)
    if model_type == "centroid":
        model = CentroidClassifier(encoder=encoder, metric=distance_metric)
    elif model_type == "medoid":
        model = MedoidClassifier(encoder=encoder, metric=distance_metric)
    elif model_type == "knn":
        model = KNNClassifier(encoder=encoder, metric=distance_metric, k=k)
    return model


def create_dataset_partitions(dataset_id: str, categories: bool = True) -> List[Partition]:

    LOG = logging.getLogger("fewshot.evaluate")

    splits = [
        f"/data/deeplearning/datasets/{dataset_id}/train.json",
        f"/data/deeplearning/datasets/{dataset_id}/validation.json",
        f"/data/deeplearning/datasets/{dataset_id}/test.json",
    ]

    partitions_dict: Dict[str, List[Datapoint]] = {}
    partitions_info_dict: Dict[str, Tuple[datetime.date, str]] = {}
    for filepath in splits:

        LOG.info(f"Loading dataset split from {filepath}")

        with open(filepath) as f:
            data = json.load(f)

        images = data["images"]
        annotations = data["annotations"]

        image_id2image = {image["id"]: image for image in images}

        for annotation in tqdm.tqdm(annotations):
            label = annotation["label"]

            if label == "driptape":
                continue

            x = annotation["x"]
            y = annotation["y"]
            radius = annotation["radius"]
            image_id = annotation["image_id"]
            confidence = annotation["confidence"]

            if confidence < 2:
                continue

            image = image_id2image[image_id]
            geohash = image["geohash"][:6]
            captured_at = image["captured_at"]
            key = image["uri"][5:]

            date = datetime.datetime.fromtimestamp(captured_at / 1000).date()

            if label in ["broadleaf", "offshoot", "purslane", "grass"]:
                if categories:
                    aliased_label = label
                else:
                    aliased_label = "weed"
            else:
                aliased_label = "crop"

            datapoint = Datapoint(
                image_id=image_id,
                x=x,
                y=y,
                radius=radius,
                label=aliased_label,
                s3_key=key,
                captured_at=image["captured_at"],
                geohash=image["geohash"],
                image_crop_id=image["crop_id"],
            )

            partition_key = f"{date}-{geohash}"

            if partition_key in partitions_dict.keys():
                partitions_dict[partition_key].append(datapoint)
            else:
                partitions_dict[partition_key] = [datapoint]
                partitions_info_dict[partition_key] = (date, geohash)

    partitions = []
    for key, datapoints in partitions_dict.items():

        if len(datapoints) < 500:
            continue

        sorted_datapoints: Dict[str, List[Datapoint]] = {}
        for datapoint in datapoints:
            if datapoint.label not in sorted_datapoints.keys():
                sorted_datapoints[datapoint.label] = []

            sorted_datapoints[datapoint.label].append(datapoint)

        category_counts_check = True
        for k, v in sorted_datapoints.items():

            if len(v) < 50:
                category_counts_check = False

        if category_counts_check:

            date, geohash = partitions_info_dict[key]

            partition = Partition(datapoints, [datapoint.label for datapoint in datapoints], date, geohash,)

            partitions.append(partition)

            LOG.info(
                f"Added partition: {partition.date} - {partition.geohash} | {len(partition.datapoints)} datapoints"
            )

    return partitions


def get_fewshot_dataset(partition: Partition) -> fewshot.Dataset:
    dataset = fewshot.Dataset(data=partition.datapoints, labels=partition.labels, load_fn=get_image_from_cache)
    return dataset


def analyze_logs(filepath: str) -> None:

    with open(filepath) as f:

        data = []
        for line in f.readlines():
            episode = json.loads(line.rstrip())

            rank = episode["rank"]
            episode_index = episode["episode_index"]
            predictions = episode["data"]

            for prediction in predictions:
                prediction["rank"] = rank
                prediction["episode_index"] = episode_index
            data.append(prediction)

    df = pd.DataFrame(data)

    crop_predicted_as_crop = df[(df["label"] == "crop") & (df["prediction"] == "crop")].shape[0]
    crop_predicted_as_weed = df[(df["label"] == "crop") & (df["prediction"] == "weed")].shape[0]
    weed_predicted_as_weed = df[(df["label"] == "weed") & (df["prediction"] == "weed")].shape[0]
    weed_predicted_as_crop = df[(df["label"] == "weed") & (df["prediction"] == "crop")].shape[0]

    print("-" * 80)
    print(filepath)
    print("     crop,  weed")
    print("crop", crop_predicted_as_crop, crop_predicted_as_weed)
    print("weed", weed_predicted_as_crop, weed_predicted_as_weed)

    threshold = 0.9

    crops_detected = df[(df["prediction"] == "crop") & (df["confidence"] > threshold) & (df["label"] == "crop")].shape[
        0
    ]
    weeds_detected = df[(df["prediction"] == "weed") & (df["confidence"] > threshold) & (df["label"] == "weed")].shape[
        0
    ]
    crops_detected_as_weeds = df[
        (df["prediction"] == "weed") & (df["confidence"] > threshold) & (df["label"] == "crop")
    ].shape[0]
    weeds_detected_as_crops = df[
        (df["prediction"] == "crop") & (df["confidence"] > threshold) & (df["label"] == "weed")
    ].shape[0]

    total_crops = df[df["label"] == "crop"].shape[0]
    total_weeds = df[df["label"] == "weed"].shape[0]

    crop_detections = df[(df["prediction"] == "crop") & (df["confidence"] > threshold)].shape[0]
    weed_detections = df[(df["prediction"] == "weed") & (df["confidence"] > threshold)].shape[0]

    print(f"Crops detected: {crops_detected}, Percent: {crops_detected / total_crops}")
    print(f"Weeds detected: {weeds_detected}, Percent: {weeds_detected / total_weeds}")
    print(f"Crops detected as weeds: {crops_detected_as_weeds}, Percent: {crops_detected_as_weeds / crop_detections}")
    print(f"Weeds detected as crops: {weeds_detected_as_crops}, Percent: {weeds_detected_as_crops / weed_detections}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--pipeline-id", type=str, default=None)
    parser.add_argument("--model-id", type=str, default=None)
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument("--categories", action="store_true")
    parser.add_argument("--model_type", type=str, default="centroid")
    parser.add_argument("--distance-metric", type=str, default="euclidean")
    parser.add_argument("--k", type=int, default=1)
    parser.add_argument("--description", type=str, default=None)

    parser.set_defaults(fast_run=False, categories=False)
    args = parser.parse_args()

    pipeline_id = args.pipeline_id
    model_id = args.model_id
    fast_run = args.fast_run
    dataset_id = args.dataset_id
    categories = args.categories
    model_type = args.model_type
    distance_metric = args.distance_metric
    k = args.k

    veselka_client = VeselkaClient(use_carbon_cache=True)
    pipeline = veselka_client.get_pipeline(pipeline_id)

    # Get model from s3 based on supplied model ID
    download_model(model_id)

    # Instantiate the model
    model = get_model(model_id, model_type=model_type, distance_metric=distance_metric, k=k)

    LOG.info("Creating dataset...")

    # Get dataset from Veselka based on pipeline ID
    # NOTE: This downloads the dataset to /data/deeplearning/datasets/{dataset_id}
    if dataset_id is None:
        dataset_info = veselka_client.create_deepweed_dataset(crop_ids=pipeline.data_source_crop_ids, fast_run=fast_run)
        dataset_id = dataset_info["id"]
    else:
        dataset_info = veselka_client.get_dataset(dataset_id)

    veselka_client.download_dataset(dataset_info)

    import time

    temp_id = f"{time.time()}"
    os.makedirs(f"/data/deeplearning/models/{temp_id}", exist_ok=True)
    wandb.init(id=temp_id, project="protonets-evaluation")
    wandb.finish()

    partitions = create_dataset_partitions(dataset_id, categories=categories)
    import random

    random.shuffle(partitions)
    # Loop over partitioned datasets

    embedding_files: List[str] = []
    log_files: List[str] = []
    for partition in partitions:

        LOG.info(f"Evaluating partition: {partition.date}-{partition.geohash}")
        LOG.info(f"Number of datapoints: {len(partition.datapoints)}")
        LOG.info(f"Number of classes: {partition.num_classes}")

        dataset = get_fewshot_dataset(partition)

        data_dir = "/data/deeplearning/models"
        logging_prefix = f"evaluation-{partition.date}-{partition.geohash}"
        data_dir = f"/data/deeplearning/models/{temp_id}"

        fewshot.evaluate(
            model,
            dataset,
            model_id=temp_id,
            wandb_project="protonets-evaluation",
            data_dir=data_dir,
            num_episodes=25,
            num_classes=partition.num_classes,
            num_support_samples=15,
            num_query_samples=32,
            logging_prefix=logging_prefix,
        )

        dataset = get_fewshot_dataset(partition)

        fewshot.embed(
            model._encoder, dataset, data_dir=data_dir, logging_prefix=logging_prefix,
        )
        embedding_filepath = f"{data_dir}/embeddings/{logging_prefix}-embeddings.jsonl"
        log_file = f"/data/deeplearning/models/{temp_id}/{logging_prefix}-results.jsonl"

        embeddings = read_embeddings(embedding_filepath)

        filepath = f"{data_dir}/embeddings/heatmap/{logging_prefix}.png"
        os.makedirs(f"{data_dir}/embeddings/heatmap", exist_ok=True)
        make_embeddings_heatmap(embeddings, filepath)

        filepath = f"{data_dir}/embeddings/distribution-heatmap/{logging_prefix}.png"
        os.makedirs(f"{data_dir}/embeddings/distribution-heatmap", exist_ok=True)
        make_embeddings_distribution_heatmap(embeddings, filepath)

        filepath = f"{data_dir}/embeddings/mean-variance/{logging_prefix}.png"
        os.makedirs(f"{data_dir}/embeddings/mean-variance", exist_ok=True)
        make_embeddings_mean_variance_plot(embeddings, filepath)

        make_tsv_files(embedding_filepath, f"{data_dir}/embeddings")

        log_files.append(log_file)

        for log_file in log_files:
            analyze_logs(log_file)

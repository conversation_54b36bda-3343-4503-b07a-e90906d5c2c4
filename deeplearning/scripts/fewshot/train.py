import argparse
import dataclasses
import datetime
import hashlib
import json
import logging
import os
import random
import struct
import time
from typing import Tuple

import boto3
import dataclass_wizard
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential

import fewshot
from deeplearning.scripts.utils.utils import generate_model_id
from deeplearning.utils.trainer import upload_directory

logging.basicConfig()
logging.getLogger().setLevel(logging.INFO)
LOG = logging.getLogger(__name__)


@dataclasses.dataclass(eq=True, frozen=True)
class Config(dataclass_wizard.JSONWizard):
    dataset_id: str
    model_id: str
    num_epochs: int = 100
    num_training_episodes: int = 1000
    num_validation_episodes: int = 250
    num_evaluation_episodes: int = 250


@retry(
    wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
    stop=stop_after_delay(30 * 60),
    before_sleep=before_sleep_log(LOG, logging.INFO),
)
def load_image(key: str) -> Image.Image:

    filename = key.split("/")[-1]
    class_dir = filename.split("_")[0]
    image = Image.open(f"/data_ro/imagenet/{class_dir}/{filename}").convert("RGB")

    return image


def get_datasets(config: Config) -> Tuple[fewshot.Dataset, fewshot.Dataset, fewshot.Dataset]:

    dataset_directory = f"/data/deeplearning/datasets/{config.dataset_id}"
    os.makedirs(dataset_directory, exist_ok=True)

    s3_bucket_write = "maka-pono"
    s3_key_prefix_write = (
        f"models/{config.model_id}/dataset/{config.dataset_id}/"  # Uploading a lot of datasets right now, maybe bad?
    )

    s3_bucket = "carbon-ml"
    s3_key_prefix = f"datasets/{config.dataset_id}/"

    s3_client = boto3.client("s3")

    # Check to see if the dataset already exists
    response = s3_client.list_objects(Bucket=s3_bucket, Prefix=s3_key_prefix, Delimiter="/", MaxKeys=1)
    dataset_exists = "Contents" in response

    if dataset_exists:
        LOG.info("Dataset already exists. Downloading from s3...")

        for split in ["train", "val", "test"]:
            s3_client.download_file(s3_bucket, f"{s3_key_prefix}{split}.json", f"{dataset_directory}/{split}.json")
    else:
        LOG.info("Creating new dataset...")

        imagenet_training_data_prefix = "imagenet/ILSVRC2012/extracted_data/ILSVRC2012_img_train/"
        response = s3_client.list_objects(
            Bucket=s3_bucket, Prefix=imagenet_training_data_prefix, Delimiter="/", MaxKeys=2000
        )

        class_names = [item["Prefix"].split("/")[-2] for item in response["CommonPrefixes"]]
        assert len(class_names) == 1000, f"Expected 1000 imagenet classes but found {len(class_names)}"

        random.shuffle(class_names)

        splits = {
            "train": class_names[:700],
            "val": class_names[700:850],
            "test": class_names[850:],
        }

        for split, class_names in splits.items():
            LOG.info(f"Creating {split} split ...")
            with open(f"{dataset_directory}/{split}.json", "w") as f:
                for class_name in class_names:
                    s3_class_prefix = f"{imagenet_training_data_prefix}{class_name}/"

                    paginator = s3_client.get_paginator("list_objects_v2")
                    pages = paginator.paginate(Bucket=s3_bucket, Prefix=s3_class_prefix)

                    for page in pages:
                        for object in page["Contents"]:
                            key = object["Key"]
                            f.write(f"{key},{class_name}\n")

            s3_client.upload_file(
                f"{dataset_directory}/{split}.json", s3_bucket_write, f"{s3_key_prefix_write}{split}.json"
            )

    for split in ["train", "val", "test"]:

        LOG.info(f"Loading {split} into memory...")

        with open(f"{dataset_directory}/{split}.json", "r") as f:
            lines = f.readlines()

        data = [line.split(",")[0] for line in lines]
        labels = [line.split(",")[1].strip() for line in lines]

        dataset = fewshot.Dataset(data=data, labels=labels, load_fn=load_image)

        if split == "train":
            train_dataset = dataset
        elif split == "val":
            val_dataset = dataset
        elif split == "test":
            test_dataset = dataset

    return train_dataset, val_dataset, test_dataset


def get_model(test_model: bool) -> fewshot.models.BaseModel:

    if test_model:
        encoder = fewshot.encoders.SimpleCNN(classify=False)
    else:
        encoder = fewshot.encoders.ResNet(architecture="resnet50", pretrained=False)

    model = fewshot.models.ProtoNets(encoder)
    return model


def get_wand_project() -> str:
    current_month_and_year = datetime.datetime.now().strftime("%Y-%m")
    return f"protonets-{current_month_and_year}"


def generate_dataset_id() -> str:
    md5 = hashlib.md5()
    md5.update(struct.pack("d", time.time()))
    dataset_id = md5.hexdigest()
    return dataset_id


if __name__ == "__main__":

    parser = argparse.ArgumentParser()

    parser.add_argument("--pipeline-id", type=str, default=None)
    parser.add_argument("--dl-config", type=json.loads, default={})
    parser.add_argument("--job-id", type=str, default=None)
    parser.add_argument("--description", type=str, default=None)
    parser.add_argument("--production", action="store_true", dest="production")
    parser.add_argument("--preview", action="store_true", dest="preview")
    parser.add_argument("--fast-run", action="store_true")

    parser.set_defaults(production=False, preview=False, fast_run=False)

    args = parser.parse_args()

    assert not (args.preview and args.production), "Cannot be both preview and production"

    config = Config(
        **{
            "model_id": args.job_id if args.job_id is not None else generate_model_id(),
            "dataset_id": generate_dataset_id(),
            "num_epochs": 1 if args.fast_run else 1000,
            "num_training_episodes": 1 if args.fast_run else 1000,
            "num_validation_episodes": 1 if args.fast_run else 250,
            "num_evaluation_episodes": 1 if args.fast_run else 250,
            **args.dl_config,
        }
    )

    LOG.info(f"Config: {dataclasses.asdict(config)}")

    train_data, val_data, test_data = get_datasets(config)

    model = get_model(args.fast_run)

    data_dir = "/data/deeplearning/models"
    experiment_directory = f"{data_dir}/{config.model_id}"

    # save config to experiment directory
    os.makedirs(experiment_directory, exist_ok=True)
    with open(f"{experiment_directory}/config.json", "w") as f:
        json.dump(dataclasses.asdict(config), f)

    fewshot.train(
        model,
        train_data,
        val_data,
        num_epochs=config.num_epochs,
        num_training_episodes=config.num_training_episodes,
        num_validation_episodes=config.num_validation_episodes,
        wandb_project=get_wand_project(),
        model_id=config.model_id,
        data_dir=experiment_directory,
    )

    best_weights_filepath = f"{experiment_directory}/weights/best.pt"
    model.load_weights(best_weights_filepath)

    fewshot.evaluate(
        model,
        test_data,
        wandb_project=get_wand_project(),
        num_episodes=config.num_evaluation_episodes,
        model_id=config.model_id,
        data_dir=experiment_directory,
    )

    upload_directory(experiment_directory)

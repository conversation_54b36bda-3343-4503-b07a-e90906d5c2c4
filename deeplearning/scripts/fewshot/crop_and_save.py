import argparse

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.fewshot.utils import crop_and_save
from lib.common.veselka.client import VeselkaClient

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-id", type=str, required=True)
    parser.add_argument("--output-dir", type=str, required=True)
    parser.add_argument("--fixed-radius", type=int, default=None)
    parser.add_argument("--format", type=str, default="png")
    args = parser.parse_args()

    client = VeselkaClient(data_dir=f"{CARBON_DATA_DIR}/deeplearning")

    point_categories = client.get_point_categories()
    crop_point_category_id = [pc["id"] for pc in point_categories if pc["name"] == "crop"][0]

    dataset_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{args.dataset_id}/datapoints.jsonl"

    crop_and_save(dataset_path, args.output_dir, args.fixed_radius, format=args.format)

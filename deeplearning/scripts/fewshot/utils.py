import io
import json
import logging
import multiprocessing
import os
import shutil
import time
from typing import Any, Callable, Dict, Iterator, List, Optional, Tuple, Union

import boto3
import numpy as np
import PIL
import pydantic
import requests
import tensorrt as trt
import torch
import tqdm
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential
from torch2trt import torch2trt

import fewshot
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.tensorrt import save_tensorrt_model
from deeplearning.utils.dataset import pil_loader
from fewshot import dataset_types
from fewshot.utilities import IMAGENET_MEAN, IMAGENET_STD, default_evaluation_transform_fn
from lib.common.s3_cache_proxy.client import S3CacheProxyClient
from lib.common.veselka.client import VeselkaClient

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


class Config(pydantic.BaseModel):
    dataset_id: Optional[str]
    model_id: str
    num_epochs: int = 100
    num_classes: int = 5
    num_validation_classes: int = 5
    num_support_samples: int = 5
    num_query_samples: int = 8
    num_training_episodes: int = 250
    num_validation_episodes: int = 100
    num_evaluation_episodes: int = 100
    pretrained: bool = True
    num_workers: int = 4
    weight_decay: float = 0
    parent_dataset_id: Optional[str] = None
    parent_model_id: Optional[str] = None
    embed_parent_model: bool = False
    backbone: str = "resnet50"
    verbose: bool = False
    learning_rate: float = 1e-4
    description: Optional[str] = None
    dropout_1b1: float = 0.0
    dropout_3b3: float = 0.0
    geo_date_sampling: bool = False
    geo_precision: int = 4
    distance_metric: str = "euclidean"
    model_type: str = "protonets"
    resume_from_model_id: Optional[str] = None


class Datapoint(pydantic.BaseModel):
    image_id: str
    x: float
    y: float
    radius: float
    label: str
    s3_key: str
    captured_at: int
    geohash: str
    image_crop_id: str
    point_id: Optional[str] = None


def save_chips_from_image(
    line: str,
    output_dir: str,
    fixed_radius: Optional[int] = None,
    format: str = "png",
    keep_point_category_ids: Optional[List[str]] = None,
) -> None:
    image = json.loads(line)
    carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")
    client = S3CacheProxyClient(s3_cache_proxy_host=carbon_cache_host, timeout=30)

    s3_upload_dir = None
    s3_client = None

    s3_key = image["uri"][5:]

    if output_dir.startswith("s3://"):
        s3_upload_dir = output_dir[5:]
        output_dir = f"/data/tmp/{image['image_id']}"

        s3_client = boto3.client("s3")

    if len(image["points"]) == 0:
        return

    point = image["points"][0]

    bucket = s3_key.split("/")[0]
    key = s3_key[len(bucket) + 1 :]

    img = client.get_image(bucket=bucket, key=key)

    if img.size[0] == 0 or img.size[1] == 0:
        LOG.info(f"Failed to load image: {s3_key} - using cache proxy")

    if keep_point_category_ids is None:
        keep_point_category_ids = []

    for point in image["points"]:
        folder = f"{point['id'][-3:]}" if s3_upload_dir is None else ""
        if point["point_category_id"] in keep_point_category_ids and not os.path.exists(
            os.path.join(output_dir, folder, f"{point['id']}.{format}")
        ):
            os.makedirs(os.path.join(output_dir, folder), exist_ok=True)

            radius = int(fixed_radius if fixed_radius else point["radius"])
            x = point["x"]
            y = point["y"]

            if radius > 0:
                if img.size[0] == 0 or img.size[1] == 0:
                    chip = client.get_image_subset(bucket=bucket, key=key, x=x, y=y, radius=radius, fill=True)
                else:
                    chip = img.crop((x - radius, y - radius, x + radius, y + radius))
                try:
                    chip.save(os.path.join(output_dir, folder, f"{point['id']}.{format}"))
                except Exception as e:
                    LOG.info(f"Failed to load point: {s3_key}, {radius}, {x:.2f}, {y:.2f}, error: {e}")

    if s3_client is not None and s3_upload_dir is not None:
        bucket, key = client.split_uri(s3_upload_dir)
        for point in image["points"]:
            s3_client.upload_file(
                Filename=os.path.join(output_dir, f"{point['id']}.{format}"),
                Bucket=bucket,
                Key=os.path.join(key, f"{point['id']}.{format}"),
            )

        shutil.rmtree(output_dir)


def crop_and_save(dataset_path: str, output_dir: str, fixed_radius: Optional[int] = None, format: str = "png") -> None:
    if output_dir.startswith("s3://"):
        LOG.info(f"Uploading to {output_dir}")
    else:
        LOG.info(f"Saving to {output_dir}")

    client = VeselkaClient()

    point_categories = client.get_point_categories()
    crop_point_category_id = [pc["id"] for pc in point_categories if pc["name"] == "crop"][0]

    keep_point_category_ids = [crop_point_category_id]

    with open(dataset_path, "r") as f:
        futures = []
        with multiprocessing.Pool(32, maxtasksperchild=100) as pool:
            for i, line in enumerate(f):
                futures.append(
                    pool.apply_async(
                        save_chips_from_image, args=(line, output_dir, fixed_radius, format, keep_point_category_ids)
                    )
                )

            for future in tqdm.tqdm(futures):
                future.get()


@retry(
    wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
    stop=stop_after_delay(30 * 60),
    before_sleep=before_sleep_log(LOG, logging.INFO),
)
def get_image_from_cache(datapoint: Datapoint) -> Image.Image:

    key = datapoint.s3_key
    x = int(datapoint.x - datapoint.radius)
    y = int(datapoint.y - datapoint.radius)
    width = int(2 * datapoint.radius)
    height = int(2 * datapoint.radius)

    if "bud" in key:
        if x + width > 3000:
            width = 3000 - x
        if y + height > 4096:
            height = 4096 - y
    elif "slayer" in key or "reaper" in key:
        if x + width > 4096:
            width = 4096 - x
        if y + height > 3000:
            height = 3000 - y
    else:
        raise ValueError(f"Unknown key: {key}")

    if x < 0:
        width += x
        x = 0
    if y < 0:
        height += y
        y = 0

    carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")
    response = requests.get(f"http://{carbon_cache_host}/{key}?x={x}&y={y}&width={width}&height={height}", timeout=30)
    assert response.ok, f"Failed to retrieve item ({key}) from cache: {response.text}"
    image = Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB")

    image_width, image_height = image.size

    if image_width != width or image_height != height:
        if image_width != width:
            LOG.info(f"Returned image does not have expected width: {image_width} != {width}. Key: {key}. Retrying.")
        elif image_height != height:
            LOG.info(f"Returned image does not have expected height: {image_height} != {height}. Key: {key}. Retrying.")

        response = requests.get(
            f"http://{carbon_cache_host}/{key}?x={x}&y={y}&width={width}&height={height}&break=1", timeout=30
        )
        assert response.ok, f"Failed to retrieve item ({key}) from cache: {response.text}"
        image = Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB")

        image_width, image_height = image.size

    assert (
        image_width == width
    ), f"Returned image does not have expected width: {image_width} != {width}. Key: {key}. Retry failed."
    assert (
        image_height == height
    ), f"Returned image does not have expected height: {image_height} != {height}. Key: {key}. Retry failed."

    return image


class CropDataset(torch.utils.data.Dataset[dataset_types.Datapoint]):  # noqa type-arg
    def __init__(
        self,
        data: Dict[str, str],
        labels: List[Dict[str, Any]],
        transform_fn: Callable[[Any], torch.Tensor] = default_evaluation_transform_fn,
        use_s3_cache_proxy: bool = True,
        local_subset: bool = False,
        local_dataset_path: Optional[str] = None,
        geo_date_sampling: bool = False,
    ) -> None:
        self._data = data
        self._labels = labels
        self._transform_fn = transform_fn
        self._use_s3_cache_proxy = use_s3_cache_proxy
        self._local_subset = local_subset
        self._local_path = local_dataset_path
        self._geo_date_sampling = geo_date_sampling

        self._s3_cache_proxy_client: Optional[S3CacheProxyClient] = None

        self._class_names = list(
            set(
                [
                    label["label"] if not self._geo_date_sampling else f"{label['label']}_{label['geo-date']}"
                    for label in tqdm.tqdm(self._labels)
                ]
            )
        )
        self._name2indices: Dict[str, List[int]] = {}
        if self._geo_date_sampling:
            self._name2indices = {}
            for i, label in tqdm.tqdm(enumerate(self._labels)):
                if f"{label['label']}_{label['geo-date']}" not in self._name2indices:
                    self._name2indices[f"{label['label']}_{label['geo-date']}"] = []
                self._name2indices[f"{label['label']}_{label['geo-date']}"].append(i)
        else:
            self._name2indices = {
                name: [i for i, label in enumerate(self._labels) if label["label"] == name]
                for name in self._class_names
            }

        self._class_names = [name for name in tqdm.tqdm(self._name2indices.keys()) if len(self._name2indices[name]) > 0]

    def _get_s3_cache_proxy_client(self) -> S3CacheProxyClient:
        if self._use_s3_cache_proxy:
            s3_cache_proxy_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")
        else:
            s3_cache_proxy_host = None

        return S3CacheProxyClient(s3_cache_proxy_host=s3_cache_proxy_host, timeout=30)

    @property
    def class_names(self) -> List[str]:
        return self._class_names

    @property
    def name2indices(self) -> Dict[str, List[int]]:
        return self._name2indices

    def __getitem__(self, item: Union[int, Tuple[int, dataset_types.DatapointRole]]) -> dataset_types.Datapoint:

        if self._s3_cache_proxy_client is None:
            self._s3_cache_proxy_client = self._get_s3_cache_proxy_client()

        index: int
        role: dataset_types.DatapointRole

        if isinstance(item, int):
            index = item
            role = dataset_types.DatapointRole.QUERY
        else:
            index, role = item

        x = self._labels[index]["x"]
        y = self._labels[index]["y"]
        radius = self._labels[index]["radius"]
        label = (
            f"{self._labels[index]['label']}_{self._labels[index]['geo-date']}"
            if self._geo_date_sampling
            else self._labels[index]["label"]
        )
        image_id = self._labels[index]["image_id"]
        point_id = self._labels[index]["point_id"]
        uri = self._data[image_id]
        identifier = index

        if self._local_path is not None:
            image_path = f"{self._local_path}/{point_id[-3:]}/{point_id}.jpg"
            try:
                pil_image = pil_loader(image_path)

                tensor_image = self._transform_fn(pil_image)
                datapoint = dataset_types.Datapoint(tensor_image, image_path, label, role)
                return datapoint

            except (FileNotFoundError, PIL.UnidentifiedImageError):
                LOG.info(f"Image {image_path} not found locally or corrupted - loading from S3")

        if uri.startswith("s3"):
            split_s3_path = uri.split("/", maxsplit=3)
            assert len(split_s3_path) == 4  # Ex: ['s3:', '', 'maka-pono', 'media/test.png']

            assert self._s3_cache_proxy_client is not None
            if self._local_subset:
                pil_full_image = self._s3_cache_proxy_client.get_image(split_s3_path[2], split_s3_path[3])
                pil_image = pil_full_image.crop((x - radius, y - radius, x + radius, y + radius))
            else:
                pil_image = self._s3_cache_proxy_client.get_image_subset(
                    split_s3_path[2], split_s3_path[3], x, y, radius
                )

        else:
            pil_image = pil_loader(uri)

        tensor_image = self._transform_fn(pil_image)

        datapoint = dataset_types.Datapoint(tensor_image, identifier, label, role)

        return datapoint

    def __len__(self) -> int:
        return len(self._labels)


class GeoDateSampler(fewshot.samplers.Sampler):
    # Won't work with tiny datasets that have lots of geodates without enough samples
    def __init__(
        self,
        dataset: CropDataset,
        num_classes: int,
        num_support_samples: int,
        num_query_samples: int,
        num_episodes: int,
        seed: int = 1000,
    ) -> None:
        self._dataset = dataset
        self._num_classes = num_classes
        assert self._num_classes <= len(self._dataset.class_names)

        self._num_support_samples = num_support_samples
        self._num_query_samples = num_query_samples
        self._num_episodes = num_episodes

        self.crop2geohashes: Dict[str, List[str]] = {}
        for crop_geodate in self._dataset.class_names:
            if len(self._dataset._name2indices[crop_geodate]) >= self._num_support_samples + self._num_query_samples:
                crop, geodate = crop_geodate.split("_")[0], crop_geodate.split("_")[1]
                if crop not in self.crop2geohashes:
                    self.crop2geohashes[crop] = []
                self.crop2geohashes[crop].append(geodate)

        self._rng = np.random.RandomState(seed)

    def __iter__(self) -> Iterator[List[Tuple[int, dataset_types.DatapointRole]]]:
        for _ in range(self._num_episodes):
            crops = self._rng.choice(list(self.crop2geohashes.keys()), self._num_classes, replace=False)

            support_roles = [dataset_types.DatapointRole.SUPPORT] * self._num_support_samples
            query_roles = [dataset_types.DatapointRole.QUERY] * self._num_query_samples

            samples_list = []
            roles_list = []
            for crop in crops:
                geodate = self._rng.choice(self.crop2geohashes[crop], 1)[0]

                num_samples = self._num_support_samples + self._num_query_samples

                class_name = crop + "_" + geodate
                samples = []
                indices = self._dataset.name2indices[class_name]
                for sample in self._rng.choice(indices, min(len(indices), num_samples), replace=False):
                    samples.append(int(sample))

                samples_list.extend(samples)
                roles_list.extend(support_roles + query_roles)

            episode = [(a, b) for a, b in zip(samples_list, roles_list)]

            yield episode


class DatasetWrapper:
    def __init__(self, dataset: CropDataset, num_samples: Optional[int] = None) -> None:
        self._dataset = dataset
        self._num_samples = num_samples

    def __getitem__(self, index: int) -> Any:
        index = int(torch.randint(0, len(self._dataset), (1,))[0].item())
        return self._dataset[index].tensor.cuda()

    def __len__(self) -> int:
        return len(self._dataset) if not self._num_samples else self._num_samples


def convert_trt(
    model: fewshot.models.BaseModel,
    calib_dataset: CropDataset,
    validation_dataset: CropDataset,
    config: Config,
    trt_path: str,
    num_samples: int = 1000,
    max_batch_size: int = 3,
    calibration_batch_size: int = 16,
) -> None:
    wrapped_dataset = DatasetWrapper(calib_dataset, num_samples)

    test_input = [calib_dataset[0].tensor.unsqueeze(0).repeat(max_batch_size, 1, 1, 1).cuda()]

    model.eval().cuda()

    encoder = model.encoder

    pyt_embeddings = []
    pyt_timing = []
    for i in range(min(20, len(validation_dataset))):
        start = time.time()
        pyt_embeddings.append(encoder(validation_dataset[i].tensor.unsqueeze(0).cuda()))
        end = time.time()
        if i > 2:  # warmup
            pyt_timing.append(end - start)

    stacked_pyt_embeddings = torch.cat(pyt_embeddings, dim=0)
    stacked_pyt_embeddings /= stacked_pyt_embeddings.norm(dim=1, keepdim=True)

    with torch.no_grad():
        trt_encoder = torch2trt(
            encoder,
            test_input,
            fp16_mode=False,
            int8_mode=True,
            int8_calib_dataset=wrapped_dataset,
            int8_calib_batch_size=calibration_batch_size,
            max_batch_size=max_batch_size,
            max_workspace_size=1 << 22,
            log_level=trt.Logger.INFO,
            strict_type_constraints=True,
            use_implicit_batch_dimension=False,
        )

    trt_embeddings = []
    trt_timing = []
    for i in range(min(20, len(validation_dataset))):
        start = time.time()
        emb = trt_encoder(validation_dataset[i].tensor.unsqueeze(0).cuda())
        trt_embeddings.append(emb[0, :].unsqueeze(0))
        end = time.time()
        if i > 2:  # warmup
            trt_timing.append(end - start)
    stacked_trt_embeddings = torch.cat(trt_embeddings, dim=0)
    stacked_trt_embeddings /= stacked_trt_embeddings.norm(dim=1, keepdim=True)

    cos_sims = stacked_pyt_embeddings @ stacked_trt_embeddings.T
    embedding_error = torch.diag(cos_sims, 0)

    print("Embedding error (Cosine similarity): ")
    print(f"\tmean: {embedding_error.mean():.5f}")
    print(f"\tmedian: {torch.median(embedding_error):.5f}")
    print(f"\tmin: {embedding_error.min():.5f}")
    print(f"\tmax: {embedding_error.max():.5f}")
    print(f"\t99%tile: {torch.quantile(embedding_error, 0.99):.5f}")

    print(f"Average pre-cvt time: {1000 * sum(pyt_timing) / len(pyt_timing):.5f} ms")
    print(f"Average post-cvt time: {1000 * sum(trt_timing) / len(trt_timing):.5f} ms")

    metadata = ModelMetadata(
        input_dtype=torch.float32,
        input_size=test_input[0].shape[2:],
        supports_half=False,
        trained_embeddings=True,
        backbone_architecture=config.backbone,
        model_class=config.model_type,
        max_batch_size=max_batch_size,
        means=IMAGENET_MEAN,
        stds=IMAGENET_STD,
    )

    save_tensorrt_model(trt_encoder, metadata, trt_path)

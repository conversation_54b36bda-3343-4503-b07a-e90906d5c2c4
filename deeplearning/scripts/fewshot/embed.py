import argparse
import json
import logging
import os
import uuid
from typing import List

import torch
from PIL import Image

import fewshot
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
)
from deeplearning.scripts.embeddings.utils import get_local_embedding_filepath, upload_embedding_hdf5
from deeplearning.scripts.fewshot.utils import Datapoint, get_image_from_cache
from deeplearning.scripts.utils.utils import add_job_creator_arguments
from deeplearning.utils.uuid import get_globally_unique_id
from lib.common.s3_cache_proxy.client import S3CacheProxyClient
from lib.common.veselka.client import VeselkaClient

logging.basicConfig()
LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)

WORKING_DIR = "temp-workspace"
TRAIN_CROP_RATIO = 0.7  # Percentage of crop classes used for training


class LoaderFn:
    def __init__(self, datapoints: List[Datapoint]):
        self._datapoints = datapoints

    def __call__(self, index: int) -> Image.Image:

        datapoint = self._datapoints[index]
        image = get_image_from_cache(datapoint)
        return image


def get_datapoints(filepath: str) -> List[Datapoint]:
    with open(filepath) as f:
        data = json.load(f)

    images = data["images"]
    annotations = data["annotations"]

    image_id2image = {image["id"]: image for image in images}

    datapoints: List[Datapoint] = []
    for annotation in annotations:
        label = annotation["label"]

        if label == "driptape":
            continue

        image_id = annotation["image_id"]

        image = image_id2image[image_id]
        s3_key = image["uri"][5:]

        if annotation["radius"] != 0:
            datapoint = Datapoint(
                image_id=image_id,
                x=annotation["x"],
                y=annotation["y"],
                radius=annotation["radius"],
                label=label,
                s3_key=s3_key,
                captured_at=image["captured_at"],
                geohash=image["geohash"],
                image_crop_id=image["crop_id"],
                point_id=get_globally_unique_id(annotation),
            )

            datapoints.append(datapoint)

    return datapoints


def get_datapoints_v2(
    filepath: str, metadata_filepath: str, train_crop_ratio: float = TRAIN_CROP_RATIO
) -> List[Datapoint]:

    with open(filepath) as f:
        data = [json.loads(line) for line in f]
    with open(metadata_filepath) as f:
        metadata = json.load(f)

    splits = metadata["dataset_options"]["splits"]
    client = VeselkaClient(use_carbon_cache=True)
    keeps_crops = set()
    if not splits:
        crops = client.get_pipeline(metadata["dataset_options"]["pipeline_id"]).data_source_crop_ids
        keeps_crops = set(crops[round(len(crops) * train_crop_ratio) :])
    pc_metadata = client.get_point_categories()
    point_category_to_name = {pc["id"]: pc["name"].upper() for pc in pc_metadata}

    datapoints: List[Datapoint] = []
    for image in data:
        if splits or image["crop_id"] in keeps_crops:
            for point in image["points"]:
                if point["radius"] == 0:
                    continue
                datapoint = Datapoint(
                    image_id=image["image_id"],
                    x=point["x"],
                    y=point["y"],
                    radius=point["radius"],
                    label=point_category_to_name[point["point_category_id"]],
                    s3_key=image["uri"][5:],
                    captured_at=image["captured_at"],
                    geohash=image["geohash"],
                    image_crop_id=image["crop_id"],
                    point_id=point["id"],
                )

                datapoints.append(datapoint)

    return datapoints


def main() -> None:

    parser = argparse.ArgumentParser()

    parser.add_argument("--model-id", type=str, required=True)
    parser.add_argument("--dataset-id", type=str, required=True)
    parser.add_argument("--fast-run", action="store_true", default=False)
    parser.add_argument("--dataset-version", type=int, default=1, help="Dataset version to use, 1 or 2")
    add_job_creator_arguments(parser)

    parser.set_defaults(fast_run=False)

    args = parser.parse_args()

    version_id = str(uuid.uuid4())
    output_path = get_local_embedding_filepath(args.dataset_id, args.model_id, version_id=version_id)

    if os.path.exists(output_path):
        raise Exception(f"Output path {output_path} already exists")

    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    s3_cache_proxy_host = os.getenv("S3_CACHE_PROXY_HOST")
    s3_cache_proxy_client = S3CacheProxyClient(s3_cache_proxy_host)
    veselka_client = VeselkaClient(use_carbon_cache=True, data_dir=f"{CARBON_DATA_DIR}/deeplearning")

    # Download dataset test set file
    if args.dataset_version == 2:
        dataset_v2_info = veselka_client.get_dataset_v2(args.dataset_id)
        assert dataset_v2_info is not None, "Dataset not found"
        LOG.info("Downloading dataset v2")
        veselka_client.download_dataset_v2(dataset_v2_info)
    else:
        dataset_info = veselka_client.get_dataset(args.dataset_id)
        LOG.info("Downloading dataset")
        veselka_client.download_dataset(dataset_info)
    test_set_filepath = f"{CARBON_DATA_DIR}/deeplearning/datasets/{args.dataset_id}/test.json"

    LOG.info("Finished downloading dataset")

    if args.dataset_version == 2 and dataset_v2_info is not None:
        if dataset_v2_info.test is not None:
            test_set_filepath = f"{CARBON_DATA_DIR}/deeplearning/datasets/{args.dataset_id}/test.jsonl"
        else:
            test_set_filepath = f"{CARBON_DATA_DIR}/deeplearning/datasets/{args.dataset_id}/datapoints.jsonl"

    # Assemble datapoints from the dataset json file
    if args.dataset_version == 2:
        metadata_path = f"{CARBON_DATA_DIR}/deeplearning/datasets/{args.dataset_id}/metadata.json"
        datapoints = get_datapoints_v2(test_set_filepath, metadata_path)
    else:
        datapoints = get_datapoints(test_set_filepath)

    if args.fast_run:
        datapoints = datapoints[:1000]

    # Download model weights file
    key = f"models/{args.model_id}/weights/best.pt"
    model_filepath = f"{CARBON_DATA_DIR}/deeplearning/{key}"

    if not os.path.exists(model_filepath):
        s3_cache_proxy_client.download(bucket="maka-pono", key=key, filepath=model_filepath)

    # Instantiate model and load weights
    model = fewshot.models.ProtoNets(encoder=fewshot.encoders.ResNet(architecture="resnet50", pretrained=False))
    model.load_weights(model_filepath)

    data = [index for index, _ in enumerate(datapoints)]
    labels = [datapoint.label for datapoint in datapoints]
    load_fn = LoaderFn(datapoints)

    # Construct the few-shot dataset
    dataset = fewshot.Dataset(data=data, labels=labels, load_fn=load_fn)

    # Embed the datapoints
    fewshot.embed(model._encoder, dataset, data_dir=WORKING_DIR, num_gpus=torch.cuda.device_count())

    # convert embeddings to hdf5 file
    embeddings_dataset_metadata = EmbeddingDatasetMetadata(
        model_id=args.model_id,
        embedding_size=model._encoder.embedding_size,
        dataset_id=args.dataset_id,
        version_id=version_id,
    )

    embedding_dataset = EmbeddingDataset(dataset_filepath=output_path, metadata=embeddings_dataset_metadata)

    temp_filepath = f"{WORKING_DIR}/embeddings/test-embeddings.jsonl"
    with open(temp_filepath, "r") as f:
        for line in f.readlines():
            embedding_data = json.loads(line)

            index = embedding_data["identifier"]
            embedding = torch.tensor(embedding_data["embedding"])
            datapoint = datapoints[index]

            embedding_datapoint_metadata = EmbeddingDatapointMetadata(
                image_id=datapoint.image_id,
                image_url=datapoint.s3_key,
                captured_at=datapoint.captured_at,
                geohash=datapoint.geohash,
                point_category_id=datapoint.label,
                image_crop_id=datapoint.image_crop_id,
                x=datapoint.x,
                y=datapoint.y,
                radius=datapoint.radius,
                point_id=datapoint.point_id,
            )

            embedding_datapoint = EmbeddingDatapoint(embedding=embedding, metadata=embedding_datapoint_metadata)
            embedding_dataset.append(embedding_datapoint)

    if not args.fast_run:
        upload_embedding_hdf5(output_path, args.dataset_id, args.model_id, version_id=version_id)

    LOG.info(f"Finished running embed for model. Saved at {output_path}")

    if os.path.exists(temp_filepath):
        os.remove(temp_filepath)


if __name__ == "__main__":
    main()

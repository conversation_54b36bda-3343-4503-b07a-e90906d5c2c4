import argparse
import datetime
import logging

import torch
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.furrows.config import FurrowsConfig
from deeplearning.furrows.datasets import get_furrows_datasets
from deeplearning.furrows.models.rgb import FurrowsRGB
from deeplearning.furrows.trainer import FurrowsTrainer
from deeplearning.scripts.furrows.utils import DATA_CACHE_LOCATION, FURROW_CATEGORIES, s3_path_to_local_path
from deeplearning.scripts.utils.utils import add_common_arguments, generate_model_id, run_debuggable
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.trainer import Environment
from lib.common.veselka.client import DatasetV2, VeselkaClient

LOG = logging.getLogger(__name__)


@record
def main() -> None:
    parser = argparse.ArgumentParser()

    add_common_arguments(parser)
    args = parser.parse_args()

    model_id = args.job_id
    if args.job_id is None:
        model_id = generate_model_id()

    if not args.pipeline_id:
        args.pipeline_id = "71f9063f-ff15-4704-99d5-0d0da40d3076"  # Default to furrows pipeline id

    dl_config_dict = {
        "num_epochs": 60 if not args.fast_run else 1,
        "fast_run": args.fast_run,
        **args.dl_config,
    }
    if args.fast_run:
        dl_config_dict["num_samples"] = 100
        dl_config_dict["wandb_project"] = "furrows-fast-run"
    dl_config = FurrowsConfig.from_dict(dl_config_dict)

    trainer = FurrowsTrainer()

    dataset_info = None
    if torch.distributed.get_rank() == 0:
        veselka_client = VeselkaClient(DATA_CACHE_LOCATION)
        dataset_id = args.dataset_id
        if dataset_id is None:
            dataset_id = veselka_client.create_furrows_dataset(fast_run=args.fast_run)
        dataset_info = DatasetV2.model_validate(veselka_client.get_dataset(dataset_id))
        veselka_client.download_dataset_v2(dataset_info)
        LOG.info(f"Downloaded dataset: {dataset_id}")

    dataset_info = broadcast_object(dataset_info)
    assert dataset_info is not None
    assert dataset_info.validation is not None
    assert dataset_info.test is not None
    assert dataset_info.train is not None

    environment = Environment.DEVELOPMENT
    if dl_config.fast_run:
        environment = Environment.DEVELOPMENT
    elif args.preview:
        environment = Environment.PREVIEW
    elif args.production:
        environment = Environment.PRODUCTION

    if args.description is None:
        description = f"(train) Development run {datetime.datetime.now()}"
    else:
        description = args.description

    datasets = get_furrows_datasets(
        s3_path_to_local_path(dataset_info.train),
        s3_path_to_local_path(dataset_info.validation),
        s3_path_to_local_path(dataset_info.test),
        dl_config,
        FURROW_CATEGORIES,
    )
    model = FurrowsRGB(len(FURROW_CATEGORIES))

    trainer.train(
        dl_config,
        datasets,
        model,
        pipeline_id=args.pipeline_id,
        dataset_id=dataset_info.id,
        checkpoint_dir=f"/data/deeplearning/models/{model_id}",
        resume_from=args.resume_from,
        description=description,
        deploy=environment == Environment.PRODUCTION,
        environment=environment,
    )


if __name__ == "__main__":
    run_debuggable(main)

import argparse
import logging
import os
import platform
import sys

LOG = logging.getLogger(__name__)


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    if platform.machine() != "aarch64":
        parser.add_argument("--tensorrt-version", choices=["*******", "10.0.1"], required=True)
    args, _ = parser.parse_known_args()

    if platform.machine() != "aarch64":
        sys.path = [x for x in sys.path if not x.startswith("/opt/TensorRT")]
        sys.path.insert(0, f"/opt/TensorRT-{args.tensorrt_version}/python/site-packages")

    # only load deeplearning code after updating sys.path
    from deeplearning.furrows.trt_convert import TrtConvert
    from deeplearning.scripts.utils.utils import TrainingInfo
    from deeplearning.utils.trainer import (
        S3_BUCKET,
        compute_md5sum,
        get_compute_capability,
        get_tensorrt_version,
        upload_directory,
        get_tensorrt_file_name,
    )
    from deeplearning.furrows.datasets import get_furrows_datasets
    from lib.common.veselka.client import VeselkaClient, DatasetV2
    from deeplearning.scripts.furrows.utils import DATA_CACHE_LOCATION, FURROW_CATEGORIES, s3_path_to_local_path

    training_info = TrainingInfo(args.model_id)
    trt_file_name = get_tensorrt_file_name(training_info.furrows_config.convert_int8)
    trt_file_path = os.path.join(training_info.data_dir, trt_file_name)

    veselka_client = VeselkaClient(DATA_CACHE_LOCATION)
    dataset_id = training_info.dataset_id
    if dataset_id is None:
        dataset_id = veselka_client.create_furrows_dataset()
    dataset_info = DatasetV2.model_validate(veselka_client.get_dataset(dataset_id))
    veselka_client.download_dataset_v2(dataset_info)
    LOG.info(f"Downloaded dataset: {dataset_id}")

    assert dataset_info.train is not None
    assert dataset_info.validation is not None
    assert dataset_info.test is not None
    datasets = get_furrows_datasets(
        s3_path_to_local_path(dataset_info.train),
        s3_path_to_local_path(dataset_info.validation),
        s3_path_to_local_path(dataset_info.test),
        training_info.furrows_config,
        FURROW_CATEGORIES,
    )

    trt_converter = TrtConvert(
        calibration_dataset=datasets.get_calibration(), validation_dataset=datasets.get_validation()
    )
    trt_converter.load_model(training_info.best_model_weights, training_info.furrows_config)
    trt_converter.convert(
        max_batch_size=training_info.furrows_config.evaluation_batch_size,
        save_to=trt_file_path,
        int8=training_info.furrows_config.convert_int8,
        fp16=training_info.furrows_config.convert_fp16,
        calibration_batch_size=16,
        calibration_only=False,
        error_metrics=True,
    )

    upload_directory(training_info.data_dir)

    veselka_client = VeselkaClient()
    veselka_client.post_model_artifact(
        model_id=args.model_id,
        tensorrt_version=get_tensorrt_version(),
        compute_capability=get_compute_capability(),
        url=f"s3://{S3_BUCKET}/models/{args.model_id}/{trt_file_name}",
        checksum=compute_md5sum(trt_file_path),
    )


if __name__ == "__main__":
    main()

import argparse
import json
import logging
import multiprocessing
import os
import uuid
from typing import Any, Dict

import boto3
import torch
import torchvision.transforms.functional as TF
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

from cv.deepweed.deepweed_python import DeepweedModel
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
)
from deeplearning.scripts.embeddings.evaluate import run_evaluation
from deeplearning.scripts.embeddings.utils import get_embedding_hdf5_s3_path, upload_embedding_hdf5
from deeplearning.scripts.utils.utils import TrainingInfo
from deeplearning.utils.dataset import DEFAULT_GEOHASH, pil_loader
from deeplearning.utils.download_utils import download_records
from lib.common.model.cpp.model_python import AtomicModel

LOG = logging.getLogger(__name__)


def compute_embeddings(
    trt_path: str,
    captcha_dir: str,
    plant_captcha_metadata: Dict[str, Any],
    s3_path: str,
    embedding_dataset: EmbeddingDataset,
) -> None:
    labels = []
    predictions = []

    deepweed_model = DeepweedModel(AtomicModel(trt_path, 0))
    for row_dir in os.listdir(captcha_dir):
        if row_dir.startswith("row"):
            for trajectory in os.listdir(os.path.join(captcha_dir, row_dir)):
                trajectory_path = os.path.join(captcha_dir, row_dir, trajectory)
                if not os.path.isdir(trajectory_path):
                    continue

                trajectory_embeddings = []
                metadata = None
                image_files = []
                image_coordinates = []
                for image_file in os.listdir(trajectory_path):
                    if not image_file.endswith(".png"):
                        continue

                    metadata_path = os.path.join(trajectory_path, image_file.replace(".png", ".meta.json"))
                    if not os.path.exists(metadata_path):
                        continue

                    with open(metadata_path) as f:
                        metadata = json.load(f)

                    x = metadata["xPx"]
                    y = metadata["yPx"]
                    image_coordinates.append((x, y))

                    full_image_path = os.path.join(trajectory_path, image_file)
                    image = pil_loader(full_image_path)
                    image_tensor = (TF.to_tensor(image) * 255).to(torch.uint8)

                    embeddings = [
                        embedding_prediction.embedding
                        for embedding_prediction in deepweed_model.get_embedding(image_tensor, x, y)
                    ]
                    if len(embeddings) == 0:
                        continue

                    embedding = embeddings[0][0]
                    trajectory_embeddings.append(embedding)
                    image_files.append(image_file)

                assert metadata is not None

                if not os.path.exists(os.path.join(captcha_dir, "results", metadata["id"])):
                    continue

                initial_label = metadata["initialLabel"]

                with open(os.path.join(captcha_dir, "results", metadata["id"]), "r") as f:
                    classification = f.read().strip()

                if classification == "IGNORE":
                    continue

                if initial_label != "IGNORE":
                    labels.append(classification)
                    predictions.append(initial_label)

                trajectory_embeddings_tensor = trajectory_embeddings[1]  # Use center image for now
                x, y = image_coordinates[1]

                embedding_datapoint_metadata = EmbeddingDatapointMetadata(
                    point_category_id=classification,
                    geohash=DEFAULT_GEOHASH,
                    captured_at=plant_captcha_metadata["startTimeMs"],
                    x=x,
                    y=y,
                    image_crop_id=plant_captcha_metadata["cropId"],
                    image_id=metadata["id"],
                    image_url=os.path.join(s3_path, row_dir, trajectory, image_files[1]),
                )

                embedding_datapoint = EmbeddingDatapoint(
                    trajectory_embeddings_tensor.cpu(), embedding_datapoint_metadata
                )
                embedding_dataset.append(embedding_datapoint)

    classes = ["CROP", "WEED"]
    precision, recall, _, _ = precision_recall_fscore_support(labels, predictions, labels=classes)
    avg_acc = accuracy_score(labels, predictions)
    LOG.info(f"Average Accuracy: {avg_acc:.2f}")
    for i in range(len(classes)):
        class_prec, class_rec = precision[i], recall[i]

        LOG.info(f"{classes[i].lower()} Precision: {class_prec:.2f}")
        LOG.info(f"{classes[i].lower()} Recall: {class_rec:.2f}")
        LOG.info("")


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")
    parser = argparse.ArgumentParser("Generate embeddings from plant captcha")
    parser.add_argument("--model-id", type=str, required=True)
    parser.add_argument("--plant-captcha-s3-path", type=str, required=True)
    parser.add_argument("--evaluate", action="store_true", default=False)
    parser.add_argument("--fast-run", action="store_true")
    args = parser.parse_args()

    training_info = TrainingInfo(args.model_id)
    assert training_info.trt_path is not None

    bucket, plant_captcha_s3_path = args.plant_captcha_s3_path[len("s3://") :].split("/", maxsplit=1)

    client = boto3.client("s3")
    result = client.list_objects(Bucket=bucket, Prefix=os.path.join(plant_captcha_s3_path, ""), Delimiter="/")
    common_prefixes = result.get("CommonPrefixes")
    if common_prefixes is None:
        return

    plant_captcha_s3_path = common_prefixes[0]["Prefix"].rstrip("/")
    version_id = str(uuid.uuid4())

    download_records(
        os.path.basename(plant_captcha_s3_path),
        skip_existing_files=True,
        s3_directory=os.path.dirname(plant_captcha_s3_path),
        bucket=bucket,
    )

    plant_captcha_dataset_id = os.path.basename(os.path.dirname(plant_captcha_s3_path))
    embedding_metadata = EmbeddingDatasetMetadata(
        model_id=args.model_id, dataset_id=plant_captcha_dataset_id, embedding_size=1024, version_id=version_id
    )

    embedding_dataset_path = f"{CARBON_DATA_DIR}/deeplearning/{get_embedding_hdf5_s3_path(embedding_metadata.dataset_id, embedding_metadata.model_id, version_id=version_id)}"
    os.makedirs(os.path.dirname(embedding_dataset_path), exist_ok=True)
    if os.path.exists(embedding_dataset_path):
        os.remove(embedding_dataset_path)
    embedding_dataset = EmbeddingDataset(embedding_dataset_path, embedding_metadata)

    with open(os.path.join(CARBON_DATA_DIR, "deeplearning", plant_captcha_s3_path, "plant_captcha.json")) as f:
        plant_captcha_metadata = json.load(f)

    captcha_dir = os.path.join(CARBON_DATA_DIR, "deeplearning", plant_captcha_s3_path)

    process = multiprocessing.Process(
        target=compute_embeddings,
        args=(
            training_info.trt_path,
            captcha_dir,
            plant_captcha_metadata,
            args.plant_captcha_s3_path,
            embedding_dataset,
        ),
    )
    process.start()
    process.join()

    if not args.fast_run:
        upload_embedding_hdf5(
            embedding_dataset_path, dataset_id=plant_captcha_dataset_id, model_id=args.model_id, version_id=version_id
        )
    LOG.info(
        f"Plant Captcha embeddings uploaded. dataset_id: {plant_captcha_dataset_id}, model_id: {args.model_id}, length: {len(embedding_dataset)}. Saved at {embedding_dataset_path}"
    )

    if args.evaluate:
        run_evaluation(
            dataset_id=plant_captcha_dataset_id,
            model_id=args.model_id,
            min_examples_per_class=10,
            num_query_samples=8,
            num_support_samples=5,
            weed_classes=["weed"],
            version_id=version_id,
        )


if __name__ == "__main__":
    main()

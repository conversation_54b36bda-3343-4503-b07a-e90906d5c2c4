import io
import os
import numpy as np
import requests
import cv2

from PIL import Image

# === CONFIGURATION ===
INPUT_FILE = "deeplearning/chips_to_download.txt"  # replace with your filename
MIN_CHIP_SIZE = 600
OUTPUT_DIR = "/data/chips_to_download12"
IMAGE_SERVICE_URL = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")

# === ENSURE OUTPUT DIR EXISTS ===
os.makedirs(OUTPUT_DIR, exist_ok=True)

# === MAIN SCRIPT ===

count = 0
success = 0
failure = 0
with open(INPUT_FILE, "r") as f:
    for line_number, line in enumerate(f, start=1):
        count += 1
        line = line.strip()
        if not line or line.startswith("#"):
            continue

        try:
            parts = [p.strip() for p in line.split("|")]
            if len(parts) != 4:
                print(f"[Line {line_number}] Skipping malformed line: {line}")
                continue

            url, radius_str, x_str, y_str = parts
            key = url.split("s3://", 1)[1]
            radius = float(radius_str)
            x_center = float(x_str)
            y_center = float(y_str)

            width = height = int(2 * radius)
            if width < MIN_CHIP_SIZE:
                width = height = MIN_CHIP_SIZE

            x = int(x_center - width / 2)
            y = int(y_center - height / 2)

            request_url = f"http://{IMAGE_SERVICE_URL}/{key}?x={x}&y={y}&width={width}&height={height}"

            response = requests.get(request_url, timeout=30)
            # response.raise_for_status()
            assert response.ok

            filename = key.replace("/", "_")  # flat filename
            output_path = os.path.join(OUTPUT_DIR, f"{x}_{y}_{filename}")
            im = Image.open(io.BytesIO(response.content), formats=["png"])
            
            image = np.array(im.convert("RGB"))[:,:,::-1]
            cv2.imwrite(output_path, image)

            # with open(output_path, "wb") as out_file:
            #     out_file.write(response.content)

            print(f"[OK] Saved: {output_path}")
            success += 1

        except Exception as e:
            print(f"[Error] Line {line_number} failed: {e}")
            failure += 1
            
print(f"{count} {success} {failure}")
import numpy as np
import pytest

EPS = 1e-6


@pytest.mark.dl_unit_test
def test_bisecting_k_medoids_cosine() -> None:
    from deeplearning.clustering.bisecting_k_medoids import BisectingKMedoids

    bisecting_k_medoids = BisectingKMedoids(metric="cosine", n_clusters=3)
    items = np.array([[0.0, 1.0], [0.01, 0.99], [-1.0, 0.0], [-0.99, 0.001], [-0.6, 0.011], [-0.59, 0.01],])

    clusters = bisecting_k_medoids.fit(items)
    assert np.array_equal(clusters.labels_, np.array([0, 0, 2, 2, 1, 1]))


@pytest.mark.dl_unit_test
def test_bisecting_k_medoids_euclidean() -> None:
    from deeplearning.clustering.bisecting_k_medoids import BisectingKMedoids

    bisecting_k_medoids = BisectingKMedoids(metric="euclidean", n_clusters=3)
    items = np.array([[0.0, 1.0], [0.01, 0.99], [-1.0, 0.0], [-0.99, 0.001], [-0.6, 0.011], [-0.59, 0.01],])

    clusters = bisecting_k_medoids.fit(items)
    assert np.array_equal(clusters.labels_, np.array([1, 1, 2, 2, 3, 3]))
    assert (
        abs(
            clusters.inertia_
            - sum([(0.01 ** 2 + 0.01 ** 2) ** 0.5, (0.01 ** 2 + 0.001 ** 2) ** 0.5, (0.01 ** 2 + 0.001 ** 2) ** 0.5])
        )
        < EPS
    )


@pytest.mark.dl_unit_test
def test_bisecting_k_medoids_manhattan() -> None:
    from deeplearning.clustering.bisecting_k_medoids import BisectingKMedoids

    bisecting_k_medoids = BisectingKMedoids(metric="manhattan", n_clusters=3)
    items = np.array([[0.0, 1.0], [0.01, 0.99], [-1.0, 0.0], [-0.99, 0.001], [-0.6, 0.011], [-0.59, 0.01],])

    clusters = bisecting_k_medoids.fit(items)
    assert np.array_equal(clusters.labels_, np.array([1, 1, 2, 2, 3, 3]))
    assert abs(clusters.inertia_ - sum([0.02 + 0.011 + 0.011])) < EPS

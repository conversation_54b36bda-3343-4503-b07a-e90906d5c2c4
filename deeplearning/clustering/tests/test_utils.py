import pytest


@pytest.mark.dl_unit_test
def test_get_best_clustering() -> None:
    from deeplearning.clustering.utils import COMPARISON_SILHOUETTE, COMPARISON_SIMILARITY, get_best_clustering_config

    scores = {
        str((COMPARISON_SIMILARITY, "algo1")): [
            {"k": 5, "score": 0.1},
            {"k": 6, "score": 0.6},
            {"k": 7, "score": 0.4},
            {"k": 8, "score": 0.3},
        ],
        str((COMPARISON_SILHOUETTE, "algo1")): [
            {"k": 5, "score": 0.75},
            {"k": 6, "score": 0.49},
            {"k": 7, "score": 0.55},
            {"k": 8, "score": 0.56},
        ],
        str((COMPARISON_SIMILARITY, "algo2")): [
            {"k": 5, "score": 0.4},
            {"k": 6, "score": 0.3},
            {"k": 7, "score": 0.6},
            {"k": 8, "score": 0.3},
        ],
        str((COMPARISON_SILHOUETTE, "algo2")): [
            {"k": 5, "score": 0.75},
            {"k": 6, "score": 0.51},
            {"k": 7, "score": 0.45},
            {"k": 8, "score": 0.99},
        ],
    }

    best_clustering_configuration = get_best_clustering_config(scores)

    assert best_clustering_configuration == {
        COMPARISON_SIMILARITY: 0.4,
        COMPARISON_SILHOUETTE: 0.55,
        "k": 7,
        "algorithm": "algo1",
    }

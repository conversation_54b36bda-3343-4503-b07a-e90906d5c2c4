import argparse
import concurrent.futures
import functools
import logging
import multiprocessing
import os
import random
from collections import defaultdict
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np
import pandas as pd
import torch
from sklearn.cluster import BisectingKMeans, KMeans
from sklearn.metrics import calinski_harabasz_score, silhouette_score
from tqdm import tqdm

from deeplearning.clustering.bisecting_k_medoids import BisectingKMedoids
from deeplearning.comparison.data_utils import (
    download_comparison_files_for_dataframe,
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_dir,
    load_embeddings_from_torch,
)
from deeplearning.constants import CARBON_DATA_DIR, Environment
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.embeddings.metrics.aggregate import MeanNeighborhoodAverage
from deeplearning.embeddings.metrics.comparison_silhouette_score import ComparisonSilhouetteScore
from deeplearning.embeddings.metrics.comparison_similarity_score import ComparisonSimilarityScore
from deeplearning.embeddings.metrics.neighborhood import Neighborhood, PumapComparisonPoint
from deeplearning.embeddings.scripts.calculate_embedding_scores import (
    aggregate_annotation_embedding_pairs,
    make_df,
    retrieve_comparison_annotations,
)
from deeplearning.embeddings.utils.clustering import get_progress, get_progress_filepath, save_file, save_progress
from deeplearning.scripts.utils.utils import get_dataset_version, pick_comparison_model_id
from deeplearning.utils.download_utils import download_records
from lib.common.time.time import maka_control_timestamp_ms
from sklearn_extra.cluster import KMedoids

CLUSTERING_NAMES_TO_ALGORITHMS: Dict[str, Dict[str, Any]] = {
    "k_means": {
        "func": functools.partial(KMeans, init="k-means++"),
        "metric": "euclidean",
        "embedding_extraction_func": lambda x: x.pumap,
    },
    "bisecting_k_means": {
        "func": functools.partial(BisectingKMeans, init="k-means++"),
        "metric": "euclidean",
        "embedding_extraction_func": lambda x: x.pumap,
    },
    "k_medoids_comparison": {
        "func": functools.partial(KMedoids, init="k-medoids++", metric="cosine",),
        "metric": "cosine",
        "embedding_extraction_func": lambda x: x.comparison,
    },
    "k_medoids_deepweed": {
        "func": functools.partial(KMedoids, init="k-medoids++", metric="euclidean",),
        "metric": "euclidean",
        "embedding_extraction_func": lambda x: x.pumap,
    },
    "bisecting_k_medoids_comparison": {
        "func": functools.partial(BisectingKMedoids, init="k-medoids++", metric="cosine"),
        "metric": "cosine",
        "embedding_extraction_func": lambda x: x.comparison,
    },
    "bisecting_k_medoids_deepweed": {
        "func": functools.partial(BisectingKMedoids, init="k-medoids++", metric="euclidean"),
        "metric": "euclidean",
        "embedding_extraction_func": lambda x: x.pumap,
    },
    "k_medoids_deepweed_manhattan": {
        "func": functools.partial(KMedoids, init="k-medoids++", metric="manhattan",),
        "metric": "manhattan",
        "embedding_extraction_func": lambda x: x.pumap,
    },
    "bisecting_k_medoids_deepweed_manhattan": {
        "func": functools.partial(BisectingKMedoids, init="k-medoids++", metric="manhattan"),
        "metric": "manhattan",
        "embedding_extraction_func": lambda x: x.pumap,
    },
}

WCSS = "wcss"
CALINSKI_HARABASZ = "calinski_harabasz"
SILHOUETTE = "silhouette"
COMPARISON_SIMILARITY = "comparison_similarity"
COMPARISON_SILHOUETTE = "comparison_silhouette"

DEFAULT_METRICS: List[str] = [WCSS, CALINSKI_HARABASZ, SILHOUETTE, COMPARISON_SIMILARITY, COMPARISON_SILHOUETTE]


def get_comparison_embeddings(
    comparison_model_id: str, pt_files_from_image_id: Set[str], comparison_embedding_path: str, limit: int
) -> List[Dict[str, Any]]:
    download_records(
        item_id=comparison_model_id,
        s3_directory=f"{Environment.PRODUCTION.name.lower()}/comparison_evaluations",
        save_dir=comparison_embedding_path,
        bucket=get_comparison_evaluation_bucket(),
        skip_existing_files=True,
        filename_filters=pt_files_from_image_id,
        only_use_basename_in_download=True,
    )

    embeddings = []
    for file in tqdm(pt_files_from_image_id):
        # file with keys: "image_meta", "embeddings_data", "embeddings"
        # "image_meta" with keys: "image_id", "label_id", "image_url", "model_id", "geohash", "epoch"
        # "embeddings_data" with keys: "x", "y", "radius", "category". len == # of points
        # "embeddings": embeddings. len == # of points
        filepath = os.path.join(comparison_embedding_path, file)
        if not os.path.exists(filepath):
            logging.warning(f"File {filepath} does not exist")
            continue

        datapoints = load_embeddings_from_torch(input=filepath)
        image_data = datapoints["image_meta"]
        datapoints_embeddings_data = datapoints["embeddings_data"]
        datapoints_embeddings = datapoints["embeddings"]

        for (datapoint_embeddings_data, datapoint_embeddings) in zip(datapoints_embeddings_data, datapoints_embeddings):
            embeddings.append(
                {
                    "embedding": datapoint_embeddings,
                    "category": datapoint_embeddings_data["category"],
                    "filepath": image_data["image_url"],
                    "x": datapoint_embeddings_data["x"],
                    "y": datapoint_embeddings_data["y"],
                    "point_id": "",
                    "image_id": image_data["image_id"],
                }
            )
    logging.info(f"Retrieved {len(embeddings)} embedding.")

    return embeddings


def use_comparison(metrics: List[str]) -> bool:
    return COMPARISON_SILHOUETTE in metrics or COMPARISON_SIMILARITY in metrics


def comparison_similarity_score(
    clustering: Any, embedding_comparison_pairs: List[PumapComparisonPoint]
) -> Optional[float]:
    neighborhoods: List[Neighborhood] = []

    unique_labels = np.unique(clustering.labels_).tolist()
    for label in unique_labels:
        indices_for_cluster = np.nonzero((clustering.labels_ == label))
        transposed_indices = np.transpose(indices_for_cluster).squeeze()
        cluster_items = list(transposed_indices)

        for _ in range(100):
            sampled_indices = random.sample(cluster_items, k=min(101, len(cluster_items)))
            central_index = sampled_indices[0]
            remaining_indices = sampled_indices[1:]

            central_item = embedding_comparison_pairs[central_index]
            remaining_items = [embedding_comparison_pairs[ind] for ind in remaining_indices]

            neighborhoods.append(Neighborhood(central_item, remaining_items))

    mean_comparison_similarity_score_dist = MeanNeighborhoodAverage(
        neighborhoods, ComparisonSimilarityScore
    ).calculate()
    if mean_comparison_similarity_score_dist is not None:
        score = mean_comparison_similarity_score_dist["mean"].item()
    else:
        score = None

    return score


def comparison_silhouette_score(
    clustering: Any, embedding_comparison_pairs: List[PumapComparisonPoint]
) -> Optional[float]:
    neighborhood_nonneighborhood_pairs: List[Tuple[Neighborhood, Neighborhood]] = []

    unique_labels = np.unique(clustering.labels_).tolist()
    for label in unique_labels:
        indices_for_cluster = np.nonzero((clustering.labels_ == label))
        transposed_indices = np.transpose(indices_for_cluster).squeeze()
        cluster_items = list(transposed_indices)

        for non_neighbor_label in unique_labels:
            if non_neighbor_label == label:
                continue

            nn_indices_for_cluster = np.nonzero((clustering.labels_ == non_neighbor_label))
            nn_transposed_indices = np.transpose(nn_indices_for_cluster).squeeze()
            nn_cluster_items = list(nn_transposed_indices)

            for _ in range(100):
                sampled_indices = random.sample(cluster_items, k=min(101, len(cluster_items)))
                central_index = sampled_indices[0]
                remaining_indices = sampled_indices[1:]

                central_item = embedding_comparison_pairs[central_index]
                remaining_items = [embedding_comparison_pairs[ind] for ind in remaining_indices]

                nn_sampled_indices = random.sample(nn_cluster_items, k=min(100, len(nn_cluster_items)))

                nn_items = [embedding_comparison_pairs[ind] for ind in nn_sampled_indices]

                neighborhood_nonneighborhood_pairs.append(
                    (Neighborhood(central_item, remaining_items), Neighborhood(central_item, nn_items))
                )

    mean_comparison_silhouette_score_dist = MeanNeighborhoodAverage(
        [n[0] for n in neighborhood_nonneighborhood_pairs],
        ComparisonSilhouetteScore,
        [n[1] for n in neighborhood_nonneighborhood_pairs],
    ).calculate()
    if mean_comparison_silhouette_score_dist is not None:
        score = mean_comparison_silhouette_score_dist["mean"].item()
    else:
        score = None

    return score


def run_clustering(  # noqa: C901
    embeddings_df: pd.DataFrame,
    min_k: int,
    max_k: int,
    model_id: str,
    metrics: List[str],
    dataset_annotations_dict: Dict[str, List[Dict[str, Any]]],
    comparison_model_id: str,
    image_url2id: Dict[str, str],
    max_pairs: int,
    clustering_algorithms: List[str],
    clustering_output_file: Optional[str] = None,
    only_evaluate_new: bool = True,
) -> Tuple[Dict[str, List[Any]], Dict[Tuple[int, str], Any]]:
    # I want some way to tie image_identifier + x + y to cluster label
    if use_comparison(metrics):
        embedding_comparison_pairs = aggregate_annotation_embedding_pairs(
            model_embeddings=embeddings_df,
            dataset_annotations_dict=dataset_annotations_dict,
            comparison_model_id=comparison_model_id,
            model_id=model_id,
            image_url2id=image_url2id,
            max_pairs=max_pairs,
            embedding_type="FULL",
            config={},
        )
        logging.info("Retrieved comparison values")

    progress = get_progress(model_id_filepath=get_progress_filepath(model_id=model_id))

    scores = defaultdict(list)
    clusterings: Dict[Tuple[int, str], Any] = {}

    clustering_information: Dict[str, Any] = {"model_id": model_id, "max_pairs": max_pairs, "clusters": {}}

    for clustering_algorithm in clustering_algorithms:
        algorithm = CLUSTERING_NAMES_TO_ALGORITHMS[clustering_algorithm]
        embeddings = np.array(
            [
                algorithm["embedding_extraction_func"](item).squeeze().numpy().astype(np.float64)
                for item in embedding_comparison_pairs
            ]
        )

        if clustering_algorithm not in clustering_information["clusters"]:
            clustering_information["clusters"][clustering_algorithm] = {}

        for k in range(min_k, max_k + 1):
            print(f"Calculating scores for {clustering_algorithm}: {k}")
            need_to_eval = []

            for metric in metrics:
                need_to_eval.append((metric, clustering_algorithm))

            already_cached = []
            for key in need_to_eval:
                prog_key = str(key)
                if progress.get(prog_key) is None:
                    progress[prog_key] = {}

                if only_evaluate_new and progress[prog_key].get(str(k)) is not None:
                    scores[prog_key].append({"k": k, "score": progress[prog_key][str(k)]})
                    print(f"\tAlready have k={k} cached for {prog_key}")
                    already_cached.append(key)

            if len(already_cached) == len(need_to_eval):
                continue

            clusterer = algorithm["func"](n_clusters=k)
            clustering = clusterer.fit(embeddings)
            clustering_information["clusters"][clustering_algorithm][k] = defaultdict(list)
            for i, item in enumerate(embedding_comparison_pairs):
                item_meta = item.metadata

                clustering_information["clusters"][clustering_algorithm][k][item_meta["filepath"]].append(
                    {"x": item_meta["x"], "y": item_meta["y"], "cluster_label": str(clustering.labels_[i])}
                )
            for key in need_to_eval:
                if key in already_cached:
                    continue
                if key[0] == CALINSKI_HARABASZ:
                    score = calinski_harabasz_score(embeddings, clustering.labels_)
                elif key[0] == SILHOUETTE:
                    score = silhouette_score(embeddings, clustering.labels_, metric=algorithm["metric"],)
                elif key[0] == WCSS:
                    score = clustering.inertia_
                elif key[0] == COMPARISON_SIMILARITY:
                    score = comparison_similarity_score(clustering, embedding_comparison_pairs)
                elif key[0] == COMPARISON_SILHOUETTE:
                    score = comparison_silhouette_score(clustering, embedding_comparison_pairs)

                print(f"Keys and score {key} {k} {score}")

                progress[str(key)][k] = score
                save_progress(model_id, progress)
                if clustering_output_file:
                    save_file(clustering_output_file, clustering_information)
                scores[str(key)].append({"k": k, "score": score})
            clusterings[(k, clustering_algorithm)] = clustering

    return scores, clusterings


def run_sweeps(
    model_id: str,  # comparison_model_id
    dataset_id: str,  # The dataset_id used by the training job.
    min_k: int,
    max_k: int,
    metrics: List[str],
    limit: int,
    clustering_algorithms: List[str],
    clustering_output_file: Optional[str] = None,
    only_evaluate_new: bool = True,
) -> Tuple[Optional[Dict[str, List[Any]]], Optional[Dict[Tuple[int, str], Any]]]:
    if not use_comparison(metrics):
        return None, None

    comparison_model_id = pick_comparison_model_id(comparison_model_id=None, parent_comparison_model_id=None)

    dataset_version = get_dataset_version(dataset_id)

    dataset_annotations_dict, image_url2id, _ = retrieve_comparison_annotations(
        dataset_id=dataset_id,
        comparison_model_id=comparison_model_id,
        filename="test.json" if dataset_version == 1 else "test.jsonl",
    )
    if (
        not dataset_annotations_dict or not image_url2id or comparison_model_id is None
    ):  # Check either dataset_annotations_dict or image_url2id is an empty dict.
        return None, None

    pt_files_from_image_id: Set[str] = {f"{key}.pt" for key in dataset_annotations_dict}
    embeddings = get_comparison_embeddings(
        comparison_model_id=comparison_model_id,
        pt_files_from_image_id=pt_files_from_image_id,
        comparison_embedding_path=f"{get_comparison_evaluation_dir()}/{Environment.PRODUCTION.name.lower()}/comparison_evaluations/{comparison_model_id}",
        limit=limit,
    )
    embeddings_df = make_df(embeddings=embeddings)

    scores, clustering = run_clustering(
        embeddings_df=embeddings_df,
        min_k=min_k,
        max_k=max_k,
        model_id=model_id,
        metrics=metrics,
        dataset_annotations_dict=dataset_annotations_dict,
        comparison_model_id=comparison_model_id,
        image_url2id=image_url2id,
        max_pairs=limit,
        clustering_algorithms=clustering_algorithms,
        clustering_output_file=clustering_output_file,
        only_evaluate_new=only_evaluate_new,
    )
    return scores, clustering


def get_best_clustering_config(scores: Dict[str, List[Any]]) -> Dict[str, Any]:
    proposed_clusterings = []
    clusterings_for_k: Dict[int, Any] = {}
    for key, values in scores.items():
        metric, clustering_algorithm = eval(key)
        for val in values:
            k = val["k"]
            score = val["score"]
            if clustering_algorithm not in clusterings_for_k:
                clusterings_for_k[clustering_algorithm] = {}
            if k not in clusterings_for_k[clustering_algorithm]:
                clusterings_for_k[clustering_algorithm][k] = {}

            clusterings_for_k[clustering_algorithm][k][metric] = score

    for algo, k_scores in clusterings_for_k.items():
        for k, scores in k_scores.items():
            clustering_scores = {"k": k, "algorithm": algo}
            clustering_scores.update(scores)
            proposed_clusterings.append(clustering_scores)

    max_comparison_sim = 0
    best_clustering_config = None
    for score_dict in proposed_clusterings:
        if score_dict[COMPARISON_SIMILARITY] > max_comparison_sim and score_dict[COMPARISON_SILHOUETTE] > 0.5:
            max_comparison_sim = score_dict[COMPARISON_SIMILARITY]
            best_clustering_config = score_dict

    assert best_clustering_config is not None

    return best_clustering_config


def get_best_clustering(
    model_id: str, dataset_id: str, min_k: int, max_k: int, limit: int, clustering_algorithm: str,
) -> Any:
    scores, clusterings = run_sweeps(
        model_id=model_id,
        dataset_id=dataset_id,
        min_k=min_k,
        max_k=max_k,
        metrics=[COMPARISON_SIMILARITY, COMPARISON_SILHOUETTE],
        limit=limit,
        clustering_algorithms=[clustering_algorithm],
        only_evaluate_new=False,
    )

    assert scores is not None and clusterings is not None
    best_clustering_config = get_best_clustering_config(scores)
    best_clustering_key = (best_clustering_config["k"], best_clustering_config["algorithm"])
    best_clustering = clusterings[best_clustering_key]

    return best_clustering


def predict_cluster_index(clustering: Any, filepath: str, dir: str) -> None:
    embeddings = load_embeddings_from_torch(filepath)
    np_embeddings = embeddings["embeddings"].numpy()
    predicted_indices = clustering.predict(np_embeddings)
    torch_predicted_indices = torch.from_numpy(predicted_indices)
    embeddings["embeddings"] = torch_predicted_indices

    base_filename = os.path.basename(filepath)
    torch.save(embeddings, os.path.join(dir, base_filename))


def cluster_comparison_embeddings(clustering: Any, comparison_model_id: str, comparison_cluster_dir: str) -> None:
    embedding_dir = f"{get_comparison_evaluation_dir()}/{comparison_model_id}"
    os.makedirs(comparison_cluster_dir, exist_ok=True)

    with concurrent.futures.ThreadPoolExecutor(max_workers=multiprocessing.cpu_count()) as executor:
        for file in os.listdir(embedding_dir):
            executor.submit(
                predict_cluster_index, clustering, os.path.join(embedding_dir, file), dir=comparison_cluster_dir
            )


def enable_embedding_clusters_sampling(
    dl_config: DeepweedConfig, dataset_id: str, dataset_parameters: Optional[Dict[str, Any]]
) -> None:
    assert dataset_parameters is not None, "dataset_parameters cannot be None."
    assert dl_config.clustering_algorithm == "k_medoids_comparison", "Only k_medoids_comparison can be used (for now)"
    assert dl_config.embedding_balancing_model is not None, "embedding_balancing_model cannot be None."
    best_clustering = get_best_clustering(
        model_id=dl_config.embedding_balancing_model,
        dataset_id=dataset_id,  # We need to use the current generated dataset_id for embedding clusters.
        min_k=dl_config.clustering_min_k,
        max_k=dl_config.clustering_max_k,
        limit=dl_config.clustering_n,
        clustering_algorithm=dl_config.clustering_algorithm,
    )

    assert dl_config.comparison_model_id is not None, "comparison_model_id cannot be None."
    download_comparison_files_for_dataframe(
        json_filepath=dataset_parameters["train_filepath"], comparison_model_id=dl_config.comparison_model_id
    )
    comparison_cluster_dir = os.path.join(
        f"{CARBON_DATA_DIR}",
        f"deeplearning/comparison_indices/{dl_config.comparison_model_id}/{best_clustering.__class__.__name__}/{best_clustering.cluster_centers_.shape[0]}/{best_clustering.labels_.shape[0]}",
    )
    cluster_comparison_embeddings(
        clustering=best_clustering,
        comparison_model_id=dl_config.comparison_model_id,
        comparison_cluster_dir=comparison_cluster_dir,
    )

    dataset_parameters["embedding_balancing_evaluation_path"] = comparison_cluster_dir
    dataset_parameters["embedding_balancing_on_clusters"] = True


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    parser.add_argument("--min-k", type=int, default=2)
    parser.add_argument("--max-k", type=int, default=10)
    parser.add_argument("--limit", type=int, default=int(1e5))
    parser.add_argument(
        "--clustering-algorithms", type=str, default=",".join(list(CLUSTERING_NAMES_TO_ALGORITHMS.keys()))
    )
    parser.add_argument("--metrics", type=str, default=",".join(DEFAULT_METRICS))
    parser.add_argument(
        "--clustering-output",
        type=str,
        help="Output file for clustering information, which can be used for plotting clusters",
        default=None,
    )
    parser.add_argument("--no-cache", action="store_false", dest="only_evaluate_new")
    parser.set_defaults(only_evaluate_new=True)
    args = parser.parse_args()

    clustering_algorithms = args.clustering_algorithms.split(",")

    start = maka_control_timestamp_ms()
    run_sweeps(
        args.model_id,
        args.min_k,
        args.max_k,
        args.metrics.split(","),
        args.limit,
        clustering_algorithms,
        args.clustering_output,
        args.only_evaluate_new,
    )
    end = maka_control_timestamp_ms()

    duration_seconds = (end - start) / 1000
    minutes = duration_seconds // 60
    seconds = duration_seconds % 60
    print(f"Total runtime: {minutes}m{seconds}s")


if __name__ == "__main__":
    main()

from collections import defaultdict
from typing import Any, Dict, List, Optional, cast

import numpy as np
import numpy.typing as npt
import torch

from deeplearning.comparison.data_utils import cosine_similarity_normed_inputs
from sklearn_extra.cluster import KMedoids

METRIC_COSINE = "cosine"
METRIC_EUCLIDEAN = "euclidean"
METRIC_MANHATTAN = "manhattan"


class Cluster:
    def __init__(self, medoid: npt.NDArray[Any], inertia: float, indices: List[int]) -> None:
        self.inertia = inertia
        self.medoid = medoid
        self.indices = indices

    def __len__(self) -> int:
        return len(self.indices)


class Clustering:
    def __init__(self, items: npt.NDArray[Any], clustering_dictionary: Dict[int, Cluster]):
        self._clustering_dictionary = clustering_dictionary

        self.inertia_ = sum([val.inertia for val in self._clustering_dictionary.values()])
        labels = [-1] * items.shape[0]

        for cluster_id, cluster in self._clustering_dictionary.items():
            for index in cluster.indices:
                labels[index] = cluster_id

        self.labels_ = np.array(labels)


class BisectingKMedoids:
    def __init__(self, metric: str, n_clusters: int, init: str = "k-medoids++"):
        assert metric in [METRIC_COSINE, METRIC_EUCLIDEAN, METRIC_MANHATTAN], f"Metric {metric} not currently supported"
        self._metric = metric
        self._n_clusters = n_clusters
        self._init_method = init

    def _calculate_cosine_inertia(self, cluster_center: npt.NDArray[Any], cluster_points: npt.NDArray[Any]) -> float:
        cluster_array = torch.nn.functional.normalize(torch.from_numpy(cluster_points).to(device="cuda"))
        cluster_center_tensor = torch.nn.functional.normalize(
            torch.from_numpy(cluster_center).unsqueeze(0).to(device="cuda")
        )

        inertia = (
            1
            - torch.nn.functional.softplus(
                cosine_similarity_normed_inputs(cluster_center_tensor, cluster_array), beta=20, threshold=2
            )
        ).sum()
        return cast(float, inertia.item())

    def _calculate_euclidean_inertia(self, cluster_center: npt.NDArray[Any], cluster_points: npt.NDArray[Any]) -> float:
        inertia = np.linalg.norm(cluster_points - cluster_center, axis=1).sum()
        return cast(float, inertia)

    def _calculate_manhattan_inertia(self, cluster_center: npt.NDArray[Any], cluster_points: npt.NDArray[Any]) -> float:
        inertia = np.abs(cluster_points - cluster_center).sum()
        return cast(float, inertia)

    def fit(self, items: npt.NDArray[Any], max_iter: int = 300, random_state: Optional[int] = None) -> Clustering:
        cluster_indices = np.array([i for i in range(items.shape[0])])

        clusters: Dict[int, Cluster] = {}
        while len(clusters) < self._n_clusters:
            new_cluster_dict_data: Dict[int, List[int]] = defaultdict(list)

            cluster_items = items[cluster_indices]
            new_clusters = KMedoids(
                n_clusters=2, metric=self._metric, max_iter=max_iter, random_state=random_state
            ).fit(cluster_items)
            for ind, cluster_label in enumerate(new_clusters.labels_):
                new_cluster_dict_data[cluster_label].append(cluster_indices[ind])

            medoids = new_clusters.cluster_centers_

            new_cluster_items = {}

            for ind, cluster_point_indices in new_cluster_dict_data.items():
                cluster_points = items[cluster_point_indices]
                medoid = medoids[ind]
                embeddings = np.array(cluster_points)
                if self._metric == METRIC_COSINE:
                    inertia = self._calculate_cosine_inertia(medoid, embeddings)
                elif self._metric == METRIC_EUCLIDEAN:
                    inertia = self._calculate_euclidean_inertia(medoid, embeddings)
                elif self._metric == METRIC_MANHATTAN:
                    inertia = self._calculate_manhattan_inertia(medoid, embeddings)

                new_cluster_items[ind] = Cluster(medoid, inertia, cluster_point_indices)

            if len(clusters) == 0:
                iteration_adjustment = 0
            else:
                iteration_adjustment = max(clusters.keys()) + 1
            for key, value in new_cluster_items.items():
                clusters[key + iteration_adjustment] = value
            max_inertia = -1.0
            index_to_split = 0
            for ind, cluster in clusters.items():
                if cluster.inertia > max_inertia:
                    index_to_split = ind
                    max_inertia = cluster.inertia

            if len(clusters) == self._n_clusters:
                break

            cluster_to_split = clusters[index_to_split]
            del clusters[index_to_split]
            cluster_indices = np.array(cluster_to_split.indices)

        return Clustering(items, clusters)

#!/usr/bin/env bash
set -eu

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

if [ $# -lt 1 ]; then
    echo "Usage: $0 [--full | --best-checkpoint | --gradient-norms] <model id> [destination]"
fi

include="--exclude * --include torchscript_model.pth"
if [ $1 == "--full" ]; then
    include="--include *"
    shift
fi

if [ $1 == "--best_checkpoint" ] || [ $1 == "--best-checkpoint" ]; then
    include="${include} --include best_checkpoint.ckpt"
    shift
fi

if [ $1 == "--int8_calib" ] || [ $1 == "--int8-calib" ]; then
    include="${include} --include trt_int8.calib"
    shift
fi

if [ $1 == "--gradient_norms" ] || [ $1 == "--gradient-norms" ]; then
    include="${include} --include gradient_norms*.pickle"
    shift
fi

if [ $1 == "--converted" ]; then
    include="${include} --include trt_*.trt"
    shift
fi


model_id=$1
shift

if [ $# -ge 1 ]; then
    destination=$1
    shift
else
    destination=${script_dir}/../../lightning_logs
fi

mkdir -p ${destination}
set -o noglob
aws s3 sync s3://maka-pono/models/${model_id} ${destination}/${model_id} ${include}

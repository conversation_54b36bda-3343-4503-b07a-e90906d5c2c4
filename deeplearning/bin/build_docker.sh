#!/usr/bin/env bash
set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
DEEPLEARNING_CONTAINER_DIR="${SCRIPT_DIR}"/../../services/containers/deeplearning/
DOCKER_BUILD_CONTEXT=${SCRIPT_DIR}/../..

export MAKA_DOCKER_DEEPLEARNING_TAG=${MAKA_DOCKER_DEEPLEARNING_TAG:-ghcr.io/carbonrobotics/robot/deeplearning}
export MAKA_DOCKER_CONTAINER_NAME=${MAKA_DOCKER_CONTAINER_NAME:-}
export MAKA_MODELS_DIR=${MAKA_MODELS_DIR:-}

# copy users authorized_keys into build context, if exits
[ -f ~/.ssh/authorized_keys ] && cp -p ~/.ssh/authorized_keys "${DEEPLEARNING_CONTAINER_DIR}"

# build
docker build -t "${MAKA_DOCKER_DEEPLEARNING_TAG}" "${@}" "${DOCKER_BUILD_CONTEXT}" -f services/containers/deeplearning/Dockerfile; \

# cleanup git
git checkout -- "${DEEPLEARNING_CONTAINER_DIR}"/authorized_keys

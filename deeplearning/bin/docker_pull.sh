#!/bin/bash -eu

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
dockerfile_dir=${script_dir}/../docker

if [ $# -lt 3 ]; then
	echo "Usage: $0 <type> <location> <output file> [<tag>]"
	exit 1
fi

model_type=$1
location=$2
output_file=$3
tag=${4-latest}

image_name=ghcr.io/carbonrobotics/maka/models/${model_type}/${location}:${tag}

docker pull ${image_name}
container_id=$(docker create ${image_name} zzzz)

docker cp ${container_id}:/models/${model_type}/${model_type}.trt ${output_file}

docker rm ${container_id}
docker rmi ${image_name}

import traceback
from typing import Any, Callable, TypeVar, cast

T = TypeVar("T", bound=Callable[..., Any])


def log_uncaught_exception(func: T) -> T:
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        try:
            return func(*args, **kwargs)
        except Exception:
            print("Uncaught exception thrown from {func}:".format(func=func))
            traceback.print_exc()
            raise

    return cast(T, wrapper)

import enum
import os
from typing import List, Optional, Sequence, <PERSON><PERSON>

import numpy as np
from PIL import Image, ImageFile

ImageFile.LOAD_TRUNCATED_IMAGES = True

DEFAULT_SPLITS = (70, 20, 10)
DEFAULT_GEOHASH = "7zzzzzzzzzzz"


class DatasetType(enum.Enum):
    TRAIN = 1
    TEST = 2
    VALIDATION = 3
    CALIBRATION = 4


def get_carbon_cache_host() -> Optional[str]:
    carbon_cache_host: Optional[str] = None
    if not os.getenv("DISABLE_S3_CACHE_PROXY"):
        carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")
    return carbon_cache_host


def pil_loader(path: str, grayscale: bool = False) -> Image.Image:
    # open path as file to avoid ResourceWarning (https://github.com/python-pillow/Pillow/issues/835)
    with open(path, "rb") as f:
        img = Image.open(f)
        return img.convert("L" if grayscale else "RGB")


def index_split(dataset_size: int, splits: Sequence[int], random_seed: int = 42) -> Tuple[List[int], ...]:
    np.random.seed(random_seed)
    all_indices = np.arange(0, dataset_size)
    np.random.shuffle(all_indices)

    cum_splits = np.cumsum(np.array(splits).astype(np.int8))
    assert cum_splits[-1] == 100, "Splits must add up too 100."

    cum_index_splits = cum_splits * dataset_size / 100
    cum_index_splits = cum_index_splits.astype(int)

    indices_list = []
    for split_idx in range(len(splits)):
        a = cum_index_splits[split_idx - 1] if split_idx > 0 else 0
        b = cum_index_splits[split_idx]
        indices_list.append(all_indices[a:b].tolist())

    return tuple(indices_list)


def get_recency_split_value(reverse_sorted_timestamps: List[int], min_images: int, time_back: int) -> Tuple[int, int]:
    recency_count = 0
    index = 0
    candidate = reverse_sorted_timestamps[0]
    while index < len(reverse_sorted_timestamps) and (
        recency_count <= min_images or reverse_sorted_timestamps[index] >= time_back
    ):
        candidate = reverse_sorted_timestamps[index]
        index += 1
        recency_count += 1

    return candidate, recency_count

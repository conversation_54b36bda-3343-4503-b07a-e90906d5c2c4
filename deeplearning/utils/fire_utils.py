from typing import Any, List, Optional, Tuple, Union, overload


@overload
def safe_split(s: Union[str, List[str], Tuple[str, ...]]) -> List[str]:
    ...


@overload
def safe_split(s: Any) -> Optional[List[Any]]:
    ...


def safe_split(s: Any) -> Optional[List[Any]]:
    if s is None:
        return None

    if isinstance(s, str):
        return s.split(",")

    if isinstance(s, list) or isinstance(s, tuple):
        return list(s)

    return [s]

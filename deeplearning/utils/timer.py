import sys
import time
from typing import Any


class Timer:
    def __init__(self, name: str) -> None:
        self.name = name

    def __enter__(self) -> None:
        self.start = time.time()
        print("start: {}".format(self.name), file=sys.stderr)

    def __exit__(self, t: Any, v: Any, tb: Any) -> None:
        self.end = time.time()
        print("end: {} -> {}".format(self.name, self.end - self.start), file=sys.stderr)

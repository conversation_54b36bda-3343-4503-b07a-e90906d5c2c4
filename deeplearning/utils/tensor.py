import logging
import math
from enum import Enum
from typing import Any, List, Optional, Tuple

import cv2
import numpy as np
import pandas
import torch

from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder_decorator


def broadcast_object(object: Any, src_rank: int = 0) -> Any:
    object_list = [object]
    torch.distributed.broadcast_object_list(object_list, src=src_rank)
    return object_list[0]


@duration_perf_recorder_decorator(PerfCategory.TRAINING)
def gather_objects(outputs: List[Any]) -> List[Any]:
    all_objects: List[Any] = []
    if torch.distributed.get_rank() == 0:
        output_list = [None for _ in range(torch.distributed.get_world_size())]
    else:
        output_list = None
    torch.distributed.gather_object(outputs, object_gather_list=output_list)
    if torch.distributed.get_rank() == 0:
        assert output_list is not None
        for x in output_list:
            assert x is not None
            all_objects.extend(x)
    return all_objects


def make_texture_tensor(shape: Tuple[int, int], index: int) -> torch.Tensor:
    base = None
    if index == 0:
        # solid fill
        base = torch.ones((7, 7))
    elif index == 1:
        base = torch.tensor(
            [
                [1, 1, 0, 0, 0, 0, 1],
                [1, 1, 1, 0, 0, 0, 0],
                [0, 1, 1, 1, 0, 0, 0],
                [0, 0, 1, 1, 1, 0, 0],
                [0, 0, 0, 1, 1, 1, 0],
                [0, 0, 0, 0, 1, 1, 1],
                [1, 0, 0, 0, 0, 1, 1],
            ]
        )
    elif index == 2:
        base = torch.tensor(
            [
                [1, 0, 0, 0, 0, 1, 1],
                [0, 0, 0, 0, 1, 1, 1],
                [0, 0, 0, 1, 1, 1, 0],
                [0, 0, 1, 1, 1, 0, 0],
                [0, 1, 1, 1, 0, 0, 0],
                [1, 1, 1, 0, 0, 0, 0],
                [1, 1, 0, 0, 0, 0, 1],
            ]
        )
    elif index == 3:
        base = torch.tensor(
            [
                [1, 1, 0, 0, 0, 0, 1],
                [1, 1, 1, 0, 0, 0, 0],
                [0, 1, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 1, 0],
                [0, 0, 0, 0, 1, 1, 1],
                [1, 0, 0, 0, 0, 1, 1],
            ]
        )
    elif index == 4:
        base = torch.tensor(
            [
                [1, 0, 0, 0, 0, 1, 1],
                [0, 0, 0, 0, 1, 1, 1],
                [0, 0, 0, 0, 0, 1, 0],
                [0, 0, 0, 0, 0, 0, 0],
                [0, 1, 0, 0, 0, 0, 0],
                [1, 1, 1, 0, 0, 0, 0],
                [1, 1, 0, 0, 0, 0, 1],
            ]
        )
    else:
        raise Exception(f"Don't have texture for index={index}")
    return base.repeat(math.ceil(shape[0] / 5), math.ceil(shape[1] / 5))[: shape[0], : shape[1]].bool()


def recursive_move(obj: Any, device: torch.device, whitelist: Optional[List[type]] = None) -> Any:
    if (
        isinstance(obj, int)
        or isinstance(obj, str)
        or isinstance(obj, float)
        or isinstance(obj, pandas.core.frame.DataFrame)
        or obj is None
        or any(isinstance(obj, t) for t in (whitelist or []))
    ):
        return obj
    if isinstance(obj, torch.Tensor):
        return obj.to(device)
    if isinstance(obj, tuple) or isinstance(obj, list):
        return type(obj)(recursive_move(e, device, whitelist=whitelist) for e in obj)
    if isinstance(obj, dict):
        return {k: recursive_move(v, device, whitelist=whitelist) for k, v in obj.items()}
    raise Exception(f"Unknown type: {type(obj)}")


def recursive_to_numpy(obj: Any, whitelist: Optional[List[type]] = None) -> Any:
    if (
        isinstance(obj, int)
        or isinstance(obj, str)
        or isinstance(obj, float)
        or isinstance(obj, pandas.core.frame.DataFrame)
        or obj is None
        or any(isinstance(obj, t) for t in (whitelist or []))
    ):
        return obj
    if isinstance(obj, torch.Tensor):
        return obj.numpy()
    if isinstance(obj, tuple) or isinstance(obj, list):
        return type(obj)(recursive_to_numpy(e, whitelist=whitelist) for e in obj)
    if isinstance(obj, dict):
        return {k: recursive_to_numpy(v, whitelist=whitelist) for k, v in obj.items()}
    raise Exception(f"Unknown type: {type(obj)}")


def recursive_to_tensor(obj: Any, whitelist: Optional[List[type]] = None) -> Any:
    if (
        isinstance(obj, int)
        or isinstance(obj, str)
        or isinstance(obj, float)
        or isinstance(obj, pandas.core.frame.DataFrame)
        or obj is None
        or any(isinstance(obj, t) for t in (whitelist or []))
    ):
        return obj
    if isinstance(obj, np.ndarray):
        return torch.tensor(obj)
    if isinstance(obj, tuple) or isinstance(obj, list):
        return type(obj)(recursive_to_tensor(e, whitelist=whitelist) for e in obj)
    if isinstance(obj, dict):
        return {k: recursive_to_tensor(v, whitelist=whitelist) for k, v in obj.items()}
    raise Exception(f"Unknown type: {type(obj)}")


def recursive_detach(obj: Any, whitelist: Optional[List[type]] = None) -> Any:
    if (
        isinstance(obj, int)
        or isinstance(obj, str)
        or isinstance(obj, float)
        or isinstance(obj, pandas.core.frame.DataFrame)
        or obj is None
        or any(isinstance(obj, t) for t in (whitelist or []))
    ):
        return obj
    if isinstance(obj, torch.Tensor):
        return obj.detach()
    if isinstance(obj, tuple) or isinstance(obj, list):
        return type(obj)(recursive_detach(e, whitelist=whitelist) for e in obj)
    if isinstance(obj, dict):
        return {k: recursive_detach(v, whitelist=whitelist) for k, v in obj.items()}
    raise Exception(f"Unknown type: {type(obj)}")


class RenderMarkerKind(Enum):
    DIAMOND = 0
    CIRCLE = 1


def render_marker(mask: torch.Tensor, x: int, y: int, psize: int, marker_kind: RenderMarkerKind) -> None:
    assert len(mask.shape) == 2, f"Wrong mask shape: {mask.shape}"

    if x < 0 or x >= mask.shape[1] or y < 0 or y >= mask.shape[0]:
        # TODO(asergeev): better handling for off-screen center rendering
        logging.warning(f"Rendering outside of mask: {mask.shape}, x={x}, y={y}, psize={psize}")
        return

    # Draw the marker
    pane = np.zeros((psize * 2 + 5, psize * 2 + 5), dtype=np.uint8)
    pane_center = (psize + 2, psize + 2)
    if marker_kind == RenderMarkerKind.DIAMOND:
        cv2.drawMarker(pane, pane_center, (1.0,), markerType=cv2.MARKER_DIAMOND, markerSize=psize * 2, thickness=3)
    elif marker_kind == RenderMarkerKind.CIRCLE:
        cv2.circle(pane, pane_center, psize, (1.0,), thickness=cv2.FILLED)
    else:
        assert False, f"Unknown marker_kind: {marker_kind}"

    # Copy marker into the tensor
    left = min(psize + 2, x)
    right = min(psize + 3, mask.shape[1] - x)
    top = min(psize + 2, y)
    bottom = min(psize + 3, mask.shape[0] - y)
    pane_crop = pane[pane_center[1] - top : pane_center[1] + bottom, pane_center[0] - left : pane_center[0] + right]
    mask_crop = mask[y - top : y + bottom, x - left : x + right]
    mask_crop[:] = torch.max(mask_crop, torch.tensor(pane_crop).type_as(mask).to(mask.device))

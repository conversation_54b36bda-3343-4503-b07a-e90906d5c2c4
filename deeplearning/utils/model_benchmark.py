import time
from typing import Any, List, Optional, Tuple

import tensorrt as trt
import torch
from torch2trt import TensorBatchDataset, torch2trt

from deeplearning.model_io import ModelMetadata, load_tensorrt_model, save_tensorrt_model
from deeplearning.utils.benchmark import print_latencies


class Profiler(trt.IProfiler):
    def __init__(self) -> None:
        trt.IProfiler.__init__(self)

        self._timings: List[Tuple[str, float]] = []

    def report_layer_time(self, layer_name: str, ms: float) -> None:
        self._timings.append((layer_name, ms))

    def print_layer_timings(self) -> None:
        timings = sorted(self._timings, key=lambda x: x[1], reverse=True)

        for layer_name, ms in timings:
            print(f"{layer_name}: {ms}ms")


class ModelBenchmark:
    def __init__(
        self,
        fp16: bool = False,
        int8: bool = False,
        batch_size: int = 1,
        inputs: Optional[List[torch.Tensor]] = None,
        model: Any = None,
        model_cache_name: str = "",
        use_implicit_batch_dimension_trt: bool = True,
    ) -> None:
        inputs = inputs if inputs is not None else []
        self._fp16 = fp16
        self._int8 = int8
        self._batch_size = batch_size
        self._inputs = inputs
        self._model_cache_name = model_cache_name
        self._use_implicit_batch_dimension_trt = use_implicit_batch_dimension_trt
        self._stream = torch.cuda.Stream()

        self._model = model
        self._model.eval().cuda()

    def pytorch(self) -> "ModelBenchmark":
        """Turn model into PyTorch"""
        if self._fp16:
            self._model.half()
            self._inputs = [x.half() for x in self._inputs]
        return self

    def script(self) -> "ModelBenchmark":
        """Turn model into TorchScript"""
        self._model = torch.jit.script(self._model)
        return self

    def trt(self) -> "ModelBenchmark":
        """Convert model to TensorRT"""
        with torch.no_grad():
            self._model = torch2trt(
                self._model,
                self._inputs,
                fp16_mode=self._fp16,
                int8_mode=self._int8,
                int8_calib_dataset=TensorBatchDataset([x[0:1] for x in self._inputs]),
                max_batch_size=self._batch_size,
                max_workspace_size=1 << 27,
                log_level=trt.Logger.INFO,
                strict_type_constraints=True,
                use_implicit_batch_dimension=self._use_implicit_batch_dimension_trt,
            )
        metadata = ModelMetadata(input_dtype=torch.float16 if self._fp16 else torch.float32,)
        save_tensorrt_model(self._model, metadata, self._model_cache_name)
        return self

    def trt_load(self) -> "ModelBenchmark":
        """Load previous converterd TensorRT model"""
        self._model, metadata = load_tensorrt_model(self._model_cache_name)
        self._model.set_cache_context(True)
        assert (metadata.input_dtype == torch.float16) == self._fp16
        return self

    def trt_profile(self) -> "ModelBenchmark":
        """Profile converted or loaded TensorRT model"""
        # warm-up
        for x in range(3):
            self._run_once()

        # run actual profiling
        self._model.context.profiler = Profiler()
        self._run_once()

        self._model.context.profiler.print_layer_timings()

        return self

    def benchmark(self, iterations: int = 30) -> "ModelBenchmark":
        """Run benchmark"""
        self.trt_load()  # Use our TRT runtime

        # warm-up
        for x in range(3):
            self._run_once()

        # run the actual benchmark
        latencies = []
        for x in range(iterations):
            latencies.append(self._run_once())

        print_latencies(latencies, unit="ms")

        return self

    def done(self) -> None:
        """Run me last!"""
        pass

    def _run_once(self) -> float:
        with torch.no_grad(), torch.cuda.StreamContext(self._stream):
            start = time.perf_counter()
            self._model(*self._inputs)
            torch.cuda.synchronize()
        return (time.perf_counter() - start) * 1000.0

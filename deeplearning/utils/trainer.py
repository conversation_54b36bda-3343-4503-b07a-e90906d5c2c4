import datetime
import glob
import hashlib
import json
import logging
import os
import re
import struct
import time
import warnings
from abc import ABC, abstractmethod, abstractproperty
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union, cast

import boto3
import fire
import lightning.pytorch as pl
import numpy as np
import requests
import tensorrt
import torch
import torch.nn.functional as F
from lightning.pytorch.loggers import Wandb<PERSON>ogger
from tenacity import retry, stop_after_attempt, stop_after_delay, wait_exponential, wait_fixed
from torch.utils.data import get_worker_info
from urllib3.exceptions import InsecureRequestWarning

from deeplearning.constants import EMBEDDING_VERSION, S3_BUCKET, Environment
from deeplearning.model_io import ModelMetadata, save_pytorch_model
from deeplearning.model_io.torchscript import save_torchscript_model
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.progress_bar import ProgressBar
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import WANDB_PROJECT_MAP
from deeplearning.utils.wandb_logger import make_wandb_logger
from lib.common.perf.perf_tracker import PerfCategory, add_perf_data_point, get_data_category, set_verbosity
from lib.common.time.time import maka_control_timestamp_ms
from lib.common.veselka.client import VeselkaClient

LOG = logging.getLogger(__name__)

warnings.filterwarnings("ignore", ".*It is recommended to use.*")
warnings.filterwarnings("ignore", "The value of the smallest subnormal for.*")

set_verbosity(False)

CI_SUFFIX = "ci"

# Suppress only the single warning from urllib3 needed.
requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class SaveLastCheckpointCallback(pl.callbacks.Callback):
    def __init__(self, save_dir: str, training_module: "TrainingModule"):
        self._save_dir = save_dir
        self._module = training_module
        self.save_function = None

    def on_train_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """Called at the end of the training epoch"""

        # save both latest and latest_with_metadata, because latest can be used for resuming training but
        # latest_with_metadata is required for trt conversion.
        filepath = os.path.join(self._save_dir, "latest.ckpt")
        trainer.save_checkpoint(filepath)

        if torch.distributed.get_rank() == 0:
            LOG.info(f"Epoch: {trainer.current_epoch} | Saving model to {filepath}")

            # Save the best PyTorch checkpoint for possible conversion to TensorRT
            mod = self._module.export_model()
            meta = self._module.export_metadata()
            save_pytorch_model(
                mod, meta, os.path.join(self._save_dir, "latest_with_metadata.ckpt"),
            )
            LOG.info("Done saving checkpoint")


class EnableCheckpointCallback(pl.callbacks.Callback):
    def __init__(self, checkpoint_callback: pl.callbacks.ModelCheckpoint, checkpoint_start_epoch: int):
        self._checkpoint_callback = checkpoint_callback
        self._checkpoint_start_epoch = checkpoint_start_epoch

    def on_train_epoch_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """Called when the epoch begins."""
        if trainer.current_epoch >= self._checkpoint_start_epoch:
            self._checkpoint_callback.save_top_k = 1


class TimingCallback(pl.callbacks.Callback):
    def __init__(self, logger: Any):
        self._train_epoch_start_ms = 0
        self._validation_epoch_start_ms = 0
        self._test_epoch_start_ms = 0
        self.logger = logger

    def on_epoch_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        self._train_epoch_start_ms = maka_control_timestamp_ms()

    def on_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        add_perf_data_point(
            PerfCategory.TRAINING, "train_epoch_time", "ms", maka_control_timestamp_ms() - self._train_epoch_start_ms
        )
        log_timing_metrics(self.logger)

    def on_validation_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        self._validation_epoch_start_ms = maka_control_timestamp_ms()

    def on_validation_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        add_perf_data_point(
            PerfCategory.TRAINING,
            "validation_time",
            "ms",
            maka_control_timestamp_ms() - self._validation_epoch_start_ms,
        )
        log_timing_metrics(self.logger)

    def on_test_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        self._test_epoch_start_ms = maka_control_timestamp_ms()

    def on_test_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        add_perf_data_point(
            PerfCategory.TRAINING, "test_time", "ms", maka_control_timestamp_ms() - self._test_epoch_start_ms
        )
        log_timing_metrics(self.logger)


def log_timing_metrics(logger: Any) -> None:
    if logger is not None:
        timing_metrics: Dict[str, Any] = {}
        add_timing_metrics(timing_metrics)
        logger.experiment.log(timing_metrics)


def add_timing_metrics(metrics_dict: Dict[str, Any]) -> Dict[str, Any]:
    try:
        timing_dict_raw = get_data_category(PerfCategory.TRAINING)

        timing_dict = {}
        for name, avg in sorted(timing_dict_raw.items()):
            timing_dict[f"avg_timing_{name}_s"] = avg.value / 1000
            timing_dict[f"total_timing_{name}_s"] = avg.total / 1000

        metrics_dict.update(timing_dict)
        return metrics_dict
    # In case nothing has been logged yet. The log calls are asynchronous.
    except KeyError:
        return metrics_dict


def get_compute_capability() -> str:
    major_version, minor_version = torch.cuda.get_device_capability()
    return f"{major_version}.{minor_version}"


def get_tensorrt_version() -> str:
    return cast(str, tensorrt.__version__)


def get_tensorrt_file_name(convert_int8: bool) -> str:
    tensorrt_version = get_tensorrt_version().replace(".", "")
    compute_capability = get_compute_capability().replace(".", "")
    return f"trt_{'int8' if convert_int8 else 'fp16'}_{tensorrt_version}_{compute_capability}.trt"


@retry(wait=wait_exponential(multiplier=1, min=60, max=5 * 60), stop=stop_after_delay(30 * 60))
def upload_file(root: str, file: str, dest_directory: str) -> None:
    s3 = boto3.client(service_name="s3")
    s3.upload_file(os.path.join(root, file), S3_BUCKET, os.path.join(dest_directory, file))


def upload_directory(
    source_dir: str, skip_extensions: Optional[Set[str]] = None, skip_dirs: Optional[Set[str]] = None,
) -> None:
    skip_extensions = skip_extensions if skip_extensions is not None else {"png", "wandb"}
    skip_dirs = skip_dirs if skip_dirs is not None else {"wandb"}
    for root, _, files in os.walk(source_dir):
        for file in files:
            if os.path.splitext(file)[1] in skip_extensions:
                continue
            root_plus_file = os.path.join(root, file)
            path_split = root_plus_file.split("/")

            intersection_dir = skip_dirs.intersection(path_split)

            if len(intersection_dir) > 0:
                continue
            root_minus_front = root.split("/data/deeplearning/models/")[-1]
            dest_directory = os.path.join("models", root_minus_front)

            LOG.info(
                f"Uploading file {os.path.join(root, file)} to S3: s3://{S3_BUCKET}/{os.path.join(dest_directory, file)} ..."
            )
            upload_file(root, file, dest_directory)


def compute_md5sum(path: str) -> str:
    with open(path, "rb") as f:
        m = hashlib.md5()
        m.update(f.read())
        return m.hexdigest()


class TrainingModule(pl.LightningModule, ABC):
    @abstractproperty
    def use_cases(self) -> List[ModelUseCase]:
        pass

    @abstractmethod
    def export_metadata(self) -> ModelMetadata:
        pass

    @abstractmethod
    def export_model(self) -> torch.nn.Module:
        pass

    @abstractmethod
    def export_torch_script(self) -> torch.jit.ScriptModule:
        pass

    @abstractmethod
    def set_exp_dir(self, exp_dir: str) -> None:
        pass

    @abstractmethod
    def set_experiment_url(self, experiment_url: str) -> None:
        pass

    def optimize_for_testing(self, best_checkpoint: Optional[str] = None) -> bool:
        return False

    @property
    def save_torchscript(self) -> bool:
        return False

    def are_embeddings_reduced_scaled(self) -> bool:
        return False

    def train_pumap(self) -> bool:
        return False


class Trainer:
    def __init__(self) -> None:
        logging.basicConfig()
        logging.getLogger().setLevel(logging.INFO)

        if not torch.distributed.is_initialized():
            # If the script is not launched with `torchrun`, set the necessary environment variables.
            if "LOCAL_RANK" not in os.environ:
                os.environ["LOCAL_RANK"] = "0"
                os.environ["RANK"] = "0"
                os.environ["WORLD_SIZE"] = "1"
                os.environ["MASTER_ADDR"] = "127.0.0.1"
                os.environ["MASTER_PORT"] = "0"
            local_rank = int(os.environ["LOCAL_RANK"])
            torch.cuda.set_device(local_rank)
            # Long timeout here is due to long dataset creation times. The default timeout is 30 minutes.
            torch.distributed.init_process_group(backend="cpu:gloo,cuda:nccl", timeout=datetime.timedelta(hours=24))

        os.environ["WANDB_SILENT"] = "true"

        self.veselka_client = VeselkaClient()
        self._container_id = os.getenv("CONTAINER_ID")
        self._container_version = os.getenv("CONTAINER_VERSION")

    def _infer(
        self,
        module: TrainingModule,
        resume_from: Optional[str] = None,
        fast_run: bool = False,
        trt_path: Optional[str] = None,
        evaluation_dir: Optional[str] = None,
        logger: Optional[Any] = None,
        strategy: str = "ddp_find_unused_parameters_false",
    ) -> None:
        if evaluation_dir is not None:
            exp_dir = self._find_exp_dir(evaluation_dir)
            module.set_exp_dir(exp_dir)
        if trt_path is not None:
            module.load_trt(trt_path)

        progress_bar = ProgressBar()
        callbacks = [progress_bar] if torch.distributed.get_rank() == 0 else []

        num_devices = min(torch.cuda.device_count(), torch.distributed.get_world_size())
        trainer = pl.Trainer(
            gradient_clip_val=5.0,
            max_epochs=0,
            enable_progress_bar=True,
            num_sanity_val_steps=0,
            callbacks=callbacks,
            accelerator="gpu",
            devices=num_devices,
            num_nodes=torch.distributed.get_world_size() // num_devices,
            strategy=strategy,
            limit_train_batches=1 if fast_run else None,
            limit_val_batches=1 if fast_run else None,
            limit_test_batches=1 if fast_run else None,
            enable_model_summary=False,
            inference_mode=False,
            logger=logger if logger is not None else False,
        )

        trainer.test(module, ckpt_path=resume_from)

    def initialize_pl_trainer(
        self,
        module: TrainingModule,
        fast_run: bool = False,
        epochs: int = 30,
        checkpoint_start_pct: int = 50,
        check_val_every_n_epoch: int = 1,
        precision: Optional[str] = None,
        sync_batchnorm: bool = False,
        logger: Optional[Any] = None,
        monitor_metric: str = "val_oec",
        strategy: str = "ddp_find_unused_parameters_false",
        ckpt_dir: str = "",
    ) -> pl.Trainer:
        checkpoint_callback = pl.callbacks.ModelCheckpoint(
            dirpath=ckpt_dir,
            save_top_k=0 if not fast_run else 1,
            verbose=True,
            monitor=monitor_metric,
            mode="max",
            filename="{epoch}",
        )

        if fast_run:
            checkpoint_start_epoch = 0
        else:
            checkpoint_start_epoch = int(epochs * checkpoint_start_pct / 100)

        enable_checkpoint_callback = EnableCheckpointCallback(
            checkpoint_callback, checkpoint_start_epoch=checkpoint_start_epoch
        )
        timing_callback = TimingCallback(logger=logger)
        save_last_checkpoint_callback = SaveLastCheckpointCallback(save_dir=ckpt_dir, training_module=module)
        progress_bar = ProgressBar()
        callbacks = [
            enable_checkpoint_callback,
            save_last_checkpoint_callback,
            checkpoint_callback,
            timing_callback,
            progress_bar,
        ]

        num_devices = min(torch.cuda.device_count(), torch.distributed.get_world_size())
        trainer = pl.Trainer(
            accelerator="gpu",
            devices=num_devices,
            num_nodes=torch.distributed.get_world_size() // num_devices,
            strategy=strategy,
            gradient_clip_val=5.0,
            max_epochs=epochs,
            logger=logger if logger is not None else False,
            enable_progress_bar=True,
            callbacks=callbacks,
            num_sanity_val_steps=0,
            check_val_every_n_epoch=1 if fast_run else check_val_every_n_epoch,
            log_every_n_steps=1,
            limit_train_batches=1 if fast_run else None,
            limit_val_batches=1 if fast_run else None,
            limit_test_batches=1 if fast_run else None,
            enable_model_summary=False,
            precision=precision if precision is not None else "32-true",
            inference_mode=False,
            sync_batchnorm=sync_batchnorm,
        )

        return trainer

    def get_exp_dir_and_logger(
        self,
        module: TrainingModule,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        checkpoint_dir: Optional[str] = None,
        wandb_project: str = "",
        logger: WandbLogger = None,
    ) -> Tuple[str, WandbLogger]:
        exp_dir = None
        if torch.distributed.get_rank() == 0:
            if log_experiment:
                exp_dir = self._find_exp_dir(checkpoint_dir)
                if logger is None:
                    logger = make_wandb_logger(
                        name=description, tags=safe_split(tags), project=wandb_project, exp_dir=exp_dir
                    )
            else:
                exp_dir = self._find_exp_dir(checkpoint_dir)

        exp_dir = broadcast_object(exp_dir)
        assert exp_dir is not None

        return exp_dir, logger

    def get_wandb_project(self, module: TrainingModule) -> str:
        wandb_project = None
        if torch.distributed.get_rank() == 0:
            wandb_project = WANDB_PROJECT_MAP[module.use_cases[0]]
            if self._ci_run:
                wandb_project = f"{wandb_project}_{CI_SUFFIX}"

            deepweed_config = getattr(module, "config", None)
            if deepweed_config is not None:
                wandb_project = deepweed_config.wandb_project

        wandb_project = broadcast_object(wandb_project)
        assert wandb_project is not None

        return wandb_project

    def get_best_checkpoint(self, ckpt_dir: str) -> Optional[str]:
        best_checkpoint: Optional[str] = None
        # Load the best checkpoint
        checkpoints = glob.glob(os.path.join(ckpt_dir, "epoch=*.ckpt"))
        if len(checkpoints) == 0:
            logging.info("No checkpoints found! Using starting weights for testing.")
        else:
            best_checkpoint = checkpoints[0]
            logging.info("Selected best checkpoint {}".format(best_checkpoint))

        return best_checkpoint

    def _train(  # noqa: C901
        self,
        module: TrainingModule,
        version: int,
        fast_run: bool = False,
        epochs: int = 30,
        checkpoint_start_pct: int = 50,
        resume_from: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        ci_run: bool = False,
        checkpoint_dir: Optional[str] = None,
        crop: Optional[str] = None,
        customer: Optional[str] = None,
        robot_name: Optional[str] = None,
        environment: Optional[Environment] = None,
        deploy: bool = False,
        sub_type: Optional[str] = None,
        pipeline_id: Optional[str] = None,
        crop_ids: Optional[List[str]] = None,
        parent_model_id: Optional[str] = None,
        dataset_id: Optional[str] = None,
        check_val_every_n_epoch: int = 1,
        followup_logging_fn: Optional[Callable[[Any, str], None]] = None,
        precision: Optional[str] = None,
        sync_batchnorm: bool = False,
        logger: Optional[Any] = None,
        monitor_metric: str = "val_oec",
        comparison_model_id: Optional[str] = None,
        strategy: str = "ddp_find_unused_parameters_false",
    ) -> Optional[str]:
        # Set up experiment
        ckpt_dir: Optional[str]
        self._ci_run = ci_run

        exp_dir = None
        if torch.distributed.get_rank() == 0:
            if log_experiment:
                wandb_project = WANDB_PROJECT_MAP[module.use_cases[0]]
                if self._ci_run:
                    wandb_project = f"{wandb_project}_{CI_SUFFIX}"

                deepweed_config = getattr(module, "config", None)
                if deepweed_config is not None:
                    wandb_project = deepweed_config.wandb_project

                exp_dir = self._find_exp_dir(checkpoint_dir)
                logger = make_wandb_logger(
                    name=description, tags=safe_split(tags), project=wandb_project, exp_dir=exp_dir
                )
                if dataset_id:
                    logger.experiment.config["veselka_dataset_id"] = dataset_id

                experiment_url = "https://app.wandb.ai/{}/{}/runs/{}".format(
                    os.environ["WANDB_ENTITY"], wandb_project, logger.version
                )
                module.set_experiment_url(experiment_url)
                if not ci_run:
                    # TODO: git_sha, location, training_docker_tag, snapshot_json
                    self._veselka_post_model(
                        model_id=logger.version,
                        model_type=WANDB_PROJECT_MAP[module.use_cases[0]],
                        description=description,
                        wandb_url=experiment_url,
                        version=version,
                        crop=crop,
                        customer=customer,
                        robot_name=robot_name,
                        environment=environment,
                        exp_dir=exp_dir,
                        deploy=deploy,
                        sub_type=sub_type,
                        pipeline_id=pipeline_id,
                        crop_ids=crop_ids,
                        parent_model_id=parent_model_id,
                        dataset_id=dataset_id,
                        comparison_model_id=comparison_model_id,
                    )
            else:
                exp_dir = self._find_exp_dir(checkpoint_dir)

        exp_dir = broadcast_object(exp_dir)
        assert exp_dir is not None
        module.set_exp_dir(exp_dir)

        ckpt_dir = os.path.join(exp_dir, "ckpt")
        os.makedirs(ckpt_dir, exist_ok=True)

        if torch.distributed.get_rank() == 0 and log_experiment and logger is not None:
            logger.experiment.config.update(
                {"container": {"container_id": self._container_id, "container_version": self._container_version}},
                allow_val_change=True,
            )

        trainer = self.initialize_pl_trainer(
            module=module,
            fast_run=fast_run,
            epochs=epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            check_val_every_n_epoch=check_val_every_n_epoch,
            precision=precision,
            sync_batchnorm=sync_batchnorm,
            logger=logger,
            monitor_metric=monitor_metric,
            strategy=strategy,
            ckpt_dir=ckpt_dir,
        )

        # Fit the model
        trainer.fit(module, ckpt_path=resume_from)

        best_checkpoint: Optional[str] = None
        # Load the best checkpoint
        ckpt_dir = broadcast_object(ckpt_dir)
        checkpoints = glob.glob(os.path.join(cast(str, ckpt_dir), "epoch=*.ckpt"))
        if len(checkpoints) == 0:
            logging.info("No checkpoints found! Using starting weights for testing.")
            best_checkpoint = resume_from
        else:
            best_checkpoint = checkpoints[0]
            logging.info("Selected best checkpoint {}".format(best_checkpoint))
        # Run test pass
        trainer.test(ckpt_path=best_checkpoint)

        if torch.distributed.get_rank() == 0 and log_experiment and best_checkpoint is not None and logger is not None:
            match = re.fullmatch(r"epoch=(\d*)\.ckpt", os.path.basename(best_checkpoint))
            if match is not None:
                logger.experiment.config.update(
                    {"best_epoch": int(match.group(1))}, allow_val_change=True,
                )

        if module.train_pumap():
            trainer.save_checkpoint(best_checkpoint)

        torch.distributed.barrier()

        if module.are_embeddings_reduced_scaled():
            trainer.test(ckpt_path=best_checkpoint)

        # Optimize for testing
        successful_optimization = module.optimize_for_testing()
        if successful_optimization:
            trainer.test(ckpt_path=best_checkpoint)

        if torch.distributed.get_rank() == 0 and followup_logging_fn is not None and logger is not None:
            try:
                followup_logging_fn(logger, exp_dir)
            except Exception as e:
                logging.error(f"Error logging: {e}")

        if torch.distributed.get_rank() == 0:
            # Save the best PyTorch checkpoint for possible conversion to TensorRT
            save_pytorch_model(
                module.export_model(), module.export_metadata(), os.path.join(exp_dir, "best_checkpoint.ckpt"),
            )

            if module.save_torchscript:
                save_torchscript_model(
                    module.export_torch_script(), module.export_metadata(), os.path.join(exp_dir, "torchscript.pt")
                )

            # Upload model files to S3
            if logger is not None and not self._ci_run:
                self._upload_s3(exp_dir)

            log_timing_metrics(logger)

            return exp_dir

        return None

    def _train_and_unoptimized_test(  # noqa: C901
        self,
        module: TrainingModule,
        version: int,
        fast_run: bool = False,
        epochs: int = 30,
        checkpoint_start_pct: int = 50,
        resume_from: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        ci_run: bool = False,
        checkpoint_dir: Optional[str] = None,
        crop: Optional[str] = None,
        customer: Optional[str] = None,
        robot_name: Optional[str] = None,
        environment: Optional[Environment] = None,
        deploy: bool = False,
        sub_type: Optional[str] = None,
        pipeline_id: Optional[str] = None,
        crop_ids: Optional[List[str]] = None,
        parent_model_id: Optional[str] = None,
        dataset_id: Optional[str] = None,
        check_val_every_n_epoch: int = 1,
        precision: Optional[str] = None,
        sync_batchnorm: bool = False,
        logger: Optional[Any] = None,
        monitor_metric: str = "val_oec",
        comparison_model_id: Optional[str] = None,
        strategy: str = "ddp_find_unused_parameters_false",
        geohash_prefix: Optional[str] = None,
    ) -> None:
        # Set up experiment
        wandb_project: Optional[str]
        self._ci_run = ci_run

        exp_dir = None

        wandb_project = self.get_wandb_project(module)
        exp_dir, logger = self.get_exp_dir_and_logger(
            module,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            wandb_project=wandb_project,
            logger=logger,
        )

        if torch.distributed.get_rank() == 0:
            if log_experiment:
                experiment_url = "https://app.wandb.ai/{}/{}/runs/{}".format(
                    os.environ["WANDB_ENTITY"], wandb_project, logger.version
                )
                if dataset_id:
                    logger.experiment.config["veselka_dataset_id"] = dataset_id
                module.set_experiment_url(experiment_url)
                if not ci_run:
                    # TODO: git_sha, location, training_docker_tag, snapshot_json
                    self._veselka_post_model(
                        model_id=logger.version,
                        model_type=WANDB_PROJECT_MAP[module.use_cases[0]],
                        description=description,
                        wandb_url=experiment_url,
                        version=version,
                        crop=crop,
                        customer=customer,
                        robot_name=robot_name,
                        environment=environment,
                        exp_dir=exp_dir,
                        deploy=deploy,
                        sub_type=sub_type,
                        pipeline_id=pipeline_id,
                        crop_ids=crop_ids,
                        parent_model_id=parent_model_id,
                        dataset_id=dataset_id,
                        comparison_model_id=comparison_model_id,
                        geohash_prefix=geohash_prefix,
                    )
        torch.distributed.barrier()

        module.set_exp_dir(exp_dir)
        ckpt_dir = os.path.join(exp_dir, "ckpt")
        os.makedirs(ckpt_dir, exist_ok=True)

        if torch.distributed.get_rank() == 0 and log_experiment:
            logger.experiment.config.update(
                {"container": {"container_id": self._container_id, "container_version": self._container_version}},
                allow_val_change=True,
            )

        trainer = self.initialize_pl_trainer(
            module=module,
            fast_run=fast_run,
            epochs=epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            check_val_every_n_epoch=check_val_every_n_epoch,
            precision=precision,
            sync_batchnorm=sync_batchnorm,
            logger=logger,
            monitor_metric=monitor_metric,
            strategy=strategy,
            ckpt_dir=ckpt_dir,
        )
        # Fit the model
        trainer.fit(module, ckpt_path=resume_from)

        best_checkpoint = self.get_best_checkpoint(ckpt_dir)
        if best_checkpoint is None and resume_from is not None:
            best_checkpoint = resume_from

        # Run test pass
        trainer.test(ckpt_path=best_checkpoint)
        torch.distributed.barrier()

        if torch.distributed.get_rank() == 0 and log_experiment and best_checkpoint is not None:
            match = re.fullmatch(r"epoch=(\d*)\.ckpt", os.path.basename(best_checkpoint))
            if match is not None:
                logger.experiment.config.update(
                    {"best_epoch": int(match.group(1))}, allow_val_change=True,
                )

        self.save_best_checkpoint(module, exp_dir)

    def save_best_checkpoint(self, module: TrainingModule, exp_dir: str) -> None:
        if torch.distributed.get_rank() == 0:
            # Save the best PyTorch checkpoint for possible conversion to TensorRT
            save_pytorch_model(
                module.export_model(), module.export_metadata(), os.path.join(exp_dir, "best_checkpoint.ckpt"),
            )

            if module.save_torchscript:
                save_torchscript_model(
                    module.export_torch_script(), module.export_metadata(), os.path.join(exp_dir, "torchscript.pt")
                )
        torch.distributed.barrier()

    def _train_pumap(
        self,
        module: TrainingModule,
        fast_run: bool = False,
        epochs: int = 30,
        checkpoint_start_pct: int = 50,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        checkpoint_dir: Optional[str] = None,
        check_val_every_n_epoch: int = 1,
        precision: Optional[str] = None,
        sync_batchnorm: bool = False,
        logger: Optional[Any] = None,
        monitor_metric: str = "val_oec",
        strategy: str = "ddp_find_unused_parameters_false",
    ) -> None:
        wandb_project = self.get_wandb_project(module)

        exp_dir, logger = self.get_exp_dir_and_logger(
            module,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            wandb_project=wandb_project,
        )

        module.set_exp_dir(exp_dir)

        ckpt_dir = os.path.join(exp_dir, "ckpt")
        os.makedirs(ckpt_dir, exist_ok=True)

        trainer = self.initialize_pl_trainer(
            module=module,
            fast_run=fast_run,
            epochs=epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            check_val_every_n_epoch=check_val_every_n_epoch,
            precision=precision,
            sync_batchnorm=sync_batchnorm,
            logger=logger,
            monitor_metric=monitor_metric,
            strategy=strategy,
            ckpt_dir=ckpt_dir,
        )

        best_checkpoint = os.path.join(exp_dir, "best_checkpoint.ckpt")

        if module.train_pumap():
            trainer.save_checkpoint(best_checkpoint)

        torch.distributed.barrier()

    def _test_reduced_scaled(
        self,
        module: TrainingModule,
        fast_run: bool = False,
        epochs: int = 30,
        checkpoint_start_pct: int = 50,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        checkpoint_dir: Optional[str] = None,
        check_val_every_n_epoch: int = 1,
        precision: Optional[str] = None,
        sync_batchnorm: bool = False,
        logger: Optional[Any] = None,
        monitor_metric: str = "val_oec",
        strategy: str = "ddp_find_unused_parameters_false",
    ) -> None:
        wandb_project = self.get_wandb_project(module)

        exp_dir, logger = self.get_exp_dir_and_logger(
            module,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            wandb_project=wandb_project,
        )

        module.set_exp_dir(exp_dir)

        ckpt_dir = os.path.join(exp_dir, "ckpt")
        os.makedirs(ckpt_dir, exist_ok=True)

        trainer = self.initialize_pl_trainer(
            module=module,
            fast_run=fast_run,
            epochs=epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            check_val_every_n_epoch=check_val_every_n_epoch,
            precision=precision,
            sync_batchnorm=sync_batchnorm,
            logger=logger,
            monitor_metric=monitor_metric,
            strategy=strategy,
            ckpt_dir=ckpt_dir,
        )

        best_checkpoint = os.path.join(exp_dir, "best_checkpoint.ckpt")

        if module.are_embeddings_reduced_scaled():
            trainer.test(ckpt_path=best_checkpoint)

    def _optimize_and_test(
        self,
        module: TrainingModule,
        fast_run: bool = False,
        epochs: int = 30,
        checkpoint_start_pct: int = 50,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        checkpoint_dir: Optional[str] = None,
        check_val_every_n_epoch: int = 1,
        followup_logging_fn: Optional[Callable[[Any, str], None]] = None,
        precision: Optional[str] = None,
        sync_batchnorm: bool = False,
        logger: Optional[Any] = None,
        monitor_metric: str = "val_oec",
        strategy: str = "ddp_find_unused_parameters_false",
        ci_run: bool = False,
    ) -> Optional[str]:
        wandb_project: Optional[str]
        self._ci_run = ci_run
        wandb_project = self.get_wandb_project(module)
        exp_dir, logger = self.get_exp_dir_and_logger(
            module,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            wandb_project=wandb_project,
            logger=logger,
        )
        module.set_exp_dir(exp_dir)
        if torch.distributed.get_rank() == 0:
            experiment_url = "https://app.wandb.ai/{}/{}/runs/{}".format(
                os.environ["WANDB_ENTITY"], wandb_project, logger.version
            )
            module.set_experiment_url(experiment_url)

        ckpt_dir = os.path.join(exp_dir, "ckpt")
        os.makedirs(ckpt_dir, exist_ok=True)

        trainer = self.initialize_pl_trainer(
            module=module,
            fast_run=fast_run,
            epochs=epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            check_val_every_n_epoch=check_val_every_n_epoch,
            precision=precision,
            sync_batchnorm=sync_batchnorm,
            logger=logger,
            monitor_metric=monitor_metric,
            strategy=strategy,
            ckpt_dir=ckpt_dir,
        )

        best_checkpoint = os.path.join(exp_dir, "best_checkpoint.ckpt")
        successful_optimization = module.optimize_for_testing(best_checkpoint)

        if successful_optimization:
            trainer.test(model=module)
        if torch.distributed.get_rank() == 0 and followup_logging_fn is not None and logger is not None:
            try:
                followup_logging_fn(logger, exp_dir)
            except Exception as e:
                logging.error(f"Error logging: {e}")

        if torch.distributed.get_rank() == 0:
            # Upload model files to S3
            if logger is not None and not self._ci_run:
                self._upload_s3(exp_dir)

            log_timing_metrics(logger)

            return exp_dir

        return None

    @retry(wait=wait_fixed(10), stop=stop_after_attempt(6))
    def _veselka_post_model(  # noqa: C901
        self,
        model_id: str,
        url: Optional[str] = None,
        customer: Optional[str] = None,
        crop: Optional[str] = None,
        robot_name: Optional[str] = None,
        version: Optional[int] = None,
        training_docker_tag: Optional[str] = None,
        git_sha: Optional[str] = None,
        checksum: Optional[str] = None,
        location: Optional[str] = None,
        trained_at: Optional[int] = None,
        model_type: Optional[str] = None,
        description: Optional[str] = None,
        metadata_json: Optional[str] = None,
        test_results_json: Optional[str] = None,
        wandb_json: Optional[str] = None,
        wandb_url: Optional[str] = None,
        snapshot_json: Optional[str] = None,
        is_good_to_deploy: Optional[bool] = None,
        environment: Optional[Environment] = None,
        exp_dir: Optional[str] = None,
        deploy: Optional[bool] = None,
        weed_point_threshold: Optional[float] = None,
        crop_point_threshold: Optional[float] = None,
        is_pretraining: Optional[bool] = None,
        dataset_id: Optional[str] = None,
        sub_type: Optional[str] = None,
        pipeline_id: Optional[str] = None,
        crop_ids: Optional[List[str]] = None,
        parent_model_id: Optional[str] = None,
        recency_split: Optional[int] = None,
        geohash_prefix: Optional[str] = None,
        family_id: Optional[str] = None,
        comparison_model_id: Optional[str] = None,
        dl_config: Optional[Dict[str, Any]] = None,
    ) -> None:

        data: Dict[str, Any] = {"id": model_id}
        if url is not None:
            data["url"] = url
            data["compute_capability"] = get_compute_capability()
            data["tensorrt_version"] = get_tensorrt_version()
        if customer is not None:
            data["customer"] = customer.lower()
        if crop is not None and crop_ids is None:
            data["crop"] = crop.lower()
        if robot_name is not None:
            data["robot_name"] = robot_name.lower()
        if version is not None:
            data["version"] = version
        if training_docker_tag is not None:
            data["training_docker_tag"] = training_docker_tag
        if git_sha is not None:
            data["git_sha"] = git_sha
        if checksum is not None:
            data["checksum"] = checksum
        if location is not None:
            data["location"] = location.lower()
        if trained_at is not None:
            data["trained_at"] = trained_at
        if model_type is not None:
            data["type"] = model_type
        if description is not None:
            data["description"] = description
        if metadata_json is not None:
            data["metadata_json"] = metadata_json

            if json.loads(metadata_json).get("trained_embeddings"):
                data["embedding_version"] = EMBEDDING_VERSION
        if test_results_json is not None:
            data["test_results_json"] = test_results_json
        if wandb_json is not None:
            data["wandb_json"] = wandb_json
        if wandb_url is not None:
            data["wandb_url"] = wandb_url
        if snapshot_json is not None:
            data["snapshot_json"] = snapshot_json
        if is_good_to_deploy is not None:
            data["is_good_to_deploy"] = is_good_to_deploy
        if environment is not None:
            data["environment"] = environment.name.lower()
        if deploy is not None:
            data["deploy"] = deploy
        if weed_point_threshold is not None:
            data["weed_point_threshold"] = weed_point_threshold
        if crop_point_threshold is not None:
            data["crop_point_threshold"] = crop_point_threshold
        if is_pretraining is not None:
            data["is_pretraining"] = is_pretraining
        if dataset_id is not None:
            data["dataset_id"] = dataset_id
        if sub_type is not None:
            data["sub_type"] = sub_type
        if self._container_id is not None:
            data["container_id"] = self._container_id
        if self._container_version is not None:
            data["container_version"] = self._container_version
        if pipeline_id is not None:
            data["pipeline_id"] = pipeline_id
        if crop_ids is not None:
            data["crop_ids"] = crop_ids
        if parent_model_id is not None:
            data["parent_model_id"] = parent_model_id
        if recency_split is not None:
            data["recency_split"] = recency_split
        if geohash_prefix is not None:
            data["geohash_prefix"] = geohash_prefix
        if family_id is not None:
            data["family_id"] = family_id
        if comparison_model_id is not None:
            data["comparison_model_id"] = comparison_model_id
        if dl_config is not None:
            data["dl_config"] = dl_config

        self.veselka_client.post_model(data)

        if exp_dir is not None:
            os.makedirs(exp_dir, exist_ok=True)

            veselka_metadata_filepath = os.path.join(exp_dir, "veselka-metadata.json")

            if os.path.exists(veselka_metadata_filepath):
                with open(veselka_metadata_filepath) as f:
                    old_data = json.load(f)
                data.update(old_data)

            with open(veselka_metadata_filepath, "w") as f:
                json.dump(data, f)

            self._force_upload_s3(model_id, exp_dir, "veselka-metadata.json")

    def _find_exp_dir(self, name: Optional[str] = None) -> str:
        if name is None:
            md5 = hashlib.md5()
            md5.update(struct.pack("d", time.time()))
            exp_dir = os.path.join("lightning_logs", md5.hexdigest())
        else:
            exp_dir = os.path.join("lightning_logs", name)
        os.makedirs(exp_dir, exist_ok=True)
        return exp_dir

    def _upload_s3(
        self, exp_dir: str, skip_extensions: Optional[Set[str]] = None, skip_dirs: Optional[Set[str]] = None
    ) -> None:
        skip_extensions = skip_extensions if skip_extensions is not None else {"png"}
        skip_dirs = skip_dirs if skip_dirs is not None else {"wandb"}
        upload_directory(exp_dir, skip_extensions=skip_extensions, skip_dirs=skip_dirs)

    def _force_upload_s3(self, version: str, exp_dir: str, filename: str) -> None:
        s3 = boto3.client(service_name="s3")
        dest_path = os.path.join("models", version, filename)
        logging.info(f"Uploading to S3: s3://{S3_BUCKET}/{dest_path} ...")
        s3.upload_file(os.path.join(exp_dir, filename), S3_BUCKET, dest_path)

    @classmethod
    def main(cls: Any) -> None:
        try:
            fire.Fire(cls)
        except:  # noqa
            if os.getenv("TRAINER_DEBUG"):
                if torch.distributed.get_rank() == 0:
                    import pdb
                    import sys
                    import traceback

                    extype, value, tb = sys.exc_info()
                    traceback.print_exc()
                    pdb.post_mortem(tb)
            else:
                raise


def conv_2d_backward(
    input: torch.Tensor,
    grad_output: torch.Tensor,
    in_channels: int,
    out_channels: int,
    kernel_size: Union[int, Tuple[int, ...]],
    stride: Union[int, Tuple[int, ...]],
    dilation: Union[int, Tuple[int, ...]],
    padding: Union[int, Tuple[int, ...]],
    groups: int,
) -> torch.Tensor:

    # Using the following two links:
    # https://arxiv.org/pdf/1912.06015.pdf
    # https://github.com/owkin/grad-cnns/blob/master/code/gradcnn/crb_backward.py#L25

    nd = 2
    if isinstance(kernel_size, int):
        kernel_size = (kernel_size,) * nd
    if isinstance(stride, int):
        stride = (stride,) * nd
    if isinstance(dilation, int):
        dilation = (dilation,) * nd
    if isinstance(padding, int):
        padding = (padding,) * nd

    # Get some useful sizes
    batch_size = input.size(0)
    input_shape = input.size()[-nd:]
    output_shape = grad_output.size()[-nd:]

    # Reshape to extract groups from the convolutional layer
    # Channels are seen as an extra spatial dimension with kernel size 1
    input_conv = input.view(1, batch_size * groups, in_channels // groups, *input_shape)

    # Compute convolution between input and output; the batchsize is seen
    # as channels, taking advantage of the `groups` argument
    grad_output_conv = grad_output.view(-1, 1, 1, *output_shape)

    stride = (1, *stride)
    dilation = (1, *dilation)
    padding = (0, *padding)

    s_ = np.s_[..., : kernel_size[0], : kernel_size[1]]

    conv = F.conv3d(
        input_conv, grad_output_conv, groups=batch_size * groups, stride=dilation, dilation=stride, padding=padding
    )

    # Because of rounding shapes when using non-default stride or dilation,
    # convolution result must be truncated to convolution kernel size
    conv = conv[s_]

    # Reshape weight gradient to correct shape
    new_shape = [batch_size, out_channels, in_channels // groups, *kernel_size]
    weight_bgrad = conv.view(*new_shape).contiguous()

    return weight_bgrad


def get_examples_with_highest_last_n_epochs(
    n: int, gradient_norms: Dict[str, List[Tuple[int, float]]]
) -> List[Tuple[str, float]]:
    means = []
    for key, value in gradient_norms.items():
        avg_value = float(np.mean([v[1] for v in value if v[0] >= n]))
        means.append((key, avg_value))

    means.sort(reverse=True, key=lambda x: x[1])
    descending_keys = [(v[0], float(v[1])) for v in means]
    return descending_keys


def worker_init_fn(num_workers: int, worker_id: int) -> None:
    worker_info = get_worker_info()

    if worker_info is not None and hasattr(worker_info.dataset, "set_seed"):
        seed = torch.distributed.get_rank() * num_workers + worker_id
        worker_info.dataset.set_seed(seed)

from typing import List, Tuple, cast

import torch
import torch.nn.functional as F


def _tile_crop_coord(size: int, crop_size: int, tile: int) -> List[int]:
    result = []

    # forward crops
    origin = 0
    while origin + crop_size <= size:
        if origin > size / 2:
            break
        result.append(origin)
        origin += tile

    # reverse crops
    origin = size - crop_size
    while origin >= 0:
        if origin + crop_size < size / 2:
            break
        result.append(origin)
        origin -= tile

    return sorted(list(set(result)))


def tile_crop_origins(
    *, size: Tuple[int, int], crop_size: Tuple[int, int], tile: Tuple[int, int]
) -> List[Tuple[int, int]]:
    height, width = size
    crop_height, crop_width = crop_size
    tile_height, tile_width = tile

    crop_origins = []
    for y in _tile_crop_coord(size=height, crop_size=crop_height, tile=tile_height):
        for x in _tile_crop_coord(size=width, crop_size=crop_width, tile=tile_width):
            crop_origins.append((y, x))
    return crop_origins


def tile_crop(tensor: torch.Tensor, crop_origins: List[Tuple[int, int]], crop_size: Tuple[int, int]) -> torch.Tensor:
    crop_height, crop_width = crop_size
    return torch.cat(
        [
            tensor[:, :, origin_y : origin_y + crop_height, origin_x : origin_x + crop_width]
            for (origin_y, origin_x) in crop_origins
        ],
        dim=0,
    )


def interpolate(input: torch.Tensor, size: Tuple[int, int]) -> torch.Tensor:
    assert len(input.shape) == 4, f"Expected 4D input: {input.shape}"
    if size[0] < input.shape[2] and size[1] < input.shape[3]:
        return cast(torch.Tensor, F.interpolate(input, size, mode="area"))
    else:
        return cast(torch.Tensor, F.interpolate(input, size, mode="bilinear", align_corners=False))

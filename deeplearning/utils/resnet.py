import logging
import os
from typing import cast

import boto3
import requests
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential

RESNET50_WEIGHTS_KEY = "pretrained_weights/resnet50-11ad3fa6.pth"
RESNET50_WEIGHTS_BUCKET = "maka-build-artifacts"

LOG = logging.getLogger(__name__)


def get_resnet50_weights_url() -> str:
    if not os.getenv("DISABLE_S3_CACHE_PROXY"):
        carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")

        return f"http://{carbon_cache_host}/{RESNET50_WEIGHTS_BUCKET}/{RESNET50_WEIGHTS_KEY}"
    else:
        s3 = boto3.client("s3")
        presigned_url = s3.generate_presigned_url(
            "get_object", Params={"Bucket": RESNET50_WEIGHTS_BUCKET, "Key": RESNET50_WEIGHTS_KEY}, ExpiresIn=600
        )
        return cast(str, presigned_url)


@retry(
    wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
    stop=stop_after_delay(30 * 60),
    before_sleep=before_sleep_log(LOG, logging.INFO),
)
def download_weights_to_cache(url: str) -> None:
    path = f"{os.path.expanduser('~')}/.cache/torch/hub/checkpoints/resnet50-11ad3fa6.pth"
    if os.path.exists(path):
        return
    LOG.info("Attempting to pre-download weights")
    response = requests.get(url)
    assert response.ok, f"{response.status_code}: {response.text}"
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, "wb") as f:
        f.write(response.content)
        LOG.info("Succeeded writing weights to local cache")

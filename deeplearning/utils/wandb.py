import os
from typing import <PERSON><PERSON>

from deeplearning.utils.use_cases import ModelUseCase

WANDB_PROJECT_MAP = {
    ModelUseCase.PREDICT: "deepweed",
    ModelUseCase.TARGET: "target_safety",
    ModelUseCase.DRIVING: "furrows",
    ModelUseCase.P2P: "p2p",
    ModelUseCase.COMPARISON: "comparison",
    ModelUseCase.PARAMETRIC_UMAP: "parametric_umap",
}


def get_wandb_metadata_json_str(experiment_directory: str) -> Optional[str]:
    wandb_metadata_filepath = os.path.join(experiment_directory, "wandb", "latest-run", "files", "wandb-metadata.json")

    wandb_json: Optional[str] = None
    if os.path.exists(wandb_metadata_filepath):
        with open(wandb_metadata_filepath, "r") as f:
            wandb_json = f.read()

    return wandb_json

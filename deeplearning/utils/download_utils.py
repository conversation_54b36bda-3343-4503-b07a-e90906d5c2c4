import concurrent.futures
import logging
import os
from pathlib import Path
from typing import Optional, Set

import boto3
import torch
import tqdm

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.utils.dataset import get_carbon_cache_host
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

LOG = logging.getLogger(__name__)


def download_single_record(carbon_cache_host: Optional[str], bucket_name: str, filepath: str, object_key: str) -> None:
    client = S3CacheProxyClient(carbon_cache_host, timeout=30)
    client.download(bucket_name, object_key, filepath, make_dirs=True, exist_ok=True)


def download_resume_model(resume_from: Optional[str]) -> None:
    if not resume_from:
        return

    if torch.distributed.get_rank() == 0:
        LOG.info(f"Resuming training using model: {resume_from}.")
        model_id = Path(resume_from).parents[1].name
        download_records(item_id=model_id)

    torch.distributed.barrier()


def download_records(
    item_id: str,
    s3_directory: str = "models",
    save_dir: Optional[str] = None,
    include_points_db: bool = False,
    skip_existing_files: bool = False,
    bucket: str = "maka-pono",
    filename_filters: Optional[Set[str]] = None,
    only_use_basename_in_download: bool = False,
) -> None:
    """Downloads all information saved from a previously trained model.

    Args:
        item_id (str): The id to uniquely identify what we should be downloading.
    """
    assert item_id != ""
    if save_dir is None:
        save_dir = os.path.join(CARBON_DATA_DIR, "deeplearning")
    prefix = f"{s3_directory}/{item_id}"

    carbon_cache_host = get_carbon_cache_host()
    skip_count = 0

    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        s3_bucket = boto3.resource("s3").Bucket(bucket)
        futures = []
        for object in s3_bucket.objects.filter(Prefix=prefix):
            if "/wandb/" in object.key or ((not include_points_db) and "points_v2.db" in object.key):
                # Skip W&B artifacts like images
                continue

            fname = object.key
            if object.size == 0:
                continue
            if only_use_basename_in_download:
                fname = os.path.basename(fname)
            filepath = os.path.join(save_dir, fname)
            if os.path.exists(filepath) and skip_existing_files:
                continue

            if filename_filters is not None:
                if os.path.basename(object.key) not in filename_filters:
                    skip_count += 1
                    continue
            if not os.path.exists(os.path.dirname(filepath)):
                os.makedirs(os.path.dirname(filepath))

            futures.append(executor.submit(download_single_record, carbon_cache_host, bucket, filepath, object.key))

        number_completed = 0
        with tqdm.tqdm(total=len(futures)) as pbar:
            for future in concurrent.futures.as_completed(futures):
                pbar.update(1)
                try:
                    future.result()
                    number_completed += 1
                except Exception as e:
                    raise e
                if number_completed % 1000 == 0:
                    logging.info(f"Downloaded {number_completed} of {len(futures)}")

        logging.info(f"Downloading completed. Skipped {skip_count} files because they weren't in the filename_filter")

    return None

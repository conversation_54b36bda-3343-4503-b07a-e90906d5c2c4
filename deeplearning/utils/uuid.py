from typing import Any, Dict


def check_if_uuid_format(id: str) -> bool:
    return len(id) == 36 and id[8] == "-" and id[13] == "-" and id[18] == "-" and id[23] == "-"


def make_unique_id(image_id: str, id: str) -> str:
    return f"{image_id}-{id}"


def get_globally_unique_id(annotation_dict: Dict[str, Any]) -> str:
    return str(
        annotation_dict["id"]
        if check_if_uuid_format(str(annotation_dict["id"]))
        else make_unique_id(annotation_dict["image_id"], str(annotation_dict["id"]))
    )

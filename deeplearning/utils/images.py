import math
import random
from typing import Any, Dict, Optional, Tuple

import cv2
import numpy as np
import numpy.typing as npt
import torch
import torch.nn.functional as F

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

# First 3 values correspond to Imagenet, the last is to standardize the depth layer if it exists
IMAGENET_MEANS = [0.485, 0.456, 0.406, 0.424]
IMAGENET_STDS = [0.229, 0.224, 0.225, 0.322]

DEFAULT_WIDTH = 1800
DEFAULT_HEIGHT = 1696
DEFAULT_TRAIN_WIDTH = 1800
DEFAULT_TRAIN_HEIGHT = 1696


def denormalize_image(img: torch.Tensor) -> torch.Tensor:
    img_dev = img.device

    n = img.shape[0]
    std = IMAGENET_STDS[:n]
    mean = IMAGENET_MEANS[:n]

    stds = torch.tensor(std).to(img_dev).reshape((n, 1, 1))
    means = torch.tensor(mean).to(img_dev).reshape((n, 1, 1))
    return img * stds + means


def crop_or_pad(
    image: torch.Tensor, coord: Tuple[float, float], size: Tuple[int, int]
) -> Tuple[torch.Tensor, float, float]:
    # extra padding with half crop on all edges
    width, height = size
    image_padded = F.pad(
        image,
        [
            # padding for width
            math.floor(width / 2),
            math.ceil(width / 2),
            # padding for height
            math.floor(height / 2),
            math.ceil(height / 2),
        ],
    )
    point_x, point_y = int(coord[0]), int(coord[1])
    image_crop = image_padded[
        ..., point_y : point_y + height, point_x : point_x + width,
    ]
    assert image_crop.shape[-1] == width, f"Wrong crop shape: {image_crop.shape}"
    assert image_crop.shape[-2] == height, f"Wrong crop shape: {image_crop.shape}"

    cropped_padded_to_original_shift_x = point_x - math.floor(width / 2)
    cropped_padded_to_original_shift_y = point_y - math.floor(height / 2)

    return image_crop, cropped_padded_to_original_shift_x, cropped_padded_to_original_shift_y


def _random_sample_around_one(minval: float, maxval: float) -> float:
    assert minval <= 1.0
    assert maxval >= 1.0

    sample = random.uniform(-1, 1)
    if sample < 0:
        # Rescale from (-1, 0) to (minval-1, 0)
        sample *= 1 - minval
        # Rescale from (minval-1, 0) to (minval, 1)
        sample += 1
    else:
        # Rescale from (0, 1) to (0, maxval-1)
        sample *= maxval - 1
        # Rescale from (0, maxval-1) to (1, maxval)
        sample += 1
    return sample


def random_color_jitter(
    input: torch.Tensor,
    brightness: Tuple[float, float],
    saturation: Tuple[float, float],
    hue: Tuple[float, float],
    gamma: Tuple[float, float],
) -> torch.Tensor:
    def adjust_brightness_(image: npt.NDArray[np.float64]) -> None:
        image[:, :, 2] = (image[:, :, 2] * _random_sample_around_one(*brightness)).clip(0.0, 1.0)

    def adjust_saturation_(image: npt.NDArray[np.float64]) -> None:
        image[:, :, 1] = (image[:, :, 1] * _random_sample_around_one(*saturation)).clip(0.0, 1.0)

    def adjust_hue_(image: npt.NDArray[np.float64]) -> None:
        image[:, :, 0] = np.fmod(image[:, :, 0] + random.uniform(*hue), 360.0)

    def adjust_gamma_(image: npt.NDArray[np.float64]) -> None:
        image[:, :, 2] = image[:, :, 2] ** _random_sample_around_one(*gamma)

    input_np = input.numpy().transpose(1, 2, 0)
    input_hsv = cv2.cvtColor(input_np, cv2.COLOR_RGB2HSV)
    transforms = [adjust_brightness_, adjust_saturation_, adjust_hue_, adjust_gamma_]
    random.shuffle(transforms)
    for t in transforms:
        t(input_hsv)
    input_np = cv2.cvtColor(input_hsv, cv2.COLOR_HSV2RGB)
    ret_input = torch.tensor(input_np.transpose(2, 0, 1))
    return ret_input


def random_gaussian_noise(
    input: torch.Tensor, stddev: float, transforms_dict: Optional[Dict[str, Any]] = None
) -> Tuple[torch.Tensor, Dict[str, Any]]:
    transforms_dict = transforms_dict if transforms_dict is not None else {}
    noise = torch.randn((1, 1024 * 1024), device=input.device).expand(1024, -1)
    transforms_dict["random_gaussian_noise_shape"] = noise.shape

    num_buckets = math.ceil(input.numel() / transforms_dict["random_gaussian_noise_shape"][1]) + 1
    # this makes approximately input-sized copy of the random noise (with 1MB of extra noise)
    noise_for_input = noise[:num_buckets].reshape((-1))
    transforms_dict["offset"] = transforms_dict.get(
        "offset", random.randint(0, noise_for_input.numel() - input.numel())
    )  # Use the previous condition if exists else re-determine.

    input -= 0.5  # [-0.5, 0.5]
    input *= 2  # [-1, 1]
    input += (
        noise_for_input[transforms_dict["offset"] : input.numel() + transforms_dict["offset"]].reshape_as(input)
        * stddev
    )
    input /= 2  # [-0.5, 0.5]
    input += 0.5  # [0, 1]
    input = input.clamp(0, 1)

    return input, transforms_dict

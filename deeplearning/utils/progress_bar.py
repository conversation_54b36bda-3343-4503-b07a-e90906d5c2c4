import enum
import logging
import typing

import lightning.pytorch.callbacks
import torch
from lightning.pytorch import LightningModule, Trainer

from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)


class Mode(enum.Enum):
    TRAINING = 0
    VALIDATION = 1
    TESTING = 2
    SANITY_CHECK = 3


class ProgressBar(lightning.pytorch.callbacks.ProgressBar):
    def __init__(self) -> None:
        super().__init__()
        self.enabled = True
        self.mode = Mode.TRAINING
        self.end_times: typing.List[float] = []

    def enable(self) -> None:
        self.enabled = True

    def disable(self) -> None:
        self.enabled = False

    def print_message(self, trainer: Trainer, pl_module: LightningModule, batch_idx: int) -> None:
        metrics = self.get_metrics(trainer, pl_module)

        if self.mode == Mode.TRAINING:
            message = f"EPOCH {self.trainer.current_epoch:03d}"
            message += f", TRAINING {batch_idx:04d} / {self.total_train_batches:04d}"

        if self.mode == Mode.VALIDATION:
            message = f"EPOCH {self.trainer.current_epoch:03d}"
            message += f", VALIDATION {batch_idx:04d} / {self.total_val_batches:04d}"

        if self.mode == Mode.TESTING:
            message = f"EPOCH {self.trainer.current_epoch:03d}"
            message += f", TESTING {batch_idx:04d} / {len(pl_module.test_dataloader()):04d}"

        batch_delta_seconds = (self.batch_stop_time - self.batch_start_time) / 1000
        message += f", Rank 0 batch took {batch_delta_seconds:.3f} seconds"

        model_elapsed_time = metrics.get("model_elapsed_time", None)
        if model_elapsed_time is not None:
            model_elapsed_time = model_elapsed_time / 1000
        message += f", Rank 0 model took {model_elapsed_time} seconds"

        for key, value in metrics.items():
            if "loss" in key:
                message += f", {key}={float(value):08.4f}"

        if self.enabled:
            LOG.info(message)

    def on_train_batch_start(
        self, trainer: Trainer, pl_module: LightningModule, batch: typing.Any, batch_idx: int
    ) -> None:
        super().on_train_batch_start(trainer, pl_module, batch, batch_idx)
        self.batch_start_time = maka_control_timestamp_ms()

    def on_validation_batch_start(
        self, trainer: Trainer, pl_module: LightningModule, batch: typing.Any, batch_idx: int, dataloader_idx: int = 0
    ) -> None:
        super().on_validation_batch_start(trainer, pl_module, batch, batch_idx, dataloader_idx)
        self.batch_start_time = maka_control_timestamp_ms()

    def on_train_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: typing.List[torch.Tensor],
        batch: typing.List[torch.Tensor],
        batch_idx: int,
    ) -> None:
        super().on_train_batch_end(trainer, pl_module, outputs, batch, batch_idx)
        self.batch_stop_time = maka_control_timestamp_ms()
        self.print_message(trainer, pl_module, batch_idx)

    def on_validation_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: typing.List[torch.Tensor],
        batch: typing.List[torch.Tensor],
        batch_idx: int,
    ) -> None:
        super().on_validation_batch_end(trainer, pl_module, outputs, batch, batch_idx)
        self.batch_stop_time = maka_control_timestamp_ms()
        self.print_message(trainer, pl_module, batch_idx)

    def on_test_batch_start(
        self, trainer: Trainer, pl_module: LightningModule, batch: typing.Any, batch_idx: int, dataloader_idx: int = 0
    ) -> None:
        super().on_test_batch_start(trainer, pl_module, batch, batch_idx, dataloader_idx)
        self.batch_start_time = maka_control_timestamp_ms()

    def on_test_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: typing.List[torch.Tensor],
        batch: typing.List[torch.Tensor],
        batch_idx: int,
    ) -> None:
        super().on_test_batch_end(trainer, pl_module, outputs, batch, batch_idx)
        self.batch_stop_time = maka_control_timestamp_ms()
        self.print_message(trainer, pl_module, batch_idx)

    def on_train_start(self, trainer: Trainer, pl_module: LightningModule) -> None:
        super().on_train_start(trainer, pl_module)
        self.mode = Mode.TRAINING
        self.end_times = []

    def on_validation_start(self, trainer: Trainer, pl_module: LightningModule) -> None:
        super().on_validation_start(trainer, pl_module)
        self.mode = Mode.VALIDATION
        self.end_times = []

    def on_test_start(self, trainer: Trainer, pl_module: LightningModule) -> None:
        super().on_test_start(trainer, pl_module)
        self.mode = Mode.TESTING
        self.end_times = []

    def on_train_end(self, trainer: Trainer, pl_module: LightningModule) -> None:
        super().on_train_end(trainer, pl_module)
        self.mode = Mode.TRAINING

    def on_validation_end(self, trainer: Trainer, pl_module: LightningModule) -> None:
        super().on_validation_end(trainer, pl_module)
        self.mode = Mode.TRAINING

    def on_test_end(self, trainer: Trainer, pl_module: LightningModule) -> None:
        super().on_test_end(trainer, pl_module)
        self.mode = Mode.TRAINING

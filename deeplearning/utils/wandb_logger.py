import os
from typing import Any, List, Optional

from lightning.pytorch.loggers import Wandb<PERSON>ogger
from wandb import Settings


def make_wandb_logger(
    name: Optional[str], tags: Optional[List[str]], project: str, exp_dir: str, **kwargs: Any
) -> WandbLogger:
    # Make W&B happy, it needs directory pre-created
    os.makedirs(os.path.join(exp_dir, "wandb"), exist_ok=True)
    if tags:
        tags = [x for x in tags if x]
    logger = WandbLogger(
        save_dir=exp_dir, dir=exp_dir, id=os.path.basename(exp_dir), name=name, project=project, tags=tags, **kwargs
    )
    # Explicitly create experiment
    logger.experiment
    # Make artifact upload work with spawning child processes
    os.environ["WANDB_DIR"] = exp_dir
    return logger


def make_primary_wandb_logger(
    name: Optional[str], tags: Optional[List[str]], project: str, exp_dir: str
) -> WandbLogger:
    logger = make_wandb_logger(name, tags, project, exp_dir, settings=Settings(mode="shared", x_primary=True))
    # Remove reference to W&B service, so that children re-initialize it
    del os.environ["WANDB_SERVICE"]
    return logger


class DummyExperiment:
    def __init__(self, id: str, name: str):
        self.id = id
        self.name = name
        self._attach_id = None


def make_secondary_wandb_logger(primary_logger: WandbLogger) -> WandbLogger:
    logger = WandbLogger(
        save_dir=primary_logger.save_dir,
        dir=primary_logger.save_dir,
        id=primary_logger.experiment.id,
        name=primary_logger.experiment.name,
        project=primary_logger.experiment.project,
        tags=primary_logger.experiment.tags,
        settings=Settings(mode="shared", x_primary=False, x_update_finish_state=False),
    )
    # De-initialize experiment to make it friendly to transfer to a different node
    logger._experiment = DummyExperiment(id=primary_logger.experiment.id, name=primary_logger.experiment.name)
    return logger

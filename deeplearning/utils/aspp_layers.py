from typing import Any, List, Type, Union

import torch
import torch.nn.functional as F
from torch import nn

from deeplearning.trt_extensions import TRTDontFuse

BATCHNORM_OPTIONS = Union[Type[nn.BatchNorm2d]]


class ASPPConv(nn.Sequential):
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        dilation: int,
        batch_norm: BATCHNORM_OPTIONS,
        relu_inplace: bool = True,
    ):
        modules = []
        if dilation >= 14:
            # Workaround for an issue in TensorRT that makes Conv2d with padding >= 14 slow.
            # Separating ConstantPad2d from Conv2d via TRTDontFuse() alleviates the issue.
            # See also: https://gist.github.com/alsrgv/126c75b68bef9b66e14e70cd05357897
            modules += [
                nn.ConstantPad2d(dilation, 0.0),
                TRTDontFuse(),
                nn.Conv2d(in_channels, out_channels, 3, dilation=dilation, bias=False),
            ]
        else:
            modules += [
                nn.Conv2d(in_channels, out_channels, 3, padding=dilation, dilation=dilation, bias=False),
            ]
        modules += [batch_norm(out_channels), nn.ReLU(inplace=relu_inplace)]
        super(ASPPConv, self).__init__(*modules)


class ASPPPooling(nn.Module):
    _version = 2

    def __init__(self, in_channels: int, out_channels: int, batch_norm: BATCHNORM_OPTIONS, relu_inplace: bool = True):
        super(ASPPPooling, self).__init__()
        self.mods = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            batch_norm(out_channels),
            nn.ReLU(inplace=relu_inplace),
        )

    def forward(self, x: torch.Tensor) -> Any:
        size = x.shape[-2:]
        x = self.mods(x)
        original_dtype = x.dtype
        if original_dtype == torch.bfloat16:
            x = x.to(torch.float32)

        output = F.interpolate(x, size=size)

        if original_dtype == torch.bfloat16:
            output = output.to(torch.bfloat16)
        return output

    def _load_from_state_dict(
        self,
        state_dict: Any,
        prefix: str,
        local_metadata: Any,
        strict: bool,
        missing_keys: Any,
        unexpected_keys: Any,
        error_msgs: Any,
    ) -> None:
        version = local_metadata.get("version", 1)
        if version < 2:
            for key in list(state_dict.keys()):
                # In version 1, `self.mods` did not exist and submodules were
                # named as in nn.Sequential, so add `mods.` to the name if
                # necessary
                if key.startswith(prefix):
                    postfix = key[len(prefix) :]
                    if not postfix.startswith("mods."):
                        new_key = prefix + "mods." + postfix
                        value = state_dict[key]
                        del state_dict[key]
                        state_dict[new_key] = value

        super(ASPPPooling, self)._load_from_state_dict(
            state_dict, prefix, local_metadata, strict, missing_keys, unexpected_keys, error_msgs
        )


class ASPP(nn.Module):
    def __init__(
        self, in_channels: int, atrous_rates: List[int], batch_norm: BATCHNORM_OPTIONS, relu_inplace: bool = True
    ):
        super(ASPP, self).__init__()
        out_channels = 256
        modules: List[Union[ASPPConv, ASPPPooling, nn.Sequential]] = []
        modules.append(
            nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, bias=False),
                batch_norm(out_channels),
                nn.ReLU(inplace=relu_inplace),
            )
        )

        for rate in atrous_rates:
            modules.append(ASPPConv(in_channels, out_channels, rate, batch_norm, relu_inplace=relu_inplace))
        modules.append(ASPPPooling(in_channels, out_channels, batch_norm, relu_inplace=relu_inplace))

        self.convs = nn.ModuleList(modules)

        self.project = nn.Sequential(
            nn.Conv2d((len(atrous_rates) + 2) * out_channels, out_channels, 1, bias=False),
            batch_norm(out_channels),
            nn.ReLU(inplace=relu_inplace),
            nn.Dropout(0.5),
        )

    def forward(self, x: torch.Tensor) -> Any:
        result_list = []
        for layer in self.convs:
            result_list.append(layer(x))
        result = torch.cat(result_list, dim=1)
        return self.project(result)

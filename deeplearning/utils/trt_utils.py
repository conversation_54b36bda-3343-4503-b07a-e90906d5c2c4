from typing import Any, Dict, List, Optional, Tuple, cast

import numpy as np
import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.datasets_types import DatasetLabel
from deeplearning.deepweed.losses import point_loss, segmentation_loss
from deeplearning.deepweed.losses.embeddings_loss import embeddings_loss, info_nce_loss, triplet_loss
from deeplearning.deepweed.metadata import ImageMetadata
from deeplearning.deepweed.metrics import Metrics
from deeplearning.deepweed.model import DeepweedOutput
from deeplearning.deepweed.remote_veselka_dataset import RemoteVeselkaDatasetClient
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass


def compute_deepweed_metrics(
    out: DeepweedOutput,
    out_hat: DeepweedOutput,
    out_label: DatasetLabel,
    batch_enabled_weed_point_classes: torch.Tensor,
    batch_enabled_segm_classes: torch.Tensor,
    batch_enabled_hits: torch.Tensor,
    out_point_confidence: List[torch.Tensor],
    out_point_crop_protection: List[torch.Tensor],
    filepaths: Optional[List[str]],
    loss_multipliers: torch.Tensor,
    segm_class_weights: torch.Tensor,
    image_metadata: List[ImageMetadata],
    config: DeepweedConfig,
    weed_point_class_weights: torch.Tensor,
    point_hit_weights: torch.Tensor,
    crop_classes: List[str],
    dataset: RemoteVeselkaDatasetClient,
    compute_dataframes: Optional[bool] = False,
    model_elapsed_time: Optional[float] = None,
    starting_pos: Optional[List[Tuple[float, float]]] = None,
    embeddings: Optional[torch.Tensor] = None,
    capture_embeddings: bool = False,
    reduced_scaled_embeddings: Optional[torch.Tensor] = None,
    save_embeddings_to_db: bool = False,
    evaluation_height: int = 0,
    evaluation_width: int = 0,
    transpose_points: List[bool] = [],
    current_epoch: int = 0,
    overprediction_warmup: Optional[int] = None,
    segm_dice_loss_weight: float = 1.0,
    segm_bce_loss_weight: float = 5.0,
    no_crop_background_multiplier: float = 2.0,
) -> Tuple[Metrics, Dict[str, Any]]:
    batch_size = out.batch_size

    pre_sigmoid_point_hit_threshold = -np.log(1 / config.point_hit_threshold - 1)

    if segm_class_weights.sum() > 1e-9:
        segm_dice_loss, segm_dice_loss_per_sample = segmentation_loss.segm_dice_loss(
            out.mask,
            out_hat.mask,
            segm_class_weights,
            batch_enabled_segm_classes.unsqueeze(-1).unsqueeze(-1),
            out_hat.pre_sigmoid,
            current_epoch,
            overprediction_warmup,
        )
        segm_bce_loss, segm_bce_loss_per_sample = segmentation_loss.segm_bce_loss(
            out.mask,
            out_hat.mask,
            segm_class_weights,
            batch_enabled_segm_classes.unsqueeze(-1).unsqueeze(-1),
            out_hat.pre_sigmoid,
        )
    else:
        segm_dice_loss = torch.tensor(0.0, device=out.mask.device)
        segm_dice_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        segm_bce_loss = torch.tensor(0.0, device=out.mask.device)
        segm_bce_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)

    point_weed_hit_loss = torch.tensor(0.0, device=out.mask.device)
    point_weed_hit_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
    point_crop_hit_loss = torch.tensor(0.0, device=out.mask.device)
    point_crop_hit_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
    point_plant_hit_loss = torch.tensor(0.0, device=out.mask.device)
    point_plant_hit_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
    point_category_loss = torch.tensor(0.0, device=out.mask.device)
    point_category_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
    point_offset_loss = torch.tensor(0.0, device=out.mask.device)
    point_offset_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
    point_size_loss = torch.tensor(0.0, device=out.mask.device)
    point_size_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
    embedding_loss = torch.tensor(0.0, device=out.mask.device)
    embedding_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)

    positive_distances = None
    negative_distances = None
    positive_comparison = None
    negative_comparison = None

    if weed_point_class_weights.sum() > 1e-9:
        crop_punishment_weights = [
            torch.stack(
                [
                    torch.where(  # All spots where there is a point hit but on doesn't exist in the label
                        torch.logical_and(
                            y_point_hits_i[HitClass.WEED : HitClass.WEED + 1, :, :] <= config.point_hit_threshold,
                            y_point_hits_hat_i[HitClass.WEED : HitClass.WEED + 1, :, :]
                            > (pre_sigmoid_point_hit_threshold if out_hat.pre_sigmoid else config.point_hit_threshold),
                        ),
                        y_point_crop_protection_i,  # Pick from the multipliers, which take into account whether there is a crop/baby crop at that location
                        torch.ones_like(y_point_crop_protection_i),  # ones
                    )
                    # Do this computation separately for every example
                    for (y_point_hits_i, y_point_hits_hat_i, y_point_crop_protection_i) in zip(
                        y_point_hits, y_point_hits_hat, y_point_crop_protection
                    )
                ]
            )
            for (y_point_hits, y_point_hits_hat, y_point_crop_protection) in zip(
                out.point_hits, out_hat.point_hits, out_point_crop_protection
            )
        ]

        point_weed_hit_losses = [
            point_loss.point_hit_loss(*t)
            for t in zip(
                out.point_hits,
                out_hat.point_hits,
                out_point_confidence,
                crop_punishment_weights,
                config.point_downsample,
                loss_multipliers,
                [batch_enabled_hits.unsqueeze(-1).unsqueeze(-1)] * len(config.point_downsample),
                [HitClass.WEED] * len(config.point_downsample),
                [out_hat.pre_sigmoid] * len(config.point_downsample),
                [point_hit_weights] * len(config.point_downsample),
                [config.point_weight_constant] * len(config.point_downsample),
                [crop_classes] * len(config.point_downsample),
                [no_crop_background_multiplier] * len(config.point_downsample),
                [config.focal_loss] * len(config.point_downsample),
            )
        ]
        point_weed_hit_loss = cast(torch.Tensor, sum(item[0] for item in point_weed_hit_losses))
        point_weed_hit_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_weed_hit_losses))
        point_crop_hit_losses = [
            point_loss.point_hit_loss(*t)
            for t in zip(
                out.point_hits,
                out_hat.point_hits,
                out_point_confidence,
                crop_punishment_weights,
                config.point_downsample,
                loss_multipliers,
                [batch_enabled_hits.unsqueeze(-1).unsqueeze(-1)] * len(config.point_downsample),
                [HitClass.CROP] * len(config.point_downsample),
                [out_hat.pre_sigmoid] * len(config.point_downsample),
                [point_hit_weights] * len(config.point_downsample),
                [config.point_weight_constant] * len(config.point_downsample),
                [crop_classes] * len(config.point_downsample),
                [no_crop_background_multiplier] * len(config.point_downsample),
                [config.focal_loss] * len(config.point_downsample),
            )
        ]
        point_crop_hit_loss = cast(torch.Tensor, sum(item[0] for item in point_crop_hit_losses))
        point_crop_hit_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_crop_hit_losses))

        point_plant_hit_losses = [
            point_loss.point_hit_loss(*t)
            for t in zip(
                out.point_hits,
                out_hat.point_hits,
                out_point_confidence,
                crop_punishment_weights,
                config.point_downsample,
                loss_multipliers,
                [batch_enabled_hits.unsqueeze(-1).unsqueeze(-1)] * len(config.point_downsample),
                [HitClass.PLANT] * len(config.point_downsample),
                [out_hat.pre_sigmoid] * len(config.point_downsample),
                [point_hit_weights] * len(config.point_downsample),
                [config.point_weight_constant] * len(config.point_downsample),
                [crop_classes] * len(config.point_downsample),
                [no_crop_background_multiplier] * len(config.point_downsample),
                [config.focal_loss] * len(config.point_downsample),
            )
        ]
        point_plant_hit_loss = cast(torch.Tensor, sum(item[0] for item in point_plant_hit_losses))
        point_plant_hit_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_plant_hit_losses))

        point_category_losses = [
            point_loss.point_category_loss(*t)
            for t in zip(
                out.point_hits,
                out.point_categories,
                out_hat.point_categories,
                out_point_confidence,
                config.point_downsample,
                loss_multipliers,
                batch_enabled_weed_point_classes.unsqueeze(-1).unsqueeze(-1),
                [out_hat.pre_sigmoid] * len(config.point_downsample),
                [config.point_weight_constant] * len(config.point_downsample),
                [config.point_hit_threshold] * len(config.point_downsample),
                [weed_point_class_weights] * len(config.point_downsample),
            )
        ]
        point_category_loss = cast(torch.Tensor, sum(item[0] for item in point_category_losses))
        point_category_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_category_losses))

        point_offset_losses = [
            point_loss.point_offset_loss(*t)
            for t in zip(
                out.point_hits,
                out.point_offsets,
                out_hat.point_offsets,
                out_point_confidence,
                config.point_downsample,
                loss_multipliers,
                [config.point_weight_constant] * len(config.point_downsample),
                [config.point_hit_threshold] * len(config.point_downsample),
            )
        ]
        point_offset_loss = cast(torch.Tensor, sum(item[0] for item in point_offset_losses))
        point_offset_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_offset_losses))

        point_size_losses = [
            point_loss.point_size_loss(*t)
            for t in zip(
                out.point_hits,
                out.point_sizes,
                out_hat.point_sizes,
                out_point_confidence,
                config.point_downsample,
                loss_multipliers,
                [config.point_weight_constant] * len(config.point_downsample),
                [config.point_hit_threshold] * len(config.point_downsample),
            )
        ]
        point_size_loss = cast(torch.Tensor, sum(item[0] for item in point_size_losses))
        point_size_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_size_losses))

        if embeddings is not None:
            if config.embedding_loss_function == "pairwise":
                (
                    embedding_loss,
                    embedding_loss_per_sample,
                    positive_distances,
                    negative_distances,
                    positive_comparison,
                    negative_comparison,
                ) = embeddings_loss(
                    out_label.points,
                    embeddings,
                    base_embedding_difference_on_hit_class=config.base_embedding_label_on_hit,
                    max_items_to_compare=config.max_comparison_pairs_for_embedding_training,
                    min_for_positive=config.embedding_positive_threshold,
                    max_for_negative=config.embedding_negative_threshold,
                    margin=config.embedding_margin,
                    compare_whole_batch=config.apply_embedding_loss_across_batch,
                    use_comparison_similarity_loss=config.use_comparison_similarity_loss,
                    use_points_with_overlap=config.use_points_with_overlap,
                    without_overlap_and_without_overlap_percentage=config.without_overlap_and_without_overlap_percentage,
                    with_overlap_and_with_overlap_percentage=config.with_overlap_and_with_overlap_percentage,
                )
            elif config.embedding_loss_function == "info_nce":
                embedding_loss, embedding_loss_per_sample, _ = info_nce_loss(
                    out_label.points,
                    embeddings,
                    base_embedding_difference_on_hit_class=config.base_embedding_label_on_hit,
                    min_for_positive=config.embedding_positive_threshold,
                    max_for_negative=config.embedding_negative_threshold,
                    max_items_to_compare=config.max_comparison_pairs_for_embedding_training,
                )
            elif config.embedding_loss_function == "triplet":
                (embedding_loss, embedding_loss_per_sample, positive_distances, negative_distances,) = triplet_loss(
                    out_label.points,
                    embeddings,
                    max_items_to_compare=config.max_comparison_pairs_for_embedding_training,
                    min_for_positive=config.embedding_positive_threshold,
                    max_for_negative=config.embedding_negative_threshold,
                    margin=config.embedding_margin,
                    random_neighbor=config.triplet_loss_random_neighbor,
                    base_embedding_difference_on_hit_class=config.base_embedding_label_on_hit,
                )

            # Remove embedding labels from out_label.points because they aren't used passed here and take up too much memory
            for point_list in out_label.points:
                for po in point_list:
                    del po.comparison_embedding

    out_hat = out_hat.sigmoid()

    # fence label & predictions based on enabled classes for this batch
    with torch.no_grad():
        out = out.fence(batch_enabled_segm_classes)
    out_hat = out_hat.fence(batch_enabled_segm_classes)

    model_is_driptape = bool(segm_class_weights.sum() > 1e-9 and weed_point_class_weights.sum() < 1e-9)

    overall_loss_per_sample = (
        segm_dice_loss_weight * segm_dice_loss_per_sample
        + segm_bce_loss_weight * segm_bce_loss_per_sample
        + config.point_weed_hit_loss_weight * point_weed_hit_loss_per_sample
        + config.point_crop_hit_loss_weight * point_crop_hit_loss_per_sample
        + config.point_plant_hit_loss_weight * point_plant_hit_loss_per_sample
        + config.point_category_loss_weight * point_category_loss_per_sample
        + config.point_offset_loss_weight * point_offset_loss_per_sample
        + config.point_size_loss_weight * point_size_loss_per_sample
        + config.embedding_loss_weight * embedding_loss_per_sample
    )

    # Record losses in the dictionary
    recording_loss_dict = {
        "overall_loss_per_sample": overall_loss_per_sample.detach().cpu(),
        "segm_dice_loss_per_sample": segm_dice_loss_per_sample.detach().cpu(),
        "weighted_segm_dice_loss_per_sample": segm_dice_loss_weight * segm_dice_loss_per_sample.detach().cpu(),
        "segm_bce_loss_per_sample": segm_bce_loss_per_sample.detach().cpu(),
        "weighted_segm_bce_loss_per_sample": segm_bce_loss_weight * segm_bce_loss_per_sample.detach().cpu(),
        "point_weed_hit_loss_per_sample": point_weed_hit_loss_per_sample.detach().cpu(),
        "weighted_point_weed_hit_loss_per_sample": config.point_weed_hit_loss_weight
        * point_weed_hit_loss_per_sample.detach().cpu(),
        "point_crop_hit_loss_per_sample": point_crop_hit_loss_per_sample.detach().cpu(),
        "weighted_point_crop_hit_loss_per_sample": config.point_crop_hit_loss_weight
        * point_crop_hit_loss_per_sample.detach().cpu(),
        "point_plant_hit_loss_per_sample": point_plant_hit_loss_per_sample.detach().cpu(),
        "weighted_point_plant_hit_loss_per_sample": config.point_plant_hit_loss_weight
        * point_plant_hit_loss_per_sample.detach().cpu(),
        "point_category_loss_per_sample": point_category_loss_per_sample.detach().cpu(),
        "weighted_point_category_loss_per_sample": config.point_category_loss_weight
        * point_category_loss_per_sample.detach().cpu(),
        "point_offset_loss_per_sample": point_offset_loss_per_sample.detach().cpu(),
        "weighted_point_offset_loss_per_sample": config.point_offset_loss_weight
        * point_offset_loss_per_sample.detach().cpu(),
        "point_size_loss_per_sample": point_size_loss_per_sample.detach().cpu(),
        "weighted_point_size_loss_per_sample": config.point_size_loss_weight
        * point_size_loss_per_sample.detach().cpu(),
        "embedding_loss_per_sample": embedding_loss_per_sample.detach().cpu(),
        "weighted_embedding_loss_per_sample": config.embedding_loss_weight * embedding_loss_per_sample.detach().cpu(),
    }

    return (
        Metrics(
            out,
            out_hat,
            out_label,
            segm_dice_loss,
            segm_bce_loss,
            segm_class_weights > 0,
            point_weed_hit_loss,
            point_crop_hit_loss,
            point_plant_hit_loss,
            point_category_loss,
            point_offset_loss,
            point_size_loss,
            embedding_loss,
            weed_point_class_weights > 0,
            batch_enabled_hits,
            batch_enabled_weed_point_classes,
            dataset.get_train_ppi(),
            0.0,
            dataset.weed_classes,
            dataset.segm_classes,
            image_metadata,
            config,
            compute_dataframes=compute_dataframes,
            learning_rate=config.lr,
            filepaths=filepaths,
            model_elapsed_time=model_elapsed_time,
            starting_pos=starting_pos,
            segm_dice_loss_weight=segm_dice_loss_weight,
            segm_bce_loss_weight=segm_bce_loss_weight,
            point_weed_hit_loss_weight=config.point_weed_hit_loss_weight,
            point_crop_hit_loss_weight=config.point_crop_hit_loss_weight,
            point_plant_hit_loss_weight=config.point_plant_hit_loss_weight,
            point_category_loss_weight=config.point_category_loss_weight,
            point_offset_loss_weight=config.point_offset_loss_weight,
            point_size_loss_weight=config.point_size_loss_weight,
            embedding_loss_weight=config.embedding_loss_weight,
            embeddings=embeddings if capture_embeddings else None,
            reduced_scaled_embeddings=reduced_scaled_embeddings,
            total_positive_example_distance=positive_distances.sum().unsqueeze(0)
            if positive_distances is not None
            else None,
            total_negative_example_distance=negative_distances.sum().unsqueeze(0)
            if negative_distances is not None
            else None,
            total_positive_example_comparison=positive_comparison.sum().unsqueeze(0)
            if positive_comparison is not None
            else None,
            total_negative_example_comparison=negative_comparison.sum().unsqueeze(0)
            if negative_comparison is not None
            else None,
            total_positive_examples=torch.tensor(torch.numel(positive_distances)).unsqueeze(0)
            if positive_distances is not None
            else None,
            total_negative_examples=torch.tensor(torch.numel(negative_distances)).unsqueeze(0)
            if negative_distances is not None
            else None,
            save_embeddings_to_db=save_embeddings_to_db,
            for_driptape_model=model_is_driptape,
            evaluation_height=evaluation_height,
            evaluation_width=evaluation_width,
            transpose_points=transpose_points,
        ),
        recording_loss_dict,
    )

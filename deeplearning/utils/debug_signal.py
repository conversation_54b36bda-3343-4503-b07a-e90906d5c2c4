import signal
import threading
from typing import Any


class DebugSignal:
    def __init__(self) -> None:
        self._usr1_signalled = threading.Event()
        signal.signal(signal.SIGUSR1, self._usr1_handler)

    def _usr1_handler(self, ignum: Any, frame: Any) -> None:
        self._usr1_signalled.set()

    def is_set(self) -> bool:
        return self._usr1_signalled.is_set()

    def clear(self) -> None:
        return self._usr1_signalled.clear()

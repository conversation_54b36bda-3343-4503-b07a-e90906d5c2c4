# deep-learning
Deep Learning (training and inference)

This includes libraries and scripts for:
* Training
* Inference
* Experiments
* etc...

To support NVIDIA TensorRT, you must use the provided `Dockerfile` (services/container/cv/Dockerfile)

### Model Info

```
$ python -m deeplearning.tools.model_info -m /path/to/model
```

# Standard Jobs

## Introduction

The deeplearning codebase is responsible for running four standard jobs in our clustered resource environment. These
are pre-training, training, fine-tuning, and evaluation. In this document we will step through each one and highlight
the specific similarities and differences between them. 

## Overall Script Structure

Each of our standard tasks follows the same basic structure. In each instance we have three core steps. 

    1. Define a dataset
    2. Define a model
    3. Define a algorithm parameters
    4. Run the model and dataset through the algorithm


### Pre-training

Model pre-training is intended to help get the model parameters into a reasonable space for learning about general
plants. We train these models by selecting all of our crop data and training a single model across the entire dataset.
These runs use a higher number of epochs and take significantly longer to train. They are not intended to be deployed
but rather serve as a starting point for our standard training runs.

Models produced from the pre-training step are not fit to deploy to the field.

### Training

Model training is the core functionality of the deeplearning codebase. We train for a standard 50 epochs and utilize a
learning rate schedule that halves the learning rate after a predetermined number of epochs. Models are initialized
with either random weights or weights that were determined via a pre-training run. Segmentation classes (just drip-tape
right now) are included under very specific conditions: 

  1) We have enough examples of drip-tape
  2) We are not training with pre-trained weights

If we are training with pre-trained weights then the segmentation portion of the model has already been trained and we
leave it unchanged so as to not degrade its performance on the smaller dataset of segmentation examples.

Models produced from the training step are fit to deploy to the field.

### Fine-tuning

Model fine-tuning follows a similar trend to model training but instead of starting from a pre-trained model we start
from a fully-trained model. This means that the model weights we start with should already be fairly performant on the
crop in question. The purpose of fine-tuning is to rapidly improve our model on recently gathered datapoints. To this
end we change our sampling strategy for this process and split our sampling between old and new images 50/50.

Models produced from the fine-tuning step are fit to deploy to the field.

### Evaluation

Model evaluations are conducted once a model is fully trained and do not produce models themselves. This step produces
a set of test results that can be used to improve our understand of model performance on new data as it comes into the
database. The core product of the evaluation step is a CSV file which contains a subset of the predictions a model puts
out for each image. This data can then be parsed to provide custom metrics on the fly to various data splits.

No models are produced by the evaluation step.
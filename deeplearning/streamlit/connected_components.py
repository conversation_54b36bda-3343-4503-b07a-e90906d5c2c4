import time
from random import randint
from typing import List, <PERSON>ple

import cupy as cp
import cv2
import numpy as np
import streamlit as st

from cv.connected_components.connected_components_numba import connected_components_numba
from cv.connected_components.connected_components_cupy_raw_kernel import connected_components_cupy

HEIGHT = 3000
WIDTH = 4000
MAX_RADIUS = 100


def generate_random(number_blobs: int) -> cp.ndarray:
    image = np.zeros((HEIGHT, WIDTH, 1))
    for i in range(number_blobs):
        x = randint(0, WIDTH)
        y = randint(0, HEIGHT)
        size = randint(1, MAX_RADIUS)
        image = cv2.circle(image, (x, y), size, 1, -1)

    return cp.asarray(image)


def run_connected_components_numba(image: cp.ndarray) -> List[Tuple[float, float, float]]:
    start = time.time()
    centroids = connected_components_numba(image)
    end = time.time()

    st.text(f"Time to get numba centroids: {end - start} seconds")

    return centroids


def run_connected_components_cupy(image: cp.ndarray) -> List[Tuple[float, float, float]]:
    start_cupy = time.time()
    centroids = connected_components_cupy(image)
    end_cupy = time.time()

    st.text(f"Time to get cupy centroids: {end_cupy - start_cupy} seconds")

    return centroids


st.title("Connected Components")
print("streamlit", time.time())

number_blobs = st.sidebar.slider("Number of blobs", min_value=0, max_value=50, value=5)
cc_version = st.sidebar.radio("Version", ["cupy", "numba"])

image = generate_random(number_blobs)

if cc_version == "numba":
    centroids = run_connected_components_numba(image)
else:
    centroids = run_connected_components_cupy(image)

image = cp.asnumpy(cp.dstack([image] * 3))

st.text(f"Number of centroids detected {len(centroids)}")
for centroid in centroids:
    image = cv2.circle(image, (int(centroid[0]), int(centroid[1])), int(centroid[2]), [1, 0, 0.5], 5)
    image = cv2.circle(image, (int(centroid[0]), int(centroid[1])), 5, [1, 0, 0.5], 5)

st.header("Connected Components")
st.image(cp.asnumpy(image), use_column_width=True)

del image, centroids, number_blobs

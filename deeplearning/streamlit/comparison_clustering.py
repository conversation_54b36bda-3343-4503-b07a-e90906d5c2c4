import logging
import random
from typing import Any, Dict, List, Optional

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import streamlit as st
import torch
from scipy.cluster.hierarchy import dendrogram
from sklearn.cluster import DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize

from deeplearning.constants import CROP_ID_TO_LABEL
from deeplearning.streamlit.comparison_lookup import find_nearest_neighbors_indices, get_embeddings, retrieve_inputs
from deeplearning.tools.visualization import do_tsne, format_hue

DB_SCAN = "DB_SCAN"
AGGLOMERATIVE = "AGGLOMERATIVE"

CLUSTERING_ALGS = [DB_SCAN, AGGLOMERATIVE]


def get_tsne(data: np.ndarray, metadata: List[Dict[str, Any]]):
    tsne_res, items = do_tsne(
        features=data,
        metadata=metadata,
        parameters={"perplexity": 10, "n_iter": 500, "learning_rate": 10, "num_samples": len(metadata)},
        not_wanted=[],
    )

    return tsne_res, items


def get_plot(tsne_res, items, legend_field: str = "cluster_label") -> None:
    x = tsne_res[:, 0]
    y = tsne_res[:, 1]

    legend, color = format_hue([i[legend_field] for i in items])

    matplotlib.rcParams["savefig.dpi"] = 300
    matplotlib.rcParams["font.size"] = 20
    matplotlib.rcParams["figure.figsize"] = [25, 25]

    fig, ax = plt.subplots()

    scatter = ax.scatter(x=x, y=y, c=color, cmap="tab20")

    legend = ax.legend(scatter.legend_elements(num=len(legend))[0], legend, bbox_to_anchor=(1.05, 1), loc="upper left")
    ax.add_artist(legend)

    ax.set_title(f"{legend_field} (TSNE)")

    plt.axis("off")
    plt.tick_params(left=False, bottom=False)

    plt.ylim(min(y) - 0, max(y) + 0)
    plt.xlim(min(x) - 0, max(x) + 0)

    return fig


def plot_dendrogram(model, **kwargs):
    counts = np.zeros(model.children_.shape[0])
    n_samples = len(model.labels_)
    for i, merge in enumerate(model.children_):
        current_count = 0
        for child_idx in merge:
            if child_idx < n_samples:
                current_count += 1  # leaf node
            else:
                current_count += counts[child_idx - n_samples]
        counts[i] = current_count

    linkage_matrix = np.column_stack([model.children_, model.distances_, counts]).astype(float)

    # Plot the corresponding dendrogram
    fig, ax = plt.subplots()
    dendrogram(linkage_matrix, ax=ax, **kwargs)
    return fig


def get_distance_cosine_softplus_metric(data: np.ndarray) -> np.ndarray:
    normalized_data = normalize(data)
    cosined_matrix = np.matmul(normalized_data, normalized_data.transpose())
    softplused_matrix = torch.nn.Softplus(beta=20, threshold=2)(torch.from_numpy(cosined_matrix)).numpy()
    return np.clip(1 - softplused_matrix, 0, 1)


def cluster(  # noqa: C901
    algorithm: str,
    data: np.ndarray,
    embedding_data: List[Dict[str, Any]],
    evaluation_id,
    n_dim,
    sample_n_points,
    normalize_input,
    transformed_embedding,
    embedding_images,
) -> None:
    logging.info(f"Clustering: {algorithm}...")
    plot_dendro = False
    if algorithm == DB_SCAN:
        st.sidebar.header("DB_SCAN parameters")
        eps = st.sidebar.number_input("eps", value=0.5)
        min_samples = st.sidebar.number_input("min_samples", value=5)
        metric = st.sidebar.radio("metric", ["euclidean", "cosine", "cosine_softplus"])
        key = f"{evaluation_id}::{n_dim}::{sample_n_points}::{eps}::{min_samples}::{metric}::{normalize_input}::db_scan"
        if key not in st.session_state:
            st.session_state[key] = {}
        input_data = data

        if metric == "cosine_softplus":
            if "distance_cosine_softplus_metric" not in st.session_state[key]:
                input_data = get_distance_cosine_softplus_metric(data)
                st.session_state[key].update({"distance_cosine_softplus_metric": input_data})
            input_data = st.session_state[key]["distance_cosine_softplus_metric"]

        clustering = DBSCAN(
            eps=eps, min_samples=min_samples, metric="precomputed" if metric == "cosine_softplus" else metric
        ).fit(input_data)
        clustering_labels = [str(cl) for cl in clustering.labels_]
    elif algorithm == AGGLOMERATIVE:
        st.sidebar.header("AGGLOMERATIVE_CLUSTERING parameters")
        restriction = st.sidebar.radio("Restriction", ["n_clusters", "distance_threshold"])
        num_clusters = None
        distance_threshold = None
        if restriction == "n_clusters":
            num_clusters = st.sidebar.number_input("n_clusters", value=10)
        else:
            distance_threshold = st.sidebar.number_input("distance_threshold", value=0.5)
        linkage = st.sidebar.radio("linkage", ["ward", "complete", "average", "single"])

        metric = st.sidebar.radio("metric", ["euclidean", "cosine", "cosine_softplus"])
        plot_dendro = st.sidebar.checkbox("Plot Dendrogram", value=False)
        key = f"{evaluation_id}::{n_dim}::{sample_n_points}::{restriction}::{num_clusters}::{distance_threshold}::{linkage}::{metric}::{normalize_input}::db_scan"
        if key not in st.session_state:
            st.session_state[key] = {}
        input_data = data

        if metric == "cosine_softplus":
            if "distance_cosine_softplus_metric" not in st.session_state[key]:
                input_data = get_distance_cosine_softplus_metric(data)
                st.session_state[key].update({"distance_cosine_softplus_metric": input_data})
            input_data = st.session_state[key]["distance_cosine_softplus_metric"]

        clustering = AgglomerativeClustering(
            n_clusters=num_clusters,
            distance_threshold=distance_threshold,
            linkage=linkage,
            compute_distances=True,
            metric="precomputed" if metric == "cosine_softplus" else metric,
        ).fit(input_data)
        clustering_labels = [str(cl) for cl in clustering.labels_]

    for idx, item in enumerate(embedding_data):
        label = clustering_labels[idx]
        item["cluster_label"] = label
        item["predicted_clz"] = -2
        item["category_string"] = CROP_ID_TO_LABEL.get(item["category"], item["category"])

    if transformed_embedding is not None:
        images, _ = find_nearest_neighbors_indices(
            torch.from_numpy(transformed_embedding).cuda(), torch.from_numpy(data).cuda(), embedding_data
        )

        st.image(embedding_images[0])
        grid = retrieve_inputs(images[0])

        image_id_to_class = {item["image_id"]: item["cluster_label"] for item in images[0]}
        cols = st.columns(5)
        for idx, grid_item in enumerate(grid):
            with cols[idx % len(cols)]:
                st.image(grid_item.display_image, f"Class {image_id_to_class[grid_item.image_id]}")

    logging.info("Getting images from cluster...")
    col0, col1, col2 = st.columns([1, 3, 2])

    with col0:
        elements = []
        for label in list(set(clustering_labels)):
            ele = {"label": label, "num_items": len([item for item in clustering_labels if item == label])}
            elements.append(ele)

        st.markdown(
            pd.DataFrame(elements, columns=["label", "num_items"]).style.hide_index().to_html(), unsafe_allow_html=True
        )

    with col1:
        logging.info("Plotting...")
        if "cluster_label_plot" not in st.session_state[key]:
            tsne_res, tsne_items = get_tsne(data, embedding_data)
            fig1 = get_plot(tsne_res, tsne_items, "cluster_label")
            fig2 = get_plot(tsne_res, tsne_items, "category_string")
            st.session_state[key].update({"cluster_label_plot": fig1, "cluster_string_plot": fig2})
        st.pyplot(st.session_state[key]["cluster_label_plot"])
        st.pyplot(st.session_state[key]["cluster_string_plot"])
        if plot_dendro:
            if "dendro" not in st.session_state[key]:
                fig_dendro = plot_dendrogram(clustering)
                st.session_state[key].update({"dendro": fig_dendro})
            st.pyplot(st.session_state[key]["dendro"])

    with col2:
        for cluster_label in list(set(clustering_labels)):
            with st.expander(f"{cluster_label}"):
                items = []
                for ind in range(len(embedding_data)):
                    d = embedding_data[ind]
                    lab = clustering_labels[ind]
                    if lab == cluster_label:
                        items.append(d)

                items_to_display = retrieve_inputs(items[:10])
                for idx, grid_item in enumerate(items_to_display):
                    st.image(grid_item.display_image)


def preprocess(
    evaluation_id: str,
    data: torch.Tensor,
    n_dim: int,
    embeddings_data: List[Dict[str, Any]],
    sample_n_points: Optional[int] = None,
    normalize_input: bool = False,
    embedding_to_search: Optional[np.ndarray] = None,
) -> np.ndarray:
    key = f"{evaluation_id}::{n_dim}::{sample_n_points}::{normalize_input}"

    if key not in st.session_state:
        pca = PCA(n_components=n_dim)

        embeddings_array = data.cpu().numpy()
        if sample_n_points is not None:
            random.seed(1)
            indices = random.sample(list(range(embeddings_array.shape[0])), k=sample_n_points)
            embs_list = embeddings_array.tolist()
            items = [embs_list[ind] for ind in indices]
            emb_data = [embeddings_data[ind] for ind in indices]
            embeddings_data = emb_data
            embeddings_array = np.array(items)
        if normalize_input:
            embeddings_array = normalize(embeddings_array)
        embeddings = pca.fit_transform(embeddings_array)
        st.session_state[key] = {"embeddings": embeddings, "embeddings_data": embeddings_data, "pca": pca}

    transformed_embedding = None
    if embedding_to_search is not None:
        if normalize_input:
            embedding_to_search = normalize(embedding_to_search.cpu().numpy())
        transformed_embedding = st.session_state[key]["pca"].transform(embedding_to_search)

    return st.session_state[key]["embeddings"], st.session_state[key]["embeddings_data"], transformed_embedding


def get_filters():
    algorithm = st.sidebar.radio("Cluster algorithm", CLUSTERING_ALGS)
    number_dimensions = st.sidebar.number_input("Reduce to N dimension", value=128)
    sample_n_points = st.sidebar.text_input("Sample N Points")
    normalize_input = st.sidebar.checkbox("Normalize Input", value=True)

    return {
        "algorithm": algorithm,
        "number_dimensions": number_dimensions,
        "sample_n_points": sample_n_points,
        "normalize_input": normalize_input,
    }


def clustering(
    evaluation_id, embedding_to_search: Optional[torch.Tensor] = None, embedding_images: Optional[torch.Tensor] = None
):
    embeddings_array, embeddings_data = get_embeddings(evaluation_id, False)
    filters = get_filters()

    algorithm = filters["algorithm"]
    number_dimensions = filters["number_dimensions"]
    sample_n_points = filters["sample_n_points"]
    normalize_input = filters["normalize_input"]

    embeddings, embeddings_data, transformed_embedding = preprocess(
        evaluation_id,
        embeddings_array,
        number_dimensions,
        embeddings_data,
        int(sample_n_points) if sample_n_points != "" else None,
        normalize_input,
        embedding_to_search,
    )

    cluster(
        algorithm,
        embeddings,
        embeddings_data,
        evaluation_id,
        number_dimensions,
        sample_n_points,
        normalize_input,
        transformed_embedding,
        embedding_images,
    )


def main():
    st.header("Comparison Clustering")

    evaluation_id = st.sidebar.text_input("Evaluation ID", "0fc9e4083b72aab9bda2c11a18776235")

    clustering(evaluation_id, None, None)


if __name__ == "__main__":
    main()

import os
from deeplearning.comparison.data_utils import load_embeddings_from_torch
import streamlit as st

def process_embeddings_data(data):
    """
    Process embeddings data to:
    1. Add the corresponding embedding row to each embeddings_data entry
    2. Order embeddings_data by key {x}_{y}
    3. Return processed data
    """
    embeddings_data = data['embeddings_data']
    embeddings = data['embeddings']

    # Step 1: Add embedding row to each embeddings_data entry
    processed_embeddings_data = []
    for i, embedding_data in enumerate(embeddings_data):
        # Create a copy of the embedding data and add the embedding
        enhanced_data = embedding_data.copy()
        enhanced_data['embedding'] = embeddings[i].tolist()  # Convert tensor to list for JSON serialization
        processed_embeddings_data.append(enhanced_data)

    # Step 2: Order by key {x}_{y}
    def get_sort_key(item):
        x = item.get('x', 0)
        y = item.get('y', 0)
        return f"{x}_{y}"

    processed_embeddings_data.sort(key=get_sort_key)

    # Create ordered dictionary with {x}_{y} as keys
    ordered_embeddings_data = {}
    for item in processed_embeddings_data:
        key = get_sort_key(item)
        ordered_embeddings_data[key] = item

    return ordered_embeddings_data

def main():
    new_dir = "/data/deeplearning/comparison_evaluations/production/comparison_evaluations/74658a692506cb66d80e6e2313249759"
    standard_dir = "/data/deeplearning/7465-comp"

    new_files = os.listdir(new_dir)
    standard_files = os.listdir(standard_dir)

    intersection_set = set(new_files).intersection(set(standard_files))
    intersection_list = list(intersection_set)

    index = st.sidebar.slider("Index", min_value=0, max_value=len(intersection_list) - 1, step=1, value=0)

    file = intersection_list[index]
    standard = load_embeddings_from_torch(os.path.join(standard_dir, file))
    new = load_embeddings_from_torch(os.path.join(new_dir, file))

    # Process embeddings data
    standard_processed = process_embeddings_data(standard)
    new_processed = process_embeddings_data(new)

    st.header("Image Meta")
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Standard")
        st.json(standard['image_meta'])
    with col2:
        st.subheader("New")
        st.json(new['image_meta'])

    st.header("Embeddings Data (Ordered by x_y)")
    col1, col2 = st.columns(2)
    with col1:
        st.subheader("Standard")
        st.json(standard_processed)
    with col2:
        st.subheader("New")
        st.json(new_processed)

    st.header("Embedding Vectors Preview")
    col1, col2 = st.columns(2)
    with col1:
        st.subheader("Standard")
        st.write("Shape:", standard['embeddings'].shape)
        # Display first few embeddings with truncated vectors
        for key, data in list(standard_processed.items())[:5]:  # Show first 5
            st.write(f"**{key}**: {data['embedding'][:10]}... (showing first 10 values)")
    with col2:
        st.subheader("New")
        st.write("Shape:", new['embeddings'].shape)
        # Display first few embeddings with truncated vectors
        for key, data in list(new_processed.items())[:5]:  # Show first 5
            st.write(f"**{key}**: {data['embedding'][:10]}... (showing first 10 values)")

if __name__ == "__main__":
    main()

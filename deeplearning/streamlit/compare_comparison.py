import os
from deeplearning.comparison.data_utils import load_embeddings_from_torch
import streamlit as st

def main():
    new_dir = "/data/deeplearning/comparison_evaluations/production/comparison_evaluations/74658a692506cb66d80e6e2313249759"
    standard_dir = "/data/deeplearning/7465-comp"
    
    new_files = os.listdir(new_dir)
    standard_files = os.listdir(standard_dir)
    
    intersection_set = set(new_files).intersection(set(standard_files))
    intersection_list = list(intersection_set)
    
    index = st.sidebar.slider("Index", min_value=0, max_value=len(intersection_list) - 1, step=1, value=0)
    
    file = intersection_list[index]
    standard = load_embeddings_from_torch(os.path.join(standard_dir, file))
    new = load_embeddings_from_torch(os.path.join(new_dir, file))    

    st.header("Image Meta")
    col1, col2 = st.columns(2)
    
    with col1:
        st.json(standard['image_meta'])
    with col2:
        st.json(new['image_meta'])
        
    st.header("Embeddings data")
    col1, col2 = st.columns(2)
    with col1:
        st.json(standard['embeddings_data'])
    with col2:
        st.json(new['embeddings_data'])
        
    st.header("Embeddings")
    col1, col2 = st.columns(2)
    with col1:
        st.write(standard['embeddings'])
    with col2:
        st.write(new['embeddings'])

if __name__ == "__main__":
    main()

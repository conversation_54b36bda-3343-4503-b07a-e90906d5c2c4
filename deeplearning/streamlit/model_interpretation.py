import functools
import json
import os
from typing import List

import boto3
import cv2
import numpy as np
import streamlit as st
import torch
from captum.attr import GuidedGradCam

from deeplearning.deepweed.datasets import DeepweedDataset, image_transforms
from deeplearning.deepweed.datasets_types import <PERSON><PERSON><PERSON>abe<PERSON>
from deeplearning.deepweed.metadata import Point
from deeplearning.deepweed.model import POINT_DOWNSAMPLE, Deepweed, DeepweedSeparableLayers
from deeplearning.deepweed.point_utils import compute_downsampled_size, make_centroid_tensors, render_hits
from deeplearning.scripts.utils.utils import download_records
from deeplearning.trt_extensions import Upsample
from deeplearning.utils.images import (
    DEFAULT_HEIGHT,
    DEFAULT_TRAIN_HEIGHT,
    DEFAULT_TRAIN_WIDTH,
    DEFAULT_WIDTH,
    denormalize_image,
)
from deeplearning.utils.resize_utils import tile_crop_origins
from lib.common.veselka.client import get_datapoint_json

st.title("Guided GradCam")

enabled_hit_class_names = ["WEED", "CROP"]
enabled_hit_classes = [0, 1]


@st.cache(allow_output_mutation=True)
def load_model_wrapper(model_checkpoint):
    checkpoints = torch.load(model_checkpoint)
    # TODO pass classes from model
    model = Deepweed(num_weed_point_classes=4, num_segm_classes=1, relu_inplace=False)
    model.load_state_dict(checkpoints["state_dict"])
    model.cuda().eval()
    model_wrapper = DeepweedSeparableLayers(model)
    return model_wrapper, model


@st.cache(allow_output_mutation=True)
def load_dataset(root_dir, weed_classes: List[str], crop_classes: List[str], segm_classes: List[str]):
    transforms = functools.partial(
        image_transforms,
        height=DEFAULT_HEIGHT,
        width=DEFAULT_WIDTH,
        train_height=DEFAULT_TRAIN_HEIGHT,
        train_width=DEFAULT_TRAIN_WIDTH,
        train_ppi=200,
        dilate_mask=0,
    )
    dataset = DeepweedDataset(
        filepath_index=f"{root_dir}/train.csv",
        transforms=transforms(training=True),
        weed_classes=weed_classes,
        crop_classes=crop_classes,
        segm_classes=segm_classes,
        camera="predict",
        require_certified_image=False,
    )

    return dataset


def download_model(model_id: str) -> str:
    best_checkpoint_path = f"{os.getenv('MAKA_DATA_DIR', '/data')}/deeplearning/models/{model_id}/best_checkpoint.ckpt"
    if not os.path.exists(best_checkpoint_path):
        download_records(model_id)
    return best_checkpoint_path


def download_image(image_url: str) -> str:
    tmp_dir = (
        f"{os.getenv('MAKA_DATA_DIR', '/data')}/tmp_streamlit_images/{os.path.splitext(image_url.split('/')[-1])[0]}"
    )
    os.makedirs(tmp_dir, exist_ok=True)
    tmp_path = f"{tmp_dir}/{image_url.split('/')[-1]}"
    metadata_url = f"{os.path.splitext(image_url)[0]}.metadata.json"
    tmp_metadata = f"{os.path.splitext(tmp_path)[0]}.metadata.json"
    tmp_points = f"{os.path.splitext(tmp_path)[0]}.points.json"
    if not os.path.exists(tmp_path):
        boto3.resource("s3").Bucket("maka-pono").download_file(image_url, tmp_path)
        boto3.resource("s3").Bucket("maka-pono").download_file(metadata_url, tmp_metadata)

        mock_datapoint = {"city": "asdf", "imageId": "asdf", "updated": 0, "categories": {"crownLabels": {}}}
        datapoint_json = get_datapoint_json(mock_datapoint, [])
        with open(tmp_points, "w") as f:
            json.dump(datapoint_json, f)

    with open(f"{tmp_dir}/train.csv", "w") as f:
        f.write(tmp_path)

    return tmp_dir


def get_square_filter(hit_clz, row, col, filter_size, image):
    downsampled_size = compute_downsampled_size((image.shape[1], image.shape[2]), POINT_DOWNSAMPLE[0])
    filt = torch.zeros(2, downsampled_size[0], downsampled_size[1])
    row_min = max(row // POINT_DOWNSAMPLE[0] - filter_size // POINT_DOWNSAMPLE[0], 0)
    row_max = min(row // POINT_DOWNSAMPLE[0] + filter_size // POINT_DOWNSAMPLE[0] + 1, downsampled_size[0])
    col_min = max(col // POINT_DOWNSAMPLE[0] - filter_size // POINT_DOWNSAMPLE[0], 0)
    col_max = min(col // POINT_DOWNSAMPLE[0] + filter_size // POINT_DOWNSAMPLE[0] + 1, downsampled_size[1])
    filt[hit_clz, row_min:row_max, col_min:col_max] = 1
    return filt


def get_hit_region(hit_clz, label_truth):
    filt = torch.from_numpy(label_truth).unsqueeze(0).repeat(2, 1, 1)
    if hit_clz == 0:
        filt[1, :, :] = 0
    else:
        filt[0, :, :] = 0

    downsampled_size = compute_downsampled_size((filt.shape[1], filt.shape[2]), POINT_DOWNSAMPLE[0])

    filt = torch.nn.functional.interpolate(filt.unsqueeze(0), (downsampled_size[0], downsampled_size[1]))

    return filt


def get_centroid_tensor_filter(clz, row, col, downsample, criteria, dataset):
    centroid_map = torch.zeros((len(enabled_hit_classes), image.shape[1], image.shape[2]))
    centroid_map[clz, row, col] = 1
    hits, _, _, _, _, _ = make_centroid_tensors(centroid_map, dataset.weed_classes, downsample=downsample)
    zeros = torch.zeros(hits.shape)
    zeros[clz, :, :] = 1
    hits = hits * zeros
    if criteria == 0:
        argmax = torch.argmax(hits[clz, :, :]).item()
        row = int(argmax / hits.shape[2])
        col = int(argmax % hits.shape[2])
        hits[clz, row, col] = 1
    else:
        hits[hits > 0.4] = 1
    hits[hits < 1] = 0
    return hits


def get_filter(x, y, radius, clz, hit_class, weed_categories, image_shape, downsample, min_threshold):
    point = Point(x, y, radius, 1.0, clz, 1.0, hit_class)
    dataset_label = DatasetLabel(torch.zeros((1, 1, image_shape[1], image_shape[2])), [[point]])
    hits_original, _, _, _, _, _ = make_centroid_tensors(dataset_label, weed_categories, downsample)

    zeros = torch.zeros(hits_original.shape)
    zeros[0, hit_class, :, :] = 1
    hits = hits_original * zeros

    return hits


def normalize_attributions(attributions):
    attributions = attributions - np.mean(attributions)
    attributions = attributions / (np.std(attributions) + 1e-5)
    attributions = np.clip(attributions, 0, 1)

    heatmap = np.uint8(255 * attributions)
    heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_TWILIGHT) / 255.0
    return heatmap


def vals(attributions):
    attributions /= np.max(attributions)
    attributions = np.clip(attributions, 0, 1)
    heatmap = np.uint8(255 * attributions)
    heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_BONE) / 255.0

    return heatmap


def de_extremified_attributions(attributions):
    std = np.std(attributions)
    # Dividing by STD and clipping so that extreme gradient values don't dominate the display
    attributions /= std
    attributions = np.clip(attributions, 0, 1)

    heatmap = np.uint8(255 * attributions)
    heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_BONE) / 255.0

    return heatmap


def naive_heatmap(attributions):
    attributions = attributions - np.min(attributions)
    heatmap = attributions / (np.max(attributions) + 1e-7)

    heatmap = np.uint8(255 * heatmap)
    heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_TWILIGHT_SHIFTED) / 255.0

    return np.clip(2 * heatmap.astype(np.float64), 0.0, 1.0)


def handle_centroids(
    pixel_row,
    pixel_col,
    model_wrapper,
    image,
    denormalized_img,
    hit_class,
    weed_categories,
    min_threshold,
    cc_labels,
    cc_label,
):
    # Workaround for a weird segfault
    cv2.connectedComponentsWithStats(np.ones((1, 1), dtype=np.uint8))

    filter_type = st.sidebar.radio("Filter type", ["hit_region", "hit_size", "square"])

    label_truth = None
    if filter_type == "square":
        size = st.sidebar.slider("Filter size in pixels", min_value=0, max_value=100, step=1, value=50)
        filt = get_square_filter(hit_clz=hit_class, row=pixel_row, col=pixel_col, filter_size=size, image=image)
    elif filter_type == "hit_size":
        filt = get_filter(
            y=pixel_row,
            x=pixel_col,
            radius=10,
            clz=None,
            hit_class=hit_class,
            weed_categories=weed_categories,
            downsample=POINT_DOWNSAMPLE[0],
            image_shape=image.size(),
            min_threshold=min_threshold,
        )
    else:
        label_truth = (cc_labels == cc_label).astype(np.float32)
        filt = get_hit_region(hit_class, label_truth).squeeze(0)

    model_wrapper.filter = filt
    layer = st.sidebar.radio("Layer of interest", ["Second to Last Conv", "First Conv"])
    if layer == "Second to Last Conv":
        gradcam = GuidedGradCam(model_wrapper, model_wrapper.model.second_to_last_conv_layers[0])
    else:
        gradcam = GuidedGradCam(model_wrapper, model_wrapper.model.first_conv_layers[0])
    input_tensor = image.unsqueeze(0).cuda()
    attributions = gradcam.attribute(input_tensor)

    filt = filt.squeeze(0)[hit_class, :, :]

    # Upsample filt to match the image, then normalize so max value is 1.
    filt = (
        Upsample()(filt.unsqueeze(0).unsqueeze(0), size=(denormalized_img.shape[1], denormalized_img.shape[2]))
        / filt.max()
    )

    masked_img = denormalized_img
    masked_img = np.moveaxis(masked_img, 0, 2)

    return masked_img, filt, attributions, label_truth


@st.cache(allow_output_mutation=True)
def make_model_predictions(hit_class: str, image: torch.Tensor, weed_categories: List[str], min_threshold: float):
    image = image.unsqueeze(0).cuda()
    predictions = model_wrapper.model.forward(image)
    hits = render_hits(predictions[3], (image.shape[2], image.shape[3]))

    return hits.squeeze(0)[hit_class, :, :].cpu().detach()


def display_image(denormalized_img):
    masked_img = denormalized_img * 2.0 / 3.0
    rearranged = np.moveaxis(masked_img, 0, 2)
    rearranged /= rearranged.max()
    st.image(rearranged, use_column_width=True)


model_id = st.sidebar.text_input("Model ID", value="423c244e-1a36-4f44-af0b-84e70f3ba05c")

image_key = st.sidebar.text_input(
    "S3 Image Key",
    value="media/slayer10/2022-12-08/brassica/predict_slayer10_row2_predict1_2022-12-08T19-04-46-188000Z.png",
)

best_checkpoint_path = download_model(model_id)
image_dir = download_image(image_key)

dataset = load_dataset(image_dir, [], [], [])
model_wrapper, model = load_model_wrapper(best_checkpoint_path)

image = dataset[0].image

# Tile height and width

tile_origins = tile_crop_origins(
    size=(image.shape[1], image.shape[2]), crop_size=(DEFAULT_HEIGHT, DEFAULT_WIDTH), tile=(1200, 1200),
)

tile_ind = st.sidebar.slider("Tile", min_value=0, max_value=len(tile_origins) - 1, step=1)
tile = tile_origins[tile_ind]

image = image[:, tile[0] : tile[0] + DEFAULT_HEIGHT, tile[1] : tile[1] + DEFAULT_WIDTH]

overlay_amount = st.sidebar.slider("Interpretation Overlay Amount", min_value=0.0, max_value=1.0, value=0.5, step=0.01)
hit_class = st.sidebar.radio("Hit Class", enabled_hit_classes, format_func=lambda clz: enabled_hit_class_names[clz])

min_threshold = st.sidebar.slider("Min threshold", min_value=0.0, max_value=1.0, value=0.5, step=0.01)

pixel_row = st.sidebar.slider("Row", min_value=0, max_value=image.shape[1] - 1, step=1, value=int(image.shape[1] / 2))
pixel_col = st.sidebar.slider(
    "Column", min_value=0, max_value=image.shape[2] - 1, step=1, value=int(image.shape[2] / 2)
)

display_options = st.sidebar.radio("Display Attributions", ["Value", "Adjusted by STD", "Normalized", "Naive"])

denormalized_img = np.clip(denormalize_image(image.squeeze()).numpy(), 0, 1.0)

hits = make_model_predictions(
    hit_class, image, weed_categories=["BROADLEAF", "GRASS", "PURSLANE", "OFFSHOOT"], min_threshold=min_threshold
)

hits2 = (hits > min_threshold).numpy().astype(np.uint8)

cc_num_labels, cc_labels, cc_stats, cc_centroids = cv2.connectedComponentsWithStats(hits2)


max_label = 0
max_area = 0
for i in range(len(cc_stats)):
    if cc_stats[i][4] > max_area:
        max_label = i
        max_area = cc_stats[i][4]

list_of_labels = [i for i in range(cc_num_labels) if i != max_label]
label = 0

if len(list_of_labels) > 0:
    label = st.sidebar.select_slider("Hit Region", options=list_of_labels)

masked_img, filt, attributions, label_truth = handle_centroids(
    hit_class=hit_class,
    pixel_row=pixel_row,
    pixel_col=pixel_col,
    model_wrapper=model_wrapper,
    image=image,
    denormalized_img=denormalized_img,
    weed_categories=["BROADLEAF", "GRASS", "PURSLANE", "OFFSHOOT"],
    min_threshold=min_threshold,
    cc_labels=cc_labels,
    cc_label=label,
)

# Post Processing
attributions = attributions.squeeze().permute(1, 2, 0).cpu().detach().numpy()
attr_min = attributions.min()
attr_mean = attributions.mean()
attr_max = attributions.max()

if display_options == "Value":
    heatmap = vals(attributions)
elif display_options == "Normalized":
    heatmap = normalize_attributions(attributions)
elif display_options == "Adjusted by STD":
    heatmap = de_extremified_attributions(attributions)
else:
    heatmap = naive_heatmap(attributions)

superimposed = st.sidebar.radio("Superimpose heatmap on image?", [True, False])

if superimposed:
    superimposed_img = overlay_amount * heatmap + (1 - overlay_amount) * masked_img
else:
    superimposed_img = heatmap

# Draw green cross-hairs and blue filter
draw_annotations = st.sidebar.radio("Draw guiding annotations?", [True, False])
if draw_annotations:
    if label_truth is not None:
        for label in list_of_labels:
            centroid = cc_centroids[label]
            superimposed_img = cv2.putText(
                superimposed_img,
                str(label),
                (int(centroid[0]) - 10, int(centroid[1]) - 10),
                fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                fontScale=1.0,
                color=(1, 0, 0),
                thickness=5,
            )

    else:
        superimposed_img[pixel_row, :, 1] = 1
        superimposed_img[:, pixel_col, 1] = 1
        superimposed_img[:, :, 2] += 0.2 * filt.squeeze(0).squeeze(0).numpy()

    hits = hits > min_threshold
    superimposed_img[:, :, 1] += 0.2 * hits.squeeze(0).squeeze(0).numpy()
    superimposed_img /= superimposed_img.max()

# Display
st.text("The sum of the network output in the blue area is %s" % model_wrapper.output_sum)
st.text("Attribution min: %s, mean: %s, max: %s" % (attr_min, attr_mean, attr_max))
st.image(superimposed_img, use_column_width="auto")

del masked_img, filt, denormalized_img, superimposed_img, attributions

import streamlit as st

from deeplearning.embeddings.io import EmbeddingDataset


def main():
    hdf5_file = st.sidebar.text_input("HDF5 file")

    if hdf5_file:
        dataset = EmbeddingDataset(hdf5_file)
        st.header("Dataset metadata")
        st.json(dataset.metadata.model_dump())
        number_items = len(dataset)

        select = st.sidebar.slider("index", min_value=0, max_value=number_items - 1)

        datapoint = dataset[select]
        st.header("Datapoint Metadata")
        st.json(datapoint.metadata.model_dump())
        st.header("Predicted Points")
        if datapoint.predictions_metadata:
            st.json([pred.model_dump() for pred in datapoint.predictions_metadata])
            st.markdown([embedding for embedding in datapoint.predictions_embeddings])
        else:
            st.markdown("No predicted points for this item")
        st.header("Embedding")
        st.markdown(f"Shape: {datapoint.embedding.shape}")
        st.markdown(datapoint.embedding)


if __name__ == "__main__":
    main()

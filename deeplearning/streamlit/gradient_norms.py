import streamlit as st
import pickle
import numpy as np
import pandas as pd

from deeplearning.utils.trainer import get_examples_with_highest_last_n_epochs

filepath = st.sidebar.text_input("Gradient Norm Pickle", value="gradient_norms.pickle")

with open(filepath, "rb") as fp:
    gradient_norms = pickle.load(fp)

st.header(f"{len(gradient_norms)} samples")

epochs = int(st.sidebar.text_input("How many epochs to consider?", value=10))
descending_keys = get_examples_with_highest_last_n_epochs(epochs, gradient_norms)
x = st.sidebar.slider("Top/Bottom x samples", value=3, min_value=1, max_value=10)

top_x = descending_keys[:x]
bottom_x = descending_keys[-x:]

line_chart = np.zeros((40, 2 * x))

col = []
for i in range(len(top_x)):
    key = top_x[i][0]
    value = gradient_norms[key]
    val = np.zeros((40))
    for j in range(len(value)):
        epoch = value[j][0]
        norm = value[j][1]
        val[epoch] = norm
    line_chart[:, i] = val
    col.append(key)

for i in range(len(bottom_x)):
    key = bottom_x[i][0]
    value = gradient_norms[key]
    val = np.zeros((40))
    for j in range(len(value)):
        epoch = value[j][0]
        norm = value[j][1]
        val[epoch] = norm
    line_chart[:, i] = val
    col.append(key)

chart_data = pd.DataFrame(line_chart, columns=col)
st.line_chart(chart_data)

new_file = st.sidebar.text_input("Filename", value=f"gradient_norms_last_{epochs}_epochs.pickle")
if st.button("Save gradient norms"):
    with open(new_file, "wb") as nfp:
        keys_to_scores = {key_score[0]: key_score[1] for key_score in descending_keys}
        pickle.dump(keys_to_scores, nfp)

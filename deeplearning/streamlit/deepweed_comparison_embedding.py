import json
import os
from copy import deepcopy
from typing import Any, Dict, List, Tuple

import cv2
import numpy as np
import streamlit as st

from deeplearning.utils.dataset import pil_loader
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

DESCENDING = "descending"
LAYOUT = [4, 4, 3, 1, 1, 2]


@st.cache_data
def load_image(filepath: str) -> np.array:
    if filepath.startswith("s3"):
        split_s3_path = filepath.split("/", maxsplit=3)
        assert len(split_s3_path) == 4  # Ex: ['s3:', '', 'maka-pono', 'media/test.png']
        image = S3CacheProxyClient(s3_cache_proxy_host=None).get_image(split_s3_path[2], split_s3_path[3])
    else:
        image = pil_loader(filepath)

    return np.array(image)


def display_row(row_columns: Any, row_data: Dict[str, Any], column_keys: List[str]) -> None:
    def draw_image_with_point(image: np.array, x: float, y: float, radius: int, thickness: int) -> None:
        try:
            cv2.circle(
                img=image, center=(int(x), int(y)), radius=radius, color=(255, 0, 0), thickness=thickness,
            )
            st.image(image, use_column_width=True)
        except cv2.error as ce:
            st.error(f"OpenCV error occurred: {ce}")
        except Exception as e:
            st.error(f"An unexpected error occurred: {e}")

    def crop_image_and_adjust_coordinates(
        image: np.array, center_x: float, center_y: float, crop_width: int, crop_height: int
    ) -> Tuple[np.array, Tuple[int, int]]:
        # Calculate the starting and ending coordinates
        start_x = max(int(center_x - crop_width / 2), 0)
        start_y = max(int(center_y - crop_height / 2), 0)
        end_x = min(int(center_x + crop_width / 2), image.shape[1])
        end_y = min(int(center_y + crop_height / 2), image.shape[0])

        # Crop the image
        cropped_image = image[start_y:end_y, start_x:end_x]

        # Adjust the coordinates
        new_x = center_x - start_x
        new_y = center_y - start_y

        return cropped_image, (new_x, new_y)

    img = load_image(row_data["image_url"])
    with row_columns[0]:  # Draw image.
        draw_image_with_point(deepcopy(img), row_data["x"], row_data["y"], radius=50, thickness=10)

    with row_columns[1]:  # Draw image.
        cropped_img, (new_x, new_y) = crop_image_and_adjust_coordinates(
            deepcopy(img), row_data["x"], row_data["y"], crop_width=200, crop_height=200
        )
        draw_image_with_point(cropped_img, new_x, new_y, radius=1, thickness=3)

    for column, key in zip(row_columns[2:], column_keys[2:]):
        with column:
            st.text(f"{row_data[key]}")


class DeepWeedEmbeddingComparator:
    def __init__(self, filepath: str) -> None:
        self.filepath: str = filepath
        self.all_data = self.get_recording_file()
        assert self.all_data is not None, "No data in the current JSON file."

    @st.cache_data  # Cache the serializable objects.
    def get_recording_file(_self) -> List[Dict[str, Any]]:
        if os.path.exists(_self.filepath):
            with open(_self.filepath, "r") as f:
                try:
                    data = json.load(f)  # Load existing JSON data.
                    return data
                except json.JSONDecodeError as e:
                    raise RuntimeError(f"JSON Decode Error in file {_self.filepath}: {e}")
                except Exception as e:
                    raise RuntimeError(f"An unexpected error occurred: {e}")
        else:
            raise FileNotFoundError(f"File does not exist at {_self.filepath}")

    def sorted_by_condition(self, order_mode: str = "ascending") -> None:
        self.all_data = sorted(
            self.all_data, key=lambda x: x["original_num_similar_points"], reverse=(order_mode == DESCENDING)
        )  # Reverse if descending


def main():
    st.set_page_config(layout="wide")

    filepath = st.sidebar.text_input("Filepath")
    if not filepath:
        st.info("Please specify Filepath")
        raise st.stop()

    deepweed_embedding_comparator = DeepWeedEmbeddingComparator(filepath=filepath)
    order_mode = st.sidebar.radio("Set ascending or descending order", ["ascending", "descending"])
    show_metadata = st.sidebar.radio("Show the metadata or not", [True, False])
    deepweed_embedding_comparator.sorted_by_condition(order_mode=order_mode)

    index = st.sidebar.slider(
        "Datapoint Index", min_value=0, max_value=len(deepweed_embedding_comparator.all_data) - 1, step=1, value=0
    )
    datapoint_information = deepweed_embedding_comparator.all_data[index]

    st.markdown("### Datapoint")
    table_columns = st.columns(spec=LAYOUT)  # Adjust column widths for alignment.
    column_name_list = ["image", "cropped_image", "image_url", "x", "y", "original_num_similar_points"]
    for table_column, column_name in zip(table_columns, column_name_list):
        table_column.write(column_name)
    display_row(row_columns=table_columns, row_data=datapoint_information, column_keys=column_name_list)

    if len(datapoint_information["similar_points"]) != 0:
        st.markdown("### Top Similar Points")
        table_columns = st.columns(spec=LAYOUT)  # Adjust column widths for alignment.
        column_name_list = ["image", "cropped_image", "image_url", "x", "y", "score"]

        for table_column, column_name in zip(table_columns, column_name_list):
            table_column.write(column_name)

        for row in datapoint_information["similar_points"]:
            row_columns = st.columns(spec=LAYOUT)
            display_row(row_columns=row_columns, row_data=row, column_keys=column_name_list)

    if show_metadata:
        st.markdown("### Metadata")
        st.write(datapoint_information)


if __name__ == "__main__":
    main()

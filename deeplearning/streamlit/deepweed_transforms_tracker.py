import copy
import datetime
import json
import os
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple

import cv2
import h5py
import numpy as np
import streamlit as st
import torch

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.model import Deepweed
from deeplearning.deepweed.remote_veselka_dataset import RemoteVeselkaDatasetClient, initialize_datasets
from deeplearning.scripts.utils.utils import get_dataset as download_dataset
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.images import denormalize_image
from deepweed_transforms import get_dataset
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass

# To prevent a crash when we log rank during random transforms if there's a failure.
if not torch.distributed.is_initialized():
    os.environ.setdefault("MASTER_ADDR", "localhost")
    os.environ.setdefault("MASTER_PORT", "12355")
    os.environ.setdefault("RANK", "0")
    os.environ.setdefault("WORLD_SIZE", "1")
    torch.distributed.init_process_group(backend="cpu:gloo,cuda:nccl", timeout=datetime.timedelta(hours=2))

DESCENDING = "descending"
SAVED_FILENAME = "loss.h5"


@st.cache_data
def get_datasets_and_metadata(dataset_id: str):
    dataset_parameters = {
        "config_dict": DeepweedConfig().to_json(),
        "train_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json",
        "validation_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/validation.json",
        "test_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json",
    }
    train_dataset, validation_dataset, test_dataset, _ = initialize_datasets(**dataset_parameters)

    datasets_dict = {
        "dataset": {"train": train_dataset, "validation": validation_dataset, "test": test_dataset},
        "metadata": {},
    }

    for dataset_key, dataset_value in datasets_dict["dataset"].items():
        datasets_dict["metadata"][dataset_key] = {}
        for sample in dataset_value:
            datasets_dict["metadata"][dataset_key][sample["uri"]] = sample

    return datasets_dict["metadata"]


class DeepWeedTransformsTracker:
    def __init__(self, model_id: str, phase: str) -> None:
        self.model_id: str = model_id
        self.phase: str = phase
        self.dataset_id, self.dataset, self.metadata_dict, self.all_data = self.get_recording_file(
            self.model_id, self.phase
        )
        assert self.all_data is not None, "No data in the current hdf5 file."
        self.length = len(self.all_data["epoch"])
        self.valid_indices = np.arange(self.length)
        self.losses_categories: List[str] = list(self.all_data["losses"].keys())
        self.min_epoch, self.max_epoch = int(min(self.all_data["epoch"])), int(max(self.all_data["epoch"]))
        self.min_step, self.max_step = int(min(self.all_data["step"])), int(max(self.all_data["step"]))

    @st.cache_resource  # Cache the unserializable objects.
    def get_recording_file(
        _self, model_id, phase,
    ) -> Tuple[str, RemoteVeselkaDatasetClient, Dict[str, Any], List[Any]]:
        filepath: str = f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/{phase}_record/{SAVED_FILENAME}"

        if not os.path.exists(filepath):
            download_records(item_id=model_id)

        with h5py.File(filepath, "r", libver="latest") as f:
            try:
                all_data: Dict[str, Any] = {
                    "dataset_id": f.attrs["dataset_id"],
                    "phase": f.attrs["phase"],
                    "image_uri": [],
                    "losses": defaultdict(np.array),
                    "epoch": [],
                    "step": [],
                    "transforms": [],
                }

                if not os.path.exists(f"{CARBON_DATA_DIR}/deeplearning/datasets/{all_data['dataset_id']}"):
                    download_dataset(dataset_id=all_data["dataset_id"])

                metadata_dict = get_datasets_and_metadata(dataset_id=all_data["dataset_id"])

                if os.path.exists(f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/sampled_filepaths.json"):
                    with open(f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/sampled_filepaths.json") as jf:
                        sampling_information = json.load(jf)

                    for key in sampling_information["filepaths"].keys():
                        if key in metadata_dict[all_data["phase"]]:
                            metadata_dict[all_data["phase"]][key]["sample_count"] = sampling_information["filepaths"][
                                key
                            ]

                if not st.session_state.get(
                    "remote_dataset_client"
                ):  # Cache the RemoteVeselkaDatasetClient to prevent gRPC errors.
                    st.info("Loading RemoteVeselkaDatasetClient")
                    st.session_state.remote_dataset_client = get_dataset(
                        dataset_id=all_data["dataset_id"], split=all_data["phase"]
                    )

                for data_key, data_value in f.items():
                    if data_key == "losses":
                        for loss_key, loss_value in data_value.items():
                            all_data[data_key][loss_key] = loss_value[:]

                    elif data_key == "image_uri":
                        all_data[data_key] = np.char.decode(data_value[:].astype("S"), encoding="utf-8")

                    else:  # for data_key in ["epoch", "step", "transforms"]
                        all_data[data_key] = data_value[:]

                return (
                    all_data["dataset_id"],
                    st.session_state.remote_dataset_client,
                    metadata_dict[all_data["phase"]],
                    all_data,
                )

            except OSError as e:
                st.warning(f"A HDF5 Decode Error occurred trying to download: {model_id}.")
                raise e

    def filtered_by_condition(self, conditions_dict: Dict[str, Any]) -> None:
        if conditions_dict:
            for condition_key, condition_value in conditions_dict.items():
                self.valid_indices = np.intersect1d(
                    self.valid_indices, np.where(self.all_data[condition_key] == condition_value)[0], assume_unique=True
                )

    def sorted_by_condition(self, condition: Optional[str], order_mode: str = "descending") -> None:
        if condition:
            compared_losses = self.all_data["losses"][condition][self.valid_indices]
            compared_losses_sorted_indices = np.argsort(compared_losses * (-1 if order_mode == DESCENDING else 1))
            self.valid_indices = self.valid_indices[compared_losses_sorted_indices]


def main():
    st.set_page_config(layout="wide")

    model_id = st.sidebar.text_input("Model ID")
    if not model_id:
        st.info("Please specify Model ID")
        raise st.stop()
    split = st.sidebar.radio("Split", ["train", "validation", "test"])

    deepweed_transforms_tracker = DeepWeedTransformsTracker(model_id=model_id, phase=split)

    conditions: List[str] = ["epoch", "step", "image_uri"]
    conditions_to_include: List[str] = st.sidebar.multiselect("Conditions to include", conditions)
    conditions_dict = {}
    for condition_to_include in conditions_to_include:
        condition = None
        if condition_to_include == "epoch":
            condition = st.sidebar.slider(
                "Epoch",
                min_value=deepweed_transforms_tracker.min_epoch,
                max_value=deepweed_transforms_tracker.max_epoch,
                step=1,
                value=0,
            )
        elif condition_to_include == "step":
            condition = st.sidebar.slider(
                "Step",
                min_value=deepweed_transforms_tracker.min_step,
                max_value=deepweed_transforms_tracker.max_step,
                step=1,
                value=0,
            )
        elif condition_to_include == "image_uri":
            condition = st.sidebar.text_input(condition_to_include)

        if condition is None or condition == "":
            st.info(f"Please specify {condition_to_include}")
            raise st.stop()
        conditions_dict[condition_to_include] = condition
    loss_condition = st.sidebar.radio("Loss Condition", [None] + deepweed_transforms_tracker.losses_categories)
    order_mode: str = "descending"
    if loss_condition:
        order_mode = st.sidebar.radio("Set ascending or descending order", ["ascending", "descending"])

    deepweed_transforms_tracker.filtered_by_condition(conditions_dict=conditions_dict)
    deepweed_transforms_tracker.sorted_by_condition(condition=loss_condition, order_mode=order_mode)
    assert len(deepweed_transforms_tracker.valid_indices) != 0, "No valid data."

    index = st.sidebar.slider(
        "Dataset Index", min_value=0, max_value=len(deepweed_transforms_tracker.valid_indices) - 1, step=1, value=0
    )
    mapping_index = deepweed_transforms_tracker.valid_indices[index]
    dataset_id = deepweed_transforms_tracker.dataset_id
    phase = deepweed_transforms_tracker.phase
    image_uri = deepweed_transforms_tracker.all_data["image_uri"][mapping_index]
    assert (
        deepweed_transforms_tracker.metadata_dict.get(image_uri, None) is not None
    ), f"Metadata for image_uri '{image_uri}' not found in metadata_dict. Ensure that metadata_dict contains the necessary information for all images."
    losses = {key: value[mapping_index] for key, value in deepweed_transforms_tracker.all_data["losses"].items()}
    epoch = deepweed_transforms_tracker.all_data["epoch"][mapping_index]
    step = deepweed_transforms_tracker.all_data["step"][mapping_index]
    transforms = json.loads(deepweed_transforms_tracker.all_data["transforms"][mapping_index])
    datapoint = deepweed_transforms_tracker.dataset.get_datapoint_from_metadata(
        metadata=deepweed_transforms_tracker.metadata_dict[image_uri]
    )

    st.write(datapoint.image_meta.__dict__)
    st.write(
        f"Original image sampled {deepweed_transforms_tracker.metadata_dict[image_uri].get('sample_count', 0)} times"
    )
    col1, col2 = st.columns(2)
    image_original = copy.deepcopy(datapoint.image)
    image_np = (image_original * 255).to(torch.uint8).permute(1, 2, 0).cpu().numpy().copy()
    col1.image(image_np, channels="rgb")
    col1.write("Original Image")
    image_transforms, dataset_label, _ = deepweed_transforms_tracker.dataset.preprocess_datapoint(
        datapoint.image.cuda(),
        [x.cuda() for x in datapoint.target_list if x is not None],
        copy.deepcopy(datapoint.points),
        datapoint.image_meta,
        set(
            [
                x
                for i, x in enumerate(deepweed_transforms_tracker.dataset.weed_classes)
                if datapoint.enabled_weed_point_classes[i] == 1
            ]
        ),
        set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
        datapoint.enabled_segm_classes,
        previous_transforms_list=transforms,
    )
    points = dataset_label.points[0]
    image_transforms = denormalize_image(copy.deepcopy(image_transforms))
    image_np = (image_transforms * 255).to(torch.uint8).permute(1, 2, 0).cpu().numpy().copy()
    for point in points:
        if point.hit_clz == HitClass.WEED:
            color = (255, 0, 0)
        else:
            color = (0, 255, 0)
        cv2.circle(image_np, (int(point.x), int(point.y)), 5, color, -1)
    col2.image(image_np, channels="rgb")
    col2.write("Transformed Image")
    sample_information = {
        "dataset_id": dataset_id,
        "phase": phase,
        "image_uri": image_uri,
        "losses": losses,
        "epoch": epoch,
        "step": step,
        "transforms": transforms,
    }
    st.write(sample_information)


if __name__ == "__main__":
    main()

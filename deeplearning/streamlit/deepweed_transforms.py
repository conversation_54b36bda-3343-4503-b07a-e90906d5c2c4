import copy
import threading

import cv2
import streamlit as st
import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.model import Deepweed
from deeplearning.deepweed.remote_veselka_dataset import (
    RemoteVeselkaDataset,
    RemoteVeselkaDatasetClient,
    RemoteVeselkaDatasetServer,
)
from deeplearning.scripts.utils.utils import get_crop_and_weed_names
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.images import denormalize_image
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass
from lib.common.veselka.client import VeselkaClient


@st.cache_resource  # Cache the unserializable objects.
def get_dataset(dataset_id: str, split: str) -> RemoteVeselkaDatasetClient:
    client = VeselkaClient()
    dataset_info = client.get_dataset(dataset_id)
    client.download_dataset(dataset_info)
    crops, weeds = get_crop_and_weed_names(dataset_id)

    stop_event = threading.Event()

    def init_dataset_server() -> None:
        veselka_dataset = RemoteVeselkaDataset(
            DeepweedConfig(),
            f"/data/deeplearning/datasets/{dataset_id}/{split}.json",
            mode=DatasetType.VALIDATION,
            segm_classes=tuple(["DRIPTAPE"]),
            weed_classes=tuple(weeds),
            crop_classes=tuple(crops),
        )
        remote_dataset_server = RemoteVeselkaDatasetServer({DatasetType.TRAIN: veselka_dataset})
        remote_dataset_server.run(blocking=False)
        stop_event.wait()

    remote_dataset_thread = threading.Thread(target=init_dataset_server, daemon=True)
    remote_dataset_thread.start()

    dataset = RemoteVeselkaDatasetClient(DatasetType.TRAIN, skip_normalization=True, dl_config=DeepweedConfig())

    return dataset


def main():
    st.set_page_config(layout="wide")
    dataset_id = st.sidebar.text_input("Dataset ID")
    if not dataset_id:
        st.info("Please specify Dataset ID")
        raise st.stop()

    split = st.sidebar.radio("Split", ["train", "validation", "test"])

    dataset = get_dataset(dataset_id, split)

    index = st.sidebar.slider("Dataset Index", min_value=0, max_value=len(dataset), step=1, value=0)

    dataset = RemoteVeselkaDatasetClient(DatasetType.TRAIN, skip_normalization=True, dl_config=DeepweedConfig())

    datapoint = dataset[index]
    st.write(datapoint.image_meta.__dict__)
    st.sidebar.button("Randomize")
    col1, col2 = st.columns(2)
    for i in range(6):

        image, dataset_label = dataset.preprocess_datapoint(
            datapoint.image.cuda(),
            [x.cuda() for x in datapoint.target_list if x is not None],
            copy.deepcopy(datapoint.points),
            datapoint.image_meta,
            set([x for i, x in enumerate(dataset.weed_classes) if datapoint.enabled_weed_point_classes[i] == 1]),
            set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
            datapoint.enabled_segm_classes,
        )
        points = dataset_label.points[0]
        image = image.cpu().numpy().copy()
        image = denormalize_image(image)

        image_np = (image * 255).to(torch.uint8).permute(1, 2, 0).cpu().numpy().copy()
        for point in points:
            if point.hit_clz == HitClass.WEED:
                color = (255, 0, 0)
            else:
                color = (0, 255, 0)
            cv2.circle(image_np, (int(point.x), int(point.y)), 5, color, -1)

        if i % 2 == 0:
            column = col1
        else:
            column = col2
        column.image(image_np, channels="rgb")


if __name__ == "__main__":
    main()

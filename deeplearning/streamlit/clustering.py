import os

import streamlit as st

from deeplearning.scripts.utils.utils import get_dataset
from deeplearning.streamlit.comparison_clustering import clustering
from deeplearning.streamlit.comparison_lookup import comparison_mining, embedding_lookup
from deeplearning.streamlit.comparison_lookup import get_filters as get_lookup_filters
from deeplearning.streamlit.comparison_lookup import get_image_urls, reset_page

IMAGE_SEARCH = "Image Search"
EXAMPLE_MINING = "Example Mining"
CLUSTERING = "Clustering"

OPTIONS = [IMAGE_SEARCH, EXAMPLE_MINING, CLUSTERING]


def main():
    st.header("Clustering Explorer")

    option = st.sidebar.radio("Please select an option", OPTIONS)
    evaluation_id = st.sidebar.text_input(
        "Evaluation Id", value="0fc9e4083b72aab9bda2c11a18776235", on_change=reset_page
    )
    if option in [IMAGE_SEARCH, EXAMPLE_MINING]:
        filters = get_lookup_filters()
        model_id = filters["model_id"]
        dataset_id = filters["dataset_id"]
        distance_metric = filters["distance_metric"]
        normalize_inputs = filters["normalize_inputs"]
        min_value = filters["min_value"]
        max_value = filters["max_value"]
        min_radius_px = filters["min_radius_px"]
        k = filters["k"]
        categories_to_include = filters["categories_to_include"]
        categories_to_exclude = filters["categories_to_exclude"]
        if dataset_id != "":
            if not os.path.exists(f"/data/deeplearning/datasets/{dataset_id}"):
                dataset_id, _ = get_dataset(dataset_id=dataset_id)
            if "dataset_id" not in st.session_state or st.session_state["dataset_id"] != dataset_id:
                st.session_state["dataset_id"] = dataset_id
                st.session_state["image_urls"] = get_image_urls(dataset_id)
        if option == IMAGE_SEARCH:
            embedding_lookup(
                evaluation_id,
                model_id,
                dataset_id,
                k,
                distance_metric,
                normalize_inputs,
                min_value,
                max_value,
                min_radius_px,
                categories_to_include,
                categories_to_exclude,
            )
        else:
            comparison_mining(
                evaluation_id,
                model_id,
                dataset_id,
                k,
                distance_metric,
                normalize_inputs,
                min_value,
                max_value,
                min_radius_px,
            )
    elif option == CLUSTERING:
        clustering(
            evaluation_id, st.session_state.get("embedding_to_search"), st.session_state.get("embedding_images_to_view")
        )


if __name__ == "__main__":
    main()

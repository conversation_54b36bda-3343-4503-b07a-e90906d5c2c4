import math

import streamlit as st
import torch

from deeplearning.deepweed.model import Deepweed


def make_model():
    model = Deepweed(4, 1)
    model.eval().cuda().requires_grad_(False)
    return model


def compute_input_grad(model):
    input = torch.randn(1, 3, 1800, 1696).cuda().requires_grad_(True)
    output = model(input)
    backbone = output.embedding_output[0]

    with torch.no_grad():
        label = backbone.clone()
        label[:, :, 20:21, 20:21] = torch.randn(1, 1, 1, 1)

    loss = ((backbone - label) ** 2).sum()
    loss.backward()
    grad = input.grad[0][0].cpu()

    return grad


st.title("Model Receptive Fields")

model = make_model()
grad = compute_input_grad(model)

grad[grad != 0] = 1
num_nonzero = grad.count_nonzero()
st.write(math.sqrt(num_nonzero))

st.image(grad.numpy(), use_column_width=True)

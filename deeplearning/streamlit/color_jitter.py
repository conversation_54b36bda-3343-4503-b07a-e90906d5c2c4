from itertools import permutations
import os
from PIL import Image
import streamlit as st
import torchvision.transforms.functional as TF


def find_images(root_dir):
    result = []
    for file_name in os.listdir(root_dir):
        if file_name.endswith(".png") and ".mask_" not in file_name:
            result.append(file_name)
    return result


st.title("Color Jitter")

root_dir = st.sidebar.text_input("Directory", value="/data/labels")
file_name = st.sidebar.selectbox("Image", find_images(root_dir))
img = Image.open(os.path.join(root_dir, file_name))

brightness_factor = st.sidebar.slider("Brightness", value=1.0, min_value=0.0, max_value=2.0)
contrast_factor = st.sidebar.slider("Contrast", value=1.0, min_value=0.0, max_value=2.0)
saturation_factor = st.sidebar.slider("Saturation", value=1.0, min_value=0.0, max_value=2.0)
hue_factor = st.sidebar.slider("Hue", value=0.0, min_value=-0.5, max_value=0.5)
transforms = [
    lambda img: TF.adjust_brightness(img, brightness_factor),
    lambda img: TF.adjust_contrast(img, contrast_factor),
    lambda img: TF.adjust_saturation(img, saturation_factor),
    lambda img: TF.adjust_hue(img, hue_factor),
]

all_permutations = list(permutations(transforms))
permutation = st.sidebar.slider("Permutation", value=0, min_value=0, max_value=len(all_permutations) - 1)
permuted_transforms = all_permutations[permutation]

st.header("Original")
st.image(img, use_column_width=True)

st.header("Transformed")
for t in permuted_transforms:
    img = t(img)
st.image(img, use_column_width=True)

from collections import defaultdict

import boto3

import streamlit as st

from tools.recorder.diagnostics_snapshot_reader import DiagnosticsSnapshotReader
from zip_download.zip_download_manager import LocalZip, run, Zip


def get_zips_from_s3(robot_id: str):
    # run("", "/data/diagnostics", "carbon-diagnostics", robot_id)
    s3 = boto3.resource("s3")
    diags = []
    for x in s3.Bucket("carbon-diagnostics").objects.all():
        if not x.key.startswith(f"diagnostics/{robot_id}/"):
            continue
        if x.key.endswith(".zip"):
            diags.append(x.key)
            
    return diags


def main():
    st.title("Trajectory Viewer")
    
    robot_id = st.text_input("Robot ID")
    diags = []
    if not robot_id:
        return
    diags = get_zips_from_s3(robot_id)
        
    st.text(diags)
    
    diag = st.selectbox("Select", diags)
        
    st.text(diag)
    
    
    
    
    
    # snap = DiagnosticsSnapshotReader("/data/AlexDedup-0711/row1/diagnostic_snapshots.carbon")
    # per_id_history = defaultdict(lambda: [])

    # frame = snap.next()
    # while frame:
    #     for traj in frame.trajectories:
    #         per_id_history[traj.id].append([traj.y_mm, traj.z_mm])
    #     frame = snap.next()
    

if __name__ == "__main__":
    main()
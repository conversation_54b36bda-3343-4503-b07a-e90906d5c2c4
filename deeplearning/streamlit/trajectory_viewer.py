from collections import defaultdict
import os
from typing import Any, Dict, List, <PERSON>ple
import zipfile
import plotly.graph_objects as go

import boto3
import pandas as pd

import streamlit as st

from tools.recorder.diagnostics_snapshot_reader import DiagnosticsSnapshotReader

# TODO [Augment]
# Plot embedding category distances https://plotly.com/python/radar-chart/
# Add a min/max time filter and show trajectory points that start in that range

def get_zips_from_s3(robot_id: str):
    s3 = boto3.resource("s3")
    diags = []
    for x in s3.Bucket("carbon-diagnostics").objects.all():
        if not x.key.startswith(f"diagnostics/{robot_id}/"):
            continue
        if x.key.endswith(".zip"):
            diags.append(x.key)
            
    return diags

def download_zip_from_s3(zip_path: str) -> str:
    s3 = boto3.client('s3')
    local_file = f"/data/{zip_path}"
    os.makedirs(os.path.dirname(local_file), exist_ok=True)
    if not os.path.exists(local_file):
        s3.download_file("carbon-diagnostics", zip_path, local_file)
    return local_file

def unzip(zip_path: str, robot_id: str) -> str:
    path = f"/data/traj_diagnostics/{os.path.splitext(zip_path)[0]}"
    os.makedirs(path, exist_ok=True)
    with zipfile.ZipFile(zip_path, "r") as zf:
        zf.extractall(path)
    return path

def list_files(unzipped_path: str) -> List[str]:
    files = []
    for (dirpath, _, filenames) in os.walk(unzipped_path):
        for f in filenames:
            files.append(os.path.join(os.path.join(dirpath, f)))
    
    return files

def get_trajectories(filepath: str, ind: int, min_start_time_ms: int = None, max_start_time_ms: int = None) -> Tuple[Dict[int, Dict[str, Any]], int, int]:
    snap = DiagnosticsSnapshotReader(filepath)
    per_id_history = defaultdict(list)
    trajectory_start_times = {}  # Track when each trajectory first appears
    all_timestamps = []  # Track all timestamps to find min/max

    if not snap.is_valid:
        return per_id_history, 0, 0

    frame = snap.next()
    while frame:
        frame_timestamp = frame.timestamp_ms if hasattr(frame, 'timestamp_ms') else 0
        all_timestamps.append(frame_timestamp)

        for traj in frame.trajectories:
            traj_key = f"{ind}_{traj.id}"

            # Record the start time for this trajectory if we haven't seen it before
            if traj_key not in trajectory_start_times:
                trajectory_start_times[traj_key] = frame_timestamp

            # Add trajectory point data
            per_id_history[traj_key].append({
                "y_mm": traj.y_mm,
                "z_mm": traj.z_mm,
                "timestamp_ms": frame_timestamp
            })
        frame = snap.next()

    # Calculate min/max timestamps
    min_time = min(all_timestamps) if all_timestamps else 0
    max_time = max(all_timestamps) if all_timestamps else 0

    # Filter trajectories based on their start times
    if min_start_time_ms is not None or max_start_time_ms is not None:
        filtered_history = {}
        for traj_key, points in per_id_history.items():
            start_time = trajectory_start_times.get(traj_key, 0)

            # Check if trajectory start time is within the specified range
            if min_start_time_ms is not None and start_time < min_start_time_ms:
                continue
            if max_start_time_ms is not None and start_time > max_start_time_ms:
                continue

            filtered_history[traj_key] = points

        return filtered_history, min_time, max_time

    return per_id_history, min_time, max_time

def get_trajectory_start_times(trajectory_by_id: Dict[int, Dict[str, Any]]) -> Dict[str, int]:
    """Extract the start time (first timestamp) for each trajectory"""
    start_times = {}
    for traj_id, points in trajectory_by_id.items():
        if points:
            start_times[traj_id] = points[0].get('timestamp_ms', 0)
    return start_times

def plot_trajectories(trajectory_by_id: Dict[int, Dict[str, Any]], keys: List[str], max_ymm: float, title: str):
    trajectories = {}
    
    for key in keys:
        val = trajectory_by_id[key]
        trajectories[key] = [(v["y_mm"], v["z_mm"]) for v in val if v["y_mm"] < max_ymm]

    fig = go.Figure()

    for key, traj in trajectories.items():
        x, y = zip(*traj)
        fig.add_trace(go.Scatter(x=x, y=y, mode='lines+markers', name=key))

    fig.update_layout(
        title=title,
        xaxis_title="y_mm",
        yaxis_title="z_mm",
        legend_title="Trajectories"
    )

    st.plotly_chart(fig)

def main():
    st.title("Trajectory Viewer")

    robot_id = st.text_input("Robot ID")
    diags = []
    if not robot_id:
        return
    diags = get_zips_from_s3(robot_id)

    # Sidebar controls
    num_trajectories = st.sidebar.number_input("Number of trajectories", min_value=1, max_value=100, value=10)
    max_ymm = st.sidebar.slider("Max y_mm position", min_value=1, max_value=2000, value=400) # 400 is roughly 15 inches

    diags = ["<select>"] + diags

    diag = st.selectbox("Select", diags)

    if diag == "<select>":
        return
    zip_path = download_zip_from_s3(diag)
    unzipped_path = unzip(zip_path, robot_id)
    st.text(f"Downloaded and unzipped {zip_path}")

    files = list_files(unzipped_path)

    # First pass: get trajectories and time range without filtering
    trajectory_by_id = {}
    file_min_time = float('inf')
    file_max_time = 0

    with st.spinner("Processing trajectories and scanning time range..."):
        ind = 0
        for file in files:
            if file.endswith("diagnostic_snapshots.carbon"):
                trajectories, min_time, max_time = get_trajectories(file, ind)
                trajectory_by_id.update(trajectories)
                if min_time > 0:
                    file_min_time = min(file_min_time, min_time)
                    file_max_time = max(file_max_time, max_time)
                ind += 1

    # Handle case where no timestamps were found
    if file_min_time == float('inf'):
        file_min_time = 0

    # Time filtering controls (now that we know the actual time range)
    st.sidebar.header("Trajectory Start Time Filter")
    enable_time_filter = st.sidebar.checkbox("Enable trajectory start time filtering")

    if file_min_time > 0 and file_max_time > 0:
        st.sidebar.write(f"File time range: {file_min_time} - {file_max_time} ms")

        if enable_time_filter:
            st.sidebar.write("Filter trajectories by when they first appear:")
            min_time_input = st.sidebar.slider(
                "Min start timestamp (ms)",
                value=file_min_time,
                min_value=file_min_time,
                max_value=file_max_time,
                step=1000,
                format="%d"
            )
            max_time_input = st.sidebar.slider(
                "Max start timestamp (ms)",
                value=file_max_time,
                min_value=file_min_time,
                max_value=file_max_time,
                step=1000,
                format="%d"
            )

            min_start_time_ms = int(min_time_input)
            max_start_time_ms = int(max_time_input)

            st.sidebar.success(f"Filtering trajectories that start between {min_start_time_ms} and {max_start_time_ms}")

            # Second pass: reprocess with filtering if needed
            if min_start_time_ms != file_min_time or max_start_time_ms != file_max_time:
                trajectory_by_id = {}
                with st.spinner("Applying time filters..."):
                    ind = 0
                    for file in files:
                        if file.endswith("diagnostic_snapshots.carbon"):
                            trajectories, _, _ = get_trajectories(file, ind, min_start_time_ms, max_start_time_ms)
                            trajectory_by_id.update(trajectories)
                            ind += 1
    else:
        st.sidebar.warning("No timestamps found in diagnostic files")

    if not trajectory_by_id:
        st.warning("No trajectories found with the current start time filters")
        return

    keys_and_counts = [(key, len(val)) for key, val in trajectory_by_id.items()]
    keys_and_counts = sorted(keys_and_counts, key=lambda x: x[1], reverse=True)
    keys = [keys_and_counts[i][0] for i in range(min(num_trajectories, len(trajectory_by_id)))]

    # Display statistics
    st.subheader("Trajectory Statistics")
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Trajectories", len(trajectory_by_id))
    with col2:
        total_points = sum(len(val) for val in trajectory_by_id.values())
        st.metric("Total Points", total_points)
    with col3:
        if enable_time_filter and (min_start_time_ms or max_start_time_ms):
            st.metric("Start Time Filter", "Active")
        else:
            st.metric("Start Time Filter", "Inactive")

    # Show trajectory start time information
    if trajectory_by_id:
        start_times = get_trajectory_start_times(trajectory_by_id)
        if start_times:
            all_start_times = list(start_times.values())
            min_start = min(all_start_times)
            max_start = max(all_start_times)

            st.subheader("Trajectory Start Time Range")
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Earliest Start", f"{min_start} ms")
            with col2:
                st.metric("Latest Start", f"{max_start} ms")

    plot_trajectories(trajectory_by_id, keys, max_ymm, "Trajectory Heights")
    

if __name__ == "__main__":
    main()
from collections import defaultdict
import os
from typing import Any, Dict, List
import zipfile
import plotly.graph_objects as go

import boto3
import pandas as pd

import streamlit as st

from tools.recorder.diagnostics_snapshot_reader import DiagnosticsSnapshotReader

# TODO [Augment]
# Plot embedding category distances https://plotly.com/python/radar-chart/
# Add a min/max time filter and show trajectory points that start in that range

def get_zips_from_s3(robot_id: str):
    s3 = boto3.resource("s3")
    diags = []
    for x in s3.Bucket("carbon-diagnostics").objects.all():
        if not x.key.startswith(f"diagnostics/{robot_id}/"):
            continue
        if x.key.endswith(".zip"):
            diags.append(x.key)
            
    return diags

def download_zip_from_s3(zip_path: str) -> str:
    s3 = boto3.client('s3')
    local_file = f"/data/{zip_path}"
    os.makedirs(os.path.dirname(local_file), exist_ok=True)
    if not os.path.exists(local_file):
        s3.download_file("carbon-diagnostics", zip_path, local_file)
    return local_file

def unzip(zip_path: str, robot_id: str) -> str:
    path = f"/data/traj_diagnostics/{os.path.splitext(zip_path)[0]}"
    os.makedirs(path, exist_ok=True)
    with zipfile.ZipFile(zip_path, "r") as zf:
        zf.extractall(path)
    return path

def list_files(unzipped_path: str) -> List[str]:
    files = []
    for (dirpath, _, filenames) in os.walk(unzipped_path):
        for f in filenames:
            files.append(os.path.join(os.path.join(dirpath, f)))
    
    return files

def get_trajectories(filepath: str, ind: int) -> Dict[int, Dict[str, Any]]:
    snap = DiagnosticsSnapshotReader(filepath)
    per_id_history = defaultdict(list)

    if not snap.is_valid:
        return per_id_history

    frame = snap.next()
    while frame:
        for traj in frame.trajectories:
            per_id_history[f"{ind}_{traj.id}"].append({
                "y_mm": traj.y_mm,
                "z_mm": traj.z_mm
            })
        frame = snap.next()
        
    return per_id_history

def plot_trajectories(trajectory_by_id: Dict[int, Dict[str, Any]], keys: List[str], max_ymm: float, title: str):
    trajectories = {}
    
    for key in keys:
        val = trajectory_by_id[key]
        trajectories[key] = [(v["y_mm"], v["z_mm"]) for v in val if v["y_mm"] < max_ymm]

    fig = go.Figure()

    for key, traj in trajectories.items():
        x, y = zip(*traj)
        fig.add_trace(go.Scatter(x=x, y=y, mode='lines+markers', name=key))

    fig.update_layout(
        title=title,
        xaxis_title="y_mm",
        yaxis_title="z_mm",
        legend_title="Trajectories"
    )

    st.plotly_chart(fig)

def main():
    st.title("Trajectory Viewer")
    
    robot_id = st.text_input("Robot ID")
    diags = []
    if not robot_id:
        return
    diags = get_zips_from_s3(robot_id)
    
    num_trajectories = st.sidebar.number_input("Number of trajectories", min_value=1, max_value=100, value=10)
    max_ymm = st.sidebar.slider("Max y_mm position", min_value=1, max_value=2000, value=400) # 400 is roughly 15 inches
            
    diags = ["<select>"] + diags
    
    diag = st.selectbox("Select", diags)
            
    if diag == "<select>":
        return
    zip_path = download_zip_from_s3(diag)
    unzipped_path = unzip(zip_path, robot_id)
    st.text(f"Downloaded and unzipped {zip_path}")
    
    files = list_files(unzipped_path)
    
    trajectory_by_id = {}
    
    ind = 0
    for file in files:
        if file.endswith("diagnostic_snapshots.carbon"):
            trajectory_by_id.update(get_trajectories(file, ind))
            ind += 1
            
    keys_and_counts = [(key, len(val)) for key, val in trajectory_by_id.items()]
    keys_and_counts = sorted(keys_and_counts, key=lambda x: x[1], reverse=True)
    keys = [keys_and_counts[i][0] for i in range(min(num_trajectories, len(trajectory_by_id)))]
        
    plot_trajectories(trajectory_by_id, keys, max_ymm, "Trajectory Heights")
    

if __name__ == "__main__":
    main()
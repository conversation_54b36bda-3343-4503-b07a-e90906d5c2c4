import math

from deeplearning.comparison.models.learnable_reduction_densenet_201_cosine_comparison_model import LearnableReductionDensenet201CosineComparisonModel
import streamlit as st
import torch


def make_model():
    model = LearnableReductionDensenet201CosineComparisonModel()
    model.eval().cuda().requires_grad_(False)
    return model


def compute_bb_grad(model):
    input = torch.randn(1, 3, 400, 400).cuda().requires_grad_(True)
    output = model(input)
    backbone_out = output[2]

    with torch.no_grad():
        label = backbone_out.clone()
        label[:, :, 10:11, 10:11] = torch.randn(1, 1, 1, 1)

    loss = ((backbone_out - label) ** 2).sum()
    loss.backward()
    grad = input.grad[0][0].cpu()

    return grad

def compute_prereduction_grad(model):
    input = torch.randn(1, 3, 400, 400).cuda().requires_grad_(True)
    output = model(input)
    out = output[3]

    with torch.no_grad():
        label = out.clone()
        label[:, :, 2:3, 2:3] = torch.randn(1, 1, 1, 1)

    loss = ((out - label) ** 2).sum()
    loss.backward()
    grad = input.grad[0][0].cpu()

    return grad


st.title("Model Receptive Fields")

model = make_model()

layer = st.sidebar.radio("Choose Layer", ["BB", "Prereduction"])
if layer == "BB":
    grad = compute_bb_grad(model)
elif layer == "Prereduction":
    grad = compute_prereduction_grad(model)

# grad[grad != 0] = 1
grad = torch.abs(grad)

grad = grad / torch.max(grad)
num_nonzero = grad.count_nonzero()
st.write(math.sqrt(num_nonzero))

st.image(grad.numpy(), use_column_width=True)

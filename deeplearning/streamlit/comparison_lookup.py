import datetime
import io
import json
import logging
import multiprocessing
import os
import random
import shutil
import zipfile
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import boto3
import cv2
import numpy as np
import pandas as pd
import requests
import streamlit as st
import torch
from PIL import Image

from cv.deepweed.deepweed_python import AtomicModel, ComparisonModel
from deeplearning.comparison.data_utils import cosine_similarity_normed_inputs, load_embeddings_from_torch
from deeplearning.comparison.datasets import get_transforms
from deeplearning.comparison.models.utils import ComparisonModelBase
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.utils.utils import get_dataset
from deeplearning.utils.dataset import pil_loader
from deeplearning.utils.download_utils import download_records
from lib.common.generation import GENERATION
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder, get_perf_log_line, set_verbosity
from lib.common.time.time import maka_control_timestamp_ms

set_verbosity(False)

# -------- Constants
TRANSFORMS = get_transforms()
COSINE_SIMILARITY = "Cosine Similarity"
EUCLIDEAN_DISTANCE = "Euclidean"
IMAGE_SEARCH = "Image Search"
COMPARISON_MINER = "Comparison Miner"

CARBON_CACHE_HOST = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")
PLANT_CAPTCHA_DIRECTORY = os.path.join(CARBON_DATA_DIR, "plant-captcha/")
PLANT_CAPTCHA_BUCKET_NAME = "carbon-plant-captcha"
PLANT_CAPTCHA_PREFIX = "plant-captcha/"

# ------- Page Functions


class S3Cache:
    def __init__(self, bucket_name: str, key: str, mode: str = "r"):
        self.bucket_name = bucket_name
        self.bucket = boto3.resource("s3").Bucket(self.bucket_name)
        self.key = key
        self.mode = mode

    def __enter__(self):
        filepath = os.path.join(CARBON_DATA_DIR, self.key)
        if os.path.exists(filepath):
            self.f = open(filepath, self.mode)
        else:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with st.spinner(f"`Downloading {self.key}`"):
                self.bucket.download_file(self.key, filepath)
                self.f = open(filepath, self.mode)

        return self.f

    def __exit__(self, execution_type, execution_value, execution_traceback):
        self.f.close()


if "page" not in st.session_state:
    st.session_state.page = 0

st.set_page_config(layout="wide")


def get_robot_options():
    client = boto3.client("s3")
    objects_list = [
        x["Prefix"][len(PLANT_CAPTCHA_PREFIX) : -1]
        for x in client.list_objects(Bucket=PLANT_CAPTCHA_BUCKET_NAME, Prefix=PLANT_CAPTCHA_PREFIX, Delimiter="/").get(
            "CommonPrefixes"
        )
    ]
    return objects_list


def get_captcha_options(robot):
    client = boto3.client("s3")
    prefix = PLANT_CAPTCHA_PREFIX + robot + "/"
    objects_list = [
        x["Key"][len(prefix) :]
        for x in client.list_objects(Bucket=PLANT_CAPTCHA_BUCKET_NAME, Prefix=prefix).get("Contents")
    ]
    objects_list = [x for x in objects_list if x[-4:] == ".zip"]

    # Ex: plant-captcha_slayer55_SnapPeasTest_2024-07-20_00-50-55.zip
    objects_with_time = {}
    for object in objects_list:
        date_strings = object[:-4].split("_")[-2:]  # ["2024-07-20", "00-50-55"]
        year, month, day, hour, minute, second = [
            int(x) for x in "-".join(date_strings).split("-")
        ]  # ["2024", "07", "20", "00", "50", "55"])

        date = datetime.datetime(year, month, day, hour, minute, second)
        objects_with_time[date] = object

    return [objects_with_time[x] for x in sorted(objects_with_time.keys(), reverse=True)]


class GridItem:
    def __init__(
        self,
        model_image: np.ndarray,
        image_id: str,
        image_dir: str,
        image_path: str,
        size: int,
        id: int,
        display_image: np.ndarray,
        annotation_id: int,
    ) -> None:
        self.model_image = model_image
        self.image_id = image_id
        self.image_dir = image_dir
        self.image_path = image_path
        self.size = size
        self.id = id
        self.display_image = display_image
        self.annotation_id = annotation_id


def reset_page() -> np.ndarray:
    st.session_state.page = 0


def download_map(inputs):
    image_url, image_id, x, y, use_cropped_cache, prep_image, id, annotation_id = inputs
    return download_image(image_url, image_id, x, y, use_cropped_cache, prep_image, id, annotation_id)


def get_captcha_metadata(robot: str, captcha_name: str) -> Dict[str, Dict[str, Any]]:
    trajectory_metadata = defaultdict(list)
    with S3Cache(bucket_name=PLANT_CAPTCHA_BUCKET_NAME, key=f"{PLANT_CAPTCHA_PREFIX}{robot}/{captcha_name}"):
        zipped = zipfile.ZipFile(os.path.join(PLANT_CAPTCHA_DIRECTORY, robot, captcha_name), "r")
        for file in zipped.namelist():
            if file.endswith(".meta.json"):
                metadata = json.loads(zipped.read(file).decode("utf-8"))
                trajectory_metadata[metadata["id"]].append(metadata)

    return trajectory_metadata


def download_captcha_image(
    robot: str, captcha_name: str, trajectory_id: str,
):
    components = trajectory_id.split("_")
    row_id = components[0]
    trajectory_id = components[1]
    images = []
    image_path = os.path.join(
        CARBON_DATA_DIR, f"deeplearning/tmp-streamlit-images/{robot}/{captcha_name}/{trajectory_id}.png"
    )
    os.makedirs(os.path.dirname(image_path), exist_ok=True)
    with duration_perf_recorder(PerfCategory.STREAMLIT, "download_captcha_image"):
        with S3Cache(bucket_name=PLANT_CAPTCHA_BUCKET_NAME, key=f"{PLANT_CAPTCHA_PREFIX}{robot}/{captcha_name}"):
            zipped = zipfile.ZipFile(os.path.join(PLANT_CAPTCHA_DIRECTORY, robot, captcha_name), "r")
            for file in zipped.namelist():
                if f"row{row_id}/{trajectory_id}" in file and file.endswith(".png"):
                    image = Image.open(io.BytesIO(bytearray(zipped.read(file))))
                    image.save(image_path, "PNG", bitmap_format="png")
                    size = image.size
                    image = np.array(Image.open(io.BytesIO(bytearray(zipped.read(file)))))
                    images.append(image)

        image_columns = st.columns(3)
        for ind, image in enumerate(images):
            with image_columns[ind % 3]:
                st.image(image)

    image = images[0]

    display_image = prepare_image(np.array(image), 200, 200, for_display=True)

    return GridItem(
        model_image=image,
        image_id="",
        image_dir="",
        image_path=image_path,
        size=size,
        id=f"{row_id}_{trajectory_id}",
        display_image=display_image,
        annotation_id="",
    )


def download_image(
    image_url: str,
    image_id: str,
    x: Optional[float] = None,
    y: Optional[float] = None,
    use_cropped_cache: bool = False,
    prep_image: bool = False,
    id: int = -1,
    annotation_id: int = -1,
) -> str:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "download_image"):
        image_dir = f"{os.getenv('MAKA_DATA_DIR', '/data')}/deeplearning/tmp_streamlit_images/{os.path.splitext(image_url.split('/')[-1])[0]}"
        os.makedirs(image_dir, exist_ok=True)
        split_s3_path = image_url.split("/")
        media_index = split_s3_path.index("media")
        relative_image_path = os.path.join(*split_s3_path[media_index + 1 :])
        image_s3_path = os.path.join("media", relative_image_path)

        filepath = split_s3_path[-1]
        if use_cropped_cache:
            filepath, file_extension = os.path.splitext(filepath)
            filepath += f"_x_{x}_y_{y}"
            filepath += file_extension

        image_path = os.path.join(image_dir, filepath)
        if os.path.exists(image_path):
            image = pil_loader(image_path)
            size = image.size
            x = 200 if use_cropped_cache else x
            y = 200 if use_cropped_cache else y
        else:
            query = f"http://{CARBON_CACHE_HOST}/maka-pono/{image_s3_path}"
            cropped = False
            if use_cropped_cache:
                cropped = True
                cropping_radius = 200
                max_height = 3000
                max_width = 4096
                # Hacky and dumb but quick
                if GENERATION.SLAYER.to_str() not in image_s3_path:
                    max_height = 4096
                    max_width = 3000
                x_origin = int(min(max(x - cropping_radius, 0), max_width - 2 * cropping_radius))
                y_origin = int(min(max(y - cropping_radius, 0), max_height - 2 * cropping_radius))

                x = x - x_origin
                y = y - y_origin

                height = int(2 * cropping_radius)
                width = int(2 * cropping_radius)
                query += f"?x={x_origin}&y={y_origin}&height={height}&width={width}"

            response = requests.get(query, timeout=30)
            tmp_image = Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB")
            size = tmp_image.size

            if size == 0 and cropped:
                query += "&break=1"
                print(f"Trying again! {query}")
                response = requests.get(query, timeout=30)
                tmp_image = Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB")
            tmp_image.save(image_path, "PNG", bitmap_format="png")
            image = pil_loader(image_path)
            size = image.size

        transform_image = np.array(image)
        display_image = (
            prepare_image(np.array(image), x, y, for_display=True)
            if x is not None and y is not None
            else np.array(image)
        )
        if prep_image:
            image = np.array(image)
            transform_image = prepare_image(image, x, y, for_display=False)
            display_image = prepare_image(image, x, y, for_display=True)
            display_image = cv2.circle(display_image * 255, (200, 200), 20, (255, 20, 147), 1) / 255
            transform_image = np.array(transform_image.cpu())

        return GridItem(
            model_image=transform_image,
            image_id=image_id,
            image_dir=image_dir,
            image_path=image_path,
            size=size,
            id=id,
            display_image=display_image,
            annotation_id=annotation_id,
        )


def in_bounds(x: float, y: float, border_to_extract_px: int, image_url: str) -> bool:
    height = 3000
    width = 4096
    if GENERATION.BUD.to_str() in image_url:
        height = 4096
        width = 3000

    return (
        x > border_to_extract_px
        and x < (width - border_to_extract_px)
        and y > border_to_extract_px
        and y < (height - border_to_extract_px)
    )


def get_annotations(
    dataset_id: str, image_url: Optional[str] = None, min_radius: float = 0
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    with duration_perf_recorder(
        PerfCategory.STREAMLIT, f"get_annotations_{'with' if image_url is not None else 'without'}_image"
    ):
        with open(f"/data/deeplearning/datasets/{dataset_id}/{st.session_state.get('dataset_split')}") as f:
            data = json.load(f)

        categories = data["categories"]

        if image_url is not None:
            image_id = [x["id"] for x in data["images"] if x["uri"] == image_url][0]
            annotations = [
                x for x in data["annotations"] if x["image_id"] == image_id and x["annotation_type"] == "point"
            ]
        else:
            annotations = [
                x
                for x in data["annotations"]
                if x["annotation_type"] == "point"
                and x.get("confidence", 0) == 2
                and in_bounds(x["x"], x["y"], x["radius"], st.session_state["image_urls"][x["image_id"]])
                and x["radius"] > min_radius
            ]
        return annotations, categories


def get_image_urls(dataset_id: str) -> Dict[str, str]:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "get_image_urls"):
        with open(f"/data/deeplearning/datasets/{dataset_id}/{st.session_state.get('dataset_split')}") as f:
            data = json.load(f)

        image_urls = {x["id"]: x["uri"] for x in data["images"]}
        image_urls_to_timestamp = {x["uri"]: x["captured_at"] for x in data["images"]}

        return image_urls, image_urls_to_timestamp


def get_crosshair_image(
    image_dir: str, x: int, y: int, color: Tuple[int] = (0, 0, 0), thickness: int = 10
) -> np.ndarray:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "get_crosshair_image"):
        image = cv2.imread(image_dir)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        cv2.line(image, (x, 0), (x, image.shape[0]), color, thickness)
        cv2.line(image, (0, y), (image.shape[1], y), color, thickness)

        for idx, annotation in enumerate(st.session_state["annotations"]):
            p_x, p_y = annotation["x"], annotation["y"]
            radius = 50
            ann_color = (255, 0, 0) if (p_x, p_y) != (x, y) else color

            image = cv2.circle(image, (p_x, p_y), radius, ann_color, 10)

            image = cv2.putText(
                image,
                str(idx),
                (int(p_x - radius), int(p_y - radius)),
                fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                fontScale=2.0,
                color=ann_color,
                thickness=5,
            )

        return np.array(image)


def prepare_image(
    image: np.ndarray, x: int, y: int, cropping_radius: int = 200, for_display: bool = False
) -> Union[torch.Tensor, np.ndarray]:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "prepare_image"):
        if for_display:
            padded_image_og = np.pad(
                image,
                [(cropping_radius, cropping_radius), (cropping_radius, cropping_radius), (0, 0),],
                mode="constant",
            )
            padded_x = int(x + cropping_radius)
            padded_y = int(y + cropping_radius)
            padded_image: np.ndarray = padded_image_og[
                padded_y - cropping_radius : padded_y + cropping_radius,
                padded_x - cropping_radius : padded_x + cropping_radius,
                :,
            ]

            padded_image = padded_image.astype(np.float32)
            padded_image /= 255
            return padded_image

        image_tensor = torch.tensor(image).permute(2, 0, 1).float()
        return image_tensor.unsqueeze(0)


def prepare_images():
    with duration_perf_recorder(PerfCategory.STREAMLIT, "prepare_images"):
        assert "images" in st.session_state, "No Images Selected"
        inp = []
        images = []
        for image_path, x, y in st.session_state["images"]:
            image = np.array(pil_loader(image_path))
            prepared = prepare_image(image, x, y)
            inp.append(prepared)

            image = prepare_image(image, x, y, for_display=True)
            image = cv2.circle(image * 255, (200, 200), 20, (255, 20, 147), 3) / 255
            images.append(np.array(image))

        return torch.cat(inp, dim=0), images


def get_model(model_id: str) -> ComparisonModelBase:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "get_model"):
        if f"model_{model_id}" in st.session_state:
            return st.session_state[f"model_{model_id}"]

        model_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/{model_id}")
        if os.path.exists(model_dir):
            logging.info("Model Already Downloaded")
        else:
            record_dir = os.path.join(CARBON_DATA_DIR, "deeplearning/models/models")
            download_records(model_id, save_dir=os.path.join(CARBON_DATA_DIR, "deeplearning/models"))
            shutil.copytree(os.path.join(record_dir, model_id), model_dir)
            shutil.rmtree(record_dir)

        path = os.path.join(model_dir, "trt_fp32.trt")
        model = ComparisonModel(AtomicModel(path, 0))

        st.session_state[f"model_{model_id}"] = model

        return model


def get_images_from_dataset(dataset_id: str) -> List[str]:
    with open(f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/{st.session_state.get('dataset_split')}") as f:
        a = json.load(f)

    image_ids = [image["id"] for image in a["images"]]
    return image_ids


def get_embeddings(
    dataset_id: str,
    model_id: str,
    normalize: bool,
    split: str,
    categories_to_include: Optional[List[str]] = None,
    categories_to_exclude: Optional[List[str]] = None,
) -> Tuple[np.ndarray, Dict[str, Any], Dict[str, str]]:
    categories_to_include = categories_to_include if categories_to_include is not None else []
    categories_to_exclude = categories_to_exclude if categories_to_exclude is not None else []
    with duration_perf_recorder(PerfCategory.STREAMLIT, "get_embeddings"):
        comparison_eval_dir = f"{CARBON_DATA_DIR}/deeplearning/comparison_evaluations/{model_id}"
        os.makedirs(comparison_eval_dir, exist_ok=True)

        key = f"embeddings_array_{dataset_id}_{split}_{normalize}_with{'-'.join(categories_to_include)}_without{'-'.join(categories_to_exclude)}"

        if key in st.session_state:
            logging.info("Cached Embeddings")
            embeddings_array = st.session_state[key]
            embeddings_data = st.session_state[f"embeddings_data_{key}"]
        else:
            image_ids = get_images_from_dataset(dataset_id)
            for image_id in image_ids:
                if not os.path.exists(os.path.join(comparison_eval_dir, f"{image_id}.pt")):
                    download_records(
                        bucket="carbon-comparison-evaluations",
                        s3_directory=f"production/comparison_evaluations/{model_id}",
                        save_dir=comparison_eval_dir,
                        item_id=f"{image_id}.pt",
                        skip_existing_files=True,
                    )

            all_embeddings = []
            all_metadata = []
            for eval in os.listdir(comparison_eval_dir):
                eval_image = os.path.splitext(eval)[0]
                if eval_image not in image_ids:
                    continue
                try:
                    embeddings = load_embeddings_from_torch(os.path.join(comparison_eval_dir, eval))
                except Exception as e:
                    logging.warn(f"Couldn't load embeddings from torch, skipping {eval}: {e}")
                    continue
                image_metadata = embeddings["image_meta"]
                embeddings_data = embeddings["embeddings_data"]

                metadata_to_add = []
                embeddings_to_add = []
                for ind, emb_data in enumerate(embeddings_data):
                    emb_data.update(image_metadata)
                    metadata_to_add.append(emb_data)
                    embeddings_to_add.append(embeddings["embeddings"][ind])

                if len(metadata_to_add) > 0:
                    all_metadata.extend(metadata_to_add)
                    all_embeddings.append(torch.stack(embeddings_to_add))

            torch_embeddings = torch.cat(all_embeddings)

            if normalize:
                embeddings_array = torch.nn.functional.normalize(torch_embeddings, dim=1).half().cuda()
            else:
                embeddings_array = torch_embeddings.half().cuda()

            embeddings_data = all_metadata
            if len(categories_to_include) or len(categories_to_exclude):
                embeddings_data_list = []
                embeddings_array_list = []
                for idx, embedding in enumerate(embeddings_data):
                    include = len(categories_to_include) == 0 or embedding["category"] in categories_to_include
                    exclude = len(categories_to_exclude) > 0 and embedding["category"] in categories_to_exclude
                    if include and not exclude:
                        embeddings_array_list.append(embeddings_array[idx])
                        embeddings_data_list.append(embedding)
                embeddings_data = embeddings_data_list
                embeddings_array = torch.stack(embeddings_array_list)
            for idx, embedding in enumerate(embeddings_data):
                embedding["id"] = idx

            st.session_state[key] = embeddings_array
            st.session_state[f"embeddings_data_{key}"] = embeddings_data

        return embeddings_array, embeddings_data


def find_nearest_neighbors_indices(
    query_tensor: torch.Tensor,
    db_tensors: torch.Tensor,
    embedding_data: List[Dict[str, Any]],
    k: int = 10,
    distance_metric: str = COSINE_SIMILARITY,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    min_size: Optional[float] = None,
) -> Tuple[List[int], List[int]]:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "find_nearest_neighbors_indices"):
        # Because similar points mean lower euclidean distance, but higher cosine similarity, multiplying by -1 when sorting the array allows us to
        # accurately sort the metrics from most similar to least similar
        if distance_metric == EUCLIDEAN_DISTANCE:
            distances = torch.cdist(query_tensor, db_tensors).squeeze().cpu().numpy()
            nearest_neighbors = np.argsort(distances).tolist()
            nearest_distances = [distances[x] for x in nearest_neighbors]
            closest_images = [embedding_data[x] for x in nearest_neighbors]

            filtered_distances = []
            filtered_images = []
            seen = set()

            for distance, image in zip(nearest_distances, closest_images):
                if min_value is not None and distance < min_value:
                    continue
                if max_value is not None and distance > max_value:
                    continue
                if min_size is not None and image["radius"] <= min_size:
                    continue
                key = (image["image_id"], image["x"], image["y"], image["radius"], image["category"])
                if key not in seen:
                    filtered_distances.append(distance)
                    filtered_images.append(image)
                    seen.add(key)

            return [filtered_images[:k],], [filtered_distances[:k],]

        elif distance_metric == COSINE_SIMILARITY:
            cossim = cosine_similarity_normed_inputs(query_tensor, db_tensors)
            distances = cossim.cpu().numpy()
            sorted_distances = np.argsort(-distances)

            return_distances = []
            return_images = []
            for ind in range(sorted_distances.shape[0]):
                model_neighbors = sorted_distances[ind]
                nearest_distances = [distances[ind][x] for x in model_neighbors]
                closest_images = [embedding_data[x] for x in model_neighbors]

                filtered_distances = []
                filtered_images = []
                seen = set()

                for distance, image in zip(nearest_distances, closest_images):
                    if min_value is not None and distance < min_value:
                        continue
                    if max_value is not None and distance > max_value:
                        continue
                    if min_size is not None and image["radius"] <= min_size:
                        continue
                    key = (image["image_id"], image["x"], image["y"], image["radius"], image["category"])
                    if key not in seen:
                        filtered_distances.append(distance)
                        filtered_images.append(image)
                        seen.add(key)

                return_distances.append(filtered_distances[:k])
                return_images.append(filtered_images[:k])

            return return_images, return_distances


def retrieve_inputs(data: List[Dict[str, Any]]) -> List[np.ndarray]:
    with duration_perf_recorder(PerfCategory.STREAMLIT, "retrieve_inputs"):
        grid = []
        threadpool = multiprocessing.pool.ThreadPool(64)
        inputs = []
        for data_ind in range(len(data)):
            dictionary = data[data_ind]
            image_id = dictionary["image_id"]
            x = dictionary["x"]
            y = dictionary["y"]

            url = dictionary["image_url"]
            inputs.append((url, image_id, int(x), int(y), True, True, data_ind, dictionary.get("id", -1)))

        grid = threadpool.map(download_map, inputs)
        return grid


def get_annotation_dict(suffix: str, annotation: Dict[str, Any], category: str) -> Dict[str, Any]:
    return {
        f"image_{suffix}": annotation["image_id"],
        f"x_{suffix}": annotation["x"],
        f"y_{suffix}": annotation["y"],
        f"radius_{suffix}": annotation["radius"],
        f"category_{suffix}": category,
        f"label_{suffix}": annotation.get("label_id", ""),
    }


def fetch_data_from_annotations(annotation_idx, data):
    with duration_perf_recorder(PerfCategory.STREAMLIT, "fetch_data_from_annotations"):
        annotation = st.session_state["annotations"][annotation_idx]
        category = [x["name"] for x in data["categories"] if x["id"] == annotation["category_id"]][0]
        return get_annotation_dict("one", annotation, category)


def fetch_data_from_annotations_by_id(annotation_id, data, image_id):
    with duration_perf_recorder(PerfCategory.STREAMLIT, "fetch_data_from_annotations_by_id"):
        annotation = [
            item
            for item in st.session_state["annotations"]
            if item["id"] == annotation_id and item["image_id"] == image_id
        ][0]
        category = [x["name"] for x in data["categories"] if x["id"] == annotation["category_id"]][0]
        return get_annotation_dict("one", annotation, category)


def fetch_data_from_embeddings_by_embedding_id(embedding_id, evaluation_id):
    with duration_perf_recorder(PerfCategory.STREAMLIT, "fetch_data_from_embeddings_by_id"):
        embedding = [
            item for item in st.session_state[f"embeddings_data_{evaluation_id}"] if item["id"] == embedding_id
        ][0]
        return get_annotation_dict("one", embedding, embedding["category"])


def generate_table(
    closest_images: List[Dict[str, Any]],
    *,
    annotation_idx: Optional[int] = None,
    annotation_ids: Optional[List[int]] = None,
    image_ids: Optional[List[str]] = None,
    evaluation_id: Optional[str] = None,
    embedding_ids: Optional[List[str]] = None,
):
    with duration_perf_recorder(PerfCategory.STREAMLIT, "generate_table"):
        assert (
            annotation_idx is not None
            or (annotation_ids is not None and image_ids is not None)
            or (evaluation_id is not None and embedding_ids is not None)
        )
        with open(
            f"/data/deeplearning/datasets/{st.session_state['dataset_id']}/{st.session_state.get('dataset_split')}"
        ) as f:
            data = json.load(f)
        table = []
        for idx, image in enumerate(closest_images):
            if annotation_idx is not None:
                image1 = fetch_data_from_annotations(annotation_idx, data)
            elif annotation_ids is not None and image_ids is not None:
                image1 = fetch_data_from_annotations_by_id(annotation_ids[idx], data, image_ids[idx])
            else:
                image1 = fetch_data_from_embeddings_by_embedding_id(embedding_ids[idx], evaluation_id)
            row = image1.copy()
            image2 = get_annotation_dict("two", image, image["category"])
            row.update(image2)
            table.append(row)

        return pd.DataFrame(table)


def populate_grid(
    dataset_id: str,
    model_id: str,
    split: str,
    k: int = 10,
    distance_metric: str = COSINE_SIMILARITY,
    annotation_idx: Optional[int] = None,
    normalize: bool = True,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    min_size: Optional[float] = None,
    categories_to_include: Optional[List[str]] = None,
    categories_to_exclude: Optional[List[str]] = None,
):
    categories_to_include = categories_to_include if categories_to_include is not None else []
    categories_to_exclude = categories_to_exclude if categories_to_exclude is not None else []
    st.text(f"split {st.session_state['dataset_split']}")
    with duration_perf_recorder(PerfCategory.STREAMLIT, "populate_grid"):
        # Loading Model
        logging.info("Loading Model")
        model = get_model(model_id)

        # Getting Embeddings For Image
        logging.info("Getting Embeddings")
        inputs, images = prepare_images()

        with torch.no_grad():
            embeddings = model.infer(inputs, inputs.shape[-1] // 2, inputs.shape[-2] // 2).embedding[0].squeeze()
            if len(embeddings.shape) == 1:
                embeddings = embeddings.unsqueeze(0)
            embedding = torch.mean(embeddings, dim=0).unsqueeze(0).half()

            if normalize:
                embedding = torch.nn.functional.normalize(embedding, dim=1)

        # Retrieving Embeddings From Evaluations
        logging.info("Getting Evaluation Embeddings")
        evaluation_embeddings, embeddings_data = get_embeddings(
            dataset_id, model_id, normalize, split, categories_to_include, categories_to_exclude
        )
        # Finding Indices of K-Nearest Neighbors
        logging.info("Getting Neighbors")
        closest_images, nearest_neighbor_distances = find_nearest_neighbors_indices(
            embedding,
            evaluation_embeddings,
            embeddings_data,
            k=k,
            distance_metric=distance_metric,
            min_value=min_value,
            max_value=max_value,
            min_size=min_size,
        )

        closest_images = closest_images[0]
        nearest_neighbor_distances = nearest_neighbor_distances[0]
        st.session_state["embedding_to_search"] = embedding
        st.session_state["embedding_images_to_view"] = images
        # Getting Image Grid
        logging.info("Retrieving Images")
        if len(closest_images[0]) > 0:
            grid = retrieve_inputs(closest_images)

            tuples_grid = [(im, f"OG Image {idx+1}") for idx, im in enumerate(images)]
            for idx, grid_item in enumerate(grid):
                tuples_grid.append(
                    (grid_item.display_image, f"{idx} - {nearest_neighbor_distances[idx]: .3f} : {grid_item.image_id}")
                )

            st.session_state["grid"] = tuples_grid

            if annotation_idx is not None and len(st.session_state["images"]) == 1:
                df = generate_table(closest_images, annotation_idx=annotation_idx)
                st.session_state["table"] = df
            else:
                st.session_state["table"] = None

        st.session_state.page = 1


def add_image(image_path: str, x: float, y: float):
    with duration_perf_recorder(PerfCategory.STREAMLIT, "add_image"):
        reset_page()
        if "images" not in st.session_state:
            st.session_state["images"] = set()

        if (image_path, x, y) not in st.session_state["images"]:
            st.session_state["images"].add((image_path, x, y))
            st.warning("Added Image")
        else:
            st.warning("Duplicate Image Detected, Not Added")


def reset_images():
    reset_page()
    st.session_state["images"] = set()
    st.session_state["annotations"] = []
    st.warning("List Cleared")


def embedding_lookup(  # noqa: C901
    model_id: str,
    dataset_id: str,
    split: str,
    k: int,
    distance_metric: str,
    normalize_inputs: bool,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    min_size: Optional[float] = None,
    categories_to_include: Optional[List[str]] = None,
    categories_to_exclude: Optional[List[str]] = None,
) -> None:
    categories_to_include = categories_to_include if categories_to_include is not None else []
    categories_to_exclude = categories_to_exclude if categories_to_exclude is not None else []
    source_of_image = st.sidebar.radio("Source of image", ["Captcha", "Random", "Specific"])
    image_key = ""
    if source_of_image == "Captcha":
        robot = st.sidebar.selectbox("Robot", get_robot_options())
        captcha_name = st.sidebar.selectbox("Captcha zip", get_captcha_options(robot))
        trajectory_metadata = get_captcha_metadata(robot, captcha_name)
        trajectory_id = st.sidebar.selectbox("Trajectory ID", list(trajectory_metadata.keys()))
        st.session_state["captcha_id"] = captcha_name
        st.session_state["trajectory_id"] = trajectory_id
        st.session_state["trajectory_metadata"] = trajectory_metadata
    elif source_of_image == "Random":
        if "image_urls" in st.session_state and "rand_image_key" not in st.session_state:
            image_key = random.choice(list(st.session_state["image_urls"].values()))
            st.session_state["rand_image_key"] = image_key
            logging.info(f"Feeling lucky! {image_key}")
        elif "rand_image_key" in st.session_state:
            image_key = st.session_state["rand_image_key"]
    else:
        image_key = st.sidebar.text_input(
            "S3 Image Key",
            value="s3://maka-pono/media/slayer17/2023-05-01/509153c4-8e52-4418-ae6c-c93d69484bd7/predict_slayer17_row1_predict3_2023-05-01T23-42-34-728000Z.png",
            on_change=reset_page,
        )
        if "rand_image_key" in st.session_state:
            del st.session_state["rand_image_key"]

    if captcha_name == "" and trajectory_id == "" and image_key == "":
        return

    if captcha_name != "" and trajectory_id != "":
        downloaded_item = download_captcha_image(robot, captcha_name, trajectory_id)
        col1, col2, col3 = st.sidebar.columns(3)
        image_path = downloaded_item.image_path
        size = downloaded_item.size

        metadata = st.session_state["trajectory_metadata"][downloaded_item.id][0]

        if dataset_id != "":
            st.session_state["annotations"], st.session_state["categories"] = get_annotations(dataset_id)

        col1.button("Add", on_click=add_image, args=[downloaded_item.image_path, metadata["yPx"], metadata["xPx"]])
        col2.button(
            "Search",
            on_click=populate_grid,
            args=[
                dataset_id,
                model_id,
                split,
                int(k),
                distance_metric,
                None,
                normalize_inputs,
                min_value,
                max_value,
                min_size,
                categories_to_include,
                categories_to_exclude,
            ],
        )

        col3.button("Reset", on_click=reset_images)

        if "images" in st.session_state:
            st.sidebar.text(f"{len(st.session_state['images'])} images")
            for (_im, _x, _y) in st.session_state["images"]:
                st.sidebar.text(f"image {_x} {_y} {_im}")

        if st.session_state.page == 1:
            columns = st.columns(5)
            # Making Grid of Images
            logging.info("Creating Grid")

            if "grid" in st.session_state:
                for idx, grid_item in enumerate(st.session_state["grid"]):
                    with columns[idx % len(columns)]:
                        st.image(grid_item[0], caption=grid_item[1])

            if "table" in st.session_state and st.session_state["table"] is not None:
                graph = st.expander("Comparison Label Table")
                graph.markdown(st.session_state["table"].style.hide_index().to_html(), unsafe_allow_html=True)
    elif image_key != "":
        downloaded_item = download_image(image_key, "")
        image_path = downloaded_item.image_path
        size = downloaded_item.size

        if dataset_id != "":
            st.session_state["annotations"], st.session_state["categories"] = get_annotations(dataset_id, image_key)

            annotation_idx = st.sidebar.number_input(
                label="Annotation",
                min_value=0,
                max_value=len(st.session_state["annotations"]) - 1,
                step=1,
                on_change=reset_page,
            )
            x = st.session_state["annotations"][annotation_idx]["x"]
            y = st.session_state["annotations"][annotation_idx]["y"]
        else:
            st.session_state["annotations"] = []
            x = st.sidebar.slider("x", 0, size[0] - 1, value=size[0] // 2, on_change=reset_page)
            y = st.sidebar.slider("y", 0, size[1] - 1, value=size[1] // 2, on_change=reset_page)
            annotation_idx = None

        st.sidebar.image(get_crosshair_image(image_path, x, y))

        col1, col2, col3 = st.sidebar.columns(3)

        col1.button("Add", on_click=add_image, args=[image_path, x, y])

        col2.button(
            "Search",
            on_click=populate_grid,
            args=[
                dataset_id,
                model_id,
                int(k),
                distance_metric,
                annotation_idx,
                normalize_inputs,
                min_value,
                max_value,
                min_size,
                categories_to_include,
                categories_to_exclude,
            ],
        )

        col3.button("Reset", on_click=reset_images)

        if "images" in st.session_state:
            st.sidebar.text(f"{len(st.session_state['images'])} images")
            for (_im, _x, _y) in st.session_state["images"]:
                st.sidebar.text(f"image {_x} {_y} {_im}")

        # Main Screen
        if st.session_state.page == 0:
            st.image(get_crosshair_image(image_path, x, y))
        elif st.session_state.page == 1:

            columns = st.columns(5)
            # Making Grid of Images
            logging.info("Creating Grid")

            if "grid" in st.session_state:
                for idx, grid_item in enumerate(st.session_state["grid"]):
                    with columns[idx % len(columns)]:
                        st.image(grid_item[0], caption=grid_item[1])

            if "table" in st.session_state and st.session_state["table"] is not None:
                graph = st.expander("Comparison Label Table")
                graph.markdown(st.session_state["table"].style.hide_index().to_html(), unsafe_allow_html=True)


def run_model_on_images(model_id: str, grid: List[GridItem], normalize_inputs: bool) -> torch.Tensor:
    model = get_model(model_id)
    batch_size = 16
    model_embeddings = []

    for ind in range(0, len(grid), batch_size):
        batch = grid[ind : min(ind + batch_size, len(grid))]
        batch_images = torch.Tensor(
            [b.model_image.squeeze(0) if len(b.model_image.shape) == 4 else b.model_image for b in batch]
        )

        with duration_perf_recorder(PerfCategory.STREAMLIT, "run_model_on_batch"):
            with torch.no_grad():
                embeddings = model(batch_images.cuda()).squeeze()
                if len(embeddings.shape) == 1:
                    embeddings = embeddings.unsqueeze(0)
                embeddings = embeddings.half()

                if normalize_inputs:
                    embeddings = torch.nn.functional.normalize(embeddings, dim=1)

                model_embeddings.append(embeddings)

    return torch.cat(model_embeddings, dim=0)


def get_category_buckets(
    items: List[Dict[str, Any]], class_func: Callable[[Dict[str, Any]], str]
) -> List[List[Dict[str, Any]]]:
    category_buckets = defaultdict(list)

    for item in items:
        name = class_func(item).lower()
        if name in ["broadleaf", "grass", "purslane", "offshoot"]:
            category_buckets[name].append(item)
        else:
            category_buckets["crop"].append(item)

    return category_buckets


def get_categories(dataset_id: str) -> Dict[str, Any]:
    with open(f"/data/deeplearning/datasets/{dataset_id}/{st.session_state.get('dataset_split')}") as f:
        data = json.load(f)

    return data["categories"]


def comparison_mining(
    model_id: str,
    dataset_id: str,
    k: int,
    distance_metric: str,
    normalize_inputs: bool,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    min_size: Optional[float] = None,
    split: str = "",
) -> None:
    display_images = st.sidebar.checkbox("Display images", value=True)

    logging.info("Getting Evaluation Embeddings")
    evaluation_embeddings, embeddings_data = get_embeddings(dataset_id, normalize_inputs, split=split)

    if "annotations_comparison_mining" not in st.session_state:
        st.session_state["annotations_comparison_mining"] = defaultdict(dict)
    if len(st.session_state["annotations_comparison_mining"][dataset_id]) == 0:
        annotations_list, categories = get_annotations(dataset_id, min_radius=min_size)
        st.session_state["annotations"] = annotations_list
        category_id_to_class = {category["id"]: category["name"] for category in categories}

        category_buckets = get_category_buckets(
            annotations_list, lambda annotation: category_id_to_class[annotation["category_id"]],
        )

        st.session_state["annotations_comparison_mining"][dataset_id] = category_buckets

    points_to_retrieve = []

    for _, bucket in st.session_state["annotations_comparison_mining"][dataset_id].items():
        items = random.sample(
            bucket, min(k // len(st.session_state["annotations_comparison_mining"][dataset_id]), len(bucket))
        )
        for item in items:
            item["image_url"] = st.session_state["image_urls"][item["image_id"]]
        points_to_retrieve.extend(items)

    grid_of_model_input = retrieve_inputs(points_to_retrieve)
    all_model_embeddings = run_model_on_images(model_id, grid_of_model_input, normalize_inputs)

    logging.info("Getting Neighbors")
    closest_images, nearest_neighbor_distances = find_nearest_neighbors_indices(
        all_model_embeddings,
        evaluation_embeddings,
        embeddings_data,
        k=k,
        distance_metric=distance_metric,
        min_value=min_value,
        max_value=max_value,
        min_size=min_size,
    )

    all_items = []

    for idx, grid_item in enumerate(grid_of_model_input):
        if len(closest_images[idx]) == 0:
            continue
        random_selected_index = random.randint(0, len(closest_images[idx]) - 1)
        closest_image = closest_images[idx][random_selected_index]
        distance = nearest_neighbor_distances[idx][random_selected_index]
        all_items.append({"grid_item": grid_item, "distance": distance, "closest_image": closest_image})

    grid_comps = retrieve_inputs([item["closest_image"] for item in all_items])
    grid_id_to_image = {}
    for grid_item in grid_comps:
        grid_id_to_image[grid_item.id] = (grid_item.display_image, grid_item.image_id)

    if display_images:
        for idx, item in enumerate(all_items):
            grid_item = item["grid_item"]
            col1, col2 = st.columns(2)
            col1.image(grid_item.display_image, caption=grid_item.image_id)
            col2.image(
                grid_id_to_image[idx][0], caption=f"Distance: {item['distance']}, Image id: {grid_id_to_image[idx][1]}"
            )

    annotation_ids = [item["grid_item"].annotation_id for item in all_items]
    image_ids = [item["grid_item"].image_id for item in all_items]
    st.session_state["table_df"] = generate_table(
        [item["closest_image"] for item in all_items], annotation_ids=annotation_ids, image_ids=image_ids
    )

    graph = st.expander("Comparison Label Table")
    graph.markdown(st.session_state["table_df"].style.hide_index().to_html(), unsafe_allow_html=True)

    filepath = "/data/comparison_mined_examples.csv"

    st.session_state["table_df"].to_csv(filepath, index=False)
    with open(filepath, "r") as f:
        st.download_button("Download comparisons", f, file_name="comparison_mined_examples.csv")


def get_filters(dataset_split):
    model_id = st.sidebar.text_input("Model Id", value="7ec76682c175ec684af161274980c827", on_change=reset_page)
    dataset_id = st.sidebar.text_input("Dataset of Image", value="0cf50352-20b0-42dd-a515-73e4080df6a1")
    if dataset_id != "":
        if not os.path.exists(f"/data/deeplearning/datasets/{dataset_id}"):
            dataset_id, _ = get_dataset(dataset_id=dataset_id)
        if (
            "dataset_id" not in st.session_state
            or st.session_state["dataset_id"] != dataset_id
            or st.session_state.get("dataset_split") != dataset_split
        ):
            st.session_state["dataset_id"] = dataset_id
            st.session_state["dataset_split"] = dataset_split
            st.session_state["image_urls"], st.session_state["image_url2captured_at"] = get_image_urls(dataset_id)
    k = int(st.sidebar.text_input("Num Neighbors", value="10", on_change=reset_page))

    distance_metric = st.sidebar.radio(
        "Distance Metric", options=[EUCLIDEAN_DISTANCE, COSINE_SIMILARITY], index=1, on_change=reset_page
    )

    normalize_inputs = distance_metric == COSINE_SIMILARITY

    min_value = None
    max_value = None
    if distance_metric == COSINE_SIMILARITY:
        min_value = st.sidebar.number_input("Display results greater than", value=0.0)
        max_value = st.sidebar.number_input("Display results less than", value=1.0)

    min_radius_px = st.sidebar.number_input("Min Radius (mm)", value=5) * (200 / 25.4)

    categories = [item["name"] for item in get_categories(dataset_id) if item["type"] == "point"]
    categories_to_include = st.sidebar.multiselect("Categories to include", categories)
    categories_to_exclude = st.sidebar.multiselect("Categories to exclude", categories)

    return {
        "model_id": model_id,
        "dataset_id": dataset_id,
        "distance_metric": distance_metric,
        "normalize_inputs": normalize_inputs,
        "min_value": min_value,
        "max_value": max_value,
        "min_radius_px": min_radius_px,
        "k": k,
        "categories_to_include": categories_to_include,
        "categories_to_exclude": categories_to_exclude,
    }


def main():  # noqa: C901
    start = maka_control_timestamp_ms()
    st.title("Comparison Model Embedding Tool")
    mode = st.sidebar.radio("Mode", [IMAGE_SEARCH, COMPARISON_MINER])
    split = st.sidebar.radio("Split", ["test.json", "validation.json", "train.json"])
    # -------------Screen
    # Sidebar Input
    filters = get_filters(split)

    model_id = filters["model_id"]
    dataset_id = filters["dataset_id"]
    distance_metric = filters["distance_metric"]
    normalize_inputs = filters["normalize_inputs"]
    min_value = filters["min_value"]
    max_value = filters["max_value"]
    min_radius_px = filters["min_radius_px"]
    k = filters["k"]

    if dataset_id == "":
        return

    if mode == IMAGE_SEARCH:
        with duration_perf_recorder(PerfCategory.STREAMLIT, "embedding_lookup"):
            embedding_lookup(
                model_id, dataset_id, split, k, distance_metric, normalize_inputs, min_value, max_value, min_radius_px,
            )
    else:
        with duration_perf_recorder(PerfCategory.STREAMLIT, "comparison_mining"):
            comparison_mining(
                model_id,
                dataset_id,
                k,
                distance_metric,
                normalize_inputs,
                min_value,
                max_value,
                min_radius_px,
                split=split,
            )

    st.text(f"Tool ran in {maka_control_timestamp_ms() - start} ms")
    st.text(f"Runtime: {get_perf_log_line(PerfCategory.STREAMLIT)}")


if __name__ == "__main__":
    main()

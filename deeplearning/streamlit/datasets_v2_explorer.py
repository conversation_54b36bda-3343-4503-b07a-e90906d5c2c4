import datetime
import io
import json
import os
import sys
from typing import List, Optional

import pandas as pd
import plotly.express as px
import pydantic
import pydeck as pdk
import streamlit as st
from PIL import Image, ImageDraw

from lib.common.s3_cache_proxy.client import S3CacheProxyClient

CARBON_ROBOT_DIR = os.getenv("MAKA_ROBOT_DIR")
sys.path.append(CARBON_ROBOT_DIR)


CARBON_DATA_DIR = os.getenv("MAKA_DATA_DIR")
S3_CACHE_PROXY_SERVICE_HOST = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")


class Point(pydantic.BaseModel):
    x: float
    y: float
    radius: float
    point_category_id: str
    confidence: int
    label: str


class Datapoint(pydantic.BaseModel):
    image_id: str
    label_id: Optional[str]
    detection_id: Optional[str]

    crop_id: str
    robot_id: str
    captured_at: datetime.datetime
    geohash: str
    points: List[Point]
    uri: str
    latitude: float
    longitude: float
    augmented: bool

    point_categories: List[str]


class Dataset(pydantic.BaseModel):
    datapoints: List[Datapoint]
    crop_ids: List[str]
    robot_ids: List[str]
    start_date: datetime.date
    end_date: datetime.date
    point_category_ids: List[str]


class DatasetExplorer:
    def __init__(self):
        assert CARBON_DATA_DIR is not None, "Please set the MAKA_DATA_DIR environment variable"
        self._dataset_dir = os.path.join(CARBON_DATA_DIR, "deeplearning/datasets")

        self._s3_cache_proxy_client = S3CacheProxyClient(S3_CACHE_PROXY_SERVICE_HOST)

    @staticmethod
    def _load_dataset(filepath) -> Dataset:  # noqa: C901
        # Load dataset
        datapoints = []
        start_date = None
        end_date = None
        robot_ids = []
        crop_ids = []
        point_category_ids = []

        if filepath.endswith("json"):
            with open(filepath, "r") as f:
                dataset = json.load(f)

            image_id2points = {}

            for annotation_dict in dataset.get("annotations", []):
                if annotation_dict["annotation_type"] == "point":

                    point = Point(point_category_id=annotation_dict["label"], **annotation_dict)

                    if annotation_dict["image_id"] not in image_id2points:
                        image_id2points[annotation_dict["image_id"]] = []

                    image_id2points[annotation_dict["image_id"]].append(point)

            for datapoint_dict in dataset.get("images", []):
                datapoint = Datapoint(
                    image_id=datapoint_dict["id"],
                    label_id=datapoint_dict.get("label_id"),
                    detection_id=datapoint_dict.get("detection_id"),
                    crop_id=datapoint_dict["crop_id"],
                    robot_id=datapoint_dict["robot_id"],
                    captured_at=datetime.datetime.fromtimestamp(datapoint_dict["captured_at"] / 1000),
                    geohash=datapoint_dict["geohash"],
                    points=image_id2points.get(datapoint_dict["id"], []),
                    uri=datapoint_dict["uri"],
                    latitude=datapoint_dict["latitude"],
                    longitude=datapoint_dict["longitude"],
                    augmented=datapoint_dict.get("label_id") is None,
                    point_categories=datapoint_dict.get("point_categories"),
                )
                datapoints.append(datapoint)

                if start_date is None or start_date > datapoint.captured_at.date():
                    start_date = datapoint.captured_at.date()
                if end_date is None or end_date < datapoint.captured_at.date():
                    end_date = datapoint.captured_at.date()

                if datapoint.robot_id not in robot_ids:
                    robot_ids.append(datapoint.robot_id)
                if datapoint.crop_id not in crop_ids:
                    crop_ids.append(datapoint.crop_id)

        if filepath.endswith("jsonl"):
            with open(filepath) as f:
                for line in f.readlines():
                    datapoint_dict = json.loads(line)

                    print(datapoint_dict)

                    datapoint = Datapoint(
                        image_id=datapoint_dict["image_id"],
                        label_id=datapoint_dict.get("label_id"),
                        detection_id=datapoint_dict.get("detection_id"),
                        crop_id=datapoint_dict["crop_id"],
                        robot_id=datapoint_dict["robot_id"],
                        captured_at=datetime.datetime.fromtimestamp(datapoint_dict["captured_at"] / 1000),
                        geohash=datapoint_dict["geohash"],
                        points=datapoint_dict["points"],
                        uri=datapoint_dict["uri"],
                        latitude=datapoint_dict["latitude"],
                        longitude=datapoint_dict["longitude"],
                        augmented=datapoint_dict.get("label_id") is None,
                        point_categories=datapoint_dict.get("certified_point_category_ids"),
                    )
                    datapoints.append(datapoint)

                    if start_date is None or start_date > datapoint.captured_at.date():
                        start_date = datapoint.captured_at.date()
                    if end_date is None or end_date < datapoint.captured_at.date():
                        end_date = datapoint.captured_at.date()

                    if datapoint.robot_id not in robot_ids:
                        robot_ids.append(datapoint.robot_id)
                    if datapoint.crop_id not in crop_ids:
                        crop_ids.append(datapoint.crop_id)

                    for point in datapoint.points:
                        if point.point_category_id not in point_category_ids:
                            point_category_ids.append(point.point_category_id)

        dataset = Dataset(
            datapoints=datapoints,
            crop_ids=crop_ids,
            robot_ids=robot_ids,
            start_date=start_date,
            end_date=end_date,
            point_category_ids=point_category_ids,
        )

        return dataset

    def run(self) -> None:
        st.set_page_config(page_title="Dataset V2 Explorer", layout="wide")
        st.sidebar.title("Dataset Explorer")
        st.sidebar.divider()

        datasets = os.listdir(self._dataset_dir)
        datasets.sort(key=lambda x: os.path.getmtime(os.path.join(self._dataset_dir, x)), reverse=True)
        dataset_id = st.sidebar.selectbox("Dataset ID", datasets, index=None, placeholder="")

        if dataset_id is None:
            return None

        dataset_dir = os.path.join(self._dataset_dir, dataset_id)
        filenames = sorted(os.listdir(dataset_dir))
        filename = st.sidebar.selectbox("Select a file", filenames, index=None)

        if filename is None:
            return None

        dataset = self._load_dataset(os.path.join(dataset_dir, filename))

        column_1, column_2 = st.sidebar.columns(2)
        start_date = column_1.date_input("Start Date", value=dataset.start_date)
        end_date = column_2.date_input("End Date", value=dataset.end_date)
        crops = st.sidebar.multiselect("Crops", dataset.crop_ids)
        categories = st.sidebar.multiselect("Categories", dataset.point_category_ids)
        implements = st.sidebar.multiselect("Implements", dataset.robot_ids)
        predictions = st.sidebar.checkbox("Include model labels")
        labels = st.sidebar.checkbox("Include human labels")

        # Apply filters
        filtered_datapoints = []
        for datapoint in dataset.datapoints:
            if datapoint.captured_at.date() < start_date or datapoint.captured_at.date() > end_date:
                continue
            if len(crops) > 0 and datapoint.crop_id not in crops:
                continue
            if len(implements) > 0 and datapoint.robot_id not in implements:
                continue
            if not predictions and datapoint.detection_id is not None:
                continue
            if not labels and datapoint.label_id is not None:
                continue
            if len(categories) > 0 and datapoint.point_category_id not in categories:
                continue

            filtered_datapoints.append(datapoint)

        tab_1, tab_2, tab_3 = st.tabs(["Image", "Map", "Timeline"])

        with tab_1:
            if len(filtered_datapoints) == 0:
                st.write("No datapoints found")
            else:

                datapoint_index = st.slider("Image Selection", min_value=1, max_value=len(filtered_datapoints))

                datapoint = filtered_datapoints[datapoint_index - 1]

                bucket, key = self._s3_cache_proxy_client.split_uri(datapoint.uri)
                image = Image.open(io.BytesIO(self._s3_cache_proxy_client.get(bucket, key)))
                draw = ImageDraw.Draw(image)

                # Draw a dot (circle) for each point
                for point in datapoint.points:
                    # Calculate the bounding box for the circle
                    top_left = (point.x - point.radius, point.y - point.radius)
                    bottom_right = (point.x + point.radius, point.y + point.radius)

                    # Draw the circle (fill='red' or change color as needed)
                    draw.ellipse([top_left, bottom_right], outline="yellow", width=4)

                    center_dot_radius = 20  # Adjust the size of the center dot as needed
                    center_top_left = (point.x - center_dot_radius, point.y - center_dot_radius)
                    center_bottom_right = (point.x + center_dot_radius, point.y + center_dot_radius)

                    # Draw the center dot
                    if point.confidence == 0:
                        draw.ellipse([center_top_left, center_bottom_right], outline="yellow", fill="red", width=4)
                    else:
                        draw.ellipse([center_top_left, center_bottom_right], outline="yellow", width=4)

                    center_dot_radius = 4  # Adjust the size of the center dot as needed
                    center_top_left = (point.x - center_dot_radius, point.y - center_dot_radius)
                    center_bottom_right = (point.x + center_dot_radius, point.y + center_dot_radius)

                    draw.ellipse([center_top_left, center_bottom_right], fill="yellow")  # Center point in blue

                st.image(image, use_column_width="auto")

                with st.expander("Datapoint JSON"):
                    st.json(datapoint.dict())

        with tab_2:

            latitude = []
            longitude = []
            metadata = []
            for datapoint in filtered_datapoints:
                latitude.append(datapoint.latitude)
                longitude.append(datapoint.longitude)

                data = {
                    "crop_id": datapoint.crop_id,
                    "robot_id": datapoint.robot_id,
                }

                line = ""
                for key, value in data.items():
                    line += f"{key}={value}\n"

                metadata.append(line)

            data = pd.DataFrame({"latitude": latitude, "longitude": longitude, "metadata": metadata})

            # Map the points
            st.pydeck_chart(
                pdk.Deck(
                    map_style="mapbox://styles/mapbox/light-v9",
                    initial_view_state=pdk.ViewState(
                        latitude=data["latitude"].mean(), longitude=data["longitude"].mean(), zoom=4,
                    ),
                    layers=[
                        pdk.Layer(
                            "ScatterplotLayer",
                            data=data,
                            get_position="[longitude, latitude]",
                            get_color="[200, 30, 0, 160]",
                            get_radius=2,
                            radius_min_pixels=2,
                            radius_max_pixels=3,
                            pickable=True,
                        ),
                    ],
                    tooltip={"text": "{metadata}"},
                )
            )

        with tab_3:
            # Sample list of datetime.date objects
            dates_list = [datapoint.captured_at.date() for datapoint in filtered_datapoints]

            # Create a pandas DataFrame from the date list and count occurrences
            df = pd.DataFrame(dates_list, columns=["date"])
            df["count"] = 1  # Add a column to count occurrences
            df_grouped = df.groupby("date").sum().reset_index()  # Group by date and count occurrences

            # Create a complete timeline from start to stop date
            all_dates = pd.date_range(start=start_date, end=end_date).date
            timeline_df = pd.DataFrame(all_dates, columns=["date"])

            # Merge the counted dates with the complete timeline, filling missing days with 0 count
            timeline_df = timeline_df.merge(df_grouped, on="date", how="left").fillna(0)
            timeline_df["count"] = timeline_df["count"].astype(int)

            # Plotting with Plotly
            fig = px.bar(
                timeline_df,
                x="date",
                y="count",
                labels={"date": "Date", "count": "Count"},
                title="Image Counts per Day",
            )

            # Display the plot in Streamlit
            st.plotly_chart(fig)


if __name__ == "__main__":
    app = DatasetExplorer()
    app.run()

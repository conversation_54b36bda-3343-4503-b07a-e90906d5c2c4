import cv2
import streamlit as st
import torch

from deeplearning.furrows.config import FurrowsConfig
from deeplearning.furrows.datasets import FurrowsDataset, dataset_transforms
from deeplearning.utils.images import denormalize_image
from lib.common.veselka.client import VeselkaClient

enabled_line_classes = ["96a2abbd-38af-408c-b4b8-aaacd08aa5e7"]
config = FurrowsConfig()


@st.cache_resource  # Cache the unserializable objects.
def get_dataset(dataset_id: str, split: str) -> FurrowsDataset:
    client = VeselkaClient()
    dataset_info = client.get_dataset(dataset_id)
    client.download_dataset(dataset_info)

    dataset = FurrowsDataset(
        f"/data/deeplearning/datasets/{dataset_id}/{split}.jsonl",
        detection_classes=enabled_line_classes,
        transforms=dataset_transforms(
            config.train_height, config.train_width, dilate_mask=config.dilate_mask, training=True
        ),
    )

    return dataset


def main():
    st.set_page_config(layout="wide")
    dataset_id = st.sidebar.text_input("Dataset ID")
    if not dataset_id:
        st.info("Please specify Dataset ID")
        raise st.stop()

    split = st.sidebar.radio("Split", ["train", "validation", "test"])

    dataset = get_dataset(dataset_id, split)

    index = st.sidebar.slider("Dataset Index", min_value=0, max_value=len(dataset), step=1, value=0)

    datapoint = dataset[index]
    st.write(datapoint.metadata.__dict__)
    st.sidebar.button("Randomize")
    col1, col2 = st.columns(2)
    for i in range(5):

        datapoint = dataset[index]
        dataset.preprocess_datapoint(datapoint)
        lines = datapoint.lines
        image = datapoint.image
        image = denormalize_image(image)

        image_np = (image * 255).to(torch.uint8).permute(1, 2, 0).cpu().contiguous().numpy()
        for line in lines:
            cv2.line(image_np, (int(line.x1), int(line.y1)), (int(line.x2), int(line.y2)), (255, 0, 0), 5)

        if i % 2 == 0:
            column = col1
        else:
            column = col2
        column.image(image_np, channels="rgb")


if __name__ == "__main__":
    main()

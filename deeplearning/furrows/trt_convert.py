from typing import Any, Dict, Optional

import fire
import numpy as np
import tensorrt as trt
import torch
from torch2trt import torch2trt

from deeplearning.furrows.config import FurrowsConfig
from deeplearning.furrows.datasets import FurrowsDataset
from deeplearning.furrows.model_output import FurrowsOutputFactory
from deeplearning.furrows.models.rgb import FurrowsRGB
from deeplearning.model_io import load_pytorch_model, peek_pytorch_metadata, save_tensorrt_model
from deeplearning.model_io.metadata import ModelMetadata

MAX_CALIBRATION_ITEMS = 500
MIN_CALIBRATION_ITEMS = 50


class TrtConvert(object):
    def __init__(
        self,
        calibration_dataset: Optional[FurrowsDataset] = None,
        validation_dataset: Optional[FurrowsDataset] = None,
        metadata: Optional[ModelMetadata] = None,
        model: Optional[torch.nn.Module] = None,
    ) -> None:
        self._calibration_dataset = calibration_dataset
        self._validation_dataset = validation_dataset
        if self._calibration_dataset is not None:
            assert len(self._calibration_dataset), "Provided dataset does not have any labelled data."
        self._ckpt_metadata = metadata
        self._model = model

    def load_model_cli(self, checkpoint: str, config_dict: Dict[str, Any]) -> "TrtConvert":
        config = FurrowsConfig.from_dict(config_dict)
        return self.load_model(checkpoint, config)

    def load_model(self, checkpoint: str, config: FurrowsConfig) -> "TrtConvert":
        self._ckpt_metadata = peek_pytorch_metadata(checkpoint)

        segm_classes = self._ckpt_metadata.segm_classes
        assert segm_classes is not None
        template_model = FurrowsRGB(len(segm_classes))
        self._model, _ = load_pytorch_model(template_model, checkpoint, strict=False)

        return self

    def _nandiff(self, input1: torch.Tensor, input2: torch.Tensor) -> torch.Tensor:
        is_nan = torch.isnan(input1)
        input1[is_nan] = 0

        is_nan = torch.isnan(input2)
        input2[is_nan] = 0

        diff = (input1 - input2).abs()
        return diff.sum() / diff.count_nonzero()

    def convert(
        self,
        max_batch_size: int,
        save_to: str,
        fp16: bool = False,
        int8: bool = False,
        calibration_epochs: int = 1,
        calibration_batch_size: int = 1,
        calibration_cache_input_file: Optional[str] = None,
        calibration_cache_output_file: Optional[str] = None,
        calibration_only: bool = False,
        error_metrics: bool = False,
    ) -> None:
        assert self._ckpt_metadata is not None
        assert self._model is not None
        assert self._calibration_dataset is not None
        assert self._ckpt_metadata.input_size is not None

        # Load the model.
        self._model.eval().cuda()

        if int8 and len(self._calibration_dataset) < MIN_CALIBRATION_ITEMS:
            print("WARNING: int8 calibration may not generalize well on small datasets")

        dataset = self._calibration_dataset

        # Prepare test tensor.
        datapoint = dataset.preprocess_datapoint(dataset[0])
        test_tensor = torch.unsqueeze(datapoint.image, 0).repeat(max_batch_size, 1, 1, 1).cuda()

        output_pyt = []
        if error_metrics and self._validation_dataset is not None:
            for i in range(min(20, len(self._validation_dataset))):
                image = (
                    self._validation_dataset.preprocess_datapoint(self._validation_dataset[i]).image.unsqueeze(0).cuda()
                )
                out = FurrowsOutputFactory.unpack(self._model(image)).detach().cpu()
                output_pyt.append(out)

        # Convert to FP16 if needed.
        if fp16:
            self._model.half()
            test_tensor = test_tensor.half()

        # Run the conversion.
        with torch.no_grad():
            trt_model = torch2trt(
                self._model,
                [test_tensor],
                fp16_mode=fp16,
                int8_mode=int8,
                int8_calib_dataset=dataset,
                int8_calib_batch_size=calibration_batch_size,
                int8_calib_cache_input_path=calibration_cache_input_file,
                int8_calib_cache_output_path=calibration_cache_output_file,
                max_batch_size=max_batch_size,
                max_workspace_size=1 << 20,
                log_level=trt.Logger.INFO,
                strict_type_constraints=False,
            )

        if error_metrics and self._validation_dataset is not None:
            # Free model memory to support creating a trt context
            del self._model

            output_trt = []
            for i in range(min(20, len(self._validation_dataset))):
                if fp16:
                    image = (
                        self._validation_dataset.preprocess_datapoint(self._validation_dataset[i])
                        .image.half()
                        .unsqueeze(0)
                    )
                else:
                    image = self._validation_dataset.preprocess_datapoint(self._validation_dataset[i]).image.unsqueeze(
                        0
                    )
                image = image.cuda()
                out_trt = FurrowsOutputFactory.unpack(trt_model(image)).cpu()
                output_trt.append(out_trt)

            mask_diffs = []
            direction_diffs = []
            for i in range(len(output_pyt)):
                mask_diffs.append(self._nandiff(output_trt[i].mask, output_pyt[i].mask))
                direction_diffs.append(self._nandiff(output_trt[i].direction, output_pyt[i].direction))

            mask_diffs_np = np.array(mask_diffs)
            print(
                "Mask absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                % (
                    np.mean(mask_diffs_np),
                    np.median(mask_diffs_np),
                    np.min(mask_diffs_np),
                    np.max(mask_diffs_np),
                    np.percentile(mask_diffs_np, 99),
                )
            )

            direction_diffs_np = np.array(direction_diffs)
            print(
                "Direction absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                % (
                    np.mean(direction_diffs_np),
                    np.median(direction_diffs_np),
                    np.min(direction_diffs_np),
                    np.max(direction_diffs_np),
                    np.percentile(direction_diffs_np, 99),
                )
            )

        if calibration_only:
            return

        # Save the resulting converted model.
        torch_dtype = torch.float16 if fp16 else torch.float32
        metadata = self._ckpt_metadata.with_input_dtype(torch_dtype).with_max_batch_size(max_batch_size)
        save_tensorrt_model(trt_model, metadata, save_to)


if __name__ == "__main__":
    fire.Fire(TrtConvert)

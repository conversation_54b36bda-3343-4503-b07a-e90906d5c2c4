import time
from typing import Any, Dict, Optional

import fire
import torch

from cv.furrows.furrows_python import generate_rho_theta
from deeplearning.furrows.model_output import FurrowsOutputFactory
from deeplearning.furrows.models.rgb import FurrowsRGB
from deeplearning.utils.model_benchmark import ModelBenchmark


class FurrowsBenchmark(ModelBenchmark):
    _TRT_MODEL_FILE = "furrows_resnet50.trt"

    def __init__(
        self,
        fp16: bool = False,
        int8: bool = False,
        batch_size: int = 1,
        height: int = 928,
        width: int = 1440,
        num_classes: int = 1,
        config_dict: Optional[Dict[str, Any]] = None,
    ) -> None:
        config_dict = config_dict if config_dict is not None else {}
        model: torch.nn.Module = FurrowsRGB(num_classes,)
        test_tensor = torch.rand(batch_size, 3, height, width).cuda()

        super(FurrowsBenchmark, self).__init__(
            fp16,
            int8,
            batch_size,
            [test_tensor],
            model,
            FurrowsBenchmark._TRT_MODEL_FILE,
            use_implicit_batch_dimension_trt=False,
        )

    def _run_once(self) -> float:
        with torch.no_grad(), torch.cuda.StreamContext(self._stream):
            start = time.perf_counter()
            output = FurrowsOutputFactory.unpack(self._model(*self._inputs))
            origin = (output.direction.shape[3] // 2, output.direction.shape[2] // 2)
            generate_rho_theta(
                output.direction[0][0], output.mask[0][0], origin=origin, threshold=0.5,
            )
            torch.cuda.synchronize()
        return (time.perf_counter() - start) * 1000.0


if __name__ == "__main__":
    fire.Fire(FurrowsBenchmark)

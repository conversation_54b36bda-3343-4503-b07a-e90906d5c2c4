import logging
import math
import os
import random
import socket
import time
import uuid
from typing import Any, Callable, Dict, List, Optional, Tuple, Union, cast

import cv2
import numpy as np
import numpy.typing as npt
import torch
import torch.nn.functional as F
import wandb
from sqlalchemy.orm import Session
from torch.utils.data import Data<PERSON><PERSON><PERSON>, Sam<PERSON>, get_worker_info

import deeplearning.server.trt_runtime as rt
from cv.furrows.furrows_python import compute_theta_tensor, generate_rho_theta
from deeplearning.dl_metrics.furrows_metrics import furrows_db
from deeplearning.furrows.config import FurrowsConfig
from deeplearning.furrows.dataset_types import Line
from deeplearning.furrows.datasets import FurrowsDatapoint, FurrowsDataset, FurrowsDatasets
from deeplearning.furrows.line_utils import render_hough_lines
from deeplearning.furrows.metadata import ImageMetadata
from deeplearning.furrows.metrics import AverageMetrics, Metrics
from deeplearning.furrows.model_output import FurrowsOutput, FurrowsOutputFactory
from deeplearning.furrows.trt_convert import TrtConvert
from deeplearning.furrows.version import get_version
from deeplearning.model_io import ModelMetadata, load_tensorrt_model
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS, denormalize_image
from deeplearning.utils.segm_utils import dice_loss
from deeplearning.utils.tensor import gather_objects
from deeplearning.utils.trainer import (
    S3_BUCKET,
    Environment,
    Trainer,
    TrainingModule,
    compute_md5sum,
    get_tensorrt_file_name,
)
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import get_wandb_metadata_json_str
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder, set_verbosity

LOG = logging.getLogger(__name__)
set_verbosity(False)


class FurrowsTrainingModule(TrainingModule):
    def __init__(self, datasets: FurrowsDatasets, config: FurrowsConfig, model: torch.nn.Module):
        super().__init__()

        self._datasets = datasets
        self._config = config
        self._model = model

        self._validation_outputs: List[Metrics] = []
        self._test_outputs: List[Metrics] = []
        self._training_outputs: List[Metrics] = []

        self._use_cases = [ModelUseCase.DRIVING]

        if torch.distributed.get_rank() == 0:
            LOG.info(
                "Datasets: train={train} examples, val={val} examples, test={test} examples".format(
                    train=len(self._datasets.get_training()),
                    val=len(self._datasets.get_validation()),
                    test=len(self._datasets.get_test()),
                )
            )

        self.line_class_weights = torch.tensor([1.0])
        self.segm_class_weights = torch.tensor([1.0])
        self.trt_model: Optional[torch.nn.Module] = None
        self.is_trt_fp16 = False
        self._db_engine: Optional[Session] = None

    @property
    def config(self) -> FurrowsConfig:
        return self._config

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        if self.trt_model is not None:
            if self._config.convert_fp16:
                # convert input to correct dtype
                x = x.half()
            y_hat = self.trt_model(x)
            # convert output to correct dtype
            y_hat_unpacked = FurrowsOutputFactory.unpack(y_hat).float()
            y_hat = y_hat_unpacked.pack()
        else:
            y_hat = self._model(x, gradient_checkpoint=True, preserve_zero_tensor_shape=True)
        return cast(Tuple[torch.Tensor, ...], y_hat)

    def line_loss(
        self, y: torch.Tensor, y_hat: torch.Tensor, y_mask: torch.Tensor, y_hat_mask: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        assert (
            len(y.shape) == 5 and len(y_hat.shape) == 5
        ), f"Input to line loss should be 5D: y {y.shape} and y_hat {y_hat.shape}"

        batch_size = y_mask.shape[0]

        reshaped_line_class_weights = (
            self.line_class_weights.repeat(batch_size).reshape(batch_size, -1, 1, 1, 1).to(y.device)
        )

        (mask_dice_loss, _) = dice_loss(y_hat_mask, y_mask, class_weights=self.line_class_weights,)

        segmentation_mask = y_mask.unsqueeze(4).expand(-1, -1, -1, -1, 2)
        masked_y = segmentation_mask * y * reshaped_line_class_weights
        masked_y_hat = segmentation_mask * y_hat * reshaped_line_class_weights
        direction_loss = torch.nn.functional.huber_loss(masked_y, masked_y_hat)

        return (mask_dice_loss), direction_loss

    def metrics_to_db(self, metrics: Metrics, metadata: List[ImageMetadata]) -> None:
        assert self._db_engine is not None
        db_session = furrows_db.get_session(self._db_engine)
        image_metrics = metrics.get_image_metrics()
        for n in range(len(image_metrics)):
            image_metadata = metadata[n]
            truncated_filepath = image_metadata.filepath
            image_db = furrows_db.get_image(db_session, truncated_filepath)
            if not image_db:
                image_db = furrows_db.Image(
                    id=str(uuid.uuid4()),
                    filepath=truncated_filepath,
                    date=image_metadata.captured_at.date(),
                    timestamp_ms=time.mktime(image_metadata.captured_at.timetuple()),
                    robot_id=image_metadata.robot_id,
                    cam_id=image_metadata.camera_id,
                    geohash=image_metadata.geohash,
                )
                db_session.add(image_db)
                db_session.flush()

            for metric, value in image_metrics[n]:
                if math.isnan(value):
                    continue
                image_metric = furrows_db.ImageMetric(
                    image_id=image_db.id, metric_name=metric, numerator=value, denominator=1
                )
                db_session.add(image_metric)

        db_session.commit()

    @property
    def output_size(self) -> Tuple[int, int]:
        return (
            self._config.train_height // self._model.downsample_rate,
            self._config.train_width // self._model.downsample_rate,
        )

    def compute_metrics(
        self,
        x: torch.Tensor,
        lines: List[List[Line]],
        batch_enabled_classes: torch.Tensor,
        metadata: List[ImageMetadata],
    ) -> Tuple[Metrics, FurrowsOutput, FurrowsOutput]:
        out_hat = FurrowsOutputFactory.unpack(self.forward(x))
        out = FurrowsOutputFactory.from_label(
            lines,
            enabled_line_classes=self._datasets.get_training().detection_classes,
            output_shape=self.output_size,
            downsample_rate=self._model.downsample_rate,
            line_thickness=self._config.line_thickness,
            dilation_iterations=self._config.dilate_mask,
        ).cuda()

        # fence label & predictions based on enabled classes for this batch
        with torch.no_grad():
            out = out.fence(batch_enabled_classes)
        out_hat = out_hat.fence(batch_enabled_classes)

        if self.line_class_weights.sum() > 1e-9:
            mask_loss, direction_loss = self.line_loss(out.direction, out_hat.direction, out.mask, out_hat.mask)
        else:
            mask_loss = torch.tensor(0.0, device=out.mask.device)
            direction_loss = torch.tensor(0.0, device=out.mask.device)

        metrics = Metrics(
            out,
            out_hat,
            mask_loss,
            direction_loss,
            self.line_class_weights > 0,
            self._config,
            learning_rate=self.optimizer.param_groups[0]["lr"] if hasattr(self, "optimizer") else 0,
        )
        if self._db_engine is not None:
            self.metrics_to_db(metrics, metadata)
        return metrics, out, out_hat

    COLLATED_DATASET_ELEMENT = Tuple[torch.Tensor, List[List[Line]], torch.Tensor, List[ImageMetadata]]

    @staticmethod
    def collate_fn(data: List[FurrowsDatapoint], skip: bool = True) -> Any:
        if skip:
            return data

        return (
            torch.stack([x.image for x in data]).cuda(),
            [x.lines for x in data],
            torch.stack([x.batch_enabled_classes for x in data]).cuda(),
            [x.metadata for x in data],
        )

    @torch.no_grad()
    def gpu_transforms(self, batch: List[FurrowsDatapoint], dataset_type: DatasetType) -> List[FurrowsDatapoint]:
        with duration_perf_recorder(f"{PerfCategory.TRAINING}", "gpu_transforms"):
            dataset: FurrowsDataset

            if dataset_type == DatasetType.TRAIN:
                dataset = self._datasets.get_training()
            elif dataset_type == DatasetType.VALIDATION:
                dataset = self._datasets.get_validation()
            elif dataset_type == DatasetType.TEST:
                dataset = self._datasets.get_test()

            for datapoint in batch:
                datapoint.image = datapoint.image.cuda()
                with torch.autocast(device_type="cuda", enabled=False):
                    dataset.preprocess_datapoint(datapoint)

            return batch

    def training_step(self, batch: List[FurrowsDatapoint], batch_nb: int) -> Dict[str, Any]:
        self.batch_idx = batch_nb

        preprocessed_batch = self.gpu_transforms(batch, dataset_type=DatasetType.TRAIN)
        x, y, batch_enabled_classes, metadata = self.collate_fn(preprocessed_batch, skip=False)

        metrics, out, out_hat = self.compute_metrics(x, y, batch_enabled_classes, metadata)

        if self.logger is not None:
            for n in range(x.shape[0]):
                if random.random() < self._config.train_log_image_p:
                    self.log_image(
                        x,
                        out,
                        out_hat,
                        batch_enabled_classes,
                        "train_",
                        n,
                        filename=os.path.basename(metadata[n].filepath),
                    )

        detached_metrics = metrics.detach().cpu()
        metrics_dict = detached_metrics.to_dict(lr=self.optimizer.param_groups[0]["lr"], epoch=self.current_epoch)
        # PyTorch-Lightning doesn't like "loss" in progress bar since it's already there
        metrics_dict_no_loss = metrics_dict.copy()

        self.log_dict(metrics_dict_no_loss, logger=False, prog_bar=True, on_epoch=False, on_step=True)

        self._training_outputs.append(detached_metrics)

        return {"loss": metrics.loss, "metrics": detached_metrics}

    def on_train_start(self) -> None:
        if self.logger is not None:
            # Log model graph
            self.logger.watch(self._model, log=None)

            # Log config values
            self.logger.experiment.config.update(
                {
                    "image_file_counts": {
                        "train": self._datasets.get_training().distinct_examples,
                        "validation": self._datasets.get_validation().distinct_examples,
                        "test": self._datasets.get_test().distinct_examples,
                    },
                    "hostname": os.getenv("NODE_NAME", socket.gethostname()),
                    "initial_lr": self._config.lr,
                },
                allow_val_change=True,
            )

            self.logger.experiment.config.update(self._config.to_dict(), allow_val_change=True)

    def on_train_epoch_end(self) -> None:
        avg_metrics = AverageMetrics(
            gather_objects(self._training_outputs),
            segm_enabled_classes=self.segm_class_weights > 0,
            line_enabled_classes=self.line_class_weights > 0,
        )
        self._training_outputs.clear()
        if torch.distributed.get_rank() != 0:
            return

        metrics_dict = {k: v.item() for k, v in avg_metrics.to_dict(prefix="train_").items()}
        logging.info(f"\n\nTRAINING RESULTS: {metrics_dict}\n")

        self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

    def evaluation(self, batch: List[FurrowsDatapoint], prefix: str, log_p: float = 1.0) -> Metrics:
        preprocessed_batch = self.gpu_transforms(batch, dataset_type=DatasetType.VALIDATION)
        x, y, batch_enabled_classes, metadata = self.collate_fn(preprocessed_batch, skip=False)
        metrics, out, out_hat = self.compute_metrics(x, y, batch_enabled_classes, metadata)

        if self.logger is not None:
            for n in range(x.shape[0]):
                if random.random() < log_p:
                    self.log_image(
                        x,
                        out,
                        out_hat,
                        batch_enabled_classes,
                        prefix,
                        n,
                        filename=os.path.basename(metadata[n].filepath),
                    )
        return metrics.detach().cpu()

    def validation_step(self, batch: List[FurrowsDatapoint], batch_nb: int) -> None:
        self._validation_outputs.append(self.evaluation(batch, "val_", self._config.val_log_image_p))

    def on_validation_epoch_end(self) -> None:
        avg_metrics = AverageMetrics(
            gather_objects(self._validation_outputs),
            segm_enabled_classes=self.segm_class_weights > 0,
            line_enabled_classes=self.line_class_weights > 0,
        )
        self._validation_outputs.clear()

        if torch.distributed.get_rank() == 0:
            metrics_dict = {k: v.item() for k, v in avg_metrics.to_dict(prefix="val_").items()}
            logging.info(f"\n\nVALIDATION RESULTS: {metrics_dict}\n")
            self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

            val_oec_list = [metrics_dict["val_oec"]]
            torch.distributed.broadcast_object_list(val_oec_list)
        else:
            val_oec_list = [0.0]
            torch.distributed.broadcast_object_list(val_oec_list)
            self.log("val_oec", val_oec_list[0])

    def test_step(self, batch: List[FurrowsDatapoint], batch_nb: int) -> None:
        self._test_outputs.append(self.evaluation(batch, self.get_test_prefix(), self._config.test_log_image_p))

    def on_test_start(self) -> None:
        assert self._exp_dir is not None
        db_path = os.path.join(self._exp_dir, f"{self.get_test_prefix()}db/furrows_{furrows_db.__version__}.db")
        if torch.distributed.get_rank() == 0:
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            self._db_engine = furrows_db.get_db(db_path)
        torch.distributed.barrier()
        if torch.distributed.get_rank() != 0:
            self._db_engine = furrows_db.get_db(db_path)

    def on_test_epoch_end(self) -> None:
        avg_metrics = AverageMetrics(
            gather_objects(self._test_outputs),
            segm_enabled_classes=self.segm_class_weights > 0,
            line_enabled_classes=self.line_class_weights > 0,
        )
        self._test_outputs.clear()

        if torch.distributed.get_rank() == 0:
            metrics_dict = {k: v.item() for k, v in avg_metrics.to_dict(prefix=self.get_test_prefix()).items()}
            logging.info(f"\n\nTEST RESULTS: {metrics_dict}\n")
            self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

            test_oec_list = [metrics_dict[f"{self.get_test_prefix()}oec"]]
            torch.distributed.broadcast_object_list(test_oec_list)
        else:
            test_oec_list = [0.0]
            torch.distributed.broadcast_object_list(test_oec_list)
            self.log(f"{self.get_test_prefix()}oec", test_oec_list[0])
        self._db_engine = None

    def set_exp_dir(self, exp_dir: str) -> None:
        self._exp_dir = exp_dir

    def set_experiment_url(self, experiment_url: str) -> None:
        self._experiment_url = experiment_url

    def export_metadata(self) -> ModelMetadata:
        number_of_input_layers = 4 if self._config.supports_depth else 3
        return ModelMetadata(
            input_dtype=torch.float32,
            input_size=(self._config.train_height, self._config.train_width),
            means=IMAGENET_MEANS[:number_of_input_layers],
            stds=IMAGENET_STDS[:number_of_input_layers],
            experiment_url=self._experiment_url,
            segm_classes=self._datasets.train_dataset.detection_classes,
            use_cases=self.use_cases,
            supports_depth=self._config.supports_depth,
            supports_half=True,
            not_interleaved=True,
        )

    def export_model(self) -> torch.nn.Module:
        return self._model

    def export_torch_script(self) -> torch.jit.ScriptModule:
        model_bn = self.export_model()

        return cast(torch.jit.ScriptModule, torch.jit.script(model_bn))

    @property
    def use_cases(self) -> List[ModelUseCase]:
        return self._use_cases

    def configure_optimizers(
        self,
    ) -> Union[
        torch.optim.Optimizer, Tuple[List[torch.optim.Optimizer], List[torch.optim.lr_scheduler._LRScheduler]],
    ]:
        self.optimizer = torch.optim.SGD(
            self.parameters(),
            lr=self._config.lr * torch.distributed.get_world_size(),
            momentum=self._config.momentum,
            weight_decay=self._config.weight_decay,
        )

        if len(self._config.lr_milestones) > 0:
            scheduler = torch.optim.lr_scheduler.MultiStepLR(
                self.optimizer, milestones=self._config.lr_milestones, gamma=self._config.lr_gamma
            )
            return [self.optimizer], [cast(torch.optim.lr_scheduler._LRScheduler, scheduler)]

        return self.optimizer

    def get_test_prefix(self) -> str:
        prefix = "test_"
        if self.trt_model is None:
            prefix = prefix + "unoptimized_"

        return prefix

    @property
    def trt_file_name(self) -> str:
        return get_tensorrt_file_name(self._config.convert_int8)

    @property
    def trt_file_path(self) -> str:
        assert self._exp_dir is not None
        return os.path.join(self._exp_dir, self.trt_file_name)

    def get_worker_init_fn(self) -> Callable[[int], None]:
        num_workers = self._config.num_workers

        def worker_init_fn(worker_id: int) -> None:
            worker_info = get_worker_info()

            if worker_info is not None and hasattr(worker_info.dataset, "set_seed"):
                seed = torch.distributed.get_rank() * num_workers + worker_id
                worker_info.dataset.set_seed(seed)

        return worker_init_fn

    def train_dataloader(self) -> "DataLoader[FurrowsDatapoint]":
        dataset = self._datasets.get_training()

        sampler: Sampler[FurrowsDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank(), shuffle=True
        )

        return DataLoader(
            dataset,
            sampler=sampler,
            batch_size=self._config.train_batch_size,
            collate_fn=self.collate_fn,
            pin_memory=False,
            drop_last=True,
            num_workers=self._config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
        )

    def val_dataloader(self) -> "DataLoader[FurrowsDatapoint]":
        # OPTIONAL
        # can also return a list of val dataloaders
        dataset = self._datasets.get_validation()
        sampler: Sampler[FurrowsDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank()
        )
        return DataLoader(
            dataset,
            sampler=sampler,
            batch_size=self._config.evaluation_batch_size,
            collate_fn=self.collate_fn,
            pin_memory=False,
            num_workers=self._config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
        )

    def test_dataloader(self) -> "DataLoader[FurrowsDatapoint]":
        # OPTIONAL
        # can also return a list of test dataloaders
        dataset = self._datasets.get_test()
        sampler: Sampler[FurrowsDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank(), drop_last=True
        )
        return DataLoader(
            dataset,
            sampler=sampler,
            batch_size=self._config.evaluation_batch_size,
            collate_fn=self.collate_fn,
            pin_memory=False,
            num_workers=self._config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
        )

    def load_trt(self, trt_file_path: str) -> None:
        self.trt_model, metadata = load_tensorrt_model(trt_file_path)
        self.is_trt_fp16 = metadata.input_dtype == torch.float16
        cast(rt.TRTModule, self.trt_model).set_cache_context(True)

        LOG.info(f"Rank {torch.distributed.get_rank()} loaded TensorRT model.")

    @torch.no_grad()
    def optimize_for_testing(self, best_checkpoint: Optional[str] = None) -> bool:
        if self._config.make_trt_model and self.trt_model is None:
            torch.cuda.empty_cache()
            if torch.distributed.get_rank() == 0:
                assert self._exp_dir is not None
                trt_converter = TrtConvert(
                    self._datasets.get_calibration(),
                    self._datasets.get_validation(),
                    self.export_metadata(),
                    self.export_model(),
                )
                trt_converter.convert(
                    max_batch_size=self._config.evaluation_batch_size,
                    save_to=self.trt_file_path,
                    int8=self._config.convert_int8,
                    fp16=self._config.convert_fp16,
                    calibration_batch_size=16,
                    error_metrics=True,
                )

            # wait for model to be created
            torch.distributed.barrier()

            # replace model with optimized model
            self.load_trt(self.trt_file_path)
            # wait for model to be loaded
            torch.distributed.barrier()
            return True
        return False

    @torch.no_grad()
    def log_image(
        self,
        x: torch.Tensor,
        out: FurrowsOutput,
        out_hat: FurrowsOutput,
        batch_enabled_classes: torch.Tensor,
        prefix: str,
        n: int,
        filename: str,
        segm_threshold: float = 0.5,
    ) -> None:
        assert self.logger is not None

        img = x[n]
        if img.shape[0] == 4:
            # RGBD to RGB
            img = img[:3, :, :]
        img = denormalize_image(img)

        for class_id, category in enumerate(self._datasets.get_training().detection_classes):
            if not batch_enabled_classes[n, class_id].item():
                # category not labeled for a given image
                continue

            mask_red = None
            mask_green = None
            if self.line_class_weights[class_id] > 0:
                mask_red = self.render_line_mask(out.mask[n, class_id], out.direction[n, class_id], segm_threshold)
                mask_green = self.render_line_mask(
                    out_hat.mask[n, class_id], out_hat.direction[n, class_id], segm_threshold
                )
                self.log_hough_images(
                    img,
                    out.mask[n, class_id],
                    out.direction[n, class_id],
                    out_hat.mask[n, class_id],
                    out_hat.direction[n, class_id],
                    prefix,
                    category,
                )
                self.log_mask_images(
                    img, out.mask[n, class_id], out_hat.mask[n, class_id], prefix, category, segm_threshold
                )
            else:
                continue

            if (mask_red is None or not mask_red.sum() > 0) and (mask_green is None or not mask_green.sum() > 0):
                # not interesting image
                continue

            masked_img = img.clone()
            if mask_red is not None:
                mask_red = (
                    F.interpolate(mask_red.unsqueeze(0).unsqueeze(0), (img.shape[-2], img.shape[-1]))
                    .squeeze(0)
                    .squeeze(0)
                )
                assert mask_red is not None
                masked_img[0, :, :] = torch.max(masked_img[0, :, :], mask_red.float())
            if mask_green is not None:
                mask_green = (
                    F.interpolate(mask_green.unsqueeze(0).unsqueeze(0), (img.shape[-2], img.shape[-1]))
                    .squeeze(0)
                    .squeeze(0)
                )
                assert mask_green is not None
                masked_img[1, :, :] = torch.max(masked_img[1, :, :], mask_green.float())

            filename_overlay = np.zeros((masked_img.shape[1:3]), dtype=np.uint8)
            cv2.putText(filename_overlay, filename, (10, 30), cv2.FONT_HERSHEY_COMPLEX_SMALL, 1, (1,), 2)
            masked_img[1, :, :] = torch.max(masked_img[1, :, :], torch.tensor(filename_overlay).cuda().float())

            self.logger.experiment.log(
                {f"{prefix}lines_{category}": [wandb.Image(masked_img)]},
                step=self.trainer.global_step if "train" in prefix else None,
            )

    def render_line_mask(self, mask: torch.Tensor, direction: torch.Tensor, segm_threshold: float) -> torch.Tensor:
        mask = mask > segm_threshold  # Threshold mask using segm_thesh
        angles_theta = compute_theta_tensor(direction.float(), self._config.flip_threshold).cpu()

        vecs: npt.NDArray[Any] = np.zeros(mask.shape)  # Vecs the size of image, which we want for overlaying
        for idx, (row, col) in enumerate(mask.nonzero().cpu()):
            if idx % 10 != 0:
                continue

            cos_theta, sin_theta = angles_theta[row, col]
            vy, vx = -cos_theta, sin_theta

            start = ((col - int(30 * vx)).item(), (row - int(30 * vy)).item())
            end = ((col + int(30 * vx)).item(), (row + int(30 * vy)).item())
            vecs = cv2.line(vecs, start, end, (1,), 2,)
        return torch.tensor(vecs).to(mask.device)

    def log_hough_images(
        self,
        img: torch.Tensor,
        mask: torch.Tensor,
        direction: torch.Tensor,
        mask_hat: torch.Tensor,
        direction_hat: torch.Tensor,
        prefix: str,
        category: str,
    ) -> None:
        origin = (mask.shape[1] // 2, mask.shape[0] // 2)
        central_line = generate_rho_theta(direction, mask, origin=origin, threshold=self.config.hough_threshold,)
        central_line_hat = generate_rho_theta(
            direction_hat, mask_hat, origin=origin, threshold=self.config.hough_threshold,
        )

        line_mask = (
            torch.tensor(render_hough_lines(central_line, (mask.shape[0], mask.shape[1]), thickness=2, origin=origin))
            .cuda()
            .float()
        )
        line_mask_hat = (
            torch.tensor(
                render_hough_lines(central_line_hat, (mask.shape[0], mask.shape[1]), thickness=2, origin=origin)
            )
            .cuda()
            .float()
        )
        line_mask = (
            F.interpolate(line_mask.unsqueeze(0).unsqueeze(0), (img.shape[-2], img.shape[-1])).squeeze(0).squeeze(0)
        )
        line_mask_hat = (
            F.interpolate(line_mask_hat.unsqueeze(0).unsqueeze(0), (img.shape[-2], img.shape[-1])).squeeze(0).squeeze(0)
        )
        masked_img = img.clone()
        masked_img[0, :, :] = torch.max(masked_img[0, :, :], line_mask)
        masked_img[2, :, :] = torch.max(masked_img[2, :, :], line_mask_hat)

        self.logger.experiment.log(
            {f"{prefix}hough_transform_{category}": [wandb.Image(masked_img)]},
            step=self.trainer.global_step if "train" in prefix else None,
        )

    def log_mask_images(
        self,
        img: torch.Tensor,
        mask: torch.Tensor,
        mask_hat: torch.Tensor,
        prefix: str,
        category: str,
        segm_threshold: float,
    ) -> None:
        line_mask = F.interpolate(mask.unsqueeze(0).unsqueeze(0), (img.shape[-2], img.shape[-1])).squeeze(0).squeeze(0)
        line_mask_hat = (
            F.interpolate(mask_hat.unsqueeze(0).unsqueeze(0), (img.shape[-2], img.shape[-1])).squeeze(0).squeeze(0)
        )
        masked_img = img.clone()
        masked_img[0, :, :] = torch.max(masked_img[0, :, :], line_mask)
        masked_img[1, :, :] = torch.max(masked_img[1, :, :], ((line_mask > 0.01) & (line_mask < 0.99)).float())
        masked_img[2, :, :] = torch.max(masked_img[2, :, :], (line_mask_hat > segm_threshold))

        self.logger.experiment.log(
            {f"{prefix}mask_{category}": [wandb.Image(masked_img)]},
            step=self.trainer.global_step if "train" in prefix else None,
        )


class FurrowsTrainer(Trainer):
    def __init__(self) -> None:
        super().__init__()

    def train(
        self,
        config: FurrowsConfig,
        datasets: FurrowsDatasets,
        model: torch.nn.Module,
        pipeline_id: str,
        dataset_id: str,
        resume_from: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        log_experiment: bool = True,
        checkpoint_dir: Optional[str] = None,
        environment: Environment = Environment.DEVELOPMENT,
        deploy: bool = False,
    ) -> None:
        module = FurrowsTrainingModule(datasets, config, model)

        exp_dir = super()._train(
            module,
            get_version(),
            fast_run=config.fast_run,
            epochs=config.num_epochs,
            resume_from=resume_from,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            ci_run=config.ci_run,
            checkpoint_dir=checkpoint_dir,
            environment=environment,
            deploy=deploy,
            dataset_id=dataset_id,
        )

        if not config.ci_run and log_experiment and exp_dir is not None:
            version = exp_dir.rsplit("/", maxsplit=1)[-1]
            self._upload_s3(exp_dir)
            wandb_json = get_wandb_metadata_json_str(exp_dir)
            self._veselka_post_model(
                model_id=version,
                url=f"s3://{S3_BUCKET}/models/{version}/{module.trt_file_name}",
                checksum=compute_md5sum(module.trt_file_path),
                trained_at=int(time.time()),
                metadata_json=module.export_metadata().dump(),
                wandb_json=wandb_json,
                is_good_to_deploy=True,
                dl_config=config.to_dict(),
                exp_dir=exp_dir,
                pipeline_id=pipeline_id,
                dataset_id=dataset_id,
            )

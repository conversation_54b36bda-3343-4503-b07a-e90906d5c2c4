import copy
import datetime
import io
import logging
from typing import Any, List, Optional, <PERSON><PERSON>, Tu<PERSON>, cast

import numpy as np
import pandas
import requests
import torch
import torchvision.transforms.functional as TF
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential
from torch.utils.data import Dataset
from urllib.parse import urlparse

import deeplearning.furrows.transforms as T
from deeplearning.furrows.config import FurrowsConfig
from deeplearning.furrows.dataset_types import FurrowsDatapoint, Line
from deeplearning.furrows.metadata import ImageMetadata
from deeplearning.utils.dataset import get_carbon_cache_host
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS

DATASET_ELEMENT = Tuple[torch.Tensor, torch.Tensor, torch.Tensor, ImageMetadata]

LOG = logging.getLogger(__name__)


def dataset_transforms(
    train_height: int, train_width: int, training: bool = False, calibration: bool = False,
) -> T.Compose:
    tfms: List[Any] = []
    random_translation = (0, 0)
    if training:
        random_translation = (train_height // 8, train_width // 8)
    tfms += [T.CenterCrop((train_height, train_width), random_translation)]
    if training:
        tfms += [
            T.RandomChoice(
                [
                    T.RandomRotation(1.0, 1.0, -5, 5),
                    T.ColorJitter(
                        brightness=(0.75, 1.33),
                        saturation=(0.5, 2.0),
                        hue=(0, 0),
                        gamma=(0.75, 1.33),
                        contrast=(0.5, 1.33),
                    ),
                    T.GaussianNoise(stddev=0.03),
                    T.RandomVerticalFlip(p=0.5),
                    T.RandomHorizontalFlip(p=0.5),
                ]
            )
        ]
    tfms += [
        T.Normalize(mean=IMAGENET_MEANS, std=IMAGENET_STDS),
    ]
    return T.Compose(tfms)


class FurrowsDataset(Dataset[FurrowsDatapoint], Sized):
    def __init__(
        self,
        path: str,
        config: FurrowsConfig,
        detection_classes: List[str],
        transforms: Optional[T.Compose] = None,
        num_samples: Optional[int] = None,
        seed: int = 1,
        include_depth: bool = False,
    ) -> None:
        super().__init__()
        self._transforms = transforms
        self._num_samples = num_samples
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)
        self._carbon_cache_host = get_carbon_cache_host()
        self._detection_classes = detection_classes
        self._include_depth = include_depth
        self._config = config

        self._df = pandas.read_json(path_or_buf=path, lines=True)
        self._df["date"] = self._df["img_timestamp_ms"].apply(
            lambda x: datetime.datetime.fromtimestamp(x // 1000).date().isoformat()
        )
        self._df["geohash_bucket"] = self._df["geohash"].apply(lambda x: x[: self._config.sampling_geohash_precision])
        self._df["index"] = list(range(len(self._df)))

    @property
    def detection_classes(self) -> List[str]:
        return self._detection_classes

    @property
    def distinct_examples(self) -> int:
        return len(self._df)

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def _get_from_cache(self, s3_url: str) -> requests.models.Response:
        parsed_uri = urlparse(s3_url)
        s3_path = parsed_uri.path.lstrip("/")
        s3_bucket = parsed_uri.netloc
        response = requests.get(f"http://{self._carbon_cache_host}/{s3_bucket}/{s3_path}", timeout=30)
        assert response.ok, f"Failed to retrieve item ({s3_url}) from cache: {response.text}"
        return response

    def _load_image(self, image_meta: ImageMetadata) -> torch.Tensor:
        image_path = image_meta.filepath
        image_data = io.BytesIO(self._get_from_cache(image_path).content)
        if image_meta.filepath.endswith(".npz"):
            data = np.load(image_data)
            image = TF.to_tensor(data[image_meta.npz_rgb_key])
            if self._include_depth:
                depth = TF.to_tensor(data[image_meta.npz_depth_key])
                image = torch.cat((image, depth), dim=0)
        else:
            image = TF.to_tensor(Image.open(image_data, formats=["png"]).convert("RGB"))

        return cast(torch.Tensor, image)

    def set_seed(self, seed: int) -> None:
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)

    def _sample_index(self) -> int:
        geohash_bucket = self._rng.choice(list(self._df["geohash_bucket"].unique()))
        df = self._df[self._df["geohash_bucket"] == geohash_bucket]

        date = self._rng.choice(list(self._df["date"].unique()))
        df = self._df[self._df["date"] == date]
        index: int = self._rng.choice(df["index"])

        return index

    def __getitem__(self, index: int) -> FurrowsDatapoint:
        if self._num_samples:
            index = self._sample_index()

        metadata = self._df.iloc[index]
        image_meta = ImageMetadata(
            filepath=metadata["img_url"],
            captured_at=datetime.datetime.fromtimestamp(metadata["img_timestamp_ms"] // 1000),
            robot_id=metadata["robot_id"],
            camera_id=metadata["cam_id"],
            geohash=metadata["geohash"],
        )
        lines = [
            Line(x["start"]["x"], x["start"]["y"], x["end"]["x"], x["end"]["y"], x["category_id"])
            for x in metadata["label_data"]["line_labels"]
        ]

        # TODO(evanbro): Add certified classes once its stored in the dataset
        enabled_classes = torch.tensor([True for c in self._detection_classes])

        image = self._load_image(image_meta)

        return FurrowsDatapoint(image, lines, enabled_classes, image_meta)

    def __len__(self) -> int:
        if self._num_samples is None:
            return len(self._df)
        else:
            return self._num_samples

    def preprocess_datapoint(self, datapoint: FurrowsDatapoint) -> FurrowsDatapoint:
        if self._transforms is not None:
            image, lines = self._transforms(datapoint.image, copy.deepcopy(datapoint.lines), datapoint.metadata,)

            datapoint.image = image
            datapoint.lines = lines

        return datapoint


class FurrowsDatasets:
    def __init__(
        self,
        train_dataset: FurrowsDataset,
        validation_dataset: FurrowsDataset,
        test_dataset: FurrowsDataset,
        calibration_dataset: FurrowsDataset,
    ) -> None:
        self.train_dataset = train_dataset
        self.validation_dataset = validation_dataset
        self.test_dataset = test_dataset
        self.calibration_dataset = calibration_dataset

    def get_training(self) -> FurrowsDataset:
        return self.train_dataset

    def get_validation(self) -> FurrowsDataset:
        return self.validation_dataset

    def get_test(self) -> FurrowsDataset:
        return self.test_dataset

    def get_calibration(self) -> FurrowsDataset:
        return self.calibration_dataset


def get_furrows_datasets(
    train: str, validation: str, test: str, config: FurrowsConfig, detection_classes: List[str],
) -> FurrowsDatasets:
    training_dataset = FurrowsDataset(
        train,
        config,
        detection_classes,
        dataset_transforms(config.train_height, config.train_width, training=True),
        num_samples=config.num_samples,
        include_depth=config.supports_depth,
    )
    validation_dataset = FurrowsDataset(
        validation,
        config,
        detection_classes,
        dataset_transforms(config.train_height, config.train_width),
        include_depth=config.supports_depth,
    )
    test_dataset = FurrowsDataset(
        test,
        config,
        detection_classes,
        dataset_transforms(config.train_height, config.train_width),
        include_depth=config.supports_depth,
    )
    calibration_dataset = FurrowsDataset(
        train,
        config,
        detection_classes,
        dataset_transforms(config.train_height, config.train_width, calibration=True),
        num_samples=1024,
        include_depth=config.supports_depth,
    )
    datasets = FurrowsDatasets(
        train_dataset=training_dataset,
        validation_dataset=validation_dataset,
        test_dataset=test_dataset,
        calibration_dataset=calibration_dataset,
    )
    return datasets

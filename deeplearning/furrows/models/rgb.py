from typing import TYPE_CHECKING, Type

import torch
import torch.nn.functional as F
from torch.utils import checkpoint
from torchvision.models._utils import IntermediateLayerGetter
from torchvision.models.resnet import ResNet18_Weights, resnet18

from deeplearning.furrows.model_output import FurrowsOutput
from deeplearning.furrows.version import get_version
from deeplearning.utils.aspp_layers import ASPP

if TYPE_CHECKING:
    torch_jit_unused = lambda x: x
else:
    torch_jit_unused = torch.jit.unused


class FurrowsRGB(torch.nn.Module):
    def __init__(
        self, num_classes: int, batch_norm: Type[torch.nn.BatchNorm2d] = torch.nn.BatchNorm2d,
    ):
        super().__init__()
        self.downsample_rate = 4
        self._num_classes = num_classes
        self._version = get_version()

        self.backbone_segm = IntermediateLayerGetter(
            resnet18(
                weights=ResNet18_Weights.IMAGENET1K_V1,
                # Momentum adjustment because batch norms will be forwarded twice
                # https://github.com/prigoyal/pytorch_memonger/blob/master/tutorial/Checkpointing_for_PyTorch_models.ipynb
                norm_layer=lambda planes: batch_norm(planes, momentum=(1 - (1 - 0.1) ** 0.5)),
            ),
            return_layers={"layer1": "interim", "layer3": "out"},
        )
        self.aspp = ASPP(256, [12, 24, 36], batch_norm)
        self.interim_conv = torch.nn.Sequential(
            torch.nn.Conv2d(64, 64, 1, bias=False), batch_norm(64), torch.nn.ReLU(inplace=True),
        )
        n = 320
        self.out_conv = torch.nn.Sequential(
            torch.nn.Conv2d(n, 256, 3, padding=1, bias=False),
            batch_norm(256),
            torch.nn.ReLU(inplace=True),
            torch.nn.Conv2d(256, self._num_classes, 1),
        )
        self.direction_conv = torch.nn.Sequential(
            torch.nn.Conv2d(n, 32, 3, padding=1, bias=False),
            batch_norm(32),
            torch.nn.ReLU(inplace=True),
            torch.nn.Conv2d(32, 2 * self._num_classes, 1),
        )

    def split_channel(self, x: torch.Tensor, factor: int) -> torch.Tensor:
        """Splits channel dimension and makes factor last dimension."""
        assert x.shape[1] % factor == 0, f"Input is not splittable by {factor}: {x.shape}"
        return x.reshape(x.shape[0], int(x.shape[1] / factor), factor, x.shape[2], x.shape[3]).permute(0, 1, 3, 4, 2)

    @torch_jit_unused
    def _run_backbone_segm_checkpointed(self, input):
        def run_backbone_segm(input, dummy_reqs_grad):
            features = self.backbone_segm(input)
            return features["interim"], features["out"]

        # Dummy requiring gradient is necessary for backbone gradients to be computed
        dummy_reqs_grad = torch.tensor(1.0, requires_grad=True)
        return checkpoint.checkpoint(run_backbone_segm, input, dummy_reqs_grad, use_reentrant=False)

    def _run_backbone_segm(self, input):
        features = self.backbone_segm(input)
        return features["interim"], features["out"]

    def forward(self, input: torch.Tensor, gradient_checkpoint: bool = False, preserve_zero_tensor_shape: bool = False):
        assert input.shape[1] in [3, 4], f"Expecting input to be NCHW/N(C+D)HW: {input.shape}"
        # Interim is at H/4, W/4 resolution
        # Out is at H/16, W/16 resolution

        input = input[:, :3, :, :]

        if gradient_checkpoint:
            interim, out = self._run_backbone_segm_checkpointed(input)
        else:
            interim, out = self._run_backbone_segm(input)
        out_aspp = self.aspp(out)

        # See https://arxiv.org/pdf/1802.02611.pdf for details
        interim = self.interim_conv(interim)
        out_interim = F.interpolate(out_aspp, interim.shape[-2:])

        cat_out_interim = torch.cat([out_interim, interim], dim=1)

        mask = self.out_conv(cat_out_interim)
        mask = torch.sigmoid(mask)

        direction = self.direction_conv(cat_out_interim)
        direction = torch.tanh(self.split_channel(direction, factor=2))
        direction = torch.nn.functional.normalize(direction, dim=4)

        result = FurrowsOutput(self._version, mask, direction)
        return result.pack()

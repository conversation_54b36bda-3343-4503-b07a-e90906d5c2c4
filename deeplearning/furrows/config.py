import functools
import math
from dataclasses import dataclass, field
from typing import List

from dataclass_wizard import JSONWizard

from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import WANDB_PROJECT_MAP


@dataclass(eq=True, frozen=True)
class FurrowsConfig(JSONWizard):
    train_height: int = 928
    train_width: int = 720
    train_batch_size: int = 6
    evaluation_batch_size: int = 3
    supports_depth: bool = False

    # Optimizer hyperparameters
    lr: float = 0.00875
    lr_gamma: float = 0.5
    lr_milestones: List[int] = field(default_factory=functools.partial(list, [10, 20, 30, 40, 50, 60]))
    momentum: float = 0.9
    weight_decay: float = 0

    # Other configuration settings
    wandb_project: str = WANDB_PROJECT_MAP[ModelUseCase.DRIVING]

    gradient_checkpoint: bool = True

    precision: str = "32-true"
    num_samples: int = 8000
    num_epochs: int = 30
    fast_run: bool = False
    ci_run: bool = False
    make_trt_model: bool = True
    convert_int8: bool = False
    convert_fp16: bool = False

    train_log_image_p: float = 0.05
    val_log_image_p: float = 0.5
    test_log_image_p: float = 0.5

    dilate_mask: int = 0
    line_thickness: int = 3
    rho_step: float = 1
    theta_step: float = math.pi / 360
    hough_threshold: float = 0.5
    blur_kernel_size: int = 3
    flip_threshold: float = 1e-3

    mask_loss_multiplier: float = 0.5
    direction_loss_multiplier: float = 3333

    num_workers: int = 2

    sampling_geohash_precision: int = 4

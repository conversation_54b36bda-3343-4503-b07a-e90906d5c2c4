from typing import List, <PERSON><PERSON>, Union

import cv2
import numpy as np
import torch

from deeplearning.furrows.dataset_types import Line
from deeplearning.furrows.transforms import DilateMask


def make_2theta_tensor(
    lines: List[List[Line]],
    output_shape: Tuple[int, int],
    enabled_classes: List[str],
    downsample_rate: float,
    line_thickness: int,
    dilation_iterations: int,
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    For each example in input, for each class, get 2theta tensors
    Output: NxClassxHxWxCoordinates
    """

    dilate_mask = DilateMask(dilation_iterations)

    direction = torch.zeros(
        (len(lines), len(enabled_classes), output_shape[0], output_shape[1], 2), dtype=torch.float32
    )
    mask = np.zeros((len(lines), len(enabled_classes), output_shape[0], output_shape[1]), dtype=np.float32)

    for example_id in range(len(lines)):
        for line_index, line in enumerate(lines[example_id]):
            if line.category not in enabled_classes:
                continue
            val = line_index + 1
            class_id = enabled_classes.index(line.category)

            cv2.line(
                mask[example_id, class_id],
                (int(line.x1 / downsample_rate), int(line.y1 / downsample_rate)),
                (int(line.x2 / downsample_rate), int(line.y2 / downsample_rate)),
                (val,),
                line_thickness,
            )

            # The line is drawn using line index value then we grab the indices of that value to decide where to store direction
            if dilation_iterations > 0:
                indices = np.nonzero(
                    dilate_mask(torch.from_numpy(mask[example_id, class_id] == val).unsqueeze(0).unsqueeze(0))
                    .squeeze(0)
                    .squeeze(0)
                    .numpy()
                )
            else:
                indices = np.nonzero(mask[example_id, class_id] == val)

            p1 = np.array([line.x1, line.y1])
            p2 = np.array([line.x2, line.y2])
            if p1[1] > p2[1]:
                p1, p2 = p2, p1

            v = p2 - p1
            v_norm = np.linalg.norm(v)
            if v_norm == 0:
                continue
            v = v / v_norm

            sin_theta = v[0]
            cos_theta = -v[1]
            # since we don't care about line direction, we will map 0..pi to 0..2pi
            # and store sin/cos of that
            sin_2theta = torch.tensor(2 * sin_theta * cos_theta, dtype=torch.float32)
            cos_2theta = torch.tensor(cos_theta ** 2 - sin_theta ** 2, dtype=torch.float32)
            direction[example_id, class_id, indices[0], indices[1], 0] = cos_2theta
            direction[example_id, class_id, indices[0], indices[1], 1] = sin_2theta

    mask_torch = torch.from_numpy(mask)
    mask_torch[mask_torch > 0] = 1

    if dilation_iterations > 0:
        mask_torch = dilate_mask(mask_torch)

    return direction, mask_torch


class FurrowsOutput:
    def __init__(
        self, version: int, mask: torch.Tensor, direction: torch.Tensor,
    ):
        self.version = version
        self.mask = mask
        self.direction = direction

    def fence(self, batch_enabled_classes: torch.Tensor) -> "FurrowsOutput":
        bec_4d = batch_enabled_classes.unsqueeze(-1).unsqueeze(-1)
        bec_5d = bec_4d.unsqueeze(-1)
        becd_5d = bec_5d.expand(-1, -1, -1, -1, 2)
        return FurrowsOutput(version=self.version, mask=self.mask * bec_4d, direction=self.direction * becd_5d,)

    def cpu(self) -> "FurrowsOutput":
        return FurrowsOutput(version=self.version, mask=self.mask.cpu(), direction=self.direction.cpu(),)

    def cuda(self) -> "FurrowsOutput":
        return FurrowsOutput(version=self.version, mask=self.mask.cuda(), direction=self.direction.cuda(),)

    def detach(self) -> "FurrowsOutput":
        return FurrowsOutput(version=self.version, mask=self.mask.detach(), direction=self.direction.detach(),)

    def float(self) -> "FurrowsOutput":
        return FurrowsOutput(version=self.version, mask=self.mask.float(), direction=self.direction.float(),)

    @property
    def device(self) -> torch.device:
        return self.mask.device

    @property
    def batch_size(self) -> int:
        # We ensure that dummy mask has correct batch size
        return self.mask.shape[0]

    def pack(self) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return (
            torch.tensor([self.version], dtype=torch.int32, device=self.mask.device),
            self.mask,
            self.direction,
        )


class FurrowsOutputFactory:
    """This is separated from FurrowsOutput to make the latter TorchScript compatible."""

    @staticmethod
    def _unpack_tuple(input: Tuple[torch.Tensor, ...]) -> FurrowsOutput:
        assert len(input) >= 1, f"Invalid input size: {len(input)}"

        ptr = 0
        version = int(input[ptr][0])
        ptr += 1

        mask = input[ptr]
        ptr += 1

        if len(input) == ptr:
            direction = torch.zeros(mask.unsqueeze(-1).expand(-1, -1, -1, -1, 2).shape, device=mask.device)
        else:
            direction = input[ptr]
            ptr += 1

        # We ignore additional inputs for forward-compatibility
        return FurrowsOutput(version, mask, direction)

    @staticmethod
    def unpack(input: Union[torch.Tensor, Tuple[torch.Tensor, ...]]) -> FurrowsOutput:
        if isinstance(input, torch.Tensor):
            return FurrowsOutputFactory._unpack_tuple((input,))
        return FurrowsOutputFactory._unpack_tuple(input)

    @staticmethod
    def from_label(
        lines: List[List[Line]],
        enabled_line_classes: List[str],
        output_shape: Tuple[int, int],
        downsample_rate: float,
        line_thickness: int,
        dilation_iterations: int,
    ) -> FurrowsOutput:
        direction, mask = make_2theta_tensor(
            lines, output_shape, enabled_line_classes, downsample_rate, line_thickness, dilation_iterations
        )

        return FurrowsOutput(0, mask, direction)

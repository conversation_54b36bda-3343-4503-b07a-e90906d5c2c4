from typing import Any, Dict, List, Optional, <PERSON><PERSON>

import torch

from cv.furrows.furrows_python import generate_rho_theta
from deeplearning.furrows.config import FurrowsConfig
from deeplearning.furrows.line_utils import angle_deltas, get_angle_difference, get_pixel_offset
from deeplearning.furrows.model_output import FurrowsOutput
from deeplearning.utils.tensor import recursive_detach, recursive_move


def get_pixel_offset_and_angle_difference(
    central_line: List[Tuple[float, float]], central_line_hat: List[Tuple[float, float]], shape: Tuple[int, int]
) -> Tuple[Optional[float], Optional[float]]:
    p = None
    a = None
    if len(central_line) > 0 and len(central_line_hat) > 0:
        p = get_pixel_offset(central_line_hat[0], central_line[0], shape)
        a = get_angle_difference(central_line_hat[0], central_line[0])
    elif len(central_line) > 0 or len(central_line_hat) > 0:
        # Punish if central line isn't produced
        p = float(shape[1])
        a = 1.0
    else:
        p = 0.0
        a = 0.0

    return p, a


class Metrics:
    def __init__(
        self,
        out: FurrowsOutput,
        out_hat: FurrowsOutput,
        mask_loss: torch.Tensor,
        direction_loss: torch.Tensor,
        line_enabled_classes: torch.Tensor,
        config: FurrowsConfig,
        learning_rate: float,
        segm_threshold: float = 0.5,
    ) -> None:
        self.mask_loss = mask_loss
        self.direction_loss = direction_loss
        self.weighted_mask_loss = self.mask_loss * config.mask_loss_multiplier
        self.weighted_direction_loss = self.direction_loss * config.direction_loss_multiplier
        self.loss = self.weighted_direction_loss + self.weighted_mask_loss

        self.learning_rate = torch.tensor(learning_rate)
        self.config = config
        self.width = out.direction.shape[3]

        with torch.no_grad():
            # line
            self.line_enabled_classes = line_enabled_classes
            if any(line_enabled_classes):
                self.angle_delta = angle_deltas(
                    out.direction, out_hat.direction, out.mask, line_enabled_classes, threshold=segm_threshold
                )
            else:
                self.angle_delta = torch.tensor([0.0] * out.mask.shape[0])
            self.angle_mae = torch.mean(torch.abs(torch.tensor(self.angle_delta)))

            (self.central_line_angles, self.central_line_offsets) = self.compute_line_angle_and_pixel_mae(out, out_hat)
            self.central_line_angle_mae = torch.mean(self.central_line_angles)
            self.central_line_offset_mae = torch.mean(self.central_line_offsets)

    def detach(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_detach(self.__dict__, whitelist=[FurrowsConfig])
        return obj

    def cpu(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_move(self.__dict__, torch.device("cpu"), whitelist=[FurrowsConfig])
        return obj

    def get_image_metrics(self) -> List[List[Tuple[str, float]]]:
        batch_metrics = []
        for n in range(len(self.angle_delta)):
            batch_metrics.append(
                [
                    ("angle_delta", self.angle_delta[n].item()),
                    ("central_line_angle", self.central_line_angles[n].item()),
                    ("central_line_offset", self.central_line_offsets[n].item()),
                ]
            )
        return batch_metrics

    def compute_line_angle_and_pixel_mae(
        self, out: FurrowsOutput, out_hat: FurrowsOutput
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        origin = (out.direction.shape[3] // 2, out.direction.shape[2] // 2)
        p = []
        a = []
        for batch_ind in range(out.direction.shape[0]):
            direction = out.direction[batch_ind]
            direction_hat = out_hat.direction[batch_ind]
            mask = out.mask[batch_ind].float()
            mask_hat = out_hat.mask[batch_ind].float()
            for class_ind in range(out.direction.shape[1]):
                if self.line_enabled_classes[class_ind]:
                    central_line = generate_rho_theta(
                        direction[class_ind], mask[class_ind], origin=origin, threshold=self.config.hough_threshold,
                    )
                    central_line_hat = generate_rho_theta(
                        direction_hat[class_ind],
                        mask_hat[class_ind],
                        origin=origin,
                        threshold=self.config.hough_threshold,
                    )

                    (_p, _a) = get_pixel_offset_and_angle_difference(
                        central_line, central_line_hat, (out.direction.shape[2], out.direction.shape[3])
                    )
                    if _p is not None:
                        p.append(_p)
                    if _a is not None:
                        a.append(_a)

        if not len(p) or not len(a):
            return torch.tensor(0.0), torch.tensor(0.0)

        p_mean = torch.tensor(p)
        a_mean = torch.tensor(a)
        return a_mean, p_mean

    def to_dict(self, prefix: str = "", **additional_metrics: Dict[str, Any]) -> Dict[str, Any]:
        d: Dict[str, Any] = dict()
        d[f"{prefix}loss"] = self.loss

        d[f"{prefix}mask_loss"] = self.mask_loss
        d[f"{prefix}direction_loss"] = self.direction_loss
        d[f"{prefix}weighted_mask_loss"] = self.weighted_mask_loss
        d[f"{prefix}weighted_direction_loss"] = self.weighted_direction_loss
        d[f"{prefix}angle_mae"] = self.angle_mae
        d[f"{prefix}central_line_offset_mae"] = self.central_line_offset_mae
        d[f"{prefix}central_line_angle_mae"] = self.central_line_angle_mae

        for key, value in additional_metrics.items():
            d[key] = value

        return d


class AverageMetrics(object):
    def __init__(
        self, metrics: List[Metrics], segm_enabled_classes: torch.Tensor, line_enabled_classes: torch.Tensor,
    ) -> None:
        assert all([m.loss.device.type == "cpu" for m in metrics]), "Metrics have not been .cpu()'d"
        self.metrics = metrics
        self.segm_enabled_classes = segm_enabled_classes
        self.line_enabled_classes = line_enabled_classes

    def compute_avg_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.loss for m in self.metrics]).mean()

    def compute_avg_mask_loss(self) -> torch.Tensor:
        if (len(self.metrics)) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.mask_loss for m in self.metrics]).mean()

    def compute_avg_direction_loss(self) -> torch.Tensor:
        if (len(self.metrics)) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.direction_loss for m in self.metrics]).mean()

    def compute_avg_weighted_mask_loss(self) -> torch.Tensor:
        if (len(self.metrics)) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.weighted_mask_loss for m in self.metrics]).mean()

    def compute_avg_weighted_direction_loss(self) -> torch.Tensor:
        if (len(self.metrics)) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.weighted_direction_loss for m in self.metrics]).mean()

    def compute_avg_angle_mae(self) -> torch.Tensor:
        if (len(self.metrics)) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.angle_mae for m in self.metrics]).mean()

    def get_all_angle_deltas(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor([0])

        deltas = torch.cat([m.angle_delta for m in self.metrics])
        return deltas

    def compute_avg_central_line_offset_mae(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.central_line_offset_mae for m in self.metrics]).mean()

    def compute_avg_central_line_angle_mae(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.central_line_angle_mae for m in self.metrics]).mean()

    def compute_line_oec(self) -> torch.Tensor:
        # dividing by 3 makes the output nicer
        p_mean = torch.tanh(-torch.log(self.compute_avg_central_line_offset_mae() / self.metrics[0].width) / 3)
        a_mean = torch.tanh(-torch.log(self.compute_avg_central_line_angle_mae()) / 3)

        # line_offset can be greater than one when training first begins, as the predicted
        # x at the bottom of the image may be more than the image-width away from the true
        # value. Tanh(-log(>1)) results in negative values, so we relu here to make the
        # harmonic mean between 0..1
        p_mean = torch.relu(p_mean)

        # OEC should be in 0..1 range
        return (2 * p_mean * a_mean) / (p_mean + a_mean + 1e-6)

    def compute_oec(self) -> torch.Tensor:
        """Overall evaluation criteria"""
        return self.compute_line_oec()

    def to_dict(self, prefix: str = "", **additional_metrics: Dict[str, Any]) -> Dict[str, Any]:  # noqa: C901
        d: Dict[str, Any] = dict()
        d[f"avg_{prefix}loss"] = self.compute_avg_loss()

        d[f"avg_{prefix}mask_loss"] = self.compute_avg_mask_loss()
        d[f"avg_{prefix}direction_loss"] = self.compute_avg_direction_loss()
        d[f"avg_{prefix}weighted_mask_loss"] = self.compute_avg_weighted_mask_loss()
        d[f"avg_{prefix}weighted_direction_loss"] = self.compute_avg_weighted_direction_loss()
        d[f"avg_{prefix}angle_mae"] = self.compute_avg_angle_mae()
        d[f"avg_{prefix}central_line_offset_mae"] = self.compute_avg_central_line_offset_mae()
        d[f"avg_{prefix}central_line_angle_mae"] = self.compute_avg_central_line_angle_mae()

        # overall evaluation criteria
        d[f"{prefix}oec"] = self.compute_oec()

        d[f"{prefix}learning_rate"] = self.metrics[0].learning_rate

        for key, value in additional_metrics.items():
            d[key] = value

        return d

    def get_hist_dict(self, prefix: str = "") -> Dict[str, List[float]]:
        d = {}
        d[f"{prefix}angle_deltas"] = self.get_all_angle_deltas().tolist()
        return d

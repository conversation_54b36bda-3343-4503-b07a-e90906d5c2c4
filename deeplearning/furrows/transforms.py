import math
import random
from typing import Any, Callable, List, Tuple

import cv2
import numpy as np
import torch
import torch.nn.functional as F
import torchvision.transforms as T
import torchvision.transforms.functional

from deeplearning.furrows.dataset_types import Line
from deeplearning.furrows.metadata import ImageMetadata
from deeplearning.utils.images import _random_sample_around_one, random_gaussian_noise
from deeplearning.utils.resize_utils import interpolate

Transform = Callable[
    [torch.Tensor, List[Line], ImageMetadata], Tuple[torch.Tensor, List[Line]],
]


class RandomChoice(T.RandomChoice):
    def __init__(self, augmentations: List[Transform]) -> None:
        self._augmentations = augmentations

    def __call__(self, *args: Any, **kwargs: Any) -> Tuple[torch.Tensor, List[Line]]:
        augmentation = random.choice(self._augmentations)
        return augmentation(*args, **kwargs)


class Compose(T.Compose):
    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        for t in self.transforms:
            input, lines = t(input, lines, image_meta)
        return input, lines


class TransposeAlignLongerSide:
    def __init__(self, want_longer_width: bool):
        self.want_longer_width = want_longer_width

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        if self.want_longer_width and input.shape[1] > input.shape[2]:
            # Want longer width, but height is currently longer
            assert len(input.shape) == 3, f"Expected 3-dim input shape: {input.shape}"
            assert input.shape[0] in [3, 4], f"Expected CHW/(C+D)HW order: {input.shape}"
            input = input.permute(0, 2, 1)
            for line in lines:
                line.x1, line.y1 = line.y1, line.x1
                line.x2, line.y2 = line.y2, line.x2

        return input, lines


class ColorJitter(T.ColorJitter):
    def __init__(
        self,
        brightness: Tuple[float, float],
        saturation: Tuple[float, float],
        hue: Tuple[float, float],
        gamma: Tuple[float, float],
        contrast: Tuple[float, float],
    ):
        self.brightness = brightness
        self.saturation = saturation
        self.hue = hue
        self.gamma = gamma
        self.contrast = contrast
        super().__init__(brightness=self.brightness, hue=self.hue, saturation=self.saturation, contrast=self.contrast)

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        depth = None
        if input.shape[0] == 4:
            depth = input[3:4, :, :]
            input = input[:3, :, :]
        ret_input = super().__call__(input)
        ret_input = ret_input ** _random_sample_around_one(*self.gamma)
        if depth is not None:
            ret_input = torch.cat((ret_input, depth), 0)
        return ret_input, lines


class RandomBlur:
    def __init__(self, min_blur_radius: int, max_blur_radius: int) -> None:
        self.min_blur_radius = min_blur_radius
        self.max_blur_radius = max_blur_radius

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        blur_radius = random.randint(self.min_blur_radius, self.max_blur_radius)
        if blur_radius > 0:
            img = input.numpy().transpose(1, 2, 0)
            img = cv2.blur(img, (blur_radius * 2 + 1, blur_radius * 2 + 1))
            input = torch.tensor(img.transpose(2, 0, 1))
        return input, lines


class GaussianNoise:
    def __init__(self, stddev: float) -> None:
        self.stddev = stddev

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        return random_gaussian_noise(input, self.stddev)[0], lines


class RandomSimBlue:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        assert input.shape[0] in [3, 4], f"Expected CHW/(C+D)HW order: {input.shape}"
        if random.random() < self.p:
            input[2] = 0.75 * input[1] + 0.25 * input[0]
        return input, lines


class RandomRotation:
    def __init__(
        self, min_scale: float, max_scale: float, min_angle: int, max_angle: int, border_mode: int = 0
    ) -> None:
        self.min_scale = min_scale
        self.max_scale = max_scale
        self.min_angle = min_angle
        self.max_angle = max_angle
        self.border_mode = border_mode

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        cx = input.shape[2] // 2
        cy = input.shape[1] // 2
        angle = random.uniform(self.min_angle, self.max_angle)
        size_multiplier = random.uniform(self.min_scale, self.max_scale)
        size = (int(round(input.shape[1] * size_multiplier)), int(round(input.shape[2] * size_multiplier)))

        M = cv2.getRotationMatrix2D((cx, cy), angle, 1.0)
        np_points_start = np.array([[line.x1, line.y1] for line in lines])
        np_points_end = np.array([[line.x2, line.y2] for line in lines])
        np_points_start = np.expand_dims(np_points_start, 1)
        np_points_end = np.expand_dims(np_points_end, 1)

        warped_lines = []
        if len(lines) > 0:
            warped_points_np_start = np.squeeze(cv2.transform(np_points_start, M), 1)
            warped_points_np_end = np.squeeze(cv2.transform(np_points_end, M), 1)

            for i in range(len(warped_points_np_start)):
                line = lines[i]
                line.x1 = warped_points_np_start[i][0] * size_multiplier
                line.y1 = warped_points_np_start[i][1] * size_multiplier
                line.x2 = warped_points_np_end[i][0] * size_multiplier
                line.y2 = warped_points_np_end[i][1] * size_multiplier
                warped_lines.append(line)

        # Pad image by half its size, then rotate and scale. Select inner input size from the result.
        warped_input = interpolate(
            T.functional.rotate(
                input, angle, interpolation=T.InterpolationMode.BILINEAR, fill=self.border_mode,
            ).unsqueeze(0),
            (size[0], size[1]),
        ).squeeze(0)

        return warped_input, warped_lines


class RandomCrop:
    def __init__(self, size: Tuple[int, int]) -> None:
        self.size = size

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        assert input.shape[2] >= self.size[1], "RandomCrop width is larger than image width"
        assert input.shape[1] >= self.size[0], "RandomCrop height is larger than image height"
        ox = int(random.uniform(0, input.shape[2] - self.size[1]))
        oy = int(random.uniform(0, input.shape[1] - self.size[0]))
        cropped_input = input[:, oy : oy + self.size[0], ox : ox + self.size[1]]
        return cropped_input, lines


class PadIfNeeded:
    def __init__(self, size: Tuple[int, int]) -> None:
        self.size = size

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        pad_width = max(self.size[1] - input.shape[2], 0)
        pad_height = max(self.size[0] - input.shape[1], 0)
        pad_sizes = [
            math.floor(pad_width / 2),
            math.ceil(pad_width / 2),
            math.floor(pad_height / 2),
            math.ceil(pad_height / 2),
        ]
        input = F.pad(input, pad_sizes)
        for line in lines:
            line.x1 += pad_sizes[0]
            line.y1 += pad_sizes[2]
            line.x2 += pad_sizes[0]
            line.y2 += pad_sizes[2]
        return input, lines


class CenterCrop:
    def __init__(self, size: Tuple[int, int], max_random_translation: Tuple[int, int]) -> None:
        self.size = size
        self.max_random_translation = max_random_translation

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        random_translation_x = random.randint(-self.max_random_translation[1], self.max_random_translation[1])
        random_translation_y = random.randint(-self.max_random_translation[0], self.max_random_translation[0])

        half_size = (self.size[0] // 2, self.size[1] // 2)
        center_x = input.shape[-1] // 2
        center_y = input.shape[-2] // 2
        start_x = max(center_x - (half_size[1] + random_translation_x), 0)
        start_y = max(center_y - (half_size[0] + random_translation_y), 0)
        input = torchvision.transforms.functional.crop(input, start_y, start_x, self.size[0], self.size[1])
        for line in lines:
            line.x1 -= start_x
            line.y1 -= start_y
            line.x2 -= start_x
            line.y2 -= start_y
        return input, lines


class Resize:
    def __init__(self, size: Tuple[int, int]) -> None:
        self.size = size

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        current_aspect_ratio = input.shape[-2] / input.shape[-1]
        expected_aspect_ratio = self.size[0] / self.size[1]
        padding = (0, 0, 0, 0)
        if current_aspect_ratio > expected_aspect_ratio:
            needed_width = input.shape[-2] / expected_aspect_ratio
            needed_width_padding = input.shape[-1] - needed_width
            padding = (math.floor(needed_width_padding / 2), math.ceil(needed_width_padding / 2), 0, 0)
        elif current_aspect_ratio < expected_aspect_ratio:
            needed_height = input.shape[-1] * expected_aspect_ratio
            needed_height_padding = input.shape[-2] - needed_height
            padding = (0, 0, math.floor(needed_height_padding / 2), math.ceil(needed_height_padding / 2))

        input = F.pad(input, padding)
        after_pad_shape = input.shape[:-2]
        input = interpolate(input.unsqueeze(0), self.size).squeeze(0)

        width_ratio = self.size[0] / after_pad_shape[-1]
        height_ratio = self.size[1] / after_pad_shape[-2]
        for line in lines:
            line.x1 = (line.x1 + padding[0]) * width_ratio
            line.y1 = (line.y1 + padding[2]) * height_ratio
            line.x2 = (line.x2 + padding[0]) * width_ratio
            line.y2 = (line.y2 + padding[2]) * height_ratio

        return input, lines


class RandomVerticalFlip:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        if random.random() < self.p:
            input = torch.flip(input, dims=[-2])
            for line in lines:
                line.y1 = input.shape[-2] - line.y1
                line.y2 = input.shape[-2] - line.y2
        return input, lines


class RandomHorizontalFlip:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        if random.random() < self.p:
            input = torch.flip(input, dims=[-1])
            for line in lines:
                line.x1 = input.shape[-1] - line.x1
                line.x2 = input.shape[-1] - line.x2
        return input, lines


class Normalize(T.Normalize):
    def __call__(
        self, input: torch.Tensor, lines: List[Line], image_meta: ImageMetadata
    ) -> Tuple[torch.Tensor, List[Line]]:
        assert input.shape[0] in [3, 4], f"Expected CHW/(C+D)HW order: {input.shape}"
        if input.shape[0] == 3:
            depth = torch.zeros((1, input.shape[1], input.shape[2]), device=input.device)
            input = torch.cat((input, depth), 0)
            remove_depth = True
        else:
            remove_depth = False

        normalized_image = super().__call__(input)

        if remove_depth:
            normalized_image = normalized_image[:3, :, :]

        return normalized_image, lines


class DilateMask:
    EPS = 1e-3

    def __init__(self, iterations: int) -> None:
        self.iterations = iterations

    @staticmethod
    def _conv2d(mask: torch.Tensor, size: int) -> Tuple[torch.Tensor, int]:
        w = torch.ones(1, 1, size * 2 + 1, size * 2 + 1).to(mask.device)
        mask = mask.float()  # turn to float for convolving
        mask = F.conv2d(mask, w, padding=size)
        return mask, w.numel()

    def __call__(self, mask: torch.Tensor,) -> torch.Tensor:
        dilated_mask = mask
        for x in range(self.iterations):
            dilated_mask, _ = self._conv2d(dilated_mask, size=1)
            dilated_mask = dilated_mask > DilateMask.EPS
        mask = torch.tensor(0.5 * mask + 0.5 * dilated_mask)
        return mask

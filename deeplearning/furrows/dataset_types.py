from typing import List

import torch

from deeplearning.furrows.metadata import ImageMetadata


class Line:
    def __init__(self, x1: float, y1: float, x2: float, y2: float, category: str):
        self.x1 = x1
        self.y1 = y1
        self.x2 = x2
        self.y2 = y2
        self.category = category


class FurrowsDatapoint:
    def __init__(
        self, image: torch.Tensor, lines: List[Line], batch_enabled_classes: torch.Tensor, metadata: ImageMetadata,
    ):
        self.image = image
        self.batch_enabled_classes = batch_enabled_classes
        self.metadata = metadata
        self.lines = lines

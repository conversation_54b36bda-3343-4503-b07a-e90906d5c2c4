import math
from typing import Any, <PERSON>, Tuple, Union, cast

import cv2
import numpy as np
import numpy.typing as npt
import torch

from cv.furrows.furrows_python import compute_theta_tensor


def render_hough_lines(
    rho_thetas: List[Tuple[float, float]],
    image_shape: Tuple[int, ...],
    thickness: int = 3,
    origin: Tuple[int, int] = (0, 0),
) -> npt.NDArray[Any]:
    mask: npt.NDArray[Any] = np.zeros((image_shape[0], image_shape[1]))

    for (rho, theta) in rho_thetas:
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)

        x0 = cos_theta * rho + origin[0]
        y0 = sin_theta * rho + origin[1]
        x1 = int(x0 + 2000 * (-sin_theta))
        y1 = int(y0 + 2000 * (cos_theta))
        x2 = int(x0 - 2000 * (-sin_theta))
        y2 = int(y0 - 2000 * (cos_theta))

        mask = cv2.line(img=mask, pt1=(x1, y1), pt2=(x2, y2), color=(1,), thickness=thickness)

    return mask


def angle_deltas(
    out_direction: torch.Tensor,
    out_hat_direction: torch.Tensor,
    out_mask: torch.Tensor,
    enabled_classes: torch.Tensor,
    threshold: float,
    flip_threshold: float = 1e-3,
) -> torch.Tensor:
    assert len(out_direction.shape) == 5, f"out_direction must be 5D: {out_direction.shape}"
    assert len(out_hat_direction.shape) == 5, f"out_hat_direction must be 5D: {out_hat_direction.shape}"
    assert len(out_mask.shape) == 4, f"out_mask must be 4D: {out_mask.shape}"

    theta_tensor = compute_theta_tensor(out_direction, flip_threshold)
    theta_hat_tensor = compute_theta_tensor(out_hat_direction, flip_threshold)
    theta = torch.acos(theta_tensor[..., 0])
    theta_hat = torch.acos(theta_hat_tensor[..., 0])

    angle_deltas = []
    for n in range(out_mask.shape[0]):
        direction_mask = out_mask[n] * enabled_classes.reshape((-1, 1, 1)).to(out_mask[n].device) > threshold
        delta = theta[n][direction_mask] - theta_hat[n][direction_mask]
        angle_deltas.append(delta.mean().item())

    return torch.tensor(angle_deltas)


def get_pixel_offset(
    rho_theta_hat: Tuple[float, float],
    rho_theta: Tuple[float, float],
    image_shape: Union[torch.Tensor, Tuple[int, int]],
) -> float:
    (rho_hat, theta_hat) = rho_theta_hat
    (rho, theta) = rho_theta
    height = image_shape[0]
    eps = 1e-6

    x = (rho - height * np.sin(theta)) / (np.cos(theta) + eps)
    x_hat = (rho_hat - height * np.sin(theta_hat)) / (np.cos(theta_hat) + eps)

    return cast(float, np.abs(x - x_hat))


def get_angle_difference(rho_theta_hat: Tuple[float, float], rho_theta: Tuple[float, float]) -> float:
    (_, theta_hat) = rho_theta_hat
    (_, theta) = rho_theta

    return cast(float, np.abs(theta - theta_hat) / math.pi)

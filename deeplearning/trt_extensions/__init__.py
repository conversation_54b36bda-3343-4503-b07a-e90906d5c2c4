import tensorrt as trt

from deeplearning.trt_extensions.cast import *  # noqa
from deeplearning.trt_extensions.constants import *  # noqa
from deeplearning.trt_extensions.dont_fuse import *  # noqa
from deeplearning.trt_extensions.p2p_loops import *  # noqa
from deeplearning.trt_extensions.precision import *  # noqa
from deeplearning.trt_extensions.sum import *  # noqa
from deeplearning.trt_extensions.upsample import *  # noqa

try:

    def load_plugins() -> None:
        import os
        import ctypes

        version_number = trt.__version__.split(".")[0]
        ctypes.CDLL(
            os.path.join(os.path.dirname(__file__), "..", "..", "lib64", f"libtrt_extensions_{version_number}.so")
        )

        registry = trt.get_plugin_registry()
        maka_creators = [c for c in registry.plugin_creator_list if c.plugin_namespace == "maka_trt_extensions"]
        for c in maka_creators:
            registry.register_creator(c, "maka_trt_extensions")

    load_plugins()
except Exception as e:
    print(e)

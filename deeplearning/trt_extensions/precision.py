import tensorrt
import torch
import torch.nn as nn
from torch2trt import Conversion<PERSON>ontext

from deeplearning.trt_extensions.utils import maka_trt_converter

__all__ = ["TRTPrecision"]


class TRTPrecision(nn.Module):
    """This identity layer sets the precision of subsequent layers."""

    def forward(self, x: torch.Tensor, precision: tensorrt.DataType) -> torch.Tensor:
        return x


@maka_trt_converter("TRTPrecision.forward")
def convert_TRTPrecision(ctx: ConversionContext) -> None:
    input = ctx.method_args[1]
    output = ctx.method_return
    ctx.network.set_precision(ctx.method_args[2])
    output._trt = input._trt

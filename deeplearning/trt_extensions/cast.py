from typing import List

import tensorrt
from torch2trt import ConversionContext, add_missing_trt_tensors, tensorrt_converter

__all__: List[str] = []


def _do_conversion(ctx: ConversionContext, dtype: tensorrt.DataType) -> None:
    input = ctx.method_args[0]
    input_trt = add_missing_trt_tensors(ctx.network, [input])[0]
    output = ctx.method_return

    if tensorrt.__version__ == "8.0.1.6":
        layer = ctx.network.add_identity(input_trt)
        layer.set_output_type(0, dtype)
        output._trt = layer.get_output(0)
    else:
        layer = ctx.network.add_cast(input_trt, dtype)
        output._trt = layer.get_output(0)


@tensorrt_converter("torch.Tensor.bool")  # type: ignore
def convert_bool(ctx: ConversionContext) -> None:
    _do_conversion(ctx, tensorrt.DataType.BOOL)


@tensorrt_converter("torch.Tensor.float")  # type: ignore
def convert_float(ctx: ConversionContext) -> None:
    _do_conversion(ctx, tensorrt.DataType.FLOAT)


@tensorrt_converter("torch.Tensor.half")  # type: ignore
def convert_half(ctx: ConversionContext) -> None:
    _do_conversion(ctx, tensorrt.DataType.HALF)


@tensorrt_converter("torch.Tensor.int")  # type: ignore
def convert_int(ctx: ConversionContext) -> None:
    _do_conversion(ctx, tensorrt.DataType.INT32)


@tensorrt_converter("torch.Tensor.long")  # type: ignore
def convert_long(ctx: ConversionContext) -> None:
    _do_conversion(ctx, tensorrt.DataType.INT64)

from typing import List

from torch2trt import ConversionContext, add_trt_constant, tensorrt_converter

__all__: List[str] = []


@tensorrt_converter("torch.arange")  # type: ignore
@tensorrt_converter("torch.tensor")  # type: ignore
@tensorrt_converter("torch.zeros")  # type: ignore
@tensorrt_converter("torch.zeros_like")  # type: ignore
def convert_tensor(ctx: ConversionContext) -> None:
    output = ctx.method_return
    output._trt = add_trt_constant(ctx.network, output.contiguous())

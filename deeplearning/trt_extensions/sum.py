from typing import Any

import tensorrt as trt
from torch2trt import ConversionContext, tensorrt_converter
from torch2trt.torch2trt import trt_

from generated.deeplearning.trt_extensions.native.sum_pb2 import SumMessage


def get_sum_plugin(dim: int) -> Any:
    PLUGIN_NAME = "sum"
    registry = trt.get_plugin_registry()
    creator = [
        c for c in registry.plugin_creator_list if c.name == PLUGIN_NAME and c.plugin_namespace == "maka_trt_extensions"
    ][0]
    message = SumMessage(dim=dim,)
    return creator.deserialize_plugin(PLUGIN_NAME, message.SerializeToString())


@tensorrt_converter("torch.Tensor.sum")  # type: ignore
def convert_TorchTensorSum(ctx: ConversionContext) -> None:
    tensor = trt_(ctx.network, ctx.method_args[0])
    output = ctx.method_return

    plugin = get_sum_plugin(ctx.method_kwargs["dim"])
    output_layer = ctx.network.add_plugin_v2([tensor], plugin)

    output._trt = output_layer.get_output(0)

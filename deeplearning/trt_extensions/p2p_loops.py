from typing import Any, List

import tensorrt as trt
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch2trt import ConversionContext
from torch2trt.torch2trt import trt_

from deeplearning.trt_extensions.utils import maka_trt_converter
from generated.deeplearning.trt_extensions.native.instance_wise_conv2d_pb2 import InstanceWiseConv2DMessage
from generated.deeplearning.trt_extensions.native.upsample_align_pb2 import UpsampleAlignMessage
from generated.deeplearning.trt_extensions.native.upsample_pad_crop_pb2 import UpsamplePadCropMessage
from generated.deeplearning.trt_extensions.native.upsample_threshold_pb2 import UpsampleThresholdMessage

__all__ = ["InstanceWiseConv2dLoop", "UpsamplePadCropLoop", "UpsampleAlignLoop", "UpsampleThreshold"]

EPS = 1e-5


def upsample_like(a: torch.Tensor, shape: List[int], min_threshold: float = EPS, eps: float = EPS) -> torch.Tensor:
    assert len(a.shape) == 4, f"Expected 4D tensor, got: {a.shape}"
    assert len(shape) >= 2, f"Expected >= 2D tensor, got: {shape}"
    a_interpolated_list = [
        F.interpolate(
            (a[idx : idx + 1] >= max(min_threshold, a[idx].max().item())).float(),
            shape[-2:],
            mode="bilinear",
            align_corners=True,
        )
        for idx in range(a.shape[0])
    ]
    a_interpolated_thresholded = torch.cat([(a_i >= max(eps, a_i.max())).float() for a_i in a_interpolated_list])
    return a_interpolated_thresholded


def get_upsample_threshold_plugin(crop_height: int, crop_width: int, min_threshold: float) -> Any:
    PLUGIN_NAME = "upsample_threshold"
    registry = trt.get_plugin_registry()
    creator = [
        c for c in registry.plugin_creator_list if c.name == PLUGIN_NAME and c.plugin_namespace == "maka_trt_extensions"
    ][0]
    message = UpsampleThresholdMessage(crop_height=crop_height, crop_width=crop_width, min_threshold=min_threshold)
    return creator.deserialize_plugin(PLUGIN_NAME, message.SerializeToString())


def get_convolution_plugin(padding_height: int, padding_width: int, stride_height: int, stride_width: int) -> Any:
    PLUGIN_NAME = "instance_wise_conv2d"
    registry = trt.get_plugin_registry()
    creator = [
        c for c in registry.plugin_creator_list if c.name == PLUGIN_NAME and c.plugin_namespace == "maka_trt_extensions"
    ][0]
    message = InstanceWiseConv2DMessage(
        padding_height=padding_height,
        padding_width=padding_width,
        stride_height=stride_height,
        stride_width=stride_width,
    )
    return creator.deserialize_plugin(PLUGIN_NAME, message.SerializeToString())


def get_upsample_pad_crop_plugin(padding: int, crop_size: int) -> Any:
    PLUGIN_NAME = "upsample_pad_crop"
    registry = trt.get_plugin_registry()
    creator = [
        c for c in registry.plugin_creator_list if c.name == PLUGIN_NAME and c.plugin_namespace == "maka_trt_extensions"
    ][0]
    message = UpsamplePadCropMessage(padding=padding, crop_size=crop_size)
    return creator.deserialize_plugin(PLUGIN_NAME, message.SerializeToString())


def get_upsample_align_plugin(padding: int, post_padding: int, crop_size: int) -> Any:
    PLUGIN_NAME = "upsample_align"
    registry = trt.get_plugin_registry()
    creator = [
        c for c in registry.plugin_creator_list if c.name == PLUGIN_NAME and c.plugin_namespace == "maka_trt_extensions"
    ][0]
    message = UpsampleAlignMessage(padding=padding, post_padding=post_padding, crop_size=crop_size)
    return creator.deserialize_plugin(PLUGIN_NAME, message.SerializeToString())


class UpsampleThreshold(nn.Module):
    def forward(self, a: torch.Tensor, shape: List[int], min_threshold: float = EPS, eps: float = EPS) -> torch.Tensor:
        return upsample_like(a, shape, min_threshold, eps)


@maka_trt_converter("UpsampleThreshold.forward")
def convert_upsample_threshold_output(ctx: ConversionContext) -> None:
    image = trt_(ctx.network, ctx.method_args[1])
    shape = ctx.method_args[2]
    if len(ctx.method_args) > 3:
        min_threshold = ctx.method_args[3]
    else:
        min_threshold = ctx.method_kwargs["min_threshold"]

    output = ctx.method_return

    plugin = get_upsample_threshold_plugin(shape[2], shape[3], min_threshold)
    conv_layer = ctx.network.add_plugin_v2([image], plugin)
    output._trt = conv_layer.get_output(0)


FP16_INSTANCE_WISE_CONV2D_SCALE_FACTOR = 0.125


class InstanceWiseConv2dLoop(nn.Module):
    def forward(self, image: torch.Tensor, perspective: torch.Tensor, is_fp16: bool) -> torch.Tensor:
        out_list = []
        for idx in range(perspective.shape[0]):
            out_list.append(
                F.conv2d(
                    image[idx : idx + 1],
                    perspective[idx : idx + 1],
                    padding=(perspective.shape[2] // 2, perspective.shape[3] // 2),
                )
            )
        return torch.cat(out_list)


@maka_trt_converter("InstanceWiseConv2dLoop.forward")
def convert_InstanceWiseConv2dLoop(ctx: ConversionContext) -> None:
    image, fp16_scale_factor = trt_(ctx.network, ctx.method_args[1], FP16_INSTANCE_WISE_CONV2D_SCALE_FACTOR)
    perspective = trt_(ctx.network, ctx.method_args[2])
    is_fp16 = ctx.method_args[3]

    output = ctx.method_return

    # Scale values prior to convolution to avoid infinity
    if is_fp16:
        image = ctx.network.add_elementwise(image, fp16_scale_factor, trt.ElementWiseOperation.PROD).get_output(0)
        perspective = ctx.network.add_elementwise(
            perspective, fp16_scale_factor, trt.ElementWiseOperation.PROD
        ).get_output(0)

    plugin = get_convolution_plugin(perspective.shape[2] // 2, perspective.shape[3] // 2, 1, 1)
    conv_layer = ctx.network.add_plugin_v2([image, perspective], plugin)
    output._trt = conv_layer.get_output(0)


class UpsamplePadCropLoop(nn.Module):
    def forward(self, out_softmax1: torch.Tensor, image: torch.Tensor, padding: int, crop_size: int) -> torch.Tensor:
        image_crops_list = []
        for idx in range(out_softmax1.shape[0]):
            out_softmax_up_nz = upsample_like(out_softmax1[idx : idx + 1], list(image.shape)).nonzero()
            y, x = out_softmax_up_nz[0, 2], out_softmax_up_nz[0, 3]
            one_image_padded = F.pad(image[idx], [padding, padding, padding, padding])
            one_image_cropped = one_image_padded[:, y : y + crop_size, x : x + crop_size]
            image_crops_list.append(one_image_cropped)
        output = torch.stack(image_crops_list)
        return output


@maka_trt_converter("UpsamplePadCropLoop.forward")
def convert_UpsamplePadCropLoop(ctx: ConversionContext) -> None:
    out_softmax1 = trt_(ctx.network, ctx.method_args[1])
    image = trt_(ctx.network, ctx.method_args[2])
    padding = ctx.method_args[3]
    crop_size = ctx.method_args[4]
    output = ctx.method_return

    plugin = get_upsample_pad_crop_plugin(padding, crop_size)
    output_layer = ctx.network.add_plugin_v2([out_softmax1, image], plugin)
    output._trt = output_layer.get_output(0)


class UpsampleAlignLoop(nn.Module):
    def forward(
        self,
        out_softmax1: torch.Tensor,
        out_softmax2: torch.Tensor,
        image: torch.Tensor,
        perspective: torch.Tensor,
        padding: int,
        post_padding: int,
        crop_size: int,
    ) -> torch.Tensor:
        image_crops_list = []
        for idx in range(out_softmax1.shape[0]):
            out_softmax1_up_nz = upsample_like(out_softmax1[idx : idx + 1], list(image.shape)).nonzero()
            y, x = out_softmax1_up_nz[0, 2], out_softmax1_up_nz[0, 3]
            out_softmax2_up_nz = upsample_like(
                out_softmax2[idx : idx + 1], [perspective.shape[2] + 32, perspective.shape[3] + 32]
            ).nonzero()
            oy, ox = out_softmax2_up_nz[0, 2], out_softmax2_up_nz[0, 3]
            y += oy - padding
            x += ox - padding
            one_image_padded = F.pad(image[idx], [post_padding, post_padding, post_padding, post_padding])
            one_image_cropped = one_image_padded[
                :,
                y + post_padding - crop_size : y + post_padding + crop_size,
                x + post_padding - crop_size : x + post_padding + crop_size,
            ]
            image_crops_list.append(one_image_cropped)
        return torch.stack(image_crops_list)


@maka_trt_converter("UpsampleAlignLoop.forward")
def convert_UpsampleAlignLoop(ctx: ConversionContext) -> None:
    out_softmax1 = trt_(ctx.network, ctx.method_args[1])
    out_softmax2 = trt_(ctx.network, ctx.method_args[2])
    image = trt_(ctx.network, ctx.method_args[3])
    perspective = trt_(ctx.network, ctx.method_args[4])
    padding = ctx.method_args[5]
    post_padding = ctx.method_args[6]
    crop_size = ctx.method_args[7]
    output = ctx.method_return

    plugin = get_upsample_align_plugin(padding, post_padding, crop_size)
    conv_layer = ctx.network.add_plugin_v2([out_softmax1, out_softmax2, image, perspective], plugin)
    output._trt = conv_layer.get_output(0)

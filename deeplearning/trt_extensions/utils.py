from typing import Callable, TypeVar, cast

from torch2trt import tensorrt_converter, torch2trt

from deeplearning import trt_extensions

if not hasattr(torch2trt, "maka_trt_extensions"):
    # Fake-register Maka TRT extensions under torch2trt to enable
    # discoverability in its converter resolver.
    # TODO(asergeev): Send a PR to support a proper plugin model.
    setattr(torch2trt, "maka_trt_extensions", trt_extensions)


T = TypeVar("T")


def maka_trt_converter(fn_name: str) -> Callable[[T], T]:
    return cast(Callable[[T], T], tensorrt_converter("torch2trt.maka_trt_extensions." + fn_name))

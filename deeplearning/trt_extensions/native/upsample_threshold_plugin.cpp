#include <NvInfer.h>
#include <cublas_v2.h>
#include <cuda.h>
#include <cudnn.h>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "deeplearning/trt_extensions/native/upsample_threshold.pb.h"
#include "maka_plugin_base_dynamic.h"

using namespace nvinfer1;

namespace trt_extensions {

template <typename T>
int upsample_threshold(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
                       const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream,
                       float min_threshold);

class UpsampleThresholdPlugin : public MakaDynamicPlugin<UpsampleThresholdPlugin, UpsampleThresholdMessage> {
public:
  UpsampleThresholdPlugin(UpsampleThresholdMessage message) : MakaDynamicPlugin(message, "upsample_threshold") {}

  nvinfer1::DimsExprs getOutputDimensions(int outputIndex, const nvinfer1::DimsExprs* inputs, int nbInputs,
                                          nvinfer1::IExprBuilder& exprBuilder) noexcept override {
    nvinfer1::DimsExprs output;
    output.nbDims = 4;

    output.d[0] = inputs[0].d[0];
    output.d[1] = inputs[0].d[1];
    output.d[2] = exprBuilder.constant(message.crop_height());
    output.d[3] = exprBuilder.constant(message.crop_width());

    return output;
  }

  bool supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) noexcept override {
    const PluginTensorDesc& in1 = inOut[0];

    if (pos == 0) {
      return in1.type == nvinfer1::DataType::kFLOAT && in1.format == nvinfer1::TensorFormat::kLINEAR;
    }

    // pos == 2, accessing information about output tensor
    const PluginTensorDesc& out = inOut[1];

    return (in1.type == out.type && in1.format == out.format);
  }

  void configurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int nbInputs,
                       const nvinfer1::DynamicPluginTensorDesc* out, int nbOutputs) noexcept override {}

  size_t getWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int nbInputs,
                          const nvinfer1::PluginTensorDesc* outputs, int nbOutputs) const noexcept override {
    return 0;
  }

  int enqueue(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
              const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream) noexcept override {
    if (outputDesc[0].type == nvinfer1::DataType::kFLOAT) {
      return upsample_threshold<float>(inputDesc, outputDesc, inputs, outputs, workspace, stream,
                                       message.min_threshold());
    } else if (outputDesc[0].type == nvinfer1::DataType::kHALF) {
      return upsample_threshold<half>(inputDesc, outputDesc, inputs, outputs, workspace, stream,
                                      message.min_threshold());
    } else {
      // Unsupported
      return 1;
    }
  }
};

class UpsampleThresholdPluginCreator
    : public MakaDynamicPluginCreator<UpsampleThresholdPlugin, UpsampleThresholdMessage> {
public:
  UpsampleThresholdPluginCreator() : MakaDynamicPluginCreator("upsample_threshold") {}
};

REGISTER_TENSORRT_PLUGIN(UpsampleThresholdPluginCreator);

} // namespace trt_extensions

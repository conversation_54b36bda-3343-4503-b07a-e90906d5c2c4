file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp *.cu)

file(GLOB PROTO_FILES *.proto)

foreach(PROTO_FILE ${PROTO_FILES})
CompileProto(${PROTO_FILE} GENERATED_PATH GOPKG proto/calibration LANGS python mypy cpp)
endforeach()

string(REGEX REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "${GENERATED_PATH}" PROTO_GEN "${PROTO_FILES}")
string(REGEX REPLACE "[.]proto" ".pb.cc" PROTO_SOURCES "${PROTO_GEN}")

add_library(trt_extensions_proto SHARED ${PROTO_SOURCES})
target_link_libraries(trt_extensions_proto PUBLIC protobuf)

add_library(trt_extensions_8 SHARED ${SOURCES})
if (CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "aarch64")
target_link_libraries(trt_extensions_8 PUBLIC nvinfer nvinfer_plugin nvrtc nvrtc-builtins cudnn)
target_link_libraries(trt_extensions_8 PUBLIC torch cudart trt_extensions_proto)
else()
target_include_directories(trt_extensions_8 SYSTEM BEFORE PRIVATE /opt/TensorRT-*******/targets/x86_64-linux-gnu/include/)
target_link_libraries(trt_extensions_8 PUBLIC /opt/TensorRT-*******/targets/x86_64-linux-gnu/lib/libnvinfer.so /opt/TensorRT-*******/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so nvrtc_static nvrtc-builtins_static nvptxcompiler_static cudnn)
target_link_libraries(trt_extensions_8 PUBLIC torch cuda cudart trt_extensions_proto)
endif()
target_compile_options(trt_extensions_8 PRIVATE "-w")
target_link_directories(trt_extensions_8 PUBLIC /usr/local/lib /usr/local/cuda/lib64/)
set_target_properties(trt_extensions_8 PROPERTIES OUTPUT_NAME libtrt_extensions_8.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/lib64)

if (NOT CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "aarch64")
add_library(trt_extensions_10 SHARED ${SOURCES})
target_include_directories(trt_extensions_10 SYSTEM BEFORE PRIVATE /opt/TensorRT-********/targets/x86_64-linux-gnu/include/)
target_link_libraries(trt_extensions_10 PUBLIC /opt/TensorRT-********/targets/x86_64-linux-gnu/lib/libnvinfer.so /opt/TensorRT-********/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so nvrtc_static nvrtc-builtins_static nvptxcompiler_static cudnn)
target_link_libraries(trt_extensions_10 PUBLIC torch cuda cudart trt_extensions_proto)
target_compile_options(trt_extensions_10 PRIVATE "-w")
target_link_directories(trt_extensions_10 PUBLIC /usr/local/lib /usr/local/cuda/lib64/)
set_target_properties(trt_extensions_10 PROPERTIES OUTPUT_NAME libtrt_extensions_10.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/lib64)
endif()
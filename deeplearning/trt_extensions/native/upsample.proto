syntax = "proto3";

import "deeplearning/trt_extensions/native/common.proto";

package trt_extensions;

message UpsampleMessage {
  int64 width = 1;
  int64 height = 2;

  // below params are configured by TRT and not set by user
  DataTypeMessage dtype = 3;
  DataFormatMessage format = 4;
  repeated int64 input_size = 5;
  repeated int64 output_size = 6;
  float input_scale = 7;
  float output_scale = 8;
}

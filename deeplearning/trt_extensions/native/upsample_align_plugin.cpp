#include <NvInfer.h>
#include <cublas_v2.h>
#include <cuda.h>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "deeplearning/trt_extensions/native/upsample_align.pb.h"
#include "maka_plugin_base_dynamic.h"

using namespace nvinfer1;

namespace trt_extensions {

template <typename T>
int upsample_align(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
                   const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream, int padding,
                   int post_padding, int crop_size);

class UpsampleAlignPlugin : public MakaDynamicPlugin<UpsampleAlignPlugin, UpsampleAlignMessage> {
private:
public:
  UpsampleAlignPlugin(UpsampleAlignMessage message) : MakaDynamicPlugin(message, "upsample_align") {}

  nvinfer1::DimsExprs getOutputDimensions(int outputIndex, const nvinfer1::DimsExprs* inputs, int nbInputs,
                                          nvinfer1::IExprBuilder& exprBuilder) noexcept override {
    nvinfer1::DimsExprs output;
    output.nbDims = 4;

    output.d[0] = inputs[2].d[0];
    output.d[1] = inputs[2].d[1];
    output.d[2] = exprBuilder.constant(message.crop_size() * 2);
    output.d[3] = exprBuilder.constant(message.crop_size() * 2);

    return output;
  }

  bool supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) noexcept override {
    const PluginTensorDesc& in1 = inOut[0];
    if (pos == 0) {
      return (in1.type == nvinfer1::DataType::kFLOAT) && (in1.format == nvinfer1::TensorFormat::kLINEAR);
    }

    const PluginTensorDesc& in2 = inOut[1];
    if (pos == 1) {
      return (in1.type == in2.type) && (in2.format == nvinfer1::TensorFormat::kLINEAR);
    }

    const PluginTensorDesc& in3 = inOut[2];
    if (pos == 2) {
      return (in3.type == nvinfer1::DataType::kFLOAT && in3.format == nvinfer1::TensorFormat::kLINEAR);
    }

    const PluginTensorDesc& in4 = inOut[3];
    if (pos == 3) {
      return (in4.type == nvinfer1::DataType::kFLOAT && in4.format == nvinfer1::TensorFormat::kLINEAR);
    }

    // pos == 4, accessing information about output tensor
    const PluginTensorDesc& out = inOut[4];

    return (in3.type == out.type) && (in3.format == out.format);
  }

  void configurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int nbInputs,
                       const nvinfer1::DynamicPluginTensorDesc* out, int nbOutputs) noexcept override {}

  size_t getWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int nbInputs,
                          const nvinfer1::PluginTensorDesc* outputs, int nbOutputs) const noexcept override {
    return 0;
  }

  int enqueue(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
              const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream) noexcept override {
    if (inputDesc[2].type == nvinfer1::DataType::kFLOAT) {
      return upsample_align<float>(inputDesc, outputDesc, inputs, outputs, workspace, stream, message.padding(),
                                   message.post_padding(), message.crop_size());
    } else if (inputDesc[2].type == nvinfer1::DataType::kHALF) {
      return upsample_align<half>(inputDesc, outputDesc, inputs, outputs, workspace, stream, message.padding(),
                                  message.post_padding(), message.crop_size());
    } else {
      // Unsupported
      return 1;
    }
  }
};

class UpsampleAlignPluginCreator : public MakaDynamicPluginCreator<UpsampleAlignPlugin, UpsampleAlignMessage> {
public:
  UpsampleAlignPluginCreator() : MakaDynamicPluginCreator("upsample_align") {}
};

REGISTER_TENSORRT_PLUGIN(UpsampleAlignPluginCreator);

} // namespace trt_extensions

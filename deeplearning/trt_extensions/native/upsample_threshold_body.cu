#include <NvInfer.h>
#include <glm/glm.hpp>
#include <thrust/extrema.h>

#include "allocator.h"
#include "crop_utils.cuh"

namespace trt_extensions {

template <typename T>
__global__ void SetMaxKernel(T* output_ptr, glm::ivec4 max_position, glm::ivec4 output_dims, float* max_iter,
                             float min_threshold) {
  const int x = blockIdx.x * blockDim.x + threadIdx.x;
  const int y = blockIdx.y * blockDim.y + threadIdx.y;
  const int z = blockIdx.z * blockDim.z + threadIdx.z;

  glm::ivec4 out_pos(x, y, z, 0);

  if (out_pos.x < 0 || out_pos.x >= output_dims.x || //
      out_pos.y < 0 || out_pos.y >= output_dims.y || //
      out_pos.z < 0 || out_pos.z >= output_dims.z) {
    return;
  }

  int64_t out_index = Sub2Ind(out_pos, output_dims);
  if (out_pos.x == max_position.x && out_pos.y == max_position.y && *max_iter >= min_threshold) {
    output_ptr[out_index] = 1.0f;
  } else {
    output_ptr[out_index] = 0.0f;
  }
}

template <typename T>
int upsample_threshold(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
                       const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream,
                       float min_threshold) {
  ThrustTRTAllocator allocator;
  float* const input_ptr = (float*)inputs[0];
  T* const output_ptr = (T*)outputs[0];

  for (int i = 0; i < inputDesc[0].dims.d[0]; i++) {
    glm::ivec4 input_dims = ToGLM(inputDesc[0].dims);
    glm::ivec4 output_dims = ToGLM(outputDesc[0].dims);

    input_dims.w = 1;
    output_dims.w = 1;

    const int64_t input_num_el = NumElements(input_dims);
    const int64_t output_ptr_num_el = NumElements(output_dims);

    float* const input_i_ptr = input_ptr + i * input_num_el;
    T* const output_i_ptr = output_ptr + i * output_ptr_num_el;

    float* const max_iter =
        thrust::max_element(thrust::cuda::par(allocator).on(stream), input_i_ptr, input_i_ptr + input_num_el);

    int64_t max_offset = max_iter - input_i_ptr;
    glm::ivec4 max_position = Ind2Sub(max_offset, input_dims);
    // Handle scaling like align_corners=True. See
    // https://github.com/pytorch/pytorch/blob/59d71b9664b57b0ea0de0d87cea87b21daa4dd7b/aten/src/THNN/generic/upsampling.h#L32
    max_position.y = std::round(((float)max_position.y / (input_dims.y - 1)) * (output_dims.y - 1));
    max_position.x = std::round(((float)max_position.x / (input_dims.x - 1)) * (output_dims.x - 1));

    dim3 block_dims(16, 16, output_dims.z);
    dim3 grid_dims(iDivUp(output_dims.x, block_dims.x), iDivUp(output_dims.y, block_dims.y));
    SetMaxKernel<<<grid_dims, block_dims, 0, stream>>>(output_i_ptr, max_position, output_dims, max_iter,
                                                       min_threshold);
  }

  return 0;
}

template int upsample_threshold<float>(const nvinfer1::PluginTensorDesc* inputDesc,
                                       const nvinfer1::PluginTensorDesc* outputDesc, const void* const* inputs,
                                       void* const* outputs, void* workspace, cudaStream_t stream, float min_threshold);

template int upsample_threshold<half>(const nvinfer1::PluginTensorDesc* inputDesc,
                                      const nvinfer1::PluginTensorDesc* outputDesc, const void* const* inputs,
                                      void* const* outputs, void* workspace, cudaStream_t stream, float min_threshold);

} // namespace trt_extensions
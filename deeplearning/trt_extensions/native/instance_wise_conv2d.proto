syntax = "proto3";

import "deeplearning/trt_extensions/native/common.proto";

package trt_extensions;

message InstanceWiseConv2DMessage {
  int64 padding_width = 1;
  int64 padding_height = 2;
  int64 stride_width = 3;
  int64 stride_height = 4;

  // below params are configured by TRT and not set by user
  DataTypeMessage dtype = 5;
  DataFormatMessage format = 6;
  repeated int64 input_size = 7;
  repeated int64 output_size = 8;
  float input_scale = 9;
  float output_scale = 10;
}
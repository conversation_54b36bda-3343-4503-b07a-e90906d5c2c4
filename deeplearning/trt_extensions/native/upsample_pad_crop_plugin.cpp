#include <NvInfer.h>
#include <cublas_v2.h>
#include <cuda.h>
#include <cudnn.h>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "deeplearning/trt_extensions/native/upsample_pad_crop.pb.h"
#include "maka_plugin_base_dynamic.h"

using namespace nvinfer1;

namespace trt_extensions {

template <typename T>
int upsample_pad_crop(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
                      const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream,
                      int padding);

class UpsamplePadCropPlugin : public MakaDynamicPlugin<UpsamplePadCropPlugin, UpsamplePadCropMessage> {
private:
public:
  UpsamplePadCropPlugin(UpsamplePadCropMessage message) : MakaDynamicPlugin(message, "upsample_pad_crop") {}

  nvinfer1::DimsExprs getOutputDimensions(int outputIndex, const nvinfer1::DimsExprs* inputs, int nbInputs,
                                          nvinfer1::IExprBuilder& exprBuilder) noexcept override {
    nvinfer1::DimsExprs output;
    output.nbDims = 4;

    output.d[0] = inputs[1].d[0];
    output.d[1] = inputs[1].d[1];
    output.d[2] = exprBuilder.constant(message.crop_size());
    output.d[3] = exprBuilder.constant(message.crop_size());

    return output;
  }

  bool supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) noexcept override {
    // TRTORCH_ASSERT(0 <= pos && pos <= 2, "There should be exactly 3 connections to the plugin - 2 input, 1 output");
    // TRTORCH_ASSERT(nbInputs == 2, "Expected a two tensors as input to conv2d plugin");
    // TRTORCH_ASSERT(nbOutputs == 1, "Expected a single tensor as output to conv2d plugin");

    const PluginTensorDesc& in1 = inOut[0];

    const PluginTensorDesc& in2 = inOut[1];

    if (pos == 0) {
      return (in1.type == nvinfer1::DataType::kFLOAT) && (in1.format == nvinfer1::TensorFormat::kLINEAR);
    }

    if (pos == 1) {
      return (in2.type == nvinfer1::DataType::kFLOAT) && (in2.format == nvinfer1::TensorFormat::kLINEAR);
    }

    // pos == 2, accessing information about output tensor
    const PluginTensorDesc& out = inOut[2];

    return (in1.type == out.type) && (in1.format == out.format);
  }

  void configurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int nbInputs,
                       const nvinfer1::DynamicPluginTensorDesc* out, int nbOutputs) noexcept override {}

  size_t getWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int nbInputs,
                          const nvinfer1::PluginTensorDesc* outputs, int nbOutputs) const noexcept override {
    return 0;
  }

  int enqueue(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
              const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream) noexcept override {
    if (inputDesc[1].type == nvinfer1::DataType::kFLOAT) {
      return upsample_pad_crop<float>(inputDesc, outputDesc, inputs, outputs, workspace, stream, message.padding());
    } else if (inputDesc[1].type == nvinfer1::DataType::kHALF) {
      return upsample_pad_crop<half>(inputDesc, outputDesc, inputs, outputs, workspace, stream, message.padding());
    } else {
      // Unsupported
      return 1;
    }
  }
};

class UpsamplePadCropPluginCreator : public MakaDynamicPluginCreator<UpsamplePadCropPlugin, UpsamplePadCropMessage> {
public:
  UpsamplePadCropPluginCreator() : MakaDynamicPluginCreator("upsample_pad_crop") {}
};

REGISTER_TENSORRT_PLUGIN(UpsamplePadCropPluginCreator);

} // namespace trt_extensions

#include <NvInfer.h>
#include <glm/glm.hpp>

namespace trt_extensions {

inline glm::ivec4 ToGLM(nvinfer1::Dims dims) { return glm::ivec4(dims.d[3], dims.d[2], dims.d[1], dims.d[0]); }

inline int iDivUp(int x, int y) { return (x + y - 1) / y; }

template <int N> __host__ __device__ inline int64_t NumElements(glm::vec<N, int> dims) {
  int64_t num_el = dims[0];
  for (int i = 1; i < N; i++) {
    num_el *= dims[i];
  }
  return num_el;
}

template <int N> __host__ __device__ inline glm::vec<N, int> Ind2Sub(int64_t index, glm::vec<N, int> dims) {
  int64_t num_el = NumElements(dims);
  glm::vec<N, int> subscripts;
  for (int i = N - 1; i > 0; i--) {
    num_el /= dims[i];
    subscripts[i] = index / num_el;
    index = index % num_el;
  }
  subscripts[0] = index;
  return subscripts;
}

template <int N> __host__ __device__ inline int64_t Sub2Ind(glm::vec<N, int> pos, glm::vec<N, int> dims) {
  int64_t num_el = NumElements(dims);
  int index = 0;
  for (int i = N - 1; i >= 0; i--) {
    num_el /= dims[i];
    index += pos[i] * num_el;
  }
  return index;
}

} // namespace trt_extensions
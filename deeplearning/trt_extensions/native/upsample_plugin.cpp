#include <NvInfer.h>
#include <c10/cuda/CUDAGuard.h>
#include <cuda.h>
#include <cuda_runtime.h>

#include "deeplearning/trt_extensions/native/upsample.pb.h"
#include "lib/common/cpp/resize_util.h"
#include "maka_plugin_base.h"

using namespace nvinfer1;

namespace trt_extensions {

class UpsamplePlugin : public MakaPlugin<UpsamplePlugin, UpsampleMessage> {
public:
  UpsamplePlugin(UpsampleMessage message) : MakaPlugin(message, "upsample") {}

  Dims getOutputDimensions(int index, const Dims* inputs, int nbInputDims) noexcept override {
    Dims dims;
    dims.nbDims = inputs->nbDims;

    dims.d[0] = inputs->d[0];
    dims.d[1] = message.height();
    dims.d[2] = message.width();

    return dims;
  }

  bool supportsFormatCombination(int pos, const PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) const noexcept override {
    if (inOut[pos].type != DataType::kFLOAT && inOut[pos].type != DataType::kINT8) {
      // TensorRT segfaults with kHALF, so limit ourselves to FP32 and INT8 for now.
      return false;
    }
    if (inOut[pos].format != PluginFormat::kLINEAR && inOut[pos].format != PluginFormat::kCHW32) {
      // These two formats give us good enough performance.
      return false;
    }
    if (inOut[pos].format != inOut[0].format) {
      // All formats have to match.
      return false;
    }
    if (inOut[pos].type != inOut[0].type) {
      // All types have to match.
      return false;
    }
    return true;
  }

  torch::Dtype getDataType() const {
    switch (message.dtype()) {
    case DataTypeMessage::kFloat:
      return torch::kF32;
      break;

    case DataTypeMessage::kHalf:
      return torch::kF16;
      break;

    case DataTypeMessage::kInt8:
      return torch::kInt8;
      break;

    case DataTypeMessage::kInt32:
      return torch::kInt32;
      break;

    default:
      return torch::kF32;
      break;
    }
  }

  int enqueue(int batchSize, const void* const* inputs, void* const* outputs, void* workspace,
              cudaStream_t stream) noexcept override {
    float* input_ptr = (float*)inputs[0];
    float* output_ptr = (float*)outputs[0];

    // Compute width & height scales
    float width_scale = float(message.output_size(2)) / float(message.input_size(2));
    float height_scale = float(message.output_size(1)) / float(message.input_size(1));

    std::vector<int64_t> input_dims(message.input_size_size() + 1);
    input_dims[0] = batchSize;
    for (int i = 0; i < message.input_size_size(); i++) {
      input_dims[i + 1] = message.input_size(i);
    }

    std::vector<int64_t> output_dims(message.output_size_size() + 1);
    output_dims[0] = batchSize;
    for (int i = 0; i < message.output_size_size(); i++) {
      output_dims[i + 1] = message.output_size(i);
    }

    int device;
    cudaGetDevice(&device);
    auto torch_stream = c10::cuda::getStreamFromExternal(stream, device);

    c10::cuda::CUDAStreamGuard stream_guard(torch_stream);

    auto input =
        torch::from_blob(input_ptr, input_dims, torch::TensorOptions().device(torch::kCUDA).dtype(getDataType()))
            .to(torch::kF32);
    auto output =
        torch::from_blob(output_ptr, output_dims, torch::TensorOptions().device(torch::kCUDA).dtype(getDataType()));

    size_t inner_chan, outer_chan;
    switch (message.format()) {
    case DataFormatMessage::kLINEAR:
      inner_chan = 1;
      outer_chan = message.input_size(0);
      break;
    case DataFormatMessage::kCHW32:
      // See docs for PluginFormat enum.
      inner_chan = 32;
      outer_chan = (message.input_size(0) + 31) / 32;
      break;
    default:
      std::cerr << "Unknown format in UpsamplePlugin::enqueue" << std::endl;
      return 1;
    }

    auto interm_output = lib::common::interpolate(
        input, glm::ivec2(output_dims[output_dims.size() - 1], output_dims[output_dims.size() - 2]));

    output.copy_(interm_output);

    return 0;
  }
};

class UpsamplePluginCreator : public MakaPluginCreator<UpsamplePlugin, UpsampleMessage> {
public:
  UpsamplePluginCreator() : MakaPluginCreator("upsample") {}
};

REGISTER_TENSORRT_PLUGIN(UpsamplePluginCreator);

} // namespace trt_extensions

#ifndef TRT_EXTENSIONS_CONV2D_PLUGIN_H
#define TRT_EXTENSIONS_CONV2D_PLUGIN_H

#include <NvInfer.h>
#include <cuda.h>
#include <cudnn.h>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "deeplearning/trt_extensions/native/instance_wise_conv2d.pb.h"
#include "deeplearning/trt_extensions/native/maka_plugin_base_dynamic.h"

using namespace nvinfer1;

namespace trt_extensions {

class InstanceWiseConv2DPlugin : public MakaDynamicPlugin<InstanceWiseConv2DPlugin, InstanceWiseConv2DMessage> {
private:
  cudnnHandle_t cudnn_ = nullptr;

  // Mutable allows us to reuse allocated descriptors (even in const functions)
  mutable cudnnTensorDescriptor_t input_descriptor_ = nullptr;
  mutable cudnnFilterDescriptor_t kernel_descriptor_ = nullptr;
  mutable cudnnConvolutionDescriptor_t convolution_descriptor_ = nullptr;
  mutable cudnnTensorDescriptor_t output_descriptor_ = nullptr;

  // Specify an algorithm rather than asking cudnn for the best one to avoid choosing algorithm in enqueue
  static const cudnnConvolutionFwdAlgo_t kConvolutionAlgorithm = CUDNN_CONVOLUTION_FWD_ALGO_FFT;

  static const nvinfer1::IDimensionExpr* GetConvolutionOutputDimension(nvinfer1::IExprBuilder& exprBuilder,
                                                                       const nvinfer1::IDimensionExpr& input_size,
                                                                       const nvinfer1::IDimensionExpr& kernel_size,
                                                                       int padding, int stride);

  static cudnnDataType_t TRTTypeToCUDNNType(nvinfer1::DataType trt_type);

  bool SetDescriptors(const nvinfer1::PluginTensorDesc* inputs,
                      const nvinfer1::PluginTensorDesc* outputs) const noexcept;

public:
  InstanceWiseConv2DPlugin(InstanceWiseConv2DMessage message);

  ~InstanceWiseConv2DPlugin();

  nvinfer1::DimsExprs getOutputDimensions(int outputIndex, const nvinfer1::DimsExprs* inputs, int nbInputs,
                                          nvinfer1::IExprBuilder& exprBuilder) noexcept override;

  bool supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) noexcept override;

  void configurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int nbInputs,
                       const nvinfer1::DynamicPluginTensorDesc* out, int nbOutputs) noexcept override;

  size_t getWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int nbInputs,
                          const nvinfer1::PluginTensorDesc* outputs, int nbOutputs) const noexcept override;

  virtual void destroy() noexcept override;

  int enqueue(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
              const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream) noexcept override;
};

class InstanceWiseConv2DPluginCreator
    : public MakaDynamicPluginCreator<InstanceWiseConv2DPlugin, InstanceWiseConv2DMessage> {
public:
  InstanceWiseConv2DPluginCreator() : MakaDynamicPluginCreator("instance_wise_conv2d") {}
};

} // namespace trt_extensions

#endif // TRT_EXTENSIONS_CONV2D_PLUGIN_H
syntax = "proto3";

import "deeplearning/trt_extensions/native/common.proto";

package trt_extensions;

message UpsampleThresholdMessage {
  int64 crop_width = 1;
  int64 crop_height = 2;
  float min_threshold = 3;

  // below params are configured by TRT and not set by user
  DataTypeMessage dtype = 4;
  DataFormatMessage format = 5;
  repeated int64 input_size = 6;
  repeated int64 output_size = 7;
  float input_scale = 8;
  float output_scale = 9;
}
#include "crop_kernel.h"
#include "crop_utils.cuh"
#include <cuda_fp16.h>

namespace trt_extensions {

template <typename T>
__global__ void CropKernel(T* input_ptr, T* output_ptr, glm::ivec4 input_position, glm::ivec4 input_dims,
                           glm::ivec4 output_dims) {
  const int x = blockIdx.x * blockDim.x + threadIdx.x;
  const int y = blockIdx.y * blockDim.y + threadIdx.y;
  const int z = blockIdx.z * blockDim.z + threadIdx.z;

  glm::ivec4 out_pos(x, y, z, 0);

  if (out_pos.x < 0 || out_pos.x >= output_dims.x || out_pos.y < 0 || out_pos.y >= output_dims.y || out_pos.z < 0 ||
      out_pos.z >= output_dims.z) {
    return;
  }

  glm::ivec4 in_pos = out_pos;
  in_pos.y += input_position.y;
  in_pos.x += input_position.x;

  int64_t out_index = Sub2Ind(out_pos, output_dims);
  if (0 <= in_pos.y && in_pos.y < input_dims.y && 0 <= in_pos.x && in_pos.x < input_dims.x) {
    int64_t in_index = Sub2Ind(in_pos, input_dims);
    output_ptr[out_index] = input_ptr[in_index];
  } else {
    output_ptr[out_index] = 0.0f;
  }
}

template __global__ void CropKernel(float* input_ptr, float* output_ptr, glm::ivec4 input_position,
                                    glm::ivec4 input_dims, glm::ivec4 output_dim);

template __global__ void CropKernel(half* input_ptr, half* output_ptr, glm::ivec4 input_position, glm::ivec4 input_dims,
                                    glm::ivec4 output_dim);

} // namespace trt_extensions
#ifndef TRT_EXTENSIONS_MAKA_DYNAMIC_PLUGIN_BASE_H
#define TRT_EXTENSIONS_MAKA_DYNAMIC_PLUGIN_BASE_H 1

#include <NvInfer.h>

#include "deeplearning/trt_extensions/native/common.pb.h"

using namespace nvinfer1;

namespace trt_extensions {

template <typename PLUGIN_TYPE, typename MESSAGE_TYPE> class MakaDynamicPlugin : public IPluginV2DynamicExt {
protected:
  MESSAGE_TYPE message;
  const std::string plugin_name;

public:
  MakaDynamicPlugin(MESSAGE_TYPE message, const std::string& plugin_name)
      : message(message), plugin_name(plugin_name) {}

  const char* getPluginType() const noexcept override { return plugin_name.c_str(); };

  const char* getPluginVersion() const noexcept override { return "1"; }

  int getNbOutputs() const noexcept override { return 1; }

  DataType getOutputDataType(int index, const nvinfer1::DataType* inputTypes, int nbInputs) const noexcept {
    return inputTypes[0];
  }

  int initialize() noexcept override { return 0; }

  void terminate() noexcept override {}

  size_t getSerializationSize() const noexcept override { return message.SerializeAsString().size(); }

  void serialize(void* buffer) const noexcept override { message.SerializeToArray(buffer, getSerializationSize()); }

  virtual void destroy() noexcept override {}

  void setPluginNamespace(const char* pluginNamespace) noexcept override {}

  const char* getPluginNamespace() const noexcept override { return "maka_trt_extensions"; }

  IPluginV2DynamicExt* clone() const noexcept override { return new PLUGIN_TYPE(message); }
};

template <typename PLUGIN_TYPE, typename MESSAGE_TYPE> class MakaDynamicPluginCreator : public IPluginCreator {
protected:
  const std::string plugin_name;

public:
  MakaDynamicPluginCreator(const std::string& plugin_name) : plugin_name(plugin_name) {}

  const char* getPluginNamespace() const noexcept override { return "maka_trt_extensions"; }

  const char* getPluginName() const noexcept override { return plugin_name.c_str(); }

  const char* getPluginVersion() const noexcept override { return "1"; }

  IPluginV2* deserializePlugin(const char* name, const void* data, size_t length) noexcept override {
    MESSAGE_TYPE message;
    message.ParseFromArray(data, length);
    return new PLUGIN_TYPE(message);
  }

  void setPluginNamespace(const char* N) noexcept override {}
  const PluginFieldCollection* getFieldNames() noexcept override { return nullptr; }

  IPluginV2* createPlugin(const char* name, const PluginFieldCollection* fc) noexcept override { return nullptr; }
};

} // namespace trt_extensions

#endif
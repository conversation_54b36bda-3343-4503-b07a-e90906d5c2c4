#include "instance_wise_conv2d_plugin.h"

#include <cuda_fp16.h>
#include <cudnn.h>

using namespace nvinfer1;

namespace trt_extensions {

/*
 * InstanceWiseConv2DPlugin class implementations
 */

#define checkCUDNN(expression, error_return_code)                                                                      \
  {                                                                                                                    \
    cudnnStatus_t status = (expression);                                                                               \
    if (status != CUDNN_STATUS_SUCCESS) {                                                                              \
      std::cerr << "Error on line " << __LINE__ << ": " << cudnnGetErrorString(status) << std::endl;                   \
      return error_return_code;                                                                                        \
    }                                                                                                                  \
  }

InstanceWiseConv2DPlugin::InstanceWiseConv2DPlugin(InstanceWiseConv2DMessage message)
    : MakaDynamicPlugin(message, "instance_wise_conv2d") {
  checkCUDNN(cudnnCreate(&cudnn_), );
  checkCUDNN(cudnnCreateTensorDescriptor(&input_descriptor_), );
  checkCUDNN(cudnnCreateFilterDescriptor(&kernel_descriptor_), );
  checkCUDNN(cudnnCreateConvolutionDescriptor(&convolution_descriptor_), );
  checkCUDNN(cudnnCreateTensorDescriptor(&output_descriptor_), );
}

InstanceWiseConv2DPlugin::~InstanceWiseConv2DPlugin() { destroy(); }

void InstanceWiseConv2DPlugin::destroy() noexcept {
  if (cudnn_ != nullptr) {
    checkCUDNN(cudnnDestroy(cudnn_), );
    cudnn_ = nullptr;
  }
  if (input_descriptor_ != nullptr) {
    checkCUDNN(cudnnDestroyTensorDescriptor(input_descriptor_), );
    input_descriptor_ = nullptr;
  }
  if (kernel_descriptor_ != nullptr) {
    checkCUDNN(cudnnDestroyFilterDescriptor(kernel_descriptor_), );
    kernel_descriptor_ = nullptr;
  }
  if (convolution_descriptor_ != nullptr) {
    checkCUDNN(cudnnDestroyConvolutionDescriptor(convolution_descriptor_), );
    convolution_descriptor_ = nullptr;
  }
  if (output_descriptor_ != nullptr) {
    checkCUDNN(cudnnDestroyTensorDescriptor(output_descriptor_), );
    output_descriptor_ = nullptr;
  }
}

const nvinfer1::IDimensionExpr* InstanceWiseConv2DPlugin::GetConvolutionOutputDimension(
    nvinfer1::IExprBuilder& exprBuilder, const nvinfer1::IDimensionExpr& input_size,
    const nvinfer1::IDimensionExpr& kernel_size, int padding, int stride) {
  return exprBuilder.operation(
      nvinfer1::DimensionOperation::kSUM,
      *exprBuilder.operation(
          nvinfer1::DimensionOperation::kCEIL_DIV,
          *exprBuilder.operation(nvinfer1::DimensionOperation::kSUM,
                                 *exprBuilder.operation(nvinfer1::DimensionOperation::kSUB, input_size, kernel_size),
                                 *exprBuilder.constant(padding * 2)),
          *exprBuilder.constant(stride)),
      *exprBuilder.constant(1));
}

cudnnDataType_t InstanceWiseConv2DPlugin::TRTTypeToCUDNNType(nvinfer1::DataType trt_type) {
  switch (trt_type) {
  case nvinfer1::DataType::kFLOAT:
    return CUDNN_DATA_FLOAT;
    break;

  case nvinfer1::DataType::kHALF:
    return CUDNN_DATA_HALF;
    break;

  case nvinfer1::DataType::kINT8:
    return CUDNN_DATA_INT8;
    break;

  case nvinfer1::DataType::kINT32:
    return CUDNN_DATA_INT32;
    break;

  default:
    return CUDNN_DATA_FLOAT;
    break;
  }
}

nvinfer1::DimsExprs InstanceWiseConv2DPlugin::getOutputDimensions(int outputIndex, const nvinfer1::DimsExprs* inputs,
                                                                  int nbInputs,
                                                                  nvinfer1::IExprBuilder& exprBuilder) noexcept {
  nvinfer1::DimsExprs output;
  output.nbDims = 4;

  output.d[0] = inputs[0].d[0];
  output.d[1] = exprBuilder.constant(1);
  output.d[2] = GetConvolutionOutputDimension(exprBuilder, *inputs[0].d[2], *inputs[1].d[2], message.padding_height(),
                                              message.stride_height());
  output.d[3] = GetConvolutionOutputDimension(exprBuilder, *inputs[0].d[3], *inputs[1].d[3], message.padding_width(),
                                              message.stride_width());

  return output;
}

bool InstanceWiseConv2DPlugin::supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                                         int nbOutputs) noexcept {
  const PluginTensorDesc& in1 = inOut[0];
  if (pos == 0) {
    return in1.type == nvinfer1::DataType::kFLOAT && in1.format == nvinfer1::TensorFormat::kLINEAR;
  }

  const PluginTensorDesc& in2 = inOut[1];
  if (pos == 1) {
    return (in2.type == in1.type) && in2.format == nvinfer1::TensorFormat::kLINEAR;
  }

  // pos == 2, accessing information about output tensor
  const PluginTensorDesc& out = inOut[2];

  return (out.type == in1.type) && (in1.format == out.format);
}

void InstanceWiseConv2DPlugin::configurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int nbInputs,
                                               const nvinfer1::DynamicPluginTensorDesc* out, int nbOutputs) noexcept {}

bool InstanceWiseConv2DPlugin::SetDescriptors(const nvinfer1::PluginTensorDesc* inputs,
                                              const nvinfer1::PluginTensorDesc* outputs) const noexcept {
  checkCUDNN(cudnnSetTensor4dDescriptor(input_descriptor_,
                                        /*format=*/CUDNN_TENSOR_NCHW,
                                        /*dataType=*/TRTTypeToCUDNNType(inputs[0].type),
                                        /*batch_size=*/1,
                                        /*channels=*/inputs[0].dims.d[1],
                                        /*image_height=*/inputs[0].dims.d[2],
                                        /*image_width=*/inputs[0].dims.d[3]),
             false);

  checkCUDNN(cudnnSetFilter4dDescriptor(kernel_descriptor_,
                                        /*dataType=*/TRTTypeToCUDNNType(inputs[1].type),
                                        /*format=*/CUDNN_TENSOR_NCHW,
                                        /*out_channels=*/1,
                                        /*in_channels=*/inputs[0].dims.d[1],
                                        /*kernel_height=*/inputs[1].dims.d[2],
                                        /*kernel_width=*/inputs[1].dims.d[3]),
             false);

  checkCUDNN(cudnnSetConvolution2dDescriptor(convolution_descriptor_,
                                             /*pad_height=*/message.padding_height(),
                                             /*pad_width=*/message.padding_width(),
                                             /*vertical_stride=*/message.stride_height(),
                                             /*horizontal_stride=*/message.stride_width(),
                                             /*dilation_height=*/1,
                                             /*dilation_width=*/1,
                                             /*mode=*/CUDNN_CROSS_CORRELATION,
                                             /*computeType=*/CUDNN_DATA_FLOAT),
             false);

  checkCUDNN(cudnnSetTensor4dDescriptor(output_descriptor_,
                                        /*format=*/CUDNN_TENSOR_NCHW,
                                        /*dataType=*/TRTTypeToCUDNNType(outputs[0].type),
                                        /*batch_size=*/1,
                                        /*channels=*/1,
                                        /*image_height=*/outputs[0].dims.d[2],
                                        /*image_width=*/outputs[0].dims.d[3]),
             false);

  return true;
}

size_t InstanceWiseConv2DPlugin::getWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int nbInputs,
                                                  const nvinfer1::PluginTensorDesc* outputs,
                                                  int nbOutputs) const noexcept {

  if (!SetDescriptors(inputs, outputs))
    return 0;

  size_t workspace_bytes;
  checkCUDNN(cudnnGetConvolutionForwardWorkspaceSize(cudnn_, input_descriptor_, kernel_descriptor_,
                                                     convolution_descriptor_, output_descriptor_, kConvolutionAlgorithm,
                                                     &workspace_bytes),
             0);

  return workspace_bytes;
}

int InstanceWiseConv2DPlugin::enqueue(const nvinfer1::PluginTensorDesc* inputDesc,
                                      const nvinfer1::PluginTensorDesc* outputDesc, const void* const* inputs,
                                      void* const* outputs, void* workspace, cudaStream_t stream) noexcept {
  float* input_ptr = (float*)inputs[0];
  float* weights_ptr = (float*)inputs[1];
  float* output_ptr = (float*)outputs[0];

  if (!SetDescriptors(inputDesc, outputDesc))
    return 1;

  cudnnSetStream(cudnn_, stream);

  /*
    Uncomment to check most performant algorithm. It is currently hard-coded since algorithm search is slow.
    int num_algos = 0;
    cudnnConvolutionFwdAlgoPerf_t algorithm_perf_results;
    checkCUDNN(cudnnFindConvolutionForwardAlgorithm(cudnn_, input_descriptor_, kernel_descriptor_,
                                                    convolution_descriptor_, output_descriptor_, 1, &num_algos,
                                                    &algorithm_perf_results),
               1);

    std::cout << "Top algorithm " << algorithm_perf_results.algo << std::endl;
  */

  size_t workspace_bytes;
  checkCUDNN(cudnnGetConvolutionForwardWorkspaceSize(cudnn_, input_descriptor_, kernel_descriptor_,
                                                     convolution_descriptor_, output_descriptor_, kConvolutionAlgorithm,
                                                     &workspace_bytes),
             1);

  for (int i = 0; i < inputDesc[0].dims.d[0]; i++) {
    float* input_i_ptr = input_ptr + i * (inputDesc[0].dims.d[1] * inputDesc[0].dims.d[2] * inputDesc[0].dims.d[3]);
    float* weights_i_ptr = weights_ptr + i * (inputDesc[1].dims.d[1] * inputDesc[1].dims.d[2] * inputDesc[1].dims.d[3]);
    float* output_i_ptr =
        output_ptr + i * (outputDesc[0].dims.d[1] * outputDesc[0].dims.d[2] * outputDesc[0].dims.d[3]);

    const float alpha = 1.0f, beta = 0.0f;
    checkCUDNN(cudnnConvolutionForward(cudnn_, &alpha, input_descriptor_, input_i_ptr, kernel_descriptor_,
                                       weights_i_ptr, convolution_descriptor_, kConvolutionAlgorithm, workspace,
                                       workspace_bytes, &beta, output_descriptor_, output_i_ptr),
               1);
  }

  return 0;
}

REGISTER_TENSORRT_PLUGIN(InstanceWiseConv2DPluginCreator);

} // namespace trt_extensions
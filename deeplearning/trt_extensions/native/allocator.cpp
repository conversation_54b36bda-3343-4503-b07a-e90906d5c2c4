#include "c10/cuda/CUDACachingAllocator.h"

#include "allocator.h"
#include "lib/common/cpp/cuda_util.h"

namespace trt_extensions {

ThrustTRTAllocator::ThrustTRTAllocator() {
  int device_count;
  CUDA_ERROR_CHECK(cudaGetDeviceCount(&device_count));
  // Verify allocator is initialized. No-op if already initialized.
  c10::cuda::CUDACachingAllocator::init(device_count);
}

char* ThrustTRTAllocator::allocate(std::ptrdiff_t num_bytes) {
  return (char*)c10::cuda::CUDACachingAllocator::raw_alloc(num_bytes);
}

void ThrustTRTAllocator::deallocate(char* ptr, size_t) { c10::cuda::CUDACachingAllocator::raw_delete(ptr); }

} // namespace trt_extensions

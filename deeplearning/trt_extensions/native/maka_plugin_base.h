#ifndef TRT_EXTENSIONS_MAKA_PLUGIN_BASE_H
#define TRT_EXTENSIONS_MAKA_PLUGIN_BASE_H 1

#include <NvInfer.h>

#include "deeplearning/trt_extensions/native/common.pb.h"

using namespace nvinfer1;

namespace trt_extensions {

template <typename PLUGIN_TYPE, typename MESSAGE_TYPE> class MakaPlugin : public IPluginV2IOExt {
protected:
  MESSAGE_TYPE message;
  const std::string plugin_name;

public:
  MakaPlugin(MESSAGE_TYPE message, const std::string& plugin_name) : message(message), plugin_name(plugin_name) {}

  const char* getPluginType() const noexcept override { return plugin_name.c_str(); };

  const char* getPluginVersion() const noexcept override { return "1"; }

  int getNbOutputs() const noexcept override { return 1; }

  DataType getOutputDataType(int index, const nvinfer1::DataType* inputTypes, int nbInputs) const noexcept {
    return inputTypes[0];
  }

  bool isOutputBroadcastAcrossBatch(int outputIndex, const bool* inputIsBroadcasted, int nbInputs) const noexcept {
    return false;
  }

  bool canBroadcastInputAcrossBatch(int inputIndex) const noexcept { return false; }

  void configurePlugin(const PluginTensorDesc* in, int nbInput, const PluginTensorDesc* out,
                       int nbOutput) noexcept override {
    // set data type
    switch (in[0].type) {
    case DataType::kFLOAT:
      message.set_dtype(DataTypeMessage::kFloat);
      break;
    case DataType::kHALF:
      message.set_dtype(DataTypeMessage::kHalf);
      break;
    case DataType::kINT32:
      message.set_dtype(DataTypeMessage::kInt32);
      break;
    case DataType::kINT8:
      message.set_dtype(DataTypeMessage::kInt8);
      break;
    default:
      return;
    }

    // set data format
    switch (in[0].format) {
    case PluginFormat::kLINEAR:
      message.set_format(DataFormatMessage::kLINEAR);
      break;
    case PluginFormat::kCHW2:
      message.set_format(DataFormatMessage::kCHW2);
      break;
    case PluginFormat::kHWC8:
      message.set_format(DataFormatMessage::kHWC8);
      break;
    case PluginFormat::kCHW4:
      message.set_format(DataFormatMessage::kCHW4);
      break;
    case PluginFormat::kCHW16:
      message.set_format(DataFormatMessage::kCHW16);
      break;
    case PluginFormat::kCHW32:
      message.set_format(DataFormatMessage::kCHW32);
      break;
    default:
      return;
    }

    // set input sizes
    for (int i = 0; i < in[0].dims.nbDims; i++) {
      message.add_input_size(in[0].dims.d[i]);
    }

    // set output sizes
    for (int i = 0; i < out[0].dims.nbDims; i++) {
      message.add_output_size(out[0].dims.d[i]);
    }

    // scales
    message.set_input_scale(in[0].scale >= 0.0f ? in[0].scale : 1.0f);
    message.set_output_scale(out[0].scale >= 0.0f ? out[0].scale : 1.0f);
  }

  int initialize() noexcept override { return 0; }

  void terminate() noexcept override {}

  // Be sure to override this if plugin uses GPU memory other than input & output buffers.
  size_t getWorkspaceSize(int maxBatchSize) const noexcept override { return 0; }

  size_t getSerializationSize() const noexcept override { return message.SerializeAsString().size(); }

  void serialize(void* buffer) const noexcept override { message.SerializeToArray(buffer, getSerializationSize()); }

  void destroy() noexcept override {}

  void setPluginNamespace(const char* pluginNamespace) noexcept override {}

  const char* getPluginNamespace() const noexcept override { return "maka_trt_extensions"; }

  IPluginV2Ext* clone() const noexcept override { return new PLUGIN_TYPE(message); }
};

template <typename PLUGIN_TYPE, typename MESSAGE_TYPE> class MakaPluginCreator : public IPluginCreator {
protected:
  const std::string plugin_name;

public:
  MakaPluginCreator(const std::string& plugin_name) : plugin_name(plugin_name) {}

  const char* getPluginNamespace() const noexcept override { return "maka_trt_extensions"; }

  const char* getPluginName() const noexcept override { return plugin_name.c_str(); }

  const char* getPluginVersion() const noexcept override { return "1"; }

  IPluginV2* deserializePlugin(const char* name, const void* data, size_t length) noexcept override {
    MESSAGE_TYPE message;
    message.ParseFromArray(data, length);
    return new PLUGIN_TYPE(message);
  }

  void setPluginNamespace(const char* N) noexcept override {}
  const PluginFieldCollection* getFieldNames() noexcept override { return nullptr; }

  IPluginV2* createPlugin(const char* name, const PluginFieldCollection* fc) noexcept override { return nullptr; }
};

} // namespace trt_extensions

#endif
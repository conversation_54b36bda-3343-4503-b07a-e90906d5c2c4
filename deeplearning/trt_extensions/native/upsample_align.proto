syntax = "proto3";

import "deeplearning/trt_extensions/native/common.proto";

package trt_extensions;

message UpsampleAlignMessage {
  int64 padding = 1;
  int64 post_padding = 2;
  int64 crop_size = 3;

  // below params are configured by TRT and not set by user
  DataTypeMessage dtype = 4;
  DataFormatMessage format = 5;
  repeated int64 input_size = 6;
  repeated int64 output_size = 7;
  float input_scale = 8;
  float output_scale = 9;
}
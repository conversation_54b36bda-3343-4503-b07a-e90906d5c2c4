#include "sum_plugin.h"

#include <c10/cuda/CUDAGuard.h>
#include <cuda_fp16.h>

using namespace nvinfer1;

namespace trt_extensions {

/*
 * SumPlugin class implementations
 */

SumPlugin::SumPlugin(SumMessage message) : MakaPlugin(message, "sum") {}

SumPlugin::~SumPlugin() { destroy(); }

void SumPlugin::destroy() noexcept {}

torch::Dtype SumPlugin::getDataType() const {
  switch (message.dtype()) {
  case DataTypeMessage::kFloat:
    return torch::kF32;
    break;

  case DataTypeMessage::kHalf:
    return torch::kF16;
    break;

  case DataTypeMessage::kInt8:
    return torch::kInt8;
    break;

  case DataTypeMessage::kInt32:
    return torch::kInt32;
    break;

  default:
    return torch::kF32;
    break;
  }
}

nvinfer1::Dims SumPlugin::getOutputDimensions(int index, const nvinfer1::Dims* inputs, int nbInputDims) noexcept {
  nvinfer1::Dims output;
  output.nbDims = inputs[0].nbDims - 1;
  int dim = message.dim() - 1;

  for (int i = 0; i < dim; i++) {
    output.d[i] = inputs[0].d[i];
  }
  for (int i = dim + 1; i < inputs[0].nbDims; i++) {
    output.d[i - 1] = inputs[0].d[i];
  }

  return output;
}

bool SumPlugin::supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                          int nbOutputs) const noexcept {
  const PluginTensorDesc& in1 = inOut[0];
  if (pos == 0) {
    return in1.type == nvinfer1::DataType::kFLOAT && in1.format == nvinfer1::TensorFormat::kLINEAR;
  }

  // pos == 1, accessing information about output tensor
  const PluginTensorDesc& out = inOut[1];

  return (out.type == in1.type) && (in1.format == out.format);
}

size_t SumPlugin::getWorkspaceSize(int max_batch_size) const noexcept { return 0; }

int SumPlugin::enqueue(int batch_size, const void* const* inputs, void* const* outputs, void* workspace,
                       cudaStream_t stream) noexcept {
  float* input_ptr = (float*)inputs[0];
  float* output_ptr = (float*)outputs[0];

  if (message.input_size_size() == 0 || message.output_size_size() == 0) {
    return 0;
  }

  std::vector<int64_t> input_dims(message.input_size_size() + 1);
  input_dims[0] = batch_size;
  for (int i = 0; i < message.input_size_size(); i++) {
    input_dims[i + 1] = message.input_size(i);
  }

  std::vector<int64_t> output_dims(input_dims.size() - 1);
  output_dims[0] = batch_size;
  for (int i = 0; i < message.output_size_size(); i++) {
    output_dims[i + 1] = message.output_size(i);
  }

  int device;
  cudaGetDevice(&device);
  auto torch_stream = c10::cuda::getStreamFromExternal(stream, device);

  c10::cuda::CUDAStreamGuard stream_guard(torch_stream);

  auto input =
      torch::from_blob(input_ptr, input_dims, torch::TensorOptions().device(torch::kCUDA).dtype(getDataType()));
  auto output =
      torch::from_blob(output_ptr, output_dims, torch::TensorOptions().device(torch::kCUDA).dtype(getDataType()));

  torch::sum_out(output, input, message.dim());

  return 0;
}

REGISTER_TENSORRT_PLUGIN(SumPluginCreator);

} // namespace trt_extensions
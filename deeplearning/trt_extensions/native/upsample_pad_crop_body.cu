#include <NvInfer.h>
#include <glm/glm.hpp>
#include <thrust/extrema.h>

#include "allocator.h"
#include "crop_kernel.h"
#include "crop_utils.cuh"

namespace trt_extensions {

template <typename T>
int upsample_pad_crop(const nvinfer1::PluginTensorDesc* inputDesc, const nvinfer1::PluginTensorDesc* outputDesc,
                      const void* const* inputs, void* const* outputs, void* workspace, cudaStream_t stream,
                      int padding) {
  ThrustTRTAllocator allocator;
  float* const out_softmax1_ptr = (float*)inputs[0];
  T* const image_ptr = (T*)inputs[1];
  T* const output_ptr = (T*)outputs[0];

  for (int i = 0; i < inputDesc[0].dims.d[0]; i++) {
    glm::ivec4 out_softmax1_dims = ToGLM(inputDesc[0].dims);
    glm::ivec4 image_dims = ToGLM(inputDesc[1].dims);
    glm::ivec4 output_dims = ToGLM(outputDesc[0].dims);

    out_softmax1_dims.w = 1;
    image_dims.w = 1;
    output_dims.w = 1;

    const int64_t out_softmax1_num_el = NumElements(out_softmax1_dims);
    const int64_t output_ptr_num_el = NumElements(output_dims);

    float* const out_softmax1_i_ptr = out_softmax1_ptr + i * out_softmax1_num_el;
    T* const image_i_ptr = image_ptr + i * NumElements(image_dims);
    T* const output_i_ptr = output_ptr + i * output_ptr_num_el;

    float* const max_iter = thrust::max_element(thrust::cuda::par(allocator).on(stream), out_softmax1_i_ptr,
                                                out_softmax1_i_ptr + out_softmax1_num_el);

    const int64_t max_offset = max_iter - out_softmax1_i_ptr;
    glm::ivec4 image_position = Ind2Sub(max_offset, out_softmax1_dims);
    image_position.y = std::round(((float)image_position.y / (out_softmax1_dims.y - 1)) * (image_dims.y - 1));
    image_position.x = std::round(((float)image_position.x / (out_softmax1_dims.x - 1)) * (image_dims.x - 1));
    image_position.y -= padding;
    image_position.x -= padding;

    dim3 block_dims(16, 16, output_dims.z);
    dim3 grid_dims(iDivUp(output_dims.x, block_dims.x), iDivUp(output_dims.y, block_dims.y));
    CropKernel<<<grid_dims, block_dims, 0, stream>>>(image_i_ptr, output_i_ptr, image_position, image_dims,
                                                     output_dims);
  }

  return 0;
}

template int upsample_pad_crop<float>(const nvinfer1::PluginTensorDesc* inputDesc,
                                      const nvinfer1::PluginTensorDesc* outputDesc, const void* const* inputs,
                                      void* const* outputs, void* workspace, cudaStream_t stream, int padding);

template int upsample_pad_crop<half>(const nvinfer1::PluginTensorDesc* inputDesc,
                                     const nvinfer1::PluginTensorDesc* outputDesc, const void* const* inputs,
                                     void* const* outputs, void* workspace, cudaStream_t stream, int padding);

} // namespace trt_extensions
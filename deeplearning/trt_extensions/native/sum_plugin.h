#ifndef TRT_EXTENSIONS_CONV2D_PLUGIN_H
#define TRT_EXTENSIONS_CONV2D_PLUGIN_H

#include <NvInfer.h>
#include <cuda.h>
#include <cudnn.h>
#include <iostream>
#include <sstream>
#include <string>
#include <torch/torch.h>
#include <vector>

#include "deeplearning/trt_extensions/native/sum.pb.h"
#include "maka_plugin_base.h"

using namespace nvinfer1;

namespace trt_extensions {

class SumPlugin : public MakaPlugin<SumPlugin, SumMessage> {
private:
  torch::Dtype getDataType() const;

public:
  SumPlugin(SumMessage message);

  ~SumPlugin();

  nvinfer1::Dims getOutputDimensions(int index, const nvinfer1::Dims* inputs, int nbInputDims) noexcept override;

  bool supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) const noexcept override;

  size_t getWorkspaceSize(int max_batch_size) const noexcept override;

  virtual void destroy() noexcept override;

  int enqueue(int batch_size, const void* const* inputs, void* const* outputs, void* workspace,
              cudaStream_t stream) noexcept override;
};

class SumPluginCreator : public MakaPluginCreator<SumPlugin, SumMessage> {
public:
  SumPluginCreator() : MakaPluginCreator("sum") {}
};

} // namespace trt_extensions

#endif // TRT_EXTENSIONS_CONV2D_PLUGIN_H
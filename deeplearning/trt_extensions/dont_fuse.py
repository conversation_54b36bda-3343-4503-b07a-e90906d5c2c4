import torch
import torch.nn as nn
from torch2trt import ConversionContext

from deeplearning.trt_extensions.utils import maka_trt_converter

__all__ = ["TRTDontFuse"]


class TRTDontFuse(nn.Module):
    """This identity layer breaks layer fusion (on purpose)."""

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x


@maka_trt_converter("TRTDontFuse.forward")
def convert_TRTDontFuse(ctx: ConversionContext) -> None:
    input = ctx.method_args[1]
    output = ctx.method_return
    layer = ctx.network.add_identity(input._trt)
    output._trt = layer.get_output(0)

from typing import Any, List

import tensorrt as trt
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch2trt import ConversionContext
from torch2trt.torch2trt import trt_

from deeplearning.trt_extensions.utils import maka_trt_converter
from generated.deeplearning.trt_extensions.native.upsample_pb2 import UpsampleMessage

__all__ = ["Upsample"]


class Upsample(nn.Module):
    def forward(self, x: torch.Tensor, size: List[int]) -> torch.Tensor:
        # Type annotation is necessary for the proper TorchScript conversion
        result = F.interpolate(x, size=size, mode="bilinear", align_corners=False)
        # weirdness that I don't understand caused runtime errors when combining these two lines?
        # also using cast() fails with "RuntimeError: builtin cannot be used as a value:"
        # also using type: ignore fails at runtime
        # so we are excluding typing checks for this whole file for now...
        return result


def get_upsample_plugin(width: int, height: int) -> Any:
    PLUGIN_NAME = "upsample"
    registry = trt.get_plugin_registry()
    creator = [
        c for c in registry.plugin_creator_list if c.name == PLUGIN_NAME and c.plugin_namespace == "maka_trt_extensions"
    ][0]
    message = UpsampleMessage(width=width, height=height)
    return creator.deserialize_plugin(PLUGIN_NAME, message.SerializeToString())


@maka_trt_converter("Upsample.forward")
def convert_Upsample(ctx: ConversionContext) -> None:
    input = ctx.method_args[1]
    input_trt = trt_(ctx.network, input)
    output = ctx.method_return

    # currently only works for NCHW
    height, width = output.shape[2:]

    plugin = get_upsample_plugin(width, height)
    layer = ctx.network.add_plugin_v2([input_trt], plugin)
    output._trt = layer.get_output(0)

file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)

CompileProto(../proto/trt_model.proto MODEL_GENERATED_PATH LANGS python mypy cpp)
CompileProto(../proto/metadata.proto METADATA_GENERATED_PATH LANGS python mypy cpp)

add_library(model_io_proto ${MODEL_GENERATED_PATH}/trt_model.pb.cc ${METADATA_GENERATED_PATH}/metadata.pb.cc)
target_compile_options(model_io_proto PRIVATE "-w")

add_library(model_io SHARED ${SOURCES})
target_link_libraries(model_io PUBLIC protobuf trt_runtime model_io_proto exceptions)

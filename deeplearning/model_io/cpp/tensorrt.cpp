#include "deeplearning/model_io/cpp/tensorrt.h"

#include <ATen/cuda/CUDAContext.h>
#include <filesystem>
#include <fmt/format.h>
#include <fstream>

#include "generated/deeplearning/model_io/proto/trt_model.pb.h"
#include "lib/common/cpp/exceptions.h"

namespace deeplearning {
namespace model_io {

std::string resolve_model_file(std::string path, int gpu_id) {
  if (std::filesystem::is_directory(path)) {
    // Look up based on supported compute capabilities and TRT versions
    for (auto& trt_version : server::trt_runtime::TRTRuntime::SUPPORTED_TRT_VERSIONS) {
      int start_gpu, end_gpu;
      if (gpu_id >= 0) {
        start_gpu = gpu_id;
        end_gpu = gpu_id;
      } else {
        // Find model for any one of the GPUs
        start_gpu = 0;
        end_gpu = (int)torch::cuda::device_count() - 1;
      }
      for (int i = start_gpu; i <= end_gpu; i++) {
        auto properties = at::cuda::getDeviceProperties((int8_t)i);
        auto candidate_path =
            fmt::format("{}/sm{}.{}_trt{}.trt", path, properties->major, properties->minor, trt_version);
        if (std::filesystem::exists(candidate_path)) {
          return candidate_path;
        }
      }
    }

    throw maka_error(fmt::format("Unable to find suitable TRT model in {}", path));
  } else {
    return path;
  }
}

ModelMetadataProto load_trt_metadata(std::string path) {
  path = resolve_model_file(path, -1);

  std::ifstream model_file(path, std::ios_base::binary);
  if (!model_file.good()) {
    throw maka_error(fmt::format("Failed to load TRT model file {}", path));
  }

  TRTModelProto trt_model_proto;
  if (!trt_model_proto.ParseFromIstream(&model_file) || trt_model_proto.input_names_size() == 0 ||
      trt_model_proto.output_names_size() == 0 || !trt_model_proto.has_metadata() ||
      trt_model_proto.engine_bytes().empty()) {
    throw maka_error(fmt::format("Failed to load TRT model file {}", path));
  }

  auto input_names_field = trt_model_proto.input_names();
  auto output_names_field = trt_model_proto.output_names();

  return trt_model_proto.metadata();
}

std::tuple<std::shared_ptr<server::trt_runtime::TRTRuntime>, ModelMetadataProto> load_trt_model(std::string path,
                                                                                                int gpu_id) {
  path = resolve_model_file(path, gpu_id);

  std::ifstream model_file(path, std::ios_base::binary);
  if (!model_file.good()) {
    throw maka_error(fmt::format("Failed to load TRT model file {}", path));
  }

  TRTModelProto trt_model_proto;
  if (!trt_model_proto.ParseFromIstream(&model_file) || trt_model_proto.input_names_size() == 0 ||
      trt_model_proto.output_names_size() == 0 || !trt_model_proto.has_metadata() ||
      trt_model_proto.engine_bytes().empty()) {
    throw maka_error(fmt::format("Failed to load TRT model file {}", path));
  }

  auto input_names_field = trt_model_proto.input_names();
  auto output_names_field = trt_model_proto.output_names();

  ModelMetadataProto model_metadata = trt_model_proto.metadata();

  std::string version = trt_model_proto.tensorrt_version();
  if (version == "") {
    version = "8.0.1.6";
  }

  std::shared_ptr<server::trt_runtime::TRTRuntime> model_runtime(new server::trt_runtime::TRTRuntime(
      trt_model_proto.engine_bytes(), {input_names_field.begin(), input_names_field.end()},
      {output_names_field.begin(), output_names_field.end()}, version, gpu_id,
      trt_model_proto.implicit_batch_dimension()));

  return {model_runtime, model_metadata};
}

} // namespace model_io

} // namespace deeplearning

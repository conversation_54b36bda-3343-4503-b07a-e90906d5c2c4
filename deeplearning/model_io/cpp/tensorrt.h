#include "deeplearning/server/trt_runtime/cpp/runtime.h"
#include "generated/deeplearning/model_io/proto/metadata.pb.h"

namespace deeplearning {
namespace model_io {

ModelMetadataProto load_trt_metadata(std::string path);
std::tuple<std::shared_ptr<server::trt_runtime::TRTRuntime>, ModelMetadataProto> load_trt_model(std::string path,
                                                                                                int gpu_id);

} // namespace model_io

} // namespace deeplearning

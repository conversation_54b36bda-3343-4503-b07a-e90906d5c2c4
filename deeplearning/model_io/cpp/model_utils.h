#include <torch/torch.h>

#include "lib/common/cpp/exceptions.h"

namespace deeplearning {
namespace model_io {
inline at::ScalarType dtype_to_torch_type(const std::string& dtype_str) {
  if (dtype_str == "torch.float16") {
    return torch::kF16;
  } else if (dtype_str == "torch.float32") {
    return torch::kF32;
  } else {
    throw maka_error("Unknown dtype " + dtype_str);
  }
}
} // namespace model_io

} // namespace deeplearning

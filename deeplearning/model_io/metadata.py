import copy
import json
from typing import Dict, List, Optional, Tuple, cast

import torch
from aenum import AutoNumberEnum

from deeplearning.utils.use_cases import ModelUseCase
from generated.deeplearning.model_io.proto.metadata_pb2 import (
    ModelMetadataProto,
    ScalerShifterParameterProto,
    SizeProto,
)


class ModelType(AutoNumberEnum):
    PYTORCH: "ModelType" = ()  # type: ignore
    TORCH_SCRIPT: "ModelType" = ()  # type: ignore
    TENSOR_RT: "ModelType" = ()  # type: ignore


# TODO(asergeev): cameras, resolution (or pixels-per-inch)
class ModelMetadata:
    LATEST_VERSION = 2

    def __init__(
        self,
        input_dtype: torch.dtype,
        input_size: Optional[Tuple[int, int]] = None,
        means: Optional[List[float]] = None,
        stds: Optional[List[float]] = None,
        experiment_url: Optional[str] = None,
        supported_classes: Optional[List[str]] = None,  # Deprecated, only for deeplabv3
        weed_point_classes: Optional[List[str]] = None,
        crop_point_classes: Optional[List[str]] = None,
        segm_classes: Optional[List[str]] = None,
        line_classes: Optional[List[str]] = None,
        use_cases: Optional[List[ModelUseCase]] = None,
        supports_depth: Optional[bool] = None,
        ppi: Optional[float] = None,
        tile: Optional[Tuple[int, int]] = None,
        supports_half: Optional[bool] = None,
        max_batch_size: Optional[int] = None,
        aux_input_sizes: Optional[List[Tuple[int, int]]] = None,
        not_interleaved: Optional[bool] = None,
        discard_points_border_px: Optional[float] = None,
        model_class: Optional[str] = None,  # Used by comparison model
        version: Optional[int] = LATEST_VERSION,
        plant_enabled: Optional[bool] = None,
        trained_embeddings: Optional[bool] = None,
        contains_pumap_head: Optional[bool] = None,
        scaler_shifter_parameters: Optional[Dict[str, float]] = None,
        backbone_architecture: Optional[str] = None,
        crop_ids: Optional[List[str]] = None,
        crop_embeddings: Optional[bool] = None,
    ):
        self._model_type: Optional[ModelType] = None
        self._input_dtype = input_dtype
        self._input_size = input_size
        self._means = means
        self._stds = stds
        self._experiment_url = experiment_url
        self._supported_classes = supported_classes
        self._weed_point_classes = weed_point_classes
        self._crop_point_classes = crop_point_classes
        self._line_classes = line_classes
        self._segm_classes = segm_classes
        self._use_cases = use_cases
        self._supports_depth = supports_depth
        self._ppi = ppi
        self._tile = tile
        self._supports_half = supports_half
        self._max_batch_size = max_batch_size
        self._aux_input_sizes = aux_input_sizes
        self._not_interleaved = not_interleaved
        self._discard_points_border_px = discard_points_border_px
        self._model_class = model_class
        # First version did not include version field
        self._version = version if version is not None else 1
        self._plant_enabled = plant_enabled
        self._trained_embeddings = trained_embeddings
        self._contains_pumap_head = contains_pumap_head
        self._scaler_shifter_parameters = scaler_shifter_parameters
        self._backbone_architecture = backbone_architecture
        self._crop_ids = crop_ids
        self._crop_embeddings = crop_embeddings
        self._upgrade()

    def _upgrade(self) -> None:
        if self._version < 2 and self._use_cases is not None and ModelUseCase.P2P in self._use_cases:
            # aux_input_size used to be input_size for P2P before V2
            assert self._input_size is not None
            self._aux_input_sizes = [self._input_size]
            self._input_size = None
        self._version = ModelMetadata.LATEST_VERSION

    @property
    def version(self) -> int:
        return self._version

    def with_model_type(self, model_type: Optional[ModelType]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._model_type = model_type
        return metadata

    @property
    def model_type(self) -> Optional[ModelType]:
        return self._model_type

    def with_input_dtype(self, input_dtype: torch.dtype) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._input_dtype = input_dtype
        return metadata

    @property
    def input_dtype(self) -> torch.dtype:
        return self._input_dtype

    def with_input_size(self, input_size: Optional[Tuple[int, int]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._input_size = input_size
        return metadata

    @property
    def input_size(self) -> Optional[Tuple[int, int]]:
        """
        Input size that model expects: (HxW)

        If None, model can accept inputs of various size.
        """
        return self._input_size

    def with_means(self, means: Optional[List[float]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._means = means
        return metadata

    @property
    def means(self) -> Optional[List[float]]:
        return self._means

    def with_stds(self, stds: Optional[List[float]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._stds = stds
        return metadata

    @property
    def stds(self) -> Optional[List[float]]:
        return self._stds

    def with_experiment_url(self, experiment_url: Optional[str]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._experiment_url = experiment_url
        return metadata

    @property
    def experiment_url(self) -> Optional[str]:
        return self._experiment_url

    def with_supported_classes(self, supported_classes: Optional[List[str]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._supported_classes = supported_classes
        return metadata

    @property
    def supported_classes(self) -> Optional[List[str]]:
        return self._supported_classes

    def with_weed_point_classes(self, weed_point_classes: Optional[List[str]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._weed_point_classes = weed_point_classes
        return metadata

    @property
    def weed_point_classes(self) -> Optional[List[str]]:
        return self._weed_point_classes

    def with_crop_point_classes(self, crop_point_classes: Optional[List[str]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._crop_point_classes = crop_point_classes
        return metadata

    @property
    def crop_point_classes(self) -> Optional[List[str]]:
        return self._crop_point_classes

    def with_segm_classes(self, segm_classes: Optional[List[str]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._segm_classes = segm_classes
        return metadata

    @property
    def segm_classes(self) -> Optional[List[str]]:
        return self._segm_classes

    def with_line_classes(self, line_classes: Optional[List[str]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._line_classes = line_classes
        return metadata

    @property
    def line_classes(self) -> Optional[List[str]]:
        return self._line_classes

    def with_use_cases(self, use_cases: Optional[List[ModelUseCase]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._use_cases = use_cases
        return metadata

    @property
    def use_cases(self) -> Optional[List[ModelUseCase]]:
        return self._use_cases

    def with_supports_depth(self, supports_depth: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._supports_depth = supports_depth
        return metadata

    @property
    def supports_depth(self) -> Optional[bool]:
        return self._supports_depth

    def with_ppi(self, ppi: Optional[float]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._ppi = ppi
        return metadata

    @property
    def ppi(self) -> Optional[float]:
        return self._ppi

    def with_tile(self, tile: Optional[Tuple[int, int]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._tile = tile
        return metadata

    @property
    def tile(self) -> Optional[Tuple[int, int]]:
        return self._tile

    def with_supports_half(self, supports_half: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._supports_half = supports_half
        return metadata

    @property
    def supports_half(self) -> Optional[bool]:
        """Flag indicating whether model supports model.half() and fp16 input."""
        return self._supports_half

    def with_max_batch_size(self, max_batch_size: int) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._max_batch_size = max_batch_size
        return metadata

    @property
    def max_batch_size(self) -> Optional[int]:
        """Maximum supported batch size, typical for TensorRT models."""
        return self._max_batch_size

    def with_aux_input_sizes(self, aux_input_sizes: Optional[List[Tuple[int, int]]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._aux_input_sizes = aux_input_sizes
        return metadata

    @property
    def aux_input_sizes(self) -> Optional[List[Tuple[int, int]]]:
        return self._aux_input_sizes

    def with_not_interleaved(self, not_interleaved: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._not_interleaved = not_interleaved
        return metadata

    @property
    def not_interleaved(self) -> Optional[bool]:
        """Whether we use interleaved hits."""
        return self._not_interleaved

    def with_discard_points_border_px(self, discard_points_border_px: Optional[float]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._discard_points_border_px = discard_points_border_px
        return metadata

    @property
    def discard_points_border_px(self) -> Optional[float]:
        return self._discard_points_border_px

    def with_plant_enabled(self, plant_enabled: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._plant_enabled = plant_enabled
        return metadata

    @property
    def plant_enabled(self) -> Optional[bool]:
        return self._plant_enabled

    def with_trained_embeddings(self, trained_embeddings: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._trained_embeddings = trained_embeddings
        return metadata

    @property
    def trained_embeddings(self) -> Optional[bool]:
        return self._trained_embeddings

    def with_contains_pumap_head(self, contains_pumap_head: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._contains_pumap_head = contains_pumap_head
        return metadata

    @property
    def contains_pumap_head(self) -> Optional[bool]:
        return self._contains_pumap_head

    def with_scaler_shifter_parameters(self, scaler_shifter_parameters: Optional[Dict[str, float]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._scaler_shifter_parameters = scaler_shifter_parameters
        return metadata

    @property
    def scaler_shifter_parameters(self) -> Optional[Dict[str, float]]:
        return self._scaler_shifter_parameters

    def with_model_class(self, model_class: Optional[str]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._model_class = model_class
        return metadata

    @property
    def model_class(self) -> Optional[str]:
        return self._model_class

    def with_backbone_architecture(self, backbone_architecture: Optional[str]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._backbone_architecture = backbone_architecture
        return metadata

    @property
    def backbone_architecture(self) -> Optional[str]:
        return self._backbone_architecture

    def with_crop_ids(self, crop_ids: Optional[List[str]]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._crop_ids = crop_ids
        return metadata

    @property
    def crop_ids(self) -> Optional[List[str]]:
        return self._crop_ids

    def with_crop_embeddings(self, crop_embeddings: Optional[bool]) -> "ModelMetadata":
        metadata = copy.deepcopy(self)
        metadata._crop_embeddings = crop_embeddings
        return metadata

    @property
    def crop_embeddings(self) -> Optional[bool]:
        return self._crop_embeddings

    def dump(self) -> str:
        return json.dumps(
            {
                "model_type": self._model_type.name if self._model_type is not None else None,
                "input_dtype": str(self._input_dtype),
                "input_size": self._input_size,
                "means": self._means,
                "stds": self._stds,
                "experiment_url": self._experiment_url,
                "supported_classes": (
                    [c for c in self._supported_classes] if self._supported_classes is not None else None
                ),
                "weed_point_classes": self._weed_point_classes,
                "crop_point_classes": self._crop_point_classes,
                "segm_classes": self._segm_classes,
                "line_classes": self._line_classes,
                "use_cases": ([c.name for c in self._use_cases] if self._use_cases is not None else None),
                "supports_depth": self._supports_depth,
                "ppi": self._ppi,
                "tile": self._tile,
                "supports_half": self._supports_half,
                "max_batch_size": self._max_batch_size,
                "aux_input_sizes": self._aux_input_sizes,
                "not_interleaved": self._not_interleaved,
                "discard_points_border_px": self._discard_points_border_px,
                "model_class": self._model_class,
                "version": self._version,
                "plant_enabled": self.plant_enabled,
                "trained_embeddings": self.trained_embeddings,
                "contains_pumap_head": self.contains_pumap_head,
                "scaler_shifter_parameters": self.scaler_shifter_parameters,
                "backbone_architecture": self.backbone_architecture,
                "crop_ids": self._crop_ids,
                "crop_embeddings": self._crop_embeddings,
            },
            indent=5,
        )

    @staticmethod
    def load(serialized: str) -> "ModelMetadata":
        d = json.loads(serialized)
        return ModelMetadata(
            input_dtype=getattr(torch, d["input_dtype"].split(".")[1]),
            input_size=cast(Tuple[int, int], tuple(d["input_size"])) if d.get("input_size") is not None else None,
            means=d.get("means"),
            stds=d.get("stds"),
            experiment_url=d.get("experiment_url"),
            supported_classes=([c for c in d["supported_classes"]] if d.get("supported_classes") is not None else None),
            crop_point_classes=d.get("crop_point_classes"),
            weed_point_classes=d.get("weed_point_classes"),
            segm_classes=d.get("segm_classes"),
            line_classes=d.get("line_classes"),
            use_cases=([ModelUseCase[c] for c in d["use_cases"]] if d.get("use_cases") is not None else None),
            supports_depth=d.get("supports_depth"),
            ppi=d.get("ppi"),
            tile=d.get("tile"),
            supports_half=d.get("supports_half"),
            max_batch_size=d.get("max_batch_size"),
            aux_input_sizes=d.get("aux_input_sizes"),
            not_interleaved=d.get("not_interleaved"),
            discard_points_border_px=d.get("discard_points_border_px"),
            model_class=d.get("model_class"),
            version=d.get("version"),
            plant_enabled=d.get("plant_enabled"),
            trained_embeddings=d.get("trained_embeddings"),
            contains_pumap_head=d.get("contains_pumap_head"),
            scaler_shifter_parameters=d.get("scaler_shifter_parameters"),
            backbone_architecture=d.get("backbone_architecture"),
            crop_ids=d.get("crop_ids"),
            crop_embeddings=d.get("crop_embeddings"),
        ).with_model_type(
            ModelType[d["model_type"]] if d.get("model_type") is not None else None  # type: ignore
        )

    def to_proto(self) -> "ModelMetadataProto":  # noqa: C901
        metadata_proto = ModelMetadataProto()
        metadata_proto.input_dtype = str(self._input_dtype)
        if self._input_size is not None:
            metadata_proto.input_size.CopyFrom(SizeProto(width=self._input_size[1], height=self._input_size[0]))
        else:
            metadata_proto.input_size.CopyFrom(SizeProto(width=-1, height=-1))
        if self._means is not None:
            metadata_proto.means.extend(self._means)
        if self._stds is not None:
            metadata_proto.stds.extend(self._stds)
        if self._experiment_url is not None:
            metadata_proto.experiment_url = self._experiment_url
        if self._supported_classes is not None:
            metadata_proto.supported_classes.extend([c for c in self._supported_classes])
        if self._weed_point_classes is not None:
            metadata_proto.weed_point_classes.extend(self._weed_point_classes)
        if self._crop_point_classes is not None:
            metadata_proto.crop_point_classes.extend(self._crop_point_classes)
        if self._segm_classes is not None:
            metadata_proto.segm_classes.extend(self._segm_classes)
        if self._line_classes is not None:
            metadata_proto.line_classes.extend(self._line_classes)
        if self._use_cases is not None:
            metadata_proto.use_cases.extend([c.name for c in self._use_cases])
        if self._supports_depth is not None:
            metadata_proto.supports_depth = self._supports_depth
        if self._ppi is not None:
            metadata_proto.ppi = self._ppi
        if self._tile is not None:
            metadata_proto.tile.CopyFrom(SizeProto(width=self._tile[1], height=self._tile[0]))
        if self._supports_half is not None:
            metadata_proto.supports_half = self._supports_half
        if self._max_batch_size is not None:
            metadata_proto.max_batch_size = self._max_batch_size
        if self._aux_input_sizes is not None:
            metadata_proto.aux_input_sizes.extend([SizeProto(width=x[1], height=x[0]) for x in self._aux_input_sizes])
        if self._max_batch_size is not None:
            metadata_proto.max_batch_size = self._max_batch_size
        if self._model_type is not None:
            metadata_proto.model_type = self._model_type.name
        if self._not_interleaved is not None:
            metadata_proto.not_interleaved = self._not_interleaved
        if self._discard_points_border_px is not None:
            metadata_proto.discard_points_border_px = self._discard_points_border_px
        if self._plant_enabled is not None:
            metadata_proto.plant_enabled = self._plant_enabled
        if self._model_class is not None:
            metadata_proto.model_class = self._model_class
        if self._trained_embeddings is not None:
            metadata_proto.trained_embeddings = self._trained_embeddings
        if self._contains_pumap_head is not None:
            metadata_proto.contains_pumap_head = self._contains_pumap_head
        if self._backbone_architecture is not None:
            metadata_proto.backbone_architecture = self._backbone_architecture
        if self._scaler_shifter_parameters is not None:
            metadata_proto.scaler_shifter_parameters.CopyFrom(
                ScalerShifterParameterProto(
                    min_real=self._scaler_shifter_parameters["min_real"],
                    max_real=self._scaler_shifter_parameters["max_real"],
                    min_scale=self._scaler_shifter_parameters["min_scale"],
                    max_scale=self._scaler_shifter_parameters["max_scale"],
                    shift_real=self._scaler_shifter_parameters["shift_real"],
                    shift_ind=cast(int, self._scaler_shifter_parameters["shift_ind"]),
                )
            )
        if self._crop_ids is not None:
            metadata_proto.crop_ids.extend(self._crop_ids)
        if self._crop_embeddings is not None:
            metadata_proto.crop_embeddings = self._crop_embeddings
        metadata_proto.version = self._version
        return metadata_proto

    @staticmethod
    def from_proto(model_metadata_proto: ModelMetadataProto) -> "ModelMetadata":
        return ModelMetadata(
            input_dtype=getattr(torch, model_metadata_proto.input_dtype.split(".")[1]),
            input_size=(model_metadata_proto.input_size.height, model_metadata_proto.input_size.width),
            means=list(model_metadata_proto.means),
            stds=list(model_metadata_proto.stds),
            experiment_url=model_metadata_proto.experiment_url,
            supported_classes=[c for c in model_metadata_proto.supported_classes],
            weed_point_classes=list(model_metadata_proto.weed_point_classes),
            crop_point_classes=list(model_metadata_proto.crop_point_classes),
            segm_classes=list(model_metadata_proto.segm_classes),
            line_classes=list(model_metadata_proto.line_classes),
            use_cases=[ModelUseCase[c] for c in model_metadata_proto.use_cases],
            supports_depth=model_metadata_proto.supports_depth,
            ppi=model_metadata_proto.ppi,
            tile=(model_metadata_proto.tile.height, model_metadata_proto.tile.width),
            supports_half=model_metadata_proto.supports_half,
            max_batch_size=model_metadata_proto.max_batch_size,
            aux_input_sizes=[(x.height, x.width) for x in model_metadata_proto.aux_input_sizes],
            not_interleaved=model_metadata_proto.not_interleaved,
            discard_points_border_px=model_metadata_proto.discard_points_border_px,
            model_class=model_metadata_proto.model_class,
            version=model_metadata_proto.version,
            plant_enabled=model_metadata_proto.plant_enabled,
            trained_embeddings=model_metadata_proto.trained_embeddings,
            contains_pumap_head=model_metadata_proto.contains_pumap_head,
            backbone_architecture=model_metadata_proto.backbone_architecture,
            scaler_shifter_parameters={
                "min_real": model_metadata_proto.scaler_shifter_parameters.min_real,
                "max_real": model_metadata_proto.scaler_shifter_parameters.max_real,
                "min_scale": model_metadata_proto.scaler_shifter_parameters.min_scale,
                "max_scale": model_metadata_proto.scaler_shifter_parameters.max_scale,
                "shift_real": model_metadata_proto.scaler_shifter_parameters.shift_real,
                "shift_ind": model_metadata_proto.scaler_shifter_parameters.shift_ind,
            },
            crop_ids=list(model_metadata_proto.crop_ids),
            crop_embeddings=model_metadata_proto.crop_embeddings,
        ).with_model_type(
            ModelType[model_metadata_proto.model_type] if model_metadata_proto.model_type else None  # type: ignore
        )

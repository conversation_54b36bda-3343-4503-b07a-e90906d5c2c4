from typing import Tuple

import google.protobuf.message
import torch

from deeplearning.model_io.metadata import ModelMetadata, ModelType
from generated.deeplearning.model_io.proto.trt_model_pb2 import TRTModelProto


def save_tensorrt_model(model: torch.nn.Module, metadata: ModelMetadata, path: str) -> None:
    import torch2trt

    assert isinstance(model, torch2trt.TRTModule)
    container = model.state_dict()
    metadata = metadata.with_model_type(ModelType.TENSOR_RT)
    trt_model_proto = TRTModelProto()
    trt_model_proto.metadata.CopyFrom(metadata.to_proto())
    trt_model_proto.engine_bytes = bytes(container["engine"])
    trt_model_proto.input_names.extend(container["input_names"])
    trt_model_proto.output_names.extend(container["output_names"])
    trt_model_proto.implicit_batch_dimension = container.get("implicit_batch_dimension", False)
    trt_model_proto.tensorrt_version = container.get("version", "8.0.1.6")

    with open(path, "wb") as fp:
        fp.write(trt_model_proto.SerializeToString())


def peek_tensorrt_metadata(path: str) -> ModelMetadata:
    container = torch.load(path)
    metadata = ModelMetadata.load(container["metadata"])
    assert metadata.model_type == ModelType.TENSOR_RT
    return metadata


def load_tensorrt_model(path: str) -> Tuple[torch.nn.Module, ModelMetadata]:
    import deeplearning.trt_extensions  # noqa, load all Maka plugins
    import deeplearning.server.trt_runtime as rt

    # Try loading file as a protobuf tensorrt model
    try:
        with open(path, "rb") as fp:
            proto_bytes = fp.read()
        model, metadata = rt.TRTModule.from_proto(proto_bytes)
        assert metadata.model_type == ModelType.TENSOR_RT
        return (model, metadata)
    except google.protobuf.message.DecodeError:
        pass

    # Otherwise, try decoding as pytorch tensorrt model
    model = rt.TRTModule()
    container = torch.load(path)
    model.load_state_dict(container)
    metadata = ModelMetadata.load(container["metadata"])
    assert metadata.model_type == ModelType.TENSOR_RT
    return (model, metadata)

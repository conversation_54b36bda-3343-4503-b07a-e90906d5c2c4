import copy
from typing import <PERSON><PERSON>

import torch

from deeplearning.model_io.metadata import ModelMetadata, ModelType


def save_pytorch_model(model: torch.nn.Module, metadata: ModelMetadata, path: str) -> None:
    metadata = metadata.with_model_type(ModelType.PYTORCH)
    container = {
        "state_dict": model.state_dict(),
        "metadata": metadata.dump(),
    }
    torch.save(container, path)


def peek_pytorch_metadata(path: str) -> ModelMetadata:
    container = torch.load(path, map_location=torch.device("cpu"))
    metadata = ModelMetadata.load(container["metadata"])
    assert metadata.model_type == ModelType.PYTORCH
    return metadata


def load_pytorch_model(
    template_model: torch.nn.Module, path: str, strict: bool = True
) -> Tuple[torch.nn.Module, ModelMetadata]:
    container = torch.load(path, map_location=torch.device("cpu"))
    metadata = ModelMetadata.load(container["metadata"])
    assert metadata.model_type == ModelType.PYTORCH
    model = copy.deepcopy(template_model)
    model.load_state_dict(container["state_dict"], strict=strict)
    return (model, metadata)

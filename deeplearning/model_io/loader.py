import traceback
from typing import <PERSON><PERSON>

import torch

from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.tensorrt import load_tensorrt_model
from deeplearning.model_io.torchscript import load_torchscript_model
from lib.common.error import MakaException


class ModelLoadException(MakaException):
    """Exception that is thrown when we're unable to load a model."""

    pass


def load_model(path: str) -> Tu<PERSON>[torch.nn.Module, ModelMetadata]:
    # Try TorchScript load first
    torchscript_error = None
    try:
        return load_torchscript_model(path)
    except Exception:
        torchscript_error = traceback.format_exc()

    # Try TensorRT load
    trt_error = None
    try:
        return load_tensorrt_model(path)
    except Exception:
        trt_error = traceback.format_exc()

    raise ModelLoadException(
        "Cannot load model {} as neither TorchScript nor TensorRT model.\n"
        "TorchScript error:\n{}\n"
        "TensorRT error:\n{}".format(path, torchscript_error, trt_error)
    )

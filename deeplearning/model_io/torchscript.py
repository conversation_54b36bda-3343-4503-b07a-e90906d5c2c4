from typing import <PERSON><PERSON>

import torch
import torch.jit

from deeplearning.model_io.metadata import ModelMetadata, ModelType


def save_torchscript_model(model: torch.jit.ScriptModule, metadata: ModelMetadata, path: str) -> None:
    metadata = metadata.with_model_type(ModelType.TORCH_SCRIPT)
    extra_files = {"metadata": metadata.dump()}
    torch.jit.save(model, path, _extra_files=extra_files)


def peek_torchscript_metadata(path: str) -> ModelMetadata:
    extra_files = {"metadata": ""}
    _ = torch.jit.load(path, _extra_files=extra_files)
    metadata = ModelMetadata.load(extra_files["metadata"])
    assert metadata.model_type == ModelType.TORCH_SCRIPT
    return metadata


def load_torchscript_model(path: str) -> Tuple[torch.nn.Module, ModelMetadata]:
    extra_files = {"metadata": ""}
    model = torch.jit.load(path, _extra_files=extra_files)
    metadata = ModelMetadata.load(extra_files["metadata"])
    assert metadata.model_type == ModelType.TORCH_SCRIPT
    return (model, metadata)

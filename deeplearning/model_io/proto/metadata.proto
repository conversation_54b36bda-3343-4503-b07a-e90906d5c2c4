syntax = "proto3";

message SizeProto {
    int32 width = 1;
    int32 height = 2;
}

message ScalerShifterParameterProto {
    float min_real = 1;
    float max_real = 2;
    float min_scale = 3;
    float max_scale = 4;
    float shift_real = 5;
    int32 shift_ind = 6;
}

message ModelMetadataProto {
    string input_dtype = 1;
    SizeProto input_size = 2;
    repeated float means = 3;
    repeated float stds = 4;
    string experiment_url = 5;
    repeated string supported_classes = 6;
    repeated string use_cases = 7;
    bool supports_depth = 8;
    float ppi = 9;
    SizeProto tile = 10;
    bool supports_half = 11;
    int32 max_batch_size = 12;
    repeated SizeProto aux_input_sizes = 13;
    int32 version = 14;
    string model_type = 15;
    bool not_interleaved = 16;
    float discard_points_border_px = 17;
    repeated string weed_point_classes = 18;
    repeated string crop_point_classes = 19;
    repeated string segm_classes = 20;
    repeated string line_classes = 21;
    string model_class = 22;
    bool plant_enabled = 23;
    bool trained_embeddings = 24;
    bool contains_pumap_head = 25;
    string backbone_architecture = 26;
    ScalerShifterParameterProto scaler_shifter_parameters = 27;
    repeated string crop_ids = 28;
    bool crop_embeddings = 29;
}

import json
import logging
from typing import Dict, List, Union

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.semi_supervised import coco_types
from deeplearning.semi_supervised.config import Config
from deeplearning.semi_supervised.types import Classification, Datapoint, Detection, Point
from lib.common.veselka.client import VeselkaClient

LOG = logging.getLogger(__name__)


POINT_CATEGORY_IDS = {
    "9fd4e9d0-2d01-4ae1-89c5-f4b4f6de9a3c": "broadleaf",
    "2e678c61-f476-4485-aa9c-6a0e4bc7c626": "offshoot",
    "f032b206-92cf-400a-b79e-e22bdb7930e3": "grass",
    "1659226a-fe1c-450c-828f-88dc8819c25d": "purslane",
    "8bb671f4-0eb7-4778-8262-3fc5445c6ea8": "crop",
}


def get_standard_dataset(config: Config) -> str:
    client = VeselkaClient(data_dir=f"{CARBON_DATA_DIR}/deeplearning")

    if config.dataset_id is None:
        dataset_id = client.create_deepweed_dataset_v2(config.pipeline_id, unlabeled_data=True)
    else:
        dataset_id = config.dataset_id

    dataset = client.get_dataset_v2(dataset_id=dataset_id)

    if dataset is None:
        raise RuntimeError(f"Failed to get dataset: dataset_id={dataset_id}")

    client.download_dataset_v2(dataset=dataset)
    LOG.info(f"Downloaded dataset: {dataset.id}")

    return dataset.id


def compile_semi_supervised_dataset(config: Config, dataset_id: str) -> None:  # noqa: C901
    LOG.info("Compiling semi-supervised dataset")

    num_labeled_images = 0
    num_predicted_images = 0
    num_labeled_points = 0
    num_predicted_points = 0

    num_high_confidence = 0
    num_low_confidence = 0

    experiment_dir = f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}"
    dataset_dir = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}"

    detections_filepath = f"{experiment_dir}/detections.jsonl"
    classifications_filepath = f"{experiment_dir}/classifications.jsonl"
    train_filepath = f"{dataset_dir}/train.jsonl"
    semi_supervised_train = f"{experiment_dir}/semi_supervised_train.jsonl"

    # TODO: Turn all of these into pydantic objects
    with open(detections_filepath, "r") as f:
        detections = [Detection.parse_raw(line) for line in f]

    image_id2detection: Dict[str, Detection] = {detection.image_id: detection for detection in detections}

    with open(classifications_filepath, "r") as f:
        classifications = [Classification.parse_raw(line) for line in f]

    image_id2classifications: Dict[str, List[Classification]] = {}
    for c in classifications:
        if c.image_id not in image_id2classifications:
            image_id2classifications[c.image_id] = []

        image_id2classifications[c.image_id].append(c)

    with open(train_filepath, "r") as f:
        train_datapoints = [Datapoint.parse_raw(line) for line in f]

    with open(semi_supervised_train, "w") as f:
        for datapoint in train_datapoints:

            if datapoint.label_id is not None:
                num_labeled_images += 1
                num_labeled_points += len(datapoint.points)
                f.write(datapoint.json() + "\n")
                continue

            if not config.augment_dataset:
                continue

            detection = image_id2detection.get(datapoint.image_id)
            detection_classifications = image_id2classifications.get(datapoint.image_id)

            if detection is None:
                LOG.info(f"Failed to find detection for image_id: {datapoint.image_id}")
                continue

            if detection_classifications is None:
                LOG.info(f"Failed to find classifications for image_id: {datapoint.image_id}")
                continue

            datapoint.detection_id = detection.id

            detection_point_id2classification = {c.detection_point_id: c for c in detection_classifications}

            for detection_point in detection.points:
                classification = detection_point_id2classification.get(detection_point.id)

                if classification is None:
                    if config.fast_run:
                        continue

                    LOG.info(f"Failed to find classification for detection_point_id: {detection_point.id}")
                    continue

                for point_category_id in classification.certified_point_category_ids:
                    if point_category_id not in datapoint.certified_point_category_ids:
                        datapoint.certified_point_category_ids.append(point_category_id)

                if detection_point.plant_score > config.detection_plant_low_confidence_threshold:

                    id = detection_point.id
                    x = detection_point.x
                    y = detection_point.y
                    radius = detection_point.radius
                    point_category_id = classification.point_category_id

                    if (
                        classification.probability > config.classification_threshold
                        and detection_point.plant_score > config.detection_plant_high_confidence_threshold
                    ):
                        confidence = 2
                        num_high_confidence += 1
                    else:
                        confidence = 0
                        num_low_confidence += 1

                    point = Point(
                        id=id, x=x, y=y, radius=radius, point_category_id=point_category_id, confidence=confidence,
                    )

                    datapoint.points.append(point)

            if len(datapoint.points) > 0 or len(datapoint.polygons) > 0:
                num_predicted_images += 1
                num_predicted_points += len(datapoint.points)
                f.write(datapoint.json() + "\n")

    convert_to_coco_format(
        input_filepath=semi_supervised_train,
        output_filepath=f"{dataset_dir}/train.json",
        dataset_id=dataset_id,
        dataset_name="unknown",  # TODO: source dataset name or verify we don't need it
        dataset_created_at=0,  # TODO: source dataset created or verify we don't need it
    )

    convert_to_coco_format(
        input_filepath=f"{dataset_dir}/validation.jsonl",
        output_filepath=f"{dataset_dir}/validation.json",
        dataset_id=dataset_id,
        dataset_name="unknown",  # TODO: source dataset name or verify we don't need it
        dataset_created_at=0,  # TODO: source dataset created or verify we don't need it
    )

    convert_to_coco_format(
        input_filepath=f"{dataset_dir}/test.jsonl",
        output_filepath=f"{dataset_dir}/test.json",
        dataset_id=dataset_id,
        dataset_name="unknown",  # TODO: source dataset name or verify we don't need it
        dataset_created_at=0,  # TODO: source dataset created or verify we don't need it
    )

    LOG.info(f"Num labeled images: {num_labeled_images}")
    LOG.info(f"Num labeled points: {num_labeled_points}")

    LOG.info(f"Num predicted images: {num_predicted_images}")
    LOG.info(f"Num predicted points: {num_predicted_points}")
    LOG.info(f"Num predicted high confidence points: {num_high_confidence}")
    LOG.info(f"Num predicted low confidence points: {num_low_confidence}")


def convert_to_coco_format(  # noqa: C901
    input_filepath: str, output_filepath: str, dataset_id: str, dataset_name: str, dataset_created_at: int
) -> None:

    images: List[coco_types.COCOImage] = []
    annotations: List[Union[coco_types.COCOPointAnnotation, coco_types.COCOPolygonAnnotation]] = []
    categories: List[coco_types.COCOCategory] = []

    with open(input_filepath, "r") as f:
        for line in f.readlines():
            datapoint = Datapoint.parse_raw(line)

            if len(datapoint.certified_point_category_ids) == 0:
                point_categories = list(
                    set([POINT_CATEGORY_IDS.get(p.point_category_id, p.point_category_id) for p in datapoint.points])
                )
            else:
                point_categories = [POINT_CATEGORY_IDS.get(c, c) for c in datapoint.certified_point_category_ids]

            # TODO: Pull from certified categories on the label
            if datapoint.label_id is not None:
                polygon_categories = ["driptape"]
            else:
                polygon_categories = []

            # Create and store the image
            image = coco_types.COCOImage(
                id=datapoint.image_id,
                point_categories=point_categories,
                polygon_categories=polygon_categories,
                city=datapoint.geohash[:4],
                row_id="unknown",
                cam_id="unknown",
                **datapoint.dict(),
            )

            images.append(image)

            for point in datapoint.points:
                category_exists = False

                if point.point_category_id in POINT_CATEGORY_IDS:
                    category_name = POINT_CATEGORY_IDS[point.point_category_id]
                else:
                    category_name = point.point_category_id

                for existing_category in categories:
                    if category_name == existing_category.name:
                        category_exists = True
                        category = existing_category
                        break

                if not category_exists:

                    if category_name == "crop":
                        supercategory = "crop"
                    else:
                        supercategory = "weed"
                    category = coco_types.COCOCategory(
                        supercategory=supercategory, name=category_name, id=len(categories), type="point",
                    )
                    categories.append(category)

                if datapoint.label_id is not None:
                    label_id = datapoint.label_id
                elif datapoint.detection_id is not None:
                    label_id = datapoint.detection_id
                else:
                    raise RuntimeError("Datapoint must have either label_id or detection_id")

                point_annotation = coco_types.COCOPointAnnotation(
                    image_id=datapoint.image_id,
                    last_updated=0,
                    label_id=label_id,
                    category_id=category.id,
                    label=category_name,
                    **point.dict(),
                )

                annotations.append(point_annotation)

            for polygon in datapoint.polygons:
                category_exists = False
                for existing_category in categories:
                    if polygon.label == existing_category.name:
                        category_exists = True
                        category = existing_category
                        break

                if not category_exists:
                    category = coco_types.COCOCategory(
                        supercategory="polygon", name=polygon.label, id=len(categories), type="polygon",
                    )
                    categories.append(category)

                if datapoint.label_id is not None:
                    label_id = datapoint.label_id
                elif datapoint.detection_id is not None:
                    label_id = datapoint.detection_id
                else:
                    raise RuntimeError("Datapoint must have either label_id or detection_id")

                polygon_annotation = coco_types.COCOPolygonAnnotation(
                    image_id=datapoint.image_id,
                    last_updated=0,
                    label_id=label_id,
                    category_id=category.id,
                    **polygon.dict(),
                )

                annotations.append(polygon_annotation)

    info = coco_types.COCOInfo(
        version="v2", split="train", id=dataset_id, name=dataset_name, created=dataset_created_at,
    )

    coco_dataset = coco_types.COCODataset(
        info=info, licenses=[], images=images, annotations=annotations, categories=categories,
    )

    with open(output_filepath, "w") as f:
        json.dump(coco_dataset.dict(), f)

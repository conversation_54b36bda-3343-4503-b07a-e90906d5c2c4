import json
import logging
import multiprocessing
import os
import threading
import traceback
from typing import List

import torch

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.semi_supervised.config import Config
from deeplearning.semi_supervised.types import Datapoint, Detection
from veselka.cv.deepweed.server import LOG as PREDICTION_LOG
from veselka.cv.deepweed.server import DeepweedPredictionHandler

LOG = logging.getLogger(__name__)


def get_dataset(filepath: str) -> List[Datapoint]:
    dataset = []

    with open(filepath, "r") as f:
        for line in f.readlines():
            datapoint = Datapoint.parse_raw(line)
            if datapoint.label_id is None:
                dataset.append(datapoint)

    return dataset


def process_chunk(config: Config, dataset: List[Datapoint], worker_index: int) -> None:
    try:
        PREDICTION_LOG.setLevel(level=logging.DEBUG)

        device_count = torch.cuda.device_count()
        deepweed_prediction_handler = DeepweedPredictionHandler(threading.Lock(), gpu_index=worker_index % device_count)
        output_file = f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}/detections.jsonl"

        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        if not config.augment_dataset:
            with open(output_file, "w") as f:
                f.write("")

            return None

        for index, datapoint in enumerate(dataset):
            LOG.info(f"Prediction runner {worker_index} processing datapoint {index + 1} / {len(dataset)}")

            request = {
                "threshold": 0.05,
                "model_id": config.detection_model_id,
                "image_id": datapoint.image_id,
                "model_s3_path": f"s3://maka-pono/models/{config.detection_model_id}/trt_int8_8016_86.trt",
                "image_s3_path": datapoint.uri,
                "predict_border": True,
                "ppi": 200,
            }

            response = deepweed_prediction_handler._dl_prediction_v2(request)
            response["points"] = [point for point in response["points"] if point["hit_class"] == 2]  # Only plants

            detection = Detection(uri=datapoint.uri, **response)

            with open(output_file, "a") as f:
                f.write(detection.json() + "\n")
    except Exception:
        LOG.info(f"Prediction runner {worker_index} failed.")
        traceback.print_exc()


def generate_detections(config: Config, dataset_id: str) -> None:
    dataset = get_dataset(filepath=f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.jsonl")

    LOG.info(f"Running predictions over {len(dataset)} unlabeled datapoints")

    if config.fast_run:
        num_workers = 8 * 1
    else:
        num_workers = 8 * config.detection_num_workers

    if config.fast_run:
        chunk_size = 2
    else:
        chunk_size = (len(dataset) // num_workers) + 1

    with multiprocessing.Pool(num_workers) as pool:
        for worker_index in range(num_workers):
            start = worker_index * chunk_size
            end = (worker_index + 1) * chunk_size

            LOG.info(f"Chunking dataset: start={start}, end={end}, dataset length={len(dataset)}")
            pool.apply_async(process_chunk, args=(config, dataset[start:end], worker_index))

        pool.close()
        pool.join()

    LOG.info("Completed detection generation on unlabeled datapoints")

    with open(f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}/detections.jsonl") as f:
        generated_data = [json.loads(line) for line in f.readlines()]

    LOG.info(f"Dataset size: {len(dataset)}")
    LOG.info(f"Generated detections: {len(generated_data)}")

import logging
import os
from typing import Dict, Union

import torch
import torch.utils

import fewshot
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.semi_supervised.types import Datapoint, Detection, DetectionPoint, Point
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

LOG = logging.getLogger(__name__)

CROP_POINT_CATEGORY_ID = "8bb671f4-0eb7-4778-8262-3fc5445c6ea8"
PLANT_POINT_CATEGORY_ID = ""


def datapoint_loader(datapoint: Union[Datapoint, Detection], point: Union[Point, DetectionPoint]) -> torch.Tensor:

    s3_cache_proxy_client = S3CacheProxyClient(s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"))

    bucket, key = s3_cache_proxy_client.split_uri(datapoint.uri)
    image = s3_cache_proxy_client.get_image_subset(bucket, key, point.x, point.y, point.radius)

    tensor: torch.Tensor = fewshot.utilities.default_evaluation_transform_fn(image)

    return tensor


def get_model(model_id: str) -> fewshot.models.ProtoNets:
    filepath = f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/weights/best.pt"
    encoder = fewshot.encoders.ResNet(architecture="resnet50", pretrained=False)
    model = fewshot.models.ProtoNets(encoder)
    model.load_weights(filepath)
    return model


def download_model(model_id: str) -> None:
    s3_cache_proxy = S3CacheProxyClient(os.getenv("S3_CACHE_PROXY_SERVICE_HOST"))

    bucket = "maka-pono"
    key = f"media/models/{model_id}/weights/best.pt"
    filepath = f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}/weights/best.pt"

    if not os.path.exists(filepath):
        s3_cache_proxy.download(bucket, key, filepath)


def load_detections(filepath: str) -> Dict[str, Detection]:
    detections: Dict[str, Detection] = {}
    with open(filepath, "r") as f:
        for line in f.readlines():
            detection = Detection.parse_raw(line)
            detections[detection.image_id] = detection
    return detections

import logging
import math
from typing import Iterator, <PERSON>, <PERSON>ple

import torch
import torch.utils

import fewshot
from deeplearning.semi_supervised.classification.types import FewshotIndex
from deeplearning.semi_supervised.classification.utils import datapoint_loader
from deeplearning.semi_supervised.types import Datapoint, Detection, DetectionPoint, Point

LOG = logging.getLogger(__name__)

CROP_POINT_CATEGORY_ID = "8bb671f4-0eb7-4778-8262-3fc5445c6ea8"
PLANT_POINT_CATEGORY_ID = ""


class EpisodeDataset(torch.utils.data.Dataset[DetectionPoint]):
    def __init__(
        self, support_set: List[Tuple[Datapoint, Point]], query_set: List[Tuple[Detection, DetectionPoint]]
    ) -> None:

        self._support_set = support_set
        self._query_set = query_set

        assert len(self._support_set) != 0, f"Support set is empty, query set has {len(self._query_set)} datapoints"

    def __getitem__(self, index: FewshotIndex) -> fewshot.dataset_types.Datapoint:

        if index.role == fewshot.dataset_types.DatapointRole.SUPPORT:
            datapoint, point = self._support_set[index.index]

            image = datapoint_loader(datapoint, point)

            fewshot_datapoint = fewshot.dataset_types.Datapoint(
                image=image, identifier=point.id, label=point.point_category_id, role=index.role,
            )
        else:
            detection, detection_point = self._query_set[index.index]
            image = datapoint_loader(detection, detection_point)

            fewshot_datapoint = fewshot.dataset_types.Datapoint(
                image=image, identifier=detection_point.id, label="plant", role=index.role,
            )

        return fewshot_datapoint

    @property
    def num_support_points(self) -> int:
        return len(self._support_set)

    def __len__(self) -> int:
        return len(self._query_set)

    def collate_fn(self, datapoints: List[fewshot.dataset_types.Datapoint]) -> fewshot.Episode:
        episode = fewshot.dataset_types.Episode(datapoints)
        return episode


class EpisodeSampler(torch.utils.data.Sampler[List[FewshotIndex]]):
    def __init__(self, dataset: EpisodeDataset, batch_size: int) -> None:
        self._dataset = dataset
        self._batch_size = batch_size

        self._support_set = [
            FewshotIndex(index=i, role=fewshot.dataset_types.DatapointRole.SUPPORT)
            for i in range(self._dataset.num_support_points)
        ]

    def __iter__(self) -> Iterator[List[FewshotIndex]]:

        query_set = []
        for index in range(len(self._dataset)):
            fewshot_index = FewshotIndex(index=index, role=fewshot.dataset_types.DatapointRole.QUERY)

            query_set.append(fewshot_index)

            if len(query_set) == self._batch_size:
                episode = self._support_set + query_set
                yield episode
                query_set = []

        if len(query_set) > 0:
            episode = self._support_set + query_set
            yield episode
            query_set = []

    def __len__(self) -> int:
        return math.ceil(len(self._dataset) / self._batch_size)

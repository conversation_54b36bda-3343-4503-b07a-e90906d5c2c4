import datetime
import logging
import multiprocessing
import random
import traceback
from typing import Dict, List, <PERSON><PERSON>

import torch
import torch.utils

import fewshot
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.semi_supervised.classification.dataset import EpisodeDataset, EpisodeSampler
from deeplearning.semi_supervised.classification.types import DataBlock
from deeplearning.semi_supervised.classification.utils import download_model, get_model, load_detections
from deeplearning.semi_supervised.config import Config
from deeplearning.semi_supervised.types import Classification, Datapoint, Detection, DetectionPoint, Point

LOG = logging.getLogger(__name__)

CROP_POINT_CATEGORY_ID = "8bb671f4-0eb7-4778-8262-3fc5445c6ea8"
PLANT_POINT_CATEGORY_ID = ""


def process_chunk(config: Config, data_blocks: List[DataBlock], gpu_index: int) -> None:  # noqa: C901
    try:

        LOG.info(f"Starting classification subprocess {gpu_index}")

        experiment_dir = f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}"
        classifications_filepath = f"{experiment_dir}/classifications.jsonl"
        detections_filepath = f"{experiment_dir}/detections.jsonl"

        if not config.augment_dataset:
            with open(classifications_filepath, "w") as f:
                f.write("")

            return None

        detection_point_id2image_id: Dict[str, str] = {}

        detections = load_detections(detections_filepath)

        model = get_model(config.classification_model_id)
        model = model.eval().to(f"cuda:{gpu_index}")

        for index, data_block in enumerate(data_blocks):
            labeled_points: Dict[str, List[Tuple[Datapoint, Point]]] = {}
            for datapoint in data_block.labeled_datapoints:
                for point in datapoint.points:
                    if point.point_category_id not in labeled_points:
                        labeled_points[point.point_category_id] = []

                    sample = (datapoint, point)

                    labeled_points[point.point_category_id].append(sample)

            # Shuffle the points
            for value in labeled_points.values():
                random.shuffle(value)

            support_set = []
            for _, points in labeled_points.items():
                for datapoint, point in points[: config.classification_num_examples]:
                    support_set.append((datapoint, point))

            query_set: List[Tuple[Detection, DetectionPoint]] = []

            for datapoint in data_block.unlabeled_datapoints:
                detection = detections.get(datapoint.image_id)

                if detection is None:
                    continue

                for detection_point in detection.points:

                    detection_point_id2image_id[detection_point.id] = detection.image_id

                    query_set.append((detection, detection_point))

            logging_info = f"{gpu_index=}, {data_block.crop_id=}, {data_block.date=}, {data_block.geohash=}"
            if len(support_set) == 0:
                LOG.info(f"Skipping data block due to no support points: {logging_info}")
                continue

            if len(query_set) == 0:
                LOG.info(f"Skipping data block due to no query points: {logging_info}")
                continue

            episode_dataset = EpisodeDataset(support_set, query_set)
            episode_sampler = EpisodeSampler(episode_dataset, batch_size=config.classification_batch_size)

            dataloader = torch.utils.data.DataLoader(
                episode_dataset,
                batch_sampler=episode_sampler,
                num_workers=config.classification_num_workers,
                collate_fn=episode_dataset.collate_fn,
            )

            for episode_index, episode in enumerate(dataloader):
                LOG.info(
                    f"Process {gpu_index} | Data block: {data_block.date} {data_block.geohash} {index} / {len(data_blocks)} | Episode: {episode_index + 1} / {len(dataloader)}"
                )
                episode.to(f"cuda:{gpu_index}")

                logits = model(episode)

                query_set_datapoints = [
                    datapoint
                    for datapoint in episode.datapoints
                    if datapoint.role == fewshot.dataset_types.DatapointRole.QUERY
                ]

                with open(classifications_filepath, "a") as f:
                    for datapoint_index, fewshot_datapoint in enumerate(query_set_datapoints):
                        logit = logits[datapoint_index]
                        logit[logit.isnan()] = logit[~logit.isnan()].min()
                        probability = (
                            torch.nn.functional.softmax(
                                logits[datapoint_index][~logits[datapoint_index].isnan()], dim=0
                            )
                            .max()
                            .item()
                        )

                        image_id = detection_point_id2image_id[fewshot_datapoint.identifier]
                        classification = Classification(
                            date=data_block.date.strftime("%Y-%m-%d"),
                            geohash=data_block.geohash,
                            detection_point_id=fewshot_datapoint.identifier,
                            point_category_id=episode.class_names[logit.argmax()],
                            probability=probability,
                            image_id=image_id,
                            certified_point_category_ids=episode.class_names,
                        )

                        f.write(classification.json() + "\n")

    except Exception:
        LOG.info(f"Classification runner {gpu_index} failed.")
        traceback.print_exc()


def generate_classifications(config: Config, dataset_id: str) -> None:
    dataset_dir = f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}"

    train_filepath = f"{dataset_dir}/train.jsonl"
    data_blocks: Dict[str, DataBlock] = {}
    with open(train_filepath, "r") as f:
        for line in f.readlines():
            datapoint = Datapoint.parse_raw(line)

            # NOTE: captured_at is a unix timestamp in ms
            date = datetime.datetime.fromtimestamp(datapoint.captured_at / 1000).date()
            geohash = datapoint.geohash[: config.classification_geohash_precision]

            key = f"{datapoint.crop_id}-{date}-{geohash}"

            if key not in data_blocks:
                data_blocks[key] = DataBlock(crop_id=datapoint.crop_id, date=date, geohash=geohash)

            if datapoint.label_id is None:
                data_blocks[key].unlabeled_datapoints.append(datapoint)
            else:
                data_blocks[key].labeled_datapoints.append(datapoint)

    data_blocks_list = list(data_blocks.values())

    num_gpus = 8
    chunk_size = len(data_blocks_list) // num_gpus

    download_model(config.classification_model_id)

    processes = []
    for gpu_index in range(num_gpus):
        chunk = data_blocks_list[gpu_index * chunk_size : (gpu_index + 1) * chunk_size]
        process = multiprocessing.Process(target=process_chunk, args=(config, chunk, gpu_index))
        process.daemon = False
        process.start()
        processes.append(process)

    for p in processes:
        p.join()

import datetime
import logging
from typing import List

import pydantic

import fewshot
from deeplearning.semi_supervised.types import Datapoint

LOG = logging.getLogger(__name__)

CROP_POINT_CATEGORY_ID = "8bb671f4-0eb7-4778-8262-3fc5445c6ea8"
PLANT_POINT_CATEGORY_ID = ""


class DataBlock(pydantic.BaseModel):
    crop_id: str
    date: datetime.date
    geohash: str
    unlabeled_datapoints: List[Datapoint] = []
    labeled_datapoints: List[Datapoint] = []


class FewshotIndex(pydantic.BaseModel):
    index: int
    role: fewshot.dataset_types.DatapointRole


class Crop(pydantic.BaseModel):
    crop_id: str
    crop_point_category_id: str

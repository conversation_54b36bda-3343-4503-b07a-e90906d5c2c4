from typing import Dict, List, Optional

from pydantic import BaseModel, Field

from deeplearning.semi_supervised.utils import generate_id


# Point made during detection process
class DetectionPoint(BaseModel):
    id: str = Field(default_factory=generate_id)  # TODO: Use UUID
    x: float
    y: float
    radius: float
    plant_score: float


# Annotation made during detection process
class Detection(BaseModel):
    id: str = Field(default_factory=generate_id)
    image_id: str
    model_id: str
    threshold: float
    predict_border: bool
    points: List[DetectionPoint]
    uri: str


# Point in dataset before or after semi-supervised augmentation
class Point(BaseModel):
    id: str
    x: float
    y: float
    radius: float
    point_category_id: str
    confidence: int


class Polygon(BaseModel):
    id: Optional[str]
    label: str
    confidence: int
    coordinates: List[List[Dict[str, float]]]


# Datapoint in dataset before or after semi-supervised augmentation
class Datapoint(BaseModel):
    image_id: str
    task_id: str
    label_id: Optional[str]
    detection_id: Optional[str]

    uri: str
    width: int
    height: int
    is_new: bool
    capture_session_id: Optional[str]
    crop_id: str
    robot_id: str
    captured_at: int
    geohash: str
    latitude: float
    longitude: float
    ppi: Optional[int]

    points: List[Point]
    polygons: List[Polygon]

    certified_point_category_ids: List[str] = Field(default_factory=list)


class Classification(BaseModel):
    date: str
    geohash: str
    detection_point_id: str
    point_category_id: str
    probability: float
    image_id: str
    certified_point_category_ids: List[str]

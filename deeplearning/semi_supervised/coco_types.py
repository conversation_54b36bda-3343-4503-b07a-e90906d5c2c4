from typing import List, Optional, Union

from pydantic import BaseModel


class COCOPointAnnotation(BaseModel):
    annotation_type: str = "point"
    image_id: str
    last_updated: int
    label_id: str
    category_id: int
    label: str
    x: float
    y: float
    radius: float
    confidence: int


class COCOPolygonAnnotation(BaseModel):
    annotation_type: str = "polygon"
    image_id: str
    last_updated: int
    label_id: str
    category_id: int
    label: str


class COCOImage(BaseModel):
    id: str
    label_id: Optional[str] = None
    detection_id: Optional[str] = None
    uri: str
    height: int
    width: int
    crop: Optional[str] = None
    crop_id: str
    captured_at: int
    city: Optional[str] = None
    robot_id: str
    latitude: float
    longitude: float
    is_new: bool
    session_name: Optional[str] = None
    point_categories: List[str]
    polygon_categories: List[str] = ["driptape"]
    geohash: str
    row_id: Optional[str]
    cam_id: Optional[str]
    date_group_id: Optional[str] = None


class COCOCategory(BaseModel):
    supercategory: str
    name: str
    id: int
    type: str


class COCOInfo(BaseModel):
    version: str
    split: str
    id: str
    name: str
    created: int


class COCODataset(BaseModel):
    info: COCOInfo
    licenses: List[str]
    images: List[COCOImage]
    annotations: List[Union[COCOPointAnnotation, COCOPolygonAnnotation]]
    categories: List[COCOCategory]

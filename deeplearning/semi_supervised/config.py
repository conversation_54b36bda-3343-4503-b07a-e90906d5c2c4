from typing import Optional

import pydantic


class Config(pydantic.BaseModel):

    # General parameters
    model_id: str
    pipeline_id: str
    dataset_id: Optional[str]
    augment_dataset: bool = True
    wandb_project: str = "semi-supervised-development"
    description: Optional[str] = None

    fast_run: bool = False

    # Detection parameters
    detection_run: bool = True
    detection_num_workers: int = 12
    detection_model_id: str
    detection_plant_low_confidence_threshold: float = 0.3
    detection_plant_high_confidence_threshold: float = 0.5

    # Classification parameters
    classification_run: bool = True
    classification_model_id: str
    classification_batch_size: int = 64
    classification_num_examples: int = 5
    classification_threshold: float = 0.5
    classification_num_workers: int = 4
    classification_geohash_precision: int = 6

    # Dataset compilation parameters
    dataset_compilation_run: bool = True

    # Deepweed parameters
    deepweed_train_run: bool = True
    deepweed_train_embeddings: bool = False

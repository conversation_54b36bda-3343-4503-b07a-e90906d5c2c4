import datetime
import logging
import os
from typing import Any, Dict, List, Optional

import pydantic
import torch
from lightning.pytorch.loggers import Wan<PERSON><PERSON><PERSON>ogger
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.remote_veselka_dataset import VeselkaDataset
from deeplearning.scripts.deepweed.spawn_utils import (
    EvaluateComparisonEmbeddingsArguments,
    EvaluateDeepweedEmbeddingsArguments,
    OptimizeAndTestArguments,
    PumapArguments,
    TrainAndUnoptimizedTestArguments,
    cleanup_spawn_script,
    evaluate_comparison_embeddings,
    evaluate_embeddings_on_new_data,
    get_upload_model_fn,
    load_pumap_head,
    optimize_and_test,
    test_pumap,
    train_and_unoptimized_test,
    train_pumap,
)
from deeplearning.scripts.utils.elastic_launcher import elastic_launcher
from deeplearning.scripts.utils.utils import (
    CONTAINER_VERSION,
    TrainingInfo,
    autodetect_driptape_model,
    autodetect_pretrained_model,
    generate_model_id,
    get_dataset_split_image_count,
    get_last_nth_timestamps,
    get_pipeline,
    get_segmentation_classes,
    pick_comparison_model_id,
    remove_files_from_dataset_json,
)
from deeplearning.semi_supervised.config import Config
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.trainer import Environment
from deeplearning.utils.wandb_logger import make_wandb_logger

LOG = logging.getLogger(__name__)


class Args(pydantic.BaseModel):
    job_id: str
    pipeline_id: str
    dataset_id: str

    production: bool = False
    preview: bool = False
    fast_run: bool = False
    tags: Optional[List[str]] = None
    pretrained_model: Optional[str] = None
    disable_crop: bool = False
    geohash_max_precision: int = 4
    geohash_min_precision: int = 4
    use_date_groups: bool = False
    nproc_per_node: int = 8
    resume_from: Optional[str] = None

    autodetect_pretrained_model: bool = False
    autodetect_driptape_model: bool = False
    dl_config: Dict[str, Any] = {}

    comparison_model_id: Optional[str] = None
    segmentation_model: Optional[str] = None
    description: Optional[str] = None
    train_embeddings: bool = False


@record
def main(config: Config, dataset_id: str) -> None:  # noqa: C901
    try:
        logger: Optional[WandbLogger] = None
        veselka_dataset: Optional[VeselkaDataset] = None

        args = Args(
            job_id=config.model_id,
            pipeline_id=config.pipeline_id,
            dataset_id=dataset_id,
            fast_run=config.fast_run,
            pretrained_model=config.detection_model_id,
            train_embeddings=config.deepweed_train_embeddings,
            description=config.description,
        )

        assert not (args.production and args.preview), "Run cannot be both production and preview"

        tags = ["full-training"]

        if args.tags is not None:
            tags += args.tags

        if args.pretrained_model == "" or args.pretrained_model == "none":
            args.pretrained_model = None

        if args.job_id is None:
            model_id = generate_model_id()
        else:
            model_id = args.job_id

        dl_config_dict = {
            "wandb_project": config.wandb_project,
            "num_samples": 64 if args.fast_run else 6000,
            "num_epochs": 1 if args.fast_run else 40,
            "pretrain_layer_prefix_skip_list": ["point_category_convs"],
            "fast_run": args.fast_run,
            "training_set_ratio": 1.0,
            "checkpoint_start_ratio": 0.8,
            "sampling_algorithm": "geohash-month-hit",
            "sampling_geohash_precision": 6,
            "model_id": model_id,
            **args.dl_config,
        }

        if args.preview and not args.fast_run:
            environment = Environment.PREVIEW
        elif args.production and not args.fast_run:
            environment = Environment.PRODUCTION
        else:
            environment = Environment.DEVELOPMENT

        if (args.production or args.preview) and not CONTAINER_VERSION:
            assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
        elif CONTAINER_VERSION:
            if not args.pretrained_model and args.autodetect_pretrained_model:
                args.pretrained_model = autodetect_pretrained_model(environment)

            if not args.segmentation_model and args.autodetect_driptape_model:
                args.segmentation_model = autodetect_driptape_model(min_test_oec=0.85, environment=environment)

        comparison_model_id = None
        training_info = None
        if args.pretrained_model is not None:
            training_info = TrainingInfo(args.pretrained_model)

        comparison_model_id = pick_comparison_model_id(
            args.comparison_model_id, training_info.comparison_model_id if training_info is not None else None,
        )
        assert comparison_model_id is not None
        dl_config_dict["comparison_model_id"] = comparison_model_id
        dl_config = DeepweedConfig.from_dict(dl_config_dict)

        pipeline = get_pipeline(args.pipeline_id)

        num_training_images = get_dataset_split_image_count(dataset_id, "train")

        if "disable_crop" in pipeline.custom_arguments:
            args.disable_crop = bool(pipeline.custom_arguments["disable_crop"])

        segmentation_training_info = None
        pretrained_segmentation_model = None
        if training_info is not None:
            pretrained_model = training_info.model_weights
            segmentation_training_info = training_info
        else:
            pretrained_model = None

        if args.segmentation_model is not None:
            segmentation_training_info = TrainingInfo(args.segmentation_model)

        if segmentation_training_info is not None:
            pretrained_segmentation_model = segmentation_training_info.best_model_weights
            test_segmentation_classes = get_segmentation_classes(segmentation_training_info)
        else:
            pretrained_segmentation_model = None
            test_segmentation_classes = None

        if args.description is None:
            description = f"(semi-supervised train) Development run {datetime.datetime.now()}"
        else:
            description = args.description

        crops = ["CROP"]
        weeds = ["BROADLEAF", "OFFSHOOT", "GRASS", "PURSLANE"]

        tags.append(pipeline.name.lower())
        if args.pretrained_model:
            tags.append("pretrained")

        wandb_config = {}

        if args.pretrained_model is not None:
            wandb_config["parent_model_id"] = args.pretrained_model
        if args.segmentation_model is not None:
            wandb_config["parent_segmentation_model_id"] = args.segmentation_model

        dataset_parameters = {
            "config": dl_config,
            "train_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json",
            "validation_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/validation.json",
            "test_filepath": f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/test.json",
            "num_samples": (
                max(int(num_training_images * dl_config.training_set_ratio), dl_config.num_samples)
                if dl_config.training_set_ratio is not None
                else dl_config.num_samples
            ),
            "crop_classes": tuple(crops),
            "weed_classes": tuple(weeds),
            "test_segm_classes": tuple(test_segmentation_classes) if test_segmentation_classes is not None else None,
            "train_ppi": 200,
            "geohash_max_precision": args.geohash_max_precision,
            "geohash_min_precision": args.geohash_min_precision,
            "use_date_groups": args.use_date_groups,
            "calibration_dataset_size": 16 if args.fast_run else 1024,
            "embedding_balancing_evaluation_path": f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dl_config.embedding_balancing_model}/image_point_embeddings"
            if dl_config.embedding_balancing_model is not None
            else None,
        }

        if dl_config.train_embeddings and dl_config.evaluate_new_comparison_data:
            elastic_launcher(
                args.nproc_per_node,
                evaluate_comparison_embeddings,
                get_upload_model_fn(model_id),
                EvaluateComparisonEmbeddingsArguments(
                    comparison_model_id=comparison_model_id,
                    environment=environment,
                    dataset_id=dataset_id,
                    fast_run=args.fast_run,
                ),
            )

        if dl_config.evaluate_embeddings_for_new_points:
            assert dl_config.embedding_balancing_model is not None, "Embedding model needs to be set"

            embedding_training_info = TrainingInfo(dl_config.embedding_balancing_model)
            eval_dir = f"{CARBON_DATA_DIR}/deeplearning/embeddings/{dl_config.embedding_balancing_model}"
            if os.path.exists(os.path.join(eval_dir, "test_dataframes/points_v2.db")):
                os.remove(os.path.join(eval_dir, "test_dataframes/points_v2.db"))

            old_files = set()

            for _, _, files in os.walk(eval_dir):
                for file in files:
                    if file.endswith(".pt"):
                        old_files.add(os.path.splitext(file)[0])

            # Remove files if they already exist in the embeddings directory
            embedding_model_eval_filepath, new_number_images = remove_files_from_dataset_json(
                f"{CARBON_DATA_DIR}/deeplearning/datasets/{dataset_id}/train.json", old_files, "id"
            )

            LOG.info(
                f"Skipping {len(old_files)} images from embedding extraction (this leaves {new_number_images} to evaluate)."
            )

            eval_config_dict = {
                "model_id": dl_config.embedding_balancing_model,
                **args.dl_config,
            }
            del eval_config_dict["embedding_balancing_model"]
            eval_config = DeepweedConfig.from_dict(eval_config_dict)

            elastic_launcher(
                args.nproc_per_node,
                evaluate_embeddings_on_new_data,
                get_upload_model_fn(model_id),
                EvaluateDeepweedEmbeddingsArguments(
                    eval_config=eval_config,
                    eval_json=embedding_model_eval_filepath,
                    crops=crops,
                    weeds=weeds,
                    embedding_training_info_obj=embedding_training_info,
                    eval_dir=eval_dir,
                    dataset_id=dataset_id,
                ),
            )

        dw_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/{model_id}")

        logger = make_wandb_logger(
            name=description, tags=safe_split(tags), project=dl_config.wandb_project, exp_dir=dw_experiment_dir,
        )
        assert logger is not None

        veselka_dataset = VeselkaDataset(**dataset_parameters)
        assert veselka_dataset is not None
        last_nth_timestamps = get_last_nth_timestamps(veselka_dataset.datasets.new_data_captured_ats)

        elastic_launcher(
            args.nproc_per_node,
            train_and_unoptimized_test,
            get_upload_model_fn(model_id),
            TrainAndUnoptimizedTestArguments(
                dl_config=dl_config,
                datasets=veselka_dataset.datasets,
                pretrained_model=pretrained_model,
                pretrained_segmentation_model=pretrained_segmentation_model,
                description=description,
                tags=tags,
                model_id=model_id,
                resume_from=args.resume_from,
                environment=environment,
                dataset_id=dataset_id,
                last_nth_timestamps=last_nth_timestamps,
                pipeline_id=args.pipeline_id,
                data_source_crop_ids=pipeline.data_source_crop_ids,
                wandb_config=wandb_config,
                parent_model_id=args.pretrained_model,
                logger=logger,
                sub_type="full_train",
            ),
        )

        pumap_experiment_dir = os.path.join(CARBON_DATA_DIR, f"deeplearning/models/pumap-{model_id}")
        if dl_config.train_embeddings:
            elastic_launcher(
                args.nproc_per_node,
                train_pumap,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_experiment_dir=dw_experiment_dir,
                    pumap_experiment_dir=pumap_experiment_dir,
                    dw_config=dl_config,
                    logger=logger,
                ),
            )
            elastic_launcher(
                args.nproc_per_node,
                test_pumap,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_experiment_dir=dw_experiment_dir, pumap_experiment_dir=pumap_experiment_dir, dw_config=dl_config,
                ),
            )
            elastic_launcher(
                args.nproc_per_node,
                load_pumap_head,
                get_upload_model_fn(model_id),
                PumapArguments(
                    dw_config=dl_config, dw_experiment_dir=dw_experiment_dir, pumap_experiment_dir=pumap_experiment_dir,
                ),
            )

        elastic_launcher(
            args.nproc_per_node,
            optimize_and_test,
            get_upload_model_fn(model_id),
            OptimizeAndTestArguments(
                dl_config=dl_config,
                datasets=veselka_dataset.datasets,
                pretrained_model=pretrained_model,
                pretrained_segmentation_model=pretrained_segmentation_model,
                description=description,
                tags=tags,
                model_id=model_id,
                dataset_id=dataset_id,
                last_nth_timestamps=last_nth_timestamps,
                data_source_crop_ids=pipeline.data_source_crop_ids,
                parent_model_id=args.pretrained_model,
                logger=logger,
                wandb_config=wandb_config,
            ),
        )
    except Exception as e:
        raise e
    finally:
        cleanup_spawn_script(logger, veselka_dataset)


def train_deepweed(config: Config, dataset_id: str) -> None:
    try:
        main(config, dataset_id)
    except:  # noqa
        if os.getenv("TRAINER_DEBUG"):
            if torch.distributed.get_rank() == 0:
                import pdb
                import sys
                import traceback

                extype, value, tb = sys.exc_info()
                traceback.print_exc()
                pdb.post_mortem(tb)
        else:
            raise

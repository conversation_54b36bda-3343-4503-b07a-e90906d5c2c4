import enum
import logging
import os
from typing import Any, Callable, Dict, List, Optional

from alembic import command
from alembic.config import Config
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    Date,
    Enum,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    PrimaryKeyConstraint,
    String,
    create_engine,
    event,
)
from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship

from .version import __version__

Base = declarative_base()


def run_alembic(db_path: str, callback: Callable[[Config], None]) -> None:
    script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "alembic"))
    cfg = Config()
    cfg.set_main_option("script_location", script_path)
    cfg.set_main_option("sqlalchemy.url", f"sqlite:///{db_path}")
    callback(cfg)


def get_db(db_path: str) -> Engine:
    if os.path.exists(db_path):
        run_alembic(db_path, lambda cfg: command.upgrade(cfg, "head"))

    engine = create_engine(f"sqlite:///{db_path}?_pragma=busy_timeout(5000)")
    Base.metadata.create_all(engine)
    run_alembic(db_path, lambda cfg: command.stamp(cfg, "head"))

    def _fk_pragma_on_connect(dbapi_connection: Engine, connection_record: Any) -> None:
        dbapi_connection.execute("pragma foreign_keys=ON")

    event.listen(engine, "connect", _fk_pragma_on_connect)

    return engine


def get_session(engine: Engine) -> Session:
    session = Session(engine)
    return session


class Image(Base):  # type: ignore
    __tablename__ = "images"

    id = Column(String, primary_key=True)
    filepath = Column(String, nullable=False, unique=True)
    date = Column(Date, nullable=False)
    timestamp_ms = Column(Integer, nullable=False)
    robot_id = Column(String, nullable=False)
    cam_id = Column(String, nullable=False)
    geohash = Column(String, nullable=True)

    metrics = relationship("ImageMetric", back_populates="image")

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})


class ImageMetric(Base):  # type: ignore
    __tablename__ = "image_metrics"
    image_id = Column(Integer, ForeignKey("images.id"), nullable=False)
    metric_name = Column(String, nullable=False)
    numerator = Column(Float, nullable=False)
    denominator = Column(Float, nullable=False)

    image = relationship("Image", back_populates="metrics")

    __table_args__ = (PrimaryKeyConstraint(image_id, metric_name),)

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


def get_image(session: Session, filepath: str) -> Optional[Image]:
    return session.query(Image).filter(Image.filepath == filepath).first()

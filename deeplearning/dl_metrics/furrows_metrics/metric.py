import copy
from datetime import date
from typing import Any, List, Optional, Set, Tuple

import pandas as pd
from sqlalchemy import func

from . import furrows_db


class Metric:
    def __init__(
        self, metric_names: List[str] = [], min_denom_value: Optional[int] = None, max_denom_value: Optional[int] = None
    ) -> None:
        self.filter_date_range: Optional[Tuple[date, date]] = None
        self.filter_robot_ids: Optional[Set[str]] = None
        self.filter_geohash_prefixes: Optional[Set[str]] = None
        self.filter_filepaths_to_exclude: Optional[Set[str]] = None
        self.filter_filepaths_to_include: Optional[Set[str]] = None
        self.group_by_date = False
        self.group_by_filepath = False
        self.group_by_robot_id = False
        self.group_by_geohash = False
        self.group_by_image_id = False
        self._metric_names = metric_names
        self._min_denom_value = min_denom_value
        self._max_denom_value = max_denom_value
        self.name = ", ".join(metric_names)

    def filter(
        self,
        date_range: Optional[Tuple[date, date]] = None,
        robot_ids: Optional[Set[str]] = None,
        geohash_prefixes: Optional[Set[str]] = None,
        filepaths_to_exclude: Optional[Set[str]] = None,
        filepaths_to_include: Optional[Set[str]] = None,
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric.filter_date_range = date_range
        new_metric.filter_robot_ids = robot_ids
        new_metric.filter_geohash_prefixes = geohash_prefixes
        new_metric.filter_filepaths_to_exclude = filepaths_to_exclude
        new_metric.filter_filepaths_to_include = filepaths_to_include
        return new_metric

    def group_by(
        self,
        date: bool = False,
        filepath: bool = False,
        robot_id: bool = False,
        geohash: bool = False,
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric.group_by_date = date
        new_metric.group_by_filepath = filepath
        new_metric.group_by_robot_id = robot_id
        new_metric.group_by_geohash = geohash
        return new_metric

    def _build_group_by_entities_and_labels(self) -> Tuple[List[Any], List[Any]]:
        group_by_entities = []
        group_by_labels = []
        if self.group_by_date:
            group_by_entities.append(furrows_db.Image.date.label("date"))
            group_by_labels.append("date")
        if self.group_by_filepath:
            group_by_entities.append(furrows_db.Image.filepath.label("filepath"))
            group_by_labels.append("filepath")
        if self.group_by_robot_id:
            group_by_entities.append(furrows_db.Image.robot_id.label("robot_id"))
            group_by_labels.append("robot_id")
        if self.group_by_geohash:
            group_by_entities.append(furrows_db.Image.geohash.label("geohash"))
            group_by_labels.append("geohash")

        return group_by_entities, group_by_labels

    def __call__(self, session: furrows_db.Session) -> pd.DataFrame:
        group_by_entities, group_by_labels = self._build_group_by_entities_and_labels()

        query = (
            session.query(
                *group_by_entities,
                func.sum(furrows_db.ImageMetric.numerator).label("numerator"),
                func.sum(furrows_db.ImageMetric.denominator).label("denominator"),
            )
            .select_from(furrows_db.ImageMetric)
            .join(furrows_db.Image, furrows_db.ImageMetric.image_id == furrows_db.Image.id)
        )

        if len(self._metric_names) > 0:
            query = query.filter(furrows_db.ImageMetric.metric_name.in_(self._metric_names))

        if self._min_denom_value is not None:
            query = query.filter(furrows_db.ImageMetric.denominator >= self._min_denom_value)
        if self._max_denom_value is not None:
            query = query.filter(furrows_db.ImageMetric.denominator <= self._max_denom_value)

        query = query.group_by(furrows_db.ImageMetric.metric_name).group_by(*group_by_labels)

        return pd.read_sql_query(query.statement, session.bind)

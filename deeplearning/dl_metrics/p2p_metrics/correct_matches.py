import pandas as pd
from sqlalchemy import null, or_

from . import defaults
from .metric import Metric
from .p2p_db import Match, Session


class CorrectMatches(Metric):
    def __init__(
        self,
        distance_threshold_px: float = defaults.DISTANCE_THRESHOLD_PX,
    ) -> None:
        super().__init__(
            distance_threshold_px=distance_threshold_px,
            name=f"Correct Matches",
            description=f"True positives or true negatives",
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        correct_match_filters = [
            Match.positive_match == Match.positive_match_hat,
            or_(Match.distance == null(), Match.distance <= self.distance_threshold_px),
        ]

        match_filters = []

        return self._query_points(session=session, match_filters=match_filters, numerator_filters=correct_match_filters)

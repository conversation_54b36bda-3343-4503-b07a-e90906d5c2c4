import pandas as pd
from sqlalchemy import false, null, or_, true

from . import defaults
from .metric import Metric
from .p2p_db import Match, Session


class FalseMatches(Metric):
    def __init__(
        self,
        distance_threshold_px: float = defaults.DISTANCE_THRESHOLD_PX,
    ) -> None:
        super().__init__(
            distance_threshold_px=distance_threshold_px,
            name=f"False Matches",
            description=f"False positives",
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        correct_match_filters = [
            Match.positive_match_hat == true(),
        ]

        match_filters = [
            Match.positive_match == false(),
        ]

        return self._query_points(session=session, match_filters=match_filters, numerator_filters=correct_match_filters)

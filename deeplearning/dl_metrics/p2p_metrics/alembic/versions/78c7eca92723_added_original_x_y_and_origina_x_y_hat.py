"""added original x/y and origina x/y hat

Revision ID: 78c7eca92723
Revises: 
Create Date: 2025-03-18 21:20:41.090335

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "78c7eca92723"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("matches", sa.Column("original_x", sa.Integer(), nullable=True))
    op.add_column("matches", sa.Column("original_y", sa.Integer(), nullable=True))
    op.add_column("matches", sa.Column("original_x_hat", sa.Integer(), nullable=True))
    op.add_column("matches", sa.Column("original_y_hat", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("matches", "original_y_hat")
    op.drop_column("matches", "original_x_hat")
    op.drop_column("matches", "original_y")
    op.drop_column("matches", "original_x")
    # ### end Alembic commands ###

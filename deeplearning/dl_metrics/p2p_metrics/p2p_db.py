import enum
import logging
import os
from typing import Any, Callable, Dict, List, Optional

from alembic import command
from alembic.config import Config
from sqlalchemy import (
    JSO<PERSON>,
    Boolean,
    Column,
    Date,
    Enum,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    PrimaryKeyConstraint,
    String,
    create_engine,
    event,
)
from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship

from .version import __version__

logging.getLogger("alembic.runtime.migration").setLevel(logging.WARN)

__all__ = [
    "Engine",
    "Image",
    "Session",
    "get_db",
    "get_session",
    "__version__",
]

Base = declarative_base()


def run_alembic(db_path: str, callback: Callable[[Config], None]) -> None:
    script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "alembic"))
    cfg = Config()
    cfg.set_main_option("script_location", script_path)
    cfg.set_main_option("sqlalchemy.url", f"sqlite:///{db_path}")
    callback(cfg)


def get_db(db_path: str) -> Engine:
    if os.path.exists(db_path):
        run_alembic(db_path, lambda cfg: command.upgrade(cfg, "head"))

    engine = create_engine(f"sqlite:///{db_path}?_pragma=busy_timeout(5000)")
    Base.metadata.create_all(engine)
    run_alembic(db_path, lambda cfg: command.stamp(cfg, "head"))

    def _fk_pragma_on_connect(dbapi_connection: Engine, connection_record: Any) -> None:
        dbapi_connection.execute("pragma foreign_keys=ON")

    event.listen(engine, "connect", _fk_pragma_on_connect)

    return engine


def get_session(engine: Engine) -> Session:
    session = Session(engine)
    return session


class Image(Base):  # type: ignore
    __tablename__ = "images"

    id = Column(String, primary_key=True)
    filepath = Column(String, nullable=False, unique=True)
    width = Column(Integer, nullable=False)
    height = Column(Integer, nullable=False)
    ppi = Column(Float, nullable=False)
    crop_id = Column(String, nullable=True)
    date = Column(Date, nullable=False)
    timestamp_ms = Column(Integer, nullable=False)
    robot_id = Column(String, nullable=False)
    row_id = Column(String, nullable=False)
    cam_id = Column(String, nullable=False)
    geo = Column(JSON, nullable=False)
    geohash = Column(String, nullable=True)

    matches = relationship("Match", back_populates="image")

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})


class Match(Base):
    __tablename__ = "matches"

    id = Column(String, primary_key=True)
    image_id = Column(Integer, ForeignKey("images.id"), nullable=False)
    distance = Column(Float, nullable=True)

    x = Column(Float, nullable=True)
    y = Column(Float, nullable=True)
    positive_match = Column(Boolean)

    x_hat = Column(Float, nullable=True)
    y_hat = Column(Float, nullable=True)
    positive_match_hat = Column(Boolean)

    original_x = Column(Integer, nullable=True)
    original_y = Column(Integer, nullable=True)
    original_x_hat = Column(Integer, nullable=True)
    original_y_hat = Column(Integer, nullable=True)

    image = relationship("Image", back_populates="matches")

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})


def get_image(session: Session, filepath: str) -> Optional[Image]:
    return session.query(Image).filter(Image.filepath == filepath).first()

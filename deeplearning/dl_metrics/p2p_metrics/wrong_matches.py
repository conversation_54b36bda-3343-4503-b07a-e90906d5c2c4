import pandas as pd
from sqlalchemy import false, null, or_, true

from . import defaults
from .metric import Metric
from .p2p_db import Match, Session


class WrongMatches(Metric):
    def __init__(
        self,
        distance_threshold_px: float = defaults.DISTANCE_THRESHOLD_PX,
    ) -> None:
        super().__init__(
            distance_threshold_px=distance_threshold_px,
            name=f"Wrong Matches",
            description=f"True positives matched to a location above distance threshold",
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        correct_match_filters = [
            Match.distance > self.distance_threshold_px,
        ]

        match_filters = [
            Match.positive_match == true(),
            Match.positive_match_hat == true(),
        ]

        return self._query_points(session=session, match_filters=match_filters, numerator_filters=correct_match_filters)

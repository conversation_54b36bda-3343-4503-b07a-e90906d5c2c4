import copy
from abc import ABC, abstractmethod
from datetime import date
from typing import Any, List, Optional, Set, Tuple

import pandas as pd
from sqlalchemy import Integer, and_, cast, func, or_

from . import defaults
from .p2p_db import Image, Match, Session


class Metric(ABC):
    def __init__(
        self,
        distance_threshold_px: float = defaults.DISTANCE_THRESHOLD_PX,
        name: str = "Metric",
        description: str = "",
    ) -> None:
        self.distance_threshold_px = distance_threshold_px
        self.filter_date_range: Optional[Tuple[date, date]] = None
        self.filter_robot_ids: Optional[Set[str]] = None
        self.filter_crops: Optional[Set[str]] = None
        self.filter_geohash_prefixes: Optional[Set[str]] = None
        self.filter_filepaths_to_exclude: Optional[Set[str]] = None
        self.filter_filepaths_to_include: Optional[Set[str]] = None
        self.group_by_date = False
        self.group_by_filepath = False
        self.group_by_robot_id = False
        self.group_by_crop_id = False
        self.group_by_geohash = False
        self.group_by_image_id = False
        self.group_by_points = False
        self.name = name
        self.description = description

    @abstractmethod
    def __call__(self, session: Session) -> pd.DataFrame:
        """Returns numerators and denominators of metric as well as group by columns."""
        pass

    def docs(self) -> Optional[str]:
        return Metric.__init__.__doc__

    def filter(
        self,
        date_range: Optional[Tuple[date, date]] = None,
        robot_ids: Optional[Set[str]] = None,
        crops: Optional[Set[str]] = None,
        geohash_prefixes: Optional[Set[str]] = None,
        filepaths_to_exclude: Optional[Set[str]] = None,
        filepaths_to_include: Optional[Set[str]] = None,
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric.filter_date_range = date_range
        new_metric.filter_robot_ids = robot_ids
        new_metric.filter_crops = crops
        new_metric.filter_geohash_prefixes = geohash_prefixes
        new_metric.filter_filepaths_to_exclude = filepaths_to_exclude
        new_metric.filter_filepaths_to_include = filepaths_to_include
        return new_metric

    def group_by(
        self,
        date: bool = False,
        filepath: bool = False,
        robot_id: bool = False,
        crop_id: bool = False,
        geohash: bool = False,
        image_id: bool = False,
        point: bool = False,
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric.group_by_date = date
        new_metric.group_by_filepath = filepath
        new_metric.group_by_robot_id = robot_id
        new_metric.group_by_crop_id = crop_id
        new_metric.group_by_geohash = geohash
        new_metric.group_by_image_id = image_id
        new_metric.group_by_points = point
        return new_metric

    def _query_points(  # noqa: C901
        self, session: Session, match_filters: List[Any], numerator_filters: List[Any]
    ) -> pd.DataFrame:
        group_by_entities = []
        group_by_labels = []
        if self.group_by_date:
            group_by_entities.append(Image.date.label("date"))
            group_by_labels.append("date")
        if self.group_by_filepath:
            group_by_entities.append(Image.filepath.label("filepath"))
            group_by_labels.append("filepath")
        if self.group_by_robot_id:
            group_by_entities.append(Image.robot_id.label("robot_id"))
            group_by_labels.append("robot_id")
        if self.group_by_crop_id:
            group_by_entities.append(Image.crop_id.label("crop_id"))
            group_by_labels.append("crop_id")
        if self.group_by_geohash:
            group_by_entities.append(Image.geohash.label("geohash"))
            group_by_labels.append("geohash")
        if self.group_by_image_id:
            group_by_entities.append(Image.id.label("image_id"))
            group_by_labels.append("image_id")
        if self.group_by_points:
            group_by_entities.extend(
                [
                    Match.original_x.label("original_x"),
                    Match.original_y.label("original_y"),
                    Match.positive_match.label("positive_match"),
                    Match.original_x_hat.label("original_x_hat"),
                    Match.original_y_hat.label("original_y_hat"),
                    Match.positive_match_hat.label("positive_match_hat"),
                ]
            )
            group_by_labels.extend(
                ["original_x", "original_y", "positive_match", "original_x_hat", "original_y_hat", "positive_match_hat"]
            )

        image_filters = []
        if self.filter_date_range:
            image_filters.append(Image.date >= self.filter_date_range[0])
            image_filters.append(Image.date <= self.filter_date_range[1])
        if self.filter_robot_ids:
            image_filters.append(Image.robot_id.in_(self.filter_robot_ids))
        if self.filter_crops:
            image_filters.append(Image.crop_id.in_(self.filter_crops))
        if self.filter_geohash_prefixes:
            image_filters.append(or_(*[Image.geohash.startswith(gh) for gh in self.filter_geohash_prefixes]))
        if self.filter_filepaths_to_include:
            image_filters.append(Image.filepath.in_(self.filter_filepaths_to_include))
        if self.filter_filepaths_to_exclude:
            image_filters.append(Image.filepath.notin_(self.filter_filepaths_to_exclude))

        query = (
            session.query(
                *group_by_entities,
                func.sum(cast(and_(*numerator_filters), Integer)).label("numerator"),
                func.count(Match.id).label("denominator"),
            )
            .select_from(Match)
            .join(Match.image)
            .filter(*image_filters)
            .filter(*match_filters)
            .group_by(*group_by_labels)
        )
        return pd.read_sql_query(query.statement, session.bind)

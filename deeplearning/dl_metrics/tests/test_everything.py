# Silly but useful test that basically runs through all operations

import os
import shutil
import sys

import boto3
import pandas

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

import dl_metrics  # noqa # isort:skip


def test_df_conversion_and_metric_initialization():
    model_id = "fut-20230113-a11gx1ijvd"
    tmp_filepath = f"/tmp/dl-metrics/models/{model_id}"
    tmp_database = f"/tmp/dl-metrics/test_{dl_metrics.__version__}.db"
    if os.path.exists(tmp_filepath):
        shutil.rmtree(tmp_filepath)

    if os.path.exists(tmp_database):
        os.remove(tmp_database)
    os.makedirs(f"{tmp_filepath}/test_dataframes")

    boto3.client("s3").download_file(
        "maka-pono",
        f"models/{model_id}/test_dataframes/dataframe.csv",
        f"{tmp_filepath}/test_dataframes/dataframe.csv",
    )

    df = pandas.read_csv(f"/tmp/dl-metrics/models/{model_id}/test_dataframes/dataframe.csv")
    db = dl_metrics.get_db(tmp_database)
    session = dl_metrics.get_session(db)
    dl_metrics.convert_df(df, session)

    for MetricClass in dl_metrics.metrics_list:
        results = MetricClass()(session)

        print(results)

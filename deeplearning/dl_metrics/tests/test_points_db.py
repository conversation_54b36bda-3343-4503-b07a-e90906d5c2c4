from datetime import date
import os
import shutil
import sys

import pytest

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from sqlalchemy import and_
from sqlalchemy.exc import IntegrityError

from dl_metrics.points_db import (  # noqa # isort:skip
    Embedding,
    EmbeddingType,
    HitClass,
    Image,
    ImageMetric,
    Point,
    PointPair,
    PointType,
    copy_db,
    get_db,
    get_session,
    simple_copy_db,
)

EPS = 1e-5


def test_simple_copy_db():
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db_source = "/tmp/test_dbs/test_source_points.db"
    test_db_dest = "/tmp/test_dbs/test_dest_points.db"
    
    image = Image(
        id=1,
        filepath="123.png",
        width=4096,
        height=3000,
        ppi=123,
        crop="abc",
        crop_id="id_abc",
        robot_id="robot_1",
        date=date.today(),
        timestamp_ms=12,
        row_id="1",
        cam_id="1",
        session_name="123",
        geo={},
        crop_count=1,
        weed_count=2,
        geohash="abc"
    )
    
    point1 = Point(
        id=2,
        image_id=1,
        x=10,
        y=20,
        score=0.123,
        type=PointType.PREDICTION,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=1.0,
        weed_score=0.5,
        crop_score=0.3,
        uuid="point-2"
    )
    
    point2 = Point(
        id=3,
        image_id=1,
        x=5,
        y=7,
        score=0.989,
        type=PointType.LABEL,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=0.2,
        weed_score=0.6,
        crop_score=0.2,
        uuid="point-3"
    )
    
    point_pair = PointPair(
        point1_id = 2,
        point2_id = 3,
        distance_mm=10.0
    )
    
    embedding1 = Embedding(
        point_id=2,
        embedding=[1, 2, 3, 4],
        type=EmbeddingType.FULL
    )
    
    image_metric1 = ImageMetric(
        image_id=1,
        metric_name="metric_abcd",
        numerator=1,
        denominator=9,
        x_origin=22,
        y_origin=33,
        width=42,
        height=53,
        whole_image=False
    )
    
    with get_session(get_db(test_db_dest)) as dest_sess:
        dest_sess.bulk_save_objects([image, point1, point2, point_pair, embedding1, image_metric1])
        dest_sess.commit()
        
    image = Image(
        id=55,
        filepath="245.png",
        width=4096,
        height=3000,
        ppi=245,
        crop="bcd",
        crop_id="id_bcd",
        robot_id="robot_2",
        date=date.today(),
        timestamp_ms=12,
        row_id="1",
        cam_id="1",
        session_name="123",
        geo={},
        crop_count=1,
        weed_count=2,
        geohash="abc"
    )
    
    point1 = Point(
        id=56,
        image_id=55,
        x=110,
        y=120,
        score=0.456,
        type=PointType.PREDICTION,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=0.99,
        weed_score=0.88,
        crop_score=0.77,
        uuid="point-56",
    )
    
    point2 = Point(
        id=57,
        image_id=55,
        x=15,
        y=17,
        score=0.878,
        type=PointType.LABEL,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=0.33,
        weed_score=0.22,
        crop_score=0.11,
        uuid="point-57",
    )
    
    point_pair = PointPair(
        point1_id = 56,
        point2_id = 57,
        distance_mm=15.0
    )
    
    embedding1 = Embedding(
        point_id=56,
        embedding=[12,23,34,45],
        type=EmbeddingType.FULL
    )
    
    image_metric1 = ImageMetric(
        image_id=55,
        metric_name="metric_qwas",
        numerator=3,
        denominator=7,
        x_origin=29,
        y_origin=39,
        width=49,
        height=59,
        whole_image=False
    )
    
    with get_session(get_db(test_db_source)) as source_sess:
        source_sess.bulk_save_objects([image, point1, point2, point_pair, embedding1, image_metric1])
        source_sess.commit()
        
    simple_copy_db(test_db_source, test_db_dest)
    
    with get_session(get_db(test_db_dest)) as sess:
        images = sess.query(Image).all()
        points = sess.query(Point).all()
        point_pairs = sess.query(PointPair).all()
        embeddings = sess.query(Embedding).all()
        image_metrics = sess.query(ImageMetric).all()
        
        # Overall consistency
        assert len(images) == 2
        assert len(points) == 4
        assert len(point_pairs) == 2
        assert len(embeddings) == 2
        assert len(image_metrics) == 2
        
        # Image consistency
        ids = [im.id for im in images]
        assert sorted(ids) == [1, 55]
        
        # Points consistency
        points_for_1 = sess.query(Point).filter(Point.image_id==1).order_by(Point.id).all()
        points_for_55 = sess.query(Point).filter(Point.image_id==55).order_by(Point.id).all()
        
        assert len(points_for_1) == 2
        assert len(points_for_55) == 2
        assert points_for_1[0].uuid == "point-2"
        assert points_for_1[1].uuid == "point-3"
        
        assert points_for_1[0].id == 2
        assert points_for_1[1].id == 3
        
        assert abs(points_for_1[0].score - 0.123) < EPS
        assert abs(points_for_1[1].score - 0.989) < EPS
        assert points_for_1[0].plant_score == 1.0
        assert points_for_1[0].weed_score == 0.5
        assert points_for_1[0].crop_score == 0.3
        assert points_for_1[1].plant_score == 0.2
        assert points_for_1[1].weed_score == 0.6
        assert points_for_1[1].crop_score == 0.2
        
        
        assert points_for_55[0].uuid == "point-56"
        assert points_for_55[1].uuid == "point-57"
        
        assert points_for_55[0].id == 56
        assert points_for_55[1].id == 57
        
        assert abs(points_for_55[0].score - 0.456) < EPS
        assert abs(points_for_55[1].score - 0.878) < EPS
        assert points_for_55[0].plant_score == 0.99
        assert points_for_55[0].weed_score == 0.88
        assert points_for_55[0].crop_score == 0.77
        assert points_for_55[1].plant_score == 0.33
        assert points_for_55[1].weed_score == 0.22
        assert points_for_55[1].crop_score == 0.11
        
        # PointPair consistency
        point_pair_for_first = sess.query(PointPair).filter(and_(PointPair.point1_id==2, PointPair.point2_id==3)).all()
        point_pair_for_second = sess.query(PointPair).filter(and_(PointPair.point1_id==56, PointPair.point2_id==57)).all()
        
        assert len(point_pair_for_first) == 1
        assert len(point_pair_for_second) == 1
        
        assert abs(point_pair_for_first[0].distance_mm - 10.0) < EPS
        assert abs(point_pair_for_second[0].distance_mm - 15.0) < EPS
        
        # Embedding consistency
        embedding_for_first_point = sess.query(Embedding).filter(Embedding.point_id==2).all()
        embedding_for_second_point = sess.query(Embedding).filter(Embedding.point_id==56).all()
                    
        assert len(embedding_for_first_point) == 1
        assert len(embedding_for_second_point) == 1
        
        assert embedding_for_first_point[0].embedding == [1,2,3,4]
        assert embedding_for_second_point[0].embedding == [12,23,34,45]
        
        # Image Metric
        image_metric_for_first = sess.query(ImageMetric).filter(ImageMetric.image_id==1).all()
        image_metric_for_second = sess.query(ImageMetric).filter(ImageMetric.image_id==55).all()
        
        assert len(image_metric_for_first) == 1
        assert len(image_metric_for_second) == 1
        
        assert abs(image_metric_for_first[0].numerator - 1.) < EPS and abs(image_metric_for_first[0].denominator - 9.) < EPS and image_metric_for_first[0].metric_name == "metric_abcd"
        assert abs(image_metric_for_second[0].numerator - 3.) < EPS and abs(image_metric_for_second[0].denominator - 7.) < EPS and image_metric_for_second[0].metric_name == "metric_qwas"
        assert image_metric_for_first[0].x_origin == 22
        assert image_metric_for_first[0].y_origin == 33
        assert image_metric_for_first[0].width == 42
        assert image_metric_for_first[0].height == 53
        assert image_metric_for_first[0].whole_image == False
        
        assert image_metric_for_second[0].x_origin == 29
        assert image_metric_for_second[0].y_origin == 39
        assert image_metric_for_second[0].width == 49
        assert image_metric_for_second[0].height == 59
        assert image_metric_for_second[0].whole_image == False
        
        
    shutil.rmtree("/tmp/test_dbs")


def test_copy_db():
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db_source = "/tmp/test_dbs/test_source_points.db"
    test_db_dest = "/tmp/test_dbs/test_dest_points.db"

    image = Image(
        id=1,
        filepath="123.png",
        width=4096,
        height=3000,
        ppi=123,
        crop="abc",
        crop_id="id_abc",
        robot_id="robot_1",
        date=date.today(),
        timestamp_ms=12,
        row_id="1",
        cam_id="1",
        session_name="123",
        geo={},
        crop_count=1,
        weed_count=2,
        geohash="abc",
    )

    point1 = Point(
        id=2,
        image_id=1,
        x=10,
        y=20,
        score=0.123,
        type=PointType.PREDICTION,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=1.0,
        weed_score=0.5,
        crop_score=0.3,
        uuid="point-2",
    )

    point2 = Point(
        id=3,
        image_id=1,
        x=5,
        y=7,
        score=0.989,
        type=PointType.LABEL,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=0.2,
        weed_score=0.6,
        crop_score=0.2,
        uuid="point-3",
    )

    point_pair = PointPair(point1_id=2, point2_id=3, distance_mm=10.0)

    embedding1 = Embedding(point_id=2, embedding=[1, 2, 3, 4], type=EmbeddingType.FULL)

    image_metric1 = ImageMetric(
        image_id=1,
        metric_name="metric_abcd",
        numerator=1,
        denominator=9,
        x_origin=22,
        y_origin=33,
        width=42,
        height=53,
        whole_image=False,
    )

    with get_session(get_db(test_db_dest)) as dest_sess:
        dest_sess.bulk_save_objects([image, point1, point2, point_pair, embedding1, image_metric1])
        dest_sess.commit()

    image = Image(
        id=55,
        filepath="245.png",
        width=4096,
        height=3000,
        ppi=245,
        crop="bcd",
        crop_id="id_bcd",
        robot_id="robot_2",
        date=date.today(),
        timestamp_ms=12,
        row_id="1",
        cam_id="1",
        session_name="123",
        geo={},
        crop_count=1,
        weed_count=2,
        geohash="abc",
    )

    point1 = Point(
        id=56,
        image_id=55,
        x=110,
        y=120,
        score=0.456,
        type=PointType.PREDICTION,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=0.99,
        weed_score=0.88,
        crop_score=0.77,
        uuid="point-56",
    )

    point2 = Point(
        id=57,
        image_id=55,
        x=15,
        y=17,
        score=0.878,
        type=PointType.LABEL,
        r_mm=12,
        category_class_name="asdf",
        confidence=2,
        hit_class=HitClass.WEED,
        category_scores={},
        plant_score=0.33,
        weed_score=0.22,
        crop_score=0.11,
        uuid="point-57",
    )

    point_pair = PointPair(point1_id=56, point2_id=57, distance_mm=15.0)

    embedding1 = Embedding(point_id=56, embedding=[12, 23, 34, 45], type=EmbeddingType.FULL)

    image_metric1 = ImageMetric(
        image_id=55,
        metric_name="metric_qwas",
        numerator=3,
        denominator=7,
        x_origin=29,
        y_origin=39,
        width=49,
        height=59,
        whole_image=False,
    )

    with get_session(get_db(test_db_source)) as source_sess:
        source_sess.bulk_save_objects([image, point1, point2, point_pair, embedding1, image_metric1])
        source_sess.commit()

    copy_db(get_session(get_db(test_db_source)), get_session(get_db(test_db_dest)))

    with get_session(get_db(test_db_dest)) as sess:
        images = sess.query(Image).all()
        points = sess.query(Point).all()
        point_pairs = sess.query(PointPair).all()
        embeddings = sess.query(Embedding).all()
        image_metrics = sess.query(ImageMetric).all()

        # Overall consistency
        assert len(images) == 2
        assert len(points) == 4
        assert len(point_pairs) == 2
        assert len(embeddings) == 2
        assert len(image_metrics) == 2

        # Image consistency
        ids = [im.id for im in images]
        assert sorted(ids) == [
            1,
            4,
        ]  # 1 for the original image in the db, 4 for the new image (max(image_id, point_id) + 1)

        # Points consistency
        points_for_1 = sess.query(Point).filter(Point.image_id == 1).order_by(Point.id).all()
        points_for_4 = sess.query(Point).filter(Point.image_id == 4).order_by(Point.id).all()

        assert len(points_for_1) == 2
        assert len(points_for_4) == 2
        assert points_for_1[0].uuid == "point-2"
        assert points_for_1[1].uuid == "point-3"

        assert points_for_1[0].id == 2
        assert points_for_1[1].id == 3

        assert abs(points_for_1[0].score - 0.123) < EPS
        assert abs(points_for_1[1].score - 0.989) < EPS
        assert points_for_1[0].plant_score == 1.0
        assert points_for_1[0].weed_score == 0.5
        assert points_for_1[0].crop_score == 0.3
        assert points_for_1[1].plant_score == 0.2
        assert points_for_1[1].weed_score == 0.6
        assert points_for_1[1].crop_score == 0.2

        assert points_for_4[0].uuid == "point-56"
        assert points_for_4[1].uuid == "point-57"

        assert points_for_4[0].id == 5
        assert points_for_4[1].id == 6

        assert abs(points_for_4[0].score - 0.456) < EPS
        assert abs(points_for_4[1].score - 0.878) < EPS
        assert points_for_4[0].plant_score == 0.99
        assert points_for_4[0].weed_score == 0.88
        assert points_for_4[0].crop_score == 0.77
        assert points_for_4[1].plant_score == 0.33
        assert points_for_4[1].weed_score == 0.22
        assert points_for_4[1].crop_score == 0.11

        # PointPair consistency
        point_pair_for_first = (
            sess.query(PointPair).filter(and_(PointPair.point1_id == 2, PointPair.point2_id == 3)).all()
        )
        point_pair_for_second = (
            sess.query(PointPair).filter(and_(PointPair.point1_id == 5, PointPair.point2_id == 6)).all()
        )

        assert len(point_pair_for_first) == 1
        assert len(point_pair_for_second) == 1

        assert abs(point_pair_for_first[0].distance_mm - 10.0) < EPS
        assert abs(point_pair_for_second[0].distance_mm - 15.0) < EPS

        # Embedding consistency
        embedding_for_first_point = (
            sess.query(Embedding).filter(Embedding.point_id == 2).all()
        )  # 2 is the new id for the first point in the dest db
        embedding_for_second_point = (
            sess.query(Embedding).filter(Embedding.point_id == 5).all()
        )  # 5 is the new id for the first point from the source db in the dest db

        assert len(embedding_for_first_point) == 1
        assert len(embedding_for_second_point) == 1

        assert embedding_for_first_point[0].embedding == [1, 2, 3, 4]
        assert embedding_for_second_point[0].embedding == [12, 23, 34, 45]

        # Image Metric
        image_metric_for_first = sess.query(ImageMetric).filter(ImageMetric.image_id == 1).all()
        image_metric_for_second = sess.query(ImageMetric).filter(ImageMetric.image_id == 4).all()

        assert len(image_metric_for_first) == 1
        assert len(image_metric_for_second) == 1

        assert (
            abs(image_metric_for_first[0].numerator - 1.0) < EPS
            and abs(image_metric_for_first[0].denominator - 9.0) < EPS
            and image_metric_for_first[0].metric_name == "metric_abcd"
        )
        assert (
            abs(image_metric_for_second[0].numerator - 3.0) < EPS
            and abs(image_metric_for_second[0].denominator - 7.0) < EPS
            and image_metric_for_second[0].metric_name == "metric_qwas"
        )
        assert image_metric_for_first[0].x_origin == 22
        assert image_metric_for_first[0].y_origin == 33
        assert image_metric_for_first[0].width == 42
        assert image_metric_for_first[0].height == 53
        assert image_metric_for_first[0].whole_image == False

        assert image_metric_for_second[0].x_origin == 29
        assert image_metric_for_second[0].y_origin == 39
        assert image_metric_for_second[0].width == 49
        assert image_metric_for_second[0].height == 59
        assert image_metric_for_second[0].whole_image == False

    shutil.rmtree("/tmp/test_dbs")


def test_foreign_key_constraint() -> None:
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db = "/tmp/test_dbs/test.db"

    with get_session(get_db(test_db)) as session:
        image = Image(
            id=1,
            filepath="123.png",
            width=4096,
            height=3000,
            ppi=123,
            crop="abc",
            crop_id="id_abc",
            robot_id="robot_1",
            date=date.today(),
            timestamp_ms=12,
            row_id="1",
            cam_id="1",
            session_name="123",
            geo={},
            crop_count=1,
            weed_count=2,
            geohash="abc",
        )

        point1 = Point(
            id=2,
            image_id=1,
            x=10,
            y=20,
            score=0.123,
            type=PointType.PREDICTION,
            r_mm=12,
            category_class_name="asdf",
            confidence=2,
            hit_class=HitClass.WEED,
            category_scores={},
            plant_score=1.0,
            weed_score=0.5,
            crop_score=0.3,
            uuid="point-2",
        )

        point2 = Point(
            id=3,
            image_id=1,
            x=5,
            y=7,
            score=0.989,
            type=PointType.LABEL,
            r_mm=12,
            category_class_name="asdf",
            confidence=2,
            hit_class=HitClass.WEED,
            category_scores={},
            plant_score=0.2,
            weed_score=0.6,
            crop_score=0.2,
            uuid="point-3",
        )

        # The point ids don't exist in the points table
        point_pair = PointPair(point1_id=74, point2_id=75, distance_mm=10.0)

        with pytest.raises(IntegrityError):
            session.bulk_save_objects([image, point1, point2, point_pair])
            session.commit()

    shutil.rmtree("/tmp/test_dbs")

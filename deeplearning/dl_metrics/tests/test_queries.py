import json
import os
import shutil
from datetime import date

from dl_metrics.points_db import (
    Embedding,
    EmbeddingType,
    HitClass,
    Image,
    Point,
    PointPair,
    PointType,
    get_db,
    get_session,
)
from dl_metrics.queries import get_labels_and_matched_plant_predictions


def create_db_instance():
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db = "/tmp/test_dbs/test_points.db"

    with get_session(get_db(test_db)) as sess:
        image = Image(
            id=1,
            filepath="filepath-1",
            width=100,
            height=200,
            ppi=200,
            crop_id="crop-id-1",
            date=date.fromtimestamp(50000),
            timestamp_ms=50000,
            robot_id="robot-1",
            row_id="row-1",
            cam_id="predict-1",
            session_name="session-1",
            geo=json.loads("{}"),
            crop_count=1,
            weed_count=22,
        )

        point1 = Point(
            id=2,
            image_id=1,
            x=10,
            y=20,
            r_mm=2,
            score=1.0,
            category_class_name="category-1",
            confidence=2,
            hit_class=HitClass.WEED,
            type=PointType.LABEL,
            category_scores=json.dumps({"category-1": 1.0, "category-2": 0.5}),
            plant_score=None,
            weed_score=None,
            crop_score=None,
            uuid="uuid-1",
        )

        point2 = Point(
            id=3,
            image_id=1,
            x=50,
            y=60,
            r_mm=2,
            score=1.0,
            category_class_name="category-2",
            confidence=2,
            hit_class=HitClass.PLANT,
            type=PointType.PREDICTION,
            category_scores=json.dumps({"category-1": 0.3, "category-2": 0.9}),
            plant_score=0.76,
            weed_score=0.75,
            crop_score=0.1,
            uuid=None,
        )

        point3 = Point(
            id=4,
            image_id=1,
            x=30,
            y=40,
            r_mm=2,
            score=1.0,
            category_class_name="category-1",
            confidence=2,
            hit_class=HitClass.CROP,
            type=PointType.LABEL,
            category_scores=json.dumps({"category-1": 0.5, "category-2": 0.56}),
            plant_score=None,
            weed_score=None,
            crop_score=None,
            uuid="uuid-3",
        )

        point4 = Point(
            id=5,
            image_id=1,
            x=30,
            y=40,
            r_mm=2,
            score=1.0,
            category_class_name="category-1",
            confidence=0,
            hit_class=HitClass.CROP,
            type=PointType.LABEL,
            category_scores=json.dumps({"category-1": 0.5, "category-2": 0.56}),
            plant_score=None,
            weed_score=None,
            crop_score=None,
            uuid="uuid-4",
        )

        embedding1 = Embedding(point_id=2, type=EmbeddingType.FULL, embedding=[1, 2, 3, 4])

        embedding2 = Embedding(
            point_id=3,
            type=EmbeddingType.FULL,
            embedding=[2, 3, 4, 5],
        )

        embedding3 = Embedding(
            point_id=4,
            type=EmbeddingType.FULL,
            embedding=[3, 4, 5, 6],
        )

        embedding4 = Embedding(
            point_id=4,
            type=EmbeddingType.REDUCED_SCALED,
            embedding=[9],
        )

        point_pair1 = PointPair(
            point1_id=2,
            point2_id=3,
            distance_mm=4.0,
        )

        sess.bulk_save_objects(
            [image, point1, point2, point3, point4, embedding1, embedding2, embedding3, embedding4, point_pair1]
        )
        sess.commit()

    return test_db


def test_get_labels_and_matched_plant_predictions_defaults():
    test_db = create_db_instance()

    items, matches = get_labels_and_matched_plant_predictions(test_db)

    assert len(items) == 2
    assert items[0]["id"] == 2
    assert items[0]["prediction_id"] == 3
    assert items[1]["id"] == 4
    assert items[1]["prediction_id"] == None
    assert len(matches) == 1
    assert len(matches[2]) == 1
    assert matches[2][0]["id"] == 3


def test_get_labels_and_matched_plant_predictions_too_far():
    test_db = create_db_instance()

    items, matches = get_labels_and_matched_plant_predictions(test_db, distance_threshold_mm=2.0)

    assert len(items) == 2
    assert items[0]["id"] == 2
    assert items[0]["prediction_id"] == None
    assert items[1]["id"] == 4
    assert items[1]["prediction_id"] == None
    assert len(matches) == 0


def test_get_labels_and_matched_plant_predictions_too_low():
    test_db = create_db_instance()

    items, matches = get_labels_and_matched_plant_predictions(test_db, min_score=0.9)

    assert len(items) == 2
    assert items[0]["id"] == 2
    assert items[0]["prediction_id"] == None
    assert items[1]["id"] == 4
    assert items[1]["prediction_id"] == None
    assert len(matches) == 0

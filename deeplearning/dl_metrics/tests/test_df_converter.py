import json
import os
import shutil
import sys
from typing import Any, Dict, List

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

import pandas as pd
from dl_metrics.df_converter import convert_df
from dl_metrics.points_db import (  # noqa # isort:skip
    Embedding,
    EmbeddingType,
    HitClass,
    Image,
    ImageMetric,
    Point,
    PointPair,
    get_db,
    get_session,
)


def get_sample_df(add_embedding: bool = False) -> pd.DataFrame:
    items = [
        {
            "filepath": "asdf.png",
            "x": 200.0,
            "y": 300.0,
            "r": 10.0,
            "score": 0.56,
            "category_class_name": "broadleaf",
            "confidence": 0,
            "hit_class_name": HitClass.WEED.value,
            "tile_index": (1, 1),
            "category_scores": json.dumps({"broadleaf": 0.9, "grass": 0.2}),
            "crop_id": "crop_id_1",
            "robot_id": "slayer1234",
            "row_id": 2,
            "cam_id": "predict2",
            "timestamp_ms": 10001,
            "session_name": "session_asd",
            "geohash": "geohash_1234",
            "type": "label",
            "plant_score": 0.8,
            "weed_score": 0.5,
            "crop_score": 0.2,
            "uuid": "point1-asdfg",
        },
        {
            "filepath": "qwerty.png",
            "x": 400.0,
            "y": 500.0,
            "r": 20.0,
            "score": 0.23,
            "category_class_name": HitClass.CROP.name,
            "confidence": 2,
            "hit_class_name": HitClass.CROP.value,
            "tile_index": (2, 2),
            "category_scores": json.dumps({"crop": 0.99}),
            "crop_id": "crop_id_2",
            "robot_id": "slayer5678",
            "row_id": 1,
            "cam_id": "predict4",
            "timestamp_ms": 20002,
            "session_name": "session_qwe",
            "geohash": "geohash_5678",
            "type": "prediction",
            "plant_score": 0.7,
            "weed_score": 0.1,
            "crop_score": 0.8,
            "uuid": "point2-qwert",
        },
    ]

    if add_embedding:
        items[0]["embedding"] = [1, 2, 3, 4, 5]
        items[0]["reduced_embedding"] = [23]
        items[1]["embedding"] = None
        items[1]["reduced_embedding"] = None

    df = pd.DataFrame(items)

    return df


def get_sample_image_metrics() -> Dict[str, List[Dict[str, Any]]]:
    return {
        "qwerty.png": [
            {"metric_name": "abcde", "numerator": 101, "denominator": 341},
            {"metric_name": "xcdfv", "numerator": 23, "denominator": 24},
        ]
    }


def test_df_converter():
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db = "/tmp/test_dbs/test_points.db"

    df = get_sample_df(False)
    with get_session(get_db(test_db)) as sess:
        out_ind = convert_df(df, sess, rank=9, image_metrics=get_sample_image_metrics())

        images = sess.query(Image).order_by(Image.id).all()
        points = sess.query(Point).order_by(Point.id).all()
        point_pairs = sess.query(PointPair).all()
        embeddings = sess.query(Embedding).all()
        image_metrics = sess.query(ImageMetric).order_by(ImageMetric.metric_name).all()

    assert out_ind == 4

    assert len(images) == 2
    assert len(points) == 2
    assert len(point_pairs) == 0
    assert len(embeddings) == 0
    assert len(image_metrics) == 2

    assert images[0].id == 109
    assert images[0].filepath == "asdf.png"
    assert images[0].row_id == "2"

    assert images[1].id == 309
    assert images[1].filepath == "qwerty.png"
    assert images[1].row_id == "1"

    assert points[0].id == 209
    assert points[0].uuid == "point1-asdfg"
    assert points[0].hit_class == HitClass.WEED
    assert points[0].category_class_name == "broadleaf"
    assert points[0].plant_score == 0.8
    assert points[0].weed_score == 0.5
    assert points[0].crop_score == 0.2

    assert points[1].id == 409
    assert points[1].uuid == "point2-qwert"
    assert points[1].hit_class == HitClass.CROP
    assert points[1].category_class_name == "crop_id_2"
    assert points[1].plant_score == 0.7
    assert points[1].weed_score == 0.1
    assert points[1].crop_score == 0.8

    assert image_metrics[0].metric_name == "abcde"
    assert abs(image_metrics[0].numerator - 101.0) < 1e-10
    assert abs(image_metrics[0].denominator - 341) < 1e-10

    assert image_metrics[1].metric_name == "xcdfv"
    assert abs(image_metrics[1].numerator - 23) < 1e-10
    assert abs(image_metrics[1].denominator - 24) < 1e-10

    shutil.rmtree("/tmp/test_dbs")


def test_df_converter_with_embeddings():
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db = "/tmp/test_dbs/test_points.db"

    df = get_sample_df(True)
    with get_session(get_db(test_db)) as sess:
        out_ind = convert_df(df, sess, rank=9)

        images = sess.query(Image).all()
        points = sess.query(Point).all()
        point_pairs = sess.query(PointPair).all()
        embeddings = sess.query(Embedding).all()

    assert out_ind == 4

    assert len(images) == 2
    assert len(points) == 2
    assert len(point_pairs) == 0
    assert len(embeddings) == 2

    for image in images:
        assert image.id in [109, 309]

    for point in points:
        assert point.id in [209, 409]

    assert embeddings[0].point_id == 209
    assert embeddings[0].embedding == [1, 2, 3, 4, 5]
    assert embeddings[0].type == EmbeddingType.FULL

    assert embeddings[1].point_id == 209
    assert embeddings[1].embedding == [23]
    assert embeddings[1].type == EmbeddingType.REDUCED

    shutil.rmtree("/tmp/test_dbs")

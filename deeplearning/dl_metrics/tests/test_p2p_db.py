import os
import shutil
from datetime import datetime

import pytest
from p2p_metrics.p2p_db import Image, Match, get_db, get_session
from sqlalchemy.exc import IntegrityError


def test_foreign_key_constraint() -> None:
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db = "/tmp/test_dbs/test_p2p.db"

    image = Image(
        id="abcd",
        filepath="file-1",
        width=200,
        height=200,
        ppi=200,
        crop_id="asdf",
        date=datetime.now(),
        timestamp_ms=10,
        robot_id="robot1",
        row_id="row1",
        cam_id="target12",
        geo={},
        geohash="aaaa",
    )

    match = Match(
        id="match1",
        image_id="no-image-id",
        distance=10,
        x=10,
        y=10,
        positive_match=False,
        x_hat=11,
        y_hat=11,
        positive_match_hat=False,
    )

    with pytest.raises(IntegrityError):
        with get_session(get_db(test_db)) as session:
            session.bulk_save_objects([image, match])
            session.commit()

    shutil.rmtree("/tmp/test_dbs")

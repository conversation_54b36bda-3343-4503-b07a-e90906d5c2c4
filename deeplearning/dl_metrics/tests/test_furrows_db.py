import os
import shutil
from datetime import datetime

import pytest
from furrows_metrics.furrows_db import Image, ImageMetric, get_db, get_session
from sqlalchemy.exc import IntegrityError


def test_foreign_key_constraint() -> None:
    if os.path.exists("/tmp/test_dbs"):
        shutil.rmtree("/tmp/test_dbs")
    os.makedirs("/tmp/test_dbs")
    test_db = "/tmp/test_dbs/test_furrow.db"

    image = Image(
        id="abcd",
        filepath="file-1",
        date=datetime.now(),
        timestamp_ms=10,
        robot_id="robot1",
        cam_id="drive12",
        geohash="aaaa",
    )

    metric = ImageMetric(
        image_id="no-real-image",
        metric_name="patrick",
        numerator=10,
        denominator=20,
    )

    with pytest.raises(IntegrityError):
        with get_session(get_db(test_db)) as session:
            session.bulk_save_objects([image, metric])
            session.commit()

    shutil.rmtree("/tmp/test_dbs")

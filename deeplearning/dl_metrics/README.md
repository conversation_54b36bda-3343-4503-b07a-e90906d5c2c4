# DL Metrics

The DL Metrics repo is intended to be a single source of truth for the definition of our various model metrics. We
leverage a mysql database definition of our points objects in order to compute various detection model metrics quickly
and efficiently.

Below is an example of how to convert a `dataframe.csv` file to a `points.db` file and then use that `points.db` file
to quickly compute the percentage of crops targeted for various sizes of crops.

```python
import pandas
import dl_metrics

# Load your results dataframe generated from Deepweed training
df = pandas.read_csv("dataframe.csv")

db = dl_metrics.get_db(f"points_{dl_metrics.__version__}.db")
session = dl_metrics.get_session(db)
dl_metrics.convert_df(df, session)
metric = dl_metrics.CropsTargeted().filter(robot_ids={"slayer2", "slayer3", "slayer4"}).group_by(size=True)

results = metric(session)

print(results)

"""
Output:
    size  numerator  denominator
0      1         11           20
1      2        104          367
2      3        309         2402
3      5        392        13243
4      8        348        60596
5     13        167        81946
6     21         41        21811
7     34         19         6169
8     55          4         1256
9     89          0           60
10   144          0            1
"""
```

## Database

Command to generate an alembic revision:

```
alembic -x DB_FILE=/path/to/points.db revision --autogenerate -m "Change description"
```


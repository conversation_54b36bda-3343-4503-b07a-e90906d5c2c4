import copy
from abc import ABC, abstractmethod
from typing import Any, List, Optional, Set, Tuple

import pandas as pd
from sqlalchemy import Integer, and_, cast, func, or_
from sqlalchemy.orm import aliased

from . import comparison_db


class Metric(ABC):
    def __init__(
        self, name: str = "<PERSON><PERSON>", negative_pred_threshold: float = 0.2, positive_pred_threshold: float = 0.8
    ) -> None:
        self._negative_pred_threshold = negative_pred_threshold
        self._positive_pred_threshold = positive_pred_threshold

        self._filter_users: Optional[Set[str]] = None
        self._filter_source: Optional[Set[str]] = None
        self._filter_match: Optional[Set[bool]] = None
        self._filter_split: Optional[Set[str]] = None
        self.name = name

        self._filter_geohash_prefixes_0: Optional[Set[str]] = None
        self._plant_0: Optional[str] = None
        self._min_mean_size_0: Optional[float] = None
        self._max_mean_size_0: Optional[float] = None
        self._image_id_0: Optional[str] = None
        self._use_overlapped_by_circle_0: Optional[Set[bool]] = {True, False}
        self._use_overlapped_by_center_0: Optional[Set[bool]] = {True, False}

        self._filter_geohash_prefixes_1: Optional[Set[str]] = None
        self._plant_1: Optional[str] = None
        self._min_mean_size_1: Optional[float] = None
        self._max_mean_size_1: Optional[float] = None
        self._image_id_1: Optional[str] = None
        self._use_overlapped_by_circle_1: Optional[Set[bool]] = {True, False}
        self._use_overlapped_by_center_1: Optional[Set[bool]] = {True, False}

    @abstractmethod
    def __call__(self, session: comparison_db.Session) -> pd.DataFrame:
        pass

    def docs(self) -> Optional[str]:
        return Metric.__init__.__doc__

    def filter(
        self,
        users: Optional[Set[str]] = None,
        source: Optional[Set[str]] = None,
        match: Optional[Set[bool]] = None,
        split: Optional[Set[str]] = None,
        geohash_prefixes_0: Optional[Set[str]] = None,
        plant_0: Optional[str] = None,
        min_mean_size_0: Optional[float] = None,
        max_mean_size_0: Optional[float] = None,
        image_id_0: Optional[str] = None,
        use_overlapped_by_circle_0: Optional[Set[bool]] = {True, False},
        use_overlapped_by_center_0: Optional[Set[bool]] = {True, False},
        geohash_prefixes_1: Optional[Set[str]] = None,
        plant_1: Optional[str] = None,
        min_mean_size_1: Optional[float] = None,
        max_mean_size_1: Optional[float] = None,
        image_id_1: Optional[str] = None,
        use_overlapped_by_circle_1: Optional[Set[bool]] = {True, False},
        use_overlapped_by_center_1: Optional[Set[bool]] = {True, False},
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric._filter_users = users
        new_metric._filter_source = source
        new_metric._filter_match = match
        new_metric._filter_split = split

        new_metric._filter_geohash_prefixes_0 = geohash_prefixes_0
        new_metric._plant_0 = plant_0
        new_metric._min_mean_size_0 = min_mean_size_0
        new_metric._max_mean_size_0 = max_mean_size_0
        new_metric._image_id_0 = image_id_0
        new_metric._use_overlapped_by_circle_0 = use_overlapped_by_circle_0
        new_metric._use_overlapped_by_center_0 = use_overlapped_by_center_0

        new_metric._filter_geohash_prefixes_1 = geohash_prefixes_1
        new_metric._plant_1 = plant_1
        new_metric._min_mean_size_1 = min_mean_size_1
        new_metric._max_mean_size_1 = max_mean_size_1
        new_metric._image_id_1 = image_id_1
        new_metric._use_overlapped_by_circle_1 = use_overlapped_by_circle_1
        new_metric._use_overlapped_by_center_1 = use_overlapped_by_center_1

        return new_metric

    def apply_item_filters(self, item1: comparison_db.ComparisonItem, item2: comparison_db.ComparisonItem) -> List[Any]:
        comparison_item_filters = []

        if self._filter_geohash_prefixes_0 is not None:
            comparison_item_filters.append(item1.geohash.in_(self._filter_geohash_prefixes_0))
        if self._plant_0[1] is not None:
            comparison_item_filters.append(item1.category == self._plant_0[1])
        if self._min_mean_size_0 is not None:
            comparison_item_filters.append(item1.radius >= self._min_mean_size_0)
        if self._max_mean_size_0 is not None:
            comparison_item_filters.append(item1.radius <= self._max_mean_size_0)
        if self._image_id_0 is not None:
            comparison_item_filters.append(item1.image_id == self._image_id_0)
        if self._use_overlapped_by_circle_0 is not None:
            comparison_item_filters.append(
                or_(item1.overlap_by_circle.in_(self._use_overlapped_by_circle_0), item1.overlap_by_circle.is_(None))
            )
        if self._use_overlapped_by_center_0 is not None:
            comparison_item_filters.append(
                or_(item1.overlap_by_center.in_(self._use_overlapped_by_center_0), item1.overlap_by_center.is_(None))
            )

        if self._filter_geohash_prefixes_1 is not None:
            comparison_item_filters.append(item2.geohash.in_(self._filter_geohash_prefixes_1))
        if self._plant_1[1] is not None:
            comparison_item_filters.append(item2.category == self._plant_1[1])
        if self._min_mean_size_1 is not None:
            comparison_item_filters.append(item2.radius >= self._min_mean_size_1)
        if self._max_mean_size_1 is not None:
            comparison_item_filters.append(item2.radius <= self._max_mean_size_1)
        if self._image_id_1 is not None:
            comparison_item_filters.append(item2.image_id == self._image_id_1)
        if self._use_overlapped_by_circle_1 is not None:
            comparison_item_filters.append(
                or_(item2.overlap_by_circle.in_(self._use_overlapped_by_circle_1), item2.overlap_by_circle.is_(None))
            )
        if self._use_overlapped_by_center_1 is not None:
            comparison_item_filters.append(
                or_(item2.overlap_by_center.in_(self._use_overlapped_by_center_1), item2.overlap_by_center.is_(None))
            )

        return comparison_item_filters

    def apply_pair_filters(
        self,
    ) -> List[Any]:
        comparison_pair_filters = []

        if self._filter_users is not None:
            comparison_pair_filters.append(comparison_db.ComparisonPair.user.in_(self._filter_users))
        if self._filter_source is not None:
            comparison_pair_filters.append(comparison_db.ComparisonPair.source.in_(self._filter_source))
        if self._filter_match is not None:
            comparison_pair_filters.append(comparison_db.ComparisonPair.match.in_(self._filter_match))
        if self._filter_split is not None:
            comparison_pair_filters.append(comparison_db.ComparisonPair.split.in_(self._filter_split))

        return comparison_pair_filters

    def create_initial_filters(
        self,
    ) -> Tuple[comparison_db.ComparisonItem, comparison_db.ComparisonItem, List[Any], List[Any], List[Any]]:
        Item1 = aliased(comparison_db.ComparisonItem)
        Item2 = aliased(comparison_db.ComparisonItem)

        comparison_pair_filters = self.apply_pair_filters()
        comparison_item_filters1 = self.apply_item_filters(item1=Item1, item2=Item2)
        comparison_item_filters2 = self.apply_item_filters(item1=Item2, item2=Item1)

        return Item1, Item2, comparison_pair_filters, comparison_item_filters1, comparison_item_filters2

    def query_points(
        self,
        session: comparison_db.Session,
        Item1: comparison_db.ComparisonItem,
        Item2: comparison_db.ComparisonItem,
        comparison_pair_filters: List[Any],
        comparison_item_filters1: List[Any],
        comparison_item_filters2: List[Any],
        numerator_filters: Any,
        denominator_filters: Any,
    ) -> pd.DataFrame:
        query = (
            session.query(
                func.sum(cast(numerator_filters, Integer)).label("numerator"),
                func.sum(cast(denominator_filters, Integer)).label("denominator"),
            )
            .select_from(comparison_db.ComparisonPair)
            .join(Item1, Item1.id == comparison_db.ComparisonPair.item1_id)
            .join(Item2, Item2.id == comparison_db.ComparisonPair.item2_id)
            .filter(*comparison_pair_filters)
            .filter(or_(and_(*comparison_item_filters1), and_(*comparison_item_filters2)))
        )

        return pd.read_sql_query(query.statement, session.bind)

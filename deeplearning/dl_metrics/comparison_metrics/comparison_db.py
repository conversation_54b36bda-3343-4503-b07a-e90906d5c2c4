import logging
import os
from typing import Any, Callable, Dict, Optional

from alembic import command
from alembic.config import Config
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    String,
    create_engine,
    event,
)
from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship

from .version import __version__

logging.getLogger("alembic.runtime.migration").setLevel(logging.WARN)

__all__ = [
    "Engine",
    "Session",
    "get_db",
    "get_session",
    "get_comparison_metadata",
    "ComparisonMetadata",
    "ComparisonItem",
    "ComparisonPair",
    "__version__",
]

Base = declarative_base()


def run_alembic(db_path: str, callback: Callable[[Config], None]) -> None:
    script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "alembic"))
    cfg = Config()
    cfg.set_main_option("script_location", script_path)
    cfg.set_main_option("sqlalchemy.url", f"sqlite:///{db_path}")
    callback(cfg)


def get_db(db_path: str) -> Engine:
    if os.path.exists(db_path):
        run_alembic(db_path, lambda cfg: command.upgrade(cfg, "head"))

    engine = create_engine(f"sqlite:///{db_path}?_pragma=busy_timeout(5000)")
    Base.metadata.create_all(engine)
    run_alembic(db_path, lambda cfg: command.stamp(cfg, "head"))

    def _fk_pragma_on_connect(dbapi_connection: Engine, connection_record: Any) -> None:
        dbapi_connection.execute("pragma foreign_keys=ON")

    event.listen(engine, "connect", _fk_pragma_on_connect)

    return engine


def get_session(engine: Engine) -> Session:
    session = Session(engine)
    return session


class ComparisonMetadata(Base):
    __tablename__ = "comparison_metadata"

    id = Column(String, primary_key=True)  # Unique ID for the metadata.
    comparison_model_id = Column(String, nullable=False)
    dataset_id = Column(String, nullable=True)

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


class ComparisonItem(Base):
    __tablename__ = "comparison_items"

    id = Column(String, primary_key=True)  # Unique ID for this datapoint.
    uuid = Column(String, nullable=True)  # Matches UUID of ComparisonEmbeddings or ComparisonEmbeddingObject.
    image_id = Column(String, nullable=False)  # Image ID for this datapoint.
    s3_url = Column(String, nullable=False)  # Image URL for this datapoint.
    label_id = Column(String, nullable=False)  # Label ID for this datapoint.
    category = Column(String, nullable=False)  # Category for this datapoint.
    x = Column(Float, nullable=False)  # X-coordinate for this datapoint.
    y = Column(Float, nullable=False)  # Y-coordinate for this datapoint.
    radius = Column(Float, nullable=False)  # Radius for this datapoint.
    geohash = Column(String, nullable=False)  # Geohash for this datapoint.
    overlap_by_circle = Column(Boolean, nullable=True)  # Only stored from evaluation.
    overlap_by_center = Column(Boolean, nullable=True)  # Only stored from evaluation.

    comparison_pairs1 = relationship("ComparisonPair", foreign_keys="[ComparisonPair.item1_id]", back_populates="item1")
    comparison_pairs2 = relationship("ComparisonPair", foreign_keys="[ComparisonPair.item2_id]", back_populates="item2")

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


class ComparisonPair(Base):
    __tablename__ = "comparison_pairs"

    id = Column(String, primary_key=True)  # Unique ID for this datapoint.

    item1_id = Column(String, ForeignKey("comparison_items.id"), nullable=False)
    item2_id = Column(String, ForeignKey("comparison_items.id"), nullable=False)
    item1 = relationship("ComparisonItem", foreign_keys=[item1_id], back_populates="comparison_pairs1")
    item2 = relationship("ComparisonItem", foreign_keys=[item2_id], back_populates="comparison_pairs2")

    match = Column(Boolean, nullable=True)  # Indicates the ground truth of the pair.
    user = Column(String, nullable=False)  # The user who labeled the comparison.
    source = Column(String, nullable=False)  # Specifies whether the sample is from sampled/prioritized/generated.

    split = Column(String, nullable=True)  # Denotes the dataset split from train/validation/test.
    epoch = Column(Integer, nullable=True)  # The current epoch during training.
    batch_nb = Column(Integer, nullable=True)  # The current batch_nb during training.
    comparison_score = Column(
        Float, nullable=False
    )  # The score representing the similarity between the two datapoints.

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


def get_comparison_metadata(
    session: Session, comparison_model_id: Optional[str] = None, dataset_id: Optional[str] = None
) -> Optional[ComparisonMetadata]:

    return (
        session.query(ComparisonMetadata)
        .filter(
            ComparisonMetadata.comparison_model_id == comparison_model_id, ComparisonMetadata.dataset_id == dataset_id
        )
        .first()
    )

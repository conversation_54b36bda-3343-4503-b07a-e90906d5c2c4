import uuid
from typing import Any, Dict, List, Optional, <PERSON><PERSON>

import torch
from sqlalchemy.orm import Session

from . import comparison_db

__all__ = ["create_comparison_item", "store_comparison_db"]


def create_comparison_item(session: Session, image_info: Dict[str, Any]) -> comparison_db.ComparisonItem:
    comparison_item = comparison_db.ComparisonItem(
        id=str(uuid.uuid4()),  # Unique ID for the comparison_db.
        uuid=image_info["uuid"],  # UUID to match the comparison image.
        image_id=image_info["id"],
        s3_url=image_info["s3_url"],
        label_id=image_info["label_id"],
        category=image_info["category"],
        x=image_info["x"],
        y=image_info["y"],
        radius=image_info["radius"],
        geohash=image_info["geohash"],
        overlap_by_circle=image_info["overlap_by_circle"],
        overlap_by_center=image_info["overlap_by_center"],
    )
    session.add(comparison_item)
    session.flush()

    return comparison_item


def store_comparison_db(
    out_hat: torch.Tensor,
    out_threshed: torch.Tensor,
    comparison_db_session: Session,
    recovery_info: Tuple[List[Dict[str, Any]], List[Dict[str, Any]]],
    stage_info: Optional[Dict[str, Any]] = None,
    comparison_metadata: Optional[Dict[str, Any]] = None,
) -> None:
    recovery_info1 = recovery_info[0]
    recovery_info2 = recovery_info[1]
    split = stage_info.get("stage", None) if stage_info is not None else None
    epoch = stage_info.get("epoch", None) if stage_info is not None else None
    batch_nb = stage_info.get("batch_nb", None) if stage_info is not None else None
    comparison_model_id = (
        comparison_metadata.get("comparison_model_id", None) if comparison_metadata is not None else None
    )
    dataset_id = comparison_metadata.get("dataset_id", None) if comparison_metadata is not None else None
    users = comparison_metadata.get("users", None) if comparison_metadata is not None else None
    sources = comparison_metadata.get("sources", None) if comparison_metadata is not None else None
    image_id_to_url = comparison_metadata.get("image_id_to_url", None) if comparison_metadata is not None else None

    if batch_nb is not None and batch_nb == 0:
        comparison_metadata = comparison_db.get_comparison_metadata(
            session=comparison_db_session,
            comparison_model_id=comparison_model_id,
            dataset_id=dataset_id,
        )  # If dataset_id is None, it indicates that the comparison_db was built during training.
        if not comparison_metadata:
            comparison_metadata = comparison_db.ComparisonMetadata(
                id=str(uuid.uuid4()),
                comparison_model_id=comparison_model_id,
                dataset_id=dataset_id,
            )
            comparison_db_session.add(comparison_metadata)
            comparison_db_session.flush()
    for n, (image_one_info, image_two_info) in enumerate(zip(recovery_info1, recovery_info2)):
        if image_id_to_url is not None:  # Add s3 url.
            image_one_info["s3_url"] = image_id_to_url[image_one_info["id"]]
            image_two_info["s3_url"] = image_id_to_url[image_two_info["id"]]
        user = users[n] if users is not None else None
        source = sources[n] if sources is not None else None

        comparison_item1 = create_comparison_item(session=comparison_db_session, image_info=image_one_info)
        comparison_item2 = create_comparison_item(session=comparison_db_session, image_info=image_two_info)
        comparison_pair = comparison_db.ComparisonPair(
            id=str(uuid.uuid4()),
            item1_id=comparison_item1.id,
            item2_id=comparison_item2.id,
            match=out_threshed[n].item(),
            user=user,
            source=source,
            split=split,
            epoch=epoch,
            batch_nb=batch_nb,
            comparison_score=out_hat[n].item(),
        )
        comparison_db_session.add(comparison_pair)
        comparison_db_session.flush()

    comparison_db_session.commit()

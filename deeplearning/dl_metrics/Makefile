
lint:
	docker run --rm --network none --platform linux/amd64 -v $(shell pwd):$(shell pwd) -w $(shell pwd) quay.io/amboss-mededu/pyfmt:latest isort --ignore-whitespace ./dl_metrics
	docker run --rm --network none --platform linux/amd64 -v $(shell pwd):$(shell pwd) -w $(shell pwd) quay.io/amboss-mededu/pyfmt:latest black -l 120 ./dl_metrics
	docker run --rm --network none --platform linux/amd64 -v $(shell pwd):$(shell pwd) -w $(shell pwd) quay.io/amboss-mededu/pyfmt:latest flake8 ./dl_metrics

test-3.8:
	docker run --rm --network host -v $(HOME)/.aws:/root/.aws:ro -v $(shell pwd):$(shell pwd) -w $(shell pwd) python:3.8 make test

test-3.9:
	docker run --rm --network host -v $(HOME)/.aws:/root/.aws:ro -v $(shell pwd):$(shell pwd) -w $(shell pwd) python:3.9 make test

test-3.10:
	docker run --rm --network host -v $(HOME)/.aws:/root/.aws:ro -v $(shell pwd):$(shell pwd) -w $(shell pwd) python:3.10 make test

test-all-versions: test-3.8 test-3.9 test-3.10

test:
	pip install -r requirements.txt
	pip install -r test-requirements.txt
	pytest

ci_test:
	pip install -r requirements.txt
	pip install -r test-requirements.txt
	pytest -k 'not test_df_conversion_and_metric_initialization'
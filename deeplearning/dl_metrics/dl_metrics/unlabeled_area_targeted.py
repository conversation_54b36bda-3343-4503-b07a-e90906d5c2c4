from typing import List, Optional

import pandas as pd
from sqlalchemy import and_

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class UnlabeledAreaTargeted(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
        included_predicted_category_class_names: Optional[List[str]] = None,
        excluded_predicted_category_class_names: Optional[List[str]] = None,
    ):
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Unlabeled area targeted",
            description="""
                Weeds targeted outside of known labels. Weed is targeted if there's a weed detection and no
                corresponding crop detections within the crop protection radius.
            """,
        )

        self.included_predicted_category_class_names = included_predicted_category_class_names
        self.excluded_predicted_category_class_names = excluded_predicted_category_class_names

        if self.included_predicted_category_class_names:
            self.name += f" (including {', '.join(self.included_predicted_category_class_names)} predictions)"
        if self.excluded_predicted_category_class_names:
            self.name += f" (excluding {', '.join(self.excluded_predicted_category_class_names)} predictions)"

    def __call__(self, session: Session) -> pd.DataFrame:
        predicted_category_class_name_filters = []
        if self.included_predicted_category_class_names:
            predicted_category_class_name_filters.append(
                Point.category_class_name.in_(self.included_predicted_category_class_names)
            )
        if self.excluded_predicted_category_class_names:
            predicted_category_class_name_filters.append(
                Point.category_class_name.not_in(self.excluded_predicted_category_class_names)
            )

        unlabeled_area_targeted_filters = [
            ~Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.LABEL,
                            PointPair.distance_mm < Point.r_mm,
                        )
                    ),
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.PREDICTION,
            Point.hit_class == HitClass.WEED,
            Point.score > self.wpt,
            *predicted_category_class_name_filters,
            ~Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.PREDICTION,
                            Point.hit_class == HitClass.CROP,
                            Point.score > self.cpt,
                        )
                    ),
                    PointPair.distance_mm < self.crop_protection_radius_mm,
                )
            ),
        ]

        return self._query_points(
            session=session, point_filters=point_filters, numerator_filters=unlabeled_area_targeted_filters
        )

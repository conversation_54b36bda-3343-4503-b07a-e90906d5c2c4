import pandas as pd

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointType, Session


class UncertainWeedDetections(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
        a: float = 0.4,
        b: float = 0.6,
    ) -> None:
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name=f"Uncertain weed detections ({a}, {b})",
            description=f"Weed detections with scores between {a} and {b}.",
        )
        self.a = a
        self.b = b

    def __call__(self, session: Session) -> pd.DataFrame:
        uncertain_point_filters = [
            Point.score < self.b,
        ]

        point_filters = [
            Point.type == PointType.PREDICTION,
            Point.hit_class == HitClass.WEED,
            Point.score > self.a,
        ]

        return self._query_points(
            session=session, point_filters=point_filters, numerator_filters=uncertain_point_filters
        )

import pandas as pd
from sqlalchemy import and_

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class CropsDetected(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
    ) -> None:
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Crops detected",
            description=f"""
                We say a crop is properly detected if we predict a crop within {distance_threshold_mm}mm of the
                label at the specified crop point threshold. These are calculated without any crop protection
                algorithms; they simply crops that the model predicted correctly at the given crop-point-threshold
                and distance threshold.
            """,
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        crop_detected_filters = [
            Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.PREDICTION,
                            Point.hit_class == HitClass.CROP,
                            Point.score > self.cpt,
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.LABEL,
            Point.hit_class == HitClass.CROP,
            Point.confidence == 2,
        ]

        return self._query_points(session=session, point_filters=point_filters, numerator_filters=crop_detected_filters)

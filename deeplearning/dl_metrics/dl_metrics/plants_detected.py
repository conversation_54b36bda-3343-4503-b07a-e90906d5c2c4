from typing import Optional

import pandas as pd
from sqlalchemy import and_

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class PlantsDetected(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
        label_hit_class: Optional[HitClass] = None,
    ) -> None:
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Plants detected",
            description=f"""
                We say a plant is properly detected if we predict a plant within {distance_threshold_mm}mm
                of the label at the specified weed point threshold or crop point threshold (whichever is lower).
                These are calculated without any crop protection algorithms; they simply plants that the model
                predicted correctly at the given thresholds.
            """,
        )
        self.label_hit_class = label_hit_class
        if self.label_hit_class is not None:
            self.name = f"{self.label_hit_class.name.lower().capitalize()}s detected as plants"

    def __call__(self, session: Session) -> pd.DataFrame:
        plant_detected_filters = [
            Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.PREDICTION,
                            Point.hit_class == HitClass.PLANT,
                            Point.score > self.plant_threshold,
                        ),
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.LABEL,
            Point.confidence == 2,
        ]
        if self.label_hit_class is not None:
            point_filters.append(Point.hit_class == self.label_hit_class)

        return self._query_points(
            session=session, point_filters=point_filters, numerator_filters=plant_detected_filters
        )

from typing import List, cast

from sqlalchemy import case, literal_column
from sqlalchemy.sql.expression import Case

__all__ = ["mm2px", "px2mm", "get_size_bins_list", "get_next_size_bin", "size_bin"]

Y_POSITION_BINS: List[float] = [0, 1000, 2000, 3000]
SIZE_BINS: List[float] = [0, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233]
CROP_COUNT_BINS: List[float] = [0, 20, 40, 80, 160, 320, 640]


def mm2px(distance_mm: float, ppi: int = 200) -> float:
    distance_inches = distance_mm / 25.4
    distance_pixels = ppi * distance_inches
    return distance_pixels


def px2mm(distance_pixels: float, ppi: int = 200) -> float:
    distance_inches = distance_pixels / ppi
    distance_mm = distance_inches * 25.4
    return distance_mm


def get_size_bins_list() -> List[float]:
    return SIZE_BINS


def get_next_size_bin(x: float) -> float:
    idx = SIZE_BINS.index(x)
    if idx + 1 < len(SIZE_BINS):
        return SIZE_BINS[idx + 1]
    else:
        return float("inf")


def size_bin(r: float) -> Case:
    return case(
        *((r >= literal_column(str(x)), literal_column(str(x))) for x in sorted(get_size_bins_list(), reverse=True)),
    )


def get_y_position_bins_list() -> List[float]:
    return Y_POSITION_BINS


def y_position_bin(y: float) -> Case:
    return case(
        *(
            (y >= literal_column(str(x)), literal_column(str(x)))
            for x in sorted(get_y_position_bins_list(), reverse=True)
        ),
    )


def get_next_y_position_bin(x: float) -> float:
    idx = Y_POSITION_BINS.index(x)
    if idx + 1 < len(Y_POSITION_BINS):
        return Y_POSITION_BINS[idx + 1]
    else:
        return float("inf")


def get_crop_count_bins_list() -> List[float]:
    return CROP_COUNT_BINS


def get_next_crop_count_bin(x: float) -> float:
    idx = CROP_COUNT_BINS.index(cast(int, x))
    if idx + 1 < len(CROP_COUNT_BINS):
        return CROP_COUNT_BINS[idx + 1]
    else:
        return float("inf")


def crop_count_bin(r: float) -> Case:
    return case(
        *(
            (r >= literal_column(str(x)), literal_column(str(x)))
            for x in sorted(get_crop_count_bins_list(), reverse=True)
        ),
    )


def nan_divide(n: float, d: float) -> float:
    if d == 0.0:
        return float("nan")
    return float(n) / d

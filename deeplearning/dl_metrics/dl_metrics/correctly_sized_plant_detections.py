import pandas as pd
from sqlalchemy import and_, func, or_
from sqlalchemy.orm import aliased

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class CorrectlySizedPlantDetections(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
        size_threshold_pct: float = 20,
        size_thershold_mm: float = 1,
    ) -> None:
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Correctly sized plant detections",
            description=f"""
                Plant detections are correctly sized if they're within {size_threshold_pct}% or
                {size_thershold_mm}mm of label size.
            """,
        )
        self.size_threshold_pct = size_threshold_pct
        self.size_threshold_mm = size_thershold_mm

    def __call__(self, session: Session) -> pd.DataFrame:
        inner_point = aliased(Point, name="inner_point")

        correct_size_filters = [
            Point.pairs.any(
                and_(
                    PointPair.point2.of_type(inner_point).has(
                        and_(
                            inner_point.type == PointType.LABEL,
                            inner_point.confidence == 2,
                            or_(
                                and_(
                                    Point.r_mm > inner_point.r_mm * (1.0 - self.size_threshold_pct / 100.0),
                                    Point.r_mm < inner_point.r_mm * (1.0 + self.size_threshold_pct / 100.0),
                                ),
                                func.abs(Point.r_mm - inner_point.r_mm) < self.size_threshold_mm,
                            ),
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.PREDICTION,
            Point.hit_class == HitClass.PLANT,
            Point.score > self.plant_threshold,
            Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.LABEL,
                            Point.confidence == 2,
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        return self._query_points(session=session, point_filters=point_filters, numerator_filters=correct_size_filters)

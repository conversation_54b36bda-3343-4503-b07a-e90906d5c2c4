from typing import List, Optional
import pandas as pd

from .metric import Metric
from .points_db import Session


class ImageLevelMetric(Metric):
    def __init__(
        self, metric_names: List[str] = [], min_denom_value: Optional[int] = None, max_denom_value: Optional[int] = None
    ):
        super().__init__(
            name="Image Metrics",
            description="""
                This is a generic way to get image_metrics.
            """,
        )

        self._metric_names = metric_names
        self._min_denom_value = min_denom_value
        self._max_denom_value = max_denom_value

    def __call__(self, session: Session) -> pd.DataFrame:
        return self._query_image_metrics(
            session=session,
            metric_names=self._metric_names,
            min_denom_value=self._min_denom_value,
            max_denom_value=self._max_denom_value,
        )

import ast
import logging
import math
from datetime import date
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
from scipy.spatial import cKDTree
from sqlalchemy.orm import Session

from .metric_utils import mm2px, px2mm
from .points_db import Embedding, EmbeddingType, HitClass, Image, ImageMetric, Point, PointPair, PointType

__all__ = ["convert_df"]

IMAGE_WIDTH = 4096
IMAGE_HEIGHT = 3000
PPI = 200

BORDER = 100
TILE_WIDTH = 1800
TILE_HEIGHT = 1696

DISTANCE_UPPER_BOUND_MM = 12.7


class IdAssigner:
    def __init__(self, initial_value: int):
        self._next_id = initial_value

    def mkid(self) -> int:
        self._next_id += 1
        return self._next_id

    def current(self) -> int:
        return self._next_id


def eval_if_str(value: Any) -> Any:
    if isinstance(value, str):
        return ast.literal_eval(value)
    return value


def coalesce(value: Any, default: Any) -> Any:
    if not value:
        return default
    if isinstance(value, float) and math.isnan(value):
        return default
    return value


# flake8: noqa: C901
def convert_df(
    df: pd.DataFrame,
    session: Session,
    rank: int = 0,
    initial_id: Optional[int] = None,
    verbose: bool = True,
    tiling: bool = True,
    image_metrics: Dict[str, List[Dict[str, Any]]] = {},
    tile_height: Optional[int] = TILE_HEIGHT,
    tile_width: Optional[int] = TILE_WIDTH,
    corrected_transpose: bool = False,
) -> int:
    if initial_id is None:
        image_id_values = [image.id // 100 for image in session.query(Image)]
        point_id_values = [point.id // 100 for point in session.query(Point)]

        id_assigner = IdAssigner(
            initial_value=max(
                max(image_id_values) if len(image_id_values) > 0 else 0,
                max(point_id_values) if len(point_id_values) > 0 else 0,
            )
        )
    else:
        id_assigner = IdAssigner(initial_value=initial_id)

    if verbose:
        logging.info("Converting dataframe started.")

    filepath_crop_counts = dict()
    for filepath, crop_labels in df[(df["type"] == "label") & (df["hit_class_name"] == HitClass.CROP.value)].groupby(
        "filepath"
    ):
        filepath_crop_counts[filepath] = len(crop_labels)

    filepath_weed_counts = dict()
    for filepath, weed_labels in df[(df["type"] == "label") & (df["hit_class_name"] == HitClass.WEED.value)].groupby(
        "filepath"
    ):
        filepath_weed_counts[filepath] = len(weed_labels)

    point_dups = 0
    for filepath, image_df in df.groupby("filepath"):
        image_points: Dict[Tuple[Any, ...], Point] = {}
        image_embeddings: Dict[Tuple[Any, ...], Embedding] = {}
        image = None
        for _, row in image_df.iterrows():
            image_key = row["filepath"]
            if image is None:
                image_id = rank + id_assigner.mkid() * 100
                image = Image(
                    id=image_id,
                    filepath=row["filepath"],
                    width=IMAGE_WIDTH,
                    height=IMAGE_HEIGHT,
                    ppi=PPI,
                    crop=row.get("crop"),
                    crop_id=row.get("crop_id"),
                    date=date.fromtimestamp(int(row["timestamp_ms"]) // 1000),
                    timestamp_ms=int(row["timestamp_ms"]),
                    robot_id=coalesce(row.get("robot_id"), ""),
                    row_id=coalesce(row.get("row_id"), ""),
                    cam_id=coalesce(row.get("cam_id"), ""),
                    session_name=coalesce(row.get("session_name"), ""),
                    geo=eval_if_str(coalesce(row.get("geo"), {})),
                    crop_count=filepath_crop_counts.get(image_key, 0),
                    weed_count=filepath_weed_counts.get(image_key, 0),
                    geohash=row.get("geohash"),
                )

                succeeded_adding_image = False
                for _ in range(10):
                    try:
                        session.add(image)
                        session.commit()
                        succeeded_adding_image = True
                        break
                    except:
                        session.rollback()

                if succeeded_adding_image and image_key in image_metrics:
                    image_mets = []
                    for item in image_metrics[image_key]:
                        image_mets.append(
                            ImageMetric(
                                image_id=image_id,
                                metric_name=item["metric_name"],
                                numerator=item["numerator"],
                                denominator=item["denominator"],
                                x_origin=item.get("x_origin", 0),
                                y_origin=item.get("y_origin", 0),
                                width=item.get("width", 0),
                                height=item.get("height", 0),
                                whole_image=item.get("whole_image", True),
                            )
                        )
                    for _ in range(10):
                        try:
                            session.bulk_save_objects(image_mets)
                            session.commit()
                            break
                        except:
                            session.rollback()

            # skip points on the border
            if tiling:
                x_max = tile_width - BORDER
                y_max = tile_height - BORDER
            else:
                x_max = IMAGE_WIDTH - BORDER
                y_max = IMAGE_HEIGHT - BORDER

            if row["x"] < BORDER or row["y"] < BORDER or row["x"] > x_max or row["y"] > y_max:
                continue

            # tile
            tile_x, tile_y = eval_if_str(row["tile_index"])

            # this avoids duplicates that are present for some reason
            image_point_key = (
                row["x"] + tile_x,
                row["y"] + tile_y,
                row["r"],
                row["score"],
                row["category_class_name"],
                row["hit_class_name"],
                row["type"],
            )
            transpose = row.get("transpose", False)
            if image_point_key not in image_points:
                pid = rank + id_assigner.mkid() * 100
                x_ = row["x"] + tile_x
                y_ = row["y"] + tile_y
                if transpose:
                    x_, y_ = y_, x_
                image_id_ = image.id
                image_points[image_point_key] = Point(
                    id=pid,
                    x=x_,
                    y=y_,
                    r_mm=px2mm(row["r"], ppi=image.ppi),
                    score=row["score"],
                    category_class_name=(
                        image.crop_id if row["category_class_name"] == "CROP" else row["category_class_name"]
                    ),
                    confidence=row["confidence"],
                    hit_class=HitClass(row["hit_class_name"]),
                    type=(PointType.PREDICTION if row["type"] == "prediction" else PointType.LABEL),
                    image_id=image_id_,
                    category_scores=row.get("category_scores"),
                    plant_score=row.get("plant_score"),
                    weed_score=row.get("weed_score"),
                    crop_score=row.get("crop_score"),
                    uuid=row.get("uuid"),
                    corrected_transpose=corrected_transpose,
                )

                if "embedding" in row and row["embedding"] != None:
                    image_embeddings[(image_id_, x_, y_, EmbeddingType.FULL)] = Embedding(
                        point_id=pid,
                        type=EmbeddingType.FULL,
                        embedding=row["embedding"],
                    )
                if "reduced_embedding" in row and row["reduced_embedding"] != None:
                    image_embeddings[(image_id_, x_, y_, EmbeddingType.REDUCED)] = Embedding(
                        point_id=pid,
                        type=EmbeddingType.REDUCED,
                        embedding=row["reduced_embedding"],
                    )
                if "reduced_scaled_embedding" in row and row["reduced_scaled_embedding"] != None:
                    image_embeddings[(image_id_, x_, y_, EmbeddingType.REDUCED_SCALED)] = Embedding(
                        point_id=pid,
                        type=EmbeddingType.REDUCED_SCALED,
                        embedding=row["reduced_scaled_embedding"],
                    )
            else:
                point_dups += 1
        for _ in range(10):
            try:
                session.bulk_save_objects(image_points.values())
                if len(image_embeddings) > 0:
                    session.bulk_save_objects(image_embeddings.values())
                session.commit()
                break
            except:
                session.rollback()

        if len(image_points.values()) == 0:
            continue

        point_pairs = []
        point_query_f = lambda p: (p.x, p.y)  # noqa
        point_list = list(image_points.values())
        points_kd = cKDTree([point_query_f(p) for p in point_list])
        point_pairs_set = set()
        for p1 in point_list:
            distance_search_mm = max(p1.r_mm, DISTANCE_UPPER_BOUND_MM)
            distance_search_px = mm2px(distance_search_mm, ppi=image.ppi)
            _, p2_indices = points_kd.query(
                point_query_f(p1), k=100, distance_upper_bound=distance_search_px
            )
            for p2_idx in p2_indices:
                if p2_idx == points_kd.n:
                    break

                p2 = point_list[p2_idx]
                distance_mm = px2mm(math.hypot(p1.x - p2.x, p1.y - p2.y), ppi=image.ppi)
                if p1.id != p2.id and distance_mm < distance_search_mm:
                    if (p1.id, p2.id) not in point_pairs_set:
                        point_pairs_set.add((p1.id, p2.id))
                        point_pairs.append(
                            PointPair(
                                point1_id=p1.id,
                                point2_id=p2.id,
                                distance_mm=distance_mm,
                            )
                        )
                    if (p2.id, p1.id) not in point_pairs_set:
                        point_pairs_set.add((p2.id, p1.id))
                        point_pairs.append(
                            PointPair(
                                point1_id=p2.id,
                                point2_id=p1.id,
                                distance_mm=distance_mm,
                            )
                        )

        for _ in range(10):
            try:
                session.bulk_save_objects(point_pairs)
                session.commit()
                break
            except:
                session.rollback()
    if verbose:
        logging.info(f"Loading complete. Duplicate points: {point_dups}")

    del df

    return id_assigner.current()

import pandas as pd
from sqlalchemy import and_

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class WeedsDetected(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = 2,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
    ) -> None:
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Weeds detected",
            description=f"""
                We say a weed is properly detected if we predict a weed within {distance_threshold_mm}mm the
                label at the specified weed point threshold. These are computed without any crop protection
                algorithms; they are simply weeds that the model predicted correctly at the given weed-point-threshold
                and distance threshold.
            """,
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        weed_detected_filters = [
            Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.PREDICTION,
                            Point.hit_class == HitClass.WEED,
                            Point.score > self.wpt,
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.LABEL,
            Point.hit_class == HitClass.WEED,
            Point.confidence == 2,
        ]

        return self._query_points(session=session, point_filters=point_filters, numerator_filters=weed_detected_filters)

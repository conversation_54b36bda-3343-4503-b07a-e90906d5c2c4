import os

import pandas
from sqlalchemy.engine import Engine

from .df_converter import convert_df
from .points_db import get_db, get_session
from .version import __version__


def get_db_from_directory(db_directory: str) -> Engine:
    db_path = f"{db_directory}/points_{__version__}.db"
    if os.path.exists(db_path):
        engine = get_db(db_path)
    else:
        dataframe_filepath = f"{db_directory}/dataframe.parquet"
        if os.path.exists(dataframe_filepath):
            df = pandas.read_parquet(dataframe_filepath)
            engine = get_db(db_path)
            session = get_session(engine)
            try:
                convert_df(df, session)
            finally:
                session.close()
        else:
            raise RuntimeError("Failed to create points database engine. SQLite file and dataframe file not found.")
    return engine

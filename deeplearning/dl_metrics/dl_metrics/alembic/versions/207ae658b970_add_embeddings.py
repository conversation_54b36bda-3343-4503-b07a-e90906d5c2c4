"""add embeddings

Revision ID: 207ae658b970
Revises:
Create Date: 2024-01-15 20:40:52.751768

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "207ae658b970"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "embeddings",
        sa.<PERSON>umn("point_id", sa.In<PERSON>ger(), nullable=False),
        sa.Column("type", sa.Enum("FULL", "REDUCED", name="embeddingtype"), nullable=False),
        sa.<PERSON>umn("embedding", sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(
            ["point_id"],
            ["points.id"],
        ),
        sa.PrimaryKeyConstraint("point_id", "type"),
    )
    op.create_index(op.f("ix_embeddings_point_id"), "embeddings", ["point_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_embeddings_point_id"), table_name="embeddings")
    op.drop_table("embeddings")
    # ### end Alembic commands ###

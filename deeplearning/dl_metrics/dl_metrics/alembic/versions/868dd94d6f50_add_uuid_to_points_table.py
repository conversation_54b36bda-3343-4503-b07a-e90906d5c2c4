"""add uuid to points table

Revision ID: 868dd94d6f50
Revises: 6d185726b231
Create Date: 2024-12-06 17:36:31.854389

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "868dd94d6f50"
down_revision = "6d185726b231"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.batch_alter_table("points") as batch_op:
        batch_op.add_column(sa.Column("uuid", sa.String(), nullable=True))
        batch_op.create_unique_constraint("uq_points_uuid", ["uuid"])


def downgrade() -> None:
    with op.batch_alter_table("points") as batch_op:
        batch_op.drop_constraint("uq_points_uuid", type_="unique")
        batch_op.drop_column("uuid")

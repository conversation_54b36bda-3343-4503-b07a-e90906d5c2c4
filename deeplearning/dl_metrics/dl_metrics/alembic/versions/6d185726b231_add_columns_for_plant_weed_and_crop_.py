"""add columns for plant weed and crop scores

Revision ID: 6d185726b231
Revises: b3eb42aaf6c5
Create Date: 2024-11-26 23:05:33.521022

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6d185726b231"
down_revision = "b3eb42aaf6c5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("points", sa.Column("plant_score", sa.Float(), nullable=True))
    op.add_column("points", sa.Column("weed_score", sa.Float(), nullable=True))
    op.add_column("points", sa.Column("crop_score", sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("points", "crop_score")
    op.drop_column("points", "weed_score")
    op.drop_column("points", "plant_score")
    # ### end Alembic commands ###

"""add image metrics

Revision ID: f98061ff1f83
Revises: 207ae658b970
Create Date: 2024-02-23 16:55:55.443789

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f98061ff1f83"
down_revision = "207ae658b970"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "image_metrics",
        sa.Column("image_id", sa.Integer(), nullable=False),
        sa.Column("metric_name", sa.String(), nullable=False),
        sa.Column("numerator", sa.Float(), nullable=False),
        sa.Column("denominator", sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(
            ["image_id"],
            ["images.id"],
        ),
        sa.PrimaryKeyConstraint("image_id", "metric_name"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("image_metrics")
    # ### end Alembic commands ###

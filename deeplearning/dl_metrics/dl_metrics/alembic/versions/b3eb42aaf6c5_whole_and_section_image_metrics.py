"""Whole and section image metrics

Revision ID: b3eb42aaf6c5
Revises: f98061ff1f83
Create Date: 2024-07-11 21:43:21.892985

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b3eb42aaf6c5"
down_revision = "f98061ff1f83"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("image_metrics", sa.Column("x_origin", sa.Integer(), nullable=False, server_default="0"))
    op.add_column("image_metrics", sa.Column("y_origin", sa.Integer(), nullable=False, server_default="0"))
    op.add_column("image_metrics", sa.Column("width", sa.Integer(), nullable=False, server_default="0"))
    op.add_column("image_metrics", sa.Column("height", sa.Integer(), nullable=False, server_default="0"))
    op.add_column("image_metrics", sa.Column("whole_image", sa.<PERSON>(), nullable=False, server_default="true"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("image_metrics", "whole_image")
    op.drop_column("image_metrics", "height")
    op.drop_column("image_metrics", "width")
    op.drop_column("image_metrics", "y_origin")
    op.drop_column("image_metrics", "x_origin")
    # ### end Alembic commands ###

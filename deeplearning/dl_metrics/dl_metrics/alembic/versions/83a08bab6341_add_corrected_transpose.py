"""empty message

Revision ID: 83a08bab6341
Revises: 868dd94d6f50
Create Date: 2024-12-19 05:15:11.098272

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "83a08bab6341"
down_revision = "868dd94d6f50"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("points", sa.Column("corrected_transpose", sa.<PERSON>(), nullable=False, server_default="false"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("points", "corrected_transpose")
    # ### end Alembic commands ###

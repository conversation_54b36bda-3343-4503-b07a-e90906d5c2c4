import copy
from abc import ABC, abstractmethod
from datetime import date
from typing import Any, List, Optional, Set, Tuple

import pandas as pd
from sqlalchemy import Integer, MetaData, Table, and_, cast, column, func, or_
from sqlalchemy.orm import Query

from . import defaults
from .metric_utils import crop_count_bin, size_bin, y_position_bin
from .points_db import Embedding, EmbeddingType, Image, ImageMetric, Point, Session


class Metric(ABC):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
        name: str = "<PERSON><PERSON>",
        description: str = "",
    ) -> None:
        self.crop_protection_radius_mm = crop_protection_radius_mm
        self.distance_threshold_mm = distance_threshold_mm
        self.wpt = wpt
        self.cpt = cpt
        self.plant_threshold = plant_threshold
        self.filter_date_range: Optional[Tuple[date, date]] = None
        self.filter_robot_ids: Optional[Set[str]] = None
        self.filter_crops: Optional[Set[str]] = None
        self.filter_session_names: Optional[Set[str]] = None
        self.filter_geohash_prefixes: Optional[Set[str]] = None
        self.filter_filepaths_to_exclude: Optional[Set[str]] = None
        self.filter_filepaths_to_include: Optional[Set[str]] = None
        self.filter_point_ids: Optional[Set[int]] = None
        self.filter_point_size: Optional[str] = None
        self.filter_embedding_bucket: Optional[Set[int]] = None
        self.filter_point_uuids: Optional[Set[str]] = None
        self.group_by_date = False
        self.group_by_size = False
        self.group_by_filepath = False
        self.group_by_weed_density = False
        self.group_by_crop_count = False
        self.group_by_robot_id = False
        self.group_by_category_class_name = False
        self.group_by_crop_id = False
        self.group_by_geohash = False
        self.group_by_image_id = False
        self.group_by_y_position = False
        self.group_by_point_id = False
        self.group_by_uuid = False
        self.group_by_geohash_prefix = None
        self.group_by_embedding_bucket = None
        self.name = name
        self.description = description

    @abstractmethod
    def __call__(self, session: Session) -> pd.DataFrame:
        """Returns numerators and denominators of metric as well as group by columns."""
        pass

    def docs(self) -> Optional[str]:
        return Metric.__init__.__doc__

    def filter(
        self,
        date_range: Optional[Tuple[date, date]] = None,
        robot_ids: Optional[Set[str]] = None,
        crops: Optional[Set[str]] = None,
        session_names: Optional[Set[str]] = None,
        geohash_prefixes: Optional[Set[str]] = None,
        filepaths_to_exclude: Optional[Set[str]] = None,
        filepaths_to_include: Optional[Set[str]] = None,
        filter_point_ids: Optional[Set[int]] = None,
        filter_point_uuids: Optional[Set[str]] = None,
        size: Optional[Tuple[float, float]] = None,
        embedding_bucket: Optional[Tuple[int]] = None,
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric.filter_date_range = date_range
        new_metric.filter_robot_ids = robot_ids
        new_metric.filter_crops = crops
        new_metric.filter_session_names = session_names
        new_metric.filter_geohash_prefixes = geohash_prefixes
        new_metric.filter_filepaths_to_exclude = filepaths_to_exclude
        new_metric.filter_filepaths_to_include = filepaths_to_include
        new_metric.filter_point_ids = filter_point_ids
        new_metric.filter_point_uuids = filter_point_uuids
        new_metric.filter_point_size = size
        new_metric.filter_embedding_bucket = embedding_bucket

        return new_metric

    def group_by(
        self,
        date: bool = False,
        size: bool = False,
        filepath: bool = False,
        crop_count: bool = False,
        robot_id: bool = False,
        category_class_name: bool = False,
        crop_id: bool = False,
        geohash: bool = False,
        image_id: bool = False,
        y_position: bool = False,
        uuid: bool = False,
        point_id: bool = False,
        embedding_bucket: Optional[List[int]] = None,
        geohash_prefix: Optional[int] = None,
    ) -> "Metric":
        new_metric = copy.deepcopy(self)
        new_metric.group_by_date = date
        new_metric.group_by_size = size
        new_metric.group_by_filepath = filepath
        new_metric.group_by_crop_count = crop_count
        new_metric.group_by_robot_id = robot_id
        new_metric.group_by_category_class_name = category_class_name
        new_metric.group_by_crop_id = crop_id
        new_metric.group_by_geohash = geohash
        new_metric.group_by_image_id = image_id
        new_metric.group_by_y_position = y_position
        new_metric.group_by_point_id = point_id
        new_metric.group_by_uuid = uuid
        new_metric.group_by_geohash_prefix = geohash_prefix
        new_metric.group_by_embedding_bucket = embedding_bucket
        return new_metric

    def _build_point_filters(self, embeddings_exist=False) -> List[Any]:
        point_filters = []
        if self.filter_point_ids:
            point_filters.append(Point.id.in_(self.filter_point_ids))
        if self.filter_point_size is not None:
            point_filters.append(Point.r_mm > self.filter_point_size[0])
            point_filters.append(Point.r_mm < self.filter_point_size[1])
        if self.filter_embedding_bucket and embeddings_exist:
            point_filters.append(column("embeddings_bucket").in_(self.filter_embedding_bucket))

        return point_filters

    def _build_image_filters(self) -> List[Any]:
        image_filters = []
        if self.filter_date_range:
            image_filters.append(Image.date >= self.filter_date_range[0])
            image_filters.append(Image.date <= self.filter_date_range[1])
        if self.filter_robot_ids:
            image_filters.append(Image.robot_id.in_(self.filter_robot_ids))
        if self.filter_crops:
            image_filters.append(Image.crop_id.in_(self.filter_crops))
        if self.filter_session_names:
            image_filters.append(Image.session_name.in_(self.filter_session_names))
        if self.filter_geohash_prefixes:
            image_filters.append(or_(*[Image.geohash.startswith(gh) for gh in self.filter_geohash_prefixes]))
        if self.filter_filepaths_to_include:
            image_filters.append(Image.filepath.in_(self.filter_filepaths_to_include))
        if self.filter_filepaths_to_exclude:
            image_filters.append(Image.filepath.notin_(self.filter_filepaths_to_exclude))

        return image_filters

    def _build_group_by_entities_and_labels(self, embeddings_exist=False) -> Tuple[List[Any], List[Any]]:
        group_by_entities = []
        group_by_labels = []
        if self.group_by_date:
            group_by_entities.append(Image.date.label("date"))
            group_by_labels.append("date")
        if self.group_by_size:
            group_by_entities.append(size_bin(Point.r_mm).label("size"))
            group_by_labels.append("size")
        if self.group_by_y_position:
            group_by_entities.append(y_position_bin(Point.y).label("y_position"))
            group_by_labels.append("y_position")
        if self.group_by_crop_count:
            group_by_entities.append(crop_count_bin(Image.crop_count).label("crop_count"))
            group_by_labels.append("crop_count")
        if self.group_by_filepath:
            group_by_entities.append(Image.filepath.label("filepath"))
            group_by_labels.append("filepath")
        if self.group_by_robot_id:
            group_by_entities.append(Image.robot_id.label("robot_id"))
            group_by_labels.append("robot_id")
        if self.group_by_category_class_name:
            group_by_entities.append(Point.category_class_name.label("category_class_name"))
            group_by_labels.append("category_class_name")
        if self.group_by_crop_id:
            group_by_entities.append(Image.crop_id.label("crop_id"))
            group_by_labels.append("crop_id")
        if self.group_by_geohash:
            group_by_entities.append(Image.geohash.label("geohash"))
            group_by_labels.append("geohash")
        if self.group_by_image_id:
            group_by_entities.append(Image.id.label("image_id"))
            group_by_labels.append("image_id")
        if self.group_by_point_id:
            group_by_entities.append(Point.id.label("point_id"))
            group_by_labels.append("point_id")
        if self.group_by_geohash_prefix:
            group_by_entities.append(
                func.substr(Image.geohash, 1, self.group_by_geohash_prefix).label("geohash_prefix")
            )
            group_by_labels.append("geohash_prefix")
        if self.group_by_embedding_bucket and embeddings_exist:
            x_size, y_size = self.group_by_embedding_bucket
            query = (
                func.cast(func.max(func.min(Embedding.embedding[0] * x_size, x_size - 1), 0), Integer)
                + x_size * func.cast(func.max(func.min(Embedding.embedding[1] * y_size, y_size - 1), 0), Integer)
            ).label("embeddings_bucket")

            group_by_entities.append(query)
            group_by_labels.append("embeddings_bucket")
        if self.group_by_uuid:
            group_by_entities.append(Point.id.label("uuid"))
            group_by_labels.append("uuid")

        return group_by_entities, group_by_labels

    def _filter_uuids_by_join(self, query: Query, session: Session) -> Query:
        if self.filter_point_uuids:
            uuid_df = pd.DataFrame({"tmp_uuid": list(self.filter_point_uuids)})
            uuid_df.to_sql("tmp_uuid_filter", session.bind, if_exists="replace", index=False)
            tmp_table = Table("tmp_uuid_filter", MetaData(), autoload_with=session.bind)
            query = query.join(tmp_table, Point.uuid == tmp_table.c.tmp_uuid)

        return query

    def _query_points(  # noqa: C901
        self, session: Session, point_filters: List[Any], numerator_filters: List[Any]
    ) -> pd.DataFrame:
        embeddings_exist = session.query(Embedding).count() > 0

        group_by_entities, group_by_labels = self._build_group_by_entities_and_labels(embeddings_exist)
        image_filters = self._build_image_filters()
        point_filters_ = self._build_point_filters(embeddings_exist)

        point_filters = point_filters + point_filters_
        query = session.query(
            *group_by_entities,
            func.sum(cast(and_(*numerator_filters), Integer)).label("numerator"),
            func.count(Point.id).label("denominator"),
        ).select_from(Point)
        # joining the embeddings if the embeddings exist
        if self.group_by_embedding_bucket and embeddings_exist:
            query = query.join(
                Embedding,
                and_(Embedding.point_id == Point.id, Embedding.type == EmbeddingType.REDUCED_SCALED),
                isouter=True,
            )

        query = query.join(Point.image)
        query = self._filter_uuids_by_join(session=session, query=query)

        # group by at the end
        query = query.group_by(*group_by_labels).filter(*image_filters).filter(*point_filters)

        return pd.read_sql_query(query.statement, session.bind)

    def _query_image_metrics(
        self,
        session: Session,
        metric_names: List[str] = [],
        min_denom_value: Optional[int] = None,
        max_denom_value: Optional[int] = None,
    ) -> pd.DataFrame:
        group_by_entities, group_by_labels = self._build_group_by_entities_and_labels()
        image_filters = self._build_image_filters()

        query = (
            session.query(
                *group_by_entities,
                func.sum(ImageMetric.numerator).label("numerator"),
                func.sum(ImageMetric.denominator).label("denominator"),
            )
            .select_from(ImageMetric)
            .join(Image, ImageMetric.image_id == Image.id)
        )

        if len(metric_names) > 0:
            query = query.filter(ImageMetric.metric_name.in_(metric_names))

        if min_denom_value is not None:
            query = query.filter(ImageMetric.denominator >= min_denom_value)
        if max_denom_value is not None:
            query = query.filter(ImageMetric.denominator <= max_denom_value)

        # Only return items where the metric represents whole image
        query = query.filter(ImageMetric.whole_image.is_(True))

        query = query.filter(*image_filters).group_by(ImageMetric.metric_name).group_by(*group_by_labels)

        return pd.read_sql_query(query.statement, session.bind)

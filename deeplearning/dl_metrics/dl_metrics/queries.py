import json
from collections import defaultdict
from typing import Any, Dict, List, <PERSON><PERSON>

from sqlalchemy import and_

from .defaults import DISTANCE_THRESHOLD_MM, MIN_PLANT_SCORE

from .points_db import HitClass, Image, Point, PointPair, PointType, get_db, get_session


def get_labeled_points(
    points_db_file: str,
) -> List[Any]:
    with get_session(get_db(points_db_file)) as sess:
        labels = (
            sess.query(
                Point.id.label("id"),
                Image.filepath.label("filepath"),
                Image.geohash.label("geohash"),
                Point.x.label("point_x"),
                Point.y.label("point_y"),
                Point.uuid.label("uuid"),
            )
            .join(Image, Point.image_id == Image.id)
            .filter(Point.confidence == 2)
            .filter(Point.type == PointType.LABEL)
            .filter(Point.hit_class != HitClass.PLANT)
            .all()
        )

    return labels


def get_labels_and_matched_plant_predictions(
    points_db_file: str,
    distance_threshold_mm: float = DISTANCE_THRESHOLD_MM,
    min_score: float = MIN_PLANT_SCORE,
) -> Tuple[List[Any], Dict[int, List[Dict[str, Any]]]]:
    with get_session(get_db(points_db_file)) as sess:
        plant_prediction_query = (
            sess.query(
                Point.id.label("prediction_id"),
                Point.plant_score.label("prediction_plant_score"),
                Point.weed_score.label("prediction_weed_score"),
                Point.crop_score.label("prediction_crop_score"),
                Point.category_scores.label("prediction_category_scores"),
                Point.uuid.label("prediction_uuid"),
            )
            .filter(Point.type == PointType.PREDICTION)
            .filter(Point.hit_class == HitClass.PLANT)
            .filter(Point.plant_score >= min_score)
        )

        plant_predictions_subquery = plant_prediction_query.subquery()

        embeddings_with_neighbors = (
            sess.query(
                Point.id.label("id"),
                Image.filepath.label("filepath"),
                Image.geohash.label("geohash"),
                Image.crop_id.label("crop_id"),
                Point.x.label("point_x"),
                Point.y.label("point_y"),
                Point.r_mm.label("point_r_mm"),
                Point.category_class_name.label("category_class_name"),
                Image.timestamp_ms.label("timestamp_ms"),
                Point.uuid,
                plant_predictions_subquery.c.prediction_id,
                plant_predictions_subquery.c.prediction_plant_score,
                plant_predictions_subquery.c.prediction_weed_score,
                plant_predictions_subquery.c.prediction_crop_score,
                plant_predictions_subquery.c.prediction_category_scores,
                plant_predictions_subquery.c.prediction_uuid,
                PointPair.distance_mm,
            )
            .join(Image, Point.image_id == Image.id)
            .filter(Point.confidence == 2)
            .filter(Point.type == PointType.LABEL)
            .filter(Point.hit_class != HitClass.PLANT)
            .join(
                PointPair,
                and_(
                    PointPair.point1_id == Point.id,
                    PointPair.distance_mm < distance_threshold_mm,
                ),
                isouter=True,
            )
            .join(
                plant_predictions_subquery,
                plant_predictions_subquery.c.prediction_id == PointPair.point2_id,
                isouter=True,
            )
            .all()
        )

    matched_plant_predictions = defaultdict(list)
    items = {}
    for item in embeddings_with_neighbors:
        item_dict = dict(item)
        items[item.id] = item

        if item.prediction_id is not None:
            category_scores = {k.lower(): v for k, v in json.loads(item.prediction_category_scores).items()}
            matched_plant_predictions[item_dict["id"]].append(
                {
                    "id": item.prediction_id,
                    "plant_score": item.prediction_plant_score,
                    "weed_score": item.prediction_weed_score,
                    "crop_score": item.prediction_crop_score,
                    "category_scores": category_scores,
                    "distance_mm": item.distance_mm,
                    "uuid": item.prediction_uuid,
                }
            )

    return list(items.values()), matched_plant_predictions

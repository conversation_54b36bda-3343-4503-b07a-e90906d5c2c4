import enum
import logging
import os
import sqlite3
from typing import Any, Callable, Dict, List

from alembic import command
from alembic.config import Config
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    Date,
    Enum,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    PrimaryKeyConstraint,
    String,
    create_engine,
    event,
    func,
)
from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship

from .version import __version__

logging.getLogger("alembic.runtime.migration").setLevel(logging.WARN)

__all__ = [
    "Engine",
    "HitClass",
    "Image",
    "PointType",
    "Session",
    "Point",
    "PointPair",
    "ImageMetric",
    "copy_db",
    "get_db",
    "get_session",
    "simple_copy_db",
    "__version__",
]

Base = declarative_base()


def run_alembic(db_path: str, callback: Callable[[Config], None]) -> None:
    script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "alembic"))
    cfg = Config()
    cfg.set_main_option("script_location", script_path)
    cfg.set_main_option("sqlalchemy.url", f"sqlite:///{db_path}")
    callback(cfg)


def get_db(db_path: str) -> Engine:
    if os.path.exists(db_path):
        run_alembic(db_path, lambda cfg: command.upgrade(cfg, "head"))

    engine = create_engine(f"sqlite:///{db_path}?_pragma=busy_timeout(5000)")
    Base.metadata.create_all(engine)
    run_alembic(db_path, lambda cfg: command.stamp(cfg, "head"))

    def _fk_pragma_on_connect(dbapi_connection: Engine, connection_record: Any) -> None:
        dbapi_connection.execute("pragma foreign_keys=ON")

    event.listen(engine, "connect", _fk_pragma_on_connect)

    return engine


def get_session(engine: Engine) -> Session:
    session = Session(engine)
    return session


def get_overlapping_filepaths(session: Session, filepaths: List[str]) -> List[str]:
    return [image.filepath for image in session.query(Image.filepath).filter(Image.filepath.in_(filepaths)).all()]


def get_distinct_filepaths(session: Session) -> List[str]:
    return [image.filepath for image in session.query(Image.filepath).distinct()]


def get_points_for_file(session: Session, filepath: str) -> List[Dict[str, Any]]:
    image_ids = session.query(Image.id).filter(Image.filepath == filepath).all()
    image_id = image_ids[0]
    points = session.query(Point).filter(Point.image_id == image_id.id).all()
    return [point.to_dict() for point in points]


def simple_copy_db(source_path: str, dest_path: str) -> None:
    """Simple copy DB without remapping IDs."""
    assert os.path.exists(source_path)

    # run alembic migrations
    get_db(source_path)
    get_db(dest_path)

    with sqlite3.connect(dest_path) as dest_conn:
        dest_conn.execute(f"ATTACH DATABASE '{source_path}' AS source")
        dest_conn.execute("INSERT INTO images SELECT * FROM source.images")
        dest_conn.execute("INSERT INTO image_metrics SELECT * FROM source.image_metrics")
        dest_conn.execute("INSERT INTO points SELECT * FROM source.points")
        dest_conn.execute("INSERT INTO point_pairs SELECT * FROM source.point_pairs")
        dest_conn.execute("INSERT INTO embeddings SELECT * FROM source.embeddings")
        dest_conn.execute("COMMIT")
        dest_conn.execute("DETACH DATABASE source")


def copy_db(source_session: Session, dest_session: Session) -> None:
    source_images = source_session.query(Image).all()
    source_points = source_session.query(Point).all()
    source_point_pairs = source_session.query(PointPair).all()
    source_embeddings = source_session.query(Embedding).all()
    source_image_metrics = source_session.query(ImageMetric).all()

    max_image_id = dest_session.query(func.max(Image.id)).scalar() or 0
    max_point_id = dest_session.query(func.max(Point.id)).scalar() or 0
    id = max(max_image_id, max_point_id)

    images_to_copy = []
    points_to_copy = []
    point_pairs_to_copy = []
    embeddings_to_copy = []
    image_metrics_to_copy = []

    old_to_new_image_ids = {}
    for source_image in source_images:
        id = id + 1
        old_to_new_image_ids[source_image.id] = id
        copy_source = source_image.copy(id)
        images_to_copy.append(copy_source)

    old_to_new_point_ids = {}
    for source_point in source_points:
        id = id + 1
        old_to_new_point_ids[source_point.id] = id
        copy_source = source_point.copy(id, old_to_new_image_ids[source_point.image_id])
        points_to_copy.append(copy_source)

    for source_point_pair in source_point_pairs:
        p1_id = old_to_new_point_ids[source_point_pair.point1_id]
        p2_id = old_to_new_point_ids[source_point_pair.point2_id]
        copy_source = source_point_pair.copy(p1_id, p2_id)
        point_pairs_to_copy.append(copy_source)

    for source_embedding in source_embeddings:
        id = old_to_new_point_ids[source_embedding.point_id]
        copy_source = source_embedding.copy(id)
        embeddings_to_copy.append(copy_source)

    for source_image_metric in source_image_metrics:
        id = old_to_new_image_ids[source_image_metric.image_id]
        copy_source = source_image_metric.copy(id)
        image_metrics_to_copy.append(copy_source)

    dest_session.bulk_save_objects(images_to_copy)
    dest_session.bulk_save_objects(points_to_copy)
    dest_session.bulk_save_objects(point_pairs_to_copy)
    dest_session.bulk_save_objects(embeddings_to_copy)
    dest_session.bulk_save_objects(image_metrics_to_copy)
    dest_session.commit()


class HitClass(enum.Enum):
    WEED = 0
    CROP = 1
    PLANT = 2


class PointType(enum.Enum):
    LABEL = 0
    PREDICTION = 1


class Image(Base):  # type: ignore
    __tablename__ = "images"

    id = Column(Integer, primary_key=True)
    filepath = Column(String, nullable=False, unique=True)
    width = Column(Integer, nullable=False)
    height = Column(Integer, nullable=False)
    ppi = Column(Float, nullable=False)
    crop = Column(String, nullable=True)
    crop_id = Column(String, nullable=True)
    date = Column(Date, nullable=False)
    timestamp_ms = Column(Integer, nullable=False)
    robot_id = Column(String, nullable=False)
    row_id = Column(String, nullable=False)
    cam_id = Column(String, nullable=False)
    session_name = Column(String, nullable=False)
    geo = Column(JSON, nullable=False)
    crop_count = Column(Integer, nullable=False)
    weed_count = Column(Integer, nullable=False)
    geohash = Column(String, nullable=True)

    points = relationship("Point", back_populates="image")

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def copy(self, id: int) -> "Image":
        old_params = {column.key: getattr(self, column.key) for column in self.__table__.columns}
        del old_params["id"]
        image = Image(id=id, **old_params)
        return image


class Point(Base):  # type: ignore
    __tablename__ = "points"

    id = Column(Integer, primary_key=True)
    image_id = Column(Integer, ForeignKey("images.id"), nullable=False)
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    r_mm = Column(Float, nullable=False)
    score = Column(Float, nullable=False)
    category_class_name = Column(String, nullable=False)
    confidence = Column(Integer, nullable=False)
    hit_class = Column(Enum(HitClass), nullable=False)
    type = Column(Enum(PointType), nullable=False)
    category_scores = Column(JSON, nullable=True)
    plant_score = Column(Float, nullable=True)
    weed_score = Column(Float, nullable=True)
    crop_score = Column(Float, nullable=True)
    uuid = Column(String, nullable=True, unique=True)
    corrected_transpose = Column(Boolean, nullable=False, default=False)

    image = relationship("Image", back_populates="points")
    pairs = relationship(
        "PointPair",
        back_populates="point1",
        primaryjoin="Point.id == PointPair.point1_id",
    )
    embeddings = relationship("Embedding", back_populates="points", primaryjoin="Point.id == Embedding.point_id")

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def copy(self, id: int, image_id: int) -> "Point":
        old_params = {column.key: getattr(self, column.key) for column in self.__table__.columns}
        del old_params["id"]
        del old_params["image_id"]
        point = Point(id=id, image_id=image_id, **old_params)
        return point


class PointPair(Base):  # type: ignore
    __tablename__ = "point_pairs"

    point1_id = Column(Integer, ForeignKey("points.id"), nullable=False, index=True)
    point2_id = Column(Integer, ForeignKey("points.id"), nullable=False)
    distance_mm = Column(Float, nullable=False)

    point1 = relationship("Point", foreign_keys=[point1_id])
    point2 = relationship("Point", foreign_keys=[point2_id])

    __table_args__ = (PrimaryKeyConstraint(point1_id, point2_id),)

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def copy(self, id1: int, id2: int) -> "PointPair":
        old_params = {column.key: getattr(self, column.key) for column in self.__table__.columns}
        del old_params["point1_id"]
        del old_params["point2_id"]
        point_pair = PointPair(point1_id=id1, point2_id=id2, **old_params)
        return point_pair


class EmbeddingType(enum.Enum):
    FULL = 0
    REDUCED = 1
    REDUCED_SCALED = 2


class Embedding(Base):  # type: ignore
    __tablename__ = "embeddings"
    point_id = Column(Integer, ForeignKey("points.id"), nullable=False, index=True)
    type = Column(Enum(EmbeddingType), nullable=False)
    embedding = Column(JSON, nullable=False)

    points = relationship("Point", back_populates="embeddings", primaryjoin="Embedding.point_id == Point.id")

    __table_args__ = (PrimaryKeyConstraint(point_id, type),)

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def copy(self, id: int) -> "Embedding":
        old_params = {column.key: getattr(self, column.key) for column in self.__table__.columns}
        del old_params["point_id"]
        embeddings = Embedding(point_id=id, **old_params)
        return embeddings

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


class ImageMetric(Base):  # type: ignore
    __tablename__ = "image_metrics"
    image_id = Column(Integer, ForeignKey("images.id"), nullable=False)
    metric_name = Column(String, nullable=False)
    numerator = Column(Float, nullable=False)
    denominator = Column(Float, nullable=False)
    x_origin = Column(Integer, nullable=False)
    y_origin = Column(Integer, nullable=False)
    width = Column(Integer, nullable=False)
    height = Column(Integer, nullable=False)
    whole_image = Column(Boolean, nullable=False)

    __table_args__ = (PrimaryKeyConstraint(image_id, metric_name, whole_image, x_origin, y_origin, height, width),)

    def __repr__(self) -> str:
        return str({column.key: getattr(self, column.key) for column in self.__table__.columns})

    def copy(self, image_id: int) -> "ImageMetric":
        old_params = {column.key: getattr(self, column.key) for column in self.__table__.columns}
        del old_params["image_id"]
        embeddings = ImageMetric(image_id=image_id, **old_params)
        return embeddings

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}

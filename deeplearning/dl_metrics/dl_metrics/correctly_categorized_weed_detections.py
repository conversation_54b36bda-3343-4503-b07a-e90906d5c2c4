import pandas as pd
from sqlalchemy import and_
from sqlalchemy.orm import aliased

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class CorrectlyCategorizedWeedDetections(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = 2,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
    ) -> None:
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Correctly categorized weed detections",
            description="""
                Percentage of correctly categorized weed detections among
                the weed detections that have the corresponding label.
            """,
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        inner_point = aliased(Point, name="inner_point")

        correct_category_filters = [
            Point.pairs.any(
                and_(
                    PointPair.point2.of_type(inner_point).has(
                        and_(
                            inner_point.type == PointType.LABEL,
                            inner_point.hit_class == HitClass.WEED,
                            inner_point.confidence == 2,
                            inner_point.category_class_name == Point.category_class_name,
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.PREDICTION,
            Point.hit_class == HitClass.WEED,
            Point.score > self.wpt,
            Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.LABEL,
                            Point.hit_class == HitClass.WEED,
                            Point.confidence == 2,
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        return self._query_points(
            session=session, point_filters=point_filters, numerator_filters=correct_category_filters
        )

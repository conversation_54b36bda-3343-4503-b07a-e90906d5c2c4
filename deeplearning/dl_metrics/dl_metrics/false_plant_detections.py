import pandas as pd
from sqlalchemy import and_

from . import defaults
from .metric import Metric
from .points_db import Point, PointPair, PointType, Session


class FalsePlantDetections(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
    ):
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="False plant detections",
            description="""Plant detections where there is no label.""",
        )

    def __call__(self, session: Session) -> pd.DataFrame:
        no_plant_label_filters = [
            ~Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.LABEL,
                            PointPair.distance_mm < self.distance_threshold_mm,
                        )
                    ),
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.PREDICTION,
            Point.score > self.plant_threshold,
            ~Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.LABEL,
                            Point.confidence == 0,
                            PointPair.distance_mm < self.distance_threshold_mm,
                        )
                    ),
                )
            ),
        ]

        return self._query_points(
            session=session, point_filters=point_filters, numerator_filters=no_plant_label_filters
        )

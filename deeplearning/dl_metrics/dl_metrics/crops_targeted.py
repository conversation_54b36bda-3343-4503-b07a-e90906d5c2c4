from typing import List, Optional

import pandas as pd
from sqlalchemy import and_

from . import defaults
from .metric import Metric
from .points_db import HitClass, Point, PointPair, PointType, Session


class CropsTargeted(Metric):
    def __init__(
        self,
        crop_protection_radius_mm: float = defaults.CROP_PROTECTION_RADIUS_MM,
        distance_threshold_mm: float = defaults.DISTANCE_THRESHOLD_MM,
        wpt: float = defaults.WPT,
        cpt: float = defaults.CPT,
        plant_threshold: float = defaults.PLANT_THRESHOLD,
        included_predicted_category_class_names: Optional[List[str]] = None,
        excluded_predicted_category_class_names: Optional[List[str]] = None,
        detected_as_plants: bool = False,
        use_max_score: bool = False,
    ):
        super().__init__(
            crop_protection_radius_mm=crop_protection_radius_mm,
            distance_threshold_mm=distance_threshold_mm,
            wpt=wpt,
            cpt=cpt,
            plant_threshold=plant_threshold,
            name="Crops targeted",
            description=f"""
                We say a crop is targeted if we predict a weed within {distance_threshold_mm}mm of the crop label
                at the specified weed point threshold and don't predict a crop within the crop protection radius
                at the specified crop point threshold.
            """,
        )

        self.included_predicted_category_class_names = included_predicted_category_class_names
        self.excluded_predicted_category_class_names = excluded_predicted_category_class_names
        self.detected_as_plants = detected_as_plants
        self.use_max_score = use_max_score

        if self.use_max_score:
            self.name += " using max score"
        if self.detected_as_plants:
            self.name += " that were also detected as plants"
        if self.included_predicted_category_class_names:
            self.name += f" (including {', '.join(self.included_predicted_category_class_names)} predictions)"
        if self.excluded_predicted_category_class_names:
            self.name += f" (excluding {', '.join(self.excluded_predicted_category_class_names)} predictions)"

    def __call__(self, session: Session) -> pd.DataFrame:
        predicted_category_class_name_filters = []
        if self.included_predicted_category_class_names:
            predicted_category_class_name_filters.append(
                Point.category_class_name.in_(self.included_predicted_category_class_names)
            )
        if self.excluded_predicted_category_class_names:
            predicted_category_class_name_filters.append(
                Point.category_class_name.not_in(self.excluded_predicted_category_class_names)
            )

        if self.use_max_score:
            weed_prediction_filters = [
                Point.hit_class == HitClass.PLANT,
                Point.weed_score > Point.crop_score,
                Point.plant_score > self.plant_threshold,
            ]
            crop_prediction_filters = [
                Point.hit_class == HitClass.PLANT,
                Point.weed_score < Point.crop_score,
                Point.plant_score > self.plant_threshold,
            ]
        else:
            weed_prediction_filters = [
                Point.hit_class == HitClass.WEED,
                Point.score > self.wpt,
            ]
            crop_prediction_filters = [
                Point.hit_class == HitClass.CROP,
                Point.score > self.cpt,
            ]

        crop_targeted_filters = [
            Point.pairs.any(
                and_(
                    PointPair.point2.has(
                        and_(
                            Point.type == PointType.PREDICTION,
                            *weed_prediction_filters,
                            *predicted_category_class_name_filters,
                            ~Point.pairs.any(
                                and_(
                                    PointPair.point2.has(
                                        and_(
                                            Point.type == PointType.PREDICTION,
                                            *crop_prediction_filters,
                                        )
                                    ),
                                    PointPair.distance_mm < self.crop_protection_radius_mm,
                                )
                            ),
                        )
                    ),
                    PointPair.distance_mm < self.distance_threshold_mm,
                )
            ),
        ]

        point_filters = [
            Point.type == PointType.LABEL,
            Point.hit_class == HitClass.CROP,
            Point.confidence == 2,
        ]

        if self.detected_as_plants:
            point_filters += [
                Point.pairs.any(
                    and_(
                        PointPair.point2.has(
                            and_(
                                Point.type == PointType.PREDICTION,
                                Point.hit_class == HitClass.PLANT,
                                Point.score > self.plant_threshold,
                            )
                        ),
                        PointPair.distance_mm < self.distance_threshold_mm,
                    )
                ),
            ]

        return self._query_points(session=session, point_filters=point_filters, numerator_filters=crop_targeted_filters)

import functools
import logging
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, cast

import numpy as np
import torch
import wandb
from lightning.pytorch.loggers import Wandb<PERSON>ogger
from sqlalchemy import and_
from sqlalchemy.sql.expression import func
from torch.utils.data import DataLoader
from umap.umap_ import find_ab_params

import deeplearning.dl_metrics as dl_metrics
from deeplearning.comparison.data_utils import ComparisonEmbeddingObject, embeddings_to_dataframes
from deeplearning.comparison.version import get_version
from deeplearning.embeddings.io import EmbeddingLookupTables
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.pytorch import load_pytorch_model
from deeplearning.parametric_umap.graph import Graph, PUMAPDataset, get_graph_elements, get_umap_graph
from deeplearning.parametric_umap.loss import umap_loss
from deeplearning.parametric_umap.metrics import AverageMetrics, Metrics
from deeplearning.parametric_umap.model import ParametricUMAP
from deeplearning.utils.tensor import broadcast_object, gather_objects
from deeplearning.utils.trainer import Trainer, TrainingModule, worker_init_fn
from deeplearning.utils.use_cases import ModelUseCase
from lib.common.collections.list import flatten
from lib.common.time.sleep import sleep_ms

LOG = logging.getLogger(__name__)


def collate_fn(data: List[Tuple[torch.Tensor, torch.Tensor, Dict[str, Any], Dict[str, Any]]]) -> Any:
    batch = (
        torch.stack([d[0] for d in data], dim=0),
        torch.stack([d[1] for d in data], dim=0),
        [d[2] for d in data],
        [d[3] for d in data],
    )

    return batch


class PUMAPTrainingModule(TrainingModule):
    def __init__(
        self,
        lr: float,
        model: ParametricUMAP,
        batch_size: int,
        train_dataset: PUMAPDataset,
        validation_dataset: PUMAPDataset,
        test_dataset: PUMAPDataset,
        min_dist: float = 0.1,
        lr_milestones: Optional[List[int]] = None,
        lr_gamma: float = 0.1,
    ):
        super().__init__()
        lr_milestones = lr_milestones if lr_milestones is not None else [5, 10, 15, 20, 25, 30, 40]
        self.lr = lr
        self._model = model
        self.a, self.b = find_ab_params(1.0, min_dist)
        self._validation_outputs: List[Metrics] = []
        self._test_outputs: List[Metrics] = []
        self._train_outputs: List[Metrics] = []
        self._batch_size = batch_size
        self._num_workers = 1
        self._experiment_url: Optional[str] = None

        self._train_dataset = train_dataset
        self._validation_dataset = validation_dataset
        self._test_dataset = test_dataset
        self._lr_milestones = lr_milestones
        self._lr_gamma = lr_gamma
        self._points_db: Optional[str] = None

        self._tested_embedding_point_ids: Set[int] = set()

    @property
    def use_cases(self) -> List[ModelUseCase]:
        return [ModelUseCase.PARAMETRIC_UMAP]

    def configure_optimizers(self):  # type: ignore
        self.optimizer = torch.optim.AdamW(self.parameters(), lr=self.lr)

        if len(self._lr_milestones) > 0:
            scheduler = torch.optim.lr_scheduler.MultiStepLR(
                self.optimizer, milestones=self._lr_milestones, gamma=self._lr_gamma
            )
            return [self.optimizer], [scheduler]

        return self.optimizer

    def compute_metrics(
        self, batch: Tuple[torch.Tensor, torch.Tensor, List[Dict[str, Any]], List[Dict[str, Any]]]
    ) -> Tuple[torch.Tensor, Metrics, torch.Tensor, torch.Tensor, List[Dict[str, Any]], List[Dict[str, Any]]]:
        (edges_to_exp, edges_from_exp, labels_to, labels_from) = batch
        embedding_to = self._model(edges_to_exp.cuda())
        embedding_from = self._model(edges_from_exp.cuda())
        loss = umap_loss(embedding_to, embedding_from, self.a, self.b, edges_to_exp.shape[0], negative_sample_rate=5)
        metrics = Metrics(loss=loss).detach().cpu()
        return loss, metrics, embedding_to.detach().cpu(), embedding_from.detach().cpu(), labels_to, labels_from

    def get_worker_init_fn(self) -> Callable[[int], None]:
        num_workers = self._num_workers
        return functools.partial(worker_init_fn, num_workers)

    def train_dataloader(self) -> DataLoader[Tuple[torch.Tensor, torch.Tensor, Dict[str, Any], Dict[str, Any]]]:
        sampler: torch.utils.data.Sampler[Any] = torch.utils.data.distributed.DistributedSampler(
            self._train_dataset,
            num_replicas=torch.distributed.get_world_size(),
            rank=torch.distributed.get_rank(),
            shuffle=True,
        )
        return DataLoader(
            dataset=self._train_dataset,
            batch_size=self._batch_size,
            num_workers=self._num_workers,
            sampler=sampler,
            collate_fn=collate_fn,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def val_dataloader(self) -> DataLoader[Tuple[torch.Tensor, torch.Tensor, Dict[str, Any], Dict[str, Any]]]:
        sampler: torch.utils.data.Sampler[Any] = torch.utils.data.distributed.DistributedSampler(
            self._validation_dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank()
        )
        return DataLoader(
            dataset=self._validation_dataset,
            batch_size=self._batch_size,
            num_workers=self._num_workers,
            sampler=sampler,
            collate_fn=collate_fn,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def test_dataloader(self) -> DataLoader[Tuple[torch.Tensor, torch.Tensor, Dict[str, Any], Dict[str, Any]]]:
        sampler: torch.utils.data.Sampler[Any] = torch.utils.data.distributed.DistributedSampler(
            self._test_dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank()
        )
        return DataLoader(
            dataset=self._test_dataset,
            batch_size=self._batch_size,
            num_workers=self._num_workers,
            sampler=sampler,
            collate_fn=collate_fn,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor, List[Dict[str, Any]], List[Dict[str, Any]]], batch_idx: int
    ) -> torch.Tensor:
        loss, metrics, _, _, _, _ = self.compute_metrics(batch)
        self._train_outputs.append(metrics)
        self.log_dict(
            metrics.detach().cpu().to_dict(prefix="pumap_train_step_"),
            logger=False,
            prog_bar=True,
            on_epoch=False,
            on_step=True,
        )
        return loss

    @torch.no_grad()
    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor, List[Dict[str, Any]], List[Dict[str, Any]]], batch_idx: int
    ) -> torch.Tensor:
        loss, metrics, _, _, _, _ = self.compute_metrics(batch)
        self._validation_outputs.append(metrics)
        return loss

    @torch.no_grad()
    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor, List[Dict[str, Any]], List[Dict[str, Any]]], batch_idx: int
    ) -> torch.Tensor:
        loss, metrics, embedding_to, embedding_from, labels_to, labels_from = self.compute_metrics(batch)
        self._test_outputs.append(metrics)
        embedding_type = (
            dl_metrics.EmbeddingType.REDUCED if self._model._scaler is None else dl_metrics.EmbeddingType.REDUCED_SCALED
        )
        if self._points_db is not None:
            save_embedding_items = [(embedding_to, embedding_from, labels_to, labels_from)]
            all_save_embedding_items = gather_objects(save_embedding_items)
            if torch.distributed.get_rank() == 0:
                embeddings_to_add: Dict[int, Any] = {}
                point_ids_to_add: Set[int] = set()

                embedding_to = torch.cat([item[0] for item in all_save_embedding_items])
                embedding_from = torch.cat([item[1] for item in all_save_embedding_items])
                labels_to = flatten([item[2] for item in all_save_embedding_items])
                labels_from = flatten([item[3] for item in all_save_embedding_items])

                for i in range(embedding_to.shape[0]):
                    emb_to = embedding_to[i]
                    emb_from = embedding_from[i]
                    label_to = labels_to[i]
                    label_from = labels_from[i]

                    if (
                        label_to["point_id"] not in self._tested_embedding_point_ids
                        and label_to["point_id"] not in point_ids_to_add
                    ):
                        embeddings_to_add[label_to["point_id"]] = dl_metrics.Embedding(
                            point_id=label_to["point_id"],
                            type=embedding_type,
                            embedding=emb_to.detach().cpu().tolist(),
                        )
                        point_ids_to_add.add(label_to["point_id"])

                    if (
                        label_from["point_id"] not in self._tested_embedding_point_ids
                        and label_from["point_id"] not in point_ids_to_add
                    ):
                        embeddings_to_add[label_from["point_id"]] = dl_metrics.Embedding(
                            point_id=label_from["point_id"],
                            type=embedding_type,
                            embedding=emb_from.detach().cpu().tolist(),
                        )
                        point_ids_to_add.add(label_from["point_id"])

                with dl_metrics.get_session(dl_metrics.get_db(self._points_db)) as sess:
                    point_ids = (
                        sess.query(dl_metrics.Embedding.point_id)
                        .filter(
                            and_(
                                dl_metrics.Embedding.point_id.in_(list(point_ids_to_add)),
                                dl_metrics.Embedding.type == embedding_type,
                            )
                        )
                        .all()
                    )

                for p_id in point_ids:
                    del embeddings_to_add[label_from["point_id"]]
                    del embeddings_to_add[label_to["point_id"]]
                    point_ids_to_add.remove(p_id)

                for i in range(3):
                    try:
                        with dl_metrics.get_session(dl_metrics.get_db(self._points_db)) as sess:
                            sess.bulk_save_objects(embeddings_to_add.values())
                            sess.commit()
                            for item in point_ids_to_add:
                                self._tested_embedding_point_ids.add(item)
                            break
                    except Exception as e:
                        LOG.warn(f"Error committing embedding: {e}")
                    sleep_ms(1000)
        return loss

    def on_train_epoch_end(self) -> None:
        all_metrics: List[Metrics] = gather_objects(self._train_outputs)
        self._train_outputs.clear()
        self._train_dataset.reload_random_samples()
        if torch.distributed.get_rank() != 0:
            return
        average_metrics = AverageMetrics(all_metrics)
        metrics_dict = {k: v.item() for k, v in average_metrics.to_dict(prefix="pumap_train_").items()}
        if self.logger is not None and isinstance(self.logger, WandbLogger):
            self.logger.experiment.summary.update(metrics_dict)
        self.log_dict(metrics_dict)

    def on_validation_epoch_end(self) -> None:
        all_metrics: List[Metrics] = gather_objects(self._validation_outputs)
        self._validation_outputs.clear()
        if torch.distributed.get_rank() == 0:
            average_metrics = AverageMetrics(all_metrics)
            metrics_dict = {k: v.item() for k, v in average_metrics.to_dict(prefix="pumap_val_").items()}
            if self.logger is not None and isinstance(self.logger, WandbLogger):
                self.logger.experiment.summary.update(metrics_dict)
            self.log_dict(metrics_dict)
            val_oec_list = [metrics_dict["pumap_val_oec"]]
            torch.distributed.broadcast_object_list(val_oec_list)
        else:
            val_oec_list = [0.0]
            torch.distributed.broadcast_object_list(val_oec_list)
            self.log("pumap_val_oec", val_oec_list[0])

    def on_test_epoch_end(self) -> None:
        all_metrics: List[Metrics] = gather_objects(self._test_outputs)
        self._test_outputs.clear()
        data_list: List[Optional[torch.Tensor]] = [None]
        if torch.distributed.get_rank() == 0:
            average_metrics = AverageMetrics(all_metrics)
            metrics_dict = {k: v.item() for k, v in average_metrics.to_dict(prefix="pumap_test_").items()}
            self.log_dict(metrics_dict)
            data, labels = self._test_dataset.get_data()
            prefix = "pumap_test"
            if self._model._scaler is not None:
                prefix = "pumap_test_scaled"
            try:
                self.log_embeddings_and_reductions(data, labels, prefix)
                if self.logger is not None and isinstance(self.logger, WandbLogger):
                    self.logger.experiment.summary.update(metrics_dict)
            except Exception as e:
                LOG.warn(f"Wasn't able to log embeddings: {e}")

            data_list = [data]

        torch.distributed.broadcast_object_list(data_list, src=0)
        data = cast(torch.Tensor, data_list[0])

        if self._model._scaler is None:
            self.scale_and_shift_model_output(data)

    def scale_and_shift_model_output(self, data: torch.Tensor) -> None:
        min_real_list: List[Optional[float]] = [None]
        max_real_list: List[Optional[float]] = [None]
        shift_real_list: List[Optional[float]] = [None]
        shift_ind_list: List[Optional[int]] = [None]
        if torch.distributed.get_rank() == 0:
            reduced_a = []
            reduced_b = []
            with torch.no_grad():
                for i in range(0, data.shape[0], 1000):
                    input_data = data[i : i + 1000, :].unsqueeze(-1).unsqueeze(-1).cuda()
                    reduced_embedding = self._model(input_data).squeeze().squeeze()
                    reduced_a.extend([reduced_emb[0] for reduced_emb in reduced_embedding.detach().cpu()])
                    reduced_b.extend([reduced_emb[1] for reduced_emb in reduced_embedding.detach().cpu()])

            percentiles_a = np.percentile(reduced_a, [10, 50, 90])
            percentiles_b = np.percentile(reduced_b, [10, 50, 90])

            if percentiles_a[2] - percentiles_a[0] > percentiles_b[2] - percentiles_b[0]:
                min_real = percentiles_a[0]
                max_real = percentiles_a[2]
                shift_ind = 1
                shift_real = percentiles_b[1]
            else:
                min_real = percentiles_b[0]
                max_real = percentiles_b[2]
                shift_ind = 0
                shift_real = percentiles_a[1]

            min_real_list = [min_real]
            max_real_list = [max_real]
            shift_real_list = [shift_real]
            shift_ind_list = [shift_ind]

        torch.distributed.broadcast_object_list(min_real_list, src=0)
        torch.distributed.broadcast_object_list(max_real_list, src=0)
        torch.distributed.broadcast_object_list(shift_real_list, src=0)
        torch.distributed.broadcast_object_list(shift_ind_list, src=0)

        min_real = min_real_list[0]
        max_real = max_real_list[0]
        shift_real = shift_real_list[0]
        shift_ind = cast(int, shift_ind_list[0])

        self._model.set_scaler(min_real, max_real, 0.1, 0.9)
        self._model.set_shifter(shift_real, shift_ind)

    def log_embeddings_and_reductions(self, data: torch.Tensor, labels: List[Any], prefix: str = "") -> None:
        full_datapoints: List[ComparisonEmbeddingObject] = []
        reduced_datapoints: List[ComparisonEmbeddingObject] = []
        data_list = data.tolist()
        self._model.eval()
        with torch.no_grad():
            for i, embedding in enumerate(data_list):
                reduced_embedding = (
                    self._model(data[i, :].unsqueeze(0).unsqueeze(-1).unsqueeze(-1).cuda())
                    .squeeze()
                    .squeeze()
                    .squeeze()
                )

                full_datapoints.append(
                    ComparisonEmbeddingObject(
                        image_id="",
                        x=0,
                        y=0,
                        radius=0,
                        category=labels[i]["category"],
                        embedding=embedding,
                        epoch="test",
                        geohash="",
                    )
                )

                reduced_datapoints.append(
                    ComparisonEmbeddingObject(
                        image_id="",
                        x=0,
                        y=0,
                        radius=0,
                        category=labels[i]["category"],
                        embedding=reduced_embedding.cpu().tolist(),
                        epoch="test",
                        geohash="",
                    )
                )

                if i >= 1000:
                    break

        embeddings_df, _, _, _ = embeddings_to_dataframes(full_datapoints, pca_components=50)
        reduced_embeddings_df, _, _, _ = embeddings_to_dataframes(reduced_datapoints, pca_components=None)

        if self.logger is not None:
            self.logger.experiment.log(
                {
                    f"{prefix}.input_embeddings": wandb.Table(dataframe=embeddings_df),
                    f"{prefix}.output_embeddings": wandb.Table(dataframe=reduced_embeddings_df),
                }
            )

    def export_metadata(self) -> ModelMetadata:
        return ModelMetadata(
            input_dtype=torch.float32,
            input_size=(1, 1),
            experiment_url=self._experiment_url,
            use_cases=self.use_cases,
            # TODO(asergeev): figure out low precision support
            supports_half=False,
            model_class=self._model.__class__.__name__,
            scaler_shifter_parameters=self._model.scaler_shifter_parameters,
        )

    def on_after_backward(self) -> None:
        # Log gradient histograms to W&B
        if self.logger is not None and self.trainer.global_step % 100 == 0:
            for name, p in self._model.named_parameters():
                if p.grad is None:
                    continue
                hist_counts = torch.histc(p.grad, min=-1.0, max=1.0, bins=40).cpu().numpy()
                hist_bins = np.linspace(-1.0, 1.0, 41)
                np_histogram = (hist_counts, hist_bins)
                self.logger.experiment.log(
                    {f"{name}.pumap.grad": wandb.Histogram(np_histogram=np_histogram)}, step=self.logger.experiment.step
                )

    def are_embeddings_reduced_scaled(self) -> bool:
        return self._model._scaler is not None

    def set_exp_dir(self, exp_dir: str) -> None:
        pass

    def set_experiment_url(self, experiment_url: str) -> None:
        self._experiment_url = experiment_url

    def export_model(self) -> torch.nn.Module:
        return self._model

    def export_torch_script(self) -> torch.jit.ScriptModule:
        return cast(torch.jit.ScriptModule, torch.jit.script(self._model))

    def set_points_db(self, points_db: str) -> None:
        self._points_db = points_db


class PUMAPTrainer(Trainer):
    def __init__(self,) -> None:
        super().__init__()
        self._train_dataset: Optional[PUMAPDataset] = None
        self._val_dataset: Optional[PUMAPDataset] = None
        self._test_dataset: Optional[PUMAPDataset] = None

    def __enter__(self) -> "PUMAPTrainer":
        return self

    def __exit__(self, exc_type: Any, exc_value: Any, traceback: Any) -> None:
        return

    def dataset(
        self,
        points_db_path: str,
        embedding_hdf5_file: str,
        n_neighbors: int = 15,
        only_test: bool = False,
        fast_run: bool = False,
    ) -> None:
        graph_list: List[Optional[Graph]] = [None]
        labels: List[Optional[Dict[str, Any]]] = [None]
        data: torch.Tensor = torch.ones((1, 1))
        if torch.distributed.get_rank() == 0:
            embs = []
            with dl_metrics.get_session(dl_metrics.get_db(points_db_path)) as sess:
                embeddings_from_points_db = (
                    sess.query(
                        dl_metrics.Point.category_class_name.label("category"),
                        dl_metrics.Image.filepath.label("filepath"),
                        dl_metrics.Point.x.label("point_x"),
                        dl_metrics.Point.y.label("point_y"),
                        dl_metrics.Point.id.label("point_id"),
                        dl_metrics.Point.uuid,
                    )
                    .join(dl_metrics.Image, dl_metrics.Point.image_id == dl_metrics.Image.id)
                    .order_by(func.random())
                    .limit(int(5e5))
                ).all()

                embedding_lookup_table = EmbeddingLookupTables.load(embedding_hdf5_file)

                for item in embeddings_from_points_db:
                    embedding = embedding_lookup_table.get(item.uuid)

                    if embedding is None:
                        continue
                    embs.append(
                        {
                            "embedding": embedding,
                            "category": item.category,
                            "filepath": item.filepath,
                            "point_x": item.point_x,
                            "point_y": item.point_y,
                            "point_id": item.point_id,
                        }
                    )

            for e in embs:
                assert "embedding" in e
                assert e["embedding"] is not None
            data = torch.stack([d["embedding"] for d in embs])
            labels = []
            for item in embs:
                del item["embedding"]
                labels.append(item)
            del embs
            umap_graph = get_umap_graph(data, n_neighbors=n_neighbors)
            graph = get_graph_elements(umap_graph, None)
            graph_list = [graph]

        labels = broadcast_object(labels)
        data = broadcast_object(data)
        graph_list = broadcast_object(graph_list)
        graph = cast(Graph, graph_list[0])

        assert graph is not None

        shuffle_mask = np.random.permutation(np.arange(len(graph)))
        head = graph.head[shuffle_mask]
        tail = graph.tail[shuffle_mask]
        epochs_per_sample = graph.epochs_per_sample[shuffle_mask]

        if only_test:
            self._train_dataset = PUMAPDataset(
                torch.ones((1, 1)), head=np.ones((1, 1)), tail=np.ones((1, 1)), labels=[], epochs_per_sample=None
            )
            self._validation_dataset = PUMAPDataset(
                torch.ones((1, 1)), head=np.ones((1, 1)), tail=np.ones((1, 1)), labels=[], epochs_per_sample=None
            )
            self._test_dataset = PUMAPDataset(
                data, head=head, tail=tail, labels=cast(List[Dict[str, Any]], labels), epochs_per_sample=None
            )
        else:
            train_percent = 0.7
            validation_percent = 0.8
            if fast_run:
                # in fast run we can have so few elements that in order to make sure it works, we should allow for more items in validation
                train_percent = 0.5
                validation_percent = 0.7
            train_boundary = int(head.shape[0] * train_percent)
            validation_boundary = int(head.shape[0] * validation_percent)
            self._train_dataset = PUMAPDataset(
                data,
                head=head[:train_boundary],
                tail=tail[:train_boundary],
                epochs_per_sample=epochs_per_sample[:train_boundary],
                labels=cast(List[Dict[str, Any]], labels),
                examples_per_epoch=int(1e6),
            )
            self._validation_dataset = PUMAPDataset(
                data,
                head=head[train_boundary:validation_boundary],
                tail=tail[train_boundary:validation_boundary],
                epochs_per_sample=None,
                labels=cast(List[Dict[str, Any]], labels),
            )
            self._test_dataset = PUMAPDataset(
                data,
                head=head[validation_boundary:],
                tail=tail[validation_boundary:],
                epochs_per_sample=None,
                labels=cast(List[Dict[str, Any]], labels),
            )

    def train(
        self,
        input_features: int = 1024,
        min_dist: float = 0.1,
        metric: str = "euclidean",
        lr: float = 1e-3,
        epochs: int = 10,
        batch_size: int = 1000,
        fast_run: bool = False,
        checkpoint_dir: str = "",
        lr_milestones: Optional[List[int]] = None,
        lr_gamma: float = 1e-3,
        description: str = "",
        log_experiment: bool = True,
        logger: Optional[Any] = None,
    ) -> None:
        lr_milestones = lr_milestones if lr_milestones is not None else [10, 20, 30, 40]
        self._model = ParametricUMAP(input_features)
        self.min_dist = min_dist
        self.metric = metric
        self.lr = lr
        self.epochs = epochs

        assert self._train_dataset is not None
        assert self._test_dataset is not None
        assert self._validation_dataset is not None

        module = PUMAPTrainingModule(
            lr=lr,
            model=self._model,
            min_dist=min_dist,
            batch_size=batch_size,
            train_dataset=self._train_dataset,
            validation_dataset=self._validation_dataset,
            test_dataset=self._test_dataset,
            lr_milestones=lr_milestones,
            lr_gamma=lr_gamma,
        )

        self.experiment_directory = super()._train(
            module,
            get_version(),
            description=description,
            fast_run=fast_run,
            epochs=self.epochs,
            sub_type="parametric_umap",
            checkpoint_dir=checkpoint_dir,
            checkpoint_start_pct=0,
            log_experiment=log_experiment,
            logger=logger,
            monitor_metric="pumap_val_oec",
        )

    def infer(
        self,
        checkpoint_file: str,
        points_db: str,
        batch_size: int = 1000,
        logger: Optional[Any] = None,
        fast_run: bool = False,
    ) -> None:
        self._model, metadata = load_pytorch_model(ParametricUMAP(), checkpoint_file)  # type: ignore
        if metadata.scaler_shifter_parameters is not None:
            self._model.set_scaler_shifter(metadata.scaler_shifter_parameters)
        assert self._train_dataset is not None
        assert self._test_dataset is not None
        assert self._validation_dataset is not None
        module = PUMAPTrainingModule(
            model=self._model,
            train_dataset=self._train_dataset,
            validation_dataset=self._validation_dataset,
            test_dataset=self._test_dataset,
            batch_size=batch_size,
            lr=1e-1,
        )
        module.set_points_db(points_db)
        super()._infer(module, logger=logger, fast_run=fast_run)

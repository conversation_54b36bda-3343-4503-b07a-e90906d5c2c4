from typing import Any, Dict, List

import torch

from deeplearning.utils.tensor import recursive_detach, recursive_move


class Metrics:
    def __init__(self, loss: torch.Tensor):
        self._loss = loss

    @property
    def loss(self) -> torch.Tensor:
        return self._loss

    def to_dict(self, prefix: str = "") -> Dict[str, Any]:
        d: Dict[str, Any] = dict()
        d[f"{prefix}loss"] = self.loss
        return d

    def detach(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_detach(self.__dict__)
        return obj

    def cpu(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_move(self.__dict__, torch.device("cpu"))
        return obj


class AverageMetrics:
    def __init__(self, metrics: List[Metrics]):
        self.metrics = metrics

    def compute_avg_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.loss for m in self.metrics]).mean()

    def compute_oec(self) -> torch.Tensor:
        oec: torch.Tensor = 1.0 / (self.compute_avg_loss() + 1e-10)
        return oec

    def to_dict(self, prefix: str = "") -> Dict[str, Any]:
        d: Dict[str, Any] = dict()
        d[f"{prefix}oec"] = self.compute_oec()
        d[f"avg_{prefix}loss"] = self.compute_avg_loss()

        return d

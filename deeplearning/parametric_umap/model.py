from typing import Dict, Optional, cast

import torch
import torch.nn as nn


class Scaler:
    def __init__(self, min_real: float, max_real: float, min_scale: float = 0.1, max_scale: float = 0.9) -> None:
        self._min_real = min_real
        self._max_real = max_real
        self._min_scale = min_scale
        self._max_scale = max_scale

    def __call__(self, input: torch.Tensor) -> torch.Tensor:
        return ((input - self._min_real) / (self._max_real - self._min_real)) * (
            self._max_scale - self._min_scale
        ) + self._min_scale


class Shifter:
    def __init__(self, shift_real: float) -> None:
        self._shift_real = shift_real

    def __call__(self, input: torch.Tensor) -> torch.Tensor:
        return input - self._shift_real


class ParametricUMAP(torch.nn.Module):
    def __init__(  # type: ignore
        self, input_dims: int = 1024, output_dims: int = 2, batch_norm=nn.BatchNorm2d, relu_inplace: bool = True
    ) -> None:
        super().__init__()
        self._relu_inplace = relu_inplace

        self._network = nn.Sequential(  # Might be best network but overfits fast
            nn.Conv2d(input_dims, 512, kernel_size=1, padding=0, stride=1, bias=False),
            batch_norm(512),
            nn.LeakyReLU(inplace=self._relu_inplace),
            nn.Conv2d(512, 256, kernel_size=1, padding=0, stride=1, bias=False),
            batch_norm(256),
            nn.LeakyReLU(inplace=self._relu_inplace),
            nn.Conv2d(256, 128, kernel_size=1, padding=0, stride=1, bias=False),
            batch_norm(128),
            nn.LeakyReLU(inplace=self._relu_inplace),
            nn.Conv2d(128, 64, kernel_size=1, padding=0, stride=1, bias=False),
            batch_norm(64),
            nn.Tanh(),
            nn.Conv2d(64, output_dims, kernel_size=1, padding=0, stride=1, bias=True),
        )

        self._scaler: Optional[Scaler] = None
        self._shifter: Optional[Shifter] = None
        self._shift_ind: Optional[int] = None

    def forward(self, embeddings: torch.Tensor) -> torch.Tensor:
        out = cast(torch.Tensor, self._network(embeddings))

        if self._shifter and self._shift_ind is not None:
            out[:, self._shift_ind, :, :] = self._shifter(out[:, self._shift_ind, :, :])
        if self._scaler:
            out = self._scaler(out)

        return out

    def set_scaler(self, min_real: float, max_real: float, min_scale: float = 0.1, max_scale: float = 0.9) -> None:
        self._scaler = Scaler(min_real, max_real, min_scale, max_scale)

    def set_shifter(self, shift_real: float, shift_ind: int) -> None:
        self._shifter = Shifter(shift_real)
        self._shift_ind = shift_ind

    @property
    def scaler_min_real(self) -> Optional[float]:
        if self._scaler is None:
            return None
        return self._scaler._min_real

    @property
    def scaler_max_real(self) -> Optional[float]:
        if self._scaler is None:
            return None
        return self._scaler._max_real

    @property
    def scaler_min_scale(self) -> Optional[float]:
        if self._scaler is None:
            return None
        return self._scaler._min_scale

    @property
    def scaler_max_scale(self) -> Optional[float]:
        if self._scaler is None:
            return None
        return self._scaler._max_scale

    @property
    def shifter_real(self) -> Optional[float]:
        if self._shifter is None:
            return None
        return self._shifter._shift_real

    @property
    def shifter_ind(self) -> Optional[int]:
        return self._shift_ind

    @property
    def scaler_shifter_parameters(self) -> Optional[Dict[str, float]]:
        if self._scaler is None:
            return None

        assert self._scaler is not None and self._shifter is not None and self._shift_ind is not None
        return {
            "min_real": cast(float, self.scaler_min_real),
            "max_real": cast(float, self.scaler_max_real),
            "min_scale": cast(float, self.scaler_min_scale),
            "max_scale": cast(float, self.scaler_max_scale),
            "shift_real": cast(float, self.shifter_real),
            "shift_ind": cast(int, self.shifter_ind),
        }

    def set_scaler_shifter(self, scaler_shifter_parameters: Dict[str, float]) -> None:
        if len(scaler_shifter_parameters) == 0:
            return
        self.set_scaler(
            scaler_shifter_parameters["min_real"],
            scaler_shifter_parameters["max_real"],
            scaler_shifter_parameters["min_scale"],
            scaler_shifter_parameters["max_scale"],
        )
        self.set_shifter(scaler_shifter_parameters["shift_real"], cast(int, scaler_shifter_parameters["shift_ind"]))

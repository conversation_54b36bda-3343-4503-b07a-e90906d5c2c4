import logging

import torch


def convert_distance_to_probability(distances: torch.Tensor, a: float = 1.0, b: float = 1.0) -> torch.Tensor:
    return -torch.log1p(a * distances ** (2 * b))


def compute_cross_entropy(
    probabilities_graph: torch.Tensor, probabilities_distance: torch.Tensor, repulsion_strength: float = 1.0
) -> torch.Tensor:
    attraction_term: torch.Tensor = -probabilities_graph * torch.nn.functional.logsigmoid(probabilities_distance)
    repellant_term: torch.Tensor = (
        -(1.0 - probabilities_graph)
        * (torch.nn.functional.logsigmoid(probabilities_distance) - probabilities_distance)
        * repulsion_strength
    )

    return attraction_term + repellant_term


def umap_loss(
    embedding_to: torch.Tensor,
    embedding_from: torch.Tensor,
    a: float,
    b: float,
    batch_size: int,
    negative_sample_rate: int = 5,
) -> torch.Tensor:
    _embedding_to = embedding_to.squeeze().squeeze()
    _embedding_from = embedding_from.squeeze().squeeze()
    embedding_neg_to = _embedding_to.repeat(negative_sample_rate, 1).to(device=embedding_to.device)
    repeat_neg = _embedding_from.repeat(negative_sample_rate, 1).to(device=embedding_to.device)
    embedding_neg_from = repeat_neg[torch.randperm(repeat_neg.shape[0])]
    try:
        distance_embedding = torch.cat(
            ((_embedding_to - _embedding_from).norm(dim=1), (embedding_neg_to - embedding_neg_from).norm(dim=1)), dim=0
        ).to(device=embedding_to.device)
    except Exception as e:
        logging.error(
            f"Failed to compute distance: {e}. _embedding_to {_embedding_to.shape} _embedding_from {_embedding_from.shape} embedding_neg_to {embedding_neg_to.shape} embedding_neg_from {embedding_neg_from.shape}"
        )
        raise e

    probabilities_distance = convert_distance_to_probability(distance_embedding, a, b)

    probabilities_graph = torch.cat((torch.ones(batch_size), torch.zeros(batch_size * negative_sample_rate)), dim=0).to(
        device=embedding_to.device
    )

    cross_entropy_loss = compute_cross_entropy(probabilities_graph, probabilities_distance, repulsion_strength=1.0)
    loss = torch.mean(cross_entropy_loss)
    return loss

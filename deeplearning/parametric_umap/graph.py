from typing import Any, Dict, List, Optional, Tuple, cast

import numpy as np
import numpy.typing as npt
import torch
from pynndescent import N<PERSON><PERSON><PERSON>
from sklearn.utils import check_random_state
from torch.utils.data import Dataset
from umap.umap_ import fuzzy_simplicial_set


def get_umap_graph(data: torch.Tensor, n_neighbors: int = 15, metric: str = "euclidean") -> Any:
    n_trees = 5 + int(round((data.shape[0]) ** 0.5 / 20.0))
    n_iters = max(5, int(round(np.log2(data.shape[0]))))

    nnd = NNDescent(
        data, n_neighbors=n_neighbors, metric=metric, n_trees=n_trees, n_iters=n_iters, max_candidates=60, verbose=True
    )

    knn_indices, knn_dists = nnd.neighbor_graph
    umap_graph, _, _ = fuzzy_simplicial_set(
        X=data,
        n_neighbors=n_neighbors,
        metric=metric,
        random_state=check_random_state(None),
        knn_indices=knn_indices,
        knn_dists=knn_dists,
    )

    return umap_graph


def get_graph_elements(graph_: Any, n_epochs: Optional[int] = None) -> "Graph":
    graph = graph_.tocoo()
    graph.sum_duplicates()

    if n_epochs is None:
        if graph.shape[0] <= 10000:
            n_epochs = 500
        else:
            n_epochs = 200

    graph.data[graph.data < (graph.data.max() / float(n_epochs))] = 0.0
    graph.eliminate_zeros()

    graph_obj = Graph(graph, n_epochs)

    return graph_obj


class Graph:
    def __init__(self, graph: Any, n_epochs: int):
        self._graph = graph
        self._n_epochs = n_epochs

    def set_n_epochs(self, n_epochs: int) -> None:
        self._n_epochs = n_epochs

    @property
    def head(self) -> Any:
        return self._graph.row

    @property
    def tail(self) -> Any:
        return self._graph.col

    @property
    def epochs_per_sample(self) -> Any:
        return self._n_epochs * self._graph.data

    def __len__(self) -> int:
        return cast(int, self._graph.shape[0])


class PUMAPDataset(Dataset[Tuple[torch.Tensor, torch.Tensor, Dict[str, Any], Dict[str, Any]]]):
    def __init__(
        self,
        data: torch.Tensor,
        head: npt.NDArray[Any],
        tail: npt.NDArray[Any],
        labels: List[Dict[str, Any]],
        epochs_per_sample: Optional[npt.NDArray[Any]] = None,
        examples_per_epoch: Optional[int] = None,
    ):
        self.data = data
        self._examples_per_epoch = examples_per_epoch

        self.edges_to_exp: npt.NDArray[Any] = head
        self.edges_from_exp: npt.NDArray[Any] = tail
        if epochs_per_sample is not None:
            self.indices = list(range(head.shape[0]))
            self.weights = epochs_per_sample / epochs_per_sample.sum()

            self.reload_random_samples()
        self.labels = labels

    def __len__(self) -> int:
        if self._examples_per_epoch:
            return self._examples_per_epoch
        return int(self.edges_to_exp.shape[0])

    def reload_random_samples(self) -> None:
        self.randomly_ordered_indices = np.random.choice(self.indices, size=self._examples_per_epoch, p=self.weights)

    def __getitem__(self, index: int) -> Tuple[torch.Tensor, torch.Tensor, Dict[str, Any], Dict[str, Any]]:
        if self._examples_per_epoch:
            index = self.randomly_ordered_indices[index]
        data_to_index = self.edges_to_exp[index]
        data_from_index = self.edges_from_exp[index]
        edges_to_exp = self.data[data_to_index]
        edges_from_exp = self.data[data_from_index]

        label_from = self.labels[data_to_index]
        label_to = self.labels[data_from_index]
        return (
            edges_to_exp.unsqueeze(-1).unsqueeze(-1),
            edges_from_exp.unsqueeze(-1).unsqueeze(-1),
            label_to,
            label_from,
        )

    def get_data(self) -> Tuple[torch.Tensor, List[Any]]:
        return self.data, self.labels

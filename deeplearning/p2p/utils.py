import torch
import torch.nn.functional as F

EPS = 1e-5


def downsample_like(a: torch.Tensor, b: torch.Tensor, eps: float = EPS) -> torch.Tensor:
    assert len(a.shape) == 4, f"Expected 4D tensor, got: {a.shape}"
    assert len(b.shape) == 4, f"Expected 4D tensor, got: {b.shape}"
    a_interpolated = F.interpolate(a, b.shape[-2:], mode="area")
    a_interpolated_thresholded = torch.stack([(a_i >= max(eps, a_i.max())).float() for a_i in a_interpolated])
    return a_interpolated_thresholded


def upsample_like(a: torch.Tensor, b: torch.Tensor, min_threshold: float = EPS, eps: float = EPS) -> torch.Tensor:
    assert len(a.shape) == 4, f"Expected 4D tensor, got: {a.shape}"
    assert len(b.shape) == 4, f"Expected 4D tensor, got: {b.shape}"
    a_interpolated_list = [
        F.interpolate(
            (a[idx : idx + 1] >= max(min_threshold, a[idx].max().item())).float(),
            b.shape[-2:],
            mode="bilinear",
            align_corners=True,
        )
        for idx in range(a.shape[0])
    ]
    a_interpolated_thresholded = torch.cat([(a_i >= max(eps, a_i.max())).float() for a_i in a_interpolated_list])
    return a_interpolated_thresholded

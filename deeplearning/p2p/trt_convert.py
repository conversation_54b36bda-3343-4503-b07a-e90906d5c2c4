import logging
import math
from typing import List, Op<PERSON>, Tu<PERSON>, Union, cast

import fire
import numpy as np
import tensorrt as trt
import torch
from torch2trt import torch2trt

from deeplearning.model_io import load_pytorch_model, peek_pytorch_metadata, save_tensorrt_model
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.p2p.datasets import P2PDataset, P2PTrainValDatasets
from deeplearning.p2p.models.p2p_model_v1 import P2PModelV1
from deeplearning.p2p.models.utils import P2PModel, P2PModelOutputFactory
from deeplearning.p2p.models_dict import MODELS_DICT
from deeplearning.utils.fire_utils import safe_split


class TrtConvert(object):
    def __init__(
        self,
        dataset: Optional[P2PTrainValDatasets] = None,
        metadata: Optional[ModelMetadata] = None,
        model: Optional[P2PModel] = None,
    ) -> None:
        self._dataset = dataset
        self._ckpt_metadata = metadata
        self._model = model

    def load_dataset(self, data_root: Union[str, Tuple[str, ...]],) -> "TrtConvert":
        dataset = P2PDataset(safe_split(data_root))
        self._dataset = P2PTrainValDatasets(dataset, dataset, dataset, dataset)
        return self

    def load_model(self, checkpoint: str) -> "TrtConvert":
        # Grab metadata
        self._ckpt_metadata = peek_pytorch_metadata(checkpoint)

        # Load the model.
        template_model = (
            MODELS_DICT.get(self._ckpt_metadata.model_class, P2PModelV1)()
            if self._ckpt_metadata.model_class
            else P2PModelV1()
        )
        self._model, _ = cast(Tuple[P2PModel, ModelMetadata], load_pytorch_model(template_model, checkpoint))
        return self

    def convert(
        self, max_batch_size: int, save_to: str, fp16: bool = False, int8: bool = False, calibration_epochs: int = 1,
    ) -> "TrtConvert":
        assert self._model is not None
        assert self._ckpt_metadata is not None
        assert self._dataset is not None
        self._model.eval().cuda()
        self._model.is_fp16 = True
        coords_pyt = []
        scores_pyt: List[Optional[float]] = []

        for i in range(0, len(self._dataset.get_validation())):
            datapoint = self._dataset.get_validation()[i]
            input_perspective = datapoint[0].unsqueeze(0).cuda()
            input_image = datapoint[1].unsqueeze(0).cuda()
            out_pyt = P2PModelOutputFactory.unpack(self._model(input_perspective, input_image))

            coord_pyt = out_pyt.coord(0)
            coords_pyt.append(coord_pyt)

            if coord_pyt is not None:
                scores_pyt.append(out_pyt.score(0))
            else:
                scores_pyt.append(None)

        # Prepare test tensor.
        datapoint = self._dataset.get_calibration()[0]
        input_data_perspective = torch.unsqueeze(datapoint[0], 0).cuda().repeat(max_batch_size, 1, 1, 1)
        input_data_image = torch.unsqueeze(datapoint[1], 0).cuda().repeat(max_batch_size, 1, 1, 1)

        # Run the conversion.
        with torch.no_grad():
            trt_model = torch2trt(
                self._model,
                [input_data_perspective, input_data_image],
                fp16_mode=fp16,
                int8_mode=int8,
                int8_calib_dataset=self._dataset.get_calibration(),
                max_batch_size=max_batch_size,
                max_workspace_size=1 << 27,
                log_level=trt.Logger.INFO,
                strict_type_constraints=False,
                use_implicit_batch_dimension=False,
            )

        class_name = str(self._model.__class__.__name__)
        del self._model

        coord_diffs = []
        score_diffs = []
        pyt_relevant_samples = 0
        trt_relevant_samples = 0
        trt_extraneous_samples = 0
        for i in range(0, len(self._dataset.get_validation()), max_batch_size):
            input_perspectives = []
            input_images = []
            batch_size = min(len(self._dataset.get_validation()) - i, max_batch_size)
            for j in range(batch_size):
                datapoint = self._dataset.get_validation()[i + j]
                input_perspectives.append(datapoint[0])
                input_images.append(datapoint[1])
            input_perspective = (torch.stack(input_perspectives)).cuda()
            input_image = (torch.stack(input_images)).cuda()
            out_trt = P2PModelOutputFactory.unpack(trt_model(input_perspective, input_image))

            for batch_index in range(batch_size):
                coord_pyt = coords_pyt[i + batch_index]
                coord_trt = out_trt.coord(batch_index)
                if coord_pyt is None and coord_trt is None:
                    continue
                elif coord_pyt is None and coord_trt is not None:
                    trt_extraneous_samples += 1
                    continue
                elif coord_pyt is not None and coord_trt is None:
                    pyt_relevant_samples += 1
                    continue

                assert coord_trt is not None and coord_pyt is not None

                pyt_relevant_samples += 1
                trt_relevant_samples += 1

                coord_diffs.append(math.sqrt(pow(coord_trt[0] - coord_pyt[0], 2) + pow(coord_trt[1] - coord_pyt[1], 2)))

                score_pyt = scores_pyt[i + batch_index]
                assert score_pyt is not None
                score_trt = out_trt.score(batch_index)
                score_diffs.append(abs(score_pyt - score_trt))

        if trt_relevant_samples + trt_extraneous_samples == 0:
            raise Exception("No matches found when running metrics. Dataset is bad or conversion failed.")
        else:
            print(
                "PyTorch vs. TensorRT precision: %.1f%%, recall: %.1f%%"
                % (
                    trt_relevant_samples / (trt_relevant_samples + trt_extraneous_samples) * 100,
                    trt_relevant_samples / pyt_relevant_samples * 100,
                )
            )

            if len(coord_diffs) != 0:
                print(
                    "Coordinates euclidean distance mean: %.3fpx, median: %.3f, min: %.3fpx, max: %.3fpx, 99%%tile: %.3fpx"
                    % (
                        np.mean(coord_diffs),
                        np.median(coord_diffs),
                        np.min(coord_diffs),
                        np.max(coord_diffs),
                        np.percentile(coord_diffs, 99),
                    )
                )

            if len(score_diffs) != 0:
                print(
                    "Score absolute distance mean: %.3f, median: %.3f, min: %.3f, max: %.3f, 99%%tile: %.3f"
                    % (
                        np.mean(score_diffs),
                        np.median(score_diffs),
                        np.min(score_diffs),
                        np.max(score_diffs),
                        np.percentile(score_diffs, 99),
                    )
                )

        # Save the resulting converted model.
        torch_dtype = torch.float32
        metadata = (
            self._ckpt_metadata.with_input_dtype(torch_dtype)
            .with_input_size((input_data_image.shape[3], input_data_image.shape[2]))
            .with_max_batch_size(max_batch_size)
            .with_aux_input_sizes([(input_data_perspective.shape[3], input_data_perspective.shape[2])])
            .with_model_class(class_name)
        )
        logging.info(f"hehehe {class_name}")
        save_tensorrt_model(trt_model, metadata, save_to)
        return self


if __name__ == "__main__":
    fire.Fire(TrtConvert)

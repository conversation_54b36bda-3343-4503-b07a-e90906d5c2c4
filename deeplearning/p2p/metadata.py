import datetime
import json
import logging
import os
import re
from typing import List, Optional, cast

import geohash

from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)

META_JSON = "meta.json"
MAX_PERSPECTIVE_ID = 9


# TODO(asergeev): Abstract away common parts of metadata among deeplabv3 and p2p
# NOTE: there a lot of subtle differences between DeepLabV3 and P2P metadata


class LabelMetadata:
    def __init__(self, filepath: Optional[str], certified: bool):
        self._filepath = filepath
        self._certified = certified

    @property
    def filepath(self) -> Optional[str]:
        return self._filepath

    @property
    def certified(self) -> bool:
        return self._certified

    @staticmethod
    def empty() -> "LabelMetadata":
        return LabelMetadata(filepath=None, certified=False)

    @staticmethod
    def from_mask_json(filepath: str) -> "LabelMetadata":
        assert os.path.exists(filepath), f"Mask .json file does not exist: {filepath}"

        dirpath, filename = os.path.split(filepath)
        mask_filename = os.path.splitext(filename)[0] + ".png"
        mask_filepath: Optional[str] = os.path.join(dirpath, mask_filename)
        if not os.path.exists(cast(str, mask_filepath)):
            # Mask does not exist - reset to None
            mask_filepath = None

        with open(filepath, "r") as f:
            meta_json = json.load(f)

        assert meta_json["version"] == 1, f"Unexpected version: {meta_json['version']}"
        return LabelMetadata(filepath=mask_filepath, certified=meta_json["certified"])


class ImageMetadata:
    def __init__(
        self, image_path: str, perspective_paths: List[str], label: LabelMetadata,
    ):
        self._image_path = image_path
        self._perspective_paths = perspective_paths
        self._label = label
        self._geohash: Optional[str] = None
        metadata = {}

        metadata_file_path = os.path.splitext(os.path.splitext(self._image_path)[0])[0] + ".metadata.json"
        if os.path.exists(metadata_file_path):
            with open(metadata_file_path) as f:
                metadata = json.load(f)
        if "ppi" not in metadata:
            LOG.warning(f"Could not find ppi in {metadata_file_path}")
        ppi = float(metadata.get("ppi", 182))
        perspective_ppi = float(metadata.get("perspective_ppi", 200))
        self._crop: str = metadata.get("crop", "unknown")
        self._crop_id: str = metadata.get("crop_id", self._crop)
        self._robot_id: str = metadata.get("robot_id", "unknown")
        self._captured_at = datetime.datetime.fromtimestamp(
            int(metadata.get("timestamp_ms", maka_control_timestamp_ms())) // 1000
        )
        self._row_id: str = metadata.get("row_id", 1)
        self._cam_id: str = metadata.get("cam_id", "unknown")
        if (
            "geo" in metadata
            and "lla" in metadata["geo"]
            and "lat" in metadata["geo"]["lla"]
            and "lng" in metadata["geo"]["lla"]
        ):
            self._geohash = geohash.encode(
                float(metadata["geo"]["lla"]["lat"]), float(metadata["geo"]["lla"]["lng"]), precision=9
            )

        assert ppi is not None, f"Could not determine target ppi: {image_path}"
        assert perspective_ppi is not None, f"Could not determine perspective ppi: {image_path}"

        self._ppi = ppi
        self._perspective_ppi = perspective_ppi

    @property
    def image_path(self) -> str:
        return self._image_path

    @property
    def ppi(self) -> float:
        return self._ppi

    @property
    def perspective_paths(self) -> List[str]:
        return self._perspective_paths

    @property
    def perspective_ppi(self) -> float:
        return self._perspective_ppi

    @property
    def label(self) -> LabelMetadata:
        return self._label

    @property
    def crop_id(self) -> str:
        return self._crop_id

    @property
    def robot_id(self) -> str:
        return self._robot_id

    @property
    def captured_at(self) -> datetime.datetime:
        return self._captured_at

    @property
    def row_id(self) -> str:
        return self._row_id

    @property
    def cam_id(self) -> str:
        return self._cam_id

    @property
    def geohash(self) -> Optional[str]:
        return self._geohash

    def has_certified_label(self) -> bool:
        return self.label.certified

    def has_positive_label(self) -> bool:
        return self.has_certified_label() and self.label.filepath is not None

    @staticmethod
    def from_file(image_path: str) -> "ImageMetadata":
        dirpath, filename = os.path.split(image_path)
        filename_no_ext = os.path.splitext(filename)[0]
        assert filename_no_ext.endswith(".image"), f"Image file does not end with .image: {filename}"

        # Labels are saved under .image_annotated suffix
        filename_no_suffix = os.path.splitext(filename_no_ext)[0]
        mask_json_filename = f"{filename_no_suffix}.image_annotated.mask_match.json"
        mask_json_filepath = os.path.join(dirpath, mask_json_filename)
        if os.path.exists(mask_json_filepath):
            label = LabelMetadata.from_mask_json(mask_json_filepath)
        else:
            label = LabelMetadata.empty()

        # Find perspectives
        perspective_paths = [os.path.join(dirpath, f"{filename_no_suffix}.perspective.png")]

        return ImageMetadata(image_path=image_path, perspective_paths=perspective_paths, label=label,)


def list_files(dirpath: str) -> List[ImageMetadata]:
    mask_regex = r"^.*\.image_annotated.mask_match.json$"
    files = [f for f in os.listdir(dirpath) if re.match(mask_regex, f)]
    result = []
    for filename in files:
        result.append(
            ImageMetadata.from_file(
                image_path=os.path.join(dirpath, filename).replace(".image_annotated.mask_match.json", ".image.png")
            )
        )
    return result


class PredictionReconstructionMetadata:
    def __init__(self, shift_x: float, shift_y: float, model_ppi: float) -> None:
        self._shift_x = shift_x
        self._shift_y = shift_y
        self._model_ppi = model_ppi

    @property
    def shift_x(self) -> float:
        return self._shift_x

    @property
    def shift_y(self) -> float:
        return self._shift_y

    @property
    def model_ppi(self) -> float:
        return self._model_ppi

import math
import time
import uuid
from typing import Any, Dict, List, Optional, Tu<PERSON>

import torch
from sqlalchemy.engine import Engine

from deeplearning.dl_metrics.p2p_metrics import p2p_db
from deeplearning.p2p.datasets import label_coord
from deeplearning.p2p.metadata import ImageMetadata, PredictionReconstructionMetadata
from deeplearning.p2p.models.utils import P2PModelOutput
from deeplearning.utils.tensor import recursive_detach, recursive_move

EPS = 1e-5
LASER_RADIUS_INCHES = 0.1  # 5 mm diameter at 100ppi


class MatchAccuracy:
    def __init__(self, ppi: int = 100) -> None:
        self._bingo = torch.tensor(0.0)
        self._hit_4px = torch.tensor(0.0)
        self._hit_8px = torch.tensor(0.0)
        self._miss = torch.tensor(0.0)
        self._none_p = torch.tensor(0.0)
        self._none_n = torch.tensor(0.0)
        self._n = torch.tensor(0.0)
        self._false_positive = torch.tensor(0.0)  # Shots that were not present in the image

        self._shot = torch.tensor(0.0)
        self._shot_miss = torch.tensor(0.0)
        self._average_distance = torch.tensor(0.0)

        self.ppi = ppi  # Pixels per inch, used for distance calculations

    @property
    def bingo(self) -> torch.Tensor:
        """Direct hits. Bingo!"""
        return self._bingo / (self._n + EPS)

    @property
    def hit_4px(self) -> torch.Tensor:
        """Hits within 4 pixels. <1mm at 100ppi."""
        return self._hit_4px / (self._n + EPS)

    @property
    def hit_8px(self) -> torch.Tensor:
        """Hits within 8 pixels. <2mm at 100ppi."""
        return self._hit_8px / (self._n + EPS)

    @property
    def miss(self) -> torch.Tensor:
        """Misses. Shots >2mm away from true point."""
        return self._miss / (self._n + EPS)

    @property
    def none_p(self) -> torch.Tensor:
        """Positive not-matches. Perspective is occluded / not present in image."""
        return self._none_p / (self._n + EPS)

    @property
    def none_n(self) -> torch.Tensor:
        """Negative non-matches. Perspective was in the image but was not found."""
        return self._none_n / (self._n + EPS)

    @property
    def shot(self) -> torch.Tensor:
        """Number of shots that were within the laser radius."""
        return self._shot / (self._n + EPS)

    @property
    def average_distance(self) -> torch.Tensor:
        """Average distance of shots in inches"""
        return self._average_distance / (self._n + EPS)

    @property
    def shot_miss(self) -> torch.Tensor:
        """Number of shots that were outside the laser radius."""
        return self._shot_miss / (self._n + EPS)

    @property
    def false_positive(self) -> torch.Tensor:
        """Number of shots that were not present in the image."""
        return self._false_positive / (self._n + EPS)

    def add_observation(self, coord: Optional[Tuple[float, float]], coord_hat: Optional[Tuple[float, float]]) -> None:
        if coord is not None:
            if coord_hat is not None:
                d = math.hypot(coord[0] - coord_hat[0], coord[1] - coord_hat[1])
                if d < EPS:
                    self._bingo += 1.0
                elif d < 4 + EPS:
                    self._hit_4px += 1.0
                elif d < 8 + EPS:
                    self._hit_8px += 1.0
                else:
                    self._miss += 1.0

                if d < LASER_RADIUS_INCHES * self.ppi + EPS:
                    self._shot += 1.0
                else:
                    self._shot_miss += 1.0
                self._average_distance += d / self.ppi  # Convert to inches

            else:
                self._none_n += 1.0  # False Negative
        else:
            if coord_hat is not None:
                self._miss += 1.0  # False Positive
                self._false_positive += 1.0
            else:
                self._none_p += 1.0  # True Negative
        self._n += 1.0

    def to_dict(self, prefix: str = "") -> Dict[str, torch.Tensor]:
        return {
            f"{prefix}bingo": self.bingo,
            f"{prefix}hit_4px": self.hit_4px,
            f"{prefix}hit_8px": self.hit_8px,
            f"{prefix}miss": self.miss,
            f"{prefix}none_p": self.none_p,
            f"{prefix}none_n": self.none_n,
            f"{prefix}shot": self.shot,
            f"{prefix}shot_miss": self.shot_miss,
            f"{prefix}average_distance_inches": self.average_distance,
            f"{prefix}false_positive": self.false_positive,
        }

    @staticmethod
    def merge(accuracies: "List[MatchAccuracy]") -> "MatchAccuracy":
        result = MatchAccuracy()
        result._bingo = torch.stack([a._bingo for a in accuracies]).sum()
        result._hit_4px = torch.stack([a._hit_4px for a in accuracies]).sum()
        result._hit_8px = torch.stack([a._hit_8px for a in accuracies]).sum()
        result._miss = torch.stack([a._miss for a in accuracies]).sum()
        result._none_p = torch.stack([a._none_p for a in accuracies]).sum()
        result._none_n = torch.stack([a._none_n for a in accuracies]).sum()
        result._shot = torch.stack([a._shot for a in accuracies]).sum()
        result._average_distance = torch.stack([a._average_distance for a in accuracies]).sum()
        result._shot_miss = torch.stack([a._shot_miss for a in accuracies]).sum()
        result._false_positive = torch.stack([a._false_positive for a in accuracies]).sum()
        result._n = torch.stack([a._n for a in accuracies]).sum()
        return result


class Metrics:
    def __init__(
        self,
        label: torch.Tensor,
        out_hat: P2PModelOutput,
        hit_loss: torch.Tensor,
        offset_loss: torch.Tensor,
        match_loss: torch.Tensor,
        hit_threshold: float,
        score_threshold: float,
        metadata: List[ImageMetadata],
        dataset_root: str,
        ppi: int,
        p2p_db_engine: Optional[Engine] = None,
        reconstruction_metadata: Optional[List[PredictionReconstructionMetadata]] = None,
    ):
        self.hit_loss = hit_loss
        self.offset_loss = offset_loss
        self.match_loss = match_loss
        self.loss = hit_loss + offset_loss + match_loss
        p2p_db_session = None
        if p2p_db_engine is not None:
            p2p_db_session = p2p_db.get_session(p2p_db_engine)

        with torch.no_grad():
            self.first_stage_accuracy = MatchAccuracy(ppi=ppi)
            self.second_stage_accuracy = MatchAccuracy(ppi=ppi)
            self.last_stage_accuracy = MatchAccuracy(ppi=ppi)

            batch_size = label.shape[0]
            for n in range(batch_size):
                coord = label_coord(label, n)
                self.first_stage_accuracy.add_observation(
                    coord, out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold, stage=1)
                )
                self.second_stage_accuracy.add_observation(
                    coord, out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold, stage=2)
                )
                self.last_stage_accuracy.add_observation(
                    coord, out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold)
                )

                coord_hat = out_hat.coord(n, hit_threshold=hit_threshold, score_threshold=score_threshold)

                if p2p_db_session is not None:
                    image_metadata = metadata[n]
                    original_x, original_y = None, None
                    original_x_hat, original_y_hat = None, None
                    if reconstruction_metadata is not None:
                        cropped_padded_to_original_shift_x = reconstruction_metadata[n].shift_x
                        cropped_padded_to_original_shift_y = reconstruction_metadata[n].shift_y
                        model_ppi = reconstruction_metadata[n].model_ppi
                        if coord is not None:
                            original_x = int(
                                (coord[0] + cropped_padded_to_original_shift_x) * image_metadata.ppi / model_ppi
                            )
                            original_y = int(
                                (coord[1] + cropped_padded_to_original_shift_y) * image_metadata.ppi / model_ppi
                            )
                        if coord_hat is not None:
                            original_x_hat = int(
                                (coord_hat[0] + cropped_padded_to_original_shift_x) * image_metadata.ppi / model_ppi
                            )
                            original_y_hat = int(
                                (coord_hat[1] + cropped_padded_to_original_shift_y) * image_metadata.ppi / model_ppi
                            )
                    truncated_filepath = image_metadata.image_path[len(dataset_root) :]
                    image_db = p2p_db.get_image(p2p_db_session, truncated_filepath)
                    if not image_db:
                        image_db = p2p_db.Image(
                            id=str(uuid.uuid4()),
                            filepath=image_metadata.image_path[len(dataset_root) :],
                            width=1800,
                            height=1696,
                            ppi=image_metadata.ppi,
                            crop_id=image_metadata.crop_id,
                            date=image_metadata.captured_at.date(),
                            timestamp_ms=time.mktime(image_metadata.captured_at.timetuple()),
                            robot_id=image_metadata.robot_id,
                            row_id=image_metadata.row_id,
                            cam_id=image_metadata.cam_id,
                            geo=image_metadata.geohash,
                        )
                        p2p_db_session.add(image_db)
                        p2p_db_session.flush()

                    distance = None
                    if coord_hat is not None and coord is not None:
                        distance = math.hypot(coord[0] - coord_hat[0], coord[1] - coord_hat[1])

                    match_db = p2p_db.Match(
                        id=str(uuid.uuid4()),
                        image_id=image_db.id,
                        distance=distance,
                        x=coord[0] if coord is not None else None,
                        y=coord[1] if coord is not None else None,
                        positive_match=coord is not None,
                        x_hat=coord_hat[0] if coord_hat is not None else None,
                        y_hat=coord_hat[1] if coord_hat is not None else None,
                        positive_match_hat=coord_hat is not None,
                        original_x=original_x,
                        original_y=original_y,
                        original_x_hat=original_x_hat,
                        original_y_hat=original_y_hat,
                    )
                    p2p_db_session.add(match_db)

            if p2p_db_session is not None:
                p2p_db_session.commit()

    def detach(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_detach(self.__dict__, whitelist=[MatchAccuracy])
        return obj

    def cpu(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_move(self.__dict__, torch.device("cpu"), whitelist=[MatchAccuracy])
        return obj

    def to_dict(self, prefix: str = "", **additional_metrics: Dict[str, Any]) -> Dict[str, Any]:
        d: Dict[str, Any] = {
            f"{prefix}hit_loss": self.hit_loss,
            f"{prefix}offset_loss": self.offset_loss,
            f"{prefix}match_loss": self.match_loss,
            f"{prefix}loss": self.loss,
        }

        d.update(self.first_stage_accuracy.to_dict("point_"))
        d.update(self.second_stage_accuracy.to_dict("point_adj_"))
        d.update(self.last_stage_accuracy.to_dict("point_adj2_"))

        for key, value in additional_metrics.items():
            d[key] = value

        return d


class AverageMetrics(object):
    def __init__(self, metrics: List[Metrics]):
        assert all([m.loss.device.type == "cpu" for m in metrics]), "Metrics have not been .cpu()'d"
        self.metrics = metrics

    def compute_avg_hit_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.hit_loss for m in self.metrics]).mean()

    def compute_avg_offset_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.offset_loss for m in self.metrics]).mean()

    def compute_avg_match_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.match_loss for m in self.metrics]).mean()

    def compute_avg_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.loss for m in self.metrics]).mean()

    def merge_first_stage_accuracy(self) -> MatchAccuracy:
        return MatchAccuracy.merge([m.first_stage_accuracy for m in self.metrics])

    def merge_second_stage_accuracy(self) -> MatchAccuracy:
        return MatchAccuracy.merge([m.second_stage_accuracy for m in self.metrics])

    def merge_last_stage_accuracy(self) -> MatchAccuracy:
        return MatchAccuracy.merge([m.last_stage_accuracy for m in self.metrics])

    def compute_oec(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        acc = self.merge_last_stage_accuracy()
        return acc.shot + acc.none_p

    def to_dict(self, prefix: str = "", **additional_metrics: Dict[str, Any]) -> Dict[str, Any]:
        d: Dict[str, Any] = {
            f"{prefix}hit_loss": self.compute_avg_hit_loss(),
            f"{prefix}offset_loss": self.compute_avg_offset_loss(),
            f"{prefix}match_loss": self.compute_avg_match_loss(),
            f"{prefix}loss": self.compute_avg_loss(),
            f"{prefix}oec": self.compute_oec(),
        }

        d.update(self.merge_first_stage_accuracy().to_dict(f"{prefix}point_"))
        d.update(self.merge_second_stage_accuracy().to_dict(f"{prefix}point_adj_"))
        d.update(self.merge_last_stage_accuracy().to_dict(f"{prefix}point_adj2_"))

        for key, value in additional_metrics.items():
            d[key] = value

        return d

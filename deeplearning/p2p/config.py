from dataclasses import dataclass

from dataclass_wizard import JSONWizard

from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import WANDB_PROJECT_MAP

DEFAULT_BACKBONE2_ARCHITECTURE = "resnet50"


@dataclass(eq=True, frozen=True)
class P2PConfig(JSONWizard):
    model: str = "P2PModelV1"
    wandb_project: str = WANDB_PROJECT_MAP[ModelUseCase.P2P]

    make_trt_model: bool = True
    ci_run: bool = False

    backbone2_architecture: str = DEFAULT_BACKBONE2_ARCHITECTURE

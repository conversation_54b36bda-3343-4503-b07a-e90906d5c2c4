import time

import fire
import torch

from deeplearning.p2p.models.p2p_model_v1 import P2PModelV1
from deeplearning.utils.images import crop_or_pad
from deeplearning.utils.model_benchmark import ModelBenchmark
from deeplearning.utils.resize_utils import interpolate
from lib.common.image.resize import adjust_size_ppi


class P2PBenchmark(ModelBenchmark):
    _TRT_MODEL_FILE = "p2p_resnet50.trt"

    def __init__(
        self,
        fp16: bool = False,
        int8: bool = False,
        batch_size: int = 1,
        model_input_height: int = 600,
        model_input_width: int = 600,
        perspective_height: int = 100,
        perspective_width: int = 100,
        input_height: int = 1280,
        input_width: int = 800,
        model_ppi: int = 100,
        input_ppi: int = 133,
    ) -> None:
        model = P2PModelV1(fp16)
        test_tensor = torch.rand(batch_size, 3, model_input_height, model_input_width).cuda()
        test_tensor_perspective = torch.rand(batch_size, 3, perspective_height, perspective_width).cuda()
        super(P2PBenchmark, self).__init__(
            fp16,
            int8,
            batch_size,
            [test_tensor_perspective, test_tensor],
            model,
            P2PBenchmark._TRT_MODEL_FILE,
            use_implicit_batch_dimension_trt=False,
        )
        self._input_width = input_width
        self._input_height = input_height
        self._perspective_width = perspective_width
        self._perspective_height = perspective_height
        self._model_input_width = model_input_width
        self._model_input_height = model_input_height
        self._model_ppi = model_ppi
        self._input_ppi = input_ppi
        self._fp16 = fp16
        self._int8 = int8
        self._batch_size = batch_size
        self._inputs = [test_tensor_perspective, test_tensor]
        self._model_cache_name = P2PBenchmark._TRT_MODEL_FILE
        self._use_implicit_batch_dimension_trt = False

        self._model = model
        self._model.eval().cuda()

    def _run_once(self) -> float:
        with torch.no_grad():
            input_dims = (self._input_width, self._input_height)
            input = (
                torch.rand(self._batch_size, 1, input_dims[1], input_dims[0], dtype=torch.float32)
                .cuda()
                .repeat((1, 3, 1, 1))
            )

            torch.cuda.synchronize()
            start = time.perf_counter()
            test_tensor_perspective = torch.rand(
                self._batch_size, 3, self._perspective_height, self._perspective_width, dtype=torch.float32
            ).cuda()

            crop_size = (self._model_input_width, self._model_input_height)
            first_crop_size = adjust_size_ppi(crop_size, self._model_ppi, self._input_ppi)
            test_tensor, _, _ = crop_or_pad(
                input, (input_dims[0] // 2, input_dims[1] // 2), (first_crop_size[0] + 10, first_crop_size[1] + 10)
            )

            second_crop_size = adjust_size_ppi((input.shape[-1], input.shape[-2]), self._input_ppi, self._model_ppi)
            test_tensor = interpolate(test_tensor, second_crop_size)
            final_crop_center = adjust_size_ppi(
                (first_crop_size[0] // 2, first_crop_size[1] // 2), self._input_ppi, self._model_ppi
            )
            test_tensor, _, _ = crop_or_pad(test_tensor, final_crop_center, crop_size)
            inputs = [test_tensor_perspective.contiguous(), test_tensor.contiguous()]
            torch.cuda.synchronize()
            end = time.perf_counter()
            self._model(*inputs)

        return (end - start) * 1000.0


if __name__ == "__main__":
    fire.Fire(P2PBenchmark)

import io
import logging
import os
import random
from typing import Any, List, Optional, Sized, Tuple

import cv2
import numpy as np
import numpy.typing as npt
import pandas
import requests
import torch
import torch.nn.functional as F
import torchvision.transforms.functional as TF
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential
from torch.utils.data import Dataset

from deeplearning.p2p.metadata import ImageMetadata, PredictionReconstructionMetadata, list_files
from deeplearning.utils.dataset import get_carbon_cache_host, index_split, pil_loader
from deeplearning.utils.images import (
    IMAGENET_MEANS,
    IMAGENET_STDS,
    crop_or_pad,
    random_color_jitter,
    random_gaussian_noise,
)
from deeplearning.utils.resize_utils import interpolate
from lib.common.image.resize import adjust_coord_ppi, adjust_size_ppi

DATASET_ELEMENT = Tuple[torch.Tensor, torch.Tensor, torch.Tensor, ImageMetadata, PredictionReconstructionMetadata]
LABEL_THRESHOLD = 0.5

LOG = logging.getLogger(__name__)


class P2PDataset(Dataset[DATASET_ELEMENT], Sized):
    def __init__(
        self,
        roots: Optional[List[str]] = None,
        files: Optional[List[ImageMetadata]] = None,
        perspective_crop_size_inches: float = 1,
        image_crop_size_inches: float = 6,
        model_ppi: int = 100,
        training: bool = False,
        splits: Optional[Tuple[int, ...]] = None,
        split_idx: Optional[int] = None,
        smearing: Optional[int] = None,
        num_samples: Optional[int] = None,
        seed: int = 1,
    ) -> None:
        super().__init__()
        self._perspective_crop_size_inches = perspective_crop_size_inches
        self._image_crop_size_inches = image_crop_size_inches
        self._model_ppi = model_ppi
        self._training = training
        assert smearing is None or smearing % 2 == 0, f"Smearing must be divisible by 2: {smearing}"
        self._half_smearing = int(smearing / 2) if smearing is not None else 0
        self._files: List[ImageMetadata] = []
        self._num_samples = num_samples
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)
        self._carbon_cache_host = get_carbon_cache_host()

        if files is not None:
            self._files = files
        else:
            assert roots is not None, "Either roots or files must be supplied to P2PDataset"
            for root in roots:
                assert os.path.exists(root), f"{root} does not exist"
                assert os.path.isdir(root), f"{root} is not a directory"
                found_at_least_one_certified_image = False
                for dirname, _, _ in os.walk(root + "/"):
                    for image_meta in list_files(dirname):
                        if image_meta.has_certified_label():
                            self._files.append(image_meta)
                            found_at_least_one_certified_image = True
                assert found_at_least_one_certified_image, f"{root} does not have any certified images"

            # Sort files for consistent results across machines
            self._files = sorted(self._files, key=lambda m: m.image_path)

            if splits is not None:
                assert split_idx is not None
                splits_indices = index_split(len(self._files), splits)
                self._files = [self._files[idx] for idx in splits_indices[split_idx]]

        metadata = []
        for index, file in enumerate(self._files):
            metadata.append(
                (
                    index,
                    file.crop_id,
                    file.robot_id,
                    file.captured_at.date().isoformat(),
                    file.label.filepath is not None,
                )
            )

        self._metadata_df = pandas.DataFrame(metadata, columns=["index", "crop_id", "robot_id", "date", "is_match"])

    def _find_centroid_xy(self, mask: npt.NDArray[Any], filepath: str) -> Optional[Tuple[float, float]]:
        _, _, stats, centroids = cv2.connectedComponentsWithStats((mask > 127).astype(np.uint8))

        # Delete centroid of largest component (background)
        centroids = np.delete(centroids, np.argmax(stats[:, -1]), 0)
        stats = np.delete(stats, np.argmax(stats[:, -1]), 0)

        assert (
            len(centroids) <= 1
        ), f"Expected none or a single annotation in {filepath}, got: {len(centroids)}: {centroids}"
        if len(centroids):
            cx, cy = centroids[0]
            return (cx, cy)
        else:
            return None

    def _random_transform(self, image: torch.Tensor) -> torch.Tensor:
        # Random color jitter
        image = random_color_jitter(
            image, brightness=(0.75, 1.5), saturation=(0.75, 1.5), hue=(-30, 30), gamma=(0.75, 1.5)
        )

        # Random gaussian noise
        image, _ = random_gaussian_noise(image, stddev=0.05)

        return image

    def _transform_point(self, point: Tuple[float, float], M: npt.NDArray[Any]) -> Tuple[float, float]:
        return cv2.transform(np.array([[point]]), M)[0, 0]  # type: ignore

    def _is_within(self, image: npt.NDArray[Any], point: Tuple[float, float]) -> bool:
        return point[0] >= 0 and point[0] < image.shape[1] and point[1] < image.shape[0] and point[1] >= 0

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def _get_image_from_cache(self, image_s3_path: str, grayscale: bool = False) -> Image.Image:
        response = requests.get(f"http://{self._carbon_cache_host}/maka-pono/{image_s3_path}", timeout=30)
        assert response.ok, f"Failed to retrieve image ({image_s3_path}) from cache: {response.text}"
        return Image.open(io.BytesIO(response.content), formats=["png"]).convert("L" if grayscale else "RGB")

    def _load_image(self, path: str, grayscale: bool = False) -> Image.Image:
        if self._carbon_cache_host is not None:
            return self._get_image_from_cache(path.replace("/data/deeplearning/p2p/", ""), grayscale=grayscale)
        else:
            return pil_loader(path, grayscale=grayscale)

    def _open_image(self, meta: ImageMetadata) -> Tuple[torch.Tensor, torch.Tensor, float, float, bool]:
        image = np.array(self._load_image(meta.image_path))
        if meta.label.filepath is not None:
            image_label = np.array(self._load_image(meta.label.filepath, grayscale=True))
            image_point = self._find_centroid_xy(image_label, meta.label.filepath)
        else:
            image_label = np.zeros_like(image[:, :, 0])
            image_point = None

        if self._training:
            # Random rotation and resize
            cx = random.uniform(0, image.shape[1])
            cy = random.uniform(0, image.shape[0])
            angle = random.uniform(-360, 360)
            scale = random.uniform(0.8, 1.2)
            M = cv2.getRotationMatrix2D((cx, cy), angle, scale)
            image = cv2.warpAffine(image, M, (image.shape[1], image.shape[0]))
            warped_image_point = self._transform_point(image_point, M) if image_point is not None else None
            if warped_image_point is not None and self._is_within(image, warped_image_point):
                image_point = warped_image_point
            else:
                image_point = None

            # Random transpose
            transpose = random.uniform(0.0, 1.0) > 0.5
            if transpose:
                image = image.transpose((1, 0, 2))
                image_point = (image_point[1], image_point[0]) if image_point is not None else None
        else:
            angle = 0.0
            scale = 1.0
            transpose = False

        # convert to tensors
        image_t = TF.to_tensor(image)

        if self._training:
            image_t = self._random_transform(image_t)

        # resize
        new_size = adjust_size_ppi((image_t.shape[1], image_t.shape[2]), meta.ppi, self._model_ppi)
        image_t_resized = interpolate(image_t.unsqueeze(0), new_size).squeeze(0)
        image_label_t_resized = torch.zeros((1, image_t.shape[1], image_t.shape[2]))
        if image_point is not None:
            new_image_point = adjust_coord_ppi(image_point, meta.ppi, self._model_ppi)
            image_label_t_resized[:, int(new_image_point[1]), int(new_image_point[0])] = 1.0

        return (image_t_resized, image_label_t_resized, angle, scale, transpose)

    def _open_perspective(self, meta: ImageMetadata, angle: float, scale: float, transpose: bool) -> torch.Tensor:
        assert len(meta.perspective_paths) > 0, f"Image {meta.image_path} does not have any perspectives"

        # TODO(asergeev): For now we're only using first perspective. Add support for multiple.
        perspective = np.array(self._load_image(meta.perspective_paths[0]))

        if self._training:
            # Apply rotation and resize
            cx = perspective.shape[1] / 2
            cy = perspective.shape[0] / 2
            M = cv2.getRotationMatrix2D((cx, cy), angle, scale)
            perspective = cv2.warpAffine(perspective, M, (perspective.shape[1], perspective.shape[0]))

            # Transpose
            if transpose:
                perspective = perspective.transpose((1, 0, 2))

        # convert to tensor
        perspective_t = TF.to_tensor(perspective)

        if self._training:
            perspective_t = self._random_transform(perspective_t)

        # resize
        new_size = adjust_size_ppi(
            (perspective_t.shape[1], perspective_t.shape[2]), meta.perspective_ppi, self._model_ppi
        )
        perspective_t_resized = interpolate(perspective_t.unsqueeze(0), new_size).squeeze(0)
        return perspective_t_resized

    def set_seed(self, seed: int) -> None:
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)

    def _sample_index(self) -> int:
        df = self._metadata_df

        # Sample the datapoint index
        index: int = self._rng.choice(df["index"])

        return index

    def __getitem__(self, index: int) -> DATASET_ELEMENT:
        if self._num_samples:
            index = self._sample_index()

        image_meta = self._files[index]

        # open the image
        image_t, image_label_t, angle, scale, transpose = self._open_image(image_meta)
        image_center_x = int(image_t.shape[2] / 2)
        image_center_y = int(image_t.shape[1] / 2)

        # crop image & label for batching
        image_crop_t, cropped_padded_to_original_shift_x, cropped_padded_to_original_shift_y = crop_or_pad(
            image_t, (image_center_x, image_center_y), (self.input_size, self.input_size)
        )
        image_label_crop_t, _, _ = crop_or_pad(
            image_label_t, (image_center_x, image_center_y), (self.input_size, self.input_size)
        )

        # open perspective
        perspective_t = self._open_perspective(image_meta, angle, scale, transpose)
        perspective_point_x = int(perspective_t.shape[2] / 2)
        perspective_point_y = int(perspective_t.shape[1] / 2)

        # crop perspective
        perspective_crop_t, _, _ = crop_or_pad(
            perspective_t,
            (perspective_point_x, perspective_point_y),
            (self.perspective_input_size, self.perspective_input_size),
        )

        # normalize tensors
        perspective_crop_t = TF.normalize(perspective_crop_t, mean=IMAGENET_MEANS[:3], std=IMAGENET_STDS[:3])
        image_crop_t = TF.normalize(image_crop_t, mean=IMAGENET_MEANS[:3], std=IMAGENET_STDS[:3])

        # introduce smearing
        image_crop_t = F.avg_pool2d(
            image_crop_t.unsqueeze(0), (1 + self._half_smearing * 2, 1), padding=(self._half_smearing, 0), stride=1
        ).squeeze(0)

        reconstruction_metadata = PredictionReconstructionMetadata(
            shift_x=cropped_padded_to_original_shift_x,
            shift_y=cropped_padded_to_original_shift_y,
            model_ppi=self.model_ppi,
        )

        return (perspective_crop_t, image_crop_t, image_label_crop_t, image_meta, reconstruction_metadata)

    def __len__(self) -> int:
        if self._num_samples is None:
            return len(self._files)
        else:
            return self._num_samples

    @property
    def num_files(self) -> int:
        return len(self._files)

    @property
    def input_size(self) -> int:
        return int(self._image_crop_size_inches * self._model_ppi)

    @property
    def perspective_input_size(self) -> int:
        return int(self._perspective_crop_size_inches * self._model_ppi)

    @property
    def model_ppi(self) -> int:
        return self._model_ppi

    @property
    def smearing(self) -> int:
        return self._half_smearing * 2


def label_coord(label: torch.Tensor, n: int) -> Optional[Tuple[int, int]]:
    assert len(label.shape) == 4, f"Wrong label shape: {label.shape}"

    coords = (label[n, 0] > LABEL_THRESHOLD).nonzero()
    if not len(coords):
        return None

    y, x = coords[0]
    return (int(x), int(y))


class P2PTrainValDatasets:
    def __init__(
        self,
        train_dataset: P2PDataset,
        validation_dataset: P2PDataset,
        test_dataset: P2PDataset,
        calibration_dataset: P2PDataset,
    ) -> None:
        self.train_dataset = train_dataset
        self.validation_dataset = validation_dataset
        self.test_dataset = test_dataset
        self.calibration_dataset = calibration_dataset

    def get_training(self) -> P2PDataset:
        return self.train_dataset

    def get_validation(self) -> P2PDataset:
        return self.validation_dataset

    def get_test(self) -> P2PDataset:
        return self.test_dataset

    def get_calibration(self) -> P2PDataset:
        return self.calibration_dataset

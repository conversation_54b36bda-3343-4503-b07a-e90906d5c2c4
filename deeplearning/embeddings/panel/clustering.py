import os
import random
from collections import defaultdict
from typing import Any, Dict, List, Tuple

import pandas as pd
import panel as pn
import plotly.express as px

from deeplearning.embeddings.utils.clustering import get_clustering_directory, get_progress
from deeplearning.panel.embedding_vis import EmbeddingPoint, get_cluster_information

pn.extension(loading_spinner="dots", loading_color="#1e52d6", sizing_mode="stretch_width")
pn.extension("plotly")


def plot_metrics(metric_filepath: str) -> Tuple[List[Any], List[Any]]:
    metrics = get_progress(metric_filepath)

    plots = []
    metric_dfs: Dict[str, Any] = {}
    for metric, k_dict in metrics.items():
        scores = sorted([{"k": int(k), "score": v} for k, v in k_dict.items()], key=lambda x: x["k"])
        df = pd.DataFrame(scores)
        plt = px.line(df, x="k", y="score", title=metric)
        plots.append(pn.panel(plt))

        met, algo = eval(metric)

        if algo not in metric_dfs:
            metric_dfs[algo] = {}

        for item in scores:
            k = item["k"]
            score = item["score"]
            if k not in metric_dfs[algo]:
                metric_dfs[algo][k] = {}
            metric_dfs[algo][k][met] = score

    dataframes = []
    for algo, data in metric_dfs.items():
        list_of_data = []
        sorted_keys = sorted([k for k in data.keys()])
        for key in sorted_keys:
            item = {"k": key}
            item.update(data[key])
            list_of_data.append(item)

        dataframes.append(pn.Column(pn.panel(algo), pn.panel(pd.DataFrame(list_of_data), name=algo)))

    return plots, dataframes


def run_sweeps(
    metric_filename: str,
    clustering_information_filepath: str,
    algorithm: str,
    number_clusters: int,
    display_images: bool,
) -> Any:
    if metric_filename == "":
        return None

    cluster_information, _ = get_cluster_information(clustering_information_filepath, algorithm, str(number_clusters))

    images = []
    if cluster_information is not None:
        cluster_label_to_filepaths = defaultdict(list)
        for image, points in cluster_information.items():
            for point in points:
                if point["x"] < 200 or point["y"] < 200 or point["x"] > 2800 or point["y"] > 2800:
                    continue
                cluster_label_to_filepaths[point["cluster_label"]].append(
                    {"image": image, "cluster_label": point["cluster_label"], "x": point["x"], "y": point["y"]}
                )
        sorted_cluster = [str(val) for val in sorted([int(ind) for ind in cluster_label_to_filepaths.keys()])]
        for cluster in sorted_cluster:
            image_filepaths = cluster_label_to_filepaths[cluster]
            random.shuffle(image_filepaths)
            images.append(pn.panel(f"Cluster {cluster}"))
            image_row = []
            for file in image_filepaths[:10]:

                embedding_point = EmbeddingPoint(
                    embedding=[],
                    embedding_type="",
                    point_type="",
                    category="",
                    filepath=file["image"],
                    hit_class="",
                    x=file["x"],
                    y=file["y"],
                    point_id=0,
                )
                cached_filepath = embedding_point.cache_filepath
                image_row.append(pn.pane.Image(cached_filepath, width=200, height=200))
                if len(image_row) == 10:
                    images.append(pn.Row(*image_row))
                    image_row = []
            if len(image_row):
                images.append(*image_row)

    metric_filepath = os.path.join(get_clustering_directory(), f"{metric_filename}.json")

    plots = []
    elbow_vis, dataframes = plot_metrics(metric_filepath)
    if display_images:
        plots.extend(images)
    plots.extend(dataframes)
    plots.extend(elbow_vis)

    return pn.Column(*plots)


files = [""] + [
    os.path.splitext(f)[0]
    for f in os.listdir(get_clustering_directory())
    if os.path.isfile(os.path.join(get_clustering_directory(), f))
]

metric_filepath = pn.widgets.Select(name="Models with Metrics", options=files)
pn.param.ParamMethod.loading_indicator = True

clustering_information_filepath = pn.widgets.TextInput(name="Cluster label filepath")
clustering_algorithm = pn.widgets.TextInput(name="Cluster algorithm")
clustering_number = pn.widgets.IntInput(name="Number of clusters")
display_images = pn.widgets.Checkbox(name="Display Images")

app = pn.Column(
    pn.Row(metric_filepath),
    pn.Row(clustering_information_filepath, clustering_algorithm, clustering_number),
    pn.Row(display_images),
    pn.Row(
        pn.param.ParamFunction(
            pn.bind(
                run_sweeps,
                metric_filepath,
                clustering_information_filepath,
                clustering_algorithm,
                clustering_number,
                display_images,
            ),
            loading_indicator=True,
        ),
    ),
)

app.servable()

import json
from typing import Any, Dict

import panel as pn
import plotly.express as px

from deeplearning.embeddings.scripts.utils import get_displayable_scores

pn.extension("plotly")

cached_data: Dict[str, Any] = {}


def get_metric_dropdown(metric: str) -> pn.widgets.Select:
    available_configs = sorted(list(cached_data["data"][metric].keys()))
    available_mets = set()
    for item in available_configs:
        for key, _ in eval(item).items():
            available_mets.add(key)
    x_variable = pn.widgets.Select(name="X variable", options=list(available_mets))
    return x_variable


def get_table(data: Dict[str, Any]) -> pn.panel:
    df = get_displayable_scores(data)
    return pn.panel(df,)


def plot_by_x_variable(metric: str, x_variable: str, min_neighborhood_size: int) -> pn.Column:
    df = get_displayable_scores(cached_data["data"]).sort_values(by=x_variable)
    figures = []

    color_discrete_map = dict(zip(df["model_id"].unique(), px.colors.qualitative.G10))

    for embedding_type in list(df["embedding_type"].unique()):

        fig = px.line(
            df[
                (df["embedding_type"] == embedding_type)
                & (df["metric"] == metric)
                & (df["mean_len"] > min_neighborhood_size)
            ],
            x=x_variable,
            y="mean",
            color="model_id",
            title=f"{embedding_type} embeddings (mean {metric} against {x_variable})",
            markers=True,
            color_discrete_map=color_discrete_map,
            custom_data=[x_variable, "mean", "std", "mean_len", "std_len"],
        )
        fig.update_traces(
            mode="markers+lines",
            hovertemplate="<b>X:</b>%{customdata[0]}<br>"
            + "<b>Mean:</b> %{customdata[1]}<br>"
            + "<b>std:</b> %{customdata[2]}<br>"
            + "<b>Mean Neighborhood:</b> %{customdata[3]}<br>"
            + "<b>std Neighborhood:</b> %{customdata[4]}<br><br>"
            + "<extra></extra>",
        )
        fig.update_layout(yaxis_title=metric)
        figures.append(pn.panel(fig, width_policy="max"))

    return pn.Column(*figures, width_policy="max")


def plot_by_model_id(metric: str, x_variable: str, min_neighborhood_size: int) -> pn.Column:
    df = get_displayable_scores(cached_data["data"]).sort_values(by=x_variable)
    figures = []

    color_discrete_map = dict(zip(df["embedding_type"].unique(), px.colors.qualitative.G10))

    for model_id in list(df["model_id"].unique()):
        fig = px.line(
            df[(df["model_id"] == model_id) & (df["metric"] == metric) & (df["mean_len"] > min_neighborhood_size)],
            x=x_variable,
            y="mean",
            color="embedding_type",
            title=f"{model_id} embeddings (mean {metric} against {x_variable})",
            markers=True,
            color_discrete_map=color_discrete_map,
            custom_data=[x_variable, "mean", "std", "mean_len", "std_len"],
        )
        fig.update_traces(
            mode="markers+lines",
            hovertemplate="<b>X:</b>%{customdata[0]}<br>"
            + "<b>Mean:</b> %{customdata[1]}<br>"
            + "<b>std:</b> %{customdata[2]}<br>"
            + "<b>Mean Neighborhood:</b> %{customdata[3]}<br>"
            + "<b>std Neighborhood:</b> %{customdata[4]}<br><br>"
            + "<extra></extra>",
        )
        fig.update_layout(yaxis_title=metric)
        figures.append(pn.panel(fig, width_policy="max"))

    return pn.Column(*figures, width_policy="max")


def get_plot(filepath: str, metric: str, x_variable: str, min_neighborhood_size: int) -> pn.Column:
    if filepath == "":
        return "Pass a filepath"

    if not ("filepath" in cached_data and cached_data["filepath"] == filepath):
        with open(filepath) as f:
            cached_data["data"] = json.load(f)
        cached_data["filepath"] = filepath

    items = []
    if metric != "" and x_variable != "":
        items.append(plot_by_x_variable(metric, x_variable, min_neighborhood_size))
        items.append(plot_by_model_id(metric, x_variable, min_neighborhood_size))
    items.append(get_table(cached_data["data"]))
    return pn.Column(*items)


filepath_widget = pn.widgets.input.TextInput(name="Filepath", value="", placeholder="File")
metric = pn.widgets.input.TextInput(name="Metric", value="", placeholder="Metric")
x_variable = pn.widgets.input.TextInput(name="X Var", value="", placeholder="X Variable")
min_neighborhood_size = pn.widgets.input.IntInput(name="Minimum Neighborhood Size", value=0)

app = pn.Column(
    pn.Row(filepath_widget, metric, x_variable, min_neighborhood_size),
    pn.Row(
        pn.bind(get_plot, filepath_widget, metric, x_variable, min_neighborhood_size),
        width_policy="max",
        height_policy="max",
    ),
    width_policy="max",
    height_policy="max",
)

app.servable()

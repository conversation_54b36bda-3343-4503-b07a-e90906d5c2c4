from typing import Any, Dict

import pandas as pd


def get_displayable_scores(scores: Dict[str, Any]) -> pd.DataFrame:
    displayable_scores = []
    for model_id, model_scores in scores.items():
        for met_name, met_configs in model_scores.items():
            for met_config, met_embedding_types in met_configs.items():
                for met_embedding_type, met_values in met_embedding_types.items():
                    displayable_scores.append(
                        {
                            "model_id": model_id,
                            "metric": met_name,
                            "embedding_type": met_embedding_type,
                            **eval(met_config),
                            **met_values,
                        }
                    )

    return pd.DataFrame(displayable_scores)

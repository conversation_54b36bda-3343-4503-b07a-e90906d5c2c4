# Onion
python -m deeplearning.clustering.calculate_cluster_scores --model-id fut-20241029-ti44p2ph37 --min-k 2 --max-k 10 --metrics "comparison_silhouette,comparison_similarity,silhouette" --clustering-output /data/deeplearning/cluster_analysis/testing_fut-20241029-ti44p2ph37.txt --no-cache

# LDGL
python -m deeplearning.clustering.calculate_cluster_scores --model-id fut-20241031-wph1o2o6yo --min-k 2 --max-k 10 --metrics "comparison_silhouette,comparison_similarity,silhouette" --clustering-output /data/deeplearning/cluster_analysis/testing_fut-20241031-wph1o2o6yo.txt --no-cache

# Carrot 
python -m deeplearning.clustering.calculate_cluster_scores --model-id fut-20241026-u73domskra --min-k 2 --max-k 10 --metrics "comparison_silhouette,comparison_similarity,silhouette" --clustering-output /data/deeplearning/cluster_analysis/testing_fut-20241026-u73domskra.txt --no-cache

# Stevia
python -m deeplearning.clustering.calculate_cluster_scores --model-id fut-20241031-knh49ocob6 --min-k 2 --max-k 10 --metrics "comparison_silhouette,comparison_similarity,silhouette" --clustering-output /data/deeplearning/cluster_analysis/testing_fut-20241031-knh49ocob6.txt --no-cache

# Parsley
python -m deeplearning.clustering.calculate_cluster_scores --model-id fut-20241030-g5jtlpfvzs --min-k 2 --max-k 10 --metrics "comparison_silhouette,comparison_similarity,silhouette" --clustering-output /data/deeplearning/cluster_analysis/testing_fut-20241030-g5jtlpfvzs.txt --no-cache
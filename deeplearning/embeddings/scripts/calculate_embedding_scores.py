import argparse
import io
import json
import logging
import os
import random
import threading
import time
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional, Tuple, Type, cast

import numpy as np
import pandas as pd
import requests
import torch
import umap
from scipy.spatial import KDTree
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize
from tqdm import tqdm

from deeplearning import dl_metrics
from deeplearning.comparison.data_utils import (
    get_all_annotations_from_images,
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_prefix,
    get_images_with_comparison_embeddings,
    load_embeddings_from_torch,
)
from deeplearning.constants import CARBON_DATA_DIR, Environment
from deeplearning.embeddings.metrics.aggregate import MeanNeighborhoodAverage
from deeplearning.embeddings.metrics.comparison_silhouette_score import ComparisonSilhouetteScore
from deeplearning.embeddings.metrics.comparison_similarity_score import ComparisonSimilarityScore
from deeplearning.embeddings.metrics.neighborhood import Neighborhood, PumapComparisonPoint
from deeplearning.embeddings.metrics.score import Score
from deeplearning.embeddings.scripts.utils import get_displayable_scores
from deeplearning.scripts.utils.utils import get_dataset, pick_comparison_model_id
from deeplearning.utils.dataset import get_carbon_cache_host
from deeplearning.utils.download_utils import download_records

METRICS: Dict[str, Type[Score]] = {
    "comparison_similarity_score": ComparisonSimilarityScore,
    "comparison_silhouette_score": ComparisonSilhouetteScore,
}

NUM_NEIGHBORHOODS_DEFAULT = 100


def get_cache_filename(
    comparison_model_id: str, model_id: str, embedding_type: str, config: Optional[Dict[str, Any]]
) -> str:
    return f"{comparison_model_id}_{model_id}_{embedding_type}_{config}.pt"


def load_cache_file(
    model_id: str, comparison_model_id: str, embedding_type: str, max_pairs: int, config: Optional[Dict[str, Any]]
) -> Optional[Dict[str, Any]]:
    filename = get_cache_filename(comparison_model_id, model_id, embedding_type, config)
    cache_directory = os.path.join(CARBON_DATA_DIR, "deeplearning", "embedding_analysis_cache")

    if not os.path.exists(os.path.join(cache_directory, filename)):
        return None
    else:
        file: Dict[str, Any] = torch.load(os.path.join(cache_directory, filename))
        if file["max_pairs"] < max_pairs:
            return None
        return file


def get_dataset_from_model(model_id: str) -> str:
    path = os.path.join(CARBON_DATA_DIR, "deeplearning", "models", model_id, "veselka-metadata.json")
    with open(path) as f:
        file = json.load(f)
        loaded_dataset_id = file["dataset_id"]

    dataset_id, _ = get_dataset(dataset_id=loaded_dataset_id)

    return dataset_id


def generate_nearest_neighborhoods(
    name: str,
    metric_config: Dict[str, Any],
    comparison_points: List[PumapComparisonPoint],
    points_kd: KDTree,
    return_non_neighborhood: bool = False,
) -> Tuple[List[Neighborhood], Optional[List[Neighborhood]]]:
    neighborhoods: List[Neighborhood] = []
    non_neighborhoods: List[Neighborhood] = []
    failure = 0
    while len(neighborhoods) < metric_config["num_neighborhoods"] and failure < 3 * metric_config["num_neighborhoods"]:
        point_idx = random.randint(0, len(comparison_points) - 1)
        point = comparison_points[point_idx]

        # If looking for non_neighborhoods, should I just get the next radius worth of items for the non-neighborhood?
        neighbors_idx = points_kd.query_ball_point(x=point.pumap, r=metric_config["max_radius"])
        if len(neighbors_idx) == 0:
            failure += 1
            continue

        neighborhoods.append(Neighborhood(point, [comparison_points[x] for x in neighbors_idx[0] if x != point_idx]))

        if return_non_neighborhood:
            non_neighbors_idx = points_kd.query_ball_point(x=point.pumap, r=2 * metric_config["max_radius"])
            if len(non_neighbors_idx) == 0:
                failure += 1
                continue

            non_neighborhoods.append(
                Neighborhood(
                    point,
                    [comparison_points[x] for x in non_neighbors_idx[0] if x != point_idx and x not in neighbors_idx],
                )
            )

    return neighborhoods, non_neighborhoods if return_non_neighborhood else None


def add_metrics_args(parser: argparse.ArgumentParser) -> None:
    parser.add_argument("--comparison-similarity-score-config", nargs="+", type=json.loads, default=[{}])
    parser.add_argument("--comparison-silhouette-score-config", nargs="+", type=json.loads, default=[{}])
    parser.add_argument("--metrics", nargs="+", default=list(METRICS.keys()))


def create_metrics_configs(args: Any) -> Dict[str, List[Dict[str, Any]]]:
    metric_configs = defaultdict(list)

    for config in args.comparison_similarity_score_config:
        metric_configs["comparison_similarity_score"].append(
            {
                "max_radius": 0.1,
                "num_neighbors": None,
                "num_neighborhoods": NUM_NEIGHBORHOODS_DEFAULT,
                "neighborhood_method": "nearest_neighbors",
                **config,
            }
        )

    for config in args.comparison_silhouette_score_config:
        metric_configs["comparison_silhouette_score"].append(
            {
                "max_radius": 0.1,
                "num_neighbors": None,
                "num_neighborhoods": NUM_NEIGHBORHOODS_DEFAULT,
                "neighborhood_method": "nearest_neighbors",
                "non_neighborhood": True,
                **config,
            }
        )

    for metric_name, configs in metric_configs.items():
        for config in configs:
            assert (
                config["num_neighbors"] is not None or config["max_radius"] is not None
            ), f"{metric_name}: num_neighbors and max_radius cannot both be None"

            assert config["num_neighborhoods"] >= 1

    return metric_configs


def get_pt_from_cache(bucket: str, key: str, row: pd.Series) -> Optional[torch.Tensor]:
    response = requests.get(f"http://{get_carbon_cache_host()}/{bucket}/{key}", timeout=30)
    assert response.ok, f"Failed to retrieve item ({key}) from cache: {response.text}"
    embedding_dict = load_embeddings_from_torch(io.BytesIO(response.content))

    for idx in range(len(embedding_dict["embeddings_data"])):
        data = embedding_dict["embeddings_data"][idx]
        if (abs(data["x"] - row["x"]) <= 1e-6 and abs(data["y"] - row["y"]) <= 1e-6) or (
            abs(data["x"] - row["y"]) <= 1e-6 and abs(data["y"] - row["x"]) <= 1e-6
        ):
            return cast(torch.Tensor, embedding_dict["embeddings"][idx])
    return None


def retrieve_comparison_annotations(
    dataset_id: str, comparison_model_id: str, filename: str = "test.json",
) -> Tuple[Dict[str, List[Dict[str, Any]]], Dict[str, str], str]:

    dataset_annotations_dict: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    path = os.path.join(CARBON_DATA_DIR, "deeplearning", "datasets", dataset_id, filename)
    if path.endswith("json"):
        with open(path, "r") as f:
            file = json.load(f)

        image_ids_with_comparison_embeddings, local_dir = get_images_with_comparison_embeddings(
            model_id=comparison_model_id
        )
        logging.info(f"Get images with comparison embeddings with size: {len(image_ids_with_comparison_embeddings)}.")

        image_url2id = {image["uri"]: image["id"] for image in file["images"]}
        image_ids_from_file = set(image_url2id.values())
        annotations = get_all_annotations_from_images(path=path, image_ids=image_ids_with_comparison_embeddings)
        logging.info(f"Get all annotations from images with size: {len(annotations)}.")

        max_workers = min(32, (os.cpu_count() or 1) * 5)
        lock = threading.Lock()

        def process_annotation(annotation: Dict[str, Any]) -> None:
            image_id = annotation["image_id"]
            if (
                image_id not in image_ids_with_comparison_embeddings
                or image_id not in image_ids_from_file
                or annotation["annotation_type"] != "point"
            ):
                return

            with lock:
                dataset_annotations_dict[image_id].append(annotation)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_annotation, annotation) for annotation in tqdm(annotations)]

            with tqdm(total=len(futures), desc="Processing annotations") as pbar:
                for future in as_completed(futures):
                    future.result()
                    pbar.update(1)

    else:
        with open(path, "r") as f:
            file = [json.loads(line) for line in f]

        image_ids_with_comparison_embeddings, local_dir = get_images_with_comparison_embeddings(
            model_id=comparison_model_id
        )
        logging.info(f"Get images with comparison embeddings with size: {len(image_ids_with_comparison_embeddings)}.")

        image_url2id = {image["uri"]: image["image_id"] for image in file}
        dataset_annotations_dict = {
            img["image_id"]: img["points"] for img in file if img["image_id"] in image_ids_with_comparison_embeddings
        }

    return dataset_annotations_dict, image_url2id, local_dir


def query_model_embeddings(model_id: str, embedding_type: str) -> List[Any]:
    logging.info("Querying Model Embeddings")

    eval_dir = f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}"
    point_db_path = (
        os.path.join(eval_dir, "test_dataframes/points_v2.db")
        if embedding_type == "REDUCED_SCALED"
        else os.path.join(eval_dir, "test_unoptimized_dataframes/points_v2.db")
    )

    with dl_metrics.get_session(dl_metrics.get_db(point_db_path)) as sess:
        query = (
            sess.query(
                dl_metrics.Embedding.point_id.label("point_id"),
                dl_metrics.Point.image_id.label("image_id"),
                dl_metrics.Embedding.embedding.label("embedding"),
                dl_metrics.Point.category_class_name.label("category"),
                dl_metrics.Image.filepath.label("filepath"),
                dl_metrics.Point.x.label("x"),
                dl_metrics.Point.y.label("y"),
                dl_metrics.Point.uuid.label("uuid"),
            )
            .join(dl_metrics.Point, dl_metrics.Embedding.point_id == dl_metrics.Point.id)
            .join(dl_metrics.Image, dl_metrics.Point.image_id == dl_metrics.Image.id)
            .filter(
                dl_metrics.Embedding.type
                == (
                    dl_metrics.EmbeddingType.REDUCED_SCALED
                    if embedding_type == "REDUCED_SCALED"
                    else dl_metrics.EmbeddingType.FULL
                )
            )
            .filter(dl_metrics.Point.confidence > 0)
            .filter(dl_metrics.Point.type == dl_metrics.PointType.LABEL)
            .filter(dl_metrics.Point.hit_class != dl_metrics.HitClass.PLANT)
            .all()
        )

        return [dict(item) for item in query]


def make_df(embeddings: List[Any]) -> pd.DataFrame:
    return pd.DataFrame(embeddings, columns=list(embeddings[0].keys()),)


def apply_reducer(reducer: Any, embedding_objects: List[Any]) -> pd.DataFrame:
    embedding_values = [obj["embedding"] for obj in embedding_objects]
    low_dim = reducer.fit_transform(normalize(embedding_values))
    for i in range(len(embedding_objects)):
        embedding_objects[i]["embedding"] = low_dim[i]

    return embedding_objects


def binary_embeddings(embedding_objects: List[Any]) -> pd.DataFrame:
    for embedding_object in embedding_objects:
        binary_embedding = torch.round(torch.sigmoid(torch.tensor(embedding_object["embedding"])))
        random_noise = 0.001 * torch.randn_like(binary_embedding)
        # Adding random noise to help KDTree deal with duplicate points
        embedding_object["embedding"] = (binary_embedding + random_noise).tolist()
    return embedding_objects


def pca_embeddings(embedding_objects: List[Any], config: Dict[str, Any]) -> pd.DataFrame:
    logging.info(f"PCA reducing with config {config}")
    reducer = PCA(n_components=config.get("n_components", 2),)
    return apply_reducer(reducer, embedding_objects)


def umap_embeddings(embedding_objects: List[Any], config: Dict[str, Any]) -> pd.DataFrame:
    logging.info(f"UMAP reducing with config {config}")
    reducer = umap.UMAP(
        n_neighbors=config.get("n_neighbors", 25),
        min_dist=config.get("min_dist", 0.1),
        n_components=config.get("n_components", 2),
        metric="euclidean",
        spread=1,
    )
    return apply_reducer(reducer, embedding_objects)


def aggregate_annotation_embedding_pairs(
    model_embeddings: pd.DataFrame,
    dataset_annotations_dict: Dict[str, List[Dict[str, Any]]],
    comparison_model_id: str,
    model_id: str,
    image_url2id: Dict[str, str],
    max_pairs: int,
    embedding_type: str,
    config: Optional[Dict[str, Any]],
) -> List[PumapComparisonPoint]:
    pumap_comparison_points: List[PumapComparisonPoint] = []
    for idx, row in model_embeddings.iterrows():
        # ex: s3://maka-pono/media/slayer53/2023-12-03/0c065c8e-2996-4bb1-8500-87bb5850c91e/predict_slayer53_row3_predict3_2023-12-03T21-16-48-349000Z.png
        image_id = image_url2id.get(row["filepath"])
        if image_id is None or dataset_annotations_dict.get(image_id) is None:
            continue
        for annotation in dataset_annotations_dict[image_id]:
            if ((abs(annotation["x"] - row["y"]) <= 1e-6 and abs(annotation["y"] - row["x"]) <= 1e-6)) or (
                (abs(annotation["x"] - row["x"]) <= 1e-6 and abs(annotation["y"] - row["y"]) <= 1e-6)
                and annotation["confidence"] > 0
            ):
                pumapped_point: torch.Tensor = torch.Tensor(row["embedding"])
                comparison_embedding = get_pt_from_cache(
                    get_comparison_evaluation_bucket(),
                    f"{get_comparison_evaluation_prefix(comparison_model_id, Environment.PRODUCTION)}/{image_id}.pt",
                    row,
                )
                if comparison_embedding is not None:
                    pumap_comparison_points.append(
                        PumapComparisonPoint(
                            pumapped_point.unsqueeze(0), comparison_embedding.unsqueeze(0), metadata=row
                        )
                    )

                break

        if len(pumap_comparison_points) >= max_pairs:
            break

    filename = get_cache_filename(comparison_model_id, model_id, embedding_type, config)
    cache_directory = os.path.join(CARBON_DATA_DIR, "deeplearning", "embedding_analysis_cache")

    if os.path.exists(os.path.join(cache_directory, filename)):
        os.remove(os.path.join(cache_directory, filename))
    os.makedirs(cache_directory, exist_ok=True)
    torch.save({"points": pumap_comparison_points, "max_pairs": max_pairs}, os.path.join(cache_directory, filename))

    return pumap_comparison_points


def main() -> None:  # noqa: C901
    parser = argparse.ArgumentParser()

    parser.add_argument("--comparison-model-id", type=str, default=None)
    parser.add_argument("--model-id", nargs="+", type=str, default=None)
    parser.add_argument("--embedding-type", nargs="+", type=str, help="FULL, REDUCED_SCALED, UMAP, PCA, BINARY")
    parser.add_argument("--umap-config", nargs="+", type=json.loads, default=[{"n_components": 2}])
    parser.add_argument("--pca-config", nargs="+", type=json.loads, default=[{"n_components": 2}])
    parser.add_argument("--max-pairs", type=int, default=5000)
    parser.add_argument("--output-path", default=None, type=str)
    add_metrics_args(parser)

    args = parser.parse_args()
    metrics_args = create_metrics_configs(args)

    assert args.model_id is not None
    scores: Dict[str, Dict[str, Dict[str, Any]]] = defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))

    for model_id in args.model_id:
        for embedding_type in args.embedding_type:
            configs: List[Dict[str, Any]] = [{}]
            if embedding_type == "UMAP":
                configs = args.umap_config
            elif embedding_type == "PCA":
                configs = args.pca_config
            for config in configs:
                comparison_model_id = pick_comparison_model_id(args.comparison_model_id, None)

                cache_data = load_cache_file(model_id, comparison_model_id, embedding_type, args.max_pairs, config)

                if cache_data is not None:
                    logging.info("Cache File Found")
                    comparison_points = cache_data["points"]
                else:
                    # --------- Downloading Appropriate Data
                    logging.info("Downloading Data")
                    download_records(model_id, s3_directory="models", include_points_db=True, skip_existing_files=True)
                    dataset_id = get_dataset_from_model(model_id=model_id)

                    # ---------- Getting Annotations with Comparisons
                    start = time.time()
                    dataset_annotations_dict, image_url2id, _ = retrieve_comparison_annotations(
                        dataset_id, comparison_model_id
                    )
                    logging.info(f"Retrieving Comparison Annotations {time.time() - start:.2f}")

                    # --------- Query Model Embeddings
                    start = time.time()
                    model_embedding_query = query_model_embeddings(model_id=model_id, embedding_type=embedding_type)
                    logging.info(f"Querying Model Embeddings: {time.time() - start:.2f} seconds")

                    if embedding_type == "UMAP":
                        start = time.time()
                        assert config is not None
                        model_embedding_query = umap_embeddings(model_embedding_query, config=config)
                        logging.info(f"UMAPing embeddings: {time.time() - start:.2f} seconds")
                    elif embedding_type == "PCA":
                        start = time.time()
                        assert config is not None
                        model_embedding_query = pca_embeddings(model_embedding_query, config=config)
                        logging.info(f"PCAing embeddings: {time.time() - start:.2f} seconds")
                    elif embedding_type == "BINARY":
                        start = time.time()
                        model_embedding_query = binary_embeddings(model_embedding_query)
                        logging.info(f"Binarificating embeddings: {time.time() - start:.2f} seconds")

                    start = time.time()
                    model_embeddings = make_df(model_embedding_query)
                    logging.info(f"Constructing Model Embedding DataFrame: {time.time() - start:.2f} seconds")
                    # --------- Aggregate
                    start = time.time()
                    comparison_points = aggregate_annotation_embedding_pairs(
                        model_embeddings,
                        dataset_annotations_dict,
                        comparison_model_id,
                        model_id,
                        image_url2id,
                        args.max_pairs,
                        embedding_type,
                        config,
                    )
                    logging.info(f"Aggregation: {time.time() - start:.2f} seconds")

                logging.info(f"Computing Metrics: model_id={model_id} embedding_type={embedding_type} config={config}")
                points_array = np.concatenate([x.pumap for x in comparison_points])
                points_kd = KDTree(points_array)
                for name in args.metrics:
                    metric = METRICS[name]
                    neighborhoods: List[Neighborhood] = []

                    metric_configs = metrics_args[name]

                    for metric_config in metric_configs:
                        logging.info(f"          metric={name}, config={metric_config}")
                        if metric_config["neighborhood_method"] == "nearest_neighbors":
                            neighborhoods, non_neighborhoods = generate_nearest_neighborhoods(
                                name,
                                metric_config,
                                comparison_points,
                                points_kd,
                                return_non_neighborhood=metric_config.get("non_neighborhood", False),
                            )

                        assert (
                            len(neighborhoods) >= 1
                        ), "Invalid neighborhood method, must be in ['nearest_neighbors', 'random']"

                        neighborhood_scores = MeanNeighborhoodAverage(neighborhoods, metric, non_neighborhoods)
                        mean_neighborhood_average = neighborhood_scores.calculate()
                        if mean_neighborhood_average is None:
                            logging.warning(
                                f"Couldn't find non-empty neighborhoods for metric={name}, config={str(metric_config)}, embedding_type={embedding_type}. Skipping..."
                            )
                            continue

                        mean_average_items = {key: val.item() for key, val in mean_neighborhood_average.items()}
                        emb_type_name = embedding_type
                        if config is not None:
                            emb_type_name = f"{emb_type_name}_{config}"
                        scores[model_id][name][str(metric_config)][emb_type_name] = mean_average_items
                        scores[model_id][name][str(metric_config)][emb_type_name].update(neighborhood_scores.len_dist())

                        # Incrementally save
                        if args.output_path is not None:
                            with open(args.output_path, "w") as f:
                                json.dump(scores, f)

    displayable_scores_df = get_displayable_scores(scores)
    logging.info("\n" + str(displayable_scores_df))


if __name__ == "__main__":
    main()

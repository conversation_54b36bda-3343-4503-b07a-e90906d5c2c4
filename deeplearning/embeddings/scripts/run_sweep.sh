# python -m deeplearning.embeddings.scripts.calculate_embedding_scores \
#  --model-id fut-20241017-utl58frb63 \ #fut-20240826-0ajw79iki6 fut-20240824-idqjyg4cbx fut-20240826-pfu8a8oqvg fut-20240824-iqensc5m11 fut-20240818-nvtom75jew \
#  --output-path /data/deeplearning/output-fut-20241017-utl58frb63.json \
#  --comparison-similarity-score-config '{"max_radius": 0.03}' '{"max_radius": 0.1}' '{"max_radius": 0.5}' '{"max_radius": 1}' '{"max_radius": 3}' '{"max_radius": 5}' '{"max_radius": 10}' '{"max_radius": 15}' '{"max_radius": 20}' '{"max_radius": 25}' '{"max_radius": 30}' '{"max_radius": 35}' '{"max_radius": 40}' '{"max_radius": 45}' '{"max_radius": 50}' '{"max_radius": 60}' '{"max_radius": 75}' '{"max_radius": 100}' '{"max_radius": 150}' '{"max_radius": 200}' \
#  --metrics comparison_similarity_score \
#  --umap-config '{"n_neighbors": 5}' '{"n_neighbors": 15}' '{"n_neighbors": 25}' '{"n_neighbors": 5, "n_components": 128}' '{"n_neighbors": 15, "n_components": 128}' '{"n_neighbors": 25, "n_components": 128}' \
#  --pca-config '{"n_components": 2}' '{"n_components": 4}' '{"n_components": 8}' '{"n_components": 32}' '{"n_components": 64}' '{"n_components": 128}' '{"n_components": 256}' '{"n_components": 512}' \
#  --embedding-type FULL # REDUCED_SCALED # FULL REDUCED_SCALED UMAP

# python -m deeplearning.embeddings.scripts.calculate_embedding_scores \
#  --model-id fut-20241017-utl58frb63 fut-20241019-lga0oem0im  \
#  --output-path /data/deeplearning/output-triplet-vs-contrastive-brassica-3.json \
#  --comparison-similarity-score-config '{"max_radius": 0.03}' '{"max_radius": 0.1}' '{"max_radius": 0.5}' '{"max_radius": 1}' '{"max_radius": 3}' '{"max_radius": 5}' '{"max_radius": 10}' '{"max_radius": 15}' '{"max_radius": 20}' '{"max_radius": 25}' '{"max_radius": 50}' '{"max_radius": 75}' '{"max_radius": 100}' '{"max_radius": 150}' '{"max_radius": 200}' '{"max_radius": 300}' '{"max_radius": 400}' '{"max_radius": 500}' \
#  --metrics comparison_similarity_score \
#  --embedding-type FULL REDUCED_SCALED BINARY # FULL REDUCED_SCALED UMAP

#  python -m deeplearning.embeddings.scripts.calculate_embedding_scores \
#  --model-id fut-20241017-utl58frb63  \
#  --output-path /data/deeplearning/output-test-1.json \
#  --comparison-similarity-score-config '{"max_radius": 0.03}' '{"max_radius": 0.1}' '{"max_radius": 0.5}' '{"max_radius": 1}' '{"max_radius": 3}' '{"max_radius": 5}' '{"max_radius": 10}' '{"max_radius": 15}' '{"max_radius": 20}' '{"max_radius": 25}' '{"max_radius": 50}' '{"max_radius": 75}' '{"max_radius": 100}' '{"max_radius": 150}' '{"max_radius": 200}' '{"max_radius": 300}' '{"max_radius": 400}' '{"max_radius": 500}' \
#  --metrics comparison_similarity_score \
#  --embedding-type BINARY # FULL REDUCED_SCALED UMAP
 #--umap-config '{"n_neighbors": 5}' '{"n_neighbors": 15}' '{"n_neighbors": 25}' '{"n_neighbors": 5, "n_components": 128}' '{"n_neighbors": 15, "n_components": 128}' '{"n_neighbors": 25, "n_components": 128}' \
 #--pca-config '{"n_components": 2}' '{"n_components": 4}' '{"n_components": 8}' '{"n_components": 32}' '{"n_components": 64}' '{"n_components": 128}' '{"n_components": 256}' '{"n_components": 512}' \


# fut-20240824-idqjyg4cbx fut-20240826-pfu8a8oqvg fut-20240824-iqensc5m11 fut-20240818-nvtom75jew \
#  --umap-dimension 2 64 128 512 \
#  --model-id fut-20240717-bc4wkshxb6 fut-20240717-3hg22i7c1v fut-20240722-6icpw0okt2 fut-20240722-ypzkqpgakz fut-20240726-o52hch8ph0 \ '{"max_radius": 0.003}' '{"max_radius": 0.01}' '{"max_radius": 0.03}' '{"max_radius": 0.1}' '{"max_radius": 0.3}'

python -m deeplearning.embeddings.scripts.calculate_embedding_scores \
 --model-id fut-20241017-utl58frb63 \
 --output-path /data/deeplearning/test-comp-sil-b.json \
 --comparison-silhouette-score-config '{"max_radius": 0.03}' '{"max_radius": 0.1}' '{"max_radius": 0.5}' '{"max_radius": 1}' '{"max_radius": 3}' '{"max_radius": 5}' '{"max_radius": 10}' '{"max_radius": 15}' '{"max_radius": 20}' '{"max_radius": 25}' '{"max_radius": 30}' '{"max_radius": 35}' '{"max_radius": 40}' '{"max_radius": 45}' '{"max_radius": 50}' '{"max_radius": 60}' '{"max_radius": 75}' '{"max_radius": 100}' '{"max_radius": 150}' '{"max_radius": 200}' '{"max_radius": 300}' '{"max_radius": 500}' '{"max_radius": 750}' '{"max_radius": 1000}'  \
 --metrics comparison_silhouette_score \
 --embedding-type FULL REDUCED_SCALED # FULL REDUCED_SCALED UMAP



# fut-20240823-2fjmqq8vrp fut-20240821-f58o06srxd

# python -m deeplearning.embeddings.scripts.calculate_embedding_scores \
#  --model-id  fut-20240818-sfdx3jpxoq fut-20240814-2axzr8kxwp fut-20240717-bc4wkshxb6 \
#  --output-path /data/deeplearning/comparing-crops.json \
#  --comparison-similarity-score-config '{"max_radius": 0.003}' '{"max_radius": 0.01}' '{"max_radius": 0.03}' '{"max_radius": 0.1}' '{"max_radius": 0.3}' '{"max_radius": 1}' '{"max_radius": 3}' '{"max_radius": 5}' '{"max_radius": 10}' '{"max_radius": 20}' '{"max_radius": 30}' '{"max_radius": 40}' '{"max_radius": 50}' '{"max_radius": 75}' '{"max_radius": 100}' \
#  --metrics comparison_similarity_score \
#  --embedding-type FULL REDUCED_SCALED


# QUICK TEST
# python -m deeplearning.embeddings.scripts.calculate_embedding_scores \
#  --model-id fut-20240717-bc4wkshxb6 \
#  --output-path /data/deeplearning/out-test-sweep.json \
#  --comparison-similarity-score-config '{"max_radius": 5}' \
#  --metrics comparison_similarity_score \
#  --umap-dimension 2 4 \
#  --embedding-type UMAP REDUCED_SCALED FULL

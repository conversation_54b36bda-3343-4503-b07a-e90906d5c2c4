import torch

from deeplearning.embeddings.metrics.comparison_similarity_score import ComparisonSimilarityScore
from deeplearning.embeddings.metrics.neighborhood import Neighborhood
from deeplearning.embeddings.metrics.score import Score


class ComparisonSilhouetteScore(Score):
    """
    Calculates the silhouette comparison score for some central point
    Neighborhood contains a central point
    Non_Neighborhood is another neighborhood (in best practice, should be nearest neighborhood)
    CS_i is average in-neighborhood comparison score
    CS_b is average between-neighborhood comparison score

    comparison_silhouette_score = (CS_i - CS_b) / (max(CS_i, CS_b))

    comparison_silhouette_score = 1 if CS_i >> CS_b
    comparison_silhouette_score = 0 if CS_i == CS_b
    comparison_silhouette_score = -1 if CS_i << CS_b
    """

    def __init__(self, neighborhood: Neighborhood, non_neighborhood: Neighborhood):
        super().__init__(neighborhood, non_neighborhood)

    def calculate(self) -> torch.Tensor:
        avg_in_neighborhood_sim = ComparisonSimilarityScore(self._neighborhood).calculate()
        assert self._non_neighborhood is not None
        avg_between_neighborhood_sim = ComparisonSimilarityScore(self._non_neighborhood).calculate()

        return (avg_in_neighborhood_sim - avg_between_neighborhood_sim) / (
            torch.max(avg_in_neighborhood_sim, avg_between_neighborhood_sim)
        )

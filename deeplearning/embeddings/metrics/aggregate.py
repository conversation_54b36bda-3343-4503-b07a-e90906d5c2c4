from typing import Dict, List, Optional, Type

import torch

from deeplearning.embeddings.metrics.correlation_score import CorrelationScore
from deeplearning.embeddings.metrics.neighborhood import Neighborhood
from deeplearning.embeddings.metrics.score import Score


class MeanNeighborhoodAverage:
    def __init__(
        self,
        neighborhoods: List[Neighborhood],
        score_obj: Type[Score] = CorrelationScore,
        non_neighborhoods: Optional[List[Neighborhood]] = None,
    ):
        self._neighborhoods = neighborhoods
        self._score_obj = score_obj
        self._non_neighborhoods = non_neighborhoods

    def calculate(self) -> Optional[Dict[str, torch.Tensor]]:
        score_list = [
            self._score_obj(
                neighborhood, self._non_neighborhoods[ind] if self._non_neighborhoods is not None else None
            ).calculate()
            for ind, neighborhood in enumerate(self._neighborhoods)
        ]
        scores = torch.tensor([score for score in score_list if not torch.isnan(score)])

        if scores.numel() == 0:
            return None

        return {
            "mean": scores.mean(),
            "std": scores.std(),
            "max": scores.max(),
            "min": scores.min(),
            "median": scores.median(),
        }

    def len_dist(self) -> Optional[Dict[str, float]]:
        len_list = [float(len(self._score_obj(neighborhood, None))) for neighborhood in self._neighborhoods]
        lens = torch.tensor(len_list)

        return {
            "mean_len": lens.mean().item(),
            "std_len": lens.std().item(),
            "max_len": lens.max().item(),
            "min_len": lens.min().item(),
            "median_len": lens.median().item(),
        }

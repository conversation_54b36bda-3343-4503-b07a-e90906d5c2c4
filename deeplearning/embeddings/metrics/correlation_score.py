from typing import Optional

import torch
from scipy.stats import pearsonr

from deeplearning.embeddings.metrics.neighborhood import Neighborhood
from deeplearning.embeddings.metrics.score import Score


class CorrelationScore(Score):
    def __init__(self, neighborhood: Neighborhood, non_neighborhood: Optional[Neighborhood] = None):
        super().__init__(neighborhood, non_neighborhood)

    def calculate(self) -> torch.Tensor:
        central_pumap = self._neighborhood.central_point.pumap
        central_comparison = self._neighborhood.central_point.comparison

        pumap_distances = [
            torch.nn.functional.pairwise_distance(central_pumap, neighbor.pumap).item()
            for neighbor in self._neighborhood.neighbors
        ]
        comparison_similarity = [
            torch.nn.functional.cosine_similarity(central_comparison, neighbor.comparison).item()
            for neighbor in self._neighborhood.neighbors
        ]

        return torch.Tensor([pearsonr(pumap_distances, comparison_similarity).statistic])

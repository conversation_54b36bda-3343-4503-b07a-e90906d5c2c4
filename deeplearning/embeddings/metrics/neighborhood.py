from typing import Any, Dict, List, Optional

import torch


class PumapComparisonPoint:
    def __init__(
        self, pumap: torch.Tensor, comparison: torch.Tensor, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        assert len(pumap.shape) == 2
        assert len(comparison.shape) == 2
        self.pumap = pumap
        self.comparison = comparison
        self.metadata = metadata if metadata is not None else {}


class Neighborhood:
    def __init__(self, central_point: PumapComparisonPoint, neighbors: List[PumapComparisonPoint]):
        self.central_point = central_point
        self.neighbors = neighbors

    def __len__(self) -> int:
        return len(self.neighbors)

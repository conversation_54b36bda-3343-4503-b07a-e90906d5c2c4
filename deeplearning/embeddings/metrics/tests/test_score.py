import math
from typing import List, Optional, Type

import torch

from deeplearning.embeddings.metrics.comparison_silhouette_score import ComparisonSilhouetteScore
from deeplearning.embeddings.metrics.comparison_similarity_score import ComparisonSimilarityScore
from deeplearning.embeddings.metrics.correlation_score import CorrelationScore
from deeplearning.embeddings.metrics.neighborhood import Neighborhood, PumapComparisonPoint
from deeplearning.embeddings.metrics.score import Score

EPS = 1e-6


softplus = torch.nn.Softplus(beta=20, threshold=2)


def build_score(
    central_point: PumapComparisonPoint,
    neighborhood_points: List[PumapComparisonPoint],
    score_class: Type[Score],
    non_neighborhood_points: Optional[List[PumapComparisonPoint]] = None,
) -> Score:
    neighborhood = Neighborhood(central_point=central_point, neighbors=neighborhood_points)
    non_neighborhood: Optional[Neighborhood] = None
    if non_neighborhood_points is not None:
        non_neighborhood = Neighborhood(central_point=central_point, neighbors=non_neighborhood_points)
    return score_class(neighborhood, non_neighborhood)


def test_comparison_similarity_score_same_pumap_similar_comparison() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[2.0, 0.0]]),),
    ]

    comparison_similarity_score = build_score(central_point, neighbor_points, ComparisonSimilarityScore)

    assert abs(comparison_similarity_score.calculate() - softplus(torch.tensor([1.0]))) < EPS


def test_comparison_similarity_score_divergent_same_pumap_different_comparison() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 1.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[0.0, 1.0]]),),
    ]

    comparison_similarity_score = build_score(central_point, neighbor_points, ComparisonSimilarityScore)
    expected = torch.mean(softplus(torch.cos(torch.Tensor([math.pi / 4, math.pi / 2]))))

    assert abs(comparison_similarity_score.calculate() - expected) < EPS


def test_comparison_similarity_score_divergent_different_pumap_different_comparison() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[1.0, 0.5]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 1.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.2, 0.5]]), torch.Tensor([[0.0, 1.0]]),),
    ]

    comparison_similarity_score = build_score(central_point, neighbor_points, ComparisonSimilarityScore)
    expected = torch.mean(softplus(torch.cos(torch.Tensor([math.pi / 4, math.pi / 2]))))

    assert abs(comparison_similarity_score.calculate() - expected) < EPS


def test_correlation_score_perfect_negative_correlation() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[0.0, 0.0]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(  # maps to (1, 0) in (pumap distance, comparison similarity) space
            torch.Tensor([[1.0, 0.0]]), torch.Tensor([[0.0, 3.0]]),
        ),
        PumapComparisonPoint(  # maps to (0.5, 0.5) in (pumap distance, comparison similarity) space
            torch.Tensor([[0.0, 0.5]]), torch.Tensor([[1.0, 1.73]]),
        ),
        PumapComparisonPoint(
            torch.Tensor([[0, 0]]),  # maps to (0, 1) in (pumap distance, comparison similarity) space
            torch.Tensor([[3.0, 0.0]]),
        ),
    ]

    comparison_similarity_score = build_score(central_point, neighbor_points, CorrelationScore)
    assert abs(comparison_similarity_score.calculate() - -1.0) < EPS


def test_comparison_silhouette_score_should_be_1() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[2.0, 0.0]]),),
    ]
    non_neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[-0.5, -0.5]]), torch.Tensor([[-1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[-0.5, -0.5]]), torch.Tensor([[-2.0, 0.0]]),),
    ]

    comparison_silhouette_score = build_score(
        central_point, neighbor_points, ComparisonSilhouetteScore, non_neighbor_points
    )

    assert abs(comparison_silhouette_score.calculate() - torch.tensor([1.0])) < EPS


def test_comparison_silhouette_score_should_be_0() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[2.0, 0.0]]),),
    ]
    non_neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[2.0, 0.0]]),),
    ]

    comparison_silhouette_score = build_score(
        central_point, neighbor_points, ComparisonSilhouetteScore, non_neighbor_points
    )

    assert abs(comparison_silhouette_score.calculate() - torch.tensor([0.0])) < EPS


def test_comparison_silhouette_score_should_be_neg_1() -> None:
    central_point = PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[3.0, 0.0]]))
    neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[-0.5, -0.5]]), torch.Tensor([[-1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[-0.5, -0.5]]), torch.Tensor([[-2.0, 0.0]]),),
    ]
    non_neighbor_points = [
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[1.0, 0.0]]),),
        PumapComparisonPoint(torch.Tensor([[0.5, 0.5]]), torch.Tensor([[2.0, 0.0]]),),
    ]

    comparison_silhouette_score = build_score(
        central_point, neighbor_points, ComparisonSilhouetteScore, non_neighbor_points
    )

    assert abs(comparison_silhouette_score.calculate() - torch.tensor([-1.0])) < EPS

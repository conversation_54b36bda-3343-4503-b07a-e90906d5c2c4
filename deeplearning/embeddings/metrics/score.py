import abc
from typing import Optional

import torch

from deeplearning.embeddings.metrics.neighborhood import Neighborhood


class Score(abc.ABC):
    def __init__(self, neighborhood: Neighborhood, non_neighborhood: Optional[Neighborhood] = None) -> None:
        self._neighborhood = neighborhood
        self._non_neighborhood = non_neighborhood

        if self._non_neighborhood is not None:
            assert (
                self._non_neighborhood.central_point == self._neighborhood.central_point
            ), f"Central points must match if passing a non-neighborhood: neighborhood ({self._neighborhood.central_point}), non_neighborhood ({self._non_neighborhood.central_point})"

    @abc.abstractmethod
    def calculate(self) -> torch.Tensor:
        pass

    def __len__(self) -> int:
        return len(self._neighborhood)

from typing import Optional

import torch

from deeplearning.embeddings.metrics.neighborhood import Neighborhood
from deeplearning.embeddings.metrics.score import Score


class ComparisonSimilarityScore(Score):
    def __init__(self, neighborhood: Neighborhood, non_neighborhood: Optional[Neighborhood] = None):
        super().__init__(neighborhood, non_neighborhood)

    def calculate(self) -> torch.Tensor:
        central_comparison = self._neighborhood.central_point.comparison
        softplus = torch.nn.Softplus(beta=20, threshold=2)
        comparison_similarity = [
            softplus(torch.nn.functional.cosine_similarity(central_comparison, neighbor.comparison))
            for neighbor in self._neighborhood.neighbors
        ]

        return torch.mean(torch.Tensor(comparison_similarity))

import json
import os
from typing import Any, Dict, Iterator, List, Optional, Union, cast

import h5py
import numpy as np
import numpy.typing as npt
import pydantic
import torch


class EmbeddingDatasetMetadata(pydantic.BaseModel):
    model_id: str
    embedding_size: int
    dataset_id: str
    embedding_precision: str = "float32"

    model_config = pydantic.ConfigDict(protected_namespaces=())
    version_id: Optional[str] = None


class EmbeddingDatapointPrediction(pydantic.BaseModel):
    plant_score: float
    weed_score: float
    crop_score: float
    category_scores: Dict[str, float]
    distance_mm: float


class EmbeddingDatapointMetadata(pydantic.BaseModel):
    captured_at: int
    geohash: str
    image_id: str
    image_url: Optional[str] = None
    point_category_id: str
    label_id: Optional[str] = None
    detection_id: Optional[str] = None
    point_id: Optional[str] = None
    image_crop_id: str
    x: float
    y: float
    radius: Optional[float] = 0.0


class EmbeddingDatapoint:
    def __init__(
        self,
        embedding: torch.Tensor,
        metadata: EmbeddingDatapointMetadata,
        predictions_metadata: Optional[List[EmbeddingDatapointPrediction]] = None,
        predictions_embeddings: Optional[List[torch.Tensor]] = None,
    ):
        predictions_metadata = predictions_metadata if predictions_metadata is not None else []
        predictions_embeddings = predictions_embeddings if predictions_embeddings is not None else []
        self.embedding = embedding
        self.metadata = metadata
        self.predictions_metadata = predictions_metadata
        self.predictions_embeddings = predictions_embeddings

    def to(self, device: torch.device) -> "EmbeddingDatapoint":
        self.embedding = self.embedding.to(device)
        return self


class EmbeddingDataset:
    def __init__(self, dataset_filepath: str, metadata: Optional[EmbeddingDatasetMetadata] = None):
        self.dataset_filepath = dataset_filepath
        self._metadata = metadata

        if not os.path.exists(self.dataset_filepath):
            if self._metadata is None:
                raise RuntimeError(
                    "Embedding filepath does not exist and no metadata was provided to initialize the dataset."
                )

            with h5py.File(self.dataset_filepath, "w", libver="latest") as f:
                f.create_dataset(
                    "embeddings",
                    shape=(0, self._metadata.embedding_size),
                    maxshape=(None, None),
                    dtype=self._metadata.embedding_precision,
                )
                f.create_dataset(
                    "embeddings_metadata", shape=(0, 1), maxshape=(None, 1), dtype=h5py.special_dtype(vlen=str)
                )
                f.create_dataset(
                    "predictions_embeddings",
                    shape=(0, self._metadata.embedding_size),
                    maxshape=(None, None),
                    dtype=self._metadata.embedding_precision,
                )
                f.create_dataset(
                    "predictions_metadata", shape=(0, 1), maxshape=(None, None), dtype=h5py.special_dtype(vlen=str)
                )
                f.create_dataset(
                    "index_to_predictions", shape=(0, 1), maxshape=(None, 1), dtype=h5py.special_dtype(vlen=str)
                )
                f.attrs["metadata"] = json.dumps(self._metadata.model_dump())

    def __getitem__(self, index: int) -> EmbeddingDatapoint:

        with h5py.File(self.dataset_filepath, "r", libver="latest") as f:
            embedding = torch.from_numpy(f["embeddings"][index])
            metadata = EmbeddingDatapointMetadata(**json.loads(f["embeddings_metadata"][index][0]))
            predictions_metadata = []
            predictions_embeddings = []
            if "index_to_predictions" in f:
                prediction_indices = json.loads(f["index_to_predictions"][index][0])
                for prediction_index in prediction_indices:
                    predictions_metadata.append(
                        EmbeddingDatapointPrediction(**json.loads(f["predictions_metadata"][prediction_index][0]))
                    )
                    predictions_embeddings.append(torch.from_numpy(f["predictions_embeddings"][prediction_index]))

        datapoint = EmbeddingDatapoint(
            embedding=embedding,
            metadata=metadata,
            predictions_metadata=predictions_metadata,
            predictions_embeddings=predictions_embeddings,
        )

        return datapoint

    def __iter__(self) -> Iterator[EmbeddingDatapoint]:
        # Use a generator to yield elements using __getitem__
        for index in range(len(self)):
            yield self[index]

    def __len__(self) -> int:
        with h5py.File(self.dataset_filepath, "r", libver="latest") as f:
            length: int = f["embeddings"].shape[0]

        return length

    def get_metadata(self, index: int) -> EmbeddingDatapointMetadata:
        with h5py.File(self.dataset_filepath, "r", libver="latest") as f:
            return EmbeddingDatapointMetadata(**json.loads(f["embeddings_metadata"][index][0]))

    def append(self, data: Union[EmbeddingDatapoint, List[EmbeddingDatapoint]]) -> None:
        if isinstance(data, EmbeddingDatapoint):
            data = [data]

        with h5py.File(self.dataset_filepath, "a", libver="latest") as f:
            embeddings = f["embeddings"]
            metadata = f["embeddings_metadata"]
            predictions_embeddings = f["predictions_embeddings"]
            predictions_metadata = f["predictions_metadata"]
            index_to_predictions = f["index_to_predictions"]

            current_embeddings_num_datapoints = embeddings.shape[0]
            current_metadata_num_datapoints = metadata.shape[0]
            current_predictions_embeddings_num_datapoints = predictions_embeddings.shape[0]
            current_predictions_metadata_num_datapoints = predictions_metadata.shape[0]

            assert current_embeddings_num_datapoints == current_metadata_num_datapoints
            assert current_predictions_embeddings_num_datapoints == current_predictions_metadata_num_datapoints

            current_num_datapoints = current_embeddings_num_datapoints
            current_prediction_datapoints = current_predictions_embeddings_num_datapoints

            pred_embeddings_to_add = []
            pred_metadata_to_add = []
            embedding_to_add = []
            metadata_to_add = []
            index_to_prediction_to_add = []
            curr_prediction_datapoint_ptr = current_prediction_datapoints
            for datapoint in data:
                new_index_to_predictions = []

                for j in range(len(datapoint.predictions_embeddings)):
                    pred_embeddings_to_add.append(datapoint.predictions_embeddings[j])
                    pred_metadata_to_add.append(json.dumps(datapoint.predictions_metadata[j].model_dump()))
                    new_index_to_predictions.append(curr_prediction_datapoint_ptr)

                    curr_prediction_datapoint_ptr += 1

                embedding_to_add.append(datapoint.embedding)
                metadata_to_add.append(json.dumps(datapoint.metadata.model_dump()))
                index_to_prediction_to_add.append(json.dumps(new_index_to_predictions))

            embeddings.resize(current_num_datapoints + len(data), axis=0)
            metadata.resize(current_num_datapoints + len(data), axis=0)
            index_to_predictions.resize(current_num_datapoints + len(data), axis=0)
            embeddings[current_num_datapoints : current_num_datapoints + len(embedding_to_add)] = torch.stack(
                embedding_to_add
            )
            metadata[current_num_datapoints : current_num_datapoints + len(metadata_to_add)] = np.expand_dims(
                np.array(metadata_to_add), axis=-1
            )
            index_to_predictions[
                current_num_datapoints : current_num_datapoints + len(index_to_prediction_to_add)
            ] = np.expand_dims(np.array(index_to_prediction_to_add), axis=-1)

            if len(pred_embeddings_to_add):
                predictions_embeddings.resize(current_prediction_datapoints + len(pred_embeddings_to_add), axis=0)
                predictions_metadata.resize(current_prediction_datapoints + len(pred_metadata_to_add), axis=0)

                predictions_embeddings[
                    current_prediction_datapoints : current_prediction_datapoints + len(pred_embeddings_to_add)
                ] = torch.stack(pred_embeddings_to_add)
                predictions_metadata[
                    current_prediction_datapoints : current_prediction_datapoints + len(pred_metadata_to_add)
                ] = np.expand_dims(np.array(pred_metadata_to_add), axis=-1)

    @property
    def metadata(self) -> EmbeddingDatasetMetadata:
        if self._metadata is None:
            with h5py.File(self.dataset_filepath, "r", libver="latest") as f:
                self._metadata = EmbeddingDatasetMetadata(**json.loads(f.attrs["metadata"]))
        return self._metadata

    @metadata.setter
    def metadata(self, metadata: EmbeddingDatasetMetadata) -> None:
        self._metadata = metadata

        with h5py.File(self.dataset_filepath, "w", libver="latest") as f:
            f.attrs["metadata"] = json.dumps(metadata.model_dump())


class EmbeddingLookupTable:
    def __init__(self, filepath: str, embedding_type: Optional[str] = None):
        self._filepath = filepath
        if not os.path.exists(self._filepath):
            with h5py.File(self._filepath, "w", libver="latest") as f:
                f.attrs["meta_embedding_type"] = embedding_type
                f.attrs["meta_group_id_len"] = 2

    @property
    def embedding_type(self) -> Optional[str]:
        with h5py.File(self._filepath, "r", libver="latest") as f:
            if "meta_embedding_type" not in f.attrs:
                return None
            return cast(Optional[str], f.attrs["meta_embedding_type"])

    def get(self, uuid: str) -> Optional[torch.Tensor]:
        embedding: Optional[torch.Tensor] = None
        with h5py.File(self._filepath, "r", libver="latest") as f:
            group_id = f'group_{uuid[:f.attrs["meta_group_id_len"]]}'
            if group_id in f and uuid in f[group_id]:
                embedding = torch.from_numpy(f[group_id][uuid][:])
        return embedding

    def _convert_to_numpy(self, embedding: Union[torch.Tensor, npt.NDArray[Any]]) -> npt.NDArray[Any]:
        if isinstance(embedding, torch.Tensor):
            return cast(npt.NDArray[Any], embedding.numpy())
        return cast(npt.NDArray[Any], embedding)

    def set(self, uuid: str, embedding: Union[torch.Tensor, npt.NDArray[Any]]) -> None:
        with h5py.File(self._filepath, "a", libver="latest") as f:
            group_id = f'group_{uuid[:f.attrs["meta_group_id_len"]]}'
            if group_id not in f:
                f.create_group(group_id)

            if uuid in f[group_id]:
                f[group_id][uuid][:] = self._convert_to_numpy(embedding)
            else:
                f[group_id].create_dataset(uuid, data=self._convert_to_numpy(embedding))

    def list(self) -> List[str]:
        with h5py.File(self._filepath, "r", libver="latest") as f:
            group_names = [name for name, item in f.items() if isinstance(item, h5py.Group)]
            datasets = []
            for group in group_names:
                datasets.extend(list(f[group].keys()))
            return datasets


class EmbeddingLookupTables:
    def __init__(self, embedding_lookup_table_paths: List[str]):
        self._uuid_to_path_ind = {}
        self._paths = embedding_lookup_table_paths
        for i, path in enumerate(self._paths):
            emb_lookup_table = EmbeddingLookupTable(path)
            for key in emb_lookup_table.list():
                self._uuid_to_path_ind[key] = i

    def get(self, uuid: str) -> Optional[torch.Tensor]:
        if uuid not in self._uuid_to_path_ind:
            return None
        path = self._paths[self._uuid_to_path_ind[uuid]]
        return EmbeddingLookupTable(path).get(uuid)

    def save(self, filepath: str) -> None:
        with h5py.File(filepath, "w", libver="latest") as f:
            f.attrs["embedding_lookup_filepaths"] = json.dumps(self._paths)

    @staticmethod
    def load(filepath: str) -> "EmbeddingLookupTables":
        with h5py.File(filepath, "r", libver="latest") as f:
            return EmbeddingLookupTables(json.loads(f.attrs["embedding_lookup_filepaths"]))

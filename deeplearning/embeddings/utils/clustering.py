import json
import os
from typing import Any, Dict

from deeplearning.constants import CARBON_DATA_DIR


def get_clustering_directory() -> str:
    return os.path.join(CARBON_DATA_DIR, "deeplearning/clustering")


def get_progress_filepath(model_id: str) -> str:
    return os.path.join(get_clustering_directory(), f"{model_id}.json")


def get_progress(model_id_filepath: str) -> Dict[str, Any]:
    if not os.path.exists(model_id_filepath):
        print(f"File {model_id_filepath} does not exist")
        return {}
    with open(model_id_filepath) as f:
        progress: Dict[str, Any] = json.load(f)

    return progress


def save_progress(model_id: str, progress: Dict[str, Any]) -> None:
    progress_filepath = get_progress_filepath(model_id)
    save_file(progress_filepath, progress)


def save_file(output_filepath: str, item: Dict[str, Any]) -> None:
    os.makedirs(os.path.dirname(output_filepath), exist_ok=True)

    with open(output_filepath, "w") as f:
        json.dump(item, f)

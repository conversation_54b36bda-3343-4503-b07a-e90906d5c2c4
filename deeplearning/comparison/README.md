# Comparison Model

The model supported here will be trained on pairs of plants that are labeled either similar or different. This model will predict the distance between any two pairs of plants.

## Sample Runs

`./deeplearning/bin/run_docker.sh torchrun --nproc-per-node=8 -m deeplearning.scripts.standard_jobs.comparison --initial-lr 0.01 --pretrained-model prt-20230602-vadfiyn3kq`

`./deeplearning/bin/run_docker.sh htorchrun --nproc-per-node=4 -m deeplearning.scripts.standard_jobs.comparison --initial-lr 0.01 --pretrained-model prt-20230602-vadfiyn3kq --model CosineComparisonModel --train-batch-size 6 --val-batch-size 3`
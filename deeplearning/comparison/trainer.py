import functools
import logging
import os
import random
import time
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type, Union, cast

import cv2
import numpy as np
import numpy.typing as npt
import torch
import wandb
from lightning.pytorch.loggers import <PERSON>d<PERSON><PERSON><PERSON>ger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session
from sqlalchemy.orm.session import sessionmaker
from tabulate import tabulate
from torch.utils.data import DataLoader

import deeplearning.server.trt_runtime as rt
from deeplearning.comparison.config import ComparisonConfig
from deeplearning.comparison.constants import SIZE_BUCKETS
from deeplearning.comparison.data_utils import (  # noqa
    ComparisonEmbeddings,
    embeddings_to_dataframes,
    get_embeddings_engine,
    get_predictions_and_labels_engine,
    px2mm,
    save_comparison_embeddings,
    send_embeddings_to_db,
    send_predictions_to_db,
)
from deeplearning.comparison.datasets import (
    ComparisonDatapoint,
    ComparisonDataset,
    ComparisonDatasets,
    DatasetType,
    transform_batch,
)
from deeplearning.comparison.metrics import AverageMetrics, Metrics
from deeplearning.comparison.models.learnable_reduction_densenet_201_cosine_comparison_model import (
    LearnableReductionDensenet201CosineComparisonModel,
)
from deeplearning.comparison.models.utils import (
    ComparisonEmbeddingOutputFactory,
    ComparisonModelBase,
    ComparisonModelOutput,
    ComparisonModelOutputFactory,
)
from deeplearning.comparison.sampler import (
    ComparisonLabelSampler,
    ComparisonLabelSamplerBasic,
    ComparisonLabelSamplerSource,
)
from deeplearning.comparison.trt_convert import MAX_BATCH_SIZE, TrtConvert
from deeplearning.comparison.version import get_version
from deeplearning.constants import CROP_ID_TO_LABEL, S3_BUCKET
from deeplearning.dl_metrics.comparison_metrics import comparison_db
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.tensorrt import load_tensorrt_model
from deeplearning.utils.debug_signal import DebugSignal
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS, denormalize_image
from deeplearning.utils.tensor import gather_objects
from deeplearning.utils.trainer import Environment, Trainer, TrainingModule, compute_md5sum, worker_init_fn
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import get_wandb_metadata_json_str
from lib.common.collections.list import flatten

Base = declarative_base()

LOG = logging.getLogger(__name__)

logging.basicConfig(format="%(asctime)s %(levelname)s:%(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S")


class LrAdjuster(Enum):
    MultiStep = 0
    CosineAnnealing = 1


class ComparisonTrainingModule(TrainingModule):
    def __init__(
        self,
        config: ComparisonConfig,
        datasets: ComparisonDatasets,
        image_id_to_url: Optional[Dict[str, str]],
        initial_lr: float,
        lr_milestones: Optional[List[int]],
        lr_gamma: float,
        epochs: Optional[int],
        train_batch_size: int,
        val_batch_size: int,
        test_batch_size: int,
        data_dir: str,
        model_id: str,
        embedding_output_path: str,
        comparison_db_output_path: str,
        dataset_info_dict: Optional[Dict[str, Any]] = None,
        data_pipeline_processes: int = 4,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        pretrained_model: Optional[str] = None,
        additional_wandb_config: Optional[Dict[str, Any]] = None,
        model: Type[ComparisonModelBase] = LearnableReductionDensenet201CosineComparisonModel,
        loss_fn: Callable[[torch.Tensor, torch.Tensor], torch.Tensor] = torch.nn.functional.binary_cross_entropy,
        experiment_dir: Optional[str] = None,
        save_embeddings_in_db: bool = False,
        log_experiment: bool = True,
        lr_adjuster: LrAdjuster = LrAdjuster.MultiStep,
        lr_eta_min: float = 0.0,
        lr_t_max: int = 40,
        threshold: float = 0.5,
        save_image_point_embeddings: bool = False,
        save_hdf5_embeddings: bool = False,
        predictions_and_labels_path: Optional[str] = None,
    ):
        super().__init__()
        dataset_info_dict = dataset_info_dict if dataset_info_dict is not None else {}
        additional_wandb_config = additional_wandb_config if additional_wandb_config is not None else {}
        self._datasets = datasets
        self._image_id_to_url = image_id_to_url
        self._lr_adjuster = lr_adjuster
        self._initial_lr = initial_lr
        self._lr_milestones = lr_milestones
        self._lr_gamma = lr_gamma
        self._lr_t_max = lr_t_max
        self._lr_eta_min = lr_eta_min
        self._epochs = epochs
        self._train_batch_size = train_batch_size
        self._val_batch_size = val_batch_size
        self._data_pipeline_processes = data_pipeline_processes
        self._train_log_image_p = train_log_image_p
        self._val_log_image_p = val_log_image_p
        self._experiment_dir = experiment_dir
        self._save_embeddings_in_db = save_embeddings_in_db
        self._log_experiment = log_experiment
        self._dataset_info_dict = dataset_info_dict
        self._model_id = model_id
        self._config = config
        self._threshold = threshold
        self._test_batch_size = test_batch_size

        self._data_dir = data_dir
        os.makedirs(self._data_dir, exist_ok=True)

        self._output_dir = self._experiment_dir if self._experiment_dir is not None else self._data_dir

        self._embedding_db_path = os.path.join(self._output_dir, "comparison_embeddings.db")

        self._predictions_and_labels_session = None
        if predictions_and_labels_path is not None:
            os.makedirs(os.path.dirname(predictions_and_labels_path), exist_ok=True)
            predictions_and_labels_engine = get_predictions_and_labels_engine(
                predictions_and_labels_path, generate_table=torch.distributed.get_rank() == 0
            )

            torch.distributed.barrier()
            self._predictions_and_labels_session = sessionmaker()
            self._predictions_and_labels_session.configure(bind=predictions_and_labels_engine)

        if torch.distributed.get_rank() == 0 and os.path.exists(self._embedding_db_path):
            os.remove(self._embedding_db_path)
        self._engine = get_embeddings_engine(self._embedding_db_path, generate_table=torch.distributed.get_rank() == 0)
        torch.distributed.barrier()  # Wait for rank 0 to create tables

        self._session = sessionmaker()
        self._session.configure(bind=self._engine)

        self._debug_signal = DebugSignal()
        self._experiment_url: Optional[str] = None
        self._additional_wandb_config = additional_wandb_config

        self._validation_outputs: List[Metrics] = []
        self._test_outputs: List[Metrics] = []
        self._training_outputs: List[Metrics] = []

        self._model = model(pretrained_model=pretrained_model)
        self._match_is_above_threshold: bool = self._model.match_is_above_threshold
        if torch.distributed.get_rank() == 0:
            logging.info(f"match is above thresh {self._match_is_above_threshold}")
            logging.info(
                f"Datasets: train={len(self._datasets.get_training())} examples, val={len(self._datasets.get_validation())} examples, test={len(self._datasets.get_test())} examples"
            )
        self._loss_fn = loss_fn

        self._trt_max_batch_size = self._test_batch_size

        self.trt_model: Optional[Any] = None
        self._embeddings_set: Set[Tuple[str, ...]] = set()

        self._embedding_output_path = embedding_output_path
        os.makedirs(self._embedding_output_path, exist_ok=True)

        self._save_image_point_embeddings = save_image_point_embeddings
        self._save_hdf5_embeddings = save_hdf5_embeddings

        # DB is stored under the test_prefix directory currently.
        self._comparison_db_path = os.path.join(comparison_db_output_path, f"comparison_{comparison_db.__version__}.db")
        os.makedirs(os.path.dirname(self._comparison_db_path), exist_ok=True)
        self._comparison_db_engine: Optional[Session] = comparison_db.get_db(db_path=self._comparison_db_path)

    @property
    def use_cases(self) -> List[ModelUseCase]:
        return [ModelUseCase.COMPARISON]

    @property
    def config(self) -> ComparisonConfig:
        return self._config

    def forward(
        self,
        image_one: torch.Tensor,
        image_two: torch.Tensor,
        recovery_info_one: List[Dict[str, Any]],
        recovery_info_two: List[Dict[str, Any]],
        epoch: str,
    ) -> Tuple[ComparisonModelOutput, List[ComparisonEmbeddings]]:
        if self.trt_model is not None:
            reduction_one = ComparisonEmbeddingOutputFactory.unpack(self.trt_model(image_one)).out
            reduction_two = ComparisonEmbeddingOutputFactory.unpack(self.trt_model(image_two)).out
        else:
            reduction_one = ComparisonEmbeddingOutputFactory.unpack(self._model(image_one)).out
            reduction_two = ComparisonEmbeddingOutputFactory.unpack(self._model(image_two)).out

        embeddings = []

        for reduction_item, recovery_info in [(reduction_one, recovery_info_one), (reduction_two, recovery_info_two)]:
            for n in range(reduction_item.shape[0]):
                emb = flatten(reduction_item[n].tolist())
                embeddings.append(
                    ComparisonEmbeddings(
                        image_id=recovery_info[n]["id"],
                        x=recovery_info[n]["x"],
                        y=recovery_info[n]["y"],
                        radius=recovery_info[n]["radius"],
                        category=recovery_info[n]["category"],
                        label_id=recovery_info[n]["label_id"],
                        embedding=emb,
                        epoch=epoch,
                        geohash=recovery_info[n]["geohash"],
                        uuid=recovery_info[n]["uuid"],
                    )
                )
        return (
            ComparisonModelOutputFactory.unpack(self._model.forward_similarity(reduction_one, reduction_two)),
            embeddings,
        )

    def get_worker_init_fn(self) -> Callable[[int], None]:
        num_workers = self._data_pipeline_processes
        return functools.partial(worker_init_fn, num_workers)

    def train_dataloader(self) -> "DataLoader[ComparisonDatapoint]":
        sampler: torch.utils.data.Sampler[ComparisonDatapoint] = torch.utils.data.distributed.DistributedSampler(
            self._datasets.get_training(),
            num_replicas=torch.distributed.get_world_size(),
            rank=torch.distributed.get_rank(),
            shuffle=True,
        )
        return DataLoader(
            self._datasets.get_training(),
            sampler=sampler,
            batch_size=self._train_batch_size,
            num_workers=self._data_pipeline_processes,
            collate_fn=self.collate_fn_datapoints,
            pin_memory=False,
            drop_last=True,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def val_dataloader(self) -> "DataLoader[ComparisonDatapoint]":
        sampler: torch.utils.data.Sampler[ComparisonDatapoint] = torch.utils.data.distributed.DistributedSampler(
            self._datasets.get_validation(),
            num_replicas=torch.distributed.get_world_size(),
            rank=torch.distributed.get_rank(),
            shuffle=False,
        )
        return DataLoader(
            self._datasets.get_validation(),
            sampler=sampler,
            batch_size=self._val_batch_size,
            num_workers=self._data_pipeline_processes,
            collate_fn=self.collate_fn_datapoints,
            pin_memory=False,
            drop_last=True,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def test_dataloader(self) -> "DataLoader[ComparisonDatapoint]":
        sampler: torch.utils.data.Sampler[ComparisonDatapoint] = torch.utils.data.distributed.DistributedSampler(
            self._datasets.get_test(),
            num_replicas=torch.distributed.get_world_size(),
            rank=torch.distributed.get_rank(),
            shuffle=False,
        )
        return DataLoader(
            self._datasets.get_test(),
            sampler=sampler,
            batch_size=self._test_batch_size,
            num_workers=self._data_pipeline_processes,
            collate_fn=self.collate_fn_datapoints,
            pin_memory=False,
            drop_last=True,
            worker_init_fn=self.get_worker_init_fn(),
        )

    def on_train_start(self) -> None:
        if self.logger is not None and self._log_experiment:
            self.logger.experiment.config.batch_size = self._train_batch_size
            self.logger.watch(self._model)
            self.logger.experiment.config.update(
                self._additional_wandb_config, allow_val_change=True,
            )
            self.logger.experiment.config["train_dataset_split"] = self._datasets.get_training().sampler_breakdown
            self.logger.experiment.config[
                "validation_dataset_split"
            ] = self._datasets.get_validation().sampler_breakdown
            self.logger.experiment.config["test_dataset_split"] = self._datasets.get_test().sampler_breakdown

    def configure_optimizers(
        self,
    ) -> Union[
        torch.optim.Optimizer, Tuple[List[torch.optim.Optimizer], List[torch.optim.lr_scheduler._LRScheduler]],
    ]:
        # REQUIRED
        self._optimizer = torch.optim.SGD(
            self.parameters(), lr=self._initial_lr * torch.distributed.get_world_size(), momentum=0.9, weight_decay=1e-5
        )

        if self._lr_adjuster == LrAdjuster.MultiStep:
            assert self._lr_milestones is not None
            scheduler_mslr = torch.optim.lr_scheduler.MultiStepLR(
                self._optimizer, milestones=self._lr_milestones, gamma=self._lr_gamma
            )
            return [self._optimizer], [cast(torch.optim.lr_scheduler._LRScheduler, scheduler_mslr)]
        elif self._lr_adjuster == LrAdjuster.CosineAnnealing:
            scheduler_colr = torch.optim.lr_scheduler.CosineAnnealingLR(
                self._optimizer, T_max=self._lr_t_max, eta_min=self._lr_eta_min
            )
            return [self._optimizer], [cast(torch.optim.lr_scheduler._LRScheduler, scheduler_colr)]

        return self._optimizer

    @staticmethod
    def collate_fn(data: List[ComparisonDatapoint],) -> Any:
        if type(data[0]) is torch.Tensor:
            return data

        batch = (
            torch.stack([cast(torch.Tensor, datapoint.image_one.image) for datapoint in data]).cuda(),
            torch.stack([cast(torch.Tensor, datapoint.image_two.image) for datapoint in data]).cuda(),
            torch.stack([torch.tensor(datapoint.label.target) for datapoint in data]).cuda(),
            [datapoint.metadata.label_id for datapoint in data],
            [datapoint.metadata.user for datapoint in data],
            [datapoint.metadata.source for datapoint in data],
            [datapoint.image_one.recovery_info for datapoint in data],
            [datapoint.image_two.recovery_info for datapoint in data],
        )

        return batch

    @staticmethod
    def collate_fn_datapoints(data: List[ComparisonDatapoint],) -> Any:
        return data

    @torch.no_grad()
    def gpu_transforms(self, batch: List[ComparisonDatapoint], dataset_type: DatasetType) -> List[ComparisonDatapoint]:
        dataset: ComparisonDataset

        if dataset_type == DatasetType.TRAIN:
            dataset = self._datasets.get_training()
        elif dataset_type == DatasetType.VALIDATION:
            dataset = self._datasets.get_validation()
        elif dataset_type == DatasetType.TEST:
            dataset = self._datasets.get_test()

        return transform_batch(batch, dataset)

    def create_step_info(
        self,
        stage: Optional[str],
        epoch: Optional[int],
        batch_nb: Optional[int],
        comparison_model_id: str,
        dataset_id: Optional[str],
        label_ids: List[str],
        users: List[str],
        sources: List[str],
        image_id_to_url: Optional[Dict[str, str]],
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        stage_info = {
            "stage": stage,
            "epoch": epoch,
            "batch_nb": batch_nb,
        }
        comparison_metadata = {
            "comparison_model_id": comparison_model_id,
            "dataset_id": dataset_id,
            "label_ids": label_ids,
            "users": users,
            "sources": sources,
            "image_id_to_url": image_id_to_url,
        }

        return stage_info, comparison_metadata

    def training_step(self, batch: List[ComparisonDatapoint], batch_nb: int) -> Dict[str, Any]:
        if self._debug_signal.is_set():
            import pdb

            pdb.set_trace()
            self._debug_signal.clear()

        preprocessed_batch = self.gpu_transforms(batch, dataset_type=DatasetType.TRAIN)
        image_one, image_two, target, label_ids, users, sources, recovery_info_one, recovery_info_two = self.collate_fn(
            preprocessed_batch
        )
        stage_info, comparison_metadata = self.create_step_info(
            stage="train",
            epoch=self.current_epoch,
            batch_nb=batch_nb,
            comparison_model_id=self._model_id,
            dataset_id=self._dataset_info_dict.get("dataset_id", None) if self._dataset_info_dict is not None else None,
            label_ids=label_ids,
            users=users,
            sources=sources,
            image_id_to_url=self._image_id_to_url,
        )
        out_hat, _ = self.forward(
            image_one, image_two, recovery_info_one, recovery_info_two, f"train_{self.current_epoch}"
        )

        loss = self._loss_fn(out_hat.out.squeeze(), target)

        classes = [
            (
                CROP_ID_TO_LABEL.get(recovery_info_one[n]["category"], recovery_info_one[n]["category"]),
                CROP_ID_TO_LABEL.get(recovery_info_two[n]["category"], recovery_info_two[n]["category"]),
            )
            for n in range(len(recovery_info_one))
        ]
        sizes = [
            (px2mm(recovery_info_one[n]["radius"]), px2mm(recovery_info_two[n]["radius"]))
            for n in range(len(recovery_info_one))
        ]
        metrics = Metrics(
            loss=loss,
            out=target,
            out_hat=out_hat.out.squeeze(),
            classes=classes,
            sizes=sizes,
            threshold=self._threshold,
            match_is_above_threshold=self._match_is_above_threshold,
            recovery_info=(recovery_info_one, recovery_info_two),
            stage_info=stage_info,
            comparison_metadata=comparison_metadata,
            comparison_db_engine=self._comparison_db_engine
            if self._epochs is not None and self.current_epoch == self._epochs - 1
            else None,  # Store training metric only during the last epoch.
        )

        if torch.distributed.get_rank() == 0:
            if self.logger is not None and self._log_experiment:
                for n in range(image_one.shape[0]):
                    if random.random() < self._train_log_image_p:
                        self.log_image(
                            image_one[n], image_two[n], target[n], out_hat.out.squeeze()[n], "train_", label_ids[n],
                        )

        metrics_detached = metrics.detach().cpu()

        metrics_detached_dict = metrics_detached.to_dict()
        metrics_detached_dict.update({"lr": self._optimizer.param_groups[0]["lr"]})

        self.log_dict(metrics_detached_dict, logger=False, prog_bar=True, on_epoch=False, on_step=True)

        result = {"loss": metrics.loss}
        self._training_outputs.append(metrics_detached)

        return result

    def on_train_epoch_end(self) -> None:
        all_metrics = gather_objects(self._training_outputs)
        self._training_outputs.clear()
        if torch.distributed.get_rank() != 0:
            return

        metrics = AverageMetrics(all_metrics)
        matrix_components, categories = metrics.compute_class_performance_components()
        metrics_dict = {k: v.item() for k, v in metrics.to_dict(prefix="train_").items()}
        logging.info(f"\n\nTRAINING RESULTS: {metrics_dict}\n")
        if self.logger is not None and self._log_experiment and isinstance(self.logger, WandbLogger):
            self.logger.experiment.summary.update(metrics_dict)
            scores = [score for score in metrics.scores if score < 2]
            positive_scores = [score for score in metrics.positive_scores if score < 2]
            negative_scores = [score for score in metrics.negative_scores if score < 2]
            self.logger.experiment.log(
                {
                    "train.scores": wandb.Histogram(sequence=scores, num_bins=20),
                    "train.positive_scores": wandb.Histogram(sequence=positive_scores, num_bins=20),
                    "train.negative_scores": wandb.Histogram(sequence=negative_scores, num_bins=20),
                },
                step=self.logger.experiment.step,
            )
            self.log_confusion_matrix(metrics, prefix="train_")
            self.log_class_matrix(matrix_components, categories, prefix="train_")
            self.log_size_matrix(metrics, prefix="train_")

        self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

    @torch.no_grad()
    def validation_step(self, batch: List[ComparisonDatapoint], batch_nb: int) -> None:
        if self._debug_signal.is_set():
            import pdb

            pdb.set_trace()
            self._debug_signal.clear()

        preprocessed_batch = self.gpu_transforms(batch, dataset_type=DatasetType.VALIDATION)
        image_one, image_two, target, label_ids, users, sources, recovery_info_one, recovery_info_two = self.collate_fn(
            preprocessed_batch
        )
        stage_info, comparison_metadata = self.create_step_info(
            stage="validation",
            epoch=self.current_epoch,
            batch_nb=batch_nb,
            comparison_model_id=self._model_id,
            dataset_id=self._dataset_info_dict.get("dataset_id", None) if self._dataset_info_dict is not None else None,
            label_ids=label_ids,
            users=users,
            sources=sources,
            image_id_to_url=self._image_id_to_url,
        )
        out_hat, _ = self.forward(
            image_one, image_two, recovery_info_one, recovery_info_two, f"val_{self.current_epoch}"
        )

        loss = self._loss_fn(out_hat.out.squeeze(), target)
        classes = [
            (
                CROP_ID_TO_LABEL.get(recovery_info_one[n]["category"], recovery_info_one[n]["category"]),
                CROP_ID_TO_LABEL.get(recovery_info_two[n]["category"], recovery_info_two[n]["category"]),
            )
            for n in range(len(recovery_info_one))
        ]
        sizes = [
            (px2mm(recovery_info_one[n]["radius"]), px2mm(recovery_info_two[n]["radius"]))
            for n in range(len(recovery_info_one))
        ]
        metrics = Metrics(
            loss=loss,
            out=target,
            out_hat=out_hat.out.squeeze(),
            classes=classes,
            sizes=sizes,
            threshold=self._threshold,
            match_is_above_threshold=self._match_is_above_threshold,
            recovery_info=(recovery_info_one, recovery_info_two),
            stage_info=stage_info,
            comparison_metadata=comparison_metadata,
            comparison_db_engine=self._comparison_db_engine
            if self._epochs is not None and self.current_epoch == self._epochs - 1
            else None,  # Store validation metric only during the last epoch.
        )

        if torch.distributed.get_rank() == 0:
            if self.logger is not None and self._log_experiment:
                for n in range(image_one.shape[0]):
                    if random.random() < self._val_log_image_p:
                        self.log_image(
                            image_one[n], image_two[n], target[n], out_hat.out.squeeze()[n], "val_", label_ids[n],
                        )

        metrics_detached = metrics.detach().cpu()
        self._validation_outputs.append(metrics_detached)

    def on_validation_epoch_end(self) -> None:
        all_metrics = gather_objects(self._validation_outputs)
        self._validation_outputs.clear()
        if torch.distributed.get_rank() == 0:
            metrics = AverageMetrics(all_metrics)
            metrics_dict = {k: v.item() for k, v in metrics.to_dict(prefix="val_").items()}
            matrix_components, categories = metrics.compute_class_performance_components()
            logging.info(f"\n\nVALIDATION RESULTS: {metrics_dict}\n")
            if self.logger is not None and self._log_experiment and isinstance(self.logger, WandbLogger):
                self.logger.experiment.summary.update(metrics_dict)
                scores = [score for score in metrics.scores if score < 2]
                positive_scores = [score for score in metrics.positive_scores if score < 2]
                negative_scores = [score for score in metrics.negative_scores if score < 2]
                self.logger.experiment.log(
                    {
                        "val.scores": wandb.Histogram(sequence=scores, num_bins=20),
                        "val.positive_scores": wandb.Histogram(sequence=positive_scores, num_bins=20),
                        "val.negative_scores": wandb.Histogram(sequence=negative_scores, num_bins=20),
                    },
                    step=self.logger.experiment.step,
                )
                self.log_confusion_matrix(metrics, prefix="val_")
                self.log_class_matrix(matrix_components, categories, prefix="val_")
                self.log_size_matrix(metrics, prefix="val_")

            f1_results = []
            for category_key, matrix_component in matrix_components.items():
                f1_results.append(
                    {
                        "category_key": category_key,
                        "accuracy": matrix_component["accuracy"],
                        "count": matrix_component["count"],
                    }
                )
            self._datasets.get_training().update_probabilities(f1_results)

            self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)
            val_oec_list = [metrics_dict["val_oec"]]
            torch.distributed.broadcast_object_list(val_oec_list)
        else:
            val_oec_list = [0.0]
            torch.distributed.broadcast_object_list(val_oec_list)
            self.log("val_oec", val_oec_list[0])

    @torch.no_grad()
    def test_step(self, batch: List[ComparisonDatapoint], batch_nb: int) -> None:
        # TODO(Raven): Horovod runs may interfere with resetting of this set
        if batch_nb == 0:
            self._embeddings_set = set()

        if self._debug_signal.is_set():
            import pdb

            pdb.set_trace()
            self._debug_signal.clear()

        try:
            preprocessed_batch = self.gpu_transforms(batch, dataset_type=DatasetType.TEST)
            (
                image_one,
                image_two,
                target,
                label_ids,
                users,
                sources,
                recovery_info_one,
                recovery_info_two,
            ) = self.collate_fn(preprocessed_batch)
            stage_info, comparison_metadata = self.create_step_info(
                stage="test",
                epoch=self.current_epoch,
                batch_nb=batch_nb,
                comparison_model_id=self._model_id,
                dataset_id=self._dataset_info_dict.get("dataset_id", None)
                if self._dataset_info_dict is not None
                else None,
                label_ids=label_ids,
                users=users,
                sources=sources,
                image_id_to_url=self._image_id_to_url,
            )
            out_hat, embeddings = self.forward(
                image_one, image_two, recovery_info_one, recovery_info_two, f"{self.get_test_prefix()}",
            )

            if self._save_embeddings_in_db and len(embeddings) > 0:
                with self._session() as sess:
                    self._embeddings_set = send_embeddings_to_db(sess, embeddings, self._embeddings_set)

                if self._predictions_and_labels_session is not None:
                    with self._predictions_and_labels_session() as sess:
                        send_predictions_to_db(sess, label_ids, out_hat.out, target)

            loss = self._loss_fn(out_hat.out.squeeze(), target)
            classes = [
                (
                    CROP_ID_TO_LABEL.get(recovery_info_one[n]["category"], recovery_info_one[n]["category"]),
                    CROP_ID_TO_LABEL.get(recovery_info_two[n]["category"], recovery_info_two[n]["category"]),
                )
                for n in range(len(recovery_info_one))
            ]
            sizes = [
                (px2mm(recovery_info_one[n]["radius"]), px2mm(recovery_info_two[n]["radius"]))
                for n in range(len(recovery_info_one))
            ]
            metrics = Metrics(
                loss=loss,
                out=target,
                out_hat=out_hat.out.squeeze(),
                classes=classes,
                sizes=sizes,
                threshold=self._threshold,
                match_is_above_threshold=self._match_is_above_threshold,
                recovery_info=(recovery_info_one, recovery_info_two),
                stage_info=stage_info,
                comparison_metadata=comparison_metadata,
                comparison_db_engine=self._comparison_db_engine,
            )
            metrics_detached = metrics.detach().cpu()
            self._test_outputs.append(metrics_detached)
        except Exception as e:
            logging.warning(f"Couldn't run test step: {e}")

    def get_test_prefix(self) -> str:
        prefix = "test_"
        if self.trt_model is None:
            prefix += "unoptimized_"
        return prefix

    def on_test_epoch_end(self) -> None:
        all_metrics = gather_objects(self._test_outputs)
        self._test_outputs.clear()
        if torch.distributed.get_rank() != 0:
            return
        metrics = AverageMetrics(all_metrics)

        del all_metrics

        all_embeddings = None
        with self._session() as sess:
            all_embeddings = sess.query(ComparisonEmbeddings).filter_by(epoch=f"{self.get_test_prefix()}").all()

        if self._save_image_point_embeddings:
            save_comparison_embeddings(
                embeddings=all_embeddings,
                dir=self._embedding_output_path,
                id_to_url=self._datasets.get_test().image_ids_to_urls,
                captured_at_dict=self._dataset_info_dict["captured_at"],
                crop_id_dict=self._dataset_info_dict["crop_id"],
                model_id=self._model_id,
                dataset_id=self._dataset_info_dict["dataset_id"],
                mode="torch",
            )
            LOG.info("Finish saving comparison embeddings: torch.")

        if self._save_hdf5_embeddings:
            save_comparison_embeddings(
                embeddings=all_embeddings,
                dir=self._embedding_output_path,
                id_to_url=self._datasets.get_test().image_ids_to_urls,
                captured_at_dict=self._dataset_info_dict["captured_at"],
                crop_id_dict=self._dataset_info_dict["crop_id"],
                model_id=self._model_id,
                dataset_id=self._dataset_info_dict["dataset_id"],
                mode="hdf5",
            )
            LOG.info("Finish saving comparison embeddings: hdf5.")

        if self.logger is not None and self._log_experiment and isinstance(self.logger, WandbLogger):
            metrics_dict = {k: v.item() for k, v in metrics.to_dict(prefix=self.get_test_prefix()).items()}
            matrix_components, categories = metrics.compute_class_performance_components()
            self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)
            logging.info(f"\n\nTEST RESULTS: {metrics_dict}\n")
            self.logger.experiment.summary.update(metrics_dict)
            scores = [score for score in metrics.scores if score < 2]
            positive_scores = [score for score in metrics.positive_scores if score < 2]
            negative_scores = [score for score in metrics.negative_scores if score < 2]
            self.logger.experiment.log(
                {
                    "test.scores": wandb.Histogram(sequence=scores, num_bins=20),
                    "test.positive_scores": wandb.Histogram(sequence=positive_scores, num_bins=20),
                    "test.negative_scores": wandb.Histogram(sequence=negative_scores, num_bins=20),
                },
                step=self.logger.experiment.step,
            )

            self.log_confusion_matrix(metrics, prefix=f"{self.get_test_prefix()}")
            self.log_class_matrix(matrix_components, categories, prefix=f"{self.get_test_prefix()}")
            self.log_size_matrix(metrics, prefix=f"{self.get_test_prefix()}")

            # Sending Embeddings to wandb
            embeddings_df, embeddings_metadata, _, n_comp = embeddings_to_dataframes(all_embeddings)
            # Loading Images and Adding to Embeddings
            embeddings_df[n_comp + 1] = [
                wandb.Image(
                    self._datasets.get_test().crop_image_by_image_id(
                        embeddings_metadata["image_id"][x],
                        embeddings_metadata["x"][x],
                        embeddings_metadata["y"][x],
                        embeddings_metadata["radius"][x],
                        cache_cropped_image=True,
                    )
                )
                for x in range(len(embeddings_metadata))
            ]

            self.logger.experiment.log({"test.embeddings": wandb.Table(dataframe=embeddings_df)})

    def set_exp_dir(self, exp_dir: str) -> None:
        pass

    def set_experiment_url(self, experiment_url: str) -> None:
        self._experiment_url = experiment_url

    def export_model(self) -> torch.nn.Module:
        return self._model

    def export_torch_script(self) -> torch.jit.ScriptModule:
        return cast(torch.jit.ScriptModule, torch.jit.script(self._model))

    def export_metadata(self) -> ModelMetadata:
        return ModelMetadata(
            input_dtype=torch.float32,
            input_size=(self._datasets.get_training().image_radius * 2, self._datasets.get_training().image_radius * 2),
            means=IMAGENET_MEANS[:3],
            stds=IMAGENET_STDS[:3],
            experiment_url=self._experiment_url,
            use_cases=self.use_cases,
            ppi=self._datasets.get_training().model_ppi,
            # TODO(asergeev): figure out low precision support
            supports_half=False,
            model_class=self._model.__class__.__name__,
        )

    @torch.no_grad()
    def log_image(
        self,
        image_one: torch.Tensor,
        image_two: torch.Tensor,
        out: torch.Tensor,
        out_hat: torch.Tensor,
        prefix: str,
        label_id: str,
    ) -> None:
        assert self.logger is not None and self._log_experiment and isinstance(self.logger, WandbLogger)

        img1 = denormalize_image(image_one)
        img2 = denormalize_image(image_two)

        img = torch.cat([img1, img2], dim=2)

        img = img * 255.0
        img = torch.clip(img, 0, 255)

        example_type = ""
        out_hat_val = out_hat.item()
        out_val = out.item()

        if out_hat_val >= 0.5 and out_val == 1:
            example_type = "true_positive"
        elif out_hat_val < 0.5 and out_val == 1:
            example_type = "false_negative"
        elif out_hat_val >= 0.5 and out_val == 0:
            example_type = "false_positive"
        else:
            example_type = "true_negative"

        filename_overlay: npt.NDArray[Any] = np.zeros((img.shape[1:3]), dtype=np.uint8)
        filename_overlay = cv2.putText(
            filename_overlay, f"label_id: {label_id}", (20, 20), cv2.FONT_HERSHEY_COMPLEX_SMALL, 1, (255,), 2
        )
        filename_overlay = cv2.putText(
            filename_overlay, f"score:    {out_hat_val}", (20, 50), cv2.FONT_HERSHEY_COMPLEX_SMALL, 1, (255,), 2
        )
        img[1, :, :] = torch.max(img[1, :, :], torch.tensor(filename_overlay).cuda().float())

        self.logger.experiment.log({f"{prefix}{example_type}": [wandb.Image(img)]})

    def log_confusion_matrix(self, avg_metrics: AverageMetrics, prefix: str) -> None:
        if len(avg_metrics.metrics) <= 0:
            return

        confusion_matrix_components = avg_metrics.compute_confusion_matrix_components()
        self._log_confusion_matrix(
            prefix,
            true_positives=confusion_matrix_components["true_positives"],
            true_negatives=confusion_matrix_components["true_negatives"],
            false_positives=confusion_matrix_components["false_positives"],
            false_negatives=confusion_matrix_components["false_negatives"],
        )

    def _log_confusion_matrix(
        self,
        prefix: str,
        true_positives: torch.Tensor,
        true_negatives: torch.Tensor,
        false_positives: torch.Tensor,
        false_negatives: torch.Tensor,
    ) -> None:
        assert self.logger is not None and isinstance(self.logger, WandbLogger)
        table = [
            ["l_match", true_positives, false_negatives],
            ["l_no_match", false_positives, true_negatives],
        ]

        confusion_matrix = tabulate(table, headers=["p_match", "p_no_match"])

        self.logger.experiment.log(
            {f"{prefix}confusion_matrix": wandb.Html(f"<pre>{confusion_matrix}</pre>")},
            step=self.logger.experiment.step,
        )

    def log_class_matrix(
        self, matrix_components: Dict[Tuple[str, str], Dict[str, torch.Tensor]], categories: List[str], prefix: str
    ) -> None:
        self.log_matrix(matrix_components, categories, f"{prefix}category_")

        for key, value in matrix_components.items():
            sum = (
                value["true_positives"].item()
                + value["true_negatives"].item()
                + value["false_positives"].item()
                + value["false_negatives"].item()
            )
            if sum > 20:
                self._log_confusion_matrix(
                    prefix=f"{prefix}{key[0]}_{key[1]}_",
                    true_positives=value["true_positives"] / sum,
                    true_negatives=value["true_negatives"] / sum,
                    false_positives=value["false_positives"] / sum,
                    false_negatives=value["false_negatives"] / sum,
                )

    def log_matrix(
        self, matrix_components: Dict[Tuple[str, str], Dict[str, torch.Tensor]], headers: List[str], prefix: str
    ) -> None:
        assert self.logger is not None and isinstance(self.logger, WandbLogger)
        table_f1 = []
        table_accuracy = []
        table_precision = []
        table_recall = []
        for category_a in headers:
            row_f1: List[Any] = [category_a]
            row_accuracy: List[Any] = [category_a]
            row_precision: List[Any] = [category_a]
            row_recall: List[Any] = [category_a]
            for category_b in headers:
                key = (category_a, category_b)
                component = matrix_components.get(key, {})

                f1 = (component["f1"].item(), component["count"].item()) if component.get("f1") is not None else None
                accuracy = (
                    (component["accuracy"].item(), component["count"].item())
                    if component.get("accuracy") is not None
                    else None
                )
                precision = (
                    (component["precision"].item(), component["count"].item())
                    if component.get("precision") is not None
                    else None
                )
                recall = (
                    (component["recall"].item(), component["count"].item())
                    if component.get("recall") is not None
                    else None
                )

                row_f1.append(f1)
                row_accuracy.append(accuracy)
                row_precision.append(precision)
                row_recall.append(recall)

            table_f1.append(row_f1)
            table_accuracy.append(row_accuracy)
            table_precision.append(row_precision)
            table_recall.append(row_recall)

        matrix_f1 = tabulate(table_f1, headers=headers)
        matrix_accuracy = tabulate(table_accuracy, headers=headers)
        matrix_precision = tabulate(table_precision, headers=headers)
        matrix_recall = tabulate(table_recall, headers=headers)

        self.logger.experiment.log(
            {f"{prefix}f1_matrix": wandb.Html(f"<pre>{matrix_f1}</pre>")}, step=self.logger.experiment.step,
        )

        self.logger.experiment.log(
            {f"{prefix}accuracy_matrix": wandb.Html(f"<pre>{matrix_accuracy}</pre>")}, step=self.logger.experiment.step,
        )

        self.logger.experiment.log(
            {f"{prefix}precision_matrix": wandb.Html(f"<pre>{matrix_precision}</pre>")},
            step=self.logger.experiment.step,
        )

        self.logger.experiment.log(
            {f"{prefix}recall_matrix": wandb.Html(f"<pre>{matrix_recall}</pre>")}, step=self.logger.experiment.step,
        )

    def log_size_matrix(self, avg_metrics: AverageMetrics, prefix: str) -> None:
        if len(avg_metrics.metrics) <= 0:
            return
        matrix_components = avg_metrics.compute_size_performance_components()
        self.log_matrix(matrix_components, [str(b) for b in SIZE_BUCKETS], f"{prefix}size_")

    @property
    def trt_file_name(self) -> str:
        # TODO [Raven] replace with get_tensorrt_file_name() when we either convert to int8 or fp16
        return "trt_fp32.trt"

    @property
    def trt_file_path(self) -> str:
        assert self._output_dir is not None
        return os.path.join(self._output_dir, self.trt_file_name)

    def optimize_for_testing(self, best_checkpoint: Optional[str] = None) -> bool:
        if self.config.make_trt_model:
            torch.cuda.empty_cache()
            assert self._output_dir is not None
            trt_converter = TrtConvert(self._datasets, self.export_metadata(), self.export_model())
            trt_converter.convert(
                max_batch_size=self._trt_max_batch_size, save_to=self.trt_file_path,
            )
            torch.distributed.barrier()

            # replace model with optimized model
            self.load_trt(self.trt_file_path)
            return True
        return False

    def load_trt(self, trt_file_path: str) -> None:
        self.trt_model, _ = load_tensorrt_model(trt_file_path)
        cast(rt.TRTModule, self.trt_model).set_cache_context(True)

        LOG.info(f"Rank {torch.distributed.get_rank()} loaded TensorRT model.")


class ComparisonTrainer(Trainer):
    def __init__(self) -> None:
        super().__init__()
        self._datasets: Optional[ComparisonDatasets] = None

    def __enter__(self) -> "ComparisonTrainer":
        return self

    def __exit__(self, exc_type: Any, exc_value: Any, traceback: Any) -> None:
        return

    def explicit_split_dataset(
        self,
        label_dataset_path: str,
        model_dir: str,
        image_id_to_url: Dict[str, str],
        image_id_to_geohash: Dict[str, Optional[str]],
        train_ids_in_dataset: List[str],
        validation_ids_in_dataset: List[str],
        test_ids_in_dataset: List[str],
        source_weights: Dict[str, float],
        source_sampler: Type[ComparisonLabelSampler],
        num_balanced_examples: int = 2000,
        mask_around_plant: bool = False,
        sampler: Type[ComparisonLabelSampler] = ComparisonLabelSamplerBasic,
        extra_augmentations: bool = False,
        crop_on_server: bool = True,
        label_id_to_uuids: Optional[Dict[str, Any]] = None,
        image_id_to_height_width: Optional[Dict[str, Tuple[int, int]]] = None,
        overlapped_points_dict: Optional[Dict[str, Dict[str, Dict[Tuple[float, float, str], bool]]]] = None,
    ) -> "Trainer":
        assert source_sampler is not issubclass(
            sampler, ComparisonLabelSamplerSource
        ), "Sampler used by the source sampler cannot be ComparisonLabelSamplerSource."
        label_id_to_uuids = label_id_to_uuids if label_id_to_uuids is not None else {}
        image_id_to_height_width = image_id_to_height_width if image_id_to_height_width is not None else {}

        mk_dataset = functools.partial(
            ComparisonDataset,
            label_database_path=label_dataset_path,
            model_dir=model_dir,
            image_id_to_url=image_id_to_url,
            source_weights=source_weights,
            source_sampler=source_sampler,
            mask_around_plant=mask_around_plant,
            image_id_to_geohash=image_id_to_geohash,
            sampler=sampler,
            extra_augmentations=extra_augmentations,
            crop_on_server=crop_on_server,
            label_id_to_uuids=label_id_to_uuids,
            image_id_to_height_width=image_id_to_height_width,
            overlapped_points_dict=overlapped_points_dict,
        )

        train_dataset = mk_dataset(
            ids_in_dataset=train_ids_in_dataset,
            num_balanced_examples=num_balanced_examples,
            training=True,
            phase="train",
        )
        val_dataset = mk_dataset(ids_in_dataset=validation_ids_in_dataset, training=False, phase="validation")
        test_dataset = mk_dataset(ids_in_dataset=test_ids_in_dataset, training=False, phase="test")
        calibration_dataset = mk_dataset(
            ids_in_dataset=test_ids_in_dataset, training=False, num_balanced_examples=128, phase="calibration"
        )
        self._datasets = ComparisonDatasets(
            train_dataset=train_dataset,
            validation_dataset=val_dataset,
            test_dataset=test_dataset,
            calibration_dataset=calibration_dataset,
        )

        return self

    def train(
        self,
        config: ComparisonConfig,
        checkpoint_dir: str,
        model_id: str,
        embedding_output_path: str,
        image_id_to_url: Optional[Dict[str, str]],
        fast_run: bool = False,
        epochs: int = 30,
        initial_lr: float = 0.01,
        lr_milestones: Optional[List[int]] = None,
        lr_gamma: float = 0.5,
        train_batch_size: int = 4,
        val_batch_size: int = 4,
        test_batch_size: int = MAX_BATCH_SIZE,
        data_pipeline_processes: int = 4,
        resume_from: Optional[str] = None,
        description: Optional[str] = None,
        log_experiment: bool = True,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        environment: Environment = Environment.DEVELOPMENT,
        deploy: bool = False,
        pretrained_model: Optional[str] = None,
        tags: Optional[Union[str, Tuple[str, ...]]] = None,
        additional_wandb_config: Optional[Dict[str, Any]] = None,
        model: Type[ComparisonModelBase] = LearnableReductionDensenet201CosineComparisonModel,
        loss_fn: Callable[[torch.Tensor, torch.Tensor], torch.Tensor] = torch.nn.functional.binary_cross_entropy,
        save_embeddings_in_db: bool = False,
        lr_adjuster: LrAdjuster = LrAdjuster.MultiStep,
        lr_eta_min: float = 0.0,
        lr_t_max: int = 40,
        threshold: float = 0.5,
    ) -> None:
        additional_wandb_config = additional_wandb_config if additional_wandb_config is not None else {}
        assert self._datasets is not None

        module = ComparisonTrainingModule(
            config=config,
            datasets=self._datasets,
            image_id_to_url=image_id_to_url,
            initial_lr=initial_lr,
            lr_milestones=lr_milestones,
            lr_gamma=lr_gamma,
            epochs=epochs,
            train_batch_size=train_batch_size,
            val_batch_size=val_batch_size,
            test_batch_size=test_batch_size,
            data_pipeline_processes=data_pipeline_processes,
            model=model,
            loss_fn=loss_fn,
            data_dir=checkpoint_dir,
            save_embeddings_in_db=save_embeddings_in_db,
            model_id=model_id,
            embedding_output_path=embedding_output_path,
            comparison_db_output_path=checkpoint_dir,
            additional_wandb_config=additional_wandb_config,
            lr_adjuster=lr_adjuster,
            lr_eta_min=lr_eta_min,
            lr_t_max=lr_t_max,
            threshold=threshold,
        )

        self.experiment_directory = super()._train(
            module,
            get_version(),
            fast_run=fast_run,
            epochs=epochs,
            resume_from=resume_from,
            description=description,
            tags=tags,
            log_experiment=log_experiment,
            ci_run=config.ci_run,
            checkpoint_dir=checkpoint_dir,
            environment=environment,
            deploy=deploy,
            sub_type="comparison",
            sync_batchnorm=True,
        )

        if not config.ci_run and torch.distributed.get_rank() == 0:
            assert self.experiment_directory
            version = self.experiment_directory.rsplit("/", maxsplit=1)[-1]
            wandb_json = get_wandb_metadata_json_str(self.experiment_directory)
            self._veselka_post_model(
                model_id=version,
                url=f"s3://{S3_BUCKET}/models/{version}/{module.trt_file_name}",
                checksum=compute_md5sum(module.trt_file_path),
                trained_at=int(time.time()),
                metadata_json=module.export_metadata().dump(),
                wandb_json=wandb_json,
                is_good_to_deploy=True,
                dl_config=config.to_dict(),
            )

    def infer(
        self,
        config: ComparisonConfig,
        checkpoint_dir: str,
        experiment_dir: str,
        dataset_info_dict: Dict[str, Any],
        model_id: str,
        embedding_output_path: str,
        comparison_db_output_path: str,
        image_id_to_url: Optional[Dict[str, str]],
        fast_run: bool = False,
        initial_lr: float = 0.01,
        lr_milestones: Optional[List[int]] = None,
        lr_gamma: float = 0.5,
        train_batch_size: int = 4,
        val_batch_size: int = 4,
        test_batch_size: int = MAX_BATCH_SIZE,
        data_pipeline_processes: int = 4,
        resume_from: Optional[str] = None,
        trt_filepath: Optional[str] = None,
        model: Type[ComparisonModelBase] = LearnableReductionDensenet201CosineComparisonModel,
        loss_fn: Callable[[torch.Tensor, torch.Tensor], torch.Tensor] = torch.nn.functional.binary_cross_entropy,
        save_embeddings_in_db: bool = False,
        save_image_point_embeddings: bool = False,
        save_hdf5_embeddings: bool = False,
        predictions_and_labels_path: Optional[str] = None,
    ) -> None:
        assert self._datasets is not None

        module = ComparisonTrainingModule(
            config=config,
            datasets=self._datasets,
            image_id_to_url=image_id_to_url,
            initial_lr=initial_lr,
            lr_milestones=lr_milestones,
            lr_gamma=lr_gamma,
            epochs=None,
            train_batch_size=train_batch_size,
            val_batch_size=val_batch_size,
            test_batch_size=test_batch_size,
            data_pipeline_processes=data_pipeline_processes,
            model=model,
            loss_fn=loss_fn,
            data_dir=checkpoint_dir,
            experiment_dir=experiment_dir,
            save_embeddings_in_db=save_embeddings_in_db,
            log_experiment=False,
            model_id=model_id,
            dataset_info_dict=dataset_info_dict,
            embedding_output_path=embedding_output_path,
            comparison_db_output_path=comparison_db_output_path,
            save_image_point_embeddings=save_image_point_embeddings,
            save_hdf5_embeddings=save_hdf5_embeddings,
            predictions_and_labels_path=predictions_and_labels_path,
        )

        if trt_filepath is not None:
            module.load_trt(trt_filepath)

        super()._infer(module=module, fast_run=fast_run, resume_from=resume_from, evaluation_dir=experiment_dir)

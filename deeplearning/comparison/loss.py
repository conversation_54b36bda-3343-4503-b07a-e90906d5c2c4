from typing import Callable, Optional, cast

import torch


def get_weights_false_positives(out_hat: torch.Tensor, out_target: torch.Tensor, weight: float) -> torch.Tensor:
    weights = torch.ones_like(out_hat)
    weights[((out_hat > 0.5) & (out_target < 0.5))] = weight
    return weights


def binary_cross_entropy_punish_false_positives(out_hat: torch.Tensor, out_target: torch.Tensor) -> torch.Tensor:
    weights = get_weights_false_positives(out_hat, out_target, 2)
    return torch.nn.functional.binary_cross_entropy(out_hat, out_target, weight=weights)


def mse_loss_punish_false_positives(out_hat: torch.Tensor, out_target: torch.Tensor) -> torch.Tensor:
    weights = get_weights_false_positives(out_hat, out_target, 2)
    return torch.nn.functional.mse_loss(weights * out_hat, out_target)


def contrastive_loss(
    out_hat: torch.Tensor,
    out_target: torch.Tensor,
    margin: float = 1.0,
    aggregation_fn: Optional[Callable[[torch.Tensor], torch.Tensor]] = torch.mean,
) -> torch.Tensor:
    loss = out_target * torch.pow(out_hat, 2) + (1 - out_target) * torch.pow(torch.clamp(margin - out_hat, min=0.0), 2)
    if aggregation_fn:
        loss = aggregation_fn(loss)

    return cast(torch.Tensor, loss)


def contrastive_loss_ratio(
    out_hat: torch.Tensor, out_target: torch.Tensor, aggregation_fn: Callable[[torch.Tensor], torch.Tensor] = torch.mean
) -> torch.Tensor:
    loss = aggregation_fn((out_target * torch.pow(out_hat, 2) + (1 - out_target) * torch.pow(1 / (out_hat + 1e-10), 2)))

    return loss

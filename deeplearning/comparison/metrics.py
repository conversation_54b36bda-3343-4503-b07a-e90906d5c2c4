from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Tuple, cast

import torch
from sqlalchemy.engine import Engine

from deeplearning.comparison.data_utils import get_category_key, get_size_key
from deeplearning.dl_metrics.comparison_metrics import comparison_db
from deeplearning.dl_metrics.comparison_metrics.utils import store_comparison_db
from deeplearning.utils.tensor import recursive_detach, recursive_move


def global_ratio(a: torch.Tensor, b: torch.Tensor, smooth: float = 1e-6,) -> torch.Tensor:
    a = a.sum(0)
    b = b.sum(0)
    metric = (a + smooth) / (b + smooth)

    if len(metric.shape) == 1:
        metric = metric.sum(0) / metric.shape[0]

    assert len(metric.shape) == 0
    return metric


class Metrics:
    def __init__(
        self,
        out: torch.Tensor,
        out_hat: torch.Tensor,
        loss: torch.Tensor,
        classes: List[Tuple[str, str]],
        sizes: List[Tuple[float, float]],
        recovery_info: Optional[Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]] = None,
        stage_info: Optional[Dict[str, Any]] = None,
        comparison_metadata: Optional[Dict[str, Any]] = None,
        threshold: float = 0.5,
        match_is_above_threshold: bool = True,
        comparison_db_engine: Optional[Engine] = None,
    ):
        # _recovering_information contains: id, x, y, radius, category, label_id, geohash, uuid

        self.loss = loss

        self.out = out
        self.out_hat = out_hat

        out_threshed = self.out > 0.5

        out_below_threshed = self.out <= 0.5
        comparison_db_session = (
            comparison_db.get_session(engine=comparison_db_engine) if comparison_db_engine is not None else None
        )

        if match_is_above_threshold:
            out_hat_match = self.out_hat > threshold
            out_hat_non_match = self.out_hat <= threshold
        else:
            out_hat_match = self.out_hat <= threshold
            out_hat_non_match = self.out_hat > threshold

        with torch.no_grad():
            self.true_positives = (out_threshed & out_hat_match).float()
            self.true_negatives = (out_below_threshed & out_hat_non_match).float()
            self.false_positives = (out_below_threshed & out_hat_match).float()
            self.false_negatives = (out_threshed & out_hat_non_match).float()
            self.out_sum = out_threshed.float()
            self.out_hat_sum = out_hat_match.float()

            if comparison_db_session is not None and recovery_info is not None:
                store_comparison_db(
                    out_hat=out_hat,
                    out_threshed=out_threshed,
                    comparison_db_session=comparison_db_session,
                    recovery_info=recovery_info,
                    stage_info=stage_info,
                    comparison_metadata=comparison_metadata,
                )

        self.classes = classes
        self.sizes = sizes

    def compute_precision(self,) -> torch.Tensor:
        return global_ratio(self.true_positives, self.out_hat_sum)

    def compute_recall(self) -> torch.Tensor:
        return global_ratio(self.true_positives, self.out_sum)

    def compute_accuracy(self) -> torch.Tensor:
        return (self.true_positives.sum() + self.true_negatives.sum()) / self.true_positives.shape[0]

    def compute_f1(self,) -> torch.Tensor:
        p = self.compute_precision()
        r = self.compute_recall()
        return 2 * p * r / (p + r)

    def to_dict(self, prefix: str = "") -> Dict[str, Any]:
        d: Dict[str, Any] = dict()
        d[f"{prefix}loss"] = self.loss
        d[f"{prefix}recall"] = self.compute_recall()
        d[f"{prefix}precision"] = self.compute_precision()
        d[f"{prefix}f1"] = self.compute_f1()
        return d

    def detach(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_detach(self.__dict__)
        return obj

    def cpu(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_move(self.__dict__, torch.device("cpu"))
        return obj


class AverageMetrics:
    def __init__(
        self, metrics: List[Metrics],
    ):
        self.metrics = metrics

    def compute_avg_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.loss for m in self.metrics]).mean()

    def compute_global_precision(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        true_positives_list: List[torch.Tensor] = []
        out_hat_sum_list: List[torch.Tensor] = []

        for metric in self.metrics:
            true_positives_list.extend(metric.true_positives)
            out_hat_sum_list.extend(metric.out_hat_sum)

        true_positives = torch.stack(true_positives_list)
        out_hats = torch.stack(out_hat_sum_list)
        return global_ratio(true_positives, out_hats)

    def compute_global_recall(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        true_positives_list: List[torch.Tensor] = []
        out_sum_list: List[torch.Tensor] = []

        for metric in self.metrics:
            true_positives_list.extend(metric.true_positives)
            out_sum_list.extend(metric.out_sum)

        true_positives = torch.stack(true_positives_list)
        outs = torch.stack(out_sum_list)
        return global_ratio(true_positives, outs)

    def compute_global_f1(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)
        p = self.compute_global_precision()
        r = self.compute_global_recall()
        return 2 * p * r / (p + r)

    def compute_global_accuracy(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        true_positives_list: List[torch.Tensor] = []
        true_negatives_list: List[torch.Tensor] = []

        for metric in self.metrics:
            true_positives_list.extend(metric.true_positives)
            true_negatives_list.extend(metric.true_negatives)

        true_positives = torch.stack(true_positives_list)
        true_negatives = torch.stack(true_negatives_list)

        return (true_positives.sum() + true_negatives.sum()) / len(true_positives)

    def compute_global_l1_error(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_list: List[torch.Tensor] = []
        out_hat_list: List[torch.Tensor] = []

        for metric in self.metrics:
            out_list.extend(metric.out)
            out_hat_list.extend(metric.out_hat)

        out = torch.stack(out_list)
        out_hat = torch.stack(out_hat_list)

        return (out - out_hat).abs().mean()

    def compute_global_positive_l1_error(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_list: List[torch.Tensor] = []
        out_hat_list: List[torch.Tensor] = []

        for metric in self.metrics:
            out_list.extend(metric.out)
            out_hat_list.extend(metric.out_hat)

        out = torch.stack(out_list)
        out_hat = torch.stack(out_hat_list)

        filtered_out_hat = out_hat[out > 0.5]

        return cast(torch.Tensor, 1 - filtered_out_hat).mean()

    def compute_global_negative_l1_error(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_list: List[torch.Tensor] = []
        out_hat_list: List[torch.Tensor] = []

        for metric in self.metrics:
            out_list.extend(metric.out)
            out_hat_list.extend(metric.out_hat)

        out = torch.stack(out_list)
        out_hat = torch.stack(out_hat_list)

        filtered_out_hat = out_hat[out < 0.5]

        return filtered_out_hat.mean()

    def get_percentage_positive_targets(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_sum_list: List[torch.Tensor] = []
        for metrics in self.metrics:
            out_sum_list.extend(metrics.out_sum)

        if len(out_sum_list) == 0:
            return torch.tensor(0.0)

        out_sum_tensor = torch.stack(out_sum_list)

        return out_sum_tensor.sum(0) / out_sum_tensor.shape[0]

    def get_percentage_of_positive_targets_in_range(self, min: float, max: float) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_list: List[torch.Tensor] = []
        out_hat_list: List[torch.Tensor] = []

        for metric in self.metrics:
            out_list.extend(metric.out)
            out_hat_list.extend(metric.out_hat)

        out = torch.stack(out_list)
        out_hat = torch.stack(out_hat_list)

        positive_examples = out > 0.5

        positive_examples_within_range = (out > 0.5) & (out_hat >= min) & (out_hat <= max)

        return positive_examples_within_range.sum(0) / positive_examples.sum(0)

    def get_percentage_of_negative_targets_in_range(self, min: float, max: float) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_list: List[torch.Tensor] = []
        out_hat_list: List[torch.Tensor] = []

        for metric in self.metrics:
            out_list.extend(metric.out)
            out_hat_list.extend(metric.out_hat)

        out = torch.stack(out_list)
        out_hat = torch.stack(out_hat_list)

        negative_examples = out < 0.5

        negative_examples_within_range = (out < 0.5) & (out_hat >= min) & (out_hat <= max)

        return negative_examples_within_range.sum(0) / negative_examples.sum(0)

    def get_number_total_targets(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        out_sum_list: List[torch.Tensor] = []
        for metrics in self.metrics:
            out_sum_list.extend(metrics.out_sum)

        return torch.tensor(len(out_sum_list))

    def compute_oec(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return self.compute_global_f1()

    def compute_confusion_matrix_components(self) -> Dict[str, torch.Tensor]:
        true_positives: List[torch.Tensor] = []
        true_negatives: List[torch.Tensor] = []
        false_positives: List[torch.Tensor] = []
        false_negatives: List[torch.Tensor] = []

        for metrics in self.metrics:
            true_positives.extend(metrics.true_positives)
            true_negatives.extend(metrics.true_negatives)
            false_positives.extend(metrics.false_positives)
            false_negatives.extend(metrics.false_negatives)

        t_true_positives = torch.stack(true_positives)
        t_true_negatives = torch.stack(true_negatives)
        t_false_positives = torch.stack(false_positives)
        t_false_negatives = torch.stack(false_negatives)

        number_items = len(t_true_positives)

        return {
            "true_positives": t_true_positives.sum() / number_items,
            "true_negatives": t_true_negatives.sum() / number_items,
            "false_positives": t_false_positives.sum() / number_items,
            "false_negatives": t_false_negatives.sum() / number_items,
        }

    def compute_size_performance_components(self) -> Dict[Tuple[str, str], Dict[str, torch.Tensor]]:
        key_metrics, _ = self.get_breakdown_metrics(self.get_sizes, get_size_key)
        return key_metrics

    def compute_class_performance_components(self) -> Tuple[Dict[Tuple[str, str], Dict[str, torch.Tensor]], List[str]]:
        return self.get_breakdown_metrics(self.get_categories, get_category_key)

    def get_categories(self, metrics: Metrics) -> List[Tuple[str, str]]:
        return metrics.classes

    def get_sizes(self, metrics: Metrics) -> List[Tuple[float, float]]:
        return metrics.sizes

    def get_breakdown_metrics(
        self,
        get_items: Callable[[Metrics], List[Tuple[Any, Any]]],
        get_key: Callable[[Tuple[str, str]], Tuple[str, str]],
    ) -> Tuple[Dict[Tuple[str, str], Dict[str, torch.Tensor]], List[Any]]:
        performance_true_positives = defaultdict(list)
        performance_true_negatives = defaultdict(list)
        performance_false_positives = defaultdict(list)
        performance_false_negatives = defaultdict(list)

        values = set()

        for metrics in self.metrics:
            for ind, items in enumerate(get_items(metrics)):
                key = get_key(items)
                values.add(key[0])
                values.add(key[1])
                performance_true_positives[key].append(metrics.true_positives[ind])
                performance_true_negatives[key].append(metrics.true_negatives[ind])
                performance_false_positives[key].append(metrics.false_positives[ind])
                performance_false_negatives[key].append(metrics.false_negatives[ind])

        key_metrics: Dict[Tuple[str, str], Dict[str, torch.Tensor]] = defaultdict(dict)

        for key, true_positives_list in performance_true_positives.items():
            true_positives = torch.stack(true_positives_list)
            true_negatives = torch.stack(performance_true_negatives[key])
            false_positives = torch.stack(performance_false_positives[key])
            false_negatives = torch.stack(performance_false_negatives[key])

            key_metrics[key]["true_positives"] = true_positives.sum()
            key_metrics[key]["true_negatives"] = true_negatives.sum()
            key_metrics[key]["false_positives"] = false_positives.sum()
            key_metrics[key]["false_negatives"] = false_negatives.sum()
            key_metrics[key]["count"] = torch.ones(1) * len(true_positives_list)

            key_metrics[key]["precision"] = true_positives.sum() / (true_positives.sum() + false_positives.sum() + 1e-9)
            key_metrics[key]["recall"] = true_positives.sum() / (true_positives.sum() + false_negatives.sum() + 1e-9)
            key_metrics[key]["f1"] = (2 * key_metrics[key]["precision"] * key_metrics[key]["recall"]) / (
                key_metrics[key]["precision"] + key_metrics[key]["recall"] + 1e-9
            )
            key_metrics[key]["accuracy"] = (true_positives.sum() + true_negatives.sum()) / (
                true_positives.sum() + true_negatives.sum() + false_positives.sum() + false_negatives.sum() + 1e-9
            )

        return key_metrics, sorted(list(values))

    @property
    def scores(self) -> List[float]:
        if len(self.metrics) == 0:
            return []

        out_scores: List[float] = []
        for met in self.metrics:
            out_scores.extend(met.out_hat.tolist())

        return out_scores

    @property
    def positive_scores(self) -> List[float]:
        if len(self.metrics) == 0:
            return []

        out_scores: List[float] = []
        for met in self.metrics:
            out_scores.extend(met.out_hat[met.out > 0.5].tolist())

        return out_scores

    @property
    def negative_scores(self) -> List[float]:
        if len(self.metrics) == 0:
            return []

        out_scores: List[float] = []
        for met in self.metrics:
            out_scores.extend(met.out_hat[met.out < 0.5].tolist())

        return out_scores

    def to_dict(self, prefix: str = "") -> Dict[str, Any]:
        d: Dict[str, Any] = dict()
        d[f"{prefix}oec"] = self.compute_oec()
        d[f"avg_{prefix}loss"] = self.compute_avg_loss()
        d[f"global_{prefix}precision"] = self.compute_global_precision()
        d[f"global_{prefix}recall"] = self.compute_global_recall()
        d[f"global_{prefix}f1"] = self.compute_global_f1()
        d[f"global_{prefix}accuracy"] = self.compute_global_accuracy()
        d[f"global_{prefix}l1_error"] = self.compute_global_l1_error()
        d[f"global_{prefix}positive_l1_error"] = self.compute_global_positive_l1_error()
        d[f"global_{prefix}negative_l1_error"] = self.compute_global_negative_l1_error()
        d[f"global_{prefix}percentage_positive_targets"] = self.get_percentage_positive_targets()
        d[f"global_{prefix}percentage_positive_examples_above_90"] = self.get_percentage_of_positive_targets_in_range(
            0.9, 1.0
        )
        d[f"global_{prefix}percentage_negative_examples_below_50"] = self.get_percentage_of_negative_targets_in_range(
            0.0, 0.5
        )

        return d

import functools
from typing import Dict, Type, cast

from deeplearning.comparison.models.center_pixel_densenet_201_cosine_comparison_model import (
    CenterPixelDensenet201CosineComparisonModel,
)
from deeplearning.comparison.models.center_pixel_multi_resolution_cosine_comparison_model import (
    CenterPixelMultiResolutionCosineComparisonModel,
)
from deeplearning.comparison.models.learnable_reduction_densenet_201_cosine_comparison_model import (
    LearnableReductionDensenet201CosineComparisonModel,
)
from deeplearning.comparison.models.learnable_reduction_multi_resolution_cosine_comparison_model import (
    LearnableReductionMultiResolutionCosineComparisonModel,
)
from deeplearning.comparison.models.utils import ComparisonModelBase

MODELS_DICT: Dict[str, Type[ComparisonModelBase]] = {
    "CenterPixelDensenet201CosineComparisonModel": CenterPixelDensenet201CosineComparisonModel,
    "LearnableReductionDensenet201CosineComparisonModel": LearnableReductionDensenet201CosineComparisonModel,
    "LearnableReductionDensenet201CosineComparisonModel2048": cast(
        Type[ComparisonModelBase],
        functools.partial(LearnableReductionDensenet201CosineComparisonModel, output_dim=2048),
    ),
    "LearnableReductionDensenet201CosineComparisonModel1024": cast(
        Type[ComparisonModelBase],
        functools.partial(LearnableReductionDensenet201CosineComparisonModel, output_dim=1024),
    ),
    "LearnableReductionDensenet201CosineComparisonModel512": cast(
        Type[ComparisonModelBase], functools.partial(LearnableReductionDensenet201CosineComparisonModel, output_dim=512)
    ),
    "LearnableReductionMultiResolutionCosineComparisonModel": LearnableReductionMultiResolutionCosineComparisonModel,
    "CenterPixelMultiResolutionCosineComparisonModel": CenterPixelMultiResolutionCosineComparisonModel,
}

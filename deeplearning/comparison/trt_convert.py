from typing import cast

import fire
import tensorrt as trt
import torch
import torchvision.transforms.functional as TF
from torch2trt import torch2trt

from deeplearning.comparison.datasets import ComparisonDatasets, transform_batch
from deeplearning.comparison.models_dict import MODELS_DICT
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.pytorch import load_pytorch_model, peek_pytorch_metadata
from deeplearning.model_io.tensorrt import save_tensorrt_model

MAX_BATCH_SIZE = 24


class TrtConvert(object):
    def __init__(self, datasets: ComparisonDatasets, metadata: ModelMetadata, model: torch.nn.Module,) -> None:
        self._datasets: ComparisonDatasets = datasets
        self._ckpt_metadata = metadata
        self._model = model

    def load_model(self, checkpoint: str) -> "TrtConvert":
        # Grab metadata
        self._ckpt_metadata = peek_pytorch_metadata(checkpoint)
        assert self._ckpt_metadata.model_class is not None
        template_model = MODELS_DICT[self._ckpt_metadata.model_class]()
        self._model, _ = load_pytorch_model(template_model, checkpoint)
        return self

    def convert(self, max_batch_size: int, save_to: str, calibration_epochs: int = 1,) -> None:  # noqa: C901
        assert self._model is not None
        assert self._ckpt_metadata is not None
        self._model.eval().cuda()

        datapoint = self._datasets.get_calibration()[0]
        datapoint.image_one.set_image(TF.to_tensor(datapoint.image_one.image))
        datapoint.image_two.set_image(TF.to_tensor(datapoint.image_two.image))
        datapoints = transform_batch([datapoint], self._datasets.get_calibration())

        input_data_image = (
            torch.unsqueeze(cast(torch.Tensor, datapoints[0].image_one.image), 0).cuda().repeat(max_batch_size, 1, 1, 1)
        )

        # Run the conversion.
        with torch.no_grad():
            trt_model = torch2trt(
                self._model,
                [input_data_image],
                fp16_mode=False,
                int8_mode=False,
                max_batch_size=max_batch_size,
                max_workspace_size=1 << 27,
                log_level=trt.Logger.INFO,
                strict_type_constraints=False,
                use_implicit_batch_dimension=False,
            )

        del self._model

        # Save the resulting converted model.
        torch_dtype = torch.float32
        metadata = (
            self._ckpt_metadata.with_input_dtype(torch_dtype)
            .with_input_size((input_data_image.shape[3], input_data_image.shape[2]))
            .with_max_batch_size(max_batch_size)
        )
        save_tensorrt_model(trt_model, metadata, save_to)


if __name__ == "__main__":
    fire.Fire(TrtConvert)

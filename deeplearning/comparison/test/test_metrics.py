import torch

from deeplearning.comparison.data_utils import get_size_bucket
from deeplearning.comparison.metrics import AverageMetrics, Metrics

EPS = 1e-5

CLASSES = [("a", "b"), ("a", "a"), ("b", "a"), ("b", "b"), ("a", "c"), ("c", "a"), ("b", "c")]
SIZES = [(1.5, 2.5), (3.5, 5), (4.5, 6), (6, 4.5), (9, 20), (20, 10), (22, 18)]


def test_metrics_precision() -> None:
    prediction = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]

    metrics = Metrics(
        classes=CLASSES, sizes=SIZES, out=torch.tensor(target), out_hat=torch.tensor(prediction), loss=torch.tensor(0.0)
    )

    expected_precision = 2.0 / 3.0

    assert torch.abs(metrics.compute_precision() - expected_precision) < EPS


def test_metrics_recall() -> None:
    prediction = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]

    metrics = Metrics(
        classes=CLASSES, sizes=SIZES, out=torch.tensor(target), out_hat=torch.tensor(prediction), loss=torch.tensor(0.0)
    )

    expected_recall = 2.0 / 4.0

    assert torch.abs(metrics.compute_recall() - expected_recall) < EPS


def test_metrics_f1() -> None:
    prediction = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]

    metrics = Metrics(
        classes=CLASSES, sizes=SIZES, out=torch.tensor(target), out_hat=torch.tensor(prediction), loss=torch.tensor(0.0)
    )

    expected_f1 = 24.0 / 42.0

    assert torch.abs(metrics.compute_f1() - expected_f1) < EPS


def test_average_metrics_global_precision() -> None:
    prediction1 = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.32, 0.55, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])

    expected_precision = 4 / 7

    assert torch.abs(avg_metrics.compute_global_precision() - expected_precision) < EPS


def test_average_metrics_global_recall() -> None:
    prediction1 = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.32, 0.55, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])

    expected_recall = 4 / 7

    assert torch.abs(avg_metrics.compute_global_recall() - expected_recall) < EPS


def test_average_metrics_global_f1() -> None:
    prediction1 = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.32, 0.55, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])

    expected_f1 = 4 / 7

    assert torch.abs(avg_metrics.compute_global_f1() - expected_f1) < EPS


def test_get_size_bucket() -> None:
    values = [0.5, 1.5, 2.5, 3.5, 5.5, 8.5, 13, 1000]
    actual_results = []
    for val in values:
        actual_results.append(get_size_bucket(val))

    assert actual_results == [0, 1, 2, 3, 5, 8, 13, 233]


def test_compute_class_performance_components() -> None:
    prediction1 = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.32, 0.55, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])

    class_performance_components, _ = avg_metrics.compute_class_performance_components()
    assert len(class_performance_components) == 5
    assert abs(class_performance_components[("a", "b")]["recall"] - 1.0) < EPS
    assert abs(class_performance_components[("a", "b")]["precision"] - 2 / 3) < EPS
    assert abs(class_performance_components[("a", "b")]["accuracy"] - 3 / 4) < EPS
    assert abs(class_performance_components[("a", "b")]["f1"] - (4 / 5)) < EPS
    assert class_performance_components[("a", "b")]["count"] == 4


def test_compute_size_performance_components() -> None:
    prediction1 = [0.56, 0.32, 0.22, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.32, 0.55, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])

    class_performance_components = avg_metrics.compute_size_performance_components()
    assert len(class_performance_components) == 4
    assert abs(class_performance_components[("8", "13")]["recall"] - 0.0) < EPS
    assert abs(class_performance_components[("8", "13")]["precision"] - 0.0) < EPS
    assert abs(class_performance_components[("8", "13")]["accuracy"] - 1 / 4) < EPS
    assert abs(class_performance_components[("8", "13")]["f1"] - 0.0) < EPS
    assert class_performance_components[("8", "13")]["count"] == 4


def test_get_percentage_of_positive_targets_in_range() -> None:
    prediction1 = [0.56, 0.32, 0.9, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.95, 0.99, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])
    percentage = avg_metrics.get_percentage_of_positive_targets_in_range(0.9, 1.0)

    assert abs(percentage - 3 / 7) < EPS


def test_get_percentage_of_negative_targets_in_range() -> None:
    prediction1 = [0.56, 0.32, 0.9, 0.49, 0.51, 0.01, 0.98]
    target1 = [1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0]
    metrics1 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target1),
        out_hat=torch.tensor(prediction1),
        loss=torch.tensor(0.0),
    )

    prediction2 = [0.66, 0.95, 0.99, 0.49, 0.51, 0.43, 0.98]
    target2 = [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]
    metrics2 = Metrics(
        classes=CLASSES,
        sizes=SIZES,
        out=torch.tensor(target2),
        out_hat=torch.tensor(prediction2),
        loss=torch.tensor(0.0),
    )

    avg_metrics = AverageMetrics(metrics=[metrics1, metrics2])
    percentage = avg_metrics.get_percentage_of_negative_targets_in_range(0.0, 0.5)
    assert abs(percentage - 3 / 7) < EPS

import torch

from deeplearning.comparison.loss import contrastive_loss


def test_contrastive_loss() -> None:
    out_hat = torch.Tensor([0.0, 1.0, 20.0])
    out_target = torch.Tensor([1.0, 1.0, 1.0])

    loss = contrastive_loss(out_hat, out_target, margin=10, aggregation_fn=torch.mean)
    expected_loss = 401 / 3

    assert abs(loss - expected_loss) < 1e-6

    out_hat = torch.Tensor([0.0, 1.0, 20.0])
    out_target = torch.Tensor([0.0, 0.0, 0.0])

    loss = contrastive_loss(out_hat, out_target, margin=10, aggregation_fn=torch.mean)
    expected_loss = 181 / 3

    assert abs(loss - expected_loss) < 1e-6

    out_hat = torch.Tensor([0.0, 1.0, 300.0])
    out_target = torch.Tensor([1.0, 1.0, 0.0])

    loss = contrastive_loss(out_hat, out_target, margin=10, aggregation_fn=torch.mean)
    expected_loss = 1 / 3

    assert abs(loss - expected_loss) < 1e-6

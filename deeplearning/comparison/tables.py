from typing import Any, Dict, List, Optional
from uuid import uuid4

from sqlalchemy import <PERSON>SO<PERSON>, BigInteger, Boolean, Column, Float, Foreign<PERSON>ey, Integer, String
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class ComparisonLabels(Base):  # type: ignore
    __tablename__ = "comparison_labels"
    id = Column(String, primary_key=True)
    created = Column(BigInteger)
    match = Column(Boolean, nullable=True)
    valid = Column(Boolean)
    image_one = Column(String)
    label_one = Column(String)
    label_one_category = Column(String)
    label_one_x = Column(Float)
    label_one_y = Column(Float)
    label_one_radius = Column(Float)
    image_two = Column(String)
    label_two = Column(String)
    label_two_category = Column(String)
    label_two_x = Column(Float)
    label_two_y = Column(Float)
    label_two_radius = Column(Float)
    user = Column(String)
    source = Column(String)

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


class ComparisonReviews(Base):  # type: ignore
    __tablename__ = "comparison_reviews"
    id = Column(String, primary_key=True)
    created = Column(BigInteger)
    label_id = Column(String, ForeignKey("comparison_labels.id"))
    user = Column(String)
    valid = Column(Boolean)


class ComparisonEmbeddings(Base):  # type: ignore
    __tablename__ = "comparison_embeddings"
    id = Column(Integer, primary_key=True, autoincrement=True)
    image_id = Column(String, index=True)
    x = Column(Float)
    y = Column(Float)
    radius = Column(Float)
    category = Column(String)
    label_id = Column(String)
    epoch = Column(String)
    embedding = Column(JSON)
    geohash = Column(String)
    uuid = Column(String)

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


class ComparisonPredictionAndLabel(Base):  # type: ignore
    __tablename__ = "comparison_predictions_and_labels"
    id = Column(String, primary_key=True)
    prediction = Column(Float, nullable=False)
    target = Column(Float, nullable=False)


class ComparisonEmbeddingObject:
    def __init__(
        self,
        image_id: str,
        x: float,
        y: float,
        radius: float,
        category: str,
        epoch: str,
        embedding: List[Any],
        geohash: Optional[str],
        filepath: Optional[str] = None,
        uuid: Optional[str] = None,
    ) -> None:
        self.image_id = image_id
        self.x = x
        self.y = y
        self.radius = radius
        self.category = category
        self.epoch = epoch
        self.embedding = embedding
        self.geohash = geohash
        self.filepath = filepath
        self.uuid = uuid if uuid is not None else str(uuid4())

    def to_dict(self) -> Dict[str, Any]:
        return {
            "image_id": self.image_id,
            "x": self.x,
            "y": self.y,
            "radius": self.radius,
            "category": self.category,
            "epoch": self.epoch,
            "embedding": self.embedding,
            "geohash": self.geohash,
            "filepath": self.filepath,
            "uuid": self.uuid,
        }

    @staticmethod
    def from_dict(dict: Dict[str, Any]) -> "ComparisonEmbeddingObject":
        return ComparisonEmbeddingObject(
            image_id=dict["image_id"],
            x=dict["x"],
            y=dict["y"],
            radius=dict["radius"],
            category=dict["category"],
            epoch=dict["epoch"],
            embedding=dict["embedding"],
            geohash=dict["geohash"],
            filepath=dict.get("filepath"),
            uuid=dict.get("uuid"),
        )

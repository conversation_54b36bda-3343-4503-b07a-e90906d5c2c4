from collections import defaultdict
import functools
import logging
import random
from typing import Any, Callable, Dict, List, Optional, Tuple
from deeplearning.comparison.data_utils import save_image_level_embedding_files_to_torch
from deeplearning.comparison.evaluator.config import ComparisonEvaluatorConfig
from deeplearning.comparison.models.utils import ComparisonEmbeddingOutputFactory
from deeplearning.deepweed.model import Deepweed
from deeplearning.deepweed.remote_veselka_dataset.remote_datasets import RemoteVeselkaDatasets
from deeplearning.deepweed.trainer import DATASET_TYPES
from deeplearning.model_io.tensorrt import load_tensorrt_model
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.images import crop_or_pad, denormalize_image
from deeplearning.utils.trainer import Trainer, TrainingModule, worker_init_fn
from deeplearning.deepweed.dataset_transforms import PADDING
from lib.common.perf.perf_categories import PerfCategory
from lib.common.perf.perf_tracker import duration_perf_recorder_decorator
import torch
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>, Sam<PERSON>
from deeplearning.deepweed.datasets_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, DeepweedDatapoint
from torchvision.utils import save_image


class ComparisonEvaluatorModule(TrainingModule):
    def __init__(
        self,
        config: ComparisonEvaluatorConfig,
        datasets: RemoteVeselkaDatasets,
        trt_file_path: str,
    ):
        super().__init__()
        self.config = config
        self.datasets = datasets
        self.trt_model, self.trt_metadata = load_tensorrt_model(trt_file_path)
        
    def export_metadata(self):
        return self.trt_metadata
    
    def export_model(self):
        return self.trt_model
    
    def export_torch_script(self):
        return None
    
    def set_exp_dir(self, dir: str):
        pass
    
    def set_experiment_url(self, dir: str):
        pass
    
    @property
    def use_cases(self):
        return None
    
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def forward(self, comparison_input: torch.Tensor) -> torch.Tensor:
        reduction = ComparisonEmbeddingOutputFactory.unpack(self.trt_model(comparison_input)).out
        return reduction
        
        
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def test_dataloader(self) -> "DataLoader[DeepweedDatapoint]": 
        dataset = self.datasets.get_bare_transform_dataset()
        sampler: Sampler[DeepweedDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank(), drop_last=True
        )
        return DataLoader(
            dataset,
            sampler=sampler,
            collate_fn=self.collate_fn,
            pin_memory=False,
            num_workers=self.config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
            batch_size=1
        )
        
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def get_worker_init_fn(self) -> Callable[[int], None]:
        num_workers = self.config.num_workers
        return functools.partial(worker_init_fn, num_workers)
    
    @staticmethod
    def collate_fn(data: List[DeepweedDatapoint],) -> Any:
        if type(data[0]) is torch.Tensor:
            return data

        if data[0].target is None:
            return data

        targets = []
        for datapoint in data:
            assert datapoint.target is not None, "Datapoint missing targets"
            targets.append(datapoint.target)

        batch = (
            torch.stack([datapoint.image for datapoint in data]).cuda(),
            DatasetLabel.stack(targets),
            [datapoint.filepath for datapoint in data],
            [datapoint.image_meta for datapoint in data]
        )

        return batch
    
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    @torch.no_grad()
    def gpu_transforms(
        self, batch: List[DeepweedDatapoint]
    ) -> Tuple[List[DeepweedDatapoint], List[List[Dict[str, Any]]]]:
        dataset = self.datasets.get_bare_transform_dataset()

        transforms_batch_list = []

        for datapoint in batch:
            image = datapoint.image.cuda()
            datapoint.enabled_hits

            assert datapoint.target_list is not None, "Datapoint is missing target list."

            target_list = [x if x is None else x.cuda() for x in datapoint.target_list]

            with torch.autocast(device_type="cuda", enabled=False):
                image, target, transforms = dataset.preprocess_datapoint(
                    image,
                    target_list,
                    datapoint.points,
                    datapoint.image_meta,
                    set(
                        [x for i, x in enumerate(dataset.weed_classes) if datapoint.enabled_weed_point_classes[i] == 1]
                    ),
                    set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
                    datapoint.enabled_segm_classes,
                    datapoint.enabled_embedding_bucket,
                    previous_transforms_list=None,
                )
            datapoint.image = image
            datapoint.target = target
            transforms_batch_list.append(transforms)

        return batch, transforms_batch_list
        
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    @torch.no_grad()
    def test_step(self, batch: Any, batch_nb: int):
        # For each image, crop out items and run comparison trt model
        preprocessed_batch, _ = self.gpu_transforms(batch)
        
        images, targets, filepaths, image_metadata = self.collate_fn(preprocessed_batch)
        
        for batch_ind in range(len(filepaths)):
            image = images[batch_ind]
            target_points = targets.points[batch_ind]
            if len(target_points) == 0:
                continue
            cropped_images = []
            image_embeddings = []
            points_to_save = []
            for i in range(0, len(target_points), self.trt_metadata.max_batch_size):
                cropped_images = []
                for j in range(i, min(i + self.trt_metadata.max_batch_size, len(target_points))):
                    point = target_points[j]
                    if point.confidence == 0 or (point.x < 0 or point.x > image.shape[-1]) or (point.y < 0 or point.y > image.shape[-2]):
                        continue
                    try:
                        cropped_image, _, _ = crop_or_pad(image, (point.x, point.y), self.trt_metadata.input_size)
                        if random.random() < 0.01:
                            save_image(denormalize_image(cropped_image), f"/data/deeplearning/saved_sample_images/{image_metadata[batch_ind].image_id}_{point.x}_{point.y}.png")
                    except Exception as e:
                        logging.warning(f"Exception cropping image: {e}")
                        logging.warning(f"RAVEN failed {point.x} {point.y} {image_metadata[batch_ind].image_id} {image.shape}")
                        raise e
                    cropped_images.append(cropped_image)
                    points_to_save.append(point)
                
                if len(cropped_images) == 0:
                    logging.warning("RAVEN no cropped images")
                    continue
                 
                if len(cropped_images) < self.trt_metadata.max_batch_size:
                    try:
                        cropped_images += [torch.ones_like(cropped_images[0])] * (self.trt_metadata.max_batch_size - len(cropped_images))
                    except Exception as e:
                        logging.warning(f"RAVEN failed {len(cropped_images)}")
                        raise e
                cropped_images = torch.stack(cropped_images).cuda()
                embeddings = self.forward(cropped_images)
                # logging.warning(f"Embeddings shape {embeddings.shape}")
                image_embeddings.append(embeddings)
            # for point in target_points: # For each point in target.points, append the cropped image
            #     cropped_image, _, _ = crop_or_pad(image, (point.x, point.y), self.trt_metadata.input_size)
            #     cropped_images.append(cropped_image) 
            # image_embeddings = []
            # for i in range(0, len(cropped_images), self.trt_metadata.max_batch_size):
            #     comparison_batch = cropped_images[i : i + self.trt_metadata.max_batch_size]
            #     if len(comparison_batch) < self.trt_metadata.max_batch_size:
            #         comparison_batch += [torch.ones_like(comparison_batch[0])] * (self.trt_metadata.max_batch_size - len(comparison_batch))
            #     comparison_batch = torch.stack(comparison_batch).cuda()
            #     embeddings = self.forward(comparison_batch)
            #     image_embeddings.append(embeddings)
            
            if len(image_embeddings) == 0:
                logging.warning(f"RAVE no image embeddings")
                continue
            cat_image_embeddings = torch.cat(image_embeddings)
            
            embeddings_data = defaultdict(list)
            image_id = image_metadata[batch_ind].image_id
                        
            for point_ind, point in enumerate(points_to_save):
                try:
                    point_embedding = cat_image_embeddings[point_ind]
                except Exception as e:
                    logging.warning(f"RAVEN Exception getting embedding: {cat_image_embeddings.shape} {point_ind}")
                    raise e
                point_embedding_data = {
                    "image_id": image_id,
                    "x": point.x - PADDING,
                    "y": point.y - PADDING,
                    "radius": point.r,
                    "category": point.clz,
                    "label_id": "",
                    "epoch": "eval",
                    "image_url": filepaths[batch_ind],
                    "model_id": self.config.model_id,
                    "geohash": image_metadata[batch_ind].geohash,
                    "captured_at": image_metadata[batch_ind].timestamp_ms,
                    "crop_id": image_metadata[batch_ind].crop_id,
                    "uuid": point.uuid,
                }
                embeddings_data[image_id].append((point_embedding_data, point_embedding.squeeze().squeeze().tolist()))
            
            
            save_image_level_embedding_files_to_torch(embeddings_data=embeddings_data, dir=self.config.save_dir)
                
            
class ComparisonEvaluator(Trainer):
    def __init__(self) -> None:
        super().__init__()
        self._datasets: Optional[RemoteVeselkaDatasets] = None
        
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.deterministic = False
        torch.set_float32_matmul_precision("medium")

    def __enter__(self) -> "ComparisonEvaluator":
        return self

    def __exit__(self, exc_type: Any, exc_value: Any, traceback: Any) -> None:
        return
    
    def shutdown(self) -> None:
        if self._remote_dataset_process:
            self._remote_dataset_process.terminate()
            self._remote_dataset_process = None
    
    def evaluate(
        self,
        datasets: RemoteVeselkaDatasets,
        config: ComparisonEvaluatorConfig,
        trt_file_path: str,
    ) -> None:
        self._datasets = datasets
        self.module = ComparisonEvaluatorModule(
            config=config,
            datasets=datasets,
            trt_file_path=trt_file_path,
        )
        super()._infer(self.module, fast_run=config.fast_run)

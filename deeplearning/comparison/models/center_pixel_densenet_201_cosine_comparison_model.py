from typing import Any, Optional, <PERSON><PERSON>

import torch
from torch import nn
from torchvision.models._utils import IntermediateLayerGetter
from torchvision.models.densenet import densenet201

from deeplearning.comparison.models.utils import ComparisonEmbeddingOutput, ComparisonModelBase, ComparisonModelOutput
from deeplearning.comparison.version import get_version

EPS = 1e-5


class CenterPixelDensenet201CosineComparisonModel(ComparisonModelBase):
    def __init__(self, pretrained_model: Optional[str] = None, batch_norm: Any = nn.BatchNorm2d) -> None:
        super().__init__(pretrained_model, batch_norm)
        self.eps = EPS
        self.version = get_version()

        self.backbone1 = IntermediateLayerGetter(densenet201(pretrained=True), return_layers={"features": "out"},)
        self.convs1 = nn.Sequential(
            nn.Conv2d(1920, 1920, 5, padding=3, stride=2, bias=False, groups=128),
            batch_norm(1920, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(1920, 4096, 5, padding=3, stride=2, bias=False, groups=128),
            batch_norm(4096, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(4096, 4096, 3, padding=1, bias=False, groups=1024),
        )

        self.cosine_sim = nn.CosineSimilarity(dim=1)

        # Softplus is a smooth approximation of relu
        self.softplus = nn.Softplus(beta=20, threshold=2)

    def forward(self, image: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        assert image.shape[2] == image.shape[3], f"Expected square images with same shape, got: {image.shape}"

        image_bb = self.backbone1(image)["out"]
        image_conved = self.convs1(image_bb)

        x, y = image_conved.shape[-1] // 2, image_conved.shape[-2] // 2

        center: torch.Tensor = image_conved[:, :, y, x]
        return ComparisonEmbeddingOutput(version=self.version, out=center).pack()

    def forward_similarity(
        self, embedding_one: torch.Tensor, embedding_two: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        assert (
            embedding_one.shape == embedding_two.shape
        ), f"Expected two embeddings with the same shape, got: {embedding_one.shape} and {embedding_two.shape}"
        cosine_sim = self.cosine_sim(embedding_one, embedding_two)

        out = self.softplus(cosine_sim)

        output = ComparisonModelOutput(version=self.version, out=out,)
        return output.pack()

import abc
from typing import Any, Optional, Tu<PERSON>

import torch


class ComparisonEmbeddingOutput:
    def __init__(self, version: int, out: torch.Tensor):
        self.version = version
        self.out = out

    def pack(self,) -> Tuple[torch.Tensor, torch.Tensor]:
        return (
            torch.tensor([self.version], dtype=torch.int32, device=self.out.device),  # version
            self.out,
        )

    @property
    # TODO: JIT fails with unknown type torch.device
    # def device(self) -> torch.device:
    def device(self) -> str:
        return str(self.out.device)


class ComparisonEmbeddingOutputFactory:
    @staticmethod
    def unpack(input: Tuple[Any, ...]) -> ComparisonEmbeddingOutput:
        version, out = input
        return ComparisonEmbeddingOutput(version, out)


class ComparisonModelOutput:
    def __init__(self, version: int, out: torch.Tensor):
        self.version = version
        self.out = out

    def pack(self,) -> Tuple[torch.Tensor, torch.Tensor]:
        return (
            torch.tensor([self.version], dtype=torch.int32, device=self.out.device),  # version
            self.out,
        )

    @property
    # TODO: JIT fails with unknown type torch.device
    # def device(self) -> torch.device:
    def device(self) -> str:
        return str(self.out.device)


class ComparisonModelOutputFactory:
    @staticmethod
    def unpack(input: Tuple[Any, ...]) -> ComparisonModelOutput:
        version, out = input
        return ComparisonModelOutput(version, out)


class ComparisonModelBase(torch.nn.Module):
    def __init__(self, pretrained_model: Optional[str] = None, batch_norm: Any = torch.nn.BatchNorm2d) -> None:
        super().__init__()
        self._pretrained_model = pretrained_model
        self._batch_norm = batch_norm
        self.match_is_above_threshold = True

    @abc.abstractmethod
    def forward(self, image: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        pass

    @abc.abstractmethod
    def forward_similarity(
        self, embedding_one: torch.Tensor, embedding_two: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        pass

from typing import Any, Optional, <PERSON><PERSON>

import torch
from torch import nn
from torchvision.models._utils import IntermediateLayerGetter
from torchvision.models.resnet import resnet50

from deeplearning.comparison.models.utils import ComparisonEmbeddingOutput, ComparisonModelBase, ComparisonModelOutput
from deeplearning.comparison.version import get_version
from deeplearning.trt_extensions.upsample import Upsample

EPS = 1e-5


class LearnableReductionMultiResolutionCosineComparisonModel(ComparisonModelBase):
    def __init__(self, pretrained_model: Optional[str] = None, batch_norm: Any = nn.BatchNorm2d) -> None:
        super().__init__(pretrained_model, batch_norm)
        self.eps = EPS
        self.version = get_version()
        if pretrained_model is not None:
            ckpt = torch.load(pretrained_model, map_location=torch.device("cpu"))
            state_dict = ckpt["state_dict"]
            new_state_dict = {}
            for key in state_dict:
                if key.startswith("backbone_point"):
                    new_state_dict[key[len("backbone_point.") :]] = state_dict[key]
        self.backbone1 = IntermediateLayerGetter(
            resnet50(pretrained=True, norm_layer=batch_norm), return_layers={"layer1": "out1", "layer3": "out3"},
        )
        if pretrained_model is not None:
            self.backbone1.load_state_dict(new_state_dict)
        self.convs1 = nn.Sequential(
            nn.Conv2d(256, 2048, 5, padding=3, stride=2, bias=False, groups=256),
            batch_norm(2048, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(2048, 2048, 5, padding=3, stride=2, bias=False, groups=512),
            batch_norm(2048, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(2048, 2048, 5, padding=3, stride=2, bias=False, groups=512),
            batch_norm(2048, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(2048, 2048, 3, padding=1, bias=False, groups=1024),
        )
        self.convs3 = nn.Sequential(
            nn.Conv2d(1024, 2048, 5, padding=3, stride=2, bias=False, groups=256),
            batch_norm(2048, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(2048, 2048, 5, padding=3, stride=2, bias=False, groups=512),
            batch_norm(2048, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(2048, 2048, 5, padding=3, stride=2, bias=False, groups=512),
            batch_norm(2048, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(2048, 2048, 3, padding=1, bias=False, groups=1024),
        )

        self.blend_conv = nn.Sequential(
            nn.Conv2d(4096, 4096, 3, padding=1, stride=2, bias=False),
            batch_norm(4096, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(4096, 4096, 3, padding=1, stride=2, bias=False),
        )

        self.reduction = nn.Sequential(
            batch_norm(4096, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(4096, 4096, 4, padding=0, bias=False, groups=512),
        )

        self.cosine_sim = nn.CosineSimilarity(dim=1)
        self.upsample = Upsample()

        # Softplus is a smooth approximation of relu
        self.softplus = nn.Softplus(beta=20, threshold=2)

    def forward(self, image: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        assert image.shape[2] == image.shape[3], f"Expected square images with same shape, got: {image.shape}"

        image_bbs = self.backbone1(image)
        image_bb1 = image_bbs["out1"]
        image_conved_1 = self.convs1(image_bb1)
        image_bb3 = image_bbs["out3"]
        image_conved_3 = self.convs3(image_bb3)

        image_conved_3_upsampled = self.upsample(image_conved_3, size=image_conved_1.shape[-2:])
        image_conved = torch.cat([image_conved_1, image_conved_3_upsampled], dim=1)
        image_conved = self.blend_conv(image_conved)

        image_reduced: torch.Tensor = self.reduction(image_conved)
        return ComparisonEmbeddingOutput(version=self.version, out=image_reduced).pack()

    def forward_similarity(
        self, embedding_one: torch.Tensor, embedding_two: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        assert (
            embedding_one.shape == embedding_two.shape
        ), f"Expected two embeddings with the same shape, got: {embedding_one.shape} and {embedding_two.shape}"

        cosine_sim = self.cosine_sim(embedding_one, embedding_two)
        out = self.softplus(cosine_sim)

        output = ComparisonModelOutput(version=self.version, out=out,)
        return output.pack()

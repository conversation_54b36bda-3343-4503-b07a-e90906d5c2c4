import random
from typing import Any, Callable, List, Tuple, cast

import torch
import torchvision.transforms as T

from deeplearning.utils.images import _random_sample_around_one, random_gaussian_noise

Transform = Callable[[torch.Tensor], torch.Tensor]


class RandomChoice(T.RandomChoice):
    def __init__(self, augmentations: List[Transform]) -> None:
        self._augmentations = augmentations

    def __call__(self, *args: Any, **kwargs: Any) -> torch.Tensor:
        augmentation = random.choice(self._augmentations)
        return augmentation(*args, **kwargs)


class Compose(T.Compose):
    def __call__(self, input: torch.Tensor,) -> torch.Tensor:
        for t in self.transforms:
            input = t(input)
        return input


class Normalize(T.Normalize):
    def __call__(self, input: torch.Tensor) -> torch.Tensor:
        assert input.shape[0] == 3, f"Expected CHW/(C+D)HW order: {input.shape}"

        normalized_image: torch.Tensor = super().__call__(input)

        return normalized_image


class RandomRotation:
    def __init__(self, border_mode: int = 0) -> None:
        self.border_mode = border_mode

    def __call__(self, input: torch.Tensor,) -> torch.Tensor:
        angle = random.uniform(-360, 360)
        warped_input: torch.Tensor = T.functional.rotate(
            input, angle, interpolation=T.InterpolationMode.BILINEAR, fill=self.border_mode
        )

        return warped_input


class ColorJitter(T.ColorJitter):
    def __init__(
        self,
        brightness: Tuple[float, float],
        saturation: Tuple[float, float],
        hue: Tuple[float, float],
        gamma: Tuple[float, float],
    ):
        self.brightness = brightness
        self.saturation = saturation
        self.hue = hue
        self.gamma = gamma
        super().__init__(brightness=self.brightness, hue=self.hue, saturation=self.saturation)

    def __call__(self, input: torch.Tensor) -> torch.Tensor:
        jittered_input = super().__call__(input)
        jittered_input = jittered_input ** _random_sample_around_one(*self.gamma)
        return cast(torch.Tensor, jittered_input)


class GaussianNoise:
    def __init__(self, stddev: float) -> None:
        self.stddev = stddev

    def __call__(self, input: torch.Tensor,) -> torch.Tensor:
        return random_gaussian_noise(input, self.stddev)[0]


class RandomVerticalFlip:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(self, input: torch.Tensor,) -> torch.Tensor:
        if random.random() < self.p:
            input = torch.flip(input, dims=[-2])
        return input


class RandomHorizontalFlip:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(self, input: torch.Tensor,) -> torch.Tensor:
        if random.random() < self.p:
            input = torch.flip(input, dims=[-1])
        return input


class GaussianBlur(T.GaussianBlur):
    def __init__(self, kernel_size: float, stddev: Tuple[float, float]) -> None:
        self.stddev = stddev
        self.kernel_size = kernel_size
        super().__init__(self.kernel_size, self.stddev)

    def __call__(self, input: torch.Tensor,) -> torch.Tensor:
        return cast(torch.Tensor, super().__call__(input))

import math
import os
from typing import Any, Dict, List, Optional, cast

import pandas
import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.metrics.metrics import Metrics
from deeplearning.deepweed.metrics.utils import global_ratio, mean_ratio
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass


class AverageMetrics(object):
    def __init__(
        self,
        metrics: List[Metrics],
        segm_enabled_classes: torch.Tensor,
        point_enabled_classes: torch.Tensor,
        point_enabled_hits: torch.Tensor,
        segm_classes: List[str],
        config: DeepweedConfig,
    ) -> None:
        assert all([m.loss.device.type == "cpu" for m in metrics]), "Metrics have not been .cpu()'d"
        self.config = config
        self.metrics = metrics
        self.segm_enabled_classes = segm_enabled_classes
        self.point_enabled_classes = point_enabled_classes
        self.point_enabled_hits = point_enabled_hits
        self.segm_classes = segm_classes

        predictions_dataframes = [
            metric.predictions_dataframe for metric in metrics if metric.predictions_dataframe is not None
        ]
        labels_dataframes = [metric.labels_dataframe for metric in metrics if metric.labels_dataframe is not None]

        if len(predictions_dataframes) > 0:
            self.predictions_dataframe = pandas.concat(predictions_dataframes)
        else:
            self.predictions_dataframe = None

        if len(predictions_dataframes) > 0:
            self.labels_dataframe = pandas.concat(labels_dataframes)
        else:
            self.predictions_dataframe = None

    def compute_avg_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.loss for m in self.metrics]).mean()

    def compute_avg_segm_dice_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.segm_dice_loss for m in self.metrics]).mean()

    def compute_avg_segm_bce_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.segm_bce_loss for m in self.metrics]).mean()

    def compute_avg_dots(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.dots for m in self.metrics]).mean()

    def compute_iou(self, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        func = mean_ratio if average else global_ratio

        if class_id is None:
            return func(
                torch.cat([m.intersection for m in self.metrics]),
                torch.cat([m.union for m in self.metrics]),
                self.segm_enabled_classes,
            )
        else:
            return func(
                torch.cat([m.intersection[:, class_id] for m in self.metrics]),
                torch.cat([m.union[:, class_id] for m in self.metrics]),
            )

    def compute_segm_precision(self, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        func = mean_ratio if average else global_ratio

        if class_id is None:
            return func(
                torch.cat([m.intersection for m in self.metrics]),
                torch.cat([m.input_sum for m in self.metrics]),
                self.segm_enabled_classes,
            )
        else:
            return func(
                torch.cat([m.intersection[:, class_id] for m in self.metrics]),
                torch.cat([m.input_sum[:, class_id] for m in self.metrics]),
            )

    def compute_segm_recall(self, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        func = mean_ratio if average else global_ratio

        if class_id is None:
            return func(
                torch.cat([m.intersection for m in self.metrics]),
                torch.cat([m.target_sum for m in self.metrics]),
                self.segm_enabled_classes,
            )
        else:
            return func(
                torch.cat([m.intersection[:, class_id] for m in self.metrics]),
                torch.cat([m.target_sum[:, class_id] for m in self.metrics]),
            )

    def compute_segm_f1(self, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        p = self.compute_segm_precision(class_id=class_id, average=average)
        r = self.compute_segm_recall(class_id=class_id, average=average)
        return 2 * p * r / (p + r)

    def compute_avg_point_weed_hit_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.point_weed_hit_loss for m in self.metrics]).mean()

    def compute_avg_point_crop_hit_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.point_crop_hit_loss for m in self.metrics]).mean()

    def compute_avg_point_plant_hit_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.point_plant_hit_loss for m in self.metrics]).mean()

    def compute_avg_point_offset_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.point_offset_loss for m in self.metrics]).mean()

    def compute_avg_point_size_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.point_size_loss for m in self.metrics]).mean()

    def compute_avg_point_category_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.point_category_loss for m in self.metrics]).mean()

    def compute_avg_embedding_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.embedding_loss for m in self.metrics]).mean()

    def compute_avg_nonzero_embedding_loss(self) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        embedding_losses = torch.stack([m.embedding_loss for m in self.metrics])

        return embedding_losses[embedding_losses.nonzero(as_tuple=True)].mean()

    def compute_avg_weighted_losses(self, loss_term: str) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([getattr(m, loss_term) for m in self.metrics]).mean()

    def compute_point_hit_precision(self, index: int, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        func = mean_ratio if average else global_ratio

        if class_id is None:
            return func(
                torch.cat([m.point_hit_f1_overlaps[index] for m in self.metrics]),
                torch.cat([m.point_hit_f1_inputs[index] for m in self.metrics]),
                self.point_enabled_hits,
            )
        else:
            return func(
                torch.cat([m.point_hit_f1_overlaps[index][:, class_id] for m in self.metrics]),
                torch.cat([m.point_hit_f1_inputs[index][:, class_id] for m in self.metrics]),
            )

    def compute_point_hit_recall(self, index: int, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        func = mean_ratio if average else global_ratio

        if class_id is None:
            return func(
                torch.cat([m.point_hit_f1_overlaps[index] for m in self.metrics]),
                torch.cat([m.point_hit_f1_targets[index] for m in self.metrics]),
                self.point_enabled_hits,
            )
        else:
            return func(
                torch.cat([m.point_hit_f1_overlaps[index][:, class_id] for m in self.metrics]),
                torch.cat([m.point_hit_f1_targets[index][:, class_id] for m in self.metrics]),
            )

    def compute_point_hit_f1(self, index: int, average: bool, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        p = self.compute_point_hit_precision(index, class_id=class_id, average=average)
        r = self.compute_point_hit_recall(index, class_id=class_id, average=average)
        return 2 * p * r / (p + r)

    def compute_avg_point_offset_error(self, index: int, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.compute_point_offset_error(index, class_id) for m in self.metrics]).mean()

    def compute_avg_point_size_error(self, index: int, class_id: Optional[int] = None) -> torch.Tensor:
        if len(self.metrics) == 0:
            return torch.tensor(0.0)

        return torch.stack([m.compute_point_size_error(index, class_id) for m in self.metrics]).mean()

    def compute_segm_oec(self) -> torch.Tensor:
        """
        Compute evaluation criteria for segmentation.

        We use harmonic mean between mIOU and gIOU to compute OEC.
        If predictions collapse to 0, gIOU = 0.
        If predictions often sprinkle dots on images w/o object of interest, mIOU -> 0.

        Harmonic mean will balance out these concerns.
        """
        g = self.compute_iou(average=False)
        m = self.compute_iou(average=True)

        if math.isnan(g) or math.isnan(m):
            return torch.tensor(1.0)

        return 2 * g * m / (g + m)

    def get_dataframe(self) -> Optional[pandas.DataFrame]:
        df: Optional[pandas.DataFrame] = None
        if self.predictions_dataframe is not None and self.labels_dataframe is not None:
            self.predictions_dataframe["type"] = "prediction"
            self.labels_dataframe["type"] = "label"
            df = pandas.concat([self.predictions_dataframe, self.labels_dataframe])

        return df

    def save_dataframes_to_file(self, epoch: int, save_dir: str, test: bool = False) -> None:
        os.makedirs(save_dir, exist_ok=True)

        df = self.get_dataframe()

        if df is not None:
            if test:
                df.to_csv(os.path.join(save_dir, "dataframe.csv"), index=False)
                df.to_parquet(os.path.join(save_dir, "dataframe.parquet"), index=False)
            else:
                df.to_csv(os.path.join(save_dir, f"epoch_{epoch}_dataframe.csv"), index=False)
                df.to_parquet(os.path.join(save_dir, f"epoch_{epoch}_dataframe.parquet"), index=False)

    def compute_avg_distance(
        self, example_dists: List[torch.Tensor], example_counts: List[torch.Tensor]
    ) -> torch.Tensor:
        if len(example_dists) == 0:
            return torch.tensor(0.0)

        return torch.cat(example_dists).sum() / (torch.cat(example_counts).sum() + 1e-6)

    def compute_avg_positive_example_distance(self) -> torch.Tensor:
        pos_example_dists = [
            m.total_positive_example_distance for m in self.metrics if m.total_positive_example_distance is not None
        ]

        positive_examples = [m.total_positive_examples for m in self.metrics if m.total_positive_examples is not None]

        return self.compute_avg_distance(pos_example_dists, positive_examples)

    def compute_avg_negative_example_distance(self) -> torch.Tensor:
        neg_example_dists = [
            m.total_negative_example_distance for m in self.metrics if m.total_negative_example_distance is not None
        ]

        negative_examples = [m.total_negative_examples for m in self.metrics if m.total_negative_examples is not None]

        return self.compute_avg_distance(neg_example_dists, negative_examples)

    def compute_avg_example_distance(self) -> torch.Tensor:
        pos_example_dists = [
            m.total_positive_example_distance for m in self.metrics if m.total_positive_example_distance is not None
        ]
        neg_example_dists = [
            m.total_negative_example_distance for m in self.metrics if m.total_negative_example_distance is not None
        ]

        positive_examples = [m.total_positive_examples for m in self.metrics if m.total_positive_examples is not None]
        negative_examples = [m.total_negative_examples for m in self.metrics if m.total_negative_examples is not None]

        return self.compute_avg_distance(pos_example_dists + neg_example_dists, positive_examples + negative_examples)

    def compute_avg_positive_example_comparison(self) -> torch.Tensor:
        pos_example_dists = [
            m.total_positive_example_comparison for m in self.metrics if m.total_positive_example_comparison is not None
        ]

        positive_examples = [m.total_positive_examples for m in self.metrics if m.total_positive_examples is not None]

        return self.compute_avg_distance(pos_example_dists, positive_examples)

    def compute_avg_negative_example_comparison(self) -> torch.Tensor:
        neg_example_dists = [
            m.total_negative_example_comparison for m in self.metrics if m.total_negative_example_comparison is not None
        ]

        negative_examples = [m.total_negative_examples for m in self.metrics if m.total_negative_examples is not None]

        return self.compute_avg_distance(neg_example_dists, negative_examples)

    def compute_avg_example_comparison(self) -> torch.Tensor:
        pos_example_dists = [
            m.total_positive_example_comparison for m in self.metrics if m.total_positive_example_comparison is not None
        ]
        neg_example_dists = [
            m.total_negative_example_comparison for m in self.metrics if m.total_negative_example_comparison is not None
        ]

        positive_examples = [m.total_positive_examples for m in self.metrics if m.total_positive_examples is not None]
        negative_examples = [m.total_negative_examples for m in self.metrics if m.total_negative_examples is not None]

        return self.compute_avg_distance(pos_example_dists + neg_example_dists, positive_examples + negative_examples)

    def to_dict(self, prefix: str = "", **additional_metrics: Dict[str, Any]) -> Dict[str, Any]:  # noqa: C901
        d: Dict[str, Any] = dict()

        # Loss metrics
        d[f"avg_{prefix}loss"] = self.compute_avg_loss()

        d[f"avg_{prefix}segm_dice_loss"] = self.compute_avg_segm_dice_loss()
        d[f"avg_{prefix}segm_bce_loss"] = self.compute_avg_segm_bce_loss()

        d[f"avg_{prefix}point_weed_hit_loss"] = self.compute_avg_point_weed_hit_loss()
        d[f"avg_{prefix}point_crop_hit_loss"] = self.compute_avg_point_crop_hit_loss()
        d[f"avg_{prefix}point_plant_hit_loss"] = self.compute_avg_point_plant_hit_loss()
        d[f"avg_{prefix}point_offset_loss"] = self.compute_avg_point_offset_loss()
        d[f"avg_{prefix}point_size_loss"] = self.compute_avg_point_size_loss()
        d[f"avg_{prefix}point_category_loss"] = self.compute_avg_point_category_loss()

        weighted_loss_term_names = [
            "weighted_segm_dice_loss",
            "weighted_segm_bce_loss",
            "weighted_point_plant_hit_loss",
            "weighted_point_weed_hit_loss",
            "weighted_point_crop_hit_loss",
            "weighted_point_offset_loss",
            "weighted_point_size_loss",
            "weighted_point_category_loss",
            "weighted_embedding_loss",
        ]

        for weighted_loss_term_name in weighted_loss_term_names:
            d[f"avg_{prefix}{weighted_loss_term_name}"] = self.compute_avg_weighted_losses(weighted_loss_term_name)

        d[f"avg_{prefix}embedding_loss"] = self.compute_avg_embedding_loss()
        d[f"avg_{prefix}nonzero_embedding_loss"] = self.compute_avg_nonzero_embedding_loss()

        # Performance metrics
        d[f"avg_{prefix}dots"] = self.compute_avg_dots()

        for average in [False, True]:
            avg_str = "m" if average else "g"
            d[f"{prefix}{avg_str}iou"] = self.compute_iou(average=average)
            for i, segm_class_name in enumerate(self.segm_classes):
                if self.segm_enabled_classes[i]:
                    d[f"{prefix}{avg_str}iou_{segm_class_name}"] = self.compute_iou(class_id=i, average=average)

        for average in [False, True]:
            avg_str = "avg" if average else "global"
            d[f"{avg_str}_{prefix}segm_precision"] = self.compute_segm_precision(average=average)
            d[f"{avg_str}_{prefix}segm_recall"] = self.compute_segm_recall(average=average)
            d[f"{avg_str}_{prefix}segm_f1"] = self.compute_segm_f1(average=average)
            for i, segm_class_name in enumerate(self.segm_classes):
                if self.segm_enabled_classes[i]:
                    d[f"{avg_str}_{prefix}segm_precision_{segm_class_name}"] = self.compute_segm_precision(
                        class_id=i, average=average
                    )
                    d[f"{avg_str}_{prefix}segm_recall_{segm_class_name}"] = self.compute_segm_recall(
                        class_id=i, average=average
                    )
                    d[f"{avg_str}_{prefix}segm_f1_{segm_class_name}"] = self.compute_segm_f1(
                        class_id=i, average=average
                    )

        d[f"avg_{prefix}positive_example_distance"] = self.compute_avg_positive_example_distance()
        d[f"avg_{prefix}negative_example_distance"] = self.compute_avg_negative_example_distance()
        d[f"avg_{prefix}distance"] = self.compute_avg_example_distance()

        d[f"avg_{prefix}positive_example_comparison"] = self.compute_avg_positive_example_comparison()
        d[f"avg_{prefix}negative_example_comparison"] = self.compute_avg_negative_example_comparison()
        d[f"avg_{prefix}comparison"] = self.compute_avg_example_comparison()

        for (idx, downsample) in enumerate(self.config.point_downsample):
            for average in [False, True]:
                avg_str = "avg" if average else "global"
                d[f"{avg_str}_{prefix}point_hit_precision_{downsample}"] = self.compute_point_hit_precision(
                    idx, average=average
                )
                d[f"{avg_str}_{prefix}point_hit_recall_{downsample}"] = self.compute_point_hit_recall(
                    idx, average=average
                )
                d[f"{avg_str}_{prefix}point_hit_f1_{downsample}"] = self.compute_point_hit_f1(idx, average=average)
                if average:
                    d[f"{avg_str}_{prefix}point_offset_error_{downsample}"] = self.compute_avg_point_offset_error(idx)
                    d[f"{avg_str}_{prefix}point_size_error_{downsample}"] = self.compute_avg_point_size_error(idx)

        for category, class_name in {"crop": HitClass.CROP, "weed": HitClass.WEED, "plant": HitClass.PLANT}.items():
            class_id = cast(int, class_name)
            for (idx, downsample) in enumerate(self.config.point_downsample):
                for average in [False, True]:
                    avg_str = "avg" if average else "global"
                    d[
                        f"{avg_str}_{prefix}point_hit_precision_{downsample}_{category}"
                    ] = self.compute_point_hit_precision(idx, class_id=class_id, average=average)
                    d[f"{avg_str}_{prefix}point_hit_recall_{downsample}_{category}"] = self.compute_point_hit_recall(
                        idx, class_id=class_id, average=average
                    )
                    d[f"{avg_str}_{prefix}point_hit_f1_{downsample}_{category}"] = self.compute_point_hit_f1(
                        idx, class_id=class_id, average=average
                    )
                    if average:
                        d[
                            f"{avg_str}_{prefix}point_offset_error_{downsample}_{category}"
                        ] = self.compute_avg_point_offset_error(idx, class_id=class_id)
                        d[
                            f"{avg_str}_{prefix}point_size_error_{downsample}_{category}"
                        ] = self.compute_avg_point_size_error(idx, class_id=class_id)

        # overall evaluation criteria
        d[f"{prefix}segmentation_oec"] = self.compute_segm_oec()

        if len(self.metrics) > 0:
            d["learning_rate"] = self.metrics[0].learning_rate

        for key, value in additional_metrics.items():
            d[key] = value

        return d

import copy
import json
import os
from collections import defaultdict
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, cast

import pandas
import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.datasets_types import DatasetLabel
from deeplearning.deepweed.metadata import ImageMetadata, Point
from deeplearning.deepweed.metrics.utils import LOW_POINT_THRESHOLD, get_metric_components, mean_ratio
from deeplearning.deepweed.model import Deepweed, DeepweedOutput
from deeplearning.deepweed.point_utils import BEAM_RADIUS_IN, compute_points, f1_components, get_embedding_at_hit_point
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.embeddings.io import EmbeddingLookupTable
from deeplearning.utils.segm_utils import dots_count, iou_and_pr_components
from deeplearning.utils.tensor import recursive_detach, recursive_move, recursive_to_numpy, recursive_to_tensor
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue


def process_single_item(value: torch.Tensor, return_numerator_denominator: bool = False) -> torch.Tensor:
    if return_numerator_denominator:
        return torch.stack([value, torch.ones_like(value)])
    else:
        return value


class Metrics:
    def __init__(
        self,
        out: DeepweedOutput,
        out_hat: DeepweedOutput,
        out_label: DatasetLabel,
        segm_dice_loss: torch.Tensor,
        segm_bce_loss: torch.Tensor,
        segm_enabled_classes: torch.Tensor,
        point_weed_hit_loss: torch.Tensor,
        point_crop_hit_loss: torch.Tensor,
        point_plant_hit_loss: torch.Tensor,
        point_category_loss: torch.Tensor,
        point_offset_loss: torch.Tensor,
        point_size_loss: torch.Tensor,
        embedding_loss: torch.Tensor,
        point_enabled_classes: torch.Tensor,
        hit_enabled_classes: torch.Tensor,
        batch_enabled_weed_point_classes: torch.Tensor,
        model_ppi: Optional[float],
        discard_points_border_px: float,
        weed_point_classes: List[str],
        segm_classes: List[str],
        image_metadata: List[ImageMetadata],
        config: DeepweedConfig,
        segm_threshold: float = 0.5,
        thresholds: Optional[Dict["HitClassValue", float]] = None,
        use_confidence: bool = False,
        learning_rate: Optional[float] = None,
        compute_dataframes: Optional[bool] = False,
        filepaths: Optional[List[str]] = None,
        model_elapsed_time: Optional[float] = None,
        starting_pos: Optional[List[Tuple[float, float]]] = None,
        segm_dice_loss_weight: float = 1.0,
        segm_bce_loss_weight: float = 5.0,
        point_weed_hit_loss_weight: float = 1.0,
        point_crop_hit_loss_weight: float = 0.625,
        point_plant_hit_loss_weight: float = 1.0,
        point_category_loss_weight: float = 10.0,
        point_offset_loss_weight: float = 25.0,
        point_size_loss_weight: float = 3.125,
        embedding_loss_weight: float = 1.0,
        embeddings: Optional[torch.Tensor] = None,
        reduced_scaled_embeddings: Optional[torch.Tensor] = None,
        exp_dir: Optional[str] = None,
        total_positive_example_distance: Optional[torch.Tensor] = None,
        total_negative_example_distance: Optional[torch.Tensor] = None,
        total_positive_example_comparison: Optional[torch.Tensor] = None,
        total_negative_example_comparison: Optional[torch.Tensor] = None,
        total_positive_examples: Optional[torch.Tensor] = None,
        total_negative_examples: Optional[torch.Tensor] = None,
        save_embeddings_to_db: bool = False,
        for_driptape_model: bool = False,
        evaluation_height: int = 0,
        evaluation_width: int = 0,
        transpose_points: Optional[List[bool]] = None,
        embeddings_lookup_tables: Optional[Dict[EmbeddingType, EmbeddingLookupTable]] = None,
        save_embeddings_as_fp16: bool = False,
    ) -> None:
        thresholds = thresholds if thresholds is not None else {}
        transpose_points = transpose_points if transpose_points is not None else []
        embeddings_lookup_tables = embeddings_lookup_tables if embeddings_lookup_tables is not None else {}

        self.config = config
        self._transpose_points = transpose_points

        self.segm_dice_loss = segm_dice_loss
        self.segm_bce_loss = segm_bce_loss
        self.point_weed_hit_loss = point_weed_hit_loss
        self.point_crop_hit_loss = point_crop_hit_loss
        self.point_plant_hit_loss = point_plant_hit_loss
        self.point_category_loss = point_category_loss
        self.point_offset_loss = point_offset_loss
        self.point_size_loss = point_size_loss
        self.embedding_loss = embedding_loss

        self.weighted_segm_dice_loss = segm_dice_loss_weight * self.segm_dice_loss
        self.weighted_segm_bce_loss = segm_bce_loss_weight * self.segm_bce_loss

        self.weighted_point_plant_hit_loss = point_plant_hit_loss_weight * self.point_plant_hit_loss
        self.weighted_point_crop_hit_loss = point_crop_hit_loss_weight * self.point_crop_hit_loss
        self.weighted_point_weed_hit_loss = point_weed_hit_loss_weight * self.point_weed_hit_loss

        self.weighted_point_category_loss = point_category_loss_weight * self.point_category_loss
        self.weighted_point_offset_loss = point_offset_loss_weight * self.point_offset_loss
        self.weighted_point_size_loss = point_size_loss_weight * self.point_size_loss

        self.weighted_embedding_loss = embedding_loss_weight * self.embedding_loss

        self.loss = (
            self.weighted_segm_dice_loss
            + self.weighted_segm_bce_loss
            + self.weighted_point_weed_hit_loss
            + self.weighted_point_crop_hit_loss
            + self.weighted_point_plant_hit_loss
            + self.weighted_point_category_loss
            + self.weighted_point_offset_loss
            + self.weighted_point_size_loss
            + self.weighted_embedding_loss
        )

        self.total_positive_example_distance = total_positive_example_distance
        self.total_negative_example_distance = total_negative_example_distance
        self.total_positive_example_comparison = total_positive_example_comparison
        self.total_negative_example_comparison = total_negative_example_comparison
        self.total_positive_examples = total_positive_examples
        self.total_negative_examples = total_negative_examples

        self.segm_classes = segm_classes
        self.learning_rate = torch.tensor(learning_rate)
        self.compute_dataframes = compute_dataframes
        self.model_elapsed_time = model_elapsed_time
        self.predictions_dataframe = None
        self.labels_dataframe = None
        image_metadata_dict = (
            {image_meta.filepath: image_meta for image_meta in image_metadata} if image_metadata is not None else None
        )
        self.evaluation_height = evaluation_height
        self.evaluation_width = evaluation_width
        self.starting_pos = starting_pos

        with torch.no_grad():
            # segmentation
            self.segm_enabled_classes = segm_enabled_classes
            iou_pr_components = iou_and_pr_components(out_hat.mask, out.mask, threshold=segm_threshold)
            self.intersection = iou_pr_components["intersection"]
            self.union = iou_pr_components["union"]
            self.input_sum = iou_pr_components["input_sum"]
            self.target_sum = iou_pr_components["target_sum"]

            self.true_positives = iou_pr_components["true_positives"]
            self.true_negatives = iou_pr_components["true_negatives"]
            self.false_positives = iou_pr_components["false_positives"]
            self.false_negatives = iou_pr_components["false_negatives"]

            self.dots = dots_count(out_hat.mask, segm_enabled_classes)

            # point
            zipped_hits = zip(out.point_hits, out_hat.point_hits)
            zipped_hits_sizes_and_offsets = zip(
                out.point_hits, out.point_sizes, out_hat.point_sizes, out.point_offsets, out_hat.point_offsets
            )

            self.hit_enabled_classes = hit_enabled_classes
            self.point_enabled_classes = point_enabled_classes
            self.point_hit_f1_overlaps, self.point_hit_f1_inputs, self.point_hit_f1_targets = zip(
                *[
                    f1_components(
                        h_ht, h, thresholds=[thresholds.get(clz, 0.5) for clz in Deepweed.SUPPORTED_HIT_CLASSES]
                    )
                    for (h, h_ht) in zipped_hits
                ]
            )
            self.point_offset_mse = []
            self.point_size_mse = []
            for (h, s, s_ht, o, o_ht) in zipped_hits_sizes_and_offsets:
                downsample_point_offset_mse = []
                downsample_point_size_mse = []
                for clz in Deepweed.SUPPORTED_HIT_CLASSES:
                    offset_deltas = (
                        o[:, clz][h[:, clz] > thresholds.get(clz, 0.5)]
                        - o_ht[:, clz][h[:, clz] > thresholds.get(clz, 0.5)]
                    )
                    if offset_deltas.shape[0] == 0:
                        downsample_point_offset_mse.append(torch.zeros(1, device=offset_deltas.device))
                    else:
                        offset_mse = (offset_deltas ** 2).sum(dim=1).sqrt().mean().unsqueeze(0)
                        downsample_point_offset_mse.append(offset_mse)

                    size_deltas = (
                        s[:, clz][h[:, clz] > thresholds.get(clz, 0.5)]
                        - s_ht[:, clz][h[:, clz] > thresholds.get(clz, 0.5)]
                    )
                    if size_deltas.shape[0] == 0:
                        downsample_point_size_mse.append(torch.zeros(1, device=offset_deltas.device))
                    else:
                        size_mse = size_deltas.abs().mean().unsqueeze(0)
                        downsample_point_size_mse.append(size_mse)

                self.point_offset_mse.append(downsample_point_offset_mse)
                self.point_size_mse.append(downsample_point_size_mse)

            # point overall evaluation criteria components
            if model_ppi is None:
                # hack for models that do not use ppi, e.g. furrows
                assert point_enabled_classes.sum() == 0
                model_ppi = 200
            beam_radius_px = BEAM_RADIUS_IN * model_ppi

            if self.compute_dataframes:
                _, low_threshold_points_hat = compute_points(
                    out_hat.point_hits,
                    out_hat.point_categories,
                    out_hat.point_offsets,
                    out_hat.point_sizes,
                    out_hat.pumap_output,
                    (out_label.mask.shape[2], out_label.mask.shape[3]),
                    beam_radius_px,
                    weed_point_classes,
                    thresholds={clz: LOW_POINT_THRESHOLD for clz in Deepweed.SUPPORTED_HIT_CLASSES},
                )
                points_hat = [
                    [point for point in points if point.score > thresholds.get(point.hit_clz, 0.5)]
                    for points in low_threshold_points_hat
                ]
            else:
                _, points_hat = compute_points(
                    out_hat.point_hits,
                    out_hat.point_categories,
                    out_hat.point_offsets,
                    out_hat.point_sizes,
                    out_hat.pumap_output,
                    (out_label.mask.shape[2], out_label.mask.shape[3]),
                    beam_radius_px,
                    weed_point_classes,
                    thresholds=thresholds,
                )

            if self.compute_dataframes:
                if filepaths is not None:
                    if for_driptape_model:
                        self.predictions_dataframe = self._get_driptape_dataframe(filepaths)
                        self.labels_dataframe = self._get_driptape_dataframe(filepaths)
                    else:
                        self.predictions_dataframe = self._get_dataframe(
                            low_threshold_points_hat, filepaths, 512, weed_point_classes, starting_pos
                        )

                        self.labels_dataframe = self._get_dataframe(
                            out_label.points, filepaths, 256, weed_point_classes, starting_pos
                        )

                    self.predictions_dataframe = self.data_enrichment(self.predictions_dataframe, image_metadata_dict)
                    self.labels_dataframe = self.data_enrichment(self.labels_dataframe, image_metadata_dict)
                    if save_embeddings_to_db:
                        if embeddings is not None and embeddings_lookup_tables.get(EmbeddingType.FULL) is not None:
                            full_embeddings_lookup_table: EmbeddingLookupTable = cast(
                                EmbeddingLookupTable, embeddings_lookup_tables.get(EmbeddingType.FULL)
                            )
                            self.add_embeddings_to_hdf5(
                                self.predictions_dataframe,
                                full_embeddings_lookup_table,
                                embeddings,
                                save_embeddings_as_fp16=save_embeddings_as_fp16,
                            )
                            self.add_embeddings_to_hdf5(
                                self.labels_dataframe,
                                full_embeddings_lookup_table,
                                embeddings,
                                save_embeddings_as_fp16=save_embeddings_as_fp16,
                            )
                        if reduced_scaled_embeddings is not None:
                            self.predictions_dataframe = self.add_embeddings_to_dataframe(
                                self.predictions_dataframe, reduced_scaled_embeddings, "reduced_scaled_embedding"
                            )
                            self.labels_dataframe = self.add_embeddings_to_dataframe(
                                self.labels_dataframe, reduced_scaled_embeddings, "reduced_scaled_embedding"
                            )

                            if embeddings_lookup_tables.get(EmbeddingType.REDUCED_SCALED) is not None:
                                reduced_scaled_embeddings_lookup_table = cast(
                                    EmbeddingLookupTable, embeddings_lookup_tables.get(EmbeddingType.REDUCED_SCALED)
                                )
                                self.add_embeddings_to_hdf5(
                                    self.predictions_dataframe,
                                    reduced_scaled_embeddings_lookup_table,
                                    reduced_scaled_embeddings,
                                    save_embeddings_as_fp16=save_embeddings_as_fp16,
                                )
                                self.add_embeddings_to_hdf5(
                                    self.labels_dataframe,
                                    reduced_scaled_embeddings_lookup_table,
                                    reduced_scaled_embeddings,
                                    save_embeddings_as_fp16=save_embeddings_as_fp16,
                                )
                else:
                    self.predictions_dataframe = None
                    self.labels_dataframe = None

    def remove_dataframe(self) -> "Metrics":
        metrics = copy.deepcopy(self)
        metrics.compute_dataframes = False
        metrics.predictions_dataframe = None
        metrics.labels_dataframe = None
        return metrics

    def _get_driptape_dataframe(self, filepaths: List[str]) -> pandas.DataFrame:
        # Hacky method that just injects a fake point per image. This allows us to build out a points db file that
        # has images and image_metrics so we can analyze driptape. In the future if we separate points and metrics into their
        # own DBs, we can change this up.
        data = []
        for filepath in filepaths:
            data.append(
                [filepath, 0, 0, 0, 0, 0, 0, HitClass.WEED, (0, 0), json.dumps({}), 0,]
            )
        return pandas.DataFrame(
            data,
            columns=[
                "filepath",
                "x",
                "y",
                "r",
                "score",
                "category_class_name",
                "confidence",
                "hit_class_name",
                "tile_index",
                "category_scores",
                "batch_index",
            ],
        )

    def _get_dataframe(
        self,
        batched_points: List[List[Point]],
        filepaths: List[str],
        max_points: int,
        weed_point_classes: List[str],
        starting_pos: Optional[List[Tuple[float, float]]],
    ) -> pandas.DataFrame:
        data = []
        for batch_index, points in enumerate(batched_points):
            filepath = filepaths[batch_index]
            transpose_points = self._transpose_points[batch_index] if len(self._transpose_points) > 0 else False

            points = sorted(points, key=lambda point: point.score, reverse=True)[:max_points]
            for point in points:
                weed_categories_map = {}
                if point.clz_categories is not None:
                    weed_categories_map = {k: v for k, v in point.clz_categories}
                hit_y, hit_x = point.hit_y_x[0] if len(point.hit_y_x) else (-1, -1)

                if point.hit_clz == HitClass.PLANT and point.weed_clz_categories is not None:
                    weed_categories_map = {k: v for k, v in point.weed_clz_categories}

                data.append(
                    [
                        filepath,
                        point.x,
                        point.y,
                        point.r,
                        point.score,
                        point.clz,
                        point.confidence,
                        point.hit_clz,
                        starting_pos[batch_index] if starting_pos is not None else (0, 0),
                        json.dumps(weed_categories_map),
                        batch_index,
                        hit_y,
                        hit_x,
                        point.uuid,
                        point.plant_score,
                        point.weed_score,
                        point.crop_score,
                        transpose_points,
                    ]
                )

        return pandas.DataFrame(
            data,
            columns=[
                "filepath",
                "x",
                "y",
                "r",
                "score",
                "category_class_name",
                "confidence",
                "hit_class_name",
                "tile_index",
                "category_scores",
                "batch_index",
                "hit_y",
                "hit_x",
                "uuid",
                "plant_score",
                "weed_score",
                "crop_score",
                "transpose",
            ],
        )

    def data_enrichment(
        self, dataframe: pandas.DataFrame, image_metadata_dict: Dict[str, ImageMetadata]
    ) -> pandas.DataFrame:
        if dataframe.empty:
            return dataframe

        metadata_keys = ["crop_id", "robot_id", "row_id", "cam_id", "timestamp_ms", "session_name", "geohash"]

        data = []
        for filepath in dataframe["filepath"].tolist():
            if image_metadata_dict is not None:
                filtered_metadata = [getattr(image_metadata_dict[filepath], key, None) for key in metadata_keys]
            else:
                return dataframe

            data.append(filtered_metadata)

        data = pandas.DataFrame(data, columns=metadata_keys)
        dataframe = pandas.concat([dataframe, data], axis=1)

        return dataframe

    def add_embeddings_to_dataframe(
        self, dataframe: pandas.DataFrame, embeddings: torch.Tensor, column_name: str = "embedding"
    ) -> pandas.DataFrame:
        if dataframe.empty:
            return dataframe

        headers = [column_name]

        data = []
        for _, row in dataframe.iterrows():
            embedding = get_embedding_at_hit_point(
                embeddings, batch_idx=row["batch_index"], x=row["hit_x"], y=row["hit_y"]
            ).tolist()
            data.append({column_name: embedding})

        data = pandas.DataFrame(data, columns=headers)
        dataframe = pandas.concat([dataframe, data], axis=1)

        return dataframe

    def add_embeddings_to_hdf5(
        self,
        dataframe: pandas.DataFrame,
        hdf5_dataset: EmbeddingLookupTable,
        embeddings: torch.Tensor,
        save_embeddings_as_fp16: bool,
    ) -> None:
        for _, row in dataframe.iterrows():
            uuid = row["uuid"]
            assert uuid is not None
            embedding = get_embedding_at_hit_point(
                embeddings, batch_idx=row["batch_index"], x=row["hit_x"], y=row["hit_y"]
            )
            emb = embedding.detach().cpu()
            if save_embeddings_as_fp16:
                emb = emb.half()
            hdf5_dataset.set(uuid, emb)

    def remove_embeddings_from_dataframe(self) -> None:
        for embedding_name in ["embedding", "reduced_embedding", "reduced_scaled_embedding"]:
            if self.predictions_dataframe is not None and embedding_name in self.predictions_dataframe.columns:
                self.predictions_dataframe.drop(columns=[embedding_name], inplace=True)
            if self.labels_dataframe is not None and embedding_name in self.labels_dataframe.columns:
                self.labels_dataframe.drop(columns=[embedding_name], inplace=True)

    def get_dataframe(self) -> Optional[pandas.DataFrame]:
        df: Optional[pandas.DataFrame] = None
        if self.predictions_dataframe is not None and self.labels_dataframe is not None:
            self.predictions_dataframe["type"] = "prediction"
            self.labels_dataframe["type"] = "label"
            df = pandas.concat([self.predictions_dataframe, self.labels_dataframe])

        return df

    def load_metadata(self, filepath: str) -> Optional[Any]:
        filepath = os.path.splitext(filepath)[0] + ".metadata.json"
        metadata = None
        if os.path.exists(filepath):
            with open(filepath) as f:
                metadata = json.load(f)
        return metadata

    def detach(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_detach(self.__dict__, whitelist=[DeepweedConfig])
        return obj

    def to_numpy(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_to_numpy(self.__dict__, whitelist=[DeepweedConfig])
        return obj

    def to_tensor(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_to_tensor(self.__dict__, whitelist=[DeepweedConfig])
        return obj

    def cpu(self) -> "Metrics":
        obj = Metrics.__new__(Metrics)
        obj.__dict__ = recursive_move(self.__dict__, torch.device("cpu"), whitelist=[DeepweedConfig])
        return obj

    def compute_miou(self, class_id: Optional[int] = None, return_numerator_denominator: bool = False) -> torch.Tensor:
        func = get_metric_components if return_numerator_denominator else mean_ratio
        if class_id is None:
            return func(self.intersection, self.union, self.segm_enabled_classes)
        else:
            return func(self.intersection[:, class_id], self.union[:, class_id])

    def compute_segm_precision(
        self, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        func = get_metric_components if return_numerator_denominator else mean_ratio
        if class_id is None:
            return func(self.intersection, self.input_sum, self.segm_enabled_classes)
        else:
            return func(self.intersection[:, class_id], self.input_sum[:, class_id])

    def compute_segm_recall(
        self, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        func = get_metric_components if return_numerator_denominator else mean_ratio
        if class_id is None:
            return func(self.intersection, self.target_sum, self.segm_enabled_classes)
        else:
            return func(self.intersection[:, class_id], self.target_sum[:, class_id])

    def compute_segm_f1(
        self, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        p = self.compute_segm_precision(class_id)
        r = self.compute_segm_recall(class_id)
        num = 2 * p * r
        denom = p + r
        f1 = num / denom
        if return_numerator_denominator:
            return torch.stack([f1, torch.ones_like(f1)])
        return f1

    def compute_simple_fraction(
        self, numerator: torch.Tensor, denominator: torch.Tensor, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        if return_numerator_denominator:
            return torch.stack([numerator, denominator])

        if denominator.sum() == 0:
            return torch.tensor(0.0)

        return numerator / denominator

    def compute_point_hit_precision(
        self, index: int, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        func = get_metric_components if return_numerator_denominator else mean_ratio
        if class_id is None:
            return func(self.point_hit_f1_overlaps[index], self.point_hit_f1_inputs[index], self.hit_enabled_classes)
        else:
            return func(self.point_hit_f1_overlaps[index][:, class_id], self.point_hit_f1_inputs[index][:, class_id])

    def compute_point_hit_recall(
        self, index: int, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        func = get_metric_components if return_numerator_denominator else mean_ratio
        if class_id is None:
            return func(self.point_hit_f1_overlaps[index], self.point_hit_f1_targets[index], self.hit_enabled_classes)
        else:
            return func(self.point_hit_f1_overlaps[index][:, class_id], self.point_hit_f1_targets[index][:, class_id])

    def compute_point_hit_f1(
        self, index: int, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        p = self.compute_point_hit_precision(index, class_id)
        r = self.compute_point_hit_recall(index, class_id)

        num = 2 * p * r
        denom = p + r
        f1 = num / denom
        if return_numerator_denominator:
            return torch.stack([f1, torch.ones_like(f1)])

        return f1

    def compute_point_offset_error(
        self, index: int, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        deltas_list = self.point_offset_mse[index]

        if class_id is None:
            deltas = torch.cat(deltas_list)
        else:
            deltas = deltas_list[class_id]

        if len(deltas) == 0:
            if return_numerator_denominator:
                return torch.tensor([0.0, 0.0]).to(self.loss.device)
            return torch.tensor(0.0).to(self.loss.device)

        if return_numerator_denominator:
            return torch.stack([deltas.sum(), torch.tensor(deltas.shape[0])])
        # mean absolute distance
        return deltas.mean()

    def compute_point_size_error(
        self, index: int, class_id: Optional[int] = None, return_numerator_denominator: bool = False
    ) -> torch.Tensor:
        deltas_list = self.point_size_mse[index]

        if class_id is None:
            deltas = torch.cat(deltas_list)
        else:
            deltas = deltas_list[class_id]

        if len(deltas) == 0:
            if return_numerator_denominator:
                return torch.tensor([0.0, 0.0]).to(self.loss.device)
            return torch.tensor(0.0).to(self.loss.device)

        if return_numerator_denominator:
            return torch.stack([deltas.sum(), torch.tensor(deltas.shape[0])])
        # mean absolute delta
        return deltas.mean()

    def get_losses(self, return_numerator_denominator: bool = False) -> Dict[str, Any]:
        d: Dict[str, Any] = dict()

        # Loss metrics
        d["loss"] = process_single_item(self.loss, return_numerator_denominator=return_numerator_denominator)
        d["segm_dice_loss"] = process_single_item(
            self.segm_dice_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["segm_bce_loss"] = process_single_item(
            self.segm_bce_loss, return_numerator_denominator=return_numerator_denominator
        )

        d["point_weed_hit_loss"] = process_single_item(
            self.point_weed_hit_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["point_crop_hit_loss"] = process_single_item(
            self.point_crop_hit_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["point_plant_hit_loss"] = process_single_item(
            self.point_plant_hit_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["point_offset_loss"] = process_single_item(
            self.point_offset_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["point_size_loss"] = process_single_item(
            self.point_size_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["point_category_loss"] = process_single_item(
            self.point_category_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["embedding_loss"] = process_single_item(
            self.embedding_loss, return_numerator_denominator=return_numerator_denominator
        )

        d["weighted_segm_dice_loss"] = process_single_item(
            self.weighted_segm_dice_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_segm_bce_loss"] = process_single_item(
            self.weighted_segm_bce_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_point_weed_hit_loss"] = process_single_item(
            self.weighted_point_weed_hit_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_point_crop_hit_loss"] = process_single_item(
            self.weighted_point_crop_hit_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_point_plant_hit_loss"] = process_single_item(
            self.weighted_point_plant_hit_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_point_offset_loss"] = process_single_item(
            self.weighted_point_offset_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_point_size_loss"] = process_single_item(
            self.weighted_point_size_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_point_category_loss"] = process_single_item(
            self.weighted_point_category_loss, return_numerator_denominator=return_numerator_denominator
        )
        d["weighted_embedding_loss"] = process_single_item(
            self.weighted_embedding_loss, return_numerator_denominator=return_numerator_denominator
        )
        return d

    def get_metrics(self, return_numerator_denominator: bool = False) -> Dict[str, Any]:
        d: Dict[str, Any] = dict()

        # Performance metrics
        d["miou"] = self.compute_miou(return_numerator_denominator=return_numerator_denominator)
        d["segm_precision"] = self.compute_segm_precision(return_numerator_denominator=return_numerator_denominator)
        d["segm_recall"] = self.compute_segm_recall(return_numerator_denominator=return_numerator_denominator)
        d["segm_f1"] = self.compute_segm_f1(return_numerator_denominator=return_numerator_denominator)

        tp = self.true_positives.sum()
        fp = self.false_positives.sum()
        tn = self.true_negatives.sum()
        fn = self.false_negatives.sum()
        total_pixels = tp + fp + tn + fn
        d["segm_true_positive"] = self.compute_simple_fraction(
            tp, total_pixels, return_numerator_denominator=return_numerator_denominator
        )
        d["segm_false_positive"] = self.compute_simple_fraction(
            fp, total_pixels, return_numerator_denominator=return_numerator_denominator
        )
        d["segm_true_negative"] = self.compute_simple_fraction(
            tn, total_pixels, return_numerator_denominator=return_numerator_denominator
        )
        d["segm_false_negative"] = self.compute_simple_fraction(
            fn, total_pixels, return_numerator_denominator=return_numerator_denominator
        )

        for idx, segm_class_name in enumerate(self.segm_classes):
            if self.segm_enabled_classes[idx]:
                d[f"miou_{segm_class_name}"] = self.compute_miou(
                    idx, return_numerator_denominator=return_numerator_denominator
                )
                d[f"segm_precision_{segm_class_name}"] = self.compute_segm_precision(
                    idx, return_numerator_denominator=return_numerator_denominator
                )
                d[f"segm_recall_{segm_class_name}"] = self.compute_segm_recall(
                    idx, return_numerator_denominator=return_numerator_denominator
                )
                d[f"segm_f1_{segm_class_name}"] = self.compute_segm_f1(
                    idx, return_numerator_denominator=return_numerator_denominator
                )

        for (idx, downsample) in enumerate(self.config.point_downsample):
            d[f"point_hit_precision_{downsample}"] = self.compute_point_hit_precision(
                idx, return_numerator_denominator=return_numerator_denominator
            )
            d[f"point_hit_recall_{downsample}"] = self.compute_point_hit_recall(
                idx, return_numerator_denominator=return_numerator_denominator
            )
            d[f"point_hit_f1_{downsample}"] = self.compute_point_hit_f1(
                idx, return_numerator_denominator=return_numerator_denominator
            )
            d[f"point_offset_error_{downsample}"] = self.compute_point_offset_error(
                idx, return_numerator_denominator=return_numerator_denominator
            )
            d[f"point_size_error_{downsample}"] = self.compute_point_size_error(
                idx, return_numerator_denominator=return_numerator_denominator
            )

        for category, class_name in {"crop": HitClass.CROP, "weed": HitClass.WEED, "plant": HitClass.PLANT}.items():
            class_id = cast(int, class_name)
            for (idx, downsample) in enumerate(self.config.point_downsample):
                d[f"point_hit_precision_{downsample}_{category}"] = self.compute_point_hit_precision(
                    idx, class_id, return_numerator_denominator=return_numerator_denominator
                )
                d[f"point_hit_recall_{downsample}_{category}"] = self.compute_point_hit_recall(
                    idx, class_id, return_numerator_denominator=return_numerator_denominator
                )
                d[f"point_hit_f1_{downsample}_{category}"] = self.compute_point_hit_f1(
                    idx, class_id, return_numerator_denominator=return_numerator_denominator
                )
                d[f"point_offset_error_{downsample}_{category}"] = self.compute_point_offset_error(
                    idx, class_id, return_numerator_denominator=return_numerator_denominator
                )
                d[f"point_size_error_{downsample}_{category}"] = self.compute_point_size_error(
                    idx, class_id, return_numerator_denominator=return_numerator_denominator
                )

        return d

    def get_section_metrics(
        self, return_numerator_denominator: bool = False
    ) -> Dict[Tuple[int, int, int, int], Dict[str, Any]]:
        d: Dict[Tuple[int, int, int, int], Dict[str, Any]] = defaultdict(lambda: defaultdict(dict))
        if self.starting_pos is None:
            return d
        for i, (starting_x, starting_y) in enumerate(self.starting_pos):
            tp = self.true_positives[i].sum()
            fp = self.false_positives[i].sum()
            tn = self.true_negatives[i].sum()
            fn = self.false_negatives[i].sum()
            total_pixels = tp + fp + tn + fn

            key = (int(starting_x), int(starting_y), self.evaluation_height, self.evaluation_width)
            d[key]["segm_true_positive"] = self.compute_simple_fraction(
                tp, total_pixels, return_numerator_denominator=return_numerator_denominator
            )
            d[key]["segm_false_positive"] = self.compute_simple_fraction(
                fp, total_pixels, return_numerator_denominator=return_numerator_denominator
            )
            d[key]["segm_true_negative"] = self.compute_simple_fraction(
                tn, total_pixels, return_numerator_denominator=return_numerator_denominator
            )
            d[key]["segm_false_negative"] = self.compute_simple_fraction(
                fn, total_pixels, return_numerator_denominator=return_numerator_denominator
            )

        return d

    def to_dict(self, prefix: str = "", **additional_metrics: Dict[str, Any]) -> Dict[str, Any]:
        losses = self.get_losses(return_numerator_denominator=False)
        metrics = self.get_metrics(return_numerator_denominator=False)

        d: Dict[str, Any] = {}

        for dictionary in [losses, metrics]:
            for key, value in dictionary.items():
                d[f"{prefix}{key}"] = value

        # Other metrics
        d[f"{prefix}dots"] = self.dots
        d[f"{prefix}learning_rate"] = self.learning_rate

        if self.model_elapsed_time is not None:
            d["model_elapsed_time"] = self.model_elapsed_time

        for key, value in additional_metrics.items():
            d[key] = value

        return d

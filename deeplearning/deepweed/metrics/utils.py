import math
from typing import Optional

import torch

OVERLAP_THRESHOLDS = [5, 15, 25, 50, 75, 85, 95]
LOW_POINT_THRESHOLD = 0.1


def sum_over_enabled_classes(component: torch.Tensor, enabled_classes: Optional[torch.Tensor] = None,) -> torch.Tensor:
    if enabled_classes is not None:
        assert len(component.shape) == 1, f"Wrong shape: {component.shape}"
        component = (component * enabled_classes.to(component.device)).sum(-1)
    return component.sum(-1)


def mean_over_enabled_classes(metric: torch.Tensor, enabled_classes: Optional[torch.Tensor] = None) -> torch.Tensor:
    # Mean over enabled classes
    if enabled_classes is not None:
        assert len(metric.shape) == 1, f"Wrong shape: {metric.shape}"
        metric = (metric * enabled_classes.to(metric.device)).sum(-1) / enabled_classes.sum()
    return metric


def mean_ratio(
    a: torch.Tensor, b: torch.Tensor, enabled_classes: Optional[torch.Tensor] = None, smooth: float = 1e-6,
) -> torch.Tensor:
    assert (
        enabled_classes is None or enabled_classes.dtype == torch.bool
    ), f"enabled_classes must be bool: {enabled_classes.dtype}"

    # Compute IoU per class, per example
    metric = (a + smooth) / (b + smooth)

    # Mean over examples
    metric = metric.mean(0)

    # Mean over enabled classes
    metric = mean_over_enabled_classes(metric, enabled_classes)

    if len(metric.shape) == 1:
        metric = metric.sum(0) / metric.shape[0]

    assert len(metric.shape) == 0
    return metric


def global_ratio(
    a: torch.Tensor, b: torch.Tensor, enabled_classes: Optional[torch.Tensor] = None, smooth: float = 1e-6,
) -> torch.Tensor:
    assert (
        enabled_classes is None or enabled_classes.dtype == torch.bool
    ), f"enabled_classes must be bool: {enabled_classes.dtype}"

    # Sum over examples
    a = a.sum(0)
    b = b.sum(0)

    # Compute IoU per class
    metric = (a + smooth) / (b + smooth)

    # Mean over enabled classes
    metric = mean_over_enabled_classes(metric, enabled_classes)

    if len(metric.shape) == 1:
        metric = metric.sum(0) / metric.shape[0]

    assert len(metric.shape) == 0
    return metric


def get_metric_components(
    a: torch.Tensor, b: torch.Tensor, enabled_classes: Optional[torch.Tensor] = None, smooth: float = 0,
) -> torch.Tensor:
    a_summed = a.sum(0)
    b_summed = b.sum(0)

    return torch.stack(
        [sum_over_enabled_classes(a_summed, enabled_classes), sum_over_enabled_classes(b_summed, enabled_classes),]
    )


def compute_point_oec(weeds_targeted: float, crops_targeted: float) -> float:
    crops_targeted_oec_10x = min(crops_targeted * 10, 1)
    return 2 * weeds_targeted * (1 - crops_targeted_oec_10x) / (weeds_targeted + 1 - crops_targeted_oec_10x)


def compute_oec(point_oec: float, segmentation_oec: float) -> float:
    if math.isnan(point_oec):
        point_oec = 1.0

    if math.isnan(segmentation_oec):
        segmentation_oec = 1.0

    return 2 * segmentation_oec * point_oec / (segmentation_oec + point_oec)

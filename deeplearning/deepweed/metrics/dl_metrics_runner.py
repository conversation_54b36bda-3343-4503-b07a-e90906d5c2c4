import datetime
import functools
import io
import logging
from typing import Any, Dict, List, Optional

import matplotlib.pyplot as plt
import pandas
from PIL import Image
from sqlalchemy.orm import sessionmaker

from deeplearning import dl_metrics
from deeplearning.dl_metrics import nan_divide, points_db
from lib.common.time.time import maka_control_timestamp_ms


def nan_divide_on_row(df: pandas.DataFrame) -> pandas.DataFrame:
    return nan_divide(df["numerator"], df["denominator"])


LOG = logging.getLogger(__name__)


class DlMetricsRunner:
    def __init__(self, points_db_directory: str) -> None:
        p_db = dl_metrics.get_db_from_directory(points_db_directory)
        self._sessionmaker = sessionmaker(p_db)
        self._min_date = datetime.date(year=2000, month=1, day=1)
        self._tomorrow_date = datetime.datetime.now() + datetime.timedelta(1)

    def to_metric_name(self, name: str) -> str:
        return name.lower().replace(" ", "_")

    def __call__(
        self,
        wpt: float,
        cpt: float,
        filters: Dict[str, Any],
        group_by: Dict[str, Any],
        prefix: str,
        add_min_max: bool = False,
        distance_threshold_mm: Optional[float] = None,
    ) -> Dict[str, Any]:
        metrics: List[Any] = [
            dl_metrics.CropsTargeted,
            dl_metrics.WeedsTargeted,
            dl_metrics.CropsDetected,
            dl_metrics.WeedsDetected,
            dl_metrics.CropsPossiblyTargeted,
            dl_metrics.PlantsDetected,
            functools.partial(dl_metrics.UncertainWeedDetections, a=0.4, b=0.6),
            functools.partial(dl_metrics.UncertainWeedDetections, a=0.3, b=0.7),
            dl_metrics.UnlabeledAreaTargeted,
            dl_metrics.WeedsDetectedAsCrops,
            dl_metrics.CropsDetectedAsWeeds,
            dl_metrics.CorrectlySizedWeedDetections,
            dl_metrics.CorrectlySizedCropDetections,
            dl_metrics.FalseCropDetections,
            dl_metrics.FalsePlantDetections,
        ]

        metric_results = {}
        for metric_type in metrics:
            if distance_threshold_mm is not None:
                metric_type = functools.partial(metric_type, distance_threshold_mm=distance_threshold_mm)
            metric = metric_type(wpt=wpt, cpt=cpt)
            with self._sessionmaker() as session:
                result = metric.filter(date_range=(self._min_date, self._tomorrow_date), **filters).group_by(
                    **group_by
                )(session)

            if len(group_by) > 0:
                result.agg({"numerator": "sum", "denominator": "sum"})
                group_division = result.apply(nan_divide_on_row, axis=1)

                if add_min_max:
                    base_str = f"dl_metrics_{prefix}{self.to_metric_name(metric.name)}"
                    metric_results[base_str + "_min"] = (
                        group_division.min() if group_division.shape[0] > 0 else float("nan")
                    )
                    metric_results[base_str + "_mean"] = (
                        group_division.mean() if group_division.shape[0] > 0 else float("nan")
                    )
                    metric_results[base_str + "_max"] = (
                        group_division.max() if group_division.shape[0] > 0 else float("nan")
                    )
                else:
                    metric_results[f"dl_metrics_{prefix}{self.to_metric_name(metric.name)}"] = (
                        group_division.mean() if group_division.shape[0] > 0 else float("nan")
                    )
            else:
                metric_results[f"dl_metrics_{prefix}{self.to_metric_name(metric.name)}"] = nan_divide(
                    result["numerator"].sum(), result["denominator"].sum()
                )

        return metric_results

    def make_score_distribution_plot(self, probabilities: pandas.DataFrame, title: str, log_scale: bool) -> Image.Image:
        probabilities.plot(kind="hist", density=True, bins=100, alpha=0.65, legend=None, figsize=(10, 7))

        plt.rcParams.update({"font.size": 8})
        plt.style.use("bmh")
        plt.xticks([x / 10 for x in range(1, 10)])
        plt.xlim([0, 1])
        plt.ylabel("Density")
        plt.xlabel("Confidence Score")

        if log_scale:
            plt.ylim([0.01, 100])
            plt.yscale("log")
            plt.title(title + f"\n{len(probabilities)} Samples, log scale", loc="left")
        else:
            plt.ylim([0, 20])
            plt.title(title + f"\n{len(probabilities)} Samples", loc="left")

        axis = plt.gca()

        for _, spine in axis.spines.items():
            spine.set_visible(False)

        axis.grid(False)
        axis.tick_params(left=False, bottom=False)

        image_buffer = io.BytesIO()

        plt.savefig(image_buffer, format="png")
        plt.close()

        image = Image.open(image_buffer)

        return image

    def get_score_distribution(self) -> Dict[str, plt.Figure]:
        start = maka_control_timestamp_ms()
        LOG.info("Creating score distribution plots")

        plots = {}

        # Global plots
        with self._sessionmaker() as session:
            probabilities = pandas.DataFrame(
                session.query(points_db.Point.score)
                .filter(points_db.Point.type == points_db.PointType.PREDICTION)
                .all()
            )

        if len(probabilities) > 0:
            title = "Test Global Set Score Distribution"
            plots["score_distribution_test_global"] = self.make_score_distribution_plot(probabilities, title, False)
            plots["score_distribution_test_log_scale_global"] = self.make_score_distribution_plot(
                probabilities, title, True
            )

        # Hit class plots
        with self._sessionmaker() as session:
            unique_hit_classes: List[points_db.HitClass] = [
                item[0] for item in session.query(points_db.Point.hit_class).distinct()
            ]
            for hit_class in unique_hit_classes:
                probabilities = pandas.DataFrame(
                    session.query(points_db.Point.score)
                    .filter(
                        points_db.Point.type == points_db.PointType.PREDICTION, points_db.Point.hit_class == hit_class
                    )
                    .all()
                )

                if len(probabilities) > 0:
                    title = f"Test {hit_class} Score Distribution"
                    plots[f"score_distribution_test_{hit_class}"] = self.make_score_distribution_plot(
                        probabilities, title, False
                    )
                    plots[f"score_distribution_test_log_scale_{hit_class}"] = self.make_score_distribution_plot(
                        probabilities, title, True
                    )

            # Category class plots
            unique_category_class_names = [
                item[0] for item in session.query(points_db.Point.category_class_name).distinct()
            ]
            for category_class_name in unique_category_class_names:
                probabilities = pandas.DataFrame(
                    session.query(points_db.Point.score)
                    .filter(
                        points_db.Point.type == points_db.PointType.PREDICTION,
                        points_db.Point.category_class_name == category_class_name,
                    )
                    .all()
                )

                if len(probabilities) > 0:
                    title = f"Test {category_class_name} Score Distribution"
                    plots[f"score_distribution_test_{category_class_name}"] = self.make_score_distribution_plot(
                        probabilities, title, False
                    )
                    plots[
                        f"score_distribution_test_log_scale_{category_class_name}"
                    ] = self.make_score_distribution_plot(probabilities, title, True)

        end = maka_control_timestamp_ms()
        LOG.info(f"DL Metrics Timing: Score distribution generation={end - start} ms")
        return plots

from typing import Any, Dict, List

import torch

from deeplearning.deepweed.metrics.metrics import Metrics


class AggregateMetrics(object):
    def __init__(self, metrics: List[Metrics]):
        self._metrics = metrics

    def process_value(self, val: torch.Tensor) -> float:
        return val.item() if not torch.isnan(val) else 0

    def to_dict(self, file_key: str) -> Dict[str, List[Dict[str, Any]]]:
        whole_image_metrics: Dict[str, Dict[str, Any]] = {}
        section_image_metrics: Dict[str, Dict[str, Any]] = {}

        for met in self._metrics:
            loss_components = met.get_losses(return_numerator_denominator=True)
            met_components = met.get_metrics(return_numerator_denominator=True)

            for components_dict in [loss_components, met_components]:
                for component_name, component_value in components_dict.items():
                    if component_name not in whole_image_metrics:
                        whole_image_metrics[component_name] = {
                            "component_name": component_name,
                            "numerator": self.process_value(component_value[0]),
                            "denominator": self.process_value(component_value[1]),
                            "whole_image": True,
                        }
                    else:
                        whole_image_metrics[component_name]["numerator"] += self.process_value(component_value[0])
                        whole_image_metrics[component_name]["denominator"] += self.process_value(component_value[1])
            for section_components_dict in [met.get_section_metrics(return_numerator_denominator=True)]:
                for (
                    (x_origin, y_origin, eval_height, eval_width),
                    section_components_values,
                ) in section_components_dict.items():
                    for component_name, component_value in section_components_values.items():
                        section_image_metrics[f"{component_name}_{x_origin}_{y_origin}_{eval_height}_{eval_width}"] = {
                            "component_name": component_name,
                            "numerator": self.process_value(component_value[0]),
                            "denominator": self.process_value(component_value[1]),
                            "whole_image": False,
                            "x_origin": x_origin,
                            "y_origin": y_origin,
                            "height": eval_height,
                            "width": eval_width,
                        }
        return {
            file_key: [
                {
                    "metric_name": val["component_name"],
                    "numerator": val["numerator"],
                    "denominator": val["denominator"],
                    "whole_image": val["whole_image"],
                    "x_origin": val.get("x_origin", 0),
                    "y_origin": val.get("y_origin", 0),
                    "height": val.get("height", 0),
                    "width": val.get("width", 0),
                }
                for _, val in list(whole_image_metrics.items()) + list(section_image_metrics.items())
            ]
        }
